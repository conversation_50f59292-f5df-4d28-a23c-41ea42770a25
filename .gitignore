# makefile文件
*/Makefile
*/build/Makefile
*/CMakeFiles/
*/build/CMakeFiles/
*/CMakeCache.txt
*/build/CMakeCache.txt
*/cmake_install.cmake
*/build/cmake_install.cmake
csmain/objects/
csmain/release/bin/csmain
csmain/release/bin/csmain_cli

csbase/CMakeCache.txt
csbase/CMakeFiles/
csbase/Makefile
csbase/cmake_install.cmake
csbase/libcsbase.a

cssession/CMakeCache.txt
cssession/CMakeFiles/
cssession/Makefile
cssession/cmake_install.cmake
cssession/release/bin/cssession

csconfig/objects/
csconfig/release/bin/csconfig

csroute/.gitignore
csroute/CMakeCache.txt
csroute/CMakeFiles/
csroute/Makefile
csroute/cmake_install.cmake
csroute/release/bin/csroute

csvrtsp/src/net/libvrtspd/objects/
csvrtsp/release/bin/csvrtspd

smarthome-rtsp/src/net/libvrtspd/objects/
smarthome-rtsp/release/bin/csvrtspd

csmsip/CMakeCache.txt
csmsip/CMakeFiles/
csmsip/Makefile
csmsip/cmake_install.cmake
csmsip/release/bin/csmsip

cspdu2kafkamq/CMakeCache.txt
cspdu2kafkamq/CMakeFiles/
cspdu2kafkamq/Makefile
cspdu2kafkamq/cmake_install.cmake
cspdu2kafkamq/release/bin/cspdu2kafkamq

csgate/objects/
csgate/release/bin/csgate

csadapt/objects/
csadapt/release/bin/csadapt

csmediagate/ZLMediaKit/build/
csbase/thirdlib/ZLMediaKit/linux_build

# objects文件
*/objects/
csftp/*.o
smarthome-rtsp/src/net/libvrtspd/objects/
csmediagate/ZLMediaKit/release/

# bin文件
csmain/release/bin/csmain
csmain/release/bin/csmain_cli
csroute/release/bin/csroute
cssession/release/bin/cssession
csconfig/release/bin/csconfig
csmsip/release/bin/csmsip
csgate/release/bin/csgate
csadapt/release/bin/csadapt
cspdu2kafkamq/release/bin/cspdu2kafkamq
csresid/release/bin/csresid
csoffice/release/bin/csoffice
csmediagate/release/bin/csmediagate
csvrecord/release/bin/csvrecord
csstorage/release/bin/csstorage
csvrtsp/release/bin/csvrtspd
smarthome-rtsp/release/bin/csvrtspd

# 编译库文件
csbase/libcsbase.a
csbase/thirdlib/libcpprest.so
pbxmod/lib/libakpbxmod.so

# protobuf源文件
csbase/protobuf/AK.*
csbase/grpc/csmain/AK.*
csbase/grpc/cssession/AK.*
csbase/grpc/cspbxrpc/AK.*
csbase/grpc/csvideorecord/AK.*

# protobuf头文件
csbp/proto/proto.php
csbp/proto/proto_crontab.php
csbp/proto/proto_crontab_office.php
csbp/proto/proto_office.php

# 编译打包文件
akcs_*_packeg.tar.gz
akcs_*_packeg

csvrtsp/src/net/libvrtspd/objects/

csoffice/build/CMakeCache.txt
csoffice/build/CMakeFiles/
csoffice/build/Makefile
csoffice/build/cmake_install.cmake
csoffice/release/bin/csoffice

csadapt/CMakeFiles/
csadapt/cmake_install.cmake
csadapt/CMakeCache.txt
csadapt/Makefile

csgate/CMakeFiles/
csgate/cmake_install.cmake
csgate/CMakeCache.txt
csgate/Makefile

csmain/CMakeFiles
csmain/cmake_install.cmake
csmain/CMakeCache.txt
csmain/Makefile
csbase/thirdlib/libstdc++.so
.vscode/settings.json

csconfig/CMakeFiles/
csconfig/cmake_install.cmake
csconfig/CMakeCache.txt
csconfig/Makefile

.vs/
.vscode/

csbp/cppcheck/output/*
build/.cmake/api/v1/query/client-vscode/query.json
