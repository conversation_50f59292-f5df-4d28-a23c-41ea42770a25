SL50上行消息生成文件规则
1. 需要给定一个json内容
2. 文件名称把json内容里面command去掉前缀v1.0_u_通过下划线替换为驼峰命名
3. 生成在cssmartlock/src/main/SL50/目录下，如果json内容里面parma内容为空，生成内容参考GetNewVersion.h和GetNewVersion.cpp,如果不为空生成内容参考ReportLockLog.cpp和ReportLockLog.h
4. 如果消息param里面是数组类型,需要在头文件新增结构体进行维护,如果只是简单参数直接在类中实现.
5. IParseData需要根据json内容进行解析，存到自己类中的成员
6. RegSL50UpFunc消息ID需要补充到cssmartlock\src\SmartLockMsgDef.h文件中,command为消息id

SL50 下行ACK消息文件生成规则
1. 需要给定一个json内容
2. 文件名称把json内容里面command去掉前缀v1.0_u_通过下划线替换为驼峰命名，再加上前缀Ack
3. 生成在csbase/SL50/DownAckMessage/目录下，生成内容参考csbase/SL50/DownAckMessage/AckTimeInfo.h和csbase/SL50/DownAckMessage/AckTimeInfo.cpp
4. 如果参数操过三个,需要通过成员函数进行设置，不能全部维护在构造函数中

SL50 下行消息文件生成规则
1. 需要给定一个json内容
2. 文件名称把json内容里面command去掉前缀v1.0_d_通过下划线替换为驼峰命名，再加上前缀Down
3. 生成在csbase/SL50/DownMessage/目录下，如果json内容command不是v1.0_d_device_ha_control生成内容参考csbase/SL50/DownMessage/DownCloseRtsp.h和csbase/SL50/DownMessage/DownCloseRtsp.cpp, 如果是v1.0_d_device_ha_control生成内容参考csbase/SL50/DownMessage/LockControl.h和csbase/SL50/DownMessage/LockControl.cpp
4. 如果参数操过三个,需要通过成员函数进行设置，不能全部维护在构造函数中

