#csadapt conf
csadapt_outerip=**************
web_domain=
csadapt_port=8503

#cspbx conf
cspbx_ip=*************
cspbx_port=5070

#db conf
db_ip=127.0.0.1
db_username=dbuser01
db_database=AKCS
db_port=3306

#log db conf
db_log_ip=***********
db_log_port=3306
db_log_database=LOG

#nsq
nsq_delpic_topic=delpic

#etcd conf,etcd是集群,通过配置文件指定
etcd_srv_net=http://***********:8507;http://***********:18507;

#nsq route conf
nsq_route_topic=ak_route

#beanstalkd
beanstalkd_ip=127.0.0.1
beanstalkd_backup_ip=

#remote sshd domain
remote_config_domain=remoteconfig.akuvox.com

#web ip
web_ip=*************

#1=ccloud 2=scloud 3=ecloud 4=ucloud 5=other 6=rcloud
system_area_type=5
#空房间不写主账号联系人，小区列表id(id1,id2) 
#目前只有美国有特殊处理,注意前后的逗号要有
community_ids=,5037,5041,5532,

#AWS
is_aws=0
aws_mysql_ip=*************

#csadapt是否查询AwsRedirect表 做重定向判断
aws_redirect=0

#kafka
notify_app_backend_topic=notify_app_backend
notify_app_backend_group=notify_app_backend_group
notify_app_backend_thread_num=1
kafka_broker_ip=

#特殊社区写配置投递到专门的tube即web_to_adapt101,社区id间用用逗号隔开（举例：,1, 或 ,1,2,3, ）
#现阶段家庭数很多的社区刷联系人很慢，会导致堆积，暂时放到单独的tube消费
special_mng_id=

#写配置的线程个数  不包括转发(转发硬编码两个) csconfig.conf也要同步修改
write_thread_number=4

log_encrypt=0
log_trace=1

#新办公
#数据分析队列
kafka-notify-appbackend-analysis-topic=notify-appbackend-analysis
kafka-notify-appbackend-analysis-group=notify-appbackend-analysis_group
notify-appbackend-analysis-thread-num = 2

#email/sms队列
kafka-notify-appbackend-push-topic=notify-appbackend-push
kafka-notify-appbackend-push-group=notify-appbackend-push_group
notify-appbackend-push-thread-num = 2

#实时通知设备
kafka-notify-appbackend-notify-topic=notify-appbackend-notify
kafka-notify-appbackend-notify-group=notify-appbackend-notify_group
notify-appbackend-notify-thread-num = 2

#消息通知
kafka-notify-web-message-topic=notify_web_message
kafka-notify-web-message-group=notify_web_message_group
notify-web-message-thread-num = 2

#通知csconfig
kafka-notify-csconfig-topic=notify-csconfig

#网关编号
gateway_num=
