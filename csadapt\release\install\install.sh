#!/bin/bash

set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
DOCKER_IMG=$3
CONTAINER_NAME=csadapt

[[ -z "$RSYNC_PATH" ]] && { echo "【RSYNC_PATH】变量值不能为空"; exit 1; }

# Input:
#   $1: item
#   $2: conf_file
# Output:
#   value: the value of item
grep_conf() {
    grep -w "$1" "$2" | cut -d'=' -f 2 | awk '{gsub(/^[ \t]+|[ \t]+$/, "");print}'
}

# ============================================================================
# ==== main
# ============================================================================
cd "$(dirname "$0")"
PKG_ROOT=$(cd .. && pwd)

IP_FILE=/etc/ip
INSTALL_CONF=$RSYNC_PATH/app_backend_install.conf
APP_NAME=csadapt    # 安装包中 bin 目录下应用程序的文件名
APP_HOME=/usr/local/akcs/csadapt
LOG_PATH=/var/log/csadaptlog
CTRL_SCRIPT=csadaptctl.sh
RUN_SCRIPT=csadaptrun.sh
RUN_SCRIPT_PATH=$APP_HOME/scripts/$RUN_SCRIPT
SIGNAL=${SIGNAL:-TERM}
ADAPT_SOCKET_HOME=/var/adapt_sock
ADAPT_SOCKET_FILE=/var/adapt_sock/adapt.sock

CSADAPT_SCRIPTS_DIR="/usr/local/akcs/csadapt/scripts/"   
SOURCE_DIR="$RSYNC_PATH/csadapt/scripts"              

if [ ! -d "$CSADAPT_SCRIPTS_DIR" ]; then
    mkdir -p "$CSADAPT_SCRIPTS_DIR"
else
    rm -rf "$CSADAPT_SCRIPTS_DIR/*"
fi
cp -r "$SOURCE_DIR/"* "$CSADAPT_SCRIPTS_DIR/"

# 输出结果
echo "文件已成功复制到 $CSADAPT_SCRIPTS_DIR"

# ----------------------------------------------------------------------------
# 开始安装
# ----------------------------------------------------------------------------
echo "Begin to install $APP_NAME"

echo '读取配置'

if [ ! -f $IP_FILE ]; then
    echo "文件不存在：$IP_FILE"
    exit 1
fi

if [ ! -f $INSTALL_CONF ]; then
    echo "文件不存在：$INSTALL_CONF"
    exit 1
fi

ENV_CONF_PARAM="
-e WEB_DOMAIN=$(grep_conf 'WEB_DOMAIN' $INSTALL_CONF)
-e WEB_IP=$(grep_conf 'WEB_IP' $INSTALL_CONF)
-e SYSTEM_AREA=$(grep_conf 'GATEWAY_NUM' $INSTALL_CONF)
-e MYSQL_INNER_IP=$(grep_conf 'MYSQL_INNER_IP' $INSTALL_CONF)
-e ETCD_INNER_IP=$(grep_conf 'ETCD_INNER_IP' $INSTALL_CONF)
-e BEANSTALKD_IP=$(grep_conf 'BEANSTALKD_IP' $INSTALL_CONF)
-e BEANSTALKD_BACKUP_IP=$(grep_conf 'BEANSTALKD_BACKUP_IP' $INSTALL_CONF || echo '')
-e PBX_OUTER_IP=$(grep_conf 'PBX_OUTER_IPV4' $INSTALL_CONF)
-e IS_AWS=$(grep_conf 'IS_AWS' $INSTALL_CONF || echo '0')
-e AWS_REDIRECT=$(grep_conf 'AWS_REDIRECT' $INSTALL_CONF || echo '0')
-e REDIS_INNER_IP=$(grep_conf 'REDIS_INNER_IP' $INSTALL_CONF)
-e ENABLE_REDIS_SENTINEL=$(grep_conf 'ENABLE_REDIS_SENTINEL' $INSTALL_CONF)
-e SENTINEL_HOSTS=$(grep_conf 'SENTINEL_HOSTS' $INSTALL_CONF)
-e KAFKA_INNER_IP=$(grep_conf 'KAFKA_INNER_IP' $INSTALL_CONF)
-e LOG_MYSQL_INNER_IP=$(grep_conf 'LOG_MYSQL_INNER_IP' $INSTALL_CONF)
-e GATEWAY_NUM=$(grep_conf 'GATEWAY_NUM' $INSTALL_CONF)
"

echo "创建存放日志的文件夹 $LOG_PATH"
if [ ! -d $LOG_PATH ]; then
    mkdir -p $LOG_PATH
fi

echo ${ENV_CONF_PARAM};



# ----------------------------------------------------------------------------
# 启动服务
# ----------------------------------------------------------------------------

if [ `docker ps -a | grep -w $CONTAINER_NAME | wc -l` -gt 0 ];then
    old_image_id=$(docker inspect --format='{{.Image}}' $CONTAINER_NAME)
    docker stop $CONTAINER_NAME;
    docker rm -f $CONTAINER_NAME;
    docker rmi -f $old_image_id || true

else
    # 停止旧的守护脚本和服务
    echo "停止守护脚本 $RUN_SCRIPT"
    run_pids=$(ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep | awk '{print $2}' || true)
    if [ -n "$run_pids" ]; then
        kill -9 $run_pids
        sleep 2
    fi
    echo "停止服务 $APP_NAME"
    app_pids=$(pidof csadapt || true)
    if [ -n "$app_pids" ]; then
        kill -s $SIGNAL $app_pids
        sleep 2
    fi
    sed -i '/csadaptrun.sh/d' /etc/init.d/rc.local
fi
docker run -d -e TZ=Asia/Shanghai ${ENV_CONF_PARAM} --restart=always --net=host -v /usr/share/zoneinfo:/usr/share/zoneinfo -v /var/log/csadaptlog:/var/log/csadaptlog -v /var/core:/var/core -v /etc/ip:/etc/ip -v /var/adapt_sock:/var/adapt_sock -v /etc/kdc.conf:/etc/kdc.conf -v /bin/crypto:/bin/crypto --name ${CONTAINER_NAME} ${DOCKER_IMG} /bin/bash /usr/local/akcs/csadapt/scripts/csadaptrun.sh


#守护进程中会进行环境变量替换配置文件中的内容
#具体看csadapt/scripts/sedconf.sh
