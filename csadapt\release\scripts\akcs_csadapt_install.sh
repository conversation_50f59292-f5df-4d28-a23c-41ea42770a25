#!/bin/bash

#csroute安装脚本,含csroute等两个组件
AKCS_INSTALL_PATH=/usr/local/akcs/csadapt
AKCS_RUN_SCRIPT_NAME=csadaptrun.sh
AKCS_RUN_SCRIPT=${AKCS_INSTALL_PATH}/scripts/${AKCS_RUN_SCRIPT_NAME}
INSTALL_CONF=/etc/csadapt_install.conf
HOST_IP=/etc/ip
WORK_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
PAKCAGES_ROOT=${WORK_DIR}/..
chmod 777 -R ${PAKCAGES_ROOT}/*

if [ ! -d /usr/local/akcs ];then
    mkdir /usr/local/akcs
fi
#read 删除不能用问题
stty erase ^H
font_off(){
	echo -en "\033[0m"
}
blue(){
    echo -e "\033[34m$1\033[0m"
	font_off
}
green(){
    echo -e  "\033[32m$1\033[0m"
	font_off
}
red(){
    echo -e "\033[31m$1\033[0m"
	font_off
}
yellow(){
    echo -e "\033[33m$1\033[0m"
	font_off
}

CheckIPAddr()
{
    echo $1|grep "^[0-9]\{1,3\}\.\([0-9]\{1,3\}\.\)\{2\}[0-9]\{1,3\}$" > /dev/null;
    #IP地址必须为全数字
    if [ $? -ne 0 ]
    then
        return 1
    fi
    ipaddr=$1
    a=`echo $ipaddr|awk -F . '{print $1}'`  #以"."分隔，取出每个列的值
    b=`echo $ipaddr|awk -F . '{print $2}'`
    c=`echo $ipaddr|awk -F . '{print $3}'`
    d=`echo $ipaddr|awk -F . '{print $4}'`
    for num in $a $b $c $d
    do
        if [ $num -gt 255 ] || [ $num -lt 0 ]    #每个数值必须在0-255之间
        then
            return 1
        fi
    done
    return 0
}
EnterHostIPAddr()
{
   #输入内网IP
    yellow "Enter your host server inner IPV4: \c"
    #不能写成这样:read $SERVER_INNER_IP;
    read SERVER_INNER_IP;

    #输入外网IP
    yellow "Enter your host server outer IPV4: \c"
    read SERVERIP;

    #输入IP6
    yellow "Enter your host server IPV6: \c"
    read SERVERIPV6;

    for ip in $SERVER_INNER_IP $SERVERIP ; do
      CheckIPAddr $ip
      if [ $? -eq 0 ]; then
        echo "$ip is valid"
      else
        echo "$ip is invalid, exit"
        exit 1;
      fi
    done
    #写入主机的IP文件
    echo "" >$HOST_IP
    echo "SERVER_INNER_IP=$SERVER_INNER_IP" >>$HOST_IP
    echo "SERVERIP=$SERVERIP" >>$HOST_IP
    echo "SERVERIPV6=$SERVERIPV6" >>$HOST_IP
}
EnterBasicSrvIPAddr()
{
    #输入本机域名
    yellow "Enter the host's domain name: \c"
    read SERVER_DOMAIN;

    #输入etcd内网IP
    yellow "Enter your etcd cluster servers inner IPV4,(eg:************:5204;************:15204;...): \c"
    read ETCD_INNER_IP;

    #输入mysql内网IP
    yellow "Enter your mysql server inner IPV4: \c"
    read MYSQL_INNER_IP;

    #输入PBX的外网IPv4
    yellow "Enter your pbx server outer IPV4: \c"
    read PBX_OUTER_IP;

    #延时队列内网地址IPv4
    yellow "Enter your beanstalkd server inner IPV4: \c"
    read BEANSTALKD_IP;

    #延时队列内网地址IPv4
    yellow "Enter your web server outer IPV4: \c"
    read WEB_IP;

    #输入远程设备访问的域名
    yellow "Enter your remote devices config server domain name : \c"
    read REMOTE_CONIFG_DOMAIN_NAME;

	#选择不同地区网页做不同处理
    blue "Area list:"
    blue "   1)ccloud"
    blue "   2)scloud"
	blue "   3)ecloud"
	blue "   4)ucloud"
	blue "   5)other"
	blue "   6)rcloud"
    yellow "Enter your choice no: \c"
    read SYSTEM_AREA;

	yellow "is AWS environment. Enter 1/0(1 means yes, 0 means no): \c"
    read IS_AWS;
	
    #输入redis内网IP
    yellow "Enter your redis server inner IPV4: \c"
    read REDIS_INNER_IP;	
	
    for ip in  $MYSQL_INNER_IP $PBX_OUTER_IP; do
      CheckIPAddr $ip
      if [ $? -eq 0 ]; then
        echo "$ip is valid"
      else
        echo "$ip is invalid, exit"
        exit 1;
      fi
    done
    #写入基础服务的IP文件
    echo "" >$INSTALL_CONF
    echo "SERVER_DOMAIN=$SERVER_DOMAIN" >>$INSTALL_CONF
    echo "ETCD_INNER_IP=$ETCD_INNER_IP" >>$INSTALL_CONF
    echo "MYSQL_INNER_IP=$MYSQL_INNER_IP" >>$INSTALL_CONF
    echo "PBX_OUTER_IP=$PBX_OUTER_IP" >>$INSTALL_CONF
    echo "BEANSTALKD_IP=$BEANSTALKD_IP" >>$INSTALL_CONF
	echo "REMOTE_CONIFG_DOMAIN_NAME=$REMOTE_CONIFG_DOMAIN_NAME" >>$INSTALL_CONF
	echo "IS_CHINA_VERSION=$IS_CHINA_VERSION" >>$INSTALL_CONF
	echo "IS_AWS=$IS_AWS" >>$INSTALL_CONF
	echo "WEB_IP=$WEB_IP" >>$INSTALL_CONF
	echo "SYSTEM_AREA=$SYSTEM_AREA" >>$INSTALL_CONF
	echo "REDIS_INNER_IP=$REDIS_INNER_IP" >>$INSTALL_CONF
}


EchoHostIPAddr()
{
    inner_ip_str="SERVER_INNER_IP="
    inner_ip_cat=`cat $HOST_IP | grep -w SERVER_INNER_IP | awk -F'=' '{ print $2 }'`
    inner_ip=$inner_ip_str$inner_ip_cat
    echo $inner_ip

    outer_ipv4_str="SERVERIP="
    outer_ipv4_cat=`cat $HOST_IP | grep -w SERVERIP | awk -F'=' '{ print $2 }'`
    outer_ipv4=$outer_ipv4_str$outer_ipv4_cat
    echo $outer_ipv4

    outer_ipv6_str="SERVERIPV6="
    outer_ipv6_cat=`cat $HOST_IP | grep -w SERVERIPV6 | awk -F'=' '{ print $2 }'`
    outer_ipv6=$outer_ipv6_str$outer_ipv6_cat
    echo $outer_ipv6
}


EchoBasicSrvIPAddr()
{
    server_domain_str="SERVER_DOMAIN="
    server_domain_cat=`cat $INSTALL_CONF | grep -w SERVER_DOMAIN | awk -F'=' '{ print $2 }'`
    server_domain=$server_domain_str$server_domain_cat
    echo $server_domain

    etcd_inner_ip_str="ETCD_INNER_IP="
    etcd_inner_ip_cat=`cat $INSTALL_CONF | grep -w ETCD_INNER_IP | awk -F'=' '{ print $2 }'`
    etcd_inner_ip=$etcd_inner_ip_str$etcd_inner_ip_cat
    echo $etcd_inner_ip

    mysql_inner_ip_str="MYSQL_INNER_IP="
    mysql_inner_ip_cat=`cat $INSTALL_CONF | grep -w MYSQL_INNER_IP | awk -F'=' '{ print $2 }'`
    mysql_inner_ip=$mysql_inner_ip_str$mysql_inner_ip_cat
    echo $mysql_inner_ip

    pbx_outer_ipv4_str="PBX_OUTER_IP="
    pbx_outer_ipv4_cat=`cat $INSTALL_CONF | grep -w PBX_OUTER_IP | awk -F'=' '{ print $2 }'`
    pbx_outer_ipv4=$pbx_outer_ipv4_str$pbx_outer_ipv4_cat
    echo $pbx_outer_ipv4

    ipv4_str="BEANSTALKD_IP="
    ipv4_cat=`cat $INSTALL_CONF | grep -w BEANSTALKD_IP | awk -F'=' '{ print $2 }'`
    inner_ipv4=$ipv4_str$ipv4_cat
    echo $inner_ipv4

    ipv4_str="REMOTE_CONIFG_DOMAIN_NAME="
    ipv4_cat=`cat $INSTALL_CONF | grep -w REMOTE_CONIFG_DOMAIN_NAME | awk -F'=' '{ print $2 }'`
    inner_ipv4=$ipv4_str$ipv4_cat
    echo $inner_ipv4

    ipv4_str="WEB_IP="
    ipv4_cat=`cat $INSTALL_CONF | grep -w WEB_IP | awk -F'=' '{ print $2 }'`
    inner_ipv4=$ipv4_str$ipv4_cat
    echo $inner_ipv4
	
	tmp_str="IS_AWS="
    tmp_cat=`cat $INSTALL_CONF | grep -w IS_AWS | awk -F'=' '{ print $2 }'`
    tmp_ipv4=$tmp_str$tmp_cat
    echo $tmp_ipv4 
	
	tmp_str="SYSTEM_AREA="
    tmp_cat=`cat $INSTALL_CONF | grep -w SYSTEM_AREA | awk -F'=' '{ print $2 }'`
    tmp_ipv4=$tmp_str$tmp_cat
    echo $tmp_ipv4
	
	tmp_str="REDIS_INNER_IP="
    tmp_cat=`cat $INSTALL_CONF | grep -w REDIS_INNER_IP | awk -F'=' '{ print $2 }'`
    tmp_ipv4=$tmp_str$tmp_cat
    echo $tmp_ipv4 		
}

function Md5sumCheck()
{
	newfile=$1
	oldfile=$2
	newmd5=`md5sum $newfile|awk '{print $1}'`
	oldmd5=`md5sum $oldfile|awk '{print $1}'`
	if [ $oldmd5 != $newmd5 ];then
	echo "md5sum check error!"
	echo "$oldfile install failed!"
	exit 0

	fi
}
#added by chenyc,2019-03-29,分布式脚本，先确定本机的内外网地址,所有的ip信息放在:/etc/ip里面
if [ -f $HOST_IP ];then
    EchoHostIPAddr
    SERVER_INNER_IP=`cat $HOST_IP | grep -w SERVER_INNER_IP | awk -F'=' '{ print $2 }'`
    SERVERIP=`cat $HOST_IP | grep -w SERVERIP | awk -F'=' '{ print $2 }'`
    SERVERIPV6=`cat $HOST_IP | grep -w SERVERIPV6 | awk -F'=' '{ print $2 }'`

    yellow "please comfirm the host ip information is ok(host ip must contain inner ip and outer ipv4, outer ipv6 is an option.)? Enter 1/0(1 means ok, 0 means no):"
    read yes;
    if [ $yes -eq 0 ];then
        EnterHostIPAddr
    fi
else
    blue "Can not found host ip file </etc/ip>, please enter all information below:"
    EnterHostIPAddr
fi
#再确定redis、mysql、etcd等组件的内网ip信息
if [ -f $INSTALL_CONF ];then
    EchoBasicSrvIPAddr
    SERVER_DOMAIN=`cat $INSTALL_CONF | grep -w SERVER_DOMAIN | awk -F'=' '{ print $2 }'`
    ETCD_INNER_IP=`cat $INSTALL_CONF | grep -w ETCD_INNER_IP | awk -F'=' '{ print $2 }'`
    MYSQL_INNER_IP=`cat $INSTALL_CONF | grep -w MYSQL_INNER_IP | awk -F'=' '{ print $2 }'`
    PBX_OUTER_IP=`cat $INSTALL_CONF | grep -w PBX_OUTER_IP | awk -F'=' '{ print $2 }'`
    BEANSTALKD_IP=`cat $INSTALL_CONF | grep -w BEANSTALKD_IP | awk -F'=' '{ print $2 }'`
	REMOTE_CONIFG_DOMAIN_NAME=`cat $INSTALL_CONF | grep -w REMOTE_CONIFG_DOMAIN_NAME | awk -F'=' '{ print $2 }'`
	WEB_IP=`cat $INSTALL_CONF | grep -w WEB_IP | awk -F'=' '{ print $2 }'`
	SYSTEM_AREA=`cat $INSTALL_CONF | grep -w SYSTEM_AREA | awk -F'=' '{ print $2 }'`	
	IS_AWS=`cat $INSTALL_CONF | grep -w IS_AWS | awk -F'=' '{ print $2 }'`
	REDIS_INNER_IP=`cat $INSTALL_CONF | grep -w REDIS_INNER_IP | awk -F'=' '{ print $2 }'`
    yellow "please comfirm the basic server inner ip information is ok? Enter 1/0(1 means ok, 0 means no):"
    read yes;
    if [ $yes -eq 0 ];then
        EnterBasicSrvIPAddr
    fi
else
    blue "Can not found system config </etc/csadapt_install.conf>, please enter all information below:"
    EnterBasicSrvIPAddr
fi

#replace serverip,注意ip后面的等号不能有空格
sed -i "s/^.*etcd_srv_net=.*/etcd_srv_net=${ETCD_INNER_IP}/g" ${PAKCAGES_ROOT}/csadapt/conf/csadapt.conf
sed -i "s/^.*db_ip=.*/db_ip=${MYSQL_INNER_IP}/g" ${PAKCAGES_ROOT}/csadapt/conf/csadapt.conf
sed -i "s/^.*csadapt_outerip=.*/csadapt_outerip=${SERVERIP}/g" ${PAKCAGES_ROOT}/csadapt/conf/csadapt.conf
sed -i "s/^.*cspbx_ip=.*/cspbx_ip=${PBX_OUTER_IP}/g" ${PAKCAGES_ROOT}/csadapt/conf/csadapt.conf
sed -i "s/^.*csadapt_outerdomain=.*/csadapt_outerdomain=${SERVER_DOMAIN}/g" ${PAKCAGES_ROOT}/csadapt/conf/csadapt.conf
sed -i "s/^.*beanstalkd_ip=.*/beanstalkd_ip=${BEANSTALKD_IP}/g" ${PAKCAGES_ROOT}/csadapt/conf/csadapt.conf
sed -i "s/^.*remote_config_domain=.*/remote_config_domain=${REMOTE_CONIFG_DOMAIN_NAME}/g" ${PAKCAGES_ROOT}/csadapt/conf/csadapt.conf
sed -i "s/^.*web_ip=.*/web_ip=${WEB_IP}/g" ${PAKCAGES_ROOT}/csadapt/conf/csadapt.conf
sed -i "s/^.*system_area_type=.*/system_area_type=${SYSTEM_AREA}/g" ${PAKCAGES_ROOT}/csadapt/conf/csadapt.conf
sed -i "s/^.*is_aws=.*/is_aws=${IS_AWS}/g" ${PAKCAGES_ROOT}/csadapt/conf/csadapt.conf
sed -i "s/^.*userdetail_host=.*/userdetail_host=${REDIS_INNER_IP}/g" ${PAKCAGES_ROOT}/csadapt/conf/csadapt_redis.conf

#后于之前替换的代码,保证db redis配置不被覆盖
#add by chenzhx 20200702先关闭选择，避免安装时候误操作
#bash $WORK_DIR/dbproxy-install.sh $INSTALL_CONF ${PAKCAGES_ROOT}/csadapt/conf/csadapt.conf
bash $WORK_DIR/redis-install.sh $INSTALL_CONF ${PAKCAGES_ROOT}/csadapt/conf/csadapt_redis.conf

scriptpid=`ps -fe|grep "${AKCS_RUN_SCRIPT_NAME}" |grep -v grep |awk '{print $2}'`
if [ -n "${scriptpid}" ];then
    echo "${AKCS_RUN_SCRIPT_NAME} is running at ${scriptpid}, will kill it first."
	kill -kill ${scriptpid}
	sleep 2
fi

echo "stopping csadapt services..."
${PAKCAGES_ROOT}/csadapt_scripts/csadaptctl.sh stop
sleep 1
echo "making logs directories..."
if [ ! -d /var/log/csadaptlog ]; then
    mkdir -p /var/log/csadaptlog
fi


echo "copying akcs csadapt files..."
if [ -d /usr/local/akcs/csadapt ]; then
    rm -rf  /usr/local/akcs/csadapt
fi

if [ -d /usr/local/akcs/csadapt_scripts ]; then
    rm -rf  /usr/local/akcs/csadapt_scripts
fi

chmod 777 -R /usr/local/akcs/

cp -rf ${PAKCAGES_ROOT}/csadapt/ /usr/local/akcs
#4.6新增md5sum校验，避免拷贝不完全
Md5sumCheck ${PAKCAGES_ROOT}/csadapt/bin/csadapt /usr/local/akcs/csadapt/bin/csadapt

mkdir -p /usr/local/akcs/csadapt/scripts
chmod -R 777 /usr/local/akcs/csadapt/scripts/
cp -rf ${PAKCAGES_ROOT}/csadapt_scripts/* /usr/local/akcs/csadapt/scripts/


#增加dbuser01账号的ip地址支持
mysql -N  -h ${MYSQL_INNER_IP} -u root -pAk@56@<EMAIL> -e "grant select,insert,delete,update,create,drop,INDEX on AKCS.*  to dbuser01@\"${SERVER_INNER_IP}\" Identified by \"Ak@56@<EMAIL>\"" 2>/dev/null
mysql -N  -h ${MYSQL_INNER_IP} -u root -pAk@56@<EMAIL> -e "grant select,insert,delete,update,create,drop,INDEX on freeswitch.*  to dbuser01@\"${SERVER_INNER_IP}\" Identified by \"Ak@56@<EMAIL>\"" 2>/dev/null
mysql -N  -h ${MYSQL_INNER_IP} -u root -pAk@56@<EMAIL>  -e "flush privileges" 2>/dev/null

#add run script to rc.local
if [ -z "`grep "${AKCS_RUN_SCRIPT}" /etc/init.d/rc.local`" ];then
	echo "bash ${AKCS_RUN_SCRIPT} &" >> /etc/init.d/rc.local
fi

echo "starting services..."
chmod 777 /usr/local/akcs/csadapt/scripts/csadaptctl.sh
/usr/local/akcs/csadapt/scripts/csadaptctl.sh start

sleep 1
chmod 777 ${AKCS_RUN_SCRIPT}
if [ -z "`ps -fe|grep "${AKCS_RUN_SCRIPT_NAME}" |grep -v grep`" ];then
	nohup bash ${AKCS_RUN_SCRIPT} >/dev/null 2>&1 &
fi
echo "csadapt install completed ..."

#echo status
/usr/local/akcs/csadapt/scripts/csadaptctl.sh status
