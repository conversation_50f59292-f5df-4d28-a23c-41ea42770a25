#!/bin/bash
ACMD="$1"
CSADAPT_BIN='/usr/local/akcs/csadapt/bin/csadapt'

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_csadapt()
{
	nohup $CSADAPT_BIN >/dev/null 2>&1 &
    echo "Start csadapt successful"
    if [ -z "`ps -fe|grep "csadaptrun.sh" |grep -v grep`" ];then
        nohup bash /usr/local/akcs/csadapt/scripts/csadaptrun.sh >/dev/null 2>&1 &
    fi
}
stop_csadapt()
{
    echo "Begin to stop csadaptrun.sh"
    csadaptrunid=`ps aux | grep -w csadaptrun.sh | grep -v grep | awk '{ print $(2) }'`
    if [ -n "${csadaptrunid}" ];then
	    echo "csadaptrun.sh is running at ${csadaptrunid}, will kill it first."
	    kill -9 ${csadaptrunid}
    fi
    echo "Begin to stop csadapt"
    kill -9 `pidof csadapt`
    sleep 2
    echo "Stop csadapt successful"
}

case $ACMD in
  start)
     start_csadapt
    ;;
  stop)
     stop_csadapt
    ;;
  restart)
    stop_csadapt
    sleep 1
    start_csadapt
    ;;
  status)
    if [ -f /var/run/csadapt.pid ];then
        pid=`cat /var/run/csadapt.pid`
        if [ $pid"x" = "x" ];then
           #pid里面的文件是空的
           pid="xxxxxxxxxx"
        fi
    else
        #重启之后没有这个pid文件
        pid="xxxxxxxxxx"
    fi
    cnt=`ls /proc/$pid | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m csadapt is stop!!!\033[0m"
        exit 1
    else
        echo "\033[0;32m csadapt is running \033[0m"
        exit 0
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

