<?php
ini_set('date.timezone','Asia/Shanghai');

// 消费组信息 /usr/local/kafka/bin/kafka-consumer-groups.sh  --bootstrap-server 172.18.41.221:8520 --group notify_app_backend_group --describe
// 消息指定消息 /usr/local/kafka/bin/kafka-console-consumer.sh --bootstrap-server 172.18.41.221:8520  --topic notify_app_backend_group --offset 10  --partition 0 --max-messages 1
// 主题信息  /usr/local/kafka/bin/kafka-topics.sh --zookeeper 172.18.41.221:8521 --describe --topic notify_app_backend
// 修改分区数量 /usr/local/kafka/bin/kafka-topics.sh --alter  --zookeeper 172.18.41.221:8521  --topic notify_app_backend  --partitions 5

function cmd_usage($cmd)
{
    echo("Usage: \n");
    echo("  Kafka Commands:\n");
    echo("    ".$cmd. " get <partition> <offset>   # 获取指定分区和偏移量的消息\n");  
    echo("    ".$cmd. " list                        # 列出消费者组的描述信息\n");
    
    echo("\n  Metrics Command:\n");
    echo("    ".$cmd. " metrics                    # 获取csroute的指标数据\n");  
    exit(0);
}

function parse_conf()
{
	$fileContent = file('/usr/local/akcs/csadapt/conf/csadapt.conf');
	
	$configs = array();
	foreach ($fileContent as $line) {
		$line = trim($line); // 去掉开头和结尾的空格和换行符
        if (!empty($line)) { // 跳过空行
            parse_str($line, $config);
            $configs = array_merge($configs, $config);
        }
	}
	return $configs;
}

function parse_etc_ip()
{
	$fileContent = file('/etc/ip');
	
	$configs = array();
	foreach ($fileContent as $line) {
		$line = trim($line); // 去掉开头和结尾的空格和换行符
        if (!empty($line)) { // 跳过空行
            parse_str($line, $config);
            $configs = array_merge($configs, $config);
        }
	}
	return $configs;
}

if ($argc < 2){  
	cmd_usage($argv[0]);
}

if ($argv[1] == "get") 
{
	$partition = $argv[2];
	$offset = $argv[3];

	$consumer_config = parse_conf();
	$kafka_ip = $consumer_config['kafka_broker_ip'];
	echo "Excute the following command on running kafka server machine\n";
	echo "/usr/local/kafka/bin/kafka-console-consumer.sh --bootstrap-server $kafka_ip --topic notify_app_backend --offset $offset  --partition $partition --max-messages 1\n";
	echo "新办公\n";
	echo "email/sms队列:\n";
	echo "/usr/local/kafka/bin/kafka-console-consumer.sh --bootstrap-server $kafka_ip --topic notify-appbackend-push --offset $offset --partition $partition --max-messages 1\n";    
	echo "实时通知设备:\n";
	echo "/usr/local/kafka/bin/kafka-console-consumer.sh --bootstrap-server $kafka_ip --topic notify-appbackend-notify --offset $offset --partition $partition --max-messages 1\n";    
	echo "消息通知\n";
	echo "/usr/local/kafka/bin/kafka-console-consumer.sh --bootstrap-server $kafka_ip --topic notify_web_message --offset $offset --partition $partition --max-messages 1\n";    
}
elseif ($argv[1] == "list")
{
	$consumer_config = parse_conf();
	$kafka_ip = $consumer_config['kafka_broker_ip'];
	echo "Excute the following command on running kafka server machine\n";
	echo "/usr/local/kafka/bin/kafka-consumer-groups.sh  --bootstrap-server $kafka_ip --group notify_app_backend_group --describe\n";	
	echo "新办公\n";
	echo "email/sms队列\n";
	echo "/usr/local/kafka/bin/kafka-consumer-groups.sh  --bootstrap-server $kafka_ip --group notify-appbackend-push_group --describe\n";	
	echo "实时通知设备\n";
	echo "/usr/local/kafka/bin/kafka-consumer-groups.sh  --bootstrap-server $kafka_ip --group notify-appbackend-notify_group --describe\n";	
	echo "消息通知\n";
	echo "/usr/local/kafka/bin/kafka-consumer-groups.sh  --bootstrap-server $kafka_ip --group notify_web_message_group --describe\n";	

}
elseif ($argv[1] == "metrics") 
{
	$etc_ip = parse_etc_ip();
	$SERVER_INNER_IP = $etc_ip['SERVER_INNER_IP'];
    echo "curl $SERVER_INNER_IP:9241/metrics\n"; 
	$output = shell_exec("curl $SERVER_INNER_IP:9241/metrics");
	echo $output;

}
else
{
	cmd_usage($argv[0]);
}

?>