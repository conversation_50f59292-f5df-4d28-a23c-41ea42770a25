#!/bin/bash

#守护脚本启动前，先设置配置文件
/bin/bash /usr/local/akcs/csadapt/scripts/sedconf.sh

PROCESS_NAME=csadapt
PROCESS_START_CMD="/usr/local/akcs/csadapt/scripts/csadaptctl.sh start"
PROCESS_PID_FILE=/var/run/csadapt.pid
LOG_FILE=/var/log/csadapt_run_daemon.log
PROCESS_COMMON_SCRIPTS="/usr/local/akcs/csadapt/scripts/common.sh"
LOG_BACK_SCRIPTS="/usr/local/akcs/csadapt/scripts/log_back.sh"
csadaptlog_path="/var/log/csadaptlog"
CSADAPT_BIN='/usr/local/akcs/csadapt/bin/csadapt'

#一定要是绝对路径，不然就变成要指定目录执行这个run
source $PROCESS_COMMON_SCRIPTS
source $LOG_BACK_SCRIPTS

#容器化后直接前台运行，这样挂了之后docker也会重启，这样才能监控检测
$CSADAPT_BIN >/dev/null 2>&1

