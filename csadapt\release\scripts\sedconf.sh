#!/bin/bash
# 替换配置文件
echo '替换配置文件的配置'

sed -i "
    s/^.*etcd_srv_net=.*/etcd_srv_net=${ETCD_INNER_IP}/g
    s/^.*db_ip=.*/db_ip=${MYSQL_INNER_IP}/g
    s/^.*csadapt_outerip=.*/csadapt_outerip=${WEB_IP}/g
    s/^.*cspbx_ip=.*/cspbx_ip=${PBX_OUTER_IP}/g
    s/^.*web_domain=.*/web_domain=${WEB_DOMAIN}/g
    s/^.*beanstalkd_ip=.*/beanstalkd_ip=${BEANSTALKD_IP}/g
    s/^.*beanstalkd_backup_ip=.*/beanstalkd_backup_ip=${BEANSTALKD_BACKUP_IP}/g    
    s/^.*remote_config_domain=.*/remote_config_domain=${REMOTE_CONIFG_DOMAIN_NAME}/g
    s/^.*web_ip=.*/web_ip=${WEB_IP}/g
    s/^.*system_area_type=.*/system_area_type=${SYSTEM_AREA}/g
	s/^.*aws_redirect=.*/aws_redirect=${AWS_REDIRECT}/g
	s/^.*kafka_broker_ip=.*/kafka_broker_ip=${KAFKA_INNER_IP}:8520/g
    s/^.*db_log_ip=.*/db_log_ip=${LOG_MYSQL_INNER_IP}/g
    s/^.*db_log_port=.*/db_log_port=3306/g
    s/^.*gateway_num=.*/gateway_num=${GATEWAY_NUM}/g
    s/^.*is_aws=.*/is_aws=${IS_AWS}/g" /usr/local/akcs/csadapt/conf/csadapt.conf

	
# redis 配置
sed -i "
    s/^.*proc_record_host=.*/proc_record_host=${REDIS_INNER_IP}/g
    s/^.*appcode_host=.*/appcode_host=${REDIS_INNER_IP}/g
    " /usr/local/akcs/csadapt/conf/csadapt_redis.conf

# redis sentinel 配置
if [ "$ENABLE_REDIS_SENTINEL" = "1" ]; then
    sed -i "s/^.*sentinels=.*/sentinels=${SENTINEL_HOSTS}/g" /usr/local/akcs/csadapt/conf/csadapt_redis.conf
else
    sed -i "s/^.*sentinels=.*/sentinels=/g" /usr/local/akcs/csadapt/conf/csadapt_redis.conf
fi
