用于运行的容器镜像中的可执行文件以及动态库都是strip过的，要gdb调试详细信息，可按如下步骤到调试环境调试

调试环境搭建（基础ubuntu20环境）：
mkdir -p /root/cpp_ubuntu20
docker run -itd -e TZ=Asia/Shanghai --restart=always --net=host -v /root/cpp_ubuntu20_debug:/root/cpp_ubuntu20_debug --name cpp_ubuntu20_debug registry.cn-hangzhou.aliyuncs.com/ak_system/cpp_docker_ubuntu20:1.0_debug /bin/bash

要调试对应组件时：
docker run --rm -v /root/cpp_ubuntu20_debug:/root/cpp_ubuntu20_debug registry.cn-hangzhou.aliyuncs.com/ak_system/csadapt:csadapt_dev_dev_7.1.0_zhiwei.chen_csadapt_dev_7.1.0_zhiwei.chen_csadapt_20241209135649_debug cp -rf /archive /root/cpp_ubuntu20_debug
上面的registry.cn-hangzhou.aliyuncs.com/ak_system/csadapt:csadapt_dev_dev_7.1.0_zhiwei.chen_csadapt_dev_7.1.0_zhiwei.chen_csadapt_20241209135649_debug代表的是对应版本未strip掉的软件归档包
即{镜像名:版本号}_debug，看下当前运行的容器用的是哪个{镜像名:版本号} 然后拼上_debug即可

然后进入到cpp_ubuntu20_debug容器中进行gdb调试即可

docker run --rm -v /root/cpp_ubuntu20_debug:/root/cpp_ubuntu20_debug registry.cn-hangzhou.aliyuncs.com/ak_system/csstorage:csstorage_dev_dev_7.1.3_csstorage_docker_dev_7.1.3_csstorage_docker_20250429170037_debug cp -rf /archive /root/cpp_ubuntu20_debug
