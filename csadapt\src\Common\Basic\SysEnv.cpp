#include <stdio.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <errno.h>
#include <pthread.h>
#include <string.h>
#include <ctype.h>
#include <string>

#include "BasicDefine.h"
#include "iconv.h"
#include "SysEnv.h"

int code_convert(const char* from_charset, char* inbuf, size_t inlen, const char* to_charset, char* outbuf, size_t outlen)
{
    iconv_t cd;
    int rc;
    char** pin = &inbuf;
    char** pout = &outbuf;

    cd = iconv_open(to_charset, from_charset);
    if (cd == 0)
    {
        return -1;
    }
    memset(outbuf, 0, outlen);
    if (iconv(cd, pin, &inlen, pout, &outlen) == -1)
    {
        return -1;
    }
    iconv_close(cd);
    return 0;
}
//UNICODE码转为GB2312码
int CSADAPT_U2G(char* inbuf, size_t inlen, char* outbuf, size_t outlen)
{
    return code_convert("utf-8", inbuf, inlen, "gb2312", outbuf, outlen);
}
//GB2312码转为UNICODE码
int CSADAPT_G2U(char* inbuf, size_t inlen, char* outbuf, size_t outlen)
{
    return code_convert("gb2312", inbuf, inlen, "utf-8", outbuf, outlen);
}

int GetLastError()
{
    return errno;
}