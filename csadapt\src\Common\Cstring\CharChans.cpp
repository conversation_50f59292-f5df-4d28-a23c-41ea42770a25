#include <stdio.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <errno.h>
#include <pthread.h>
#include <string.h>
#include <ctype.h>
#include <string>

#include "BasicDefine.h"
#include "CharChans.h"
#include "SysEnv.h"
#include "util.h"

int TransUtf8ToTchar(const char* pszSrc, TCHAR* pszDst, int nDstSize)
{
    if (pszSrc == NULL
            || pszDst == NULL
            || nDstSize <= 0)
    {
        return -1;
    }
    Snprintf(pszDst, nDstSize, pszSrc);
    return 0;
}

int TransTcharToUtf8(const TCHAR* pszSrc, char* pszDst, int nDstSize)
{
    if (pszSrc == NULL
            || pszDst == NULL
            || nDstSize <= 0)
    {
        return -1;
    }
    int nSrcLen = strlen(pszSrc) + 1;
    char* pTmpSrc = new (std::nothrow) char[nSrcLen];
    if (pTmpSrc == NULL)
    {
        return -1;
    }
    memset(pTmpSrc, 0, nSrcLen);
    Snprintf(pTmpSrc, nSrcLen, pszSrc);

    int nRet = CSADAPT_G2U(pTmpSrc, strlen(pszSrc), pszDst, (size_t)nDstSize);
    delete[] pTmpSrc;

    return nRet;
}
char* _tcscpy_s(char* pszDst, uint32_t nsize, const char* pszSrc)
{
    Snprintf(pszDst, nsize, pszSrc);
    return pszDst;
}

char* strcat_s(char* dest, int n, const char* src)
{
    return strncat(dest, src, n);
}


