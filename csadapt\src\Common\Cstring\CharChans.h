#ifndef   __CHAR_CHANS_H__
#define   __CHAR_CHANS_H__

#ifdef __cplusplus
extern "C" {
#endif

#ifndef sprintf_s
#define sprintf_s snprintf
#endif

int TransUtf8ToTchar(const char* pszSrc, TCHAR* pszDst, int nDstSize);
char* _tcscpy_s(char* pszDst, unsigned int nsize, const char* pszSrc);
int TransTcharToUtf8(const TCHAR* pszSrc, char* pszDst, int nDstSize);
char* strcat_s(char* dest, int n, const char* src);

#ifdef __cplusplus
}
#endif

#endif //__CHAR_CHANS_H__
