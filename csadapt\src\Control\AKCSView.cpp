#include <stdio.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <errno.h>
#include <pthread.h>
#include <assert.h>
#include <string.h>
#include <ctype.h>
#include <vector>
#include <string>
#include <sstream>
#include <queue>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "AdaptDef.h"
#include "AKCSMsg.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "IPCControl.h"
#include "AKCSView.h"
#include "AkLogging.h"
#include "SafeCacheConn.h"
#include "redis/PubSubManager.h"
#include "util.h"
#include <evpp/evnsq/producer.h>
#include "dbinterface/CommunityInfo.h"
#include "AK.Adapt.pb.h"
#include "AK.Crontab.pb.h"
#include "AkcsMsgDef.h"
#include "AK.Server.pb.h"
#include "AK.Route.pb.h"
#include "AkcsPduBase.h"
#include "AdaptMQProduce.h"
#include "ConfigFileReader.h"
#include <boost/algorithm/string.hpp>
#include "AkcsCommonDef.h"
#include "AkcsMonitor.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/Delivery.h"
#include "dbinterface/Staff.h"
#include "dbinterface/CommunityInfo.h"
#include "dbinterface/Account.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "gid/SnowFlakeGid.h"
#include "dbinterface/PmAccountMap.h"
#include "dbinterface/ProjectInfo.h"
#include "dbinterface/PropertyInfo.h"
#include "dbinterface/AccountMap.h"
#include "dbinterface/AccountUserInfo.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/PmEmergencyDoorLog.h"
#include "BackendP2PMsgControl.h"
#include "dbinterface/Log/PersonalCapture.h"
#include "dbinterface/AlarmDB.h"
#include "dbinterface/PersonalAccountCnf.h"
#include "dbinterface/DistributorInfo.h"
#include "SL50/DownMessage/LockControl.h"
#include "SL50/DownMessage/UnLockControl.h"
#include "SL50/DownMessage/LockUpdateConfig.h"

extern PubSubManager* g_pub_sub_mng_ptr;
extern CSADAPT_CONF gstCSADAPTConf;
extern evnsq::Producer* g_nsq_pub_mng_ptr;
extern RouteMQProduce* g_nsq_producer;
extern LOG_DELIVERY gstAKCSLogDelivery;

uint64_t g_openapi_health_check_count = 0;
static const char channel_pic_del[] = "ak_redis_pic_del";

#define PHP_MAIL_TO_RESET_PASSWD  "php /usr/local/akcs/csadapt/scripts/adapt.mail.php "
#define PHP_MAIL_TO_CREATE_UID  "php /usr/local/akcs/csadapt/scripts/create_uid.mail.php "
#define PHP_MAIL_TO_CHANGE_PWD  "php /usr/local/akcs/csadapt/scripts/change_pwd.mail.php "
#define FAKE_EMAIL_QUEUE_SIZE  5
#define FAKE_EMAIL_BIT_DIFF  2


CAKCSView::CAKCSView()
{

}

CAKCSView::~CAKCSView()
{

}

CAKCSView* CAKCSView::instance = NULL;

CAKCSView* CAKCSView::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CAKCSView();
    }

    return instance;
}
CAKCSView* GetAKCSViewInstance()
{
    return CAKCSView::GetInstance();
}

// 检查个人APP的智能家居权限
bool CAKCSView::CheckPersonalSmarthome(const ResidentPerAccount &account)
{
    std::string target_uuid;
    // 确定要查询的UUID
    if (account.role == ACCOUNT_ROLE_PERSONNAL_MAIN)
    {
        target_uuid = account.uuid;
    }
    else if (account.role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT)
    {
        target_uuid = account.parent_uuid;
    }

    // 查询账户信息并检查智能家居权限
    ResidentPerAccount per_account;
    return (0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(target_uuid, per_account))
           && per_account.enable_smarthome;
}

// 检查社区APP的智能家居权限
bool CAKCSView::CheckCommunitySmarthome(const ResidentPerAccount &account)
{
    std::string node_uuid;
    if (account.role == ACCOUNT_ROLE_PERSONNAL_MAIN)
    {
        node_uuid = account.uuid;
    }
    else if (account.role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT)
    {
        node_uuid = account.parent_uuid;
    }

    // 获取DistributorInfo
    DistributorInfoSt dis_info;
    dbinterface::DistributorInfo::GetDistributorInfoByNodeUUID(node_uuid, dis_info);

    // 检查社区是否启用智能家居
    CommunityInfoPtr comm_info = std::make_shared<CommunityInfo>(account.account);
    if (!comm_info || !comm_info->EnableSmartHome())
    {
        return false;
    }

    // 物业管理员直接返回false, 不管社区开关都要发
    if (account.role == ACCOUNT_ROLE_COMMUNITY_PM)
    {
        return false;
    }

    // 判断apt家居开关
    return dbinterface::ResidentPersonalAccount::IsCommunityAptEnableSmartHome(account);
}

int CAKCSView::CheckEnableSmarthome(const ResidentPerAccount &account)
{
    switch (account.conn_type)
    {
        case csmain::DeviceType::PERSONNAL_APP:
            return CheckPersonalSmarthome(account) ? 1 : 0;

        case csmain::DeviceType::COMMUNITY_APP:
            return CheckCommunitySmarthome(account) ? 1 : 0;

        default:
            return 0;
    }
}

int CAKCSView::CheckEnableSmarthomeByEmail(const std::string &email)
{
    ResidentPerAccount  account;
    memset(&account, 0, sizeof(account));
    if (0 != dbinterface::ResidentPersonalAccount::GetEmailAccount(email, account))
    {
        AK_LOG_INFO << "email not found app.email=" << email;
        return 0;
    }
    return CheckEnableSmarthome(account);
}

int CAKCSView::CheckEnableSmarthomeByUid(const std::string &uid)
{
    ResidentPerAccount  account;
    memset(&account, 0, sizeof(account));
    if (0 != dbinterface::ResidentPersonalAccount::GetUidAccount(uid, account))
    {
        AK_LOG_INFO << "uid not found app.uid=" << uid;
        return 0;
    }
    return CheckEnableSmarthome(account);
}


//重启设备
void CAKCSView::OnDeviceReboot(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnDeviceReboot param is null";
        return;
    }

    int nRet = -1;
    socklen_t nSockAddrlen = sizeof(struct sockaddr_in);

    CSP2A_REBOOT_DEVICE stDeviceInfo;
    CSP2A_REBOOT_DEVICE* pstDeviceInfo = &stDeviceInfo;
    memset(pstDeviceInfo, 0, sizeof(CSP2A_REBOOT_DEVICE));
    AK::Adapt::DevRebootNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstDeviceInfo->szMac, sizeof(pstDeviceInfo->szMac), msg.mac().c_str());

    AK_LOG_INFO << "OnDeviceReboot mac:" << pstDeviceInfo->szMac;
    if (strlen(pstDeviceInfo->szMac) <= 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    //发送请求设备状态的消息给csmain进程
    if (GetIPCControlInstance()->SendRebootDev(pstDeviceInfo) < 0)
    {
        AK_LOG_WARN << "SendRebootDev failed";
        return;
    }
}

//重置设备
void CAKCSView::OnDeviceReset(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnDeviceReset param is null";
        return;
    }

    int nRet = -1;
    socklen_t nSockAddrlen = sizeof(struct sockaddr_in);

    CSP2A_REBOOT_DEVICE stDeviceInfo;
    CSP2A_REBOOT_DEVICE* pstDeviceInfo = &stDeviceInfo;
    memset(pstDeviceInfo, 0, sizeof(CSP2A_REBOOT_DEVICE));
    AK::Adapt::DevRebootNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstDeviceInfo->szMac, sizeof(pstDeviceInfo->szMac), msg.mac().c_str());

    AK_LOG_INFO << "OnDeviceReset mac:" << pstDeviceInfo->szMac;
    if (strlen(pstDeviceInfo->szMac) <= 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    //发送请求设备状态的消息给csmain进程
    if (GetIPCControlInstance()->SendResetDev(pstDeviceInfo) < 0)
    {
        AK_LOG_WARN << "SendRebootDev failed";
        return;
    }
}

//客户端请求重置密码
void CAKCSView::OnResetPasswd(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnResetPasswd The param is NULL";
        return;
    }
    CSP2A_USER_EAMIL_INFO stUserEmailInfo;
    CSP2A_USER_EAMIL_INFO* pstUserEmailInfo = &stUserEmailInfo;
    memset(pstUserEmailInfo, 0, sizeof(CSP2A_USER_EAMIL_INFO));
    AK::Adapt::ResetPasswd msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstUserEmailInfo->szEmail, sizeof(pstUserEmailInfo->szEmail), msg.email().c_str());
    Snprintf(pstUserEmailInfo->szToken, sizeof(pstUserEmailInfo->szToken), msg.token().c_str());
    Snprintf(pstUserEmailInfo->szUser, sizeof(pstUserEmailInfo->szUser), msg.user().c_str());
    Snprintf(pstUserEmailInfo->szWebIP, sizeof(pstUserEmailInfo->szWebIP), msg.web_ip().c_str());
    Snprintf(pstUserEmailInfo->szRoleType, sizeof(pstUserEmailInfo->szRoleType), msg.role_type().c_str());
    AK_LOG_INFO << "Request reset passwd notify. uid:" << pstUserEmailInfo->szUser << " email:" << pstUserEmailInfo->szEmail;
    if (strlen(pstUserEmailInfo->szUser) <= 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }

    if (strlen(pstUserEmailInfo->szEmail) <= 0)
    {
        ResidentPerAccount account;
        memset(&account, 0, sizeof(account));
        if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(pstUserEmailInfo->szUser, account))
        {
            if (account.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT || account.role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT)
            {
                ResidentPerAccount main_account;
                memset(&main_account, 0, sizeof(main_account));
                if (0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(account.parent_uuid, main_account)
                    && (strlen(main_account.getEmail()) > 0))
                {
                    Snprintf(pstUserEmailInfo->szEmail, sizeof(pstUserEmailInfo->szEmail), main_account.getEmail());
                    Snprintf(pstUserEmailInfo->szUser, sizeof(pstUserEmailInfo->szUser), main_account.account);
                    GetIPCControlInstance()->SendPerResetPwdMailToMaster(pstUserEmailInfo);
                }
                else
                {
                    AK_LOG_WARN << "parameter error";
                }
                return;
            }
        }
    }

    //给csmain发送通知,用户重置密码需要发送邮箱
    if (GetIPCControlInstance()->SendPerResetPwdMail(pstUserEmailInfo) != 0)
    {
        AK_LOG_WARN << "Send create uid mail to csmain failed";
        return;
    }
}

//创建帐号
void CAKCSView::OnCreateUid(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnCreateUid The param is NULL";
        return;
    }

    CSP2A_USER_CREATE_INFO stUserCreateInfo;
    CSP2A_USER_CREATE_INFO* pstUserCreateInfo = &stUserCreateInfo;
    memset(pstUserCreateInfo, 0, sizeof(CSP2A_USER_CREATE_INFO));
    AK::Adapt::PerCreateUser msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstUserCreateInfo->szEmail, sizeof(pstUserCreateInfo->szEmail), msg.email().c_str());
    Snprintf(pstUserCreateInfo->szPwd, sizeof(pstUserCreateInfo->szPwd), msg.pwd().c_str());
    Snprintf(pstUserCreateInfo->szQRCodeBody, sizeof(pstUserCreateInfo->szQRCodeBody), msg.qrcode_body().c_str());
    Snprintf(pstUserCreateInfo->szQRCodeUrl, sizeof(pstUserCreateInfo->szQRCodeUrl), msg.qrcode_url().c_str());
    Snprintf(pstUserCreateInfo->szUser, sizeof(pstUserCreateInfo->szUser), msg.user().c_str());

    if (strlen(gstCSADAPTConf.web_domain) > 1)
    {
        ::snprintf(pstUserCreateInfo->szServerWebUrl, sizeof(pstUserCreateInfo->szServerWebUrl), "https://%s", gstCSADAPTConf.web_domain);
    }
    else
    {
        ::snprintf(pstUserCreateInfo->szServerWebUrl, sizeof(pstUserCreateInfo->szServerWebUrl), "http://%s", gstCSADAPTConf.szCsadaptOuterIP);
    }

    AK_LOG_INFO << "Request personal createuid:" << pstUserCreateInfo->szUser << " email:" << pstUserCreateInfo->szEmail;
    if (strlen(pstUserCreateInfo->szUser) <= 0 || strlen(pstUserCreateInfo->szPwd) <= 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }

    ResidentPerAccount personal_account;
    memset(&personal_account, 0, sizeof(personal_account));
    int ret = dbinterface::ResidentPersonalAccount::GetUidAccount(pstUserCreateInfo->szUser, personal_account);
    pstUserCreateInfo->enable_smarthome = CheckEnableSmarthome(personal_account);

    if (ret == 0 && strlen(pstUserCreateInfo->szEmail) <= 0)
    {
        //查询是否为从账号
        if (personal_account.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT || personal_account.role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT)
        {
            ResidentPerAccount master_personal_account;
            memset(&master_personal_account, 0, sizeof(master_personal_account));
            if (0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(personal_account.parent_uuid, master_personal_account))
            {
                if (strlen(master_personal_account.getEmail()) > 0)
                {
                    //从账户发主账号邮件既需要知道主账号的uid，也需要知道自身的uid
                    Snprintf(pstUserCreateInfo->user_uid, sizeof(pstUserCreateInfo->szUser), msg.user().c_str());
                    Snprintf(pstUserCreateInfo->szEmail, sizeof(pstUserCreateInfo->szEmail), master_personal_account.getEmail());
                    Snprintf(pstUserCreateInfo->szUser, sizeof(pstUserCreateInfo->szUser), master_personal_account.account);
                    GetIPCControlInstance()->SendPerCreateUidMailToMaster(pstUserCreateInfo);
                }
                else if (strlen(master_personal_account.getMobileNumber()) > 0)
                {
                    AK::Server::SendSmsCreateUid sms_create_uid;
                    sms_create_uid.set_phone(master_personal_account.getMobileNumber());
                    sms_create_uid.set_area_code(master_personal_account.phone_code);
                    sms_create_uid.set_user(pstUserCreateInfo->szUser);
                    sms_create_uid.set_pwd(pstUserCreateInfo->szPwd);
                    sms_create_uid.set_type(SMS_FAMILY_CREATE_UID);
                    sms_create_uid.set_language(master_personal_account.language);
                    sms_create_uid.set_enable_smarthome(pstUserCreateInfo->enable_smarthome);

                    AK_LOG_INFO << "Send CreateUidSmsMsg=" << sms_create_uid.DebugString();

                    CAkcsPdu pdu;
                    pdu.SetMsgBody(&sms_create_uid);
                    pdu.SetHeadLen(sizeof(PduHeader_t));
                    pdu.SetVersion(50);
                    pdu.SetCommandId(MSG_C2S_SEND_SMS_CREATE_UID);
                    pdu.SetSeqNum(0);
                    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
                }
                else
                {
                    AK_LOG_WARN << "parameter error";
                }
            }
            
            return;
        }
        else
        {
            AK_LOG_WARN << "parameter error";
            return;
        }
    }

    // 假邮箱标识
    pstUserCreateInfo->is_fake = FindFakeEmail(pstUserCreateInfo->szEmail);

    //查询区域管理员试用时长，更新到账号下
    //GetPersonalAccountInstance()->DaoUpdateFreeDays(pstUserCreateInfo->szUser);
    //给csmain发送通知,新建用户需要发送邮箱
    if (GetIPCControlInstance()->SendPerCreateUidMail(pstUserCreateInfo) != 0)
    {
        AK_LOG_WARN << "Send create uid mail to csmain failed";
        return;
    }
}

int CAKCSView::FindFakeEmail(std::string email)
{
    int is_fake = 0;
    //缓存邮箱,最多5个
    if (fake_emails_.size() >= FAKE_EMAIL_QUEUE_SIZE)
    {
        //前5封发送，第六封开始检查邮箱，连续五次比对为相似邮箱确认为假邮件
        int diff_count = 0;
        for (auto iter = fake_emails_.begin(); iter != fake_emails_.end(); iter++)
        {
            int ret = GetEmailDiff(email, *iter);
            if (ret > FAKE_EMAIL_BIT_DIFF)
            {
                diff_count++;
            }
        }

        if (0 == diff_count)
        {
            AK_LOG_INFO << "Find Fake email:" << email;
            is_fake = 1;
        }
        std::lock_guard<std::mutex> lock(email_lock_);
        fake_emails_.pop_front();
    }
    std::lock_guard<std::mutex> lock(email_lock_);
    fake_emails_.push_back(email);

    return is_fake;
}

//获取前后两个邮箱差异位数
int CAKCSView::GetEmailDiff(std::string email_before, std::string email_after)
{
    int diff = 0;
    int min_size = 0;
    int length_diff = 0;

    if (email_before.size() > email_after.size())
    {
        min_size = email_after.size();
        length_diff = email_before.size() - email_after.size();
    }
    else if (email_before.size() < email_after.size())
    {
        min_size = email_before.size();
        length_diff = email_after.size() - email_before.size();
    }

    for (int i = 0; i < min_size; i++)
    {
        if (email_before[i] != email_after[i])
        {
            diff ++;
        }
    }

    diff += length_diff;
    return diff;
}

//终端用户主动修改密码
void CAKCSView::OnChangePwd(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnChangePwd The param is NULL";
        return;
    }

    CSP2A_USER_CREATE_INFO stUserCreateInfo;
    CSP2A_USER_CREATE_INFO* pstUserCreateInfo = &stUserCreateInfo;
    memset(pstUserCreateInfo, 0, sizeof(CSP2A_USER_CREATE_INFO));
    AK::Adapt::PerChangePwd msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstUserCreateInfo->szEmail, sizeof(pstUserCreateInfo->szEmail), msg.email().c_str());
    Snprintf(pstUserCreateInfo->szPwd, sizeof(pstUserCreateInfo->szPwd), msg.pwd().c_str());
    Snprintf(pstUserCreateInfo->szQRCodeBody, sizeof(pstUserCreateInfo->szQRCodeBody), msg.qrcode_body().c_str());
    Snprintf(pstUserCreateInfo->szQRCodeUrl, sizeof(pstUserCreateInfo->szQRCodeUrl), msg.qrcode_url().c_str());
    Snprintf(pstUserCreateInfo->szUser, sizeof(pstUserCreateInfo->szUser), msg.user().c_str());

    AK_LOG_INFO << "Request personal change pwd, email:" << pstUserCreateInfo->szEmail << " user " << pstUserCreateInfo->szUser;
    if (strlen(pstUserCreateInfo->szUser) <= 0 || strlen(pstUserCreateInfo->szQRCodeUrl) <= 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }

    if (strlen(pstUserCreateInfo->szEmail) <= 0)
    {
        ResidentPerAccount account;
        memset(&account, 0, sizeof(account));
        if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(pstUserCreateInfo->szUser, account))
        {
            if (account.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT || account.role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT)
            {
                ResidentPerAccount main_account;
                memset(&main_account, 0, sizeof(main_account));
                if (0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(account.parent_uuid, main_account)
                    && (strlen(main_account.getEmail()) > 0))
                {
                    Snprintf(pstUserCreateInfo->user_uid, sizeof(pstUserCreateInfo->szUser), msg.user().c_str());
                    Snprintf(pstUserCreateInfo->szEmail, sizeof(pstUserCreateInfo->szEmail), main_account.getEmail());
                    Snprintf(pstUserCreateInfo->szUser, sizeof(pstUserCreateInfo->szUser), main_account.account);
                    GetIPCControlInstance()->SendPerChangePwdMailToMaster(pstUserCreateInfo);
                }
                else
                {
                    AK_LOG_WARN << "parameter error";
                }
                return;
            }
        }
    }

    //给csmain发送通知,用户修改密码需要发送邮箱
    if (GetIPCControlInstance()->SendPerChangePwdMail(pstUserCreateInfo) != 0)
    {
        AK_LOG_WARN << "Send change uid mail to csmain failed";
        return;
    }
}

//终端用户管理员发送文本消息给联动系统
void CAKCSView::OnSendMessage(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnSendMessage The param is NULL";
        return;
    }
    
    AK::Adapt::PerNewMessage notify;
    int account_id;
    CHECK_PB_PARSE_MSG(notify.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    account_id = ATOI(notify.data().c_str());

    AK_LOG_INFO << "Request personal per message.";
    //给csmain发送通知,有最新的文本消息需要发送给联动系统
    if (GetIPCControlInstance()->SendPerMessage() != 0)
    {
        AK_LOG_WARN << "SendPerMessage failed";
        return;
    }

}

//同一联动系统中的用户在界面上或者app上处理告警的时候通知csmain去通知设备告警处理的结果
void CAKCSView::OnPersonalAlarmDeal(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnPersonalAlarmDeal The param is NULL";
        return;
    }

    AK::Adapt::PersonalAlarmDeal alarm_msg;
    CHECK_PB_PARSE_MSG(alarm_msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));

    AK::BackendCommon::BackendP2PBaseMessage base = BackendP2PMsgControl::CreateP2PBaseMsg(
        MSG_C2S_NOTIFY_PERSONAL_ALARM_DEAL,
        TransP2PMsgType::TO_APP_UID,
        alarm_msg.user(),
        BackendP2PMsgControl::DevProjectTypeToDevType(project::PERSONAL),
        project::PERSONAL
    );

    AK::Server::P2PAlarmDealNotifyMsg p2p_msg;
    p2p_msg.set_area_node(alarm_msg.area_node());
    p2p_msg.set_user(alarm_msg.user());
    p2p_msg.set_alarm_id(alarm_msg.alarm_id());
    p2p_msg.set_result(alarm_msg.result());

    base.mutable_p2palarmdealnotifymsg2()->CopyFrom(p2p_msg);
    BackendP2PMsgControl::PushMsg2Route(&base, project::PERSONAL);
    return;
}

//同一联动系统中的用户在界面上或者app上处理告警的时候通知csmain去通知设备告警处理的结果
void CAKCSView::OnCommunityAlarmDeal(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnCommunityAlarmDeal The param is NULL";
        return;
    }

    AK::Adapt::PersonalAlarmDeal alarm_msg;
    CHECK_PB_PARSE_MSG(alarm_msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));

    AK::BackendCommon::BackendP2PBaseMessage base = BackendP2PMsgControl::CreateP2PBaseMsg(
        MSG_C2S_NOTIFY_COMMUNITY_ALARM_DEAL,
        TransP2PMsgType::TO_APP_UID,
        alarm_msg.user(),
        BackendP2PMsgControl::DevProjectTypeToDevType(project::RESIDENCE),
        project::RESIDENCE
    );

    AK::Server::P2PAlarmDealNotifyMsg p2p_msg;
    p2p_msg.set_area_node(alarm_msg.area_node());
    p2p_msg.set_user(alarm_msg.user());
    p2p_msg.set_alarm_id(alarm_msg.alarm_id());
    p2p_msg.set_result(alarm_msg.result());

    base.mutable_p2palarmdealnotifymsg2()->CopyFrom(p2p_msg);
    BackendP2PMsgControl::PushMsg2Route(&base, project::RESIDENCE);
    return;
}

void CAKCSView::NotifyDelCommunityPics(uint32_t mng_id)
{
    AK::Server::GroupAdaptConfFileChangeMsg msg;
    msg.set_mng_id(mng_id);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_P2A_NOTIFY_COMMUNITY_MESSAGE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQTopicForDelPic);
}

void CAKCSView::NotifyDelPersonalPics(const std::string& pic_url)
{
    AK::Adapt::PerDelPic msg;
    msg.set_pic_url(pic_url);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_P2A_PERSONNAL_DEL_PIC);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQTopicForDelPic);
}

void CAKCSView::NotifyPerDelDevPics(const std::string& mac)
{
    AK::Adapt::PerDelDev msg;
    msg.set_mac(mac); 

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_P2A_PERSONNAL_DEL_DEV);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQTopicForDelPic);
}


//个人终端用户删除图片
void CAKCSView::OnPersonalDelPic(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnPersonalDelPic The param is NULL";
        return;
    }

    CSP2A_PER_DEL_PIC stPicConf;
    CSP2A_PER_DEL_PIC* pstPicConf = &stPicConf;
    memset(pstPicConf, 0, sizeof(CSP2A_PER_DEL_PIC));
    AK::Adapt::PerDelPic msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstPicConf->szPicUrl, sizeof(pstPicConf->szPicUrl), msg.pic_url().c_str());

    AK_LOG_INFO << "Request del pic " << pstPicConf->szPicUrl;
    NotifyDelPersonalPics(msg.pic_url());
}


//app即将过期,由定时脚本触发,单个调用
void CAKCSView::OnAppWillBeExpire(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnAppWillBeExpire The param is NULL";
        return;
    }

    CSP2A_DEV_APP_WILLBE_EXPIRE stUidInfo;
    CSP2A_DEV_APP_WILLBE_EXPIRE* pstUidInfo = &stUidInfo;
    memset(pstUidInfo, 0, sizeof(CSP2A_DEV_APP_WILLBE_EXPIRE));
    AK::Crontab::AppWillExpire msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstUidInfo->szEmail, sizeof(pstUidInfo->szEmail), msg.email().c_str());
    Snprintf(pstUidInfo->szUid, sizeof(pstUidInfo->szUid), msg.uid().c_str());
    Snprintf(pstUidInfo->szUserName, sizeof(pstUidInfo->szUserName), msg.user_name().c_str());
    Snprintf(pstUidInfo->community, sizeof(pstUidInfo->community), msg.community().c_str());

    AK_LOG_INFO << "Request app will be expire. uid:" << pstUidInfo->szUid << " email:" << pstUidInfo->szEmail;
    if (strlen(pstUidInfo->szUid) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }

    //家居通知
    LINKER_NORMAL_MSG linker_msg;
    SOCKET_MSG_LINKER_EXPIRE_MESSAGE expire_msg;
    memset(&linker_msg, 0, sizeof(linker_msg));
    memset(&expire_msg, 0, sizeof(expire_msg));
    ProjectInfo project(msg.uid(), linker_msg);
    expire_msg.type = LinkerExpireType::ACCOUNT_APP_EXPIRE;
    expire_msg.notify_type = LinkerExpireNotifyType::NOTIFY_WILL_EXPIRE;
    //定时脚本只检查3天过期的账户
    expire_msg.leave_days = 3;
    expire_msg.expire_timestamp = linker_msg.expire_timestamp;
    PushLinKerExpire(expire_msg, linker_msg);

    if(linker_msg.enable_smarthome)
    {
        //启用家居不需要邮件通知。原先在expire php脚本处理
        return;
    }
    if (strlen(pstUidInfo->szEmail) == 0)
    {
        AK_LOG_WARN << "OnAppWillBeExpire parameter error. email is null.";
    }
    //给csmain发送校验码通知
    if (GetIPCControlInstance()->SendDevAppWillBeExpire(pstUidInfo) != 0)
    {
        AK_LOG_WARN << "Send create uid mail to csmain failed";
        return;
    }

}

//app过期
void CAKCSView::OnAppExpire(void* pMsgBuf, unsigned int nMsgLen)
{
    char szExpireNode[1024] = "";
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnAppExpire The param is NULL";
        return;
    }

    CSP2A_DEV_APP_EXPIRE stDevAppExpire;
    CSP2A_DEV_APP_EXPIRE* pstDevAppExpire = &stDevAppExpire;
    memset(pstDevAppExpire, 0, sizeof(CSP2A_DEV_APP_EXPIRE));
    AK::Crontab::AppExpire msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstDevAppExpire->szEmail, sizeof(pstDevAppExpire->szEmail), msg.email().c_str());
    Snprintf(pstDevAppExpire->szUid, sizeof(pstDevAppExpire->szUid), msg.uid().c_str());
    Snprintf(pstDevAppExpire->szUserName, sizeof(pstDevAppExpire->szUserName), msg.user_name().c_str());
    Snprintf(pstDevAppExpire->community, sizeof(pstDevAppExpire->community), msg.community().c_str());

    AK_LOG_INFO << "Request app expire";
    if (strlen(pstDevAppExpire->szUid) == 0)
    {
        AK_LOG_WARN << "Request app expire parameter error";
        return;
    }

    //家居通知
    LINKER_NORMAL_MSG linker_msg;
    SOCKET_MSG_LINKER_EXPIRE_MESSAGE expire_msg;
    memset(&linker_msg, 0, sizeof(linker_msg));
    memset(&expire_msg, 0, sizeof(expire_msg));
    ProjectInfo project(msg.uid(), linker_msg);
    expire_msg.type = LinkerExpireType::ACCOUNT_APP_EXPIRE;
    expire_msg.notify_type = LinkerExpireNotifyType::NOTIFY_EXPIRE;
    expire_msg.leave_days = -1;
    expire_msg.expire_timestamp = linker_msg.expire_timestamp;
    PushLinKerExpire(expire_msg, linker_msg);

    if(linker_msg.enable_smarthome)
    {
        //启用家居不需要邮件通知。原先在expire php脚本处理
        return;
    }
    if (strlen(pstDevAppExpire->szEmail) == 0)
    {
        AK_LOG_WARN << "Request app expire parameter error. email is null.";
    }
    //modified by chenyc,2019-07-01,只有app会过
    GetIPCControlInstance()->SendAppExpire(pstDevAppExpire);
}

//个人社区删除虚拟主账号---或者删除主账号
void CAKCSView::OnPerPublicDevDelVirtAccount(void* pMsgBuf, unsigned int nMsgLen)
{
/*
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnPerPublicDevDelVirtAccount The param is NULL";
        return;
    }

    CSP2A_DEV_DEL_PER_PUBLIC_VIRT_ACCOUNT stVirtAccount;
    CSP2A_DEV_DEL_PER_PUBLIC_VIRT_ACCOUNT* pstVirtAccount = &stVirtAccount;
    AK::Adapt::NotityDelPerPublicDevVirtuaAccount msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstVirtAccount->szAccount, sizeof(pstVirtAccount->szAccount), msg.account().c_str());

    std::vector<PER_NODE_ACCOUNT> oNodes;
    std::string stAccount = pstVirtAccount->szAccount;//个人管理员账号
    AK_LOG_INFO << "Request del per public virtual account. mng account:" << pstVirtAccount->szAccount;
    if (strlen(pstVirtAccount->szAccount) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    GetPersonalAccountInstance()->DaoGetNodeListByMngName(stAccount, oNodes);

    for (auto& Node : oNodes)
    {
        if (Node.nRole == csmain::PER_PUBLIC_VIRT_USER_ROLE)
        {
            UpdatePerPublicDevContactlist(Node.szNode);
        }
        else
        {
            UpdateNodeContactlist(Node.szNode);
        }
        NotifyNodeContactlistChange(Node.szNode);
    }
*/
}

//用户实时增加视频存储计划
void CAKCSView::OnAddVideoStorageSched(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnAddVideoStorageSched The param is NULL";
        return;
    }

    CSP2A_ADD_VIDEO_STORAGE_SCHED stVsSchedulInfo;
    CSP2A_ADD_VIDEO_STORAGE_SCHED* pstVsSchedulInfo = &stVsSchedulInfo;
    memset(pstVsSchedulInfo, 0, sizeof(CSP2A_ADD_VIDEO_STORAGE_SCHED));
    AK::Adapt::NotityAddVideoSched msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstVsSchedulInfo->begin_time, sizeof(pstVsSchedulInfo->begin_time), msg.begin_time().c_str());
    Snprintf(pstVsSchedulInfo->end_time, sizeof(pstVsSchedulInfo->end_time), msg.end_time().c_str());
    Snprintf(pstVsSchedulInfo->mac, sizeof(pstVsSchedulInfo->mac), msg.mac().c_str());
    pstVsSchedulInfo->id = msg.id();
    pstVsSchedulInfo->sched_type = msg.sched_type();
    pstVsSchedulInfo->date_flag = msg.date_flag();

    AK_LOG_INFO << "Request add video storage schedule, time is ." << pstVsSchedulInfo->begin_time << " type is ." << pstVsSchedulInfo->sched_type;
    if (strlen(pstVsSchedulInfo->mac) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    //给csmain发送通知
    if (GetIPCControlInstance()->SendAddVsSched(pstVsSchedulInfo) != 0)
    {
        AK_LOG_WARN << "SendAddVsSched failed";
        return;
    }
}

void CAKCSView::OnDelVideoStorageSched(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnDelVideoStorageSched The param is NULL";
        return;
    }

    CSP2A_DEL_VIDEO_STORAGE_SCHED stVsSchedulInfo;
    CSP2A_DEL_VIDEO_STORAGE_SCHED* pstVsSchedulInfo = &stVsSchedulInfo;
    memset(pstVsSchedulInfo, 0, sizeof(CSP2A_DEL_VIDEO_STORAGE_SCHED));
    AK::Adapt::NotityDelVideoSched msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    pstVsSchedulInfo->id = msg.id();
    pstVsSchedulInfo->sched_type = msg.sched_type();
    Snprintf(pstVsSchedulInfo->mac, sizeof(pstVsSchedulInfo->mac), msg.mac().c_str());

    AK_LOG_INFO << "Request delete video storage schedule. is is " << pstVsSchedulInfo->id << " sched_type is " << pstVsSchedulInfo->sched_type;
    if (strlen(pstVsSchedulInfo->mac) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    //给csmain发送通知
    if (GetIPCControlInstance()->SendDelVsSched(pstVsSchedulInfo) != 0)
    {
        AK_LOG_WARN << "SendDelVsSched failed";
        return;
    }
}

void CAKCSView::OnDelVideoStorage(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnDelVideoStorage The param is NULL";
        return;
    }

    CSP2A_DEL_VIDEO_STORAGE stVsStorage;
    CSP2A_DEL_VIDEO_STORAGE* pstVsStorage = &stVsStorage;
    memset(pstVsStorage, 0, sizeof(CSP2A_DEL_VIDEO_STORAGE));
    AK::Adapt::NotityDelVideo msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    pstVsStorage->video_id = msg.video_id();

    AK_LOG_INFO << "Request delete video storage schedule,video_id is " << pstVsStorage->video_id;
    if (pstVsStorage->video_id == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    //给csmain发送通知
    if (GetIPCControlInstance()->SendDelVs(pstVsStorage) != 0)
    {
        AK_LOG_WARN << "SendDelVs failed";
        return;
    }
}

void CAKCSView::OnShareTempKeyEmail(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnShareTempKeyEmail The param is NULL";
        return;
    }

    CSP2A_SHARE_TEMKEY_INFO stShareTempkey;
    CSP2A_SHARE_TEMKEY_INFO* pstShareTempkey = &stShareTempkey;
    memset(pstShareTempkey, 0, sizeof(CSP2A_SHARE_TEMKEY_INFO));
    AK::Adapt::ShareTempKeyNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstShareTempkey->szCountOrEvery, sizeof(pstShareTempkey->szCountOrEvery), msg.count_every().c_str());
    Snprintf(pstShareTempkey->szEmail, sizeof(pstShareTempkey->szEmail), msg.email().c_str());
    Snprintf(pstShareTempkey->szStartTime, sizeof(pstShareTempkey->szStartTime), msg.start_time().c_str());

    Snprintf(pstShareTempkey->szStopTime, sizeof(pstShareTempkey->szStopTime), msg.stop_time().c_str());
    Snprintf(pstShareTempkey->szMsg, sizeof(pstShareTempkey->szMsg), msg.msg().c_str());
    Snprintf(pstShareTempkey->szTmpKey, sizeof(pstShareTempkey->szTmpKey), msg.tmp_key().c_str());
    Snprintf(pstShareTempkey->szQRCodeBody, sizeof(pstShareTempkey->szQRCodeBody), msg.qrcode_body().c_str());
    Snprintf(pstShareTempkey->szLanguage, sizeof(pstShareTempkey->szLanguage), msg.language().c_str());
    if (strlen(gstCSADAPTConf.web_domain) > 1)
    {
        ::snprintf(pstShareTempkey->szWebUrl, sizeof(pstShareTempkey->szWebUrl), "https://%s", gstCSADAPTConf.web_domain);
    }
    else
    {
        ::snprintf(pstShareTempkey->szWebUrl, sizeof(pstShareTempkey->szWebUrl), "http://%s", gstCSADAPTConf.szCsadaptOuterIP);
    }
    pstShareTempkey->mng_id = msg.mng_id();

    AK_LOG_INFO << "Request share tmpkey to email: " << pstShareTempkey->szEmail;
    if (strlen(pstShareTempkey->szEmail) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    //给csmain发送通知
    if (GetIPCControlInstance()->SendShareTmpkeyEmail(pstShareTempkey) != 0)
    {
        AK_LOG_WARN << "SendShareTmpkeyEmail failed";
        return;
    }
}

void CAKCSView::OnCreatePropertyWorkEmail(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnCreatePropertyWorkEmail The param is NULL";
        return;
    }

    CSP2A_CREATE_PROPERTY_WORK_INFO stCreateProperty;
    CSP2A_CREATE_PROPERTY_WORK_INFO* pstCreateProperty = &stCreateProperty;
    memset(pstCreateProperty, 0, sizeof(CSP2A_CREATE_PROPERTY_WORK_INFO));
    AK::Adapt::CreatePropertyWorkNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstCreateProperty->szEmail, sizeof(pstCreateProperty->szEmail), msg.email().c_str());
    Snprintf(pstCreateProperty->szPassword, sizeof(pstCreateProperty->szPassword), msg.password().c_str());
    Snprintf(pstCreateProperty->szUserName, sizeof(pstCreateProperty->szUserName), msg.user_name().c_str());

    if (strlen(gstCSADAPTConf.web_domain) > 1)
    {
        ::snprintf(pstCreateProperty->szServerWebUrl, sizeof(pstCreateProperty->szServerWebUrl), "http://%s/manage", gstCSADAPTConf.web_domain);
    }
    else
    {
        ::snprintf(pstCreateProperty->szServerWebUrl, sizeof(pstCreateProperty->szServerWebUrl), "http://%s/manage", gstCSADAPTConf.szCsadaptOuterIP);
    }

    AK_LOG_INFO << "Request create property work user: " << pstCreateProperty->szUserName << " email:" << pstCreateProperty->szEmail;
    if (strlen(pstCreateProperty->szEmail) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    //给csmain发送通知
    if (GetIPCControlInstance()->SendCreatePropertyWork(pstCreateProperty) != 0)
    {
        AK_LOG_WARN << "SendCreatePropertyWork failed";
        return;
    }
}

void CAKCSView::OnAccountActiveEmail(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnAccountActiveEmail The param is NULL";
        return;
    }

    CSP2A_ACCOUNT_ACTIVE_INFO stAccountActive;
    CSP2A_ACCOUNT_ACTIVE_INFO* pstAccountActive = &stAccountActive;
    memset(pstAccountActive, 0, sizeof(CSP2A_ACCOUNT_ACTIVE_INFO));
    AK::Adapt::AccountActiveNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    pstAccountActive->nActive = msg.active();
    pstAccountActive->subscription = msg.subscription();
    Snprintf(pstAccountActive->szEmail, sizeof(pstAccountActive->szEmail), msg.email().c_str());
    Snprintf(pstAccountActive->szUserName, sizeof(pstAccountActive->szUserName), msg.user_name().c_str());

    if (CheckEnableSmarthomeByEmail(pstAccountActive->szEmail))
    {
        AK_LOG_INFO << "No need send enable smarthome Account Active Email, user: " << pstAccountActive->szUserName << " email:" << pstAccountActive->szEmail;
        return;        
    }

    //6.3办公不发激活邮件
    OfficeAccount account;
    if (dbinterface::OfficePersonalAccount::GetEmailAccount(pstAccountActive->szEmail, account) == 0 && IsOfficeRole(account.role))
    {
        AK_LOG_INFO << "No need send Office Account Active Email, user: " << pstAccountActive->szUserName << " email:" << pstAccountActive->szEmail;
        return;
    }

    ResidentPerAccount personal_account;
    memset(&personal_account, 0, sizeof(personal_account));
    if (0 != dbinterface::ResidentPersonalAccount::GetEmailAccount(pstAccountActive->szEmail, personal_account))
    {
        AK_LOG_WARN << "GetEmailAccount Failed,user=" << pstAccountActive->szUserName << " email:" << pstAccountActive->szEmail;
        return;
    }
    
    std::string expire_time;
    dbinterface::ResidentPersonalAccount::GetExpireTime(expire_time, personal_account);
    Snprintf(pstAccountActive->expire_time, sizeof(pstAccountActive->expire_time), expire_time.c_str());

    if (::strlen(gstCSADAPTConf.web_domain) > 1)
    {
        ::snprintf(pstAccountActive->szServerWebUrl, sizeof(pstAccountActive->szServerWebUrl), "http://%s", gstCSADAPTConf.web_domain);
    }
    else
    {
        ::snprintf(pstAccountActive->szServerWebUrl, sizeof(pstAccountActive->szServerWebUrl), "http://%s", gstCSADAPTConf.szCsadaptOuterIP);
    }

    AK_LOG_INFO << "Request Account Active Email, user: " << pstAccountActive->szUserName << " email:" << pstAccountActive->szEmail;
    if (strlen(pstAccountActive->szEmail) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    //给csmain发送通知
    if (GetIPCControlInstance()->SendAccountActiveEmail(pstAccountActive) != 0)
    {
        AK_LOG_WARN << "SendAccountActiveEmail failed";
        return;
    }
}

void RouteLockMessage()
{
    {
        UnLockControl lock_control( "lock.dv-4955be9d920011efbccc00163e18451b","dv-4955be9d920011efbccc00163e18451b");
 
        AK::Route::P2PRoutLockMessage p2p_route_lock_message;
        p2p_route_lock_message.set_akcs_command(LockControl::AKCS_COMMAND);
        p2p_route_lock_message.set_client_id("dv-4955be9d920011efbccc00163e18451b");
        p2p_route_lock_message.set_json_message(lock_control.to_json());
        p2p_route_lock_message.set_trace_id("tracID_dv-4955be9d920011efbccc00163e18451b");
        
        AK_LOG_INFO << "Send RouteLockMessage msg:" << p2p_route_lock_message.DebugString();
    
        CAkcsPdu pdu;
        pdu.SetMsgBody(&p2p_route_lock_message);
        pdu.SetHeadLen(sizeof(PduHeader_t));
        pdu.SetVersion(50);
        pdu.SetCommandId(AKCS_R2S_P2P_SMARTLOCK_MSG);
        pdu.SetSeqNum(0);
        g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    }
    {
        LockUpdateConfig lock_control;
 
        lock_control.language_ = "en";
        lock_control.entity_id_ = "lock.dv-4955be9d920011efbccc00163e18451b";
        lock_control.volume_ = "high";
        AK::Route::P2PRoutLockMessage p2p_route_lock_message;
        p2p_route_lock_message.set_akcs_command(LockControl::AKCS_COMMAND);
        p2p_route_lock_message.set_client_id("dv-4955be9d920011efbccc00163e18451b");
        p2p_route_lock_message.set_json_message(lock_control.to_json());
        p2p_route_lock_message.set_trace_id("tracID_dv-4955be9d920011efbccc00163e18451b");
        
        AK_LOG_INFO << "Send RouteLockMessage msg:" << p2p_route_lock_message.DebugString();
    
        CAkcsPdu pdu;
        pdu.SetMsgBody(&p2p_route_lock_message);
        pdu.SetHeadLen(sizeof(PduHeader_t));
        pdu.SetVersion(50);
        pdu.SetCommandId(AKCS_R2S_P2P_SMARTLOCK_MSG);
        pdu.SetSeqNum(0);
        g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    }

}


void CAKCSView::OnRemoteOpenDoor(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnRemoteOpenDoor The param is NULL";
        return;
    }

    RouteLockMessage();

    
    AK::Adapt::OpenDoorNotify msg;
    uint32_t project_type = NTOHL(*((unsigned int*)((unsigned char*)msg_buf + 8)));
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    AK_LOG_INFO << "OnRemoteOpenDoor:msg=" << msg.DebugString();

    if (msg.mac().empty())
    {
        AK_LOG_WARN << "parameter error: mac is empty";
        return;
    }

    std::string repost_mac;
    // 开门需要通过室内机转发, 查找apt内可用的室内机用于转发
    if (msg.repost() && 0 != dbinterface::ResidentDevices::GetRepostDev(msg.uid(), repost_mac)
                     && 0 != dbinterface::ResidentPerDevices::GetRepostDev(msg.uid(), repost_mac))
    {
        AK_LOG_WARN << "there is no indoor to repost,uid = " << msg.uid() << ", mac = " << msg.mac();
        return;
    }

    std::string traceid = msg.msg_traceid();
    if (traceid.size() <= 0)
    {
        traceid = std::to_string(AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId());
    }

    AK_LOG_INFO << "OnRemoteOpenDoor: project_type=" << project_type << ",uid=" << msg.uid() 
                << ",mac=" << msg.mac()  << ",relay=" << msg.relay() << ",repost_mac=" << repost_mac;

    AK::Server::P2POpenDoorNotifyMsg post_msg;
    post_msg.set_uid(msg.uid());
    post_msg.set_mac(msg.mac());
    post_msg.set_relay(msg.relay());
    post_msg.set_relay_type((int)OpenDoorRelayType::RELAY);
    post_msg.set_project_type(project_type);
    post_msg.set_repost_mac(repost_mac);
    post_msg.set_msg_traceid(traceid);

    AK::BackendCommon::BackendP2PBaseMessage base_msg = BackendP2PMsgControl::CreateP2PBaseMsg(
        AKCS_M2R_P2P_REMOTE_OPENDOOR_MSG,
        TransP2PMsgType::TO_DEV_MAC,
        repost_mac.empty() ? msg.mac() : repost_mac,
        BackendP2PMsgControl::DevProjectTypeToDevType(project_type),
        project_type
    );

    base_msg.mutable_p2popendoornotifymsg2()->CopyFrom(post_msg);
    BackendP2PMsgControl::PushMsg2Route(&base_msg, project_type);
}

void CAKCSView::OnRemoteOpenSecurityRelay(void *msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnRemoteOpenSecurityRelay The param is NULL";
        return;
    }

    AK::Adapt::OpenSecurityRelayNotify msg;
    uint32_t project_type = NTOHL(*((unsigned int*)((unsigned char*)msg_buf + 8)));
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    if (msg.mac().empty())
    {
        AK_LOG_WARN << "parameter error: mac is empty";
        return;
    }

    std::string repost_mac;
    // 开门需要通过室内机转发, 查找apt内可用的室内机用于转发
    if (msg.repost() && 0 != dbinterface::ResidentDevices::GetRepostDev(msg.uid(), repost_mac)
                     && 0 != dbinterface::ResidentPerDevices::GetRepostDev(msg.uid(), repost_mac))
    {
        AK_LOG_WARN << "there is no indoor to repost,uid = " << msg.uid() << ", mac = " << msg.mac();
        return;
    }

    std::string traceid = msg.msg_traceid();
    if (traceid.size() <= 0)
    {
        traceid = std::to_string(AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId());
    }

    AK_LOG_INFO << "OnRemoteOpenSecurityRelay: project_type=" << project_type << ",uid=" << msg.uid() 
                << ",mac=" << msg.mac()  << ",security_relay=" << msg.security_relay() << ",repost_mac=" << repost_mac;

    AK::Server::P2POpenDoorNotifyMsg post_msg;
    post_msg.set_uid(msg.uid());
    post_msg.set_mac(msg.mac());
    post_msg.set_relay(msg.security_relay());
    post_msg.set_relay_type((int)OpenDoorRelayType::SECURITY_RELAY);
    post_msg.set_project_type(project_type);
    post_msg.set_repost_mac(repost_mac);
    post_msg.set_msg_traceid(traceid);

    AK::BackendCommon::BackendP2PBaseMessage base_msg = BackendP2PMsgControl::CreateP2PBaseMsg(
        AKCS_M2R_P2P_REMOTE_OPENDOOR_MSG,
        TransP2PMsgType::TO_DEV_MAC,
        repost_mac.empty() ? msg.mac() : repost_mac,
        BackendP2PMsgControl::DevProjectTypeToDevType(project_type),
        project_type
    );

    base_msg.mutable_p2popendoornotifymsg2()->CopyFrom(post_msg);
    BackendP2PMsgControl::PushMsg2Route(&base_msg, project_type);
}

project::PROJECT_TYPE TransAccountGradeToProjectType(int grade)
{
    if (grade == AccountGrade::COMMUNITY_MANEGER_GRADE)
    {
        return project::PROJECT_TYPE::RESIDENCE;
    }
    else if (grade == AccountGrade::OFFICE_MANEGER_GRADE)
    {
        return project::PROJECT_TYPE::OFFICE;
    }
    else if (grade == AccountGrade::PERSONAL_MANEGER_GRADE)
    {
        return project::PROJECT_TYPE::PERSONAL;
    }
    return project::PROJECT_TYPE::NONE;
}

void CAKCSView::OnPmEmergencyDoorControl(void *msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnPmEmergencyDoorControl The param is NULL";
        return;
    }

    AK::Adapt::PmEmergencyDoorControlNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    
    EmergencyDoorControlInfo emergency_info;

    Snprintf(emergency_info.uuid, sizeof(emergency_info.uuid), msg.uuid().c_str());
    emergency_info.control_type = msg.type();

    if (strlen(emergency_info.uuid) == 0)
    {
        AK_LOG_WARN << "parameter error,uuid is empty";
        return;
    }

    AK_LOG_INFO << "OnPmEmergencyDoorControl uuid=" <<  emergency_info.uuid  << " ,type=" << emergency_info.control_type;

    //获取项目信息
    std::string project_uuid; 
    if (0 != dbinterface::PmEmergencyDoorLog::GetProjectUUIDByUUID(emergency_info.uuid, project_uuid))
    {
        AK_LOG_WARN << "get project uuid failed. emergencydoorlog uuid=" << emergency_info.uuid;
        return;
    }

    dbinterface::AccountInfo project_info;
    if (0 != dbinterface::Account::GetAccountByUUID(project_uuid, project_info))
    {
        AK_LOG_WARN << "get project info failed. project uuid=" << project_uuid;
        return;
    }

    EmergencyNotifyHandle(project_info, emergency_info);
    EmergencyDoorControlHandle(project_info, emergency_info);

    return;
}

bool CAKCSView::CheckEmergencyNeedNotify(int project_id, int project_type)
{
    if (project_type == project::PROJECT_TYPE::RESIDENCE)
    {
        CommunityInfo comm_info(project_id);
        if (comm_info.InitSuccess() && comm_info.EmergencyNeedNotify())
        {
            return true;
        }
    }
    return false;
}

int CAKCSView::EmergencyNotifyHandle(const dbinterface::AccountInfo& project_info, const EmergencyDoorControlInfo& emergency_info)
{
    project::PROJECT_TYPE project_type = TransAccountGradeToProjectType(project_info.grade);
    if (CheckEmergencyNeedNotify(project_info.id, project_type))
    {
        SendEmergencyNotify(project_info, emergency_info.control_type, project_type);
        InsertEmegencyNotifyAlarmLog(project_info, emergency_info.control_type);
    }
    else
    {
        AK_LOG_INFO << "No Need to Send Emergency Notify, project id=" << project_info.id;
    }
    return 0;
}

void CAKCSView::SendEmergencyNotify(const dbinterface::AccountInfo& project_info, int control_type, int project_type)
{
    AK::Server::P2PSendEmergencyNotifyMsg p2p_msg;
    p2p_msg.set_control_type(control_type);
    AK::BackendCommon::BackendP2PBaseMessage base;

    AK_LOG_INFO << "SendEmergencyNotify, control_type=" << control_type << " project_uuid=" << project_info.uuid;

    //获取要发送的App账号列表
    std::set<std::string> app_list;
    if (project_type == project::PROJECT_TYPE::RESIDENCE)
    {
        GetCommunityAllAppList(project_info.uuid, app_list);
    }

    //App相关P2P消息发送
    for(const auto& account : app_list)
    {
        p2p_msg.set_receiver_uid(account);
        base = BackendP2PMsgControl::CreateP2PBaseMsg(AKCS_M2R_P2P_SEND_EMERGENCY_NOTIFY_MSG, TransP2PMsgType::TO_APP_UID, account,
                                                    BackendP2PMsgControl::DevProjectTypeToDevType(project_type), project_type);
        base.mutable_p2psendemergencynotifymsg2()->CopyFrom(p2p_msg);
        BackendP2PMsgControl::PushMsg2Route(&base, project_type);
    }

    //获取要发送的室内机列表
    std::set<std::string> indoor_dev_list;
    if (project_type == project::PROJECT_TYPE::RESIDENCE)
    {
        if(0 != dbinterface::ResidentDevices::GetAllAptIndoorDevices(project_info.id, indoor_dev_list))
        {
            return;
        }
    }

    //室内机相关P2P消息发送
    for(const auto& mac : indoor_dev_list)
    {
        p2p_msg.set_receiver_uid(mac);
        base = BackendP2PMsgControl::CreateP2PBaseMsg(AKCS_M2R_P2P_SEND_EMERGENCY_NOTIFY_MSG, TransP2PMsgType::TO_DEV_MAC, mac,
                                                            BackendP2PMsgControl::DevProjectTypeToDevType(project_type), project_type);
        base.mutable_p2psendemergencynotifymsg2()->CopyFrom(p2p_msg);
        BackendP2PMsgControl::PushMsg2Route(&base, project_type);
    }

    return;
}

void CAKCSView::GetCommunityAllAppList(const std::string& project_uuid, std::set<std::string>& app_list)
{
    //获取社区下主账号列表
    std::set<std::string> node_list;
    if (0 != dbinterface::ResidentPersonalAccount::GetNodeListByProjectUUID(project_uuid, node_list))
    {
        AK_LOG_WARN << "Get node list failed. project_uuid=" << project_uuid;
        return;
    }
    
    for (const auto& node : node_list)
    {
        //获取社区下账号列表
        if (0 != dbinterface::ResidentPersonalAccount::GetAttendantListByUid(node, app_list))
        {
            AK_LOG_WARN << "Get room app list failed. node=" << node;
        }
    }
    return;
}

int CAKCSView::EmergencyDoorControlHandle(const dbinterface::AccountInfo& project_info, const EmergencyDoorControlInfo& emergency_info)
{
    project::PROJECT_TYPE project_type = TransAccountGradeToProjectType(project_info.grade);

    //办公走旧方法
    if (project_type == project::PROJECT_TYPE::OFFICE)
    {
        AK::Adapt::PmEmergencyDoorControlNotify msg;
        msg.set_type(emergency_info.control_type);
        msg.set_uuid(emergency_info.uuid);

        CAkcsPdu pdu;
        pdu.SetMsgBody(&msg);
        pdu.SetHeadLen(sizeof(PduHeader_t));
        pdu.SetVersion(50);
        pdu.SetCommandId(MSG_C2S_PM_EMERGENCY_DOOR_CONTROL);
        pdu.SetSeqNum(0);
        g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
        return 0;
    }

    ACT_OPEN_DOOR_TYPE act_type;
    std::string pm_uuid;
    std::string initiator;
    dbinterface::PmEmergencyDoorLogInfoList info_list;
    dbinterface::PmEmergencyDoorLog::GetPmEmergencyDoorLogByUUID(emergency_info.uuid, pm_uuid, info_list);
    dbinterface::PropertyInfo::GetFullNameByUUID(pm_uuid, initiator);

    if(info_list.empty())
    {
        AK_LOG_INFO << "PmEmergencyDoorControl UUID =" << emergency_info.uuid << " PmEmergencyDoorLogList is null";
        return 0;
    }
    
	if(emergency_info.control_type == PmEmergencyDoorControlType::CONTROL_TYPE_OPEN)
    {
        act_type = ACT_OPEN_DOOR_TYPE::PM_UNLOCK;
    }
    else if(emergency_info.control_type == PmEmergencyDoorControlType::CONTROL_TYPE_CLOSE)
    {
        act_type = ACT_OPEN_DOOR_TYPE::PM_LOCK;
    }
    
    AK::BackendCommon::BackendP2PBaseMessage base_msg;

    for(auto const& info : info_list)
    {	
        ResidentDev dev;
        if (0 != dbinterface::ResidentDevices::GetUUIDDev(info.device_uuid, dev))
        {
            AK_LOG_WARN << "Get device Info failed. device uuid=" << info.device_uuid;
            continue;
        }
        //离线记录异常doorlog
        if (dev.status == DeviceStatus::DEVICE_STATUS_OFFLINE)
        {
            AK_LOG_WARN << "device is offline ,device_uuid =" << info.device_uuid;
            dbinterface::PmEmergencyDoorLog::UpdateDeviceAbnormalStatus(emergency_info.uuid, info.device_uuid, dbinterface::RelayStatus::OFFLINE);
            dbinterface::PersonalCapture::RecordEmergencyContorlDoorLog(info.device_uuid, initiator, act_type, dbinterface::RelayStatus::OFFLINE, gstAKCSLogDelivery.personal_capture_delivery);
            continue;
        }

        base_msg = BackendP2PMsgControl::CreateP2PBaseMsg(AKCS_M2R_EMERGENCY_DOOR_CONTROL, TransP2PMsgType::TO_DEV_UUID, info.device_uuid,
                                                            BackendP2PMsgControl::DevProjectTypeToDevType(project_type), project_type);

        //设备在线消息推送
        AK::Server::P2PPmEmergencyDoorControlMsg control_msg;
        control_msg.set_msg_uuid(emergency_info.uuid);
        control_msg.set_device_uuid(info.device_uuid);
        control_msg.set_initiator(initiator);
        control_msg.set_auto_manual(OPERATE_TYPE::MANUAL);
        control_msg.set_operation_type(emergency_info.control_type);
        control_msg.set_relay(info.relay);
        control_msg.set_security_relay(info.security_relay);

        base_msg.mutable_p2ppmemergencydoorcontrolmsg2()->CopyFrom(control_msg);
        BackendP2PMsgControl::PushMsg2Route(&base_msg, project_type);
	}

    return 0;
}

//通知PM社区多少账号即将过期,单个通知
void CAKCSView::OnPMAccountWillExpire(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnPMAccountWillExpire The param is NULL";
        return;
    }

    CSP2A_PM_INFO stPmInfo;
    CSP2A_PM_INFO* pstPmInfo = &stPmInfo;
    memset(pstPmInfo, 0, sizeof(CSP2A_PM_INFO));
    AK::Crontab::PMAppWillBeExpire msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstPmInfo->szCommunity, sizeof(pstPmInfo->szCommunity), msg.community().c_str());
    Snprintf(pstPmInfo->szEmail, sizeof(pstPmInfo->szEmail), msg.email().c_str());
    Snprintf(pstPmInfo->szName, sizeof(pstPmInfo->szName), msg.name().c_str());
    Snprintf(pstPmInfo->list, sizeof(pstPmInfo->list), msg.list().c_str());
    pstPmInfo->nAccountNum = msg.account_num();
    pstPmInfo->nBefore = msg.before();

    AK_LOG_INFO << "Request notify pm account will expire Email, pm community: " << pstPmInfo->szCommunity;
    if (strlen(pstPmInfo->szEmail) == 0 || strlen(pstPmInfo->szCommunity) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    //给csroute
    GetIPCControlInstance()->SendPmEmail(pstPmInfo);

}

//alexa登陆
void CAKCSView::OnAlexaLogin(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnPMAccountWillExpire The param is NULL";
        return;
    }

    CSP2A_ALEXA_LOGIN_INFO alexa;
    AK::Adapt::AlexaLoginNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(alexa.node, sizeof(alexa.node), msg.node().c_str());
    AK_LOG_INFO << "Request notify alexa login node: " << msg.node();
    if (strlen(alexa.node) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }

    //给csroute
    GetIPCControlInstance()->SendAlexaLogin(&alexa);
}

//alexa设置arming
void CAKCSView::OnAlexaSetArming(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnPMAccountWillExpire The param is NULL";
        return;
    }
    CSP2A_ALEXA_SET_ARMING_INFO alexa;
    AK::Adapt::AlexaSetArmingNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(alexa.mac, sizeof(alexa.mac), msg.mac().c_str());
    alexa.mode =  msg.mode();
    AK_LOG_INFO << "Request  alexa set arming mac: " << msg.mac() << " mode:" << msg.mode();
    if (strlen(alexa.mac) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    //给csroute
    GetIPCControlInstance()->SendAlexaSetArming(&alexa);
}

void CAKCSView::OnPhoneExpire(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnPhoneExpire The param is NULL";
        return;
    }

    CSP2A_DEV_APP_EXPIRE stPhoneExpire;
    CSP2A_DEV_APP_EXPIRE* pstPhoneExpire = &stPhoneExpire;
    memset(pstPhoneExpire, 0, sizeof(CSP2A_DEV_APP_EXPIRE));
    AK::Crontab::PhoneExpire msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstPhoneExpire->szEmail, sizeof(pstPhoneExpire->szEmail), msg.email().c_str());
    Snprintf(pstPhoneExpire->szUid, sizeof(pstPhoneExpire->szUid), msg.uid().c_str());
    Snprintf(pstPhoneExpire->szUserName, sizeof(pstPhoneExpire->szUserName), msg.name().c_str());

    AK_LOG_INFO << "Request Phone expire, Account: " << pstPhoneExpire->szUid;
    if (strlen(pstPhoneExpire->szUid) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }

    //家居通知
    LINKER_NORMAL_MSG linker_msg;
    SOCKET_MSG_LINKER_EXPIRE_MESSAGE expire_msg;
    memset(&linker_msg, 0, sizeof(linker_msg));
    memset(&expire_msg, 0, sizeof(expire_msg));
    ProjectInfo project(msg.uid(), linker_msg);
    expire_msg.type = LinkerExpireType::ACCOUNT_FEATURE_EXPIRE;

    expire_msg.notify_type = LinkerExpireNotifyType::NOTIFY_EXPIRE;
    expire_msg.leave_days = -1;
    expire_msg.expire_timestamp = linker_msg.phone_expire_timestamp;
    PushLinKerExpire(expire_msg, linker_msg);

    if(linker_msg.enable_smarthome)
    {
        //启用家居不需要邮件通知。原先在expire php脚本处理
        return;
    }
    GetIPCControlInstance()->SendPhoneExpire(pstPhoneExpire);
}

void CAKCSView::OnPhoneWillExpire(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnPhoneWillExpire The param is NULL";
        return;
    }

    CSP2A_DEV_APP_EXPIRE stPhoneWillExpire;
    CSP2A_DEV_APP_EXPIRE* pstPhoneWillExpire = &stPhoneWillExpire;
    memset(pstPhoneWillExpire, 0, sizeof(CSP2A_DEV_APP_EXPIRE));
    AK::Crontab::PhoneWillExpire msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstPhoneWillExpire->szEmail, sizeof(pstPhoneWillExpire->szEmail), msg.email().c_str());
    Snprintf(pstPhoneWillExpire->szUid, sizeof(pstPhoneWillExpire->szUid), msg.uid().c_str());
    Snprintf(pstPhoneWillExpire->szUserName, sizeof(pstPhoneWillExpire->szUserName), msg.name().c_str());
    pstPhoneWillExpire->nBefore = msg.before();

    AK_LOG_INFO << "Request Phone will expire, Account: " << pstPhoneWillExpire->szUid;
    if (strlen(pstPhoneWillExpire->szUid) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }

    //家居通知
    LINKER_NORMAL_MSG linker_msg;
    SOCKET_MSG_LINKER_EXPIRE_MESSAGE expire_msg;
    memset(&linker_msg, 0, sizeof(linker_msg));
    memset(&expire_msg, 0, sizeof(expire_msg));
    ProjectInfo project(msg.uid(), linker_msg);
    expire_msg.type = LinkerExpireType::ACCOUNT_FEATURE_EXPIRE;

    expire_msg.notify_type = LinkerExpireNotifyType::NOTIFY_WILL_EXPIRE;
    expire_msg.leave_days = msg.before();
    expire_msg.expire_timestamp = linker_msg.phone_expire_timestamp;
    PushLinKerExpire(expire_msg, linker_msg);
    if(linker_msg.enable_smarthome)
    {
        //启用家居不需要邮件通知。原先在expire php脚本处理
        return;
    }

    GetIPCControlInstance()->SendPhoneWillExpire(pstPhoneWillExpire);
}

void CAKCSView::OnInstallerAppWillExpire(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnInstallerAppWillExpire The param is NULL";
        return;
    }

    CSP2A_INSTALLER_EXPIRE stInstallerExpire;
    CSP2A_INSTALLER_EXPIRE* pstInstallerExpire = &stInstallerExpire;
    memset(pstInstallerExpire, 0, sizeof(CSP2A_INSTALLER_EXPIRE));
    AK::Crontab::InstallerAppWillExpire msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstInstallerExpire->szEmail, sizeof(pstInstallerExpire->szEmail), msg.email().c_str());
    Snprintf(pstInstallerExpire->szUserName, sizeof(pstInstallerExpire->szUserName), msg.name().c_str());
    Snprintf(pstInstallerExpire->list, sizeof(pstInstallerExpire->list), msg.list().c_str());
    Snprintf(pstInstallerExpire->community, sizeof(pstInstallerExpire->community), msg.location().c_str());
    pstInstallerExpire->nCount = msg.count();
    pstInstallerExpire->nBefore = msg.before();

    AK_LOG_INFO << "Request Installer App will expire, Installer: " << pstInstallerExpire->szUserName;
    if (strlen(pstInstallerExpire->szEmail) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    GetIPCControlInstance()->SendInstallerAppWillExpire(pstInstallerExpire, msg.location());
}

void CAKCSView::OnInstallerPhoneWillExpire(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnInstallerPhoneWillExpire The param is NULL";
        return;
    }

    CSP2A_INSTALLER_EXPIRE stInstallerExpire;
    CSP2A_INSTALLER_EXPIRE* pstInstallerExpire = &stInstallerExpire;
    memset(pstInstallerExpire, 0, sizeof(CSP2A_INSTALLER_EXPIRE));
    AK::Crontab::InstallerPhoneWillExpire msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstInstallerExpire->szEmail, sizeof(pstInstallerExpire->szEmail), msg.email().c_str());
    Snprintf(pstInstallerExpire->szUserName, sizeof(pstInstallerExpire->szUserName), msg.name().c_str());
    Snprintf(pstInstallerExpire->list, sizeof(pstInstallerExpire->list), msg.list().c_str());
    pstInstallerExpire->nCount = msg.count();
    pstInstallerExpire->nBefore = msg.before();

    AK_LOG_INFO << "Request Installer phone will expire, Installer: " << pstInstallerExpire->szUserName;
    if (strlen(pstInstallerExpire->szEmail) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    GetIPCControlInstance()->SendInstallerPhoneWillExpire(pstInstallerExpire);
}

void CAKCSView::OnInstallerAppExpire(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnInstallerAppExpire The param is NULL";
        return;
    }

    CSP2A_INSTALLER_EXPIRE stInstallerExpire;
    CSP2A_INSTALLER_EXPIRE* pstInstallerExpire = &stInstallerExpire;
    memset(pstInstallerExpire, 0, sizeof(CSP2A_INSTALLER_EXPIRE));
    AK::Crontab::InstallerAppExpire msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstInstallerExpire->szEmail, sizeof(pstInstallerExpire->szEmail), msg.email().c_str());
    Snprintf(pstInstallerExpire->szUserName, sizeof(pstInstallerExpire->szUserName), msg.name().c_str());
    Snprintf(pstInstallerExpire->list, sizeof(pstInstallerExpire->list), msg.list().c_str());
    pstInstallerExpire->nCount = msg.count();
    pstInstallerExpire->nBefore = 0;    //已过期

    AK_LOG_INFO << "Request Installer App expire, Installer: " << pstInstallerExpire->szUserName;
    if (strlen(pstInstallerExpire->szEmail) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    GetIPCControlInstance()->SendInstallerAppWillExpire(pstInstallerExpire, msg.location());
}

void CAKCSView::OnInstallerPhoneExpire(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnInstallerPhoneExpire The param is NULL";
        return;
    }

    CSP2A_INSTALLER_EXPIRE stInstallerExpire;
    CSP2A_INSTALLER_EXPIRE* pstInstallerExpire = &stInstallerExpire;
    memset(pstInstallerExpire, 0, sizeof(CSP2A_INSTALLER_EXPIRE));
    AK::Crontab::InstallerPhoneExpire msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstInstallerExpire->szEmail, sizeof(pstInstallerExpire->szEmail), msg.email().c_str());
    Snprintf(pstInstallerExpire->szUserName, sizeof(pstInstallerExpire->szUserName), msg.name().c_str());
    Snprintf(pstInstallerExpire->list, sizeof(pstInstallerExpire->list), msg.list().c_str());
    pstInstallerExpire->nCount = msg.count();
    pstInstallerExpire->nBefore = 0;    //已过期

    AK_LOG_INFO << "Request Installer phone expire, Installer: " << pstInstallerExpire->szUserName;
    if (strlen(pstInstallerExpire->szEmail) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    GetIPCControlInstance()->SendInstallerPhoneWillExpire(pstInstallerExpire);
}

void CAKCSView::OnPMFeatureWillExpire(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnPMFeatureWillExpire The param is NULL";
        return;
    }

    CSP2A_PM_EXPIRE stpmexpire;
    CSP2A_PM_EXPIRE* pmexpire = &stpmexpire;
    memset(pmexpire, 0, sizeof(CSP2A_PM_EXPIRE));
    AK::Crontab::PmFeatureWillExpire msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pmexpire->email, sizeof(pmexpire->email), msg.email().c_str());
    Snprintf(pmexpire->username, sizeof(pmexpire->username), msg.name().c_str());
    Snprintf(pmexpire->community, sizeof(pmexpire->community), msg.location().c_str());
    pmexpire->nbefore = msg.before();

    AK_LOG_INFO << "Request PM Feature will expire, PM: " << pmexpire->username;
    if (strlen(pmexpire->email) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    GetIPCControlInstance()->SendPMFeatureWillExpire(pmexpire);
}

void CAKCSView::OnInstallerFeatureWillExpire(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnInstallerFeatureWillExpire The param is NULL";
        return;
    }

    CSP2A_INSTALLER_EXPIRE stinstallerexpire;
    CSP2A_INSTALLER_EXPIRE* pstinstallerexpire = &stinstallerexpire;
    memset(pstinstallerexpire, 0, sizeof(CSP2A_INSTALLER_EXPIRE));
    AK::Crontab::InstallerFeatureWillExpire msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstinstallerexpire->szEmail, sizeof(pstinstallerexpire->szEmail), msg.email().c_str());
    Snprintf(pstinstallerexpire->szUserName, sizeof(pstinstallerexpire->szUserName), msg.name().c_str());
    Snprintf(pstinstallerexpire->community, sizeof(pstinstallerexpire->community), msg.location().c_str());
    pstinstallerexpire->nBefore = msg.before();

    AK_LOG_INFO << "Request Installer feature will expire, Installer: " << pstinstallerexpire->szUserName;
    if (strlen(pstinstallerexpire->szEmail) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    GetIPCControlInstance()->SendInstallerFeatureWillExpire(pstinstallerexpire);
}

void CAKCSView::OnCreateRemoteDevContorl(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnCreateRemoteDevContorl The param is NULL";
        return;
    }

    AK::Adapt::CreateRemoteDevContorl msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));

    AK_LOG_INFO << "Create remote device web contorl.mac= " << msg.mac();

    CSP2A_CREATE_REMOTE_DEV_CONTORL_INFO info;
    memset(&info, 0, sizeof(CSP2A_CREATE_REMOTE_DEV_CONTORL_INFO));
    ::snprintf(info.user, sizeof(info.user), "%s", msg.user().c_str());
    ::snprintf(info.password, sizeof(info.password), "%s", msg.password().c_str());
    ::snprintf(info.mac, sizeof(info.mac), "%s", msg.mac().c_str());
    Snprintf(info.ssh_proxy_domain, sizeof(info.ssh_proxy_domain), msg.ssh_proxy_domain().c_str());
    info.port = msg.port();

    if (strlen(info.mac) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }

    //给csroute
    if (GetIPCControlInstance()->SendCreateRemoteDevContorl(info) != 0)
    {
        AK_LOG_WARN << "SendCreateRemoteDevContorl failed";
        return;
    }
}


void CAKCSView::OnFaceServerPicDownload(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnFaceServerPicDownload The param is NULL";
        return;
    }

    AK::Adapt::FaceServerPicDownloadNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    AK_LOG_INFO << "OnFaceServerPicDownload:msg=" << msg.DebugString();

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_NOTIFY_FACESERVER_PIC_DOWNLOAD);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);

    return;
}

void CAKCSView::OnFaceServerPicBatchDownload(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnFaceServerPicDownload The param is NULL";
        return;
    }

    AK::Adapt::FaceServerPicDownloadBatchNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    AK_LOG_INFO << "OnFaceServerPicBatchDownload:msg=" << msg.DebugString();

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_NOTIFY_FACESERVER_PIC_BATCH_DOWNLOAD);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);

    return;
}

void CAKCSView::OnFaceServerPicBatchModify(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnFaceServerPicBatchModify The param is NULL";
        return;
    }

    AK::Adapt::FaceServerPicModifyBatchNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    AK_LOG_INFO << "OnFaceServerPicBatchModify:msg=" << msg.DebugString();

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_NOTIFY_FACESERVER_PIC_BATCH_MODIFY);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);

    return;

}

void CAKCSView::OnFaceServerPicModify(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnFaceServerPicModify The param is NULL";
        return;
    }

    AK::Adapt::FaceServerPicModifyNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    AK_LOG_INFO << "OnFaceServerPicModify:msg=" << msg.DebugString();

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_NOTIFY_FACESERVER_PIC_MODIFY);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);

    return;

}

void CAKCSView::OnFaceServerPicBatchDelete(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnFaceServerPicBatchDelete The param is NULL";
        return;
    }

    AK::Adapt::FaceServerPicDeleteBatchNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    AK_LOG_INFO << "OnFaceServerPicBatchDelete:msg=" << msg.DebugString();

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_NOTIFY_FACESERVER_PIC_BATCH_DELETE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);

    return;
}

void CAKCSView::OnFaceServerPicDelete(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnFaceServerPicDelete The param is NULL";
        return;
    }

    AK::Adapt::FaceServerPicDeleteNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    AK_LOG_INFO << "OnFaceServerPicDelete:msg=" << msg.DebugString();

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_NOTIFY_FACESERVER_PIC_DELETE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);

    return;
}

void CAKCSView::OnSendSmsCode(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnSendSmsCode The param is NULL";
        return;
    }

    AK::Adapt::SendSmsCode msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    AK_LOG_INFO << "OnSendSmsCode: Type= " << msg.type() << " phone:" << msg.phone() << " user_type:" << msg.user_type();

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_SEND_SMS_CODE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);

    return;
}

void CAKCSView::OnPmExportLog(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnPmExportLog The param is NULL";
        return;
    }

    AK::Adapt::PmExportLog msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    AK_LOG_INFO << "OnPmExportLog: communitid= " << msg.communit_id() << " trace_id=" << msg.trace_id() << " datas="
                << msg.datas();

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PM_EXPORT_LOG);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);

    return;
}


void CAKCSView::OnFeedbackNotify(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnPmExportLog The param is NULL";
        return;
    }

    AK::Adapt::FeedbackNotify grpc_msg;
    CHECK_PB_PARSE_MSG(grpc_msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));

    Json::Reader reader;
    Json::Value root;
    // reader将Json字符串解析到root，root将包含Json里所有子元素
    if (!reader.parse(grpc_msg.datas(), root))
    {
        AK_LOG_WARN << "parse json error.data=" << grpc_msg.datas();
        return;
    }
    AK_LOG_INFO << "OnFreebackNotify: communitid= " << grpc_msg.datas();
    
    std::stringstream msg;
    std::stringstream send_files;
    std::stringstream send_emails;

    Json::Value files = root["files"];
    Json::Value send_email_list = root["send_email_list"];
    
    for(int i = 0;i < files.size();i++)
    {
        send_files << " -a " << files[i].asString();
    }
    for(int i = 0;i < send_email_list.size();i++)
    {
        if (i == 0 )
        {
            send_emails << " -b " << send_email_list[i].asString();
        }
        else
        {
            send_emails << " -c " << send_email_list[i].asString();
        }
    }    

    
    std::string content =  root["content"].asString();
    boost::replace_all(content, "\"", "\\\"");    
    msg << "Account:" << root["account"].asString() <<"\nemail:" << root["email"].asString() << "\n" << "Content:" << content << "\n\n" << gstCSADAPTConf.szServerOutIP;

    char cmd[4096] = "";
    snprintf(cmd, sizeof(cmd), "echo \"%s\" | mutt -s \"用户问题反馈-%s\"  %s %s &", msg.str().c_str(), gstCSADAPTConf.szServerHostname, send_files.str().c_str(), send_emails.str().c_str());
    system(cmd);
    return;
}

void CAKCSView::OnUpdateMacConfig(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnUpdateMacConfig The param is NULL";
        return;
    }

    AK::Adapt::DevConfigUpdateNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    AK_LOG_INFO << "OnUpdateMacConfig msg=" << msg.DebugString();

    //对下发的config进行转义操作
    char tmp_config[CS_COMMON_MSG_AUTOP_SIZE] = "";
    ChangeSpecialXmlChar(tmp_config, CS_COMMON_MSG_AUTOP_SIZE, msg.config().c_str(), msg.config().size());
    msg.set_config(tmp_config);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_UPDATE_TO_DEVICE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);

    return;
}

int CAKCSView::OneDeviceDoorControl(DEVICE_SETTING* cur_device_setting, int type)
{
    uint32_t command_id = 0;
    if (type == DoorControlType::OPEN)
    {
        command_id = MSG_C2S_KEEP_OPEN_RELAY;
    }
    else if (type == DoorControlType::CLOSE)
    {
        command_id = MSG_C2S_KEEP_CLOSE_RELAY;
    }
    else
    {
        return -1;
    }

    int relay_value = 0;
    GetValueByRelay(cur_device_setting->relay, relay_value);
    std::string door_num = RelayToString(relay_value);
    uint32_t relay = std::stoi(door_num);
    if (0 == relay)
    {
        AK_LOG_WARN << "OneDeviceDoorControl failed, mac=" << cur_device_setting->mac << " has invalid relay=" << cur_device_setting->relay;
        return -1;
    }

    AK::Server::P2PAdaptRemoteOpenDoorMsg msg;
    msg.set_mac(cur_device_setting->mac);
    msg.set_relay(relay);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(command_id);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);

    return 0;
}

void CAKCSView::OnOnceAutopNotify(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnRegularyAutopNotify The param is NULL";
        return;
    }

    AK::Adapt::OnceAutopNotify once_autop_msg;

    CHECK_PB_PARSE_MSG(once_autop_msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    AK_LOG_INFO << "OnOnceAutopNotify msg=" << once_autop_msg.DebugString();
    
    int size = once_autop_msg.mac_list_size();
    for (int i = 0; i < size; i++)
    {
        AK::Adapt::DevConfigUpdateNotify msg;
        msg.set_config(once_autop_msg.config());
        msg.set_mac(once_autop_msg.mac_list(i));
        AK_LOG_INFO << "OnUpdateMacConfig msg=" << msg.DebugString();

        CAkcsPdu pdu;
        pdu.SetMsgBody(&msg);
        pdu.SetHeadLen(sizeof(PduHeader_t));
        pdu.SetVersion(50);
        pdu.SetCommandId(MSG_C2S_UPDATE_TO_DEVICE);
        pdu.SetSeqNum(0);
        g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    }
}

/**
 * 云平台给用户发送6位的随机密码以验证身份（模板另外给出）
 * 若用户有绑定邮箱，则发给邮箱。
 * 若用户只绑定了手机号，则发给手机号。
 * 验证码时效性5分钟。一进到此页面会自动发第一次验证码，获取验证码的按钮倒计时60秒，以后每次重新获取，都需要等60秒
 * 
 * @param msg_buf 
 * @param msg_len 
 */
void CAKCSView::OnDelAppAccountNotify(void *msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnDelAppAccountNotify The param is NULL";
        return;
    }

    AK::Adapt::DelAppAccountNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    AK_LOG_INFO << "OnDelAppAccountNotify msg=" << msg.DebugString();

    ResidentPerAccount personal_account;
    memset(&personal_account, 0, sizeof(personal_account));
    if (0 != dbinterface::ResidentPersonalAccount::GetUidAccount(msg.account(), personal_account))
    {
        AK_LOG_WARN << "GetUidAccount Failed,account=" << msg.account();
        return;
    }

    AK_LOG_INFO << "Use Account=" << msg.account() << " Get Email=" << personal_account.getEmail() 
     << ";PhoneCode=" << personal_account.phone_code << ";MobileNumber=" << personal_account.getMobileNumber() 
     << ";Language=" << personal_account.language << ";Name=" << personal_account.name;

    //获取6位随机数
    int random_num = GetRandomNum(1000000);
    char code[7] = {0};
    snprintf(code, sizeof(code), "%06d", random_num);

    //若用户有绑定邮箱，则发给邮箱
    if (strlen(personal_account.getEmail()) > 0)
    {
        std::string msg_code = WriteCodeToRedis(msg.account(), code);
        if (msg_code.length() == 0)
        {
            AK_LOG_WARN << "DaoGetAccountStatuByNode Failed,msg_code is null";
            return;
        }
        AK::Server::P2PAdaptDelAppAccountMailMsg del_app_accout_msg;
        del_app_accout_msg.set_code(msg_code);
        del_app_accout_msg.set_language(personal_account.language);
        del_app_accout_msg.set_name(personal_account.name);
        del_app_accout_msg.set_email(personal_account.getEmail());
        AK_LOG_INFO << "Send DelAppAccountMailMsg=" << del_app_accout_msg.DebugString();

        CAkcsPdu pdu;
        pdu.SetMsgBody(&del_app_accout_msg);
        pdu.SetHeadLen(sizeof(PduHeader_t));
        pdu.SetVersion(50);
        pdu.SetCommandId(MSG_C2S_SEND_MAIL_DELETE_APP_ACCOUNT);
        pdu.SetSeqNum(0);
        g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
        return;
    }

    //从账号无手机号和邮箱
    if ((personal_account.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT || personal_account.role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT) 
        && strlen(personal_account.getMobileNumber()) == 0)
    {
        ResidentPerAccount master_personal_account;
        memset(&master_personal_account, 0, sizeof(master_personal_account));
        if (0 != dbinterface::ResidentPersonalAccount::GetUUIDAccount(personal_account.parent_uuid, master_personal_account))
        {
            AK_LOG_WARN << "GetUUIDAccount Failed,uuid=" << personal_account.parent_uuid;
            return;
        }
        if (strlen(master_personal_account.getEmail()) > 0)
        {
            std::string msg_code = WriteCodeToRedis(msg.account(), code);
            AK::Server::P2PAdaptDelAppAccountMailMsg del_app_accout_msg;
            del_app_accout_msg.set_code(msg_code);
            del_app_accout_msg.set_language(master_personal_account.language);
            del_app_accout_msg.set_name(master_personal_account.name);
            del_app_accout_msg.set_email(master_personal_account.getEmail());
            del_app_accout_msg.set_to_master(SEND_TO_MASTER);
            AK_LOG_INFO << "Send DelAppAccountMailMsg to master=" << del_app_accout_msg.DebugString();

            CAkcsPdu pdu;
            pdu.SetMsgBody(&del_app_accout_msg);
            pdu.SetHeadLen(sizeof(PduHeader_t));
            pdu.SetVersion(50);
            pdu.SetCommandId(MSG_C2S_SEND_MAIL_DELETE_APP_ACCOUNT);
            pdu.SetSeqNum(0);
            g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
        }
        else if (strlen(master_personal_account.getMobileNumber()) > 0 && strlen(master_personal_account.phone_code) > 0)
        {
            std::string msg_code = WriteCodeToRedis(msg.account(), code);
            AK::Server::SendSmsCodeDelAppAccount send_sms_code;
            send_sms_code.set_area_code(master_personal_account.phone_code);
            send_sms_code.set_phone(master_personal_account.getMobileNumber());
            send_sms_code.set_type(SMS_FAMILY_DEL_APP_ACCOUNT);
            send_sms_code.set_language(master_personal_account.language);
            send_sms_code.set_code(msg_code);
            AK_LOG_INFO << "Send DelAppAccountSmsMsg to master=" << send_sms_code.DebugString();

            CAkcsPdu pdu;
            pdu.SetMsgBody(&send_sms_code);
            pdu.SetHeadLen(sizeof(PduHeader_t));
            pdu.SetVersion(50);
            pdu.SetCommandId(MSG_C2S_SEND_SMS_DELETE_APP_ACCOUNT);
            pdu.SetSeqNum(0);
            g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
        }
        else
        {
            AK_LOG_WARN << "parameter error";
        }
        return;
    }

    std::string msg_code = WriteCodeToRedis(msg.account(), code);
    if (msg_code.length() == 0)
    {
        AK_LOG_WARN << "DaoGetAccountStatuByNode Failed,msg_code is null";
        return;
    }    
    AK::Server::SendSmsCodeDelAppAccount send_sms_code;
    send_sms_code.set_area_code(personal_account.phone_code);
    send_sms_code.set_phone(personal_account.getMobileNumber());
    send_sms_code.set_type(SMS_DEL_APP_ACCOUNT);
    send_sms_code.set_language(personal_account.language);
    send_sms_code.set_code(msg_code);
    AK_LOG_INFO << "Send DelAppAccountSmsMsg=" << send_sms_code.DebugString();

    CAkcsPdu pdu;
    pdu.SetMsgBody(&send_sms_code);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_SEND_SMS_DELETE_APP_ACCOUNT);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
}

std::string CAKCSView::WriteCodeToRedis(const std::string &account, const std::string &code)
{
    SafeCacheConn redis(g_redis_db_appcode);
    std::string exist_code = redis.get(account);

    if (exist_code.empty())
    {
        redis.set(account, code);
        redis.expire(account, 300);
        return code;
    }

    return exist_code;
}

//通知PM社区多少账号即将过期,单个通知
void CAKCSView::OnPMAppAccountWillBeExpire(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnPMAppAccountWillExpire The param is NULL";
        return;
    }

    CSP2A_PM_INFO stPmInfo;
    CSP2A_PM_INFO* pstPmInfo = &stPmInfo;
    memset(pstPmInfo, 0, sizeof(CSP2A_PM_INFO));
    AK::Crontab::PMAppAccountWillBeExpire msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstPmInfo->szCommunity, sizeof(pstPmInfo->szCommunity), msg.community().c_str());
    Snprintf(pstPmInfo->szEmail, sizeof(pstPmInfo->szEmail), msg.email().c_str());
    Snprintf(pstPmInfo->szName, sizeof(pstPmInfo->szName), msg.name().c_str());
    Snprintf(pstPmInfo->list, sizeof(pstPmInfo->list), msg.list().c_str());
    pstPmInfo->nAccountNum = msg.account_num();
    pstPmInfo->nBefore = msg.before();

    AK_LOG_INFO << "Request notify pm app account will expire Email, pm community: " << pstPmInfo->szCommunity;
    if (strlen(pstPmInfo->szEmail) == 0 || strlen(pstPmInfo->szCommunity) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    //给csroute
    GetIPCControlInstance()->SendPmAppAccountWillBeExpireEmail(pstPmInfo);

}

//通知PM社区多少账号即将过期,单个通知
void CAKCSView::OnPMAppAccountExpire(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnPMAppAccountExpire The param is NULL";
        return;
    }

    CSP2A_PM_INFO stPmInfo;
    CSP2A_PM_INFO* pstPmInfo = &stPmInfo;
    memset(pstPmInfo, 0, sizeof(CSP2A_PM_INFO));
    AK::Crontab::PMAppAccountExpire msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pstPmInfo->szCommunity, sizeof(pstPmInfo->szCommunity), msg.community().c_str());
    Snprintf(pstPmInfo->szEmail, sizeof(pstPmInfo->szEmail), msg.email().c_str());
    Snprintf(pstPmInfo->szName, sizeof(pstPmInfo->szName), msg.name().c_str());
    Snprintf(pstPmInfo->list, sizeof(pstPmInfo->list), msg.list().c_str());
    pstPmInfo->nAccountNum = msg.account_num();

    AK_LOG_INFO << "Request notify pm app account expire Email, pm community: " << pstPmInfo->szCommunity;
    if (strlen(pstPmInfo->szEmail) == 0 || strlen(pstPmInfo->szCommunity) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    //给csroute
    GetIPCControlInstance()->SendPmAppAccountExpireEmail(pstPmInfo);

}

void CAKCSView::OnPMAccountActiveEmail(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnPmAccountActiveEmail The param is NULL";
        return;
    }
    
    CSP2A_ACCOUNT_ACTIVE_INFO account;
    CSP2A_ACCOUNT_ACTIVE_INFO* account_active_info = &account;
    memset(account_active_info, 0, sizeof(CSP2A_ACCOUNT_ACTIVE_INFO));

    AK::Adapt::PmAccountActiveEmailNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    account_active_info->nActive = msg.active();
    account_active_info->subscription = msg.subscription();
    Snprintf(account_active_info->account, sizeof(account_active_info->account), msg.account().c_str());

    //获取pm账号的映射信息
    dbinterface::PmAccountInfoPtr pm_account_info = nullptr;
    dbinterface::PmAccountMap::GetPmInfoByAccount(account_active_info->account, pm_account_info);
    if(!pm_account_info)
    {
        AK_LOG_WARN << "GetPmInfoByAccount Failed, pm_account_info is null";
        return;
    }

    //6.6一人多套房修改，pm在AccountMap是1对1的
    UserInfoAccount account_user;
    if (0 !=  dbinterface::AccountUserInfo::GetAccountUserInfoByAccountUUID(pm_account_info->account_uuid, account_user))
    {
        AK_LOG_WARN << "GetAccountUserInfoByAccountUUID Failed";
        return;
    }

    //获取pm账号的用户名
    ResidentPerAccount account_username_info;
    memset(&account_username_info, 0, sizeof(account_username_info));
    if(0 != dbinterface::ResidentPersonalAccount::GetUUIDAccount(pm_account_info->personal_account_uuid, account_username_info))
    {
        AK_LOG_WARN << "GetUUIDAccount Failed, pm_personal_account_info is null";
        return;
    }

    //获取pm账号的过期时间
    std::string expire_time;
    dbinterface::ResidentPersonalAccount::GetExpireTimeByUUID(pm_account_info->personal_account_uuid, expire_time, ACCOUNT_APP_EXPIRE);
    
    Snprintf(account_active_info->szEmail, sizeof(account_active_info->szEmail), account_user.email);
    Snprintf(account_active_info->szUserName, sizeof(account_active_info->szUserName), account_username_info.name);
    Snprintf(account_active_info->expire_time, sizeof(account_active_info->expire_time), expire_time.c_str());

    if (::strlen(gstCSADAPTConf.web_domain) > 1)
    {
        ::snprintf(account_active_info->szServerWebUrl, sizeof(account_active_info->szServerWebUrl), "http://%s", gstCSADAPTConf.web_domain);
    }
    else
    {
        ::snprintf(account_active_info->szServerWebUrl, sizeof(account_active_info->szServerWebUrl), "http://%s", gstCSADAPTConf.szCsadaptOuterIP);
    }

    AK_LOG_INFO << "Request Pm Account Active Email, user: " << account_active_info->szUserName << " email:" << account_active_info->szEmail;
    if (strlen(account_active_info->szEmail) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    //给csroute发送通知
    if (GetIPCControlInstance()->SendPmAccountActiveEmail(account_active_info) != 0)
    {
        AK_LOG_WARN << "SendAccountActiveEmail failed";
        return;
    }
}

void CAKCSView::PushLinKerExpire(const SOCKET_MSG_LINKER_EXPIRE_MESSAGE& expire_msg, const LINKER_NORMAL_MSG &linker_msg)
{
    Json::Value item;
    Json::FastWriter w;
    item["type"] = expire_msg.type;
    item["notify_type"] = expire_msg.notify_type;
    item["leave_days"] = expire_msg.leave_days;
    item["expire_timestamp"] = expire_msg.expire_timestamp * 1000;
    item["account_uuid"] = linker_msg.account_uuid;
    item["account_name"] = linker_msg.account_name;
    item["node_uuid"] = linker_msg.account_uuid;//目前过期只通知主账号
    item["project_type"] = linker_msg.project_type;
    item["project_uuid"] = linker_msg.project_uuid;
    item["timestamp"] = GetCurrentMilliTimeStamp();
    item["ins_uuid"] = linker_msg.ins_uuid;
    item["enable_smarthome"] = linker_msg.enable_smarthome;
    std::string data_json = w.write(item);
    GetIPCControlInstance()->SendLinKerCommonMsg(LinkerPushMsgType::LINKER_MSG_TYPE_ACCOUNT_EXPIRE, data_json, linker_msg.account_uuid);
}

void CAKCSView::OnPmAppRenewServer(ResidentPerAccount& account)
{
    CSP2A_UID_PM_RENEW_SRV_INFO uid_info;
    memset(&uid_info, 0, sizeof(CSP2A_UID_PM_RENEW_SRV_INFO));
    Snprintf(uid_info.username, sizeof(uid_info.username), account.name);
    Snprintf(uid_info.account, sizeof(uid_info.account), account.account);
    uid_info.type = 0;

    //获取pm账号的映射信息
    dbinterface::PmAccountInfoPtr pm_account_info = nullptr;
    if (0 == dbinterface::PmAccountMap::GetPmInfoByAccount(account.account, pm_account_info))
    {
        //6.6一人多套房修改，pm在AccountMap是1对1的
        UserInfoAccount account_user;
        if (0 !=  dbinterface::AccountUserInfo::GetAccountUserInfoByAccountUUID(pm_account_info->account_uuid, account_user))
        {
            AK_LOG_WARN << "GetAccountUserInfoByAccountUUID Failed";
            return;
        }

        Snprintf(uid_info.email, sizeof(uid_info.email), account_user.email);
        AK_LOG_INFO << "Request pm renew server, account:" << account.account << " email:" << uid_info.email;
    }
    else
    {
        AK_LOG_WARN << "GetPmInfoByAccount Failed";
        return;
    }

    //获取expire_time,转换时区操作
    std::string expire_time;
    dbinterface::ResidentPersonalAccount::GetExpireTime(expire_time, account);
    Snprintf(uid_info.time, sizeof(uid_info.time), expire_time.c_str());
   
    GetIPCControlInstance()->SendPmRenewServerEmail(uid_info);      
}

// 开三方锁
void CAKCSView::OnThirdPartyLockNotify(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnThirdPartyLockNotify The param is NULL";
        return;
    }
    
    AK::Adapt::ThirdPartyLockNotify notify;
    CHECK_PB_PARSE_MSG(notify.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    
    Json::Value item;
    Json::FastWriter w;
    
    item["pic_name"] = "";
    item["uuid"] = notify.uuid();
    item["initiator"] = notify.initiator();
    item["lock_name"] = notify.lock_name();
    item["lock_type"] = notify.lock_type();
    item["capture_type"] = notify.capture_type();
    item["personal_account_uuid"] = notify.personal_account_uuid();

    std::string data_json = w.write(item);

    //给csmain发送通知,有最新的文本消息需要发送给联动系统
    if (GetIPCControlInstance()->SendLinKerCommonMsg(notify.message_type(), data_json, notify.uuid()) != 0)
    {
        AK_LOG_WARN << "SendLinKerCommonMsg failed";
        return;
    }

}

void CAKCSView::OnUserAddNewSite(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnUserAddNewSite The param is NULL";
        return;
    }

    CSP2A_PER_ADD_NEWSITE per_add_new_site;
    memset(&per_add_new_site, 0, sizeof(CSP2A_PER_ADD_NEWSITE));
    AK::Adapt::PerAddNewSite msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(per_add_new_site.userinfo_uuid, sizeof(per_add_new_site.userinfo_uuid), msg.userinfo_uuid().c_str());
    Snprintf(per_add_new_site.project_name, sizeof(per_add_new_site.project_name), msg.project_name().c_str());
    Snprintf(per_add_new_site.apt_num, sizeof(per_add_new_site.apt_num), msg.apt_num().c_str());
    Snprintf(per_add_new_site.email, sizeof(per_add_new_site.email), msg.email().c_str());
    Snprintf(per_add_new_site.send_type, sizeof(per_add_new_site.send_type), msg.send_type().c_str());
    
    ResidentPerAccount account;
    memset(&account, 0, sizeof(ResidentPerAccount));
    if (0 == dbinterface::ResidentPersonalAccount::GetMainSiteAccountByUserInfoUUID(per_add_new_site.userinfo_uuid, account))
    {
        Snprintf(per_add_new_site.name, sizeof(per_add_new_site.name), account.name);
        per_add_new_site.role = account.role;
    }

    AK_LOG_INFO << "OnUserAddNewSite, user: " << per_add_new_site.name;
    if (strlen(per_add_new_site.email) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    GetIPCControlInstance()->SendUserAddNewSite(per_add_new_site);
}

void CAKCSView::OnPmWebLinkNewSites(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnPmWebLinkNewSites The param is NULL";
        return;
    }

    CSP2A_PM_LINK_NEWSITES pm_link_new_sites;
    memset(&pm_link_new_sites, 0, sizeof(CSP2A_PM_LINK_NEWSITES));
    AK::Adapt::PmLinkNewSites msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(pm_link_new_sites.account_uuid, sizeof(pm_link_new_sites.account_uuid), msg.account_uuid().c_str());
    Snprintf(pm_link_new_sites.comm_name_list, sizeof(pm_link_new_sites.comm_name_list), msg.comm_name_list().c_str());
    Snprintf(pm_link_new_sites.office_name_list, sizeof(pm_link_new_sites.office_name_list), msg.office_name_list().c_str());
    Snprintf(pm_link_new_sites.email, sizeof(pm_link_new_sites.email), msg.email().c_str());

    std::string pm_name;
    dbinterface::PropertyInfo::GetFullNameByUUID(pm_link_new_sites.account_uuid, pm_name);
    Snprintf(pm_link_new_sites.name, sizeof(pm_link_new_sites.name), pm_name.c_str());

    AK_LOG_INFO << "OnPmWebLinkNewSites, pm: " << pm_link_new_sites.name;
    if (strlen(pm_link_new_sites.email) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    GetIPCControlInstance()->SendPmWebLinkNewSites(pm_link_new_sites);
}

//pm web创建帐号,user字段填写pm account的uuid
void CAKCSView::OnPmWebCreateUid(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnPmWebCreateUid The param is NULL";
        return;
    }

    CSP2A_USER_CREATE_INFO user_create_info;
    memset(&user_create_info, 0, sizeof(CSP2A_USER_CREATE_INFO));
    AK::Adapt::PerCreateUser msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(user_create_info.szEmail, sizeof(user_create_info.szEmail), msg.email().c_str());
    Snprintf(user_create_info.szPwd, sizeof(user_create_info.szPwd), msg.pwd().c_str());
    Snprintf(user_create_info.szQRCodeBody, sizeof(user_create_info.szQRCodeBody), msg.qrcode_body().c_str());
    Snprintf(user_create_info.szQRCodeUrl, sizeof(user_create_info.szQRCodeUrl), msg.qrcode_url().c_str());
    Snprintf(user_create_info.szUser, sizeof(user_create_info.szUser), msg.user().c_str());

    if (strlen(gstCSADAPTConf.web_domain) > 1)
    {
        ::snprintf(user_create_info.szServerWebUrl, sizeof(user_create_info.szServerWebUrl), "https://%s", gstCSADAPTConf.web_domain);
    }
    else
    {
        ::snprintf(user_create_info.szServerWebUrl, sizeof(user_create_info.szServerWebUrl), "http://%s", gstCSADAPTConf.szCsadaptOuterIP);
    }

    AK_LOG_INFO << "Request PmWebCreateUid:" << user_create_info.szUser << " email:" << user_create_info.szEmail;
    if (strlen(user_create_info.szUser) <= 0 || strlen(user_create_info.szPwd) <= 0 || strlen(user_create_info.szEmail) <= 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }

    if (GetIPCControlInstance()->SendPmWebCreateUidMail(user_create_info) != 0)
    {
        AK_LOG_WARN << "Send PmWebCreateUid failed";
        return;
    }
}

//pm web主动修改密码,user字段填写pm account的uuid
void CAKCSView::OnPmWebChangePwd(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnPmWebChangePwd The param is NULL";
        return;
    }

    CSP2A_USER_CREATE_INFO user_create_info;
    memset(&user_create_info, 0, sizeof(CSP2A_USER_CREATE_INFO));
    AK::Adapt::PerChangePwd msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(user_create_info.szEmail, sizeof(user_create_info.szEmail), msg.email().c_str());
    Snprintf(user_create_info.szPwd, sizeof(user_create_info.szPwd), msg.pwd().c_str());
    Snprintf(user_create_info.szQRCodeBody, sizeof(user_create_info.szQRCodeBody), msg.qrcode_body().c_str());
    Snprintf(user_create_info.szQRCodeUrl, sizeof(user_create_info.szQRCodeUrl), msg.qrcode_url().c_str());
    Snprintf(user_create_info.szUser, sizeof(user_create_info.szUser), msg.user().c_str());

    AK_LOG_INFO << "Request PmWebChangePwd, email:" << user_create_info.szEmail << " user " << user_create_info.szUser;
    if (strlen(user_create_info.szUser) <= 0 || strlen(user_create_info.szPwd) <= 0 || strlen(user_create_info.szEmail) <= 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }

    if (GetIPCControlInstance()->SendPmWebChangePwdMail(user_create_info) != 0)
    {
        AK_LOG_WARN << "Send PmWebChangePwd failed";
        return;
    }
}

void CAKCSView::OnSendCommonSmsCode(void *msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnSendCommonSmsCode The param is NULL";
        return;
    }

    AK::Adapt::SendSMSVerficationCode msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    AK_LOG_INFO << "OnSendCommonSmsCode msg=" << msg.DebugString();

    CSP2A_SEND_VERFICATION_CODE verification_code;
    memset(&verification_code, 0, sizeof(CSP2A_SEND_VERFICATION_CODE));
    Snprintf(verification_code.mobile_number, sizeof(verification_code.mobile_number), msg.mobile_number().c_str());
    Snprintf(verification_code.phone_code, sizeof(verification_code.phone_code), msg.phone_code().c_str());
    Snprintf(verification_code.language, sizeof(verification_code.language), msg.language().c_str());
    Snprintf(verification_code.code, sizeof(verification_code.code), msg.code().c_str());
    Snprintf(verification_code.type, sizeof(verification_code.type), msg.type().c_str());

    if (GetIPCControlInstance()->SendCommonSmsCode(verification_code) != 0)
    {
        AK_LOG_WARN << "Send SMS Code failed";
        return;
    }
}

void CAKCSView::OnSendCommonEmailCode(void *msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnSendCommonEmailCode The param is NULL";
        return;
    }

    AK::Adapt::SendEmailVerficationCode msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    AK_LOG_INFO << "OnSendCommonEmailCode msg=" << msg.DebugString();

    CSP2A_SEND_VERFICATION_CODE verification_code;
    memset(&verification_code, 0, sizeof(CSP2A_SEND_VERFICATION_CODE));
    Snprintf(verification_code.email, sizeof(verification_code.email), msg.email().c_str());
    Snprintf(verification_code.name, sizeof(verification_code.name), msg.name().c_str());
    Snprintf(verification_code.language, sizeof(verification_code.language), msg.language().c_str());
    Snprintf(verification_code.code, sizeof(verification_code.code), msg.code().c_str());
    Snprintf(verification_code.type, sizeof(verification_code.type), msg.type().c_str());

    if (GetIPCControlInstance()->SendCommonEmailCode(verification_code) != 0)
    {
        AK_LOG_WARN << "Send Email Code failed";
        return;
    }
}

void CAKCSView::OnPcapCaptureNotify(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnPcapCapture The param is NULL";
        return;
    }

    AK::Adapt::WebPcapCaptureNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    AK_LOG_INFO << "OnPcapCaptureNotify msg = " <<  msg.DebugString();

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PCAP_CAPTURE_CONTROL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
}

void CAKCSView::OnSendEmailNotify(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnSendEmailNotify The param is NULL";
        return;
    }

    AK::Adapt::SendEmailNotifyMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    AK_LOG_INFO << "OnSendEmailNotify msg";
    
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_SEND_EMAIL_NOTIFY);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
}

int CAKCSView::checkUserEnableSmartHome(std::string account, int expire_type, int notify_type, int before)
{
    LINKER_NORMAL_MSG linker_msg;
    SOCKET_MSG_LINKER_EXPIRE_MESSAGE expire_msg;
    memset(&linker_msg, 0, sizeof(linker_msg));
    memset(&expire_msg, 0, sizeof(expire_msg));
    ProjectInfo project(account, linker_msg);
    expire_msg.type = expire_type;
    expire_msg.notify_type = notify_type;
    expire_msg.leave_days = before;
    expire_msg.expire_timestamp = linker_msg.expire_timestamp;
    PushLinKerExpire(expire_msg, linker_msg);

    if(linker_msg.enable_smarthome)
    {
        //启用家居不需要邮件通知。原先在expire php脚本处理
        return 0;
    }

    return -1;
}

void CAKCSView::OnSendCrontabEmailNotify(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnSendCrontabEmailNotify The param is NULL";
        return;
    }

    AK::Crontab::SendEmailNotifyMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));

    //兼容家居
    Json::Reader reader;
    Json::Value root;
    Json::Value data;
    if (!reader.parse(msg.payload(), root))
    {
        AK_LOG_WARN << "parse json error.data=" << msg.payload();
        return;
    }

    if (!reader.parse(root["data"].asString(), data))
    {
        AK_LOG_WARN << "parse json error.data=" << root["data"];
        return;
    }
    
    AK_LOG_INFO << "OnSendCrontabEmailNotify: data= " << root["data"] << " account= " << data["account"].asString();
    
    std::string email_type = data["email_type"].asString();
    int expire_type = 0;
    int notify_type = 0;
    int before = 0;

    //以下type都是只发enduser
    if (email_type == "account_expire")
    {
        expire_type = LinkerExpireType::ACCOUNT_APP_EXPIRE;
        notify_type = LinkerExpireNotifyType::NOTIFY_EXPIRE;
        before = -1;
    }
    else if (email_type == "will_expire")
    {
        //用户过期只有3天发enduser
        expire_type = LinkerExpireType::ACCOUNT_APP_EXPIRE;
        notify_type = LinkerExpireNotifyType::NOTIFY_WILL_EXPIRE;
        before = 3;
    }
    else if (email_type == "phone_expire")
    {
        expire_type = LinkerExpireType::ACCOUNT_FEATURE_EXPIRE;
        notify_type = LinkerExpireNotifyType::NOTIFY_EXPIRE;
        before = -1;
    }
    else if (email_type == "phone_will_expire")
    {
        expire_type = LinkerExpireType::ACCOUNT_FEATURE_EXPIRE;
        notify_type = LinkerExpireNotifyType::NOTIFY_WILL_EXPIRE;
        before = data["before"].asInt();
    }
    
    if (expire_type != 0 && notify_type != 0)
    {
        //0代表启用智能家居
        if (0 == checkUserEnableSmartHome(data["account"].asString(), expire_type, notify_type, before))
        {
            return;
        }
    }
    
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_SEND_EMAIL_NOTIFY);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
}

void CAKCSView::OnKitAccountLogOff(void* msg_buf, unsigned int msg_len)
{
    if (nullptr == msg_buf)
    {
        AK_LOG_WARN << "OnKitAccountLogOff The param is NULL";
        return;
    }

    AK::Adapt::KitAccountLogOff msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));

    //获取node中的设备列表
    ResidentDeviceList devlist;
    if (0 == dbinterface::ResidentPerDevices::GetNodeDevList(msg.node(), devlist))
    {
        for (const auto& dev : devlist)
        {
            GetIPCControlInstance()->SendRequestDevDelLog(dev.mac);
        }
    }
}

void CAKCSView::OnSipPcapCaptureNotify(void* msg_buf, unsigned int msg_len)
{
    if (nullptr == msg_buf)
    {
        AK_LOG_WARN << "OnPcapCapture The param is NULL";
        return;
    }

    AK::Adapt::WebSipPcapCaptureNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    AK_LOG_INFO << "OnSipPcapCaptureNotify msg = " <<  msg.DebugString();

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_SIP_PCAP_CAPTURE_CONTROL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
}

void CAKCSView::OnOpenApiHealthCheckNotify(void* msg_buf, unsigned int msg_len)
{
    if (nullptr == msg_buf)
    {
        AK_LOG_WARN << "OnPcapCapture The param is NULL";
        return;
    }

    AK::Adapt::OpenApiSocketHealthCheck msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    AK_LOG_INFO << "OnOpenApiHealthCheckNotify msg = " <<  msg.DebugString();
    
    ++g_openapi_health_check_count;
    return;
}

void CAKCSView::InsertEmegencyNotifyAlarmLog(const dbinterface::AccountInfo& project_info, int control_type)
{
    //获取社区下主账号列表
    std::set<std::string> node_list;
    if (0 != dbinterface::ResidentPersonalAccount::GetNodeListByProjectUUID(project_info.uuid, node_list))
    {
        AK_LOG_WARN << "Get node list failed. project_uuid=" << project_info.uuid;
        return;
    }

    int alarm_code = 0;
    if (control_type == PmEmergencyDoorControlType::CONTROL_TYPE_OPEN)
    {
        alarm_code = (int)ALARM_CODE::EMERGENCY_NOTIFY_OPEN;
    }
    else if (control_type == PmEmergencyDoorControlType::CONTROL_TYPE_CLOSE)
    {
        alarm_code = (int)ALARM_CODE::EMERGENCY_NOTIFY_CLOSED;
    }

    ALARM alarm;
    memset(&alarm, 0, sizeof(alarm));
    alarm.manager_account_id = project_info.id;
    alarm.status = (uint32_t)AlarmStatus::ALARM_STATUS_UNDEALED;
    alarm.alarm_code = alarm_code;

    for (const auto& node : node_list)
    {
        Snprintf(alarm.device_node, sizeof(alarm.device_node), node.c_str());
        dbinterface::Alarm::AddAlarm(&alarm, gstCSADAPTConf.server_tag);
    }

    return;
}

// 家居室内机开门消息
void CAKCSView::OnDeviceRemoteOpenDoor(void* pMsgBuf, unsigned int nMsgLen)
{
    if (NULL == pMsgBuf)
    {
        AK_LOG_WARN << "OnDeviceRemoteOpenDoor The param is NULL";
        return;
    }

    // 解出原消息
    AK::Adapt::OpenDoorNotifyMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)pMsgBuf + CS_COMMON_MSG_HEADER_SIZE, nMsgLen - CS_COMMON_MSG_HEADER_SIZE));

    project::PROJECT_TYPE project_type = project::PROJECT_TYPE::RESIDENCE;
    if (msg.project_type() == 1) {
        project_type = project::PROJECT_TYPE::PERSONAL;
    }

    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    AK_LOG_INFO << "OnDeviceRemoteOpenDoor: uid=" <<  msg.uid() << ", project_type=" << msg.project_type()
                << ", mac=" << msg.mac() << ", repost_mac=" << msg.repost_mac() 
                << ", relay_type=" << msg.relay_type() << ", relay=" << msg.relay()
                << ", traceid=" << traceid;

    /*
    AK::Server::P2PDeviceOpenDoorNotifyMsg p2p_msg;
    p2p_msg.set_mac(msg.mac());
    p2p_msg.set_from_mac(msg.from_mac());
    p2p_msg.set_type(msg.open_door_type());
    p2p_msg.set_relay(msg.relay());
    p2p_msg.set_msg_traceid(std::to_string(traceid));

    AK_LOG_INFO << "Device Request Remote OpenDoor. mac:" << p2p_msg.mac() << ", from_mac:" << p2p_msg.from_mac()
                << ",type: " << p2p_msg.type() << ",relay:" << p2p_msg.relay();
    
    AK::BackendCommon::BackendP2PBaseMessage base;
    base = BackendP2PMsgControl::CreateP2PBaseMsg(AKCS_M2R_P2P_DEVICE_OPEN_DOOR_MSG,
                                                TransP2PMsgType::TO_DEV_MAC, msg.mac(),
                                                BackendP2PMsgControl::DevProjectTypeToDevType(project_type),
                                                project_type);
    base.mutable_p2pdeviceopendoornotifymsg2()->CopyFrom(p2p_msg);
    BackendP2PMsgControl::PushMsg2Route(&base, project_type);
    */

    // 转成P2P消息
    AK::Server::P2POpenDoorNotifyMsg post_msg;
    post_msg.set_uid(msg.uid());
    post_msg.set_mac(msg.mac());
    post_msg.set_relay(msg.relay());
    post_msg.set_relay_type(msg.relay_type());
    post_msg.set_project_type(project_type);
    post_msg.set_repost_mac(msg.repost_mac());
    post_msg.set_msg_traceid(std::to_string(traceid));

    AK::BackendCommon::BackendP2PBaseMessage base_msg = BackendP2PMsgControl::CreateP2PBaseMsg(
        AKCS_M2R_P2P_REMOTE_OPENDOOR_MSG,
        TransP2PMsgType::TO_DEV_MAC,
        msg.mac(),
        BackendP2PMsgControl::DevProjectTypeToDevType(project_type),
        project_type
    );

    base_msg.mutable_p2popendoornotifymsg2()->CopyFrom(post_msg);
    BackendP2PMsgControl::PushMsg2Route(&base_msg, project_type);
}

void CAKCSView::OnRequestDeviceCapture(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnRequestDeviceCapture The param is NULL";
        return;
    }

    AK::Adapt::RequestDeviceCaptureNotifyMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));

    project::PROJECT_TYPE project_type = project::PROJECT_TYPE::RESIDENCE;
    if (msg.project_type() == 1) {
        project_type = project::PROJECT_TYPE::PERSONAL;
    }
    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    AK::Server::P2PRequestDeviceCaptureNotifyMsg p2p_msg;
    p2p_msg.set_mac(msg.mac());
    p2p_msg.set_site(msg.site());
    p2p_msg.set_uuid(msg.uuid());
    p2p_msg.set_camera(msg.camera());
    p2p_msg.set_msg_traceid(std::to_string(traceid));


    AK_LOG_INFO << "Device Request Device Capture. mac:" << p2p_msg.mac() << ", site:" << p2p_msg.site()
                << ",uuid: " << p2p_msg.uuid() << ",camera:" << p2p_msg.camera() << ",project_type:" << msg.project_type();

    AK::BackendCommon::BackendP2PBaseMessage base;
    base = BackendP2PMsgControl::CreateP2PBaseMsg(AKCS_M2R_P2P_REQUEST_DEVICE_CAPTURE_MSG, TransP2PMsgType::TO_DEV_MAC, msg.mac(),
                                                  BackendP2PMsgControl::DevProjectTypeToDevType(project_type), project_type);
    base.mutable_p2prequestdevicecapturenotifymsg2()->CopyFrom(p2p_msg);
    BackendP2PMsgControl::PushMsg2Route(&base, project_type);

}

void CAKCSView::OnSmartLockUpdate(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnSmartLockUpdate The param is NULL";
        return;
    }

    AK::Adapt::SmartLockUpdateNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    
    std::string lock_uuid = msg.lock_uuid();
    int notify_smartlock_type = msg.lock_type();

    //消息类型转换
    AK::Server::SmartLockUpdateConfigurationNotifyMsg inner_msg;
    inner_msg.set_lock_uuid(lock_uuid);
    inner_msg.set_lock_type(notify_smartlock_type);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&inner_msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_R2S_UPDATE_SMARTLOCK_CONFIGURATION_NOTIFY_REQ);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
}

void CAKCSView::OnSmartLockHttpUpMessageRoute(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnSmartLockHttpUpMessageRoute The param is NULL";
        return;
    }

    AK::Adapt::SmartLockHttpUpP2PRouteMessage msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_R2S_P2P_SMARTLOCK_HTTP_UP_MSG);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
}
