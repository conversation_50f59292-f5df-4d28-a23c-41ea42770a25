#include "stdlib.h"
#include <functional>
#include "AkcsPduBase.h"
#include "util.h"
#include<evpp/evnsq/message.h>
#include "AdaptMQProduce.h"
#include "evpp/event_loop.h"
#include "AkcsMonitor.h"
#include "AkLogging.h"


const static uint32_t kAkMsgHoldLen = sizeof(int32_t);
RouteMQProduce* g_nsq_producer = nullptr;

void MQProduceInit()
{
    evpp::EventLoop nsq_loop;
    evnsq::Option op;
    //op.auth_secret = auth_secret;
    evnsq::Producer client(&nsq_loop, op);
    g_nsq_producer = new RouteMQProduce(&client);
    client.SetMessageCallback(&OnRouteMQMessage);
    client.SetReadyCallback(std::bind(&RouteMQProduce::OnNSQReady, g_nsq_producer));
    client.SetConnectErrorCallback(std::bind(&RouteMQProduce::OnConnectError, g_nsq_producer, std::placeholders::_1));
    std::string inner_addr = GetEth0IPAddr();
    std::string nsqd_tcp_addr = inner_addr + std::string(":8514");
    client.ConnectToNSQDs(nsqd_tcp_addr);
    
    AKCS::Singleton<SystemMonitor>::instance().Init(&client);
    nsq_loop.Run();
}

//msg不需要再判断nsq的响应消息，见: NSQConn::OnMessage
int OnRouteMQMessage(const evnsq::Message* msg)
{
    AK_LOG_INFO << "Received a message, id=" << msg->id << " message=[" << msg->body.ToString() << "]";
    return 0;
}

void RouteMQProduce::OnConnectError(const std::string& addr)
{   
    nsq_status_ = false;
    AK_LOG_WARN << "Connect nsqd-" << addr << " error";
}

void RouteMQProduce::OnNSQReady()
{
    nsq_status_ = true;
    AK_LOG_INFO << "Connect nsqd ready";
}

bool RouteMQProduce::OnPublish(CAkcsPdu& pdu, const std::string& topic)
{
    const std::string msg(pdu.GetBuffer(), pdu.GetLength());
    if (!client_->Publish(topic, msg))
    {
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csconfig", "NSQD publish msg error", AKCS_MONITOR_ALARM_NSQD_PUBLISH_CSADAPT);
        return false;
    }
    return true;
}


