#include <stdio.h>
#include "IPCControl.h"
#include <stdlib.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <errno.h>
#include <assert.h>
#include <string.h>
#include <ctype.h>
#include <string>
#include "util_cstring.h"
#include "AdaptDef.h"
#include "AKCSMsg.h"
#include "AkLogging.h"

#include "UnixSocketControl.h"
#include "AKCSView.h"
#include "AdaptMQProduce.h"
#include "AK.Server.pb.h"
#include "AK.Linker.pb.h"
#include "AkcsMsgDef.h"
#include "util.h"
#include "AkcsCommonDef.h"
#include "AK.BackendCommon.pb.h"
#include "BackendP2PMsgControl.h"
#include "dbinterface/ProjectInfo.h"


#define IPC_RECONNECT_INTERVAL  1000
#define IPC_SELECT_TIMEOUT      2000

extern CSADAPT_CONF gstCSADAPTConf;
extern RouteMQProduce* g_nsq_producer;

CIPCControl* GetIPCControlInstance()
{
    return CIPCControl::GetInstance();
}

CIPCControl::CIPCControl()
{

}
CIPCControl::~CIPCControl()
{

}

CIPCControl* CIPCControl::instance = NULL;

CIPCControl* CIPCControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CIPCControl();
    }

    return instance;
}

//个人终端用户,发送请求设备状态的UDP消息给csmain进程
int CIPCControl::SendPersonalReportStatus(std::string strMac)
{
    AK::Server::P2PAdaptReportStatusMsg msg;
    msg.set_mac(strMac);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PER_SEND_REPORT_STATUS);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//发送远程重启设备的消息给csmain进程
int CIPCControl::SendRebootDev(CSP2A_REBOOT_DEVICE* pstDeviceNetInfo)
{
    if (NULL == pstDeviceNetInfo)
    {
        AK_LOG_WARN << "param is null";
        return -1;
    }

    AK::Server::P2PAdaptRebootDevMsg msg;
    msg.set_mac(pstDeviceNetInfo->szMac);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_REBOOT_DEVICE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//发送远程重置设备的消息给csmain进程
int CIPCControl::SendResetDev(CSP2A_REBOOT_DEVICE* pstDeviceNetInfo)
{
    if (NULL == pstDeviceNetInfo)
    {
        AK_LOG_WARN << "param is null";
        return -1;
    }

    AK::Server::P2PAdaptResetDevMsg msg;
    msg.set_mac(pstDeviceNetInfo->szMac);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_RESET_DEVICE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int CIPCControl::GetDevConfigure(CSP2A_FROM_DEVICE_CONFIGURE* pstDeviceConf, unsigned int nSeq)
{
#if 0

    if (NULL == pstDeviceConf)
    {
        AK_LOG_WARN << "param is null";
        return -1;
    }
    int nRet = -1;
    //请求序列号放在IPC_MSG ipcMsg.param1
    nRet = SendMsg(MSG_C2S_CONFIGURE_FROM_DEVICE, nSeq, 0, pstDeviceConf, sizeof(CSP2A_FROM_DEVICE_CONFIGURE));
    if (nRet < 0)
    {
        AK_LOG_WARN << "SendMsg to csmain failed.";
        return -1;
    }
#endif
    return 0;
}


//个人终端用户,客户端请求修改同一联动单元的设备或者app的配置信息
int CIPCControl::SendPerAlarmDeal(const CSP2A_PERSONNAL_DEAL_ALARM* pstAlarmDealInfo)
{
    if (NULL == pstAlarmDealInfo)
    {
        AK_LOG_WARN << "param is null";
        return -1;
    }
    AK::Server::GroupPerAlarmDealMsg msg;
    msg.set_node(pstAlarmDealInfo->szAreaNode);
    msg.set_alarm_id(pstAlarmDealInfo->szAlarmID);
    msg.set_deal_user(pstAlarmDealInfo->szUser);
    msg.set_deal_result(pstAlarmDealInfo->szResult);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_NOTIFY_PERSONAL_ALARM_DEAL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);

    return 0;
}

//社区用户,发送警告处理
int CIPCControl::SendCommunityAlarmDeal(const CSP2A_COMMUNITY_DEAL_ALARM* pstAlarmDealInfo)
{
    if (NULL == pstAlarmDealInfo)
    {
        AK_LOG_WARN << "param is null";
        return -1;
    }
    AK::Server::GroupPerAlarmDealMsg msg;
    msg.set_node(pstAlarmDealInfo->szAreaNode);
    msg.set_alarm_id(pstAlarmDealInfo->szAlarmID);
    msg.set_deal_user(pstAlarmDealInfo->szUser);
    msg.set_deal_result(pstAlarmDealInfo->szResult);
    msg.set_deal_time(pstAlarmDealInfo->szResult);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_NOTIFY_COMMUNITY_ALARM_DEAL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//个人终端用户,发送请求设备注销sip的tcp消息给csmain进程
int CIPCControl::SendPerDevLogOutSip(const std::string& strMac)
{
    AK::Server::P2PAdaptDevLogOutMsg msg;
    msg.set_macs(strMac);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PER_SEND_DEL_DEV);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//个人终端用户,发送请求设备注销sip的tcp消息给csmain进程
int CIPCControl::SendPerUidLogOutSip(const std::string& strUid)
{
    AK::Server::P2PAdaptUidLogOutMsg msg;
    msg.set_uids(strUid);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PER_SEND_DEL_UID);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//个人终端用户,发送有最新待发送文本消息的tcp消息给csmain进程
int CIPCControl::SendPerMessage()
{
    while (1)
    {
        PerMsgSendList text_messages;
        int finish = dbinterface::Message::GetTextMsgSendList(text_messages);//从数据库读取未发送出去的多条消息
        if (finish)
        {
            return 0;
        }
        int count = 0;
        for (auto& text_send : text_messages)//可能有多条消息
        {
            snprintf(text_send.text_message.from, sizeof(text_send.text_message.from), "%s", "Security Center");
            AK::Server::GroupAdaptTextMsg msg;
            msg.add_node_list(text_send.account);
            msg.set_client_type(text_send.client_type);
            msg.set_title(text_send.text_message.title);
            msg.set_content(text_send.text_message.content);
            msg.set_time(text_send.text_message.time);
            msg.set_from(text_send.text_message.from);
            msg.set_to(text_send.text_message.to);//
            msg.set_id(text_send.text_message.id);
            msg.set_type(text_send.text_message.type);
            
            CAkcsPdu pdu;
            pdu.SetMsgBody(&msg);
            pdu.SetHeadLen(sizeof(PduHeader_t));
            pdu.SetVersion(50);
            pdu.SetCommandId(MSG_C2S_PER_SEND_TEXT_MSG);
            pdu.SetSeqNum(0);
            g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);

            if (text_send.client_type == PersoanlMessageSend::TextClientType::APP_SEND)
            {
                //家居text通知
                LINKER_NORMAL_MSG linker_msg;
                memset(&linker_msg, 0, sizeof(linker_msg));
                ProjectInfo project(text_send.account, linker_msg);
                PushLinKerText(text_send, linker_msg);
            }

            ++count;
            if (count == 50)
            {
                usleep(100 * 1000);
                count = 0;
            }
        }     
    }
    return 0;
}

//个人终端用户,发送新建用户的邮箱信息给csmain进程,csmain再发送给cspush
int CIPCControl::SendPerCreateUidMail(CSP2A_USER_CREATE_INFO* pstUserCreateInfo)
{
    AK::Server::P2PAdaptCreateUidMailMsg msg;
    msg.set_user(pstUserCreateInfo->szUser);
    msg.set_pwd(pstUserCreateInfo->szPwd);
    msg.set_email(pstUserCreateInfo->szEmail);
    msg.set_qrcode_body(pstUserCreateInfo->szQRCodeBody);
    msg.set_qrcode_url(pstUserCreateInfo->szQRCodeUrl);
    msg.set_srv_web_url(pstUserCreateInfo->szServerWebUrl);
    msg.set_is_fake(pstUserCreateInfo->is_fake);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PER_SEND_CREATE_UID_MAIL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//个人终端用户,发送新建用户的邮箱信息给csmain进程,csmain再发送给cspush
int CIPCControl::SendPerCreateUidMailToMaster(CSP2A_USER_CREATE_INFO* pstUserCreateInfo)
{
    AK::Server::P2PAdaptCreateUidMailMsg msg;
    //主账号uid
    msg.set_user(pstUserCreateInfo->szUser);
    //从账号uid
    msg.set_user_uid(pstUserCreateInfo->user_uid);
    msg.set_pwd(pstUserCreateInfo->szPwd);
    msg.set_email(pstUserCreateInfo->szEmail);
    msg.set_qrcode_body(pstUserCreateInfo->szQRCodeBody);
    msg.set_qrcode_url(pstUserCreateInfo->szQRCodeUrl);
    msg.set_srv_web_url(pstUserCreateInfo->szServerWebUrl);
    msg.set_to_master(SEND_TO_MASTER);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PER_SEND_CREATE_UID_MAIL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//界面上用户忘记密码,csadapt通知csmain,发送相关信息到用户邮箱
int CIPCControl::SendPerResetPwdMail(CSP2A_USER_EAMIL_INFO* pstUserEmailInfo)
{
    AK::Server::P2PAdaptResetPwdMailMsg msg;
    msg.set_web_ip(pstUserEmailInfo->szWebIP);
    msg.set_user(pstUserEmailInfo->szUser);
    msg.set_email(pstUserEmailInfo->szEmail);
    msg.set_token(pstUserEmailInfo->szToken);
    msg.set_role_type(pstUserEmailInfo->szRoleType);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PER_SEND_RESET_PWD_MAIL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//界面上用户忘记密码,csadapt通知csmain,发送相关信息到用户邮箱
int CIPCControl::SendPerResetPwdMailToMaster(CSP2A_USER_EAMIL_INFO* pstUserEmailInfo)
{
    AK::Server::P2PAdaptResetPwdMailMsg msg;
    msg.set_web_ip(pstUserEmailInfo->szWebIP);
    msg.set_user(pstUserEmailInfo->szUser);
    msg.set_email(pstUserEmailInfo->szEmail);
    msg.set_token(pstUserEmailInfo->szToken);
    msg.set_role_type(pstUserEmailInfo->szRoleType);
    msg.set_to_master(SEND_TO_MASTER);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PER_SEND_RESET_PWD_MAIL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//界面上用户修改密码,csadapt通知csmain,发送相关信息到用户邮箱
int CIPCControl::SendPerChangePwdMail(CSP2A_USER_CREATE_INFO* pstUserCreateInfo)
{
    AK::Server::P2PAdaptPerChangePwdMailMsg msg;
    msg.set_user(pstUserCreateInfo->szUser);
    msg.set_pwd(pstUserCreateInfo->szPwd);
    msg.set_email(pstUserCreateInfo->szEmail);
    msg.set_qrcode_body(pstUserCreateInfo->szQRCodeBody);
    msg.set_qrcode_url(pstUserCreateInfo->szQRCodeUrl);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PER_SEND_CHANGE_PWD_MAIL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int CIPCControl::SendPerChangePwdMailToMaster(CSP2A_USER_CREATE_INFO* pstUserCreateInfo)
{
    AK::Server::P2PAdaptPerChangePwdMailMsg msg;
    msg.set_user(pstUserCreateInfo->szUser);
    msg.set_user_uid(pstUserCreateInfo->user_uid);
    msg.set_pwd(pstUserCreateInfo->szPwd);
    msg.set_email(pstUserCreateInfo->szEmail);
    msg.set_qrcode_body(pstUserCreateInfo->szQRCodeBody);
    msg.set_qrcode_url(pstUserCreateInfo->szQRCodeUrl);
    msg.set_to_master(SEND_TO_MASTER);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PER_SEND_CHANGE_PWD_MAIL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}


//个人自己注册账号,csadapt通知csmain,发送相关信息到用户邮箱
int CIPCControl::SendPerCheckCodeMail(CSP2A_SEND_CHECK_CODE* pstSendCheckCode)
{
    AK::Server::P2PAdaptPerCheckCodeMailMsg msg;
    msg.set_check_code(pstSendCheckCode->szCheckCode);
    msg.set_email(pstSendCheckCode->szEmail);
    msg.set_language(pstSendCheckCode->szLanguage);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PER_SEND_CHECK_CODE_MAIL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;

}

//app过期了
int CIPCControl::SendAppExpire(const CSP2A_DEV_APP_EXPIRE* pstExpire)
{
    AK::Server::GroupAdaptDevAppExpireMsg msg;
    //目前只有一个,php脚本传过来是一个个传的.后续需要修改成流式接口
    AK::Server::GroupAdaptDevAppExpireMsg::GroupAdaptAppExpireInnerMsg* inner_msg = msg.add_expire_uid_list();
    inner_msg->set_user_name(pstExpire->szUserName);
    inner_msg->set_email(pstExpire->szEmail);
    inner_msg->set_community(pstExpire->community);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_DEV_APP_EXPIRE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//设备app即将过期了
int CIPCControl::SendDevAppWillBeExpire(const CSP2A_DEV_APP_WILLBE_EXPIRE* pstExpire)
{
    AK::Server::P2PAdaptDevAppWillBeExpireMsg msg;
    msg.set_user_name(pstExpire->szUserName);
    msg.set_email(pstExpire->szEmail);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_DEV_APP_WILL_BE_EXPIRE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}


//设备未过期
int CIPCControl::SendDevNotExpire(const CSP2A_DEV_NOT_EXPIRE* pstExpire)
{
    AK::Server::GroupAdaptDevNotExpireMsg msg;
    msg.set_uids(pstExpire->szUids);
    msg.set_macs(pstExpire->szMacs);
    msg.set_node(pstExpire->szNode);
    msg.set_type(pstExpire->nType);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_DEV_NOT_EXPIRE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//清空设备码
int CIPCControl::SendDevCleanDeviceCode(const CSP2A_DEV_CLEAN_DEVICE_CODE* pstExpire)
{
    AK::Server::P2PAdaptDevCleanDeviceCodeMsg msg;
    msg.set_macs(pstExpire->szMacs);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_DEV_CLEAN_DEV_CODE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}
//added by chenyc, 2018-08-27,for 视频存储
int CIPCControl::SendAddVsSched(CSP2A_ADD_VIDEO_STORAGE_SCHED* pstVsSchedulInfo)
{
    AK::Server::GroupAdaptAddVsSchedMsg msg;
    msg.set_id(pstVsSchedulInfo->id);
    msg.set_sched_type(pstVsSchedulInfo->sched_type);
    msg.set_date_flag(pstVsSchedulInfo->date_flag);
    msg.set_mac(pstVsSchedulInfo->mac);
    msg.set_begin_time(pstVsSchedulInfo->begin_time);
    msg.set_end_time(pstVsSchedulInfo->end_time);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_ADD_VIDEO_STORAGE_SCHED);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int CIPCControl::SendDelVsSched(const CSP2A_DEL_VIDEO_STORAGE_SCHED* pstVsSchedInfo)
{
    AK::Server::GroupAdaptDelVsSchedMsg msg;
    msg.set_id(pstVsSchedInfo->id);
    msg.set_sched_type(pstVsSchedInfo->sched_type);
    msg.set_mac(pstVsSchedInfo->mac);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_DEL_VIDEO_STORAGE_SCHED);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int CIPCControl::SendDelVs(const CSP2A_DEL_VIDEO_STORAGE* pstVs)
{
    AK::Server::P2PAdaptDelVsMsg msg;
    msg.set_video_id(pstVs->video_id);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_DEL_VIDEO_STORAGE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//修改设备，要通知csmain更新内存数据
int CIPCControl::SendDevChange(CSP2A_DEVICE_CHANGE_INFO* pstDevChange)
{
    AK::Server::P2PAdaptDevChangeMsg msg;
    msg.set_mac_id(pstDevChange->nMacid);
    msg.set_is_per(pstDevChange->nIsPer);
    msg.set_mac(pstDevChange->szMac);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_DEV_CHANGE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//账号是否激活
int CIPCControl::SendAccountActiveEmail(const CSP2A_ACCOUNT_ACTIVE_INFO* account_info)
{
    AK::Server::P2PAdaptAccountActInfoMsg msg;
    msg.set_active(account_info->nActive);
    msg.set_user_name(account_info->szUserName);
    msg.set_email(account_info->szEmail);
    msg.set_web_url(account_info->szServerWebUrl);
    msg.set_time(account_info->expire_time);
    msg.set_subscription(account_info->subscription);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_ACCOUNT_ACTIVE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int CIPCControl::SendPmAccountActiveEmail(const CSP2A_ACCOUNT_ACTIVE_INFO* account_info)
{
    AK::Server::P2PAdaptPmAccountActInfoMsg msg;
    msg.set_account(account_info->account);
    msg.set_active(account_info->nActive);
    msg.set_user_name(account_info->szUserName);
    msg.set_email(account_info->szEmail);
    msg.set_web_url(account_info->szServerWebUrl);
    msg.set_time(account_info->expire_time);
    msg.set_subscription(account_info->subscription);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PM_ACCOUNT_ACTIVE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//分享key
int CIPCControl::SendShareTmpkeyEmail(const CSP2A_SHARE_TEMKEY_INFO* pstShareTempkey)
{
    AK::Server::P2PAdaptTmpKeyInfoMsg msg;
    msg.set_tmp_key(pstShareTempkey->szTmpKey);
    msg.set_email(pstShareTempkey->szEmail);
    msg.set_msg(pstShareTempkey->szMsg);
    msg.set_count_every(pstShareTempkey->szCountOrEvery);
    msg.set_start_time(pstShareTempkey->szStartTime);
    msg.set_stop_time(pstShareTempkey->szStopTime);
    msg.set_qrcode_body(pstShareTempkey->szQRCodeBody);
    msg.set_web_url(pstShareTempkey->szWebUrl);
    msg.set_language(pstShareTempkey->szLanguage);
    msg.set_mng_id(pstShareTempkey->mng_id);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_SHARE_TMPKEY);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

/*
//remote opendoor
int CIPCControl::SendRemoteOpenDoor(const CSP2A_REMOTE_OPENDDOR_INFO* pstRemoteOpenDoor)
{
    AK::Server::P2PAdaptRemoteOpenDoorMsg msg;
    msg.set_mac(pstRemoteOpenDoor->mac);
    msg.set_uid(pstRemoteOpenDoor->uid);
    msg.set_relay(pstRemoteOpenDoor->relay);
    msg.set_msg_traceid(pstRemoteOpenDoor->trace_id);
    msg.set_repost_mac(pstRemoteOpenDoor->repost_mac);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_REMOTE_OPENDOOR);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}
*/

//创建物业
int CIPCControl::SendCreatePropertyWork(const CSP2A_CREATE_PROPERTY_WORK_INFO* pstCreateProperty)
{
    AK::Server::P2PAdaptCreatePropertyWorkMsg msg;
    msg.set_email(pstCreateProperty->szEmail);
    msg.set_user_name(pstCreateProperty->szUserName);
    msg.set_pwd(pstCreateProperty->szPassword);
    msg.set_srv_web_url(pstCreateProperty->szServerWebUrl);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_CREATE_PROPERTY_WORK);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}
//enduser续费成功
int CIPCControl::SendRenewServerEmail(const std::vector<CSP2A_UID_RENEW_SRV_INFO>& uid_infos)
{
    AK::Server::P2PAdaptRenewSrvMsg msg;

    for (const auto& uid_info : uid_infos)
    {
        AK::Server::P2PAdaptRenewSrvMsg::P2PAdaptRenewSrvInnerMsg* inner_msg = msg.add_renew_srv_uid_list();
        inner_msg->set_user_name(uid_info.szUserName);
        inner_msg->set_email(uid_info.szEmail);
        inner_msg->set_time(uid_info.szTime);
        inner_msg->set_type(uid_info.type);
    }
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_RENEW_SERVER);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//pm app续费操作
int CIPCControl::SendPmRenewServerEmail(const CSP2A_UID_PM_RENEW_SRV_INFO& uid_info)
{
    AK::Server::P2PAdaptPmRenewSrvMsg msg;
    msg.set_type(uid_info.type);
    msg.set_time(uid_info.time);
    msg.set_uid(uid_info.account);
    msg.set_email(uid_info.email);
    msg.set_user_name(uid_info.username);
    
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PM_RENEW_SERVER);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//通知PM社区账号多少过期
int CIPCControl::SendPmEmail(const CSP2A_PM_INFO* pstPmInfo)
{
    AK::Server::P2PAdaptPMAccountWillExpireMsg msg;
    msg.set_community(pstPmInfo->szCommunity);
    msg.set_email(pstPmInfo->szEmail);
    msg.set_pm_name(pstPmInfo->szName);
    msg.set_account_num(pstPmInfo->nAccountNum);
    msg.set_before(pstPmInfo->nBefore);
    msg.set_list(pstPmInfo->list);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PM_WILL_EXPIRE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}


int CIPCControl::SendAlexaLogin(CSP2A_ALEXA_LOGIN_INFO* pst_alexa_login)
{
    AK::Server::GroupAdaptAlexaLoginMsg msg;
    msg.set_node(pst_alexa_login->node);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_ALEXA_LOGIN_MSG);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int CIPCControl::SendAlexaSetArming(CSP2A_ALEXA_SET_ARMING_INFO* pst_alexa_set_arming)
{
    AK::Server::P2PAdaptAlexaSetArmingMsg msg;
    msg.set_mac(pst_alexa_set_arming->mac);
    msg.set_mode(pst_alexa_set_arming->mode);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_ALEXA_SET_ARMING_MSG);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//落地过期了
int CIPCControl::SendPhoneExpire(const CSP2A_DEV_APP_EXPIRE* pstExpire)
{
    AK::Server::P2PAdaptPhoneExpireMsg msg;
    msg.set_user_name(pstExpire->szUserName);
    msg.set_email(pstExpire->szEmail);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PHONE_EXPIRE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//落地即将过期了
int CIPCControl::SendPhoneWillExpire(const CSP2A_DEV_APP_EXPIRE* pstExpire)
{
    AK::Server::P2PAdaptPhoneWillExpireMsg msg;
    msg.set_user_name(pstExpire->szUserName);
    msg.set_email(pstExpire->szEmail);
    msg.set_before(pstExpire->nBefore);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PHONE_WILL_EXPIRE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//通知Installer落地即将过期了
int CIPCControl::SendInstallerPhoneWillExpire(const CSP2A_INSTALLER_EXPIRE* pstExpire)
{
    AK::Server::P2PAdaptInstallerPhoneWillExpireMsg msg;
    msg.set_user_name(pstExpire->szUserName);
    msg.set_email(pstExpire->szEmail);
    msg.set_count(pstExpire->nCount);
    msg.set_before(pstExpire->nBefore);
    msg.set_list(pstExpire->list);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_INSTALLER_PHONE_WILL_EXPIRE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//通知Installer账号即将过期了
int CIPCControl::SendInstallerAppWillExpire(const CSP2A_INSTALLER_EXPIRE* pstExpire, std::string community)
{
    AK::Server::P2PAdaptInstallerAppWillExpireMsg msg;
    msg.set_user_name(pstExpire->szUserName);
    msg.set_email(pstExpire->szEmail);
    msg.set_count(pstExpire->nCount);
    msg.set_community(community);
    msg.set_before(pstExpire->nBefore);
    msg.set_list(pstExpire->list);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_INSTALLER_APP_WILL_EXPIRE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//通知PM高级功能即将过期了
int CIPCControl::SendPMFeatureWillExpire(const CSP2A_PM_EXPIRE* pstExpire)
{
    AK::Server::P2PAdaptPmFeatureWillExpireMsg msg;
    msg.set_user_name(pstExpire->username);
    msg.set_email(pstExpire->email);
    msg.set_before(pstExpire->nbefore);
    msg.set_location(pstExpire->community);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PM_FEATURE_WILL_EXPIRE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//通知Installer高级功能即将过期了
int CIPCControl::SendInstallerFeatureWillExpire(const CSP2A_INSTALLER_EXPIRE* pstExpire)
{
    AK::Server::P2PAdaptInstallerFeatureWillExpireMsg msg;
    msg.set_user_name(pstExpire->szUserName);
    msg.set_email(pstExpire->szEmail);
    msg.set_before(pstExpire->nBefore);
    msg.set_location(pstExpire->community);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_INSTALLER_FEATURE_WILL_EXPIRE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}



//创建远程连接
int CIPCControl::SendCreateRemoteDevContorl(CSP2A_CREATE_REMOTE_DEV_CONTORL_INFO& info)
{
    AK::Server::P2PAdaptCreateRemoteDevContorlMsg msg;
    msg.set_user(info.user);
    msg.set_password(info.password);
    msg.set_port(info.port);
    msg.set_mac(info.mac);
    msg.set_ssh_proxy_domain(info.ssh_proxy_domain);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_CREATE_REMOTE_DEV_CONTORL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//通知csmain刷新conn缓存
int CIPCControl::NotifyRefreshConnCache(CSP2A_REFRESH_CACHE& info)
{
    AK::Server::GroupAdaptNotifyRefreshConnCache msg;
    msg.set_mac(info.mac);
    msg.set_node(info.node);
    msg.set_type(info.type);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_REFRESH_CONN_CACHE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//csadapt通知csmain,发送keysend
int CIPCControl::SendDevFileChange(CSP2A_DEV_FILE_CHANGE* dev_change)
{
    AK::Server::P2PAdaptNotifyFileChangeMsg msg;
    msg.set_mac(dev_change->mac);
    msg.set_type(dev_change->type);
    msg.set_msg_traceid(dev_change->traceid);
    msg.set_file_path(dev_change->file_path);
    msg.set_file_md5(dev_change->file_md5);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_NOTIFY_FILE_CHANGE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//通知PM社区账号多少过期
int CIPCControl::SendPmAppAccountWillBeExpireEmail(const CSP2A_PM_INFO* pstPmInfo)
{
    AK::Server::P2PAdaptPMAppAccountWillExpireMsg msg;
    msg.set_community(pstPmInfo->szCommunity);
    msg.set_email(pstPmInfo->szEmail);
    msg.set_pm_name(pstPmInfo->szName);
    msg.set_account_num(pstPmInfo->nAccountNum);
    msg.set_before(pstPmInfo->nBefore);
    msg.set_list(pstPmInfo->list);
    AK_LOG_INFO << "Send SendPmAppAccountWillBeExpireEmail=" << msg.DebugString();
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PM_APP_ACCOUNT_WILL_EXPIRE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

//通知PM社区账号多少过期
int CIPCControl::SendPmAppAccountExpireEmail(const CSP2A_PM_INFO* pstPmInfo)
{
    AK::Server::P2PAdaptPMAppAccountExpireMsg msg;
    msg.set_community(pstPmInfo->szCommunity);
    msg.set_email(pstPmInfo->szEmail);
    msg.set_pm_name(pstPmInfo->szName);
    msg.set_account_num(pstPmInfo->nAccountNum);
    msg.set_list(pstPmInfo->list);
    AK_LOG_INFO << "Send SendPmAppAccountExpireEmail=" << msg.DebugString();
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PM_APP_ACCOUNT_EXPIRE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int CIPCControl::PushLinKerText(const PersoanlMessageSend& text_msg, const LINKER_NORMAL_MSG &linker_msg)
{
    Json::Value item;
    Json::FastWriter w;
    FormatLinkerJsonData(linker_msg, item);
    item["title"] = text_msg.text_message.title;
    item["content"] = text_msg.text_message.content;
    item["notice_name"] = text_msg.text_message.from;
    std::string data_json = w.write(item);
    SendLinKerCommonMsg(LinkerPushMsgType::LINKER_MSG_TYPE_MESSAGE, data_json, linker_msg.account_uuid);
    return 0;
}

void CIPCControl::FormatLinkerJsonData(const LINKER_NORMAL_MSG &linker_msg, Json::Value &item)
{
    item["dev_uuid"] = linker_msg.dev_uuid;
    item["dev_name"] = linker_msg.dev_name;
    item["dev_type"] = linker_msg.dev_type;
    item["dev_grade"] = linker_msg.dev_grade;
    item["account_uuid"] = linker_msg.account_uuid;
    item["node_uuid"] = linker_msg.node_uuid;
    item["account_name"] = linker_msg.account_name;
    item["language"] = linker_msg.language;
    item["project_type"] = linker_msg.project_type;
    item["project_uuid"] = linker_msg.project_uuid;
    std::time_t t = std::time(0);
    item["timestamp"] = GetCurrentMilliTimeStamp();
    item["ins_uuid"] = linker_msg.ins_uuid;
    item["enable_smarthome"] = linker_msg.enable_smarthome;
}

int CIPCControl::SendLinKerCommonMsg(int msg_type, const std::string &data_json, const std::string &key)
{
    AK::Linker::P2PRouteLinker msg;
    msg.set_message_type(msg_type);
    msg.set_msg_json(data_json);
    msg.set_key(key);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_M2R_GROUP_PUSH_CSLINKER_COMMON_MSG);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int CIPCControl::SendUserAddNewSite(const CSP2A_PER_ADD_NEWSITE& per_add_new_site)
{
    AK::Server::P2PSendUserAddNewSite msg;
    msg.set_name(per_add_new_site.name);
    msg.set_email(per_add_new_site.email);
    msg.set_project_name(per_add_new_site.project_name);
    msg.set_apt_num(per_add_new_site.apt_num);
    msg.set_send_type(per_add_new_site.send_type);
    msg.set_role(per_add_new_site.role);
    AK_LOG_INFO << "SendUserAddNewSite=" << msg.DebugString();

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_SEND_USER_ADD_NEWSITE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);    
    return 0;
}

int CIPCControl::SendPmWebLinkNewSites(const CSP2A_PM_LINK_NEWSITES& pm_link_new_sites)
{
    AK::Server::P2PSendPmWebLinkNewSites msg;
    msg.set_comm_name_list(pm_link_new_sites.comm_name_list);
    msg.set_office_name_list(pm_link_new_sites.office_name_list);
    msg.set_email(pm_link_new_sites.email);
    msg.set_name(pm_link_new_sites.name);
    AK_LOG_INFO << "SendPmWebLinkNewSites=" << msg.DebugString();

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_SEND_PM_WEB_LINK_NEWSITES);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int CIPCControl::SendPmWebCreateUidMail(const CSP2A_USER_CREATE_INFO& user_create_info)
{
    AK::Server::P2PAdaptCreateUidMailMsg msg;
    msg.set_user(user_create_info.szUser);
    msg.set_pwd(user_create_info.szPwd);
    msg.set_email(user_create_info.szEmail);
    msg.set_qrcode_body(user_create_info.szQRCodeBody);
    msg.set_qrcode_url(user_create_info.szQRCodeUrl);
    msg.set_srv_web_url(user_create_info.szServerWebUrl);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PM_WEB_CREATE_UID_MAIL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int CIPCControl::SendPmWebChangePwdMail(const CSP2A_USER_CREATE_INFO& user_create_info)
{
    AK::Server::P2PAdaptPerChangePwdMailMsg msg;
    msg.set_user(user_create_info.szUser);
    msg.set_pwd(user_create_info.szPwd);
    msg.set_email(user_create_info.szEmail);
    msg.set_qrcode_body(user_create_info.szQRCodeBody);
    msg.set_qrcode_url(user_create_info.szQRCodeUrl);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PM_WEB_CHANGE_UID_MAIL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int CIPCControl::SendCommonEmailCode(const CSP2A_SEND_VERFICATION_CODE& verification_code)
{
    AK::Server::P2PSendCodeToEmail send_email_code;
    send_email_code.set_name(verification_code.name);
    send_email_code.set_email(verification_code.email);
    send_email_code.set_language(verification_code.language);
    send_email_code.set_code(verification_code.code);
    send_email_code.set_type(verification_code.type);
    AK_LOG_INFO << "SendCommonEmailCode=" << send_email_code.DebugString();

    CAkcsPdu pdu;
    pdu.SetMsgBody(&send_email_code);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_SEND_CODE_TO_EMAIL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int CIPCControl::SendCommonSmsCode(const CSP2A_SEND_VERFICATION_CODE& verification_code)
{
    AK::Server::P2PSendCodeToMobile send_sms_code;
    send_sms_code.set_area_code(verification_code.phone_code);
    send_sms_code.set_phone(verification_code.mobile_number);
    send_sms_code.set_language(verification_code.language);
    send_sms_code.set_code(verification_code.code);
    send_sms_code.set_type(verification_code.type);
    AK_LOG_INFO << "SendCommonSmsCode=" << send_sms_code.DebugString();

    CAkcsPdu pdu;
    pdu.SetMsgBody(&send_sms_code);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_SEND_CODE_TO_MOBILE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);    
    return 0;
}

void CIPCControl::SendRequestDevDelLog(const std::string& mac)
{
    AK::Server::P2PSendRequestDevDelLog msg;
    msg.set_mac(mac);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_REQUEST_DEV_DEL_LOG);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);    
}

