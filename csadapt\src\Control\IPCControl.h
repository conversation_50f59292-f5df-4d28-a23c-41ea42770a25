#ifndef __IPC_CONTROL_H__
#define __IPC_CONTROL_H__

#include "stdint.h"
#include "json/json.h"
#include "dbinterface/Message.h"
#include "dbinterface/OfficeMessage.h"
#include "AkcsCommonSt.h"
#include "AkcsWebMsgSt.h"



typedef struct CSP2A_REBOOT_DEVICE_T  CSP2A_REBOOT_DEVICE;

//modified by chenyc,2019-03-05, 由原先的跟csmain的直接通信改成经过csroute,ipc的名称暂时不变.
class CIPCControl
{
public:
    CIPCControl();
    virtual ~CIPCControl();
    static CIPCControl* GetInstance();

    //发送重启设备请求给csmain模块
    int SendRebootDev(CSP2A_REBOOT_DEVICE* pstDeviceNetInfo);
    int SendResetDev(CSP2A_REBOOT_DEVICE* pstDeviceNetInfo);
    int GetDevConfigure(CSP2A_FROM_DEVICE_CONFIGURE* pstDeviceConf, unsigned int nSeq);
    //个人终端用户,客户端请求修改同一联动单元的设备或者app的配置信息
    int SendPerAlarmDeal(const CSP2A_PERSONNAL_DEAL_ALARM* pstAlarmDealInfo);
    //社区警告被处理通知
    int SendCommunityAlarmDeal(const CSP2A_COMMUNITY_DEAL_ALARM* pstAlarmDealInfo);
    //个人终端用户,发送请求设备状态的UDP消息给csmain进程
    int SendPersonalReportStatus(std::string strMac);
    int SendPerDevLogOutSip(const std::string& strMac);
    int SendPerUidLogOutSip(const std::string& strUid);
    int SendPerMessage();
    int SendPerResetPwdMail(CSP2A_USER_EAMIL_INFO* pstUserEmailInfo);
    int SendPerCreateUidMail(CSP2A_USER_CREATE_INFO* pstUserCreateInfo);
    int SendPerChangePwdMail(CSP2A_USER_CREATE_INFO* pstUserCreateInfo);

    //个人注册账号发送校验码
    int SendPerCheckCodeMail(CSP2A_SEND_CHECK_CODE* pstSendCheckCode);
    int SendAppExpire(const CSP2A_DEV_APP_EXPIRE* pstExpire);

    int SendDevNotExpire(const CSP2A_DEV_NOT_EXPIRE* pstExpire);
    int SendDevCleanDeviceCode(const CSP2A_DEV_CLEAN_DEVICE_CODE* pstExpire);
    int SendDevAppWillBeExpire(const CSP2A_DEV_APP_WILLBE_EXPIRE* pstExpire);
    int SendAddVsSched(CSP2A_ADD_VIDEO_STORAGE_SCHED* pstVsSchedulInfo);//csroute需要广播给所有的csmain的...
    int SendDelVsSched(const CSP2A_DEL_VIDEO_STORAGE_SCHED* pstVsSchedInfo);
    int SendDelVs(const CSP2A_DEL_VIDEO_STORAGE* pstVs);
    int SendDevChange(CSP2A_DEVICE_CHANGE_INFO* pstDevChange);
    int SendShareTmpkeyEmail(const CSP2A_SHARE_TEMKEY_INFO* pstShareTempkey);
    // int SendRemoteOpenDoor(const CSP2A_REMOTE_OPENDDOR_INFO* pstRemoteOpenDoor);
    int SendCreatePropertyWork(const CSP2A_CREATE_PROPERTY_WORK_INFO* pstCreateProperty);
    int SendAccountActiveEmail(const CSP2A_ACCOUNT_ACTIVE_INFO* pstExpire);
    //v4.5
    int SendRenewServerEmail(const std::vector<CSP2A_UID_RENEW_SRV_INFO>& uid_infos);
    int SendPmRenewServerEmail(const CSP2A_UID_PM_RENEW_SRV_INFO& uid_info);
    int SendPmEmail(const CSP2A_PM_INFO* pstPmInfo);

    int SendAlexaLogin(CSP2A_ALEXA_LOGIN_INFO* pst_alexa_login);
    int SendAlexaSetArming(CSP2A_ALEXA_SET_ARMING_INFO* pst_alexa_set_arming);

    int SendPhoneExpire(const CSP2A_DEV_APP_EXPIRE* pstExpire);
    int SendPhoneWillExpire(const CSP2A_DEV_APP_EXPIRE* pstExpire);
    int SendInstallerPhoneWillExpire(const CSP2A_INSTALLER_EXPIRE* pstExpire);
    int SendInstallerAppWillExpire(const CSP2A_INSTALLER_EXPIRE* pstExpire, std::string community);
    int SendCreateRemoteDevContorl(CSP2A_CREATE_REMOTE_DEV_CONTORL_INFO& info);
    int NotifyRefreshConnCache(CSP2A_REFRESH_CACHE& info);
    int SendDevFileChange(CSP2A_DEV_FILE_CHANGE* dev_change);

    int SendPMFeatureWillExpire(const CSP2A_PM_EXPIRE* pstExpire);
    int SendInstallerFeatureWillExpire(const CSP2A_INSTALLER_EXPIRE* pstExpire);
    int SendPerCreateUidMailToMaster(CSP2A_USER_CREATE_INFO* pstUserCreateInfo);
    int SendPerResetPwdMailToMaster(CSP2A_USER_EAMIL_INFO* pstUserEmailInfo);
    int SendPmAppAccountWillBeExpireEmail(const CSP2A_PM_INFO* pstPmInfo);
    int SendPmAppAccountExpireEmail(const CSP2A_PM_INFO* pstPmInfo);
    int SendPerChangePwdMailToMaster(CSP2A_USER_CREATE_INFO* pstUserCreateInfo);
    int SendPmAccountActiveEmail(const CSP2A_ACCOUNT_ACTIVE_INFO* account_info);
    int PushLinKerText(const PersoanlMessageSend& text_msg, const LINKER_NORMAL_MSG &linker_msg);
    void FormatLinkerJsonData(const LINKER_NORMAL_MSG &linker_msg, Json::Value &item);
    int SendLinKerCommonMsg(int msg_type, const std::string &data_json, const std::string &key);
    int SendUserAddNewSite(const CSP2A_PER_ADD_NEWSITE& per_add_new_site);
    int SendPmWebLinkNewSites(const CSP2A_PM_LINK_NEWSITES& pm_link_new_sites);
    int SendPmWebCreateUidMail(const CSP2A_USER_CREATE_INFO& user_create_info);
    int SendPmWebChangePwdMail(const CSP2A_USER_CREATE_INFO& user_create_info);
    int SendCommonEmailCode(const CSP2A_SEND_VERFICATION_CODE& verification_code);
    int SendCommonSmsCode(const CSP2A_SEND_VERFICATION_CODE& verification_code);
    void SendRequestDevDelLog(const std::string& mac);
        

private:
    static CIPCControl* instance;
};

CIPCControl* GetIPCControlInstance();
#endif //__IPC_CONTROL_H__

