#include "util.h"
#include "AdaptDef.h"
#include "AkLogging.h"
#include "KafkaConsumerAppBackendTopicHandle.h"
#include "UnixSocketControl.h"

extern CSADAPT_CONF gstCSADAPTConf;

void HandleKafkaNotifyBackendTopicMsg::StartKafkaConsumer()
{
    kafak_.SetParma(
        gstCSADAPTConf.kafka_broker_ip, gstCSADAPTConf.notify_app_backend_topic,
        gstCSADAPTConf.notify_app_backend_group, gstCSADAPTConf.notify_app_backend_thread_num
    );

    kafak_.SetConsumerCb(
        std::bind(
            &HandleKafkaNotifyBackendTopicMsg::HandleKafkaMessage, this, std::placeholders::_1,
            std::placeholders::_2, std::placeholders::_3, std::placeholders::_4
        )
    );

    kafak_.Start();
}


bool HandleKafkaNotifyBackendTopicMsg::HandleKafkaMessage(uint64_t partition, uint64_t offset, const std::string& key, const std::string& org_msg)
{
    GetUnixSocketControlInstance()->AddMsg((char*)org_msg.c_str(), org_msg.length());
    return true;
}
