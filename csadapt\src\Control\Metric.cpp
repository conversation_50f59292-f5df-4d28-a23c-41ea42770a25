#include "Metric.h"
#include "CachePool.h"
#include "AkLogging.h"
#include "EtcdCliMng.h"
#include "AdaptMQProduce.h"
#include "RouteClientMng.h"
#include "ConnectionPool.h"
#include "ConfigFileReader.h"
#include "UnixSocketControl.h"
#include "KafkaConsumerNotifyTopicHandle.h"

extern RouteMQProduce* g_nsq_producer;
extern CAkEtcdCliManager* g_etcd_cli_mng;
extern uint64_t g_openapi_health_check_count;
#define VERSION_CONF_FILE "/usr/local/akcs/csadapt/conf/version.conf"


void InitMetricInstance()
{
    MetricService* metric_service = MetricService::GetInstance();
    if (metric_service == nullptr)
    {
        AK_LOG_WARN << "metric service init failed.";
        return;
    }

    //版本信息
    CConfigFileReader tag_config_file(VERSION_CONF_FILE);
    std::string branch_or_tag_version = tag_config_file.GetConfigName("branch_or_tag");
    static long version_metric = (long)ATOI(branch_or_tag_version.c_str());

    // 添加 metric 指标
    metric_service->AddMetric(
        "db_get_conn_failed_count",
        "DB GetConnection failed count",
        "csadapt_db_get_conn_failed_count",
        nullptr
    );
    metric_service->AddMetric(
        "logdb_get_conn_failed_count",
        "LOGDB GetConnection failed count",
        "csadapt_logdb_get_conn_failed_count",
        nullptr
    );
    metric_service->AddMetric(
        "nsq_check",
        "nsq producer status",
        "csadapt_nsq_check_error",
        []() -> long { return (long)(g_nsq_producer->Status() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "beanstalk_check",
        "beanstalk server status",
        "csadapt_beanstalk_check_error",
        []() -> long { return (long)(GetUnixSocketControlInstance()->CheckBeanstalkStatus() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "beanstalk_backup_check",
        "beanstalk backup server status",
        "csadapt_beanstalk_backup_check_error",
        []() -> long { return (long)(GetUnixSocketControlInstance()->CheckBeanstalkBackUpStatus() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "redis_check",
        "redis server status",
        "csadapt_redis_check_error",
        []() -> long { return (long)(CacheManager::getInstance()->CheckRedisNormal() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "db_conn_check",
        "db conn status",
        "csadapt_db_conn_check_error",
        []() -> long { return (long)(GetDBConnPollInstance()->CheckDBConnNormal() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "kafka_check",
        "kafka server status",
        "csadapt_kafka_check_error",
        []() -> long { return (long)(AKCS::Singleton<HandleKafkaNotifyTopicMsg>::instance().Status() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "csroute_check",
        "route server status",
        "csadapt_csroute_check_error",
        []() -> long { return (long)(CRouteClientMng::Instance()->CheckRouteNormal() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "etcd_check",
        "etcd server status",
        "csadapt_etcd_check_error",
        []() -> long { return (long)(g_etcd_cli_mng->CheckEtcdCliStatus() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "openapi_socket_check_count",
        "openapi socket health check times",
        "csadapt_openapi_socket_check_count",
        []()-> long { return (long)g_openapi_health_check_count; }
    );
    metric_service->AddMetric(
        "version_metric",
        "version description",
        "version_metric{team=\"app_backend\"}",
        []() -> long { return version_metric; }
    );

    // 添加proto消息到达计数器
    metric_service->AddMetric(
        "csadapt_adapt_proto_message_received_total",
        "AK.Adapt.proto message received count from slim",
        "csadapt_adapt_proto_message_received_total",
        nullptr
    );
    metric_service->AddMetric(
        "csadapt_adapt_office_proto_message_received_total", 
        "AK.Adapt.Office.proto message received count from slim",
        "csadapt_adapt_office_proto_message_received_total",
        nullptr
    );
}


