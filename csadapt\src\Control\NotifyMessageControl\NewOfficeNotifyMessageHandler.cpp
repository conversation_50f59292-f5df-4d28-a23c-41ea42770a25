#include "dbinterface/Message.h"
#include "AK.BackendCommon.pb.h"
#include "NewOfficeNotifyMessageHandler.h"
#include "BackendP2PMsgControl.h"
#include "dbinterface/ProjectInfo.h"
#include "dbinterface/OfficeMessage.h"
#include "util_judge.h"

extern CSADAPT_CONF gstCSADAPTConf;
extern RouteMQProduce* g_nsq_producer;

void NewOfficeNotifyMessageHandler::SendMessage(const std::string& msg, const std::string& msg_type, const KakfaMsgKV& kv)
{

    if (!KafkaWebMsgParse::CheckKeyExist(kv, "project_type"))
    {
        AK_LOG_WARN << "NewOfficeNotifyMessageHandler SendMessage project_type is null. msg=" << msg << "type=" << msg_type;
        return;
    }

    if (!KafkaWebMsgParse::CheckKeyExist(kv, "message_uuid"))
    {
        AK_LOG_WARN << "NewOfficeNotifyMessageHandler SendMessage message_uuid is null. msg=" << msg << "type=" << msg_type;
        return;
    }

    AK_LOG_INFO << "NewOfficeNotifyMessageHandler SendMessage (csadapt->csroute->kafka->csoffice) project_type="
          <<  kv.at("project_type") << ", message_uuid=" <<kv.at("message_uuid");
    if (kv.at("project_type") == "3")
    {
        (void)NewOfficeSendPerMessage(kv.at("message_uuid"));
    }
    else
    {
        // TODO: 其他类型的项目
        AK_LOG_WARN << "NewOfficeNotifyMessageHandler SendMessage Unsupported project_type=" << kv.at("project_type");
    }

    return;
}




/*****************  private methods *****************/

int NewOfficeNotifyMessageHandler::NewOfficeSendPerMessage(const std::string& message_uuid)
{
    AK_LOG_INFO << "Recv NewOfficeSendPerMessage SendMessage message_uuid=" << message_uuid;
    AK::BackendCommon::BackendP2PBaseMessage base_msg = BackendP2PMsgControl::CreateP2PBaseMsg(
        AKCS_M2R_P2P_SEND_TEXT_NOTIFY_MSG,
        TransP2PMsgType::TRAN_TYPE_NONE,
        "",
        BackendP2PMsgControl::DevProjectTypeToDevType((int)project::PROJECT_TYPE::OFFICE_NEW),
        (int)project::PROJECT_TYPE::OFFICE_NEW
    );

    while (1)
    {
        MsgSendList text_messages;
        int finish = dbinterface::OfficeMessage::GetTextMsgSendList(message_uuid, text_messages);//从数据库读取未发送出去的多条消息
        if (finish)
        {
            return 0;
        }

        int count = 0;
        for (auto& text_send : text_messages)
        {
            snprintf(text_send.from, sizeof(text_send.from), "%s", "Security Center");
            AK::Server::P2PCommonTxtMsgNotifyMsg msg;
            msg.set_client_type(text_send.client_type);
            msg.set_title(text_send.title);
            msg.set_content(text_send.content);
            msg.set_time(text_send.create_time);
            msg.set_from(text_send.from);
            msg.set_to(text_send.to);
            msg.set_recv_msg_id(text_send.recv_msg_id);
            msg.set_msg_type((int)text_send.msg_type);

            if(text_send.client_type == MessageClientType::APP_SEND){
                base_msg.set_type(TransP2PMsgType::TO_APP_UUID);
                base_msg.set_uid(text_send.personal_account_uuid);
                msg.set_uuid(text_send.personal_account_uuid);
                if (strlen(text_send.personal_account_uuid) == 0)
                {
                    continue;
                }
                AK_LOG_INFO << "NewOfficeSendPerMessage SendMessage message_uuid=" << message_uuid << " send to app :" << text_send.personal_account_uuid;
            }
            else if(text_send.client_type == MessageClientType::DEV_SEND){
                base_msg.set_type(TransP2PMsgType::TO_DEV_UUID);
                base_msg.set_uid(text_send.device_uuid);
                msg.set_uuid(text_send.device_uuid);
                if (strlen(text_send.device_uuid) == 0)
                {
                    continue;
                }

                AK_LOG_INFO << "NewOfficeSendPerMessage SendMessage message_uuid=" << message_uuid << " send to dev:" << text_send.device_uuid;
            }

            base_msg.mutable_p2pcommontxtmsgnotifymsg2()->CopyFrom(msg);
            BackendP2PMsgControl::PushMsg2Route(&base_msg, (int)project::PROJECT_TYPE::OFFICE_NEW);

            ++count;
            if (count == 50)
            {
                usleep(100 * 1000);
                count = 0;
            }
        }
    }
    return 0;
}
