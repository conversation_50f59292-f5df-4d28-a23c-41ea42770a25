#ifndef __NEW_OFFICE_NOTIFY_MESSAGE_HANDLER_H__
#define __NEW_OFFICE_NOTIFY_MESSAGE_HANDLER_H__

#include <mutex>
#include <atomic>
#include <string>
#include <unordered_map> 
#include <condition_variable>

#include "util.h"
#include "AkLogging.h"
#include "json/json.h"
#include "AdaptDef.h"
#include "AkcsMsgDef.h"
#include "AK.Adapt.pb.h"
#include "AK.Server.pb.h"
#include "AkcsPduBase.h"
#include "AdaptMQProduce.h"
#include "KafkaParseWebMsg.h"
#include "AkcsKafkaProducer.h"
#include "Control/IPCControl.h"

class NewOfficeNotifyMessageHandler
{
public:
    static void SendMessage(const std::string& msg, const std::string& msg_type, const KakfaMsgKV& kv);

private:
    static int NewOfficeSendPerMessage(const std::string& message_uuid);
};

#endif
