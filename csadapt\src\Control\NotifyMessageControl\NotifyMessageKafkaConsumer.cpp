#include "util.h"
#include "AdaptDef.h"
#include "AkLogging.h"
#include "NotifyMessageKafkaConsumer.h"
#include "NewOfficeNotifyMessageHandler.h"
extern CSADAPT_CONF gstCSADAPTConf;

void NotifyMessageKafkaConsumer::Init()
{
    RegisterHandle("send_message", NotifyMessageHandler::SendMessage);
    RegisterHandle("newoffice_send_message", NewOfficeNotifyMessageHandler::SendMessage);
}

void NotifyMessageKafkaConsumer::StartKafkaConsumer()
{
    kafak_.SetParma(
        gstCSADAPTConf.kafka_broker_ip, gstCSADAPTConf.notify_web_message_topic,
        gstCSADAPTConf.notify_web_message_group, gstCSADAPTConf.notify_web_message_thread_num
    );

    kafak_.SetConsumerCb(
        std::bind(
            &NotifyMessageKafkaConsumer::HandleKafkaMessage, this, std::placeholders::_1,
            std::placeholders::_2, std::placeholders::_3, std::placeholders::_4
        )
    );

    kafak_.Start();
}

void NotifyMessageKafkaConsumer::RegisterHandle(const std::string& msg_type, HandleKafkaNotifyFunc func)
{
    functions_.insert(std::map<std::string, HandleKafkaNotifyFunc>::value_type(msg_type, func));
}


bool NotifyMessageKafkaConsumer::HandleKafkaMessage(uint64_t partition, uint64_t offset, const std::string& key, const std::string& org_msg)
{
    KafkaWebMsgParse msg(org_msg);
    if (!msg.ParseOk())
    {
        return true;
    }

    auto it = functions_.find(msg.msg_type_);
    if (it == functions_.end())
    {
        AK_LOG_WARN << "Not found msg_type=" << msg.msg_type_ << ", trace_id=" << msg.trace_id_;
        return true;
    }

    it->second(org_msg, msg.msg_type_, msg.kv_);
    return true;
}
