#ifndef __NOTIFY_MESSAGE_KAFKA_CONSUMER_H__
#define __NOTIFY_MESSAGE_KAFKA_CONSUMER_H__
#include <string>
#include <vector>
#include <iostream>

#include "Singleton.h"
#include "KafkaParseWebMsg.h"
#include "AkcsKafkaConsumer.h"
#include "NotifyMessageHandler.h"

typedef void (*HandleKafkaNotifyFunc)(const std::string& org_msg, const std::string& msg_type_, const KakfaMsgKV& kv);

class NotifyMessageKafkaConsumer
{
public:
    // 实现单例
    friend class AKCS::Singleton<NotifyMessageKafkaConsumer>;

    void Init();
    void StartKafkaConsumer();
    void RegisterHandle(const std::string& msg_type, HandleKafkaNotifyFunc func);
    bool HandleKafkaMessage(uint64_t partition, uint64_t offset, const std::string& key, const std::string& msg);
    bool Status() {return kafak_.Status();}

private:
    std::map<std::string, HandleKafkaNotifyFunc> functions_;
    int kafka_consmuer_thread_ = 1;
    AkcsKafkaConsumer kafak_;
};

#endif // __NOTIFY_MESSAGE_CONSUMER
