#ifndef __UNIX_CONTROL_H__
#define __UNIX_CONTROL_H__

#include <map>
#include <mutex>
#include "consistent-hash/QueueConsistentHash.h"

#define SPECIAL_MSG_TUBE_ID            101


#define BEANSTALK_NORMAL_DELAY          1
#define BEANSTALK_AWS_DELAY             3
#define BEANSTALK_IPCHANGE_DELAY      180


class CUnixSocketControl
{
public:
    CUnixSocketControl();
    ~CUnixSocketControl();
    static CUnixSocketControl* GetInstance();
    void Init(const std::string& beanstalk_ip);

    //生产者线程
    void* IPCServerThread();

    void AddMsg(char* msg, int len);
    int GetWriteFileTube(const std::string &str, int write_num);
    int GetCommTube();
    //消费者线程
    static void ProcessMsgForBeanstalk(int tube_id, const std::string& beanstalk_ip);
    
    void AddMsgToBeanstalk(const char* msg, int len, int tube_id, int to_aws, int delay_interval);
    void AddMsgToBeanstalkBackup(const char* msg, int len, int tube_id, int to_aws, int delay_interval);
    int DispatchMsg(void* pMsgBuf, unsigned int nMsgLen);

    int GetWirteFileThreadNum();
    bool CheckBeanstalkStatus();
    bool CheckBeanstalkBackUpStatus();
    void InitPduBeanstalk();

    void AddCommonMsgToKafka(char* msg, int len, const std::string& kafka_key);
private:
    CUnixSocketControl(const CUnixSocketControl&);
    CUnixSocketControl& operator = (const CUnixSocketControl&);
    int CheckToAwsByAccountID(int account_id);
    int CheckToAwsByUid(const std::string &uid, int is_dev = 0);
    bool IsBigProject(int project_id);

    //消息处理句柄
    int OnUdpMessage(uint32_t msg, uint32_t wParam, uint32_t lParam, void* lpData);

    static CUnixSocketControl* instance;

    std::mutex mutex_cnt_;
    int thread_num_;                // 消费线程数， 每个线程对应其队列web_to_adapt%d
    int thread_wirte_file_num_;     // 写配置线程数
    int thread_common_num_;         // 转发线程数
    int config_tube_num_;
    int polling_cnt_;

    akcs_consistent_hash::QueueConsistentHash write_file_queue_consistent_hash_;

};

CUnixSocketControl* GetUnixSocketControlInstance();

#endif //__UNIX_CONTROL_H__
