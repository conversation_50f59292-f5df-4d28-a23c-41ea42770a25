#include <stdio.h>
#include <stdlib.h>
#include <evpp/libevent.h>
#include <evpp/timestamp.h>
#include <evpp/event_loop_thread.h>
#include <evpp/httpc/request.h>
#include <evpp/httpc/conn.h>
#include <evpp/httpc/response.h>
#include "evpp/http/service.h"
#include "evpp/http/context.h"
#include "evpp/http/http_server.h"

#include "HttpServer.h"
#include "util.h"
#include "http/HttpMsgControl.h"
#include "AES256.h"
#include "AdaptMQProduce.h"
#include "UnixSocketControl.h"
#include "AdaptDef.h"
#include "RouteClientMng.h"
#include "CachePool.h"
#include "ConnectionPool.h"
#include "AkLogging.h"

#include "KafkaConsumerAppBackendTopicHandle.h"
#include "KafkaConsumerPushTopicHandle.h"
#include "KafkaConsumerNotifyTopicHandle.h"
#include "EtcdCliMng.h"
#include "MetricService.h"

//全局变量
extern CAkEtcdCliManager* g_etcd_cli_mng;
extern RouteMQProduce* g_nsq_producer;
extern CSADAPT_CONF gstCSADAPTConf;

//当请求无效时的处理函数
void DefaultHandler(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    AK_LOG_WARN << "http req route is not define";
    cb("http req route is not define");
}

void HttpReqMetricsCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    MetricService* metric_service = MetricService::GetInstance();
    if (metric_service == nullptr)
    {
        AK_LOG_WARN << "metric service init failed.";
        cb("# metric service init failed.");
        return;
    }

    metric_service->UpdateMetrics();
    std::string response = metric_service->ToFormateString();
    cb(response);
    return;
}

void startHttpServer()
{
    const int port = 9241;
    const int thread_num = 0;
    std::string addr = GetEth0IPAddr(); //监听在内网
    evpp::http::Server server(addr, thread_num, false);
    server.RegisterDefaultHandler(&DefaultHandler);
    server.RegisterHandler("/metrics", HttpReqMetricsCallback);
    server.Init(port);
    server.Start();
    return;
}
