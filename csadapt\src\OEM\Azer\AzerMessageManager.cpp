#include "AzerMessageManager.h"
#include "AkLogging.h"

//TODO:后续词条翻译后改成阿塞拜疆语
const std::array<std::string, 12> AzerMonthAdapter::month_names = {
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December"
};

static const char *g_html_tag_kapital_link = "<kapital-link>";
static const char *g_slash_html_tag_kapital_link = "</kapital-link>";

AzerBillMessageManager::AzerBillMessageManager(int type, const BillingPeriod& billing_period, const std::string& pay_link)
{
    bill_type_ = type;
    billing_period_ = billing_period;
    pay_link_ = pay_link;
}

std::string AzerBillMessageManager::GenerateBillTitle()
{
    if(bill_type_ == BillType::AUTO_SEND)
    {
        return GenerateAutoSendBillTitle();
    }
    else if(bill_type_ == BillType::MANUAL_SEND)
    {
        return GenerateManualSendBillTitle();
    }
    AK_LOG_WARN << "bill type wrong, type:" << bill_type_;
    return "";
}

std::string AzerBillMessageManager::GenerateBillContent()
{
    if(bill_type_ == BillType::AUTO_SEND)
    {
        return  GenerateAutoSendBillContent();
    }
    else if(bill_type_ == BillType::MANUAL_SEND)
    {
        return GenerateManualSendBillContent();
    }
    AK_LOG_WARN << "bill type wrong, type:" << bill_type_;
    return "";
}

std::string AzerBillMessageManager::GenerateAutoSendBillTitle()
{
    std::string effect_month_name = AzerMonthAdapter::GetMonthNames(billing_period_.effect_month);

    std::stringstream title_ss;
    title_ss << "Communal fee bill for " << effect_month_name << ", " << billing_period_.effect_year;
    return title_ss.str();
}

std::string AzerBillMessageManager::GenerateManualSendBillTitle()
{
    std::string effect_month_name = AzerMonthAdapter::GetMonthNames(billing_period_.effect_month);
    std::string expire_month_name = AzerMonthAdapter::GetMonthNames(billing_period_.expire_month);

    std::stringstream title_ss;
    title_ss << "Communal fee bill for " << effect_month_name << " " << billing_period_.effect_day << ", " << billing_period_.effect_year
                                << " - " << expire_month_name << " " << billing_period_.expire_day << ", " << billing_period_.expire_year;
    return title_ss.str();
}

std::string AzerBillMessageManager::GenerateAutoSendBillContent()
{
    std::string effect_month_name = AzerMonthAdapter::GetMonthNames(billing_period_.effect_month);

    std::stringstream content_ss;
    content_ss << "Dear resident,\n\n"
                    << "Communal fee bill for " << effect_month_name << ", " << billing_period_.effect_year
                    << " has been generated. Please open the following link in the browser to complete the payment before "
                    << effect_month_name << " " << billing_period_.effect_day << ", " << billing_period_.effect_year << ".\n\n"
                    << g_html_tag_kapital_link << pay_link_ <<  g_slash_html_tag_kapital_link << " ";
    return content_ss.str();   
}

std::string AzerBillMessageManager::GenerateManualSendBillContent()
{
    std::string effect_month_name = AzerMonthAdapter::GetMonthNames(billing_period_.effect_month);
    std::string expire_month_name = AzerMonthAdapter::GetMonthNames(billing_period_.expire_month);

    std::stringstream content_ss;
    content_ss << "Dear resident,\n\n"
                    << "Communal fee bill for " << effect_month_name << " " << billing_period_.effect_day << ", " << billing_period_.effect_year
                    << " - " << expire_month_name << " " << billing_period_.expire_day << ", " << billing_period_.expire_year
                    << " has been generated. Please open the following link in the browser to complete the payment.\n\n"
                    << g_html_tag_kapital_link << pay_link_ <<  g_slash_html_tag_kapital_link << " ";
    return content_ss.str();   
}