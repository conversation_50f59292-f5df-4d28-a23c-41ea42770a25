#ifndef __AZER_MESSAGE_MANAGER_H__
#define __AZER_MESSAGE_MANAGER_H__

#include<string>
#include<array>
#include<vector>

//账单类型枚举
enum BillType
{
    AUTO_SEND = 0, //自动发送
    MANUAL_SEND = 1, //全发送
};

class AzerMonthAdapter
{
public:
    static std::string GetMonthNames(int month)
    {
        if(month >= 1 && month <= 12)
        {
            return month_names[month - 1];
        }
        return "";
    }
private:
    static const std::array<std::string, 12> month_names;
};

typedef struct BILLING_PERIOD_INFO
{
    //生效时间
    uint32_t effect_year;
    uint32_t effect_month;
    uint32_t effect_day;
    //过期时间 自动发送无下列字段
    uint32_t expire_year;
    uint32_t expire_month;
    uint32_t expire_day;
}BillingPeriod;

//TODO:改成阿塞拜疆语
class AzerBillMessageManager
{
public:
    AzerBillMessageManager(int type, const BillingPeriod& billing_period, const std::string& pay_link);
    std::string GenerateBillTitle();
    std::string GenerateBillContent();
private:
    std::string GenerateAutoSendBillTitle();
    std::string GenerateAutoSendBillContent();
    std::string GenerateManualSendBillTitle();
    std::string GenerateManualSendBillContent();
private:
    int bill_type_;
    BillingPeriod billing_period_;
    std::string pay_link_;
};
#endif // __AZER_MESSAGE_MANAGER_H__