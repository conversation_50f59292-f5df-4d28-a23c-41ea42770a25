#include "AzerUnixSocketControl.h"
#include "stdafx.h"
#include "AkcsMsgDef.h"
#include "AkcsCommonDef.h"
#include "AkLogging.h"
#include "AK.Crontab.pb.h"
#include "AkcsPduBase.h"
#include "json/json.h"
#include "AK.Server.pb.h"
#include "AK.Adapt.pb.h"
#include "AdaptMQProduce.h"
#include "dbinterface/resident/PersonalAccountUserInfo.h"
#include "dbinterface/Message.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "AK.Resid.pb.h"
#include "BackendP2PMsgControl.h"
#include "AzerMessageManager.h"

AzerUnixSocketControl::AzerUnixSocketControl()
{

}

AzerUnixSocketControl* AzerUnixSocketControl::GetInstance()
{
    static AzerUnixSocketControl instance;
    return &instance;
}

int AzerUnixSocketControl::OnSocketMsg(void* msg_buf, unsigned int len)
{
    uint32_t id = NTOHL(*((unsigned int*)((unsigned char*)msg_buf + 4)));
    uint32_t project_type =  NTOHL(*((unsigned int*)((unsigned char*)msg_buf + 8)));
    
    if(project_type != project::RESIDENCE)
    {
        AK_LOG_WARN << "Office Message not support";
        return 0;
    }

    switch (id)
    {
        case MSG_P2A_SEND_MESSAGE_NOTIFY: //手动发送Message
        {
            OnManualSenBillMsg(msg_buf, len);
            break;
        }

        case MSG_P2A_SEND_MESSAGE_CRONTAB_NOTIFY: //自动发送Message
        {
            OnAutoSendBillMsg(msg_buf, len);
            break;
        }

        default:
        {
            AK_LOG_WARN << "Failed to match msg id:" << id;
            return -1;
        }
    }
    return 0;
}

void AzerUnixSocketControl::OnManualSenBillMsg(void *msg_buf, unsigned int len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnSendPayLinkMsg The Param is NULL";
        return;
    }

    AK::Adapt::SendMessageNotifyMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));

    std::string account =  msg.key();
    
    Json::Reader reader;
    Json::Value root;
    Json::Value data;    
     if (!reader.parse(msg.payload(), root))
    {
        AK_LOG_WARN << "parse json error.data=" << msg.payload();
        return;
    }

    if(root["OEM"] != "Azerbaijan")
    {
        AK_LOG_WARN << "msg oem wrong, data=" << msg.payload();
    }

    if (!reader.parse(root["data"].asString(), data))
    {
        AK_LOG_WARN << "parse json error.data=" << root["data"];
        return;
    }

    std::string pay_link = data["pay_link"].asString();
    uint32_t effect_year = data["effect_year"].asInt();
    uint32_t effect_month = data["effect_month"].asInt();
    uint32_t effect_day = data["effect_day"].asInt();
    uint32_t expire_year = data["expire_year"].asInt();
    uint32_t expire_month = data["expire_month"].asInt();
    uint32_t expire_day = data["expire_day"].asInt();

    AK_LOG_INFO << "OnManualSendBillMsg: data= " << root["data"] << " account= " << account;

    BillingPeriod billing_period = {effect_year, effect_month, effect_day, expire_year, expire_month, expire_day};
    AzerBillMessageManager bill_message(BillType::MANUAL_SEND, billing_period, pay_link);

    std::string title = bill_message.GenerateBillTitle();
    std::string content = bill_message.GenerateBillContent();
    int message_type = (int)MessageType2::TEXT_MSG;
    int recevier_type = MessageClientType::APP_SEND;
    std::string account_uuid;
    dbinterface::ResidentPersonalAccount::GetUUIDByAccount(account, account_uuid);

    int recv_msg_id = AzerInsertMessage(account, title, content, message_type, recevier_type);

    if (recv_msg_id <= 0)
    {
        AK_LOG_WARN << "add manual send bill message failed. message account list id:" << recv_msg_id;
        return;
    }

    AK::BackendCommon::BackendP2PBaseMessage base;
    base = BackendP2PMsgControl::CreateP2PBaseMsg(AKCS_M2R_P2P_SEND_TEXT_NOTIFY_MSG, TransP2PMsgType::TO_APP_UID, account,
                BackendP2PMsgControl::DevProjectTypeToDevType(project::RESIDENCE), project::RESIDENCE);

    AK::Server::P2PCommonTxtMsgNotifyMsg p2p_msg;
    p2p_msg.set_msg_type(message_type);
    p2p_msg.set_title(title);
    p2p_msg.set_content(content);
    p2p_msg.set_client_type(recevier_type);
    p2p_msg.set_uuid(account_uuid);
    p2p_msg.set_recv_msg_id(recv_msg_id);

    base.mutable_p2pcommontxtmsgnotifymsg2()->CopyFrom(p2p_msg);
    BackendP2PMsgControl::PushMsg2Route(&base, project::RESIDENCE);

    return;
}

void AzerUnixSocketControl::OnAutoSendBillMsg(void *msg_buf, unsigned int len)
{
    if(nullptr == msg_buf)
    {
        AK_LOG_WARN << "OnSendPayLinkMsg The Param is NULL";
        return;
    }

    AK::Adapt::SendMessageNotifyMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));

    std::string account = msg.key();

    Json::Reader reader;
    Json::Value root;
    Json::Value data;    
     if (!reader.parse(msg.payload(), root))
    {
        AK_LOG_WARN << "parse json error.data=" << msg.payload();
        return;
    }

    if(root["OEM"] != "Azerbaijan")
    {
        AK_LOG_WARN << "msg oem wrong, data=" << msg.payload();
    }

    if (!reader.parse(root["data"].asString(), data))
    {
        AK_LOG_WARN << "parse json error.data=" << root["data"];
        return;
    }

    std::string pay_link = data["pay_link"].asString();
    std::string language = data["language"].asString();
    uint32_t effect_year = data["effect_year"].asInt();
    uint32_t effect_month = data["effect_month"].asInt();
    uint32_t effect_day = data["effect_day"].asInt();

    AK_LOG_INFO << "OnAutoSendBillMsg: data= " << root["data"] << " account= " << account;

    BillingPeriod billing_period = {effect_year, effect_month, effect_day, 0, 0, 0}; //自动发送无需手动指定过期时间
    AzerBillMessageManager bill_message(BillType::AUTO_SEND, billing_period, pay_link);

    std::string title = bill_message.GenerateBillTitle();
    std::string content = bill_message.GenerateBillContent();
    int message_type = (int)MessageType2::TEXT_MSG;
    int recevier_type = MessageClientType::APP_SEND;
    std::string account_uuid;
    dbinterface::ResidentPersonalAccount::GetUUIDByAccount(account, account_uuid);

    int recv_msg_id = AzerInsertMessage(account, title, content, message_type, recevier_type);

    if (recv_msg_id <= 0)
    {
        AK_LOG_WARN << "add manual send bill message failed. message account list id:" << recv_msg_id;
        return;
    }

    AK::BackendCommon::BackendP2PBaseMessage base;
    base = BackendP2PMsgControl::CreateP2PBaseMsg(AKCS_M2R_P2P_SEND_TEXT_NOTIFY_MSG, TransP2PMsgType::TO_APP_UID, account,
                BackendP2PMsgControl::DevProjectTypeToDevType(project::RESIDENCE), project::RESIDENCE);

    AK::Server::P2PCommonTxtMsgNotifyMsg p2p_msg;
    p2p_msg.set_msg_type(message_type);
    p2p_msg.set_title(title);
    p2p_msg.set_content(content);
    p2p_msg.set_client_type(recevier_type);
    p2p_msg.set_uuid(account_uuid);
    p2p_msg.set_recv_msg_id(recv_msg_id);

    base.mutable_p2pcommontxtmsgnotifymsg2()->CopyFrom(p2p_msg);
    BackendP2PMsgControl::PushMsg2Route(&base, project::RESIDENCE);

    return;
}

int AzerUnixSocketControl::AzerInsertMessage(const std::string& account, const std::string& title, const std::string& content, int message_type, int recevier_type)
{
    //根据主账号查找对应社区ID,用于PM Message列表展示
    ResidentPerAccount per_account;
    memset(&per_account, 0, sizeof(per_account));
    if (0 != dbinterface::ResidentPersonalAccount::InitAccountByUid(account, per_account))
    {
        AK_LOG_WARN << "get account info failed. account:" << account;
        return -1;
    }
 
    int mng_id = per_account.parent_id;
    int receiver_type = PersoanlMessageSend::TextClientType::APP_SEND;

    int message_account_list_id = 0;
    message_account_list_id = dbinterface::Message::InsertSentMessage(account, title, content, message_type, receiver_type, mng_id, per_account.name);
    if (message_account_list_id <= 0)
    {
        AK_LOG_WARN << "add manual send bill message failed. message account list id:" << message_account_list_id;
        return -1;
    }

    return message_account_list_id;
}