#ifndef __AZER_UNIX_SOCKET_CONTROL_H__
#define __AZER_UNIX_SOCKET_CONTROL_H__
#include "AK.Resid.pb.h"
#include "AkcsCommonDef.h"
#include "AK.BackendCommon.pb.h"
class AzerUnixSocketControl
{
public:
    static AzerUnixSocketControl* GetInstance();
    AzerUnixSocketControl();
    int OnSocketMsg(void* msg_buf, unsigned int len); 
    //手动发送Message处理
    void OnManualSenBillMsg(void* msg_buf, unsigned int len);
    //自动发送Message处理
    void OnAutoSendBillMsg(void *msg_buf, unsigned int len);

private:
    //阿塞拜疆消息插入，返回MessageAccountList的ID，用于离线推送
    int AzerInsertMessage(const std::string& account, const std::string& title, const std::string& content, int message_type, int recevier_type);
};

#endif // __AZER_UNIX_SOCKET_CONTROL_H__