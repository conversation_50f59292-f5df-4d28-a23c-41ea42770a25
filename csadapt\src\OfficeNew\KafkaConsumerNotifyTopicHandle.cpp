#include "util.h"
#include "AdaptDef.h"
#include "AkLogging.h"
#include "NewOfficeSmsNotify.h"
#include "NewOfficeDataAnalysis.h"
#include "NewOfficeOnceAutop.h"
#include "NewOfficeResetDevice.h"
#include "NewOfficeRebootDevice.h"
#include "NewOfficeLockDownControl.h"
#include "NewOfficeRemoteDevControl.h"
#include "NewOfficeEmergencyControl.h"
#include "KafkaConsumerNotifyTopicHandle.h"

extern CSADAPT_CONF gstCSADAPTConf;

void HandleKafkaNotifyTopicMsg::Init()
{
    RegNewOfficeHandle("once_autop", NewOfficeOnceAutop::Handle);
    RegNewOfficeHandle("reset_device", NewOfficeResetDevice::Handle);
    RegNewOfficeHandle("reboot_device", NewOfficeRebootDevice::Handle);
    RegNewOfficeHandle("remote_dev_control", NewOfficeRemoteDevControl::Handle);
    RegNewOfficeHandle("emergency_control", NewOfficeEmergencyControl::Handle);
    RegNewOfficeHandle("account_modify", NewOfficeNotifyHandler::AccountModify);
    RegNewOfficeHandle("remote_open_door", NewOfficeNotifyHandler::RemoteOpenDoor);
    RegNewOfficeHandle("remote_open_security_relay", NewOfficeNotifyHandler::RemoteOpenSecurityRelay);
    RegNewOfficeHandle("newoffice_exportlog", NewOfficeNotifyHandler::ExportLog);
    RegNewOfficeHandle("lockdown_control", NewOfficeLockDownControl::Handle);
}

void HandleKafkaNotifyTopicMsg::RegNewOfficeHandle(const std::string& msg_type, HandleWebNotifyFunc func)
{
    functions_.insert(std::map<std::string, HandleWebNotifyFunc>::value_type(msg_type, func));
}

void HandleKafkaNotifyTopicMsg::StartKafkaConsumer()
{
    kafak_.SetParma(
        gstCSADAPTConf.kafka_broker_ip, gstCSADAPTConf.appbackend_notify_topic,
        gstCSADAPTConf.appbackend_notify_group, gstCSADAPTConf.appbackend_notify_thread_num
    );

    kafak_.SetConsumerCb(
        std::bind(
            &HandleKafkaNotifyTopicMsg::HandleKafkaMessage, this, std::placeholders::_1,
            std::placeholders::_2, std::placeholders::_3, std::placeholders::_4
        )
    );

    kafak_.Start();
}

bool HandleKafkaNotifyTopicMsg::HandleTcpMessage(const std::string& org_msg)
{
    KafkaWebMsgParse msg(org_msg);
    if (!msg.ParseOk())
    {
        return true;
    }
    
    auto it = functions_.find(msg.msg_type_);
    if (it == functions_.end())
    {
        AK_LOG_WARN << "Not found msg_type=" << msg.msg_type_ << ", trace_id=" << msg.trace_id_;
        return true;
    }

    it->second(org_msg, msg.msg_type_, msg.kv_);
    return true;
}

bool HandleKafkaNotifyTopicMsg::HandleKafkaMessage(uint64_t partition, uint64_t offset, const std::string& key, const std::string& org_msg)
{
    KafkaWebMsgParse msg(org_msg);
    if (!msg.ParseOk())
    {
        return true;
    }

    auto it = functions_.find(msg.msg_type_);
    if (it == functions_.end())
    {
        AK_LOG_WARN << "Not found msg_type=" << msg.msg_type_ << ", trace_id=" << msg.trace_id_;
        return true;
    }

    it->second(org_msg, msg.msg_type_, msg.kv_);
    return true;
}
