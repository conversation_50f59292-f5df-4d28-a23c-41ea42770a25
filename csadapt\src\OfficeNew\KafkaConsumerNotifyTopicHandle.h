#ifndef KAFKA_CONSUMER_HANDLE_NOTIFY_TOPIC_H_
#define KAFKA_CONSUMER_HANDLE_NOTIFY_TOPIC_H_

#include <map>
#include <string>
#include <unordered_map> 
#include "Singleton.h"
#include "AK.Server.pb.h"
#include "KafkaParseWebMsg.h"
#include "AkcsKafkaConsumer.h"
#include "NewOfficeNotifyHandler.h"

typedef void (*HandleWebNotifyFunc)(const std::string& org_msg, const std::string& msg_type_, const KakfaMsgKV& kv);

class HandleKafkaNotifyTopicMsg
{
public:
    // 实现单例
    friend class AKCS::Singleton<HandleKafkaNotifyTopicMsg>;

    void Init();
    void RegNewOfficeHandle(const std::string& msg_type, HandleWebNotifyFunc func);

    void StartKafkaConsumer();
    bool HandleTcpMessage(const std::string& msg);
    bool HandleKafkaMessage(uint64_t partition, uint64_t offset, const std::string& key, const std::string& msg);
    bool Status() {return kafak_.Status();}
private:
    std::map<std::string, HandleWebNotifyFunc> functions_;
    int kafka_consmuer_thread_ = 1;
    AkcsKafkaConsumer kafak_;
};


#endif

