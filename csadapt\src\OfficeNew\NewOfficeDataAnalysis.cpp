#include <string>
#include "NewOfficeDataAnalysis.h"
#include "AkLogging.h"
#include "KafkaConsumerPushTopicHandle.h"


static const std::vector<std::string> kKeys = {"message"};

void NewOfficeDataAnalysis::Handle(const std::string& org_msg, const std::string& msg_type, const KakfaMsgKV &kv)
{
    AK_LOG_INFO << "MsgHandleDataAnalysis";
    if (KafkaWebMsgParse::CheckKeysExist(kv, kKeys))
    {
        return;
    }

    //构造推送到csconfig

    
}


