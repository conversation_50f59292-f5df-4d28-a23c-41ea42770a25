#include "BackendP2PMsgControl.h"
#include "NewOfficeEmergencyControl.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/new-office/OfficeAdmin.h"

extern CSADAPT_CONF gstCSADAPTConf;
extern LOG_DELIVERY gstAKCSLogDelivery;
extern std::map<string, AKCS_DST> g_time_zone_DST;

static const std::vector<std::string> kKeys = {"control_type", "emergency_uuid"};

void NewOfficeEmergencyControl::Handle(const std::string& web_notify_msg, const std::string& msg_type, const KakfaMsgKV &kv)
{
    if (!KafkaWebMsgParse::CheckKeysExist(kv, kKeys))
    {
        AK_LOG_WARN << "NewOfficeEmergencyControl Keys Error, web_notify_msg = " << web_notify_msg;
        return;
    }

    EmergencyDoorControlInfo emergency_info;
    emergency_info.control_type = ATOI(kv.at("control_type").c_str());
    Snprintf(emergency_info.uuid, sizeof(emergency_info.uuid), kv.at("emergency_uuid").c_str());
    
    AK_LOG_INFO << "NewOfficeEmergencyControl uuid = " << emergency_info.uuid << ", type = " << emergency_info.control_type;
    
    //获取项目信息
    std::string project_uuid;
    if (0 != dbinterface::PmEmergencyDoorLog::GetProjectUUIDByUUID(emergency_info.uuid, project_uuid))
    {
        AK_LOG_WARN << "NewOfficeEmergencyControl get project uuid failed. emergencydoorlog uuid=" << emergency_info.uuid;
        return;
    }
    
    dbinterface::AccountInfo project_info;
    if (0 != dbinterface::Account::GetAccountByUUID(project_uuid, project_info))
    {
        AK_LOG_WARN << "NewOfficeEmergencyControl get project info failed. project uuid=" << project_uuid;
        return;
    }
    
    // 通知门口机开关门
    EmergencyControlNotify(project_info, emergency_info);

    // 通知app和室内机
    EmergencyMessageNotify(project_info, emergency_info);
    
    return;
}

void NewOfficeEmergencyControl::EmergencyControlNotify(const dbinterface::AccountInfo& project_info, const EmergencyDoorControlInfo& emergency_info)
{
    // 转换为doorlog类型
    ACT_OPEN_DOOR_TYPE act_type = dbinterface::PmEmergencyDoorLog::GetEmergencyControlType(emergency_info.control_type);

    // 获取开门设备列表
    dbinterface::PmEmergencyDoorLogInfoList emergency_door_info_list;
    dbinterface::PmEmergencyDoorLog::GetEmergencyDoorLogListByUUID(emergency_info.uuid, emergency_door_info_list);

    // 获取 initiator 名称
    std::string initiator = dbinterface::PmEmergencyDoorLog::GetEmergencyDoorLogInitiatorByUUID(emergency_info.uuid);
    
    for (const auto& emergency_door_info : emergency_door_info_list)
    {	
        ResidentDev dev;
        if (0 != dbinterface::ResidentDevices::GetUUIDDev(emergency_door_info.device_uuid, dev))
        {
            AK_LOG_WARN << "EmergencyControlNotify GetUUIDDev failed, uuid = " << emergency_door_info.device_uuid;
            continue;
        }

        dbinterface::RelayStatus status = dbinterface::PmEmergencyDoorLog::CheckDevStatus(dev);
        if (dbinterface::RelayStatus::PROCESSING == status)
        {
            // 设备状态正常 发送开门通知
            AK::Server::P2PPmEmergencyDoorControlMsg control_msg;
            control_msg.set_mac(dev.mac);
            control_msg.set_device_uuid(dev.uuid);
            control_msg.set_initiator(initiator);
            control_msg.set_msg_uuid(emergency_info.uuid);
            control_msg.set_auto_manual(OPERATE_TYPE::MANUAL);
            control_msg.set_operation_type(emergency_info.control_type);
            control_msg.set_relay(emergency_door_info.relay);
            control_msg.set_security_relay(emergency_door_info.security_relay);

            AK::BackendCommon::BackendP2PBaseMessage base_msg;
            base_msg = BackendP2PMsgControl::CreateP2PBaseMsg(AKCS_M2R_EMERGENCY_DOOR_CONTROL, TransP2PMsgType::TO_DEV_UUID, dev.uuid, csmain::DeviceType::OFFICE_DEV, project::PROJECT_TYPE::OFFICE);
            base_msg.mutable_p2ppmemergencydoorcontrolmsg2()->CopyFrom(control_msg);
            BackendP2PMsgControl::PushMsg2Route(&base_msg, project::PROJECT_TYPE::OFFICE);
            
            AK_LOG_INFO << "EmergencyControlNotify control type = " << emergency_info.control_type << ", mac = " << dev.mac << ", relay = " << emergency_door_info.relay << ", security_relay = " << emergency_door_info.security_relay;
        }
        else
        {
            // 设备状态异常 更新记录doorlog
            dbinterface::PmEmergencyDoorLog::RecordAbnormalStatus(emergency_info.uuid, dev.uuid, initiator, status, act_type, gstAKCSLogDelivery.personal_capture_delivery);
        }
    }
    
    return;
}

void NewOfficeEmergencyControl::EmergencyMessageNotify(const dbinterface::AccountInfo& project_info, const EmergencyDoorControlInfo& emergency_info)
{
    OfficeInfo office_info(project_info.id);
    if (!office_info.EmergencyNeedNotify())
    {
        AK_LOG_INFO << "EmergencyMessageNotify office " << office_info.Name() << " emergency notification switch off";
        return;
    }

    AK::BackendCommon::BackendP2PBaseMessage base;
    AK::Server::P2PSendEmergencyNotifyMsg p2p_msg;
    p2p_msg.set_control_type(emergency_info.control_type);
    p2p_msg.set_timenow(GetNodeNowDateTimeByTimeZoneStr(office_info.TimeZone(), g_time_zone_DST));
    
    OfficeAccountList app_list;
    dbinterface::OfficePersonalAccount::GetNewOfficeAllPersonnelList(project_info.uuid, app_list);

    // 记录alarm日志
    dbinterface::Alarm::InsertEmegencyNotifyAlarmLog(app_list, project_info.id, emergency_info.control_type, gstCSADAPTConf.server_tag);
    
    // 通知app
    for (const auto& app : app_list)
    {
        p2p_msg.set_receiver_uid(app.account);
        base = BackendP2PMsgControl::CreateP2PBaseMsg(AKCS_M2R_P2P_SEND_EMERGENCY_NOTIFY_MSG, TransP2PMsgType::TO_APP_UID, app.account, csmain::DeviceType::OFFICE_APP, project::PROJECT_TYPE::OFFICE);
        base.mutable_p2psendemergencynotifymsg2()->CopyFrom(p2p_msg);
        
        BackendP2PMsgControl::PushMsg2Route(&base, project::PROJECT_TYPE::OFFICE);
        AK_LOG_INFO << "EmergencyMessageNotify app, office = " << office_info.Name() << ", account = " << app.account;
    }

    // 通知AdminApp
    OfficeAdminInfoList office_admin_info_list;
    dbinterface::OfficeAdmin::GetOfficeAdminInfoListByOfficeUUID(project_info.uuid, office_admin_info_list);
    for (const auto& admin_info : office_admin_info_list)
    {
        if (strlen(admin_info.personal_account_uuid) == 0 || admin_info.app_status == 0)
        {
            continue;
        }

        OfficeAccount per_account;
        if (0 == dbinterface::OfficePersonalAccount::GetUUIDAccount(admin_info.personal_account_uuid, per_account))
        {
            // 记录alarm日志
            dbinterface::Alarm::InsertEmegencyNotifyAlarmLog(admin_info, per_account.account, project_info.id, emergency_info.control_type, gstCSADAPTConf.server_tag);

            // 通知Admin APP
            p2p_msg.set_receiver_uid(per_account.account);
            base = BackendP2PMsgControl::CreateP2PBaseMsg(AKCS_M2R_P2P_SEND_EMERGENCY_NOTIFY_MSG, TransP2PMsgType::TO_APP_UID, per_account.account, csmain::DeviceType::OFFICE_APP, project::PROJECT_TYPE::OFFICE);
            base.mutable_p2psendemergencynotifymsg2()->CopyFrom(p2p_msg);
            
            BackendP2PMsgControl::PushMsg2Route(&base, project::PROJECT_TYPE::OFFICE);
            AK_LOG_INFO << "EmergencyMessageNotify admin app, office = " << office_info.Name() << ", account = " << per_account.account;
        }
    }

    // 通知室内机
    OfficeDevList indoor_list;
    dbinterface::OfficeDevices::GetAllOfficeIndoorListByMngID(project_info.id, indoor_list);

    for (const auto& indoor : indoor_list)
    {
        if (indoor->status == DeviceStatus::DEVICE_STATUS_OFFLINE)
        {
            AK_LOG_INFO << "EmergencyMessageNotify dev is offline, office = " << office_info.Name() << ", mac = " << indoor->mac;
            continue;
        }

        p2p_msg.set_receiver_uid(indoor->mac);
        base = BackendP2PMsgControl::CreateP2PBaseMsg(AKCS_M2R_P2P_SEND_EMERGENCY_NOTIFY_MSG, TransP2PMsgType::TO_DEV_MAC, indoor->mac, csmain::DeviceType::OFFICE_DEV, project::PROJECT_TYPE::OFFICE);
        base.mutable_p2psendemergencynotifymsg2()->CopyFrom(p2p_msg);
        
        BackendP2PMsgControl::PushMsg2Route(&base, project::PROJECT_TYPE::OFFICE);
        AK_LOG_INFO << "EmergencyMessageNotify indoor, office = " << office_info.Name() << ", mac = " << indoor->mac;
    }

    return;
}
