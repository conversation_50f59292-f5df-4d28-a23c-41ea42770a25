#ifndef __MSG_HANDLE_NEW_OFFICE_EMERGENCY_CONTROL_H__
#define __MSG_HANDLE_NEW_OFFICE_EMERGENCY_CONTROL_H__

#include <string>
#include <unordered_map> 
#include "json/json.h"
#include "AkLogging.h"
#include "AK.Adapt.pb.h"
#include "AkcsPduBase.h"
#include "AdaptDef.h"
#include "AkcsMsgDef.h"
#include "AdaptMQProduce.h"
#include "KafkaParseWebMsg.h"
#include "KafkaConsumerPushTopicHandle.h"
#include "dbinterface/AlarmDB.h"
#include "dbinterface/Account.h"
#include "dbinterface/PropertyInfo.h"
#include "dbinterface/PmEmergencyDoorLog.h"
#include "dbinterface/Log/LogSlice.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/resident/ResidentDevices.h"

class NewOfficeEmergencyControl
{
public:
    NewOfficeEmergencyControl() = default;
    static void Handle(const std::string& msg, const std::string& msg_type, const KakfaMsgKV &kv);

private:
    static void EmergencyControlNotify(const dbinterface::AccountInfo& project_info, const EmergencyDoorControlInfo& emergency_info);
    static void EmergencyMessageNotify(const dbinterface::AccountInfo& project_info, const EmergencyDoorControlInfo& emergency_info);

    static ACT_OPEN_DOOR_TYPE GetEmergencyControlType(int control_type);
};


#endif
