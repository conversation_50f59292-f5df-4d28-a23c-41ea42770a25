#include "BackendP2PMsgControl.h"
#include "NewOfficeLockDownControl.h"
#include "dbinterface/new-office/LockDownControl.h"

extern CSADAPT_CONF gstCSADAPTConf;
extern LOG_DELIVERY gstAKCSLogDelivery;
extern std::map<string, AKCS_DST> g_time_zone_DST;

static const std::vector<std::string> kKeys = {"lockdown_uuid"};

void NewOfficeLockDownControl::Handle(const std::string& web_notify_msg, const std::string& msg_type, const KakfaMsgKV &kv)
{
    if (!KafkaWebMsgParse::CheckKeysExist(kv, kKeys))
    {
        AK_LOG_WARN << "NewOfficeLockDownControl Keys Error, web_notify_msg = " << web_notify_msg;
        return;
    }

    std::string lockdown_uuid = kv.at("lockdown_uuid").c_str();

    LockDownControlInfo lockdown_control_info;
    dbinterface::LockDownControl::GetLockDownControlInfoByUUID(lockdown_uuid, lockdown_control_info);
    
    AK_LOG_INFO << "NewOfficeLockDownControl lockdown_uuid = " << lockdown_uuid <<  ", switch = " << (int)lockdown_control_info.lockdown_switch;

    // lockdown设备列表
    LockDownDoorInfoList lockdown_door_list;
    dbinterface::LockDownControl::GetLockDownDoorListByUUID(lockdown_uuid, lockdown_door_list);

    // 发送lockdown指令给设备
    for (const auto& lockdown_door : lockdown_door_list)
    {
        ResidentDev dev;
        if (0 != dbinterface::ResidentDevices::GetUUIDDev(lockdown_door.device_uuid, dev))
        {
            AK_LOG_WARN << "NewOfficeLockDownControl GetUUIDDev failed, uuid = " << lockdown_door.device_uuid;
            continue;
        }
        
        AK::Server::P2PLockDownDoorControlMsg control_msg;
        control_msg.set_mac(dev.mac);
        control_msg.set_device_uuid(dev.uuid);
        control_msg.set_msg_uuid(lockdown_uuid);
        control_msg.set_relay(lockdown_door.relay);
        control_msg.set_security_relay(lockdown_door.security_relay);
        control_msg.set_control_switch((int)lockdown_control_info.lockdown_switch);

        AK::BackendCommon::BackendP2PBaseMessage base_msg;
        base_msg = BackendP2PMsgControl::CreateP2PBaseMsg(AKCS_M2R_P2P_LOCKDOWN_DOOR_CONTROL, TransP2PMsgType::TO_DEV_UUID, dev.uuid, csmain::DeviceType::OFFICE_DEV, project::PROJECT_TYPE::OFFICE);
        base_msg.mutable_p2plockdowndoorcontrolmsg2()->CopyFrom(control_msg);
        BackendP2PMsgControl::PushMsg2Route(&base_msg, project::PROJECT_TYPE::OFFICE);

        AK_LOG_INFO << "NewOfficeLockDownControl control_type = " << (int)lockdown_control_info.lockdown_switch << ", mac = " << dev.mac << ", relay = " << lockdown_door.relay << ", security_relay = " << lockdown_door.security_relay;
    }

    return;
}