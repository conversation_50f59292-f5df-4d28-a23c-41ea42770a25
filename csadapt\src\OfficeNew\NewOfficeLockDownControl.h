#ifndef __MSG_HANDLE_NEW_OFFICE_LOCKDOWN_DOOR_H__
#define __MSG_HANDLE_NEW_OFFICE_LOCKDOWN_DOOR_H__

#include <string>
#include <unordered_map> 
#include "json/json.h"
#include "AkLogging.h"
#include "AK.Adapt.pb.h"
#include "AkcsPduBase.h"
#include "AdaptDef.h"
#include "AkcsMsgDef.h"
#include "AdaptMQProduce.h"
#include "KafkaParseWebMsg.h"
#include "dbinterface/AlarmDB.h"
#include "dbinterface/Account.h"
#include "dbinterface/PropertyInfo.h"
#include "dbinterface/Log/LogSlice.h"
#include "KafkaConsumerPushTopicHandle.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/office/OfficePersonalAccount.h"

class NewOfficeLockDownControl
{
public:
    NewOfficeLockDownControl() = default;
    static void Handle(const std::string& msg, const std::string& msg_type, const KakfaMsgKV &kv);
};


#endif
