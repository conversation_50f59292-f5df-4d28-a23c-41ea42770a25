#include "NewOfficeNotifyHandler.h"

extern CSADAPT_CONF gstCSADAPTConf;
extern RouteMQProduce* g_nsq_producer;


std::mutex NewOfficeNotifyHandler::office_kafka_mutex_;
std::shared_ptr<AkcsKafkaProducer> NewOfficeNotifyHandler::office_kafka_producer_ = nullptr;

void NewOfficeNotifyHandler::InitDataAnalysisKafkaProducer()
{
    std::unique_lock<std::mutex> lock(NewOfficeNotifyHandler::office_kafka_mutex_);

    if (NewOfficeNotifyHandler::office_kafka_producer_ == nullptr)
    {
        NewOfficeNotifyHandler::office_kafka_producer_ = std::make_shared<AkcsKafkaProducer>(
            gstCSADAPTConf.appbackend_analysis_topic, gstCSADAPTConf.kafka_broker_ip
        );
    }
}

void NewOfficeNotifyHandler::AccountModify(const std::string& notify_msg, const std::string& msg_type, const KakfaMsgKV& kv)
{
    if (NewOfficeNotifyHandler::office_kafka_producer_ == nullptr)
    {
        AK_LOG_WARN << "NewOfficeNotifyHandler AccountModify kafka producer is null. notify_msg=" << notify_msg;
        return;
    }

    if (KafkaWebMsgParse::CheckKeyExist(kv, "office_uuid") == false)
    {
        AK_LOG_WARN << "NewOfficeNotifyHandler AccountModify officeid is empty. notify_msg=" << notify_msg;
        return;
    }

    AK_LOG_INFO << "NewOfficeNotifyHandler AccountModify officeid=" <<  kv.at("office_uuid");
    office_kafka_producer_->ProduceMsgWithLock( kv.at("office_uuid"), notify_msg);
}

void NewOfficeNotifyHandler::RemoteOpenDoor(const std::string& msg, const std::string& msg_type, const KakfaMsgKV& kv)
{
    if (!KafkaWebMsgParse::CheckKeysExist(kv, {"mac", "uid", "relay", "trace_id"}))
    {
        AK_LOG_WARN << "NewOfficeNotifyHandler RemoteOpenDoor mac is null. msg=" << msg << "type=" << msg_type;
        return;
    }

    std::string mac = kv.at("mac");
    project::PROJECT_TYPE project_type = project::OFFICE_NEW;
    AK_LOG_INFO << "NewOfficeNotifyHandler RemoteOpenDoor mac=" << mac;

    AK::Server::P2POpenDoorNotifyMsg post_msg;
    post_msg.set_uid(kv.at("uid"));
    post_msg.set_mac(mac);
    post_msg.set_relay(ATOI(kv.at("relay").c_str()));
    post_msg.set_relay_type((int)OpenDoorRelayType::RELAY);
    post_msg.set_project_type((int)project_type);
    post_msg.set_repost_mac("");    // 新办公没有转发
    post_msg.set_msg_traceid(kv.at("trace_id"));

    AK::BackendCommon::BackendP2PBaseMessage base_msg = BackendP2PMsgControl::CreateP2PBaseMsg(
        AKCS_M2R_P2P_REMOTE_OPENDOOR_MSG,
        TransP2PMsgType::TO_DEV_MAC,
        mac,
        BackendP2PMsgControl::DevProjectTypeToDevType(project_type),
        project_type
    );

    base_msg.mutable_p2popendoornotifymsg2()->CopyFrom(post_msg);
    BackendP2PMsgControl::PushMsg2Route(&base_msg, project_type);
}

void NewOfficeNotifyHandler::RemoteOpenSecurityRelay(const std::string& msg, const std::string& msg_type, const KakfaMsgKV& kv)
{
    if (!KafkaWebMsgParse::CheckKeysExist(kv, {"mac", "uid", "security_relay", "trace_id", "repost"}))
    {
        AK_LOG_WARN << "NewOfficeNotifyHandler RemoteOpenDoor check keys failed. msg=" << msg << "type=" << msg_type;
        return;
    }

    std::string mac = kv.at("mac");
    project::PROJECT_TYPE project_type = project::OFFICE_NEW;
    AK_LOG_INFO << "NewOfficeNotifyHandler OpenSecurityRelay mac=" << mac;

    AK::Server::P2POpenDoorNotifyMsg post_msg;
    post_msg.set_uid(kv.at("uid"));
    post_msg.set_mac(mac);
    post_msg.set_relay(ATOI(kv.at("security_relay").c_str()));
    post_msg.set_relay_type((int)OpenDoorRelayType::SECURITY_RELAY);
    post_msg.set_project_type((int)project_type);
    post_msg.set_repost_mac("");    // 新办公没有转发
    post_msg.set_msg_traceid(kv.at("trace_id"));

    AK::BackendCommon::BackendP2PBaseMessage base_msg = BackendP2PMsgControl::CreateP2PBaseMsg(
        AKCS_M2R_P2P_REMOTE_OPENDOOR_MSG,
        TransP2PMsgType::TO_DEV_MAC,
        mac,
        BackendP2PMsgControl::DevProjectTypeToDevType(project_type),
        project_type
    );

    base_msg.mutable_p2popendoornotifymsg2()->CopyFrom(post_msg);
    BackendP2PMsgControl::PushMsg2Route(&base_msg, project_type);
}

void NewOfficeNotifyHandler::ExportLog(const std::string& msg, const std::string& msg_type, const KakfaMsgKV& kv)
{
    if (!KafkaWebMsgParse::CheckKeyExist(kv, "operator_type"))
    {
        AK_LOG_WARN << "NewOfficeNotifyHandler ExportLog operator_type is null. msg=" << msg << "type=" << msg_type;
        return;
    }

    if (!KafkaWebMsgParse::CheckKeyExist(kv, "record_uuid"))
    {
        AK_LOG_WARN << "NewOfficeNotifyHandler ExportLog record_uuid is null. msg=" << msg << "type=" << msg_type;
        return;
    }

    AK_LOG_INFO << "NewOfficeNotifyHandler NewOfficeExportLog operator_type="
                << kv.at("operator_type") << ", record_uuid=" << kv.at("record_uuid");

    CAkcsPdu pdu;
    pdu.SetMsgBody(msg.data(), msg.size());
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetCommandId(MSG_C2S_NEW_OFFICE_EXPORT_LOG);
    pdu.SetProjectType(project::OFFICE_NEW);
    pdu.SetVersion(50);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return;
}
