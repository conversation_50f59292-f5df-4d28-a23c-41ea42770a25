#ifndef __NEW_OFFICE_NOTIFY_HANDLER_H__
#define __NEW_OFFICE_NOTIFY_HANDLER_H__


#include <mutex>
#include <atomic>
#include <string>
#include <unordered_map> 
#include <condition_variable>

#include "json/json.h"

#include "util.h"
#include "AdaptDef.h"
#include "AkLogging.h"
#include "AkcsMsgDef.h"
#include "AkcsPduBase.h"
#include "AK.Adapt.pb.h"
#include "AK.Server.pb.h"
#include "AdaptMQProduce.h"
#include "KafkaParseWebMsg.h"
#include "AkcsKafkaProducer.h"
#include "Control/IPCControl.h"
#include "AK.BackendCommon.pb.h"
#include "BackendP2PMsgControl.h"

class NewOfficeNotifyHandler
{
public:
    NewOfficeNotifyHandler() {}

private:
    static std::mutex  office_kafka_mutex_;
    static std::shared_ptr<AkcsKafkaProducer> office_kafka_producer_;

public:
    static void InitDataAnalysisKafkaProducer();
    static void AccountModify(const std::string& msg, const std::string& msg_type, const KakfaMsgKV& kv);
    static void RemoteOpenDoor(const std::string& msg, const std::string& msg_type, const KakfaMsgKV& kv);
    static void RemoteOpenSecurityRelay(const std::string& msg, const std::string& msg_type, const KakfaMsgKV& kv);
    static void ExportLog(const std::string& msg, const std::string& msg_type, const KakfaMsgKV& kv);
};

#endif
