#include "NewOfficeOnceAutop.h"

extern CSADAPT_CONF gstCSADAPTConf;
extern RouteMQProduce* g_nsq_producer;

void NewOfficeOnceAutop::Handle(const std::string& notify_msg, const std::string& msg_type, const KakfaMsgKV &kv)
{
    if (!KafkaWebMsgParse::CheckKeyExist(kv, "mac"))
    {
        AK_LOG_WARN << "NewOfficeOnceAutop mac is null. notify_msg = " << notify_msg;
        return;
    }

    if (!KafkaWebMsgParse::CheckKeyExist(kv, "config"))
    {
        AK_LOG_WARN << "NewOfficeOnceAutop config is null. notify_msg = " << notify_msg;
        return;
    }

    std::string mac =  kv.at("mac");
    std::string once_autop = kv.at("config");
    
    AK_LOG_INFO << "Send OnceAutop Command to " << mac << " Success";

    //对下发的config进行转义操作
    char config[CS_COMMON_MSG_AUTOP_SIZE] = "";
    ChangeSpecialXmlChar(config, CS_COMMON_MSG_AUTOP_SIZE, once_autop.c_str(), once_autop.size());

    AK::Adapt::DevConfigUpdateNotify msg;
    msg.set_mac(mac);
    msg.set_config(config);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_UPDATE_TO_DEVICE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);

    return; 
}
