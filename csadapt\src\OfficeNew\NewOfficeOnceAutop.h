#ifndef __MSG_HANDLE_ONCE_AUTOP_H__
#define __MSG_HANDLE_ONCE_AUTOP_H__

#include <string>
#include <unordered_map> 
#include "AkLogging.h"
#include "json/json.h"
#include "BasicDefine.h"
#include "AdaptDef.h"
#include "AkcsMsgDef.h"
#include "AK.Adapt.pb.h"
#include "AkcsPduBase.h"
#include "AdaptMQProduce.h"
#include "KafkaParseWebMsg.h"


class NewOfficeOnceAutop
{
public:
    NewOfficeOnceAutop() = default;
    static void Handle(const std::string& msg, const std::string& msg_type, const KakfaMsgKV &kv);
};

#endif
