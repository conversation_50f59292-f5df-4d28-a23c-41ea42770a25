#include "NewOfficeRebootDevice.h"

extern CSADAPT_CONF gstCSADAPTConf;
extern RouteMQProduce* g_nsq_producer;

void NewOfficeRebootDevice::Handle(const std::string& notify_msg, const std::string& msg_type, const KakfaMsgKV &kv)
{
    if (!KafkaWebMsgParse::CheckKeyExist(kv, "mac"))
    {
        AK_LOG_WARN << "NewOfficeRebootDevice mac is null. notify_msg = " << notify_msg;
        return;
    }
    
    AK_LOG_INFO << "Send RebootDevice Command to Mac = " << kv.at("mac") << " Success";

    AK::Server::P2PAdaptRebootDevMsg msg;
    msg.set_mac(kv.at("mac"));
    
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.Set<PERSON>(50);
    pdu.SetCommandId(MSG_C2S_REBOOT_DEVICE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);

    return; 
}
