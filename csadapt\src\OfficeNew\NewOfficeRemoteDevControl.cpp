#include "NewOfficeRemoteDevControl.h"

extern CSADAPT_CONF gstCSADAPTConf;
extern RouteMQProduce* g_nsq_producer;

static const std::vector<std::string> kKeys = {"mac", "port", "user", "passwd", "ssh_proxy_domain"};

void NewOfficeRemoteDevControl::Handle(const std::string& notify_msg, const std::string& msg_type, const KakfaMsgKV &kv)
{
    if (!KafkaWebMsgParse::CheckKeysExist(kv, kKeys))
    {
        AK_LOG_WARN << "NewOfficeRemoteDevControl checkkeys error, msg = " << notify_msg;
        return;
    }
    
    AK_LOG_INFO << "NewOfficeRemoteDevControl notify_msg = " << notify_msg;

    AK::Server::P2PAdaptCreateRemoteDevContorlMsg msg;
    msg.set_mac(kv.at("mac"));
    msg.set_user(kv.at("user"));
    msg.set_password(kv.at("passwd"));
    msg.set_port(ATOI(kv.at("port").c_str()));
    msg.set_ssh_proxy_domain(kv.at("ssh_proxy_domain"));

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_CREATE_REMOTE_DEV_CONTORL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    
    return;
}
