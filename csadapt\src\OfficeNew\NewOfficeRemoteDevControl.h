#ifndef __MSG_HANDLE_REMOTE_DEV_CONTROL_H__
#define __MSG_HANDLE_REMOTE_DEV_CONTROL_H__

#include <string>
#include <unordered_map> 
#include "util.h"
#include "AkLogging.h"
#include "json/json.h"
#include "BasicDefine.h"
#include "AdaptDef.h"
#include "AkcsMsgDef.h"
#include "AK.Server.pb.h"
#include "AkcsPduBase.h"
#include "AdaptMQProduce.h"
#include "KafkaParseWebMsg.h"


class NewOfficeRemoteDevControl
{
public:
    NewOfficeRemoteDevControl(){}
    static void Handle(const std::string& msg, const std::string& msg_type, const KakfaMsgKV &kv);
};


#endif

