#include "NewOfficeResetDevice.h"

extern CSADAPT_CONF gstCSADAPTConf;
extern RouteMQProduce* g_nsq_producer;

void NewOfficeResetDevice::Handle(const std::string& notify_msg, const std::string& msg_type, const KakfaMsgKV &kv)
{
    if (!KafkaWebMsgParse::CheckKeyExist(kv, "mac"))
    {
        AK_LOG_WARN << "NewOfficeResetDevice mac is null. notify_msg = " << notify_msg;
        return;
    }
        
    AK_LOG_INFO << "Send ResetDevice Command to Mac = " << kv.at("mac") << " Success";

    AK::Server::P2PAdaptResetDevMsg msg;
    msg.set_mac(kv.at("mac"));
    
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVers<PERSON>(50);
    pdu.SetCommandId(MSG_C2S_RESET_DEVICE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);

    return; 
}
