#ifndef __MSG_HANDLE_RESET_DEVICE_H__
#define __MSG_HANDLE_RESET_DEVICE_H__

#include <string>
#include <unordered_map> 
#include "AkLogging.h"
#include "json/json.h"
#include "AdaptDef.h"
#include "AkcsMsgDef.h"
#include "AK.Server.pb.h"
#include "AkcsPduBase.h"
#include "AdaptMQProduce.h"
#include "KafkaParseWebMsg.h"


class NewOfficeResetDevice
{
public:
    NewOfficeResetDevice() = default;
    static void Handle(const std::string& msg, const std::string& msg_type, const KakfaMsgKV &kv);
};

#endif
