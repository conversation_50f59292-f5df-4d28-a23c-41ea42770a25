#include <string>
#include "NewOfficeTransferEmail.h"
#include "AkLogging.h"
#include "KafkaConsumerPushTopicHandle.h"

extern CSADAPT_CONF gstCSADAPTConf;
extern RouteMQProduce* g_nsq_producer;

/*
邮件格式:
        {
            "ver": "1"
            "OEM": "Akuvox",
            "app_type": "email",
            "data": ""
        }
*/

static const std::vector<std::string> kKeys = { "email_type", "email", "language" };

void NewOfficeTransferEmail::Handle(const std::string& web_notify_msg, const std::string& msg_type, const KakfaMsgKV& kv)
{
    if (!KafkaWebMsgParse::CheckKeysExist(kv, kKeys))
    {
        AK_LOG_WARN << "NewOfficeTransferEmail CheckKeysExist error, web_notify_msg = " << web_notify_msg;
        return;
    }

    std::string email = kv.at("email");

    Json::Value root_value;
    root_value["ver"] = "1";
    root_value["OEM"] = "Akuvox";
    root_value["app_type"] = "email";

    Json::Value data_value;
    data_value["gw_code"] = std::to_string(gstCSADAPTConf.gateway_num);
    data_value["url"] = "https://" + std::string(gstCSADAPTConf.web_domain);
    data_value["web_url"] = "https://" + std::string(gstCSADAPTConf.web_domain);

    Json::Value root;
    Json::Reader reader;
    for (const auto& tmpkv : kv)
    {
        if (reader.parse(tmpkv.second, root) && (root.isObject() || root.isArray()))
        {
            // 字符串转成json::value
            data_value[tmpkv.first] = root;
        }
        else
        {
            data_value[tmpkv.first] = tmpkv.second;
        }
    }

    // 创建用户判断邮箱是否为假邮件
    if (data_value["email_type"] == "office_create_uid")
    {
        int is_fake_email = GetAKCSViewInstance()->FindFakeEmail(email);
        data_value["is_fake"] = std::to_string(is_fake_email);
    }
    if (data_value["email_type"] == "office_reset_pwd")
    {
        data_value["web_ip"] = std::string(gstCSADAPTConf.web_domain);
    }
    data_value["project_type"] = std::to_string(project::OFFICE_NEW);

    Json::FastWriter writer;
    root_value["data"] = writer.write(data_value);
    std::string data_json = writer.write(root_value);

    AK::Adapt::SendEmailNotifyMsg send_mail;
    send_mail.set_payload(data_json);
    send_mail.set_key(email);

    AK_LOG_INFO << "NewOfficeTransferEmail: email=" << email << ", data_json=" << data_json;

    CAkcsPdu pdu;
    pdu.SetMsgBody(&send_mail);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_SEND_EMAIL_NOTIFY);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return;
}




