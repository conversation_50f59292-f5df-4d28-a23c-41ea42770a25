#include <stdio.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <errno.h>
#include <assert.h>
#include <string.h>
#include <ctype.h>
#include <string>
#include "util_cstring.h"
#include "AdaptDef.h"
#include "AKCSMsg.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "OfficeIPCControl.h"
#include "UnixSocketControl.h"
#include "AKCSView.h"
#include "AdaptMQProduce.h"
#include "AK.Server.pb.h"
#include "AK.ServerOffice.pb.h"
#include "AkcsMsgDef.h"
#include "util.h"
#include "AkcsCommonDef.h"

#define IPC_RECONNECT_INTERVAL  1000
#define IPC_SELECT_TIMEOUT      2000

extern CSADAPT_CONF gstCSADAPTConf;
extern RouteMQProduce* g_nsq_producer;
static OfficeIPCControl* instance = nullptr;

OfficeIPCControl* GetOfficeIPCControlInstance()
{
    return OfficeIPCControl::GetInstance();
}

OfficeIPCControl::OfficeIPCControl()
{

}
OfficeIPCControl::~OfficeIPCControl()
{

}
OfficeIPCControl* OfficeIPCControl::instance = NULL;

OfficeIPCControl* OfficeIPCControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new OfficeIPCControl();
    }

    return instance;
}

int OfficeIPCControl::SendOfficeCreateUidMail(CSP2A_USER_CREATE_INFO* usercreateinfo)
{
    AK::Server::P2PAdaptCreateUidMailMsg msg;
    msg.set_user(usercreateinfo->szUser);
    msg.set_pwd(usercreateinfo->szPwd);
    msg.set_email(usercreateinfo->szEmail);
    msg.set_qrcode_body(usercreateinfo->szQRCodeBody);
    msg.set_qrcode_url(usercreateinfo->szQRCodeUrl);
    msg.set_srv_web_url(usercreateinfo->szServerWebUrl);
    msg.set_is_fake(usercreateinfo->is_fake);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetProjectType(project::OFFICE);
    pdu.SetCommandId(MSG_C2S_OFFICE_SEND_CREATE_UID_MAIL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int OfficeIPCControl::SendPMOfficeRenewEmail(const CSP2A_PM_INFO* pminfo)
{
    AK::ServerOffice::P2PAdaptPMOfficeRenewMsg msg;
    msg.set_community(pminfo->szCommunity);
    msg.set_email(pminfo->szEmail);
    msg.set_pm_name(pminfo->szName);
    msg.set_account_num(pminfo->nAccountNum);
    msg.set_list(pminfo->list);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetProjectType(project::OFFICE);
    pdu.SetCommandId(MSG_C2S_OFFICE_SEND_ACCOUNT_RENEW_MAIL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int OfficeIPCControl::SendPmOfficeAccountWillExpire(const CSP2A_PM_INFO* pminfo)
{
    AK::Server::P2PAdaptPMAccountWillExpireMsg msg;
    msg.set_community(pminfo->szCommunity);
    msg.set_email(pminfo->szEmail);
    msg.set_pm_name(pminfo->szName);
    msg.set_account_num(pminfo->nAccountNum);
    msg.set_before(pminfo->nBefore);
    msg.set_list(pminfo->list);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetProjectType(project::OFFICE);
    pdu.SetCommandId(MSG_C2S_OFFICE_SEND_PM_ACCOUNT_WILL_EXPIRE_MAIL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int OfficeIPCControl::SendPmOfficeAccountExpire(const CSP2A_PM_INFO* pminfo)
{
    AK::ServerOffice::PMAppExpire msg;
    msg.set_community(pminfo->szCommunity);
    msg.set_email(pminfo->szEmail);
    msg.set_name(pminfo->szName);
    msg.set_account_num(pminfo->nAccountNum);
    msg.set_list(pminfo->list);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetProjectType(project::OFFICE);
    pdu.SetCommandId(MSG_C2S_OFFICE_SEND_PM_ACCOUNT_EXPIRE_MAIL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int OfficeIPCControl::SendOfficePerResetPwdMail(CSP2A_USER_EAMIL_INFO* useremailinfo)
{
    AK::Server::P2PAdaptResetPwdMailMsg msg;
    msg.set_web_ip(useremailinfo->szWebIP);
    msg.set_user(useremailinfo->szUser);
    msg.set_email(useremailinfo->szEmail);
    msg.set_token(useremailinfo->szToken);
    msg.set_role_type(useremailinfo->szRoleType);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetProjectType(project::OFFICE);
    pdu.SetCommandId(MSG_C2S_OFFICE_SEND_RESET_PWD_MAIL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int OfficeIPCControl::SendOfficePerChangePwdMail(CSP2A_USER_CREATE_INFO* usercreateinfo)
{
    AK::Server::P2PAdaptPerChangePwdMailMsg msg;
    msg.set_user(usercreateinfo->szUser);
    msg.set_pwd(usercreateinfo->szPwd);
    msg.set_email(usercreateinfo->szEmail);
    msg.set_qrcode_body(usercreateinfo->szQRCodeBody);
    msg.set_qrcode_url(usercreateinfo->szQRCodeUrl);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetProjectType(project::OFFICE);
    pdu.SetCommandId(MSG_C2S_OFFICE_SEND_CHANGE_PWD_MAIL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}


int OfficeIPCControl::SendPmFeatureWillExpire(const CSP2A_PM_EXPIRE* expire)
{
    AK::Server::P2PAdaptPmFeatureWillExpireMsg msg;
    msg.set_user_name(expire->username);
    msg.set_email(expire->email);
    msg.set_before(expire->nbefore);
    msg.set_location(expire->community);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetProjectType(project::OFFICE);
    pdu.SetCommandId(MSG_C2S_OFFICE_SEND_PM_FEATURE_WILL_EXPIRE_MAIL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int OfficeIPCControl::SendInstallerFeatureWillExpire(const CSP2A_INSTALLER_EXPIRE* expire)
{
    AK::Server::P2PAdaptInstallerFeatureWillExpireMsg msg;
    msg.set_user_name(expire->szUserName);
    msg.set_email(expire->szEmail);
    msg.set_before(expire->nBefore);
    msg.set_location(expire->community);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetProjectType(project::OFFICE);
    pdu.SetCommandId(MSG_C2S_OFFICE_SEND_INSTALLER_FEATURE_WILL_EXPIRE_MAIL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int OfficeIPCControl::SendPmFeatureExpire(const CSP2A_PM_EXPIRE* expire)
{
    AK::Server::P2PAdaptPmFeatureWillExpireMsg msg;
    msg.set_user_name(expire->username);
    msg.set_email(expire->email);
    msg.set_before(expire->nbefore);
    msg.set_location(expire->community);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetProjectType(project::OFFICE);
    pdu.SetCommandId(MSG_C2S_OFFICE_SEND_PM_FEATURE_EXPIRE_MAIL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int OfficeIPCControl::SendInstallerFeatureExpire(const CSP2A_INSTALLER_EXPIRE* expire)
{
    AK::Server::P2PAdaptInstallerFeatureWillExpireMsg msg;
    msg.set_user_name(expire->szUserName);
    msg.set_email(expire->szEmail);
    msg.set_before(expire->nBefore);
    msg.set_location(expire->community);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetProjectType(project::OFFICE);
    pdu.SetCommandId(MSG_C2S_OFFICE_SEND_INSTALLER_FEATURE_EXPIRE_MAIL);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
    return 0;
}

int OfficeIPCControl::SendUserAddNewSite(const CSP2A_PER_ADD_NEWSITE& per_add_new_site)
{
    AK::Server::P2PSendUserAddNewSite user_add_new_site;
    user_add_new_site.set_name(per_add_new_site.name);
    user_add_new_site.set_email(per_add_new_site.email);
    user_add_new_site.set_project_name(per_add_new_site.project_name);
    user_add_new_site.set_apt_num(per_add_new_site.apt_num);
    AK_LOG_INFO << "SendUserAddNewSite=" << user_add_new_site.DebugString();

    CAkcsPdu pdu;
    pdu.SetMsgBody(&user_add_new_site);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetProjectType(project::OFFICE);
    pdu.SetCommandId(MSG_C2S_OFFICE_SEND_USER_ADD_NEWSITE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSADAPTConf.szNSQRouteTopic);
	return 0;
}




