#ifndef __OFFICE_IPC_CONTROL_H__
#define __OFFICE_IPC_CONTROL_H__

#include "stdint.h"

typedef struct CSP2A_REBOOT_DEVICE_T  CSP2A_REBOOT_DEVICE;

//modified by chenyc,2019-03-05, 由原先的跟csmain的直接通信改成经过csroute,ipc的名称暂时不变.
class OfficeIPCControl
{
public:
    OfficeIPCControl();
    virtual ~OfficeIPCControl();
    static OfficeIPCControl* GetInstance();
    int SendOfficeCreateUidMail(CSP2A_USER_CREATE_INFO* usercreateinfo);
    int SendPMOfficeRenewEmail(const CSP2A_PM_INFO* pminfo);
    int SendPmOfficeAccountWillExpire(const CSP2A_PM_INFO* pminfo);
    int SendOfficePerResetPwdMail(CSP2A_USER_EAMIL_INFO* useremailinfo);
    int SendOfficePerChangePwdMail(CSP2A_USER_CREATE_INFO* usercreateinfo);

    int SendPmOfficeAccountExpire(const CSP2A_PM_INFO* pminfo);
    int SendPmFeatureWillExpire(const CSP2A_PM_EXPIRE* expire);
    int SendInstallerFeatureWillExpire(const CSP2A_INSTALLER_EXPIRE* expire);
    int SendPmFeatureExpire(const CSP2A_PM_EXPIRE* expire);
    int SendInstallerFeatureExpire(const CSP2A_INSTALLER_EXPIRE* expire);
    int SendUserAddNewSite(const CSP2A_PER_ADD_NEWSITE& per_add_new_site);
private:
    static OfficeIPCControl* instance;
};
#endif //__IPC_CONTROL_H__

