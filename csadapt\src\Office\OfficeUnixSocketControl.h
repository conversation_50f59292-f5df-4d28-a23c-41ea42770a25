#ifndef __OFFICE_WEB_MEESSAGE_HANDLE_H__
#define __OFFICE_WEB_MEESSAGE_HANDLE_H__

#include <vector>
#include <map>
#include <set>
#include <unordered_set>
#include <memory>
#include <functional>

class OfficeUnixMsgControl
{
public:
    OfficeUnixMsgControl();
    int OnSocketMsg(void* msg_buf, unsigned int len);    

    static OfficeUnixMsgControl* Instance();
    
    void OnCreateUid(void* msg_buf, unsigned int len);
    void OnAccountRenew(void* msg_buf, unsigned int len);
    void OnPMAccountWillExpire(void* msg_buf, unsigned int len);
    void OnResetPwd(void* msg_buf, unsigned int len);
    void OnChangePwd(void* msg_buf, unsigned int len);
    void OnPMAccountExpire(void* msg_buf, unsigned int len);
    void OnPMFeatureWillExpire(void* msg_buf, unsigned int len);
    void OnInstallerFeatureWillExpire(void* msg_buf, unsigned int len);
    void OnPMFeatureExpire(void* msg_buf, unsigned int len);
    void OnInstallerFeatureExpire(void* msg_buf, unsigned int len);
    void OnUserAddNewSite(void* msg_buf, unsigned int len);
    void OnOfficeAlarmDeal(void* msg_buf, unsigned int len);
    void OnNewOfficeAlarmDeal(void* msg_buf, unsigned int len);

private:
    static OfficeUnixMsgControl* office_unix_msg_instance_;
};

#endif //__OFFICE_MEESSAGE_HANDLE_H__
