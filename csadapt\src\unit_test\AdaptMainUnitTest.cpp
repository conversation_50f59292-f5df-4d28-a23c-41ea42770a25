﻿//#define CATCH_CONFIG_MAIN
#define CATCH_CONFIG_RUNNER
#include <catch2/catch.hpp>
#include <catch2/catch_reporter_teamcity.hpp>
#include <catch2/catch_reporter_tap.hpp>
#include <catch2/catch_reporter_sonarqube.hpp>

#include <stdio.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <errno.h>
#include <pthread.h>
#include <assert.h>
#include <string.h>
#include <ctype.h>
#include <string>
#include <iostream>
#include <sstream>
#include <thread>
#include <fcntl.h>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "CharChans.h"
#include "AdaptDef.h"
#include "AKCSMsg.h"
#include "Rldb.h"
#include "UnixSocketControl.h"
#include "AKCSView.h"
#include "ConfigFileReader.h"
#include "redis/PubSubManager.h"
#include <unistd.h>
#include <signal.h>
#include "ConnectionPool.h"
#include "AkLogging.h"
#include <evpp/evnsq/producer.h>
#include <evpp/event_loop.h>
#include "AdaptMQProduce.h"
#include "AdaptEtcd.h"
#include "AdaptMQProduce.h"
#include "AES256.h"


CSADAPT_CONF gstCSADAPTConf;
PubSubManager* g_pub_sub_mng_ptr = nullptr;
evnsq::Producer* g_nsq_pub_mng_ptr = nullptr;

#define CSADAPD_CONF_FILE "../conf/csadapt.conf"
//#define CSADAPD_CONF_FILE "/usr/local/akcs/csadapt/conf/csadapt.conf"


/* 初始化数据库连接 */
int DaoInit()
{
    ConnPool* gConnPool = GetDBConnPollInstance();
    if (NULL == gConnPool)
    {
        AK_LOG_WARN << "DaoInit failed.";
        return -1;
    }
    gConnPool->Init(gstCSADAPTConf.szDbIP, gstCSADAPTConf.szDbUserName, gstCSADAPTConf.szDbPassword, gstCSADAPTConf.szDbDatabase, gstCSADAPTConf.nDbPort, MAX_RLDB_CONN, "csadapt");
    return 0;
}
void glogInit()
{
    google::InitGoogleLogging("csadaptTest");
    google::SetLogDestination(google::GLOG_INFO, "/var/log/csadaptlog/INFO");
    google::SetLogDestination(google::GLOG_WARNING, "/var/log/csadaptlog/log/WARN");
    google::SetLogDestination(google::GLOG_ERROR, "/var/log/csadaptlog/log/ERROR");
    google::SetLogDestination(google::GLOG_FATAL, "/var/log/csadaptlog/log/FATAL");
    FLAGS_logbufsecs = 0;
    google::SetStderrLogging(google::GLOG_WARNING); // 输出到标准输出的时候大于INFO级别的都输出;
    FLAGS_max_log_size = 50;    //单日志文件最大50M
}

void ConfInit()
{
    memset(&gstCSADAPTConf, 0, sizeof(CSADAPT_CONF));
    CConfigFileReader config_file(CSADAPD_CONF_FILE);

    Snprintf(gstCSADAPTConf.szCsadaptOuterIP, sizeof(gstCSADAPTConf.szCsadaptOuterIP), config_file.GetConfigName("csadapt_outerip"));
    const char* log_level = config_file.GetConfigName("csadapt_loglevel");
    gstCSADAPTConf.nLogLevel = ATOI(log_level);

    Snprintf(gstCSADAPTConf.szCspbxOuterIP, sizeof(gstCSADAPTConf.szCspbxOuterIP), config_file.GetConfigName("cspbx_ip"));
    Snprintf(gstCSADAPTConf.szCspbxOuterPort, sizeof(gstCSADAPTConf.szCspbxOuterPort), config_file.GetConfigName("cspbx_port"));

    Snprintf(gstCSADAPTConf.szDbIP, sizeof(gstCSADAPTConf.szDbIP), config_file.GetConfigName("db_ip"));
    Snprintf(gstCSADAPTConf.szDbUserName, sizeof(gstCSADAPTConf.szDbUserName), config_file.GetConfigName("db_username"));
    Snprintf(gstCSADAPTConf.szDbPassword, sizeof(gstCSADAPTConf.szDbPassword), config_file.GetConfigName("db_passwd"));
    Snprintf(gstCSADAPTConf.szDbDatabase, sizeof(gstCSADAPTConf.szDbDatabase), config_file.GetConfigName("db_database"));
    //Snprintf(gstCSADAPTConf.szDbSocketFile, sizeof(gstCSADAPTConf.szDbSocketFile), config_file.GetConfigName("db_socketfile"));

    const char* db_port = config_file.GetConfigName("db_port");
    gstCSADAPTConf.nDbPort = ATOI(db_port);

    const char* encrypt = config_file.GetConfigName("noencrypt");
    gstCSADAPTConf.nNoEncrypt = ATOI(encrypt);

    Snprintf(gstCSADAPTConf.web_domain, sizeof(gstCSADAPTConf.web_domain), config_file.GetConfigName("csadapt_outerdomain"));

    /*读取OEM特殊配置文件，直接加入配置文件选项*/
    FILE* pFstream = nullptr;
    if ((pFstream = fopen("../conf/oem_config.conf", "r")) != nullptr)
    {
        fread(gstCSADAPTConf.szOEMConfig, sizeof(char), sizeof(gstCSADAPTConf.szOEMConfig), pFstream);
        fclose(pFstream);
    }
    Snprintf(gstCSADAPTConf.szNSQTopicForDelPic, sizeof(gstCSADAPTConf.szNSQTopicForDelPic), config_file.GetConfigName("nsq_delpic_topic"));
    Snprintf(gstCSADAPTConf.szEtcdServerAddr, sizeof(gstCSADAPTConf.szEtcdServerAddr), config_file.GetConfigName("etcd_srv_net"));
    Snprintf(gstCSADAPTConf.szNSQRouteTopic, sizeof(gstCSADAPTConf.szNSQRouteTopic), config_file.GetConfigName("nsq_route_topic"));

    Snprintf(gstCSADAPTConf.szBeanStalkAddr, sizeof(gstCSADAPTConf.szBeanStalkAddr), config_file.GetConfigName("beanstalkd_ip"));

    Snprintf(gstCSADAPTConf.szSshProxyDomain, sizeof(gstCSADAPTConf.szSshProxyDomain), config_file.GetConfigName("remote_config_domain"));
    Snprintf(gstCSADAPTConf.web_ip, sizeof(gstCSADAPTConf.web_ip), config_file.GetConfigName("web_ip"));
}

int main(int argc, char* argv[])
{
    Catch::Session session;

    // 通过修改configData()对象来设置参数
    session.configData().reporterName = "compact";
    session.configData().showDurations = Catch::ShowDurations::OrNot::Always;

    // catch解析输入的命令行参数
    int ret_code = session.applyCommandLine(argc, argv);
    if (ret_code != 0) // Indicates a command line error
        return ret_code;

    // 在解析完输入参数后，这里可修改configData()以重写覆盖参数
    session.configData().reporterName = "xml";

    ConfInit();
    DaoInit();
    glogInit();

    int num_failed = session.run();

    return num_failed;
}


