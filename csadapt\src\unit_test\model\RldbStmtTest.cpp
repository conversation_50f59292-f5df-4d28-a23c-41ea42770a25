﻿#include <string>
#include <sstream>
#include <map>
#include <catch2/catch.hpp>
#include "AkLogging.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "RldbStmt.h"


using namespace std;

extern int DaoInit();
extern void ConfInit();


void QueryMemoyLeakTest()
{
    AK_LOG_INFO << "QueryCase1 begin";
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* rldb_conn = conn.get();
    if (NULL == rldb_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }

    const char* sql = "select ID, MsgId, Account, Token, Grade, DevCount, Message, CreateTime, UpdateTime "
                      " from AccountUT where MsgId = ? and Grade = ?";
    auto rldb_stmt = rldb_conn->Prepare(sql);
    if (nullptr == rldb_stmt)
    {
        ReleaseDBConn(conn);
        return;
    }

    int64_t msg_id = 1001;
    rldb_stmt->BindInt(0, msg_id);

    int32_t grade_in = 3;
    rldb_stmt->BindTinyInt(1, grade_in);

    int k = 0;
    int64_t id = 0;
    rldb_stmt->BindOutInt64(k++, &id);
    int64_t msg_id_out = 0;
    rldb_stmt->BindOutInt64(k++, &msg_id_out);
    char account[64];
    rldb_stmt->BindOutString(k++, account, sizeof(account));
    char token[1025];
    rldb_stmt->BindOutString(k++, token, sizeof(token));
    int64_t grade = 0;
    rldb_stmt->BindOutInt64(k++, &grade);
    int64_t dev_count = 0;
    rldb_stmt->BindOutInt64(k++, &dev_count);
    char message[1025];
    rldb_stmt->BindOutString(k++, message, sizeof(message));
    char create_time[50];
    rldb_stmt->BindOutString(k++, create_time, sizeof(create_time));
    char update_time[50];
    rldb_stmt->BindOutString(k++, update_time, sizeof(update_time));

    int64_t affected_rows = rldb_stmt->Execute();
    AK_LOG_INFO << "Query rows:" << affected_rows;

    int rows = 1;
    while (rldb_stmt->Fetch())
    {
        AK_LOG_INFO << "Rows=" << rows << ";ID=" << id << ";Msg_id=" << msg_id_out << ";token=" << token
                    << ";grade=" << grade << ";dev_count=" << dev_count << ";message=" << message
                    << ";create_time=" << create_time << ";update_time=" << update_time;
        ++rows;
    }

    ReleaseDBConn(conn);
}

int DaoCheckUser(std::string strUser)
{
    std::stringstream streamSQL;
    streamSQL << "SELECT Account,Passwd,ExpireTime,Role,unix_timestamp(ExpireTime), unix_timestamp(),Active,ID,ParentID FROM PersonalAccount "
              << "WHERE (Account = ?  or Email = ? or MobileNumber = ?) limit 1";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* pTmpConn = conn.get();
    if (NULL == pTmpConn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    auto stmt = pTmpConn->Prepare(streamSQL.str());
    if (nullptr == stmt)
    {
        ReleaseDBConn(conn);
        return -1;
    }

    stmt->BindString(0, strUser);
    stmt->BindString(1, strUser);
    stmt->BindString(2, strUser);

    char szUid[64] = "";
    char szPasswd[65] = "";
    char szExpireTime[64] = "";
    char szExpireTimestamp[64] = "";
    char szCurTime[64] = "";
    int nActive = 0;
    unsigned int nExpTime = 0;
    unsigned int nCurTime = 0;
    int role = 0, id = 0, parent_id = 0;

    stmt->BindOutString(0, szUid, sizeof(szUid));
    stmt->BindOutString(1, szPasswd, sizeof(szPasswd));
    stmt->BindOutString(2, szExpireTime, sizeof(szExpireTime));
    stmt->BindOutInt32(3, &role);
    stmt->BindOutString(4, szExpireTimestamp, sizeof(szExpireTimestamp));
    stmt->BindOutString(5, szCurTime, sizeof(szCurTime));
    stmt->BindOutInt32(6, &nActive);
    stmt->BindOutInt32(7, &id);
    stmt->BindOutInt32(8, &parent_id);

    stmt->Execute();
    if (stmt->Fetch())
    {
        AK_LOG_INFO << "uid=" << szUid << ";passwd=" << szPasswd << ";expire_time=" << szExpireTime << ";role=" << role <<
                    ";expire_timestamp=" << szExpireTimestamp << ";cur_timestamp=" << szCurTime << ";active=" << nActive <<
                    ";id=" << id << ";parent_id=" << parent_id;
    }
    else
    {
        AK_LOG_INFO << "Fetch no result";
    }
	ReleaseDBConn(conn);
    return 0;
}

int DaoCheckToken(const std::string &strToken)
{
	const int ERR_TOKEN_INVALID = -10;
	int64_t cur_time = 0;
	int64_t exp_time = 0;
	RldbPtr conn = GetDBConnPollInstance()->GetConnection();
	CRldb* pTmpConn = conn.get();
	if (NULL == pTmpConn)
	{
		return ERR_TOKEN_INVALID;
	}
	std::stringstream streamSQL;
	streamSQL << "/*master*/select Account,AppTokenEt,unix_timestamp() from Token where AppToken= ? limit 1";

	auto stmt = pTmpConn->Prepare(streamSQL.str());
	if (nullptr == stmt)
	{
		ReleaseDBConn(conn);
		return ERR_TOKEN_INVALID;
	}

	char account[33];
	(void)stmt->BindString(0, strToken);
	(void)stmt->BindOutString(0, account, sizeof(account));
	(void)stmt->BindOutInt64(1, &exp_time);
	(void)stmt->BindOutInt64(2, &cur_time);

	(void)stmt->Execute();

	if (!stmt->Fetch())
	{
		ReleaseDBConn(conn);
		AK_LOG_INFO << "Fetch no result";
		return ERR_TOKEN_INVALID;
	}
	else
	{
		ReleaseDBConn(conn);
		AK_LOG_INFO << "account=" << account << ";exp_time=" << exp_time << ";cur_time=" << cur_time;
		return 0;
	}
}

int DaoTokenContinuation(const std::string strUser, const std::string strPasswd, const std::string token)
{
	const int ERR_PASSWD_INVALID = -10;
    const int TOKEN_LIMITED_TIME = 604800; //限制token时效7天

    if(strUser.size() == 0 || strPasswd.size() == 0 || token.size() == 0)
    {
       return ERR_PASSWD_INVALID; 
    }
    int64_t limited_time = TOKEN_LIMITED_TIME; //限制token时效7天
    std::stringstream streamSQL;
    streamSQL << "SELECT Account,Passwd,unix_timestamp() FROM PersonalAccount "
              << "WHERE (Account = ? or Email = ? or MobileNumber = ?) limit 1";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* pTmpConn = conn.get();
    if (NULL == pTmpConn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

	auto stmt = pTmpConn->Prepare(streamSQL.str());
	if (nullptr == stmt)
	{
		ReleaseDBConn(conn);
		return -1;
	}
	(void)stmt->BindString(0, strUser);
	(void)stmt->BindString(1, strUser);
	(void)stmt->BindString(2, strUser);

	char szUid[64] = "";
	char szPasswd[64] = "";
	char szCurTime[64] = "";
	(void)stmt->BindOutString(0, szUid, sizeof(szUid));
	(void)stmt->BindOutString(1, szPasswd, sizeof(szPasswd));
	(void)stmt->BindOutString(2, szCurTime, sizeof(szCurTime));

	(void)stmt->Execute();
    if (stmt->Fetch())
    {
		AK_LOG_INFO << "uid=" << szUid << ";passwd=" << szPasswd << ";cur_time=" << szCurTime;
        std::string password = szPasswd;

        limited_time = limited_time + ATOI(szCurTime);
		AK_LOG_INFO << "limit_time=" << limited_time;

		std::string update_sql = "UPDATE Token set AppTokenEt = ? WHERE Account = ? and AppToken= ? ";
		auto update_stmt = pTmpConn->Prepare(update_sql);
		if (nullptr == update_stmt)
		{
			ReleaseDBConn(conn);
            return ERR_PASSWD_INVALID;
		}

		(void)update_stmt->BindInt(0, limited_time);
		(void)update_stmt->BindString(1, szUid);
		(void)update_stmt->BindString(2, token);
		int64_t exec_rows = update_stmt->Execute();
		AK_LOG_INFO << "exec_rows=" << exec_rows;

        ReleaseDBConn(conn);
        return 0;
    }

    ReleaseDBConn(conn);
    return -9;
}

/**
 *
DROP TABLE IF EXISTS AccountUT;
CREATE TABLE `AccountUT` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `MsgId` int(10) unsigned NOT NULL,
  `Account` char(64) NOT NULL,
  `Token` varchar(1024) DEFAULT '',
  `Grade` tinyint(1) DEFAULT 0 COMMENT '等级:1超;11区域;21小区;22个人终端;31物业',
  `DevCount` SMALLINT(11),
  `Message` TEXT  NULL DEFAULT '',
  `CreateTime` datetime DEFAULT NULL,
  `UpdateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8;
 *
 * <AUTHOR> (2021/3/22)
 *
 * @param "RldbStmtTest"
 * @param "IRUD"
 */
TEST_CASE("RldbStmtTest", "[IRUD]")
{
    ConfInit();
    DaoInit();

    SECTION("InsertNoBind")
    {
        AK_LOG_INFO << "InsertNoBind begin";

        RldbPtr conn = GetDBConnPollInstance()->GetConnection();
        CRldb* rldb_conn = conn.get();
        if (NULL == rldb_conn)
        {
            AK_LOG_WARN << "Get DB conn failed.";
            return;
        }

        const char* sql = "insert into AccountUT values(null, 1001, 'ak2021', 'Token12345', 3, 120, 'Hello World', '2020-09-19 17:54:38', '')";
        auto rldb_stmt = rldb_conn->Prepare(sql);
        if (nullptr == rldb_stmt)
        {
            ReleaseDBConn(conn);
            return;
        }
        AK_LOG_INFO << "Use Count=" << rldb_stmt.use_count();

        int64_t affected_rows = rldb_stmt->Execute();
        AK_LOG_INFO << "Insert rows:" << affected_rows;

        ReleaseDBConn(conn);
    }

    SECTION("InsertFail-Cannot be null")
    {
        AK_LOG_INFO << "InsertFail begin";

        RldbPtr conn = GetDBConnPollInstance()->GetConnection();
        CRldb* rldb_conn = conn.get();
        if (NULL == rldb_conn)
        {
            AK_LOG_WARN << "Get DB conn failed.";
            return;
        }

        const char* sql = "insert into AccountUT values(null, null, 'ak2021', 'Token12345', -1, 120, '', '', null)";
        auto rldb_stmt = rldb_conn->Prepare(sql);
        if (nullptr == rldb_stmt)
        {
            ReleaseDBConn(conn);
            return;
        }

        int64_t affected_rows = rldb_stmt->Execute();
        AK_LOG_INFO << "Insert rows:" << affected_rows;

        ReleaseDBConn(conn);
    }

    SECTION("InsertFail-Prepare")
    {
        AK_LOG_INFO << "InsertFail-Prepare begin";

        RldbPtr conn = GetDBConnPollInstance()->GetConnection();
        CRldb* rldb_conn = conn.get();
        if (NULL == rldb_conn)
        {
            AK_LOG_WARN << "Get DB conn failed.";
            return;
        }

        const char* sql = "insert into AccountUT valuess(null, '10001', 'ak2021', 'Token12345', -1, 120, '', '', null)";
        auto rldb_stmt = rldb_conn->Prepare(sql);
        if (nullptr == rldb_stmt)
        {
            ReleaseDBConn(conn);
            AK_LOG_WARN << "Prepare failed:" << sql;
            return;
        }

        int64_t affected_rows = rldb_stmt->Execute();
        AK_LOG_INFO << "Insert rows:" << affected_rows;

        ReleaseDBConn(conn);
    }

    SECTION("Insert")
    {
        AK_LOG_INFO << "Insert begin";
        RldbPtr conn = GetDBConnPollInstance()->GetConnection();
        CRldb* rldb_conn = conn.get();
        if (NULL == rldb_conn)
        {
            AK_LOG_WARN << "Get DB conn failed.";
            return;
        }

        const char* sql = "insert into AccountUT values(null, ?, ?, ?, ?, ?, ?, ?, ?)";
        auto rldb_stmt = rldb_conn->Prepare(sql);
        if (nullptr == rldb_stmt)
        {
            ReleaseDBConn(conn);
            return;
        }
        int64_t id = 1001;
        rldb_stmt->BindInt(0, id);

        char account[65];
        snprintf(account,  sizeof(account), "%s", "akuvox2020");
        rldb_stmt->BindString(1, account);

        char token[1025];
        snprintf(token, sizeof(token), "%s", "20200826204155cloudinterfaceauto");
        rldb_stmt->BindString(2,  token);

        int grade = 1;
        rldb_stmt->BindTinyInt(3, grade);

        int dev_count = 86;
        rldb_stmt->BindInt(4, dev_count);

        string message = "If you want something different in your binary package than you get in the standard package distro for your OS";
        rldb_stmt->BindString(5, message);

        char create_time[56] = "2020-09-18 17:54:38";
        rldb_stmt->BindString(6, create_time);

        char update_time[56] = "2020-09-19 17:54:38";
        rldb_stmt->BindString(7, update_time);

        int64_t affected_rows = rldb_stmt->Execute();
        AK_LOG_INFO << "Insert rows:" << affected_rows;
        ReleaseDBConn(conn);
    }

    SECTION("UpdateError")
    {
        AK_LOG_INFO << "UpdateError begin";
        RldbPtr conn = GetDBConnPollInstance()->GetConnection();
        CRldb* rldb_conn = conn.get();
        if (NULL == rldb_conn)
        {
            AK_LOG_WARN << "Get DB conn failed.";
            return;
        }

        const char* sql = "update AccountUT set Grade = ? where MsgId = ?";
        auto rldb_stmt = rldb_conn->Prepare(sql);
        if (nullptr == rldb_stmt)
        {
            ReleaseDBConn(conn);
            return;
        }

        int grade = 11;
        rldb_stmt->BindTinyInt(0, grade);

        int64_t msg_id = 1001;
        //rldb_stmt->BindInt(1, msg_id);

        int64_t affected_rows = rldb_stmt->Execute();
        AK_LOG_INFO << "Update rows:" << affected_rows;

        ReleaseDBConn(conn);
    }

    SECTION("UpdateErrorCase2")
    {
        AK_LOG_INFO << "UpdateErrorCase2 begin";
        RldbPtr conn = GetDBConnPollInstance()->GetConnection();
        CRldb* rldb_conn = conn.get();
        if (NULL == rldb_conn)
        {
            AK_LOG_WARN << "Get DB conn failed.";
            return;
        }

        const char* sql = "update AccountUT set Grade = ? where MsgId = ?";
        auto rldb_stmt = rldb_conn->Prepare(sql);
        if (nullptr == rldb_stmt)
        {
            ReleaseDBConn(conn);
            return;
        }

        //int grade = 11;
        //rldb_stmt->BindTinyInt(0, grade);

        //int64_t msg_id = 1001;
        //rldb_stmt->BindInt(1, msg_id);

        int64_t affected_rows = rldb_stmt->Execute();
        AK_LOG_INFO << "Update rows:" << affected_rows;

        ReleaseDBConn(conn);
    }

    SECTION("Update")
    {
        AK_LOG_INFO << "Update begin";
        RldbPtr conn = GetDBConnPollInstance()->GetConnection();
        CRldb* rldb_conn = conn.get();
        if (NULL == rldb_conn)
        {
            AK_LOG_WARN << "Get DB conn failed.";
            return;
        }

        const char* sql = "update AccountUT set Grade = ? where MsgId = ?";
        auto rldb_stmt = rldb_conn->Prepare(sql);
        if (nullptr == rldb_stmt)
        {
            ReleaseDBConn(conn);
            return;
        }

        int grade = 3;
        rldb_stmt->BindTinyInt(0, grade);

        int64_t msg_id = 1001;
        rldb_stmt->BindInt(1, msg_id);

        int64_t affected_rows = rldb_stmt->Execute();
        AK_LOG_INFO << "Update rows:" << affected_rows;

        ReleaseDBConn(conn);
    }

    SECTION("QueryCase1")
    {
        AK_LOG_INFO << "QueryCase1 begin";
        RldbPtr conn = GetDBConnPollInstance()->GetConnection();
        CRldb* rldb_conn = conn.get();
        if (NULL == rldb_conn)
        {
            AK_LOG_WARN << "Get DB conn failed.";
            return;
        }

        const char* sql = "select ID, MsgId, Account, Token, Grade, DevCount, Message, CreateTime, UpdateTime "
                          " from AccountUT where MsgId = ? and Grade = ?";
        auto rldb_stmt = rldb_conn->Prepare(sql);
        if (nullptr == rldb_stmt)
        {
            ReleaseDBConn(conn);
            return;
        }

        int64_t msg_id = 1001;
        rldb_stmt->BindInt(0, msg_id);

        int32_t grade_in = 3;
        rldb_stmt->BindTinyInt(1, grade_in);

        int k = 0;
        int64_t id = 0;
        rldb_stmt->BindOutInt64(k++, &id);
        int64_t msg_id_out = 0;
        rldb_stmt->BindOutInt64(k++, &msg_id_out);
        char account[64];
        rldb_stmt->BindOutString(k++, account, sizeof(account));
        char token[1025];
        rldb_stmt->BindOutString(k++, token, sizeof(token));
        int64_t grade = 0;
        rldb_stmt->BindOutInt64(k++, &grade);
        int64_t dev_count = 0;
        rldb_stmt->BindOutInt64(k++, &dev_count);
        char message[1025];
        rldb_stmt->BindOutString(k++, message, sizeof(message));
        char create_time[50];
        rldb_stmt->BindOutString(k++, create_time, sizeof(create_time));
        char update_time[50];
        rldb_stmt->BindOutString(k++, update_time, sizeof(update_time));

        int64_t affected_rows = rldb_stmt->Execute();
        AK_LOG_INFO << "Query rows:" << affected_rows;

        int rows = 1;
        while (rldb_stmt->Fetch())
        {
            AK_LOG_INFO << "Rows=" << rows << ";ID=" << id << ";Msg_id=" << msg_id_out << ";token=" << token
                        << ";grade=" << grade << ";dev_count=" << dev_count << ";message=" << message
                        << ";create_time=" << create_time << ";update_time=" << update_time;
            ++rows;
        }

        ReleaseDBConn(conn);
    }

    SECTION("QueryCase2")
    {
        AK_LOG_INFO << "QueryCase2 begin";
        RldbPtr conn = GetDBConnPollInstance()->GetConnection();
        CRldb* rldb_conn = conn.get();
        if (NULL == rldb_conn)
        {
            AK_LOG_WARN << "Get DB conn failed.";
            return;
        }

        const char* sql = "select ID, MsgId, Account, Token, Grade, DevCount, Message, CreateTime, UpdateTime "
                          " from AccountUT ";
        auto rldb_stmt = rldb_conn->Prepare(sql);
        if (nullptr == rldb_stmt)
        {
            ReleaseDBConn(conn);
            return;
        }

        int k = 0;
        int64_t id = 0;
        rldb_stmt->BindOutInt64(k++, &id);
        int64_t msg_id_out = 0;
        rldb_stmt->BindOutInt64(k++, &msg_id_out);
        char account[64];
        rldb_stmt->BindOutString(k++, account, sizeof(account));
        char token[1025];
        rldb_stmt->BindOutString(k++, token, sizeof(token));
        int64_t grade = 0;
        rldb_stmt->BindOutInt64(k++, &grade);
        int64_t dev_count = 0;
        rldb_stmt->BindOutInt64(k++, &dev_count);
        char message[1025];
        rldb_stmt->BindOutString(k++, message, sizeof(message));
        char create_time[50];
        rldb_stmt->BindOutString(k++, create_time, sizeof(create_time));
        char update_time[50];
        rldb_stmt->BindOutString(k++, update_time, sizeof(update_time));

        int64_t affected_rows = rldb_stmt->Execute();
        AK_LOG_INFO << "Query rows:" << affected_rows;

        int rows = 1;
        while (rldb_stmt->Fetch())
        {
            AK_LOG_INFO << "Rows=" << rows << ";ID=" << id << ";Msg_id=" << msg_id_out << ";token=" << token
                        << ";grade=" << grade << ";dev_count=" << dev_count << ";message=" << message
                        << ";create_time=" << create_time << ";update_time=" << update_time;
            ++rows;
        }

        ReleaseDBConn(conn);
    }

    SECTION("QueryCase3")
    {
        AK_LOG_INFO << "QueryCase3 begin";
        RldbPtr conn = GetDBConnPollInstance()->GetConnection();
        CRldb* rldb_conn = conn.get();
        if (NULL == rldb_conn)
        {
            AK_LOG_WARN << "Get DB conn failed.";
            return;
        }

        const char* sql = "select ID, MsgId, Account, Token, Grade, DevCount, Message, CreateTime, UpdateTime "
                          " from AccountUT where Message like ? ";
        auto rldb_stmt = rldb_conn->Prepare(sql);
        if (nullptr == rldb_stmt)
        {
            ReleaseDBConn(conn);
            return;
        }

        char message_in[246] = "%you want%";
        rldb_stmt->BindString(0, message_in);

        int k = 0;
        int64_t id = 0;
        rldb_stmt->BindOutInt64(k++, &id);
        int64_t msg_id_out = 0;
        rldb_stmt->BindOutInt64(k++, &msg_id_out);
        char account[64];
        rldb_stmt->BindOutString(k++, account, sizeof(account));
        char token[1025];
        rldb_stmt->BindOutString(k++, token, sizeof(token));
        int64_t grade = 0;
        rldb_stmt->BindOutInt64(k++, &grade);
        int64_t dev_count = 0;
        rldb_stmt->BindOutInt64(k++, &dev_count);
        char message[1025];
        rldb_stmt->BindOutString(k++, message, sizeof(message));
        char create_time[50];
        rldb_stmt->BindOutString(k++, create_time, sizeof(create_time));
        char update_time[50];
        rldb_stmt->BindOutString(k++, update_time, sizeof(update_time));

        int64_t affected_rows = rldb_stmt->Execute();
        AK_LOG_INFO << "Query rows:" << affected_rows;

        int rows = 1;
        while (rldb_stmt->Fetch())
        {
            AK_LOG_INFO << "Rows=" << rows << ";ID=" << id << ";Msg_id=" << msg_id_out << ";token=" << token
                        << ";grade=" << grade << ";dev_count=" << dev_count << ";message=" << message
                        << ";create_time=" << create_time << ";update_time=" << update_time;
            ++rows;
        }

        ReleaseDBConn(conn);
    }

    SECTION("QueryCase4")
    {
        AK_LOG_INFO << "QueryCase4 begin,buffer is small,data truncated";
        RldbPtr conn = GetDBConnPollInstance()->GetConnection();
        CRldb* rldb_conn = conn.get();
        if (NULL == rldb_conn)
        {
            AK_LOG_WARN << "Get DB conn failed.";
            return;
        }

        const char* sql = "select ID, MsgId, Account, Token, Grade, DevCount, Message, CreateTime, UpdateTime "
                          " from AccountUT ";
        auto rldb_stmt = rldb_conn->Prepare(sql);
        if (nullptr == rldb_stmt)
        {
            ReleaseDBConn(conn);
            return;
        }

        int k = 0;
        int64_t id = 0;
        rldb_stmt->BindOutInt64(k++, &id);
        int64_t msg_id_out = 0;
        rldb_stmt->BindOutInt64(k++, &msg_id_out);
        char account[64];
        rldb_stmt->BindOutString(k++, account, sizeof(account));
        char token[1025];
        rldb_stmt->BindOutString(k++, token, sizeof(token));
        int64_t grade = 0;
        rldb_stmt->BindOutInt64(k++, &grade);
        int64_t dev_count = 0;
        rldb_stmt->BindOutInt64(k++, &dev_count);
        char message[10];
        rldb_stmt->BindOutString(k++, message, sizeof(message));
        char create_time[50];
        rldb_stmt->BindOutString(k++, create_time, sizeof(create_time));
        char update_time[50];
        rldb_stmt->BindOutString(k++, update_time, sizeof(update_time));

        int64_t affected_rows = rldb_stmt->Execute();
        AK_LOG_INFO << "Query rows:" << affected_rows;

        int rows = 1;
        while (rldb_stmt->Fetch())
        {
            AK_LOG_INFO << "Rows=" << rows << ";ID=" << id << ";Msg_id=" << msg_id_out << ";token=" << token
                        << ";grade=" << grade << ";dev_count=" << dev_count << ";message=" << message
                        << ";create_time=" << create_time << ";update_time=" << update_time;
            ++rows;
        }

        ReleaseDBConn(conn);
    }

    SECTION("Delete")
    {
        AK_LOG_INFO << "Delete begin";
        RldbPtr conn = GetDBConnPollInstance()->GetConnection();
        CRldb* rldb_conn = conn.get();
        if (NULL == rldb_conn)
        {
            AK_LOG_WARN << "Get DB conn failed.";
            return;
        }

        const char* sql = "delete from AccountUT where MsgId = ?";
        auto rldb_stmt = rldb_conn->Prepare(sql);
        if (nullptr == rldb_stmt)
        {
            ReleaseDBConn(conn);
            return;
        }

        int64_t msg_id = 1001;
        rldb_stmt->BindInt(0, msg_id);

        int64_t affected_rows = rldb_stmt->Execute();
        AK_LOG_INFO << "Delete rows:" << affected_rows;

        ReleaseDBConn(conn);
    }

    SECTION("DaoCheckUser")
    {
        DaoCheckUser("**********");
        DaoCheckUser("<EMAIL>");
        DaoCheckUser("***********");
        DaoCheckUser("**********");
    }

	SECTION("DaoCheckToken")
	{
		DaoCheckToken("20200826204155cloudinterfaceauto");
		DaoCheckToken("************55cloudinterfaceauto");
	}

	SECTION("DaoTokenContinuation")
	{
		DaoTokenContinuation("**********", "Aa111111", "dd1dab09BFLk6k3k0h1XGvmMEOpZRReM");
	}
}


TEST_CASE("RldbStmtAttack", "[.Attack]")
{
    ConfInit();
    DaoInit();

    SECTION("AttackSuccessCase1")
    {
        AK_LOG_INFO << "AttackSuccessCase1 begin:数字注入";
        RldbPtr conn = GetDBConnPollInstance()->GetConnection();
        CRldb* rldb_conn = conn.get();
        if (NULL == rldb_conn)
        {
            AK_LOG_WARN << "Get DB conn failed.";
            return;
        }

        char account[31];
        snprintf(account, sizeof(account), "%s", "********** OR 1 =1");
        std::stringstream stream_sql;
        stream_sql << "select ID, Role, ParentID, Name, Account, TimeZone from PersonalAccount "
                   << " where Account = " << account << " and passwd = '68cf63c62bc68d71fc41c028375e2f6e'";

        CRldbQuery query(rldb_conn);
        query.Query(stream_sql.str());

        if (!query.MoveToNextRow())
        {
            ReleaseDBConn(conn);
            return;
        }

        AK_LOG_INFO << "ID=" << query.GetRowData(0) << ";role=" << query.GetRowData(1) << ";parent_id=" << query.GetRowData(2) <<
                    ";name=" << query.GetRowData(3) << ";account=" << query.GetRowData(4);

        ReleaseDBConn(conn);
    }

    SECTION("AttackFailCase1")
    {
        AK_LOG_INFO << "AttackFailCase1 begin:数字注入";
        RldbPtr conn = GetDBConnPollInstance()->GetConnection();
        CRldb* rldb_conn = conn.get();
        if (NULL == rldb_conn)
        {
            AK_LOG_WARN << "Get DB conn failed.";
            return;
        }

        char account[31];
        snprintf(account, sizeof(account), "%s", "********** OR 1 =1");
        std::stringstream stream_sql;
        stream_sql << "select ID, Role, ParentID, Name, Account, TimeZone from PersonalAccount "
                   << " where Account = ? and passwd = '68cf63c62bc68d71fc41c028375e2f6e'";

        auto stmt = rldb_conn->Prepare(stream_sql.str());
        if (nullptr == stmt)
        {
            ReleaseDBConn(conn);
            return;
        }

        stmt->BindString(0, account);

        int64_t id = 0;
        stmt->BindOutInt64(0, &id);

        stmt->Execute();

        if (stmt->Fetch())
        {
            AK_LOG_INFO << "Fetch result,id=" << id;
        }
        else
        {
            AK_LOG_INFO << "Fetch no result";
        }

        ReleaseDBConn(conn);
    }

    SECTION("AttackSuccessCase2")
    {
        AK_LOG_INFO << "AttackSuccessCase2 begin:字符串注入";
        RldbPtr conn = GetDBConnPollInstance()->GetConnection();
        CRldb* rldb_conn = conn.get();
        if (NULL == rldb_conn)
        {
            AK_LOG_WARN << "Get DB conn failed.";
            return;
        }

        char account[31];
        snprintf(account, sizeof(account), "%s", "**********'#'");
        std::stringstream stream_sql;
        stream_sql << "select ID, Role, ParentID, Name, Account, TimeZone from PersonalAccount "
                   << " where Account = '" << account << "' and passwd = '68cf63c62bc68d71fc41c028375e2f6e'";

        CRldbQuery query(rldb_conn);
        query.Query(stream_sql.str());

        if (!query.MoveToNextRow())
        {
            ReleaseDBConn(conn);
            return;
        }

        AK_LOG_INFO << "ID=" << query.GetRowData(0) << ";role=" << query.GetRowData(1) << ";parent_id=" << query.GetRowData(2) <<
                    ";name=" << query.GetRowData(3) << ";account=" << query.GetRowData(4);

        ReleaseDBConn(conn);
    }

    SECTION("RldbStmtMemoryLeakTest")
    {
        while (1)
        {
            QueryMemoyLeakTest();
            sleep(1);
        }
    }
}



