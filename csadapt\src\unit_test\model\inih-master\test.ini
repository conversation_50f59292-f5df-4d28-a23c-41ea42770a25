; Test config file for ini_example.c and INIReaderTest.cpp

[protocol]             ; Protocol configuration
version=6              ; IPv6

[user]
name = <PERSON>       ; Spaces around '=' are stripped
email = <EMAIL>  ; And comments (like this) ignored
active = true          ; Test a boolean
pi = 3.14159           ; Test a floating point number
multi = this is a      ; test
        multi-line value ; test
