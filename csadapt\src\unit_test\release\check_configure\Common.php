<?php
const ADAPT_CONF_FILE="../conf/csadapt.conf";

function getDB()
{
	$data = parse_ini_file(ADAPT_CONF_FILE);
    $dbhost = $data["db_ip"];
    $dbuser = $data["db_username"];
    $dbpass = $data["db_passwd"];
    $dbname = $data["db_database"];
    $dbport = $data["db_port"];

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
	$dbConnection->query('set names utf8;');
    return $dbConnection;
}

function exec_sql($sql)
{
	$db = getDB();
	$sth = $db->prepare($sql);
	$sth->execute();	
}


