#case的通用配置
[common]
init_sql_cmd=mysql -h 127.0.0.1 -u root -pAk@56@<EMAIL> AKCS_UnitTest -e 'source ../db/init.sql'
#如果其他的case有配置，用case的detect
detect_bin=php ../check_configure/CommDevContactTest.php

[case_common]
#把file放在这里，那么写sql就可以很清晰知道更新哪个mac
check_file_path=/var/www//download/community/5/1/6300100003/ContactList/CE0000000009.xml
sql_cmd=
#group|属性:值|...
#check_group=6300000001|Name:System Test1|
#gruop|uid|属性:值|...
#check_uid=6300000001|6300100003|Name:System Test1|
#当检测项多了可以通过json进行配置
check_by_json=1

[case_unit_common]
check_file_path=/var/www//download/community/5/1/Public_1/ContactList/CE0000000002.xml
sql_cmd=
check_by_json=1

[case_dev_v4400]
check_file_path=/var/www//download/community/5/1/6300100003/ContactList/CE0000000009.xml
sql_cmd=update Devices set DclientVer=100 where Mac="CE0000000009"
#group|属性:值|...
check_group=6300000001|Name:System Test1|
#gruop|uid|属性:值|...
check_uid=6300000001|6300100003|Name:System Test1|