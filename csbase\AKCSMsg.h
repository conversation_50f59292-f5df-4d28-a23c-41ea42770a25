#ifndef __AKCS_MSG_H__
#define __AKCS_MSG_H__

#include "BasicDefine.h"
#include "AkcsCommonSt.h"
#include<memory>
#include "util.h"

#define PHONE_CALL_OUT_SUBFIX           "0"
#define PHONE_SUBSTR_DETECT_NUMBER       7 //phone从后面开始检测的长度

enum RobinCallType
{

    ROBINCALL_TYPE_INDOOR = 2, //室内机
    ROBINCALL_TYPE_USER = 6,   //app 用户类型
    ROBINCALL_TYPE_PHONE = 7,   //phone
    ROBINCALL_TYPE_PHONE2 = 8,  //phone2
    ROBINCALL_TYPE_PHONE3 = 9,  //phone3
};

typedef std::shared_ptr<DEV_COMM_KEY> DevCommKeyPtr;
typedef std::list<DevCommKeyPtr> DevCommKeyPtrList;
enum DEV_KEY_TYPE{
    DEV_KEY_RF,
    DEV_KEY_PIN
};

typedef std::vector<DEVICE_CONTACTLIST> NodeAppList;
typedef NodeAppList::iterator NodeAppListIter;
typedef std::map<std::string/*node*/, NodeAppList> MapNodeAppList;
typedef MapNodeAppList::const_iterator MapNodeAppListIter;
//account对应的权限组设备列表
typedef std::map<std::string, std::set<std::string>> MapUserAGDeviceList;
typedef std::shared_ptr<MapUserAGDeviceList> MapUserAGDeviceListPtr;

//单元公共设备下的Account
typedef struct COMMUNITY_ACCOUNTS_INFO_T
{
    char account[USER_SIZE];
    char uuid[UUID_SIZE];
    int unit_id;
} COMMUNITY_ACCOUNTS_INFO;
typedef std::vector<COMMUNITY_ACCOUNTS_INFO> CommunitAccountInfoList;
typedef std::map<uint32_t/*unitid*/, CommunitAccountInfoList> MapUnitCommunitAccountList;
typedef std::map<uint32_t/*unitid*/, const CommunitAccountInfoList&> ConstMapUnitCommunitAccountList;

typedef MapUnitCommunitAccountList::const_iterator MapUnitCommunitAccountListIter;

typedef struct SOCKET_MSG_LINKER_EXPIRE_MESSAGE_T
{
    int type;
    int notify_type;
    int leave_days;
    long long expire_timestamp;
} SOCKET_MSG_LINKER_EXPIRE_MESSAGE;


#endif //__AKCS_MSG_H__
