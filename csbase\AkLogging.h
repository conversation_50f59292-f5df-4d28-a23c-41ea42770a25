#ifndef __AKCS_BASE_LOGING_H__
#define __AKCS_BASE_LOGING_H__


#ifdef __cplusplus
#define GOOGLE_GLOG_DLL_DECL           // 使用静态glog库时，必须定义这个
#define GLOG_NO_ABBREVIATED_SEVERITIES // 没这个编译会出错,传说因为和Windows.h冲突

#include <glog/logging.h>

//added by chenyc,2017-11-29,增加AK日志宏

/*
#define AK_LOG_DEBUG DLOG(INFO)
#define AK_LOG_INFO  LOG(INFO)
#define AK_DLOG_INFO LOG(INFO) << __PRETTY_FUNCTION__ << " this=" << this << " "
#define AK_LOG_WARN  LOG(WARNING)
#define AK_DLOG_WARN LOG(WARNING) << __PRETTY_FUNCTION__ << " this=" << this << " "
#define AK_LOG_ERROR LOG(ERROR)
#define AK_LOG_FATAL LOG(FATAL)  //fatal出发时,会打印出进程的调动栈
*/

#endif // end of define __cplusplus

#define ENCLOG_FLG  "#enc#:"
#define ENCLOG  ENCLOG_FLG <<


#include <iostream>
#include <string>
#include <ostream>
#include <functional>
#include <ios>
#include "ThreadLocalSingleton.h"
#include "util_string.h"
#include "encrypt/AES256.h"

class AkLogControlSingleton : private boost::noncopyable
{

public:
    static AkLogControlSingleton& GetInstance() 
    {
        static AkLogControlSingleton instance;
        return instance;
    }
  
    void SetEncryptSwitch(int encrypt_switch) 
    {
        encrypt_switch_ = encrypt_switch;
    }
    void SetTraceidSwitch(int traceid_switch)
    {
        traceid_switch_ = traceid_switch;
    }
    int GetEncryptSwitch() 
    {
        return encrypt_switch_;
    }
    int GetTraceidSwitch()
    {
        return traceid_switch_;
    }
    


private:
    AkLogControlSingleton() 
    {
        encrypt_switch_ = 0;
        traceid_switch_ = 0;
    }

    int encrypt_switch_;
    int traceid_switch_;
};


class AkGlog: public std::ostream  {
public:
    AkGlog(const char* file, int line, google::LogSeverity se) {
        log_ = new google::LogMessage(file, line, se);
        if(AkLogControlSingleton::GetInstance().GetTraceidSwitch())
        {
            log_->stream() << "ThreadTraceid:" << ThreadLocalSingleton::GetInstance().GetTraceID() << " ";
        }
    }

    ~AkGlog()
    {
        if (log_)
        {
            delete log_;
        }
    }
    
    template<typename T>
    AkGlog& operator<<(const T& value) {
        log_->stream() << value;
        return *this;
    }

    // 重载操作符来处理字符串字面量
    AkGlog& operator<<(const char* value) 
    {
        return HandleLogOperator(value);
    }
    AkGlog& operator<<(const std::string &value)
    {
        return HandleLogOperator(value);
    }
    AkGlog& operator<<(char* value) 
    {
        return HandleLogOperator(value);
    }
        
private:

    bool next_enc_ = false;
    google::LogMessage *log_;

    void LogEncryptOutput(const std::string &value)
    {
        std::string output_log = value;
        char aes_data[2048] = "";
        if(output_log.size() > 0 && 0 == LogAESEncrypt(output_log.c_str(), output_log.length(), aes_data, sizeof(aes_data)))
        {
            output_log = aes_data;
        }
        
        log_->stream() << "[" << output_log << "]" ;
    }

    bool NeedNextEnc(const std::string &value)
    {
        if( strcmp(value.c_str(), ENCLOG_FLG) == 0 || 
            StringEndsWith(value, "email:") ||
            StringEndsWith(value, "phone:") ||
            StringEndsWith(value, "login_account:") ||
            StringEndsWith(value, "user:") )
        {
            return true;
        }

        return false;
    }

    AkGlog& HandleLogOperator(const std::string &value)
    {
        if(!AkLogControlSingleton::GetInstance().GetEncryptSwitch())
        {
            //未开启加密开关，直接原样输出 返回
            log_->stream() << value;
            return *this;
        }
       
        if(next_enc_ == true)
        {
            LogEncryptOutput(value);
            next_enc_ = false;
        }
        else
        {
            log_->stream() << value;
        }
        if (NeedNextEnc(value))
        {   
            next_enc_ = true;
        }        
        return *this;
    }
};


#define AK_LOG_DEBUG  AkGlog(__FILE__, __LINE__, google::GLOG_INFO)
#define AK_LOG_INFO  AkGlog(__FILE__, __LINE__, google::GLOG_INFO)
#define AK_LOG_WARN  AkGlog(__FILE__, __LINE__, google::GLOG_WARNING)
#define AK_LOG_ERROR  AkGlog(__FILE__, __LINE__, google::GLOG_ERROR)
#define AK_LOG_FATAL  AkGlog(__FILE__, __LINE__, google::GLOG_FATAL)

#endif /* __AKCS_BASE_LOGING_H__ */
