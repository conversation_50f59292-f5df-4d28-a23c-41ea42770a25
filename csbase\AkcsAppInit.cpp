#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include <string.h>
#include <unistd.h>
#include "AkcsAppInit.h"
#include <fcntl.h>
#include <signal.h>
#include <unistd.h>
#include <sys/stat.h>
#include "AkLogging.h"
#include "glog/logging.h"

#define write_lock(fd,offset,whence,len) lock_reg(fd,F_SETLK,F_WRLCK,offset,whence,len)
static int lock_reg(int fd, int cmd, int type, off_t offset, int whence, off_t len)
{
    struct flock lock;
    lock.l_type = type;
    lock.l_start = offset;
    lock.l_whence = whence;
    lock.l_len = len;

    int ret = fcntl(fd, cmd, &lock);
    return ret;
}

int IsSingleton2(const char*pidfile)
{
    int fd, val;
    char buf[10];
    if ((fd = open(pidfile, O_WRONLY | O_CREAT, S_IRWXU | S_IRWXG | S_IRWXO)) < 0)
    {
        return 0;
    }
    if (write_lock(fd, 0, SEEK_SET, 0) < 0)
    {
        return 0;
    }
    if (ftruncate(fd, 0) < 0)
    {
        return 0;
    }
    sprintf(buf, "%d\n", getpid());
    if ((std::size_t)write(fd, buf, strlen(buf)) != strlen(buf))
    {
        return 0;
    }
    if ((val = fcntl(fd, F_GETFD, 0)) < 0)
    {
        return 0;
    }
    val |= FD_CLOEXEC;
    if (fcntl(fd, F_SETFD, val) < 0)
    {
        return 0;
    }
    return 1;
}

void GlogInit2(const char* argv, const char* logname)
{

    char info[256] = {0};
    char warn[256] = {0};
    char error[256] = {0};
    char fatal[256] = {0};
    snprintf(info, sizeof(info), "/var/log/%s/INFO", logname);
    snprintf(warn, sizeof(warn), "/var/log/%s/WARN", logname);
    snprintf(error, sizeof(error), "/var/log/%s/ERROR", logname);
    snprintf(fatal, sizeof(fatal), "/var/log/%s/FATAL", logname);

    google::InitGoogleLogging(argv);
    google::SetLogDestination(google::GLOG_INFO, info);
    google::SetLogDestination(google::GLOG_WARNING, warn);
    google::SetLogDestination(google::GLOG_ERROR, error);
    google::SetLogDestination(google::GLOG_FATAL, fatal);
    FLAGS_logbufsecs = 0;
    google::SetStderrLogging(google::GLOG_WARNING); // 输出到标准输出的时候大于INFO级别的都输出;
    FLAGS_max_log_size = 200;    //单日志文件最大200M
}

void GlogClean2()
{
    google::ShutdownGoogleLogging();
}

