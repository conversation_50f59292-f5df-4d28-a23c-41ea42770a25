#include "AkcsBussiness.h"
#include "AkLogging.h"

void BussinessLimit::InitBussiness(const std::string& bussiness, const uint32_t period, const uint32_t num, 
	                                      const uint32_t key_expire,const BussinessLimitCallback& cb)
{
	
	std::map<std::string/*bussiness*/, BussinessParams>::iterator it = bussiness_params_.find(bussiness);
    if (it == bussiness_params_.end())
    {
		BussinessParams param;
		param.key_expire = key_expire;
		param.num = num;
		param.period = period;
		param.cb = cb;
		bussiness_params_.insert(std::pair<std::string, BussinessParams>(bussiness, param));
		KeyTimes key_limits;
		bussiness_key_limits_[bussiness] = key_limits;
	}
}

int BussinessLimit::AddBussiness(const std::string& bussiness, const std::string& key)
{
	time_t now = time(nullptr);
	std::map<std::string/*bussiness*/, BussinessParams>::iterator it = bussiness_params_.find(bussiness);
	if (it == bussiness_params_.end())
	{
		return IN_LIMIT;
	}
	//TODO,先共用一把锁
    {
		std::lock_guard<std::mutex> lock(mutex_);
		if(bussiness_key_limits_[bussiness].find(key) == bussiness_key_limits_[bussiness].end())
		{
			std::list<time_t> time_lists;
			time_lists.push_front(now);
			bussiness_key_limits_[bussiness][key] = time_lists;
			return IN_LIMIT;
		}
		
		BussinessParams param = bussiness_params_[bussiness];
		std::list<time_t> &time_lists = bussiness_key_limits_[bussiness][key];
		AK_LOG_WARN << "AddBussiness " << bussiness << ", key is " << key << ", num is " << time_lists.size() << ", limit is " << param.num;
		if (time_lists.size() < param.num)
		{
			time_lists.push_front(now);
		}
		else
		{
			//最新的从头插入,最久的从尾巴删除,保证最多只有num个时间点,内存占用不过多.
			time_t time_index_first = time_lists.back();
			time_lists.pop_back();
			time_lists.push_front(now);
			if(now - time_index_first < param.period)//证明在时间段内,产生超过num_次业务了
			{
				param.cb(bussiness, key);
				return OUT_OF_LIMIT;
			}
			else//次数达到了,但是首末两次的时间间隔已经超过单位时间了,此时清空掉该key下的所有时间记录节点
			{
				bussiness_key_limits_[bussiness][key].clear();
                return OUT_OF_LIMIT;
			}
		}
	}
	return IN_LIMIT;
}

//定期巡检,删除一段时间内没触发时间的key
void BussinessLimit::RemoveBussiness()
{
	time_t now = time(nullptr);
	{
		std::lock_guard<std::mutex> lock(mutex_);

		for(auto & bussiness_key_time_list : bussiness_key_limits_)
		{
			std::string bussiness = bussiness_key_time_list.first;
			BussinessParams param = bussiness_params_[bussiness];			
			for(auto & key_time_list : bussiness_key_time_list.second)
			{
				std::list<time_t> time_lists = key_time_list.second;
				if(now - time_lists.front() > param.key_expire)//该key已经在一段时间内未发生业务了,可以删除掉
				{
					bussiness_key_time_list.second.erase(key_time_list.first);
					
				}
			}
		}
	}
}

