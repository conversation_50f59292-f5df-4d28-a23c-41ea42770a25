// Author: chency
// Date: 2019-11-25
// File: AkcsBussiness.h
// Des: 系统的公共业务模块

#ifndef __AKCS_BASE_BUSSINESS_H__
#define __AKCS_BASE_BUSSINESS_H__
#include <set>
#include <vector>
#include <map>
#include <list>
#include <chrono>
#include "Singleton.h"
#include <mutex>
#include <functional>

//added by chenyc,v4.6,2019-09-18,一段时间内业务次数限制接口
//例如:5分钟内只能错误10次,或者2天内只能错误10次等业务需求
//前期采用简单代码实现，后续可以通过redis的list数据结构实现
//用法: AKCS::Singleton<BussinessLimit>::instance().xx()

typedef std::function<void(const std::string& bussiness,const std::string& key)> BussinessLimitCallback;
class BussinessLimit{
public:
    friend class AKCS::Singleton<BussinessLimit>;
	typedef std::map<std::string, std::list<time_t> > KeyTimes;
	virtual ~BussinessLimit() {}
	enum 
	{
		IN_LIMIT = 0,
		OUT_OF_LIMIT = 1,
	};
public:
	//key_expire:如果多长时间内，该业务下的key再次触发业务,则删除对应的key,避免内存无限缓慢增长.单位:s
	//cb:当超过业务的限制时，触发调用者提供的回调函数
	//注:当前版本num>1,才能使用
	void InitBussiness(const std::string& bussiness, const uint32_t period, const uint32_t num, 
	                          const uint32_t key_expire,const BussinessLimitCallback& cb);
	//通过返回值判断是否触发条件,0:未触发,1:触发
    int AddBussiness(const std::string& bussiness, const std::string& key);
	
	//定期遍历所有bussiness下的keys,删除过期的key(类惰性删除).
	void RemoveBussiness();
private:
	//具体说明,例如要限制csgate错误http路由的限制黑名单,则bussiness:csgate_http, key:具体的对端ip地址
	std::string bussiness_;
	struct BussinessParams
    {
		int period;
		uint32_t num;
		int key_expire;
		BussinessLimitCallback cb;
	};
	std::map<std::string/*bussiness*/, BussinessParams> bussiness_params_;//具体业务实例的参数
	std::map<std::string/*bussiness*/, KeyTimes> bussiness_key_limits_;//具体业务实例的发生时间戳列表
	std::mutex mutex_;
};

#endif //__AKCS_BASE_BUSSINESS_H__

