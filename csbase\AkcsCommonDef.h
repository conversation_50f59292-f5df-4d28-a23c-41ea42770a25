#ifndef __AKCS_COMMON_DEF_H__
#define __AKCS_COMMON_DEF_H__

#include <stdint.h>  //uint32_t
#include <vector>
#include <string>
#include <map>
#include <list>
#include "BasicDefine.h"

#define RTSP_SERVER_PORT2    8601

#define DEVICE_FLAGS_WITHOUT_RELAY      0xFFFF0F


__attribute__((unused)) static const char *g_redis_db_userdetail = "userdetail";
__attribute__((unused)) static const char *g_redis_db_appdnd = "appdnd";
__attribute__((unused)) static const char *g_redis_db_appstat = "appstat";
__attribute__((unused)) static const char *g_redis_db_appconf = "appconf";
__attribute__((unused)) static const char *g_redis_db_dev_outerip = "dev_outerip";
__attribute__((unused)) static const char *g_redis_db_proc_record = "proc_record";
__attribute__((unused)) static const char *g_redis_db_weather = "weather";
__attribute__((unused)) static const char *g_redis_db_appcode = "appcode";
__attribute__((unused)) static const char *g_redis_db_mac_vrtspsid = "mac_vrtspsid";
__attribute__((unused)) static const char *g_redis_db_rtspnonce = "rtspnonce";
__attribute__((unused)) static const char *g_redis_db_seq_pbxsid = "seq_pbxsid";
__attribute__((unused)) static const char *g_redis_db_antipassback = "antipassback";
__attribute__((unused)) static const char *g_redis_db_backend_limiting = "backend_limiting";
__attribute__((unused)) static const char *g_redis_db_pm_tow_factor_auth = "pm_tow_factor_auth";
__attribute__((unused)) static const char *g_redis_db_smart_lock = "smart_lock";
__attribute__((unused)) static const char *g_redis_db_video_record = "video_record";
__attribute__((unused)) static const char *g_redis_db_auth = "auth";


//proc_record 特殊前缀
__attribute__((unused)) static const char *g_proc_record_bigprojec = "BigPj-";


//平台用户角色
namespace AccountGrade
{
    enum 
    {
        SUPER_MANEGER_GRADE = 1,
        AREA_MANEGER_GRADE = 11,
        COMMUNITY_MANEGER_GRADE = 21,
        PERSONAL_MANEGER_GRADE = 22,
        OFFICE_MANEGER_GRADE = 23,
        PERSONAL_USER_GRADE = 31,
    };
}

enum
{
    ACCOUNT_ROLE_MANAGEMENT = 1,            //管理员
    ACCOUNT_ROLE_PROPERTY = 2,              //物业
    ACCOUNT_ROLE_INSTALL = 3,               //安装者
    ACCOUNT_ROLE_PERSONNAL_MAIN = 10,       //个人终端用户主账号
    ACCOUNT_ROLE_PERSONNAL_ATTENDANT = 11,  //个人终端用户从账号
    ACCOUNT_ROLE_PERSONNAL_V_PUB = 12,      //个人虚拟账号，用于公共设备
    ACCOUNT_ROLE_COMMUNITY_MAIN = 20,       //社区用户主账号
    ACCOUNT_ROLE_COMMUNITY_ATTENDANT = 21,  //社区用户从账号
    ACCOUNT_ROLE_OFFICE_MAIN = 30,          //office主账号
    ACCOUNT_ROLE_OFFICE_ADMIN = 31,         //office 管理员
    ACCOUNT_ROLE_OFFICE_NEW_PER = 32,       //新办公用户
    ACCOUNT_ROLE_COMMUNITY_PM = 40,         //社区用户PM账号
    ACCOUNT_ROLE_OFFICE_NEW_ADMIN = 41,     //新办公，admin app
    
    ACCOUNT_ROLE_MAX,
};


namespace project
{

enum PROJECT_TYPE{
    RESIDENCE = 0,
    OFFICE = 1,
    PERSONAL =2,
    OFFICE_NEW =3,//需要区分新旧办公的地方才需要打这个标识，不然打OFFICE标识
    NONE =100,
};

}

namespace oem
{
    enum OEM_TYPE{
        AK = 0,
        MYSMART = 1,
        FASTTEL = 2,
        BELAHOME = 3,
        AZER, //阿塞拜疆OEM类型
    };

}

enum
{
    DEVICE_TYPE_STAIR = 0,           //梯口机
    DEVICE_TYPE_DOOR,                //门口机
    DEVICE_TYPE_INDOOR,              //室内机
    DEVICE_TYPE_MANAGEMENT,          //管理中心机
    DEVICE_TYPE_WALL,                //围墙机
    DEVICE_TYPE_THIRD_CAMERA = 7,        //第三方摄像头

    DEVICE_TYPE_ANALOG_HANDLE = 20, //模拟手柄

    DEVICE_TYPE_ACCESS = 50,         //门禁
    DEVICE_TYPE_APP = 100,                 //app
    DEVICE_TYPE_MAX,
};

enum MonitorPlatform
{
    MONITOR_TYPE_SMARTPLUS_AND_INDOOR_AND_MANAGMENT = 1,
    MONITOR_TYPE_ONLY_SMARTPLUS,
    MONITOR_TYPE_ONLY_INDOOR_AND_MANAGMENT,
};

enum VoiceMsgSendType
{
    SEND_TO_FAMILY = 0,           //发给整个家庭/办公people
    SEND_TO_INDOOR,                //发给室内机
    SEND_TO_APP,              //发给app
};

enum
{
    DEVICE_MONITOR_TYPE_THIRD_CAMERA = 0,
    DEVICE_MONITOR_TYPE_CLOUD
};

enum
{
    DEVICE_SETTING_FLAG_PER_PUBLIC = 1,
};

namespace csmain //这个太多了后期统一优化
{
    enum DevProjectType
    {
        PROJECT_COMMUNITY_DEV = 0,
        PROJECT_OFFICE_DEV = 1,
    };

    enum CommunityDeviceGrade
    {
        COMMUNITY_DEVICE_TYPE_NONE = 0,//代表个人
        COMMUNITY_DEVICE_TYPE_PUBLIC = 1,
        COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT = 2,
        COMMUNITY_DEVICE_TYPE_PERSONAL = 3,
    };

    enum DeviceType //客户端的类型,映射到 tcp conn
    {
        COMMUNITY_NONE = 0,
        COMMUNITY_DEV,
        COMMUNITY_APP,
        PERSONNAL_DEV,
        PERSONNAL_APP,
        OFFICE_DEV, //不能新增new_office_dev类型
        OFFICE_APP,
    };
   
}

namespace csgate
{
    enum ERRCODE
    {
        ERR_SUCCESS = 0,
        //用户管理
        ERR_USER_NOT_EXIT = 2,//本来是1的，现在统一改为2 密码错误
        ERR_PASSWD_INVALID = 2,
        ERR_APP_EXPIRE = 3,
        ERR_APP_UNACTIVE = 4,
        ERR_APP_UNPAID = 5, //额外的app未支付
        ERR_TOKEN_INVALID = 7,
        ERR_VERSION_INVALID = 8,
        ERR_PM_APP_STATUS_CLOSED = 9, //PM APP的状态是关闭的
        ERR_PM_APP_UNCREATED = 10, //PM APP未创建
        ERR_INSTALLER_APP_STATUS_CLOSED = 11, //Ins APP的状态是关闭的
        ERR_REFRESH_TOKEN_INVALID = 12, //refresh token错误
        ERR_NEED_TWO_FACTOR_AUTH = 13,
        ERR_ADMIN_APP_UNCREATED = 14,
    };
}

//平台用户角色
namespace ServerArea
{
    enum 
    {
        ecloud = 1,
        ucloud = 3,
        incloud = 4,
        jpcloud = 5,
        aucloud = 7,
        scloud = 8,
        ccloud = 9,
        rucloud = 10,
        asbj = 22,
    };
}

enum DEVICES_MD5_TYPE{
    CONFIG_MD5 = 0,
    FACE_MD5,
    USER_MATE_MD5,
    SCHEDULE_MD5,
    CONTACT_MD5,
};


enum CONTROL_TYPE
{
    CLOSE_DOOR = 0,
    OPEN_DOOR = 1,
};  

/*
新：
SmartPlus and indoor monitors：直接呼室内机+App
Phone and indoor monitors：直接呼室内机+Phone
SmartPlus and indoor monitors, with phone as backup：先呼室内机+App，无应答再呼Phone
Indoor monitors with SmartPlus as backup：先呼室内机，再呼App
Indoor monitors with phone as backup：先呼室内机，再呼Phone
Indoor monitors with SmartPlus as backup, finally the phone：先呼室内机，再呼App，无应答再呼Phone

*/
enum NODE_CALL_TYPE
{
    //callloop=0
    NODE_CALL_TYPE_APP_INDOOR = 0,//旧：呼叫该家庭下的SmartPlus+室内机(同时呼叫)app主账号不移出群组响铃
    //V4.3 落地号码+室内机+APP
    //V4.4 改为室内机+phone 用dclient版本判断
    //callloop=2
    NODE_CALL_TYPE_INDOOR_PHONE = 1,//旧：呼叫该家庭（主账户）的落地号码+室内机(同时呼叫)app主账号移出群组响铃(相当之前的开启落地)
    //callloop=1
    NODE_CALL_TYPE_APP_INDOOR_BACK_PHONE = 2,//先呼叫SmartPlus+室内机(同时呼叫)，若未接听，再呼叫落地号码。app主账号不移出群组响铃
    //callloop=2以下
    NODE_CALL_TYPE_INDOOR_BACK_APP = 3,
    NODE_CALL_TYPE_INDOOR_BACK_PHONE = 4,
    NODE_CALL_TYPE_INDOOR_BACK_APP_BACK_PHONE = 5,


    NODE_CALL_TYPE_INDOOR_PHONE_OLD = 100,//设备是V4.4时候，代码NODE_CALL_TYPE_INDOOR_PHONE=2 替换为这个
};

enum CALL_LOOP_TYPE
{
    CALL_LOOP_TYPE_NORMAL = 0,//NODE_CALL_TYPE=0
    CALL_LOOP_TYPE_APP_INDOOR_BACK_PHONE = 1,//V4.3 NODE_CALL_TYPE=2
    CALL_LOOP_TYPE_GROUP_CALL = 2,//分组呼叫 NODE_CALL_TYPE=1、3、4、5
};

enum OPERATE_TYPE
{
    AUTO = 0,
    MANUAL = 1,
};

enum class ALARM_CODE
{
    OLD_DATA                    = 0,    // 旧数据
    DOOR_UNLOCK                 = 1,    // 门未关告警
    INFRARED                    = 2,    // 告警
    DRMAGENT                    = 3,    // 告警
    SMOKE                       = 4,    // 告警
    GAS                         = 5,    // 告警
    URGENCY                     = 6,    // 告警
    SOS                         = 7,    // SOS告警
    TAMPER                      = 8,    // 防拆告警
    EMERGENCY                   = 9,    // 紧急告警
    DOOR_HELD_OPEN              = 10,   // 门常开告警
    EMERGENCY_NOTIFY_OPEN       = 11,   // 紧急开门
    EMERGENCY_NOTIFY_CLOSED     = 12,   // 紧急关门
    AJAX_ALARM                  = 13,   // AJAX告警
    MOTION                      = 14,   // 移动侦测
    BREAK_IN                    = 15,   // 强闯告警
};


enum DeviceSwitch
{
    ARMING_HOME = 0,
    ARMING_AWAY = 1,
    ARMING_SLEEP = 2,
    DEV_MNG_ALL = 3,//管理机是否开启全选
    RLEAY_STATUS_1 = 4,//relay状态
    RLEAY_STATUS_2 = 5,
    RLEAY_STATUS_3 = 6,
    RLEAY_STATUS_4 = 7,
    INDOOR_ONLINE = 8,//室内机上线标识
    INDOOR_IS_KIT = 9,//室内机所属的家庭是否为Kit方案

};

enum DEVICES_SOFTWARE_VERSION{
    SOFTWARE_E12 = 12,
    SOFTWARE_R20 = 20, 
    SOFTWARE_R29 = 29,
    SOFTWARE_A092 = 92,
    SOFTWARE_A094 = 94,
    SOFTWARE_A01 = 101,
    SOFTWARE_A02 = 102,
    SOFTWARE_A03 = 103,
    SOFTWARE_E16 = 116,
    SOFTWARE_R20V2 = 220,
    SOFTWARE_EC33 = 33,
    SOFTWARE_E12_SV823 = 312,
    SOFTWARE_S539 = 539,
    SOFTWARE_X915 = 915,
    SOFTWARE_X916 = 916,
    SOFTWARE_X915_V2 = 2915,
    SOFTWARE_R20V3 = 320,
    SOFTWARE_G31 = 31,
    SOFTWARE_SL50 = 250,
    SOFTWARE_SM_VIRTUAL = 9999, // 家居虚拟设备
    SOFTWARE_XL10 = 910,
    SOFTWARE_X910 = 2910,
    SOFTWARE_937 = 937,
    SOFTWARE_SL20 = 2000,
    SOFTWARE_SL21 = 2100,
    SOFTWARE_SL30 = 30,
    SOFTWARE_SL40 = 40,
    SOFTWARE_SL60 = 60, // 60锁不过滤联系人
    SOFTWARE_SL60_OEM_YASA = 127, // SL60亚萨锁需过滤联系人，版本号第二部分
};


enum CAPTURE_LOG_RET_TYPE{
    SUCCESS = 0,
    FAILURE = 1,
    OFFILNE = 2, // 一键开门设备离线的Response
    LOCKDOWN = 2 // 设备lockdonw后上报的开门Response
};

enum ACT_OPEN_DOOR_TYPE
{
    ACT_OPEN_DOOR_TYPE_UNKNOW = 99999,
    CALL = 0,//call开门截图——社区这里代表app 开门  个人还未改
    TMPKEY = 1,
    LOCALKEY = 2,  //私钥
    RFCARD = 3,
    FACE = 4,
    REMOTE_OPEN_DOOR = 5,//dclient开门 app首页开门
    
    PM_UNLOCK = 9,     //PM一键开门
    AUTO_UNLOCK = 10,  //设备告警自动开门
    DELIVERY_UNLOCK = 11, //快递校验开门
    BOOKING_UNLOCK = 12, //booking校验开门
    ID_ACCESS_UNLOCK = 13, //南美身份证开门
    INWARD_UNLOCK = 14, //内开门
    HANDSET_UNLOCK = 15, // 模拟手柄开门
    LICENSE_PLATE_UNLOCK = 16, // 车牌开门

    OFFLINE_CODE_UNLOCK = 98, // 离线密码开门

    CALL_CAPTURE = 103,//通话截图，在呼叫时候就截图，不管是否接听    
    TEMP_CAPTURE = 104,//测温截图
    PM_LOCK = 105,      //PM一键关门

    //以下三个由设备CALL=0 转为平台要求的类型
    CLOUD_CALL_UNLOCK_APP = 6,
    CLOUD_CALL_UNLOCK_INDOOR = 7,
    CLOUD_CALL_UNLOCK_GUARD_PHONE = 8,

    //以下三个由设备REMOTE_OPEN_DOOR=5 转为平台要求的类型
    CLOUD_REMOTE_UNLOCK_APP = 5,
    CLOUD_REMOTE_UNLOCK_INDOOR = 7,
    CLOUD_REMOTE_UNLOCK_GUARD_PHONE = 8,   
     
    CLOUD_NFC = 100,
    CLOUD_BLE = 101,//小于 102 全部是开门截图(web需要根据这个标识过滤所有开门)
    CLOUD_APP_MANUAL = 102,//app手动截图 放在这里是因为截图放的表在一起

    //三方锁相关
    AUTO_LOCK = 200,
    LOCK_BY_APP = 201,
    DOOR_SENSOR_OPEN = 202,
    DOOR_SENSOR_CLOSE = 203,
};

enum MANAGER_TYPE
{
    COMMUNITY = 0,
    PERSONAL = 1,      
};

enum DEV_TYPE
{
   PERSONAL_DEVICE  = 0,
   COMMUNITY_DEVICE = 1,      
};

enum APP_STATE
{
    APP_STATE_OFFLINE = 0, //不在线需要唤醒
    APP_STATE_ONLINE = 1, //在线直接呼叫
    APP_STATE_DND = 2, //app免打扰
    APP_STATE_CALLER_EXPIRE = 3, //主叫过期
    APP_STATE_CALLEE_EXPIRE = 4, //被叫过期
    DEV_STATE_CALLEE_NOT_SUPPORT_SRTP = 5, 
    APP_STATE_CALL_BLOCK = 6, //户户通拉黑
    APP_STATE_LOGOUT = 7, //app logout
    APP_STATE_ONLINE_PUSH = 8, //app在线推送,部分sdk客户需要在线也推送
};

enum LinkerPushMsgType
{
    LINKER_MSG_TYPE_KIT = 1,
    LINKER_MSG_TYPE_QRIO_OPEN_DOOR = 2,
    LINKER_MSG_TYPE_QRIO_CLOSE_DOOR = 3,
    LINKER_MSG_TYPE_YALE_OPEN_DOOR = 4,
    LINKER_MSG_TYPE_YALE_CLOSE_DOOR = 5,
    LINKER_MSG_TYPE_OPENDOOR = 6,
    LINKER_MSG_TYPE_MESSAGE = 7,
    LINKER_MSG_TYPE_TMPKEY = 8,
    LINKER_MSG_TYPE_DELIVERY = 9,
    LINKER_MSG_TYPE_ACCOUNT_EXPIRE = 10,
    LINKER_MSG_TYPE_WEATHER = 11,
    LINKER_MSG_TYPE_PACPORT_REGIST = 12, //pacport云注册
    LINKER_MSG_TYPE_PACPORT_UNREGIST = 13, //pacport云注销
    LINKER_MSG_TYPE_PACPORT_UNLOCK_CHECK = 14, //pacport快递单校验
    LINKER_MSG_TYPE_KIT_CREATE_ROOM = 15,
    LINKER_MSG_TYPE_KIT_DELETE_ROOM = 16,
    LINKER_MSG_TYPE_KIT_RESET_ROOM = 17,
    LINKER_MSG_TYPE_KIT_DELETE_MAC = 18,
    LINKER_MSG_TYPE_KIT_REPLACE_MAC = 19,
    LINKER_MSG_TYPE_DORMAKABA_OPEN_DOOR = 20,
    LINKER_MSG_TYPE_SALTO_OPEN_DOOR = 21,
    LINKER_MSG_TYPE_ITEC_OPEN_DOOR = 22,
    LINKER_MSG_TYPE_TT_OPEN_DOOR = 23,    
    LINKER_MSG_TYPE_KIT_ADD_KIT_DEVICE = 24,
    LINKER_MSG_TYPE_DEVICE_MODIFY_LOCATION = 25,
    LINKER_MSG_TYPE_KIT_REQUEST_ACCOUNT_LOGOUT = 26,
};

enum LinkerExpireType
{
    ACCOUNT_APP_EXPIRE = 1,     //账户过期
    ACCOUNT_FEATURE_EXPIRE = 2, //单住户高级功能过期
};

enum LinkerExpireNotifyType
{
    NOTIFY_WILL_EXPIRE = 1,
    NOTIFY_EXPIRE = 2,
};

enum DEVICE_BRAND
{
   AKUVOX = 0,
   OTHERS = 1,
};

enum USER_TYPE{
     ACCOUNT = 0,
     STAFF = 1,
     DELIVERY = 2,
     USER_TYPE_NULL
};

enum FILE_TYPE {
     FILE_TYPE_JPG = 2,
     FILE_TYPE_WAV = 5,
     FILE_TYPE_VIDEO = 6,
     FILE_TYPE_TAR = 8,
};

enum UPLOAD_IMAGE_ERROR_CODE {
    UPLOAD_IMAGE_SUCCESS = 0,
    UPLOAD_BIG_IMAGE_TO_FDFS_ERROR = 1,
    UPLOAD_BIG_IMAGE_INVALID_URL = 2,
    UPLOAD_BIG_IMAGE_TO_S3_ERROR = 3,
    UPLOAD_SMALL_IMAGE_TO_FDFS_ERROR = 4,
    UPLOAD_SMALL_IMAGE_INVALID_URL = 5,
    UPLOAD_SMALL_IMAGE_TO_S3_ERROR = 6,
    UPLOAD_VOICE_FILE_TO_S3_ERROR = 7,
    UPLOAD_VIDEO_FILE_TO_S3_ERROR = 8,
    UPLOAD_VIDEO_FILE_TO_FDFS_ERROR = 9
};

enum PCAP_CAPTURE_TYPE {
     PCAP_CAPTURE_STOP = 0,
     PCAP_CAPTURE_START = 1,
};

enum TransP2PMsgType {   
     TRAN_TYPE_NONE = 0,    
     TO_DEV_MAC = 1,
     TO_APP_UID = 2,
     TO_DEV_UUID = 3,
     TO_APP_UUID = 4,
     TO_ALL_APP = 5, //群发给项目中的所有APP
     TO_APP_UID_ONLINE = 6, //APP单点，只发在线消息
};

enum MsgEncryptType {   
     ENC_TYPE_NONE = 0,    //无需加密或已加密过
     TYEP_DEFAULT_ENCRYPT,
     TYEP_DEFAULT_MAC_ENCRYPT,
     TYEP_MAC_ENCRYPT,
};

enum CONN_INFO_TYPE{
    DISCONNECT = 0,
    HEARTBEAT = 1,
    LOGOUT = 2,  
};

enum MessageClientType
{
    DEV_SEND = 1,  //设备发送
    APP_SEND = 2,  //app发送
};
    
enum MessageSendStatus
{
    SEND_INCOMPLETE = 0,  //未发送
    SEND_SCUCCESS = 1,  //发送成功
};

enum MessageContentType
{
    TEXT_MSG = 0,  
    DELIVERY_MSG = 1,  
    TMPKEY_MSG = 2,
    DELIVERY_BOX_MSG = 3,  //用于JTS
    YALE_BATTERY_2WEEK = 4,
    YALE_BATTERY_1WEEK = 5,
    YALE_BATTERY_LOW = 6,
    VOICE_MSG = 7,
    LOCKDOWN_ON_MSG = 8,
    LOCKDOWN_OFF_MSG = 9,
};

enum SchedType
{
    ONCE_SCHED = 0,
    DAILY_SCHED,
    WEEKLY_SCHED,
    EACH_DOOR_ONCE_SCHED = 3, //每道门开一次的单次计划
};


enum AzAccountAccessUpdateType
{
    TYPE_ADD = 0,
    TYPE_DEL = 1,
};

enum 
{
    FUNC_DEV_TIME_ZONE = 0, //设备有夏令时功能
    FUNC_DEV_SUPPORT_SRTP = 1,
    FUNC_DEV_SUPPORT_DOWNLOAD_TLS12 = 2,
    FUNC_DEV_SUPPORT_INTERCEPT_EMPTY_AUTHCODE = 3,
    FUNC_DEV_IS_SMARTHOME_DEV = 4,
    FUNC_DEV_IS_NEW_OFFICE_DEV = 5,
    FUNC_DEV_GET_REMOTECONFIG_ADDR_BY_DCLIENT = 6, //通过dclient下发获取远程访问地址
    FUNC_DEV_SUPPORT_RTSP_RTP_CONFUSE = 7,
    FUNC_DEV_SUPPORT_ANTIPASSBACK = 8, // 反潜回
    FUNC_DEV_SUPPORT_HTTP_REDIRECT_HTTPS = 9, //是否支持http重定向到https
    FUNC_DEV_SUPPORT_ANALOG_HANDLE = 10, //是否支持模拟手柄
    FUNC_DEV_SUPPORT_EXTERN_PUSH_BUTTON = 11, //是否支持外接板
    FUNC_DEV_SUPPORT_MULTI_MONITOR = 12, //是否支持多摄像头
    FUNC_DEV_SUPPORT_VIDEO_RECORD = 13,   //是否支持视频存储
    FUNC_DEV_SUPPORT_OPENDOOR_ACK = 14, //是否支持开门校验+结果返回
    FUNC_DEV_SUPPORT_ARMING_REPOST = 16 //是否支持arming转发
};


enum IndoorRelayType
{
    TYPE_LOCAL = 0,
    TYPE_EXTERN = 1,
};

enum ExtraRelayMode
{
    MODE_RS485 = 1,
    MODE_ETHERNET,
    MODE_RS485_INPUT_NOT_LATCHING,
    MODE_RS485_INPUT_LATCHING,
};

//外接relay mode autop对应取值
enum ExtraRelayModeAutoP
{
    DEV_MODE_RS485 = 0,
    DEV_MODE_ETHERNET = 1,
    DEV_MODE_RS485_INPUT_NOT_LATCHING = 2,
    DEV_MODE_RS485_INPUT_LATCHING = 3,
    
};

enum ExtraRelayType
{
    TYPE_NONE = 0,
    TYPE_MK48,
    TYPE_HF,
    TYPE_RSAC_C1_R8,
};

enum ExtraRelayOutputType
{
    TYPE_EXTERN_RELAY = 0,
    TYPE_DIGITAL_RELAY,
};


enum ExtraRelayTypeAutoP
{
    DEV_TYPE_MK48 = 2,
    DEV_TYPE_HF = 3,
    DEV_TYPE_RSAC_C1_R8 = 4,
    
};

//组件内部健康检查类型
enum InnerHealthyCheckType
{
    CHECK_TYPE_NSQ = 0,
    CHECK_TYPE_BEANSTALK,
    CHECK_TYPE_BEANSTALK_BACKUP,
    CHECK_TYPE_REDIS,
    CHECK_TYPE_DB,
    CHECK_TYPE_KAFKA,
    CHECK_TYPE_ROUTE,
    CHECK_TYPE_OPENAPI_SOCKET,
    CHECK_TYPE_FDFS,
    CHECK_TYPE_ETCD,
    /*
    用于表示枚举类型的总数
    */
   CHECK_TYPE_MAX,
};

enum PmEmergencyDoorControlType
{
    CONTROL_TYPE_CLOSE = 0,
    CONTROL_TYPE_OPEN = 1,
};

enum DeviceStatus
{
    DEVICE_STATUS_OFFLINE = 0,
    DEVICE_STATUS_ONLINE = 1,
};

enum AlarmStatus
{
    ALARM_STATUS_UNDEALED = 0,
    ALARM_STATUS_DEALED,
    ALARM_STATUS_MAX,
};

enum PushWebMsgType
{
    PUSH_WEB_MSG_EMERGENCY = 0,
    PUSH_WEB_MSG_ATTENDANCE = 1,
    PUSH_WEB_MSG_ACCESS_DOOR = 2,
};

namespace IDAccessCheck
{

enum CheckRes
{
    CHECK_RES_FAILED = 0,
    CHECK_RES_SUCCESS = 1,
};

enum SchedType
{
    NEVER = 1, //单次计划
    DAILY_SCHED = 2, //日计划
    WEEKLY_SCHED = 3, //周计划
};

enum Mode
{
    OFF = 0,
    RUN = 1,
    RUN_SERIAL = 2,
};

enum IDAccessOwner
{
    ID_ACCESS_OWNER_PM = 1,
    ID_ACCESS_OWNER_USER = 2,
};

}

//booking预约状态
enum AmenityReservationStatus
{
    AMENITY_RESERVATION_SUCCESS = 0,
    AMENITY_RESERVATION_CACEL = 1,
    AMENITY_RESERVATION_INVALID = 2,
};

enum BookingTmpkeyCheckRes
{
    CHECK_RES_SUCCESS = 0,
    CHECK_RES_FAILED,
    CHECK_RES_NOT_BIND_AMENITY,
    CHECK_RES_TMPKEY_NOT_FOUND,
    CHECK_RES_AMENITY_NOT_MATCH,
    CHECK_RES_RESERVATION_STATUS_UNNORMAL,
    CHECK_RES_TMPKEY_USED_TIME_LIMIT,
};

enum AptCallType
{
    APT_CALLTYPE_GROUP_CALL = 0,
    APT_CALLTYPE_SEQUENCE_CALL = 1,
};

//sequence call被叫类型
enum SeqCalleeType
{
    CALLEE_TYPE_INDOOR = 2,
    CALLEE_TYPE_ANALOG_DEVICE = 3, //模拟手柄
    CALLEE_TYPE_APP = 6,
    CALLEE_TYPE_PHONE = 7,
    CALLEE_TYPE_PHONE_2 = 8,
    CALLEE_TYPE_PHONE_3 = 9,
};

enum class DoorLogUserType
{
    NONE = 0,
    VISITOR = 1,
    END_USER = 2,
    STAFF = 3,
    DELIVERY = 4,
};

enum class AkcsDeviceType
{
    AKCS_DEVICE_TYPE_NORMAL = 0,
    AKCS_DEVICE_TYPE_APP,
    AKCS_DEVICE_TYPE_APP_SDK,
};

enum class RelayType
{
    NONE = 0,
    RELAY = 1,
    SECURITY_TYPE = 2,
};

enum class AntiPassbackResult
{
    SUCCESS = 0,
    FAILURE = 1
};

enum class AntiPassbackAccessMode
{
    NORMAL = 0,
    ENTRY = 1,
    EXIT = 2,
    ENTRY_VIOLATION = 3,
    EXIT_VIOLATION = 4
};

enum class AntiPassbackAreaCreatorType
{
    INSTALLER = 1,
    PM = 2,
    ADMIN = 3
};

enum class AntiPassbackScheduleType
{
    ALWAYS = 1,
    DALIY = 2
};

enum class AntiPassbackRestrictionType
{
    DENY_ACCESS = 1,
    LOG_VIOLATIONS_ONLY = 2
};

enum class AreaRestrictionBlockedReason
{
    ENTRY = 1,
    EXIT = 2,
};

enum class AntiPassbackInitiatorType
{
    PERSONNEL = 1,
    TEMPKEY = 2,
    DELIVERY = 3
};

enum class MotionDectionOption
{
	MOTION_DECTION_DISABLED = 0, //禁用移动侦测
	MOTION_DECTION_IR = 1, //红外检测
	MOTION_DECTION_VIDEO = 2 //视频方式检测
};

enum class CallSeqType
{
    CALL_SEQ_TYPE_APP = 1,
    CALL_SEQ_TYPE_DEV = 2,    
    CALL_SEQ_TYPE_LANDLINE = 3,
};

enum class MessageType2
{
    TEXT_MSG = 0,  
    DELIVERY_MSG = 1,  
    TMPKEY_MSG = 2,
    DELIVERY_BOX_MSG = 3,  //用于JTS
    YALE_BATTERY_2WEEK = 4,
    YALE_BATTERY_1WEEK = 5,
    YALE_BATTERY_LOW = 6,
    VOICE_MSG = 7,
    BOOKING_MSG = 8, 
    DOAMAKABA_BATTERY_LOW = 9, //dormakaba低电量通知
    AKUBELA_LOCK_BATTERY_NOTICE = 10, //家居智能锁电量通知
    TRAILERROR_NOTICE =11, //连续试错密码通知
    ITEC_BATTERY_LOW_5_PERCENT = 12, //itec锁电量低于5%通知
    ITEC_BATTERY_LOW_10_PERCENT = 13, //itec锁电量低于10%通知
    ITEC_BATTERY_LOW_15_PERCENT = 14, //itec锁电量低于15%通知
    MAILBOX_ARRIVAL_MSG = 15,
    SMARTLOCK_DOORBELL_EVENT = 16, //智能锁门铃事件通知
    SMARTLOCK_DWELL_EVENT = 17, //智能锁驻留事件通知
    SMARTLOCK_TAMPER_EVENT = 18, //智能锁防撬事件通知
    SMARTLOCK_HIJACK_EVENT = 19, //智能锁劫持事件通知

};

enum class NameOrder
{
    FIRST_NAME_FIRST = 0,
    LAST_NAME_FIRST = 1
};

enum class AnalogMode
{
    ANALOG_MODE_DIRECT = 0,
    ANALOG_MODE_PROXY = 1,
};

enum class SL20LockCredentialType
{
    /*
    对讲目前未下发的方式
    CREDENTIAL_TYPE_PM_PWD = 1, 
    CREDENTIAL_TYPE_PM_CARD = 2, 
    CREDENTIAL_TYPE_PM_FINGERPRINT = 3, 
    */
    CREDENTIAL_TYPE_USER_PIN = 4,
    CREDENTIAL_TYPE_USER_CARD = 5, 
    /*
    对讲目前未下发的方式
    CREDENTIAL_TYPE_USER_FINGERPRINT = 6, 
    CREDENTIAL_TYPE_HIGHJACK_PWD = 7,
    CREDENTIAL_TYPE_HIGHJACK_CARD = 8,
    CREDENTIAL_TYPE_HIGHJACK_FINGERPRINT = 9,
    */
};

enum class SL20LockVersionType
{
    SL20_LOCK_MODULE_VERSION = 0,
    SL20_LOCK_BODY_VERSION = 1,
    SL20_LOCK_COMBINED_VERSION = 2,
};

enum class RemoteControlAckResponseType
{
    RESPONSE_TYPE_NULL = -1,
    RESPONSE_TYPE_APP = 0,
    RESPONSE_TYPE_DEV = 1,
};

enum class DatabaseExistenceStatus
{
    QUERY_ERROR = -1,
    NOT_EXIST = 0,
    EXIST     = 1,
};

enum class SoundType
{
    Gun = 0,
    Dog = 1,
    Baby = 2,
    Glass = 3,
    Siren = 4,
};

enum class VideoRecordCallType
{
    IP = 0,
    SIP = 1
};

enum class VideoRecordPlayStatus
{
    OK = 0,
    NO_PERMISSION = 1,
    NOT_FOUND_TRACE_ID = 2, 
    NOT_FOUND_VIDEO = 3
};

enum class VideoStorageProjectType
{
    PERSONAL = 1,
    COMMUNITY = 2,
    OFFICE = 3
};

enum GroundFloorType
{
    GROUND_FLOOR_CONFIGURE_ON_DEVICE = 1,//云端不做下发，设备本地配置
    GROUND_FLOOR_NONE = 2,//无中间楼层
    GROUND_FLOOR_G0 = 3,
    GROUND_FLOOR_G0_G1 = 4,
    GROUND_FLOOR_G0_G1_G2 = 5
};

enum class KitEndUserDisplayStatus
{
    USER_REG_CODE = 0, //下发用户注册二维码
    USER_REG_INFO = 1, //下发用户注册信息
    CLEAR_USER_INFO = 2, //清空用户信息
};

enum class OpenDoorRelayType
{
    RELAY = 0,
    SECURITY_RELAY = 1,
};

enum class ProjectCreatorType
{
    DEFAULT = 1,  //默认AK项目
    INSTALLER_KIT = 2,  //通过Installer Kit创建的项目
};

enum class DevOnlineStatus
{
    OFFLINE = 0,
    ONLINE = 1
};

enum class DoorRelayAccessMode
{
    ACCESS_OUT = 0,
    ACCESS_IN = 1
};

enum class MusterType
{
    ILLEGAL = 0, //不合法值用0
    USER = 1, //用下发user点名的，目前包括personnel和delivery
    TMPKEY = 2, //用tmpkey点名的，目前暂未做(7.1.1)
    

    TYPE_LIMIT,
};

enum class MusterReportAccountType
{
    PERSONNEL = 0,
    DELIVERY = 1,
};

enum class NotifyWebAccessDoorMessageID
{
    ENTRY_OR_EXIT_RECORD = 0, //进出记录
    MUSTER_READER = 1,  // 点名
};

enum class DoorEntryExitMode
{
    ERROR = -1,
    ENTRY = 0,
    EXIT = 1,
};

enum class AlarmNotifyTargetType
{
    DEV_MANAGEMENT  = 0,    // 管理机
    DEV_INDOOR      = 1,    // 室内机
    DEV_OUTDOOR     = 2,    // 门口机
    APP_USER        = 3,    // User APP
    APP_PM          = 4,    // PM   APP
    APP_ADMIN       = 5,    // AdminAPP
};

enum class AdminAppStatus
{
    DISABLE = 0,
    ENABLE = 1,
};

enum class ContactDevType
{
    DOOR,
    INDOOR,
    MANAGER
};

enum class CardType
{
    INITIAL_CARD = 0, //最早的card类型默认为0，包括RfCard,ble,nfc
    LICENSE_PLATE = 1, //车牌类型
};

enum class ParkingIoType
{
    ERROR = -1,
    ENTRY_AND_EXIT = 0,
    ENTRY = 1,
    EXIT = 2,
};

enum class CommonProjectType
{
    ERROR = -1,
    SINGLE = 1,
    COMMUNITY = 2,
    OFFICE = 3,
};

enum class ActLogRelayEntryMode
{
    EXIT = 0,
    ENTRY = 1,
};

enum class DoorRelayType
{
    RELAY = 0,
    SECURITY_RELAY = 1,
};

enum class PhoneUserType
{
    END_USER = 0,
    ADMIN = 1,
};

enum class SmartLockEventType
{
    DOORBELL_EVENT = 1, //门铃事件
    DOOR_OPEN_EVENT = 2, //开门事件
};

enum class NotifySmartLockType
{
    NOTIFY_SMARTLOCK_TYPE_SL20 = 0,
};

enum class SL20LockProjectType
{
    PERSONAL = 1,
    COMMUNITY = 2,
};

enum class CallGroupType
{
    NONE = 0, // 单呼/群呼--无法确定
    GROUP_LOCAL = 1, // 本地群呼
    GROUP_APT = 2, // APT群呼
    GROUP_MANAGE = 3, // 管理员群呼
};

enum class CallType
{
    NONE = 0,
    APP2APP = 1,
    DEV2APP = 2,
    APP2DEV = 3,
    DEV2DEV = 4,
    DEV2PHONE = 5,
    APP2PHONE = 6,
    GROUP_CALL = 7, //群呼
    GROUP_EACH_CALL = 8, //群呼时群组下每个账号单独记录
};

#endif //__AKCS_COMMON_DEF_H__
