#ifndef __AKCS_COMMON_ST_H__
#define __AKCS_COMMON_ST_H__
#include "BasicDefine.h"
#include "AkcsCommonDef.h"

#define IP_ADDR_LEN     32
#define AKCS_SIP_SIZE   64

#define D_CLIENT_VERSION_1_0            1 //处理下载联系人用文件方式
#define D_CLIENT_VERSION_4400           4400 //V4.4
#define D_CLIENT_VERSION_4600           4600 //V4.6
#define D_CLIENT_VERSION_5300           5300 //V5.3
#define D_CLIENT_VERSION_5400           5400 //V5.4
#define D_CLIENT_VERSION_6000           6000 //V6.0
#define D_CLIENT_VERSION_6100           6100 //V6.1
#define D_CLIENT_VERSION_6500           6500 //V6.5
#define D_CLIENT_VERSION_6533           6533 //室内机群呼Management Center版本
#define D_CLIENT_VERSION_6807           6807 //新办公Dclient版本

//设备(室外机)上报动作消息(即Logs：呼叫，输入密码，卡开门等开门动作)给平台
typedef struct SOCKET_MSG_DEV_REPORT_ACTIVITY_T
{
#define LOG_INITIATOR_SIZE  64
    char mac[MAC_SIZE];
    char pic_name[PIC_NAME_SIZE];//截图的图片名称
    char third_camera_pic_name[PIC_NAME_SIZE];//第三方截图的图片名称
    char capture_action[CAPTURE_ACTION_SIZE];
    char initiator[CAPTURE_INITIATOR_SIZE];     // 设备上报的initiator字段, 跟type字段挂钩，Type=Initiator的关系如下：CALL=sip(账号); INPUTPWD=key; CARD=card code
    char initiator_sql[CAPTURE_INITIATOR_SIZE]; // 根据initiator和type 转化为使用者名称
    int  act_type; //csmain::ActType
    int  resp; //csmain::CaptureLogRetType
    char account[USER_SIZE];
    char room_num[64];
    char key[32];  // 设备上报的initiator, 人脸开门key置为"--"
    uint32_t  mng_id; //单住户不用赋值
    int  unit_id;
    char unit_uuid[64];
    int  is_public; //1公共设备      0个人
    int  mng_type;  //1个人 0社区
    char location[LOCATION_SIZE]; //设备的location
    char sip_account[SIP_SIZE];
    char per_id[64]; //对应User文件里面的唯一标识
    char face_url[256]; //注册时的人脸图片url
    int depart_mode; //1表示离开；0表示进去
    char msg_seq[32];
    char pic_url[256];
    char spic_url[256];
    int64_t capture_time;
    char relay[32];
    char project_uuid2[64]; //社区/办公为项目uuid，单住户不初始化
    char srelay[32];
    char account_uuid[64]; //开门人的uuid
    char company_uuid[64];
    DoorLogUserType user_type; //开门日志记录使用者类型
    uint32_t grade;       //设备的归属等级，是社区共享，单元共享，还是用户独占
    int access_mode;      // 反潜回开门方式: 0-Normal, 1-Entry, 2-Exit, 3-Entry Violation(违规进入), 4-Exit Violation(违规出去)
    char call_trace_id[64];
    char video_record_name[256]; // 视频存储文件名
    char indoor_mac_list[512];   // 走ip群呼室内机的mac列表,最多20个
    char dev_uuid[64];
    char video_url[256];
    char access_door_user_uuid[64]; // personnel-OfficePersonnel的uuid, delivery-OfficeDelivery的uuid
    char relay_entry_mode[16];
    char security_relay_entry_mode[16];
    char door_name_list[512];   // door名称列表;分割
    DatabaseExistenceStatus is_attendance;   //是否考勤设备

    char db_delivery_uuid[64]; //项目uuid 或者是单住户主账号uuid

    SOCKET_MSG_DEV_REPORT_ACTIVITY_T() {
        memset(this, 0, sizeof(*this));
    }
} SOCKET_MSG_DEV_REPORT_ACTIVITY;

//设备信息
typedef struct DEVICE_SETTING_T
{
    uint32_t id;
    uint32_t matched; //该标志位用于遍历列表时标志该节点是否有被匹配过
    uint32_t type;
    uint32_t extension;
    uint32_t status;
    uint32_t port;
    uint32_t manager_account_id;
    char community[32]; //对个人终端用户就是:个人终端管理员
    char device_node[32];//对个人终端用户就是:主账号/联动系统
    char ip_addr[40];
    char wired_ip_addr[40];
    char subnet_mask[40];
    char wired_subnet_mask[40];
    char gateway[40];
    char primary_dns[40];
    char secondary_dns[40];
    char mac[20];
    char outer_ip[40];//外网IP
    char download_server[64];
    char SWVer[32];
    char HWVer[32];
    char last_connection[24];
    //char szConfigSettings[DEVICESETTING_CONFIGSETTINGS_SIZE];
    char private_key_md5[36];
    char rf_id_md5[36];
    char config_md5[36];
    char location[64];
    char sip_account[32];
    char rtsp_password[36];
    char sip_password[64]; //sip密码
    //uint32_t  nBindAppCount;
    char contact_md5[36];
    char face_md5[36];
    char auth_code[32];//鉴权码   
    csmain::DeviceType device_type; //设备类型,社区还是个人终端
    uint32_t unit_id;// 单元id
    uint32_t grade;//设备的归属等级，是社区共享，单元共享，还是用户独占
    uint32_t dclient_version;//dclient版本，联系人列表扩大，4096不够用，所以用下载方式。兼容前面版本
    uint32_t indoor_arming; //室内机arming
    short is_personal;//是不是单住户 办公和社区=0
    short is_public;
    short oem_id;
    short firmware;
    int flag;//第一位用于标识是否是个人终端里面的公共
    int is_ip_addr_change;
    int is_dev_upgrade;    
    int relay_status;//目前只是室内机的relay
    char user_meta_md5[36];
    char schedule_md5[36];
    int flags; //按位标识 1=home;2=away;3=sleep;4=管理机是否开启全选,默认开启;5-8位代表设备relay的开关情况 0关1开;9=室内机上线标识;10=室内机所属的家庭是否为Kit方案;
    int project_type; //0住宅，1办公 2单住户

    char uuid[64];
    int brand;  //品牌 0-akuvox 1-other
    int init_status;  //dis,ins信息初始化状态
    char node_uuid[64];
    char project_uuid[64];//项目有赋值，单住户没有赋值
    char community_unit_uuid[64];
    char ins_uuid[64];
    int enable_smarthome;
    char relay[4096];//dtmf 信息。#,name,1,1;*,xx,是否显示在home,是否显示在talking;
    char security_relay[4096];//dtmf 信息。#,name,1,1;*,xx,是否显示在home,是否显示在talking;

    char push_button[512];
    short is_expire;//标识是否过期
    short netgroup_num;//网络编号

    //v4.1 discreet
    short enable_motion;
    short motion_time;
    short enable_robin_call;
    short robin_call_time;
    char robin_call_val[256];

    short is_communit_pub_dev;//是否是社区公共设备
    short enable_ip_direct;
    short stair_show; //梯口机列表显示配置  0默认 1roomnum 2app/indoor
    char config[2064];//新增的配置
    char mng_account[64];//新增的配置
    uint32_t sip_type;// siptype
    char seq[64];//分组呼叫
    char user_info_md5[36];
    int repost;//转流标识

    uint64_t fun_bit;//功能bit位

    int dynamics_iv;//aes 动态ivs
    bool is_ipv6;
    bool is_new_office;
    int allow_end_user_monitor;
    uint8_t camera_num;
    uint32_t allow_end_user_hold_door; //设备是否允许终端用户设置门常开
    char create_time[32]; //形如2024-11-07 10:26:30
    uint64_t conn_version; //链接更新的版本
    
    struct DEVICE_SETTING_T* next;
    int enable_package_detection;
    // int enable_sound_detection;
    // int sound_type;
} DEVICE_SETTING;

typedef struct SOCKET_MSG_LINKER_NORMAL_NOTIFY_T
{
    char dev_uuid[64];
    char dev_name[64];
    int dev_type;
    int dev_grade;
    char account_uuid[64];
    char node_uuid[64];
    char account_name[64];
    int project_type;
    char project_uuid[64];
    char timestamp[64];
    char ins_uuid[64];
    char language[32];
    time_t phone_expire_timestamp;
    time_t expire_timestamp;
    int enable_smarthome;
    int role;
}LINKER_NORMAL_MSG;

typedef struct PbxCallHistory_T
{
#define VALUE_SIP_SIZE          16
#define VALUE_NAME_SIZE         128
#define VALUE_TIME_SIZE         32
#define VALUE_IP_ADDR_SIZE      32

    int bill_second; //接听时长
    char caller[VALUE_SIP_SIZE];//主叫
    char callee[VALUE_SIP_SIZE];//被叫---被叫和接听和pbx相反，有历史原因
    char called[VALUE_SIP_SIZE];//接听
    char start_time[VALUE_TIME_SIZE];
    char answer_time[VALUE_TIME_SIZE];

    char bill_second_time[VALUE_TIME_SIZE];
    //below section is not in msg
    int is_answer; // 1 is anwer / 0 not answer
    int status; //代表是否已读，如果是接听就是1 未接听时候插入时候就是0
    char caller_name[VALUE_NAME_SIZE];
    char callee_name[VALUE_NAME_SIZE];
    char called_name[VALUE_NAME_SIZE];
    char node[32];
    char sipgroup[VALUE_SIP_SIZE]; //群组呼叫没人接听时候，会记录这个字段
    int mng_id; //社区个人管理员ID
    int calltype;//呼叫类型 1=app->app 2=dev->app 3=app->dev 4=dev->dev
    char freeswitch_node[VALUE_IP_ADDR_SIZE];
    char caller_ops_node[VALUE_IP_ADDR_SIZE];
    char callee_ops_node[VALUE_IP_ADDR_SIZE];
    char caller_uuid[64];// 主叫 (PersonalAccountUUID / DeviceUUID)
    char callee_uuid[64];// 被叫 (PersonalAccountUUID / DeviceUUID) ---被叫和接听和pbx相反，有历史原因
    char company_uuid[64];
    char call_trace_id[64];
    char group_call_list[1024];
    int caller_role;
    int callee_role;
    char project_uuid2[64]; //社区/办公为项目uuid，单住户不初始化
    char db_delivery_uuid[64]; //项目uuid 或者是单住户主账号uuid
    CallGroupType call_group_type; // 呼叫类型
    char glocal_callee_name[VALUE_NAME_SIZE];

    struct PbxCallHistory_T* next;
} PbxCallHistory;

//设备截图事件
typedef struct PERSONNAL_CAPTURE_T
{
    uint32_t id;
    int  type; //csmain::CaptureType
    char mac[20];
    char picture_name[256];
    char capture_log[64];
    char capture_time[24];
    char account[32];
    int  manager_id;
    int  device_type; //1公共设备   0个人
    int  manager_type;//1个人0社区
    char location[64]; //设备的location
    char sip_account[32];
    char project_uuid2[64]; //社区/办公为项目uuid，单住户不初始化
    char db_delivery_uuid[64]; //项目uuid 或者是单住户主账号uuid

    char dev_uuid[64]; // 设备uud
    char video_record_name[256]; // 视频存储文件名
    int  detection_type; //移动侦测类型 0:移动侦测1:包裹检测2声音检测
    int  detection_info; //侦测类型为1时，这里的0包裹放入，1包裹放出；侦测类型为2时，这里的0枪声 1狗叫声 2孩子哭声 3玻璃破碎 4警笛
} PERSONNAL_CAPTURE;

//APP请求平台进行截图,app->csmain->vrtsp
typedef struct UIPC_MSG_CAPTURE_RTSP_T
{
    char mac[16];
    char picture_name[256]; //截图的图片名称
    char node[32];
    char username[128];//谁截的图
    int capture_type; //截图类型，app手动截图
    char room_num[16];//哪个房间
    int  manager_id;
    int  device_type; //1公共设备   0个人
    int  manager_type;//1个人0社区
    char location[64]; //设备的location
    char sip_account[32];
    char project_uuid[64]; //社区/办公为项目uuid，单住户不初始化
    char db_delivery_uuid[64]; //项目uuid 或者是单住户主账号uuid
    char company_uuid[64]; //公司UUID（当前仅在新办公项目中使用）
    char flow_uuid[64];
    char dev_uuid[64]; // 设备uuid
    UIPC_MSG_CAPTURE_RTSP_T() {
        memset(this, 0, sizeof(*this));
    }
} UIPC_MSG_CAPTURE_RTSP;

typedef struct INNER_CONN_INFO_T
{
    char uid[32];
    char username[64];
    csmain::DeviceType conn_type;//设备/app
    int role;
    int type;//1掉线 2心跳； 上线可以通过上报状态确认
} InnerConnInfo;

typedef struct INNER_CONN_INFO_APP_T
{
    char node[32];
    char account[32];   //实际站点
    char main_site[32];   //主站点
    uint32_t account_role;  //主站点的role
    uint32_t real_site_role; //实际站点role
    uint32_t manager_id;
    uint32_t unit_id;
} InnerConnInfoApp;


typedef struct AKCS_TIME_ZONE_T
{
    char time_zone[16];
    char name[32];
    char start[16];
    char end[16];
    int type;
    int offset;
} AKCS_TIME_ZONE;

//联系人结构
typedef struct DEVICE_CONTACTLIST_T
{
    char name[64]; //用户名32->64
    char sip_account[32];
    char ip[16];
    char mac[20];
    char rtsp_pwd[36];
    char room_num[32];
    char sip_group[32];
    int  id;
    int  enable_ip_direct;
    int  type;
    int  phone_status; //0:不使用落地呼叫，1:使用
    int  call_type; //enum NODE_CALL_TYPE
    int  call_loop; //enum NODE_CALL_TYPE=NODE_CALL_TYPE_APP_BACK_PHONE
    char phone[64];
    char phone2[64];
    char phone3[64];
    char phone_code[64];
    char room_name[128];
    char seq[64];//呼叫顺序
    char seq2[64];//单呼从账号 呼叫顺序
    int role;
    char unit_apt[32];//lifesyle需求 房间唯一标识building-apt
    uint32_t unit_id;
    int only_apt; //没有创建用户
    char uuid[64];
    char parent_uuid[64];
    int switch_flag; //按位开关标示符
    char floor[8];
    char email[64];
    char mobile_number[32];
    uint32_t mng_account_id;
    char firstname[128];
    char lastname[128];
    char unit_uuid[64];
    int app_login_status; //0:未登陆过app, 1:登陆过app
} DEVICE_CONTACTLIST;

typedef struct PRIVATE_KEY_T
{
#define PRIVATE_KEY_CODE_SIZE       64
#define PRIVATE_KEY_USER_SIZE       64
#define PRIVATE_KEY_DESC_SIZE       256
#define PRIVATE_KEY_ACCESS_SIZE     4096
    uint32_t id;
    uint32_t type;
    uint32_t status;
    uint32_t account_id;
    uint32_t unit_id;
    uint32_t mng_account_id;
    char code[PRIVATE_KEY_CODE_SIZE];
    char user[128];  //对于个人终端用户,是允许的从账号,对于社区用户是允许的物业等
    char create_time[DATETIME_SIZE];
    char expire_time[DATETIME_SIZE];
    char desc[PRIVATE_KEY_DESC_SIZE];
    char access[PRIVATE_KEY_ACCESS_SIZE]; //个人终端用户这里是设备的mac地址,对于社区就是设备节点:1.1.2.3.1-2的形式
    char node[32]; //个人终端用户主账号
    DEVICE_SETTING* access_device_list; //每一把key能访问的设备列表,个人终端用户这里是设备的mac地址,对于社区就是设备节点:1.1.2.3.1-2的形式
    uint32_t types;//1物业 2快递
    uint32_t room_id;
    char time_start[DATETIME_SIZE];
    char time_end[DATETIME_SIZE];
    char day_start[DATETIME_SIZE];
    char day_end[DATETIME_SIZE];
    char access_mac[MAC_SIZE];          // 有权限的设备Mac
    int week_day;
    int relay;
    int security_relay;
    int building;//unit id
    char apt[128];// room number
    int sche_type;
    int date_flag;
    CardType card_type;
    char ufh[PRIVATE_KEY_CODE_SIZE];
    struct PRIVATE_KEY_T* next;
} PRIVATE_KEY;

typedef PRIVATE_KEY RF_KEY;
typedef PRIVATE_KEY DEV_COMM_KEY;

//个人公共设备 外联设备信息
typedef struct PER_NODE_DEVICES_T
{
    int  node_id;
    char node[32];
} PER_NODE_DEVICES;

typedef struct COMMUNIT_UNIT_INFO_T
{
    int unit_id;
} COMMUNIT_UNIT_INFO;

typedef struct SOCKET_MSG_DEV_REPORT_INDOOR_RELAY_STATUS_T
{
    char relay_ids[64]; //对应开启的relay id列表
    IndoorRelayType relay_type; //室内机relay类型
    SOCKET_MSG_DEV_REPORT_INDOOR_RELAY_STATUS_T()
    {
        memset(this, 0, sizeof(*this));
    }
} SOCKET_MSG_DEV_REPORT_INDOOR_RELAY_STATUS;

typedef struct EMERGENCY_DOOR_CONTROL_INFO_T
{
    char uuid[64]; //对应PmEmergencyDoorLog表的UUID
    int control_type; //0-一键关门 1-一键开门
    EMERGENCY_DOOR_CONTROL_INFO_T() {
        memset(this, 0, sizeof(*this));
    }
} EmergencyDoorControlInfo;

typedef struct OFFLINE_PUSH_MSG_INFO_T
{
    int force_push; //是否走强制推送
    char data[1024];
    OFFLINE_PUSH_MSG_INFO_T()
    {
        memset(this, 0, sizeof(*this));
    }
} OfflinePushInfo;

typedef struct ID_ACCESS_INFO_T
{
    /* data */
    int id_access_mode;
    char id_access_run[32];
    char id_access_serial[32];
    ID_ACCESS_INFO_T()
    {
        memset(this, 0, sizeof(*this));
    }
    ID_ACCESS_INFO_T(int mode, const std::string& run, const std::string& serial) : id_access_mode(mode)
    {
        snprintf(id_access_run, sizeof(id_access_run), "%s", run.c_str());
        snprintf(id_access_serial, sizeof(id_access_serial), "%s", serial.c_str());
    }
} IDAccessInfo;

typedef struct CHECK_VISITOR_ID_ACCESS_INFO_T
{
    /* data */
    char visitor_uuid[36]; //用于后续更新次数
    char now_time_with_ymd[64];
    char now_time[64];
    int day_of_week;
    int mode;
    char run[16];
    char serial[16];
    char mac[32];
    int allowed_times;
    int access_times;
    int check_res;
    int relay_val;
    int se_relay_val;
    int sche_type;
    char name[128];

    CHECK_VISITOR_ID_ACCESS_INFO_T()
    {
        memset(this, 0, sizeof(*this));
    }
} CheckVisitorIDAccessInfo;

typedef struct APP_ASYNC_RESPONSE_MSG_T
{
    char err_code[16]; //对应返回App的错误码
    char message[128]; //对应返回App的message
    char json_datas[2048]; //对应返回App的datas

    APP_ASYNC_RESPONSE_MSG_T()
    {
        memset(this, 0, sizeof(*this));
    }
} AppAsyncResponseMsg;

typedef struct SOCKET_MSG_DEV_REPORT_UPDATE_DOOR_IP_T
{
    char mac[32]; 
    char ip[32]; 
    SOCKET_MSG_DEV_REPORT_UPDATE_DOOR_IP_T()
    {
        memset(this, 0, sizeof(*this));
    }
}SOCKET_MSG_DEV_REPORT_UPDATE_DOOR_IP;

typedef struct ACCESS_DOOR_NOTIFY_MSG_T
{
    char account_type[16]; // personnel/delivery
    char user_uuid[64]; // personnel-OfficePersonnel的UUID， delivery—OfficeDelivery的UUID
    char access_mode[16]; // 进-entry 出-exit
    char door_uuid_list[512]; // door uuid list, 多个用","分隔
    uint32_t timestamp;

    ACCESS_DOOR_NOTIFY_MSG_T()
    {
        memset(this, 0, sizeof(*this));
    }
} AccessDoorNotifyMsg;

typedef struct PARKING_LOG_T
{
    ParkingIoType  io_type; //0匹配当前车辆情况进出1进去2离开-1为未匹配上
    char project_uuid[64]; //项目uuid
    CommonProjectType  project_type;
    char parking_lot_uuid[64];
    char personal_account_uuid[64];
    char office_company_uuid[64];
    char unit_uuid[64];
    char license_plate[32];
    long parking_time;
    char parking_door[256];
    char parking_pic_name[256];
    char mac[MAC_SIZE];
    
    char entry_door[256]; 
    char entry_time[VALUE_TIME_SIZE];
    char entry_pic_name[256];
    char entry_pic_url[256];
    char entry_spic_url[256];
    
    char exit_door[256]; 
    char exit_time[VALUE_TIME_SIZE];
    char exit_pic_name[256];

    PARKING_LOG_T() {
        memset(this, 0, sizeof(*this));
    }
} PARKING_LOG;


typedef struct CALL_HISTORY_RECORD_T
{
    char table_name[64];
    int table_call_history_id;
    int call_type;
    int is_answer;
    char answer_name[VALUE_NAME_SIZE];
    int answer_duration;

    CALL_HISTORY_RECORD_T()
    {
        memset(this, 0, sizeof(*this));
    }
} CallHistoryRecord;

#endif //__AKCS_COMMON_ST_H__

