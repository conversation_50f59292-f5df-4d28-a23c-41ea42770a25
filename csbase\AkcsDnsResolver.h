#ifndef __AKCS_DNS_RESOLVER_H__
#define __AKCS_DNS_RESOLVER_H__

#include <evpp/event_loop.h>
#include <evpp/event_loop_thread.h>
#include <evpp/dns_resolver.h>


typedef std::function<int(const std::vector <std::string>& addrs)> DNSResolverChange;

class AkcsDNSResolver
{
public:
    AkcsDNSResolver(const std::string &domain, evpp::EventLoop *loop):loop_(loop),domain_(domain)
    {
        auto pos = domain.find(":");
        if (std::string::npos != pos)
        {
            domain_ = domain.substr(0, pos);
        }       
        loop_->RunAfter(1, std::bind(&AkcsDNSResolver::OnResolver, this));
        loop_->RunEvery(evpp::Duration(30.0), std::bind(&AkcsDNSResolver::OnResolver, this));
    }
    void OnResolver()
    {
        auto fn_resolved = [this](const std::vector <struct in_addr>& addrs) {
            uint64_t now_ip_count = 0;
            std::vector<std::string> ips;
            for (auto &a : addrs)
            {
                //根据ip的十进制相加进行判断是否变化
                now_ip_count += a.s_addr;
                std::string ip = inet_ntoa(a);
                ips.push_back(ip);
            }
            if (now_ip_count !=0 && last_ip_count_ != now_ip_count)
            {
                LOG_INFO << "Domain:" << domain_ << " IP CHANGE!";
                last_ip_count_ = now_ip_count;
                if (dns_change_fn_)
                {
                    dns_change_fn_(ips);
                }
            }
            
        };

        evpp::Duration delay(double(2.0)); // 3s
        std::shared_ptr<evpp::DNSResolver> dns_resolver(
            new evpp::DNSResolver(loop_, domain_, delay, fn_resolved));
        dns_resolver->Start();
        dns_resolver.reset();    
    }
    void SetOnChange(DNSResolverChange fn)
    {
        dns_change_fn_ = fn;
    }

public:
    evpp::EventLoop *loop_;
    std::string domain_;
    uint64_t last_ip_count_ = 0;
    DNSResolverChange dns_change_fn_;
};

#endif /* __AKCS_DNS_RESOLVER_H__ */
