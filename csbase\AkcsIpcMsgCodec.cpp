#include <string.h>
#include <functional>
#include <memory>
#include <evpp/tcp_conn.h>
#include <evpp/buffer.h>
#include "AkcsPduBase.h"
#include "AkcsIpcMsgCodec.h"
#include "AkLogging.h"


//| package length   | true  | int32 bigendian | 包长度   |
//| header Length    | true  | int16 bigendian | 包头长度 |
//| ver              | true  | int16 bigendian | 协议版本 |
//| id               | true  | int32 bigendian | 协议指令 |
//| seq              | true  | int32 bigendian | 序列号      |
//| traceid          | true  | uint64 bigendian | 跟踪id    |
//| body             | false | binary          | 具体消息 |

void AkcsIpcMsgCodec::OnMessage(const evpp::TCPConnPtr& conn, evpp::Buffer* buf)
{
    while (buf->size() >= kHeaderLen)
    {
        const uint32_t len = buf->PeekInt32(); //获取包长度
        if (len > 65536 || len < sizeof(PduHeader_t))
        {
            AK_LOG_WARN << "Invalid length " << len;
            conn->Close();
            break;
        }

        if (buf->size() >= len)
        {
            std::unique_ptr<CAkcsPdu> pPdu(new CAkcsPdu());
            //std::unique_ptr<CAkcsPdu> pPdu = std::make_unique<CAkcsPdu>(); c++14的标准
		    pPdu->Write(buf->data(), len); //包整体长度全部整进去
		    char tmp_buf[sizeof(PduHeader_t)] = {0};
            memcpy(tmp_buf, buf->data(), sizeof(tmp_buf));
		    if (pPdu->ReadPduHeader(tmp_buf, sizeof(PduHeader_t)) != 0) //赋值包长度的信息,并校验包头长度是否正确
		    {
				 AK_LOG_WARN << "Pdu packet header len is invalid";
            }
            else
            {
				message_callback_(conn, pPdu); //解码完之后，保证是一条完整的消息，然后就调用完整的消息处理函数
            }
		    buf->Skip(len);
        }
        else
        {
            break;
        }
    }
}

void AkcsIpcMsgCodec2::OnMessage(const evpp::TCPConnPtr& conn, evpp::Buffer* buf)
{
    while (buf->size() >= kHeaderLen)
    {
        const uint32_t len = buf->PeekInt32(); //获取包长度
        if (len > 65536 || len < sizeof(PduHeader_t))
        {
            AK_LOG_WARN << "Invalid length " << len;
            conn->Close();
            break;
        }

        if (buf->size() >= len)
        {
            std::unique_ptr<CAkcsPdu> pPdu(new CAkcsPdu());
            //std::unique_ptr<CAkcsPdu> pPdu = std::make_unique<CAkcsPdu>(); c++14的标准
		    pPdu->Write(buf->data(), len); //包整体长度全部整进去
		    char tmp_buf[sizeof(PduHeader_t)] = {0};
            memcpy(tmp_buf, buf->data(), sizeof(tmp_buf));
		    if (pPdu->ReadPduHeader(tmp_buf, sizeof(PduHeader_t)) != 0) //赋值包长度的信息,并校验包头长度是否正确
		    {
				 AK_LOG_WARN << "Pdu packet header len is invalid";
            }
            else
            {
				message_callback2_(conn, pPdu); //解码完之后，保证是一条完整的消息，然后就调用完整的消息处理函数
            }
		    buf->Skip(len);
        }
        else
        {
            break;
        }
    }
}

