#ifndef __AKCS_BASE_MSG_CODEC_H__
#define __AKCS_BASE_MSG_CODEC_H__

#include <functional>
#include <evpp/tcp_conn.h>
#include <evpp/buffer.h>
#include "AkcsPduBase.h"

class AkcsIpcMsgCodec {
public:
    typedef std::function<void(const evpp::TCPConnPtr&,
                            const std::unique_ptr<CAkcsPdu>&)> StringMessageCallback;
    explicit AkcsIpcMsgCodec(const StringMessageCallback& cb)
        : message_callback_(cb) {}
    void OnMessage(const evpp::TCPConnPtr& conn, evpp::Buffer* buf);
    
private:
    StringMessageCallback message_callback_;
    const static size_t kHeaderLen = sizeof(int32_t);
};

//新增一个类 主要是csroute回调时const类型不匹配问题
class AkcsIpcMsgCodec2 {
public:
    typedef std::function<void(const evpp::TCPConnPtr&,
                            std::unique_ptr<CAkcsPdu>&)> StringMessageCallback2;
    explicit AkcsIpcMsgCodec2(const StringMessageCallback2& cb)
        : message_callback2_(cb) {}
    void OnMessage(const evpp::TCPConnPtr& conn, evpp::Buffer* buf);
    
private:
    StringMessageCallback2 message_callback2_;
    const static size_t kHeaderLen = sizeof(int32_t);
};


#endif /* __AKCS_BASE_MSG_CODEC_H__ */
