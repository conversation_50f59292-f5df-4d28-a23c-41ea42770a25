#include <time.h>
#include <string>
#include <stdlib.h>
#include <sstream>
#include "AkLogging.h"
#include "json/json.h"
#include "AkcsMonitor.h"
#include "ConfigFileReader.h"

static const char alarm_tiopic[] = "akcs_alarm";
static const char add_iptables_tiopic[] = "akcs_add_iptables";
using namespace Akcs;
void SystemMonitor::Init(evnsq::Producer *client)
{
	is_init_ = true;
	client_ = client;
	//topic_ = monitor_tiopic;
	//add by chenzhx 初始化内网外网ip
    CConfigFileReader ipfile("/etc/ip"); 
    hostname_ = ipfile.GetConfigName("AKCS_HOSTNAME");
    out_ip_ = ipfile.GetConfigName("SERVERIP");
}

int SystemMonitor::TriggerMonitorAlarm(const std::string& worker_node, const std::string& description, const std::string&alarm_key)
{
    //获取当前的大致时间。
    time_t setTime;
	time(&setTime);
	tm* ptm = localtime(&setTime);
	std::string time = std::to_string(ptm->tm_year + 1900)
	                   + "-"
	                   + std::to_string(ptm->tm_mon + 1)
	                   + "-"
	                   + std::to_string(ptm->tm_mday)
	                   + " "
	                   + std::to_string(ptm->tm_hour) + ":"
	                   + std::to_string(ptm->tm_min) + ":"
	                   + std::to_string(ptm->tm_sec);	

    return PushAlarmMsg(worker_node, description, time, alarm_key);
}

int SystemMonitor::PushAlarmMsg(const std::string& worker_node, const std::string& description, const std::string& 
time, const std::string&alarm_key)
{
    Json::Value item;
    Json::FastWriter w;
	item["node"] = worker_node;
	item["time"] = time;
    item["key"] = alarm_key;    
    item["description"] = description;//description 就是对应得value,再iptables告警下，就是对应得black_ip
    item["hostname"] = hostname_;
    item["ip"] = out_ip_;
	std::string alarm_msg_json = w.write(item);
	AK_LOG_INFO << "push monitor alarm msg:" << alarm_msg_json;
    //投递给nsq
    if(!is_init_)
    {
		AK_LOG_WARN << "SystemMonitor has not been init";
		return -1;
	}
    if (!client_->Publish(alarm_tiopic, alarm_msg_json))
    {
		AK_LOG_WARN  << "push monitor alarm msg to nsq failed: " << alarm_msg_json;
		//added by chenyc,v5.2,2020-04-03,当发布失败的时候,有概率应用组件跟nsq之间已经断开连接,且健康检查失败了,此时告警系统也必然失败
		//暂时采用硬编码的方式,直接在本进程调用邮件mutt的可执行文件
		char cmd[1024] = {0};
        snprintf(cmd, sizeof(cmd), "echo \"%s\" | mutt -s \"SystemMonitor publish alarm msg error\"  -b <EMAIL> -c  <EMAIL> -c <EMAIL> \
			     <EMAIL> &", alarm_msg_json.c_str());
        system(cmd);
        return -1;
    }
    return 0;
}

//added by chenyc,2020-03-25, 增加ip攻击黑名单的基础函数
void SystemMonitor::TriggerMonitorIptables(const std::string& worker_node, const std::string& iptables_ip)
{
    //获取当前的大致时间。
    time_t setTime;
	time(&setTime);
	tm* ptm = localtime(&setTime);
	std::string time = std::to_string(ptm->tm_year + 1900)
	                   + "-"
	                   + std::to_string(ptm->tm_mon + 1)
	                   + "-"
	                   + std::to_string(ptm->tm_mday)
	                   + " "
	                   + std::to_string(ptm->tm_hour) + ":"
	                   + std::to_string(ptm->tm_min) + ":"
	                   + std::to_string(ptm->tm_sec);	

    PushIptablesMsg(worker_node, time, iptables_ip);
}

void SystemMonitor::PushIptablesMsg(const std::string& worker_node, const std::string& time, const std::string& iptables_ip)
{
    Json::Value item;
    Json::Value value;
    Json::FastWriter w;
	item["node"] = worker_node;
	item["time"] = time;
    item["key"] = AKCS_MONITOR_ALARM_IPTABLES;    
    item["description"] = iptables_ip;//description 就是对应得value,再iptables告警下，就是对应得black_ip
    item["hostname"] = hostname_;
    item["ip"] = out_ip_;
    item["black_ip"] = iptables_ip;//兼容旧得，后面monitor更新后可以去掉
	std::string iptables_msg_json = w.write(item);
	AK_LOG_INFO << "push iptables_msg_json iptables msg:" << iptables_msg_json;
    //投递给nsq
    if(!is_init_)
    {
		AK_LOG_WARN << "SystemMonitor has not been init";
		return;
	}
    if (!client_->Publish(add_iptables_tiopic, iptables_msg_json))
    {
		AK_LOG_WARN  << "push monitor iptables msg to nsq failed:" << iptables_msg_json;
    }
}


