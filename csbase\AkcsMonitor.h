// Author: chency
// Date: 2019-08-08
// File: AkcsMonitor.h
// Des: 系统的内部业务监控告警模块

#ifndef __AKCS_BASE_MONITOR_H__
#define __AKCS_BASE_MONITOR_H__

#include "Singleton.h"
#include <evpp/evnsq/producer.h>


static const std::string AKCS_MONITOR_ALARM_CONFIG_WRITE_FAILED = "alarm.csadapt.config.write_failed";
static const std::string AKCS_MONITOR_ALARM_UPDATE_DEVICE_MD5_FAILED = "alarm.csadapt.db.update_device_md5";
static const std::string AKCS_MONITOR_ALARM_IPTABLES= "alarm.iptables.blackip";
static const std::string AKCS_MONITOR_ALARM_DB_CONNECT= "alarm.db.connect_err";
static const std::string AKCS_MONITOR_ALARM_UPDATE_DEV_CONNECT_STATUS= "alarm.csmain.update_dev.status";
static const std::string AKCS_MONITOR_ALARM_UPDATE_APP_PUSH_TOKEN= "alarm.csmain.update_app.push_token";
static const std::string AKCS_MONITOR_ALARM_CONSUME_KAFKA_CSADAPT= "alarm.kafka.consume.csadapt";
static const std::string AKCS_MONITOR_ALARM_CONSUME_KAFKA_SIP= "alarm.kafka.consume.sip";
static const std::string AKCS_MONITOR_ALARM_PRODUCE_KAFKA_SIP= "alarm.kafka.produce.sip";
static const std::string AKCS_MONITOR_ALARM_PRODUCE_KAFKA_EMAIL= "alarm.kafka.produce.email";
static const std::string AKCS_MONITOR_ALARM_PRODUCE_KAFKA_CSLINKER= "alarm.kafka.produce.cslinker";
static const std::string AKCS_MONITOR_ALARM_NSQD_PUBLISH_CSMAIN = "alarm.nsq.publish.csmain";
static const std::string AKCS_MONITOR_ALARM_NSQD_PUBLISH_CSADAPT = "alarm.nsq.publish.csadapt";
static const std::string AKCS_MONITOR_ALARM_NSQD_PUBLISH_CSROUTE = "alarm.nsq.publish.csroute";
static const std::string AKCS_MONITOR_ALARM_GRPC_TIMEOUT_CSSESSION= "alarm.grpc.timeout.cssession";
static const std::string AKCS_MONITOR_ALARM_GRPC_TIMEOUT_CSMAIN= "alarm.grpc.timeout.csmain";
static const std::string AKCS_MONITOR_ALARM_GRPC_TIMEOUT_CSPBXRPC= "alarm.grpc.timeout.cspbxrpc";
static const std::string AKCS_MONITOR_ALARM_QUEUE_OVERFLOW_CSMAIN_NOTIFY= "alarm.queue.overflow.csmain_notify";
static const std::string AKCS_MONITOR_ALARM_QUEUE_OVERFLOW_CSROUTE_MQ= "alarm.queue.overflow.csroute_mq";
static const std::string AKCS_MONITOR_ALARM_CONNECT_OVERFLOW_FTP= "alarm.connect.overflow.ftp";
static const std::string AKCS_MONITOR_ALARM_CONNECT_OVERFLOW_CSMAIN= "alarm.connect.overflow.csmain";
static const std::string AKCS_MONITOR_ALARM_CONNECT_OVERFLOW_RTSP= "alarm.connect.overflow.rtsp";
static const std::string AKCS_MONITOR_ALARM_CONNECT_OVERFLOW_ONE_DEVICE_RTSP= "alarm.connect.overflow.one_dev_rtsp";
static const std::string AKCS_MONITOR_ALARM_FREESWITCH = "alarm.freeswitch";
static const std::string AKCS_MONITOR_ALARM_MODULE_CONNECT_ERROR = "alarm.module.connect.error";
//错误数据告警。可以马上介入处理的异常告警。不可介入的不能用，不然邮件有可能会爆满。
static const std::string AKCS_MONITOR_ALARM_DATA_ERROR = "alarm.csadapt.can-handle.data.error";
static const std::string AKCS_MONITOR_ALARM_API_RATE_LIMIT = "alarm.csgate.api.ratelimit";
static const std::string AKCS_MONITOR_ALARM_CONN_RATE_LIMIT = "alarm.csmain.conn.ratelimit";
static const std::string AKCS_MONITOR_ALARM_HTTP_REQUEST = "alarm.http.request_err";
static const std::string AKCS_MONITOR_ALARM_REPEATED_REFRESH_USERINFO_ERROR = "alarm.csadapt.repeated-refresh-userinfo.error";
static const std::string AKCS_MONITOR_ALARM_REPEATED_REFRESH_IPCHANGE_ERROR = "alarm.csadapt.repeated-ipchange.error";
static const std::string AKCS_MONITOR_ALARM_MAC_DUPLICATE_CSMAIN = "alarm.csmain.mac-duplicate.error";
static const std::string AKCS_MONITOR_ALARM_QUEUE_OVERFLOW_ZIPKIN_NOTIFY= "alarm.queue.overflow.zipkin";


//added by chenyc,2020-03-25,该模块直接对接monitor节点监控告警模块,相关消息交互需要双方制定
class SystemMonitor{
public:
    // 实现单例
    friend class AKCS::Singleton<SystemMonitor>;
    // 用法:AKCS::Singleton<SystemMonitor>::instance().xx()
	virtual ~SystemMonitor() {is_init_ = false;}
public:
	void Init(evnsq::Producer *client);
    //worker_node:业务工作节点,跟etcd注册中的值类似，eg:csmain
    //description:具体的告警内容
    int TriggerMonitorAlarm(const std::string& worker_node, const std::string& description, const std::string&alarm_key);
	void TriggerMonitorIptables(const std::string& worker_node, const std::string& iptables_ip);

private:
    //投递告警消息
    virtual int PushAlarmMsg(const std::string& worker_node, const std::string& description, const std::string& time, const std::string&alarm_key);
	virtual void PushIptablesMsg(const std::string& worker_node, const std::string& time, const std::string& iptables_ip);
private:
	bool is_init_;
	evnsq::Producer *client_;
	std::string hostname_;
	std::string out_ip_;
};

#endif /* __AKCS_BASE_MONITOR_H__ */
