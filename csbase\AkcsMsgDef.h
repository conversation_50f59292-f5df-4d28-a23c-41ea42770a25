#ifndef __AKCS_BASE_MSG_DEF_H__
#define __AKCS_BASE_MSG_DEF_H__

#include <stdint.h>  //uint32_t
#include <vector>
#include <string>
#include <map>
#include <list>
#include "AkLogging.h"

#define CHECK_PB_PARSE_MSG(ret) { \
    if (ret == false) \
    {\
        AK_LOG_WARN << "parse pb msg failed.";\
        return;\
    }\
}

#define CHECK_PB_PARSE_MSG_ERR_RET(ret, err_ret) { \
    if (ret == false) \
    {\
        AK_LOG_WARN << "parse pb msg failed.";\
        return err_ret;\
    }\
}

/* php <-->csadapt的消息枚举 */
#define MSG_P2A             0x00400000

enum
{

    MSG_P2A_REBOOT_TO_DEVICE = MSG_P2A + 1,

    /** 当界面上请求远程配置设备时通知csadapt,csadapt会将消息ID修改并透传给csmain, PHP <-->csadapt
     */
    MSG_P2A_CONFIGURE_TO_DEVICE = MSG_P2A + 2,

    /** 当界面上请求远程设备的配置信息时通知csadapt,csadapt会将消息ID修改并透传给csmain, PHP <-->csadapt
     */
    MSG_P2A_CONFIGURE_FROM_DEVICE = MSG_P2A + 3,

    /**界面或者app请求重置密码时,发送邮件通知用户
     */
    MSG_P2A_RESET_PASSWD = MSG_P2A + 4,

    //added by chenyc,2017-09-01
    /**个人终端用户,界面添加/删除个人终端用户主账号,或者个人终端管理员往主账号添加设备,或者修改key所能打开的设备时触发
     */
    MSG_P2A_PERSONNAL_UPDATE_USER = MSG_P2A + 5,

    //同一联动系统中设备的location或者app用户的昵称发生改变时,csadapt透传给csmain,由csmain去通知设备进行更新,app不需要通知,app是实时查询的
    MSG_P2A_PERSONNAL_UPDATE_NODE_DEV = MSG_P2A + 6,

    //同一联动系统中的用户在界面上或者app上处理告警的时候通知csmain去通知设备告警处理的结果
    MSG_P2A_PERSONNAL_ALARM_DEAL = MSG_P2A + 7,

    //个人终端管理员添加设备
    MSG_P2A_PERSONNAL_ADD_DEV = MSG_P2A + 8,

    //个人终端管理员修改设备
    MSG_P2A_PERSONNAL_MODIFY_DEV = MSG_P2A + 9,

    //个人终端管理员删除设备
    MSG_P2A_PERSONNAL_DEL_DEV = MSG_P2A + 10,

    //个人终端管理员删除个人终端账号
    MSG_P2A_PERSONNAL_DEL_UID = MSG_P2A + 11,

    //个人终端管理员删除个人终端账号
    MSG_P2A_PERSONNAL_DEL_PIC = MSG_P2A + 12,

    //个人终端管理员创建uid
    MSG_P2A_PERSONNAL_CREATE_UID = MSG_P2A + 13,

    //个人终端管理员创建uid
    MSG_P2A_PERSONNAL_CHANGE_PWD = MSG_P2A + 14,

    //个人终端管理员需要发送文本消息给联动系统
    MSG_P2A_PERSONNAL_NEW_TEXT_MESSAGE = MSG_P2A + 15,

    /////////////V4.0社区开始
    //社区更新设备操作
    MSG_P2A_COMMUNITY_UPDATE_NODE_DEV = MSG_P2A + 16,

    //社区更新用户
    MSG_P2A_COMMUNITY_UPDATE_USER = MSG_P2A + 17,

    //社区警告被处理通知
    MSG_P2A_COMMUNITY_ALARM_DEAL = MSG_P2A + 18,

    //社区设备添加
    MSG_P2A_COMMUNITY_ADD_DEV = MSG_P2A + 19,

    //社区设备修改
    MSG_P2A_COMMUNITY_MODIFY_DEV = MSG_P2A + 20,

    //社区设备删除
    MSG_P2A_COMMUNITY_DEL_DEV = MSG_P2A + 21,

    //社区删除账号
    MSG_P2A_COMMUNITY_DEL_UID = MSG_P2A + 22,

    //社区删除图片
    MSG_P2A_COMMUNITY_DEL_PIC = MSG_P2A + 23,

    //社区创建账号
    MSG_P2A_COMMUNITY_CREATE_UID = MSG_P2A + 24,

    //社区改变密码
    MSG_P2A_COMMUNITY_CHANGE_PWD = MSG_P2A + 25,

    //社区管理员发送消息
    MSG_P2A_COMMUNITY_NEW_TEXT_MESSAGE = MSG_P2A + 26,

    //社区修改主账号通知 只有修改主账号名称和房间号码
    MSG_P2A_COMMUNITY_MODIFY_MASTER_USER = MSG_P2A + 27,

    // 注册账号时候要求发送验证码给某个邮箱
    MSG_P2A_PERSONNAL_SEND_CKECK_CODE_TO_EMAIL = MSG_P2A + 28,
    //绑定完设备后通知设备清空设备码
    MSG_P2A_NOTIFY_DEV_CLEAN_DEV_CODE = MSG_P2A + 29,
    // 付费后 由过期到未过期的通知
    MSG_P2A_NOTIFY_DEV_NOT_EXPIRE = MSG_P2A + 30,
    //个人社区 删除虚拟主账号
    MSG_P2A_NOTIFY_DEL_PER_PUBLIC_DEV_VIRTUAL_ACCOUNT = MSG_P2A + 31,

    // 用户实时增加视频存储计划
    MSG_P2A_NOTIFY_ADD_VIDEO_SCHED = MSG_P2A + 32,
    // 用户实时增加视频存储计划
    MSG_P2A_NOTIFY_DEL_VIDEO_SCHED = MSG_P2A + 33,
    // 用户实时删除视频存储
    MSG_P2A_NOTIFY_DEL_VIDEO_STORAGE = MSG_P2A + 34,
    // 社区管理员修改公共设备key
    MSG_P2A_NOTIFY_COMMUNITY_PUBLIC_KEY_CHANGE = MSG_P2A + 35,

    // 账号是否激活
    MSG_P2A_NOTIFY_ACOUNT_ACTIVE = MSG_P2A + 36,
    // 分享tempkey
    MSG_P2A_NOTIFY_SHARE_TEMPKEY = MSG_P2A + 37,
    // 远程开门
    MSG_P2A_NOTIFY_REMOTE_OPENDOOR = MSG_P2A + 38,
    // 创建物业
    MSG_P2A_NOTIFY_CREATE_PROPERTY_WORK = MSG_P2A + 39,
    // 更新小区所有公共设备
    MSG_P2A_NOTIFY_UPDATE_ALL_PUB_DEV = MSG_P2A + 40,

    // 设备单独更新配置
    MSG_P2A_NOTIFY_DEV_CONFIG_UPDATE = MSG_P2A + 41,
    //社区更新APT+PIN的方式
    MSG_P2A_NOTIFY_COMMUNITY_APT_PIN_CHANGE = MSG_P2A + 42,
    //远程开门 开的是Security Relay
    MSG_P2A_NOTIFY_REMOTE_OPEN_SECURITY_RELAY = MSG_P2A + 43,
    //PM一键开关门
    MSG_P2A_NOTIFY_PM_EMERGENCY_DOOR_CONTROL = MSG_P2A + 44,
    // 家居设备通过web远程开门
    MSG_P2A_DEVICE_NOTIFY_REMOTE_OPENDOOR = MSG_P2A + 45,
    // 家居设备通过web对设备发起监控截图
    MSG_P2A_DEVICE_NOTIFY_REQUEST_CAPTURE = MSG_P2A + 46,

    //app 已经过期
    MSG_P2A_APP_EXPIRE = MSG_P2A + 100,
    //app 即将过期
    MSG_P2A_APP_WILLBE_EXPIRE = MSG_P2A + 101,
    //主账号试用期即将到期
    MSG_P2A_FREE_TRIAL_EXPIRE = MSG_P2A + 102,
    //enduser续费成功
    MSG_P2A_NOTIFY_ENDUSER_RENEWSERVER = MSG_P2A + 103,
    //通知PM,enduser账号即将过期
    MSG_P2A_NOTIFY_PM_ACCOUNT_WILL_EXPIRE = MSG_P2A + 104,
    //alexa登陆
    MSG_P2A_NOTIFY_ALEXA_LOGIN = MSG_P2A + 105,
    //alexa设置arming
    MSG_P2A_NOTIFY_ALEXA_SET_ARMING = MSG_P2A + 106,
    //落地过期通知
    MSG_P2A_PHONE_EXPIRE = MSG_P2A + 107,
    //落地即将过期通知
    MSG_P2A_PHONE_WILL_EXPIRE = MSG_P2A + 108,
    //账号过期通知付费Installer
    MSG_P2A_NOTIFY_INSTALLER_APP_WILL_EXPIRE = MSG_P2A + 109,
    //落地过期通知付费Installer
    MSG_P2A_NOTIFY_INSTALLER_PHONE_WILL_EXPIRE = MSG_P2A + 110,
    //社区月租从原先的收费改成永久免费,后台去刷新社区下账号的过期时间
    MSG_P2A_NOTIFY_UPDATE_COMM_MONTHLY_FEE = MSG_P2A + 111,
    //开启远程设备网页访问
    MSG_P2A_NOTIFY_CREATE_REMOTE_DEV_CONTORL = MSG_P2A + 112,
    //已过期通知
    MSG_P2A_NOTIFY_INSTALLER_APP_EXPIRE = MSG_P2A + 113,
    MSG_P2A_NOTIFY_INSTALLER_PHONE_EXPIRE = MSG_P2A + 114,
    //发送短信验证码
    MSG_P2A_SEND_SMS_CODE = MSG_P2A + 115,
    //PM导出日志
    MSG_P2A_PM_EXPORT_LOG = MSG_P2A + 116,
    //app 用户反馈发送邮件
    MSG_P2A_APP_FEEDBACK = MSG_P2A + 117,
    //AUTOP单次下发
    MSG_P2A_UPDATE_MAC_CONFIG = MSG_P2A + 118,
    //AUTOP批量下发 REGULAR
    MSG_P2A_REGULAR_AUTOP = MSG_P2A + 119,
    //AUTOP批量下发 ONCE
    MSG_P2A_ONCE_AUTOP = MSG_P2A + 120,
    //删除app账户
    MSG_P2A_DELETE_APP_ACCOUNT = MSG_P2A + 121,
    
    //发消息通知AKCS让设备去下载图片
    MSG_P2A_NOTIFY_FACESERVER_PIC_DOWNLOAD = MSG_P2A + 201,
    //发消息通知AKCS让设备修改人脸数据
    MSG_P2A_NOTIFY_FACESERVER_PIC_MODIFY = MSG_P2A + 202,
    //发消息通知AKCS让设备删除人脸数据
    MSG_P2A_NOTIFY_FACESERVER_PIC_DELETE = MSG_P2A + 203,
    //发消息通知AKCS让多台设备去下载图片
    MSG_P2A_NOTIFY_FACESERVER_PIC_BATCH_DOWNLOAD = MSG_P2A + 204,
    //发消息通知AKCS让多台设备修改人脸数据
    MSG_P2A_NOTIFY_FACESERVER_PIC_BATCH_MODIFY = MSG_P2A + 205,
    //发消息通知AKCS让多台设备删除人脸数据
    MSG_P2A_NOTIFY_FACESERVER_PIC_BATCH_DELETE = MSG_P2A + 206,
    //权限组更新
    MSG_P2A_NOTIFY_ACCESS_GROUP_MODIFY = MSG_P2A + 207,
    //权限组用户更新
    MSG_P2A_NOTIFY_ACCESS_GROUP_PER_MODIFY = MSG_P2A + 208,
    //新社区人员更新
    MSG_P2A_NOTIFY_COMMUNITY_PERSONAL_MODIFY = MSG_P2A + 209,
    //新社区用户
    MSG_P2A_NOTIFY_COMMUNITY_ACCOUNT_MODIFY = MSG_P2A + 210, 
    
    //导入用户数据通知
    MSG_P2A_NOTIFY_COMMUNITY_IMPORT_ACCOUNT_DATAS= MSG_P2A + 211, 

    //pm激活邮件通知
    MSG_P2A_NOTIFY_PM_ACOUNT_ACTIVE = MSG_P2A + 212,
    
    //新版本：个人终端管理员下更新配置
    MSG_P2A_NOTIFY_PERSONAL_MESSAGE = MSG_P2A + 1000,
    //新版本：社区下更新配置
    MSG_P2A_NOTIFY_COMMUNITY_MESSAGE = MSG_P2A + 1001,

    //高级功能过期通知付费PM
    MSG_P2A_NOTIFY_PM_FEATURE_WILL_EXPIRE = MSG_P2A + 1002,
    //高级功能过期通知付费Installer
    MSG_P2A_NOTIFY_INSTALLER_FEATURE_WILL_EXPIRE = MSG_P2A + 1003,
    //数据分析
    MSG_P2A_NOTIFY_DATA_ANALYSIS_NOTIFY = MSG_P2A + 1004,

    //PM APP 账户创建
    MSG_P2A_PM_CREATE_UID = MSG_P2A + 1005,

    //PM APP 账户即将过期
    MSG_P2A_NOTIFY_PM_APP_ACCOUNT_WILL_EXPIRE = MSG_P2A + 1006,

    //PM APP 账户已过期
    MSG_P2A_NOTIFY_PM_APP_ACCOUNT_EXPIRE = MSG_P2A + 1007,

    //三方锁开关锁
    MSG_P2A_NOTIFY_OPERATE_THIRD_PARTYA_LOCK = MSG_P2A + 1008,

    //远程重置设备
    MSG_P2A_RESET_TO_DEVICE = MSG_P2A + 1009,

    //社区用户添加新站点
    MSG_P2A_COMMUNITY_ADD_NEW_SITE = MSG_P2A + 1010,

    //单住户添加新站点
    MSG_P2A_PERSONAL_ADD_NEW_SITE = MSG_P2A + 1011,

    //pm web link新站点
    MSG_P2A_PM_LINK_NEW_SITES = MSG_P2A + 1012,

    //pm app 添加新站点
    MSG_P2A_PM_APP_ADD_NEW_SITE = MSG_P2A + 1013,

    //pm web创建pm
    MSG_P2A_PM_WEB_CREATE_UID = MSG_P2A + 1014,

    //pm web重置密码
    MSG_P2A_PM_WEB_CHANGE_PWD = MSG_P2A + 1015,

    //app更改邮箱/手机号发送验证码
    MSG_P2A_SEND_COMMON_SMS_CODE = MSG_P2A + 1016,

    MSG_P2A_SEND_COMMON_EMAIL_CODE = MSG_P2A + 1017,

    // 开启/关闭抓包
    MSG_P2A_PCAP_CAPTURE_CONTROL = MSG_P2A + 1018,

    // 发送邮件
    MSG_P2A_SEND_EMAIL_NOTIFY = MSG_P2A + 1019,

    // 发送过期邮件
    MSG_P2A_SEND_EMAIL_CRONTAB_NOTIFY = MSG_P2A + 1020,

    //发送Message
    MSG_P2A_SEND_MESSAGE_NOTIFY = MSG_P2A + 1021,

    //发送过期Message
    MSG_P2A_SEND_MESSAGE_CRONTAB_NOTIFY = MSG_P2A + 1022,
    
    //用户权限组更新通知
    MSG_P2A_ACCOUNT_ACCESS_UPDATE_NOTIFY = MSG_P2A + 1023,

    // 智能锁配置更新推送
    MSG_P2A_NOTIFY_SMARTLOCK_UPDATE = MSG_P2A + 1024,

    /*以下是office*/
    //office更新配置
    MSG_P2A_NOTIFY_OFFICE_INFO_UPDATE = MSG_P2A + 2000,

    //office创建用户
    MSG_P2A_NOTIFY_OFFICE_CREATE_UID = MSG_P2A + 2001,

    //office用户账户续费
    MSG_P2A_NOTIFY_OFFICE_ACCOUNT_RENEW = MSG_P2A + 2002,

    //office账户过期
    MSG_P2A_NOTIFY_OFFICE_PM_ACCOUNT_WILL_EXPIRE = MSG_P2A + 2003,

    //office账户重置密码
    MSG_P2A_NOTIFY_OFFICE_ACCOUNT_RESETPWD = MSG_P2A + 2004,

    //office账户改密码
    MSG_P2A_NOTIFY_OFFICE_ACCOUNT_CHANGEPWD = MSG_P2A + 2005,
    //权限组修改
    MSG_P2A_NOTIFY_OFFICE_ACCESS_GROUP_MODIFY = MSG_P2A + 2006,
    //权限组相关：用户修改
    MSG_P2A_NOTIFY_OFFICE_COMMUNITY_ACCOUNT_MODIFY = MSG_P2A + 2007,
    //导入用户
    MSG_P2A_NOTIFY_OFFICE_COMMUNITY_IMPORT_ACCOUNT_DATAS = MSG_P2A + 2008,
    //office账户过期
    MSG_P2A_NOTIFY_OFFICE_PM_ACCOUNT_EXPIRE = MSG_P2A + 2009,
    //office高级功能过期通知pm
    MSG_P2A_NOTIFY_OFFICE_PM_FEATURE_WILL_EXPIRE = MSG_P2A + 2010,
    //office高级功能过期通知ins/dis
    MSG_P2A_NOTIFY_OFFICE_INSTALLER_FEATURE_WILL_EXPIRE = MSG_P2A + 2011,
    //office高级功能过期通知pm
    MSG_P2A_NOTIFY_OFFICE_PM_FEATURE_EXPIRE = MSG_P2A + 2012,
    //office高级功能过期通知ins/dis
    MSG_P2A_NOTIFY_OFFICE_INSTALLER_FEATURE_EXPIRE = MSG_P2A + 2013,
    //office人员修改
    MSG_P2A_NOTIFY_OFFICE_PERSONAL_MODIFY = MSG_P2A + 2014,
    //办公添加新站点
    MSG_P2A_OFFICE_ADD_NEW_SITE = MSG_P2A + 2015,
    //kit方案下的账号注销
    MSG_P2A_KIT_ACCOUNT_LOG_OFF = MSG_P2A + 2016,
    //开启sip抓包
    MSG_P2A_SIP_PCAP_CAPTURE_CONTROL = MSG_P2A + 2017,
    //openapi socket健康探测
    MSG_C2S_OPENAPI_SOCKET_HEALTH_CHECK = MSG_P2A + 2018,
    //旧办公警告被处理通知
    MSG_P2A_OFFICE_ALARM_DEAL           = MSG_P2A + 2019,
    //新办公警告被处理通知
    MSG_P2A_NEWOFFICE_ALARM_DEAL        = MSG_P2A + 2020,
};


/* csmain <-->csadapt*/
#define MSG_S2C             0x00100000
#define MSG_C2S             0x00110000
//csroute->csadapt
enum
{
    MSG_S2C_ALARM_RECEIVED = MSG_S2C + 1,
    MSG_S2C_TEXT_MESSAGE_RECEIVED,
    MSG_S2C_UPDATE_DEV_CONFIGURE,
    MSG_S2C_DEV_CONFIG_RECEIVED,
    // 通知重写文件
    MSG_S2C_DEV_CONFIG_REWRITE,
    // 通知用户重写文件
    MSG_S2C_ACCOUNT_CONFIG_REWRITE,
    //设备上报访客信息
    MSG_S2C_DEV_REPORT_VISITOR,
    //设备请求获取详细信息
    MSG_S2C_DEV_REQ_USER_INFO,
    //数据分析解析完后投入延时队列的消息id,TODO：暂时先放这里
    MSG_S2C_DATAANALYSIS_COMM_FILE_UPDATE,
    //数据分析解析完后投入延时队列的消息id,TODO：暂时先放这里
    MSG_S2C_DATAANALYSIS_COMM_ACCESS_FILE_UPDATE,    
};

enum
{
    CSMAIN_UPDATE_CONFIG_IP_CHANGE = 1,
    CSMAIN_UPDATE_CONFIG_UPGRADE = 2,
    CSMAIN_UPDATE_CONFIG_MAINTANCE = 3,//运维更新设备配置
    CSMAIN_UPDATE_CONFIG_RF_CHANGE = 4,//nfc/ble更新
};


//csadapt->csroute
enum
{
    MSG_C2S_SEND_REPORT_STATUS = MSG_C2S + 1,
    MSG_C2S_UPDATE_ADDRESS,
    MSG_C2S_REBOOT_DEVICE,
    MSG_C2S_ADD_DEV,
    MSG_C2S_UPDATE_TO_DEVICE,
    MSG_C2S_CONFIGURE_TO_DEVICE,
    MSG_C2S_CONFIGURE_FROM_DEVICE,
    MSG_C2S_NOTIFY_APP_CONF_CHANGE,
    MSG_C2S_NOTIFY_APP_BIND,
   //post when client add/delete/modify privatekey/rfidkey
    MSG_C2S_NOTIFY_KEY_CHANGE,
   // post when client add/delete/modify privatekey/rfidkey
    MSG_C2S_NOTIFY_UPDATE_NODE,
   //个人终端用户在界面上\app处理告警时，csadapt通知csmain去通知设备
    MSG_C2S_NOTIFY_PERSONAL_ALARM_DEAL,
    // post when client need personal device report status
    MSG_C2S_PER_SEND_REPORT_STATUS,
    // 界面上删除个人终端用户设备时
    MSG_C2S_PER_SEND_DEL_DEV,
    //界面上删除个人终端UID
    MSG_C2S_PER_SEND_DEL_UID,
    //界面上管理员发送文本消息给设备\app
    MSG_C2S_PER_SEND_TEXT_MSG,
    //界面上管理员添加用户,csadapt通知csmain,发送相关信息到用户邮箱
    MSG_C2S_PER_SEND_CREATE_UID_MAIL,
    //界面上用户忘记密码,csadapt通知csmain,发送相关信息到用户邮箱
    MSG_C2S_PER_SEND_RESET_PWD_MAIL,
    //界面上用户修改密码,csadapt通知csmain,发送相关信息到用户邮箱
    MSG_C2S_PER_SEND_CHANGE_PWD_MAIL,
    //社区更新设备信息
    MSG_C2S_NOTIFY_UPDATE_COMMUNITY_NODE,
    //社区alarm信息处理
    MSG_C2S_NOTIFY_COMMUNITY_ALARM_DEAL,
    //更新配置文件
    MSG_C2S_NOTIFY_CONFIG_FILE_CHANGE,
    //设备或app过期
    MSG_C2S_DEV_APP_EXPIRE,
    //设备app即将过期
    MSG_C2S_DEV_APP_WILL_BE_EXPIRE,
    //试用期即将过期
    MSG_C2S_FREETRIAL_WILL_BE_EXPIRE,
    //个人注册账号发送验证码到邮箱
    MSG_C2S_PER_SEND_CHECK_CODE_MAIL,
    //设备变成未过期
    MSG_C2S_DEV_NOT_EXPIRE,
    //清空设备码
    MSG_C2S_DEV_CLEAN_DEV_CODE,    
    //实时增加视频存储计划
    MSG_C2S_ADD_VIDEO_STORAGE_SCHED,
    //实时删除视频存储计划
    MSG_C2S_DEL_VIDEO_STORAGE_SCHED,
    //实时删除视频存储片段
    MSG_C2S_DEL_VIDEO_STORAGE,
    //设备改变
    MSG_C2S_DEV_CHANGE,
    //是否激活账号 邮件通知
    MSG_C2S_ACCOUNT_ACTIVE,
    //PM APP激活 邮件通知
    MSG_C2S_PM_ACCOUNT_ACTIVE,
    //分享tmpkey
    MSG_C2S_SHARE_TMPKEY,
    //远程开门
    MSG_C2S_REMOTE_OPENDOOR,
    //远程开SECURITY RELAY
    MSG_C2S_REMOTE_OPEN_SECURITY_RELAY,
    //创建物业
    MSG_C2S_CREATE_PROPERTY_WORK,
    //续费成功
    MSG_C2S_RENEW_SERVER,
    //通知PM社区下账号即将过期
    MSG_C2S_PM_WILL_EXPIRE,
    //PM一键开关门
    MSG_C2S_PM_EMERGENCY_DOOR_CONTROL,

    //alexa账号登陆
    MSG_C2S_ALEXA_LOGIN_MSG,
    //alexa设置布撤防
    MSG_C2S_ALEXA_SET_ARMING_MSG,  
    //落地过期通知终端用户
    MSG_C2S_PHONE_EXPIRE,
    //落地即将过期
    MSG_C2S_PHONE_WILL_EXPIRE,
    //落地即将过期通知付费Installer
    MSG_C2S_INSTALLER_PHONE_WILL_EXPIRE,
    //账号即将过期通知付费Installer
    MSG_C2S_INSTALLER_APP_WILL_EXPIRE,
    //创建远程设备访问
    MSG_C2S_CREATE_REMOTE_DEV_CONTORL,    
    //发消息通知AKCS让设备去下载图片
    MSG_C2S_NOTIFY_FACESERVER_PIC_DOWNLOAD,
    //发消息通知AKCS让设备修改人脸数据
    MSG_C2S_NOTIFY_FACESERVER_PIC_MODIFY,
    //发消息通知AKCS让设备删除人脸数据
    MSG_C2S_NOTIFY_FACESERVER_PIC_DELETE,
    //发消息通知AKCS让设备去下载图片
    MSG_C2S_NOTIFY_FACESERVER_PIC_BATCH_DOWNLOAD,
    //发消息通知AKCS让设备修改人脸数据
    MSG_C2S_NOTIFY_FACESERVER_PIC_BATCH_MODIFY,
    //发消息通知AKCS让设备删除人脸数据
    MSG_C2S_NOTIFY_FACESERVER_PIC_BATCH_DELETE,
    //发送短信验证码
    MSG_C2S_SEND_SMS_CODE,
    //导出日志
    MSG_C2S_PM_EXPORT_LOG,
    //刷新缓存
    MSG_C2S_REFRESH_CONN_CACHE,
    //PM一键打开所有公共设备的门
    MSG_C2S_KEEP_OPEN_RELAY,
    //PM一键关闭所有公共设备的门
    MSG_C2S_KEEP_CLOSE_RELAY,
    //通知指定设备获取对应类型的文件
    MSG_C2S_NOTIFY_FILE_CHANGE,
    //高级功能即将过期通知付费PM
    MSG_C2S_PM_FEATURE_WILL_EXPIRE,
    //落地即将过期通知付费Installer
    MSG_C2S_INSTALLER_FEATURE_WILL_EXPIRE,
    //删除APP账号,SMS
    MSG_C2S_SEND_SMS_DELETE_APP_ACCOUNT,
    //删除APP账号,EMAIL
    MSG_C2S_SEND_MAIL_DELETE_APP_ACCOUNT,
    //创建从账户短信
    MSG_C2S_SEND_SMS_CREATE_UID,
    //重置从账户密码短信
    MSG_C2S_SEND_SMS_CHANGE_PWD,
    //pm app创建账户
    MSG_C2S_PM_APP_SEND_CREATE_UID_MAIL,
    //pm app账户即将过期
    MSG_C2S_PM_APP_ACCOUNT_WILL_EXPIRE,
    //pm app账户已过期
    MSG_C2S_PM_APP_ACCOUNT_EXPIRE,
    //pm app续费
    MSG_C2S_PM_RENEW_SERVER,
    //重置设备
    MSG_C2S_RESET_DEVICE,
    //用户添加新站点
    MSG_C2S_SEND_USER_ADD_NEWSITE,
    //pm web link新站点
    MSG_C2S_SEND_PM_WEB_LINK_NEWSITES,
    //pm web 创建pm
    MSG_C2S_PM_WEB_CREATE_UID_MAIL,
    //pm web 重置密码
    MSG_C2S_PM_WEB_CHANGE_UID_MAIL,
    //发送验证码到邮箱
    MSG_C2S_SEND_CODE_TO_EMAIL,
    //发送验证码到手机
    MSG_C2S_SEND_CODE_TO_MOBILE,
    //多套房主站点变更
    MSG_C2S_CHANGE_MAIN_SITE,
    //rtsp pcap抓包
    MSG_C2S_PCAP_CAPTURE_CONTROL,
    //发送邮件
    MSG_C2S_SEND_EMAIL_NOTIFY,
    //通知设备端删除开门记录
    MSG_C2S_REQUEST_DEV_DEL_LOG,
    //通知app刷新userconf
    MSG_C2S_REFRESH_APP_CONF,
    
    //以下为office
    //office创建邮件
    MSG_C2S_OFFICE_SEND_CREATE_UID_MAIL,
    MSG_C2S_OFFICE_SEND_ACCOUNT_RENEW_MAIL,
    MSG_C2S_OFFICE_SEND_PM_ACCOUNT_WILL_EXPIRE_MAIL,
    MSG_C2S_OFFICE_SEND_RESET_PWD_MAIL,
    MSG_C2S_OFFICE_SEND_CHANGE_PWD_MAIL,
    MSG_C2S_OFFICE_NOTIFY_CONFIG_FILE_CHANGE,
    MSG_C2S_OFFICE_SEND_PM_ACCOUNT_EXPIRE_MAIL,
    MSG_C2S_OFFICE_SEND_PM_FEATURE_WILL_EXPIRE_MAIL,
    MSG_C2S_OFFICE_SEND_INSTALLER_FEATURE_WILL_EXPIRE_MAIL,
    MSG_C2S_OFFICE_SEND_PM_FEATURE_EXPIRE_MAIL,
    MSG_C2S_OFFICE_SEND_INSTALLER_FEATURE_EXPIRE_MAIL,
    MSG_C2S_OFFICE_SEND_USER_ADD_NEWSITE,

    //sip pcap抓包
    MSG_C2S_SIP_PCAP_CAPTURE_CONTROL,    

    /************ new office ************/
    //导出日志
    MSG_C2S_NEW_OFFICE_EXPORT_LOG,
    //通知设备是否是考勤机
    MSG_C2S_OFFICE_DEVICE_SEND_IS_ATTENDANCE,
    
};

enum SmsType
{
    SMS_LOGIN = 0,
    SMS_RESET = 1,
    SMS_DELIVERY = 2,
    SMS_FLOW_OUT_OF_LIMIT = 3,
    SMS_DELIVERY_BOX = 4,   //JTS客户
    SMS_DEL_APP_ACCOUNT = 5, //删除APP账户的验证码
    SMS_FAMILY_CREATE_UID = 6,
    SMS_FAMILY_DEL_APP_ACCOUNT = 7,
    SMS_COMMON_SEND_CODE = 8,
};

//added by chenyc,2022.01.19,增加事件过滤框架的枚举
enum EventFilter
{
    EF_TEST_REQ_USER_INFO_UPDATE = 0,
    EF_TEST_RESP_USER_INFO_UPDATE,

};

//注:对于没有响应的消息请求,如果请求消息处理错误再响应,否则暂时全部不响应
enum
{
    //logic srv向csroute注册服务id
    AKCS_MSG_L2R_REG_UID_REQ = 0x00000001,
    AKCS_MSG_L2R_REG_UID_RESP = 0x00001001,
    AKCS_MSG_R2L_PING_REQ = 0x00000002,
    AKCS_MSG_R2L_PING_RESP = 0x00001002,

    //rtsp csroute集群下顺序消息走长连接服务
    AKCS_MSG_L2R_START_RTSP_REQ = 0x00002001,
    AKCS_MSG_L2R_STOP_RTSP_REQ = 0x00002002,
    AKCS_MSG_L2R_KEEPALIVE_RTSP_REQ = 0x00002003,
};

//csmain向csroute发送mq消息
enum
{
    //csmain向csroute发送mq广播消息,route在广播消息时,不再通知广播源(即发出广播消息的csmain)
    AKCS_M2R_GROUP_COMM_ALARM_REQ = 0x00400001,
    AKCS_M2R_GROUP_COMM_ALARM_RESP = 0x00401001,
    AKCS_M2R_GROUP_COMM_ALARM_DEAL_REQ = 0x00400002,
    AKCS_M2R_GROUP_COMM_ALARM_DEAL_RESP = 0x00401002,   
    AKCS_M2R_GROUP_PER_ALARM_REQ = 0x00400003,
    AKCS_M2R_GROUP_PER_ALARM_RESP = 0x00401003,
    AKCS_M2R_GROUP_PER_ALARM_DEAL_REQ = 0x00400004,
    AKCS_M2R_GROUP_PER_ALARM_DEAL_RESP = 0x00401004,
    AKCS_M2R_GROUP_PER_MOTION_REQ = 0x00400005,
    AKCS_M2R_GROUP_PER_MOTION_RESP = 0x00401005, 
    AKCS_M2R_P2P_RTSP_CAPTURE_REQ = 0x00400006,
    AKCS_M2R_P2P_RTSP_CAPTURE_RESP = 0x00401006,
    AKCS_M2R_GROUP_MNG_TEXT_MSG_REQ = 0x00400007,
    AKCS_M2R_GROUP_MNG_TEXT_MSG_RESP = 0x00401007,
    AKCS_M2R_P2P_APP_GET_ARMING_MSG_REQ = 0x00400008,
    AKCS_M2R_P2P_APP_GET_ARMING_MSG_RESP = 0x00401008,  
    AKCS_M2R_P2P_VISITOR_AUTHORIZE_REQ = 0x00400009,
    AKCS_M2R_P2P_VISITOR_AUTHORIZE_RESP = 0x00401009,
    AKCS_M2R_P2P_FACE_DATA_FORWARD_REQ = 0x0040000a,
    AKCS_M2R_P2P_FACE_DATA_FORWARD_RESP = 0x0040100a,
    AKCS_M2R_P2P_NOTIFY_DEV_OFFLINE_REQ = 0x0040000b,
    AKCS_M2R_P2P_NOTIFY_DEV_OFFLINE_RESP = 0x0040100b,
    AKCS_M2R_P2P_OPEN_DOOR_REQ = 0x0040000c,
    AKCS_M2R_P2P_OPEN_DOOR_RESP = 0x0040100c,
    AKCS_M2R_P2P_SEND_DELIVERY_REQ = 0x0040000d,
    AKCS_M2R_P2P_SEND_DELIVERY_RESP = 0x0040100d,
    AKCS_M2R_P2P_SEND_TMPKEY_USED_REQ = 0x0040000e,
    AKCS_M2R_P2P_SEND_TMPKEY_USED_RESP = 0x0040100e,
    AKCS_M2R_P2P_SEND_REMIND_FLOW_OUT_OF_LIMIT = 0x0040000f,
    AKCS_M2R_P2P_CHANGE_RELAY_REQ = 0x00400010,
    AKCS_M2R_P2P_CHANGE_RELAY_RESP = 0x00401010,
    AKCS_M2R_GROUP_REPORT_RELAY_REQ = 0x00400011,
    AKCS_M2R_GROUP_REPORT_RELAY_RESP = 0x00401011,
    AKCS_M2R_P2P_SEND_VOICE_MSG = 0x00401012,
    AKCS_M2R_P2P_OPEN_DOOR_ACK = 0x00401013,
    AKCS_M2R_GROUP_PUSH_CSLINKER_COMMON_MSG = 0x00401014,
    AKCS_M2R_P2P_SEND_DELIVERY_MSG = 0x00401015,
    AKCS_M2R_P2P_INDOOR_RELAY_CONTROL_MSG = 0x00401016,
    AKCS_M2R_P2P_SEND_EMERGENCY_NOTIFY_MSG = 0x00401017,
    AKCS_M2R_EMERGENCY_DOOR_CONTROL = 0x00401018,
    AKCS_M2R_PUSH_WEB_COMMON_MSG = 0x00401019,
    AKCS_M2R_P2P_SEND_MOTION_NOTIFY_MSG = 0X00401020,
    AKCS_M2R_P2P_SEND_TEXT_MSG = 0x00401021,
    AKCS_M2R_P2P_STOP_VIDEO_RECORD_REQ = 0x00400022,
    AKCS_M2R_P2P_DEVICE_OPEN_DOOR_MSG = 0x00401023,
    AKCS_M2R_P2P_REQUEST_DEVICE_CAPTURE_MSG = 0x00401024,

    //office 0x0041
    AKCS_M2R_GROUP_OFFICE_ALARM_REQ = 0x00410001,
    AKCS_M2R_GROUP_OFFICE_ALARM_DEAL_REQ = 0x00410002,
    AKCS_M2R_GROUP_OFFICE_PER_MOTION_REQ = 0x00410005,
    AKCS_M2R_P2P_OFFICE_APP_GET_ARMING_MSG_REQ = 0x00410008,
    AKCS_M2R_P2P_OFFICE_APP_GET_ARMING_MSG_RESP = 0x00411008,
    AKCS_M2R_P2P_OFFICE_VISITOR_AUTHORIZE_REQ = 0x00410009,
    AKCS_M2R_P2P_OFFICE_RTSP_CAPTURE_REQ = 0x00410006,
    AKCS_M2R_GROUP_OFFICE_MNG_TEXT_MSG_REQ = 0x00410007,
    AKCS_M2R_P2P_OFFICE_OPEN_DOOR_REQ = 0x0041000c,
    AKCS_M2R_P2P_OFFICE_SEND_DELIVERY_REQ = 0x0041000d,
    AKCS_M2R_P2P_OFFICE_SEND_REMIND_FLOW_OUT_OF_LIMIT = 0x0041000f,
    AKCS_M2R_P2P_OFFICE_CHANGE_RELAY_REQ = 0x00410010,
    AKCS_M2R_GROUP_OFFICE_REPORT_RELAY_REQ = 0x00410011,
    AKCS_M2R_P2P_NOTIFY_OFFICE_DEV_OFFLINE_REQ = 0x00410012,
    AKCS_M2R_P2P_OFFICE_SEND_ALARM_NOTIFY_MSG = 0x00410013,
    AKCS_M2R_P2P_SEND_TEXT_NOTIFY_MSG = 0x00410014,
    AKCS_M2R_P2P_TEMPKEY_USED_NOTIFY_MSG = 0x00410015,
    AKCS_M2R_GROUP_ALARM_DEAL_NOTIFY_MSG = 0x00410016,
    AKCS_M2R_GROUP_ALARM_DEAL_REPLY_MSG  = 0x00410017,
    AKCS_M2R_GROUP_OFFICE_ALARM_DEAL_NOTIFY_MSG     = 0x00410018,
    AKCS_M2R_GROUP_NEWOFFICE_ALARM_DEAL_NOTIFY_MSG  = 0x00410019,
    AKCS_M2R_P2P_LOCKDOWN_DOOR_CONTROL = 0x00410020,
    AKCS_M2R_P2P_SEND_LOCKDOWN_NOTIFY_MSG = 0x00410021,
    AKCS_M2R_P2P_SEND_SL20_LOCK_EVENT_NOTIFY = 0x00410022,
    
    //业务p2p
    AKCS_BUSSNESS_P2P_MSG = 0x00420001,
};

//csvrtsp向csroute发送mq消息
enum
{
    AKCS_V2R_START_RTSP_REQ = 0x00500001,
    AKCS_V2R_START_RTSP_RESP = 0x00501001,
    AKCS_V2R_STOP_RTSP_REQ = 0x00500002,
    AKCS_V2R_STOP_RTSP_RESP = 0x00501002,
    AKCS_V2R_KEEPALIVE_RTSP_REQ = 0x00500003,
    AKCS_V2R_KEEPALIVE_RTSP_RESP = 0x00501003,
};

///route->csmain
enum
{
    //csadapt向csroute发送mq消息,csroute可能需要变更消息类型,并主动发送给csmain
    AKCS_R2M_DEL_DEV_REQ = 0x00600001,
    AKCS_R2M_DEL_UID_REQ = 0x00600002,
    AKCS_R2M_CLEAN_DEV_CODE_REQ = 0x00600003,

    AKCS_R2M_START_RTSP_REQ = 0x00600005, //route转发vrtspd对设备的监控
    AKCS_R2M_START_RTSP_RESP = 0x00601005,
    AKCS_R2M_STOP_RTSP_REQ = 0x00600006,
    AKCS_R2M_STOP_RTSP_RESP = 0x00601006,
    AKCS_R2M_KEEPALIVE_RTSP_REQ = 0x00600007,
    AKCS_R2M_KEEPALIVE_RTSP_RESP = 0x00601007,
    AKCS_R2M_UPGRADE_DEV_REQ = 0x00600008,
    AKCS_R2M_UPGRADE_DEV_RESP = 0x00601008,    
    AKCS_R2M_WEATHER_INFO_RESP = 0x00601009,
};

///route->csvrtspd
enum
{
    AKCS_R2V_START_RTSP_REQ = 0x00700001,//route转发vrtspd对设备的监控
    AKCS_R2V_START_RTSP_RESP = 0x00701001,
    AKCS_R2V_STOP_RTSP_REQ = 0x00700002,
    AKCS_R2V_STOP_RTSP_RESP = 0x00701002,
    AKCS_R2V_RTSP_CAPTURE_REQ = 0x00700003,
    AKCS_R2V_RTSP_CAPTURE_RESP = 0x00701003,
    AKCS_R2V_KEEPALIVE_RTSP_REQ = 0x00700004,
    AKCS_R2V_KEEPALIVE_RTSP_RESP = 0x00701004,
    AKCS_R2V_PCAP_CAPTURE_REQ = 0x00700005, // route转发vrtspd对设备的抓包
    AKCS_R2V_PCAP_CAPTURE_RESP = 0x00701005, // route转发vrtspd对设备的抓包
};

//csstorage向csroute发送mq消息
enum
{
    AKCS_S2R_P2P_OFFLINE_MSG_ACK_REQ = 0x00900001,//离线消息处理成功
    AKCS_S2R_P2P_VOICE_MSG_ACK_REQ = 0x00900002,//语音消息处理成功
};

enum
{
    AKCS_R2S_P2P_OFFLINE_MSG_ACK_REQ = 0x00a00001,//离线消息处理成功
    AKCS_R2S_P2P_VOICE_MSG_ACK_REQ = 0x00a00002,//语音消息处理成功
};


///csagent
enum
{
    AKCS_B2G_ADD_IPTABLES_REQ = 0x00800001,//业务方(B:bussiness)发送攻击者ip给各个节点的agent
    AKCS_B2G_DEL_IPTABLES_REQ = 0x00800002,//业务方发送攻击者ip给各个节点的agent
};

// cslinker->csroute
enum
{
    AKCS_L2R_WEATHER_INFO_RESP = 0x00b00001,  // cslinker返回天气信息
    AKCS_L2R_PACPORT_UNLOCK_RESP = 0x00b00002,//cslinker返回pacport快递校验结果
    AKCS_L2R_DEV_COMMON_ACK = 0x00b00003,     //cslinker返回ack给设备
};

// csroute->csoffice/csresid
enum
{
    AKCS_R2B_P2P_WEATHER_INFO_RESP                  = 0x00c00001, //后端业务处理天气消息
    AKCS_R2B_P2P_REFRESH_APP_USERCONF               = 0x00c00002, //后端业务处理刷新app userconf
    AKCS_R2B_P2P_PACPORT_UNLOCK_RESP                = 0x00c00003, //后端业务处理pacport解锁校验结果
    AKCS_R2B_P2P_DEV_COMMON_ACK                     = 0x00c00004, //后端业务处理ack给设备
    AKCS_R2B_P2P_REFRESH_DEVICE_IS_ATTENDANCE       = 0x00c00005, //后端业务发送刷新设备IsAttendance
    AKCS_M2R_P2P_REMOTE_OPENDOOR_MSG                = 0x00c00006, //后端业务处理远程开门

    // AKCS_R2B_P2P_FROM_DEVICE_OPENDOOR_REQ           = 0x00c00007, //后端业务处理室内机的开门请求
    // AKCS_R2B_P2P_OFFICE_FROM_DEVICE_OPEN_DOOR_REQ   = 0x00c00008, //后端业务处理室内机的开门请求
    // AKCS_R2B_P2P_REMOTE_OPENDOOR                    = 0x00c00005, //后端业务处理远程开门
    // AKCS_R2B_P2P_REMOTE_OPEN_SECURITY_RELAY         = 0x00c00007, //后端业务处理远程安全relay
};

// csroute->siphub
enum
{
    AKCS_R2S_P2P_SIP_PCAP_REQ = 0x00d00001, // sip抓包
};

// csroute->smartlock
enum
{
    AKCS_R2S_UPDATE_SMARTLOCK_CONFIGURATION_NOTIFY_REQ = 0x00e00001, // 推送锁更新配置信息
};

typedef enum 
{
    REG_DEV_SID = 0,
    QUERY_DEV_SID = 1,
    REG_UID_SID = 2,
    QUERY_UID_SID = 3,
    REM_UID_SID = 4,
    QUERY_UIDS_BY_SID_NODE = 5,
    QUERY_DEV_UUID_SID = 6,
    QUERY_ACCOUNT_UUID_SID = 7,
}SessionSrvType;


typedef enum 
{
    REPORT_ARMING_ACTION_TYPE_REBOOT = 0,
    REPORT_ARMING_ACTION_TYPE_GET,
    REPORT_ARMING_ACTION_TYPE_SETTED,
    REPORT_ARMING_ACTION_TYPE_SELF_SET,
    REPORT_ARMING_ACTION_TYPE_REPORT_SET,
    REPORT_ARMING_ACTION_TYPE_FORBID,
    REPORT_ARMING_ACTION_TYPE_DEV_SET = 99 //R20 设置arming

}ArmingRespType;

typedef enum 
{
    ARMING_HOME_SYNC_TYPE_OFF = 0,
    ARMING_HOME_SYNC_TYPE_ON,
    ARMING_HOME_SYNC_TYPE_NOTIFY_OFF_CONFIG,
    ARMING_HOME_SYNC_TYPE_NOTIFY_ON_CONFIG
}ArmingHomeSyncType;


enum class CSMAIN_RPC_SERVER_TYPE
{
    QUERY_UID_STATUS = 0,
};

enum class CSPBX_RPC_SERVER_TYPE
{
    QUERY_UID_STATUS = 0,
    WAKEUP_APP = 1,
    QUERY_LANDLINE_STATUS = 2,
    WRITE_CALL_HISTORY = 3,  
    QUERY_LANDLINE_NUMBER = 4,
    QUERY_MAIN_SITE_SIP = 5,
    HANGUP_APP = 6,
    QUERY_SIP_INFO =7,
    //标记枚举的数量
    CHECK_TYPE_MAX,
};

enum class CSVIDEORECORD_RPC_SERVER_TYPE
{
    START_VIDEO_RECORD = 0,
    STOP_VIDEO_RECORD = 1,
};

typedef enum 
{
    SEND_TO_SLAVE = 0,
    SEND_TO_MASTER = 1,
}EmailAndPhoneSendType;

#define APP_ERR_CODE_SUCCESS "0"
#define APP_ERR_CODE_OPEN_DOOR_FAILED "1000000016"

class CAkcsMsg
{
public:
    CAkcsMsg()
    : msgbody_buf_(nullptr)
    , msgbody_len_(0) {
        
    }
    virtual ~CAkcsMsg() {} //浅拷贝,不能释放内存
    virtual void ParseMsgBody(char* buf, uint32_t len)
    {
        msgbody_buf_ = buf;
        msgbody_len_ = len;
    }

protected:
    char* msgbody_buf_;  //note:没有持有自己的一份内存
    uint32_t msgbody_len_; 
};
typedef std::map<std::string/*主叫方sip*/, std::list<std::string>/*被叫方sip列表*/> CallBothSideSips;
class CAkcsQueryAppsStatu : public CAkcsMsg
{
public:
    CAkcsQueryAppsStatu() {}
    ~CAkcsQueryAppsStatu() {}
    const std::vector<std::string> GetAppSips() const;
        
};

//pbx通知
class QueryCallSipsStatus : public CAkcsMsg
{
public:
    QueryCallSipsStatus() {}
    ~QueryCallSipsStatus() {}
};

class CAkcsQueryCallLandline : public CAkcsMsg
{
public:
    CAkcsQueryCallLandline() {}
    ~CAkcsQueryCallLandline() {}
    std::string GetPhone() const;
        
};


#endif //__AKCS_BASE_MSG_DEF_H__
