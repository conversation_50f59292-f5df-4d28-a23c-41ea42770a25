/*
 * AkcsPduBase.h
 */

#ifndef __AKCS_BASE_MYSQL_SEG_FLAGS_H__
#define __AKCS_BASE_MYSQL_SEG_FLAGS_H__


namespace MysqlSegFlags {

//devices/personaldevices 表flags标识
//chenzhx add 之前位数统计错了第一位应该是没有用的 第四位是管理机是否全选设备, 
// 这个和室内机是不相干的，所以目前不影响使用
enum Devices {
    DEVTYPE = 1<<0,  //个人是否是公共设备 #这位是没有用的
    STHOME = 1<<1,   //设备触发器home是否可以设置
    STAWAY = 1<<2,   //设备触发器AWAY是否可以设置
    STSLEEP = 1<<3,   //设备触发器SLEEP是否可以设置
};

}
#endif /* __AKCS_BASE_MYSQL_SEG_FLAGS_H__ */