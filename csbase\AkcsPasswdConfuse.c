#include <stdio.h>
#include <stdlib.h>
#include <time.h>
#include <string.h>
#include <unistd.h>
#include "AkcsPasswdConfuse.h"
//用于凯撒的偏移列表,找到字符然后加上偏移得到最终的字符，这个要和php的对应
static const char confuse_passwd_str[] = "CADaHgKxWec5f2otYIiRlmNLqP0z3hU7ZnFbES8QTOs9dG6yMvu4Jp1VjkrBwX";
static const char passwd_code_list[] = "abcdefghijklmnopqrstuvwxyz1234567890ABCDEFGHIJKLMNOPQRSTUVWXYZ";

static const int random_head_len = 4;
static const int confuse_out_len = 32;
static const int confuse_prefix_len = 7;
static const int confuse_passwd_max_len = 25;// confuse_out_len - confuse_prefix_len
static const int confuse_passwd_min_len = 6;
static const int confuse_setp_index = 4;
static const int confuse_setp_len = 1;//凯撒的偏移setp 用1 位表示
static const int confuse_password_index = 5;
static const int confuse_password_len = 2;//密码长度 用2 位表示


int CreateRandStr(char *str, int len)
{
    srand(time(NULL));
    int i;
    int password_len  = strlen(passwd_code_list);
    for (i = 0; i < len; ++i)
    {
       int num = rand() % password_len;
       str[i] = passwd_code_list[num];
    }
    str[i] = '\0';
    return 0;
}


int GetConfuseOffsetIndex(char element)
{
	int index = -1;
	switch (element)
	{
        case 'C':
            index = 0;
            break;
        case 'A':
            index = 1;
            break;
        case 'D':
            index = 2;
            break;
        case 'a':
            index = 3;
            break;
        case 'H':
            index = 4;
            break;
        case 'g':
            index = 5;
            break;
        case 'K':
            index = 6;
            break;
        case 'x':
            index = 7;
            break;
        case 'W':
            index = 8;
            break;
        case 'e':
            index = 9;
            break;
        case 'c':
            index = 10;
            break;
        case '5':
            index = 11;
            break;
        case 'f':
            index = 12;
            break;
        case '2':
            index = 13;
            break;
        case 'o':
            index = 14;
            break;
        case 't':
            index = 15;
            break;
        case 'Y':
            index = 16;
            break;
        case 'I':
            index = 17;
            break;
        case 'i':
            index = 18;
            break;
        case 'R':
            index = 19;
            break;
        case 'l':
            index = 20;
            break;
        case 'm':
            index = 21;
            break;
        case 'N':
            index = 22;
            break;
        case 'L':
            index = 23;
            break;
        case 'q':
            index = 24;
            break;
        case 'P':
            index = 25;
            break;
        case '0':
            index = 26;
            break;
        case 'z':
            index = 27;
            break;
        case '3':
            index = 28;
            break;
        case 'h':
            index = 29;
            break;
        case 'U':
            index = 30;
            break;
        case '7':
            index = 31;
            break;
        case 'Z':
            index = 32;
            break;
        case 'n':
            index = 33;
            break;
        case 'F':
            index = 34;
            break;
        case 'b':
            index = 35;
            break;
        case 'E':
            index = 36;
            break;
        case 'S':
            index = 37;
            break;
        case '8':
            index = 38;
            break;
        case 'Q':
            index = 39;
            break;
        case 'T':
            index = 40;
            break;
        case 'O':
            index = 41;
            break;
        case 's':
            index = 42;
            break;
        case '9':
            index = 43;
            break;
        case 'd':
            index = 44;
            break;
        case 'G':
            index = 45;
            break;
        case '6':
            index = 46;
            break;
        case 'y':
            index = 47;
            break;
        case 'M':
            index = 48;
            break;
        case 'v':
            index = 49;
            break;
        case 'u':
            index = 50;
            break;
        case '4':
            index = 51;
            break;
        case 'J':
            index = 52;
            break;
        case 'p':
            index = 53;
            break;
        case '1':
            index = 54;
            break;
        case 'V':
            index = 55;
            break;
        case 'j':
            index = 56;
            break;
        case 'k':
            index = 57;
            break;
        case 'r':
            index = 58;
            break;
        case 'B':
            index = 59;
            break;
        case 'w':
            index = 60;
            break;
        case 'X':
            index = 61;
            break;
    	default:
    		break;
	}
	return index;
}

int GetDecodeChar(char *src, char *dst, int step, int all_len)
{
    int old_index = GetConfuseOffsetIndex(*src);
    int new_index = 0;
    if (old_index == -1)
    {
        *dst = *src;
    }
    else
    {
        if (old_index - step < 0)
        {
            new_index = old_index - step + all_len;
        }
        else
        {
            new_index = old_index - step;
        }
        *dst = confuse_passwd_str[new_index];
    }

    return 0;
}


int PasswdDecode(const char *src, int srclen, char *dst, int dst_len)
{	
    if (srclen != confuse_out_len || !dst)
    {
        //如果不是混淆的长度，那么原封不动返回
        if (dst)
        {
            snprintf(dst, dst_len, "%s", src);
        }
        return -1;
    }
    int password_all_str_len = strlen(confuse_passwd_str);
    char  prefix[64] = "";
    char  str_step[64] = "";
    char  code_len_tmp[64] = "";
    strncpy(prefix, src, confuse_prefix_len);
    strncpy(str_step, src + confuse_setp_index, confuse_setp_len);
    strncpy(code_len_tmp, src + confuse_password_index, confuse_password_len);
	int step = atoi(str_step);

    char code_len[3] = "";
    GetDecodeChar(code_len_tmp, code_len, step, password_all_str_len);
    GetDecodeChar(code_len_tmp + 1, code_len + 1, step, password_all_str_len);

	int len = atoi(code_len);
    if (len > dst_len - 1)
    {
        snprintf(dst, dst_len, "%s", src);
        return -1;        
    }

    char  before_code[64] = "";
    strncpy(before_code, src + confuse_prefix_len, len);
    int i = 0;
    for (; i < len; i++)
    {
        GetDecodeChar(before_code + i, dst + i, step, password_all_str_len);
    }
    dst[i] = '\0';
    return 0;
}

int GetEncodeChar(const char *src, char *dst, int step, int all_len)
{
    int old_index = GetConfuseOffsetIndex(*src);
    int new_index = 0;
    if (old_index == -1)
    {
        *dst = *src;
        return 0;
    }
    
    if (old_index + step > all_len - 1)
    {
        new_index = old_index + step - all_len;
    }
    else
    {
        new_index = old_index + step;
    }
    *dst = confuse_passwd_str[new_index];    
    return 0;
}

int PasswdEncode(const char *origin_code, int code_len, char *dst, int dst_len)
{	
    if (!origin_code || !dst || code_len < confuse_passwd_min_len || code_len > confuse_passwd_max_len )
    {
        //不符合要求返回原来的字符串
        if (dst && origin_code)
        {
            snprintf(dst, dst_len, "%s", origin_code);
        }    
	    return -1;
    }
    int password_all_str_len = strlen(confuse_passwd_str);    

    //随机头
    char head[64] = {0};
    CreateRandStr(head, random_head_len);

    //偏移值
    int step = rand() % 9 + 1;

    //混淆密码长度
    char password_len[3] = "";
    char tmp[3] = "";
    sprintf(tmp, "%02d", code_len);
    GetEncodeChar(tmp, password_len, step, password_all_str_len);
    GetEncodeChar(tmp + 1, password_len + 1, step, password_all_str_len);

    //随机头+偏移值+密码长度
    char prefix[8] = {0};
    sprintf(prefix, "%s%d%s", head, step, password_len);
    
    char new_code[32] = {0};
    int i = 0;
    for (i = 0; i < code_len; i++)
    {
        GetEncodeChar(origin_code + i, new_code + i, step, password_all_str_len);
    }

    //补充尾部
    int suffix_len = confuse_passwd_max_len - code_len;
    char suffix[64] = {0};
    CreateRandStr(suffix, suffix_len);

    snprintf(dst, dst_len, "%s%s%s", prefix, new_code, suffix);
    return 0;
}

#if 0
#include <iostream>
#include <vector>

void SplitString(const std::string& s, const std::string& c, std::vector<std::string>& oVec)
{
  std::string::size_type pos1 = 0;
  auto pos2 = s.find(c);
  while(std::string::npos != pos2)
  {
	//容错，防止加入一个空串,形如:12,,23,42 或者:12,23, 或者:12,23,,
    if (pos2 > pos1)
    {
    	oVec.push_back(s.substr(pos1, pos2-pos1));
    }
    pos1 = pos2 + c.size();
    pos2 = s.find(c, pos1);
  }
  //将最后一个字符串压入容器中
  if(pos1 != s.length())
  {
      oVec.push_back(s.substr(pos1));
  }
}

int main(void)
{
     //web_backend/apache-v3.0/notify/tools/confuse/confuse_passwd_interface_test.php
    //这份文件由php端的压测工具生成，模拟web添加密码混淆，c端解析的流程
    FILE *fp = fopen("./confuse_test.log", "r");
    if (fp)
    {
        char line[1024] = "";
        while(fgets(line, 1024, fp) != NULL)
        {

          std::string s = line; 
          std::string new1 = s.substr(0,s.find_last_not_of(" \n\r\t")+1);
            std::vector<std::string> v;
            SplitString(new1, ",", v);
            char d[64] = "";
            PasswdDecode(v[1].c_str(),v[1].length(), d, sizeof(d));
            std::string decode = d;
            if (decode != v[0])
            {
                std::cout << "============error :" <<  new1;
                return 1;
            }
            std::cout << d << "   " << v[0] <<  std::endl;
        }
    }
    else
    {
        std::cout << "file no exist！" << std::endl;
    }
        
	return 0;
}


/*
#include <iostream>
int main(void)
{
    int i = 0;
    using namespace std;
	for (i = 0; i < 100000; i++)
	{
	    char code[32] = "";
        int num = rand() % confuse_passwd_max_len + 1;
        if(num < confuse_passwd_min_len)
        {
            num = confuse_passwd_min_len + num;
        }
        CreateRandStr(code, num);

        char encode[64] = "";
        PasswdEncode(code, num, encode, sizeof(encode));

        char decode[64] = "";
        PasswdDecode(encode, (int)strlen(encode), decode, sizeof(decode));

        
		if(strlen(code) > 0 && strcmp(code, decode) != 0 )
		{
            cout << "code:" << code << endl;
            cout << "encode:" << encode <<  endl;
            cout << "decode:" << decode << endl;
            cout << ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>error"<< endl;
			return -1;
		}
        cout << "code:" << code << endl;
        cout << "encode:" << encode <<  endl;
        cout << "decode:" << decode << endl;        
        cout << "num:" << num << endl;
        sleep(1);
	}
	return 0;
}
*/
#endif
