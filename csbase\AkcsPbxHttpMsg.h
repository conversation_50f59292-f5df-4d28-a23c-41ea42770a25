#ifndef _AKCS_PBX_HTTP_MSG_H_
#define _AKCS_PBX_HTTP_MSG_H_

//http request url
#define PBX_HTTP_URL_UID_STATUS       "/app-status"
#define PBX_HTTP_URL_APP_WAKEUP       "/app-wakeup"
#define PBX_HTTP_URL_WRITE_HISTORY    "/write-history"
#define PBX_HTTP_URL_LANDLINE_STATUS  "/landline-status"




//http parma
#define PBX_HTTP_PARMA_STATUS     "Status"
#define PBX_HTTP_PARMA_UID        "Sip"
#define PBX_HTTP_PARMA_CALLER     "Caller"
#define PBX_HTTP_PARMA_CALLEE     "Callee"
#define PBX_HTTP_PARMA_CALLER_NAME     "CallerName"
#define PBX_HTTP_PARMA_CALLED          "Called"
#define PBX_HTTP_PARMA_START_TIME     "StartTime"
#define PBX_HTTP_PARMA_ANSWER_TIME     "AnswerTime"
#define PBX_HTTP_PARMA_BILL_SECOND     "BillSecond"
#define PBX_HTTP_PARMA_APPTYPE     "AppType"
#define PBX_HTTP_PARMA_PHONE     "Phone"
#define PBX_HTTP_PARMA_TRACEID     "TraceId"

//http return message format
#define HTTP_RET_PARMA_DATA        "data"
#define HTTP_RET_PARMA_MESSAGE      "message"
#define HTTP_RET_PARMA_RESULT_CODE  "result"
#define HTTP_RET_PARMA_STATUS       "status"
#define HTTP_RET_PARMA_CODE         "code"


//http ret code 
#define HTTP_RET_RESULT_SUCC      0
#define HTTP_RET_RESULT_INTERNAL_SERVER_ERROR   500



//http ret value 
#define HTTP_RET_VALUE_ONLINE       "online"
#define HTTP_RET_VALUE_OFFLINE      "offline"
#define HTTP_RET_VALUE_DND          "dnd"



#endif




