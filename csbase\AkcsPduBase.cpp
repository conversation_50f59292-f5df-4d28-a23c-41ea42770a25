#include "util.h"
#include "AkcsPduBase.h"
#include "ThreadLocalSingleton.h"


CAkcsPdu::CAkcsPdu()
{
	pdu_header_.packet_len = 0;
	pdu_header_.header_len = 0;
	pdu_header_.msg_ver = 0;
	pdu_header_.msg_id = 0;
	pdu_header_.msg_seq = 0;
	pdu_header_.msg_traceid = 0;
    pdu_header_.msg_project_type = 0;
    pdu_header_.msg_parent_id = 0;
    pdu_create_time_ = std::chrono::steady_clock::now();
}

char* CAkcsPdu::GetBuffer()
{
    return (char *)buf_.GetBuffer();
}

uint32_t CAkcsPdu::GetLength()
{
    return buf_.GetWriteOffset();
}

char* CAkcsPdu::GetBodyData()
{
    return (char *)(buf_.GetBuffer() + pdu_header_.header_len); //消息头不同版本不一定一致,当服务端跟客户端的消息头不一致时
}

uint32_t CAkcsPdu::GetBodyLength()
{
    uint32_t body_length = 0;
    body_length = buf_.GetWriteOffset() - pdu_header_.header_len;
    return body_length;
}

int CAkcsPdu::ReadPduHeader(char* buf, uint32_t len)
{
	int ret = -1;
	if (len >= sizeof(PduHeader_t) && buf) {
		CByteStream is((uchar_t *)buf, len);
        
		is >> pdu_header_.packet_len;  //内部已经做了大小端序的转化
		is >> pdu_header_.header_len;
		is >> pdu_header_.msg_ver;
		is >> pdu_header_.msg_id;
		is >> pdu_header_.msg_seq;
		is >> pdu_header_.msg_traceid;
        is >> pdu_header_.msg_project_type;
        is >> pdu_header_.msg_parent_id;
		pdu_header_.header_len <= pdu_header_.packet_len ? (ret = 0) : (ret = -1);
	}

	return ret;
}
//写消息包整体长度的字段
void CAkcsPdu::WriteMsgLen()
{
	uchar_t* buf = (uchar_t*)GetBuffer();
	CByteStream::WriteInt32(buf, GetLength());
}

//整体包长packet_len不需要设置,当包组装完成后,通过buf.GetLength()来获取即可
void CAkcsPdu::SetHeadLen(uint16_t head_len)
{
	uchar_t* buf = (uchar_t*)GetBuffer();
	CByteStream::WriteUint16(buf + 4, head_len);
}

void CAkcsPdu::SetVersion(uint16_t version)
{
	uchar_t* buf = (uchar_t*)GetBuffer();
	CByteStream::WriteUint16(buf + 6, version);
}

void CAkcsPdu::SetCommandId(uint32_t command_id)
{
    uchar_t* buf = (uchar_t*)GetBuffer();
    CByteStream::WriteUint32(buf + 8, command_id);
}

void CAkcsPdu::SetSeqNum(uint32_t seq_num)
{
	uchar_t* buf = (uchar_t*)GetBuffer();
	CByteStream::WriteUint32(buf + 12, seq_num);
}
void CAkcsPdu::SetTraceId(uint64_t trace_id)
{
	uchar_t* buf = (uchar_t*)GetBuffer();
	CByteStream::WriteUint64(buf + 16, trace_id);//16:累积偏移
}

void CAkcsPdu::SetProjectType(uint16_t type)
{
    uchar_t* buf = (uchar_t*)GetBuffer();
    CByteStream::WriteUint16(buf + 24, type);
}

void CAkcsPdu::SetParentId(uint64_t parent_id)
{
	uchar_t* buf = (uchar_t*)GetBuffer();
	CByteStream::WriteUint64(buf + 26, parent_id);
}


void CAkcsPdu::SetMsgBody(const void* msg_body, uint32_t len)
{
    //设置包体，则需要重置下空间
    buf_.Read(NULL, buf_.GetWriteOffset());
    buf_.Write(NULL, sizeof(PduHeader_t)); //写包头
    buf_.Write(msg_body, len);
    WriteMsgLen(); //赋值消息包长度

    InitMsgHeader();
}
//protobuf消息
void CAkcsPdu::SetMsgBody(const google::protobuf::MessageLite* msg)
{
    //设置包体，则需要重置下空间
    buf_.Read(NULL, buf_.GetWriteOffset());
    buf_.Write(NULL, sizeof(PduHeader_t)); //写包头
    uint32_t msg_size = msg->ByteSize();
    uchar_t* szData = new uchar_t[msg_size];
    if (!msg->SerializeToArray(szData, msg_size))
    {
        ::printf("pb msg miss required fields.");
    }
    buf_.Write(szData, msg_size);
    delete[] szData;
    WriteMsgLen(); //赋值消息包长度
    
    pdu_header_.msg_traceid = ThreadLocalSingleton::GetInstance().GetTraceID();
    
    InitMsgHeader();    
}
//add by chenzhx 20220114 如果外层没有进行设置,那么默认值就是随机值
void CAkcsPdu::InitMsgHeader()
{
    uchar_t* buf = (uchar_t*)GetBuffer();
    CByteStream::WriteUint16(buf + 4, pdu_header_.header_len);
    
    buf = (uchar_t*)GetBuffer();
    CByteStream::WriteUint16(buf + 6, pdu_header_.msg_ver);

    buf = (uchar_t*)GetBuffer();
    CByteStream::WriteUint32(buf + 8, pdu_header_.msg_id);

    buf = (uchar_t*)GetBuffer();
    CByteStream::WriteUint32(buf + 12, pdu_header_.msg_seq);

    buf = (uchar_t*)GetBuffer();
    CByteStream::WriteUint64(buf + 16, pdu_header_.msg_traceid);//16:累积偏移

    buf = (uchar_t*)GetBuffer();
    CByteStream::WriteUint16(buf + 24, pdu_header_.msg_project_type);  

    buf = (uchar_t*)GetBuffer();
    CByteStream::WriteUint64(buf + 26, pdu_header_.msg_parent_id);

}


