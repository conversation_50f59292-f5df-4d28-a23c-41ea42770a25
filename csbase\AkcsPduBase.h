/*
 * AkcsPduBase.h
 */

#ifndef __AKCS_BASE_MSG_PDUBASE_H__
#define __AKCS_BASE_MSG_PDUBASE_H__

#include "UtilPdu.h"
#include "google/protobuf/message_lite.h"
#include <memory>  
#include <chrono>

#define IM_PDU_HEADER_LEN		16
#define IM_PDU_VERSION			1

//| package length   | true  | int32 bigendian | 包长度       |
//| header Length    | true  | int16 bigendian | 包头长度     |
//| ver              | true  | int16 bigendian | 协议版本         |
//| id               | true  | int32 bigendian | 协议指令         |
//| seq              | true  | int32 bigendian | 序列号          |
//| traceid          | true  | int64 bigendian | 全链路跟踪id      |
//| body             | false | binary          | 具体消息         |
typedef struct {
    uint32_t packet_len;  
    uint16_t header_len; //包头长,含packet_len字段
    uint16_t msg_ver;
    uint32_t msg_id;
    uint32_t msg_seq;
	uint64_t msg_traceid;//added by chenyc,2019-08-06,增加traceid用于实现全链路跟踪
	uint16_t msg_project_type;//业务类型 chenzhx 20211206
	uint64_t msg_parent_id;//全链路跟踪parent_id
} PduHeader_t;

class  CAkcsPdu
{
public:
    CAkcsPdu();
    virtual ~CAkcsPdu() {}
    
    char* GetBuffer();
    uint32_t GetLength();
    char* GetBodyData();
    uint32_t GetBodyLength();
    
    uint16_t GetHeadLen() { return pdu_header_.header_len; }
    uint16_t GetVersion() { return pdu_header_.msg_ver; }
    uint32_t GetCommandId() { return pdu_header_.msg_id; }
    uint32_t GetSeqNum() { return pdu_header_.msg_seq; }
    uint64_t GetTraceId() { return pdu_header_.msg_traceid; }
    uint64_t GetParentId() { return pdu_header_.msg_parent_id; }
    uint16_t GetProjectType() { return pdu_header_.msg_project_type; }
	
    void SetHeadLen(uint16_t head_len);  
    void SetVersion(uint16_t version);  
    void SetCommandId(uint32_t command_id);
    void SetSeqNum(uint32_t seq_num);
    void SetTraceId(uint64_t trace_id);
    //默认0住宅、1办公
    void SetProjectType(uint16_t business_type);
    void SetParentId(uint64_t parent_id);
    
    void WriteMsgLen();
    
    void Write(const char* buf, uint32_t packet_len) { buf_.Write((const void*)buf, packet_len);}//在内层申请了内存,并自己负责释放 
    int ReadPduHeader(char* buf, uint32_t len);
    void SetMsgBody(const void* msg, uint32_t len);
    void SetMsgBody(const google::protobuf::MessageLite* msg);
    std::chrono::steady_clock::time_point GetPduCreateTime() { return pdu_create_time_; }

private:
    void InitMsgHeader();
    CSimpleBuffer	buf_;  //消息包: 消息头+消息体
    PduHeader_t	    pdu_header_;  //消息头 
    std::chrono::steady_clock::time_point pdu_create_time_;
};

typedef std::shared_ptr<CAkcsPdu> AkcsPduPrt;
typedef std::list<AkcsPduPrt> AkcsPduList;

#endif /* __AKCS_BASE_MSG_PDUBASE_H__ */
