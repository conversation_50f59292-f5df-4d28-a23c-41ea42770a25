#include <AkLogging.h>
#include <AkcsRequestRecorder.h>

const size_t MAX_REQUESTS = 20000;
const size_t MAX_CLIENTS = 500;

void CAkcsRequestRecorder::RecordRequest(const std::string& client_id, const std::string& message_id) 
{
    std::lock_guard<std::mutex> lock(mutex_);

    recent_requests_.emplace_back(client_id, message_id);
    request_counts_[client_id][message_id]++;

    if (recent_requests_.size() > MAX_REQUESTS) 
    {
        const auto& old_request = recent_requests_.front();
        const auto& old_client = std::get<0>(old_request);
        const auto& old_message_id = std::get<1>(old_request);

        if (--request_counts_[old_client][old_message_id] == 0) 
        {
            request_counts_[old_client].erase(old_message_id);
            if (request_counts_[old_client].empty()) 
            {
                request_counts_.erase(old_client);
            }
        }

        recent_requests_.pop_front();
    }
}

std::vector<std::tuple<std::string, std::string, int>> CAkcsRequestRecorder::GetSortedRequestCounts() 
{
    std::lock_guard<std::mutex> lock(mutex_);
	
    std::vector<std::tuple<std::string, std::string, int>> sorted_requests;

    for (const auto& client_pair : request_counts_) 
    {
        for (const auto& message_pair : client_pair.second) 
        {
            sorted_requests.emplace_back(client_pair.first, message_pair.first, message_pair.second);
        }
    }

    sort(sorted_requests.begin(), sorted_requests.end(), [](const std::tuple<std::string, std::string, int>& a, const std::tuple<std::string, std::string, int>& b) 
    {
        return std::get<2>(a) > std::get<2>(b);
    });

    return sorted_requests;
}
