#include <iostream>
#include <unordered_map>
#include <deque>
#include <tuple>
#include <mutex>
#include <vector>
#include <algorithm>
#include <string>
#include <random>
#include <chrono>
#include <iomanip>
#include <sstream>

class CAkcsRequestRecorder 
{
public:
    static CAkcsRequestRecorder& getInstance() {
        static CAkcsRequestRecorder instance;
        return instance;
    }
	
    void RecordRequest(const std::string& client, const std::string& message_id);
    std::vector<std::tuple<std::string, std::string, int>> GetSortedRequestCounts();
	
private:
    CAkcsRequestRecorder() {}
    std::mutex mutex_;
    std::deque<std::tuple<std::string, std::string>> recent_requests_;
    std::unordered_map<std::string, std::unordered_map<std::string, int>> request_counts_;
};
