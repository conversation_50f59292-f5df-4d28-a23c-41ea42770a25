#ifndef __CSP2A_COMMON_H__
#define __CSP2A_COMMON_H__

#include <stdint.h>
#include "BasicDefine.h"
#include "AkcsMsgDef.h"

#ifndef URL_SIZE
#define URL_SIZE                 256
#endif

#ifndef MAC_SIZE
#define MAC_SIZE                 20
#endif

#ifndef IP_SIZE
#define IP_SIZE                  16
#endif

#ifndef MODULE_SIZE
#define MODULE_SIZE              64
#endif

#ifndef CONFIGPAIR_SIZE
#define CONFIGPAIR_SIZE          640
#endif

#ifndef AREA_NODE_SIZE
#define AREA_NODE_SIZE                  32    //地址节点长度
#endif

#ifndef COMMUNITY_SIZE
#define COMMUNITY_SIZE                  32    //社区账号编码长度
#endif

#ifndef BIND_CODE_SIZE
#define BIND_CODE_SIZE                  24    //绑定码编码长度
#endif

#ifndef DEV_ACCESS_SIZE
#define DEV_ACCESS_SIZE                 960   //privatekey/rfidkey所有能匹配的设备
#endif

enum/*update为增删改都可能, modify只是修改*/
{
    //ip变化和设备升级
    CSMAIN_COMM_DEV_IP_CHANGE = 800,
    CSMAIN_COMM_DEV_UPGRADE = 801,
    CSMAIN_COMM_DEV_MAINTANCE = 802,
    CSMAIN_COMM_UNIT_DEV_IP_CHANGE = 803,
    CSMAIN_COMM_UNIT_DEV_UPGRADE = 804,
    CSMAIN_COMM_UNIT_DEV_MAINTANCE = 805,
    CSMAIN_COMM_PUB_DEV_IP_CHANGE = 806,
    CSMAIN_COMM_PUB_DEV_UPGRADE = 807,
    CSMAIN_COMM_PUB_DEV_MAINTANCE = 808,//运维更新设备信息// 个人设备需要更新联动，公共设备只需要更新公共设备
    CSMAIN_COMM_ACCOUNT_NFC_UPDATE = 809,//更新账号的nfc值
    
    //office ip变化和设备升级
    CSMAIN_OFFICE_DEV_IP_CHANGE = 820,
    CSMAIN_OFFICE_DEV_UPGRADE = 821,
    CSMAIN_OFFICE_DEV_MAINTANCE = 822,
    CSMAIN_OFFICE_UNIT_DEV_IP_CHANGE = 823,
    CSMAIN_OFFICE_UNIT_DEV_UPGRADE = 824,
    CSMAIN_OFFICE_UNIT_DEV_MAINTANCE = 825,
    CSMAIN_OFFICE_PUB_DEV_IP_CHANGE = 826,
    CSMAIN_OFFICE_PUB_DEV_UPGRADE = 827,
    CSMAIN_OFFICE_PUB_DEV_MAINTANCE = 828,//运维更新设备信息// 个人设备需要更新联动，公共设备只需要更新公共设备
    CSMAIN_OFFICE_ACCOUNT_NFC_UPDATE = 829,//更新账号的nfc值
    
    CSMAIN_PER_DEV_IP_CHANGE = 900,
    CSMAIN_PER_DEV_UPGRADE = 901,
    CSMAIN_PER_DEV_MAINTANCE = 902,//运维更新设备信息 // 个人设备需要更新联动，公共设备只需要更新公共设备
    CSMAIN_PER_DEV_NFC_CHANGE = 903,
    
    //个人
    /*(只会更新config/contact不会更新卡)IP直播切换、motion开关切换、群组呼叫和顺序呼叫更新*/
    WEB_PER_NODE_UPDATE = 1000,
    WEB_PER_ADD_USER = 1001,
    WEB_PER_DEL_USER = 1002,
    WEB_PER_MODIFY_USER = 1003,
    WEB_PER_ADD_SLAVE_USER = 1004,
    WEB_PER_DEL_SLAVE_USER = 1005,
    WEB_PER_MODIFY_SLAVE_USER = 1006,  
    WEB_PER_UPDATE_RF = 1007,
    WEB_PER_UPDATE_PIN = 1008,
    WEB_PER_ADD_DEV = 1009,  
    WEB_PER_DEL_DEV = 1010,
    WEB_PER_MODIFY_DEV = 1011,
    WEB_PER_PHONE_PAY_SUCC = 1012,  
    WEB_PER_UPLOAD_FACE_PIC = 1013,  
    WEB_PER_DELETE_FACE_PIC = 1014,
    WEB_PER_UPDATE_TIMEZOME = 1015,
    WEB_PER_UPDATE_MAC_CONFIG = 1016,
    WEB_PER_MODIFY_DETECTION_CONFIG = 1017, //侦测：包括移动/包裹/声音
    WEB_PER_UPDATE_NODE_CONTACT = 1018,
    WEB_PER_DEL_THIRD_CAMERA = 1019,
    WEB_PER_UPDATE_NODE_CONFIG = 1020,

    //社区
    /*(只会更新config/contact不会更新卡)IP直播切换、motion开关切换、群组呼叫和顺序呼叫更新*/
    WEB_COMM_NODE_UPDATE = 2000,
    WEB_COMM_ADD_USER = 2001,
    WEB_COMM_DEL_USER = 2002,
    WEB_COMM_MODIFY_USER = 2003,  
    WEB_COMM_ADD_SLAVE_USER = 2004,
    WEB_COMM_DEL_SLAVE_USER = 2005,
    WEB_COMM_MODIFY_SLAVE_USER = 2006,      
    WEB_COMM_UPDATE_RF = 2007,
    WEB_COMM_UPDATE_PIN = 2008,
    WEB_COMM_ADD_DEV = 2009,  
    WEB_COMM_DEL_DEV = 2010,
    WEB_COMM_MODIFY_DEV = 2011,
    WEB_COMM_UPLOAD_FACE_PIC = 2103,
    WEB_COMM_DELETE_FACE_PIC = 2104,
    WEB_COMM_UPDATE_MAC_CONFIG = 2105,
    WEB_COMM_UPDATE_COMMUNITY_CALLS = 2106,
    WEB_COMM_UPDATE_COMMUNITY_PUB_UNIT_CONTACT = 2107,
    WEB_COMM_UPDATE_APT_CALLRULE = 2108,    
    WEB_COMM_UPDATE_PUB_MAC_CONTACT = 2109,
    WEB_COMM_UPDATE_CONFIG_AND_CONTACT = 2110,
    WEB_COMM_MODIFY_MAC_CONTACT =2111,
    WEB_COMM_DEL_THIRD_CAMERA = 2112,

    //社区单元设备
    WEB_COMM_UNIT_UPDATE_RF = 3001,
    WEB_COMM_UNIT_UPDATE_PIN = 3002,
    WEB_COMM_UNIT_ADD_DEV = 3003,  
    WEB_COMM_UNIT_DEL_DEV = 3004,
    WEB_COMM_UNIT_MODIFY_DEV = 3005,
    WEB_COMM_UNIT_DEL_THIRD_CAMERA = 3006,

    //社区最外围设备
    WEB_COMM_PUB_UPDATE_RF = 4001,
    WEB_COMM_PUB_UPDATE_PIN = 4002,
    WEB_COMM_PUB_ADD_DEV = 4003,  
    WEB_COMM_PUB_DEL_DEV = 4004,
    WEB_COMM_PUB_MODIFY_DEV = 4005,
    WEB_COMM_PUB_DEL_THIRD_CAMERA = 4006,
    
    //社区信息
    WEB_COMM_INFO = 5001,
    WEB_COMM_APT_PIN = 5002,
    WEB_COMM_MOTION = 5003,  
    WEB_COMM_IMPORT_COMMUNITY = 5004,
    WEB_COMM_ADD_BUILDING = 5005,    
    WEB_COMM_DEL_BUILDING = 5006,   
    WEB_COMM_MODIFY_BUILDING = 5007,
    WEB_COMM_MODIFY_TIMEINFO = 5008,/*时区时间格式,作用整个社区*/
    WEB_COMM_DELETE_COMMUNITY = 5009,/*删除社区*/
    WEB_COMM_IMPORT_FACE_PIC = 5010,
    WEB_COMM_DELETE_ALL_FACE_PIC = 5011,
    WEB_COMM_DELETE_ALL_RF_CARD = 5012,
    WEB_COMM_NOTIFY_FLOW_OUT_OF_LIMIT = 5013, //通知设备开启流量超额上报
    WEB_COMM_PUB_OPEN_ALL_DOOR = 5014,
    WEB_COMM_PUB_CLOSE_ALL_DOOR = 5015,
    WEB_COMM_ALLOW_CREATE_PIN = 5016,
    WEB_COMM_FEATURE_PLAN_RENEW = 5017, //高级功能续费
    WEB_COMM_MODIFY_PRIVATE_ACCESS = 5018, //pm操作PublicArea+PrivateArea的开关
    WEB_COMM_UPDATE_LANDLINE_STATUS = 5019,
    WEB_COMM_MODIFY_BUILDING_NAME = 5020, //building名称修改
    WEB_COMM_MODIFY_NODE_MOTION_CONFIG = 5021, //社区编辑个人motion
    WEB_COMM_MODIFY_FEATURE_PLAN = 5022,
    WEB_COMM_UPDATE_COMMUNITY_ALL = 5023, //更新社区所有的配置
    WEB_COMM_ADD_INDOOR_PLAN_DEV = 5024, //室内机方案添加设备，通知设备上报状态
    WEB_COMM_MODIFY_CONTACT_DISPLAY_ORDER = 5025, // 社区联系人姓名展示顺序更改, 刷contact
    WEB_COMM_MODIFY_BUILDING_FLOOR_SETTING = 5026, //building起始楼层和G0/G1/G2 修改，刷新config
    WEB_COMM_MODIFY_NODE_DEV_DETECTION = 5027, // 社区用户设备侦测开关修改
    WEB_COMM_MODIFY_NODE_CONFIG = 5028,
    //Office 沿用之前社区的id,只改了名称
    /*(只会更新config/contact不会更新卡)IP直播切换、motion开关切换、群组呼叫和顺序呼叫更新*/
    WEB_OFFICE_NODE_UPDATE = 2000,
    WEB_OFFICE_ADD_USER = 2001,
    WEB_OFFICE_DEL_USER = 2002,
    WEB_OFFICE_MODIFY_USER = 2003,  
    //WEB_COMM_ADD_SLAVE_USER = 2004,
    //WEB_COMM_DEL_SLAVE_USER = 2005,
    //WEB_COMM_MODIFY_SLAVE_USER = 2006,      
    //WEB_COMM_UPDATE_RF = 2007,
    //WEB_COMM_UPDATE_PIN = 2008,
    WEB_OFFICE_ADD_DEV = 2009,  
    WEB_OFFICE_DEL_DEV = 2010,
    WEB_OFFICE_MODIFY_DEV = 2011,
    WEB_OFFICE_UPLOAD_FACE_PIC = 2103,
    WEB_OFFICE_DELETE_FACE_PIC = 2104,
    WEB_OFFICE_UPDATE_MAC_CONFIG = 2105,
    WEB_OFFICE_UPDATE_NODE_PUB_USER = 2106,
    WEB_OFFICE_UPDATE_NODE_CONTACT = 2107,
    
    //社区单元设备
    //WEB_COMM_UNIT_UPDATE_RF = 3001,
    //WEB_COMM_UNIT_UPDATE_PIN = 3002,
    WEB_OFFICE_UNIT_ADD_DEV = 3003,  
    WEB_OFFICE_UNIT_DEL_DEV = 3004,
    WEB_OFFICE_UNIT_MODIFY_DEV = 3005,

    //社区最外围设备
    //WEB_COMM_PUB_UPDATE_RF = 4001,
    //WEB_COMM_PUB_UPDATE_PIN = 4002,
    WEB_OFFICE_PUB_ADD_DEV = 4003,  
    WEB_OFFICE_PUB_DEL_DEV = 4004,
    WEB_OFFICE_PUB_MODIFY_DEV = 4005,

    //社区信息
    WEB_OFFICE_INFO = 5001,
    WEB_OFFICE_APT_PIN = 5002,
    WEB_OFFICE_MOTION = 5003,  
    WEB_OFFICE_IMPORT_OFFICE = 5004,
    WEB_OFFICE_ADD_BUILDING = 5005,    
    WEB_OFFICE_DEL_BUILDING = 5006,   
    WEB_OFFICE_MODIFY_BUILDING = 5007,
    WEB_OFFICE_MODIFY_TIMEINFO = 5008,/*时区时间格式,作用整个社区*/
    WEB_OFFICE_DELETE_OFFICE = 5009,/*删除办公*/
    WEB_OFFICE_IMPORT_FACE_PIC = 5010,
    WEB_OFFICE_DELETE_ALL_FACE_PIC = 5011,
    //WEB_COMM_DELETE_ALL_RF_CARD = 5012,
    WEB_OFFICE_NOTIFY_FLOW_OUT_OF_LIMIT = 5013, //通知设备开启流量超额上报
    WEB_OFFICE_PUB_OPEN_ALL_DOOR = 5014,
    WEB_OFFICE_PUB_CLOSE_ALL_DOOR = 5015,
    WEB_OFFICE_ALLOW_CREATE_PIN = 5016,
    WEB_OFFICE_FEATURE_PLAN_RENEW = 5017, //高级功能续费
    WEB_OFFICE_MODIFY_CONTACT_DISPLAY_ORDER = 5018, // 办公联系人姓名展示顺序更改, 刷contact

    //office新消息
    //用户
    WEB_OFFICE_ACCOUNT_CHANGE = 6000,
    
    //部门设备
    WEB_OFFICE_DEPARTMENT_CHANGE = 6100,
    WEB_OFFICE_MODIFY_DEPARTMENT_NAME = 6101,
    
    //最外围设备
    WEB_OFFICE_PUB_CHANGE = 6200,
    
    //办公公共修改
    WEB_OFFICE_INFO_CHANGE = 6300,

    //办公数据分析新增
    WEB_OFFICE_ADD_ACCOUNT_ACCESS = 6400,
    WEB_OFFICE_MODIFY_ACCOUNT_ACCESS = 6401,
    WEB_OFFICE_ADD_USER_ACCESSGROUP = 6402,
    WEB_OFFICE_MODIFY_USER_ACCESSGROUP = 6403,
    WEB_OFFICE_ADD_USER_ACCESSGROUP_DEVICE = 6404,
    WEB_OFFICE_DEL_USER_ACCESSGROUP_DEVICE = 6405,
    WEB_OFFICE_MODIFY_USER_ACCESSGROUP_DEVICE = 6406,
    //刷新用户关联的权限组设备及个人设备
    WEB_OFFICE_MODIFY_USER_ALL_ACCESS = 6407,
    WEB_OFFICE_DEL_ACCOUNT_ACCESS = 6408,
    WEB_OFFICE_MODIFY_ACCESS_GROUP = 6411,
    WEB_OFFICE_MODIFY_STAFF = 6414,
    WEB_OFFICE_MODIFY_DELIVERY = 6417,

    //社区数据分析新增
    WEB_COMM_ADD_ACCOUNT_ACCESS = 6500,
    WEB_COMM_MODIFY_ACCOUNT_ACCESS = 6501,
    WEB_COMM_ADD_USER_ACCESSGROUP = 6502,
    WEB_COMM_MODIFY_USER_ACCESSGROUP = 6503,
    WEB_COMM_ADD_USER_ACCESSGROUP_DEVICE = 6504,
    WEB_COMM_DEL_USER_ACCESSGROUP_DEVICE = 6505,
    WEB_COMM_MODIFY_USER_ACCESSGROUP_DEVICE = 6506,
    WEB_COMM_ADD_PM_APP_ACCOUNT = 6507,
    WEB_COMM_DEL_PM_APP_ACCOUNT = 6508,
    WEB_COMM_MODIFY_PM_APP_ACCOUNT = 6509,
    //刷新用户关联的权限组设备及家庭设备
    WEB_COMM_MODIFY_USER_ALL_ACCESS = 6510,
    WEB_COMM_DEL_ACCOUNT_ACCESS = 6511,
    WEB_COMM_UPDATE_NODE_USER = 6512,
    WEB_COMM_MODIFY_PM_APP_STATUS = 6513,
    WEB_COMM_MODIFY_ACCESS_GROUP = 6516,
    WEB_COMM_MODIFY_STAFF = 6519,
    WEB_COMM_MODIFY_DELIVERY = 6522,
    WEB_COMM_MODIFY_HOLD_DOOR = 6523,
    WEB_COMM_MODIFY_USER_META = 6524,
    //智能锁,型号为SL20
    SMARTLOCK_SL20_CONFIG_UPDATE = 6600,
    //更新节点下SL20锁的配置
    SMARTLOCK_NODE_SL20_CONFIG_UPDATE = 6601,
    //更新项目下SL20锁的配置
    SMARTLOCK_PROJECT_SL20_CONFIG_UPDATE = 6602,
    //通知单个锁更新配置
    SMARTLOCK_SL20_LOCK_UPDATE_NOTIFY = 6603,
    //特殊：锁保活开关关闭的情况，要推一条消息给锁
    SMARTLOCK_SL20_LOCK_KEEP_ALIVE_SWITCH_CHANGE = 6604,
};


enum class OfficeUpdateType
{
    OFFICE_ACCESS_GROUP_CHANGE = 10000, // 更新权限组信息

    OFFICE_PUB_USER_INFO_CHANGE = 10001,  //所有设备的user

    OFFICE_GROUP_CHANGE = 10002, // 联系人和user

    OFFICE_DEV_CONFIG_CHANGE_WITH_MAC = 10003, // 单个设备的config. 携带设备

    OFFICE_USER_INFO_CHANGE = 10004, //contact、usermate、shecdule
    
    OFFICE_USER_ACCESS_CHANGE = 10005, //usermate

    OFFICE_DEV_INFO_CHANGE = 10006, // 单个设备的所有配置 + 所有设备的contact
    
    OFFICE_HOLIDAY_CHANGE = 10007, //all shecdule

    OFFICE_PROJECT_CHANGE = 10008, //项目信息，motion项目名称等 config/contact

    OFFICE_PROJECT_CHANGE_FOR_USER = 10009, //项目信息的高级功能过期+pin改变 需要更新所有user
    
    OFFICE_DEV_DELETE = 10010, // 只处理设备删除自己的配置文件全部清空

    OFFICE_DEV_ADD = 10011, // 上报状态更新

    OFFICE_IMPORT_PROJECT = 10012, // 刷新项目
    
    OFFICE_COMPANY_INFO_CHANGE= 10013, // 办公更新公司info
};


/* 以下为CS各通信结构体定义 */

/**界面需要远程重启设备时php将设备信息传给csadpt,php<-->csadapt
 *
 *@param[IN]  szMac -- 相关设备的MAC地址
 *@param[OUT] ret -- csadapt模块返回的错误码，具体见:CSCOMM_ERR_CODE
 */
typedef struct CSP2A_REBOOT_DEVICE_T
{
    char szMac[MAC_SIZE];
    int  nRet;
} CSP2A_REBOOT_DEVICE;

/** 远程请求设备的配置信息,php<-->csadapt
 *
 *@param[IN]  ip_addr -- 需要远程的设备IP地址
 *@param[OUT] ret -- csadapt模块返回的错误码，具体见:CSCOMM_ERR_CODE
 */
typedef struct CSP2A_TO_DEVICE_CONFIGURE_T
{
    char mac[MAC_SIZE];
    //modify by chenyc,2017-06-20,之前value是从界面上保存在value中的,在多用户同时操作时value会被覆盖，修改成从界面上直接将
    //key1=value1;key2=value2... 的形式传过来，nModuleID不再使用
    //int  module_id;
    char config_pair[CONFIGPAIR_SIZE];
    int  ret;
} CSP2A_TO_DEVICE_CONFIGURE;


/** 远程请求设备的配置信息,php<-->csadapt
 *
 *@param[IN]  ip_addr -- 需要远程的设备IP地址
 *@param[OUT] ret -- csadapt模块返回的错误码，具体见:CSCOMM_ERR_CODE
 */
typedef struct CSP2A_FROM_DEVICE_CONFIGURE_T
{
    char mac[MAC_SIZE];
    int  module_id;
    int  ret;
} CSP2A_FROM_DEVICE_CONFIGURE;


/**界面增加/修改设备时php将设备信息传给csadpt,php<-->csadapt
 *
 *@param[IN]  community -- 社区编码
 *@param[IN]  mac -- 相关设备的MAC地址
 *@param[IN]  mac_old -- 修改之前设备的MAC地址,只在修改设备时用
 *@param[OUT] ret -- csadapt模块返回的错误码，具体见:CSCOMM_ERR_CODE
 */
typedef struct CSP2A_DEVICE_INFO_T
{
    char community[COMMUNITY_SIZE];
    char mac[MAC_SIZE];
    char mac_old[MAC_SIZE];
    int  ret;
} CSP2A_DEVICE_INFO;

/* Begin added by chenyc,2017-05-03,多用户功能开发 */

//用户等级枚举
typedef enum CSCOMM_USER_CODE_T
{
    CSCOMM_USER_ADMIN = 0,                  /* 超级管理员admin */
    CSCOMM_USER_SENIOR = 1,                 /* 高级管理员 */
    CSCOMM_USER_JUNIOR = 2,                 /* 初级管理员 */
    CSCOMM_USER_PROPERTY = 3,               /* 物业管理员 */
    CSCOMM_USER_OWNER = 4,                  /* 业主/一般用户,不具备管理员权限 */

} CSCOMM_USER_CODE;


/**界面添加用户时php将设备信息传给csadapt,php<-->csadapt
 *
 *@param[IN]  parent_user -- 父用户编码
 *@param[IN]  new_user --  新添加的用户编码
 *@param[IN]  user_grade -- 新添加的用户等级
 *@param[OUT] ret -- csadapt模块返回的错误码，具体见:CSCOMM_ERR_CODE
 */
typedef struct CSP2A_USER_INFO_T
{
    char parent_user[USER_SIZE];
    char new_user[USER_SIZE];
    int  user_grade;
    int  ret;
} CSP2A_USER_INFO;

/* End added by chenyc,2017-05-03,多用户功能开发 */

/**界面需要刷新设备配置信息文件时php将设备信息传给csadpt,php<-->csadapt
 *
 *@param[IN]  user -- 用户名 (第三版本就是社区)
 */
typedef struct CSP2A_UPDATE_USERINFO_T
{
    char user[USER_SIZE];
} CSP2A_UPDATE_USERINFO;

/**界面需要刷新设备配置信息文件时php将设备信息传给csadpt,php<-->csadapt
 *
 *@param[IN]  commuity -- 社区
 *@param[IN]  type --  操作类型  csadapt::UPDATE_ADD_COMM
 */
typedef struct CSP2A_UPDATE_COMMUITY_T
{
    char commuity[USER_SIZE];
    int type;
} CSP2A_UPDATE_COMMUITY;

/**界面修改app配置信息时,php将设备信息传给csadpt,php<-->csadapt
 *
 *@param[IN]  community -- APP所在社区账号
 *@param[IN]  area_node --  APP所在地址节点
 *@param[IN] bindcode -- APP所绑定的绑定码
 *@param[IN] app_no --     APP的序列号
 */
typedef struct CSP2A_APP_INFO_T
{
    char community[COMMUNITY_SIZE];
    char area_node[AREA_NODE_SIZE];
    char bindcode[BIND_CODE_SIZE];
    uint32_t app_no;
} CSP2A_APP_INFO;

/**界面增删改privatekey/rfidkey的信息之后,csadapt进行配置文件的刷新以及通知csmain去下发设备重新上报状态的通知
 *
 *@param[IN]  community -- APP所在社区账号
 *@param[IN]  dev_access -- privatekey/rfidkey所有能匹配的设备,例如:1-1,1.1.1-1,*******-1,(设备之间以<,>分割)
 */
typedef struct CSP2A_KEY_ACCESS_INFO_T
{
    char community[COMMUNITY_SIZE];
    char dev_access[DEV_ACCESS_SIZE];
} CSP2A_KEY_ACCESS_INFO;

/**界面或者app请求重置密码时,发送邮件通知用户
 *
 *@param[IN]  szUser -- 个人终端账号
 *@param[IN]  szEmail -- 个人终端账号邮箱
 */
typedef struct CSP2A_USER_EAMIL_INFO_T
{
#define DOMAIN_SIZE 64
    char szWebIP[DOMAIN_SIZE]; //部署系统web/vbell所在的服务器IP
    char szUser[USER_SIZE];
    char szEmail[EMAIL_SIZE];
    char szToken[TOKEN_SIZE];
    char szRoleType[USER_SIZE];
} CSP2A_USER_EAMIL_INFO;


/**创建时,发送邮件通知用户
 *
 *@param[IN]  user -- 个人终端账号
 *@param[IN]  user -- 个人终端账号
 *@param[IN]  email -- 个人终端账号邮箱
 *@param[IN]  QRCode_url -- 二维码图片的url
 */
typedef struct CSP2A_USER_CREATE_INFO_T
{
#define USER_QRCODE_BODY_SIZE 2048 //图片的base64 不能传不是base64的因为，推送服务器解析不了全部内容
    char szUser[USER_SIZE];
    char user_uid[USER_SIZE];
    char szPwd[PASSWORD_SIZE];
    char szEmail[EMAIL_SIZE];
    char szQRCodeBody[USER_QRCODE_BODY_SIZE];
    char szQRCodeUrl[URL_SIZE];
    char szServerWebUrl[URL_SIZE];
    char community[LOCATION_SIZE];
    int is_fake;
} CSP2A_USER_CREATE_INFO;



/**界面需要刷新设备配置信息文件时php将设备信息传给csadpt,php<-->csadapt
 *
 *@param[IN]  user -- 主账号
 *@param[IN]  type -- 增删动作  csadapt::UPDATE_ADD_USER
 */
typedef struct CSP2A_UPDATE_USER_T
{
    char user[USER_SIZE];
    int type;
} CSP2A_UPDATE_USER;

/**个人终端用户,客户端请求修改同一联动单元的设备或者app的配置信息
 *
 *@param[IN]  szNode -- 个人终端联动单元
 */
typedef struct CSP2A_PERSONNAL_UPDATE_NODE_T
{
    char szNode[USER_SIZE];
    int nUpdateType;// enum WebUpdateType
} CSP2A_PERSONNAL_UPDATE_NODE;




/**界面发送设备更新请求
 *
*@param[IN]  manager_account_id    -- 社区管理员id
*@param[IN]  unit_id          -- 社区单元id
*@param[IN]  account_id       --社区个人终端id 联动
*@param[IN]  nUpdateDevType   --更新的设备类型 1个人 2公共 3单元公共
*@param[IN]  nUpdateType      -- 更新的类型 1rfcard 2tmpkey 3privatekey 4设备(增删改)
 */

typedef struct CSP2A_COMMUNITY_UPDATE_NODE_T
{
    char node[USER_SIZE];//个人账号，就是联动
    char mac[32]; //社区公共设备更新提醒，因为目前社区设备，只需要通知自己
    int manager_account_id;//社区管理员id
    int unit_id; //单元id
    int nUpdateDevType;//更新设备的类型
    int change_type;//0=web 1=ipchange 2=upgrade 3网页单独更新autop
    //uint32_t nUpdateType;// enum WebUpdateType
} CSP2A_COMMUNITY_UPDATE_NODE;




enum WebUpdateType
{
    WEB_UPDATE_RFCARD = 1,
    WEB_UPDATE_TMP_KEY = 2,
    WEB_UPDATE_PRIVATE_KEY = 3,
    WEB_UPDATE_CONTACT = 4,
    WEB_UPDATE_CONFIG = 5,
};

enum WebUpdateDevType //
{
    WEB_UPDATE_DEV_TYPE_COMMUNITY_PER = 1,
    WEB_UPDATE_DEV_TYPE_COMMUNITY_PUBLIC = 2,
    WEB_UPDATE_DEV_TYPE_COMMUNITY_PUBLIC_UNIT = 3,
    WEB_UPDATE_DEV_TYPE_COMMUNITY_ALL = 4,
};

/**界面需要刷新设备配置信息文件时php将设备信息传给csadpt,php<-->csadapt
 *
 *@param[IN]  mng_account_id -- 社区管理员id
 *@param[IN]  unit_id -- 社区单元id
 *@param[IN]  account_id --社区个人终端id
 *@param[IN]  type -- 增删动作  enum WebUpdateUserType
 */
typedef struct CSP2A_UPDATE_COMMUNITY_USER_T
{
    char node[USER_SIZE];//个人账号，就是联动
    int mng_account_id;//社区管理员id
    int unit_id; //单元id
    int type; //enum WebUpdateUserType
} CSP2A_UPDATE_COMMUNITY_USER;

enum WebUpdateUserType
{
    WEB_UPDATE_USER_TYPE_COMMUNITY_PER_ADD = 1,
    WEB_UPDATE_USER_TYPE_COMMUNITY_PER_DEL = 2,
    WEB_UPDATE_USER_TYPE_COMMUNITY_UNIT_ADD = 3,
    WEB_UPDATE_USER_TYPE_COMMUNITY_UNIT_DEL = 4,
};


//管理员设置sip类型
enum DevMngSipType
{
    DevMngSipType_UDP = 0,
    DevMngSipType_TCP = 1,
    DevMngSipType_TLS = 2,
    DevMngSipType_NONE = 3,
};

//管理员设置sip类型
enum AccountFlags
{
    FLAGS_RTP_CONFUSE = 1 << 0,
    FLAGS_COMMUNITY_CONTACT = 1 << 4,   //户户通开关
};



//个人终端告警处理结构体
typedef struct CSP2A_PERSONNAL_DEAL_ALARM_T
{
#define ALARM_ID_SIZE            16
#define ALARM_RESULT_SIZE        64
    char szAreaNode[AREA_NODE_SIZE]; //32
    char szUser[USER_SIZE]; //32
    char szAlarmID[ALARM_ID_SIZE];
    char szResult[ALARM_RESULT_SIZE];
} CSP2A_PERSONNAL_DEAL_ALARM;


/**个人终端管理员,界面增加/修改设备时php将设备信息传给csadpt,php<-->csadapt
 *
 *@param[IN]  mac -- 相关设备的MAC地址
 *@param[IN]  mac_old -- 修改之前设备的MAC地址,只在修改设备时用
 */
typedef struct CSP2A_PERSONAL_DEVICE_INFO_T
{
    char mac[MAC_SIZE];
    char mac_old[MAC_SIZE];
} CSP2A_PERSONAL_DEVICE_INFO;

/**个人终端用户,界面上删除设备
 *
 *@param[IN]  mac -- 相关设备的MAC地址
 */
typedef struct CSP2A_PER_DEL_DEVICE_T
{
    char mac[MAC_SIZE];//单个删除
} CSP2A_PER_DEL_DEVICE;

/**个人终端用户,界面上删除联动系统账号
 *
 *@param[IN]  uids -- 相关账号的uid
 */
typedef struct CSP2A_PER_DEL_UID_T
{
    char uids[USER_SIZE * 40];//可以批量删除用户 add chenzhx 20180410
} CSP2A_PER_DEL_UID;


/**个人终端用户,app上删除图片
 *
 *@param[IN]  szPicUrl -- 相关图片的url
 */
typedef struct CSP2A_PER_DEL_PIC_T
{
    char szPicUrl[URL_SIZE * 40];
} CSP2A_PER_DEL_PIC;



namespace csconfig
{
enum UpdateUserTYpe
{
    UPDATE_ADD_USER = 0,
    UPDATE_DEL_USER,
};

enum AddDelCommuityTYpe
{
    UPDATE_ADD_COMM = 0,
    UPDATE_DEL_COMM,
};

}

//社区告警处理结构体
typedef struct CSP2A_COMMUNITY_DEAL_ALARM_T
{
#define ALARM_ID_SIZE            16
#define ALARM_RESULT_SIZE        64
    char szAreaNode[AREA_NODE_SIZE]; //32
    char szUser[USER_SIZE]; //32
    char szAlarmID[ALARM_ID_SIZE];
    char szResult[ALARM_RESULT_SIZE];
} CSP2A_COMMUNITY_DEAL_ALARM;

//办公告警处理结构体
typedef struct CSP2A_OFFICE_DEAL_ALARM_T
{
#define ALARM_ID_SIZE            16
#define ALARM_RESULT_SIZE        64
    char szAreaNode[AREA_NODE_SIZE]; //32
    char szUser[USER_SIZE]; //32
    char szAlarmID[ALARM_ID_SIZE];
    char szResult[ALARM_RESULT_SIZE];
} CSP2A_OFFICE_DEAL_ALARM;

/**用户自己注册主账号,发送验证码到邮箱
 *
 *@param[IN]  szCode -- 检验码
 *@param[IN]  szEmail -- 账号邮箱
 */
typedef struct CSP2A_SEND_CHECK_CODE_T
{
#define CHECK_CODE_SIZE 16
    char szCheckCode[CHECK_CODE_SIZE];
    char szEmail[EMAIL_SIZE];
    char szLanguage[8];
} CSP2A_SEND_CHECK_CODE;



//设备/app过期
enum DevAppExpireType
{
    DEV_APP_EXPIRE_TYPE_APP = 1,
    DEV_APP_EXPIRE_TYPE_PER_DEV = 2,
    DEV_APP_EXPIRE_TYPE_COMM_DEV = 3,
};

typedef struct CSP2A_DEV_APP_EXPIRE_T
{
    char szUid[16];
    char szUserName[128];//昵称,发邮件用
    char szEmail[64];
    char community[32];
    int nBefore;    //提前多少天的通知
    int subscription; //月费
} CSP2A_DEV_APP_EXPIRE;

typedef struct CSP2A_DEV_APP_WILLBE_EXPIRE_T
{
    char szUid[16];
    char szUserName[128];//昵称
    char szEmail[64];
    char community[32];
} CSP2A_DEV_APP_WILLBE_EXPIRE;


typedef struct CSP2A_INSTALLER_EXPIRE_T
{
    char szUserName[128];//昵称,发邮件用
    char szEmail[64];
    int  nCount;
    int nBefore;    //提前多少天的通知
    char community[32];
    char list[2048];
} CSP2A_INSTALLER_EXPIRE;


typedef struct CSP2A_PM_EXPIRE_T
{
    char username[128];//昵称,发邮件用
    char email[64];
    int  ncount;
    int nbefore;    //提前多少天的通知
    char community[32];
    char list[2048];
} CSP2A_PM_EXPIRE;



typedef struct CSP2A_FREE_TRIAL_WILLBE_EXPIRE_T
{
    char server_web_url[32];
    char node_name[32];
    char email[64];
    int type;//1个人  2社区
} CSP2A_FREE_TRIAL_WILLBE_EXPIRE;


typedef struct CSP2A_DEV_NOT_EXPIRE_T
{
    char szUids[512];
    char szMacs[512];
    char szNode[32];
    int nType;//1个人 2社区
} CSP2A_DEV_NOT_EXPIRE;


typedef struct CSP2A_DEV_CLEAN_DEVICE_CODE_T
{
    char szMacs[128];
} CSP2A_DEV_CLEAN_DEVICE_CODE;


enum CONFIG_FILE_CHANGE_NOTIFY_TYPE
{
    CONFIG_FILE_CHANGE_NOTIFY_TYPE_NODE = 1,
    CONFIG_FILE_CHANGE_NOTIFY_TYPE_MAC = 2,
    CONFIG_FILE_CHANGE_NOTIFY_TYPE_COMMUNITY = 3,//整个社区更新
    CONFIG_FILE_CHANGE_NOTIFY_TYPE_UNIT = 4,//整个单元更新
    CONFIG_FILE_CHANGE_NOTIFY_TYPE_PUB_DEV = 5,//公共设备
    CONFIG_FILE_CHANGE_NOTIFY_TYPE_UNIT_DEV = 6,//单元设备
    CONFIG_FILE_CHANGE_NOTIFY_TYPE_NODE_ONLY = 7,//只需要通知节点，不需要通知unit pub
    CONFIG_FILE_CHANGE_NOTIFY_TYPE_ALL_PUB = 8,//最外层+所有楼栋的公共设备    
};

enum DEV_FILE_CHANGE_NOTIFY_TYPE
{
    DEV_FILE_CHANGE_NOTIFY_USER_INFO = 1,
};



#define CONFIG_FILE_CHANGE_TYPE_CONTACT  (1<<0)
#define CONFIG_FILE_CHANGE_TYPE_CONFIG   (1<<1)
#define CONFIG_FILE_CHANGE_TYPE_RF       (1<<2)
#define CONFIG_FILE_CHANGE_TYPE_PRV      (1<<3)




//csmain to csadapt
//csmain 某个mac ip 变化 通知生成contactlist
typedef struct CSC2A_REWIRTE_CONTACTLIST_T
{
    char mac[MAC_SIZE];
} CSC2A_REWIRTE_CONTACTLIST;


typedef struct CSC2A_REWIRTE_CONFIGFILE_T
{
    char mac[MAC_SIZE];
} CSC2A_REWIRTE_CONFIGFILE;

typedef struct CSP2A_DEV_DEL_PER_PUBLIC_VIRT_ACCOUNT_T
{
    char account[64];
} CSP2A_DEV_DEL_PER_PUBLIC_VIRT_ACCOUNT;

//begin added by chenyc,2018-08-27,for 视频存储
typedef struct CSP2A_ADD_VIDEO_STORAGE_SCHED_T
{
    uint32_t id;
    uint32_t sched_type;
    uint32_t date_flag;
    char mac[16];
    char begin_time[24];//HH:MM:SS or YYYY-MM-DD HH:MM:SS
    char end_time[24];//HH:MM:SS or YYYY-MM-DD HH:MM:SS
} CSP2A_ADD_VIDEO_STORAGE_SCHED;

typedef struct CSP2A_DEL_VIDEO_STORAGE_SCHED_T
{
    uint32_t id;
    uint32_t sched_type;
    char mac[16];
} CSP2A_DEL_VIDEO_STORAGE_SCHED;

typedef struct CSP2A_DEL_VIDEO_STORAGE_T
{
    uint32_t video_id;
} CSP2A_DEL_VIDEO_STORAGE;



/**界面发送更新请求
 *
*@param[IN]  mac
*@param[IN]  mng_account_id -- 社区管理员id
*@param[IN]  unit_id -- 社区单元id
*@param[IN]  update_dev_type --更新的设备类型 1个人 2公共 3单元公共
 */

typedef struct CSP2A_COMMUNITY_PUB_UPDATE_KEY_T
{
    //php 只需要传mac
    char mac[1024]; //社区公共设备更新提醒，因为目前社区设备，只需要通知自己；用分割隔开。注意从有到无的变化
    int mng_account_id;//社区管理员id
    int unit_id; //单元id
    int update_dev_type;//更新设备的类型
} CSP2A_COMMUNITY_PUB_UPDATE_KEY;

/**
 *
 *@param[IN]  macid -- 表的ID
 *@param[IN]  nIsPer -- 是否是个人
 */
typedef struct CSP2A_DEVICE_CHANGE_INFO_T
{
    int nMacid;
    int nIsPer;
    char szMac[16];
} CSP2A_DEVICE_CHANGE_INFO;



/**
 *
 *@param[IN]  nActive --1 激活 0不激活
 *@param[IN]  szUserName -- 用户名
 *@param[IN]  subscription -- 1收月费 0不收月费
 */
typedef struct CSP2A_ACCOUNT_ACTIVE_INFO_T
{
    int nActive;
    char szUserName[64];
    char szEmail[EMAIL_SIZE];
    char szServerWebUrl[URL_SIZE];
    char expire_time[32];
    int subscription;
    char account[32];
} CSP2A_ACCOUNT_ACTIVE_INFO;




typedef struct CSP2A_SHARE_TEMKEY_INFO_T
{
    char szTmpKey[32];
    char szEmail[EMAIL_SIZE];
    char szMsg[VALUE_SIZE];
    char szCountOrEvery[VALUE_SIZE];
    char szStartTime[VALUE_SIZE];
    char szStopTime[VALUE_SIZE];
    char szQRCodeBody[USER_QRCODE_BODY_SIZE];
    char szWebUrl[URL_SIZE];
    char szLanguage[8];
    int  mng_id;
} CSP2A_SHARE_TEMKEY_INFO;




/**
 *
 *@param[IN]  szTmpKey -- app tmpkey
 */
typedef struct CSP2A_REMOTE_OPENDDOR_INFO_T
{
    char mac[32];
    char uid[32];
    char trace_id[64]; //室内机开门mac拼接traceid,长度32不够
    char repost_mac[32]; // 用于转发开门的mac
    int relay;//开第几个门0-2
} CSP2A_REMOTE_OPENDDOR_INFO;



/**
 *
 *@param[IN]
 */
typedef struct CSP2A_CREATE_PROPERTY_WORK_INFO_T
{
    char szEmail[EMAIL_SIZE];
    char szUserName[64];
    char szPassword[64];
    char szServerWebUrl[128];
} CSP2A_CREATE_PROPERTY_WORK_INFO;



/**
 *
 *@param[IN]
 */
typedef struct CSP2A_UPDATE_COMMUNITY_ALL_PUB_DEV_INFO_T
{
    int mng_id;
} CSP2A_UPDATE_COMMUNITY_ALL_PUB_DEV_INFO;


/**
 *
 *@param[IN]
 */
typedef struct CSP2A_COMMUNITY_APT_PIN_CHANGHE_INFO_T
{
    int mng_id;
} CSP2A_COMMUNITY_APT_PIN_CHANGHE_INFO;

/**
 *
 *@param[IN]
 */
typedef struct CSP2A_UPDATE_DEV_CONFIG_INFO_T
{
    char mac[32];
} CSP2A_UPDATE_DEV_CONFIG_INFO;

/**
 *
 *@param[IN]
 */
typedef struct CSP2A_END_USER_T
{
    char szUid[256];//最多同时16*16,多于该数值由php保证进行切割
} CSP2A_END_USER;

/**
 *
 *@param[IN]
 */
typedef struct CSP2A_UID_RENEW_SRV_INFO_T
{
    char szUserName[128];
    char szEmail[64];
    char szTime[64];
    int  type;//type=0 账号续费; type=1 落地续费
} CSP2A_UID_RENEW_SRV_INFO;


/**
 *
 *@param[IN]
 */
typedef struct CSP2A_PM_RENEW_SRV_INFO_T
{
    char account[32];
    char username[128];
    char email[64];
    char time[64];
    int  type;//type=0 账号续费; type=1 落地续费
} CSP2A_UID_PM_RENEW_SRV_INFO;

/**
 *
 *@param[IN]  单个PM的信息
 */
typedef struct CSP2A_PM_INFO_T
{
    char szCommunity[128];
    char szEmail[64];
    char szName[128];//pm的name
    int nAccountNum;
    int nBefore;    //提前多少天的通知
    char list[2048];
} CSP2A_PM_INFO;



typedef struct CSP2A_ALEXA_SET_ARMING_INFO_T
{
    char mac[32];
    int mode;
} CSP2A_ALEXA_SET_ARMING_INFO;

typedef struct CSP2A_ALEXA_LOGIN_INFO_T
{
    char node[32];
} CSP2A_ALEXA_LOGIN_INFO;


typedef struct CSP2A_CREATE_REMOTE_DEV_CONTORL_INFO_T
{
    char user[128];
    char password[128];
    char mac[16];
    int port;
    char ssh_proxy_domain[128];
} CSP2A_CREATE_REMOTE_DEV_CONTORL_INFO;

typedef struct CSP2A_CONFIG_FILE_CHANGE_T
{
    int  type;//enum CONFIG_FILE_CHANGE_TYPE
    int  nNotifyType;//enum CONFIG_FILE_CHANGE_NOTIFY_TYPE
    uint32_t  mng_id;
    uint32_t  unit_id;
    char mac[MAC_SIZE];
    char node[USER_SIZE];
} CSP2A_CONFIG_FILE_CHANGE;

typedef struct CSP2A_DEV_FILE_CHANGE_T
{
    int  type;//enum CONFIG_FILE_CHANGE_NOTIFY_TYPE
    DULONG traceid;
    char mac[MAC_SIZE];
    char file_path[256];
    char file_md5[MD5_SIZE];
} CSP2A_DEV_FILE_CHANGE;



enum REFRESH_CACHE_TYPE
{
    DELETE_USER,
    MODIFY_DEV,
};

typedef struct CSP2A_REFRESH_CACHE_T
{
    int  type;
    char node[64];
    char mac[64];
} CSP2A_REFRESH_CACHE;

typedef struct CSP2A_PER_ADD_NEWSITE_T
{
    char userinfo_uuid[64];
    char name[128];
    char project_name[128];
    char apt_num[64];
    char email[64];
    int role;
    char send_type[64];
} CSP2A_PER_ADD_NEWSITE;

typedef struct CSP2A_PM_LINK_NEWSITES_T
{
    char account_uuid[64];
    char comm_name_list[2048];
    char office_name_list[2048];
    char email[64];
    char name[128];
    int project_type;
} CSP2A_PM_LINK_NEWSITES;

typedef struct CSP2A_SEND_VERFICATION_CODE_T
{
    char account_uuid[64];
    char email[64];
    char mobile_number[25];
    char phone_code[24];
    char name[128];
    char language[32];
    char code[32];
    char type[64];
} CSP2A_SEND_VERFICATION_CODE;




#endif //__CSP2A_COMMON_H__

