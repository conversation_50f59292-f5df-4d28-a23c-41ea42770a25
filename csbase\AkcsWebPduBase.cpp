#include "util.h"
#include "AkcsWebPduBase.h"
#include "AkLogging.h"


CAkcsWebPdu::CAkcsWebPdu()
{
	pdu_header_.packet_len = 0;
	pdu_header_.msg_id = 0;    
	pdu_header_.from = 0;
	pdu_header_.param1 = 0;
	pdu_header_.param2 = 0;
    header_len_ = 20;
}

char* CAkcsWebPdu::GetBuffer()
{
    return (char *)buf_.GetBuffer();
}

uint32_t CAkcsWebPdu::GetLength()
{
    return buf_.GetWriteOffset();
}

char* CAkcsWebPdu::GetBodyData()
{
    return (char *)(buf_.GetBuffer() + header_len_); //消息头不同版本不一定一致,当服务端跟客户端的消息头不一致时
}

uint32_t CAkcsWebPdu::GetBodyLength()
{
    uint32_t body_length = 0;
    body_length = buf_.GetWriteOffset() - header_len_;
    return body_length;
}

int CAkcsWebPdu::ReadPduHeader(char* buf, uint32_t len)
{
	int ret = -1;
	if (len >= sizeof(WebPduHeader_t) && buf) {
		CByteStream is((uchar_t *)buf, len);
        
		is >> pdu_header_.packet_len;  //内部已经做了大小端序的转化
		is >> pdu_header_.msg_id;
		is >> pdu_header_.from;
		is >> pdu_header_.param1;
		is >> pdu_header_.param2;
		ret = 0;
	}

	return ret;
}
//写消息包整体长度的字段
void CAkcsWebPdu::WriteMsgLen()
{
	uchar_t* buf = (uchar_t*)GetBuffer();
	CByteStream::WriteInt32(buf, GetLength());
}

void CAkcsWebPdu::SetMsgID(uint32_t msg_id)
{
	uchar_t* buf = (uchar_t*)GetBuffer();
	CByteStream::WriteUint32(buf + 4, msg_id);
}

void CAkcsWebPdu::SetProjectType(uint32_t type)
{
    uchar_t* buf = (uchar_t*)GetBuffer();
    CByteStream::WriteUint32(buf + 8, type);
}

void CAkcsWebPdu::SetParam1(uint32_t param1)
{
    uchar_t* buf = (uchar_t*)GetBuffer();
    CByteStream::WriteUint32(buf + 12, param1);
}

void CAkcsWebPdu::SetParam2(uint32_t param2)
{
    uchar_t* buf = (uchar_t*)GetBuffer();
    CByteStream::WriteUint32(buf + 16, param2);
}


void CAkcsWebPdu::SetMsgBody(const void* msg_body, uint32_t len)
{
    //设置包体，则需要重置下空间
    buf_.Read(NULL, buf_.GetWriteOffset());
    buf_.Write(NULL, sizeof(WebPduHeader_t)); //写包头
    buf_.Write(msg_body, len);
    WriteMsgLen(); //赋值消息包长度
    
    pdu_header_.param2 = (uint32_t)ThreadLocalSingleton::GetInstance().GetTraceID();

    InitMsgHeader();
}
//protobuf消息
void CAkcsWebPdu::SetMsgBody(const google::protobuf::MessageLite* msg)
{
    //设置包体，则需要重置下空间
    buf_.Read(NULL, buf_.GetWriteOffset());
    buf_.Write(NULL, sizeof(WebPduHeader_t)); //写包头
    uint32_t msg_size = msg->ByteSize();
    uchar_t* szData = new uchar_t[msg_size];
    if (!msg->SerializeToArray(szData, msg_size))
    {
        ::printf("pb msg miss required fields.");
    }
    buf_.Write(szData, msg_size);
    delete[] szData;
    WriteMsgLen(); //赋值消息包长度

    pdu_header_.param2 = (uint32_t)ThreadLocalSingleton::GetInstance().GetTraceID();

    InitMsgHeader();
}
//add by chenzhx 20220114 如果外层没有进行设置,那么默认值就是随机值
void CAkcsWebPdu::InitMsgHeader()
{
    uchar_t* buf = (uchar_t*)GetBuffer();
    CByteStream::WriteUint32(buf + 4, pdu_header_.msg_id);

    buf = (uchar_t*)GetBuffer();
    CByteStream::WriteUint32(buf + 8, pdu_header_.from);

    buf = (uchar_t*)GetBuffer();
    CByteStream::WriteUint32(buf + 12, pdu_header_.param1);//16:累积偏移

    buf = (uchar_t*)GetBuffer();
    CByteStream::WriteUint32(buf + 16, pdu_header_.param2);  
}
