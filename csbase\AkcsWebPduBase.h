/*
 * AkcsWebPduBase.h
 */

#ifndef __AKCS_WEB_BASE_MSG_PDUBASE_H__
#define __AKCS_WEB_BASE_MSG_PDUBASE_H__

#include "UtilPdu.h"
#include "google/protobuf/message_lite.h"

#define IM_PDU_HEADER_LEN		16
#define IM_PDU_VERSION			1

/*
消息格式：
       消息头：length+id+from+param1+param2
       length是总长度(4字节),id消息类型(4字节),from暂时无用(4字节),param1/param2(预留字段,
均为4字节)
       消息体：采用ProtoBuf协议
*/

typedef struct {
    uint32_t packet_len;  
    uint32_t msg_id;
    uint32_t from;//目前当project使用
    uint32_t param1;
    uint32_t param2;
} WebPduHeader_t;

class  CAkcsWebPdu
{
public:
    CAkcsWebPdu();
    virtual ~CAkcsWebPdu() {}
    
    char* <PERSON>Buffer();
    uint32_t GetLength();
    char* GetBodyData();
    uint32_t GetBodyLength();
    
    uint16_t GetMsgID() { return pdu_header_.msg_id; }
    uint32_t GetParam1() { return pdu_header_.param1; }
    uint32_t GetParam2() { return pdu_header_.param2; }
    uint16_t GetProjectType() { return pdu_header_.from; }

    void SetMsgID(uint32_t msgid);
    void SetProjectType(uint32_t from);
    void SetParam1(uint32_t param1);
    void SetParam2(uint32_t param2);
		
    void WriteMsgLen();
    void Write(const char* buf, uint32_t packet_len) { buf_.Write((const void*)buf, packet_len);}//在内层申请了内存,并自己负责释放 
    int ReadPduHeader(char* buf, uint32_t len);
    void SetMsgBody(const void* msg, uint32_t len);
    void SetMsgBody(const google::protobuf::MessageLite* msg);
    
private:
    void InitMsgHeader();
    CSimpleBuffer	buf_;  //消息包: 消息头+消息体
    WebPduHeader_t	    pdu_header_;  //消息头 
    int header_len_;
};

#endif /* __AKCS_BASE_MSG_PDUBASE_H__ */
