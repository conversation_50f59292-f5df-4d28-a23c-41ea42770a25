#ifndef _BASIC_DEFINE_H_
#define _BASIC_DEFINE_H_

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <sys/ioctl.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <time.h>
#include <unistd.h>
#include <net/if.h>
#include <errno.h>
#include <netdb.h>
#include <string>
#include <list>
#include <vector>
#include <map>
#include <iostream>
#include <stdarg.h>

#define IN 
#define INOUT 
#define OUT 

#define LPCWSTR const char *

#define _stprintf_s snprintf
#define _stscanf_s  sscanf
#define _tcslen strlen
#define _tcscmp strcmp

#ifndef __FUNCTIONW__
#define __FUNCTIONW__ __FUNCTION__
#endif

#ifndef _LOG_TRACE_
#define _LOG_TRACE_ __FILE__,__LINE__
#endif

#define AKCSDEF 0

/* 基本数据类型定义 */

#ifndef TRUE
#define TRUE true
#endif

#ifndef FALSE
#define FALSE false
#endif

#ifndef WCHAR
#define WCHAR  char
#endif

#ifndef BOOL
#define BOOL bool
#endif

#ifndef SHORT
#define SHORT short
#endif

#ifndef ULONG
#define ULONG unsigned long
#endif

#ifndef DULONG
#define DULONG unsigned long long
#endif

#ifndef TCHAR
#define TCHAR char
#endif

#ifndef VOID
#define VOID void
#endif

#define NTOHL   ntohl


/* 错误信息定义 */
#define ERR_COMM_SUCCEED        0  //成功

/* 函数入参错误*/
#define ERR_PARAMETER_NULL       1

/* 数据操作错误定义 */
#define ERR_DAO_INIT       11 //数据库初始化失败
#define ERR_DAO_RECONN     12
#define ERR_DAO_CONN       13 //数据库连接失败

/* 以下为配置文件操作错误 */
#define ERR_CONF_FILE_NOTEXIST      50 /*配置文件名不存在*/
#define ERR_CONF_SECTION_NOTEXIST    51 /*节名不存在*/
#define ERR_CONF_KEYNAME_NOTEXIST      52 /*键名不存在*/
#define ERR_CONF_STRINGLEN_NOTEQUAL     53 /*两个字符串长度不同*/
#define ERR_CONF_STRING_NOTEQUAL       54 /*两个字符串内容不相同*/
#define ERR_CONF_STRING_EQUAL        55 /*两个字符串内容相同*/

/* socket 操作结果*/
#define ERR_SOCK_UDP_SEDN       100 /*两个字符串内容相同*/


/* 系统调用失败*/
#define ERR_SYS_NEW             200
#define ERR_SYS_MALLOC            201


#define ERR_COMM_FAIL           0xffffffff


#define CUR_TIME_LEN    24
#define LOG_MAX_LEN     1024  

#define LOG_PATH_LEN    64
#define COMMON_STR_LEN 64


//TIMER���
#define TIMER_ID_BASE				0
#define TIMER_ID_UPDATE_DEVICE		1
#define TIMER_ID_HEAET_BIT			2
#define TIMER_ID_UPDATE_PROCESS		3
#define TIMER_ID_SECOND				4
#define TIMER_ID_LOCKSCREEN			5
#define TIMER_VAL_SECOND			1000
#define TIMER_VAL_BASE				1000
#define TIMER_VAL_UPDATE_DEVICE		2000
#define TIMER_VAL_UPDATE_PROCESS	500
#define TIMER_VAL_LOCK_SCREEN		20000

#define TIMER_ID_REFRESH	1
#define TIMER_VAL_REFRESH	1000

//ͨ�õĿ���������/ʱ��ģʽ ��ȡ��ʽ
#define FORMATE_GET_DATE_FROM_INT	"%04d-%02d-%02d"
#define FORMATE_GET_TIME_FROM_INT	"%02d:%02d:%02d"
#define FORMATE_GET_DATE_TIME_FROM_INT	"%04d-%02d-%02d %02d:%02d:%02d"
#define FORMATE_SET_DATE_USDP	"yyyy-MM-dd"
#define FORMATE_SET_TIME_USDP	"HH:mm:ss"
#define FORMATE_SET_DATE_TIME_USDP	"yyyy-MM-dd HH:mm:ss"
#define FORMATE_GET_DATE_FROM_TIME_DATA	"%Y-%m-%d"
#define FORMATE_GET_TIME_FROM_TIME_DATA	"%H:%M:%S"
#define FORMATE_GET_DATE_TIME_FROM_TIME_DATA	"%Y-%m-%d %H:%M:%S"


#ifndef MAC_SIZE
#define MAC_SIZE                 20
#endif

#ifndef PIC_NAME_SIZE
#define PIC_NAME_SIZE            256
#endif

#ifndef CAPTURE_ACTION_SIZE
#define CAPTURE_ACTION_SIZE             64
#endif

#ifndef CAPTURE_INITIATOR_SIZE
#define CAPTURE_INITIATOR_SIZE          128
#endif

#ifndef CAPTURE_INITIATOR_SIZE
#define CAPTURE_INITIATOR_SIZE          128
#endif

//账号名长度
#ifndef USER_SIZE
#define USER_SIZE                       32
#endif

//个人终端用户设备location
#ifndef LOCATION_SIZE
#define LOCATION_SIZE                   64
#endif

#ifndef SIP_SIZE
#define SIP_SIZE                        32
#endif

#ifndef DEV_LOCATION_SIZE
#define DEV_LOCATION_SIZE                   64
#endif

#ifndef IP_SIZE
#define IP_SIZE                         16
#endif

#ifndef IPV6_SIZE
#define IPV6_SIZE                       40
#endif

#ifndef MAC_SIZE
#define MAC_SIZE                        20
#endif

#ifndef VALUE_SIZE
#define VALUE_SIZE                      64
#endif

#ifndef INT_SIZE
#define INT_SIZE                        12
#endif

#ifndef DATETIME_SIZE
#define DATETIME_SIZE                   24
#endif

#ifndef MD5_SIZE
#define MD5_SIZE                        36
#endif

#ifndef URL_SIZE
#define URL_SIZE                        256
#endif

//账号名长度
#ifndef USER_SIZE
#define USER_SIZE                       32
#endif

//账号名/邮箱长度
#ifndef USER_EMAIL_SIZE
#define USER_EMAIL_SIZE                 64
#endif


//个人终端用户联动系统
#ifndef NODE_SIZE
#define NODE_SIZE                       32
#endif

#ifndef UUID_SIZE
#define UUID_SIZE                       36
#endif

//个人终端用户设备location
#ifndef LOCATION_SIZE
#define LOCATION_SIZE                   64
#endif

#ifndef MSG_SEQ_SIZE
#define MSG_SEQ_SIZE                    16
#endif

#ifndef SIP_SIZE
#define SIP_SIZE                        32
#endif

#ifndef RTSP_PWD_SIZE
#define RTSP_PWD_SIZE                   64
#endif

//app端外推送的token
#ifndef TOKEN_SIZE
#define TOKEN_SIZE                     256   //fcm token 152
#endif

#define EMAIL_SIZE                      64
#define PASSWORD_SIZE                   64

#define VERSION_1_0                     1
#define VERSION_2_0                     2

#define D_CLIENT_VERSION_1_0            1 //处理下载联系人用文件方式
#define D_CLIENT_VERSION_4400           4400 //V4.4
#define D_CLIENT_VERSION_4600           4600 //V4.6
#define D_CLIENT_VERSION_5000           5000 //V5.0
#define D_CLIENT_VERSION_5200           5200 //V5.2
#define D_CLIENT_VERSION_5300           5300 //V5.3
#define D_CLIENT_VERSION_5400           5400 //V5.4
#define D_CLIENT_VERSION_6000           6000 //V6.0
#define D_CLIENT_VERSION_6100           6100 //V6.1
#define D_CLIENT_VERSION_6200           6200 //V6.2
#define D_CLIENT_VERSION_6400           6400 //V6.4
#define D_CLIENT_VERSION_6500           6500 //V6.5
#define D_CLIENT_VERSION_6520           6500 //V6.5.2
#define D_CLIENT_VERSION_6533           6533 //三方摄像头室内机稳定分支
#define D_CLIENT_VERSION_6542           6542 //三方摄像头话机稳定分支



#define PROTOCAL_SIZE                   16
#define DEVICE_ID_SIZE                  24
#define DEVICE_TYPE_SIZE                24
#define DEVICE_STATUS_SIZE              16
#define DEVICE_SWVER_SIZE               16
#define DEVICE_HWVER_SIZE               32

#define KEY_TYPE_SIZE                   24
#define KEY_VALUE_SIZE                  32

#define STRING_VALUE_SIZE               1024
#define DATA_SIZE                       4096

#define SHOW_TIME                       32
#define MAX_PIC_NUM                     8
#define MAX_URL_LEN                     256
#define COMMUNITY_SIZE                  32    //社区编码长度
#define MSG_ID_LEN                      16
#define CAPTURE_LOG_SIZE                64
#define CAPTURE_PIC_SIZE                256
//#define NODE_SIZE                     16
#define ARMING_ACTION_SIZE              8
#define PIC_NAME_SIZE                   256
#define CAPTURE_ACTION_SIZE             64
#define CAPTURE_INITIATOR_SIZE          128
#define VIDEO_ACTION_SIZE               64
#define WEATHER_EXPIRE_SECOND           1800

#ifndef _T
#define _T(str) str
#endif

#define PROTOCAL_NAME_DEFAULT           _T("1.0")

#define DCLI_OFFLINE_CHECK_VER          4600

#define OFFLINE_ALARM_TYPE_NAME_DEFAULT           _T("alarm")
#define OFFLINE_ALARM_TYPE_CONTENT_DEFAULT        _T("Device Offline")

#define SOCKET_MSG_MAGIC_MSB        0xBC
#define SOCKET_MSG_MAGIC_LSB        0xDE
#define SOCKET_MSG_ID_MASK          0x07FF
#define SOCKET_MSG_VERSION_MASK     0x3800
#define SOCKET_MSG_VERSION_OFFSET   11

#define SOCKET_MSG_VERSION_01       (1<<SOCKET_MSG_VERSION_OFFSET) //不加密的
#define SOCKET_MSG_VERSION_02       (2<<SOCKET_MSG_VERSION_OFFSET) //加密的

#define PUD_HEADER_SIZE     34
#define CS_COMMON_MSG_HEADER_SIZE     20
//兼容6.3数据分析网页数据收集过长的问题，改为1M，后续在funcs做过滤
#define CS_COMMON_MSG_DATA_SIZE       1024000 /*v4.0 2014->4096为了传qr的图片*/
#define CS_COMMON_MSG_MAX_SIZE        (CS_COMMON_MSG_DATA_SIZE + CS_COMMON_MSG_HEADER_SIZE)
#define CS_COMMON_MSG_AUTOP_SIZE      2048


#define TEST_API_PRIVATE_MD5_KEY "@$AKuVox%#25";   //测试接口内部校验的aes固定串


#endif /* _BASIC_DEFINE_H_ */
