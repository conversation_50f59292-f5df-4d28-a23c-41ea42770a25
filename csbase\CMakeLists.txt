cmake_minimum_required(VERSION 2.6)
PROJECT(Base)

AUX_SOURCE_DIRECTORY(./ SRC_LIST1)
#AUX_SOURCE_DIRECTORY(./redis SRC_LIST2)
AUX_SOURCE_DIRECTORY(./jsoncpp0.5/src/json SRC_JSON)
AUX_SOURCE_DIRECTORY(./Tinyxml SRC_XML)
AUX_SOURCE_DIRECTORY(./Character SRC_CHARACTER)
AUX_SOURCE_DIRECTORY(./timeticker SRC_TIMETICKER)
SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11")

SET(LIBRARY_OUTPUT_PATH ./)
#SET(EXECUTABLE_OUTPUT_PATH  ./bin)

ADD_DEFINITIONS( -std=c++11 -shared -fPIC -g -W -Wall -D_REENTRANT -D_FILE_OFFSET_BITS=64 -DAC_HAS_INFO
-DAC_HAS_WARNING -DAC_HAS_ERROR -DAC_HAS_CRITICAL -DTIXML_USE_STL
-DAC_HAS_DEBUG -DLINUX_DAEMON)

INCLUDE_DIRECTORIES(./ ./redis ./jsoncpp0.5/include ./evpp /usr/local/boost/include /usr/local/protobuf/include /usr/local/grpc/include ./Tinyxml ./Character ./timeticker)

ADD_LIBRARY(csbase STATIC ${SRC_LIST1} ${SRC_JSON} ${SRC_XML} ${SRC_CHARACTER} ${SRC_TIMETICKER})  #编成静态库

#ADD_XXXX必须在TARGET_LINK_LIBRARIES前面，否则会报错
#ADD_LIBRARY(${PROJECTNAME} SHARED ${SRC_LIST})
#ADD_EXECUTABLE(${PROJECTNAME} ${SRC_LIST})

TARGET_LINK_LIBRARIES(csbase pthread hiredis curl glog)
