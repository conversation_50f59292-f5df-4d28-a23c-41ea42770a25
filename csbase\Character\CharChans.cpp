#include "SysEnv.h"
#include "BasicDefine.h"
#include "CharChans.h"
#include <string.h>

int TransUtf8ToTchar(const char *pszSrc, TCHAR *pszDst, int nDstSize)
{
	if (pszSrc == NULL
		|| pszDst == NULL
		|| nDstSize <= 0)
	{
		return -1;
	}

	strncpy(pszDst, pszSrc, nDstSize - 1);
	pszDst[nDstSize - 1] = '\0';
	return 0;
}

int TransTcharToUtf8(const TCHAR *pszSrc, char *pszDst, int nDstSize)
{
	if (pszSrc == NULL
		|| pszDst == NULL
		|| nDstSize <= 0)
	{
		return -1;
	}
	int nSrcLen = strlen(pszSrc) + 1;
	char *pTmpSrc = new(std::nothrow) char[nSrcLen];
    if (pTmpSrc == NULL)
    {
		return -1;
    }
    memset(pTmpSrc, 0, nSrcLen);
    strncpy(pTmpSrc, pszSrc, strlen(pszSrc));
    
	int nRet = AKCS_G2U(pTmpSrc,strlen(pszSrc),pszDst,(size_t)nDstSize);
    delete[] pTmpSrc;

    return nRet;
}
char* _tcscpy_s(char *pszDst, uint32_t nsize, const char *pszSrc)
{
	strncpy(pszDst, pszSrc, nsize);
    pszDst[nsize - 1] = 0;
	
	return pszDst;
}

char * strcat_s(char *dest, size_t n, const char *src)
{
	return strncat(dest, src, n);
}


