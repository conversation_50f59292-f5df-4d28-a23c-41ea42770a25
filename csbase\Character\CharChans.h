#ifndef   _CHAR_CHANS_H_
#define   _CHAR_CHANS_H_

#ifdef __cplusplus
extern "C" {
#endif

#ifndef sprintf_s
#define sprintf_s snprintf
#endif

int TransUtf8ToTchar(const char *pszSrc, TCHAR *pszDst, int nDstSize);
char* _tcscpy_s(char *pszDst, uint32_t nsize, const char *pszSrc);
int TransTcharToUtf8(const TCHAR *pszSrc, char *pszDst, int nDstSize);
char * strcat_s(char *dest, size_t n, const char *src);

#ifdef __cplusplus
}
#endif

#endif //_CHAR_CHANS_H_