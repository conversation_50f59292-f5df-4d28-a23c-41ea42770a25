#ifndef _SYS_ENV_H_
#define _SYS_ENV_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <stddef.h>
//VOID GetCurTime(INOUT char *pszDate, INOUT uint32_t nSize);
int code_convert(const char* from_charset, char* inbuf, size_t inlen, const char* to_charset, char* outbuf, size_t outlen) ;
int AKCS_U2G(char* inbuf, size_t inlen, char* outbuf, size_t outlen);
int AKCS_G2U(char* inbuf, size_t inlen, char* outbuf, size_t outlen);
int GetLastError();

#ifdef __cplusplus
}
#endif
#endif //_SYS_ENV_H_
