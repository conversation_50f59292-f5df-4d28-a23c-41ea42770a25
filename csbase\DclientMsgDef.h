#ifndef __MSG_DCLIENT_MSG_DEF_H__
#define __MSG_DCLIENT_MSG_DEF_H__

#define XML_NODE_VALUE_SIZE         1024
#define XML_NODE_TEXT_SIZE          1024
#define XML_NODE_LINE_SIZE          4096
#define URL_LEN                     256

#define CHECH_SIZE_TOO_LONG(n1, n2)\
    {\
        if ((n1) > (n2))\
        {\
            (n1) -= (n2);\
        }\
        else\
        {\
            AK_LOG_WARN << "xml size is too long, the memory we hold only [ "<< (n1) <<" ], but xml msg need [ "<< (n2) <<" ]";\
            return 0;\
        }\
    }

#define CHECK_BUFFER_OVER_FLOW(n1, n2) \
    do { if ((int)(n1) >= (n2)) { AK_LOG_WARN << "xml buffer is overflow, real size is [" << (n1) << "], but define size is [" << (n2) << "]";} } while (0)



#define SOCKET_MSG_TYPE_NAME_BOOTUP             "Bootup"
#define SOCKET_MSG_TYPE_NAME_REQ_CONN           "RequestConnection"
#define SOCKET_MSG_TYPE_NAME_REQ_STATUS         "RequestStatus"
#define SOCKET_MSG_TYPE_NAME_REPORT_STATUS      "ReportStatus"
#define SOCKET_MSG_TYPE_NAME_REBOOT             "Reboot"
#define SOCKET_MSG_TYPE_NAME_REQ_CONFIG         "RequestConfig"
#define SOCKET_MSG_TYPE_NAME_REPORT_CONFIG      "ReportConfig"
#define SOCKET_MSG_TYPE_NAME_UPDATE_CONFIG      "UpdateConfig"
#define SOCKET_MSG_TYPE_NAME_UPGRADE_START      "UpgradeStart"
#define SOCKET_MSG_TYPE_NAME_FILE_END           "FileEnd"
#define SOCKET_MSG_TYPE_NAME_ACK                "Ack"
#define SOCKET_MSG_TYPE_NAME_MAKE_CALL          "MakeCall"
#define SOCKET_MSG_TYPE_NAME_HANG_UP            "HangUp"
#define SOCKET_MSG_TYPE_NAME_PUSH_AD            "PushAd"
#define SOCKET_MSG_TYPE_NAME_KEY_SEND           "KeySend"
#define SOCKET_MSG_TYPE_NAME_UPGRADE_SEND       "UpgradeSend"
#define SOCKET_MSG_TYPE_NAME_OWNER_MESSAGE      "OwnerMessage"
#define SOCKET_MSG_TYPE_NAME_TEXT_MESSAGE       "TextMessage"
#define SOCKET_MSG_TYPE_NAME_AD_SEND            "AdSend"
#define SOCKET_MSG_TYPE_NAME_ALARM_SEND         "AlarmSend"

/* Begin added by chenyc,2017-01-09,增加手机APP与云平台的通信 */
#define SOCKET_MSG_TYPE_NAME_UPDATE_DEV_CONF    "UpdateDevConf"   //云平台返回设备信息给APP
#define SOCKET_MSG_TYPE_NAME_REQUEST_DEV_CONF   "RequestDevConf"  //APP向云平台请求设备信息
/* End added by chenyc,2017-01-09,增加手机APP与云平台的通信 */

/* Begin added by chenyc,2017-05-24,云平台接入app开发 */
#define SOCKET_MSG_TYPE_NAME_CHECK_TMPKEY_ACK   "CheckTmpKeyAck"
#define SOCKET_MSG_TYPE_NAME_GET_BINDCODE_ACK   "GetBindCodeAck"
#define SOCKET_MSG_TYPE_NAME_UN_BINDCODE_ACK    "UnBindCodeAck"
#define SOCKET_MSG_TYPE_NAME_GET_BINDCODE_LIST_ACK    "GetBindListAck"
#define SOCKET_MSG_TYPE_NAME_NOTIFY_BINDCODE_CHANGE   "BindStatusChangeNotify"
#define SOCKET_MSG_TYPE_NAME_NOTIFY_ALARM             "AlarmNotify"
#define SOCKET_MSG_TYPE_NAME_ALARM_ACK                "AlarmAck"
#define SOCKET_MSG_TYPE_NAME_DEAL_NOTIFY_ALARM        "DealAlarmNotify"
#define SOCKET_MSG_TYPE_NAME_APP_CONF_CHANGE_NOTIFY   "AppConfChangeNotify"
#define SOCKET_MSG_TYPE_NAME_START_DEV_RTSP            "StartRTSP"
#define SOCKET_MSG_TYPE_NAME_STOP_DEV_RTSP             "StopRTSP"

#define SOCKET_MSG_TYPE_NAME_SEND_DEV_LIST             "SendDevList" //个人终端用户联动单元的设备列表
#define SOCKET_MSG_TYPE_NAME_SEND_MOTION_ALERT         "MotionAlert" //平台下发motion alert
#define SOCKET_MSG_TYPE_NAME_SEND_KEEP_RTSP            "KeepRtsp" //平台下发给设备保持rtsp链路的信令
#define SOCKET_MSG_TYPE_NAME_SEND_REQ_ARMING           "RequestArming" //平台下发设备布撤防状态
#define SOCKET_MSG_TYPE_NAME_SEND_REP_ARMING           "ReportArming" //平台响应app设备的布撤防状态

#define SOCKET_MSG_TYPE_NAME_SEND_LOGIN_RESP           "AppLoginResp"

//v4.3
#define SOCKET_MSG_TYPE_NAME_OPENDOOR               "OpenDoor"

#define SOCKET_MSG_TYPE_NAME_OPEN_SECURITY_RELAY "OpenDoorSecurityRelay"

#define SOCKET_MSG_TYPE_NAME_REQUEST_OPEN_DOOR "RequestOpenDoor"
#define SOCKET_MSG_TYPE_NAME_REQUEST_OPEN_SECURITY_RELAY "RequestOpenSecurityRelay"
#define SOCKET_MSG_TYPE_NAME_EMERGENCY_DOOR_CONTROL "RequestEmergencyDoorControl"

//V4.4
#define SOCKET_MSG_TYPE_NAME_ALARM_TRANSLATE                "AlarmTranslate"
#define XML_NODE_NAME_MSG_PARAM_ALARM_MSG       "AlarmMsg"
#define XML_NODE_NAME_MSG_PARAM_APT       "APT"

#define SOCKET_MSG_TYPE_NAME_VISITOR_AUTH        "VisitorAuthAck"
#define SOCKET_MSG_TYPE_NAME_FACE_DATA_MSG       "FaceDataMsg"
#define SOCKET_MSG_TYPE_NAME_VISITOR_KEY         "VisitorTempKeyAck"
#define SOCKET_MSG_TYPE_NAME_OPENDOOR_ACK        "OpenDoorAck"
#define SOCKET_MSG_TYPE_NAME_RESET             "ResetToFactory"



#define XML_NODE_NAME_MSG                   "Msg"
#define XML_NODE_NAME_MSG_TYPE              "Type"
#define XML_NODE_NAME_MSG_TYPE_CHECK_TMPKEY "CheckTmpKey"
#define XML_NODE_NAME_MSG_TYPE_BIND_CODE    "GetBindCode"
#define XML_NODE_NAME_MSG_TYPE_UNBIND_CODE  "UnBindCode"
#define XML_NODE_NAME_MSG_TYPE_BIND_LIST    "GetBindList"
#define XML_NODE_NAME_MSG_TYPE_POST_APP_CODE   "PostAppBindCode"
#define XML_NODE_NAME_MSG_TYPE_PUT_ALARM_DEAL  "DealAlarm"
#define XML_NODE_NAME_MSG_TYPE_REPORT_STATUS   "ReportStatus"

#define XML_NODE_NAME_MSG_TYPE_REQUEST_DEV_LIST   "RequestDevList" //个人终端用户设备获取联动单元设备列表
#define XML_NODE_NAME_MSG_TYPE_REPORT_MOTION_ALERT   "MotionAlert" //设备上报motion alert
#define XML_NODE_NAME_MSG_TYPE_SET_MOTION_ALERT      "SetMotionAlert" //设备上报是否接受motion alert的状态
#define XML_NODE_NAME_MSG_TYPE_REQ_ARMING            "RequestArming" //设备进行布防、撤防的信令
#define XML_NODE_NAME_MSG_TYPE_REPORT_ARMING         "ReportArming" //设备进行布防、撤防的信令
#define XML_NODE_NAME_MSG_TYPE_REPORT_ACTIVITY       "ReportAct" //设备上报动作(开门)记录
#define XML_NODE_NAME_MSG_TYPE_REQUEST_CAPTURE       "ReqCapture" //app请求对某个设备的rtsp流进行截图
#define XML_NODE_NAME_MSG_TYPE_REQUEST_VIDEO_STORAGE      "ReqVideoStorage" //app请求对某个设备的rtsp流进行截图

#define XML_NODE_NAME_MSG_PROTOCAL          "Protocal"
#define XML_NODE_NAME_MSG_PARAM             "Params"
#define XML_NODE_NAME_MSG_PARAM_IP          "IP"
#define XML_NODE_NAME_MSG_PARAM_SUBNETMASK  "SM"
#define XML_NODE_NAME_MSG_PARAM_GATEWAY     "GW"
#define XML_NODE_NAME_MSG_PARAM_PRIMARYDNS  "DNS1"
#define XML_NODE_NAME_MSG_PARAM_SECONDARYDNS    "DNS2"
#define XML_NODE_NAME_MSG_PARAM_PORT        "Port"
#define XML_NODE_NAME_MSG_PARAM_DEVICEID    "DeviceID"
#define XML_NODE_NAME_MSG_PARAM_EXTENSION   "Extension"
#define XML_NODE_NAME_MSG_PARAM_DOWNLOADSERVER  "DownloadServer"
#define XML_NODE_NAME_MSG_PARAM_MAC         "MAC"
#define XML_NODE_NAME_MSG_PARAM_STATUS      "Status"
#define XML_NODE_NAME_MSG_PARAM_SWVER       "SWVer"
#define XML_NODE_NAME_MSG_PARAM_HWVER       "HWVer"
#define XML_NODE_NAME_MSG_PARAM_PRIKEYURL   "PrikeyUrl"
#define XML_NODE_NAME_MSG_PARAM_PRIKEYMD5   "PrikeyMD5"
#define XML_NODE_NAME_MSG_PARAM_RFIDURL     "RfidUrl"
#define XML_NODE_NAME_MSG_PARAM_RFIDMD5     "RfidMD5"
#define XML_NODE_NAME_MSG_PARAM_CONFIGURL   "ConfigUrl"
#define XML_NODE_NAME_MSG_PARAM_CONFIGMD5   "ConfigMD5"
#define XML_NODE_NAME_MSG_PARAM_ADDRURL     "AddrUrl"
#define XML_NODE_NAME_MSG_PARAM_ADDRMD5     "AddrMD5"
#define XML_NODE_NAME_MSG_PARAM_CONFIGMD5   "ConfigMD5"
#define XML_NODE_NAME_MSG_PARAM_ITEM        "Item"
#define XML_NODE_NAME_MSG_PARAM_ITEMS       "Items"
#define XML_NODE_NAME_MSG_PARAM_MODULE      "Module"
#define XML_NODE_NAME_MSG_PARAM_PATH        "Path"
#define XML_NODE_NAME_MSG_PARAM_SIZE        "Size"
#define XML_NODE_NAME_MSG_PARAM_MD5         "MD5"
#define XML_NODE_NAME_MSG_PARAM_KEYTYPE     "KeyType"
#define XML_NODE_NAME_MSG_PARAM_KEYVALUE    "KeyValue"
#define XML_NODE_NAME_MSG_PARAM_MSGID       "MsgID"
#define XML_NODE_NAME_MSG_PARAM_MSGCRC      "MsgCRC"
#define XML_NODE_NAME_MSG_PARAM_RESULT      "Result"
#define XML_NODE_NAME_MSG_PARAM_INFO        "Info"
#define XML_NODE_NAME_MSG_PARAM_FROM        "From"
#define XML_NODE_NAME_MSG_PARAM_TO          "To"
#define XML_NODE_NAME_MSG_PARAM_MODE        "Mode"
#define XML_NODE_NAME_MSG_PARAM_FIRMWAREVER "FirmwareVer"
#define XML_NODE_NAME_MSG_PARAM_FIRMWAREURL "FirmwareUrl"
#define XML_NODE_NAME_MSG_PARAM_TITLE       "Titel"
#define XML_NODE_NAME_MSG_PARAM_CONTENT     "Content"
#define XML_NODE_NAME_MSG_PARAM_TIME        "Time"
#define XML_NODE_NAME_MSG_PARAM_TYPE        "Type"
#define XML_NODE_NAME_MSG_PARAM_URL         "Url"
#define XML_NODE_NAME_MSG_PARAM_URL2         "URL"
#define XML_NODE_NAME_MSG_PARAM_DURATION    "Duration"
#define XML_NODE_NAME_MSG_PARAM_COUNT       "Count"
#define XML_NODE_NAME_MSG_PARAM_ID          "ID"
#define XML_NODE_NAME_MSG_PARAM_ADDRESS     "Address"
#define XML_NODE_NAME_MSG_PARAM_DEVICETYPE  "DeviceType"
#define XML_NODE_NAME_MSG_PARAM_FORCE       "Force"
#define XML_NODE_NAME_MSG_PARAM_HEARTBEAT   "HeartBeatPeriod"
#define XML_NODE_NAME_MSG_DEV_NAME          "DevName"
#define XML_NODE_NAME_MSG_PARAM_MSGUUID     "MsgUUID" //pm一键开门,pmlog的uuid
#define XML_NODE_NAME_MSG_BUTTON_MODULE_LIST   "ModuleList"
#define XML_NODE_NAME_MSG_BUTTON_MODULE     "Module"
#define XML_NODE_NAME_MSG_INS_KIT           "InsKit"

#define XML_NODE_NAME_MSG_PARAM_PUSH_AD_FILE        "File"
#define XML_NODE_NAME_MSG_PARAM_PUSH_AD_SHOWTIME        "ShowTime"
#define XML_NODE_NAME_MSG_PARAM_PUSH_AD_DURATION        "Duration"

#define XML_NODE_NAME_MSG_PARAM_USER        "User"
#define XML_NODE_NAME_MSG_PARAM_CODE        "Code"

/* Begin added by chenyc,2017-01-09,增加手机APP与云平台的通信 */
#define XML_NODE_NAME_MSG_PARAM_APP_NAME   "AppName"
#define XML_NODE_NAME_MSG_PARAM_DEV_MAC    "DevMAC"
#define XML_NODE_NAME_MSG_PARAM_APP_NO     "AppNo"
/* End added by chenyc,2017-01-09,增加手机APP与云平台的通信 */

#define XML_NODE_NAME_MSG_PARAM_TMPKEY          "TmpKey"
#define XML_NODE_NAME_MSG_PARAM_MSGSEQ          "MsgSeq"
#define XML_NODE_NAME_MSG_PARAM_BINDCODE        "BindCode"
#define XML_NODE_NAME_MSG_PARAM_BINDNUM         "BindNum"
#define XML_NODE_NAME_MSG_BIND                  "Bind"
#define XML_NODE_NAME_MSG_BIND_BINDCODE         "BindCode"
#define XML_NODE_NAME_MSG_BIND_TIME             "Time"
#define XML_NODE_NAME_MSG_BIND_STATUS           "Status"
#define XML_NODE_NAME_MSG_BIND_DEVICECODE       "DeviceCode"
#define XML_NODE_NAME_MSG_ALARM_ID              "ID"
#define XML_NODE_NAME_MSG_ALARM_TYPE            "Type"
#define XML_NODE_NAME_MSG_ALARM_TIME            "Time"
#define XML_NODE_NAME_MSG_ALARM_DEV_TYPE        "DevType"
#define XML_NODE_NAME_MSG_ALARM_DEAL_USER       "User"
#define XML_NODE_NAME_MSG_ALARM_DEAL_RESULT     "Result"
#define XML_NODE_NAME_MSG_REMOTE_IP             "RemoteIP"
#define XML_NODE_NAME_MSG_REMOTE_PORT           "RemotePort"
#define XML_NODE_NAME_MSG_PARAM_PASSWD          "Passwd"
#define XML_NODE_NAME_MSG_PARAM_DEVNUM          "DevNum"
#define XML_NODE_NAME_MSG_PARAM_DEV             "Dev"
#define XML_NODE_NAME_MSG_PARAM_NAME            "Name"
#define XML_NODE_NAME_MSG_PARAM_SIP             "Sip"
#define XML_NODE_NAME_MSG_PARAM_TOKEN           "DeviceToken"   //IOS手机唯一标识
#define XML_NODE_NAME_MSG_PARAM_FCM_TOKEN       "FcmToken"      //Androi手机唯一标识
#define XML_NODE_NAME_MSG_PARAM_VOIP_TOKEN       "VoipToken"
#define XML_NODE_NAME_MSG_PARAM_APP_TOKEN       "AppToken"      //app从网关获取的token
#define XML_NODE_NAME_MSG_PARAM_MODELID         "ModuleID"
#define XML_NODE_NAME_MSG_PARAM_RTSP_PWD        "RtspPwd"
#define XML_NODE_NAME_MSG_PIC_NAME              "PicName"
#define XML_NODE_NAME_MSG_VIDEO_RECORD_NAME     "VideoRecordName"
#define XML_NODE_NAME_MSG_THIRD_CAMERA_PIC_NAME "ThirdCameraPicName"
#define XML_NODE_NAME_MSG_INDOORS               "Indoors"

#define XML_NODE_NAME_MSG_ACTION                "Action"
#define XML_NODE_NAME_MSG_INITIATOR             "Initiator"
#define XML_NODE_NAME_MSG_RESP                  "Response"
#define XML_NODE_NAME_MSG_PER_ID                "PerID"
#define XML_NODE_NAME_MSG_PERSONNEL_ID          "PersonnelID"
#define XML_NODE_NAME_MSG_ACCESS_MODE           "AccessMode"
#define XML_NODE_NAME_MSG_DETECTION_TYPE        "DetectionType"
#define XML_NODE_NAME_MSG_DETECTION_INFO        "DetectionInfo"

#define XML_NODE_NAME_MSG_MODE                  "Mode"
#define XML_NODE_NAME_MSG_TO_NAME               "ToName"
#define XML_NODE_NAME_MSG_FROM_NAME             "FromName"
#define XML_NODE_NAME_MSG_EXPIRE                "Expire"


#define XML_NODE_NAME_MSG_LAST_ID               "LastID"
#define XML_NODE_NAME_MSG_UNREAD_COUNT          "UnReadMsgCnt"
#define XML_NODE_NAME_MSG_IS_EXPIRE         "IsExpire"
#define XML_NODE_NAME_MSG_IS_ACTIVE         "Active"


#define XML_NODE_NAME_MSG_PARAM_LOCALTION       "Location"
#define XML_NODE_NAME_MSG_CAP_TIME              "Time"
#define XML_NODE_NAME_MSG_PARAM_CAP_ID          "CaptureID"

#define XML_NODE_NAME_MSG_ROOM_NUM              "RoomNumber"

#define XML_NODE_NAME_MSG_PARAM_DCLIENT_VERSION "DclientVer"
#define XML_NODE_NAME_MSG_TYPE_CONTACT_URL      "ContactsUrl"
#define XML_NODE_NAME_MSG_CHECK_DTMF            "RemoteSipUser"
#define XML_NODE_NAME_MSG_CHECK_DTMF_RESULT     "Result"

#define XML_NODE_NAME_MSG_PARAM_CONTACTMD5  "ContactMD5"
#define XML_NODE_NAME_MSG_PARAM_CONTACTURL  "ContactUrl"
#define XML_NODE_NAME_MSG_PARAM_TZMD5       "TzMD5"
#define XML_NODE_NAME_MSG_PARAM_TZURL       "TzUrl"
#define XML_NODE_NAME_MSG_PARAM_TZDATAMD5   "TzDataMD5"
#define XML_NODE_NAME_MSG_PARAM_TZDATAURL   "TzDataUrl"
#define XML_NODE_NAME_MSG_PARAM_FACEMD5   "FaceSyncMD5"
#define XML_NODE_NAME_MSG_PARAM_FACEURL   "FaceSyncUrl"

#define XML_NODE_NAME_MSG_PARAM_MATEMD5   "ACMetaMD5"
#define XML_NODE_NAME_MSG_PARAM_USERMATEMD5   "UserMetaMD5"
#define XML_NODE_NAME_MSG_PARAM_MATEURL   "ACMetaUrl"
#define XML_NODE_NAME_MSG_PARAM_INFOMD5   "ACInfoMD5"
#define XML_NODE_NAME_MSG_PARAM_INFOURL  "ACInfoUrl"
#define XML_NODE_NAME_MSG_PARAM_SCHEDULEMD5   "ScheduleMD5"
#define XML_NODE_NAME_MSG_PARAM_SCHEDULEURL  "ScheduleUrl"



#define XML_NODE_NAME_MSG_DEVICE_CODE               "DeviceCode"
#define XML_NODE_NAME_MSG_LOCAL_IP                  "LocalIP"

#define XML_NODE_NAME_MSG_PARAM_VIDEO_ID            "Vid"
#define XML_NODE_NAME_MSG_COMMAND               "Command"
#define XML_NODE_NAME_MSG_PARAM_SEQ             "Seq"
#define XML_NODE_NAME_MSG_PARAM_CONTENT             "Content"

#define XML_NODE_NAME_MSG_PARAM_COMMON_VERSION             "Version"
#define XML_NODE_NAME_MSG_PARAM_CHECK_CODE         "AuthCode"

#define XML_NODE_NAME_MSG_PARAM_APP_VERSION             "AppVersion"
#define XML_NODE_NAME_MSG_PARAM_APP_LANGUAGE             "Language"

//运维上传文件
#define XML_NODE_NAME_MSG_SERVERURL     "ServerUrl"
#define XML_NODE_NAME_MSG_USERNAME      "Username"
#define XML_NODE_NAME_MSG_PASSWORD      "Password"
#define XML_NODE_NAME_MSG_FILENAME      "Filename"
#define XML_NODE_NAME_MSG_FILENAME2      "FileName"
#define XML_NODE_NAME_MSG_FILEMD5      "FileMD5"
#define XML_NODE_NAME_MSG_DURATION      "Duration"
#define XML_NODE_NAME_MSG_SERVER_ADDR   "ServerAddr"

//V4.3
#define XML_NODE_NAME_MSG_CALLER        "Caller"
#define XML_NODE_NAME_MSG_CALLEE        "Callee"
#define XML_NODE_NAME_MSG_DAIL_OUT      "DailOut"

#define XML_NODE_NAME_MSG_PARAM_NODES               "Nodes"
#define XML_NODE_NAME_MSG_PARAM_ARMINGTYPE          "Arming"
#define XML_NODE_NAME_MSG_PARAM_ALARMCODE           "AlarmCode"
#define XML_NODE_NAME_MSG_PARAM_VISITOR             "Visitor"
#define XML_NODE_NAME_MSG_PARAM_EMAIL               "Email"
#define XML_NODE_NAME_MSG_PARAM_ACCOUNT             "Account"
#define XML_NODE_NAME_MSG_PARAM_MODEL_NAME          "ModelName"

#define XML_NODE_NAME_MSG_PARAM_SECRET              "AccessKeySecret"
#define XML_NODE_NAME_MSG_PARAM_KEYID               "AccessKeyId"
#define XML_NODE_NAME_MSG_PARAM_SECRET_TOKEN        "SecurityToken"
#define XML_NODE_NAME_MSG_PARAM_BUCKET              "Bucket"
#define XML_NODE_NAME_MSG_PARAM_ENDPOINT            "Endpoint"

#define XML_NODE_NAME_MSG_PARAM_OEM_NAME            "OEM"
#define XML_NODE_NAME_MSG_PARAM_ALARMZONE           "AlarmZone"
#define XML_NODE_NAME_MSG_PARAM_ALARMLOCATION           "AlarmLocation"
#define XML_NODE_NAME_MSG_PARAM_ALARMCUSTOMIZE          "AlarmCustomize"

#define XML_NODE_NAME_MSG_PARAM_SSH_PORT            "SSHPort"
#define XML_NODE_NAME_MSG_PARAM_TRACE_ID            "TraceID"
#define XML_NODE_NAME_MSG_PARAM_RELAY               "Relay"
#define XML_NODE_NAME_MSG_PARAM_SECURITY_RELAY      "SecurityRelay"
#define XML_NODE_NAME_MSG_PARAM_UNITAPT             "UnitApt"

#define XML_NODE_NAME_MSG_PARAM_NUMBER              "Number"
#define XML_NODE_NAME_MSG_PARAM_DELIVERY            "Delivery"
#define XML_NODE_NAME_MSG_PARAM_RELAY_STATUS        "RelayStatus"
#define XML_NODE_NAME_MSG_PARAM_SWITCH              "Switch"

#define XML_NODE_NAME_MSG_PARAM_PERCENT             "Percent"
#define XML_NODE_NAME_MSG_PARAM_LIMIT               "Limit"

#define XML_NODE_NAME_MSG_PARAM_ACID             "ACID"

#define XML_NODE_NAME_MSG_PARAM_ACCESS_TIMES        "AccessTimes"
#define XML_NODE_NAME_MSG_PARAM_UNIQUE_ID           "UniqueID"
#define XML_NODE_NAME_MSG_PARAM_APT_NUM             "AptNum"
#define XML_NODE_NAME_MSG_PARAM_BOX_NUM             "BoxNum"
#define XML_NODE_NAME_MSG_PARAM_BOX_PWD             "BoxPwd"


#define XML_NODE_NAME_MSG_PARAM_PERID             "PerID"
#define XML_NODE_NAME_MSG_PARAM_MSG_TYPE             "MsgType"
#define XML_NODE_NAME_MSG_PARAM_UID             "UID"
#define XML_NODE_NAME_MSG_PARAM_VOICE_UNREAD             "VoiceUnread"
#define XML_NODE_NAME_MSG_PARAM_PAGE_INDEX       "PageIndex"
#define XML_NODE_NAME_MSG_PARAM_PAGE_SIZE        "PageSize"
#define XML_NODE_NAME_MSG_PARAM_MSG_COUNT        "MsgCount"
#define XML_NODE_NAME_MSG_PARAM_UUID             "UUID"
#define XML_NODE_NAME_MSG_PARAM_FRONTDOOR_MAC    "FrontDoorMAC"
#define XML_NODE_NAME_MSG_PARAM_FLOOR           "LiftFloorNum"

//6.5
#define XML_NODE_NAME_MSG_IS_THIRD             "IsThird"
#define XML_NODE_NAME_MSG_CAMERA_UUID             "CamerUUID"

//6.7
#define XML_NODE_NAME_MSG_DOOR_RELAY_STATUS        "Input"
#define XML_NODE_NAME_MSG_DOOR_SE_RELAY_STATUS        "SecInput"
#define XML_NODE_NAME_MSG_MANUAL_UPDATE       "ManualUpdate"
#define XML_NODE_NAME_MSG_TRACKING_NUM      "TrackingNum"
#define XML_NODE_NAME_MSG_COURIER_NAME      "CourierName"

#define XML_NODE_ATTRIBUTE_TYPE                   "Type"
#define XML_NODE_ATTRIBUTE_RELAY_TYPE_EXTERN      "Extern"
#define XML_NODE_ATTRIBUTE_RELAY_TYPE_LOCAL       "Local"

#define XML_NODE_NAME_MSG_ID_ACCESS_RUN             "IDAccessRun"
#define XML_NODE_NAME_MSG_ID_ACCESS_SERIAL          "IDAccessSerial"

// 7.0.0
#define XML_NODE_NAME_MSG_PARAM_ACCESS_MODE         "AccessMode"
#define XML_NODE_NAME_MSG_PARAM_SPECIFIC_FIELDS     "SpecificFields"
#define XML_NODE_NAME_MSG_PARAM_INPUT               "Input"
#define XML_NODE_NAME_MSG_DEPARTMENT_UUID           "DepartmentUUID"
#define XML_NODE_NAME_MSG_CALL_TRACE_ID             "CallTraceID"

#define XML_NODE_NAME_MSG_CALL_TRACE_ID             "CallTraceID"
#define XML_NODE_NAME_MSG_PARAM_UPPER_CASE_SIP      "SIP"
#define XML_NODE_NAME_MSG_PARAM_SITE                "Site"
#define XML_NODE_NAME_MSG_TIMEZONE                  "TimeZone"
#define XML_NODE_NAME_MSG_RECORD_VIDEO              "RecordVideo"
#define XML_NODE_NAME_MSG_CAMERA                    "Camera"
#define XML_NODE_NAME_MSG_STREAM_ID                 "StreamID"
#define XML_NODE_NAME_MSG_WIRED_IP                  "wired"
#define XML_NODE_NAME_MSG_RELAY_ENTRY_MODE          "EntryMode"
#define XML_NODE_NAME_MSG_SECURITY_RELAY_ENTRY_MODE "SEntryMode"
// 7.1.1
#define XML_NODE_NAME_MSG_PARAM_MUSTER_TYPE         "MusterType"
#define XML_NODE_NAME_MSG_PARAM_MUSTER_USER         "MusterUser"
#define XML_NODE_NAME_MSG_PARAM_NODE                "Node"
#define XML_NODE_NAME_MSG_PARAM_MSG_TITLE           "Title"


#define MSG_TO_DEVICE_REQUEST_CONNECTION            0x0001
#define MSG_TO_DEVICE_REQUEST_STATUS                0x0002
#define MSG_TO_DEVICE_REQUEST_CONFIG                0x0003
#define MSG_TO_DEVICE_UPDATE_CONFIG                 0x0004
#define MSG_TO_DEVICE_FILE_START                    0x0005
#define MSG_TO_DEVICE_FILE_DATA                     0x0006
#define MSG_TO_DEVICE_FILE_END                      0x0007
#define MSG_TO_DEVICE_UPGRADE_START                 0x0008
#define MSG_TO_DEVICE_REQUEST_FILE                  0x0009
#define MSG_TO_DEVICE_REMOTE_CONTROL                0x000A
#define MSG_TO_DEVICE_ACK                           0x000B
#define MSG_TO_DEVICE_SEND_DISCOVER                 0x000C
#define MSG_TO_DEVICE_ACK_DISCOVER                  0x000D
#define MSG_TO_DEVICE_PUSH_AD                       0x000E
#define MSG_TO_DEVICE_SEND_OWNER_MESSAGE            0x000F   //added by chenyc,2018-01-31, 已弃用
#define MSG_TO_DEVICE_KEY_SEND                      0x0010
#define MSG_TO_DEVICE_UPGRADE_SEND                  0x0011
#define MSG_TO_DEVICE_AD_SEND                       0x0012
#define MSG_TO_DEVICE_REQUEST_CONFIG_UDP            0x0013
#define MSG_TO_DEVICE_UPDATE_CONFIG_UDP             0x0014
#define MSG_TO_DEVICE_ALARM_SEND                    0x0015
#define MSG_TO_DEVICE_SEND_TEXT_MESSAGE             0x0016

/* Begin added by chenyc,2017-05-24,云平台接入app开发 */
#define MSG_TO_DEVICE_CHECK_TMP_KEY_ACK             0x0018   //云平台校验临时秘钥的响应
#define MSG_TO_DEVICE_CREATE_BIND_CODE_ACK          0x0019   //云平台创建绑定码的响应
#define MSG_TO_DEVICE_DELETE_BIND_CODE_ACK          0x001A   //云平台解除绑定码的响应
#define MSG_TO_DEVICE_BIND_CODE_LIST_ACK            0x001B   //云平台下发所有绑定列表
#define MSG_TO_DEVICE_NOTIFY_BIND_CODE_CHANGE       0x001C   //云平台下发绑定状态改变的通知
#define MSG_TO_DEVICE_NOTIFY_ALARM_OCCURED          0x001D   //云平台下发alarm告警的通知
#define MSG_TO_DEVICE_NOTIFY_ALARM_DEAL             0x001E   //云平台下发告警已经处理完毕的通知
#define MSG_TO_DEVICE_NOTIFY_ALARM_ACK              0x001F   //云平台响应设备的告警
#define MSG_TO_DEVICE_NOTIFY_CONF_CHANGE            0x0020   //云平台下发设备/app配置信息改变的通知

#define MSG_TO_DEVICE_APP_IDENTITY_ACK              0x0021   //云平台响应app身份识别结果(已弃用,2017*08-25)
#define MSG_TO_DEVICE_RECONN_ACCESS_SERVER          0x0022   //云平台下发设备\app重新连接接入服务器的命令
#define MSG_TO_DEVICE_START_RTSP                    0x0023   //云平台下发设备启动RTSP监控
#define MSG_TO_DEVICE_STOP_RTSP                     0x0024   //云平台下发设备停止RTSP监控

#define MSG_TO_DEVICE_SEND_DEVICE_LIST              0x0025   //云平台下发同一联动单元内的设备列表,用于个人终端用户设备
#define MSG_TO_DEVICE_SEND_DEVICE_LIST_CHANGE       0x0026   //云平台下发同一联动单元内的设备列表发生变化的通知,用于个人终端用户设备
#define MSG_TO_DEVICE_APP_REPORT_STATUS_ACK         0x0027   //云平台响应app(android/ios)的状态上报消息

//以下为平台v3.1新增功能
#define MSG_TO_DEVICE_APP_LOGOUT_SIP                0x0028  //平台下发设备\app注销sip的信令

//目前限于门口机罗伯特版本
#define MSG_FROM_DEVICE_HANDLE_ARMING                 0x0029   //设备给设备下发布防、撤防的信令

//以下为平台v3.2新增功能
#define MSG_TO_APP_NOTIFY_MOTION_OCCURED            0x0032   //云平台下发motion alert的通知给app
#define MSG_TO_DEVICE_HANDLE_ARMING                 0x0033   //平台给设备下发布防、撤防的信令
#define MSG_TO_APP_RESP_DEV_ARMING_STATUS           0x0034   //平台给app响应设备布防、撤防状态的消息
#define MSG_TO_DEVICE_KEEP_RTSP                     0x0035   //平台发送给设备保持RTSP连接

#define MSG_TO_DEVICE_QUIT_NODE                     0x0036   //平台发送给设备退出联动系统的指令,设备需要做注销联系人\注销sip账号等一系列清理动作

//chenzhx v4.0
#define MSG_TO_DEVICE_APP_LOGIN_RESP                0x0037  //app登陆后放回一些信息给app ，目前只有未读消息id
#define MSG_TO_DEVICE_CONTACT_URL                   0x0038 //发送获取联系人列表的url
#define MSG_TO_DEVICE_CHECK_DTMF_ACK                0x0039  //校验dtmf按键返回
//v3.4
#define MSG_TO_DEVICE_DEVICE_CODE                   0x003A   //平台返回设备码让用户绑定或注册主账号
#define MSG_TO_DEVICE_CLEAR_DEVICE_CODE             0x003B   //清空设备码
//v4.2
#define MSG_TO_DEVICE_APP_FORCE_LOGOUT              0x003C  // app多地登陆 被强制退出
#define MSG_TO_DEVICE_HEARBEAT_ACK                  0x003D  // 心跳回复

#define MSG_TO_DEVICE_VISITOR_AUTH_ACK              0x003F  //下发通过访客授权
#define MSG_TO_DEVICE_FACE_DATA_FORWARD             0x0042  //人脸数据转发
#define MSG_TO_DEVICE_TEMP_KEY_MSG                  0x0043  //TempKeyCode下发给X916

#define MSG_TO_DEVICE_REGISTER_FACE                 0x0210  //注册人脸信息
#define MSG_TO_DEVICE_MODIFY_FACE                   0x0211  //修改人脸信息
#define MSG_TO_DEVICE_DELETE_FACE                   0x0212  //删除人脸信息
#define MSG_TO_DEVICE_GSFACE_HTTPAPI_LOGIN          0x0213 //通知设备下载人脸文件信息

#define MSG_TO_DEVICE_SEND_OSS_STS                  0x0073 //下发相应oss sts令牌给设备
#define MSG_TO_DEVICE_NOTIFY_ATTENDANCE_SERVICE     0x0080  //通知设备下发离线log文件//云弃用

/* End added by chenyc,2017-05-24,云平台接入app开发 */

#define MSG_FROM_DEVICE_CHECK_KEY                   0x0100
#define MSG_FROM_DEVICE_ACK                         0x0101
#define MSG_FROM_DEVICE_REPORT_STATUS               0x0102
#define MSG_FROM_DEVICE_REPORT_CONFIG               0x0103
#define MSG_FROM_DEVICE_BOOTUP                      0x0104
#define MSG_FROM_DEVICE_FILE_START                  0x0105
#define MSG_FROM_DEVICE_FILE_DATA                   0x0106
#define MSG_FROM_DEVICE_FILE_END                    0x0107
#define MSG_FROM_DEVICE_ALARM                       0x0108
#define MSG_FROM_DEVICE_TEXT_MSG                    0x0109
#define MSG_FROM_DEVICE_ACCESS_INFO                 0x010A
/* Begin added by chenyc,2017-01-04,修改服务端心跳机制 */
#define MSG_FROM_DEVICE_HEART_BEAT                  0x010B
#define MSG_TO_DEVICE_UPDATE_HEARTBEAT_PERIOD       0x010C

/* Added by chenyc,2017-01-09,APP获取设备的SIP相关信息 */
#define MSG_FROM_APP_REQUEST_CONFIG                 0x010D  //APP向云平台请求设备信息
#define MSG_TO_APP_UPDATE_CONFIG                    0x010E  //云平台响应APP的消息

/* Begin added by chenyc,2017-05-24,云平台接入app开发 */
#define MSG_FROM_DEVICE_CHECK_TMP_KEY               0x010F  //梯口机校验临时秘钥
#define MSG_FROM_DEVICE_CREATE_BIND_CODE            0x0110  //室内机请求生成绑定码
#define MSG_FROM_DEVICE_DELETE_BIND_CODE            0x0111  //室内机请求解绑绑定码
#define MSG_FROM_DEVICE_GET_BIND_CODE_LIST          0x0112  //室内机请求所有绑定列表
#define MSG_FROM_DEVICE_POST_BIND_CODE              0x0113  //APP主动向平台推送绑定码 
#define MSG_FROM_DEVICE_PUT_ALARM_DEAL              0x0114  //设备或者app向平台推送告警处理的消息

//以下为平台v3.0新增功能
#define MSG_FROM_DEVICE_POST_APP_IDENTITY           0x0115  //APP主动向平台推送身份识别:账号+密码 (已弃用,2017*08-25)
#define MSG_FROM_ANDROID_REPORT_STATUS              0x0116  //安卓向平台上报状态
#define MSG_FROM_IOS_REPORT_STATUS                  0x0117  //IOS向平台上报状态
#define MSG_FROM_DEVICE_REQUEST_DEVICE_LIST         0x0118  //设备平台请求统一联动单元内的设备列表,用于个人终端用户

//以下为平台v3.2新增功能
#define MSG_FROM_DEVICE_REPORT_ARMING_STATUS        0x0122  //设备上报当前布防状态给平台
#define MSG_FROM_DEVICE_MOTION_ALERT                0x0123  //平台接受设备端上传的motion alert的消息
#define MSG_FROM_DEVICE_REPORT_ACTIVITY_LOGS        0x0124  //设备上报动作消息给平台
#define MSG_FROM_APP_REQUEST_CAPTURE                0x0125  //APP请求平台对设备的视频流进行截图
#define MSG_FROM_APP_SET_RECV_MOTION_ALERT_STATUS   0x0126  //app设置是否接收平台下发的motion alert的通知消息
#define MSG_FROM_APP_HANDLE_DEV_ARMING              0x0127  //app上报对设备进行布防、撤防的信令
#define MSG_FROM_APP_REPORT_LOGOUT                  0x0128  //app通知平台logout
//v4.0
#define MSG_FROM_DEVICE_CHECK_DTMF                  0x0129  //校验dtmf按键
//4.2视频存储开发
#define MSG_FROM_DEVICE_VIDEO_STORAGE_ACTION        0x012A  //视频存储信令,含:启动、停止
#define MSG_FROM_DEVICE_CLI_COMMAND_RESP            0x012B  //cli 返回
//V4.3
#define MSG_FROM_DEVICE_REPORT_CALL_CAPTURE          0x012C  //通话截图消息


//运维接口
#define MSG_TO_DEVICE_MAINTENANCE_GETLOG             0x0201 //获取设备的log
#define MSG_TO_DEVICE_MAINTENANCE_START_PCAP         0x0202 //开启抓包
#define MSG_TO_DEVICE_MAINTENANCE_STOP_PCAP          0x0203 //停止抓包
#define MSG_TO_DEVICE_MAINTENANCE_REBOOT_DEV         0x0204 //重启设备
#define MSG_TO_DEVICE_MAINTENANCE_GET_DEV_CONFIG     0x0205 //获取设备配置文件
#define MSG_TO_DEVICE_MAINTENANCE_RECONNECT_RPS      0x0206  //重新连接rps
#define MSG_TO_DEVICE_MAINTENANCE_RECONNECT_GATEWAY  0x0207  //重新连接网关
#define MSG_TO_DEVICE_MAINTENANCE_RECONNECT_ACCESSSERVER     0x0208  //重新连接接入服务器

#define MSG_TO_DEVICE_CLI_COMMAND    0x0209  //控制终端消息信令

#define MSG_TO_DEVICE_MAINTENANCE_SERVER_CHANGE      0x020A  //更新服务器地址


//V4.0 to devices
#define MSG_TO_DEVICE_DOOR_MOTION_ALERT              0x0050  //云平台转发motion消息给室内机

//v4.4
#define MSG_TO_DEVICE_MANAGE_ALARM  0x0051 //云平台转发alarm消息给R47
#define MSG_FROM_DEVICE_MANAGE_BROADCAST_MSG   0x012D  //管理机广播消息

//v4.6
#define MSG_TO_DEVICE_SERVER_HEARTBEAT      0x003E //服务器发送确认心跳,目前用于alex
#define MSG_FROM_DEVICE_ACK_HEARTBEAT       0x012E //alex设备回复服务器心跳
#define MSG_TO_DEVICE_REQUEST_SENSOR_TRIGGER        0x0072
#define MSG_RROM_DEVICE_RESPONSE_SENSOR_TRIGGER     0x0131

#define MSG_FROM_DEVICE_REPORT_VISITOR_MSG            0x0162  //上报访客信息
#define MSG_FROM_DEVICE_APP_REPORT_VISITOR_AUTH_MSG   0x0163  //上报访客授权信息

#define MSG_FROM_DEVICE_REQUEST_OSS_STS               0x0164  //设备请求oss sts令牌

#define MSG_FROM_DEVICE_REMOTE_CONTROL_ACK            0x0165 //远程控制ACK
#define MSG_FROM_DEVICE_REQUEST_OPENDOOR              0x0166 //室内机请求开门

#define MSG_TO_DEVICE_REMOTE_DEV_WEB_CONTORL          0x0074 //远程设备网页访问
#define MSG_TO_DEVICE_OPENDOOR_ACK                    0x0075 //给室内机返回开门是否成功

#define MSG_FROM_DEVICE_REQUEST_ACINFO                0x0167 //请求获取联系人

#define MSG_FROM_DEVICE_SEND_DELIVERY_MSG             0x0168 //快递消息
#define MSG_FROM_DEVICE_SYNC_ACTIVITY                 0x0169 //批量上传开门记录
#define MSG_FROM_DEVICE_REPORT_RELAY_STATUS           0x0170 //设备上报当前relay状态
#define MSG_FROM_DEVICE_FLOW_OUT_OF_LIMIT             0x0171 //流量超出限额提醒
#define MSG_FROM_DEVICE_REPORT_ACCESS_TIMES           0x0172
#define MSG_FROM_DEVICE_SEND_DELIVERY_BOX_MSG         0x0178 //快递消息 用于JTS
#define MSG_FROM_DEVICE_REQUEST_END_USER_REG          0x0181  //请求待注册用户信息
#define MSG_FROM_DEVICE_REPORT_KIT_DEVICES            0x0182 //室内机将设备的固件号和MAC地址传输给云
#define MSG_FROM_DEVICE_ADD_KIT_DEVICES               0x0183 //手动添加设备：室内机可手动输入MAC，Location，选择Type告知云去绑定设备
#define MSG_FROM_DEVICE_REQUEST_KIT_DEVICES           0x0184 //云需要把当前家庭下的设备信息告知室内机
#define MSG_FROM_DEVICE_MODIFY_LOCATION               0x0185 //室内机可修改设备的Location信息，云需要接收并修改
#define MSG_FROM_DEVICE_RESPONSE_EMERGENCY_KEEP_OPEN_DOOR 0x018D //设备上报一键开门结果
#define MSG_FROM_DEVICE_RESPONSE_EMERGENCY_CLOSE_DOOR 0x018E //设备上报一键关门结果
#define MSG_FROM_DEVICE_REPORT_FILE_MD5               0x0193 //上报设备md5值


#define MSG_TO_DEVICE_REQUEST_KEEP_OPEN_RELAY         0X0081 //常开relay
#define MSG_TO_DEVICE_REQUEST_KEEP_CLOSE_RELAY        0X0082 //常关relay
#define MSG_TO_APP_CHANGE_RELAY_STATUS                0x0083
#define MSG_TO_DEVICE_REG_END_USER                    0X0086 //注册EndUser
#define MSG_TO_DEVICE_REQUEST_IS_KIT                  0X0087 //下发给设备是否KIT方案
#define MSG_TO_DEVICE_REPORT_KIT_DEVICES              0X0088 //云需要把当前家庭下的设备信息告知室内机
#define MSG_FROM_APP_REQUEST_CHANGE_RELAY             0X0330 //APP开关设备relay
#define MSG_TO_DEVICE_REQUEST_EMERGENCY_KEEP_OPEN_DOOR 0x0090 //PM一键开门
#define MSG_TO_DEVICE_REQUEST_EMERGENCY_CLOSE_DOOR    0X0091 //PM一键关门


//6.5语音留言新增
#define MSG_FROM_DEVICE_REPORT_VOICE_MSG            0X018F
#define MSG_TO_DEVICE_ONLINE_NOTIFY_MSG             0x008D
#define MSG_FROM_DEVICE_REQUEST_VOICE_MSG_LIST      0x0190
#define MSG_TO_DEVICE_REPORT_VOICE_MSG_LIST         0x008E
#define MSG_FROM_DEVICE_REQUEST_VOICE_MSG_URL       0x0191
#define MSG_TO_DEVICE_REPORT_VOICE_MSG_URL          0x008F
#define MSG_FROM_DEVICE_REQUEST_DEL_VOICE_MSG       0x0192
//6.5
#define MSG_FROM_DEVICE_THIRD_CAMERA_MEDIA_INFO     0x0198
#define MSG_FROM_DEVICE_REQUEST_ACCOUNT_LOGOUT      0x0194
#define MSG_TO_DEVICE_KIT_DEL_LOG                   0X0092
//6.7
#define MSG_FROM_DEVICE_REPORT_INPUT_STATUS         0x019B
#define MSG_FROM_DEVICE_REQUEST_WEATHER             0x019D
#define MSG_TO_DEVICE_REPORT_WEATHER_MSG            0X0098
#define MSG_FROM_DEVICE_REPORT_TRANS_ACTIVITY_LOGS  0x01A0 //hager 室内机上传门口机的截图
#define MSG_TO_APP_LOGOUT_ACK                       0x0215

//doorcom快递柜
#define MSG_FROM_DEVICE_SEND_DOORCOM_DELIVERY_MSG       0x019F

//pacport快递
#define MSG_FROM_DEVICE_PACPORT_REGISTER            0x1A1
#define MSG_FROM_DEVICE_REPORT_PACPORT_CHECK_INFO   0x1A2

#define MSG_FROM_DEVICE_RESPONSE_EXTERN_PUSH_BUTTON  0x01A4

//访客ID Access校验
#define MSG_FROM_DEVICE_CHECK_VISITOR_IDACCESS 0x1A5
//访客ID Access校验结果回复
#define MSG_TO_DEVICE_CHECK_VISITOR_IDACCESS_ACK 0x229

#define MSG_TO_DEVICE_SEND_PACPORT_CHECK_RESULT 0x0223

// hager kit
#define MSG_FROM_DEVICE_REQUEST_CREATE_ROOM         0x0321
#define MSG_FROM_DEVICE_REQUEST_DELETE_MAC          0x0322
#define MSG_FROM_DEVICE_REQUEST_DELETE_ROOM         0x0323
#define MSG_FROM_DEVICE_REQUEST_DELETE_USER         0x0324

// 反潜回
#define MSG_FROM_DEVICE_REQUEST_ANTIPASSBACK_OPEN_DOOR 0x0199
#define MSG_TO_DEVICE_RESPONSE_ANTIPASSBACK_OPEN_DOOR  0x0095

#define MSG_FROM_DEVICE_REQUEST_RECORD_VIDEO_MSG_LIST    0x01B5
#define MSG_TO_DEVICE_RESPONSE_RECORD_VIDEO_MSG_LIST     0x022B
#define MSG_FROM_DEVICE_REQUEST_PLAY_VIDEO               0x01B6
#define MSG_TO_DEVICE_RECORD_VIDEO_URL                   0x022C
#define MSG_FROM_APP_REQUEST_STOP_CAPTURE                0x01B7
#define MSG_FROM_DEVICE_MUSTER_REPORT_USER               0x01B8


#define MSG_TO_APP_CHANGE_RELAY_STATUS_COMMON   0x227
#define MSG_TO_DEVICE_REQUEST_CHANGE_RELAY 0x226
#define MSG_TO_DEVAPP_EMERGENCY_CONTROL_NOTIFY 0x228

#define MSG_TO_DEVICE_REQUEST_LOCKDOWN_DOOR 0x0231
#define MSG_FROM_DEVICE_RESPONSE_LOCKDOWN_DOOR 0x01BB

#define MSG_TO_APP_ASYNC_REQ_RESP 0x22D

#define MSG_FROM_DEVICE_REPORT_TIMEZONE 0x01B3
#define MSG_FROM_DEVICE_REPORT_MAILBOX_ARRIVAL_NOTICE 0x01B9


#define MSG_FROM_DEVICE_REPORT_VIDEO_RECORD 0x01BC
#define MSG_FROM_DEVICE_UPDATE_DOOR_IP 0x01BD

#define MSG_FROM_DEVICE_REQUEST_VOICE_ASSISTANT_TOKEN 0x01BA
#define MSG_TO_DEVICE_VOICE_ASSISTANT_TOKEN 0x0230

#define MSG_TO_APP_SMARTLOCK_EVENT_NOTIFY 0x0233

//以下 长连接和后端通信的id
#define MSG_FROM_INNER_CONN_INFO                0X400
#define MSG_FROM_INNER_CONN_APP_FORCE_LOGOUT    0x401

#endif

