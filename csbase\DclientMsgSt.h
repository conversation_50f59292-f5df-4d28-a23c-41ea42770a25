#ifndef __MSG_DCLIENT_MSG_ST_DEF_H__
#define __MSG_DCLIENT_MSG_ST_DEF_H__

// 这个文件定义的是WINDOWS端和LINUX端共用的宏定义和结构体,开发时一方定义即可
#include "BasicDefine.h"
#include <string>
#include <sstream>
#include "AkcsCommonDef.h"

#define _T(str) str

//平台用户等级
enum
{
    ACCOUNT_LEVEL_ADMIN = 0,               //平台超级管理员
    ACCOUNT_LEVEL_AREA = 11,              //区域管理员
    ACCOUNT_LEVEL_COMMUNITY = 21,         //社区管理员(物业、安装者)
    ACCOUNT_LEVEL_RESALE = 22,            //零售管理员(个人终端用户管理员)
    ACCOUNT_LEVEL_ENDUSER = 31,           //终端用户(含个人终端用户、个人终端用户)
    ACCOUNT_LEVEL_MAX,
};

//数据库列索引
enum
{
    TAB_INDEX_0 = 0,
    TAB_INDEX_1,
    TAB_INDEX_2,
    TAB_INDEX_3,
    TAB_INDEX_4,
    TAB_INDEX_5,
    TAB_INDEX_6,
    TAB_INDEX_7,
    TAB_INDEX_8,

};

//tcp连接客户端的类型,设备/ANDROID/IOS
enum TCP_CONNT_TYPE
{
    TCP_CONNT_TYPE_DEV = 0,           //设备
    TCP_CONNT_TYPE_ANDROID,          //android
    TCP_CONNT_TYPE_IOS,              //ios
};


//运维消息定义
//让设备上传文件的通用结构体
typedef struct HTTP_MSG_DEV_GET_FILE_COMMON_T
{
    char mac[MAC_SIZE];
    char username[16];
    char password[16];
    char server_url[64];
    char file_name[256];
    char location[64];
    int druation;//单独用于抓包 时长的
} HTTP_MSG_DEV_GET_FILE_COMMON;

//设备重新走rps和网关的结构
typedef struct HTTP_MSG_DEV_RECONNECT_COMMON_T
{
    char mac[MAC_SIZE];
    char server_addr[64];
} HTTP_MSG_DEV_RECONNECT_COMMON;


//时区自动更新
typedef struct HTTP_MSG_UPDATE_TIMEZONE_DATE_T
{
    char mac[MAC_SIZE];
    char tz_data_url[256];
    char tz_xml_url[256];
    char type[16];//"TzData/TzXml"
    char tz_data_md5[MD5_SIZE];
} HTTP_MSG_UPDATE_TIMEZONE_DATE;


//IPC消息
typedef struct IPC_MSG_T
{
    unsigned int len;
    unsigned int id;
    unsigned int from;
    unsigned int param1;
    unsigned int param2;
#define IPC_MSG_HEADER_SIZE     20
#define IPC_MSG_DATA_SIZE       4096  /*v4.0 2014->4096为了传qr的图片*/
#define IPC_MSG_MAX_SIZE        (IPC_MSG_DATA_SIZE + IPC_MSG_HEADER_SIZE)
    unsigned char data[IPC_MSG_DATA_SIZE];
} IPC_MSG;

//IPC消息
typedef struct IPC_MSG_HEAD_T
{
    unsigned int len;
    unsigned int id;
    unsigned int from;
    unsigned int param1;
    unsigned int param2;
} IPC_MSG_HEAD;

typedef struct SOCKET_MSG_NORMAL_T
{
#define SOCKET_MSG_MAGIC_SIZE           2
#define SOCKET_MSG_DATA_SIZE            4096
#define SOCKET_MSG_NORMAL_HEADER_SIZE   10

    unsigned char magic[SOCKET_MSG_MAGIC_SIZE];
    uint16_t crc;
    uint16_t message_id;
    uint16_t head_size;
    uint16_t data_size;
    unsigned char data[SOCKET_MSG_DATA_SIZE];
} SOCKET_MSG_NORMAL;

typedef struct SOCKET_MSG_T
{
#define MAX_SOCKET_FRAME_SIZE       4096
    TCHAR remote_addr[IP_SIZE];
    uint32_t port;
    uint32_t socket_fd;
    uint32_t size;
    //modified by chenyc,20230517,csmain中SOCKET_MSG跟SOCKET_MSG_NORMAL总是配合使用,否则data的长度还是视具体业务定
    unsigned char data[sizeof(SOCKET_MSG_NORMAL)];
} SOCKET_MSG;

//无限制消息长度结构体
typedef struct SOCKET_MSG_NO_LIMIT_T
{
    TCHAR remote_addr[IP_SIZE];
    uint32_t port;
    uint32_t socket_fd;
    int size;
    unsigned char data[0];
} SOCKET_MSG_NO_LIMIT;

//无限制消息长度结构体
typedef struct SOCKET_MSG_NORMAL_NO_LIMIT_T
{
#define SOCKET_MSG_MAGIC_SIZE           2
#define SOCKET_MSG_DATA_SIZE            4096
#define SOCKET_MSG_NORMAL_HEADER_SIZE   10

    unsigned char magic[SOCKET_MSG_MAGIC_SIZE];
    uint16_t crc;
    uint16_t message_id;
    uint16_t head_size;
    uint16_t data_size;
    unsigned char data[0];
} SOCKET_MSG_NORMAL_NO_LIMIT;


typedef struct SOCKET_MSG_FILEDATA_T
{
#define SOCKET_MSG_FILEDATA_HEADER_SIZE     16
    unsigned char magic[SOCKET_MSG_MAGIC_SIZE];
    uint16_t crc;
    uint16_t message_id;
    uint16_t head_size;
    uint16_t data_size;
    uint16_t reserve;
    uint32_t data_offset;
    unsigned char data[SOCKET_MSG_DATA_SIZE];
} SOCKET_MSG_FILEDATA;

typedef struct SOCKET_MSG_BOOTUP_T
{
    TCHAR protocal[PROTOCAL_SIZE];
    TCHAR ip_addr[IP_SIZE];
    uint32_t port;
} SOCKET_MSG_BOOTUP;

typedef struct SOCKET_MSG_REQ_STATUS_T
{
    TCHAR protocal[PROTOCAL_SIZE];
} SOCKET_MSG_REQ_STATUS;

typedef struct SOCKET_MSG_REQ_CONN_T
{
    TCHAR protocal[PROTOCAL_SIZE];
    TCHAR ip_addr[IP_SIZE];
    uint32_t port;
    BOOL force_connect;
} SOCKET_MSG_REQ_CONN;

typedef struct SOCKET_MSG_ACK_T
{
#define ACK_RESULT_SIZE 32
#define ACK_INFO_SIZE 256
#define ACK_MSGID_SIZE 32
#define ACK_MSGCRC_SIZE 32
    TCHAR protocal[PROTOCAL_SIZE];
    TCHAR message_id[ACK_MSGID_SIZE];
    TCHAR message_crc[ACK_MSGCRC_SIZE];
    TCHAR result[ACK_RESULT_SIZE];
    TCHAR info[ACK_INFO_SIZE];
} SOCKET_MSG_ACK;

typedef struct SOCKET_MSG_REPORT_STATUS_T
{
#ifndef MAC_CHECK_CODE_SIZE
#define MAC_CHECK_CODE_SIZE                 32
#endif

    TCHAR protocal[PROTOCAL_SIZE];
    TCHAR device_id[DEVICE_ID_SIZE];
    TCHAR extension[INT_SIZE];
    TCHAR download_server[VALUE_SIZE];
    TCHAR type[INT_SIZE];
    TCHAR ip_addr[IPV6_SIZE];
    TCHAR wired_ip_addr[IPV6_SIZE]; //有线ip，区分于ip_addr优先取的无线ip
    TCHAR wired_subnet_mask[IPV6_SIZE]; //有线子网掩码，区分于subnet_mask优先取的无线子网掩码
    TCHAR subnet_mask[IPV6_SIZE];
    TCHAR gateway[IPV6_SIZE];
    TCHAR primary_dns[IPV6_SIZE];
    TCHAR secondary_dns[IPV6_SIZE];
    TCHAR mac[MAC_SIZE];
    TCHAR status[DEVICE_STATUS_SIZE];
    TCHAR SWVer[DEVICE_SWVER_SIZE];
    TCHAR HWVer[DEVICE_HWVER_SIZE];
    TCHAR private_key_md5[MD5_SIZE];
    TCHAR rf_id_md5[MD5_SIZE];
    TCHAR address_md5[MD5_SIZE];
    TCHAR config_md5[MD5_SIZE];
    TCHAR contact_md5[MD5_SIZE];
    TCHAR auth_code[MAC_CHECK_CODE_SIZE];
    char  tz_md5[MD5_SIZE]; //嵌入式设备
    char  tz_data_md5[MD5_SIZE]; //Android设备
    char  face_md5[MD5_SIZE];
    uint32_t dclient_version;
    uint32_t indoor_arming;
    int relay_status;

    char  user_meta_md5[MD5_SIZE];
    char  schedule_md5[MD5_SIZE];   
    int is_third_party;
    int dynamics_iv;//aes 动态iv
    ULONG func_bit;
} SOCKET_MSG_REPORT_STATUS;

typedef struct SOCKET_MSG_REMOTE_CONTROL_T
{
#define REMOTE_CONTROL_ITEM_NUM         8
#define REMOTE_CONTROL_ITEM_SIZE        128
#define REMOTE_CONTROL_TYPE_SIZE        16
    TCHAR protocal[PROTOCAL_SIZE];
    TCHAR type[REMOTE_CONTROL_ITEM_SIZE];
    TCHAR item[REMOTE_CONTROL_ITEM_NUM][REMOTE_CONTROL_ITEM_SIZE];
    TCHAR value[REMOTE_CONTROL_ITEM_NUM][REMOTE_CONTROL_ITEM_SIZE];
} SOCKET_MSG_REMOTE_CONTROL;

typedef struct SOCKET_MSG_EMERGENCY_CONTROL_T
{
    char msg_uuid[64];
    char relay[8];
    char security_relay[8];
    char device_uuid[64];
    char initiator[64];
    int operation_type; //关门0/开门1
    int auto_manual;    //自动0/手动1
    char mac[32];
    int act_type;
    SOCKET_MSG_EMERGENCY_CONTROL_T() {
        memset(this, 0, sizeof(*this));
    }
}SOCKET_MSG_EMERGENCY_CONTROL;


typedef struct SOCKET_MSG_UPGRADE_START_T
{
#define FILE_PATH_SIZE                  128
    //#define FILE_MD5_SIZE                 32
#define FILE_MD5_SIZE                   36   //32代码编写时,容易发生截断的问题

    TCHAR protocal[PROTOCAL_SIZE];
    TCHAR file_path[FILE_PATH_SIZE];
    TCHAR file_md5[FILE_MD5_SIZE];
    uint32_t file_size;
} SOCKET_MSG_UPGRADE_START;


typedef struct SOCKET_MSG_KEY_SEND_T
{
    TCHAR protocal[PROTOCAL_SIZE];
    TCHAR private_key_url[URL_SIZE];
    TCHAR private_key_md5[MD5_SIZE];
    TCHAR rf_id_url[URL_SIZE];
    TCHAR rf_id_md5[MD5_SIZE];
    TCHAR config_url[URL_SIZE];
    TCHAR config_md5[MD5_SIZE];
    TCHAR addr_url[URL_SIZE];
    TCHAR addr_md5[MD5_SIZE];
    //v4.0
    TCHAR contact_url[URL_SIZE];
    TCHAR contact_md5[MD5_SIZE];
    char  tz_url[URL_SIZE];
    char  tz_md5[MD5_SIZE];
    char  tz_data_url[URL_SIZE];
    char  tz_data_md5[MD5_SIZE];
    char  face_url[URL_SIZE];
    char  face_md5[MD5_SIZE];

    char  user_info_url[URL_SIZE];
    char  user_info_md5[MD5_SIZE];
    char  user_meta_url[URL_SIZE];
    char  user_meta_md5[MD5_SIZE];

    char  schedule_url[URL_SIZE];
    char  schedule_md5[MD5_SIZE];
    int keysend_type;
} SOCKET_MSG_KEY_SEND;


typedef struct SOCKET_MSG_UPGRADE_SEND_T
{
    TCHAR protocal[PROTOCAL_SIZE];
    TCHAR firmware_version[DEVICE_SWVER_SIZE];
    TCHAR firmware_url[1024];//URL_SIZE
    int is_need_reset;
} SOCKET_MSG_UPGRADE_SEND;


typedef struct SOCKET_MSG_TEXT_MESSAGE_T
{
#define TEXTMSG_TYPE_SIZE       24
#define TEXTMSG_CONTENT_SIZE    3500
#define TEXTMSG_USER_SIZE       24
#define TEXTMSG_TITLE_SIZE      1025
#define TEXTMSG_EXTENSION_SIZE      1024
    TCHAR protocal[PROTOCAL_SIZE];
    TCHAR title[TEXTMSG_TITLE_SIZE];
    TCHAR content[TEXTMSG_CONTENT_SIZE];
    TCHAR time[DATETIME_SIZE];
    TCHAR from[TEXTMSG_USER_SIZE];
    TCHAR to[TEXTMSG_USER_SIZE];
    TCHAR extension_field[TEXTMSG_EXTENSION_SIZE];
    uint32_t id;//MessageAccountList表id 用于离线推送url拼接
    int type;
} SOCKET_MSG_TEXT_MESSAGE;

typedef struct SOCKET_MSG_SEND_TEXT_MESSAGE_T
{
    enum TextSendType
    {
        MULTI_SEND = 0, //多个发送
        FULL_SEND = 1,  //全发送
    };
    enum TextClientType
    {
        BOTH_SEND = 0, //APP、设备均发送
        DEV_SEND = 1,  //设备发送
        APP_SEND = 2,  //app发送
    };

    SOCKET_MSG_TEXT_MESSAGE text_message;
    std::string account; //注意,不能用memset初始化..
    int client_type; //终端的类型,设备/app
    int per_manager_id; //个人终端管理员的id
} SOCKET_MSG_SEND_TEXT_MESSAGE;


typedef struct SOCKET_MSG_ACCESS_INFO_T
{
#define ACCESS_INFO_CODE_SIZE       24
    TCHAR protocal[PROTOCAL_SIZE];
    TCHAR code[ACCESS_INFO_CODE_SIZE];
} SOCKET_MSG_ACCESS_INFO;

typedef struct SOCKET_MSG_ALARM_T
{
#define ALARM_TYPE_SIZE         64
    TCHAR protocal[PROTOCAL_SIZE];
    TCHAR type[ALARM_TYPE_SIZE];//一串室内机串上来的英文数据
    TCHAR msg_seq[MSG_SEQ_SIZE];
    int alarm_code;//告警类型 用于程序判断和多语言展示判断
    int alarm_zone;//防区
    int alarm_location;//位置
    int alarm_customize;//1 设备自定义alarm
    int alarm_id; //插入数据库alarm记录对应的id
    char alarm_uuid[64]; //数据库中alarm记录对应的uuid
    char input[32]; //input常开告警
    int relay_num;
    RelayType relay_type;
    char company_uuid[64];
    SOCKET_MSG_ALARM_T() {
        memset(this, 0, sizeof(*this));
    }
} SOCKET_MSG_ALARM;

typedef struct SOCKET_MSG_ALARM_SEND_T
{
    TCHAR protocal[PROTOCAL_SIZE];
    TCHAR type[ALARM_TYPE_SIZE];
    TCHAR community[COMMUNITY_SIZE];
    TCHAR address[DEVICE_ID_SIZE]; //对个人终端用户就是联动单元, 赋值为主账号node
    TCHAR time[DATETIME_SIZE];
    TCHAR msg_seq[MSG_SEQ_SIZE];
    TCHAR from_local[LOCATION_SIZE];//location
    TCHAR mac[MAC_SIZE];
    uint32_t grade;                //设备的归属等级，是社区共享，单元共享，还是用户独占
    uint32_t manager_account_id;
    uint32_t unit_id;
    uint32_t extension;
    uint32_t device_type;
    uint32_t id;
    TCHAR APT[64];
    int alarm_code;//告警类型 用于程序判断和多语言展示判断
    int alarm_zone;//防区
    int alarm_location;//位置
    int alarm_customize;//1 设备自定义alarm
    TCHAR title[256];
    uint64_t trace_id;
    char input[32];
    char site[32];
    SOCKET_MSG_ALARM_SEND_T() {
        memset(this, 0, sizeof(*this));
    }
} SOCKET_MSG_ALARM_SEND;

// 告警处理消息
typedef struct SOCKET_MSG_ALARM_DEAL_T
{
#define ALARM_ID_SIZE           16
#define ALARM_RESULT_SIZE       64
#define ALARM_DEAL_TYPE_SIZE    64
#define ALARM_DEV_NAME_SIZE     64
#define AREA_NODE_SIZE          32    //地址节点长度

    char protocal[PROTOCAL_SIZE];
    char community[COMMUNITY_SIZE];
    char area_node[AREA_NODE_SIZE];
    char alarm_id[ALARM_ID_SIZE];
    char user[USER_SIZE];
    char result[ALARM_RESULT_SIZE];
    char type[ALARM_DEAL_TYPE_SIZE];//告警的处理类型
    char time[DATETIME_SIZE];
    char mac[MAC_SIZE]; //v3.1加密
    char device_name[ALARM_DEV_NAME_SIZE];
    uint32_t manager_account_id;
    int alarm_code;
    int alarm_zone;
    int alarm_location;
    int alarm_customize;//1 设备自定义alarm
    uint32_t unit_id;
    char title[256];
    uint64_t trace_id;
    SOCKET_MSG_ALARM_DEAL_T() {
        memset(this, 0, sizeof(*this));
    }
} SOCKET_MSG_ALARM_DEAL;

//下发设备rtsp服务获取RTP流的指令
typedef struct SOCKET_MSG_REQ_RTSP_T
{
    char protocal[PROTOCAL_SIZE];
    char remote_ip[IPV6_SIZE];
    char SSRC[32];//十六进制 AB0103D0
    char camera_uuid[64];
    char video_type[32];
    int remote_port;
    int type; // csmain::RtspType
    int have_third_camera;
    int video_pt;
    char transfer_door_uuid[32]; //西班牙转流为门口机的sip, hager转流为为门口机的mac
    char video_fmtp[256]; // 解决sdp携带sps/vps信息的摄像头
    char srtp_key[256];   // rtp加密的类型和key
    int rtp_confuse;
    char camera_name[32];
    int stream_id;
    SOCKET_MSG_REQ_RTSP_T() {
        memset(this, 0, sizeof(*this));
    }
} SOCKET_MSG_REQ_RTSP;


////////////////////////////////////////////////////////////////

typedef struct SOCKET_MSG_MOTION_ALERT_T
{
    char mac[MAC_SIZE];
    char picture_name[PIC_NAME_SIZE];
    char video_record_name[256];
    int detection_type;
    int detection_info;
    SOCKET_MSG_MOTION_ALERT_T() {
        memset(this, 0, sizeof(*this));
    }
} SOCKET_MSG_MOTION_ALERT;

typedef struct SOCKET_MSG_MOTION_ALERT_SEND_T
{
    char protocal[PROTOCAL_SIZE];
    char mac[MAC_SIZE];
    char node[NODE_SIZE]; //对个人终端用户就是联动单元
    char location[LOCATION_SIZE]; //设备的location
    uint32_t id;
    char capture_time[NODE_SIZE];
    char sip_account[SIP_SIZE];
    char picture_addr[128];
    uint32_t detection_type;
    uint32_t detection_info;
    SOCKET_MSG_MOTION_ALERT_SEND_T() {
        memset(this, 0, sizeof(*this));
    }
} SOCKET_MSG_MOTION_ALERT_SEND;

//app上报对设备进行布防、撤防的信令
typedef struct SOCKET_MSG_DEV_ARMING_T
{
    char mac[MAC_SIZE];
    char uid[USER_SIZE];
    char szAction[ARMING_ACTION_SIZE]; //只有两种状态: Get | Set
    int  mode; //csmain::ArmingType
    int  resp_action; //设备报arming状态时，指明是那种情况 1=app get  2=app set 3=self set
    int  home_sync; //室内机自己配置要求云转发,0=关 1=开 2=同步关配置给其他室内机和门口机  3=同步开配置给其他室内机和门口机
    SOCKET_MSG_DEV_ARMING_T() {
        memset(this, 0, sizeof(*this));
    }
} SOCKET_MSG_DEV_ARMING;

//app请求对某个设备的rtsp流进行截图
typedef struct SOCKET_MSG_REQ_CAPTURE_T
{
    char mac[MAC_SIZE];
    char uuid[64];
    char site[64];
    char node[64];
    char username[128];
    char pic_name[128];
    char camera[32];
    char flow_uuid[64];
    int stream_id;
    int record_video;
    SOCKET_MSG_REQ_CAPTURE_T() {
        memset(this, 0, sizeof(*this));
    }
} SOCKET_MSG_REQ_CAPTURE;


//app登陆时候返回数据
typedef struct SOCKET_MSG_RESP_APPLOGIN_T
{
    int last_msg_id;
    int unread_number;
    short is_expire;
    short id_active;
} SOCKET_MSG_RESP_APPLOGIN;

//设备判断dtmf按键
typedef struct SOCKET_MSG_CHECK_DTMF_T
{
    char sip[SIP_SIZE];
    char check_seq[MSG_SEQ_SIZE];
    int result; //1校验成功 0失败
    SOCKET_MSG_CHECK_DTMF_T() {
        memset(this, 0, sizeof(*this));
    }
} SOCKET_MSG_CHECK_DTMF;

//app请求对某个设备的rtsp流进行截图
typedef struct SOCKET_MSG_VIDEO_STORAGE_T
{
    char uid[VIDEO_ACTION_SIZE];
    bool is_start_storage;
    SOCKET_MSG_VIDEO_STORAGE_T() {
        memset(this, 0, sizeof(*this));
    }
} SOCKET_MSG_VIDEO_STORAGE;


//设备下载配置文件路径
typedef struct SOCKET_MSG_CONFIG_URL_T
{
#define CONFIG_URL_SIZE 256
    char config_url[CONFIG_URL_SIZE];
    char contact_url[CONFIG_URL_SIZE];
    SOCKET_MSG_CONFIG_URL_T() {
        memset(this, 0, sizeof(*this));
    }
} SOCKET_MSG_CONFIG_URL;

typedef struct CONFIG_MODULE_T
{
#define CONFIG_MODULE_ITEM_NUM          32
#define CONFIG_MODULE_ITEM_SIZE         256
    TCHAR item[CONFIG_MODULE_ITEM_NUM][CONFIG_MODULE_ITEM_SIZE];
    CONFIG_MODULE_T() {
        memset(this, 0, sizeof(*this));
    }
} CONFIG_MODULE;

typedef struct SOCKET_MSG_CONFIG_T
{
    TCHAR protocal[PROTOCAL_SIZE];
    TCHAR mac[33];
    CONFIG_MODULE module;
    int config_count;
    SOCKET_MSG_CONFIG_T() {
        memset(this, 0, sizeof(*this));
    }
} SOCKET_MSG_CONFIG;


//命令返回
typedef struct SOCKET_MSG_COMMAND_RESP_T
{
    char sequence[16];
    char message[4096];
    SOCKET_MSG_COMMAND_RESP_T() {
        memset(this, 0, sizeof(*this));
    }
} SOCKET_MSG_COMMAND_RESP;

//call 截图
typedef struct SOCKET_MSG_CALL_CAPTURE_T
{
    char picture_name[PIC_NAME_SIZE];//0C1100000001-1513232303_0_CALL.jpg
    char caller[32];//主叫 呼出=自己的sip 呼入=对方sip
    char callee[32];//被叫 呼出=对方sip(是自己真实呼出号码，不管Remote-party-id) 呼入=自己sip
    int dialog_out; //呼出=1  呼入=0
    char msg_seq[32];
    char pic_url[256];
    char spic_url[256];
    char department_uuid[64];  // 部门uuid
    long capture_time;
    char call_trace_id[64];
    char video_record_name[256]; // 视频存储文件名
    char indoor_mac_list[512]; // 视频存储文件名
    char video_url[256];
    SOCKET_MSG_CALL_CAPTURE_T() {
        memset(this, 0, sizeof(*this));
    }
} SOCKET_MSG_CALL_CAPTURE;

//管理中心机广播消息
typedef struct SOCKET_MSG_MNG_DEV_REPORT_MSG_T
{
    char title[128];
    char content[512];
    char time[DATETIME_SIZE];
    char nodes[3 * 1024];
    SOCKET_MSG_MNG_DEV_REPORT_MSG_T() {
        memset(this, 0, sizeof(*this));
    }
} SOCKET_MSG_MNG_DEV_REPORT_MSG;


//arming模式是否可以切换
typedef struct SOCKET_MSG_SENSOR_TIRGGER_MSG_T
{
    int home;//是否可以切换home模式 1不可以切换
    int sleep;
    int away;
    SOCKET_MSG_SENSOR_TIRGGER_MSG_T() {
        memset(this, 0, sizeof(*this));
    }
} SOCKET_MSG_SENSOR_TIRGGER_MSG;

typedef struct SOCKET_MSG_DEV_REPORT_VISITOR_T
{
    char visitor[VALUE_SIZE];
    char email[VALUE_SIZE];
    char id_number[VALUE_SIZE];
    char from[VALUE_SIZE];
    char account[VALUE_SIZE];
    char model_name[PIC_NAME_SIZE];
    int  count;
    int  tempkey_code;
    SOCKET_MSG_DEV_REPORT_VISITOR_T() {
        memset(this, 0, sizeof(*this));
    }
} SOCKET_MSG_DEV_REPORT_VISITOR;

typedef struct SOCKET_MSG_DEV_REPORT_VISITOR_AUTH_T
{
    char sip_account[VALUE_SIZE];
    int  count;
    SOCKET_MSG_DEV_REPORT_VISITOR_AUTH_T() {
        memset(this, 0, sizeof(*this));
    }
} SOCKET_MSG_DEV_REPORT_VISITOR_AUTH;

typedef struct SOCKET_MSG_DEV_REPORT_FACE_DATA_T
{
    char model_url[URL_SIZE];
    SOCKET_MSG_DEV_REPORT_FACE_DATA_T() {
        memset(this, 0, sizeof(*this));
    }
} SOCKET_MSG_DEV_REPORT_FACE_DATA;

typedef struct SOCKET_MSG_DEV_OSS_STS_T
{
    char secret[TOKEN_SIZE];
    char key_id[TOKEN_SIZE];
    char token[STRING_VALUE_SIZE];
    char oss_bucket[VALUE_SIZE];
    char endpoint[URL_SIZE];
    SOCKET_MSG_DEV_OSS_STS_T() {
        memset(this, 0, sizeof(*this));
    }
} SOCKET_MSG_DEV_OSS_STS;

//远程设备访问
typedef struct SOCKET_MSG_REMOTE_DEV_CONTORL_T
{
    char user[128];
    char password[128];
    char mac[16];
    int  port;
    char ssh_proxy_domain[128];
    SOCKET_MSG_REMOTE_DEV_CONTORL_T() {
        memset(this, 0, sizeof(*this));
    }
} SOCKET_MSG_REMOTE_DEV_CONTORL;

typedef struct SOCKET_MSG_DEV_REMOTE_ACK_T
{
#define REMOTE_CONTROL_ID_SIZE      64
#define REMOTE_CONTROL_TYPE_SIZE    16
    char type[REMOTE_CONTROL_TYPE_SIZE];
    char trace_id[64];//拼接了mac会变长
    char info[VALUE_SIZE];
    int result;  //0=失败 1=成功
    int msg_id;
    int response_type; //0=app 1=dev
    SOCKET_MSG_DEV_REMOTE_ACK_T() {
        memset(this, 0, sizeof(*this));
    }
} SOCKET_MSG_DEV_REMOTE_ACK;

typedef struct SOCKET_MSG_DEV_REQUEST_OPEN_T
{
    char type[32];      // SOCKET_MSG_TYPE_NAME_REQUEST_OPEN_DOOR / SOCKET_MSG_TYPE_NAME_OPEN_SECURITY_RELAY
    char mac[20];
    char trace_id[64];
    int relay;
} SOCKET_MSG_DEV_REQUEST_OPEN;

typedef struct SOCKET_MSG_DEV_SEND_DELIVERY_T
{
    char account[32];
    int amount;
    struct SOCKET_MSG_DEV_SEND_DELIVERY_T* next;
} SOCKET_MSG_DEV_SEND_DELIVERY;

typedef struct SOCKET_MSG_DEV_SEND_DELIVERY_OEM_T
{
    char account[32];
    char apt_num[64];
    char box_num[64];
    char box_pwd[64];
} SOCKET_MSG_DEV_SEND_DELIVERY_OEM;

typedef struct SOCKET_MSG_DEV_DELIVERY_T
{
    char account[32];
    int amount;
} SOCKET_MSG_DEV_DELIVERY;

typedef struct SOCKET_MSG_DEV_RELAY_CHANGE_T
{
    char mac[16];
    int relay_id;
    int relay_switch;
} SOCKET_MSG_DEV_RELAY_CHANGE;


typedef struct MSG_DEV_OFFINE_NOTIFY_T
{
#define NAME_SIZE 32
#define LIST_SIZE 256
    char community[NAME_SIZE];
    char pm_name[NAME_SIZE];
    char location_list[LIST_SIZE];
    char mac_list[LIST_SIZE];
    char email[EMAIL_SIZE];
    char time_zone[36];
} MSG_DEV_OFFINE_NOTIFY;


struct SOCKET_MSG_FLOW_OUT_LIMIT
{
    double percent;
    int64_t limit;
};

struct SOCKET_MSG_USER_INFO
{
    char mac[MAC_SIZE];
    DULONG traceid;
    char uuids[4096];//*********;*********
};

struct SOCKET_MSG_DEV_REPORT_ACCESS_TIMES
{
    char temp_key[20];
    char unique_id[30];
    int access_times;
};

struct SOCKET_MSG_DEV_KIT_DEVICE
{
    int type;  // 0:梯口机 1:门口机 2:室内机 3:管理中心机 4:围墙机 5:SDMC 50: 门禁
    char mac[MAC_SIZE];
    char location[65];
    char version[33];
    char relay[2048];
    
    std::string Print()
    {
        std::stringstream stream;
        stream << "Kit Device:mac=" << mac << ";location=" << location << ";type=" << type << ";version=" << version << ";relay=" << relay;
        return stream.str();
    }
};

struct SOCKET_MSG_DEV_OFFLINE_ACTIVE
{
    //char mac[16];
    char file_name[256];
    char file_md5[33];
    char sequence[16];
};

struct SOCKET_MSG_DEV_VOICE_MSG
{
    int msg_type;
    int dev_type;
    char uid[64];
    char pic_name[256];
    char file_name[256];
};

struct SOCKET_MSG_DEV_ONLINE_NOTIFY
{
    int unread_voice_count;
    char mac[20];
    char uuid[64];
};

struct SOCKET_MSG_DEV_VOICE_MSG_LIST
{
    int page_size;
    int page_index;
    int msg_count;
};

struct SOCKET_MSG_DEV_VOICE_MSG_URL
{
    char uuid[64];
    char url[1024];
};

typedef struct SOCKET_MSG_REPORT_FILEMD5_T
{
    char private_key_md5[MD5_SIZE];
    char rf_id_md5[MD5_SIZE];
    char config_md5[MD5_SIZE];
    char contact_md5[MD5_SIZE];
    char user_meta_md5[MD5_SIZE];
    char schedule_md5[MD5_SIZE];   
} SOCKET_MSG_REPORT_FILEMD5;

struct SOCKET_MSG_COMMON_ACK
{
    char msg_type[64];
    char mac[20];
    char trace_id[128];
    int result;
};

struct SOCKET_MSG_COMMON_SEQ_ACK
{
    uint16_t msg_id;
    char mac[20];
    char msg_seq[32];
};

struct SOCKET_MSG_RELAY_STATUS
{
    char msg_type[64];
    char door_relay_status[8];
    char door_se_relay_status[8];
    char trace_id[128];
};

typedef struct SOCKET_MSG_DEV_REQ_WEATHER_WAY_T
{
    int manual_update; // 手动刷新=1, 定时刷新=0
}SOCKET_MSG_DEV_REQ_WEATHER_WAY;

typedef struct SOCKET_MSG_DEV_WEATHER_INFO_T
{   
    char mac[20];
    char city[128];
    char states[128];
    char country[128];
    char weather[16];
    char humidity[16];
    char temperature[16];
}SOCKET_MSG_DEV_WEATHER_INFO;

//临时访问秘钥校验请求结构体
typedef struct SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY_T
{
    char protocal[16];
    char tmpkey[24];
    char mac[20];
    char area_node[32];
    char msg_seq[16];
    char unit_apt[128];//floor会比较长
    char relay[16];
    char security_relay[16];
    int  relay_value;
    int security_relay_value;
    int result;   //0:校验成功  1:校验失败
    uint32_t unit_id;
    uint32_t manager_account_id;
    int personal_public_device_id;
    uint32_t personal_unit_id;
    int is_pmcreate;
    char node[32];
    char account[32];
    int sche_type;
    int key_table_type;
    int key_id;
    int access_times;
    char room_num[16];
    char floor[64];
    uint32_t mngaccount_id;
    char amenity_reservation_uuid[64];
    char company_uuid[64];
    
    SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY_T() {
        memset(this, 0, sizeof(*this));
    }
}SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY;

// 获取设备传感器状态
typedef struct SOCKET_MSG_SENSOR_TRIGGER_T
{
    char mac[16];
    int mode;
}SOCKET_MSG_SENSOR_TRIGGER;


// 设备上报pacport注册消息
typedef struct SOCKET_MSG_REQ_PACPORT_REG_T
{
    int status; // 0=注销 1=注册
    /* data */
    SOCKET_MSG_REQ_PACPORT_REG_T()
    {
        memset(this, 0, sizeof(*this));
    }
}SOCKET_MSG_REQ_PACPORT_REG;

// 设备注册pacport所需信息
typedef struct SOCKET_MSG_PACPORT_REG_INFO_T
{
    char mac[16];
    char prefecture_name[513]; 
    char city_name[513];
    char district_name[513];
    char street_num[64]; 
    char postal_code[33];

    SOCKET_MSG_PACPORT_REG_INFO_T()
    {
        memset(this, 0, sizeof(*this));
    }
}SOCKET_MSG_PACPORT_REG_INFO;

// 设备上报pacport注册消息
typedef struct SOCKET_MSG_REQ_PACPORT_UNLOCK_T
{
    char room_num[129];
    char tracking_num[64]; //快递条码编号
    char courier_name[64]; //快递公司名称
    char trace_id[64]; //标识一次快递校验
}SOCKET_MSG_REQ_PACPORT_UNLOCK;

// 设备pacport校验所需信息
typedef struct SOCKET_MSG_PACPORT_UNLOCK_CHECK_T
{
    char mac[16];
    char room_num[129];
    char tracking_num[64]; //快递条码编号
    char courier_name[64]; //快递公司名称
    char trace_id[64]; //标识一次快递校验
}SOCKET_MSG_PACPORT_UNLOCK_CHECK;

// 下发设备pacport校验结果
typedef struct SOCKET_MSG_PACPORT_UNLOCK_RES_T
{
    char mac[16];
    uint32_t result; //0=失败 1=成功
    char trace_id[64];
    char master_account[32]; //房间主账号Account
    SOCKET_MSG_PACPORT_UNLOCK_RES_T()
    {
        memset(this, 0, sizeof(*this));
    }
}SOCKET_MSG_PACPORT_UNLOCK_RES;

typedef struct SOCKET_MSG_DEV_DOORCOM_DELIVERY_MSG_T
{
    char apt_num[128];
    char box_num[16];
    int status;
    SOCKET_MSG_DEV_DOORCOM_DELIVERY_MSG_T()
    {
        memset(this, 0, sizeof(*this));
    }
}SOCKET_MSG_DEV_DOORCOM_DELIVERY_MSG;


typedef struct SOCKET_MSG_DEL_KIT_DEVICE_T
{
    char mac[20]; // 被删除的mac 
    char msg_seq[32];
    SOCKET_MSG_DEL_KIT_DEVICE_T() {
        memset(this, 0, sizeof(*this));
    }
}SOCKET_MSG_DEL_KIT_DEVICE;

typedef struct SOCKET_MSG_KIT_DEL_ROOM_T
{
    char msg_seq[32];
    SOCKET_MSG_KIT_DEL_ROOM_T() {
        memset(this, 0, sizeof(*this));
    }
}SOCKET_MSG_KIT_DEL_ROOM;

typedef struct SOCKET_MSG_KIT_RESET_ROOM_T
{
    char msg_seq[32];
    SOCKET_MSG_KIT_RESET_ROOM_T() {
        memset(this, 0, sizeof(*this));
    }
}SOCKET_MSG_KIT_RESET_ROOM;
typedef struct SOCKET_MSG_DEV_REPORT_RELAY_STATUS_T
{
    char relay_ids[16]; //上报开启的relayid，从1开始
    int relay_type; //relay类型
    SOCKET_MSG_DEV_REPORT_RELAY_STATUS_T()
    {
        memset(this, 0, sizeof(*this));
    }
}SOCKET_MSG_DEV_REPORT_RELAY_STATUS;

typedef struct SOCKET_MSG_APP_REQUEST_CHANGE_RELAY_T
{
    char mac[32];
    int relay_id;
    int relay_type;
    int relay_switch;
    SOCKET_MSG_APP_REQUEST_CHANGE_RELAY_T()
    {
        memset(this, 0, sizeof(*this));
    }
}SOCKET_MSG_APP_REQUEST_CHANGE_RELAY;

// 反潜回开门请求
typedef struct SOCKET_MSG_REQ_ANTI_PASSBACK_OPEN_T
{
    AntiPassbackAccessMode  access_mode;       // 0-Normal, 1-Entry, 2-Exit, 3-Entry Violation(违规进入), 4-Exit Violation(违规出去)
    int  act_type;          // csmain::ActType
    char trace_id[64];      // 请求唯一标识
    char personnel_id[64];  // user文件的PerID, tmpkey的code
    int relay;
    int security_relay;
    AntiPassbackInitiatorType initiator_type;
    int expire_time;
    SOCKET_MSG_REQ_ANTI_PASSBACK_OPEN_T() {
        memset(this, 0, sizeof(*this));
    }
}SOCKET_MSG_REQ_ANTI_PASSBACK_OPEN;

// 反潜回开门响应
typedef struct SOCKET_MSG_RESP_ANTI_PASSBACK_OPEN_T
{
    AntiPassbackResult  result;            // 0-允许开门, 1-拒绝开门
    AntiPassbackAccessMode  access_mode;       // 0-Normal, 1-Entry, 2-Exit, 3-Entry Violation(违规进入), 4-Exit Violation(违规出去)
    char mac[32];
    char trace_id[64];      // 请求唯一标识
    char personnel_id[64];  // user文件的PerID

    SOCKET_MSG_RESP_ANTI_PASSBACK_OPEN_T() {
        memset(this, 0, sizeof(*this));
    }
}SOCKET_MSG_RESP_ANTI_PASSBACK_OPEN;

//南美身份证校验信息
typedef struct SOCKET_MSG_REPORT_CHECK_ID_ACCESS_T
{
    char id_access_run[16];
    char id_access_serial[16];
    char msg_seq[16];
    char protocal[16];
    SOCKET_MSG_REPORT_CHECK_ID_ACCESS_T()
    {
        memset(this, 0, sizeof(*this));
    }
}SOCKET_MSG_REPORT_CHECK_ID_ACCESS;

typedef struct SOCKET_MSG_TO_DEVICE_CHECK_ID_ACCESS_T
{
    int check_res; //0:校验失败 1:校验成功
    char msg_seq[16];
    char relay[16];
    char security_relay[16];
    char protocal[16];
    char visitor_uuid[36];
    char initiator[36];
    char visitor_name[128];
    SOCKET_MSG_TO_DEVICE_CHECK_ID_ACCESS_T()
    {
        memset(this, 0, sizeof(*this));
    }
}SOCKET_MSG_TO_DEVICE_CHECK_ID_ACCESS;

typedef struct SOCKET_MSG_REQUEST_RECORD_VIDEO_T
{
    char call_trace_id[64];
    VideoRecordCallType type;
    VideoRecordPlayStatus status;
    int is_answer;
    int answer_duration;
    char answer_name[COMMON_STR_LEN];
    int is_group_call;
    SOCKET_MSG_REQUEST_RECORD_VIDEO_T() {
        memset(this, 0, sizeof(*this));
    }
} SOCKET_MSG_REQUEST_RECORD_VIDEO ;


typedef struct SOCKET_MSG_REQUEST_STOP_RECORD_VIDEO_T
{
    char mac[64];
    char site[32];
    SOCKET_MSG_REQUEST_STOP_RECORD_VIDEO_T() {
        memset(this, 0, sizeof(*this));
    }
} SOCKET_MSG_REQUEST_STOP_RECORD_VIDEO;

typedef struct SOCKET_MSG_DEVICE_REQUEST_OPEN_DOOR_T
{
    char     protocal[PROTOCAL_SIZE];
    char     mac[MAC_SIZE];
    char     relay[8];
    char     trace_id[64];

} SOCKET_MSG_DEVICE_REQUEST_OPEN_DOOR;

typedef struct SOCKET_MSG_REPORT_MUSTER_USER_T
{
    MusterType  muster_type;
    char        muster_user[32];

    SOCKET_MSG_REPORT_MUSTER_USER_T() {
        memset(this, 0, sizeof(*this));
    }
} SOCKET_MSG_REPORT_MUSTER_USER;

typedef struct SOCKET_MSG_OPEN_DOOR_ACK_T
{
    char     protocal[PROTOCAL_SIZE];
    int      result;

} SOCKET_MSG_OPEN_DOOR_ACK_T;

typedef struct SOCKET_MSG_REQUEST_LOCKDOWN_T
{
    char mode[8];
    char mac[32];
    char msg_uuid[64];
    char relay[8];
    char security_relay[8];
    char device_uuid[64];
    SOCKET_MSG_REQUEST_LOCKDOWN_T() {
        memset(this, 0, sizeof(*this));
    }
}SOCKET_MSG_REQUEST_LOCKDOWN;

typedef struct SOCKET_MSG_RESPONSE_LOCKDOWN_T
{
    char uuid[64];
    char mode[8];
    char relay[64];
    char security_relay[64];
    char device_uuid[64];
    SOCKET_MSG_RESPONSE_LOCKDOWN_T() {
        memset(this, 0, sizeof(*this));
    }
}SOCKET_MSG_RESPONSE_LOCKDOWN;

typedef struct SOCKET_MSG_REPORT_MAILBOX_ARRIVAL_NOTICE_T
{
    char account[16];
    char title[256];
    char content[2048];
    SOCKET_MSG_REPORT_MAILBOX_ARRIVAL_NOTICE_T()
    {
        memset(this, 0, sizeof(*this));
    }
}SOCKET_MSG_REPORT_MAILBOX_ARRIVAL_NOTICE;

typedef struct SOCKET_MSG_DEVICE_REPORT_VIDEO_RECORD_T
{
    char pic_name[256];
    char video_record_name[256];

} SOCKET_MSG_DEVICE_REPORT_VIDEO_RECORD;

typedef struct SOCKET_MSG_DEVICE_REPORT_IP_CALL_RECORD_T
{
    char caller[32];
    char callee[32];
    char call_trace_id[64];
    char caller_name[64];
    char callee_name[64];
    int is_group_call;
    int is_answer;
    int duration;
    uint64_t start_time_stamp;
    uint64_t answer_time_stamp;
    char trace_id[64];
    SOCKET_MSG_DEVICE_REPORT_IP_CALL_RECORD_T() {
        memset(this, 0, sizeof(*this));
    }
} SOCKET_MSG_DEVICE_REPORT_IP_CALL_RECORD;


#endif
