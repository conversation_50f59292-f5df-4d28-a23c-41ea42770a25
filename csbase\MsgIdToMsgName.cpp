#include "AkcsMsgDef.h"
#include "DclientMsgDef.h"
#include "MsgIdToMsgName.h"
#include "AkcsWebMsgSt.h"
#include <map>

std::map<int, std::string> akcs_message_map = {
    // logic srv向csroute注册服务id
    {AKCS_MSG_L2R_REG_UID_REQ, "AKCS_MSG_L2R_REG_UID_REQ"},
    {AKCS_MSG_L2R_REG_UID_RESP, "AKCS_MSG_L2R_REG_UID_RESP"},
    {AKCS_MSG_R2L_PING_REQ, "AKCS_MSG_R2L_PING_REQ"},
    {AKCS_MSG_R2L_PING_RESP, "AKCS_MSG_R2L_PING_RESP"},
    {AKCS_MSG_L2R_START_RTSP_REQ, "AKCS_MSG_L2R_START_RTSP_REQ"},
    {AKCS_MSG_L2R_STOP_RTSP_REQ, "AKCS_MSG_L2R_STOP_RTSP_REQ"},
    {AKCS_MSG_L2R_KEEPALIVE_RTSP_REQ, "AKCS_MSG_L2R_KEEPALIVE_RTSP_REQ"},

    // csmain向csroute发送mq消息
    {AKCS_M2R_GROUP_COMM_ALARM_REQ, "AKCS_M2R_GROUP_COMM_ALARM_REQ"},
    {AKCS_M2R_GROUP_COMM_ALARM_RESP, "AKCS_M2R_GROUP_COMM_ALARM_RESP"},
    {AKCS_M2R_GROUP_COMM_ALARM_DEAL_REQ, "AKCS_M2R_GROUP_COMM_ALARM_DEAL_REQ"},
    {AKCS_M2R_GROUP_COMM_ALARM_DEAL_RESP, "AKCS_M2R_GROUP_COMM_ALARM_DEAL_RESP"},
    {AKCS_M2R_GROUP_PER_ALARM_REQ, "AKCS_M2R_GROUP_PER_ALARM_REQ"},
    {AKCS_M2R_GROUP_PER_ALARM_RESP, "AKCS_M2R_GROUP_PER_ALARM_RESP"},
    {AKCS_M2R_GROUP_PER_ALARM_DEAL_REQ, "AKCS_M2R_GROUP_PER_ALARM_DEAL_REQ"},
    {AKCS_M2R_GROUP_PER_ALARM_DEAL_RESP, "AKCS_M2R_GROUP_PER_ALARM_DEAL_RESP"},
    {AKCS_M2R_GROUP_PER_MOTION_REQ, "AKCS_M2R_GROUP_PER_MOTION_REQ"},
    {AKCS_M2R_GROUP_PER_MOTION_RESP, "AKCS_M2R_GROUP_PER_MOTION_RESP"},
    {AKCS_M2R_P2P_RTSP_CAPTURE_REQ, "AKCS_M2R_P2P_RTSP_CAPTURE_REQ"},
    {AKCS_M2R_P2P_RTSP_CAPTURE_RESP, "AKCS_M2R_P2P_RTSP_CAPTURE_RESP"},
    {AKCS_M2R_GROUP_MNG_TEXT_MSG_REQ, "AKCS_M2R_GROUP_MNG_TEXT_MSG_REQ"},
    {AKCS_M2R_GROUP_MNG_TEXT_MSG_RESP, "AKCS_M2R_GROUP_MNG_TEXT_MSG_RESP"},
    {AKCS_M2R_P2P_APP_GET_ARMING_MSG_REQ, "AKCS_M2R_P2P_APP_GET_ARMING_MSG_REQ"},
    {AKCS_M2R_P2P_APP_GET_ARMING_MSG_RESP, "AKCS_M2R_P2P_APP_GET_ARMING_MSG_RESP"},
    {AKCS_M2R_P2P_VISITOR_AUTHORIZE_REQ, "AKCS_M2R_P2P_VISITOR_AUTHORIZE_REQ"},
    {AKCS_M2R_P2P_VISITOR_AUTHORIZE_RESP, "AKCS_M2R_P2P_VISITOR_AUTHORIZE_RESP"},
    {AKCS_M2R_P2P_FACE_DATA_FORWARD_REQ, "AKCS_M2R_P2P_FACE_DATA_FORWARD_REQ"},
    {AKCS_M2R_P2P_FACE_DATA_FORWARD_RESP, "AKCS_M2R_P2P_FACE_DATA_FORWARD_RESP"},
    {AKCS_M2R_P2P_NOTIFY_DEV_OFFLINE_REQ, "AKCS_M2R_P2P_NOTIFY_DEV_OFFLINE_REQ"},
    {AKCS_M2R_P2P_NOTIFY_DEV_OFFLINE_RESP, "AKCS_M2R_P2P_NOTIFY_DEV_OFFLINE_RESP"},
    {AKCS_M2R_P2P_OPEN_DOOR_REQ, "AKCS_M2R_P2P_OPEN_DOOR_REQ"},
    {AKCS_M2R_P2P_OPEN_DOOR_RESP, "AKCS_M2R_P2P_OPEN_DOOR_RESP"},
    {AKCS_M2R_P2P_SEND_DELIVERY_REQ, "AKCS_M2R_P2P_SEND_DELIVERY_REQ"},
    {AKCS_M2R_P2P_SEND_DELIVERY_RESP, "AKCS_M2R_P2P_SEND_DELIVERY_RESP"},
    {AKCS_M2R_P2P_SEND_TMPKEY_USED_REQ, "AKCS_M2R_P2P_SEND_TMPKEY_USED_REQ"},
    {AKCS_M2R_P2P_SEND_TMPKEY_USED_RESP, "AKCS_M2R_P2P_SEND_TMPKEY_USED_RESP"},
    {AKCS_M2R_P2P_SEND_REMIND_FLOW_OUT_OF_LIMIT, "AKCS_M2R_P2P_SEND_REMIND_FLOW_OUT_OF_LIMIT"},
    {AKCS_M2R_P2P_CHANGE_RELAY_REQ, "AKCS_M2R_P2P_CHANGE_RELAY_REQ"},
    {AKCS_M2R_P2P_CONTROL_ZIGBEE_DEVICE_REQ, "AKCS_M2R_P2P_CONTROL_ZIGBEE_DEVICE_REQ"},
    {AKCS_M2R_P2P_CHANGE_RELAY_RESP, "AKCS_M2R_P2P_CHANGE_RELAY_RESP"},
    {AKCS_M2R_GROUP_REPORT_RELAY_REQ, "AKCS_M2R_GROUP_REPORT_RELAY_REQ"},
    {AKCS_M2R_GROUP_REPORT_RELAY_RESP, "AKCS_M2R_GROUP_REPORT_RELAY_RESP"},
    {AKCS_M2R_P2P_SEND_VOICE_MSG, "AKCS_M2R_P2P_SEND_VOICE_MSG"},
    {AKCS_M2R_P2P_OPEN_DOOR_ACK, "AKCS_M2R_P2P_OPEN_DOOR_ACK"},
    {AKCS_M2R_GROUP_PUSH_CSLINKER_COMMON_MSG, "AKCS_M2R_GROUP_PUSH_CSLINKER_COMMON_MSG"},
    {AKCS_M2R_P2P_SEND_DELIVERY_MSG, "AKCS_M2R_P2P_SEND_DELIVERY_MSG"},
    {AKCS_M2R_P2P_INDOOR_RELAY_CONTROL_MSG, "AKCS_M2R_P2P_INDOOR_RELAY_CONTROL_MSG"},
    {AKCS_M2R_P2P_SEND_EMERGENCY_NOTIFY_MSG, "AKCS_M2R_P2P_SEND_EMERGENCY_NOTIFY_MSG"},
    {AKCS_M2R_EMERGENCY_DOOR_CONTROL, "AKCS_M2R_EMERGENCY_DOOR_CONTROL"},
    {AKCS_M2R_PUSH_WEB_COMMON_MSG, "AKCS_M2R_PUSH_WEB_COMMON_MSG"},
    {AKCS_M2R_P2P_SEND_MOTION_NOTIFY_MSG, "AKCS_M2R_P2P_SEND_MOTION_NOTIFY_MSG"},
    {AKCS_M2R_P2P_SEND_ZIGBEE_STATUS_CHANGE_MSG, "AKCS_M2R_P2P_SEND_ZIGBEE_STATUS_CHANGE_MSG"},
    {AKCS_M2R_P2P_SEND_TEXT_MSG, "AKCS_M2R_P2P_SEND_TEXT_MSG"},
    {AKCS_M2R_P2P_STOP_VIDEO_RECORD_REQ, "AKCS_M2R_P2P_STOP_VIDEO_RECORD_REQ"},
    {AKCS_M2R_P2P_DEVICE_OPEN_DOOR_MSG, "AKCS_M2R_P2P_DEVICE_OPEN_DOOR_MSG"},
    {AKCS_M2R_P2P_AJAX_MESSAGE_NOTIFY, "AKCS_M2R_P2P_AJAX_MESSAGE_NOTIFY"},

    // office 0x0041
    {AKCS_M2R_GROUP_OFFICE_ALARM_REQ, "AKCS_M2R_GROUP_OFFICE_ALARM_REQ"},
    {AKCS_M2R_GROUP_OFFICE_ALARM_DEAL_REQ, "AKCS_M2R_GROUP_OFFICE_ALARM_DEAL_REQ"},
    {AKCS_M2R_GROUP_OFFICE_PER_MOTION_REQ, "AKCS_M2R_GROUP_OFFICE_PER_MOTION_REQ"},
    {AKCS_M2R_P2P_OFFICE_APP_GET_ARMING_MSG_REQ, "AKCS_M2R_P2P_OFFICE_APP_GET_ARMING_MSG_REQ"},
    {AKCS_M2R_P2P_OFFICE_APP_GET_ARMING_MSG_RESP, "AKCS_M2R_P2P_OFFICE_APP_GET_ARMING_MSG_RESP"},
    {AKCS_M2R_P2P_OFFICE_VISITOR_AUTHORIZE_REQ, "AKCS_M2R_P2P_OFFICE_VISITOR_AUTHORIZE_REQ"},
    {AKCS_M2R_P2P_OFFICE_RTSP_CAPTURE_REQ, "AKCS_M2R_P2P_OFFICE_RTSP_CAPTURE_REQ"},
    {AKCS_M2R_GROUP_OFFICE_MNG_TEXT_MSG_REQ, "AKCS_M2R_GROUP_OFFICE_MNG_TEXT_MSG_REQ"},
    {AKCS_M2R_P2P_OFFICE_OPEN_DOOR_REQ, "AKCS_M2R_P2P_OFFICE_OPEN_DOOR_REQ"},
    {AKCS_M2R_P2P_OFFICE_SEND_DELIVERY_REQ, "AKCS_M2R_P2P_OFFICE_SEND_DELIVERY_REQ"},
    {AKCS_M2R_P2P_OFFICE_SEND_REMIND_FLOW_OUT_OF_LIMIT, "AKCS_M2R_P2P_OFFICE_SEND_REMIND_FLOW_OUT_OF_LIMIT"},
    {AKCS_M2R_P2P_OFFICE_CHANGE_RELAY_REQ, "AKCS_M2R_P2P_OFFICE_CHANGE_RELAY_REQ"},
    {AKCS_M2R_GROUP_OFFICE_REPORT_RELAY_REQ, "AKCS_M2R_GROUP_OFFICE_REPORT_RELAY_REQ"},
    {AKCS_M2R_P2P_NOTIFY_OFFICE_DEV_OFFLINE_REQ, "AKCS_M2R_P2P_NOTIFY_OFFICE_DEV_OFFLINE_REQ"},
    {AKCS_M2R_P2P_OFFICE_SEND_ALARM_NOTIFY_MSG, "AKCS_M2R_P2P_OFFICE_SEND_ALARM_NOTIFY_MSG"},
    {AKCS_M2R_P2P_SEND_TEXT_NOTIFY_MSG, "AKCS_M2R_P2P_SEND_TEXT_NOTIFY_MSG"},
    {AKCS_M2R_P2P_TEMPKEY_USED_NOTIFY_MSG, "AKCS_M2R_P2P_TEMPKEY_USED_NOTIFY_MSG"},

    // 业务p2p
    {AKCS_BUSSNESS_P2P_MSG, "AKCS_BUSSNESS_P2P_MSG"},

    // csvrtsp向csroute发送mq消息
    {AKCS_V2R_START_RTSP_REQ, "AKCS_V2R_START_RTSP_REQ"},
    {AKCS_V2R_START_RTSP_RESP, "AKCS_V2R_START_RTSP_RESP"},
    {AKCS_V2R_STOP_RTSP_REQ, "AKCS_V2R_STOP_RTSP_REQ"},
    {AKCS_V2R_STOP_RTSP_RESP, "AKCS_V2R_STOP_RTSP_RESP"},
    {AKCS_V2R_KEEPALIVE_RTSP_REQ, "AKCS_V2R_KEEPALIVE_RTSP_REQ"},
    {AKCS_V2R_KEEPALIVE_RTSP_RESP, "AKCS_V2R_KEEPALIVE_RTSP_RESP"},

    // route->csmain
    {AKCS_R2M_DEL_DEV_REQ, "AKCS_R2M_DEL_DEV_REQ"},
    {AKCS_R2M_DEL_UID_REQ, "AKCS_R2M_DEL_UID_REQ"},
    {AKCS_R2M_CLEAN_DEV_CODE_REQ, "AKCS_R2M_CLEAN_DEV_CODE_REQ"},
    {AKCS_R2M_START_RTSP_REQ, "AKCS_R2M_START_RTSP_REQ"},
    {AKCS_R2M_START_RTSP_RESP, "AKCS_R2M_START_RTSP_RESP"},
    {AKCS_R2M_STOP_RTSP_REQ, "AKCS_R2M_STOP_RTSP_REQ"},
    {AKCS_R2M_STOP_RTSP_RESP, "AKCS_R2M_STOP_RTSP_RESP"},
    {AKCS_R2M_KEEPALIVE_RTSP_REQ, "AKCS_R2M_KEEPALIVE_RTSP_REQ"},
    {AKCS_R2M_KEEPALIVE_RTSP_RESP, "AKCS_R2M_KEEPALIVE_RTSP_RESP"},
    {AKCS_R2M_UPGRADE_DEV_REQ, "AKCS_R2M_UPGRADE_DEV_REQ"},
    {AKCS_R2M_UPGRADE_DEV_RESP, "AKCS_R2M_UPGRADE_DEV_RESP"},
    {AKCS_R2M_WEATHER_INFO_RESP, "AKCS_R2M_WEATHER_INFO_RESP"},

    // route->csvrtspd
    {AKCS_R2V_START_RTSP_REQ, "AKCS_R2V_START_RTSP_REQ"},
    {AKCS_R2V_START_RTSP_RESP, "AKCS_R2V_START_RTSP_RESP"},
    {AKCS_R2V_STOP_RTSP_REQ, "AKCS_R2V_STOP_RTSP_REQ"},
    {AKCS_R2V_STOP_RTSP_RESP, "AKCS_R2V_STOP_RTSP_RESP"},
    {AKCS_R2V_RTSP_CAPTURE_REQ, "AKCS_R2V_RTSP_CAPTURE_REQ"},
    {AKCS_R2V_RTSP_CAPTURE_RESP, "AKCS_R2V_RTSP_CAPTURE_RESP"},
    {AKCS_R2V_KEEPALIVE_RTSP_REQ, "AKCS_R2V_KEEPALIVE_RTSP_REQ"},
    {AKCS_R2V_KEEPALIVE_RTSP_RESP, "AKCS_R2V_KEEPALIVE_RTSP_RESP"},
    {AKCS_R2V_PCAP_CAPTURE_REQ, "AKCS_R2V_PCAP_CAPTURE_REQ"},
    {AKCS_R2V_PCAP_CAPTURE_RESP, "AKCS_R2V_PCAP_CAPTURE_RESP"},

    // csstorage向csroute发送mq消息
    {AKCS_S2R_P2P_OFFLINE_MSG_ACK_REQ, "AKCS_S2R_P2P_OFFLINE_MSG_ACK_REQ"},
    {AKCS_S2R_P2P_VOICE_MSG_ACK_REQ, "AKCS_S2R_P2P_VOICE_MSG_ACK_REQ"},

    // route->csstorage
    {AKCS_R2S_P2P_OFFLINE_MSG_ACK_REQ, "AKCS_R2S_P2P_OFFLINE_MSG_ACK_REQ"},
    {AKCS_R2S_P2P_VOICE_MSG_ACK_REQ, "AKCS_R2S_P2P_VOICE_MSG_ACK_REQ"},

    // csagent
    {AKCS_B2G_ADD_IPTABLES_REQ, "AKCS_B2G_ADD_IPTABLES_REQ"},
    {AKCS_B2G_DEL_IPTABLES_REQ, "AKCS_B2G_DEL_IPTABLES_REQ"},

    // cslinker->csroute
    {AKCS_L2R_WEATHER_INFO_RESP, "AKCS_L2R_WEATHER_INFO_RESP"},
    {AKCS_L2R_PACPORT_UNLOCK_RESP, "AKCS_L2R_PACPORT_UNLOCK_RESP"},
    {AKCS_L2R_DEV_COMMON_ACK, "AKCS_L2R_DEV_COMMON_ACK"},

    // csroute->csoffice/csresid
    {AKCS_R2B_P2P_WEATHER_INFO_RESP, "AKCS_R2B_P2P_WEATHER_INFO_RESP"},
    {AKCS_R2B_P2P_REFRESH_APP_USERCONF, "AKCS_R2B_P2P_REFRESH_APP_USERCONF"},
    {AKCS_R2B_P2P_PACPORT_UNLOCK_RESP, "AKCS_R2B_P2P_PACPORT_UNLOCK_RESP"},
    {AKCS_R2B_P2P_DEV_COMMON_ACK, "AKCS_R2B_P2P_DEV_COMMON_ACK"},
    {AKCS_R2B_P2P_REFRESH_DEVICE_IS_ATTENDANCE, "AKCS_R2B_P2P_REFRESH_DEVICE_IS_ATTENDANCE"},

    // csroute->siphub
    {AKCS_R2S_P2P_SIP_PCAP_REQ, "AKCS_R2S_P2P_SIP_PCAP_REQ"},

    // csroute->cssmartlock
    {AKCS_R2S_UPDATE_SMARTLOCK_CONFIGURATION_NOTIFY_REQ, "AKCS_R2S_UPDATE_SMARTLOCK_CONFIGURATION_NOTIFY_REQ"},
    {AKCS_R2S_P2P_SMARTLOCK_MSG, "AKCS_R2S_P2P_SMARTLOCK_MSG"},
    {AKCS_R2S_P2P_ACK_SMARTLOCK_MSG, "AKCS_R2S_P2P_ACK_SMARTLOCK_MSG"},
    {AKCS_R2S_P2P_SMARTLOCK_HTTP_UP_MSG, "AKCS_R2S_P2P_SMARTLOCK_HTTP_UP_MSG"},
    //
    {MSG_C2S_SEND_REPORT_STATUS, "MSG_C2S_SEND_REPORT_STATUS"},
    {MSG_C2S_UPDATE_ADDRESS, "MSG_C2S_UPDATE_ADDRESS"},
    {MSG_C2S_REBOOT_DEVICE, "MSG_C2S_REBOOT_DEVICE"},
    {MSG_C2S_ADD_DEV, "MSG_C2S_ADD_DEV"},
    {MSG_C2S_UPDATE_TO_DEVICE, "MSG_C2S_UPDATE_TO_DEVICE"},
    {MSG_C2S_CONFIGURE_TO_DEVICE, "MSG_C2S_CONFIGURE_TO_DEVICE"},
    {MSG_C2S_CONFIGURE_FROM_DEVICE, "MSG_C2S_CONFIGURE_FROM_DEVICE"},
    {MSG_C2S_NOTIFY_APP_CONF_CHANGE, "MSG_C2S_NOTIFY_APP_CONF_CHANGE"},
    {MSG_C2S_NOTIFY_APP_BIND, "MSG_C2S_NOTIFY_APP_BIND"},
    {MSG_C2S_NOTIFY_KEY_CHANGE, "MSG_C2S_NOTIFY_KEY_CHANGE"},
    {MSG_C2S_NOTIFY_UPDATE_NODE, "MSG_C2S_NOTIFY_UPDATE_NODE"},
    {MSG_C2S_NOTIFY_PERSONAL_ALARM_DEAL, "MSG_C2S_NOTIFY_PERSONAL_ALARM_DEAL"},
    {MSG_C2S_PER_SEND_REPORT_STATUS, "MSG_C2S_PER_SEND_REPORT_STATUS"},
    {MSG_C2S_PER_SEND_DEL_DEV, "MSG_C2S_PER_SEND_DEL_DEV"},
    {MSG_C2S_PER_SEND_DEL_UID, "MSG_C2S_PER_SEND_DEL_UID"},
    {MSG_C2S_PER_SEND_TEXT_MSG, "MSG_C2S_PER_SEND_TEXT_MSG"},
    {MSG_C2S_PER_SEND_CREATE_UID_MAIL, "MSG_C2S_PER_SEND_CREATE_UID_MAIL"},
    {MSG_C2S_PER_SEND_RESET_PWD_MAIL, "MSG_C2S_PER_SEND_RESET_PWD_MAIL"},
    {MSG_C2S_PER_SEND_CHANGE_PWD_MAIL, "MSG_C2S_PER_SEND_CHANGE_PWD_MAIL"},
    {MSG_C2S_NOTIFY_UPDATE_COMMUNITY_NODE, "MSG_C2S_NOTIFY_UPDATE_COMMUNITY_NODE"},
    {MSG_C2S_NOTIFY_COMMUNITY_ALARM_DEAL, "MSG_C2S_NOTIFY_COMMUNITY_ALARM_DEAL"},
    {MSG_C2S_NOTIFY_CONFIG_FILE_CHANGE, "MSG_C2S_NOTIFY_CONFIG_FILE_CHANGE"},
    {MSG_C2S_DEV_APP_EXPIRE, "MSG_C2S_DEV_APP_EXPIRE"},
    {MSG_C2S_DEV_APP_WILL_BE_EXPIRE, "MSG_C2S_DEV_APP_WILL_BE_EXPIRE"},
    {MSG_C2S_FREETRIAL_WILL_BE_EXPIRE, "MSG_C2S_FREETRIAL_WILL_BE_EXPIRE"},
    {MSG_C2S_PER_SEND_CHECK_CODE_MAIL, "MSG_C2S_PER_SEND_CHECK_CODE_MAIL"},
    {MSG_C2S_DEV_NOT_EXPIRE, "MSG_C2S_DEV_NOT_EXPIRE"},
    {MSG_C2S_DEV_CLEAN_DEV_CODE, "MSG_C2S_DEV_CLEAN_DEV_CODE"},
    {MSG_C2S_ADD_VIDEO_STORAGE_SCHED, "MSG_C2S_ADD_VIDEO_STORAGE_SCHED"},
    {MSG_C2S_DEL_VIDEO_STORAGE_SCHED, "MSG_C2S_DEL_VIDEO_STORAGE_SCHED"},
    {MSG_C2S_DEL_VIDEO_STORAGE, "MSG_C2S_DEL_VIDEO_STORAGE"},
    {MSG_C2S_DEV_CHANGE, "MSG_C2S_DEV_CHANGE"},
    {MSG_C2S_ACCOUNT_ACTIVE, "MSG_C2S_ACCOUNT_ACTIVE"},
    {MSG_C2S_PM_ACCOUNT_ACTIVE, "MSG_C2S_PM_ACCOUNT_ACTIVE"},
    {MSG_C2S_SHARE_TMPKEY, "MSG_C2S_SHARE_TMPKEY"},
    {MSG_C2S_REMOTE_OPENDOOR, "MSG_C2S_REMOTE_OPENDOOR"},
    {MSG_C2S_REMOTE_OPEN_SECURITY_RELAY, "MSG_C2S_REMOTE_OPEN_SECURITY_RELAY"},
    {MSG_C2S_CREATE_PROPERTY_WORK, "MSG_C2S_CREATE_PROPERTY_WORK"},
    {MSG_C2S_RENEW_SERVER, "MSG_C2S_RENEW_SERVER"},
    {MSG_C2S_PM_WILL_EXPIRE, "MSG_C2S_PM_WILL_EXPIRE"},
    {MSG_C2S_PM_EMERGENCY_DOOR_CONTROL, "MSG_C2S_PM_EMERGENCY_DOOR_CONTROL"},
    {MSG_C2S_ALEXA_LOGIN_MSG, "MSG_C2S_ALEXA_LOGIN_MSG"},
    {MSG_C2S_ALEXA_SET_ARMING_MSG, "MSG_C2S_ALEXA_SET_ARMING_MSG"},
    {MSG_C2S_PHONE_EXPIRE, "MSG_C2S_PHONE_EXPIRE"},
    {MSG_C2S_PHONE_WILL_EXPIRE, "MSG_C2S_PHONE_WILL_EXPIRE"},
    {MSG_C2S_INSTALLER_PHONE_WILL_EXPIRE, "MSG_C2S_INSTALLER_PHONE_WILL_EXPIRE"},
    {MSG_C2S_INSTALLER_APP_WILL_EXPIRE, "MSG_C2S_INSTALLER_APP_WILL_EXPIRE"},
    {MSG_C2S_CREATE_REMOTE_DEV_CONTORL, "MSG_C2S_CREATE_REMOTE_DEV_CONTORL"},
    {MSG_C2S_NOTIFY_FACESERVER_PIC_DOWNLOAD, "MSG_C2S_NOTIFY_FACESERVER_PIC_DOWNLOAD"},
    {MSG_C2S_NOTIFY_FACESERVER_PIC_MODIFY, "MSG_C2S_NOTIFY_FACESERVER_PIC_MODIFY"},
    {MSG_C2S_NOTIFY_FACESERVER_PIC_DELETE, "MSG_C2S_NOTIFY_FACESERVER_PIC_DELETE"},
    {MSG_C2S_NOTIFY_FACESERVER_PIC_BATCH_DOWNLOAD, "MSG_C2S_NOTIFY_FACESERVER_PIC_BATCH_DOWNLOAD"},
    {MSG_C2S_NOTIFY_FACESERVER_PIC_BATCH_MODIFY, "MSG_C2S_NOTIFY_FACESERVER_PIC_BATCH_MODIFY"},
    {MSG_C2S_NOTIFY_FACESERVER_PIC_BATCH_DELETE, "MSG_C2S_NOTIFY_FACESERVER_PIC_BATCH_DELETE"},
    {MSG_C2S_SEND_SMS_CODE, "MSG_C2S_SEND_SMS_CODE"},
    {MSG_C2S_PM_EXPORT_LOG, "MSG_C2S_PM_EXPORT_LOG"},
    {MSG_C2S_REFRESH_CONN_CACHE, "MSG_C2S_REFRESH_CONN_CACHE"},
    {MSG_C2S_KEEP_OPEN_RELAY, "MSG_C2S_KEEP_OPEN_RELAY"},
    {MSG_C2S_KEEP_CLOSE_RELAY, "MSG_C2S_KEEP_CLOSE_RELAY"},
    {MSG_C2S_NOTIFY_FILE_CHANGE, "MSG_C2S_NOTIFY_FILE_CHANGE"},
    {MSG_C2S_PM_FEATURE_WILL_EXPIRE, "MSG_C2S_PM_FEATURE_WILL_EXPIRE"},
    {MSG_C2S_INSTALLER_FEATURE_WILL_EXPIRE, "MSG_C2S_INSTALLER_FEATURE_WILL_EXPIRE"},
    {MSG_C2S_SEND_SMS_DELETE_APP_ACCOUNT, "MSG_C2S_SEND_SMS_DELETE_APP_ACCOUNT"},
    {MSG_C2S_SEND_MAIL_DELETE_APP_ACCOUNT, "MSG_C2S_SEND_MAIL_DELETE_APP_ACCOUNT"},
    {MSG_C2S_SEND_SMS_CREATE_UID, "MSG_C2S_SEND_SMS_CREATE_UID"},
    {MSG_C2S_SEND_SMS_CHANGE_PWD, "MSG_C2S_SEND_SMS_CHANGE_PWD"},
    {MSG_C2S_PM_APP_SEND_CREATE_UID_MAIL, "MSG_C2S_PM_APP_SEND_CREATE_UID_MAIL"},
    {MSG_C2S_PM_APP_ACCOUNT_WILL_EXPIRE, "MSG_C2S_PM_APP_ACCOUNT_WILL_EXPIRE"},
    {MSG_C2S_PM_APP_ACCOUNT_EXPIRE, "MSG_C2S_PM_APP_ACCOUNT_EXPIRE"},
    {MSG_C2S_PM_RENEW_SERVER, "MSG_C2S_PM_RENEW_SERVER"},
    {MSG_C2S_RESET_DEVICE, "MSG_C2S_RESET_DEVICE"},
    {MSG_C2S_SEND_USER_ADD_NEWSITE, "MSG_C2S_SEND_USER_ADD_NEWSITE"},
    {MSG_C2S_SEND_PM_WEB_LINK_NEWSITES, "MSG_C2S_SEND_PM_WEB_LINK_NEWSITES"},
    {MSG_C2S_PM_WEB_CREATE_UID_MAIL, "MSG_C2S_PM_WEB_CREATE_UID_MAIL"},
    {MSG_C2S_PM_WEB_CHANGE_UID_MAIL, "MSG_C2S_PM_WEB_CHANGE_UID_MAIL"},
    {MSG_C2S_SEND_CODE_TO_EMAIL, "MSG_C2S_SEND_CODE_TO_EMAIL"},
    {MSG_C2S_SEND_CODE_TO_MOBILE, "MSG_C2S_SEND_CODE_TO_MOBILE"},
    {MSG_C2S_CHANGE_MAIN_SITE, "MSG_C2S_CHANGE_MAIN_SITE"},
    {MSG_C2S_PCAP_CAPTURE_CONTROL, "MSG_C2S_PCAP_CAPTURE_CONTROL"},
    {MSG_C2S_SEND_EMAIL_NOTIFY, "MSG_C2S_SEND_EMAIL_NOTIFY"},
    {MSG_C2S_REQUEST_DEV_DEL_LOG, "MSG_C2S_REQUEST_DEV_DEL_LOG"},
    {MSG_C2S_REFRESH_APP_CONF, "MSG_C2S_REFRESH_APP_CONF"},
    {MSG_C2S_OFFICE_SEND_CREATE_UID_MAIL, "MSG_C2S_OFFICE_SEND_CREATE_UID_MAIL"},
    {MSG_C2S_OFFICE_SEND_ACCOUNT_RENEW_MAIL, "MSG_C2S_OFFICE_SEND_ACCOUNT_RENEW_MAIL"},
    {MSG_C2S_OFFICE_SEND_PM_ACCOUNT_WILL_EXPIRE_MAIL, "MSG_C2S_OFFICE_SEND_PM_ACCOUNT_WILL_EXPIRE_MAIL"},
    {MSG_C2S_OFFICE_SEND_RESET_PWD_MAIL, "MSG_C2S_OFFICE_SEND_RESET_PWD_MAIL"},
    {MSG_C2S_OFFICE_SEND_CHANGE_PWD_MAIL, "MSG_C2S_OFFICE_SEND_CHANGE_PWD_MAIL"},
    {MSG_C2S_OFFICE_NOTIFY_CONFIG_FILE_CHANGE, "MSG_C2S_OFFICE_NOTIFY_CONFIG_FILE_CHANGE"},
    {MSG_C2S_OFFICE_SEND_PM_ACCOUNT_EXPIRE_MAIL, "MSG_C2S_OFFICE_SEND_PM_ACCOUNT_EXPIRE_MAIL"},
    {MSG_C2S_OFFICE_SEND_PM_FEATURE_WILL_EXPIRE_MAIL, "MSG_C2S_OFFICE_SEND_PM_FEATURE_WILL_EXPIRE_MAIL"},
    {MSG_C2S_OFFICE_SEND_INSTALLER_FEATURE_WILL_EXPIRE_MAIL, "MSG_C2S_OFFICE_SEND_INSTALLER_FEATURE_WILL_EXPIRE_MAIL"},
    {MSG_C2S_OFFICE_SEND_PM_FEATURE_EXPIRE_MAIL, "MSG_C2S_OFFICE_SEND_PM_FEATURE_EXPIRE_MAIL"},
    {MSG_C2S_OFFICE_SEND_INSTALLER_FEATURE_EXPIRE_MAIL, "MSG_C2S_OFFICE_SEND_INSTALLER_FEATURE_EXPIRE_MAIL"},
    {MSG_C2S_OFFICE_SEND_USER_ADD_NEWSITE, "MSG_C2S_OFFICE_SEND_USER_ADD_NEWSITE"},
    {MSG_C2S_SIP_PCAP_CAPTURE_CONTROL, "MSG_C2S_SIP_PCAP_CAPTURE_CONTROL"},
    {MSG_C2S_NEW_OFFICE_EXPORT_LOG, "MSG_C2S_NEW_OFFICE_EXPORT_LOG"},
    {MSG_C2S_OFFICE_DEVICE_SEND_IS_ATTENDANCE, "MSG_C2S_OFFICE_DEVICE_SEND_IS_ATTENDANCE"},
    
};

std::map<int, std::string> declient_message_map = {
    {MSG_TO_DEVICE_REQUEST_CONNECTION, "MSG_TO_DEVICE_REQUEST_CONNECTION"},
    {MSG_TO_DEVICE_REQUEST_STATUS, "MSG_TO_DEVICE_REQUEST_STATUS"},
    {MSG_TO_DEVICE_REQUEST_CONFIG, "MSG_TO_DEVICE_REQUEST_CONFIG"},
    {MSG_TO_DEVICE_UPDATE_CONFIG, "MSG_TO_DEVICE_UPDATE_CONFIG"},
    {MSG_TO_DEVICE_FILE_START, "MSG_TO_DEVICE_FILE_START"},
    {MSG_TO_DEVICE_FILE_DATA, "MSG_TO_DEVICE_FILE_DATA"},
    {MSG_TO_DEVICE_FILE_END, "MSG_TO_DEVICE_FILE_END"},
    {MSG_TO_DEVICE_UPGRADE_START, "MSG_TO_DEVICE_UPGRADE_START"},
    {MSG_TO_DEVICE_REQUEST_FILE, "MSG_TO_DEVICE_REQUEST_FILE"},
    {MSG_TO_DEVICE_REMOTE_CONTROL, "MSG_TO_DEVICE_REMOTE_CONTROL"},
    {MSG_TO_DEVICE_ACK, "MSG_TO_DEVICE_ACK"},
    {MSG_TO_DEVICE_SEND_DISCOVER, "MSG_TO_DEVICE_SEND_DISCOVER"},
    {MSG_TO_DEVICE_ACK_DISCOVER, "MSG_TO_DEVICE_ACK_DISCOVER"},
    {MSG_TO_DEVICE_PUSH_AD, "MSG_TO_DEVICE_PUSH_AD"},
    {MSG_TO_DEVICE_SEND_OWNER_MESSAGE, "MSG_TO_DEVICE_SEND_OWNER_MESSAGE"}, //added by chenyc,2018-01-31, 已弃用
    {MSG_TO_DEVICE_KEY_SEND, "MSG_TO_DEVICE_KEY_SEND"},
    {MSG_TO_DEVICE_UPGRADE_SEND, "MSG_TO_DEVICE_UPGRADE_SEND"},
    {MSG_TO_DEVICE_AD_SEND, "MSG_TO_DEVICE_AD_SEND"},
    {MSG_TO_DEVICE_REQUEST_CONFIG_UDP, "MSG_TO_DEVICE_REQUEST_CONFIG_UDP"},
    {MSG_TO_DEVICE_UPDATE_CONFIG_UDP, "MSG_TO_DEVICE_UPDATE_CONFIG_UDP"},
    {MSG_TO_DEVICE_ALARM_SEND, "MSG_TO_DEVICE_ALARM_SEND"},
    {MSG_TO_DEVICE_SEND_TEXT_MESSAGE, "MSG_TO_DEVICE_SEND_TEXT_MESSAGE"},
    {MSG_TO_DEVICE_CHECK_TMP_KEY_ACK, "MSG_TO_DEVICE_CHECK_TMP_KEY_ACK"}, //云平台校验临时秘钥的响应
    {MSG_TO_DEVICE_CREATE_BIND_CODE_ACK, "MSG_TO_DEVICE_CREATE_BIND_CODE_ACK"}, //云平台创建绑定码的响应
    {MSG_TO_DEVICE_DELETE_BIND_CODE_ACK, "MSG_TO_DEVICE_DELETE_BIND_CODE_ACK"}, //云平台解除绑定码的响应
    {MSG_TO_DEVICE_BIND_CODE_LIST_ACK, "MSG_TO_DEVICE_BIND_CODE_LIST_ACK"}, //云平台下发所有绑定列表
    {MSG_TO_DEVICE_NOTIFY_BIND_CODE_CHANGE, "MSG_TO_DEVICE_NOTIFY_BIND_CODE_CHANGE"}, //云平台下发绑定状态改变的通知
    {MSG_TO_DEVICE_NOTIFY_ALARM_OCCURED, "MSG_TO_DEVICE_NOTIFY_ALARM_OCCURED"}, //云平台下发alarm告警的通知
    {MSG_TO_DEVICE_NOTIFY_ALARM_DEAL, "MSG_TO_DEVICE_NOTIFY_ALARM_DEAL"}, //云平台下发告警已经处理完毕的通知
    {MSG_TO_DEVICE_NOTIFY_ALARM_ACK, "MSG_TO_DEVICE_NOTIFY_ALARM_ACK"}, //云平台响应设备的告警
    {MSG_TO_DEVICE_NOTIFY_CONF_CHANGE, "MSG_TO_DEVICE_NOTIFY_CONF_CHANGE"}, //云平台下发设备/app配置信息改变的通知
    {MSG_TO_DEVICE_APP_IDENTITY_ACK, "MSG_TO_DEVICE_APP_IDENTITY_ACK"}, //云平台响应app身份识别结果(已弃用,2017*08-25)
    {MSG_TO_DEVICE_RECONN_ACCESS_SERVER, "MSG_TO_DEVICE_RECONN_ACCESS_SERVER"}, //云平台下发设备\app重新连接接入服务器的命令
    {MSG_TO_DEVICE_START_RTSP, "MSG_TO_DEVICE_START_RTSP"}, //云平台下发设备启动RTSP监控
    {MSG_TO_DEVICE_STOP_RTSP, "MSG_TO_DEVICE_STOP_RTSP"}, //云平台下发设备停止RTSP监控
    {MSG_TO_DEVICE_SEND_DEVICE_LIST, "MSG_TO_DEVICE_SEND_DEVICE_LIST"}, //云平台下发同一联动单元内的设备列表,用于个人终端用户设备
    {MSG_TO_DEVICE_SEND_DEVICE_LIST_CHANGE, "MSG_TO_DEVICE_SEND_DEVICE_LIST_CHANGE"}, //云平台下发同一联动单元内的设备列表发生变化的通知,用于个人终端用户设备
    {MSG_TO_DEVICE_APP_REPORT_STATUS_ACK, "MSG_TO_DEVICE_APP_REPORT_STATUS_ACK"}, //云平台响应app(android/ios)的状态上报消息
    {MSG_TO_DEVICE_APP_LOGOUT_SIP, "MSG_TO_DEVICE_APP_LOGOUT_SIP"}, //平台下发设备\app注销sip的信令
    {MSG_FROM_DEVICE_HANDLE_ARMING, "MSG_FROM_DEVICE_HANDLE_ARMING"}, //设备给设备下发布防、撤防的信令
    {MSG_TO_APP_NOTIFY_MOTION_OCCURED, "MSG_TO_APP_NOTIFY_MOTION_OCCURED"}, //云平台下发motion alert的通知给app
    {MSG_TO_DEVICE_HANDLE_ARMING, "MSG_TO_DEVICE_HANDLE_ARMING"}, //平台给设备下发布防、撤防的信令
    {MSG_TO_APP_RESP_DEV_ARMING_STATUS, "MSG_TO_APP_RESP_DEV_ARMING_STATUS"}, //平台给app响应设备布防、撤防状态的消息
    {MSG_TO_DEVICE_KEEP_RTSP, "MSG_TO_DEVICE_KEEP_RTSP"}, //平台发送给设备保持RTSP连接
    {MSG_TO_DEVICE_QUIT_NODE, "MSG_TO_DEVICE_QUIT_NODE"}, //平台发送给设备退出联动系统的指令,设备需要做注销联系人\注销sip账号等一系列清理动作
    {MSG_TO_DEVICE_APP_LOGIN_RESP, "MSG_TO_DEVICE_APP_LOGIN_RESP"}, //app登陆后放回一些信息给app ，目前只有未读消息id
    {MSG_TO_DEVICE_CONTACT_URL, "MSG_TO_DEVICE_CONTACT_URL"}, //发送获取联系人列表的url
    {MSG_TO_DEVICE_CHECK_DTMF_ACK, "MSG_TO_DEVICE_CHECK_DTMF_ACK"}, //校验dtmf按键返回
    {MSG_TO_DEVICE_DEVICE_CODE, "MSG_TO_DEVICE_DEVICE_CODE"}, //平台返回设备码让用户绑定或注册主账号
    {MSG_TO_DEVICE_CLEAR_DEVICE_CODE, "MSG_TO_DEVICE_CLEAR_DEVICE_CODE"}, //清空设备码
    {MSG_TO_DEVICE_APP_FORCE_LOGOUT, "MSG_TO_DEVICE_APP_FORCE_LOGOUT"}, // app多地登陆 被强制退出
    {MSG_TO_DEVICE_HEARBEAT_ACK, "MSG_TO_DEVICE_HEARBEAT_ACK"}, // 心跳回复
    {MSG_TO_DEVICE_VISITOR_AUTH_ACK, "MSG_TO_DEVICE_VISITOR_AUTH_ACK"}, //下发通过访客授权
    {MSG_TO_DEVICE_FACE_DATA_FORWARD, "MSG_TO_DEVICE_FACE_DATA_FORWARD"}, //人脸数据转发
    {MSG_TO_DEVICE_TEMP_KEY_MSG, "MSG_TO_DEVICE_TEMP_KEY_MSG"}, //TempKeyCode下发给X916
    {MSG_TO_DEVICE_REGISTER_FACE, "MSG_TO_DEVICE_REGISTER_FACE"}, //注册人脸信息
    {MSG_TO_DEVICE_MODIFY_FACE, "MSG_TO_DEVICE_MODIFY_FACE"}, //修改人脸信息
    {MSG_TO_DEVICE_DELETE_FACE, "MSG_TO_DEVICE_DELETE_FACE"}, //删除人脸信息
    {MSG_TO_DEVICE_GSFACE_HTTPAPI_LOGIN, "MSG_TO_DEVICE_GSFACE_HTTPAPI_LOGIN"}, //通知设备下载人脸文件信息
    {MSG_TO_DEVICE_SEND_OSS_STS, "MSG_TO_DEVICE_SEND_OSS_STS"}, //下发相应oss sts令牌给设备
    {MSG_TO_DEVICE_NOTIFY_ATTENDANCE_SERVICE, "MSG_TO_DEVICE_NOTIFY_ATTENDANCE_SERVICE"}, //通知设备下发离线log文件//云弃用
    {MSG_FROM_DEVICE_CHECK_KEY, "MSG_FROM_DEVICE_CHECK_KEY"},
    {MSG_FROM_DEVICE_ACK, "MSG_FROM_DEVICE_ACK"},
    {MSG_FROM_DEVICE_REPORT_STATUS, "MSG_FROM_DEVICE_REPORT_STATUS"},
    {MSG_FROM_DEVICE_REPORT_CONFIG, "MSG_FROM_DEVICE_REPORT_CONFIG"},
    {MSG_FROM_DEVICE_BOOTUP, "MSG_FROM_DEVICE_BOOTUP"},
    {MSG_FROM_DEVICE_FILE_START, "MSG_FROM_DEVICE_FILE_START"},
    {MSG_FROM_DEVICE_FILE_DATA, "MSG_FROM_DEVICE_FILE_DATA"},
    {MSG_FROM_DEVICE_FILE_END, "MSG_FROM_DEVICE_FILE_END"},
    {MSG_FROM_DEVICE_ALARM, "MSG_FROM_DEVICE_ALARM"},
    {MSG_FROM_DEVICE_TEXT_MSG, "MSG_FROM_DEVICE_TEXT_MSG"},
    {MSG_FROM_DEVICE_ACCESS_INFO, "MSG_FROM_DEVICE_ACCESS_INFO"},
    {MSG_FROM_DEVICE_HEART_BEAT, "MSG_FROM_DEVICE_HEART_BEAT"},
    {MSG_TO_DEVICE_UPDATE_HEARTBEAT_PERIOD, "MSG_TO_DEVICE_UPDATE_HEARTBEAT_PERIOD"},
    {MSG_FROM_APP_REQUEST_CONFIG, "MSG_FROM_APP_REQUEST_CONFIG"}, //APP向云平台请求设备信息
    {MSG_TO_APP_UPDATE_CONFIG, "MSG_TO_APP_UPDATE_CONFIG"}, //云平台响应APP的消息
    {MSG_FROM_DEVICE_CHECK_TMP_KEY, "MSG_FROM_DEVICE_CHECK_TMP_KEY"}, //梯口机校验临时秘钥
    {MSG_FROM_DEVICE_CREATE_BIND_CODE, "MSG_FROM_DEVICE_CREATE_BIND_CODE"}, //室内机请求生成绑定码
    {MSG_FROM_DEVICE_DELETE_BIND_CODE, "MSG_FROM_DEVICE_DELETE_BIND_CODE"}, //室内机请求解绑绑定码
    {MSG_FROM_DEVICE_GET_BIND_CODE_LIST, "MSG_FROM_DEVICE_GET_BIND_CODE_LIST"}, //室内机请求所有绑定列表
    {MSG_FROM_DEVICE_POST_BIND_CODE, "MSG_FROM_DEVICE_POST_BIND_CODE"}, //APP主动向平台推送绑定码 
    {MSG_FROM_DEVICE_PUT_ALARM_DEAL, "MSG_FROM_DEVICE_PUT_ALARM_DEAL"}, //设备或者app向平台推送告警处理的消息
    {MSG_FROM_DEVICE_POST_APP_IDENTITY, "MSG_FROM_DEVICE_POST_APP_IDENTITY"}, //APP主动向平台推送身份识别:账号+密码 (已弃用,2017*08-25)
    {MSG_FROM_ANDROID_REPORT_STATUS, "MSG_FROM_ANDROID_REPORT_STATUS"}, //安卓向平台上报状态
    {MSG_FROM_IOS_REPORT_STATUS, "MSG_FROM_IOS_REPORT_STATUS"}, //IOS向平台上报状态
    {MSG_FROM_DEVICE_REQUEST_DEVICE_LIST, "MSG_FROM_DEVICE_REQUEST_DEVICE_LIST"}, //设备平台请求统一联动单元内的设备列表,用于个人终端用户
    {MSG_FROM_DEVICE_REPORT_ARMING_STATUS, "MSG_FROM_DEVICE_REPORT_ARMING_STATUS"}, //设备上报当前布防状态给平台
    {MSG_FROM_DEVICE_MOTION_ALERT, "MSG_FROM_DEVICE_MOTION_ALERT"}, //平台接受设备端上传的motion alert的消息
    {MSG_FROM_DEVICE_REPORT_ACTIVITY_LOGS, "MSG_FROM_DEVICE_REPORT_ACTIVITY_LOGS"}, //设备上报动作消息给平台
    {MSG_FROM_APP_REQUEST_CAPTURE, "MSG_FROM_APP_REQUEST_CAPTURE"}, //APP请求平台对设备的视频流进行截图
    {MSG_FROM_APP_SET_RECV_MOTION_ALERT_STATUS, "MSG_FROM_APP_SET_RECV_MOTION_ALERT_STATUS"}, //app设置是否接收平台下发的motion alert的通知消息
    {MSG_FROM_APP_HANDLE_DEV_ARMING, "MSG_FROM_APP_HANDLE_DEV_ARMING"}, //app上报对设备进行布防、撤防的信令
    {MSG_FROM_APP_REPORT_LOGOUT, "MSG_FROM_APP_REPORT_LOGOUT"}, //app通知平台logout
    {MSG_FROM_DEVICE_CHECK_DTMF, "MSG_FROM_DEVICE_CHECK_DTMF"}, //校验dtmf按键
    {MSG_FROM_DEVICE_VIDEO_STORAGE_ACTION, "MSG_FROM_DEVICE_VIDEO_STORAGE_ACTION"}, //视频存储信令,含:启动、停止
    {MSG_FROM_DEVICE_CLI_COMMAND_RESP, "MSG_FROM_DEVICE_CLI_COMMAND_RESP"}, //cli 返回
    {MSG_FROM_DEVICE_REPORT_CALL_CAPTURE, "MSG_FROM_DEVICE_REPORT_CALL_CAPTURE"}, //通话截图消息
    {MSG_TO_DEVICE_MAINTENANCE_GETLOG, "MSG_TO_DEVICE_MAINTENANCE_GETLOG"}, //获取设备的log
    {MSG_TO_DEVICE_MAINTENANCE_START_PCAP, "MSG_TO_DEVICE_MAINTENANCE_START_PCAP"}, //开启抓包
    {MSG_TO_DEVICE_MAINTENANCE_STOP_PCAP, "MSG_TO_DEVICE_MAINTENANCE_STOP_PCAP"}, //停止抓包
    {MSG_TO_DEVICE_MAINTENANCE_REBOOT_DEV, "MSG_TO_DEVICE_MAINTENANCE_REBOOT_DEV"}, //重启设备
    {MSG_TO_DEVICE_MAINTENANCE_GET_DEV_CONFIG, "MSG_TO_DEVICE_MAINTENANCE_GET_DEV_CONFIG"}, //获取设备配置文件
    {MSG_TO_DEVICE_MAINTENANCE_RECONNECT_RPS, "MSG_TO_DEVICE_MAINTENANCE_RECONNECT_RPS"}, //重新连接rps
    {MSG_TO_DEVICE_MAINTENANCE_RECONNECT_GATEWAY, "MSG_TO_DEVICE_MAINTENANCE_RECONNECT_GATEWAY"}, //重新连接网关
    {MSG_TO_DEVICE_MAINTENANCE_RECONNECT_ACCESSSERVER, "MSG_TO_DEVICE_MAINTENANCE_RECONNECT_ACCESSSERVER"}, //重新连接接入服务器
    {MSG_TO_DEVICE_CLI_COMMAND, "MSG_TO_DEVICE_CLI_COMMAND"}, //控制终端消息信令
    {MSG_TO_DEVICE_MAINTENANCE_SERVER_CHANGE, "MSG_TO_DEVICE_MAINTENANCE_SERVER_CHANGE"}, //更新服务器地址
    {MSG_TO_DEVICE_DOOR_MOTION_ALERT, "MSG_TO_DEVICE_DOOR_MOTION_ALERT"}, //云平台转发motion消息给室内机
    {MSG_TO_DEVICE_MANAGE_ALARM, "MSG_TO_DEVICE_MANAGE_ALARM"}, //云平台转发alarm消息给R47
    {MSG_FROM_DEVICE_MANAGE_BROADCAST_MSG, "MSG_FROM_DEVICE_MANAGE_BROADCAST_MSG"}, //管理机广播消息
    {MSG_TO_DEVICE_SERVER_HEARTBEAT, "MSG_TO_DEVICE_SERVER_HEARTBEAT"}, //服务器发送确认心跳,目前用于alex
    {MSG_FROM_DEVICE_ACK_HEARTBEAT, "MSG_FROM_DEVICE_ACK_HEARTBEAT"}, //alex设备回复服务器心跳
    {MSG_TO_DEVICE_REQUEST_SENSOR_TRIGGER, "MSG_TO_DEVICE_REQUEST_SENSOR_TRIGGER"},
    {MSG_RROM_DEVICE_RESPONSE_SENSOR_TRIGGER, "MSG_RROM_DEVICE_RESPONSE_SENSOR_TRIGGER"},
    {MSG_FROM_DEVICE_REPORT_VISITOR_MSG, "MSG_FROM_DEVICE_REPORT_VISITOR_MSG"}, //上报访客信息
    {MSG_FROM_DEVICE_APP_REPORT_VISITOR_AUTH_MSG, "MSG_FROM_DEVICE_APP_REPORT_VISITOR_AUTH_MSG"}, //上报访客授权信息
    {MSG_FROM_DEVICE_REQUEST_OSS_STS, "MSG_FROM_DEVICE_REQUEST_OSS_STS"}, //设备请求oss sts令牌
    {MSG_FROM_DEVICE_REMOTE_CONTROL_ACK, "MSG_FROM_DEVICE_REMOTE_CONTROL_ACK"}, //远程控制ACK
    {MSG_FROM_DEVICE_REQUEST_OPENDOOR, "MSG_FROM_DEVICE_REQUEST_OPENDOOR"}, //室内机请求开门
    {MSG_TO_DEVICE_REMOTE_DEV_WEB_CONTORL, "MSG_TO_DEVICE_REMOTE_DEV_WEB_CONTORL"}, //远程设备网页访问
    {MSG_TO_DEVICE_OPENDOOR_ACK, "MSG_TO_DEVICE_OPENDOOR_ACK"}, //给室内机返回开门是否成功
    {MSG_FROM_DEVICE_REQUEST_ACINFO, "MSG_FROM_DEVICE_REQUEST_ACINFO"}, //请求获取联系人
    {MSG_FROM_DEVICE_SEND_DELIVERY_MSG, "MSG_FROM_DEVICE_SEND_DELIVERY_MSG"}, //快递消息
    {MSG_FROM_DEVICE_SYNC_ACTIVITY, "MSG_FROM_DEVICE_SYNC_ACTIVITY"}, //批量上传开门记录
    {MSG_FROM_DEVICE_REPORT_RELAY_STATUS, "MSG_FROM_DEVICE_REPORT_RELAY_STATUS"}, //设备上报当前relay状态
    {MSG_FROM_DEVICE_FLOW_OUT_OF_LIMIT, "MSG_FROM_DEVICE_FLOW_OUT_OF_LIMIT"}, //流量超出限额提醒
    {MSG_FROM_DEVICE_REPORT_ACCESS_TIMES, "MSG_FROM_DEVICE_REPORT_ACCESS_TIMES"},
    {MSG_FROM_DEVICE_SEND_DELIVERY_BOX_MSG, "MSG_FROM_DEVICE_SEND_DELIVERY_BOX_MSG"}, //快递消息 用于JTS
    {MSG_FROM_DEVICE_REQUEST_END_USER_REG, "MSG_FROM_DEVICE_REQUEST_END_USER_REG"}, //请求待注册用户信息
    {MSG_FROM_DEVICE_REPORT_KIT_DEVICES, "MSG_FROM_DEVICE_REPORT_KIT_DEVICES"}, //室内机将设备的固件号和MAC地址传输给云
    {MSG_FROM_DEVICE_ADD_KIT_DEVICES, "MSG_FROM_DEVICE_ADD_KIT_DEVICES"}, //手动添加设备：室内机可手动输入MAC，Location，选择Type告知云去绑定设备
    {MSG_FROM_DEVICE_REQUEST_KIT_DEVICES, "MSG_FROM_DEVICE_REQUEST_KIT_DEVICES"}, //云需要把当前家庭下的设备信息告知室内机
    {MSG_FROM_DEVICE_MODIFY_LOCATION, "MSG_FROM_DEVICE_MODIFY_LOCATION"}, //室内机可修改设备的Location信息，云需要接收并修改
    {MSG_FROM_DEVICE_RESPONSE_EMERGENCY_KEEP_OPEN_DOOR, "MSG_FROM_DEVICE_RESPONSE_EMERGENCY_KEEP_OPEN_DOOR"}, //设备上报一键开门结果
    {MSG_FROM_DEVICE_RESPONSE_EMERGENCY_CLOSE_DOOR, "MSG_FROM_DEVICE_RESPONSE_EMERGENCY_CLOSE_DOOR"}, //设备上报一键关门结果
    {MSG_FROM_DEVICE_REPORT_FILE_MD5, "MSG_FROM_DEVICE_REPORT_FILE_MD5"}, //上报设备md5值
    {MSG_TO_DEVICE_REQUEST_KEEP_OPEN_RELAY, "MSG_TO_DEVICE_REQUEST_KEEP_OPEN_RELAY"}, //常开relay
    {MSG_TO_DEVICE_REQUEST_KEEP_CLOSE_RELAY, "MSG_TO_DEVICE_REQUEST_KEEP_CLOSE_RELAY"}, //常关relay
    {MSG_TO_APP_CHANGE_RELAY_STATUS, "MSG_TO_APP_CHANGE_RELAY_STATUS"},
    {MSG_TO_DEVICE_REG_END_USER, "MSG_TO_DEVICE_REG_END_USER"}, //注册EndUser
    {MSG_TO_DEVICE_REQUEST_IS_KIT, "MSG_TO_DEVICE_REQUEST_IS_KIT"}, //下发给设备是否KIT方案
    {MSG_TO_DEVICE_REPORT_KIT_DEVICES, "MSG_TO_DEVICE_REPORT_KIT_DEVICES"}, //云需要把当前家庭下的设备信息告知室内机
    {MSG_FROM_APP_REQUEST_CHANGE_RELAY, "MSG_FROM_APP_REQUEST_CHANGE_RELAY"}, //APP开关设备relay
    {MSG_TO_DEVICE_REQUEST_EMERGENCY_KEEP_OPEN_DOOR, "MSG_TO_DEVICE_REQUEST_EMERGENCY_KEEP_OPEN_DOOR"}, //PM一键开门
    {MSG_TO_DEVICE_REQUEST_EMERGENCY_CLOSE_DOOR, "MSG_TO_DEVICE_REQUEST_EMERGENCY_CLOSE_DOOR"}, //PM一键关门
    {MSG_FROM_DEVICE_REPORT_VOICE_MSG, "MSG_FROM_DEVICE_REPORT_VOICE_MSG"},
    {MSG_TO_DEVICE_ONLINE_NOTIFY_MSG, "MSG_TO_DEVICE_ONLINE_NOTIFY_MSG"},
    {MSG_FROM_DEVICE_REQUEST_VOICE_MSG_LIST, "MSG_FROM_DEVICE_REQUEST_VOICE_MSG_LIST"},
    {MSG_TO_DEVICE_REPORT_VOICE_MSG_LIST, "MSG_TO_DEVICE_REPORT_VOICE_MSG_LIST"},
    {MSG_FROM_DEVICE_REQUEST_VOICE_MSG_URL, "MSG_FROM_DEVICE_REQUEST_VOICE_MSG_URL"},
    {MSG_TO_DEVICE_REPORT_VOICE_MSG_URL, "MSG_TO_DEVICE_REPORT_VOICE_MSG_URL"},
    {MSG_FROM_DEVICE_REQUEST_DEL_VOICE_MSG, "MSG_FROM_DEVICE_REQUEST_DEL_VOICE_MSG"},
    {MSG_FROM_DEVICE_THIRD_CAMERA_MEDIA_INFO, "MSG_FROM_DEVICE_THIRD_CAMERA_MEDIA_INFO"},
    {MSG_FROM_DEVICE_REQUEST_ACCOUNT_LOGOUT, "MSG_FROM_DEVICE_REQUEST_ACCOUNT_LOGOUT"},
    {MSG_TO_DEVICE_KIT_DEL_LOG, "MSG_TO_DEVICE_KIT_DEL_LOG"},
    {MSG_FROM_DEVICE_REPORT_INPUT_STATUS, "MSG_FROM_DEVICE_REPORT_INPUT_STATUS"},
    {MSG_FROM_DEVICE_REQUEST_WEATHER, "MSG_FROM_DEVICE_REQUEST_WEATHER"},
    {MSG_TO_DEVICE_REPORT_WEATHER_MSG, "MSG_TO_DEVICE_REPORT_WEATHER_MSG"},
    {MSG_FROM_DEVICE_REPORT_TRANS_ACTIVITY_LOGS, "MSG_FROM_DEVICE_REPORT_TRANS_ACTIVITY_LOGS"},
    {MSG_TO_APP_LOGOUT_ACK, "MSG_TO_APP_LOGOUT_ACK"},
    {MSG_FROM_DEVICE_SEND_DOORCOM_DELIVERY_MSG, "MSG_FROM_DEVICE_SEND_DOORCOM_DELIVERY_MSG"},
    {MSG_FROM_DEVICE_PACPORT_REGISTER, "MSG_FROM_DEVICE_PACPORT_REGISTER"},
    {MSG_FROM_DEVICE_REPORT_PACPORT_CHECK_INFO, "MSG_FROM_DEVICE_REPORT_PACPORT_CHECK_INFO"},
    {MSG_FROM_DEVICE_RESPONSE_EXTERN_PUSH_BUTTON, "MSG_FROM_DEVICE_RESPONSE_EXTERN_PUSH_BUTTON"},
    {MSG_FROM_DEVICE_CHECK_VISITOR_IDACCESS, "MSG_FROM_DEVICE_CHECK_VISITOR_IDACCESS"},
    {MSG_TO_DEVICE_CHECK_VISITOR_IDACCESS_ACK, "MSG_TO_DEVICE_CHECK_VISITOR_IDACCESS_ACK"},
    {MSG_TO_DEVICE_SEND_PACPORT_CHECK_RESULT, "MSG_TO_DEVICE_SEND_PACPORT_CHECK_RESULT"},
    {MSG_FROM_DEVICE_REQUEST_CREATE_ROOM, "MSG_FROM_DEVICE_REQUEST_CREATE_ROOM"},
    {MSG_FROM_DEVICE_REQUEST_DELETE_MAC, "MSG_FROM_DEVICE_REQUEST_DELETE_MAC"},
    {MSG_FROM_DEVICE_REQUEST_DELETE_ROOM, "MSG_FROM_DEVICE_REQUEST_DELETE_ROOM"},
    {MSG_FROM_DEVICE_REQUEST_DELETE_USER, "MSG_FROM_DEVICE_REQUEST_DELETE_USER"},
    {MSG_FROM_DEVICE_REQUEST_ANTIPASSBACK_OPEN_DOOR, "MSG_FROM_DEVICE_REQUEST_ANTIPASSBACK_OPEN_DOOR"},
    {MSG_TO_DEVICE_RESPONSE_ANTIPASSBACK_OPEN_DOOR, "MSG_TO_DEVICE_RESPONSE_ANTIPASSBACK_OPEN_DOOR"},
    {MSG_FROM_DEVICE_REQUEST_RECORD_VIDEO_MSG_LIST, "MSG_FROM_DEVICE_REQUEST_RECORD_VIDEO_MSG_LIST"},
    {MSG_TO_DEVICE_RESPONSE_RECORD_VIDEO_MSG_LIST, "MSG_TO_DEVICE_RESPONSE_RECORD_VIDEO_MSG_LIST"},
    {MSG_FROM_DEVICE_REQUEST_PLAY_VIDEO, "MSG_FROM_DEVICE_REQUEST_PLAY_VIDEO"},
    {MSG_TO_DEVICE_RECORD_VIDEO_URL, "MSG_TO_DEVICE_RECORD_VIDEO_URL"},
    {MSG_FROM_APP_REQUEST_STOP_CAPTURE, "MSG_FROM_APP_REQUEST_STOP_CAPTURE"},
    {MSG_TO_APP_CHANGE_RELAY_STATUS_COMMON, "MSG_TO_APP_CHANGE_RELAY_STATUS_COMMON"},
    {MSG_TO_DEVICE_REQUEST_CHANGE_RELAY, "MSG_TO_DEVICE_REQUEST_CHANGE_RELAY"},
    {MSG_TO_DEVAPP_EMERGENCY_CONTROL_NOTIFY, "MSG_TO_DEVAPP_EMERGENCY_CONTROL_NOTIFY"},
    {MSG_FROM_DEVICE_REPORT_TIMEZONE, "MSG_FROM_DEVICE_REPORT_TIMEZONE"},
    {MSG_FROM_INNER_CONN_INFO, "MSG_FROM_INNER_CONN_INFO"},
    {MSG_FROM_INNER_CONN_APP_FORCE_LOGOUT, "MSG_FROM_INNER_CONN_APP_FORCE_LOGOUT"},
    {MSG_FROM_DEVICE_MUSTER_REPORT_USER, "MSG_FROM_DEVICE_MUSTER_REPORT_USER"},
    {MSG_FROM_DEVICE_RESPONSE_LOCKDOWN_DOOR, "MSG_FROM_DEVICE_RESPONSE_LOCKDOWN_DOOR"},
    {MSG_TO_DEVICE_REQUEST_LOCKDOWN_DOOR, "MSG_TO_DEVICE_REQUEST_LOCKDOWN_DOOR"},
    {MSG_FROM_DEVICE_REPORT_ZIGBEE_SUMMARY, "MSG_FROM_DEVICE_REPORT_ZIGBEE_SUMMARY"},
    {MSG_FROM_DEVICE_REPORTE_ZIGBEE_DETAILS, "MSG_FROM_DEVICE_REPORTE_ZIGBEE_DETAILS"},
    {MSG_FROM_DEVICE_REPORT_ZIGBEE_STATUS_CHANGE, "MSG_FROM_DEVICE_REPORT_ZIGBEE_STATUS_CHANGE"},
    {MSG_FROM_APP_REQUEST_CONTROL_ZIGBEE_DEVICE, "MSG_FROM_APP_REQUEST_CONTROL_ZIGBEE_DEVICE"},
    {MSG_TO_DEVICE_REQUEST_ZIGBEE_DETAILS, "MSG_TO_DEVICE_REQUEST_ZIGBEE_DETAILS"},
    {MSG_TO_DEVICE_CONTROL_ZIGBEE_DEVICE, "MSG_TO_DEVICE_CONTROL_ZIGBEE_DEVICE"},
    {MSG_TO_APP_CHANGE_ZIGBEE_DEVICE_STATUS, "MSG_TO_APP_CHANGE_ZIGBEE_DEVICE_STATUS"},
};

std::multimap<int, std::string> config_change_type_map = {
    {CSMAIN_COMM_DEV_IP_CHANGE, "CSMAIN_COMM_DEV_IP_CHANGE"},
    {CSMAIN_COMM_DEV_UPGRADE, "CSMAIN_COMM_DEV_UPGRADE"},
    {CSMAIN_COMM_DEV_MAINTANCE, "CSMAIN_COMM_DEV_MAINTANCE"},
    {CSMAIN_COMM_UNIT_DEV_IP_CHANGE, "CSMAIN_COMM_UNIT_DEV_IP_CHANGE"},
    {CSMAIN_COMM_UNIT_DEV_UPGRADE, "CSMAIN_COMM_UNIT_DEV_UPGRADE"},
    {CSMAIN_COMM_UNIT_DEV_MAINTANCE, "CSMAIN_COMM_UNIT_DEV_MAINTANCE"},
    {CSMAIN_COMM_PUB_DEV_IP_CHANGE, "CSMAIN_COMM_PUB_DEV_IP_CHANGE"},
    {CSMAIN_COMM_PUB_DEV_UPGRADE, "CSMAIN_COMM_PUB_DEV_UPGRADE"},
    {CSMAIN_COMM_PUB_DEV_MAINTANCE, "CSMAIN_COMM_PUB_DEV_MAINTANCE"},
    {CSMAIN_COMM_ACCOUNT_NFC_UPDATE, "CSMAIN_COMM_ACCOUNT_NFC_UPDATE"},
    
    {CSMAIN_OFFICE_DEV_IP_CHANGE, "CSMAIN_OFFICE_DEV_IP_CHANGE"},
    {CSMAIN_OFFICE_DEV_UPGRADE, "CSMAIN_OFFICE_DEV_UPGRADE"},
    {CSMAIN_OFFICE_DEV_MAINTANCE, "CSMAIN_OFFICE_DEV_MAINTANCE"},
    {CSMAIN_OFFICE_UNIT_DEV_IP_CHANGE, "CSMAIN_OFFICE_UNIT_DEV_IP_CHANGE"},
    {CSMAIN_OFFICE_UNIT_DEV_UPGRADE, "CSMAIN_OFFICE_UNIT_DEV_UPGRADE"},
    {CSMAIN_OFFICE_UNIT_DEV_MAINTANCE, "CSMAIN_OFFICE_UNIT_DEV_MAINTANCE"},
    {CSMAIN_OFFICE_PUB_DEV_IP_CHANGE, "CSMAIN_OFFICE_PUB_DEV_IP_CHANGE"},
    {CSMAIN_OFFICE_PUB_DEV_UPGRADE, "CSMAIN_OFFICE_PUB_DEV_UPGRADE"},
    {CSMAIN_OFFICE_PUB_DEV_MAINTANCE, "CSMAIN_OFFICE_PUB_DEV_MAINTANCE"},
    {CSMAIN_OFFICE_ACCOUNT_NFC_UPDATE, "CSMAIN_OFFICE_ACCOUNT_NFC_UPDATE"},
    
    {CSMAIN_PER_DEV_IP_CHANGE, "CSMAIN_PER_DEV_IP_CHANGE"},
    {CSMAIN_PER_DEV_UPGRADE, "CSMAIN_PER_DEV_UPGRADE"},
    {CSMAIN_PER_DEV_MAINTANCE, "CSMAIN_PER_DEV_MAINTANCE"},
    {CSMAIN_PER_DEV_NFC_CHANGE, "CSMAIN_PER_DEV_NFC_CHANGE"},
    
    {WEB_PER_NODE_UPDATE, "WEB_PER_NODE_UPDATE"},
    {WEB_PER_ADD_USER, "WEB_PER_ADD_USER"},
    {WEB_PER_DEL_USER, "WEB_PER_DEL_USER"},
    {WEB_PER_MODIFY_USER, "WEB_PER_MODIFY_USER"},
    {WEB_PER_ADD_SLAVE_USER, "WEB_PER_ADD_SLAVE_USER"},
    {WEB_PER_DEL_SLAVE_USER, "WEB_PER_DEL_SLAVE_USER"},
    {WEB_PER_MODIFY_SLAVE_USER, "WEB_PER_MODIFY_SLAVE_USER"},
    {WEB_PER_UPDATE_RF, "WEB_PER_UPDATE_RF"},
    {WEB_PER_UPDATE_PIN, "WEB_PER_UPDATE_PIN"},
    {WEB_PER_ADD_DEV, "WEB_PER_ADD_DEV"},
    {WEB_PER_DEL_DEV, "WEB_PER_DEL_DEV"},
    {WEB_PER_MODIFY_DEV, "WEB_PER_MODIFY_DEV"},
    {WEB_PER_PHONE_PAY_SUCC, "WEB_PER_PHONE_PAY_SUCC"},
    {WEB_PER_UPLOAD_FACE_PIC, "WEB_PER_UPLOAD_FACE_PIC"},
    {WEB_PER_DELETE_FACE_PIC, "WEB_PER_DELETE_FACE_PIC"},
    {WEB_PER_UPDATE_TIMEZOME, "WEB_PER_UPDATE_TIMEZOME"},
    {WEB_PER_UPDATE_MAC_CONFIG, "WEB_PER_UPDATE_MAC_CONFIG"},
    {WEB_PER_MODIFY_DETECTION_CONFIG, "WEB_PER_MODIFY_DETECTION_CONFIG"},
    {WEB_PER_UPDATE_NODE_CONTACT, "WEB_PER_UPDATE_NODE_CONTACT"},

    {WEB_COMM_NODE_UPDATE, "WEB_COMM_NODE_UPDATE"},
    {WEB_COMM_ADD_USER, "WEB_COMM_ADD_USER"},
    {WEB_COMM_DEL_USER, "WEB_COMM_DEL_USER"},
    {WEB_COMM_MODIFY_USER, "WEB_COMM_MODIFY_USER"},
    {WEB_COMM_ADD_SLAVE_USER, "WEB_COMM_ADD_SLAVE_USER"},
    {WEB_COMM_DEL_SLAVE_USER, "WEB_COMM_DEL_SLAVE_USER"},
    {WEB_COMM_MODIFY_SLAVE_USER, "WEB_COMM_MODIFY_SLAVE_USER"},
    {WEB_COMM_UPDATE_RF, "WEB_COMM_UPDATE_RF"},
    {WEB_COMM_UPDATE_PIN, "WEB_COMM_UPDATE_PIN"},
    {WEB_COMM_ADD_DEV, "WEB_COMM_ADD_DEV"},
    {WEB_COMM_DEL_DEV, "WEB_COMM_DEL_DEV"},
    {WEB_COMM_MODIFY_DEV, "WEB_COMM_MODIFY_DEV"},
    {WEB_COMM_UPLOAD_FACE_PIC, "WEB_COMM_UPLOAD_FACE_PIC"},
    {WEB_COMM_DELETE_FACE_PIC, "WEB_COMM_DELETE_FACE_PIC"},
    {WEB_COMM_UPDATE_MAC_CONFIG, "WEB_COMM_UPDATE_MAC_CONFIG"},
    {WEB_COMM_UPDATE_COMMUNITY_CALLS, "WEB_COMM_UPDATE_COMMUNITY_CALLS"},
    {WEB_COMM_UPDATE_COMMUNITY_PUB_UNIT_CONTACT, "WEB_COMM_UPDATE_COMMUNITY_PUB_UNIT_CONTACT"},
    {WEB_COMM_UPDATE_APT_CALLRULE, "WEB_COMM_UPDATE_APT_CALLRULE"},
    {WEB_COMM_UPDATE_PUB_MAC_CONTACT, "WEB_COMM_UPDATE_PUB_MAC_CONTACT"},
    {WEB_COMM_UPDATE_CONFIG_AND_CONTACT, "WEB_COMM_UPDATE_CONFIG_AND_CONTACT"},
    {WEB_COMM_MODIFY_MAC_CONTACT, "WEB_COMM_MODIFY_MAC_CONTACT"},

    {WEB_COMM_UNIT_UPDATE_RF, "WEB_COMM_UNIT_UPDATE_RF"},
    {WEB_COMM_UNIT_UPDATE_PIN, "WEB_COMM_UNIT_UPDATE_PIN"},
    {WEB_COMM_UNIT_ADD_DEV, "WEB_COMM_UNIT_ADD_DEV"},
    {WEB_COMM_UNIT_DEL_DEV, "WEB_COMM_UNIT_DEL_DEV"},
    {WEB_COMM_UNIT_MODIFY_DEV, "WEB_COMM_UNIT_MODIFY_DEV"},

    {WEB_COMM_PUB_UPDATE_RF, "WEB_COMM_PUB_UPDATE_RF"},
    {WEB_COMM_PUB_UPDATE_PIN, "WEB_COMM_PUB_UPDATE_PIN"},
    {WEB_COMM_PUB_ADD_DEV, "WEB_COMM_PUB_ADD_DEV"},
    {WEB_COMM_PUB_DEL_DEV, "WEB_COMM_PUB_DEL_DEV"},
    {WEB_COMM_PUB_MODIFY_DEV, "WEB_COMM_PUB_MODIFY_DEV"},
    
    {WEB_COMM_INFO, "WEB_COMM_INFO"},
    {WEB_COMM_APT_PIN, "WEB_COMM_APT_PIN"},
    {WEB_COMM_MOTION, "WEB_COMM_MOTION"},
    {WEB_COMM_IMPORT_COMMUNITY, "WEB_COMM_IMPORT_COMMUNITY"},
    {WEB_COMM_ADD_BUILDING, "WEB_COMM_ADD_BUILDING"},
    {WEB_COMM_DEL_BUILDING, "WEB_COMM_DEL_BUILDING"},
    {WEB_COMM_MODIFY_BUILDING, "WEB_COMM_MODIFY_BUILDING"},
    {WEB_COMM_MODIFY_TIMEINFO, "WEB_COMM_MODIFY_TIMEINFO"},
    {WEB_COMM_DELETE_COMMUNITY, "WEB_COMM_DELETE_COMMUNITY"},
    {WEB_COMM_IMPORT_FACE_PIC, "WEB_COMM_IMPORT_FACE_PIC"},
    {WEB_COMM_DELETE_ALL_FACE_PIC, "WEB_COMM_DELETE_ALL_FACE_PIC"},
    {WEB_COMM_DELETE_ALL_RF_CARD, "WEB_COMM_DELETE_ALL_RF_CARD"},
    {WEB_COMM_NOTIFY_FLOW_OUT_OF_LIMIT, "WEB_COMM_NOTIFY_FLOW_OUT_OF_LIMIT"},
    {WEB_COMM_PUB_OPEN_ALL_DOOR, "WEB_COMM_PUB_OPEN_ALL_DOOR"},
    {WEB_COMM_PUB_CLOSE_ALL_DOOR, "WEB_COMM_PUB_CLOSE_ALL_DOOR"},
    {WEB_COMM_ALLOW_CREATE_PIN, "WEB_COMM_ALLOW_CREATE_PIN"},
    {WEB_COMM_FEATURE_PLAN_RENEW, "WEB_COMM_FEATURE_PLAN_RENEW"},
    {WEB_COMM_MODIFY_PRIVATE_ACCESS, "WEB_COMM_MODIFY_PRIVATE_ACCESS"},
    {WEB_COMM_UPDATE_LANDLINE_STATUS, "WEB_COMM_UPDATE_LANDLINE_STATUS"},
    {WEB_COMM_MODIFY_BUILDING_NAME, "WEB_COMM_MODIFY_BUILDING_NAME"},
    {WEB_COMM_MODIFY_NODE_MOTION_CONFIG, "WEB_COMM_MODIFY_NODE_MOTION_CONFIG"},
    {WEB_COMM_MODIFY_FEATURE_PLAN, "WEB_COMM_MODIFY_FEATURE_PLAN"},
    {WEB_COMM_UPDATE_COMMUNITY_ALL, "WEB_COMM_UPDATE_COMMUNITY_ALL"},
    {WEB_COMM_ADD_INDOOR_PLAN_DEV, "WEB_COMM_ADD_INDOOR_PLAN_DEV"},
    {WEB_COMM_MODIFY_CONTACT_DISPLAY_ORDER, "WEB_COMM_MODIFY_CONTACT_DISPLAY_ORDER"},
    {WEB_COMM_MODIFY_BUILDING_FLOOR_SETTING, "WEB_COMM_MODIFY_BUILDING_FLOOR_SETTING"},

    {WEB_OFFICE_NODE_UPDATE, "WEB_OFFICE_NODE_UPDATE"},
    {WEB_OFFICE_ADD_USER, "WEB_OFFICE_ADD_USER"},
    {WEB_OFFICE_DEL_USER, "WEB_OFFICE_DEL_USER"},
    {WEB_OFFICE_MODIFY_USER, "WEB_OFFICE_MODIFY_USER"},
    {WEB_OFFICE_ADD_DEV, "WEB_OFFICE_ADD_DEV"},
    {WEB_OFFICE_DEL_DEV, "WEB_OFFICE_DEL_DEV"},
    {WEB_OFFICE_MODIFY_DEV, "WEB_OFFICE_MODIFY_DEV"},
    {WEB_OFFICE_UPLOAD_FACE_PIC, "WEB_OFFICE_UPLOAD_FACE_PIC"},
    {WEB_OFFICE_DELETE_FACE_PIC, "WEB_OFFICE_DELETE_FACE_PIC"},
    {WEB_OFFICE_UPDATE_MAC_CONFIG, "WEB_OFFICE_UPDATE_MAC_CONFIG"},
    {WEB_OFFICE_UPDATE_NODE_PUB_USER, "WEB_OFFICE_UPDATE_NODE_PUB_USER"},
    {WEB_OFFICE_UPDATE_NODE_CONTACT, "WEB_OFFICE_UPDATE_NODE_CONTACT"},
    
    {WEB_OFFICE_UNIT_ADD_DEV, "WEB_OFFICE_UNIT_ADD_DEV"},
    {WEB_OFFICE_UNIT_DEL_DEV, "WEB_OFFICE_UNIT_DEL_DEV"},
    {WEB_OFFICE_UNIT_MODIFY_DEV, "WEB_OFFICE_UNIT_MODIFY_DEV"},

    {WEB_OFFICE_PUB_ADD_DEV, "WEB_OFFICE_PUB_ADD_DEV"},
    {WEB_OFFICE_PUB_DEL_DEV, "WEB_OFFICE_PUB_DEL_DEV"},
    {WEB_OFFICE_PUB_MODIFY_DEV, "WEB_OFFICE_PUB_MODIFY_DEV"},

    {WEB_OFFICE_INFO, "WEB_OFFICE_INFO"},
    {WEB_OFFICE_APT_PIN, "WEB_OFFICE_APT_PIN"},
    {WEB_OFFICE_MOTION, "WEB_OFFICE_MOTION"},
    {WEB_OFFICE_IMPORT_OFFICE, "WEB_OFFICE_IMPORT_OFFICE"},
    {WEB_OFFICE_ADD_BUILDING, "WEB_OFFICE_ADD_BUILDING"},
    {WEB_OFFICE_DEL_BUILDING, "WEB_OFFICE_DEL_BUILDING"},
    {WEB_OFFICE_MODIFY_BUILDING, "WEB_OFFICE_MODIFY_BUILDING"},
    {WEB_OFFICE_MODIFY_TIMEINFO, "WEB_OFFICE_MODIFY_TIMEINFO"},
    {WEB_OFFICE_DELETE_OFFICE, "WEB_OFFICE_DELETE_OFFICE"},
    {WEB_OFFICE_IMPORT_FACE_PIC, "WEB_OFFICE_IMPORT_FACE_PIC"},
    {WEB_OFFICE_DELETE_ALL_FACE_PIC, "WEB_OFFICE_DELETE_ALL_FACE_PIC"},
    {WEB_OFFICE_NOTIFY_FLOW_OUT_OF_LIMIT, "WEB_OFFICE_NOTIFY_FLOW_OUT_OF_LIMIT"},
    {WEB_OFFICE_PUB_OPEN_ALL_DOOR, "WEB_OFFICE_PUB_OPEN_ALL_DOOR"},
    {WEB_OFFICE_PUB_CLOSE_ALL_DOOR, "WEB_OFFICE_PUB_CLOSE_ALL_DOOR"},
    {WEB_OFFICE_ALLOW_CREATE_PIN, "WEB_OFFICE_ALLOW_CREATE_PIN"},
    {WEB_OFFICE_FEATURE_PLAN_RENEW, "WEB_OFFICE_FEATURE_PLAN_RENEW"},
    {WEB_OFFICE_MODIFY_CONTACT_DISPLAY_ORDER, "WEB_OFFICE_MODIFY_CONTACT_DISPLAY_ORDER"},

    {WEB_OFFICE_ACCOUNT_CHANGE, "WEB_OFFICE_ACCOUNT_CHANGE"},
    
    {WEB_OFFICE_DEPARTMENT_CHANGE, "WEB_OFFICE_DEPARTMENT_CHANGE"},
    {WEB_OFFICE_MODIFY_DEPARTMENT_NAME, "WEB_OFFICE_MODIFY_DEPARTMENT_NAME"},
    
    {WEB_OFFICE_PUB_CHANGE, "WEB_OFFICE_PUB_CHANGE"},
    
    {WEB_OFFICE_INFO_CHANGE, "WEB_OFFICE_INFO_CHANGE"},

    {WEB_OFFICE_ADD_ACCOUNT_ACCESS, "WEB_OFFICE_ADD_ACCOUNT_ACCESS"},
    {WEB_OFFICE_MODIFY_ACCOUNT_ACCESS, "WEB_OFFICE_MODIFY_ACCOUNT_ACCESS"},
    {WEB_OFFICE_ADD_USER_ACCESSGROUP, "WEB_OFFICE_ADD_USER_ACCESSGROUP"},
    {WEB_OFFICE_MODIFY_USER_ACCESSGROUP, "WEB_OFFICE_MODIFY_USER_ACCESSGROUP"},
    {WEB_OFFICE_ADD_USER_ACCESSGROUP_DEVICE, "WEB_OFFICE_ADD_USER_ACCESSGROUP_DEVICE"},
    {WEB_OFFICE_DEL_USER_ACCESSGROUP_DEVICE, "WEB_OFFICE_DEL_USER_ACCESSGROUP_DEVICE"},
    {WEB_OFFICE_MODIFY_USER_ACCESSGROUP_DEVICE, "WEB_OFFICE_MODIFY_USER_ACCESSGROUP_DEVICE"},
    {WEB_OFFICE_MODIFY_USER_ALL_ACCESS, "WEB_OFFICE_MODIFY_USER_ALL_ACCESS"},
    {WEB_OFFICE_DEL_ACCOUNT_ACCESS, "WEB_OFFICE_DEL_ACCOUNT_ACCESS"},
    {WEB_OFFICE_MODIFY_ACCESS_GROUP, "WEB_OFFICE_MODIFY_ACCESS_GROUP"},
    {WEB_OFFICE_MODIFY_STAFF, "WEB_OFFICE_MODIFY_STAFF"},
    {WEB_OFFICE_MODIFY_DELIVERY, "WEB_OFFICE_MODIFY_DELIVERY"},

    {WEB_COMM_ADD_ACCOUNT_ACCESS, "WEB_COMM_ADD_ACCOUNT_ACCESS"},
    {WEB_COMM_MODIFY_ACCOUNT_ACCESS, "WEB_COMM_MODIFY_ACCOUNT_ACCESS"},
    {WEB_COMM_ADD_USER_ACCESSGROUP, "WEB_COMM_ADD_USER_ACCESSGROUP"},
    {WEB_COMM_MODIFY_USER_ACCESSGROUP, "WEB_COMM_MODIFY_USER_ACCESSGROUP"},
    {WEB_COMM_ADD_USER_ACCESSGROUP_DEVICE, "WEB_COMM_ADD_USER_ACCESSGROUP_DEVICE"},
    {WEB_COMM_DEL_USER_ACCESSGROUP_DEVICE, "WEB_COMM_DEL_USER_ACCESSGROUP_DEVICE"},
    {WEB_COMM_MODIFY_USER_ACCESSGROUP_DEVICE, "WEB_COMM_MODIFY_USER_ACCESSGROUP_DEVICE"},
    {WEB_COMM_ADD_PM_APP_ACCOUNT, "WEB_COMM_ADD_PM_APP_ACCOUNT"},
    {WEB_COMM_DEL_PM_APP_ACCOUNT, "WEB_COMM_DEL_PM_APP_ACCOUNT"},
    {WEB_COMM_MODIFY_PM_APP_ACCOUNT, "WEB_COMM_MODIFY_PM_APP_ACCOUNT"},
    {WEB_COMM_MODIFY_USER_ALL_ACCESS, "WEB_COMM_MODIFY_USER_ALL_ACCESS"},
    {WEB_COMM_DEL_ACCOUNT_ACCESS, "WEB_COMM_DEL_ACCOUNT_ACCESS"},
    {WEB_COMM_UPDATE_NODE_USER, "WEB_COMM_UPDATE_NODE_USER"},
    {WEB_COMM_MODIFY_PM_APP_STATUS, "WEB_COMM_MODIFY_PM_APP_STATUS"},
    {WEB_COMM_MODIFY_ACCESS_GROUP, "WEB_COMM_MODIFY_ACCESS_GROUP"},
    {WEB_COMM_MODIFY_STAFF, "WEB_COMM_MODIFY_STAFF"},
    {WEB_COMM_MODIFY_DELIVERY, "WEB_COMM_MODIFY_DELIVERY"},
    {WEB_COMM_MODIFY_HOLD_DOOR, "WEB_COMM_MODIFY_HOLD_DOOR"},
    {SMARTLOCK_SL20_LOCK_UPDATE_NOTIFY, "SMARTLOCK_SL20_LOCK_UPDATE_NOTIFY"},
    {SMARTLOCK_SL20_LOCK_KEEP_ALIVE_SWITCH_CHANGE, "SMARTLOCK_SL20_LOCK_KEEP_ALIVE_SWITCH_CHANGE"},
    {SMARTLOCK_SL50_LOCK_REQUEST_UPGRADE, "SMARTLOCK_SL50_LOCK_REQUEST_UPGRADE"},
    {SMARTLOCK_SL50_LOCK_START_UPGRADE, "SMARTLOCK_SL50_LOCK_START_UPGRADE"},
    {SMARTLOCK_CONFIG_UPDATE, "SMARTLOCK_CONFIG_UPDATE"},
    {SMARTLOCK_NODE_CONFIG_UPDATE, "SMARTLOCK_NODE_CONFIG_UPDATE"},
    {SMARTLOCK_PROJECT_CONFIG_UPDATE, "SMARTLOCK_PROJECT_CONFIG_UPDATE"}
};


static std::string ToHexString(int message_id) {
    char buffer[16];
    std::snprintf(buffer, sizeof(buffer), "%X", message_id);
    return std::string(buffer);
}

std::string MsgIdToMsgName::GetDeclientMessageName(int message_id) 
{
    auto it = declient_message_map.find(message_id);
    if (it != declient_message_map.end()) {
        return it->second;
    }
    else {
        return ToHexString(message_id);
    }
}

std::string MsgIdToMsgName::GetAkcsMessageName(int message_id) 
{
    auto it = akcs_message_map.find(message_id);
    if (it != akcs_message_map.end()) {
        return it->second;
    }
    else {
        return ToHexString(message_id);
    }
}

std::string MsgIdToMsgName::GetConfigChangeTypeName(int message_id) 
{
    // 获取所有与 message_id 匹配的键值对范围
    auto range = config_change_type_map.equal_range(message_id);

    if (range.first != range.second) {
        std::string result;

        // 遍历范围内的所有值，并将它们拼接成一个字符串
        for (auto it = range.first; it != range.second; ++it) 
        {
            if (!result.empty()) {
                result += "/"; // 用 / 分隔多个值
            }
            result += it->second;
        }
        return result;
    } else {
        return ToHexString(message_id);
    }
}

