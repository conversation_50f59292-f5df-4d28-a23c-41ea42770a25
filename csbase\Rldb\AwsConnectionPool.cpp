/*
    *File: connection_pool.h
    *Author: chenyc
    *date:2017-04-19
*/
//#include "stdafx.h"
#include <stdio.h>
#include "AwsConnectionPool.h"
#include "Rldb.h"
#include <unistd.h>
#include "AkLogging.h"
#include <sys/types.h>
#include <unistd.h>
#include <sys/syscall.h>
#include "AkcsMonitor.h"
#include "ConfigFileReader.h"
#include "MetricService.h"

#define gettid() syscall(SYS_gettid)

AwsConnPool* AwsConnPool::m_connPool = NULL;

//当一个线程申请多个连接时候发送告警，不能直接挂掉，因为有可能旧的接口没有测试到
static void SendConnOutOfRangeAlarm(const std::string &db_ip, const std::string &out_ip, const std::string &app)
{
    char error[512] = "";
    snprintf(error, sizeof(error), "db connect error, one thread request two db connect. ip=%s,dbip=%s pid=%d app=%s", 
    out_ip.c_str(), db_ip.c_str(), getpid(), app.c_str());

    AK_LOG_WARN << error;
    //触发监控告警
    std::string worker_node = "db_connect";
    int ret = AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, error, AKCS_MONITOR_ALARM_DB_CONNECT);
    if (ret == -1)
    {
        char cmd[1024] = "";
        snprintf(cmd, sizeof(cmd), "echo \"%s\" | mutt -s \"One Thread Request Two DB Connect\"  -b <EMAIL> -c  <EMAIL> -c <EMAIL> &", error);
        system(cmd);
        AK_LOG_WARN << "SystemMonitor has not been init, send email with mutt.";
    }
}


//连接池的构造函数
AwsConnPool::AwsConnPool()
{
	this->curSize=0;
    pthread_mutex_init(&lock,NULL);
}
//获取连接池对象，单例模式,为避免竞争,在主线程中调用
AwsConnPool* AwsConnPool::GetInstance()
{
	if(m_connPool == NULL)
	{
		m_connPool = new AwsConnPool();
	}
	return m_connPool;
}


//初始化数据库连接信息
void AwsConnPool::Init(const std::string ip, const std::string user,
          const std::string pwd,const std::string db,
          int port, int pool_size, const std::string app)
{
	db_ip = ip;
	db_username = user;
    db_password = pwd;
    db_database = db;
    db_port = port;
    maxSize = pool_size;
    this->app = app;
    InitConnection(maxSize);
    
    CConfigFileReader ipfile("/etc/ip"); 
    out_ip = ipfile.GetConfigName("SERVERIP");    
}



//初始化连接池，创建最大连接数的一半连接数量
void AwsConnPool::InitConnection(int nInitialSize)
{
	RldbPtr conn;
	for(int i = 0; i < nInitialSize; i++)
	{
		conn = this->CreateConnection();
        ConnList.push_back(conn);
        ++(this->curSize);
	}
/*
    MY_CHARSET_INFO charset;
    mysql_get_character_set_info(&conn->m_mysql, &charset);
    AK_LOG_WARN <<"character set number:" << charset.number;
    AK_LOG_WARN <<"character set state:"<< charset.state;
    AK_LOG_WARN <<"collation name:"<< charset.csname;
    AK_LOG_WARN <<"character set name:"<< charset.name;
    AK_LOG_WARN <<"comment:"<< charset.comment;
    AK_LOG_WARN <<"character set directory:"<< charset.dir;
    AK_LOG_WARN <<"min. length for multibyte strings:"<< charset.mbminlen;
    AK_LOG_WARN <<"max. length for multibyte strings:"<< charset.mbmaxlen;
*/
    
}
//创建连接,返回一个Connection
RldbPtr AwsConnPool::CreateConnection()
{
	RldbPtr ConnSp(new CRldb(db_ip, db_port, db_username, db_password, db_database, app));
    //马上建立连接
    ConnSp->Connect();
    
    return ConnSp;   
}



#include <execinfo.h>
static void backtrace()
{
    //编译选项加入-rdynamic -g进行调试 先直接返回，出现问题时候可以调试
    return;
    
    void * array[10];
    char ** strings;
    size_t size = backtrace(array, 10);
    strings = backtrace_symbols(array, size);
    char str[512];
    for (size_t i = 0; i < size; ++i)
    { 
      snprintf(str, sizeof(str), "backtrace%lu:[%s]", i, strings[i]);
      AK_LOG_INFO << str;
    }
    free(strings);    
}


//在连接池中获得一个连接
RldbPtr AwsConnPool::GetConnection(){
	RldbPtr con;
	pthread_mutex_lock(&lock);
	if(ConnList.size() > 0)//连接池容器中还有连接
	{
        con = ConnList.front();//得到第一个连接
		ConnList.pop_front();//移除第一个连接 
		
        pid_t pid = gettid();
        std::list<int>::iterator it;
        for (it = conn_pthread_register.begin(); it != conn_pthread_register.end(); ++it) 
		{
            if (*it == pid)
            {
                //有可能嵌套或者没有释放
                backtrace();
                SendConnOutOfRangeAlarm(db_ip, out_ip, app);
                //AK_LOG_FATAL << "GetConnection FATAL. thread get two db conn.";	
            }
		}
		conn_pthread_register.push_back(pid);
	}
    else 
    {        
		AK_LOG_WARN << "GetConnection failed,try again.";
        pthread_mutex_unlock(&lock);
        //10ms之后再次尝试获取链接
        usleep(10 * 1000);
        pthread_mutex_lock(&lock);
        if(ConnList.size()>0)
        {
            con = ConnList.front();
            ConnList.pop_front();
        }

        // 获取链接失败次数 +1
        MetricService* metric_service = MetricService::GetInstance();
        if(metric_service) {
            metric_service->AddValue("db_get_conn_failed_count", 1);
        }
    }
    pthread_mutex_unlock(&lock);
    return con;

}
//回收数据库连接
void AwsConnPool::ReleaseConnection(RldbPtr ConnSp)
{
    pthread_mutex_lock(&lock);
    //判断该链接对象是否有效
    if(NULL != ConnSp.get())
    {
    	ConnList.push_back(ConnSp);
		pid_t pid = gettid();
		conn_pthread_register.remove(pid);
    }
    pthread_mutex_unlock(&lock);
}
//连接池的析构函数
AwsConnPool::~AwsConnPool()
{
	this->DestoryConnPool();
}
//销毁连接池,首先要先销毁连接池的连接
void AwsConnPool::DestoryConnPool()
{
	ConnList.clear();//清空连接池中的连接
    curSize=0;
}

AwsConnPool* GetAwsDBConnPollInstance()
{
	return AwsConnPool::GetInstance();
}

void ReleaseAwsDBConn(RldbPtr &ConnPrt)
{
	GetAwsDBConnPollInstance()->ReleaseConnection(ConnPrt);
    //释放对象绑定的连接.避免在一个函数中多次调用ReleaseConnection时重复push_back到容器中
    //因此，只能传过来reference 而不是 const reference,外层调用者要注意,释放完之后就不能再使用指针了.
    ConnPrt.reset();
}

