#include "ConnectionManager.h"

AKCSDBConnectionManager::AKCSDBConnectionManager()
{
    db_conn_ = GetDBConnPollInstance()->GetConnection();
}

AKCSDBConnectionManager::~AKCSDBConnectionManager()
{
    ReleaseDBConn(db_conn_);
}

RldbPtr AKCSDBConnectionManager::GetDBConnection()
{
    return db_conn_;
}

MappingConnectionManager::MappingConnectionManager()
{
    db_conn_ = GetMappingDBConnPollInstance()->GetConnection();
}

MappingConnectionManager::~MappingConnectionManager()
{
    ReleaseMappingDBConn(db_conn_);
}

RldbPtr MappingConnectionManager::GetDBConnection()
{
    return db_conn_;
}

LOGDBConnectionManager::LOGDBConnectionManager()
{
    db_conn_ = GetLogDBConnPollInstance()->GetConnection();
}

LOGDBConnectionManager::~LOGDBConnectionManager()
{
    ReleaseLogDBConn(db_conn_);
}

RldbPtr LOGDBConnectionManager::GetDBConnection()
{
    return db_conn_;
}