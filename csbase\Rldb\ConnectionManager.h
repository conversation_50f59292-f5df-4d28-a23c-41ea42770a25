#ifndef __CONNECTION_MANAGER_H__
#define __CONNECTION_MANAGER_H__
#include "AkLogging.h"
#include "ConnectionPool.h"
#include "MappingConnectionPool.h"
#include "LogConnectionPool.h"
/*
    conn_name:获取的数据库连接
    fail_ret:连接获取失败时返回值 
*/
#define GET_DB_CONN_ERR_RETURN_VOID(conn_name) \
    AKCSDBConnectionManager conn_manager; \
    auto conn_name = conn_manager.GetDBConnection(); \
    if(conn_name.get() == nullptr) \
    { \
        AK_LOG_WARN << "Get DB conn failed."; \
        return; \
    }

#define GET_DB_CONN_ERR_RETURN(conn_name, fail_ret) \
    AKCSDBConnectionManager conn_manager; \
    auto conn_name = conn_manager.GetDBConnection(); \
    if(conn_name.get() == nullptr) \
    { \
        AK_LOG_WARN << "Get DB conn failed."; \
        return fail_ret; \
    }

#define GET_MAPPING_DB_CONN_ERR_RETURN_VOID(conn_name) \
        MappingConnectionManager conn_manager; \
        auto conn_name = conn_manager.GetDBConnection(); \
        if(conn_name.get() == nullptr) \
        { \
            AK_LOG_WARN << "Get Mapping DB conn failed."; \
            return; \
        }

#define GET_MAPPING_DB_CONN_ERR_RETURN(conn_name, fail_ret) \
        MappingConnectionManager conn_manager; \
        auto conn_name = conn_manager.GetDBConnection(); \
        if(conn_name.get() == nullptr) \
        { \
            AK_LOG_WARN << "Get Mapping DB conn failed."; \
            return fail_ret; \
        }

#define GET_LOG_DB_CONN_ERR_RETURN_VOID(conn_name) \
    LOGDBConnectionManager conn_manager; \
    auto conn_name = conn_manager.GetDBConnection(); \
    if(conn_name.get() == nullptr) \
    { \
        AK_LOG_WARN << "Get Log DB conn failed."; \
        return; \
    }

#define GET_LOG_DB_CONN_ERR_RETURN(conn_name, fail_ret) \
    LOGDBConnectionManager conn_manager; \
    auto conn_name = conn_manager.GetDBConnection(); \
    if(conn_name.get() == nullptr) \
    { \
        AK_LOG_WARN << "Get Log DB conn failed."; \
        return fail_ret; \
    }

class AKCSDBConnectionManager
{
public:
    AKCSDBConnectionManager();
    RldbPtr GetDBConnection();
    ~AKCSDBConnectionManager();
private:
    RldbPtr db_conn_;
};


class LOGDBConnectionManager
{
public:
    LOGDBConnectionManager();
    RldbPtr GetDBConnection();
    ~LOGDBConnectionManager();
private:
    RldbPtr db_conn_;
};


class MappingConnectionManager
{
public:
    MappingConnectionManager();
    RldbPtr GetDBConnection();
    ~MappingConnectionManager();
private:
    RldbPtr db_conn_;
};

#endif // __CONNECTION_MANAGER_H__
