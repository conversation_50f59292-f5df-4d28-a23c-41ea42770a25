/*
    *File: connection_pool.h
    *Author: chenyc
    *date:2017-04-19
*/
#ifndef __CONNECTION_POOL_H__
#define __CONNECTION_POOL_H__

#include <pthread.h>
#include <list>
#include <memory>
#include <Rldb.h>
#include "RldbQuery.h"
#include <mutex>

#define AKCS_DB_MAX_CONN 20

class CRldb;
typedef std::shared_ptr<CRldb> RldbPtr;
    
class ConnPool
{
public:

    ~ConnPool();
    RldbPtr GetConnection();//获得数据库连接
    void ReleaseConnection(const RldbPtr &ConnPrt);//将数据库连接放回到连接池的容器中
    static ConnPool* GetInstance();//获取数据库连接池对象
    void Init(const std::string ip, const std::string user, const std::string pwd,
                 const std::string db, int port, int pool_init_size, const std::string app_name, int max_size = AKCS_DB_MAX_CONN);//初始化数据库连接信息   

    //added by chenyc,2022.03.22,支持应用层动态修改数据库服务的地址与端口
    void ReInit(const std::string &ip, const int port);
    bool CheckDBConnNormal();
    int CheckDBPool();
private:
    int cur_size_;//当前已建立的数据库连接数量
    int init_size_;//连接池中初始化的数据库连接数
    int max_size_; //最大可动态扩展的连接数大小
    std::list<RldbPtr> ConnList;//连接池的容器队列
    std::list<int/*pthread_id*/> conn_pthread_register;//记录线程分配conn
    pthread_mutex_t lock;//线程锁
    static ConnPool* m_connPool;
    
    RldbPtr CreateConnection();//创建一个连接
    void InitConnection(int iInitialSize);//初始化数据库连接池
    void ReInitConnection(const int nInitialSize);
    void DestoryConnPool();//销毁数据库连接池
    ConnPool(std::string db_ip, int db_port, std::string db_username, std::string db_password,
                std::string db_database, int maxSize);
    ConnPool();

    std::string db_username;
    std::string db_password;
    std::string db_database;
    std::string db_ip;
    std::string out_ip;
    std::string app;//哪个组件
    int db_port;    
    
};

ConnPool* GetDBConnPollInstance();
void ReleaseDBConn(RldbPtr &ConnPrt);


#endif	/*_CONNECTION_POOL_H */
