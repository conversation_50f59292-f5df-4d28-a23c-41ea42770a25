#include <stdio.h>
#include <unistd.h>
#include <sys/types.h>
#include <unistd.h>
#include <sys/syscall.h>
#include "ConnectionPoolTemplate.h"
#include "Rldb.h"
#include "AkLogging.h"
#include "AkcsMonitor.h"
#include "ConfigFileReader.h"
#include "MetricService.h"

#define gettid() syscall(SYS_gettid)

//当一个线程申请多个连接时候发送告警，不能直接挂掉，因为有可能旧的接口没有测试到
static void SendConnOutOfRangeAlarm(const std::string &db_ip, const std::string &out_ip, const std::string &app)
{
    char error[512] = "";
    snprintf(error, sizeof(error), "db connect error, one thread request two db connect. ip=%s,dbip=%s pid=%d app=%s", 
    out_ip.c_str(), db_ip.c_str(), getpid(), app.c_str());

    AK_LOG_WARN << error;
    //触发监控告警
    std::string worker_node = "db_connect";
    int ret = AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, error, AKCS_MONITOR_ALARM_DB_CONNECT);
    if (ret == -1)
    {
        char cmd[1024] = "";
        snprintf(cmd, sizeof(cmd), "echo \"%s\" | mutt -s \"One Thread Request Two DB Connect\"  -b <EMAIL> -c  <EMAIL> -c <EMAIL> &", error);
        system(cmd);
        AK_LOG_WARN << "SystemMonitor has not been init, send email with mutt.";
    }
}

template <DB_CONN_POOL_NUMBER number>
//连接池的构造函数
ConnPoolTemplate<number>::ConnPoolTemplate()
{
    cur_size_ = 0;
    pthread_mutex_init(&lock_, NULL);
}

template <DB_CONN_POOL_NUMBER number>
//连接池的析构函数
ConnPoolTemplate<number>::~ConnPoolTemplate()
{
	DestoryConnPool();
}

template <DB_CONN_POOL_NUMBER number>
//初始化数据库连接信息
void ConnPoolTemplate<number>::Init(const std::string ip, const std::string user,
          const std::string pwd,const std::string db,
          int port, int pool_size, const std::string app_name)
{
	db_ip_ = ip;
	db_username_ = user;
    db_password_ = pwd;
    db_database_ = db;
    db_port_ = port;
    max_size_ = pool_size;
    app_ = app_name;
    InitConnection(max_size_);
    
    CConfigFileReader ipfile("/etc/ip"); 
    out_ip_ = ipfile.GetConfigName("SERVERIP");    
}

template <DB_CONN_POOL_NUMBER number>
void ConnPoolTemplate<number>::ReInit(const std::string &ip, const int port)
{
    db_ip_ = ip;
    db_port_ = port;
    ReInitConnection(max_size_);
}        

template <DB_CONN_POOL_NUMBER number>
//初始化连接池，创建最大连接数的一半连接数量
void ConnPoolTemplate<number>::InitConnection(int init_size)
{
	RldbPtr conn;
    pthread_mutex_lock(&lock_);
	for(int i = 0; i < init_size; i++)
	{
		conn = this->CreateConnection();
        conn_list_.push_back(conn);
        cur_size_++;
	}    
    pthread_mutex_unlock(&lock_);
}

template <DB_CONN_POOL_NUMBER number>
//重新初始化连接池，创建最大连接数的一半连接数量
void ConnPoolTemplate<number>::ReInitConnection(const int init_size)
{
    RldbPtr conn;
    pthread_mutex_lock(&lock_);
    conn_list_.clear();//清空连接池中的连接
    cur_size_ = 0;
    for(int i = 0; i < init_size; i++)
    {
        conn = CreateConnection();
        conn_list_.push_back(conn);
        cur_size_++;
	}
    pthread_mutex_unlock(&lock_);
}

template <DB_CONN_POOL_NUMBER number>
//创建连接,返回一个Connection
RldbPtr ConnPoolTemplate<number>::CreateConnection()
{
	RldbPtr ConnSp(new CRldb(db_ip_, db_port_, db_username_, db_password_, db_database_, app_));
    //马上建立连接
    ConnSp->Connect();
    return ConnSp;   
}

template <DB_CONN_POOL_NUMBER number>
//在连接池中获得一个连接
RldbPtr ConnPoolTemplate<number>::GetConnection(){
	RldbPtr con;
	pthread_mutex_lock(&lock_);
	if(conn_list_.size() > 0)//连接池容器中还有连接
	{
        con = conn_list_.front();//得到第一个连接
		conn_list_.pop_front();//移除第一个连接 
		
        pid_t pid = gettid();
        std::list<int>::iterator it;
        for (it = conn_pthread_register_.begin(); it != conn_pthread_register_.end(); ++it) 
		{
            if (*it == pid)
            {
                SendConnOutOfRangeAlarm(db_ip_, out_ip_, app_);
            }
		}
		conn_pthread_register_.push_back(pid);
	}
    else 
    {        
		AK_LOG_WARN << "GetConnection failed,try again.";
        pthread_mutex_unlock(&lock_);
        //10ms之后再次尝试获取链接
        usleep(10 * 1000);
        pthread_mutex_lock(&lock_);
        if(conn_list_.size()>0)
        {
            con = conn_list_.front();
            conn_list_.pop_front();
        }

        // 获取链接失败次数 +1
        MetricService* metric_service = MetricService::GetInstance();
        if(metric_service) {
            metric_service->AddValue("db_get_conn_failed_count", 1);
        }
    }
    pthread_mutex_unlock(&lock_);
    return con;

}

template <DB_CONN_POOL_NUMBER number>
bool ConnPoolTemplate<number>::CheckDBConnNormal()
{
    pthread_mutex_lock(&lock_);
    for (const auto &it : conn_list_)
    {
        if (!it->CheckDBConn())
        {
            pthread_mutex_unlock(&lock_);
            return false;
        }
    }
    pthread_mutex_unlock(&lock_);
    return true;
}

template <DB_CONN_POOL_NUMBER number>
//回收数据库连接
void ConnPoolTemplate<number>::ReleaseConnection(RldbPtr &conn_ptr)
{
    if(conn_ptr.get())
    {
        if ((conn_ptr->GetDbIP() != db_ip_) || (conn_ptr->GetDbPort() != db_port_))
        {
            return;
        }
        pthread_mutex_lock(&lock_);
    	conn_list_.push_back(conn_ptr);
    	pid_t pid = gettid();
    	conn_pthread_register_.remove(pid);
        pthread_mutex_unlock(&lock_);
    }
    
    conn_ptr.reset();
}

template <DB_CONN_POOL_NUMBER number>
//销毁连接池,首先要先销毁连接池的连接
void ConnPoolTemplate<number>::DestoryConnPool()
{
	conn_list_.clear();//清空连接池中的连接
    cur_size_=0;
}

// 显式实例化模板
template class ConnPoolTemplate<DB_CONN_POOL_NUMBER::POOL_NUMBER_DEFAULT>;
template class ConnPoolTemplate<DB_CONN_POOL_NUMBER::POOL_NUMBER_1>;
//template class ConnPoolTemplate<DB_CONN_POOL_NUMBER::POOL_NUMBER_2>;
//template class ConnPoolTemplate<DB_CONN_POOL_NUMBER::POOL_NUMBER_3>;
//....


RldbPtr ConnPoolTemplateGetConnection(int db_num)
{
   switch (db_num) 
   {
        case DB_CONN_POOL_NUMBER::POOL_NUMBER_DEFAULT:
            return ConnPoolTemplate<POOL_NUMBER_DEFAULT>::GetInstance().GetConnection();
        case DB_CONN_POOL_NUMBER::POOL_NUMBER_1:
            return ConnPoolTemplate<POOL_NUMBER_1>::GetInstance().GetConnection();
        default:
            //直接断言
    		AK_LOG_FATAL << "ConnPoolTemplateGetConnection error.";
            return nullptr;
    }
}

void ConnPoolTemplateReleaseConnection(RldbPtr &conn_ptr, int db_num)
{
   switch (db_num) 
   {
        case DB_CONN_POOL_NUMBER::POOL_NUMBER_DEFAULT:
            ConnPoolTemplate<POOL_NUMBER_DEFAULT>::GetInstance().ReleaseConnection(conn_ptr);
            break;
        case DB_CONN_POOL_NUMBER::POOL_NUMBER_1:
            ConnPoolTemplate<POOL_NUMBER_1>::GetInstance().ReleaseConnection(conn_ptr);            
            break;
        default:
            //直接断言
    		AK_LOG_FATAL << "ConnPoolTemplateReleaseConnection error.";
    }
}

