#ifndef __CONNECTION_POOL_TEMPLATE_H__
#define __CONNECTION_POOL_TEMPLATE_H__

#include <pthread.h>
#include <list>
#include <memory>
#include <Rldb.h>
#include "RldbQuery.h"
#include <mutex>
#include <boost/noncopyable.hpp>


#define MAX_RLDB_CONN 10

class CRldb;
typedef std::shared_ptr<CRldb> RldbPtr;

//即当前暂支持最多用四个ConnPool单例
enum DB_CONN_POOL_NUMBER
{
    POOL_NUMBER_DEFAULT = 0,
    POOL_NUMBER_1 = 1,   
    //POOL_NUMBER_2 = 2,
    //POOL_NUMBER_3 = 3,
    //....
};


template <DB_CONN_POOL_NUMBER number>
class ConnPoolTemplate : boost::noncopyable
{
public:

    ~ConnPoolTemplate();
    RldbPtr GetConnection();//获得数据库连接
    void ReleaseConnection(RldbPtr &conn_ptr);//将数据库连接放回到连接池的容器中
    static ConnPoolTemplate& GetInstance()
    {
        static ConnPoolTemplate<number> instance;
        return instance;
    }
    void Init(const std::string ip, const std::string user, const std::string pwd,
                 const std::string db, int port, int pool_size, const std::string app_name);//初始化数据库连接信息   
    void ReInit(const std::string &ip, const int port);
    bool CheckDBConnNormal();
    
private:
    
    int cur_size_;//当前已建立的数据库连接数量
    int max_size_;//连接池中定义的最大数据库连接数
    std::list<RldbPtr> conn_list_;//连接池的容器队列
    std::list<int/*pthread_id*/> conn_pthread_register_;//记录线程分配conn
    pthread_mutex_t lock_;//线程锁
    
    std::string db_username_;
    std::string db_password_;
    std::string db_database_;
    std::string db_ip_;
    std::string out_ip_;
    std::string app_;//哪个组件
    int db_port_;   

    RldbPtr CreateConnection();//创建一个连接
    void InitConnection(int init_size);//初始化数据库连接池
    void ReInitConnection(const int init_size);
    void DestoryConnPool();//销毁数据库连接池
    ConnPoolTemplate();
    
};

RldbPtr ConnPoolTemplateGetConnection(int db_num);
void ConnPoolTemplateReleaseConnection(RldbPtr &conn_ptr, int db_num);


#endif	/*__CONNECTION_POOL_TEMPLATE_H__ */
