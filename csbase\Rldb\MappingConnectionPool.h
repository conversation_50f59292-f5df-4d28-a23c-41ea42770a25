#ifndef __MAPPING_CONNECTION_POOL_H__
#define __MAPPING_CONNECTION_POOL_H__

#include <pthread.h>
#include <list>
#include <memory>
#include <Rldb.h>
#include "RldbQuery.h"

#define MAX_RLDB_CONN 10

class CRldb;
typedef std::shared_ptr<CRldb> RldbPtr;
    
class MappingConnPool
{
public:

    ~MappingConnPool();
    RldbPtr GetConnection();//获得数据库连接
    void ReleaseConnection(const RldbPtr &ConnPrt);//将数据库连接放回到连接池的容器中
    static MappingConnPool* GetInstance();//获取数据库连接池对象
    void Init(const std::string ip, const std::string user, const std::string pwd,
    const std::string db, int port, int pool_size, const std::string app_name);//初始化数据库连接信息   

    void ReInit(const std::string &ip, const int port);
private:
    int curSize;//当前已建立的数据库连接数量
    int maxSize;//连接池中定义的最大数据库连接数
    std::list<RldbPtr> ConnList;//连接池的容器队列
    std::list<int/*pthread_id*/> conn_pthread_register;//记录线程分配conn
    pthread_mutex_t lock;//线程锁
    static MappingConnPool* m_connPool;

    RldbPtr CreateConnection();//创建一个连接
    void InitConnection(int iInitialSize);//初始化数据库连接池
    void ReInitConnection(const int nInitialSize);
    void DestoryConnPool();//销毁数据库连接池
    MappingConnPool(std::string db_ip, int db_port, std::string db_username, std::string db_password,
    std::string db_database, int maxSize);
    MappingConnPool();

    std::string db_username;
    std::string db_password;
    std::string db_database;
    std::string db_ip;
    std::string out_ip;
    std::string app;//哪个组件
    int db_port;    

};

MappingConnPool* GetMappingDBConnPollInstance();
void ReleaseMappingDBConn(RldbPtr &ConnPrt);


#endif	/*_CONNECTION_POOL_H */
