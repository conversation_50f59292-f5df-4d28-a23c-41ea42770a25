#include "BasicDefine.h"
//#include "CharChans.h"
//#include "AdaptDebug.h"
#include "Rldb.h"
#include "AkLogging.h"
#include "AkcsMonitor.h"
#include "ConfigFileReader.h"
#include "RldbStmt.h"
#include "ThreadLocalSingleton.h"
#include "iconv.h"


#define SQL_ERROR_LEN   1024
#define SQL_CMD_LEN		2048

extern CRldb rldb;


void SendDbConnErrorAlarm(const std::string &db_ip, const std::string &out_ip, const std::string &app)
{
    char error[512] = "";
    snprintf(error, sizeof(error), "connect db error, ip=%s,dbip=%s pid=%d app=%s", 
    out_ip.c_str(), db_ip.c_str(), getpid(), app.c_str());

    AK_LOG_WARN << error;
    //触发监控告警
    std::string worker_node = "db_connect";
    int ret = AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, error, AKCS_MONITOR_ALARM_DB_CONNECT);
    if (ret == -1)
    {
        char cmd[1024] = "";
        snprintf(cmd, sizeof(cmd), "echo \"%s\" | mutt -s \"Connect DB Error\"  -b <EMAIL> -c  <EMAIL> -c <EMAIL> &", error);
        system(cmd);
        AK_LOG_WARN << "SystemMonitor has not been init, send email with mutt.";
    }
}

static bool Utf8Check(const std::string& char_buf)
{
    if(char_buf.size() == 0)
    {
        return true;
    }
    size_t inlen = char_buf.size() + 1;
    std::vector<char> inbuf(inlen, 0);
    memcpy(inbuf.data(), char_buf.c_str(), char_buf.size());
    size_t outlen = inlen;    
    std::vector<char> outbuf(outlen, 0);

    iconv_t cd;
    char* pin = inbuf.data();
    char* pout = outbuf.data();

    cd = iconv_open("utf-8", "utf-8");
    if (cd == 0)
    {
        //异常情况，先return true
        return true;
    }
    if (iconv(cd, &pin, &inlen, &pout, &outlen) == (std::size_t)-1)
    {
        iconv_close(cd);
        return false;
    }
    
    iconv_close(cd);
    return true;
}

CRldb::CRldb(const std::string &db_ip, int db_port, const std::string &db_username,
               const std::string &db_password, const std::string &db_database, const std::string &app)
{
	connected_ = false;
    db_ip_ = db_ip;
    db_port_ = db_port;
    db_username_ = db_username;
    db_password_ = db_password;
    db_database_ = db_database;
    app_ = app;
    
    CConfigFileReader ipfile("/etc/ip"); 
    this->out_ip_ = ipfile.GetConfigName("SERVERIP");     
}
//modified by chenyc,2022.03.23,析构的时候,释放连接
CRldb::~CRldb()
{
   mysql_close(&m_mysql);
   connected_ = false;
}

int CRldb::Connect()
{
	if(connected_)
	{
		AK_LOG_WARN << "Database already connected.";
		return -1;
	}
	if(mysql_init(&m_mysql) == NULL)
	{
        AK_LOG_WARN << "mysql_init() failed.";
        SendDbConnErrorAlarm(db_ip_, out_ip_, app_);
		return -1;
	}
	if(mysql_real_connect(&m_mysql, db_ip_.c_str(), db_username_.c_str(), db_password_.c_str(), 
        				  db_database_.c_str(), db_port_, NULL, 0) == NULL)
	{
		char error[SQL_ERROR_LEN] = {0};
		::snprintf(error, sizeof(error), "%s", mysql_error(&m_mysql));
		AK_LOG_WARN << "Connect to Database failed: " <<  error;
        SendDbConnErrorAlarm(db_ip_, out_ip_, app_);
		return -1;
	}

	connected_ = TRUE;
	mysql_set_character_set(&m_mysql,  "utf8");
	return 0;
}

int CRldb::Disconnect()
{
	mysql_close(&m_mysql);
	connected_ = false;
	return 0;
}


std::shared_ptr<CRldbStmt> CRldb::Prepare(const std::string &str_sql)
{
	MYSQL_STMT *stmt = mysql_stmt_init(&m_mysql);
	if (mysql_stmt_prepare(stmt, str_sql.c_str(), str_sql.size()))
	{
		AK_LOG_WARN << "Prepare failed,error msg:" << mysql_stmt_error(stmt);
		return nullptr;
	}

	return std::make_shared<CRldbStmt>(stmt);
}

//返回影响的行数,入参为string类型
int CRldb::Execute(const std::string &str_sql)
{
    if(!connected_)
    {
        Connect();
        if(!connected_)
        {
            AK_LOG_WARN << "Execute failed: Database not connected.";
            return -1;
        }
    }    
    int nRet = mysql_query(&m_mysql, str_sql.c_str());
    if(nRet != 0)
    { 
        char error[SQL_ERROR_LEN];
        ::strncpy(error, mysql_error(&m_mysql), sizeof(error)-1);
        error[SQL_ERROR_LEN-1] = '\0';

        //判断数据库是否断开了
        if(strstr(error, "SQL syntax"))
        {
            AK_LOG_WARN << "Execute failed,error: [" << error << "], SQL=" << str_sql;
            return -1;
        }
        else
        {
            //操作数据库失败强制重连,重连失败再打印日志即可
            //AK_LOG_WARN << "Force to reconnect DB: [" << error << "], SQL=" << str_sql;
            Disconnect();
            Connect();
            nRet = mysql_query(&m_mysql, str_sql.c_str());
            if(nRet != 0)
            {
                AK_LOG_WARN << "connect DB: [" << error << "], SQL=" << str_sql;
                return -1;
            }
        }
    }    
    int nAffectedRows = (int)mysql_affected_rows(&m_mysql);
    //added by chenyc,2017-04-25,即使是执行insert/delete/update等没有查询结果的操作,也许要将结果集读出来再释放掉.
    MYSQL_RES *result;
    result = mysql_store_result(&m_mysql);
    mysql_free_result(result); 

    return (int)nAffectedRows;
}

//返回影响的行数,入参为string类型,不释放查询结果
int CRldb::Execute_query(const std::string &str_sql)
{
    if(!connected_)
    {
        Connect();
        if(!connected_)
        {
            ThreadLocalSingleton::GetInstance().SetDbStatus(false);
            AK_LOG_WARN << "Execute failed: Database not connected.";
            return -2;
        }
    }

    int nRet = mysql_query(&m_mysql, str_sql.c_str());
    if(nRet != 0)
    { 
        char error[SQL_ERROR_LEN];
        ::strncpy(error, mysql_error(&m_mysql), sizeof(error)-1);
        error[SQL_ERROR_LEN-1] = '\0';

        //语法错误
        if(strstr(error, "SQL syntax"))
        {
            AK_LOG_WARN << "Execute failed,error: [" << error << "], SQL=" << str_sql;
            return -2;
        }
        //连接失败或Unknown column等
        else
        {
            //操作数据库失败强制重连
            //AK_LOG_WARN << "Force to reconnect DB: [" << error << "], SQL=" << str_sql;
            Disconnect();
            Connect();
            nRet = mysql_query(&m_mysql, str_sql.c_str());
            if(nRet != 0)
            {
                if(!strstr(error, "SQL syntax"))
                {
                    ThreadLocalSingleton::GetInstance().SetDbStatus(false);
                }
                AK_LOG_WARN << "connect DB: [" << error << "], SQL=" << str_sql;
                return -2;
            }
        }
    }
    int nAffectedRows = (int)mysql_affected_rows(&m_mysql);//add by chenzhx select 时候返回-1
    return (int)nAffectedRows;
}


//判断查询结果是否存在
bool CRldb::IsDataExist(const std::string& str_sql)
{
	Execute_query(str_sql);

	MYSQL_RES *pRes = NULL;
	//获取执行结果集并保存到类成员指针中
	if((pRes = mysql_store_result(&m_mysql)) == NULL)
	{
		return false;
	}

	unsigned long nRow = (unsigned long)mysql_num_rows(pRes);

	mysql_free_result(pRes);

	return (nRow > 0);
}


int CRldb::BeginTransAction()
{
	return mysql_query(&m_mysql, "START TRANSACTION");
}

int CRldb::EndTransAction()
{
	return mysql_query(&m_mysql, "COMMIT");
}

int CRldb::TransActionRollback()
{
	return mysql_query(&m_mysql, "rollback");
}

std::string CRldb::GetDbIP() const
{
    return db_ip_;

}
int CRldb::GetDbPort() const
{
    return db_port_;
}

bool CRldb::CheckDBConn() 
{
    if(connected_)
    {
        return connected_;
    }
    else
    {
        //可能是与mysql的连接已经断开，此时如果这个连接业务还未重新使用，不会变更状态，因此主动重连下
        //解决metrics无法自动恢复的问题
        Connect();
        return connected_;
    }
}

int CRldb::EscapeString(const std::string &from, std::string &to)
{
    char *escaped_string = new char[2 * from.length() + 1];
    memset(escaped_string, 0, 2 * from.length() + 1);
    mysql_real_escape_string(&m_mysql, escaped_string, from.c_str(), from.length());
    to = escaped_string;
    delete[] escaped_string;
    return 0;
}

void CRldb::ProcessInsertData(const std::map<std::string, std::string>& str_datas, const std::map<std::string, int>& int_datas, 
                                    std::string& keys, std::string& vals)
{
    // 处理 int 型数据
    for (const auto& data : int_datas) 
    {
        keys += data.first + ",";
        //int型value不需要转义
        vals += std::to_string(data.second) + ",";
    }

    // 处理字符串数据
    for (const auto& data : str_datas) 
    {
        //对value进行转义
        std::string escaped_val;
        EscapeString(data.second, escaped_val);

        std::string key;
        std::string val;

        if (data.first.find("sql_") == 0) //前缀为"sql_"的为数据库系统函数调用
        {
            key = data.first.substr(strlen("sql_"));
            val = escaped_val;
        } 
        else 
        {
            key = data.first;
            if(!Utf8Check(escaped_val))
            {
                AK_LOG_WARN << "Utf8 Check error, key:" << key <<" value:" << escaped_val;
                escaped_val.clear();
            }
            
            //字符类型还需要在两侧添加单引号
            val = "'" + escaped_val + "'";
        }
        keys += key + ",";
        vals += val + ",";
    }

    // 去除末尾逗号
    if (!keys.empty()) keys.pop_back();
    if (!vals.empty()) vals.pop_back();
}

void CRldb::ProcessUpdateData(const std::map<std::string, std::string>& str_datas, const std::map<std::string, int>& int_datas, std::string& update_clause)
{
    // 处理 int 型数据
    for (const auto& data : int_datas) 
    {
        update_clause += data.first + " = " + std::to_string(data.second) + ",";
    }

    // 处理字符串数据
    for (const auto& data : str_datas) 
    {
        std::string escaped_val;
        EscapeString(data.second, escaped_val);

        std::string key;
        std::string val;

        if (data.first.find("sql_") == 0) 
        {
            key = data.first.substr(strlen("sql_"));
            val = escaped_val;
        } 
        else
        {
            key = data.first;
            val = "'" + escaped_val + "'";
        }
        update_clause += key + " = " + val + ",";
    }

    // 去除末尾逗号
    if (!update_clause.empty()) update_clause.pop_back();
}

int CRldb::InsertData(const std::string& table_name, const std::map<std::string, std::string>& str_datas, const std::map<std::string, int>& int_datas)
{
    if (str_datas.empty() && int_datas.empty()) {
        AK_LOG_WARN << "data map is empty";
        return -1;
    }
    if (table_name.empty()) {
        AK_LOG_WARN << "table name is empty str";
        return -1;
    }

    std::string insert_keys;
    std::string insert_vals;
    std::stringstream sql;

    ProcessInsertData(str_datas, int_datas, insert_keys, insert_vals);

    sql << "insert into " << table_name << " (" << insert_keys << ") "
        << "VALUES (" << insert_vals << ")";
    if (Execute(sql.str()) < 0) {
        AK_LOG_WARN << "sql execute failed, sql:" << sql.str();
        return -1;
    }
    return 0;
}

int CRldb::InsertOrUpdateData(const std::string& table_name, const std::map<std::string, std::string>& insert_str_datas, const std::map<std::string, int>& insert_int_datas, 
                                const std::map<std::string, std::string>& update_str_datas, const std::map<std::string, int>& update_int_datas)
{
    if (insert_str_datas.empty() && insert_int_datas.empty()) 
    {
        AK_LOG_WARN << "The data insertion mapping is empty.";
        return -1;
    }

    if (update_str_datas.empty() && update_int_datas.empty()) 
    {
        AK_LOG_WARN << "The data update mapping is empty.";
        return -1;
    }
    
    if (table_name.empty()) {
        AK_LOG_WARN << "The table name is empty.";
        return -1;
    }

    std::string insert_keys;
    std::string insert_vals;
    std::string update_clause;
    std::stringstream sql;

    ProcessInsertData(insert_str_datas, insert_int_datas, insert_keys, insert_vals);
    ProcessUpdateData(update_str_datas, update_int_datas, update_clause);

    sql << "INSERT INTO " << table_name << " (" << insert_keys << ") "
        << "VALUES (" << insert_vals << ") "
        << "ON DUPLICATE KEY UPDATE " << update_clause;

    if (Execute(sql.str()) < 0) {
        AK_LOG_WARN << "SQL execution failed, SQL: " << sql.str();
        return -1;
    }
    return 0;
}


