#ifndef __ADAPT_RLDB_H__
#define __ADAPT_RLDB_H__

#include "mysql.h"
#include "ConnectionPool.h"
#include <memory>
#include <map>

class CRldbStmt;
class CRldb
{
public:
	CRldb(const std::string &db_ip, int db_port, const std::string &db_username,
               const std::string &db_password, const std::string &db_database, const std::string &app);
	~CRldb();
    CRldb() = delete;
    CRldb(CRldb& other) = delete;
    CRldb& operator = (CRldb& other) = delete;

	int Connect();
	int Disconnect();

    std::shared_ptr<CRldbStmt> Prepare(const std::string &str_sql);

	//返回影响的行数
	int Execute(const std::string &str_sql);
    int Execute_query(const std::string &str_sql);

	//判断查询结果是否存在
	bool IsDataExist(const std::string &str_sql);

	int BeginTransAction();
	int EndTransAction();
    int TransActionRollback();
    //added by chenyc,2022.03.22,增加dbconn对象的ip port接口,方便新旧db作比较
    std::string GetDbIP() const;
    int GetDbPort() const;
	static CRldb *GetInstance();
     // 插入数据并自动转义字段和值
    int InsertData(const std::string& table_name, const std::map<std::string, std::string>& str_datas, const std::map<std::string, int>& int_datas);
    int InsertOrUpdateData(const std::string& table_name, const std::map<std::string, std::string>& insert_str_datas, const std::map<std::string, int>& insert_int_datas, 
                                const std::map<std::string, std::string>& update_str_datas, const std::map<std::string, int>& update_int_datas);
    void ProcessUpdateData(const std::map<std::string, std::string>& str_datas, const std::map<std::string, int>& int_datas, std::string& update_clause);
    void ProcessInsertData(const std::map<std::string, std::string>& str_datas, const std::map<std::string, int>& int_datas, std::string& keys, std::string& vals);
	MYSQL m_mysql;
    bool CheckDBConn();
private:
	bool connected_;

    std::string db_username_;
    std::string db_password_;
    std::string db_database_;
    std::string db_ip_;
    int db_port_;
    std::string app_;
    std::string out_ip_;

	static CRldb *instance;
    int EscapeString(const std::string &from, std::string &to);
};

#endif //__ADAPT_RLDB_H__

