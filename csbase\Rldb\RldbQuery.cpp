
#include <string.h>

#include "RldbQuery.h"
#include "AkLogging.h"



CRldbQuery::CRldbQuery(CRldb *pRldb)
{
	m_pMySQLRes = NULL;
	m_pRldb = pRldb;
	m_pRow = NULL;
	m_nFieldCount = 0;
}

CRldbQuery::~CRldbQuery()
{
	if(m_pMySQLRes != NULL)
	{
		mysql_free_result(m_pMySQLRes);
		m_pMySQLRes = NULL;
	}
}

//返回查询行数
//added by chenyc,2017-06-14,注意不能嵌套查询,但是是会话安全的.每个数据库连接都有自己的结果集存放处
long CRldbQuery::Query(const std::string &strSQL)
{
	if(m_pRldb == NULL)
	{
		return 0;
	}

	if(m_pMySQLRes != NULL)
	{
		mysql_free_result(m_pMySQLRes);
	}

    int ret = 0;
	ret = m_pRldb->Execute_query(strSQL);

	//获取执行结果集并保存到类成员指针中
	if((m_pMySQLRes = mysql_store_result(&m_pRldb->m_mysql)) == NULL)
	{
		return -1;
	}
	m_nFieldCount = mysql_num_fields(m_pMySQLRes);

    if (ret == -2)
    {
        return -1;
    }

	return (unsigned long)mysql_num_rows(m_pMySQLRes);
}

//移动到下一行,并判断是否还有数据
bool CRldbQuery::MoveToNextRow()
{
	if(m_pMySQLRes == NULL)
	{
		return false;
	}
	m_pRow = mysql_fetch_row(m_pMySQLRes);
	return (m_pRow != NULL);
}

//获取当前行的某列数据
const char *CRldbQuery::GetRowData(unsigned int nCol)
{
	if((m_pRow == NULL) || (nCol >= m_nFieldCount))
	{
		return "";
	}

	return m_pRow[nCol] == NULL ? "" : m_pRow[nCol];
}

int CRldbQuery::GetColNo(const char *pszColName)
{
	int nCount = 0;
	if((pszColName == NULL) || (m_pMySQLRes == NULL))
	{
		return -1;
	}
	//char szColName[128] = "";
	//TransTcharToUtf8(pszColName, szColName, sizeof(szColName));
	MYSQL_FIELD *pField;
	while((pField = mysql_fetch_field(m_pMySQLRes)) != NULL) 
	{
		if(strcmp(pszColName, pField->name) == 0)
		{
			break;
		}

		nCount++;
	}

	return nCount;
}

//获取当前列名的某列数据
const char *CRldbQuery::GetRowData(const std::string strColName)
{
	//yicong.chen,注意加了.GetLength()
    int nCol = GetColNo(strColName.c_str());
	if((nCol < 0) || (nCol >= (int)m_nFieldCount))
	{
		return "";
	}

	return m_pRow[nCol] == NULL ? "" : m_pRow[nCol];
}
