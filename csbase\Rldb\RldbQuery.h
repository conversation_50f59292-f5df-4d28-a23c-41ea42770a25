#ifndef __RLDB_QUERY_H__
#define __RLDB_QUERY_H__

#include "BasicDefine.h"
#include "mysql.h"
#include "Rldb.h"

//前置声明
class CRldb;
class CRldbQuery
{
public:
	CRldbQuery(CRldb *pRldb);
	~CRldbQuery();

	//返回查询行数
    long Query(const std::string &strSQL);
	
	//移动到下一列 
	bool MoveToNextRow();

	//获取当前行的某列数据
	const char *GetRowData(unsigned int nCol);

	//获取当前列名的某列数据
	const char *GetRowData(const std::string strColName);

	int GetColNo(const char *pszColName);

private:
	MYSQL_RES *m_pMySQLRes;
	CRldb *m_pRldb;
	MYSQL_ROW m_pRow;
	unsigned int m_nFieldCount;
};

#endif //__RLDB_QUERY_H__
