﻿#include "RldbStmt.h"
#include "AkLogging.h"


CRldbStmt::CRldbStmt(MYSQL_STMT* stmt)
{
    stmt_ = stmt;
    count_ = mysql_stmt_param_count(stmt_);
    if (count_ > 0)
    {
        params_ = new MYSQL_BIND[count_];
        memset(params_, 0, count_ * sizeof(MYSQL_BIND));
    }
    else
    {
        params_ = nullptr;
    }
    mysql_res_ = nullptr;
    memset(result_, 0,  sizeof(result_));
}

CRldbStmt::~CRldbStmt()
{
    if (stmt_)
    {
        mysql_stmt_close(stmt_);
        stmt_ = nullptr;
    }

    if (params_)
    {
        delete[] params_;
        params_ = nullptr;
    }

    if (mysql_res_)
    {
        mysql_free_result(mysql_res_);
        mysql_res_ = nullptr;
    }
}

int CRldbStmt::BindParam(int i, enum_field_types buffer_type, void* buffer, int buffer_length, my_bool* is_null, long unsigned int* length)
{
    if ((size_t)i >= count_)
    {
        AK_LOG_WARN << "Stmt::bind_param error! index:" << i << ",count:" << count_;
        return -1;
    }

    MYSQL_BIND& b   = params_[i];
    b.buffer_type   = buffer_type;
    b.buffer        = (char*)buffer;
    b.buffer_length = buffer_length;
    b.is_null       = is_null;
    b.length        = length;

    return 0;
}

int CRldbStmt::BindInt(int i, const int64_t& value)
{
    return BindParam(i, MYSQL_TYPE_LONGLONG, (char*)&value, 0, 0, 0);
}

int CRldbStmt::BindOutInt64(int i, int64_t* value)
{
    return BindOut(i, MYSQL_TYPE_LONGLONG, value, 0);
}

int CRldbStmt::BindOutInt32(int i, int32_t* value)
{
    return BindOut(i, MYSQL_TYPE_LONG, value, 0);
}

int CRldbStmt::BindOut(int i, enum_field_types buffer_type, void* buffer, int buffer_length)
{
    if (i >= MAX_FETCH_COL)
    {
        AK_LOG_WARN << "Stmt::bind_param error! index:" << i << ",max_fetch_columns:" << MAX_FETCH_COL;
        return -1;
    }

    MYSQL_BIND& b   = result_[i];
    b.buffer_type   = buffer_type;
    b.buffer        = (char*)buffer;
    b.buffer_length = buffer_length;
    b.is_null       = &is_null_[i];
    b.length        = &length_[i];

    return 0;
}

int CRldbStmt::BindTinyInt(int i, const int32_t& value)
{
    return BindParam(i, MYSQL_TYPE_SHORT, (char*)&value, 0, 0, 0);
}

int CRldbStmt::BindOutTinyInt(int i, int16_t* value)
{
    return BindOut(i, MYSQL_TYPE_SHORT, value, 0);
}

int CRldbStmt::BindString(int i, const char* value)
{
    return BindParam(i, MYSQL_TYPE_STRING, (char*)value, strlen(value), 0,  &(params_[i].buffer_length));
}

int CRldbStmt::BindOutString(int i, char* value, int len)
{
    return BindOut(i, MYSQL_TYPE_STRING, value, len);
}

int CRldbStmt::BindString(int i, const std::string& value)
{
    return BindParam(i, MYSQL_TYPE_STRING, (char*)value.c_str(), value.size(), 0, &(params_[i].buffer_length));
}

int64_t CRldbStmt::Execute()
{
    if (nullptr == stmt_)
    {
        AK_LOG_WARN << "Execute failed.Prepare first";
        return -1;
    }

    if (params_ != nullptr)
    {
        if (mysql_stmt_bind_param(stmt_, params_))
        {
            AK_LOG_WARN << "mysql_stmt_bind_param() failed,error msg:" << mysql_stmt_error(stmt_);
            return -1;
        }
    }

    if (mysql_stmt_execute(stmt_))
    {
        AK_LOG_WARN << "mysql_stmt_execute() failed,error msg:" << mysql_stmt_error(stmt_);
        return -1;
    }

    uint64_t affected_rows = mysql_stmt_affected_rows(stmt_);
    return (int64_t)affected_rows;
}

bool CRldbStmt::Fetch()
{
    if (nullptr == mysql_res_)
    {
        mysql_res_ = mysql_stmt_result_metadata(stmt_);
        if (nullptr == mysql_res_)
        {
            return false;
        }

        if (mysql_stmt_bind_result(stmt_, result_))
        {
            AK_LOG_WARN << "mysql_stmt_bind_result failed,error msg:" << mysql_stmt_error(stmt_);
            return false;
        }

        if (mysql_stmt_store_result(stmt_))
        {
            AK_LOG_WARN << "mysql_stmt_store_result failed,error msg:" << mysql_stmt_error(stmt_);
            return false;
        }
    }

    const char* msg = nullptr;
    int ret = mysql_stmt_fetch(stmt_);
    switch (ret)
    {
        case 0:
            return true;
        case MYSQL_NO_DATA:
            return false;
        case MYSQL_DATA_TRUNCATED:
            msg = "data truncated";
            break;
        default:
            msg = mysql_stmt_error(stmt_);
            break;
    }

    if (msg)
    {
        AK_LOG_WARN << "Fetch failed,error msg:" << msg;
        return false;
    }
    return true;
}

const char* CRldbStmt::ErrorMsg()
{
    if (stmt_)
    {
        return mysql_stmt_error(stmt_);
    }
    else
    {
        return "";
    }
}

