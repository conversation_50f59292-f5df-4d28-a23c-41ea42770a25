﻿#ifndef _RLDB_STMT_H_
#define _RLDB_STMT_H_

#include "BasicDefine.h"
#include "mysql.h"

const int MAX_FETCH_COL = 128;

class CRldbStmt
{
public:
    CRldbStmt(MYSQL_STMT* stmt);
    ~CRldbStmt();
    int BindParam(int i, enum_field_types buffer_type, void* buffer, int buffer_length, my_bool* is_null, long unsigned int* length);
    /**
     * 绑定输入
     *
     * <AUTHOR> (2021/3/23)
     *
     * @param i
     * @param value
     *
     * @return int
     */
    int BindInt(int i, const int64_t& value);
    /**
     * tinyint数据类型只能用这个方法
     * 有符号:-128 ~ 127
     * 无符号:0 ~ 255
     * <AUTHOR> (2021/3/23)
     *
     * @param i
     * @param value
     *
     * @return int
     */
    int BindTinyInt(int i, const int32_t& value);

    /**
     * char *value should long live tot execute()
     *
     * <AUTHOR> (2021/3/23)
     *
     * @param i
     * @param value
     *
     * @return int
     */
    int BindString(int i, const char* value);
    /**
     * 绑定输入
     *
     * <AUTHOR> (2021/3/23)
     *
     * @param i
     * @param value
     *
     * @return int
     */
    int BindString(int i, const std::string& value);
    /**
     * 返回值小于0,表示失败
     *
     * <AUTHOR> (2021/3/23)
     *
     * @return int64_t
     */
    int64_t Execute();

    /**
     * 查询数据
     *
     * <AUTHOR> (2021/3/23)
     *
     * @return bool
     */
    bool Fetch();

    /**
     * 绑定输出
     *
     * <AUTHOR> (2021/3/23)
     *
     * @param i
     * @param buffer_type
     * @param buffer
     * @param buffer_length
     *
     * @return int
     */
    int BindOut(int i, enum_field_types buffer_type, void* buffer, int buffer_length);
    /**
     * 绑定输出
     *
     * <AUTHOR> (2021/3/23)
     *
     * @param i
     * @param value
     *
     * @return int
     */
    int BindOutInt64(int i, int64_t* value);
    /**
     * 绑定输出
     *
     * <AUTHOR> (2021/3/23)
     *
     * @param i
     * @param value
     *
     * @return int
     */
    int BindOutInt32(int i, int32_t* value);
    /**
     * 绑定输出
     *
     * <AUTHOR> (2021/3/23)
     *
     * @param i
     * @param value
     *
     * @return int
     */
    int BindOutTinyInt(int i, int16_t* value);
    /**
     * 绑定输出,注意value的长度要能存放的下
     *
     * <AUTHOR> (2021/3/23)
     *
     * @param i
     * @param value
     * @param len
     *
     * @return int
     */
    int BindOutString(int i, char* value, int len);
    /**
     * 返回失败原因
     *
     * <AUTHOR> (2021/3/23)
     *
     * @return const char*
     */
    const char* ErrorMsg();
private:
    CRldbStmt()
    {
        stmt_ = nullptr;
        count_ = 0;
        params_ = nullptr;
        mysql_res_ = nullptr;
    }
private:
    MYSQL_STMT* stmt_;
    MYSQL_BIND* params_;
    size_t count_;

    MYSQL_BIND result_[MAX_FETCH_COL];
    unsigned long length_[MAX_FETCH_COL];
    my_bool is_null_[MAX_FETCH_COL];
    MYSQL_RES* mysql_res_;
};

#endif

