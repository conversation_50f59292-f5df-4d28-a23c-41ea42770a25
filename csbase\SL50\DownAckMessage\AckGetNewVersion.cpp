#include "AckGetNewVersion.h"

AckGetNewVersion::AckGetNewVersion(std::string &version_no, std::string &device_version, std::string& upgrade_id)
{
    version_no_ = version_no;
    device_version_ = device_version;
    upgrade_id_ = upgrade_id;
    immediately_ = false;
    is_forced_ = false;
}

void AckGetNewVersion::SetAckID(std::string &id)
{
    id_ = id;
}

std::string AckGetNewVersion::to_json() 
{
    Json::Value j, param;
    AckBaseParam::to_json(j, id_, COMMOND);
    
    param["version_no"] = version_no_;
    param["device_version"] = device_version_;
    param["upgrade_id"] = upgrade_id_;
    param["immediately"] = immediately_;
    param["is_forced"] = is_forced_;
    
    j["param"] = param;
    Json::FastWriter writer;
    return writer.write(j);
}
