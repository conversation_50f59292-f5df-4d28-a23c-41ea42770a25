#ifndef __ACK_GET_NEW_VERSION_H_
#define __ACK_GET_NEW_VERSION_H_

#include <iostream>
#include <memory>
#include <json/json.h>
#include "SL50/DownMessage/DownMessageBase.h"

class AckGetNewVersion :public AckBaseParam{
public:
    AckGetNewVersion(std::string &version_no, std::string &device_version, std::string& upgrade_id);

    //如果没有设置代表主动下行的消息，有设置代表是设备请求后在下行的消息
    void SetAckID(std::string &id);
    ~AckGetNewVersion() = default;

    static constexpr const char* COMMOND = "v1.0_u_get_new_version";

    std::string to_json();

    std::string id_;
    std::string version_no_;
    std::string device_version_;
    std::string upgrade_id_;
    bool immediately_;
    bool is_forced_;
};

#endif