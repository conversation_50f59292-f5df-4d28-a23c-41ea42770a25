#include "AckGetSipGroup.h"

AckGetSipGroup::AckGetSipGroup(std::string &sip_group)
{
    sip_group_ = sip_group;
}

void AckGetSipGroup::SetAckID(std::string &id)
{
    id_ = id;
}

std::string AckGetSipGroup::to_json() {
    if(id_.empty())
    {
        id_ = std::to_string(AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId());
    }
    Json::Value j, param;
    AckBaseParam::to_json(j, id_, COMMOND);
    
    param["sip_group"] = sip_group_;
    
    j["param"] = param;
    Json::FastWriter writer;
    return writer.write(j);
}