#ifndef __ACK_GET_SIP_GROUP_H_
#define __ACK_GET_SIP_GROUP_H_
#include <iostream>
#include <memory>
#include <json/json.h>
#include "SL50/DownMessage/DownMessageBase.h"

class AckGetSipGroup :public AckBaseParam{
public:
    AckGetSipGroup(std::string &sip_group);
    //如果没有设置代表主动下行的消息，有设置代表是设备请求后在下行的消息
    void SetAckID(std::string &id);
    ~AckGetSipGroup() = default;

    static constexpr const char* COMMOND = "v1.0_u_get_sip_group";
    static constexpr const char* AKCS_COMMAND = "v1.0_u_get_sip_group_AKCS";

    std::string to_json();

    std::string sip_group_;
    std::string id_;
};

#endif