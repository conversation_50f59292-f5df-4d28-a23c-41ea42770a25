#include "AckGetSipInfo.h"

AckGetSipInfo::AckGetSipInfo(std::string &server_url, std::string &sip, std::string &password, int sip_type, int confusion)
{
    server_url_ = server_url;
    sip_ = sip;
    password_ = password;
    sip_type_ = sip_type;
    confusion_ = confusion;
}

void AckGetSipInfo::SetAckID(std::string &id)
{
    id_ = id;
}

std::string AckGetSipInfo::to_json() {
    if(id_.empty())
    {
        id_ = std::to_string(AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId());
    }
    Json::Value j, param;
    AckBaseParam::to_json(j, id_, COMMOND);
    
    param["server_url"] = server_url_;
    param["sip"] = sip_;
    param["password"] = password_;
    param["sip_type"] = sip_type_;
    param["confusion"] = confusion_;
    
    j["param"] = param;
    Json::FastWriter writer;
    return writer.write(j);
}