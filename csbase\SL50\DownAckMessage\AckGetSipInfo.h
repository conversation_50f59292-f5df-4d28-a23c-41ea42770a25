#ifndef __ACK_GET_SIP_INFO_H_
#define __ACK_GET_SIP_INFO_H_
#include <iostream>
#include <memory>
#include <json/json.h>
#include "SL50/DownMessage/DownMessageBase.h"

class AckGetSipInfo :public AckBaseParam{
public:
    AckGetSipInfo(std::string &server_url, std::string &sip, std::string &password, int sip_type, int confusion);
    //如果没有设置代表主动下行的消息，有设置代表是设备请求后在下行的消息
    void SetAckID(std::string &id);
    ~AckGetSipInfo() = default;

    static constexpr const char* COMMOND = "v1.0_u_get_sip_info";
    static constexpr const char* AKCS_COMMAND = "v1.0_u_get_sip_info_AKCS";

    std::string to_json();

    std::string server_url_;
    std::string sip_;
    std::string password_;
    int sip_type_;
    int confusion_;
    std::string id_;
};

#endif