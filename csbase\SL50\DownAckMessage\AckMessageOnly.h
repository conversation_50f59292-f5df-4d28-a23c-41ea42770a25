#ifndef __ACK_MESSAGE_ONLY_H_
#define __ACK_MESSAGE_ONLY_H_
#include <iostream>
#include <memory>
#include <json/json.h>
#include "SL50/DownMessage/DownMessageBase.h"

class AckMessageOnly :public AckBaseParam{
public:
    AckMessageOnly(const std::string &id, std::string &command);
    ~AckMessageOnly() = default;

    std::string to_json();

    std::string id_;
    std::string command_;

};

#endif