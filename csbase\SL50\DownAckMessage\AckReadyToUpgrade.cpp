#include "AckReadyToUpgrade.h"

AckReadyToUpgrade::AckReadyToUpgrade(std::string &upgrade_id, std::string &version_path, std::string& product_path, 
                    std::string& dependency_path, std::string& record, std::string& record_path)
{
    upgrade_id_ = upgrade_id;
    version_path_ = version_path;
    product_path_ = product_path;
    dependency_path_ = dependency_path;
    record_ = record;
    record_path_ = record_path;
}

void AckReadyToUpgrade::SetAckID(std::string &id)
{
    id_ = id;
}

std::string AckReadyToUpgrade::to_json() 
{
    Json::Value j, param;
    AckBaseParam::to_json(j, id_, COMMOND);
    
    param["upgrade_id"] = upgrade_id_;
    param["version_path"] = version_path_;
    param["product_path"] = product_path_;
    param["dependency_path"] = dependency_path_;
    param["record"] = record_;
    param["record_path"] = record_path_;
    
    j["param"] = param;
    Json::FastWriter writer;
    return writer.write(j);
}
