#ifndef __ACK_READY_TO_UPGRADE_H_
#define __ACK_READY_TO_UPGRADE_H_

#include <iostream>
#include <memory>
#include <json/json.h>
#include "SL50/DownMessage/DownMessageBase.h"

class AckReadyToUpgrade :public AckBaseParam{
public:
    AckReadyToUpgrade(std::string &upgrade_id, std::string &version_path, std::string& product_path, 
                    std::string& dependency_path, std::string& record, std::string& record_path);

    //如果没有设置代表主动下行的消息，有设置代表是设备请求后在下行的消息
    void SetAckID(std::string &id);
    ~AckReadyToUpgrade() = default;

    static constexpr const char* COMMOND = "v1.0_u_ready_to_upgrade";

    std::string to_json();

    std::string id_;
    std::string upgrade_id_;
    std::string version_path_;
    std::string product_path_;
    std::string dependency_path_;
    std::string record_;
    std::string record_path_;
};

#endif