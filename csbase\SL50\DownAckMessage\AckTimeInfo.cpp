#include "AckTimeInfo.h"

AckTimeInfo::AckTimeInfo(std::string &timezone, std::string &daylight_timezone)
{
    timezone_ = timezone;
    daylight_timezone_ = daylight_timezone;
}

void AckTimeInfo::SetAckID(std::string &id)
{
    id_ = id;
}

std::string AckTimeInfo::to_json() {
    if(id_.empty())
    {
        id_ = std::to_string(AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId());
    }
    Json::Value j, param;
    AckBaseParam::to_json(j, id_, COMMOND);
    
    param["timezone"] = timezone_;
    param["daylight_timezone"] = daylight_timezone_;
    param["cloud_timestamp"] = GetCurrentMilliTimeStamp();;
    
    j["param"] = param;
    Json::FastWriter writer;
    return writer.write(j);
}
