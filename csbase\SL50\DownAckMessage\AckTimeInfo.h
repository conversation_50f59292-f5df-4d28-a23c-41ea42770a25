#ifndef __ACK_TIMEINFO_H_
#define __ACK_TIMEINFO_H_
#include <iostream>
#include <memory>
#include <json/json.h>
#include "SL50/DownMessage/DownMessageBase.h"

class AckTimeInfo :public AckBaseParam{
public:
    AckTimeInfo(std::string &timezone,std::string &daylight_timezone);
    //如果没有设置代表主动下行的消息，有设置代表是设备请求后在下行的消息
    void SetAckID(std::string &id);
    ~AckTimeInfo() = default;

    static constexpr const char* COMMOND = "v1.0_u_get_timezone_and_timetamp";
    static constexpr const char* AKCS_COMMAND = "v1.0_u_get_timezone_and_timetamp_AKCS";

    std::string to_json();

    std::string timezone_;
    std::string daylight_timezone_;
    std::string id_;
};

#endif