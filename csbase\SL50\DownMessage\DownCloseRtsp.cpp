#include "DownCloseRtsp.h"
DownCloseRtsp::DownCloseRtsp( const std::string& device)
    : device_id_(device) {}

std::string DownCloseRtsp::to_json() {
    Json::Value param;
    Json::Value j;
    BaseParam::to_json(j, COMMOND);

    param["device_id"] = device_id_;
    
    j["param"] = param;
    Json::FastWriter writer;
    return writer.write(j);
}

void DownCloseRtsp::from_json(const std::string& json_str) {
    Json::Value j;
    Json::Reader reader;
    if (!reader.parse(json_str, j)) {
        throw std::runtime_error("Failed to parse JSON string");
    }
    
    if (j.isMember("id")) id_ = j["id"].asString();
    if (j.isMember("command")) command_ = j["command"].asString();

    if (j.isMember("param")) {
        Json::Value param = j["param"];
        if (param.isMember("device_id")) device_id_ = param["device_id"].asString();
    }
}