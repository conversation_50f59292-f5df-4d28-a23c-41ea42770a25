#ifndef __DOWN_CLOSE_RTSP_H_
#define __DOWN_CLOSE_RTSP_H_
#include <iostream>
#include <memory>
#include <json/json.h>
#include "DownMessageBase.h"

class DownCloseRtsp : public BaseParam {
public:
    static constexpr const char* COMMOND = "v1.0_d_close_rtsp";
    static constexpr const char* AKCS_COMMAND = "v1.0_d_close_rtsp";

    // 业务参数
    std::string device_id_;

    DownCloseRtsp(const std::string& device);

    std::string to_json();
    void from_json(const std::string& json_str);
};
#endif