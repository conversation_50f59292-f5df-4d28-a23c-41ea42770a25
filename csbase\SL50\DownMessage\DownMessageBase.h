#ifndef __DOWN_MESSAGE_BASE_H__
#define  __DOWN_MESSAGE_BASE_H__
#include <iostream>
#include <memory>
#include <json/json.h>
#include "gid/SnowFlakeGid.h"
#include "util_time.h"
// ---------- ServiceCall ----------
class ServiceCall {
public:
    std::string type_;
    std::string domain_;
    std::string service_;
    int service_id_ = 700;
    
    ServiceCall(const std::string& t, const std::string& d, const std::string& s) 
        : type_(t), domain_(d), service_(s) {}
    
    bool isValid() const {
        return !type_.empty() && !domain_.empty() && !service_.empty();
    }
    
    void to_json(Json::Value& json) const {
        json["type"] = type_;
        json["domain"] = domain_;
        json["service"] = service_;
        json["id"] = service_id_;
    }
    
    void from_json(const Json::Value& json) {
        type_ = json["type"].asString();
        domain_ = json["domain"].asString();
        service_ = json["service"].asString();
        
        if (!isValid()) {
            throw std::runtime_error("Invalid ServiceCall parameters");
        }
    }
};


class BaseParam {
public:
    std::string trace_id_;
    std::string id_;
    std::string command_;
    
    virtual ~BaseParam() {}
    virtual void to_json(Json::Value& j, const std::string &command) {
        trace_id_ = std::to_string(AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId());
        j["id"] = trace_id_;
        j["command"] = command;
    }
};

class AckBaseParam {
    public:
        std::string id_;
        std::string command_;
        
        virtual ~AckBaseParam() {}
        virtual void to_json(Json::Value& j, const std::string &id, const std::string &command) {
            j["id"] = id;
            j["command"] = command;
            j["success"] = true;
            j["timestamp"] = GetCurrentMilliTimeStamp();
        }
};

#endif