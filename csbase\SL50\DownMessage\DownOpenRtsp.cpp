#include "DownOpenRtsp.h"

DownOpenRtsp::DownOpenRtsp() {}

void DownOpenRtsp::SetDeviceId(const std::string& device_id) {
    device_id_ = device_id;
}

void DownOpenRtsp::SetKeepalive(int keepalive) {
    keepalive_ = keepalive;
}

void DownOpenRtsp::SetRtspIp(const std::string& rtsp_ip) {
    rtsp_ip_ = rtsp_ip;
}

void DownOpenRtsp::SetRtspPort(int rtsp_port) {
    rtsp_port_ = rtsp_port;
}

void DownOpenRtsp::SetSsrc(const std::string& ssrc) {
    ssrc_ = ssrc;
}

void DownOpenRtsp::SetAudioPort(int audio_port) {
    audio_port_ = audio_port;
}

void DownOpenRtsp::SetAudioSsrc(const std::string& audio_ssrc) {
    audio_ssrc_ = audio_ssrc;
}

void DownOpenRtsp::SetAudioReceivePort(int audio_receive_port) {
    audio_receive_port_ = audio_receive_port;
}

std::string DownOpenRtsp::to_json() {
    Json::Value param;
    Json::Value j;
    BaseParam::to_json(j, COMMOND);

    param["device_id"] = device_id_;
    param["keepalive"] = keepalive_;
    param["rtsp_ip"] = rtsp_ip_;
    param["rtsp_port"] = rtsp_port_;
    param["ssrc"] = ssrc_;
    param["audio_port"] = audio_port_;
    param["audio_ssrc"] = audio_ssrc_;
    param["audio_receive_port"] = audio_receive_port_;
    
    j["param"] = param;
    Json::FastWriter writer;
    return writer.write(j);
}

void DownOpenRtsp::from_json(const std::string& json_str) {
    Json::Value j;
    Json::Reader reader;
    if (!reader.parse(json_str, j)) {
        throw std::runtime_error("Failed to parse JSON string");
    }
    
    if (j.isMember("id")) id_ = j["id"].asString();
    if (j.isMember("command")) command_ = j["command"].asString();

    if (j.isMember("param")) {
        Json::Value param = j["param"];
        if (param.isMember("device_id")) device_id_ = param["device_id"].asString();
        if (param.isMember("keepalive")) keepalive_ = param["keepalive"].asInt();
        if (param.isMember("rtsp_ip")) rtsp_ip_ = param["rtsp_ip"].asString();
        if (param.isMember("rtsp_port")) rtsp_port_ = param["rtsp_port"].asInt();
        if (param.isMember("ssrc")) ssrc_ = param["ssrc"].asString();
        if (param.isMember("audio_port")) audio_port_ = param["audio_port"].asInt();
        if (param.isMember("audio_ssrc")) audio_ssrc_ = param["audio_ssrc"].asString();
        if (param.isMember("audio_receive_port")) audio_receive_port_ = param["audio_receive_port"].asInt();
    }
}