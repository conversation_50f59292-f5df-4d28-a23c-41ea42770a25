#ifndef __DOWN_OPEN_RTSP_H_
#define __DOWN_OPEN_RTSP_H_
#include <iostream>
#include <memory>
#include <json/json.h>
#include "DownMessageBase.h"

class DownOpenRtsp : public BaseParam {
public:
    static constexpr const char* COMMOND = "v1.0_d_open_rtsp";
    static constexpr const char* AKCS_COMMAND = "v1.0_d_open_rtsp";

    // 业务参数
    std::string device_id_;
    int keepalive_;
    std::string rtsp_ip_;
    int rtsp_port_;
    std::string ssrc_;
    int audio_port_;
    std::string audio_ssrc_;
    int audio_receive_port_;

    DownOpenRtsp();
    ~DownOpenRtsp() = default;

    // 参数设置函数
    void SetDeviceId(const std::string& device_id);
    void SetKeepalive(int keepalive);
    void SetRtspIp(const std::string& rtsp_ip);
    void SetRtspPort(int rtsp_port);
    void SetSsrc(const std::string& ssrc);
    void SetAudioPort(int audio_port);
    void SetAudioSsrc(const std::string& audio_ssrc);
    void SetAudioReceivePort(int audio_receive_port);

    std::string to_json();
    void from_json(const std::string& json_str);
};
#endif