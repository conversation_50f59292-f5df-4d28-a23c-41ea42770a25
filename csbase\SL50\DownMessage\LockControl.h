#ifndef __LOCK_CONTROL_H_
#define __LOCK_CONTROL_H_
#include <iostream>
#include <memory>
#include <json/json.h>
#include "DownMessageBase.h"

class LockControl : public BaseParam, public ServiceCall {
public:
    static constexpr const char* DEFAULT_SERVICE_TYPE = "call_service";
    static constexpr const char* DEFAULT_SERVICE_DOMAIN = "lock";
    static constexpr const char* SERVICE_NAME = "lock";
    static constexpr const char* COMMOND = "v1.0_d_device_ha_control";
    static constexpr const char* AKCS_COMMAND = "v1.0_d_device_ha_control_lock";

    std::string entity_id_;
    std::string device_id_;

    LockControl();
    LockControl(const std::string& entity, const std::string& device);

    std::string to_json();
    void from_json(const std::string& json_str);
};

#endif