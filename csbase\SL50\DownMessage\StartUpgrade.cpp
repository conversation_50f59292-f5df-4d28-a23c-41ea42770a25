#include "StartUpgrade.h"

StartUpgrade::StartUpgrade() : ServiceCall(DEFAULT_SERVICE_TYPE, DEFAULT_SERVICE_DOMAIN, SERVICE_NAME) {}

StartUpgrade::StartUpgrade(const std::string& device_id)
    : ServiceCall(DEFAULT_SERVICE_TYPE, DEFAULT_SERVICE_DOMAIN, SERVICE_NAME),
      device_id_(device_id) { type_ = std::string("config/ak_device/ctrl"); action_ = std::string("upgrade"); }

std::string StartUpgrade::to_json() 
{
    Json::Value param;
    Json::Value j;
    BaseParam::to_json(j, COMMOND);
    ServiceCall::to_json(param);
    Json::Value sd;
    sd["type"] = type_;
    sd["action"] = action_;
    sd["device_id"] = device_id_;

    param["service_data"] = sd;
    
    j["param"] = param;
    Json::FastWriter writer;
    return writer.write(j);
}

void StartUpgrade::from_json(const std::string& json_str) {
    Json::Value j;
    Json::Reader reader;
    if (!reader.parse(json_str, j)) {
        throw std::runtime_error("Failed to parse JSON string");
    }
    
    if (j.isMember("id")) id_ = j["id"].asString();
    if (j.isMember("command")) command_ = j["command"].asString();
    ServiceCall::from_json(j);
    if (service_ != SERVICE_NAME) throw std::runtime_error("Invalid service name for UnLockControl");
    if (j.isMember("service_data")) {
        Json::Value sd = j["service_data"];
        if (sd.isMember("type")) type_ = sd["type"].asString();
        if (sd.isMember("action")) action_ = sd["action"].asString();
        if (sd.isMember("device_id")) device_id_ = sd["device_id"].asString();
    }
}