#ifndef __START_UPGRADE_H__
#define __START_UPGRADE_H__
#include <iostream>
#include <memory>
#include <json/json.h>
#include "DownMessageBase.h"

class StartUpgrade : public BaseParam, public ServiceCall {
public:
    static constexpr const char* DEFAULT_SERVICE_TYPE = "call_service";
    static constexpr const char* DEFAULT_SERVICE_DOMAIN = "lock";
    static constexpr const char* SERVICE_NAME = "start_upgrade";
    static constexpr const char* COMMOND = "v1.0_d_device_ctrl";
    static constexpr const char* AKCS_COMMOND = "v1.0_d_device_ctrl";

    StartUpgrade();
    StartUpgrade(const std::string& device_id);

    std::string to_json();
    void from_json(const std::string& json_str);

    std::string type_;
    std::string action_;
    std::string device_id_;
};
#endif