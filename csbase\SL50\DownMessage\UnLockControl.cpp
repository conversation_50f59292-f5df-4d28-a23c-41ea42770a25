#include "UnLockControl.h"

UnLockControl::UnLockControl() : ServiceCall(DEFAULT_SERVICE_TYPE, DEFAULT_SERVICE_DOMAIN, SERVICE_NAME) {}

UnLockControl::UnLockControl(const std::string& entity, const std::string& device)
    : ServiceCall(DEFAULT_SERVICE_TYPE, DEFAULT_SERVICE_DOMAIN, SERVICE_NAME),
      device_id_(device), entity_id_(entity) {}

UnLockControl::UnLockControl(const std::string& device)
    : ServiceCall(DEFAULT_SERVICE_TYPE, DEFAULT_SERVICE_DOMAIN, SERVICE_NAME),
      device_id_(device) { entity_id_ = std::string("lock.") + device_id_; }

std::string UnLockControl::to_json() {
    Json::Value param;
    Json::Value j;
    BaseParam::to_json(j, COMMOND);
    ServiceCall::to_json(param);
    Json::Value sd;
    sd["entity_id"] = entity_id_;
    sd["device_id"] = device_id_;
    param["service_data"] = sd;
    
    j["param"] = param;
    Json::FastWriter writer;
    return writer.write(j);
}

void UnLockControl::from_json(const std::string& json_str) {
    Json::Value j;
    Json::Reader reader;
    if (!reader.parse(json_str, j)) {
        throw std::runtime_error("Failed to parse JSON string");
    }
    
    if (j.isMember("id")) id_ = j["id"].asString();
    if (j.isMember("command")) command_ = j["command"].asString();
    ServiceCall::from_json(j);
    if (service_ != SERVICE_NAME) throw std::runtime_error("Invalid service name for UnLockControl");
    if (j.isMember("service_data")) {
        Json::Value sd = j["service_data"];
        if (sd.isMember("entity_id")) entity_id_ = sd["entity_id"].asString();
        if (sd.isMember("device_id")) device_id_ = sd["device_id"].asString();
    }
}