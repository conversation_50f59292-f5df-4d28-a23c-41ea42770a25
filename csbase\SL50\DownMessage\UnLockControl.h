#ifndef __UNLCK_CONTROL_H_
#define __UNLCK_CONTROL_H_
#include <iostream>
#include <memory>
#include <json/json.h>
#include "DownMessageBase.h"

class UnLockControl : public BaseParam, public ServiceCall {
public:
    static constexpr const char* DEFAULT_SERVICE_TYPE = "call_service";
    static constexpr const char* DEFAULT_SERVICE_DOMAIN = "lock";
    static constexpr const char* SERVICE_NAME = "unlock";
    static constexpr const char* COMMOND = "v1.0_d_device_ha_control";
    static constexpr const char* AKCS_COMMAND = "v1.0_d_device_ha_control_unlock";

    // 业务参数
    std::string device_id_;
    std::string entity_id_;

    UnLockControl();
    UnLockControl(const std::string& device);
    UnLockControl(const std::string& entity, const std::string& device);

    std::string to_json();
    void from_json(const std::string& json_str);
};
#endif