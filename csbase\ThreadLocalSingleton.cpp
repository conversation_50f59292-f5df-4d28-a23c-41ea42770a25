#include "ThreadLocalSingleton.h"


/*
void ThreadLocalSingleton::SetKeyValue(const std::string& key, const std::string& val)
{    
    data_container_[key] = val;
}

std::string ThreadLocalSingleton::GetValueByKey(const std::string& key)
{
    std::string val;
    
    const auto& iter = data_container_.find(key);
    if(iter != data_container_.end())
    {
        val = iter->second;
    }   

    return val;
}
*/

void ThreadLocalSingleton::SetTraceID(uint64_t val)
{
    data_container_int_[traceid_] = val;
}

uint64_t ThreadLocalSingleton::GetTraceID()
{
    const auto& iter = data_container_int_.find(traceid_);
    if(iter != data_container_int_.end())
    {
        return iter->second;
    }
    return 0;
}

