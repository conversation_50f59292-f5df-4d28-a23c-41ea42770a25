﻿#ifndef _THREAD_LOCAL_H_
#define _THREAD_LOCAL_H_

#include <boost/noncopyable.hpp>
#include <string>
#include <map>

class ThreadLocalSingleton : private boost::noncopyable
{

public:
    
    ThreadLocalSingleton() {
        db_status_ = true;
    }
    //获取线程唯一的单例实例的静态方法
    static ThreadLocalSingleton& GetInstance() {
        //使用线程局部存储（C++11 thread_local）创建实例
        static thread_local ThreadLocalSingleton instance;
        return instance;
    }

    //void SetKeyValue(const std::string& key, const std::string& val);
    //std::string GetValueByKey(const std::string& key);
    void SetTraceID(uint64_t val);
    uint64_t GetTraceID();
    
    void SetDbStatus(bool status) {
        db_status_ = status;
    }
    bool GetDbStatus() {
        return db_status_;
    }

private:
    //线程独占的数据容器，因此存取无需加锁
    //std::map<std::string, std::string> data_container_;
    std::map<std::string, uint64_t> data_container_int_;

    const std::string traceid_ ="traceid";
    bool db_status_;
};

#endif

