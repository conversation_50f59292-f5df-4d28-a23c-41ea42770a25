#include "ThreadVariable.h"
#include <sys/syscall.h>
#include "AkLogging.h"

#define GetThreadID() syscall(SYS_gettid)


ThreadVariable& ThreadVariable::GetInstance()
{
	static ThreadVariable instance;
	return instance;
}


void ThreadVariable::SetKeyValue(const std::string& key, uint64_t val)
{    
    pid_t tid = GetThreadID();
    buffer1_[(int)tid][key] = val;
}

void ThreadVariable::GetValByKey(const std::string& key, uint64_t& val)
{
    pid_t tid = GetThreadID();   

    const auto& it = buffer1_.find((int)tid);
    if(it != buffer1_.end())
    {
        std::map<std::string, uint64_t> key_value_map = it->second;
        const auto& iter = key_value_map.find(key);
        if(iter != key_value_map.end())
        {
            val = iter->second;
        }
    }
    
}


