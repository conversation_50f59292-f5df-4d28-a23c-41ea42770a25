﻿#ifndef _THREAD_VARIABLE_H_
#define _THREAD_VARIABLE_H_

#include <boost/noncopyable.hpp>
#include <string>
#include <map>

//可根据不同的数据类型定义不同的map
typedef std::map<int, std::map<std::string, uint64_t>> BufferMap1;

//维护同一个线程中的共享变量
class ThreadVariable : private boost::noncopyable
{

public:
    
    static ThreadVariable& GetInstance();
    
    void SetKeyValue(const std::string& key, uint64_t val);
    
    void GetValByKey(const std::string& key, uint64_t& val);

private:

    BufferMap1 buffer1_;

};

#endif

