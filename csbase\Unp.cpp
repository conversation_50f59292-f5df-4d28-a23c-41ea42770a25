#include    "Unp.h"
#include    "AkLogging.h"

int Accept(int fd, struct sockaddr* sa, socklen_t* salenptr)
{
    int n = -1;
again:
    if ((n = accept(fd, sa, salenptr)) < 0)
    {
#ifdef  EPROTO
        if (errno == EPROTO || errno == ECONNABORTED)
#else
        if (errno == ECONNABORTED)
#endif
            goto again;
        else
        {
            AK_LOG_WARN << "accept error";
        }
    }
    return (n);
}

char* sock_ntop(const struct sockaddr* sa, socklen_t salen)
{
    (void)salen; //warning: unused parameter
    char        portstr[8];
    static char str[128];       /* Unix domain is largest */

    switch (sa->sa_family)
    {
        case AF_INET:
        {
            struct sockaddr_in*  sin = (struct sockaddr_in*) sa;

            if (inet_ntop(AF_INET, &sin->sin_addr, str, sizeof(str)) == NULL)
            {
                return (NULL);
            }
            if (ntohs(sin->sin_port) != 0)
            {
                snprintf(portstr, sizeof(portstr), ":%d", ntohs(sin->sin_port));
                strcat(str, portstr);
            }
            return (str);
        }
        case AF_INET6:
        {
            struct sockaddr_in6* sin6 = (struct sockaddr_in6*) sa;

            str[0] = '[';
            if (inet_ntop(AF_INET6, &sin6->sin6_addr, str + 1, sizeof(str) - 1) == NULL)
            {
                return (NULL);
            }
            if (ntohs(sin6->sin6_port) != 0)
            {
                snprintf(portstr, sizeof(portstr), "]:%d", ntohs(sin6->sin6_port));
                strcat(str, portstr);
                return (str);
            }
            return (str + 1);
        }
        default:
            return (NULL);
    }
}


char* sock_ntop_ip(const struct sockaddr* sa, socklen_t salen)
{
    (void)salen; //warning: unused parameter
    static char str[128];       /* Unix domain is largest */

    switch (sa->sa_family)
    {
        case AF_INET:
        {
            struct sockaddr_in*  sin = (struct sockaddr_in*) sa;

            if (inet_ntop(AF_INET, &sin->sin_addr, str, sizeof(str)) == NULL)
            {
                return (NULL);
            }
            return (str);
        }
        case AF_INET6:
        {
            struct sockaddr_in6* sin6 = (struct sockaddr_in6*) sa;

            str[0] = '[';
            if (inet_ntop(AF_INET6, &sin6->sin6_addr, str + 1, sizeof(str) - 1) == NULL)
            {
                return (NULL);
            }
            strcat(str, "]");
            return (str);
        }
		default:
            return (NULL);
    }
}
char* Sock_ntop(const struct sockaddr* sa, socklen_t salen)
{
    char*    ptr;

    if ((ptr = sock_ntop(sa, salen)) == NULL)
    {
        AK_LOG_WARN << "sock_ntop error";    /* inet_ntop() sets errno */
    }
    return (ptr);
}

char* Sock_ntop_ip(const struct sockaddr* sa, socklen_t salen)
{
    char* ptr;

    if ((ptr = sock_ntop_ip(sa, salen)) == NULL)
    {
        AK_LOG_WARN << "sock_ntop error";    /* inet_ntop() sets errno */
    }
    return (ptr);
}

short sock_get_port(const struct sockaddr* sa)
{
    switch (sa->sa_family)
    {
        case AF_INET:
        {
            struct sockaddr_in*  sin = (struct sockaddr_in*) sa;

            return (sin->sin_port);
        }

        case AF_INET6:
        {
            struct sockaddr_in6* sin6 = (struct sockaddr_in6*) sa;

            return (sin6->sin6_port);
        }
    }
    return (-1);    /* ??? */
}

char* sock_inet_ntop(const struct sockaddr* sa)
{
    static char str_sock_inet_ntop[128];        /* Unix domain is largest */
    switch (sa->sa_family)
    {
        case AF_INET:
        {
            struct sockaddr_in*  sin = (struct sockaddr_in*) sa;

            if (inet_ntop(AF_INET, &sin->sin_addr, str_sock_inet_ntop, sizeof(str_sock_inet_ntop)) == NULL)
            {
                AK_LOG_WARN << "sock_inet_ntop error";
                return (NULL);
            }
            return (str_sock_inet_ntop);
        }
        /* end sock_ntop */
        case AF_INET6:
        {
            struct sockaddr_in6* sin6 = (struct sockaddr_in6*) sa;

            if (inet_ntop(AF_INET6, &sin6->sin6_addr, str_sock_inet_ntop, sizeof(str_sock_inet_ntop)) == NULL)
            {
                AK_LOG_WARN << "sock_inet_ntop error";
                return (NULL);
            }
            return (str_sock_inet_ntop);
        }
    }
    return (NULL);
}

void Sendto(int fd, const void* ptr, size_t nbytes, int flags,
            const struct sockaddr* sa, socklen_t salen)
{
    if (sendto(fd, ptr, nbytes, flags, sa, salen) != (ssize_t)nbytes)
    {
        AK_LOG_ERROR << "sendto error, fd =" << fd << ", error = " << strerror(errno);
    }
}