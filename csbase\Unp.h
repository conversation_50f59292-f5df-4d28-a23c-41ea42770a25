#ifndef __AKCS_BASE_UNP_H__
#define __AKCS_BASE_UNP_H__

#include	<sys/types.h>	/* basic system data types */
#include	<sys/socket.h>	/* basic socket definitions */
#if TIME_WITH_SYS_TIME
#include	<sys/time.h>	/* timeval{} for select() */
#include	<time.h>		/* timespec{} for pselect() */
#else
#if HAVE_SYS_TIME_H
#include	<sys/time.h>	/* includes <time.h> unsafely */
#else
#include	<time.h>		/* old system? */
#endif
#endif
#include	<netinet/in.h>	/* sockaddr_in{} and other Internet defns */
#include	<arpa/inet.h>	/* inet(3) functions */
#include	<errno.h>
#include	<fcntl.h>		/* for nonblocking */
#include	<netdb.h>
#include	<signal.h>
#include	<stdio.h>
#include	<stdlib.h>
#include	<string.h>
#include	<sys/stat.h>	/* for S_xxx file mode constants */
#include	<sys/uio.h>		/* for iovec{} and readv/writev */
#include	<unistd.h>
#include	<sys/wait.h>
#include	<sys/un.h>		/* for Unix domain sockets */
#ifdef	HAVE_SYS_SELECT_H
# include	<sys/select.h>	/* for convenience */
#endif

#ifdef	HAVE_SYS_SYSCTL_H
#ifdef	HAVE_SYS_PARAM_H
# include	<sys/param.h>	/* OpenBSD prereq for sysctl.h */
#endif
# include	<sys/sysctl.h>
#endif

#ifdef	HAVE_POLL_H
# include	<poll.h>		/* for convenience */
#endif

#ifdef	HAVE_SYS_EVENT_H
# include	<sys/event.h>	/* for kqueue */
#endif

#ifdef	HAVE_STRINGS_H
# include	<strings.h>		/* for convenience */
#endif

#define	SA	struct sockaddr

#ifndef HAVE_STRUCT_SOCKADDR_STORAGE
/*
 * RFC 3493: protocol-independent placeholder for socket addresses
 */
#define	__SS_MAXSIZE	128
#define	__SS_ALIGNSIZE	(sizeof(int64_t))
#ifdef HAVE_SOCKADDR_SA_LEN
#define	__SS_PAD1SIZE	(__SS_ALIGNSIZE - sizeof(u_char) - sizeof(sa_family_t))
#else
#define	__SS_PAD1SIZE	(__SS_ALIGNSIZE - sizeof(sa_family_t))
#endif
#define	__SS_PAD2SIZE	(__SS_MAXSIZE - 2*__SS_ALIGNSIZE)
#if 0
struct sockaddr_storage {
#ifdef HAVE_SOCKADDR_SA_LEN
	u_char		ss_len;
#endif
	sa_family_t	ss_family;
	char		__ss_pad1[__SS_PAD1SIZE];
	int64_t		__ss_align;
	char		__ss_pad2[__SS_PAD2SIZE];
};
#endif
#endif

int Accept(int, SA *, socklen_t *);

char *Sock_ntop(const SA *, socklen_t);

char *sock_inet_ntop(const struct sockaddr *sa);

void Sendto(int fd, const void *ptr, size_t nbytes, int flags,
	        const struct sockaddr *sa, socklen_t salen);
            
short sock_get_port(const struct sockaddr *sa);
char *Sock_ntop_ip(const struct sockaddr *sa, socklen_t salen);
#endif//__AKCS_BASE_UNP_H__