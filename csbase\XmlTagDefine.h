﻿#ifndef _XML_TAG_DEFINE_H_
#define _XML_TAG_DEFINE_H_
#include <string>

namespace csmain
{
namespace xmltag
{
const std::string TYPE = "_Type"; //区分type和msg_type
const std::string PROTOCAL = "_PROTOCAL"; //区分protocal和protocal_value
const std::string MAC = "Mac";
const std::string URL = "Url";
const std::string PIC_MD5 = "PicMd5";
const std::string NAME = "Name";
const std::string DOOR_NUM = "DoorNum";
const std::string WEEK = "Week";
const std::string TIME_START = "TimeStart";
const std::string TIME_END = "TimeEnd";
const std::string ID = "ID";
const std::string HTTP_API = "HttpApi";
const std::string RELAY = "Relay";
const std::string RELAY_STATUS = "RelayStatus";
const std::string ACCOUNT = "Account";
const std::string REG_URL = "RegUrl";
const std::string STATUS = "Status";
const std::string IS_KIT = "IsKit";
const std::string ACCOUNT_NAME = "AccountName";
const std::string EMAIL = "Email";
const std::string MOBILE_NUMBER = "MobileNumber";
const std::string SERVICE = "Service";
const std::string MSG_SEQ = "MsgSeq";
const std::string RESULT = "Result";
const std::string MSG_ID = "MsgID";
const std::string MSG_TYPE = "Type";
const std::string MSG_UUID = "MsgUUID";
const std::string SECURITY_REALY = "SecurityRelay";
const std::string INITIATOR = "Initiator";
const std::string TRACE_ID = "TraceID";
const std::string INFO = "Info";
const std::string MSG_TYPE2 = "MsgType";
const std::string UUID = "UUID";
const std::string VOICE_UNREAD = "VoiceUnread";
const std::string URL2 = "URL";
const std::string CITY = "City";
const std::string WEATHER = "Weather";
const std::string HUMIDITY = "Humidity";
const std::string TEMPERATURE = "Temperature";
const std::string CHECK_RES = "CheckRes";
const std::string PER_ID = "PerID";
const std::string RELAYS = "Relays";
const std::string ACCESS_MODE = "AccessMode";
const std::string PERSONNEL_ID = "PersonnelID";
const std::string VISITOR_UUID = "VisitorUUID";
const std::string VISITOR_NAME = "VisitorName";
const std::string UNIT_APT = "UnitApt";
const std::string LIFT_FLOOR_NUM = "LiftFloorNum";
const std::string RELAY_TYPE = "Type";
const std::string CONTROL_TYPE = "ControlType";
const std::string APT = "APT";
const std::string SITE = "Site";
const std::string TIME = "Time";
const std::string TITLE = "Title";
const std::string ADDRESS = "Address";
const std::string DEV_TYPE = "DevType";
const std::string DEV_NAME = "DevName";
const std::string EXTENSION = "Extension";
const std::string FROM_NAME = "FromName";
const std::string ALARM_MSG = "AlarmMsg";
const std::string ALARM_CODE = "AlarmCode";
const std::string ALARM_ZONE = "AlarmZone";
const std::string ALARM_LOCATION = "AlarmLocation";
const std::string ALARM_CUSTOMIZE = "AlarmCustomize";
const std::string MAC_CAPITAL = "MAC";
const std::string CAPTURE_ID = "CaptureID";
const std::string SIP = "Sip";
const std::string LOCATION = "Location";
const std::string COMPANY_UUID = "CompanyUUID";
const std::string MSG_FROM = "From";
const std::string MSG_TO = "To";
const std::string MSG_CONTENT = "Content";
const std::string MSG_TIME = "Time";
const std::string MSG_RESP = "Resp";
const std::string USER = "User";
const std::string MODE = "Mode";
const std::string TITEL2 = "Titel";
const std::string TOKEN = "Token";
const std::string DETECTION_TYPE = "DetectionType";
const std::string DETECTION_INFO = "DetectionInfo";
const std::string LOCK_UUID = "LockUUID";
const std::string EVENT_TYPE = "EventType";
const std::string MESSAGE_ACCOUNT_LIST_ID = "MessageAccountListID";
const std::string ACTION = "Action";
const std::string SYNC = "Sync";
const std::string HOME_SYNC = "HomeSync";
}
}

#endif

