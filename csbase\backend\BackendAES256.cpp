//#include "stdafx.h"
#include <string>
#include <string.h>
#include "BackendAES256.h"
#include "aes.h"
#include <arpa/inet.h>
#include "AkcsCommonSt.h"
#include "AkLogging.h"
#include "util.h"


std::string generateRandomString(int length) {
    const std::string characters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    const int charactersLength = characters.length();
    srand(time(0));
    std::string result = "";
    for (int i = 0; i < length; i++) {
        result += characters[rand() % charactersLength];
    }
    return result;
}


int AES_256_DECRYPT_Dynamics_IV(unsigned char* in, unsigned char* out, unsigned char* key, int nSize)
{
    if (in == NULL || key == NULL || out == NULL)
    {
        return 0;
    }

    int dynamics = 0;
    unsigned char iv[17] = {0};
    if(strncmp((char *)in, DYNAMICS_VI_FLAGS, 8) == 0)
    {
        memcpy(iv, in + 8, 16);
        dynamics = 1;
    }
    else
    {
        memset(iv, 0, sizeof(iv));
    }
    AES_KEY aes_key;
    char szkey[KEY_LENGTH + 1] = {0};
    memset(&aes_key, 0, sizeof(AES_KEY));
    genKey((char*)key, szkey, sizeof(szkey));   //保证秘钥不超过32字节(即aes 的最高级别 256 bit)，若超过则截断
    
    AES_set_decrypt_key((const unsigned char*)szkey, 256, &aes_key);
    if (dynamics)
    {
        nSize -= DYNAMICS_VI_HEAD_LEN;
        AES_cbc_encrypt(in + DYNAMICS_VI_HEAD_LEN, out, nSize, &aes_key, iv, AES_DECRYPT);
    }
    else 
    {
        AES_cbc_encrypt(in, out, nSize, &aes_key, iv, AES_DECRYPT);
    }
    return dynamics;
    //CBC模式对于每个待加密的密码块在加密前会先与前一个密码块的密文异或然后再用加密器加密。第一个明文块与一个叫初始化向量的数据块异或
    // AES_cbc_encrypt允许length不是16(128位)的整数倍，不足的部分会用0填充，输出总是16的整数倍。
}

int AES_256_ENCRYPT_With_IV(unsigned char* in, unsigned char* out, unsigned char* key, int nSize, unsigned char* iv)
{
    if (in == NULL || key == NULL || out == NULL)
    {
        return 1;
    }
    AES_KEY aes_key;
    char szkey[KEY_LENGTH + 1] = {0};    
    memset(&aes_key, 0, sizeof(AES_KEY));
    genKey((char*)key, szkey, sizeof(key));
    AES_set_encrypt_key((const unsigned char*)key, 256, &aes_key);
    AES_cbc_encrypt(in, out, nSize, &aes_key, iv, AES_ENCRYPT);
    return 0;
}

int AesEncryptByDefaultMac(const char* pIn, char* pOut, int* pDataSize, const uint32_t max_allowed_size)
{
    unsigned char iv[17] = {0};
    memset(iv, 0, sizeof(iv));

    if (pIn == NULL || pOut == NULL || pDataSize == NULL)
    {
        return -1;
    }
    *pDataSize = strlen(pIn);
    *pDataSize = ((*pDataSize - 1) / 16 + 1) * 16; //AES加密会补齐16字节，所以需要补齐
    if (max_allowed_size < (uint32_t)(*pDataSize + 1))
    {
        return -1;
    }    
    char* pszOutBuf = new char[*pDataSize + 1];
    memset(pszOutBuf, 0, *pDataSize + 1);
    
    //生成key
    std::string strAesKey = AES_KEY_DEFAULT_MAC;
    strAesKey += AES_KEY_DEFAULT_MASK;
    char szAesKeyTmp[KEY_LENGTH + 1] = {0};
    char* pszAesKey = NULL;
    Snprintf(szAesKeyTmp, sizeof(szAesKeyTmp),  strAesKey.c_str());
    pszAesKey = strupr(szAesKeyTmp);

    AES_256_ENCRYPT_With_IV((unsigned char*)pIn, (unsigned char*)pszOutBuf, (unsigned char*)pszAesKey, *pDataSize, iv);
    memcpy(pOut, pszOutBuf, *pDataSize + 1);
    
    delete []pszOutBuf;
    return 0;
}

//先用这个new代表新版本，等后面全部弄完把旧的代码都已调
int AesEncryptByMacNew(const char* pIn, char* pOut, const std::string& strMac, int* pDataSize, int dynamics, const uint32_t max_allowed_size)
{
    unsigned char iv[17] = {0};
    unsigned char ivtmp[17] = {0};

    if(!dynamics)
    {
        memset(iv, 0, sizeof(iv));
    }
    else
    {
        std::string s = generateRandomString(16);
        snprintf((char*)iv, sizeof(iv), "%s", s.c_str());
        snprintf((char*)ivtmp, sizeof(ivtmp), "%s", s.c_str());
    }

    if (pIn == NULL || pOut == NULL || pDataSize == NULL)
    {
        return -1;
    }
    *pDataSize = strlen(pIn);
    *pDataSize = ((*pDataSize - 1) / 16 + 1) * 16; //AES加密会补齐16字节，所以需要补齐

    if (max_allowed_size < (uint32_t)(*pDataSize + 1 + DYNAMICS_VI_HEAD_LEN))
    {
        return -1;
    }
    
    char* pszOutBuf = new char[*pDataSize + 1 + DYNAMICS_VI_HEAD_LEN];
    memset(pszOutBuf, 0, *pDataSize + 1 + DYNAMICS_VI_HEAD_LEN);
    
    //生成key
    std::string strAesKey = strMac;
    strAesKey += AES_KEY_DEFAULT_MASK;
    char szAesKeyTmp[KEY_LENGTH + 1] = {0};
    char* pszAesKey = NULL;
    ::strncpy(szAesKeyTmp, strAesKey.c_str(), sizeof(szAesKeyTmp) - 1);
    pszAesKey = strupr(szAesKeyTmp);

    AES_256_ENCRYPT_With_IV((unsigned char*)pIn, (unsigned char*)pszOutBuf, (unsigned char*)pszAesKey, *pDataSize, iv);
    if (dynamics)
    {
        memcpy(pOut, DYNAMICS_VI_FLAGS, 8);
        memcpy(pOut + 8, ivtmp, 16);
        memcpy(pOut + DYNAMICS_VI_HEAD_LEN, pszOutBuf, *pDataSize + 1);
        *pDataSize += DYNAMICS_VI_HEAD_LEN;
    }
    else 
    {
        memcpy(pOut, pszOutBuf, *pDataSize + 1);
    }
    
    delete []pszOutBuf;
    return 0;
}

int AesEncryptByDefault(char* pIn, char* pOut, int* pDataSize, const uint32_t max_allowed_size)
{
    if (pIn == NULL || pOut == NULL || pDataSize == NULL)
    {
        return -1;
    }
    *pDataSize = strlen(pIn);
    *pDataSize = ((*pDataSize - 1) / 16 + 1) * 16; //AES加密会补齐16字节，所以需要补齐
    if (max_allowed_size < (uint32_t)(*pDataSize + 1))
    {
        return -1;
    }    
    char* pszOutBuf = new char[*pDataSize + 1];
    memset(pszOutBuf, 0, *pDataSize + 1);
    AES_256_ENCRYPT((unsigned char*)pIn, (unsigned char*)pszOutBuf, (unsigned char*)AES_ENCRYPT_KEY_V1, *pDataSize);
    memcpy(pOut, pszOutBuf, *pDataSize + 1);
    delete []pszOutBuf;
    return 0;
}


//TODO:用于解密发送给设备得信令内容，用AesDecryptByMac会段错误，先简单处理
int AesDecryptByMac2(char* pIn, char* pOut, const std::string& strMac, int nDataSize)
{
	if(pIn == NULL || pOut == NULL)
	{
		return -1;
	}
	//对MSG进行AES加密
	char *pszOutBuf = new char[nDataSize + 1];
	memset(pszOutBuf, 0, nDataSize + 1);

	//生成key
	std::string strAesKey = strMac;
	strAesKey += AES_KEY_DEFAULT_MASK;
	char szAesKeyTmp[KEY_LENGTH+1]={0};
	char *pszAesKey = NULL;
    Snprintf(szAesKeyTmp, sizeof(szAesKeyTmp),  strAesKey.c_str());
	pszAesKey = strupr(szAesKeyTmp);

	AES_256_DECRYPT((unsigned char*)pIn, (unsigned char*)pszOutBuf, (unsigned char*)pszAesKey, nDataSize);
    /*if (strlen(pszOutBuf) >= nDataSize)//这种情况下一定有问题了,暂时通过丢弃这条消息
    {
        memcpy(pOut, pszOutBuf, nDataSize - 1);
        pOut[nDataSize] = '\0';
    }
    else
    {
        memcpy(pOut, pszOutBuf, strlen(pszOutBuf) + 1);
    }*/
    memcpy(pOut, pszOutBuf, strlen(pszOutBuf) + 1);
	delete []pszOutBuf;
	return 0;
}

// 用于解密设备发上来的Dclient消息
int AesDecryptByMac(char* pIn, char* pOut, const std::string& strMac, int nDataSize)
{
	if(pIn == NULL || pOut == NULL)
	{
		return -1;
	}
	//对MSG进行AES加密
	char *pszOutBuf = new char[nDataSize + 1];
	memset(pszOutBuf, 0, nDataSize + 1);

	//生成key
	std::string strAesKey = strMac;
	strAesKey += AES_KEY_DEFAULT_MASK;
	char szAesKeyTmp[KEY_LENGTH+1]={0};
	char *pszAesKey = NULL;
    Snprintf(szAesKeyTmp, sizeof(szAesKeyTmp),  strAesKey.c_str());
	pszAesKey = strupr(szAesKeyTmp);

	//AES_256_DECRYPT((unsigned char*)pIn, (unsigned char*)pszOutBuf, (unsigned char*)pszAesKey, nDataSize);
    int dy_iv = AES_256_DECRYPT_Dynamics_IV((unsigned char*)pIn, (unsigned char*)pszOutBuf, (unsigned char*)pszAesKey, nDataSize);
    if (dy_iv)
    {
        nDataSize -= DYNAMICS_VI_HEAD_LEN;
    }    
    if (strlen(pszOutBuf) >= (std::size_t)nDataSize)//这种情况下一定有问题了,暂时通过丢弃这条消息
    {
        memcpy(pOut, pszOutBuf, nDataSize - 1);
        pOut[nDataSize] = '\0';
    }
    else
    {
        memcpy(pOut, pszOutBuf, strlen(pszOutBuf) + 1);
    }
	delete []pszOutBuf;
	return 0;
}

int AesDecryptByDefault(char* pIn, char* pOut, int nDataSize)
{
	if(pIn == NULL || pOut == NULL || nDataSize == 0)
	{
		return -1;
	}
	char *pszOutBuf = new char[nDataSize + 1];
	memset(pszOutBuf, 0, nDataSize + 1);
	//AES_256_DECRYPT((unsigned char*)pIn, (unsigned char*)pszOutBuf, (unsigned char*)AES_ENCRYPT_KEY_V1, nDataSize);
    int dy_iv = AES_256_DECRYPT_Dynamics_IV((unsigned char*)pIn, (unsigned char*)pszOutBuf, (unsigned char*)AES_ENCRYPT_KEY_V1, nDataSize);
    if (dy_iv)
    {
        nDataSize -= DYNAMICS_VI_HEAD_LEN;
    }
    if (strlen(pszOutBuf) >= (std::size_t)nDataSize)
    {
        memcpy(pOut, pszOutBuf, nDataSize - 1);
        pOut[nDataSize] = '\0';
    }
    else
    {
        memcpy(pOut, pszOutBuf, strlen(pszOutBuf) + 1);
    }
	delete []pszOutBuf;
	return dy_iv;
}

//在上报状态时候确认是否需要用动态iv
int AesDecryptByDefaultForReportStatus(char* pIn, char* pOut, int nDataSize, int &dy_iv)
{
	if(pIn == NULL || pOut == NULL || nDataSize == 0)
	{
		return -1;
	}
	char *pszOutBuf = new char[nDataSize + 1];
	memset(pszOutBuf, 0, nDataSize + 1);
	dy_iv = AES_256_DECRYPT_Dynamics_IV((unsigned char*)pIn, (unsigned char*)pszOutBuf, (unsigned char*)AES_ENCRYPT_KEY_V1, nDataSize);
    if (dy_iv)
    {
        nDataSize -= DYNAMICS_VI_HEAD_LEN;
    }

    if (strlen(pszOutBuf) >= (std::size_t)nDataSize)
    {
        memcpy(pOut, pszOutBuf, nDataSize - 1);
        pOut[nDataSize] = '\0';
    }
    else
    {
        memcpy(pOut, pszOutBuf, strlen(pszOutBuf) + 1);
    }
	delete []pszOutBuf;
	return 0;
}

int AesEncryptDynamicsIV(const char* pIn, char* pOut, int* pDataSize, const std::string &key, const uint32_t max_allowed_size)
{
    unsigned char iv[17] = {0};
    unsigned char ivtmp[17] = {0};
    
    std::string s = generateRandomString(16);
    snprintf((char*)iv, sizeof(iv), "%s", s.c_str());
    snprintf((char*)ivtmp, sizeof(ivtmp), "%s", s.c_str());
    if (pIn == NULL || pOut == NULL || pDataSize == NULL)
    {
        return -1;
    }
    *pDataSize = strlen(pIn);
    *pDataSize = ((*pDataSize - 1) / 16 + 1) * 16; //AES加密会补齐16字节，所以需要补齐
    if (max_allowed_size < (uint32_t)(*pDataSize + 1 + DYNAMICS_VI_HEAD_LEN))
    {
        return -1;
    }

    
    char* pszOutBuf = new char[*pDataSize + 1 + DYNAMICS_VI_HEAD_LEN];
    memset(pszOutBuf, 0, *pDataSize + 1 + DYNAMICS_VI_HEAD_LEN);
    
    //生成key
    char szAesKeyTmp[KEY_LENGTH + 1] = {0};
    Snprintf(szAesKeyTmp, sizeof(szAesKeyTmp),  key.c_str());

    AES_256_ENCRYPT_With_IV((unsigned char*)pIn, (unsigned char*)pszOutBuf, (unsigned char*)szAesKeyTmp, *pDataSize, iv);
    memcpy(pOut, DYNAMICS_VI_FLAGS, 8);
    memcpy(pOut + 8, ivtmp, 16);
    memcpy(pOut + DYNAMICS_VI_HEAD_LEN, pszOutBuf, *pDataSize + 1);
    *pDataSize += DYNAMICS_VI_HEAD_LEN;
    
    delete []pszOutBuf;
    return 0;
}

int AesEncryptByDefaultForDynamicsIV(char* pIn, char* pOut, int* pDataSize, const uint32_t max_allowed_size)
{
    AesEncryptDynamicsIV(pIn, pOut, pDataSize, AES_ENCRYPT_KEY_V1, max_allowed_size);
    return 0;
}

int AesEncryptByDefaultMacDynamicsIV(const char* pIn, char* pOut, int* pDataSize, const uint32_t max_allowed_size)
{
	//生成key
	std::string strAesKey = AES_KEY_DEFAULT_MAC;
	strAesKey += AES_KEY_DEFAULT_MASK;
	char szAesKeyTmp[KEY_LENGTH+1]={0};
    Snprintf(szAesKeyTmp, sizeof(szAesKeyTmp),  strAesKey.c_str());
    strupr(szAesKeyTmp);

    AesEncryptDynamicsIV(pIn, pOut, pDataSize, szAesKeyTmp, max_allowed_size);  
    return 0;
}

int AesEncryptByDefaultNew(char* pIn, char* pOut, int* pDataSize, int dynamics, const uint32_t max_allowed_size)
{
    if (dynamics)
    {
        AesEncryptDynamicsIV(pIn, pOut, pDataSize, AES_ENCRYPT_KEY_V1, max_allowed_size);
    }
    else
    {
        AesEncryptByDefault(pIn, pOut, pDataSize, max_allowed_size);
    }
    return 0;
}

int AesEncryptByDefaultMacNew(char* pIn, char* pOut, int* pDataSize, int dynamics, const uint32_t max_allowed_size)
{
    if (dynamics)
    {
        AesEncryptByDefaultMacDynamicsIV(pIn, pOut, pDataSize, max_allowed_size);
    }
    else
    {
        AesEncryptByDefaultMac(pIn, pOut, pDataSize, max_allowed_size);
    }
    return 0;
}

int AesDyIvEncryptByMac(const char* pIn, char* pOut, const std::string& strMac, int* pDataSize)
{
    unsigned char iv[17] = {0};
    unsigned char ivtmp[17] = {0};

    std::string s = generateRandomString(16);
    snprintf((char*)iv, sizeof(iv), "%s", s.c_str());
    snprintf((char*)ivtmp, sizeof(ivtmp), "%s", s.c_str());

    if (pIn == NULL || pOut == NULL || pDataSize == NULL)
    {
        return -1;
    }
    *pDataSize = strlen(pIn);
    *pDataSize = ((*pDataSize - 1) / 16 + 1) * 16; //AES加密会补齐16字节，所以需要补齐
    char* pszOutBuf = new char[*pDataSize + 1 + DYNAMICS_VI_HEAD_LEN];
    memset(pszOutBuf, 0, *pDataSize + 1 + DYNAMICS_VI_HEAD_LEN);
    
    //生成key
    std::string strAesKey = strMac;
    strAesKey += AES_KEY_DEFAULT_MASK;
    char szAesKeyTmp[KEY_LENGTH + 1] = {0};
    char* pszAesKey = NULL;
    ::strncpy(szAesKeyTmp, strAesKey.c_str(), sizeof(szAesKeyTmp) - 1);
    pszAesKey = strupr(szAesKeyTmp);

    AES_256_ENCRYPT_With_IV((unsigned char*)pIn, (unsigned char*)pszOutBuf, (unsigned char*)pszAesKey, *pDataSize, iv);
    memcpy(pOut, DYNAMICS_VI_FLAGS, 8);
    memcpy(pOut + 8, ivtmp, 16);
    memcpy(pOut + DYNAMICS_VI_HEAD_LEN, pszOutBuf, *pDataSize + 1);
    *pDataSize += DYNAMICS_VI_HEAD_LEN;
    
    delete []pszOutBuf;
    return 0;
}

