#ifndef __BACKEND_AES_INCLUDED__
#define __BACKEND_AES_INCLUDED__

#include "AES256.h"
#define HTONS   htons
#define NTOHS   ntohs
#define HTONL   htonl
#define NTOHL   ntohl

#define DYNAMICS_VI_FLAGS  "iakv6600"
#define DYNAMICS_VI_HEAD_LEN  24


int AesEncryptByDefault(char* pIn, char* pOut, int* pDataSize, const uint32_t max_allowed_size);
int AesDecryptByMac(char* pIn, char* pOut, const std::string& strMac, int nDataSize);
int AesDecryptByDefault(char* pIn, char* pOut, int nDataSize);
int AesDecryptByDefaultForReportStatus(char* pIn, char* pOut, int nDataSize, int &dy_iv);

//TODO:用于解密发送给设备得信令内容，用AesDecryptByMac会段错误，先简单处理
int AesDecryptByMac2(char* pIn, char* pOut, const std::string& strMac, int nDataSize);

int AES_256_ENCRYPT_With_IV(unsigned char* in, unsigned char* out, unsigned char* key, int nSize, unsigned char* iv);

int AesEncryptDynamicsIV(const char* pIn, char* pOut, int* pDataSize, const std::string &key, const uint32_t max_allowed_size);
int AesEncryptByDefaultMac(const char* pIn, char* pOut, int* pDataSize, const uint32_t max_allowed_size);
int AesEncryptByDefaultMacDynamicsIV(const char* pIn, char* pOut, int* pDataSize, const uint32_t max_allowed_size);
int AesEncryptByDefaultForDynamicsIV(char* pIn, char* pOut, int* pDataSize, const uint32_t max_allowed_size);


int AesEncryptByMacNew(const char* pIn, char* pOut, const std::string& strMac, int* pDataSize, int dynamics, const uint32_t max_allowed_size);
int AesEncryptByDefaultMacNew(char* pIn, char* pOut, int* pDataSize, int dynamics, const uint32_t max_allowed_size);
int AesEncryptByDefaultNew(char* pIn, char* pOut, int* pDataSize, int dynamics, const uint32_t max_allowed_size);

int AesDyIvEncryptByMac(const char* pIn, char* pOut, const std::string& strMac, int* pDataSize);

std::string generateRandomString(int length);

#endif
