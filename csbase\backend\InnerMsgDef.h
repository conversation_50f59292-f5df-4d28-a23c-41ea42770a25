#ifndef __MSG_INNER_MSG_DEF_H__
#define __MSG_INNER_MSG_DEF_H__

#include <memory>
#include "BasicDefine.h"


#define MSG_SEQ_SIZE                    16    //消息序列号
#define COMMUNITY_SIZE                  32    //社区编码长度
#define AREA_NODE_COMMA_COUNT           4     //地址节点<,>分割符最大个数
#define AREA_NODE_SIZE                  32    //地址节点长度
#define TMP_KEY_SIZE                    24    //临时秘钥长度
#define IMEI_SIZE                       16    //手机串号长度

#define RELAY_DEFAULT                   15    //relay默认值1234
#define RELAY_NUM                       4     //relay数量最多4个


namespace csmain
{
enum AppType
{
    APP_IOS = 0,
    APP_ANDROID_HUAWEI,
    APP_ANDROID_XIAOMI,
    APP_ANDROID_OTHERS,
    APP_ANDROID_FCM,
    APP_ANDROID_OPPO,
    APP_ANDROID_VIVO,
    APP_ANDROID_FLYME,
    APP_ANDROID_JPUSH,
};

enum CaptureType //设备端截图的时间类型
{
    kMotion = 0,
    kLogs = 1,
};

enum MotionRecvType //app端是否接受motion alert的设置项
{
    kNoRecv = 0,
    kRecv = 1,
    kNone = 2, //初始化状态，在此状态下,需要查询redis中缓存的状态
};

enum ArmingType //app端是否接受motion alert的设置项
{
    kDisarm = 0,
    kIndoor,
    kSleeping,
    kOutdoor,
};
enum ActType //室内机记录的各种动作(开门)记录
{
    kCall = 0,
    kInputPwd,
    kCard,
};

enum CaptureLogRetType //设备（室外机）上报(开门)消息的动作流
{
    kSuccess = 0,
    kFail,
};

}

typedef struct DEVICE_SIP_T
{
    char name[USER_SIZE];
    char sip_account[SIP_SIZE];
    char ip[IP_SIZE];
    char mac[MAC_SIZE];
    char rtsp_password[RTSP_PWD_SIZE];
    char room_num[16];
    char uuid[64];
    int  type;
} DEVICE_SIP;

//个人终端用户设备列表结构体
typedef struct PERSONNAL_DEVICE_SIP_T
{
    char name[USER_SIZE];
    char sip_account[SIP_SIZE];
    char ip[IP_SIZE];
    char mac[MAC_SIZE];
    char rtsp_password[RTSP_PWD_SIZE];
    char uuid[64];
    int  type;
} PERSONNAL_DEVICE_SIP;

//社区终端用户设备列表结构体
typedef struct COMMUNITY_DEVICE_SIP_T
{
    char uuid[64];
    int  type;
} COMMUNITY_DEVICE_SIP;

//added by chenyc,2017-08-25,个人终端用户功能开发
//个人终端用户app信息结构体
typedef struct SOCKET_MSG_PERSONNAL_APP_CONF_T
{
    char protocal[PROTOCAL_SIZE];
    char node[NODE_SIZE];  //指个人用户联动系统单元
    char user[USER_EMAIL_SIZE];  //解析xml完之后,可以是uid或者email
    char password[MD5_SIZE];
    char token[TOKEN_SIZE];//端外推送的token
    char fcm_token[TOKEN_SIZE];
    char voip_token[TOKEN_SIZE];
    char app_token[TOKEN_SIZE]; // 网关登陆验证的token
    char sip[SIP_SIZE];  //2017-11-17,支持pbx对接,索引sip->uid时新增
    char username[USER_EMAIL_SIZE];
    int version;//app上传的版本号，用于兼容处理 通用的版本号ios/android一样
    int  mobile_type;   //参见 csmain::AppType
    char msg_seq[MSG_SEQ_SIZE];
    uint32_t role;//add by chenzhx ********
    int  lastread_message_id;
    int  is_expire;
    char app_version[32]; //app版本号，是各个上架时候的版本
    int  id_active;
    uint32_t manager_account_id;//add by chenzhx ********
    uint32_t unit_id;
    char language[32]; //app语言
    char oem_name[USER_SIZE];  //OEM
    char email[65];
    char mobile_number[25];
    char uuid[64];
    int is_office;//add by chenzhx ********
    int dynamics_iv;//aes 动态iv
    char user_info_uuid[64];
} SOCKET_MSG_PERSONNAL_APP_CONF;

typedef struct SOCKET_MSG_ALARM_DEAL_INFO_T
{
#define ALARM_ID_SIZE            16
#define ALARM_RESULT_SIZE        64
#define ALARM_DEAL_TYPE_SIZE     64
#define ALARM_DEV_NAME_SIZE      64

    char        protocal[PROTOCAL_SIZE];
    char        community[COMMUNITY_SIZE];
    char        area_node[AREA_NODE_SIZE];
    char        alarm_id[ALARM_ID_SIZE];
    char        user[USER_SIZE];
    char        result[ALARM_RESULT_SIZE];
    char        type[ALARM_DEAL_TYPE_SIZE];//告警的处理类型
    char        time[DATETIME_SIZE];
    char        mac[MAC_SIZE];      //v3.1加密
    char        device_name[ALARM_DEV_NAME_SIZE];
    uint32_t    manager_account_id;
    int         alarm_code;
    int         alarm_zone;
    int         alarm_location;
    int         alarm_customize;    //1 设备自定义alarm
    uint32_t    unit_id;
    char        site[64];           // app 实际站点
    char        title[256];
    uint64_t    trace_id;

    SOCKET_MSG_ALARM_DEAL_INFO_T() {
        memset(this, 0, sizeof(*this));
    }
} SOCKET_MSG_ALARM_DEAL_INFO;


#endif

