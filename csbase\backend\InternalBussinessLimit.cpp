#include "InternalBussinessLimit.h"

InternalBussinessLimit::InternalBussinessLimit(int bussiness_type)
{
    if(bussiness_type == REPORT_RELAY)
    {
        period_ = 10; //10s内
        num_ = 8;   //8次
    }
    
    limit_status_ = NORMAL;
    memset(&msg_buffer_, 0, sizeof(MsgStruct));      
}

InternalBussinessLimit::~InternalBussinessLimit()
{

}

int InternalBussinessLimit::AddBussiness(const MsgStruct* msg_buffer)
{    
    //因为都是各自维护InternalBussinessLimit对象，所以不用加锁
    time_t now = time(nullptr);
    if (time_list_.size() < num_)
    {
        time_list_.push_front(now);
        limit_status_ =  NORMAL;
    }
    else
    {
        time_t time_index_first = time_list_.back();
        time_list_.pop_back();
        time_list_.push_front(now);
        if(now - time_index_first < period_)
        {
            memcpy(&msg_buffer_, msg_buffer, sizeof(MsgStruct));
            limit_status_ = LIMITED;
        }
        else
        {
            limit_status_ =  NORMAL;
        }
    }

    return limit_status_;
}

int InternalBussinessLimit::CheckBussinessLimit()
{
    int ret = limit_status_;
    if(limit_status_ ==  NORMAL)
    {
        //若已恢复正常，清理内存
        RemoveBussiness();
    }
    else
    {
        //避免下次心跳校验时还是旧状态
        limit_status_ = NORMAL;
    }
    
    return ret;
}

void InternalBussinessLimit::RemoveBussiness()
{
    time_list_.clear();
    memset(&msg_buffer_, 0, sizeof(MsgStruct));    
}

void InternalBussinessLimit::GetMsgBuffer(MsgStruct& msg_buffer)
{
    memcpy(&msg_buffer, &msg_buffer_, sizeof(MsgStruct));
}


