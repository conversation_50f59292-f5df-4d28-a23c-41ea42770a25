#ifndef __INTERNAL_BUSSINESS_LIMIT_H__
#define __INTERNAL_BUSSINESS_LIMIT_H__
#include <list>
#include <memory>
#include <map>
#include "AkcsCommonDef.h"
#include "DclientMsgDef.h"
#include "InnerMsgDef.h"
#include "AkLogging.h"
#include "MsgStruct.h"

//backend组件内部业务消息限流处理类
class InternalBussinessLimit
{
public:
    InternalBussinessLimit(int bussiness_type);
    ~InternalBussinessLimit();
    
    int AddBussiness(const MsgStruct* msg_buffer);    
    int CheckBussinessLimit();
    void RemoveBussiness();
    //获取缓存的消息信息
    void GetMsgBuffer(MsgStruct& msg_buffer);

    
    enum LimitStatus
    {
        NORMAL = 0,     //正常状态
        LIMITED = 1,   //进入限流状态
    };


    enum BussinessType
    {
    	REPORT_RELAY = 0, //对应的消息id MSG_FROM_DEVICE_REPORT_RELAY_STATUS
    };
private:
    int period_;
    unsigned int num_;
    std::list<time_t> time_list_;
    MsgStruct msg_buffer_;
    LimitStatus limit_status_;
};

typedef std::shared_ptr<InternalBussinessLimit> InternalBussinessLimitPtr;

typedef std::map<InternalBussinessLimit::BussinessType, InternalBussinessLimitPtr> BussinessLimitMap;


#endif
