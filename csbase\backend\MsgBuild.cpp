#include "tinystr.h"
#include "tinyxml.h"
#include "CsmainAES256.h"
#include "MsgBuild.h"
#include "util.h"
#include "CharChans.h"
#include <sys/types.h>
#include <sys/ipc.h>
#include <sys/msg.h>
#include <arpa/inet.h>
#include "DclientMsgDef.h"
#include "XmlTagDefine.h"
#include "AkLogging.h"
#include "DclientMsgSt.h"
#include "BackendAES256.h"
#include "AkcsCommonDef.h"
#include "XmlMsgBuilder.h"
#include "util_relay.h"
#include "json/json.h"
#include "encrypt/Base64.h"
#include "util_string.h"
#include "MsgIdToMsgName.h"


CMsgBuildHandle* GetMsgBuildHandleInstance()
{
    return CMsgBuildHandle::GetInstance();
}

CMsgBuildHandle::CMsgBuildHandle()
{

}

CMsgBuildHandle::~CMsgBuildHandle()
{

}

CMsgBuildHandle* CMsgBuildHandle::instance = NULL;

CMsgBuildHandle* CMsgBuildHandle::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CMsgBuildHandle();
    }

    return instance;
}
/*不转换因为之前有很多，这样写替换最简单---chenzhx20181012*/
static int NoTransTcharToUtf8(const TCHAR* pszSrc, char* pszDst, int nDstSize)
{
    if (pszSrc == NULL
            || pszDst == NULL
            || nDstSize <= 0)
    {
        return -1;
    }
    ::snprintf(pszDst, nDstSize, "%s", pszSrc);
    return 0;
}

static int rl_strcat_s(char* dst, const unsigned long src_len, const char* src)
{
    if (dst == NULL || src == NULL)
    {
        return -1;
    }

    std::size_t copy_size = strlen(src);
    std::size_t cur_len = strlen(dst);
    if (copy_size > ((src_len - 1) - cur_len))
    {
        copy_size = ((src_len - 1) - cur_len);
    }

    strncpy(dst + cur_len, src, copy_size);
    *(dst + cur_len + copy_size) = '\0';

    return 0;
}

std::string XmlTrans(const char* pszItem)
{
    char item[4096] = "";
    if (pszItem == NULL)
    {
        return item;
    }
    int size = strlen(pszItem);
    int item_pos = 0;
    for (int i = 0; i < size; i++)
    {
        char tmp = pszItem[i];
        switch (tmp)
        {
            case '&':
            {
                rl_strcat_s(item + item_pos, sizeof(item), "&amp;");
                item_pos += 5;
            }
            break;
            case '<':
            {
                rl_strcat_s(item + item_pos, sizeof(item), "&lt;");
                item_pos += 4;
            }
            break;
            case '>':
            {
                rl_strcat_s(item + item_pos, sizeof(item), "&gt;");
                item_pos += 4;
            }
            break;
            case '\'':
            {
                rl_strcat_s(item + item_pos, sizeof(item), "&apos;");
                item_pos += 6;
            }
            break;
            case '\"':
            {
                rl_strcat_s(item + item_pos, sizeof(item), "&quot;");
                item_pos += 6;
            }
            break;
            default:
                rl_strcat_s(item + item_pos, 2, pszItem + i);
                item_pos += 1;
                break;
        }
    }
    std::string strItem = item;
    return strItem;
}


int CMsgBuildHandle::BuildNormalMsgHeader(SOCKET_MSG* socket_message, uint16_t message_id, int ver, uint32_t data_size)
{
    if (socket_message == NULL)
    {
        return -1;
    }

    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message->data;
    msg_normal->magic[0] = SOCKET_MSG_MAGIC_MSB;
    msg_normal->magic[1] = SOCKET_MSG_MAGIC_LSB;
    msg_normal->message_id = message_id & SOCKET_MSG_ID_MASK;  //放空版本号
    if (ver == VERSION_1_0)
    {
        msg_normal->message_id = message_id | SOCKET_MSG_VERSION_01;  //不加密
    }
    else
    {
        msg_normal->message_id = message_id | SOCKET_MSG_VERSION_02;
    }
    msg_normal->head_size = htons(SOCKET_MSG_NORMAL_HEADER_SIZE);
    msg_normal->data_size = htons(data_size);

    return 0;
}


int CMsgBuildHandle::OnBuildCommonMsg(SOCKET_MSG* socket_msg, uint16_t msg_id, std::map<std::string, std::string>& tag_map, int need_mac)
{
    if (socket_msg == NULL)
    {
        AK_LOG_WARN << "OnBuildCommonMsg failed, socket_message is null";
        return -1;
    }

    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_msg->data;
    char* pay_load = (char*)msg_normal->data;

    if (0 == tag_map.count(csmain::xmltag::MAC))
    {
        AK_LOG_WARN << "OnBuildCommonMsg failed, map must contain Mac key";
        return -1;
    }
    string mac = tag_map[csmain::xmltag::MAC];
    if (need_mac == MsgParamControl::NO_NEED_MAC)
    {
        tag_map.erase(csmain::xmltag::MAC);
    }

    string xml_msg = BuildCommonMsg(tag_map);
    if (xml_msg.empty())
    {
        AK_LOG_WARN << "OnBuildCommonMsg failed, map must contain Type key";
        return -1;
    }
    AK_LOG_INFO << "Build MsgId=[" << msg_id << "]" << ". msgname = " << MsgIdToMsgName::GetDeclientMessageName(msg_id) << ", Xml Success.\n" << xml_msg;

    ::snprintf(pay_load, sizeof(msg_normal->data), "%s", xml_msg.c_str());
    int data_size = strlen(pay_load);

    AesEncryptByMac(pay_load, pay_load, mac, &data_size, sizeof(msg_normal->data)); 

    if (BuildNormalMsgHeader(socket_msg,  msg_id, VERSION_2_0, data_size) < 0)
    {
        AK_LOG_WARN << "BuildNormalMsgHeader failed";
        return -1;
    }

    socket_msg->size = data_size + SOCKET_MSG_NORMAL_HEADER_SIZE;
    return 0;
}

// 强制dynamic iv加密
int CMsgBuildHandle::OnBuildDyIvCommonMsg(SOCKET_MSG* socket_msg, uint16_t msg_id, std::map<std::string, std::string>& tag_map, int need_mac)
{
    if (socket_msg == NULL)
    {
        AK_LOG_WARN << "OnBuildDyIvCommonMsg failed, socket_message is null";
        return -1;
    }

    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_msg->data;
    char* pay_load = (char*)msg_normal->data;

    if (0 == tag_map.count(csmain::xmltag::MAC))
    {
        AK_LOG_WARN << "OnBuildDyIvCommonMsg failed, map must contain Mac key";
        return -1;
    }
    string mac = tag_map[csmain::xmltag::MAC];
    if (need_mac == MsgParamControl::NO_NEED_MAC)
    {
        tag_map.erase(csmain::xmltag::MAC);
    }

    string xml_msg = BuildCommonMsg(tag_map);
    if (xml_msg.empty())
    {
        AK_LOG_WARN << "OnBuildDyIvCommonMsg failed, map must contain Type key";
        return -1;
    }
    AK_LOG_INFO << "Build MsgId=[" << msg_id << "]" << ". msgname = " << MsgIdToMsgName::GetDeclientMessageName(msg_id) << ", Xml Success.\n" << xml_msg;

    ::snprintf(pay_load, sizeof(msg_normal->data), "%s", xml_msg.c_str());
    int data_size = strlen(pay_load);

    // 强制dynamic iv加密
    AesDyIvEncryptByMac(pay_load, pay_load, mac, &data_size); 

    if (BuildNormalMsgHeader(socket_msg,  msg_id, VERSION_2_0, data_size) < 0)
    {
        AK_LOG_WARN << "OnBuildDyIvCommonMsg failed";
        return -1;
    }

    socket_msg->size = data_size + SOCKET_MSG_NORMAL_HEADER_SIZE;
    return 0;
}

int CMsgBuildHandle::OnBuildCommonEncDefaultMsg(SOCKET_MSG& socket_msg,SOCKET_MSG& dy_iv_socket_message, uint16_t msg_id, std::map<std::string, std::string>& tag_map, int need_mac)
{
    memset(&socket_msg, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_msg.data;
    char* pay_load = (char*)msg_normal->data;

    string xml_msg = BuildCommonMsg(tag_map);
    if (xml_msg.empty())
    {
        AK_LOG_WARN << "OnBuildCommonMsg failed, map must contain Type key";
        return -1;
    }
    AK_LOG_INFO << "Build MsgId=[" << msg_id << "]" << ". msgname = " << MsgIdToMsgName::GetDeclientMessageName(msg_id) << ", Xml Success.\n" << xml_msg;

    ::snprintf(pay_load, sizeof(msg_normal->data), "%s", xml_msg.c_str());
    EncryptDefalutMsg(socket_msg, dy_iv_socket_message, msg_id);
    return 0;
}

int CMsgBuildHandle::EncryptDefalutMsg(SOCKET_MSG &socket_message, SOCKET_MSG& dy_iv_socket_message, int msg_id)
{
    memcpy(&dy_iv_socket_message, &socket_message, sizeof(socket_message));

    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message.data;
    char* payload = (char*)msg_normal->data;
    int data_size = strlen((char*)msg_normal->data);
    AesEncryptByDefault(payload, payload, &data_size, sizeof(msg_normal->data)); //默认都加密了
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;

    if (BuildNormalMsgHeader(&socket_message,  msg_id, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }

    socket_message.size = data_size + head_size;

    msg_normal = (SOCKET_MSG_NORMAL*)dy_iv_socket_message.data;
    payload = (char*)msg_normal->data;
    data_size = strlen((char*)msg_normal->data);
    AesEncryptByDefaultForDynamicsIV(payload, payload, &data_size, sizeof(msg_normal->data)); //默认都加密了
    head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;
    if (BuildNormalMsgHeader(&dy_iv_socket_message,  msg_id, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }
    dy_iv_socket_message.size = data_size + head_size;
    return 0;
}


/*<Msg>
  <Type>xxx</Type>
  <Params>
    <Tag1>0C11050A72E4</Tag1>
    <Tag2>0</Tag2>
  </Params>
</Msg>*/
std::string CMsgBuildHandle::BuildCommonMsg(std::map<std::string, std::string>& tag_map)
{
    if (tag_map.size() <= 1 || tag_map.count(csmain::xmltag::TYPE) == 0)
    {
        return "";
    }

    TiXmlElement root_node(XML_NODE_NAME_MSG);
    TiXmlElement* type_node = new TiXmlElement(XML_NODE_NAME_MSG_TYPE);
    string msg_type = tag_map[csmain::xmltag::TYPE];
    tag_map.erase(csmain::xmltag::TYPE);
    TiXmlText* type_text = new TiXmlText(msg_type.c_str());
    type_node->LinkEndChild(type_text);
    root_node.LinkEndChild(type_node);

    TiXmlElement* msg_param_node = new TiXmlElement(XML_NODE_NAME_MSG_PARAM);
    for (auto it = tag_map.begin(); it != tag_map.end(); it++)
    {
        string tag = it->first;
        TiXmlElement* node = new TiXmlElement(tag.c_str());

        string value = it->second;
        TiXmlText* text = new TiXmlText(value.c_str());
        node->LinkEndChild(text);

        msg_param_node->LinkEndChild(node);
    }
    root_node.LinkEndChild(msg_param_node);

    TiXmlPrinter printer;
    root_node.Accept(&printer);

    return printer.CStr();
}

int CMsgBuildHandle::BuildOnlineNotifyMsg(SOCKET_MSG* socket_message, const SOCKET_MSG_DEV_ONLINE_NOTIFY &online_msg, const std::string& mac)
{
    if (socket_message == NULL)
    {
        return -1;
    }

    uint16_t msg_id = MSG_TO_DEVICE_ONLINE_NOTIFY_MSG;
    std::map<std::string, std::string> tag_map;
    tag_map[csmain::xmltag::MAC] = online_msg.mac;
    tag_map[csmain::xmltag::VOICE_UNREAD] = std::to_string(online_msg.unread_voice_count);
    tag_map[csmain::xmltag::UUID] = online_msg.uuid;
    tag_map[csmain::xmltag::TYPE] = "OnlineNotifyMsg";

    if (OnBuildCommonMsg(socket_message, msg_id, tag_map, MsgParamControl::NO_NEED_MAC) < 0)
    {
        AK_LOG_WARN << "BuildOnlineNotifyMsg OnBuildCommonMsg failed.";
        return -1;
    }
    return 0;
}

void CMsgBuildHandle::BuildOnlineNotifyXmlMsg(const SOCKET_MSG_DEV_ONLINE_NOTIFY &online_msg, std::string& xml_msg)
{
    //组装消息
    std::map<std::string, std::string> tag_map;
    tag_map[csmain::xmltag::VOICE_UNREAD] = std::to_string(online_msg.unread_voice_count);
    tag_map[csmain::xmltag::UUID] = online_msg.uuid;
    tag_map[csmain::xmltag::TYPE] = "OnlineNotifyMsg";
    xml_msg = BuildCommonMsg(tag_map);
}

int CMsgBuildHandle::BuildVoiceMsgUrlNotifyMsg(SOCKET_MSG* socket_message, const SOCKET_MSG_DEV_VOICE_MSG_URL &url_msg, const std::string &mac)
{
    if (socket_message == NULL)
    {
        return -1;
    }

    uint16_t msg_id = MSG_TO_DEVICE_REPORT_VOICE_MSG_URL;
    std::map<std::string, std::string> tag_map;
    tag_map[csmain::xmltag::MAC] = mac;
    tag_map[csmain::xmltag::URL2] = url_msg.url;
    tag_map[csmain::xmltag::UUID] = url_msg.uuid;
    tag_map[csmain::xmltag::TYPE] = "ReportVoiceMsgURL";

    if (OnBuildCommonMsg(socket_message, msg_id, tag_map, MsgParamControl::NO_NEED_MAC) < 0)
    {
        AK_LOG_WARN << "BuildOnlineNotifyMsg OnBuildCommonMsg failed.";
        return -1;
    }

    return 0;
}

int CMsgBuildHandle::BuildVoiceMsgUrlNotifyMsg(const SOCKET_MSG_DEV_VOICE_MSG_URL &url_msg, const std::string &mac, std::string& xml_msg)
{
    std::map<std::string, std::string> tag_map;
    tag_map[csmain::xmltag::MAC] = mac;
    tag_map[csmain::xmltag::URL2] = url_msg.url;
    tag_map[csmain::xmltag::UUID] = url_msg.uuid;
    tag_map[csmain::xmltag::TYPE] = "ReportVoiceMsgURL";
    xml_msg = BuildCommonMsg(tag_map);
    return 0;
}

int CMsgBuildHandle::BuildRegEndUserUrlMsg(const RegEndUserInfo &user_info, const std::string &mac, std::string& xml_msg)
{
    char status[10];
    snprintf(status, sizeof(status), "%d", user_info.status);

    std::map<std::string, std::string> tag_map;
    tag_map[csmain::xmltag::MAC] = mac;
    tag_map[csmain::xmltag::ACCOUNT] = user_info.account;
    tag_map[csmain::xmltag::REG_URL] = user_info.reg_url;
    tag_map[csmain::xmltag::STATUS] = status;
    tag_map[csmain::xmltag::ACCOUNT_NAME] = user_info.account_name;
    tag_map[csmain::xmltag::EMAIL] = user_info.email;
    tag_map[csmain::xmltag::MOBILE_NUMBER] = user_info.mobile_number;
    tag_map[csmain::xmltag::TYPE] = "RegEndUser";
    xml_msg = BuildCommonMsg(tag_map);
    AK_LOG_INFO << "Build Xml Success.\n" << xml_msg;
    return 0;
}

int CMsgBuildHandle::BuildReqKitDevices(char *buf, int size, const std::vector<ResidentDev> &kit_devices)
{
    if (buf == NULL)
    {
        return -1;
    }

    char* temp_line = new char[XML_NODE_LINE_SIZE];
    char* temp_buffer = new char[XML_NODE_LINE_SIZE];

    memset(buf, 0, size);
    sprintf_s(temp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, temp_line);

    sprintf_s(temp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, "ReportKitDevices", XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, temp_line);


    sprintf_s(temp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, temp_line);

    for (auto kit_device : kit_devices)
    {
        memset(temp_buffer, 0, XML_NODE_LINE_SIZE);
        sprintf_s(temp_line, XML_NODE_LINE_SIZE, "\r\n\t\t\t<%s>%s</%s>", XML_NODE_NAME_MSG_PARAM_MAC, kit_device.mac, XML_NODE_NAME_MSG_PARAM_MAC);
        strcat_s(temp_buffer, XML_NODE_LINE_SIZE, temp_line);

        std::string encode_location = URLEncode(kit_device.location);
        sprintf_s(temp_line, XML_NODE_LINE_SIZE, "\r\n\t\t\t<%s>%s</%s>", XML_NODE_NAME_MSG_PARAM_LOCALTION, encode_location.c_str(), XML_NODE_NAME_MSG_PARAM_LOCALTION);
        strcat_s(temp_buffer, XML_NODE_LINE_SIZE, temp_line);
        sprintf_s(temp_line, XML_NODE_LINE_SIZE, "\r\n\t\t\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TYPE, kit_device.dev_type, XML_NODE_NAME_MSG_PARAM_TYPE);
        strcat_s(temp_buffer, XML_NODE_LINE_SIZE, temp_line);

        sprintf_s(temp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\t\t\r\n", XML_NODE_NAME_MSG_PARAM_ITEM, temp_buffer, XML_NODE_NAME_MSG_PARAM_ITEM);
        strcat_s(buf, size, temp_line);
    }

    sprintf_s(temp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, temp_line);

    sprintf_s(temp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, temp_line);

    delete []temp_line;
    delete []temp_buffer;

    return 0;
}

int CMsgBuildHandle::BuildVoiceMsgListNotifyMsg(char* buf, int size, const PersonalVoiceMsgSendList &send_list, const SOCKET_MSG_DEV_VOICE_MSG_LIST& voice_msg)
{
    assert(buf);
    char* tmp_line = new char[XML_NODE_LINE_SIZE];
    char* tmp_buffer = new char[XML_NODE_LINE_SIZE];

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, "ReportVoiceMsgList", XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_PAGE_INDEX, voice_msg.page_index, XML_NODE_NAME_MSG_PARAM_PAGE_INDEX);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_PAGE_SIZE, voice_msg.page_size, XML_NODE_NAME_MSG_PARAM_PAGE_SIZE);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MSG_COUNT, voice_msg.msg_count, XML_NODE_NAME_MSG_PARAM_MSG_COUNT);
    strcat_s(buf, size, tmp_line);

    for (auto voice_info : send_list)
    {
        memset(tmp_buffer, 0, XML_NODE_LINE_SIZE);
        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\r\n\t\t\t<%s>%s</%s>", XML_NODE_NAME_MSG_PARAM_UUID, voice_info.uuid, XML_NODE_NAME_MSG_PARAM_UUID);
        strcat_s(tmp_buffer, XML_NODE_LINE_SIZE, tmp_line);

        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\r\n\t\t\t<%s>%s</%s>", XML_NODE_NAME_MSG_PARAM_TIME, voice_info.time, XML_NODE_NAME_MSG_PARAM_TIME);
        strcat_s(tmp_buffer, XML_NODE_LINE_SIZE, tmp_line);

        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\r\n\t\t\t<%s>%s</%s>", XML_NODE_NAME_MSG_PARAM_LOCALTION, voice_info.location, XML_NODE_NAME_MSG_PARAM_LOCALTION);
        strcat_s(tmp_buffer, XML_NODE_LINE_SIZE, tmp_line);

        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\r\n\t\t\t<%s>%s</%s>", XML_NODE_NAME_MSG_PARAM_FRONTDOOR_MAC, voice_info.front_mac, XML_NODE_NAME_MSG_PARAM_FRONTDOOR_MAC);
        strcat_s(tmp_buffer, XML_NODE_LINE_SIZE, tmp_line);

        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\r\n\t\t\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_PARAM_STATUS, voice_info.status, XML_NODE_NAME_MSG_PARAM_STATUS);
        strcat_s(tmp_buffer, XML_NODE_LINE_SIZE, tmp_line);

        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\t\t\r\n", XML_NODE_NAME_MSG_PARAM_ITEM, tmp_buffer, XML_NODE_NAME_MSG_PARAM_ITEM);
        strcat_s(buf, size, tmp_line);
    }

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;
    delete []tmp_buffer;

    return 0;
}

int CMsgBuildHandle::BuildCommonAckMsg(SOCKET_MSG* socket_message, uint16_t msg_id, const SOCKET_MSG_COMMON_ACK &common_ack)
{
    if (socket_message == NULL)
    {
        return -1;
    }

    char msg_id_str[9];
    snprintf(msg_id_str, sizeof(msg_id_str), "%x", msg_id);

    std::map<std::string, std::string> tag_map;
    tag_map[csmain::xmltag::MAC] = common_ack.mac;
    tag_map[csmain::xmltag::RESULT] = to_string(common_ack.result);
    tag_map[csmain::xmltag::TRACE_ID] = common_ack.trace_id;
    tag_map[csmain::xmltag::MSG_ID] = msg_id_str;
    tag_map[csmain::xmltag::TYPE] = "Ack";

    if (OnBuildCommonMsg(socket_message, MSG_TO_DEVICE_ACK, tag_map, MsgParamControl::NO_NEED_MAC) < 0)
    {
        AK_LOG_WARN << "BuildCommonAckMsg failed.";
        return -1;
    }

    return 0;
}

int CMsgBuildHandle::BuildCommonAckMsg(uint16_t msg_id, const SOCKET_MSG_COMMON_ACK &common_ack, std::string &xml_msg)
{
    char msg_id_str[9];
    snprintf(msg_id_str, sizeof(msg_id_str), "%x", msg_id);

    std::map<std::string, std::string> tag_map;
    tag_map[csmain::xmltag::RESULT] = to_string(common_ack.result);
    tag_map[csmain::xmltag::TRACE_ID] = common_ack.trace_id;
    tag_map[csmain::xmltag::MSG_ID] = msg_id_str;
    tag_map[csmain::xmltag::TYPE] = "Ack";
    xml_msg = BuildCommonMsg(tag_map);
    return 0;
}

int CMsgBuildHandle::BuildCommonAckMsg(uint16_t msg_id, const std::string &msg_seq, std::string &xml_msg)
{
    char msg_id_str[9];
    snprintf(msg_id_str, sizeof(msg_id_str), "%x", msg_id);

    std::map<std::string, std::string> tag_map;
    tag_map[csmain::xmltag::MSG_SEQ] = msg_seq;
    tag_map[csmain::xmltag::RESULT] = "OK";
    tag_map[csmain::xmltag::TYPE] = "Ack";
    tag_map[csmain::xmltag::MSG_ID] = msg_id_str;
    xml_msg = BuildCommonMsg(tag_map);

    AK_LOG_INFO << "BuildCommonAckMsg success. msg:\n" << xml_msg;
    return 0;
}

//默认密码正常加密和动态iv加密
int CMsgBuildHandle::EncryptDefalutMacMsg(SOCKET_MSG &socket_message, SOCKET_MSG& dy_iv_socket_message, int msg_id)
{
    memcpy(&dy_iv_socket_message, &socket_message, sizeof(socket_message));

    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message.data;
    char* payload = (char*)msg_normal->data;
    int data_size = strlen((char*)msg_normal->data);
    AesEncryptByDefaultMac(payload, payload, &data_size, sizeof(msg_normal->data)); //默认都加密了
    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;

    if (BuildNormalMsgHeader(&socket_message,  msg_id, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }

    socket_message.size = data_size + head_size;

    msg_normal = (SOCKET_MSG_NORMAL*)dy_iv_socket_message.data;
    payload = (char*)msg_normal->data;
    data_size = strlen((char*)msg_normal->data);
    AesEncryptByDefaultMacDynamicsIV(payload, payload, &data_size, sizeof(msg_normal->data)); //默认都加密了
    head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;
    if (BuildNormalMsgHeader(&dy_iv_socket_message,  msg_id, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }
    dy_iv_socket_message.size = data_size + head_size;
    return 0;
}


//平台发送文本消息给设备\app
int CMsgBuildHandle::BuildTextMessageMsg(SOCKET_MSG &socket_message, SOCKET_MSG_TEXT_MESSAGE* text_message)
{
    if (text_message == NULL)
    {
        return -1;
    }

    memset(&socket_message, 0, sizeof(SOCKET_MSG));
    SOCKET_MSG_NORMAL* msg_normal = (SOCKET_MSG_NORMAL*)socket_message.data;
    if (BuildTextMessageXmlMsg((char*)msg_normal->data, sizeof(msg_normal->data), text_message) < 0)
    {
        return -1;
    }
    
    return 0;
}
/*
<Msg>
  <Type>TextMessage</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <MsgID>这条消息对应数据的ID</MsgID>
    <Title>Hello!</Title>
    <Content>Hello,Everyone!</Content>
    <From></From>
    <To></To>
    <Time>2016-08-26 10:00:00</Time>
  </Params>
</Msg>
*/
int CMsgBuildHandle::BuildTextMessageXmlMsg(char* buf, int size, SOCKET_MSG_TEXT_MESSAGE* text_message)
{
    if ((buf == NULL) || (text_message == NULL))
    {
        return -1;
    }

    char* tmp_line = new char[XML_NODE_LINE_SIZE];
    char* tmp_buffer = new char[XML_NODE_LINE_SIZE];

    //memset(buf, 0 ,sizeof(buf));
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE, SOCKET_MSG_TYPE_NAME_TEXT_MESSAGE, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    NoTransTcharToUtf8(text_message->protocal, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%d</%s>\r\n", XML_NODE_NAME_MSG_TYPE, text_message->type, XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    char tmp[32];
    ::snprintf(tmp, sizeof(tmp), "%d", text_message->id);
    NoTransTcharToUtf8(tmp, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_MSGID, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_MSGID);
    strcat_s(buf, size, tmp_line);


    NoTransTcharToUtf8(text_message->title, tmp_buffer, XML_NODE_LINE_SIZE);
    std::string payload = XmlTrans(tmp_buffer);
    //TranslateXmlFormat(payload);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TITLE, payload.c_str(), XML_NODE_NAME_MSG_PARAM_TITLE);
    strcat_s(buf, size, tmp_line);

    NoTransTcharToUtf8(text_message->content, tmp_buffer, XML_NODE_LINE_SIZE);
    payload = XmlTrans(tmp_buffer);
    //TranslateXmlFormat(payload);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_CONTENT, payload.c_str(), XML_NODE_NAME_MSG_PARAM_CONTENT);
    strcat_s(buf, size, tmp_line);

    NoTransTcharToUtf8(text_message->from, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_FROM, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_FROM);
    strcat_s(buf, size, tmp_line);

    NoTransTcharToUtf8(text_message->to, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TO, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_TO);
    strcat_s(buf, size, tmp_line);

    NoTransTcharToUtf8(text_message->time, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_TIME, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_TIME);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
    strcat_s(buf, size, tmp_line);

    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;
    delete []tmp_buffer;

    return 0;
}

/*
<Msg>
    <Params>
        <City>当前的城市</City>//城市
        <Weather>sunny</Weather> //sunny，cloudy，rainy，snowy，foggy---多语言需要设备自己转
        <Humidity>60</Humidity>//湿度（%RH）
        <Temperature>30</Temperature> //温度（摄氏度）---需要华氏度设备自己转
    </Params>
</Msg>
*/
int CMsgBuildHandle::BuildWeatherInfoMsg(SOCKET_MSG* socket_message, uint16_t msg_id, const SOCKET_MSG_DEV_WEATHER_INFO &weather_msg)
{
    if (socket_message == NULL)
    {
        return -1;
    }

    std::map<std::string, std::string> tag_map;
    tag_map[csmain::xmltag::TYPE] = "WeatherInfo";
    tag_map[csmain::xmltag::MAC] = weather_msg.mac;
    tag_map[csmain::xmltag::CITY] = weather_msg.city;
    tag_map[csmain::xmltag::WEATHER] = weather_msg.weather;
    tag_map[csmain::xmltag::HUMIDITY] = weather_msg.humidity;
    tag_map[csmain::xmltag::TEMPERATURE] = weather_msg.temperature;

    if (OnBuildCommonMsg(socket_message, msg_id, tag_map, MsgParamControl::NO_NEED_MAC) < 0)
    {
        AK_LOG_WARN << "BuildWeatherInfoMsg OnBuildCommonMsg failed.";
        return -1;
    }    

    return 0;       
}

int CMsgBuildHandle::BuildWeatherInfoMsg(const SOCKET_MSG_DEV_WEATHER_INFO &weather_msg, std::string &msg)
{
    std::map<std::string, std::string> tag_map;
    tag_map[csmain::xmltag::TYPE] = "WeatherInfo";
    tag_map[csmain::xmltag::MAC] = weather_msg.mac;
    tag_map[csmain::xmltag::CITY] = weather_msg.city;
    tag_map[csmain::xmltag::WEATHER] = weather_msg.weather;
    tag_map[csmain::xmltag::HUMIDITY] = weather_msg.humidity;
    tag_map[csmain::xmltag::TEMPERATURE] = weather_msg.temperature;

    msg = BuildCommonMsg(tag_map);
    return 0;       
}

int CMsgBuildHandle::BuildPacportUnlockCheckResMsg(const SOCKET_MSG_PACPORT_UNLOCK_RES& unlcok_check_res_info, std::string& msg)
{
    std::map<std::string, std::string> tag_map;
    tag_map[csmain::xmltag::CHECK_RES] = std::to_string(unlcok_check_res_info.result);
    tag_map[csmain::xmltag::TRACE_ID] = unlcok_check_res_info.trace_id;
    tag_map[csmain::xmltag::MAC] = unlcok_check_res_info.mac;
    tag_map[csmain::xmltag::TYPE] = "SendPacportCheckResult";
    tag_map[csmain::xmltag::PER_ID] = unlcok_check_res_info.master_account;

    msg = BuildCommonMsg(tag_map);
    AK_LOG_INFO << "Build Pacport Unlock Check result msg:" << msg;

    return 0;
}

int CMsgBuildHandle::BuildSendKitMsg(SOCKET_MSG& socket_msg, const std::string& mac)
{
    uint16_t msg_id = MSG_TO_DEVICE_REQUEST_IS_KIT;
    std::map<std::string, std::string> tag_map;
    tag_map[csmain::xmltag::MAC] = mac;
    tag_map[csmain::xmltag::IS_KIT] = "1";
    tag_map[csmain::xmltag::TYPE] = "RequestIsKit";

    if (OnBuildCommonMsg(&socket_msg, msg_id, tag_map, MsgParamControl::NO_NEED_MAC) < 0)
    {
        AK_LOG_WARN << "BuildSendKitMsg OnBuildCommonMsg failed.";
        return -1;
    }
    return 0;
}

int CMsgBuildHandle::BuildCreateRoomAckMsg(SOCKET_MSG* socket_message, uint16_t msg_id, const SOCKET_MSG_COMMON_SEQ_ACK& ack)
{
    char msg_id_str[9];
    snprintf(msg_id_str, sizeof(msg_id_str), "%x", ack.msg_id);

    std::map<std::string, std::string> tag_map;
    tag_map[csmain::xmltag::MAC] = ack.mac;
    tag_map[csmain::xmltag::MSG_SEQ] = ack.msg_seq;
    tag_map[csmain::xmltag::RESULT] = "OK";
    tag_map[csmain::xmltag::TYPE] = "Ack";
    tag_map[csmain::xmltag::MSG_ID] = msg_id_str;

    if (OnBuildDyIvCommonMsg(socket_message, MSG_TO_DEVICE_ACK, tag_map, MsgParamControl::NO_NEED_MAC) < 0)
    {
        AK_LOG_WARN << "BuildCreateRoomAckMsg failed.";
        return -1;
    }
    return 0;       
}

int CMsgBuildHandle::BuildHagerSendKitMsg(SOCKET_MSG* socket_message, const std::string& mac)
{
    if (socket_message == NULL)
    {
        return -1;
    }
    
    uint16_t msg_id = MSG_TO_DEVICE_REQUEST_IS_KIT;
    std::map<std::string, std::string> tag_map;
    tag_map[csmain::xmltag::TYPE] = "RequestIsKit";
    tag_map[csmain::xmltag::MAC] = mac;
    tag_map[csmain::xmltag::IS_KIT] = "1";

    if (OnBuildDyIvCommonMsg(socket_message, msg_id, tag_map, MsgParamControl::NO_NEED_MAC) < 0)
    {
        AK_LOG_WARN << "BuildHagerSendKitMsg failed.";
        return -1;
    }    

    return 0;       
}

int CMsgBuildHandle::BuildDevListChangeMsg(SOCKET_MSG* socket_message, uint16_t msg_id)
{
    if (socket_message == NULL)
    {
        return -1;
    }

    int head_size = SOCKET_MSG_NORMAL_HEADER_SIZE;
    int data_size = 0;
    if (BuildNormalMsgHeader(socket_message,  MSG_TO_DEVICE_SEND_DEVICE_LIST_CHANGE, VERSION_2_0, data_size) < 0)
    {
        return -1;
    }

    socket_message->size = data_size + head_size;

    return 0;       
}

int CMsgBuildHandle::BuildPacportUnlockResMsg(SOCKET_MSG* socket_message, uint16_t msg_id, const SOCKET_MSG_PACPORT_UNLOCK_RES &unlock_info)
{
    if (socket_message == nullptr)
    {
        return -1;
    }

    std::map<std::string, std::string> tag_map;
    tag_map[csmain::xmltag::CHECK_RES] = std::to_string(unlock_info.result);
    tag_map[csmain::xmltag::TRACE_ID] = unlock_info.trace_id;
    tag_map[csmain::xmltag::MAC] = unlock_info.mac;
    tag_map[csmain::xmltag::TYPE] = "SendPacportCheckResult";
    tag_map[csmain::xmltag::PER_ID] = unlock_info.master_account;

    if (OnBuildCommonMsg(socket_message, msg_id, tag_map, MsgParamControl::NO_NEED_MAC) < 0)
    {
        AK_LOG_WARN << "BuildPacportUnlockResMsg OnBuildCommonMsg failed.";
        return -1;
    }    

    return 0;
}

void CMsgBuildHandle::BuildReqChangeAppRelayMsg(uint64_t relay_status, int relay_type, const std::string& mac, std::string& msg, uint16_t& msg_id , int start_relay_id)
{
    //根据relay类型组装Dclient消息
    switch (relay_type)
    {
        //室内机LocalRelay
        case IndoorRelayType::TYPE_LOCAL:
            BuildReqChangeAppLocalRelayMsg(relay_status, relay_type, mac, msg);
            msg_id = MSG_TO_APP_CHANGE_RELAY_STATUS;
            break;
        case IndoorRelayType::TYPE_EXTERN:
            BuildReqChangeAppExternRelayMsg(relay_status, relay_type, mac, msg, start_relay_id);
            msg_id = MSG_TO_APP_CHANGE_RELAY_STATUS_COMMON;
            break;
        default:
            AK_LOG_INFO << "Build req change app relay msg, relay type:" << relay_type;
            break;
    }
    return;
}

void CMsgBuildHandle::BuildReqChangeAppLocalRelayMsg(uint64_t relay_status, int relay_type, const std::string& mac, std::string& msg)
{
    XmlBuilder xml_builder("ChangeRelayStatus");

    XmlKV tag_map;
    tag_map[csmain::xmltag::MAC] = mac;
    tag_map[csmain::xmltag::RELAY_STATUS] = std::to_string(relay_status);

    xml_builder.AddKeyValue(tag_map);

    msg = xml_builder.GenerateXML();

    AK_LOG_INFO << "build dclient msg success. msg:\n" << msg;
}

/*
<Msg>
  <Type>RequestAppChangeRelay</Type>
  <Params> 
      <Mac>0C11050068F4</Mac> //室内机MAC 长度：32字节
      <Relays>
          <Relay Type="Extern" Status="1">0</Relay> //app relay id从0开始
          <Relay Type="Extern" Status="1">2</Relay>
     </Relays>  
  </Params>   
</Msg>
*/
void CMsgBuildHandle::BuildReqChangeAppExternRelayMsg(uint64_t relay_status, int relay_type, const std::string& mac, std::string& msg, int start_relay_id)
{
    XmlBuilder xml_builder("RequestAppChangeRelay");

    XmlKV tag_map;
    tag_map[csmain::xmltag::MAC] = mac;
    xml_builder.AddKeyValue(tag_map);

    XmlKeyAttrKv tag_relays;
    ChangeIndoorExternRelayStatusToAppUse(relay_status, tag_relays, start_relay_id);
    xml_builder.AddKeyListValue("Relays", "Relay", tag_relays);

    msg = xml_builder.GenerateXML();

    AK_LOG_INFO << "build dclient msg success. msg:\n" << msg;
    return;
}

void CMsgBuildHandle::BuildEmergencyDoorControlMsg(const SOCKET_MSG_EMERGENCY_CONTROL& control_msg, const std::string& mac, std::string& msg)
{
    std::map<std::string, std::string> tag_map;
    
    tag_map[csmain::xmltag::TYPE] = "RequestEmergencyDoorControl";
    tag_map[csmain::xmltag::MSG_TYPE] = std::to_string(control_msg.auto_manual);
    tag_map[csmain::xmltag::MSG_UUID] = control_msg.msg_uuid;
    tag_map[csmain::xmltag::INITIATOR] = control_msg.initiator;
    tag_map[csmain::xmltag::SECURITY_REALY] = control_msg.security_relay;
    tag_map[csmain::xmltag::RELAY] = control_msg.relay;
    tag_map[csmain::xmltag::MAC] = mac;

    msg = BuildCommonMsg(tag_map);
    AK_LOG_INFO << "Build EmergencyDoorControlMsg \n " << msg;
    return;
}

void CMsgBuildHandle::BuildEmergencyControlNotifyMsg(int control_type, const std::string& time, const std::string& receiver_account, std::string& msg)
{
    std::map<std::string, std::string> tag_map;

    if (receiver_account.size() > 0)
    {
        tag_map[csmain::xmltag::SITE] = receiver_account;
    }

    tag_map[csmain::xmltag::TYPE] = "EmergencyControlNotify";
    tag_map[csmain::xmltag::CONTROL_TYPE] = std::to_string(control_type);
    tag_map[csmain::xmltag::TIME] = time;

    msg = BuildCommonMsg(tag_map);
    AK_LOG_INFO << "Build EmergencyControlNotifyMsg  \n " << msg;
    return;
}

void CMsgBuildHandle::BuildAlarmAckMsg(const SOCKET_MSG_ALARM& alarm_msg, std::string& msg)
{
    XmlBuilder xml_builder("AlarmAck", alarm_msg.protocal);
    
    XmlKV tag_map;

    tag_map[csmain::xmltag::ID] = std::to_string(alarm_msg.alarm_id);
    tag_map[csmain::xmltag::MSG_SEQ] = alarm_msg.msg_seq;
    tag_map[csmain::xmltag::RELAY_TYPE] = alarm_msg.type;
    tag_map[csmain::xmltag::ALARM_CODE] = std::to_string(alarm_msg.alarm_code);

    xml_builder.AddKeyValue(tag_map);
    
    msg = xml_builder.GenerateXML();

    AK_LOG_INFO << "Build AlarmAckMsg\n" << msg;
}

/*
<Msg>
  <Type>CheckVisitorIDAccessAck</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <Result>1</Result>  (校验成功：1 ，校验失败：0)
    <MsgSeq>429496795</MsgSeq>
    <Relay>123</Relay>
    <SecurityRelay>12</SecurityRelay>
    <VisitorUUID>xxxxxxxxxxxxxxx</VistorUUID>           //36字节
  </Params>
</Msg>
*/

void CMsgBuildHandle::BuildCheckVisitorIDAccessReplyMsg(const SOCKET_MSG_TO_DEVICE_CHECK_ID_ACCESS& check_id_access_res, std::string& msg)
{
    XmlKV tag_map;
    tag_map[csmain::xmltag::RESULT] = std::to_string(check_id_access_res.check_res);
    tag_map[csmain::xmltag::MSG_SEQ] = check_id_access_res.msg_seq;
    tag_map[csmain::xmltag::RELAY] = check_id_access_res.relay;
    tag_map[csmain::xmltag::SECURITY_REALY] = check_id_access_res.security_relay;
    tag_map[csmain::xmltag::VISITOR_UUID] = check_id_access_res.visitor_uuid;
    tag_map[csmain::xmltag::INITIATOR] = check_id_access_res.initiator;
    tag_map[csmain::xmltag::VISITOR_NAME] = check_id_access_res.visitor_name;

    XmlBuilder xml_builder("CheckVisitorIDAccessAck", check_id_access_res.protocal);
    xml_builder.AddKeyValue(tag_map);

    msg = xml_builder.GenerateXML();
    AK_LOG_INFO << "build check visitor id access reply msg:\n" << msg;

    return;
}

// 发给app和室内机的
void CMsgBuildHandle::BuildAlarmOccuredNotifyMsg(const SOCKET_MSG_ALARM_SEND& alarm_msg, std::string& msg)
{
    XmlKV tag_map;
    std::ostringstream filled_msg_id;
    filled_msg_id << std::setw(10) << std::setfill('0') << alarm_msg.id;
    
    tag_map[csmain::xmltag::ID] = filled_msg_id.str();
    tag_map[csmain::xmltag::MAC_CAPITAL] = alarm_msg.mac;
    tag_map[csmain::xmltag::TIME] = alarm_msg.time;
    tag_map[csmain::xmltag::TITLE] = alarm_msg.title;
    // 兼容address 没有值的场景
    tag_map[csmain::xmltag::SITE] = strlen(alarm_msg.address) == 0 ? alarm_msg.site : alarm_msg.address;
    tag_map[csmain::xmltag::MSG_SEQ] = alarm_msg.msg_seq;
    tag_map[csmain::xmltag::RELAY_TYPE] = alarm_msg.type;
    tag_map[csmain::xmltag::ADDRESS] = alarm_msg.from_local;
    tag_map[csmain::xmltag::EXTENSION] = std::to_string(alarm_msg.extension);
    tag_map[csmain::xmltag::DEV_TYPE] = std::to_string(alarm_msg.device_type);
    tag_map[csmain::xmltag::FROM_NAME] = alarm_msg.from_local;
    tag_map[csmain::xmltag::ALARM_CODE] = std::to_string(alarm_msg.alarm_code);
    tag_map[csmain::xmltag::ALARM_ZONE] = std::to_string(alarm_msg.alarm_zone);
    tag_map[csmain::xmltag::ALARM_LOCATION] = std::to_string(alarm_msg.alarm_location);
    tag_map[csmain::xmltag::ALARM_CUSTOMIZE] = std::to_string(alarm_msg.alarm_customize);

    XmlBuilder xml_builder("AlarmSend", alarm_msg.protocal);
    xml_builder.AddKeyValue(tag_map);

    msg = xml_builder.GenerateXML();
    AK_LOG_INFO << "BuildAlarmOccuredNotifyMsg \n " << msg;
    
    return;
}

// 发给管理机的
/*
<Msg>
  <Type>Alarm</Type>
  <Protocal>1.0</Protocal>
  <Params>
        <MsgSeq>1234444</MsgSeq>
        <ID>74019876</ID>
        <Type>Normal</Type>
        <FromName>INDOOR_1.1.1.1.2-1</FromName>
        <RelayName>door 1</RelayName>
        <ToName>MANAGEMENT_1.1.1-1</ToName>
        <From>192.168.1.14</From>
        <To>192.168.1.15</To>
        <Time>2016-08-26 10:00:00</Time>
  </Params>
</Msg>
*/
void CMsgBuildHandle::BuildAlarmNotifyManageMsg(const SOCKET_MSG_ALARM_SEND& alarm_msg, std::string& msg)
{
    std::map <std::string, std::string> tag_map;
    std::ostringstream filled_msg_id;
    filled_msg_id << std::setw(10) << std::setfill('0') << alarm_msg.id;
    
    tag_map[csmain::xmltag::ID] = filled_msg_id.str();
    tag_map[csmain::xmltag::APT] = alarm_msg.APT;
    tag_map[csmain::xmltag::MAC_CAPITAL] = alarm_msg.mac;
    tag_map[csmain::xmltag::TIME] = alarm_msg.time;
    tag_map[csmain::xmltag::ALARM_MSG] = alarm_msg.type;
    tag_map[csmain::xmltag::MSG_SEQ] = alarm_msg.msg_seq;
    tag_map[csmain::xmltag::FROM_NAME] = alarm_msg.from_local;
    tag_map[csmain::xmltag::ALARM_CODE] = std::to_string(alarm_msg.alarm_code);
    tag_map[csmain::xmltag::ALARM_ZONE] = std::to_string(alarm_msg.alarm_zone);
    tag_map[csmain::xmltag::ALARM_LOCATION] = std::to_string(alarm_msg.alarm_location);
    tag_map[csmain::xmltag::ALARM_CUSTOMIZE] = std::to_string(alarm_msg.alarm_customize);

    XmlBuilder xml_builder("AlarmSend", alarm_msg.protocal);
    xml_builder.AddKeyValue(tag_map);

    msg = xml_builder.GenerateXML();
    
    AK_LOG_INFO << "BuildAlarmNotifyManageMsg \n " << msg;
    return;
}


/*
<Msg>
  <Type>MotionAlert</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <MAC>04294920011</MAC> (检测到motion的设备MAC)
     <CaptureID>1343</CaptureID> (这条capture mysql id)
     <Sip>10033</Sip> (sip账号)
     <Time>2018-04-17 10:00:29</Time> (上报时间)
     <Location>R26</Location>
    <Site>站点账号 64字节字符串</Site> 
  </Params>
</Msg>
*/
void CMsgBuildHandle::BuildMotionAlertMsg(const SOCKET_MSG_MOTION_ALERT_SEND& motion_alert_send, std::string& msg)
{
    XmlBuilder xml_builder("MotionAlert", motion_alert_send.protocal);

    XmlKV tag_map;
    tag_map[csmain::xmltag::MAC_CAPITAL] = motion_alert_send.mac;
    tag_map[csmain::xmltag::CAPTURE_ID] = std::to_string(motion_alert_send.id);
    tag_map[csmain::xmltag::SIP] = motion_alert_send.sip_account;
    tag_map[csmain::xmltag::TIME] = motion_alert_send.capture_time;
    tag_map[csmain::xmltag::LOCATION] = motion_alert_send.location;
    tag_map[csmain::xmltag::SITE] = motion_alert_send.node;
    tag_map[csmain::xmltag::DETECTION_TYPE] = std::to_string(motion_alert_send.detection_type);
    tag_map[csmain::xmltag::DETECTION_INFO] = std::to_string(motion_alert_send.detection_info);

    xml_builder.AddKeyValue(tag_map);

    msg = xml_builder.GenerateXML();

    AK_LOG_INFO << "build motion alert notify msg:\n" << msg;
    return;
}

void CMsgBuildHandle::BuildCheckTmpKeyAckMsg(const SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& tempkey_info, std::string& msg)
{
    XmlKV tag_map;
    tag_map[csmain::xmltag::MAC] = tempkey_info.mac;
    tag_map[csmain::xmltag::RESULT] = std::to_string(tempkey_info.result);
    tag_map[csmain::xmltag::MSG_SEQ] = tempkey_info.msg_seq;
    tag_map[csmain::xmltag::RELAY] = tempkey_info.relay;
    tag_map[csmain::xmltag::SECURITY_REALY] = tempkey_info.security_relay;
    tag_map[csmain::xmltag::UNIT_APT] = tempkey_info.unit_apt;
    tag_map[csmain::xmltag::PER_ID] = tempkey_info.account;
    tag_map[csmain::xmltag::LIFT_FLOOR_NUM] = tempkey_info.floor;
    tag_map[csmain::xmltag::COMPANY_UUID] = tempkey_info.company_uuid;

    XmlBuilder xml_builder("CheckTmpKeyAck");
    xml_builder.AddKeyValue(tag_map);
    
    msg = xml_builder.GenerateXML();

    AK_LOG_INFO << "BuildCheckTmpKeyAckMsg success. \n" << msg;
    return;
}

/*
<Msg>
   <Type>ResponseAntipassbackOpenDoor</Type>
   <Params>
      <TraceID>**********</TraceID>        // 消息ID，32位，回应哪条消息的消息ID
      <Result>0</Result>                   // 返回结果，0-允许开门，1-拒绝开门
      <PersonnelID>123456</PersonnelID>    // 人员ID，tmpkey时用code
      <AccessMode>1</AccessMode>           // 0-Normal, 1-Entry, 2-Exit, 3-Entry Violation(违规进入), 4-Exit Violation(违规出去)
   </Params>
</Msg>
*/
void CMsgBuildHandle::BuildAntiPassbackRespMsg(const SOCKET_MSG_RESP_ANTI_PASSBACK_OPEN& resp_msg, std::string &msg)
{
    XmlBuilder xml_builder("ResponseAntipassbackOpenDoor");

    XmlKV tag_map;
    tag_map[csmain::xmltag::MAC] = resp_msg.mac;
    tag_map[csmain::xmltag::TRACE_ID] = resp_msg.trace_id;
    tag_map[csmain::xmltag::PERSONNEL_ID] = resp_msg.personnel_id;
    tag_map[csmain::xmltag::RESULT] = std::to_string(int(resp_msg.result));
    tag_map[csmain::xmltag::ACCESS_MODE] = std::to_string(int(resp_msg.access_mode));
    
    xml_builder.AddKeyValue(tag_map);
    msg = xml_builder.GenerateXML();
    
    AK_LOG_INFO << "BuildAntiPassbackResp success. \n" << msg;
    return;
}

void CMsgBuildHandle::BuildMessageTxtInfoMsg(const AK::Server::P2PCommonTxtMsgNotifyMsg &txt_msg, std::string &msg)
{
    std::map<std::string, std::string> tag_map;
    tag_map[csmain::xmltag::MSG_TIME] = txt_msg.time();
    tag_map[csmain::xmltag::MSG_FROM] = txt_msg.from();
    tag_map[csmain::xmltag::MSG_TO] = txt_msg.to();
    tag_map[csmain::xmltag::TITEL2] = txt_msg.title();
    tag_map[csmain::xmltag::MSG_CONTENT] = txt_msg.content();

    XmlBuilder xml_builder("TextMessage");
    xml_builder.AddKeyValue(tag_map);
    
    msg = xml_builder.GenerateXML();  
    AK_LOG_INFO << "BuildMessageTxtInfoMsg success. \n" << msg;
    return;
}

void CMsgBuildHandle::BuildDclientOpenDoorAckMsg(const SOCKET_MSG_DEV_REMOTE_ACK& ack_msg, std::string& msg)
{
    std::string trace_id = ack_msg.trace_id;
    //trace_id：dev_mac_traceid
    auto flag_pos = trace_id.find_first_of("_");
    auto site_or_mac_pos = trace_id.find_last_of("_");
    if (flag_pos == std::string::npos || site_or_mac_pos == std::string::npos || site_or_mac_pos <= flag_pos)
    {
        AK_LOG_WARN << "BuildOpenDoorAckMsg failed. open door trace_id=" << trace_id;
        return;
    }
    auto mac_str = trace_id.substr(flag_pos + 1, site_or_mac_pos - flag_pos - 1);
    auto origin_trace_id = trace_id.substr(site_or_mac_pos + 1);

    std::map<std::string, std::string> tag_map;
    tag_map[csmain::xmltag::MAC] = mac_str;
    tag_map[csmain::xmltag::INFO] = ack_msg.info;
    tag_map[csmain::xmltag::TRACE_ID] = origin_trace_id;
    tag_map[csmain::xmltag::RESULT] = std::to_string(ack_msg.result);    

    XmlBuilder xml_builder("OpenDoorAck");
    xml_builder.AddKeyValue(tag_map);

    msg = xml_builder.GenerateXML();  
    AK_LOG_INFO << "BuildOpenDoorAckMsg success. \n" << msg;
    return;
}



/*
<Msg>
<Params>
  <Type>RemoteOpenDoor</Type>  // 业务类型标识
  <TraceID></TraceID>      // 64位，唯一标识某条消息
  <Resp>{Resp}</Resp>
</Params>
</Msg>

{Resp}:
{
  "err_code": "**********",
  "message": "app slave account unactive",
  "datas": {
    "key1": "value1",
    "key2": "value2"
  }
}
*/
void CMsgBuildHandle::BuildAppAsyncResponseMsg(const std::string& msg_type, const std::string& trace_id, AppAsyncResponseMsg& resp_msg, std::string& msg)
{
    Json::Value data;
    data["err_code"] = resp_msg.err_code;
    data["message"] = resp_msg.message;
    if (strlen(resp_msg.json_datas) > 0)
    {
        data["datas"] = resp_msg.json_datas;
    }

    Json::FastWriter writer;
    std::string resp;
    try 
    {
        resp = writer.write(data);
    } 
    catch (const std::exception& e) 
    
    {
        AK_LOG_WARN << "get json string failed.";
        return;
    }

    //将resp用base64编码处理，避免xml将其转义
    std::string base64_out = GetBase64Str(resp);

    std::map<std::string, std::string> tag_map;
    tag_map[csmain::xmltag::TRACE_ID] = trace_id;
    tag_map[csmain::xmltag::MSG_RESP] = base64_out;

    XmlBuilder xml_builder(msg_type);
    xml_builder.AddKeyValue(tag_map);

    msg = xml_builder.GenerateXML();
    AK_LOG_INFO << "BuildAppAsyncResponseAckMsg success. \n" << msg;
    return;
}
/*
<Msg>
    <Type>RespRecordVideo</Type>
    <Params>
        <Items>
            <Item>
                <CallTraceID ID="xxxtraceid">0</CallTraceID> <!-- 状态: 0=OK, 1=NoPermission, 2=NoFoundTraceID, 3=NoFoundVideo(没有录制视频)。只有OK室内机需要显示可以播放视频 -->
            </Item>
            <Item>
                <CallTraceID ID="xxxtraceid">0</CallTraceID> <!-- 状态: 0=OK, 1=NoPermission, 2=NoFoundTraceID, 3=NoFoundVideo -->
            </Item>
            <!-- 可以继续添加其他 Item -->
        </Items>
    </Params>
</Msg>
*/
void CMsgBuildHandle::BuildRecordVideoInfoMsg(std::vector<SOCKET_MSG_REQUEST_RECORD_VIDEO>& record_msg_list, std::string &msg)
{
    XmlBuilder xml_builder("RespRecordVideo");
    
    std::unique_ptr<TiXmlElement> items_element(new TiXmlElement(XML_NODE_NAME_MSG_PARAM_ITEMS));
    TiXmlElement* items_ptr = items_element.get();
    xml_builder.GetParamsElement()->LinkEndChild(items_element.release());  

    // 为每个记录创建Item节点
    for (const auto& record_msg : record_msg_list) 
    {
        std::unique_ptr<TiXmlElement> item_element(new TiXmlElement(XML_NODE_NAME_MSG_PARAM_ITEM));
        std::unique_ptr<TiXmlElement> call_trace_element(new TiXmlElement(XML_NODE_NAME_MSG_CALL_TRACE_ID));
        std::unique_ptr<TiXmlText> status_text(new TiXmlText(std::to_string(static_cast<int>(record_msg.status)).c_str()));

        call_trace_element->SetAttribute(XML_NODE_NAME_MSG_PARAM_ID, record_msg.call_trace_id);

        call_trace_element->LinkEndChild(status_text.release());
        item_element->LinkEndChild(call_trace_element.release());
        items_ptr->LinkEndChild(item_element.release());
    }

    msg = xml_builder.GenerateXML();
    AK_LOG_INFO << "BuildRecordVideoInfoMsg success. msg:\n" << msg;

    return;
}

/*
<Msg>
    <Type>VideoUrl</Type>
    <Params>
        <URL ID="xxxtraceid">https://xxxxxxx/xxxxxx</URL>  // 这个url是有过期时间的，点播失败要重新请求URL。长度512
    </Params>
</Msg>
*/
void CMsgBuildHandle::BuildRecordVideoPlayUrlMsg(const std::string& call_trace_id, const std::string& video_play_url, std::string &msg)
{
    XmlBuilder xml_builder("VideoUrl");

    std::unique_ptr<TiXmlElement> url_element(new TiXmlElement(XML_NODE_NAME_MSG_PARAM_URL2));

    url_element->SetAttribute(XML_NODE_NAME_MSG_PARAM_ID, call_trace_id.c_str());

    std::unique_ptr<TiXmlText> url_text(new TiXmlText(video_play_url.c_str()));
    url_element->LinkEndChild(url_text.release());

    xml_builder.GetParamsElement()->LinkEndChild(url_element.release());

    msg = xml_builder.GenerateXML();
    
    AK_LOG_INFO << "BuildRecordVideoPlayUrlMsg success. msg:\n" << msg;
    return;
}





/*<Msg>
  <Type>RequestOpenDoor</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <MAC>0C11050A72E4</MAC>
    <Relay>0</Relay>    //relay_id，开哪个门
    <TraceID>2123dfasd23223gdd</TraceID>    //新增：时间戳+8位随机字符串(数字+字母大小写字符)
  </Params>
</Msg>*/

void CMsgBuildHandle::BuildRequestOpenDoorMsg(const SOCKET_MSG_DEVICE_REQUEST_OPEN_DOOR& request_open_door, std::string& msg)
{
    XmlKV tag_map;
    tag_map[csmain::xmltag::MAC_CAPITAL] = request_open_door.mac;
    tag_map[csmain::xmltag::RELAY] = request_open_door.relay;
    tag_map[csmain::xmltag::TRACE_ID] = request_open_door.trace_id;

    XmlBuilder xml_builder("RequestOpenDoor", request_open_door.protocal);
    xml_builder.AddKeyValue(tag_map);

    msg = xml_builder.GenerateXML();
    AK_LOG_INFO << "build request open door msg:\n" << msg;

    return;
}

/*<Msg>
  <Type>RequestOpenSecurityRelay</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <MAC>0C11050A72E4</MAC>
    <Relay>0</Relay>    //relay_id，开哪个门
    <TraceID>2123dfasd23223gdd</TraceID>    //新增：时间戳+8位随机字符串(数字+字母大小写字符)
  </Params>
</Msg>*/
void CMsgBuildHandle::BuildRequestOpenSecurityRelayMsg(const SOCKET_MSG_DEVICE_REQUEST_OPEN_DOOR& request_open_door, std::string& msg)
{
    XmlKV tag_map;
    tag_map[csmain::xmltag::MAC_CAPITAL] = request_open_door.mac;
    tag_map[csmain::xmltag::RELAY] = request_open_door.relay;
    tag_map[csmain::xmltag::TRACE_ID] = request_open_door.trace_id;

    XmlBuilder xml_builder("RequestOpenSecurityRelay", request_open_door.protocal);
    xml_builder.AddKeyValue(tag_map);

    msg = xml_builder.GenerateXML();
    AK_LOG_INFO << "build request open security relay msg:\n" << msg;

    return;
}

/*
<Msg>
  <Type>OpenDoorAck</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <result>1</result>  //是否开门成功
  </Params>
</Msg>
*/
void CMsgBuildHandle::BuildOpenDoorAckMsg(const SOCKET_MSG_OPEN_DOOR_ACK_T& open_door_ack, std::string& msg)
{
    XmlKV tag_map;
    tag_map[csmain::xmltag::RESULT] = std::to_string(open_door_ack.result);

    XmlBuilder xml_builder("OpenDoorAck", open_door_ack.protocal);
    xml_builder.AddKeyValue(tag_map);

    msg = xml_builder.GenerateXML();
    AK_LOG_INFO << "build request open door ack msg:\n" << msg;

    return;
}

/*
<Msg>
  <Type>Reboot</Type>
  <Protocal>1.0</Protocal>
</Msg>
或
<Msg>
  <Type>OpenDoor</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <Item>0</Item>
  </Params>
</Msg>
*/
int CMsgBuildHandle::BuildRemoteControlMsg(char* buf, int size, SOCKET_MSG_REMOTE_CONTROL* remote_control_msg)
{
    if ((buf == NULL) || (remote_control_msg == NULL))
    {
        return -1;
    }

    char* tmp_line = new char[XML_NODE_LINE_SIZE];
    char* tmp_buffer = new char[XML_NODE_LINE_SIZE];

    //memset(buf, 0 ,sizeof(buf));
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "<%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(remote_control_msg->type, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_TYPE,  XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_TYPE);
    strcat_s(buf, size, tmp_line);

    TransTcharToUtf8(remote_control_msg->protocal, tmp_buffer, XML_NODE_LINE_SIZE);
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PROTOCAL,  XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PROTOCAL);
    strcat_s(buf, size, tmp_line);

    //判断是否插入Params
    if (_tcslen(remote_control_msg->item[0]) > 0)
    {
        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t<%s>\r\n", XML_NODE_NAME_MSG_PARAM);
        strcat_s(buf, size, tmp_line);

        for (int i = 0; i < REMOTE_CONTROL_ITEM_NUM; i++)
        {
            if (_tcslen(remote_control_msg->item[i]) == 0)
            {
                break;
            }
            TransTcharToUtf8(remote_control_msg->item[i], tmp_buffer, XML_NODE_LINE_SIZE);
            sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t\t<%s>%s</%s>\r\n", XML_NODE_NAME_MSG_PARAM_ITEM, XmlTrans(tmp_buffer).c_str(), XML_NODE_NAME_MSG_PARAM_ITEM);
            strcat_s(buf, size, tmp_line);
        }

        sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "\t</%s>\r\n", XML_NODE_NAME_MSG_PARAM);
        strcat_s(buf, size, tmp_line);
    }
    sprintf_s(tmp_line, XML_NODE_LINE_SIZE, "</%s>\r\n", XML_NODE_NAME_MSG);
    strcat_s(buf, size, tmp_line);

    CHECK_BUFFER_OVER_FLOW(strlen(buf), size);
    delete []tmp_line;
    delete []tmp_buffer;

    return 0;
}


/*
<Msg>
  <Type>DealAlarmNotify</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <ID>00000042949</ID>                   (alarmid， 10位数字)
     <user>XXX</user>                       (告警的处理人)
     <Result>XXX</Result>                   (告警的处理内容,长度最大512字节)
     <Type>XXX</Type>                       (告警的处理类型)
     <Time>YYYY-MM-HH DD:HH:SS</Time>       (告警的处理时间)
     <DevName>xxx设备的location</DevName>   
  </Params>
</Msg>
*/
// 构造告警被处理的消息
void CMsgBuildHandle::BuildAlarmDealReplyMsg(const SOCKET_MSG_ALARM_DEAL_INFO& alarm_deal_msg, std::string& msg)
{
    std::map <std::string, std::string> tag_map;
    tag_map[csmain::xmltag::ID] = alarm_deal_msg.alarm_id;
    tag_map[csmain::xmltag::USER] = alarm_deal_msg.user;
    tag_map[csmain::xmltag::RESULT] = alarm_deal_msg.result;
    tag_map[csmain::xmltag::TYPE] = alarm_deal_msg.type;
    tag_map[csmain::xmltag::TIME] = alarm_deal_msg.time;
    tag_map[csmain::xmltag::DEV_NAME] = alarm_deal_msg.device_name;

    tag_map[csmain::xmltag::ALARM_CODE] = std::to_string(alarm_deal_msg.alarm_code);
    tag_map[csmain::xmltag::ALARM_ZONE] = std::to_string(alarm_deal_msg.alarm_zone);
    tag_map[csmain::xmltag::ALARM_LOCATION] = std::to_string(alarm_deal_msg.alarm_location);
    tag_map[csmain::xmltag::ALARM_CUSTOMIZE] = std::to_string(alarm_deal_msg.alarm_customize);
    tag_map[csmain::xmltag::SITE] = alarm_deal_msg.site;
    tag_map[csmain::xmltag::TITLE] = alarm_deal_msg.title;

    XmlBuilder xml_builder("DealAlarmNotify", alarm_deal_msg.protocal);
    xml_builder.AddKeyValue(tag_map);

    msg = xml_builder.GenerateXML();
    AK_LOG_INFO << "BuildAlarmDealReplyMsg \n " << msg;
    return;
}

void CMsgBuildHandle::BuildLockDownControlMsg(const SOCKET_MSG_REQUEST_LOCKDOWN& lockdown_info, std::string& msg)
{
    std::map <std::string, std::string> tag_map;
    tag_map[csmain::xmltag::MODE] = lockdown_info.mode;
    tag_map[csmain::xmltag::RELAY] = lockdown_info.relay;
    tag_map[csmain::xmltag::UUID] = lockdown_info.msg_uuid;
    tag_map[csmain::xmltag::SECURITY_REALY] = lockdown_info.security_relay;

    XmlBuilder xml_builder("RequestLockdownDoor");
    xml_builder.AddKeyValue(tag_map);

    msg = xml_builder.GenerateXML();
    AK_LOG_INFO << "BuildLockDownControlMsg \n " << msg;
    return;
}

int CMsgBuildHandle::BuildVoiceAssistantTokenMsg(const std::string& token, std::string &msg)
{
    std::map<std::string, std::string> tag_map;
    tag_map[csmain::xmltag::TOKEN] = token;
    tag_map[csmain::xmltag::TYPE] = "VoiceAssistantToken";

    msg = BuildCommonMsg(tag_map);
    return 0;       
}

void CMsgBuildHandle::BuildSmartLockMessageNotifyEventXmlMsg(const SOCKET_MSG_TEXT_MESSAGE& text_message, const std::string& site, const std::string& lock_name, std::string& msg)
{
    std::map<std::string, std::string> tag_map;

    int event_type = 0;
    switch (text_message.type)
    {
        case (int)MessageType2::SMARTLOCK_DOORBELL_EVENT:
            event_type = (int)SmartLockEventType::DOORBELL_EVENT;
            break;
        default:
            break;
    }

    tag_map[csmain::xmltag::LOCATION] = lock_name;
    tag_map[csmain::xmltag::EVENT_TYPE] = std::to_string(event_type);
    tag_map[csmain::xmltag::TIME] = text_message.time;
    tag_map[csmain::xmltag::MESSAGE_ACCOUNT_LIST_ID] = std::to_string(text_message.id);
    tag_map[csmain::xmltag::SITE] = site;

    XmlBuilder xml_builder("SmartLockEventNotify");
    xml_builder.AddKeyValue(tag_map);

    msg = xml_builder.GenerateXML();
}

void CMsgBuildHandle::BuildSL20LockEventNotifyMsg(int event_type, const std::string& lock_uuid, const std::string& site, std::string& msg)
{
    std::map<std::string, std::string> tag_map;

    tag_map[csmain::xmltag::LOCK_UUID] = lock_uuid;
    tag_map[csmain::xmltag::EVENT_TYPE] = std::to_string(event_type);
    tag_map[csmain::xmltag::SITE] = site;

    XmlBuilder xml_builder("SmartLockEventNotify");
    xml_builder.AddKeyValue(tag_map);

    msg = xml_builder.GenerateXML();
    AK_LOG_INFO << "BuildSL20LockEventNotifyMsg \n " << msg;
}