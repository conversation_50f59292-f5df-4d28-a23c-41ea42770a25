#ifndef __MSG_BUILD__H__
#define __MSG_BUILD__H__
#include <string>
#include <map>
#include <vector>
#include <iomanip>
#include "DclientMsgDef.h"
#include "InnerMsgDef.h"
#include "dbinterface/PersonalVoiceMsg.h"
#include "DclientMsgSt.h"
#include "AkcsCommonSt.h"
#include "AK.BackendCommon.pb.h"
#include "AK.Server.pb.h"
#include "PendingRegUser.h"
#include "dbinterface/resident/ResidentDevices.h"


enum MsgParamControl
{
    NO_NEED_MAC = 0,
    NEED_MAC = 1
};

enum MsgParamEncrypt
{
    MAC_KEY = 0,
    DEFAULT_KEY = 1
};

typedef std::map<std::string/*key*/, std::string/*value*/> XmlKV;
typedef std::map<std::string/*key*/, XmlKV > XmlKeyAttrKv;

class CMsgBuildHandle
{
public:
    CMsgBuildHandle();
    ~CMsgBuildHandle();
    static CMsgBuildHandle* GetInstance();

    int BuildVoiceMsgUrlNotifyMsg(SOCKET_MSG* socket_message, const SOCKET_MSG_DEV_VOICE_MSG_URL &url_msg, 
        const std::string  &mac);
    int BuildVoiceMsgUrlNotifyMsg(const SOCKET_MSG_DEV_VOICE_MSG_URL &url_msg, const std::string &mac, std::string& xml_msg);  

    int BuildOnlineNotifyMsg(SOCKET_MSG* socket_message, const SOCKET_MSG_DEV_ONLINE_NOTIFY &online_msg, const std::string& mac);
    int BuildSendKitMsg(SOCKET_MSG& socket_msg, const std::string& mac);
    int BuildHagerSendKitMsg(SOCKET_MSG* socket_message, const std::string& mac);

    int BuildCommonAckMsg(SOCKET_MSG* socket_message, uint16_t msg_id, const SOCKET_MSG_COMMON_ACK &common_ack);
    int BuildCommonAckMsg(uint16_t msg_id, const SOCKET_MSG_COMMON_ACK &common_ack, std::string &xml_msg);
    int BuildCommonAckMsg(uint16_t msg_id, const std::string &msg_seq, std::string &xml_msg);
    int BuildTextMessageMsg(SOCKET_MSG &socket_message, SOCKET_MSG_TEXT_MESSAGE* text_message);
    
    void BuildOnlineNotifyXmlMsg(const SOCKET_MSG_DEV_ONLINE_NOTIFY &online_msg, std::string& xml_msg);
    int BuildTextMessageXmlMsg(char* buf, int size, SOCKET_MSG_TEXT_MESSAGE* text_message);
    int BuildWeatherInfoMsg(SOCKET_MSG* socket_message, uint16_t msg_id, const SOCKET_MSG_DEV_WEATHER_INFO &weather_msg);
    int BuildDevListChangeMsg(SOCKET_MSG* socket_message, uint16_t msg_id);
    int BuildPacportUnlockResMsg(SOCKET_MSG* socket_message, uint16_t msg_id, const SOCKET_MSG_PACPORT_UNLOCK_RES &unlock_info);
    int BuildPacportUnlockCheckResMsg(const SOCKET_MSG_PACPORT_UNLOCK_RES& unlcok_check_res_info, std::string& msg);
    
    int BuildVoiceMsgListNotifyMsg(char* buf, int size, const PersonalVoiceMsgSendList &send_list, const SOCKET_MSG_DEV_VOICE_MSG_LIST& voice_msg);
    int BuildWeatherInfoMsg(const SOCKET_MSG_DEV_WEATHER_INFO &weather_msg, std::string &msg);
    int BuildCreateRoomAckMsg(SOCKET_MSG* socket_message, uint16_t msg_id, const SOCKET_MSG_COMMON_SEQ_ACK& ack);
    int BuildNormalMsgHeader(SOCKET_MSG* socket_message, uint16_t message_id, int ver, uint32_t data_size);
    void BuildReqChangeAppRelayMsg(uint64_t relay_status, int relay_type, const std::string& mac, std::string& msg, uint16_t& msg_id, int start_relay_id = 0);
    void BuildAntiPassbackRespMsg(const SOCKET_MSG_RESP_ANTI_PASSBACK_OPEN& resp, std::string &msg);
    void BuildEmergencyDoorControlMsg(const SOCKET_MSG_EMERGENCY_CONTROL& control_msg, const std::string& mac, std::string& msg);
    void BuildAlarmOccuredNotifyMsg(const SOCKET_MSG_ALARM_SEND& alarm_msg, std::string& msg);
    void BuildAlarmNotifyManageMsg(const SOCKET_MSG_ALARM_SEND& alarm_msg, std::string& msg);
    void BuildEmergencyControlNotifyMsg(int control_type, const std::string& time, const std::string& receiver_account, std::string& msg);
    void BuildCheckVisitorIDAccessReplyMsg(const SOCKET_MSG_TO_DEVICE_CHECK_ID_ACCESS& check_id_access_res, std::string& msg);
    void BuildMotionAlertMsg(const SOCKET_MSG_MOTION_ALERT_SEND& motion_alert_send, std::string& msg);
    void BuildCheckTmpKeyAckMsg(const SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& tempkey_info, std::string& msg);
    void BuildAlarmAckMsg(const SOCKET_MSG_ALARM& alarm_msg, std::string& msg);
    void BuildMessageTxtInfoMsg(const AK::Server::P2PCommonTxtMsgNotifyMsg &txt_msg, std::string &msg);    
    void BuildDclientOpenDoorAckMsg(const SOCKET_MSG_DEV_REMOTE_ACK& ack_msg, std::string& msg);
    void BuildAppAsyncResponseMsg(const std::string& msg_type, const std::string& trace_id, AppAsyncResponseMsg& resp_msg, std::string& msg);
    void BuildRecordVideoInfoMsg(std::vector<SOCKET_MSG_REQUEST_RECORD_VIDEO>& request_record_msg_list, std::string &msg);
    void BuildRecordVideoPlayUrlMsg(const std::string& call_trace_id, const std::string& video_play_url, std::string &msg);
    int BuildRegEndUserUrlMsg(const RegEndUserInfo &user_info, const std::string &mac, std::string& xml_msg);
    int BuildReqKitDevices(char *buf, int size, const std::vector<ResidentDev> &kit_devices);
    void BuildRequestOpenDoorMsg(const SOCKET_MSG_DEVICE_REQUEST_OPEN_DOOR& request_open_door, std::string& msg);
    void BuildRequestOpenSecurityRelayMsg(const SOCKET_MSG_DEVICE_REQUEST_OPEN_DOOR& request_open_door, std::string& msg);
    int  BuildRemoteControlMsg(char* buf, int size, SOCKET_MSG_REMOTE_CONTROL* remote_control_msg);
    void BuildOpenDoorAckMsg(const SOCKET_MSG_OPEN_DOOR_ACK_T& open_door_ack, std::string& msg);
    void BuildAlarmDealReplyMsg(const SOCKET_MSG_ALARM_DEAL_INFO& alarm_deal_info, std::string& msg);
    void BuildLockDownControlMsg(const SOCKET_MSG_REQUEST_LOCKDOWN& lockdown_info, std::string& msg);
    int BuildVoiceAssistantTokenMsg(const std::string& token, std::string &msg);
    void BuildSmartLockMessageNotifyEventXmlMsg(const SOCKET_MSG_TEXT_MESSAGE& text_message, const std::string& site, const std::string& lock_name, std::string& msg);
    void BuildSL20LockEventNotifyMsg(int event_type, const std::string& lock_uuid, const std::string& site, std::string& msg);

private:
    //common
    int OnBuildCommonMsg(SOCKET_MSG* socket_msg, uint16_t msg_id, std::map<std::string, std::string>& tag_map, int need_mac);
    int OnBuildDyIvCommonMsg(SOCKET_MSG* socket_msg, uint16_t msg_id, std::map<std::string, std::string>& tag_map, int need_mac);
    int OnBuildCommonEncDefaultMsg(SOCKET_MSG& socket_msg,SOCKET_MSG& dy_iv_socket_message, uint16_t msg_id, std::map<std::string, std::string>& tag_map, int need_mac);
    std::string BuildCommonMsg(std::map<std::string, std::string>& tag_map);
    int EncryptDefalutMsg(SOCKET_MSG &socket_message, SOCKET_MSG& dy_iv_socket_message, int msg_id);
    int EncryptDefalutMacMsg(SOCKET_MSG &socket_message, SOCKET_MSG& dy_iv_socket_message, int msg_id);
    static CMsgBuildHandle* instance;
    void BuildReqChangeAppLocalRelayMsg(uint64_t relay_status, int relay_type, const std::string& mac, std::string& msg);
    void BuildReqChangeAppExternRelayMsg(uint64_t relay_status, int relay_type, const std::string& mac, std::string& msg, int start_relay_id );
};

CMsgBuildHandle* GetMsgBuildHandleInstance();

#endif

