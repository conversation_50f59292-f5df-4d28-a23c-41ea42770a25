#include "tinystr.h"
#include "tinyxml.h"
#include "CsmainAES256.h"
#include "MsgParse.h"
#include "util.h"
#include "CharChans.h"
#include <sys/types.h>
#include <sys/ipc.h>
#include <sys/msg.h>
#include <arpa/inet.h>
#include "DclientMsgDef.h"
#include "AKCSDao.h"
#include "AkLogging.h"
#include "AkcsCommonDef.h"
#include "dbinterface/AntiPassbackDoor.h"

CMsgParseHandle* GetMsgParseHandleInstance()
{
    return CMsgParseHandle::GetInstance();
}

CMsgParseHandle::CMsgParseHandle()
{

}

CMsgParseHandle::~CMsgParseHandle()
{

}

CMsgParseHandle* CMsgParseHandle::instance = NULL;

CMsgParseHandle* CMsgParseHandle::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CMsgParseHandle();
    }

    return instance;
}


/*
<Msg>
  <Type>ReportStatus</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <DeviceID>*******.2</DeviceID>
    <Extension>1</Extension>
    <Type>1</Type>
    <IP>*************</IP>
    <SM>*************</SM>
    <GW>***********</GW>
    <DNS1>***********</DNS1>
    <DNS2>***********</DNS2>
    <MAC>0C1105000001</MAC>
    <Status>Idle</Status>
    <SWVer>**********</SWVer>
    <HWVer>********.0.0.0.0</HWVer>
    <PrikeyMD5>BB80C9DC38FC4F1FF0DFB71A13CEF28B</PrikeyMD5>
    <RfidMD5>BB80C9DC38FC4F1FF0DFB71A13CEF28B</RfidMD5>
    <AddrMD5>BB80C9DC38FC4F1FF0DFB71A13CEF28B</AddrMD5>
    <ConfigMD5>BB80C9DC38FC4F1FF0DFB71A13CEF28B</ConfigMD5>
    <ContactMD5>BB80C9DC38FC4F1FF0DFB71A13CEF28B</ContactMD5>
    <DClientVer>1</DClientVer> dclient版本号，目前版本定为1
  </Params>
</Msg>
*/
int CMsgParseHandle::ParseReportStatusMsg(char* buf, SOCKET_MSG_REPORT_STATUS* report_status_message, uint32_t data_size, uint32_t version)
{
    if ((buf == NULL) || (report_status_message == NULL))
    {
        return -1;
    }
    int dy_iv = 0;
    //新版本需要先进行AES解密处理
    if (version == VERSION_2_0)
    {
        AesDecryptByDefaultForReportStatus(buf, buf, data_size, dy_iv);
    }
    memset(report_status_message, 0, sizeof(SOCKET_MSG_REPORT_STATUS));
    report_status_message->dynamics_iv = dy_iv;

    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.buf is " <<  buf;
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
        {
            //暂不需要处理
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
        {
            TransUtf8ToTchar(node->GetText(), report_status_message->protocal, sizeof(report_status_message->protocal) / sizeof(TCHAR));
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_DEVICEID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->device_id, sizeof(report_status_message->device_id) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_EXTENSION) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->extension, sizeof(report_status_message->extension) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_DOWNLOADSERVER) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->download_server, sizeof(report_status_message->download_server) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_TYPE) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->type, sizeof(report_status_message->type) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_IP) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->ip_addr, sizeof(report_status_message->ip_addr) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_SUBNETMASK) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->subnet_mask, sizeof(report_status_message->subnet_mask) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_GATEWAY) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->gateway, sizeof(report_status_message->gateway) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_PRIMARYDNS) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->primary_dns, sizeof(report_status_message->primary_dns) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_SECONDARYDNS) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->secondary_dns, sizeof(report_status_message->secondary_dns) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MAC) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->mac, sizeof(report_status_message->mac) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_STATUS) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->status, sizeof(report_status_message->status) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_SWVER) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->SWVer, sizeof(report_status_message->SWVer) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_HWVER) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->HWVer, sizeof(report_status_message->HWVer) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_PRIKEYMD5) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->private_key_md5, sizeof(report_status_message->private_key_md5) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_RFIDMD5) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->rf_id_md5, sizeof(report_status_message->rf_id_md5) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_ADDRMD5) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->address_md5, sizeof(report_status_message->address_md5) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_CONFIGMD5) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->config_md5, sizeof(report_status_message->config_md5) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_TZMD5) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->tz_md5, sizeof(report_status_message->tz_md5) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_TZDATAMD5) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->tz_data_md5, sizeof(report_status_message->tz_data_md5) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_FACEMD5) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->face_md5, sizeof(report_status_message->face_md5) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_DCLIENT_VERSION) == 0)
                {
                    report_status_message->dclient_version = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_CONTACTMD5) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->contact_md5, sizeof(report_status_message->contact_md5) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_CHECK_CODE) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->auth_code, sizeof(report_status_message->auth_code) / sizeof(TCHAR));
                    if (!strcasecmp(report_status_message->auth_code, "NULL"))
                    {
                        ::snprintf(report_status_message->auth_code, sizeof(report_status_message->auth_code), "%s", "");
                    }
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_ARMINGTYPE) == 0)
                {
                    report_status_message->indoor_arming = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_RELAY_STATUS) == 0)
                {
                    char doornum[RELAY_NUM+1] = {0};
                    TransUtf8ToTchar(sub_node->GetText(), doornum, sizeof(doornum));                  
                    report_status_message->relay_status = DoornumToRelayStatus(doornum);
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_SCHEDULEMD5) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->schedule_md5, sizeof(report_status_message->schedule_md5) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MATEMD5) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_status_message->user_meta_md5, sizeof(report_status_message->user_meta_md5) / sizeof(TCHAR));
                }                

            }
        }
    }
    AK_LOG_INFO << "Mac " << report_status_message->mac << " Report Statu:" << buf;

    return 0;
}

/*
<Msg>
  <Type>CheckTmpKeyAck</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <Result>1</Result>  (校验不成功：0)
    <MsgSeq>429496795</MsgSeq>
    <Relay>123</Relay>
    <UnitApt>12-101</UnitApt>
  </Params>
</Msg>
*/

int CMsgParseHandle::ParseReportVoiceMsg(char *buf, void *msg1)
{
    if (buf == NULL || msg1 == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }
    PersonalVoiceMsgInfo* msg = reinterpret_cast<PersonalVoiceMsgInfo*>(msg1);

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReportVoiceMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MSG_TYPE) == 0)
                {
                    msg->msg_type = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_TYPE) == 0)
                {
                    msg->dev_type = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_UID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->uid, sizeof(msg->uid) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_FILENAME2) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->file_name, sizeof(msg->file_name) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PIC_NAME) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->pic_name, sizeof(msg->pic_name) / sizeof(TCHAR));
                }
            }
        }
    }

    return 0;
}


/*
<Msg>
  <Type>RequestVoiceMsgList</Type>
  <Params>
    <PageIndex>0</PageIndex>  //当前页数；0,1,2...
    <PageSize>5</PageSize>  //单页数目
  </Params>
</Msg>
*/
int CMsgParseHandle::ParseRequestVoiceMsgList(char *buf,  void *msg1)
{
    if (buf == NULL || msg1 == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    SOCKET_MSG_DEV_VOICE_MSG_LIST* msg = reinterpret_cast<SOCKET_MSG_DEV_VOICE_MSG_LIST*>(msg1);
    
    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReportVoiceMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_PAGE_INDEX) == 0)
                {
                    msg->page_index = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_PAGE_SIZE) == 0)
                {
                    msg->page_size = ATOI(sub_node->GetText());
                }
            }
        }
    }

    return 0;
}

/*
<Msg>
  <Type>RequestVoiceMsgURL</Type>
  <Params>
    <UUID>xxxx</UUID>  //语音信息UUID
  </Params>
  </Msg>
*/
int CMsgParseHandle::ParseRequestVoiceMsgUrl(char *buf, void *msg1)
{
    if (buf == NULL || msg1 == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }
    SOCKET_MSG_DEV_VOICE_MSG_URL* msg = reinterpret_cast<SOCKET_MSG_DEV_VOICE_MSG_URL*>(msg1);

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseRequestVoiceMsgUrl text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_UUID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->uuid, sizeof(msg->uuid) / sizeof(TCHAR));
                }
            }
        }
    }

    return 0;
}


/*
<Msg>
  <Type>RequestDelVoiceMsg</Type>
  <Params>
    <UUID>xxxx</UUID>  //语音信息UUID
  </Params>
</Msg>
*/
int CMsgParseHandle::ParseRequestDelVoiceMsg(char *buf, void *msg1)
{
    if (buf == NULL || msg1 == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }
    SOCKET_MSG_DEV_VOICE_MSG_URL* msg = reinterpret_cast<SOCKET_MSG_DEV_VOICE_MSG_URL*>(msg1);

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReportVoiceMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_UUID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->uuid, sizeof(msg->uuid) / sizeof(TCHAR));
                }
            }
        }
    }

    return 0;
}

int CMsgParseHandle::ProcessAppRequestChangeRelayMsg(SOCKET_MSG_NORMAL* pNormalMsg, SOCKET_MSG_APP_REQUEST_CHANGE_RELAY& change_relay_msg)
{
    if (pNormalMsg == NULL)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    //int msg_version = (pNormalMsg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密
    uint32_t data_size = ntohs(pNormalMsg->data_size);
    char* payload = (char*)pNormalMsg->data;
    
    AesDecryptByDefault(payload, payload, data_size);

    if (ParseRequestChangeRelayStatusMsg(payload, (void*)&change_relay_msg) < 0)
    {
        AK_LOG_WARN << "ParseRequestChangeRelayStatusMsg failed.";
        return -1;
    }

    return 0;
}

/*
<Msg>
    <Type>ChangeRelay</Type>
    <Params>
        <Mac>0Cxxxxxxx</Mac>
        <Relay Type="Extern">0</Relay> //relay id
        <Switch>1</Switch> //开关 1-开 0-关
    </Params>
</Msg>
*/
int CMsgParseHandle::ParseRequestChangeRelayStatusMsg(char *buf, void *msg)
{
    if (buf == NULL || msg == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }
    SOCKET_MSG_APP_REQUEST_CHANGE_RELAY *change_relay_msg = reinterpret_cast<SOCKET_MSG_APP_REQUEST_CHANGE_RELAY*>(msg);

    char text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseIndoorRelayStatus text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed";
        return -1;
    }
    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }
    //主节点名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched" << XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MAC) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), change_relay_msg->mac, sizeof(change_relay_msg->mac));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_RELAY) == 0)
                {
                    char tmp[8] = {};
                    TransTcharToUtf8(sub_node->GetText(), tmp, sizeof(tmp));
                    change_relay_msg->relay_id = ATOI(tmp);
                    const char* relay_type = sub_node->Attribute(XML_NODE_ATTRIBUTE_TYPE);
                    if(relay_type)
                    {
                        if(strcmp(relay_type, XML_NODE_ATTRIBUTE_RELAY_TYPE_EXTERN) == 0)
                        {
                            change_relay_msg->relay_type = IndoorRelayType::TYPE_EXTERN;
                        }
                        else if(strcmp(relay_type, XML_NODE_ATTRIBUTE_RELAY_TYPE_LOCAL) == 0)
                        {
                            change_relay_msg->relay_type = IndoorRelayType::TYPE_LOCAL;
                        }
                    }
                    //旧接口兼容
                    else
                    {
                        change_relay_msg->relay_type = IndoorRelayType::TYPE_LOCAL;
                    }
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_SWITCH) == 0)
                {
                    char tmp[8] = {};
                    TransUtf8ToTchar(sub_node->GetText(), tmp, sizeof(tmp));
                    change_relay_msg->relay_switch = ATOI(tmp);
                } 
            }
        }
    }
    return 0;
}

int CMsgParseHandle::ProcessAppReportStatusMsg(SOCKET_MSG_NORMAL* pNormalMsg, SOCKET_MSG_PERSONNAL_APP_CONF& app_config)
{
    if (pNormalMsg == NULL)
    {
        AK_LOG_WARN << "The input params is null.";
        return -1;
    }

    int msg_version = (pNormalMsg->message_id & SOCKET_MSG_VERSION_MASK) >> SOCKET_MSG_VERSION_OFFSET; //版本号,根据这个来判断是否加密
    uint32_t data_size = ntohs(pNormalMsg->data_size);
    char* payload = (char*)pNormalMsg->data;
    //新版本需要先进行AES解密处理
    if (msg_version == VERSION_2_0)
    {
        int dy_iv = 0;
        AesDecryptByDefaultForReportStatus(payload, payload, data_size, dy_iv);
        app_config.dynamics_iv = dy_iv;
    }

    if (ParseAppReportStatusMsg(payload, &app_config) < 0)
    {
        AK_LOG_WARN << "ParseAppReportStatusMsg failed.";
        return -1;
    }
    return 0;
}


int CMsgParseHandle::ParseAppReportStatusMsg(char* buf, SOCKET_MSG_PERSONNAL_APP_CONF* personnal_info)
{
    if ((buf == NULL) || (personnal_info == NULL))
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
        {
            if (strncmp(node->GetText(), XML_NODE_NAME_MSG_TYPE_REPORT_STATUS, strlen(XML_NODE_NAME_MSG_TYPE_REPORT_STATUS)) != 0)
            {
                //AK_LOG_WARN << "Type Node must be %s, but it is %s"), __FUNCTIONW__, XML_NODE_NAME_MSG_TYPE_REPORT_STATUS, node->GetText());
                //return -1;
            }
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
        {
            TransUtf8ToTchar(node->GetText(), personnal_info->protocal, sizeof(personnal_info->protocal));
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_USER) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), personnal_info->user, sizeof(personnal_info->user));
                }

                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_PASSWD) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), personnal_info->password, sizeof(personnal_info->password));
                }

                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_TOKEN) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), personnal_info->token, sizeof(personnal_info->token));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_FCM_TOKEN) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), personnal_info->fcm_token, sizeof(personnal_info->fcm_token));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_VOIP_TOKEN) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), personnal_info->voip_token, sizeof(personnal_info->voip_token));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MODELID) == 0)
                {
                    personnal_info->mobile_type = ATOI(sub_node->GetText() ? sub_node->GetText() : "");
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_APP_TOKEN) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), personnal_info->app_token, sizeof(personnal_info->app_token));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_COMMON_VERSION) == 0)
                {
                    personnal_info->version = ATOI(sub_node->GetText() ? sub_node->GetText() : "");
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MSGSEQ) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), personnal_info->msg_seq, sizeof(personnal_info->msg_seq));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_APP_VERSION) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), personnal_info->app_version, sizeof(personnal_info->app_version));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_OEM_NAME) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), personnal_info->oem_name, sizeof(personnal_info->oem_name));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_APP_LANGUAGE) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), personnal_info->language, sizeof(personnal_info->language));
                    if (strlen(personnal_info->language) == 0)
                    {
                        TransUtf8ToTchar("en", personnal_info->language, sizeof(personnal_info->language));
                    }
                }

            }
        }
    }

    AK_LOG_INFO << "ParseAppReportStatusMsg user:" << personnal_info->user;
    return 0;
}
/*
<Msg>
    <Type>RequestWeather</Type>
    <Params>
        <ManualUpdate>0</ManualUpdate>//0 or 1 手动刷新赋1  定时更新赋0
    </Params>
</Msg>
*/
int CMsgParseHandle::ParseRequestWeatherMsg(char *buf, void *msg1)
{
    if (buf == NULL || msg1 == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }
    SOCKET_MSG_DEV_REQ_WEATHER_WAY* msg = reinterpret_cast<SOCKET_MSG_DEV_REQ_WEATHER_WAY*>(msg1);

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseRequestWeatherMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }
    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_MANUAL_UPDATE) == 0)
                {
                    msg->manual_update = ATOI(sub_node->GetText());
                }
            }
        }
    }

    return 0;
}
/*
<Msg>  
    <Type>ReportDoorRelay</Type>
    <Params>   
        <Input>1102</Input> //共四个字符，代表relay1-4，其中0为关 1为开 2为未检测到，如示例即为relay1-2为开 relay3为关 relay4为未检测到（此字段允许为空；但若此字段有值，尽管设备没有relayxx也代表未检测到relayxx，对应位数需要传2）
        <SecInput>12</SecInput> //共两个个字符，代表security relay1-2，其中0为关 1为开 2为未检测到，如示例即为security relay1为开 relay2为未检测到 （此字段允许为空；但若此字段有值，尽管设备没有relayxx也代表未检测到relayxx，对应位数需要传2）
        <TraceID>xxxxxxxxxxxx</TraceID>//云端回复ACK时会带回去，用于设备确认唯一一条信令
    <Params>  
<Msg> 
*/
int CMsgParseHandle::ParseReportRelayStatusMsg(char *buf, void *msg1)
{
    if (buf == NULL || msg1 == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }
    SOCKET_MSG_RELAY_STATUS* msg = reinterpret_cast<SOCKET_MSG_RELAY_STATUS*>(msg1);

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReportRelayStatusMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
        {
            TransUtf8ToTchar(node->GetText(), msg->msg_type, sizeof(msg->msg_type));
        }
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_DOOR_RELAY_STATUS) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->door_relay_status, sizeof(msg->door_relay_status) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_DOOR_SE_RELAY_STATUS) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->door_se_relay_status, sizeof(msg->door_se_relay_status) / sizeof(TCHAR));
                }
                else if(strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_TRACE_ID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->trace_id, sizeof(msg->trace_id) / sizeof(TCHAR));
                }
            }
        }
    }

    return 0;
}

/*
<Msg>   
  <Type>PacportRegist</Type>
  <Params> 
      <Status>1</Status>   // 0-从Pacport云注销 1-注册到Pacport云  长度：2字节
  </Params>   
</Msg>
*/
int CMsgParseHandle::ParsePacportRegMsg(char *buf, void *req_msg)
{
    if (buf == nullptr || req_msg == nullptr)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }
    SOCKET_MSG_REQ_PACPORT_REG* msg = reinterpret_cast<SOCKET_MSG_REQ_PACPORT_REG*>(req_msg);

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParsePacportRegMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }
    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_STATUS) == 0)
                {
                    msg->status = ATOI(sub_node->GetText());
                }
            }
        }
    }

    return 0;
}

/*
<Msg>   
  <Type>ReportPacportCheckInfo</Type>
  <Params> 
      <AptNum>111</AptNum>   // 快递对应的房间号  长度：129字节
      <TrackingNum>12345</TrackingNum>   // 快递上条码编号 长度：64字节
      <CourierName>japanpost</CourierName>  //快递对应的快递公司名 长度：64字节
      <TraceID>1234567890</TraceID> //标识一次快递校验处理的完整链路 长度：64字节
  </Params>   
</Msg>
*/
int CMsgParseHandle::ParsePacportUnlockMsg(char *buf, void *req_msg)
{
    if (buf == nullptr || req_msg == nullptr)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }
    SOCKET_MSG_REQ_PACPORT_UNLOCK* msg = reinterpret_cast<SOCKET_MSG_REQ_PACPORT_UNLOCK*>(req_msg);

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParsePacportRegMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }
    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_APT_NUM) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->room_num, sizeof(msg->room_num) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_TRACKING_NUM) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->tracking_num, sizeof(msg->tracking_num) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_COURIER_NAME) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->courier_name, sizeof(msg->courier_name) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_TRACE_ID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->trace_id, sizeof(msg->trace_id) / sizeof(TCHAR));
                }
            }
        }
    }

    return 0;    
}

/*
<Msg>   
  <Type>ReportDeliveryMsg</Type>
  <Params>     
      <AptNum>1004</AptNum>   // 选择家庭时对应的房间号
      <BoxNum>5</BoxNum>   // 快递所在箱号
      <Status>0</Status>   // 存放/取出快递状态  0：存入  1：取出
  </Params>   
</Msg>
*/
int CMsgParseHandle::ParseRequestDeliveryMsg(char *buf, void *msg1)
{
    if (buf == NULL || msg1 == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }
    SOCKET_MSG_DEV_DOORCOM_DELIVERY_MSG* msg = reinterpret_cast<SOCKET_MSG_DEV_DOORCOM_DELIVERY_MSG*>(msg1);

    char text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseDoorcomDeliveryMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed";
        return -1;
    }
    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched" << XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for(sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_APT_NUM) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->apt_num, sizeof(msg->apt_num));
                }
                else if(strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_BOX_NUM) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->box_num, sizeof(msg->box_num));
                }
                else if(strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_STATUS) == 0)
                {
                    msg->status = ATOI(sub_node->GetText());
                }
            }
        }
    }
    return 0;
}


/*
<Msg>
  <Type>ReportTransferActivity</Type>
  <Protocal>2.0</Protocal>
  <Params>
    <Type>0</Type>                    // 开门类型,0:通话中开门
    <Seq>539679811</Seq>              // 用于ack
    <Response>0</Response>            // 开门结果
    <Initiator>6504100200</Initiator> // app sip(sip交互)
    <Relay>12</Relay>                 // 开了哪个relay
    <SecurityRelay>12</SecurityRelay> // 开了哪个sr
    <Location>front door</Location>   // 门口机名称
    <PicName>dv-dc746232884b11ee81bb00163e047e78-1704928988_0_DD_5f8790b5a8cfb5eb383b780a5d534dcb.jpg</PicName> // 室内机UUID-xxx
  </Params>
</Msg>
*/
int CMsgParseHandle::ParseReportTransActLogMsg(char *buf, void *msg1)
{
    if (buf == NULL || msg1 == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }
    SOCKET_MSG_DEV_REPORT_ACTIVITY* msg = reinterpret_cast<SOCKET_MSG_DEV_REPORT_ACTIVITY*>(msg1);

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReportTransActLogMsg text=" << text;
    
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }
    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_ALARM_TYPE) == 0)
                {
                    msg->act_type = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PIC_NAME) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->pic_name, sizeof(msg->pic_name) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_INITIATOR) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->initiator, sizeof(msg->initiator) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_RESP) == 0)
                {
                    msg->resp = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_SEQ) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->msg_seq, sizeof(msg->msg_seq) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_RELAY) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->relay, sizeof(msg->relay) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_SECURITY_RELAY) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->srelay, sizeof(msg->srelay) / sizeof(TCHAR));
                }       
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_LOCALTION) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->location, sizeof(msg->location) / sizeof(TCHAR));
                }               
            }
        }
    }

    return 0;
}


/*
<Msg>
    <Type>CreateRoom</Type>
    <Params>
    <MsgSeq>429496795</MsgSeq>
        <Item>
            <MAC>0A:02:03:20:01:17</MAC>
            <Location> XXX </Location>
            <Type> 1 </Type> 0:梯口机; 1:门口机; 2:室内机; 3:管理中心机; 4:围墙机; 5:SDMC; 50: 门禁
        </Item>
        <Item>
            <MAC>0A:02:03:20:01:18</MAC>
            <Location> XXX </Location>
            <Type> 1 </Type> 0:梯口机; 1:门口机; 2:室内机; 3:管理中心机; 4:围墙机; 5:SDMC; 50: 门禁
        </Item>
    </Params>
</Msg>
*/
int CMsgParseHandle::ParseRequestCreateRoomMsg(char *buf, void *msg1, std::string& msg_seq)
{
    if (buf == NULL || msg1 == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    std::vector<SOCKET_MSG_DEV_KIT_DEVICE>* kit_devices = reinterpret_cast<std::vector<SOCKET_MSG_DEV_KIT_DEVICE>*>(msg1);

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseRequestCreateRoomMsg text = " << text;

    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MSGSEQ) == 0)
                {
                    msg_seq = sub_node->GetText();
                    continue;
                }
                
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_ITEM) != 0)
                {
                    continue;
                }
                
                SOCKET_MSG_DEV_KIT_DEVICE kit_device;
                memset(&kit_device, 0, sizeof(kit_device));
                
                TiXmlElement* item_node = NULL;
                for (item_node = sub_node->FirstChildElement(); item_node; item_node = item_node->NextSiblingElement())
                {
                    if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_MAC) == 0)
                    {
                        TransUtf8ToTchar(item_node->GetText(), kit_device.mac, sizeof(kit_device.mac) / sizeof(TCHAR));
                    }
                    else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_LOCALTION) == 0)
                    {
                        std::string decode_location = URLDecode(item_node->GetText());
                        Snprintf(kit_device.location, sizeof(kit_device.location), decode_location.c_str());
                    }
                    else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
                    {
                        kit_device.type = ATOI(item_node->GetText());
                    }
                }
                kit_devices->push_back(kit_device);
            }
        }
    }

    return 0;
}

/*
<Msg>
    <Type>DeleteMac</Type>
    <Params>
        <MsgSeq>429496795</MsgSeq>
        <MAC>0A0203200117</MAC>
    </Params>
</Msg>
*/
int CMsgParseHandle::ParseRequestDelDeviceMsg(char *buf, void *msg1)
{
    if (buf == NULL || msg1 == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    SOCKET_MSG_DEL_KIT_DEVICE* msg = reinterpret_cast<SOCKET_MSG_DEL_KIT_DEVICE*>(msg1);

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseRequestDelDeviceMsg text = " << text;
    
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MAC) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->mac, sizeof(msg->mac) / sizeof(char));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MSGSEQ) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->msg_seq, sizeof(msg->msg_seq) / sizeof(char));
                }
            }
        }
    }

    return 0;
}

/*
<Msg>
    <Type>DeleteUser</Type>
    <Params>
        <MsgSeq>429496795</MsgSeq>
    </Params>
</Msg>
*/
int CMsgParseHandle::ParseRequestResetRoomMsg(char *buf, void *msg1)
{
    if (buf == NULL || msg1 == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    SOCKET_MSG_KIT_RESET_ROOM* msg = reinterpret_cast<SOCKET_MSG_KIT_RESET_ROOM*>(msg1);

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseRequestResetRoomMsg text = " << text;
    
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MSGSEQ) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->msg_seq, sizeof(msg->msg_seq) / sizeof(char));
                }
            }
        }
    }

    return 0;
}

/*
<Msg>
    <Type>DeleteRoom</Type>
    <Params>
        <MsgSeq>429496795</MsgSeq>
    </Params>
</Msg>
*/
int CMsgParseHandle::ParseRequestDelRoomMsg(char *buf, void *msg1)
{
    if (buf == NULL || msg1 == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    SOCKET_MSG_KIT_DEL_ROOM* msg = reinterpret_cast<SOCKET_MSG_KIT_DEL_ROOM*>(msg1);

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseRequestDelRoomMsg text = " << text;
    
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MSGSEQ) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->msg_seq, sizeof(msg->msg_seq) / sizeof(char));
                }
            }
        }
    }

    return 0;
}

/*
<Msg>   
	<Type>ReportRelay</Type>
	<Params>
	    <RelayStatus Type="xxx">123</RelayStatus> //开启的relay id列表, 外接relay:Type="Extern"
	</Params>
</Msg>
*/

int CMsgParseHandle::ParseReportIndoorRelayStatusMsg(char *buf, void *msg)
{
    if (buf == NULL || msg == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    SOCKET_MSG_DEV_REPORT_INDOOR_RELAY_STATUS *relay_status_msg = reinterpret_cast<SOCKET_MSG_DEV_REPORT_INDOOR_RELAY_STATUS*>(msg);

    char text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseIndoorRelayStatus text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed";
        return -1;
    }
    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }
    //主节点名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched" << XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_RELAY_STATUS) == 0)
                {
                    const char* relay_type = sub_node->Attribute(XML_NODE_ATTRIBUTE_TYPE);
                    if (relay_type)
                    {
                        if (strcmp(relay_type, XML_NODE_ATTRIBUTE_RELAY_TYPE_EXTERN) == 0)
                        {
                            relay_status_msg->relay_type = IndoorRelayType::TYPE_EXTERN;
                        }
                        else if (strcmp(relay_type, XML_NODE_ATTRIBUTE_RELAY_TYPE_LOCAL) == 0)
                        {
                            relay_status_msg->relay_type = IndoorRelayType::TYPE_LOCAL;
                        }
                    }
                    //兼容旧的接口,type为Local
                    else
                    {
                        relay_status_msg->relay_type = IndoorRelayType::TYPE_LOCAL;
                    }
                    TransUtf8ToTchar(sub_node->GetText(), relay_status_msg->relay_ids, sizeof(relay_status_msg->relay_ids));
                }
            }
        }
    }
    return 0;
}

int CMsgParseHandle::ParseResponseEmergencyControlMsg(char *buf, SOCKET_MSG_EMERGENCY_CONTROL *emergency_control_msg)
{
    if (buf == nullptr || emergency_control_msg == nullptr)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];

    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReportEmergencyControlMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MSGUUID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), emergency_control_msg->msg_uuid, sizeof(emergency_control_msg->msg_uuid));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_RELAY) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), emergency_control_msg->relay, sizeof(emergency_control_msg->relay));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_SECURITY_RELAY) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), emergency_control_msg->security_relay, sizeof(emergency_control_msg->security_relay));
                }
            }
        }
    }
    return 0;
}

/*
<Msg>
    <Type>Alarm</Type>
    <Protocal>1.0</Protocal>
    <Params>
        <Type>Normal</Type>
        <AlarmCode>15</AlarmCode>                   // 15 表示强闯告警
        <MsgSeq>0123233000</MsgSeq>                 // 10位数字
        <AlarmZone>xxxxxxx</AlarmZone>              // 告警区域
        <AlarmLocation>xxxxxxx</AlarmLocation>      // 字符串
        <AlarmCustomize>xxxxx</AlarmCustomize>      // 字符串
        <SpecificFields>                            //用来对AlarmCode的补充说明
            <Input></Input>                         // 常开或者强闯Input A\B\C\D，只能单个。云再转为对应的relay。
        </SpecificFields>
    </Params>
</Msg>
*/
int CMsgParseHandle::ParseAlarmMsg(char *buf, SOCKET_MSG_ALARM *alarm_msg)
{
    if (buf == nullptr || alarm_msg == nullptr)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    AK_LOG_INFO << "ParseAlarmMsg \n" << buf;
    memset(alarm_msg, 0, sizeof(SOCKET_MSG_ALARM));

    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }
    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " << XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
        {
            //暂不需要处理
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
        {
            TransUtf8ToTchar(node->GetText(), alarm_msg->protocal, sizeof(alarm_msg->protocal) / sizeof(TCHAR));
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_TYPE) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), alarm_msg->type, sizeof(alarm_msg->type) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MSGSEQ) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), alarm_msg->msg_seq, sizeof(alarm_msg->msg_seq) / sizeof(TCHAR));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_ALARMCODE) == 0)
                {
                    alarm_msg->alarm_code = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_ALARMZONE) == 0)
                {
                    alarm_msg->alarm_zone = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_ALARMLOCATION) == 0)
                {
                    alarm_msg->alarm_location = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_ALARMCUSTOMIZE) == 0)
                {
                    alarm_msg->alarm_customize = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_SPECIFIC_FIELDS) == 0)
                {
                    TiXmlElement* sub_sub_node = NULL;
                    for (sub_sub_node = sub_node->FirstChildElement(); sub_sub_node; sub_sub_node = sub_sub_node->NextSiblingElement())
                    {
                        if (strcmp(sub_sub_node->Value(), XML_NODE_NAME_MSG_PARAM_INPUT) == 0)
                        {
                            TransUtf8ToTchar(sub_sub_node->GetText(), alarm_msg->input, sizeof(alarm_msg->input) / sizeof(TCHAR));
                        }
                    }
                }
            }
        }
    }

    return 0;
}


/*
<Msg>
  <Type>DealAlarm</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <AlarmID>00000042949</AlarmID> (alarmid， 11位数字)
     <user>XXX</user>        (告警的处理人，室内机可以先填写本身的地址节点，eg:*******.1-2)
     <Result>XXX</Result>    (告警的处理内容)
     <Type>XXX</Type>        (告警的处理类型)
     <Time>YYYY-MM-HH DD:HH:SS</Time>   (告警的处理时间)
  </Params>
</Msg>
*/
int CMsgParseHandle::ParseAlarmDealMsg(char* buf, SOCKET_MSG_ALARM_DEAL* alarm_deal_info)
{
    if ((buf == NULL) || (alarm_deal_info == NULL))
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];

    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseAlarmDealMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
        {
            if (strncmp(node->GetText(), XML_NODE_NAME_MSG_TYPE_PUT_ALARM_DEAL, strlen(XML_NODE_NAME_MSG_TYPE_PUT_ALARM_DEAL)) != 0)
            {
                //AK_LOG_WARN << "Type Node must be %s, but it is %s"), __FUNCTIONW__, XML_NODE_NAME_MSG_TYPE_PUT_ALARM_DEAL, node->GetText());
                //return -1;
            }
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
        {
            TransUtf8ToTchar(node->GetText(), alarm_deal_info->protocal, sizeof(alarm_deal_info->protocal));
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_ALARM_ID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), alarm_deal_info->alarm_id, sizeof(alarm_deal_info->alarm_id));
                }
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_ALARM_DEAL_USER) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), alarm_deal_info->user, sizeof(alarm_deal_info->user));
                }
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_ALARM_DEAL_RESULT) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), alarm_deal_info->result, sizeof(alarm_deal_info->result));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_ALARM_TYPE) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), alarm_deal_info->type, sizeof(alarm_deal_info->type));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_ALARM_TIME) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), alarm_deal_info->time, sizeof(alarm_deal_info->time));
                }
            }
        }
    }

    return 0;
}

/*
<Msg>
  <Type>CheckVisitorIDAccess</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <IDAccessRun>0101010211</IDAccessRun>
    <IDAccessSerial>1101010102</IDAccessSerial>
    <MsgSeq>429496795</MsgSeq>
  </Params>
</Msg>
*/
int CMsgParseHandle::ParseCheckIDAccessMsg(char *buf, void *msg)
{
    if (buf == NULL || msg == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    SOCKET_MSG_REPORT_CHECK_ID_ACCESS *id_access_check_msg = reinterpret_cast<SOCKET_MSG_REPORT_CHECK_ID_ACCESS*>(msg);

    char text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseCheckIDAccessMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed";
        return -1;
    }
    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }
    //主节点名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched" << XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
        {
            TransUtf8ToTchar(node->GetText(), id_access_check_msg->protocal, sizeof(id_access_check_msg->protocal) / sizeof(char));
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_ID_ACCESS_RUN) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), id_access_check_msg->id_access_run, sizeof(id_access_check_msg->id_access_run) / sizeof(char));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_ID_ACCESS_SERIAL) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), id_access_check_msg->id_access_serial, sizeof(id_access_check_msg->id_access_serial) / sizeof(char));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MSGSEQ) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), id_access_check_msg->msg_seq, sizeof(id_access_check_msg->msg_seq) / sizeof(char));
                }
            }
        }
    }

    return 0;
}

/*
个人终端用户设备motion alert上报
<Msg>
  <Type>MotionAlert</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <AlertID>042949</AlertID> (Alertid， 由设备递增，最大11位数字)
     <PicName>0C110000000_10256546_1.jpg</PicName> (eg:0C1100000001_1513232303_0.jpg)
     <DetectionType>0</DetectionType> 0:移动侦测1:包裹检测2声音检测,没这个类型时都为移动侦测
     <DetectionInfo>0</DetectionInfo> 侦测类型为1时，这里的0包裹放入，1包裹放出；侦测类型为2时，这里的0枪声 1狗叫声 2孩子哭声 3玻璃破碎 4警笛
  </Params>
</Msg>
*/

int CMsgParseHandle::ParseMotionAlertMsg(char *buf, void *msg)
{
    if (buf == NULL || msg == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    SOCKET_MSG_MOTION_ALERT *motion_alert_msg = reinterpret_cast<SOCKET_MSG_MOTION_ALERT*>(msg);

    char text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseMotionAlertMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed";
        return -1;
    }
    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }
    //主节点名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched" << XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PIC_NAME) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), motion_alert_msg->picture_name, sizeof(motion_alert_msg->picture_name));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_VIDEO_RECORD_NAME) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), motion_alert_msg->video_record_name, sizeof(motion_alert_msg->video_record_name));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_DETECTION_TYPE) == 0)
                {
                    motion_alert_msg->detection_type = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_DETECTION_INFO) == 0)
                {
                    motion_alert_msg->detection_info = ATOI(sub_node->GetText());
                }
            }
        }
    }

    return 0;
}

/*
<Msg>
<Type>ReportActivity</Type>
    <Protocal>2.0</Protocal>
    <Params>
        <Type>5</Type>
        <PicName>0A0203200117-1616054761_0_DoorDev_4a2c83e2af7e5d4ea8e469328b2db0d9.jpg</PicName>
        <Initiator>6504100200</Initiator>
        <Response>0</Response>
        <DepartMode>0</DepartMode>
        <AccessMode>1</AccessMode>                          //0-Normal, 1-Entry, 2-Exit, 3-Entry Violation(违规进入), 4-Exit Violation(违规出去)。若没配置，则认定为0；
        <SecurityRelay "EntryMode"="1">1</SecurityRelay>    //开了哪个relay, EntryMode0代表out 1代表in，多个relay时候拼接起来
        <Relay "EntryMode"="010">134</Relay>                //开了哪个relay, EntryMode0代表out 1代表in，多个relay时候拼接起来
        <PerID>6504100200</PerID>            
        <Time>1677783783</Time>               
    </Params>
</Msg>
*/
int CMsgParseHandle::ParseReportActMsg(char* buf, void *msg)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }
    
    SOCKET_MSG_DEV_REPORT_ACTIVITY *act_msg = reinterpret_cast<SOCKET_MSG_DEV_REPORT_ACTIVITY*>(msg);

    TCHAR text[4096];

    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReportActMsg text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_ALARM_TYPE) == 0)
                {
                    char type[8] = {0};
                    TransUtf8ToTchar(sub_node->GetText(), type, sizeof(type));
                    act_msg->act_type = ATOI(type);
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PIC_NAME) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), act_msg->pic_name, sizeof(act_msg->pic_name));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_INITIATOR) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), act_msg->initiator, sizeof(act_msg->initiator));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_RESP) == 0)
                {
                    char szResp[2] = {0};
                    TransUtf8ToTchar(sub_node->GetText(), szResp, sizeof(szResp));
                    act_msg->resp = ATOI(szResp);
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PER_ID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), act_msg->per_id, sizeof(act_msg->per_id));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_SEQ) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), act_msg->msg_seq, sizeof(act_msg->msg_seq));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_RELAY) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), act_msg->relay, sizeof(act_msg->relay));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_SECURITY_RELAY) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), act_msg->srelay, sizeof(act_msg->srelay));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_ACCESS_MODE) == 0)
                {
                    act_msg->access_mode = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_RELAY_ENTRY_MODE) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), act_msg->relay_entry_mode, sizeof(act_msg->relay_entry_mode));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_SECURITY_RELAY_ENTRY_MODE) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), act_msg->security_relay_entry_mode, sizeof(act_msg->security_relay_entry_mode));
                }
            }
        }
    }

    // 不是返潜回类型的开门, 把entryMode转换成accessMode
    if (act_msg->access_mode == (int)AntiPassbackDoorType::NORMAL)
    {
        if (strlen(act_msg->relay_entry_mode) > 0)
        {
            // 先取第一个relay的entry mode, 当已知问题
            int relay_entry_mode = ATOI(std::string(1, *act_msg->relay_entry_mode).c_str());
            act_msg->access_mode = relay_entry_mode == (int)ActLogRelayEntryMode::EXIT ? (int)AntiPassbackDoorType::EXIT : (int)AntiPassbackDoorType::ENTRY;
            return 0;
        }
        
        if (strlen(act_msg->security_relay_entry_mode) > 0)
        {
            // 先取第一个security relay的entry mode, 当已知问题
            int security_relay_entry_mode = ATOI(std::string(1, *act_msg->security_relay_entry_mode).c_str());
            act_msg->access_mode = security_relay_entry_mode == (int)ActLogRelayEntryMode::EXIT ? (int)AntiPassbackDoorType::EXIT : (int)AntiPassbackDoorType::ENTRY;
            return 0;
        }
    }
    
    return 0;
}

/*
<Msg>
  <Type>CheckTmpKey</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <TmpKey>01010102</TmpKey>
    <MsgSeq>429496795</MsgSeq>
  </Params>
</Msg>
*/
int CMsgParseHandle::ParseCheckTmpKeyMsg(char* buf, void *msg)
{
    if ((buf == NULL) || (msg == NULL))
    {
        AK_LOG_WARN << "ParseCheckTmpKeyMsg Input Param is NULL";
        return -1;
    }

    SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY *tmpkey_info = reinterpret_cast<SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY*>(msg);

    TCHAR text[4096];

    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "CheckTmpKeyMsg text=" << text;
    
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
        {
            if (strncmp(node->GetText(), XML_NODE_NAME_MSG_TYPE_CHECK_TMPKEY, strlen(XML_NODE_NAME_MSG_TYPE_CHECK_TMPKEY)) != 0)
            {
                //AK_LOG_WARN << "Type Node must be %s, but it is %s"), __FUNCTIONW__, XML_NODE_NAME_MSG_TYPE_CHECK_TMPKEY, node->GetText());
                //return -1;
            }
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PROTOCAL) == 0)
        {
            TransUtf8ToTchar(node->GetText(), tmpkey_info->protocal, sizeof(tmpkey_info->protocal));
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_TMPKEY) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), tmpkey_info->tmpkey, sizeof(tmpkey_info->tmpkey));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MSGSEQ) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), tmpkey_info->msg_seq, sizeof(tmpkey_info->msg_seq));
                }
            }
        }
    }

    return 0;
}

/*
<Msg>
  <Type>RequestAntipassbackOpenDoor</Type>
  <Params>
    <TraceID>1234567890</TraceID>       // 消息ID，32位，用来唯一标识一次请求
    <PersonnelID>123456</PersonnelID>   // User文件PerID
    <AccessMode>1</AccessMode>          // 访问模式：0-Normal，1-Entry，2-Exit
    <Type>1</Type>                      // 开门方式 (同ReportActivity) 1=tmpkey 2=pin ......
    <Relay>1</Relay>                               // 开了那个relay 
    <SecurityRelay></Relay>                    // 开了那个security relay    
  </Params>
</Msg>
*/
int CMsgParseHandle::ParseRequestAntipassbackOpenDoorMsg(char *buf, void *raw_msg)
{
    if (buf == NULL || raw_msg == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    SOCKET_MSG_REQ_ANTI_PASSBACK_OPEN* msg = reinterpret_cast<SOCKET_MSG_REQ_ANTI_PASSBACK_OPEN*>(raw_msg);

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseRequestAntipassbackOpenDoorMsg \n " << text;
    
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_TRACE_ID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->trace_id, sizeof(msg->trace_id) / sizeof(char));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PERSONNEL_ID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), msg->personnel_id, sizeof(msg->personnel_id) / sizeof(char));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_ACCESS_MODE) == 0)
                {
                    msg->access_mode = AntiPassbackAccessMode(ATOI(sub_node->GetText()));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_ATTRIBUTE_TYPE) == 0)
                {
                    msg->act_type = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_RELAY) == 0)
                {
                    msg->relay = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_SECURITY_RELAY) == 0)
                {
                    msg->security_relay = ATOI(sub_node->GetText());
                }
            }
        }
    }

    return 0;
}

/*
<Msg>
  <Params>
     <PicName>0C110000000-10256546_1_CALL.jpg</PicName> // (长度64) (图片名称统一用，eg:0C1100000001-1513232303_0_CALL.jpg)
     <DepartmentUUID>dv-abccd....</DepartmentUUID>      // 群呼部门时候填写联系人里面Department UUID, Callee这时候可以放空。
     <DailOut>1</DailOut>                               // 呼出=1  呼入=0
     <Caller>712012511</Caller>                         // (长度32) 主叫SIP: 呼出=自己的sip 呼入=对方sip
     <Callee>712012000</Callee>                         // (长度32) 被叫SIP: 呼出=对方sip(是自己真实呼出号码，不管Remote-party-id), 呼入=自己sip
     <CallTraceID>sip账号+ timestamp(10位) + randstring(10位)</CallTraceID>//呼叫唯一id
                                                        // 7.0.0修改：群呼时候取group node标识传给云，单呼逻辑不变 取对应的UID，这个对住宅是一样的修改。
  </Params>
</Msg>
*/
int CMsgParseHandle::ParseReportCallCaptureMsg(char* buf, void* msg)
{
    if (buf == NULL || msg == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseCallCaptureMsg text=" << text;

    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " << XML_NODE_NAME_MSG;
        return -1;
    }

    SOCKET_MSG_CALL_CAPTURE* call_capture = reinterpret_cast<SOCKET_MSG_CALL_CAPTURE*>(msg);
    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {

        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PIC_NAME) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), call_capture->picture_name, sizeof(call_capture->picture_name));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_CALLER) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), call_capture->caller, sizeof(call_capture->caller));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_CALLEE) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), call_capture->callee, sizeof(call_capture->callee));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_CALL_TRACE_ID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), call_capture->call_trace_id, sizeof(call_capture->call_trace_id));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_DEPARTMENT_UUID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), call_capture->department_uuid, sizeof(call_capture->department_uuid));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_DAIL_OUT) == 0)
                {
                    char tmp[8] = "";
                    TransUtf8ToTchar(sub_node->GetText(), tmp, sizeof(tmp));
                    call_capture->dialog_out = ATOI(tmp);
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_VIDEO_RECORD_NAME) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), call_capture->video_record_name, sizeof(call_capture->video_record_name));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_INDOORS) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), call_capture->indoor_mac_list, sizeof(call_capture->indoor_mac_list));
                }
            }
        }
    }

    return 0;
}

/*
<Msg>
  <Type>FlowOutOfLimit</Type>
  <Protocal>1.0</Protocal>
  <Params>
    <Percent>80</Percent>   // 使用了百分多少
    <Limit>40</Limit>       // 流量总额度多少   如40G
  </Params>
</Msg>
*/
int CMsgParseHandle::ParseReportSimCardFlowLimitMsg(char *buf, void *raw_msg)
{
    if (buf == NULL || raw_msg == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    SOCKET_MSG_FLOW_OUT_LIMIT* msg = reinterpret_cast<SOCKET_MSG_FLOW_OUT_LIMIT*>(raw_msg);

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReportSimCardFlowLimitMsg \n " << text;
    
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_PERCENT) == 0)
                {
                    msg->percent = atof(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_LIMIT) == 0)
                {
                    msg->limit = atoll(sub_node->GetText());
                }
            }
        }
    }

    return 0;
}

/*
<Msg>
<Type>ReportTimeZone</Type>
    <Params>
        <TimeZone>+0:00 Abidjan</TimeZone>
    </Params>
</Msg>
*/
int CMsgParseHandle::ParseReportTimeZoneMsg(char *buf, std::string& timezone)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReportTimeZoneMsg \n " << text;
    
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_TIMEZONE) == 0)
                {
                    timezone = sub_node->GetText();
                }
            }
        }
    }

    return 0;
}
