#ifndef __MSG_PARSE_H__
#define __MSG_PARSE_H__
#include <string>
#include <map>
#include <vector>
#include "DclientMsgDef.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
//#include "Office2MainHandle.h"
#include "InnerMsgDef.h"
#include "dbinterface/PersonalVoiceMsg.h"
#include "DclientMsgSt.h"
#include "dbinterface/AntiPassbackDoor.h"
#include "AkcsCommonDef.h"

class CMsgParseHandle
{
public:
    CMsgParseHandle();
    ~CMsgParseHandle();
    int ParseReportStatusMsg(char* buf, SOCKET_MSG_REPORT_STATUS* report_status_message, uint32_t data_size, uint32_t version);
    //语音留言上传
    static int ParseReportVoiceMsg(char *buf, void *msg);
    static int ParseRequestVoiceMsgList(char *buf, void *msg);
    static int ParseRequestVoiceMsgUrl(char *buf, void *msg);
    static int ParseRequestDelVoiceMsg(char *buf, void *msg);
    static int ProcessAppReportStatusMsg(SOCKET_MSG_NORMAL* pNormalMsg, SOCKET_MSG_PERSONNAL_APP_CONF& app_config);
    static int ParseAppReportStatusMsg(char* buf, SOCKET_MSG_PERSONNAL_APP_CONF* personnal_info);

    //relay status解析
    static int ParseReportRelayStatusMsg(char *buf, void *msg);
    static int ParseRequestWeatherMsg(char *buf, void *msg);

    static int ParseRequestDeliveryMsg(char *buf, void *msg);
    static int ParsePacportRegMsg(char *buf, void *req_msg);
    static int ParsePacportUnlockMsg(char *buf, void *req_msg);

    // hager kit
    static int ParseRequestCreateRoomMsg(char *buf, void *msg, std::string& msg_seq);
    static int ParseRequestDelDeviceMsg(char *buf, void *msg);
    static int ParseRequestDelRoomMsg(char *buf, void *msg);
    static int ParseRequestResetRoomMsg(char *buf, void *msg);
    static int ParseReportTransActLogMsg(char *buf, void *msg);
    static int ParseReportIndoorRelayStatusMsg(char *buf, void *msg);
    static int ProcessAppRequestChangeRelayMsg(SOCKET_MSG_NORMAL* pNormalMsg, SOCKET_MSG_APP_REQUEST_CHANGE_RELAY& change_relay_msg);
    static int ParseRequestChangeRelayStatusMsg(char *buf, void *msg);
    static int ParseRequestAntipassbackOpenDoorMsg(char *buf, void *raw_msg);
    static int ParseResponseEmergencyControlMsg(char *buf, SOCKET_MSG_EMERGENCY_CONTROL *emergency_control_msg);
    static int ParseAlarmMsg(char *buf, SOCKET_MSG_ALARM *alarm_msg);
    static int ParseAlarmDealMsg(char* buf, SOCKET_MSG_ALARM_DEAL* alarm_deal_info);
    static int ParseCheckIDAccessMsg(char *buf, void *msg);
    static int ParseMotionAlertMsg(char *buf, void* msg);
    static int ParseReportActMsg(char* buf, void *msg);
    static int ParseCheckTmpKeyMsg(char* buf, void *msg);
    static int ParseReportSimCardFlowLimitMsg(char *buf, void *msg);


    // 截图消息
    static int ParseReportCallCaptureMsg(char *buf, void *msg);
    static int ParseReportTimeZoneMsg(char *buf, std::string& timezone);
    static CMsgParseHandle* GetInstance();
private:

    static CMsgParseHandle* instance;

};

CMsgParseHandle* GetMsgParseHandleInstance();

#endif

