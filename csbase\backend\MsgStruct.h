#ifndef __MSG_STRUCT_H__
#define __MSG_STRUCT_H__

#include "DclientMsgSt.h"

struct MsgHelper
{
    const static uint32_t kMsgSize = 5400;
    long  msg_type;
    char data[kMsgSize];
};

struct MsgStruct
{
    csmain::DeviceType  client_type;
    char client[64];    // 根据send_type类型, 表示客户端, Eg: mac地址/app的uid/设备uuid/用户UUID
    char outer_ip[40];
    int outer_port;
    bool conn_change;   // 是否有连接信息变化(ipv6/ipv4/动态iv)
    uint64_t traceid;
    uint32_t msg_len;
    char msg_data[sizeof(SOCKET_MSG_NORMAL)];
    
    TransP2PMsgType send_type;
    MsgEncryptType enc_type;
    int msg_id;    
    int force_push;     // 是否强制走推送，正常情况下只有app离线才推
    uint32_t push_msg_len;
    char push_msg_data[1024];
};

#endif
