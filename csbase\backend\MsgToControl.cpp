#include <stdlib.h>
#include <stdio.h>
#include <string>
#include <tuple>
#include "util.h"
#include "MsgToControl.h"
#include "MsgParse.h"
#include "MsgBuild.h"
#include "MsgControl.h"
#include "ClientControl.h"


CMsgToControl* GetMsgToControlInstance()
{
    return CMsgToControl::GetInstance();
}

CMsgToControl::CMsgToControl()
{

}

CMsgToControl::~CMsgToControl()
{

}

CMsgToControl* CMsgToControl::instance = NULL;

CMsgToControl* CMsgToControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CMsgToControl();
    }

    return instance;
}


int CMsgToControl::SendOnlineNotifyMsg(const std::string& mac, const SOCKET_MSG_DEV_ONLINE_NOTIFY &online_msg)
{
    //组装消息
    SOCKET_MSG socket_message;
    memset(&socket_message, 0, sizeof(socket_message));
    if (GetMsgBuildHandleInstance()->BuildOnlineNotifyMsg(&socket_message, online_msg, mac) != 0)
    {
        AK_LOG_WARN << "BuildOnlineNotifyMsg failed";
        return -1;
    }

    ResidentDev dev;
    GET_DEV_SETTING(mac, dev);
    
    if (GetClientControlInstance()->SendTransferMsg(mac, dev.conn_type, socket_message.data, socket_message.size) < 0)
    {
        AK_LOG_WARN << "Send OnlineNotifyMsg to dev failed.";
        return -1;
    }
    return 0;
}

int CMsgToControl::SendIsKit(const std::string& mac)
{
    SOCKET_MSG socket_message;
    memset(&socket_message, 0, sizeof(socket_message));
    if (GetMsgBuildHandleInstance()->BuildSendKitMsg(socket_message, mac) != 0)
    {
        AK_LOG_WARN << "BuildOnlineNotifyMsg failed";
        return -1;
    }

    ResidentDev dev;
    GET_DEV_SETTING(mac, dev);

    if (GetClientControlInstance()->SendTransferMsg(mac, dev.conn_type, socket_message.data, socket_message.size) < 0)
    {
        AK_LOG_WARN << "Send SendIsKit to dev failed.";
        return -1;
    }
    return 0;
}

int CMsgToControl::SendVoiceMsgUrl(const std::string &mac,const std::string &mac_uuid, const std::string &uuid, const std::string &url)
{
    SOCKET_MSG_DEV_VOICE_MSG_URL url_msg;
    memset(&url_msg, 0, sizeof(url_msg));
    snprintf(url_msg.uuid, sizeof(url_msg.uuid), "%s", uuid.c_str());
    snprintf(url_msg.url, sizeof(url_msg.url), "%s", url.c_str());
    
    //组装消息
    SOCKET_MSG socket_message;
    memset(&socket_message, 0, sizeof(socket_message));
    if (GetMsgBuildHandleInstance()->BuildVoiceMsgUrlNotifyMsg(&socket_message, url_msg, mac) != 0)
    {
        AK_LOG_WARN << "BuildVoiceMsgListNotifyMsg failed";
        return -1;
    }

    ResidentDev dev;
    GET_DEV_SETTING(mac, dev);

    if (GetClientControlInstance()->SendTransferMsg(mac, dev.conn_type, socket_message.data, socket_message.size) < 0)
    {
        AK_LOG_WARN << "Send SendVoiceMsgUrl to dev failed.";
        return -1;
    }

    //更新已读未读状态
    if (0 != dbinterface::PersonalVoiceMsg::UpdateVoiceMsgStatus(url_msg.uuid, mac_uuid))
    {
        AK_LOG_WARN << "UpdateVoiceMsgStatus failed";
        return -1;
    }    
    return 0;
}

int CMsgToControl::SendCommonAckMsg(uint16_t msg_id, const SOCKET_MSG_COMMON_ACK &common_ack)
{    
    SOCKET_MSG socket_message;
    memset(&socket_message, 0, sizeof(socket_message));
    if (GetMsgBuildHandleInstance()->BuildCommonAckMsg(&socket_message, msg_id, common_ack) != 0)
    {
        AK_LOG_WARN << "SendCommonAckMsg failed";
        return -1;
    }

    ResidentDev dev;
    GET_DEV_SETTING(common_ack.mac, dev);
    
    if (GetClientControlInstance()->SendTransferMsg(common_ack.mac, dev.conn_type, socket_message.data, socket_message.size) < 0)
    {
        AK_LOG_WARN << "SendCommonAckMsg failed.";
        return -1;
    }
    return 0;    
}

int CMsgToControl::SendDevWeatherInfoMsg(uint16_t msg_id, const SOCKET_MSG_DEV_WEATHER_INFO &weather_info)
{    
    SOCKET_MSG socket_message;
    memset(&socket_message, 0, sizeof(socket_message));
    
    if (GetMsgBuildHandleInstance()->BuildWeatherInfoMsg(&socket_message, msg_id, weather_info) != 0)
    {
        AK_LOG_WARN << "SendDevWeatherInfoMsg failed";
        return -1;
    }

    ResidentDev dev;
    GET_DEV_SETTING(weather_info.mac, dev);
    
    if (GetClientControlInstance()->SendTransferMsg(weather_info.mac, dev.conn_type, socket_message.data, socket_message.size) < 0)
    {
        AK_LOG_WARN << "SendDevWeatherInfoMsg failed.";
        return -1;
    }
    return 0;    
}

int CMsgToControl::SendHagerCreateRoomAck(uint16_t msg_id, const std::string& mac, const std::string& msg_seq)
{
    SOCKET_MSG_COMMON_SEQ_ACK ack;
    memset(&ack, 0, sizeof(ack));
    ack.msg_id = msg_id;
    Snprintf(ack.mac, sizeof(ack.mac), mac.c_str());
    Snprintf(ack.msg_seq, sizeof(ack.msg_seq), msg_seq.c_str());

    SOCKET_MSG socket_message;
    memset(&socket_message, 0, sizeof(socket_message));
    if (GetMsgBuildHandleInstance()->BuildCreateRoomAckMsg(&socket_message, msg_id, ack) != 0)
    {
       AK_LOG_WARN << "BuildCreateRoomAckMsg failed";
       return -1;
    }
    
    if (GetClientControlInstance()->SendTransferMsg(mac, csmain::DeviceType::PERSONNAL_DEV, socket_message.data, socket_message.size) < 0)
    {
        AK_LOG_WARN << "SendHagerCreateRommAck failed.";
        return -1;
    }
    return 0;
}


//通知app刷新Userconf
int CMsgToControl::SendDevListChangeMsg(uint16_t msg_id, const std::string& uid)
{    
    SOCKET_MSG socket_message;
    memset(&socket_message, 0, sizeof(socket_message));
    
    if (GetMsgBuildHandleInstance()->BuildDevListChangeMsg(&socket_message, msg_id) != 0)
    {
        AK_LOG_WARN << "SendDevListChangeMsg failed";
        return -1;
    }

    //TODO: 这里是住宅调用的，默认就用社区的
    if (GetClientControlInstance()->SendTransferMsg(uid, csmain::DeviceType::COMMUNITY_APP, socket_message.data, socket_message.size) < 0)
    {
        AK_LOG_WARN << "SendDevListChangeMsg failed.";
        return -1;
    }
    return 0;    
}


//通知app刷新Userconf
int CMsgToControl::SendOfficeDevListChangeMsg(uint16_t msg_id, const std::string& uid)
{    
    SOCKET_MSG socket_message;
    memset(&socket_message, 0, sizeof(socket_message));
    
    if (GetMsgBuildHandleInstance()->BuildDevListChangeMsg(&socket_message, msg_id) != 0)
    {
        AK_LOG_WARN << "SendDevListChangeMsg failed";
        return -1;
    }
    
    if (GetClientControlInstance()->SendTransferMsg(uid, csmain::DeviceType::OFFICE_APP, socket_message.data, socket_message.size) < 0)
    {
        AK_LOG_WARN << "SendDevListChangeMsg failed.";
        return -1;
    }
    return 0;    
}


int CMsgToControl::SendDevPacportUnlockResMsg(uint16_t msg_id, const SOCKET_MSG_PACPORT_UNLOCK_RES &unlock_info)
{
    //消息组装
    SOCKET_MSG socket_message;
    memset(&socket_message, 0, sizeof(socket_message));
    
    if (GetMsgBuildHandleInstance()->BuildPacportUnlockResMsg(&socket_message, msg_id, unlock_info) != 0)
    {
        AK_LOG_WARN << "SendDevListChangeMsg failed";
        return -1;
    }

    ResidentDev dev;
    GET_DEV_SETTING(unlock_info.mac, dev);
    
    if (GetClientControlInstance()->SendTransferMsg(unlock_info.mac, dev.conn_type, socket_message.data, socket_message.size) < 0)
    {
        AK_LOG_WARN << "SendDevWeatherInfoMsg failed.";
        return -1;
    }
    return 0; 
}

int CMsgToControl::SendHagerDevIsKitPlanMsg(uint16_t msg_id, const std::string& mac)
{
    SOCKET_MSG socket_message;
    memset(&socket_message, 0, sizeof(socket_message));
    
    if (GetMsgBuildHandleInstance()->BuildHagerSendKitMsg(&socket_message, mac) != 0)
    {
        AK_LOG_WARN << "SendHagerDevIsKitPlanMsg failed";
        return -1;
    }
    
    if (GetClientControlInstance()->SendTransferMsg(mac, csmain::DeviceType::PERSONNAL_DEV, socket_message.data, socket_message.size) < 0)
    {
        AK_LOG_WARN << "SendHagerDevIsKitPlanMsg failed.";
        return -1;
    }
    return 0;    
}

