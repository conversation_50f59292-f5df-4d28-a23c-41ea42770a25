#ifndef __MSG_TO_CONTROL_H__
#define __MSG_TO_CONTROL_H__

#include "dbinterface/PersonalVoiceMsg.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "DclientMsgSt.h"

//class evpp::Any;
class CMsgToControl
{

public:
    CMsgToControl();
    ~CMsgToControl();
    static CMsgToControl* GetInstance();

    int SendOnlineNotifyMsg(const std::string& mac, const SOCKET_MSG_DEV_ONLINE_NOTIFY &online_msg);
    int SendIsKit(const std::string& mac);
    int SendVoiceMsgUrl(const std::string &mac,const std::string &mac_uuid, const std::string &uuid, const std::string &url);
    int SendCommonAckMsg(uint16_t msg_id, const SOCKET_MSG_COMMON_ACK &common_ack);
    int SendDevWeatherInfoMsg(uint16_t msg_id, const SOCKET_MSG_DEV_WEATHER_INFO &weather_info);
    int SendHagerCreateRoomAck(uint16_t msg_id, const std::string& mac, const std::string& msg_seq);
    int SendHagerDevIsKitPlanMsg(uint16_t msg_id, const std::string& mac);
    int SendDevListChangeMsg(uint16_t msg_id, const std::string& uid);
    int SendDevPacportUnlockResMsg(uint16_t msg_id, const SOCKET_MSG_PACPORT_UNLOCK_RES &unlock_info);


    int SendOfficeDevListChangeMsg(uint16_t msg_id, const std::string& uid);    
private:

private:
    static CMsgToControl* instance;
};

CMsgToControl* GetMsgToControlInstance();

#endif

