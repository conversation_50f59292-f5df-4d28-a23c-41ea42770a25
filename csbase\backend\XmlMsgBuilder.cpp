#include "XmlMsgBuilder.h"

XmlBuilder::XmlBuilder(const std::string& msg_type_name, const std::string& protocal) 
{
    root_element_ = new TiXmlElement("Msg");
    xml_doc_.LinkEndChild(root_element_);

    TiXmlElement* key_element = new TiXmlElement("Type");
    key_element->LinkEndChild(new TiXmlText(msg_type_name.c_str()));
    root_element_->LinkEndChild(key_element); // 添加到 Params 节点下

    if(protocal.size() > 0)
    {
        //添加protocal节点
        TiXmlElement* key_element = new TiXmlElement("Protocal");
        key_element->LinkEndChild(new TiXmlText(protocal.c_str()));
        root_element_->LinkEndChild(key_element);
    }

    params_element_ = new TiXmlElement("Params"); // 初始化 Params 节点
    root_element_->LinkEndChild(params_element_);
}

void XmlBuilder::AddKeyValue(const std::map<std::string, std::string>& map) 
{
    for (const auto& pair : map)
    {
        TiXmlElement* sub_element = new TiXmlElement(pair.first.c_str());
        TiXmlText* sub_text = new TiXmlText(pair.second.c_str());
        sub_element->LinkEndChild(sub_text);
        params_element_->LinkEndChild(sub_element); // 添加到 Params 节点下
    }
}

void XmlBuilder::AddKeyListValue(const std::string &parent_key, const std::string &key, const std::map<std::string/*value*/, std::map<std::string, std::string> /*attr*/>& attr_map)
{
    TiXmlElement* param_item_element = new TiXmlElement(parent_key.c_str());
    params_element_->LinkEndChild(param_item_element); // 添加到 Params 节点下
    for (const auto& pair : attr_map)
    {
        TiXmlElement* param_item_node = new TiXmlElement(key.c_str());
        param_item_node->LinkEndChild(new TiXmlText(pair.first.c_str())); 
        const auto& node_attrs = pair.second;
        for (const auto& attrPair : node_attrs)
        {
            param_item_node->SetAttribute(attrPair.first.c_str(), attrPair.second.c_str()); 
        }
        param_item_element->LinkEndChild(param_item_node); 
    }
}

std::string XmlBuilder::GenerateXML()
{
    TiXmlPrinter printer;
    xml_doc_.Accept(&printer);
    return printer.CStr();
}
