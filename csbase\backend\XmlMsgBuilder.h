#ifndef __XML_MSG_BUILDER_H__
#define __XML_MSG_BUILDER_H__
#include <list>
#include <string>
#include <map>
#include <iostream>
#include <unordered_map>
#include <map>
#include "tinyxml.h"

class XmlBuilder {
private:
    TiXmlDocument xml_doc_;
    TiXmlElement* root_element_;
    TiXmlElement* params_element_; // 新增一个成员变量

public:
    XmlBuilder(const std::string& msg_type_name, const std::string& protocal = "");

    // void AddProtocal(const std::string& protocal);

    void AddKeyValue(const std::map<std::string, std::string>& map);

    void AddKeyListValue(const std::string &parent_key, const std::string &key, const std::map<std::string/*value*/, std::map<std::string, std::string> /*attr*/>& attr_map);

    std::string GenerateXML();

    TiXmlElement* GetParamsElement() { return params_element_; }

    //不需要delete, 不会内存泄漏
    ~XmlBuilder(){};
};


#endif //__XML_MSG_BUILDER_H__


