#ifndef __PARSE_DEV_REQUEST_OPEN_H__
#define __PARSE_DEV_REQUEST_OPEN_H__

#include "util.h"
#include "tinystr.h"
#include "tinyxml.h"
#include "CharChans.h"
#include "AkLogging.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"
#include "DclientMsgSt.h"

namespace akcs_msgparse
{
    /*
    <Msg>
      <Type>RequestOpenDoor</Type>              // 开普通门 RequestOpenDoor/ 开安全门 RequestOpenSecurityRelay
      <Protocal>1.0</Protocal>
      <Params>
        <MAC>0C11050A72E4</MAC>
        <Relay>0</Relay>                        //relay_id，开哪个门
        <TraceID>2123dfasd23223gdd</TraceID>    //新增：时间戳+8位随机字符串(数字+字母大小写字符)
      </Params>
    </Msg>
    */
    static int ParseDevRequestOpen(char* buf, SOCKET_MSG_DEV_REQUEST_OPEN& dev_request_open)
    {
        if (buf == NULL)
        {
            AK_LOG_WARN << "Input Param is NULL";
            return -1;
        }

        TCHAR text[4096];

        TransUtf8ToTchar(buf, text, 4096);
        TiXmlDocument doc;
        if (!doc.LoadBuffer(buf))
        {
            AK_LOG_WARN << "XML LoadBuffer failed.";
            return -1;
        }

        AK_LOG_INFO << "ParseRequestOpen Msg: " << text;

        TiXmlElement* root = doc.RootElement();
        TiXmlElement* node = NULL;
        if (NULL == root)
        {
            AK_LOG_WARN << "ROOT Node is NULL";
            return -1;
        }

        //主节点的名称如果不是Msg则跳过
        if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
        {
            AK_LOG_WARN << "mismatched " << XML_NODE_NAME_MSG;
            return -1;
        }

        for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
        {

            if (strcmp(node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
            {
                TransUtf8ToTchar(node->GetText(), dev_request_open.type, sizeof(dev_request_open.type)); //开门的类型,是普通relay还是security relay
            }
            else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
            {
                TiXmlElement* sub_node = NULL;
                for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
                {
                    if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MAC) == 0)
                    {
                        TransUtf8ToTchar(sub_node->GetText(), dev_request_open.mac, sizeof(dev_request_open.mac));
                    }
                    if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_RELAY) == 0)
                    {
                        char tmp[8] = "";
                        TransUtf8ToTchar(sub_node->GetText(), tmp, sizeof(tmp));
                        dev_request_open.relay = ATOI(tmp);
                    }
                    if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_TRACE_ID) == 0)
                    {
                        TransUtf8ToTchar(sub_node->GetText(), dev_request_open.trace_id, sizeof(dev_request_open.trace_id));
                    }
                }
            }
        }

        return 0;
    }

}

#endif //__PARSE_DEV_REQUEST_OPEN_H__
