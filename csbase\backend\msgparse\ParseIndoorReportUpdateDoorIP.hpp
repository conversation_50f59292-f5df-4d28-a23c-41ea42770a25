#ifndef __PARSE_INDOOR_REPORT_UPDATE_DOOR_IP_H__
#define __PARSE_INDOOR_REPORT_UPDATE_DOOR_IP_H__

#include "util.h"
#include "tinystr.h"
#include "tinyxml.h"
#include "CharChans.h"
#include "AkLogging.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"

namespace akcs_msgparse
{

/*
<Msg>
  <Params>
  <Mac>xxxxx</Mac> //门口机mac
  <IP>222.x.x.x</IP>//ip  </Params>
</Msg>

*/
    static int ParseIndoorReportUpdateDoorIP(char *buf, SOCKET_MSG_DEV_REPORT_UPDATE_DOOR_IP& ip_info)
    {
        if (buf == NULL)
        {
            AK_LOG_WARN << "Input Param is NULL";
            return -1;
        }
    
        TCHAR text[4096];
        TransUtf8ToTchar(buf, text, 4096);
        AK_LOG_INFO << "ParseIndoorReportUpdateDoorIP \n " << text;
        
        TiXmlDocument doc;
        if (!doc.LoadBuffer(buf))
        {
            AK_LOG_WARN << "XML LoadBuffer failed.";
            return -1;
        }
    
        TiXmlElement* root_node = doc.RootElement();
        if (NULL == root_node)
        {
            AK_LOG_WARN << "ROOT Node is NULL";
            return -1;
        }
    
        //主节点的名称如果不是Msg则跳过
        if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
        {
            AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
            return -1;
        }
    
        TiXmlElement* node = NULL;
        for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
        {
            if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
            {
                TiXmlElement* sub_node = NULL;
                for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
                {
                    if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_IP) == 0)
                    {
                        TransUtf8ToTchar(sub_node->GetText(), ip_info.ip, sizeof(ip_info.ip));
                    }
                    else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MAC) == 0)
                    {
                        TransUtf8ToTchar(sub_node->GetText(), ip_info.mac, sizeof(ip_info.mac));
                    }
                }
            }
        }
        return 0;
    }



}

#endif 
