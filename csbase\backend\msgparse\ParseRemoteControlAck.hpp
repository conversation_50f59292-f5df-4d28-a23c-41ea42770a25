#ifndef __PARSE_REMOTE_CONTROL_ACK_H__
#define __PARSE_REMOTE_CONTROL_ACK_H__

#include "util.h"
#include "tinystr.h"
#include "tinyxml.h"
#include "CharChans.h"
#include "AkLogging.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"
#include "DclientMsgSt.h"

namespace akcs_msgparse
{
/*<Msg>
  <Params>
    <MsgID>000A<MsgID/>  <!—命令ID/-->
    <Type>OpenDoor</Type> <!--这个标识和控制命令的一致/-->
    <TraceID>0123233000</TraceID>
    <Result>1</Result>//重启不用管这个字段
    <Info></Info>
</Msg>*/
static int ParseRemoteControlAck(char *buf, SOCKET_MSG_DEV_REMOTE_ACK& remote_ack_msg)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "ParseRemoteControlAck Input Param is NULL";
        return -1;
    }

    char text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseRemoteControlAck text: \n" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT is NULL";
        return -1;
    }
    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {

        if (strcmp(node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
        {
            //暂不需要处理
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), remote_ack_msg.type, sizeof(remote_ack_msg.type));
                }
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_TRACE_ID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), remote_ack_msg.trace_id, sizeof(remote_ack_msg.trace_id));
                }
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_INFO) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), remote_ack_msg.info, sizeof(remote_ack_msg.info));
                }
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MSGID) == 0)
                {
                    char tmp[8] = "";
                    TransUtf8ToTchar(sub_node->GetText(), tmp, sizeof(tmp));
                    remote_ack_msg.msg_id =  ATOI(tmp);
                }
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_RESULT) == 0)
                {
                    char tmp[8] = "";
                    TransUtf8ToTchar(sub_node->GetText(), tmp, sizeof(tmp));
                    remote_ack_msg.result =  ATOI(tmp);
                }
            }
        }
    }

    return 0;
}

}

#endif // __PARSE_REMOTE_CONTROL_ACK_H__
