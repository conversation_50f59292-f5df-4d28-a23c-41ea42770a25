#ifndef __PARSE_REPORT_IP_CALL_RECORD_MSG_H__
#define __PARSE_REPORT_IP_CALL_RECORD_MSG_H__

#include "util.h"
#include "tinystr.h"
#include "tinyxml.h"
#include "CharChans.h"
#include "AkLogging.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"

namespace akcs_msgparse
{
/*
<Msg>
    <Type>ReportOpendoorVideo</Type>
    <Protocal>2.0</Protocal>
    <Params>
        <PicName>0A0203200117-1616054761_0_DoorDev_4a2c83e2af7e5d4ea8e469328b2db0d9.jpg</PicName>//这个图片名称和开门时候图片的名称对应，这样云按这个名称更新对应doorlog的视频信息。
        <VideoRecordName>cn-3cba6e767e3e11efa64706f736f69293-1728380415-DRC-186f6cca1a801ccd27262d2bee174946.mp4</VideoRecordName> // 长度256字节，文件名规则同语音留言，$UUID-$Timestamp-DRC-$Token ，标识DRC（door record）
    </Params>
</Msg>

<Msg>
    <Type>ReportIpCallRecord</Type>
    <Protocal>2.0</Protocal>
    <Params>
        <Caller>712012511</Caller>(长度32)//自己的sip账号
        <Callee>712012000</Callee>(长度32)//contact里面的uid
        <CallTraceID>6501100682-1719309757-ABCDEFGHIJKL</CallTraceID> //通话时候的X-CallTraceID
        <CallerName>name1</CallerName>//自己的localtion
        <CalleeName>name2</CalleeName>//contact里面的name。
        <IsGroupCall>1</IsGroupCall>//是否是群呼  </Params>
        <IsAnswer>1</IsAnswer>//是否接听 0 or 1
        <Duration>10</Duration>//时长
        <StartTimeStamp></StartTimeStamp>//开始呼出的时间戳，云需要做时区转换
        <AnswerTimeStamp></AnswerTimeStamp>//接听时间戳
        <TraceID></TraceID> //每条消息id需要一个专门的traceid,规则可以是CallTraceID的值加下划线0 1 2
    </Params>
</Msg>
*/
static int ParseReportIpCallRecordMsg(char *buf, SOCKET_MSG_DEVICE_REPORT_IP_CALL_RECORD& report_ip_call_record)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReportIpCallRecordMsg \n " << text;
    
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_CALLER) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_ip_call_record.caller, sizeof(report_ip_call_record.caller));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_CALLEE) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_ip_call_record.callee, sizeof(report_ip_call_record.callee));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_CALL_TRACE_ID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_ip_call_record.call_trace_id, sizeof(report_ip_call_record.call_trace_id));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_CALLER_NAME) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_ip_call_record.caller_name, sizeof(report_ip_call_record.caller_name));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_CALLEE_NAME) == 0)
                {   
                    TransUtf8ToTchar(sub_node->GetText(), report_ip_call_record.callee_name, sizeof(report_ip_call_record.callee_name));    
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_IS_GROUP_CALL) == 0)
                {
                    report_ip_call_record.is_group_call = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_IS_ANSWER) == 0)
                {
                    report_ip_call_record.is_answer = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_START_TIME_STAMP) == 0)
                {
                    report_ip_call_record.start_time_stamp = ATOULL(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_ANSWER_TIME_STAMP) == 0)
                {
                    report_ip_call_record.answer_time_stamp = ATOULL(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_DURATION) == 0)
                {
                    report_ip_call_record.duration = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_TRACE_ID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_ip_call_record.trace_id, sizeof(report_ip_call_record.trace_id));
                }
            }
        }
    }
    return 0;
}


}

#endif 
