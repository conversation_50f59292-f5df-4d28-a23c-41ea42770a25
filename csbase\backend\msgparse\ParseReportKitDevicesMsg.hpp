#ifndef __PARSE_REPORT_KIT_DEVICES_MSG_H__
#define __PARSE_REPORT_KIT_DEVICES_MSG_H__

#include "util.h"
#include "tinystr.h"
#include "tinyxml.h"
#include "CharChans.h"
#include "AkLogging.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"

/*
<Msg>
<Type>ReportKitDevices</Type>
<Params>
<Item>   
<MAC>0A:02:03:20:01:17</MAC>
<SWVer>29.30.2.601</SWVer>
<IP>xxx.xxx.xxx.xxx</IP>
<Model>C313</Model>
</Item>
....
<Item> 同上 </Item>
</Params>
</Msg>
*/

namespace akcs_msgparse
{
static int ParseReportKitDevices(char *buf, std::vector<SOCKET_MSG_DEV_KIT_DEVICE> &kit_devices)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReportKitDevices text=" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) != 0)
        {
            continue;
        }

        TiXmlElement* sub_node = NULL;
        for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
        {
            if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_ITEM) != 0)
            {
                continue;
            }

            SOCKET_MSG_DEV_KIT_DEVICE kit_device;
            memset(&kit_device, 0, sizeof(kit_device));
            TiXmlElement* item_node = NULL;
            for (item_node = sub_node->FirstChildElement(); item_node; item_node = item_node->NextSiblingElement())
            {
                if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_MAC) == 0)
                {
                    TransUtf8ToTchar(item_node->GetText(), kit_device.mac, sizeof(kit_device.mac) / sizeof(TCHAR));
                }
                else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_LOCALTION) == 0)
                {
                    std::string decode_location = URLDecode(item_node->GetText());
                    Snprintf(kit_device.location, sizeof(kit_device.location), decode_location.c_str());
                }
                else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
                {
                    kit_device.type = ATOI(item_node->GetText());
                }
                else if (strcmp(item_node->Value(), XML_NODE_NAME_MSG_PARAM_SWVER) == 0)
                {
                    TransUtf8ToTchar(item_node->GetText(), kit_device.version, sizeof(kit_device.version) / sizeof(TCHAR));
                }
            }
            kit_devices.push_back(kit_device);
        }
    }

    return 0;
}

}
#endif