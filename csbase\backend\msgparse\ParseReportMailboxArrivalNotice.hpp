#ifndef __PARSE_REQUEST_RECORD_VIDEO_MSG_LIST_H__
#define __PARSE_REQUEST_RECORD_VIDEO_MSG_LIST_H__

#include "util.h"
#include "tinystr.h"
#include "tinyxml.h"
#include "CharChans.h"
#include "AkLogging.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"

namespace akcs_msgparse
{
/*
<Msg>
  <Type>RepExtMailboxArrivalNotice</Type>
  <Protocal>2.0</Protocal>
  <Params>
  <Node>578287342</Node>     //房间主账户账号
  <Title>mailbox message</Title>  //通知的消息标题
  <Content>0</Content> //通知内容
  </Params>
</Msg>

*/
static int ParseReportMailboxArrivalNotice(char *buf, SOCKET_MSG_REPORT_MAILBOX_ARRIVAL_NOTICE& mailbox_message)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];

    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << " Parse Report Mailbox Arrival Notice Msg text=" << text;
    
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_NODE) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), mailbox_message.account, sizeof(mailbox_message.account));
                }
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MSG_TITLE) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), mailbox_message.title, sizeof(mailbox_message.title));
                }
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_CONTENT) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), mailbox_message.content, sizeof(mailbox_message.content));
                }
            }
        }
    }

    return 0;
}


}

#endif 

