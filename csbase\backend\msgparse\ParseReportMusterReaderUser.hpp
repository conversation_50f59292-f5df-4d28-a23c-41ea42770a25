#ifndef __PARSE_REMOTE_MUSTER_READER_USER_H__
#define __PARSE_REMOTE_MUSTER_READER_USER_H__

#include "util.h"
#include "tinystr.h"
#include "tinyxml.h"
#include "CharChans.h"
#include "AkLogging.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"
#include "DclientMsgSt.h"

namespace akcs_msgparse
{
/*
<Msg>
    <Type>MusterReportUser</Type>
    <Params>
        <MusterType></MusterType> // 点名类型 1=常规开门方式点名 2=TmpKey方式点名
        <MusterUser></MusterUser> // 点名用户信息 当MusterType=1时，为User的PerID；当MusterType=2时，为云上回复的TmpKeyUUID 64字节
    </Params>
</Msg>
*/
static int ParseReportMusterReaderUser(char *buf, SOCKET_MSG_REPORT_MUSTER_USER& report_muster_user_msg)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "ParseReportMusterReaderUser Input Param is NULL";
        return -1;
    }

    char text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReportMusterReaderUser text: \n" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed";
        return -1;
    }

    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT is NULL";
        return -1;
    }
    //主节点的名称如果不是Msg则跳过
    if (strcmp(root->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {

        if (strcmp(node->Value(), XML_NODE_NAME_MSG_TYPE) == 0)
        {
            //暂不需要处理
        }
        else if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MUSTER_TYPE) == 0)
                {
                    char tmp[8] = "";
                    TransUtf8ToTchar(sub_node->GetText(), tmp, sizeof(tmp));
                    int tmp_type = ATOI(tmp);
                    //合法性校验
                    if (tmp_type >= (int)MusterType::TYPE_LIMIT || tmp_type <= (int)MusterType::ILLEGAL)
                    {
                        report_muster_user_msg.muster_type = MusterType::ILLEGAL;
                    }
                    else
                    {
                        report_muster_user_msg.muster_type = static_cast<MusterType>(tmp_type);
                    }
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MUSTER_USER) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_muster_user_msg.muster_user, sizeof(report_muster_user_msg.muster_user));
                }
            }
        }
    }

    return 0;
}

}

#endif // __PARSE_REMOTE_MUSTER_READER_USER_H__
