#ifndef __PARSE_REPORT_VIDEO_RECORD_MSG_H__
#define __PARSE_REPORT_VIDEO_RECORD_MSG_H__

#include "util.h"
#include "tinystr.h"
#include "tinyxml.h"
#include "CharChans.h"
#include "AkLogging.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"

namespace akcs_msgparse
{
/*
<Msg>
    <Type>ReportOpendoorVideo</Type>
    <Protocal>2.0</Protocal>
    <Params>
        <PicName>0A0203200117-1616054761_0_DoorDev_4a2c83e2af7e5d4ea8e469328b2db0d9.jpg</PicName>//这个图片名称和开门时候图片的名称对应，这样云按这个名称更新对应doorlog的视频信息。
        <VideoRecordName>cn-3cba6e767e3e11efa64706f736f69293-1728380415-DRC-186f6cca1a801ccd27262d2bee174946.mp4</VideoRecordName> // 长度256字节，文件名规则同语音留言，$UUID-$Timestamp-DRC-$Token ，标识DRC（door record）
    </Params>
</Msg>
*/
static int ParseReportVideoRecordMsg(char *buf, SOCKET_MSG_DEVICE_REPORT_VIDEO_RECORD& report_record_video)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseRequestStopVideoRecordMsg \n " << text;
    
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PIC_NAME) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_record_video.pic_name, sizeof(report_record_video.pic_name));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_VIDEO_RECORD_NAME) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), report_record_video.video_record_name, sizeof(report_record_video.video_record_name));
                }
            }
        }
    }
    return 0;
}


}

#endif 
