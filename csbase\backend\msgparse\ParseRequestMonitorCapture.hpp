#ifndef __PARSE_REQUEST_STOP_VIDEO_RECORD_H__
#define __PARSE_REQUEST_STOP_VIDEO_RECORD_H__

#include "util.h"
#include "tinystr.h"
#include "tinyxml.h"
#include "CharChans.h"
#include "AkLogging.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"

namespace akcs_msgparse
{
/*
<Msg>
  <Type>ReqCapture</Type>
  <Protocal>1.0</Protocal>
  <Params>
     <MAC>0C110000000</MAC>  (app要截图的设备mac地址)
     <UUID>xxxx</UUID>
     <Site>站点账号 userconf请求到的当前站点的账号 (sip字段)64字节字符串</Site>
     <RecordVideo>1</RecordVideo>     //RecordVideo 1开启视频录制
     <Camera>Main</Camera> //请求截图的摄像头 Main=主摄像头 Auxiliary=辅摄像头
     <StreamID>1</StreamID> //请求截图的流ID 1=主码流(默认流) 2=子码流
  </Params>
</Msg>
*/
static int ParseRequestMonitorCaptureMsg(char *buf, SOCKET_MSG_REQ_CAPTURE& request_capture)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseRequestCaptureMsg \n " << text;
    
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MAC) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), request_capture.mac, sizeof(request_capture.mac));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_UUID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), request_capture.uuid, sizeof(request_capture.uuid));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_SITE) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), request_capture.site, sizeof(request_capture.site));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_RECORD_VIDEO) == 0)
                {
                    request_capture.record_video = ATOI(sub_node->GetText());
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_CAMERA) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), request_capture.camera, sizeof(request_capture.camera));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_STREAM_ID) == 0)
                {
                    request_capture.stream_id = ATOI(sub_node->GetText());
                }
            }
        }
    }
    
    return 0;
}


}

#endif 
