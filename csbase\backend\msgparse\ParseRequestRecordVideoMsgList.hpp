#ifndef __PARSE_REQUEST_RECORD_VIDEO_MSG_LIST_H__
#define __PARSE_REQUEST_RECORD_VIDEO_MSG_LIST_H__

#include "util.h"
#include "tinystr.h"
#include "tinyxml.h"
#include "CharChans.h"
#include "AkLogging.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"

namespace akcs_msgparse
{

/*
<Msg>
    <Type>RequestRecordVideo</Type>
    <Params>
        <Items>
            <Item Type="IP">
                <CallTraceID>xxxx</CallTraceID> //通话的traceid  Type=IP代表ip直播的通话 。长度128
            </Item>
            <Item Type="SIP">
                <CallTraceID>xxxx</CallTraceID> //通话的traceid  Type=IP代表ip直播的通话 。长度128
            </Item>
            <!-- 可以继续添加其他 Item -->
        </Items>
    </Params>
</Msg>
*/
static int ParseRequestRecordVideoMsgList(char *buf, std::vector<SOCKET_MSG_REQUEST_RECORD_VIDEO>& msg_list)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "ParseRequestRecordVideoMsgList Input Param Is NULL";
        return -1;
    }

    char text[4096];
    TransUtf8ToTchar(buf, text, sizeof(text));
    AK_LOG_INFO << "ParseRequestRecordVideoMsgList \n " << text;
    
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed";
        return -1;
    }
    
    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }
    
    //主节点名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched" << XML_NODE_NAME_MSG;
        return -1;
    }
    
    TiXmlElement* params_node = root_node->FirstChildElement(XML_NODE_NAME_MSG_PARAM);
    if (params_node == NULL)
    {
        AK_LOG_WARN << "<Params> node not found";
        return -1;
    }

    // Get Items node
    TiXmlElement* items_node = params_node->FirstChildElement(XML_NODE_NAME_MSG_PARAM_ITEMS);
    if (items_node == nullptr)
    {
        AK_LOG_WARN << "<Items> node not found";
        return -1;
    }

    for (TiXmlElement* item_node = items_node->FirstChildElement(XML_NODE_NAME_MSG_PARAM_ITEM); item_node != nullptr;  item_node = item_node->NextSiblingElement(XML_NODE_NAME_MSG_PARAM_ITEM))
    {
        SOCKET_MSG_REQUEST_RECORD_VIDEO record_video_msg;
        
        const char* type_attr = item_node->Attribute(XML_NODE_NAME_MSG_PARAM_TYPE);
        if (type_attr == nullptr)
        {
            AK_LOG_WARN << "Item missing Type attribute";
            continue;
        }
        
        if (strcmp(type_attr, XML_NODE_NAME_MSG_PARAM_IP) == 0)
        {
            record_video_msg.type = VideoRecordCallType::IP;
        }
        else if (strcmp(type_attr, XML_NODE_NAME_MSG_PARAM_UPPER_CASE_SIP) == 0)
        {
            record_video_msg.type = VideoRecordCallType::SIP;
        }
        
        TiXmlElement* call_trace_id_node = item_node->FirstChildElement(XML_NODE_NAME_MSG_CALL_TRACE_ID);
        if (call_trace_id_node == nullptr || call_trace_id_node->GetText() == nullptr)
        {
            continue;
        }
        
        const char* call_trace_id_text = call_trace_id_node->GetText();
        if (call_trace_id_text != NULL)
        {
            Snprintf(record_video_msg.call_trace_id, sizeof(record_video_msg.call_trace_id), call_trace_id_text);
        }
     
        AK_LOG_INFO << "Parsed Item - Type: " << type_attr << ", type = " << (int)record_video_msg.type << ", CallTraceID: " << record_video_msg.call_trace_id;
        msg_list.push_back(record_video_msg);
    }
    
    return 0;
}


}

#endif 
