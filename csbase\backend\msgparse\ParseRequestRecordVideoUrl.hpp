#ifndef __PARSE_REQUEST_RECORD_VIDEO_MSG_LIST_H__
#define __PARSE_REQUEST_RECORD_VIDEO_MSG_LIST_H__

#include "util.h"
#include "tinystr.h"
#include "tinyxml.h"
#include "CharChans.h"
#include "AkLogging.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"

namespace akcs_msgparse
{
/*
<Msg>
    <Type>PlayVideo</Type>
    <Params>
        <CallTraceID Type="IP">xxxx</CallTraceID>   //xxxx通话的traceid，Type=IP代表ip直播的通话  Type=SIP代表走云
    </Params>
</Msg>
*/
static int ParseRequestPlayRecordVideoUrl(char *buf, SOCKET_MSG_REQUEST_RECORD_VIDEO& record_video)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "ParseRequestPlayRecordVideoUrl Input Param is NULL";
        return -1;
    }

    char text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseRequestPlayRecordVideoUrl text: \n" << text;
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed";
        return -1;
    }
    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }
    //主节点名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched" << XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_CALL_TRACE_ID) == 0)
                {
                    const char* type_attr = sub_node->Attribute(XML_NODE_ATTRIBUTE_TYPE);
                    if (type_attr)
                    {
                        if (strcmp(type_attr, XML_NODE_NAME_MSG_PARAM_IP) == 0)
                        {
                            record_video.type = VideoRecordCallType::IP;
                        }
                        else if (strcmp(type_attr, XML_NODE_NAME_MSG_PARAM_UPPER_CASE_SIP) == 0)
                        {
                            record_video.type = VideoRecordCallType::SIP;
                        }
                    }
                    TransUtf8ToTchar(sub_node->GetText(), record_video.call_trace_id, sizeof(record_video.call_trace_id));
                }
            }
        }
    }
    return 0;
}


}

#endif 
