#ifndef __PARSE_RESPONSEEXTERN_PUSH_BUTTON_MSG_H__
#define __PARSE_RESPONSEEXTERN_PUSH_BUTTON_MSG_H__

#include "util.h"
#include "tinystr.h"
#include "tinyxml.h"
#include "CharChans.h"
#include "AkLogging.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"


namespace akcs_msgparse
{

static int ParseResponseExternPushButtonMsg(char *buf, std::map<int, int>& msg)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseReportRelayStatusMsg text: \n" << text;
    TiXmlDocument doc;
    if (!doc.Load<PERSON>uffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    // 主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "Mismatched " << XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_BUTTON_MODULE_LIST) == 0)
                {
                    TiXmlElement* module_node = NULL;
                    
                    for (module_node = sub_node->FirstChildElement(); module_node; module_node = module_node->NextSiblingElement())
                    {
                        if (strcmp(module_node->Value(), XML_NODE_NAME_MSG_BUTTON_MODULE) == 0)
                        {
                            int id = -1;
                            int button_count = -1;
                            module_node->Attribute("ID", &id);
                            module_node->Attribute("ButtonCount", &button_count);
                            if (id >= 0 && button_count >= 0)
                            {
                                msg[id] = button_count;
                            }
                        }
                    }
                }
            }
        }
    }
    return 0;
}

}

#endif
