#ifndef __PARSE_RESPONSE_LOCKDOWN_H__
#define __PARSE_RESPONSE_LOCKDOWN_H__

#include "util.h"
#include "tinystr.h"
#include "tinyxml.h"
#include "CharChans.h"
#include "AkLogging.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"

namespace akcs_msgparse
{
/*
<Type>ResponseLockdownDoor</Type>
    <Params>
        <Mode>ON/OFF</Mode>     //On表示锁定，OFF表示解除锁定
        <UUID>123xxxxxxxxxxxxxxxx4</UUID>
        <Relay>1010</Relay>     //每位代表一个relay, 最高位为第一个relay，0代表on失败 1代表on成功 。开门relay为124 返回为1010,代表relay1锁定on成功relay2锁定on失败relay3锁定on成功relay4锁定on失败；没有这个relay时会直接忽略这一位
        <SecurityRelay>11</SecurityRelay> //同Relay，没有就留空
    </Params>
</Msg>
*/
static int ParseResponseLockDownMsg(char *buf, SOCKET_MSG_RESPONSE_LOCKDOWN& response_lockdown)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseResponseLockDownMsg \n " << text;
    
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_MODE) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), response_lockdown.mode, sizeof(response_lockdown.mode));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_UUID) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), response_lockdown.uuid, sizeof(response_lockdown.uuid));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_RELAY) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), response_lockdown.relay, sizeof(response_lockdown.relay));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_SECURITY_RELAY) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), response_lockdown.security_relay, sizeof(response_lockdown.security_relay));
                }
            }
        }
    }
    return 0;
}


}

#endif 
