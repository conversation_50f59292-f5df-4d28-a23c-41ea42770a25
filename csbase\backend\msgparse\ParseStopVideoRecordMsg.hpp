#ifndef __PARSE_REQUEST_STOP_VIDEO_RECORD_H__
#define __PARSE_REQUEST_STOP_VIDEO_RECORD_H__

#include "util.h"
#include "tinystr.h"
#include "tinyxml.h"
#include "CharChans.h"
#include "AkLogging.h"
#include "DclientMsgDef.h"
#include "AkcsCommonDef.h"

namespace akcs_msgparse
{
/*
<Msg>
    <Type>StopVideo</Type>
    <Protocal>1.0</Protocal>
    <Params>
        <MAC>0C110000000</MAC> 
        <Site>站点账号 userconf请求到的当前站点的账号 (sip字段)64字节字符串</Site>
    </Params>
</Msg>
*/
static int ParseRequestStopVideoRecordMsg(char *buf, SOCKET_MSG_REQUEST_STOP_RECORD_VIDEO& stop_record_video)
{
    if (buf == NULL)
    {
        AK_LOG_WARN << "Input Param is NULL";
        return -1;
    }

    TCHAR text[4096];
    TransUtf8ToTchar(buf, text, 4096);
    AK_LOG_INFO << "ParseRequestStopVideoRecordMsg \n " << text;
    
    TiXmlDocument doc;
    if (!doc.LoadBuffer(buf))
    {
        AK_LOG_WARN << "XML LoadBuffer failed.";
        return -1;
    }

    TiXmlElement* root_node = doc.RootElement();
    if (NULL == root_node)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    //主节点的名称如果不是Msg则跳过
    if (strcmp(root_node->Value(), XML_NODE_NAME_MSG) != 0)
    {
        AK_LOG_WARN << "mismatched " <<  XML_NODE_NAME_MSG;
        return -1;
    }

    TiXmlElement* node = NULL;
    for (node = root_node->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        if (strcmp(node->Value(), XML_NODE_NAME_MSG_PARAM) == 0)
        {
            TiXmlElement* sub_node = NULL;
            for (sub_node = node->FirstChildElement(); sub_node; sub_node = sub_node->NextSiblingElement())
            {
                if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_MAC) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), stop_record_video.mac, sizeof(stop_record_video.mac));
                }
                else if (strcmp(sub_node->Value(), XML_NODE_NAME_MSG_PARAM_SITE) == 0)
                {
                    TransUtf8ToTchar(sub_node->GetText(), stop_record_video.site, sizeof(stop_record_video.site));
                }
            }
        }
    }
    return 0;
}


}

#endif 
