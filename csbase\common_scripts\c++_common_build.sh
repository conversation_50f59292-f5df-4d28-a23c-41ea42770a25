#!/bin/bash
# ****************************************************************************
# Author        :   buhuai.su
# Last modified :   2022-08-05
# Filename      :   build.sh
# Version       :
# Description   :   web 的构建脚本
# Modifier      :
# ****************************************************************************

set -euo pipefail

PROJECT_PATH=$1        #git clone时项目路径
SRC_PATH=$2            #编译后代码存储路径

DOCKER_TAG=$3          #docker镜像tag号
CONTAINER_NAME=$4      #启动容器名称
PROJECT_NAME=$5
JOB_NAME=$6
PACK_TYPE=${10}   #ubuntu20  or ubuntu14

WORKSPACE_PATH=/opt/jenkins/workspace

DOCKER_ROOT=/home/<USER>
DOCKER_PROJECT_ROOT=$DOCKER_ROOT/$JOB_NAME/$PROJECT_NAME

#在源码包的同一路径下生成安装包
AKCS_PACKAGE_NAME=objects
AKCS_PACKAGE_ROOT=$WORKSPACE_PATH/$JOB_NAME/$PROJECT_NAME/$AKCS_PACKAGE_NAME

ts=$(date +%s.%N)
echo "【开始编译项目】$ts"
mkdir -p "$AKCS_PACKAGE_ROOT"


if [ "ubuntu14" == $PACK_TYPE ]; then
    image=registry.cn-hangzhou.aliyuncs.com/ak_system/app_backend_for_cicd:akcloud-1.0
elif [ "ubuntu20" == $PACK_TYPE ]; then
    image=registry.cn-hangzhou.aliyuncs.com/ak_system/app_backend_build_cicd_ubuntu20:akcloud-1.0
    rm -rf $WORKSPACE_PATH/$JOB_NAME/csbase/thirdlib
    cp -rf $WORKSPACE_PATH/$JOB_NAME/csbase/thirdlib-ubuntu20 $WORKSPACE_PATH/$JOB_NAME/csbase/thirdlib
else
    echo "Error: ubuntu系统版本是 $PACK_TYPE, 安装脚本不支持"
    exit 1
fi

# 保存分支tag
branch_or_tag="${9}"

# 提取最后一个 "_" 或 "-" 后面的部分
suffix=$(echo "$branch_or_tag" | sed -E 's/.*[-_](.*)/\1/')

# 去掉所有的 "."，保留纯数字
version=$(echo "$suffix" | tr -d '.')

output_file="$PROJECT_PATH/$PROJECT_NAME/release/conf/version.conf"

# 将提取的版本号写入文件
echo "branch_or_tag=$version" > "$output_file"

docker run --rm --name "build_backend-$ts" -v $WORKSPACE_PATH:$DOCKER_ROOT $image bash -c "cd $DOCKER_PROJECT_ROOT/build && bash -x build.sh clean && bash -x build.sh build"