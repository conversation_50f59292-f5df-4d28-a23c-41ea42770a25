# 检查是否传递了 DBINTERFACE_QUERY_DIR 和 CSBASE_SOURCE_DIR 变量
if(NOT DEFINED DBINTERFACE_QUERY_DIR)
    message(FATAL_ERROR "DBINTERFACE_QUERY_DIR variable is not defined")
endif()

if(NOT DEFINED CSBASE_SOURCE_DIR)
    message(FATAL_ERROR "CSBASE_SOURCE_DIR variable is not defined")
endif()

# 执行 Bash 脚本并捕获返回值和输出
execute_process(
    COMMAND bash find_included_dbinterface_files.sh ${DBINTERFACE_QUERY_DIR} ${CSBASE_SOURCE_DIR} ${DBINTERFACE_FILES_OUTPUT_DIR}
    WORKING_DIRECTORY ${CSBASE_SOURCE_DIR}/common_scripts
)
# 读取 Bash 脚本生成的文件列表
file(READ "${DBINTERFACE_FILES_OUTPUT_DIR}/dbinterface_files.txt" file_content)
string(REPLACE "\n" ";" file_list "${file_content}")

set(prefixed_file_list "")

foreach(file ${file_list})
    list(APPEND prefixed_file_list "${CSBASE_SOURCE_DIR}/${file}")
endforeach()

set(prefixed_file_list ${prefixed_file_list})



