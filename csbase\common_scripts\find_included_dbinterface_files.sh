#!/bin/bash
dbinterface_query_dirs="$1"
recursive_root_dir="$2"
dbinterface_files_output_dir="$3"

# 用于存储所有找到的初始文件
initial_files=()

# 迭代所有初始根目录
for initial_root_dir in ${dbinterface_query_dirs}; do
    root_files=$(grep -r "dbinterface" "$initial_root_dir" | grep "include" | awk -F'#include ' '{print $2}' | tr -d '"' | tr -d '\r' | sort | uniq)
    initial_files+=($(echo "$root_files" | tr ' ' '\n' | sort | uniq))
done


# 用于存储所有找到的文件
all_files=()

# 用于存储已经处理过的文件
processed_files=()
function find_included_files {
    local file="$1"
	local file_base="${file%.h}"  # 去掉 .h 扩展名
    local file_path_h="$recursive_root_dir/$file_base.h"
    local file_path_cpp="$recursive_root_dir/$file_base.cpp"	

    # 检查并处理 .h 文件
    if [[ -f "$file_path_h" && ! " ${processed_files[@]} " =~ " ${file_base}.h " ]]; then
        processed_files+=("${file_base}.h")
        included_files=$(grep '#include' "$file_path_h" | grep "dbinterface" | awk -F'#include ' '{print $2}' | tr -d '"' | tr -d '\r' | sort | uniq)
        for included_file in ${included_files[@]}; do
            if [[ ! " ${all_files[@]} " =~ " ${included_file} " ]]; then
                all_files+=("$included_file")
                find_included_files "${included_file%.h}"
            fi
        done
    fi
    
    # 检查并处理 .cpp 文件
    if [[ -f "$file_path_cpp" && ! " ${processed_files[@]} " =~ " ${file_base}.cpp " ]]; then
        processed_files+=("${file_base}.cpp")
        included_files=$(grep '#include' "$file_path_cpp" | grep "dbinterface" | awk -F'#include ' '{print $2}' | tr -d '"' | tr -d '\r' | sort | uniq)
        for included_file in ${included_files[@]}; do
            if [[ ! " ${all_files[@]} " =~ " ${included_file} " ]]; then
                all_files+=("$included_file")
                find_included_files "${included_file%.h}"
            fi
        done
    fi
}

# 开始处理初始文件
for file in ${initial_files[@]}; do
    if [[ ! " ${all_files[@]} " =~ " ${file} " ]]; then
        all_files+=("$file")
    fi
    find_included_files "$file"
done

output_file="$dbinterface_files_output_dir/dbinterface_files.txt"
> "$output_file"
# 只添加存在的 .cpp 文件
for file in "${all_files[@]}"; do
	# 获取文件的目录路径
    dir=$(dirname "$file")
    # 获取文件的基本名称（不含路径和后缀）
    filename=$(basename "$file")
    
    # 获取 .h 前面的所有字符
    base="${filename%.*}"

    # 替换后缀为 .cpp
	new_file="$dir/$base.cpp"

    # 检查新文件是否存在
    if [[ -f "$recursive_root_dir/$new_file" ]]; then
        echo "$new_file" >> "$output_file"
    fi
done

