#ifndef __AKCS_QUEUE_CONSISTENT_HASH_H__
#define __AKCS_QUEUE_CONSISTENT_HASH_H__

#include <boost/noncopyable.hpp>
#include <map>
#include <string>
#include <boost/functional/hash.hpp>
#include <boost/format.hpp>
#include <boost/crc.hpp>
#include "ConsistentHashMap.hpp"
#include <mutex>

namespace akcs_consistent_hash
{

struct vnode_t
{
    vnode_t() {}
    vnode_t(int queue_num, std::size_t v)
        : queue_index_(queue_num), vnode_id_(v) {}

    std::string to_str() const
    {
        return boost::str(boost::format("%1%-%2%") % queue_index_ % vnode_id_);
    }
    int queue_index_;
    std::size_t vnode_id_;
};

struct crc32_hasher
{
    uint32_t operator()(const vnode_t& node)   //这样就可以这样了: size_type hash = hasher_(node);
    {
        boost::crc_32_type ret;
        std::string vnode = node.to_str();
        ret.process_bytes(vnode.c_str(), vnode.size()); //对vnode的str进行哈希,得到虚拟节点的key
        return ret.checksum();
    }
    typedef uint32_t result_type;
};

//通过一致性哈希算法进行负载均衡
class QueueConsistentHash : boost::noncopyable
{
public:
    typedef ConsistentHash<vnode_t, crc32_hasher> consistent_hash_t;
    static const int kVnodeNum = 5000;
public:
    QueueConsistentHash() {}
    ~QueueConsistentHash() {}

    uint32_t crc32_hash(const std::string key);
    void InitQueueNumList(int queue_num);
    int GetQueueNumByKey(const std::string& key);
    
private:
    std::mutex write_file_tube_consistent_mutex_;
    consistent_hash_t write_file_tube_consistent_hash_;
};



}
#endif // __AKCS_QUEUE_CONSISTENT_HASH_H__

