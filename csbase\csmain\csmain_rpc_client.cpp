#include "csmain_rpc_client.h"
#include "AkcsMonitor.h"
#include <grpcpp/impl/codegen/status_code_enum.h> 

CompletionQueue g_csmain_rpc_cq_;

//同步接口
int MainRpcClient::QueryAppDclientStatus(const std::string &uid, uint64_t msg_traceid)
{
    QueryAppDclientStatusRequest query_app_client_status_request;
    query_app_client_status_request.set_uid(uid);
    query_app_client_status_request.set_msg_traceid(msg_traceid);

    gpr_timespec timespec;
    timespec.tv_sec = 2; //设置同步阻塞时间为2秒
    timespec.tv_nsec = 0;
    timespec.clock_type = GPR_TIMESPAN;
    
    ClientContext context;
    context.set_deadline(timespec);
    
    QueryAppDclientStatusReply query_app_client_status_reply;
    Status status = stub_->QueryAppDclientStatusHandle(&context, query_app_client_status_request, &query_app_client_status_reply);
    if (status.ok())
    {
        int ret = query_app_client_status_reply.ret();
        return ret;
    }
    else if ( status.error_code() == grpc::DEADLINE_EXCEEDED)//rpc超时，证明下游服务已经过载了
    {
        AK_LOG_WARN << "RPC DEADLINE_EXCEEDED, query uid failed, uid is [" << uid << "]";
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csmain", "QueryAppDclientStatus exceeded, the backend server is csmain", AKCS_MONITOR_ALARM_GRPC_TIMEOUT_CSMAIN);
        return -1;
    }
    else
    {
        AK_LOG_WARN << "RPC failed, query uid [" << uid << "]'s srv id err: " << status.error_code() << ": " << status.error_message();
        return -1;
    }

}

//异步注册
void AsyncCompleteCMRpc()
{
    void* got_tag;
    bool ok = false;

    // Block until the next result is available in the completion queue "cq".
    while (g_csmain_rpc_cq_.Next(&got_tag, &ok)) {
        // The tag in this example is the memory location of the call object
        AsyncCsmainClientCall* call = static_cast<AsyncCsmainClientCall*>(got_tag); 

        // Verify that the request was completed successfully. Note that "ok"
        // corresponds solely to the request for updates introduced by Finish().
        GPR_ASSERT(ok);

        if (call->status.ok())
        {

        }
        else
        {
            AK_LOG_WARN << "RPC failed, please check rpc server";
        }
        delete call; 
    }
}



