/*
 *
 * Copyright 2015 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
#ifndef _CSMAIN_RPC_CLIENT_H_
#define _CSMAIN_RPC_CLIENT_H_

#include <iostream>
#include <memory>
#include <string>

#include <grpcpp/grpcpp.h>
#include <grpc/support/log.h>
#include <thread>
#include <unistd.h>
#include <grpcpp/grpcpp.h>

#include "AK.Main.grpc.pb.h"
#include "AkLogging.h"
#include "AkcsMsgDef.h"
#include "AkcsCommonSt.h"
#include "PbxMsgDef.h"


using grpc::Channel;
using grpc::ClientAsyncResponseReader;
using grpc::ClientContext;
using grpc::CompletionQueue;
using grpc::Status;

using AK::Main::QueryAppDclientStatusRequest;
using AK::Main::QueryAppDclientStatusReply;

using AK::Main::MainRpcSrv; //rpc服务名

class MainRpcClient;
typedef std::shared_ptr<MainRpcClient> MainRpcClientPtr;

//对rpc内部接口的封装
//TODO:目前csmain的rpc没有异步处理的调用，如果有的话，csmain服务列表更新时候 CmRpcClientMng需要管理异步处理线程
class MainRpcClient {
  public:
      public:
    explicit MainRpcClient(const std::string &srv_net/*ip:port*/) {
      channel_ = grpc::CreateChannel(srv_net, grpc::InsecureChannelCredentials());
      stub_ = MainRpcSrv::NewStub(channel_);
    }

    std::shared_ptr<Channel> channel_;

    // Assembles the client's payload and sends it to the server.
    int QueryAppDclientStatus(const std::string &uid, uint64_t msg_traceid);

    // Out of the passed in Channel comes the stub, stored here, our view of the
    // server's exposed services.
    std::unique_ptr<MainRpcSrv::Stub> stub_;
};

// The producer-consumer queue we use to communicate asynchronously with the
// gRPC runtime.
//CompletionQueue g_csmain_rpc_cq_;

// Loop while listening for completed responses.
// Prints out the response from the server.
void AsyncCompleteCMRpc();

// struct for keeping state and data information
// TODO,通过多态来处理AsyncClientCall的逻辑
struct AsyncCsmainClientCall 
{
    // Context for the client. It could be used to convey extra information to
    // the server and/or tweak certain RPC behaviors.
    ClientContext context;

    // Storage for the status of the RPC upon completion.
    Status status;
};



#endif











