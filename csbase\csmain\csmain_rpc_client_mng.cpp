#include <map>
#include <set>
#include <mutex>
#include "csmain_rpc_client_mng.h"
#include "util.h"

MainRpcClientMng* MainRpcClientMng::pInstance_ = nullptr;

MainRpcClientMng* MainRpcClientMng::Instance()
{
	if (!pInstance_)
    {
		pInstance_ = new MainRpcClientMng();
	}
	return pInstance_;
}

void MainRpcClientMng::AddCsmainRpcSrv(const std::string &csmain_grpc_addr, const MainRpcClientPtr& csmain_rpc_cli)
{
    std::string logic_srv_id="csmain_";
    std::string::size_type pos = csmain_grpc_addr.find(":");
    logic_srv_id += csmain_grpc_addr.substr(0, pos);

    std::lock_guard<std::mutex> lock(csmain_rpc_clis_mutex_);
    csmain_rpc_clis_.insert(std::pair<std::string, MainRpcClientPtr>(logic_srv_id, csmain_rpc_cli));
    AK_LOG_INFO << "add csmain_rpc_client " << logic_srv_id;
} 

void MainRpcClientMng::UpdateCsmainRpcSrv(const std::set<std::string> &csmain_rpc_addrs) 
{
	//TODO后面逻辑服务器数量多的时候,用两个set取差集加速处理
    std::lock_guard<std::mutex> lock(csmain_rpc_clis_mutex_);
    for(const auto &rpc_addr : csmain_rpc_addrs)//先检查新上线的csmain rpc srv  rpc_addr=192.13.1.1:9003
    {
        auto it = csmain_rpc_clis_.find(rpc_addr);
        if(it == csmain_rpc_clis_.end())
        {
        	std::string logic_srv_id="csmain_";
            std::string::size_type pos = rpc_addr.find(":");
        	logic_srv_id += rpc_addr.substr(0, pos);
            MainRpcClientPtr route_cli_ptr(new MainRpcClient(rpc_addr));
            csmain_rpc_clis_.insert(std::pair<std::string, MainRpcClientPtr>(logic_srv_id, route_cli_ptr));
            AK_LOG_INFO << "add csmain_rpc_client " << logic_srv_id;
        }
        else
        {
            //如果没有改变，那么rpc客户端会自己重连
        }
    }
	//再检查下线的csmain rpc srv
	if(csmain_rpc_clis_.size() == csmain_rpc_addrs.size())
    {
		return;
    }
	for (auto it = csmain_rpc_clis_.begin(); it != csmain_rpc_clis_.end();)
    {
        auto it2 = csmain_rpc_addrs.find(it->first);
        if(it2 == csmain_rpc_addrs.end())
        {
			AK_LOG_INFO << "del csmain_rpc_client";
            csmain_rpc_clis_.erase(it++);
        }
        else
        {
        	it++;
        }
    }
} 


MainRpcClientPtr MainRpcClientMng::getRpcClientInstance(const std::string &logic_srv_id)
{
    std::lock_guard<std::mutex> lock(csmain_rpc_clis_mutex_);
    auto it = csmain_rpc_clis_.find(logic_srv_id);
    if(it == csmain_rpc_clis_.end())
    {
    	AK_LOG_INFO << "cannot find [" << logic_srv_id << "] main rpc server";
    	return nullptr;
    }
    else
    {
        return csmain_rpc_clis_[logic_srv_id];
    }
    return nullptr;
} 

MainRpcClientPtr MainRpcClientMng::getRpcRandomClientInstance()
{
	int srv_num = csmain_rpc_clis_.size();
    int index = GetRandomNum(srv_num);
    int i = 0;
    
    std::lock_guard<std::mutex> lock(csmain_rpc_clis_mutex_);
    map<std::string/*ip:port*/, MainRpcClientPtr>::iterator it;
    for(it=csmain_rpc_clis_.begin();it!=csmain_rpc_clis_.end(); i++,it++)
    {
        if (i == index)
        {
            return it->second;
        }
    }
    return nullptr;
} 


