#ifndef __CSMAIN_ROUTE_CLIENT_MNG_H__
#define __CSMAIN_ROUTE_CLIENT_MNG_H__
#include <list>
#include <string>
#include <map>
#include <mutex>
#include <boost/noncopyable.hpp>
#include "csmain_rpc_client.h"

class MainRpcClientMng : public boost::noncopyable
{
public:
    MainRpcClientMng()
    {}
    ~MainRpcClientMng()
    {}
	static MainRpcClientMng* Instance();
    void AddCsmainRpcSrv(const std::string &csmain_addr, const MainRpcClientPtr& csmain_rpc_cli);
    void UpdateCsmainRpcSrv(const std::set<std::string> &csmain_addrs); 
    MainRpcClientPtr getRpcClientInstance(const std::string &logic_srv_id);
    MainRpcClientPtr getRpcRandomClientInstance();

private:
    static MainRpcClientMng* pInstance_;
    std::mutex csmain_rpc_clis_mutex_; 
    std::map<std::string/*ip:port*/, MainRpcClientPtr> csmain_rpc_clis_;
};

#endif //__CSMAIN_ROUTE_CLIENT_MNG_H__
