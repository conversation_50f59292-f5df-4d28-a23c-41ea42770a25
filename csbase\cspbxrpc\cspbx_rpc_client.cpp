#include "cspbx_rpc_client.h"
#include <grpcpp/impl/codegen/status_code_enum.h> 

CompletionQueue g_cspbx_rpc_cq_;

//同步接口
int PbxRpcClient::QueryUidStatus(const std::string &uid, uint64_t msg_traceid, const std::string &caller, const std::string &original_callee, uint32_t app_type)
{
    QueryUidStatusRequest query_uid_status_request;
    query_uid_status_request.set_uid(uid);
    query_uid_status_request.set_msg_traceid(msg_traceid);
    query_uid_status_request.set_caller(caller);
    query_uid_status_request.set_original_callee(original_callee);
    query_uid_status_request.set_app_type(app_type);
    
    gpr_timespec timespec;
    timespec.tv_sec = 2;//设置同步阻塞时间为2秒
    timespec.tv_nsec = 0;
    timespec.clock_type = GPR_TIMESPAN;
    ClientContext context;
    context.set_deadline(timespec);
    QueryUidStatusReply query_uid_status_reply;
    Status status = stub_->QueryUidStatusHandle(&context, query_uid_status_request, &query_uid_status_reply);
    if (status.ok())
    {
        int ret = query_uid_status_reply.ret();
        return ret;
    }
    else if ( status.error_code() == grpc::DEADLINE_EXCEEDED)//rpc超时，证明下游服务已经过载了
    {
        AK_LOG_WARN << "RPC DEADLINE_EXCEEDED, query uid failed, uid is [" << uid << "]";
        //触发监控告警
        std::string worker_node = "cspbxrpc_";
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, "QueryUidStatus exceeded, the backend server is cspbxrpc", AKCS_MONITOR_ALARM_GRPC_TIMEOUT_CSPBXRPC);
        return -1;
    }
    
    AK_LOG_WARN << "RPC failed, query uid [" << uid << "]'s srv id err: " << status.error_code() << ": " << status.error_message();
    return -1;
}

//同步接口
int PbxRpcClient::WakeupApp(uint64_t msg_traceid, const std::string &caller_sip, const std::string &callee_sip, 
                                const std::string &nick_name_location, uint32_t app_type, const std::string &x_caller, 
                                const std::string& timestamp, const std::string &x_name)
{
    WakeupAppRequest wakeup_app_request;
    wakeup_app_request.set_caller_sip(caller_sip);
    wakeup_app_request.set_callee_sip(callee_sip);
    wakeup_app_request.set_nick_name_location(nick_name_location);
    wakeup_app_request.set_msg_traceid(msg_traceid);
    wakeup_app_request.set_app_type(app_type);
    wakeup_app_request.set_x_caller(x_caller);
    wakeup_app_request.set_timestamp(timestamp);
    wakeup_app_request.set_x_name(x_name);
    
    gpr_timespec timespec;
    timespec.tv_sec = 2;//设置同步阻塞时间为2秒
    timespec.tv_nsec = 0;
    timespec.clock_type = GPR_TIMESPAN;
    ClientContext context;
    context.set_deadline(timespec);
    WakeupAppReply wakeup_app_reply;
    Status status = stub_->WakeupAppHandle(&context, wakeup_app_request, &wakeup_app_reply);
    if (status.ok())
    {
        int ret= wakeup_app_reply.ret();
        return ret;
    }
    else if ( status.error_code() == grpc::DEADLINE_EXCEEDED)//rpc超时，证明下游服务已经过载了
    {
        AK_LOG_WARN << "RPC DEADLINE_EXCEEDED, wakeup uid failed, uid is [" << callee_sip << "]";
        //触发监控告警
        std::string worker_node = "cspbxrpc_";
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, "WakeupApp exceeded, the backend server is cspbxrpc", AKCS_MONITOR_ALARM_GRPC_TIMEOUT_CSPBXRPC);
        return -1;
    }
    
    AK_LOG_WARN << "RPC failed, query uid [" << callee_sip << "]'s srv id err: " <<status.error_code() << ": " << status.error_message();
    return -1;
}

//异步接口
void PbxRpcClient::HangupApp(uint64_t msg_traceid, const std::string &caller_sip, const std::string &callee_sip, 
                                  const std::string &nick_name_location, uint32_t app_type, const std::string &x_caller, const std::string &x_name)
{
    HangupAppRequest request;
    request.set_caller_sip(caller_sip);
    request.set_callee_sip(callee_sip);
    request.set_nick_name_location(nick_name_location);
    request.set_msg_traceid(msg_traceid);
    request.set_app_type(app_type);
    request.set_x_caller(x_caller);
    request.set_x_name(x_name);

    AsyncCspbxrpcClientCall* call = new AsyncCspbxrpcClientCall;
    call->s_type_ = CSPBX_RPC_SERVER_TYPE::HANGUP_APP;
    call->hangup_app_response_reader = stub_->PrepareAsyncHangupAppHandle(&call->context, request, &g_cspbx_rpc_cq_);
    call->hangup_app_response_reader->StartCall();
    call->hangup_app_response_reader->Finish(&call->hangup_app_reply_, &call->status, (void*)call);
    return;
}

int PbxRpcClient::QueryLandlineStatus(const std::string &caller_sip, const std::string &phone, uint64_t msg_traceid)
{
    QueryLandlineStatusRequest request;
    request.set_caller_sip(caller_sip);
    request.set_phone_number(phone);
    request.set_msg_traceid(msg_traceid);
    gpr_timespec timespec;
    timespec.tv_sec = 2;//设置同步阻塞时间为2秒
    timespec.tv_nsec = 0;
    timespec.clock_type = GPR_TIMESPAN;
    ClientContext context;
    context.set_deadline(timespec);
    QueryLandlineStatusReply query_landline_status_reply;
    Status status = stub_->QueryLandlineStatusHandle(&context, request, &query_landline_status_reply);
    if (status.ok())
    {
        int ret= query_landline_status_reply.ret();
        return ret;
    }
    else if ( status.error_code() == grpc::DEADLINE_EXCEEDED)//rpc超时，证明下游服务已经过载了
    {
        AK_LOG_WARN << "RPC DEADLINE_EXCEEDED,  caller=" << caller_sip << " phone=" << phone;
        //触发监控告警
        std::string worker_node = "cspbxrpc_";
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, "QueryLandlineStatus exceeded, the backend server is cspbxrpc", AKCS_MONITOR_ALARM_GRPC_TIMEOUT_CSPBXRPC);
        return 1; //兜底：服务过载时，返回status = 1代表落地正常，保证用户能正常呼叫落地。但可能导致用户落地过期还能收到呼叫
    }
    
    AK_LOG_WARN << "RPC failed, query uid caller=" << caller_sip  << " phone=" << phone <<" err: " <<status.error_code() << ": " << status.error_message();
    return -1;
}

//异步接口
int PbxRpcClient::WriteCallHistory(AKCS_CALL_HISTORY* call_history, uint64_t msg_traceid)
{
    WriteCallHistoryRequest request;
    request.set_msg_traceid(msg_traceid);
    request.set_caller(call_history->caller_sip);
    request.set_callee(call_history->callee_sip);
    request.set_called(call_history->called_sip);
    request.set_caller_name(call_history->caller_name);
    request.set_start_time(call_history->start_time);
    request.set_answer_time(call_history->answer_time);
    request.set_bill_second(call_history->bill_second);
    request.set_freeswitch_node(call_history->freeswitch_node);
    request.set_caller_ops_node(call_history->caller_ops_node);
    request.set_callee_ops_node(call_history->callee_ops_node);
    request.set_group_call_list(call_history->group_call_list);
    request.set_call_trace_id(call_history->call_trace_id);
    request.set_callee_name(call_history->callee_name);
    request.set_is_ip_call(call_history->is_ip_call);
    request.set_db_delivery_uuid(call_history->db_delivery_uuid);
    

    AsyncCspbxrpcClientCall* call = new AsyncCspbxrpcClientCall;
    call->s_type_ = CSPBX_RPC_SERVER_TYPE::WRITE_CALL_HISTORY;
    call->write_callhistory_response_reader =
      stub_->PrepareAsyncWriteCallHistoryHandle(&call->context, request, &g_cspbx_rpc_cq_);
    call->write_callhistory_response_reader->StartCall();
    call->write_callhistory_response_reader->Finish(&call->write_callhistory_reply_, &call->status, (void*)call);
    return 0;

}

int PbxRpcClient::WriteCallHistory(AKCS_CALL_HISTORY* call_history, const std::string& msg_traceid_str)
{
    // 使用哈希函数将字符串转换为一个数值
    std::hash<std::string> hash_fn;
    size_t hash_value = hash_fn(msg_traceid_str);
    AK_LOG_INFO << "msg_traceid=" << msg_traceid_str << ",hash_value=" << hash_value;
    return WriteCallHistory(call_history, hash_value);
}
    
std::string PbxRpcClient::QueryLandlineNumber(const std::string &sip, int type, uint64_t msg_traceid, std::string &phone_code)
{
    QueryLandlineNumberRequest request;
    request.set_sip(sip);
    request.set_type(type);
    request.set_msg_traceid(msg_traceid);
    gpr_timespec timespec;
    timespec.tv_sec = 2;//设置同步阻塞时间为2秒
    timespec.tv_nsec = 0;
    timespec.clock_type = GPR_TIMESPAN;
    ClientContext context;
    context.set_deadline(timespec);
    QueryLandlineNumberReply query_landline_number_reply;
    Status status = stub_->QueryLandlineNumberHandle(&context, request, &query_landline_number_reply);
    if (status.ok())
    {
        std::string ret= query_landline_number_reply.ret();
        phone_code = query_landline_number_reply.phone_code();
        return ret;
    }
    else if ( status.error_code() == grpc::DEADLINE_EXCEEDED)//rpc超时，证明下游服务已经过载了
    {
        AK_LOG_WARN << "RPC DEADLINE_EXCEEDED,  sip=" << sip << " traceid=" << msg_traceid;
        //触发监控告警
        std::string worker_node = "cspbxrpc_";
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, "QueryLandlineNumber exceeded, the backend server is cspbxrpc", AKCS_MONITOR_ALARM_GRPC_TIMEOUT_CSPBXRPC);
        return "";
    }
    
    AK_LOG_WARN << "RPC failed, query uid caller=" << sip << " traceid=" << msg_traceid <<" err: " <<status.error_code() << ": " << status.error_message();
    return "";
}
    
std::string PbxRpcClient::QueryMainSiteSip(const std::string &sip, uint64_t msg_traceid)
{
    QueryMainSiteSipRequest request;
    request.set_sip(sip);
    request.set_msg_traceid(msg_traceid);
    gpr_timespec timespec;
    timespec.tv_sec = 2;//设置同步阻塞时间为2秒
    timespec.tv_nsec = 0;
    timespec.clock_type = GPR_TIMESPAN;
    ClientContext context;
    context.set_deadline(timespec);
    QueryMainSiteSipReply query_main_site_sip_reply;
    Status status = stub_->QueryMainSiteSipHandle(&context, request, &query_main_site_sip_reply);
    if (status.ok())
    {
        std::string ret= query_main_site_sip_reply.main_sip();
        return ret;
    }
    else if ( status.error_code() == grpc::DEADLINE_EXCEEDED)//rpc超时，证明下游服务已经过载了
    {
        AK_LOG_WARN << "RPC DEADLINE_EXCEEDED,  sip=" << sip << " traceid=" << msg_traceid;
        //触发监控告警
        std::string worker_node = "cspbxrpc_";
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, "QueryMainSiteSip exceeded, the backend server is cspbxrpc", AKCS_MONITOR_ALARM_GRPC_TIMEOUT_CSPBXRPC);
        return "";
    }
    
    AK_LOG_WARN << "RPC failed, query uid=" << sip << " traceid=" << msg_traceid <<" err: " <<status.error_code() << ": " << status.error_message();
    return "";
}


std::string PbxRpcClient::QuerySipInfo(const std::string &sip, uint64_t msg_traceid)
{
    QuerySipInfoRequest request;
    request.set_sip(sip);
    request.set_msg_traceid(msg_traceid);
    gpr_timespec timespec;
    timespec.tv_sec = 2;//设置同步阻塞时间为2秒
    timespec.tv_nsec = 0;
    timespec.clock_type = GPR_TIMESPAN;
    ClientContext context;
    context.set_deadline(timespec);
    QuerySipInfoReply reply;
    Status status = stub_->QuerySipInfoHandle(&context, request, &reply);
    if (status.ok())
    {
        return reply.name();
    }
    else if ( status.error_code() == grpc::DEADLINE_EXCEEDED)//rpc超时，证明下游服务已经过载了
    {
        AK_LOG_WARN << "RPC DEADLINE_EXCEEDED,  sip=" << sip << " traceid=" << msg_traceid;
        //触发监控告警
        std::string worker_node = "cspbxrpc_";
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, "QuerySipInfoHandle exceeded, the backend server is cspbxrpc", AKCS_MONITOR_ALARM_GRPC_TIMEOUT_CSPBXRPC);
        return "";
    }
    
    AK_LOG_WARN << "RPC failed, query uid=" << sip << " traceid=" << msg_traceid <<" err: " <<status.error_code() << ": " << status.error_message();
    return "";
}


//异步注册
void AsyncCompleteCsPbxRpc()
{
    void* got_tag;
    bool ok = false;

    // Block until the next result is available in the completion queue "cq".
    while (g_cspbx_rpc_cq_.Next(&got_tag, &ok)) {
        // The tag in this example is the memory location of the call object
        AsyncCspbxrpcClientCall* call = static_cast<AsyncCspbxrpcClientCall*>(got_tag); 

        // Verify that the request was completed successfully. Note that "ok"
        // corresponds solely to the request for updates introduced by Finish().
        GPR_ASSERT(ok);

        if (call->status.ok())
        {
            if(call->s_type_ == CSPBX_RPC_SERVER_TYPE::WRITE_CALL_HISTORY)
            {
                AK_LOG_INFO << "Async Call write history return, ret=" << call->write_callhistory_reply_.ret();
            }
            else if (call->s_type_ == CSPBX_RPC_SERVER_TYPE::HANGUP_APP)
            {
                AK_LOG_INFO << "Async hangup app return, ret=" << call->hangup_app_reply_.ret();
            }
        }
        else
        {
            AK_LOG_WARN << "RPC failed, please check rpc server";
        }
        delete call; 
    }
}



