/*
 *
 * Copyright 2015 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
#ifndef __CSPBX_RPC_CLIENT_H__
#define __CSPBX_RPC_CLIENT_H__

#include <iostream>
#include <memory>
#include <string>

#include <grpcpp/grpcpp.h>
#include <grpc/support/log.h>
#include <thread>
#include <unistd.h>
#include <grpcpp/grpcpp.h>

#include "AK.PBX.grpc.pb.h"
#include "AkLogging.h"
#include "AkcsMsgDef.h"
#include "AkcsCommonSt.h"
#include "PbxMsgDef.h"
#include "AkcsMonitor.h"


using grpc::Channel;
using grpc::ClientAsyncResponseReader;
using grpc::ClientContext;
using grpc::CompletionQueue;
using grpc::Status;


using AK::PBX::QueryUidStatusRequest;
using AK::PBX::QueryUidStatusReply;
using AK::PBX::WakeupAppRequest;
using AK::PBX::WakeupAppReply;
using AK::PBX::QueryLandlineStatusRequest;
using AK::PBX::QueryLandlineStatusReply;
using AK::PBX::WriteCallHistoryRequest;
using AK::PBX::WriteCallHistoryReply;
using AK::PBX::QueryLandlineNumberRequest;
using AK::PBX::QueryLandlineNumberReply;
using AK::PBX::QueryMainSiteSipRequest;
using AK::PBX::QueryMainSiteSipReply;
using AK::PBX::HangupAppRequest;
using AK::PBX::HangupAppReply;
using AK::PBX::QuerySipInfoRequest;
using AK::PBX::QuerySipInfoReply;

using AK::PBX::PbxRpcSrv;

class PbxRpcClient;
typedef std::shared_ptr<PbxRpcClient> PbxRpcClientPtr;

class PbxRpcClient {
  public:
      public:
    explicit PbxRpcClient(const std::string &srv_net/*ip:port*/) {
      channel_ = grpc::CreateChannel(srv_net, grpc::InsecureChannelCredentials());
      stub_ = PbxRpcSrv::NewStub(channel_);
    }

    std::shared_ptr<Channel> channel_;

    // Assembles the client's payload and sends it to the server.
    int QueryUidStatus(const std::string &uid, uint64_t msg_traceid, const std::string &caller, const std::string &original_callee, uint32_t app_type);
    int WakeupApp(uint64_t msg_traceid, const std::string &caller_sip, const std::string &callee_sip, const std::string &nick_name_location, uint32_t app_type, const std::string &xcaller, const std::string& timestamp, const std::string &x_name);
    void HangupApp(uint64_t msg_traceid, const std::string &caller_sip, const std::string &callee_sip, const std::string &nick_name_location, uint32_t app_type, const std::string &xcaller, const std::string &x_name);
    std::string QuerySipInfo(const std::string &sip, uint64_t msg_traceid);

    int WriteCallHistory(AKCS_CALL_HISTORY* call_history, uint64_t msg_traceid);
    int WriteCallHistory(AKCS_CALL_HISTORY* call_history, const std::string& msg_traceid);
    int QueryLandlineStatus(const std::string &caller_sip, const std::string &phone, uint64_t msg_traceid);

    std::string QueryLandlineNumber(const std::string &sip, int type, uint64_t msg_traceid, std::string &phone_code);
    std::string QueryMainSiteSip(const std::string &sip, uint64_t msg_traceid);

    // Out of the passed in Channel comes the stub, stored here, our view of the
    // server's exposed services.
    std::unique_ptr<PbxRpcSrv::Stub> stub_;
};

// The producer-consumer queue we use to communicate asynchronously with the
// Loop while listening for completed responses.
// Prints out the response from the server.
void AsyncCompleteCsPbxRpc();

// struct for keeping state and data information
// TODO,通过多态来处理AsyncClientCall的逻辑
struct AsyncCspbxrpcClientCall {
    
    CSPBX_RPC_SERVER_TYPE s_type_;
    // Container for the data we expect from the server.
    WriteCallHistoryReply write_callhistory_reply_;
    HangupAppReply hangup_app_reply_;
   
    // Context for the client. It could be used to convey extra information to
    // the server and/or tweak certain RPC behaviors.
    ClientContext context;

    // Storage for the status of the RPC upon completion.
    Status status;

    //ClientAsyncResponseReader<HelloReply> 客户端异步响应读取对象
    std::unique_ptr<ClientAsyncResponseReader<WriteCallHistoryReply>> write_callhistory_response_reader;
    std::unique_ptr<ClientAsyncResponseReader<HangupAppReply>> hangup_app_response_reader;
};



#endif
