#include <map>
#include <set>
#include <mutex>
#include "util.h"
#include "cspbx_rpc_client_mng.h"

PbxRpcClientMng* PbxRpcClientMng::pInstance_ = nullptr;

PbxRpcClientMng* PbxRpcClientMng::Instance()
{
	if (!pInstance_)
    {
		pInstance_ = new PbxRpcClientMng();
	}
	return pInstance_;
}

void PbxRpcClientMng::AddPbxRpcSrv(const std::string &cspbx_grpc_addr, const PbxRpcClientPtr& cspbx_rpc_cli)
{
    std::string logic_srv_id = "cspbxrpc_";
    std::string::size_type pos = cspbx_grpc_addr.find(":");
    logic_srv_id += cspbx_grpc_addr.substr(0, pos);

    std::lock_guard<std::mutex> lock(cspbx_rpc_clis_mutex_);
    cspbx_rpc_clis_.insert(std::pair<std::string, PbxRpcClientPtr>(logic_srv_id, cspbx_rpc_cli));
    AK_LOG_INFO << "add cspbx_rpc_client " << logic_srv_id;
} 

void PbxRpcClientMng::UpdatePbxRpcSrv(const std::set<std::string> &cspbx_rpc_addrs) 
{
	//TODO后面逻辑服务器数量多的时候,用两个set取差集加速处理
    std::lock_guard<std::mutex> lock(cspbx_rpc_clis_mutex_);
    for(const auto &rpc_addr : cspbx_rpc_addrs)
    {
        auto it = cspbx_rpc_clis_.find(rpc_addr);
        if(it == cspbx_rpc_clis_.end())
        {
        	std::string logic_srv_id="cspbxrpc_";
            std::string::size_type pos = rpc_addr.find(":");
        	logic_srv_id += rpc_addr.substr(0, pos);
            PbxRpcClientPtr call_cli_ptr(new PbxRpcClient(rpc_addr));
            cspbx_rpc_clis_.insert(std::pair<std::string, PbxRpcClientPtr>(logic_srv_id, call_cli_ptr));
            AK_LOG_INFO << "add cspbx_rpc_client " << logic_srv_id;
        }
        else
        {
            //如果没有改变，那么rpc客户端会自己重连
        }
    }
    
	//再检查下线的cspbx rpc srv
	if(cspbx_rpc_clis_.size() == cspbx_rpc_addrs.size())
    {
		return;
    }
	for (auto it = cspbx_rpc_clis_.begin(); it != cspbx_rpc_clis_.end();)
    {
        auto it2 = cspbx_rpc_addrs.find(it->first);
        if(it2 == cspbx_rpc_addrs.end())
        {
			AK_LOG_INFO << "del cspbx_rpc_client";
            cspbx_rpc_clis_.erase(it++);
        }
        else
        {
        	it++;
        }
    }
} 

PbxRpcClientPtr PbxRpcClientMng::GetRpcClientInstance(const std::string &logic_srv_id)
{
    std::lock_guard<std::mutex> lock(cspbx_rpc_clis_mutex_);
    auto it = cspbx_rpc_clis_.find(logic_srv_id);
    if(it == cspbx_rpc_clis_.end())
    {
    	AK_LOG_INFO << "cannot find [" << logic_srv_id << "] cxpbxrpc server";
    	return nullptr;
    }
    else
    {
        return cspbx_rpc_clis_[logic_srv_id];
    }
    return nullptr;
} 

PbxRpcClientPtr PbxRpcClientMng::GetRpcRandomClientInstance()
{    
    auto it = std::next(cspbx_rpc_clis_.begin(), current_index_.fetch_add(1) % cspbx_rpc_clis_.size());
    return it->second;
}
