#ifndef __CSCALL_RPC_CLIENT_CLIENT_MNG_H__
#define __CSCALL_RPC_CLIENT_CLIENT_MNG_H__
#include <list>
#include <string>
#include <map>
#include <mutex>
#include <boost/noncopyable.hpp>
#include "cspbx_rpc_client.h"

class PbxRpcClientMng : public boost::noncopyable
{
public:
    PbxRpcClientMng()
    {}
    ~PbxRpcClientMng()
    {}
	static PbxRpcClientMng* Instance();
    void AddPbxRpcSrv(const std::string &cspbx_rpc_addr, const PbxRpcClientPtr& cspbx_rpc_cli);
    void UpdatePbxRpcSrv(const std::set<std::string> &cspbx_rpc_addrs); 
    PbxRpcClientPtr GetRpcClientInstance(const std::string &logic_srv_id);
    PbxRpcClientPtr GetRpcRandomClientInstance();

private:
    static PbxRpcClientMng* pInstance_;
    std::mutex cspbx_rpc_clis_mutex_; 
    std::atomic<uint64_t> current_index_{0};
    std::map<std::string/*ip:port*/, PbxRpcClientPtr> cspbx_rpc_clis_;
};

#endif //__CSCALL_RPC_CLIENT_CLIENT_MNG_H__