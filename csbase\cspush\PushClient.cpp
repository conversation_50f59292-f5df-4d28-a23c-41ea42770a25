#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "json/json.h"
#include "PushClient.h"
#include <boost/algorithm/string.hpp>


CPushClient::CPushClient(evpp::EventLoop* loop,
                         const std::string& serverAddr/*ip:port*/,
                         const std::string& name)
    : client_(loop, serverAddr, name)
    , addr_(serverAddr)
{

    client_.SetConnectionCallback(
        std::bind(&CPushClient::OnConnection, this, std::placeholders::_1));
    client_.SetMessageCallback(
        std::bind(&CPushClient::OnMessage, this, std::placeholders::_1, std::placeholders::_2));
    client_.set_connecting_timeout(evpp::Duration(5.0));
    client_.set_auto_reconnect(true);
}

bool CPushClient::IsConnStatus()
{
    return connect_status_ == true;
}

std::string CPushClient::GetAddr()
{
    return addr_;
}

