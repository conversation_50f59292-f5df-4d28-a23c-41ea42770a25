#ifndef __PUSH_CLIENT_H__
#define __PUSH_CLIENT_H__

#include <evpp/tcp_client.h>
#include "AkLogging.h"
#include "evpp/tcp_conn.h"

namespace csmain
{
enum PushMsgType
{
    PUSH_MSG_TYPE_ONLY_ONLINE = -1,
    PUSH_MSG_TYPE_CALL = 0,
    PUSH_MSG_TYPE_ALARM,
    PUSH_MSG_TYPE_DEALALARM,
    PUSH_MSG_TYPE_MOTION,
    PUSH_MSG_TYPE_FORCE_LOGOUT,
    PUSH_MSG_TYPE_DELIVERY,
    PUSH_MSG_TYPE_TMPKEY,
    PUSH_MSG_TYPE_TEXT,
    PUSH_MSG_TYPE_DELIVERY_BOX, //用于JTS
    PUSH_MSG_TYPE_VOICE_MSG,
    PUSH_MSG_TYPE_YALE_BATTERY,
    PUSH_MSG_TYPE_TRIGGER_CSPUSH_TEST,//测试push服务是否正常
    PUSH_MSG_TYPE_EMERGENCY_NOTIFY,//一键开关门告警通知类型
    PUSH_MSG_TYPE_DORMAKABA_BATTERY,
    PUSH_MSG_TYPE_AKUBELA_LOCK_BATTERY, //家居锁电量通知
    PUSH_MSG_TYPE_LOCK_TRAILERROR_NOTICE, //锁连续试错通知
    PUSH_MSG_TYPE_LOCKDOWN_ON_NOTIFY, //lockdown开启通知
    PUSH_MSG_TYPE_LOCKDOWN_OFF_NOTIFY, //lockdown关闭通知
    PUSH_MSG_TYPE_ITEC_BATTERY, //Itec低电量通知
    PUSH_MSG_TYPE_MAILBOX_ARRIVAL_NOTICE, //信箱到达通知
    PUSH_MSG_TYPE_SMARTLOCK_DOORBELL_EVENT, //智能锁门铃事件通知
};

}
#define PUSH_SERVER_VER "1"

typedef std::map<std::string/*key*/, std::string/*value*/> AppOfflinePushKV;

class CPushClient;
typedef std::shared_ptr<CPushClient> PushClientPtr;

class CPushClient
{
public:
    CPushClient(evpp::EventLoop* loop,
                const std::string& serverAddr/*ip:port*/,
                const std::string& name);

    virtual ~CPushClient(){}
    void Start()
    {
        client_.Connect();
        client_.set_auto_reconnect(true);
    }

    void Stop()
    {
        client_.set_auto_reconnect(false);    
        if (connect_status_ == true)
        {
            client_.Disconnect();
            connect_status_ = false;
        }
    }


    void ReConnectByNewSeverAddr(const std::string& serverAddr)
    {
        addr_ = serverAddr;
        client_.ReconnectByNewServerAddr(serverAddr);
    }

    bool IsConnStatus();
    std::string GetAddr();

    virtual void OnMessage(const evpp::TCPConnPtr& conn, evpp::Buffer* buf)
    {

    }
    void OnConnection(const evpp::TCPConnPtr& conn)
    {
        if (conn->IsConnected())
        {
            connect_status_ = true;
            AK_LOG_INFO << "connect to push server " << addr_ << " successful.";
        }
        else
        {
            connect_status_ = false;
            AK_LOG_WARN << "disconnect to push server ";
        }
    }

    evpp::TCPClient client_;
    std::atomic<bool> connect_status_;
    std::string addr_;
};

#endif // __CSMAIN_PUSH_CLIENT_H__
