#ifndef _PUSH_CLIENT_MNG_H__
#define _PUSH_CLIENT_MNG_H__
#include <list>
#include <string>
#include <map>
#include <mutex>
#include <boost/noncopyable.hpp>
#include "PushClient.h"

typedef std::map<std::string, PushClientPtr>PushClientMap;

typedef std::function<PushClientPtr(const std::string&, evpp::EventLoop* loop)> CreatePushCallback;


class CPushClientMng : public boost::noncopyable
{
public:
    CPushClientMng()
    {}
    ~CPushClientMng()
    {}
    static CPushClientMng* Instance();
    void AddPushSrv(const std::string& push_addr, evpp::EventLoop* etcd_loop, const CreatePushCallback &cb);
    void UpdatePushSrv(const std::set<std::string>& push_addrs, evpp::EventLoop* etcd_loop, const CreatePushCallback &cb);
    const PushClientPtr GetPushSrv();
    const PushClientMap GetAllPushSrv();
    void AddOuterPushSrv(const PushClientPtr& push_cli)
    {
        outer_push_ = push_cli;
    }

private:
    void RemoveDisconnectCli();
    static CPushClientMng* pInstance_;
    
    std::mutex push_clis_mutex_;
    PushClientMap push_clis_;

    std::mutex push_clis_remove_mutex_;
    std::vector<PushClientPtr> push_remove_clis_;

    std::mutex push_clis_iter_mutex_;

    //全局的，用于test环境安卓没有办法推送的问题
    PushClientPtr outer_push_;

};


template <typename T>
void PushSrvConnInit(const std::set<std::string>& cspush_addrs, evpp::EventLoop* loop, T ClientType)
{
    for (const auto& cspush : cspush_addrs) //ip:port的形式
    {
        CPushClientMng::Instance()->AddPushSrv(cspush, loop, ClientType);
    }
}


#endif //__CSROUTE_PUSH_CLIENT_MNG_H__


