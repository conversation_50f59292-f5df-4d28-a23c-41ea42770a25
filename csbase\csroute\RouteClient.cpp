#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "json/json.h"
#include "RouteClient.h"
#include <boost/algorithm/string.hpp>
#include "AK.Server.pb.h"
#include "AkcsMsgDef.h"
#include "AkcsMonitor.h"
#include "AkcsCommonDef.h"
#include "RouteClient.h"

CRouteClient::CRouteClient(evpp::EventLoop* loop,
                           const std::string& serverAddr/*ip:port*/,
                           const std::string& name)
    : client_(loop, serverAddr, name)
    , addr_(serverAddr)
    , route_codec_(std::bind(&CRouteClient::OnMessage, this, std::placeholders::_1, std::placeholders::_2))
    , ping_status_(true)

{
    client_.SetConnectionCallback(
        std::bind(&CRouteClient::OnConnection, this, std::placeholders::_1));
    client_.SetMessageCallback(
        std::bind(&AkcsIpcMsgCodec::OnMessage, &route_codec_, std::placeholders::_1, std::placeholders::_2));
    client_.set_connecting_timeout(evpp::Duration(2.0));
    client_.set_auto_reconnect(true);
    loop->RunEvery(evpp::Duration(121.0), std::bind(&CRouteClient::onRoutePingCheckTimer, this));//121.0:保证csroute有两个周期的ping
}

void CRouteClient::Stop()
{
    if (connect_status_ == true)
    {
        client_.Disconnect();
        connect_status_ = false;
    }
    client_.set_auto_reconnect(false);
}

void CRouteClient::OnConnection(const evpp::TCPConnPtr& conn)
{
    if (conn->IsConnected())
    {
        AK_LOG_INFO << "connect to route server successful, " << conn->AddrToString();
        connect_status_ = true;
        //注册srv-id
        AK::Server::LogicSrvReg msg_logic_srv_reg;
        msg_logic_srv_reg.set_logic_srv_uid(logic_srv_id_);
        msg_logic_srv_reg.set_srv_type(logic_type_);
        CAkcsPdu pdu;
        pdu.SetMsgBody(&msg_logic_srv_reg);
        pdu.SetHeadLen(sizeof(PduHeader_t));
        pdu.SetVersion(50); //ver=50
        pdu.SetCommandId(AKCS_MSG_L2R_REG_UID_REQ);
        pdu.SetSeqNum(0);
        conn->Send(pdu.GetBuffer(), pdu.GetLength());
    }
    else //参考: CRouteClientMng::UpdateRouteSrv 的逻辑处理
    {
        AK_LOG_WARN << "disconnect to route server " << conn->AddrToString();        
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csmain", "csmain connect csroute error", AKCS_MONITOR_ALARM_MODULE_CONNECT_ERROR);

        if (connect_status_ == true)//对端先断开
        {
            connect_status_ = false;
            //modified by chenyc, 2019-05-10,因为逻辑服务器在etcd中，变成永久有效的了,所以不能再主动调用： client_.Disconnect(). 需要不断重试，
            // 只有在etcd下线的时候，才能通过stop调用，设置.set_auto_reconnect(false);
            //client_.Disconnect();//当对端主动关闭的时候,本段立马执行关闭.
        }
        else
        {
            //本端先断开的情况,也会回调这个函数,暂时不需要业务处理
        }
    }
}

void CRouteClient::OnRoutePing()
{
    ping_status_ = true;
}
void CRouteClient::onRoutePingCheckTimer()
{
    if ((ping_status_ == false) && (connect_status_ == true))
    {
        AK_LOG_WARN << "in one ping check loop, i donnot have received any ping msg from csroute, reconnect to csroute ";
        client_.Disconnect();
        client_.Connect();
        client_.set_auto_reconnect(true);
    }
    ping_status_ = false;
}

bool CRouteClient::IsConnStatus()
{
    return connect_status_ == true;
}

std::string CRouteClient::GetAddr()
{
    return addr_;
}

