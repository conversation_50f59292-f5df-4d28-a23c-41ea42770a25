#ifndef __ROUTE_CLIENT_H__
#define __ROUTE_CLIENT_H__

#include <evpp/tcp_client.h>
#include "AkcsIpcMsgCodec.h"
#include "AkcsMsgDef.h"
#include "AK.Base.pb.h"

class CRouteClient;
typedef std::shared_ptr<CRouteClient> RouteClientPtr;

class CRouteClient
{
public:
    CRouteClient(evpp::EventLoop* loop,
                 const std::string& serverAddr/*ip:port*/,
                 const std::string& name);

    virtual ~CRouteClient(){};
    void Start()
    {
        client_.Connect();
    }

    void Stop();
    void ReConnectByNewSeverAddr(const std::string& serverAddr)
    {
        addr_ = serverAddr;
        client_.ReconnectByNewServerAddr(serverAddr);
    }

    bool IsConnStatus();
    void OnRoutePing();
    void onRoutePingCheckTimer();
    std::string GetAddr();

    virtual void SetServerInfo(const std::string& id, AK::Base::LogicClientType type){};
    virtual void OnMessage(const evpp::TCPConnPtr& conn, const std::unique_ptr<CAkcsPdu>& pdu){};    

    AK::Base::LogicClientType logic_type_;
    std::string logic_srv_id_;    
    void OnConnection(const evpp::TCPConnPtr& conn);

private:
    evpp::TCPClient client_;
    std::string addr_;
    AkcsIpcMsgCodec route_codec_;
    std::atomic<bool> connect_status_;//与tcp服务器是否连接的状态标示符
    std::atomic<bool> ping_status_;//ping的状态标记
};

#endif // _ROUTE_CLIENT_H__
