#include <map>
#include <set>
#include <mutex>
#include "RouteClientMng.h"

CRouteClientMng* CRouteClientMng::pInstance_ = nullptr;

CRouteClientMng* CRouteClientMng::Instance()
{
    if (!pInstance_)
    {
        pInstance_ = new CRouteClientMng();
    }
    return pInstance_;
}

void CRouteClientMng::AddRouteSrv(const std::string& route_addr, evpp::EventLoop* loop, const CreateCallback &cb)
{
    RouteClientPtr route_cli_ptr = cb(route_addr, loop);
    route_cli_ptr->Start();
    std::lock_guard<std::mutex> lock(route_clis_mutex_);
    route_clis_.insert(std::pair<std::string, RouteClientPtr>(route_addr, route_cli_ptr));
}

void CRouteClientMng::UpdateRouteSrv(const std::set<std::string>& route_addrs, evpp::EventLoop* loop, const CreateCallback &cb)
{
    //TODO,2019-02-28,后面逻辑服务器数量多的时候,用两个set取差集加速处理
    std::lock_guard<std::mutex> lock(route_clis_mutex_);
    for (const auto& route_addr : route_addrs) //先检查新上线的route srv
    {
        auto it = route_clis_.find(route_addr);
        if (it == route_clis_.end())
        {
            AK_LOG_INFO << "add route_cli_ptr " << route_addr;
            RouteClientPtr route_cli_ptr = cb(route_addr, loop);
            route_cli_ptr->Start();
            route_clis_.insert(std::pair<std::string, RouteClientPtr>(route_addr, route_cli_ptr));
        }
        else//更新的tcp连接,也就是route_addr不变,但是对应的与csroute的tcp连接已经改变了,如果是csroute主动先断开,同时马上又拉起注册进etcd了,
            //此时本端还没来得及删除掉对应的pair,那么此时需要刷新
        {
            if (!it->second->IsConnStatus())
            {
                AK_LOG_INFO << "update route_cli_ptr, route_addr is:" << route_addr;
                //added by chenyc, 2019-05-24,由于etcd的key改成永久有效的，所以对于更新的tcp连接,不再需要处理了.后续改成续约的话,就要重新打开该开关
#if 0
                RouteClientPtr route_cli_ptr(new CRouteClient(loop, route_addr, "csmain client", logic_srv_id));
                route_cli_ptr->Start();
                route_clis_[route_addr] = route_cli_ptr;
#endif
            }
        }
    }
    //再检查下线的route srv
    if (route_clis_.size() == route_addrs.size())
    {
        return;
    }
    for (auto it = route_clis_.begin(); it != route_clis_.end();)
    {
        AK_LOG_INFO << "route_clis_ is " << route_clis_.size();
        auto it2 = route_addrs.find(it->first);
        if (it2 == route_addrs.end())
        {
            //AK_LOG_INFO << "del route_cli_ptr";
            //1、当通过etcd的watch回调到这里的时候,对端已经关闭tcp很长时间了,所以CRouteClient::OnConnection要增加
            //   client_.Disconnect();//当对端主动关闭的时候,本段立马执行关闭的动作
            //2、当对端并没有断开tcp连接，只是由于对端与etcd之间断开连接了,此时相当于本段主动断开连接的
            it->second->Stop();
            
            //先简单处理：等待3秒再来删除，不然路由线程和etcd线程同时处理会挂掉
            std::lock_guard<std::mutex> lock(route_clis_remove_mutex_);
            route_remove_clis_.push_back(it->second);
            loop->RunAfter(evpp::Duration(3.0), std::bind(&CRouteClientMng::RemoveDisconnectCli, this));
            
            route_clis_.erase(it++);
        }
        else
        {
            it++;
        }
    }
}

void CRouteClientMng::RemoveDisconnectCli()
{
    std::lock_guard<std::mutex> lock(route_clis_remove_mutex_);
    for(auto it = route_remove_clis_.begin(); it != route_remove_clis_.end(); it++)
    {
        AK_LOG_INFO << "Remote route route_cli_ptr:" << it->get()->GetAddr();
        it = route_remove_clis_.erase(it);
        if(it == route_remove_clis_.end())
        {
            break;
        }
    }
}
bool CRouteClientMng::CheckRouteNormal()
{
    std::lock_guard<std::mutex> lock(route_clis_mutex_);
    for(const auto& route_cli : route_clis_)
    {
        if (!route_cli.second->IsConnStatus())
        {
            return false;
        }
    }
    return true;
}
