#ifndef _ROUTE_CLIENT_MNG_H__
#define _ROUTE_CLIENT_MNG_H__
#include <list>
#include <string>
#include <map>
#include <mutex>
#include <boost/noncopyable.hpp>
#include "RouteClient.h"
#include "EtcdCliMng.h"

typedef std::function<RouteClientPtr(const std::string&, evpp::EventLoop* loop)> CreateCallback;


class CRouteClientMng : public boost::noncopyable
{
public:
    CRouteClientMng()
    {}
    ~CRouteClientMng()
    {}
    static CRouteClientMng* Instance();
    void AddRouteSrv(const std::string& route_addr, evpp::EventLoop* loop, const CreateCallback &cb);
    void UpdateRouteSrv(const std::set<std::string>& route_addrs, evpp::EventLoop* loop, const CreateCallback &cb);
    bool CheckRouteNormal();
private:
    void RemoveDisconnectCli();
    static CRouteClientMng* pInstance_;
    std::mutex route_clis_mutex_;
    std::map<std::string/*csroute的ip:port*/, RouteClientPtr> route_clis_;

    std::mutex route_clis_remove_mutex_;
    std::vector<RouteClientPtr> route_remove_clis_;
};

/*
template <typename T>
void UpdateRouteSrvList(T ClientType, CAkEtcdCliManager* etcd, std::shared_ptr<evpp::EventLoop> &loop)
{
    std::set<std::string> csroute_addrs;
    if (etcd->GetAllRouteSrvs(csroute_addrs) == 0)
    {
        AK_LOG_INFO << "UpdateRouteSrv begin";
        CRouteClientMng::Instance()->UpdateRouteSrv(csroute_addrs,  loop, 
            std::bind(ClientType, std::placeholders::_1, std::placeholders::_2));
    }
}
*/

template <typename T>
void RouteSrvConnInit(const std::set<std::string>& csroute_addrs, evpp::EventLoop* loop, T ClientType) {
    for (const auto& csroute : csroute_addrs) //ip:port的形式
    {
        CRouteClientMng::Instance()->AddRouteSrv(csroute, loop, ClientType);
    }

}




#endif //_ROUTE_CLIENT_MNG_H__


