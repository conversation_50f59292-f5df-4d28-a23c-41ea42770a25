#include "VideoRecordClient.h"
#include <grpcpp/impl/codegen/status_code_enum.h> 

CompletionQueue g_csvideorecord_rpc_cq_;

void VideoRecordRpcClient::StartVideoRecord(const std::string &site, const std::string &mac)
{
    StartVideoRecordRequest request;
    request.set_site(site);
    request.set_mac(mac);

    AsyncCsVideoRecordRpcClientCall* call = new AsyncCsVideoRecordRpcClientCall;
    call->s_type_ = CSVIDEORECORD_RPC_SERVER_TYPE::START_VIDEO_RECORD;
    call->start_video_record_response_reader = stub_->PrepareAsyncStartVideoRecordHandle(&call->context, request, &g_csvideorecord_rpc_cq_);
    call->start_video_record_response_reader->StartCall();
    call->start_video_record_response_reader->Finish(&call->start_video_record_reply_, &call->status, (void*)call);
    return;
}

void VideoRecordRpcClient::StopVideoRecord(const std::string &site, const std::string &mac)
{
    StopVideoRecordRequest request;
    request.set_site(site);
    request.set_mac(mac);

    AsyncCsVideoRecordRpcClientCall* call = new AsyncCsVideoRecordRpcClientCall;
    call->s_type_ = CSVIDEORECORD_RPC_SERVER_TYPE::STOP_VIDEO_RECORD;
    call->stop_video_record_response_reader = stub_->PrepareAsyncStopVideoRecordHandle(&call->context, request, &g_csvideorecord_rpc_cq_);
    call->stop_video_record_response_reader->StartCall();
    call->stop_video_record_response_reader->Finish(&call->stop_video_record_reply_, &call->status, (void*)call);
    return;
}

//异步注册
void AsyncCompleteCsVideoRecordRpc()
{
    void* got_tag;
    bool ok = false;

    // Block until the next result is available in the completion queue "cq".
    while (g_csvideorecord_rpc_cq_.Next(&got_tag, &ok)) {
        // The tag in this example is the memory location of the call object
        AsyncCsVideoRecordRpcClientCall* call = static_cast<AsyncCsVideoRecordRpcClientCall*>(got_tag); 
        
        // Verify that the request was completed successfully. Note that "ok"
        // corresponds solely to the request for updates introduced by Finish().
        GPR_ASSERT(ok);
        
        if (call->status.ok())
        {
            if(call->s_type_ == CSVIDEORECORD_RPC_SERVER_TYPE::START_VIDEO_RECORD)
            {
                AK_LOG_INFO << "Async start video record return, ret = " << call->start_video_record_reply_.ret();
            }
            else if (call->s_type_ == CSVIDEORECORD_RPC_SERVER_TYPE::STOP_VIDEO_RECORD)
            {
                AK_LOG_INFO << "Async stop video record return, ret = " << call->stop_video_record_reply_.ret();
            }
        }
        else
        {
            AK_LOG_WARN << "RPC failed, please check rpc server";
        }
        delete call; 
    }
}