/*
 *
 * Copyright 2015 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
#ifndef __CSVIDEORECORD_RPC_CLIENT_H__
#define __CSVIDEORECORD_RPC_CLIENT_H__

#include <iostream>
#include <memory>
#include <string>

#include <grpcpp/grpcpp.h>
#include <grpc/support/log.h>
#include <thread>
#include <unistd.h>
#include <grpcpp/grpcpp.h>

#include "AkLogging.h"
#include "AkcsMsgDef.h"
#include "AkcsCommonSt.h"
#include "AkcsMonitor.h"
#include "AK.VideoRecord.grpc.pb.h"

using grpc::Channel;
using grpc::ClientAsyncResponseReader;
using grpc::ClientContext;
using grpc::CompletionQueue;
using grpc::Status;

using AK::VideoRecord::VideoRecordRpcSrv;
using AK::VideoRecord::StartVideoRecordRequest;
using AK::VideoRecord::StartVideoRecordReply;
using AK::VideoRecord::StopVideoRecordRequest;
using AK::VideoRecord::StopVideoRecordReply;

class VideoRecordRpcClient;
typedef std::shared_ptr<VideoRecordRpcClient> VideoRecordRpcClientPtr;

class VideoRecordRpcClient {
  public:
      public:
    explicit VideoRecordRpcClient(const std::string &srv_net) {
      channel_ = grpc::CreateChannel(srv_net, grpc::InsecureChannelCredentials());
      stub_ = VideoRecordRpcSrv::NewStub(channel_);
    }

    std::shared_ptr<Channel> channel_;

    void StartVideoRecord(const std::string &site, const std::string &mac);
    void StopVideoRecord(const std::string &site, const std::string &mac);

    std::unique_ptr<VideoRecordRpcSrv::Stub> stub_;
};

void AsyncCompleteCsVideoRecordRpc();

struct AsyncCsVideoRecordRpcClientCall {
    
    CSVIDEORECORD_RPC_SERVER_TYPE s_type_;
    // Container for the data we expect from the server.
    StartVideoRecordReply start_video_record_reply_;
    StopVideoRecordReply stop_video_record_reply_;
   
    // Context for the client. It could be used to convey extra information to
    // the server and/or tweak certain RPC behaviors.
    ClientContext context;

    // Storage for the status of the RPC upon completion.
    Status status;

    //ClientAsyncResponseReader<HelloReply> 客户端异步响应读取对象
    std::unique_ptr<ClientAsyncResponseReader<StartVideoRecordReply>> start_video_record_response_reader;
    std::unique_ptr<ClientAsyncResponseReader<StopVideoRecordReply>> stop_video_record_response_reader;
};

#endif