#include <map>
#include <set>
#include <mutex>
#include "util.h"
#include "VideoRecordClientMng.h"

VideoRecordClientMng* VideoRecordClientMng::pInstance_ = nullptr;

VideoRecordClientMng* VideoRecordClientMng::Instance()
{
	if (!pInstance_)
    {
		pInstance_ = new VideoRecordClientMng();
	}
	return pInstance_;
}

void VideoRecordClientMng::AddVideoRecordRpcSrv(const std::string &csvideorecord_grpc_addr, const VideoRecordRpcClientPtr& rpc_cli_ptr)
{
    std::string logic_srv_id = "csvideorecord_";
    std::string::size_type pos = csvideorecord_grpc_addr.find(":");
    logic_srv_id += csvideorecord_grpc_addr.substr(0, pos);

    std::lock_guard<std::mutex> lock(csvideorecord_rpc_clis_mutex_);
    csvideorecord_rpc_clis_.insert(std::pair<std::string, VideoRecordRpcClientPtr>(logic_srv_id, rpc_cli_ptr));
    AK_LOG_INFO << "add csvideorecord_rpc_client " << logic_srv_id;
} 

void VideoRecordClientMng::UpdateVideoRecordRpcSrv(const std::set<std::string> &csvideorecord_rpc_addrs) 
{
	//TODO后面逻辑服务器数量多的时候,用两个set取差集加速处理
    std::lock_guard<std::mutex> lock(csvideorecord_rpc_clis_mutex_);
    for (const auto &rpc_addr : csvideorecord_rpc_addrs)
    {
        auto it = csvideorecord_rpc_clis_.find(rpc_addr);
        if (it == csvideorecord_rpc_clis_.end())
        {
        	std::string logic_srv_id = "csvideorecord_";
            std::string::size_type pos = rpc_addr.find(":");
        	logic_srv_id += rpc_addr.substr(0, pos);

            auto rpc_cli_ptr = std::make_shared<VideoRecordRpcClient>(rpc_addr);
            csvideorecord_rpc_clis_.insert(std::pair<std::string, VideoRecordRpcClientPtr>(logic_srv_id, rpc_cli_ptr));
            AK_LOG_INFO << "add csvideorecord_rpc_client " << logic_srv_id;
        }
        else
        {
            //如果没有改变，那么rpc客户端会自己重连
        }
    }
    
	//再检查下线的csvideorecord rpc srv
	if(csvideorecord_rpc_clis_.size() == csvideorecord_rpc_addrs.size())
    {
		return;
    }
	for (auto it = csvideorecord_rpc_clis_.begin(); it != csvideorecord_rpc_clis_.end();)
    {
        auto it2 = csvideorecord_rpc_addrs.find(it->first);
        if(it2 == csvideorecord_rpc_addrs.end())
        {
			AK_LOG_INFO << "del csvideorecord_rpc_client";
            csvideorecord_rpc_clis_.erase(it++);
        }
        else
        {
        	it++;
        }
    }
}

VideoRecordRpcClientPtr VideoRecordClientMng::GetRpcClientInstance(const std::string &logic_srv_id)
{
    std::lock_guard<std::mutex> lock(csvideorecord_rpc_clis_mutex_);
    auto it = csvideorecord_rpc_clis_.find(logic_srv_id);
    if(it == csvideorecord_rpc_clis_.end())
    {
    	AK_LOG_INFO << "cannot find [" << logic_srv_id << "] csvideorecord server";
    	return nullptr;
    }
    else
    {
        return csvideorecord_rpc_clis_[logic_srv_id];
    }
    return nullptr;
}

std::pair<std::string, VideoRecordRpcClientPtr> VideoRecordClientMng::GetRpcRandomClientInstance()
{
    // 使用 std::next 获取当前索引的迭代器
    auto it = std::next(csvideorecord_rpc_clis_.begin(), current_index_.fetch_add(1) % csvideorecord_rpc_clis_.size());
    
    // 返回 key 和 value 的 pair
    return {it->first, it->second};
}