#ifndef __CSVIDEORECORD_RPC_CLIENT_MNG_H__
#define __CSVIDEORECORD_RPC_CLIENT_MNG_H__

#include <string>
#include <map>
#include <mutex>
#include <boost/noncopyable.hpp>
#include "VideoRecordClient.h"

class VideoRecordClientMng : public boost::noncopyable
{
public:
    VideoRecordClientMng() {};
    ~VideoRecordClientMng() {};
    static VideoRecordClientMng* Instance();
    void AddVideoRecordRpcSrv(const std::string &csvideorecord_grpc_addr, const VideoRecordRpcClientPtr& cspbx_rpc_cli);
    void UpdateVideoRecordRpcSrv(const std::set<std::string> &csvideorecord_grpc_adds); 
    VideoRecordRpcClientPtr GetRpcClientInstance(const std::string &logic_srv_id);
    std::pair<std::string, VideoRecordRpcClientPtr> GetRpcRandomClientInstance();
private:
    static VideoRecordClientMng* pInstance_;
    std::mutex csvideorecord_rpc_clis_mutex_; 
    std::atomic<uint64_t> current_index_{0};
    std::map<std::string/*ip:port*/, VideoRecordRpcClientPtr> csvideorecord_rpc_clis_;
};

#endif