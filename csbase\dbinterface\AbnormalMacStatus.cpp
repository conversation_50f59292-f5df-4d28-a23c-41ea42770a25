#include <sstream>
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/InterfaceComm.h"
#include "AbnormalMacStatus.h"
#include <string.h>
#include "AkLogging.h"

const int KMonthToSecond = 60 * 60 * 24 * 30;

namespace dbinterface{
AbnormalMacStatus::AbnormalMacStatus()
{

}

AbnormalMacStatus::~AbnormalMacStatus()
{

}

int AbnormalMacStatus::InsertAbnormalMacStatus(const std::string& mac, const std::string& ip, uint32_t dclient_ver, const std::string& sw_ver)
{
    int num = 0;
    int time_diff = 0;
    std::stringstream streamSQL;
    streamSQL << "SELECT Num,(now() - LastAbnormalTime) as TimeDiff FROM AbnormalMacStatus WHERE MAC= '"//时间戳也需要查出来。
              << mac
              << "'";
    
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    if (query.MoveToNextRow())
    {
       num = ATOI(query.GetRowData(0));
       time_diff = ATOI(query.GetRowData(1));//time_diff表示当前距上一次发生异常的时间差,单位s
       if (time_diff - KMonthToSecond >= 0)//策略: 如果上一次的异常已经是1个月之前的了,判断为正常的设备降级处理现象
       {
            num = 0;//异常次数回退
            char sql[512] = {0};
            ::snprintf(sql, 512, "UPDATE AbnormalMacStatus SET LastAbnormalTime = now(), Num = 1 where MAC= '%s'",mac.c_str());
            tmp_conn->Execute(sql);
       }
       else
       {
            char sql[512] = {0};
            ::snprintf(sql, 512, "UPDATE AbnormalMacStatus SET LastAbnormalTime = now(), Num = Num + 1 where MAC= '%s'",mac.c_str());
            tmp_conn->Execute(sql);
       }
       
    }
    else
    {
        char sql[512] = {0};
        ::snprintf(sql, 512, "INSERT INTO AbnormalMacStatus(`MAC`,`DclientVer`,`SWVer`,`LastAbnormalTime`,`IP`,`Num`) VALUES ('%s', '%u', '%s', now(), '%s', 1)",
                   mac.c_str(), dclient_ver, sw_ver.c_str(),ip.c_str());
        tmp_conn->Execute(sql);
    }
    
    ReleaseDBConn(conn);
    return num + 1;
}


}


