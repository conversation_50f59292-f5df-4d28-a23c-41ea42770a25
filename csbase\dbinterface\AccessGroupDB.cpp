#include <sstream>
#include <string.h>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "AkLogging.h"
#include "AccessGroupDB.h"
#include "UserAccessGroup.h"
#include "util.h"
#include "dbinterface/resident/AmenityDevice.h"
#include "dbinterface/PubDevMngList.h"
#include "ConnectionManager.h"

namespace dbinterface
{


AccessGroup::AccessGroup()
{

}

int AccessGroup::HaveDefaultAG(const std::string& account, int dev_unit_id)
{  
    std::stringstream sql;
    sql << "select UnitID from AccessGroup G left join AccountAccess  A on A.AccessGroupID=G.ID where A.Account='"
        << account
        << "';";
    int have_default_ag = 0;
    
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return have_default_ag;
    } 
    CRldbQuery query(tmp_conn);
    
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        int unit_id =  ATOI(query.GetRowData(0));
        if(unit_id > 0)
        {
            //公共设备或同楼栋设备
            if(dev_unit_id == 0 || unit_id == dev_unit_id)
            {
                have_default_ag = 1;
            }
        }
    }
    
    ReleaseDBConn(conn);
    return have_default_ag;
}


int AccessGroup::GetUserAGDeviceList(const std::string& account, std::map<std::string, int> &mac_relay, std::map<std::string, int> &mac_serelay)
{
    if (account.length() == 0)
    {
        return 0;
    }
    std::stringstream sql1,sql2;
    
    sql1 << "select MAC,Relay,SecurityRelay from UserAccessGroupDevice  where UserAccessGroupID in (select ID from UserAccessGroup where Account='"
        << account
        << "');";
    
    sql2 << "select MAC,Relay,SecurityRelay from AccessGroupDevice where AccessGroupID in (select AccessGroupID From AccountAccess where Account='"
        << account
        << "');";
    

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    } 
    CRldbQuery query(tmp_conn);
    
    query.Query(sql1.str());
    while (query.MoveToNextRow())
    { 
        int relay = 0;
        int serelay = 0;
        char mac[32];
        Snprintf(mac, sizeof(mac), query.GetRowData(0));
        relay =  ATOI(query.GetRowData(1));
        serelay =  ATOI(query.GetRowData(2));
        mac_relay[mac] = relay | mac_relay[mac];
        mac_serelay[mac] = serelay | mac_serelay[mac];
    }

    query.Query(sql2.str());
    while (query.MoveToNextRow())
    {
        int relay = 0;
        int serelay = 0;
        char mac[32];
        Snprintf(mac, sizeof(mac), query.GetRowData(0));
        relay =  ATOI(query.GetRowData(1));
        serelay =  ATOI(query.GetRowData(2));
        mac_relay[mac] = relay | mac_relay[mac];
        mac_serelay[mac] = serelay | mac_serelay[mac];
    }

    ReleaseDBConn(conn);
    return 0;    
}

int AccessGroup::GetMngIDByAgID(int id)
{
    int mng_id = 0;
    std::stringstream sql;
    sql << "select CommunityID " 
        << "from AccessGroup where ID = "
        << id;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        mng_id = ATOI(query.GetRowData(0));
    }
        
    ReleaseDBConn(conn);
    return mng_id;
}

int AccessGroup::GetAgRelay(const std::string& mac,int id)
{
    int relay = -1;
    std::stringstream sql;
    sql << "select Relay "
        << "from AccessGroupDevice where AccessGroupID = "
        << id
        << " AND MAC = '"
        << mac
        << "'";
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        relay = ATOI(query.GetRowData(0));
    }

    ReleaseDBConn(conn);
    return relay;
}

bool AccessGroup::IsDefaultAG(const int ag_id)
{
    bool is_default_ag = false;
    std::stringstream sql;
    sql << "select UnitID from AccessGroup where ID = "
        << ag_id;
    
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return false;
    } 
    CRldbQuery query(tmp_conn);
    
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        int unit_id =  ATOI(query.GetRowData(0));
        if(unit_id > 0)
        {
            is_default_ag = true;
        }
    }
    
    ReleaseDBConn(conn);
    return is_default_ag;
}

//检查设备给定ID的权限组中是否包含某个relay,传入原有权限组ID列表，通过"/"分隔;传入relay字段代表设备的第几个relay
std::string AccessGroup::GetValidRelaySchedule(const std::string &mac, const std::string& relay_schedule, unsigned int relay_index)
{
    std::string valid_schedule;
    if(relay_index == 0)
    {
        AK_LOG_INFO << "pass relay_index wrong, val is 0";
        return valid_schedule;
    }

    std::set<std::string> schedules;
    SplitString(relay_schedule, "/", schedules);

    for(const auto &schedule : schedules) 
    {
        int relay_val = -1;
        if (dbinterface::AccessGroup::IsDefaultAG(ATOI(schedule.c_str())))
        {
            valid_schedule += schedule;
            valid_schedule += "/";
            continue;
        }
        relay_val = dbinterface::AccessGroup::GetAgRelay(mac, ATOI(schedule.c_str()));
        if (relay_val <= 0) {
            continue;
        }
        //判断当前relay是否在权限组中
        if ((relay_val >> (relay_index - 1)) & 1) {
            valid_schedule += schedule;
            valid_schedule += "/";
        }
    }
    return valid_schedule;

}


/*获取公共设备包含的权限组*/
void AccessGroup::GetPubMacAccessGroupList(DEVICE_SETTING* dev, AccessGroupInfoPtrList &list)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }

    std::stringstream str_sql;
    str_sql << "select A.ID,A.CommunityID,A.SchedulerType,A.DateFlag,A.BeginTime,"
        << "A.EndTime,A.StartTime,A.StopTime,D.Relay,DATE_FORMAT(A.BeginTime,'%Y%m%d') as daystart,DATE_FORMAT(A.EndTime,'%Y%m%d') as dayend, "
        << " A.Name,D.SecurityRelay From AccessGroup A left join AccessGroupDevice D on A.ID=D.AccessGroupID where D.Mac='" << dev->mac << "'";
           
    CRldbQuery query(tmp_conn);
    query.Query(str_sql.str());
    while (query.MoveToNextRow())
    {
        //防止出现脏数据影响真实的数据
        if (ATOUI(query.GetRowData(1)) != dev->manager_account_id)
        {
            AK_LOG_WARN << "Is have Dirty data. AccessGroupDevice table, mac:" << dev->mac << " dev community is:" << 
            dev->manager_account_id << "  AccessGroup community is:" << query.GetRowData(1);
            continue;
        }
        AccessGroupInfoPtr ag = std::make_shared<AccessGroupInfo>();
        ag->id_ = ATOI(query.GetRowData(0));
        ag->community_id_ = ATOI(query.GetRowData(1));
        ag->scheduler_type_ = ATOI(query.GetRowData(2));
        ag->date_flag_ = ATOI(query.GetRowData(3));
        Snprintf(ag->day_start_, sizeof(ag->day_start_), query.GetRowData(4));
        Snprintf(ag->day_end_, sizeof(ag->day_end_), query.GetRowData(5));
        Snprintf(ag->time_start_, sizeof(ag->time_start_), query.GetRowData(6));
        Snprintf(ag->time_end_, sizeof(ag->time_end_), query.GetRowData(7));        
        ag->relay_ = ATOI(query.GetRowData(8));
        Snprintf(ag->day_start_for_ymd_, sizeof(ag->day_start_for_ymd_), query.GetRowData(9));
        Snprintf(ag->day_end_for_ymd_, sizeof(ag->day_end_for_ymd_), query.GetRowData(10));
        Snprintf(ag->name_, sizeof(ag->name_), query.GetRowData(11));     
        ag->security_relay_ = ATOI(query.GetRowData(12));
        
        list.push_back(ag);
    }
    ReleaseDBConn(conn);    
}

/*
building 设备默认权限组：AccessGroup UnitID 与该设备unit_id 匹配的权限组
最外层设备：AccessGroup CommunityID 该设备manager_account_id 匹配且 UnitID 不为 0 的权限组
*/
void AccessGroup::GetDefaultAccessGroupList(DEVICE_SETTING* dev, AccessGroupInfoPtrList &list)
{    
    if(dbinterface::AmenityDevice::IsRemoveDefaultAccess(dev->uuid))
    {
        AK_LOG_INFO << "device remove default access. mac: " << dev->mac;
        return;
    }
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }

    std::stringstream str_sql;
    if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
        str_sql << "select ID,CommunityID,Name,UnitID From AccessGroup where UnitID=" << dev->unit_id;
    }
    else if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
    {   
        
        str_sql << "select ID,CommunityID,Name,UnitID From AccessGroup where CommunityID=" << dev->manager_account_id << " and UnitID!=0;";
    }

    CRldbQuery query(tmp_conn);
    query.Query(str_sql.str());
    while (query.MoveToNextRow())
    {
        AccessGroupInfoPtr ag = std::make_shared<AccessGroupInfo>();
        ag->id_ = ATOI(query.GetRowData(0));
        ag->community_id_ = ATOI(query.GetRowData(1));
        Snprintf(ag->name_, sizeof(ag->name_), query.GetRowData(2));
        ag->unit_id_ = ATOI(query.GetRowData(3));
        
        //以下是全开的默认配置
        ag->scheduler_type_ = SchedType::DAILY_SCHED;
        ag->date_flag_ = 127;
        Snprintf(ag->day_start_, sizeof(ag->day_start_), "");
        Snprintf(ag->day_end_, sizeof(ag->day_end_), "");
        Snprintf(ag->time_start_, sizeof(ag->time_start_), "00:00:00");
        Snprintf(ag->time_end_, sizeof(ag->time_end_), "23:59:59");  

        int default_relay = 0;
        GetValueByRelay(dev->relay, default_relay);
        ag->relay_ = default_relay;

        int default_security_relay = 0;
        GetValueByRelay(dev->security_relay, default_security_relay);
        ag->security_relay_ = default_security_relay;

        list.push_back(ag);
    }
    ReleaseDBConn(conn);      
}

int AccessGroup::GetMacListByAccessGroupID(uint32_t id, std::set<std::string> &mac_set)
{
    int unitid = 0;
    int community_id = 0;
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::stringstream str_sql;
    str_sql << "select Mac from AccessGroupDevice where AccessGroupID =" << id;
           
    CRldbQuery query(tmp_conn);
    query.Query(str_sql.str());
    int is_default = 1;
    while (query.MoveToNextRow())
    {
        std::string mac = query.GetRowData(0);
        mac_set.insert(mac);
        is_default = 0;
    }

    if (is_default)
    {
        str_sql.str("");
        str_sql << "select UnitID,CommunityID From AccessGroup where ID=" << id;

        query.Query(str_sql.str());
        if (query.MoveToNextRow())
        {
            unitid = ATOI(query.GetRowData(0));
            community_id = ATOI(query.GetRowData(1));
        }        
    }
    ReleaseDBConn(conn);

    if (unitid > 0)
    {
        ResidentDeviceList dev_list;
        dbinterface::ResidentDevices::GetPubDevList(community_id, dev_list);
        dbinterface::ResidentDevices::GetDepartmentDevList(unitid, dev_list);
        for (auto cur_dev : dev_list)
        {
            if(!dbinterface::AmenityDevice::IsRemoveDefaultAccess(cur_dev.uuid))
            {
                mac_set.insert(cur_dev.mac);
            }
        }
    }

    return 0;
}

/*获取社区公共用户关联的设备*/
void AccessGroup::GetPubUserAccessGroupDevList(USER_TYPE user_type, const std::vector<uint32_t> &userlist, 
    std::set<std::string> &mac_set)
{
    int community_id = 0;
    std::set<int> unit_set;
    int size = userlist.size();
    if (size <= 0 )
    {
        return;
    }

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }
    
    std::string accounts = ListToSeparatedFormatString(userlist);
    std::stringstream stream_sql;
    if (user_type == USER_TYPE::STAFF)
    {
        stream_sql << "select Mac from AccessGroupDevice where AccessGroupID in (select AccessGroupID From StaffAccess where StaffID in(" << accounts  << "))";
    }
    else if (user_type == USER_TYPE::DELIVERY)
    {
        stream_sql << "select Mac from AccessGroupDevice where AccessGroupID in (select AccessGroupID From DeliveryAccess where DeliveryID in(" << accounts  << "))";
    }
    else 
    {
        AK_LOG_WARN << "GetPubUserAccessGroupDevList, error user type=" << user_type;
        ReleaseDBConn(conn);
        return;
    }
           
    CRldbQuery query(tmp_conn);
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        mac_set.insert(query.GetRowData(0));
    }

    //获取默认权限组
    stream_sql.str("");
    if (user_type == USER_TYPE::STAFF)
    {
        stream_sql << "select CommunityID, UnitID from AccessGroup G left join StaffAccess  A on A.AccessGroupID=G.ID where A.StaffID in(" << accounts << ")";
    }
    else if (user_type == USER_TYPE::DELIVERY)
    {
        stream_sql << "select CommunityID, UnitID from AccessGroup G left join DeliveryAccess  A on A.AccessGroupID=G.ID where A.DeliveryID in(" << accounts << ")";
    }
    
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        community_id = ATOI(query.GetRowData(0));
        if ( ATOI(query.GetRowData(1)) > 0 )
        {
            unit_set.insert(ATOI(query.GetRowData(1)));
        }
    }

    ReleaseDBConn(conn);

    for (auto unitid : unit_set)
    {
        ResidentDeviceList dev_list;
        dbinterface::ResidentDevices::GetPubDevList(community_id, dev_list);
        dbinterface::ResidentDevices::GetDepartmentDevList(unitid, dev_list);
        for (auto cur_dev : dev_list)
        {
            mac_set.insert(cur_dev.mac);
        }
    }
}


/*获取社区家庭用户关联的公共设备, 包含默认权限组*/
void AccessGroup::GetAccessGroupDevListByUser(const std::vector<std::string> &userlist, std::set<std::string> &mac_set)
{
    int community_id = 0;
    std::set<int> unit_set;
    int size = userlist.size();
    if (size <= 0 )
    {
        return;
    }
    
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }

    //查询公共设备
    std::string accounts = ListToSeparatedFormatString(userlist);
    std::stringstream stream_sql;
    stream_sql << "select Mac from AccessGroupDevice where AccessGroupID in "
        << "(select AccessGroupID From AccountAccess where Account in (" << accounts  << "))";
    
    CRldbQuery query(tmp_conn);
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        mac_set.insert(query.GetRowData(0));
    }

    //获取默认权限组
    stream_sql.str("");
    stream_sql << "select CommunityID, UnitID from AccessGroup G left join AccountAccess  A on A.AccessGroupID=G.ID where A.Account in(" << accounts << ")";
    
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        community_id = ATOI(query.GetRowData(0));
        if ( ATOI(query.GetRowData(1)) > 0 )
        {
            unit_set.insert(ATOI(query.GetRowData(1)));
        }
    }    

    ReleaseDBConn(conn);
    
    for (auto unitid : unit_set)
    {
        ResidentDeviceList dev_list;
        dbinterface::ResidentDevices::GetPubDevList(community_id, dev_list);
        dbinterface::ResidentDevices::GetDepartmentDevList(unitid, dev_list);
        for (auto cur_dev : dev_list)
        {
            mac_set.insert(cur_dev.mac);
        }
    }
}

int AccessGroup::GetCommunityIDByAccessGroup(unsigned int access_group_id, unsigned int &community_id)
{
    std::stringstream stream_sql;
    stream_sql << "select CommunityID from AccessGroup where ID =" << access_group_id;
    
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);

    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        community_id = ATOI(query.GetRowData(0));
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }

    ReleaseDBConn(conn);
    return 0;
}

void AccessGroup::GetDevDefaultAccessGroupIDList(const ResidentDev& dev, std::vector<int>& access_group_id_list)
{
    GET_DB_CONN_ERR_RETURN(conn,)
    CRldb* tmp_conn = conn.get();

    std::stringstream str_sql;
    if (dev.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
        str_sql << "select ID From AccessGroup where UnitID=" << dev.unit_id;
    }
    else if (dev.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
    {
        str_sql << "select ID From AccessGroup where CommunityID=" << dev.project_mng_id << " and UnitID!=0;";
    }

    CRldbQuery query(tmp_conn);
    query.Query(str_sql.str());
    while (query.MoveToNextRow())
    {
        int ag_id = ATOI(query.GetRowData(0));
        access_group_id_list.push_back(ag_id);
    }
    return;
}

int AccessGroup::GetAgIDListByCommunityID(unsigned int community_id, std::set<int>& ag_id_list)
{
    std::stringstream stream_sql;
    stream_sql << "select ID from AccessGroup where CommunityID = " << community_id;
    
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());

    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        ag_id_list.insert(ATOI(query.GetRowData(0)));
    }

    return 0;
}

//获取用户有权限的relay值
void AccessGroup::GetUserAGRelayByMac(const std::string& account, const std::string& mac, AccessGroupRelayValues &relay_values)
{
    std::map<std::string, int> mac_relay;
    std::map<std::string, int> mac_serelay;
    
    if (0 == dbinterface::AccessGroup::GetUserAGDeviceList(account, mac_relay, mac_serelay)) {
        relay_values.access_group_relay_value = mac_relay[mac];
        relay_values.access_group_serelay_value = mac_serelay[mac];
    }
    return ;
}


//ag_id_mac_map 取出去后还需要添加默认权限组，以及判断是否有公共设施去掉默认权限组
int AccessGroup::GetAgInfoByCommunityID(unsigned int community_id, AccessGroupIDMap &ag_info_map, 
 AccessGroupMacMap &ag_info_mac_map, AccessGroupIDMacMap &ag_id_mac_map)
{
    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    
    std::stringstream str_sql;
    str_sql << "select A.ID,A.CommunityID,A.SchedulerType,A.DateFlag,A.BeginTime,"
        << "A.EndTime,A.StartTime,A.StopTime,D.Relay,DATE_FORMAT(A.BeginTime,'%Y%m%d') as daystart,DATE_FORMAT(A.EndTime,'%Y%m%d') as dayend, "
        << " A.Name,D.SecurityRelay,A.UnitID,D.Mac From AccessGroup A left join AccessGroupDevice D on A.ID=D.AccessGroupID where A.CommunityID=" << community_id << "";
           
    CRldbQuery query(db_conn.get());
    query.Query(str_sql.str());
    while (query.MoveToNextRow())
    {
        AccessGroupInfo  ag;
        std::string mac;
        ag.id_ = ATOI(query.GetRowData(0));
        ag.community_id_ = ATOI(query.GetRowData(1));
        ag.scheduler_type_ = ATOI(query.GetRowData(2));
        ag.date_flag_ = ATOI(query.GetRowData(3));
        Snprintf(ag.day_start_, sizeof(ag.day_start_), query.GetRowData(4));
        Snprintf(ag.day_end_, sizeof(ag.day_end_), query.GetRowData(5));
        Snprintf(ag.time_start_, sizeof(ag.time_start_), query.GetRowData(6));
        Snprintf(ag.time_end_, sizeof(ag.time_end_), query.GetRowData(7));        
        ag.relay_ = ATOI(query.GetRowData(8));
        Snprintf(ag.day_start_for_ymd_, sizeof(ag.day_start_for_ymd_), query.GetRowData(9));
        Snprintf(ag.day_end_for_ymd_, sizeof(ag.day_end_for_ymd_), query.GetRowData(10));
        Snprintf(ag.name_, sizeof(ag.name_), query.GetRowData(11));     
        ag.security_relay_ = ATOI(query.GetRowData(12));
        ag.unit_id_ = ATOI(query.GetRowData(13));
        if (ag.unit_id_ > 0)
        {
            ag.is_default_ = 1;
        }
        mac = query.GetRowData(14);

        if (mac.size() > 0)
        {
            ag_info_mac_map.insert(std::make_pair(mac, ag));
            ag_id_mac_map.insert(std::make_pair(ag.id_, mac));
        }
        ag_info_map.insert(std::make_pair(ag.id_, ag));
    }
    return 0;
}




 int AccessGroup::GetPubAgAccountByCommunityID(unsigned int community_id, AccessGroupIDAccountMap &ag_id_account_map)
 {
    GET_DB_CONN_ERR_RETURN(db_conn, -1);
     
 
     std::stringstream str_sql2;
     str_sql2 << "select A.Account,A.AccessGroupID From AccountAccess A left join AccessGroup G on G.ID=A.AccessGroupID where G.CommunityID=" << community_id << "";
            
     CRldbQuery query(db_conn.get());
     query.Query(str_sql2.str());
     while (query.MoveToNextRow())
     {
         std::string account = query.GetRowData(0);
         uint32_t ag_id = ATOI(query.GetRowData(1));
 
         ag_id_account_map.insert(std::make_pair(ag_id, account));
     }
    return 0;
 }


}

