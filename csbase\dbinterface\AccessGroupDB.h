#ifndef __DB_ACCESS_GROUP_H__
#define __DB_ACCESS_GROUP_H__
#include <string>
#include <memory>
#include <vector>
#include <set>
#include <stdint.h>
#include "AkcsCommonDef.h"
#include "AkcsCommonSt.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/resident/ResidentDevices.h"

typedef struct AccessGroup_T/*mac为粒度*/
{
    uint32_t id_;//要保证唯一 数据库id   
    uint32_t community_id_;
    uint32_t unit_id_;
    int is_default_; //如果unit_id > 0是默认权限组
    int scheduler_type_;
    int date_flag_;
    char time_start_[24];
    char time_end_[24];
    char day_start_[24];
    char day_end_[24];
    char day_start_for_ymd_[24];//只取日期
    char day_end_for_ymd_[24];//只取日期
    int relay_;/*这个设备对应这个权限组的relay*/
    int security_relay_;/*这个设备对应这个权限组的security relay*/
    char name_[128];

    AccessGroup_T() {
        memset(this, 0, sizeof(*this));
    }       
}AccessGroupInfo;

typedef std::list<struct AccessGroup_T> AccessGroupInfoList;
typedef std::shared_ptr<AccessGroupInfo> AccessGroupInfoPtr;
typedef std::list<AccessGroupInfoPtr> AccessGroupInfoPtrList;


using AccessGroupIDMap = std::map<uint32_t, AccessGroupInfo>;
using AccessGroupMacMap = std::multimap<std::string, AccessGroupInfo>;
using AccessGroupIDAccountMap = std::multimap<uint32_t, std::string>;
using AccessGroupIDMacMap = std::multimap<uint32_t, std::string>;


typedef struct UserAccess_T/*mac为粒度*/
{
    int ag_id;
    char name[128];
    char uuid[64];
    char meta[32];
    int dbid;
    int role;
    int unit_id;
    char db_uuid[64];
    char pm_rf[20];
    char pin[20];
    char face_url[64];
    char face_md5[32];
    int id_access_mode;
    char id_access_run[16];
    char id_access_serial[16];

    UserAccess_T() {
        memset(this, 0, sizeof(*this));
    }    
}UserAccessNode;

typedef std::vector<UserAccessNode> UserAccessNodeList;

struct AccessGroupRelayValues 
{
    int access_group_relay_value ;
    int access_group_serelay_value ;
    AccessGroupRelayValues() 
    {
        memset(this, 0, sizeof(*this));
    }
};

namespace dbinterface
{
class AccessGroup
{
public:
    AccessGroup();
    ~AccessGroup();

    static int GetUserAGDeviceList(const std::string& account, std::map<std::string, int> &mac_relay, std::map<std::string, int> &mac_serelay);
    static int HaveDefaultAG(const std::string& account, int dev_unit_id);
    static int GetMngIDByAgID(int id);
    static int GetAgRelay(const std::string& mac, int id);
    static std::string GetValidRelaySchedule(const std::string &mac, const std::string& relay_schedule, unsigned int relay_index);
    static void GetPubMacAccessGroupList(DEVICE_SETTING* dev, AccessGroupInfoPtrList &list);
    static void GetDefaultAccessGroupList(DEVICE_SETTING* dev, AccessGroupInfoPtrList &list);
    static void GetDevDefaultAccessGroupIDList(const ResidentDev& dev, std::vector<int>& access_group_id_list);
    static int GetMacListByAccessGroupID(uint32_t id, std::set<std::string> &mac_set);
    static void GetPubUserAccessGroupDevList(USER_TYPE user_type, const std::vector<uint32_t> &userlist, 
        std::set<std::string> &mac_set);
    static void GetAccessGroupDevListByUser(const std::vector<std::string> &userlist, std::set<std::string> &mac_set);
    static int GetCommunityIDByAccessGroup(unsigned int access_group_id, unsigned int &community_id);
    static bool IsDefaultAG(const int ag_id);    
    static int GetAgIDListByCommunityID(unsigned int community_id, std::set<int>& ag_id_list);
    static void GetUserAGRelayByMac(const std::string& account, const std::string& mac, AccessGroupRelayValues &relay_values);
    static int GetAgInfoByCommunityID(unsigned int community_id, AccessGroupIDMap &ag_info_map, 
 AccessGroupMacMap &ag_info_mac_map, AccessGroupIDMacMap &ag_id_mac_map);
     static int GetPubAgAccountByCommunityID(unsigned int community_id, AccessGroupIDAccountMap &ag_id_account_map);
private:
    
};


}
#endif
