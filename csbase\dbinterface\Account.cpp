#include <sstream>
#include <string.h>
#include "util.h"
#include "dbinterface/Account.h"
#include "AkLogging.h"
#include "dbinterface/AccountMap.h"
#include "AkcsCommonDef.h"
#include "dbinterface/AccountUserInfo.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/DataConfusion.h"
#include "util_judge.h"



namespace dbinterface
{

// encrypt field : Phone

static const std::string account_sec = " ID,Account,Grade,Role,ParentID,Location,\
    SipPrefix,Special,Phone,TimeZone,HouseCount,EnableValidTimeSetting,EnableCountSetting,ManageGroup,CustomizeForm,ChargeMode,SipType,\
    Initialization,Language,SendExpireEmailType,Flags,SendRenew,UUID  ";

static const std::string account_sec2 = " AA.ID,AA.Account,AA.Grade,AA.Role,AA.ParentID,AA.Location,\
    AA.SipPrefix,AA.Special,AA.Phone,AA.TimeZone,AA.HouseCount,AA.EnableValidTimeSetting,AA.EnableCountSetting,AA.ManageGroup,AA.CustomizeForm,AA.ChargeMode,AA.SipType,\
    AA.Initialization,AA.Language,AA.SendExpireEmailType,AA.Flags,AA.SendRenew,AA.UUID  ";



Account::Account()
{

}



void Account::GetAccountFromSql(AccountInfo &account, CRldbQuery& query)
{
    account.id = ATOI(query.GetRowData(0));
    Snprintf(account.account, sizeof(account.account), query.GetRowData(1));
    account.grade = ATOI(query.GetRowData(2));
    account.role = ATOI(query.GetRowData(3));
    account.parent_id = ATOI(query.GetRowData(4));
    Snprintf(account.location, sizeof(account.location), query.GetRowData(5));
    account.sip_prefix = ATOI(query.GetRowData(6));
    account.special = ATOI(query.GetRowData(7));
    Snprintf(account.phone, sizeof(account.phone), dbinterface::DataConfusion::Decrypt(query.GetRowData(8)).c_str());
    Snprintf(account.timezone, sizeof(account.timezone), query.GetRowData(9));
    account.house_count = ATOI(query.GetRowData(10));
    account.is_enable_valid_time = ATOI(query.GetRowData(11));
    account.is_enable_count = ATOI(query.GetRowData(12));
    account.manage_group = ATOI(query.GetRowData(13));
    account.customize_form = ATOI(query.GetRowData(14));
    account.chargemode = ATOI(query.GetRowData(15));
    account.sip_type = ATOI(query.GetRowData(16));
    account.is_initialized = ATOI(query.GetRowData(17));
    Snprintf(account.language, sizeof(account.language), query.GetRowData(18));
    account.expire_email_type = ATOI(query.GetRowData(19));
    account.flags = ATOI(query.GetRowData(20));
    account.sendrenew = ATOI(query.GetRowData(21));
    Snprintf(account.uuid, sizeof(account.uuid), query.GetRowData(22));
    return;
}

int Account::GetAccountById(uint32_t id, AccountInfo &account)
{
    std::stringstream streamSQL;
    streamSQL << "select " << account_sec << "from Account where ID = " << id;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    if (query.MoveToNextRow())
    {
        GetAccountFromSql(account, query);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;    
}

int Account::GetAccountByUUID(const std::string& uuid, AccountInfo &account)
{
    std::stringstream stream_sql;
    stream_sql << "select " << account_sec << " from Account where UUID = '" << uuid << "'";
    
    GET_DB_CONN_ERR_RETURN(conn, -1);
    CRldbQuery query(conn.get());
    
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetAccountFromSql(account, query);
    }
    else
    {
        AK_LOG_INFO << "GetAccountByUUID Failed, UUID = " << uuid;
        return -1;
    }
    
    return 0;
}

int Account::GetAccountFromMasterByUUID(const std::string& uuid, AccountInfo &account)
{
    std::stringstream streamSQL;
    streamSQL << "/*master*/select " << account_sec << "from Account where UUID = '" << uuid <<"'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    if (query.MoveToNextRow())
    {
        GetAccountFromSql(account, query);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;    
}

int Account::GetAccountInfoByAccount(const std::string& account, AccountInfo &account_info)
{
    std::stringstream streamsql;
    streamsql << "select " << account_sec << "from Account where Account = '" << account <<"'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamsql.str());
    if (query.MoveToNextRow())
    {
        GetAccountFromSql(account_info, query);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;    
}

int Account::GetAccountByEmail(const std::string& email, AccountInfo &account)
{
    UserInfoAccount user_info;
    if (0 != dbinterface::AccountUserInfo::GetAccountInfoByEmail(email, user_info))
    {
        AK_LOG_WARN << "GetAccountInfoByEmail failed.";
        return -1;
    }

    std::string account_uuid;
    if (0 != dbinterface::AccountMap::GetAccountUUIDByUserInfoUUID(user_info.uuid, account_uuid))
    {
        AK_LOG_WARN << "GetAccountUUIDByUserInfoUUID failed.";
        return -1;
    }

    return GetAccountByUUID(account_uuid, account);
}

int Account::GetAccountFromMasterByEmail(const std::string& email, AccountInfo &account)
{
    UserInfoAccount user_info;
    memset(&user_info, 0, sizeof(user_info));
    if (0 != dbinterface::AccountUserInfo::GetAccountInfoFromMasterByEmail(email, user_info))
    {
        AK_LOG_WARN << "GetAccountInfoByEmail failed.";
        return -1;
    }

    std::string account_uuid;
    if (0 != dbinterface::AccountMap::GetAccountUUIDFromMasterByUserInfoUUID(user_info.uuid, account_uuid))
    {
        AK_LOG_WARN << "GetAccountUUIDByUserInfoUUID failed.";
        return -1;
    }

    return GetAccountFromMasterByUUID(account_uuid, account);
    
}

int Account::GetDisAccountBySubId(uint32_t id, AccountInfo &account)
{
    std::stringstream streamSQL;
    streamSQL << "select " << account_sec2 << " from Account A left join Account AA on AA.ID=A.ParentID where A.ID=" << id;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    if (query.MoveToNextRow())
    {
        GetAccountFromSql(account, query);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;    
}

std::string Account::GetOfficeNameById(uint32_t id)
{
    AccountInfo account;
    memset(&account, 0, sizeof(account));
    GetAccountById(id, account);
    return account.location;
}

int Account::GetAccountGradeById(uint32_t id)
{
    AccountInfo account;
    memset(&account, 0, sizeof(account));
    GetAccountById(id, account);
    return account.grade;
}

int Account::GetUUIDByMngAccountId(uint32_t id, std::string &uuid)
{
    std::stringstream streamSQL;
    streamSQL << "select UUID from Account where ID = " << id;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    if (query.MoveToNextRow())
    {
        uuid = query.GetRowData(0);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;    
}

int Account::GetCommunityContactSwitch(int mng_id)
{
    dbinterface::AccountInfo dis_account;
    memset(&dis_account, 0, sizeof(dis_account));
    int contact_switch = 0;
    //获取Distributor的开关
    GetDisAccountBySubId(mng_id, dis_account);
    contact_switch = dbinterface::SwitchHandle(dis_account.flags, FLAGS_COMMUNITY_CONTACT);

    if(contact_switch)
    {
        dbinterface::AccountInfo account;
        memset(&account, 0, sizeof(account));
        //获取Community的开关
        GetAccountById(mng_id, account);
        contact_switch = dbinterface::SwitchHandle(account.flags, FLAGS_COMMUNITY_CONTACT);
    }

    return contact_switch;
}

//account_id：社区/个人管理员id
int Account::GetDistributor(const int account_id, std::string& dis_account)
{
    if (account_id <= 0)
    {
        return -1;
    }

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        return -1;
    }
    CRldbQuery query(tmp_conn);

    std::stringstream streamsql;
    streamsql << "select b.Account Distributor from Account a "
              << " join Account b on a.ParentID = b.ID "
              << " where a.id = " << account_id << " limit 1";

    query.Query(streamsql.str());

    if (query.MoveToNextRow())
    {
        dis_account = query.GetRowData(0);
        ReleaseDBConn(conn);
        return 0;
    }
    ReleaseDBConn(conn);
    return -1;

}

int Account::GetInsAccountInfoByManageGroup(const int manage_group, AccountInfo &account_info)
{
    if (manage_group <= 0)
    {
        return -1;
    }

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        return -1;
    }
    CRldbQuery query(tmp_conn);

    std::stringstream streamsql;
    streamsql << "select " << account_sec << " from Account where ManageGroup = " << manage_group
              << " AND Grade = " << AccountGrade::PERSONAL_MANEGER_GRADE << " limit 1";
    
    query.Query(streamsql.str());

    if(query.MoveToNextRow())
    {
        GetAccountFromSql(account_info, query);
        ReleaseDBConn(conn);
        return 0;
    }
    ReleaseDBConn(conn);
    return -1;
}

//获取buildinstall 管理员的sip设置
int Account::GetMngTransType(int mng_id, int &siptype, int &rtpconfuse, int &rtsp_type)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::stringstream streamsql;
    streamsql << "select B.SipType,B.Flags,B.RtspType from Account A left join Account B on A.ManageGroup = B.ID where A.ID=" << mng_id;
    CRldbQuery query(tmp_conn);
    query.Query(streamsql.str());

    if (query.MoveToNextRow())
    {
        int flags;
        siptype = ATOI(query.GetRowData(0));
        flags = ATOI(query.GetRowData(1));
        rtpconfuse = dbinterface::SwitchHandle(flags, FLAGS_RTP_CONFUSE);
        rtsp_type = ATOI(query.GetRowData(2));

        ReleaseDBConn(conn);
        return 0;
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    
    ReleaseDBConn(conn);
    return 0;
}

//获取buildinstall 管理员的sip设置
int Account::GetMngTransType(const std::string &project_uuid, MngSetting &mng_setting)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::stringstream streamsql;
    streamsql << "select B.SipType,B.Flags,B.RtspType from Account A left join Account B on A.ManageGroup = B.ID where A.UUID='" << project_uuid <<"'";
    CRldbQuery query(tmp_conn);
    query.Query(streamsql.str());

    if (query.MoveToNextRow())
    {
        int flags;
        mng_setting.sip_type = ATOI(query.GetRowData(0));
        flags = ATOI(query.GetRowData(1));
        mng_setting.rtp_confuse = dbinterface::SwitchHandle(flags, FLAGS_RTP_CONFUSE);
        mng_setting.rtsp_type = ATOI(query.GetRowData(2));

        ReleaseDBConn(conn);
        return 0;
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    
    ReleaseDBConn(conn);
    return 0;
}


int Account::GetPerMngTransType(const std::string &node, int &siptype, int &rtp_confuse, int &rtsp_type)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    int flags = 0;
    std::stringstream streamsql;
    streamsql << "select B.SipType,B.Flags,B.RtspType from Account A left join PersonalAccount P on P.ParentID=A.ID Left join Account B \
        on A.ManageGroup=B.ID where P.Role=10 and P.Account='" << node << "'";
    CRldbQuery query(tmp_conn);
    query.Query(streamsql.str());

    if (query.MoveToNextRow())
    {
        siptype = ATOI(query.GetRowData(0));
        flags = ATOI(query.GetRowData(1));
        rtp_confuse = dbinterface::SwitchHandle(flags, FLAGS_RTP_CONFUSE);
		rtsp_type = ATOI(query.GetRowData(2));
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }

    ReleaseDBConn(conn);
    return 0;
}

int Account::GetDisAccount(const ResidentPerAccount& personal_account, AccountInfo& dis_account)
{
    std::stringstream stream_sql;
    
    if (personal_account.role == ACCOUNT_ROLE_PERSONNAL_MAIN || IsOfficeRole(personal_account.role) || personal_account.role == ACCOUNT_ROLE_COMMUNITY_PM)
    {
        stream_sql << "select dis.Account from PersonalAccount master left join Account ins on master.ParentUUID = ins.UUID left join Account dis on ins.ParentUUID = dis.UUID where master.Account = '" << personal_account.account << "'";
    }
    else if (personal_account.role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT)
    {
        stream_sql << "select dis.Account from PersonalAccount slave left join PersonalAccount master on slave.ParentUUID = master.UUID left join Account ins on master.ParentUUID = ins.UUID left join Account dis on ins.ParentUUID = dis.UUID where slave.Account = '" << personal_account.account << "'";
    }
    else if (personal_account.role == ACCOUNT_ROLE_COMMUNITY_MAIN)
    {
        stream_sql << "select dis.Account from PersonalAccount master left join Account community on master.ParentUUID = community.UUID left join Account dis on community.ParentUUID = dis.UUID where master.Account = '" << personal_account.account << "'";
    }
    else if (personal_account.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT)
    {
        stream_sql << "select dis.Account from PersonalAccount slave left join PersonalAccount master on slave.ParentUUID = master.UUID left join Account community on master.ParentUUID = community.UUID left join Account dis on community.ParentUUID = dis.UUID where slave.Account = '" << personal_account.account << "'";
    }
    
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    CRldbQuery query(tmp_conn);
    query.Query(stream_sql.str());

    if(query.MoveToNextRow())
    {
        Snprintf(dis_account.account, sizeof(dis_account.account), query.GetRowData(0));
            
        ReleaseDBConn(conn);
        return 0;
    }

    ReleaseDBConn(conn);
    return -1;
}

bool Account::CheckRtpConfuseByDev(const ResidentDev& dev)
{
    bool is_support_confuse = dbinterface::SwitchHandle(dev.fun_bit, FUNC_DEV_SUPPORT_RTSP_RTP_CONFUSE);
    if(!is_support_confuse)
    {
        return false;
    }

    int siptype,rtsp_type,rtp_confuse = 0;
    if(dev.project_type == project::PERSONAL)
    {
        GetPerMngTransType(dev.node, siptype, rtp_confuse, rtsp_type);
    }
    else
    {
        GetMngTransType(dev.project_mng_id, siptype, rtp_confuse, rtsp_type);
    }

    return rtp_confuse ? true : false;

}

int Account::GetAccountInfoByAccountUserInfoUUID(const std::string& account_user_info_uuid, std::vector<AccountInfo>& accounts)
{
    std::stringstream stream_sql;
    stream_sql << "SELECT " << account_sec2 << " FROM Account AA "
               << "JOIN AccountMap M ON AA.UUID = M.AccountUUID "
               << "WHERE M.UserInfoUUID = '" << account_user_info_uuid << "'";

    GET_DB_CONN_ERR_RETURN(tmp_conn, -1);
    CRldbQuery query(tmp_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        AccountInfo account;
        GetAccountFromSql(account, query);
        accounts.push_back(account);  
    }

    return accounts.empty() ? -1 : 0;
}

bool Account::IsInstaller(const std::string& account_user_info_uuid)
{
    std::vector<AccountInfo> accounts;
    int result = GetAccountInfoByAccountUserInfoUUID(account_user_info_uuid, accounts);
    
    if (result == 0 && !accounts.empty())
    {
        const AccountInfo& account = accounts[0];
        if ((account.grade == AccountGrade::COMMUNITY_MANEGER_GRADE || account.grade == AccountGrade::PERSONAL_MANEGER_GRADE) && account.id == account.manage_group)
        {
            return true; 
        }
    }
    return false; 
}

bool Account::IsPm(const std::string& account_user_info_uuid)
{
    std::vector<AccountInfo> accounts;
    int result = GetAccountInfoByAccountUserInfoUUID(account_user_info_uuid, accounts);
    
    if (result == 0 && !accounts.empty())
    {
        const AccountInfo& account = accounts[0];
        if (account.grade == AccountGrade::PERSONAL_USER_GRADE)
        {
            return true; 
        }
    }
    return false; 
}

// 获取项目的Ins和Dis的UUID
bool Account::GetInsDisUUIDByProjectUUID(const std::string &project_uuid, std::string& ins_uuid, std::string& dis_uuid){
    ins_uuid = "";
    dis_uuid = "";
    std::stringstream stream_sql;
    stream_sql << "SELECT INS.UUID AS INS_UUID,P.ParentUUID AS DIS_UUID FROM Account P "
               << "JOIN Account INS ON P.ManageGroup = INS.ID "
               << "WHERE P.UUID = '" << project_uuid << "'";

    GET_DB_CONN_ERR_RETURN(tmp_conn, -1);
    CRldbQuery query(tmp_conn.get());
    query.Query(stream_sql.str());
    if(query.MoveToNextRow())
    {
        ins_uuid = query.GetRowData(0);
        dis_uuid = query.GetRowData(1);
        return true;
    }

    return false;
}

}

