#ifndef __DB_ACCOUNT_H__
#define __DB_ACCOUNT_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/resident/ResidentDevices.h"

namespace dbinterface
{


typedef struct Account_T
{
    int id;
    int grade;  //23为office角色
    int role;
    int parent_id;
    int sip_prefix;
    int special;
    int house_count;
    int is_enable_valid_time;
    int is_enable_count;
    int manage_group;
    int customize_form;
    int chargemode;
    int sip_type;
    int is_initialized;
    int expire_email_type;
    int flags;
    int sendrenew;
    char location[128];//数据库字段长度
    char account[64];
    char language[32];
    char phone[20];
    char timezone[64];
    char uuid[64];
    Account_T()
    {
        memset(this, 0, sizeof(*this));
    }
}AccountInfo;

typedef struct MngSetting_T
{
    int sip_type;
    int rtp_confuse;
    int rtsp_type;
    MngSetting_T()
    {
        memset(this, 0, sizeof(*this));
    }
}MngSetting;


class Account
{
    enum AccountFlags
    {
        FLAGS_RTP_CONFUSE = 0,
        FLAGS_COMMUNITY_CONTACT = 4,   //户户通开关
    };      

public:
    Account();
    ~Account();
    
    static std::string GetOfficeNameById(uint32_t id);
    static int GetAccountGradeById(uint32_t id);
    
    static int GetDisAccountBySubId(uint32_t id, AccountInfo &account);
    static int GetAccountById(uint32_t id, AccountInfo &account);
	static int GetAccountInfoByAccount(const std::string& account, AccountInfo &account_info);
    static int GetAccountByEmail(const std::string& email, AccountInfo &account);
    static int GetAccountFromMasterByEmail(const std::string& email, AccountInfo &account);
    static int GetAccountByUUID(const std::string& uuid, AccountInfo &account);
    static int GetAccountFromMasterByUUID(const std::string& uuid, AccountInfo &account);
    static int GetUUIDByMngAccountId(uint32_t id, std::string &uuid);
    //获取户户通开关
    static int GetCommunityContactSwitch(int mng_id);
	static int GetDistributor(const int account_id, std::string& dt_account);
    static int GetInsAccountInfoByManageGroup(const int manage_group, AccountInfo &account_info);
    static int GetMngTransType(int mng_id, int &siptype, int &rtpconfuse, int &rtsp_type);
    static int GetMngTransType(const std::string &project_uuid, MngSetting &setting);    
    static int GetPerMngTransType(const std::string &node, int &siptype, int &rtp_confuse, int &rtsp_type);
    static int GetDisAccount(const ResidentPerAccount& personal_account, AccountInfo& dis_account);
    static bool CheckRtpConfuseByDev(const ResidentDev& dev);    
    static int GetAccountInfoByAccountUserInfoUUID(const std::string& account_user_info_uuid, std::vector<AccountInfo>& accounts);
    static bool IsInstaller(const std::string& account_user_info_uuid);
    static bool IsPm(const std::string& account_user_info_uuid);
    static bool GetInsDisUUIDByProjectUUID(const std::string &project_uuid, std::string& ins_uuid, std::string& dis_uuid);
private:
    static void GetAccountFromSql(AccountInfo &account, CRldbQuery& query);
};

}
#endif
