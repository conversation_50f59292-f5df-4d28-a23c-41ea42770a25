#include <sstream>
#include <string.h>
#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "AccountAccess.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/DataConfusion.h"

namespace dbinterface
{


AccountAccess::AccountAccess()
{

}


void AccountAccess::GetAccountInfoByAccessGroup(unsigned int access_group_id, ResidentPerAccountList &account_info_list)
{
    std::set<std::string> account_list;
    std::stringstream stream_sql;
    stream_sql << "select account from AccountAccess where AccessGroupID = " << access_group_id;
    
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }
    CRldbQuery query(tmp_conn);

    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        account_list.insert(query.GetRowData(0));
    }
    ReleaseDBConn(conn);
    
    //获取account的详细信息
    for(const auto& account : account_list)
    {
        ResidentPerAccount account_info;
        memset(&account_info, 0, sizeof(account_info));
        dbinterface::ResidentPersonalAccount::GetUidAccount(account, account_info);
        account_info_list.push_back(account_info);
    }
}

/*住户, 公共设备权限组包含的用户列表*/
void AccountAccess::GetPubDevAccountListByAccessGroupID(uint id, UserAccessNodeList &list)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }

    std::stringstream str_sql;
    str_sql << "select P.Name,P.Account,P.Version,P.ID,P.Role,P.UnitID,P.UUID From AccountAccess A left join PersonalAccount P "
        << "on P.Account=A.Account where A.AccessGroupID=" << id << " order by A.ID";

    CRldbQuery query(tmp_conn);
    query.Query(str_sql.str());
    while (query.MoveToNextRow())
    {
        if (strlen(query.GetRowData(1)) == 0)
        {
            AK_LOG_WARN << "Have Dirty data. AccountAccess table, AccessGroupID:" << id << " not found account.";       
            continue;
        }
        UserAccessNode ua;
        ua.ag_id = id;
        Snprintf(ua.name, sizeof(ua.name), dbinterface::DataConfusion::Decrypt(query.GetRowData(0)).c_str());
        Snprintf(ua.uuid, sizeof(ua.uuid), query.GetRowData(1));
        Snprintf(ua.meta, sizeof(ua.meta), query.GetRowData(2));
        ua.dbid = ATOI(query.GetRowData(3));
        ua.role = ATOI(query.GetRowData(4));
        ua.unit_id = ATOI(query.GetRowData(5));
        Snprintf(ua.db_uuid, sizeof(ua.db_uuid), query.GetRowData(6));
        list.push_back(ua);
    }
    ReleaseDBConn(conn);    
}


int AccountAccess::GetAccountsByAccessGroup(unsigned int ag_id, std::set<std::string> &accounts)
{
    std::stringstream stream_sql;
    stream_sql << "select account from AccountAccess where AccessGroupID = " << ag_id;

    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());

    query.Query(stream_sql.str());

    while (query.MoveToNextRow())
    {
        accounts.insert(query.GetRowData(0));
    }
    return 0;
}




}

