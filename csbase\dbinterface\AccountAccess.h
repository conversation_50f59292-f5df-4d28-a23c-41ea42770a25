#ifndef __DB_ACCOUNT_ACCESS_H__
#define __DB_ACCOUNT_ACCESS_H__
#include <string>
#include <memory>
#include <vector>
#include <set>
#include <stdint.h>
#include "AkcsCommonDef.h"
#include "AkcsCommonSt.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "AccessGroupDB.h"
#include "ConnectionManager.h"



namespace dbinterface
{

class AccountAccess
{
public:
    AccountAccess();
    ~AccountAccess();
    static void GetAccountInfoByAccessGroup(unsigned int access_group_id, ResidentPerAccountList &account_info_list);
    static void GetPubDevAccountListByAccessGroupID(uint id, UserAccessNodeList &list);
    static int GetAccountsByAccessGroup(unsigned int ag_id, std::set<std::string> &accounts);

private:
    
};


}
#endif
