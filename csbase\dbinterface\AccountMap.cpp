#include <sstream>
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/InterfaceComm.h"
#include "AccountMap.h"
#include <string.h>
#include "AkLogging.h"

namespace dbinterface{
AccountMap::AccountMap()
{

}

AccountMap::~AccountMap()
{

}

int AccountMap::GetAccountUUIDByUserInfoUUID(const std::string &user_info_uuid, std::string &account_uuid)
{
    std::stringstream streamsql;
    streamsql << "SELECT AccountUUID FROM AccountMap WHERE UserInfoUUID = '"
              << user_info_uuid
              << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    CRldbQuery query(ptmpconn);

    query.Query(streamsql.str());
    if (query.MoveToNextRow())
    {
        account_uuid = query.GetRowData(0);
    }

    ReleaseDBConn(conn);
    return 0;
}

int AccountMap::GetAccountUUIDFromMasterByUserInfoUUID(const std::string &user_info_uuid, std::string &account_uuid)
{
    std::stringstream streamsql;
    streamsql << "/*master*/select AccountUUID from AccountMap where UserInfoUUID = '" << user_info_uuid << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    CRldbQuery query(ptmpconn);

    query.Query(streamsql.str());
    if (query.MoveToNextRow())
    {
        account_uuid = query.GetRowData(0);
    }

    ReleaseDBConn(conn);
    return 0;
}

int AccountMap::GetUserInfoUUIDByAccountUUID(const std::string &account_uuid, std::string &user_info_uuid)
{
    std::stringstream streamsql;
    streamsql << "SELECT UserInfoUUID FROM AccountMap WHERE AccountUUID = '"
              << account_uuid
              << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(ptmpconn);

    query.Query(streamsql.str());
    if (query.MoveToNextRow())
    {
        user_info_uuid = query.GetRowData(0);
    }
    ReleaseDBConn(conn);
    return 0;
}






}

