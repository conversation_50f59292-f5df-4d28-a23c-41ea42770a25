#ifndef _ACCOUNTMAP_H_
#define _ACCOUNTMAP_H_
#include <string>
#include <memory>
#include <tuple>


namespace dbinterface{


class AccountMap
{
public:
    AccountMap();
    ~AccountMap();
    static int GetAccountUUIDByUserInfoUUID(const std::string &user_info_uuid, std::string &account_uuid);
    static int GetAccountUUIDFromMasterByUserInfoUUID(const std::string &user_info_uuid, std::string &account_uuid);
    static int GetUserInfoUUIDByAccountUUID(const std::string &account_uuid, std::string &user_info_uuid);
private:
};

}


#endif
