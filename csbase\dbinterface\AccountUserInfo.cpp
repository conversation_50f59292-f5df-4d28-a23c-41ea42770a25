#include <sstream>
#include <string.h>
#include "util.h"
#include "AkLogging.h"
#include "dbinterface/AccountMap.h"
#include "dbinterface/AccountUserInfo.h"
#include "dbinterface/DataConfusion.h"
#include "dbinterface/InterfaceComm.h"
#include "ConnectionManager.h"

namespace dbinterface
{

// encrypt field : Email、Phone
static const std::string account_sec = " ID,LoginAccount,Email,Passwd,AppMainUserAccount,AppLastLoginUserAccount,IsLink,UUID,AppStatus,Phone,TwoFactorAuth ";

AccountUserInfo::AccountUserInfo()
{
	    
}


void AccountUserInfo::GetAccountFromSql(UserInfoAccount &account, CRldbQuery& query)
{
    account.id = ATOI(query.GetRowData(0));
    Snprintf(account.account, sizeof(account.account), query.GetRowData(1));
    Snprintf(account.email, sizeof(account.email), dbinterface::DataConfusion::Decrypt(query.GetRowData(2)).c_str());
    Snprintf(account.passwd, sizeof(account.passwd), query.GetRowData(3));
    Snprintf(account.main_user_account, sizeof(account.main_user_account), query.GetRowData(4));
    Snprintf(account.last_login_account, sizeof(account.last_login_account), query.GetRowData(5));
    account.is_link = ATOI(query.GetRowData(6));
    Snprintf(account.uuid, sizeof(account.uuid), query.GetRowData(7));
    account.installer_app_status = ATOI(query.GetRowData(8));
    Snprintf(account.phone, sizeof(account.phone),  dbinterface::DataConfusion::Decrypt(query.GetRowData(9)).c_str());
    account.two_factor_auth = ATOI(query.GetRowData(10));
    return;
}



int AccountUserInfo::GetAccountUserInfoByUUID(const std::string& uuid, UserInfoAccount &account_info)
{
    std::stringstream streamsql;
    streamsql << "select " << account_sec << "from AccountUserInfo where UUID = '" << uuid << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    CRldbQuery query(tmp_conn);
    query.Query(streamsql.str());
    if (query.MoveToNextRow())
    {
        GetAccountFromSql(account_info, query);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;    
}

int AccountUserInfo::GetAccountInfoByEmail(const std::string& email, UserInfoAccount &account_info)
{
    auto pos = email.find('@');
    if (pos == std::string::npos)
    {
        return -1;
    }       

    std::string encrypt_email = dbinterface::DataConfusion::Encrypt(email.c_str());
    
    std::stringstream streamsql;
    streamsql << "select " << account_sec << "from AccountUserInfo where Email = '" << encrypt_email << "'";
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamsql.str());
    if (query.MoveToNextRow())
    {
        GetAccountFromSql(account_info, query);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;    
}

int AccountUserInfo::GetAccountInfoFromMasterByEmail(const std::string& email, UserInfoAccount &account_info)
{
    auto pos = email.find('@');
    if (pos == std::string::npos)
    {
        return -1;
    }    
    
    std::string encrypt_email = dbinterface::DataConfusion::Encrypt(email.c_str());
    
    std::stringstream streamsql;
    streamsql << "/*master*/select " << account_sec << "from AccountUserInfo where Email = '" << encrypt_email << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamsql.str());
    if (query.MoveToNextRow())
    {
        GetAccountFromSql(account_info, query);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;    
}

int AccountUserInfo::GetAccountUserInfoByAccountUUID(const std::string& uuid, UserInfoAccount &account_info)
{
    std::string userinfo_uuid;
    if (0 !=  dbinterface::AccountMap::GetUserInfoUUIDByAccountUUID(uuid, userinfo_uuid))
    {
        AK_LOG_WARN << "GetUserInfoUUIDByAccountUUID Failed";
        return -1;
    }

    if (0 !=  dbinterface::AccountUserInfo::GetAccountUserInfoByUUID(userinfo_uuid, account_info))
    {
        AK_LOG_WARN << "GetAccountUserInfoByUUID Failed";
        return -1;
    }

    return 0;
}

int AccountUserInfo::GetAccountUserInfoByEmailOrLoginAccount(const std::string& login_account, UserInfoAccount& account_info)
{
    // 邮箱登录会有大小写问题, 例如: <EMAIL> 和 <EMAIL>
    // 必须要先通过mapping库查出所有邮箱, 再用加密后的邮箱去匹配用户
    std::vector<std::string> encrypt_email_list;
    
    std::stringstream stream_sql_mapping;
    stream_sql_mapping << "/*master*/ select EnColumn from AKCSMapping.EmailMapping where DeColumn = '" << login_account << "'";

    GET_DB_CONN_ERR_RETURN(tmp_conn, -1);
    CRldbQuery query(tmp_conn.get());
    
    query.Query(stream_sql_mapping.str());
    while (query.MoveToNextRow())
    {
        std::string email_encolumn = query.GetRowData(0);
        encrypt_email_list.push_back(email_encolumn);
    }

    // 使用邮箱登录
    if (encrypt_email_list.size() > 0)
    {
        // 遍历mapping库中所有的邮箱,找到匹配上用户的那个邮箱
        for (const auto& encrypt_email : encrypt_email_list)
        {
            std::stringstream stream_sql;
            stream_sql << "select " << account_sec << " from AccountUserInfo where Email = '" << encrypt_email << "'";
            
            query.Query(stream_sql.str());
            if (query.MoveToNextRow())
            {
                GetAccountFromSql(account_info, query);
                return 0;
            }
        }
    }
    else
    {
        // 使用sip登录
        std::stringstream stream_sql;
        stream_sql << "select " << account_sec << " from AccountUserInfo where LoginAccount = '" << login_account << "'";

        query.Query(stream_sql.str());
        if (query.MoveToNextRow())
        {
            GetAccountFromSql(account_info, query);
            return 0;
        }
    }

    return -1; 
}

int AccountUserInfo::GetAccountUserInfoOnlyByLoginAccount(const std::string& login_account, UserInfoAccount& account_info)
{
    std::stringstream stream_sql;
    //pm app 在AccountUserInfo表的数据 LoginAccount一定是email
    //ins app目前不支持邮箱作为账号,只通过login_account查询
    stream_sql << "select " << account_sec << " from AccountUserInfo where LoginAccount = '" << login_account << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetAccountFromSql(account_info, query);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0; 
}

int AccountUserInfo::GetAccountUserInfoOnlyByPhone(const std::string& phone, UserInfoAccount& account_info)
{
    if (phone.length() < 5) //定一个长度防止传入空格或者空字符串导致查出很多账号
    {
        return -1;
    }

    std::string encrypt_phone = dbinterface::DataConfusion::Encrypt(phone.c_str());

    std::stringstream stream_sql;
    stream_sql << "select " << account_sec << " from AccountUserInfo where Phone = '" << encrypt_phone << "'";

    GET_DB_CONN_ERR_RETURN(tmp_conn, -1);
    CRldbQuery query(tmp_conn.get());
    query.Query(stream_sql.str());
    if (!query.MoveToNextRow())
    {
        return -1;
    }

    GetAccountFromSql(account_info, query);
    return 0;
}

}

