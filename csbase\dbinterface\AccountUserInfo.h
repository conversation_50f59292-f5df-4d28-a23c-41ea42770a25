#ifndef __DB_ACCOUNT_USERINFO_H__
#define __DB_ACCOUNT_USERINFO_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include "RldbQuery.h"
#include "Rldb.h"

typedef struct UserInfoAccount_T
{
    int id;
    int is_link;
    char account[64];
    char passwd[64];
    char email[64];
    char mobile_number[64];
    char main_user_account[64];
    char last_login_account[64];
    char uuid[64];
    char phone[32];
    int installer_app_status; //字段只对ins_app有效
    int two_factor_auth;
    UserInfoAccount_T()
    {
        memset(this, 0, sizeof(*this));
    }
}UserInfoAccount;

namespace dbinterface
{


class AccountUserInfo
{
public:
    AccountUserInfo();
    ~AccountUserInfo();

    static int GetAccountUserInfoByEmailOrLoginAccount(const std::string& login_account, UserInfoAccount& user_info);
    static int GetAccountUserInfoByUUID(const std::string& uuid, UserInfoAccount &account_info);
    static int GetAccountInfoByEmail(const std::string& email, UserInfoAccount &account_info);
    static int GetAccountInfoFromMasterByEmail(const std::string& email, UserInfoAccount &account_info);
    static int GetAccountUserInfoByAccountUUID(const std::string& uuid, UserInfoAccount &account_info);
    //Ins app新增status查询 0:关闭 1:开启
    static int GetAccountUserInfoOnlyByLoginAccount(const std::string& login_account, UserInfoAccount& account_info);
    static int GetAccountUserInfoOnlyByPhone(const std::string& phone, UserInfoAccount& account_info);
private:
    static void GetAccountFromSql(UserInfoAccount &account, CRldbQuery& query);
};

}

#endif
