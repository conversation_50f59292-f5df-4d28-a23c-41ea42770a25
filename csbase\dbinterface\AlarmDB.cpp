#include <sstream>
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/UUID.h"
#include "AlarmDB.h"
#include <string.h>
#include "AkLogging.h"
#include <boost/algorithm/string.hpp>
#include "util.h"
#include "AkcsCommonDef.h"
#include "dbinterface/new-office/OfficePersonnel.h"


namespace dbinterface{
Alarm::Alarm()
{

}

Alarm::~Alarm()
{

}

int Alarm::GetAlarm(uint32_t id, ALARM* alarm)
{
    std::stringstream streamSQL;
    streamSQL << "/*master*/select ID,AlarmType,MngAccountID,UnitID,DevicesMAC,Node,AlarmTime,Status,\
                  DealTime,DealUser,DealType,DealResult,AlarmCode,AlarmCustomize,AlarmLocation,AlarmZone,\
                  UUID,IsRead,DevicesUUID,RelayType,RelayNum,CompanyUUID,TraceID from Alarms where ID=" << id;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(conn.get());

    query.Query(streamSQL.str());
    if (query.MoveToNextRow())
    {
        alarm->id = ATOI(query.GetRowData(0));
        Snprintf(alarm->alarm_type, sizeof(alarm->alarm_type), query.GetRowData(1));
        alarm->manager_account_id = ATOI(query.GetRowData(2));
        alarm->unit_id = ATOI(query.GetRowData(3));
        Snprintf(alarm->mac, sizeof(alarm->mac), query.GetRowData(4));
        Snprintf(alarm->device_node, sizeof(alarm->device_node), query.GetRowData(5));
        Snprintf(alarm->alarm_time, sizeof(alarm->alarm_time), query.GetRowData(6));
        alarm->status = ATOI(query.GetRowData(7));
        Snprintf(alarm->deal_time, sizeof(alarm->deal_time), query.GetRowData(8));
        Snprintf(alarm->deal_user, sizeof(alarm->deal_user), query.GetRowData(9));
        alarm->deal_type = ATOI(query.GetRowData(10));
        Snprintf(alarm->deal_result, sizeof(alarm->deal_result), query.GetRowData(11));
        alarm->alarm_code = ATOI(query.GetRowData(12));
        alarm->alarm_customize = ATOI(query.GetRowData(13));
        alarm->alarm_location = ATOI(query.GetRowData(14));
        alarm->alarm_zone = ATOI(query.GetRowData(15));
        Snprintf(alarm->uuid, sizeof(alarm->uuid), query.GetRowData(16));
        alarm->is_read = ATOI(query.GetRowData(17));
        Snprintf(alarm->device_uuid, sizeof(alarm->device_uuid), query.GetRowData(18));
        alarm->relay_type = (RelayType)ATOI(query.GetRowData(19));
        alarm->relay_num = ATOI(query.GetRowData(20));
        Snprintf(alarm->company_uuid, sizeof(alarm->company_uuid), query.GetRowData(21));
        alarm->trace_id = ATOI(query.GetRowData(22));
        
        ReleaseDBConn(conn);
        return 0;
    }
    ReleaseDBConn(conn);
    return -1;
}

//处理ALARM
int Alarm::DealAlarm(ALARM* alarm)
{
    if (alarm == NULL)
    {
        return -1;
    }

    std::string DealUser = alarm->deal_user;
    boost::replace_all(DealUser, "'", "\\'");

    char sql[2048] = {0};
    ::snprintf(sql, 2048, "update Alarms set Status=%d,DealType='%d',DealResult='%s',DealUser='%s',DealTime=localtime() where ID=%d",
          alarm->status,
          alarm->deal_type,
          alarm->deal_result,
          DealUser.c_str(),
          alarm->id);
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    int ret = conn->Execute(sql) > 0 ? 0 : -1;
    //释放数据库连接
    ReleaseDBConn(conn);
    return ret;
}

//添加ALARM
int Alarm::AddAlarm(ALARM* alarm, const std::string& server_tag)
{
    if (alarm == NULL)
    {
        return -1;
    }
    std::string uuid;
    dbinterface::UUID::GenerateUUID(server_tag, uuid);
    Snprintf(alarm->uuid, sizeof(alarm->uuid), uuid.c_str());
    
    //插入数据构造
    std::map<std::string, std::string> str_map;
    str_map.emplace("AlarmType", alarm->alarm_type);
    str_map.emplace("DevicesMAC", alarm->mac);
    str_map.emplace("Node", alarm->device_node);
    str_map.emplace("sql_AlarmTime", "now()");
    str_map.emplace("UUID", alarm->uuid);
    if(strlen(alarm->device_uuid) != 0){
        str_map.emplace("DevicesUUID", alarm->device_uuid);
    }
    if(strlen(alarm->company_uuid) != 0){
        str_map.emplace("CompanyUUID", alarm->company_uuid);
    }
    
    std::map<std::string, int> int_map;
    int_map.emplace("MngAccountID", alarm->manager_account_id);
    int_map.emplace("UnitID", alarm->unit_id);
    int_map.emplace("Status", alarm->status);
    int_map.emplace("AlarmCode", alarm->alarm_code);
    int_map.emplace("AlarmLocation", alarm->alarm_location);
    int_map.emplace("AlarmZone", alarm->alarm_zone);
    int_map.emplace("AlarmCustomize", alarm->alarm_customize);
    int_map.emplace("RelayType", int(alarm->relay_type));
    int_map.emplace("RelayNum", alarm->relay_num);

    std::string table_name = "Alarms";//插入表名

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    int ret = conn->InsertData(table_name, str_map, int_map);
    //added by chenyc,2017-06-02,获取本回话最新插入的数据列ID,支持向app发送该id的告警消息.
    //因为SELECT last_insert_id()结果是基于会话的，所以即使有其他线程切换,再回来也是本线程上次执行的结果.
    char sql2[512] = {0};
    ::snprintf(sql2, 512,"SELECT last_insert_id()");

    CRldbQuery query(tmp_conn);
    query.Query(sql2);
    if (query.MoveToNextRow())
    {
        alarm->id = ATOI(query.GetRowData(0));
    }

    //释放数据库连接
    ReleaseDBConn(conn);
    return ret;
}

//更新告警状态
int Alarm::DealAlarmStatus(const ALARM_DEAL_INFO& alarm_deal_info)
{
    std::string user = alarm_deal_info.user;
    boost::replace_all(user, "'", "\\'");

    char sql[2048] = {0};
    ::snprintf(sql, 2048,"update Alarms set Status=1,DealType=0,DealResult='%s',DealUser='%s',DealTime=%s,DealResult='Deal' where ID=%d",
                  alarm_deal_info.result,
                  user.c_str(),
                  /*alarm_deal_info.time*/"now()",
                  ATOI(alarm_deal_info.alarm_id));
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    int ret = conn->Execute(sql) > 0 ? 0 : -1;
    //释放数据库连接
    ReleaseDBConn(conn);
    return ret;
}

//通过alarm id获取到告警解除时的相关信息
int Alarm::GetAlarmInfo(const std::string& id, ALARM_DEAL_OFFLINE_INFO& alarm_info)
{
    uint32_t alarm_id = ATOI(id.c_str());
    char sql[2048] = {0};
    ::snprintf(sql, 2048,"/*master*/select A.AlarmType,A.DevicesMAC,D.Location,A.MngAccountID,A.AlarmCode,A.AlarmZone,A.AlarmLocation,A.AlarmCustomize,A.UnitID,A.TraceID from Alarms A \
                    left join Devices D on A.DevicesMAC = D.MAC \
                    where A.ID = %d", alarm_id);

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(conn.get());

    query.Query(sql);
    if (query.MoveToNextRow())
    {
        Snprintf(alarm_info.alarm_type, sizeof(alarm_info.alarm_type), query.GetRowData(0));
        Snprintf(alarm_info.mac, sizeof(alarm_info.mac), query.GetRowData(1));
        Snprintf(alarm_info.device_location, sizeof(alarm_info.device_location), query.GetRowData(2));
        alarm_info.manager_account_id = ATOI(query.GetRowData(3));
        alarm_info.alarm_code = ATOI(query.GetRowData(4));
        alarm_info.alarm_zone = ATOI(query.GetRowData(5));
        alarm_info.alarm_location = ATOI(query.GetRowData(6));
        alarm_info.alarm_customize = ATOI(query.GetRowData(7));
        alarm_info.unit_id = ATOI(query.GetRowData(8));
        char *end_ptr;
        alarm_info.trace_id = std::strtoull(query.GetRowData(9), &end_ptr, 10);
        ReleaseDBConn(conn);
        return 0;
    }
    ReleaseDBConn(conn);
    return -1;
}

void Alarm::InsertEmegencyNotifyAlarmLog(const OfficeAccountList& app_list, int mng_id, int control_type, const std::string& server_tag)
{
    int alarm_code = 0;
    if (control_type == PmEmergencyDoorControlType::CONTROL_TYPE_OPEN)
    {
        alarm_code = (int)ALARM_CODE::EMERGENCY_NOTIFY_OPEN;
    }
    else if (control_type == PmEmergencyDoorControlType::CONTROL_TYPE_CLOSE)
    {
        alarm_code = (int)ALARM_CODE::EMERGENCY_NOTIFY_CLOSED;
    }

    ALARM alarm;
    memset(&alarm, 0, sizeof(alarm));
    
    alarm.alarm_code = alarm_code;
    alarm.manager_account_id = mng_id;
    alarm.status = (uint32_t)AlarmStatus::ALARM_STATUS_UNDEALED;
    OfficePersonnelInfo office_personnel_info;

    for (const auto& app : app_list)
    {
        memset(&office_personnel_info, 0, sizeof(office_personnel_info));
        dbinterface::OfficePersonnel::GetOfficePersonnelByPersonalAccountUUID(app.uuid, office_personnel_info);
        Snprintf(alarm.company_uuid, sizeof(alarm.company_uuid), office_personnel_info.office_company_uuid);
        Snprintf(alarm.device_node, sizeof(alarm.device_node), app.account);
        dbinterface::Alarm::AddAlarm(&alarm, server_tag);
        AK_LOG_INFO << "InsertEmegencyNotifyAlarmLog account = " << app.account << ", alarm_code = " << alarm_code;
    }

    return;
}

void Alarm::InsertEmegencyNotifyAlarmLog(const OfficeAdminInfo& admin, const std::string& account, int mng_id, int control_type, const std::string& server_tag)
{
    ALARM alarm;
    memset(&alarm, 0, sizeof(alarm));
    alarm.manager_account_id = mng_id;
    alarm.status = (uint32_t)AlarmStatus::ALARM_STATUS_UNDEALED;
    Snprintf(alarm.device_node, sizeof(alarm.device_node), account.c_str());
    Snprintf(alarm.company_uuid, sizeof(alarm.company_uuid), admin.office_company_uuid);
    if (control_type == PmEmergencyDoorControlType::CONTROL_TYPE_OPEN)
    {
        alarm.alarm_code = (int)ALARM_CODE::EMERGENCY_NOTIFY_OPEN;
    }
    else if (control_type == PmEmergencyDoorControlType::CONTROL_TYPE_CLOSE)
    {
        alarm.alarm_code = (int)ALARM_CODE::EMERGENCY_NOTIFY_CLOSED;
    }

    dbinterface::Alarm::AddAlarm(&alarm, server_tag);
    AK_LOG_INFO << "InsertEmegencyNotifyAlarmLog admin account_uuid = " << admin.account_uuid << ", alarm_code = " << alarm.alarm_code;
    return;
}


}


