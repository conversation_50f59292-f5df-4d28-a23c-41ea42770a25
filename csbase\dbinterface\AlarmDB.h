#ifndef __DB_ALARM_H__
#define __DB_ALARM_H__

#include <string>
#include <memory>
#include <tuple>
#include "AkcsCommonDef.h"
#include "dbinterface/PmEmergencyDoorLog.h"
#include "dbinterface/new-office/OfficeAdmin.h"
#include "dbinterface/office/OfficePersonalAccount.h"

typedef struct ALARM_T
{
    uint32_t id;
    uint32_t status;
    uint32_t deal_type;
    uint32_t unit_id;
    uint32_t manager_account_id;
    char alarm_type[64];
    char mac[32];
    char device_node[24];
    char device_uuid[64];
    char alarm_time[24];
    char deal_time[24];
    char deal_user[32];
    char deal_result[1024];
    char uuid[64];
    int alarm_code;//告警类型 用于程序判断和多语言展示判断
    int alarm_zone;//防区
    int alarm_location;//位置
    int alarm_customize;//1 设备自定义alarm
    uint64_t trace_id;
    int is_read;
    int relay_num;
    char company_uuid[64];
    RelayType relay_type;
    
    struct ALARM_T* next;

    ALARM_T() {
        memset(this, 0, sizeof(*this));
    }    
} ALARM;

typedef struct ALARM_DEAL_T
{
    char protocal[16];
    char community[32];
    char area_node[32];
    char alarm_id[16];
    char user[32];
    char result[64];
    char type[64];//告警的处理类型
    char time[24];
    char mac[20]; //v3.1加密
    char device_name[64];
    uint32_t manager_account_id;
    int alarm_code;
    int alarm_zone;
    int alarm_location;
    int alarm_customize;//1 设备自定义alarm
    uint32_t unit_id;
} ALARM_DEAL_INFO;

typedef struct ALARM_DEAL_OFFLINE_T
{
    char mac[20];
    char alarm_type[64];
    char device_location[64];
    uint32_t manager_account_id;
    int alarm_code;
    int alarm_location;
    int alarm_customize;//1 设备自定义alarm
    int alarm_zone;
    uint32_t unit_id;
    uint64_t trace_id;
} ALARM_DEAL_OFFLINE_INFO;

namespace dbinterface{
class Alarm
{
public:
    Alarm();
    ~Alarm();
    static int GetAlarm(uint32_t id, ALARM* alarm);
    static int DealAlarm(ALARM* alarm);
    static int AddAlarm(ALARM* alarm, const std::string& server_tag);
    static int DealAlarmStatus(const ALARM_DEAL_INFO& alarm_deal_info);
    static int GetAlarmInfo(const std::string& id, ALARM_DEAL_OFFLINE_INFO& alarm_info);
    static void InsertEmegencyNotifyAlarmLog(const OfficeAccountList& app_list, int mng_id, int control_type, const std::string& server_tag);
    static void InsertEmegencyNotifyAlarmLog(const OfficeAdminInfo& admin_info, const std::string& account, int mng_id, int control_type, const std::string& server_tag);
private:
};

}


#endif
