#include <sstream>
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/InterfaceComm.h"
#include "AlexaToken.h"
#include <string.h>
#include "AkLogging.h"
#include "util.h"

namespace dbinterface{
AlexaToken::AlexaToken()
{

}

AlexaToken::~AlexaToken()
{

}

static const std::string alexa_token_info_sec = "AlexaUID,AccessToken,RefreshToken,AwsAccessToken,AwsRefreshToken,NodeUUID,ProjectUUID,PersonalAccountUUID ";

void AlexaToken::GetAlexaTokenInfoFromSql(CRldbQuery& query, AlexaTokenInfo &alexa_token_info)
{
    Snprintf(alexa_token_info.alexa_uid, sizeof(alexa_token_info.alexa_uid), query.GetRowData(0)); 
    Snprintf(alexa_token_info.access_token, sizeof(alexa_token_info.access_token), query.GetRowData(1)); 
    Snprintf(alexa_token_info.refresh_token, sizeof(alexa_token_info.refresh_token), query.GetRowData(2)); 
    Snprintf(alexa_token_info.aws_access_token, sizeof(alexa_token_info.aws_access_token), query.GetRowData(3)); 
    Snprintf(alexa_token_info.aws_refresh_token, sizeof(alexa_token_info.aws_refresh_token), query.GetRowData(4)); 
    Snprintf(alexa_token_info.node_uuid, sizeof(alexa_token_info.node_uuid), query.GetRowData(5)); 
    Snprintf(alexa_token_info.project_uuid, sizeof(alexa_token_info.project_uuid), query.GetRowData(6)); 
    Snprintf(alexa_token_info.personal_account_uuid, sizeof(alexa_token_info.personal_account_uuid), query.GetRowData(7)); 
    return;
}

int AlexaToken::GetAlexaTokenInfoListByProjectUUID(const std::string &project_uuid, AlexaTokenInfoList &alexa_token_info_list)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::stringstream stream_sql;
    stream_sql << "select " << alexa_token_info_sec << " from AlexaToken where ProjectUUID = '" << project_uuid << "'";

    CRldbQuery query(tmp_conn);
    query.Query(stream_sql.str());

    while (query.MoveToNextRow())
    {
        AlexaTokenInfo alexa_token_info;
        GetAlexaTokenInfoFromSql(query, alexa_token_info);
        alexa_token_info_list.push_back(alexa_token_info);
    }
    
    ReleaseDBConn(conn);
    return 0;
}

int AlexaToken::GetAlexaTokenInfoByNodeUUID(const std::string &node_uuid, AlexaTokenInfo &alexa_token_info)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::stringstream stream_sql;
    stream_sql << "select " << alexa_token_info_sec << " from AlexaToken where NodeUUID = '" << node_uuid << "'";

    CRldbQuery query(tmp_conn);
    query.Query(stream_sql.str());

    if (query.MoveToNextRow())
    {
        GetAlexaTokenInfoFromSql(query, alexa_token_info);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    
    ReleaseDBConn(conn);
    return 0;
}


int AlexaToken::CheckDevRelateToAlexaUser(const std::string &project_uuid)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::stringstream stream_sql;
    stream_sql << "select count(1) from AlexaToken where ProjectUUID = '" << project_uuid <<"'";

    CRldbQuery query(tmp_conn);
    query.Query(stream_sql.str());
    
    if(query.MoveToNextRow())
    {   
        int count = ATOI(query.GetRowData(0));
        ReleaseDBConn(conn);
        return count > 0 ? 0 : -1;
    }
    
    ReleaseDBConn(conn);
    return -1;
}

}


