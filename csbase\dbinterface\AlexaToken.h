#ifndef __ALEXA_TOKEN_H__
#define __ALEXA_TOKEN_H__
#include <string>
#include <memory>

namespace dbinterface{

typedef struct AlexaTokenInfo_T
{
    char alexa_uid[256];              // alexa的uid
    char access_token[128];           // ak_cloud的token
    char refresh_token[128];          // ak_cloud的token
    char aws_access_token[128];       // aws的token
    char aws_refresh_token[128];      // aws的token
    char node_uuid[64];               // 主账号的uuid
    char project_uuid[64];            // community/office uuid / per nodeuuid
    char personal_account_uuid[64];   // PersonalAccountUserInfo AppMainUserAccount 对应的用户的UUID

    AlexaTokenInfo_T() {
        memset(this, 0, sizeof(*this));
    }
}AlexaTokenInfo;

typedef std::vector<AlexaTokenInfo> AlexaTokenInfoList;

class AlexaToken
{
public:
    AlexaToken();
    ~AlexaToken();

    static int GetAlexaTokenInfoListByProjectUUID(const std::string &project_uuid, AlexaTokenInfoList &alexa_token_info_list);
    static int GetAlexaTokenInfoByNodeUUID(const std::string &node_uuid, AlexaTokenInfo &alexa_token_info);
    static int CheckDevRelateToAlexaUser(const std::string &project_uuid);    
private:
    static void GetAlexaTokenInfoFromSql(CRldbQuery& query, AlexaTokenInfo &alexa_token_info);
};

}


#endif

