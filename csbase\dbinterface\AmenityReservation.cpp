#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "AmenityReservation.h"

namespace dbinterface {

static const std::string amenity_reservation_info_sec = " ID,UUID,AmenityUUID,ProjectUUID,UnitUUID,RoomUUID,PersonalAccountUUID,KeyAllowedCounts,StartTime,EndTime,QrCodeUrl,TmpKey, \
                                                                                        Status,InvalidReason,AmenityName,AmenityDescription,BookerName,AptName,KeyUsedCounts ";


void AmenityReservation::GetAmenityReservationFromSql(AmenityReservationInfo& amenity_reservation_info, CRldbQuery& query)
{
    amenity_reservation_info.id = ATOI(query.GetRowData(0));
    Snprintf(amenity_reservation_info.uuid, sizeof(amenity_reservation_info.uuid), query.GetRowData(1));
    Snprintf(amenity_reservation_info.amenity_uuid, sizeof(amenity_reservation_info.amenity_uuid), query.GetRowData(2));
    Snprintf(amenity_reservation_info.project_uuid, sizeof(amenity_reservation_info.project_uuid), query.GetRowData(3));
    Snprintf(amenity_reservation_info.unit_uuid, sizeof(amenity_reservation_info.unit_uuid), query.GetRowData(4));
    Snprintf(amenity_reservation_info.room_uuid, sizeof(amenity_reservation_info.room_uuid), query.GetRowData(5));
    Snprintf(amenity_reservation_info.personal_account_uuid, sizeof(amenity_reservation_info.personal_account_uuid), query.GetRowData(6));
    amenity_reservation_info.key_allowed_counts = ATOI(query.GetRowData(7));
    Snprintf(amenity_reservation_info.start_time, sizeof(amenity_reservation_info.start_time), query.GetRowData(8));
    Snprintf(amenity_reservation_info.end_time, sizeof(amenity_reservation_info.end_time), query.GetRowData(9));
    Snprintf(amenity_reservation_info.qr_code_url, sizeof(amenity_reservation_info.qr_code_url), query.GetRowData(10));
    amenity_reservation_info.tmp_key = ATOI(query.GetRowData(11));
    amenity_reservation_info.status = ATOI(query.GetRowData(12));
    amenity_reservation_info.invalid_reason = ATOI(query.GetRowData(13));
    Snprintf(amenity_reservation_info.amenity_name, sizeof(amenity_reservation_info.amenity_name), query.GetRowData(14));
    Snprintf(amenity_reservation_info.amenity_description, sizeof(amenity_reservation_info.amenity_description), query.GetRowData(15));
    Snprintf(amenity_reservation_info.booker_name, sizeof(amenity_reservation_info.booker_name), query.GetRowData(16));
    Snprintf(amenity_reservation_info.apt_name, sizeof(amenity_reservation_info.apt_name), query.GetRowData(17));
    amenity_reservation_info.key_used_counts = ATOI(query.GetRowData(18));
    return;
}

int AmenityReservation::GetAmenityReservationByUUID(const std::string& uuid, AmenityReservationInfo& amenity_reservation_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << amenity_reservation_info_sec << " from AmenityReservation where UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetAmenityReservationFromSql(amenity_reservation_info, query);
    }
    else
    {
        AK_LOG_WARN << "get AmenityReservationInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}

int AmenityReservation::GetAmenityReservationByTmpKey(const std::string& tmp_key, AmenityReservationInfo& amenity_reservation_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << amenity_reservation_info_sec << " from AmenityReservation where TmpKey = '" << tmp_key << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetAmenityReservationFromSql(amenity_reservation_info, query);
    }
    else
    {
        AK_LOG_WARN << "get AmenityReservationInfo by TmpKey failed, TmpKey = " << tmp_key;
        return -1;
    }
    return 0;
}

int AmenityReservation::GetAmenityReservationByTmpKeyAndTime(const std::string& tmp_key, const std::string& now_time, AmenityReservationInfo& amenity_reservation_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << amenity_reservation_info_sec << " from AmenityReservation where TmpKey = '" << tmp_key 
                     << "' and StartTime < '" << now_time <<  "' and EndTime > '" << now_time << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetAmenityReservationFromSql(amenity_reservation_info, query);
    }
    else
    {
        return -1;
    }
    return 0;
}

int AmenityReservation::UpdateTmpKeyUsedCounts(const std::string& uuid)
{
    std::stringstream stream_sql;
    stream_sql << "update AmenityReservation set KeyUsedCounts = KeyUsedCounts + 1 where UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    db_conn->Execute(stream_sql.str());

    return 0;
}

}