#ifndef __DB_AMENITY_RESERVATION_H__
#define __DB_AMENITY_RESERVATION_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct AmenityReservationInfo_T
{
    int id;
    char uuid[36];
    char amenity_uuid[36];
    char project_uuid[36];
    char unit_uuid[36];
    char room_uuid[36];
    char personal_account_uuid[36];
    int key_allowed_counts;
    int key_used_counts;
    char start_time[32];
    char end_time[32];
    char qr_code_url[128];
    int tmp_key;
    int status;
    int invalid_reason;
    char amenity_name[64];
    char amenity_description[128];
    char booker_name[128];
    char apt_name[512];
    AmenityReservationInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} AmenityReservationInfo;

namespace dbinterface {

class AmenityReservation
{
public:
    static int GetAmenityReservationByUUID(const std::string& uuid, AmenityReservationInfo& amenity_reservation_info);
    static int GetAmenityReservationByTmpKey(const std::string& tmp_key, AmenityReservationInfo& amenity_reservation_info);
    static int GetAmenityReservationByTmpKeyAndTime(const std::string& tmp_key, const std::string& now_time, AmenityReservationInfo& amenity_reservation_info);
    static int UpdateTmpKeyUsedCounts(const std::string& uuid);

private:
    AmenityReservation() = delete;
    ~AmenityReservation() = delete;
    static void GetAmenityReservationFromSql(AmenityReservationInfo& amenity_reservation_info, CRldbQuery& query);
};

}
#endif