#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "AnalogDevice.h"

namespace dbinterface {

static const std::string analog_device_info_sec = " UUID,AccountUUID,CommunityUnitUUID,PersonalAccountUUID,AnalogDeviceName,AnalogDeviceNumber,AnalogDeviceDtmf,ID ";

void AnalogDevice::GetAnalogDeviceFromSql(AnalogDeviceInfo& analog_device_info, CRldbQuery& query)
{
    Snprintf(analog_device_info.uuid, sizeof(analog_device_info.uuid), query.GetRowData(0));
    Snprintf(analog_device_info.account_uuid, sizeof(analog_device_info.account_uuid), query.GetRowData(1));
    Snprintf(analog_device_info.community_unit_uuid, sizeof(analog_device_info.community_unit_uuid), query.GetRowData(2));
    Snprintf(analog_device_info.personal_account_uuid, sizeof(analog_device_info.personal_account_uuid), query.GetRowData(3));
    Snprintf(analog_device_info.analog_device_name, sizeof(analog_device_info.analog_device_name), query.GetRowData(4));
    Snprintf(analog_device_info.analog_device_number, sizeof(analog_device_info.analog_device_number), query.GetRowData(5));
    Snprintf(analog_device_info.dtmf_code, sizeof(analog_device_info.dtmf_code), query.GetRowData(6));
    analog_device_info.id = std::stoul(query.GetRowData(7));
    return;
}

int AnalogDevice::GetAnalogDeviceByUUID(const std::string& uuid, AnalogDeviceInfo& analog_device_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << analog_device_info_sec << " from AnalogDevice where UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetAnalogDeviceFromSql(analog_device_info, query);
    }
    else
    {
        AK_LOG_WARN << "get AnalogDeviceInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}

DatabaseExistenceStatus AnalogDevice::GetAnalogDeviceNodeUUIDMapByProjectUUID(const std::string& project_uuid, AnalogDeviceNodeUuidMap& node_uuid_analog_device_map)
{
    std::stringstream stream_sql;
    stream_sql << "select " << analog_device_info_sec << " from AnalogDevice where AccountUUID = '" << project_uuid << "'";
    GET_DB_CONN_ERR_RETURN(conn, DatabaseExistenceStatus::QUERY_ERROR);
    CRldbQuery query(conn.get());
    query.Query(stream_sql.str());

    while (query.MoveToNextRow())
    {
        AnalogDeviceInfo analog_device_info;
        GetAnalogDeviceFromSql(analog_device_info, query);
        node_uuid_analog_device_map.insert(std::make_pair(analog_device_info.personal_account_uuid, analog_device_info));
    }

    return DatabaseExistenceStatus::EXIST;
}

}