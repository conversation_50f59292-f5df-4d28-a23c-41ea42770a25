#ifndef __DB_ANALOG_DEVICE_H__
#define __DB_ANALOG_DEVICE_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"
#include <list>
#include "AkcsCommonDef.h"

typedef struct AnalogDeviceInfo_T
{
    char analog_device_name[256];
    char uuid[36];
    char account_uuid[36];
    char community_unit_uuid[36];
    char personal_account_uuid[36];
    char analog_device_number[16];
    char node[16];
    char uid[16]; //用于contact关联
    uint64_t id;
    char dtmf_code[8];
    AnalogDeviceInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} AnalogDeviceInfo;

typedef std::list<AnalogDeviceInfo> AnalogDeviceList;
typedef std::multimap<std::string/*node_uuid*/, AnalogDeviceInfo> AnalogDeviceNodeUuidMap;

namespace dbinterface {

class AnalogDevice
{
public:
    static int GetAnalogDeviceByUUID(const std::string& uuid, AnalogDeviceInfo& analog_device_info);
    static DatabaseExistenceStatus GetAnalogDeviceNodeUUIDMapByProjectUUID(const std::string& project_uuid, AnalogDeviceNodeUuidMap& node_uuid_analog_device_map);

private:
    AnalogDevice() = delete;
    ~AnalogDevice() = delete;
    static void GetAnalogDeviceFromSql(AnalogDeviceInfo& analog_device_info, CRldbQuery& query);
};

}
#endif