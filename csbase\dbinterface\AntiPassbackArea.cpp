#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "AntiPassbackArea.h"

namespace dbinterface{

static const std::string antipassback_area_info_sec = " Enable,ScheduleType,RestrictType,RestrictTime,Name,StartTime,StopTime,UUID,AccountUUID,CreatorType,OfficeCompanyUUID ";

void AntiPassbackArea::GetAntiPassbackAreaFromSql(AntiPassbackAreaInfo& area_info, CRldbQuery& query)
{
    area_info.enable = ATOI(query.GetRowData(0));
    area_info.schedule_type = AntiPassbackScheduleType(ATOI(query.GetRowData(1)));
    area_info.restriction_type = AntiPassbackRestrictionType(ATOI(query.GetRowData(2)));
    area_info.restriction_timeout = ATOI(query.GetRowData(3));
    Snprintf(area_info.name, sizeof(area_info.name), query.GetRowData(4));
    Snprintf(area_info.start_time, sizeof(area_info.start_time), query.GetRowData(5));
    Snprintf(area_info.stop_time, sizeof(area_info.stop_time), query.GetRowData(6));
    Snprintf(area_info.uuid, sizeof(area_info.uuid), query.GetRowData(7));
    Snprintf(area_info.account_uuid, sizeof(area_info.account_uuid), query.GetRowData(8));
    area_info.creator_type = AntiPassbackAreaCreatorType(ATOI(query.GetRowData(9)));
    Snprintf(area_info.office_company_uuid, sizeof(area_info.office_company_uuid), query.GetRowData(10));

    return;
}

int AntiPassbackArea::GetAntiPassbackAreaByUUID(const std::string& uuid, AntiPassbackAreaInfo& area_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << antipassback_area_info_sec << " from AntiPassbackArea where UUID = '" << uuid << "'";

    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetAntiPassbackAreaFromSql(area_info, query);
    }
    else
    {
        AK_LOG_INFO << "GetAntiPassbackAreaByUUID error, area uuid = " << uuid;
        return -1;
    }
    return 0;
}

int AntiPassbackArea::GetAntiPassbackListByAccountUUID(const std::string& account_uuid, AntiPassbackAreaInfoList& antipassback_area_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << antipassback_area_info_sec << " from AntiPassbackArea where AccountUUID = '" << account_uuid << "'";

    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while(query.MoveToNextRow())
    {
        AntiPassbackAreaInfo area_info;
        GetAntiPassbackAreaFromSql(area_info, query);
        antipassback_area_list.push_back(area_info);
    }
    return 0;
}

bool AntiPassbackArea::InRestrictionTime(const AntiPassbackAreaInfo& area_info, const std::string& time_zone, std::map<std::string, AKCS_DST>& date_info)
{
    if (area_info.schedule_type == AntiPassbackScheduleType::ALWAYS)
    {
        AK_LOG_INFO << "AntipassbackArea Check InRestrictTime, schedule type is always, area_uuid = " << area_info.uuid << ", area_name = " << area_info.name;
        return true;
    }
    else if (area_info.schedule_type == AntiPassbackScheduleType::DALIY)
    {
        std::string timenow = GetDailyDateTimeByTimeZoneStr(time_zone, date_info);
        AK_LOG_INFO << "AntipassbackArea Check InRestrictTime, schedule type is daliy, area_uuid = " << area_info.uuid << ", area_name = " << area_info.name << ", timenow = " << timenow;
        
        std::stringstream stream_sql;
        stream_sql << "select count(*) from AntiPassbackArea where UUID = '" << area_info.uuid << "' and StartTime < '" << timenow << "' and StopTime > '" << timenow << "'";

        GET_DB_CONN_ERR_RETURN(db_conn, -1);

        CRldbQuery query(db_conn.get());
        query.Query(stream_sql.str());
        if (query.MoveToNextRow())
        {
            return ATOI(query.GetRowData(0)) > 0;
        }
    }

    return false;
}

int AntiPassbackArea::GetAntiPassbackAreaDevListByUUID(const std::string& uuid, AkcsStringSet &dev_list)
{
    std::stringstream stream_sql;
    stream_sql << "select Door.DevicesUUID from AntiPassbackArea Area left join AntiPassbackDoor Door on Door.AntiPassbackAreaUUID = Area.UUID where Area.UUID = '" << uuid << "'";

    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        dev_list.insert(query.GetRowData(0));
    }
    
    return 0;
}


}
