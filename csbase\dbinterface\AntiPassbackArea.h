#ifndef __DB_ANTI_PASSBACK_AREA_H__
#define __DB_ANTI_PASSBACK_AREA_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "util_time.h"
#include "BasicDefine.h"
#include "AkcsCommonDef.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct AntiPassbackAreaInfo_T
{
    int enable;
    int restriction_timeout;                         // 区域限制的超时时间: 分钟
    AntiPassbackScheduleType schedule_type;          // 反潜回时间计划类型: 0=Always, 1=Daily
    AntiPassbackRestrictionType restriction_type;    // 限制类型: 0=off, 1=Deny Access, 2=Log violations only
    AntiPassbackAreaCreatorType creator_type;

    char uuid[64];         
    char name[256];          // 区域名称
    char start_time[16];     // Daily计划的开始时间点
    char stop_time[16];      // Daily计划的结束时间点
    char account_uuid[64];  
    char office_company_uuid[64];
}AntiPassbackAreaInfo;

using AntiPassbackAreaInfoList = std::vector<AntiPassbackAreaInfo>;

namespace dbinterface{

class AntiPassbackArea
{
public:
    static int GetAntiPassbackAreaByUUID(const std::string& uuid, AntiPassbackAreaInfo& area_info);
    static bool InRestrictionTime(const AntiPassbackAreaInfo& area_info, const std::string& time_zone, std::map<std::string, AKCS_DST>& date_info);
    static int GetAntiPassbackAreaDevListByUUID(const std::string& uuid, AkcsStringSet &dev_list);
    static int GetAntiPassbackListByAccountUUID(const std::string& account_uuid, AntiPassbackAreaInfoList& antipassback_area_list);
private:
    AntiPassbackArea() = delete;
    ~AntiPassbackArea() = delete;
    static void GetAntiPassbackAreaFromSql(AntiPassbackAreaInfo& area_info, CRldbQuery& query);
};

}


#endif
