#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "AntiPassbackDoor.h"

namespace dbinterface{

static const std::string anti_passback_door_info_sec = " D.AntiPassbackAreaUUID,D.<PERSON>UUID,D.RelayType,D.<PERSON>,D.Type,D.UUID,A.Enable ";

void AntiPassBackDoor::GetAntiPassbackDoorFromSql(AntiPassBackDoorInfo& anti_passback_door, CRldbQuery& query)
{
    Snprintf(anti_passback_door.area_uuid, sizeof(anti_passback_door.area_uuid), query.GetRowData(0));
    Snprintf(anti_passback_door.dev_uuid, sizeof(anti_passback_door.dev_uuid), query.GetRowData(1));
    anti_passback_door.relay_type = AntiPassbackRelayType(ATOI(query.GetRowData(2)));
    anti_passback_door.relay_num = ATOI(query.GetRowData(3));
    anti_passback_door.door_type = AntiPassbackDoorType(ATOI(query.GetRowData(4)));
    Snprintf(anti_passback_door.uuid, sizeof(anti_passback_door.uuid), query.GetRowData(5));
    anti_passback_door.area_enable = ATOI(query.GetRowData(6));
    return;
}

int AntiPassBackDoor::GetAntiPassBackDoorListByDevUUID(const std::string& dev_uuid, AntiPassBackDoorInfoList& door_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << anti_passback_door_info_sec << " from AntiPassbackDoor D left join AntiPassbackArea A on D.AntiPassbackAreaUUID = A.UUID where D.DevicesUUID = '" << dev_uuid << "'";

    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    
    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        AntiPassBackDoorInfo anti_passback_door;
        GetAntiPassbackDoorFromSql(anti_passback_door, query);
        door_list.push_back(anti_passback_door);
    }

    return 0;
}

int AntiPassBackDoor::GetAntiPassBackDoorListByAreaUUID(const std::string& area_uuid, AntiPassBackDoorInfoList& door_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << anti_passback_door_info_sec << " from AntiPassbackDoor D left join AntiPassbackArea A on D.AntiPassbackAreaUUID = A.UUID where D.AntiPassbackAreaUUID = '" << area_uuid << "'";

    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    
    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        AntiPassBackDoorInfo anti_passback_door;
        GetAntiPassbackDoorFromSql(anti_passback_door, query);
        door_list.push_back(anti_passback_door);
    }

    return 0;
}

// 获取项目下的所有返潜回Door
int AntiPassBackDoor::GetAntiPassBackDoorListByProjectUUID(const std::string& project_uuid, AntiPassBackDoorInfoList& door_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << anti_passback_door_info_sec << " from AntiPassbackDoor D left join AntiPassbackArea A on D.AntiPassbackAreaUUID = A.UUID where A.AccountUUID = '" << project_uuid << "'";

    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    
    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        AntiPassBackDoorInfo anti_passback_door;
        GetAntiPassbackDoorFromSql(anti_passback_door, query);
        door_list.push_back(anti_passback_door);
    }

    return 0;
}

int AntiPassBackDoor::GetAntiPassBackDoorInfo(const std::string& dev_uuid, AntiPassbackRelayType relay_type, int relay_num, AntiPassBackDoorInfo& anti_passback_door)
{
    std::stringstream stream_sql;
    stream_sql << "select " << anti_passback_door_info_sec << " from AntiPassbackDoor D left join AntiPassbackArea A on D.AntiPassbackAreaUUID = A.UUID where D.DevicesUUID = '" << dev_uuid << "' and D.RelayType = " << (int)relay_type << " and D.RelayNum = " << relay_num;

    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    
    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if  (query.MoveToNextRow())
    {
        GetAntiPassbackDoorFromSql(anti_passback_door, query);
    }
    else
    {
        AK_LOG_WARN << "GetAntiPassBackDoorInfo failed, dev_uuid = " << dev_uuid << ", relay_type = " << int(relay_type) << ", relay_num = " << relay_num;
        return -1;
    }

    return 0;
}

}
