#ifndef __DB_ANTI_PASSBACK_DOOR_H__
#define __DB_ANTI_PASSBACK_DOOR_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"


enum class AntiPassbackDoorType
{
    NORMAL = 0,
    ENTRY = 1,
    EXIT = 2
};

enum class AntiPassbackRelayType
{
    RELAY = 1,
    SECURITY_RELAY = 2
};

typedef struct AntiPassBackDoorInfo_T
{
    char uuid[64];
    char dev_uuid[64];
    char area_uuid[64];
    int  relay_num;
    int  area_enable;
    AntiPassbackRelayType relay_type;
    AntiPassbackDoorType door_type;

    AntiPassBackDoorInfo_T() {
        memset(this, 0, sizeof(*this));
    }
}AntiPassBackDoorInfo;

using AntiPassBackDoorInfoList = std::vector<AntiPassBackDoorInfo>;

namespace dbinterface{

class AntiPassBackDoor
{
public:
    // 一台设备的relay可以绑定到不同AntiPassbackArea,会对应多个条AntiPassbackDoor记录
    static int GetAntiPassBackDoorListByDevUUID(const std::string& dev_uuid, AntiPassBackDoorInfoList& door_list);
    static int GetAntiPassBackDoorListByAreaUUID(const std::string& area_uuid, AntiPassBackDoorInfoList& door_list);
    static int GetAntiPassBackDoorListByProjectUUID(const std::string& project_uuid, AntiPassBackDoorInfoList& door_list);
    static int GetAntiPassBackDoorInfo(const std::string& dev_uuid, AntiPassbackRelayType relay_type, int relay_num, AntiPassBackDoorInfo& anti_passback_door);
    
private:
    AntiPassBackDoor() = delete;
    ~AntiPassBackDoor() = delete;

    static void GetAntiPassbackDoorFromSql(AntiPassBackDoorInfo& anti_passback_door, CRldbQuery& query);
};

}

#endif
