#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "dbinterface/ApiUsageInfo.h"
#include <map>
#include <string>
#include "Rldb.h"

namespace dbinterface {

int ApiUsageInfo::InsertOrUpdateApiUsageInfo(const std::string& user, const std::string& api_name)
{
    std::map<std::string, std::string> insert_str_datas;
    insert_str_datas.emplace("User", user);
    insert_str_datas.emplace("ApiName", api_name);
    
    std::map<std::string, int> insert_int_datas;
    
    std::map<std::string, std::string> update_str_datas;
    update_str_datas.emplace("sql_LastUseTime","CURRENT_TIMESTAMP");
    
    std::map<std::string, int> update_int_datas;
    
    std::string table_name= "ApiUsageInfo";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    CRldb* conn = db_conn.get();
    int ret = conn->InsertOrUpdateData(table_name, insert_str_datas, insert_int_datas, update_str_datas, update_int_datas);
    
    if (ret < 0) {
        AK_LOG_WARN << "Failed to insert or update data";
    }
    return ret;
}


}