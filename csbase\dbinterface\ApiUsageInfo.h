#ifndef __DB_API_USAGE_INFO_H__
#define __DB_API_USAGE_INFO_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

namespace dbinterface {

class ApiUsageInfo
{
public:
    static int InsertOrUpdateApiUsageInfo(const std::string& user, const std::string& api_name);
private:
    ApiUsageInfo() = delete;
    ~ApiUsageInfo() = delete;
    
};

}
#endif