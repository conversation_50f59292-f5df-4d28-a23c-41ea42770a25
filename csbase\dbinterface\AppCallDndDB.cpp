#include <sstream>
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/InterfaceComm.h"
#include "AppCallDndDB.h"
#include <string.h>
#include "AkLogging.h"

namespace dbinterface{
AppCallDnd::AppCallDnd()
{

}

AppCallDnd::~AppCallDnd()
{

}

int AppCallDnd::GetAppDndInfo(const std::string& account, AppDndInfo& dnd_info)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* rldb_conn = conn.get();
    if (NULL == rldb_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::stringstream stream_sql;
    stream_sql << "select a.Status,a.StartTime,a.EndTime from AppCallDND a "
                  " where a.Account = " << account << " limit 1";

    CRldbQuery query(rldb_conn);
    query.Query(stream_sql.str());

    while (query.MoveToNextRow())
    {
        dnd_info.status = ATOI(query.GetRowData(0));
        dnd_info.start_time = ATOI(query.GetRowData(1));
        dnd_info.end_time = ATOI(query.GetRowData(2));
    }

    ReleaseDBConn(conn);
    return 0;
}


}


