#ifndef _APP_CALL_DND_H_
#define _APP_CALL_DND_H_

#include <string>
#include <memory>
#include <tuple>

typedef struct AppDndInfo_T
{
	unsigned int account_id;
	char account[33];
	int status;
	int start_time;
	int end_time;
}AppDndInfo;


namespace dbinterface{
class AppCallDnd
{
public:
    AppCallDnd();
    ~AppCallDnd();
    static int GetAppDndInfo(const std::string& account, AppDndInfo& dnd_info);
private:
};

}


#endif
