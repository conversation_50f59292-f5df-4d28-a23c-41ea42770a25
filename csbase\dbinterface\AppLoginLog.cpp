#include <sstream>
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/InterfaceComm.h"
#include "AppLoginLog.h"
#include <string.h>
#include "AkLogging.h"

namespace dbinterface{
AppLoginLog::AppLoginLog()
{

}

AppLoginLog::~AppLoginLog()
{

}

int AppLoginLog::InsertAppLoginLog(const std::string &uid, const std::string &node)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    std::stringstream sql;
    sql << "INSERT INTO AppLoginLog"
               << " (Uid,Node) VALUES ('"
               << uid << "','"
               << node << "');";

    if (conn->Execute(sql.str()) < 0)
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;
}


}


