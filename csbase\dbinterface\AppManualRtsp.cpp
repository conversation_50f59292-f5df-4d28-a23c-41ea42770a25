#include <sstream>
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/InterfaceComm.h"
#include "AppManualRtsp.h"
#include <string.h>
#include "AkLogging.h"

namespace dbinterface{
AppManualRtsp::AppManualRtsp()
{

}

AppManualRtsp::~AppManualRtsp()
{

}

void AppManualRtsp::InsertAppManualRtsp(const std::string &mac, const std::string &account, const std::string & node, 
                                                    const std::string & create_time, int duration)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }
    char sql[1024] = "";
    ::snprintf(sql, sizeof(sql), "insert into AppManualRtsp(CreateTime, UserAccount, Account, MAC, Duration) \
    			values('%s','%s','%s','%s','%d')", create_time.c_str(), account.c_str(), node.c_str(), mac.c_str(), duration);
    
    conn->Execute(sql);
    ReleaseDBConn(conn);
    return ;
}


}


