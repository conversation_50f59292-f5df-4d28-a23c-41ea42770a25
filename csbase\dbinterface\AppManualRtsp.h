#ifndef _APP_MANUAL_RTSP_H_
#define _APP_MANUAL_RTSP_H_

#include <string>
#include <memory>
#include <tuple>

namespace dbinterface{
class AppManualRtsp
{
public:
    AppManualRtsp();
    ~AppManualRtsp();
    static void InsertAppManualRtsp(const std::string &mac, const std::string &account, const std::string & node, 
                                                    const std::string & create_time, int duration);
private:
};

}


#endif
