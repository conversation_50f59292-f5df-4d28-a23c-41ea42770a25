#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "AppPushTokenDB.h"
#include <string.h>
#include "AkLogging.h"
#include "util.h"

namespace dbinterface
{

AppPushToken::AppPushToken()
{

}

int AppPushToken::UpdateAppSmartType(const std::string &uid, int is_set)
{
    std::stringstream sql;
    sql << "/*master*/SELECT ID FROM AppPushToken WHERE Account= '"
              << uid
              << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        sql.str("");
        sql << "update AppPushToken set AppOem=" << is_set << " where Account=" << uid;
        tmp_conn->Execute(sql.str());
    }
    else
    {
        char sz_sql[1024] = {0};
        ::snprintf(sz_sql, sizeof(sz_sql), "insert into AppPushToken(Account, AppOem) values('%s','%d');", uid.c_str(), is_set);
         
        std::string str_sql = sz_sql;
        int ret = tmp_conn->Execute(str_sql) > 0 ? 0 : -1;
        if (ret == -1)
        {
            AK_LOG_WARN << "insert error " << sz_sql;
            ReleaseDBConn(conn);
            return -1;
        }
        ReleaseDBConn(conn);
        return 0;
    }
    ReleaseDBConn(conn);
    return 0;
}

int AppPushToken::UpdateAppPushInfo(const std::string uid, const AppPushTokenInfo& token_info)
{
    std::stringstream streamsql;
    streamsql << "/*master*/SELECT ID FROM AppPushToken WHERE Account= '"
              << uid
              << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
    query.Query(streamsql.str());
    if (query.MoveToNextRow())
    {
        ATOI(query.GetRowData(0));
    }
    else
    {
        char sql[2048] = {0};
        ::snprintf(sql, sizeof(sql), "insert into AppPushToken(Account, AppType, FcmPushToken, IOSPushToken, VoipToken,\
            Node, Login, Version,AppVersion,Language,Oem, IsDynamicsIV) values('%s','%d','%s','%s', '%s', '%s', '%d', '%d', '%s', '%s', '',%d);", \
                   uid.c_str(), token_info.mobile_type, token_info.fcm_token, token_info.token, \
                   token_info.voip_token,  token_info.node, 1, token_info.common_version, \
                   token_info.app_version, token_info.language, token_info.is_dy_iv);
        std::string str_sql = sql;
        int ret = conn->Execute(str_sql) > 0 ? 0 : -1;
        if (ret == -1)
        {
            AK_LOG_WARN << "insert error " << sql;
            ReleaseDBConn(conn);
            return -1;
        }
        ReleaseDBConn(conn);
        return 0;
    }
    char sql[2048] = {0};
    ::snprintf(sql, sizeof(sql), "update AppPushToken set FcmPushToken='%s', IOSPushToken='%s',VoipToken='%s',\
        Version='%d',AppType='%d', Node='%s', AppVersion='%s',Language='%s',OnlineTime=now(), IsDynamicsIV=%d where Account='%s'", \
               token_info.fcm_token, token_info.token, token_info.voip_token, token_info.common_version, 
               token_info.mobile_type,token_info.node, token_info.app_version, token_info.language, token_info.is_dy_iv, uid.c_str());
    std::string str_sql = sql;
    int ret = conn->Execute(str_sql) >= 0 ? 0 : -1;
    ReleaseDBConn(conn);
    return ret;
}

int AppPushToken::DeleteAppPushToken(const std::string uid)
{
    std::stringstream streamsql;
    streamsql << "delete from  AppPushToken WHERE Account= '"
              << uid
              << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    int ret = conn->Execute(streamsql.str()) > 0 ? 0 : -1;
    ReleaseDBConn(conn);
    return ret;
}

int AppPushToken::GetAppPushTokenByUid(const std::string uid, AppPushTokenInfo& token_info)
{
    std::stringstream streamsql;
    streamsql << "/*master*/SELECT AppType, FcmPushToken, IOSPushToken,VoipToken,Version,Language,Oem,AppOem,IsDynamicsIV FROM AppPushToken WHERE Account= '" << uid << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
    query.Query(streamsql.str());
    int ret = 0;
    if (query.MoveToNextRow())
    {
        token_info.mobile_type = ATOI(query.GetRowData(0));
        Snprintf(token_info.fcm_token, sizeof(token_info.fcm_token),query.GetRowData(1));
        Snprintf(token_info.token, sizeof(token_info.token),query.GetRowData(2));
        Snprintf(token_info.voip_token, sizeof(token_info.voip_token),query.GetRowData(3));
        token_info.common_version = ATOI(query.GetRowData(4));
        Snprintf(token_info.language, sizeof(token_info.language),query.GetRowData(5));
        Snprintf(token_info.oem_name, sizeof(token_info.oem_name),query.GetRowData(6));
        token_info.app_oem = ATOI(query.GetRowData(7));
        token_info.is_dy_iv = ATOI(query.GetRowData(8));
        ret = 0;
    }
    else
    {
        ret = -1;
    }
    ReleaseDBConn(conn);
    return ret;
}

int AppPushToken::GetUidsAppTokenByNode(const std::string node, AppPushTokenList& token_list)
{
    std::stringstream streamsql;
    streamsql << "SELECT AppType, FcmPushToken, IOSPushToken,VoipToken,Account,Version,Login,Language,\
        AppOem, IsDynamicsIV FROM AppPushToken WHERE Node= '"
              << node
              << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
    query.Query(streamsql.str());
    while (query.MoveToNextRow())
    {
        AppPushTokenInfo token_info;
        memset(&token_info, 0, sizeof(token_info));
        token_info.mobile_type = ATOI(query.GetRowData(0));
        Snprintf(token_info.fcm_token, sizeof(token_info.fcm_token),query.GetRowData(1));
        Snprintf(token_info.token, sizeof(token_info.token),query.GetRowData(2));
        Snprintf(token_info.voip_token, sizeof(token_info.voip_token),query.GetRowData(3));
        token_info.common_version = ATOI(query.GetRowData(4));
        Snprintf(token_info.language, sizeof(token_info.language),query.GetRowData(5));
        Snprintf(token_info.oem_name, sizeof(token_info.oem_name),query.GetRowData(6));
        token_info.app_oem = ATOI(query.GetRowData(7));
        token_info.is_dy_iv = ATOI(query.GetRowData(8));
        token_list.push_back(token_info);
    }
    ReleaseDBConn(conn);
    return 0;
}


bool AppPushToken::CheckOnlinePushSwitch(const std::string uid)
{
    std::stringstream streamsql;
    streamsql << "SELECT C.OnlinePushSwitch FROM AppPushToken P join ApiClient C on C.ClientName=P.Oem WHERE P.Account= '" << uid << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
    query.Query(streamsql.str());
    int onlinepush_switch = 0;
    if (query.MoveToNextRow())
    {
        onlinepush_switch = ATOI(query.GetRowData(0));
    }

    ReleaseDBConn(conn);
    return (onlinepush_switch == 1 ? true : false);
}




}

