#ifndef __APP_PUSHTOKEN_DB_H__
#define __APP_PUSHTOKEN_DB_H__

#include <string>
#include <memory>
#include <vector>
#include <stdint.h>

typedef struct AppPushToken_T
{
    char node[64];
    int mobile_type;
    char fcm_token[512];
    char token[512];
    char voip_token[512];
    char app_token[512];
    int common_version;
    char app_version[64];
    char language[64];
    char oem_name[64];
    int app_oem;
    int is_dy_iv;

    AppPushToken_T() {
        memset(this, 0, sizeof(*this));
    }
}AppPushTokenInfo;

typedef std::vector<AppPushTokenInfo>AppPushTokenList;

namespace dbinterface
{

class AppPushToken
{
public:
    AppPushToken();
    ~AppPushToken();
    static int UpdateAppSmartType(const std::string &uid, int is_set);
    static int UpdateAppPushInfo(const std::string uid, const AppPushTokenInfo& token_info);
    static int DeleteAppPushToken(const std::string uid);
    static int GetAppPushTokenByUid(const std::string uid, AppPushTokenInfo& token_info);
    static int GetUidsAppTokenByNode(const std::string node, AppPushTokenList& token_list);    
    static bool CheckOnlinePushSwitch(const std::string uid);
private:
};

}
#endif

