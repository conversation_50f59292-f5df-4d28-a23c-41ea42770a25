#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "AwsRedirect.h"
#include <string.h>
#include "AkLogging.h"
#include "AkcsCommonDef.h"
#include "ConnectionManager.h"

namespace dbinterface
{


AwsRedirect::AwsRedirect()
{

}

int AwsRedirect::CheckUserRedirectByDisID(int dis_id)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        return 0;
    }
    CRldbQuery query(tmp_conn);
    std::stringstream sql;
    sql << "select Cloud from AwsRedirect where AccountID = " << dis_id;
    query.Query(sql.str());

    if (query.MoveToNextRow())
    {
        int cloud = ATOI(query.GetRowData(0));
        ReleaseDBConn(conn);
        return cloud;
    }
    
    ReleaseDBConn(conn);
    return 0;
}


//按项目id进行调度
int AwsRedirect::CheckUserRedirectByProjectID(int project_id)
{
    GET_DB_CONN_ERR_RETURN(conn, -1);

    CRldbQuery query(conn.get());
    std::stringstream sql;
    sql << "select Cloud from AwsRedirectProject where ProjectID = " << project_id;
    query.Query(sql.str());

    if (query.MoveToNextRow())
    {
        int cloud = ATOI(query.GetRowData(0));
        return cloud;
    }
    
    return 0;
}


bool AwsRedirect::CheckUserRedirect(const AwsInfo &account_info, int aws_redirect)
{
    if(aws_redirect == 0)
    {
        return false;
    }

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        return false;
    }
    CRldbQuery query(tmp_conn);

    std::stringstream sql;
    if(account_info.account_id) //msg通过传account_id进行调度，可以直接判断是否redirect
    {   
        sql << "SELECT ID FROM AwsRedirect WHERE AccountID = " << account_info.account_id;
    }
    else
    {
        if(!account_info.is_dev)    //msg通过传node进行调度，可以通过parentid判断是否redirect
        {        
            sql << "SELECT R.ID FROM AwsRedirect R JOIN PersonalAccount A ON R.AccountID = A.ParentID WHERE A.Account = '" << account_info.uid << "'";
        }
        else    ///msg通过传mac进行调度，可以通过parentid判断是否redirect
        {             
            sql << "SELECT R.ID FROM AwsRedirect R JOIN Devices D ON R.AccountID = D.MngAccountID WHERE D.MAC = '" << account_info.uid << "'"
                << " UNION SELECT R.ID FROM AwsRedirect R JOIN PersonalDevices D ON R.Account = D.Community WHERE D.MAC = '" << account_info.uid << "'";
        }
    }

    query.Query(sql.str());

    if (!query.MoveToNextRow())
    {      
        AK_LOG_INFO << "DaoCheckUserRedirect return No";
        ReleaseDBConn(conn);
        return false;
    }
    
    AK_LOG_INFO << "DaoCheckUserRedirect return Yes";
    ReleaseDBConn(conn);
    return true;
}

bool AwsRedirect::CheckUserRedirectByProject(const AwsInfo &aws_info)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        return false;
    }
    CRldbQuery query(tmp_conn);

    std::stringstream sql;
    if(!aws_info.is_dev)
    {
        if(aws_info.role == ACCOUNT_ROLE_COMMUNITY_MAIN || aws_info.role == ACCOUNT_ROLE_PERSONNAL_MAIN)
        {
            sql << "SELECT ID FROM AwsRedirect WHERE AccountID = " << aws_info.personal_parent_id;
        }
        else
        {
            sql << "SELECT R.ID FROM AwsRedirect R JOIN PersonalAccount P ON R.AccountID = P.ParentID WHERE P.ID =" << aws_info.personal_parent_id;
        }
    }
    else
    {
        if(!aws_info.is_community_dev)
        {
            sql << "SELECT R.ID FROM AwsRedirect R JOIN PersonalDevices D ON R.Account = D.Community WHERE D.MAC ='" << aws_info.mac << "'";
        }
        else
        {
            sql << "SELECT ID FROM AwsRedirect WHERE AccountID = " << aws_info.community_id;
        }
    }

    query.Query(sql.str());

    if (!query.MoveToNextRow())
    {       
        AK_LOG_INFO << "CheckUserRedirectByProject return No";
        ReleaseDBConn(conn);
        return false;
    }
    
    AK_LOG_INFO << "CheckUserRedirectByProject return Yes";
    ReleaseDBConn(conn);
    return true;
}


}



