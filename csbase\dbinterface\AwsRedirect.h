#ifndef __AWS_REDIRECT_H__
#define __AWS_REDIRECT_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>

typedef struct AwsInfo_T
{
    int  account_id;    //mng_id or installer_id
    int  is_dev;
    char uid[32];   //dev--mac or user--node 
    int personal_parent_id;
    int role;
    int is_community_dev;
    int community_id;
    char mac[32];
}AwsInfo;

enum RedirectCloudType
{
    REDIRECT_UNINIT = -1,//未初始化
    REDIRECT_NO_NEED = 0,//不需要重定向
    REDIRECT_JCLOUD = 1,
    REDIRECT_AUCLOUD = 2,   
    REDIRECT_ASBJ = 3,   
    REDIRECT_ECLOUD_TO_UCLOUD = 4,   
};      

//通过Key 返回给app,app通过key到web端请求详细的信息
#define REDIRECT_JCLOUD_WORD_KEY "textRedirectJcloudWordKey"
#define REDIRECT_AUCLOUD_WORD_KEY "textRedirectAucloudWordKey"
#define REDIRECT_ASBJ_WORD_KEY "textRedirectAsbjcloudWordKey"
#define REDIRECT_ECLOUD_TO_UCLOUD_WORD_KEY "textRedirectUcloudWordKey"
#define REDIRECT_SCLOUD_WORD_KEY "textRedirectScloudWordKey"
#define REDIRECT_CCLOUD_WORD_KEY "textRedirectCcloudWordKey"
#define REDIRECT_ECLOUD_WORD_KEY "textRedirectEcloudWordKey"
#define REDIRECT_RUCLOUD_WORD_KEY "textRedirectRucloudWordKey"


namespace dbinterface
{
class AwsRedirect
{

public:
    AwsRedirect();
    ~AwsRedirect();
    static int CheckUserRedirectByDisID(int dis_id);
    static bool CheckUserRedirect(const AwsInfo &aws_info, int aws_redirect);
    static int CheckUserRedirectByProjectID(int project_id);
    static bool CheckUserRedirectByProject(const AwsInfo &aws_info);
    
    private:

};

}
#endif
