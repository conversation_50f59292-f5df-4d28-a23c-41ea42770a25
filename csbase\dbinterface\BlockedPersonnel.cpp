#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "BlockedPersonnel.h"

namespace dbinterface {

static const std::string blocked_personnel_info_sec = " ID,DisplayID,IsBlocked,Reason,InitiatorType,Initiator,ExpireTime,OfficeDeliveryUUID,PersonalAccountUUID,OfficeTempKeyUUID,AntiPassbackAreaUUID,UUID ";

void BlockedPersonnel::GetBlockedPersonnelFromSql(BlockedPersonnelInfo& blocked_personnel_info, CRldbQuery& query)
{
    blocked_personnel_info.id = ATOI(query.GetRowData(0));
    Snprintf(blocked_personnel_info.display_id, sizeof(blocked_personnel_info.display_id), query.GetRowData(1));
    blocked_personnel_info.is_blocked =  ATOI(query.GetRowData(2));
    blocked_personnel_info.reason = AreaRestrictionBlockedReason(ATOI(query.GetRowData(3)));
    blocked_personnel_info.initiator_type = AntiPassbackInitiatorType(ATOI(query.GetRowData(4)));
    Snprintf(blocked_personnel_info.initiator, sizeof(blocked_personnel_info.initiator), query.GetRowData(5));
    Snprintf(blocked_personnel_info.expire_time, sizeof(blocked_personnel_info.expire_time), query.GetRowData(6));
    Snprintf(blocked_personnel_info.office_delivery_uuid, sizeof(blocked_personnel_info.office_delivery_uuid), query.GetRowData(7));
    Snprintf(blocked_personnel_info.personal_account_uuid, sizeof(blocked_personnel_info.personal_account_uuid), query.GetRowData(8));
    Snprintf(blocked_personnel_info.office_tempkey_uuid, sizeof(blocked_personnel_info.office_tempkey_uuid), query.GetRowData(9));
    Snprintf(blocked_personnel_info.anti_passback_area_uuid, sizeof(blocked_personnel_info.anti_passback_area_uuid), query.GetRowData(10));
    Snprintf(blocked_personnel_info.uuid, sizeof(blocked_personnel_info.uuid), query.GetRowData(11));
    return;
}

int BlockedPersonnel::GetBlockedPersonnelByPersonalAccountUUID(const std::string& personal_account_uuid, const std::string& area_uuid, BlockedPersonnelInfo& blocked_personnel_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << blocked_personnel_info_sec << " from BlockedPersonnel where PersonalAccountUUID = '" << personal_account_uuid << "' and AntiPassbackAreaUUID = '" << area_uuid << "' and IsBlocked = 1";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetBlockedPersonnelFromSql(blocked_personnel_info, query);
    }
    else
    {
        AK_LOG_WARN << "GetBlockedPersonnelByPersonalAccountUUID Failed, PersonalAccountUUID = " << personal_account_uuid << ", AreaUUID = " << area_uuid;
        return -1;
    }
    return 0;
}

int BlockedPersonnel::GetBlockedPersonnelByDeliveryUUID(const std::string& office_delivery_uuid, const std::string& area_uuid, BlockedPersonnelInfo& blocked_personnel_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << blocked_personnel_info_sec << " from BlockedPersonnel where OfficeDeliveryUUID = '" << office_delivery_uuid << "' and AntiPassbackAreaUUID = '" << area_uuid << "' and IsBlocked = 1";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetBlockedPersonnelFromSql(blocked_personnel_info, query);
    }
    else
    {
        AK_LOG_WARN << "GetBlockedPersonnelByDeliveryUUID failed, DeliveryUUID = " << office_delivery_uuid;
        return -1;
    }
    return 0;
}

int BlockedPersonnel::GetBlockedPersonnelByOfficeTempKeyUUID(const std::string& office_tempkey_uuid, const std::string& area_uuid, BlockedPersonnelInfo& blocked_personnel_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << blocked_personnel_info_sec << " from BlockedPersonnel where OfficeTempKeyUUID = '" << office_tempkey_uuid << "' and AntiPassbackAreaUUID = '" << area_uuid << "' and IsBlocked = 1";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetBlockedPersonnelFromSql(blocked_personnel_info, query);
    }
    else
    {
        AK_LOG_WARN << "GetBlockedPersonnelByOfficeTempKeyUUID failed, OfficeTempKeyUUID = " << office_tempkey_uuid;
        return -1;
    }
    return 0;
}

int BlockedPersonnel::InsertBlockedPersonnel(const BlockedPersonnelInfo& blocked_personnel)
{
    std::string expire_time = "DATE_ADD(now(), INTERVAL " + std::to_string(blocked_personnel.block_time * 60) + " SECOND)";

    std::map<std::string, std::string> str_map;
    str_map.emplace("DisplayID", blocked_personnel.display_id);
    str_map.emplace("Initiator", blocked_personnel.initiator);
    str_map.emplace("sql_ExpireTime", expire_time);
    str_map.emplace("OfficeDeliveryUUID", blocked_personnel.office_delivery_uuid);
    str_map.emplace("OfficeTempKeyUUID", blocked_personnel.office_tempkey_uuid);
    str_map.emplace("PersonalAccountUUID", blocked_personnel.personal_account_uuid);
    str_map.emplace("AntiPassbackAreaUUID", blocked_personnel.anti_passback_area_uuid);
    str_map.emplace("UUID", blocked_personnel.uuid);

    std::map<std::string, int> int_map;
    int_map.emplace("Reason", int(blocked_personnel.reason));
    int_map.emplace("IsBlocked", blocked_personnel.is_blocked);
    int_map.emplace("InitiatorType", int(blocked_personnel.initiator_type));

    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    return db_conn->InsertData("BlockedPersonnel", str_map, int_map);
}

int BlockedPersonnel::GetBlockedPersonnelList(BlockedPersonnelInfoList& blocked_personnel_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << blocked_personnel_info_sec << " from BlockedPersonnel where ExpireTime < now() and IsBlocked = 1";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        BlockedPersonnelInfo blocked_personnel_info;
        GetBlockedPersonnelFromSql(blocked_personnel_info, query);
        blocked_personnel_list.push_back(blocked_personnel_info);
    }
    
    return 0;
}

int BlockedPersonnel::ReleaseBlockedPersonnelByUUID(const std::string& uuid)
{
    std::stringstream stream_sql;
    stream_sql << "update BlockedPersonnel set IsBlocked = 0 where UUID = '" << uuid << "'";

    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    
    return db_conn->Execute(stream_sql.str()) > 0;
}

int BlockedPersonnel::ReleaseBlockedPersonnelByInitiator(const std::string& area_uuid, const std::string& initiator)
{
    std::stringstream stream_sql;
    stream_sql << "update BlockedPersonnel set IsBlocked = 0 where Initiator = '" << initiator << "' and AntiPassbackAreaUUID = '" << area_uuid << "'";

    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    
    return db_conn->Execute(stream_sql.str()) > 0;
}

}
