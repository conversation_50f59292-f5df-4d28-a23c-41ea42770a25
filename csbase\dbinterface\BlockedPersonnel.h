#ifndef __DB_BLOCKED_PERSONNEL_H__
#define __DB_BLOCKED_PERSONNEL_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "AkcsCommonDef.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct BlockedPersonnelInfo_T
{
    int id;
    int block_time;
    int is_blocked;
    
    AreaRestrictionBlockedReason reason;
    AntiPassbackInitiatorType initiator_type;
    char expire_time[20];
    char display_id[256];
    char initiator[64];
    char uuid[64];
    char office_delivery_uuid[64];
    char office_tempkey_uuid[64];
    char personal_account_uuid[64];
    char anti_passback_area_uuid[64];
    
    BlockedPersonnelInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} BlockedPersonnelInfo;

typedef std::vector<BlockedPersonnelInfo> BlockedPersonnelInfoList;

namespace dbinterface{

class BlockedPersonnel
{
public:
    static int GetBlockedPersonnelByDeliveryUUID(const std::string& office_delivery_uuid, const std::string& area_uuid, BlockedPersonnelInfo& blocked_personnel_info);
    static int GetBlockedPersonnelByOfficeTempKeyUUID(const std::string& office_tempkey_uuid, const std::string& area_uuid, BlockedPersonnelInfo& blocked_personnel_info);
    static int GetBlockedPersonnelByPersonalAccountUUID(const std::string& personal_account_uuid, const std::string& area_uuid, BlockedPersonnelInfo& blocked_personnel_info);
    
    static int InsertBlockedPersonnel(const BlockedPersonnelInfo& blocked_personnel);
    static int GetBlockedPersonnelList(BlockedPersonnelInfoList& blocked_personnel_list);
    static int ReleaseBlockedPersonnelByUUID(const std::string& uuid);
    static int ReleaseBlockedPersonnelByInitiator(const std::string& area_uuid, const std::string& initiator);

private:
    BlockedPersonnel() = delete;
    ~BlockedPersonnel() = delete;
    static void GetBlockedPersonnelFromSql(BlockedPersonnelInfo& blocked_personnel_info, CRldbQuery& query);
};

}


#endif
