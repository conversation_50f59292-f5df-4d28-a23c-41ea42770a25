#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/CommPerPrivateKey.h"
#include <string.h>
#include "AkLogging.h"
#include "ConnectionManager.h"
#include "util.h"

namespace dbinterface
{

CommPerPrivateKey::CommPerPrivateKey()
{

}

void CommPerPrivateKey::GetAccountPrivatekeyList(const std::string& users, UsersPinInfoMap& pm_create_key_list, UsersPinInfoMap& user_create_key_list)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    std::vector<std::string> vec;
    std::vector<std::string> s_vec;
    std::stringstream str_sql;
    CRldbQuery query(tmp_conn);
    str_sql << "select Code,Special,Account From CommPerPrivateKey where Account in(" << users << ");";
    query.Query(str_sql.str());
    while (query.MoveToNextRow())
    {
        std::string code = query.GetRowData(0);
        std::string uid = query.GetRowData(2);
        int special = ATOI(query.GetRowData(1));

        if (special)
        {
            UsersPinInfoMapIter iter = user_create_key_list.find(uid);
            if (iter != user_create_key_list.end())
            {
                iter->second.push_back(code);
            }
            else
            {
                std::vector<std::string> vec;
                vec.push_back(code);
                user_create_key_list.insert(std::make_pair(uid, vec));            
            }
            continue;
        }
        
        UsersPinInfoMapIter iter = pm_create_key_list.find(uid);
        if (iter != pm_create_key_list.end())
        {
            iter->second.push_back(code);
        }
        else
        {
            std::vector<std::string> vec;
            vec.push_back(code);
            pm_create_key_list.insert(std::make_pair(uid, vec));            
        }
    }
    ReleaseDBConn(conn);
    return;
}

void CommPerPrivateKey::GetOrderedAccountPrivatekeyList(const std::string& users, UserPinInfoList& pin_list)
{
    GET_DB_CONN_ERR_RETURN_VOID(db_conn)

    CRldbQuery query(db_conn.get());
    std::stringstream str_sql;
    str_sql << "select Code,Special,Account From CommPerPrivateKey where Account in(" << users << ") order by ID;";
    query.Query(str_sql.str());
    while (query.MoveToNextRow())
    {
        UserPinInfo pin_info;
        Snprintf(pin_info.pin, sizeof(pin_info.pin), query.GetRowData(0));
        pin_info.is_create_by_pm = (ATOI(query.GetRowData(1)) == 0);
        Snprintf(pin_info.account, sizeof(pin_info.account), query.GetRowData(0));
        pin_list.push_back(pin_info);
    }

    return;
}

}

