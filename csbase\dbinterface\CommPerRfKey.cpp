#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/CommPerRfKey.h"
#include <string.h>
#include "AkLogging.h"
#include "ConnectionManager.h"
#include "util.h"

namespace dbinterface
{

CommPerRfKey::CommPerRfKey()
{

}

void CommPerRfKey::GetAccountRfkeyList(const std::string& accounts, UsersRFInfoMap &map)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();

    std::stringstream str_sql;
    str_sql << "select Code,Account From  CommPerRfKey where Account in(" << accounts << ");";

    CRldbQuery query(tmp_conn);
    query.Query(str_sql.str());
    while (query.MoveToNextRow())
    {
        std::string code = query.GetRowData(0);
        std::string uid = query.GetRowData(1);
        std::map<std::string, std::vector<std::string>>::iterator iter = map.find(uid);
        if (iter != map.end())
        {
            iter->second.push_back(code);
        }
        else
        {
            std::vector<std::string> list;
            list.push_back(code);
            map.insert(std::make_pair(uid, list));            
        }
    }

    ReleaseDBConn(conn);
    return;
}

void CommPerRfKey::GetOrderedAccountRfkeyList(const std::string& accounts, UserRFInfoList &list)
{
    GET_DB_CONN_ERR_RETURN_VOID(conn);

    std::stringstream str_sql;
    str_sql << "select Code,Account,IsCreateByPm From  CommPerRfKey where Account in(" << accounts << ") order by ID;";
    CRldbQuery query(conn.get());
    query.Query(str_sql.str());
    while (query.MoveToNextRow())
    {
        UserRFInfo rf_info;
        Snprintf(rf_info.rf_card, sizeof(rf_info.rf_card), query.GetRowData(0));
        rf_info.is_create_by_pm = (ATOI(query.GetRowData(1)) == 1);
        Snprintf(rf_info.account, sizeof(rf_info.account), query.GetRowData(2));
        list.push_back(rf_info);
    }

    return;
}

}

