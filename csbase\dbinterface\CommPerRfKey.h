#ifndef __DB_COMM_RFKEY_H__
#define __DB_COMM_RFKEY_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>

typedef struct USER_RF_INFO_T
{
    char rf_card[20];
    char account[32];
    bool is_create_by_pm;

    USER_RF_INFO_T()
    {
        memset(this, 0, sizeof(*this));
    }

} UserRFInfo;

typedef std::list<UserRFInfo> UserRFInfoList;
typedef std::map<std::string/*uuid*/, std::vector<std::string>> UsersRFInfoMap;
typedef UsersRFInfoMap::iterator UsersRFInfoMapIter;


namespace dbinterface
{

class CommPerRfKey
{
public:
    CommPerRfKey();
    ~CommPerRfKey();
    static void GetAccountRfkeyList(const std::string& accounts, UsersRFInfoMap &map);
    static void GetOrderedAccountRfkeyList(const std::string& accounts, UserRFInfoList &list);
private:
};

}
#endif
