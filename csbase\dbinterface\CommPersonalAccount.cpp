#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/CommPersonalAccount.h"
#include <string.h>
#include "AkLogging.h"
#include "util.h"
#include "AkcsCommonDef.h"
#include "ConnectionManager.h"

namespace dbinterface
{

CommPersonalAccount::CommPersonalAccount()
{

}

//把account,account,字符串列表转为map
void CommPersonalAccount::UsersToMap(const std::string& users, UserNodeInfoMap &user_node)
{
    std::string user_str = users;
    StringReplace(user_str, "'", "");  //去掉单引号
	
    std::string delim = ",";
    size_t last = 0;
    size_t index = user_str.find(delim, last);
    while (index != std::string::npos)
    {
        std::string item = user_str.substr(last, index - last);
        user_node.insert(std::make_pair(item, item));
        last = index + delim.size();
        index = user_str.find(delim, last);
    }
    if (index - last > 0)
    {
        std::string item = user_str.substr(last, index - last);
        user_node.insert(std::make_pair(item, item));
    }
}

void CommPersonalAccount::NodesToStrings(UserNodeInfoMap &user_node, std::string& nodes_str)
{
    if (user_node.size() == 0)
    {
        return;
    }
    for (const auto& user : user_node)
    {
        nodes_str +=  "'" + user.second + "'" + ",";
    }
    nodes_str.pop_back(); // 删除最后一个逗号
}


void CommPersonalAccount::GetAccountRfkeyList(const std::string& accounts, UsersRFInfoMap &map)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();

    std::stringstream str_sql;
    str_sql << "select NFCCode,BLECode,Account From  PersonalAccount where Account in(" << accounts << ");";

    CRldbQuery query(tmp_conn);
    query.Query(str_sql.str());
    while (query.MoveToNextRow())
    {
        std::string nfc = query.GetRowData(0);
        std::string ble = query.GetRowData(1);
        std::string uid = query.GetRowData(2);
        
        UsersRFInfoMapIter iter = map.find(uid);
        if (iter != map.end())
        {
            if (nfc.size() > 4)
            {
                iter->second.push_back(nfc);
            }
            if (ble.size() > 4)
            {
                iter->second.push_back(ble);
            }
        }
        else
        {
            std::vector<std::string> list;
            if (nfc.size() > 4)
            {
                list.push_back(nfc);
            }
            if (ble.size() > 4)
            {
                list.push_back(ble);
            }
            map.insert(std::make_pair(uid, list));            
        }
    }

    ReleaseDBConn(conn);
    return;
}


void CommPersonalAccount::UsersToNodesMap(const std::string& users, UserNodeInfoMap &map)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }
    std::stringstream str_sql;
    //把users直接转为user-node
    UsersToMap(users, map);

    //只要查找从账号的node进行替换。TODO:这里没有用到索引
    str_sql << "select P.Account,PP.Account from PersonalAccount P left join PersonalAccount PP on PP.ParentID=P.ID "
    << " where PP.Account in (" << users << ") and PP.Role in (" 
    << ACCOUNT_ROLE_COMMUNITY_ATTENDANT << "," << ACCOUNT_ROLE_PERSONNAL_ATTENDANT <<")";
    CRldbQuery query(tmp_conn);
    query.Query(str_sql.str());
    while (query.MoveToNextRow())
    {
        std::string node = query.GetRowData(0);
        std::string uid = query.GetRowData(1);
        UserNodeInfoMapIter iter = map.find(uid);
        if (iter != map.end())
        {
            //更新user是从账号对应的node
            iter->second = node;
        }
    }    

    ReleaseDBConn(conn);
}

//获取用户的node
int CommPersonalAccount::GetUidNode(const std::string& uid, std::string& real_node)
{
    int ret = -1;
    std::stringstream str_sql;

    str_sql << "select Account, Role,ParentID  from PersonalAccount "
    << " where Account= '" << uid << "';";
    
    GET_DB_CONN_ERR_RETURN(tmp_conn, -1); //获取数据库连接失败时，返回-1
    CRldbQuery query(tmp_conn.get());
    query.Query(str_sql.str());
    if (query.MoveToNextRow())
    {
        ret = 0;
        real_node = query.GetRowData(0);
        int role = ATOI(query.GetRowData(1));
        int parentid = ATOI(query.GetRowData(2));
        if (role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT || role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT)
        {
            str_sql.str("");
            str_sql << "select Account from PersonalAccount "
            << " where ID= '" << parentid << "';";
            query.Query(str_sql.str());
            if (query.MoveToNextRow())
            {
                real_node = query.GetRowData(0);
                ret = 0;
            }
            else
            {
                ret = -1;
            }
        }
        
    }    
    return ret;
}

//根据房间获取主账号
int CommPersonalAccount::GetNodeByRoomID(uint32_t room_id, std::string& account)
{
    int ret = -1;
    //room_id为0，直接返回
    if(room_id == 0)
    {
        return ret;
    }
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return ret;
    }
    std::stringstream str_sql;

    str_sql << "select Account from PersonalAccount"
               << " where RoomID = " << room_id 
               << " and Role =  " << ACCOUNT_ROLE_COMMUNITY_MAIN << " limit 1"; //主账号才有RoomID
       
    CRldbQuery query(tmp_conn);
    query.Query(str_sql.str());
    if(query.MoveToNextRow())
    {
        ret = 0;
        account = query.GetRowData(0);
    }
    ReleaseDBConn(conn);
    return ret;
}

}


