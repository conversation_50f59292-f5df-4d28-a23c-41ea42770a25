#ifndef __COMM_PERSONAL_ACCOUNT_H__
#define __COMM_PERSONAL_ACCOUNT_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include "RldbQuery.h"
#include "Rldb.h"
#include <assert.h>
#include "CommPerRfKey.h"


typedef std::map<std::string/*user*/, std::string/*node*/> UserNodeInfoMap;
typedef UserNodeInfoMap::iterator UserNodeInfoMapIter;

namespace dbinterface
{

class CommPersonalAccount
{
public:
    CommPersonalAccount();
    ~CommPersonalAccount();
    
    static void GetAccountRfkeyList(const std::string& accounts, UsersRFInfoMap &map);
    static void NodesToStrings(UserNodeInfoMap &users, std::string& nodes_str);
    static void UsersToNodesMap(const std::string& users, UserNodeInfoMap &map);
    static int GetUidNode(const std::string& uid, std::string& real_node);
    static int GetNodeByRoomID(uint32_t room_id, std::string& account);
private:
    static void UsersToMap(const std::string& users, UserNodeInfoMap &user_node);
};

}
#endif
