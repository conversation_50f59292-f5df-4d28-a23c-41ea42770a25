#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "CommunityCallRule.h"

namespace dbinterface {

static const std::string community_call_rule_info_sec = " <PERSON>.<PERSON>,R.PersonalAccountUUID,R.<PERSON>allType,R.UUID ";


void CommunityCallRule::GetCommunityCallRuleFromSql(CommunityCallRuleInfo& community_call_rule_info, CRldbQuery& query)
{
    community_call_rule_info.id = ATOI(query.GetRowData(0));
    Snprintf(community_call_rule_info.personal_account_uuid, sizeof(community_call_rule_info.personal_account_uuid), query.GetRowData(1));
    community_call_rule_info.apt_call_type = ATOI(query.GetRowData(2));
    Snprintf(community_call_rule_info.uuid, sizeof(community_call_rule_info.uuid), query.GetRowData(3));
    return;
}

int CommunityCallRule::GetCommunityCallRuleByUUID(const std::string& uuid, CommunityCallRuleInfo& community_call_rule_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << community_call_rule_info_sec << " from CommunityCallRule R where UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetCommunityCallRuleFromSql(community_call_rule_info, query);
    }
    else
    {
        AK_LOG_WARN << "get CommunityCallRuleInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}

int CommunityCallRule::GetCommunityCallRuleByPersonalAccountUUID(const std::string& personal_account_uuid, CommunityCallRuleInfo& community_call_rule_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << community_call_rule_info_sec << " from CommunityCallRule R where PersonalAccountUUID = '" << personal_account_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetCommunityCallRuleFromSql(community_call_rule_info, query);
    }
    else
    {
        return -1;
    }
    return 0;
}


int CommunityCallRule::GetCommunityCallRuleByCommunityUUID(const std::string& community_uuid, CommunityCallRuleInfoMap& community_call_rule_info_map)
{
    std::stringstream stream_sql;
    stream_sql << "select " << community_call_rule_info_sec << " from CommunityCallRule R join PersonalAccount P on R.PersonalAccountUUID = P.UUID where P.ParentUUID = '" << community_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        CommunityCallRuleInfo community_call_rule_info;
        GetCommunityCallRuleFromSql(community_call_rule_info, query);
        community_call_rule_info_map.insert(std::make_pair(community_call_rule_info.personal_account_uuid, community_call_rule_info));
    }
    return 0;
}

static const std::string community_sequence_call_list_info_sec = " ID,CallRuleUUID,Sequence,CalleeUUID,CalleeType,UUID ";

void CommunitySequenceCallList::GetCommunitySequenceCallListFromSql(CommunitySequenceCallListInfo& community_sequence_call_list_info, CRldbQuery& query)
{
    community_sequence_call_list_info.id = ATOI(query.GetRowData(0));
    Snprintf(community_sequence_call_list_info.call_rule_uuid, sizeof(community_sequence_call_list_info.call_rule_uuid), query.GetRowData(1));
    community_sequence_call_list_info.sequence = ATOI(query.GetRowData(2));
    Snprintf(community_sequence_call_list_info.callee_uuid, sizeof(community_sequence_call_list_info.callee_uuid), query.GetRowData(3));
    community_sequence_call_list_info.callee_type = ATOI(query.GetRowData(4));
    Snprintf(community_sequence_call_list_info.uuid, sizeof(community_sequence_call_list_info.uuid), query.GetRowData(5));
    return;
}

int CommunitySequenceCallList::GetCommunitySequenceCallMapByCallRuleUUID(const std::string& call_rule_uuid, CommunitySeqCallMap& seq_call_map)
{
    std::stringstream stream_sql;
    stream_sql << "select " << community_sequence_call_list_info_sec << " from CommunitySequenceCallList where CallRuleUUID = '" << call_rule_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CommunitySequenceCallListInfo community_sequence_call_list_info;

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        GetCommunitySequenceCallListFromSql(community_sequence_call_list_info, query);
        seq_call_map[community_sequence_call_list_info.sequence].push_back(community_sequence_call_list_info);
    }

    return 0;
}


}
