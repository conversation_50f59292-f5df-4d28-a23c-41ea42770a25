#ifndef __DB_COMMUNITY_CALL_RULE_H__
#define __DB_COMMUNITY_CALL_RULE_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct CommunityCallRuleInfo_T
{
    int id;
    char personal_account_uuid[36];
    int apt_call_type;
    char uuid[36];
    CommunityCallRuleInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} CommunityCallRuleInfo;

typedef struct CommunitySequenceCallListInfo_T
{
    int id;
    char call_rule_uuid[36];
    int sequence;
    char callee_uuid[36];
    int callee_type;
    char uuid[36];
    CommunitySequenceCallListInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} CommunitySequenceCallListInfo;

typedef std::list<CommunitySequenceCallListInfo> CommunitySeqCallRoundList; //每一轮的呼叫序列
typedef std::map<int, CommunitySeqCallRoundList> CommunitySeqCallMap; //轮数-呼叫序列映射

using CommunityCallRuleInfoMap = std::map<std::string/*PersonalAccount.UUID*/, CommunityCallRuleInfo>;

namespace dbinterface {

class CommunityCallRule
{
public:
    static int GetCommunityCallRuleByUUID(const std::string& uuid, CommunityCallRuleInfo& community_call_rule_info);
    static int GetCommunityCallRuleByPersonalAccountUUID(const std::string& personal_account_uuid, CommunityCallRuleInfo& community_call_rule_info);
    static int GetCommunityCallRuleByCommunityUUID(const std::string& community_uuid, CommunityCallRuleInfoMap& community_call_rule_info_map);
private:
    CommunityCallRule() = delete;
    ~CommunityCallRule() = delete;
    static void GetCommunityCallRuleFromSql(CommunityCallRuleInfo& community_call_rule_info, CRldbQuery& query);
};

class CommunitySequenceCallList
{
public:
    static int GetCommunitySequenceCallMapByCallRuleUUID(const std::string& call_rule_uuid, CommunitySeqCallMap& seq_call_map);

private:
    CommunitySequenceCallList() = delete;
    ~CommunitySequenceCallList() = delete;
    static void GetCommunitySequenceCallListFromSql(CommunitySequenceCallListInfo& community_sequence_call_list_info, CRldbQuery& query);
};


}
#endif
