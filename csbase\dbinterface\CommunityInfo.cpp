#include <sstream>
#include "RldbQuery.h"
#include "Rldb.h"
#include "CommunityInfo.h"
#include <string.h>
#include "BasicDefine.h"
#include "InterfaceComm.h"
#include "AkLogging.h"
#include "AkcsCommonDef.h"
#include "util.h"
#include "util_judge.h"
#include "dbinterface/DataConfusion.h"
#include "ConnectionManager.h"

// encrypt field : MobileNumber

CommunityInfo::CommunityInfo(unsigned int community_id)
{
    communit_id_ = community_id;
    enable_motion_ = 0;
    motion_time_ = 0;
    apt_pin_type_ = 0;
    face_enrollment_ = 0;
    id_card_verification_ = 0;
    switch_flag_ = 0;
    is_new_ = 0;
    is_expire_ = 0;
    init_success_ = 0;
    is_support_scan_indoor_qrcode_to_reg_enduser_ = false;
    memset(mobile_number_, 0, sizeof(mobile_number_));
    memset(phone_code_, 0, sizeof(phone_code_));
    init();
}

CommunityInfo::CommunityInfo(const std::string &uid)
{
    enable_motion_ = 0;
    motion_time_ = 0;
    apt_pin_type_ = 0;
    face_enrollment_ = 0;
    id_card_verification_ = 0;
    switch_flag_ = 0;
    init_success_ = 0;
    memset(mobile_number_, 0, sizeof(mobile_number_));
    memset(phone_code_, 0, sizeof(phone_code_));
    is_new_ = 0;
    is_expire_ = 0;
    communit_id_ = 0;
    is_support_scan_indoor_qrcode_to_reg_enduser_ = false;
    GetCommunityID(uid);
    init();
}

int CommunityInfo::GetCommunityID(const std::string& uid)
{
    std::stringstream streamSQL;
    streamSQL << "select Role, ParentID from PersonalAccount where Account = '" << uid << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* pTmpConn = conn.get();
    if (NULL == pTmpConn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(pTmpConn);
    query.Query(streamSQL.str());

    int role;
    int parent_id;
    char sql[256];
    if (query.MoveToNextRow())
    {
        role = dbinterface::ATOI(query.GetRowData(0));
        parent_id = dbinterface::ATOI(query.GetRowData(1));
        if (akjudge::IsNodeAccountRole(role)) 
        {
            communit_id_ = parent_id;
        }
        else if(ACCOUNT_ROLE_COMMUNITY_ATTENDANT == role)
        {
            snprintf(sql, sizeof(sql), "SELECT ParentID FROM PersonalAccount  WHERE ID = %d", parent_id);
            query.Query(sql);
            if (query.MoveToNextRow())
            {
                communit_id_ = dbinterface::ATOI(query.GetRowData(0));
            }
        }
    }
    ReleaseDBConn(conn);
    return 0;
}


CommunityInfo::~CommunityInfo()
{

}

void CommunityInfo::init()
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* pTmpConn = conn.get();
    if (NULL == pTmpConn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }

    std::stringstream strSQL;
    strSQL << "SELECT  C.EnableMotion, C.MotionTime, C.AptPinType,A.location, C.Street, C.FaceEnrollment, "
           <<" C.IDCardVerification, C.Switch , C.IsNew , C.FeatureExpireTime < now(), C.MobileNumber, C.PhoneCode "
           << ",ISNULL(FeatureExpireTime), A.TimeZone, C.Country, C.States, C.City, C.NameDisplay, C.PostalCode, A.CustomizeForm, A.UUID,"
           << " C.EnableScanToRegister, C.CommunityPlan, C.IsAllEmergencyDoor "
           << ",C.EnablePackageDetection "
           << " FROM Account A inner join CommunityInfo C on C.AccountID=A.ID where A.ID= "
           << communit_id_;

    CRldbQuery query(pTmpConn);
    query.Query(strSQL.str());
    if (query.MoveToNextRow())
    {
        enable_motion_ = dbinterface::ATOI(query.GetRowData(0));
        motion_time_ = dbinterface::ATOI(query.GetRowData(1));
        apt_pin_type_ = dbinterface::ATOI(query.GetRowData(2));
        name_ = query.GetRowData(3);
        street_ = query.GetRowData(4);
        face_enrollment_ = dbinterface::ATOI(query.GetRowData(5));
        id_card_verification_ = dbinterface::ATOI(query.GetRowData(6));
        switch_flag_ = dbinterface::ATOI(query.GetRowData(7));
        is_new_ = dbinterface::ATOI(query.GetRowData(8));
        is_expire_ = dbinterface::ATOI(query.GetRowData(9));
        Snprintf(mobile_number_, sizeof(mobile_number_), dbinterface::DataConfusion::Decrypt(query.GetRowData(10)).c_str());
        Snprintf(phone_code_, sizeof(phone_code_),  query.GetRowData(11));
        int isnull =  dbinterface::ATOI(query.GetRowData(12));
        if (isnull)
        {
            is_expire_ = 1;
        }
        timezone_ = query.GetRowData(13);
        country_ = query.GetRowData(14);
        states_ = query.GetRowData(15);
        city_ = query.GetRowData(16);
        contact_display_order_ = ATOI(query.GetRowData(17));
        postal_code_ = query.GetRowData(18);
        time_formate_ = ATOI(query.GetRowData(19));
        uuid_ = query.GetRowData(20);
        is_support_scan_indoor_qrcode_to_reg_enduser_ = (ATOI(query.GetRowData(21)) == 1 ? true : false);
        community_creator_type_ = ATOI(query.GetRowData(22));
        is_all_emergency_door_ = ATOI(query.GetRowData(23)) == 1 ? true : false;

        enable_package_detection_ = ATOI(query.GetRowData(24));
        // enable_sound_detection_ = ATOI(query.GetRowData(25));
        // sound_type_ = ATOI(query.GetRowData(26));
        init_success_ = 1;
    }
         
    ReleaseDBConn(conn);
    return;
}


int CommunityInfo::initFeaturePlan()
{
    feature_is_init_ = FeaturePlanStatus::Init;
    //高级功能
    feature_item_ = 0;
    feature_id_ = 0;
    std::stringstream stream_sql;
    stream_sql << "select FeatureID from ManageFeature where AccountID ="<< communit_id_;

    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        feature_id_ = dbinterface::ATOI(query.GetRowData(0));     
    }
    //feature_id_ == 0 升级高级功能前的数据
    if (feature_id_ != 0 )
    {
        //FeatureID>0，查找具体功能是否在高级功能里
        std::stringstream sql2;
        sql2 << "select Item from FeaturePlan where ID = " << feature_id_;

        query.Query(sql2.str());
        if (query.MoveToNextRow())
        {
            feature_item_ = dbinterface::ATOI(query.GetRowData(0));     
        } 
    }    
    return 0;
}

int CommunityInfo::isEnableMotion()
{
    return enable_motion_;
}

int CommunityInfo::MotionTime()
{
    return motion_time_;
}

int CommunityInfo::AptPinType()
{
    return apt_pin_type_;
}

std::string & CommunityInfo::Name()
{
    return name_;
}

std::string & CommunityInfo::Street()
{
    return street_;
}

int CommunityInfo::FaceEnrollment()
{
    return face_enrollment_;
}

int CommunityInfo::IDCard()
{
    return id_card_verification_;
}

int CommunityInfo::IsAllowCreatePin() const
{
    return dbinterface::SwitchHandle(switch_flag_, SwitchType::AllowPin);
}

bool CommunityInfo::IsExpire()
{
    return (is_expire_ == 1) ? true : false;
}

int CommunityInfo::LimitFlowRemind()
{
    int flag = dbinterface::SwitchHandle(switch_flag_, SwitchType::SIMNotify);
    return flag;
}

std::string CommunityInfo::MobileNumber()
{
    return mobile_number_;
}

std::string CommunityInfo::PhoneCode()
{
    return phone_code_;
}

int CommunityInfo::GetIsNew()
{
    return is_new_;
}

uint CommunityInfo::GetCommunitID()
{
    return communit_id_;
}

int CommunityInfo::InitSuccess()
{
    return init_success_;
}

std::string CommunityInfo::GetCommunityUnitName(int unit_id)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* temp_conn = conn.get();
    if (NULL == temp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return "";
    }

    std::stringstream sql;
    sql << "SELECT UnitName FROM CommunityUnit WHERE ID = '" << unit_id << "'";

    CRldbQuery query(temp_conn);
    query.Query(sql.str());

    std::string unit_name;
    if (query.MoveToNextRow())
    {
        unit_name = query.GetRowData(0);
    }

    ReleaseDBConn(conn);
    return unit_name;
}

std::tuple<std::string, std::string> CommunityInfo::GetPMAccountLanguageLocation()
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* temp_conn = conn.get();
    if (NULL == temp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return std::make_tuple("", "");
    }

    std::stringstream sql;
    sql << "SELECT a.Language, a.Location, a.Account "
           "  FROM Account a "
           " LEFT JOIN PropertyMngList b ON a.ID = b.CommunityID "
           "  WHERE b.CommunityID =  " << communit_id_ <<
           " LIMIT 1";

    CRldbQuery query(temp_conn);
    query.Query(sql.str());

    std::string language;
    std::string location;
    if (query.MoveToNextRow())
    {
        language = query.GetRowData(0);
        location = query.GetRowData(1);
    }

    ReleaseDBConn(conn);
    return std::make_tuple(language, location);
}

int CommunityInfo::EnableSmartHome()
{
    int flag = dbinterface::SwitchHandle(switch_flag_, SwitchType::ENABLE_SMART_HOME);
    return flag;
}

int CommunityInfo::LimitFlowDataType()
{
    int flag = LimitFlowRemind();
    if (flag > 0)
    {
        return AutopDataType::LIMIT_FLOW;
    }
    else
    {
        return AutopDataType::NO_LIMIT_FLOW;
    }
}

//add bu xuzr,检查高级功能Featureplan中Item字段
//第一位(个位):快递件，第二位：pin是否可用，第三位：QrCode是否可用，第四位：限制家庭成员。第五位： 每个家庭是否必须有一台室内机；第六位人脸识别控制，第七位第三方设备';
bool CommunityInfo::CheckFeature(int index)
{
    if (feature_is_init_ == FeaturePlanStatus::UnInit)
    {
        initFeaturePlan();
    }

    //FeatureID=0代表旧小区，Tempkey/Pin按照之前的逻辑根据开关配置，快递和家庭成员控制关闭
    if (feature_id_ == 0)
    {
        if(index == TAB_DEVICES_CHECK_INDEX_PIN || index == TAB_DEVICES_CHECK_INDEX_TMPKEY)
        {
            return true;
        }
        else
        {
            return false;
        }
    }
    else
    {
        if (dbinterface::SwitchHandle(feature_item_, index))
        {
           return true;
        }
        return false;
        
    }
    return true;
}

int CommunityInfo::EnableAutoEmergency()
{
    int flag = dbinterface::SwitchHandle(switch_flag_, SwitchType::ENABLE_AUTO_EMERGENCY);
    return flag;
}

int CommunityInfo::EnablePrivateAccess() const
{
    int flag = dbinterface::SwitchHandle(switch_flag_, SwitchType::ENABLE_PRIVATE_ACCESS);
    return flag;
}

bool CommunityInfo::EmergencyNeedNotify()
{
    int flag = dbinterface::SwitchHandle(switch_flag_, SwitchType::ENABLE_EMERGENCY_NOTIFY);
    return flag == 1;
}

int CommunityInfo::EnableLandline()
{
    return dbinterface::SwitchHandle(switch_flag_, SwitchType::Landline);
}

std::string CommunityInfo::TimeZone()
{
    return timezone_;
}

int CommunityInfo::TimeFormate()
{
    return time_formate_;
}


std::string CommunityInfo::Country()
{
    return country_;
}

std::string CommunityInfo::States()
{
    return states_;
}

std::string CommunityInfo::City()
{
    return city_;
}

std::string CommunityInfo::PostalCode()
{
    return postal_code_;
}

int CommunityInfo::ContactDisplayOrder()
{
    return contact_display_order_;
}

bool CommunityInfo::IsSupportScanIndoorQRCodeToReg()
{
    return is_support_scan_indoor_qrcode_to_reg_enduser_;
}

int CommunityInfo::GetProjectPlanType()
{
    return community_creator_type_;
}

bool CommunityInfo::IsAllEmergencyDoor()
{
    return is_all_emergency_door_;
}


int CommunityInfo::EnablePackageDetection()
{
    return enable_package_detection_;
}

// int CommunityInfo::EnableSoundDetection()
// {
//     return enable_sound_detection_;
// }

// int CommunityInfo::SoundType()
// {
//     return sound_type_;
// }