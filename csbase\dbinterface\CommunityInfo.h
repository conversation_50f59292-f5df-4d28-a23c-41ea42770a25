#ifndef __COMMUNITY_INFO_H__
#define __COMMUNITY_INFO_H__
#include <string>
#include <memory>
#include <tuple>
#include "AkcsCommonDef.h"

/**
 * Autop 配置
 * Config.Network.DATAUSAGE.DataType
 *
 * <AUTHOR> (2021/5/14)
 */
enum AutopDataType
{
    LIMIT_FLOW = 0, //表示有限流量
    NO_LIMIT_FLOW = 1 //表示不限流量
};

enum FlowOutLimitRemind
{
    NO_REMIND = 0, //表示不需要提醒
    NEED_REMIND = 1 //表示需要提醒
};

enum IsNewFlag
{
    OLD_COMMUNITY = 0, //旧社区
    NEW_COMMUNITY = 1 //新社区
};

class CommunityInfo
{
public:
    CommunityInfo(unsigned int community_id);
    CommunityInfo(const std::string &uid);
    ~CommunityInfo();
    
    enum SwitchType
    {
        Landline = 0,
        OfflineNotify = 1,
        AllowPin = 2,
        SIMNotify = 3,
        ENABLE_SMART_HOME = 4,
        ENABLE_AUTO_EMERGENCY = 5,
        ENABLE_PRIVATE_ACCESS = 6,
        ENABLE_EMERGENCY_NOTIFY = 7,
    };

    enum FeaturePlan
    {
        TAB_DEVICES_CHECK_INDEX_DELIVERY = 0,
        TAB_DEVICES_CHECK_INDEX_PIN,
        TAB_DEVICES_CHECK_INDEX_TMPKEY,
        TAB_DEVICES_CHECK_INDEX_FAMILYMEMBER,
        TAB_DEVICES_CHECK_INDEX_INDOOR_PLAN,
        TAB_DEVICES_CHECK_INDEX_FACE_RECOGNITION,
        TAB_DEVICES_CHECK_INDEX_THIRD_CAMERA,
        TAB_DEVICES_CHECK_INDEX_ID_ACCESS,
    };

    //APT+PIN类型
    enum AptPinTypeEnum
    {
        PIN = 0,
        APT_PIN = 1,
    };

    enum class FeaturePlanStatus    
    {
        UnInit = 0,   
        Init = 1
    };


    std::string GetCommunityUnitName(int unit_id);
    std::tuple<std::string,std::string> GetPMAccountLanguageLocation();
    int isEnableMotion();
    int MotionTime();
    int AptPinType();
    std::string &Name();
    std::string &Street();
    int FaceEnrollment();
    int IDCard();
    int GetIsNew();
    int IsAllowCreatePin() const;
    int LimitFlowRemind();   
    int LimitFlowDataType();
    bool IsExpire();
    bool CheckFeature(int index);
    std::string MobileNumber();
    std::string PhoneCode();
    uint GetCommunitID();
    int EnableSmartHome();
    int EnableAutoEmergency();
    int EnablePrivateAccess() const;
    int EnableLandline();
    std::string City();    
    std::string States();
    std::string Country();
    std::string TimeZone();
    int TimeFormate();
    std::string PostalCode();
    int ContactDisplayOrder();
    int InitSuccess();
    bool EmergencyNeedNotify();
    int EnablePackageDetection();
    // int EnableSoundDetection();
    // int SoundType();
    std::string UUID(){return uuid_;};
    bool IsSupportScanIndoorQRCodeToReg();
    bool IsAllEmergencyDoor();
    int GetProjectPlanType();

private:
    void init();
    int initFeaturePlan();
    int GetCommunityID(const std::string& uid);
    unsigned int communit_id_;
    int enable_motion_;
    int motion_time_;
    int apt_pin_type_;
    std::string name_;
    std::string street_;
    int face_enrollment_;
    int id_card_verification_;
    int switch_flag_;
    int is_new_;
    int is_expire_;

    FeaturePlanStatus feature_is_init_ = FeaturePlanStatus::UnInit;
    int feature_item_;
    int feature_id_;
    char mobile_number_[25];
    char phone_code_[9];
    std::string timezone_;
    int time_formate_;
    std::string country_; // 国家
    std::string states_;  // 省份
    std::string city_;    // 城市
    std::string postal_code_; //邮政编号

    int contact_display_order_; // 联系人姓名展示顺序，0代表FirstName+LastName, 1代表LastName+FirstName 
    int init_success_;

    std::string uuid_;

    bool is_support_scan_indoor_qrcode_to_reg_enduser_; //支持社区下室内机通过扫描二维码注册用户
    bool is_all_emergency_door_; //紧急告警开门是否选定所有门
    int community_creator_type_;


    int enable_package_detection_;
    // int enable_sound_detection_;
    // int sound_type_;
};

typedef std::shared_ptr<CommunityInfo> CommunityInfoPtr;



#endif
