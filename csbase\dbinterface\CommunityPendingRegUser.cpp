#include <sstream>
#include <string.h>
#include "Rldb.h"
#include "util.h"
#include "util_string.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "ConnectionManager.h"
#include "CommunityPendingRegUser.h"
#include "dbinterface/InterfaceComm.h"

namespace dbinterface{

int CommunityPendingRegUser::InsertCommunityPendingRegUser(RegEndUserInfo& reg_info)
{
    int ret = 0;
    
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    // 开启事务,防止室内机同时请求生成了多个token下发
    db_conn->BeginTransAction();

    std::stringstream stream_sql;
    stream_sql << "select Status,Token,Account from CommunityPendingRegUser where Account = '" << reg_info.account << "' for update";

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        reg_info.status = ATOI(query.GetRowData(0));
        Snprintf(reg_info.token, sizeof(reg_info.token), query.GetRowData(1));
        Snprintf(reg_info.account, sizeof(reg_info.account), query.GetRowData(2));
    }
    
    // token为空: 未插入过则插入一条数据, token重置过则更新token
    if (strlen(reg_info.token) == 0)
    {
        std::string token = GetNbitRandomString(18);
        Snprintf(reg_info.token, sizeof(reg_info.token), token.c_str());

        stream_sql.str("");
        stream_sql << "insert into CommunityPendingRegUser (Account, Token) values ('" << reg_info.account << "','" << reg_info.token << "')"
                   << "on duplicate key update Token = '" << reg_info.token << "'";

        if (db_conn->Execute(stream_sql.str()) < 0)
        {
            ret = -1;
            db_conn->TransActionRollback();
        }
    }

    db_conn->EndTransAction();
    return ret;
}



}