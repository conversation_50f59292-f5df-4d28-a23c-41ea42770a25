#ifndef __DB_COMMUNITY_PENDING_REG_USER_H__
#define __DB_COMMUNITY_PENDING_REG_USER_H__

#include <string>
#include <memory>
#include <tuple>
#include "BasicDefine.h"
#include "PendingRegUser.h"


namespace dbinterface{
class CommunityPendingRegUser
{
public:
    static int InsertCommunityPendingRegUser(RegEndUserInfo& reg_info);
private:
    CommunityPendingRegUser() = delete;
    ~CommunityPendingRegUser()= delete;

};

}


#endif