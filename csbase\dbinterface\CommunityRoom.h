#ifndef __DB_COMMUNITY_ROOM_H__
#define __DB_COMMUNITY_ROOM_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include "RldbQuery.h"
#include "Rldb.h"
#include "DclientMsgSt.h"
#include "AkcsCommonSt.h"
#include "util.h"


typedef struct CommunityRoom_T
{
    int id;
    //char room_name[128];
    int unit_id;
    char room_number[16];
    char floor[16];
    char unit_uuid[64];
    char uuid[64];
    CommunityRoom_T() { 
        room_number[0] = 0;
        floor[0] = 0;
        memset(&unit_uuid, 0, sizeof(unit_uuid));
        memset(&uuid, 0, sizeof(uuid));
    };    
}CommunityRoomInfo;

typedef std::shared_ptr<CommunityRoomInfo> CommunityRoomInfoPtr;
typedef std::map<std::string/*node*/, CommunityRoomInfo> CommunityRoomInfoMap;
typedef CommunityRoomInfoMap::iterator CommunityRoomInfoMapIter;


namespace dbinterface
{

class CommunityRoom
{
public:
    CommunityRoom();
    ~CommunityRoom();
    static int GetCommunityRoomByID(int id, CommunityRoomInfo &key_info);
    static int GetCommunityRoomByUUID(const std::string& uuid, CommunityRoomInfo &key_info);
    static int GetCommunityRoomByNodes(const std::string &nodes, CommunityRoomInfoMap &map);
    static int GetCommunityRoomByNode(const std::string &node, CommunityRoomInfo& info);
    static int GetRoomIDByUnitIDAndRoomNum(uint32_t unit_id, const std::string& room_num, uint32_t& room_id);
    static int GetCommunityRoomByProjetID(uint32_t project_id, CommunityRoomInfoMap &map);
private:
};

}


class CommunityRoomContorl
{
public:
    CommunityRoomContorl(){}
    void Init(uint32_t mng_id)
    {
        dbinterface::CommunityRoom::GetCommunityRoomByProjetID(mng_id, node_room_map_);
    }
    int GetNodeRoom(const std::string &node, CommunityRoomInfo &room)
    {
        const auto &it = node_room_map_.find(node);
        if (it != node_room_map_.end())
        {
            room = it->second;
            return 0;
        }
        return -1;
    }
    
    ~CommunityRoomContorl(){};
private:
    CommunityRoomInfoMap node_room_map_;
};




#endif
