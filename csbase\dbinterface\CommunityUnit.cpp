#include "AkLogging.h"
#include "dbinterface/CommunityUnit.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"


namespace dbinterface
{

static const std::string community_init_info_sec = " MngAccountID,UnitName,Floor,GroundFloor,StartFloor";

void CommunityUnit::GetCommunityUnitFromSql(CommunityUnitInfo& community_unit_info, CRldbQuery& query)
{
    community_unit_info.mng_id = ATOI(query.GetRowData(0));
    Snprintf(community_unit_info.unit_name, sizeof(community_unit_info.unit_name), query.GetRowData(1));
    Snprintf(community_unit_info.floor, sizeof(community_unit_info.floor), query.GetRowData(2));
    community_unit_info.ground_floor = ATOI(query.GetRowData(3));
    Snprintf(community_unit_info.start_floor, sizeof(community_unit_info.start_floor), query.GetRowData(4));
    return;
}

int CommunityUnit::GetCommunityUnitByID(int id, CommunityUnitInfo& community_unit_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << community_init_info_sec << " from CommunityUnit where ID = " << id;
    
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetCommunityUnitFromSql(community_unit_info, query);
    }
    else
    {
        AK_LOG_WARN << "GetCommunityUnitByID failed, id = " << id;
        return -1;
    }
    
    return 0;
}

int CommunityUnit::GetCommunityUnitByUUID(const std::string& uuid, CommunityUnitInfo& community_unit_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << community_init_info_sec << " from CommunityUnit where UUID = '" << uuid << "'";
    
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetCommunityUnitFromSql(community_unit_info, query);
    }
    else
    {
        AK_LOG_WARN << "GetCommunityUnitByUUID failed, uuid = " << uuid;
        return -1;
    }
    
    return 0;
}

int CommunityUnit::GetCommunityUnitsByMngID(int mng_id, std::vector<COMMUNIT_UNIT_INFO>& units_info)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    COMMUNIT_UNIT_INFO unit_info;
    std::stringstream streamSQL2;
    streamSQL2 << "SELECT ID FROM CommunityUnit "
               << "WHERE MngAccountID = '"
               << mng_id
               << "'";

    CRldbQuery query(tmp_conn);
    query.Query(streamSQL2.str());


    while (query.MoveToNextRow())
    {
        memset(&unit_info, 0, sizeof(unit_info));
        unit_info.unit_id =  ATOI(query.GetRowData(0));
        units_info.push_back(unit_info);
    }

    ReleaseDBConn(conn);
    return 0;

}

std::string CommunityUnit::GetCommunityUnitName(int unit_id)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return "";
    }

    std::stringstream streamSQL2;
    streamSQL2 << "SELECT UnitName FROM CommunityUnit "
               << "WHERE ID = '"
               << unit_id
               << "'";
    std::string strName;
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL2.str());

    if (query.MoveToNextRow())
    {
        strName = query.GetRowData(0);
    }

    ReleaseDBConn(conn);
    return strName;

}


int CommunityUnit::GetCommunityUnitMap(int mng_id, CommunityUnitMap& units_info)
{
    std::stringstream stream_sql;
    stream_sql << "SELECT ID,UnitName,Floor,GroundFloor,StartFloor,BuildingID FROM CommunityUnit "
               << "WHERE MngAccountID = '"
               << mng_id
               << "'";

   GET_DB_CONN_ERR_RETURN(db_conn, -1);
   
   CRldbQuery query(db_conn.get());
   query.Query(stream_sql.str());

    while (query.MoveToNextRow())
    {
        CommunityUnitInfo info;
        info.id = ATOI(query.GetRowData(0));
        Snprintf(info.unit_name, sizeof(info.unit_name), query.GetRowData(1));
        Snprintf(info.floor, sizeof(info.floor), query.GetRowData(2));
        info.ground_floor = ATOI(query.GetRowData(3));
        Snprintf(info.start_floor, sizeof(info.start_floor), query.GetRowData(4));
        info.building_id = ATOI(query.GetRowData(5));
        units_info.insert(std::make_pair(info.id, info));
    }
    return 0;
}


}

