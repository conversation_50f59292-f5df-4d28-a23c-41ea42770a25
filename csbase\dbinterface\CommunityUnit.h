#ifndef __DB_COMMUNITY_UNIT_H__
#define __DB_COMMUNITY_UNIT_H__

#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include <sstream>
#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkcsCommonDef.h"
#include "AkcsCommonSt.h"

typedef struct CommunityUnit_T
{
    int id;
    int mng_id;
    char unit_name[64];
    char floor[64];
    int ground_floor;
    char start_floor[64];

    CommunityUnit_T() {
        memset(this, 0, sizeof(*this));
    }
}CommunityUnitInfo;

using CommunityUnitMap = std::map<uint32_t, CommunityUnitInfo>;

namespace dbinterface
{

class CommunityUnit
{
public:
    static int GetCommunityUnitByID(int id, CommunityUnitInfo& community_unit_info);
    static int GetCommunityUnitByUUID(const std::string& uuid, CommunityUnitInfo& community_unit_info);
    static int GetCommunityUnitsByMngID(int mng_id, std::vector<COMMUNIT_UNIT_INFO>& units_info);
    static std::string GetCommunityUnitName(int unit_id);

    static int GetCommunityUnitMap(int mng_id,        CommunityUnitMap& units_info);
private:
    static void GetCommunityUnitFromSql(CommunityUnitInfo& community_unit_info, CRldbQuery& query);

    CommunityUnit() = delete;
    ~CommunityUnit() = delete;
};

}
#endif
