#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "ContactBlock.h"
#include <string.h>
#include "AkLogging.h"

namespace dbinterface
{


ContactBlock::ContactBlock()
{

}


int ContactBlock::GetBlackListByPerUUID(const std::string &uuid, std::set<std::string> &list)
{
    std::stringstream streamsql;
    streamsql << "select BlockPersonalAccountUUID from ContactBlackList where PersonalAccountUUID = '" 
              << uuid << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamsql.str());
    while (query.MoveToNextRow())
    {
        std::string tmp_uuid;
        tmp_uuid = query.GetRowData(0);
        list.insert(tmp_uuid);
    }

    ReleaseDBConn(conn);
    return 0;    
}

int ContactBlock::GetPerUUIDListByBlack(const std::string &uuid, std::set<std::string> &list)
{
    std::stringstream streamsql;
    streamsql << "select PersonalAccountUUID from ContactBlackList where BlockPersonalAccountUUID = '" 
              << uuid << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamsql.str());
    while (query.MoveToNextRow())
    {
        std::string tmp_uuid;
        tmp_uuid = query.GetRowData(0);
        list.insert(tmp_uuid);
    }

    ReleaseDBConn(conn);
    return 0;    
}


bool ContactBlock::JudgeBlock(const std::string &callee, const std::string &caller)
{
    std::stringstream streamsql;
    streamsql << "select ID from ContactBlackList where PersonalAccountUUID = '" 
              << callee << "' and BlockPersonalAccountUUID = '" << caller << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return false;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamsql.str());
    if (query.MoveToNextRow())
    {
        ReleaseDBConn(conn);
        return true;
    }

    ReleaseDBConn(conn);
    return false;    
}


}

