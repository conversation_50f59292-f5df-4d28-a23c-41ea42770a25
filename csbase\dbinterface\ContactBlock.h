#ifndef __DB_BLOCK_H__
#define __DB_BLOCK_H__
#include <string>
#include <set>

namespace dbinterface
{

class ContactBlock
{
public:
    ContactBlock();
    ~ContactBlock();
    //获取某个房间的黑名单列表   
    static int GetBlackListByPerUUID(const std::string &uuid, std::set<std::string> &list);
    //获取某个房间被哪些房间加入了黑名单 
    static int GetPerUUIDListByBlack(const std::string &uuid, std::set<std::string> &list);
    //判断是否被拉黑
    static bool JudgeBlock(const std::string &callee, const std::string &caller);

};

}
#endif
