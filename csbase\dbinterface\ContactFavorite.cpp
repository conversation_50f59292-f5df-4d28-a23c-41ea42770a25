#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "ContactFavorite.h"
#include <string.h>
#include "AkLogging.h"

namespace dbinterface
{


ContactFavorite::ContactFavorite()
{

}


int ContactFavorite::GetFavoriteListByPerUUID(const std::string &uuid, std::set<std::string> &list)
{
    std::stringstream streamsql;
    streamsql << "select FavoritePersonalAccountUUID from ContactFavoriteList where PersonalAccountUUID = '" 
              << uuid << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamsql.str());
    while (query.MoveToNextRow())
    {
        std::string tmp_uuid;
        tmp_uuid = query.GetRowData(0);
        list.insert(tmp_uuid);
    }

    ReleaseDBConn(conn);
    return 0;    
}

int ContactFavorite::GetPerUUIDListByFavorite(const std::string &uuid, std::set<std::string> &list)
{
    std::stringstream streamsql;
    streamsql << "select PersonalAccountUUID from ContactFavoriteList where FavoritePersonalAccountUUID = '" 
              << uuid << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamsql.str());
    while (query.MoveToNextRow())
    {
        std::string tmp_uuid;
        tmp_uuid = query.GetRowData(0);
        list.insert(tmp_uuid);
    }

    ReleaseDBConn(conn);
    return 0;    
}


}

