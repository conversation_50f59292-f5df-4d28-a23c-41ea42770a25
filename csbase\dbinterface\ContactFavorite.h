#ifndef __DB_FAVORITE_H__
#define __DB_FAVORITE_H__
#include <string>
#include <set>


namespace dbinterface
{

class ContactFavorite
{
public:
    ContactFavorite();
    ~ContactFavorite();
    //获取某个房间的收藏列表          
    static int GetFavoriteListByPerUUID(const std::string &uuid, std::set<std::string> &list);
    //获取某个房间被哪些房间收藏
    static int GetPerUUIDListByFavorite(const std::string &uuid, std::set<std::string> &list);

};

}
#endif
