#include "AES256.h"
#include "AkLogging.h"
#include "DataConfusion.h"

namespace dbinterface
{

static bool KCacheDataConfusion = false;
std::unordered_map<std::string/*加密前*/, std::string/*加密后*/> KCacheDataConfusionEncryptMap;
std::unordered_map<std::string/*加密后*/, std::string/*加密前*/> KCacheDataConfusionDecryptMap;

static const char KDataConfusionIV[] = "0123456789000000";
static const char KDataConfusionKey[] = "Akuvox1956131*69czeahaaew216023*";

void DataConfusion::SetCacheOption(int enable)
{
    if (enable)
    {
        KCacheDataConfusion = true;
    }
    else
    {
        KCacheDataConfusion = false;
    }
    return;
}

std::string DataConfusion::Encrypt(const char* data)
{
    if (!data || strlen(data) == 0) 
    {
        return "";    
    }

    // 从缓存中取
    std::string cache_data = GetGlobaEncryptCache(data);
    if (!cache_data.empty())
    {
        return cache_data;
    }

    // 加密数据
    char iv[20] = "";
    char encrypt_data[513] = "";
    
    GetIV(iv);
    
    AES256Base64Encrypt(KDataConfusionKey, iv, data, strlen(data), encrypt_data, sizeof(encrypt_data));

    // 设置到缓存中
    SetGlobalEncryptCache(data, encrypt_data);
    
    return encrypt_data;
}

std::string DataConfusion::Decrypt(const char* data)
{
    if (!data || strlen(data) == 0) 
    {
        return "";    
    }

    // 从缓存中取
    std::string cache_data = GetGlobalDecryptCache(data);
    if (!cache_data.empty())
    {
        return cache_data;
    }

    // 解密数据
    char iv[20] = "";
    char decrypt_data[513] = "";
    
    GetIV(iv);
    
    AES256Base64Decrypt(KDataConfusionKey, iv, data, strlen(data), decrypt_data, sizeof(decrypt_data));

    // 设置到缓存中
    SetGlobalDecryptCache(data, decrypt_data);
    
    return decrypt_data;
}

void DataConfusion::GetIV(char *iv)
{
    if (!iv) 
    {
        return;
    }
    memset(iv, 0, sizeof(KDataConfusionIV));
    snprintf(iv, sizeof(KDataConfusionIV), "%s", KDataConfusionIV);
}

void DataConfusion::SetGlobalEncryptCache(const std::string& key, const std::string& value)
{
    if (!KCacheDataConfusion)
    {
        return;
    }

    if (!key.empty() && !value.empty())
    {
        KCacheDataConfusionEncryptMap[key] = value;
    }
    return;
}

void DataConfusion::SetGlobalDecryptCache(const std::string& key, const std::string& value)
{
    if (!KCacheDataConfusion)
    {
        return;
    }

    if (!key.empty() && !value.empty())
    {
        KCacheDataConfusionDecryptMap[key] = value;
    }
    
    return;
}

std::string DataConfusion::GetGlobaEncryptCache(const std::string& key)
{
    if (!KCacheDataConfusion)
    {
        return "";
    }

    return KCacheDataConfusionEncryptMap[key];
}

std::string DataConfusion::GetGlobalDecryptCache(const std::string& key)
{
    if (!KCacheDataConfusion)
    {
        return "";
    }

    return KCacheDataConfusionDecryptMap[key];
}

int DataConfusion::GetEncryptCacheCount()
{
    return KCacheDataConfusionEncryptMap.size();
}

int DataConfusion::GetDecryptCacheCount()
{
    return KCacheDataConfusionDecryptMap.size();
}


}
