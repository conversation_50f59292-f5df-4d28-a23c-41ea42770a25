#ifndef __DB_DATA_CONFUSION_H__
#define __DB_DATA_CONFUSION_H__

#include <string>
#include <unordered_map>

namespace dbinterface
{

class DataConfusion
{
public:

    static void  SetCacheOption(int enable);

    static std::string Encrypt(const char* data);
    static std::string Decrypt(const char* data);
    
    static void SetGlobalEncryptCache(const std::string& key, const std::string& value);
    static void SetGlobalDecryptCache(const std::string& key, const std::string& value);
    
    static std::string GetGlobaEncryptCache(const std::string& key);
    static std::string GetGlobalDecryptCache(const std::string& key);

    static int GetEncryptCacheCount();
    static int GetDecryptCacheCount();
private:
    static void GetIV(char* iv);

    DataConfusion() = delete;
    ~DataConfusion() = delete;
};

}
#endif
