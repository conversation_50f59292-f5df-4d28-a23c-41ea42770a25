#ifndef __DB_COMMON_ST_H__
#define __DB_COMMON_ST_H__

#include <string>
#include <memory>
#include "AkcsCommonDef.h"




//会导致重复包含的问题放到这里统一定义或者声明
struct ResidentPersonalAccount_T;

typedef struct PersonalAccountNodeInfo_T{
    char node[32];
    char username[128];
    
    csmain::DeviceType conn_type;
    PersonalAccountNodeInfo_T() {
        memset(this, 0, sizeof(*this));
    }

}PersonalAccountNodeInfo;


typedef std::vector<struct ResidentPersonalAccount_T> ResidentPerAccountList;
typedef std::map<std::string, PersonalAccountNodeInfo> PersonalAccountNodeInfoMap;


#endif
