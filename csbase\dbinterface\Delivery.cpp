#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "Delivery.h"
#include <string.h>
#include "AkLogging.h"
#include  "ConnectionManager.h"

namespace dbinterface
{

Delivery::Delivery()
{

}

int Delivery::InitDeliveryByID(uint32_t id, DeliveryInfo &delivery)
{
    std::stringstream streamSQL;
    streamSQL << "select Version from Delivery where ID = '" << id << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    if (query.MoveToNextRow())
    {
        delivery.version = ATOI(query.GetRowData(0));
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;
}

int Delivery::InitDeliveryByUUID(const std::string& uuid, DeliveryInfo& delivery)
{
    GET_DB_CONN_ERR_RETURN(conn, -1)

    std::stringstream stream_sql;
    stream_sql << "select Version,CommunityID from Delivery where UUID = '"
                       << uuid << "'";

    CRldbQuery query(conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        delivery.version = ATOI(query.GetRowData(0));
        delivery.community_id = ATOI(query.GetRowData(1));
    }
    
    return 0;    
}

int Delivery::GetVersionById(uint32_t id)
{
    DeliveryInfo delivery;
    memset(&delivery, 0, sizeof(delivery));
    InitDeliveryByID(id, delivery);
    return delivery.version;
}

}


