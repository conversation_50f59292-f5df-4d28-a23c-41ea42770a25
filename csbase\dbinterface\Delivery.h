#ifndef __DB_DELIVERY_H__
#define __DB_DELIVERY_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include <cstring>

namespace dbinterface
{


typedef struct Delivery_T
{
    int version;    //数据库字段长度
    uint32_t community_id; //社区id
    Delivery_T()
    {
        memset(this, 0, sizeof(*this));
    }
}DeliveryInfo;

class Delivery
{
public:
    Delivery();
    ~Delivery();
    
    static int GetVersionById(uint32_t id);
    static int InitDeliveryByUUID(const std::string& uuid, DeliveryInfo& staff);
private:
    static int InitDeliveryByID(uint32_t id, DeliveryInfo &delivery);
};

typedef std::shared_ptr<DeliveryInfo> DeliveryInfoPtr;


}
#endif
