#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include <string.h>
#include "AkLogging.h"
#include "DeliveryAccess.h"
#include "util.h"
#include <vector>
#include "ConnectionManager.h"
namespace dbinterface
{

DeliveryAccess::DeliveryAccess()
{

}

int DeliveryAccess::GetAgIDsByDeliveryID(int delivery_id, std::vector<unsigned int>& ag_ids)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    std::stringstream stream_sql;
    stream_sql << "select AccessGroupID from DeliveryAccess where DeliveryID = " << delivery_id;

    CRldbQuery query(tmp_conn);
    query.Query(stream_sql.str());
    
    while (query.MoveToNextRow())
    {
        ag_ids.push_back(ATOI(query.GetRowData(0)));
    }
    
    ReleaseDBConn(conn);
    return 0;
}

int DeliveryAccess::GetAgIDsByDeliveryUUID(const std::string& delivery_uuid, std::vector<uint32_t>& ag_ids)
{
    GET_DB_CONN_ERR_RETURN(conn, -1)

    std::stringstream stream_sql;
    stream_sql << "select DA.AccessGroupID from DeliveryAccess DA left join Delivery D on DA.DeliveryID = D.ID "
                       <<  " where D.UUID = '" << delivery_uuid << "'";

    CRldbQuery query(conn.get());
    query.Query(stream_sql.str());

    while (query.MoveToNextRow())
    {
        ag_ids.push_back(ATOI(query.GetRowData(0)));
    }

    return 0;
}

/*公共人员：公共设备权限组包含的staff/delivery列表*/
void DeliveryAccess::GetPubDevDeliveryListByAccessGroupID(uint id, UserAccessNodeList &list)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }

    std::stringstream str_sql;
    str_sql << "select P.Name,P.CardCode,P.PinCode,P.Version,P.UUID,P.ID,DID.Mode,DID.Run,DID.Serial From DeliveryAccess D left join Delivery P "
        << "on P.ID=D.DeliveryID left join DeliveryIDAccess DID on DID.DeliveryUUID=P.UUID where D.AccessGroupID=" << id << " order by D.ID";

    CRldbQuery query(tmp_conn);
    query.Query(str_sql.str());
    while (query.MoveToNextRow())
    {
        UserAccessNode ua;
        ua.ag_id = id;
        Snprintf(ua.name, sizeof(ua.name), query.GetRowData(0));
        Snprintf(ua.pm_rf, sizeof(ua.pm_rf), query.GetRowData(1));
        Snprintf(ua.pin, sizeof(ua.pin), query.GetRowData(2));
        Snprintf(ua.meta, sizeof(ua.meta), query.GetRowData(3));
        Snprintf(ua.db_uuid, sizeof(ua.db_uuid), query.GetRowData(4));
        ua.dbid = ATOI(query.GetRowData(5));
        ua.id_access_mode = ATOI(query.GetRowData(6));
        Snprintf(ua.id_access_run, sizeof(ua.id_access_run), query.GetRowData(7));
        Snprintf(ua.id_access_serial, sizeof(ua.id_access_serial), query.GetRowData(8));
        list.push_back(ua);
    }

    ReleaseDBConn(conn);      
}


}

