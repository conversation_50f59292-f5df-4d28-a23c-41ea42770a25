#ifndef __DB_DELIVERY_ACCESS_H__
#define __DB_DELIVERY_ACCESS_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include "AkcsCommonDef.h"
#include "AkcsCommonSt.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "AccessGroupDB.h"


namespace dbinterface
{

class DeliveryAccess
{
public:
    DeliveryAccess();
    ~DeliveryAccess();
    static int GetAgIDsByDeliveryID(int id, std::vector<unsigned int>& ag_ids);
    static int GetAgIDsByDeliveryUUID(const std::string& delivery_uuid, std::vector<uint32_t>& ag_ids);
    static void GetPubDevDeliveryListByAccessGroupID(uint id, UserAccessNodeList &list);
    
private: 
};


}

#endif

