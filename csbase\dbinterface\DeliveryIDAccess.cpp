#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "DeliveryIDAccess.h"

namespace dbinterface {

static const std::string delivery_id_access_info_sec = " UUID,DeliveryUUID,Mode,Run,Serial ";

void DeliveryIDAccess::GetDeliveryIDAccessFromSql(DeliveryIDAccessInfo& delivery_id_access_info, CRldbQuery& query)
{
    Snprintf(delivery_id_access_info.uuid, sizeof(delivery_id_access_info.uuid), query.GetRowData(0));
    Snprintf(delivery_id_access_info.delivery_uuid, sizeof(delivery_id_access_info.delivery_uuid), query.GetRowData(1));
    delivery_id_access_info.mode = ATOI(query.GetRowData(2));
    Snprintf(delivery_id_access_info.run, sizeof(delivery_id_access_info.run), query.GetRowData(3));
    Snprintf(delivery_id_access_info.serial, sizeof(delivery_id_access_info.serial), query.GetRowData(4));
    return;
}

int DeliveryIDAccess::GetDeliveryIDAccessByUUID(const std::string& uuid, DeliveryIDAccessInfo& delivery_id_access_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << delivery_id_access_info_sec << " from DeliveryIDAccess where UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetDeliveryIDAccessFromSql(delivery_id_access_info, query);
    }
    else
    {
        AK_LOG_WARN << "get DeliveryIDAccessInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}

int DeliveryIDAccess::GetDeliveryIDAccessByDeliveryUUID(const std::string& delivery_uuid, DeliveryIDAccessInfo& delivery_id_access_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << delivery_id_access_info_sec << " from DeliveryIDAccess where DeliveryUUID = '" << delivery_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetDeliveryIDAccessFromSql(delivery_id_access_info, query);
    }
    else
    {
        AK_LOG_WARN << "get DeliveryIDAccessInfo by DeliveryUUID failed, DeliveryUUID = " << delivery_uuid;
        return -1;
    }
    return 0;
}

}