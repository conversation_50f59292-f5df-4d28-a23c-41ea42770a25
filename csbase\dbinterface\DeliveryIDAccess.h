#ifndef __DB_DELIVERY_I_D_ACCESS_H__
#define __DB_DELIVERY_I_D_ACCESS_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct DeliveryIDAccessInfo_T
{
    char uuid[36];
    char delivery_uuid[36];
    int mode;
    char run[10];
    char serial[9];
    DeliveryIDAccessInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} DeliveryIDAccessInfo;

namespace dbinterface {

class DeliveryIDAccess
{
public:
    static int GetDeliveryIDAccessByUUID(const std::string& uuid, DeliveryIDAccessInfo& delivery_id_access_info);
    static int GetDeliveryIDAccessByDeliveryUUID(const std::string& delivery_uuid, DeliveryIDAccessInfo& delivery_id_access_info);

private:
    DeliveryIDAccess() = delete;
    ~DeliveryIDAccess() = delete;
    static void GetDeliveryIDAccessFromSql(DeliveryIDAccessInfo& delivery_id_access_info, CRldbQuery& query);
};

}
#endif