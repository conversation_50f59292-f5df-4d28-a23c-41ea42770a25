#include <sstream>
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/InterfaceComm.h"
#include "DevLoginLog.h"
#include <string.h>
#include "AkLogging.h"


namespace dbinterface{
DevLoginLog::DevLoginLog()
{

}

DevLoginLog::~DevLoginLog()
{

}

int DevLoginLog::InsertDevLoginLog(const std::string &mac, const std::string &node)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    std::stringstream sql;
    sql << "INSERT INTO DevLoginLog"
               << " (Mac,Node) VALUES ('"
               << mac << "','"
               << node << "');";

    if (conn->Execute(sql.str()) < 0)
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;
}


}


