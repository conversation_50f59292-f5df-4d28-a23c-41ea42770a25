#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include <string.h>
#include "AkLogging.h"
#include "DevOfflineLog.h"

namespace dbinterface
{

DevOfflineLog::DevOfflineLog()
{

}

int DevOfflineLog::AddDevOfflineLog(RldbPtr& conn, const std::string& mng, int dev_count, const std::string& offline_mac)
{
    if(conn == nullptr)
    {
        return -1;
    }
    
    std::stringstream stream_sql;
    stream_sql << "insert into DevOfflineLog(MngAccountID,Quantity,MacList) value("
             << mng
             << ","
             << dev_count
             << ",'"
             << offline_mac
             << "')";
    conn->Execute(stream_sql.str());

    return 0;
}

}

