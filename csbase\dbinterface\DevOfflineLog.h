#ifndef __DB_DEVOFFLINELOG_H__
#define __DB_DEVOFFLINELOG_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include "RldbQuery.h"
#include "Rldb.h"
#include "DclientMsgSt.h"
#include "AkcsCommonSt.h"
#include "util.h"

namespace dbinterface
{

class DevOfflineLog
{
public:
    DevOfflineLog();
    ~DevOfflineLog();
    static int AddDevOfflineLog(RldbPtr& conn, const std::string& mng, int dev_count, const std::string& offline_mac);
private: 
};


}

#endif

