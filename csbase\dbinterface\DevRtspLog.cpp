#include <sstream>
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/InterfaceComm.h"
#include "DevRtspLog.h"
#include <string.h>
#include "AkLogging.h"


namespace dbinterface{
DevRtspLog::DevRtspLog()
{

}

DevRtspLog::~DevRtspLog()
{

}

int DevRtspLog::InsertDevRtspLog(const std::string &mac, const std::string &node)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::stringstream streamSQL1;
    streamSQL1 << "INSERT INTO DevRtspLog"
               << " (Mac,Node) VALUES ('"
               << mac << "','"
               << node << "');";

    //if(conn->Execute(streamSQL.str()) <= 0) added by chenyc,这了当token不变时,有可能返回0
    if (conn->Execute(streamSQL1.str()) < 0)
    {
        ReleaseDBConn(conn);
        AK_LOG_WARN << "Failed to insert rtsp log, sql:%s " << streamSQL1.str();
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;
}


}


