#include <sstream>
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/InterfaceComm.h"
#include "DevUpdateUserLogDB.h"
#include <string.h>
#include "AkLogging.h"

namespace dbinterface{
DevUpdateUserLog::DevUpdateUserLog()
{

}

DevUpdateUserLog::~DevUpdateUserLog()
{

}

int DevUpdateUserLog::InsertLog(const DevUpdateUserLogInfo &info)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* temp_conn = conn.get();
    if (NULL == temp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::stringstream sql;
    sql << "insert into DevUpdateUserLog(MAC, Accounts, TraceID) values("
        << "'" << info.mac << "',"
        << "'" << info.uuids << "',"
        << "'" << info.traceid << "');";
           

	if (temp_conn->Execute(sql.str()) < 0)
	{
		AK_LOG_WARN << "insert DevUpdateUserLog failed. TraceID="<< info.traceid;
		ReleaseDBConn(conn);
		return -1;
    }
    ReleaseDBConn(conn);
    return 0;
}

int DevUpdateUserLog::UpdateFilePath(const std::string &mac, DULONG traceid, const std::string &filepath)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* temp_conn = conn.get();
    if (NULL == temp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::stringstream sql;
    sql << "update DevUpdateUserLog set FilePath="
        << "'" << filepath << "' where Mac="
        << "'" << mac << "' and TraceID="
        << traceid;

	if (temp_conn->Execute(sql.str()) < 0)
	{
		AK_LOG_WARN << "DevUpdateUserLog UpdateFilePath failed. TraceID="<< traceid << " FilePath=" << filepath << " mac=" << mac;
		ReleaseDBConn(conn);
		return -1;
    }
    ReleaseDBConn(conn);
    return 0;
}


}


