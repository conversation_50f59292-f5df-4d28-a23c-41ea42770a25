#ifndef _DB_DEV_UPDATE_USERLOG_H_
#define _DB_DEV_UPDATE_USERLOG_H_

#include <string>
#include <memory>
#include <tuple>
#include "BasicDefine.h"

typedef struct DevUpdateUserLog_T
{
    char mac[20];
    DU<PERSON><PERSON><PERSON> traceid;
    char uuids[4096];//104100005;106100003
}DevUpdateUserLogInfo;


namespace dbinterface{
class DevUpdateUserLog
{
public:
    DevUpdateUserLog();
    ~DevUpdateUserLog();
    static int InsertLog(const DevUpdateUserLogInfo &info);
    static int UpdateFilePath(const std::string &mac, DULONG traceid, const std::string &filepath);
private:
};

}


#endif
