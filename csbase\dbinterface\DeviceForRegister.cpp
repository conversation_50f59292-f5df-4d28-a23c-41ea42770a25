#include <sstream>
#include <string.h>
#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "AkcsCommonDef.h"
#include "DeviceForRegister.h"
#include "dbinterface/InterfaceComm.h"

namespace dbinterface
{

int DeviceForRegister::GetDeviceRegisterInfo(const std::string& mac, DeviceRegisterInfo& register_info)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        return 0;
    }
    CRldbQuery query(tmp_conn);
    std::stringstream stream_sql;
    stream_sql << "select MngID, PerMngID, Owner from DeviceForRegister where MAC = '" << mac << "'";
    
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        register_info.dis_id = ATOI(query.GetRowData(0));
        register_info.ins_id = ATOI(query.GetRowData(1));
        Snprintf(register_info.owner, sizeof(register_info.owner), query.GetRowData(3));
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;
}


}



