#ifndef __DEVICE_FOR_REGISTER_H__
#define __DEVICE_FOR_REGISTER_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>

typedef struct DeviceRegisterInfo_T
{
    int  dis_id;   
    int  ins_id;
    char owner[128];   
}DeviceRegisterInfo;

namespace dbinterface
{

class DeviceForRegister
{

public:
    DeviceForRegister(){}
    ~DeviceForRegister();
    static int GetDeviceRegisterInfo(const std::string& mac, DeviceRegisterInfo& register_info);
};

}
#endif
