#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "dbinterface/DevicePushButtonList.h"
#include "dbinterface/ExternPushButton.h"

namespace dbinterface {

static const std::string device_push_button_list_info_sec = " DevicePushButtonUUID,DeviceUUID,Module,Sequence,CalleeUUID,Type,UUID ";

void DevicePushButtonList::GetDevicePushButtonListFromSql(DevicePushButtonListInfo& device_push_button_list_info, CRldbQuery& query)
{
    Snprintf(device_push_button_list_info.device_push_button_uuid, sizeof(device_push_button_list_info.device_push_button_uuid), query.GetRowData(0));
    Snprintf(device_push_button_list_info.device_uuid, sizeof(device_push_button_list_info.device_uuid), query.GetRowData(1));
    device_push_button_list_info.module = ATOI(query.GetRowData(2));
    device_push_button_list_info.sequence = ATOI(query.GetRowData(3));
    Snprintf(device_push_button_list_info.callee_uuid, sizeof(device_push_button_list_info.callee_uuid), query.GetRowData(4));
    device_push_button_list_info.type = ATOI(query.GetRowData(5));
    Snprintf(device_push_button_list_info.uuid, sizeof(device_push_button_list_info.uuid), query.GetRowData(6));
    return;
}

int DevicePushButtonList::GetButtonListByPushButtonUUIDandModuleID(const std::string& device_push_button_uuid, const int& module_id, std::vector<DevicePushButtonListInfo>& device_push_button_list_info_vec)
{
    std::stringstream stream_sql;
    stream_sql << "select " << device_push_button_list_info_sec << " from DevicePushButtonList where DevicePushButtonUUID = '" << device_push_button_uuid << "' AND Module = " << module_id;
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        DevicePushButtonListInfo device_push_button_list_info;
        GetDevicePushButtonListFromSql(device_push_button_list_info, query);
        device_push_button_list_info_vec.push_back(device_push_button_list_info);
        
    }
    return 0;
}

int DevicePushButtonList::GetDevicePushButtonListMapByProjectUUID(const std::string& project_uuid, DevicePushButtonListInfoMap& device_extern_push_button_list_map)
{
    std::stringstream stream_sql;
    stream_sql << "select " << device_push_button_list_info_sec << " from DevicePushButtonList where AccountUUID = '" << project_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        DevicePushButtonListInfo device_push_button_list_info;
        GetDevicePushButtonListFromSql(device_push_button_list_info, query);
        std::string device_extern_push_button_list_key = std::string(device_push_button_list_info.device_uuid) + "_" + std::to_string(device_push_button_list_info.module);
        device_extern_push_button_list_map.emplace(device_extern_push_button_list_key, device_push_button_list_info);
    }
    return 0;
}


}