#ifndef __DB_DEVICE_PUSH_BUTTON_LIST_H__
#define __DB_DEVICE_PUSH_BUTTON_LIST_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct DevicePushButtonListInfo_T
{
    char device_push_button_uuid[36];
    char device_uuid[36];
    int module;
    int sequence;
    char callee_uuid[36];
    int type;
    char uuid[36];
    DevicePushButtonListInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} DevicePushButtonListInfo;

typedef std::multimap<std::string, DevicePushButtonListInfo>DevicePushButtonListInfoMap;

namespace dbinterface {

class DevicePushButtonList
{
public:
    static int GetButtonListByPushButtonUUIDandModuleID(const std::string& device_push_button_uuid, const int& module_id, std::vector<DevicePushButtonListInfo>& device_push_button_list_info_vec);
    static int GetDevicePushButtonListMapByProjectUUID(const std::string& project_uuid, DevicePushButtonListInfoMap& device_extern_push_button_list_map);
private:
    DevicePushButtonList() = delete;
    ~DevicePushButtonList() = delete;
    static void GetDevicePushButtonListFromSql(DevicePushButtonListInfo& device_push_button_list_info, CRldbQuery& query);
};

}
#endif
