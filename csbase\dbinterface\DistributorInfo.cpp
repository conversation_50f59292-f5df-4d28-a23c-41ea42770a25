#include <sstream>
#include <string.h>
#include "util.h"
#include "AkLogging.h"
#include "AkcsCommonDef.h"
#include "DistributorInfo.h"
#include "dbinterface/InterfaceComm.h"

namespace dbinterface
{

static const std::vector<std::string> oem_name = {"Akuvox", "hager"};
static const std::string distibutor_info_sec = " Account,IsEncryptPin,IsVillaMonitor,IsYaleLock,IsEnableOfflineSolution,IsCreateSubDis,IsProjectLandLine,IsEnableRfCardControl,OemType ";

DistributorInfo::DistributorInfo()
{

}

void DistributorInfo::GetDistributorInfoFromSql(DistributorInfoSt& distributor_info, CRldbQuery& query)
{
    Snprintf(distributor_info.account, sizeof(distributor_info.account), query.GetRowData(0));
    distributor_info.enable_pin_encrypt = ATOI(query.GetRowData(1));
    distributor_info.show_villa_monitor = ATOI(query.GetRowData(2));
    distributor_info.enable_yale_lock = ATOI(query.GetRowData(3));
    distributor_info.enable_offline_solution = ATOI(query.GetRowData(4));
    distributor_info.enable_create_subdis = ATOI(query.GetRowData(5));
    distributor_info.enable_project_landline = ATOI(query.GetRowData(6));
    distributor_info.enable_rfcard_control = ATOI(query.GetRowData(7));
    
    int oem_index = ATOI(query.GetRowData(8));
    // 确保索引在有效范围内
    if (oem_index < 0 || oem_index >= static_cast<int>(oem_name.size())) {
        AK_LOG_INFO << "OEM index is out of bounds,oem_index:  " << oem_index <<", use default oem:akuvox";
        oem_index = 0;    
    }
    Snprintf(distributor_info.oem_name, sizeof(distributor_info.oem_name), oem_name[oem_index].c_str());    
}

int DistributorInfo::GetDistributorInfo(const std::string& account, DistributorInfoSt& distributor_info)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        return 0;
    }
    CRldbQuery query(tmp_conn);
    
    std::stringstream stream_sql;
    stream_sql << "select" << distibutor_info_sec << "from DistributorInfo where Account = '" << account << "'";
    
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetDistributorInfoFromSql(distributor_info, query);
        ReleaseDBConn(conn);
        return 0;
    }

    ReleaseDBConn(conn);
    return -1;
}


}




