#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "DoorReaderList.h"

namespace dbinterface {

static const std::string door_reader_list_info_sec = " Mode,Type,Rs485Address ";

void DoorReaderList::GetDoorReaderInfoFromSql(DoorReaderInfo& door_reader_info, CRldbQuery& query)
{
    door_reader_info.mode = (DoorReaderMode)ATOI(query.GetRowData(0));
    door_reader_info.type = (DoorReaderType)ATOI(query.GetRowData(1));
    Snprintf(door_reader_info.rs485_address, sizeof(door_reader_info.rs485_address), query.GetRowData(2));
    return;
}

int DoorReaderList::GetDoorReaderListByDevicesDoorListUUID(const std::string& devices_door_list_uuid, DoorReaderInfoList& door_reader_info_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << door_reader_list_info_sec << " from DoorReaderList where DevicesDoorListUUID = '" << devices_door_list_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        DoorReaderInfo door_reader_info;
        GetDoorReaderInfoFromSql(door_reader_info, query);
        door_reader_info_list.push_back(door_reader_info);
    }
    return 0;
}


}
