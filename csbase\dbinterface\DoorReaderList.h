#ifndef __DB_DOOR_READER_LIST_H__
#define __DB_DOOR_READER_LIST_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

enum class DoorReaderMode
{
    ENTRY = 0,
    EXIT = 1,
};

enum class DoorReaderType
{
    INTERNAL = 0,
    WIEGAND_A = 1,
    WIEGAND_B = 2,
    WIEGAND_C = 3,
    WIEGAND_D = 4,
    RS485_A = 5,
    RS485_B = 6,
    RS485_C = 7,
    RS485_D = 8,   
};

enum class DoorReaderRs485ConnectType
{
    SERIES = 0,
    SINGLE_DEVICE = 1,
};

typedef struct DoorReaderInfo_T
{
    DoorReaderMode mode; 
    DoorReaderType type;
    char rs485_address[128];

    DoorReaderInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} DoorReaderInfo;

using DoorReaderInfoList = std::vector<DoorReaderInfo>;

namespace dbinterface {

class DoorReaderList
{
public:
    static int GetDoorReaderListByDevicesDoorListUUID(const std::string& devices_door_list_uuid, DoorReaderInfoList& door_reader_info_list);
private:
    DoorReaderList() = delete;
    ~DoorReaderList() = delete;
    static void GetDoorReaderInfoFromSql(DoorReaderInfo& door_reader_list_info, CRldbQuery& query);
};

}
#endif
