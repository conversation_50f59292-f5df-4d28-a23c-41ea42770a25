#include <sstream>
#include "util.h"
#include "AkLogging.h"
#include "DormakabaLock.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

namespace dbinterface
{

static const std::string dormakaba_lock_sec = " IsBind,Relay,ProjectType,Name,UUID,ThirdUUID,DeviceUUID,AccountUUID,CommunityUnitUUID,PersonalAccountUUID ";

void DormakabaLock::GetDormakabaLockFromSql(DormakabaLockInfo& dormakaba_lock, CRldbQuery& query)
{
    dormakaba_lock.is_bind = ATOI(query.GetRowData(0));
    dormakaba_lock.relay = ATOI(query.GetRowData(1));
    dormakaba_lock.project_type = ATOI(query.GetRowData(2));
    Snprintf(dormakaba_lock.name, sizeof(dormakaba_lock.name), query.GetRowData(3));
    Snprintf(dormakaba_lock.uuid, sizeof(dormakaba_lock.uuid), query.GetRowData(4));
    Snprintf(dormakaba_lock.third_uuid, sizeof(dormakaba_lock.third_uuid), query.GetRowData(5));
    Snprintf(dormakaba_lock.device_uuid, sizeof(dormakaba_lock.device_uuid), query.GetRowData(6));
    Snprintf(dormakaba_lock.account_uuid, sizeof(dormakaba_lock.account_uuid), query.GetRowData(7));
    Snprintf(dormakaba_lock.community_unit_uuid, sizeof(dormakaba_lock.community_unit_uuid), query.GetRowData(8));
    Snprintf(dormakaba_lock.personal_account_uuid, sizeof(dormakaba_lock.personal_account_uuid), query.GetRowData(9));
    return;
}

// 一台设备多个relay,每个relay都可以绑定一把锁
int DormakabaLock::GetDormakabaLockListByDeviceUUID(const std::string& device_uuid, DormakabaLockInfoList& dormakaba_lock_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << dormakaba_lock_sec <<" from DormakabaLock where DeviceUUID = '" << device_uuid << "'";

    GET_DB_CONN_ERR_RETURN(conn, -1)
    
    CRldbQuery query(conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        DormakabaLockInfo dormakaba_lock;
        GetDormakabaLockFromSql(dormakaba_lock, query);
        dormakaba_lock_list.push_back(dormakaba_lock);
    }
    
    return 0;
}


}


