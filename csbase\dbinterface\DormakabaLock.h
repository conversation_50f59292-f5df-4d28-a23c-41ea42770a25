#ifndef __DB_DORMAKABA_LOCK_H__
#define __DB_DORMAKABA_LOCK_H__

#include <string>
#include "Rldb.h"
#include "RldbQuery.h"

typedef struct DormakabaLockInfo_T
{
    int is_bind;
    int relay;
    int project_type;
    char uuid[64];
    char name[256];
    char third_uuid[64];
    char device_uuid[64];
    char account_uuid[64];
    char community_unit_uuid[64];
    char personal_account_uuid[64];

    DormakabaLockInfo_T() {
        memset(this, 0, sizeof(*this));
    }
}DormakabaLockInfo;

typedef std::vector<DormakabaLockInfo> DormakabaLockInfoList;

namespace dbinterface{

class DormakabaLock
{
public:
    static int GetDormakabaLockListByDeviceUUID(const std::string& device_uuid, DormakabaLockInfoList& dormakaba_lock_list);
    
private:
    DormakabaLock() = delete;
    ~DormakabaLock() = delete;
    
    static void GetDormakabaLockFromSql(DormakabaLockInfo& dormakaba_lock, CRldbQuery& query);
};

}


#endif
