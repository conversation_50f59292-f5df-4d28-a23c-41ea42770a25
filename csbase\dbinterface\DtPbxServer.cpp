#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "DtPbxServer.h"
#include <string.h>
#include "AkLogging.h"

namespace dbinterface
{

DtPbxServer::DtPbxServer()
{

}

int DtPbxServer::GetGetPbxServer(const std::string& distributor, std::string& pbx_ip, std::string& pbx_ipv6)
{
    if (distributor.empty())
    {
        return -1;
    }

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        return -1;
    }
    CRldbQuery query(tmp_conn);

    std::stringstream streamsql;
    streamsql << "select PbxIp, PbxIpv6 from DtPbxServer where Distributor = '" << distributor << "' limit 1";
    query.Query(streamsql.str());

    if (!query.MoveToNextRow())
    {
        ReleaseDBConn(conn);
        return -1;
    }

    pbx_ip = query.GetRowData(0);
    pbx_ipv6 = query.GetRowData(1);

    ReleaseDBConn(conn);
    return 0;
}

}

