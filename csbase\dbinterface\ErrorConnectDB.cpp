#include <sstream>
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/InterfaceComm.h"
#include "ErrorConnectDB.h"
#include <string.h>
#include "AkLogging.h"
#include "ConnectionManager.h"

namespace dbinterface{
ErrorConnect::ErrorConnect()
{

}

ErrorConnect::~ErrorConnect()
{

}

int ErrorConnect::InsertErrorConnect(const std::string& ip, int type, const std::string& mac)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* temp_conn = conn.get();
    if (NULL == temp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::stringstream sql;
    sql << "insert into ErrorConnect(IP, ErrorType, MAC) values("
        << "'" << ip << "',"
        << "'" << type << "',"
        << "'" << mac << "');";
           

    if (temp_conn->Execute(sql.str()) < 0)
    {
        AK_LOG_WARN << "InsertErrorConnect failed. ";
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;
}

int ErrorConnect::InsertAuthCodeErrorConnect(const std::string& ip, int type, const std::string& mac, const std::string& report_auth_code, const std::string& mac_pool_auth_code)
{
    //插入数据构造
    std::map<std::string, std::string>str_map;
    str_map.emplace("IP", ip);   
    str_map.emplace("MAC", mac);        
    str_map.emplace("DevReportAuthCode", report_auth_code);        
    str_map.emplace("MacPoolAuthCode", mac_pool_auth_code);    
    std::map<std::string, int>int_map;
    int_map.emplace("ErrorType", type);
    std::string table_name = "ErrorConnect";
    
    GET_DB_CONN_ERR_RETURN(tmp_conn, -1);
    CRldb* conn = tmp_conn.get();
    int ret = conn->InsertData(table_name, str_map, int_map);
    return ret;
}


}


