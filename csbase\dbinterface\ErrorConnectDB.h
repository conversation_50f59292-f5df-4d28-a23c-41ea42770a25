#ifndef _DB_ERROR_CONNECT_H_
#define _DB_ERROR_CONNECT_H_

#include <string>
#include <memory>
#include <tuple>
#include "BasicDefine.h"


namespace dbinterface{
class ErrorConnect
{
public:
    ErrorConnect();
    ~ErrorConnect();
    static int InsertErrorConnect(const std::string& ip, int type, const std::string& mac);    
    static int InsertAuthCodeErrorConnect(const std::string& ip, int type, const std::string& mac, const std::string& report_auth_code, const std::string& mac_pool_auth_code);
private:
};

}


#endif
