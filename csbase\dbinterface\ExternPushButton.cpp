#include <sstream>
#include <string>
#include <cstring>
#include "dbinterface/ExternPushButton.h"
#include "util.h"
#include "AkLogging.h" 
#include <cstdio> 
#include "ConnectionManager.h"
#include "dbinterface/SystemSettingTable.h"
#include "dbinterface/UUID.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"


namespace dbinterface
{

static const std::string push_button_sec = " ID, DeviceUUID,UUID,AccountUUID,PersonalAccountUUID,Meta,CreateTime,Module0ButtonNum,Module1ButtonNum, \
Module2ButtonNum,Module3ButtonNum,Module4ButtonNum,Module5ButtonNum ";



void ExternPushButton::GetDevicePushButtonFromSql(DevicePushButton &devicePushButton, CRldbQuery &query) 
{
    devicePushButton.id = ATOI(query.GetRowData(0));
    Snprintf(devicePushButton.device_uuid, sizeof(devicePushButton.device_uuid), query.GetRowData(1));
    Snprintf(devicePushButton.uuid, sizeof(devicePushButton.uuid), query.GetRowData(2));
    Snprintf(devicePushButton.account_uuid, sizeof(devicePushButton.account_uuid), query.GetRowData(3));
    Snprintf(devicePushButton.personal_account_uuid, sizeof(devicePushButton.personal_account_uuid), query.GetRowData(4));
    devicePushButton.meta = ATOI(query.GetRowData(5));
    Snprintf(devicePushButton.createTime, sizeof(devicePushButton.createTime), query.GetRowData(6));
    devicePushButton.module_0_buttonNum = ATOI(query.GetRowData(7));
    devicePushButton.module_1_buttonNum = ATOI(query.GetRowData(8));
    devicePushButton.module_2_buttonNum = ATOI(query.GetRowData(9));
    devicePushButton.module_3_buttonNum = ATOI(query.GetRowData(10));
    devicePushButton.module_4_buttonNum = ATOI(query.GetRowData(11));
    devicePushButton.module_5_buttonNum = ATOI(query.GetRowData(12));
    return;
}

int ExternPushButton::GetDevicePushButtonByDeviceUUID(const std::string& devices_uuid, DevicePushButton& push_button)
{
    std::stringstream sql;
    sql << "select "<< push_button_sec <<"from DevicePushButton where DeviceUUID = '" << devices_uuid << "'";
    GET_DB_CONN_ERR_RETURN(tmp_conn, -1);
    
    CRldbQuery query(tmp_conn.get());
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {   
        GetDevicePushButtonFromSql(push_button, query);
    }
    else
    {
        AK_LOG_WARN << "No data found for DeviceUUID: " << devices_uuid;
        return -1;
    }
    
    return 0;
}


int ExternPushButton::GetDevicePushButtonMapByProjectUUID(const std::string& project_uuid, DevicePushButtonMap& push_button_map)
{
    std::stringstream sql;
    sql << "select "<< push_button_sec <<"from DevicePushButton where AccountUUID = '" << project_uuid << "'";
    GET_DB_CONN_ERR_RETURN(tmp_conn, -1);
    
    CRldbQuery query(tmp_conn.get());
    query.Query(sql.str());
    while(query.MoveToNextRow())
    {   
        DevicePushButton push_button;
        GetDevicePushButtonFromSql(push_button, query);
        push_button_map[push_button.device_uuid] = push_button;
    }
    return 0;
}

//社区更新设备extern push button信息
int ExternPushButton::UpdateExternPushButton(ResidentDev& dev, std::map<int, int> &module_list)
{
    std::stringstream sql;
    std::string uuid;
    dbinterface::UUID::GenerateUUID(dbinterface::SystemSetting::GetServerTag(), uuid);
    
    std::map<std::string, std::string> insert_str_datas;
    insert_str_datas.emplace("DeviceUUID", dev.uuid);
    insert_str_datas.emplace("UUID", uuid);

    if(dev.project_type == project::PERSONAL)
    {
        //单住户设备 记录PersonalAccountUUID
        std::string personal_aacount_uuid;
        dbinterface::ResidentPersonalAccount::GetUUIDByAccount(dev.node, personal_aacount_uuid);
        insert_str_datas.emplace("PersonalAccountUUID", uuid);
    }
    //社区设备，记录社区Account表uuid
    else
    {
        insert_str_datas.emplace("AccountUUID", std::string(dev.project_uuid));
    }
    std::map<std::string, int> insert_int_datas;
    insert_int_datas.emplace("Module0ButtonNum", module_list[0]);
    insert_int_datas.emplace("Module1ButtonNum", module_list[1]);
    insert_int_datas.emplace("Module2ButtonNum", module_list[2]);
    insert_int_datas.emplace("Module3ButtonNum", module_list[3]);
    insert_int_datas.emplace("Module4ButtonNum", module_list[4]);
    insert_int_datas.emplace("Module5ButtonNum", module_list[5]);
    
    std::map<std::string, std::string> update_str_datas;
    std::map<std::string, int> update_int_datas;
    update_int_datas.emplace("Module0ButtonNum", module_list[0]);
    update_int_datas.emplace("Module1ButtonNum", module_list[1]);
    update_int_datas.emplace("Module2ButtonNum", module_list[2]);
    update_int_datas.emplace("Module3ButtonNum", module_list[3]);
    update_int_datas.emplace("Module4ButtonNum", module_list[4]);
    update_int_datas.emplace("Module5ButtonNum", module_list[5]);

    std::string table_name= "DevicePushButton";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    CRldb* conn = db_conn.get();
    int ret = conn->InsertOrUpdateData(table_name, insert_str_datas, insert_int_datas, update_str_datas, update_int_datas);
    
    if (ret < 0) {
        AK_LOG_WARN << "Failed to insert or update data";
    }
    return ret;
}

}
