#ifndef __RESIDENT_EXTERN_PUSH_BUTTON_H__
#define __RESIDENT_EXTERN_PUSH_BUTTON_H__
#include <string>
#include <memory>
#include <vector>
#include <cstring>
#include "Rldb.h"
#include "RldbQuery.h"
#include "dbinterface/resident/ResidentDevices.h"


typedef struct DevicePushButton_T {
    int id;
    char device_uuid[64];  
    char uuid[64];     
    char account_uuid[64];  
    char personal_account_uuid[64];  
    unsigned int meta;
    char createTime[32];  
    int module_0_buttonNum;
    int module_1_buttonNum;
    int module_2_buttonNum;
    int module_3_buttonNum;
    int module_4_buttonNum;
    int module_5_buttonNum;

    DevicePushButton_T() {
        memset(this, 0, sizeof(*this));
    }
} DevicePushButton;
    
typedef std::map<std::string, DevicePushButton>DevicePushButtonMap;

namespace dbinterface
{

class ExternPushButton
{
public:
    ExternPushButton(){};
    ~ExternPushButton(){};
    static int GetDevicePushButtonByDeviceUUID(const std::string&devices_uuid, DevicePushButton& push_button);
    static void GetDevicePushButtonFromSql(DevicePushButton &devicePushButton, CRldbQuery &query);
    static int UpdateExternPushButton(ResidentDev& dev, std::map<int, int> &module_list);
    static int GetDevicePushButtonMapByProjectUUID(const std::string& project_uuid, DevicePushButtonMap& push_button_map);

};

}
#endif

