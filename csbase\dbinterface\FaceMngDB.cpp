#include <sstream>
#include <string.h>
#include "RldbQuery.h"
#include "Rldb.h"
#include "AkLogging.h"
#include "FaceMngDB.h"
#include "util.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/DataConfusion.h"


namespace dbinterface
{

FaceMng::FaceMng()
{

}

int FaceMng::GetFaceMngByPersonalAccountIds(std::vector<FaceMngInfo>& face_mng_infos, const std::vector<DEVICE_CONTACTLIST>& personal_account_ids)
{
    if (personal_account_ids.size() == 0)
    {
        return 0;
    }

    std::stringstream stream_sql;
    stream_sql << "SELECT a.ID,a.MngAccountID,a.UnitID,a.PersonalAccountID,a.FileName,a.FaceUrl,b.Name,b.Account,a.FaceMD5,a.CreatorType,a.CreatorUUID "
               << "  FROM FaceMng a LEFT JOIN PersonalAccount b on a.PersonalAccountID = b.ID"
               << " where a.PersonalAccountID in(";

    std::vector<int> ids_vec;
    for (const auto& ids : personal_account_ids)
    {
        ids_vec.push_back(ids.id);
    }
    std::string ids_str = ListToSeparatedFormatString(ids_vec);
    stream_sql << ids_str << ")";

    return GetFaceMngBySql(face_mng_infos,  stream_sql.str());
}

int FaceMng::GetFaceMngByPersonalAccountIds(std::map<std::string, FaceMngInfo>& list, const std::string &uid_ids)
{
    if (uid_ids.size() == 0 )
    {
        return 1;
    }
    std::stringstream stream_sql;
    stream_sql << "SELECT a.ID,a.MngAccountID,a.UnitID,a.PersonalAccountID,a.FileName,a.FaceUrl,b.Name,b.Account,a.FaceMD5,a.CreatorType,a.CreatorUUID "
               << "  FROM FaceMng a LEFT JOIN PersonalAccount b on a.PersonalAccountID = b.ID"
               << " where a.PersonalAccountID in(" << uid_ids << ");";


    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* rldb_conn = conn.get();
    if (NULL == rldb_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    CRldbQuery query(rldb_conn);
    query.Query(stream_sql.str());

    while (query.MoveToNextRow())
    {
        FaceMngInfo face_mng_info;
        memset(&face_mng_info, 0, sizeof(face_mng_info));

        face_mng_info.face_mng_id = ATOI(query.GetRowData(0));
        face_mng_info.mng_account_id = ATOI(query.GetRowData(1));
        face_mng_info.unit_id = ATOI(query.GetRowData(2));
        face_mng_info.personal_account_id = ATOI(query.GetRowData(3));
        Snprintf(face_mng_info.file_name, sizeof(face_mng_info.file_name), query.GetRowData(4));
        Snprintf(face_mng_info.face_url, sizeof(face_mng_info.face_url), query.GetRowData(5));
        Snprintf(face_mng_info.name, sizeof(face_mng_info.name), dbinterface::DataConfusion::Decrypt(query.GetRowData(6)).c_str());
        Snprintf(face_mng_info.account, sizeof(face_mng_info.account), query.GetRowData(7));
        Snprintf(face_mng_info.face_md5, sizeof(face_mng_info.face_md5), query.GetRowData(8));
        face_mng_info.creator_type = ATOI(query.GetRowData(9));
        Snprintf(face_mng_info.creator_uuid, sizeof(face_mng_info.creator_uuid), query.GetRowData(10));

        list.insert(std::make_pair(face_mng_info.account, face_mng_info));   
    }
    ReleaseDBConn(conn);
    return 0;
}

int FaceMng::GetFaceMngBySql(std::vector<FaceMngInfo>& face_mng_infos, const std::string sql)
{
    if (0 == sql.size())
    {
        return -1;
    }

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* rldb_conn = conn.get();
    if (NULL == rldb_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    CRldbQuery query(rldb_conn);
    query.Query(sql);

    while (query.MoveToNextRow())
    {
        FaceMngInfo face_mng_info;
        memset(&face_mng_info, 0, sizeof(face_mng_info));

        face_mng_info.face_mng_id = ATOI(query.GetRowData(0));
        face_mng_info.mng_account_id = ATOI(query.GetRowData(1));
        face_mng_info.unit_id = ATOI(query.GetRowData(2));
        face_mng_info.personal_account_id = ATOI(query.GetRowData(3));
        Snprintf(face_mng_info.file_name, sizeof(face_mng_info.file_name), query.GetRowData(4));
        Snprintf(face_mng_info.face_url, sizeof(face_mng_info.face_url), query.GetRowData(5));
        Snprintf(face_mng_info.name, sizeof(face_mng_info.name), dbinterface::DataConfusion::Decrypt(query.GetRowData(6)).c_str());
        Snprintf(face_mng_info.account, sizeof(face_mng_info.account), query.GetRowData(7));
        Snprintf(face_mng_info.face_md5, sizeof(face_mng_info.face_md5), query.GetRowData(8));
        face_mng_info.creator_type = ATOI(query.GetRowData(9));
        Snprintf(face_mng_info.creator_uuid , sizeof(face_mng_info.creator_uuid), query.GetRowData(10));
        
        face_mng_infos.push_back(face_mng_info);
    }

    ReleaseDBConn(conn);
    return 0;
}

int FaceMng::GetFaceMng(std::vector<FaceMngInfo>& face_mng_infos, int mng_account_id, int unit_id, int personal_account_id)
{
    if (mng_account_id == 0 && unit_id ==0 && personal_account_id == 0)
    {
        return -1;
    }
    
    std::stringstream stream_sql;
    stream_sql << "SELECT a.ID,a.MngAccountID,a.UnitID,a.PersonalAccountID,a.FileName,a.FaceUrl,b.Name,b.Account,a.FaceMD5,a.CreatorType,a.CreatorUUID "
               << " FROM FaceMng a LEFT JOIN PersonalAccount b on a.PersonalAccountID = b.ID"
               << " where 1=1 ";

    if (mng_account_id > 0)
    {
        stream_sql << " and a.MngAccountID = " << mng_account_id;
    }

    if (unit_id > 0)
    {
        stream_sql << " and a.UnitID = " << unit_id;
    }

    if (personal_account_id > 0)
    {
        stream_sql << " and a.PersonalAccountID = " << personal_account_id;
    }

    return GetFaceMngBySql(face_mng_infos, stream_sql.str());
}




}

