#ifndef __FACE_MNG_DB_H__
#define __FACE_MNG_DB_H__
#include <string>
#include <memory>
#include <vector>
#include <set>
#include <stdint.h>
#include "AkcsCommonSt.h"
#include "BasicDefine.h"
#include "util.h"


typedef struct FaceMngInfo_T
{
    int face_mng_id;
    int mng_account_id;
    int unit_id;
    int personal_account_id; //PersonalAccount表的ID
    char file_name[129]; //图片的文件名
    char face_url[65]; //xxx/xxxx/xxx.jpg
    char name[65]; //PersonalAccount表的Name
    char account[33]; //PersonalAccount表的Account
    char face_md5[33];  //图片的MD5
    int creator_type;  //创建者类型 0-pm 1-enduser
    char creator_uuid[64];
} FaceMngInfo;


namespace dbinterface
{

class FaceMng
{
public:
    FaceMng();
    ~FaceMng();

    static int GetFaceMngByPersonalAccountIds(std::vector<FaceMngInfo>& face_mng_infos, const std::vector<DEVICE_CONTACTLIST>& personal_account_ids);
    static int GetFaceMngByPersonalAccountIds(std::map<std::string, FaceMngInfo>& list, const std::string &uid_ids);
    static int GetFaceMngBySql(std::vector<FaceMngInfo>& face_mng_infos, const std::string sql);
    static int GetFaceMng(std::vector<FaceMngInfo>& face_mng_infos, int mng_account_id, int unit_id, int personal_account_id);
    
private:
    
};



}
#endif
