#include <sstream>
#include "InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "FeaturePlan.h"
#include <string.h>
#include "AkLogging.h"

namespace dbinterface
{

FeaturePlan::FeaturePlan(unsigned int plan_id)
{
    item_ = 0;
    plan_id_ = plan_id;
    Init();
}


FeaturePlan::~FeaturePlan()
{

}


void FeaturePlan::Init()
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }

    std::stringstream sql;
    sql << "SELECT item FROM  FeaturePlan  where ID= " << plan_id_;
           
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        item_ = ATOI(query.GetRowData(0));
    }
    else
    {
       AK_LOG_WARN << "can not found FeaturePlan id : " << plan_id_; 
    }
    ReleaseDBConn(conn);
    return;
}


        
int FeaturePlan::IsEnableAllowCreatePin()
{
    return SwitchHandle(item_, PLAN_ITEM_TYPE::AllowPin);
}

int FeaturePlan::GetFeatureCommunityList(std::vector<unsigned int> &ids)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::stringstream sql;
    sql << "SELECT AccountID FROM  ManageFeature  where FeatureID= " << plan_id_;
           
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        unsigned int mngid = ATOI(query.GetRowData(0));
        ids.push_back(mngid);
    }
    ReleaseDBConn(conn);
    return 0;    
}



}

