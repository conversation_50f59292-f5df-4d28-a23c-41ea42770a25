#ifndef __FEATURE_PLAN_H__
#define __FEATURE_PLAN_H__
#include <string>
#include <memory>
#include <vector>

namespace dbinterface
{

class FeaturePlan
{
public:
    FeaturePlan(unsigned int planid);
    ~FeaturePlan();

    enum PLAN_ITEM_TYPE
    {
        Delivery= 0,
        AllowPin = 1,
        AllowTempKey = 2,
        FamilyAppControl = 3,
    };
        
    int IsEnableAllowCreatePin();
    int GetFeatureCommunityList(std::vector<unsigned int> &ids);
private:
    void Init();
    unsigned int plan_id_;
    unsigned int item_;
};

typedef std::shared_ptr<FeaturePlan> FeaturePlanPtr;


}
#endif
