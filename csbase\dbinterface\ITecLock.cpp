#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "ITecLock.h"

namespace dbinterface {

static const std::string itec_lock_info_sec = " UUID,Name,LockId,ITecGatewayUUID,BatteryLevel,DeviceUUID,Relay ";

void ITecLock::GetITecLockFromSql(ITecLockInfo& i_tec_lock_info, CRldbQuery& query)
{
    Snprintf(i_tec_lock_info.uuid, sizeof(i_tec_lock_info.uuid), query.GetRowData(0));
    Snprintf(i_tec_lock_info.name, sizeof(i_tec_lock_info.name), query.GetRowData(1));
    i_tec_lock_info.lock_id = ATOI(query.GetRowData(2));
    Snprintf(i_tec_lock_info.i_tec_gateway_uuid, sizeof(i_tec_lock_info.i_tec_gateway_uuid), query.GetRowData(3));
    i_tec_lock_info.battery_level = ATOI(query.GetRowData(4));
    Snprintf(i_tec_lock_info.device_uuid, sizeof(i_tec_lock_info.device_uuid), query.GetRowData(5));
    i_tec_lock_info.relay = ATOI(query.GetRowData(6));
    return;
}

int ITecLock::GetItecLockListByDeviceUUID(const std::string& device_uuid, ITecLockInfoList& i_tec_lock_info_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << itec_lock_info_sec << " from ITecLock where DeviceUUID = '" << device_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        ITecLockInfo i_tec_lock_info;
        GetITecLockFromSql(i_tec_lock_info, query);
        i_tec_lock_info_list.push_back(i_tec_lock_info);
    }
    return 0;
}

}
