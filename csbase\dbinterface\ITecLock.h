#ifndef __DB_I_TEC_LOCK_H__
#define __DB_I_TEC_LOCK_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct ITecLockInfo_T
{
    char uuid[36];
    char name[256];
    int lock_id;
    char i_tec_gateway_uuid[36];
    int battery_level;
    char device_uuid[36];
    int relay;
    ITecLockInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} ITecLockInfo;

typedef std::vector<ITecLockInfo> ITecLockInfoList; 

namespace dbinterface {

class ITecLock
{
public:
    static int GetItecLockListByDeviceUUID(const std::string& device_uuid, ITecLockInfoList& i_tec_lock_info_list);

private:
    ITecLock() = delete;
    ~ITecLock() = delete;
    static void GetITecLockFromSql(ITecLockInfo& i_tec_lock_info, CRldbQuery& query);
};

}
#endif