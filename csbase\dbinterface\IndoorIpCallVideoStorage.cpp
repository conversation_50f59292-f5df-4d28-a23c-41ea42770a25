#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "dbinterface/UUID.h"
#include "IndoorIpCallVideoStorage.h"
#include "dbinterface/SystemSettingTable.h"

namespace dbinterface {

static const std::string indoor_ip_call_video_storage_info_sec = " UUID,CallTraceID,IndoorDevicesUUID,DoorDevicesUUID ";

void IndoorIpCallVideoStorage::GetIndoorIpCallVideoStorageFromSql(IndoorIpCallVideoStorageInfo& indoor_ip_call_video_storage_info, CRldbQuery& query)
{
    Snprintf(indoor_ip_call_video_storage_info.uuid, sizeof(indoor_ip_call_video_storage_info.uuid), query.GetRowData(0));
    Snprintf(indoor_ip_call_video_storage_info.call_trace_id, sizeof(indoor_ip_call_video_storage_info.call_trace_id), query.GetRowData(1));
    Snprintf(indoor_ip_call_video_storage_info.indoor_devices_uuid, sizeof(indoor_ip_call_video_storage_info.indoor_devices_uuid), query.GetRowData(2));
    Snprintf(indoor_ip_call_video_storage_info.door_devices_uuid, sizeof(indoor_ip_call_video_storage_info.door_devices_uuid), query.GetRowData(3));
    return;
}

int IndoorIpCallVideoStorage::GetIndoorIpCallVideoStorageInfo(const std::string& call_trace_id, const std::string& indoor_dev_uuid, IndoorIpCallVideoStorageInfo& indoor_ip_call_video_storage_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << indoor_ip_call_video_storage_info_sec << " from IndoorIpCallVideoStorage where CallTraceID = '" << call_trace_id << "' and IndoorDevicesUUID = '" << indoor_dev_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetIndoorIpCallVideoStorageFromSql(indoor_ip_call_video_storage_info, query);
    }
    else
    {
        AK_LOG_WARN << "get IndoorIpCallVideoStorageInfo failed, CallTraceID = " << call_trace_id << ", indoor_dev_uuid = " << indoor_dev_uuid;
        return -1;
    }
    return 0;
}

void IndoorIpCallVideoStorage::InsertIndoorIpCallVideoStorageInfo(const IndoorIpCallVideoStorageInfo& indoor_ip_call_video_storage_info)
{
    //插入数据构造
    std::string uuid;
    if (0 != UUID::GenerateUUID(dbinterface::SystemSetting::GetServerTag(), uuid))
    {
        return;
    }

    std::map<std::string, int> sql_int_map;
    std::map<std::string, std::string> sql_str_map;
    sql_str_map.emplace("UUID", uuid.c_str());
    sql_str_map.emplace("CallTraceID", indoor_ip_call_video_storage_info.call_trace_id);
    sql_str_map.emplace("IndoorDevicesUUID", indoor_ip_call_video_storage_info.indoor_devices_uuid);
    sql_str_map.emplace("DoorDevicesUUID", indoor_ip_call_video_storage_info.door_devices_uuid);

    GET_DB_CONN_ERR_RETURN(db_conn,);
    db_conn->InsertData("IndoorIpCallVideoStorage", sql_str_map, sql_int_map);

    return;
}

}
