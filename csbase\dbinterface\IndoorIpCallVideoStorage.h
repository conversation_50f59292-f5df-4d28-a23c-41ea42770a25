#ifndef __DB_INDOOR_IP_CALL_VIDEO_STORAGE_H__
#define __DB_INDOOR_IP_CALL_VIDEO_STORAGE_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct IndoorIpCallVideoStorageInfo_T
{
    char uuid[36];
    char call_trace_id[36];
    char indoor_devices_uuid[36];
    char door_devices_uuid[36];
    IndoorIpCallVideoStorageInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} IndoorIpCallVideoStorageInfo;

namespace dbinterface {

class IndoorIpCallVideoStorage
{
public:
    static int GetIndoorIpCallVideoStorageInfo(const std::string& call_trace_id, const std::string& indoor_devices_uuid, IndoorIpCallVideoStorageInfo& indoor_ip_call_video_storage_info);
    static void InsertIndoorIpCallVideoStorageInfo(const IndoorIpCallVideoStorageInfo& indoor_ip_call_video_storage_info);
private:
    IndoorIpCallVideoStorage() = delete;
    ~IndoorIpCallVideoStorage() = delete;
    static void GetIndoorIpCallVideoStorageFromSql(IndoorIpCallVideoStorageInfo& indoor_ip_call_video_storage_info, CRldbQuery& query);
};

}
#endif
