#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/SystemSettingTable.h"
#include "dbinterface/UUID.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "InsToken.h"
#include <string.h>
#include "AkLogging.h"
#include "util.h"

namespace dbinterface
{

static const std::string ins_token_sec = " AppToken";


InsToken::InsToken()
{

}

void InsToken::GetInsTokenFromSql(InsTokenInfo &token, CRldbQuery& query)
{
    Snprintf(token.app_token, sizeof(token.app_token), query.GetRowData(0));
    return;
}

int InsToken::GetInsTokenInfo(const std::string &userinfo_uuid, InsTokenInfo &token_info)
{
    std::stringstream streamSQL;
    streamSQL << "select " << ins_token_sec << " from InstallerToken where AccountUserInfoUUID = '"
              << userinfo_uuid << "' limit 1;";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed."; 
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    if (query.MoveToNextRow())
    {
        GetInsTokenFromSql(token_info, query);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;   
}

//installer app Token更新
int InsToken::InsertOrUpdateInsToken(const std::string& userinfo_uuid, const std::string& token, int expire_time, const std::string &server_tag)
{
    std::string uuid;
    dbinterface::UUID::GenerateUUID(server_tag, uuid);

    std::stringstream streamsql;
    streamsql << "INSERT INTO InstallerToken (AccountuserInfoUUID, AppToken, AppTokenEt, UUID) VALUES "
              << "('"<< userinfo_uuid << "','" << token << "'," << expire_time << ",'" << uuid << "') "
              << "ON DUPLICATE KEY UPDATE AppToken = '" << token << "', AppTokenEt = '"<< expire_time << "'";
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }    
    if (tmp_conn->Execute(streamsql.str()) < 0)
    {
        ReleaseDBConn(conn);
        AK_LOG_WARN << "Failed to insert new record into db,SQL is " << streamsql.str();
        return -1;
    }

    ReleaseDBConn(conn);
    return 0;
}

//installer app token renew info更新
int InsToken::InsertOrUpdateInsTokenRenewInfo(const std::string& userinfo_uuid, unsigned int expire_time, const TokenRenewInfo& token_renew_info, const std::string& server_tag)
{
    std::string uuid;
    dbinterface::UUID::GenerateUUID(server_tag, uuid);

    std::stringstream streamsql;
    streamsql << "INSERT INTO InstallerToken (AccountuserInfoUUID, AppToken, RefreshToken, AppTokenEt, UUID) VALUES "
                     << "('" << userinfo_uuid << "','" <<  token_renew_info.token << "','" << token_renew_info.refresh_token << "','" << expire_time << "','"
                     << uuid << "')" << " ON DUPLICATE KEY UPDATE AppToken = '" << token_renew_info.token 
                     << "', RefreshToken = '" << token_renew_info.refresh_token << "', AppTokenEt = " << expire_time;
    
    GET_DB_CONN_ERR_RETURN(conn, -1);
    CRldb* tmp_conn = conn.get();
    if (tmp_conn->Execute(streamsql.str()) < 0)
    {
        AK_LOG_WARN << "Failed to insert new record into db,SQL is " << streamsql.str();
        return -1;
    }

    return 0;
}

int InsToken::UpdateInsToken(int limited_time, const std::string& token, const std::string& userinfo_uuid)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    std::stringstream streamsql;
    streamsql << "UPDATE InstallerToken set AppTokenEt = "
               << limited_time
               << " WHERE AccountUserInfoUUID = '"
               << userinfo_uuid
               << "' and AppToken='"
               << token
               << "';";
    if(conn->Execute(streamsql.str()) < 0)
    {
        ReleaseDBConn(conn);
        AK_LOG_WARN << "Failed to update db,SQL is " << streamsql.str();
        return -1;
    }

    ReleaseDBConn(conn);
    return 0;
}

int InsToken::GetInsTokenInfoByAppToken(const std::string& token, InsTokenInfo &token_info)
{
    GET_DB_CONN_ERR_RETURN(conn, -1);
    CRldb* tmp_conn = conn.get();

    std::stringstream streamsql;
    streamsql << "SELECT AppToken, RefreshToken, AccountUserInfoUUID, AppTokenEt from InstallerToken where AppToken = '" << token << "' limit 1;";

    CRldbQuery query(tmp_conn);
    query.Query(streamsql.str());
    if (!query.MoveToNextRow())
    {
        AK_LOG_WARN << "get ins token info failed. app token: " << token;
        return -1;
    }
    
    Snprintf(token_info.app_token, sizeof(token_info.app_token), query.GetRowData(0));
    Snprintf(token_info.refresh_token, sizeof(token_info.refresh_token), query.GetRowData(1));
    Snprintf(token_info.userinfo_uuid, sizeof(token_info.userinfo_uuid), query.GetRowData(2));
    token_info.exp_time = STOULL(query.GetRowData(3));

    return 0;   
}

}

