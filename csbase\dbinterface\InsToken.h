#ifndef __DB_INS_TOKEN_H__
#define __DB_INS_TOKEN_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include "RldbQuery.h"
#include "dbinterface/Token.h"
#include "ConnectionManager.h"
typedef struct INS_TOKEN_T
{
    char app_token[128];
    char refresh_token[128];
    char userinfo_uuid[64];
    uint64_t exp_time;
    INS_TOKEN_T()
    {
        memset(this, 0, sizeof(*this));
    }
}InsTokenInfo;

namespace dbinterface
{

class InsToken
{
public:
    InsToken();
    ~InsToken();
    static int GetInsTokenInfo(const std::string &userinfo_uuid, InsTokenInfo &token_info);
    static int InsertOrUpdateInsToken(const std::string& userinfo_uuid, const std::string& token, int expire_time, const std::string &server_tag);
    static int UpdateInsToken(int limited_time, const std::string& token, const std::string& userinfo_uuid);
    static int InsertOrUpdateInsTokenRenewInfo(const std::string& userinfo_uuid, unsigned int expire_time, const TokenRenewInfo& token_renew_info, const std::string& server_tag);
    static int GetInsTokenInfoByAppToken(const std::string& token, InsTokenInfo &token_info);
private:
    static void GetInsTokenFromSql(InsTokenInfo &token, CRldbQuery& query);
};

}
#endif
