#include <sstream>
#include "InterfaceComm.h"
#include <string.h>
#include <boost/algorithm/string.hpp>
#include <boost/functional/hash.hpp>
#include <boost/format.hpp>
#include <boost/crc.hpp>



namespace dbinterface{


int ATOI(const char* str)
{
    if ((str == NULL) || (strlen(str) == 0))
    {
        return 0;
    }

    return atoi(str);
}

int SwitchHandle(int value, int pos)
{
    return value & (1 << pos) ? 1 : 0;
}

void StringReplace(std::string &replace_string, const std::string &src_string, const std::string &dst_string)
{
	size_t pos = 0;
	size_t src_size = src_string.size();
	size_t dst_size = dst_string.size();
	while ((pos = replace_string.find(src_string, pos)) != std::string::npos)
	{
		replace_string.replace(pos, src_size, dst_string);
		pos += dst_size;
	}
} 

uint32_t crc32_hash(const std::string key)
{
    boost::crc_32_type ret;
    ret.process_bytes(key.c_str(), key.size());
    return ret.checksum();
}

}




