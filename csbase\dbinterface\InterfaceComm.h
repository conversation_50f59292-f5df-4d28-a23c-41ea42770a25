#ifndef __INTERFACE_COMM_H__
#define __INTERFACE_COMM_H__
#include "BasicDefine.h"

namespace dbinterface{


int ATOI(const char* str);
int SwitchHandle(int value, int pos);
void StringReplace(std::string &replace_string, const std::string &src_string, const std::string &dst_string);
void string_split(std::string& s, std::string& delim, std::vector< std::string >* ret);
uint32_t crc32_hash(const std::string key);



}



#endif
