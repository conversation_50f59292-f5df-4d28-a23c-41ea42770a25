#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "LadderControl.h"
#include <string.h>
#include "AkLogging.h"
#include "util.h"

namespace dbinterface
{

LadderControl::LadderControl()
{
    
}

std::string LadderControl::GetFloorByUUID(const std::string& uuid, unsigned int dev_unit_id, enum USER_TYPE user_type)
{
    std::stringstream stream_sql;
    if (user_type == USER_TYPE::STAFF)
    {
        stream_sql << "select Floor,UnitID from StaffLadderControlList where StaffUUID = '" << uuid << "'";
    }
    else
    {
        stream_sql << "select Floor,UnitID from DeliveryLadderControlList where DeliveryUUID = '" << uuid << "'";
    }
    
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return kDefaultFloor;
    }

    CRldbQuery query(tmp_conn);
    query.Query(stream_sql.str());

    std::string floor = kDefaultFloor;
    while (query.MoveToNextRow())
    {
        std::string user_floor = query.GetRowData(0);
        unsigned int user_unit_id = ATOI(query.GetRowData(1));

        // tmp_unit_id为0代表all buildings
        if (user_unit_id == 0 || user_unit_id == dev_unit_id)
        {
            floor = user_floor;
            break;
        }
    }
    
    ReleaseDBConn(conn);
    return floor.size() > 0 ? floor : kDefaultFloor;
}

}
