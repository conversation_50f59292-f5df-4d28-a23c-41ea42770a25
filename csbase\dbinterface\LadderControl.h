#ifndef __DB_LADDER_CONTROL_H__
#define __DB_LADDER_CONTROL_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include <AkcsCommonDef.h>

namespace dbinterface
{

typedef std::map<int/*buildID*/, std::string/*floor*/> LadderControlInfoMap;
typedef LadderControlInfoMap::iterator LadderControlInfoMapIter;


class LadderControl
{
public:
    LadderControl();
    ~LadderControl();

    static std::string GetFloorByUUID(const std::string& staff_uuid, unsigned int unit_id, enum USER_TYPE user_type);
private:
    
};

}
#endif


