#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/LicensePlate.h"
#include <string.h>
#include "ConnectionManager.h"
#include "dbinterface/DataConfusion.h"
#include "util.h"

namespace dbinterface
{

LicensePlate::LicensePlate()
{

}

void LicensePlate::GetUserLicensePlateList(const std::string& user_uuids, UsersLicensePlateInfoMap&plate_list)
{
    GET_DB_CONN_ERR_RETURN(conn,);
    std::stringstream str_sql;
    CRldbQuery query(conn.get());
    str_sql << "select Plate,UHF,PersonalAccountUUID,TimeControl,BeginTime,EndTime From LicensePlate where PersonalAccountUUID in(" << user_uuids << ");";
    query.Query(str_sql.str());
    while (query.MoveToNextRow()) {
        std::string plate = query.GetRowData(0);
        std::string uhf = query.GetRowData(1);
        std::string user_uuid = query.GetRowData(2);
        int time_control = ATOI(query.GetRowData(3));
        std::string begin_time = query.GetRowData(4);
        std::string end_time = query.GetRowData(5);

        // 0 永久有效，1 区间生效 永久有效按原有权限组时间，值为空 
        if (time_control == 0) {
            begin_time = "";
            end_time = "";
        }

        CarData car_data = {plate, uhf, begin_time, end_time};

        UsersLicensePlateInfoMapIter iter = plate_list.find(user_uuid);
        if (iter != plate_list.end()) {
            iter->second.push_back(car_data);
        } else {
            std::vector<CarData> vec = {car_data};
            plate_list.insert(std::make_pair(user_uuid, vec));
        }
    }
    return;
}


void LicensePlate::GetNodeLicensePlateList(const std::string& node, RF_KEY** keylist)
{
    RF_KEY* rf_key_header = *keylist;
    if (rf_key_header)
    {
        while (rf_key_header->next)
        {
            rf_key_header = rf_key_header->next;

        }
    }
    RF_KEY* cur_rf_key = rf_key_header;

    std::stringstream str_sql;
    str_sql << "select P2.Name,L.Plate,L.UHF From LicensePlate L join PersonalAccount P on L.ManageUUID=P.UUID join PersonalAccount P2 on L.PersonalAccountUUID=P2.UUID where P.Account='" << node <<"'";

    GET_DB_CONN_ERR_RETURN(conn,)
    CRldbQuery query(conn.get());


    query.Query(str_sql.str());
    while (query.MoveToNextRow())
    {
        RF_KEY* newkey = new RF_KEY();
        memset(newkey, 0, sizeof(RF_KEY));
        Snprintf(newkey->user, sizeof(newkey->user), dbinterface::DataConfusion::Decrypt(query.GetRowData(0)).c_str());
        Snprintf(newkey->code, sizeof(newkey->code), query.GetRowData(1));
        Snprintf(newkey->ufh, sizeof(newkey->ufh), query.GetRowData(2));
        newkey->card_type = CardType::LICENSE_PLATE;
        if (strlen(newkey->code) > 0)
        {
            if (rf_key_header == NULL)
            {
                rf_key_header = newkey;
                *keylist = rf_key_header;
            }
            else
            {
                cur_rf_key->next = newkey;
            }

            cur_rf_key = newkey;
        }
        else
        {
            delete newkey;
        }
    }
    return;

}


DatabaseExistenceStatus LicensePlate::GetLicensePlateByKey(const std::string& code, const std::string& mng_uuid, LicensePlateInfo& license_plate_info)
{
    std::stringstream stream_sql;
    stream_sql << "SELECT L.Plate,P.Name,P.UUID,L.ProjectType FROM LicensePlate L join PersonalAccount P on L.PersonalAccountUUID = P.UUID WHERE (L.Plate = '" << code << "'"
    << "OR L.UHF = '" << code << "') AND L.ManageUUID = '" << mng_uuid << "'";
    
    GET_DB_CONN_ERR_RETURN(conn, DatabaseExistenceStatus::QUERY_ERROR);
    CRldbQuery query(conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        Snprintf(license_plate_info.plate, sizeof(license_plate_info.plate), query.GetRowData(0));
        Snprintf(license_plate_info.personal_name, sizeof(license_plate_info.personal_name), dbinterface::DataConfusion::Decrypt(query.GetRowData(1)).c_str());
        Snprintf(license_plate_info.personal_uuid, sizeof(license_plate_info.personal_uuid), query.GetRowData(2));
        license_plate_info.project_type = (CommonProjectType)ATOI(query.GetRowData(3));
        return DatabaseExistenceStatus::EXIST;
    }
    
    return DatabaseExistenceStatus::NOT_EXIST;
}

}

