#ifndef __DB_COMM_LICENSE_PLATE_H__
#define __DB_COMM_LICENSE_PLATE_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include "PersonalRfcardKey.h"

struct CarData {
    std::string LicensePlate;
    std::string UFHCard;
    std::string BeginTime;
    std::string EndTime;
};
typedef std::map<std::string, std::vector<CarData>> UsersLicensePlateInfoMap;
typedef UsersLicensePlateInfoMap::iterator UsersLicensePlateInfoMapIter;

typedef struct LicensePlate_T
{
    char personal_name[256];
    char plate[32];
    char personal_uuid[64];
    CommonProjectType project_type;
}LicensePlateInfo;

namespace dbinterface
{

class LicensePlate
{
public:
    LicensePlate();
    ~LicensePlate();
    static void GetUserLicensePlateList(const std::string& user_uuids, UsersLicensePlateInfoMap& plate_list);
    static void GetNodeLicensePlateList(const std::string& node, RF_KEY** keylist);
    static DatabaseExistenceStatus GetLicensePlateByKey(const std::string& code, const std::string& mng_uuid, LicensePlateInfo& license_plate_info);
private:
};

}
#endif
