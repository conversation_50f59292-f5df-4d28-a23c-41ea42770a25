#include <sstream>
#include <string.h>
#include <ctime>
#include "Rldb.h"
#include "util.h"
#include "AkLogging.h"
#include "RldbQuery.h"
#include "ConnectionManager.h"
#include "LogConnectionPool.h"
#include "dbinterface/Log/LogSlice.h"
#include "dbinterface/Log/LogSlice.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/Log/CallHistoryDB.h"
#include "dbinterface/ProjectUserManage.h"



namespace dbinterface
{
static const char table_call_history[] = "CallHistory";

CallHistory::CallHistory()
{

}


CallHistory::~CallHistory() {}

std::string CallHistory::GetDbLogDeliveryUUID(const std::string& project_uuid)
{
    return project_uuid;
}


std::string CallHistory::GetLogTableName(const std::string& table_name, const std::string& db_delivery_uuid, int delivery)
{
    //hash取模
    uint32_t uuid_hash = crc32_hash(db_delivery_uuid);
    int hash = uuid_hash % delivery;

    //表名构造
    return table_name + "_" + std::to_string(hash);
}

int CallHistory::AddCallHistory(PbxCallHistory* call_history, int delivery)
{
    if (call_history == NULL || delivery == 0)
    {
        return -1;
    }
    
    RldbPtr conn = GetLogDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }    

    //插入数据构造
    std::map<std::string, std::string> string_map;
    string_map.emplace("Node", call_history->node);
    string_map.emplace("CallerID", call_history->caller);
    string_map.emplace("CallerName", call_history->caller_name);
    string_map.emplace("CalleeID", call_history->callee);
    string_map.emplace("CalleeName", call_history->callee_name);
    string_map.emplace("Duration", call_history->bill_second_time);
    string_map.emplace("SipGroup",  call_history->sipgroup);
    string_map.emplace("FreeswitchNode", call_history->freeswitch_node); 
    string_map.emplace("CallerOpsNode", call_history->caller_ops_node); 
    string_map.emplace("CalleeOpsNode", call_history->callee_ops_node); 
    string_map.emplace("CalledID", call_history->called); 
    string_map.emplace("CalledName", call_history->called_name); 

    if (strlen(call_history->start_time) > 0) {
        string_map.emplace("StartTime", call_history->start_time);
    } else {
        string_map.emplace("sql_StartTime", "now()");
    }

    if(strlen(call_history->project_uuid2) > 0)
    {
        string_map.emplace("ProjectUUID", call_history->project_uuid2); 
    }

    if(strlen(call_history->company_uuid) > 0)
    {
        string_map.emplace("OfficeCompanyUUID", call_history->company_uuid); 
    }

    // CallTraceID字段定义为NULL, CallLog也插入空字符串会关联到CallTraceID为空的图片
    if (strlen(call_history->call_trace_id) > 0)
    {
        string_map.emplace("CallTraceID", call_history->call_trace_id); 
    }

    std::map<std::string, int> int_map;
    int_map.emplace("GroupType", static_cast<int>(call_history->call_group_type));
    int_map.emplace("IsAnswer", call_history->is_answer);
    int_map.emplace("status", call_history->status);
    int_map.emplace("Duration2", call_history->bill_second);
    int_map.emplace("CallType", call_history->calltype);
    int_map.emplace("MngAccountID", call_history->mng_id);

    //表名构造
    std::string table_name = CallHistory::GetLogTableName(table_call_history, call_history->db_delivery_uuid, delivery);

    int ret = tmp_conn->InsertData(table_name, string_map, int_map);

    //释放数据库连接
    ReleaseLogDBConn(conn);
    return ret;
}

DatabaseExistenceStatus CallHistory::CallHistoryExist(const std::string& db_delivery_uuid, int delivery, const std::string& node, const std::string& call_trace_id)
{
    std::vector<std::string> table_list;
    std::string basic_table = GetLogTableName(table_call_history, db_delivery_uuid, delivery);
    GetCallHistoryTableList(basic_table, table_list);

    GET_LOG_DB_CONN_ERR_RETURN(conn, DatabaseExistenceStatus::QUERY_ERROR);
    CRldbQuery query(conn.get());
    for (const auto& table : table_list)
    {
        std::stringstream stream_sql;
        //判断是同个房间的 后续才让其能查询到通话记录
        stream_sql << "select count(*) from " << table << " where CallTraceID = '" << call_trace_id << "' and Node = '" << node << "'";
		
        query.Query(stream_sql.str());
        if (query.MoveToNextRow())
        {
            if (ATOI(query.GetRowData(0)) > 0)
            {
                return DatabaseExistenceStatus::EXIST;
            }
        }
    }
    return DatabaseExistenceStatus::NOT_EXIST;
}

std::string CallHistory::getLogMonth(int begin)
{
    char log_month[7];

    std::time_t now = std::time(nullptr);
    std::tm last_month = *std::localtime(&now);
    last_month.tm_mon -= begin;
    std::strftime(log_month, sizeof(log_month), "%Y%m", &last_month);
    
    return log_month;
}

//日志记录中途是否分片
int CallHistory::IsDeliveredWhileRecord(int max_save_month, time_t delivery_time)
{
    RldbPtr conn = GetLogDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);

    int delivered = 0;
    std::stringstream stream_sql;
    stream_sql << "select unix_timestamp(NOW() - interval 30*" << max_save_month <<  " day) < '" << delivery_time << "' as Flag";
    
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        delivered = ATOI(query.GetRowData(0));
    }
    
    ReleaseLogDBConn(conn);
    return delivered;
}

void CallHistory::GetCallHistoryTables(const std::string& db_delivery_uuid, int delivery, std::vector<std::string>& log_tables)
{
    std::string table_name = "CallHistory";
    LOG_SLICE_INFO log_slice_info;
    memset(&log_slice_info, 0, sizeof(log_slice_info));
    if (0 == dbinterface::LogSlice::GetSliceInfoByTableName(table_name, log_slice_info))
    {
    /*  
        int last_delivery = log_slice_info.last_delivery;
        //当前时间 减去 DeliveryTime 时间 是否小于 MaxSaveMonth
        //超过最大保存时间，说明所有的数据都落到最新的分片中，否则还有数据在旧的分片中
        // int have_old_delivery = IsDeliveredWhileRecord(log_slice_info.max_save_month, log_slice_info.delivery_time);
        
        int index = crc32_hash(project_uuid) % delivery;
        
        std::string basic_table = table_name + "_" + std::to_string(index);

        log_tables.push_back(basic_table);

        // 上次分片大小
        if (0 == last_delivery) 
        {
            // 如LOG.CallHistory_2, LOG.CallHistory_2_202303, LOG.CallHistory_2_202302, ...
            for (int i = 1; i <= log_slice_info.max_save_month; i++)
            {
                std::string log_month = getLogMonth(i);
                log_tables.push_back(basic_table + "_" + log_month);
            }
        }
        else
        {
            下面的代码注释掉，等到后续需要再扩时候还需要开发和测试验证
            if (have_old_delivery)
            {
                // 如LOG.CallHistory_2, LOG.CallHistory_2_202303, LOG.CallHistory_2_202302, ...
                int last_delivery_index = crc32_hash(project_uuid) % log_slice_info.last_delivery;
                log_tables.push_back(table_name + "_" + std::to_string(last_delivery_index));

                for (int i = 1; i < log_slice_info.max_save_month; i++)
                {
                    std::string log_month = getLogMonth(i);
                    log_tables.push_back(basic_table + "_" + log_month);
                    log_tables.push_back(table_name + "_" + std::to_string(last_delivery_index) + "_" + log_month);
                }
            }
            else
            {
                 for (int i = 1; i <= log_slice_info.max_save_month; i++) 
                 {
                    std::string log_month = getLogMonth(i);
                    log_tables.push_back(basic_table + "_" + log_month);
                }
            }
        */    
    }
}

int CallHistory::GetUnReadCallHistoryCount(const std::string& account, int delivery)
{
    int unread_count = 0;
    std::string db_delivery_uuid = dbinterface::ProjectUserManage::GetLogDeliveryUUIDByAccount(account);

    std::vector<std::string> log_tables;
    GetCallHistoryTables(db_delivery_uuid, delivery, log_tables); 

    RldbPtr conn = GetLogDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return 0;
    }
    CRldbQuery query(tmp_conn);

    std::stringstream stream_sql;
    for (const auto& table : log_tables)
    {
        stream_sql.str("");
        stream_sql << "select count(*) as num from " << table << " where CalleeID = '" << account << "' and IsAnswer = 0 and Status = 0";

        query.Query(stream_sql.str());
        if (query.MoveToNextRow())
        {
            unread_count += ATOI(query.GetRowData(0));
        }
    }

    ReleaseLogDBConn(conn);
    return unread_count;
}

DatabaseExistenceStatus CallHistory::GetCallHistoryTableList(const std::string& basic_table, std::vector<std::string>& tables_list, int limit)
{
    GET_LOG_DB_CONN_ERR_RETURN(conn, DatabaseExistenceStatus::QUERY_ERROR);
    
    std::stringstream stream_sql;
    stream_sql << "select table_name from information_schema.tables where table_name like '" << basic_table << "%'"
               << " order by case when table_name = '" << basic_table << "' then 0 else 1 end, table_name desc";
    
    // 如果 limit 大于 0，则添加 LIMIT 子句
    if (limit > 0) {
        stream_sql << " limit " << limit;
    }

    CRldbQuery query(conn.get());
    query.Query(stream_sql.str());

    while (query.MoveToNextRow())
    {
        tables_list.push_back(query.GetRowData(0));
    }
    return DatabaseExistenceStatus::EXIST;
}

DatabaseExistenceStatus CallHistory::GroupCallHistoryExist(const std::string& db_delivery_uuid, int delivery,
                        const std::string& call_trace_id, std::string& table_name, int& call_history_id)
{
    std::vector<std::string> table_list;
    std::string basic_table = GetLogTableName(table_call_history, db_delivery_uuid, delivery);
    // 防止日志出现在分月前后，导致查询不到
    GetCallHistoryTableList(basic_table, table_list, 2);

    GET_LOG_DB_CONN_ERR_RETURN(conn, DatabaseExistenceStatus::QUERY_ERROR);
    CRldbQuery query(conn.get());
    for (const auto& table : table_list)
    {
        std::stringstream stream_sql;
        //判断是否存在群呼记录，同时需要获取对应表的其余数据，方便更新接听用户
        //添加FOR UPDATE锁，防止并发插入多条CallType=7的记录
        stream_sql << "select count(*),ID from " << table << " where CallType = " << (int)CallType::GROUP_CALL << " and CallTraceID = '" << call_trace_id << "' FOR UPDATE";

		
        query.Query(stream_sql.str());
        if (query.MoveToNextRow())
        {
            if (ATOI(query.GetRowData(0)) > 0)
            {
                table_name = table;
                call_history_id = ATOI(query.GetRowData(1));
                return DatabaseExistenceStatus::EXIST;
            }
        }
    }
    return DatabaseExistenceStatus::NOT_EXIST;
}

std::vector<CallHistoryRecord> CallHistory::GetAllCallHistoryByTraceID(const std::string& db_delivery_uuid, int delivery, const std::string& call_trace_id)
{
    std::vector<CallHistoryRecord> records;
    std::vector<std::string> table_list;
    std::string basic_table = GetLogTableName(table_call_history, db_delivery_uuid, delivery);
    GetCallHistoryTableList(basic_table, table_list, 2);

    GET_LOG_DB_CONN_ERR_RETURN(conn, records);
    CRldbQuery query(conn.get());
    for (const auto& table : table_list)
    {
        std::stringstream stream_sql;
        stream_sql << "select ID,CallType,IsAnswer,CalledName,Duration2 from " << table << " where CallTraceID = '" << call_trace_id << "'";
        
        query.Query(stream_sql.str());
        while (query.MoveToNextRow())
        {
            CallHistoryRecord record;
            Snprintf(record.table_name, sizeof(record.table_name), table.c_str());
            record.table_call_history_id = ATOI(query.GetRowData(0));
            record.call_type = ATOI(query.GetRowData(1));
            record.is_answer = ATOI(query.GetRowData(2));
            Snprintf(record.answer_name, sizeof(record.answer_name), query.GetRowData(3));
            record.answer_duration = ATOI(query.GetRowData(4));
            records.push_back(record);
        }
    }
    return records;
}

int CallHistory::UpdateGroupCallHistoryAnswer(PbxCallHistory* history, const std::string& table_name, int call_history_id)
{
    GET_LOG_DB_CONN_ERR_RETURN(conn, -1);

    std::stringstream stream_sql;
    stream_sql << "update " << table_name << " set IsAnswer = 1,Duration = " << history->bill_second_time << ",Duration2 = " << history->bill_second
               << ",CalledID = '" << history->called << "',CalledName = '" << history->called_name << "'"
               << " where ID= '" << call_history_id << "' and CallTraceID = '" << history->call_trace_id << "' and CallType = " << (int)CallType::GROUP_CALL;

    int nRet = conn->Execute(stream_sql.str()) > 0 ? 0 : -1;
    return nRet;
}

int CallHistory::UpdateCallTraceIDToCallEach(const std::string& table_name, int call_history_id)
{
    GET_LOG_DB_CONN_ERR_RETURN(conn, -1);
    std::stringstream stream_sql;
    stream_sql << "update " << table_name << " set CallType = " << (int)CallType::GROUP_EACH_CALL
               << " where ID= '"  << call_history_id << "'";

    int nRet = conn->Execute(stream_sql.str()) > 0 ? 0 : -1;
    return nRet;
}


}
