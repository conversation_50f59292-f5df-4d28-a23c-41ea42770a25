#ifndef __DB_CALL_HISTORY_H__
#define __DB_CALL_HISTORY_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include "AkcsCommonSt.h"
#include "AkcsCommonDef.h"
#include <boost/algorithm/string.hpp>

namespace dbinterface
{

class CallHistory
{
public:
    CallHistory();
    ~CallHistory();
    static std::string GetDbLogDeliveryUUID(const std::string& project_uuid);
    static int AddCallHistory(PbxCallHistory* call_history, int delivery);
    static int GetUnReadCallHistoryCount(const std::string& account, int delivery);
    static DatabaseExistenceStatus CallHistoryExist(const std::string& db_delivery_uuid, int delivery, const std::string& node, const std::string& call_trace_id);
    static DatabaseExistenceStatus GroupCallHistoryExist(const std::string& db_delivery_uuid, int delivery,
                        const std::string& call_trace_id, std::string& table_name, int& call_history_id);
    static std::vector<CallHistoryRecord> GetAllCallHistoryByTraceID(const std::string& db_delivery_uuid, int delivery, const std::string& call_trace_id);
    static int UpdateGroupCallHistoryAnswer(PbxCallHistory* history, const std::string& table_name, int call_history_id);
    static int UpdateCallTraceIDToCallEach(const std::string& table_name, int call_history_id);
private:
    static std::string getLogMonth(int begin);
    static std::string GetLogTableName(const std::string& table_name, const std::string& db_delivery_uuid, int delivery);
    static int IsDeliveredWhileRecord(int max_save_month, time_t delivery_time);
    static void GetCallHistoryTables(const std::string& db_delivery_uuid, int delivery, std::vector<std::string>& log_tables);
    static DatabaseExistenceStatus GetCallHistoryTableList(const std::string& basic_table, std::vector<std::string>& tables_list, int limit = -1);
};

}
#endif

