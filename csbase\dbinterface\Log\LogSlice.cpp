#include <sstream>
#include "LogConnectionPool.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/Log/LogSlice.h"
#include <string.h>
#include "AkLogging.h"
#include "util.h"

namespace dbinterface{
LogSlice::LogSlice()
{

}

LogSlice::~LogSlice()
{

}

int LogSlice::GetDeliveryByTableName(const std::string& name)
{
    std::stringstream strsql;
    strsql << "SELECT Delivery FROM LogSlice "
           << "WHERE LogTableName = '"
           << name
           << "'";
    RldbPtr conn = GetLogDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        return 0;
    }
    CRldbQuery query(tmp_conn);
    query.Query(strsql.str());
    if (query.MoveToNextRow())
    {
        int delivery = 0;
        delivery = ATOI(query.GetRowData(0));
        ReleaseLogDBConn(conn);
        return delivery;
    }
    ReleaseLogDBConn(conn);
    return 0;
}

int LogSlice::GetSliceInfoByTableName(const std::string& name, LOG_SLICE_INFO& log_info)
{
    memset(&log_info, 0, sizeof(log_info));
    std::stringstream strsql;
    strsql << "SELECT Delivery,LastDelivery,MaxSaveMonth,unix_timestamp(DeliveryTime) FROM LogSlice "
           << "WHERE LogTableName = '"
           << name
           << "'";
    RldbPtr conn = GetLogDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        return -1;
    }
    CRldbQuery query(tmp_conn);
    query.Query(strsql.str());
    if (query.MoveToNextRow())
    {
        Snprintf(log_info.table_name, sizeof(log_info.table_name), name.c_str());
        log_info.delivery = ATOI(query.GetRowData(0));
        log_info.last_delivery = ATOI(query.GetRowData(1));
        log_info.max_save_month = ATOI(query.GetRowData(2));
        log_info.delivery_time = ATOI(query.GetRowData(3));
    }
    ReleaseLogDBConn(conn);
    return 0;
}


}


