#ifndef __DB_LOG_SLICE_H__
#define __DB_LOG_SLICE_H__
#include <string>
#include <memory>
#include <tuple>
#include <time.h>


typedef struct LOG_DELIVERY_T
{
    int personal_capture_delivery;
    int personal_motion_delivery;
    int call_history_delivery;
}LOG_DELIVERY;

typedef struct LOG_SLICE_INFO_T
{
    char table_name[64];
    int delivery;
    int last_delivery;
    int max_save_month; 
    time_t delivery_time;
}LOG_SLICE_INFO;

namespace dbinterface{
class LogSlice
{
public:
    LogSlice();
    ~LogSlice();
    static int GetDeliveryByTableName(const std::string& name);
    static int GetSliceInfoByTableName(const std::string& name, LOG_SLICE_INFO& log_info);
private:
};

}


#endif
