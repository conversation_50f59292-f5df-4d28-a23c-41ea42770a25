#include <sstream>
#include "ParkingLog.h"
#include <string.h>
#include "AkLogging.h"
#include <boost/algorithm/string.hpp>
#include <boost/functional/hash.hpp>
#include <boost/format.hpp>
#include <boost/crc.hpp>
#include "RldbQuery.h"
#include "Rldb.h"
#include <string.h>
#include "dbinterface/InterfaceComm.h"
#include "LogConnectionPool.h"
#include "AkcsCommonDef.h"
#include "dbinterface/SystemSettingTable.h"
#include "dbinterface/UUID.h"

namespace dbinterface
{

static const char table_parking_log[] = "ParkingLog";


ParkingLog::ParkingLog()
{
    
}

int ParkingLog::AddParkingLog(PARKING_LOG& parking_log)
{
    //插入数据构造
    std::map<std::string, std::string> str_map;
    std::string uuid;
    dbinterface::UUID::GenerateUUID(dbinterface::SystemSetting::GetServerTag(), uuid);
    str_map.emplace("UUID", uuid);
    str_map.emplace("AccountUUID", parking_log.project_uuid);
    str_map.emplace("ParkingLotUUID", parking_log.parking_lot_uuid);
    str_map.emplace("PersonalAccountUUID", parking_log.personal_account_uuid);
    str_map.emplace("OfficeCompanyUUID", parking_log.office_company_uuid);
    str_map.emplace("CommunityUnitUUID", parking_log.unit_uuid);
    str_map.emplace("LicensePlate", parking_log.license_plate);

    str_map.emplace("EntryDoor", parking_log.entry_door);
    str_map.emplace("EntryPicName", parking_log.entry_pic_name);
    str_map.emplace("EntryPicUrl", parking_log.entry_pic_url);
    str_map.emplace("EntrySPicUrl", parking_log.entry_pic_url);
    str_map.emplace("ExitPicName", parking_log.exit_pic_name);
    str_map.emplace("MAC", parking_log.mac);
    if (strlen(parking_log.entry_time) > 0) {
        str_map.emplace("EntryTime", parking_log.entry_time);
    }
    if (strlen(parking_log.exit_time) > 0) {
        str_map.emplace("ExitTime", parking_log.exit_time);
    }
    str_map.emplace("ExitDoor", parking_log.exit_door);
    if (strlen(parking_log.exit_pic_name) > 0) {
        if (parking_log.parking_time > 0) {
        str_map.emplace("sql_ExitTime", "FROM_UNIXTIME(" + std::to_string(parking_log.parking_time) + ")");
        } else {
            str_map.emplace("sql_ExitTime", "now()");//数据库系统函数，key以"sql_"为前缀
        }
    }
    
    std::map<std::string, int> int_map;
    int_map.emplace("ProjectType", int(parking_log.project_type));

    //表名构造
    std::string table_name = ParkingLog::GetLogTableName(table_parking_log);

    GET_LOG_DB_CONN_ERR_RETURN(conn, -1)
    
    int ret = conn->InsertData(table_name ,str_map, int_map);

    return ret;
}


int ParkingLog::UpdateParkingLogPicUrl(const std::string& mac, const std::string& pic_name, 
    const std::string& pic_url, const std::string& spic_url)
{
    GET_LOG_DB_CONN_ERR_RETURN(conn, -1)
    CRldbQuery query(conn.get());

    std::stringstream exit_sql;
    exit_sql << "update ParkingLog set ExitPicUrl = if (ExitPicUrl = '', '" << pic_url << "', ExitPicUrl)"
            << ", ExitSPicUrl = if (ExitSPicUrl = '', '" << spic_url << "', ExitSPicUrl)"
            << " where MAC= '"  << mac << "' and ExitPicName = '"  << pic_name << "' and ExitPicUrl = ''";

    int nRet = conn->Execute(exit_sql.str()) > 0 ? 1 : 0;
    return nRet;
}

// 数据量不大，暂时只按月分表
std::string ParkingLog::GetLogTableName(const std::string& table_name)
{
    return table_name;
}

}
