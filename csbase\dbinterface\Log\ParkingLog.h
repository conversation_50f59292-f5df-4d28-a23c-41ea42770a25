#ifndef __DB_PARKING_LOG_H__
#define __DB_PARKING_LOG_H__
#include <string>
#include <memory>
#include <vector>
#include <set>
#include <stdint.h>
#include "RldbQuery.h"
#include "Rldb.h"
#include "util.h"
#include "AkcsCommonDef.h"
#include "AkcsCommonSt.h"
#include "DclientMsgSt.h"
#include "ConnectionManager.h"

namespace dbinterface
{

class ParkingLog
{
public:
    ParkingLog();
    ~ParkingLog() = default;

    static int AddParkingLog(PARKING_LOG& parking_log);
    static int UpdateParkingLogPicUrl(const std::string& mac, const std::string& pic_name, const std::string& pic_url, const std::string& spic_url);
private: 
    static std::string GetLogTableName(const std::string& table_name);
};


}

#endif
