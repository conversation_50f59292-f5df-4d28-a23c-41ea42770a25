#include <sstream>
#include "ParkingVehicle.h"
#include <string.h>
#include "AkLogging.h"
#include <boost/algorithm/string.hpp>
#include <boost/functional/hash.hpp>
#include <boost/format.hpp>
#include <boost/crc.hpp>
#include "RldbQuery.h"
#include "Rldb.h"
#include <string.h>
#include "dbinterface/InterfaceComm.h"
#include "LogConnectionPool.h"
#include "AkcsCommonDef.h"
#include "dbinterface/SystemSettingTable.h"
#include "dbinterface/UUID.h"

namespace dbinterface
{

static const char table_parking_log[] = "ParkingVehicle";


ParkingVehicle::ParkingVehicle()
{
    
}

// 车辆进表，标识车辆在停车场内，出去时删除
int ParkingVehicle::AddParkingVehicle(PARKING_LOG& parking_log)
{
    //插入数据构造
    std::map<std::string, std::string> str_map;
    std::string uuid;
    dbinterface::UUID::GenerateUUID(dbinterface::SystemSetting::GetServerTag(), uuid);
    str_map.emplace("UUID", uuid);
    str_map.emplace("AccountUUID", parking_log.project_uuid);
    str_map.emplace("ParkingLotUUID", parking_log.parking_lot_uuid);
    str_map.emplace("PersonalAccountUUID", parking_log.personal_account_uuid);
    str_map.emplace("OfficeCompanyUUID", parking_log.office_company_uuid);
    str_map.emplace("CommunityUnitUUID", parking_log.unit_uuid);
    str_map.emplace("LicensePlate", parking_log.license_plate);
    str_map.emplace("MAC", parking_log.mac);
    if (parking_log.parking_time > 0) {
        str_map.emplace("sql_EntryTime", "FROM_UNIXTIME(" + std::to_string(parking_log.parking_time) + ")");
    } else {
        str_map.emplace("sql_EntryTime", "now()");//数据库系统函数，key以"sql_"为前缀
    }
    str_map.emplace("EntryDoor", parking_log.entry_door);
    str_map.emplace("EntryPicName", parking_log.parking_pic_name);
    
    std::map<std::string, int> int_map;
    int_map.emplace("ProjectType", int(parking_log.project_type));

    //表名构造
    std::string table_name = "ParkingVehicle";

    GET_DB_CONN_ERR_RETURN(conn, -1)
    
    int ret = conn->InsertData(table_name ,str_map, int_map);

    return ret;
}

int ParkingVehicle::DelParkingVehicle(PARKING_LOG& parking_log)
{
    GET_DB_CONN_ERR_RETURN(conn, -1)
    CRldbQuery query(conn.get());

    std::stringstream streamsql;
    if (strlen(parking_log.parking_lot_uuid) == 0 && strlen(parking_log.license_plate) == 0) {
        AK_LOG_WARN << "DelParkingVehicle failed.";
        return -1;
    }
    streamsql << "delete from ParkingVehicle where ParkingLotUUID = '"
              << parking_log.parking_lot_uuid << "' and LicensePlate = '" << parking_log.license_plate << "'";
    
    conn->Execute(streamsql.str());
    return 0;
}

int ParkingVehicle::UpdateParkingVehiclePicUrl(const std::string& mac, const std::string& pic_name, 
    const std::string& pic_url, const std::string& spic_url)
{
    GET_DB_CONN_ERR_RETURN(conn, -1)
    CRldbQuery query(conn.get());

    std::stringstream entry_sql;
    entry_sql << "update ParkingVehicle set EntryPicUrl = if (EntryPicUrl = '', '" << pic_url << "', EntryPicUrl)"
        << ", EntrySPicUrl = if (EntrySPicUrl = '', '" << spic_url << "', EntrySPicUrl)"
        << " where MAC= '"  << mac << "' and EntryPicName = '"  << pic_name << "' and EntryPicUrl = ''";

    int nRet = conn->Execute(entry_sql.str()) > 0 ? 1 : 0;
    return nRet;
}

DatabaseExistenceStatus ParkingVehicle::ParkingVehicleExist(const std::string& parking_lot_uuid, const std::string& license_plate, PARKING_LOG& parking_log)
{
    GET_DB_CONN_ERR_RETURN(conn, DatabaseExistenceStatus::QUERY_ERROR);
    CRldbQuery query(conn.get());
    std::stringstream stream_sql;
    stream_sql << "select count(*),EntryTime,EntryDoor,EntryPicName,EntryPicUrl,EntrySPicUrl from ParkingVehicle where ParkingLotUUID = '" << parking_lot_uuid << "' and LicensePlate = '" << license_plate << "'";
    
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        if (ATOI(query.GetRowData(0)) > 0)
        {
            Snprintf(parking_log.entry_time, sizeof(parking_log.entry_time), query.GetRowData(1));
            Snprintf(parking_log.entry_door, sizeof(parking_log.entry_door), query.GetRowData(2));
            Snprintf(parking_log.entry_pic_name, sizeof(parking_log.entry_pic_name), query.GetRowData(3));
            Snprintf(parking_log.entry_pic_url, sizeof(parking_log.entry_pic_url), query.GetRowData(4));
            Snprintf(parking_log.entry_spic_url, sizeof(parking_log.entry_spic_url), query.GetRowData(5));
            return DatabaseExistenceStatus::EXIST;
        }
    }
    return DatabaseExistenceStatus::NOT_EXIST;
}

std::string ParkingVehicle::GetLogTableName(const std::string& table_name)
{
    return table_name;
}

}
