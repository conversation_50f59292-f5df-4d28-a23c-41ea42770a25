#include <sstream>
#include "PersonalMotion.h"
#include <string.h>
#include "AkLogging.h"
#include <boost/algorithm/string.hpp>
#include <boost/functional/hash.hpp>
#include <boost/format.hpp>
#include <boost/crc.hpp>
#include "dbinterface/Log/LogSlice.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include <string.h>
#include "dbinterface/InterfaceComm.h"
#include "LogConnectionPool.h"
#include "AkcsCommonDef.h"

namespace dbinterface
{

static const char table_personnal_motion[] = "PersonalMotion";


PersonalMotion::PersonalMotion()
{
    
}

int PersonalMotion::AddPersonalMotion(PERSONNAL_CAPTURE& personnal_capture, int delivery)
{
    if (delivery == 0)
    {
        AK_LOG_WARN << "AddPersonalMotion failed: delivery is 0";
        return -1;
    }

    //插入数据构造
    std::map<std::string, std::string> str_map;
    str_map.emplace("MAC", personnal_capture.mac);
    if(strlen(personnal_capture.dev_uuid) > 0){
        str_map.emplace("DevicesUUID", personnal_capture.dev_uuid);
    }
    str_map.emplace("PicName", personnal_capture.picture_name);
    str_map.emplace("sql_CaptureTime", "now()");
    str_map.emplace("SipAccount", personnal_capture.sip_account);
    str_map.emplace("Location", personnal_capture.location);
    str_map.emplace("Node", personnal_capture.account);
    str_map.emplace("VideoRecordName", personnal_capture.video_record_name);
    
    std::map<std::string, int> int_map;
    int_map.emplace("MngAccountID", personnal_capture.manager_id);
    int_map.emplace("DevType", personnal_capture.device_type);
    int_map.emplace("MngType", personnal_capture.manager_type);
    int_map.emplace("DetectionType", personnal_capture.detection_type);
    int_map.emplace("DetectionInfo", personnal_capture.detection_info);

    //表名构造
    std::string table_name = PersonalMotion::GetLogTableName(table_personnal_motion, personnal_capture.db_delivery_uuid, delivery);

    GET_LOG_DB_CONN_ERR_RETURN(conn, -1)
    
    int ret = conn->InsertData(table_name ,str_map, int_map);

    std::stringstream sql3;
    sql3 << "SELECT last_insert_id()";

    CRldbQuery query(conn.get());
    query.Query(sql3.str());
    if (query.MoveToNextRow())
    {
        personnal_capture.id = ATOI(query.GetRowData(0));
    }

    return ret;
}

std::string PersonalMotion::GetLogTableName(const std::string& table_name, const std::string& db_delivery_uuid, int delivery)
{
    //hash取模
    uint32_t uuid_hash = crc32_hash(db_delivery_uuid);
    int hash = uuid_hash % delivery;

    //表名构造
    return table_name + "_" + std::to_string(hash);
}

}
