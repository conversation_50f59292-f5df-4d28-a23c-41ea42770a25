#ifndef __DB_PERSONAL_MOTION_H__
#define __DB_PERSONAL_MOTION_H__
#include <string>
#include <memory>
#include <vector>
#include <set>
#include <stdint.h>
#include "RldbQuery.h"
#include "Rldb.h"
#include "util.h"
#include "AkcsCommonDef.h"
#include "AkcsCommonSt.h"
#include "dbinterface/Log/LogSlice.h"
#include "DclientMsgSt.h"
#include "ConnectionManager.h"

namespace dbinterface
{

class PersonalMotion
{
public:
    PersonalMotion();
    ~PersonalMotion() = default;

    static int AddPersonalMotion(PERSONNAL_CAPTURE& personnal_capture, int delivery);
    
private: 
    static std::string GetLogTableName(const std::string& table_name, const std::string& project_uuid, int delivery);
};


}

#endif
