#include <sstream>
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/InterfaceComm.h"
#include "MacPool.h"
#include <string.h>
#include "AkLogging.h"
#include "util.h"

namespace dbinterface{
MacPool::MacPool()
{

}

MacPool::~MacPool()
{

}

bool MacPool::CheckAuthcodeExist(const std::string &mac, std::string& authcode)
{
    bool ret = false;
    RldbPtr dbconn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = dbconn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return false;
    }

    CRldbQuery query(tmp_conn);
    std::stringstream sql;

    sql << "/*master*/select Authcode from MacPool where MAC = '";
    sql<< mac;
    sql << "'";

    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        ret = true;
        authcode = query.GetRowData(0);
    }

    ReleaseDBConn(dbconn);

    return ret;
}
//传入通过","分隔的多个MAC地址字符串，返回挂在当前服务器下的MAC地址，同为","分隔的字符串
bool MacPool::FindExistDevByMac(const std::string &macs, std::string& dev_mac_list)
{
    if(macs.size() == 0)
    {
        AK_LOG_WARN << "mac list is empty";
        return false;
    }
    dev_mac_list = "";
    RldbPtr dbconn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = dbconn.get();
    if(NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return false;
    }

    std::set<std::string> mac_list;
    SplitString(macs, ",", mac_list);

    std::string mac_query;
    mac_query = ListToSeparatedFormatString(mac_list);

    if(mac_query.size() == 0)
    {
        AK_LOG_WARN << "pass wrong mac list";
        return false;
    }
    std::stringstream sql;
    sql << "select MAC from Devices where MAC in (";
    sql << mac_query;
    sql << ") ";
    sql << "union all select MAC from PersonalDevices where MAC in (";
    sql << mac_query;
    sql << ")";

    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        std::string tmp_mac = query.GetRowData(0);
        dev_mac_list.append(tmp_mac);
        dev_mac_list.append(",");
    }
    if (!dev_mac_list.size() == 0 && dev_mac_list.back() == ',')
    {
        dev_mac_list.pop_back();
    }
    ReleaseDBConn(dbconn);
    return true;
}
//传入通过","分隔的多个MAC地址字符串,删除MacPool中已存在的MAC-AuthCode映射
bool MacPool::DelAuthcodeByMac(const std::string &macs)
{
    if(macs.size() == 0)
    {
        AK_LOG_WARN << "mac list is empty";
        return false;
    }
    RldbPtr dbconn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = dbconn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return false;
    }
    //多个mac通过","分隔
    std::set<std::string> mac_list;
    SplitString(macs, ",", mac_list);

    std::string mac_query;
    mac_query = ListToSeparatedFormatString(mac_list);

    if(mac_query.size() == 0)
    {
        AK_LOG_WARN << "pass wrong mac list";
        return false;
    }
    std::stringstream sql;
    sql << "delete from MacPool where MAC in (";
    sql << mac_query;
    sql << ")";

    AK_LOG_INFO << "Execute Delete Operation:" << sql.str();

    if (tmp_conn->Execute(sql.str()) < 0)
    {
        AK_LOG_WARN << "Delete authcode failed. ";
        ReleaseDBConn(dbconn);
        return false;
    }
    ReleaseDBConn(dbconn);
    return true;
}

}


