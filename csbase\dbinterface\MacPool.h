#ifndef __MACPOOL_H__
#define __MACPOOL_H__
#include <string>
#include <memory>
#include <tuple>

namespace dbinterface{
class MacPool
{
public:
    MacPool();
    ~MacPool();
    static bool CheckAuthcodeExist(const std::string &mac, std::string& authcode);
    static bool FindExistDevByMac(const std::string &mac, std::string& dev_mac_list);
    static bool DelAuthcodeByMac(const std::string &mac); 
private:
};

}


#endif
