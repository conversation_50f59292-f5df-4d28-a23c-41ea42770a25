#include <sstream>
#include "InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "ManageFeature.h"
#include "FeaturePlan.h"
#include <string.h>
#include "AkLogging.h"

namespace dbinterface
{

ManageFeature::ManageFeature(unsigned int mng_id)
{
    mng_id_ = mng_id;
    plan_id_ = 0; //旧小区
    item_ = 0;
    init();
}


ManageFeature::~ManageFeature()
{

}


void ManageFeature::init()
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }

    std::stringstream sql;
    sql << "SELECT FeatureID FROM  ManageFeature  where AccountID= " << mng_id_;
           
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        plan_id_ = ATOI(query.GetRowData(0));
    }
    else
    {
       AK_LOG_WARN << "can not found FeaturePlan id : " << plan_id_; 
    }
    if (plan_id_ > 0)
    {
        std::stringstream sql;
        sql << "SELECT Item FROM  FeaturePlan  where ID= " << plan_id_;
        query.Query(sql.str());
        if (query.MoveToNextRow())
        {
            item_ = ATOI(query.GetRowData(0));
        }        
    }
    
    ReleaseDBConn(conn);
    return;
}

int ManageFeature::IsEnableAllowCreatePin()
{
    if (plan_id_ == 0)
    {
        return 1;
    }

    return SwitchHandle(item_, FeaturePlan::PLAN_ITEM_TYPE::AllowPin);
}

        
int ManageFeature::IsEnableAllowCreateTmpkey()
{
    if (plan_id_ == 0)
    {
        return 1;
    }

    return SwitchHandle(item_, FeaturePlan::PLAN_ITEM_TYPE::AllowTempKey); 
}

int ManageFeature::IsEnableDelivery()
{
    if (plan_id_ == 0)
    {
        return 0;
    }

    return SwitchHandle(item_, FeaturePlan::PLAN_ITEM_TYPE::Delivery); 
}

int ManageFeature::IsEnableFamilyAppControl()
{
    if (plan_id_ == 0)
    {
        return 0;
    }

    return SwitchHandle(item_, FeaturePlan::PLAN_ITEM_TYPE::FamilyAppControl); 
}

}

