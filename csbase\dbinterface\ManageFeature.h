#ifndef __MANAGEFEATURE_PLAN_H__
#define __MANAGEFEATURE_PLAN_H__
#include <string>
#include <memory>
#include <vector>

namespace dbinterface
{

class ManageFeature
{
public:
    ManageFeature(unsigned int mngid);
    ~ManageFeature();
        
    int IsEnableAllowCreatePin();
    int IsEnableAllowCreateTmpkey();
    int IsEnableDelivery();
    int IsEnableFamilyAppControl();   
private:
    void init();
    unsigned int plan_id_;
    unsigned int item_;
    unsigned int mng_id_;
};

typedef std::shared_ptr<ManageFeature> ManageFeaturePtr;


}
#endif
