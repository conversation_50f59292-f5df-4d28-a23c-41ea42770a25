#include <sstream>
#include <string.h>
#include <set>
#include <boost/algorithm/string.hpp>
#include "util.h"
#include "Message.h"
#include "AkLogging.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "AkcsCommonDef.h"
#include "ConnectionManager.h"

namespace dbinterface
{

Message::Message()
{

}


//获取到最新的发送text msg
int Message::GetTextMsgSendList(PerMsgSendList& message_list)
{
    int finish = 1;
    char sql[1024] = {0};
    ::snprintf(sql, 1024, "/*master*/select M.<PERSON>, M.Content, M.CreateTime, A.Account, M.AccountID, N.ClientType, N.Account, N.ID, M.Type, M.ID \
                           from Message M join MessageAccountList N on M.ID = N.MessageID \
                           join Account A on M.AccountID = A.ID \
                           where M.Status = 0 order by M.ID desc limit 1000");

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* pTmpConn = conn.get();
    if (NULL == pTmpConn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return finish;
    }
    CRldbQuery query(pTmpConn);
    std::string sql2 = sql;
    query.Query(sql2);
    std::vector<int> text_message_ids;
    PersoanlMessageSend send_text_msg;
    memset(&send_text_msg.text_message, 0, sizeof(send_text_msg.text_message));
    while (query.MoveToNextRow())
    {
        finish = 0;
        Snprintf(send_text_msg.text_message.title, sizeof(send_text_msg.text_message.title),  query.GetRowData(0));
        Snprintf(send_text_msg.text_message.content, sizeof(send_text_msg.text_message.content),  query.GetRowData(1));
        Snprintf(send_text_msg.text_message.time, sizeof(send_text_msg.text_message.time),  query.GetRowData(2));
        Snprintf(send_text_msg.text_message.from, sizeof(send_text_msg.text_message.from),  query.GetRowData(3));

        send_text_msg.per_manager_id = ATOI(query.GetRowData(4));
        send_text_msg.client_type = ATOI(query.GetRowData(5));
        send_text_msg.account = query.GetRowData(6);
        send_text_msg.text_message.id = ATOI(query.GetRowData(7));
        snprintf(send_text_msg.text_message.type, sizeof(send_text_msg.text_message.type), "%s", query.GetRowData(8));
        
        message_list.push_back(send_text_msg);
        text_message_ids.push_back(ATOI(query.GetRowData(9)));
        memset(&send_text_msg.text_message, 0, sizeof(send_text_msg.text_message));
    }
    
    if (text_message_ids.size() > 0)
    {
        std::string ids = ListToSeparatedFormatString(text_message_ids);
        //刷新消息发送与否的状态
        std::stringstream stream_sql;
        stream_sql << "update Message set Status = 1 where ID in(" << ids << ")";
        conn->Execute(stream_sql.str());        
    }
    ReleaseDBConn(conn);
    return finish;

}

int Message::GetTextMsgLatestIDAndUnReadNum(const std::string& uid, const std::string& node, int lastread_id, int manager_id, int& nLastID,  int& nUnReadNum)
{    
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
    std::stringstream sql;
    sql << "SELECT count(*) FROM MessageAccountList WHERE Account = '" << uid << "' AND Status = 0 AND ClientType = " << PersoanlMessageSend::APP_SEND;        
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        nUnReadNum = ATOI(query.GetRowData(0));
    }
    
    ReleaseDBConn(conn);
    return 0;
}

int Message::AddTextMsgByMngDev(const std::string& nodes, const std::string& titil, 
        const std::string& msg, int manager_id, PerMsgSendList& text_messages, int is_aws)
{
    unsigned int msgid;
    unsigned int msg_list_id;
    std::stringstream sql_msg;
    int ret = -1;    
    PersoanlMessageSend send_text_msg;    
    memset(&send_text_msg.text_message, 0, sizeof(send_text_msg.text_message));
    std::string titil2 = titil;
    if (titil2.length() == 0)
    {
        titil2 = "Security Center";
    }
    Snprintf(send_text_msg.text_message.title, sizeof(send_text_msg.text_message.title),  titil2.c_str());
    Snprintf(send_text_msg.text_message.content, sizeof(send_text_msg.text_message.content),  msg.c_str());
    send_text_msg.per_manager_id = manager_id;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return ret;
    }

    if(is_aws)
    {
        std::vector<std::string> nodes_vec;
        SplitString(nodes, ";", nodes_vec);        
        for (const auto& node : nodes_vec)
        {            
            send_text_msg.client_type = PersoanlMessageSend::DEV_SEND;
            send_text_msg.account = node;
            text_messages.push_back(send_text_msg);  
            send_text_msg.client_type = PersoanlMessageSend::APP_SEND;
            send_text_msg.account = node;
            text_messages.push_back(send_text_msg); 

            std::vector<std::string> accounts;      
            sql_msg.str("");
            sql_msg << "SELECT P1.Account FROM PersonalAccount P1 JOIN PersonalAccount P2 ON P1.ParentID=P2.ID "
                    << "WHERE P2.Account = '" << node << "'"
                    << " AND P1.Role IN (" << ACCOUNT_ROLE_PERSONNAL_ATTENDANT << "," << ACCOUNT_ROLE_COMMUNITY_ATTENDANT << ")"; 
            CRldbQuery query(tmp_conn);
            query.Query(sql_msg.str());
            while (query.MoveToNextRow())
            {
                accounts.push_back(query.GetRowData(0));
            }       
            for(const auto& account : accounts)
            {            
                send_text_msg.client_type = PersoanlMessageSend::APP_SEND;
                send_text_msg.account = account;
                text_messages.push_back(send_text_msg);
            }
        }

        ReleaseDBConn(conn);
        //GetMsgControlInstance()->PostAwsInsertMessageHttpReq(text_messages);
        return 0;        
    }

    tmp_conn->BeginTransAction();
    //modify.******** by chenzhx Status=1 代表已经发送。不然当pm再发送消息时候会继续处理
    //插入数据构造
    std::map<std::string, std::string> str_map;
    str_map.emplace("Title", titil2);
    str_map.emplace("Content", msg);
    str_map.emplace("sql_CreateTime", "now()");

    std::map<std::string, int> int_map;
    int_map.emplace("AccountID", manager_id);
    int_map.emplace("Status", 1);

    std::string table_name = "Message";

    ret = conn->InsertData(table_name, str_map, int_map);

    if(ret < 0)
    {
        tmp_conn->TransActionRollback();
        ReleaseDBConn(conn);
        return ret;
    }

    sql_msg.str("");
    sql_msg << "SELECT last_insert_id()";

    CRldbQuery query(tmp_conn);
    query.Query(sql_msg.str());
    if (query.MoveToNextRow())
    {
        msgid = ATOI(query.GetRowData(0));
    }
    
    std::vector<std::string> nodes_vec;
    SplitString(nodes, ";", nodes_vec);

    for (auto& node : nodes_vec)
    {
        //插入设备
        sql_msg.str("");
        sql_msg << "insert into MessageAccountList(ClientType,Account,MessageID) values('"
                << PersoanlMessageSend::DEV_SEND  << "','"
                << node << "',"
                << msgid  << ");";
        ret = conn->Execute(sql_msg.str()) > 0 ? 0 : -1;
        if(ret < 0)
        {
            tmp_conn->TransActionRollback();
            ReleaseDBConn(conn);
            return ret;
        }               
        send_text_msg.client_type = PersoanlMessageSend::DEV_SEND;
        send_text_msg.account = node;
        text_messages.push_back(send_text_msg);    

        //插入主账号
        sql_msg.str("");
        sql_msg << "insert into MessageAccountList(ClientType,Account,MessageID) values('"
                << PersoanlMessageSend::APP_SEND  << "','"
                << node << "',"
                << msgid  << ");";
        ret = conn->Execute(sql_msg.str()) > 0 ? 0 : -1;
        if(ret < 0)
        {
            tmp_conn->TransActionRollback();
            ReleaseDBConn(conn);
            return ret;
        }

        sql_msg.str("");
        sql_msg << "SELECT last_insert_id()";

        query.Query(sql_msg.str());
        if (query.MoveToNextRow())
        {
            msg_list_id = ATOI(query.GetRowData(0));       
            send_text_msg.text_message.id = msg_list_id;
        }
    
        send_text_msg.client_type = PersoanlMessageSend::APP_SEND;
        send_text_msg.account = node;
        text_messages.push_back(send_text_msg);    

        //插入从账号
        std::vector<std::string> accounts;      
        sql_msg.str("");        
        sql_msg << "SELECT P1.Account FROM PersonalAccount P1 JOIN PersonalAccount P2 ON P1.ParentID=P2.ID "
                << "WHERE P2.Account = '" << node << "'"
                << " AND P1.Role IN (" << ACCOUNT_ROLE_PERSONNAL_ATTENDANT << "," << ACCOUNT_ROLE_COMMUNITY_ATTENDANT << ")";

        CRldbQuery query(tmp_conn);
        query.Query(sql_msg.str());
        while (query.MoveToNextRow())
        {
            accounts.push_back(query.GetRowData(0));
        }       
        for(auto& account : accounts)
        {
            sql_msg.str("");
            sql_msg << "insert into MessageAccountList(ClientType,Account,MessageID) values('"
                    << PersoanlMessageSend::APP_SEND  << "','"
                    << account << "',"
                    << msgid  << ");";
            ret = conn->Execute(sql_msg.str()) > 0 ? 0 : -1;
            if(ret < 0)
            {
                tmp_conn->TransActionRollback();
                ReleaseDBConn(conn);
                return ret;
            }

            sql_msg.str("");
            sql_msg << "SELECT last_insert_id()";

            query.Query(sql_msg.str());
            if (query.MoveToNextRow())
            {
                msg_list_id = ATOI(query.GetRowData(0));       
                send_text_msg.text_message.id = msg_list_id;
            }
            
            send_text_msg.client_type = PersoanlMessageSend::APP_SEND;
            send_text_msg.account = account;
            text_messages.push_back(send_text_msg);   
        }
    }

    tmp_conn->EndTransAction();
    //释放数据库连接
    ReleaseDBConn(conn);
    return ret;
}

int Message::AddOfficeTextMsgByMngDev(const std::string& nodes, const std::string& titil, 
        const std::string& msg, int manager_id, PerMsgSendList& text_messages, int is_aws)
{
    unsigned int msgid;
    unsigned int msg_list_id;
    std::stringstream sql_msg;
    int ret = -1;    
    PersoanlMessageSend send_text_msg;    
    memset(&send_text_msg.text_message, 0, sizeof(send_text_msg.text_message));
    std::string titil2 = titil;
    if (titil2.length() == 0)
    {
        titil2 = "Security Center";
    }
    Snprintf(send_text_msg.text_message.title, sizeof(send_text_msg.text_message.title),  titil2.c_str());
    Snprintf(send_text_msg.text_message.content, sizeof(send_text_msg.text_message.content),  msg.c_str());
    send_text_msg.per_manager_id = manager_id;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return ret;
    }

    if(is_aws)
    {
        std::vector<std::string> nodes_vec;
        SplitString(nodes, ";", nodes_vec);        
        for (const auto& node : nodes_vec)
        {            
            send_text_msg.client_type = PersoanlMessageSend::DEV_SEND;
            send_text_msg.account = node;
            text_messages.push_back(send_text_msg);  
            send_text_msg.client_type = PersoanlMessageSend::APP_SEND;
            send_text_msg.account = node;
            text_messages.push_back(send_text_msg); 
        }

        ReleaseDBConn(conn);
        //GetMsgControlInstance()->PostAwsInsertMessageHttpReq(text_messages);
        return 0;        
    }

    tmp_conn->BeginTransAction();

    //modify.******** by chenzhx Status=1 代表已经发送。不然当pm在发送消息时候会继续处理
    //插入数据构造
    std::map<std::string, std::string> str_map;
    str_map.emplace("Title", titil2);
    str_map.emplace("Content", msg);
    str_map.emplace("sql_CreateTime", "now()");

    std::map<std::string, int> int_map;
    int_map.emplace("AccountID", manager_id);
    int_map.emplace("Status", 1);

    std::string table_name = "Message";

    ret = conn->InsertData(table_name, str_map, int_map);
    if(ret < 0)
    {
        tmp_conn->TransActionRollback();
        ReleaseDBConn(conn);
        return ret;
    }

    sql_msg.str("");
    sql_msg << "SELECT last_insert_id()";

    CRldbQuery query(tmp_conn);
    query.Query(sql_msg.str());
    if (query.MoveToNextRow())
    {
        msgid = ATOI(query.GetRowData(0));
    }
    
    std::vector<std::string> nodes_vec;
    SplitString(nodes, ";", nodes_vec);

    for (auto& node : nodes_vec)
    {
        //插入设备
        sql_msg.str("");
        sql_msg << "insert into MessageAccountList(ClientType,Account,MessageID) values('"
                << PersoanlMessageSend::DEV_SEND  << "','"
                << node << "',"
                << msgid  << ");";
        ret = conn->Execute(sql_msg.str()) > 0 ? 0 : -1;
        if(ret < 0)
        {
            tmp_conn->TransActionRollback();
            ReleaseDBConn(conn);
            return ret;
        }               

        sql_msg.str("");
        sql_msg << "SELECT last_insert_id()";

        query.Query(sql_msg.str());
        if (query.MoveToNextRow())
        {
            msg_list_id = ATOI(query.GetRowData(0));
            send_text_msg.text_message.id = msg_list_id;
        }
        
        send_text_msg.client_type = PersoanlMessageSend::DEV_SEND;
        send_text_msg.account = node;
        text_messages.push_back(send_text_msg);    

        //插入主账号
        sql_msg.str("");
        sql_msg << "insert into MessageAccountList(ClientType,Account,MessageID) values('"
                << PersoanlMessageSend::APP_SEND  << "','"
                << node << "',"
                << msgid  << ");";
        ret = conn->Execute(sql_msg.str()) > 0 ? 0 : -1;
        if(ret < 0)
        {
            tmp_conn->TransActionRollback();
            ReleaseDBConn(conn);
            return ret;
        }

        sql_msg.str("");
        sql_msg << "SELECT last_insert_id()";

        query.Query(sql_msg.str());
        if (query.MoveToNextRow())
        {
            msg_list_id = ATOI(query.GetRowData(0));
            send_text_msg.text_message.id = msg_list_id;
        }
        send_text_msg.client_type = PersoanlMessageSend::APP_SEND;
        send_text_msg.account = node;
        text_messages.push_back(send_text_msg);    
    }

    tmp_conn->EndTransAction();
    //释放数据库连接
    ReleaseDBConn(conn);
    return ret;
}

int Message::AddGroupTextMsg(int message_type, const CommPerTextMessage& comm_text_msg, const ResidentDeviceList& dev_list,
     const ResidentPerAccount& master_account, const ResidentPerAccountList& sub_account_list, PerTextMessageSendList& text_messages, bool need_notify_dev)
{
    PerTextMessageSend send_text_msg;
    //通用部分
    Snprintf(send_text_msg.comm_message.title, sizeof(send_text_msg.comm_message.title), comm_text_msg.title);
    Snprintf(send_text_msg.comm_message.content, sizeof(send_text_msg.comm_message.content), comm_text_msg.content);
    Snprintf(send_text_msg.comm_message.extension_field, sizeof(send_text_msg.comm_message.extension_field), comm_text_msg.extension_field);
    send_text_msg.comm_message.project_type = comm_text_msg.project_type;
    send_text_msg.comm_message.msg_type = (MessageType2)message_type;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if(NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    tmp_conn->BeginTransAction();
    std::map<std::string, std::string> strMap;
    strMap.emplace("Title", send_text_msg.comm_message.title);
    strMap.emplace("Content", send_text_msg.comm_message.content);
    strMap.emplace("ExtensionField", send_text_msg.comm_message.extension_field);
    strMap.emplace("sql_CreateTime", "now()");

    std::map<std::string, int> intMap;
    intMap.emplace("AccountID", 0); //非PM消息，AccountID置为0
    intMap.emplace("Status", 1); //置为已发送，防止重复发送
    intMap.emplace("Type", message_type);

    std::string table_name = "Message"; //表名

    int ret = tmp_conn->InsertData(table_name, strMap, intMap);
    if(ret < 0)
    {
        tmp_conn->TransActionRollback();
        ReleaseDBConn(conn);
        AK_LOG_WARN << "Insert into Message failed";
        return ret;
    }
    std::stringstream str_sql;
    str_sql << "SELECT last_insert_id()";
                
    CRldbQuery query(tmp_conn);
    query.Query(str_sql.str());
    int message_id;
    if (query.MoveToNextRow())
    {
        message_id = ATOI(query.GetRowData(0));
    }
    //插入设备
    if (need_notify_dev)
    {
        ret = InsertMessageAccountList(tmp_conn, PersoanlMessageSend::DEV_SEND, message_id, master_account.account);
        if(ret < 0)
        {
            tmp_conn->TransActionRollback();
            ReleaseDBConn(conn);
            AK_LOG_WARN << "Dev Insert into MessageAccountList failed";
            return ret;
        }
    }
    int message_list_id = 0;
    for(const auto& dev : dev_list)
    {
        if(dev.dev_type == DEVICE_TYPE_INDOOR) //只通知室内机
        {
            query.Query(str_sql.str());
            if(query.MoveToNextRow())
            {
                message_list_id = ATOI(query.GetRowData(0));
                send_text_msg.id = message_list_id;
            }
            send_text_msg.client_type = PersoanlMessageSend::DEV_SEND;
            Snprintf(send_text_msg.uuid, sizeof(send_text_msg.uuid), dev.uuid);
            text_messages.push_back(send_text_msg);
        }
    }

    //插入主账号
    ret = InsertMessageAccountList(tmp_conn, PersoanlMessageSend::APP_SEND, message_id, master_account.account);
    if(ret < 0)
    {
        tmp_conn->TransActionRollback();
        ReleaseDBConn(conn);
        AK_LOG_WARN << "main account Insert into MessageAccountList failed, node: " << master_account.account;
        return ret;
    }
    query.Query(str_sql.str());
    if(query.MoveToNextRow())
    {
        message_list_id = ATOI(query.GetRowData(0));
        send_text_msg.id = message_list_id;
    }
    send_text_msg.client_type = PersoanlMessageSend::APP_SEND;
    Snprintf(send_text_msg.uuid, sizeof(send_text_msg.uuid), master_account.uuid);
    text_messages.push_back(send_text_msg);

    for(const auto& sub_account : sub_account_list)
    {
        ret = InsertMessageAccountList(tmp_conn, PersoanlMessageSend::APP_SEND, message_id, sub_account.account);
        if(ret < 0)
        {
            tmp_conn->TransActionRollback();
            ReleaseDBConn(conn);
            AK_LOG_WARN << "sub account Insert into MessageAccountList failed, account: " << sub_account.account;
            return ret;
        }
        query.Query(str_sql.str());
        if(query.MoveToNextRow())
        {
            message_list_id = ATOI(query.GetRowData(0));
            send_text_msg.id = message_list_id;
        }
        send_text_msg.client_type = PersoanlMessageSend::APP_SEND;
        Snprintf(send_text_msg.uuid, sizeof(send_text_msg.uuid), sub_account.uuid);
        text_messages.push_back(send_text_msg);
    }
    tmp_conn->EndTransAction();
    ReleaseDBConn(conn);
    return ret;
}


// 一人多套房,查所有站点的未读消息数,且消息时间要在该账号的创建时间之后
int Message::getUnReadMessageCount(const std::string& user_info_uuid, const std::string& create_time)
{
    ResidentPerAccountList account_list;
    if (0 == ResidentPersonalAccount::GetAccountListByUserInfoUUID(user_info_uuid, account_list)) 
    {
        RldbPtr conn = GetDBConnPollInstance()->GetConnection();
        CRldb* tmp_conn = conn.get();
        if (NULL == tmp_conn)
        {
           AK_LOG_WARN << "Get DB conn failed.";
           return -1;
        }

        std::string uuids_str;
        std::string accounts_str;
        int unread_message_num = 0;
        
        for (const auto &account : account_list)
        {
            uuids_str += "'" + std::string(account.uuid) + "'" + ",";
            accounts_str += "'" + std::string(account.account) + "'" + ",";
        }
        uuids_str.pop_back(); // 删除最后一个逗号
        accounts_str.pop_back(); // 删除最后一个逗号

        // 查询未读notice数,且消息时间要在该账号的创建时间之后
        CRldbQuery query(tmp_conn);
        std::stringstream stream_sql; 
        stream_sql << "select count(*) as num from MessageAccountList L left join Message M on L.MessageID = M.ID where L.Account in ( " << accounts_str << ") "
                   << "and L.Status = 0 and L.ClientType = " << PersoanlMessageSend::APP_SEND << " and M.CreateTime > '" << create_time << "'";
        
        query.Query(stream_sql.str());
        if (query.MoveToNextRow())
        {
           unread_message_num += ATOI(query.GetRowData(0));
        }
        
        // 查询未读语音留言数,且消息时间要在该账号的创建时间之后
        std::stringstream stream_sql1; 
        stream_sql1 << "select count(*) as num from PersonalVoiceMsgList WHERE PersonalAccountUUID in (" << uuids_str << ") and Status = 0 and CreateTime > '" << create_time << "'";
        query.Query(stream_sql1.str());
        if (query.MoveToNextRow())
        {
           unread_message_num += ATOI(query.GetRowData(0));
        }
        
        ReleaseDBConn(conn);
        return unread_message_num;
    }
    else
    {
        return 0;
    }    
}

int Message::InsertDeliveryMsg(const std::string& account, const std::string& content, int type)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    int ret = 0;
    tmp_conn->BeginTransAction();
    std::map<std::string, int> intMap;
    std::map<std::string, std::string> strMap;

    // 插入Message表
    strMap.clear();
    strMap.emplace("Title", "Delivery");
    strMap.emplace("Content", content);

    intMap.clear();
    intMap.emplace("Status", SEND_SCUCCESS);
    intMap.emplace("Type", type);

    ret = tmp_conn->InsertData("Message", strMap, intMap);
    if(ret < 0)
    {
        tmp_conn->TransActionRollback();
        ReleaseDBConn(conn);
        AK_LOG_WARN << "Failed to insert Message, ret:" << ret;
        return -1;
    }

    int msgid = -1;
    int msg_list_id = -1;
    CRldbQuery query(tmp_conn);
    query.Query("SELECT last_insert_id()");
    if (query.MoveToNextRow())
    {
        msgid = ATOI(query.GetRowData(0));
    }
    else{
        AK_LOG_WARN << "Failed to SELECT last_insert_id" ;
        return -1;
    }

    // 插入MessageAccountList表
    tmp_conn->BeginTransAction();

    strMap.clear();
    strMap.emplace("Account", account);

    intMap.clear();
    intMap.emplace("ClientType", PersoanlMessageSend::APP_SEND);
    intMap.emplace("MessageID", msgid);

    ret = tmp_conn->InsertData("MessageAccountList", strMap, intMap);
    if(ret < 0)
    {
        tmp_conn->TransActionRollback();
        ReleaseDBConn(conn);
        AK_LOG_WARN << "Failed to insert MessageAccountList, ret:" << ret;
        return -1;
    }

    query.Query("SELECT last_insert_id()");
    if (query.MoveToNextRow())
    {
        msg_list_id = ATOI(query.GetRowData(0));
    }
    else{
        AK_LOG_WARN << "Failed to SELECT last_insert_id" ;
        return -1;
    }
    
    tmp_conn->EndTransAction();
    ReleaseDBConn(conn);
    return msg_list_id;
}

int Message::InsertMessage(const std::string& account, const std::string& content)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    int ret = 0;
    tmp_conn->BeginTransAction();
    std::map<std::string, int> sql_int_map;
    std::map<std::string, std::string> sql_srt_map;

    //插入message
    sql_srt_map.clear();
    sql_srt_map.emplace("Title", "TmpKey Used");
    sql_srt_map.emplace("Content", content);

    sql_int_map.clear();
    sql_int_map.emplace("Type", TMPKEY_MSG);
    sql_int_map.emplace("Status", SEND_SCUCCESS);
    
    ret = tmp_conn->InsertData("Message", sql_srt_map, sql_int_map);
    if (ret < 0)
    {
        AK_LOG_WARN << "Failed to insert Message, ret:" << ret;
        tmp_conn->TransActionRollback();
        return -1;
    }

    int msgid;
    int msg_list_id;
    CRldbQuery query(tmp_conn);
    query.Query("SELECT last_insert_id()");
    if (query.MoveToNextRow())
    {
        msgid = ATOI(query.GetRowData(0));
    }
    else{
        AK_LOG_WARN << "Failed to SELECT last_insert_id" ;
        return -1;
    }

    //插入MessageAccountList
    sql_srt_map.clear();
    sql_srt_map.emplace("Account", account);

    sql_int_map.clear();
    sql_int_map.emplace("ClientType", PersoanlMessageSend::APP_SEND);
    sql_int_map.emplace("MessageID", msgid);
    
    ret = tmp_conn->InsertData("MessageAccountList", sql_srt_map, sql_int_map);
    if (ret < 0)
    {
        AK_LOG_WARN << "Failed to insert MessageAccountList, ret:" << ret;
        tmp_conn->TransActionRollback();
        return -1;
    }

    query.Query("SELECT last_insert_id()");
    if (query.MoveToNextRow())
    {
        msg_list_id = ATOI(query.GetRowData(0));
    }
    else{
        AK_LOG_WARN << "Failed to SELECT last_insert_id" ;
        return -1;
    }
    
    tmp_conn->EndTransAction();
    ReleaseDBConn(conn);
    return msg_list_id;
}

//包含Meessage和MessageAccountlList表 用于后台构造Message并下发的情况
//返回插入MessageAccountList记录对应的id
int Message::InsertSentMessage(const std::string &account, const std::string &title, const std::string &content, int msg_type, int receiver_type, int pm_account_id, const std::string& receiver_name)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if(nullptr == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    tmp_conn->BeginTransAction();
    std::map<std::string, std::string> str_map;
    str_map.emplace("Title", title);
    str_map.emplace("Content", content);
    str_map.emplace("NickNames", receiver_name);

    std::map<std::string, int> int_map;
    int_map.emplace("AccountID", pm_account_id);
    int_map.emplace("Status", 1); //置为已发送，避免重复发送
    int_map.emplace("Type", msg_type);
    int_map.emplace("ReceiverType", receiver_type);

    std::string table_name = "Message"; //表名

    int ret = tmp_conn->InsertData(table_name, str_map, int_map);
    if(ret < 0)
    {
        tmp_conn->TransActionRollback();
        ReleaseDBConn(conn);
        AK_LOG_WARN << "Insert into Message failed.";
        return ret;
    }
    int msg_id = GetLastInsertID(tmp_conn);
    if (msg_id < 0)
    {
        tmp_conn->TransActionRollback();
        ReleaseDBConn(conn);
        AK_LOG_WARN << "get last insert msg id failed";
        return -1;
    }
    //根据receiver_type 插入MessageAccountList表
    if(receiver_type == PersoanlMessageSend::TextClientType::DEV_SEND ||
         receiver_type == PersoanlMessageSend::TextClientType::BOTH_SEND) //发给室内机+App或只发室内机
    {
        ret =  InsertMessageAccountList(tmp_conn, PersoanlMessageSend::DEV_SEND, msg_id, account);
    }
    if(ret < 0)
    {
        tmp_conn->TransActionRollback();
        ReleaseDBConn(conn);
        AK_LOG_WARN << "Dev Insert into MessageAccountList failed";
        return ret;
    }
    int msg_account_list_id = GetLastInsertID(tmp_conn);
    if (msg_account_list_id < 0)
    {
        tmp_conn->TransActionRollback();
        ReleaseDBConn(conn);
        AK_LOG_WARN << "get last insert msg account list id failed";
        return -1;
    }
    if(receiver_type == PersoanlMessageSend::TextClientType::APP_SEND ||
         receiver_type == PersoanlMessageSend::TextClientType::BOTH_SEND) //发给室内机+App或只发给App
    {
        ret = InsertMessageAccountList(tmp_conn, PersoanlMessageSend::APP_SEND, msg_id, account);
    }
    if(ret < 0)
    {
        tmp_conn->TransActionRollback();
        ReleaseDBConn(conn);
        AK_LOG_WARN << "Dev Insert into MessageAccountList failed";
        return ret;
    }
    msg_account_list_id = GetLastInsertID(tmp_conn);
    if (msg_account_list_id < 0)
    {
        tmp_conn->TransActionRollback();
        ReleaseDBConn(conn);
        AK_LOG_WARN << "get last insert msg account list id failed";
        return -1;
    }
    tmp_conn->EndTransAction();
    ReleaseDBConn(conn);
    return msg_account_list_id;
}


int Message::InsertMailBoxMessage(const SOCKET_MSG_REPORT_MAILBOX_ARRIVAL_NOTICE &mailbox_arrival_msg, MailBoxMsgSendList& text_messages, const std::vector<ResidentPerAccount>& accounts, const ResidentDeviceList &devlist, int project_type)
{
    MailboxArrivalNoticeMessage send_text_msg;    
    memset(&send_text_msg.text_message, 0, sizeof(send_text_msg.text_message));
    Snprintf(send_text_msg.text_message.title, sizeof(send_text_msg.text_message.title),  mailbox_arrival_msg.title);
    Snprintf(send_text_msg.text_message.content, sizeof(send_text_msg.text_message.content), mailbox_arrival_msg.content);
    Snprintf(send_text_msg.account, sizeof(send_text_msg.account), mailbox_arrival_msg.account);
    send_text_msg.project_type = project_type;
    
    GET_DB_CONN_ERR_RETURN(conn, -1);
    CRldb* tmp_conn = conn.get();
    
    tmp_conn->BeginTransAction();
    std::map<std::string, std::string> str_map;
    str_map.emplace("Title", mailbox_arrival_msg.title);
    str_map.emplace("Content", mailbox_arrival_msg.content);

    std::map<std::string, int> int_map;
    int_map.emplace("Status", 1); //置为已发送，避免重复发送
    int_map.emplace("Type", static_cast<int>(MessageType2::MAILBOX_ARRIVAL_MSG));
    int_map.emplace("ReceiverType", static_cast<int>(PersoanlMessageSend::TextClientType::BOTH_SEND));

    std::string table_name = "Message"; //表名

    int ret = tmp_conn->InsertData(table_name, str_map, int_map);
    if(ret < 0)
    {
        tmp_conn->TransActionRollback();
        AK_LOG_WARN << "Insert into Message failed.";
        return ret;
    }
    int msg_id = GetLastInsertID(tmp_conn);
    if (msg_id < 0)
    {
        tmp_conn->TransActionRollback();
        AK_LOG_WARN << "get last insert msg id failed";
        return -1;
    }
    //根据receiver_type 插入MessageAccountList表
    if (!devlist.empty()) // 检查 devlist 是否为空
    {
        ret = InsertMessageAccountList(tmp_conn, PersoanlMessageSend::DEV_SEND, msg_id, mailbox_arrival_msg.account);
        if (ret < 0)
        {
            tmp_conn->TransActionRollback();
            AK_LOG_WARN << "Dev Insert into MessageAccountList failed";
            return ret;
        }
        for(auto& dev : devlist)
        {
            send_text_msg.client_type = PersoanlMessageSend::DEV_SEND;
            send_text_msg.type = TransP2PMsgType::TO_DEV_MAC;
            send_text_msg.text_message.id = msg_id;
            strncpy(send_text_msg.uid, dev.mac, sizeof(send_text_msg.uid) - 1);
            Snprintf(send_text_msg.uuid, sizeof(send_text_msg.uuid), dev.uuid);
            text_messages.push_back(send_text_msg);
        }
    }
    for(auto& account : accounts)
    {
        ret = InsertMessageAccountList(tmp_conn, PersoanlMessageSend::APP_SEND, msg_id, account.account);
        if(ret < 0)
        {
            tmp_conn->TransActionRollback();
            AK_LOG_WARN << "Dev Insert into MessageAccountList failed";
            return ret;
        }
        send_text_msg.client_type = PersoanlMessageSend::APP_SEND;
        send_text_msg.text_message.id = msg_id;
        send_text_msg.type = TransP2PMsgType::TO_APP_UID;
        Snprintf(send_text_msg.uid, sizeof(send_text_msg.uid), account.account);
        Snprintf(send_text_msg.uuid, sizeof(send_text_msg.uuid), account.uuid);
        text_messages.push_back(send_text_msg); 
    }

    tmp_conn->EndTransAction();
    return ret;
}


//插入MessageAccountList表
int Message::InsertMessageAccountList(CRldb* tmp_conn, int client_type, int message_id, const std::string& account)
{
    if(nullptr == tmp_conn)
    {
        AK_LOG_WARN << "null conn.";
        return -1;        
    }
    std::map<std::string, std::string> strMap;
    strMap.emplace("Account", account);
    std::map<std::string, int> intMap;
    intMap.emplace("ClientType", client_type);
    intMap.emplace("MessageID", message_id);
    std::string table_name = "MessageAccountList";
    return tmp_conn->InsertData(table_name, strMap, intMap);
}

int Message::GetLastInsertID(CRldb* tmp_conn)
{
    if (nullptr == tmp_conn)
    {
        AK_LOG_WARN << "db conn is null";
        return -1;
    }

    std::stringstream str_sql;
    str_sql << "SELECT last_insert_id()";
    CRldbQuery query(tmp_conn);
    query.Query(str_sql.str());
    if (!query.MoveToNextRow())
    {
        AK_LOG_WARN << "get last insert id failed.";
        return -1;
    }
    return ATOI(query.GetRowData(0));
}


}

