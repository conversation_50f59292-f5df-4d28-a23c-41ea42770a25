#ifndef __DB_MESSAGE_H__
#define __DB_MESSAGE_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include "RldbQuery.h"
#include "Rldb.h"
#include "AkcsCommonDef.h"
#include "DclientMsgSt.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"

//message extension field
#define MESSAGE_EXTENSION_FIELD_LOCK_TYPE                       "lock_type"
#define MESSAGE_EXTENSION_FIELD_LAST_BATTERY                  "last_battery"

typedef struct PER_TEXT_MESSAGE_T
{
    char protocal[16];
    char type[24];
    char title[1025];
    char content[3500];
    char time[24];
    char from[24];
    char to[24];
    int id;                 //MessageAccountList表id 用于离线推送url拼接
} PersoanlMessage;

typedef struct PER_TEXT_MESSAGE_SEND_T
{
    enum TextSendType
    {
        MULTI_SEND = 0,     //多个发送
        FULL_SEND = 1,      //全发送
    };
    enum TextClientType
    {
        BOTH_SEND = 0,      //APP、设备均发送
        DEV_SEND = 1,       //设备发送
        APP_SEND = 2,       //app发送
    };

    PersoanlMessage text_message;
    std::string account;        // client_type=APP_SEND时有效，否则无效
    int client_type;            // 终端的类型,设备/app
    int per_manager_id;         //个人终端管理员的id
} PersoanlMessageSend;

typedef std::vector<PersoanlMessageSend> PerMsgSendList;
typedef struct CommPerTextMessage_T
{
    char title[1025];
    char content[3500];
    char extension_field[1024]; //message拓展信息字段，多语言展示需要
    char trigger_time[32];  //消息触发时间，用于弹窗展示时间
    int project_type;       //业务类型 0-社区 1-办公 2-单住户
    MessageType2 msg_type;
    CommPerTextMessage_T()
    {
        memset(this, 0, sizeof(*this));
    }
}CommPerTextMessage;

typedef struct PersonalTextMessageSendNode_T
{
    CommPerTextMessage comm_message;
    char uuid[64];          //app对应用户uuid；设备对应devices表的uuid
    int id;                 //数据库MessageAccountList表的id
    int client_type;        //接收方类型：设备/app
    int project_type;       //业务类型 0-社区 1-办公 2-单住户

    PersonalTextMessageSendNode_T ()
    {
    	uuid[0] = 0;
		id = 0;
		client_type = 0;
		project_type = 0;
	}
}PerTextMessageSend;
typedef std::vector<PerTextMessageSend> PerTextMessageSendList;

typedef struct MailboxArrivalNoticeMessage_T
{
    PersoanlMessage text_message;
    char account[24];       
    int client_type;            // 终端的类型,设备/app
    int type;                 //消息类型 TRAN_TYPE_NONE = 0,     TO_DEV_MAC = 1, TO_APP_UID = 2,TO_DEV_UUID = 3,TO_APP_UUID = 4,TO_ALL_APP = 5，TO_APP_UID_ONLINE = 6
    char uid[64];            // 主从账户的account/设备mac
    char uuid[64];            // 主从账户的uuid/设备uuid
    int project_type;
} MailboxArrivalNoticeMessage;
typedef std::vector<MailboxArrivalNoticeMessage> MailBoxMsgSendList;


namespace dbinterface
{

class Message
{
public:
    Message();
    ~Message();
    static int GetTextMsgSendList(PerMsgSendList& message_list);
    static int GetTextMsgLatestIDAndUnReadNum(const std::string& uid, const std::string& node, 
        int lastread_id, int manager_id, int& nLastID,  int& nUnReadNum);
    static int AddTextMsgByMngDev(const std::string& nodes, const std::string& titil, 
        const std::string& msg, int manager_id, PerMsgSendList& text_messages, int is_aws);
    static int AddOfficeTextMsgByMngDev(const std::string& nodes, const std::string& titil, 
        const std::string& msg, int manager_id, PerMsgSendList& text_messages, int is_aws);
    //Message默认发送给室内机  need_notify_dev只跟实际业务要不要通知室内机有关 与室内机列表是否为空无关
    static int AddGroupTextMsg(int message_type, const CommPerTextMessage& comm_text_msg, const ResidentDeviceList& dev_list,
     const ResidentPerAccount& master_account, const ResidentPerAccountList& sub_account_list, PerTextMessageSendList& text_messages,
     bool need_notify_dev = true);
    static int getUnReadMessageCount(const std::string& user_info_uuid, const std::string& create_time);
    static int InsertDeliveryMsg(const std::string& account, const std::string& content, int type);
    static int InsertMessage(const std::string& account, const std::string& content);
    
    //包含Meessage和MessageAccountlList表 用于后台构造Message并下发的情况
    //返回插入Message记录对应的id
    static int InsertSentMessage(const std::string &account, const std::string &title, const std::string &content, int msg_type, int receiver_type, int pm_account_id, const std::string& receiver_name);
    static int InsertMailBoxMessage(const SOCKET_MSG_REPORT_MAILBOX_ARRIVAL_NOTICE &mailbox_arrival_msg, MailBoxMsgSendList& text_messages, const std::vector<ResidentPerAccount>& accounts, const ResidentDeviceList &devlist, int project_type);
private:
    static int InsertMessageAccountList(CRldb* conn, int client_type, int message_id, const std::string& account);
    static int GetLastInsertID(CRldb* conn);
    

};

}
#endif
