#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "dbinterface/NotifyList.h"
#include "dbinterface/Account.h"
#include "ConnectionPool.h"
#include "Rldb.h"
#include "dbinterface/SystemSettingTable.h"
#include "dbinterface/UUID.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/PmAccountMap.h"
#include "AkLogging.h"
#include "dbinterface/NotifyList.h"
#include "dbinterface/PropertyMngList.h"


namespace dbinterface {

int PushButtonNotifyList::PushButtonNotifyInsOrPm(const std::string& receiver_uuid, const std::string& notify_uuid, const std::string& project_uuid)
{
    std::string uuid;
    dbinterface::UUID::GenerateUUID(dbinterface::SystemSetting::GetServerTag(), uuid);
    //插入数据构造
    std::map<std::string, std::string>str_map;
    str_map.emplace("UUID", uuid);
    if(notify_uuid.size() > 0)
    {
        str_map.emplace("PushButtonNotifyUUID", notify_uuid);
    }
    if(receiver_uuid.size() > 0)
    {
        str_map.emplace("ReceiverUUID", receiver_uuid);
    }
    if(project_uuid.size() > 0)
    {
        str_map.emplace("ProjectUUID", project_uuid);
    }
    
    std::map<std::string, int>int_map;
    std::string table_name = "PushButtonNotifyList";
    
    GET_DB_CONN_ERR_RETURN(tmp_conn, -1);
    CRldb* conn = tmp_conn.get();
    int ret = conn->InsertData(table_name, str_map, int_map);
    return ret;
}

void PushButtonNotifyList::InsertPushButtonNotifyList(const ResidentDev& dev, const std::string& notify_uuid)
{   
    if(notify_uuid.empty()) 
    {
        AK_LOG_WARN << "Insert PushButton Notify List Faild. Notify Uuid Is Empty";
        return;
    }
    //社区设备通知ins和pm, 别墅设备通知ins
    if(dev.project_type == project::RESIDENCE)
    {   
        AccountInfo account_info;
        dbinterface::Account::GetAccountById(dev.project_mng_id, account_info);
        //通知ins
        AccountInfo ins_account_info;
        if(0 == dbinterface::Account::GetInsAccountInfoByManageGroup(account_info.manage_group, ins_account_info))
        {
            PushButtonNotifyInsOrPm(ins_account_info.uuid, notify_uuid, account_info.uuid);
        }
        
        //通知pm
        std::vector<PropertyMngListInfo> property_mng_list_info;
        dbinterface::PropertyMngList::GetPropertyMngListByCommunityID(account_info.id, property_mng_list_info);
        if(property_mng_list_info.size() != 0)
        {
            for (const auto& property_mng_info : property_mng_list_info) 
            {
                AccountInfo pm_account_info;
                if(dbinterface::Account::GetAccountById(property_mng_info.property_id, pm_account_info) != 0)
                {
                    AK_LOG_WARN << "get pm_account_info by id failed, id = " << property_mng_info.property_id;
                    continue;
                }
                PushButtonNotifyInsOrPm(pm_account_info.uuid, notify_uuid, account_info.uuid);
            }
        }
    }
    else 
    {   
        //通知ins
        AccountInfo account_info;
        if(0 == dbinterface::Account::GetAccountInfoByAccount(dev.community, account_info))
        {
            PushButtonNotifyInsOrPm(account_info.uuid, notify_uuid);
        }
    }
}

}
