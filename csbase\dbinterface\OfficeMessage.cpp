#include <sstream>
#include <string.h>
#include <set>
#include <boost/algorithm/string.hpp>
#include "dbinterface/UUID.h"
#include "util.h"
#include "AkLogging.h"
#include "OfficeMessage.h"
#include "ConnectionManager.h"
#include "dbinterface/Message.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/SystemSettingTable.h"
#include "dbinterface/office/OfficePersonalAccount.h"

namespace dbinterface
{

//获取到最新的发送text msg
int OfficeMessage::GetTextMsgSendList(const std::string& message_uuid, MsgSendList& message_list)
{
    int finish = 1;
    char sql[1024] = { 0 };
    
    ::snprintf(sql, 1024,
        "select M.Title, M.Content, M.CreateTime, M.Type, M.ID, R.DeviceUUID, R.ClientType, R.PersonalAccountUUID, R.ID \
        from OfficeMessage M join OfficeMessageReceiver R on M.UUID = R.MessageUUID \
        where M.Status = 0 and M.UUID='%s' order by M.ID desc limit 1000", message_uuid.c_str()
    );

    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    CRldbQuery query(db_conn.get());

    std::vector<int> text_message_ids;
    
    std::string sql2 = sql;
    query.Query(sql2);
    while (query.MoveToNextRow())
    {
        finish = 0;
        CommonMessage send_text_msg;
        
        Snprintf(send_text_msg.title, sizeof(send_text_msg.title), query.GetRowData(0));
        Snprintf(send_text_msg.content, sizeof(send_text_msg.content), query.GetRowData(1));
        Snprintf(send_text_msg.create_time, sizeof(send_text_msg.create_time), query.GetRowData(2));
        send_text_msg.msg_type = (MessageType2)ATOI(query.GetRowData(3));
        text_message_ids.push_back(ATOI(query.GetRowData(4)));
        snprintf(send_text_msg.device_uuid, sizeof(send_text_msg.device_uuid), "%s", query.GetRowData(5));
        send_text_msg.client_type = (MessageClientType)ATOI(query.GetRowData(6));
        snprintf(send_text_msg.personal_account_uuid, sizeof(send_text_msg.personal_account_uuid), "%s", query.GetRowData(7));
        send_text_msg.recv_msg_id = ATOI(query.GetRowData(8));
        
        message_list.push_back(send_text_msg);
    }

    if (text_message_ids.size() > 0)
    {
        std::string ids = ListToSeparatedFormatString(text_message_ids);

        //刷新消息发送与否的状态
        std::stringstream stream_sql;
        stream_sql << "update OfficeMessage set Status = 1 where ID in(" << ids << ")";
        db_conn->Execute(stream_sql.str());
    }
    return finish;
}

int OfficeMessage::InsertOfficeMessageAndReciver(const std::string& personal_account_uuid, const std::string& content, const std::string& rbac_uuid, std::string& message_uuid)
{
    // 1. generate uuid by db.
    std::string recv_uuid = "";
    if (UUID::GenerateUUID(dbinterface::SystemSetting::GetServerTag(), message_uuid) != 0 ||
        UUID::GenerateUUID(dbinterface::SystemSetting::GetServerTag(), recv_uuid) != 0)
    {
        AK_LOG_WARN << "InsertOfficeMessageAndReciver failed: Get uuid failed.";
        return -1;
    }

    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    db_conn->BeginTransAction();

    //插入message
    std::map<std::string, std::string> sql_srt_map;
    sql_srt_map.emplace("UUID", message_uuid);
    sql_srt_map.emplace("Title", "TmpKey Used");
    sql_srt_map.emplace("Content", content);
    sql_srt_map.emplace("RBACDataGroupUUID", rbac_uuid);

    std::map<std::string, int> sql_int_map;
    sql_int_map.emplace("Type", TMPKEY_MSG);
    sql_int_map.emplace("Status", SEND_INCOMPLETE);
    
    int ret = db_conn->InsertData("OfficeMessage", sql_srt_map, sql_int_map);
    if (ret < 0)
    {
        AK_LOG_WARN << "InsertData OfficeMessage failed: ret=" << ret;
        db_conn->TransActionRollback();
        return ret;
    }

    //插入OfficeMessageReciver
    sql_srt_map.clear();
    sql_srt_map.emplace("UUID", recv_uuid);
    sql_srt_map.emplace("PersonalAccountUUID", personal_account_uuid);
    sql_srt_map.emplace("MessageUUID", message_uuid);

    sql_int_map.clear();
    sql_int_map.emplace("ClientType", MessageClientType::APP_SEND); //这里不用区分ClientType,不会影响web的展示
    
    ret = db_conn->InsertData("OfficeMessageReceiver", sql_srt_map, sql_int_map);
    if (ret < 0)
    {
        AK_LOG_WARN << "InsertData OfficeMessageReceiver failed: ret=" << ret;
        db_conn->TransActionRollback();
        return ret;
    }

    db_conn->EndTransAction();
    return 0;
}

// 插入lockdown消息
void OfficeMessage::InsertLockDownNotifyMessage(const std::string& account_uuid, const std::string& title, const std::string& content, MessageContentType type, std::string& message_uuid)
{
    UUID::GenerateUUID(dbinterface::SystemSetting::GetServerTag(), message_uuid);

    GET_DB_CONN_ERR_RETURN(db_conn,);

    // 插入message
    std::map<std::string, std::string> sql_string_map;
    sql_string_map.emplace("Title", title);
    sql_string_map.emplace("Content", content);
    sql_string_map.emplace("UUID", message_uuid);
    sql_string_map.emplace("AccountUUID", account_uuid);

    std::map<std::string, int> sql_int_map;
    sql_int_map.emplace("Type", type);
    sql_int_map.emplace("Status", SEND_INCOMPLETE);
    sql_int_map.emplace("ReceiverType", MessageClientType::APP_SEND);
    
    if (db_conn->InsertData("OfficeMessage", sql_string_map, sql_int_map) < 0)
    {
        AK_LOG_WARN << "InsertLockDownNotifyMessage failed, account_uuid = " << account_uuid << ", title = " << title << ", content = " << content << ", type = " << type;
    }
    return;
}

// 插入lockdown消息的receiver
void OfficeMessage::InsertLockDownMessageReceiver(const std::string& personal_account_uuid, const std::string& message_uuid)
{
    std::string uuid;
    UUID::GenerateUUID(dbinterface::SystemSetting::GetServerTag(), uuid);

    GET_DB_CONN_ERR_RETURN(db_conn,);

    std::map<std::string, std::string> sql_string_map;
    sql_string_map.emplace("UUID", uuid);
    sql_string_map.emplace("MessageUUID", message_uuid);
    sql_string_map.emplace("PersonalAccountUUID", personal_account_uuid);

    std::map<std::string, int> sql_int_map;
    sql_int_map.emplace("Status", MessageSendStatus::SEND_INCOMPLETE);
    sql_int_map.emplace("ClientType", MessageClientType::APP_SEND);

    if (db_conn->InsertData("OfficeMessageReceiver", sql_string_map, sql_int_map) < 0)
    {
        AK_LOG_WARN << "InsertLockDownMessageReceiver failed, personal_account_uuid =" << personal_account_uuid << ", message_uuid =" << message_uuid;
    }
    return;
}


}

