#ifndef __DB_OFFICE_MESSAGE_H__
#define __DB_OFFICE_MESSAGE_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include "RldbQuery.h"
#include "Rldb.h"
#include "Message.h"
#include "AkcsCommonDef.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"


typedef struct COMMON_MESSAGE_T
{
    MessageType2 msg_type;
    char title[1025];
    char content[3500];
    char create_time[24];
    char from[64];
    char to[64];
    int recv_msg_id;                 //发送列表的message id

    MessageClientType client_type;
    char personal_account_uuid[64];
    char device_uuid[64];
    COMMON_MESSAGE_T() {
        memset(this, 0, sizeof(*this));
    }    
} CommonMessage;

typedef std::vector<CommonMessage> MsgSendList;


namespace dbinterface
{
    class OfficeMessage
    {
    public:
        OfficeMessage() {}
        ~OfficeMessage() {}
        static int GetTextMsgSendList(const std::string& message_uuid, MsgSendList& message_list);
        static int InsertOfficeMessageAndReciver(const std::string& personal_account_uuid, const std::string& content, const std::string& rbas_uuid, std::string& message_uuid);
        static void InsertLockDownNotifyMessage(const std::string& account_uuid, const std::string& title, const std::string& content, MessageContentType type, std::string& message_uuid); 
        static void InsertLockDownMessageReceiver(const std::string& personal_account_uuid, const std::string& message_uuid);
    };
}

#endif // __DB_OFFICE_MESSAGE_H__
