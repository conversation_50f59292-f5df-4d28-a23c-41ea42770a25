#include "dbinterface/OfflinePushInfo.h"
#include <chrono>
#include <ctime>
#include <iostream>
#include "util.h"
#include "dbinterface/Account.h"
#include "dbinterface/CommunityRoom.h"
#include "dbinterface/CommunityUnit.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"

void OfflinePush::GetOfflinePushInfoByAccount(const std::string& account, OfflinePushUserInfo &offline_user)
{
    ResidentPerAccount personal_account;
    memset(&personal_account, 0, sizeof(personal_account));
    int ret = dbinterface::ResidentPersonalAccount::GetUidAccount(account, personal_account);
    if (ret != 0)
    {
        AK_LOG_WARN << "GetUidAccount failed";
        return;
    }
    offline_user.role = personal_account.role;

    if (personal_account.role == ACCOUNT_ROLE_COMMUNITY_MAIN || personal_account.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT || 
        personal_account.role == ACCOUNT_ROLE_COMMUNITY_PM)
    {
        int mng_id = 0;
        int room_id = 0;
        if (personal_account.role == ACCOUNT_ROLE_COMMUNITY_MAIN || personal_account.role == ACCOUNT_ROLE_COMMUNITY_PM)
        {
            mng_id = personal_account.parent_id;
            room_id = personal_account.room_id;
        }
        else
        {
            ResidentPerAccount main_account;
            memset(&main_account, 0, sizeof(main_account));
            if (0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(personal_account.parent_uuid, main_account))
            {
                mng_id = main_account.parent_id;
                room_id = main_account.room_id;
            }
        }

        GetCommOfflinePushInfo(mng_id, room_id, offline_user);
        //主从账户展示[社区-APT]
        if (personal_account.role == ACCOUNT_ROLE_COMMUNITY_MAIN || personal_account.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT)
        {
            snprintf(offline_user.title_prefix, sizeof(offline_user.title_prefix), "[%s-%s] ", offline_user.community_name, offline_user.apt_number);
        }
        else
        {
            //PM APP展示[社区]
            snprintf(offline_user.title_prefix, sizeof(offline_user.title_prefix), "[%s] ", offline_user.community_name);
        }
    }
    else if (personal_account.role == ACCOUNT_ROLE_PERSONNAL_MAIN || personal_account.role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT)
    {
        //room_number不为空，标题展示[房间名]
        if (strlen(personal_account.room_number))
        {
            Snprintf(offline_user.room_name, sizeof(personal_account.room_number),  personal_account.room_number);
        }
        else
        {
            //没有room_number，标题展示[用户名]
            if (personal_account.role == ACCOUNT_ROLE_PERSONNAL_MAIN)
            {
                Snprintf(offline_user.room_name, sizeof(personal_account.name),  personal_account.name);
            }
            else
            {
                //从账户room_number一定是空的，所以要再判断下主账号room_number是否为空
                ResidentPerAccount master_account;
                memset(&master_account, 0, sizeof(master_account));
                if (0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(personal_account.parent_uuid, master_account))
                {
                    if (strlen(master_account.room_number))
                    {
                        Snprintf(offline_user.room_name, sizeof(master_account.room_number),  master_account.room_number);
                    }
                    else 
                    {
                        Snprintf(offline_user.room_name, sizeof(master_account.name),  master_account.name);
                    }
                }
            }
        }
        snprintf(offline_user.title_prefix, sizeof(offline_user.title_prefix), "[%s] ", offline_user.room_name);
    }
    else if (IsOfficeRole(personal_account.role))
    {
        //办公标题展示[办公名]
        int mng_id = personal_account.parent_id;
        GetCommunityName(mng_id, offline_user);
        snprintf(offline_user.title_prefix, sizeof(offline_user.title_prefix), "[%s] ", offline_user.community_name);
    }
}

void OfflinePush::GetPmAlarmPushInfoByNode(const std::string& account, OfflinePushUserInfo &offline_user)
{
    ResidentPerAccount personal_account;
    memset(&personal_account, 0, sizeof(personal_account));
    int ret = dbinterface::ResidentPersonalAccount::GetUidAccount(account, personal_account);
    if (ret != 0)
    {
        AK_LOG_WARN << "GetUidAccount failed";
        return;
    }
    offline_user.role = personal_account.role;
    int mng_id = personal_account.parent_id;
    int unit_id = personal_account.unit_id;
    int room_id = personal_account.room_id;
    GetCommOfflinePushInfo(mng_id, room_id, offline_user, unit_id);

    if (personal_account.role == ACCOUNT_ROLE_COMMUNITY_MAIN)
    {
        //社区alarm推送给pm app展示标题[社区名-楼栋名-apt]
        snprintf(offline_user.pm_online_title, sizeof(offline_user.pm_online_title), "%s-%s-%s", offline_user.community_name, offline_user.unit_name, offline_user.apt_number);
        snprintf(offline_user.pm_offline_title, sizeof(offline_user.pm_offline_title), "[%s-%s-%s] ", offline_user.community_name, offline_user.unit_name, offline_user.apt_number);
        
    }
    else if (IsOfficeRole(personal_account.role))
    {
        //办公alarm推送给pm app展示标题[办公名-部门名-用户name]
        snprintf(offline_user.pm_online_title, sizeof(offline_user.pm_online_title), "%s-%s-%s", offline_user.community_name, offline_user.unit_name, personal_account.name);
        snprintf(offline_user.pm_offline_title, sizeof(offline_user.pm_offline_title), "[%s-%s-%s] ", offline_user.community_name, offline_user.unit_name, personal_account.name);
        
    }
}

void OfflinePush::GetCommOfflinePushInfo(int mng_id, int room_id, OfflinePushUserInfo &offline_user, int unit_id)
{
    GetCommunityName(mng_id, offline_user);

    CommunityRoomInfo room_info;
    if (0 == dbinterface::CommunityRoom::GetCommunityRoomByID(room_id, room_info))
    {
        Snprintf(offline_user.apt_number, sizeof(room_info.room_number),  room_info.room_number);
    }

    CommunityUnitInfo unit_info;
    if (0 == dbinterface::CommunityUnit::GetCommunityUnitByID(unit_id, unit_info))
    {
        Snprintf(offline_user.unit_name, sizeof(unit_info.unit_name),  unit_info.unit_name);
    }
}

void OfflinePush::GetCommunityName(int mng_id, OfflinePushUserInfo &offline_user)
{
    dbinterface::AccountInfo project_account;
    if (0 == dbinterface::Account::GetAccountById(mng_id, project_account))
    {
        Snprintf(offline_user.community_name, sizeof(project_account.location),  project_account.location);
    }
}

int OfflinePush::GetMultiSiteUserTitle(const std::string &uid, std::string &title_prefix)
{
    OfflinePushUserInfo offline_push_user;
    memset(&offline_push_user, 0, sizeof(offline_push_user));
    OfflinePush offline_push;
    offline_push.GetOfflinePushInfoByAccount(uid, offline_push_user);
    
    AK_LOG_INFO << "GetMultiSiteUserTitle uid = " << uid << ", Title: " << offline_push_user.title_prefix;
    
    title_prefix = offline_push_user.title_prefix;
    return 0;
}



