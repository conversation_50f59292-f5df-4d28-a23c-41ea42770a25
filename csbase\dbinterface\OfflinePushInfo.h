#ifndef _OFFLINE_PUSH_INFO_H_
#define _OFFLINE_PUSH_INFO_H_
#include <string>
#include <memory>
#include <vector>
#include "AkcsCommonDef.h"
#include "AkLogging.h"
#include "AkcsCommonSt.h"

typedef struct OfflinePush_T
{
    int role;
    char community_name[128];
    char unit_name[64];
    char apt_number[16];
    char room_name[64];
    char title_prefix[256];
    char pm_online_title[512];
    char pm_offline_title[512];
}OfflinePushUserInfo;

class OfflinePush
{
public:
    OfflinePush()
    {
        
    }

    ~OfflinePush()
    {

    }

    static void GetOfflinePushInfoByAccount(const std::string& account, OfflinePushUserInfo &offline_user);
    static void GetPmAlarmPushInfoByNode(const std::string& account, OfflinePushUserInfo &offline_user);
    static void GetCommOfflinePushInfo(int mng_id, int room_id, OfflinePushUserInfo &offline_user, int unit_id = 0);
    static void GetCommunityName(int mng_id, OfflinePushUserInfo &offline_user);
    
    static int GetMultiSiteUserTitle(const std::string &uid, std::string &title_prefix);
    
private:

};

#endif
