#include <sstream>
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/InterfaceComm.h"
#include "OfflineResendLog.h"
#include <string.h>
#include "AkLogging.h"

namespace dbinterface{
OfflineResendLog::OfflineResendLog()
{

}

OfflineResendLog::~OfflineResendLog()
{

}

std::string OfflineResendLog::GetOfflineResendLogSeq(const std::string &mac, const std::string &filename)
{
    std::string seq;
    std::stringstream streamSQL;
    streamSQL << "SELECT MessageSeq FROM  OfflineResendLog WHERE Mac = '"
              << mac
              << "' and FileName='"
              << filename
              <<"'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return "";
    }
    CRldbQuery query(ptmpconn);

    query.Query(streamSQL.str());
    if (query.MoveToNextRow())
    {
        seq  = query.GetRowData(0);
    }
    
    ReleaseDBConn(conn);
    return seq;
}

void OfflineResendLog::InsertOfflineResendLog(const std::string &mac, const std::string &filename, const std::string & seq)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }
    char sql[1024] = "";
    ::snprintf(sql, sizeof(sql), "insert into OfflineResendLog(Mac,FileName,MessageSeq) \
    			values('%s','%s','%s')", mac.c_str(), filename.c_str(), seq.c_str());
    
    conn->Execute(sql);
    ReleaseDBConn(conn);
    return ;
}


}


