#ifndef _OFFLINE_RESENDLOG_H__
#define _OFFLINE_RESENDLOG_H__
#include <string>
#include <memory>
#include <tuple>

namespace dbinterface{
class OfflineResendLog
{
public:
    OfflineResendLog();
    ~OfflineResendLog();
    static std::string GetOfflineResendLogSeq(const std::string &mac, const std::string &filename);
    static void InsertOfflineResendLog(const std::string &mac, const std::string &filename, const std::string &seq);
private:
};

}


#endif
