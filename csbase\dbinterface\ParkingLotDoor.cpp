#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "ParkingLotDoor.h"
#include "AkcsCommonSt.h"
#include "AkcsCommonDef.h"
#include "ConnectionManager.h"
#include "util_virtual_door.h"

namespace dbinterface {

static const std::string parking_lot_door_sec = " ParkingLotUUID,DoorType,IsSecurity,RelayIndex ";

void ParkingLotDoor::GetParkingLotDoorFromSql(ParkingLotDoorInfo& parking_lot_door, CRldbQuery& query)
{
    Snprintf(parking_lot_door.parking_lot_uuid, sizeof(parking_lot_door.parking_lot_uuid), query.GetRowData(0));
    parking_lot_door.door_type = ParkingIoType(ATOI(query.GetRowData(1)));
    parking_lot_door.is_security = DoorRelayType(ATOI(query.GetRowData(2)));
    parking_lot_door.relay_index = ATOI(query.GetRowData(3));
    return;
}

int ParkingLotDoor::GetParkingLotDoorByDevicesUUID(const std::string& uuid, ParkingLotDoorInfoList& parking_lot_door_info_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << parking_lot_door_sec << " from ParkingLotDoor where DevicesUUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        ParkingLotDoorInfo parking_lot_door;
        GetParkingLotDoorFromSql(parking_lot_door, query);
        parking_lot_door_info_list.push_back(parking_lot_door);
    }
    return 0;
}

DatabaseExistenceStatus ParkingLotDoor::ParkingLotDeviceExistByMac(const std::string& mac)
{
    std::stringstream stream_sql;
    stream_sql << "select count(*) from ParkingLotDoor where MAC = '" << mac << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, DatabaseExistenceStatus::QUERY_ERROR);
    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        if (ATOI(query.GetRowData(0)) > 0)
        {
            return DatabaseExistenceStatus::EXIST;
        }
    }
    
    return DatabaseExistenceStatus::NOT_EXIST;
}
}