#ifndef __DB_PARKING_LOT_DOOR_H__
#define __DB_PARKING_LOT_DOOR_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/new-office/DevicesDoorList.h"


typedef struct ParkingLotDoorInfo_T
{
    ParkingIoType door_type;
    DoorRelayType is_security;
    int relay_index;
    char dev_uuid[36];
    char parking_lot_uuid[36];
    
    ParkingLotDoorInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} ParkingLotDoorInfo;

using ParkingLotDoorInfoList = std::vector<ParkingLotDoorInfo>;

namespace dbinterface {

class ParkingLotDoor
{
public:
    static int GetParkingLotDoorByDevicesUUID(const std::string& uuid, ParkingLotDoorInfoList& parking_lot_door_info);
    static DatabaseExistenceStatus ParkingLotDeviceExistByMac(const std::string& mac);

private:
    ParkingLotDoor() = delete;
    ~ParkingLotDoor() = delete;
    static void GetParkingLotDoorFromSql(ParkingLotDoorInfo& parking_lot_door_info, CRldbQuery& query);
};
}
#endif