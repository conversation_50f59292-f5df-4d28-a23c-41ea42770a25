#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "PbxServer.h"
#include <string.h>
#include "AkLogging.h"

namespace dbinterface
{

PbxServer::PbxServer()
{

}

int PbxServer::GetCommPbxServer(int community_id, std::string& pbx_ip, std::string& pbx_ipv6, std::string& pbx_domain)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        return -1;
    }
    CRldbQuery query(tmp_conn);

    std::stringstream streamSQL;
    streamSQL << "select P.PbxIp, P.PbxIpv6, P.PbxDomain  from PbxServerList P left join PbxRedirectInfo R on R.PbxId=P.ID where R.CommunitID='" << community_id << "' limit 1";
    query.Query(streamSQL.str());

    if (!query.MoveToNextRow())
    {
        ReleaseDBConn(conn);
        return -1;
    }

    pbx_ip = query.GetRowData(0);
    pbx_ipv6 = query.GetRowData(1);
    pbx_domain = query.GetRowData(2);

    ReleaseDBConn(conn);
    return 0;
}

int PbxServer::GetPerPbxServer(const std::string& node, std::string& pbx_ip, std::string& pbx_ipv6, std::string& pbx_domain)
{
    if (node.empty())
    {
        return -1;
    }

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        return -1;
    }
    CRldbQuery query(tmp_conn);

    std::stringstream streamSQL;
    streamSQL << "select P.PbxIp, P.PbxIpv6, P.PbxDomain from PbxServerList P left join PbxRedirectInfo R on R.PbxID=P.ID where Node='" << node << "' limit 1";
    query.Query(streamSQL.str());

    if (!query.MoveToNextRow())
    {
        ReleaseDBConn(conn);
        return -1;
    }

    pbx_ip = query.GetRowData(0);
    pbx_ipv6 = query.GetRowData(1);
    pbx_domain = query.GetRowData(2);

    ReleaseDBConn(conn);
    return 0;
}

}

