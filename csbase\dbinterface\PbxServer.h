#ifndef __PBXSERVER_H__
#define __PBXSERVER_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>

namespace dbinterface
{

class PbxServer
{
public:
    PbxServer();
    ~PbxServer();
    static int GetCommPbxServer(int community_id, std::string& pbx_ip, std::string& pbx_ipv6, std::string& pbx_domain);
    static int GetPerPbxServer(const std::string& node, std::string& pbx_ip, std::string& pbx_ipv6, std::string& pbx_domain);
private:
};

}
#endif

