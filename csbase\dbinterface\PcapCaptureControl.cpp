#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "PcapCaptureControl.h"
#include <string.h>
#include "AkLogging.h"
#include "AkcsCommonDef.h"
#include "util.h"
namespace dbinterface
{

PcapCaptureControl::PcapCaptureControl()
{

}

PcapCaptureControl::~PcapCaptureControl()
{

}

int PcapCaptureControl::InsertPcapCaptureControlList(const std::string &uuid, const std::string &filename, const std::string &file_url)
{
    std::stringstream stream_sql;
    stream_sql << "insert into PcapCaptureControlList (PcapCaptureUUID, FileName, FileUrl) values "
              << "('" << uuid << "','" << filename << "','" << file_url << "')";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    int ret = tmp_conn->Execute(stream_sql.str()) >= 0 ? 0 : -1;   
    ReleaseDBConn(conn);
    return ret;
}

int PcapCaptureControl::GetCaptureMacList(PcapCaptureInfoList& capture_list)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    std::stringstream stream_sql;
    stream_sql << "select Status,MAC,UUID from PcapCaptureControl";
    
    CRldbQuery query(conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        PCAP_CAPTURE_INFO capture_info;
        capture_info.status = ATOI(query.GetRowData(0));
        Snprintf(capture_info.mac, sizeof(capture_info.mac), query.GetRowData(1));
        Snprintf(capture_info.uuid, sizeof(capture_info.uuid), query.GetRowData(2));
        capture_list.push_back(capture_info);
    }
     
    ReleaseDBConn(conn);
    return 0;
}


}
