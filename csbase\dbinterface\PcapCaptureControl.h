#ifndef __DB_PCAP_CAPTURE_CONTROL_H__
#define __DB_PCAP_CAPTURE_CONTROL_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include <set>
#include "ConnectionPool.h"

namespace dbinterface
{

enum PCAP_STATUS {
     PCAP_STATUS_CAPTURING = 0,
     PCAP_STATUS_FINISHED = 1,
};

typedef struct PCAP_CAPTURE_INFO_T
{   
    int status;
    char mac[20];
    char uuid[64];
} PCAP_CAPTURE_INFO;

typedef std::vector<PCAP_CAPTURE_INFO> PcapCaptureInfoList;

class PcapCaptureControl
{
public:
    PcapCaptureControl();
    ~PcapCaptureControl();
    static int GetCaptureMacList(PcapCaptureInfoList& capture_list);
    static int InsertPcapCaptureControlList(const std::string &uuid, const std::string &filename, const std::string &file_url);
private: 

};


}

#endif
