#include <sstream>
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/InterfaceComm.h"
#include "PendingRegUser.h"
#include <string.h>
#include "AkLogging.h"
#include "util.h"
#include "gid/SnowFlakeGid.h"


namespace dbinterface{
PendingRegUser::PendingRegUser()
{

}

PendingRegUser::~PendingRegUser()
{

}

int PendingRegUser::GetPendingRegUserInfoByNode(const std::string& account, RegEndUserInfo& pending_user_info)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
   CRldb* tmp_conn = conn.get();
   if (NULL == tmp_conn)
   {
       AK_LOG_WARN << "Get DB conn failed.";
       return -1;
   }

   CRldbQuery query(tmp_conn);

   std::stringstream stream_sql;
   stream_sql << "select Status, MAC, Token, Account from PendingRegUser where Account = '" << account << "'";

   query.Query(stream_sql.str());
   
    if (query.MoveToNextRow())
    {
        pending_user_info.status = ATOI(query.GetRowData(0));
        Snprintf(pending_user_info.mac, sizeof(pending_user_info.mac), query.GetRowData(1));
        Snprintf(pending_user_info.token, sizeof(pending_user_info.token), query.GetRowData(2));
        Snprintf(pending_user_info.account, sizeof(pending_user_info.account), query.GetRowData(3));
    }
    else
    {
        ReleaseDBConn(conn);
        return 0;
    }

    ReleaseDBConn(conn);
    return 0;
}

int PendingRegUser::GetPendingRegUserInfoByMac(const std::string& mac, RegEndUserInfo& pending_user_info)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
   CRldb* tmp_conn = conn.get();
   if (NULL == tmp_conn)
   {
       AK_LOG_WARN << "Get DB conn failed.";
       return -1;
   }

   CRldbQuery query(tmp_conn);

   std::stringstream stream_sql;
   stream_sql << "select Status,MAC,Token,Account from PendingRegUser where MAC = '" << mac << "'";

   query.Query(stream_sql.str());
   
    if (query.MoveToNextRow())
    {
        pending_user_info.status = ATOI(query.GetRowData(0));
        Snprintf(pending_user_info.mac, sizeof(pending_user_info.mac), query.GetRowData(1));
        Snprintf(pending_user_info.token, sizeof(pending_user_info.token), query.GetRowData(2));
        Snprintf(pending_user_info.account, sizeof(pending_user_info.account), query.GetRowData(3));
    }
    else
    {
        ReleaseDBConn(conn);
        return 0;
    }

   ReleaseDBConn(conn);
   return 0;
}

int PendingRegUser::UpdatePendingRegUserToken(const std::string& mac, const std::string& token)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
       AK_LOG_WARN << "Get DB conn failed.";
       return -1;
    }
    
    std::stringstream stream_sql;
    stream_sql << "update PendingRegUser set Token = '" << token << "' where MAC = '" << mac << "'";

    int ret = 0;
    ret = conn->Execute(stream_sql.str()) >= 0 ? 0 : -1;
    ReleaseDBConn(conn);
    return ret;
}

}


