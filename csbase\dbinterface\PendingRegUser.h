#ifndef __DB_PENDING_REG_USER_H__
#define __DB_PENDING_REG_USER_H__

#include <string>
#include <memory>
#include <tuple>
#include "BasicDefine.h"


struct RegEndUserInfo
{
    int status;
    char mac[16];
    char account[65];
    char token[64];
    char email[65];
    char reg_url[1025];
    char account_name[129];
    char mobile_number[25];
    int user_projcet_type;
    
    RegEndUserInfo(){
        memset(this, 0, sizeof(RegEndUserInfo));
    }
};


namespace dbinterface{
class PendingRegUser
{
public:
    PendingRegUser();
    ~PendingRegUser();
    static int GetPendingRegUserInfoByNode(const std::string& mac, RegEndUserInfo& pending_user_info);
    static int GetPendingRegUserInfoByMac(const std::string& mac, RegEndUserInfo& pending_user_info);
    static int UpdatePendingRegUserToken(const std::string& mac, const std::string& token);
private:
};

}


#endif
