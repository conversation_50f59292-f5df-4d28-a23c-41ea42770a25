#include <sstream>
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/InterfaceComm.h"
#include "PerNodeDevices.h"
#include <string.h>
#include "AkLogging.h"

namespace dbinterface{
PerNodeDevices::PerNodeDevices()
{

}
PerNodeDevices::~PerNodeDevices()
{

}

int PerNodeDevices::CheckNodeIDExist(int node_id)

{
    int ret = 0;
    std::stringstream streamSQL;
    streamSQL << "SELECT NodeID FROM PerNodeDevices "
              << "WHERE NodeID = '"
              << node_id
              << "' limit 1";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(ptmpconn);

    query.Query(streamSQL.str());
    if (query.MoveToNextRow())
    {
        ret = 1;
    }
    else
    {
        ret = 0;
    }
    ReleaseDBConn(conn);
    return ret;
}

int PerNodeDevices::GetNodesByPublicDevID(int public_dev_id, std::vector<PER_NODE_DEVICES>& dev_ids)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    PER_NODE_DEVICES tmp_dev;

    std::stringstream streamSQL2;
    streamSQL2 << "SELECT A.ID,A.Account FROM PerNodeDevices P join PersonalAccount A  on A.id=P.NodeID "
               << "WHERE P.PerDevID = '"
               << public_dev_id
               << "'";
    query.Query(streamSQL2.str());

    while (query.MoveToNextRow())
    {
        memset(&tmp_dev, 0, sizeof(tmp_dev));
        tmp_dev.node_id = ATOI(query.GetRowData(0));
        Snprintf(tmp_dev.node, sizeof(tmp_dev.node), query.GetRowData(1));
        dev_ids.push_back(tmp_dev);
    }
    ReleaseDBConn(conn);
    return 0;

}

int PerNodeDevices::GetPublicDevIDsByNodeID(int node_id, std::vector<int>& dev_ids)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    CRldbQuery query(tmp_conn);

    std::stringstream streamSQL2;
    streamSQL2 << "SELECT PerDevID FROM PerNodeDevices "
               << "WHERE NodeID = '"
               << node_id
               << "';";
    query.Query(streamSQL2.str());

    while (query.MoveToNextRow())
    {
        dev_ids.push_back(ATOI(query.GetRowData(0)));
    }
    ReleaseDBConn(conn);
    return 0;

}

/*获取一个公共设备的联动信息，这样就可以调用另外一个接口*/
int PerNodeDevices::GetPerOneNodeByPublicDevID(int per_dev_id, std::string& users)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    CRldbQuery query(tmp_conn);

    std::stringstream streamSQL2;
    streamSQL2 << "select P.Account from PerNodeDevices D join PersonalAccount P on D.NodeID=P.ID where D.PerDevID="
               << per_dev_id
               << " limit 1;";
    query.Query(streamSQL2.str());

    if (query.MoveToNextRow())
    {
        char node[32];
        Snprintf(node, sizeof(node), query.GetRowData(0));
        users = node;
    }
    ReleaseDBConn(conn);
    return 0;

}


}


