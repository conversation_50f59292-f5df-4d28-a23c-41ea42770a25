#ifndef _PER_NODE_DEVICES_H__
#define _PER_NODE_DEVICES_H__
#include <string>
#include <memory>
#include <tuple>
#include "DclientMsgSt.h"
#include "AkcsCommonSt.h"
#include "util.h"
#include "AkcsCommonDef.h"


namespace dbinterface{
class PerNodeDevices
{
public:
    PerNodeDevices();
    ~PerNodeDevices();
    static int CheckNodeIDExist(int node_id);
    static int GetNodesByPublicDevID(int public_dev_id, std::vector<PER_NODE_DEVICES>& dev_ids);
    static int GetPublicDevIDsByNodeID(int node_id, std::vector<int>& dev_ids);
    static int GetPerOneNodeByPublicDevID(int per_dev_id, std::string& users);
private:
};

}


#endif
