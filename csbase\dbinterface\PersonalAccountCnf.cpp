#include <sstream>
#include "dbinterface/PersonalAccountCnf.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include <string.h>
#include "AkLogging.h"
#include "dbinterface/InterfaceComm.h"
#include "util.h"
#include "dbinterface/DataConfusion.h"

using namespace Akcs;

void PersonalAccountCnfInfo::getRobinCallVal(std::vector<RobinCallValue>& robin_calls)
{
    Json::Reader reader;
    Json::Value root;
    // reader将Json字符串解析到root，root将包含Json里所有子元素
    if (!reader.parse(robin_call_val, root))
    {
        AK_LOG_WARN << "parse json error";
        return;
    }


    int i = 0;
    for (i = 0; i < 10; i++)//robin call 最大10个
    {
        char index[8];
        RobinCallValue robincall;
        memset(&robincall, 0, sizeof(robincall));
        ::snprintf(index, sizeof(index), "%d", i);
        robincall.id = root[index]["ID"].asInt();
        if (robincall.id == 0)
        {
            continue;
        }
        robincall.type = root[index]["Type"].asInt();
        robincall.is_personal = root[index]["IsPer"].asInt();
        robin_calls.push_back(robincall);
    }
    return;
}

namespace dbinterface
{

PersonalAccountCnf::PersonalAccountCnf()
{

}

//  C.EnableSoundDetection, C.SoundType 声音检测数据库暂未添加
static const std::string devices_sec = " C.ID,C.Account,C.EnableMotion,C.MotionTime,C.EnableRobinCall,C.RobinCallTime,\
                        C.RobinCallVal,C.CallType,C.WebRelay,C.Flags,C.EnablePackageDetection, C.EnableLandline, C.WithIndoorMonitor,C.EnableSmartHome ";




int PersonalAccountCnf::GetPeronalAccountCnfByNode(const std::string &node, PersonalAccountCnfInfo &info)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::stringstream sql;
    sql << "SELECT " << devices_sec << " FROM PersonalAccountCnf C WHERE Account = '" << node << "'";

    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        info.user = ATOI(query.GetRowData(1));
        info.enable_motion = ATOI(query.GetRowData(2));
        info.motion_time = ATOI(query.GetRowData(3));
        info.enable_robin_call = ATOI(query.GetRowData(4));
        info.robin_call_time = ATOI(query.GetRowData(5));
        info.robin_call_val = query.GetRowData(6) ? query.GetRowData(6) : "";
        info.call_type = ATOI(query.GetRowData(7));
        info.web_relay = ATOI(query.GetRowData(8));
        info.flags = ATOI(query.GetRowData(9));
        info.enable_package_detection = ATOI(query.GetRowData(10));
        info.enable_landline = ATOI(query.GetRowData(11));
        info.with_indoor_monitor = ATOI(query.GetRowData(12));
        info.enable_smarthome = ATOI(query.GetRowData(13));
    }
    ReleaseDBConn(conn);
    return 0;
}

//通过主账号列表查找cnf
int PersonalAccountCnf::GetPeronalAccountCnfByNodes(const std::string &nodes, PersonalAccountCnfInfoMap &map)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    std::stringstream sql;
    sql << "SELECT " << devices_sec << " FROM  PersonalAccountCnf C "
           << "WHERE Account in("
           << nodes
           << ")";

    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        PersonalAccountCnfInfo info;
        info.user = query.GetRowData(1);
        info.enable_motion = ATOI(query.GetRowData(2));
        info.motion_time = ATOI(query.GetRowData(3));
        info.enable_robin_call = ATOI(query.GetRowData(4));
        info.robin_call_time = ATOI(query.GetRowData(5));
        info.robin_call_val = query.GetRowData(6) ? query.GetRowData(6) : "";
        info.call_type = ATOI(query.GetRowData(7));
        info.web_relay = ATOI(query.GetRowData(8));
        info.flags = ATOI(query.GetRowData(9));
        info.enable_package_detection = ATOI(query.GetRowData(10));
        info.enable_landline = ATOI(query.GetRowData(11));
        info.with_indoor_monitor = ATOI(query.GetRowData(12));
        info.enable_smarthome = ATOI(query.GetRowData(13));

        // info.enable_sound_detection = ATOI(query.GetRowData(11));
        // info.sound_type = ATOI(query.GetRowData(12));
        map.insert(std::make_pair(info.user, info));
    }
    ReleaseDBConn(conn);
    return 0;
}

int PersonalAccountCnf::EnableFaceRegister(PersonalAccountCnfInfo &info)
{
    int flag = dbinterface::SwitchHandle(info.flags, PersonalAccountCnfInfo::FLAGS_TYPE::ENABLE_REGISTER_FACE);
    return flag;
}

int PersonalAccountCnf::EnableUserCreateIDAccess(PersonalAccountCnfInfo &info)
{
    int flag = dbinterface::SwitchHandle(info.flags, PersonalAccountCnfInfo::FLAGS_TYPE::ENABLE_USER_CREATE_ID_ACCESS);
    return flag;
}

bool PersonalAccountCnf::CheckFamilyMemberControl(const std::string& node, const std::string& account)
{
    bool ret = true;
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return ret;
    }
    
    std::stringstream sql;
    sql << "SELECT Flags,AllowCreateSlaveCnt FROM  PersonalAccountCnf "
           << "WHERE Account ='"
           << node
           << "'";

    CRldbQuery query(tmp_conn);
    query.Query(sql.str());

    
    if(query.MoveToNextRow())
    {
        if(dbinterface::SwitchHandle(ATOI(query.GetRowData(0)), PersonalAccountCnfInfo::FLAGS_TYPE::ENABLE_CONTROL_FAMILY_MEMBERS))
        {
            int allow_cnt = ATOI(query.GetRowData(1));
            int id = 0;

            std::stringstream sql1;
            sql1 << "SELECT ID FROM APPSpecial "
                   << "WHERE Account ='"
                   << account
                   << "'";
            
            query.Query(sql1.str());
            if(query.MoveToNextRow())
            {
                id = ATOI(query.GetRowData(0));
                
                //小于这个用户id的数据量，如果大于限制的大小则是非法的
                std::stringstream sql2;
                sql2 << "SELECT count(1) as count FROM APPSpecial "
                       << "WHERE Node ='"
                       << node
                       << "' and ID <"
                       << id;
                query.Query(sql2.str());
                if(query.MoveToNextRow())
                {   
                    int cnt = ATOI(query.GetRowData(0));
                    if(cnt >= allow_cnt)
                    {
                        ret = false;
                    }
                }
            }

        }
    }

    ReleaseDBConn(conn);
    return ret;        
}



int PersonalAccountCnf::GetPeronalAccountCnfByCommunityProjectID(uint32_t project_id, PersonalAccountCnfInfoMapPtr &node_map)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::stringstream sql;
    sql << "SELECT C.ID,C.Account,C.EnableMotion,C.MotionTime,C.EnableRobinCall,C.RobinCallTime,C.RobinCallVal,C.CallType,C.WebRelay,C.Flags,"
        <<"P.Phone,P.Phone2,P.Phone3,P.EnableIpDirect,P.PhoneCode,C.EnablePackageDetection,C.EnableLandline,C.WithIndoorMonitor,C.EnableSmartHome from PersonalAccountCnf C left join PersonalAccount P on C.Account=P.Account where P.ParentID="<< project_id << " and P.Role= " << GetCommunityMainRole() << "";

    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        PersonalAccountCnfInfo info;
        info.user = query.GetRowData(1);
        info.enable_motion = ATOI(query.GetRowData(2));
        info.motion_time = ATOI(query.GetRowData(3));
        info.enable_robin_call = ATOI(query.GetRowData(4));
        info.robin_call_time = ATOI(query.GetRowData(5));
        info.robin_call_val = query.GetRowData(6) ? query.GetRowData(6) : "";
        info.call_type = ATOI(query.GetRowData(7));
        info.web_relay = ATOI(query.GetRowData(8));
        info.flags = ATOI(query.GetRowData(9));        
        info.phone = dbinterface::DataConfusion::Decrypt(query.GetRowData(10));
        info.phone2 = dbinterface::DataConfusion::Decrypt(query.GetRowData(11));
        info.phone3 = dbinterface::DataConfusion::Decrypt(query.GetRowData(12));
        info.ip_direct = ATOI(query.GetRowData(13));
        info.phone_code = query.GetRowData(14);
        info.enable_package_detection = ATOI(query.GetRowData(15));
        info.enable_landline = ATOI(query.GetRowData(16));
        info.with_indoor_monitor = ATOI(query.GetRowData(17));
        info.enable_smarthome = ATOI(query.GetRowData(18));
        node_map->insert(std::make_pair(info.user, info));
    }
    ReleaseDBConn(conn);
    return 0;
}


}

