#ifndef __DB_PERSONAL_ACCOUNT_CNF_H__
#define __DB_PERSONAL_ACCOUNT_CNF_H__
#include <string>
#include <memory>
#include <vector>
#include <map>
#include <stdint.h>
#include "json/json.h"

typedef struct RobinCallValue_T
{
    int id;
    int type; // app devices
    int is_personal;

    //以下是加入的信息
    char ip[64];
    char sip[32];
    char phone[32];
    char phone_code[8];
    char phone2[32];
    char phone3[32];
    int role; //app role
    int netgroup;
} RobinCallValue;


class PersonalAccountCnfInfo
{
public:
    PersonalAccountCnfInfo()
    {
        enable_motion = 0;
        motion_time = 0;
        enable_robin_call = 0;
        robin_call_time = 0;
        robin_call_val = "";
        call_type = 0;
        web_relay = 0;
        flags = 0;
        ip_direct = 0;
        enable_landline = 0;
        with_indoor_monitor = 0;
        enable_smarthome = 0;
    }

    ~PersonalAccountCnfInfo()
    {

    }
    
    enum FLAGS_TYPE
    {
        ENABLE_CONTROL_FAMILY_MEMBERS = 0,
        ENABLE_REGISTER_FACE = 1,
        ENABLE_USER_CREATE_ID_ACCESS = 2,
    };
    
    void getRobinCallVal(std::vector<RobinCallValue>& robin_calls);
    
    int enable_motion;
    int motion_time;
    int enable_robin_call;
    int robin_call_time;
    std::string robin_call_val;
    int call_type;
    int web_relay;
    int flags;
    int enable_package_detection;
    int enable_landline;
    // int enable_sound_detection;
    // int sound_type;
    std::string user;
    int with_indoor_monitor;
    int enable_smarthome;

    //扩展 只有GetPeronalAccountCnfByCommunityProjectID查找时候才有
    std::string phone;
    std::string phone2;
    std::string phone3;
    std::string phone_code;
    int ip_direct;
};

typedef std::map<std::string/*node*/, PersonalAccountCnfInfo> PersonalAccountCnfInfoMap;
typedef PersonalAccountCnfInfoMap::iterator PersonalAccountCnfInfoMapIter;
using PersonalAccountCnfInfoMapPtr =  std::shared_ptr<PersonalAccountCnfInfoMap>;


namespace dbinterface
{

class PersonalAccountCnf
{
public:
    PersonalAccountCnf();
    static int GetPeronalAccountCnfByNode(const std::string &node, PersonalAccountCnfInfo &info);
    //通过主账号列表查找Room
    static int GetPeronalAccountCnfByNodes(const std::string &nodes, PersonalAccountCnfInfoMap &map);
    static int EnableFaceRegister(PersonalAccountCnfInfo &info);
    static int EnableUserCreateIDAccess(PersonalAccountCnfInfo &info);
 
    static bool CheckFamilyMemberControl(const std::string& node, const std::string& account);

    static int GetPeronalAccountCnfByCommunityProjectID(uint32_t project_id, PersonalAccountCnfInfoMapPtr &node_map);
private:
};

}
#endif
