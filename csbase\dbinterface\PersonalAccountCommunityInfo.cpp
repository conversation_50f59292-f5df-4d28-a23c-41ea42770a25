#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/PersonalAccountCommunityInfo.h"
#include <string.h>
#include "AkLogging.h"
#include "ConnectionManager.h"
#include "AkcsCommonDef.h"
#include "util.h"
namespace dbinterface
{

static const std::string apt_building_access_floor_info_sec = "IsAllBuilding,CommunityUnitUUID,AccessFloor ";

PersonalAccountCommunityInfo::PersonalAccountCommunityInfo()
{
    
}


std::string PersonalAccountCommunityInfo::GetFloorByUUID(const std::string& uuid)
{
    std::string floor;
    std::stringstream stream_sql;
    stream_sql << "select AccessFloor from PersonalAccountCommunityInfo where PersonalAccountUUID = '" << uuid << "'";

    GET_DB_CONN_ERR_RETURN(tmp_conn, "") //获取数据库连接失败时，返回空字符串
    CRldbQuery query(tmp_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        floor = query.GetRowData(0);
    }

    return floor;
}

std::string PersonalAccountCommunityInfo::GetFloorByAccount(const std::string& account)
{
    std::string floor;
    std::stringstream stream_sql;
    stream_sql << "select AccessFloor from PersonalAccountCommunityInfo P left join PersonalAccount A on P.PersonalAccountUUID = A.UUID "
               << "where A.Account = '" << account << "'";

    GET_DB_CONN_ERR_RETURN(tmp_conn, "") //获取数据库连接失败时，返回空字符串
    CRldbQuery query(tmp_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        floor = query.GetRowData(0);
    }
    return floor;
}

std::string PersonalAccountCommunityInfo::GetAptBuildingAccessFloorInfoListByUUID(const std::string& user_uuid, const std::string& user_unit_uuid, std::string &apt_floor, const DEVICE_SETTING &dev)
{
    std::stringstream stream_sql;
    stream_sql << "select " << apt_building_access_floor_info_sec << " from PersonalAccountCommunityInfo where PersonalAccountUUID = '" << user_uuid << "'";

    GET_DB_CONN_ERR_RETURN(tmp_conn, "") //获取数据库连接失败时，返回空字符串
    CRldbQuery query(tmp_conn.get());
    query.Query(stream_sql.str());
    // 默认楼层
    std::string floor = kDefaultFloor;

    // 处理楼栋设备或公共设备逻辑
    if (csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT == dev.grade)
    {
        while (query.MoveToNextRow())
        {
            int selected_is_all_building = ATOI(query.GetRowData(0));
            std::string selected_community_unit_uuid = query.GetRowData(1);
            std::string selected_building_floor = query.GetRowData(2);
            
            if (selected_is_all_building)
            {
                floor = GetAccessibleFloor(apt_floor, selected_building_floor);
                break;
            } 
            else if (selected_community_unit_uuid == dev.community_unit_uuid) 
            {
                // 若非勾选全部楼栋，则取房间所在楼栋的楼层权限+房间的楼层权限
                if (user_unit_uuid == dev.community_unit_uuid) {
                    floor = GetAccessibleFloor(apt_floor, selected_building_floor);
                } else {
                    floor = selected_building_floor;
                }
                break;
            }
        }
    } 
    else if (csmain::COMMUNITY_DEVICE_TYPE_PUBLIC == dev.grade) 
    {
        floor = apt_floor;
        while (query.MoveToNextRow())
        {
            int selected_is_all_building = ATOI(query.GetRowData(0));
            std::string selected_community_unit_uuid = query.GetRowData(1);
            std::string selected_building_floor = query.GetRowData(2);

            // 若用户楼栋和选择的楼栋一致，则取房间所在楼栋的楼层权限+勾选的楼层权限
            if (selected_is_all_building || selected_community_unit_uuid == user_unit_uuid)
            {
                floor = GetAccessibleFloor(apt_floor, selected_building_floor);
                break;
            }
        }
    }

    return floor;
}



}
