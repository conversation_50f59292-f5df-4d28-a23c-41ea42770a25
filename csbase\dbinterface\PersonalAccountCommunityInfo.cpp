#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/PersonalAccountCommunityInfo.h"
#include <string.h>
#include "AkLogging.h"
#include "ConnectionManager.h"
namespace dbinterface
{

PersonalAccountCommunityInfo::PersonalAccountCommunityInfo()
{
    
}


std::string PersonalAccountCommunityInfo::GetFloorByUUID(const std::string& uuid)
{
    std::string floor;
    std::stringstream stream_sql;
    stream_sql << "select AccessFloor from PersonalAccountCommunityInfo where PersonalAccountUUID = '" << uuid << "'";

    GET_DB_CONN_ERR_RETURN(tmp_conn, "") //获取数据库连接失败时，返回空字符串
    CRldbQuery query(tmp_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        floor = query.GetRowData(0);
    }

    return floor;
}

std::string PersonalAccountCommunityInfo::GetFloorByAccount(const std::string& account)
{
    std::string floor;
    std::stringstream stream_sql;
    stream_sql << "select AccessFloor from PersonalAccountCommunityInfo P left join PersonalAccount A on P.PersonalAccountUUID = A.UUID "
               << "where A.Account = '" << account << "'";

    GET_DB_CONN_ERR_RETURN(tmp_conn, "") //获取数据库连接失败时，返回空字符串
    CRldbQuery query(tmp_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        floor = query.GetRowData(0);
    }
    return floor;
}


}
