#ifndef __DB_PERSONAL_ACCOUNT_COMMUNITY_INFO_H__
#define __DB_PERSONAL_ACCOUNT_COMMUNITY_INFO_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include <AkcsCommonDef.h>
#include "AkcsCommonSt.h"

namespace dbinterface
{
typedef struct AptBuildingAccessFloorInfo_T
{
    int is_all_building;
    char unit_uuid[64];
    char floor[64];

    AptBuildingAccessFloorInfo_T() {
        memset(this, 0, sizeof(*this));
    }
}AptBuildingAccessFloorInfo;

typedef std::vector<AptBuildingAccessFloorInfo> AptBuildingAccessFloorInfoList;


class PersonalAccountCommunityInfo
{
public:
    PersonalAccountCommunityInfo();
    ~PersonalAccountCommunityInfo();

    static std::string GetFloorByUUID(const std::string& uuid);
    static std::string GetFloorByAccount(const std::string& account);
    static std::string GetAptBuildingAccessFloorInfoListByUUID(const std::string &user_uuid, const std::string &user_unit_uuid, std::string &apt_floor, const DEVICE_SETTING &dev);
private:
    
};

}
#endif


