#ifndef __DB_PERSONAL_ACCOUNT_COMMUNITY_INFO_H__
#define __DB_PERSONAL_ACCOUNT_COMMUNITY_INFO_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include <AkcsCommonDef.h>

namespace dbinterface
{


class PersonalAccountCommunityInfo
{
public:
    PersonalAccountCommunityInfo();
    ~PersonalAccountCommunityInfo();

    static std::string GetFloorByUUID(const std::string& uuid);
    static std::string GetFloorByAccount(const std::string& account);
private:
    
};

}
#endif


