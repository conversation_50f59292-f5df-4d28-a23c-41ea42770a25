#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include <string.h>
#include "AkLogging.h"
#include "util.h"
#include "PersonalAccountSingleInfo.h"


namespace dbinterface
{

PersonalAccountSingleInfo::PersonalAccountSingleInfo()
{

}

int PersonalAccountSingleInfo::GetSingleInfoByUUID(const std::string& uuid, SingleInfo& single_info)
{
    std::stringstream stream_sql;
    stream_sql << "select Country, States, City, PostalCode, Street from PersonalAccountSingleInfo where PersonalAccountUUID= '" << uuid << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        Snprintf(single_info.country, sizeof(single_info.country), query.GetRowData(0));
        Snprintf(single_info.states, sizeof(single_info.states), query.GetRowData(1));
        Snprintf(single_info.city, sizeof(single_info.city), query.GetRowData(2));
        Snprintf(single_info.postal_code, sizeof(single_info.postal_code), query.GetRowData(3));
        Snprintf(single_info.street, sizeof(single_info.street), query.GetRowData(4));
    }   
    else 
    {
        ReleaseDBConn(conn);
        return -1;
    }

    ReleaseDBConn(conn);
    return 0;
}


}



