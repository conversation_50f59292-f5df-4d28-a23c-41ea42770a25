#ifndef __DB_PERSONAL_ACCOUNT_SINGLE_INFO_H__
#define __DB_PERSONAL_ACCOUNT_SINGLE_INFO_H__
#include <string>
#include <memory>

namespace dbinterface
{


typedef struct SingleInfo_T
{
    char city[128];
    char states[128];
    char street[128];
    char country[128];
    char postal_code[32];
}SingleInfo;
    
class PersonalAccountSingleInfo
{
public:
    PersonalAccountSingleInfo();
    ~PersonalAccountSingleInfo();
    
    static int GetSingleInfoByUUID(const std::string& uuid, SingleInfo& single_info);
private:

};



}
#endif

