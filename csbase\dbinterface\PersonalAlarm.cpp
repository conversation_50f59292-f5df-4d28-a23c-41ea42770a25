#include <sstream>
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/UUID.h"
#include "PersonalAlarm.h"
#include <string.h>
#include "AkLogging.h"
#include <boost/algorithm/string.hpp>
#include "util.h"

namespace dbinterface{
PersonalAlarm::PersonalAlarm()
{

}

PersonalAlarm::~PersonalAlarm()
{

}

//处理ALARM
int PersonalAlarm::DealAlarm(PERSONNAL_ALARM* alarm)
{
    if (alarm == NULL)
    {
        return -1;
    }

    std::string DealUser = alarm->deal_user;
    boost::replace_all(DealUser, "'", "\\'");

    char sql[2048] = {0};
    ::snprintf(sql, 2048, "update PersonalAlarms set Status=%d,DealType='%d',DealResult='%s',DealUser='%s',DealTime=localtime() where ID=%d",
          alarm->status,
          alarm->deal_type,
          alarm->deal_result,
          DealUser.c_str(),
          alarm->id);
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    int ret = conn->Execute(sql) > 0 ? 0 : -1;
    //释放数据库连接
    ReleaseDBConn(conn);
    return ret;
}

//添加ALARM
int PersonalAlarm::AddAlarm(PERSONNAL_ALARM& personnal_alarm, const std::string server_tag)
{
    std::string uuid;
    dbinterface::UUID::GenerateUUID(server_tag, uuid);
    Snprintf(personnal_alarm.uuid, sizeof(personnal_alarm.uuid), uuid.c_str());
    
    //插入数据构造
    std::map<std::string, std::string> str_map;
    str_map.emplace("AlarmType", personnal_alarm.alarm_type);
    str_map.emplace("Community", personnal_alarm.community);
    str_map.emplace("Node", personnal_alarm.device_node);
    str_map.emplace("sql_AlarmTime", "now()");
    str_map.emplace("DevicesMAC", personnal_alarm.mac);
    str_map.emplace("UUID", personnal_alarm.uuid);

    std::map<std::string, int> int_map;
    int_map.emplace("Extension", personnal_alarm.extension);
    int_map.emplace("Status", personnal_alarm.status);
    int_map.emplace("AlarmCode", personnal_alarm.alarm_code);
    int_map.emplace("AlarmZone", personnal_alarm.alarm_zone);
    int_map.emplace("AlarmLocation", personnal_alarm.alarm_location);
    int_map.emplace("AlarmCustomize", personnal_alarm.alarm_customize);

    std::string table_name = "PersonalAlarms";//插入表名
   
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    int ret = conn->InsertData(table_name, str_map, int_map);
    //added by chenyc,2017-06-02,获取本回话最新插入的数据列ID,支持向app发送该id的告警消息.
    //因为SELECT last_insert_id()结果是基于会话的，所以即使有其他线程切换,再回来也是本线程上次执行的结果.
    char sql2[512] = {0};
    ::snprintf(sql2, 512,"SELECT last_insert_id()");

    CRldbQuery query(tmp_conn);
    query.Query(sql2);
    if (query.MoveToNextRow())
    {
        personnal_alarm.id = ATOI(query.GetRowData(0));
    }

    //释放数据库连接
    ReleaseDBConn(conn);
    return ret;
}

//更新告警状态
int PersonalAlarm::DealAlarmStatus(const PERSONAL_ALARM_DEAL_INFO& alarm_deal_info)
{
    std::string user = alarm_deal_info.user;
    boost::replace_all(user, "'", "\\'");

    char sql[512] = {0};
    ::snprintf(sql, 512,"update PersonalAlarms set Status=1,DealType=0,DealResult='%s',DealUser='%s',DealTime=%s,DealResult='Deal' where ID=%d",
                  alarm_deal_info.result,
                  user.c_str(),
                  /*alarm_deal_info.time*/"now()",
                  ATOI(alarm_deal_info.alarm_id));
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    int ret = conn->Execute(sql) > 0 ? 0 : -1;
    //释放数据库连接
    ReleaseDBConn(conn);
    return ret;
}

//通过alarm id获取到告警解除时的相关信息
int PersonalAlarm::GetAlarmInfo(const std::string& id, PERSONAL_ALARM_DEAL_OFFLINE_INFO& alarm_info)
{
    uint32_t alarm_id = ATOI(id.c_str());
    char sql[512] = {0};
    ::snprintf(sql, 512,"/*master*/select A.AlarmType,A.DevicesMAC, D.Location,A.Community,A.AlarmCode,A.AlarmZone,A.AlarmLocation,A.AlarmCustomize,A.TraceID,D.Node from PersonalAlarms A \
                    left join PersonalDevices D on A.DevicesMAC = D.MAC \
                    where A.ID = %d", alarm_id);

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(conn.get());

    query.Query(sql);
    if (query.MoveToNextRow())
    {
        Snprintf(alarm_info.alarm_type, sizeof(alarm_info.alarm_type), query.GetRowData(0));
        Snprintf(alarm_info.mac, sizeof(alarm_info.mac), query.GetRowData(1));
        Snprintf(alarm_info.device_location, sizeof(alarm_info.device_location), query.GetRowData(2));
        Snprintf(alarm_info.community, sizeof(alarm_info.community), query.GetRowData(3));
        alarm_info.alarm_code = ATOI(query.GetRowData(4));
        alarm_info.alarm_zone = ATOI(query.GetRowData(5));
        alarm_info.alarm_location = ATOI(query.GetRowData(6));
        alarm_info.alarm_customize = ATOI(query.GetRowData(7));
        char *end_ptr;
        alarm_info.trace_id = std::strtoull(query.GetRowData(8), &end_ptr, 10);
        Snprintf(alarm_info.device_node, sizeof(alarm_info.device_node), query.GetRowData(9));
        ReleaseDBConn(conn);
        return 0;
    }
    ReleaseDBConn(conn);
    return -1;
}





}


