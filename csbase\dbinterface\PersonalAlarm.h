#ifndef __DB_PERSONAL_ALARM_H__
#define __DB_PERSONAL_ALARM_H__

#include <string>
#include <memory>
#include <tuple>

typedef struct PERSONNAL_ALARM_T
{
    uint32_t id;
    uint32_t extension;
    uint32_t status;
    uint32_t deal_type;
    TCHAR alarm_type[64];
    TCHAR community[32];
    TCHAR device_node[24];
    TCHAR alarm_time[24];
    TCHAR deal_time[24];
    TCHAR deal_user[32];
    TCHAR deal_result[1024];
    TCHAR mac[20];
    int alarm_code;//告警类型 用于程序判断和多语言展示判断
    int alarm_zone;//防区
    int alarm_location;//位置
    int alarm_customize;//1 设备自定义alarm
    TCHAR uuid[64];
    uint64_t trace_id;
} PERSONNAL_ALARM;


typedef struct PERSONAL_ALARM_DEAL_T
{
    char protocal[16];
    char area_node[32];
    char alarm_id[16];
    char user[32];
    char result[64];
    char type[64];//告警的处理类型
    char time[24];
    char device_name[64];
    char community[32];
    uint32_t manager_account_id;
    int alarm_code;
    int alarm_zone;
    int alarm_location;
    int alarm_customize;//1 设备自定义alarm
    uint32_t unit_id;
} PERSONAL_ALARM_DEAL_INFO;

typedef struct PERSONAL_ALARM_DEAL_OFFLINE_T
{
    char mac[20];
    char alarm_type[64];
    char device_location[64];
    char community[32];
    int alarm_code;
    int alarm_location;
    int alarm_customize;//1 设备自定义alarm
    int alarm_zone;
    uint64_t trace_id;
    char device_node[24];
} PERSONAL_ALARM_DEAL_OFFLINE_INFO;

namespace dbinterface{
class PersonalAlarm
{
public:
    PersonalAlarm();
    ~PersonalAlarm();
    static int DealAlarm(PERSONNAL_ALARM* alarm);
    static int AddAlarm(PERSONNAL_ALARM& alarm, const std::string server_tag);
    static int DealAlarmStatus(const PERSONAL_ALARM_DEAL_INFO& alarm_deal_info);
    static int GetAlarmInfo(const std::string& id, PERSONAL_ALARM_DEAL_OFFLINE_INFO& alarm_info);
private:
};

}


#endif
