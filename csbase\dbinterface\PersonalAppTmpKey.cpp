#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "PersonalAppTmpKey.h"
#include <string.h>
#include "AkLogging.h"
#include "AkcsCommonDef.h"
#include "dbinterface/PerNodeDevices.h"
#include "dbinterface/CommunityRoom.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "ConnectionManager.h"

namespace dbinterface
{

PersonalAppTmpKey::PersonalAppTmpKey()
{

}

//查找tmp_key的用户信息
int PersonalAppTmpKey::GetUserInfoFromAppTempKey(const std::string& node, const std::string& code, PersonalTempKeyUserInfo &tmpkey_info)
{
    char sql[1024] = {0};
    ::snprintf(sql, 1024, "select Description,Node,Creator,Type From PersonalAppTmpKey where Node='%s' and TmpKey='%s'",
               node.c_str(), code.c_str());

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
    std::string sql2 = sql;
    query.Query(sql2);

    if (query.MoveToNextRow())
    {
       tmpkey_info.name = query.GetRowData(0);
       tmpkey_info.node = query.GetRowData(1);
       tmpkey_info.creator = query.GetRowData(2);
       tmpkey_info.type = ATOI(query.GetRowData(3));
    }
    if (query.MoveToNextRow())
    {
        AK_LOG_WARN << "The Tmpkey " << code << " maybe have more than one row";
    }
    ReleaseDBConn(conn);
    return 0;

}

//通过code查找tmpkey 所属者
std::string PersonalAppTmpKey::GetNameFromAppTmpkeyForCommunityPubWork(const std::string& code, int mng_id, const std::string& mac, std::string& creator)
{
    std::string name;
    char sql[1024] = {0};
    //PubAppTmpKey查出来的用户信息一定是社区的
    ::snprintf(sql, 1024, "select P.Description,C.Account from PubAppTmpKey P left join PubAppTmpKeyList  L \
    on L.KeyID=P.ID left join PersonalAccount C on C.ID = P.PersonalAccountID where L.mac='%s' and P.MngAccountID='%d' and P.Code='%s';", mac.c_str(), mng_id, code.c_str());
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return name;
    }
    CRldbQuery query(tmp_conn);
    std::string sql2 = sql;
    query.Query(sql2);

    if (query.MoveToNextRow())
    {
        name = query.GetRowData(0);
        if(query.GetRowData(1))
        {
            creator = query.GetRowData(1);
        }
    }
    ReleaseDBConn(conn);
    return name;

}

//查找tmp_key的用户信息
int PersonalAppTmpKey::GetUserInfoFromAppTempKeyForCommunityPubWork(int grade, const std::string& code, int unit_id, int mng_id, PersonalTempKeyUserInfo &tmpkey_info)
{
    char sql[1024] = {0};

    if (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
        ::snprintf(sql, 1024, "select Description,Node,Creator,Type from PersonalAppTmpKey \
        WHERE MngAccountID='%d' and  TmpKey='%s' and UnitID='%d' limit 1", mng_id, code.c_str(), unit_id);
    }
    else if (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
    {
        ::snprintf(sql, 1024, "select Description,Node,Creator,Type from PersonalAppTmpKey \
        WHERE MngAccountID='%d'  and TmpKey='%s' limit 1", mng_id, code.c_str());
    }

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
    std::string sql2 = sql;
    query.Query(sql2);

    if (query.MoveToNextRow())
    {
        tmpkey_info.name = query.GetRowData(0);
        tmpkey_info.node = query.GetRowData(1);
        tmpkey_info.creator = query.GetRowData(2);
        tmpkey_info.type = ATOI(query.GetRowData(3));
    }
    ReleaseDBConn(conn);
    return 0;

}

std::string PersonalAppTmpKey::GetNameFromAppTmpkeyForOfficePubWork(const std::string& code, int mng_id, const std::string& mac, std::string& creator)
{
    std::string name;
    char sql[1024] = {0};
    ::snprintf(sql, 1024, "select P.Description,C.Account from PubAppTmpKey P left join PubAppTmpKeyList  L \
    on L.KeyID=P.ID left join PersonalAccount C on C.ID = P.PersonalAccountID where L.mac='%s' and P.MngAccountID='%d' and P.Code='%s' ;", mac.c_str(), mng_id, code.c_str());
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return name;
    }
    CRldbQuery query(tmp_conn);
    std::string sql2 = sql;
    query.Query(sql2);

    if (query.MoveToNextRow())
    {
        name = query.GetRowData(0);
        creator = query.GetRowData(1);
    }
    ReleaseDBConn(conn);
    return name;

}

//通过code查找持卡人的昵称
std::string PersonalAppTmpKey::GetNameAndNodeFromTmpkeyForCommunityPubPersonal(int grade, const std::string& code, int unit_id, int mng_id, std::string& node, std::string& creator)
{
    std::string name;
    char sql[1024] = {0};


    if (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
        ::snprintf(sql, 1024, "select Description,Node,Creator from PersonalAppTmpKey \
        WHERE MngAccountID='%d' and TmpKey='%s' and UnitID='%d' limit 1", mng_id, code.c_str(), unit_id);
    }
    else if (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
    {
        ::snprintf(sql, 1024, "select Description,Node,Creator from PersonalAppTmpKey \
        WHERE MngAccountID='%d' and TmpKey='%s' limit 1", mng_id, code.c_str());
    }

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return name;
    }
    CRldbQuery query(tmp_conn);
    std::string sql2 = sql;
    query.Query(sql2);

    if (query.MoveToNextRow())
    {
        name = query.GetRowData(0);
        node = query.GetRowData(1);
        creator = query.GetRowData(2);
    }
    ReleaseDBConn(conn);
    return name;

}

//PersonalAppTmpKey表：用户家庭设备 校验临时秘钥
bool PersonalAppTmpKey::CheckPersonalAppTmpKeyByPerDev(SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& check_tmpkey, std::map<std::string, AKCS_DST>& dst)
{
    bool ret = FALSE;
    int room_id = 0;
    std::string time_zone;
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return ret;
    }

    CRldbQuery query(tmp_conn);
    std::stringstream sql_stream;
    //校验设备与TmpKey的绑定关系
    sql_stream << "SELECT K.SchedulerType,P.TimeZone,P.RoomID,P.Role,A.TimeZone,K.Node,L.Relay,L.SecurityRelay,K.Creator FROM PersonalAppTmpKey" ;
    sql_stream << " K left join PersonalAccount P on K.Node = P.Account left join Account A on A.ID = P.ParentID";
    sql_stream << " INNER JOIN PersonalAppTmpKeyList L on L.KeyID=K.ID and L.MAC='";
    sql_stream << check_tmpkey.mac << "'";
    sql_stream << " WHERE K.TmpKey = '";
    sql_stream << check_tmpkey.tmpkey;
    sql_stream << "' LIMIT 1";
    query.Query(sql_stream.str());
    if (query.MoveToNextRow())
    {
        int role = ATOI(query.GetRowData(3));
        check_tmpkey.sche_type = ATOI(query.GetRowData(0));
        if(ACCOUNT_ROLE_PERSONNAL_MAIN == role || ACCOUNT_ROLE_PERSONNAL_ATTENDANT == role)
        {
            time_zone = query.GetRowData(1);
        }
        else    //社区校验需统一使用社区时区
        {
            time_zone = query.GetRowData(4);
        }
        room_id = ATOI(query.GetRowData(2));
        Snprintf(check_tmpkey.node, sizeof(check_tmpkey.node), query.GetRowData(5));
        check_tmpkey.relay_value = ATOI(query.GetRowData(6));
        check_tmpkey.security_relay_value = ATOI(query.GetRowData(7));
        Snprintf(check_tmpkey.account, sizeof(check_tmpkey.account), query.GetRowData(8));
    }
    else
    {
        //AK_LOG_INFO << "CheckPersonalAppTmpKeyByPerDev not match: " << check_tmpkey.tmpkey << ";SQL=" << sql_stream.str();
        ReleaseDBConn(conn);
        return ret;
    }

    //校验TmpKey时效性
    ret = IsPersonalAppTmpKeyValid(tmp_conn, check_tmpkey, time_zone, dst);        
    ReleaseDBConn(conn);

    GetUnitAptByRoomID(room_id, check_tmpkey);
    return ret;
}

//PersonalAppTmpKey表：社区公共设备 校验临时秘钥
bool PersonalAppTmpKey::CheckPersonalAppTmpKeyByPubDev(SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& check_tmpkey, std::map<std::string, AKCS_DST>& dst)
{
    int room_id = 0;
    std::string time_zone;
    bool ret = FALSE;
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return ret;
    }
    CRldbQuery query(tmp_conn);

    std::stringstream sql_stream;
    //校验设备与TmpKey的绑定关系
    sql_stream << "SELECT K.SchedulerType,A.TimeZone,P.RoomID,P.UnitID,K.Node,L.Relay,L.SecurityRelay,K.Creator FROM PersonalAppTmpKey" ;
    sql_stream << " K left join PersonalAccount P on K.Node = P.Account ";
    sql_stream << " INNER JOIN PersonalAppTmpKeyList L on L.KeyID=K.ID and L.MAC='";
    sql_stream << check_tmpkey.mac << "'";
    sql_stream <<  " left join Account A on A.ID = K.MngAccountID WHERE K.TmpKey = '";
    sql_stream << check_tmpkey.tmpkey;
    sql_stream << "' LIMIT 1";
    query.Query(sql_stream.str());
    if (query.MoveToNextRow())
    {
        check_tmpkey.sche_type = ATOI(query.GetRowData(0));
        time_zone = query.GetRowData(1);
        room_id = ATOI(query.GetRowData(2));
        check_tmpkey.personal_unit_id = ATOI(query.GetRowData(3));
        Snprintf(check_tmpkey.node, sizeof(check_tmpkey.node), query.GetRowData(4));
        check_tmpkey.relay_value = ATOI(query.GetRowData(5));
        check_tmpkey.security_relay_value = ATOI(query.GetRowData(6));
        Snprintf(check_tmpkey.account, sizeof(check_tmpkey.account), query.GetRowData(7));
    }
    else
    {
        //AK_LOG_INFO << "CheckPersonalAppTmpKeyByPubDev not match: " << check_tmpkey.tmpkey << ";SQL=" << sql_stream.str();
        ReleaseDBConn(conn);
        return ret;
    }

    //校验TmpKey时效性
    ret = IsPersonalAppTmpKeyValid(tmp_conn, check_tmpkey, time_zone, dst);    //校验TmpKey时效性    
    ReleaseDBConn(conn); 

    GetUnitAptByRoomID(room_id, check_tmpkey);
    return ret;

}

//PubAppTmpKey表：社区设备 校验临时秘钥 
bool PersonalAppTmpKey::CheckPubAppTmpKey(SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& check_tmpkey, std::map<std::string, AKCS_DST>& dst)
{
    std::string time_zone;
    int room_id = 0;
    bool ret = FALSE;
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return ret;
    }
    CRldbQuery query(tmp_conn);

    std::stringstream sql_stream;
    //校验设备与TmpKey的绑定关系
    sql_stream << "SELECT P.SchedulerType,A.TimeZone,L.Relay,H.RoomID,H.UnitID,L.SecurityRelay,H.Account FROM PubAppTmpKey P";
    sql_stream << " INNER JOIN PubAppTmpKeyList L on L.KeyID=P.ID" ;
    sql_stream << " INNER JOIN Account A on P.MngAccountID=A.ID" ;
    sql_stream << " LEFT JOIN PersonalAccount H on P.PersonalAccountID=H.ID ";
    sql_stream << " WHERE L.Mac = '";
    sql_stream << check_tmpkey.mac;
    sql_stream << "' AND P.Code = '";
    sql_stream << check_tmpkey.tmpkey;
    sql_stream << "' LIMIT 1";
    query.Query(sql_stream.str());
    if (query.MoveToNextRow())
    {
        check_tmpkey.sche_type = ATOI(query.GetRowData(0));
        time_zone = query.GetRowData(1);
        check_tmpkey.relay_value = ATOI(query.GetRowData(2));
        room_id = ATOI(query.GetRowData(3));
        check_tmpkey.personal_unit_id = ATOI(query.GetRowData(4));
        check_tmpkey.is_pmcreate = 1;
        check_tmpkey.security_relay_value = ATOI(query.GetRowData(5));
        Snprintf(check_tmpkey.account, sizeof(check_tmpkey.account), query.GetRowData(6));
        Snprintf(check_tmpkey.node, sizeof(check_tmpkey.node), query.GetRowData(6));
    }
    else
    {
        //AK_LOG_INFO << "CheckPubAppTmpKey not match: " << check_tmpkey.tmpkey << ";SQL=" << sql_stream.str();
        ReleaseDBConn(conn);
        return ret;
    }

    //校验TmpKey时效性
    ret = IsPubAppTmpKeyValid(tmp_conn, check_tmpkey, time_zone, dst);
    ReleaseDBConn(conn);

    GetUnitAptByRoomID(room_id, check_tmpkey);
    return ret;
}

void PersonalAppTmpKey::GetUnitAptByRoomID(int room_id, SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& check_tmpkey)
{
    CommunityRoomInfo room_info;
    if (0 == dbinterface::CommunityRoom::GetCommunityRoomByID(room_id, room_info))
    {
        check_tmpkey.unit_id = room_info.unit_id;
        Snprintf(check_tmpkey.room_num, sizeof(check_tmpkey.room_num), room_info.room_number);
        Snprintf(check_tmpkey.floor, sizeof(check_tmpkey.floor), room_info.floor);
        if (strlen(check_tmpkey.floor) == 0)
        {
            //若Floor字段为空,则unit_apt中apt值为RoomName
            snprintf(check_tmpkey.unit_apt, sizeof(check_tmpkey.unit_apt), "%u-%s", check_tmpkey.unit_id, ExtractFirstNumber(check_tmpkey.room_num).c_str());
        }            
        else
        {
            //若Floor字段不为空,则unit_apt中apt值为"Floor+00"
            snprintf(check_tmpkey.unit_apt, sizeof(check_tmpkey.unit_apt), "%u-%s%s", check_tmpkey.unit_id, check_tmpkey.floor, "00");
        }
    }
}

//单住户公共设备 校验临时秘钥
//Noted by czw: 现已不可添加单住户公共设备,故基本不维护了,尽量不修改到
bool PersonalAppTmpKey::CheckTmpKeyBySingleTenantPubDev(SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& check_tmpkey, std::map<std::string, AKCS_DST>& dst)
{
    std::string time_zone;
    std::vector<PER_NODE_DEVICES> oPerNodes;
    dbinterface::PerNodeDevices::GetNodesByPublicDevID(check_tmpkey.personal_public_device_id, oPerNodes);
    std::stringstream streamNodes;
    for (auto& node : oPerNodes)
    {
        streamNodes << node.node << ",";
    }
    std::string  nodes = streamNodes.str();
    if (nodes.length())
    {
        nodes = nodes.substr(0, nodes.length() - 1);
    }
    bool ret = FALSE;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return ret;
    }
    CRldbQuery query(tmp_conn);
    std::stringstream sql_stream;
    sql_stream << "SELECT K.SchedulerType,P.TimeZone,L.Relay,L.SecurityRelay, P.Account FROM PersonalAppTmpKey" ;
    sql_stream << " K left join PersonalAccount P on K.Node = P.Account";
    sql_stream << " INNER JOIN PersonalAppTmpKeyList L on L.KeyID=K.ID and L.MAC='";
    sql_stream << check_tmpkey.mac << "'";
    sql_stream << " WHERE K.TmpKey = '";
    sql_stream << check_tmpkey.tmpkey;
    sql_stream << "' AND K.Node in( ";
    sql_stream << nodes;
    sql_stream << ") LIMIT 1";
    query.Query(sql_stream.str());
    if (query.MoveToNextRow())
    {
        check_tmpkey.sche_type = ATOI(query.GetRowData(0));
        time_zone = query.GetRowData(1);
        check_tmpkey.relay_value = ATOI(query.GetRowData(2));
        check_tmpkey.security_relay_value = ATOI(query.GetRowData(3));
        Snprintf(check_tmpkey.node, sizeof(check_tmpkey.node), query.GetRowData(4));
    }
    else
    {
        ReleaseDBConn(conn);
        return FALSE;
    }

    if (ONCE_SCHED == check_tmpkey.sche_type || EACH_DOOR_ONCE_SCHED == check_tmpkey.sche_type)
    {
        std::string tmp_time = GetNodeNowDateTimeByTimeZoneStr(time_zone, dst);
        std::stringstream streamSQL;
        streamSQL << "SELECT K.ID,K.AccessTimes FROM PersonalAppTmpKey" ;
        streamSQL << " K INNER JOIN PersonalAppTmpKeyList L on L.KeyID=K.ID  and L.MAC = '";
        streamSQL << check_tmpkey.mac << "'";
        streamSQL << " WHERE TmpKey = '";
        streamSQL << check_tmpkey.tmpkey;
        streamSQL << "' AND Node in( ";
        streamSQL << nodes;
        streamSQL << ")";
        streamSQL << " AND BeginTime < '";
        streamSQL << tmp_time;
        streamSQL << "' AND EndTime > '";
        streamSQL << tmp_time;
        streamSQL << "' AND AccessTimes < AllowedTimes LIMIT 1";

        query.Query(streamSQL.str());
        //联动单元的设备mac地址列表
        if (query.MoveToNextRow())
        {
            int ID = ATOI(query.GetRowData(0));
            int access_time = ATOI(query.GetRowData(1)) + 1;
            ret = TRUE;
            check_tmpkey.key_table_type = PER_TMP_KEY;
            check_tmpkey.access_times = access_time;
            check_tmpkey.key_id = ID;
        }
        else
        {
            ret = FALSE;
        }
    }

    else if (WEEKLY_SCHED == check_tmpkey.sche_type)
    {
        string tmp_time;
        int day_of_week = GetWeekDayAndTimeByTimeZoneStr(time_zone, tmp_time, dst);
        unsigned int date_flag = kWeek_day[day_of_week];

        std::stringstream streamSQL;
        streamSQL << "SELECT K.DateFlag FROM PersonalAppTmpKey" ;
        streamSQL << " K INNER JOIN PersonalAppTmpKeyList L on L.KeyID=K.ID and L.MAC = '";
        streamSQL << check_tmpkey.mac << "'";
        streamSQL << " WHERE TmpKey = '";
        streamSQL << check_tmpkey.tmpkey;
        streamSQL << "' AND Node in( ";
        streamSQL << nodes;
        streamSQL << ")";
        streamSQL << " AND StartTime < '";
        streamSQL << tmp_time;
        streamSQL << "' AND StopTime > '";
        streamSQL << tmp_time;
        streamSQL << "' LIMIT 1";

        query.Query(streamSQL.str());
        //联动单元的设备mac地址列表
        if (query.MoveToNextRow())
        {
            int tmp_flag = ATOI(query.GetRowData(0));
            if ((date_flag & tmp_flag) == date_flag)
            {
                ret = TRUE;
            }
            else
            {
                ret = FALSE;
            }
        }
        else
        {
            ret = FALSE;
        }
    }

    else
    {
        string tmp_time;
        GetWeekDayAndTimeByTimeZoneStr(time_zone, tmp_time, dst);
        std::stringstream streamSQL;
        streamSQL << "SELECT K.ID FROM PersonalAppTmpKey" ;
        streamSQL << " K INNER JOIN PersonalAppTmpKeyList L on L.KeyID=K.ID and L.MAC='";
        streamSQL << check_tmpkey.mac << "'";
        streamSQL << " WHERE TmpKey = '";
        streamSQL << check_tmpkey.tmpkey;
        streamSQL << "' AND Node in( ";
        streamSQL << nodes;
        streamSQL << ")";
        streamSQL << " AND StartTime < '";
        streamSQL << tmp_time;
        streamSQL << "' AND StopTime > '";
        streamSQL << tmp_time;
        streamSQL << "' LIMIT 1";

        query.Query(streamSQL.str());
        //联动单元的设备mac地址列表
        if (query.MoveToNextRow())
        {
            ret = TRUE;
        }
        else
        {
            ret = FALSE;
        }
    }

    ReleaseDBConn(conn);
    return ret;

}

int PersonalAppTmpKey::AddTempKey(const SOCKET_MSG_DEV_REPORT_VISITOR& dev_visitor_info, const std::vector<std::string>& dev, std::map<std::string, AKCS_DST>& dst)
{
    int sql_id = 0;
    int ret = -1;
    int community_id;
    int unit_id;
    std::string time_zone;
    ResidentPerAccount account;
    if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(dev_visitor_info.account, account))
    {
        community_id = account.parent_id;
        unit_id = account.unit_id;
        time_zone = account.timezone;
    }
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);

    std::string now_time = GetNodeNowDateTimeByTimeZoneStr(time_zone, dst);
    std::string offset_time = GetOffsetDateTimeByTimeZoneStr(time_zone, 12 * 3600); //访客系统TempKey时效为12小时

    tmp_conn->BeginTransAction();
    std::stringstream strSQL2;
    strSQL2 << "insert into PersonalAppTmpKey(MngAccountID,UnitID,TmpKey,Node,BeginTime,EndTime,AllowedTimes,DeliveryTXT,Description,IDNumber,Creator,SchedulerType,Type) values('"
        << community_id << "','"
        << unit_id << "','"
        << dev_visitor_info.tempkey_code << "','"
        << dev_visitor_info.account << "','"
        << now_time.c_str() << "','"
        << offset_time.c_str() << "','"
        << dev_visitor_info.count << "','"
        << dev_visitor_info.email << "','"
        << dev_visitor_info.visitor << "','"
        << dev_visitor_info.id_number << "','"
        << dev_visitor_info.account << "',0,0)";

    ret = conn->Execute(strSQL2.str()) > 0 ? 0 : -1;
    if (ret < 0)
    {
        AK_LOG_WARN << "insert into PersonalAppTmpKey failed.";
        tmp_conn->TransActionRollback();
        ReleaseDBConn(conn);
        return -1;
    }

    std::stringstream strSQL3;
    strSQL3 << "SELECT last_insert_id()";
    query.Query(strSQL3.str());
    if (query.MoveToNextRow())
    {
        sql_id = ATOI(query.GetRowData(0));
    }
    else
    {
        AK_LOG_WARN << "select last_insert_id failed.";
        tmp_conn->TransActionRollback();
        ReleaseDBConn(conn);
        return -1;
    }

    for(const auto& mac : dev)
    {
        std::stringstream sql;
        sql << "insert into PersonalAppTmpKeyList(MAC,KeyID) values('" << mac << "'," << sql_id << ")";
        ret = conn->Execute(sql.str()) >= 0 ? 0 : -1;
        if(ret < 0)
        {
            AK_LOG_WARN << "insert PersonalAppTmpKeyList failed.";
            tmp_conn->TransActionRollback();
            ReleaseDBConn(conn);
            return -1;
        }
    }
    
    tmp_conn->EndTransAction();
    ReleaseDBConn(conn);
    return sql_id;
}

//离线二维码，根据设备上报 更新可用次数
int PersonalAppTmpKey::UpdateAccessTimes(const SOCKET_MSG_DEV_REPORT_ACCESS_TIMES& report_access_times)
{
    CStrExplode str_exploed(report_access_times.unique_id, '-');
    uint32_t cnt = str_exploed.GetItemCnt();
    if (cnt != 2)
    {
        AK_LOG_WARN << "Invalid unique_id=" << report_access_times.unique_id;
        return -1;
    }

    if (report_access_times.access_times == 0)
    {
        return 0;
    }

    const char *table_name = "";
    const char *column_name = "";
    if (strcmp(str_exploed.GetItem(0), "PER") == 0)
    {
        table_name = "PersonalAppTmpKey";
        column_name = "TmpKey";
    }
    else if (strcmp(str_exploed.GetItem(0), "PUB") == 0)
    {
        table_name = "PubAppTmpKey";
        column_name = "Code";
    }
    else
    {
        AK_LOG_WARN << "Invalid unique_id=" << report_access_times.unique_id << ";flag=" << str_exploed.GetItem(0);
        return -1;
    }

    char sql[512];
    snprintf(sql, sizeof(sql), "update %s set AccessTimes = least(AccessTimes + %d, AllowedTimes) where ID = '%s' and %s = '%s'",
             table_name, report_access_times.access_times, str_exploed.GetItem(1), column_name, report_access_times.temp_key);

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* temp_conn = conn.get();
    if (NULL == temp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    temp_conn->BeginTransAction();
    int ret = conn->Execute(sql) > 0 ? 0 : -1;
    temp_conn->EndTransAction();
    ReleaseDBConn(conn);

    return ret;
}

//在线二维码，根据云端校验成功后 更新可用次数
void PersonalAppTmpKey::UpdateAccessTimes(const SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& tmp_key)
{
    if(tmp_key.result == 1)
    {
        //只有开门成功，即result=0时，才能增加使用次数
        return;
    }
    
    if(ONCE_SCHED == tmp_key.sche_type || EACH_DOOR_ONCE_SCHED == tmp_key.sche_type)
    {        
        std::string table = tmp_key.key_table_type == PER_TMP_KEY ? "PersonalAppTmpKey" : "PubAppTmpKey";
        RldbPtr conn = GetDBConnPollInstance()->GetConnection();
        CRldb* rldb_conn = conn.get();
        if (NULL == rldb_conn)
        {
            AK_LOG_WARN << "Get DB conn failed.";
            return;
        }
        
        std::stringstream sql;
        sql << "UPDATE  " << table << " SET  AccessTimes=" << tmp_key.access_times
            << " WHERE ID=" << tmp_key.key_id;
        rldb_conn->Execute(sql.str());     
        
        ReleaseDBConn(conn);
    }
}

bool PersonalAppTmpKey::IsPersonalAppTmpKeyValid(CRldb* rldb_conn, SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& check_tmpkey, const std::string& time_zone, 
    std::map<std::string, AKCS_DST>& dst)
{  
    bool ret = FALSE;
    if(rldb_conn == nullptr)
    {
        return ret;
    }
    
    CRldbQuery query(rldb_conn);
    
    if (ONCE_SCHED == check_tmpkey.sche_type || EACH_DOOR_ONCE_SCHED == check_tmpkey.sche_type)
    {
        std::string tmp_time = GetNodeNowDateTimeByTimeZoneStr(time_zone, dst);
        std::stringstream streamSQL;
        streamSQL << "SELECT K.ID,K.AccessTimes FROM PersonalAppTmpKey" ;
        streamSQL << " K INNER JOIN PersonalAppTmpKeyList L on L.KeyID=K.ID and L.MAC='";
        streamSQL << check_tmpkey.mac << "'";
        streamSQL << "  WHERE TmpKey = '";
        streamSQL << check_tmpkey.tmpkey;
        streamSQL << "' AND BeginTime < '";
        streamSQL << tmp_time;
        streamSQL << "' AND EndTime > '";
        streamSQL << tmp_time;
        streamSQL << "' AND AccessTimes < AllowedTimes LIMIT 1";
        query.Query(streamSQL.str());
        //联动单元的设备mac地址列表
        if (query.MoveToNextRow())
        {
            check_tmpkey.key_table_type = PER_TMP_KEY;
            check_tmpkey.key_id = ATOI(query.GetRowData(0));
            check_tmpkey.access_times = ATOI(query.GetRowData(1)) + 1;
            ret = TRUE;
        }
        else
        {
            ret = FALSE;
        }
    }

    else if (WEEKLY_SCHED == check_tmpkey.sche_type)
    {
        string tmp_time;
        int day_of_week = GetWeekDayAndTimeByTimeZoneStr(time_zone, tmp_time, dst);
        unsigned int date_flag = kWeek_day[day_of_week];

        std::stringstream streamSQL;
        streamSQL << "SELECT K.DateFlag FROM PersonalAppTmpKey" ;
        streamSQL << " K INNER JOIN PersonalAppTmpKeyList L on L.KeyID=K.ID and L.MAC='";
        streamSQL << check_tmpkey.mac << "'";
        streamSQL << " WHERE TmpKey = '";
        streamSQL << check_tmpkey.tmpkey;
        streamSQL << "' AND StartTime < '";
        streamSQL << tmp_time;
        streamSQL << "' AND StopTime > '";
        streamSQL << tmp_time;
        streamSQL << "' LIMIT 1";

        query.Query(streamSQL.str());
        //联动单元的设备mac地址列表
        if (query.MoveToNextRow())
        {
            int tmp_flag = ATOI(query.GetRowData(0));
            if ((date_flag & tmp_flag) == date_flag)
            {
                ret = TRUE;
            }
            else
            {
                ret = FALSE;
            }
        }
        else
        {
            ret = FALSE;
        }
    }

    else
    {
        string tmp_time;
        GetWeekDayAndTimeByTimeZoneStr(time_zone, tmp_time, dst);
        std::stringstream streamSQL;
        streamSQL << "SELECT K.ID FROM PersonalAppTmpKey" ;
        streamSQL << " K INNER JOIN PersonalAppTmpKeyList L on L.KeyID=K.ID and L.MAC='";
        streamSQL << check_tmpkey.mac << "'";
        streamSQL << " WHERE TmpKey = '";
        streamSQL << check_tmpkey.tmpkey;
        streamSQL << "' AND StartTime < '";
        streamSQL << tmp_time;
        streamSQL << "' AND StopTime > '";
        streamSQL << tmp_time;
        streamSQL << "' LIMIT 1";

        query.Query(streamSQL.str());
        //联动单元的设备mac地址列表
        if (query.MoveToNextRow())
        {
            ret = TRUE;
        }
        else
        {
            ret = FALSE;
        }
    }

    return ret;
}

bool PersonalAppTmpKey::IsPubAppTmpKeyValid(CRldb* rldb_conn, SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& check_tmpkey, const std::string& time_zone,
    std::map<std::string, AKCS_DST>& dst)
{
    bool ret = FALSE;
    if(rldb_conn == nullptr)
    {
        return ret;
    }
    
    CRldbQuery query(rldb_conn);
    
    if (ONCE_SCHED == check_tmpkey.sche_type || EACH_DOOR_ONCE_SCHED == check_tmpkey.sche_type)
    {
        std::string tmp_time = GetNodeNowDateTimeByTimeZoneStr(time_zone, dst);
        std::stringstream streamSQL;
        streamSQL << "SELECT P.ID,P.AccessTimes FROM PubAppTmpKey P left join PubAppTmpKeyList L on L.KeyID=P.ID " ;
        streamSQL << " WHERE L.Mac = '";
        streamSQL << check_tmpkey.mac;
        streamSQL << "' AND P.Code = '";
        streamSQL << check_tmpkey.tmpkey;
        streamSQL << "' AND BeginTime < '";
        streamSQL << tmp_time;
        streamSQL << "' AND EndTime > '";
        streamSQL << tmp_time;
        streamSQL << "' AND AccessTimes < AllowedTimes LIMIT 1";
        query.Query(streamSQL.str());
        //联动单元的设备mac地址列表
        if (query.MoveToNextRow())
        {
            check_tmpkey.key_table_type = PUB_TMP_KEY;
            check_tmpkey.key_id = ATOI(query.GetRowData(0));
            check_tmpkey.access_times = ATOI(query.GetRowData(1)) + 1;
            ret = TRUE; 
        }
        else
        {
            ret = FALSE;
        }

    }

    else if (WEEKLY_SCHED == check_tmpkey.sche_type)
    {
        string tmp_time;
        int day_of_week = GetWeekDayAndTimeByTimeZoneStr(time_zone, tmp_time, dst);
        unsigned int date_flag = kWeek_day[day_of_week];

        std::stringstream streamSQL;
        streamSQL << "SELECT P.DateFlag FROM PubAppTmpKey P left join PubAppTmpKeyList L on L.KeyID=P.ID " ;
        streamSQL << " WHERE L.Mac = '";
        streamSQL << check_tmpkey.mac;
        streamSQL << "' AND P.Code = '";
        streamSQL << check_tmpkey.tmpkey;
        streamSQL << "' AND StartTime < '";
        streamSQL << tmp_time;
        streamSQL << "' AND StopTime > '";
        streamSQL << tmp_time;
        streamSQL << "' LIMIT 1";

        query.Query(streamSQL.str());
        //联动单元的设备mac地址列表
        if (query.MoveToNextRow())
        {
            int tmp_flag = ATOI(query.GetRowData(0));
            if ((date_flag & tmp_flag) == date_flag)
            {
                ret = TRUE;
            }
            else
            {
                ret = FALSE;
            }
        }
        else
        {
            ret = FALSE;
        }
    }

    else
    {
        string tmp_time;
        GetWeekDayAndTimeByTimeZoneStr(time_zone, tmp_time, dst);
        std::stringstream streamSQL;
        streamSQL << "SELECT P.ID,P.AccessTimes FROM PubAppTmpKey P left join PubAppTmpKeyList L on L.KeyID=P.ID " ;
        streamSQL << " WHERE L.Mac = '";
        streamSQL << check_tmpkey.mac;
        streamSQL << "' AND P.Code = '";
        streamSQL << check_tmpkey.tmpkey;
        streamSQL << "' AND StartTime < '";
        streamSQL << tmp_time;
        streamSQL << "' AND StopTime > '";
        streamSQL << tmp_time;
        streamSQL << "' LIMIT 1";

        query.Query(streamSQL.str());
        //联动单元的设备mac地址列表
        if (query.MoveToNextRow())
        {
            ret = TRUE;
        }
        else
        {
            ret = FALSE;
        }
    }

    return ret;
}

void PersonalAppTmpKey::GetUnitAptByRoomUUID(const std::string& room_uuid, SOCKET_MSG_PERSONNAL_CHECK_TMP_KEY& check_tmpkey)
{
    CommunityRoomInfo room_info;
    if (0 == dbinterface::CommunityRoom::GetCommunityRoomByUUID(room_uuid, room_info))
    {
        GetUnitAptByRoomID(room_info.id, check_tmpkey);
    }
}

std::string PersonalAppTmpKey::GetAccessFloorByTmpkey(const std::string& tmpkey, const std::string& node, int &is_follow_my_access)
{
    std::string access_floor;
    std::stringstream stream_sql;
    stream_sql << "select AccessFloor,IsFollowMyAccess from PersonalAppTmpKey where Tmpkey = '" << tmpkey << "' and Node='" << node << "'";

    GET_DB_CONN_ERR_RETURN(conn, "") //获取数据库连接失败时，返回空字符串
    CRldbQuery query(conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        access_floor = query.GetRowData(0);
        is_follow_my_access = ATOI(query.GetRowData(1));
    }

    return access_floor;
}

}
