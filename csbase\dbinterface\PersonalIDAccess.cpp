#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "dbinterface/PersonalIDAccess.h"
#include "AkcsCommonDef.h"

namespace dbinterface {

static const std::string personal_id_access_info_sec = " ID,UUID,PersonalAccountUUID,Mode,Run,Serial,CreatorType,LastEditorType ";

void PersonalIDAccess::GetPersonalIDAccessFromSql(PersonalIDAccessInfo& personal_id_access_info, CRldbQuery& query)
{
    personal_id_access_info.id = ATOI(query.GetRowData(0));
    Snprintf(personal_id_access_info.uuid, sizeof(personal_id_access_info.uuid), query.GetRowData(1));
    Snprintf(personal_id_access_info.personal_account_uuid, sizeof(personal_id_access_info.personal_account_uuid), query.GetRowData(2));
    personal_id_access_info.mode = ATOI(query.GetRowData(3));
    Snprintf(personal_id_access_info.run, sizeof(personal_id_access_info.run), query.GetRowData(4));
    Snprintf(personal_id_access_info.serial, sizeof(personal_id_access_info.serial), query.GetRowData(5));
    personal_id_access_info.creator_type = ATOI(query.GetRowData(6));
    personal_id_access_info.last_editor_type = ATOI(query.GetRowData(7));
    return;
}

int PersonalIDAccess::GetPersonalIDAccessByUUID(const std::string& uuid, PersonalIDAccessInfo& personal_id_access_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << personal_id_access_info_sec << " from PersonalIDAccess where UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetPersonalIDAccessFromSql(personal_id_access_info, query);
    }
    else
    {
        AK_LOG_WARN << "get PersonalIDAccessInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}

int PersonalIDAccess::GetPersonalIDAccessByPersonalAccountUUID(const std::string& personal_account_uuid, PersonalIDAccessInfo& personal_id_access_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << personal_id_access_info_sec << " from PersonalIDAccess where PersonalAccountUUID = '" << personal_account_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetPersonalIDAccessFromSql(personal_id_access_info, query);
    }
    else
    {
        AK_LOG_WARN << "get PersonalIDAccessInfo by PersonalAccountUUID failed, PersonalAccountUUID = " << personal_account_uuid;
        return -1;
    }
    return 0;
}

void PersonalIDAccess::GetIDAccessListByPersonalAccountUUID(const std::string& per_uuid_str, UsersIDAccessMap& user_id_access_map, UsersIDAccessMap& special_user_id_access_map)
{
    std::stringstream stream_sql;
    stream_sql << "select " << personal_id_access_info_sec << " from PersonalIDAccess where PersonalAccountUUID in (" << per_uuid_str << ")";
    GET_DB_CONN_ERR_RETURN(db_conn,)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        PersonalIDAccessInfo personal_id_access_info;
        GetPersonalIDAccessFromSql(personal_id_access_info, query);
        if (personal_id_access_info.last_editor_type == IDAccessCheck::IDAccessOwner::ID_ACCESS_OWNER_PM)
        {
            user_id_access_map[personal_id_access_info.personal_account_uuid] = personal_id_access_info;
        }
        else
        {
            special_user_id_access_map[personal_id_access_info.personal_account_uuid] = personal_id_access_info;
        }
    }
    return;
}

}