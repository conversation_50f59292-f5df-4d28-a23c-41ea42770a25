#ifndef __DB_PERSONAL_I_D_ACCESS_H__
#define __DB_PERSONAL_I_D_ACCESS_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct PersonalIDAccessInfo_T
{
    int id;
    char uuid[36];
    char personal_account_uuid[36];
    int mode;
    char run[32];
    char serial[32];
    int creator_type;
    int last_editor_type;
    PersonalIDAccessInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} PersonalIDAccessInfo;

typedef std::map<std::string/*uuid*/, PersonalIDAccessInfo> UsersIDAccessMap;
namespace dbinterface {

class PersonalIDAccess
{
public:
    static int GetPersonalIDAccessByUUID(const std::string& uuid, PersonalIDAccessInfo& personal_id_access_info);
    static int GetPersonalIDAccessByPersonalAccountUUID(const std::string& personal_account_uuid, PersonalIDAccessInfo& personal_id_access_info);
    static void GetIDAccessListByPersonalAccountUUID(const std::string& per_uuid_str, UsersIDAccessMap& user_id_access_map, UsersIDAccessMap& special_user_id_access_map);

private:
    PersonalIDAccess() = delete;
    ~PersonalIDAccess() = delete;
    static void GetPersonalIDAccessFromSql(PersonalIDAccessInfo& personal_id_access_info, CRldbQuery& query);
};

}
#endif