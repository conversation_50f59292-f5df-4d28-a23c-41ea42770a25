#ifndef __DB_PERSONAL_PRIVATE_KEY_H__
#define __DB_PERSONAL_PRIVATE_KEY_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include "AkcsCommonDef.h"
#include "AkcsCommonSt.h"
#include <map>
#include <set>
#include "dbinterface/CommPerPrivateKey.h"

typedef struct PersonalPrivateKey_T
{
    int id;
    int mng_id;
    int unit_id;
    char node[32];
    char code[20];
    int account_id; //personal account id
    int room_id;
    char account_name[128];
    char account_uuid[64];
}PersonalPrivateKeyInfo;

namespace dbinterface
{

class PersonalPrivateKey
{
public:
    PersonalPrivateKey();
    ~PersonalPrivateKey();
    static int GetPersonalPrivateKeyByID(int id, PersonalPrivateKeyInfo &key_info);
    static int GetPersonalPrivateKeyByCode(const std::string& node, const std::string& code, PersonalPrivateKeyInfo &key_info);
    static int GetNameAndNodeFromPriKeyForCommunityPubPersonal(int grade, const std::string& code, int unit_id, int nMngid, std::vector<PersonalPrivateKeyInfo>& pri_infos);
    static std::string GetNameFromPriKeyForCommunityPubWork(const std::string& code, int mng_id, const std::string& mac);
    static PRIVATE_KEY* GetRootBothPrivateKeyList(const std::string& user);
    static void GetCommunityMacPrivList(DEVICE_SETTING* dev_setting, PRIVATE_KEY** keylist);
    static void GetCommunityPerPrivateKey(int allow_app_create_pin, DEVICE_SETTING* dev_setting, PRIVATE_KEY** keylist);
    static void GetOrderedSingleUserNodePrivateKey(const std::string& node, UserPinInfoList& pin_list);
private:
};

}
#endif
