#include <sstream>
#include <string.h>
#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "PersonalRfcardKey.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/DataConfusion.h"
#include "Rldb/ConnectionManager.h"
namespace dbinterface
{

PersonalRfcardKey::PersonalRfcardKey()
{

}

int PersonalRfcardKey::GetPersonalRfcardKeyByID(int id, PersonalRfcardKeyInfo &key_info)
{
    std::stringstream streamSQL;
    streamSQL << "select MngAccountID,UnitID,Node,Code,AccountID from PersonalRfcardKey where ID = " << id;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    
    if (query.MoveToNextRow())
    {
        key_info.id = id;
        key_info.mng_id = ATOI(query.GetRowData(0));
        key_info.unit_id = ATOI(query.GetRowData(1));
        Snprintf(key_info.node, sizeof(key_info.node), query.GetRowData(2));
        Snprintf(key_info.code, sizeof(key_info.code), query.GetRowData(3));
        key_info.account_id = ATOI(query.GetRowData(4));
    }

    ReleaseDBConn(conn);
    return 0;    
}

//通过node-code查找卡和持卡人信息
int PersonalRfcardKey::GetPersonalRfCardInfoFromRFCard(const std::string& node, const std::string& code,
                                                       PersonalRfcardKeyInfo &key_info)
    {
        char sql[1024] = {0};
        ::snprintf(sql, 1024, "select A.Name, A.UUID from PersonalAccount A \
                       left join  PersonalRfcardKey R on A.ID=R.AccountID \
                       WHERE R.Node='%s' AND R.Code = '%s' limit 1",
                   node.c_str(), code.c_str());

        RldbPtr conn = GetDBConnPollInstance()->GetConnection();
        CRldb* tmp_conn = conn.get();
        if (NULL == tmp_conn)
        {
            AK_LOG_WARN << "Get DB conn failed.";
            return -1;
        }
        CRldbQuery query(tmp_conn);
        std::string sql2 = sql;
        query.Query(sql2);

        if (query.MoveToNextRow())
        {
            Snprintf(key_info.account_name, sizeof(key_info.account_name), dbinterface::DataConfusion::Decrypt(query.GetRowData(0)).c_str());
            Snprintf(key_info.account_uuid, sizeof(key_info.account_uuid), query.GetRowData(1));
        }
        ReleaseDBConn(conn);
        return 0;

    }

//通过code查找持卡人的昵称
std::string PersonalRfcardKey::GetNameAndNodeFromRFCardForCommunityPubPersonal(int grade, const std::string& code, int unit_id, int mng_id, std::string& node)
{
    std::string name;
    char sql[1024] = {0};

    if (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
        ::snprintf(sql, 1024, "select A.Name,R.Node from PersonalAccount A left join  PersonalRfcardKey R on A.ID=R.AccountID \
        WHERE  R.Code='%s' and R.MngAccountID='%d' and R.UnitID='%d' limit 1", code.c_str(), mng_id, unit_id);
    }
    else if (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
    {
        ::snprintf(sql, 1024, "select A.Name,R.Node from PersonalAccount A left join  PersonalRfcardKey R on A.ID=R.AccountID \
        WHERE  R.Code='%s' and R.MngAccountID='%d' limit 1", code.c_str(), mng_id);
    }
    else
    {
        return name;
    }

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return name;
    }
    CRldbQuery query(tmp_conn);
    std::string sql2 = sql;
    query.Query(sql2);

    if (query.MoveToNextRow())
    {
        name = dbinterface::DataConfusion::Decrypt(query.GetRowData(0));
        node = query.GetRowData(1);
    }
    ReleaseDBConn(conn);
    return name;

}

//通过code查找持卡人的昵称
std::string PersonalRfcardKey::GetNameFromRFCardForCommunityPubWork(const std::string& code, int mng_id, const std::string& mac)
{
    std::string name;
    char sql[1024] = {0};
    /*::snprintf(sql, 1024, "select C.Name from PubRfcardKey  P left join PubRfcardKeyList  L on L.KeyID=P.ID \
    left join CommunityWorker C on C.ID=P.WorkID \
    where L.mac='%s' and P.MngAccountID='%d' and P.Code='%s';", mac.c_str(), mng_id, code.c_str());*/

    ::snprintf(sql, 1024, "select P.Name from PubRfcardKey  P left join PubRfcardKeyList  L on L.KeyID=P.ID \
    where L.mac='%s' and P.MngAccountID='%d' and P.Code='%s';", mac.c_str(), mng_id, code.c_str());
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return name;
    }
    CRldbQuery query(tmp_conn);
    std::string sql2 = sql;
    query.Query(sql2);

    if (query.MoveToNextRow())
    {
        name = query.GetRowData(0);
    }
    ReleaseDBConn(conn);
    return name;

}

//个人终端用户,user表示主账号
RF_KEY* PersonalRfcardKey::GetRootBothRfKeyList(const std::string& user)
{
    RF_KEY* rf_key_header = NULL;
    RF_KEY* cur_rf_key = NULL;
    std::string address;
    //遍历数据库查询所有节点信息
    std::stringstream streamSQL;
    streamSQL << "select K.ID,K.Type,K.Code,K.Status,K.Node,K.CreateTime,K.ExpireTime,K.Access,K.AccountID,K.UnitID,K.MngAccountID,P.Name,P.Role from PersonalRfcardKey K \
inner join PersonalAccount P on K.AccountID=P.ID  where K.Node = " << user <<" order by ID DESC";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return NULL;
    }
    CRldbQuery query(tmp_conn);


    query.Query(streamSQL.str());
    while (query.MoveToNextRow())
    {
        int role = ATOI(query.GetRowData(12));
        if (role == ACCOUNT_ROLE_COMMUNITY_MAIN || role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT)
        {
            break;//区分是个人还是社区,社区不走这个流程了
        }
        RF_KEY* newkey = new RF_KEY();
        memset(newkey, 0, sizeof(RF_KEY));
        newkey->id = ATOI(query.GetRowData(0));
        newkey->type = ATOI(query.GetRowData(1));
        Snprintf(newkey->code, sizeof(newkey->code), query.GetRowData(2));
        newkey->status = ATOI(query.GetRowData(3));
        Snprintf(newkey->node, sizeof(newkey->node), query.GetRowData(4));
        Snprintf(newkey->create_time, sizeof(newkey->create_time), query.GetRowData(5));
        Snprintf(newkey->expire_time, sizeof(newkey->expire_time), query.GetRowData(6));
        Snprintf(newkey->access, sizeof(newkey->access), query.GetRowData(7));
        newkey->account_id = ATOI(query.GetRowData(8));
        newkey->unit_id = ATOI(query.GetRowData(9));
        newkey->mng_account_id = ATOI(query.GetRowData(10));
        Snprintf(newkey->user, sizeof(newkey->user), dbinterface::DataConfusion::Decrypt(query.GetRowData(11)).c_str());
        if (rf_key_header == NULL)
        {
            rf_key_header = newkey;
        }
        else
        {
            cur_rf_key->next = newkey;
        }

        cur_rf_key = newkey;
    }
    ReleaseDBConn(conn);
    return rf_key_header;
}

RF_KEY* PersonalRfcardKey::GetCommunityRootBothRfKeyList(int id, int type)
{
    RF_KEY* rf_key_header = NULL;
    RF_KEY* cur_rf_key = NULL;
    std::string address;
    std::stringstream streamSQL;
    //遍历数据库查询所有节点信息
    if (csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT == type)
    {
        streamSQL << "select K.ID,K.Type,K.Code,K.Status,K.Node,K.CreateTime,K.ExpireTime,K.Access,K.AccountID,K.UnitID,K.MngAccountID,P.Name from PersonalRfcardKey K \
       inner join PersonalAccount P on P.Account=K.Node  where  P.UnitID= " << id <<" order by ID DESC";
    }
    else if (csmain::COMMUNITY_DEVICE_TYPE_PUBLIC == type)
    {
        //不包括单元的rfkey
        streamSQL << "select K.ID,K.Type,K.Code,K.Status,K.Node,K.CreateTime,K.ExpireTime,K.Access,K.AccountID,K.UnitID,K.MngAccountID,P.Name from PersonalRfcardKey K \
      inner join PersonalAccount P on P.Account=K.Node where  P.ParentID= " << id <<" order by ID DESC";
    }
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return NULL;
    }
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    while (query.MoveToNextRow())
    {
        RF_KEY* newkey = new RF_KEY();
        memset(newkey, 0, sizeof(RF_KEY));
        newkey->id = ATOI(query.GetRowData(0));
        newkey->type = ATOI(query.GetRowData(1));
        Snprintf(newkey->code, sizeof(newkey->code), query.GetRowData(2));
        newkey->status = ATOI(query.GetRowData(3));
        Snprintf(newkey->node, sizeof(newkey->node), query.GetRowData(4));
        Snprintf(newkey->create_time, sizeof(newkey->create_time), query.GetRowData(5));
        Snprintf(newkey->expire_time, sizeof(newkey->expire_time), query.GetRowData(6));
        Snprintf(newkey->access, sizeof(newkey->access), query.GetRowData(7));
        newkey->account_id = ATOI(query.GetRowData(8));
        newkey->unit_id = ATOI(query.GetRowData(9));
        newkey->mng_account_id = ATOI(query.GetRowData(10));
        Snprintf(newkey->user, sizeof(newkey->user), dbinterface::DataConfusion::Decrypt(query.GetRowData(11)).c_str());
        if (rf_key_header == NULL)
        {
            rf_key_header = newkey;
        }
        else
        {
            cur_rf_key->next = newkey;
        }

        cur_rf_key = newkey;
    }
    ReleaseDBConn(conn);
    return rf_key_header;
}

/*获取个人或社区联动的nfc 到rfkey*/
void PersonalRfcardKey::GetNodeNfcKeyList(const std::string& user, RF_KEY** keylist)
{
    RF_KEY* rf_key_header = *keylist;
    if (rf_key_header)
    {
        while (rf_key_header->next)
        {
            rf_key_header = rf_key_header->next;

        }
    }
    RF_KEY* cur_rf_key = rf_key_header;

    std::string address;
    //遍历数据库查询所有节点信息
    std::stringstream streamSQL;

    streamSQL << "select A.Name,A.NFCCode,A.BLECode,C.UnitID,C.RoomName From PersonalAccount A "
          << "left join PersonalAccount B on B.ID=A.ParentID and (B.Role=" << ACCOUNT_ROLE_PERSONNAL_MAIN
          << " or B.Role=" << ACCOUNT_ROLE_COMMUNITY_MAIN << ") "
          << "and (A.Role=" << ACCOUNT_ROLE_PERSONNAL_ATTENDANT << " or A.Role=" << ACCOUNT_ROLE_COMMUNITY_ATTENDANT << ") "
          << "left join CommunityRoom C on C.ID = B.RoomID where B.Account='" << user << "' "
          << "union select A.Name,A.NFCCode,A.BLECode,C.UnitID,C.RoomName from PersonalAccount A "
          << "left join CommunityRoom C on C.ID = A.RoomID where A.Account='" << user << "'";
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }
    CRldbQuery query(tmp_conn);


    query.Query(streamSQL.str());
    while (query.MoveToNextRow())
    {
        RF_KEY* newkey = new RF_KEY();
        memset(newkey, 0, sizeof(RF_KEY));
        Snprintf(newkey->user, sizeof(newkey->user), dbinterface::DataConfusion::Decrypt(query.GetRowData(0)).c_str());
        Snprintf(newkey->code, sizeof(newkey->code), query.GetRowData(1));
        newkey->building = ATOI(query.GetRowData(3));
        Snprintf(newkey->apt, sizeof(newkey->apt), query.GetRowData(4));
        if (strlen(newkey->code) > 0)
        {
            if (rf_key_header == NULL)
            {
                rf_key_header = newkey;
                *keylist = rf_key_header;
            }
            else
            {
                cur_rf_key->next = newkey;
            }

            cur_rf_key = newkey;
        }
        else
        {
            delete newkey;
        }
        //BLE
        {
            RF_KEY* newkey = new RF_KEY();
            memset(newkey, 0, sizeof(RF_KEY));
            Snprintf(newkey->user, sizeof(newkey->user), dbinterface::DataConfusion::Decrypt(query.GetRowData(0)).c_str());
            Snprintf(newkey->code, sizeof(newkey->code), query.GetRowData(2));
            newkey->building = ATOI(query.GetRowData(3));
            Snprintf(newkey->apt, sizeof(newkey->apt), query.GetRowData(4));
            if (strlen(newkey->code) > 0)
            {
                if (rf_key_header == NULL)
                {
                    rf_key_header = newkey;
                    *keylist = rf_key_header;
                }
                else
                {
                    cur_rf_key->next = newkey;
                }

                cur_rf_key = newkey;
            }
            else
            {
                delete newkey;
            }
        }

    }

    ReleaseDBConn(conn);
    return;

}

void PersonalRfcardKey::GetCommunityNfcList(int id, int type, RF_KEY** nfc_key_list)
{
    RF_KEY* rf_key_header = *nfc_key_list;
    if (rf_key_header)
    {
        while (rf_key_header->next)
        {
            rf_key_header = rf_key_header->next;
        }
    }
    RF_KEY* cur_rf_key = rf_key_header;

    std::string address;
    std::stringstream streamSQL;
    //遍历数据库查询所有节点信息
    if (csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT == type)
    {
        streamSQL << "select P.NfcCode,P.Name,P.BLECode,C.UnitID,C.RoomName from PersonalAccount P left join CommunityRoom C on C.ID = P.RoomID  where P.UnitID= " << id << " and (P.Role=" << ACCOUNT_ROLE_COMMUNITY_MAIN << ") "
                  << "union "
                  << "select P.NfcCode,P.Name,P.BLECode,C.UnitID,C.RoomName from PersonalAccount P left join PersonalAccount A "
                  << "on P.ParentID=A.ID and (P.Role=" << ACCOUNT_ROLE_COMMUNITY_ATTENDANT << ") and (A.Role=" << ACCOUNT_ROLE_COMMUNITY_MAIN << ") "
                  << "left join CommunityRoom C on C.ID = A.RoomID where A.UnitID=" << id;
    }
    else if (csmain::COMMUNITY_DEVICE_TYPE_PUBLIC == type)
    {
    streamSQL << "select P.NfcCode,P.Name,P.BLECode,C.UnitID,C.RoomName from PersonalAccount P left join CommunityRoom C on C.ID = P.RoomID where P.ParentID= " << id << " and (P.Role=" << ACCOUNT_ROLE_COMMUNITY_MAIN << ") "
              << "union "
              << "select P.NfcCode,P.Name,P.BLECode,C.UnitID,C.RoomName from PersonalAccount P left join PersonalAccount A "
              << "on P.ParentID=A.ID and (P.Role=" << ACCOUNT_ROLE_COMMUNITY_ATTENDANT << ") and (A.Role=" << ACCOUNT_ROLE_COMMUNITY_MAIN << ") "
              << "left join CommunityRoom C on C.ID = A.RoomID where A.ParentID=" << id;
    }
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    while (query.MoveToNextRow())
    {
        RF_KEY* newkey = new RF_KEY();
        memset(newkey, 0, sizeof(RF_KEY));
        Snprintf(newkey->code, sizeof(newkey->code), query.GetRowData(0));
        Snprintf(newkey->user, sizeof(newkey->user), dbinterface::DataConfusion::Decrypt(query.GetRowData(1)).c_str());
        newkey->building = ATOI(query.GetRowData(3));
        Snprintf(newkey->apt, sizeof(newkey->apt), query.GetRowData(4));
        if (strlen(newkey->code) > 0)
        {
            if (rf_key_header == NULL)
            {
                rf_key_header = newkey;
                *nfc_key_list = rf_key_header;
            }
            else
            {
                cur_rf_key->next = newkey;
            }
            cur_rf_key = newkey;

        }
        else
        {
            delete newkey;
        }

        //BLE
        {
            RF_KEY* newkey = new RF_KEY();
            memset(newkey, 0, sizeof(RF_KEY));
            Snprintf(newkey->code, sizeof(newkey->code), query.GetRowData(2));
            Snprintf(newkey->user, sizeof(newkey->user), dbinterface::DataConfusion::Decrypt(query.GetRowData(1)).c_str());
            newkey->building = ATOI(query.GetRowData(3));
            Snprintf(newkey->apt, sizeof(newkey->apt), query.GetRowData(4));
            if (strlen(newkey->code) == 0)
            {
                delete newkey;
                continue;
            }
            if (rf_key_header == NULL)
            {
                rf_key_header = newkey;
                *nfc_key_list = rf_key_header;
            }
            else
            {
                cur_rf_key->next = newkey;
            }
            cur_rf_key = newkey;
        }
    }
    ReleaseDBConn(conn);
    return;
}

void PersonalRfcardKey::GetCommunityMacRfList(DEVICE_SETTING* dev_setting, RF_KEY** RfKeyList)
{
    RF_KEY* rf_key_header = *RfKeyList;
    if (rf_key_header)
    {
        while (rf_key_header->next)
        {
            rf_key_header = rf_key_header->next;
        }
    }
    RF_KEY* cur_rf_key = rf_key_header;

    std::string address;
    std::stringstream streamSQL;

    streamSQL << "select P.Code,P.Name,P.OwnerType,P.SchedulerType,P.DateFlag,P.BeginTime,P.EndTime,P.StartTime,P.StopTime,L.Relay,L.SecurityRelay from PubRfcardKey  P left join PubRfcardKeyList  L on L.KeyID=P.ID  where L.Mac='" << dev_setting->mac
        << "' and P.MngAccountID=" << dev_setting->manager_account_id;
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    while (query.MoveToNextRow())
    {
        RF_KEY* newkey = new RF_KEY();
        memset(newkey, 0, sizeof(RF_KEY));
        Snprintf(newkey->code, sizeof(newkey->code), query.GetRowData(0));
        Snprintf(newkey->user, sizeof(newkey->user), query.GetRowData(1));
        int role = ATOI(query.GetRowData(2));
        if (role == 0) //物业
        {
            newkey->type = 1;
        }
        else if (role == 1)//快递
        {
            newkey->type = 2;
        }
        if (strlen(newkey->code) == 0)
        {
            delete newkey;
            continue;
        }

        if (D_CLIENT_VERSION_5200 > dev_setting->dclient_version)
        {
            newkey->relay = 7;
        }
        else
        {
            newkey->relay = ATOI(query.GetRowData(9));
        }

        if (dev_setting->dclient_version >= D_CLIENT_VERSION_6400)
        {
            newkey->security_relay = ATOI(query.GetRowData(10));
        }
        else
        {
            newkey->security_relay = 0;
        }

        int sche_type = ATOI(query.GetRowData(3));
        if (SchedType::ONCE_SCHED == sche_type) //单次计划
        {
            if (D_CLIENT_VERSION_5200 > dev_setting->dclient_version)   //旧设备无单次计划,故写死
            {
                Snprintf(newkey->time_start, sizeof(newkey->time_start), "00:00");
                Snprintf(newkey->time_end, sizeof(newkey->time_end), "23:59");
                newkey->week_day = 127;    //即二进制1111111,每天
            }
            else
            {
                Snprintf(newkey->day_start, sizeof(newkey->day_start), query.GetRowData(5));
                Snprintf(newkey->day_end, sizeof(newkey->day_end), query.GetRowData(6));
            }
        }
        else if (SchedType::DAILY_SCHED == sche_type) //每日计划
        {
            Snprintf(newkey->time_start, sizeof(newkey->time_start), query.GetRowData(7));
            Snprintf(newkey->time_end, sizeof(newkey->time_end), query.GetRowData(8));
            newkey->week_day = 127;    //每天
        }
        else //周计划
        {
            Snprintf(newkey->time_start, sizeof(newkey->time_start), query.GetRowData(7));
            Snprintf(newkey->time_end, sizeof(newkey->time_end), query.GetRowData(8));
            newkey->week_day = ATOI(query.GetRowData(4));   //周几
        }

        if (rf_key_header == NULL)
        {
            rf_key_header = newkey;
            *RfKeyList = rf_key_header;
        }
        else
        {
            cur_rf_key->next = newkey;
        }

        cur_rf_key = newkey;
    }
    ReleaseDBConn(conn);
    return;
}

void PersonalRfcardKey::GetCommunityPerRfKey(DEVICE_SETTING* dev_setting, PRIVATE_KEY** keylist)
{
    PRIVATE_KEY* key_header = *keylist;
    if (key_header)
    {
        while (key_header->next)
        {
            key_header = key_header->next;

        }
    }
    PRIVATE_KEY* cur_key = key_header;

    std::stringstream sql;
    sql << "select P.Code,P.SchedulerType,P.DateFlag,P.BeginTime,P.EndTime,P.StartTime,P.StopTime,L.Relay,A.Name,C.UnitID,C.RoomName,L.SecurityRelay \
            from PersonalRfcardKey P left join PersonalRfcardKeyList L on L.KeyID=P.ID left join PersonalAccount A on A.ID=P.AccountID \
            left join PersonalAccount A1 on A1.Account=P.Node left join CommunityRoom C on C.ID = A1.RoomID"
        << " where L.Mac= '"
        << dev_setting->mac
        << "' and P.MngAccountID = "
        << dev_setting->manager_account_id;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        PRIVATE_KEY* newkey = new PRIVATE_KEY;
        memset(newkey, 0, sizeof(PRIVATE_KEY));
        Snprintf(newkey->code, sizeof(newkey->code), query.GetRowData(0));

        if (strlen(newkey->code) == 0)
        {
            delete newkey;
            continue;
        }

        if (D_CLIENT_VERSION_5200 > dev_setting->dclient_version)
        {
            newkey->relay = 7;
        }
        else
        {
            newkey->relay = ATOI(query.GetRowData(7));
        }

        if (dev_setting->dclient_version >= D_CLIENT_VERSION_6400)
        {
            newkey->security_relay = ATOI(query.GetRowData(11));
        }
        else
        {
            newkey->security_relay = 0;
        }

        int sche_type = ATOI(query.GetRowData(1));
        if (SchedType::ONCE_SCHED == sche_type) //单次计划
        {
            if (D_CLIENT_VERSION_5200 > dev_setting->dclient_version)   //旧设备无单次计划,故写死
            {
                Snprintf(newkey->time_start, sizeof(newkey->time_start), "00:00");
                Snprintf(newkey->time_end, sizeof(newkey->time_end), "23:59");
                newkey->week_day = 127;    //即二进制1111111,每天
            }
            else
            {
                Snprintf(newkey->day_start, sizeof(newkey->day_start), query.GetRowData(3));
                Snprintf(newkey->day_end, sizeof(newkey->day_end), query.GetRowData(4));
            }
        }
        else if (SchedType::DAILY_SCHED == sche_type) //每日计划
        {
            Snprintf(newkey->time_start, sizeof(newkey->time_start), query.GetRowData(5));
            Snprintf(newkey->time_end, sizeof(newkey->time_end), query.GetRowData(6));
            newkey->week_day = 127;    //每天
        }
        else //周计划
        {
            Snprintf(newkey->time_start, sizeof(newkey->time_start), query.GetRowData(5));
            Snprintf(newkey->time_end, sizeof(newkey->time_end), query.GetRowData(6));
            newkey->week_day = ATOI(query.GetRowData(2));   //周几
        }

        Snprintf(newkey->user, sizeof(newkey->user),  dbinterface::DataConfusion::Decrypt(query.GetRowData(8)).c_str());
        newkey->building = ATOI(query.GetRowData(9));
        Snprintf(newkey->apt, sizeof(newkey->apt), query.GetRowData(10));

        if (key_header == NULL)
        {
            key_header = newkey;
            *keylist = key_header;
        }
        else
        {
            cur_key->next = newkey;
        }

        cur_key = newkey;
    }
    ReleaseDBConn(conn);
}

void PersonalRfcardKey::GetOrderedSingleUserNodeRfKeyList(const std::string& node, UserRFInfoList& rf_list)
{
    std::stringstream sql;
    sql << "select K.Code, P.Account from PersonalRfcardKey K left join PersonalAccount P on K.AccountID = P.ID where K.Node = '" << node << "' order by K.ID";
    
    GET_DB_CONN_ERR_RETURN_VOID(conn)
    CRldbQuery query(conn.get());
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        UserRFInfo rf_info;
        Snprintf(rf_info.rf_card, sizeof(rf_info.rf_card), query.GetRowData(0));
        Snprintf(rf_info.account, sizeof(rf_info.account), query.GetRowData(1));
        rf_list.push_back(rf_info);
    }

    return;
    
}

}

