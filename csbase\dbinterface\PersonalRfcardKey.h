#ifndef __DB_PERSONAL_RFCARD_KEY_H__
#define __DB_PERSONAL_RFCARD_KEY_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include "AkcsCommonDef.h"
#include "AkcsCommonSt.h"
#include <set>
#include <map>
#include "dbinterface/CommPerRfKey.h"

typedef struct PersonalRfcardKey_T
{
    int id;
    int mng_id;
    int unit_id;
    char node[32];
    char code[20];
    int account_id; //personal account id
    char account_name[128];
    char account_uuid[64];
}PersonalRfcardKeyInfo;

namespace dbinterface
{

class PersonalRfcardKey
{
public:
    PersonalRfcardKey();
    ~PersonalRfcardKey();
    static int GetPersonalRfcardKeyByID(int id, PersonalRfcardKeyInfo &key_info);
    static int GetPersonalRfCardInfoFromRFCard(const std::string& node, const std::string& code, PersonalRfcardKeyInfo &key_info);
    static std::string GetNameAndNodeFromRFCardForCommunityPubPersonal(int grade, const std::string& code, int unit_id, int mng_id, std::string& node);
    static std::string GetNameFromRFCardForCommunityPubWork(const std::string& code, int mng_id, const std::string& mac);
    static RF_KEY* GetRootBothRfKeyList(const std::string& user);
    static RF_KEY* GetCommunityRootBothRfKeyList(int id, int type);
    static void GetNodeNfcKeyList(const std::string& user, RF_KEY** keylist);
    static void GetCommunityNfcList(int id, int type, RF_KEY** nfc_key_list);
    static void GetCommunityMacRfList(DEVICE_SETTING* dev_setting, RF_KEY** RfKeyList);
    static void GetCommunityPerRfKey(DEVICE_SETTING* dev_setting, PRIVATE_KEY** keylist);
    static void GetOrderedSingleUserNodeRfKeyList(const std::string& node, UserRFInfoList& rf_list);
    
private:
};

}
#endif
