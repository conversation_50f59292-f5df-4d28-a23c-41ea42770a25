#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "PersonalThirdPartyCamera.h"
#include <string.h>
#include "AkLogging.h"
#include "util.h"

namespace dbinterface
{

PersonalThirdPartyCamrea::PersonalThirdPartyCamrea()
{

}

static const std::string personal_camera_sec = "UUID,Location,RtspAddress,RtspUserName,RtspPwd,MAC,VideoPt,VideoType,VideoFmtp,MonitoringPlatform,AllowEndUserMonitor";


int PersonalThirdPartyCamrea::GetCameraFromSql(ThirdPartyCamreaInfo &camera, CRldbQuery& query)
{
    Snprintf(camera.camera_uuid, sizeof(camera.camera_uuid), query.GetRowData(0));
    Snprintf(camera.location, sizeof(camera.location), query.GetRowData(1));
    Snprintf(camera.rtsp_url, sizeof(camera.rtsp_url), query.GetRowData(2));
    Snprintf(camera.username, sizeof(camera.username), query.GetRowData(3));
    Snprintf(camera.passwd, sizeof(camera.passwd), query.GetRowData(4));
    Snprintf(camera.mac, sizeof(camera.mac), query.GetRowData(5));
    camera.video_pt = ATOI(query.GetRowData(6));
    Snprintf(camera.video_type, sizeof(camera.video_type), query.GetRowData(7));
    Snprintf(camera.video_fmtp, sizeof(camera.video_fmtp), query.GetRowData(8));
    camera.monitoring_platform = ATOI(query.GetRowData(9));
    camera.allow_end_user_monitor = ATOI(query.GetRowData(10));

    return 0;
}

int PersonalThirdPartyCamrea::GetPersonalThirdPartyCameraList(const std::string &personal_uuid, ThirdPartyCamreaList &node_camera_list)
{
    std::stringstream streamSQL;
    streamSQL << "select "<< personal_camera_sec <<" from PersonalThirdPartCamera where "
              <<"PersonalAccountUUID = '" << personal_uuid << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    
    while (query.MoveToNextRow())
    {
        ThirdPartyCamreaInfo camera;
        Snprintf(camera.personal_uuid, sizeof(camera.personal_uuid), personal_uuid.c_str());
        GetCameraFromSql(camera, query);

        node_camera_list.push_back(camera);
    }

    if (node_camera_list.size() == 0)
    {
        ReleaseDBConn(conn);
        return -1;  
    }

    ReleaseDBConn(conn);
    return 0;    
}

//一台设备只能绑定一个三方摄像头
int PersonalThirdPartyCamrea::GetPersonalThirdPartyCameraByMac(const std::string &mac, ThirdPartyCamreaInfo &third_camera)
{
    std::stringstream streamSQL;
    streamSQL << "select "<< personal_camera_sec <<" from PersonalThirdPartCamera where MAC = '"
              << mac << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    
    if (query.MoveToNextRow())
    {
        GetCameraFromSql(third_camera, query);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }

    ReleaseDBConn(conn);
    return 0;    
}


int PersonalThirdPartyCamrea::GetPersonalThirdPartyCameraByUUID(const std::string &uuid, ThirdPartyCamreaInfo &third_camera)
{
    std::stringstream streamSQL;
    streamSQL << "select "<< personal_camera_sec <<" from PersonalThirdPartCamera where UUID = '"
              << uuid << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    
    if (query.MoveToNextRow())
    {
        GetCameraFromSql(third_camera, query);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }

    ReleaseDBConn(conn);
    return 0;    
}

int PersonalThirdPartyCamrea::UpdatePersonalThirdPartyCameraVideoInfo(ThirdPartyCamreaInfo &third_camera)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::stringstream streamsql;
    streamsql << "update PersonalThirdPartCamera set VideoType= '" << third_camera.video_type << "' , VideoPt = " << third_camera.video_pt << ", VideoFmtp = '" << third_camera.video_fmtp
              << "' where UUID = '"  << third_camera.camera_uuid << "'";    

    int ret = ptmpconn->Execute(streamsql.str()) > 0 ? 0 : -1;
    if (-1 == ret)
    {
        AK_LOG_WARN << "UpdatePersonalThirdPartyCameraVideoInfo failed, camera_uuid = " << third_camera.camera_uuid;
        ReleaseDBConn(conn);
        return ret;
    }

    ReleaseDBConn(conn);
    return 0;
}




}

