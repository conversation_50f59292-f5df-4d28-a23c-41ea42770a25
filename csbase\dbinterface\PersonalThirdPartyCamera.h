#ifndef __DB_PERSONAL_THIRD_PARTY_CAMERA_H__
#define __DB_PERSONAL_THIRD_PARTY_CAMERA_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include "RldbQuery.h"
#include "Rldb.h"
#include "AkcsCommonDef.h"
#include "ThirdPartyCamera.h"


namespace dbinterface
{

class PersonalThirdPartyCamrea
{
public:
    PersonalThirdPartyCamrea();
    ~PersonalThirdPartyCamrea();
    static int GetPersonalThirdPartyCameraList(const std::string &personal_uuid, ThirdPartyCamreaList &node_camera_list);
    static int GetPersonalThirdPartyCameraByMac(const std::string &mac, ThirdPartyCamreaInfo &third_camera);
    static int GetPersonalThirdPartyCameraByUUID(const std::string &uuid, ThirdPartyCamreaInfo &third_camera);
    static int UpdatePersonalThirdPartyCameraVideoInfo(ThirdPartyCamreaInfo &third_camera);
private:
    static int GetCameraFromSql(ThirdPartyCamreaInfo &camera, CRldbQuery& query);
};

}
#endif
