#include <sstream>
#include "RldbQuery.h"
#include "InterfaceComm.h"
#include "PersonalVoiceMsg.h"
#include <string.h>
#include "AkLogging.h"
#include "AkcsCommonDef.h"
#include "util.h"

namespace dbinterface{
PersonalVoiceMsg::PersonalVoiceMsg()
{

}

PersonalVoiceMsg::~PersonalVoiceMsg()
{

}

int PersonalVoiceMsg::InsertPersonalVoiceMsg(const PersonalVoiceMsgInfo &voice_msg)
{
    int ret = 0;
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    std::map<std::string, std::string> str_map;
    str_map.emplace("UUID", voice_msg.uuid);
    str_map.emplace("DoorUUID", voice_msg.dev_uuid);
    str_map.emplace("DoorMac", voice_msg.mac);
    str_map.emplace("ProjectUUID", voice_msg.project_uuid);
    str_map.emplace("Location", voice_msg.location);
    str_map.emplace("FileName", voice_msg.file_name);
    str_map.emplace("PicName", voice_msg.pic_name);

    std::map<std::string, int> int_map;
    std::string table_name = "PersonalVoiceMsg";

    ret = conn->InsertData(table_name, str_map, int_map);

    ReleaseDBConn(conn);
    return ret;
}

int PersonalVoiceMsg::InsertPersonalVoiceMsgList(const PersonalVoiceMsgNode &node)
{
    int ret = 0;
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    char sql[1024] = "";
    if (node.type == DEVICE_TYPE_APP)
    {
        ::snprintf(sql, sizeof(sql), "insert into PersonalVoiceMsgList (UUID,PersonalAccountUUID,MsgUUID) \
                    values('%s','%s','%s')", node.uuid, node.receiver_uuid, node.msg_uuid);
    }
    else if (node.type == DEVICE_TYPE_INDOOR)
    {
        ::snprintf(sql, sizeof(sql), "insert into PersonalVoiceMsgList (UUID,InDoorUUID,MsgUUID) \
                    values('%s','%s','%s')", node.uuid, node.receiver_uuid, node.msg_uuid);
    }

    ret = conn->Execute(sql) > 0 ? 0 : -1;

    ReleaseDBConn(conn);
    return ret;
}

int PersonalVoiceMsg::UpdatePersonalVoiceMsgPicUrl(const std::string &pic_url, const std::string &pic_name)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    if (pic_url.length() == 0)
    {
        AK_LOG_WARN << "PicUrl is null";
        ReleaseDBConn(conn);
        return -1;
    }

    std::stringstream streamsql;
    streamsql << "update PersonalVoiceMsg set PicUrl= '" << pic_url << "' where PicName = '" 
              << pic_name
              << "' and PicUrl = ''";    

    int ret = ptmpconn->Execute(streamsql.str()) > 0 ? 0 : -1;
    if (-1 == ret)
    {
        AK_LOG_WARN << "UpdatePicUrl failed, PicName = " << pic_name;
        ReleaseDBConn(conn);
        return ret;
    }

    ReleaseDBConn(conn);
    return 0;
}

int PersonalVoiceMsg::UpdatePersonalVoiceMsgFileUrl(const std::string &file_url, const std::string &file_name)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    if (file_url.length() == 0)
    {
        AK_LOG_WARN << "FileUrl is null";
        ReleaseDBConn(conn);
        return -1;
    }

    std::stringstream streamsql;
    streamsql << "update PersonalVoiceMsg set FileUrl= '" << file_url << "' where FileName = '" 
              << file_name
              << "' and FileUrl = ''";    

    int ret = ptmpconn->Execute(streamsql.str()) > 0 ? 0 : -1;
    if (-1 == ret)
    {
        AK_LOG_WARN << "UpdatePicUrl failed, FileName = " << file_name;
        ReleaseDBConn(conn);
        return ret;
    }

    ReleaseDBConn(conn);
    return 0;
}

//插入后立即就要进行推送或通知设备，可能出现在从库查找找不到的情况，限制从主库查找
int PersonalVoiceMsg::GetVoiceMsgInfoByUUID(const std::string &uuid, PersonalVoiceMsgInfo &voice_msg)
{
    std::stringstream streamsql;
    streamsql << "/*master*/ select DoorUUID,DoorMac,ProjectUUID,Location,FileName,PicName,FileUrl,PicUrl from PersonalVoiceMsg where UUID = '"
              << uuid << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamsql.str());

    if (query.MoveToNextRow())
    {
        Snprintf(voice_msg.uuid, sizeof(voice_msg.uuid),uuid.c_str());
        Snprintf(voice_msg.dev_uuid, sizeof(voice_msg.dev_uuid),query.GetRowData(0));
        Snprintf(voice_msg.mac, sizeof(voice_msg.mac),query.GetRowData(1));
        Snprintf(voice_msg.project_uuid, sizeof(voice_msg.project_uuid),query.GetRowData(2));
        Snprintf(voice_msg.location, sizeof(voice_msg.location),query.GetRowData(3));
        Snprintf(voice_msg.file_name, sizeof(voice_msg.file_name),query.GetRowData(4));
        Snprintf(voice_msg.pic_name, sizeof(voice_msg.pic_name),query.GetRowData(5));
        Snprintf(voice_msg.file_url, sizeof(voice_msg.file_url),query.GetRowData(6));
        Snprintf(voice_msg.pic_url, sizeof(voice_msg.pic_url),query.GetRowData(7));
    }


    ReleaseDBConn(conn);
    return 0;    
}

//根据MAC和FileName查找
int PersonalVoiceMsg::GetVoiceMsgInfoByMacAndFileName(const std::string &mac, const std::string &file_name, PersonalVoiceMsgInfo &voice_msg)
{
    std::stringstream streamsql;
    streamsql << "/*master*/ select DoorUUID,ProjectUUID,Location,PicName,FileUrl,PicUrl,UUID from PersonalVoiceMsg where DoorMac = '"
              << mac << "' and FileName = '" << file_name << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamsql.str());

    if (query.MoveToNextRow())
    {
        Snprintf(voice_msg.dev_uuid, sizeof(voice_msg.dev_uuid),query.GetRowData(0));
        Snprintf(voice_msg.mac, sizeof(voice_msg.mac),mac.c_str());
        Snprintf(voice_msg.project_uuid, sizeof(voice_msg.project_uuid),query.GetRowData(1));
        Snprintf(voice_msg.location, sizeof(voice_msg.location),query.GetRowData(2));
        Snprintf(voice_msg.file_name, sizeof(voice_msg.file_name),file_name.c_str());
        Snprintf(voice_msg.pic_name, sizeof(voice_msg.pic_name),query.GetRowData(3));
        Snprintf(voice_msg.file_url, sizeof(voice_msg.file_url),query.GetRowData(4));
        Snprintf(voice_msg.pic_url, sizeof(voice_msg.pic_url),query.GetRowData(5));
        Snprintf(voice_msg.uuid, sizeof(voice_msg.uuid),query.GetRowData(6));
    }

    ReleaseDBConn(conn);
    return 0;    
}

//插入后立即就要进行推送或通知设备，可能出现在从库查找找不到的情况，限制从主库查找
int PersonalVoiceMsg::GetVoiceMsgListInfoByUUID(const std::string &uuid, PersonalVoiceMsgSendNode &msg_node)
{
    std::stringstream streamsql;
    streamsql << "/*master*/ select ID,PersonalAccountUUID,IndoorUUID,MsgUUID,Status from PersonalVoiceMsgList where UUID = '"
              << uuid << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamsql.str());

    if (query.MoveToNextRow())
    {
        msg_node.id = ATOI(query.GetRowData(0));
        Snprintf(msg_node.personal_uuid, sizeof(msg_node.personal_uuid),query.GetRowData(1));
        Snprintf(msg_node.indoor_uuid, sizeof(msg_node.indoor_uuid),query.GetRowData(2));
        Snprintf(msg_node.uuid, sizeof(msg_node.uuid),query.GetRowData(3));
        msg_node.status = ATOI(query.GetRowData(4));
    }

    ReleaseDBConn(conn);
    return 0;    
}

int PersonalVoiceMsg::GetVoiceMsgListInfoByMsgUUID(const std::string &msg_uuid, PersonalVoiceMsgSendList &list)
{
    std::stringstream streamsql;
    streamsql << "/*master*/ select ID,PersonalAccountUUID,IndoorUUID,Status from PersonalVoiceMsgList where MsgUUID = '"
              << msg_uuid << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamsql.str());

    while (query.MoveToNextRow())
    {
        PersonalVoiceMsgSendNode msg_node;
        msg_node.id = ATOI(query.GetRowData(0));
        Snprintf(msg_node.personal_uuid, sizeof(msg_node.personal_uuid), query.GetRowData(1));
        Snprintf(msg_node.indoor_uuid, sizeof(msg_node.indoor_uuid), query.GetRowData(2));
        Snprintf(msg_node.uuid, sizeof(msg_node.uuid), msg_uuid.c_str());
        msg_node.status = ATOI(query.GetRowData(3));
        list.push_back(msg_node);
    }

    ReleaseDBConn(conn);
    return 0;    
}


//插入后立即就要进行推送或通知设备，可能出现在从库查找找不到的情况，限制从主库查找
int PersonalVoiceMsg::GetUnreadCountByIndoorUUID(const std::string &uuid)
{
    std::stringstream streamsql;
    streamsql << "/*master*/ select count(*) as count from PersonalVoiceMsgList where IndoorUUID = '"
              << uuid << "' and status = 0";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamsql.str());

    int count = 0;
    if (query.MoveToNextRow())
    {
        count = ATOI(query.GetRowData(0));
    }

    ReleaseDBConn(conn);
    return count;    
}

int PersonalVoiceMsg::GetVoicePageListByIndoorUUID(const std::string &uuid, int page_size, int page_index, PersonalVoiceMsgSendList &list)
{
    int offset = page_size * page_index;
    std::stringstream streamsql;
    streamsql << "/*master*/ select count(*) from PersonalVoiceMsgList where IndoorUUID = '"
              << uuid << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamsql.str());

    int data_count = 0;
    if (query.MoveToNextRow())
    {
        data_count = ATOI(query.GetRowData(0));
        if (page_size == 0)
        {
            page_size = 1;
        }
        std::stringstream streamsql1;
        streamsql1 << "/*master*/ select A.DoorUUID,A.DoorMac,A.Location,A.CreateTime,A.UUID,B.Status from PersonalVoiceMsgList B join PersonalVoiceMsg A on B.MsgUUID = A.UUID where B.IndoorUUID = '"
                << uuid << "' order by CreateTime desc limit " << offset << "," << page_size;
        CRldbQuery query1(tmp_conn);
        query1.Query(streamsql1.str());
        while (query1.MoveToNextRow())
        {
            PersonalVoiceMsgSendNode send_node;
            Snprintf(send_node.front_uuid, sizeof(send_node.front_uuid),query1.GetRowData(0));
            Snprintf(send_node.front_mac, sizeof(send_node.front_mac),query1.GetRowData(1));
            Snprintf(send_node.location, sizeof(send_node.location),query1.GetRowData(2));
            Snprintf(send_node.time, sizeof(send_node.time),query1.GetRowData(3));
            Snprintf(send_node.uuid, sizeof(send_node.uuid),query1.GetRowData(4));
            send_node.status = ATOI(query1.GetRowData(5));
            list.push_back(send_node);
        }
    }

    ReleaseDBConn(conn);
    return data_count;    
}

int PersonalVoiceMsg::DelVoiceMsgInfoByIndoorUUID(const std::string &msg_uuid, const std::string &dev_uuid)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::stringstream streamsql;
    streamsql << "delete from PersonalVoiceMsgList where IndoorUUID = '"
              << dev_uuid << "' and MsgUUID = '" << msg_uuid << "'";
    
    conn->Execute(streamsql.str());

    ReleaseDBConn(conn);
    return 0;
}

int PersonalVoiceMsg::GetExpiredVoiceMsgUrls(std::vector<std::string> &del_urls, int &id)
{
    std::stringstream streamsql;
    streamsql << "select ID,PicUrl,FileUrl,UUID from PersonalVoiceMsg where CreateTime > (curdate() - interval 31 day)" 
              << " and CreateTime < (curdate() - interval 30 day) and ID > " << id <<" order by ID limit 0,500";
    
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(ptmpconn);
    query.Query(streamsql.str());
    int count = 0;
    std::string msg_uuid;
    while (query.MoveToNextRow())
    {        
        id = ATOI(query.GetRowData(0));
        del_urls.push_back(query.GetRowData(1));
        del_urls.push_back(query.GetRowData(2));
        msg_uuid = query.GetRowData(3);
        //删除记录
        DelVoiceMsgByID(ptmpconn, msg_uuid);
        ++count;
    }

    ReleaseDBConn(conn);
    return count;
}

int PersonalVoiceMsg::UpdateVoiceMsgStatus(const std::string& msg_uuid, const std::string& dev_uuid)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::stringstream streamsql;
    streamsql << "update PersonalVoiceMsgList set Status= 1 where IndoorUUID = '" 
              << dev_uuid
              << "' and MsgUUID = '" << msg_uuid << "'";    

    int ret = ptmpconn->Execute(streamsql.str()) > 0 ? 0 : -1;
    if (-1 == ret)
    {
        AK_LOG_WARN << "UpdateVoiceMsgStatus failed, IndoorUUID = " << dev_uuid << " MsgUUID = " << msg_uuid;
        ReleaseDBConn(conn);
        return ret;
    }

    ReleaseDBConn(conn);
    return 0;
}

void PersonalVoiceMsg::DelVoiceMsgByID(CRldb* conn, const std::string& msg_uuid)
{
    if (msg_uuid.length() == 0 || conn == nullptr)
    {
        return;
    }

    char sql[128];
    snprintf(sql, sizeof(sql), "delete from PersonalVoiceMsg where UUID = '%s'", msg_uuid.c_str());

    char sql1[128];
    snprintf(sql1, sizeof(sql1), "delete from PersonalVoiceMsgList where MsgUUID = '%s'", msg_uuid.c_str());

    int ret = conn->Execute(sql);
    int ret1 = conn->Execute(sql1);
    if (ret < 0 || ret1< 0)
    {
        AK_LOG_WARN << "DelVoiceMsgByID failed";
    }

    return;
}





}


