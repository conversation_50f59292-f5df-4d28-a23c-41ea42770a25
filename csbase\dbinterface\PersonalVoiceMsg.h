#ifndef _DB_PERSONAL_VOICE_MSG_H__
#define _DB_PERSONAL_VOICE_MSG_H__
#include <string>
#include <memory>
#include <tuple>
#include "Rldb.h"

typedef struct PersonalVoiceMsg_T
{
    char uuid[64];
    char dev_uuid[64]; //门口机uuid
    char mac[20];    //门口机的Mac地址
    char project_uuid[64];
    int msg_type;
    int dev_type;
    char location[64];
    char uid[64];
    char pic_name[256];
    char file_name[256];
    char pic_url[256];
    char file_url[256];
}PersonalVoiceMsgInfo;

typedef struct PersonalVoiceMsgNode_T
{
    int type;    //收信人类型，2-室内机，100-app
    char uuid[64];
    char receiver_uuid[64]; //
    char msg_uuid[64]; //
}PersonalVoiceMsgNode;

typedef struct PersonalVoiceMsgSendNode_T
{
    int id;
    int status; //0-未读；1-已读
    char uuid[64]; //语音留言在云端保存的UUID
    char time[32]; //留言时间
    char location[32]; //门口机的Location
    char front_uuid[64]; //门口机的UUID
    char front_mac[20]; //门口机的mac
    char personal_uuid[64];
    char indoor_uuid[64];

    PersonalVoiceMsgSendNode_T() {
        memset(this, 0, sizeof(*this));
    }    
}PersonalVoiceMsgSendNode;

typedef std::vector<PersonalVoiceMsgSendNode>PersonalVoiceMsgSendList;
class CRldb;

namespace dbinterface{

class PersonalVoiceMsg
{
public:
    PersonalVoiceMsg();
    ~PersonalVoiceMsg();
    static int InsertPersonalVoiceMsg(const PersonalVoiceMsgInfo &voice_msg);
    static int InsertPersonalVoiceMsgList(const PersonalVoiceMsgNode &node);
    static int UpdatePersonalVoiceMsgPicUrl(const std::string &pic_url, const std::string &pic_name);
    static int UpdatePersonalVoiceMsgFileUrl(const std::string &file_url, const std::string &file_name);
    static int GetVoiceMsgInfoByUUID(const std::string &uuid, PersonalVoiceMsgInfo &voice_msg);
    static int GetUnreadCountByIndoorUUID(const std::string &uuid);
    static int GetVoicePageListByIndoorUUID(const std::string &uuid, int page_size, int page_index, PersonalVoiceMsgSendList &list);
    static int DelVoiceMsgInfoByIndoorUUID(const std::string &msg_uuid, const std::string &dev_uuid);
    static int GetExpiredVoiceMsgUrls(std::vector<std::string> &del_urls, int &id);
    static int UpdateVoiceMsgStatus(const std::string& msg_uuid, const std::string& dev_uuid);
    static void DelVoiceMsgByID(CRldb* conn, const std::string& msg_uuid);
    static int GetVoiceMsgListInfoByUUID(const std::string &uuid, PersonalVoiceMsgSendNode &msg_node);
    static int GetVoiceMsgInfoByMacAndFileName(const std::string &mac, const std::string &file_name, PersonalVoiceMsgInfo &voice_msg);
    static int GetVoiceMsgListInfoByMsgUUID(const std::string &msg_uuid, PersonalVoiceMsgSendList &list);
private:
};

}


#endif
