#include <sstream>
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/InterfaceComm.h"
#include "PmAccountMap.h"
#include <string.h>
#include "AkLogging.h"
#include "util.h"
#include "ConnectionPool.h"
#include "ConnectionManager.h"


namespace dbinterface{


static const std::string pm_account_sec = "AccountUUID,PersonalAccountUUID,PersonalAccount,ProjectUUID,AppStatus ";

PmAccountMap::PmAccountMap()
{

}

PmAccountMap::~PmAccountMap()
{

}

void PmAccountMap::GetPmAccountMapFromSql(PmAccountInfo& pm_account_map_info, CRldbQuery& query)
{
    Snprintf(pm_account_map_info.account_uuid, sizeof(pm_account_map_info.account_uuid), query.GetRowData(0));
    Snprintf(pm_account_map_info.personal_account_uuid, sizeof(pm_account_map_info.personal_account_uuid), query.GetRowData(1));
    Snprintf(pm_account_map_info.account, sizeof(pm_account_map_info.account), query.GetRowData(2));
    Snprintf(pm_account_map_info.project_uuid, sizeof(pm_account_map_info.project_uuid), query.GetRowData(3));
    pm_account_map_info.app_status = ATOI(query.GetRowData(4));
    return;
}


int PmAccountMap::checkPMAppAccountStatus(const std::string &uuid)

{
    std::stringstream streamSQL;
    streamSQL << "SELECT AppStatus FROM PmAccountMap WHERE PersonalAccountUUID = '"
              << uuid
              << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(ptmpconn);

    query.Query(streamSQL.str());
    int status = 0;
    if (query.MoveToNextRow())
    {
        status = ATOI(query.GetRowData(0));
    }
    ReleaseDBConn(conn);
    return status;
}

int PmAccountMap::GetPmInfoByAccount(const std::string &personal_account, PmAccountInfoPtr &account)
{
    std::stringstream streamSQL;
    streamSQL << "select ID, AppStatus, AccountUUID, PersonalAccountUUID, ProjectUUID from PmAccountMap "
              << "where PersonalAccount = '" << personal_account << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    if (query.MoveToNextRow())
    {
        account = std::make_shared<PmAccountInfo>();
        account->id = ATOI(query.GetRowData(0));
        account->app_status = ATOI(query.GetRowData(1));
        Snprintf(account->account_uuid, sizeof(account->account_uuid), query.GetRowData(2));
        Snprintf(account->personal_account_uuid, sizeof(account->personal_account_uuid), query.GetRowData(3));
        Snprintf(account->project_uuid, sizeof(account->project_uuid), query.GetRowData(4));
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    
    ReleaseDBConn(conn);
    return 0;
}

int PmAccountMap::checkPMAppAccountStatusByAccount(const std::string &uid)
{
    std::stringstream streamSQL;
    streamSQL <<"SELECT AppStatus FROM PmAccountMap WHERE PersonalAccount = '" << uid << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(ptmpconn);

    query.Query(streamSQL.str());
    int status = 0;
    if (query.MoveToNextRow())
    {
        status = ATOI(query.GetRowData(0));
    }
    ReleaseDBConn(conn);
    return status;
}

int PmAccountMap::GetPmListByProjectUUID(const std::string &project_uuid, std::vector<PmAccountInfo>& account_vec)
{
    std::stringstream stream_sql;
    stream_sql << "select " << pm_account_sec << "from PmAccountMap "
          << "where ProjectUUID = '" << project_uuid << "'";
   
    GET_DB_CONN_ERR_RETURN(conn, -1);
    CRldbQuery query(conn.get());
    query.Query(stream_sql.str());
    while(query.MoveToNextRow())
    {
        PmAccountInfo account;
        GetPmAccountMapFromSql(account, query);
        account_vec.push_back(account);
    }
    return 0;
}

}
