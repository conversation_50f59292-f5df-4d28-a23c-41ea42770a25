#ifndef _PMACCOUNTMAP_H__
#define _PMACCOUNTMAP_H__
#include <string>
#include <memory>
#include <tuple>


namespace dbinterface{

typedef struct Pm_Account_T
{
    int id;
    char account[32];
    int app_status;
    char timezone[64];
    char account_uuid[64];
    char personal_account_uuid[64];
    char project_uuid[64];
}PmAccountInfo;
typedef std::shared_ptr<PmAccountInfo> PmAccountInfoPtr;

class PmAccountMap
{
public:
    PmAccountMap();
    ~PmAccountMap();
    static int checkPMAppAccountStatus(const std::string &uuid);
    static int GetPmInfoByAccount(const std::string &personal_account, PmAccountInfoPtr &account);
    static int checkPMAppAccountStatusByAccount(const std::string &uid);
    static int GetPmListByProjectUUID(const std::string &project_uuid, std::vector<PmAccountInfo>& account_vec);
private:
    static void GetPmAccountMapFromSql(PmAccountInfo& pm_account_map_info, CRldbQuery& query);
};

}


#endif
