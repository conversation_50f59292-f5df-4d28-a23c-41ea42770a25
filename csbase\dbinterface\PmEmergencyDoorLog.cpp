#include <sstream>
#include "InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/PmEmergencyDoorLog.h"
#include <string.h>
#include "util_cstring.h"
#include "AkLogging.h"
#include "util.h"
#include <boost/algorithm/string.hpp>
#include "AkcsCommonDef.h"
#include "dbinterface/UUID.h"
#include "ConnectionManager.h"
#include "dbinterface/PropertyInfo.h"
#include "dbinterface/AccountUserInfo.h"
#include "dbinterface/Log/PersonalCapture.h"
#include "dbinterface/new-office/DevicesDoorList.h"
#include "util_virtual_door.h"
#include "dbinterface/resident/CommunityEmergencyDoorGroup.h"
#include "dbinterface/new-office/OfficeEmergencyDoor.h"
namespace dbinterface
{

PmEmergencyDoorLog::PmEmergencyDoorLog()
{

}

int PmEmergencyDoorLog::GetPmEmergencyDoorLogByUUID(const std::string &uuid, std::string &pm_uuid, PmEmergencyDoorLogInfoList &list)
{
    std::stringstream sql1;
    std::stringstream sql2;
    sql1 << "/*master*/select DeviceUUID, group_concat(Relay order by Relay separator ''), group_concat(SecurityRelay order by SecurityRelay separator '') "
         << "from PmEmergencyDoorLogList where PmEmergencyDoorLogUUID= '" << uuid << "' group by DeviceUUID";
    
    sql2 << "/*master*/select PmUUID,InstallerUUID,InitiatorType from PmEmergencyDoorLog where UUID = '"<< uuid << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql1.str());       
    while (query.MoveToNextRow())
    {
        PmEmergencyDoorLogInfo control_info;
        Snprintf(control_info.device_uuid, sizeof(control_info.device_uuid) , query.GetRowData(0)); 
        std::string relay = query.GetRowData(1);
        std::string security_relay = query.GetRowData(2);
        //relay和security_relay一同查出时,relay:1234,security_relay:0000,去掉多余的0
        boost::replace_all(relay,"0","");
        boost::replace_all(security_relay,"0","");
        Snprintf(control_info.relay, sizeof(control_info.relay), relay.c_str());
        Snprintf(control_info.security_relay, sizeof(control_info.security_relay), security_relay.c_str());	
		
        list.push_back(control_info);
    }

    query.Query(sql2.str());
    if(query.MoveToNextRow())
    {
        pm_uuid = query.GetRowData(0);
    }
    
    ReleaseDBConn(conn);    
    return 0;
}

//更新设备异常状态
void PmEmergencyDoorLog::UpdateDeviceAbnormalStatus(const std::string &msg_uuid,const std::string &device_uuid, int status_type)
{
    std::stringstream sql;
    sql << "update PmEmergencyDoorLogList set Status = '" << status_type
        << "' where PmEmergencyDoorLogUUID = '" << msg_uuid 
        << "' and DeviceUUID = '" << device_uuid << "'";
        
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }
    conn->Execute(sql.str());

    ReleaseDBConn(conn); 
}

//更新设备relay状态
int PmEmergencyDoorLog::UpdateDeviceRelayStatus(const std::string &device_uuid,const std::string &msg_uuid,const std::string &relay,const std::string &security_relay)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::vector<RelayStatus> relay_status(4, DISREGARD);
    std::vector<RelayStatus> security_relay_status(4, DISREGARD);

    //转换relay状态
    GetRelayStatus(relay, relay_status);
    GetRelayStatus(security_relay, security_relay_status);

    //更新relay状态
    UpdateRelayStatus(device_uuid, msg_uuid, relay_status, relay.size(), conn);
    //更新securityrelay状态
    UpdateSecurityRelayStatus(device_uuid, msg_uuid, security_relay_status, security_relay.size(), conn);
    //容错,设备上传的relay位数和数据库的对不上时,可能导致某一位relay没更新
    SetRelayStatusFailure(device_uuid, msg_uuid, conn);
    
    ReleaseDBConn(conn);
    return 0;   
}

//获取每个relay的状态：'-'代表不处理,'0'代表失败,'1'代表成功
void PmEmergencyDoorLog::GetRelayStatus(const std::string &relay,std::vector<RelayStatus> &status)
{
    for(unsigned int pos = 0; pos < relay.size(); pos++)
    {
        if(relay[pos] == RELAY_OPEN_FAILURE)
        {
            status[pos] = RelayStatus::FAILURE;
        }
        else if(relay[pos] == RELAY_OPEN_SUCCESS)
        {
            status[pos] = RelayStatus::SUCCESS;
        }
    }
}

//更新Relay状态
int PmEmergencyDoorLog::UpdateRelayStatus(const std::string &device_uuid,const std::string &msg_uuid,std::vector<RelayStatus> &status,int relay_size,RldbPtr conn)
{    
    std::stringstream sql;
    for(int pos = 0; pos < relay_size; pos++)
    {
        if(status[pos] == RelayStatus::DISREGARD)
        {
            continue;
        }
        int tmp = pos + 1;
        sql.str("");
        sql << "update PmEmergencyDoorLogList set Status = '" << status[pos]
            << "' where PmEmergencyDoorLogUUID = '"<< msg_uuid
            << "' and DeviceUUID = '"<< device_uuid 
            << "' and Relay = " << tmp;
        conn->Execute(sql.str());

    }
    
    return 0;
}

//更新SecurityRelay状态
int PmEmergencyDoorLog::UpdateSecurityRelayStatus(const std::string &device_uuid, const std::string &msg_uuid, std::vector<RelayStatus> &status, int relay_size, RldbPtr conn)
{    
    std::stringstream sql;
    for(int pos = 0; pos < relay_size; pos++)
    {
        if(status[pos] == RelayStatus::DISREGARD)
        {
            continue;
        }
        int tmp = pos + 1;
        sql.str("");
        sql << "update PmEmergencyDoorLogList set Status = '" << status[pos]
            << "' where PmEmergencyDoorLogUUID = '" << msg_uuid
            << "' and DeviceUUID = '" << device_uuid 
            << "' and SecurityRelay = " << tmp;
        conn->Execute(sql.str());

    }
    
    return 0;
}

int PmEmergencyDoorLog::InsertPmEmergencyDoorLog(const std::string &server_tag, const std::string &project_uuid, std::string &msg_uuid)
{   
    int ret = 0;
    std::stringstream streamSQL;
    UUID::GenerateUUID(server_tag, msg_uuid);
    
    streamSQL << "insert into PmEmergencyDoorLog (PmUUID,ProjectUUID,UUID,Type) values " 
              << "('','" << project_uuid << "','" << msg_uuid << "'," << OPEN_DOOR << ")";
    
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    ret = conn->Execute(streamSQL.str()) > 0 ? 0 : -1;

    ReleaseDBConn(conn); 
    return ret;
}

int PmEmergencyDoorLog::InsertPmEmergencyDoorLogList(const std::string &msg_uuid, const std::string &project_uuid, int project_type, bool is_open_all_door)
{
    //全选door,开所有支持该功能的设备的所有有效relay
    if (is_open_all_door)
    {
        ResidentDeviceList dev_list;
        ResidentDevices::GetPubSupportOpenDoorDevices(project_uuid, dev_list);
        if (project_type == (int)project::PROJECT_TYPE::RESIDENCE)
        {
            return InsertCommunityPmEmergencyDoorLogListByDevList(msg_uuid, project_uuid, dev_list);
        }
        else if (project_type == (int)project::PROJECT_TYPE::OFFICE)
        {
            return InsertOfficePmEmergencyDoorLogListByDevList(msg_uuid, project_uuid, dev_list);
        }
    }
    //非全选door, 根据用户选定要开的relay进行开启
    else 
    {
        if (project_type == (int)project::PROJECT_TYPE::RESIDENCE)
        {
            return InsertCommunityUserSelectedPmEmergencyDoorLogList(msg_uuid, project_uuid);
        }
        else if (project_type == (int)project::PROJECT_TYPE::OFFICE)
        {
            return InsertOfficeUserSelectedPmEmergencyDoorLogList(msg_uuid, project_uuid);
        }
    }
    return 0;
}

// 社区：设备->relay列表->插入数据库
int PmEmergencyDoorLog::InsertCommunityPmEmergencyDoorLogListByDevList(const std::string &msg_uuid, const std::string &project_uuid, const ResidentDeviceList &dev_list)
{
    if (dev_list.empty())
    {
        AK_LOG_WARN << "InsertCommunityPmEmergencyDoorLogListByDevList dev_list is empty. msg_uuid=" << msg_uuid << ", project_uuid=" << project_uuid;
        return 0;
    }

    GET_DB_CONN_ERR_RETURN(conn, -1);
    for (auto const &dev : dev_list)
    {
        std::vector<RELAY_INFO> relay_item;
        std::vector<RELAY_INFO> security_relay_item;
        ParseRelay(dev.relay, relay_item);
        ParseRelay(dev.security_relay, security_relay_item);
        if (0 != InsertRelayItem(dev.uuid, msg_uuid, relay_item, conn))
        {
            AK_LOG_WARN << "InsertCommunityPmEmergencyDoorLogListByDevList Relay failed.";
            return -1;
        }
        if (0 != InsertSecurityRelayItem(dev.uuid, msg_uuid, security_relay_item, conn))
        {
            AK_LOG_WARN << "InsertCommunityPmEmergencyDoorLogListByDevList SecurityRelay failed.";
            return -1;
        }
    }

    return 0;
}

// 办公：设备->door列表->转为relay->插入数据库
int PmEmergencyDoorLog::InsertOfficePmEmergencyDoorLogListByDevList(const std::string &msg_uuid, const std::string &project_uuid, const ResidentDeviceList &dev_list)
{
    for (auto const &dev : dev_list)
    {
        DevicesDoorInfoList devices_door_info_list;
        DevicesDoorList::GetDevicesDoorList(dev.uuid, devices_door_info_list);
        if (0 != InsertDoor(dev.uuid, msg_uuid, devices_door_info_list))
        {
            AK_LOG_WARN << "InsertOfficePmEmergencyDoorLogListByDevList Door failed.";
            return -1;
        }
    }

    return 0;
}

int PmEmergencyDoorLog::InsertRelay(const std::string &device_uuid, const std::string &msg_uuid, int relay_index, const std::string &relay_name, DoorRelayType relay_type, RldbPtr conn)
{
    std::string relay_column_name = relay_type == DoorRelayType::SECURITY_RELAY ? "SecurityRelay" : "Relay";

    std::stringstream sql;
    sql << "insert into PmEmergencyDoorLogList (PmEmergencyDoorLogUUID,DeviceUUID," << relay_column_name << ",RelayName) values "
        << "('" << msg_uuid << "','" << device_uuid << "'," << relay_index << ",'" << relay_name << "')";
    return conn->Execute(sql.str()) > 0 ? 0 : -1;
}

int PmEmergencyDoorLog::InsertRelayItem(const std::string &device_uuid, const std::string &msg_uuid, std::vector<RELAY_INFO> &relay_item, RldbPtr conn)
{
    int ret = 0;
    std::stringstream sql;
    int relay_num = relay_item.size();
    for(int i = 0; i < relay_num; i++)
    {
        if(!relay_item[i].enable)
        {
            continue;
        }
        ret = InsertRelay(device_uuid, msg_uuid, i + 1, relay_item[i].name, DoorRelayType::RELAY, conn);
    }
    return ret;
}

int PmEmergencyDoorLog::InsertSecurityRelayItem(const std::string &device_uuid, const std::string &msg_uuid, std::vector<RELAY_INFO> &relay_item, RldbPtr conn)
{
    int ret = 0;
    std::stringstream sql;
    int relay_num = relay_item.size();
    for(int i = 0; i < relay_num; i++)
    {
        if(!relay_item[i].enable)
        {
            continue;
        }
        ret = InsertRelay(device_uuid, msg_uuid, i + 1, relay_item[i].name, DoorRelayType::SECURITY_RELAY, conn);
    }
    return ret;
}

int PmEmergencyDoorLog::InsertDoor(const std::string &device_uuid, const std::string &msg_uuid, const DevicesDoorInfoList &devices_door_info_list)
{
    int ret = 0;
    GET_DB_CONN_ERR_RETURN(conn, -1);
    for (auto const &door : devices_door_info_list)
    {
        if (!IsSubscribedDoor(door.enable, door.active, door.expire))
        {
            continue;
        }
        int relay_index = GetRelayIndexByControlledRelay(door.controlled_relay);

        ret = InsertRelay(device_uuid, msg_uuid, relay_index, door.name, door.relay_type, conn);
    }

    return ret;
}

int PmEmergencyDoorLog::InsertCommunityUserSelectedPmEmergencyDoorLogList(const std::string &msg_uuid, const std::string &project_uuid)
{
    CommunityEmergencyDoorGroupInfoList community_emergency_door_group_info_list;
    CommunityEmergencyDoorGroup::GetCommunityEmergencyDoorGroupListByAccountUUID(project_uuid, community_emergency_door_group_info_list);

    GET_DB_CONN_ERR_RETURN(conn, -1);

    int ret = 0;

    for (const auto& community_emergency_door_group_info : community_emergency_door_group_info_list)
    {
        if (community_emergency_door_group_info.relay_enable)
        {
            ret = InsertRelay(community_emergency_door_group_info.devices_uuid, msg_uuid, community_emergency_door_group_info.relay_index,
                         community_emergency_door_group_info.relay_name, community_emergency_door_group_info.relay_type, conn);
        }
    }

    return ret;
}   

int PmEmergencyDoorLog::InsertOfficeUserSelectedPmEmergencyDoorLogList(const std::string &msg_uuid, const std::string &project_uuid)
{
    OfficeEmergencyDoorInfoList office_emergency_door_info_list;
    OfficeEmergencyDoor::GetOfficeEmergencyDoorListByAccountUUID(project_uuid, office_emergency_door_info_list);

    int ret = 0;

    for (const auto& office_emergency_door_info : office_emergency_door_info_list)
    {
        DevicesDoorInfo device_door_info;
        DevicesDoorList::GetDevicesDoorInfoByUUID(office_emergency_door_info.devices_door_list_uuid, device_door_info);
        if (IsSubscribedDoor(device_door_info.enable, device_door_info.active, device_door_info.expire))
        {
            GET_DB_CONN_ERR_RETURN(conn, -1);
            ret = InsertRelay(office_emergency_door_info.devices_uuid, msg_uuid, GetRelayIndexByControlledRelay(device_door_info.controlled_relay),
                         device_door_info.name, device_door_info.relay_type, conn);
        }
    }

    return ret;
}

void PmEmergencyDoorLog::SetRelayStatusFailure(const std::string &device_uuid, const std::string &msg_uuid, RldbPtr conn)
{
    std::stringstream streamsql;
    streamsql << "update PmEmergencyDoorLogList set Status = " << RelayStatus::FAILURE
              << " where PmEmergencyDoorLogUUID = '" << msg_uuid 
              << "' and DeviceUUID = '" << device_uuid 
              << "' and Status = " << RelayStatus::PROCESSING;
    conn->Execute(streamsql.str());

}

int PmEmergencyDoorLog::GetProjectUUIDByUUID(const std::string& uuid, std::string& project_uuid)
{
    std::stringstream stream_sql;
    stream_sql << "select ProjectUUID from PmEmergencyDoorLog where UUID = '" << uuid << "'";
    
    GET_DB_CONN_ERR_RETURN(conn, -1);
    CRldbQuery query(conn.get());
    
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        project_uuid = query.GetRowData(0);
    }
    else
    {
        AK_LOG_INFO << "GetProjectUUIDByUUID Failed, UUID = " << uuid;
        return -1;
    }
    
    return 0;
}

int PmEmergencyDoorLog::GetEmergencyDoorLogListByUUID(const std::string &uuid, PmEmergencyDoorLogInfoList &list)
{
    std::stringstream stream_sql;
    stream_sql << "/*master*/select DeviceUUID, group_concat(Relay order by Relay separator ''), group_concat(SecurityRelay order by SecurityRelay separator '') "
               << "from PmEmergencyDoorLogList where PmEmergencyDoorLogUUID= '" << uuid << "' group by DeviceUUID";

    GET_DB_CONN_ERR_RETURN(tmp_conn, -1);
    CRldbQuery query(tmp_conn.get());
    
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        PmEmergencyDoorLogInfo control_info;
        Snprintf(control_info.device_uuid, sizeof(control_info.device_uuid) , query.GetRowData(0)); 
        std::string relay = query.GetRowData(1);
        std::string security_relay = query.GetRowData(2);
        
        //relay和security_relay一同查出时,relay:1234,security_relay:0000,去掉多余的0
        boost::replace_all(relay,"0","");
        boost::replace_all(security_relay,"0","");
        Snprintf(control_info.relay, sizeof(control_info.relay), relay.c_str());
        Snprintf(control_info.security_relay, sizeof(control_info.security_relay), security_relay.c_str()); 

        list.push_back(control_info);
    }
    
    return 0;
}

std::string PmEmergencyDoorLog::GetEmergencyDoorLogInitiatorByUUID(const std::string &uuid)
{
    std::string pm_uuid;
    std::string installer_uuid;
    EmergencyInitiatorType initiator_type = EmergencyInitiatorType::UNKNOWN;

    {
        std::stringstream stream_sql;
        stream_sql <<  "/*master*/select PmUUID,InstallerUUID,InitiatorType from PmEmergencyDoorLog where UUID = '" << uuid << "'";

        GET_DB_CONN_ERR_RETURN(tmp_conn, "");
        CRldbQuery query(tmp_conn.get());

        query.Query(stream_sql.str());
        if (query.MoveToNextRow())
        {
            pm_uuid = query.GetRowData(0);
            installer_uuid = query.GetRowData(1);
            initiator_type = EmergencyInitiatorType(ATOI(query.GetRowData(2)));
        }
    }

    std::string initiator;
    if (initiator_type == EmergencyInitiatorType::PM)
    {
        dbinterface::PropertyInfo::GetFullNameByUUID(pm_uuid, initiator);
    }
    else if (initiator_type == EmergencyInitiatorType::INSTALLER)
    {
        UserInfoAccount account_user_info;
        dbinterface::AccountUserInfo::GetAccountUserInfoByAccountUUID(installer_uuid, account_user_info);
        initiator = account_user_info.account;
    }   

    return initiator;
}

RelayStatus PmEmergencyDoorLog::CheckDevStatus(const ResidentDev& dev)
{
    // 离线记录异常doorlog
    if (dev.status == DeviceStatus::DEVICE_STATUS_OFFLINE)
    {
        AK_LOG_WARN << "EmergencyControlNotify device is offline, mac = " << dev.mac;
        return RelayStatus::OFFLINE;
    }

    if (dev.dclient_ver < D_CLIENT_VERSION_6520)
    {
        AK_LOG_WARN << "EmergencyControlNotify, dclient version is lower than 6520, mac = " << dev.mac << ", dclient ver = " << dev.dclient_ver;
        return RelayStatus::VERSION_MISMATCH;
    }
    
    return RelayStatus::PROCESSING;
}

void PmEmergencyDoorLog::RecordAbnormalStatus(const std::string& emergency_uuid, const std::string& dev_uuid, const std::string& initiator, dbinterface::RelayStatus relay_status, ACT_OPEN_DOOR_TYPE act_type, int delivery)
{
    // 更新EmergencyControl
    dbinterface::PmEmergencyDoorLog::UpdateDeviceAbnormalStatus(emergency_uuid, dev_uuid, relay_status);

    // 记录doorlog    
    dbinterface::PersonalCapture::RecordEmergencyContorlDoorLog(dev_uuid, initiator, act_type, dbinterface::RelayStatus::OFFLINE, delivery);

    return;
}

ACT_OPEN_DOOR_TYPE PmEmergencyDoorLog::GetEmergencyControlType(int control_type)
{
    ACT_OPEN_DOOR_TYPE act_type = ACT_OPEN_DOOR_TYPE::ACT_OPEN_DOOR_TYPE_UNKNOW;
    
    if (control_type == PmEmergencyDoorControlType::CONTROL_TYPE_OPEN)
    {
        act_type =  ACT_OPEN_DOOR_TYPE::PM_UNLOCK;
    }
    else if (control_type == PmEmergencyDoorControlType::CONTROL_TYPE_CLOSE)
    {
        act_type = ACT_OPEN_DOOR_TYPE::PM_LOCK;
    }

    return act_type;
}

}
