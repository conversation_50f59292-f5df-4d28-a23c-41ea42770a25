#ifndef __DB_PM_EMERGENCY_DOOR_LOG_H__
#define __DB_PM_EMERGENCY_DOOR_LOG_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include "ConnectionPool.h"
#include "util.h"
#include "ConnectionManager.h"
#include "dbinterface/new-office/DevicesDoorList.h"
#include "dbinterface/resident/ResidentDevices.h"

namespace dbinterface
{

//设备上报relay开门结果，用于转换成数据库中relay状态
#define RELAY_OPEN_FAILURE         '0'   //relay开门失败
#define RELAY_OPEN_SUCCESS         '1'   //relay开门成功
#define RELAY_DISREGARD            '-'   //relay不处理


//数据库relay状态:0进行中 1成功           2开门失败 3设备离线         4不处理 
enum RelayStatus 
{
    PROCESSING = 0, 
    SUCCESS = 1,
    FAILURE = 2, 
    OFFLINE = 3,
    VERSION_MISMATCH = 4,
    TIMEOUT = 5,
    DISREGARD = 6,
};

enum class EmergencyInitiatorType
{
    UNKNOWN = 0,
    PM = 1,
    INSTALLER = 2
};

typedef struct PmEmergencyDoorLog_T
{
    char device_uuid[64];
    char relay[8];
    char security_relay[8];

    PmEmergencyDoorLog_T() {
        memset(this, 0, sizeof(*this));
    }
}PmEmergencyDoorLogInfo;

typedef std::list<PmEmergencyDoorLogInfo> PmEmergencyDoorLogInfoList;

class PmEmergencyDoorLog
{
public:
    PmEmergencyDoorLog();
    ~PmEmergencyDoorLog();
    static int GetPmEmergencyDoorLogByUUID(const std::string &uuid, std::string &pm_uuid, PmEmergencyDoorLogInfoList &list);
    static int UpdateDeviceRelayStatus(const std::string &device_uuid, const std::string &msg_uuid, const std::string &relay, const std::string &security_relay);
    static void UpdateDeviceAbnormalStatus(const std::string &msg_uuid,const std::string &device_uuid, int status_type);
    static int InsertPmEmergencyDoorLog(const std::string &server_tag, const std::string &project_uuid, std::string &msg_uuid);
    static int InsertPmEmergencyDoorLogList(const std::string &msg_uuid, const std::string &project_uuid, int project_type, bool is_open_all_door);
    static int GetProjectUUIDByUUID(const std::string &uuid, std::string& project_uuid);
    static int GetEmergencyDoorLogListByUUID(const std::string &uuid, PmEmergencyDoorLogInfoList &list);
    static std::string GetEmergencyDoorLogInitiatorByUUID(const std::string &uuid);
    static RelayStatus CheckDevStatus(const ResidentDev& dev);
    static ACT_OPEN_DOOR_TYPE GetEmergencyControlType(int control_type);
    static void RecordAbnormalStatus(const std::string& emergency_uuid, const std::string& dev_uuid, const std::string& initiator, dbinterface::RelayStatus relay_status, ACT_OPEN_DOOR_TYPE open_type, int delivery);
private: 
    static void GetRelayStatus(const std::string &relay,std::vector<RelayStatus> &status);
    static int UpdateRelayStatus(const std::string &device_uuid,const std::string &msg_uuid,std::vector<RelayStatus> &status,int relay_size,RldbPtr conn);
    static int UpdateSecurityRelayStatus(const std::string &device_uuid,const std::string &msg_uuid,std::vector<RelayStatus> &status,int relay_size,RldbPtr conn);  
    static int InsertRelayItem(const std::string &device_uuid, const std::string &msg_uuid, std::vector<RELAY_INFO> &relay_item, RldbPtr conn);
    static int InsertSecurityRelayItem(const std::string &device_uuid, const std::string &msg_uuid, std::vector<RELAY_INFO> &relay_item, RldbPtr conn);
    static int InsertRelay(const std::string &device_uuid, const std::string &msg_uuid, int relay_index, const std::string &relay_name, DoorRelayType relay_type, RldbPtr conn);
    static int InsertDoor(const std::string &device_uuid, const std::string &msg_uuid, const DevicesDoorInfoList &devices_door_info_list);

    static void SetRelayStatusFailure(const std::string &device_uuid, const std::string &msg_uuid, RldbPtr conn);
    static int InsertCommunityPmEmergencyDoorLogListByDevList(const std::string &msg_uuid, const std::string &project_uuid, const ResidentDeviceList &dev_list);
    static int InsertOfficePmEmergencyDoorLogListByDevList(const std::string &msg_uuid, const std::string &project_uuid, const ResidentDeviceList &dev_list);
    static int InsertCommunityUserSelectedPmEmergencyDoorLogList(const std::string &msg_uuid, const std::string &project_uuid);
    static int InsertOfficeUserSelectedPmEmergencyDoorLogList(const std::string &msg_uuid, const std::string &project_uuid);
};


}

#endif
