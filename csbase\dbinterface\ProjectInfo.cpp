#include "ProjectInfo.h"
#include <chrono>
#include <ctime>
#include <iostream>
#include "util.h"
#include "dbinterface/Account.h"
#include "dbinterface/CommunityInfo.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"


void ProjectInfo::init(const std::string& mac, int is_personal, LINKER_NORMAL_MSG &linker_msg)
{
    ResidentDev dev;
    ProjectUserInfo project_info;
    if (is_personal)
    {
        if (0 ==dbinterface::ResidentPerDevices::GetMacDev(mac, dev) )
        {
            linker_msg.project_type = project::PERSONAL;
            std::string node = dev.node;
            GetPersonalProjectInfo(node, project_info);
            
            Snprintf(linker_msg.dev_uuid, sizeof(linker_msg.dev_uuid),  dev.uuid);
            Snprintf(linker_msg.dev_name, sizeof(linker_msg.dev_name),  dev.location);
            linker_msg.dev_type = dev.dev_type;
            linker_msg.enable_smarthome = project_info.enable_smarthome;
            Snprintf(linker_msg.project_uuid, sizeof(linker_msg.project_uuid),  project_info.project_uuid);
            Snprintf(linker_msg.ins_uuid, sizeof(linker_msg.ins_uuid),  project_info.ins_uuid);
        }
    }
    else
    {
        if (0 == dbinterface::ResidentDevices::GetMacDev(mac, dev))
        {
            int mng_id = dev.project_mng_id;
            if (dev.project_type == project::OFFICE)
            {
                linker_msg.enable_smarthome = 0;
                GetOfficeProjectInfo(mng_id, dev.node, project_info);
                linker_msg.project_type = project::OFFICE;
            }
            else
            {
                GetCommProjectInfo(mng_id, dev.node, project_info);
                linker_msg.project_type = project::RESIDENCE;
            }
            
            linker_msg.enable_smarthome = project_info.enable_smarthome;
            Snprintf(linker_msg.project_uuid, sizeof(linker_msg.project_uuid),  project_info.project_uuid);
            Snprintf(linker_msg.ins_uuid, sizeof(linker_msg.ins_uuid),  project_info.ins_uuid);
            Snprintf(linker_msg.dev_uuid, sizeof(linker_msg.dev_uuid),  dev.uuid);
            Snprintf(linker_msg.dev_name, sizeof(linker_msg.dev_name),  dev.location);
            linker_msg.dev_type = dev.dev_type;
            linker_msg.dev_grade = dev.grade;
        } 
    }

    return;
}

void ProjectInfo::init(const std::string& account, LINKER_NORMAL_MSG &linker_msg)
{
    std::string node;
    ProjectUserInfo project_info;
    ResidentPerAccount personal_account;
    memset(&personal_account, 0, sizeof(personal_account));
    int ret = dbinterface::ResidentPersonalAccount::GetUidAccount(account, personal_account);
    if (ret != 0)
    {
        AK_LOG_WARN << "GetUidAccount failed";
        return;
    } 

    Snprintf(linker_msg.account_uuid, sizeof(linker_msg.account_uuid),  personal_account.uuid);
    Snprintf(linker_msg.account_name, sizeof(linker_msg.account_name),  personal_account.name);
    linker_msg.phone_expire_timestamp = personal_account.phone_exp_stamp;
    linker_msg.expire_timestamp = personal_account.exp_stamp;
    linker_msg.role = personal_account.role;

    if (personal_account.role == ACCOUNT_ROLE_COMMUNITY_MAIN 
        || personal_account.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT
        || personal_account.role == ACCOUNT_ROLE_COMMUNITY_PM)
    {
        linker_msg.project_type = project::RESIDENCE;
        int mng_id = 0;
        if (personal_account.role == ACCOUNT_ROLE_COMMUNITY_MAIN)
        {
            node = personal_account.account;
            mng_id = personal_account.parent_id;
        }
        else
        {
            ResidentPerAccount main_account;
            memset(&main_account, 0, sizeof(main_account));
            if (0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(personal_account.parent_uuid, main_account))
            {
                node = main_account.account;
                mng_id = main_account.parent_id;
            }
        }

        GetCommProjectInfo(mng_id, node, project_info);
        linker_msg.enable_smarthome = project_info.enable_smarthome;
        Snprintf(linker_msg.project_uuid, sizeof(linker_msg.project_uuid),  project_info.project_uuid);
        Snprintf(linker_msg.ins_uuid, sizeof(linker_msg.ins_uuid),  project_info.ins_uuid);
        Snprintf(linker_msg.node_uuid, sizeof(linker_msg.node_uuid),  project_info.node_uuid);
    }
    else if (personal_account.role == ACCOUNT_ROLE_PERSONNAL_MAIN || personal_account.role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT)
    {
        linker_msg.project_type = project::PERSONAL;
        if (personal_account.role == ACCOUNT_ROLE_PERSONNAL_MAIN)
        {
            node = personal_account.account;
        }
        else
        {
            ResidentPerAccount main_account;
            memset(&main_account, 0, sizeof(main_account));
            if (0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(personal_account.uuid, main_account))
            {
                node = main_account.account;
            }
        }

        GetPersonalProjectInfo(node, project_info);
        linker_msg.enable_smarthome = project_info.enable_smarthome;
        Snprintf(linker_msg.ins_uuid, sizeof(linker_msg.ins_uuid),  project_info.ins_uuid);
        Snprintf(linker_msg.node_uuid, sizeof(linker_msg.node_uuid),  project_info.node_uuid);
        Snprintf(linker_msg.project_uuid, sizeof(linker_msg.project_uuid),  project_info.project_uuid);
    }
    
    return;
}

//日志表获取项目uuid
void ProjectInfo::GetLogCaptureProjectUUID(const DEVICE_SETTING& dev, std::string &project_uuid)
{
    if (dev.project_type == project::RESIDENCE || dev.project_type == project::OFFICE)
    {
        dbinterface::AccountInfo account;
        if (0 == dbinterface::Account::GetAccountById(dev.manager_account_id, account))
        {
            project_uuid = account.uuid;
        }
    }
    else
    {
        ResidentPerAccount per_account;
        memset(&per_account, 0, sizeof(per_account));
        if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(dev.device_node, per_account))
        {
            project_uuid = per_account.uuid;
        }
    }
}

//日志表获取项目uuid
void ProjectInfo::GetLogCaptureProjectUUID(const std::string& mac, std::string &project_uuid)
{
    ResidentDev dev;
    ResidentDev per_dev;

    if (0 == dbinterface::ResidentDevices::GetMacDev(mac, dev))
    {
        dbinterface::AccountInfo account;
        if (0 == dbinterface::Account::GetAccountById(dev.project_mng_id, account))
        {
            project_uuid = account.uuid;
        }
    }
    else if (0 == dbinterface::ResidentPerDevices::GetMacDev(mac, per_dev))
    {
        ResidentPerAccount per_account;
        if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(per_dev.node, per_account))
        {
            project_uuid = per_account.uuid;
        }
    }

}

void ProjectInfo::GetPersonalProjectInfo(const std::string &node, ProjectUserInfo &project_info)
{
    dbinterface::AccountInfo ins_account;
    ResidentPerAccount personal_account;
    memset(&personal_account, 0, sizeof(personal_account));

    if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(node, personal_account))
    {
        project_info.enable_smarthome = personal_account.enable_smarthome;
        Snprintf(project_info.node_uuid, sizeof(project_info.node_uuid),  personal_account.uuid);
        Snprintf(project_info.timezone, sizeof(project_info.timezone),  personal_account.timezone);
        if (0 == dbinterface::Account::GetAccountByUUID(personal_account.parent_uuid, ins_account))
        {
            Snprintf(project_info.project_uuid, sizeof(project_info.project_uuid),  ins_account.uuid);
            Snprintf(project_info.ins_uuid, sizeof(project_info.ins_uuid),  ins_account.uuid);
        }
    }
}

void ProjectInfo::GetCommProjectInfo(int mng_id, const std::string &node, ProjectUserInfo &project_info)
{
    dbinterface::AccountInfo project_account;
    dbinterface::AccountInfo ins_account;

    CommunityInfoPtr comm_info = std::make_shared<CommunityInfo>(mng_id);
    if (comm_info != nullptr)
    {
        project_info.enable_smarthome = comm_info->EnableSmartHome();
        project_info.is_support_scan_indoor_qrcode_to_reg_enduser = comm_info->IsSupportScanIndoorQRCodeToReg();
    }
    Snprintf(project_info.timezone, sizeof(project_info.timezone),  comm_info->TimeZone().c_str());
    
    if (0 == dbinterface::Account::GetAccountById(mng_id, project_account))
    {
        //根据mng_id获取到小区的account
        Snprintf(project_info.project_uuid, sizeof(project_info.project_uuid),  project_account.uuid);
        //根据小区的manage_group获取ins的account
        dbinterface::Account::GetAccountById(project_account.manage_group, ins_account);
        Snprintf(project_info.ins_uuid, sizeof(project_info.ins_uuid),  ins_account.uuid);
    }


    if (node.size() > 0)
    {
        std::string node_uuid = GetNodeUUID(node);
        Snprintf(project_info.node_uuid, sizeof(project_info.node_uuid),  node_uuid.c_str());
    }
}

void ProjectInfo::GetOfficeProjectInfo(int mng_id, const std::string &node, ProjectUserInfo &project_info)
{
    dbinterface::AccountInfo project_account;
    dbinterface::AccountInfo ins_account;

    project_info.enable_smarthome = 0;

    if (0 == dbinterface::Account::GetAccountById(mng_id, project_account))
    {
        //根据mng_id获取到小区的account
        Snprintf(project_info.project_uuid, sizeof(project_info.project_uuid),  project_account.uuid);
        //根据office的manage_group获取ins的account
        dbinterface::Account::GetAccountById(project_account.manage_group, ins_account);
        Snprintf(project_info.ins_uuid, sizeof(project_info.ins_uuid),  ins_account.uuid);
    }

    if (node.size() > 0)
    {
        std::string node_uuid = GetNodeUUID(node);
        Snprintf(project_info.node_uuid, sizeof(project_info.node_uuid),  node_uuid.c_str());
    }
}

std::string ProjectInfo::GetNodeUUID(const std::string &node)
{
    ResidentPerAccount node_info;
    memset(&node_info, 0, sizeof(node_info));
    if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(node, node_info))
    {
        return node_info.uuid;
    }
    return "";
}
