#ifndef __PROJECT_INFO_H_
#define __PROJECT_INFO_H_
#include <string>
#include <memory>
#include <vector>
#include "AkcsCommonDef.h"
#include "AkLogging.h"
#include "AkcsCommonSt.h"

typedef struct Project_T
{
    char project_uuid[64];
    char ins_uuid[64];
    char node_uuid[64];
    int enable_smarthome;
    char timezone[32];
    bool is_support_scan_indoor_qrcode_to_reg_enduser;
    Project_T() {
        memset(this, 0, sizeof(*this));
    }
}ProjectUserInfo;

class ProjectInfo
{
public:
    ProjectInfo()
    {
        
    }

    //根据设备信息初始化
    ProjectInfo(const std::string& mac, int is_personal, LINKER_NORMAL_MSG &linker_msg)
    {
        init(mac, is_personal, linker_msg);
    }

    //根据用户信息初始化
    ProjectInfo(const std::string& account, LINKER_NORMAL_MSG &linker_msg)
    {
        init(account, linker_msg);
    }

    ~ProjectInfo()
    {

    }

    void GetPersonalProjectInfo(const std::string &node, ProjectUserInfo &project_info);
    void GetCommProjectInfo(int mng_id, const std::string &node, ProjectUserInfo &project_info);
    void GetOfficeProjectInfo(int mng_id, const std::string &node, ProjectUserInfo &project_info);
    
    void GetLogCaptureProjectUUID(const DEVICE_SETTING& dev, std::string &project_uuid);
    void GetLogCaptureProjectUUID(const std::string& mac, std::string &project_uuid);

private:
    std::string GetNodeUUID(const std::string &node);
    void init(const std::string& mac, int is_personal, LINKER_NORMAL_MSG &linker_msg);
    void init(const std::string& account, LINKER_NORMAL_MSG &linker_msg);

};


#endif
