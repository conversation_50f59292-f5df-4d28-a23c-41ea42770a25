#include <sstream>
#include <stdint.h>
#include "InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "ProjectUserManage.h"
#include <string.h>
#include "AkLogging.h"
#include "AkcsCommonDef.h"
#include "AccountUserInfo.h"
#include "dbinterface/resident/PersonalAccountUserInfo.h"
#include "dbinterface/AccountUserInfo.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/PersonalAccountCnf.h"
#include "dbinterface/CommunityInfo.h"
#include "dbinterface/DevOfflineLog.h"
#include "ConnectionManager.h"
#include "dbinterface/DataConfusion.h"
#include "ConnectionManager.h"
#include "util.h"
#include "dbinterface/PmAccountMap.h"


namespace dbinterface
{

int GetDeviceTypeByRole(int role)
{
    int dev_type =  csmain::COMMUNITY_NONE;
    if (role == ACCOUNT_ROLE_PERSONNAL_MAIN || role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT)
    {  
        dev_type = csmain::PERSONNAL_APP;
    }
    else if (role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT || role == ACCOUNT_ROLE_COMMUNITY_MAIN || role == ACCOUNT_ROLE_COMMUNITY_PM)
    {
        dev_type = csmain::COMMUNITY_APP;
    }
    else if (IsOfficeRole(role))
    {
        dev_type = csmain::OFFICE_APP;
    }
    
    return dev_type;
}



int ProjectUserManage::GetUserTypeBySip(const std::string &sip)
{
    SipInfo info;
    memset(&info, 0, sizeof(info));
    GetSipInfoBySip(sip, info);
    return info.sip_type;
}

int ProjectUserManage::GetSipInfoBySip(const std::string &sip, SipInfo &info)
{
    int type = 0;
    int role = 0;
    int grade = 0;
    int project_type = 0;
    int dev_type = csmain::COMMUNITY_NONE;
    std::string project_uuid = "";

    char sql[1024] = "";
    snprintf(sql, sizeof(sql),
        "select 0,Role,0,0,'',ParentUUID,UUID,'' From PersonalAccount where Account='%s' \
        union all select 1,0,0,0,Node,'',UUID,'' From PersonalDevices where SipAccount='%s' \
        union all select 2,0,ProjectType,Grade,Node,'',UUID,AccountUUID From Devices where SipAccount='%s';",
        sip.c_str(), sip.c_str(), sip.c_str()
    );

    {
        // 这里需要包在{} 里面，否则会报错: 一个线程分配两个sql连接
        GET_DB_CONN_ERR_RETURN(tmp_conn, dev_type);
        CRldbQuery query(tmp_conn.get());
        query.Query(sql);
        if (query.MoveToNextRow() == false)
        {
            return -1;
        }

        type = ATOI(query.GetRowData(0));
        role = ATOI(query.GetRowData(1));
        project_type = ATOI(query.GetRowData(2));
        grade = ATOI(query.GetRowData(3));
        snprintf(info.dev_node, sizeof(info.dev_node), "%s", query.GetRowData(4));
        snprintf(info.parent_uuid, sizeof(info.parent_uuid), "%s", query.GetRowData(5));
        snprintf(info.uuid, sizeof(info.uuid), "%s", query.GetRowData(6));
        project_uuid = query.GetRowData(7);
    }

    if (type == 0)//TODO: 可以再优化
    {
        dev_type = GetDeviceTypeByRole(role);
        if (dev_type == csmain::PERSONNAL_APP)
        {
            project_type = project::PERSONAL;
        }
        else if (dev_type == csmain::COMMUNITY_APP)
        {
            project_type = project::RESIDENCE;
        }
        else
        {
            if (IsOfficeNewRole(role) == true)
            {
                project_type = project::OFFICE_NEW;
            }
            else{
                project_type = project::OFFICE;
            }
        }
    }
    else if (type == 1)
    {
        dev_type = csmain::PERSONNAL_DEV;
        project_type = project::PERSONAL;
    }
    else if (type == 2)
    {
        OfficeInfo office_info(project_uuid);
        if (office_info.IsNew() == true)
        {
            project_type = project::OFFICE_NEW;
            dev_type = csmain::OFFICE_DEV;
        }
        else if (project_type == project::OFFICE)
        {
            dev_type = csmain::OFFICE_DEV;
        }
        else
        {
            dev_type = csmain::COMMUNITY_DEV;
        }
    }

    snprintf(info.sip, sizeof(info.sip), "%s", sip.c_str());
    info.role = role;
    info.grade = grade;
    info.project_type = project_type;
    info.sip_type = dev_type;
    return 0;
}

//获取用户类型 user: uid/email/mobile-phone
int ProjectUserManage::CheckUserType(const std::string& user)
{
    int dev_type =  csmain::COMMUNITY_NONE;
    std::string last_login_account;
    std::string main_user_account;
    PerAccountUserInfo user_info;

    // 先在PersonalAccountUserInfo表中查找用户
    if(0 == dbinterface::PersonalAccountUserInfo::GetPerAccountInfoByLoginAccount(user, user_info))
    {
        last_login_account = user_info.last_login_account;
        main_user_account = user_info.main_user_account;
    }
    else 
    {
        // 再到AccountUserInfo表中查找用户
        UserInfoAccount account_user_info;
        memset(&account_user_info, 0, sizeof(account_user_info));
        if (0 == dbinterface::AccountUserInfo::GetAccountUserInfoOnlyByLoginAccount(user, account_user_info))
        {
            last_login_account = account_user_info.last_login_account;
            main_user_account = account_user_info.main_user_account;
        }
    }
    AK_LOG_INFO << "login_account:" << user << ", last_login_account:" << last_login_account << ", main_account:" << main_user_account;

    if (main_user_account.size() == 0)
    {
        return dev_type;
    }

    
    ResidentPerAccount account;
    memset(&account, 0, sizeof(account));
    //如果last没有找到账号，那是因为被删除了，所以需要用主站点的信息
    if (0 == last_login_account.length() || 0 != dbinterface::ResidentPersonalAccount::GetUidAccount(last_login_account, account))
    {
        if (0 != dbinterface::ResidentPersonalAccount::GetUidAccount(main_user_account, account))
        {
            AK_LOG_WARN << "get user info failed, user not exist, user=" << user;
            return dev_type;
        }
    }
    
    dev_type = GetDeviceTypeByRole(account.role);
    return dev_type;
}

int ProjectUserManage::UpdateOfficeAllAccountDataVersion(int mng_id)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::stringstream streamsql;
    streamsql << "update PersonalAccount set Version=UNIX_TIMESTAMP() where ParentID =" 
              << mng_id << " and Role in(" << GetOfficeRoleStr() << ")";

    int ret = ptmpconn->Execute(streamsql.str()) >= 0 ? 0 : -1;
    if (-1 == ret)
    {
        AK_LOG_WARN << "UpdateDataVersion failed, Mngid = " << mng_id;
        ReleaseDBConn(conn);
        return ret;
    }

    ReleaseDBConn(conn);
    return 0;

}

int ProjectUserManage::UpdateCommunityAllAccountDataVersion(int mng_id)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    // 先通过mng_id查询出所有UnitID
    std::vector<int> unit_ids;
    std::stringstream stream_sql;
    stream_sql << "select ID from CommunityUnit where MngAccountID = " << mng_id;
    
    CRldbQuery query(tmp_conn);
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {   
        unit_ids.push_back(ATOI(query.GetRowData(0)));
    }
    std::string unit_ids_str = ListToSeparatedFormatString(unit_ids);
    
    // 再通过UnitID更新主从账号数据版本
    std::stringstream stream_sql2;
    stream_sql2 << "update PersonalAccount set Version=UNIX_TIMESTAMP() where UnitID in (" << unit_ids_str << ") "
                << "and role in (" << ACCOUNT_ROLE_COMMUNITY_MAIN << "," << ACCOUNT_ROLE_COMMUNITY_ATTENDANT << ")";
    int ret = tmp_conn->Execute(stream_sql2.str()) >= 0 ? 0 : -1;

    if (-1 == ret)
    {
       AK_LOG_WARN << "UpdateCommunityAllAccountDataVersion failed, Mngid = " << mng_id;
       ReleaseDBConn(conn);
       return ret;
    }
           
    ReleaseDBConn(conn);
    return 0;
}

int ProjectUserManage::UpdateAccountDataVersionByUid(const std::string &uid)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::stringstream streamsql;
    streamsql << "update PersonalAccount set Version=UNIX_TIMESTAMP() where Account = '" 
              << uid
              << "'";    

    int ret = ptmpconn->Execute(streamsql.str()) >= 0 ? 0 : -1;
    if (-1 == ret)
    {
        AK_LOG_WARN << "UpdateDataVersion failed, Account = " << uid;
        ReleaseDBConn(conn);
        return ret;
    }

    ReleaseDBConn(conn);
    return 0;
}

int ProjectUserManage::UpdateAccountDataVersionByUUID(const std::string &uuid)
{
    GET_DB_CONN_ERR_RETURN(conn, -1)

    std::stringstream streamsql;
    streamsql << "update PersonalAccount set Version=UNIX_TIMESTAMP() where UUID = '" 
              << uuid
              << "'";    

    int ret = conn.get()->Execute(streamsql.str()) >= 0 ? 0 : -1;
    if (-1 == ret)
    {
        AK_LOG_WARN << "UpdateDataVersion failed, uuid = " << uuid;
        return ret;
    }

    return 0;
}


int ProjectUserManage::UpdateAccountDataVersionByUnitMac(int unit_id, const std::string &mac)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    //默认权限组和设备关联的权限组
    std::stringstream streamsql;
    streamsql << "select ID From AccessGroup where UnitID= " 
              << unit_id
              << " union select AccessGroupID AS ID From AccessGroupDevice where MAC='"
              << mac
              << "'";    

    CRldbQuery query(ptmpconn);
    query.Query(streamsql.str());
    
    int ret = 0;
    std::set<int> ag_ids;
    while (query.MoveToNextRow())
    {
        ag_ids.insert(ATOI(query.GetRowData(0)));
    }
    ReleaseDBConn(conn);

    for (auto &ag_id : ag_ids)
    {
        UpdateDataVersionByAccessGroupID(ag_id);
    }
    
    return ret;
}

int ProjectUserManage::UpdateDataVersionByPubMac(int mng_id, const std::string &mac)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::stringstream streamsql;
    streamsql << "select ID From AccessGroup where UnitID != 0 and CommunityID= " 
              << mng_id
              << " union select AccessGroupID AS ID From AccessGroupDevice where MAC='"
              << mac
              << "'";    

    CRldbQuery query(ptmpconn);
    query.Query(streamsql.str());

    int ret = 0;
    std::set<int> ag_ids;
    while (query.MoveToNextRow())
    {
        ag_ids.insert(ATOI(query.GetRowData(0)));
    }
    ReleaseDBConn(conn);

    for (auto &ag_id : ag_ids)
    {
        UpdateDataVersionByAccessGroupID(ag_id);
    }
    return ret;
}

int ProjectUserManage::UpdateDataVersionByNode(const std::string &node)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    //更新主账号version
    std::stringstream streamsql1;
    streamsql1 << "update PersonalAccount set Version=UNIX_TIMESTAMP() where Account = '" 
          << node << "'";
    int nret = ptmpconn->Execute(streamsql1.str()) >= 0 ? 0 : -1;
    if (-1 == nret)
    {
        AK_LOG_WARN << "updateDataVersionByPerMac failed, node = " << node;
        ReleaseDBConn(conn);
        return nret;
    }

    //获取从账号
    std::stringstream streamsql;
    streamsql << "select Account From PersonalAccount where ParentID=(select ID from PersonalAccount where Account= '" 
              << node
              << "') and Role=" << ACCOUNT_ROLE_COMMUNITY_ATTENDANT;

    CRldbQuery query(ptmpconn);
    query.Query(streamsql.str());
    std::string account;
    while (query.MoveToNextRow())
    {
        //更新从账号version
        account = query.GetRowData(0);
        std::stringstream streamsql2;
        streamsql2 << "update PersonalAccount set Version=UNIX_TIMESTAMP() where Account = '" 
              << account << "'";
        nret = ptmpconn->Execute(streamsql2.str()) >= 0 ? 0 : -1;
        if (-1 == nret)
        {
            AK_LOG_WARN << "updateDataVersionByPerMac failed, attendant account = " << account;
            ReleaseDBConn(conn);
            return nret;
        }
    }

    ReleaseDBConn(conn);
    return 0;
}

int ProjectUserManage::UpdataDataVersionByAccount(const std::string &account)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    //更新主账号version
    std::stringstream streamsql1;
    streamsql1 << "update PersonalAccount set Version=UNIX_TIMESTAMP() where Account = '" 
          << account << "'";
    int nret = ptmpconn->Execute(streamsql1.str()) >= 0 ? 0 : -1;
    if (-1 == nret)
    {
        AK_LOG_WARN << "updateDataVersionByPerMac failed, account = " << account;    
    }

    ReleaseDBConn(conn);
    return nret;
}

int ProjectUserManage::UpdateDataVersionByAccessGroupID(int ag_id)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(ptmpconn);

    std::string staffs, deliverys, personals;
    std::stringstream sql1, sql2, sql3;
    int ret1 = 0;
    int ret2 = 0;
    int ret3 = 0;

    sql1 << "select GROUP_CONCAT(StaffID) from StaffAccess where AccessGroupID = " << ag_id;
    query.Query(sql1.str());
    if (query.MoveToNextRow())
    {
        staffs = query.GetRowData(0);
        if(staffs.size() > 0)
        {
            std::stringstream streamsql;
            streamsql << "update Staff set Version=UNIX_TIMESTAMP() where ID in (" 
                       << staffs << ")";
            ret1 = ptmpconn->Execute(streamsql.str()) >= 0 ? 0 : -1;
        }
    }

        
    sql2 << "select GROUP_CONCAT(DeliveryID) from DeliveryAccess where AccessGroupID = " << ag_id;
    query.Query(sql2.str());
    if (query.MoveToNextRow())
    {
        deliverys = query.GetRowData(0);
        if(deliverys.size() > 0)
        {
            std::stringstream streamsql;
            streamsql << "update Delivery set Version=UNIX_TIMESTAMP() where ID in (" 
                      << deliverys << ")";
            ret2 = ptmpconn->Execute(streamsql.str()) >= 0 ? 0 : -1;
        }
    }
        
    sql3 << "select Account from AccountAccess where AccessGroupID = " << ag_id;
    query.Query(sql3.str());
    std::vector<std::string> account_list;
    while (query.MoveToNextRow())
    {
        account_list.push_back(query.GetRowData(0));
    }
    personals = ListToSeparatedFormatString(account_list);   
    if(personals.size() > 0)
    {
        std::stringstream streamsql;
        streamsql << "update PersonalAccount set Version=UNIX_TIMESTAMP() where Account in (" 
                  << personals << ")";
        ret3 = ptmpconn->Execute(streamsql.str()) >= 0 ? 0 : -1;
    }

    if (-1 == ret1 || -1 == ret2 || -1 == ret3)
    {
        AK_LOG_WARN << "updateDataVersionByAccessGroupID failed, ag_id = " << ag_id;
        ReleaseDBConn(conn);
        return -1;
    }

    ReleaseDBConn(conn);
    return 0;
    
}

// 个人权限组
int ProjectUserManage::UpdateDataVersionByUserAccessGroupID(int ag_id)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    //更新主账号version
    std::stringstream stream_sql;
    stream_sql << "update PersonalAccount P left join UserAccessGroup U on P.Account = U.Account set Version=UNIX_TIMESTAMP() where U.ID = " << ag_id;
    int ret = tmp_conn->Execute(stream_sql.str()) >= 0 ? 0 : -1;
    if (-1 == ret)
    {
        AK_LOG_WARN << "UpdateDataVersionByUserAccessGroupID failed, ag_id = " << ag_id;
        ReleaseDBConn(conn);
        return ret;
    }
    
    ReleaseDBConn(conn);
    return 0;
}

int ProjectUserManage::UpdateDataVersionByStaffID(int staff_id)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(ptmpconn);

    int ret = 0;

    std::stringstream streamsql;
    streamsql << "update Staff set Version=UNIX_TIMESTAMP() where ID =" 
               << staff_id;
    ret = ptmpconn->Execute(streamsql.str()) >= 0 ? 0 : -1;        

    if (-1 == ret)
    {
        AK_LOG_WARN << "UpdateDataVersionByStaffID failed, staff_id = " << staff_id;
        ReleaseDBConn(conn);
        return -1;
    }

    ReleaseDBConn(conn);
    return 0;
    
}

int ProjectUserManage::UpdateDataVersionByStaffUUID(const std::string& staff_uuid)
{
    GET_DB_CONN_ERR_RETURN(conn, -1)

    CRldbQuery query(conn.get());

    int ret = 0;

    std::stringstream streamsql;
    streamsql << "update Staff set Version=UNIX_TIMESTAMP() where UUID = '" << staff_uuid << "'";
    ret = conn->Execute(streamsql.str()) >= 0 ? 0 : -1;        

    if (-1 == ret)
    {
        AK_LOG_WARN << "UpdateDataVersionByStaffUUID failed, staff_uuid = " << staff_uuid;
        return -1;
    }

    return 0;    
}

int ProjectUserManage::UpdateDataVersionByDeliveryID(int delivery_id)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(ptmpconn);

    int ret = 0;

    std::stringstream streamsql;
    streamsql << "update Delivery set Version=UNIX_TIMESTAMP() where ID =" 
               << delivery_id;
    ret = ptmpconn->Execute(streamsql.str()) >= 0 ? 0 : -1;        

    if (-1 == ret)
    {
        AK_LOG_WARN << "UpdateDataVersionByDeliveryID failed, delivery_id = " << delivery_id;
        ReleaseDBConn(conn);
        return -1;
    }

    ReleaseDBConn(conn);
    return 0;
    
}

int ProjectUserManage::UpdateDataVersionByDeliveryUUID(const std::string& delivery_uuid)
{
    GET_DB_CONN_ERR_RETURN(conn, -1)

    CRldbQuery query(conn.get());

    int ret = 0;

    std::stringstream streamsql;
    streamsql << "update Delivery set Version=UNIX_TIMESTAMP() where UUID = '" << delivery_uuid << "'";
    ret = conn->Execute(streamsql.str()) >= 0 ? 0 : -1;        

    if (-1 == ret)
    {
        AK_LOG_WARN << "UpdateDataVersionByDeliveryUUID failed, delivery_uuid = " << delivery_uuid;
        return -1;
    }

    return 0;    
}


std::string  ProjectUserManage::GetServerTag()
{
    static std::string server_tag;
    if(server_tag.length() > 0){
        return server_tag;
    }

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return "";
    }

    std::stringstream streamsql;
    streamsql << "select ServerTag from SystemSetting limit 1";
    CRldbQuery query(ptmpconn);
    query.Query(streamsql.str());
    if (query.MoveToNextRow())
    {
        server_tag = query.GetRowData(0);
    }

    ReleaseDBConn(conn);
    return server_tag;
}


int ProjectUserManage::GetUUID(const std::string &perfix, std::string &uuid)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::stringstream streamsql;
    std::string tmp_uuid;
    streamsql << "select uuid() as uuid";
    CRldbQuery query(ptmpconn);
    query.Query(streamsql.str());
    if (query.MoveToNextRow())
    {
        tmp_uuid = query.GetRowData(0);
        StringReplace(tmp_uuid, "-", "");
        uuid = perfix + tmp_uuid;
    }

    ReleaseDBConn(conn);
    return 0;
}

std::string ProjectUserManage::GetLogDeliveryUUIDByAccount(const std::string& account)
{   
    ResidentPerAccount personal_account;
    memset(&personal_account, 0, sizeof(personal_account));
    if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(account, personal_account))
    {
        std::string mng_uuid;
        std::string node_uuid;
        if (personal_account.role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT || personal_account.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT)
        {
            // 从账号,再查主账号
            ResidentPerAccount parent_account;
            memset(&parent_account, 0, sizeof(parent_account));
            if (0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(personal_account.parent_uuid, parent_account))
            {
                node_uuid = parent_account.uuid;
                mng_uuid = parent_account.parent_uuid;
            }
        }
        else
        {
            node_uuid = personal_account.uuid;
            mng_uuid = personal_account.parent_uuid;
        }
       
        // log_project_uuid
        if (personal_account.role == ACCOUNT_ROLE_PERSONNAL_MAIN || personal_account.role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT)
        {
            return node_uuid;
        }
        else
        {
            return mng_uuid;
        }
    }
    else
    {
        return "";
    }
}

int ProjectUserManage::GetDevLocation(const std::string &sip, std::string &location)
{
    ResidentDev dev;
    if (0 == dbinterface::ResidentDevices::GetSipDev(sip, dev))
    {
        location = dev.location;
    }
    else if (0 == dbinterface::ResidentPerDevices::GetSipDev(sip, dev))
    {
        location = dev.location;
    }
    return 0;
}

int ProjectUserManage::GetSipName(const std::string &sip, std::string &name)
{
    GetDevLocation(sip, name);
    if (name.size() == 0)
    {
        ResidentPerAccount account;
        memset(&account, 0, sizeof(account));
        if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(sip, account))
        {
            name = account.name;
        }
    }
    if (name.size() == 0)
    {
        name = sip;
    }
    return 0;
}

//获取当前登录站点的强提醒开关
void ProjectUserManage::GetCurrentLoginSiteAlarmReminderStatusByMainSite(const std::string& main_site, int& alarm_reminder_status, bool is_pm)
{
    UserInfoAccount account_user_info;
    PerAccountUserInfo per_account_user_info;
    std::string user_info_uuid = dbinterface::ResidentPersonalAccount::GetUserInfoUUIDByAccount(main_site);
    std::string current_site;
    if (is_pm)
    {
        if(dbinterface::AccountUserInfo::GetAccountUserInfoByUUID(user_info_uuid, account_user_info) != 0)
        {
            AK_LOG_WARN << "get account user info failed, account is: " << main_site;
            return;
        }
        current_site = GetCurrentSite(account_user_info.main_user_account, account_user_info.last_login_account);
    }
    else
    {
        if(dbinterface::PersonalAccountUserInfo::GetPerAccountInfoByUUID(user_info_uuid, per_account_user_info) != 0)
        {
            AK_LOG_WARN << "get per account user info failed, account is: " << main_site;
            return;
        }
        current_site = GetCurrentSite(per_account_user_info.main_user_account, per_account_user_info.last_login_account);
    }
    
    if(dbinterface::ResidentPersonalAccount::GetAlarmReminderStatusByAccount(current_site, alarm_reminder_status) != 0)
    {
        AK_LOG_WARN << "get alarm reminder status failed, site is: " << current_site;
        return;
    }

    return;
}

std::string ProjectUserManage::GetCurrentSite(const std::string& main_user_account, const std::string& last_login_user_account)
{
    std::string current_site = main_user_account;
    if (last_login_user_account.size() != 0)
    {
        current_site = last_login_user_account;
    }
    return current_site;
}


//判断是否为多套房用户
int ProjectUserManage::IsMultiSiteUser(const std::string& user_info_uuid)
{
    int ret = 0;
    std::stringstream stream_sql;
    stream_sql << "select count(*) > 1 as isMultiSiteUser from PersonalAccount where UserInfoUUID = '" << user_info_uuid << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return 0;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        ret = ATOI(query.GetRowData(0));
    }

    ReleaseDBConn(conn);
    return ret;
}

//判断pm app是否为多套房用户
int ProjectUserManage::IsPmAppMultiSiteUser(const std::string& user_info_uuid)
{
    ResidentPerAccountList account_list;
    if (0 != dbinterface::ResidentPersonalAccount::GetAccountListByUserInfoUUID(user_info_uuid, account_list))
    {
        AK_LOG_INFO << "GetAccountListByUserInfoUUID failed, userinfo_uuid:" << user_info_uuid;
        return 0;
    }
    
    int active_pm_counts = 0;
    for (const auto &account : account_list)
    {
        if (dbinterface::PmAccountMap::checkPMAppAccountStatus(account.uuid))
        {
            active_pm_counts ++;
        }
    }

    return (active_pm_counts > 1) ? 1 :0;
}

//判断pm app和enduser是否为多套房的接口
int ProjectUserManage::IsAccountMultiSite(const std::string& uid)
{
    ResidentPerAccount per_site;
    if (0 != dbinterface::ResidentPersonalAccount::GetUidAccount(uid, per_site))
    {
        AK_LOG_WARN << "Get site failed";
        return 0;
    }

    int is_multi_site = 0;
    if (per_site.role == ACCOUNT_ROLE_COMMUNITY_PM)
    {
        is_multi_site = IsPmAppMultiSiteUser(per_site.user_info_uuid);
    }
    else 
    {
        is_multi_site = IsMultiSiteUser(per_site.user_info_uuid);
    }

    return is_multi_site;
}


//判断站点推送是否进行多套房拦截，一人多套房账号需考虑室内机方案和从账号数量控制
//return  true:代表需拦截 false:代表无需拦截
bool ProjectUserManage::MultiSiteLimit(const std::string& site)
{
    ResidentPerAccount account;
    ResidentPerAccount node;
    if(dbinterface::ResidentPersonalAccount::GetUidAccount(site, account) != 0)
    {
        //账号不存在
       return true; 
    }

    if(0 == IsMultiSiteUser(account.user_info_uuid))
    {
        //非多套房，无需拦截功能
        return false;
    }

    if(!account.active)
    {
        AK_LOG_INFO << "MultiSiteLimit, because site inactive, account:" << account.account;
        return true;
    }

    if(account.is_expire)
    {
        AK_LOG_INFO << "MultiSiteLimit, because site expire, account:" << account.account;
        return true;
    }

    if(IsOfficeRole(account.role)|| account.role == ACCOUNT_ROLE_COMMUNITY_PM)
    {
        //这些角色暂时无下面其他拦截的业务
        return false;
    }
    
    if(account.role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT || account.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT)
    {
        dbinterface::ResidentPersonalAccount::GetUUIDAccount(account.parent_uuid, node);
    }
    else
    {
        //主账号node即account
        memcpy(&node, &account, sizeof(ResidentPerAccount));
    }

    if(node.role == ACCOUNT_ROLE_PERSONNAL_MAIN) 
    {
        //室内机方案校验
        //校验不通过 直接拦截
        if(!dbinterface::ResidentPerDevices::CheckIndoorPlan(node.account))
        {
            AK_LOG_INFO << "MultiSiteLimit, because indoor plan, account:" << account.account << ",node:" << node.account;
            return true;
        }
    }
    else if(node.role == ACCOUNT_ROLE_COMMUNITY_MAIN)
    {
        //室内机方案校验
        //校验不通过 直接拦截
        if(!dbinterface::ResidentDevices::CheckIndoorPlan(node.account))
        {   
            AK_LOG_INFO << "MultiSiteLimit, because indoor plan, account:" << account.account << ",node:" << node.account;
            return true;
        }

        if(account.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT)
        {
            //从账号数量控制
            CommunityInfoPtr comm_info = std::make_shared<CommunityInfo>(node.parent_id);
            //检查高级功能
            if (comm_info->CheckFeature(CommunityInfo::FeaturePlan::TAB_DEVICES_CHECK_INDEX_FAMILYMEMBER) 
                && !comm_info->IsExpire()) 
            {
                if(!dbinterface::PersonalAccountCnf::CheckFamilyMemberControl(node.account, account.account))
                {
                    AK_LOG_INFO << "MultiSiteLimit, because family member control, sip:" << account.account << ",node:" << node.account;
                    return true;
                }
            }
        }
    }


    return false;

}


int ProjectUserManage::UpdateNfcCodeByAccount(const std::string &account, const std::string &nfccode)
{
    GET_DB_CONN_ERR_RETURN(conn, -1);
    CRldb* tmp_conn = conn.get();

    std::stringstream sql;
    sql << "update PersonalAccount set NFCCode= '" << nfccode << "' where Account = '" << account << "'";  

    int ret = tmp_conn->Execute(sql.str()) >= 0 ? 0 : -1;
    if (-1 == ret)
    {
        AK_LOG_WARN << "Update PersonalAccount NFCCode failed, NFCCode = " << nfccode << " Account = " << account;
    }
    return 0;
}

int ProjectUserManage::UpdateBleCodeByAccount(const std::string &account, const std::string &blecode)
{
    GET_DB_CONN_ERR_RETURN(conn, -1);
    CRldb* tmp_conn = conn.get();
   
    std::stringstream sql;
    sql << "update PersonalAccount set BLECode= '" << blecode << "' where Account = '" << account << "'";

    int ret = tmp_conn->Execute(sql.str()) >= 0 ? 0 : -1;
    if (-1 == ret)
    {
        AK_LOG_WARN << "Update PersonalAccount BLECode failed, BLECode = " << blecode << " Account = " << account;
    }
    return 0;
}

int ProjectUserManage::GetRoleByAccount(const std::string& account, int& role)
{
    ResidentPerAccount per_account;
    if (0 != dbinterface::ResidentPersonalAccount::GetUidAccount(account, per_account))
    {
        return -1;
    }
    role = per_account.role;
    return 0;
}




}

