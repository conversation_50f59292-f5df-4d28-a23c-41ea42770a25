#ifndef __PROJECT_USER_MANAGE_H__
#define __PROJECT_USER_MANAGE_H__
#include <string>
#include <memory>
#include <vector>
#include "AkcsCommonDef.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "DclientMsgSt.h"
#include "AkcsCommonSt.h"
#include "util.h"


typedef struct CommonAccount_T
{
    uint32_t id;
    char account[32];
    int role;
    char language[16];
    int project_type;
    char mobile_number[32];
    char phone_code[8];
    char name[128];

    char parent_uuid[64];
    char uuid[64];
    uint32_t parent_id;
}CommonAccount;


typedef struct SipInfo_T {
    char sip[16];
    char node[32];
    int project_type;
    int sip_type;
    int grade;//同devices表
    char sipgroup[16];
    int role;
    char dev_node[16];//住户下设备的node
    char parent_uuid[64];//账号的父uuid
    char uuid[64];//账号的uuid

    SipInfo_T() {
        memset(this, 0, sizeof(*this));
    }
}SipInfo;


namespace dbinterface
{

class ProjectUserManage
{
public:
    ProjectUserManage();
    ~ProjectUserManage();
    static int GetUserTypeBySip(const std::string &sip);
    static int CheckUserType(const std::string &struser);
    static int UpdateAccountDataVersionByUid(const std::string &uid);
    static int UpdateAccountDataVersionByUUID(const std::string &uuid);
    static int UpdateCommunityAllAccountDataVersion(int mng_id);
    static int UpdateOfficeAllAccountDataVersion(int mng_id);
    static int UpdateAccountDataVersionByUnitMac(int unit_id, const std::string &mac);
    static int UpdateDataVersionByPubMac(int mng_id, const std::string &mac);
    static int UpdateDataVersionByNode(const std::string &node);
    static int UpdataDataVersionByAccount(const std::string &node);
    static int UpdateDataVersionByAccessGroupID(int ag_id);
    static int UpdateDataVersionByUserAccessGroupID(int ag_id);
    static int GetUUID(const std::string &perfix, std::string &uuid);
    static int GetCurrentTimeStamp();
    static int GetSipInfoBySip(const std::string &sip, SipInfo &info);
    static std::string GetLogDeliveryUUIDByAccount(const std::string& account);
    static std::string GetServerTag();
    
    static int UpdateDataVersionByStaffID(int staff_id);    
    static int UpdateDataVersionByStaffUUID(const std::string& staff_uuid);
    static int UpdateDataVersionByDeliveryID(int delivery_id);
    static int UpdateDataVersionByDeliveryUUID(const std::string& delivery_uuid);
    static void GetCurrentLoginSiteAlarmReminderStatusByMainSite(const std::string& main_site, int& alarm_reminder_status, bool is_pm = false);

    static int GetDevLocation(const std::string &sip, std::string &location);
    static int GetSipName(const std::string &sip, std::string &name);

    static bool MultiSiteLimit(const std::string& site);
    static int IsMultiSiteUser(const std::string& user_info_uuid);
    //判断pm app是否为多套房用户
    static int IsPmAppMultiSiteUser(const std::string& user_info_uuid);
    //判断pm app和enduser是否为多套房的接口
    static int IsAccountMultiSite(const std::string& uid);
    static int UpdateNfcCodeByAccount(const std::string &account, const std::string &nfccode);
    static int UpdateBleCodeByAccount(const std::string &account, const std::string &blecode);
    static int GetRoleByAccount(const std::string& account, int& role);

	
private:
    static std::string GetCurrentSite(const std::string& main_user_account, const std::string& last_login_user_account);
};



}
#endif
