#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include <string.h>
#include "AkLogging.h"
#include "dbinterface/PropertyInfo.h"

namespace dbinterface
{

PropertyInfo::PropertyInfo()
{

}

int PropertyInfo::GetFullNameByUUID(const std::string &uuid, std::string &fullname)
{
    std::stringstream sql;
    sql << "select concat(P.FirstName,' ',P.LastName) " 
        << "from PropertyInfo P join Account A on P.AccountID = A.ID where A.UUID = '"
        << uuid
        << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        fullname = query.GetRowData(0);
    }
        
    ReleaseDBConn(conn);
    return 0;    
}


}

