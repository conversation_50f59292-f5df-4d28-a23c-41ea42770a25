#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "dbinterface/PropertyMngList.h"

namespace dbinterface {

static const std::string property_mng_list_info_sec = " PropertyID,CommunityID,EnableDeleteAccount,EnableShowLog ";

void PropertyMngList::GetPropertyMngListFromSql(PropertyMngListInfo& property_mng_list_info, CRldbQuery& query)
{
    property_mng_list_info.property_id = ATOI(query.GetRowData(0));
    property_mng_list_info.community_id = ATOI(query.GetRowData(1));
    property_mng_list_info.enable_delete_account = ATOI(query.GetRowData(2));
    property_mng_list_info.enable_show_log = ATOI(query.GetRowData(3));
    return;
}

int PropertyMngList::GetPropertyMngListByCommunityID(int community_id, std::vector<PropertyMngListInfo>& property_mng_list_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << property_mng_list_info_sec << " from PropertyMngList where CommunityID = '" << community_id << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        PropertyMngListInfo property_mng_info;
        GetPropertyMngListFromSql(property_mng_info, query);
        property_mng_list_info.push_back(property_mng_info);
    }
    return 0;
}


}