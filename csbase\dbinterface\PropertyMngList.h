#ifndef __DB_PROPERTY_MNG_LIST_H__
#define __DB_PROPERTY_MNG_LIST_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct PropertyMngListInfo_T
{
    int property_id;
    int community_id;
    int enable_delete_account;
    int enable_show_log;
    PropertyMngListInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} PropertyMngListInfo;

namespace dbinterface {

class PropertyMngList
{
public:
    static int GetPropertyMngListByCommunityID(int community_id, std::vector<PropertyMngListInfo>& property_mng_list_info);

private:
    PropertyMngList() = delete;
    ~PropertyMngList() = delete;
    static void GetPropertyMngListFromSql(PropertyMngListInfo& property_mng_list_info, CRldbQuery& query);
};

}
#endif