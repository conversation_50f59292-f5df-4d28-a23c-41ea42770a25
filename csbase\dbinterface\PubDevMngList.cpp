#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include <string.h>
#include "AkLogging.h"
#include "PubDevMngList.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "ConnectionManager.h"

namespace dbinterface
{

PubDevMngList::PubDevMngList()
{

}

int PubDevMngList::IsManageBuilding(int dev_id, int unit_id)
{
    int ret = 0;
    std::stringstream sql;
    sql << "SELECT ID FROM PubDevMngList WHERE DevicesID = "
         << dev_id
         << " AND UnitID = "
         << unit_id;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return ret;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        ret = 1;
    }


    ReleaseDBConn(conn);
    return ret;
}

int PubDevMngList::IsManageBuildingByMac(const std::string& mac, int unit_id)
{
    ResidentDev dev;
    if (0 == dbinterface::ResidentDevices::GetMacDev(mac, dev))
    {
        // 先判断是否全管理
        if (dbinterface::SwitchHandle(dev.flags, DeviceSwitch::DEV_MNG_ALL))
        {
            return 1;
        }
        
        // 再判断是否具体管理某栋
        if (dbinterface::PubDevMngList::IsManageBuilding(dev.id, unit_id))
        {
            return 1;
        }
    }
    return 0;
}

// 查询具体的楼栋
int PubDevMngList::GetManagementBuildingListById(int dev_id, std::vector<int> & unit_id_list)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::stringstream stream_sql;
    stream_sql << "select UnitID from PubDevMngList where DevicesID = " << dev_id;
     
    CRldbQuery query(tmp_conn);
    query.Query(stream_sql.str());
    while(query.MoveToNextRow())
    {
        unit_id_list.push_back(ATOI(query.GetRowData(0)));   
    }
    
    ReleaseDBConn(conn); 
    return 0;
}


int PubDevMngList::GetManagementBuildingListByProjectId(uint32_t mng_id, ManagementBuildingMap &dev_mng_unit_list)
{
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    std::stringstream stream_sql;
    stream_sql << "select  P.DevicesID,P.UnitID  From PubDevMngList P left join Devices D on D.ID=P.DevicesID where D.MngAccountID=" << mng_id;
     
    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while(query.MoveToNextRow())
    {
        dev_mng_unit_list.insert(std::make_pair(ATOI(query.GetRowData(0)), ATOI(query.GetRowData(1))));   
    }
    return 0;
}





}

