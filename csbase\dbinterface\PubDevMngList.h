#ifndef __DB_PUB_DEV_MNG_LIST_H__
#define __DB_PUB_DEV_MNG_LIST_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include <map>




using ManagementBuildingMap = std::multimap<uint32_t/*dev_id*/, uint32_t/*unit_id*/>;

namespace dbinterface
{

class PubDevMngList
{
public:
    PubDevMngList();
    ~PubDevMngList();
    static int IsManageBuilding(int dev_id, int unit_id);
    static int IsManageBuildingByMac(const std::string& mac, int unit_id);
    static int GetManagementBuildingListById(int dev_id, std::vector<int>& unit_id_list);
    static int GetManagementBuildingListByProjectId(uint32_t mng_id, ManagementBuildingMap &dev_mng_unit_list);

private: 
};


}

#endif

