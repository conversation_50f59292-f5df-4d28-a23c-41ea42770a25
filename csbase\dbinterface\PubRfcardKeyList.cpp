#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include <string.h>
#include "AkLogging.h"
#include "PubRfcardKeyList.h"

namespace dbinterface
{

PubRfcardKeyList::PubRfcardKeyList()
{

}

int PubRfcardKeyList::GetMacByCodeID(int code_id, std::vector<std::string> &macs)
{
    std::stringstream sql;
    sql << "select MAC " 
        << "from PubRfcardKeyList where KeyID = "
        << code_id;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        std::string mac = query.GetRowData(0);
        macs.push_back(mac);
    }


    ReleaseDBConn(conn);
    return 0;
}

}

