#include "util.h"
#include <cstring>
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "dbinterface/PushButtonNotify.h"
#include "json/json.h"
#include "dbinterface/Account.h"
#include "dbinterface/CommunityUnit.h"
#include <map>
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/CommunityRoom.h"
#include "dbinterface/UUID.h"
#include "dbinterface/SystemSettingTable.h"


namespace dbinterface {


int PushButtonNotify::InsertPushButtonNotify(const ResidentDev& dev, std::string& uuid)
{
    dbinterface::UUID::GenerateUUID(dbinterface::SystemSetting::GetServerTag(), uuid);
    //插入数据构造
    std::map<std::string, std::string>str_map;
    str_map.emplace("UUID", uuid);
    str_map.emplace("Content", "EXPANSION_UNIT");
    str_map.emplace("DeviceUUID", dev.uuid);
    
    if(dev.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT || dev.grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
    {
        str_map.emplace("CommunityUnitUUID", dev.unit_uuid);
        if(dev.grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
        {
            ResidentPerAccount account;
            dbinterface::ResidentPersonalAccount::GetUidAccount(dev.node, account);
            CommunityRoomInfo info;
            dbinterface::CommunityRoom::GetCommunityRoomByID(account.room_id, info);
            str_map.emplace("CommunityRoomUUID", info.uuid);
        }
    }
    std::map<std::string, int>int_map;
    if(dev.project_type == project::RESIDENCE)
    {
        int_map.emplace("ProjectType", NotifyProjectType::NOTIFY_COMMUNITY);
    }
    else if(dev.project_type == project::PERSONAL)
    {
        int_map.emplace("ProjectType", NotifyProjectType::NOTIFY_SINGAL_FAMILY);
    }
    else
    {
        int_map.emplace("ProjectType", NotifyProjectType::NOTIFY_OFFICE);
    }
    std::string table_name = "PushButtonNotify";
    
    GET_DB_CONN_ERR_RETURN(tmp_conn, -1);
    CRldb* conn = tmp_conn.get();
    int ret = conn->InsertData(table_name, str_map, int_map);
    return ret;
}

}
