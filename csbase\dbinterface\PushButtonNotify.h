#ifndef __DB_PUSH_BUTTON_NOTIFY_H__
#define __DB_PUSH_BUTTON_NOTIFY_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"
#include "json/json.h"
#include "dbinterface/resident/ResidentDevices.h"

enum NotifyProjectType
{
        NOTIFY_SINGAL_FAMILY = 1,       //单住户
        NOTIFY_COMMUNITY = 2,       //社区
        NOTIFY_OFFICE = 3,          //办公
};

namespace dbinterface {

class PushButtonNotify
{
public:
    static int InsertPushButtonNotify(const ResidentDev& dev, std::string& uuid);

private:
    PushButtonNotify() = delete;
    ~PushButtonNotify() = delete;
};

}
#endif