#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "dbinterface/SL20Lock.h"
#include "AkcsPasswdConfuse.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"

namespace dbinterface {

static const std::string sl20_lock_info_sec = " UUID,Name,MAC,WifiStatus,KeepAlive,SecretKey,LastConnectedTime,DeviceUUID,Relay,\
    AutoLockEnable,AutoLockDelay,PinCode,IsPinCodeSynchronizing,BatteryLevel,ModuleVersion,LockBodyVersion,InstallerUUID,\
    ProjectType,AccountUUID,CommunityUnitUUID,PersonalAccountUUID,MqttPwd,CombinedVersion ";

void SL20Lock::GetSL20LockFromSql(SL20LockInfo& sl20_lock_info, CRldbQuery& query)
{
    Snprintf(sl20_lock_info.uuid, sizeof(sl20_lock_info.uuid), query.GetRowData(0));
    Snprintf(sl20_lock_info.name, sizeof(sl20_lock_info.name), query.GetRowData(1));
    Snprintf(sl20_lock_info.mac, sizeof(sl20_lock_info.mac), query.GetRowData(2));
    sl20_lock_info.wifi_status = ATOI(query.GetRowData(3));
    sl20_lock_info.keep_alive = ATOI(query.GetRowData(4));
    Snprintf(sl20_lock_info.secret_key, sizeof(sl20_lock_info.secret_key), query.GetRowData(5));
    Snprintf(sl20_lock_info.last_connected_time, sizeof(sl20_lock_info.last_connected_time), query.GetRowData(6));
    Snprintf(sl20_lock_info.device_uuid, sizeof(sl20_lock_info.device_uuid), query.GetRowData(7));
    sl20_lock_info.relay = ATOI(query.GetRowData(8));
    sl20_lock_info.auto_lock_enable = ATOI(query.GetRowData(9));
    sl20_lock_info.auto_lock_delay = ATOI(query.GetRowData(10));
    Snprintf(sl20_lock_info.pin_code, sizeof(sl20_lock_info.pin_code), query.GetRowData(11));
    sl20_lock_info.is_pin_code_synchronizing = ATOI(query.GetRowData(12));
    sl20_lock_info.battery_level = ATOI(query.GetRowData(13));
    Snprintf(sl20_lock_info.module_version, sizeof(sl20_lock_info.module_version), query.GetRowData(14));
    Snprintf(sl20_lock_info.lock_body_version, sizeof(sl20_lock_info.lock_body_version), query.GetRowData(15));
    Snprintf(sl20_lock_info.installer_uuid, sizeof(sl20_lock_info.installer_uuid), query.GetRowData(16));
    sl20_lock_info.project_type = ATOI(query.GetRowData(17));
    Snprintf(sl20_lock_info.account_uuid, sizeof(sl20_lock_info.account_uuid), query.GetRowData(18));
    Snprintf(sl20_lock_info.community_unit_uuid, sizeof(sl20_lock_info.community_unit_uuid), query.GetRowData(19));
    Snprintf(sl20_lock_info.personal_account_uuid, sizeof(sl20_lock_info.personal_account_uuid), query.GetRowData(20));
    Snprintf(sl20_lock_info.mqtt_pwd, sizeof(sl20_lock_info.mqtt_pwd), query.GetRowData(21));
    Snprintf(sl20_lock_info.combined_version, sizeof(sl20_lock_info.combined_version), query.GetRowData(22));
    
    std::string srcpwd = sl20_lock_info.mqtt_pwd;
    PasswdDecode(srcpwd.c_str(), srcpwd.size(), sl20_lock_info.mqtt_pwd, sizeof(sl20_lock_info.mqtt_pwd));
    return;
}

int SL20Lock::GetSL20LockInfoByUUID(const std::string& uuid, SL20LockInfo& sl20_lock_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << sl20_lock_info_sec << " from SL20Lock where UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetSL20LockFromSql(sl20_lock_info, query);
    }
    else
    {
        AK_LOG_WARN << "get SL20LockInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}

// 一台设备多个relay,每个relay都可以绑定一把锁
int SL20Lock::GetSL20LockListByDeviceUUID(const std::string& device_uuid, SL20LockInfoList& sl20_lock_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << sl20_lock_info_sec <<" from SL20Lock where DeviceUUID = '" << device_uuid << "'";

    GET_DB_CONN_ERR_RETURN(conn, -1);
    
    CRldbQuery query(conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        SL20LockInfo sl20_lock;
        GetSL20LockFromSql(sl20_lock, query);
        sl20_lock_list.push_back(sl20_lock);
    }
    
    return 0;
}

int SL20Lock::GetSL20LockInfoListByPersonalAccountUUID(const std::string& per_uuid, SL20LockInfoList& sl20_lock_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << sl20_lock_info_sec <<" from SL20Lock where PersonalAccountUUID = '" << per_uuid << "'";

    GET_DB_CONN_ERR_RETURN(conn, -1);
    
    CRldbQuery query(conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        SL20LockInfo sl20_lock;
        GetSL20LockFromSql(sl20_lock, query);
        sl20_lock_list.push_back(sl20_lock);
    }
    
    return 0;
}

int SL20Lock::GetSL20LockInfoByMac(const std::string& mac, SL20LockInfo& sl20_lock_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << sl20_lock_info_sec << " from SL20Lock where MAC = '" << mac << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetSL20LockFromSql(sl20_lock_info, query);
    }
    else
    {
        AK_LOG_WARN << "get SL20LockInfo by MAC failed, MAC = " << mac;
        return -1;
    }
    return 0;
}


int SL20Lock::UpdateSL20LockRelatedInfo(const SL20LockInfo& sl20_lock_info,  bool pincode_already_sync)
{
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    int is_pincode_synchronizing = pincode_already_sync ? 0 : 1;

    char sql[1024] = "";
    ::snprintf(sql, sizeof(sql), "update SL20Lock set LastConnectedTime=now(), ModuleVersion='%s', LockBodyVersion='%s', CombinedVersion='%s', IsPinCodeSynchronizing=%d where UUID='%s'",
        sl20_lock_info.module_version, sl20_lock_info.lock_body_version, sl20_lock_info.combined_version, is_pincode_synchronizing, sl20_lock_info.uuid
    );

    int ret = db_conn->Execute(sql) >= 0 ? 0 : -1;
    return ret;
}

int SL20Lock::UpdateBatteryLevel(const std::string& lock_uuid, int battery_level)
{
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    char sql[1024] = "";
    ::snprintf(sql, sizeof(sql), "update SL20Lock set BatteryLevel=%d where UUID='%s'",
        battery_level, lock_uuid.c_str()
    );

    int ret = db_conn->Execute(sql) >= 0 ? 0 : -1;
    return ret;
}

void SL20Lock::GetSL20LockUUIDListByNode(const std::string& node, std::set<std::string>& sl20_lock_uuid_list)
{
    std::string uuid;
    if (0 != dbinterface::ResidentPersonalAccount::GetUUIDByAccount(node, uuid))
    {
        AK_LOG_WARN << "node info not found. node=" << node;
        return;
    }

    SL20LockInfoList sl20_lock_list;
    if (0 != dbinterface::SL20Lock::GetSL20LockInfoListByPersonalAccountUUID(uuid, sl20_lock_list))
    {
        AK_LOG_WARN << "get SL20LockInfoListByPersonalAccountUUID failed. node=" << node;
        return;
    }

    for (const auto& sl20_lock : sl20_lock_list)
    {
        sl20_lock_uuid_list.insert(sl20_lock.uuid);
    }

    return;
}


int SL20Lock::GetSL20LockInfoListByAccountUUID(const std::string& account_uuid, SL20LockInfoList& sl20_lock_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << sl20_lock_info_sec << " from SL20Lock where AccountUUID = '" << account_uuid << "'";
    
        GET_DB_CONN_ERR_RETURN(conn, -1);
    
    CRldbQuery query(conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        SL20LockInfo sl20_lock;
        GetSL20LockFromSql(sl20_lock, query);
        sl20_lock_list.push_back(sl20_lock);
    }
    
    return 0;
}

void SL20Lock::UpdateNodeSL20LockStatusSynchronizing(const std::string& node_uuid)
{
    GET_DB_CONN_ERR_RETURN_VOID(db_conn);

    // 1: 未同步，0: 已同步
    int is_pincode_synchronizing = 1;

    // 只更新未保活的锁
    char sql[1024] = "";
    ::snprintf(sql, sizeof(sql), "update SL20Lock set IsPinCodeSynchronizing=%d where PersonalAccountUUID='%s' and KeepAlive=0",
        is_pincode_synchronizing, node_uuid.c_str()
    );

    db_conn->Execute(sql);
}

void SL20Lock::UpdateProjectSL20LockStatusSynchronizing(const std::string& project_uuid)
{
    GET_DB_CONN_ERR_RETURN_VOID(db_conn);

    // 1: 未同步，0: 已同步
    int is_pincode_synchronizing = 1;

    char sql[1024] = "";
    ::snprintf(sql, sizeof(sql), "update SL20Lock set IsPinCodeSynchronizing=%d where AccountUUID='%s' and KeepAlive=0",
        is_pincode_synchronizing, project_uuid.c_str()
    );

    db_conn->Execute(sql);
}

void SL20Lock::UpdateOfflineCodeUsed(const std::string& lock_uuid, const std::string& note)
{
    GET_DB_CONN_ERR_RETURN_VOID(db_conn);

    char sql[1024] = "";
    ::snprintf(sql, sizeof(sql), "update SL20Lock set OfflineCodeLastGenerateTime = '0000-00-00 00:00:00' where UUID='%s' and OfflineCode='%s'", lock_uuid.c_str(), note.c_str());
    db_conn->Execute(sql);
}

}
