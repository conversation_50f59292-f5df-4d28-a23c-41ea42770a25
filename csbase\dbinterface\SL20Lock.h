#ifndef __DB_S_L20_LOCK_H__
#define __DB_S_L20_LOCK_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct SL20LockInfo_T
{
    char uuid[64];
    char name[64];
    char mac[16];
    int wifi_status;
    int keep_alive; //锁是否开启保活
    char secret_key[36]; // 用于生成锁离线密码的密钥
    char last_connected_time[32];
    char device_uuid[36];
    int relay;
    int auto_lock_enable;
    int auto_lock_delay;
    char pin_code[8];
    int is_pin_code_synchronizing;
    int battery_level;
    char module_version[16];
    char lock_body_version[16];
    char combined_version[16];
    char installer_uuid[36];
    int project_type;
    char account_uuid[36];
    char community_unit_uuid[36];
    char personal_account_uuid[36];
    char mqtt_pwd[36];
    SL20LockInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} SL20LockInfo;
    
typedef std::vector<SL20LockInfo> SL20LockInfoList;

namespace dbinterface {

class SL20Lock
{
public:
    static int GetSL20LockListByDeviceUUID(const std::string& device_uuid, SL20LockInfoList& sl20_lock_list);
    static int GetSL20LockInfoByUUID(const std::string& uuid, SL20LockInfo& sl20_lock_info);
    static int GetSL20LockInfoListByPersonalAccountUUID(const std::string& per_uuid, SL20LockInfoList& sl20_lock_list);
    static int GetSL20LockInfoListByAccountUUID(const std::string& account_uuid, SL20LockInfoList& sl20_lock_list);
    static int UpdateSL20LockRelatedInfo(const SL20LockInfo& sl20_lock_info,  bool pincode_already_sync);
    static int UpdateBatteryLevel(const std::string& lock_uuid, int battery_level);
    static void UpdateNodeSL20LockStatusSynchronizing(const std::string& node_uuid);
    static void UpdateProjectSL20LockStatusSynchronizing(const std::string& project_uuid);
    static int GetSL20LockInfoByMac(const std::string& mac, SL20LockInfo& sl20_lock_info);
    static void GetSL20LockUUIDListByNode(const std::string& node, std::set<std::string>& sl20_lock_uuid_list);
    static void UpdateOfflineCodeUsed(const std::string& lock_uuid, const std::string& note);
private:
    SL20Lock() = delete;
    ~SL20Lock() = delete;
    static void GetSL20LockFromSql(SL20LockInfo& sl20_lock_info, CRldbQuery& query);
};

}
#endif
