#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "SL20Shadow.h"
#include <string.h>
#include "dbinterface/SystemSettingTable.h"
#include "dbinterface/UUID.h"

namespace dbinterface {

static const std::string sl20_shadow_info_sec = " UUID,SL20LockUUID,ConfigurationHash,Configuration ";

void SL20Shadow::GetSL20ShadowFromSql(SL20ShadowInfo& sl20_shadow_info, CRldbQuery& query)
{
    Snprintf(sl20_shadow_info.uuid, sizeof(sl20_shadow_info.uuid), query.GetRowData(0));
    Snprintf(sl20_shadow_info.sl20_lock_uuid, sizeof(sl20_shadow_info.sl20_lock_uuid), query.GetRowData(1));
    Snprintf(sl20_shadow_info.configuration_hash, sizeof(sl20_shadow_info.configuration_hash), query.GetRowData(2));
    Snprintf(sl20_shadow_info.configuration, sizeof(sl20_shadow_info.configuration), query.GetRowData(3));
    return;
}

int SL20Shadow::GetSL20ShadowBySL20LockUUID(const std::string& sl20_lock_uuid, SL20ShadowInfo& sl20_shadow_info)
{
    std::stringstream stream_sql;
    stream_sql << "/*master*/select " << sl20_shadow_info_sec << " from SL20Shadow where SL20LockUUID = '" << sl20_lock_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetSL20ShadowFromSql(sl20_shadow_info, query);
    }
    else
    {
        AK_LOG_WARN << "get SL20ShadowInfo by SL20LockUUID failed, SL20LockUUID = " << sl20_lock_uuid;
        return -1;
    }
    return 0;
}

int SL20Shadow::UpdateSL20ConfigurationShadow(SL20ShadowInfo& sl20_shadow_info)
{
    //生成UUID
    std::string uuid;
    dbinterface::UUID::GenerateUUID(dbinterface::SystemSetting::GetServerTag(), uuid);

    std::stringstream streamsql;
    streamsql << "INSERT INTO SL20Shadow (UUID, SL20LockUUID, ConfigurationHash, Configuration) VALUES "
              << "('"<< uuid << "','" << sl20_shadow_info.sl20_lock_uuid << "','"<< sl20_shadow_info.configuration_hash << "','" << sl20_shadow_info.configuration << "') "
              << "ON DUPLICATE KEY UPDATE ConfigurationHash = '" << sl20_shadow_info.configuration_hash << "', Configuration = '"<< sl20_shadow_info.configuration << "'";

    GET_DB_CONN_ERR_RETURN(conn, -1);
    CRldb* tmp_conn = conn.get();
    if (tmp_conn->Execute(streamsql.str()) < 0)
    {
        AK_LOG_WARN << "Failed to insert new record into db,SQL is " << streamsql.str();
        return -1;
    }
    return 0;
}

}