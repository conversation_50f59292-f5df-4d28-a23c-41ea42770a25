#ifndef __DB_S_L20_SHADOW_H__
#define __DB_S_L20_SHADOW_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct SL20ShadowInfo_T
{
    char uuid[36];
    char sl20_lock_uuid[36];
    char configuration_hash[32];
    char configuration[4096];
    SL20ShadowInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} SL20ShadowInfo;

namespace dbinterface {

class SL20Shadow
{
public:
    static int GetSL20ShadowBySL20LockUUID(const std::string& sl20_lock_uuid, SL20ShadowInfo& sl20_shadow_info);
    static int UpdateSL20ConfigurationShadow(SL20ShadowInfo& sl20_shadow_info);

private:
    SL20Shadow() = delete;
    ~SL20Shadow() = delete;
    static void GetSL20ShadowFromSql(SL20ShadowInfo& sl20_shadow_info, CRldbQuery& query);
};

}
#endif