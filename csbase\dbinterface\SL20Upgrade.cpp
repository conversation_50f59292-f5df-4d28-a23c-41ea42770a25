#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "SL20Upgrade.h"

namespace dbinterface {

static const std::string sl20_upgrade_info_sec = " UUID,UpgradeVersion,UpgradeLockBodyVersion,UpgradeCombinedVersion,UpgradeStatus,SL20LockUUID,FirmwareDownloadUrl ";

void SL20Upgrade::GetSL20UpgradeFromSql(SL20UpgradeInfo& sl20_upgrade_info, CRldbQuery& query)
{
    Snprintf(sl20_upgrade_info.uuid, sizeof(sl20_upgrade_info.uuid), query.GetRowData(0));
    Snprintf(sl20_upgrade_info.upgrade_module_version, sizeof(sl20_upgrade_info.upgrade_module_version), query.GetRowData(1));
    Snprintf(sl20_upgrade_info.upgrade_lock_body_version, sizeof(sl20_upgrade_info.upgrade_lock_body_version), query.GetRowData(2));
    Snprintf(sl20_upgrade_info.upgrade_combined_version, sizeof(sl20_upgrade_info.upgrade_combined_version), query.GetRowData(3));
    sl20_upgrade_info.upgrade_status = ATOI(query.GetRowData(4));
    Snprintf(sl20_upgrade_info.sl20_lock_uuid, sizeof(sl20_upgrade_info.sl20_lock_uuid), query.GetRowData(5));
    Snprintf(sl20_upgrade_info.firmware_download_url, sizeof(sl20_upgrade_info.firmware_download_url), query.GetRowData(6));
    return;
}

int SL20Upgrade::GetSL20UpgradeByUUID(const std::string& uuid, SL20UpgradeInfo& sl20_upgrade_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << sl20_upgrade_info_sec << " from SL20Upgrade where UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetSL20UpgradeFromSql(sl20_upgrade_info, query);
    }
    else
    {
        AK_LOG_WARN << "get SL20UpgradeInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}

int SL20Upgrade::GetSL20UpgradeBySL20LockUUID(const std::string& sl20_lock_uuid, SL20UpgradeInfo& sl20_upgrade_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << sl20_upgrade_info_sec << " from SL20Upgrade where SL20LockUUID = '" << sl20_lock_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetSL20UpgradeFromSql(sl20_upgrade_info, query);
    }
    else
    {
        return -1;
    }
    return 0;
}

int SL20Upgrade::UpdateSL20UpgradeStatus(const std::string& sl20_lock_uuid, int upgrade_status)
{
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    char sql[1024] = "";
    ::snprintf(sql, sizeof(sql), "update SL20Upgrade set UpgradeStatus=%d where SL20LockUUID='%s'",
        upgrade_status, sl20_lock_uuid.c_str()
    );

    int ret = db_conn->Execute(sql) >= 0 ? 0 : -1;
    return ret;
}

}