#ifndef __DB_S_L20_UPGRADE_H__
#define __DB_S_L20_UPGRADE_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct SL20UpgradeInfo_T
{
    char uuid[36];
    char upgrade_module_version[16];
    char upgrade_lock_body_version[16];
    char upgrade_combined_version[16];
    int upgrade_status;
    char sl20_lock_uuid[36];
    char firmware_download_url[256];
    SL20UpgradeInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} SL20UpgradeInfo;

namespace dbinterface {

class SL20Upgrade
{
public:
    static int GetSL20UpgradeByUUID(const std::string& uuid, SL20UpgradeInfo& sl20_upgrade_info);
    static int GetSL20UpgradeBySL20LockUUID(const std::string& sl20_lock_uuid, SL20UpgradeInfo& sl20_upgrade_info);
    static int UpdateSL20UpgradeStatus(const std::string& sl20_lock_uuid, int upgrade_status);

private:
    SL20Upgrade() = delete;
    ~SL20Upgrade() = delete;
    static void GetSL20UpgradeFromSql(SL20UpgradeInfo& sl20_upgrade_info, CRldbQuery& query);
};

}
#endif