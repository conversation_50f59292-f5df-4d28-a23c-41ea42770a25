#ifndef __DB_SL50_LOCK_H__
#define __DB_SL50_LOCK_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct SL50LockInfo_T
{
    char uuid[64];
    char smartlock_uuid[64];
    char wifi_name[128];
    char last_connected_time[32];
    char language[16];
    char volume[16];
    int stay_alarm;
    int stay_alarm_time;
    char monitoring_scope[16];
    int enable_two_factory_auth;
    char sip_account[32];
    char sip_pwd[64];
    char rtsp_pwd[64];
    char update_time[32];
    char create_time[32];
    
    SL50LockInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} SL50LockInfo;
    
typedef std::vector<SL50LockInfo> SL50LockInfoList;

namespace dbinterface {

class SL50Lock
{
public:
    /**
     * 根据SmartLock UUID获取SL50Lock信息
     */
    static int GetSL50LockInfoBySmartLockUUID(const std::string& smartlock_uuid, SL50LockInfo& sl50_lock_info);
    
    /**
     * 根据UUID获取SL50Lock信息
     */
    static int GetSL50LockInfoByUUID(const std::string& uuid, SL50LockInfo& sl50_lock_info);
    
    static int UpdateSL50LockInfoBySmartLockUUID(const std::string& smartlock_uuid, const SL50LockInfo& sl50_lock_info);

private:
    SL50Lock() = delete;
    ~SL50Lock() = delete;
    
    /**
     * 从SQL查询结果中获取SL50Lock信息
     */
    static void GetSL50LockFromSql(SL50LockInfo& sl50_lock_info, CRldbQuery& query);
};

}

#endif
