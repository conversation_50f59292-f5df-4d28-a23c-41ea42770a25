#include <sstream>
#include "util.h"
#include "AkLogging.h"
#include "SaltoLock.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

namespace dbinterface
{
static const std::string salto_lock_sec = "UUID,Name,ThirdUUID,IQUUID,DeviceUUID,Relay,RBACDataGroupUUID";

void SaltoLock::GetSaltoLockFromSql(SaltoLockInfo& salto_lock, CRldbQuery& query)
{
    Snprintf(salto_lock.uuid, sizeof(salto_lock.uuid), query.GetRowData(0));
    Snprintf(salto_lock.name, sizeof(salto_lock.name), query.GetRowData(1));
    Snprintf(salto_lock.third_uuid, sizeof(salto_lock.third_uuid), query.GetRowData(2));
    Snprintf(salto_lock.iq_uuid, sizeof(salto_lock.iq_uuid), query.GetRowData(3));
    Snprintf(salto_lock.device_uuid, sizeof(salto_lock.device_uuid), query.GetRowData(4));
    salto_lock.relay = ATOI(query.GetRowData(5));
    Snprintf(salto_lock.rbac_data_group_uuid, sizeof(salto_lock.rbac_data_group_uuid), query.GetRowData(6));
    return;
}

// 一台设备多个relay,每个relay都可以绑定一把锁
int SaltoLock::GetSaltoLockListByDeviceUUID(const std::string& device_uuid, SaltoLockInfoList& salto_lock_list)
{
    GET_DB_CONN_ERR_RETURN(conn, -1)

    std::stringstream stream_sql;
    stream_sql << "select " << salto_lock_sec <<" from SaltoLock where DeviceUUID = '" << device_uuid << "'";

    CRldbQuery query(conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        SaltoLockInfo salto_lock;
        GetSaltoLockFromSql(salto_lock, query);
        salto_lock_list.push_back(salto_lock);
    }
    
    return 0;
}
}

