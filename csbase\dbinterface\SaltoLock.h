#ifndef __DB_SALTO_LOCK_H__
#define __DB_SALTO_LOCK_H__

#include <string>
#include "Rldb.h"
#include "RldbQuery.h"

typedef struct SaltoLockInfo_T
{
    int relay;
    char uuid[64];
    char name[256];
    char iq_uuid[64];
    char third_uuid[64];
    char device_uuid[64];
    char rbac_data_group_uuid[64];

    SaltoLockInfo_T() {
        memset(this, 0, sizeof(*this));
    }
}SaltoLockInfo;

typedef std::vector<SaltoLockInfo> SaltoLockInfoList;

namespace dbinterface{

class SaltoLock
{
public:
    static int GetSaltoLockListByDeviceUUID(const std::string& device_uuid, SaltoLockInfoList& dormakaba_lock_list);
    
private:
    SaltoLock() = delete;
    ~SaltoLock() = delete;
    
    static void GetSaltoLockFromSql(SaltoLockInfo& dormakaba_lock, CRldbQuery& query);
};

}

#endif
