#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "Shadow.h"
#include <string.h>
#include "AkLogging.h"
#include "util.h"


namespace dbinterface
{

static std::string GetShadowColumnByType(SHADOW_TYPE type)
{
    std::string column;
    switch(type)
    {
        case SHADOW_CONFIG:
            column = "ConfigPath";
            break;
        case SHADOW_PRIKEY:
            column = "PrivatekeyPath";
            break;
        case SHADOW_RFID:
            column = "RfidPath";
            break;
        case SHADOW_CONTACT:
            column = "ContactPath";
            break;
        case SHADOW_FACECONF:
            column = "FacePath";
            break;     
        case SHADOW_SCHE:
            column = "SchedulePath";
            break;
        case SHADOW_USERMETA:
            column = "UserMetaPath";
            break; 
        default:
            column = "";
    }    
    return column;
}


Shadow::Shadow()
{

}

std::string Shadow::GetMd5ColumnByType(SHADOW_TYPE type)
{
    std::string column;
    switch(type)
    {
        case SHADOW_CONFIG:
            column = "ConfigMD5";
            break;
        case SHADOW_PRIKEY:
            column = "PrivatekeyMD5";
            break;
        case SHADOW_RFID:
            column = "RfidMD5";
            break;
        case SHADOW_CONTACT:
            column = "ContactMD5";
            break;
        case SHADOW_FACECONF:
            column = "FaceMD5";
            break;     
        case SHADOW_SCHE:
            column = "ScheduleMD5";
            break;
        case SHADOW_USERMETA:
            column = "UserMetaMD5";
            break; 
        default:
            column = "";
    }    
    return column;
}

int Shadow::GetAllShadowByMac(const std::string& mac, DevShadow &shadow)
{
    std::stringstream sql;

    sql << "/*master*/SELECT ConfigPath,PrivatekeyPath,RfidPath,ContactPath,FacePath,SchedulePath,UserMetaPath" 
        << " FROM DevicesShadow WHERE MAC = '" << mac
        <<"';";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    { 
        Snprintf(shadow.config_storage_path, sizeof(shadow.config_storage_path), query.GetRowData(0));
        Snprintf(shadow.prikey_storage_path, sizeof(shadow.prikey_storage_path), query.GetRowData(1));
        Snprintf(shadow.rfkey_storage_path, sizeof(shadow.rfkey_storage_path), query.GetRowData(2));
        Snprintf(shadow.contac_storage_path, sizeof(shadow.contac_storage_path), query.GetRowData(3));
        Snprintf(shadow.face_storage_path, sizeof(shadow.face_storage_path), query.GetRowData(4));
        Snprintf(shadow.schedule_storage_path, sizeof(shadow.schedule_storage_path), query.GetRowData(5));
        Snprintf(shadow.usermeta_storage_path, sizeof(shadow.usermeta_storage_path), query.GetRowData(6));
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;    
}

int Shadow::DelShadowByMac(const std::string& mac)
{
    std::stringstream sql,sql1;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    sql << "SELECT ID FROM PersonalDevices"
        <<" WHERE MAC = '" << mac
        << "' UNION SELECT ID FROM Devices WHERE MAC = '" << mac
        <<"';";
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {       
        AK_LOG_WARN << "Device does not delete";   //做保护,设备删除 后马上又加回去就会把正常的删除了
        ReleaseDBConn(conn);
        return -1;
    }

    sql1 << "DELETE FROM DevicesShadow WHERE MAC = '"
         << mac << "';";

    int ret = tmp_conn->Execute(sql1.str()) >= 0 ? 0 : -1;   
    ReleaseDBConn(conn);
    
    return ret;    
}

std::string Shadow::GetShadowByMac(const std::string& mac, SHADOW_TYPE shadow_type)
{
    std::string ret_value;
    std::string column = GetShadowColumnByType(shadow_type);
    if(column.size() == 0)
    {
        AK_LOG_WARN << "shadow type is illegal.";
        return ret_value;
    }
    std::stringstream sql;
    sql << "SELECT "  << column 
        << " FROM DevicesShadow WHERE MAC = '" << mac
        <<"';";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return ret_value;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        ret_value = query.GetRowData(0);       
    }
    
    ReleaseDBConn(conn);
    return ret_value;    
}

int Shadow::UpdateShadowByMac(const std::string& mac, SHADOW_TYPE shadow_type, const std::string& value)
{
    std::stringstream sql;
    
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::string column = GetShadowColumnByType(shadow_type);
    if(column.size() == 0)
    {
        AK_LOG_WARN << "shadow type is illegal.";
        return -1;
    }
    
    sql << "INSERT INTO DevicesShadow (" << column 
         << ",MAC) VALUES ('"  << value << "','" << mac << "')"
         << " ON DUPLICATE KEY UPDATE " << column 
         << " = '" << value << "';";
   
    int ret = tmp_conn->Execute(sql.str()) >= 0 ? 0 : -1;   
    ReleaseDBConn(conn);
    return ret;
}

int Shadow::RecordShaowError(const std::string& mac, SHADOW_TYPE shadow_type, const std::string& server_ip)
{
    std::stringstream sql;
    
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    sql << "INSERT INTO DevicesShadowError "  
         << "(MAC,ShadowType,ServerIP) VALUES ('"  << mac << "', " << shadow_type << ", '" << server_ip << "')";   

    int ret = tmp_conn->Execute(sql.str()) >= 0 ? 0 : -1;   
    ReleaseDBConn(conn);
    return ret;
}


}

