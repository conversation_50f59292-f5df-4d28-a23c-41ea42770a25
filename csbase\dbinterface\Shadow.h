#ifndef __SHADOW_H__
#define __SHADOW_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>


typedef struct DevShadow_T
{
    char usermeta_storage_path[128];
    char schedule_storage_path[128];
    char config_storage_path[128];
    char contac_storage_path[128];
    char prikey_storage_path[128];
    char rfkey_storage_path[128];
    char face_storage_path[128];
} DevShadow;

enum SHADOW_TYPE{
    SHADOW_CONFIG = 0,
    SHADOW_PRIKEY,
    SHADOW_RFID,
    SHADOW_CONTACT,
    SHADOW_FACECONF,
    SHADOW_SCHE,
    SHADOW_USERMETA,
    SHADOW_USERALL,

    SHADOW_NONE = 100,
};

namespace dbinterface
{

class Shadow
{
public:
    Shadow();
    ~Shadow();

    static int GetAllShadowByMac(const std::string& mac, DevShadow &shadow);
    static int DelShadowByMac(const std::string& mac);
    static std::string GetShadowByMac(const std::string& mac, SHADOW_TYPE shadow_type);
    static int UpdateShadowByMac(const std::string& mac, SHADOW_TYPE shadow_type, const std::string& value);
    static int RecordShaowError(const std::string& mac, SHADOW_TYPE shadow_type, const std::string& server_ip);
    static std::string GetMd5ColumnByType(SHADOW_TYPE type);
private:
    
};


}
#endif
