#include <sstream>
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/Sip.h"
#include <string.h>
#include "AkLogging.h"
#include "util.h"

namespace dbinterface{
Sip::Sip()
{

}

Sip::~Sip()
{

}

std::string Sip::GetNodeByGroupFromSip2(const std::string& sipgroup)
{
    std::stringstream strsql;
    strsql << "SELECT  Account FROM SipGroup2 "
           << "WHERE SipGroup = '"
           << sipgroup
           << "' limit 1";
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        return "";
    }
    CRldbQuery query(tmp_conn);
    query.Query(strsql.str());
    if (query.MoveToNextRow())
    {
        char node[32] = {0};
        Snprintf(node, sizeof(node),  query.GetRowData(0));
        ReleaseDBConn(conn);
        return node;
    }
    ReleaseDBConn(conn);
    return "";
}

std::string Sip::GetSipGroupByNode(const std::string &account)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return "";
    }

    std::stringstream stream_sql;;
    stream_sql << "select SipGroup from SipGroup2 WHERE Account = '" << account << "' limit 1";
  
    CRldbQuery query(tmp_conn);
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        char sip_group[32] = {0};
        Snprintf(sip_group, sizeof(sip_group),  query.GetRowData(0));
        ReleaseDBConn(conn);
        return sip_group;
    }
    else
    {
        ReleaseDBConn(conn);
        return "";
    }
}


int Sip::GetSipGroupListByProject(const std::string &project_uuid, ProjectSipGroupMap &sip_group_map)
{
    std::stringstream stream_sql;;
    stream_sql << "select P.UUID,S.SipGroup From SipGroup2 S left join PersonalAccount P on P.Account=S.Account "
        << "left join OfficePersonnel O on O.PersonalAccountUUID=P.UUID where O.AccountUUID='" << project_uuid << "'";

    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    
    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        sip_group_map.insert(std::make_pair( query.GetRowData(0),  query.GetRowData(1)));
    }


    stream_sql.str("");
    stream_sql << "select P.UUID,S.SipGroup From SipGroup2 S left join PersonalAccount P on P.Account=S.Account "
        << "left join OfficeAdmin O on O.PersonalAccountUUID=P.UUID where O.OfficeUUID='" << project_uuid << "'";

    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        sip_group_map.insert(std::make_pair( query.GetRowData(0),  query.GetRowData(1)));
    }

    return 0;
}

int Sip::GetSipGroupListByProjectId(uint32_t project_id, ProjectSipGroupNodeMap &sip_group_map)
{
    std::stringstream stream_sql;;
    stream_sql << "select P.Account,S.SipGroup From SipGroup2 S left join PersonalAccount P on P.Account=S.Account "
        << " where P.ParentID=" << project_id << " and P.Role=" << GetCommunityMainRole();

    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    
    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        sip_group_map.insert(std::make_pair( query.GetRowData(0),  query.GetRowData(1)));
    }
    return 0;
}


}


