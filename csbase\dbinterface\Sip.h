#ifndef __DB_SIP_H__
#define __DB_SIP_H__
#include <string>
#include <memory>
#include <tuple>

#include "ConnectionManager.h"

using ProjectSipGroupMap = std::map<std::string/*per_uuid*/, std::string/*sip_group*/>;
using ProjectSipGroupNodeMap = std::map<std::string/*node*/, std::string/*sip_group*/>;

namespace dbinterface{
class Sip
{
public:
    Sip();
    ~Sip();
    static std::string GetNodeByGroupFromSip2(const std::string& sipgroup);
    static std::string GetSipGroupByNode(const std::string &account);
    static int GetSipGroupListByProject(const std::string &project_uuid, ProjectSipGroupMap &sip_group_map);
    static int GetSipGroupListByProjectId(uint32_t project_id, ProjectSipGroupNodeMap &sip_group_map);
private:
};

}

class SipContorl
{
public:
    SipContorl(){}
    void Init(uint32_t mng_id)
    {
        dbinterface::Sip::GetSipGroupListByProjectId(mng_id, node_sip_group_map_);
    }
    std::string GetNodeSipGroup(const std::string &node)
    {
        auto it = node_sip_group_map_.find(node);
        if (it == node_sip_group_map_.end())
        {
            return "";
        }
        return it->second;
    }
    
    ~SipContorl(){}
private:
    ProjectSipGroupNodeMap node_sip_group_map_;
};
using SipContorlPtr =  std::shared_ptr<SipContorl>;



#endif
