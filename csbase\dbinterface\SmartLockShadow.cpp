#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "SmartLockShadow.h"
#include <string.h>
#include "dbinterface/SystemSettingTable.h"
#include "dbinterface/UUID.h"

namespace dbinterface {

static const std::string smartlock_shadow_info_sec = " UUID,SmartLockUUID,ConfigurationHash,Configuration ";

void SmartLockShadow::GetSmartLockShadowFromSql(SmartLockShadowInfo& smartlock_shadow_info, CRldbQuery& query)
{
    Snprintf(smartlock_shadow_info.uuid, sizeof(smartlock_shadow_info.uuid), query.GetRowData(0));
    Snprintf(smartlock_shadow_info.smartlock_uuid, sizeof(smartlock_shadow_info.smartlock_uuid), query.GetRowData(1));
    Snprintf(smartlock_shadow_info.configuration_hash, sizeof(smartlock_shadow_info.configuration_hash), query.GetRowData(2));
    Snprintf(smartlock_shadow_info.configuration, sizeof(smartlock_shadow_info.configuration), query.GetRowData(3));
    return;
}

int SmartLockShadow::GetSmartLockShadowBySmartLockUUID(const std::string& smart_lock_uuid, SmartLockShadowInfo& smartlock_shadow_info)
{
    std::stringstream stream_sql;
    stream_sql << "/*master*/select " << smartlock_shadow_info_sec << " from SmartLockShadow where SmartLockUUID = '" << smart_lock_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetSmartLockShadowFromSql(smartlock_shadow_info, query);
    }
    else
    {
        AK_LOG_WARN << "get SmartLockShadow by SmartLockUUID failed, SmartLockUUID = " << smart_lock_uuid;
        return -1;
    }
    return 0;
}

int SmartLockShadow::UpdateSmartLockConfigurationShadow(SmartLockShadowInfo& smartlock_shadow_info)
{
    //生成UUID
    std::string uuid;
    dbinterface::UUID::GenerateUUID(dbinterface::SystemSetting::GetServerTag(), uuid);

    std::stringstream streamsql;
    streamsql << "INSERT INTO SmartLockShadow (UUID, SmartLockUUID, ConfigurationHash, Configuration) VALUES "
              << "('"<< uuid << "','" << smartlock_shadow_info.smartlock_uuid << "','"<< smartlock_shadow_info.configuration_hash << "','" << smartlock_shadow_info.configuration << "') "
              << "ON DUPLICATE KEY UPDATE ConfigurationHash = '" << smartlock_shadow_info.configuration_hash << "', Configuration = '"<< smartlock_shadow_info.configuration << "'";

    GET_DB_CONN_ERR_RETURN(conn, -1);
    CRldb* tmp_conn = conn.get();
    if (tmp_conn->Execute(streamsql.str()) < 0)
    {
        AK_LOG_WARN << "Failed to insert new record into db,SQL is " << streamsql.str();
        return -1;
    }
    return 0;
}

}