#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "SmartLockUpgrade.h"

namespace dbinterface {

static const std::string smartlock_upgrade_info_sec = " UUID,UpgradeVersion,UpgradeLockBodyVersion,UpgradeCombinedVersion,UpgradeStatus,SmartLockUUID,FirmwareDownloadUrl ";

void SmartLockUpgrade::GetSmartLockUpgradeFromSql(SmartLockUpgradeInfo& smartlock_upgrade_info, CRldbQuery& query)
{
    Snprintf(smartlock_upgrade_info.uuid, sizeof(smartlock_upgrade_info.uuid), query.GetRowData(0));
    Snprintf(smartlock_upgrade_info.upgrade_module_version, sizeof(smartlock_upgrade_info.upgrade_module_version), query.GetRowData(1));
    Snprintf(smartlock_upgrade_info.upgrade_lock_body_version, sizeof(smartlock_upgrade_info.upgrade_lock_body_version), query.GetRowData(2));
    Snprintf(smartlock_upgrade_info.upgrade_combined_version, sizeof(smartlock_upgrade_info.upgrade_combined_version), query.GetRowData(3));
    smartlock_upgrade_info.upgrade_status = (SMARTLOCK_UPGRADE_STATUS)ATOI(query.GetRowData(4));
    Snprintf(smartlock_upgrade_info.smart_lock_uuid, sizeof(smartlock_upgrade_info.smart_lock_uuid), query.GetRowData(5));
    Snprintf(smartlock_upgrade_info.firmware_download_url, sizeof(smartlock_upgrade_info.firmware_download_url), query.GetRowData(6));
    return;
}

int SmartLockUpgrade::GetSmartLockUpgradeByUUID(const std::string& uuid, SmartLockUpgradeInfo& smartlock_upgrade_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << smartlock_upgrade_info_sec << " from SmartLockUpgrade where UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetSmartLockUpgradeFromSql(smartlock_upgrade_info, query);
    }
    else
    {
        AK_LOG_WARN << "get SmartLockUpgradeInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}

int SmartLockUpgrade::GetSmartLockUpgradeBySmartLockUUID(const std::string& smartlock_uuid, SmartLockUpgradeInfo& smartlock_upgrade_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << smartlock_upgrade_info_sec << " from SmartLockUpgrade where SmartLockUUID = '" << smartlock_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetSmartLockUpgradeFromSql(smartlock_upgrade_info, query);
    }
    else
    {
        return -1;
    }
    return 0;
}

int SmartLockUpgrade::UpdateSmartLockUpgradeStatus(const std::string& smart_lock_uuid, SMARTLOCK_UPGRADE_STATUS upgrade_status)
{
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    char sql[1024] = "";
    ::snprintf(sql, sizeof(sql), "update SmartLockUpgrade set UpgradeStatus=%d where SmartLockUUID='%s'",
        (int)upgrade_status, smart_lock_uuid.c_str()
    );

    int ret = db_conn->Execute(sql) >= 0 ? 0 : -1;
    return ret;
}

}