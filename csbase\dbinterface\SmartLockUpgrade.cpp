#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "SmartLockUpgrade.h"

namespace dbinterface {

static const std::string smart_lock_upgrade_info_sec = " UUID,UpgradeVersion,UpgradeLockBodyVersion,UpgradeCombinedVersion,UpgradeStatus,SmartLockUUID,FirmwareDownloadUrl ";

void SmartLockUpgrade::GetSmartLockUpgradeFromSql(SmartLockUpgradeInfo& smart_lock_upgrade_info, CRldbQuery& query)
{
    Snprintf(smart_lock_upgrade_info.uuid, sizeof(smart_lock_upgrade_info.uuid), query.GetRowData(0));
    Snprintf(smart_lock_upgrade_info.upgrade_module_version, sizeof(smart_lock_upgrade_info.upgrade_module_version), query.GetRowData(1));
    Snprintf(smart_lock_upgrade_info.upgrade_lock_body_version, sizeof(smart_lock_upgrade_info.upgrade_lock_body_version), query.GetRowData(2));
    Snprintf(smart_lock_upgrade_info.upgrade_combined_version, sizeof(smart_lock_upgrade_info.upgrade_combined_version), query.GetRowData(3));
    smart_lock_upgrade_info.upgrade_status = ATOI(query.GetRowData(4));
    Snprintf(smart_lock_upgrade_info.smart_lock_uuid, sizeof(smart_lock_upgrade_info.smart_lock_uuid), query.GetRowData(5));
    Snprintf(smart_lock_upgrade_info.firmware_download_url, sizeof(smart_lock_upgrade_info.firmware_download_url), query.GetRowData(6));
    return;
}

int SmartLockUpgrade::GetSmartLockUpgradeByUUID(const std::string& uuid, SmartLockUpgradeInfo& smart_lock_upgrade_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << smart_lock_upgrade_info_sec << " from SmartLockUpgrade where UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetSmartLockUpgradeFromSql(smart_lock_upgrade_info, query);
    }
    else
    {
        AK_LOG_WARN << "get SmartLockUpgradeInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}

int SmartLockUpgrade::GetSmartLockUpgradeBySmartLockUUID(const std::string& smart_lock_uuid, SmartLockUpgradeInfo& smart_lock_upgrade_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << smart_lock_upgrade_info_sec << " from SmartLockUpgrade where SmartLockUUID = '" << smart_lock_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetSmartLockUpgradeFromSql(smart_lock_upgrade_info, query);
    }
    else
    {
        return -1;
    }
    return 0;
}

int SmartLockUpgrade::UpdateSmartLockUpgradeStatus(const std::string& smart_lock_uuid, int upgrade_status)
{
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    char sql[1024] = "";
    ::snprintf(sql, sizeof(sql), "update SmartLockUpgrade set UpgradeStatus=%d where SmartLockUUID='%s'",
        upgrade_status, smart_lock_uuid.c_str()
    );

    int ret = db_conn->Execute(sql) >= 0 ? 0 : -1;
    return ret;
}


}