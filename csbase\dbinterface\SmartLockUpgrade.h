#ifndef __DB_SMART_LOCK_UPGRADE_H__
#define __DB_SMART_LOCK_UPGRADE_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

enum class SMARTLOCK_UPGRADE_STATUS {
    DONE = 0,
    PROCESSING = 1
};

typedef struct SmartLockUpgradeInfo_T
{
    char uuid[36];
    char upgrade_module_version[16];
    char upgrade_lock_body_version[16];
    char upgrade_combined_version[16];
    char smart_lock_uuid[36];
    char firmware_download_url[256];
    SMARTLOCK_UPGRADE_STATUS upgrade_status;

    SmartLockUpgradeInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} SmartLockUpgradeInfo;

namespace dbinterface {

class SmartLockUpgrade
{
public:
    static int GetSmartLockUpgradeByUUID(const std::string& uuid, SmartLockUpgradeInfo& smart_lock_upgrade_info);
    static int GetSmartLockUpgradeBySmartLockUUID(const std::string& smart_lock_uuid, SmartLockUpgradeInfo& smart_lock_upgrade_info);
    static int UpdateSmartLockUpgradeStatus(const std::string& smart_lock_uuid, int upgrade_status);

private:
    SmartLockUpgrade() = delete;
    ~SmartLockUpgrade() = delete;
    static void GetSmartLockUpgradeFromSql(SmartLockUpgradeInfo& smart_lock_upgrade_info, CRldbQuery& query);
};

}
#endif