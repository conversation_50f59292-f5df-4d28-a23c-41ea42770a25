#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "Staff.h"
#include <string.h>
#include "AkLogging.h"
#include "ConnectionManager.h"

namespace dbinterface
{

Staff::Staff()
{

}

int Staff::InitStaffByID(uint32_t id, StaffInfo &staff)
{
    std::stringstream streamSQL;
    streamSQL << "select Version from Staff where ID = '"
              << id
              << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    if (query.MoveToNextRow())
    {
        staff.version = ATOI(query.GetRowData(0));
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;    
}

int Staff::InitStaffByUUID(const std::string& uuid, StaffInfo& staff)
{
    GET_DB_CONN_ERR_RETURN(conn, -1)

    std::stringstream stream_sql;
    stream_sql << "select Version,CommunityID from Staff where UUID = '"
                       << uuid << "'";

    CRldbQuery query(conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        staff.version = ATOI(query.GetRowData(0));
        staff.community_id = ATOI(query.GetRowData(1));
    }
    
    return 0;    
}

int Staff::GetVersionById(uint32_t id)
{
    StaffInfo staff;
    memset(&staff, 0, sizeof(staff));
    InitStaffByID(id, staff);
    return staff.version;
}

}

