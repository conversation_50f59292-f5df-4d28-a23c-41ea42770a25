#ifndef __DB_STAFF_H__
#define __DB_STAFF_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>

namespace dbinterface
{


typedef struct Staff_T
{
    int version;    //数据库字段长度
    uint32_t community_id; //社区id
    Staff_T()
    {
        memset(this, 0, sizeof(*this));
    }
}StaffInfo;

class Staff
{
public:
    Staff();
    ~Staff();
    
    static int GetVersionById(uint32_t id);
    static int InitStaffByUUID(const std::string& uuid, StaffInfo& staff);
private:
    static int InitStaffByID(uint32_t id, StaffInfo &staff);
};

typedef std::shared_ptr<StaffInfo> StaffInfoPtr;

}
#endif

