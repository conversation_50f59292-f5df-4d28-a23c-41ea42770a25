#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include <string.h>
#include "AkLogging.h"
#include "StaffAccess.h"
#include "util.h"
#include "ConnectionManager.h"
#include <vector>

namespace dbinterface
{

StaffAccess::StaffAccess()
{

}

int StaffAccess::GetAgIDsByStaffID(int staff_id, std::vector<unsigned int>& ag_ids)
{
    std::stringstream stream_sql;
    stream_sql << "select AccessGroupID from StaffAccess where StaffID = " << staff_id;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(stream_sql.str());

    while (query.MoveToNextRow())
    {
       ag_ids.push_back(ATOI(query.GetRowData(0)));
    }

    ReleaseDBConn(conn);
    return 0;

}

int StaffAccess::GetAgIDsByStaffUUID(const std::string& staff_uuid, std::vector<uint32_t>& ag_ids)
{
    GET_DB_CONN_ERR_RETURN(conn, -1)

    std::stringstream stream_sql;
    stream_sql << "select SA.AccessGroupID from StaffAccess SA left join Staff S on SA.StaffID = S.ID "
                       <<  " where S.UUID = '" << staff_uuid << "'";

    CRldbQuery query(conn.get());
    query.Query(stream_sql.str());

    while (query.MoveToNextRow())
    {
        ag_ids.push_back(ATOI(query.GetRowData(0)));
    }

    return 0;
}

/*公共人员：公共设备权限组包含的staff/delivery列表*/
void StaffAccess::GetPubDevStaffListByAccessGroupID(uint id, UserAccessNodeList &list)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }

    std::stringstream str_sql;
    str_sql << "select P.Name,P.CardCode,P.Version,P.UUID,P.FaceUrl,P.FaceMD5,P.PinCode,P.ID,SID.Mode,SID.Run,SID.Serial From StaffAccess D left join Staff P "
            << "on P.ID=D.StaffID left join StaffIDAccess SID on SID.StaffUUID=P.UUID where D.AccessGroupID=" << id << " order by D.ID";

    CRldbQuery query(tmp_conn);
    query.Query(str_sql.str());
    while (query.MoveToNextRow())
    {
        UserAccessNode ua;
        ua.ag_id = id;
        Snprintf(ua.name, sizeof(ua.name), query.GetRowData(0));
        Snprintf(ua.pm_rf, sizeof(ua.pm_rf), query.GetRowData(1));
        Snprintf(ua.meta, sizeof(ua.meta), query.GetRowData(2));
        Snprintf(ua.db_uuid, sizeof(ua.db_uuid), query.GetRowData(3));
        Snprintf(ua.face_url, sizeof(ua.face_url), query.GetRowData(4));
        Snprintf(ua.face_md5, sizeof(ua.face_md5), query.GetRowData(5));
        Snprintf(ua.pin, sizeof(ua.pin), query.GetRowData(6));
        ua.dbid = ATOI(query.GetRowData(7));
        ua.id_access_mode = ATOI(query.GetRowData(8));
        Snprintf(ua.id_access_run, sizeof(ua.id_access_run), query.GetRowData(9));
        Snprintf(ua.id_access_serial, sizeof(ua.id_access_serial), query.GetRowData(10));
        list.push_back(ua);
    }

    ReleaseDBConn(conn);      
}

}

