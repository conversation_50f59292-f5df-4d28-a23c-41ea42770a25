#ifndef __DB_STAFF_ACCESS_H__
#define __DB_STAFF_ACCESS_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include "AkcsCommonDef.h"
#include "AkcsCommonSt.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "AccessGroupDB.h"

namespace dbinterface
{

class StaffAccess
{
public:
    StaffAccess();
    ~StaffAccess();
    static int GetAgIDsByStaffID(int staff_id, std::vector<unsigned int>& ag_ids);
    static int GetAgIDsByStaffUUID(const std::string& staff_uuid, std::vector<uint32_t>& ag_ids);
    static void GetPubDevStaffListByAccessGroupID(uint id, UserAccessNodeList &list);
private: 
};


}

#endif

