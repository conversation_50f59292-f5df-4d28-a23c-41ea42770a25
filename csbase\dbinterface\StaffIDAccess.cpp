#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "StaffIDAccess.h"

namespace dbinterface {

static const std::string staff_id_access_info_sec = " UUID,StaffUUID,Mode,Run,Serial ";

void StaffIDAccess::GetStaffIDAccessFromSql(StaffIDAccessInfo& staff_id_access_info, CRldbQuery& query)
{
    Snprintf(staff_id_access_info.uuid, sizeof(staff_id_access_info.uuid), query.GetRowData(0));
    Snprintf(staff_id_access_info.staff_uuid, sizeof(staff_id_access_info.staff_uuid), query.GetRowData(1));
    staff_id_access_info.mode = ATOI(query.GetRowData(2));
    Snprintf(staff_id_access_info.run, sizeof(staff_id_access_info.run), query.GetRowData(3));
    Snprintf(staff_id_access_info.serial, sizeof(staff_id_access_info.serial), query.GetRowData(4));
    return;
}

int StaffIDAccess::GetStaffIDAccessByUUID(const std::string& uuid, StaffIDAccessInfo& staff_id_access_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << staff_id_access_info_sec << " from StaffIDAccess where UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetStaffIDAccessFromSql(staff_id_access_info, query);
    }
    else
    {
        AK_LOG_WARN << "get StaffIDAccessInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}

int StaffIDAccess::GetStaffIDAccessByStaffUUID(const std::string& staff_uuid, StaffIDAccessInfo& staff_id_access_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << staff_id_access_info_sec << " from StaffIDAccess where StaffUUID = '" << staff_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetStaffIDAccessFromSql(staff_id_access_info, query);
    }
    else
    {
        AK_LOG_WARN << "get StaffIDAccessInfo by StaffUUID failed, StaffUUID = " << staff_uuid;
        return -1;
    }
    return 0;
}

}