#ifndef __DB_STAFF_I_D_ACCESS_H__
#define __DB_STAFF_I_D_ACCESS_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct StaffIDAccessInfo_T
{
    char uuid[36];
    char staff_uuid[36];
    int mode;
    char run[10];
    char serial[9];
    StaffIDAccessInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} StaffIDAccessInfo;

namespace dbinterface {

class StaffIDAccess
{
public:
    static int GetStaffIDAccessByUUID(const std::string& uuid, StaffIDAccessInfo& staff_id_access_info);
    static int GetStaffIDAccessByStaffUUID(const std::string& staff_uuid, StaffIDAccessInfo& staff_id_access_info);

private:
    StaffIDAccess() = delete;
    ~StaffIDAccess() = delete;
    static void GetStaffIDAccessFromSql(StaffIDAccessInfo& staff_id_access_info, CRldbQuery& query);
};

}
#endif