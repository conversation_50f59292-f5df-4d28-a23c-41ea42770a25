#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "SystemSettingTable.h"
#include <string.h>
#include "AkLogging.h"

namespace dbinterface
{

SystemSetting::SystemSetting()
{

}

std::string SystemSetting::GetServerTag()
{
    static std::string server_tag;
    if(server_tag.length() > 0){
        return server_tag;
    }

    std::stringstream streamSQL;
    streamSQL << "select ServerTag from SystemSetting";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return "";
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    if (query.MoveToNextRow())
    {
        server_tag = query.GetRowData(0);
    }
    else
    {
        ReleaseDBConn(conn);
        return "";
    }
    ReleaseDBConn(conn);
    return server_tag;
}

int SystemSetting::SystemExecUpgradeLock(int lock_second)
{
    std::stringstream sql;
    sql << "select UpgradeDevLock,UpgradeDevLockTime < now() as timeout FROM SystemSetting where 1=1 limit 1;";
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return 0;
    }
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());

    int lock = 0;
    int timeout = 0;
    if (query.MoveToNextRow())
    {
        lock = ATOI(query.GetRowData(0));
        timeout = ATOI(query.GetRowData(1));
    }
    
    if (lock == 0 || (lock == 1 && timeout == 1 ) )
    {
        std::stringstream sql2;
        sql2 << "UPDATE SystemSetting SET UpgradeDevLock = 1,  UpgradeDevLockTime=now() + " << lock_second << " where 1=1 limit 1";
        tmp_conn->Execute(sql2.str());
        ReleaseDBConn(conn);
        return 1;
    }
    ReleaseDBConn(conn);
    return 0;
}


void SystemSetting::SystemExecUpgradeUnLock()
{
    std::stringstream sql;
    sql << "UPDATE SystemSetting SET UpgradeDevLock = 0  where 1=1 limit 1;";
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return ;
    }
    tmp_conn->Execute(sql.str());
    ReleaseDBConn(conn);
    return;
}

}
