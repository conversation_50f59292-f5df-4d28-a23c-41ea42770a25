#ifndef __DB_SYSTEM_SETTING_Table_H__
#define __DB_SYSTEM_SETTING_Table_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>

namespace dbinterface
{

class SystemSetting
{
public:
    SystemSetting();
    ~SystemSetting();
    
    static std::string GetServerTag();
    static int SystemExecUpgradeLock(int lock_second);
    static void SystemExecUpgradeUnLock();
private:
};


}
#endif

