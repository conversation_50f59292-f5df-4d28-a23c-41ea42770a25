#include <sstream>
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/InterfaceComm.h"
#include "ThirdParty.h"
#include <string.h>
#include "AkLogging.h"
#include "util.h"

namespace dbinterface{
ThirdParty::ThirdParty()
{

}

ThirdParty::~ThirdParty()
{

}

int ThirdParty::GetThirdPartyInfo(const char* mac, ThirdPartyInfo& third_party_info)
{
    AK_LOG_DEBUG << "DaoGetThirdPartyInfo begin, mac=" << mac;

    std::stringstream stream_sql;
    stream_sql << "SELECT b.AccountID, b.CommunityID, b.GsfaceLoginApi "
               << "  FROM Devices a "
               << " JOIN ThirdParty b ON b.CommunityID = a.MngAccountID "
               << " where a.MAC = '"
               << mac
               << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed";
        return -1;
    }

    CRldbQuery query(tmp_conn);
    query.Query(stream_sql.str());

    if (query.MoveToNextRow())
    {
        third_party_info.account_id = ATOI(query.GetRowData(0));
        third_party_info.community_id = ATOI(query.GetRowData(1));
        Snprintf(third_party_info.gs_face_login_api, GS_FACE_LOGIN_API_SIZE,  query.GetRowData(2));
        AK_LOG_INFO << "GsfaceLoginApi =" << third_party_info.gs_face_login_api;
    }
    else
    {
        //do nothing
    }
    ReleaseDBConn(conn);

    AK_LOG_DEBUG << "DaoGethird_party_info end.";
    return 0;
}


bool ThirdParty::IsFaceServerDevice(const char *firmware) 
{
	const std::string version_29 = "29.";
	const std::string version_916 = "916.";

	std::string device_firmware = firmware;
	if (device_firmware.compare(0, version_29.size(), version_29) == 0 ||
		device_firmware.compare(0, version_916.size(), version_916) == 0)
	{
		return true;
	}

	return false;
}


}


