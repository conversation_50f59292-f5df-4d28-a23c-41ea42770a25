#ifndef _DB_THIRD_PARTY_H_
#define _DB_THIRD_PARTY_H_

#include <string>
#include <memory>
#include <tuple>
#include "BasicDefine.h"

#define GS_FACE_LOGIN_API_SIZE 64

struct ThirdPartyInfo
{
    uint32_t account_id;
    uint32_t community_id;
    char gs_face_login_api[GS_FACE_LOGIN_API_SIZE + 1];
};

namespace dbinterface{
class ThirdParty
{
public:
    ThirdParty();
    ~ThirdParty();
    static int GetThirdPartyInfo(const char* mac, ThirdPartyInfo& third_party_info);
    static bool IsFaceServerDevice(const char *firmware);
private:
};

}


#endif
