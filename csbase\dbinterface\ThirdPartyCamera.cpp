#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "ThirdPartyCamera.h"
#include <string.h>
#include "AkLogging.h"
#include "util.h"
#include "ConnectionManager.h"

namespace dbinterface
{

ThirdPartyCamrea::ThirdPartyCamrea()
{

}

static const std::string camera_sec = "UUID,Location,RtspAddress,RtspUserName,RtspPwd,MAC,VideoPt,VideoType,VideoFmtp,ProjectUUID,UnitID,PersonalAccountUUID,Grade,AllowEndUserMonitor,MonitoringPlatform";


int ThirdPartyCamrea::GetCameraFromSql(ThirdPartyCamreaInfo &camera, CRldbQuery& query)
{
    Snprintf(camera.camera_uuid, sizeof(camera.camera_uuid), query.GetRowData(0));
    Snprintf(camera.location, sizeof(camera.location), query.GetRowData(1));
    Snprintf(camera.rtsp_url, sizeof(camera.rtsp_url), query.GetRowData(2));
    Snprintf(camera.username, sizeof(camera.username), query.GetRowData(3));
    Snprintf(camera.passwd, sizeof(camera.passwd), query.GetRowData(4));
    Snprintf(camera.mac, sizeof(camera.mac), query.GetRowData(5));
    camera.video_pt = ATOI(query.GetRowData(6));
    Snprintf(camera.video_type, sizeof(camera.video_type), query.GetRowData(7));
    Snprintf(camera.video_fmtp, sizeof(camera.video_fmtp), query.GetRowData(8));
    Snprintf(camera.project_uuid, sizeof(camera.project_uuid), query.GetRowData(9));
    camera.unit_id = ATOI(query.GetRowData(10));
    Snprintf(camera.personal_uuid, sizeof(camera.personal_uuid), query.GetRowData(11));
    camera.grade = ATOI(query.GetRowData(12));

    Snprintf(camera.project_uuid, sizeof(camera.project_uuid), query.GetRowData(9));
    camera.unit_id = ATOI(query.GetRowData(10));
    Snprintf(camera.personal_uuid, sizeof(camera.personal_uuid), query.GetRowData(11));
    camera.grade = ATOI(query.GetRowData(12));
    camera.allow_end_user_monitor = ATOI(query.GetRowData(13));
    camera.monitoring_platform = ATOI(query.GetRowData(14));


    return 0;
}



int ThirdPartyCamrea::GetPubThirdPartyCameraList(const std::string &mng_uuid, ThirdPartyCamreaList &pub_camera_list)
{
    std::stringstream streamSQL;
    streamSQL << "select "<< camera_sec <<" from ThirdPartCamera where ProjectUUID = '"
              << mng_uuid  << "' and Grade = " << csmain::COMMUNITY_DEVICE_TYPE_PUBLIC;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    
    while (query.MoveToNextRow())
    {
        ThirdPartyCamreaInfo camera;
        Snprintf(camera.project_uuid, sizeof(camera.project_uuid), mng_uuid.c_str());
        camera.grade = csmain::COMMUNITY_DEVICE_TYPE_PUBLIC;
        GetCameraFromSql(camera, query);

        pub_camera_list.push_back(camera);
    }

    if (pub_camera_list.size() == 0)
    {
        ReleaseDBConn(conn);
        return -1;  
    }
    
    ReleaseDBConn(conn);
    return 0;    
}

int ThirdPartyCamrea::GetUnitThirdPartyCameraList(const std::string &mng_uuid, int unit_id, ThirdPartyCamreaList &unit_camera_list)
{
    std::stringstream streamSQL;
    streamSQL << "select "<< camera_sec <<" from ThirdPartCamera where ProjectUUID = '"
              << mng_uuid  << "' and UnitID = " << unit_id << " and Grade = " << csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    
    while (query.MoveToNextRow())
    {
        ThirdPartyCamreaInfo camera;
        Snprintf(camera.project_uuid, sizeof(camera.project_uuid), mng_uuid.c_str());
        camera.unit_id = unit_id;
        camera.grade = csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT;
        GetCameraFromSql(camera, query);

        unit_camera_list.push_back(camera);
    }

    if (unit_camera_list.size() == 0)
    {
        ReleaseDBConn(conn);
        return -1;  
    }

    ReleaseDBConn(conn);
    return 0;    
}

int ThirdPartyCamrea::GetPubandUnitThirdPartyCameraList(const std::string &mng_uuid, ThirdPartyCamreaList &pub_camera_list)
{
    std::stringstream streamSQL;
    streamSQL << "select "<< camera_sec <<" from ThirdPartCamera where ProjectUUID = '"
              << mng_uuid  << "' and Grade in (" << csmain::COMMUNITY_DEVICE_TYPE_PUBLIC << "," << csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT << ")" ;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    
    while (query.MoveToNextRow())
    {
        ThirdPartyCamreaInfo camera;
        Snprintf(camera.project_uuid, sizeof(camera.project_uuid), mng_uuid.c_str());
        GetCameraFromSql(camera, query);

        pub_camera_list.push_back(camera);
    }

    if (pub_camera_list.size() == 0)
    {
        ReleaseDBConn(conn);
        return -1;  
    }

    ReleaseDBConn(conn);
    return 0;    
}


int ThirdPartyCamrea::GetNodeThirdPartyCameraList(const std::string &personal_uuid, ThirdPartyCamreaList &node_camera_list)
{
    std::stringstream streamSQL;
    streamSQL << "select "<< camera_sec <<" from ThirdPartCamera where PersonalAccountUUID = '"
              << personal_uuid << "' and Grade = " << csmain::COMMUNITY_DEVICE_TYPE_PERSONAL;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    
    while (query.MoveToNextRow())
    {
        ThirdPartyCamreaInfo camera;
        Snprintf(camera.personal_uuid, sizeof(camera.personal_uuid), personal_uuid.c_str());
        camera.grade = csmain::COMMUNITY_DEVICE_TYPE_PERSONAL;
        GetCameraFromSql(camera, query);

        node_camera_list.push_back(camera);
    }

    if (node_camera_list.size() == 0)
    {
        ReleaseDBConn(conn);
        return -1;  
    }

    ReleaseDBConn(conn);
    return 0;    
}

int ThirdPartyCamrea::GetThirdPartyCameraByMac(const std::string &mac, ThirdPartyCamreaInfo &third_camera)
{
    std::stringstream streamSQL;
    streamSQL << "select "<< camera_sec <<" from ThirdPartCamera where MAC = '"
              << mac << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    
    if (query.MoveToNextRow())
    {
        GetCameraFromSql(third_camera, query);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }

    ReleaseDBConn(conn);
    return 0;    
}

int ThirdPartyCamrea::GetThirdPartyCameraByUUID(const std::string &uuid, ThirdPartyCamreaInfo &third_camera)
{
    std::stringstream streamSQL;
    streamSQL << "select "<< camera_sec <<" from ThirdPartCamera where UUID = '" << uuid << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    
    if (query.MoveToNextRow())
    {
        GetCameraFromSql(third_camera, query);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }

    ReleaseDBConn(conn);
    return 0;    
}

int ThirdPartyCamrea::UpdateThirdPartyCameraVideoInfo(ThirdPartyCamreaInfo &third_camera)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::stringstream streamsql;
    streamsql << "update ThirdPartCamera set VideoType= '" << third_camera.video_type << "' , VideoPt = " << third_camera.video_pt << ", VideoFmtp = '" << third_camera.video_fmtp 
              << "' where UUID = '" << third_camera.camera_uuid << "'";    

    int ret = ptmpconn->Execute(streamsql.str()) > 0 ? 0 : -1;
    if (-1 == ret)
    {
        AK_LOG_WARN << "UpdateThirdPartyCameraVideoInfo failed, camera_uuid = " << third_camera.camera_uuid;
        ReleaseDBConn(conn);
        return ret;
    }

    ReleaseDBConn(conn);
    return 0;
}


int ThirdPartyCamrea::GetThirdPartyCameraByProjectUUID(const std::string &project_uuid, 
 ThirdPartyCamreaList &all_pub_third_camera, ThirdPartyCamreaList &pub_third_camera, 
 ThirdPartyCamreaUnitMap &unit_third_camera, ThirdPartyCamreaPerMap &per_third_camera, ThirdPartyCamreaMacMap &mac_third_camera)
{
    std::stringstream streamSQL;
    streamSQL << "select "<< camera_sec <<" from ThirdPartCamera where ProjectUUID = '" << project_uuid << "'";

    GET_DB_CONN_ERR_RETURN(conn, -1);
    
    CRldbQuery query(conn.get());
    query.Query(streamSQL.str());
    while (query.MoveToNextRow())
    {
        ThirdPartyCamreaInfo third_camera;
        GetCameraFromSql(third_camera, query);
        if(strlen(third_camera.mac) > 0)
        {
            mac_third_camera.insert(std::make_pair(third_camera.mac, third_camera));
        }
        if (third_camera.grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
        {
            per_third_camera.insert(std::make_pair(third_camera.personal_uuid, third_camera));
            continue;
        }
        if (third_camera.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
        {
            pub_third_camera.push_back(third_camera);
            all_pub_third_camera.push_back(third_camera);
            continue;
        }
        if (third_camera.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
        {
            unit_third_camera.insert(std::make_pair(third_camera.unit_id, third_camera));
            all_pub_third_camera.push_back(third_camera);
            continue;
        }        
    }
    return 0;    
}


}

