#ifndef __DB_THIRD_PARTY_CAMERA_H__
#define __DB_THIRD_PARTY_CAMERA_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include "RldbQuery.h"
#include "Rldb.h"
#include "AkcsCommonDef.h"


typedef struct ThirdPartyCamera_T
{
    char project_uuid[64];
    char camera_uuid[64];
    char personal_uuid[64];
    int unit_id;
    int grade;
    char location[64];
    char rtsp_url[128];
    char username[64];
    char passwd[36];
    char mac[32];
    int video_pt;
    char video_type[32];
    char video_fmtp[256];
    int allow_end_user_monitor;
    int monitoring_platform;

    ThirdPartyCamera_T() {
        memset(this, 0, sizeof(*this));
    }
}ThirdPartyCamreaInfo;

typedef std::vector<ThirdPartyCamreaInfo>ThirdPartyCamreaList;
typedef std::multimap<std::string/*per_uuid*/, ThirdPartyCamreaInfo>ThirdPartyCamreaPerMap;
typedef std::multimap<uint32_t/*unit_id*/, ThirdPartyCamreaInfo>ThirdPartyCamreaUnitMap;
typedef std::map<std::string/*mac*/, ThirdPartyCamreaInfo>ThirdPartyCamreaMacMap;


namespace dbinterface
{

class ThirdPartyCamrea
{
public:
    ThirdPartyCamrea();
    ~ThirdPartyCamrea();
    static int GetPubThirdPartyCameraList(const std::string &mng_uuid, ThirdPartyCamreaList &pub_camera_list);
    static int GetUnitThirdPartyCameraList(const std::string &mng_uuid, int unit_id, ThirdPartyCamreaList &unit_camera_list);
    static int GetPubandUnitThirdPartyCameraList(const std::string &mng_uuid, ThirdPartyCamreaList &pub_camera_list);
    static int GetNodeThirdPartyCameraList(const std::string &personal_uuid, ThirdPartyCamreaList &node_camera_list);
    static int GetThirdPartyCameraByMac(const std::string &mac, ThirdPartyCamreaInfo &third_camera);
    static int GetThirdPartyCameraByUUID(const std::string &uuid, ThirdPartyCamreaInfo &third_camera);
    static int UpdateThirdPartyCameraVideoInfo(ThirdPartyCamreaInfo &third_camera);

    static int GetThirdPartyCameraByProjectUUID(const std::string &project_uuid, 
         ThirdPartyCamreaList &all_pub_third_camera, ThirdPartyCamreaList &pub_third_camera, 
         ThirdPartyCamreaUnitMap &unit_third_camera, ThirdPartyCamreaPerMap &per_third_camera, ThirdPartyCamreaMacMap &mac_third_camera);    
private:
    static int GetCameraFromSql(ThirdPartyCamreaInfo &camera, CRldbQuery& query);
};

}
#endif
