#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "ThirdPartyLockAccount.h"
#include <string.h>
#include "AkLogging.h"
#include "util.h"

namespace dbinterface
{

ThirdPartyLockAccount::ThirdPartyLockAccount()
{

}

int ThirdPartyLockAccount::GetThirdPartyLockAccountByAccount(const std::string &account, ThirdPartyLockAccountInfo &third_account)
{
    std::stringstream streamSQL;
    streamSQL << "select A.Token,A.RefreshToken,A.LockType from ThirdPartyLockAccount A join PersonalAccount B \
        on A.PersonalAccountUUID = B.UUID where B.Account = '"
              << account << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    Snprintf(third_account.account, sizeof(third_account.account), account.c_str());
    if (query.MoveToNextRow())
    {
        Snprintf(third_account.token, sizeof(third_account.token), query.GetRowData(0));
        Snprintf(third_account.refresh_token, sizeof(third_account.refresh_token), query.GetRowData(1));
        third_account.lock_type = ATOI(query.GetRowData(2));
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;    
}

}

