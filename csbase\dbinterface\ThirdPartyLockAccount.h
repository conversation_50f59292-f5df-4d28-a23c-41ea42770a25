#ifndef __DB_THIRD_PARTY_LOCK_ACCOUNT_H__
#define __DB_THIRD_PARTY_LOCK_ACCOUNT_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>

namespace dbinterface
{


typedef struct ThirdPartyLockAccount_T
{
    char token[1024];//数据库字段长度
    char refresh_token[1024];
    char account[64];
    int lock_type;//第三方类型0=Qrio，1=Yale
}ThirdPartyLockAccountInfo;

class ThirdPartyLockAccount
{
public:
    ThirdPartyLockAccount();
    ~ThirdPartyLockAccount();
    static int GetThirdPartyLockAccountByAccount(const std::string &account, ThirdPartyLockAccountInfo &third_account);
private:
};

}
#endif
