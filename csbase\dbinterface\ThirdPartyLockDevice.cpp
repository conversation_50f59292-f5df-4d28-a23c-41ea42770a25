#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "ThirdPartyLockDevice.h"
#include <string.h>
#include "AkLogging.h"
#include "util.h"

namespace dbinterface
{

ThirdPartyLockDevice::ThirdPartyLockDevice()
{

}

int ThirdPartyLockDevice::GetThirdPartyLockDevlistByMac(const std::string &mac, ThirdPartyLockDevList &third_devlist)
{
    std::stringstream streamSQL;
    streamSQL << "select PersonalAccountUUID,LockType,LockName,UUID,Relay,AutoClose,LockStatus from ThirdPartyLockDevice where MAC = '"
              << mac << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    
    while (query.MoveToNextRow())
    {
        ThirdPartyLockDeviceInfo third_dev;
        Snprintf(third_dev.mac, sizeof(third_dev.mac), mac.c_str());
        Snprintf(third_dev.personal_uuid, sizeof(third_dev.personal_uuid), query.GetRowData(0));
        third_dev.lock_type = ATOI(query.GetRowData(1));
        Snprintf(third_dev.lock_name, sizeof(third_dev.lock_name), query.GetRowData(2));
        Snprintf(third_dev.dev_uuid, sizeof(third_dev.dev_uuid), query.GetRowData(3));
        third_dev.relay = ATOI(query.GetRowData(4));
        third_dev.auto_close = ATOI(query.GetRowData(5));
        third_dev.lock_status = ATOI(query.GetRowData(6));

        third_devlist.push_back(third_dev);
    }

    ReleaseDBConn(conn);
    return 0;    
}


int ThirdPartyLockDevice::GetQrioRelayByMac(const std::string &mac)
{
    std::stringstream streamSQL;
    streamSQL << "select Relay from ThirdPartyLockDevice where MAC = '"
              << mac << "' and LockType = " << ThirdPartyLockType::QRIO;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return 0;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());

    int relay = 0;
    while (query.MoveToNextRow())
    {
        relay |= ATOI(query.GetRowData(0));
    }

    ReleaseDBConn(conn);
    return relay;    
}

int ThirdPartyLockDevice::GetYaleRelayByMac(const std::string &mac)
{
    std::stringstream streamSQL;
    streamSQL << "select Relay from ThirdPartyLockDevice where MAC = '"
              << mac << "' and LockType = " << ThirdPartyLockType::YALE;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return 0;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());

    int relay = 0;
    while (query.MoveToNextRow())
    {
        relay |= ATOI(query.GetRowData(0));
    }

    ReleaseDBConn(conn);
    return relay;    
}


}

