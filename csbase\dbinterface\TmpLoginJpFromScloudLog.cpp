#include <sstream>
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/TmpLoginJpFromScloudLog.h"
#include <string.h>
#include "AkLogging.h"

namespace dbinterface{
TmpLoginJpFromScloudLog::TmpLoginJpFromScloudLog()
{

}

TmpLoginJpFromScloudLog::~TmpLoginJpFromScloudLog()
{

}

int TmpLoginJpFromScloudLog::InsertLog(const std::string    &account)
{
    std::stringstream streamsql;
    streamsql << "INSERT INTO TmpLoginJpFromScloudLog"
               << " (Account) VALUES ('"
               << account << "');";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }    
    if (tmp_conn->Execute(streamsql.str()) < 0)
    {
        ReleaseDBConn(conn);
        AK_LOG_WARN << "Failed to insert new record into db,SQL is " << streamsql.str();
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;
}





}


