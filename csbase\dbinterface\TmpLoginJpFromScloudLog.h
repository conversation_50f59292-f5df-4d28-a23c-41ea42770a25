#ifndef __DB_TMP_LOGIN_JP_FROM_SCLOUD_H__
#define __DB_TMP_LOGIN_JP_FROM_SCLOUD_H__

#include <vector>
#include <atomic>
#include <map>
#include <string>
#include <memory>
#include <tuple>
#include "BasicDefine.h"



namespace dbinterface{
class TmpLoginJpFromScloudLog
{
public:
    TmpLoginJpFromScloudLog();
    ~TmpLoginJpFromScloudLog();
    static int InsertLog(const std::string &account);

private:
};

}


#endif
