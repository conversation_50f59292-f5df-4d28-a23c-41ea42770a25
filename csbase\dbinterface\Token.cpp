#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/Token.h"
#include <string.h>
#include "AkLogging.h"
#include "util.h"

namespace dbinterface
{

static const std::string token_sec = " ID,Account,WebToken,AppToken,AlexaToken,AlexaAccessToken,AlexaReflashToken,AppTokenEt,\
    AuthCode,AppRefreshToken,RefreshTokenEt,unix_timestamp(CreateTime),AuthToken,AppMainUserAccount,EnableCallkit";


Token::Token()
{

}

void Token::GetTokenFromSql(TokenInfo &token, CRldbQuery& query)
{
    token.id = ATOI(query.GetRowData(0));
    Snprintf(token.account, sizeof(token.account), query.GetRowData(1));
    Snprintf(token.web_token, sizeof(token.web_token), query.GetRowData(2));
    Snprintf(token.app_token, sizeof(token.app_token), query.GetRowData(3));
    Snprintf(token.alexa_token, sizeof(token.alexa_token), query.GetRowData(4));
    Snprintf(token.alexa_access_token, sizeof(token.alexa_access_token), query.GetRowData(5));
    Snprintf(token.alexa_reflash_token, sizeof(token.alexa_reflash_token), query.GetRowData(6));
    token.app_tokenet = ATOI(query.GetRowData(7));
    Snprintf(token.authcode, sizeof(token.authcode), query.GetRowData(8));
    Snprintf(token.app_refresh_token, sizeof(token.app_refresh_token), query.GetRowData(9));
    token.refresh_tokenet = ATOI(query.GetRowData(10));
    token.create_time = ATOI(query.GetRowData(11));
    Snprintf(token.auth_token, sizeof(token.auth_token), query.GetRowData(12));
    Snprintf(token.app_main_account, sizeof(token.app_main_account), query.GetRowData(13));
    token.enable_callkit = ((ATOI(query.GetRowData(14)) == 1)? true : false);
    return;
}

int Token::GetTokenInfoByAppToken(const std::string &token, TokenInfo &token_info)
{
    std::stringstream streamSQL;
    streamSQL << "/*master*/ select " << token_sec << " from Token where AppToken = '"
              << token << "' limit 1;";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    if (query.MoveToNextRow())
    {
        GetTokenFromSql(token_info, query);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;    
}

int Token::GetTokenInfoByAccount(const std::string &account, TokenInfo &token_info)
{
    std::stringstream streamSQL;
    streamSQL << "/*master*/ select " << token_sec << " from Token where AppMainUserAccount = '"
              << account << "' limit 1;";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    if (query.MoveToNextRow())
    {
        GetTokenFromSql(token_info, query);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;    
}

//迁移的token续时
int Token::UpdateTokenExpireTime(const std::string& uid)
{
    std::stringstream streamsql;
    streamsql << "UPDATE Token set AppTokenEt = unix_timestamp() + 1296000 where Account=" << uid;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }    
    if (tmp_conn->Execute(streamsql.str()) < 0)
    {
        ReleaseDBConn(conn);
        AK_LOG_WARN << "Failed to update db,SQL is " << streamsql.str();
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;
}

int Token::UpdateRefreshToken(const std::string& uid, const std::string& refresh_token)
{
    std::stringstream streamsql;
    streamsql << "UPDATE Token set AppRefreshToken = '" << refresh_token << "' where Account='" << uid << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }    
    if (tmp_conn->Execute(streamsql.str()) < 0)
    {
        ReleaseDBConn(conn);
        AK_LOG_WARN << "Failed to update db,SQL is " << streamsql.str();
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;
}


int Token::InsertOrUpdateToken(const std::string& uid, const std::string& token, const std::string& main_account, int expire_time)
{
    std::stringstream streamsql;
    streamsql << "INSERT INTO Token (Account, AppToken, AppTokenEt, AppMainUserAccount) VALUES "
              << "('"<< uid << "','" << token << "','"<< expire_time<< "','" << main_account << "') "
              << "ON DUPLICATE KEY UPDATE AppToken = '" << token << "', AppTokenEt = '"<< expire_time << "', AppMainUserAccount = '" << main_account << "'";
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }    
    if (tmp_conn->Execute(streamsql.str()) < 0)
    {
        ReleaseDBConn(conn);
        AK_LOG_WARN << "Failed to insert new record into db,SQL is " << streamsql.str();
        return -1;
    }

    ReleaseDBConn(conn);
    return 0;
}

int Token::InsertOrUpdateTokenRenewInfo(const std::string& account, const std::string& main_account, const TokenRenewInfo& token_renew_info, uint32_t expire_time)
{
    std::stringstream streamsql;
    streamsql << "INSERT INTO Token (Account, AppToken, AppTokenEt, AppMainUserAccount, AppRefreshToken) VALUES "
              << "('"<< account << "','" << token_renew_info.token << "','"<< expire_time<< "','" << main_account << "', '" <<  token_renew_info.refresh_token << "') "
              << "ON DUPLICATE KEY UPDATE AppToken = '" << token_renew_info.token << "', AppTokenEt = '"<< expire_time 
              << "', AppMainUserAccount = '" << main_account  << "', AppRefreshToken = '" << token_renew_info.refresh_token << "'";

    GET_DB_CONN_ERR_RETURN(conn, -1);
    CRldb* tmp_conn = conn.get();
    if (tmp_conn->Execute(streamsql.str()) < 0)
    {
        AK_LOG_WARN << "Failed to insert new record into db,SQL is " << streamsql.str();
        return -1;
    }
    return 0;
}

int Token::InsertOrUpdateAuthToken(const std::string& user, const std::string& main_account, const std::string& token)
{
    std::stringstream streamsql;
    streamsql << "INSERT INTO Token (Account, AuthToken, AppMainUserAccount) VALUES "
              << "('"<< user << "','" << token<< "','" << main_account << "') "
              << "ON DUPLICATE KEY UPDATE AuthToken = '" << token<< "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }     
    if (tmp_conn->Execute(streamsql.str()) < 0)
    {
        ReleaseDBConn(conn);
        AK_LOG_WARN << "Failed to insert new record into db,SQL is " << streamsql.str();
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;
}

int Token::UpdateToken(int limited_time, const std::string& token, const std::string& main_account)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    std::stringstream streamsql;
    streamsql << "UPDATE Token set AppTokenEt = "
               << limited_time
               << " WHERE AppMainUserAccount = '"
               << main_account
               << "' and AppToken='"
               << token
               << "';";
    conn->Execute(streamsql.str());

    ReleaseDBConn(conn);
    return 0;
}

int Token::UpdateAppTokenAndTimeByID(const std::string token, int id)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    std::stringstream streamsql1;
    streamsql1 << "UPDATE Token set  AppToken = '"
                       << token
                       << "', CreateTime = CURRENT_TIMESTAMP() WHERE ID="
                       << id;
    conn->Execute(streamsql1.str());

    ReleaseDBConn(conn);
    return 0;
}

int Token::ClearToken(const std::string& main_account)
{
    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    
    std::stringstream streamsql;
    streamsql << "UPDATE Token set AppToken = NULL, AppRefreshToken = NULL, AuthToken = NULL"
               << " WHERE AppMainUserAccount = '"
               << main_account
               << "';";
    db_conn->Execute(streamsql.str());
    return 0;
}


}

