#ifndef __DB_TOKEN_H__
#define __DB_TOKEN_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include "RldbQuery.h"
#include "ConnectionPool.h"
#include "ConnectionManager.h"

typedef struct TOKEN_RENEW_INFO_T
{
    char token[128];
    char refresh_token[128];
    uint64_t valid_time;
    TOKEN_RENEW_INFO_T()
    {
        memset(this, 0, sizeof(*this));
    }
}TokenRenewInfo;

typedef struct Token_T
{
    int id;
    char account[32];
    char web_token[128];
    char app_token[128];
    char alexa_token[128];
    char alexa_access_token[1024];
    char alexa_reflash_token[1024];
    int app_tokenet;
    char authcode[128];
    char app_refresh_token[128];
    int refresh_tokenet;
    char auth_token[128];
    int create_time;
    char app_main_account[64];
    bool enable_callkit;
    Token_T()
    {
        memset(this, 0, sizeof(*this));
    }
}TokenInfo;

namespace dbinterface
{

class Token
{
public:
    Token();
    ~Token();
    static int GetTokenInfoByAppToken(const std::string &token, TokenInfo &token_info);
    static int GetTokenInfoByAccount(const std::string &account, TokenInfo &token_info);
    static int UpdateTokenExpireTime(const std::string& uid);
    static int InsertOrUpdateToken(const std::string& uid, const std::string& token, const std::string& main_account, int expire_time);
    static int InsertOrUpdateAuthToken(const std::string& user, const std::string& main_account, const std::string& token);
    static int UpdateToken(int limited_time, const std::string& token, const std::string& main_account);
    static int UpdateAppTokenAndTimeByID(const std::string token, int id);
    static int InsertOrUpdateTokenRenewInfo(const std::string& account, const std::string& main_account, const TokenRenewInfo& token_renew_info, uint32_t expire_time);
    static int UpdateRefreshToken(const std::string& uid, const std::string& refresh_token);
    static int ClearToken(const std::string& main_account);

private:
    static void GetTokenFromSql(TokenInfo &token, CRldbQuery& query);

};

}
#endif
