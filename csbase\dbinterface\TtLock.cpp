#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "TtLock.h"

namespace dbinterface {

static const std::string tt_lock_info_sec = " UUID,Name,LockId,DeviceUUID,Relay,PersonalAccountUUID ";

void TtLock::GetTtLockFromSql(TtLockInfo& tt_lock_info, CRldbQuery& query)
{
    Snprintf(tt_lock_info.uuid, sizeof(tt_lock_info.uuid), query.GetRowData(0));
    Snprintf(tt_lock_info.name, sizeof(tt_lock_info.name), query.GetRowData(1));
    tt_lock_info.lock_id = ATOI(query.GetRowData(2));
    Snprintf(tt_lock_info.device_uuid, sizeof(tt_lock_info.device_uuid), query.GetRowData(3));
    tt_lock_info.relay = ATOI(query.GetRowData(4));
    Snprintf(tt_lock_info.personal_account_uuid, sizeof(tt_lock_info.personal_account_uuid), query.GetRowData(5));
    return;
}

int TtLock::GetTtLockListByDeviceUUID(const std::string& device_uuid, TtLockInfoList& tt_lock_info_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << tt_lock_info_sec << " from TtLock where DeviceUUID = '" << device_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        TtLockInfo tt_lock_info;
        GetTtLockFromSql(tt_lock_info, query);
        tt_lock_info_list.push_back(tt_lock_info);
    }
    return 0;
}


}
