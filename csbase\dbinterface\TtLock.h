#ifndef __DB_TT_LOCK_H__
#define __DB_TT_LOCK_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct TtLockInfo_T
{
    char uuid[36];
    char name[256];
    int lock_id;
    char device_uuid[36];
    int relay;
    char personal_account_uuid[36];
    TtLockInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} TtLockInfo;


typedef std::vector<TtLockInfo> TtLockInfoList; 

namespace dbinterface {

class TtLock
{
public:
    static int GetTtLockListByDeviceUUID(const std::string& device_uuid, TtLockInfoList& tt_lock_info_list);

private:
    TtLock() = delete;
    ~TtLock() = delete;
    static void GetTtLockFromSql(TtLockInfo& tt_lock_info, CRldbQuery& query);
};

}
#endif
