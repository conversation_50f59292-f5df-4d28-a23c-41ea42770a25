#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "dbinterface/TwoFactorAuthIDCode.h"
#include "dbinterface/UUID.h"
#include "dbinterface/SystemSettingTable.h"

namespace dbinterface {

static const std::string two_factor_auth_id_code_info_sec = " UUID,AccountUUID,IDCode,(ExpireTime > NOW()) AS NotExpire";

void TwoFactorAuthIDCode::GetTwoFactorAuthIDCodeFromSql(TwoFactorAuthIDCodeInfo& two_factor_auth_id_code_info, CRldbQuery& query)
{
    Snprintf(two_factor_auth_id_code_info.uuid, sizeof(two_factor_auth_id_code_info.uuid), query.GetRowData(0));
    Snprintf(two_factor_auth_id_code_info.account_uuid, sizeof(two_factor_auth_id_code_info.account_uuid), query.GetRowData(1));
    Snprintf(two_factor_auth_id_code_info.id_code, sizeof(two_factor_auth_id_code_info.id_code), query.GetRowData(2));
    two_factor_auth_id_code_info.not_expired = ATOI(query.GetRowData(3));
    return;
}

int TwoFactorAuthIDCode::GetTwoFactorAuthIDCodeByAccountUserInfoUUIDAndIDCode(const std::string& account_user_info_uuid, const std::string& id_code, TwoFactorAuthIDCodeInfo& two_factor_auth_id_code_info)
{
    std::stringstream stream_sql;
    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    stream_sql << "SELECT " << two_factor_auth_id_code_info_sec 
           << " FROM TwoFactorAuthIDCode "
           << "WHERE AccountUserInfoUUID = '" << account_user_info_uuid << "' "
           << "AND IDCode = '" << id_code << "'";

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetTwoFactorAuthIDCodeFromSql(two_factor_auth_id_code_info, query);
    }
    else
    {
        AK_LOG_WARN << "get TwoFactorAuthIDCodeInfo by AccountUserInfoUUID failed, AccountUserInfoUUID = " << account_user_info_uuid 
                    <<"id_code = " << id_code;
        return -1;
    }
    return 0;
}

int TwoFactorAuthIDCode::InsertTwoFactorAuthIDCodeByAccountUserInfoUUID(const std::string& account_user_info_uuid, const std::string& id_code)
{
    std::string uuid;
    dbinterface::UUID::GenerateUUID(dbinterface::SystemSetting::GetServerTag(), uuid);
        
    std::map<std::string, std::string> str_map;
    str_map.emplace("UUID", uuid);
    str_map.emplace("AccountUserInfoUUID", account_user_info_uuid);
    str_map.emplace("IDCode", id_code);
    str_map.emplace("sql_CreateTime", "NOW()");
    str_map.emplace("sql_ExpireTime", "DATE_ADD(NOW(), INTERVAL 30 DAY)");

    std::map<std::string, int> int_map;
    std::string table_name = "TwoFactorAuthIDCode";//插入表名
    
    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    CRldb* tmp_conn = db_conn.get();
    tmp_conn->InsertData(table_name, str_map, int_map);

    return 0;
}

}