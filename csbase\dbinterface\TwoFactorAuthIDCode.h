#ifndef __DB_TWO_FACTOR_AUTH_I_D_CODE_H__
#define __DB_TWO_FACTOR_AUTH_I_D_CODE_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct TwoFactorAuthIDCodeInfo_T
{
    char uuid[36];
    char account_uuid[36];
    char id_code[36];
    int not_expired;
    TwoFactorAuthIDCodeInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} TwoFactorAuthIDCodeInfo;

namespace dbinterface {

class TwoFactorAuthIDCode
{
public:
    static int GetTwoFactorAuthIDCodeByAccountUserInfoUUIDAndIDCode(const std::string& account_user_info_uuid, const std::string& id_code, TwoFactorAuthIDCodeInfo& two_factor_auth_id_code_info);
    static int InsertTwoFactorAuthIDCodeByAccountUserInfoUUID(const std::string& account_user_info_uuid, const std::string& id_code);
private:
    TwoFactorAuthIDCode() = delete;
    ~TwoFactorAuthIDCode() = delete;
    static void GetTwoFactorAuthIDCodeFromSql(TwoFactorAuthIDCodeInfo& two_factor_auth_id_code_info, CRldbQuery& query);
};

}
#endif