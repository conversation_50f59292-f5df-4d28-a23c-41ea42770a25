#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/SystemSettingTable.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/UUID.h"
#include <string.h>
#include "AkLogging.h"
#include "util.h"
namespace dbinterface
{

UUID::UUID()
{

}

int UUID::GenerateUUID(const std::string &server_tag, std::string &uuid)
{
    std::stringstream streamSQL;
    streamSQL << "select uuid()";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    if (query.MoveToNextRow())
    {
       uuid = query.GetRowData(0);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }

    SplicingUUID(server_tag, uuid);
    
    ReleaseDBConn(conn);
    return 0;    
}

//生成的uuid: b70ef338-2f1d-11ed-bfd4-00163e047e78
//去掉'-',拼接server_tag: na-b70ef3382f1d11edbfd400163e047e78
void UUID::SplicingUUID(const std::string &server_tag, std::string& uuid)
{
    StringReplace(uuid,"-","");
    uuid = server_tag + '-' + uuid;
}

}

