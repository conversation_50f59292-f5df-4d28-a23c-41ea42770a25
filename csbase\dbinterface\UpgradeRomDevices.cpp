#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "UpgradeRomDevices.h"
#include <string.h>
#include "AkLogging.h"
#include "util.h"

namespace dbinterface
{

UpgradeRomDevices::UpgradeRomDevices()
{

}

int UpgradeRomDevices::GetWaitUpgradeList(int rom_ver_id, int &maxid, std::vector<std::string>& macs)
{
    std::stringstream streamSQL;
    streamSQL << "SELECT id, MAC FROM UpgradeRomDevices WHERE UpgradeRomVerID = "
              << rom_ver_id
              << " AND Status = "
              << UnUpgrade
              << " order by id limit 100";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());

    char mac[16] = {0};
    int num = 0;
    while (query.MoveToNextRow())
    {
        num++;
        maxid = ATOI(query.GetRowData(0));
        Snprintf(mac, sizeof(mac),  query.GetRowData(1));
        macs.push_back(mac);
    }

    ReleaseDBConn(conn);
    return num;
}

int UpgradeRomDevices::UpdateUpgradeStatus(int maxid,int rom_ver_id)
{
    std::stringstream streamsql;
    streamsql << "UPDATE UpgradeRomDevices SET Status = "
               << Upgraded
               << " WHERE ID <= "
               << maxid
               << " AND UpgradeRomVerID = "
               << rom_ver_id;
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    conn->Execute(streamsql.str());
    ReleaseDBConn(conn);
    return 0;
}


}

