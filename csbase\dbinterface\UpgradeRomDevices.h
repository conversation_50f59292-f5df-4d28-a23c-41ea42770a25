#ifndef __DB_UPGRADE_ROM_DEVICES_H__
#define __DB_UPGRADE_ROM_DEVICES_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>

namespace dbinterface
{

class UpgradeRomDevices
{
public:
    enum DevStatus
    {
        UnUpgrade = 0,
        Upgraded,
    };

    UpgradeRomDevices();
    ~UpgradeRomDevices();
    
    static int GetWaitUpgradeList(int rom_ver_id, int &maxid, std::vector<std::string>& macs);
    static int UpdateUpgradeStatus(int maxid,int rom_ver_id);
private:

};


}
#endif

