#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "UpgradeRomVersion.h"
#include <string.h>
#include "AkLogging.h"
#include "util.h"

namespace dbinterface
{

UpgradeRomVersion::UpgradeRomVersion()
{

}

int UpgradeRomVersion::GetWaitUpgradeRomID(RomIDFileMap& map)
{
    std::stringstream streamSQL;
    streamSQL << "SELECT U.ID, U.IsNeedReset, R.Url FROM UpgradeRomVersion U "
              << "LEFT JOIN RomVersion R ON U.Version = R.Version "
              << "WHERE U.Status = 0 AND U.UpdateTime <= now() limit 10";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    int id = 0;
    int is_need_reset = 0;
    char urls[1024] = {0};
    while (query.MoveToNextRow())
    {
        id = ATOI(query.GetRowData(0));
        is_need_reset = ATOI(query.GetRowData(1));
        std::pair<int,int> rom_version_pair(id, is_need_reset);
        Snprintf(urls, sizeof(urls),  query.GetRowData(2));
        map.insert(std::make_pair(rom_version_pair, urls));
    }

    ReleaseDBConn(conn);
    return 0;
}

int UpgradeRomVersion::SetUpgradeRomIDStatus(int id, int status)
{
    std::stringstream streamSQL;
    streamSQL << "UPDATE UpgradeRomVersion SET Status = "
              << status
              << " WHERE ID = "
              << id;
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    conn->Execute(streamSQL.str());
    //释放数据库连接
    ReleaseDBConn(conn);
    return 0;
}

void UpgradeRomVersion::DelUpgradeRomID(std::vector<int>& ids)
{
    std::stringstream stream_ids;
    stream_ids << ids.front();
    std::vector<int>::iterator it = ids.begin();
    it++;
    for (; it != ids.end(); ++it)
    {
        stream_ids << ",";
        stream_ids << *it;
    }
    std::stringstream streamSQL;
    streamSQL << "DELETE FROM UpgradeRomVersion WHERE ID IN ("
              //<< stream_ids //不能流直接插
              << stream_ids.str()
              << ")";
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return ;
    }
    conn->Execute(streamSQL.str());
    //释放数据库连接
    ReleaseDBConn(conn);
    return;
}




}

