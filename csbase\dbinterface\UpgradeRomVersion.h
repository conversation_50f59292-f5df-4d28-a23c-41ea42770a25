#ifndef __DB_UPGRADE_ROM_VERSION_H__
#define __DB_UPGRADE_ROM_VERSION_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>

typedef std::map<std::pair<int, int>/*UpgradeRomVerID, IsNeedReset*/, std::string/*Version*/> RomIDFileMap;
typedef RomIDFileMap::iterator RomIDFileMapIter;


namespace dbinterface
{

class UpgradeRomVersion
{
public:
    UpgradeRomVersion();
    ~UpgradeRomVersion();
    
    static int GetWaitUpgradeRomID(RomIDFileMap& map);
    static int SetUpgradeRomIDStatus(int id, int status);
    static void DelUpgradeRomID(std::vector<int>& ids);
private:

};


}
#endif

