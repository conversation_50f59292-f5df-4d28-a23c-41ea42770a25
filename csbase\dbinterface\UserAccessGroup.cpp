#include <sstream>
#include <string.h>
#include "util.h"
#include "Rldb.h"
#include "AkLogging.h"
#include "RldbQuery.h"
#include "UserAccessGroup.h"
#include "dbinterface/DataConfusion.h"
#include "dbinterface/InterfaceComm.h"
#include "ConnectionManager.h"


namespace dbinterface
{


UserAccessGroup::UserAccessGroup()
{

}

/*获取租户设备包含的权限组*/
void UserAccessGroup::GetUserMacAccessGroupList(DEVICE_SETTING* dev, AccessGroupInfoPtrList &list)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }

    std::stringstream str_sql;
    str_sql << "select A.ID,A.SchedulerType,<PERSON>.<PERSON>,A.BeginTime,"
        << "A.EndTime,A.StartTime,A.StopTime,<PERSON><PERSON>,DATE_FORMAT(A.BeginTime,'%Y%m%d') as daystart,DATE_FORMAT(A.EndTime,'%Y%m%d') as dayend, "
        << " A.Account, D.SecurityRelay From UserAccessGroup A left join UserAccessGroupDevice D on A.ID=D.UserAccessGroupID where D.Mac='" << dev->mac << "'";
           
    CRldbQuery query(tmp_conn);
    query.Query(str_sql.str());
    while (query.MoveToNextRow())
    {
        AccessGroupInfoPtr ag = std::make_shared<AccessGroupInfo>();
        ag->id_ = ATOI(query.GetRowData(0));
        ag->scheduler_type_ = ATOI(query.GetRowData(1));
        ag->date_flag_ = ATOI(query.GetRowData(2));
        Snprintf(ag->day_start_, sizeof(ag->day_start_), query.GetRowData(3));
        Snprintf(ag->day_end_, sizeof(ag->day_end_), query.GetRowData(4));
        Snprintf(ag->time_start_, sizeof(ag->time_start_), query.GetRowData(5));
        Snprintf(ag->time_end_, sizeof(ag->time_end_), query.GetRowData(6));        
        ag->relay_ = ATOI(query.GetRowData(7));
        Snprintf(ag->day_start_for_ymd_, sizeof(ag->day_start_for_ymd_), query.GetRowData(8));
        Snprintf(ag->day_end_for_ymd_, sizeof(ag->day_end_for_ymd_), query.GetRowData(9));
        Snprintf(ag->name_, sizeof(ag->name_), query.GetRowData(10));
        ag->security_relay_ = ATOI(query.GetRowData(11));
        list.push_back(ag);
    }
    ReleaseDBConn(conn);    
}

int UserAccessGroup::GetDeviceHoldDoorList(DEVICE_SETTING* dev, HoldDoorInfoPtrList &list)
{
    std::stringstream stream_sql;
    stream_sql << "select Max(UserAccessGroupID) From UserAccessGroupDevice where MAC='" << dev->mac << "'";
    GET_DB_CONN_ERR_RETURN(conn, -1);
    CRldbQuery query(conn.get());

    query.Query(stream_sql.str());
    int latest_user_access_group_id = 0;
    if (query.MoveToNextRow()) {
        latest_user_access_group_id = ATOI(query.GetRowData(0));
    }
    
    std::stringstream door_stream_sql;
    door_stream_sql << "SELECT ID, StartTime, EndTime, Relay, "
                << "DATE_FORMAT(StartTime, '%Y%m%d') AS day_start, "
                << "DATE_FORMAT(EndTime, '%Y%m%d') AS day_end, "
                << "DATE_FORMAT(StartTime, '%H:%i:%s') AS time_start, "
                << "DATE_FORMAT(EndTime, '%H:%i:%s') AS time_end "
                << "FROM HoldDoor "
                << "WHERE DevicesUUID='" << dev->uuid << "' "
                << "AND EndManually = 0";

    query.Query(door_stream_sql.str());
    while (query.MoveToNextRow())
    {
        HoldDoorInfoPtr hd = std::make_shared<HoldDoorInfo>();
        hd->latest_user_access_group_id = latest_user_access_group_id + ATOI(query.GetRowData(0)); // 使用最后一条权限组ID + 对应HoodDoor ID确保唯一性
        Snprintf(hd->day_start_, sizeof(hd->day_start_), query.GetRowData(1));
        Snprintf(hd->day_end_, sizeof(hd->day_end_), query.GetRowData(2));
        hd->relay = ATOI(query.GetRowData(3));
        Snprintf(hd->day_start_for_ymd_, sizeof(hd->day_start_for_ymd_), query.GetRowData(4));
        Snprintf(hd->day_end_for_ymd_, sizeof(hd->day_end_for_ymd_), query.GetRowData(5));
        Snprintf(hd->time_start_, sizeof(hd->time_start_), query.GetRowData(6));
        Snprintf(hd->time_end_, sizeof(hd->time_end_), query.GetRowData(7));

        list.push_back(hd);
    }
    return 0;
}

void UserAccessGroup::GetUserAccessGroupDevList(const std::vector<std::string> &userlist, std::set<std::string> &mac_set)
{
    int size = userlist.size();
    if (size <= 0 )
    {
        return;
    }

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }

    std::string accounts = ListToSeparatedFormatString(userlist);
    std::stringstream stream_sql;
    stream_sql << "select mac from UserAccessGroupDevice  where UserAccessGroupID in "
        << "(select ID from UserAccessGroup where Account in(" << accounts << "))";
    
    CRldbQuery query(tmp_conn);
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        mac_set.insert(query.GetRowData(0));
    }
   
    ReleaseDBConn(conn);
}

//获取个人权限组设备和室内机列表
void UserAccessGroup::GetValidPerAccessGroupDevices(const std::string &node, std::set<std::string> &mac_set)
{
    AccessGroupInfo ag_info;
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }
    CRldbQuery query(tmp_conn);

    std::stringstream stream_sql1;
    stream_sql1 << "select ID,SchedulerType,BeginTime,EndTime from UserAccessGroup where Account = '" << node << "'";
    query.Query(stream_sql1.str());
    
    if (query.MoveToNextRow())
    {
        ag_info.id_ = ATOI(query.GetRowData(0));
        ag_info.scheduler_type_ = ATOI(query.GetRowData(1));
        Snprintf(ag_info.day_start_, sizeof(ag_info.day_start_), query.GetRowData(2));
        Snprintf(ag_info.day_end_, sizeof(ag_info.day_end_), query.GetRowData(3));
    }
    
    int access_group_id = ag_info.id_;
    std::stringstream stream_sql2;
    stream_sql2 << "select mac from UserAccessGroupDevice where UserAccessGroupID = '" << access_group_id << "'" ;
    query.Query(stream_sql2.str());
    while (query.MoveToNextRow())
    {
       mac_set.insert(query.GetRowData(0));
    }  
    ReleaseDBConn(conn);
    //查出室内机
    dbinterface::ResidentDevices::GetNodesIndoorOrMngDevList(node, mac_set);
}

void UserAccessGroup::GetPerDevAccountListByAccessGroupID(uint id, UserAccessNodeList &list)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }

    std::stringstream str_sql;
    str_sql << "select P.Name,P.Account,P.Version,P.ID,P.Role,P.UnitID,P.UUID From UserAccessGroup A left join PersonalAccount P "
        << "on P.Account=A.Account where A.ID=" << id << " order by A.ID";

    CRldbQuery query(tmp_conn);
    query.Query(str_sql.str());
    while (query.MoveToNextRow())
    {
        UserAccessNode ua;
        ua.ag_id = id;
        Snprintf(ua.name, sizeof(ua.name), dbinterface::DataConfusion::Decrypt(query.GetRowData(0)).c_str());
        Snprintf(ua.uuid, sizeof(ua.uuid), query.GetRowData(1));
        Snprintf(ua.meta, sizeof(ua.meta), query.GetRowData(2));
        ua.dbid = ATOI(query.GetRowData(3));
        ua.role = ATOI(query.GetRowData(4));
        ua.unit_id = ATOI(query.GetRowData(5));
        Snprintf(ua.db_uuid, sizeof(ua.db_uuid), query.GetRowData(6));
        list.push_back(ua);
    }
    ReleaseDBConn(conn);
}

void UserAccessGroup::GetAccountAccessPerDevMapByCommunityID(uint community_id, UserAccessAccountMacMap &account_mac_list)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }

    std::stringstream str_sql;
    str_sql << "select A.Account,D.Mac from UserAccessGroup A left join UserAccessGroupDevice D on A.ID=D.UserAccessGroupID "
        << " left join Devices DD on DD.Mac=D.Mac  where DD.MngAccountID=" << community_id;      


    CRldbQuery query(tmp_conn);
    query.Query(str_sql.str());
    while (query.MoveToNextRow())
    {
        std::string account = query.GetRowData(0);
        std::string mac = query.GetRowData(1);
        account_mac_list.insert(std::make_pair(account, mac));
    }
    ReleaseDBConn(conn);
}





}

