#ifndef __DB_USER_ACCESS_GROUP_H__
#define __DB_USER_ACCESS_GROUP_H__
#include <string>
#include <memory>
#include <vector>
#include <set>
#include <stdint.h>
#include "AkcsCommonDef.h"
#include "AkcsCommonSt.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "AccessGroupDB.h"


typedef struct HoldDoorInfo_T
{
    int latest_user_access_group_id;
    int relay;
    char day_start_[32];
    char day_end_[32];
    char day_start_for_ymd_[32];//只取日期
    char day_end_for_ymd_[32];//只取日期
    char time_start_[32];
    char time_end_[32];


    HoldDoorInfo_T() {
        memset(this, 0, sizeof(*this));
    }  
    
}HoldDoorInfo;

typedef std::shared_ptr<HoldDoorInfo> HoldDoorInfoPtr;
typedef std::list<HoldDoorInfoPtr> HoldDoorInfoPtrList;

using UserAccessAccountMacMap = std::multimap<std::string/*account*/, std::string/*mac*/>;

namespace dbinterface
{

class UserAccessGroup
{
public:
    UserAccessGroup();
    ~UserAccessGroup();

    static void GetUserMacAccessGroupList(DEVICE_SETTING* dev, AccessGroupInfoPtrList &list);
    static void GetUserAccessGroupDevList(const std::vector<std::string> &userlist, std::set<std::string> &mac_set);
    static void GetValidPerAccessGroupDevices(const std::string &node, std::set<std::string> &mac_set);
    static void GetPerDevAccountListByAccessGroupID(uint id, UserAccessNodeList &list);
    static void GetAccountAccessPerDevMapByCommunityID(uint community_id, UserAccessAccountMacMap &list);
    static int  GetDeviceHoldDoorList(DEVICE_SETTING* dev, HoldDoorInfoPtrList &list);

private:
    
};


}





#endif
