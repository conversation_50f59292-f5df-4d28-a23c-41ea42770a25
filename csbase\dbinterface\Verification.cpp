#include <sstream>
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/InterfaceComm.h"
#include "Verification.h"
#include <string.h>
#include "AkLogging.h"

namespace dbinterface{
VerificationCode::VerificationCode()
{

}

VerificationCode::~VerificationCode()
{

}

int VerificationCode::GetVerificationCode(const std::string &uid, VerificationPtr &code_info)
{
    code_info = std::make_shared<Verification>();
    std::stringstream streamSQL;
    streamSQL << "SELECT Code,(unix_timestamp(CreateTime) + 300) < unix_timestamp(now()) as CodeExpire FROM  VerificationCode WHERE Account = '"
              << uid
              << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(ptmpconn);

    query.Query(streamSQL.str());
    if (query.MoveToNextRow())
    {
        code_info->code = query.GetRowData(0);
        code_info->is_expire = ATOI(query.GetRowData(1));
    }
    ReleaseDBConn(conn);
    return 0;
}

int VerificationCode::DeleteVerificationCode(const std::string &uid)
{
    std::stringstream streamSQL1;   //登录成功后验证码不能二次使用
    streamSQL1 << "DELETE FROM VerificationCode WHERE Account = '"
                       << uid
                       << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    conn->Execute(streamSQL1.str());
    ReleaseDBConn(conn);
    return 0;
}

int VerificationCode::UpdateSmsCode(const std::string& account, const std::string& code)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    AK_LOG_INFO << "update sms code. " << " account=" << account << " code=" << code;
    char sql[1024] = "";
    snprintf(sql, sizeof(sql), "insert into VerificationCode (Account,Code) values ('%s','%s') \
        ON DUPLICATE KEY update Code = '%s', CreateTime = now()", account.c_str(), code.c_str(), code.c_str());
    if (conn->Execute(sql) < 0)
    {
        ReleaseDBConn(conn);
        AK_LOG_WARN << "Failed to update db,SQL is " << sql;
        return -1;
    }
     ReleaseDBConn(conn);
    return 0;
}

}


