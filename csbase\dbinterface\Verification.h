#ifndef _VERIFICATION_H__
#define _VERIFICATION_H__
#include <string>
#include <memory>
#include <tuple>

typedef struct Verification_T
{
    std::string code;
    int is_expire;
    
}Verification;

typedef std::shared_ptr<Verification> VerificationPtr;

namespace dbinterface{
class VerificationCode
{
public:
    VerificationCode();
    ~VerificationCode();
    static int GetVerificationCode(const std::string &uid, VerificationPtr &code_info);
    static int UpdateSmsCode(const std::string& account, const std::string& code);
    static int DeleteVerificationCode(const std::string &uid);

private:
};

}


#endif
