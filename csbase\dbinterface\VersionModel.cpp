#include <sstream>
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/InterfaceComm.h"
#include "VersionModel.h"
#include <string.h>
#include "AkLogging.h"
#include "ConnectionManager.h"

namespace dbinterface{
VersionModel::VersionModel()
{

}

VersionModel::~VersionModel()
{

}

int VersionModel::GetDeviceType(const std::string &firm_ware)
{
    RldbPtr dbconn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = dbconn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    size_t pos = firm_ware.find(".");
    if (pos == std::string::npos)
    {
        ReleaseDBConn(dbconn);
        return -1;
    }
    std::string version_number = firm_ware.substr(0, pos);

    CRldbQuery query(tmp_conn);

    std::stringstream sql2;
    sql2 << "select Type from VersionModel where VersionNumber = " << version_number;

    int type = -1;
    query.Query(sql2.str());
    if (query.MoveToNextRow())
    {
        type = ATOI(query.GetRowData(0));
    }
    ReleaseDBConn(dbconn);

    return type;
}

//add bu xuzr,比对mac和devicesSpecial表中的mac，存在则将devices表中flag第8位置为1。
bool VersionModel::CheckIndoorPlanFlags(const std::string &mac, int firmware_number, int is_single)
{
    int flags = 0;

    RldbPtr dbconn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = dbconn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return false;
    }

    CRldbQuery query(tmp_conn);

    std::stringstream sql1;
    std::stringstream sql2;
    std::stringstream sql3;  
     
    sql1 << "select ID from DevicesSpecial where MAC = '" << mac << "'";

    query.Query(sql1.str());
    if (query.MoveToNextRow())
    {
        sql2 << "select Type from VersionModel where VersionNumber = " << firmware_number << " and IsIndoorPlanDevice = 1";
        query.Query(sql2.str());
        if (query.MoveToNextRow())
        {
            if (ATOI(query.GetRowData(0)) != 2)
            {
                ReleaseDBConn(dbconn);
            	return false;
            }
        } 
        else
        {
            ReleaseDBConn(dbconn);
            return false;
        }
        if (is_single)
        {
            //按位标识 0位=home;1=away;2=sleep;3=管理机是否开启全选,默认开启; 4-7设备relay;8位室内机上线标识
            sql3 << "update PersonalDevices set Flags = Flags | (1 << 8) ";
            sql3 << "where MAC = '";
            sql3 << mac;
            sql3 << "'";
        }
        else
        {
            //按位标识 0位=home;1=away;2=sleep;3=管理机是否开启全选,默认开启; 4-7设备relay;8位室内机上线标识
            sql3 << "update Devices set Flags = Flags | (1 << 8) ";
            sql3 << "where MAC = '";
            sql3 << mac;
            sql3 << "'";
        }

        if (dbconn->Execute(sql3.str()) < 0)
        {
            AK_LOG_WARN << "update devices flags failed, flags = " << flags;
            ReleaseDBConn(dbconn);
            return false;
        }	
    }

    ReleaseDBConn(dbconn);
    return true;
	
}

int VersionModel::GetEmergencyControlList(FirmwareList& firmware_list)
{
    std::stringstream sql;
    RldbPtr dbconn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = dbconn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    CRldbQuery query(tmp_conn);
    sql << "select VersionNumber from VersionModel where IsOpenAllDoors = 1";
    query.Query(sql.str());

    while (query.MoveToNextRow())
    {
        firmware_list.insert(ATOI(query.GetRowData(0)));
    }

    ReleaseDBConn(dbconn);
    return 0;
}

int VersionModel::GetNoMonitorList(FirmwareList& firmware_list)
{
    std::stringstream sql;
    RldbPtr dbconn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = dbconn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    CRldbQuery query(tmp_conn);
    sql << "select VersionNumber from VersionModel where IsNoMonitor = 1";
    query.Query(sql.str());

    while (query.MoveToNextRow())
    {
        firmware_list.insert(ATOI(query.GetRowData(0)));
    }

    ReleaseDBConn(dbconn);
    return 0;
}

int VersionModel::GetPackageDetectionList(FirmwareList& firmware_list)
{
    std::stringstream sql;
    sql << "select VersionNumber from VersionModel where IsPackageDetection = 1";
    GET_DB_CONN_ERR_RETURN(conn, -1);
    CRldbQuery query(conn.get());
    query.Query(sql.str());

    while (query.MoveToNextRow())
    {
        firmware_list.insert(ATOI(query.GetRowData(0)));
    }
    return 0;
}

int VersionModel::GetHighResolutionList(FirmwareList& firmware_list)
{
    std::stringstream sql;
    sql << "select VersionNumber from VersionModel where IsHighResolutionMonitoring = 1";
    GET_DB_CONN_ERR_RETURN(conn, -1);
    CRldbQuery query(conn.get());
    query.Query(sql.str());

    while (query.MoveToNextRow())
    {
        firmware_list.insert(ATOI(query.GetRowData(0)));
    }
    return 0;
}

void VersionModel::GetHighendDevList(FirmwareList& firmware_list)
{
    std::stringstream sql;
    sql << "select VersionNumber from VersionModel where IsHighEndDevice = 1";
    GET_DB_CONN_ERR_RETURN_VOID(conn);
    CRldbQuery query(conn.get());
    query.Query(sql.str());

    while (query.MoveToNextRow())
    {
        firmware_list.insert(ATOI(query.GetRowData(0)));
    }
    return;
}

// int VersionModel::GetSoundDetectionList(FirmwareList& firmware_list)
// {
//     std::stringstream sql;
//     sql << "select VersionNumber from VersionModel where IsSoundDetection = 1";
//     GET_DB_CONN_ERR_RETURN(conn, -1);
//     CRldbQuery query(conn.get());
//     query.Query(sql.str());

//     while (query.MoveToNextRow())
//     {
//         firmware_list.insert(ATOI(query.GetRowData(0)));
//     }
//     return 0;
// }

}


