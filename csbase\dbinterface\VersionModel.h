#ifndef __VERSION_MODEL_H__
#define __VERSION_MODEL_H__
#include <string>
#include <memory>
#include <tuple>
#include <set>


typedef std::set<int> FirmwareList;

namespace dbinterface{
class VersionModel
{
public:
    VersionModel();
    ~VersionModel();
    static int GetDeviceType(const std::string &firm_ware);
    static bool CheckIndoorPlanFlags(const std::string &mac, int firmware_number, int is_single);
    static int GetEmergencyControlList(FirmwareList& firmware_list);
    static int GetNoMonitorList(FirmwareList& firmware_list);
    static int GetPackageDetectionList(FirmwareList& firmware_list);
    static int GetHighResolutionList(FirmwareList& firmware_list);
    static void GetHighendDevList(FirmwareList& firmware_list);
    // static int GetSoundDetectionList(FirmwareList& firmware_list);

private:
};

}


#endif
