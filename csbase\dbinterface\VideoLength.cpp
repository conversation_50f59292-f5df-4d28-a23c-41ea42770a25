#include <sstream>
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/InterfaceComm.h"
#include "VideoLength.h"
#include <string.h>
#include "AkLogging.h"

namespace dbinterface{
VideoLength::VideoLength()
{

}

VideoLength::~VideoLength()
{

}

int VideoLength::UpdateVideoStorageLength(const std::string& node)
{
    std::stringstream streamSQL;
    streamSQL << "update VideoLength set VideoLength=VideoLength+30 where Node = '"
              << node
              << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    conn->Execute(streamSQL.str());
    //释放数据库连接
    ReleaseDBConn(conn);
    return 0;
}

int VideoLength::GetVideoStorageTime(const std::string& node)
{
    std::stringstream streamSQL;
    streamSQL << "select VideoStorageTime from VideoLength where Node = '"
              << node
              << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    uint32_t time = 0;
    if (query.MoveToNextRow())
    {
        time = ATOI(query.GetRowData(0));
    }
    ReleaseDBConn(conn);
    return time;
}

bool VideoLength::IsSpaceAvailabe(const std::string& node)
{
    std::stringstream streamSQL;
    streamSQL << "select VideoCap,VideoLength from VideoLength where Node = '"
              << node
              << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return false;
    }
    CRldbQuery query(tmp_conn);
    query.Query(streamSQL.str());
    uint32_t video_cap = 0;
    uint32_t video_len = 0;
    bool ret = false;
    if (query.MoveToNextRow())
    {
        video_cap = ATOI(query.GetRowData(0));
        video_len = ATOI(query.GetRowData(1));
        ret = ((video_cap > video_len) && (video_cap - video_len > 30)) ? true : false;
    }
    ReleaseDBConn(conn);
    return ret;
}



}


