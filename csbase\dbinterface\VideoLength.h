#ifndef _DB_VIDEO_LENGTH_H_
#define _DB_VIDEO_LENGTH_H_

#include <string>
#include <memory>
#include <tuple>
#include "BasicDefine.h"
#include <vector>
#include <atomic>
#include <map>

namespace dbinterface{
class VideoLength
{
public:
    VideoLength();
    ~VideoLength();
    static int UpdateVideoStorageLength(const std::string& node);
    static int GetVideoStorageTime(const std::string& node);
    static bool IsSpaceAvailabe(const std::string& node);
private:
};

}


#endif
