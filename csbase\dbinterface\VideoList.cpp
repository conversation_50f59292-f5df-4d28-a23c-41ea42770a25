#include <sstream>
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/InterfaceComm.h"
#include "VideoList.h"
#include <string.h>
#include "AkLogging.h"

namespace dbinterface{
VideoList::VideoList()
{

}

VideoList::~VideoList()
{

}


int VideoList::AddVideoRecord(const std::string& node, const std::string& mac, const std::string& uri,
                                  const uint32_t global_video_id, int video_length)
{
    char sql[1024] = {0};
    ::snprintf(sql, 1024, "insert into VideoList(Node,MAC,VideoLength,VideoTime,VideoUri,VideoUid) values('%s','%s',%d,now(),'%s',%d)",
               node.c_str(),
               mac.c_str(),
               video_length,
               uri.c_str(),
               global_video_id);
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    std::string sql2 = sql;
    int ret = conn->Execute(sql2) > 0 ? 0 : -1;

    //释放数据库连接
    ReleaseDBConn(conn);
    return ret;
}


int VideoList::DelVideoRecord(uint32_t time, std::vector<uint32_t>& vids, const std::string& node, int video_length)
{
    char sql[1024] = {0};
    ::snprintf(sql, 1024, "select ID,VideoUid from VideoList where Node = %s and ((unix_timestamp(now()) - unix_timestamp(VideoTime)) > %d);",
             node.c_str(), time);
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    std::string sql2 = sql;
    CRldbQuery query(tmp_conn);
    query.Query(sql2);
    uint32_t id = 0;
    uint32_t vid = 0;
    std::vector<uint32_t> ids;
    while (query.MoveToNextRow())
    {
        id = ATOI(query.GetRowData(0));
        vid = ATOI(query.GetRowData(1));
        ids.push_back(id);
        vids.push_back(vid);
    }
    if (ids.empty())
    {
        AK_LOG_INFO << "There is no video record timeout in node " << node;
        ReleaseDBConn(conn);
        return -1;
    }
    //删除掉这些id
    std::stringstream streamIds;
    streamIds << ids.front();
    std::vector<uint32_t>::iterator it = ids.begin();
    it++;
    for (; it != ids.end(); ++it)
    {
        streamIds << ",";
        streamIds << *it;
    }
    std::stringstream streamSQL;
    streamSQL << "DELETE FROM VideoList WHERE ID IN ("
            << streamIds.str()
            << ")";
    int num = conn->Execute(streamSQL.str());
    //更新视频时长
    if (num > 0)
    {
        int video_time = num * video_length;
        std::stringstream streamSQL2;
        streamSQL2 << "update VideoLength set VideoLength=VideoLength-" << video_time << " where Node = '"
                 << node
                 << "'";
        conn->Execute(streamSQL2.str());
    }
    ReleaseDBConn(conn);
    return 0;
}


}


