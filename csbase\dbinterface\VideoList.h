#ifndef _DB_VIDEO_LIST_H_
#define _DB_VIDEO_LIST_H_

#include <string>
#include <memory>
#include <tuple>
#include "BasicDefine.h"
#include <vector>
#include <atomic>
#include <map>

namespace dbinterface{
class VideoList
{
public:
    VideoList();
    ~VideoList();
    static int AddVideoRecord(const std::string& node, const std::string& mac,
                          const std::string& uri, const uint32_t global_video_id, int video_length);
    static int DelVideoRecord(uint32_t time, std::vector<uint32_t>& vids, const std::string& node, int video_length);
private:
};

}


#endif
