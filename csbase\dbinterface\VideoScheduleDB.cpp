#include <sstream>
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/InterfaceComm.h"
#include "VideoScheduleDB.h"
#include <string.h>
#include "AkLogging.h"
#include "util.h"

namespace dbinterface{
VideoSchedule::VideoSchedule()
{

}

VideoSchedule::~VideoSchedule()
{

}

int VideoSchedule::GetVideoScheduleInfo(int type, VideoScheduleList& video_schedule_list)
{
    std::stringstream stream_sql;
    stream_sql << "SELECT ID,MAC,StartDay,StopDay,StartTime,StopTime,DateFlag FROM VideoSchedule WHERE SchedulerType = "
              << type;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed";
        return -1;
    }

    CRldbQuery query(tmp_conn);
    query.Query(stream_sql.str());

    while (query.MoveToNextRow())
    {
        VideoScheduleInfo video_schedule_info;
        video_schedule_info.id = ATOI(query.GetRowData(0));
        Snprintf(video_schedule_info.mac, sizeof(video_schedule_info.mac), query.GetRowData(1));
        Snprintf(video_schedule_info.start_day, sizeof(video_schedule_info.start_day), query.GetRowData(2));
        Snprintf(video_schedule_info.stop_day, sizeof(video_schedule_info.stop_day), query.GetRowData(3));
        Snprintf(video_schedule_info.start_time, sizeof(video_schedule_info.start_time), query.GetRowData(4));
        Snprintf(video_schedule_info.stop_time, sizeof(video_schedule_info.stop_time), query.GetRowData(5));
        video_schedule_info.date_flag = ATOI(query.GetRowData(6));
        video_schedule_list.push_back(video_schedule_info);
    }

    ReleaseDBConn(conn);
    return 0;
}

//TODO 设置一次性视频录制定时器状态
void VideoSchedule::SetSchedStatus(int id)
{
    //注意当当前时间大于过期时间时，就要从容器中剔除并设置数据库该条计划过期.
    std::stringstream streamSQL;
    streamSQL << "UPDATE VideoSchedule SET Status = "
              << 1
              << " WHERE ID = "
              << id;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return ;
    }
    conn->Execute(streamSQL.str());
    //释放数据库连接
    ReleaseDBConn(conn);
    return;
}



}


