#ifndef __DB_VIDEO_SCHEDULE_H__
#define __DB_VIDEO_SCHEDULE_H__

#include <vector>
#include <atomic>
#include <map>
#include <string>
#include <memory>
#include <tuple>
#include "BasicDefine.h"

struct VideoScheduleInfo
{
    int id;
    char mac[20];
    char start_day[16];
    char stop_day[16];
    char start_time[16];
    char stop_time[16];
    int date_flag;
};

typedef std::vector<VideoScheduleInfo>VideoScheduleList;


namespace dbinterface{
class VideoSchedule
{
public:
    VideoSchedule();
    ~VideoSchedule();
    static int GetVideoScheduleInfo(int type, VideoScheduleList& video_schedule_list);
    static void SetSchedStatus(int id);
private:
};

}


#endif
