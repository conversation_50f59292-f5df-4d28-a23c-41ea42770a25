#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "VideoStorage.h"

namespace dbinterface {

static const std::string video_storage_info_sec = " UUID,InstallerUUID,AccountUUID,PersonalAccountUUID,ProjectType,IsAllDevice,IsTrialed,IsEnable,PlanType,StorageDays,DevicesLimitNum,IsEnableCallAudio,ExpireTime ";

void VideoStorage::GetVideoStorageFromSql(VideoStorageInfo& video_storage_info, CRldbQuery& query)
{
    Snprintf(video_storage_info.uuid, sizeof(video_storage_info.uuid), query.GetRowData(0));
    Snprintf(video_storage_info.installer_uuid, sizeof(video_storage_info.installer_uuid), query.GetRowData(1));
    Snprintf(video_storage_info.account_uuid, sizeof(video_storage_info.account_uuid), query.GetRowData(2));
    Snprintf(video_storage_info.personal_account_uuid, sizeof(video_storage_info.personal_account_uuid), query.GetRowData(3));
    video_storage_info.project_type = ATOI(query.GetRowData(4));
    video_storage_info.is_all_device = ATOI(query.GetRowData(5));
    video_storage_info.is_trialed = ATOI(query.GetRowData(6));
    video_storage_info.is_enable = ATOI(query.GetRowData(7));
    video_storage_info.plan_type = ATOI(query.GetRowData(8));
    video_storage_info.storage_days = ATOI(query.GetRowData(9));
    video_storage_info.devices_limit_num = ATOI(query.GetRowData(10));
    video_storage_info.is_enable_call_audio = ATOI(query.GetRowData(11));
    Snprintf(video_storage_info.expire_time, sizeof(video_storage_info.expire_time), query.GetRowData(12));
    return;
}

int VideoStorage::GetVideoStorageByUUID(const std::string& uuid, VideoStorageInfo& video_storage_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << video_storage_info_sec << " from VideoStorage where UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetVideoStorageFromSql(video_storage_info, query);
    }
    else
    {
        AK_LOG_WARN << "get VideoStorageInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}

int VideoStorage::GetVideoStorageByAccountUUID(const std::string& account_uuid, VideoStorageInfo& video_storage_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << video_storage_info_sec << " from VideoStorage where AccountUUID = '" << account_uuid << "' and IsEnable = 1 and ExpireTime > now()";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetVideoStorageFromSql(video_storage_info, query);
    }
    else
    {
        AK_LOG_WARN << "get VideoStorageInfo by AccountUUID failed, AccountUUID = " << account_uuid;
        return -1;
    }
    return 0;
}

int VideoStorage::GetVideoStorageByPersonalAccountUUID(const std::string& personal_account_uuid, VideoStorageInfo& video_storage_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << video_storage_info_sec << " from VideoStorage where PersonalAccountUUID = '" << personal_account_uuid << "' and IsEnable = 1 and ExpireTime > now()";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetVideoStorageFromSql(video_storage_info, query);
    }
    else
    {
        AK_LOG_WARN << "get VideoStorageInfo by PersonalAccountUUID failed, PersonalAccountUUID = " << personal_account_uuid;
        return -1;
    }
    return 0;
}


}