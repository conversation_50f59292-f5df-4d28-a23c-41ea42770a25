#ifndef __DB_VIDEO_STORAGE_H__
#define __DB_VIDEO_STORAGE_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

enum VideoStorageBelongType {
    VIDEO_STORAGE_BELONG_TYPE_PROJECT = 1,
    VIDEO_STORAGE_BELONG_TYPE_PERSONAL = 2,
};

typedef struct VideoStorageInfo_T
{
    char uuid[36];
    char installer_uuid[36];
    char account_uuid[36];
    char personal_account_uuid[36];
    int project_type;
    int is_all_device;
    int is_trialed;
    int is_enable;
    int plan_type;
    int storage_days;
    int devices_limit_num;
    int is_enable_call_audio;
    char expire_time[32];
    VideoStorageBelongType belong_type;
    VideoStorageInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} VideoStorageInfo;

typedef std::vector<VideoStorageInfo> VideoStorageInfoList;

namespace dbinterface {

class VideoStorage
{
public:
    static int GetVideoStorageByUUID(const std::string& uuid, VideoStorageInfo& video_storage_info);
    static int GetVideoStorageByAccountUUID(const std::string& account_uuid, VideoStorageInfo& video_storage_info);
    static int GetVideoStorageByPersonalAccountUUID(const std::string& personal_account_uuid, VideoStorageInfo& video_storage_info);
    static int GetVideoStorageListByAccountUUID(const std::string& account_uuid, VideoStorageInfoList& video_storage_info_list);
private:
    VideoStorage() = delete;
    ~VideoStorage() = delete;
    static void GetVideoStorageFromSql(VideoStorageInfo& video_storage_info, CRldbQuery& query);
};

}
#endif