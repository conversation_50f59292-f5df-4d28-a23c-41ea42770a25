#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "VideoStorageDevice.h"

namespace dbinterface {

static const std::string video_storage_device_info_sec = " UUID,InstallerUUID,AccountUUID,PersonalAccountUUID,VideoStorageUUID,DevicesUUID ";

void VideoStorageDevice::GetVideoStorageDeviceFromSql(VideoStorageDeviceInfo& video_storage_device_info, CRldbQuery& query)
{
    Snprintf(video_storage_device_info.uuid, sizeof(video_storage_device_info.uuid), query.GetRowData(0));
    Snprintf(video_storage_device_info.installer_uuid, sizeof(video_storage_device_info.installer_uuid), query.GetRowData(1));
    Snprintf(video_storage_device_info.account_uuid, sizeof(video_storage_device_info.account_uuid), query.GetRowData(2));
    Snprintf(video_storage_device_info.personal_account_uuid, sizeof(video_storage_device_info.personal_account_uuid), query.GetRowData(3));
    Snprintf(video_storage_device_info.video_storage_uuid, sizeof(video_storage_device_info.video_storage_uuid), query.GetRowData(4));
    Snprintf(video_storage_device_info.devices_uuid, sizeof(video_storage_device_info.devices_uuid), query.GetRowData(5));
    return;
}

DatabaseExistenceStatus VideoStorageDevice::GetVideoStorageDeviceInfo(const std::string& devices_uuid, VideoStorageDeviceInfo& video_storage_device_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << video_storage_device_info_sec << " from VideoStorageDevice where DevicesUUID = '" << devices_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, DatabaseExistenceStatus::QUERY_ERROR);
    
    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetVideoStorageDeviceFromSql(video_storage_device_info, query);
    }
    else
    {
        AK_LOG_WARN << "get VideoStorageDeviceInfo by DevicesUUID failed, DevicesUUID = " << devices_uuid;
        return DatabaseExistenceStatus::NOT_EXIST;
    }
    return DatabaseExistenceStatus::EXIST;
}

DatabaseExistenceStatus VideoStorageDevice::GetVideoStorageDevicesListByProjectUUID(const std::string& project_uuid, VideoStorageDeviceInfoList& video_storage_device_info_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << video_storage_device_info_sec << " from VideoStorageDevice where AccountUUID = '" << project_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, DatabaseExistenceStatus::QUERY_ERROR);
        
    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        VideoStorageDeviceInfo video_storage_device_info;
        GetVideoStorageDeviceFromSql(video_storage_device_info, query);
        video_storage_device_info_list.push_back(video_storage_device_info);
    }
    return DatabaseExistenceStatus::EXIST;
}

}
