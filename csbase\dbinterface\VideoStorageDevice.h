#ifndef __DB_VIDEO_STORAGE_DEVICE_H__
#define __DB_VIDEO_STORAGE_DEVICE_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "AkcsCommonDef.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct VideoStorageDeviceInfo_T
{
    char uuid[36];
    char installer_uuid[36];
    char account_uuid[36];
    char personal_account_uuid[36];
    char video_storage_uuid[36];
    char devices_uuid[36];
    VideoStorageDeviceInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} VideoStorageDeviceInfo;

using VideoStorageDeviceInfoList = std::vector<VideoStorageDeviceInfo>;

namespace dbinterface {

class VideoStorageDevice
{
public:
    static DatabaseExistenceStatus GetVideoStorageDeviceInfo(const std::string& devices_uuid, VideoStorageDeviceInfo& video_storage_device_info);
    static DatabaseExistenceStatus GetVideoStorageDevicesListByProjectUUID(const std::string& project_uuid, VideoStorageDeviceInfoList& video_storage_device_info_list);

private:
    VideoStorageDevice() = delete;
    ~VideoStorageDevice() = delete;
    static void GetVideoStorageDeviceFromSql(VideoStorageDeviceInfo& video_storage_device_info, CRldbQuery& query);
};

}
#endif
