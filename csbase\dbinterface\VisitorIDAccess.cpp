#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "VisitorIDAccess.h"
#include "AkcsCommonSt.h"
#include "AkcsCommonDef.h"
#include "ConnectionManager.h"

namespace dbinterface {

static const std::string visitor_id_access_info_sec = " StartTime,StopTime,UUID,Name,PersonalAccountUUID ";

void VisitorIDAccess::GetVisitorIDAccessFromSql(VisitorIDAccessInfo& visitor_id_access_info, CRldbQuery& query)
{
    Snprintf(visitor_id_access_info.start_time, sizeof(visitor_id_access_info.start_time), query.GetRowData(0));
    Snprintf(visitor_id_access_info.stop_time, sizeof(visitor_id_access_info.stop_time), query.GetRowData(1));
    Snprintf(visitor_id_access_info.uuid, sizeof(visitor_id_access_info.uuid), query.GetRowData(2));
    Snprintf(visitor_id_access_info.name, sizeof(visitor_id_access_info.name), query.GetRowData(3));
    Snprintf(visitor_id_access_info.personal_account_uuid, sizeof(visitor_id_access_info.personal_account_uuid), query.GetRowData(4));
    return;
}

int VisitorIDAccess::GetVisitorIDAccessByUUID(const std::string& uuid, VisitorIDAccessInfo& visitor_id_access_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << visitor_id_access_info_sec << " from VisitorIDAccess where UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetVisitorIDAccessFromSql(visitor_id_access_info, query);
    }
    else
    {
        AK_LOG_WARN << "get VisitorIDAccessInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}

int VisitorIDAccess::GetVisitorIDAccessByAccountUUID(const std::string& account_uuid, VisitorIDAccessInfo& visitor_id_access_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << visitor_id_access_info_sec << " from VisitorIDAccess where AccountUUID = '" << account_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetVisitorIDAccessFromSql(visitor_id_access_info, query);
    }
    else
    {
        AK_LOG_WARN << "get VisitorIDAccessInfo by AccountUUID failed, AccountUUID = " << account_uuid;
        return -1;
    }
    return 0;
}

int VisitorIDAccess::GetVisitorIDAccessByPmUUID(const std::string& pm_uuid, VisitorIDAccessInfo& visitor_id_access_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << visitor_id_access_info_sec << " from VisitorIDAccess where PmUUID = '" << pm_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetVisitorIDAccessFromSql(visitor_id_access_info, query);
    }
    else
    {
        AK_LOG_WARN << "get VisitorIDAccessInfo by PmUUID failed, PmUUID = " << pm_uuid;
        return -1;
    }
    return 0;
}

void VisitorIDAccess::UpdateIDAccessAllowedTimesByUUID(const std::string& uuid)
{
    GET_DB_CONN_ERR_RETURN(db_conn,)

    std::stringstream stream_sql;
    stream_sql << "update VisitorIDAccess set AllowedTimes = AllowedTimes + 1 where UUID='" << uuid << "'";

    db_conn->Execute(stream_sql.str());

    return;
}

int VisitorIDAccess::CheckIDAccessPlanTime(CheckVisitorIDAccessInfo& id_access_info)
{
    if(id_access_info.sche_type == IDAccessCheck::SchedType::NEVER)
    {
        return CheckIDAccessPlanTimeScheTypeNever(id_access_info);
    }
    else if (id_access_info.sche_type == IDAccessCheck::SchedType::DAILY_SCHED)
    {
        return CheckIDAccessPlanTimeScheTypeDaliy(id_access_info);
    }
    else if (id_access_info.sche_type == IDAccessCheck::SchedType::WEEKLY_SCHED)
    {
        return CheckIDAccessPlanTimeScheTypeWeekly(id_access_info);
    }
    AK_LOG_WARN << "id access schedule type wrong. type=" << id_access_info.mode;
    return -1;
}

int VisitorIDAccess::CheckIDAccessPlanTimeScheTypeNever(CheckVisitorIDAccessInfo& id_access_info)
{
    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    CRldbQuery query(db_conn.get());
    std::stringstream streamSQL;
    streamSQL << "select 1 from VisitorIDAccess"
                    << " where UUID = '" << id_access_info.visitor_uuid << "'"
                    << " and BeginTime < '" << id_access_info.now_time_with_ymd << "'"
                    << " and EndTime > '" << id_access_info.now_time_with_ymd << "'";
    query.Query(streamSQL.str());
    if(query.MoveToNextRow())
    {
        id_access_info.check_res = IDAccessCheck::CheckRes::CHECK_RES_SUCCESS;
    }
    else
    {
        id_access_info.check_res = IDAccessCheck::CheckRes::CHECK_RES_FAILED;
    }

    return 0;
}

int VisitorIDAccess::CheckIDAccessPlanTimeScheTypeDaliy(CheckVisitorIDAccessInfo& id_access_info)
{
    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    CRldbQuery query(db_conn.get());
    std::stringstream streamSQL;
    streamSQL << "select 1 from VisitorIDAccess"
                        << " where UUID = '" << id_access_info.visitor_uuid << "'"
                        << " and StartTime < '" << id_access_info.now_time << "'"
                        << " and StopTime > '" << id_access_info.now_time << "'";
    query.Query(streamSQL.str());
    if(query.MoveToNextRow())
    {
        id_access_info.check_res = IDAccessCheck::CheckRes::CHECK_RES_SUCCESS;
    }
    else
    {
        AK_LOG_WARN << "check time plan failed. sche type = " << id_access_info.sche_type << ",now time = " << id_access_info.now_time; 
        id_access_info.check_res = IDAccessCheck::CheckRes::CHECK_RES_FAILED;
    }

    return 0;
}

int VisitorIDAccess::CheckIDAccessPlanTimeScheTypeWeekly(CheckVisitorIDAccessInfo& id_access_info)
{
    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    CRldbQuery query(db_conn.get());
    std::stringstream streamSQL;
    std::string days_str;
    streamSQL << "select Days from VisitorIDAccess"
                        << " where UUID = '" << id_access_info.visitor_uuid << "'"
                        << " and StartTime < '" << id_access_info.now_time << "'"
                        << " and StopTime > '" << id_access_info.now_time << "'";
    query.Query(streamSQL.str());
    if(query.MoveToNextRow())
    {
        days_str = query.GetRowData(0);
        AK_LOG_INFO << "weekly visitor id access, days=" << days_str;
        //当前天数不在一周可开门的天数中，校验失败
        if (days_str.find(id_access_info.day_of_week + '0') == std::string::npos) //转为字符类型对应的数字
        {
            AK_LOG_WARN << "check time plan failed. sche type = " << id_access_info.sche_type << ",now time = " << id_access_info.now_time << ",day of week=" << id_access_info.day_of_week;
            id_access_info.check_res = IDAccessCheck::CheckRes::CHECK_RES_FAILED;
        }
        else
        {
            id_access_info.check_res = IDAccessCheck::CheckRes::CHECK_RES_SUCCESS;
        }
    }
    else
    {
        AK_LOG_WARN << "check time plan failed. sche type = " << id_access_info.sche_type << ",now time = " << id_access_info.now_time << ",day of week=" << id_access_info.day_of_week; 

        id_access_info.check_res = IDAccessCheck::CheckRes::CHECK_RES_FAILED;
    }
    
    return 0;
}

int VisitorIDAccess::CheckVisitorIDAccess(CheckVisitorIDAccessInfo& id_access_info)
{
    //校验Run
    std::stringstream stream_sql;
    stream_sql << "select A.SchedulerType,A.AccessTimes,A.AllowedTimes,AL.Relay,AL.SecurityRelay,A.UUID,A.Mode,A.Serial,A.Name from "
                        << " VisitorIDAccess A left join VisitorIDAccessList AL on A.UUID = AL.VisitorIDAccessUUID"
                        << " where A.Run = '" << id_access_info.run << "'"
                        << " and AL.MAC = '" << id_access_info.mac << "'";

    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (!query.MoveToNextRow())
    {
        return -1;
    }
    std::string db_serial;
    id_access_info.sche_type = ATOI(query.GetRowData(0));
    id_access_info.access_times = ATOI(query.GetRowData(1));
    id_access_info.allowed_times = ATOI(query.GetRowData(2));
    id_access_info.relay_val = ATOI(query.GetRowData(3));
    id_access_info.se_relay_val = ATOI(query.GetRowData(4));
    Snprintf(id_access_info.visitor_uuid, sizeof(id_access_info.visitor_uuid), query.GetRowData(5));
    id_access_info.mode = ATOI(query.GetRowData(6));
    db_serial = query.GetRowData(7);
    Snprintf(id_access_info.name, sizeof(id_access_info.name), query.GetRowData(8));
    //根据Mode选择是否校验serial
    if (id_access_info.mode == IDAccessCheck::Mode::RUN_SERIAL)
    {
        if (strcmp(id_access_info.serial, db_serial.c_str()) != 0)
        {
            return -1;
        }
    }

    return 0;
}

static const std::string visitor_id_access_list_info_sec = " UUID,VisitorIDAccessUUID,MAC,Relay,SecurityRelay ";

void VisitorIDAccessList::GetVisitorIDAccessListFromSql(VisitorIDAccessListInfo& visitor_id_access_list_info, CRldbQuery& query)
{
    Snprintf(visitor_id_access_list_info.uuid, sizeof(visitor_id_access_list_info.uuid), query.GetRowData(0));
    Snprintf(visitor_id_access_list_info.visitor_id_access_uuid, sizeof(visitor_id_access_list_info.visitor_id_access_uuid), query.GetRowData(1));
    Snprintf(visitor_id_access_list_info.mac, sizeof(visitor_id_access_list_info.mac), query.GetRowData(2));
    visitor_id_access_list_info.relay = ATOI(query.GetRowData(3));
    visitor_id_access_list_info.security_relay = ATOI(query.GetRowData(4));
    return;
}

int VisitorIDAccessList::GetVisitorIDAccessListByUUID(const std::string& uuid, VisitorIDAccessListInfo& visitor_id_access_list_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << visitor_id_access_list_info_sec << " from VisitorIDAccessList where UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetVisitorIDAccessListFromSql(visitor_id_access_list_info, query);
    }
    else
    {
        AK_LOG_WARN << "get VisitorIDAccessListInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}
}