#ifndef __DB_VISITOR_I_D_ACCESS_H__
#define __DB_VISITOR_I_D_ACCESS_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"


typedef struct VisitorIDAccessInfo_T
{
    char start_time[32];
    char stop_time[32];
    char uuid[36];
    char days[16];
    int mode;
    char run[10];
    char serial[10];
    char name[128];
    char personal_account_uuid[64];
    
    VisitorIDAccessInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} VisitorIDAccessInfo;

namespace dbinterface {

class VisitorIDAccess
{
public:
    static int GetVisitorIDAccessByUUID(const std::string& uuid, VisitorIDAccessInfo& visitor_id_access_info);
    static int GetVisitorIDAccessByAccountUUID(const std::string& account_uuid, VisitorIDAccessInfo& visitor_id_access_info);
    static int GetVisitorIDAccessByPmUUID(const std::string& pm_uuid, VisitorIDAccessInfo& visitor_id_access_info);
    static int CheckIDAccessPlanTime(CheckVisitorIDAccessInfo& id_access_info);
    static int CheckIDAccessPlanTimeScheTypeNever(CheckVisitorIDAccessInfo& id_access_info);
    static int CheckIDAccessPlanTimeScheTypeDaliy(CheckVisitorIDAccessInfo& id_access_info);
    static int CheckIDAccessPlanTimeScheTypeWeekly(CheckVisitorIDAccessInfo& id_access_info);
    static void UpdateIDAccessAllowedTimesByUUID(const std::string& uuid);
    static int CheckVisitorIDAccess(CheckVisitorIDAccessInfo& id_access_info);

private:
    VisitorIDAccess() = delete;
    ~VisitorIDAccess() = delete;
    static void GetVisitorIDAccessFromSql(VisitorIDAccessInfo& visitor_id_access_info, CRldbQuery& query);
};

typedef struct VisitorIDAccessListInfo_T
{
    char uuid[36];
    char visitor_id_access_uuid[36];
    char mac[20];
    int relay;
    int security_relay;
    VisitorIDAccessListInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} VisitorIDAccessListInfo;

class VisitorIDAccessList
{
public:
    static int GetVisitorIDAccessListByUUID(const std::string& uuid, VisitorIDAccessListInfo& visitor_id_access_list_info);
    static int GetVisitorIDAccessListByIDAccessInfo(CheckVisitorIDAccessInfo& id_access_info);

private:
    VisitorIDAccessList() = delete;
    ~VisitorIDAccessList() = delete;
    static void GetVisitorIDAccessListFromSql(VisitorIDAccessListInfo& visitor_id_access_list_info, CRldbQuery& query);
};

}
#endif