#include <sstream>
#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "PhoneMapping.h"
#include "ConnectionManager.h"
#include "MappingConnectionPool.h"
#include "dbinterface/InterfaceComm.h"

namespace dbinterface
{

int PhoneMapping::FuzzySearchPhone(const std::string& detect_phone, std::vector<std::string>& encrypt_phone_list)
{
    GET_MAPPING_DB_CONN_ERR_RETURN(db_conn, -1)
    CRldbQuery query(db_conn.get());

    std::stringstream stream_sql;
    stream_sql << "/*master*/select EnColumn from PhoneMapping where DeColumn like'%" << detect_phone << "'";

    query.Query(stream_sql.str());

    while (query.MoveToNextRow())
    {
        std::string encrypt_phone = query.GetRowData(0);        
        encrypt_phone_list.push_back(encrypt_phone);
    }

    return 0;
}



}
