#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "DevicesDoorList.h"
#include "util_virtual_door.h"
#include "dbinterface/new-office/OfficeCompanyDoorList.h"

namespace dbinterface
{
    static const std::string devices_door_list_info_sec = " UUID,RelayType,Enable,`Index`,Name,Dtmf,ControlledRelay,ScheduleEnable,ScheduleAccess,\
                ShowHome,ShowTalking,PinEnable,FaceEnable,RfCardEnable,BleEnable,NfcEnable,PlateEnable,ExitButton,DoorStatus,HoldOpenAlarmEnable,\
                HoldOpenAlarmTimeOut,BreakInAlarmEnable,Active,ExpireTime <= now() as Expire,ActiveTime,ExpireTime,AccountUUID,DevicesUUID,IsEmergencyDoor,ISNULL(ExitButton),FingerPrintEnable ";

    void DevicesDoorList::GetDevicesDoorInfoFromSql(DevicesDoorInfo& devices_door_info, CRldbQuery& query)
    {
        Snprintf(devices_door_info.uuid, sizeof(devices_door_info.uuid), query.GetRowData(0));
        devices_door_info.relay_type = (DoorRelayType)ATOI(query.GetRowData(1));
        devices_door_info.enable = ATOI(query.GetRowData(2));
        devices_door_info.door_index = ATOI(query.GetRowData(3));
        Snprintf(devices_door_info.name, sizeof(devices_door_info.name), query.GetRowData(4));
        Snprintf(devices_door_info.dtmf, sizeof(devices_door_info.dtmf), query.GetRowData(5));
        Snprintf(devices_door_info.controlled_relay, sizeof(devices_door_info.controlled_relay), query.GetRowData(6));
        devices_door_info.schedule_enable = ATOI(query.GetRowData(7));
        Snprintf(devices_door_info.schedule_access, sizeof(devices_door_info.schedule_access), query.GetRowData(8));
        devices_door_info.display_app_homepage = ATOI(query.GetRowData(9));
        devices_door_info.display_app_talkingpage = ATOI(query.GetRowData(10));
        devices_door_info.pin_enable = ATOI(query.GetRowData(11));
        devices_door_info.face_enable = ATOI(query.GetRowData(12));
        devices_door_info.rf_card_enable = ATOI(query.GetRowData(13));
        devices_door_info.ble_enable = ATOI(query.GetRowData(14));
        devices_door_info.nfc_enable = ATOI(query.GetRowData(15));
        devices_door_info.license_plate_enable = ATOI(query.GetRowData(16));
        Snprintf(devices_door_info.exit_button_input, sizeof(devices_door_info.exit_button_input), query.GetRowData(17));
        Snprintf(devices_door_info.door_status_input, sizeof(devices_door_info.door_status_input), query.GetRowData(18));
        devices_door_info.holdopen_alarm_enable = ATOI(query.GetRowData(19));
        devices_door_info.holdopen_alarm_timeout = ATOI(query.GetRowData(20));
        devices_door_info.breakin_alarm_enable = ATOI(query.GetRowData(21));
        devices_door_info.active = ATOI(query.GetRowData(22));
        devices_door_info.expire = ATOI(query.GetRowData(23));
        Snprintf(devices_door_info.active_time, sizeof(devices_door_info.active_time), query.GetRowData(24));
        Snprintf(devices_door_info.expire_time, sizeof(devices_door_info.expire_time), query.GetRowData(25));
        Snprintf(devices_door_info.account_uuid, sizeof(devices_door_info.account_uuid), query.GetRowData(26));
        Snprintf(devices_door_info.devices_uuid, sizeof(devices_door_info.devices_uuid), query.GetRowData(27));
        devices_door_info.is_emergency_door = ATOI(query.GetRowData(28));
        devices_door_info.is_null_exit_button = ATOI(query.GetRowData(29));
        devices_door_info.finger_print_enable = ATOI(query.GetRowData(30));

        devices_door_info.access_control_value = GetDoorAccessControlValue(devices_door_info.pin_enable, devices_door_info.face_enable, devices_door_info.rf_card_enable, 
                            devices_door_info.ble_enable, devices_door_info.nfc_enable, devices_door_info.license_plate_enable, devices_door_info.finger_print_enable);

        return;
    }

    int DevicesDoorList::GetDevicesDoorListByDevicesUUID(const std::string& devices_uuid, DevicesDoorInfoList& devices_door_info_list)
    {
        std::stringstream stream_sql;
        stream_sql << "select " << devices_door_list_info_sec << " from DevicesDoorList where DevicesUUID = '" << devices_uuid << "'";
        GET_DB_CONN_ERR_RETURN(db_conn, -1);

        CRldbQuery query(db_conn.get());
        query.Query(stream_sql.str());
        while (query.MoveToNextRow())
        {
            DevicesDoorInfo devices_door_info;
            GetDevicesDoorInfoFromSql(devices_door_info, query);
            devices_door_info_list.push_back(devices_door_info);
        }
        return 0;
    }

    int DevicesDoorList::GetEnabledDevicesDoorListByDevicesUUID(const std::string& devices_uuid, DevicesDoorInfoList& devices_door_info_list)
    {
        std::stringstream stream_sql;
        stream_sql << "select " << devices_door_list_info_sec << " from DevicesDoorList where DevicesUUID = '" << devices_uuid << "' and Enable = 1";
        GET_DB_CONN_ERR_RETURN(db_conn, -1);

        CRldbQuery query(db_conn.get());
        query.Query(stream_sql.str());
        while (query.MoveToNextRow())
        {
            DevicesDoorInfo devices_door_info;
            GetDevicesDoorInfoFromSql(devices_door_info, query);
            devices_door_info_list.push_back(devices_door_info);
        }
        return 0;
    }

    int DevicesDoorList::GetDevicesDoorMapByProjectUUID(const std::string& project_uuid, DevicesDoorInfoMap& devices_door_info_map, DevicesPublicDoorInfoMap& pub_door_info_map)
    {
        std::set<std::string> pub_door_uuid_set;
        dbinterface::OfficeCompanyDoorList::GetPubDoorUUIDsByProjectUUID(project_uuid, pub_door_uuid_set);
        
        {
            std::stringstream stream_sql;
            stream_sql << "select " << devices_door_list_info_sec << " from DevicesDoorList where AccountUUID = '" << project_uuid << "'";
            GET_DB_CONN_ERR_RETURN(db_conn, -1);

            CRldbQuery query(db_conn.get());
            query.Query(stream_sql.str());
            while (query.MoveToNextRow())
            {
                DevicesDoorInfo devices_door_info;
                GetDevicesDoorInfoFromSql(devices_door_info, query);
                devices_door_info_map[devices_door_info.devices_uuid].push_back(devices_door_info);
                
                if(pub_door_uuid_set.find(devices_door_info.uuid) != pub_door_uuid_set.end())
                {
                    pub_door_info_map.insert(std::make_pair(devices_door_info.devices_uuid, devices_door_info));
                }
            }
        }

        // 获取项目下 Door 对应的 Readerlist 
        DoorReaderInfoListMap door_reader_info_list_map;
        dbinterface::DoorReaderList::GetDoorReaderListMapByProjectUUID(project_uuid, door_reader_info_list_map);

        // 给door的reader_list赋值
        for (auto& devices_door : devices_door_info_map)
        {
            for (auto& door : devices_door.second)
            {   
                if (door_reader_info_list_map.find(door.uuid) != door_reader_info_list_map.end())
                {
                    door.door_reader_list = door_reader_info_list_map[door.uuid];
                }
            }
        }
        
        for (auto& pub_door : pub_door_info_map)
        {
            auto& door = pub_door.second;
            if (door_reader_info_list_map.find(door.uuid) != door_reader_info_list_map.end())
            {
                door.door_reader_list = door_reader_info_list_map[door.uuid];
            }
        }
        return 0;
    }

    int DevicesDoorList::GetDevicesDoorList(const std::string& devices_uuid, DevicesDoorInfoList& devices_door_info_list)
    {
        // 获取设备的Door List
        dbinterface::DevicesDoorList::GetDevicesDoorListByDevicesUUID(devices_uuid, devices_door_info_list);

        // 获取每个Door的reader list
        for (auto& devices_door_info : devices_door_info_list)
        {
            DoorReaderInfoList door_reader_info_list;
            dbinterface::DoorReaderList::GetDoorReaderListByDevicesDoorListUUID(devices_door_info.uuid, door_reader_info_list);
            devices_door_info.door_reader_list = door_reader_info_list;
        }
        return 0;
    }

    int DevicesDoorList::GetDevicesDoorInfoByUUID(const std::string& uuid, DevicesDoorInfo& devices_door_info)
    {
        std::stringstream stream_sql;
        stream_sql << "select " << devices_door_list_info_sec << " from DevicesDoorList where UUID = '" << uuid << "'";
        GET_DB_CONN_ERR_RETURN(db_conn, -1);

        CRldbQuery query(db_conn.get());
        query.Query(stream_sql.str());
        if (query.MoveToNextRow())
        {
            GetDevicesDoorInfoFromSql(devices_door_info, query);
        }
        else
        {
            AK_LOG_WARN << "GetDevicesDoorInfoByUUID failed, DevicesDoorUUID = " << uuid;
            return -1;
        }
        return 0;
    }

    void DevicesDoorList::GetDevicesDoorEnableRelayValueByDevicesUUID(const std::string& devices_uuid, int& relay_value, int& security_relay_value)
    {
        DevicesDoorInfoList devices_door_info_list;
        dbinterface::DevicesDoorList::GetDevicesDoorListByDevicesUUID(devices_uuid, devices_door_info_list);
        
        for (const auto& door_info : devices_door_info_list)
        {
            if (door_info.enable == 0)
            {
                continue;
            }

            int relay_value_to_add = GetRelayValueByControlledRelay(door_info.controlled_relay);
            if (door_info.relay_type == DoorRelayType::RELAY)
            {
                relay_value += relay_value_to_add;
            }
            else if (door_info.relay_type == DoorRelayType::SECURITY_RELAY)
            {
                security_relay_value += relay_value_to_add;
            }
        }
        
        return;
    }

    // 判断 设备是否所有门都过期
    bool DevicesDoorList::IsAllDoorExpired(const std::string& devices_uuid)
    {
        DevicesDoorInfoList devices_door_info_list;
        dbinterface::DevicesDoorList::GetDevicesDoorListByDevicesUUID(devices_uuid, devices_door_info_list);
        for (const auto& devices_door : devices_door_info_list)
        {
            // 没有激活 或者 没有启用的 就算过期
            if(!devices_door.enable || !devices_door.active){
                continue;
            }

            // 没有过期 直接返回false
            if (devices_door.expire == false)
            {
                return false;
            }
        }
        return true;
    }
        
    // 设备只要一个door没有续费 就算过期 就整个设备的user不下发
    bool DevicesDoorList::IsSubscribedDevice(const std::string& devices_uuid)
    {
        DevicesDoorInfoList devices_door_info_list;
        dbinterface::DevicesDoorList::GetDevicesDoorListByDevicesUUID(devices_uuid, devices_door_info_list);
        for (const auto& devices_door : devices_door_info_list)
        {
            if (IsDoorInactiveOrExpired(devices_door.enable, devices_door.active, devices_door.expire))
            {
                return false;
            }
        }
        return true;
    }

    // 获取door开关开启状态下，付费正常的realy value
    void DevicesDoorList::GetDevicesNormalRelayValue(const std::string& devices_uuid, int& normal_relay_value, int& normal_security_relay_value)
    {
        DevicesDoorInfoList devices_door_info_list;
        dbinterface::DevicesDoorList::GetDevicesDoorListByDevicesUUID(devices_uuid, devices_door_info_list);
        for (const auto& devices_door : devices_door_info_list)
        {
            // 如果door没有启用，则不处理
            if (devices_door.enable == 0)
            {
                continue;
            }

            // 如果door未激活或者过期，则不处理
            if (IsDoorInactiveOrExpired(devices_door.enable, devices_door.active, devices_door.expire))
            {
                continue;
            }

            if (devices_door.relay_type == DoorRelayType::RELAY)
            {
                normal_relay_value += GetRelayValueByControlledRelay(devices_door.controlled_relay);
            }
            else if (devices_door.relay_type == DoorRelayType::SECURITY_RELAY)
            {
                normal_security_relay_value += GetRelayValueByControlledRelay(devices_door.controlled_relay);
            }
        }   
        return;
    }

    std::string DevicesDoorList::GetReportActLogDoorNameList(const std::string& devices_uuid, const std::string& relay, const std::string& security_relay)
    {
        DevicesDoorInfoList devices_door_info_list;
        dbinterface::DevicesDoorList::GetEnabledDevicesDoorListByDevicesUUID(devices_uuid, devices_door_info_list);
        
        // 获取relay对应的DoorName
        std::map<int, std::string> relay_index_name_map;
        std::map<int, std::string> security_relay_index_name_map;
        for (const auto& door_info : devices_door_info_list)
        {
            int relay_index = GetRelayIndexByControlledRelay(door_info.controlled_relay);
            if (door_info.relay_type == DoorRelayType::RELAY)
            {
                relay_index_name_map.insert(make_pair(relay_index, door_info.name));
            }
            else if (door_info.relay_type == DoorRelayType::SECURITY_RELAY)
            {
                security_relay_index_name_map.insert(make_pair(relay_index, door_info.name));
            }
        }
        
        // 遍历relay获取对应的DoorName
        std::string door_name_list;
        for (char r : relay)
        {
            int relay_index = static_cast<int>(r - '0');
            if (relay_index_name_map.find(relay_index) != relay_index_name_map.end())
            {
                door_name_list += relay_index_name_map[relay_index] + ",";
            }
        }

        // 获取security relay对应的DoorName
        for (char sr : security_relay)
        {
            int security_relay_index = static_cast<int>(sr - '0');
            if (security_relay_index_name_map.find(security_relay_index) != security_relay_index_name_map.end())
            {
                door_name_list += security_relay_index_name_map[security_relay_index] + ",";
            }
        }

        if (!door_name_list.empty() && door_name_list.back() == ',')
        {
            door_name_list.pop_back();
        }
        
        return door_name_list;
    }

    int DevicesDoorList::UpdateDoorLockDownMode(const std::string& devices_uuid, DoorRelayType relay_type, const std::string& controlled_relay, int lockdown_mode)
    {
        std::stringstream stream_sql;
        stream_sql << "update DevicesDoorList set IsLocked = " << lockdown_mode << " where DevicesUUID = '" << devices_uuid 
                   << "' and ControlledRelay = '"  << controlled_relay << "' and RelayType = '" << (int)relay_type << "'";
            
        GET_DB_CONN_ERR_RETURN(db_conn, -1);
        db_conn->Execute(stream_sql.str());

        return 0;
    }

    std::string DevicesDoorList::GetDoorUUIDListByRelayList(const std::string& devices_uuid, const std::string& relay, const std::string& security_relay)
    {
        DevicesDoorInfoList devices_door_info_list;
        dbinterface::DevicesDoorList::GetDevicesDoorListByDevicesUUID(devices_uuid, devices_door_info_list);
        
        // 获取relay对应的DoorName
        std::map<int, std::string> relay_index_door_uuid_map;
        std::map<int, std::string> se_relay_index_door_uuid_map;
        for (const auto& door_info : devices_door_info_list)
        {
            int relay_index = GetRelayIndexByControlledRelay(door_info.controlled_relay);
            if (door_info.relay_type == DoorRelayType::RELAY)
            {
                relay_index_door_uuid_map.insert(make_pair(relay_index, door_info.uuid));
            }
            else if (door_info.relay_type == DoorRelayType::SECURITY_RELAY)
            {
                se_relay_index_door_uuid_map.insert(make_pair(relay_index, door_info.uuid));
            }

        }
        
        // 遍历relay获取对应的DoorUUID
        std::string door_uuid_list;
        for (char r : relay)
        {
            int relay_index = static_cast<int>(r - '0');
            if (relay_index_door_uuid_map.find(relay_index) != relay_index_door_uuid_map.end())
            {
                door_uuid_list += relay_index_door_uuid_map[relay_index] + ";";
            }
        }

        for (char sr : security_relay)
        {
            int security_relay_index = static_cast<int>(sr - '0');
            if (se_relay_index_door_uuid_map.find(security_relay_index) != se_relay_index_door_uuid_map.end())
            {
                door_uuid_list += se_relay_index_door_uuid_map[security_relay_index] + ";";
            }
        }

        if (!door_uuid_list.empty() && door_uuid_list.back() == ';')
        {
            door_uuid_list.pop_back();
        }
        
        return door_uuid_list;
    }

    int DevicesDoorList::GetOfficeCompanyDoorByDevicesUUID(const std::string& devices_uuid, CompanyDoorList& office_company_door_info_list)
    {
        std::stringstream stream_sql;
        stream_sql << "select D.ControlledRelay,D.RelayType,O.OfficeCompanyUUID from DevicesDoorList D join OfficeCompanyDoorList O on D.UUID = O.DevicesDoorListUUID \
        where D.DevicesUUID = '" << devices_uuid << "'";
        GET_DB_CONN_ERR_RETURN(db_conn, -1);

        CRldbQuery query(db_conn.get());
        query.Query(stream_sql.str());
        CompanyDoorRelayInfoMap company_door_relay_info_map;
        while (query.MoveToNextRow())
        {
            DevicesDoorRelayInfo relay_info;
            Snprintf(relay_info.controlled_relay, sizeof(relay_info.controlled_relay), query.GetRowData(0));
            relay_info.relay_type = (DoorRelayType)ATOI(query.GetRowData(1));
            std::string office_company_uuid = query.GetRowData(2);
            company_door_relay_info_map.emplace(office_company_uuid, relay_info);
        }        

        for (auto it = company_door_relay_info_map.begin(); it != company_door_relay_info_map.end(); )
        {
            OfficeCompanyDoorInfo office_company_door_info;
            Snprintf(office_company_door_info.devices_uuid, sizeof(office_company_door_info.devices_uuid), devices_uuid.c_str());
            Snprintf(office_company_door_info.office_company_uuid, sizeof(office_company_door_info.office_company_uuid), it->first.c_str());
            
            // Get range of elements with same key
            auto range = company_door_relay_info_map.equal_range(it->first);
            
            for (auto iter_range = range.first; iter_range != range.second; ++iter_range)
            {
                const auto& relay_info = iter_range->second;
                if (relay_info.relay_type == DoorRelayType::RELAY)
                {
                    office_company_door_info.relay += GetRelayValueByControlledRelay(relay_info.controlled_relay);
                }
                else if (relay_info.relay_type == DoorRelayType::SECURITY_RELAY)
                {
                    office_company_door_info.srelay += GetRelayValueByControlledRelay(relay_info.controlled_relay);
                }
            }
            
            // Save company info
            office_company_door_info_list.push_back(office_company_door_info);
            
            // Skip to next key
            it = range.second;
        }
        return 0;
    }

}
