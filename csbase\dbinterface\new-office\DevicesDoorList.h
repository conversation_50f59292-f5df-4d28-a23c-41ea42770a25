#ifndef __DB_DEVICES_DOOR_LIST_H__
#define __DB_DEVICES_DOOR_LIST_H__

#include <map>
#include <vector>
#include <string>
#include <memory>
#include "util.h"
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/new-office/DoorReaderList.h"

typedef struct DevicesDoorInfo_T
{
    char uuid[36];
    int active;                         // door是否激活, 0: 未激活, 1: 激活
    int expire;                         // door是否过期, 0: 未过期, 1: 过期
    int enable;                         // door是否开启 0：关闭 1：开启
    DoorRelayType relay_type;           // door relay类型 0：普通Door 1：Security Door
    int door_index;                     // 第几个Door,区间为1-4
    char name[128];                     // door名称
    char dtmf[8];                       // door的dtmf
    char controlled_relay[8];           // 控制的Relay,A-D表示RelayA、B、C、D
    int schedule_enable;                // door schedule是否开启 0：关闭 1：开启
    char schedule_access[512];          // 权限组UUID列表,用逗号拼接,目前最多10个
    int display_app_homepage;           // door是否展示在SmartPlus HomePage 0：关闭 1：开启
    int display_app_talkingpage;        // door是否展示在SmartPlus Talking Page 0：关闭 1：开启
    int pin_enable;                     // pin是否开启 0：关闭 1：开启
    int face_enable;                    // face是否开启 0：关闭 1：开启
    int rf_card_enable;                 // rfCard是否开启 0：关闭 1：开启
    int ble_enable;                     // ble是否开启 0：关闭 1：开启
    int nfc_enable;                     // nfc是否开启 0：关闭 1：开启
    int license_plate_enable;           // license plate是否开启 0：关闭 1：开启
    int access_control_value;           // 按位下发支持的开门方式
    char exit_button_input[8];          // 内开门input配置,可为空
    char door_status_input[16];          // 门磁传感器链接的input,A B C D,magneticA,magneticB,magneticC,magneticD,可为空 
    int holdopen_alarm_enable;          // DoorHoldOpen是否开启 0：关闭 1：开启
    int holdopen_alarm_timeout;         // DoorHoldOpen超时时间,区间为0-300s
    int breakin_alarm_enable;           // BreakInAlarm是否开启 0：关闭 1：开启
    int is_emergency_door;              // 是否为紧急告警开的门 0：不是 1：是
    char active_time[37];               // Door的激活时间
    char expire_time[37];               // Door的过期时间
    char account_uuid[36];              // 项目uuid
    char devices_uuid[36];              // 设备uuid
    int is_null_exit_button;            // exit_button是否为null,为null时表示未编辑过
    int finger_print_enable;            // 指纹是否开启 0：关闭 1：开启 
    DoorReaderInfoList door_reader_list;// Door设置的reader

    DevicesDoorInfo_T() : active(0), expire(1), enable(0), relay_type(DoorRelayType::RELAY),
                                door_index(1), schedule_enable(0), display_app_homepage(0), display_app_talkingpage(0),
                                pin_enable(0), face_enable(0), rf_card_enable(0), ble_enable(0), nfc_enable(0), license_plate_enable(0), 
                                access_control_value(0), holdopen_alarm_enable(0), holdopen_alarm_timeout(0),breakin_alarm_enable(0),
                                is_emergency_door(0), is_null_exit_button(1), finger_print_enable(0) {
        memset(uuid, 0, sizeof(uuid));
        memset(name, 0, sizeof(name));
        memset(dtmf, 0, sizeof(dtmf));
        memset(controlled_relay, 0, sizeof(controlled_relay));
        memset(schedule_access, 0, sizeof(schedule_access));
        memset(exit_button_input, 0, sizeof(exit_button_input));
        memset(door_status_input, 0, sizeof(door_status_input));
        memset(active_time, 0, sizeof(active_time));
        memset(expire_time, 0, sizeof(expire_time));
        memset(account_uuid, 0, sizeof(account_uuid));
        memset(devices_uuid, 0, sizeof(devices_uuid));
    }
} DevicesDoorInfo;

using DevicesDoorInfoList = std::vector<DevicesDoorInfo>;
using DevicesDoorInfoMap = std::map<std::string/*dev_uuid*/, DevicesDoorInfoList>;
using DevicesPublicDoorInfoMap = std::multimap<std::string/*dev_uuid*/, DevicesDoorInfo>;

//公司与门的关联信息
typedef struct OfficeCompanyDoorInfo_T
{
    char office_company_uuid[64];
    char devices_uuid[64];
    int relay;
    int srelay;
    OfficeCompanyDoorInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} OfficeCompanyDoorInfo;
typedef struct DevicesDoorRelayInfo_T
{
    char controlled_relay[8];
    DoorRelayType relay_type;
    DevicesDoorRelayInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} DevicesDoorRelayInfo;
using CompanyDoorList = std::vector<OfficeCompanyDoorInfo>;
using CompanyDoorRelayInfoMap = std::multimap<std::string/*company_uuid*/, DevicesDoorRelayInfo>;

namespace dbinterface
{
    class DevicesDoorList
    {
    public:
        static bool IsAllDoorExpired(const std::string& devices_uuid);
        static bool IsSubscribedDevice(const std::string& devices_uuid);
        static int GetDevicesDoorInfoByUUID(const std::string& uuid, DevicesDoorInfo& devices_door_info);
        static int GetDevicesDoorList(const std::string& devices_uuid, DevicesDoorInfoList& devices_door_info_list);
        static int GetDevicesDoorMapByProjectUUID(const std::string& project_uuid, DevicesDoorInfoMap& devices_door_info_map, DevicesPublicDoorInfoMap& pub_door_info_map);
        static int GetDevicesDoorListByDevicesUUID(const std::string& devices_uuid, DevicesDoorInfoList& devices_door_info_list);
        static void GetDevicesDoorEnableRelayValueByDevicesUUID(const std::string& devices_uuid, int& relay_value, int& security_relay_value);
        static std::string GetReportActLogDoorNameList(const std::string& devices_uuid, const std::string& relay, const std::string& security_relay);
        static int UpdateDoorLockDownMode(const std::string& devices_uuid, DoorRelayType relay_type, const std::string& controlled_relay, int lockdown_mode);
        // 根据relay index list获取door uuid list, 多个之间用";"分隔
        static std::string GetDoorUUIDListByRelayList(const std::string& device_uuid, const std::string& relay, const std::string& security_relay);
        static int GetEnabledDevicesDoorListByDevicesUUID(const std::string& devices_uuid, DevicesDoorInfoList& devices_door_info_list);
        static void GetDevicesNormalRelayValue(const std::string& devices_uuid, int& normal_relay_value, int& normal_security_relay_value);
        static int GetOfficeCompanyDoorByDevicesUUID(const std::string& devices_uuid, CompanyDoorList& office_company_door_info_list);

    private:
        DevicesDoorList() = delete;
        ~DevicesDoorList() = delete;
        static void GetDevicesDoorInfoFromSql(DevicesDoorInfo& devices_door_info, CRldbQuery& query);
    };

}
#endif
