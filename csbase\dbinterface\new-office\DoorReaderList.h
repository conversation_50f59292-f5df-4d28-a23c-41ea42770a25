#ifndef __DB_DOOR_READER_LIST_H__
#define __DB_DOOR_READER_LIST_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

enum class DoorReaderMode
{
    ENTRY = 0,
    EXIT = 1,
};

enum class DoorReaderType
{
    INTERNAL = 0,
    WIEGAND = 1,
    RS485 = 2,   
};

enum class DoorReaderRs485ConnectType
{
    SERIES = 0,
    SINGLE_DEVICE = 1,
};

typedef struct DoorReaderInfo_T
{
    DoorReaderMode mode; 
    DoorReaderType type;
    char type_value[16];
    char rs485_address[128];
    char devices_doorlist_uuid[36];

    DoorReaderInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} DoorReaderInfo;

using DoorReaderInfoList = std::vector<DoorReaderInfo>;
using DoorReaderInfoListMap = std::map<std::string/*door_uuid*/, DoorReaderInfoList>;

namespace dbinterface {

class DoorReaderList
{
public:
    static int GetDoorReaderListMapByProjectUUID(const std::string& project_uuid, DoorReaderInfoListMap& door_reader_info_list);
    static int GetDoorReaderListByDevicesDoorListUUID(const std::string& devices_door_list_uuid, DoorReaderInfoList& door_reader_info_list);
private:
    DoorReaderList() = delete;
    ~DoorReaderList() = delete;
    static void GetDoorReaderInfoFromSql(DoorReaderInfo& door_reader_list_info, CRldbQuery& query);
};

}
#endif