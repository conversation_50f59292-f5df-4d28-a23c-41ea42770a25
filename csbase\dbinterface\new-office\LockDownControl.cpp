#include <sstream>
#include <string.h>
#include "util.h"
#include "util_cstring.h"
#include "AkLogging.h"
#include <boost/algorithm/string.hpp>
#include "AkcsCommonDef.h"
#include "dbinterface/UUID.h"
#include "ConnectionManager.h"
#include "dbinterface/PropertyInfo.h"
#include "dbinterface/AccountUserInfo.h"
#include "dbinterface/Log/PersonalCapture.h"
#include "dbinterface/new-office/LockDownControl.h"

namespace dbinterface
{

int LockDownControl::GetLockDownControlInfoByUUID(const std::string &lockdown_uuid, LockDownControlInfo &lockdown_control_info)
{
    std::stringstream stream_sql;
    stream_sql  << "/*master*/ select LockDownSwitch,OperatorUUID,ProjectUUID,CompanyUUID from LockDownControl where UUID = '" << lockdown_uuid << "'";

    GET_DB_CONN_ERR_RETURN(conn, -1);

    CRldbQuery query(conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        lockdown_control_info.lockdown_switch = (LockDownSwitch)ATOI(query.GetRowData(0));
        Snprintf(lockdown_control_info.operator_uuid, sizeof(lockdown_control_info.operator_uuid), query.GetRowData(1));
        Snprintf(lockdown_control_info.project_uuid, sizeof(lockdown_control_info.project_uuid), query.GetRowData(2));
        Snprintf(lockdown_control_info.company_uuid, sizeof(lockdown_control_info.company_uuid), query.GetRowData(3));
    }
    else
    {
        AK_LOG_WARN << "GetLockDownControlInfoByUUID Failed, lockdown_uuid = " << lockdown_uuid;
        return -1;
    }
    return 0;
}

// 获取一次lowndown操作的所有设备
int LockDownControl::GetLockDownDoorListByUUID(const std::string &lockdown_uuid, LockDownDoorInfoList &lockdown_door_info_list)
{
    std::stringstream stream_sql;
    stream_sql  << "/*master*/select DeviceUUID, group_concat(Relay order by Relay separator ''), group_concat(SecurityRelay order by SecurityRelay separator '') "
                << "from LockDownDoorList where LockDownUUID = '" << lockdown_uuid << "' group by DeviceUUID";

    GET_DB_CONN_ERR_RETURN(conn, -1);

    CRldbQuery query(conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        std::string device_uuid = query.GetRowData(0);
        std::string relay = query.GetRowData(1);
        std::string security_relay = query.GetRowData(2);

        //relay和security_relay一同查出时,relay:1234,security_relay:0000,去掉多余的0
        boost::replace_all(relay,"0","");
        boost::replace_all(security_relay,"0","");

        LockDownDoorInfo lockdown_door_info;
        Snprintf(lockdown_door_info.relay, sizeof(lockdown_door_info.relay), relay.c_str());
        Snprintf(lockdown_door_info.security_relay, sizeof(lockdown_door_info.security_relay), security_relay.c_str());	
        Snprintf(lockdown_door_info.device_uuid, sizeof(lockdown_door_info.device_uuid), device_uuid.c_str());

        lockdown_door_info_list.push_back(lockdown_door_info);
    }
    return 0;
}

// 获取一次lowndown操作的单个设备信息
int LockDownControl::GetDeviceLockDownDoorList(const std::string &lockdown_uuid, const std::string &device_uuid, LockDownDoorInfo &lockdown_door_info)
{
    std::stringstream stream_sql;
    stream_sql  << "/*master*/select DeviceUUID, group_concat(Relay order by Relay separator ''), group_concat(SecurityRelay order by SecurityRelay separator '') "
                << "from LockDownDoorList where LockDownUUID = '" << lockdown_uuid << "' and DeviceUUID = '" << device_uuid << "' group by DeviceUUID";

    GET_DB_CONN_ERR_RETURN(conn, -1);

    CRldbQuery query(conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        std::string device_uuid = query.GetRowData(0);
        std::string relay = query.GetRowData(1);
        std::string security_relay = query.GetRowData(2);

        //relay和security_relay一同查出时,relay:1234,security_relay:0000,去掉多余的0
        boost::replace_all(relay,"0","");
        boost::replace_all(security_relay,"0","");

        Snprintf(lockdown_door_info.relay, sizeof(lockdown_door_info.relay), relay.c_str());
        Snprintf(lockdown_door_info.security_relay, sizeof(lockdown_door_info.security_relay), security_relay.c_str());	
        Snprintf(lockdown_door_info.device_uuid, sizeof(lockdown_door_info.device_uuid), device_uuid.c_str());
    }
    return 0;
}

int LockDownControl::UpdateRelayLockDownStatus(const std::string& lockdown_uuid, const std::string& devices_uuid, int relay, bool lockdown_success)
{
    int status = lockdown_success == true ? 1 : 0;

    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    std::stringstream stream_sql;
    stream_sql << "update LockDownDoorList set Status = '" << status << "' where LockDownUUID = '" << lockdown_uuid
               << "' and DeviceUUID = '" << devices_uuid << "' and Relay = '" << relay << "'";

    db_conn->Execute(stream_sql.str());
    return 0;
}

int LockDownControl::UpdateSecurityRelayLockDownStatus(const std::string& lockdown_uuid, const std::string& devices_uuid, int relay, bool lockdown_success)
{
    int status = lockdown_success == true ? 1 : 0;

    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    std::stringstream stream_sql;
    stream_sql << "update LockDownDoorList set Status = '" << status << "' where LockDownUUID = '" << lockdown_uuid
               << "' and DeviceUUID = '" << devices_uuid << "' and SecurityRelay = '" << relay << "'";

    db_conn->Execute(stream_sql.str());
    return 0;
}

}