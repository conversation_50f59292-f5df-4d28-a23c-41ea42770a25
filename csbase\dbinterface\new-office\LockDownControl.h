#ifndef __DB_LOCKDOWN_CONTROL_H__
#define __DB_LOCKDOWN_CONTROL_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include "util.h"
#include "ConnectionPool.h"
#include "ConnectionManager.h"

enum class LockdownInitiatorType
{
    UNKNOWN = 0,
    PM = 1,
    INSTALLER = 2
};

enum class LockDownStatus
{
    PROCESSING = 0,
    SUCCESS = 1,
    FAILED = 2
};

enum class LockDownSwitch
{
    OFF = 0,
    ON = 1
};

typedef struct LockDownControlInfo_T
{
    char operator_uuid[64]; // 标识触发者的uuid，pm或者admin
    char project_uuid[64];  // 项目uuid
    char company_uuid[64];  // 公司uuid
    LockDownSwitch  lockdown_switch; // 0:关闭lockDown; 1:开启lockDown

    LockDownControlInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} LockDownControlInfo;

typedef struct LockDownDoorInfo_T
{
    char device_uuid[64];
    char relay[64];
    char security_relay[64];

    LockDownDoorInfo_T() {
        memset(this, 0, sizeof(*this));
    }
}LockDownDoorInfo;

typedef std::vector<LockDownDoorInfo> LockDownDoorInfoList;

namespace dbinterface
{
class LockDownControl
{
public:
    LockDownControl();
    ~LockDownControl();
    static int GetLockDownDoorListByUUID(const std::string &uuid, LockDownDoorInfoList &list);
    static int GetLockDownControlInfoByUUID(const std::string &lockdown_uuid, LockDownControlInfo &lockdown_control_info);
    static int GetDeviceLockDownDoorList(const std::string &lockdown_uuid, const std::string &device_uuid, LockDownDoorInfo &lockdown_door_info);
    static int UpdateRelayLockDownStatus(const std::string& lockdown_uuid, const std::string& devices_uuid, int relay, bool lockdown_success);
    static int UpdateSecurityRelayLockDownStatus(const std::string& lockdown_uuid, const std::string& devices_uuid, int relay, bool lockdown_success);
private: 
    static LockDownStatus GetLockDownStatus(const std::string& status);

};


}

#endif
