#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "OfficeAccessGroup.h"

namespace dbinterface {

static const std::string office_access_group_info_sec = " <PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON>,A.OfficeCompanyUUID,A.SchedulerType,\
    DATE_FORMAT(A.BeginDateTime,'%Y%m%d') as daystart,DATE_FORMAT(A.EndDateTime,'%Y%m%d') as dayend, <PERSON><PERSON>,S.DateFlag,S.StartTime,S.StopTime,S.ID ";

void OfficeAccessGroup::GetOfficeAccessGroupFromSql(OfficeAccessGroupInfo& office_access_group_info, CRldbQuery& query)
{
    Snprintf(office_access_group_info.uuid, sizeof(office_access_group_info.uuid), query.GetRowData(0));
    Snprintf(office_access_group_info.name, sizeof(office_access_group_info.name), query.GetRowData(1));
    Snprintf(office_access_group_info.project_uuid, sizeof(office_access_group_info.project_uuid), query.GetRowData(2));
    Snprintf(office_access_group_info.office_company_uuid, sizeof(office_access_group_info.office_company_uuid), query.GetRowData(3));
    office_access_group_info.scheduler_type = ATOI(query.GetRowData(4));
    Snprintf(office_access_group_info.begin_date_time, sizeof(office_access_group_info.begin_date_time), query.GetRowData(5));
    Snprintf(office_access_group_info.end_date_time, sizeof(office_access_group_info.end_date_time), query.GetRowData(6));
    office_access_group_info.is_default = ATOI(query.GetRowData(7));
    office_access_group_info.date_flag = ATOI(query.GetRowData(8));
    Snprintf(office_access_group_info.start_time, sizeof(office_access_group_info.start_time), query.GetRowData(9));
    Snprintf(office_access_group_info.stop_time, sizeof(office_access_group_info.stop_time), query.GetRowData(10));   
    office_access_group_info.id = ATOI(query.GetRowData(11));
    return;
}

int OfficeAccessGroup::GetOfficeAccessGroupByUUID(const std::string& uuid, AccessGroupUUIDMap& office_access_group_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_access_group_info_sec << " from OfficeAccessGroup A left join OfficeAccessGroupScheduler S on S.OfficeAccessGroupUUID=A.UUID where A.UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeAccessGroupInfo info;
        GetOfficeAccessGroupFromSql(info, query);
        office_access_group_info.insert(std::make_pair(info.uuid, info));
    }
    return 0;
}


int OfficeAccessGroup::GetOfficeAccessGroupByCompanyUUID(const std::string& commpany_uuid, AccessGroupUUIDMap& company_acc_map)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_access_group_info_sec << " from OfficeAccessGroup A left join OfficeAccessGroupScheduler S on S.OfficeAccessGroupUUID=A.UUID where A.OfficeCompanyUUID = '" << commpany_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeAccessGroupInfo info;
        GetOfficeAccessGroupFromSql(info, query);
        company_acc_map.insert(std::make_pair(info.uuid, info)); 
    }
    return 0;
}


int OfficeAccessGroup::GetOfficeAccessGroupByProjectUUID(const std::string& project_uuid, AccessGroupUUIDMap& project_acc_map)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_access_group_info_sec << " from OfficeAccessGroup A left join OfficeAccessGroupScheduler S on S.OfficeAccessGroupUUID=A.UUID where A.AccountUUID = '" << project_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeAccessGroupInfo info;
        GetOfficeAccessGroupFromSql(info, query);
        project_acc_map.insert(std::make_pair(info.uuid, info));           
    }
    return 0;
}

int OfficeAccessGroup::GetGroupAccessGroupByDeviceUUID(const std::string& dev_uuid, AkcsStringSet &ag_list)
{
    std::stringstream stream_sql;
    stream_sql << "select G.OfficeAccessGroupUUID from OfficeAccessGroup A left join OfficeAccessGroupDevice D on D.OfficeAccessGroupUUID=A.UUID "
        <<"left join OfficeGroupAccessGroup G on G.OfficeAccessGroupUUID=A.UUID where D.DevicesUUID = '" << dev_uuid << "'";
    
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        ag_list.insert(query.GetRowData(0));
    }
    return 0;    
}

int OfficeAccessGroup::GetDevListByAccessGroupUUID(const std::string& ag_uuid, AkcsStringSet& dev_uuid_list)
{
    std::stringstream stream_sql;
    stream_sql << "select D.DevicesUUID  from OfficeAccessGroup A left join OfficeAccessGroupDevice D on D.OfficeAccessGroupUUID=A.UUID where A.UUID = '" << ag_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        dev_uuid_list.insert(query.GetRowData(0));
    }
    return 0;
}

std::string OfficeAccessGroup::GetProjectUUIDByAgUUID(const std::string& uuid)
{
    std::string project_uuid;
    std::stringstream stream_sql;
    stream_sql << "select AccountUUID  from OfficeAccessGroup where UUID = '" << uuid << "'";
    
    GET_DB_CONN_ERR_RETURN(db_conn, "");

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        project_uuid = query.GetRowData(0);
    }
    return std::move(project_uuid);
}

}