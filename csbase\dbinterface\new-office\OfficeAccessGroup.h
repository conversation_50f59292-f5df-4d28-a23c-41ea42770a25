#ifndef __DB_OFFICE_ACCESS_GROUP_H__
#define __DB_OFFICE_ACCESS_GROUP_H__

#include <vector>
#include <string>
#include <memory>
#include <map>
#include "BasicDefine.h"
#include "util.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct OfficeAccessGroupInfo_T
{
    uint32_t id;//这个id是权限组列表的id，因为一个权限组有多个时间段，设备没办法一个权限组多个时间段，只能拆开
    char uuid[36];
    char name[64];
    char project_uuid[36];
    char office_company_uuid[36];
    int scheduler_type;
    int is_default;

    char begin_date_time[32];
    char end_date_time[32];
    int date_flag;
    char start_time[32];
    char stop_time[32];    
    OfficeAccessGroupInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} OfficeAccessGroupInfo;


using AccessGroupUUIDMap = std::multimap<std::string/*AccessGroupUUID*/, OfficeAccessGroupInfo>;
using OfficeAccessGroupInfoList = std::list<OfficeAccessGroupInfo>;
using OfficeAccessGroupUUIDList = std::vector<std::string/*AccessGroupUUID*/>;
namespace dbinterface {

class OfficeAccessGroup
{
public:
    static int GetOfficeAccessGroupByUUID(const std::string& uuid, AccessGroupUUIDMap& office_access_group_info);
    static int GetOfficeAccessGroupByCompanyUUID(const std::string& commpany_uuid, AccessGroupUUIDMap& company_acc_map);
    static int GetOfficeAccessGroupByProjectUUID(const std::string& project_uuid, AccessGroupUUIDMap& project_acc_map);

    static int GetGroupAccessGroupByDeviceUUID(const std::string& dev_uuid, AkcsStringSet &ag_list);
    static int GetDevListByAccessGroupUUID(const std::string& uuid, AkcsStringSet& dev_uuid_list);
    static std::string GetProjectUUIDByAgUUID(const std::string& uuid);
private:
    OfficeAccessGroup() = delete;
    ~OfficeAccessGroup() = delete;
    static void GetOfficeAccessGroupFromSql(OfficeAccessGroupInfo& office_access_group_info, CRldbQuery& query);
};

}
#endif