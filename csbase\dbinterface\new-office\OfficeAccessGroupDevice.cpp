#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "OfficeAccessGroupDevice.h"

namespace dbinterface {

static const std::string office_access_group_device_info_sec = " <PERSON><PERSON>U<PERSON>,D.OfficeAccessGroupUUID,D.<PERSON>,D.<PERSON>,D.SecurityRelay ";

void OfficeAccessGroupDevice::GetOfficeAccessGroupDeviceFromSql(OfficeAccessGroupDeviceInfo& office_access_group_device_info, CRldbQuery& query)
{
    Snprintf(office_access_group_device_info.uuid, sizeof(office_access_group_device_info.uuid), query.GetRowData(0));
    Snprintf(office_access_group_device_info.office_access_group_uuid, sizeof(office_access_group_device_info.office_access_group_uuid), query.GetRowData(1));
    Snprintf(office_access_group_device_info.devices_uuid, sizeof(office_access_group_device_info.devices_uuid), query.GetRowData(2));
    office_access_group_device_info.relay = ATOI(query.GetRowData(3));
    office_access_group_device_info.security_relay = ATOI(query.GetRowData(4));
    return;
}


int OfficeAccessGroupDevice::GetAccessGroupDevicesByProjectUUID(const std::string& project_uuid, AgDevInfoDevMap& ag_dev_map, AgDevInfoUUIDMap& ag_uuid_map)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_access_group_device_info_sec << " from OfficeAccessGroupDevice D left join OfficeAccessGroup O on D.OfficeAccessGroupUUID=O.UUID where O.AccountUUID = '" << project_uuid << "'";

    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeAccessGroupDeviceInfo info;
        GetOfficeAccessGroupDeviceFromSql(info, query);
        ag_dev_map.insert(std::make_pair(info.devices_uuid, info)); 
        ag_uuid_map.insert(std::make_pair(info.office_access_group_uuid, info)); 
    }
    return 0;    
}

int OfficeAccessGroupDevice::GetPersonnelAccessGroupDeviceList(const std::string& personal_account_uuid, AgDevInfoDevMap& ag_dev_map)
{
    std::stringstream stream_sql;
    stream_sql << "SELECT " << office_access_group_device_info_sec << "FROM OfficePersonnelGroup AS OPG "
               << "LEFT JOIN OfficeGroupAccessGroup AS OGAG ON OPG.OfficeGroupUUID = OGAG.OfficeGroupUUID "
               << "LEFT JOIN OfficeAccessGroup AS OAG ON OGAG.OfficeAccessGroupUUID = OAG.UUID "
               << "LEFT JOIN OfficeAccessGroupDevice AS D ON OAG.UUID = D.OfficeAccessGroupUUID "
               << "WHERE OPG.PersonalAccountUUID = '" << personal_account_uuid << "'AND D.UUID IS NOT NULL";

    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeAccessGroupDeviceInfo info;
        GetOfficeAccessGroupDeviceFromSql(info, query);
        ag_dev_map.insert(std::make_pair(info.devices_uuid, info)); 
    }
    return 0;
}


}
