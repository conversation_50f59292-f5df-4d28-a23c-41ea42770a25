#ifndef __DB_OFFICE_ACCESS_GROUP_DEVICE_H__
#define __DB_OFFICE_ACCESS_GROUP_DEVICE_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct OfficeAccessGroupDeviceInfo_T
{
    char uuid[36];
    char office_access_group_uuid[36];
    char devices_uuid[36];
    int relay;
    int security_relay;
    OfficeAccessGroupDeviceInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} OfficeAccessGroupDeviceInfo;



using AgDevInfoUUIDMap = std::multimap<std::string/*AccessGroupUUID*/, OfficeAccessGroupDeviceInfo>;
using AgDevInfoDevMap = std::multimap<std::string/*devicesUUID*/, OfficeAccessGroupDeviceInfo>;
using AgInfoList = std::vector<OfficeAccessGroupDeviceInfo>;

namespace dbinterface {
/*
通过权限组获取设备列表
通过设备获取权限组列表
*/
class OfficeAccessGroupDevice
{
public:
    //项目下所有的权限组列表
    static int GetAccessGroupDevicesByProjectUUID(const std::string& project_uuid, AgDevInfoDevMap& ag_dev_map, AgDevInfoUUIDMap& ag_uuid_map);
    static int GetPersonnelAccessGroupDeviceList(const std::string& personal_account_uuid, AgDevInfoDevMap& ag_dev_map);
    
private:
    OfficeAccessGroupDevice() = delete;
    ~OfficeAccessGroupDevice() = delete;
    static void GetOfficeAccessGroupDeviceFromSql(OfficeAccessGroupDeviceInfo& office_access_group_device_info, CRldbQuery& query);
};

}
#endif
