#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "OfficeAccessGroupHoliday.h"

namespace dbinterface {

static const std::string office_access_group_holiday_info_sec = " UUID,OfficeAccessGroupUUID,OfficeHolidayUUID ";

void OfficeAccessGroupHoliday::GetOfficeAccessGroupHolidayFromSql(OfficeAccessGroupHolidayInfo& office_access_group_holiday_info, CRldbQuery& query)
{
    Snprintf(office_access_group_holiday_info.uuid, sizeof(office_access_group_holiday_info.uuid), query.GetRowData(0));
    Snprintf(office_access_group_holiday_info.office_access_group_uuid, sizeof(office_access_group_holiday_info.office_access_group_uuid), query.GetRowData(1));
    Snprintf(office_access_group_holiday_info.office_holiday_uuid, sizeof(office_access_group_holiday_info.office_holiday_uuid), query.GetRowData(2));
    return;
}

int OfficeAccessGroupHoliday::GetOfficeAccessGroupHolidayByOfficeAccessGroupUUID(const std::string& office_access_group_uuid, OfficeAccessGroupHolidayInfo& office_access_group_holiday_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_access_group_holiday_info_sec << " from OfficeAccessGroupHoliday where OfficeAccessGroupUUID = '" << office_access_group_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetOfficeAccessGroupHolidayFromSql(office_access_group_holiday_info, query);
    }
    else
    {
        AK_LOG_WARN << "get OfficeAccessGroupHolidayInfo by OfficeAccessGroupUUID failed, OfficeAccessGroupUUID = " << office_access_group_uuid;
        return -1;
    }
    return 0;
}

int OfficeAccessGroupHoliday::GetOfficeAccessGroupHolidayByOfficeHolidayUUID(const std::string& office_holiday_uuid, OfficeAccessGroupHolidayInfo& office_access_group_holiday_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_access_group_holiday_info_sec << " from OfficeAccessGroupHoliday where OfficeHolidayUUID = '" << office_holiday_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetOfficeAccessGroupHolidayFromSql(office_access_group_holiday_info, query);
    }
    else
    {
        AK_LOG_WARN << "get OfficeAccessGroupHolidayInfo by OfficeHolidayUUID failed, OfficeHolidayUUID = " << office_holiday_uuid;
        return -1;
    }
    return 0;
}


}