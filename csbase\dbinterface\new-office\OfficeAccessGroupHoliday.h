#ifndef __DB_OFFICE_ACCESS_GROUP_HOLIDAY_H__
#define __DB_OFFICE_ACCESS_GROUP_HOLIDAY_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct OfficeAccessGroupHolidayInfo_T
{
    char uuid[36];
    char office_access_group_uuid[36];
    char office_holiday_uuid[36];
    OfficeAccessGroupHolidayInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} OfficeAccessGroupHolidayInfo;

namespace dbinterface {

class OfficeAccessGroupHoliday
{
public:
    static int GetOfficeAccessGroupHolidayByOfficeAccessGroupUUID(const std::string& office_access_group_uuid, OfficeAccessGroupHolidayInfo& office_access_group_holiday_info);
    static int GetOfficeAccessGroupHolidayByOfficeHolidayUUID(const std::string& office_holiday_uuid, OfficeAccessGroupHolidayInfo& office_access_group_holiday_info);

private:
    OfficeAccessGroupHoliday() = delete;
    ~OfficeAccessGroupHoliday() = delete;
    static void GetOfficeAccessGroupHolidayFromSql(OfficeAccessGroupHolidayInfo& office_access_group_holiday_info, CRldbQuery& query);
};

}
#endif