#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "dbinterface/new-office/OfficePersonnelGroup.h"
#include "dbinterface/new-office/OfficeGroupAccessGroup.h"
#include "dbinterface/new-office/OfficeAccessGroupDevice.h"
#include "dbinterface/new-office/OfficeAccessGroupPersonnel.h"

namespace dbinterface {

static const std::string office_access_group_personnel_info_sec = " UUID,PersonalAccountUUID,OfficeAccessGroupUUID ";

void OfficeAccessGroupPersonnel::GetOfficeAccessGroupPersonnelFromSql(OfficeAccessGroupPersonnelInfo& office_access_group_personnel_info, CRldbQuery& query)
{
    Snprintf(office_access_group_personnel_info.uuid, sizeof(office_access_group_personnel_info.uuid), query.GetRowData(0));
    Snprintf(office_access_group_personnel_info.personal_account_uuid, sizeof(office_access_group_personnel_info.personal_account_uuid), query.GetRowData(1));
    Snprintf(office_access_group_personnel_info.office_access_group_uuid, sizeof(office_access_group_personnel_info.office_access_group_uuid), query.GetRowData(2));
    return;
}

int OfficeAccessGroupPersonnel::GetOfficeAccessGroupPersonnelByOfficeAccessGroupUUID(const std::string& office_access_group_uuid, OfficeAccessGroupPersonnelInfo& office_access_group_personnel_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_access_group_personnel_info_sec << " from OfficeAccessGroupPersonnel where OfficeAccessGroupUUID = '" << office_access_group_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetOfficeAccessGroupPersonnelFromSql(office_access_group_personnel_info, query);
    }
    else
    {
        AK_LOG_WARN << "get OfficeAccessGroupPersonnelInfo by OfficeAccessGroupUUID failed, OfficeAccessGroupUUID = " << office_access_group_uuid;
        return -1;
    }
    return 0;
}

int OfficeAccessGroupPersonnel::GetOfficeAccessGroupPersonnelByPersonalAccountUUID(const std::string& personal_account_uuid, OfficeAccessGroupPersonnelInfo& office_access_group_personnel_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_access_group_personnel_info_sec << " from OfficeAccessGroupPersonnel where PersonalAccountUUID = '" << personal_account_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetOfficeAccessGroupPersonnelFromSql(office_access_group_personnel_info, query);
    }
    else
    {
        AK_LOG_WARN << "get OfficeAccessGroupPersonnelInfo by PersonalAccountUUID failed, PersonalAccountUUID = " << personal_account_uuid;
        return -1;
    }
    return 0;
}

// 获取personnel设备有权限的relay value
void OfficeAccessGroupPersonnel::GetPersonnelAccessGroupDevRelay(const std::string& personal_account_uuid, const std::string& devices_uuid, int& relay, int& security_realy)
{
    int access_relay = 0;
    int access_security_relay = 0;
    
    AgDevInfoDevMap ag_dev_map;
    if (0 == dbinterface::OfficeAccessGroupDevice::GetPersonnelAccessGroupDeviceList(personal_account_uuid, ag_dev_map))
    {
        auto range = ag_dev_map.equal_range(devices_uuid);
        for (auto it = range.first; it != range.second; ++it)
        {
            const OfficeAccessGroupDeviceInfo& device_info = it->second;
            access_relay = access_relay | device_info.relay;
            access_security_relay = access_security_relay | device_info.security_relay;
        }
    }

    relay = access_relay;
    security_realy = access_security_relay;
    return;
}

}
