#ifndef __DB_OFFICE_ACCESS_GROUP_PERSONNEL_H__
#define __DB_OFFICE_ACCESS_GROUP_PERSONNEL_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"



typedef struct OfficeAccessGroupPersonnelInfo_T
{
    char uuid[36];
    char personal_account_uuid[36];
    char office_access_group_uuid[36];
    OfficeAccessGroupPersonnelInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} OfficeAccessGroupPersonnelInfo;


using ProjectPerGroupMap = std::multimap<std::string/*per uuid*/, std::string/*group uuid*/>;


namespace dbinterface {

class OfficeAccessGroupPersonnel
{
public:
    static int GetOfficeAccessGroupPersonnelByOfficeAccessGroupUUID(const std::string& office_access_group_uuid, OfficeAccessGroupPersonnelInfo& office_access_group_personnel_info);
    static int GetOfficeAccessGroupPersonnelByPersonalAccountUUID(const std::string& personal_account_uuid, OfficeAccessGroupPersonnelInfo& office_access_group_personnel_info);
    static void GetPersonnelAccessGroupDevRelay(const std::string& personal_account_uuid, const std::string& devices_uuid, int& relay, int& security_realy);

private:
    OfficeAccessGroupPersonnel() = delete;
    ~OfficeAccessGroupPersonnel() = delete;
    static void GetOfficeAccessGroupPersonnelFromSql(OfficeAccessGroupPersonnelInfo& office_access_group_personnel_info, CRldbQuery& query);
};

}
#endif
