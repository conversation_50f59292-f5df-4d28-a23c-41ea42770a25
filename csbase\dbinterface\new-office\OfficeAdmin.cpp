#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "OfficeAdmin.h"

namespace dbinterface {

static const std::string office_admin_info_sec = " UUID,OfficeUUID,OfficeCompanyUUID,AccountUUID,Name,FirstName,LastName,PhoneCode,Role,PersonalAccountUUID,CallType,AppStatus ";

void OfficeAdmin::GetOfficeAdminFromSql(OfficeAdminInfo& office_admin_info, CRldbQuery& query)
{
    Snprintf(office_admin_info.uuid, sizeof(office_admin_info.uuid), query.GetRowData(0));
    Snprintf(office_admin_info.office_uuid, sizeof(office_admin_info.office_uuid), query.GetRowData(1));
    Snprintf(office_admin_info.office_company_uuid, sizeof(office_admin_info.office_company_uuid), query.GetRowData(2));
    Snprintf(office_admin_info.account_uuid, sizeof(office_admin_info.account_uuid), query.GetRowData(3));
    Snprintf(office_admin_info.name, sizeof(office_admin_info.name), query.GetRowData(4));
    Snprintf(office_admin_info.first_name, sizeof(office_admin_info.first_name), query.GetRowData(5));
    Snprintf(office_admin_info.last_name, sizeof(office_admin_info.last_name), query.GetRowData(6));
    Snprintf(office_admin_info.phone_code, sizeof(office_admin_info.phone_code), query.GetRowData(7));
    office_admin_info.role = ATOI(query.GetRowData(8));
    Snprintf(office_admin_info.personal_account_uuid, sizeof(office_admin_info.personal_account_uuid), query.GetRowData(9));
    office_admin_info.call_type = ATOI(query.GetRowData(10));
    office_admin_info.app_status = ATOI(query.GetRowData(11));
    return;
}

int OfficeAdmin::GetOfficeAdminByAccountUUID(const std::string& account_uuid, OfficeAdminInfo& office_admin_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_admin_info_sec << " from OfficeAdmin where AccountUUID = '" << account_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetOfficeAdminFromSql(office_admin_info, query);
    }
    else
    {
        AK_LOG_WARN << "get OfficeAdminInfo by AccountUUID failed, AccountUUID = " << account_uuid;
        return -1;
    }
    return 0;
}

int OfficeAdmin::GetOfficeAdminInfoListByOfficeUUID(const std::string& project_uuid, OfficeAdminInfoList& office_admin_info_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_admin_info_sec << " from OfficeAdmin where OfficeUUID = '" << project_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeAdminInfo info;
        GetOfficeAdminFromSql(info, query);
        office_admin_info_list.push_back(info);
    }
    return 0;
}

int OfficeAdmin::GetOfficeAdminInfoListByCompanyUUID(const std::string& company_uuid, OfficeAdminInfoList& office_admin_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_admin_info_sec << " from OfficeAdmin where OfficeCompanyUUID = '" << company_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeAdminInfo info;
        GetOfficeAdminFromSql(info, query);
        office_admin_list.push_back(info);
    }
    return 0;
}

int OfficeAdmin::GetOfficeAdminByPersonalAccountUUID(const std::string& personal_account_uuid, OfficeAdminInfo& office_admin_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_admin_info_sec << " from OfficeAdmin where PersonalAccountUUID = '" << personal_account_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetOfficeAdminFromSql(office_admin_info, query);
    }
    else
    {
        AK_LOG_WARN << "get OfficeAdminInfo by idx failed, personal account uuid = " << personal_account_uuid;
        return -1;
    }
    return 0;
}

int OfficeAdmin::GetOfficeAdminPerMapByProjectUUID(const std::string& project_uuid, OfficeAdminMap& per_map,  OfficeCompanyAdminMap& company_admin_map)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_admin_info_sec << " from OfficeAdmin where OfficeUUID = '" << project_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());

    while (query.MoveToNextRow())
    {
        OfficeAdminInfo info;
        GetOfficeAdminFromSql(info, query);
        per_map.insert(std::make_pair(info.personal_account_uuid, info)); 
        company_admin_map.insert(std::make_pair(info.office_company_uuid, info)); 
    }
    return 0;    
}

}
