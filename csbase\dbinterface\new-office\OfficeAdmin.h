#ifndef __DB_OFFICE_ADMIN_H__
#define __DB_OFFICE_ADMIN_H__

#include <map>
#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "util.h"
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct OfficeAdminInfo_T
{
    uint32_t    id;                         // '主键',
    char        uuid[36];                   // 'uuid，去除 -',
    char        office_uuid[36];            // 'office 在 account 的 uuid，去除 -',
    char        office_company_uuid[36];    // 'company uuid，去除 -',
    char        account_uuid[36];           // 'admin 在 account 的 uuid，去除 -',
    char        name[512];                  // varchar(512) not null default '',
    char        first_name[256];            // varchar(256) not null default '',
    char        last_name[256];             // varchar(256) not null default '',
    char        phone_code[8];              // '区号',
    uint32_t    role;                       // '管理员角色，1:公司管理员',
    char        create_time[32];            // '创建时间',
    char        update_time[32];            // '更新时间',
    char        personal_account_uuid[36];  // 'admin app对应的personalaccount',
    uint32_t    call_type;                  // '呼叫顺序和类型 呼叫顺序和类型 0=app 1=phone 2=app先 未接听后phone',
    uint32_t    app_status;                 // 'admin app开关开启状态 0:关闭, 1:开启',
    OfficeAdminInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} OfficeAdminInfo;

using OfficeAdminInfoList = std::vector<OfficeAdminInfo>;
using OfficeAdminMap = std::map<std::string/*per uuid*/, OfficeAdminInfo>;
using OfficeCompanyAdminMap = std::multimap<std::string/*company uuid*/, OfficeAdminInfo>;
using OfficeAdminInfoUUIDList = std::vector<std::string/*DevicesDoorUUID*/>;
using OfficeAdminInfoUUIDMap = std::multimap<std::string/*DevicesDoorUUID*/, OfficeAdminInfo>;

namespace dbinterface
{
    class OfficeAdmin
    {
        public:
            static int GetOfficeAdminInfoListByCompanyUUID(const std::string& company_uuid, OfficeAdminInfoList& office_admin_info_list);
            static int GetOfficeAdminByAccountUUID(const std::string& account_uuid, OfficeAdminInfo& office_admin_info);
            static int GetOfficeAdminByPersonalAccountUUID(const std::string& personal_account_uuid, OfficeAdminInfo& office_admin_info);
            static int GetOfficeAdminPerMapByProjectUUID(const std::string& project_uuid, OfficeAdminMap& per_map,              OfficeCompanyAdminMap& company_admin_map);
            static int GetOfficeAdminInfoListByOfficeUUID(const std::string& project_uuid, OfficeAdminInfoList& office_admin_info_list);

        private:
            OfficeAdmin() = delete;
            ~OfficeAdmin() = delete;
            static void GetOfficeAdminInfoFromSql(OfficeAdminInfo& office_access_group_info, CRldbQuery& query);
            static void GetOfficeAdminFromSql(OfficeAdminInfo& office_admin_info, CRldbQuery& query);
    };

}
#endif
