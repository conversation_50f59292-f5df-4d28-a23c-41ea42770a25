#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "OfficeAdminGroup.h"

namespace dbinterface {

static const std::string office_admin_group_info_sec = " <PERSON><PERSON>,O<PERSON>,O.PersonalAccountUUID,O.OfficeGroupUUID ";

void OfficeAdminGroup::GetOfficeAdminGroupFromSql(OfficeAdminGroupInfo& office_admin_group_info, CRldbQuery& query)
{
    Snprintf(office_admin_group_info.uuid, sizeof(office_admin_group_info.uuid), query.GetRowData(0));
    Snprintf(office_admin_group_info.account_uuid, sizeof(office_admin_group_info.account_uuid), query.GetRowData(1));
    Snprintf(office_admin_group_info.personal_account_uuid, sizeof(office_admin_group_info.personal_account_uuid), query.GetRowData(2));
    Snprintf(office_admin_group_info.office_group_uuid, sizeof(office_admin_group_info.office_group_uuid), query.GetRowData(3));
    return;
}

int OfficeAdminGroup::GetofficeAdminGroupByProjectUUID(const std::string& project_uuid, GroupOfAdminPerMap& group_of_admin_per_map, GroupOfAdminGroupMap& group_of_admin_group_map)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_admin_group_info_sec << " from OfficeAdminGroup O left join PersonalAccount P on P.UUID=O.PersonalAccountUUID where P.ParentUUID = '" << project_uuid << "'";

    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeAdminGroupInfo info;
        GetOfficeAdminGroupFromSql(info, query);
        group_of_admin_group_map.insert(std::make_pair(info.office_group_uuid, info));
        group_of_admin_per_map.insert(std::make_pair(info.personal_account_uuid, info));    
    }

    return 0;    
}

int OfficeAdminGroup::GetOfficeAdminGroupListByPersonalAccountUUID(const std::string& per_uuid, OfficeAdminGroupInfoList& office_admin_group_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_admin_group_info_sec << " from OfficeAdminGroup O where O.PersonalAccountUUID = '" << per_uuid << "'";

    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeAdminGroupInfo info;
        GetOfficeAdminGroupFromSql(info, query);
        office_admin_group_list.emplace_back(info);
    }

    return 0;
}

}