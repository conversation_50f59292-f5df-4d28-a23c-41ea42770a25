#ifndef __DB_OFFICE_ADMIN_GROUP_H__
#define __DB_OFFICE_ADMIN_GROUP_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct OfficeAdminGroupInfo_T
{
    char uuid[36];
    char account_uuid[36];
    char personal_account_uuid[36];
    char office_group_uuid[36];
    OfficeAdminGroupInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} OfficeAdminGroupInfo;

typedef std::vector<OfficeAdminGroupInfo> OfficeAdminGroupInfoList;

/* group 作为key*/
using GroupOfAdminGroupMap = std::multimap<std::string/*group uuid*/, OfficeAdminGroupInfo>;
/* per uuid 作为key*/
using GroupOfAdminPerMap = std::multimap<std::string/*per uuid*/, OfficeAdminGroupInfo>;

namespace dbinterface {

class OfficeAdminGroup
{
public:
    static int GetofficeAdminGroupByProjectUUID(const std::string& project_uuid, GroupOfAdminPerMap& group_of_admin_per_map, GroupOfAdminGroupMap& group_of_admin_group_map);
    static int GetOfficeAdminGroupListByPersonalAccountUUID(const std::string& per_uuid, OfficeAdminGroupInfoList& office_admin_group_list);

private:
    OfficeAdminGroup() = delete;
    ~OfficeAdminGroup() = delete;
    static void GetOfficeAdminGroupFromSql(OfficeAdminGroupInfo& office_admin_group_info, CRldbQuery& query);
};

}
#endif