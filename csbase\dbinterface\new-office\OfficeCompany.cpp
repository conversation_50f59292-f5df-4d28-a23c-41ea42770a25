#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "OfficeCompany.h"
#include "AkcsCommonDef.h"

namespace dbinterface {

static const std::string office_company_info_sec = " UUID,AccountUUID,Name ";

void OfficeCompany::GetOfficeCompanyFromSql(OfficeCompanyInfo& office_company_info, CRldbQuery& query)
{
    Snprintf(office_company_info.uuid, sizeof(office_company_info.uuid), query.GetRowData(0));
    Snprintf(office_company_info.project_uuid, sizeof(office_company_info.project_uuid), query.GetRowData(1));
    Snprintf(office_company_info.name, sizeof(office_company_info.name), query.GetRowData(2));
    return;
}

int OfficeCompany::GetOfficeCompanyByUUID(const std::string& uuid, OfficeCompanyInfo& office_company_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_company_info_sec << " from OfficeCompany where UUID = '" << uuid << "'";

    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetOfficeCompanyFromSql(office_company_info, query);
    }
    else
    {
        AK_LOG_WARN << "get OfficeCompanyInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}

int OfficeCompany::GetOfficeCompanyByProjectUUID(const std::string& project_uuid, ProjectCompanyMap& company_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_company_info_sec << " from OfficeCompany where AccountUUID = '" << project_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeCompanyInfo info;
        GetOfficeCompanyFromSql(info, query);
        company_list.insert(std::make_pair(info.uuid, info));    
    }
    return 0;
}

std::string OfficeCompany::GetOfficeCompanyUUIDByPerUUIDAndRole(const std::string& per_uuid, int role)
{
    std::string office_company_uuid;
    std::string user_table_name;
    if (role == ACCOUNT_ROLE_OFFICE_NEW_PER)
    {
        user_table_name = "OfficePersonnel";
    }
    else if (role == ACCOUNT_ROLE_OFFICE_NEW_ADMIN)
    {
        user_table_name = "OfficeAdmin";
    }

    if (user_table_name.empty())
    {
        AK_LOG_WARN << "invalid role: " << role << ", per_uuid: " << per_uuid;
        return "";
    }

    std::stringstream stream_sql;
    stream_sql << "select OfficeCompanyUUID from " << user_table_name << " where PersonalAccountUUID = '" << per_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, "")

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        office_company_uuid = query.GetRowData(0);
    }
    return office_company_uuid;
}

}