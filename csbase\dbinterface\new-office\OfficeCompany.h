#ifndef __DB_OFFICE_COMPANY_H__
#define __DB_OFFICE_COMPANY_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include <map>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct OfficeCompanyInfo_T
{
    char uuid[64];
    char project_uuid[64];
    char name[255];
    
    OfficeCompanyInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} OfficeCompanyInfo;

using ProjectCompanyMap = std::map<std::string/*CompanyUUID*/, OfficeCompanyInfo>;

namespace dbinterface {

class OfficeCompany
{
public:
    static int GetOfficeCompanyByUUID(const std::string& uuid, OfficeCompanyInfo& office_company_info);
    static int GetOfficeCompanyByProjectUUID(const std::string& project_uuid, ProjectCompanyMap& company_list);
    static std::string GetOfficeCompanyUUIDByPerUUIDAndRole(const std::string& per_uuid, int role);

private:
    OfficeCompany() = delete;
    ~OfficeCompany() = delete;
    static void GetOfficeCompanyFromSql(OfficeCompanyInfo& office_company_info, CRldbQuery& query);
};

}
#endif