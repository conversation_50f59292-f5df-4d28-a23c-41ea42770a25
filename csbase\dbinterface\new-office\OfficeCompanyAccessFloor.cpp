#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "dbinterface/new-office/OfficeCompanyAccessFloor.h"

namespace dbinterface {

static const std::string office_company_access_floor_info_sec = " F.UUID,F.OfficeCompanyUUID,F.CommunityUnitUUID,F.AccessFloors ";

void OfficeCompanyAccessFloor::GetOfficeCompanyAccessFloorFromSql(OfficeCompanyAccessFloorInfo& office_company_access_floor_info, CRldbQuery& query)
{
    Snprintf(office_company_access_floor_info.uuid, sizeof(office_company_access_floor_info.uuid), query.GetRowData(0));
    Snprintf(office_company_access_floor_info.office_company_uuid, sizeof(office_company_access_floor_info.office_company_uuid), query.GetRowData(1));
    Snprintf(office_company_access_floor_info.community_unit_uuid, sizeof(office_company_access_floor_info.community_unit_uuid), query.GetRowData(2));
    Snprintf(office_company_access_floor_info.access_floors, sizeof(office_company_access_floor_info.access_floors), query.GetRowData(3));
    return;
}

int OfficeCompanyAccessFloor::GetOfficeCompanyAccessFloorByProjectUUID(const std::string& project_uuid, OfficeCompanyAccessFloorMap& office_company_access_floor_map)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_company_access_floor_info_sec << " from OfficeCompanyAccessFloor F left join OfficeCompany C on C.UUID=F.OfficeCompanyUUID where C.AccountUUID = '" << project_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeCompanyAccessFloorInfo info;
        GetOfficeCompanyAccessFloorFromSql(info, query);
        office_company_access_floor_map.insert(std::make_pair(info.office_company_uuid, info));
    } 
    return 0;
}

int OfficeCompanyAccessFloor::GetOfficeCompanyAccessFloorByOfficeCompanyUUID(const std::string& office_company_uuid, OfficeCompanyAccessFloorList& office_company_access_floor_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_company_access_floor_info_sec << " from OfficeCompanyAccessFloor F where OfficeCompanyUUID = '" << office_company_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeCompanyAccessFloorInfo office_company_access_floor_info;
        GetOfficeCompanyAccessFloorFromSql(office_company_access_floor_info, query);
        office_company_access_floor_list.push_back(office_company_access_floor_info);
    }
    
    return 0;
}


}
