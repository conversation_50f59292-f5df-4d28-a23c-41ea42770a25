#ifndef __DB_OFFICE_COMPANY_ACCESS_FLOOR_H__
#define __DB_OFFICE_COMPANY_ACCESS_FLOOR_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct OfficeCompanyAccessFloorInfo_T
{
    char uuid[36];
    char office_company_uuid[36];
    char community_unit_uuid[36];
    char access_floors[512];
    OfficeCompanyAccessFloorInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} OfficeCompanyAccessFloorInfo;

using OfficeCompanyAccessFloorList = std::vector<OfficeCompanyAccessFloorInfo>;
using OfficeCompanyAccessFloorMap = std::multimap<std::string/*office_company_uuid*/, OfficeCompanyAccessFloorInfo>;

namespace dbinterface {

class OfficeCompanyAccessFloor
{
public:
    static int GetOfficeCompanyAccessFloorByProjectUUID(const std::string& project_uuid, OfficeCompanyAccessFloorMap& office_company_access_floor_map);
    static int GetOfficeCompanyAccessFloorByOfficeCompanyUUID(const std::string& office_company_uuid, OfficeCompanyAccessFloorList& office_company_access_floor_list);

private:
    OfficeCompanyAccessFloor() = delete;
    ~OfficeCompanyAccessFloor() = delete;
    static void GetOfficeCompanyAccessFloorFromSql(OfficeCompanyAccessFloorInfo& office_company_access_floor_info, CRldbQuery& query);
};

}
#endif