#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "OfficeCompanyDoorList.h"

namespace dbinterface
{
static const std::string office_company_door_list_info_sec = " UUID,DevicesUUID,DevicesDoorListUUID,OfficeCompanyUUID,Type,ToPublicKey ";

void OfficeCompanyDoorList::GetOfficeCompanyDoorListFromSql(OfficeCompanyDoorListInfo& office_company_door_list_info, CRldbQuery& query)
{
    Snprintf(office_company_door_list_info.uuid, sizeof(office_company_door_list_info.uuid), query.GetRowData(0));
    Snprintf(office_company_door_list_info.devices_uuid, sizeof(office_company_door_list_info.devices_uuid), query.GetRowData(1));
    Snprintf(office_company_door_list_info.devices_door_list_uuid, sizeof(office_company_door_list_info.devices_door_list_uuid), query.GetRowData(2));
    Snprintf(office_company_door_list_info.office_company_uuid, sizeof(office_company_door_list_info.office_company_uuid), query.GetRowData(3));
    office_company_door_list_info.type = (OfficeCompanyDoorListInfoType)ATOI(query.GetRowData(4));
    Snprintf(office_company_door_list_info.to_public_key, sizeof(office_company_door_list_info.to_public_key), query.GetRowData(5));
    return;
}

//获取public door(relay粒度)的devices_door_list_uuid
int OfficeCompanyDoorList::GetPubDoorUUIDsByProjectUUID(const std::string& project_uuid, std::set<std::string>& door_uuid_set)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_company_door_list_info_sec
                << " from OfficeCompanyDoorList where Type=0 and AccountUUID = '" << project_uuid << "'";

    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeCompanyDoorListInfo door_list_info;
        GetOfficeCompanyDoorListFromSql(door_list_info, query);
        door_uuid_set.insert(door_list_info.devices_door_list_uuid);
    }

    return 0;
}

// 获取设备的door list
int OfficeCompanyDoorList::GetDeviceOfficeCompanyDoorList(const std::string& devices_uuid, OfficeCompanyDoorListInfoList& office_company_door_list_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_company_door_list_info_sec << " from OfficeCompanyDoorList where DevicesUUID = '" << devices_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeCompanyDoorListInfo info;
        GetOfficeCompanyDoorListFromSql(info, query);
        office_company_door_list_list.push_back(info);
    }
    return 0;
}

int OfficeCompanyDoorList::GePrivateDoorCompanyByDevicesUUID(const std::string& devices_uuid, std::set<std::string>& company_set)
{

    std::stringstream stream_sql;
    stream_sql << "SELECT DISTINCT OfficeCompanyUUID from OfficeCompanyDoorList where Type=1 and DevicesUUID = '" << devices_uuid << "'";

    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        company_set.insert(query.GetRowData(0));
    }

    return 0;
}

int OfficeCompanyDoorList::GetOfficeCompanyDoorListByDevicesUUID(const std::string& devices_uuid, OfficeCompanyDoorListInfoList& office_company_door_list_list)
{
    std::stringstream stream_sql;   
    stream_sql << "select " << office_company_door_list_info_sec << " from OfficeCompanyDoorList where DevicesUUID = '" << devices_uuid << "'";

    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    
    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeCompanyDoorListInfo info;
        GetOfficeCompanyDoorListFromSql(info, query);
        office_company_door_list_list.push_back(info);
    }
    return 0;
}

}
