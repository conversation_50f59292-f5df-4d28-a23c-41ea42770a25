#ifndef __DB_OFFICE_COMPANY_DOOR_LIST_H__
#define __DB_OFFICE_COMPANY_DOOR_LIST_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

enum class OfficeCompanyDoorListInfoType
{
    PUBLIC = 0,
    PRIVATE = 1,
};

typedef struct OfficeCompanyDoorListInfo_T
{
    char uuid[36];
    char devices_uuid[36];
    char to_public_key[36];
    char devices_door_list_uuid[36];
    char office_company_uuid[36];
    OfficeCompanyDoorListInfoType type;
    OfficeCompanyDoorListInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} OfficeCompanyDoorListInfo;

using OfficeCompanyDoorListInfoList = std::vector<OfficeCompanyDoorListInfo>;

namespace dbinterface
{

class OfficeCompanyDoorList
{
public:

private:
    OfficeCompanyDoorList() = delete;
    ~OfficeCompanyDoorList() = delete;
    static void GetOfficeCompanyDoorListFromSql(OfficeCompanyDoorListInfo& office_company_door_list_info, CRldbQuery& query);

public:
    static int GetPubDoorUUIDsByProjectUUID(const std::string& project_uuid, std::set<std::string>& door_uuid_set);
    static int GePrivateDoorCompanyByDevicesUUID(const std::string& devices_uuid, std::set<std::string>& company_set);
    static int GetDeviceOfficeCompanyDoorList(const std::string& devices_uuid, OfficeCompanyDoorListInfoList& office_company_door_list_list);
    static int GetOfficeCompanyDoorListByDevicesUUID(const std::string& devices_uuid, OfficeCompanyDoorListInfoList& office_company_door_list_list);
};

}
#endif
