#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "OfficeDelivery.h"

namespace dbinterface {

static const std::string office_delivery_info_sec = " UUID,AccountUUID,OfficeCompanyUUID,Name,ID,UserVersion ";

void OfficeDelivery::GetOfficeDeliveryFromSql(OfficeDeliveryInfo& office_delivery_info, CRldbQuery& query)
{
    Snprintf(office_delivery_info.uuid, sizeof(office_delivery_info.uuid), query.GetRowData(0));
    Snprintf(office_delivery_info.project_uuid, sizeof(office_delivery_info.project_uuid), query.GetRowData(1));
    Snprintf(office_delivery_info.office_company_uuid, sizeof(office_delivery_info.office_company_uuid), query.GetRowData(2));
    Snprintf(office_delivery_info.name, sizeof(office_delivery_info.name), query.GetRowData(3));
    office_delivery_info.id = ATOI(query.GetRowData(4));
    office_delivery_info.version = ATOI(query.GetRowData(5));
    return;
}

int OfficeDelivery::GetOfficeDeliveryByID(int id, OfficeDeliveryInfo& delivery_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_delivery_info_sec << " from OfficeDelivery where ID = " << id;

    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetOfficeDeliveryFromSql(delivery_info, query);
    }
    else
    {
        AK_LOG_WARN << "get OfficePersonnelGroupInfo by ID failed, id = " << id;
        return -1;
    }
    return 0;
}

int OfficeDelivery::GetOfficeDeliveryByUUID(const std::string &uuid, OfficeDeliveryInfo& delivery_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_delivery_info_sec << " from OfficeDelivery where UUID = '" << uuid << "'";

    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetOfficeDeliveryFromSql(delivery_info, query);
    }
    else
    {
        AK_LOG_WARN << "get OfficePersonnelGroupInfo by ID failed, uuid = " << uuid;
        return -1;
    }
    return 0;
}


int OfficeDelivery::GetOfficeDeliveryByProjectUUID(const std::string& uuid, OfficeDeliveryMap& delivery_map, OfficeDeliveryUserMateMap &delivery_mate_map)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_delivery_info_sec << " from OfficeDelivery where AccountUUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeDeliveryInfo info;
        GetOfficeDeliveryFromSql(info, query);
        delivery_map.insert(std::make_pair(info.uuid, info)); 
        
        char uuid[64] = "";
        snprintf(uuid, sizeof(uuid), "D%09d", info.id);
        delivery_mate_map.insert(std::make_pair(uuid, info));    
    }
    return 0;
}


int OfficeDelivery::UpdateDeliveryVersion(const std::string &uuid)
{
    GET_DB_CONN_ERR_RETURN(conn, -1);

    std::stringstream stream_sql;
    stream_sql << "update OfficeDelivery set UserVersion=UNIX_TIMESTAMP() where UUID = '" << uuid << "'";    

    return conn.get()->Execute(stream_sql.str()) >= 0 ? 0 : -1;
}

int OfficeDelivery::UpdateVersionByAgGroupUUID(const AkcsStringSet &ag_list)
{
    GET_DB_CONN_ERR_RETURN(conn, -1);

    if (ag_list.size() == 0)
    {
        return 0;;
    }
    std::string ag_list_str = ListToSeparatedFormatString(ag_list);
    if (ag_list_str.size() == 0)
    {
        return 0;;
    }
    
    std::stringstream streamsql;
    streamsql << "UPDATE OfficeDelivery P JOIN OfficeDeliveryAccessGroup G ON P.UUID = G.OfficeDeliveryUUID SET P.UserVersion = UNIX_TIMESTAMP()  WHERE G.OfficeAccessGroupUUID in (" 
              << ag_list_str
              << ")";    

    int ret = conn.get()->Execute(streamsql.str()) >= 0 ? 0 : -1;
    if (-1 == ret)
    {
        AK_LOG_WARN << "UpdateVersionByGroupUUID failed, ag_list = " << ag_list_str;
        return ret;
    }

    return 0;
}

int OfficeDelivery::UpdateVersionByProjectUUID(const std::string& project_uuid)
{
    GET_DB_CONN_ERR_RETURN(conn, -1);

    if (project_uuid.size() == 0)
    {
        return 0;
    }
    
    std::stringstream streamsql;
    streamsql << "UPDATE OfficeDelivery SET UserVersion = UNIX_TIMESTAMP()  WHERE AccountUUID = '" 
              << project_uuid
              << "'";    

    int ret = conn.get()->Execute(streamsql.str()) >= 0 ? 0 : -1;
    if (-1 == ret)
    {
        AK_LOG_WARN << "UpdateVersionByProjectUUID failed, project uuid = " << project_uuid;
        return ret;
    }

    return 0;
}

}
