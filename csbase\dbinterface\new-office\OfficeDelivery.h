#ifndef __DB_OFFICE_DELIVERY_H__
#define __DB_OFFICE_DELIVERY_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct OfficeDeliveryInfo_T
{
    uint32_t id;
    char uuid[36];
    char project_uuid[36];
    char office_company_uuid[36];
    char name[64];
    uint32_t version;
    OfficeDeliveryInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} OfficeDeliveryInfo;

using OfficeDeliveryMap = std::map<std::string/*uuid*/, OfficeDeliveryInfo>;
//TODO:弄成智能指针 这样就可以少一份内存
using OfficeDeliveryUserMateMap = std::map<std::string/*userMateID*/, OfficeDeliveryInfo>;

namespace dbinterface {

class OfficeDelivery
{
public:
    static int GetOfficeDeliveryByID(int id, OfficeDeliveryInfo& delivery_info);
    static int GetOfficeDeliveryByProjectUUID(const std::string& projectuuid, OfficeDeliveryMap& delivery_map, OfficeDeliveryUserMateMap &delivery_mate_map);
    static int UpdateDeliveryVersion(const std::string &uuid);
    static int UpdateVersionByAgGroupUUID(const AkcsStringSet &ag_list);
    static int UpdateVersionByProjectUUID(const std::string& project_uuid);
    static int GetOfficeDeliveryByUUID(const std::string &uuid, OfficeDeliveryInfo& delivery_info);
private:
    OfficeDelivery() = delete;
    ~OfficeDelivery() = delete;
    static void GetOfficeDeliveryFromSql(OfficeDeliveryInfo& office_delivery_info, CRldbQuery& query);
};

}
#endif
