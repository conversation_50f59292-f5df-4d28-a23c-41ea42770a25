#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "OfficeDeliveryAccessFloor.h"

namespace dbinterface {

static const std::string office_delivery_access_floor_info_sec = " F.UUID,F.OfficeDeliveryUUID,F.CommunityUnitUUID,F.Floors ";

void OfficeDeliveryAccessFloor::GetOfficeDeliveryAccessFloorFromSql(OfficeDeliveryAccessFloorInfo& office_delivery_access_floor_info, CRldbQuery& query)
{
    Snprintf(office_delivery_access_floor_info.uuid, sizeof(office_delivery_access_floor_info.uuid), query.GetRowData(0));
    Snprintf(office_delivery_access_floor_info.office_delivery_uuid, sizeof(office_delivery_access_floor_info.office_delivery_uuid), query.GetRowData(1));
    Snprintf(office_delivery_access_floor_info.community_unit_uuid, sizeof(office_delivery_access_floor_info.community_unit_uuid), query.GetRowData(2));
    Snprintf(office_delivery_access_floor_info.floors, sizeof(office_delivery_access_floor_info.floors), query.GetRowData(3));
    return;
}

int OfficeDeliveryAccessFloor::GetOfficeDeliveryAccessFloorByUUID(const std::string& uuid, OfficeDeliveryAccessFloorInfo& office_delivery_access_floor_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_delivery_access_floor_info_sec << " from OfficeDeliveryAccessFloor F where UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetOfficeDeliveryAccessFloorFromSql(office_delivery_access_floor_info, query);
    }
    else
    {
        AK_LOG_WARN << "get OfficeDeliveryAccessFloorInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}

int OfficeDeliveryAccessFloor::GetOfficeDeliveryAccessFloorByOfficeDeliveryUUID(const std::string& office_delivery_uuid, OfficeDeliveryAccessFloorInfo& office_delivery_access_floor_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_delivery_access_floor_info_sec << " from OfficeDeliveryAccessFloor F where OfficeDeliveryUUID = '" << office_delivery_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetOfficeDeliveryAccessFloorFromSql(office_delivery_access_floor_info, query);
    }
    else
    {
        AK_LOG_WARN << "get OfficeDeliveryAccessFloorInfo by OfficeDeliveryUUID failed, OfficeDeliveryUUID = " << office_delivery_uuid;
        return -1;
    }
    return 0;
}

int OfficeDeliveryAccessFloor::GetOfficeDeliveryAccessFloorByProjectUUID(const std::string& project_uuid, OfficeDeliveryAccessFloorMap& office_delivery_access_floor_map)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_delivery_access_floor_info_sec << " from OfficeDeliveryAccessFloor F left join OfficeDelivery D on D.UUID=F.OfficeDeliveryUUID where D.AccountUUID = '" << project_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeDeliveryAccessFloorInfo info;
        GetOfficeDeliveryAccessFloorFromSql(info, query);
        office_delivery_access_floor_map.insert(std::make_pair(info.office_delivery_uuid, info));
    }
    return 0;
}

}