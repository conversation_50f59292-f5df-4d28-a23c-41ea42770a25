#ifndef __DB_OFFICE_DELIVERY_ACCESS_FLOOR_H__
#define __DB_OFFICE_DELIVERY_ACCESS_FLOOR_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct OfficeDeliveryAccessFloorInfo_T
{
    char uuid[36];
    char office_delivery_uuid[36];
    char community_unit_uuid[36];
    char floors[64];
    OfficeDeliveryAccessFloorInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} OfficeDeliveryAccessFloorInfo;

using OfficeDeliveryAccessFloorMap = std::multimap<std::string/*office_delivery_uuid*/, OfficeDeliveryAccessFloorInfo>;

namespace dbinterface {

class OfficeDeliveryAccessFloor
{
public:
    static int GetOfficeDeliveryAccessFloorByUUID(const std::string& uuid, OfficeDeliveryAccessFloorInfo& office_delivery_access_floor_info);
    static int GetOfficeDeliveryAccessFloorByOfficeDeliveryUUID(const std::string& office_delivery_uuid, OfficeDeliveryAccessFloorInfo& office_delivery_access_floor_info);

    static int GetOfficeDeliveryAccessFloorByProjectUUID(const std::string& project_uuid, OfficeDeliveryAccessFloorMap& office_delivery_access_floor_map);

private:
    OfficeDeliveryAccessFloor() = delete;
    ~OfficeDeliveryAccessFloor() = delete;
    static void GetOfficeDeliveryAccessFloorFromSql(OfficeDeliveryAccessFloorInfo& office_delivery_access_floor_info, CRldbQuery& query);
};

}
#endif