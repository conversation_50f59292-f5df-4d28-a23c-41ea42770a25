#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "OfficeDeliveryAccessGroup.h"

namespace dbinterface {

static const std::string office_delivery_access_group_info_sec = " G.UUI<PERSON>,G.OfficeDeliveryUUID,G.OfficeAccessGroupUUID ";

void OfficeDeliveryAccessGroup::GetOfficeDeliveryAccessGroupFromSql(OfficeDeliveryAccessGroupInfo& office_delivery_access_group_info, CRldbQuery& query)
{
    Snprintf(office_delivery_access_group_info.uuid, sizeof(office_delivery_access_group_info.uuid), query.GetRowData(0));
    Snprintf(office_delivery_access_group_info.office_delivery_uuid, sizeof(office_delivery_access_group_info.office_delivery_uuid), query.GetRowData(1));
    Snprintf(office_delivery_access_group_info.office_access_group_uuid, sizeof(office_delivery_access_group_info.office_access_group_uuid), query.GetRowData(2));
    return;
}

int OfficeDeliveryAccessGroup::GetOfficeDeliveryAccessGroupByProjectUUID(const std::string& project_uuid, DeliveryOfAgUUIDMap& delivery_ag_uuid_map, DeliveryOfAgAgMap& delivery_ag_ag_map )
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_delivery_access_group_info_sec << " from OfficeDeliveryAccessGroup G left join OfficeDelivery D on G.OfficeDeliveryUUID=D.UUID where D.AccountUUID = '" << project_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeDeliveryAccessGroupInfo info;
        GetOfficeDeliveryAccessGroupFromSql(info, query);
        delivery_ag_uuid_map.insert(std::make_pair(info.office_delivery_uuid, info.office_access_group_uuid));
        delivery_ag_ag_map.insert(std::make_pair(info.office_access_group_uuid, info.office_delivery_uuid));
    }    
    
    return 0;
}

}