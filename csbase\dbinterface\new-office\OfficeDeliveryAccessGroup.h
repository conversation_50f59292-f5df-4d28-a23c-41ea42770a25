#ifndef __DB_OFFICE_DELIVERY_ACCESS_GROUP_H__
#define __DB_OFFICE_DELIVERY_ACCESS_GROUP_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct OfficeDeliveryAccessGroupInfo_T
{
    char uuid[64];
    char office_delivery_uuid[36];
    char office_access_group_uuid[36];
    OfficeDeliveryAccessGroupInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} OfficeDeliveryAccessGroupInfo;


using DeliveryOfAgUUIDMap = std::multimap<std::string/*delivery_uuid*/, std::string/*access_group_uuid*/>;
using DeliveryOfAgAgMap = std::multimap<std::string/*access_group_uuid*/, std::string/*delivery_uuid*/>;

namespace dbinterface {

class OfficeDeliveryAccessGroup
{
public:
    //static int GetOfficeDeliveryAccessGroupByUUID(const std::string& uuid, OfficeDeliveryAccessGroupInfo& office_delivery_access_group_info);
    //static int GetOfficeDeliveryAccessGroupByOfficeDeliveryUUID(const std::string& office_delivery_uuid, OfficeDeliveryAccessGroupInfo& office_delivery_access_group_info);
    //static int GetOfficeDeliveryAccessGroupByOfficeAccessGroupUUID(const std::string& office_access_group_uuid, OfficeDeliveryAccessGroupInfo& office_delivery_access_group_info);

    static int GetOfficeDeliveryAccessGroupByProjectUUID(const std::string& project_uuid, DeliveryOfAgUUIDMap& delivery_ag_uuid_map, DeliveryOfAgAgMap& delivery_ag_ag_map );
private:
    OfficeDeliveryAccessGroup() = delete;
    ~OfficeDeliveryAccessGroup() = delete;
    static void GetOfficeDeliveryAccessGroupFromSql(OfficeDeliveryAccessGroupInfo& office_delivery_access_group_info, CRldbQuery& query);
};

}
#endif