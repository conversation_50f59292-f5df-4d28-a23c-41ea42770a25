#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "OfficeDeviceAssign.h"

namespace dbinterface {

static const std::string office_device_assign_info_sec = " UUID,PersonalAccountUUID,OfficeGroupUUID,OfficeCompanyUUID,DevicesUUID,Type ";

void OfficeDeviceAssign::GetOfficeDeviceAssignFromSql(OfficeDeviceAssignInfo& office_device_assign_info, CRldbQuery& query)
{
    Snprintf(office_device_assign_info.uuid, sizeof(office_device_assign_info.uuid), query.GetRowData(0));
    Snprintf(office_device_assign_info.personal_account_uuid, sizeof(office_device_assign_info.personal_account_uuid), query.GetRowData(1));
    Snprintf(office_device_assign_info.office_group_uuid, sizeof(office_device_assign_info.office_group_uuid), query.GetRowData(2));
    Snprintf(office_device_assign_info.office_company_uuid, sizeof(office_device_assign_info.office_company_uuid), query.GetRowData(3));
    Snprintf(office_device_assign_info.devices_uuid, sizeof(office_device_assign_info.devices_uuid), query.GetRowData(4));
    office_device_assign_info.type = (DeviceAssignType)ATOI(query.GetRowData(5));
    
    return;
}

/*

int OfficeDeviceAssign::GetOfficeDeviceAssignByUUID(const std::string& uuid, OfficeDeviceAssignInfo& office_device_assign_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_device_assign_info_sec << " from OfficeDeviceAssign where UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetOfficeDeviceAssignFromSql(office_device_assign_info, query);
    }
    else
    {
        AK_LOG_WARN << "get OfficeDeviceAssignInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}

int OfficeDeviceAssign::GetOfficeDeviceAssignByPersonalAccountUUID(const std::string& personal_account_uuid, OfficeDeviceAssignInfo& office_device_assign_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_device_assign_info_sec << " from OfficeDeviceAssign where PersonalAccountUUID = '" << personal_account_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetOfficeDeviceAssignFromSql(office_device_assign_info, query);
    }
    else
    {
        AK_LOG_WARN << "get OfficeDeviceAssignInfo by PersonalAccountUUID failed, PersonalAccountUUID = " << personal_account_uuid;
        return -1;
    }
    return 0;
}

int OfficeDeviceAssign::GetOfficeDeviceAssignByOfficeGroupUUID(const std::string& office_group_uuid, OfficeDeviceAssignInfo& office_device_assign_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_device_assign_info_sec << " from OfficeDeviceAssign where OfficeGroupUUID = '" << office_group_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetOfficeDeviceAssignFromSql(office_device_assign_info, query);
    }
    else
    {
        AK_LOG_WARN << "get OfficeDeviceAssignInfo by OfficeGroupUUID failed, OfficeGroupUUID = " << office_group_uuid;
        return -1;
    }
    return 0;
}

*/

int OfficeDeviceAssign::GetOfficeDeviceAssignByDevicesUUID(const std::string& devices_uuid, OfficeDeviceAssignInfo& office_device_assign_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_device_assign_info_sec << " from OfficeDeviceAssign where DevicesUUID = '" << devices_uuid << "'";
    
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetOfficeDeviceAssignFromSql(office_device_assign_info, query);
    }
    else
    {
        AK_LOG_INFO << "get OfficeDeviceAssignInfo by DevicesUUID failed, DevicesUUID = " << devices_uuid;
        return -1;
    }
    return 0;
}

int OfficeDeviceAssign::GetOfficeDeviceAssignByProjectUUID(const std::string& project_uuid, OfficeDeviceAssignDevMap &dev_map, 
  OfficeDeviceAssignPerMap &per_map, OfficeDeviceAssignGroupMap &group_map, OfficeDeviceAssignCompanyMap &company_map)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_device_assign_info_sec << " from OfficeDeviceAssign where AccountUUID = '" << project_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeDeviceAssignInfo info;
        GetOfficeDeviceAssignFromSql(info, query);
        dev_map.insert(std::make_pair(info.devices_uuid, info));
        company_map.insert(std::make_pair(info.office_company_uuid, info));   
        if (info.type == DeviceAssignType::DeviceAssignTypPer)
        {
            per_map.insert(std::make_pair(info.personal_account_uuid, info)); 
        }
        else if (info.type == DeviceAssignType::DeviceAssignTypGroup)
        {
            group_map.insert(std::make_pair(info.office_group_uuid, info)); 

        }  
    }  

    return 0;
}


int OfficeDeviceAssign::GetOfficeDeviceAssignByPerUUID(const std::string& per_uuid, OfficeDeviceAssignDevList& devlist)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_device_assign_info_sec << " from OfficeDeviceAssign where PersonalAccountUUID = '" << per_uuid << "'";
    
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeDeviceAssignInfo info;
        GetOfficeDeviceAssignFromSql(info, query);
        devlist.push_back(info);
    }
    return 0; 
}


}
