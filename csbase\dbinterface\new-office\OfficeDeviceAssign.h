#ifndef __DB_OFFICE_DEVICE_ASSIGN_H__
#define __DB_OFFICE_DEVICE_ASSIGN_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/office/OfficePersonalAccount.h"

enum DeviceAssignType
{
    DeviceAssignTypPer = 1,
    DeviceAssignTypGroup = 2,
    DeviceAssignTypCompany = 3,
};


typedef struct OfficeDeviceAssignInfo_T
{
    char uuid[36];
    char personal_account_uuid[36];
    char office_group_uuid[36];
    char devices_uuid[36];
    char office_company_uuid[36];
    DeviceAssignType type;
    OfficeDeviceAssignInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} OfficeDeviceAssignInfo;

using OfficeDeviceAssignDevMap = std::multimap<std::string/*dev_uuid*/, OfficeDeviceAssignInfo>;
using OfficeDeviceAssignPerMap = std::multimap<std::string/*peruuid*/, OfficeDeviceAssignInfo>;
using OfficeDeviceAssignGroupMap = std::multimap<std::string/*gruopuuid*/, OfficeDeviceAssignInfo>;
using OfficeDeviceAssignCompanyMap = std::multimap<std::string/*company*/, OfficeDeviceAssignInfo>;
using OfficeDeviceAssignDevList = std::list<OfficeDeviceAssignInfo>;

namespace dbinterface {

class OfficeDeviceAssign
{
public:
    /*
    static int GetOfficeDeviceAssignByUUID(const std::string& uuid, OfficeDeviceAssignInfo& office_device_assign_info);
    static int GetOfficeDeviceAssignByPersonalAccountUUID(const std::string& personal_account_uuid, OfficeDeviceAssignInfo& office_device_assign_info);
    static int GetOfficeDeviceAssignByOfficeGroupUUID(const std::string& office_group_uuid, OfficeDeviceAssignInfo& office_device_assign_info);
    */
    
    static int GetOfficeDeviceAssignByDevicesUUID(const std::string& devices_uuid, OfficeDeviceAssignInfo& office_device_assign_info);
    static int GetOfficeDeviceAssignByProjectUUID(const std::string& project_uuid, OfficeDeviceAssignDevMap &dev_map, 
        OfficeDeviceAssignPerMap &per_map, OfficeDeviceAssignGroupMap &group_map, OfficeDeviceAssignCompanyMap &company_map);
    static int GetOfficeDeviceAssignByPerUUID(const std::string& per_uuid, OfficeDeviceAssignDevList& list);
private:
    OfficeDeviceAssign() = delete;
    ~OfficeDeviceAssign() = delete;
    static void GetOfficeDeviceAssignFromSql(OfficeDeviceAssignInfo& office_device_assign_info, CRldbQuery& query);
};

}
#endif
