#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "OfficeEmergencyDoor.h"

namespace dbinterface {

static const std::string office_emergency_door_info_sec = " UUID,AccountUUID,<PERSON><PERSON>UUI<PERSON>,DevicesDoorList ";

void OfficeEmergencyDoor::GetOfficeEmergencyDoorFromSql(OfficeEmergencyDoorInfo& office_emergency_door_info, CRldbQuery& query)
{
    Snprintf(office_emergency_door_info.uuid, sizeof(office_emergency_door_info.uuid), query.GetRowData(0));
    Snprintf(office_emergency_door_info.account_uuid, sizeof(office_emergency_door_info.account_uuid), query.GetRowData(1));
    Snprintf(office_emergency_door_info.devices_uuid, sizeof(office_emergency_door_info.devices_uuid), query.GetRowData(2));
    Snprintf(office_emergency_door_info.devices_door_list_uuid, sizeof(office_emergency_door_info.devices_door_list_uuid), query.GetRowData(3));
    return;
}

int OfficeEmergencyDoor::GetOfficeEmergencyDoorByUUID(const std::string& uuid, OfficeEmergencyDoorInfo& office_emergency_door_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_emergency_door_info_sec << " from OfficeEmergencyDoor where UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetOfficeEmergencyDoorFromSql(office_emergency_door_info, query);
    }
    else
    {
        AK_LOG_WARN << "get OfficeEmergencyDoorInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}

void OfficeEmergencyDoor::GetOfficeEmergencyDoorListByAccountUUID(const std::string& account_uuid, OfficeEmergencyDoorInfoList& office_emergency_door_info_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_emergency_door_info_sec << " from OfficeEmergencyDoor where AccountUUID = '" << account_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn,);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeEmergencyDoorInfo office_emergency_door_info;
        GetOfficeEmergencyDoorFromSql(office_emergency_door_info, query);
        office_emergency_door_info_list.push_back(office_emergency_door_info);
    }
}


}