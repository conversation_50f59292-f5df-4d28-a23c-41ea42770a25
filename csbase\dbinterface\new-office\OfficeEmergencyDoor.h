#ifndef __DB_OFFICE_EMERGENCY_DOOR_H__
#define __DB_OFFICE_EMERGENCY_DOOR_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct OfficeEmergencyDoorInfo_T
{
    char uuid[36];
    char account_uuid[36];
    char devices_uuid[36];
    char devices_door_list_uuid[36];
    OfficeEmergencyDoorInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} OfficeEmergencyDoorInfo;

using OfficeEmergencyDoorInfoList = std::vector<OfficeEmergencyDoorInfo>;

namespace dbinterface {

class OfficeEmergencyDoor
{
public:
    static int GetOfficeEmergencyDoorByUUID(const std::string& uuid, OfficeEmergencyDoorInfo& office_emergency_door_info);
    static void GetOfficeEmergencyDoorListByAccountUUID(const std::string& account_uuid, OfficeEmergencyDoorInfoList& office_emergency_door_info_list);

private:
    OfficeEmergencyDoor() = delete;
    ~OfficeEmergencyDoor() = delete;
    static void GetOfficeEmergencyDoorFromSql(OfficeEmergencyDoorInfo& office_emergency_door_info, CRldbQuery& query);
};

}
#endif