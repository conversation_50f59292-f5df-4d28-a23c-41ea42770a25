#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "OfficeFace.h"

namespace dbinterface {

static const std::string face_info_sec = " UUID,AccountUUID,PersonalAccountUUID,FaceMD5,FaceUrl,CreatorType ";

void UserFace::GetFaceFromSql(FaceInfo& face_info, CRldbQuery& query)
{
    Snprintf(face_info.uuid, sizeof(face_info.uuid), query.GetRowData(0));
    Snprintf(face_info.account_uuid, sizeof(face_info.account_uuid), query.GetRowData(1));
    Snprintf(face_info.personal_account_uuid, sizeof(face_info.personal_account_uuid), query.GetRowData(2));
    Snprintf(face_info.face_md5, sizeof(face_info.face_md5), query.GetRowData(3));
    Snprintf(face_info.face_url, sizeof(face_info.face_url), query.GetRowData(4));
    face_info.creator_type = (AccessCreatorType)ATOI(query.GetRowData(5));
    return;
}

int UserFace::GetFaceByUUID(const std::string& uuid, FaceInfo& face_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << face_info_sec << " from Face where UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetFaceFromSql(face_info, query);
    }
    else
    {
        AK_LOG_WARN << "get FaceInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}

int UserFace::GetFaceByAccountUUID(const std::string& account_uuid, FaceInfo& face_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << face_info_sec << " from Face where AccountUUID = '" << account_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetFaceFromSql(face_info, query);
    }
    else
    {
        AK_LOG_WARN << "get FaceInfo by AccountUUID failed, AccountUUID = " << account_uuid;
        return -1;
    }
    return 0;
}

int UserFace::GetFaceByPersonalAccountUUID(const std::string& personal_account_uuid, FaceInfo& face_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << face_info_sec << " from Face where PersonalAccountUUID = '" << personal_account_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetFaceFromSql(face_info, query);
    }
    else
    {
        AK_LOG_WARN << "get FaceInfo by PersonalAccountUUID failed, PersonalAccountUUID = " << personal_account_uuid;
        return -1;
    }
    return 0;
}

int UserFace::GetFaceByProjectUUID(const std::string& project_uuid, UserFaceMap& face_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << face_info_sec << " from Face where AccountUUID = '" << project_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        FaceInfo info;
        GetFaceFromSql(info, query);
        face_info.insert(std::make_pair(info.personal_account_uuid, info));
    } 
    return 0;   
}

}
