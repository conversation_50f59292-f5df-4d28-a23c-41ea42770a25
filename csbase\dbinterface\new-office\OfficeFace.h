#ifndef __DB_FACE_H__
#define __DB_FACE_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"


enum class AccessCreatorType
{
    END_USER = 1,
    PM = 2,
    ADMIN = 3,
    INS = 4,
};
    
typedef struct FaceInfo_T
{
    char uuid[36];
    char account_uuid[36];
    char personal_account_uuid[36];
    char face_md5[32];
    char face_url[64];
    AccessCreatorType creator_type;
    char creator_uuid[36];
    FaceInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} FaceInfo;

using UserFaceMap = std::map<std::string/*user uuid*/, FaceInfo>;

namespace dbinterface {

class UserFace
{
public:
    static int GetFaceByUUID(const std::string& uuid, FaceInfo& face_info);
    static int GetFaceByAccountUUID(const std::string& account_uuid, FaceInfo& face_info);
    static int GetFaceByPersonalAccountUUID(const std::string& personal_account_uuid, FaceInfo& face_info);

    static int GetFaceByProjectUUID(const std::string& project, UserFaceMap& face_info);
private:
    UserFace() = delete;
    ~UserFace() = delete;
    static void GetFaceFromSql(FaceInfo& face_info, CRldbQuery& query);
};

}
#endif