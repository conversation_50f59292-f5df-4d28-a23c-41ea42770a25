#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "OfficeGroup.h"

namespace dbinterface {

static const std::string office_group_info_sec = " UUID,OfficeCompanyUUID,AccountUUID,Name,DisplayType,IsImmuneAntipassback ";

void OfficeGroup::GetOfficeGroupFromSql(OfficeGroupInfo& office_group_info, CRldbQuery& query)
{
    Snprintf(office_group_info.uuid, sizeof(office_group_info.uuid), query.GetRowData(0));
    Snprintf(office_group_info.office_company_uuid, sizeof(office_group_info.office_company_uuid), query.GetRowData(1));
    Snprintf(office_group_info.project_uuid, sizeof(office_group_info.project_uuid), query.GetRowData(2));
    Snprintf(office_group_info.name, sizeof(office_group_info.name), query.GetRowData(3));
    office_group_info.display_type = (OfficeGroupDisplayType)ATOI(query.GetRowData(4));
    office_group_info.is_immune_antipassback = ATOI(query.GetRowData(5));
    return;
}

int OfficeGroup::GetOfficeGroupByUUID(const std::string& uuid, OfficeGroupInfo& office_group_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_group_info_sec << " from OfficeGroup where UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetOfficeGroupFromSql(office_group_info, query);
    }
    else
    {
        AK_LOG_WARN << "get OfficeGroupInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}

int OfficeGroup::GetOfficeGroupByProjectUUID(const std::string& project_uuid, GroupOfCompanyGroupMap& group_map, 
  GroupOfCompanyUUIDMap &group_company_uuid_map, GroupOfCompanyCompanyMap& group_company_company_map )
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_group_info_sec << " from OfficeGroup where AccountUUID = '" << project_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeGroupInfo info;
        GetOfficeGroupFromSql(info, query);
        group_map.insert(std::make_pair(info.uuid, info));
        group_company_uuid_map.insert(std::make_pair(info.uuid, info.office_company_uuid));
        group_company_company_map.insert(std::make_pair(info.office_company_uuid, info));
    }

    return 0;
}


int OfficeGroup::GetUuidsByCompanyUUID(const std::string& company_uuid, AkcsStringSet& group_uuid_set)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_group_info_sec << " from OfficeGroup where OfficeCompanyUUID= '" << company_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeGroupInfo info;
        GetOfficeGroupFromSql(info, query);
        group_uuid_set.insert(info.uuid);
    }
    return 0;
}


int OfficeGroup::GetOfficeGroupOfCompanyByProjectUUID(const std::string& project_uuid, GroupOfCompanyUUIDMap& group_company_uuid_map)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_group_info_sec << " from OfficeGroup where AccountUUID = '" << project_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeGroupInfo info;
        GetOfficeGroupFromSql(info, query);
        group_company_uuid_map.insert(std::make_pair(info.uuid, info.office_company_uuid));
    }

    return 0;
}

}