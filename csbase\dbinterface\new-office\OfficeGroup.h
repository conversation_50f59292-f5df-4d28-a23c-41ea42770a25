#ifndef __DB_OFFICE_GROUP_H__
#define __DB_OFFICE_GROUP_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/new-office/OfficePersonnelGroup.h"

enum OfficeGroupDisplayType{
   NOT_DISPLAY = 1,
   SHOW_GROUP = 2,
   SHOW_PERSONNEL =3,
   NO_NEED, // 非门口机无需考虑GroupDisplayType
};

typedef struct OfficeGroupInfo_T
{
    char uuid[64];
    char office_company_uuid[64];
    char project_uuid[64];
    char name[64];
    OfficeGroupDisplayType display_type;
    int is_immune_antipassback;
    
    OfficeGroupInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} OfficeGroupInfo;

using OfficeGroupInfoList = std::vector<OfficeGroupInfo>;

//人员->group->commpany
using GroupOfCompanyGroupMap = std::map<std::string/*GroupUUID*/, OfficeGroupInfo>;
using GroupOfCompanyUUIDMap = std::map<std::string/*GroupUUID*/, std::string/*company uuid*/>;
using GroupOfCompanyCompanyMap = std::map<std::string/*company uuid*/, OfficeGroupInfo>;
using OfficeAccountCompanyUUIDMap = std::map<std::string/*per uuid*/, std::string/*company uuid*/>;

namespace dbinterface {

class OfficeGroup
{
public:
    static int GetOfficeGroupByUUID(const std::string& uuid, OfficeGroupInfo& office_group_info);
    static int GetOfficePersonnelGroupListByOfficePersonnelUUID(const std::string& office_personnel_uuid, OfficePersonnelGroupInfoList& office_personnel_group_info);
    static int GetOfficeGroupByProjectUUID(const std::string& project_uuid, GroupOfCompanyGroupMap& group_map, GroupOfCompanyUUIDMap &group_company_uuid_map, GroupOfCompanyCompanyMap& group_company_company_map);
    static int GetUuidsByCompanyUUID(const std::string& company_uuid, AkcsStringSet& group_uuid_set);
    static int GetOfficeGroupOfCompanyByProjectUUID(const std::string& project_uuid, GroupOfCompanyUUIDMap& group_company_uuid_map);
private:
    OfficeGroup() = delete;
    ~OfficeGroup() = delete;
    static void GetOfficeGroupFromSql(OfficeGroupInfo& office_group_info, CRldbQuery& query);

};

}
#endif
