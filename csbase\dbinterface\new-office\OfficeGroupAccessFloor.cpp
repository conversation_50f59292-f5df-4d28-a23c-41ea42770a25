#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "dbinterface/new-office/OfficePersonnelGroup.h"
#include "dbinterface/new-office/OfficeGroupAccessGroup.h"
#include "dbinterface/new-office/OfficeGroupAccessFloor.h"
#include "dbinterface/new-office/OfficeAdminGroup.h"
#include "dbinterface/new-office/OfficeGroup.h"
#include "dbinterface/new-office/OfficeCompanyAccessFloor.h"

namespace dbinterface {

static const std::string office_group_access_floor_info_sec = " F.UUID,F.OfficeGroupUUID,F.CommunityUnitUUID,F.Floors ";

void OfficeGroupAccessFloor::GetOfficeGroupAccessFloorFromSql(OfficeGroupAccessFloorInfo& office_group_access_floor_info, CRldbQuery& query)
{
    Snprintf(office_group_access_floor_info.uuid, sizeof(office_group_access_floor_info.uuid), query.GetRowData(0));
    Snprintf(office_group_access_floor_info.office_group_uuid, sizeof(office_group_access_floor_info.office_group_uuid), query.GetRowData(1));
    Snprintf(office_group_access_floor_info.community_unit_uuid, sizeof(office_group_access_floor_info.community_unit_uuid), query.GetRowData(2));
    Snprintf(office_group_access_floor_info.floors, sizeof(office_group_access_floor_info.floors), query.GetRowData(3));
    return;
}

int OfficeGroupAccessFloor::GetOfficeGroupAccessFloorByOfficeGroupUUID(const std::string& office_group_uuid, OfficeGroupAccessFloorList& office_group_access_floor_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_group_access_floor_info_sec << " from OfficeGroupAccessFloor F where OfficeGroupUUID = '" << office_group_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeGroupAccessFloorInfo office_group_access_floor_info;
        GetOfficeGroupAccessFloorFromSql(office_group_access_floor_info, query);
        office_group_access_floor_list.push_back(office_group_access_floor_info);
    }
    
    return 0;
}

int OfficeGroupAccessFloor::GetOfficeGroupAccessFloorByProjectUUID(const std::string& project_uuid, OfficeGroupAccessFloorMap& office_group_access_floor_map)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_group_access_floor_info_sec << " from OfficeGroupAccessFloor F left join OfficeGroup G on G.UUID=F.OfficeGroupUUID where G.AccountUUID = '" << project_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeGroupAccessFloorInfo info;
        GetOfficeGroupAccessFloorFromSql(info, query);
        office_group_access_floor_map.insert(std::make_pair(info.office_group_uuid, info));
    } 
    return 0;
}



std::string OfficeGroupAccessFloor::GetAccessFloorListByPersonalUnitDevice(
    const std::string& personal_account_uuid, const std::string& unit_uuid, const std::string& device_uuid, int role)
{
    /**
     * 场景说明：
     *    - 用户    属于 办公组1、办公组2
     *    - 办公组1 拥有 权限组1，权限组1 对设备有权限，  办公组1 对楼栋A的楼层1;2;3;有权限
     *    - 办公组3 拥有 权限组3，权限组3 对设备有权限，  办公组3 对楼栋A的楼层1;8;9;有权限
     *    - 办公组2 拥有 权限组2，权限组2 对设备没有权限，办公组2 对楼栋B的楼层5;6;7;有权限
     * 则：
     *      用户 能看到设备                   (权限链路: 用户->>办公组1->>权限组1->>设备）
     *      用户 对楼栋A的楼层1;2;3;8;9有权限  (权限链路: 用户->>办公组1/3->>楼栋A->>楼层1;2;3;8;9）
     *      用户 对楼栋B的楼层5;6;7没有权限    (权限链路: 用户->>办公组2--[X]-->>设备）
     */
     
    /**  注意：
      group如果没有设置可通行楼层，楼层权限默认跟随Company
      */
    // 1.获取personal_account_uuid所在的 办公组（有多个）
    std::vector<std::string> office_group_uuid_list;
    GetOfficeGroupUUIDListByPersonalAccountUUID(personal_account_uuid, role, office_group_uuid_list);

    // 2.获取设备所在的办公组列表（有多个）
    AkcsStringSet device_office_group_set;
    dbinterface::OfficeGroupAccessGroup::GetOfficeGroupListByDeviceUUID(device_uuid, device_office_group_set);
    // 3.合并楼层列表
    std::string access_floors;
    for (const auto& group_uuid : office_group_uuid_list)
    {
        // a.设备不属于该办公组
        if(device_office_group_set.find(group_uuid) == device_office_group_set.end()){
            continue;
        }

        // b.获取办公组对每个楼栋的楼层权限
        OfficeGroupAccessFloorList access_floor_list;
        dbinterface::OfficeGroupAccessFloor::GetOfficeGroupAccessFloorByOfficeGroupUUID(
            group_uuid, access_floor_list
        );

        // c.判断办公组是否设置了楼栋的楼层权限,如果 access_floor_list 为空，表示没有设置楼层权限,默认跟随company的楼层权限
        
        if (access_floor_list.empty()) 
        {
            AK_LOG_INFO << "group not band access floor, it will default to the company's accessible floor settings. group uuid: " << group_uuid;
            // 获取company对每个楼栋的楼层权限
            OfficeCompanyAccessFloorList access_company_floor_list;
            OfficeGroupInfo office_group_info;
            dbinterface::OfficeGroup::GetOfficeGroupByUUID(group_uuid, office_group_info);
            dbinterface::OfficeCompanyAccessFloor::GetOfficeCompanyAccessFloorByOfficeCompanyUUID(office_group_info.office_company_uuid, access_company_floor_list);
            for (auto item : access_company_floor_list)
            {
                if(unit_uuid == item.community_unit_uuid){
                    access_floors = GetAccessibleFloor(access_floors, item.access_floors);
                    break;
                }
            }
        }
        
        // d.办公组设置了楼层权限，合并对unit_uuid楼栋的楼层列表
        for (auto item : access_floor_list)
        {
            if(unit_uuid == item.community_unit_uuid){
                access_floors = GetAccessibleFloor(access_floors, item.floors);
                break;
            }
        }
    }

    return access_floors;
}

void OfficeGroupAccessFloor::GetOfficeGroupUUIDListByPersonalAccountUUID(const std::string& per_uuid, int role, std::vector<std::string>& office_group_uuid_list)
{
    if (role == ACCOUNT_ROLE_OFFICE_NEW_PER)
    {
        OfficePersonnelGroupInfoList personal_group_list;
        dbinterface::OfficePersonnelGroup::GetOfficePersonnelGroupListByPersonalAccountUUID(
            per_uuid, personal_group_list
        );

        for (const auto& group_info : personal_group_list)
        {
            office_group_uuid_list.push_back(group_info.office_group_uuid);
        }

        return;
    }
    else if (role == ACCOUNT_ROLE_OFFICE_NEW_ADMIN)
    {
        OfficeAdminGroupInfoList admin_group_list;
        dbinterface::OfficeAdminGroup::GetOfficeAdminGroupListByPersonalAccountUUID(
            per_uuid, admin_group_list
        );

        for (const auto& group_info : admin_group_list)
        {
            office_group_uuid_list.push_back(group_info.office_group_uuid);
        }

        return;
    }

    return;
}

}
