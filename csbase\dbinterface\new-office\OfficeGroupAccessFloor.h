#ifndef __DB_OFFICE_GROUP_ACCESS_FLOOR_H__
#define __DB_OFFICE_GROUP_ACCESS_FLOOR_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct OfficeGroupAccessFloorInfo_T
{
    char uuid[64];
    char office_group_uuid[64];
    char community_unit_uuid[36];
    char floors[512];
    OfficeGroupAccessFloorInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} OfficeGroupAccessFloorInfo;

using OfficeGroupAccessFloorList = std::vector<OfficeGroupAccessFloorInfo>;
using OfficeGroupAccessFloorMap = std::multimap<std::string/*office_group_uuid*/, OfficeGroupAccessFloorInfo>;

namespace dbinterface {

class OfficeGroupAccessFloor
{
public:
    static int GetOfficeGroupAccessFloorByOfficeGroupUUID(const std::string& office_group_uuid, OfficeGroupAccessFloorList& office_group_access_floor_list);

    static int GetOfficeGroupAccessFloorByProjectUUID(const std::string& project_uuid, OfficeGroupAccessFloorMap& office_group_access_floor_map);

        /**
     * @brief  获取某用户在某楼层，使用某指定设备的可达楼层
     * 
     * @param personal_account_uuid 用户UUID
     * @param unit_uuid 楼栋UUID
     * @param device_uuid 设备UUID
     * @return 
     */
    static std::string GetAccessFloorListByPersonalUnitDevice(
        const std::string& personal_account_uuid, const std::string& unit_uuid, const std::string& device_uuid, int role
    );

private:
    OfficeGroupAccessFloor() = delete;
    ~OfficeGroupAccessFloor() = delete;
    static void GetOfficeGroupAccessFloorFromSql(OfficeGroupAccessFloorInfo& office_group_access_floor_info, CRldbQuery& query);
    static void GetOfficeGroupUUIDListByPersonalAccountUUID(const std::string& per_uuid, int role, std::vector<std::string>& office_group_uuid_list);
};

}
#endif
