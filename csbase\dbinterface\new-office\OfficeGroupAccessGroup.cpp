#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "dbinterface/new-office/OfficePersonnelGroup.h"
#include "dbinterface/new-office/OfficeGroupAccessFloor.h"
#include "dbinterface/new-office/OfficeGroupAccessGroup.h"

namespace dbinterface {

static const std::string office_group_access_group_info_sec = " A.UUID,A.OfficeGroupUUID,A.OfficeAccessGroupUUID ";

void OfficeGroupAccessGroup::GetOfficeGroupAccessGroupFromSql(OfficeGroupAccessGroupInfo& office_group_access_group_info, CRldbQuery& query)
{
    Snprintf(office_group_access_group_info.uuid, sizeof(office_group_access_group_info.uuid), query.GetRowData(0));
    Snprintf(office_group_access_group_info.office_group_uuid, sizeof(office_group_access_group_info.office_group_uuid), query.GetRowData(1));
    Snprintf(office_group_access_group_info.office_access_group_uuid, sizeof(office_group_access_group_info.office_access_group_uuid), query.GetRowData(2));
    return;
}

int OfficeGroupAccessGroup::GetOfficeGroupAccessGroupByOfficeGroupUUID(const std::string& office_group_uuid, OfficeGroupAccessGroupInfo& office_group_access_group_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_group_access_group_info_sec << " from OfficeGroupAccessGroup A where OfficeGroupUUID = '" << office_group_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetOfficeGroupAccessGroupFromSql(office_group_access_group_info, query);
    }
    else
    {
        AK_LOG_WARN << "get OfficeGroupAccessGroupInfo by OfficeGroupUUID failed, OfficeGroupUUID = " << office_group_uuid;
        return -1;
    }
    return 0;
}

int OfficeGroupAccessGroup::GetOfficeGroupAccessGroupByOfficeAccessGroupUUID(const std::string& office_access_group_uuid, OfficeGroupAccessGroupInfo& office_group_access_group_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_group_access_group_info_sec << " from OfficeGroupAccessGroup A where OfficeAccessGroupUUID = '" << office_access_group_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetOfficeGroupAccessGroupFromSql(office_group_access_group_info, query);
    }
    else
    {
        AK_LOG_WARN << "get OfficeGroupAccessGroupInfo by OfficeAccessGroupUUID failed, OfficeAccessGroupUUID = " << office_access_group_uuid;
        return -1;
    }
    return 0;
}

int OfficeGroupAccessGroup::GetOfficeGroupAGByProjectUUID(const std::string& project_uuid, GroupOfAgAgMap& ag_map, GroupOfAgUUIDMap& group_map)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_group_access_group_info_sec << " from OfficeGroupAccessGroup A left join OfficeGroup O on O.UUID=A.OfficeGroupUUID where O.AccountUUID = '" << project_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeGroupAccessGroupInfo info;
        GetOfficeGroupAccessGroupFromSql(info, query);
        ag_map.insert(std::make_pair(info.office_access_group_uuid, info.office_group_uuid));
        group_map.insert(std::make_pair(info.office_group_uuid, info.office_access_group_uuid));
    }    

    return 0;
}

int OfficeGroupAccessGroup::GetGroupUuidsByAgUUID(const std::string& ag_uuid, AkcsStringSet &group_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_group_access_group_info_sec << " from OfficeGroupAccessGroup  A where A.OfficeAccessGroupUUID = '" << ag_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeGroupAccessGroupInfo info;
        GetOfficeGroupAccessGroupFromSql(info, query);
        group_list.insert(info.office_group_uuid);
    }    

    return 0;
}

int OfficeGroupAccessGroup::GetOfficeGroupListByDeviceUUID(const std::string& device_uuid, AkcsStringSet& group_list)
{
    std::stringstream stream_sql;
    stream_sql << "SELECT " << office_group_access_group_info_sec << "FROM OfficeGroupAccessGroup AS A "
               << "LEFT JOIN OfficeAccessGroup AS OAG ON A.OfficeAccessGroupUUID = OAG.UUID "
               << "LEFT JOIN OfficeAccessGroupDevice AS D ON OAG.UUID = D.OfficeAccessGroupUUID "
               << "WHERE D.DevicesUUID = '" << device_uuid << "'AND D.UUID IS NOT NULL";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeGroupAccessGroupInfo info;
        GetOfficeGroupAccessGroupFromSql(info, query);
        group_list.insert(info.office_group_uuid);
    }    

    return 0;
}


}
