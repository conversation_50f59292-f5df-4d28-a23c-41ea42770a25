#ifndef __DB_OFFICE_GROUP_ACCESS_GROUP_H__
#define __DB_OFFICE_GROUP_ACCESS_GROUP_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct OfficeGroupAccessGroupInfo_T
{
    char uuid[64];
    char office_group_uuid[64];
    char office_access_group_uuid[64];
    OfficeGroupAccessGroupInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} OfficeGroupAccessGroupInfo;

using GroupOfAgUUIDMap = std::multimap<std::string/*GroupUUID*/, std::string/*access_group_uuid*/>;
using GroupOfAgAgMap = std::multimap<std::string/*access_group_uuid*/, std::string/*GroupUUID*/>;
using OfficeGroupAccessGroupInfoList = std::vector<OfficeGroupAccessGroupInfo>;

namespace dbinterface {

class OfficeGroupAccessGroup
{
public:
    static int GetOfficeGroupAccessGroupByOfficeGroupUUID(const std::string& office_group_uuid, OfficeGroupAccessGroupInfo& office_group_access_group_info);
    static int GetOfficeGroupAccessGroupByOfficeAccessGroupUUID(const std::string& office_access_group_uuid, OfficeGroupAccessGroupInfo& office_group_access_group_info);

    static int GetOfficeGroupAGByProjectUUID(const std::string& project_uuid, GroupOfAgAgMap& ag_map, GroupOfAgUUIDMap&group_ag_map);
    static int GetGroupUuidsByAgUUID(const std::string& ag_uuid, AkcsStringSet &group_list);
    // 获取 指定设备 所在的办公组列表(一个设备可能在多个办公组)
    static int GetOfficeGroupListByDeviceUUID(const std::string& device_uuid, AkcsStringSet& ag_dev_map);

private:
    OfficeGroupAccessGroup() = delete;
    ~OfficeGroupAccessGroup() = delete;
    static void GetOfficeGroupAccessGroupFromSql(OfficeGroupAccessGroupInfo& office_group_access_group_info, CRldbQuery& query);
};

}
#endif
