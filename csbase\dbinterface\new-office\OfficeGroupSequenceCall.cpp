#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "OfficeGroupSequenceCall.h"

namespace dbinterface {

static const std::string office_group_sequence_call_info_sec = " UUI<PERSON>,OfficeGroupUUID,PersonalAccountUUID,CallOrder,CallType,DevicesUUID ";


void OfficeGroupSequenceCall::GetOfficeGroupSequenceCallFromSql(OfficeGroupSequenceCallInfo& office_group_sequence_call_info, CRldbQuery& query)
{
    Snprintf(office_group_sequence_call_info.uuid, sizeof(office_group_sequence_call_info.uuid), query.GetRowData(0));
    Snprintf(office_group_sequence_call_info.office_group_uuid, sizeof(office_group_sequence_call_info.office_group_uuid), query.GetRowData(1));
    Snprintf(office_group_sequence_call_info.personal_account_uuid, sizeof(office_group_sequence_call_info.personal_account_uuid), query.GetRowData(2));
    office_group_sequence_call_info.call_order = ATOI(query.GetRowData(3));
    office_group_sequence_call_info.call_type = (CallSeqType)ATOI(query.GetRowData(4));

    Snprintf(office_group_sequence_call_info.device_uuid, sizeof(office_group_sequence_call_info.device_uuid), query.GetRowData(5));
    return;
}

int OfficeGroupSequenceCall::GetOfficeGroupSequenceCallByCallUUID(const std::string& call_uuid, OfficeGroupSequenceCallInfo& office_group_sequence_call_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_group_sequence_call_info_sec << " from OfficeGroupSequenceCall where CallUUID = '" << call_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetOfficeGroupSequenceCallFromSql(office_group_sequence_call_info, query);
    }
    else
    {
        AK_LOG_WARN << "get OfficeGroupSequenceCallInfo by CallUUID failed, CallUUID = " << call_uuid;
        return -1;
    }
    return 0;
}

int OfficeGroupSequenceCall::GetOfficeGroupSequenceCallByOfficeGroupUUID(const std::string& office_group_uuid, OfficeGroupSeqCallMap& call_map)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_group_sequence_call_info_sec << " from OfficeGroupSequenceCall where OfficeGroupUUID = '" << office_group_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeGroupSequenceCallInfo info;
        GetOfficeGroupSequenceCallFromSql(info, query);
        call_map.insert(std::make_pair(info.office_group_uuid, info));
    }
    return 0;
}


}