#ifndef __DB_OFFICE_GROUP_SEQUENCE_CALL_H__
#define __DB_OFFICE_GROUP_SEQUENCE_CALL_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"


typedef struct OfficeGroupSequenceCallInfo_T
{
    char uuid[64];
    char office_group_uuid[64];
    char personal_account_uuid[64];
    char device_uuid[64];
    int call_order;//顺序 1 2 3
    CallSeqType call_type;

    OfficeGroupSequenceCallInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} OfficeGroupSequenceCallInfo;


  
using OfficeGroupSeqCallMap = std::multimap<std::string/*group uuid*/, OfficeGroupSequenceCallInfo>;


namespace dbinterface {

class OfficeGroupSequenceCall
{
public:
    static int GetOfficeGroupSequenceCallByCallUUID(const std::string& call_uuid, OfficeGroupSequenceCallInfo& office_group_sequence_call_info);
    static int GetOfficeGroupSequenceCallByOfficeGroupUUID(const std::string& office_group_uuid, OfficeGroupSeqCallMap& call_map);

private:
    OfficeGroupSequenceCall() = delete;
    ~OfficeGroupSequenceCall() = delete;
    static void GetOfficeGroupSequenceCallFromSql(OfficeGroupSequenceCallInfo& office_group_sequence_call_info, CRldbQuery& query);
};

}
#endif