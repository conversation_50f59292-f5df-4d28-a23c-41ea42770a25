#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "OfficeHoliday.h"

namespace dbinterface {

static const std::string office_holiday_info_sec = " <PERSON><PERSON>,H.AccountUUID,OfficeCompanyUUID,H.<PERSON>,<PERSON>.<PERSON>,H.StartTime,H.StopTime,H.<PERSON>,H.<PERSON>,H.Dates,H.IsAllCompany ";

void OfficeHoliday::GetOfficeHolidayFromSql(OfficeHolidayInfo& office_holiday_info, CRldbQuery& query)
{
    Snprintf(office_holiday_info.uuid, sizeof(office_holiday_info.uuid), query.GetRowData(0));
    Snprintf(office_holiday_info.project_uuid, sizeof(office_holiday_info.project_uuid), query.GetRowData(1));
    Snprintf(office_holiday_info.company_uuid, sizeof(office_holiday_info.company_uuid), query.GetRowData(2));
    Snprintf(office_holiday_info.name, sizeof(office_holiday_info.name), query.GetRowData(3));
    office_holiday_info.is_working_hours = ATOI(query.GetRowData(4));
    Snprintf(office_holiday_info.start_time, sizeof(office_holiday_info.start_time), query.GetRowData(5));
    Snprintf(office_holiday_info.stop_time, sizeof(office_holiday_info.stop_time), query.GetRowData(6));
    office_holiday_info.is_year_repeat = ATOI(query.GetRowData(7));
    Snprintf(office_holiday_info.year, sizeof(office_holiday_info.year), query.GetRowData(8));
    Snprintf(office_holiday_info.dates, sizeof(office_holiday_info.dates), query.GetRowData(9));
    office_holiday_info.is_all_company = ATOI(query.GetRowData(10));
    return;
}
/*
int OfficeHoliday::GetOfficeHolidayByUUID(const std::string& uuid, OfficeHolidayInfo& office_holiday_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_holiday_info_sec << " from OfficeHoliday where UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetOfficeHolidayFromSql(office_holiday_info, query);
    }
    else
    {
        AK_LOG_WARN << "get OfficeHolidayInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}

int OfficeHoliday::GetOfficeHolidayByAccountUUID(const std::string& project_uuid, OfficeHolidayInfo& office_holiday_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_holiday_info_sec << " from OfficeHoliday where AccountUUID = '" << project_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetOfficeHolidayFromSql(office_holiday_info, query);
    }
    else
    {
        AK_LOG_WARN << "get OfficeHolidayInfo by AccountUUID failed, AccountUUID = " << project_uuid;
        return -1;
    }
    return 0;
}

int OfficeHoliday::GetOfficeHolidayByAdminUUID(const std::string& admin_uuid, OfficeHolidayInfo& office_holiday_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_holiday_info_sec << " from OfficeHoliday where AdminUUID = '" << admin_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetOfficeHolidayFromSql(office_holiday_info, query);
    }
    else
    {
        AK_LOG_WARN << "get OfficeHolidayInfo by AdminUUID failed, AdminUUID = " << admin_uuid;
        return -1;
    }
    return 0;
}
*/

//公司的和项目的分开
int OfficeHoliday::GetOfficeHolidayByProjectUUID(const std::string& project_uuid, ProjectHolidayMap& project_holiday_map, CompanyHolidayMap& company_holiday_map)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_holiday_info_sec << " from OfficeHoliday H left join OfficeHolidayCompany O on H.UUID=O.OfficeHolidayUUID where H.AccountUUID = '" << project_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeHolidayInfo info;
        GetOfficeHolidayFromSql(info, query);
        if (info.is_all_company)
        {
            project_holiday_map.insert(std::make_pair(info.project_uuid, info)); 
        }
        else
        {
            company_holiday_map.insert(std::make_pair(info.company_uuid, info)); 
        }
    }
    
    return 0;
}

}