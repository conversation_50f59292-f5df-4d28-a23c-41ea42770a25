#ifndef __DB_OFFICE_HOLIDAY_H__
#define __DB_OFFICE_HOLIDAY_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct OfficeHolidayInfo_T
{
    char uuid[36];
    char project_uuid[36];
    char company_uuid[36];
    char name[64];
    int is_working_hours;
    char start_time[32];
    char stop_time[32];
    int is_year_repeat;
    int is_all_company;
    char year[8];
    char dates[4000];
    OfficeHolidayInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} OfficeHolidayInfo;

typedef struct OfficeHolidayCompanyInfo_T
{
    char uuid[64];
    char office_holiday_uuid[64];
    char office_company_uuid[64];
    OfficeHolidayCompanyInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} OfficeHolidayCompanyInfo;


using CompanyHolidayMap = std::multimap<std::string/*company uuid*/, OfficeHolidayInfo>;
using ProjectHolidayMap = std::multimap<std::string/*project uuid*/, OfficeHolidayInfo>;

namespace dbinterface {

class OfficeHoliday
{
public:
    /*
    static int GetOfficeHolidayByUUID(const std::string& uuid, OfficeHolidayInfo& office_holiday_info);
    static int GetOfficeHolidayByAccountUUID(const std::string& project_uuid, OfficeHolidayInfo& office_holiday_info);
    static int GetOfficeHolidayByAdminUUID(const std::string& admin_uuid, OfficeHolidayInfo& office_holiday_info);
    */
    static int GetOfficeHolidayByProjectUUID(const std::string& project_uuid, ProjectHolidayMap& project_holiday_map, CompanyHolidayMap& company_holiday_map);
private:
    OfficeHoliday() = delete;
    ~OfficeHoliday() = delete;
    static void GetOfficeHolidayFromSql(OfficeHolidayInfo& office_holiday_info, CRldbQuery& query);
};

}
#endif