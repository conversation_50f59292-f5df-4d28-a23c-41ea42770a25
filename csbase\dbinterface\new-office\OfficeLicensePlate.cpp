#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "OfficeLicensePlate.h"

namespace dbinterface {

static const std::string license_plate_info_sec = " UUID,ManageUUID,PersonalAccountUUID,Plate,UHF,TimeControl,BeginTime,EndTime ";

void UserLicensePlate::GetUserLicensePlateFromSql(LicensePlateInfo& license_plate_info, CRldbQuery& query)
{
    Snprintf(license_plate_info.uuid, sizeof(license_plate_info.uuid), query.GetRowData(0));
    Snprintf(license_plate_info.project_uuid, sizeof(license_plate_info.project_uuid), query.GetRowData(1));
    Snprintf(license_plate_info.personal_account_uuid, sizeof(license_plate_info.personal_account_uuid), query.GetRowData(2));
    Snprintf(license_plate_info.plate, sizeof(license_plate_info.plate), query.GetRowData(3));
    Snprintf(license_plate_info.ufh, sizeof(license_plate_info.ufh), query.GetRowData(4));
    license_plate_info.time_control = (TimeControl)ATOI(query.GetRowData(5));
    Snprintf(license_plate_info.begin_time, sizeof(license_plate_info.begin_time), query.GetRowData(6));
    Snprintf(license_plate_info.end_time, sizeof(license_plate_info.end_time), query.GetRowData(7));
    return;
}

int UserLicensePlate::GetUserLicensePlateByUUID(const std::string& uuid, LicensePlateInfo& license_plate_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << license_plate_info_sec << " from LicensePlate where UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetUserLicensePlateFromSql(license_plate_info, query);
    }
    else
    {
        AK_LOG_WARN << "get UserLicensePlateInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}

int UserLicensePlate::GetUserLicensePlateByPersonalAccountUUID(const std::string& personal_account_uuid, LicensePlateInfo& license_plate_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << license_plate_info_sec << " from LicensePlate where PersonalAccountUUID = '" << personal_account_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetUserLicensePlateFromSql(license_plate_info, query);
    }
    else
    {
        AK_LOG_WARN << "get UserLicensePlateInfo by PersonalAccountUUID failed, PersonalAccountUUID = " << personal_account_uuid;
        return -1;
    }
    return 0;
}

int UserLicensePlate::GetUserLicensePlateByProjectUUID(const std::string& project_uuid, UserLicensePlateMap& account_license_plate_map)
{
    std::stringstream stream_sql;
    stream_sql << "select " << license_plate_info_sec << " from LicensePlate where ManageUUID = '" << project_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        LicensePlateInfo info;
        GetUserLicensePlateFromSql(info, query);
        account_license_plate_map.insert(std::make_pair(info.personal_account_uuid, info));
    }    
    return 0;
}

}