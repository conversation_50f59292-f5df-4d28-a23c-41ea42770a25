#ifndef __DB_USER_LICENSE_PLATE_H__
#define __DB_USER_LICENSE_PLATE_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

enum class TimeControl{
    Always = 0,
    Part   = 1,
};

typedef struct LicensePlateInfo_T
{
    char uuid[36];
    char project_uuid[36];
    char personal_account_uuid[36];
    char plate[64];
    char ufh[32];
    TimeControl time_control;
    char begin_time[32];
    char end_time[32];
    LicensePlateInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} LicensePlateInfo;

using UserLicensePlateMap = std::multimap<std::string/*user uuid*/, LicensePlateInfo>;

namespace dbinterface {

class UserLicensePlate
{
public:
    static int GetUserLicensePlateByUUID(const std::string& uuid, LicensePlateInfo& license_plate_info);
    static int GetUserLicensePlateByPersonalAccountUUID(const std::string& personal_account_uuid, LicensePlateInfo& license_plate_info);

    static int GetUserLicensePlateByProjectUUID(const std::string& project_uuid, UserLicensePlateMap& account_license_plate_map);
private:
    UserLicensePlate() = delete;
    ~UserLicensePlate() = delete;
    static void GetUserLicensePlateFromSql(LicensePlateInfo& license_plate_info, CRldbQuery& query);
};

}
#endif
