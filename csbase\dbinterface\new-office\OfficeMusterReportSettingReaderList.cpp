#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "OfficeMusterReportSettingReaderList.h"

namespace dbinterface {

static const std::string office_muster_report_setting_reader_list_info_sec = " U<PERSON><PERSON>,OfficeMusterReportSettingUUID,DevicesUUID,ReaderList ";

void OfficeMusterReportSettingReaderList::GetOfficeMusterReportSettingReaderListFromSql(OfficeMusterReportSettingReaderListInfo& office_muster_report_setting_reader_list_info, CRldbQuery& query)
{
    Snprintf(office_muster_report_setting_reader_list_info.uuid, sizeof(office_muster_report_setting_reader_list_info.uuid), query.GetRowData(0));
    Snprintf(office_muster_report_setting_reader_list_info.office_muster_report_setting_uuid, sizeof(office_muster_report_setting_reader_list_info.office_muster_report_setting_uuid), query.GetRowData(1));
    Snprintf(office_muster_report_setting_reader_list_info.devices_uuid, sizeof(office_muster_report_setting_reader_list_info.devices_uuid), query.GetRowData(2));
    Snprintf(office_muster_report_setting_reader_list_info.reader_list, sizeof(office_muster_report_setting_reader_list_info.reader_list), query.GetRowData(3));
    return;
}

int OfficeMusterReportSettingReaderList::GetOfficeMusterReportSettingReaderInfoByDeviceUUID(const std::string& dev_uuid, OfficeMusterReportSettingReaderListInfo& office_muster_report_setting_reader_list_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_muster_report_setting_reader_list_info_sec << " from OfficeMusterReportSettingReaderList where DevicesUUID = '" << dev_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetOfficeMusterReportSettingReaderListFromSql(office_muster_report_setting_reader_list_info, query);
    }
    else
    {
        return -1;
    }
    
    return 0;
}

bool OfficeMusterReportSettingReaderList::IsMusterReportDevice(const std::string& dev_uuid)
{
    std::stringstream stream_sql;
    stream_sql << "select count(*) >= 1 as IsMusterReport from OfficeMusterReportSettingReaderList where DevicesUUID = '" << dev_uuid << "'";

    GET_DB_CONN_ERR_RETURN(db_conn, false)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        int count = ATOI(query.GetRowData(0));
        return count >= 1;
    }
    else
    {
        return false;
    }
}

}