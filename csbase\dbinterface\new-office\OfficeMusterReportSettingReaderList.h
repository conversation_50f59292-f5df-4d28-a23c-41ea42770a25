#ifndef __DB_OFFICE_MUSTER_REPORT_SETTING_READER_LIST_H__
#define __DB_OFFICE_MUSTER_REPORT_SETTING_READER_LIST_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct OfficeMusterReportSettingReaderListInfo_T
{
    char uuid[36];
    char office_muster_report_setting_uuid[36];
    char devices_uuid[36];
    char reader_list[20];
    OfficeMusterReportSettingReaderListInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} OfficeMusterReportSettingReaderListInfo;

namespace dbinterface {

class OfficeMusterReportSettingReaderList
{
public:
    static int GetOfficeMusterReportSettingReaderInfoByDeviceUUID(const std::string& dev_uuid, OfficeMusterReportSettingReaderListInfo& office_muster_report_setting_reader_list_info);
    static bool IsMusterReportDevice(const std::string& dev_uuid);

private:
    OfficeMusterReportSettingReaderList() = delete;
    ~OfficeMusterReportSettingReaderList() = delete;
    static void GetOfficeMusterReportSettingReaderListFromSql(OfficeMusterReportSettingReaderListInfo& office_muster_report_setting_reader_list_info, CRldbQuery& query);
};

}
#endif