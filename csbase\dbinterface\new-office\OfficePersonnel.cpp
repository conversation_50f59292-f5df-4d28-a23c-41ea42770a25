#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "OfficePersonnel.h"
#include "dbinterface/new-office/OfficePersonnelGroup.h"
#include "dbinterface/new-office/OfficeGroupAccessFloor.h"

namespace dbinterface {

static const std::string office_personnel_info_sec = " UUID,OfficeCompanyUUID,PersonalAccountUUID,IdNo,IsDisplayInDirectory,IsSmartPlusIntercom,CallType,IsSetValidTime,ValidStartTime,ValidEndTime,IsFreeAppIntercome,AppIntercomeActive,AppIntercomeExpireTime < now() as AppIntercomeExpire,AccountUUID ";

void OfficePersonnel::GetOfficePersonnelFromSql(OfficePersonnelInfo& office_personnel_info, CRldbQuery& query)
{
    Snprintf(office_personnel_info.uuid, sizeof(office_personnel_info.uuid), query.GetRowData(0));
    Snprintf(office_personnel_info.office_company_uuid, sizeof(office_personnel_info.office_company_uuid), query.GetRowData(1));
    Snprintf(office_personnel_info.personal_account_uuid, sizeof(office_personnel_info.personal_account_uuid), query.GetRowData(2));
    Snprintf(office_personnel_info.id_no, sizeof(office_personnel_info.id_no), query.GetRowData(3));
    office_personnel_info.is_display_in_directory = ATOI(query.GetRowData(4));
    office_personnel_info.is_smart_plus_intercom = ATOI(query.GetRowData(5));
    office_personnel_info.call_type = ATOI(query.GetRowData(6));
    office_personnel_info.is_set_valid_time = ATOI(query.GetRowData(7));
    Snprintf(office_personnel_info.valid_start_time, sizeof(office_personnel_info.valid_start_time), query.GetRowData(8));
    Snprintf(office_personnel_info.valid_end_time, sizeof(office_personnel_info.valid_end_time), query.GetRowData(9));
    office_personnel_info.is_free_app_intercome = ATOI(query.GetRowData(10));
    office_personnel_info.app_intercome_active = ATOI(query.GetRowData(11));
    office_personnel_info.app_intercome_expire = ATOI(query.GetRowData(12));
    Snprintf(office_personnel_info.project_uuid, sizeof(office_personnel_info.project_uuid), query.GetRowData(13));
    return;
}

int OfficePersonnel::GetOfficePersonnelByUUID(const std::string& uuid, OfficePersonnelInfo& office_personnel_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_personnel_info_sec << " from OfficePersonnel where UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetOfficePersonnelFromSql(office_personnel_info, query);
    }
    else
    {
        AK_LOG_WARN << "get OfficePersonnelInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}

int OfficePersonnel::GetOfficePersonnelByPersonalAccountUUID(const std::string& personal_account_uuid, OfficePersonnelInfo& office_personnel_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_personnel_info_sec << " from OfficePersonnel where PersonalAccountUUID = '" << personal_account_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetOfficePersonnelFromSql(office_personnel_info, query);
    }
    else
    {
        AK_LOG_WARN << "get OfficePersonnelInfo by PersonalAccountUUID failed, PersonalAccountUUID = " << personal_account_uuid;
        return -1;
    }
    return 0;
}

int OfficePersonnel::GetOfficePersonnelByProjectUUID(const std::string& project_uuid, OfficePersonnelMap& per_map)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_personnel_info_sec << " from OfficePersonnel where AccountUUID = '" << project_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());

    while (query.MoveToNextRow())
    {
        OfficePersonnelInfo info;
        GetOfficePersonnelFromSql(info, query);
        per_map.insert(std::make_pair(info.personal_account_uuid, info));    
    }
    return 0;    
}

}
