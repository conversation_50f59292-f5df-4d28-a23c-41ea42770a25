#ifndef __DB_OFFICE_PERSONNEL_H__
#define __DB_OFFICE_PERSONNEL_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct OfficePersonnelInfo_T
{
    char uuid[64];
    char office_company_uuid[64];
    char personal_account_uuid[64];
    char id_no[256];
    int is_display_in_directory;
    int is_smart_plus_intercom;
    int call_type;
    int is_set_valid_time;
    char valid_start_time[32];
    char valid_end_time[32];
    int is_free_app_intercome;
    int app_intercome_active;
    int app_intercome_expire;
    char project_uuid[64];
    
    OfficePersonnelInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} OfficePersonnelInfo;

using OfficePersonnelMap = std::map<std::string/*per uuid*/, OfficePersonnelInfo>;

namespace dbinterface {

class OfficePersonnel
{
public:
    static int GetOfficePersonnelByUUID(const std::string& uuid, OfficePersonnelInfo& office_personnel_info);
    static int GetOfficePersonnelByPersonalAccountUUID(const std::string& personal_account_uuid, OfficePersonnelInfo& office_personnel_info);
    static int GetOfficePersonnelByProjectUUID(const std::string& project_uuid, OfficePersonnelMap& per_map);

private:
    OfficePersonnel() = delete;
    ~OfficePersonnel() = delete;
    static void GetOfficePersonnelFromSql(OfficePersonnelInfo& office_personnel_info, CRldbQuery& query);
};

}

#endif
