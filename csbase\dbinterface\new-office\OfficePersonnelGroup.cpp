#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "OfficePersonnelGroup.h"

namespace dbinterface {

static const std::string office_personnel_group_info_sec = " O.ID,O.U<PERSON>,O.PersonalAccountUUID,O.OfficeGroupUUID ";

void OfficePersonnelGroup::GetOfficePersonnelGroupFromSql(OfficePersonnelGroupInfo& office_personnel_group_info, CRldbQuery& query)
{
    office_personnel_group_info.id = ATOI(query.GetRowData(0));
    Snprintf(office_personnel_group_info.uuid, sizeof(office_personnel_group_info.uuid), query.GetRowData(1));
    Snprintf(office_personnel_group_info.personal_account_uuid, sizeof(office_personnel_group_info.personal_account_uuid), query.GetRowData(2));
    Snprintf(office_personnel_group_info.office_group_uuid, sizeof(office_personnel_group_info.office_group_uuid), query.GetRowData(3));
    return;
}

int OfficePersonnelGroup::GetOfficePersonnelGroupByUUID(const std::string& uuid, OfficePersonnelGroupInfo& office_personnel_group_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_personnel_group_info_sec << " from OfficePersonnelGroup O where UUID = '" << uuid << "'";
    
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetOfficePersonnelGroupFromSql(office_personnel_group_info, query);
    }
    else
    {
        AK_LOG_WARN << "get OfficePersonnelGroupInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}

// 一个OfficePersonnel可以属于多个Group
int OfficePersonnelGroup::GetOfficePersonnelGroupListByPersonalAccountUUID(const std::string& personal_account_uuid, OfficePersonnelGroupInfoList& office_personnel_group_info_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_personnel_group_info_sec << " from OfficePersonnelGroup O where PersonalAccountUUID = '" << personal_account_uuid << "'";

    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficePersonnelGroupInfo office_personnel_group_info;
        GetOfficePersonnelGroupFromSql(office_personnel_group_info, query);
        office_personnel_group_info_list.push_back(office_personnel_group_info);
    }

    return 0;
}

int OfficePersonnelGroup::GetOfficePersonnelGroupByOfficeGroupUUID(const std::string& office_group_uuid, OfficePersonnelGroupInfo& office_personnel_group_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_personnel_group_info_sec << " from OfficePersonnelGroup O where OfficeGroupUUID = '" << office_group_uuid << "'";

    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetOfficePersonnelGroupFromSql(office_personnel_group_info, query);
    }
    else
    {
        AK_LOG_WARN << "get OfficePersonnelGroupInfo by OfficeGroupUUID failed, OfficeGroupUUID = " << office_group_uuid;
        return -1;
    }
    return 0;
}

int OfficePersonnelGroup::GetOfficePersonnelGroupByProjectUUID(const std::string& project_uuid, GroupOfPerGroupMap& group_of_per_group_map, GroupOfPerPerMap& per_map)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_personnel_group_info_sec << " from OfficePersonnelGroup O left join PersonalAccount P on P.UUID=O.PersonalAccountUUID where P.ParentUUID = '" << project_uuid << "'";

    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficePersonnelGroupInfo info;
        GetOfficePersonnelGroupFromSql(info, query);
        group_of_per_group_map.insert(std::make_pair(info.office_group_uuid, info));
        per_map.insert(std::make_pair(info.personal_account_uuid, info));    
    }

    return 0;    
}


}
