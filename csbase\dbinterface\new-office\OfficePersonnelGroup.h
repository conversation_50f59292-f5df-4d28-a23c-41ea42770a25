#ifndef __DB_OFFICE_PERSONNEL_GROUP_H__
#define __DB_OFFICE_PERSONNEL_GROUP_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct OfficePersonnelGroupInfo_T
{
    int id;
    char uuid[64];
    char personal_account_uuid[64];
    char office_group_uuid[64];
    
    OfficePersonnelGroupInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} OfficePersonnelGroupInfo;

typedef std::vector<OfficePersonnelGroupInfo> OfficePersonnelGroupInfoList;

/*group 作为key*/
using GroupOfPerGroupMap = std::multimap<std::string/*group uuid*/, OfficePersonnelGroupInfo>;
/*per 作为key*/
using GroupOfPerPerMap = std::multimap<std::string/*per uuid*/, OfficePersonnelGroupInfo>;


namespace dbinterface {

class OfficePersonnelGroup
{
public:
    static int GetOfficePersonnelGroupByUUID(const std::string& uuid, OfficePersonnelGroupInfo& office_personnel_group_info);
    static int GetOfficePersonnelGroupListByPersonalAccountUUID(const std::string& office_personnel_uuid, OfficePersonnelGroupInfoList& office_personnel_group_info);
    static int GetOfficePersonnelGroupByOfficeGroupUUID(const std::string& office_group_uuid, OfficePersonnelGroupInfo& office_personnel_group_info);
    static int GetOfficePersonnelGroupByGroupUUID(const std::string& group_uuid, GroupOfPerGroupMap& group_of_per_group_map, GroupOfPerPerMap& per_map);
    static int GetOfficePersonnelGroupByProjectUUID(const std::string& project_uuid, GroupOfPerGroupMap& group_of_per_group_map, GroupOfPerPerMap& per_map);
private:
    OfficePersonnelGroup() = delete;
    ~OfficePersonnelGroup() = delete;
    static void GetOfficePersonnelGroupFromSql(OfficePersonnelGroupInfo& office_personnel_group_info, CRldbQuery& query);
};

}

#endif
