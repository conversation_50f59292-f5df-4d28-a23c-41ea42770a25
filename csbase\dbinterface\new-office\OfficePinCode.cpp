#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "OfficePinCode.h"

namespace dbinterface {

static const std::string pin_code_info_sec = " UUID,AccountUUID,PersonalAccountUUID,OfficeDeliveryUUID,Type,Code,CreatorType ";

void UserPinCode::GetPinCodeFromSql(PinCodeInfo& pin_code_info, CRldbQuery& query)
{
    Snprintf(pin_code_info.uuid, sizeof(pin_code_info.uuid), query.GetRowData(0));
    Snprintf(pin_code_info.account_uuid, sizeof(pin_code_info.account_uuid), query.GetRowData(1));
    Snprintf(pin_code_info.personal_account_uuid, sizeof(pin_code_info.personal_account_uuid), query.GetRowData(2));
    Snprintf(pin_code_info.office_delivery_uuid, sizeof(pin_code_info.office_delivery_uuid), query.GetRowData(3));
    pin_code_info.type = (PinCodeType)ATOI(query.GetRowData(4));
    Snprintf(pin_code_info.code, sizeof(pin_code_info.code), query.GetRowData(5));
    pin_code_info.creator_type = (AccessCreatorType)ATOI(query.GetRowData(6));
    return;
}

int UserPinCode::GetPinCodeByUUID(const std::string& uuid, PinCodeInfo& pin_code_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << pin_code_info_sec << " from PinCode where UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetPinCodeFromSql(pin_code_info, query);
    }
    else
    {
        AK_LOG_WARN << "get PinCodeInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}

int UserPinCode::GetPinCodeByAccountUUID(const std::string& account_uuid, PinCodeInfo& pin_code_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << pin_code_info_sec << " from PinCode where AccountUUID = '" << account_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetPinCodeFromSql(pin_code_info, query);
    }
    else
    {
        AK_LOG_WARN << "get PinCodeInfo by AccountUUID failed, AccountUUID = " << account_uuid;
        return -1;
    }
    return 0;
}

int UserPinCode::GetPinCodeByPersonalAccountUUID(const std::string& personal_account_uuid, PinCodeInfo& pin_code_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << pin_code_info_sec << " from PinCode where PersonalAccountUUID = '" << personal_account_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetPinCodeFromSql(pin_code_info, query);
    }
    else
    {
        AK_LOG_WARN << "get PinCodeInfo by PersonalAccountUUID failed, PersonalAccountUUID = " << personal_account_uuid;
        return -1;
    }
    return 0;
}

int UserPinCode::GetPinCodeByOfficeDeliveryUUID(const std::string& office_delivery_uuid, PinCodeInfo& pin_code_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << pin_code_info_sec << " from PinCode where OfficeDeliveryUUID = '" << office_delivery_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetPinCodeFromSql(pin_code_info, query);
    }
    else
    {
        AK_LOG_WARN << "get PinCodeInfo by OfficeDeliveryUUID failed, OfficeDeliveryUUID = " << office_delivery_uuid;
        return -1;
    }
    return 0;
}

int UserPinCode::GetPinCodeByProjectUUID(const std::string& project_uuid, UserPinCodeMap& account_pin_map, UserPinCodeMap& delivery_pin_map)
{
    std::stringstream stream_sql;
    stream_sql << "select " << pin_code_info_sec << " from PinCode where AccountUUID = '" << project_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        PinCodeInfo info;
        GetPinCodeFromSql(info, query);
        if (info.type == PinCodeType::Account || info.type == PinCodeType::Admin)
        {
            account_pin_map.insert(std::make_pair(info.personal_account_uuid, info));
        }
        else if (info.type == PinCodeType::Delivery)
        {
            delivery_pin_map.insert(std::make_pair(info.office_delivery_uuid, info));
        }
    }  
    return 0;  
}

}