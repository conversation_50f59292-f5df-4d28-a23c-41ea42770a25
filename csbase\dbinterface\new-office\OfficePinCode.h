#ifndef __DB_PIN_CODE_H__
#define __DB_PIN_CODE_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"
#include "OfficeFace.h"

enum class PinCodeType{
    Account = 1,
    Delivery = 2,
    Admin = 3,
};

enum class PinCodeCreatorType{
    
};

typedef struct PinCodeInfo_T
{
    char uuid[36];
    char account_uuid[36];
    char personal_account_uuid[36];
    char office_delivery_uuid[36];
    PinCodeType type;
    AccessCreatorType creator_type;
    char code[20];
    PinCodeInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} PinCodeInfo;


using UserPinCodeMap = std::multimap<std::string/*user uuid*/, PinCodeInfo>;

namespace dbinterface {

class UserPinCode
{
public:
    static int GetPinCodeByUUID(const std::string& uuid, PinCodeInfo& pin_code_info);
    static int GetPinCodeByAccountUUID(const std::string& account_uuid, PinCodeInfo& pin_code_info);
    static int GetPinCodeByPersonalAccountUUID(const std::string& personal_account_uuid, PinCodeInfo& pin_code_info);
    static int GetPinCodeByOfficeDeliveryUUID(const std::string& office_delivery_uuid, PinCodeInfo& pin_code_info);

    static int GetPinCodeByProjectUUID(const std::string& project_uuid, UserPinCodeMap& account_pin_map, UserPinCodeMap& delivery_pin_map);

private:
    UserPinCode() = delete;
    ~UserPinCode() = delete;
    static void GetPinCodeFromSql(PinCodeInfo& pin_code_info, CRldbQuery& query);
};

}
#endif