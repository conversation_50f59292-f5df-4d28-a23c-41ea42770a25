#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "OfficeRfCard.h"

namespace dbinterface {

static const std::string rf_card_info_sec = " UUID,AccountUUID,PersonalAccountUUID,OfficeDeliveryUUID,Code,Type ";

void UserRfCard::GetUserRfCardFromSql(RfCardInfo& rf_card_info, CRldbQuery& query)
{
    Snprintf(rf_card_info.uuid, sizeof(rf_card_info.uuid), query.GetRowData(0));
    Snprintf(rf_card_info.project_uuid, sizeof(rf_card_info.project_uuid), query.GetRowData(1));
    Snprintf(rf_card_info.personal_account_uuid, sizeof(rf_card_info.personal_account_uuid), query.GetRowData(2));
    Snprintf(rf_card_info.office_delivery_uuid, sizeof(rf_card_info.office_delivery_uuid), query.GetRowData(3));
    Snprintf(rf_card_info.code, sizeof(rf_card_info.code), query.GetRowData(4));
    rf_card_info.type = (RfCardType)ATOI(query.GetRowData(5));
    return;
}

int UserRfCard::GetUserRfCardByUUID(const std::string& uuid, RfCardInfo& rf_card_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << rf_card_info_sec << " from RfCard where UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetUserRfCardFromSql(rf_card_info, query);
    }
    else
    {
        AK_LOG_WARN << "get UserRfCardInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}

int UserRfCard::GetUserRfCardByPersonalAccountUUID(const std::string& personal_account_uuid, RfCardInfo& rf_card_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << rf_card_info_sec << " from RfCard where PersonalAccountUUID = '" << personal_account_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetUserRfCardFromSql(rf_card_info, query);
    }
    else
    {
        AK_LOG_WARN << "get UserRfCardInfo by PersonalAccountUUID failed, PersonalAccountUUID = " << personal_account_uuid;
        return -1;
    }
    return 0;
}

int UserRfCard::GetUserRfCardByOfficeDeliveryUUID(const std::string& office_delivery_uuid, RfCardInfo& rf_card_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << rf_card_info_sec << " from RfCard where OfficeDeliveryUUID = '" << office_delivery_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetUserRfCardFromSql(rf_card_info, query);
    }
    else
    {
        AK_LOG_WARN << "get UserRfCardInfo by OfficeDeliveryUUID failed, OfficeDeliveryUUID = " << office_delivery_uuid;
        return -1;
    }
    return 0;
}

int UserRfCard::GetUserRfCardByProjectUUID(const std::string& project_uuid, UserRfCardMap& account_rf_map, UserRfCardMap& delivery_rf_map)
{
    std::stringstream stream_sql;
    stream_sql << "select " << rf_card_info_sec << " from RfCard where AccountUUID = '" << project_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        RfCardInfo info;
        GetUserRfCardFromSql(info, query);
        if (info.type == RfCardType::Account)
        {
            account_rf_map.insert(std::make_pair(info.personal_account_uuid, info));
        }
        else if (info.type == RfCardType::Delivery)
        {
            delivery_rf_map.insert(std::make_pair(info.office_delivery_uuid, info));
        }
    }    
    return 0;
}

}