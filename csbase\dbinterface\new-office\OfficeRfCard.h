#ifndef __DB_USER_RF_CARD_H__
#define __DB_USER_RF_CARD_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"


enum class RfCardType{
    Account = 1,
    Delivery = 2,
};

typedef struct RfCardInfo_T
{
    char uuid[36];
    char project_uuid[36];
    char personal_account_uuid[36];
    char office_delivery_uuid[36];
    char code[20];
    RfCardType type;
    RfCardInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} RfCardInfo;

using UserRfCardMap = std::multimap<std::string/*user uuid*/, RfCardInfo>;

namespace dbinterface {

class UserRfCard
{
public:
    static int GetUserRfCardByUUID(const std::string& uuid, RfCardInfo& rf_card_info);
    static int GetUserRfCardByPersonalAccountUUID(const std::string& personal_account_uuid, RfCardInfo& rf_card_info);
    static int GetUserRfCardByOfficeDeliveryUUID(const std::string& office_delivery_uuid, RfCardInfo& rf_card_info);

    static int GetUserRfCardByProjectUUID(const std::string& project_uuid, UserRfCardMap& account_rf_map, UserRfCardMap& delivery_rf_map);
private:
    UserRfCard() = delete;
    ~UserRfCard() = delete;
    static void GetUserRfCardFromSql(RfCardInfo& rf_card_info, CRldbQuery& query);
};

}
#endif
