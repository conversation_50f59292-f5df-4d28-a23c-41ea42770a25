#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "dbinterface/new-office/OfficeTempKey.h"

namespace dbinterface {

static const uint32_t kDayOfWeek[7] = {0x01, 0x02, 0x04, 0x08, 0x10, 0x20, 0x40}; //从周日开始，到周六结束

static const std::string office_temp_key_info_sec = " Code, Name, IDNumber, CreatorType, \
    CreatorPersonalAccountUUID, CreatorAccountUUID, AccessTimes, EachAllowedTimes, AllowedTimes, \
    SchedulerType, DateFlag, StartTime, StopTime, BeginDateTime, EndDateTime, DeliveryMethod, \
    QrCodeUrl, AccountUUID, OfficeCompanyUUID, UUID, RBACDataGroupUUID ";

void OfficeTempKey::GetOfficeTempKeyFromSql(OfficeTempKeyInfo& office_temp_key_info, CRldbQuery& query)
{
    office_temp_key_info.code = ATOI(query.GetRowData(0));
    Snprintf(office_temp_key_info.name, sizeof(office_temp_key_info.name), query.GetRowData(1));
    Snprintf(office_temp_key_info.id_number, sizeof(office_temp_key_info.id_number), query.GetRowData(2));
    office_temp_key_info.creator_type = (OfficeTempKeyCreatorType)ATOI(query.GetRowData(3));
    Snprintf(office_temp_key_info.creator_personal_account_uuid, sizeof(office_temp_key_info.creator_personal_account_uuid), query.GetRowData(4));
    Snprintf(office_temp_key_info.creator_account_uuid, sizeof(office_temp_key_info.creator_account_uuid), query.GetRowData(5));
    office_temp_key_info.access_times = ATOI(query.GetRowData(6));
    office_temp_key_info.each_allowed_times = ATOI(query.GetRowData(7));
    office_temp_key_info.allowed_times = ATOI(query.GetRowData(8));
    office_temp_key_info.scheduler_type = ATOI(query.GetRowData(9));
    office_temp_key_info.date_flag = ATOI(query.GetRowData(10));
    Snprintf(office_temp_key_info.start_time, sizeof(office_temp_key_info.start_time), query.GetRowData(11));
    Snprintf(office_temp_key_info.stop_time, sizeof(office_temp_key_info.stop_time), query.GetRowData(12));
    Snprintf(office_temp_key_info.begin_date_time, sizeof(office_temp_key_info.begin_date_time), query.GetRowData(13));
    Snprintf(office_temp_key_info.end_date_time, sizeof(office_temp_key_info.end_date_time), query.GetRowData(14));
    Snprintf(office_temp_key_info.delivery_method, sizeof(office_temp_key_info.delivery_method), query.GetRowData(15));
    Snprintf(office_temp_key_info.qr_code_url, sizeof(office_temp_key_info.qr_code_url), query.GetRowData(16));
    Snprintf(office_temp_key_info.account_uuid, sizeof(office_temp_key_info.account_uuid), query.GetRowData(17));
    Snprintf(office_temp_key_info.office_company_uuid, sizeof(office_temp_key_info.office_company_uuid), query.GetRowData(18));
    Snprintf(office_temp_key_info.uuid, sizeof(office_temp_key_info.uuid), query.GetRowData(19));
    Snprintf(office_temp_key_info.rbac_datagroup_uuid, sizeof(office_temp_key_info.rbac_datagroup_uuid), query.GetRowData(20));
    
    return;
}

int OfficeTempKey::GetOfficeTempKeyListInfo(const std::string& devices_uuid, OfficeTempKeyInfo& office_temp_key_info)
{
    std::stringstream stream_sql;
    stream_sql << "select Relay, SecurityRelay from OfficeTempKeyList where OfficeTempKeyUUID = '" << office_temp_key_info.uuid << "' and DevicesUUID = '" << devices_uuid << "'";
            
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        office_temp_key_info.relay = ATOI(query.GetRowData(0));
        office_temp_key_info.security_relay = ATOI(query.GetRowData(1));
    }
    else
    {
        AK_LOG_WARN << "GetOfficeTempKeyListInfo Failed, TempKey Code = " << office_temp_key_info.code << ", devices uuid = " << devices_uuid;
        return -1;
    }
    
    return 0;
}

int OfficeTempKey::GetOfficeTempKeyInfo(const std::string& code, const std::string& project_uuid, OfficeTempKeyInfo& office_temp_key_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_temp_key_info_sec << " from OfficeTempKey where Code = '" << code << "' and AccountUUID = '" << project_uuid << "'";
    
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetOfficeTempKeyFromSql(office_temp_key_info, query);
    }
    else
    {
        AK_LOG_WARN << "GetOfficeTempKeyInfo Failed, TempKey Code = " << code << ", project_uuid = " << project_uuid;
        return -1;
    }
    
    return 0;
    
}

// 校验tempkey使用时间
bool OfficeTempKey::WithinValidTime(OfficeTempKeyInfo& office_tempkey_info, const std::string& time_zone, std::map<std::string, AKCS_DST>& date_info)
{
    int today_of_week = 0;
    std::string timenow;
    std::stringstream stream_sql;

    if (office_tempkey_info.scheduler_type == SchedType::ONCE_SCHED || office_tempkey_info.scheduler_type == SchedType::EACH_DOOR_ONCE_SCHED)
    {
        timenow = GetNodeNowDateTimeByTimeZoneStr(time_zone, date_info);
        stream_sql << "select count(*) from OfficeTempKey where UUID = '" << office_tempkey_info.uuid << "' and BeginDateTime < '" << timenow << "' and EndDateTime > '" << timenow << "' order by ID desc limit 1";
    }
    else if (office_tempkey_info.scheduler_type == SchedType::DAILY_SCHED)
    {
        GetWeekDayAndTimeByTimeZoneStr(time_zone, timenow, date_info);
        stream_sql << "select count(*) from OfficeTempKey where UUID = '" << office_tempkey_info.uuid << "' and StartTime < '" << timenow << "' and StopTime > '" << timenow << "' order by ID desc limit 1";
    }
    else if (office_tempkey_info.scheduler_type == SchedType::WEEKLY_SCHED)
    {
        today_of_week = GetWeekDayAndTimeByTimeZoneStr(time_zone, timenow, date_info);
        stream_sql << "select DateFlag from OfficeTempKey where UUID = '" << office_tempkey_info.uuid << "' and StartTime < '" << timenow << "' and StopTime > '" << timenow << "' order by ID desc limit 1";
    }

    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        if (office_tempkey_info.scheduler_type == SchedType::ONCE_SCHED || office_tempkey_info.scheduler_type == SchedType::EACH_DOOR_ONCE_SCHED || office_tempkey_info.scheduler_type == SchedType::DAILY_SCHED)
        {
            return ATOI(query.GetRowData(0)) > 0;
        }
        else if (office_tempkey_info.scheduler_type == SchedType::WEEKLY_SCHED)
        {
            int scheduler_day = ATOI(query.GetRowData(0));
            unsigned int today = kDayOfWeek[today_of_week];
            
            if ((scheduler_day & today) != today)
            {
                AK_LOG_INFO << "CheckTempKey WithinValidTime Failed, SchedulerType is weekly, TempKey Code = " << office_tempkey_info.code << ", today_of_week = " << today_of_week << ", scheduler_day = " << scheduler_day ;
                return false;
            }
        }
    }
    else
    {
        AK_LOG_INFO << "CheckTempKey WithinValidTime Failed, TempKey Code = " << office_tempkey_info.code << ", timenow = " << timenow;
        return false;
    }

    return true;
}

int OfficeTempKey::UpdateAccessTimes(OfficeTempKeyInfo& office_tempkey_info)
{
    if (SchedType::ONCE_SCHED == office_tempkey_info.scheduler_type || SchedType::EACH_DOOR_ONCE_SCHED == office_tempkey_info.scheduler_type)
    {
        office_tempkey_info.access_times = office_tempkey_info.access_times + 1;

        std::stringstream stream_sql;
        stream_sql << "update OfficeTempKey set AccessTimes = " << office_tempkey_info.access_times << " where UUID = '" << office_tempkey_info.uuid << "'";
    
        GET_DB_CONN_ERR_RETURN(db_conn, -1);
    
        db_conn->Execute(stream_sql.str());
    }

    return 0;
}

}
