#ifndef __DB_OFFICE_TEMP_KEY_H__
#define __DB_OFFICE_TEMP_KEY_H__

#include <map>
#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "util_time.h"
#include "BasicDefine.h"
#include "AkcsCommonDef.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

enum class OfficeTempKeyCreatorType
{
    PM = 1,
    ENDUSER = 2,
    INSTALLER = 3,
    ADMIN = 4
};

typedef struct OfficeTempKeyInfo_T
{
    int code;
    char name[256];
    char id_number[128];
    OfficeTempKeyCreatorType creator_type;
    char creator_personal_account_uuid[64]; // endUser
    char creator_account_uuid[64];          // ins,admin,pm
    int access_times;
    int each_allowed_times;
    int allowed_times;
    int scheduler_type;
    int date_flag;
    char start_time[32];
    char stop_time[32];
    char begin_date_time[32];
    char end_date_time[32];
    char delivery_method[64];
    char qr_code_url[128];
    char account_uuid[64];
    char office_company_uuid[64];
    char uuid[64];
    char rbac_datagroup_uuid[64];
    int relay;
    int security_relay;
    OfficeTempKeyInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} OfficeTempKeyInfo;

namespace dbinterface {

class OfficeTempKey
{
public:
    static int GetOfficeTempKeyListInfo(const std::string& devices_uuid, OfficeTempKeyInfo& office_temp_key_info);
    static int GetOfficeTempKeyInfo(const std::string& code, const std::string& project_uuid, OfficeTempKeyInfo& office_temp_key_info);
    static bool WithinValidTime(OfficeTempKeyInfo& office_tempkey_info, const std::string& time_zone, std::map<std::string, AKCS_DST>& date_info);
    static int UpdateAccessTimes(OfficeTempKeyInfo& office_tempkey_info);
    
private:
    OfficeTempKey() = delete;
    ~OfficeTempKey() = delete;
    static void GetOfficeTempKeyFromSql(OfficeTempKeyInfo& office_temp_key_info, CRldbQuery& query);
};

}
#endif
