#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "OfficeDevices.h"
#include <string.h>
#include "AkLogging.h"
#include "AkcsMonitor.h"
#include "AkcsPasswdConfuse.h"
#include "AwsConnectionPool.h"
#include "util.h"
#include "dbinterface/Shadow.h"
#include "ConnectionManager.h"


namespace dbinterface
{

static const std::string office_devices_sec = " D.ID,D.Type,D.MngAccountID,D.UnitID,D.Node,\
    D.MAC,D.location,D.Grade,D.ConfigMD5,D.SipAccount,D.SipPwd,D.NetGroupNumber,\
    D.RtspPwd,D.ContactMD5,D<PERSON>how,D.<PERSON>,D.Config,D.DclientVer,D.SipType,\
    D.Flags,D.IPAddress,D.SecurityRelay,D.Firmware,D.Function,D.Status,D.UUID,\
    D.AccountUUID,D.CommunityUnitUUID";

OfficeDevices::OfficeDevices()
{

}

void OfficeDevices::GetDevicesFromSql(OfficeDevPtr dev, CRldbQuery& query)
{
    dev->id = ATOI(query.GetRowData(0));
    dev->dev_type = ATOI(query.GetRowData(1));
    dev->office_id = ATOI(query.GetRowData(2));
    dev->unit_id = ATOI(query.GetRowData(3));
    Snprintf(dev->node, sizeof(dev->node), query.GetRowData(4));
    Snprintf(dev->mac, sizeof(dev->mac), query.GetRowData(5));
    Snprintf(dev->location, sizeof(dev->location), query.GetRowData(6));
    dev->grade = ATOI(query.GetRowData(7));
    Snprintf(dev->config_md5, sizeof(dev->config_md5), query.GetRowData(8));
    Snprintf(dev->sip, sizeof(dev->sip), query.GetRowData(9));
    Snprintf(dev->sippwd, sizeof(dev->sippwd), query.GetRowData(10));
    dev->net_group_number = ATOI(query.GetRowData(11));
    Snprintf(dev->rtsppwd, sizeof(dev->rtsppwd), query.GetRowData(12));
    Snprintf(dev->contact_md5, sizeof(dev->contact_md5), query.GetRowData(13));
    dev->stair_show = ATOI(query.GetRowData(14));
    Snprintf(dev->relay, sizeof(dev->relay), query.GetRowData(15));
    Snprintf(dev->autop_config, sizeof(dev->autop_config), query.GetRowData(16));
    dev->dclient_ver = ATOI(query.GetRowData(17));
    dev->sip_type = ATOI(query.GetRowData(18));
    dev->flags = ATOI(query.GetRowData(19));
    Snprintf(dev->ipaddr, sizeof(dev->ipaddr), query.GetRowData(20));
    Snprintf(dev->security_relay, sizeof(dev->security_relay), query.GetRowData(21));
    Snprintf(dev->sw_ver, sizeof(dev->sw_ver), query.GetRowData(22));
    dev->fun_bit = strtoul(query.GetRowData(23), nullptr, 10);
    dev->status = ATOI(query.GetRowData(24));
    Snprintf(dev->uuid, sizeof(dev->uuid), query.GetRowData(25));
    Snprintf(dev->project_uuid, sizeof(dev->project_uuid), query.GetRowData(26));
    Snprintf(dev->unit_uuid, sizeof(dev->unit_uuid), query.GetRowData(27));

    std::string srcpwd = dev->rtsppwd;
    PasswdDecode(srcpwd.c_str(), srcpwd.size(), dev->rtsppwd, sizeof(dev->rtsppwd));
    srcpwd = dev->sippwd;
    PasswdDecode(srcpwd.c_str(), srcpwd.size(), dev->sippwd, sizeof(dev->sippwd));
    std::string sw_ver = dev->sw_ver;
    auto pos = sw_ver.find(".");
    if (pos != std::string::npos)
    {
        dev->oem_id = ATOI(sw_ver.substr(pos + 1).c_str());
        dev->firmwares = ATOI(sw_ver.substr(0,pos).c_str());
    }    

    dev->camera_num = ::dbinterface::SwitchHandle(dev->fun_bit, FUNC_DEV_SUPPORT_MULTI_MONITOR) ? 2 : 1;

    return;
}

void OfficeDevices::TransferOfficePtrToDevSetting(OfficeDevPtr dev_ptr, DEVICE_SETTING* dev)
{
    if (dev_ptr != nullptr && dev != nullptr)
    {
        dev->id = dev_ptr->id;
        dev->type = dev_ptr->dev_type;
        dev->manager_account_id = dev_ptr->office_id;
        dev->unit_id = dev_ptr->unit_id;
        Snprintf(dev->device_node, sizeof(dev->device_node), dev_ptr->node);
        Snprintf(dev->mac, sizeof(dev->mac), dev_ptr->mac);
        Snprintf(dev->location, sizeof(dev->location), dev_ptr->location);
        dev->grade = dev_ptr->grade;
        Snprintf(dev->config_md5, sizeof(dev->config_md5), dev_ptr->config_md5);
        Snprintf(dev->sip_account, sizeof(dev->sip_account), dev_ptr->sip);
        Snprintf(dev->sip_password, sizeof(dev->sip_password), dev_ptr->sippwd);
        dev->netgroup_num = dev_ptr->net_group_number;
        Snprintf(dev->rtsp_password, sizeof(dev->rtsp_password), dev_ptr->rtsppwd);
        Snprintf(dev->contact_md5, sizeof(dev->contact_md5), dev_ptr->contact_md5);
        dev->stair_show = dev_ptr->stair_show;
        Snprintf(dev->relay, sizeof(dev->relay), dev_ptr->relay);
        dev->dclient_version = dev_ptr->dclient_ver;
        dev->sip_type = dev_ptr->sip_type;
        dev->flags = dev_ptr->flags;
        dev->fun_bit = dev_ptr->fun_bit;
        Snprintf(dev->ip_addr, sizeof(dev->ip_addr), dev_ptr->ipaddr);
        Snprintf(dev->security_relay, sizeof(dev->security_relay), dev_ptr->security_relay);
        dev->status = dev_ptr->status;
        Snprintf(dev->uuid, sizeof(dev->uuid), dev_ptr->uuid);
        Snprintf(dev->project_uuid, sizeof(dev->project_uuid), dev_ptr->project_uuid);
        dev->firmware = dev_ptr->firmwares;

        return;
    }
}

int OfficeDevices::InitDevicesBySip(const std::string& sip, OfficeDevPtr &dev)
{
    std::stringstream sql;
    sql << "select " << office_devices_sec <<" from Devices D where SipAccount = '"
              << sip << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        dev = std::make_shared<OfficeDev>();
        GetDevicesFromSql(dev, query);
    }
    else
    {
        AK_LOG_WARN << "Get mac info error by sip, mac not exist. sip=" << sip;
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;    
}

int OfficeDevices::GetLocationAndNodeBySip(const std::string& sip, std::string& location, std::string& node)
{
    OfficeDevPtr dev = nullptr;
    int ret = InitDevicesBySip(sip, dev);
    if (dev)
    {
        location = dev->location;
        node = dev->node;
    }
    else
    {
        location = "";
        node = "";
    }
    return ret;
}

int OfficeDevices::GetDevTypeBySip(const std::string& sip)
{
    OfficeDevPtr  dev = nullptr;
    InitDevicesBySip(sip, dev);
    if (dev)
    {
        return dev->dev_type;
    }

    return -1;
}

std::string OfficeDevices::GetLocationBySip(const std::string& sip)
{
    OfficeDevPtr  dev = nullptr;
    InitDevicesBySip(sip, dev);
    if (dev)
    {
        return dev->location;
    }
    else
    {
        return "";
    }
}

int OfficeDevices::GetNodeDevList(const std::string& node, OfficeDevList &devlist)
{
    if ( node.length() == 0 )
    {
        AK_LOG_WARN << "GetNodeDevList failed. node=null!";
        return 1;
    }
    std::stringstream sql;
    sql << "select " << office_devices_sec <<" from Devices D where Node = '" << node << "' and Grade=" << csmain::COMMUNITY_DEVICE_TYPE_PERSONAL;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        OfficeDevPtr dev = std::make_shared<OfficeDev>();
        GetDevicesFromSql(dev, query);
        devlist.push_back(dev);
    }
    ReleaseDBConn(conn);
    return 0;  
}

/*获取部门所有公共设备*/
int OfficeDevices::GetDepartmentDevList(uint32_t department_id, OfficeDevList &devlist)
{
    std::stringstream sql;
    sql << "select " << office_devices_sec <<" from Devices D where UnitID = '"
              << department_id
              << "' and Grade ="<< csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        OfficeDevPtr dev = std::make_shared<OfficeDev>();
        GetDevicesFromSql(dev, query);
        devlist.push_back(dev);
    }
    ReleaseDBConn(conn);
    return 0;  
}

/*获取最外层公共设备*/
int OfficeDevices::GetPubDevList(uint32_t office_id, OfficeDevList &devlist)
{
    std::stringstream sql;
    sql << "select " << office_devices_sec <<" from Devices D where MngAccountID = '"
              << office_id
              << "' and Grade ="<< csmain::COMMUNITY_DEVICE_TYPE_PUBLIC;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        OfficeDevPtr dev = std::make_shared<OfficeDev>();
        GetDevicesFromSql(dev, query);
        devlist.push_back(dev);
    }
    ReleaseDBConn(conn);
    return 0;  
}

int OfficeDevices::UpdateOfficeDevMD5(OfficeDevPtr &dev, DEVICES_MD5_TYPE type, int is_aws)
{
    if (!dev)
    {
        return 0;
    }
    
    RldbPtr conn;
    if(is_aws)
    {
        conn = GetAwsDBConnPollInstance()->GetConnection();
    }
    else
    {
        conn = GetDBConnPollInstance()->GetConnection();
    }

    
    CRldb* tmp_conn = conn.get();
    if (nullptr == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    std::string md5_sec = "";
    std::string md5 = "";
    switch(type)
    {
        case CONFIG_MD5:
            md5 = "ConfigMD5";
            md5_sec = dev->config_md5;
            break;
        case FACE_MD5:
            md5 = "FaceMD5";
            md5_sec = dev->face_md5;
            break;
        case USER_MATE_MD5:
            md5 = "UserMetaMD5";
            md5_sec = dev->user_mate_md5;
            break;
        case SCHEDULE_MD5:
            md5 = "ScheduleMD5";
            md5_sec = dev->schedule_md5;;
            break;
        case CONTACT_MD5:
            md5 = "ContactMD5";
            md5_sec = dev->contact_md5;;
            break;                
    }

    std::stringstream sql;
    sql << "update Devices set  " << md5 <<"='" << md5_sec <<"' where ID =" << dev->id;           

    int ret = tmp_conn->Execute(sql.str()) >= 0 ? 0 : -1;
    if (-1 == ret)
    {
        char error_msg[1024];
        snprintf(error_msg, sizeof(error_msg), "Update devices failed, ID is [%d], %s is [%s]", dev->id, md5.c_str(), md5_sec.c_str());
        AK_LOG_WARN << error_msg;
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csadapt", error_msg, AKCS_MONITOR_ALARM_UPDATE_DEVICE_MD5_FAILED);
    }       

    if(is_aws)
    {
        ReleaseAwsDBConn(conn);
    }
    else
    {
        ReleaseDBConn(conn);
    }

    return 0;
}

int OfficeDevices::UpdateMd5ByID(uint32_t id, SHADOW_TYPE shadow_type, const std::string& value)
{
    std::stringstream sql;
    
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::string column = dbinterface::Shadow::GetMd5ColumnByType(shadow_type);
    if(column.size() == 0)
    {
        AK_LOG_WARN << "shadow type is illegal.";
        ReleaseDBConn(conn);
        return -1;
    }
    
    sql << "UPDATE Devices set " << column 
         << "='"  << value << "' where ID=" << id;
   
    int ret = tmp_conn->Execute(sql.str()) >= 0 ? 0 : -1; 
    if (-1 == ret)
    {
        char error_msg[1024];
        snprintf(error_msg, sizeof(error_msg), "Update Devices failed, ID is [%d], %s is [%s]", id, column.c_str(), value.c_str());
        AK_LOG_WARN << error_msg;
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("dbinterface", error_msg, AKCS_MONITOR_ALARM_UPDATE_DEVICE_MD5_FAILED);
    }     
    ReleaseDBConn(conn);
    return ret;
}


int OfficeDevices::UpdateOfficeDevMD5(OfficeDevList &dev_list, DEVICES_MD5_TYPE type, int is_aws)
{
    for(auto dev : dev_list)
    {
        UpdateOfficeDevMD5(dev, type, is_aws);
    }
    return 0;
}

/*获取 Company 的所有的管理机*/
int OfficeDevices::GetAllMngDevListByCompanyUUID(const std::string& company_uuid, OfficeDevList &devlist)
{
    if(company_uuid.size() == 0){
        AK_LOG_WARN << "GetAllMngDevListByCompanyUUID failed: company_uuid is empty.";
        return -1;
    }

    std::stringstream sql;
    sql << "SELECT " << office_devices_sec 
        << " FROM OfficeDeviceAssign A INNER JOIN Devices D ON A.DevicesUUID=D.UUID WHERE A.OfficeCompanyUUID='"
        << company_uuid << "' AND D.Type=" << DEVICE_TYPE_MANAGEMENT;

    GET_DB_CONN_ERR_RETURN(conn, -1);
    CRldbQuery query(conn.get());
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        OfficeDevPtr dev = std::make_shared<OfficeDev>();
        GetDevicesFromSql(dev, query);
        devlist.push_back(dev);
    }
    
    return 0;  
}

/*获取小区所有的管理机*/
int OfficeDevices::GetAllMngDevList(const string& office_uuid, OfficeDevList &devlist)
{
    std::stringstream sql;
    sql << "select " << office_devices_sec <<" from Devices D where AccountUUID='"
        << office_uuid << "' and Type=" << DEVICE_TYPE_MANAGEMENT;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        OfficeDevPtr dev = std::make_shared<OfficeDev>();
        GetDevicesFromSql(dev, query);
        devlist.push_back(dev);
    }
    ReleaseDBConn(conn);
    return 0;  
}

/*获取小区所有的管理机*/
int OfficeDevices::GetAllMngDevList(uint32_t office_id, OfficeDevList &devlist)
{
    std::stringstream sql;
    sql << "select " << office_devices_sec <<" from Devices D where MngAccountID="
        << office_id << " and Type=" << DEVICE_TYPE_MANAGEMENT;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        OfficeDevPtr dev = std::make_shared<OfficeDev>();
        GetDevicesFromSql(dev, query);
        devlist.push_back(dev);
    }
    ReleaseDBConn(conn);
    return 0;  
}

int OfficeDevices::GetAllPubDevList(uint32_t office_id, OfficeDevList &devlist)
{
    std::stringstream sql;
    sql << "select " << office_devices_sec <<" from Devices D where MngAccountID = '"
              << office_id
              << "' and (Grade ="<< csmain::COMMUNITY_DEVICE_TYPE_PUBLIC 
              << " or Grade =" << csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT <<")";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        OfficeDevPtr dev = std::make_shared<OfficeDev>();
        GetDevicesFromSql(dev, query);
        devlist.push_back(dev);
    }
    ReleaseDBConn(conn);
    return 0;       
}

int OfficeDevices::GetMacDev(const std::string& mac, OfficeDevList &dev_list)
{
    OfficeDevPtr dev = std::make_shared<OfficeDev>();
    GetMacDev(mac, dev);
    dev_list.push_back(dev);
    return 0;
}

int OfficeDevices::GetMacDev(const std::string& mac, OfficeDevPtr &dev)
{
    std::stringstream sql;
    sql << "select " << office_devices_sec <<" from Devices D where Mac = '" << mac << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        OfficeDevPtr dev1 = std::make_shared<OfficeDev>();
        GetDevicesFromSql(dev1, query);
        dev = dev1;
    }
    else
    {
        AK_LOG_WARN << "Get mac info error, mac not exist. mac=" << mac;
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;  
}

int OfficeDevices::GetDevByUUID(const std::string& uuid, OfficeDevPtr &dev)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_devices_sec <<" from Devices D where UUID = '" << uuid << "'";

    GET_DB_CONN_ERR_RETURN(conn, -1);
    CRldbQuery query(conn.get());
    query.Query(stream_sql.str());
    
    if(query.MoveToNextRow())
    {
        OfficeDevPtr dev1 = std::make_shared<OfficeDev>();
        GetDevicesFromSql(dev1, query);
        dev = dev1;
    }
    else
    {
        AK_LOG_WARN << "GetDevByUUID failed, dev not exist. uuid = " << uuid;
        return -1;
    }

    return 0;
}

int OfficeDevices::GetAllOfficeDevList(uint32_t office_id, OfficeDevList &devlist)
{
    std::stringstream sql;
    sql << "select " << office_devices_sec <<" from Devices D where MngAccountID="<< office_id;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        OfficeDevPtr dev = std::make_shared<OfficeDev>();
        GetDevicesFromSql(dev, query);
        devlist.push_back(dev);
    }
    ReleaseDBConn(conn);
    return 0;      
}

int OfficeDevices::GetMacListDevList(const std::set<std::string> &mac_set, OfficeDevList &devlist)
{
    int size = mac_set.size();
    if(size == 0)
    {
        return 0;
    }
    std::string mas_str = ListToSeparatedFormatString(mac_set);
    
    std::stringstream sql;
    sql << "select " << office_devices_sec << " from Devices D where mac in(";
    sql << mas_str << ")";
    
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }   
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        OfficeDevPtr dev = std::make_shared<OfficeDev>();
        GetDevicesFromSql(dev, query);
        devlist.push_back(dev);
    }
    ReleaseDBConn(conn);
    return 0;
}


int OfficeDevices::GetSipDev(const std::string& sip, OfficeDevPtr &dev)
{
    std::stringstream sql;
    sql << "select " << office_devices_sec <<" from Devices D where SipAccount = '"
              << sip
              << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        OfficeDevPtr dev1 = std::make_shared<OfficeDev>();
        GetDevicesFromSql(dev1, query);
        dev = dev1;
    }
    else
    {
        AK_LOG_WARN << "Get mac info error, sip account not exist. sip=" << sip;
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;     
}

int OfficeDevices::GetAllOfficeIndoorListByMngID(uint32_t office_id, OfficeDevList &dev_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_devices_sec << " from Devices D where MngAccountID = '" << office_id << "' and Type = " << DEVICE_TYPE_INDOOR;

    GET_DB_CONN_ERR_RETURN(conn, -1);
    CRldbQuery query(conn.get());
    query.Query(stream_sql.str());
    
    while (query.MoveToNextRow())
    {
        OfficeDevPtr dev = std::make_shared<OfficeDev>();
        GetDevicesFromSql(dev, query);
        dev_list.push_back(dev);
    }
    
    return 0;  
}


int OfficeDevices::GetAllOfficeIndoorListByMngID(const std::string& office_uuid, OfficeDevList &dev_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << office_devices_sec << " from Devices D where AccountUUID = '" << office_uuid << "' and Type = " << DEVICE_TYPE_INDOOR;

    GET_DB_CONN_ERR_RETURN(conn, -1);
    CRldbQuery query(conn.get());
    query.Query(stream_sql.str());
    
    while (query.MoveToNextRow())
    {
        OfficeDevPtr dev = std::make_shared<OfficeDev>();
        GetDevicesFromSql(dev, query);
        dev_list.push_back(dev);
    }
    
    return 0;  
}


int OfficeDevices::GetAllOfficeDevList(const std::string& office_uuid, OfficeDevMap &devlist)
{
    std::stringstream sql;
    sql << "select " << office_devices_sec <<" from Devices D where AccountUUID = '"<< office_uuid << "'";

    GET_DB_CONN_ERR_RETURN(tmp_conn, -1);
    CRldbQuery query(tmp_conn.get());
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        OfficeDevPtr dev = std::make_shared<OfficeDev>();
        GetDevicesFromSql(dev, query);
        devlist.insert(std::make_pair(dev->uuid, dev));    
    }
    
    return 0;      
}

DatabaseExistenceStatus OfficeDevices::GetIsAttendanceByUUID(const std::string& uuid)
{
    std::stringstream stream_sql;
    stream_sql << "SELECT COUNT(*) FROM Attendance.OfficeAttendanceDevice WHERE DevicesUUID = '" << uuid << "'";

    GET_DB_CONN_ERR_RETURN(conn, DatabaseExistenceStatus::QUERY_ERROR);
    CRldbQuery query(conn.get());
    int row = query.Query(stream_sql.str());
    
    if (row < 0) {
        return DatabaseExistenceStatus::QUERY_ERROR;
    }
    query.MoveToNextRow();
    return ATOI(query.GetRowData(0)) > 0 ? DatabaseExistenceStatus::EXIST : DatabaseExistenceStatus::NOT_EXIST;  
}


int OfficeDevices::GetAllOfficeDevListByProjectID(uint32_t office_id, OfficeDevList &pub_dev_list, 
 OfficeDevNodeMap &node_map, OfficeDevUnitMap& unit_map, OfficeDevList &mng_dev_list,
 OfficeDevList &pub_unit_all_dev_list, OfficeDevMacMap &mac_dev_list, OfficeDevList &all_dev_list)
{
    std::stringstream sql;
    sql << "select " << office_devices_sec <<" from Devices D where MngAccountID="<< office_id;

    GET_DB_CONN_ERR_RETURN(tmp_conn, -1);

    CRldbQuery query(tmp_conn.get());
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        OfficeDevPtr dev = std::make_shared<OfficeDev>();
        GetDevicesFromSql(dev, query);
        if(dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
        {
            pub_dev_list.push_back(dev);
            pub_unit_all_dev_list.push_back(dev);
        }
        else if(dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
        {
            unit_map.insert(std::make_pair(dev->unit_id, dev));
            pub_unit_all_dev_list.push_back(dev);
        }
        else if(dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
        {
            node_map.insert(std::make_pair(dev->node, dev)); 
        }
        
        if (dev->dev_type == DEVICE_TYPE_MANAGEMENT)
        {
            mng_dev_list.push_back(dev);
        }
        all_dev_list.push_back(dev);
        mac_dev_list.insert(std::make_pair(dev->mac, dev));
    }
    return 0;      
}





}

