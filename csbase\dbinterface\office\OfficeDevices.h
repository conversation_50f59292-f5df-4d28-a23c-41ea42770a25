#ifndef __OFFICE_DEVICES_H__
#define __OFFICE_DEVICES_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include <set>
#include "AkcsCommonDef.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/Shadow.h"
#include "AkcsCommonSt.h"


typedef struct OfficeDev_T
{
    uint32_t id;
    uint32_t office_id;
    char node[16];
    char ipaddr[64];
    char mac[16];
    //char firmware[32];
    char config_md5[36];
    char face_md5[36];
    short sip_type;// siptype
    char sip[16];
    char sippwd[64]; //sip密码
    char location[DEV_LOCATION_SIZE];
    char rtsppwd[64]; //个人终端用户使用
    int dev_type;
    uint32_t unit_id;// 单元id
    short grade;//设备的归属等级，是社区共享，单元共享，还是用户独占
    char contact_md5[36];
    char pushbutton[512];
    short net_group_number;//网络编号
    short stair_show; //梯口机列表显示配置  0默认 1roomnum 2app/indoor
    char relay[4096];//dtmf 信息。#,name,1,1;*,xx,是否显示在home,是否显示在talking;
    char security_relay[4096];//dtmf 信息。#,name,1,1;*,xx,是否显示在home,是否显示在talking;
    char autop_config[2048];//新增的配置
    uint32_t dclient_ver;//dclient版本，用于兼容旧版本
    char user_mate_md5[36];
    char user_info_md5[36];
    char schedule_md5[36];
    int flags;
    uint32_t status;
    char uuid[64];
    char call_seq[256];//呼叫顺序
    uint64_t fun_bit;
    char project_uuid[64];
    char unit_uuid[64];
    char sw_ver[32];
    int firmwares; //固件型号 29/915
    int oem_id;    
    int camera_num;
    DatabaseExistenceStatus is_attendance;   //是否考勤设备 
    bool exist_invalid_door; // 存在非法的door
    OfficeDev_T() {
        memset(this, 0, sizeof(*this));
    }
} OfficeDev;

typedef std::shared_ptr<OfficeDev> OfficeDevPtr;
typedef std::vector<OfficeDevPtr> OfficeDevList;
using OfficeDevMap = std::map<std::string/*uuid*/ ,OfficeDevPtr>;
using OfficeDevUnitMap = std::multimap<uint32_t/*unit id*/ ,OfficeDevPtr>;
using OfficeDevNodeMap = std::multimap<std::string/*uuid*/ ,OfficeDevPtr>;
using OfficeDevMacMap = std::multimap<std::string/*mac*/ ,OfficeDevPtr>;



namespace dbinterface
{

class OfficeDevices
{
public:
    OfficeDevices();
    ~OfficeDevices();

    static std::string GetLocationBySip(const std::string& sip);
    static int GetLocationAndNodeBySip(const std::string& sip, std::string& location, std::string& node);
    static int GetDevTypeBySip(const std::string& sip);
    /*家庭*/
    static int GetNodeDevList(const std::string& node, OfficeDevList &devlist);
    static int GetSipDev(const std::string& sip, OfficeDevPtr &devlist);
    static int UpdateOfficeDevMD5(OfficeDevList &dev_list, DEVICES_MD5_TYPE type, int is_aws = 0);
    static int UpdateOfficeDevMD5(OfficeDevPtr &dev, DEVICES_MD5_TYPE type, int is_aws = 0);
    static int UpdateMd5ByID(uint32_t id, SHADOW_TYPE shadow_type, const std::string& value);    
    static int GetMacDev(const std::string& mac, OfficeDevPtr &dev);
    static int GetMacDev(const std::string& mac, OfficeDevList &dev_list);
    static int GetDevByUUID(const std::string& uuid, OfficeDevPtr &dev);
    /*获取所有管理机*/
    static int GetAllMngDevList(uint32_t office_id, OfficeDevList &devlist);
    static int GetAllMngDevList(const std::string& office_uuid, OfficeDevList &devlist);
    static int GetAllMngDevListByCompanyUUID(const std::string& company_uuid, OfficeDevList &devlist);
    /*单元公共设备*/
    static int GetDepartmentDevList(uint32_t department_id, OfficeDevList &devlist);
    /*最外围公共设备*/
    static int GetPubDevList(uint32_t office_id, OfficeDevList &devlist);
    /*所有单元+最外围公共设备*/
    static int GetAllPubDevList(uint32_t office_id, OfficeDevList &devlist);
    /*所有office的设备*/
    static int GetAllOfficeDevList(uint32_t office_id, OfficeDevList &devlist);
    static int GetAllOfficeDevList(const std::string& office_uuid, OfficeDevMap &devlist);    
    static int GetMacListDevList(const std::set<std::string> &mac_set, OfficeDevList &devlist);
    static void TransferOfficePtrToDevSetting(OfficeDevPtr dev_ptr, DEVICE_SETTING* dev);
    static int GetAllOfficeIndoorListByMngID(uint32_t office_id, OfficeDevList &devlist);
    static int GetAllOfficeIndoorListByMngID(const std::string& office_uuid, OfficeDevList &devlist);
    /*判断是否为考勤机*/
    static DatabaseExistenceStatus GetIsAttendanceByUUID(const std::string& uuid);

    static int GetAllOfficeDevListByProjectID(uint32_t office_id, OfficeDevList &pub_dev_list, 
     OfficeDevNodeMap &node_map, OfficeDevUnitMap& unit_map, OfficeDevList &mng_dev_list, 
     OfficeDevList &pub_unit_all_dev_list, OfficeDevMacMap &mac_dev_list, OfficeDevList &all_dev_list);

    
private:
    static void GetDevicesFromSql(OfficeDevPtr dev, CRldbQuery& query);
    static int InitDevicesBySip(const std::string& sip, OfficeDevPtr &dev);
};


}
#endif
