#include <sstream>
#include <string.h>
#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "OfficeInfo.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/DataConfusion.h"
#include "ConnectionManager.h"

// encrypt field : MobileNumber

enum AutopDataType
{
    LIMIT_FLOW = 0, //表示有限流量
    NO_LIMIT_FLOW = 1 //表示不限流量
};

OfficeInfo::OfficeInfo(unsigned int officeid)
{
    office_id_ = officeid;
    enable_motion_ = 0;
    motion_time_ = 0;
    face_enrollment_ = 0;
    id_card_verification_ = 0;
    switch_flag_ = 0;
    is_expire_ = 0;
    init_success_ = 0;
    init();
}

OfficeInfo::OfficeInfo(const std::string &office_uuid)
{
    office_id_ = 0;
    office_uuid_ = office_uuid;
    enable_motion_ = 0;
    motion_time_ = 0;
    face_enrollment_ = 0;
    id_card_verification_ = 0;
    switch_flag_ = 0;
    is_expire_ = 0;
    init_success_ = 0;
    init();
}


OfficeInfo::~OfficeInfo()
{

}


void OfficeInfo::init()
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmpconn = conn.get();
    if (NULL == tmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }

    std::stringstream sql;
    std::stringstream where;
    if (office_uuid_.size() > 0)
    {
        where << "where A.UUID= '" << office_uuid_ << "'";

    }
    else
    {
        where << "where A.ID= " << office_id_;
    }
    sql << "SELECT  C.EnableMotion, C.MotionTime,A.location, C.Street, C.FaceEnrollment, C.IDCardVerification, "
           << " C.Switch , C.FeatureExpireTime < now(),"
           << " A.TimeZone, A.CustomizeForm,  C.MobileNumber, C.PhoneCode,ISNULL(FeatureExpireTime),C.Country,C.States,C.City, "
           << " C.NameDisplay,C.IsNew,A.UUID,C.IsEmergencyAllDoor FROM Account A inner join OfficeInfo C on C.AccountUUID=A.UUID "
           << where.str();
    
    CRldbQuery query(tmpconn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        enable_motion_ = dbinterface::ATOI(query.GetRowData(0));
        motion_time_ = dbinterface::ATOI(query.GetRowData(1));
        name_ = query.GetRowData(2);
        street_ = query.GetRowData(3);
        face_enrollment_ = dbinterface::ATOI(query.GetRowData(4));
        id_card_verification_ = dbinterface::ATOI(query.GetRowData(5));
        switch_flag_ = dbinterface::ATOI(query.GetRowData(6));
        is_expire_ = dbinterface::ATOI(query.GetRowData(7));
        timezone_ = query.GetRowData(8);
        time_format_ = dbinterface::ATOI(query.GetRowData(9));
        Snprintf(mobile_number_, sizeof(mobile_number_),  dbinterface::DataConfusion::Decrypt(query.GetRowData(10)).c_str());
        Snprintf(phone_code_, sizeof(phone_code_),  query.GetRowData(11));
        int isnull =  dbinterface::ATOI(query.GetRowData(12));
        if (isnull)
        {
            is_expire_ = 1;
        }        
        country_ = query.GetRowData(13);
        states_ = query.GetRowData(14);
        city_ = query.GetRowData(15);
        contact_display_order_ = ATOI(query.GetRowData(16));
        is_new_office_ = ATOI(query.GetRowData(17));
        office_uuid_ = query.GetRowData(18);
        is_all_emergency_door_ = ATOI(query.GetRowData(19)) == 1 ? true : false;

        init_success_ = 1;
    }
        
    ReleaseDBConn(conn);
    return;
}


int OfficeInfo::initFeaturePlan()
{
    feature_is_init_ = FeaturePlanStatus::Init;
    //高级功能
    feature_item_ = 0;
    feature_id_ = 0;
    std::stringstream stream_sql;
    stream_sql << "select FeatureID from ManageFeature where AccountID ="<< office_id_;

    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        feature_id_ = dbinterface::ATOI(query.GetRowData(0));     
    }
    //feature_id_ == 0 升级高级功能前的数据
    if (feature_id_ != 0 )
    {
        //FeatureID>0，查找具体功能是否在高级功能里
        std::stringstream sql2;
        sql2 << "select Item from FeaturePlan where ID = " << feature_id_;

        query.Query(sql2.str());
        if (query.MoveToNextRow())
        {
            feature_item_ = dbinterface::ATOI(query.GetRowData(0));     
        } 
    }         
    return 0;
}


int OfficeInfo::isEnableMotion()
{
    return enable_motion_;
}

int OfficeInfo::MotionTime()
{
    return motion_time_;
}

std::string & OfficeInfo::Name()
{
    return name_;
}

std::string & OfficeInfo::Street()
{
    return street_;
}

int OfficeInfo::FaceEnrollment()
{
    return face_enrollment_;
}

int OfficeInfo::IDCard()
{
    return id_card_verification_;
}

int OfficeInfo::IsAllowCreatePin()
{
    return dbinterface::SwitchHandle(switch_flag_, SwitchType::AllowPin);
}

bool OfficeInfo::IsExpire()
{
    return (is_expire_ == 1) ? true : false;
}

int OfficeInfo::LimitFlowRemind()
{
    int flag = dbinterface::SwitchHandle(switch_flag_, SwitchType::SIMNotify);
    return flag;
}

int OfficeInfo::LimitFlowDataType()
{
    int flag = LimitFlowRemind();
    if (flag > 0)
    {
        return AutopDataType::LIMIT_FLOW;
    }
    else
    {
        return AutopDataType::NO_LIMIT_FLOW;
    }
}

//add bu xuzr,检查高级功能Featureplan中Item字段，第一位(个位):快递件，第二位：pin是否可用，第三位：QrCode是否可用，第四位：限制家庭成员。
bool OfficeInfo::CheckFeature(int index)
{
    if (feature_is_init_ == FeaturePlanStatus::UnInit)
    {
        initFeaturePlan();
    }

    //FeatureID=0代表旧小区，Tempkey/Pin按照之前的逻辑根据开关配置，快递和家庭成员控制关闭
    if (feature_id_ == 0)
    {
        if(index == TAB_DEVICES_CHECK_INDEX_PIN || index == TAB_DEVICES_CHECK_INDEX_TMPKEY)
        {
            return true;
        }
        else
        {
            return false;
        }
    }
    else
    {
        if (dbinterface::SwitchHandle(feature_item_, index))
        {
           return true;
        }
        return false;
        
    }
    return true;
}

std::string OfficeInfo::TimeZone()
{
    return timezone_;
}

int OfficeInfo::TimeFormat()
{
    return time_format_;
}

std::string OfficeInfo::MobileNumber()
{
    return mobile_number_;
}

std::string OfficeInfo::PhoneCode()
{
    return phone_code_;
}

std::string OfficeInfo::GetCommunityUnitName(int unit_id)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* temp_conn = conn.get();
    if (NULL == temp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return "";
    }

    std::stringstream sql;
    sql << "SELECT UnitName FROM CommunityUnit "
               << "WHERE ID = '"
               << unit_id
               << "'";

    CRldbQuery query(temp_conn);
    query.Query(sql.str());

    std::string unit_name;
    if (query.MoveToNextRow())
    {
        unit_name = query.GetRowData(0);
    }

    ReleaseDBConn(conn);
    return unit_name;
}

std::tuple<std::string, std::string> OfficeInfo::GetPMAccountLanguageLocation()
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* temp_conn = conn.get();
    if (NULL == temp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return std::make_tuple("", "");
    }

    std::stringstream sql;
    sql << "SELECT a.Language, a.Location, a.Account "
           "  FROM Account a "
           " LEFT JOIN PropertyMngList b ON a.ID = b.CommunityID "
           "  WHERE b.CommunityID =  " << office_id_ <<
           " LIMIT 1";

    CRldbQuery query(temp_conn);
    query.Query(sql.str());

    std::string language;
    std::string location;
    if (query.MoveToNextRow())
    {
        language = query.GetRowData(0);
        location = query.GetRowData(1);
    }

    ReleaseDBConn(conn);
    return std::make_tuple(language, location);
}

int OfficeInfo::EnableLandline()
{
    return dbinterface::SwitchHandle(switch_flag_, SwitchType::Landline);
}

std::string OfficeInfo::Country()
{
    return country_;
}

std::string OfficeInfo::States()
{
    return states_;
}

std::string OfficeInfo::City()
{
    return city_;
}

std::string OfficeInfo::UUID()
{
    return office_uuid_;
}

unsigned int OfficeInfo::ID()
{
    return office_id_;
}

int OfficeInfo::ContactDisplayOrder()
{
    return contact_display_order_;
}

bool OfficeInfo::EnableAutoEmergency()
{
    return dbinterface::SwitchHandle(switch_flag_, SwitchType::ENABLE_AUTO_EMERGENCY);
}

bool OfficeInfo::EmergencyNeedNotify()
{
    return dbinterface::SwitchHandle(switch_flag_, SwitchType::ENABLE_EMERGENCY_NOTIFY);
}

bool OfficeInfo::IsNew()
{
    return is_new_office_;
}

int OfficeInfo::InitSuccess()
{
    return init_success_;
}

bool OfficeInfo::IsAllEmergencyDoor()
{
    return is_all_emergency_door_;
}
