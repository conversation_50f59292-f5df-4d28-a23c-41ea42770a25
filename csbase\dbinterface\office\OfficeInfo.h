#ifndef __OFFICE_INFO_H__
#define __OFFICE_INFO_H__
#include <string>
#include <memory>
#include <tuple>
#include <map>
#include "util_time.h"

class OfficeInfo
{
public:
    OfficeInfo() = default;
    OfficeInfo(unsigned int officeid);
    OfficeInfo(const std::string &uuid);
    ~OfficeInfo();

    enum SwitchType
    {
        Landline = 0,
        OfflineNotify = 1,
        AllowPin = 2,
        SIMNotify = 3,
        ENABLE_AUTO_EMERGENCY = 4,
        ENABLE_EMERGENCY_NOTIFY = 5,
    };

    enum class FeaturePlanStatus    
    {
        UnInit = 0,   
        Init = 1
    };

    enum FeaturePlan
    {
        TAB_DEVICES_CHECK_INDEX_DELIVERY = 0,
        TAB_DEVICES_CHECK_INDEX_PIN,
        TAB_DEVICES_CHECK_INDEX_TMPKEY,
        TAB_DEVICES_CHECK_INDEX_FAMILYMEMBER,
    };

    enum AllowCreatePin
    {
        DENY = 0,
        ALLOW = 1,
    };

    enum class EnumIsNew    // IsNew 字段枚举
    {
        OLD = 0,            // 旧办公
        NEW = 1             // 新办公
    };
    
    int isEnableMotion();
    int MotionTime();
    std::string &Name();
    std::string &Street();
    int FaceEnrollment();
    int IDCard();
    int IsAllowCreatePin();
    int LimitFlowRemind();   
    int LimitFlowDataType();
    bool IsExpire();
    bool CheckFeature(int index);
    std::string TimeZone();
    int TimeFormat();
    std::string MobileNumber();
    std::string PhoneCode();
    std::string GetCommunityUnitName(int unit_id);
    std::tuple<std::string,std::string> GetPMAccountLanguageLocation();
    int EnableLandline();
    std::string City();
    std::string States();
    std::string Country();
    int ContactDisplayOrder();
    bool EnableAutoEmergency();
    bool EmergencyNeedNotify();
    std::string UUID();
    unsigned int ID();
    bool IsNew();
    int InitSuccess();
    bool IsAllEmergencyDoor();

private:
    void init();
    int initFeaturePlan();
    int init_success_;
    unsigned int office_id_;
    int enable_motion_;
    int motion_time_;
    std::string name_;
    std::string street_;
    int face_enrollment_;
    int id_card_verification_;
    int switch_flag_;
    int is_expire_;

    int feature_item_;
    int feature_id_;
    int feature_expire_;
    FeaturePlanStatus feature_is_init_ = FeaturePlanStatus::UnInit;
    std::string timezone_;
    int time_format_;

    char mobile_number_[25];
    char phone_code_[9];
    
    std::string country_; // 国家
    std::string states_;  // 省份
    std::string city_;    // 城市

    int contact_display_order_; // 联系人姓名展示顺序，0代表FirstName+LastName, 1代表LastName+FirstName 
    std::string office_uuid_;

    bool is_new_office_ = false; //是否为新办公
    bool is_all_emergency_door_ = false; //紧急告警开门是否选定所有门
};

typedef std::shared_ptr<OfficeInfo> OfficeInfoPtr;



#endif
