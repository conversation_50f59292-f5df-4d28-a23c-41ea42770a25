#include <sstream>
#include <string.h>
#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "AkcsCommonDef.h"
#include "OfficeDevices.h"
#include "ConnectionManager.h"
#include "OfficePersonalAccount.h"
#include "dbinterface/CommunityUnit.h"
#include "util.h"
#include "util_judge.h"

namespace dbinterface
{

OfficePersonalAccount::OfficePersonalAccount()
{

}


// encrypt field : Name、FirstName、LastName、Phone、Phone2、Phone3

static const std::string office_account_sec = " ID, Name,ParentID,Account,UnitID,EnableIpDirect,UUID, \
Phone,PhoneCode,SipAccount,Active,ExpireTime < now() as isExpire,Role,Initialization,TempKeyPermission,FirstName,LastName, \
UserInfoUUID,EnableStrongAlarm,ParentUUID,Version, BLECode,NFCCode, CommunityUnitUUID";



void OfficePersonalAccount::GetAccountFromSql(OfficeAccount &account, CRldbQuery& query)
{
    account.id = ATOI(query.GetRowData(0));
    Snprintf(account.name, sizeof(account.name), dbinterface::DataConfusion::Decrypt(query.GetRowData(1)).c_str());
    account.office_id = ATOI(query.GetRowData(2));
    Snprintf(account.account, sizeof(account.account), query.GetRowData(3));
    account.unit_id = ATOI(query.GetRowData(4));
    account.ip_direct = ATOI(query.GetRowData(5));
    Snprintf(account.uuid, sizeof(account.uuid), query.GetRowData(6));
    Snprintf(account.phone, sizeof(account.phone), dbinterface::DataConfusion::Decrypt(query.GetRowData(7)).c_str());
    Snprintf(account.phone_code, sizeof(account.phone_code), query.GetRowData(8));
    snprintf(account.phone_with_phonecode, sizeof(account.phone_with_phonecode), "%s%s", account.phone_code, account.phone);
    Snprintf(account.sip_account, sizeof(account.sip_account), query.GetRowData(9));
    account.active = ATOI(query.GetRowData(10));
    account.is_expire = ATOI(query.GetRowData(11));
    account.role = ATOI(query.GetRowData(12));
    account.is_init = ATOI(query.GetRowData(13));
    account.is_show_tmpkey = ATOI(query.GetRowData(14));
    Snprintf(account.firstname, sizeof(account.firstname), dbinterface::DataConfusion::Decrypt(query.GetRowData(15)).c_str());
    Snprintf(account.lastname, sizeof(account.lastname), dbinterface::DataConfusion::Decrypt(query.GetRowData(16)).c_str());
    Snprintf(account.user_info_uuid, sizeof(account.user_info_uuid), query.GetRowData(17));
    account.strong_alarm = ATOI(query.GetRowData(18));
    Snprintf(account.parent_uuid, sizeof(account.parent_uuid), query.GetRowData(19));
    account.version = ATOI(query.GetRowData(20));
    Snprintf(account.ble_code, sizeof(account.ble_code), query.GetRowData(21));
    Snprintf(account.nfc_code, sizeof(account.nfc_code), query.GetRowData(22));
    Snprintf(account.unit_uuid, sizeof(account.unit_uuid), query.GetRowData(23));


    account.conn_type = csmain::DeviceType::OFFICE_APP;    

    if (akjudge::IsNodeAccountRole(account.role))
    {
        Snprintf(account.office_uuid, sizeof(account.office_uuid), account.parent_uuid);
    }
    else
    {
        //办公暂无从账号场景
    }

    return;
}

int OfficePersonalAccount::InitAccountByUid(const std::string& uid, OfficeAccount &account)
{
    std::stringstream stream_sql;
    stream_sql << "select "<< office_account_sec <<" from PersonalAccount where Account = '" << uid << "'";

    int ret = -1;
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetAccountFromSql(account, query);
        ret = 0;
    }
    else
    {
        AK_LOG_WARN << "Get uid info error, uid not exist. uid=" << uid << ", maybe uid is devices sipaccount.";
    }
    ReleaseDBConn(conn);

    return ret;    
}


int OfficePersonalAccount::InitAccountByUuid(const std::string& uuid, OfficeAccount &account)
{
    std::stringstream sql;
    sql << "select "<< office_account_sec <<" from PersonalAccount where UUID = '" << uuid << "'";

    int ret = -1;
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        GetAccountFromSql(account, query);
        ret = 0;
    }
    else
    {
        AK_LOG_WARN << "Get Uuid info error, Uuid not exist. Uuid=" << uuid;
    }
    ReleaseDBConn(conn);

    return ret;    
}


int OfficePersonalAccount::InitAccountByUser(const std::string& user, OfficeAccount &account)
{
    auto pos = user.find('@');
    if (pos == std::string::npos)
    {
        return InitAccountByUid(user, account);
    }
    else
    {
        PerAccountUserInfo per_account_user;
        if (0 == dbinterface::PersonalAccountUserInfo::GetPerAccountInfoByEmail(user, per_account_user))
        {
            return InitAccountByUid(per_account_user.main_user_account, account);
        }
    }

    return -1;
}

int OfficePersonalAccount::InitAccountByEmail(const std::string& email, OfficeAccount &account)
{
    PerAccountUserInfo per_account_user;
    //查找主站点的account
    if (0 == dbinterface::PersonalAccountUserInfo::GetPerAccountInfoByEmail(email, per_account_user))
    {
        return InitAccountByUid(per_account_user.main_user_account, account);
    }

    return -1;
}


std::string OfficePersonalAccount::GetNickNameByUid(const std::string& uid)
{
    OfficeAccount account;
    if (InitAccountByUid(uid, account) == 0)
    {
        return account.name;
    }
    return "";
}

uint32_t OfficePersonalAccount::GetOfficeIDByEmail(const std::string& email)
{
    OfficeAccount account;
    if (InitAccountByEmail(email, account) == 0)
    {
        return account.office_id;
    }
    return 0;
}

uint32_t OfficePersonalAccount::GetAccountNameAndOfficeIdByUid(const std::string& uid, 
    std::string& name, uint32_t &office_id)
{
    OfficeAccount account;
    if (InitAccountByUid(uid, account) == 0)
    {
        office_id = account.office_id;
        name = account.name;

    }
    return 0;
}

int OfficePersonalAccount::GetDepartmentAccountList(uint32_t unit_id, OfficeAccountList& account_list)
{
    std::stringstream stream_sql;
    stream_sql << "select "<< office_account_sec <<" from PersonalAccount where UnitID = " << unit_id;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeAccount account;
        GetAccountFromSql(account, query);
        account_list.push_back(account);
    }
    ReleaseDBConn(conn);
    return 0;
}


//获取社区的主账号 如果是社区公共的就是全部主账号，如果是单元，就是单元内的主账号
int OfficePersonalAccount::GetAllAccountList(uint32_t mng_id, 
  OfficeAccountList& account_list, OfficeAccountUnitIDMap& unit_id_map, OfficeAccountMateMap &account_mate_map)
{
    std::stringstream sql;
    sql << "select "<< office_account_sec <<" from PersonalAccount where ParentID ="
              << mng_id << " and Role in (" << GetOfficeRoleStr() << ");";
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        OfficeAccount account;
        GetAccountFromSql(account, query);
        account_list.push_back(account);
        account_mate_map.insert(std::make_pair(account.account, account));
        unit_id_map[account.unit_id].push_back(account);
    }
    ReleaseDBConn(conn);
    return 0;
}

int OfficePersonalAccount::GetUidAccount(const std::string &uid, OfficeAccount& account)
{
    return InitAccountByUid(uid, account);
}

int OfficePersonalAccount::GetUserAccount(const std::string &struser, OfficeAccount& account)
{
    return InitAccountByUser(struser, account);
}

int OfficePersonalAccount::GetEmailAccount(const std::string &email, OfficeAccount& account)
{
    return InitAccountByEmail(email, account);
}

int OfficePersonalAccount::GetUUIDAccount(const std::string &uuid, OfficeAccount& account)
{
    return  InitAccountByUuid(uuid, account);
}

int OfficePersonalAccount::InitAccountCnf(OfficeAccountList& account)
{
    for (auto &acc : account)
    {
        InitAccountCnf(acc);
    }

    return 0;
}


int OfficePersonalAccount::InitAccountCnf(OfficeAccount& account)
{
    if (strlen(account.account) == 0)
    {
        AK_LOG_WARN << "InitAccountCnf account is null.";
    }
    OfficeAccountCnf *cnf = &account.cnf;
    if (cnf->init_status == 1)
    {
        return 0;
    }
    
    std::stringstream sql;
    sql << "select P.Account,P.EnableMotion,P.MotionTime,P.CallType,S.SipGroup,C.EmployeeID,C.Flags From PersonalAccountCnf P "
<< "join  SipGroup2 S on S.Account=P.Account join PersonalAccountOfficeInfo C on C.PersonalAccountUUID='" 
<< account.uuid <<"' where P.Account = '" << account.account << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        cnf->init_status = 1;
        std::string tmp_account = query.GetRowData(0);
        cnf->enable_motion = ATOI(query.GetRowData(1));
        cnf->motion_time = ATOI(query.GetRowData(2));
        cnf->call_type = ATOI(query.GetRowData(3));
        Snprintf(cnf->sipgroup, sizeof(cnf->sipgroup), query.GetRowData(4));
        Snprintf(cnf->employee_id, sizeof(cnf->employee_id), query.GetRowData(5));
        cnf->flags = ATOI(query.GetRowData(6));
    }
    else
    {
        cnf->init_status = 1;
        AK_LOG_WARN << "Get uid cnf error, uid not exist. uid=" << account.account;
    }
    ReleaseDBConn(conn);
    return 0;
}

bool OfficePersonalAccount::TestFlag(OfficeAccount& account, int flag)
{
    OfficeAccountCnf *cnf = &account.cnf;
    assert(cnf->init_status);
    return SwitchHandle(cnf->flags, flag);
}

int OfficePersonalAccount::GetAccountListByPhoneList(const std::vector<std::string>& phone_list, OfficeAccountList& account_list)
{
    GET_DB_CONN_ERR_RETURN(db_conn, -1)
    CRldbQuery query(db_conn.get());

    for (const auto& phone : phone_list)
    {
        std::stringstream stream_sql;
        stream_sql << "select " << office_account_sec << " from PersonalAccount where Phone = '" << phone << "'";

        query.Query(stream_sql.str());
        while (query.MoveToNextRow())
        {
            OfficeAccount account;
            GetAccountFromSql(account, query);
            account_list.push_back(account);
        }
    }
    return 0;
}


int OfficePersonalAccount::GetPhoneAccountList(const std::string& phone, OfficeAccountList& account_list)
{
    if (phone.length() < PHONE_DETECT_NUM)//add by chenzhx ******** 如果短于这个值，应该是错误的号码，不能往下匹配，会匹配很多号码，而导致出错。
    {
        return -1;
    }
    
    std::string detect_phone = GetSubstrFromBehind(phone, PHONE_DETECT_NUM);
    
    // 从mapping库中模糊查询出尾号匹配的phone
    std::vector<std::string> encrypt_phone_list;
    dbinterface::PhoneMapping::FuzzySearchPhone(detect_phone, encrypt_phone_list);

    // 通过尾号匹配的phone查出账号信息
    OfficeAccountList fuzzy_account_list;
    GetAccountListByPhoneList(encrypt_phone_list, fuzzy_account_list);
    
    //获取最大匹配数
    int matchnum = 0;
    for (auto& account : fuzzy_account_list)
    {
        int num = GetStrMatchNumFromBehind(phone, account.phone_with_phonecode);
        if (num > matchnum)
        {
            matchnum = num;
        }
    }
    
    for (auto& account : fuzzy_account_list)
    {
        int num = GetStrMatchNumFromBehind(phone, account.phone_with_phonecode);
        if (num == matchnum)
        {
            account_list.push_back(account);
        }
    }
    
    return 0;
}


//只返回一个匹配值
int OfficePersonalAccount::GetPhoneAccountForOfficeid(const std::string& phone, unsigned int mng_id, OfficeAccount &account)
{  
    int ret = -1;
    OfficeAccountList tmp_account_list;
    GetPhoneAccountList(phone, tmp_account_list);
    
    int match_num = 0;
    for (auto& account_tmp : tmp_account_list)
    {
        if (account_tmp.office_id == mng_id)
        {
            match_num++;
            account = account_tmp;
            ret = 0;
        }
    }
    
    if (match_num > 1)
    {
        AK_LOG_WARN << "Get phone info have match several number=" << match_num << " phone=" << phone;
    }

    return ret;
}

//只返回一个匹配值
int OfficePersonalAccount::GetPhoneAccountForUid(const std::string& phone, const std::string &uid, OfficeAccount &account)
{   
    int ret = -1;
    OfficeAccountList tmp_account_list;
    GetPhoneAccountList(phone, tmp_account_list);
    
    int match_num = 0;
    for (auto& account_tmp : tmp_account_list)
    {
        if (strcmp(account_tmp.account, uid.c_str()) == 0)
        {
            match_num++;
            account = account_tmp;
            ret = 0;
        }
    }
    
    if (match_num > 1)
    {
        AK_LOG_WARN << "get phone info have match several number=" << match_num << " phone=" << phone;
    }

    return ret;
}

int OfficePersonalAccount::UpdateAllDataVersion(int office_id)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* pTmpConn = conn.get();
    if (NULL == pTmpConn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }


    std::stringstream sql;
    sql << "update PersonalAccount set Version=UNIX_TIMESTAMP() "
        << "where ParentID ="
        << office_id 
        << " and Role in (" << GetOfficeRoleStr() << ");";

    int ret = pTmpConn->Execute(sql.str()) >= 0 ? 0 : -1;
    if (-1 == ret)
    {
        AK_LOG_WARN << "UpdateDataVersion failed, office_id = " << office_id;
    }

    ReleaseDBConn(conn);
    return 0;

}

int OfficePersonalAccount::CheckUserRedirectByDis(int office_id)
{    
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        return 0;
    }
    CRldbQuery query(tmp_conn);
    
    std::stringstream sql;
    sql << " select R.Cloud from AwsRedirect R join Account A on A.ParentID = R.AccountID where A.ID = " << office_id;
    
    query.Query(sql.str());
    
    if (query.MoveToNextRow())
    {
        int cloud = ATOI(query.GetRowData(0));        
        AK_LOG_INFO << "Office DaoCheckUserRedirect return Yes, office_id: " << office_id << ", cloud = " << cloud;
        ReleaseDBConn(conn);
        return cloud;
    }
    
    ReleaseDBConn(conn);
    return 0;

}

bool OfficePersonalAccount::CheckUserRedirectByProject(int office_id)
{    
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        return false;
    }
    CRldbQuery query(tmp_conn);
    
    std::stringstream sql;
    sql << " select ID from AwsRedirect where AccountID = " << office_id;
    
    query.Query(sql.str());
    if (!query.MoveToNextRow())
    {
        AK_LOG_INFO << "Office DaoCheckUserRedirect return No";
        ReleaseDBConn(conn);
        return false;
    }

    AK_LOG_INFO << "Office DaoCheckUserRedirect return Yes, office_id: " << office_id;
    ReleaseDBConn(conn);
    return true;

}

std::string OfficePersonalAccount::GetFloorByAccountUUID(const std::string& account_uuid)
{
    std::string floor;
    std::stringstream stream_sql;
    stream_sql << "select Floor from PersonalAccountOfficeInfo where PersonalAccountUUID = '" << account_uuid << "'";
    
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
       AK_LOG_WARN << "Get DB conn failed.";
       return "";
    }

    CRldbQuery query(tmp_conn);
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        floor = query.GetRowData(0);
    }
    ReleaseDBConn(conn);
    return floor;
}

int OfficePersonalAccount::IsEnableIntercom(const std::string& account_uuid)
{    
    std::stringstream sql;
    sql << "select Flags from PersonalAccountOfficeInfo where PersonalAccountUUID='" << account_uuid <<"'";
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return 1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        int flags = ATOI(query.GetRowData(0));
        ReleaseDBConn(conn);
        return dbinterface::SwitchHandle(flags, OfficeAccountFlags::EnableCall);
    }

    ReleaseDBConn(conn);
    return 1;
}

std::string OfficePersonalAccount::GetFloorByAccount(const std::string& account)
{
    std::string floor;
    std::stringstream stream_sql;
    stream_sql << "select Floor from PersonalAccountOfficeInfo P left join PersonalAccount A on P.PersonalAccountUUID = A.UUID where A.Account = '" << account << "'";
    
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
       AK_LOG_WARN << "Get DB conn failed.";
       return "";
    }

    CRldbQuery query(tmp_conn);
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        floor = query.GetRowData(0);
    }
    
    ReleaseDBConn(conn);
    return floor;
}

int OfficePersonalAccount::GetNewOfficeAllPersonnelList(const std::string& project_uuid, OfficeAccountList& account_list)
{
    std::stringstream stream_sql;
    stream_sql << "select "<< office_account_sec <<" from PersonalAccount where ParentUUID = '" << project_uuid << "' and Role = " << ACCOUNT_ROLE_OFFICE_NEW_PER;

    GET_DB_CONN_ERR_RETURN(conn, -1);
    CRldbQuery query(conn.get());
    
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeAccount account;
        GetAccountFromSql(account, query);
        account_list.push_back(account);
    }
    
    return 0;
}


int OfficePersonalAccount::GetNewOfficeAllAdminAppList(const std::string& project_uuid, OfficeAccountList& account_list)
{
    std::stringstream stream_sql;
    stream_sql << "select "<< office_account_sec <<" from PersonalAccount where ParentUUID = '" << project_uuid << "' and Role = " << ACCOUNT_ROLE_OFFICE_NEW_ADMIN;

    GET_DB_CONN_ERR_RETURN(conn, -1);
    CRldbQuery query(conn.get());
    
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        OfficeAccount account;
        GetAccountFromSql(account, query);
        account_list.push_back(account);
    }
    
    return 0;
}

int OfficePersonalAccount::GetAllAccountList(std::string &project_uuid, OfficeAccountMap& account_map, OfficeAccountMateMap &account_mate_map)
{
    std::stringstream sql;
    sql << "select "<< office_account_sec <<" from PersonalAccount where ParentUUID = '" << project_uuid << "' and Role in (" << GetOfficeRoleStr() << ");";

    GET_DB_CONN_ERR_RETURN(tmp_conn, -1);
    CRldbQuery query(tmp_conn.get());
    query.Query(sql.str());

    while (query.MoveToNextRow())
    {
        OfficeAccount account;
        GetAccountFromSql(account, query);
        account_map.insert(std::make_pair(account.uuid, account));
        account_mate_map.insert(std::make_pair(account.account, account));
    }
    return 0;
}

std::string OfficePersonalAccount::GetLiftFloorNum(const OfficeAccount &account)
{
    // 个人的floor
    std::string user_floor = dbinterface::OfficePersonalAccount::GetFloorByAccountUUID(account.uuid);
    
    // 楼层的floor
    CommunityUnitInfo community_unit_info;
    dbinterface::CommunityUnit::GetCommunityUnitByUUID(account.unit_uuid, community_unit_info);

    // 取并集
    std::string access_floor = GetAccessibleFloor(community_unit_info.floor, user_floor);

    AK_LOG_INFO << "GetLiftFloorNum account = " << account.account << ", user_floor = " << user_floor 
                << ", unit_floor = " << community_unit_info.floor << ", access_floor = " << access_floor;
                
    return access_floor;
}


int OfficePersonalAccount::UpdateVersionByUUID(const std::string &uuid)
{
    GET_DB_CONN_ERR_RETURN(conn, -1)

    std::stringstream streamsql;
    streamsql << "update PersonalAccount set Version=UNIX_TIMESTAMP() where UUID = '" 
              << uuid
              << "'";    

    int ret = conn.get()->Execute(streamsql.str()) >= 0 ? 0 : -1;
    if (-1 == ret)
    {
        AK_LOG_WARN << "UpdateDataVersion failed, uuid = " << uuid;
        return ret;
    }

    return 0;
}

int OfficePersonalAccount::UpdateVersionByGroupUUID(const AkcsStringSet &group_list)
{
    GET_DB_CONN_ERR_RETURN(conn, -1);

    if (group_list.size() == 0)
    {
        return 0;
    }
    std::string group_list_str = ListToSeparatedFormatString(group_list);
    if (group_list_str.size() == 0)
    {
        return 0;
    }
    
    std::stringstream streamsql_personnel;
    streamsql_personnel << "UPDATE PersonalAccount P JOIN OfficePersonnelGroup G ON P.UUID = G.PersonalAccountUUID SET P.Version = UNIX_TIMESTAMP()  WHERE G.OfficeGroupUUID in (" 
              << group_list_str
              << ")";    

    int ret = conn.get()->Execute(streamsql_personnel.str()) >= 0 ? 0 : -1;


    std::stringstream streamsql_admin;
    streamsql_admin << "UPDATE PersonalAccount P JOIN OfficeAdminGroup G ON P.UUID = G.PersonalAccountUUID SET P.Version = UNIX_TIMESTAMP()  WHERE G.OfficeGroupUUID in (" 
              << group_list_str
              << ")";    

    ret = conn.get()->Execute(streamsql_admin.str()) >= 0 ? 0 : -1;

    if (-1 == ret)
    {
        AK_LOG_WARN << "UpdateVersionByGroupUUID failed, group_list_str = " << group_list_str;
    }

    return 0;
}

int OfficePersonalAccount::UpdateVersionByProjectUUID(const std::string &uuid)
{
    GET_DB_CONN_ERR_RETURN(conn, -1)

    std::stringstream streamsql;
    streamsql << "update PersonalAccount set Version=UNIX_TIMESTAMP() where ParentUUID = '" 
              << uuid
              << "' and Role in (" << GetOfficeRoleStr() << ");";    

    int ret = conn.get()->Execute(streamsql.str()) >= 0 ? 0 : -1;
    if (-1 == ret)
    {
        AK_LOG_WARN << "UpdateVersionByProjectUUID failed, uuid = " << uuid;
        return ret;
    }

    return 0;
}

int OfficePersonalAccount::GetUUIDByAccount(const std::string& account, std::string& uuid)
{
    uuid = "";
    std::stringstream sql;
    sql << "select UUID from PersonalAccount where Account = '" << account << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
       uuid = query.GetRowData(0);
    }
    ReleaseDBConn(conn);

    return 0;    
}


int OfficePersonalAccount::GetAllAccountCnfMap(const std::string &project_uuid, OfficeAccountCnfMap &account_cnf_map)
{
    GET_DB_CONN_ERR_RETURN(tmp_conn, -1);
    std::stringstream sql;
    sql << "select P.Account,P.EnableMotion,P.MotionTime,P.CallType,S.SipGroup,C.EmployeeID,C.Flags From PersonalAccountCnf P \
        left join PersonalAccount PP on PP.Account=P.Account \
        left join SipGroup2 S on S.Account=PP.Account \
        left join PersonalAccountOfficeInfo C on C.PersonalAccountUUID=PP.UUID  \
        where PP.ParentUUID='" << project_uuid <<  "' and Role in (" << GetOfficeRoleStr() << ");";


    CRldbQuery query(tmp_conn.get());
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        OfficeAccountCnf cnf;
        cnf.init_status = 1;
        std::string tmp_account = query.GetRowData(0);
        cnf.enable_motion = ATOI(query.GetRowData(1));
        cnf.motion_time = ATOI(query.GetRowData(2));
        cnf.call_type = ATOI(query.GetRowData(3));
        Snprintf(cnf.sipgroup, sizeof(cnf.sipgroup), query.GetRowData(4));
        Snprintf(cnf.employee_id, sizeof(cnf.employee_id), query.GetRowData(5));
        cnf.flags = ATOI(query.GetRowData(6));
        account_cnf_map.insert(std::make_pair(tmp_account, cnf));
    }
    return 0;

}


int OfficePersonalAccount::GetAccountByUUID(const std::string& uuid, std::string& account)
{
    std::stringstream sql;
    sql << "select Account from PersonalAccount where UUID = '"  << uuid << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
       account = query.GetRowData(0);
    }
    ReleaseDBConn(conn);

    return 0;    
}

//根据node获取家庭App账号列表
int OfficePersonalAccount::GetNodeUidListByNode(const std::string& node, std::vector<std::string>& uid_list)
{
    if (node.size() == 0)
    {
        AK_LOG_WARN << "get node uid list failed. node is empty";
        return -1;
    }

    uid_list.push_back(node);

    std::stringstream sql;
    sql << "select B.Account from PersonalAccount B "
        << "left join PersonalAccount A on A.UUID = B.ParentUUID "
        << "where A.Account = '" << node << "'"
        << "and (B.Role = " << ACCOUNT_ROLE_COMMUNITY_ATTENDANT << " or B.Role = " << ACCOUNT_ROLE_PERSONNAL_ATTENDANT << ")";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    std::string uid;
    while (query.MoveToNextRow())
    {
        uid = query.GetRowData(0);
        uid_list.push_back(uid);
    }

    ReleaseDBConn(conn);
    return 0;
}


std::string OfficePersonalAccount::GetNodeTimeZoneStr(const std::string& node)
{
    std::stringstream streamsql;
    streamsql << "select P.TimeZone, P.CustomizeForm, A.TimeZone, P.Role from PersonalAccount P join Account A on P.ParentID = A.ID where P.Account = '" << node << "'";

    std::string timezone;
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return timezone;//0时区
    }
    CRldbQuery query(tmp_conn);
    query.Query(streamsql.str());

    if (query.MoveToNextRow())
    {
        int role = ATOI(query.GetRowData(3));     
        if(ACCOUNT_ROLE_PERSONNAL_MAIN ==  role)
        {
            timezone = query.GetRowData(0);
        }
        else    //社区帐号用社区时区
        {
            timezone = query.GetRowData(2);
        }
    }

    ReleaseDBConn(conn);
    return timezone;
}

// 旧办公获取PM列表
int OfficePersonalAccount::GetOfficePmApplistByMngID(const std::string office_uuid, ResidentPerAccountList& account_list)
{
    std::stringstream stream_sql;
    stream_sql << "select A.Name,A.SipAccount,A.RoomNumber,A.EnableIpDirect,A.Phone,A.ID,A.Role,A.RoomID,A.PhoneCode,A.Phone2,A.Phone3,A.PhoneStatus,A.Special,B.CallType,A.UUID,A.UserInfoUUID,A.Account from PersonalAccount A "
               << "left join PersonalAccountCnf B on A.Account = B.Account "
               << "left join PmAccountMap C on A.UUID = C.PersonalAccountUUID "
               << "where A.ParentUUID = '" << office_uuid << "' and A.Role = " 
               << ACCOUNT_ROLE_COMMUNITY_PM << " and C.AppStatus = 1";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* temp_conn = conn.get();
    if (NULL == temp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    CRldbQuery query(temp_conn);
    query.Query(stream_sql.str());

    ResidentPerAccount account;
    while (query.MoveToNextRow())
    {
        memset(&account, 0, sizeof(account));
        Snprintf(account.name, sizeof(account.name),  dbinterface::DataConfusion::Decrypt(query.GetRowData(0)).c_str());
        Snprintf(account.sip_account, sizeof(account.sip_account), query.GetRowData(1));
        Snprintf(account.room_number, sizeof(account.room_number), query.GetRowData(2));
        account.ip_direct =  ATOI(query.GetRowData(3));
        Snprintf(account.phone, sizeof(account.phone),  dbinterface::DataConfusion::Decrypt(query.GetRowData(4)).c_str());
        account.id =  ATOI(query.GetRowData(5));
        account.role =  ATOI(query.GetRowData(6));
        Snprintf(account.phone_code, sizeof(account.phone_code), query.GetRowData(8));
        Snprintf(account.phone2, sizeof(account.phone2),  dbinterface::DataConfusion::Decrypt(query.GetRowData(9)).c_str());
        Snprintf(account.phone3, sizeof(account.phone3),  dbinterface::DataConfusion::Decrypt(query.GetRowData(10)).c_str());
        account.phone_status =  ATOI(query.GetRowData(11));
        account.only_apt =  ATOI(query.GetRowData(12));
        account.cnf.call_type = ATOI(query.GetRowData(13));
        Snprintf(account.uuid, sizeof(account.uuid), query.GetRowData(14));
        Snprintf(account.user_info_uuid, sizeof(account.user_info_uuid), query.GetRowData(15));
        Snprintf(account.account, sizeof(account.account), query.GetRowData(16));
        account_list.push_back(account);
    }

    ReleaseDBConn(conn);
    return 0;
}

}
