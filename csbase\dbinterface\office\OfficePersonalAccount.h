#ifndef __OFFICE_PERSONAL_ACCOUNT_H__
#define __OFFICE_PERSONAL_ACCOUNT_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include "RldbQuery.h"
#include "Rldb.h"
#include <assert.h>
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/DataConfusion.h"
#include "dbinterface/mapping/PhoneMapping.h"
#include "dbinterface/resident/PersonalAccountUserInfo.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"

typedef struct OfficePerAccountAdapt_T
{   
    int init_status;
    char sipgroup[32];
    int call_type;
    int call_loop;
    int enable_motion;
    int motion_time;
    char employee_id[32];
    int flags;
    int  phone_status; //0:不使用落地呼叫，1:使用
}OfficeAccountCnf;


typedef struct OfficePerAccount_T
{
    uint32_t id;
    char account[32];
    char uuid[64];
    char name[128];
    uint32_t office_id;
    uint32_t unit_id;
    char phone[32];
    char phone_code[16];
    char phone_with_phonecode[64];
    char sip_account[32];
    char call_seq[128];
    int ip_direct;
    int active;
    int is_expire;
    int role;
    int is_init;
    int is_show_tmpkey;
    char passwd[65];
    char firstname[128];
    char lastname[128];
    char user_info_uuid[64];
    int strong_alarm;
    char parent_uuid[64];
    char ble_code[32];
    char nfc_code[32];   
    char unit_uuid[32];
    char office_uuid[36]; //注意：数据库没有该字段
    uint32_t version; 
    csmain::DeviceType conn_type;//COMMUNITY_APP....
    OfficeAccountCnf cnf; 
    

    OfficePerAccount_T() {
        memset(this, 0, sizeof(*this));
    }
}OfficeAccount;

typedef std::vector<OfficeAccount>OfficeAccountList;


using OfficeUUIDSet = std::set<std::string>;
using OfficeAccountMap = std::map<std::string/*uuid*/, OfficeAccount>;
using OfficeAccountMateMap = std::map<std::string/*account*/, OfficeAccount>;/*user 详细信息需要account的映关系*/
using OfficeAccountCnfMap = std::map<std::string/*account*/, OfficeAccountCnf>;
using OfficeAccountUnitIDMap = std::map<uint32_t, OfficeAccountList>;


namespace dbinterface
{

class OfficePersonalAccount
{
public:
    OfficePersonalAccount();
    ~OfficePersonalAccount();
    
    enum OfficeAccountFlags
    {
        EnableCall = 0,
    };
    
    static std::string GetNickNameByUid(const std::string& uid);
    static uint32_t GetAccountNameAndOfficeIdByUid(const std::string& uid, std::string& name, uint32_t &office_id);
    static int GetDepartmentAccountList(uint32_t unit_id, OfficeAccountList& account_list);
    static int GetAllAccountList(uint32_t mng_id, OfficeAccountList& account_list, OfficeAccountUnitIDMap& unit_id_map, OfficeAccountMateMap &account_mate_map);
    static int GetAllAccountList(std::string &project_uuid, OfficeAccountMap& account_map, OfficeAccountMateMap &account_mate_map);
    static int GetUidAccount(const std::string &uid, OfficeAccount& account);
    static int GetUserAccount(const std::string &struser, OfficeAccount& account);
    static int GetEmailAccount(const std::string &email, OfficeAccount& account);
    static int GetUUIDByAccount(const std::string& account, std::string& uuid);
    static int InitAccountCnf(OfficeAccount& account);
    static int InitAccountCnf(OfficeAccountList& account);
    static bool TestFlag(OfficeAccount& account, int flag);
    //phone 相关
    static int GetPhoneAccountList(const std::string &phone, OfficeAccountList& account);
    static int GetPhoneAccountForOfficeid(const std::string& phone, unsigned int mng_id, OfficeAccount &account);
    static int GetPhoneAccountForUid(const std::string& phone, const std::string &uid, OfficeAccount &account);
    static uint32_t GetOfficeIDByEmail(const std::string& email);
    static int UpdateAllDataVersion(int office_id);
    static int CheckUserRedirectByDis(int office_id);
    static bool CheckUserRedirectByProject(int office_id);
    static std::string GetFloorByAccount(const std::string& account);
    static std::string GetFloorByAccountUUID(const std::string& account_uuid);    
    static int IsEnableIntercom(const std::string& account_uuid);
    static int GetAccountListByPhoneList(const std::vector<std::string>& phone_list, OfficeAccountList& account_list);
    static int GetNewOfficeAllPersonnelList(const std::string& project_uuid, OfficeAccountList& account_list);
    static int GetNewOfficeAllAdminAppList(const std::string& project_uuid, OfficeAccountList& account_list);
    static std::string GetLiftFloorNum(const OfficeAccount &account);
    static int UpdateVersionByUUID(const std::string &uuid);
    static int UpdateVersionByGroupUUID(const AkcsStringSet &ag_list);
    static int UpdateVersionByProjectUUID(const std::string &uuid);
    static int GetUUIDAccount(const std::string &uuid, OfficeAccount& account);
    static int GetAllAccountCnfMap(const std::string &project_uuid, OfficeAccountCnfMap &account_cnf_map);
	static int GetAccountByUUID(const std::string& uuid, std::string& account);
    static int GetNodeUidListByNode(const std::string &node, std::vector<std::string>& uid_list);
    static std::string GetNodeTimeZoneStr(const std::string& node);
    static int GetOfficePmApplistByMngID(const std::string office_uuid, ResidentPerAccountList& account_list);

private:
    static void GetAccountFromSql(OfficeAccount &account, CRldbQuery& query);
    static void GetStructAccountFromSql(OfficeAccount &account, CRldbQuery& query);
    static int InitAccountByUid(const std::string& uid, OfficeAccount &account);
    static int InitAccountByEmail(const std::string& email, OfficeAccount &account);
    static int InitAccountByUser(const std::string& user, OfficeAccount &account);

    static int InitAccountByUuid(const std::string& uuid, OfficeAccount &account);
    
    

    
};

}
#endif
