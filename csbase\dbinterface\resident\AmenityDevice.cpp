#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "AmenityDevice.h"
#include "dbinterface/resident/ResidentDevices.h"

namespace dbinterface {

static const std::string amenity_device_info_sec = " ID,UUID,AmenityUUID,DeviceUUID,ProjectUUID,IsRemoveAccess ";

void AmenityDevice::GetAmenityDeviceFromSql(AmenityDeviceInfo& amenity_device_info, CRldbQuery& query)
{
    amenity_device_info.id = ATOI(query.GetRowData(0));
    Snprintf(amenity_device_info.uuid, sizeof(amenity_device_info.uuid), query.GetRowData(1));
    Snprintf(amenity_device_info.amenity_uuid, sizeof(amenity_device_info.amenity_uuid), query.GetRowData(2));
    Snprintf(amenity_device_info.device_uuid, sizeof(amenity_device_info.device_uuid), query.GetRowData(3));
    Snprintf(amenity_device_info.project_uuid, sizeof(amenity_device_info.project_uuid), query.GetRowData(4));
    amenity_device_info.is_remove_access = ATOI(query.GetRowData(5));
    return;
}

int AmenityDevice::GetAmenityDeviceByUUID(const std::string& uuid, AmenityDeviceInfo& amenity_device_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << amenity_device_info_sec << " from AmenityDevice where UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetAmenityDeviceFromSql(amenity_device_info, query);
    }
    else
    {
        AK_LOG_WARN << "get AmenityDeviceInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}

int AmenityDevice::GetAmenityDeviceByAmenityUUID(const std::string& amenity_uuid, AmenityDeviceInfo& amenity_device_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << amenity_device_info_sec << " from AmenityDevice where AmenityUUID = '" << amenity_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetAmenityDeviceFromSql(amenity_device_info, query);
    }
    else
    {
        AK_LOG_WARN << "get AmenityDeviceInfo by AmenityUUID failed, AmenityUUID = " << amenity_uuid;
        return -1;
    }
    return 0;
}

int AmenityDevice::GetAmenityDeviceByDeviceUUID(const std::string& device_uuid, AmenityDeviceInfo& amenity_device_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << amenity_device_info_sec << " from AmenityDevice where DeviceUUID = '" << device_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetAmenityDeviceFromSql(amenity_device_info, query);
    }
    else
    {
        AK_LOG_WARN << "get AmenityDeviceInfo by DeviceUUID failed, DeviceUUID = " << device_uuid;
        return -1;
    }
    return 0;
}

bool AmenityDevice::IsAmenityDevice(const std::string& dev_uuid)
{
    std::stringstream stream_sql;
    stream_sql << "select 1 from AmenityDevice where DeviceUUID = '" << dev_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        return true;
    }
    return false;
}

bool AmenityDevice::IsRemoveDefaultAccess(const std::string& device_uuid)
{
    std::stringstream stream_sql;
    stream_sql << "select IsRemoveAccess from AmenityDevice where DeviceUUID = '" << device_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        int is_remove = ATOI(query.GetRowData(0));
        return is_remove ? true : false;
    }

    return false;
}

int AmenityDevice::GetAmenityDeviceByDeviceMac(const std::string& mac, AmenityDeviceInfo& amenity_device_info)
{
    //根据设备找到对应的公共设施
    ResidentDev dev;
    if (0 != dbinterface::ResidentDevices::GetMacDev(mac, dev))
    {
        AK_LOG_WARN << "dev not found. mac=" << mac;
        return -1;
    }

    //一台设备只能被一个设施绑定
    if (0 != GetAmenityDeviceByDeviceUUID(dev.uuid, amenity_device_info))
    {
        AK_LOG_WARN << "device not bind with amenity. device uuid=" << dev.uuid;
        return -1;
    }
    return 0;
}

int AmenityDevice::GetRemoveDefaultAccessByCommunityUUID(const std::string& project_uuid, AmenityDeviceRemoveAccessMacSet &remote_access_map)
{
    std::stringstream stream_sql;
    stream_sql << "select A.IsRemoveAccess, D.Mac from AmenityDevice A left join Devices D on D.UUID = A.DeviceUUID where A.ProjectUUID='" << project_uuid << "' and A.IsRemoveAccess=1";
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        int is_remove = ATOI(query.GetRowData(0));
        std::string mac = query.GetRowData(1);
        if (is_remove)
        {
            remote_access_map.insert(mac);
        }
    }

    return 0;
}


}