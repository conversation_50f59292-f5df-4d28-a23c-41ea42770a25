#ifndef __DB_AMENITY_DEVICE_H__
#define __DB_AMENITY_DEVICE_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/AmenityReservation.h"

typedef struct AmenityDeviceInfo_T
{
    int id;
    char uuid[36];
    char amenity_uuid[36];
    char device_uuid[36];
    char project_uuid[36];
    int is_remove_access;
    AmenityDeviceInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} AmenityDeviceInfo;

//只存移除的
using AmenityDeviceRemoveAccessMacSet = std::set<std::string>;

namespace dbinterface {

class AmenityDevice
{
public:
    static int GetAmenityDeviceByUUID(const std::string& uuid, AmenityDeviceInfo& amenity_device_info);
    static int GetAmenityDeviceByAmenityUUID(const std::string& amenity_uuid, AmenityDeviceInfo& amenity_device_info);
    static int GetAmenityDeviceByDeviceUUID(const std::string& device_uuid, AmenityDeviceInfo& amenity_device_info);
    static int GetAmenityDeviceByDeviceMac(const std::string& mac, AmenityDeviceInfo& amenity_device_info);
    static bool IsAmenityDevice(const std::string& device_uuid);
    static bool IsRemoveDefaultAccess(const std::string& device_uuid);
    
    static int GetRemoveDefaultAccessByCommunityUUID(const std::string& project_uuid, AmenityDeviceRemoveAccessMacSet  &remote_access_map);
private:
    AmenityDevice() = delete;
    ~AmenityDevice() = delete;
    static void GetAmenityDeviceFromSql(AmenityDeviceInfo& amenity_device_info, CRldbQuery& query);
};

}


#endif