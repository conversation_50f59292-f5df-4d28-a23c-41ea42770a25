#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "CommunityDeviceContact.h"

namespace dbinterface {

static const std::string community_device_contact_info_sec = " UUID,<PERSON>ceUUID,CommunityUUID,CommunityUnitUUID,AptUUID,PersonalAccountUUID,IndoorDeviceUUID,Type ";

void CommunityDeviceContact::GetCommunityDeviceContactFromSql(CommunityDeviceContactInfo& community_device_contact_info, CRldbQuery& query)
{
    Snprintf(community_device_contact_info.uuid, sizeof(community_device_contact_info.uuid), query.GetRowData(0));
    Snprintf(community_device_contact_info.device_uuid, sizeof(community_device_contact_info.device_uuid), query.GetRowData(1));
    Snprintf(community_device_contact_info.community_uuid, sizeof(community_device_contact_info.community_uuid), query.GetRowData(2));
    Snprintf(community_device_contact_info.community_unit_uuid, sizeof(community_device_contact_info.community_unit_uuid), query.GetRowData(3));
    Snprintf(community_device_contact_info.apt_uuid, sizeof(community_device_contact_info.apt_uuid), query.GetRowData(4));
    Snprintf(community_device_contact_info.personal_account_uuid, sizeof(community_device_contact_info.personal_account_uuid), query.GetRowData(5));
    Snprintf(community_device_contact_info.indoor_device_uuid, sizeof(community_device_contact_info.indoor_device_uuid), query.GetRowData(6));
    community_device_contact_info.type = ATOI(query.GetRowData(7));
    return;
}

int CommunityDeviceContact::GetCommunityDeviceContactByUUID(const std::string& uuid, CommunityDeviceContactInfo& community_device_contact_info)
{
    std::stringstream stream_sql;
    stream_sql << "select " << community_device_contact_info_sec << " from CommunityDeviceContact where UUID = '" << uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    if (query.MoveToNextRow())
    {
        GetCommunityDeviceContactFromSql(community_device_contact_info, query);
    }
    else
    {
        AK_LOG_WARN << "get CommunityDeviceContactInfo by UUID failed, UUID = " << uuid;
        return -1;
    }
    return 0;
}

int CommunityDeviceContact::GetCommunityDeviceContactListByDeviceUUID(const std::string& device_uuid, CommunityDeviceContactInfoList& community_device_contact_info_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << community_device_contact_info_sec << " from CommunityDeviceContact where DeviceUUID = '" << device_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn, -1)

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        CommunityDeviceContactInfo community_device_contact_info;
        GetCommunityDeviceContactFromSql(community_device_contact_info, query);
        community_device_contact_info_list.push_back(community_device_contact_info);
    }

    return 0;
}

}
