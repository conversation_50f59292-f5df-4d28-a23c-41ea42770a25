#ifndef __DB_COMMUNITY_DEVICE_CONTACT_H__
#define __DB_COMMUNITY_DEVICE_CONTACT_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct CommunityDeviceContactInfo_T
{
    char uuid[36];
    char device_uuid[36];
    char community_uuid[36];
    char community_unit_uuid[36];
    char apt_uuid[36];
    char personal_account_uuid[36];
    char indoor_device_uuid[36];
    int type;
    CommunityDeviceContactInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} CommunityDeviceContactInfo;

typedef std::vector<CommunityDeviceContactInfo> CommunityDeviceContactInfoList;

namespace dbinterface {

class CommunityDeviceContact
{
public:
    
    enum CONTACT_TYPE
    {
        BUILDING = 1,
        APT,
        PERSONAL,
        INDOOR,
    };
    
    static int GetCommunityDeviceContactByUUID(const std::string& uuid, CommunityDeviceContactInfo& community_device_contact_info);
    static int GetCommunityDeviceContactListByDeviceUUID(const std::string& device_uuid, CommunityDeviceContactInfoList& community_device_contact_info_list);

private:
    CommunityDeviceContact() = delete;
    ~CommunityDeviceContact() = delete;
    static void GetCommunityDeviceContactFromSql(CommunityDeviceContactInfo& community_device_contact_info, CRldbQuery& query);
};

}
#endif
