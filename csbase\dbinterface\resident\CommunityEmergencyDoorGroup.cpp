#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "CommunityEmergencyDoorGroup.h"
#include "util_relay.h"
#include "ConnectionManager.h"
#include "AkcsCommonDef.h"

namespace dbinterface {

static const std::string community_emergency_door_group_info_sec = " UUID,AccountUUID,DevicesUUID,RelayIndex,RelayType ";

void CommunityEmergencyDoorGroup::GetCommunityEmergencyDoorGroupFromSql(CommunityEmergencyDoorGroupInfo& community_emergency_door_group_info, CRldbQuery& query)
{
    Snprintf(community_emergency_door_group_info.uuid, sizeof(community_emergency_door_group_info.uuid), query.GetRowData(0));
    Snprintf(community_emergency_door_group_info.account_uuid, sizeof(community_emergency_door_group_info.account_uuid), query.GetRowData(1));
    Snprintf(community_emergency_door_group_info.devices_uuid, sizeof(community_emergency_door_group_info.devices_uuid), query.GetRowData(2));
    community_emergency_door_group_info.relay_index = ATOI(query.GetRowData(3));
    community_emergency_door_group_info.relay_type = (DoorRelayType)ATOI(query.GetRowData(4));
    return;
}

void CommunityEmergencyDoorGroup::GetCommunityEmergencyDoorGroupListByAccountUUID(const std::string& account_uuid, CommunityEmergencyDoorGroupInfoList& community_emergency_door_group_info_list)
{
    std::stringstream stream_sql;
    stream_sql << "select EG.RelayIndex, EG.RelayType, EG.DevicesUUID, D.Relay, D.SecurityRelay from CommunityEmergencyDoorGroup EG left join "
                <<  " Devices D on EG.DevicesUUID = D.UUID where EG.AccountUUID = '" << account_uuid << "'";
    GET_DB_CONN_ERR_RETURN(db_conn,);

    CRldbQuery query(db_conn.get());
    query.Query(stream_sql.str());
    while (query.MoveToNextRow())
    {
        CommunityEmergencyDoorGroupInfo community_emergency_door_group_info;
        
        community_emergency_door_group_info.relay_index = ATOI(query.GetRowData(0));
        community_emergency_door_group_info.relay_type = (DoorRelayType)ATOI(query.GetRowData(1));
        Snprintf(community_emergency_door_group_info.devices_uuid, sizeof(community_emergency_door_group_info.devices_uuid), query.GetRowData(2));
        std::string relay(query.GetRowData(3));
        std::string security_relay(query.GetRowData(4));

        std::string target_relay = community_emergency_door_group_info.relay_type == DoorRelayType::RELAY ? relay : security_relay;

        RELAY_INFO relay_info;
        if (0 != GetRelayItem(target_relay, community_emergency_door_group_info.relay_index, relay_info))
        {
            AK_LOG_WARN << "GetRelayItem failed. relay=" << target_relay << ", relay_index=" << community_emergency_door_group_info.relay_index;
            continue;
        }

        Snprintf(community_emergency_door_group_info.relay_name, sizeof(community_emergency_door_group_info.relay_name), relay_info.name);
        community_emergency_door_group_info.relay_enable = relay_info.enable;

        community_emergency_door_group_info_list.push_back(community_emergency_door_group_info);
    }
    return;
}


}
