#ifndef __DB_RESIDENT_EMERGENCY_DOOR_GROUP_H__
#define __DB_RESIDENT_EMERGENCY_DOOR_GROUP_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct CommunityEmergencyDoorGroupInfo_T
{
    char uuid[36];
    char account_uuid[36];
    char devices_uuid[36];
    int relay_index;
    DoorRelayType relay_type;
    char relay_name[64];
    int relay_enable;
    CommunityEmergencyDoorGroupInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} CommunityEmergencyDoorGroupInfo;

using CommunityEmergencyDoorGroupInfoList = std::vector<CommunityEmergencyDoorGroupInfo>;

namespace dbinterface {

class CommunityEmergencyDoorGroup
{
public:
    static void GetCommunityEmergencyDoorGroupListByAccountUUID(const std::string& account_uuid, CommunityEmergencyDoorGroupInfoList& community_emergency_door_group_info_list);

private:
    CommunityEmergencyDoorGroup() = delete;
    ~CommunityEmergencyDoorGroup() = delete;
    static void GetCommunityEmergencyDoorGroupFromSql(CommunityEmergencyDoorGroupInfo& community_emergency_door_group_info, CRldbQuery& query);
};

}
#endif
