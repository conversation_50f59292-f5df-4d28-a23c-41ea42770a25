#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "ExtraDevice.h"

namespace dbinterface 
{

static const std::string extra_device_info_sec = " UUID,IndoorMonitorConfigUUID,<PERSON>ceAddress,<PERSON>ceIndex,Switch ";

void ExtraDevice::GetExtraDeviceFromSql(ExtraDeviceInfo& extra_device_info, CRldbQuery& query)
{
    Snprintf(extra_device_info.uuid, sizeof(extra_device_info.uuid), query.GetRowData(0));
    Snprintf(extra_device_info.indoor_monitor_config_uuid, sizeof(extra_device_info.indoor_monitor_config_uuid), query.GetRowData(1));
    Snprintf(extra_device_info.device_address, sizeof(extra_device_info.device_address), query.GetRowData(2));
    extra_device_info.device_index = ATOI(query.GetRowData(3));
    extra_device_info.enable_switch = ATOI(query.GetRowData(4));
    return;
}


int ExtraDevice::GetExtraDevicesByIndoorConfigUUID(const std::string& indoor_config_uuid, ExtraDeviceInfoList& extra_devices)
{
    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    CRldb* conn = db_conn.get();

    std::stringstream str_sql;
    str_sql << "SELECT" << extra_device_info_sec << "FROM ExtraDevice WHERE IndoorMonitorConfigUUID = '" << indoor_config_uuid << "'";
    CRldbQuery query(conn);
    query.Query(str_sql.str());
    while (query.MoveToNextRow())
    {
        ExtraDeviceInfo extra_device_info;
        GetExtraDeviceFromSql(extra_device_info, query);
        extra_devices.push_back(extra_device_info);
    }

    return 0;
}

int ExtraDevice::GetExtraDevicesUUIDByIndoorConfigUUID(const std::string& indoor_config_uuid, std::vector<std::string>& extra_devices_uuid)
{
    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    CRldb* conn = db_conn.get();

    std::stringstream str_sql;
    str_sql << "SELECT UUID FROM ExtraDevice WHERE IndoorMonitorConfigUUID = '" << indoor_config_uuid << "'";
    
    CRldbQuery query(conn);
    query.Query(str_sql.str());
    while (query.MoveToNextRow())
    {
        std::string uuid = query.GetRowData(0);
        extra_devices_uuid.push_back(uuid);
    }

    return 0;
}

}