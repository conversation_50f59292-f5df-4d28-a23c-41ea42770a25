#ifndef __DB_EXTRA_DEVICE_H__
#define __DB_EXTRA_DEVICE_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

typedef struct ExtraDeviceInfo_T
{
    char uuid[36];
    char indoor_monitor_config_uuid[36];
    char device_address[128];
    int device_index;
    int enable_switch;
    ExtraDeviceInfo_T() 
    {
        memset(this, 0, sizeof(*this));
    }
} ExtraDeviceInfo;

using ExtraDeviceInfoList = std::vector<ExtraDeviceInfo>;
namespace dbinterface 
{

class ExtraDevice
{
public:
    static int GetExtraDevicesByIndoorConfigUUID(const std::string& indoor_config_uuid, ExtraDeviceInfoList& extra_devices);
    static int GetExtraDevicesUUIDByIndoorConfigUUID(const std::string& indoor_config_uuid, std::vector<std::string>& extra_devices_uuid);

private:
    ExtraDevice() = delete;
    ~ExtraDevice() = delete;
    static void GetExtraDeviceFromSql(ExtraDeviceInfo& extra_device_info, CRldbQuery& query);
};

}
#endif