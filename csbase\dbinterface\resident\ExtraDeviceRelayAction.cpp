#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "ExtraDeviceRelayAction.h"
#include "ExtraDevice.h"
#include "ExtraDeviceRelayList.h"

namespace dbinterface 
{

// 继电器设备常量定义
static const int EXTRA_DEVICE1_RELAY_START_INDEX = 1;
static const int EXTRA_DEVICE2_RELAY_START_INDEX = 17;
static const int EXTRA_DEVICE3_RELAY_START_INDEX = 33;
static const int EXTRA_DEVICE_RELAY_NUMBER = 16; //每块扩展板继电器数量
static const int K_RELAY_COUNT_PER_DEVICE = 8;  // 每个扩展板继电器的K类型继电器数量(K1-K8)


// 扩展查询字段，包含ExtraDeviceRelayList的ID
static const std::string extra_device_relay_action_info_sec = " a.UUID,a.ExtraDeviceRelayListUUID,a.ActionType,a.Input,a.Output,a.Hold<PERSON>y,a.ConnectType,a.<PERSON>,a.Status,b.ID as relay_id ";



void ExtraDeviceRelayAction::GetExtraDeviceRelayActionFromSql(ExtraDeviceRelayActionInfo& extra_device_relay_action_info, CRldbQuery& query)
{
    Snprintf(extra_device_relay_action_info.uuid, sizeof(extra_device_relay_action_info.uuid), query.GetRowData(0));
    Snprintf(extra_device_relay_action_info.extra_device_relay_list_uuid, sizeof(extra_device_relay_action_info.extra_device_relay_list_uuid), query.GetRowData(1));
    extra_device_relay_action_info.action_type = ATOI(query.GetRowData(2));
    Snprintf(extra_device_relay_action_info.input, sizeof(extra_device_relay_action_info.input), query.GetRowData(3));
    Snprintf(extra_device_relay_action_info.output, sizeof(extra_device_relay_action_info.output), query.GetRowData(4));
    Snprintf(extra_device_relay_action_info.hold_delay, sizeof(extra_device_relay_action_info.hold_delay), query.GetRowData(5));
    extra_device_relay_action_info.connect_type = ATOI(query.GetRowData(6));
    extra_device_relay_action_info.trigger_model = ATOI(query.GetRowData(7));
    extra_device_relay_action_info.status = ATOI(query.GetRowData(8));
    extra_device_relay_action_info.relay_id = ATOI(query.GetRowData(9));
    return;
}


int ExtraDeviceRelayAction::SetDevicesExRelayStatus(const std::string& indoor_monitor_config_uuid, uint64_t relay_status)
{

    ExtraDeviceInfoList extra_devices;
    // 根据室内机配置UUID获取关联的extra_devices信息
    if (ExtraDevice::GetExtraDevicesByIndoorConfigUUID(indoor_monitor_config_uuid, extra_devices) != 0) 
    {
        AK_LOG_WARN << "Failed to get extra devices for indoor config: " << indoor_monitor_config_uuid;
        return -1;
    }
    
    std::vector<std::string> extra_devices_uuids;
    for (const auto& extra_device : extra_devices) 
    {
        extra_devices_uuids.push_back(extra_device.uuid);
    }
    
    // 根据外接设备UUID获取relays
    ExtraDeviceRelayListInfoList relay_list;
    ExtraDeviceRelayListInfoMap relay_list_info_map;
    if (ExtraDeviceRelayList::GetRelayListByExtraDevices(extra_devices_uuids, relay_list, relay_list_info_map) != 0) 
    {
        AK_LOG_WARN << "Failed to get relay list for extra devices";
        return -1;
    }

    if (relay_list.empty()) 
    {
        AK_LOG_INFO << "No relay UUIDs found for indoor config: " << indoor_monitor_config_uuid;
        return 0;
    }

    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    CRldb* conn = db_conn.get();
    // 先将所有relay状态重置为0
    if (ResetAllRelayStatus(conn, relay_list) != 0) 
    {
        return -1;
    }
    
    // 如果继电器状态为0，表示所有继电器都关闭，无需再更新
    if (relay_status == 0) 
    {
        AK_LOG_INFO << "Relay status is 0, no need to update";
        return 0;
    }
   
    // 更新各个设备的继电器状态
    if (UpdateDevicesRelayStatus(conn, extra_devices, relay_list_info_map, relay_status) != 0) 
    {
        return -1;
    }
    
    return 0;
}

/**
 * 根据扩展板索引和继电器状态位构建该扩展板的继电器输出列表
 * @param extra_device_index 扩展板索引(1、2、3)
 * @param relay_status 继电器状态位
 * @return 生成的继电器输出列表，如 {"K1", "K3", "OT2"}
 */
std::vector<std::string> ExtraDeviceRelayAction::BuildDeviceRelayOutputList(int extra_device_index, uint64_t relay_status)
{
    std::vector<std::string> result;
    
    // 根据设备索引确定位偏移量
    int start_bit = 0;
    if (extra_device_index == 1) 
    {
        start_bit = EXTRA_DEVICE1_RELAY_START_INDEX - 1; // 位0对应K1
    } 
    else if (extra_device_index == 2) 
    {
        start_bit = EXTRA_DEVICE2_RELAY_START_INDEX - 1; // 位16对应K1
    } 
    else if (extra_device_index == 3) 
    {
        start_bit = EXTRA_DEVICE3_RELAY_START_INDEX - 1; // 位32对应K1
    } 
    else 
    {
        return result;
    }
    
    // 检查该设备的16位继电器状态
    for (int i = 0; i < EXTRA_DEVICE_RELAY_NUMBER; ++i) 
    {
        int bit_position = start_bit + i;
        if (bit_position >= 64) break; // 防止越界
        
        if (relay_status & (1ULL << bit_position)) 
        {
            // 前8位是K1-K8，后8位是OT1-OT8
            if (i < K_RELAY_COUNT_PER_DEVICE) 
            {
                result.push_back("K" + std::to_string(i + 1));
            } 
            else 
            {
                result.push_back("OT" + std::to_string(i - K_RELAY_COUNT_PER_DEVICE + 1));
            }
        }
    }
    
    return result;
}


int ExtraDeviceRelayAction::GetRelayActionsByRelayList(const std::vector<std::string>& relay_uuids, ExtraDeviceRelayActionInfoMap& relay_uuid_to_actions_map)
{
    if (relay_uuids.empty()) 
    {
        return 0;
    }

    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    CRldb* conn = db_conn.get();

    std::string uuid_in_clause = ListToSeparatedFormatString(relay_uuids);

    std::stringstream str_sql;
    // 使用JOIN查询获取ExtraDeviceRelayList的ID
    // 首先按relay_id排序，确保窗帘/升降门的顺序正确

    str_sql << "SELECT" << extra_device_relay_action_info_sec 
           << "FROM ExtraDeviceRelayAction a "
           << "JOIN ExtraDeviceRelayList b ON a.ExtraDeviceRelayListUUID = b.UUID "
           << "WHERE a.ExtraDeviceRelayListUUID IN (" << uuid_in_clause << ") "
           << "ORDER BY b.ID";
    CRldbQuery query(conn);
    query.Query(str_sql.str());
    while (query.MoveToNextRow())
    {
        ExtraDeviceRelayActionInfo relay_action_info;
        GetExtraDeviceRelayActionFromSql(relay_action_info, query);
        
        // 直接添加到multimap，以relay_uuid为key
        relay_uuid_to_actions_map.emplace(relay_action_info.extra_device_relay_list_uuid, relay_action_info);
    }

    return 0;
}

int ExtraDeviceRelayAction::ResetAllRelayStatus(CRldb* conn, const ExtraDeviceRelayListInfoList& relay_list)
{
    // 获取所有relay的UUID集合
    std::vector<std::string> all_relay_uuids;
    for (const auto& relay : relay_list) 
    {
        all_relay_uuids.push_back(relay.uuid);
    }   
    
    // 构建重置SQL
    std::stringstream reset_sql_stream;
    reset_sql_stream << "UPDATE ExtraDeviceRelayAction SET Status = 0 "
                     << "WHERE ExtraDeviceRelayListUUID IN (" << ListToSeparatedFormatString(all_relay_uuids) << ")";
    std::string reset_sql = reset_sql_stream.str();
    
    if (conn->Execute(reset_sql) == -1) 
    {
        AK_LOG_WARN << "Reset SQL execute failed. SQL is: " << reset_sql;
        return -1;
    }
    
    return 0;
}

int ExtraDeviceRelayAction::UpdateDevicesRelayStatus(CRldb* conn, const ExtraDeviceInfoList& extra_devices, 
                                                     const ExtraDeviceRelayListInfoMap& relay_list_info_map, uint64_t relay_status)
{
    // 针对每个extra_device，更新其对应的继电器状态为1
    for (const auto& extra_device : extra_devices) 
    {
        int extra_device_index = extra_device.device_index;
        std::string extra_device_uuid = extra_device.uuid;

        std::vector<std::string> extra_device_relay_uuids;
        //找到当前这块扩展板的所有relay uuid
        auto range = relay_list_info_map.equal_range(extra_device_uuid);
        for (auto itr = range.first; itr != range.second; ++itr) 
        {
            extra_device_relay_uuids.push_back(itr->second.uuid);
        }
        //确认当前这块扩展板需要更新的relay output
        const std::vector<std::string>& relay_outputs_vec = BuildDeviceRelayOutputList(extra_device_index, relay_status);
        if (relay_outputs_vec.empty() || extra_device_relay_uuids.empty()) 
        {
            continue;
        }
        
        // 1. 限制只更新属于当前扩展板的relay(通过多个ExtraDeviceRelayListUUID)
        // 2. 并根据Output值匹配该扩展板的特定继电器(K1-K8或OT1-OT8);Output值在同一块扩展板中是唯一的
        std::stringstream update_sql_stream;
        update_sql_stream << "UPDATE ExtraDeviceRelayAction SET Status = 1 "
                          << "WHERE ExtraDeviceRelayListUUID IN (" << ListToSeparatedFormatString(extra_device_relay_uuids) << ")"
                          << " AND Output IN (" << ListToSeparatedFormatString(relay_outputs_vec) << ") ";
        std::string update_sql = update_sql_stream.str();
        
        if (conn->Execute(update_sql) == -1) 
        {
            AK_LOG_WARN << "Update SQL execute failed for extra device " << extra_device_index << ". SQL is: " << update_sql;
            return -1;
        }
    }
    
    return 0;
}

}