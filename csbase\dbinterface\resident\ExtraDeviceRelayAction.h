#ifndef __DB_EXTRA_DEVICE_RELAY_ACTION_H__
#define __DB_EXTRA_DEVICE_RELAY_ACTION_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include <map>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/resident/ExtraDevice.h"
#include "dbinterface/resident/ExtraDeviceRelayList.h"

typedef struct ExtraDeviceRelayActionInfo_T
{
    char uuid[36];
    char extra_device_relay_list_uuid[36];
    int action_type;
    char input[5];
    char output[5];
    char hold_delay[10];
    int connect_type;
    int trigger_model;
    int status;
    int relay_id;  // 用于记录关联的ExtraDeviceRelayList的ID
    ExtraDeviceRelayActionInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} ExtraDeviceRelayActionInfo;

using ExtraDeviceRelayActionInfoMap = std::multimap<std::string, ExtraDeviceRelayActionInfo>;

namespace dbinterface {

// Relay功能类型枚举
enum RelayWebFunctionType {
    RELAY_FUNCTION_LIGHT = 1,        // 灯光
    RELAY_FUNCTION_SHUTTER = 2,      // 窗帘
    RELAY_FUNCTION_SHADE = 3,         // 升降门
    RELAY_FUNCTION_DOOR = 4,         // 门禁
    RELAY_FUNCTION_OTHER = 5,         // 其他
    RELAY_FUNCTION_SHUTTER_UP = 6,
    RELAY_FUNCTION_SHUTTER_DOWN = 7,
    RELAY_FUNCTION_SHUTTER_PAUSING = 8
};

// 操作类型枚举
enum ActionType {
    ACTION_TYPE_ON_OFF = 0,            // ON/OFF
    ACTION_TYPE_UP = 1,              
    ACTION_TYPE_DOWN = 2,           
};

enum FunctionDeviceValues {
    FUNCTION_DEVICES_VALUE_LIGHT = 1,            // 灯光
    FUNCTION_DEVICES_VALUE_SHUTTER_UP = 2,
    FUNCTION_DEVICES_VALUE_SHUTTER_DOWN = 3,
    FUNCTION_DEVICES_VALUE_SHUTTER_PAUSING = 4,
    FUNCTION_DEVICES_VALUE_DOOR = 5,             // 门禁
    FUNCTION_DEVICES_VALUE_OTHER = 6,            // 其他
    FUNCTION_DEVICES_VALUE_SHUTTER1_UP = 7,      // 窗帘1-上升
    FUNCTION_DEVICES_VALUE_SHUTTER1_DOWN = 8,    // 窗帘1-下降
    FUNCTION_DEVICES_VALUE_SHUTTER2_UP = 9,      // 窗帘2-上升
    FUNCTION_DEVICES_VALUE_SHUTTER2_DOWN = 10,   // 窗帘2-下降
    FUNCTION_DEVICES_VALUE_SHUTTER3_UP = 11,     // 窗帘3-上升
    FUNCTION_DEVICES_VALUE_SHUTTER3_DOWN = 12,   // 窗帘3-下降
    FUNCTION_DEVICES_VALUE_SHUTTER4_UP = 13,     // 窗帘4-上升
    FUNCTION_DEVICES_VALUE_SHUTTER4_DOWN = 14,   // 窗帘4-下降
    FUNCTION_DEVICES_VALUE_SHADE1_OPEN = 15,     // 升降门1-开启
    FUNCTION_DEVICES_VALUE_SHADE1_CLOSE = 16,    // 升降门1-关闭
    FUNCTION_DEVICES_VALUE_SHADE2_OPEN = 17,     // 升降门2-开启
    FUNCTION_DEVICES_VALUE_SHADE2_CLOSE = 18,    // 升降门2-关闭
    FUNCTION_DEVICES_VALUE_SHADE3_OPEN = 19,     // 升降门3-开启
    FUNCTION_DEVICES_VALUE_SHADE3_CLOSE = 20,    // 升降门3-关闭
    FUNCTION_DEVICES_VALUE_SHADE4_OPEN = 21,     // 升降门4-开启
    FUNCTION_DEVICES_VALUE_SHADE4_CLOSE = 22,    // 升降门4-关闭
};

// 每个设备支持的最大窗帘/升降门数量
static const int MAX_SHUTTER_PER_DEVICE = 4;

class ExtraDeviceRelayAction
{
public:
    static int SetDevicesExRelayStatus(const std::string& indoor_monitor_config_uuid, uint64_t relay_status);
    static std::vector<std::string> BuildDeviceRelayOutputList(int extra_device_index, uint64_t relay_status);
    
    static int GetRelayActionsByRelayList(const std::vector<std::string>& relay_uuids, 
                                    ExtraDeviceRelayActionInfoMap& relay_uuid_to_actions_map);

private:
    ExtraDeviceRelayAction() = delete;
    ~ExtraDeviceRelayAction() = delete;
    static void GetExtraDeviceRelayActionFromSql(ExtraDeviceRelayActionInfo& extra_device_relay_action_info, CRldbQuery& query);
    static int ResetAllRelayStatus(CRldb* conn, const ExtraDeviceRelayListInfoList& relay_list);
    static int UpdateDevicesRelayStatus(CRldb* conn, const ExtraDeviceInfoList& extra_devices, 
                                       const ExtraDeviceRelayListInfoMap& relay_list_info_map, uint64_t relay_status);
};

}
#endif