#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "ExtraDeviceRelayList.h"
#include "ExtraDevice.h"

namespace dbinterface 
{

static const std::string extra_device_relay_list_info_sec = " UUID,ExtraDeviceUUID,Name,Function,Switch,ID ";

void ExtraDeviceRelayList::GetExtraDeviceRelayListFromSql(ExtraDeviceRelayListInfo& extra_device_relay_list_info, CRldbQuery& query)
{
    Snprintf(extra_device_relay_list_info.uuid, sizeof(extra_device_relay_list_info.uuid), query.GetRowData(0));
    Snprintf(extra_device_relay_list_info.extra_device_uuid, sizeof(extra_device_relay_list_info.extra_device_uuid), query.GetRowData(1));
    Snprintf(extra_device_relay_list_info.name, sizeof(extra_device_relay_list_info.name), query.GetRowData(2));
    extra_device_relay_list_info.function = ATOI(query.GetRowData(3));
    extra_device_relay_list_info.enable_switch = ATOI(query.GetRowData(4));
    extra_device_relay_list_info.relay_id = ATOI(query.GetRowData(5));
    return;
}

int ExtraDeviceRelayList::GetRelayListByExtraDevice(const ExtraDeviceInfo& extra_device, 
                                                   std::vector<std::string>& relay_uuids,
                                                   ExtraDeviceRelayListInfoList& relay_list)
{
    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    CRldb* conn = db_conn.get();

    std::stringstream str_sql;
    // 按照ID排序查询，确保窗帘/升降门的function值计算顺序一致
    str_sql << "SELECT" << extra_device_relay_list_info_sec << "FROM ExtraDeviceRelayList WHERE ExtraDeviceUUID = '" 
           << extra_device.uuid << "' ORDER BY ID";
    CRldbQuery query(conn);
    query.Query(str_sql.str());
    while (query.MoveToNextRow())
    {
        ExtraDeviceRelayListInfo relay_info;
        GetExtraDeviceRelayListFromSql(relay_info, query);
        // 添加UUID到列表
        relay_uuids.push_back(relay_info.uuid);
        // 直接添加relay信息到relay_list
        relay_list.push_back(relay_info);
    }

    return 0;
}

int ExtraDeviceRelayList::GetRelayListByExtraDevices(const std::vector<std::string>& extra_devices_uuids, 
                                                     ExtraDeviceRelayListInfoList& relay_list, 
                                                     ExtraDeviceRelayListInfoMap& relay_list_map)
{
    if (extra_devices_uuids.empty()) 
    {
        return 0;
    }

    GET_DB_CONN_ERR_RETURN(db_conn, -1);
    CRldb* conn = db_conn.get();

    std::stringstream str_sql;
    str_sql << "SELECT" << extra_device_relay_list_info_sec << "FROM ExtraDeviceRelayList WHERE ExtraDeviceUUID IN (" << ListToSeparatedFormatString(extra_devices_uuids) << ")";
    
    CRldbQuery query(conn);
    query.Query(str_sql.str());
    while (query.MoveToNextRow())
    {
        ExtraDeviceRelayListInfo relay_info;
        GetExtraDeviceRelayListFromSql(relay_info, query);
        relay_list.push_back(relay_info);
        // 同时添加到映射中
        relay_list_map.insert({relay_info.extra_device_uuid, relay_info});
    }

    return 0;
}

}