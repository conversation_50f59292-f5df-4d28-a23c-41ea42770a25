#ifndef __DB_EXTRA_DEVICE_RELAY_LIST_H__
#define __DB_EXTRA_DEVICE_RELAY_LIST_H__

#include <vector>
#include <string>
#include <memory>
#include <sstream>
#include <map>
#include "BasicDefine.h"
#include "ConnectionManager.h"
#include "dbinterface/InterfaceComm.h"

// 前向声明
struct ExtraDeviceInfo_T;
typedef struct ExtraDeviceInfo_T ExtraDeviceInfo;

typedef struct ExtraDeviceRelayListInfo_T
{
    char uuid[36];
    char extra_device_uuid[36];
    char name[64];
    int function;
    int enable_switch;
    int relay_id;
    ExtraDeviceRelayListInfo_T() {
        memset(this, 0, sizeof(*this));
    }
} ExtraDeviceRelayListInfo;
using ExtraDeviceRelayListInfoList = std::vector<ExtraDeviceRelayListInfo>;
//extra_device_uuid 到 relay info 的映射
typedef std::multimap<std::string/*extra_device_uuid*/, ExtraDeviceRelayListInfo> ExtraDeviceRelayListInfoMap;



namespace dbinterface {

class ExtraDeviceRelayList
{
public:
    static int GetRelayListByExtraDevice(const ExtraDeviceInfo& extra_device, 
                                       std::vector<std::string>& relay_uuids,
                                       ExtraDeviceRelayListInfoList& relay_list);
    
    static int GetRelayListByExtraDevices(const std::vector<std::string>& extra_devices_uuids, 
                                         ExtraDeviceRelayListInfoList& relay_list, 
                                         ExtraDeviceRelayListInfoMap& relay_list_map);

private:
    ExtraDeviceRelayList() = delete;
    ~ExtraDeviceRelayList() = delete;
    static void GetExtraDeviceRelayListFromSql(ExtraDeviceRelayListInfo& extra_device_relay_list_info, CRldbQuery& query);
};

}
#endif