#include "IndoorMonitorConfig.h"
#include "util.h"
#include "AkLogging.h"
#include "dbinterface/InterfaceComm.h"

namespace dbinterface
{

    static const std::string indoor_monitor_config_sec = "<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>.<PERSON>, <PERSON>.<PERSON>, <PERSON><PERSON>, M.Port, M.<PERSON>, M.Meta";


void IndoorMonitorConfig::GetIndoorMonitorConfigFromSql(IndoorMonitorConfigInfo& indoor_monitor_config, CRldbQuery& query)
{
    Snprintf(indoor_monitor_config.uuid, sizeof(indoor_monitor_config.uuid), query.GetRowData(0));
    indoor_monitor_config.ex_relay_switch = ATOI(query.GetRowData(1));
    indoor_monitor_config.ex_relay_type = ATOI(query.GetRowData(2));
    indoor_monitor_config.ex_relay_mode = ATOI(query.GetRowData(3));
    Snprintf(indoor_monitor_config.ex_relay_ip, sizeof(indoor_monitor_config.ex_relay_ip), query.GetRowData(4));
    indoor_monitor_config.ex_relay_port = ATOI(query.GetRowData(5));
    Snprintf(indoor_monitor_config.dev_uuid, sizeof(indoor_monitor_config.dev_uuid), query.GetRowData(6));
    indoor_monitor_config.meta = ATOI(query.GetRowData(7));
}

int IndoorMonitorConfig::GetIndoorMonitorConfigByDevUUID(const std::string& dev_uuid, IndoorMonitorConfigInfo& indoor_monitor_config)
{
    std::stringstream str_sql;
    str_sql << "select " << indoor_monitor_config_sec << " from IndoorMonitorConfig M"
                << " where DeviceUUID = '" << dev_uuid << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(str_sql.str());
    if (query.MoveToNextRow())
    {
        GetIndoorMonitorConfigFromSql(indoor_monitor_config, query);
    } 
    else
    {
        AK_LOG_WARN << "Get indoor monitor config failed, device uuid:" << dev_uuid;
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;
}

int IndoorMonitorConfig::GetIndoorMonitorConfigByUUID(const std::string& uuid, IndoorMonitorConfigInfo& indoor_monitor_config)
{
    std::stringstream str_sql;
    str_sql << "select " << indoor_monitor_config_sec << " from IndoorMonitorConfig M"
                << " where UUID = '" << uuid << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(str_sql.str());
    if (query.MoveToNextRow())
    {
        GetIndoorMonitorConfigFromSql(indoor_monitor_config, query);
    } 
    else
    {
        AK_LOG_WARN << "Get indoor monitor config failed, uuid:" << uuid;
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;
}

int IndoorMonitorConfig::GetIndoorMonitorConfigUUIDByDevUUID(const std::string& dev_uuid, std::string& indoor_config_uuid)
{
    std::stringstream str_sql;
    str_sql << "select UUID from IndoorMonitorConfig  M"
                << " where DeviceUUID = '" << dev_uuid << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(str_sql.str());
    if (query.MoveToNextRow())
    {
        indoor_config_uuid = query.GetRowData(0);
    } 
    else
    {
        AK_LOG_WARN << "Get indoor monitor config uuid failed, device uuid:" << dev_uuid;
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;
}

int IndoorMonitorConfig::GetIndoorMonitorConfigByProjectID(uint32_t mng_id, IndoorConfigMap &indoor_map)
{
    std::stringstream str_sql;
    str_sql << "select " << indoor_monitor_config_sec << " From IndoorMonitorConfig M left join Devices D on D.UUID=M.DeviceUUID where D.MngAccountID='" << mng_id << "' and D.Type=" << DEVICE_TYPE_INDOOR;
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(str_sql.str());
    while (query.MoveToNextRow())
    {
        IndoorMonitorConfigInfo info;
        GetIndoorMonitorConfigFromSql(info, query);
        indoor_map.insert(std::make_pair(info.dev_uuid, info));
    } 

    ReleaseDBConn(conn);
    return 0;
}


}
