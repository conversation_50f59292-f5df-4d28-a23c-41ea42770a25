#ifndef __INDOOR_MONITOR_CONFIG_H__
#define __INDOOR_MONITOR_CONFIG_H__

#include<cstdint>
#include<string>
#include "RldbQuery.h"
#include "Rldb.h"
#include "AkcsCommonDef.h"

typedef struct INDOOR_MONITOR_CONFIG_INFO_T
{
    char uuid[48];
    uint32_t ex_relay_switch; //外接relay开关
    uint32_t ex_relay_type; //外接relay类型 1：MK485-G2R-8J8C V3.0 2：HF-8000 3:RSAC_C1_R8
    uint32_t ex_relay_mode; //外接relay mode 1:RS485  2:TCP 3:RS485+input(Non-Latching)  4:RS485+input(Latching)'
    char ex_relay_ip[24]; //外接relay tcp ip
    uint32_t ex_relay_port; //外接relay tcp port
    char dev_uuid[64];
    uint32_t meta; //配置数据版本号，0表示未编辑过ExtraDevice模块
    INDOOR_MONITOR_CONFIG_INFO_T()
    {
        memset(this, 0, sizeof(*this));
    }
} IndoorMonitorConfigInfo;

static const std::map<int, int> indoor_monitor_config_relay_mode_map = {
    {MODE_RS485, DEV_MODE_RS485},
    {MODE_ETHERNET, DEV_MODE_ETHERNET},
    {MODE_RS485_INPUT_NOT_LATCHING, DEV_MODE_RS485_INPUT_NOT_LATCHING},
    {MODE_RS485_INPUT_LATCHING, DEV_MODE_RS485_INPUT_LATCHING}
};

static const std::map<int, int> indoor_monitor_config_relay_type_map = {
    {TYPE_MK48, DEV_TYPE_MK48},
    {TYPE_HF, DEV_TYPE_HF},
    {TYPE_RSAC_C1_R8, DEV_TYPE_RSAC_C1_R8},
};

using IndoorConfigMap = std::map<std::string/*dev_uuid*/, IndoorMonitorConfigInfo>;

namespace dbinterface
{

class IndoorMonitorConfig
{
public:
    static int GetIndoorMonitorConfigUUIDByDevUUID(const std::string& dev_uuid, std::string& indoor_config_uuid);
    static int GetIndoorMonitorConfigByDevUUID(const std::string& dev_uuid, IndoorMonitorConfigInfo& indoor_monitor_config);
    static int GetIndoorMonitorConfigByUUID(const std::string& uuid, IndoorMonitorConfigInfo& indoor_monitor_config);

    static int GetIndoorMonitorConfigByProjectID(uint32_t mng_id, IndoorConfigMap &indoor_map);
private:
    static void GetIndoorMonitorConfigFromSql(IndoorMonitorConfigInfo& indoor_monitor_config, CRldbQuery& query);

};

}


#endif //__INDOOR_MONITOR_CONFIG_H__