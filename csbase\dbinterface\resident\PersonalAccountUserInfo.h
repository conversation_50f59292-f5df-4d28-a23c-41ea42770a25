#ifndef __DB_PERSONAL_ACCOUNT_USERINFO_H__
#define __DB_PERSONAL_ACCOUNT_USERINFO_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/DbCommonSt.h"

typedef struct PerAccountUserInfo_T
{
    int id;
    char email[64];
    char mobile_number[32];
    char passwd[64];
    char main_user_account[64]; 
    char last_login_account[64];
    char uuid[64];
    char create_time[32];
    int sip_login;
    // 默认构造函数
    PerAccountUserInfo_T() {
        memset(this, 0, sizeof(*this));
    }
}PerAccountUserInfo;

namespace dbinterface
{

class PersonalAccountUserInfo
{
public:
    PersonalAccountUserInfo();
    ~PersonalAccountUserInfo();
    static int GetPerAccountInfoByUUID(const std::string& uuid, PerAccountUserInfo &account_info);
    static int GetPerAccountInfoByEmail(const std::string& email, PerAccountUserInfo &account_info);
    static int GetPerAccountInfoByMobileNumber(const std::string& mobile_number, PerAccountUserInfo &account_info);
    static int GetPerAccountInfoByEmailFromMasterDB(const std::string& email, PerAccountUserInfo &account_info);
    static int GetPerAccountInfoByMobileFromMasterDB(const std::string& mobile_number, PerAccountUserInfo &account_info);
    static int GetPerAccountInfoByLoginAccount(const std::string& login_account, PerAccountUserInfo& user_info);
    static int GetPerAccountInfoByAccount(const std::string& account, PerAccountUserInfo& user_info);
    static int GetMainAccountByAccount(const std::string& account, std::string& main_account);
    static int GetMainAccountByAccountUUID(const std::string& uuid, std::string& main_account);
    static int GetMainAccountInfoByPerAccountUUID(const std::string& personal_account_uuid, PersonalAccountNodeInfo& master);
    static int GetPmMainSitesByAppList(const ResidentPerAccountList& pm_app_list, std::vector<std::string>& pm_main_sites);
    static int GetMainAccountUUIDByAccountUUID(const std::string& uuid, std::string& main_account_uuid);
    static void GetPmAllSitesByAppList(const ResidentPerAccountList& pm_app_list, std::map<std::string, std::string>& pm_all_sites);
    static int GetCurrentSiteByMainSite(const std::string& main_site, std::string& current_site);
private:
    static void GetAccountFromSql(PerAccountUserInfo &account, CRldbQuery& query);
};

}
#endif
