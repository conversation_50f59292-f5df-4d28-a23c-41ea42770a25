#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "ResidentDevices.h"
#include "ResidentPersonalDevices.h"
#include <string.h>
#include "AkLogging.h"
#include "AkcsMonitor.h"
#include "AkcsPasswdConfuse.h"
#include "AkcsMysqlSegFlag.h"
#include "ConnectionManager.h"
#include "util.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/Account.h"

namespace dbinterface
{


static const std::string devices_sec = " ID,Type,Node,MAC,Location,ConfigMD5,\
    SipAccount,SipPwd,NetGroupNumber,RtspPwd,ContactMD5,StairShow,Relay,Config,DclientVer,SipType,Flags,IPAddress,SecurityRelay,outerIP,UUID,Community,\
    Extension,Gateway,SubnetMask,PrimaryDNS,SecondaryDNS,Firmware,Hardware,Status,Port,LastConnection,PrivatekeyMD5,RfidMD5,Flag,AuthCode,FaceMD5,ScheduleMD5,\
    Brand,Arming,IsIPV6,IsDynamicsIV,AccSrvID,IsRepost,Function,AllowEndUserMonitor,ConnUpdateVer,WiredIpAddress,WiredSubnetMask";


ResidentPerDevices::ResidentPerDevices()
{

}

void ResidentPerDevices::GetDevicesFromSql(ResidentDev& dev, CRldbQuery& query)
{ 
    dev.id = ATOI(query.GetRowData(0));
    dev.dev_type = ATOI(query.GetRowData(1));
    Snprintf(dev.node, sizeof(dev.node), query.GetRowData(2));
    Snprintf(dev.mac, sizeof(dev.mac), query.GetRowData(3));
    Snprintf(dev.location, sizeof(dev.location), query.GetRowData(4));
    Snprintf(dev.config_md5, sizeof(dev.config_md5), query.GetRowData(5));
    Snprintf(dev.sip, sizeof(dev.sip), query.GetRowData(6));
    Snprintf(dev.sippwd, sizeof(dev.sippwd), query.GetRowData(7));
    dev.netgroup_num  = ATOI(query.GetRowData(8));
    Snprintf(dev.rtsppwd, sizeof(dev.rtsppwd), query.GetRowData(9));
    Snprintf(dev.contact_md5, sizeof(dev.contact_md5), query.GetRowData(10));
    dev.stair_show = ATOI(query.GetRowData(11));
    Snprintf(dev.relay, sizeof(dev.relay), query.GetRowData(12));
    Snprintf(dev.autop_config, sizeof(dev.autop_config), query.GetRowData(13));
    dev.dclient_ver = ATOI(query.GetRowData(14));
    dev.sip_type = ATOI(query.GetRowData(15));
    dev.flags = ATOI(query.GetRowData(16));
    Snprintf(dev.ipaddr, sizeof(dev.ipaddr), query.GetRowData(17));
    Snprintf(dev.security_relay, sizeof(dev.security_relay), query.GetRowData(18));
    Snprintf(dev.outer_ip, sizeof(dev.outer_ip), query.GetRowData(19));
    Snprintf(dev.uuid, sizeof(dev.uuid), query.GetRowData(20));
	Snprintf(dev.community, sizeof(dev.community), query.GetRowData(21));
    dev.extension = ATOI(query.GetRowData(22));
    Snprintf(dev.gateway, sizeof(dev.gateway), query.GetRowData(23));
    Snprintf(dev.subnet_mask, sizeof(dev.subnet_mask), query.GetRowData(24));
    Snprintf(dev.primary_dns, sizeof(dev.primary_dns), query.GetRowData(25));
    Snprintf(dev.secondary_dns, sizeof(dev.secondary_dns), query.GetRowData(26));
    Snprintf(dev.sw_ver, sizeof(dev.sw_ver), query.GetRowData(27));
    Snprintf(dev.hw_ver, sizeof(dev.hw_ver), query.GetRowData(28));
    dev.status = ATOI(query.GetRowData(29));
    dev.port = ATOI(query.GetRowData(30));
    Snprintf(dev.last_connection, sizeof(dev.last_connection), query.GetRowData(31));
    Snprintf(dev.private_key_md5, sizeof(dev.private_key_md5), query.GetRowData(32));
    Snprintf(dev.rf_id_md5, sizeof(dev.rf_id_md5), query.GetRowData(33));
    dev.flag = ATOI(query.GetRowData(34));
    Snprintf(dev.auth_code, sizeof(dev.auth_code), query.GetRowData(35));
    Snprintf(dev.face_md5, sizeof(dev.face_md5), query.GetRowData(36));
    Snprintf(dev.schedule_md5, sizeof(dev.schedule_md5), query.GetRowData(37));
    dev.brand = ATOI(query.GetRowData(38));
    dev.arming = ATOI(query.GetRowData(39));
    dev.is_ipv6 = ATOI(query.GetRowData(40));
    dev.is_dy_iv = ATOI(query.GetRowData(41));  
    Snprintf(dev.acc_srv_id, sizeof(dev.acc_srv_id), query.GetRowData(42));
    dev.repost = ATOI(query.GetRowData(43)); 
    dev.fun_bit = strtoul(query.GetRowData(44), nullptr, 10);
    dev.allow_end_user_monitor = ATOI(query.GetRowData(45));
    dev.conn_version = ATOULL(query.GetRowData(46));  
    Snprintf(dev.wired_ipaddr, sizeof(dev.wired_ipaddr), query.GetRowData(47));
    Snprintf(dev.wired_subnet_mask, sizeof(dev.wired_subnet_mask), query.GetRowData(48));
    //是不是单住户 办公和社区 = 0 单住户 = 1
    dev.is_personal = 1;
    std::string sw_ver = dev.sw_ver;
    auto pos = sw_ver.find(".");
    if (pos != std::string::npos)
    {
        dev.oem_id = ATOI(sw_ver.substr(pos + 1).c_str());
        dev.firmware = ATOI(sw_ver.substr(0,pos).c_str());
    }
    dev.conn_type = csmain::PERSONNAL_DEV;
    dev.project_type = project::PERSONAL;
    std::string srcpwd = dev.rtsppwd;
    PasswdDecode(srcpwd.c_str(), srcpwd.size(), dev.rtsppwd, sizeof(dev.rtsppwd));
    srcpwd = dev.sippwd;
    PasswdDecode(srcpwd.c_str(), srcpwd.size(), dev.sippwd, sizeof(dev.sippwd));
    return;
}

int ResidentPerDevices::InitDevicesBySip(const std::string& sip, ResidentDev &dev)
{
    std::stringstream sql;
    sql << "select " << devices_sec <<" from PersonalDevices where SipAccount = '"
              << sip
              << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        GetDevicesFromSql(dev, query);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;    
}

int ResidentPerDevices::GetDevicesBySip(const std::string& sip, ResidentDev &dev)
{
    return InitDevicesBySip(sip, dev);
}

int ResidentPerDevices::GetLocationAndNodeBySip(const std::string& sip, std::string& location, std::string& node)
{
    ResidentDev dev;
    int ret = InitDevicesBySip(sip, dev);
    if (ret == 0)
    {
        location = dev.location;
        node = dev.node;
    }
    else
    {
        location = "";
        node = "";
    }
    return ret;
}

int ResidentPerDevices::GetDevTypeBySip(const std::string& sip)
{
    ResidentDev dev;
    if (InitDevicesBySip(sip, dev))
    {
        return dev.dev_type;
    }

    return -1;
}

std::string ResidentPerDevices::GetLocationBySip(const std::string& sip)
{
    ResidentDev dev;
    if (0 == InitDevicesBySip(sip, dev))
    {
        return dev.location;
    }
    else
    {
        return "";
    }
}

int ResidentPerDevices::GetNodeDevList(const std::string& node, ResidentDeviceList &devlist)
{
    if ( node.length() == 0 )
    {
        AK_LOG_WARN << "GetNodeDevList failed. node=null!";
        return -1;
    }
    std::stringstream sql;
    sql << "select " << devices_sec <<" from PersonalDevices where Node = '"
              << node
              << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        ResidentDev dev;
        GetDevicesFromSql(dev, query);
        devlist.push_back(dev);
    }
    ReleaseDBConn(conn);
    return (devlist.size() > 0) ? 0 : -1;  
}

int ResidentPerDevices::UpdatePerDevMD5(ResidentDev &dev, DEVICES_MD5_TYPE type)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (nullptr == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    std::string md5_sec = "";
    std::string md5 = "";
    switch(type)
    {
        case CONFIG_MD5:
            md5 = "ConfigMD5";
            md5_sec = dev.config_md5;
            break;
        case FACE_MD5:
            md5 = "FaceMD5";
            md5_sec = dev.face_md5;
            break;
        case USER_MATE_MD5:
            md5 = "UserMetaMD5";
            md5_sec = dev.user_mate_md5;
            break;
        case SCHEDULE_MD5:
            md5 = "ScheduleMD5";
            md5_sec = dev.schedule_md5;;
            break;
        case CONTACT_MD5:
            md5 = "ContactMD5";
            md5_sec = dev.contact_md5;;
            break;                
    }

    std::stringstream sql;
    sql << "update PersonalDevices set  " << md5 <<"='" << md5_sec <<"' where ID =" << dev.id;           

    int ret = tmp_conn->Execute(sql.str()) >= 0 ? 0 : -1;
    if (-1 == ret)
    {
        char error_msg[1024];
        snprintf(error_msg, sizeof(error_msg), "Update PersonalDevices failed, ID is [%d], %s is [%s]", dev.id, md5.c_str(), md5_sec.c_str());
        AK_LOG_WARN << error_msg;
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("dbinterface", error_msg, AKCS_MONITOR_ALARM_UPDATE_DEVICE_MD5_FAILED);
    }       
    ReleaseDBConn(conn);
    return 0;
}

int ResidentPerDevices::UpdatePerDevMD5(ResidentDeviceList &dev_list, DEVICES_MD5_TYPE type)
{
    for(auto dev : dev_list)
    {
        UpdatePerDevMD5(dev, type);
    }
    return 0;
}

//这里csconfig会异步处理写文件，不能用mac。要用id或者uuid
int ResidentPerDevices::UpdateMd5ByID(uint32_t id, SHADOW_TYPE shadow_type, const std::string& value)
{
    std::stringstream sql;
    
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    std::string column = dbinterface::Shadow::GetMd5ColumnByType(shadow_type);
    if(column.size() == 0)
    {
        AK_LOG_WARN << "shadow type is illegal.";
        ReleaseDBConn(conn);
        return -1;
    }
    
    sql << "UPDATE PersonalDevices set " << column 
         << "='"  << value << "' where ID=" << id;
   
    int ret = tmp_conn->Execute(sql.str()) >= 0 ? 0 : -1;
    if (-1 == ret)
    {
        char error_msg[1024];
        snprintf(error_msg, sizeof(error_msg), "Update PersonalDevices failed, ID is [%d], %s is [%s]", id, column.c_str(), value.c_str());
        AK_LOG_WARN << error_msg;
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("dbinterface", error_msg, AKCS_MONITOR_ALARM_UPDATE_DEVICE_MD5_FAILED);
    }       
    ReleaseDBConn(conn);
    return ret;
}

int ResidentPerDevices::GetMacDev(const std::string& mac, ResidentDeviceList &dev_list)
{
    ResidentDev dev;
    GetMacDev(mac, dev);
    dev_list.push_back(dev);
    return 0;
}

int ResidentPerDevices::GetMacDev(const std::string& mac, ResidentDev &dev)
{
    std::stringstream sql;
    sql << "/*master*/select " << devices_sec <<" from PersonalDevices where Mac = '"
              << mac
              << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        GetDevicesFromSql(dev, query);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;  
}

int ResidentPerDevices::GetDevByID(int id, ResidentDev &dev)
{
    std::stringstream sql;
    sql << "select " << devices_sec <<" from PersonalDevices where ID = " << id;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        GetDevicesFromSql(dev, query);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;  
}

int ResidentPerDevices::GetDevByIds(const std::string& ids, ResidentDeviceList &dev_list)
{
    std::stringstream stream_sql;
    stream_sql << "select " << devices_sec <<" from PersonalDevices where ID in (" << ids << ")";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(stream_sql.str());
    
    while (query.MoveToNextRow())
    {
        ResidentDev dev;
        GetDevicesFromSql(dev, query);
        dev_list.push_back(dev);
    }

    ReleaseDBConn(conn);
    return 0;  
}

int ResidentPerDevices::GetUUIDDev(const std::string& uuid, ResidentDev &dev)
{
    std::stringstream sql;
    sql << "select " << devices_sec <<" from PersonalDevices where UUID = '"
              << uuid
              << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        GetDevicesFromSql(dev, query);
    }
    else
    {
        AK_LOG_INFO << "Get PersonalDevices uuid info error, uuid not exist. maybe exist Devices. uuid=" << uuid;
        ReleaseDBConn(conn);
        return -1;
    }
    ReleaseDBConn(conn);
    return 0;  
}

int ResidentPerDevices::GetSipDev(const std::string& sip, ResidentDev &dev)
{
    std::stringstream sql;
    sql << "select " << devices_sec <<" from PersonalDevices where SipAccount = '"
              << sip
              << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        GetDevicesFromSql(dev, query);
    }
    else
    {
        ReleaseDBConn(conn);
        return -1;
    }

    ReleaseDBConn(conn);
    return 0;     
}

bool ResidentPerDevices::CheckKitDevice(const std::string &mac, const std::string &node)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* rldb_conn = conn.get();
    if (NULL == rldb_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return false;
    }

    std::stringstream sql;
    CRldbQuery query(rldb_conn);
    int distributor_id = 0;

    sql << "select b.Account Distributor, b.ID DistributorID from Account a "
        << " join Account b on a.ParentID = b.ID "
        << " join PersonalAccount t on a.ID = t.ParentID "
        << " where t.Account = '" << node << "' LIMIT 1";

    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        distributor_id = ATOI(query.GetRowData(1));
    }
    else
    {
        AK_LOG_WARN << "Find Distributor error,sql=" << sql.str();
        ReleaseDBConn(conn);
        return false;
    }


    sql.str("");
    sql.clear();
    sql << "select 1 from DeviceForRegister r  "
        << " WHERE r.MAC = '" << mac << "' and r.MngID = " << distributor_id << " LIMIT 1";

    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
    }
    else
    {
        AK_LOG_WARN << "Device not add to mac library,sql=" << sql.str();
        ReleaseDBConn(conn);
        return false;
    }

    sql.str("");
    sql.clear();
    sql << "select 1 from PersonalDevices r  "
        << " WHERE r.MAC = '" << mac << "' LIMIT 1";

    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        AK_LOG_WARN << "Device is alreay be binded,sql=" << sql.str();
        ReleaseDBConn(conn);
        return false;
    }

    ReleaseDBConn(conn);
    return true;
}

//单住户公共设备
int ResidentPerDevices::GetPerMngDevByMngAccount(const std::string& mng_account, std::vector<DEVICE_SETTING>& devs)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(conn.get());
    std::stringstream sql;
    sql << "select MAC from PersonalDevices where Community='"
        << mng_account <<"' and type= " << DEVICE_TYPE_MANAGEMENT << " and flag = 1";
    query.Query(sql.str());
    int ret = -1;
    while (query.MoveToNextRow())
    {
        DEVICE_SETTING dev;
        memset(&dev, 0, sizeof(dev));
        Snprintf(dev.mac, sizeof(dev.mac), query.GetRowData(0));
        devs.push_back(dev);
        ret = 0;
    }
    ReleaseDBConn(conn);
    return ret;
}

int ResidentPerDevices::SetPerDeviceDisConnTime(const std::string& mac, const std::string& logic_srv_ip)
{
    GET_DB_CONN_ERR_RETURN(db_conn, -1);

    char sql[1024] = "";
    ::snprintf(sql, sizeof(sql),
        "update PersonalDevices set LastDisConn=CURRENT_TIMESTAMP where MAC='%s' and AccSrvID='%s'",
        mac.c_str(), logic_srv_ip.c_str()
    );

    int ret = db_conn->Execute(sql) > 0 ? 0 : -1;
    return ret;
}

int ResidentPerDevices::SetPerDeviceDisConnectStatus(const std::string& mac, const std::string& logic_srv_ip, uint64_t conn_version)
{
    char sql[1024] = "";
    ::snprintf(sql, sizeof(sql),"update PersonalDevices set Status=0, DoorRelayStatus='',DoorSeRelayStatus='',LastDisConn=CURRENT_TIMESTAMP,ConnUpdateVer=%lu + 1  where MAC='%s' and AccSrvID='%s' and ConnUpdateVer = %lu",
                  conn_version,
                  mac.c_str(),
                  logic_srv_ip.c_str(),
                  conn_version);
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    int affact_row = conn->Execute(sql);
    int ret = affact_row > 0 ? 0 : -1; 
    if (affact_row == 0)
    {
        AK_LOG_WARN << "update per device status=0 is affact row 0. mac=" << mac << " ver:" << conn_version;
    }

    ReleaseDBConn(conn);
    return ret;
}

int ResidentPerDevices::SetPerDeviceArmingStatus(const std::string& mac, int indoor_arming)
{
    char sql[1024] = "";
    ::snprintf(sql, sizeof(sql),"update PersonalDevices set Arming=%d where mac='%s'", indoor_arming, mac.c_str());
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    int ret = conn->Execute(sql) > 0 ? 0 : -1;
    ReleaseDBConn(conn);
    return ret;
}

int ResidentPerDevices::SetDeviceSensorTirggerInfo(const std::string& mac, int home, int away, int sleep)
{
    char sql[1024] = "";
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    if (home)
    {
        ::snprintf(sql, sizeof(sql),"update PersonalDevices set Flags=Flags|%d where MAC='%s';",
            MysqlSegFlags::STHOME, mac.c_str());
        conn->Execute(sql);
    }
    else
    {
        ::snprintf(sql, sizeof(sql),"update PersonalDevices set Flags=Flags^%d where MAC='%s';",
            MysqlSegFlags::STHOME, mac.c_str());
        conn->Execute(sql);
    }

    if (away)
    {
        ::snprintf(sql, sizeof(sql),"update PersonalDevices set Flags=Flags|%d where MAC='%s';",
            MysqlSegFlags::STAWAY, mac.c_str());
        conn->Execute(sql);
    }
    else
    {
        ::snprintf(sql, sizeof(sql),"update PersonalDevices set Flags=Flags^%d where MAC='%s';",
            MysqlSegFlags::STAWAY, mac.c_str());
        conn->Execute(sql);
    }

    if (sleep)
    {
        ::snprintf(sql, sizeof(sql),"update PersonalDevices set Flags=Flags|%d where MAC='%s';",
            MysqlSegFlags::STSLEEP, mac.c_str());
        conn->Execute(sql);
    }
    else
    {
        ::snprintf(sql, sizeof(sql),"update PersonalDevices set Flags=Flags^%d where MAC='%s';",
            MysqlSegFlags::STSLEEP, mac.c_str());
        conn->Execute(sql);
    }

    ReleaseDBConn(conn);
    return 0;
}

int ResidentPerDevices::SetDeviceRelayStatus(const std::string& mac, int relay_status)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    int ret = 0;
    char sql[256] = {0};
    ::snprintf(sql, sizeof(sql), "update PersonalDevices set Flags=Flags&%d|%d where MAC='%s';", 
        DEVICE_FLAGS_WITHOUT_RELAY, (relay_status<<4), mac.c_str());
    ret = conn->Execute(sql) >= 0 ? 0 : -1;

    ReleaseDBConn(conn);
    return ret;
}

int ResidentPerDevices::GetNodeIndoorDevList(const std::string& node, ResidentDeviceList &devlist)
{
    if ( node.length() == 0 )
    {
        AK_LOG_WARN << "GetNodeDevList failed. node=null!";
        return -1;
    }
    std::stringstream sql;
    sql << "select " << devices_sec <<" from PersonalDevices where Node = '"
              << node
              << "' and Type=" << DEVICE_TYPE_INDOOR;

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    while (query.MoveToNextRow())
    {
        ResidentDev dev;
        GetDevicesFromSql(dev, query);
        devlist.push_back(dev);
    }
    ReleaseDBConn(conn);
    return (devlist.size() > 0) ? 0 : -1;  
}

int ResidentPerDevices::UpdateDeviceInfo(DEVICE_SETTING* device_setting)
{
    if (device_setting == NULL)
    {
        AK_LOG_WARN << "pDeviceSettingql is null.";
        return -1;
    }
    std::string logic_srv_ip = GetEth0IPAddr();
    int flags_relay = device_setting->relay_status << 4;
    char sql[1024] = "";
    ::snprintf(sql, sizeof(sql),"update PersonalDevices set SubnetMask='%s',Gateway='%s',PrimaryDNS='%s',\
        			SecondaryDNS='%s',Status=%d,outerIP='%s',Port=%d,Firmware='%s',Hardware='%s',LastConnection='%s',DclientVer='%d',\
       				 AccSrvID='%s',Arming='%d', Flags=Flags&%d|%d,IPAddress='%s',IsIPV6=%d,IsDynamicsIV=%d,Function=%lu,WiredIpAddress='%s',WiredSubnetMask='%s',ConnUpdateVer= ConnUpdateVer + 1\
       				 where ID=%u",
                   device_setting->subnet_mask,
                   device_setting->gateway,
                   "",
                   "",
                   device_setting->status,
                   device_setting->outer_ip,
                   device_setting->port,
                   device_setting->SWVer,
                   device_setting->HWVer,
                   device_setting->last_connection,
                   device_setting->dclient_version,
                   logic_srv_ip.c_str(),
                   device_setting->indoor_arming,
                   DEVICE_FLAGS_WITHOUT_RELAY,
                   flags_relay,
                   device_setting->ip_addr,
                   device_setting->is_ipv6,
                   device_setting->dynamics_iv,
                   device_setting->fun_bit,
                   device_setting->wired_ip_addr,
                   device_setting->wired_subnet_mask,
                   device_setting->id);

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    conn->BeginTransAction();
    int affact_row = conn->Execute(sql);
    int ret = affact_row >= 0 ? affact_row : -1; //影响0行也是正确的
    uint64_t conn_version = 0;
    ::snprintf(sql, sizeof(sql),"select ConnUpdateVer from PersonalDevices where ID=%u ", device_setting->id);

    CRldbQuery query(tmp_conn);
    query.Query(sql);
    if (query.MoveToNextRow())
    {
        conn_version = ATOULL(query.GetRowData(0));
    }

    device_setting->conn_version = conn_version;

    conn->EndTransAction();
    ReleaseDBConn(conn);
    return ret;
}

bool ResidentPerDevices::CheckIndoorPlan(const std::string& account)
{
    bool ret = true;
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return ret;
    }

    std::stringstream sql;
    sql << "select MAC from DevicesSpecial where Account = '" << account << "'";

    
    CRldbQuery query(tmp_conn);
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        std::string mac = query.GetRowData(0);
        std::stringstream sql1;
        sql1 << "select Flags from PersonalDevices where MAC = '" << mac << "'";
        query.Query(sql1.str());
        if (query.MoveToNextRow())
        {
            int flag = dbinterface::SwitchHandle(ATOI(query.GetRowData(0)), DeviceSwitch::INDOOR_ONLINE);   
            ret = (flag == 1 ? true : false);
        }
    }

    //释放数据库连接
    ReleaseDBConn(conn);

    return ret;
}

//获取家庭内一台在线的室内机的用于转流
int ResidentPerDevices::GetRepostDev(const std::string& uid, std::string &mac)
{
    ResidentDeviceList dev_list;
    ResidentPerAccount user_info;

    if (0 != dbinterface::ResidentPersonalAccount::GetUidAccount(uid, user_info))
    {
        return -1;
    }

    std::string node = user_info.account;

    if (user_info.role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT)
    {
        ResidentPerAccount node_info;
        if (0 == dbinterface::ResidentPersonalAccount::GetAccountByID(user_info.parent_id, node_info))
        {
            node = node_info.account;
        }
    }

    if (0 != GetNodeDevList(node, dev_list))
    {
        return -1;
    }

    for (const auto& dev : dev_list)
    {
        if (dev.dev_type == DEVICE_TYPE_INDOOR && dev.repost && dev.status)
        {
            mac = dev.mac;
            return 0;
        }
    }

    return -1;        
}
//单租户更新设备door relay status信息
int ResidentPerDevices::UpdateDoorRelayStatus(const std::string &mac, 
const std::string &door_relay_status, const std::string &door_se_relay_status)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* tmp_conn = conn.get();
    if (NULL == tmp_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    std::stringstream sql;
    sql << "update PersonalDevices set DoorRelayStatus = '"
        << door_relay_status
        << "', DoorSeRelayStatus = '"
        << door_se_relay_status
        << "' where MAC = '"
        << mac
        << "'";
    int ret = 0;
    ret = conn->Execute(sql.str()) >= 0 ? 0 : -1;
    ReleaseDBConn(conn);
    return ret;
}

int ResidentPerDevices::GetNetGroupNumByMac(const std::string& mac, int& net_group_num)
{
    GET_DB_CONN_ERR_RETURN(conn, -1);

    std::stringstream sql;
    sql << "SELECT NetGroupNumber FROM PersonalDevices WHERE mac = '" << mac << "'";
    
    CRldbQuery query(conn.get());
    query.Query(sql.str());
    if (query.MoveToNextRow())
    {
        net_group_num = ATOI(query.GetRowData(0));
        return 0;
    }
    else
    {
        AK_LOG_WARN << "No record found for mac: " << mac;
        return -1;
    }
}


}

