#ifndef __PER_RESIDENT_DEVICES_H__
#define __PER_RESIDENT_DEVICES_H__
#include <string>
#include <memory>
#include <vector>
#include <stdint.h>
#include <set>
#include "AkcsCommonDef.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "ResidentDevices.h"
#include "dbinterface/Shadow.h"


namespace dbinterface
{

class ResidentPerDevices
{
public:
    ResidentPerDevices();
    ~ResidentPerDevices(){}

    static std::string GetLocationBySip(const std::string& sip);
    static int GetLocationAndNodeBySip(const std::string& sip, std::string& location, std::string& node);
    static int GetDevTypeBySip(const std::string& sip);
    /*家庭*/
    static int GetNodeDevList(const std::string& node, ResidentDeviceList &devlist);
    static int UpdatePerDevMD5(ResidentDeviceList &dev_list, DEVICES_MD5_TYPE type);
    static int UpdatePerDevMD5(ResidentDev &dev, DEVICES_MD5_TYPE type);
    static int UpdateMd5ByID(uint32_t id, SHADOW_TYPE shadow_type, const std::string& value);    
    static int GetMacDev(const std::string& mac, ResidentDev &dev);
    static int GetMacDev(const std::string& mac, ResidentDeviceList &dev_list);
    static int GetUUIDDev(const std::string& uuid, ResidentDev &dev);
    static int GetSipDev(const std::string& sip, ResidentDev &dev);
    static int GetDevByID(int id, ResidentDev &dev);
    static int GetDevByIds(const std::string& ids, ResidentDeviceList &dev_list);
    static bool CheckKitDevice(const std::string &mac, const std::string &node);
    static int SetPerDeviceDisConnTime(const std::string& mac, const std::string& logic_srv_ip);
    static int SetPerDeviceDisConnectStatus(const std::string& mac, const std::string& logic_srv_ip, uint64_t conn_ver);
    static int SetPerDeviceArmingStatus(const std::string& mac, int indoor_arming);
    static int SetDeviceSensorTirggerInfo(const std::string& mac, int home, int away, int sleep);
    static int SetDeviceRelayStatus(const std::string& mac, int relay_status);
    static int GetNodeIndoorDevList(const std::string& node, ResidentDeviceList &devlist);
    static int UpdateDeviceInfo(DEVICE_SETTING* device_setting);    
    static bool CheckIndoorPlan(const std::string& account);
    static int GetRepostDev(const std::string& uid, std::string &mac);
    static int UpdateDoorRelayStatus(const std::string &mac, const std::string &door_relay_status, const std::string &door_se_relay_status);
    static int GetDevicesBySip(const std::string& sip, ResidentDev &dev);
    static int GetNetGroupNumByMac(const std::string& mac, int& net_group_num);
private:
    static void GetDevicesFromSql(ResidentDev& dev, CRldbQuery& query);
    static int InitDevicesBySip(const std::string& sip, ResidentDev &dev);
};


}
#endif
