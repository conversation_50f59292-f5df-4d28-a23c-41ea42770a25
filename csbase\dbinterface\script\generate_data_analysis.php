<?php

// 生成时替换建表语句
$tableDefine= "
CREATE TABLE `PersonalAccount` (
    `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
    `Account` char(32) NOT NULL,
    `Role` tinyint(1) DEFAULT '0' COMMENT '10=个人用户主账号;11=个人从账号;12=单住户终端公共设备的虚拟账号（上���是单住户的installer）;20=社区用户主账号;21=社区从账号;;30=office主;31=office admin;40=PM',
    `ParentID` int(10) unsigned DEFAULT '0' COMMENT '对于社区用户主账号，就是社区管理员的ID',
    `UnitID` int(10) unsigned DEFAULT '0' COMMENT '对于社区用户就是unit单元的ID',
    `Info` char(64) DEFAULT '',
    `Name` varchar(512) DEFAULT '',
    `SipAccount` char(64) DEFAULT '',
    `SipPwd` char(36) DEFAULT '',
    `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `Address` char(64) DEFAULT '',
    `Phone` varchar(255) DEFAULT '',
    `PhoneStatus` tinyint(1) DEFAULT '0' COMMENT '单住户：0:不使用落地呼叫，1:使用。且该字段是作为是否开启群响铃的开关：0:不开启，1:开启',
    `RoomNumber` char(64) DEFAULT '',
    `TimeZone` varchar(64) DEFAULT '+0:00 Abidjan' COMMENT '时区',
    `ReadMsgID` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '账号读取到通知消息的id',
    `ExpireTime` datetime DEFAULT '2299-12-31 23:59:59' COMMENT '��期时间',
    `EnableIpDirect` tinyint(1) DEFAULT '1' COMMENT '是否启动ip直播 1启动 0关闭 ',
    `Special` tinyint(1) DEFAULT '0' COMMENT '特殊账号标识,标识个人终端管理员里面的公共设备的虚拟主账号',
    `CustomizeForm` tinyint(1) DEFAULT '3' COMMENT 'time1:（1:12小时制，2:24小时制）；time2:(1:y-m-d,3:m-d-y,5:d-m-y),此处值为time1+time2',
    `FreeDays` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '主账号试用天数',
    `NFCCode` varchar(32) NOT NULL DEFAULT '' COMMENT '个人账号的NFC 有值代表开，没有代表关',
    `BLECode` varchar(32) NOT NULL DEFAULT '' COMMENT '个人账号的BLE 有值代表开，没有代表关',
    `Active` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否激活 1激活 0未激活',
    `Initialization` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否初始化过',
    `appLoginStatus` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否在app登陆过 1登陆过 0未登陆过',
    `ActiveTime` timestamp NULL DEFAULT NULL,
    `FirstName` varchar(255) DEFAULT '',
    `LastName` varchar(255) DEFAULT '',
    `RoomID` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '个人房间标识',
    `SipType` tinyint(1) DEFAULT '0' COMMENT '0udp/1tcp/2tls',
    `Codec` varchar(32) NOT NULL DEFAULT '0,8,18' COMMENT '0=PCMU, 8=PCMA, 18=G.729 用逗号隔开代表优先级 18,0,8。如果值空代表默认或客户端自行定义',
    `TempKeyPermission` tinyint(1) DEFAULT '1' COMMENT 'TempKey生成权限 0:无 1:有',
    `Phone2` varchar(255) DEFAULT '',
    `Phone3` varchar(255) DEFAULT '',
    `PhoneCode` char(8) DEFAULT '' COMMENT '区号',
    `PhoneExpireTime` datetime DEFAULT '2020-01-01 00:00:00' COMMENT '不能是当前时间,不然不管有没有开落地,隔天就会邮件提醒',
    `Language` char(8) NOT NULL DEFAULT 'en',
    `Version` int(10) unsigned DEFAULT '123456' COMMENT '用户数据版本号',
    `BLEOpenDoorType` tinyint(1) DEFAULT '0' COMMENT '蓝牙开门的方式 0=摇一摇, 1=无感',
    `UUID` char(36) NOT NULL COMMENT 'uuid()去���横杠,例如45efccef469111ec8fe900163e047e78',
    `ParentUUID` char(36) NOT NULL,
    `EnableSmartHome` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '单住户是否开启智能家居开关，暂时只有单住户生效',
    `Switch` tinyint(1) NOT NULL DEFAULT '4' COMMENT '按位开关标示符:1=旧数据从账号PIN是否初始化(默认0),2=单住户高级功能开关,3=app高级设置开关(默认1)',
    `QrUrl` varchar(128) DEFAULT '' COMMENT '用户二维码链接',
    `UserInfoUUID` char(37) NOT NULL DEFAULT '' COMMENT '终端app和未关联统一的pm app的帐号对应PersonalAccountUserInfo表对应数据的UUID，已关联统一的pm app帐号对应AccountUserInfo表对应数据的UUID。第一位为0：代表关联PersonalAccountUserInfo表。第一位为1：代表关联AccountUserInfo表',
    `EnableStrongAlarm` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '用户是否开启了alarm强提醒开关 0-否,1-是',
    `NameBak` varchar(128) DEFAULT '',
    `PhoneBak` char(24) DEFAULT '',
    `Phone2Bak` char(24) DEFAULT '',
    `Phone3Bak` char(24) DEFAULT '',
    `FirstNameBak` varchar(64) DEFAULT '',
    `LastNameBak` char(64) DEFAULT '',
    `CommunityRoomUUID` char(36) DEFAULT NULL COMMENT '房间号的UUID',
    `Ringtone` varchar(64) DEFAULT '' COMMENT '推送铃声',
    PRIMARY KEY (`ID`),
    UNIQUE KEY `Account` (`Account`),
    UNIQUE KEY `UUID` (`UUID`),
    KEY `ParentID` (`ParentID`),
    KEY `UnitID` (`UnitID`),
    KEY `NFC_BLE` (`NFCCode`,`BLECode`),
    KEY `RoomID` (`RoomID`),
    KEY `ParentUUID` (`ParentUUID`),
    KEY `UserInfoUUID` (`UserInfoUUID`),
    KEY `Name` (`Name`(100))
  ) ENGINE=InnoDB AUTO_INCREMENT=752 DEFAULT CHARSET=utf8 |
";

function getTableName($tableDefine)
{
    preg_match("/CREATE TABLE `([^`]+)`/", $tableDefine, $matches);
    $tableName = $matches[1];
    return $tableName;
}

function getTableHeaderDefine($input)
{
    // 使用正则表达式将单词分隔
    $output = preg_replace_callback('/([A-Z][a-z]*)/', function($matches) {
        return '_' . strtoupper($matches[1]);
    }, $input);

    // 如果开头有下划线，则去除
    if (substr($output, 0, 1) === '_') {
        $output = substr($output, 1);
    }

    return "__CSADAPT_DATAANALYSIS_" . $output . "_H__";
}

function getDataAnalysisChangeHandle($tableName, $tableDefine)
{
    $dataChangeHandle = "    /*单个变化的检测, 主要用于缓存变化/数据清理*/ \n";

    $tableUpper = preg_replace_callback('/([A-Z][a-z]*)/', function($matches) {
        return '_' . strtoupper($matches[1]);
    }, $tableName);

    // 如果开头有下划线，则去除
    if (substr($tableUpper, 0, 1) === '_') {
        $tableUpper = substr($tableUpper, 1);
    }

    preg_match_all("/`(\w+)` ([^ ]+)/", $tableDefine, $matches, PREG_SET_ORDER);

    foreach ($matches as $match) {
        $fieldName = $match[1];
        $filedUpper = strtoupper($fieldName);
        $dataChangeHandle .= "   {DA_INDEX_{$tableUpper}_{$filedUpper}, \"{$fieldName}\", ItemChangeHandle},\n";
    }

    return rtrim($dataChangeHandle, ",\n");
}

// 表名
$tableName = getTableName($tableDefine);

// 头文件定义
$hppDefine = getTableHeaderDefine($tableName);

$headerDefine = "
#include <map>
#include <list>
#include <vector>
#include <string>
#include <memory>
#include \"AkLogging.h\"
#include \"AkcsWebMsgSt.h\"
#include \"AkcsCommonDef.h\"
";

// 构造hpp文件
$hppContent = "";
$hppContent .= "#ifndef $hppDefine\n";
$hppContent .= "#define $hppDefine\n";
$hppContent .= $headerDefine . "\n";
$hppContent .= "void RegDa" . $tableName . "Handler();" ."\n\n";
$hppContent .= "#endif" ."\n";

$fileName = "DataAnalysis" . $tableName;
file_put_contents("$fileName.h", $hppContent);


// 构造cpp文件
$cppHeaderDefine = "#include \"OfficeNew/DataAnalysis/DataAnalysis" . $tableName . ".h\"\n";
$cppHeaderDefine .= "#include \"OfficeNew/DataAnalysis/DataAnalysisOfficeNewContorl.h\"\n";
$cppHeaderDefine .= "#include \"OfficeNew/DataAnalysis/DataAnalysisFileUpdate.h\"\n";
$cppHeaderDefine .= "#include \"DataAnalysisTableParse.h\"\n";
$cppHeaderDefine .= "#include \"DataAnalysisContorl.h\"\n";
$cppHeaderDefine .= "#include \"DataAnalysisContext.h\"\n";
$cppHeaderDefine .= "#include \"DataAnalysis.h\"\n";

$handleFunction = "
static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
";

$dataChangeHandle = getDataAnalysisChangeHandle($tableName, $tableDefine);


$itemChangeHandler = "
static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    // 使用举例
    std::string uid = data.GetIndex(DA_INDEX_PERSONAL_ACCOUNT_ACCOUNT);

    // 更新数据版本
    dbinterface::ProjectUserManage::UpdateAccountDataVersionByUid(uid);

    OfficeNewFileUpdateInfo update_info(office_uuid, OFFICE_USER_INFO_CHANGE);
    update_info.SetUid(uid);
    OfficeNewFileUpdateContorl::Instance()->ProduceMsg(update_info.GetInfo());
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}
";

$regCppFunction = "void RegDa" . $tableName . "Handler()\n";
$regCppFunction .= "{\n";
$regCppFunction .= "    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);\n";
$regCppFunction .= "    RegDaSort(local_detect_key, da_change_handle, len);\n";
$regCppFunction .= "    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);\n";
$regCppFunction .= "    RegDataAnalysisDBHandlerForNewOffice(local_table_name, ptr);\n";
$regCppFunction .= "}\n";


$cppContent = "";
$cppContent .= $cppHeaderDefine . "\n";
$cppContent .= $handleFunction . "\n";
$cppContent .= "static  DataAnalysisColumnList local_detect_key;\n";
$cppContent .= "static const std::string local_table_name = \"" . $tableName . "\"" . "\n";
$cppContent .= "static DataAnalysisChangeHandle da_change_handle[] = {\n";
$cppContent .= $dataChangeHandle . "\n};\n";
$cppContent .= $itemChangeHandler . "\n";
$cppContent .= $regCppFunction . "\n";

$fileName = "DataAnalysis" . $tableName;
file_put_contents("$fileName.cpp", $cppContent);
?>

