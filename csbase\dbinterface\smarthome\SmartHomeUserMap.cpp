#include <sstream>
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/InterfaceComm.h"
#include "SmartHomeUserMap.h"
#include <string.h>
#include "AkLogging.h"

namespace dbinterface{
SmartHomeUserMap::SmartHomeUserMap()
{

}
SmartHomeUserMap::~SmartHomeUserMap()
{

}

int SmartHomeUserMap::GetSmartHomeUUIDByUser(const std::string &user, std::string &uuid)

{
    std::stringstream streamSQL;
    streamSQL << "SELECT SmartHomeUUID FROM SmartHomeUserMap "
                  << "WHERE (Account = '"
              << user
              << "') limit 1";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* ptmpconn = conn.get();
    if (NULL == ptmpconn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    CRldbQuery query(ptmpconn);

    query.Query(streamSQL.str());
    if (query.MoveToNextRow())
    {
        uuid = query.GetRowData(0);
    }
    ReleaseDBConn(conn);
    return 0;
}



}


