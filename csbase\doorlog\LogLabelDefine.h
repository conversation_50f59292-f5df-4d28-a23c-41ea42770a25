#ifndef _LOG_LABEL_DEFINE_H_
#define _LOG_LABEL_DEFINE_H_

// 旧办公 开门日志字符串定义
static const char open_door_call[] = "Try to unlock door by calling";
static const char open_door_card[] = "Try to unlock door with RF Card";
static const char open_door_on_app[] = "Unlock on SmartPlus";
static const char open_door_on_indoor[] = "Unlock on Indoor Monitor";
static const char open_door_on_guard_phone[] = "Unlock on guard phone";
static const char open_door_face[] = "Try to unlock door with Face Recognition";
static const char open_door_key[] = "Try to unlock door with key";
static const char open_door_with_nfc[] = "Unlock with NFC";
static const char open_door_with_ble[] = "Unlock with Bluetooth";
static const char open_door_with_rf[] = "Unlock with RF card";
static const char open_door_with_private[] = "Unlock with private key";
static const char open_door_with_tmpkey[] = "Unlock with temp key";
static const char open_door_pm_manually[] = "Manually Unlock";
static const char open_door_automatically[] = "Automatically Unlock";
static const char lock_door_pm_manually[] = "Manually Lock";
static const char open_door_delivery[] = "Delivery System Verification";
static const char open_door_with_id_access[] = "ID Access";
static const char booking_amenity_reservation[] = "Amenity Reservation";
static const char open_door_with_handset[] = "Handset";
static const char open_door_with_license_plate[] = "License Plate Unlock";
static const char open_door_lockdown_on[] = "Lockdown On";

// 新办公 开门日志字符串定义
static const char office_open_door_call[] = "Try to unlock door by calling";
static const char office_open_door_card[] = "Try to unlock door with RF Card";
static const char office_open_door_on_app[] = "Unlock on SmartPlus";
static const char office_open_door_on_indoor[] = "Unlock on Indoor Monitor";
static const char office_open_door_on_guard_phone[] = "Unlock on guard phone";
static const char office_open_door_face[] = "Try to unlock door with Face Recognition";
static const char office_open_door_key[] = "Try to unlock door with key";
static const char office_open_door_with_nfc[] = "Unlock with NFC";
static const char office_open_door_with_ble[] = "Unlock with Bluetooth";
static const char office_open_door_with_rf[] = "Unlock with RF card";
static const char office_open_door_with_private[] = "Unlock with private key";
static const char office_open_door_with_tmpkey[] = "Unlock with temp key";
static const char office_open_door_with_inward_unlock[] = "Unlock with inward unlock";
static const char office_open_door_pm_manually[] = "Manually Unlock";
static const char office_open_door_automatically[] = "Automatically Unlock";
static const char office_lock_door_pm_manually[] = "Manually Lock";


#endif /* _LOG_LABEL_DEFINE_H_ */
