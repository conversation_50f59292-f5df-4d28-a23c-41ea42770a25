﻿#include "RecordActLog.h"
#include "AkLogging.h"
#include "AkcsMsgDef.h"
#include "dbinterface/CommunityInfo.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/Account.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/ThirdPartyLockDevice.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "util.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/PersonalPrivateKey.h"
#include "dbinterface/PersonalAppTmpKey.h"
#include "dbinterface/PersonalRfcardKey.h"
#include "UserInfo.h"
#include "dbinterface/VisitorIDAccess.h"
#include "dbinterface/resident/AmenityDevice.h"
#include "dbinterface/AmenityReservation.h"
#include "dbinterface/CommunityRoom.h"
#include "dbinterface/Log/PersonalCapture.h"
#include "dbinterface/LicensePlate.h"
#include "dbinterface/ParkingLotDoor.h"
#include "dbinterface/new-office/DevicesDoorList.h"
#include "dbinterface/new-office/OfficeCompany.h"


RecordActLog& RecordActLog::GetInstance()
{
    static RecordActLog record_log;
    return record_log;
}

int RecordActLog::RewriteProjectInfo(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const ResidentDev& dev)
{
    if (dev.is_personal == 1) 
    {
        ResidentPerAccount account;
        if (dbinterface::ResidentPersonalAccount::GetUidAccount(dev.node, account) == 0)
        {
            Snprintf(act_msg.db_delivery_uuid, sizeof(act_msg.db_delivery_uuid), account.uuid);
        }
    } 
    else 
    {
        Snprintf(act_msg.project_uuid2, sizeof(act_msg.project_uuid2), dev.project_uuid); 
        Snprintf(act_msg.db_delivery_uuid, sizeof(act_msg.db_delivery_uuid), dev.project_uuid); 
        act_msg.mng_id = dev.project_mng_id;
    }    
    
    return 0;
}

int RecordActLog::RewriteRtspCaptureProjectInfo(UIPC_MSG_CAPTURE_RTSP& act_msg, const ResidentDev& dev)
{
    if (dev.is_personal == 1) 
    {
        ResidentPerAccount account;
        if (dbinterface::ResidentPersonalAccount::GetUidAccount(dev.node, account) == 0)
        {
            Snprintf(act_msg.db_delivery_uuid, sizeof(act_msg.db_delivery_uuid), account.uuid);
        }
    } 
    else 
    {
        Snprintf(act_msg.project_uuid, sizeof(act_msg.project_uuid), dev.project_uuid); 
        Snprintf(act_msg.db_delivery_uuid, sizeof(act_msg.db_delivery_uuid), dev.project_uuid);
        act_msg.manager_id = dev.project_mng_id;
    }
    return 0;
}

int RecordActLog::RewriteMotionProjectInfo(PERSONNAL_CAPTURE& act_msg, const ResidentDev& dev)
{
    if (dev.is_personal == 1) 
    {
        ResidentPerAccount account;
        if (dbinterface::ResidentPersonalAccount::GetUidAccount(dev.node, account) == 0)
        {
            Snprintf(act_msg.db_delivery_uuid, sizeof(act_msg.db_delivery_uuid), account.uuid);
        }
    } 
    else 
    {
        Snprintf(act_msg.project_uuid2, sizeof(act_msg.project_uuid2), dev.project_uuid); 
        Snprintf(act_msg.db_delivery_uuid, sizeof(act_msg.db_delivery_uuid), dev.project_uuid); 
        act_msg.manager_id = dev.project_mng_id;
    }
    return 0;
}

int RecordActLog::RewriteOfficeMotionProjectInfo(PERSONNAL_CAPTURE& act_msg, const std::string &project_uuid)
{
    Snprintf(act_msg.project_uuid2, sizeof(act_msg.project_uuid2), project_uuid.c_str()); 
    Snprintf(act_msg.db_delivery_uuid, sizeof(act_msg.db_delivery_uuid), project_uuid.c_str()); 
    return 0;
}

//call开门
void RecordActLog::RecordCallLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const ResidentDev& dev)
{
    //initiator V4.3改为对应开门的SIP
    Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  open_door_call);
    std::string sip = act_msg.initiator; //接听人的sip不可能是群组号，但有可能是手机号码
    if (StringAllisNum(sip) && sip.length() >= 7)//兼容V4.3之前版本 add by chenzhx 5.3版本新增长度判断
    {
        std::string nick_name;
        std::string node;
        int initiator_to_roomname = 0;
        //获取用户姓名和node
        GetNameAndNodeBySip(sip, nick_name, node);
        if (nick_name.empty())
        {
            GetLocationAndNodeBySip(sip, nick_name, node);
            //设备解锁
            if (!nick_name.empty())
            {
                initiator_to_roomname = 1;
                int dev_type;
                GetDevTypeBySip(sip, dev_type);
                //室内机通话开门口机
                if (dev_type == DEVICE_TYPE_INDOOR)
                {
                    Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  open_door_on_indoor);
                    act_msg.act_type = ACT_OPEN_DOOR_TYPE::CLOUD_CALL_UNLOCK_INDOOR;
                }
                //管理机通话开门口机
                else if (dev_type == DEVICE_TYPE_MANAGEMENT)
                {
                    Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  open_door_on_guard_phone);
                    act_msg.act_type = ACT_OPEN_DOOR_TYPE::CLOUD_CALL_UNLOCK_GUARD_PHONE;
                }
            }
            //落地号码通话中开门
            else //手机号码 找到对应的手机所属人
            {
                PersonalPhoneInfo phone_info;
                if (!dev.is_personal)
                {
                    dbinterface::ResidentPersonalAccount::GetPhoneInfoByMngID(sip, dev.project_mng_id, phone_info);
                    nick_name = phone_info.name;
                    node = phone_info.node;
                    Snprintf(act_msg.account_uuid, sizeof(act_msg.account_uuid), phone_info.account_uuid);
                }
                else
                {
                    dbinterface::ResidentPersonalAccount::GetPhoneInfoByNode(sip, dev.node, phone_info);
                    nick_name = phone_info.name;
                    node = phone_info.node;
                    Snprintf(act_msg.account_uuid, sizeof(act_msg.account_uuid), phone_info.account_uuid);
                }

                if (!nick_name.empty())
                {
                    Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  open_door_on_app);
                    act_msg.act_type = ACT_OPEN_DOOR_TYPE::CLOUD_CALL_UNLOCK_APP;
                    //name 改为name(手机号码)
                    nick_name += "(";
                    nick_name += sip;
                    nick_name += ")";
                }
                else
                {
                    nick_name = sip;//都找不到时候  赋值为设备上传的值
                }
            }
        }
        //设备上报initiator对应用户account
        else
        {
            std::string account_uuid;
            if (0 != dbinterface::ResidentPersonalAccount::GetUUIDByAccount(sip, account_uuid))
            {
                AK_LOG_WARN << "get account uuid failed. account:" << sip;
            }
            Snprintf(act_msg.account_uuid, sizeof(act_msg.account_uuid), account_uuid.c_str());
            Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  open_door_on_app);
            act_msg.act_type = ACT_OPEN_DOOR_TYPE::CLOUD_CALL_UNLOCK_APP;
        }

        if (!node.empty())
        {
            CNodeInfo cNodeCfg(node);
            Snprintf(act_msg.room_num, sizeof(act_msg.room_num),  cNodeCfg.getRoomNumber().c_str());
            Snprintf(act_msg.account, sizeof(act_msg.account),  node.c_str());//node
        }
        else
        {
            Snprintf(act_msg.room_num, sizeof(act_msg.room_num),  "--");
        }
        if (initiator_to_roomname)
        {
            nick_name = act_msg.room_num;
        }
        Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql),  nick_name.c_str());
    }
    else
    {
        //用户昵称/设备location
        Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql),  act_msg.initiator);//可能含有特殊字符
    }
    Snprintf(act_msg.key, sizeof(act_msg.key),  "--");//所用的key
}

void RecordActLog::RecordKeyLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg,const ResidentDev& dev)
{
    //写死'visitor'
    Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql),  "visitor");

    if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::LOCALKEY)
    {
        RecordLocalKeyLog(act_msg, dev);
    }
    else //tmpkey
    {
        PersonalTempKeyUserInfo tempkey_user_info;
        RecordTmpKeyLog(act_msg, dev,tempkey_user_info);
    }
}

void RecordActLog::RecordLocalKeyLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const ResidentDev& dev)
{
    int is_apt_pin = 0;
    //分割key key=房间号+key 改完key
    std::string key = act_msg.key;
    std::string room;
    std::size_t found = key.find('+');
    if (found != std::string::npos)
    {
        is_apt_pin = 1;
        room = key.substr(0, found);
        key = key.substr(found + 1);
    }

    std::string name = "";
    if (dev.is_personal || dev.grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
    {
        //个人有联动标识，在apt+pin模式下 这样的查找方式照样能满足需求
        PersonalPrivateKeyInfo key_info;
        memset(&key_info, 0, sizeof(key_info));
        dbinterface::PersonalPrivateKey::GetPersonalPrivateKeyByCode(dev.node, key, key_info);
        name = key_info.account_name;
        Snprintf(act_msg.account_uuid, sizeof(act_msg.account_uuid), key_info.account_uuid);
        CNodeInfo cNodeCfg(dev.node);
        Snprintf(act_msg.room_num, sizeof(act_msg.room_num),  cNodeCfg.getRoomNumber().c_str());
    }
    else
    {
        std::vector<PersonalPrivateKeyInfo> private_infos;
        //社区 主账号设置的rfcard对所属的unit和公共的设备生效
        std::string node;
        dbinterface::PersonalPrivateKey::GetNameAndNodeFromPriKeyForCommunityPubPersonal(dev.grade, key, dev.unit_id, dev.project_mng_id, private_infos);
        if (private_infos.empty())
        {
            //物业对公共设备添加的，一定不会重复 物业+快递
            name = dbinterface::PersonalPrivateKey::GetNameFromPriKeyForCommunityPubWork(act_msg.initiator, dev.project_mng_id, dev.mac);
        }
        else
        {
            if (is_apt_pin)
            {
                int match = 0;
                for (auto& info : private_infos)  //不同房间设置相同的pin, TODO:对于最外围 可能不同build 同房间号设置相同的pin
                {
                    CNodeInfo cNodeCfg(info.node);
                    if (cNodeCfg.getRoomNumber() == room)
                    {
                        node = info.node;
                        name = info.account_name;
                        ++match;
                    }
                }
                if (match > 1)
                {
                    AK_LOG_WARN << "APT+pin modle. devices: " << dev.mac << " privatekey:" << act_msg.initiator << " have more than one private key match";
                }
            }
            else     //可能存在相同的key, >1时候先记录 TODO:后期看能否优化
            {
                if (private_infos.size() > 1)
                {
                    AK_LOG_WARN << "pin modle. devices: " << dev.mac << " privatekey:" << act_msg.initiator << " have more than one private key match";
                }
                //前面已经判断是否为空
                node = private_infos[0].node;
                name = private_infos[0].account_name;
            }
        }
        if (!node.empty())
        {
            if (is_apt_pin)
            {
                Snprintf(act_msg.room_num, sizeof(act_msg.room_num),  room.c_str());
            }
            else
            {
                CNodeInfo cNodeCfg(node);
                Snprintf(act_msg.room_num, sizeof(act_msg.room_num),  cNodeCfg.getRoomNumber().c_str());
            }
            Snprintf(act_msg.account, sizeof(act_msg.account),  node.c_str());//node
        }
        else
        {
            Snprintf(act_msg.room_num, sizeof(act_msg.room_num),  "--");
        }
    }
    if (name.length() == 0)
    {
        name = "visitor";
    }

    Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  open_door_with_private);
    Snprintf(act_msg.key, sizeof(act_msg.key),  key.c_str());//所用的key
    Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql),  name.c_str());
}

void RecordActLog::RecordTmpKeyLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const ResidentDev& dev, PersonalTempKeyUserInfo &tempkey_user_info)
{
    //公共设施设备,优先检查booking tmp key
    if (RecordBookingTmpKeyLog(act_msg, dev, tempkey_user_info))
    {
        //为公共设施设备tmp key则直接返回
        return;
    }

    if (dev.is_personal || dev.grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
    {
        dbinterface::PersonalAppTmpKey::GetUserInfoFromAppTempKey(dev.node, act_msg.initiator, tempkey_user_info);
        if (tempkey_user_info.name.length() == 0)
        {
            //需求要求物业创建的key用description标识name
            //xxxxxxxxxx
            tempkey_user_info.name = dbinterface::PersonalAppTmpKey::GetNameFromAppTmpkeyForCommunityPubWork(act_msg.initiator, dev.project_mng_id, dev.mac, tempkey_user_info.creator);
        }
        CNodeInfo cNodeCfg(dev.node);
        Snprintf(act_msg.room_num, sizeof(act_msg.room_num),  cNodeCfg.getRoomNumber().c_str());
    }
    else
    {
        //社区 主账号设置的tmpkey对所属的unit和公共的设备生效
        dbinterface::PersonalAppTmpKey::GetUserInfoFromAppTempKeyForCommunityPubWork(dev.grade, act_msg.initiator, dev.unit_id, dev.project_mng_id, tempkey_user_info);
        if (tempkey_user_info.name.length() == 0)
        {
            //需求要求物业创建的key用description标识name
            //xxxxxxxxxx
            tempkey_user_info.name = dbinterface::PersonalAppTmpKey::GetNameFromAppTmpkeyForCommunityPubWork(act_msg.initiator, dev.project_mng_id, dev.mac, tempkey_user_info.creator);
        }
        if (tempkey_user_info.node.length() > 0)
        {
            CNodeInfo cNodeCfg(tempkey_user_info.node);
            Snprintf(act_msg.room_num, sizeof(act_msg.room_num),  cNodeCfg.getRoomNumber().c_str());
            Snprintf(act_msg.account, sizeof(act_msg.account),  tempkey_user_info.node.c_str());//node
        }
        else
        {
            Snprintf(act_msg.room_num, sizeof(act_msg.room_num),  "--");
        }
    }

    if (tempkey_user_info.type == DELIVERY_TMPKEY)
    {
        tempkey_user_info.name = "Delivery";
        Snprintf(act_msg.room_num, sizeof(act_msg.room_num),  "--");
    }
    
    if (tempkey_user_info.name.length() == 0)
    {
        tempkey_user_info.name = "visitor";
    }
    Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  open_door_with_tmpkey);
    Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql),  tempkey_user_info.name.c_str());

    //放到外部
    /*
    if ((!act_msg.resp) && (tempkey_user_info.creator.size()  > 0))
    {
        //Tmpkey使用通知创建者
        AK::Server::P2PMainSendTmpkeyUsed msg;
        msg.set_account(tempkey_user_info.creator);
        msg.set_name(tempkey_user_info.name);
        CAkcsPdu pdu;
        pdu.SetMsgBody(&msg);
        pdu.SetHeadLen(sizeof(PduHeader_t));
        pdu.SetVersion(50);
        pdu.SetCommandId(AKCS_M2R_P2P_SEND_TMPKEY_USED_REQ);
        pdu.SetSeqNum(0);
        g_nsq_producer->OnPublish(pdu, gstAKCSConf.nsq_topic);
    }
    */
}

void RecordActLog::RecordRemoteLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg)
{
     std::string sip = act_msg.initiator;
     int dev_type = DEVICE_TYPE_APP;
     std::string nick_name;
     std::string node;
     GetDevTypeBySip(sip, dev_type);
     if (dev_type == DEVICE_TYPE_INDOOR)
     {
         Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  open_door_on_indoor);
         act_msg.act_type = ACT_OPEN_DOOR_TYPE::CLOUD_REMOTE_UNLOCK_INDOOR;
         GetLocationAndNodeBySip(sip, nick_name, node);
     }
     else if (dev_type == DEVICE_TYPE_MANAGEMENT)
     {
         Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  open_door_on_guard_phone);
         act_msg.act_type = ACT_OPEN_DOOR_TYPE::CLOUD_REMOTE_UNLOCK_GUARD_PHONE;
         GetLocationAndNodeBySip(sip, nick_name, node);
     } 
     else
     {
        act_msg.act_type = ACT_OPEN_DOOR_TYPE::CLOUD_REMOTE_UNLOCK_APP;
        //initiator V4.3改为对应开门的SIP
        Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  open_door_on_app);
        //获取用户姓名和node
        GetNameAndNodeBySip(sip, nick_name, node);
        std::string account_uuid;
        if(0 != dbinterface::ResidentPersonalAccount::GetUUIDByAccount(sip, account_uuid))
        {
            AK_LOG_WARN << "get account uuid failed. account:" << sip;
        }
        Snprintf(act_msg.account_uuid, sizeof(act_msg.account_uuid), account_uuid.c_str());
     }

     if (!node.empty())
     {
         CNodeInfo cNodeCfg(node);
         Snprintf(act_msg.room_num, sizeof(act_msg.room_num),  cNodeCfg.getRoomNumber().c_str());
         Snprintf(act_msg.account, sizeof(act_msg.account),  node.c_str());//node
         if (dev_type == DEVICE_TYPE_INDOOR || dev_type == DEVICE_TYPE_MANAGEMENT)
         {
             nick_name = act_msg.room_num;
         }
     }
     else
     {
         Snprintf(act_msg.room_num, sizeof(act_msg.room_num),  "--");
     }

     Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql),  nick_name.c_str());
     Snprintf(act_msg.key, sizeof(act_msg.key),  "--");//所用的key
}

void RecordActLog::RecordRfCardLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const ResidentDev& dev)
{
    //std::string action = open_door_card;
    //action += act_msg.initiator;
    Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  open_door_card);

    //持卡用户昵称,根据node-card_code
    std::string name;
    if (dev.is_personal || dev.grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
    {
        RecordRfCardPersonalLog(act_msg, dev, name);
    }
    else
    {
        RecordRfCardPublicLog(act_msg, dev, name);
    }

    //std::string name = GetPersonnalCaptureInstance()->GetNameFromRFCard(device_setting.device_node, act_msg.initiator);
    if (name.empty())
    {
        AK_LOG_WARN << "failed to get uid's name by node:" << dev.node << " and code:" << act_msg.initiator;
        //直接用rfcard code来
        name = "visitor";
    }

    Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql),  name.c_str());
}

void RecordActLog::RecordRfCardPersonalLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const ResidentDev& dev, std::string& name)
{
    std::string device_node = dev.node;
#if 0   //由于卡号不一定唯一，先注释
    if (1 == device_setting.flag)   //如果是个人公共设备
    {
        GetPersonnalCaptureInstance()->GetDeviceNodeByCard(device_setting.id, act_msg.initiator, device_node);
        Snprintf(act_msg.account, sizeof(act_msg.account),  device_node.c_str());
    }
#endif
    PersonalRfcardKeyInfo key_info;
    memset(&key_info, 0, sizeof(key_info));
    dbinterface::PersonalRfcardKey::GetPersonalRfCardInfoFromRFCard(device_node, act_msg.initiator, key_info);
    name = key_info.account_name;
    if (name.empty())//NFC BLE
    {
        PersonalNfcKeyInfo nfc_info;
        memset(&nfc_info, 0, sizeof(nfc_info));
        dbinterface::ResidentPersonalAccount::GetNfcInfoByNodeAndCode(device_node, act_msg.initiator, nfc_info);
        name = nfc_info.account_name;
        if (!name.empty())
        {
            ChangeOpenDoorTypeRF2NfcBle(act_msg.initiator, act_msg.act_type);
            Snprintf(act_msg.account_uuid, sizeof(act_msg.account_uuid), nfc_info.account_uuid);
        }
        if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::CLOUD_NFC)
        {
            Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  open_door_with_nfc);
        }
        else if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::CLOUD_BLE)
        {
            Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  open_door_with_ble);
        }
        else
        {
            Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  open_door_with_rf);
        }
    }
    else
    {
        Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  open_door_with_rf);
        Snprintf(act_msg.account_uuid, sizeof(act_msg.account_uuid), key_info.account_uuid);
    }
    CNodeInfo cNodeCfg(device_node);
    Snprintf(act_msg.room_num, sizeof(act_msg.room_num),  cNodeCfg.getRoomNumber().c_str());
}

void RecordActLog::RecordRfCardPublicLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const ResidentDev& dev, std::string& name)
{
    //社区 主账号设置的rfcard对所属的unit和公共的设备生效
    std::string node;
    name = dbinterface::PersonalRfcardKey::GetNameAndNodeFromRFCardForCommunityPubPersonal(dev.grade, act_msg.initiator, dev.unit_id, dev.project_mng_id, node);
    if (name.empty())
    {
        name = dbinterface::PersonalRfcardKey::GetNameFromRFCardForCommunityPubWork(act_msg.initiator, dev.project_mng_id, dev.mac);
    }
    if (name.empty())
    {
        if (dev.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
        {
            name = dbinterface::ResidentPersonalAccount::GetNFCNameAndNodeForUnitPubDev(dev.unit_id, act_msg.initiator, node);
        }
        else if (dev.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
        {
            name = dbinterface::ResidentPersonalAccount::GetNFCNameAndNodeForPubDev(dev.project_mng_id, act_msg.initiator, node);
        }
        if (!name.empty())
        {
            ChangeOpenDoorTypeRF2NfcBle(act_msg.initiator, act_msg.act_type);
        }
        if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::CLOUD_NFC)
        {
            Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  open_door_with_nfc);
        }
        else if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::CLOUD_BLE)
        {
            Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  open_door_with_ble);
        }
        else
        {
            Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  open_door_with_rf);
        }
    }
    else
    {
        Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  open_door_with_rf);
    }
    if (!node.empty())
    {
        CNodeInfo cNodeCfg(node);
        Snprintf(act_msg.room_num, sizeof(act_msg.room_num),  cNodeCfg.getRoomNumber().c_str());
        Snprintf(act_msg.account, sizeof(act_msg.account),  node.c_str());//node
    }
    else
    {
        Snprintf(act_msg.room_num, sizeof(act_msg.room_num),  "--");
    }

}

void RecordActLog::RecordFaceLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const ResidentDev& dev)
{
    Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  open_door_face);
    Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql),  act_msg.initiator);
    if (dev.is_personal || dev.grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
    {
        PersonalFaceKeyInfo key_info;
        memset(&key_info, 0, sizeof(key_info));
        dbinterface::ResidentPersonalAccount::GetFaceInfoByNodeAndName(dev.node, act_msg.initiator, key_info);
        Snprintf(act_msg.account_uuid, sizeof(act_msg.account_uuid), key_info.account_uuid);
    }
}

void RecordActLog::RecordHandsetLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg)
{
    std::string node = act_msg.initiator;
    Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action), open_door_with_handset);
    Snprintf(act_msg.account, sizeof(act_msg.account), node.c_str());

    CNodeInfo node_cfg(node);
    std::string room_num = node_cfg.getRoomNumber();
    if (room_num.size() > 0)
    {
        Snprintf(act_msg.room_num, sizeof(act_msg.room_num), room_num.c_str());
        Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), act_msg.room_num);
    }

    Snprintf(act_msg.key, sizeof(act_msg.key), "--"); //相当于远程开门，没有key
    return;
}

int RecordActLog::HandleMode(const SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const ResidentDev& dev)
{
    //快递校验解锁 新社区新设备
    if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::DELIVERY_UNLOCK)
    {
        return NEW_COMMUNITY_NEW_DEVICE;
    }

    if (dev.is_personal)
    {
        return OLD_MODE;
    }

    CommunityInfo community_info(act_msg.mng_id);
    if (community_info.GetIsNew() == IsNewFlag::OLD_COMMUNITY)
    {
        return OLD_MODE;
    }

    if (dev.dclient_ver < D_CLIENT_VERSION_6100)
    {
        if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::LOCALKEY ||
            act_msg.act_type == ACT_OPEN_DOOR_TYPE::RFCARD)
        {
            return NEW_COMMUNITY_OLD_DEVICE;
        }
        else
        {
            return OLD_MODE;
        }
    }

    if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::LOCALKEY ||
        act_msg.act_type == ACT_OPEN_DOOR_TYPE::RFCARD ||
        act_msg.act_type == ACT_OPEN_DOOR_TYPE::FACE ||
        act_msg.act_type == ACT_OPEN_DOOR_TYPE::PM_UNLOCK ||
        act_msg.act_type == ACT_OPEN_DOOR_TYPE::PM_LOCK ||
        act_msg.act_type == ACT_OPEN_DOOR_TYPE::AUTO_UNLOCK ||
        act_msg.act_type == ACT_OPEN_DOOR_TYPE::ID_ACCESS_UNLOCK ||
        act_msg.act_type == ACT_OPEN_DOOR_TYPE::LICENSE_PLATE_UNLOCK)
    {
        return NEW_COMMUNITY_NEW_DEVICE;
    }

    return OLD_MODE;
}

void RecordActLog::SetCaptureAction(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg)
{
    if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::LOCALKEY)
    {
        Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  open_door_with_private);
    }
    else if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::RFCARD)
    {
        ChangeOpenDoorTypeRF2NfcBle(act_msg.initiator, act_msg.act_type);
        if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::CLOUD_NFC)
        {
            Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  open_door_with_nfc);
        }
        else if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::CLOUD_BLE)
        {
            Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  open_door_with_ble);
        }
        else
        {
            Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  open_door_card);
        }
    }
    else if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::FACE)
    {
        Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  open_door_face);
    }
     else if(act_msg.act_type == ACT_OPEN_DOOR_TYPE::PM_UNLOCK)
    {
        Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  open_door_pm_manually);
    }
    else if(act_msg.act_type == ACT_OPEN_DOOR_TYPE::PM_LOCK)
    {
       Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  lock_door_pm_manually);
    }
    else if(act_msg.act_type == ACT_OPEN_DOOR_TYPE::AUTO_UNLOCK)
    {
        Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  open_door_automatically);
    }
    else if(act_msg.act_type == ACT_OPEN_DOOR_TYPE::DELIVERY_UNLOCK)
    {
        Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action), open_door_delivery);
    }
    else if(act_msg.act_type == ACT_OPEN_DOOR_TYPE::ID_ACCESS_UNLOCK)
    {
        Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action), open_door_with_id_access);
    }
    else if(act_msg.act_type == ACT_OPEN_DOOR_TYPE::LICENSE_PLATE_UNLOCK)
    {
        Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action), open_door_with_license_plate);
        LicensePlateInfo license_plate_info; 
        if (DatabaseExistenceStatus::EXIST == dbinterface::LicensePlate::GetLicensePlateByKey(act_msg.key, act_msg.db_delivery_uuid, 
                license_plate_info))
        {
            Snprintf(act_msg.key, sizeof(act_msg.key), license_plate_info.plate);
            Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), license_plate_info.personal_name);
            Snprintf(act_msg.account_uuid, sizeof(act_msg.account_uuid), license_plate_info.personal_uuid);
            if (license_plate_info.project_type == CommonProjectType::OFFICE && strlen(act_msg.company_uuid) == 0)
            {
                // 防止设备端没传perid时根据车牌找到所属者的公司
                std::string office_company_uuid = dbinterface::OfficeCompany::GetOfficeCompanyUUIDByPerUUIDAndRole(license_plate_info.personal_uuid, ACCOUNT_ROLE_OFFICE_NEW_PER);
                Snprintf(act_msg.company_uuid, sizeof(act_msg.company_uuid), office_company_uuid.c_str());
            }
        }
    }
    else
    {
        //do nothing
    }
}

void RecordActLog::NewCommunityOldDeviceHandle(SOCKET_MSG_DEV_REPORT_ACTIVITY &act_msg, const ResidentDev& dev)
{
    std::string name;
    std::string account;
    std::string node;
    bool is_apt_pin = false;
    std::string room;

    if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::LOCALKEY)
    {
        //分割key key=房间号+key 改完key
        std::string key = act_msg.key;
        std::size_t found = key.find('+');
        if (found != std::string::npos)
        {
            is_apt_pin = true;
            room = key.substr(0, found);
            key = key.substr(found + 1);
        }

        GetNameByPrivateKey(key.c_str(), act_msg.mng_id, name, account);
    }
    else if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::RFCARD)
    {
        GetNameByRfcard(act_msg.initiator, act_msg.mng_id, name, account);
        if (name.size() == 0 && account.size() == 0)
        {
            //多找了一次旧表,Todo 优化
            RecordActLog::GetInstance().RecordRfCardLog(act_msg, dev);
            return;
        }
    }
    else
    {
        //do nothing
    }

    //如果account不空说明是用户PIN,如果为空则为快递或者物业的PIN
    if (account.size() > 0)
    {
        GetNameAndNodeBySip(account, name, node);
        CNodeInfo node_info(node);
        Snprintf(act_msg.room_num, sizeof(act_msg.room_num),  node_info.getRoomNumber().c_str());
        Snprintf(act_msg.account, sizeof(act_msg.account),  node.c_str());
    }

    if (is_apt_pin)
    {
        Snprintf(act_msg.room_num, sizeof(act_msg.room_num),  room.c_str());
    }

    //访客用户
    if (name.size() == 0)
    {
        name = "visitor";
    }
    //云用户
    else
    {
        std::string account_uuid;
        if (0 != dbinterface::ResidentPersonalAccount::GetUUIDByAccount(account, account_uuid))
        {
            AK_LOG_WARN << "get account uuid failed. account:" << account;
        }
        Snprintf(act_msg.account_uuid, sizeof(act_msg.account_uuid), account_uuid.c_str());
    }
    Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql),  name.c_str());

    if (strlen(act_msg.room_num) == 0)
    {
        Snprintf(act_msg.room_num, sizeof(act_msg.room_num),  "--");
    }

    SetCaptureAction(act_msg);
}

void RecordActLog::NewModeHandle(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg)
{
    std::string name;
    std::string node;
    uint32_t mng_id = 0;
    if (act_msg.per_id[0] == MODE_DELIVERY)
    {
        int delivery_id = ATOI(&act_msg.per_id[1]);
        name = GetNameFromDelivery(delivery_id);
        act_msg.user_type = DoorLogUserType::DELIVERY;
    }
    else if (act_msg.per_id[0] == MODE_STAFF)
    {
        int staff_id = ATOI(&act_msg.per_id[1]);
        name = GetNameFromStaff(staff_id);
        act_msg.user_type = DoorLogUserType::STAFF;
    }
    else
    {
        ResidentPerAccount per_account;
        memset(&per_account, 0, sizeof(per_account));
        if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(act_msg.per_id, per_account))
        {
            if (per_account.role == ACCOUNT_ROLE_PERSONNAL_MAIN 
                || per_account.role == ACCOUNT_ROLE_COMMUNITY_MAIN 
                || per_account.role == ACCOUNT_ROLE_COMMUNITY_PM)
            {
                node = per_account.account;
                mng_id = per_account.parent_id;
            }
            else
            {
                ResidentPerAccount main_account;
                memset(&main_account, 0, sizeof(main_account));
                if (0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(per_account.parent_uuid, main_account))
                {
                    node = main_account.account;
                    mng_id = main_account.parent_id;
                }
            }
            name = per_account.name;
        }
        //用户和设备要在同个小区下。add by chenzhx ********. 
        //出现过设备设置的per_id是云的sip账号,导致查找到对应的用户而记录错日志。
        if (act_msg.mng_id == mng_id)
        {
            CNodeInfo node_info(node);
            Snprintf(act_msg.room_num, sizeof(act_msg.room_num),  node_info.getRoomNumber().c_str());
            Snprintf(act_msg.account, sizeof(act_msg.account),  node.c_str());           
            Snprintf(act_msg.account_uuid, sizeof(act_msg.account_uuid), per_account.uuid); 
            act_msg.user_type = DoorLogUserType::END_USER;
        }
        //访客id access相关信息获取
        if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::ID_ACCESS_UNLOCK)
        {
            GetVisitorIDAccessLogInfo(act_msg, name);
        }
    }
    if (name.size() == 0)
    {
        name = "visitor";
        act_msg.user_type = DoorLogUserType::VISITOR;
    }
    Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql),  name.c_str());

    if (strlen(act_msg.room_num) == 0)
    {
        Snprintf(act_msg.room_num, sizeof(act_msg.room_num),  "--");
    }

    SetCaptureAction(act_msg);
    return;
}

std::string RecordActLog::GetNameFromDelivery(int id)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* rldb_conn = conn.get();
    if (NULL == rldb_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return "";
    }

    std::stringstream stream_sql;
    stream_sql << "select Name from Delivery  where ID = " << id << " limit 1";

    CRldbQuery query(rldb_conn);
    query.Query(stream_sql.str());

    std::string name;
    if (query.MoveToNextRow())
    {
        name = query.GetRowData(0);
    }

    ReleaseDBConn(conn);
    return name;
}

std::string RecordActLog::GetNameFromStaff(int id)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* rldb_conn = conn.get();
    if (NULL == rldb_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return "";
    }

    std::stringstream stream_sql;
    stream_sql << "select a.Name from Staff a "
               " where a.ID = " << id << " limit 1";

    CRldbQuery query(rldb_conn);
    query.Query(stream_sql.str());

    std::string name;
    if (query.MoveToNextRow())
    {
        name = query.GetRowData(0);
    }

    ReleaseDBConn(conn);
    return name;
}

int RecordActLog::GetNameByPrivateKey(const char *private_key, const int manager_account_id, std::string &name, std::string& account)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* rldb_conn = conn.get();
    if (NULL == rldb_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    char stream_sql[1024];
    snprintf(stream_sql, sizeof(stream_sql), "select a.Name, '' Account from Delivery a where a.PinCode = '%s' and a.CommunityID = %d"
             " union all select '' Name, b.Account from CommPerPrivateKey b where b.Code = '%s' and b.CommunityID = %d",
             private_key, manager_account_id, private_key, manager_account_id);

    CRldbQuery query(rldb_conn);

    query.Query(stream_sql);
    if (query.MoveToNextRow())
    {
        name = query.GetRowData(0);
        account = query.GetRowData(1);
    }

    ReleaseDBConn(conn);
    return 0;
}

int RecordActLog::GetNameByRfcard(const char* rfcard_code, const int manager_account_id, std::string &name, std::string& account)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* rldb_conn = conn.get();
    if (NULL == rldb_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    char stream_sql[1024];
    snprintf(stream_sql, sizeof(stream_sql), "select a.Name, '' Account from Delivery a where a.CardCode = '%s' and a.CommunityID = %d"
             " union all select b.Name, '' Account from Staff b where b.CardCode = '%s' and b.CommunityID = %d"
             " union all select '' Name, c.Account from CommPerRfKey c where c.Code = '%s' and c.CommunityID = %d",
             rfcard_code, manager_account_id, rfcard_code, manager_account_id, rfcard_code, manager_account_id);

    CRldbQuery query(rldb_conn);
    query.Query(stream_sql);

    if (query.MoveToNextRow())
    {
        name = query.GetRowData(0);
        account = query.GetRowData(1);
    }

    ReleaseDBConn(conn);
    return 0;
}

int RecordActLog::EmergencyType(const SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg)
{
    if((act_msg.act_type == ACT_OPEN_DOOR_TYPE::PM_UNLOCK) || 
       (act_msg.act_type == ACT_OPEN_DOOR_TYPE::PM_LOCK)   || 
       (act_msg.act_type == ACT_OPEN_DOOR_TYPE::AUTO_UNLOCK))  
    {
        return 1;
    }
    return 0;
}

void RecordActLog::GetNameAndNodeBySip(const std::string& sip, std::string& nick_name, std::string& node)
{
    ResidentPerAccount account;
    memset(&account, 0, sizeof(account));
    if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(sip, account))
    {
        if (account.role == ACCOUNT_ROLE_PERSONNAL_MAIN 
            || account.role == ACCOUNT_ROLE_COMMUNITY_MAIN 
            || account.role == ACCOUNT_ROLE_COMMUNITY_PM)
        {
            node = account.account;
        }
        else
        {
            ResidentPerAccount main_account;
            memset(&main_account, 0, sizeof(main_account));
            if (0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(account.parent_uuid, main_account))
            {
                node = main_account.account;
            }
        }

        nick_name = account.name;
    }
}

void RecordActLog::GetLocationAndNodeBySip(const std::string& sip, std::string& location, std::string& node)
{
    ResidentDev per_dev;
    ResidentDev dev;
    int ret = dbinterface::ResidentPerDevices::GetSipDev(sip, per_dev);
    if (ret < 0)
    {
        ret = dbinterface::ResidentDevices::GetSipDev(sip, dev);
        if (ret == 0)
        {
            location = dev.location;
            node = dev.node;
        }
    }
    else
    {
        location = per_dev.location;
        node = per_dev.node;
    }
}

void RecordActLog::GetDevTypeBySip(const std::string& sip, int& dev_type)
{
    ResidentDev per_dev;
    ResidentDev dev;
    int ret = dbinterface::ResidentPerDevices::GetSipDev(sip, per_dev);
    if (ret < 0)
    {
        ret = dbinterface::ResidentDevices::GetSipDev(sip, dev);
        if (ret == 0)
        {
            dev_type = dev.dev_type;
        }
    }
    else
    {
        dev_type = per_dev.dev_type;
    }
}

int RecordActLog::ChangeOpenDoorTypeRF2NfcBle(char* pszCode, int& nOpenType)
{
    if (pszCode && pszCode[0] == 'F' && pszCode[1] == '0'  && strlen(pszCode) == 16 )
    {
        nOpenType = ACT_OPEN_DOOR_TYPE::CLOUD_NFC;
    }
    else if (pszCode && pszCode[0] == 'B' && strlen(pszCode) == 16 )
    {
        nOpenType = ACT_OPEN_DOOR_TYPE::CLOUD_BLE;
    }
    return 0;
}

//返回true代表无需后续校验普通tmpkey流程
bool RecordActLog::RecordBookingTmpKeyLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const ResidentDev& dev, PersonalTempKeyUserInfo &tempkey_user_info)
{
    AmenityDeviceInfo amenity_device_info;
    //设备是否绑定公共设施
    if (0 != dbinterface::AmenityDevice::GetAmenityDeviceByDeviceUUID(dev.uuid, amenity_device_info))
    {
        return false;
    }

    //检查tmpkey是否属于booking的
    AmenityReservationInfo amenity_reservation_info;
    if (0 != dbinterface::AmenityReservation::GetAmenityReservationByTmpKey(act_msg.initiator, amenity_reservation_info))
    {
        return false;
    }

    //先赋值公共信息
    act_msg.act_type = ACT_OPEN_DOOR_TYPE::BOOKING_UNLOCK;
    Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  booking_amenity_reservation);
    Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), "visitor"); //先写死visitor

    //确保绑定的是同一个公共设施
    if (strcmp(amenity_device_info.amenity_uuid, amenity_reservation_info.amenity_uuid))
    {
        //由于tmpkey存在booking里面了，因此不可能是普通tmpkey(web那边有保障了)
        return true;
    }

    if (act_msg.resp != 0)
    {
        return true;
    }

    //获取用户信息
    ResidentPerAccount per_account;
    memset(&per_account, 0, sizeof(per_account));
    if (0 != dbinterface::ResidentPersonalAccount::GetUUIDAccount(amenity_reservation_info.personal_account_uuid, per_account))
    {
        AK_LOG_WARN << "Record booking tmpkey user failed. per account uuid=" << amenity_reservation_info.personal_account_uuid;
        return true;
    }

    Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), per_account.name);
    Snprintf(act_msg.account_uuid, sizeof(act_msg.account_uuid), amenity_reservation_info.personal_account_uuid);

    //获取房间号
    CommunityRoomInfo room_info;
    if (0 != dbinterface::CommunityRoom::GetCommunityRoomByUUID(amenity_reservation_info.room_uuid, room_info))
    {
        AK_LOG_WARN << "Record booking tmpkey room num failed. room uuid=" << amenity_reservation_info.room_uuid;
        return true;
    }

    Snprintf(act_msg.room_num, sizeof(act_msg.room_num), room_info.room_number);

    return true;
}

void RecordActLog::GetVisitorIDAccessLogInfo(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, std::string& name)
{
    VisitorIDAccessInfo visitor_id_access_info;
    dbinterface::VisitorIDAccess::GetVisitorIDAccessByUUID(act_msg.per_id, visitor_id_access_info);
    
    CUserDoorLogInfo uesr_doorlog_info(visitor_id_access_info.personal_account_uuid);

    if(name.size() == 0)
    {
        name = visitor_id_access_info.name;
        act_msg.user_type = DoorLogUserType::VISITOR;
    }

    if (strlen(act_msg.room_num) == 0)
    {
        Snprintf(act_msg.room_num, sizeof(act_msg.room_num), uesr_doorlog_info.GetRoomNum().c_str());
    }

    if(strlen(act_msg.account) == 0)
    {
        Snprintf(act_msg.account, sizeof(act_msg.account), uesr_doorlog_info.GetNode().c_str());
    }
}

bool RecordActLog::IsUserActType(int act_type)
{
    if (act_type == ACT_OPEN_DOOR_TYPE::LOCALKEY || act_type == ACT_OPEN_DOOR_TYPE::RFCARD || act_type == ACT_OPEN_DOOR_TYPE::FACE
        || act_type == ACT_OPEN_DOOR_TYPE::LICENSE_PLATE_UNLOCK)
    {
        return true;
    }

    return false;
}

bool RecordActLog::IsUserAttendanceActType(int act_type)
{
    if (act_type == ACT_OPEN_DOOR_TYPE::LOCALKEY || act_type == ACT_OPEN_DOOR_TYPE::RFCARD || act_type == ACT_OPEN_DOOR_TYPE::FACE
    || act_type == ACT_OPEN_DOOR_TYPE::CLOUD_NFC || act_type == ACT_OPEN_DOOR_TYPE::CLOUD_BLE)
    {
        return true;
    }

    return false;
}