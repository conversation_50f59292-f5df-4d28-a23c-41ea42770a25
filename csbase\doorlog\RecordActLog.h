#ifndef _RECORD_ACT_LOG_H
#define _RECORD_ACT_LOG_H

#include <boost/noncopyable.hpp>
#include "AkcsCommonDef.h"
#include "AkcsCommonSt.h"
#include <vector>
#include <string>
#include "LogLabelDefine.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/PersonalPrivateKey.h"
#include "dbinterface/PersonalAppTmpKey.h"
#include "dbinterface/PersonalRfcardKey.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/ThirdPartyLockDevice.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"


enum UuidMode
{
    MODE_DELIVERY = 'D',
    MODE_STAFF = 'S'
};

enum HandleMode
{
    OLD_MODE = 0,
    NEW_COMMUNITY_NEW_DEVICE = 1,
    NEW_COMMUNITY_OLD_DEVICE = 2,
};

enum TmpKeyUserType
{
    COMMUNITY_TMPKEY = 0,
    PERSONAL_TMPKEY = 1,
    DELIVERY_TMPKEY = 2,
};

enum ActLogDevType
{
    ACT_LOG_PERSONAL_DEV = 0,
    ACT_LOG_PUB_DEV = 1,
};

class RecordActLog: private boost::noncopyable
{
public:
    static RecordActLog& GetInstance();

    int RewriteProjectInfo(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const ResidentDev& dev);
    int RewriteRtspCaptureProjectInfo(UIPC_MSG_CAPTURE_RTSP& act_msg, const ResidentDev& dev);
    int RewriteMotionProjectInfo(PERSONNAL_CAPTURE& act_msg, const ResidentDev& dev);
    int RewriteOfficeMotionProjectInfo(PERSONNAL_CAPTURE& act_msg, const std::string &project_uuid);

    void RecordCallLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const ResidentDev& dev);

    void RecordKeyLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const ResidentDev& dev);

    void RecordLocalKeyLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const ResidentDev& dev);

    void RecordTmpKeyLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const ResidentDev& dev, PersonalTempKeyUserInfo &tempkey_user_info);

    void RecordRemoteLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg);

    void RecordRfCardLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const ResidentDev& dev);

    void RecordFaceLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const ResidentDev& dev);

    void RecordHandsetLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg);

    /**
     * 是否按照新模式处理
     * 新模式处理：获取PerID进行处理
     *
     * 新社区+新设备=新模式
     * 新社区+旧设备=旧模式,
     * 旧社区+新设备=旧模式,旧社区下发旧的数据模式,新设备转化为user概念,此时PerID是错误的,所以用旧模式
     * 旧社区+旧设备=旧模式
     *
     * <AUTHOR> (2021/5/24)
     *
     * @param device_setting
     *
     * @return bool
     */
    int HandleMode(const SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const ResidentDev& dev);

    void NewModeHandle(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg);

    void NewCommunityOldDeviceHandle(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const ResidentDev& dev);

    void SetCaptureAction(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg);

    std::string GetNameFromDelivery(int id);
    std::string GetNameFromStaff(int id);

    int EmergencyType(const SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg);
    bool IsUserActType(int act_type);
    bool IsUserAttendanceActType(int act_type);
private:

    void RecordRfCardPersonalLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const ResidentDev& dev, std::string& name);

    void RecordRfCardPublicLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const ResidentDev& dev, std::string& name);

    int GetNameByPrivateKey(const char *private_key, const int manager_account_id, std::string &name, std::string& account);

    int GetNameByRfcard(const char* rfcard_code, const int manager_account_id, std::string &name, std::string& account);

    void GetLocationAndNodeBySip(const std::string& sip, std::string& location, std::string& node);

    void GetDevTypeBySip(const std::string& sip, int& dev_type);

    void GetNameAndNodeBySip(const std::string& sip, std::string& nick_name, std::string& node);

    int ChangeOpenDoorTypeRF2NfcBle(char* pszCode, int& nOpenType);

    bool RecordBookingTmpKeyLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, const ResidentDev& dev, PersonalTempKeyUserInfo &tempkey_user_info);

    void GetVisitorIDAccessLogInfo(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, std::string& name);
};


#endif

