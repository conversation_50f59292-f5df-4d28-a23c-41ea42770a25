﻿#include "RecordNewOfficeLog.h"
#include "RecordActLog.h"
#include "AkLogging.h"
#include "AkcsMsgDef.h"
#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "dbinterface/CommunityInfo.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/ThirdPartyLockDevice.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/PersonalPrivateKey.h"
#include "dbinterface/PersonalAppTmpKey.h"
#include "dbinterface/PersonalRfcardKey.h"
#include "dbinterface/new-office/OfficeAdmin.h"
#include "dbinterface/new-office/OfficeCompany.h"


RecordNewOfficeLog& RecordNewOfficeLog::GetInstance()
{
    static RecordNewOfficeLog record_log;
    return record_log;
}

void RecordNewOfficeLog::RecordNewOfficeRemoteLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg)
{
    ResidentDev dev;
    if (0 == dbinterface::ResidentDevices::GetSipDev(act_msg.initiator, dev))
    {
        if (dev.dev_type == DEVICE_TYPE_INDOOR)
        {
            act_msg.act_type = ACT_OPEN_DOOR_TYPE::CLOUD_REMOTE_UNLOCK_INDOOR;
            Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  office_open_door_on_indoor);
        }
        else if (dev.dev_type == DEVICE_TYPE_MANAGEMENT)
        {
            act_msg.act_type = ACT_OPEN_DOOR_TYPE::CLOUD_REMOTE_UNLOCK_GUARD_PHONE;
            Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  office_open_door_on_guard_phone);
        }  
        
        OfficeDeviceAssignInfo office_device_assign_info;
        if (0 == dbinterface::OfficeDeviceAssign::GetOfficeDeviceAssignByDevicesUUID(dev.uuid, office_device_assign_info))
        {
            Snprintf(act_msg.company_uuid, sizeof(act_msg.company_uuid), office_device_assign_info.office_company_uuid);
        }

        OfficeAccount account;
        if (0 == dbinterface::OfficePersonalAccount::GetUUIDAccount(office_device_assign_info.personal_account_uuid, account))
        {
            Snprintf(act_msg.account, sizeof(act_msg.account), account.account);
            Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), account.name);
        }
    }
    else
    {
        act_msg.act_type = ACT_OPEN_DOOR_TYPE::CLOUD_REMOTE_UNLOCK_APP;
        Snprintf(act_msg.account, sizeof(act_msg.account),  act_msg.initiator);
        Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action), office_open_door_on_app);
        
        OfficeAccount account;
        if (0 == dbinterface::OfficePersonalAccount::GetUidAccount(act_msg.initiator, account))
        {
            Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), account.name);
        }
        
        std::string office_company_uuid = dbinterface::OfficeCompany::GetOfficeCompanyUUIDByPerUUIDAndRole(account.uuid, account.role);
        Snprintf(act_msg.company_uuid, sizeof(act_msg.company_uuid), office_company_uuid.c_str());
    }

    Snprintf(act_msg.key, sizeof(act_msg.key),  "--");
    Snprintf(act_msg.room_num, sizeof(act_msg.room_num),  "--");
    return;
}

void RecordNewOfficeLog::RecordNewOfficeCallLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg)
{
    Snprintf(act_msg.key, sizeof(act_msg.key),  "--");
    Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql),  act_msg.initiator);
    Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  office_open_door_call);
    
    std::string sip = act_msg.initiator; 
    if (StringAllisNum(sip) && sip.length() >= 7)
    {
        ResidentDev dev;
        OfficeAccount account;
        PersonalPhoneInfo phone_info;
        
        // personnel呼叫,通过sip查询
        if (0 == dbinterface::OfficePersonalAccount::GetUidAccount(sip, account))
        {
            act_msg.act_type = ACT_OPEN_DOOR_TYPE::CLOUD_CALL_UNLOCK_APP;
            Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action), office_open_door_on_app);
            Snprintf(act_msg.account, sizeof(act_msg.account), account.account);
            Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), account.name);

            std::string office_company_uuid = dbinterface::OfficeCompany::GetOfficeCompanyUUIDByPerUUIDAndRole(account.uuid, account.role);
            Snprintf(act_msg.company_uuid, sizeof(act_msg.company_uuid), office_company_uuid.c_str());
        }
        else if (0 == dbinterface::ResidentPersonalAccount::GetPhoneInfoByMngID(sip, act_msg.mng_id, phone_info))
        {
            if (strlen(phone_info.name) > 0)
            {
                act_msg.act_type = ACT_OPEN_DOOR_TYPE::CLOUD_CALL_UNLOCK_APP;
                Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  office_open_door_on_app);
                
                std::string landline_open = "(" + sip + ")";
                Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), landline_open.c_str());
            }
            else
            {
                Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), sip.c_str());
            }
            
            std::string office_company_uuid = dbinterface::OfficeCompany::GetOfficeCompanyUUIDByPerUUIDAndRole(phone_info.account_uuid, phone_info.role);
            Snprintf(act_msg.company_uuid, sizeof(act_msg.company_uuid), office_company_uuid.c_str());
            Snprintf(act_msg.account, sizeof(act_msg.account), phone_info.account);
        }
        else if (0 == dbinterface::ResidentDevices::GetSipDev(sip, dev))
        { 
            if (dev.dev_type == DEVICE_TYPE_INDOOR)
            {
                act_msg.act_type = ACT_OPEN_DOOR_TYPE::CLOUD_CALL_UNLOCK_INDOOR;
                Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  office_open_door_on_indoor);
            }
            else if (dev.dev_type == DEVICE_TYPE_MANAGEMENT)
            {
                act_msg.act_type = ACT_OPEN_DOOR_TYPE::CLOUD_CALL_UNLOCK_GUARD_PHONE;
                Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  office_open_door_on_guard_phone);
            }
            
            OfficeDeviceAssignInfo office_device_assign_info;
            if (0 == dbinterface::OfficeDeviceAssign::GetOfficeDeviceAssignByDevicesUUID(dev.uuid, office_device_assign_info))
            {
                Snprintf(act_msg.company_uuid, sizeof(act_msg.company_uuid), office_device_assign_info.office_company_uuid);
            }
            
            OfficeAccount account;
            if (0 == dbinterface::OfficePersonalAccount::GetUUIDAccount(office_device_assign_info.personal_account_uuid, account))
            {
                Snprintf(act_msg.account, sizeof(act_msg.account), account.account);
                Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), account.name);
            }
        }
        else
        {
            act_msg.act_type = ACT_OPEN_DOOR_TYPE::CLOUD_CALL_UNLOCK_APP;
            Snprintf(act_msg.account, sizeof(act_msg.account), sip.c_str());
            Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  office_open_door_on_app);
        }        
    }

    return;
}


void RecordNewOfficeLog::RecordNewOfficeTmpKeyLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, OfficeTempKeyInfo& tempkey_info)
{
    std::string initiator_name = "visitor";
    if (0 == dbinterface::OfficeTempKey::GetOfficeTempKeyInfo(act_msg.initiator, act_msg.project_uuid2, tempkey_info))
    {
        initiator_name = tempkey_info.name;
    }

    if (tempkey_info.creator_type == OfficeTempKeyCreatorType::ENDUSER)
    {
        OfficeAccount account;
        if (0 == dbinterface::OfficePersonalAccount::GetUUIDAccount(tempkey_info.creator_personal_account_uuid, account))
        {
            Snprintf(act_msg.account, sizeof(act_msg.account), account.account);
        }
    } 
    else if (tempkey_info.creator_type == OfficeTempKeyCreatorType::ADMIN)
    {
        //admin创建的tempkey，校验是否开启了admin app,若开启了要通知
        OfficeAdminInfo office_admin_info;
        if (0 == dbinterface::OfficeAdmin::GetOfficeAdminByAccountUUID(tempkey_info.creator_account_uuid, office_admin_info))
        {
            if ((int)AdminAppStatus::ENABLE == office_admin_info.app_status)
            {
                Snprintf(tempkey_info.creator_personal_account_uuid, sizeof(tempkey_info.creator_personal_account_uuid), office_admin_info.personal_account_uuid);
            }
        }
    }
    
    Snprintf(act_msg.room_num, sizeof(act_msg.room_num),  "--");
    Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), initiator_name.c_str());
    Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action), office_open_door_with_tmpkey);
    Snprintf(act_msg.company_uuid, sizeof(act_msg.company_uuid), tempkey_info.office_company_uuid);
    return;
}

void RecordNewOfficeLog::NewOfficeModeHandle(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg)
{
    if (act_msg.per_id[0] == MODE_DELIVERY)
    {
        OfficeDeliveryInfo delivery_info;
        int delivery_id = ATOI(&act_msg.per_id[1]);
        if (dbinterface::OfficeDelivery::GetOfficeDeliveryByID(delivery_id, delivery_info) == 0)
        {
            Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), delivery_info.name);
            Snprintf(act_msg.company_uuid, sizeof(act_msg.company_uuid), delivery_info.office_company_uuid);
        }
    }
    else
    {
        OfficeAccount per_account;
        if (0 == dbinterface::OfficePersonalAccount::GetUidAccount(act_msg.per_id, per_account))
        {
            Snprintf(act_msg.account, sizeof(act_msg.account), per_account.account);
            Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), per_account.name);
            Snprintf(act_msg.account_uuid, sizeof(act_msg.account_uuid), per_account.uuid);

            std::string office_company_uuid = dbinterface::OfficeCompany::GetOfficeCompanyUUIDByPerUUIDAndRole(per_account.uuid, per_account.role);
            Snprintf(act_msg.company_uuid, sizeof(act_msg.company_uuid), office_company_uuid.c_str());
        
        }
    }
        
    if (strlen(act_msg.initiator_sql) == 0)
    {
        Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), "visitor");
    }
    Snprintf(act_msg.room_num, sizeof(act_msg.room_num), "--");

    RecordActLog::GetInstance().SetCaptureAction(act_msg);
    return;
}


void RecordNewOfficeLog::RecordOfficeInwardUnlockLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg)
{
    // 内开门 Exit Button类型:Initiated By显示--，Key显示--，Company显示--
    Snprintf(act_msg.key, sizeof(act_msg.key), "--");
    Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), "--");
    Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action), office_open_door_with_inward_unlock);
    return;
}   

void RecordNewOfficeLog::RecordNewOfficeEmergencyControlLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg)
{
    Snprintf(act_msg.key, sizeof(act_msg.key), "--");
    Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), act_msg.initiator);
    if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::PM_UNLOCK)
    {
        Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action), office_open_door_pm_manually);
    }
    else if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::PM_LOCK)
    {
        Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action), office_lock_door_pm_manually);
    }
    else if (act_msg.act_type == ACT_OPEN_DOOR_TYPE::AUTO_UNLOCK)
    {
        Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action), office_open_door_automatically);
    }   
    return;
}
