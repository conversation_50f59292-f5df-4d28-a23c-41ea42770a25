﻿#ifndef _RECORD_NEW_OFFICE_LOG_H_
#define _RECORD_NEW_OFFICE_LOG_H_

#include <boost/noncopyable.hpp>
#include "AkcsCommonDef.h"
#include "AkcsCommonSt.h"
#include <vector>
#include <string>
#include "LogLabelDefine.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/PersonalPrivateKey.h"
#include "dbinterface/PersonalAppTmpKey.h"
#include "dbinterface/PersonalRfcardKey.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/ThirdPartyLockDevice.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/new-office/OfficeDelivery.h"
#include "dbinterface/new-office/OfficePersonnel.h"
#include "dbinterface/new-office/OfficeTempKey.h"
#include "dbinterface/new-office/OfficeDeviceAssign.h"


class RecordNewOfficeLog : private boost::noncopyable
{
public:
    static RecordNewOfficeLog& GetInstance();

    void RecordNewOfficeRemoteLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg);

    void RecordNewOfficeCallLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg);

    void RecordNewOfficeTmpKeyLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, OfficeTempKeyInfo& tempkey_info);

    void NewOfficeModeHandle(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg);

    void RecordOfficeInwardUnlockLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg);

    void RecordNewOfficeEmergencyControlLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg);
};

#endif
