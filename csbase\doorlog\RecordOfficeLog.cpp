#include "RecordOfficeLog.h"
#include "RecordActLog.h"
#include "AkLogging.h"
#include "AkcsMsgDef.h"
#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "dbinterface/CommunityInfo.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/ThirdPartyLockDevice.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/PersonalPrivateKey.h"
#include "dbinterface/PersonalAppTmpKey.h"
#include "dbinterface/PersonalRfcardKey.h"
#include "dbinterface/new-office/OfficeAdmin.h"
#include "dbinterface/new-office/OfficeCompany.h"

RecordOfficeLog& RecordOfficeLog::GetInstance()
{
    static RecordOfficeLog record_log;
    return record_log;
}

void RecordOfficeLog::RecordOfficeRemoteLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg)
{
    std::string sip = act_msg.initiator;
    std::string nick_name;
    std::string node;
    int dev_type = dbinterface::OfficeDevices::GetDevTypeBySip(sip);
    if (dev_type == DEVICE_TYPE_INDOOR)
    {
        Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  office_open_door_on_indoor);
        act_msg.act_type = ACT_OPEN_DOOR_TYPE::CLOUD_REMOTE_UNLOCK_INDOOR;
        dbinterface::OfficeDevices::GetLocationAndNodeBySip(sip, nick_name, node);
        nick_name = dbinterface::OfficePersonalAccount::GetNickNameByUid(node);
        Snprintf(act_msg.account, sizeof(act_msg.account),  node.c_str());//node
    }
    else if (dev_type == DEVICE_TYPE_MANAGEMENT)
    {
        Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  office_open_door_on_guard_phone);
        act_msg.act_type = ACT_OPEN_DOOR_TYPE::CLOUD_REMOTE_UNLOCK_GUARD_PHONE;
        dbinterface::OfficeDevices::GetLocationAndNodeBySip(sip, nick_name, node);
        nick_name = dbinterface::OfficePersonalAccount::GetNickNameByUid(node);
        Snprintf(act_msg.account, sizeof(act_msg.account),  node.c_str());//node
    } 
    else
    {
         act_msg.act_type = ACT_OPEN_DOOR_TYPE::CLOUD_REMOTE_UNLOCK_APP;
         //initiator V4.3改为对应开门的SIP
         Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  office_open_door_on_app);
         nick_name = dbinterface::OfficePersonalAccount::GetNickNameByUid(act_msg.initiator);
         Snprintf(act_msg.account, sizeof(act_msg.account),  act_msg.initiator);//node
    }

    Snprintf(act_msg.room_num, sizeof(act_msg.room_num),  "--");
    Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql),  nick_name.c_str());
    Snprintf(act_msg.key, sizeof(act_msg.key),  "--");//所用的key
}


// csoffice使用
void RecordOfficeLog::RecordOfficeCallLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg)
{
    //initiator V4.3改为对应开门的SIP
    Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  office_open_door_call);
    std::string sip = act_msg.initiator; //接听人的sip不可能是群组号，但有可能是手机号码
    if (StringAllisNum(sip) && sip.length() >= 7)//兼容V4.3之前版本 add by chenzhx 5.3版本新增长度判断
    {
        std::string nick_name;
        std::string node;
        
        OfficeAccount account;
        if (dbinterface::OfficePersonalAccount::GetUidAccount(sip, account) == 0)
        {
            node = account.account;
            nick_name = account.name;
        }
        if (nick_name.empty())
        {
            dbinterface::OfficeDevices::GetLocationAndNodeBySip(sip, nick_name, node);
            if (!nick_name.empty())
            {
                int dev_type = dbinterface::OfficeDevices::GetDevTypeBySip(sip);
                if (dev_type == DEVICE_TYPE_INDOOR)
                {
                    Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  office_open_door_on_indoor);
                    act_msg.act_type = ACT_OPEN_DOOR_TYPE::CLOUD_CALL_UNLOCK_INDOOR;
                    nick_name = dbinterface::OfficePersonalAccount::GetNickNameByUid(node);
                }
                else if (dev_type == DEVICE_TYPE_MANAGEMENT)
                {
                    Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  office_open_door_on_guard_phone);
                    act_msg.act_type = ACT_OPEN_DOOR_TYPE::CLOUD_CALL_UNLOCK_GUARD_PHONE;
                    nick_name = dbinterface::OfficePersonalAccount::GetNickNameByUid(node);
                }
            }
            else //手机号码 找到对应的手机所属人
            {
                PersonalPhoneInfo phone_info;
                memset(&phone_info, 0, sizeof(phone_info));
                dbinterface::ResidentPersonalAccount::GetPhoneInfoByMngID(sip, act_msg.mng_id, phone_info);
                nick_name = phone_info.name;
                node = phone_info.node;

                if (!nick_name.empty())
                {
                    Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  office_open_door_on_app);
                    act_msg.act_type = ACT_OPEN_DOOR_TYPE::CLOUD_CALL_UNLOCK_APP;
                    //name 改为name(手机号码)
                    nick_name += "(";
                    nick_name += sip;
                    nick_name += ")";
                }
                else
                {
                    nick_name = sip;//都找不到时候  赋值为设备上传的值
                }
            }
        }
        else
        {
            Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  office_open_door_on_app);
            act_msg.act_type = ACT_OPEN_DOOR_TYPE::CLOUD_CALL_UNLOCK_APP;
            node = sip;
        }

        if (!node.empty())
        {
            Snprintf(act_msg.account, sizeof(act_msg.account),  node.c_str());//node
        }

        Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql),  nick_name.c_str());
    }
    else
    {
        //用户昵称/设备location
        Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql),  act_msg.initiator);//可能含有特殊字符
    }
    Snprintf(act_msg.key, sizeof(act_msg.key),  "--");//所用的key

}

void RecordOfficeLog::RecordOfficeTmpKeyLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, PersonalTempKeyUserInfo &tempkey_user_info)
{
    if (act_msg.grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
    {
        dbinterface::PersonalAppTmpKey::GetUserInfoFromAppTempKey(act_msg.account, act_msg.initiator, tempkey_user_info);
        if (tempkey_user_info.name.length() == 0)
        {
            //需求要求物业创建的key用description标识name
            tempkey_user_info.name = dbinterface::PersonalAppTmpKey::GetNameFromAppTmpkeyForOfficePubWork(act_msg.initiator, act_msg.mng_id, act_msg.mac, tempkey_user_info.creator);
        }
    }
    else
    {
        //社区 主账号设置的tmpkey对所属的unit和公共的设备生效
        dbinterface::PersonalAppTmpKey::GetUserInfoFromAppTempKeyForCommunityPubWork(act_msg.grade, act_msg.initiator, act_msg.unit_id, act_msg.mng_id, tempkey_user_info);
        if (tempkey_user_info.name.length() == 0)
        {
            //需求要求物业创建的key用description标识name
            tempkey_user_info.name = dbinterface::PersonalAppTmpKey::GetNameFromAppTmpkeyForOfficePubWork(act_msg.initiator, act_msg.mng_id, act_msg.mac, tempkey_user_info.creator);
        }
        Snprintf(act_msg.account, sizeof(act_msg.account),  tempkey_user_info.node.c_str());//node
    }

    if (tempkey_user_info.name.length() == 0)
    {
        tempkey_user_info.name = "visitor";
    }
    Snprintf(act_msg.capture_action, sizeof(act_msg.capture_action),  office_open_door_with_tmpkey);
    Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql),  tempkey_user_info.name.c_str());
    Snprintf(act_msg.room_num, sizeof(act_msg.room_num),  "--");
}

void RecordOfficeLog::OfficeModeHandle(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg)
{
    std::string name;
    std::string node;

    if (act_msg.per_id[0] == MODE_DELIVERY)
    {
        OfficeDeliveryInfo delivery_info;
        int delivery_id = ATOI(&act_msg.per_id[1]);
        name = RecordActLog::GetInstance().GetNameFromDelivery(delivery_id);
    }
    else if (act_msg.per_id[0] == MODE_STAFF)
    {
        int staff_id = ATOI(&act_msg.per_id[1]);
        name = RecordActLog::GetInstance().GetNameFromStaff(staff_id);
    }
    else
    {
        OfficeAccount per_account;
        if (0 == dbinterface::OfficePersonalAccount::GetUidAccount(act_msg.per_id, per_account))
        {
            node = per_account.account;
            name = per_account.name;
        }

        Snprintf(act_msg.account, sizeof(act_msg.account), node.c_str());
    }

    if (name.size() == 0)
    {
        name = "visitor";
    }
    
    Snprintf(act_msg.room_num, sizeof(act_msg.room_num), "--");
    Snprintf(act_msg.initiator_sql, sizeof(act_msg.initiator_sql), name.c_str());

    RecordActLog::GetInstance().SetCaptureAction(act_msg);
    return;
}


