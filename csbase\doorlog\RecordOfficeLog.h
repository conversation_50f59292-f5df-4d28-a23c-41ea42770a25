#ifndef _RECORD_OFFICE_LOG_H_
#define _RECORD_OFFICE_LOG_H_

#include <boost/noncopyable.hpp>
#include "AkcsCommonDef.h"
#include "AkcsCommonSt.h"
#include <vector>
#include <string>
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/PersonalPrivateKey.h"
#include "dbinterface/PersonalAppTmpKey.h"
#include "dbinterface/PersonalRfcardKey.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/ThirdPartyLockDevice.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/new-office/OfficeDelivery.h"
#include "dbinterface/new-office/OfficePersonnel.h"
#include "dbinterface/new-office/OfficeTempKey.h"
#include "dbinterface/new-office/OfficeDeviceAssign.h"


class RecordOfficeLog: private boost::noncopyable
{
public:
    static RecordOfficeLog& GetInstance();

    void RecordOfficeRemoteLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg);

    void RecordOfficeCallLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg);

    void RecordOfficeTmpKeyLog(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, PersonalTempKeyUserInfo &tempkey_user_info);

    void OfficeModeHandle(SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg);

};

#endif
