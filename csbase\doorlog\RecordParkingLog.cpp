#include "RecordParkingLog.h"
#include "AkLogging.h"
#include "AkcsMsgDef.h"
#include "util.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "dbinterface/Log/ParkingLog.h"
#include "dbinterface/Log/ParkingVehicle.h"
#include "dbinterface/ParkingLotDoor.h"
#include "util_relay.h"

RecordParkingLog& RecordParkingLog::GetInstance()
{
    static RecordParkingLog record_log;
    return record_log;
}

CommonProjectType RecordParkingLog::ConvertProjectType(int project_type)
{
    //0住宅，1办公 2单住户
    if (project_type == 0) {
        return CommonProjectType::COMMUNITY;
    } else if (project_type == 1) {
        return CommonProjectType::OFFICE;
    } else if (project_type == 2) {
        return CommonProjectType::SINGLE;
    }
    return CommonProjectType::ERROR;
}


void RecordParkingLog::NewParkingHandle(const SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, PARKING_LOG& parking_log, const ResidentDev &dev)
{
    ParkingLotDoorInfoList parking_lot_door_info_list;
    dbinterface::ParkingLotDoor::GetParkingLotDoorByDevicesUUID(act_msg.dev_uuid, parking_lot_door_info_list);
    parking_log.io_type = ParkingIoType::ERROR; // 默认都没有匹配上停车场door
    if (parking_lot_door_info_list.size() == 0) { // 表示未匹配上停车场
        return;
    }

    Snprintf(parking_log.project_uuid, sizeof(parking_log.project_uuid), act_msg.project_uuid2);
    Snprintf(parking_log.personal_account_uuid, sizeof(parking_log.personal_account_uuid), act_msg.account_uuid);
    Snprintf(parking_log.office_company_uuid, sizeof(parking_log.office_company_uuid), act_msg.company_uuid);
    Snprintf(parking_log.unit_uuid, sizeof(parking_log.unit_uuid), act_msg.unit_uuid);
    Snprintf(parking_log.license_plate, sizeof(parking_log.license_plate), act_msg.key);
    Snprintf(parking_log.parking_pic_name, sizeof(parking_log.parking_pic_name), act_msg.pic_name);
    Snprintf(parking_log.mac, sizeof(parking_log.mac), dev.mac);
    parking_log.parking_time = act_msg.capture_time;
    parking_log.project_type = ConvertProjectType(dev.project_type);

    // 初始化状态标识
    struct DoorStatus {
        bool is_entry_exit = false;
        bool is_entry = false;
        bool is_exit = false;
    } door_status;
    
    // 设备door可以出现在不同停车场里, 取第一条符合条件记录
    // 查找进哪个停车场,记录分为可进可出记录，同时进出(前两种情况直接用一条记录匹配当前的停车记录)，只能进，只能出
    // 处理每个门禁信息
    for (const auto& door_info : parking_lot_door_info_list) {

        // relay处理, relay_str格式为1234，表示relay1/2/3/4
        const auto process_relay = [&](const char* relay_str, 
                                      DoorRelayType is_security_flag) 
        {
            const size_t len = strlen(relay_str);
            for (size_t i = 0; i < len; ++i) {
                std::string relay_char(1, relay_str[i]);
                int relay_num = ATOI(relay_char.c_str());
                std::string door_name = "";
                if (relay_num == door_info.relay_index && 
                    door_info.is_security == is_security_flag) 
                {
                    if (strlen(parking_log.parking_lot_uuid) == 0) {
                        Snprintf(parking_log.parking_lot_uuid, sizeof(parking_log.parking_lot_uuid), door_info.parking_lot_uuid);
                    }
                    if (parking_log.project_type == CommonProjectType::COMMUNITY) {
                        std::vector<RELAY_INFO> relay_items;
                        ResidentDev resident_dev;
                        dbinterface::ResidentDevices::GetUUIDDev(act_msg.dev_uuid, resident_dev);
                        if (door_info.is_security == DoorRelayType::RELAY) {
                            ParseRelay(resident_dev.relay, relay_items);
                        } else {
                            ParseRelay(resident_dev.security_relay, relay_items);
                        }
                        if (static_cast<size_t>(relay_num) <= relay_items.size() && relay_num > 0) {
                            door_name = relay_items[relay_num - 1].name;
                        }
                    } else if (parking_log.project_type == CommonProjectType::OFFICE) {
                        if (door_info.is_security == DoorRelayType::RELAY) {
                            door_name = dbinterface::DevicesDoorList::GetReportActLogDoorNameList(act_msg.dev_uuid, relay_char.c_str(), "");
                        } else {
                            door_name = dbinterface::DevicesDoorList::GetReportActLogDoorNameList(act_msg.dev_uuid, "", relay_char.c_str());
                        }
                    }
                    std::string parking_door = std::string(act_msg.location);
                    if (!door_name.empty()) {
                        parking_door = parking_door + "-" + door_name;
                    }

                    switch (door_info.door_type) {  // 使用门信息的door_type
                    case ParkingIoType::ENTRY_AND_EXIT:  // 出入均可
                        if (!door_status.is_entry_exit && strcmp(parking_log.parking_lot_uuid, door_info.parking_lot_uuid) == 0) {
                            door_status.is_entry_exit = true;
                            parking_log.io_type = ParkingIoType::ENTRY_AND_EXIT;
                            Snprintf(parking_log.entry_door, sizeof(parking_log.entry_door), parking_door.c_str());
                            Snprintf(parking_log.exit_door, sizeof(parking_log.exit_door), parking_door.c_str());
                            Snprintf(parking_log.parking_door, sizeof(parking_log.parking_door), parking_door.c_str());
                        }
                        break;
                    case ParkingIoType::ENTRY:  // 入口，且停车场匹配
                        if (!door_status.is_entry_exit && !door_status.is_entry && strcmp(parking_log.parking_lot_uuid, door_info.parking_lot_uuid) == 0) {
                            door_status.is_entry = true;
                            parking_log.io_type = ParkingIoType::ENTRY;
                            Snprintf(parking_log.entry_door, sizeof(parking_log.entry_door), parking_door.c_str());
                            Snprintf(parking_log.parking_door, sizeof(parking_log.parking_door), parking_door.c_str());
                        }
                        break;
                    case ParkingIoType::EXIT:  // 出口, 且停车场匹配
                        if (!door_status.is_entry_exit && !door_status.is_exit && strcmp(parking_log.parking_lot_uuid, door_info.parking_lot_uuid) == 0) {
                            door_status.is_exit = true;
                            parking_log.io_type = ParkingIoType::EXIT;
                            Snprintf(parking_log.exit_door, sizeof(parking_log.exit_door), parking_door.c_str());
                            Snprintf(parking_log.parking_door, sizeof(parking_log.parking_door), parking_door.c_str());
                        }
                        break;
                    default:
                        break;
                    }
                }
            }
        };

        // relay（is_security=0）
        process_relay(act_msg.relay, DoorRelayType::RELAY);

        // securityRelay（is_security=1）
        if (!door_status.is_entry_exit) {
            process_relay(act_msg.srelay, DoorRelayType::SECURITY_RELAY);
        }
    }
    // 最终状态判定
    if (door_status.is_entry && door_status.is_exit) {
        parking_log.io_type = ParkingIoType::ENTRY_AND_EXIT;
    }
    return;
}

// 车辆进出表 停车场+车牌唯一记录
void RecordParkingLog::RecordParkingVehicleLog(PARKING_LOG& parking_log)
{
    AK_LOG_INFO << "RecordParkingVehicleLog. parking_log io_type: " << (int)parking_log.io_type << " project_uuid:" 
                << parking_log.project_uuid << " project_type:" << (int)parking_log.project_type << " parking_lot_uuid:"
                << parking_log.parking_lot_uuid << " personal_account_uuid:" << parking_log.personal_account_uuid <<" office_company_uuid:"
                << parking_log.office_company_uuid << " unit_uuid:" << parking_log.unit_uuid << " license_plate:" 
                << parking_log.license_plate << " parking_time:" << parking_log.parking_time << " parking_door:" 
                << parking_log.parking_door << " parking_pic_name:" << parking_log.parking_pic_name << " mac:" 
                << parking_log.mac << " entry_door:" << parking_log.entry_door << " entry_time:"
                << parking_log.entry_time << " entry_pic_name:" << parking_log.entry_pic_name << " exit_door:"
                << parking_log.exit_door << " exit_time:" << parking_log.exit_time << " exit_pic_name:" << parking_log.exit_pic_name;
    // 车辆进入
    if (parking_log.io_type == ParkingIoType::ERROR) {
        // 未匹配上，无停车记录
        return;
    } else {
        PARKING_LOG new_parking_log;
        new_parking_log = parking_log;
        if (parking_log.io_type == ParkingIoType::ENTRY) {
            // 查询是否有人在停车场
            // 若有，变更原有停车记录，把原来的停车记录插到进出表里
            // 若无，直接插入到停车记录表里
            if (dbinterface::ParkingVehicle::ParkingVehicleExist(parking_log.parking_lot_uuid, parking_log.license_plate, new_parking_log) == DatabaseExistenceStatus::EXIST) {
                dbinterface::ParkingVehicle::DelParkingVehicle(parking_log);
                dbinterface::ParkingVehicle::AddParkingVehicle(parking_log);
                dbinterface::ParkingLog::AddParkingLog(new_parking_log);
            } else {
                dbinterface::ParkingVehicle::AddParkingVehicle(parking_log);
            }
        } else if (parking_log.io_type == ParkingIoType::EXIT) {
            // 查询是否有人在停车场
            // 若有，把原来的停车记录和当前记录插到进出表里
            // 若无，直接插入到进出表里
            if (dbinterface::ParkingVehicle::ParkingVehicleExist(parking_log.parking_lot_uuid, parking_log.license_plate, new_parking_log) == DatabaseExistenceStatus::EXIST) {
                dbinterface::ParkingVehicle::DelParkingVehicle(parking_log);
                Snprintf(new_parking_log.exit_door, sizeof(new_parking_log.exit_door), parking_log.parking_door);
                Snprintf(new_parking_log.exit_pic_name, sizeof(new_parking_log.exit_pic_name), parking_log.parking_pic_name);
                dbinterface::ParkingLog::AddParkingLog(new_parking_log);
            } else {
                Snprintf(parking_log.exit_door, sizeof(parking_log.exit_door), parking_log.parking_door);
                Snprintf(parking_log.exit_pic_name, sizeof(parking_log.exit_pic_name), parking_log.parking_pic_name);
                dbinterface::ParkingLog::AddParkingLog(parking_log);
            }
        } else if (parking_log.io_type == ParkingIoType::ENTRY_AND_EXIT) {
            // 匹配最符合当前进出记录的操作
            // 查询是否有人在停车场
            // 若有，把原来的停车记录和当前记录插到进出表里
            // 若无，直接插入到停车记录表里
            if (dbinterface::ParkingVehicle::ParkingVehicleExist(parking_log.parking_lot_uuid, parking_log.license_plate, new_parking_log) == DatabaseExistenceStatus::EXIST) {
                dbinterface::ParkingVehicle::DelParkingVehicle(parking_log);
                Snprintf(new_parking_log.exit_door, sizeof(new_parking_log.exit_door), parking_log.parking_door);
                Snprintf(new_parking_log.exit_pic_name, sizeof(new_parking_log.exit_pic_name), parking_log.parking_pic_name);
                dbinterface::ParkingLog::AddParkingLog(new_parking_log);
            } else {
                dbinterface::ParkingVehicle::AddParkingVehicle(parking_log);
            }
        }
    }

    return;
}