#ifndef _RECORD_PARKING_LOG_H_
#define _RECORD_PARKING_LOG_H_

#include <boost/noncopyable.hpp>
#include "AkcsCommonDef.h"
#include "AkcsCommonSt.h"
#include <vector>
#include <string>
#include "dbinterface/resident/ResidentDevices.h"


class RecordParkingLog: private boost::noncopyable
{
public:
    static RecordParkingLog& GetInstance();
    void NewParkingHandle(const SOCKET_MSG_DEV_REPORT_ACTIVITY& act_msg, PARKING_LOG& parking_log, const ResidentDev &dev);
    void RecordParkingVehicleLog(PARKING_LOG& parking_log);
private:
    CommonProjectType ConvertProjectType(int project_type);
};

#endif
