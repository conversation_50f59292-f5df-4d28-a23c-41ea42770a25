#include <chrono>
#include <ctime>
#include <iostream>
#include "util.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/CommunityRoom.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "doorlog/UserInfo.h"

void CNodeInfo::init()
{
    if (node_.size() < 1)
    {
        return;
    }
    std::stringstream streamSQL;
    streamSQL << "select TimeZone, RoomID,UnitID,RoomNumber,Role from PersonalAccount where Account = '" << node_ << "'";
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* pTmpConn = conn.get();
    if (NULL == pTmpConn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return ;//0时区
    }
    CRldbQuery query(pTmpConn);
    query.Query(streamSQL.str());

    int nRole = 0;
    if (query.MoveToNextRow())
    {
        timezone_ = query.GetRowData(0);
        room_id_ = ATOI(query.GetRowData(1));
        int nUnitID = ATOI(query.GetRowData(2));
		(void)nUnitID;
        room_number_ = query.GetRowData(3);
        nRole = ATOI(query.GetRowData(4));
    }
    else
    {
        ReleaseDBConn(conn);
        return;
    }

    const int ACCOUNT_ROLE_COMMUNITY_MAIN = 20;
    if (nRole == ACCOUNT_ROLE_COMMUNITY_MAIN)
    {
        streamSQL.str("");
        streamSQL << "select RoomName from CommunityRoom where ID = " << room_id_ ;
        CRldbQuery query2(pTmpConn);
        query2.Query(streamSQL.str());

        if (query2.MoveToNextRow())
        {
            room_number_ = query2.GetRowData(0);
        }
        else
        {
            ReleaseDBConn(conn);
            return;
        }
    }

    ReleaseDBConn(conn);
    return;
}

void CNodeInfoForAlexa::init()
{
    if (node_.size() < 1)
    {
        return;
    }
    std::stringstream streamSQL;
    streamSQL << "SELECT AlexaToken FROM Token WHERE Account= '" << node_ << "'";

    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* pTmpConn = conn.get();
    if (NULL == pTmpConn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return;
    }
    CRldbQuery query(pTmpConn);
    query.Query(streamSQL.str());
    if (query.MoveToNextRow())
    {
        alexa_token_ = query.GetRowData(0);
        if (alexa_token_.size() > 0)
        {
            use_alexa_ = 1;
        }
    }
    ReleaseDBConn(conn);
    return;
}

void CUserInfo::init()
{
    if (uid_.size() < 1)
    {
        return;
    }
    std::stringstream streamSQL;
    streamSQL << "select Active,Special,ExpireTime < now() as isExpireTime, ExpireTime from PersonalAccount where Account = '" << uid_ << "'";
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* pTmpConn = conn.get();
    if (NULL == pTmpConn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return ;
    }
    CRldbQuery query(pTmpConn);
    query.Query(streamSQL.str());

    if (query.MoveToNextRow())
    {
        active_ = ATOI(query.GetRowData(0));
        special_ = ATOI(query.GetRowData(1));
        is_expire_ = ATOI(query.GetRowData(2));
        expire_time_ = query.GetRowData(3);
    }

    ReleaseDBConn(conn);
    return;
}

void CUserDoorLogInfo::init()
{
    if (per_uuid_.size() == 0)
    {
        return;
    }

    ResidentPerAccount per_account; 
    if (0 != dbinterface::ResidentPersonalAccount::GetUUIDAccount(per_uuid_, per_account))
    {
        return;
    }

    if (per_account.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT || per_account.role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT)
    {
        memset(&per_account, 0, sizeof(per_account));
        if (0 != dbinterface::ResidentPersonalAccount::GetUUIDAccount(per_account.parent_uuid, per_account))
        {
            return;
        }
    }
    
    node_ = per_account.account;

    CommunityRoomInfo room_info;
    if (0 != dbinterface::CommunityRoom::GetCommunityRoomByUUID(per_account.community_room_uuid, room_info))
    {
        return;
    }

    room_number_ = room_info.room_number;
}