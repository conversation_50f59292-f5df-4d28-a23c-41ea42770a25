#ifndef __USER_INFO_H_
#define __USER_INFO_H_
#include <stdio.h>
#include <string>
#include <boost/noncopyable.hpp>
#include "AkLogging.h"

class CNodeInfo
{
public:

    CNodeInfo(const std::string& stNode)
    {
        node_ = stNode;
        init();
    }

    ~CNodeInfo()
    {

    }

    std::string getRoomNumber()
    {
        return room_number_;
    }

private:
    void init();
    std::string room_number_;
    int room_id_;
    std::string node_;
    std::string timezone_;
    int is_per_;

};


class CNodeInfoForAlexa
{
public:

    CNodeInfoForAlexa(const std::string& stNode)
    {
        node_ = stNode;
        use_alexa_ = 0;
        init();
    }

    ~CNodeInfoForAlexa()
    {

    }


    int IsUseAlexa()
    {
        return use_alexa_;
    }
    std::string AlexaToken()
    {
        return alexa_token_;
    }

private:
    void init();
    std::string alexa_token_;
    int use_alexa_;
    std::string node_;

};




class CUserInfo
{
public:

    CUserInfo(const std::string& uid)
    {
        uid_ = uid;
        init();
    }

    ~CUserInfo()
    {

    }
    //
    int IsUserLegal()
    {
        if (!active_ || is_expire_ || special_)
        {
            AK_LOG_WARN << "uid:" << uid_ << " status: active=" << active_ << " special=" << special_ << " is expire=" << is_expire_ << " expiretime=" << expire_time_;
            return 0;
        }
        return 1;
    }

private:
    void init();
    std::string uid_;
    int active_;
    int special_;//删除房间 留下账号的标识
    int is_expire_;
    std::string expire_time_;
};

//用户开门日志相关信息
class CUserDoorLogInfo
{
public:
    CUserDoorLogInfo(const std::string& per_uuid)
    {
        per_uuid_ = per_uuid;
        init();
    }

    ~CUserDoorLogInfo()
    {

    }

    std::string GetNode() { return node_; }
    std::string GetRoomNum() { return room_number_; }
private:
    void init();
    std::string per_uuid_;
    std::string room_number_;
    std::string node_;
};

#endif
