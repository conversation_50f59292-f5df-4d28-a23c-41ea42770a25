#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <openssl/aes.h>

#include "AES128.h"
#include "Base64.h"

char AES_IV[17] = "1234567887654321";

char* GetCspushAES128IV(char* iv)
{
    if (!iv)
    {
        return NULL;
    }
    memset(iv, 0, 17);
    snprintf(iv, 17, "%s", AES_IV);
    return iv;
}


/*Pkcs5Padding*/
static int AES128_CBC_Encrypt_Padding5(const char* pszKey, char* pszIv, const char* pszSrc, int nDatalen, char** pszDst, int* pnDstlen)
{
    *pszDst = NULL;
    if (!pszKey || !pszIv || !pszSrc || !pnDstlen)
    {
        return -1;
    }
    AES_KEY         key;
    AES_set_encrypt_key((const unsigned char*)pszKey, 128, &key);
    int nPadding;
    int nTotal = (nDatalen / 16 + 1) * 16;
    char* pszEncryptSrc = (char*)malloc(nTotal + 1);
    if (!pszEncryptSrc)
    {
        return -2;
    }
    char* pszEncryptOut = (char*)malloc(nTotal + 1);
    if (!pszEncryptOut)
    {
        free(pszEncryptSrc);
        return -3;
    }
    memset(pszEncryptSrc, 0, nTotal + 1);
    memset(pszEncryptOut, 0, nTotal + 1);

    if (nDatalen % 16 > 0)
    {
        nPadding = nTotal - nDatalen;
    }
    else
    {
        nPadding = 16;
    }

    memset(pszEncryptSrc, nPadding, nTotal);
    *(pszEncryptSrc + nTotal) = 0;
    memcpy(pszEncryptSrc, pszSrc, nDatalen);

    *pnDstlen = nTotal;
    AES_cbc_encrypt((unsigned char*)pszEncryptSrc, (unsigned char*)pszEncryptOut, nTotal, &key, (unsigned char*)pszIv, AES_ENCRYPT);
    *pszDst = pszEncryptOut;
    free(pszEncryptSrc);
    return 0;
}

/*Pkcs5Padding*/
static int AES128_CBC_Decrypt_Padding5(const char* pszKkey, char* pszIv, const char* pszSrc, int nLen, char** pszDst)
{
    *pszDst = NULL;
    if (!pszIv || !pszKkey || !pszSrc)
    {
        return -1;
    }
    AES_KEY         key;
    AES_set_decrypt_key((const unsigned char*)pszKkey, 128, &key);

    char* pszDstTemp = (char*)malloc(nLen + 1);
    if (!pszDstTemp)
    {
        return -1;
    }
    memset(pszDstTemp, 0, nLen + 1);

    AES_cbc_encrypt((const unsigned char*)pszSrc, (unsigned char*)pszDstTemp, nLen, &key, (unsigned char*)pszIv, AES_DECRYPT);
    unsigned char padding = *(pszDstTemp + strlen(pszDstTemp) - 1);
    if (padding > 16 || padding > strlen(pszDstTemp))
    {
        return -2;
    }
    if (padding == *(pszDstTemp + strlen(pszDstTemp) - padding))
    {
        *(pszDstTemp + strlen(pszDstTemp) - padding) = 0;
        *pszDst = pszDstTemp;
    }
    else
    {
        return -3;//padding is error
    }
    return 0;
}

int AES128Decrypt(const char* pszKey, char* pszIV, char* pszBaseSrc, int nBaseSrcLen, char** pszOut)
{
    if (pszKey == nullptr || pszBaseSrc == nullptr || pszOut == nullptr)
    {
        return -1;
    }

    char* pszAESOut = NULL;
    AES128_CBC_Decrypt_Padding5(pszKey, pszIV, pszBaseSrc, nBaseSrcLen, &pszAESOut);
    *pszOut = pszAESOut;
    return 0;
}

int AES128Encrypt(const char* pszKey, char* pszIV, const char* pszSrc, int nSrcLen, char** pszDst, int* nDstLen)
{
    *pszDst = nullptr;
    AES128_CBC_Encrypt_Padding5(pszKey, pszIV, pszSrc, nSrcLen, pszDst, nDstLen);
    if (*pszDst == nullptr)
    {
        return -1;
    }

    return 0;
}


int AES128Base64Decrypt(const char* pszKey, char* pszIV, char* pszBaseSrc, int nBaseSrcLen, char** pszOut)
{
    if (!pszKey || !pszBaseSrc || !pszOut)
    {
        return -1;
    }

    char* pszBaseOut = (char*)malloc(nBaseSrcLen + 1);
    if (!pszBaseOut)
    {
        return 1;
    }
    memset(pszBaseOut, 0, nBaseSrcLen);
    int nBaseOutLen = nBaseSrcLen;
    Base64Decode(pszBaseSrc, nBaseSrcLen, pszBaseOut, &nBaseOutLen);

    char* pszAESOut = NULL;
    AES128_CBC_Decrypt_Padding5(pszKey, pszIV, pszBaseOut, nBaseOutLen, &pszAESOut);
    *pszOut = pszAESOut;
    free(pszBaseOut);
    return 0;
}

int AES128Base64Encrypt(const char* pszKey, char* pszIV, const char* pszSrc, int nSrcLen, char** pszOut)
{
    char* pszEncDst = NULL;
    int nEncDstlen = 0;

    AES128_CBC_Encrypt_Padding5(pszKey, pszIV, pszSrc, nSrcLen, &pszEncDst, &nEncDstlen);
    if (!pszEncDst)
    {
        return -1;
    }
    int nBaseoutLen = (nEncDstlen / 3 + 1) * 4 + 1;
    char* pszBaseout = (char*)malloc(nBaseoutLen);
    if (!pszBaseout)
    {
        free(pszEncDst);
        return -2;
    }
    memset(pszBaseout, 0, nBaseoutLen);

    Base64Encode(pszEncDst, nEncDstlen, pszBaseout, &nBaseoutLen);
    *pszOut = pszBaseout;
    free(pszEncDst);
    return 0;
}

int AES128CBCEncryptFile(const char* src_file, const char* key, const char* dst_file)
{
    if (src_file == nullptr || key == nullptr || dst_file == nullptr)
    {
        return -1;
    }

    FILE* file = fopen(src_file, "rb");
    if (NULL == file)
    {
        return -1;
    }

    // 读文件
    fseek(file, 0, SEEK_END);              //把指针移动到文件的结尾 ，获取文件长度
    int src_file_len = ftell(file);      //获取文件长度
    char src_file_buf[src_file_len + 1];
    memset(src_file_buf, 0, src_file_len + 1);

    rewind(file); //把指针移动到文件开头 因为我们一开始把指针移动到结尾
    fread(src_file_buf, 1, src_file_len, file);
    fclose(file);

    // 文件加密
    int dst_file_len = 0;
    char* dst_file_buf = nullptr;
    char iv[17] = "0000000000000000";
    AES128Encrypt(key, iv, src_file_buf, src_file_len, &dst_file_buf, &dst_file_len);
    if (dst_file_buf == nullptr)
    {
        return -1;
    }

    // 写入加密后的文件
    FILE* fp = fopen(dst_file, "wb+");
    if (fp != nullptr)
    {
        if ((size_t)dst_file_len != fwrite(dst_file_buf, 1, dst_file_len, fp))
        {
            fclose(fp);
            free(dst_file_buf);
            return -1;
        }

        fclose(fp);
        fp = nullptr;
    }

    free(dst_file_buf);
    return 0;
}

int Base64Encrypt(const char* pszSrc, int nSrcLen, char** pszOut)
{
    if (!pszSrc)
    {
        return false;
    }

    char* pszBaseout = NULL;
    int nBaseoutLen = (nSrcLen / 3 + 1) * 4 + 1;
    pszBaseout = (char*)malloc(nBaseoutLen);
    if (!pszBaseout)
    {
        return false;
    }
    memset(pszBaseout, 0, nBaseoutLen);

    Base64Encode(pszSrc, nSrcLen, pszBaseout, &nBaseoutLen);
    *pszOut = pszBaseout;

    return true;
}