#ifndef _AES128_H_
#define _AES128_H_
/*!
 * @brief AES128 BASE64 Padding5
 * <AUTHOR>
 * @date 2018.06.01
 * @version v01
 */


char* GetCspushAES128IV(char* iv);

/*
int AES128_CBC_Encrypt_Padding5(const char *pszKey, char *pszIv, const char *pszSrc, int nDatalen, char **pszDst, int *pnDstlen);
int AES128_CBC_Decrypt_Padding5(const char *psz<PERSON>key, char *pszIv, const char *pszSrc, int nLen, char **pszDst);
char *Base64Encode(const char *pszBuf, const int nSize, char *pszBase64char, int *nLen);
char *Base64Decode(const char *pszBase64char, const int nSrcLen, char *pszOut, int *nOutLen);
*/
int AES128Decrypt(const char* psz<PERSON>ey, char* pszIV, const char* pszBaseSrc, int nBaseSrcLen, char** pszOut);
int AES128Encrypt(const char* pszKey, char* pszIV, const char* pszSrc, int nSrcLen, char** pszDst, int* nDstLen);

int AES128Base64Decrypt(const char* pszKey, char* pszIV, const char* pszBaseSrc, int nBaseSrcLen, char** pszOut);
int AES128Base64Encrypt(const char* pszKey, char* pszIV, const char* pszSrc, int nSrcLen, char** pszOut);

/**
 * @brief  AES文件加密
 *
 * @param  pszFilePath      文件路径
 * @param  key              加密key
 * @param  pszDstFilePath   输出文件字符串
 * @return int              0=成功，其他=失败
 */
int AES128CBCEncryptFile(const char* pszFilePath, const char* key, const char* pszDstFilePath);

/*base64加密对外接口，Add by czw*/
int Base64Encrypt(const char* pszSrc, int nSrcLen, char** pszOut);

#endif
