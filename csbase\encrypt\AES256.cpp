#include <string>
#include <string.h>
#include "AES256.h"
#include "Base64.h"
#include "aes.h"
#include <arpa/inet.h>
#include "AkLogging.h"


#define HTONS   htons
#define NTOHS   ntohs
#define HTONL   htonl
#define NTOHL   ntohl

char AES256_IV[17] = "1234567887654321";

char* strupr(char* str)
{
    char* ptr = str;
    while (*ptr != '\0')
    {
        if (islower(*ptr))
        {
            *ptr = toupper(*ptr);
        }
        ptr++;
    }
    return str;
}

char * GetAppGateIV(char *iv)
{
    if (!iv) {
        return NULL;
    }
    memset(iv, 0, 17);
    snprintf(iv, 17, "%s", AES256_IV);
    return iv;

}

void genKey(char* key, char* keyout, int nsize)
{
    if (key == NULL || keyout == NULL || nsize < KEY_LENGTH)
    {
        return;
    }
    int nSize = strlen(key);
    if (nSize > KEY_LENGTH)
    {
        nSize = KEY_LENGTH;
    }
    ::strncpy(keyout, key, nSize + 1);
    for (int i = nSize; i < KEY_LENGTH; i++)
    {
        keyout[i] = '0';
    }
    return;
}

void AES_256_DECRYPT(unsigned char* in, unsigned char* out, unsigned char* key, int nSize)
{
    if (in == NULL || key == NULL || out == NULL)
    {
        return;
    }
    AES_KEY aes_key;
    unsigned char iv[16];
    char szkey[KEY_LENGTH + 1] = {0};
    memset(iv, 0, sizeof(iv)); //初始化向量
    memset(&aes_key, 0, sizeof(AES_KEY));
    genKey((char*)key, szkey, sizeof(szkey));   //保证秘钥不超过32字节(即aes 的最高级别 256 bit)，若超过则截断
    AES_set_decrypt_key((const unsigned char*)szkey, 256, &aes_key);
    AES_cbc_encrypt(in, out, nSize, &aes_key, iv, AES_DECRYPT);
    //CBC模式对于每个待加密的密码块在加密前会先与前一个密码块的密文异或然后再用加密器加密。第一个明文块与一个叫初始化向量的数据块异或
    // AES_cbc_encrypt允许length不是16(128位)的整数倍，不足的部分会用0填充，输出总是16的整数倍。
}

void AES_256_ENCRYPT(unsigned char* in, unsigned char* out, unsigned char* key, int nSize)
{
    if (in == NULL || key == NULL || out == NULL)
    {
        return;
    }
    AES_KEY aes_key;
    unsigned char iv[16];
    char szkey[KEY_LENGTH + 1] = {0};
    memset(iv, 0, sizeof(iv));
    memset(&aes_key, 0, sizeof(AES_KEY));
    genKey((char*)key, szkey, sizeof(key));
    AES_set_encrypt_key((const unsigned char*)key, 256, &aes_key);
    AES_cbc_encrypt(in, out, nSize, &aes_key, iv, AES_ENCRYPT);
}


int FileAESEncrypt(const char* pszFilePath, const char* pKey, const char* pszDstFilePath)
{
    if (pszFilePath == NULL || pKey == NULL || pszDstFilePath == NULL)
    {
        return -1;
    }
    int nEncipherTextLen = 0;
    unsigned char* key = (unsigned char*)pKey;
    unsigned char* pEncipherText;
    unsigned char* pOriFileBuf;  //定义文件指针
    unsigned char* pEncryptFileBuf;
    int nHeaderLen = sizeof(AES_FILE_HEADER);
    //AK_LOG_INFO << _T("=====header len =====%d", nHeaderLen));
    AES_KEY aes_key;
    unsigned char iv[16];
    memset(iv, 0, sizeof(iv));
    memset(&aes_key, 0, sizeof(AES_KEY));

    FILE* pFile = fopen(pszFilePath, "rb");
    if (NULL == pFile)
    {
        return -1;
    }
    fseek(pFile, 0, SEEK_END); //把指针移动到文件的结尾 ，获取文件长度
    int nOriFileLen = ftell(pFile); //获取文件长度
    int nFillOriFileLen = ((nOriFileLen - 1) / 16 + 1) * 16;

    int nEncryptFileLen = nFillOriFileLen + nHeaderLen;

    nEncipherTextLen = nFillOriFileLen;

    pOriFileBuf = new unsigned char[nEncipherTextLen];
    pEncryptFileBuf = new unsigned char[nEncryptFileLen];
    pEncipherText = new unsigned char[nEncipherTextLen];
    memset(pOriFileBuf, 0, nEncipherTextLen);
    memset(pEncryptFileBuf, 0, nEncryptFileLen);
    memset(pEncipherText, 0, nEncipherTextLen);

    rewind(pFile); //把指针移动到文件开头 因为我们一开始把指针移动到结尾，如果不移动回来 会出错
    fread(pOriFileBuf, 1, nOriFileLen, pFile);
    fclose(pFile);

    //组织文件头数据
    AES_FILE_HEADER* pHeader = (AES_FILE_HEADER*)pEncryptFileBuf;
    pHeader->byMagicMSB = AES_FILE_HEADER_MAGIC_MSB;
    pHeader->byMagicLSB = AES_FILE_HEADER_MAGIC_LSB;
    pHeader->version = HTONS(1);
    pHeader->nFileSize = HTONL(nOriFileLen);

    //加密
    AES_set_encrypt_key(key, 256, &aes_key);
    memset(iv, 0, sizeof(iv));
    AES_cbc_encrypt(pOriFileBuf, pEncipherText, nFillOriFileLen, &aes_key, iv, AES_ENCRYPT);

    memcpy(pEncryptFileBuf + nHeaderLen, pEncipherText, nFillOriFileLen);
    //memcpy(pEncryptFileBuf, pEncipherText, nFillOriFileLen);

    //将文件头和加密后的文件数据写入文件
    FILE* fp = fopen(pszDstFilePath, "wb+");
    if (fp != NULL)
    {
        if ((std::size_t)nEncryptFileLen != fwrite(pEncryptFileBuf, 1, nEncryptFileLen, fp))
        {
            //AK_LOG_INFO << _T("Write error!"));
        }
        fclose(fp);
        fp = NULL;
    }
    delete []pOriFileBuf;
    delete []pEncryptFileBuf;
    delete []pEncipherText;
    return 0;
}

int FileAESDecrypt(const char* pszFilePath, char* pKey, const char* pDstFilePath)
{
    if (pszFilePath == NULL || pKey == NULL || pDstFilePath == NULL)
    {
        return -1;
    }
    unsigned char* pOriFileBuf = NULL;
    unsigned char* pEncryptFileBuf = NULL;
    char* pszKey = pKey;
    int nOriFileLen = 0;
    int nEncryptFileLen = 0;
    int nVersion = 0;
    AES_KEY aes_key;
    unsigned char iv[16];
    int nRet = 0;

    //读取文件数据
    FILE* pFile = fopen(pszFilePath, "rb");
    if (pFile == NULL)
    {
        return -1;
    }

    fseek(pFile, 0, SEEK_END);
    nEncryptFileLen = ftell(pFile);
    pEncryptFileBuf = new unsigned char[nEncryptFileLen];
    rewind(pFile);
    fread(pEncryptFileBuf, 1, nEncryptFileLen, pFile);
    fclose(pFile);

    //判断MAGICSUM
    AES_FILE_HEADER* pHeader = (AES_FILE_HEADER*)pEncryptFileBuf;
    if ((pHeader->byMagicMSB != AES_FILE_HEADER_MAGIC_MSB) || (pHeader->byMagicLSB != AES_FILE_HEADER_MAGIC_LSB))
    {
        nRet = -1;
        goto OpenfileAndDecrypt_Exit;
    }
    //判断version
    nVersion = NTOHS(pHeader->version);
    if (nVersion != 1)
    {
        nRet = -1;
        goto OpenfileAndDecrypt_Exit;
    }
    //获取长度
    nOriFileLen = NTOHL(pHeader->nFileSize);

    //根据version解密对应的数据

    //对收到的数据进行安全性判断
    if (nEncryptFileLen < nOriFileLen || (std::size_t)nEncryptFileLen < sizeof(AES_FILE_HEADER))
    {
        nRet = -1;
        //AK_LOG_INFO << _T(" Receive data error"));
        goto OpenfileAndDecrypt_Exit;
    }
    //pOriFileBuf = new unsigned char[nOriFileLen];// 这个加密完的文件比原始文件大， copy的时候可能导致文件内存溢出， 故申请的内存要是加密完的内存大小
    pOriFileBuf = new unsigned char[nEncryptFileLen - sizeof(AES_FILE_HEADER)];

    memset(&aes_key, 0, sizeof(AES_KEY));
    AES_set_decrypt_key((unsigned char*)pszKey, 256, &aes_key);
    memset(iv, 0, sizeof(iv));
    //nEncryptFileLen-sizeof(AES_FILE_HEADER) 这个加密完的文件比原始文件大， copy的时候可能导致文件内存溢出， 故申请的内存要
    AES_cbc_encrypt(pEncryptFileBuf + sizeof(AES_FILE_HEADER),  pOriFileBuf, nEncryptFileLen - sizeof(AES_FILE_HEADER), &aes_key, iv, AES_DECRYPT);

    //将解密后的文件数据写入文件
    pFile = fopen(pDstFilePath, "wb+");
    if (pFile != NULL)
    {
        if ((std::size_t)nOriFileLen != fwrite(pOriFileBuf, 1, nOriFileLen, pFile))
        {
            //AK_LOG_INFO << _T("Write error!"));
        }
        fclose(pFile);
        pFile = NULL;
    }
OpenfileAndDecrypt_Exit:
    if (pOriFileBuf != NULL)
    {
        delete []pOriFileBuf;
    }
    if (pEncryptFileBuf != NULL)
    {
        delete []pEncryptFileBuf;
    }
    return nRet;
}

/*Pkcs5Padding*/
int AES256_CBC_Encrypt_Padding5(const char *pszKey, char *pszIv, const char *pszSrc, int nDatalen, char **pszDst, int *pnDstlen)
{
    *pszDst = NULL;
    if (!pszKey || !pszIv || !pszSrc || !pnDstlen)
    {
        return -1;
    }
	AES_KEY         key;
	AES_set_encrypt_key((const unsigned char*)pszKey, 256, &key);
	int nPadding;
	int nTotal = (nDatalen / 16 + 1) * 16;
	char *pszEncryptSrc = (char*)malloc(nTotal + 1); 
	if (!pszEncryptSrc) {
		return -2;
	}
	char *pszEncryptOut = (char*)malloc(nTotal + 1);
	if (!pszEncryptOut) {
		free(pszEncryptSrc);
		return -3;
	}
	memset(pszEncryptSrc, 0, nTotal + 1);
	memset(pszEncryptOut, 0, nTotal + 1); 

	if (nDatalen % 16 > 0) {
		nPadding = nTotal - nDatalen;
	} else {
		nPadding = 16;
	}

	memset(pszEncryptSrc, nPadding, nTotal);
	*(pszEncryptSrc +  nTotal) = 0;
	memcpy(pszEncryptSrc, pszSrc, nDatalen);

	*pnDstlen = nTotal;
	AES_cbc_encrypt((unsigned char *)pszEncryptSrc, (unsigned char*)pszEncryptOut, nTotal, &key, (unsigned char*)pszIv, AES_ENCRYPT);
	*pszDst = pszEncryptOut;
	free(pszEncryptSrc);
	return 0;
}

/*Pkcs5Padding*/
int AES256_CBC_Decrypt_Padding5(const char *pszKkey, char *pszIv, const char *pszSrc, int nLen, char **pszDst)
{
    *pszDst = NULL;
    if (!pszIv || !pszKkey || !pszSrc)
    {
        return -1;
    }
    AES_KEY         key;
    AES_set_decrypt_key((const unsigned char*)pszKkey, 256 , &key);

    char *pszDstTemp = (char *)malloc(nLen + 1);
    if(!pszDstTemp)
    {
        return -1;
    }
    memset(pszDstTemp, 0, nLen + 1);
    
    AES_cbc_encrypt((const unsigned char*)pszSrc, (unsigned char*)pszDstTemp, nLen, &key, (unsigned char*)pszIv, AES_DECRYPT);
    unsigned char padding = *(pszDstTemp + strlen(pszDstTemp) -1);
	if (padding > 16 || padding > strlen(pszDstTemp)) {
		return -2;
	}
	if (padding == *(pszDstTemp + strlen(pszDstTemp) - padding)) {
		 *(pszDstTemp + strlen(pszDstTemp) - padding) = 0;
		 *pszDst = pszDstTemp;
	}
	else
	{
        return -3;//padding is error
	}
	return 0;
}

int AES256Base64Decrypt(const char *pszKey, char *pszIV, const char *pszBaseSrc, int nBaseSrcLen, char *pszOut, int OutLen) {
	if (!pszKey || !pszBaseSrc || !pszOut) {
		return -1;
	}

	char *pszBaseOut =  (char *)malloc(nBaseSrcLen + 1);
	if (!pszBaseOut) {
		return 1;
	}
    memset(pszBaseOut, 0, nBaseSrcLen);
    int nBaseOutLen = nBaseSrcLen;
	Base64Decode(pszBaseSrc, nBaseSrcLen,  pszBaseOut, &nBaseOutLen);
    
    char *pszAESOut = NULL;
	AES256_CBC_Decrypt_Padding5(pszKey, pszIV, pszBaseOut, nBaseOutLen, &pszAESOut);
    if (pszAESOut == NULL)
    {
        free(pszBaseOut);
        return 1;
    }
    if (strlen(pszAESOut) >= (std::size_t)OutLen)
    {
        memcpy(pszOut, pszAESOut, OutLen);
    }
    else
    {
        memcpy(pszOut, pszAESOut, strlen(pszAESOut));
    }
    free(pszBaseOut);
    free(pszAESOut);
	return 0;
}

int AES256Base64Encrypt(const char *pszKey, char *pszIV, const char *pszSrc, int nSrcLen, char *pszOut, int out_len)
{
	char *pszEncDst = NULL;
	int nEncDstlen = 0;
	
	AES256_CBC_Encrypt_Padding5(pszKey, pszIV, pszSrc, nSrcLen, &pszEncDst, &nEncDstlen);
	if (!pszEncDst)
	{
        return -1;
	}
    int nBaseoutLen = (nEncDstlen/3 + 1 ) * 4 + 1;
    char *pszBaseout = (char *)malloc(nBaseoutLen);
    if (!pszBaseout)
    {
        free(pszEncDst);
        return -2;
    }
    memset(pszBaseout, 0, nBaseoutLen);
    
	Base64Encode(pszEncDst, nEncDstlen, pszBaseout, &nBaseoutLen);
    if (strlen(pszBaseout) >= (std::size_t)out_len)
    {
        memcpy(pszOut, pszBaseout, out_len);
    }
    else
    {
        memcpy(pszOut, pszBaseout, strlen(pszBaseout));
    }
    free(pszBaseout);
    free(pszEncDst);
	return 0;
}


int LogAESEncrypt(const char *src, int src_len, char *dest, int out_len)
{
    if (src && strlen(src) == 0)
    {
        dest[0] = 0;
        return 0;
    }
    const char *log_aks_key = "mx6pC6U7RFQ8sp38DZMydQJ037yAhVmh";
    char log_aes_iv[17] = "vVhuHEBY4NiccDrj";

    char *enc_data = NULL;
    int enc_data_len = 0;

    AES256_CBC_Encrypt_Padding5(log_aks_key, log_aes_iv, src, src_len, &enc_data, &enc_data_len);
    if (!enc_data)
    {
        return -1;
    }
    int base64_data_len = (enc_data_len/3 + 1 ) * 4 + 1;
    char *base64_data = (char *)malloc(base64_data_len);
    if (!base64_data)
    {
        free(enc_data);
        return -2;
    }
    memset(base64_data, 0, base64_data_len);

    Base64Encode(enc_data, enc_data_len, base64_data, &base64_data_len);
    if (strlen(base64_data) >= (std::size_t)out_len)
    {
        memcpy(dest, base64_data, out_len);
    }
    else
    {
        memcpy(dest, base64_data, strlen(base64_data));
    }
    free(base64_data);
    free(enc_data);
    return 0;
}

