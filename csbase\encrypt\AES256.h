#ifndef __AES_INCLUDED__
#define __AES_INCLUDED__

#define KEY_LENGTH 32
#define AES_KEY_DEFAULT_MAC         "0C11050000FF" // ALARM 这一类消息由于有设备/app跟平台通讯，所以统一用统一MAC进行加密
#define AES_ENCRYPT_KEY_V1           "Akuvox55069013!@Akuvox55069013!@"
#define AES_KEY_DEFAULT_MASK        "Akuvox55069013Akuvox"
#define DEFAULT_CSGATE_KEY_MASK      "Akuvox55069013!@"

void genKey(char* key, char* keyout, int nsize);

typedef struct AES_FILE_HEADER_T
{
#define AES_FILE_HEADER_MAGIC_MSB 0xAA
#define AES_FILE_HEADER_MAGIC_LSB 0xAE
    unsigned char byMagicMSB;
    unsigned char byMagicLSB;
    unsigned short version;
    unsigned int nFileSize;
    unsigned int nReserved1;
    unsigned int nReserved2;
} AES_FILE_HEADER;


char* strupr(char* str);
void AES_256_DECRYPT(unsigned char* in, unsigned char* out, unsigned char* key, int nSize);
void AES_256_ENCRYPT(unsigned char* in, unsigned char* out, unsigned char* key, int nSize);

int OpenfileAndEncrypt(char* pszFilePath);
int OpenfileAndDecrypt(char* pszFilePath);
int FileAESEncrypt(const char* pszFilePath, const char* pKey, const char* pszDstFilePath);
int FileAESDecrypt(const char* pszFilePath, char* pKey, const char* pDstFilePath);

int AES256_CBC_Encrypt_Padding5(const char *pszKey, char *pszIv, const char *pszSrc, int nDatalen, char **pszDst, int *pnDstlen);
int AES256_CBC_Decrypt_Padding5(const char *pszKkey, char *pszIv, const char *pszSrc, int nLen, char **pszDst);
int AES256Base64Decrypt(const char *pszKey, char *pszIV, const char *pszBaseSrc, int nBaseSrcLen, char *pszOut, int OutLen);
int AES256Base64Encrypt(const char *pszKey, char *pszIV, const char *pszSrc, int nSrcLen, char *pszOut, int out_len);
char * GetAppGateIV(char *iv);
int LogAESEncrypt(const char *src, int src_len, char *dest, int out_len);


#endif
