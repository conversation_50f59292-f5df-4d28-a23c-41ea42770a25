#include <stdio.h>
#include <string.h>
#include "Base64.h"
#include <stdlib.h>

static const char *ALPHA_BASE = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
 
char *Base64Encode(const char *pszBuf, const int nSize, char *pszBase64char, int *nLen)
{
	if (!pszBuf || !pszBase64char || !nLen) {
		return NULL;
	}
    int a = 0;
    int i = 0;
    while (i < nSize) {
        char b0 = pszBuf[i++];
        char b1 = (i < nSize) ? pszBuf[i++] : 0;
        char b2 = (i < nSize) ? pszBuf[i++] : 0;
         
        int nInt63 = 0x3F; //  00111111
        int nInt255 = 0xFF; // 11111111
        if (*nLen < a + 4)
        {
            return NULL;// nLen error
        }
        pszBase64char[a++] = ALPHA_BASE[(b0 >> 2) & nInt63];
        pszBase64char[a++] = ALPHA_BASE[((b0 << 4) | ((b1 & nInt255) >> 4)) & nInt63];
        pszBase64char[a++] = ALPHA_BASE[((b1 << 2) | ((b2 & nInt255) >> 6)) & nInt63];
        pszBase64char[a++] = ALPHA_BASE[b2 & nInt63];
    }
    switch (nSize % 3) {
        case 1:
            pszBase64char[--a] = '=';
        case 2:
            pszBase64char[--a] = '=';
    }
    return pszBase64char;
}
 
char *Base64Decode(const char *pszBase64char, const int nSrcLen, char *pszOut, int *nOutLen)
{
    if (!pszBase64char || !pszOut || !nOutLen)
    {
        return NULL;
    }

    int nToint[128] = {-1};
    int i = 0;
    for (i = 0; i < 64; i++) {
        nToint[(unsigned char)ALPHA_BASE[i]] = i;
    }
    int nInt255 = 0xFF;
    int nIndex = 0;
    int nEqualCount = 0;
    if (*(pszBase64char + nSrcLen - 1) == '=') { 
        nEqualCount += 1; 
    } 
    if (*(pszBase64char + nSrcLen - 2) == '=') { 
        nEqualCount += 1; 
    } 
    for (i = 0; i < nSrcLen; i += 4) {
        int c0 = nToint[(unsigned char)pszBase64char[i]];
        int c1 = nToint[(unsigned char)pszBase64char[i + 1]];
        pszOut[nIndex++] = (((c0 << 2) | (c1 >> 4)) & nInt255);
        if (nIndex >= *nOutLen) {
             pszOut[nIndex] = 0;
            *nOutLen = nIndex;
            return pszOut;
        }
        int c2 = nToint[(unsigned char)pszBase64char[i + 2]];
        pszOut[nIndex++] = (((c1 << 4) | (c2 >> 2)) & nInt255);
        if (nIndex >= *nOutLen) {
            pszOut[nIndex] = 0;
            *nOutLen = nIndex;
            return pszOut;
        }
        int c3 = nToint[(unsigned char)pszBase64char[i + 3]];
        pszOut[nIndex++] = (((c2 << 6) | c3) & nInt255);
    }
    pszOut[nIndex - nEqualCount] = 0;
    *nOutLen = nIndex - nEqualCount;
    return pszOut;
}

