#include "Caesar.h"
#include <string.h>

namespace akuvox_encrypt {

    void CaesarEncry(char* str, int num)
    {
        if(nullptr == str)
        {
            return;
        }
        for(unsigned int i=0; i<strlen(str); i++)
        {
            if(str[i] >= 'A' && str[i] <= 'Z')
            {
                str[i] = ((str[i]-'A')+num)%26+'A';
            }
            else if(str[i] >= 'a' && str[i] <= 'z')
            {
                str[i] = ((str[i]-'a')+num)%26+'a';
            }
            else if(str[i] >= '0' && str[i] <= '9')
            {
                str[i] = ((str[i]-'0')+num)%10+'0';
            }
        }
    }
    
    void CaesarDecry(char* str, int num)
    {
        if(nullptr == str)
        {
            return;
        }
        for(unsigned int i=0; i<strlen(str); i++)
        {
            if(str[i] >= 'A' && str[i] <= 'Z')
            {
                str[i] = ((str[i]-'A')+(26-num))%26+'A';
            }
            else if(str[i] >= 'a' && str[i] <= 'z')
            {
                str[i] = ((str[i]-'a')+(26-num))%26+'a';
            }
            else if(str[i] >= '0' && str[i] <= '9')
            {
                str[i] = ((str[i]-'0')+(10-num))%10+'0';
            }
        }
    }

    
    bool AkDeviceCaesarDecry(std::string& str)
    {
        if(str.size() != CONFUSE_MAC_LENGTH)
        {
            return false;
        }
        
        char user[CONFUSE_MAC_LENGTH+1];
        snprintf(user, sizeof(user), "%s", str.c_str());
        
        //首个字节代表凯撒偏移几位
        char ch = str[0];
        int num = 0; 
        //将字符转化成相应的数字
        if((ch>='0')&&(ch<='9'))
        {
            num = ch-48;
        }
        else
        {
            return false;
        }

        char mac[13] = {0};   //mac总共12位;密文为mac凯撒加密后拆分成三段后 插入 另外三段随机值
        //去除随机值
        strncat(mac, user+4, 4);
        strncat(mac, user+12, 4);
        strncat(mac, user+20, 4);

        CaesarDecry(mac, num);
        str = mac;
        return true;
    }

}
