#ifndef _AK_CAESAR_H
#define _AK_CAESAR_H
#include <string>

/*
标准的凯撒加密只对字母进行偏移，由于我们有手机号作为用户名（纯数字），故对凯撒加密进行拓展，约定为
整体偏移3位，
即A->D、B->E、Z->C 
a->d、b->e、z->c 
0->3、1->4、9->2	(对数字也进行凯撒偏移)
*/

namespace akuvox_encrypt {

#define DEFAULT_CAESAR_NUM 3
#define CONFUSE_MAC_LENGTH 24   //混淆后的mac长度

    void CaesarEncry(char* str, int num = DEFAULT_CAESAR_NUM);
    void CaesarDecry(char* str, int num = DEFAULT_CAESAR_NUM);
    bool AkDeviceCaesarDecry(std::string& str);
}
#endif 

