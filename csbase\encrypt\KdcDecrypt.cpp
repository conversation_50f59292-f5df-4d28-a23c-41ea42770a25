#include <KdcDecrypt.h>
#include <cstdio>  
#include <iostream>
#include <memory>   
#include "AkLogging.h"
namespace akuvox_encrypt {

std::string KdcDecrypt(const std::string& encryptedStr) {
    // 构造命令
    std::string command = "echo '" + encryptedStr + "' | /bin/crypto -d";

    // 打开管道以读取命令的输出
    FILE* pipe = popen(command.c_str(), "r");
    if (!pipe) {
        AK_LOG_WARN << "Failed to open pipe! Make sure 'crypto' is available.";
    }

    // 使用智能指针管理管道关闭
    std::unique_ptr<FILE, decltype(&pclose)> pipeGuard(pipe, pclose);

    // 读取命令的输出
    char buffer[128];
    std::string result;
    while (fgets(buffer, sizeof(buffer), pipe) != nullptr) {
        result += buffer;
    }

    // 去除末尾的换行符
    if (!result.empty() && result.back() == '\n') {
        result.pop_back();
    }
    return result;
}
}