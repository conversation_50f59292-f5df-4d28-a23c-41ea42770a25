/**
 * @file md5.h
 * @The header file of md5.
 * <AUTHOR>
 * @mail <EMAIL>
 * @github https://github.com/JieweiWei
 * @data Oct 19 2014
 *
 */ 

#ifndef MD5_H
#define MD5_H


/* Parameters of MD5. */
#define s11 7
#define s12 12
#define s13 17
#define s14 22
#define s21 5
#define s22 9
#define s23 14
#define s24 20
#define s31 4
#define s32 11
#define s33 16
#define s34 23
#define s41 6
#define s42 10
#define s43 15
#define s44 21
#define _T(str) str

/**
 * @Basic MD5 functions.
 *
 * @param there bit32.
 *
 * @return one bit32.
 */
#define FFF(x, y, z) (((x) & (y)) | ((~x) & (z)))
#define G(x, y, z) (((x) & (z)) | ((y) & (~z)))
#define H(x, y, z) ((x) ^ (y) ^ (z))
#define I(x, y, z) ((y) ^ ((x) | (~z)))

/**
 * @Rotate Left.
 *
 * @param {num} the raw number.
 *
 * @param {n} rotate left n.
 *
 * @return the number after rotated left.
 */
#define ROTATELEFT(num, n) (((num) << (n)) | ((num) >> (32-(n))))

/**
 * @Transformations for rounds 1, 2, 3, and 4.
 */
#define FF(a, b, c, d, x, s, ac) { \
  (a) += FFF ((b), (c), (d)) + (x) + ac; \
  (a) = ROTATELEFT ((a), (s)); \
  (a) += (b); \
}
#define GG(a, b, c, d, x, s, ac) { \
  (a) += G ((b), (c), (d)) + (x) + ac; \
  (a) = ROTATELEFT ((a), (s)); \
  (a) += (b); \
}
#define HH(a, b, c, d, x, s, ac) { \
  (a) += H ((b), (c), (d)) + (x) + ac; \
  (a) = ROTATELEFT ((a), (s)); \
  (a) += (b); \
}
#define II(a, b, c, d, x, s, ac) { \
  (a) += I ((b), (c), (d)) + (x) + ac; \
  (a) = ROTATELEFT ((a), (s)); \
  (a) += (b); \
}

#include <string>


namespace akuvox_encrypt {
/* Define of btye.*/
typedef unsigned char Byte;
/* Define of byte. */
typedef unsigned int bit32;
typedef unsigned UNSIGNED32;

//MD5摘要值结构体
typedef struct MD5VAL_STRUCT
{
    unsigned int a;
    unsigned int b;
    unsigned int c;
    unsigned int d;
} MD5VAL;

typedef struct MD5Context
{
    UNSIGNED32 state[4];  /* state (ABCD) */
    UNSIGNED32 count[2];  /* number of bits, modulo 2^64 (lsb first) */
    unsigned char buffer[64]; /* input buffer */
} MD5_CTX;

class MD5 {
public:
  /* Construct a MD5 object with a string. */
  MD5(const std::string& message);
  MD5(const Byte* input, size_t len);

  /* Generate md5 digest. */
  const Byte* getDigest();

  /* Convert digest to string value */
  std::string toStr();
  
  /* Md5 Binary and Base64 Encode , Add by czw*/
  char* GetBase64Md5();

   //MD5文件摘要
   static std::string GetFileMD5(const std::string &path);

private:
   static MD5VAL md5File(FILE* fpin);
   static unsigned int conv(unsigned int a);
    
  /* Initialization the md5 object, processing another message block,
   * and updating the context.*/
  void init(const Byte* input, size_t len);

  /* MD5 basic transformation. Transforms state based on block. */
  void transform(const Byte block[64]);

  /* Encodes input (usigned long) into output (byte). */
  void encode(const bit32* input, Byte* output, size_t length);

  /* Decodes input (byte) into output (usigned long). */
  void decode(const Byte* input, bit32* output, size_t length);

private:
  /* Flag for mark whether calculate finished. */
  bool finished;

	/* state (ABCD). */
  bit32 state[4];

  /* number of bits, low-order word first. */
  bit32 count[2];

  /* input buffer. */
  Byte buffer[64];

  /* message digest. */
  Byte digest[16];

	/* padding for calculate. */
  static const Byte PADDING[64];

  /* Hex numbers. */
  static const char HEX_NUMBERS[16];
};
}
#endif // MD5_H
