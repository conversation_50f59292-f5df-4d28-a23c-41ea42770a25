#ifndef __ETCD_CLIENT_HPP__
#define __ETCD_CLIENT_HPP__

#include "etcd/Response.hpp"
#include "v3/include/Transaction.hpp"
#include "v3/include/AsyncTxnResponse.hpp"
#include "v3/include/Action.hpp"
#include <string>
#include <tuple>
#include <vector>
#include <map>
#include <mutex>
#include <grpc++/grpc++.h>
#include "proto/rpc.grpc.pb.h"
#include "grpc_balancer_service.h"

using etcdserverpb::KV;
using etcdserverpb::Watch;
using etcdserverpb::Lease;
using grpc::Channel;

namespace etcd
{
  /**
   * Client is responsible for maintaining a connection towards an etcd server.
   * Etcd operations can be reached via the methods of the client.
   * added by chenyc,2022.02.17,后面内部DNS建设完毕了,这里要填写etcd集群节点的域名,且支持健康检查与
   * 支持addresss的动态调整
   */
  class Client
  {
  public:
    typedef std::shared_ptr<KV::Stub> KVStubPtr;
	typedef std::shared_ptr<Watch::Stub> WatchStubPtr;
	typedef std::shared_ptr<Lease::Stub> LeaseStubPtr;

  public:
    /**
     * Constructs an etcd client object.
     * @param addresss is the list urls of the etcd server to connect to
     */
    Client(const std::vector<std::string>& addresss);

    /**
     * update etcd endpoints
     * @param addresss is the list urls of the etcd server to update
     */
    int update_endpoints(const std::vector<std::string>& addrs);

    /**
     * Sends a get request to the etcd server
     * @param key is the key to be read
     */
    pplx::task<Response> get(std::string const & key);

    /**
     * Sets the value of a key. The key will be modified if already exists or created
     * if it does not exists.
     * @param key is the key to be created or modified
     * @param value is the new value to be set
     */
    pplx::task<Response> set(std::string const & key, std::string const & value, int ttl = 0);

    /**
     * Sets the value of a key. The key will be modified if already exists or created
     * if it does not exists.
     * @param key is the key to be created or modified
     * @param value is the new value to be set
     * @param leaseId is the lease attached to the key
     */
    pplx::task<Response> set(std::string const & key, std::string const & value, int64_t leaseId);


    /**
     * Creates a new key and sets it's value. Fails if the key already exists.
     * @param key is the key to be created
     * @param value is the value to be set
     */
    pplx::task<Response> add(std::string const & key, std::string const & value, int ttl = 0);

    /**
     * Creates a new key and sets it's value. Fails if the key already exists.
     * @param key is the key to be created
     * @param value is the value to be set
     * @param leaseId is the lease attached to the key
     */
    pplx::task<Response> add(std::string const & key, std::string const & value, int64_t leaseId);

    /**
     * Modifies an existing key. Fails if the key does not exists.
     * @param key is the key to be modified
     * @param value is the new value to be set
     */
    pplx::task<Response> modify(std::string const & key, std::string const & value, int ttl = 0);

    /**
     * Modifies an existing key. Fails if the key does not exists.
     * @param key is the key to be modified
     * @param value is the new value to be set
     * @param leaseId is the lease attached to the key
     */
    pplx::task<Response> modify(std::string const & key, std::string const & value, int64_t leaseId);

    /**
     * Modifies an existing key only if it has a specific value. Fails if the key does not exists
     * or the original value differs from the expected one.
     * @param key is the key to be modified
     * @param value is the new value to be set
     * @param old_value is the value to be replaced
     */
    pplx::task<Response> modify_if(std::string const & key, std::string const & value, std::string const & old_value, int ttl = 0);

    /**
     * Modifies an existing key only if it has a specific value. Fails if the key does not exists
     * or the original value differs from the expected one.
     * @param key is the key to be modified
     * @param value is the new value to be set
     * @param old_value is the value to be replaced
     * @param leaseId is the lease attached to the key
     */
    pplx::task<Response> modify_if(std::string const & key, std::string const & value, std::string const & old_value, int64_t leaseId);

    /**
     * Modifies an existing key only if it has a specific modification index value. Fails if the key
     * does not exists or the modification index of the previous value differs from the expected one.
     * @param key is the key to be modified
     * @param value is the new value to be set
     * @param old_index is the expected index of the original value
     */
    pplx::task<Response> modify_if(std::string const & key, std::string const & value, int old_index, int ttl = 0);

    /**
     * Modifies an existing key only if it has a specific modification index value. Fails if the key
     * does not exists or the modification index of the previous value differs from the expected one.
     * @param key is the key to be modified
     * @param value is the new value to be set
     * @param old_index is the expected index of the original value
     * @param leaseId is the lease attached to the key
     */
    pplx::task<Response> modify_if(std::string const & key, std::string const & value, int old_index, int64_t leaseId);

    /**
     * Removes a single key. The key has to point to a plain, non directory entry.
     * @param key is the key to be deleted
     */
    pplx::task<Response> rm(std::string const & key);

    /**
     * Removes a single key but only if it has a specific value. Fails if the key does not exists
     * or the its value differs from the expected one.
     * @param key is the key to be deleted
     */
    pplx::task<Response> rm_if(std::string const & key, std::string const & old_value);

    /**
     * Removes an existing key only if it has a specific modification index value. Fails if the key
     * does not exists or the modification index of it differs from the expected one.
     * @param key is the key to be deleted
     * @param old_index is the expected index of the existing value
     */
    pplx::task<Response> rm_if(std::string const & key, int old_index);

    /**
     * Gets a directory listing of the directory identified by the key.
     * @param key is the key to be listed
     */
    pplx::task<Response> ls(std::string const & key);


    /**
     * Removes a directory node. Fails if the parent directory dos not exists or not a directory.
     * @param key is the directory to be created to be listed
     * @param recursive if true then delete a whole subtree, otherwise deletes only an empty directory.
     */
    pplx::task<Response> rmdir(std::string const & key, bool recursive = false);

    /**
     * Watches for changes of a key or a subtree. Please note that if you watch e.g. "/testdir" and
     * a new key is created, like "/testdir/newkey" then no change happened in the value of
     * "/testdir" so your watch will not detect this. If you want to detect addition and deletion of
     * directory entries then you have to do a recursive watch.
     * @param key is the value or directory to be watched
     * @param recursive if true watch a whole subtree
     * 同步阻塞的watch
     */
    pplx::task<Response> watch(std::string const & key, bool recursive = false);

    /**
     * Watches for changes of a key or a subtree from a specific index. The index value can be in the "past".
     * @param key is the value or directory to be watched
     * @param fromIndex the first index we are interested in
     * @param recursive if true watch a whole subtree
     * 同步阻塞的watch
     */
    pplx::task<Response> watch(std::string const & key, int fromIndex, bool recursive = false);

    /**
     * Grants a lease.
     * @param ttl is the time to live of the lease
     */
    pplx::task<Response> leasegrant(int ttl);

    //added by cyc, 2018-11-20
    /**
     * Revokes a lease.
     * @param id is the id to live of the lease
     */
    pplx::task<Response> leaserevoke(int64_t id);
    /**
     * KeepAlive a lease.
     * @param id is the id to live of the lease
     */
    pplx::task<Response> leasekeepalive(int64_t id);
	
    //added by cyc, 2019-10-23,故障节点切换;
    //added by cyc, 2022.02.18,目前已经具备原故障通道自动的重试策略与恢复能力，但是需要调用:channel->GetState(true) 才会触发channel的重连机制;
	void node_failover();

    const std::string get_healthy_node_uri() const;

      void SetUp();
      grpc_lb_addresses* CreateLbAddressesFromAddressDataList(const std::vector<AddressData>& address_data);
      void SetNextResolution(const std::vector<AddressData>& address_data);
      void SetNextReresolutionResponse(const std::vector<AddressData>& address_data);
      void ResetStub(int fallback_timeout = 0, const grpc::string& expected_targets = "") ;
      void EtcdSrvConnUpdate(const std::vector<std::string>& etcd_addrs);

  private:
	  std::map<std::string/*etcd endpoint,format:ip:port*/, std::shared_ptr<Channel> /*stub's channel*/> etcd_endpoints_channels_;
	  std::map<std::string/*etcd endpoint*/, std::tuple<KVStubPtr,WatchStubPtr,LeaseStubPtr>> etcd_endpoints_stubs_;
	  //当前使用的etcd节点,当发生故障时,节点会自动切换到etcd_endpoints_stubs_的任意健康节点.
	  KVStubPtr kv_stub_;
	  WatchStubPtr watch_stub_;
	  LeaseStubPtr lease_stub_;
	  std::mutex stub_mutex_; //保护stubs,三个公用一把锁即可
	  std::vector<std::string> addrs_;
      std::string etcd_healthy_node_uri_;

      //不启动外部负载均衡模块
      //std::vector<std::unique_ptr<BalancerServiceImpl>> balancers_;
      //std::vector<ServerThread<BalancerService>> balancer_servers_;
      grpc_core::RefCountedPtr<grpc_core::FakeResolverResponseGenerator> response_generator_;
      grpc::string kApplicationTargetName_ = "etcdcli";      
      std::shared_ptr<Channel> channel_;
};

}

#endif
