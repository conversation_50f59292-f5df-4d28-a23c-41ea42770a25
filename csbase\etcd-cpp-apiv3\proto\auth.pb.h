// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: auth.proto

#ifndef PROTOBUF_auth_2eproto__INCLUDED
#define PROTOBUF_auth_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3005001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace protobuf_auth_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[3];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
void InitDefaultsUserImpl();
void InitDefaultsUser();
void InitDefaultsPermissionImpl();
void InitDefaultsPermission();
void InitDefaultsRoleImpl();
void InitDefaultsRole();
inline void InitDefaults() {
  InitDefaultsUser();
  InitDefaultsPermission();
  InitDefaultsRole();
}
}  // namespace protobuf_auth_2eproto
namespace authpb {
class Permission;
class PermissionDefaultTypeInternal;
extern PermissionDefaultTypeInternal _Permission_default_instance_;
class Role;
class RoleDefaultTypeInternal;
extern RoleDefaultTypeInternal _Role_default_instance_;
class User;
class UserDefaultTypeInternal;
extern UserDefaultTypeInternal _User_default_instance_;
}  // namespace authpb
namespace authpb {

enum Permission_Type {
  Permission_Type_READ = 0,
  Permission_Type_WRITE = 1,
  Permission_Type_READWRITE = 2,
  Permission_Type_Permission_Type_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  Permission_Type_Permission_Type_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool Permission_Type_IsValid(int value);
const Permission_Type Permission_Type_Type_MIN = Permission_Type_READ;
const Permission_Type Permission_Type_Type_MAX = Permission_Type_READWRITE;
const int Permission_Type_Type_ARRAYSIZE = Permission_Type_Type_MAX + 1;

const ::google::protobuf::EnumDescriptor* Permission_Type_descriptor();
inline const ::std::string& Permission_Type_Name(Permission_Type value) {
  return ::google::protobuf::internal::NameOfEnum(
    Permission_Type_descriptor(), value);
}
inline bool Permission_Type_Parse(
    const ::std::string& name, Permission_Type* value) {
  return ::google::protobuf::internal::ParseNamedEnum<Permission_Type>(
    Permission_Type_descriptor(), name, value);
}
// ===================================================================

class User : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:authpb.User) */ {
 public:
  User();
  virtual ~User();

  User(const User& from);

  inline User& operator=(const User& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  User(User&& from) noexcept
    : User() {
    *this = ::std::move(from);
  }

  inline User& operator=(User&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const User& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const User* internal_default_instance() {
    return reinterpret_cast<const User*>(
               &_User_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    0;

  void Swap(User* other);
  friend void swap(User& a, User& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline User* New() const PROTOBUF_FINAL { return New(NULL); }

  User* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const User& from);
  void MergeFrom(const User& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(User* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string roles = 3;
  int roles_size() const;
  void clear_roles();
  static const int kRolesFieldNumber = 3;
  const ::std::string& roles(int index) const;
  ::std::string* mutable_roles(int index);
  void set_roles(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_roles(int index, ::std::string&& value);
  #endif
  void set_roles(int index, const char* value);
  void set_roles(int index, const char* value, size_t size);
  ::std::string* add_roles();
  void add_roles(const ::std::string& value);
  #if LANG_CXX11
  void add_roles(::std::string&& value);
  #endif
  void add_roles(const char* value);
  void add_roles(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& roles() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_roles();

  // bytes name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const void* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // bytes password = 2;
  void clear_password();
  static const int kPasswordFieldNumber = 2;
  const ::std::string& password() const;
  void set_password(const ::std::string& value);
  #if LANG_CXX11
  void set_password(::std::string&& value);
  #endif
  void set_password(const char* value);
  void set_password(const void* value, size_t size);
  ::std::string* mutable_password();
  ::std::string* release_password();
  void set_allocated_password(::std::string* password);

  // @@protoc_insertion_point(class_scope:authpb.User)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::std::string> roles_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::internal::ArenaStringPtr password_;
  mutable int _cached_size_;
  friend struct ::protobuf_auth_2eproto::TableStruct;
  friend void ::protobuf_auth_2eproto::InitDefaultsUserImpl();
};
// -------------------------------------------------------------------

class Permission : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:authpb.Permission) */ {
 public:
  Permission();
  virtual ~Permission();

  Permission(const Permission& from);

  inline Permission& operator=(const Permission& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Permission(Permission&& from) noexcept
    : Permission() {
    *this = ::std::move(from);
  }

  inline Permission& operator=(Permission&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Permission& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Permission* internal_default_instance() {
    return reinterpret_cast<const Permission*>(
               &_Permission_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    1;

  void Swap(Permission* other);
  friend void swap(Permission& a, Permission& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Permission* New() const PROTOBUF_FINAL { return New(NULL); }

  Permission* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const Permission& from);
  void MergeFrom(const Permission& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(Permission* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  typedef Permission_Type Type;
  static const Type READ =
    Permission_Type_READ;
  static const Type WRITE =
    Permission_Type_WRITE;
  static const Type READWRITE =
    Permission_Type_READWRITE;
  static inline bool Type_IsValid(int value) {
    return Permission_Type_IsValid(value);
  }
  static const Type Type_MIN =
    Permission_Type_Type_MIN;
  static const Type Type_MAX =
    Permission_Type_Type_MAX;
  static const int Type_ARRAYSIZE =
    Permission_Type_Type_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  Type_descriptor() {
    return Permission_Type_descriptor();
  }
  static inline const ::std::string& Type_Name(Type value) {
    return Permission_Type_Name(value);
  }
  static inline bool Type_Parse(const ::std::string& name,
      Type* value) {
    return Permission_Type_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // bytes key = 1;
  void clear_key();
  static const int kKeyFieldNumber = 1;
  const ::std::string& key() const;
  void set_key(const ::std::string& value);
  #if LANG_CXX11
  void set_key(::std::string&& value);
  #endif
  void set_key(const char* value);
  void set_key(const void* value, size_t size);
  ::std::string* mutable_key();
  ::std::string* release_key();
  void set_allocated_key(::std::string* key);

  // .authpb.Permission.Type permType = 2;
  void clear_permtype();
  static const int kPermTypeFieldNumber = 2;
  ::authpb::Permission_Type permtype() const;
  void set_permtype(::authpb::Permission_Type value);

  // @@protoc_insertion_point(class_scope:authpb.Permission)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr key_;
  int permtype_;
  mutable int _cached_size_;
  friend struct ::protobuf_auth_2eproto::TableStruct;
  friend void ::protobuf_auth_2eproto::InitDefaultsPermissionImpl();
};
// -------------------------------------------------------------------

class Role : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:authpb.Role) */ {
 public:
  Role();
  virtual ~Role();

  Role(const Role& from);

  inline Role& operator=(const Role& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Role(Role&& from) noexcept
    : Role() {
    *this = ::std::move(from);
  }

  inline Role& operator=(Role&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Role& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Role* internal_default_instance() {
    return reinterpret_cast<const Role*>(
               &_Role_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    2;

  void Swap(Role* other);
  friend void swap(Role& a, Role& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Role* New() const PROTOBUF_FINAL { return New(NULL); }

  Role* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const Role& from);
  void MergeFrom(const Role& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(Role* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .authpb.Permission keyPermission = 2;
  int keypermission_size() const;
  void clear_keypermission();
  static const int kKeyPermissionFieldNumber = 2;
  const ::authpb::Permission& keypermission(int index) const;
  ::authpb::Permission* mutable_keypermission(int index);
  ::authpb::Permission* add_keypermission();
  ::google::protobuf::RepeatedPtrField< ::authpb::Permission >*
      mutable_keypermission();
  const ::google::protobuf::RepeatedPtrField< ::authpb::Permission >&
      keypermission() const;

  // bytes name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const void* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // @@protoc_insertion_point(class_scope:authpb.Role)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::authpb::Permission > keypermission_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  mutable int _cached_size_;
  friend struct ::protobuf_auth_2eproto::TableStruct;
  friend void ::protobuf_auth_2eproto::InitDefaultsRoleImpl();
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// User

// bytes name = 1;
inline void User::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& User::name() const {
  // @@protoc_insertion_point(field_get:authpb.User.name)
  return name_.GetNoArena();
}
inline void User::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:authpb.User.name)
}
#if LANG_CXX11
inline void User::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:authpb.User.name)
}
#endif
inline void User::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:authpb.User.name)
}
inline void User::set_name(const void* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:authpb.User.name)
}
inline ::std::string* User::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:authpb.User.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* User::release_name() {
  // @@protoc_insertion_point(field_release:authpb.User.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void User::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:authpb.User.name)
}

// bytes password = 2;
inline void User::clear_password() {
  password_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& User::password() const {
  // @@protoc_insertion_point(field_get:authpb.User.password)
  return password_.GetNoArena();
}
inline void User::set_password(const ::std::string& value) {
  
  password_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:authpb.User.password)
}
#if LANG_CXX11
inline void User::set_password(::std::string&& value) {
  
  password_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:authpb.User.password)
}
#endif
inline void User::set_password(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  password_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:authpb.User.password)
}
inline void User::set_password(const void* value, size_t size) {
  
  password_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:authpb.User.password)
}
inline ::std::string* User::mutable_password() {
  
  // @@protoc_insertion_point(field_mutable:authpb.User.password)
  return password_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* User::release_password() {
  // @@protoc_insertion_point(field_release:authpb.User.password)
  
  return password_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void User::set_allocated_password(::std::string* password) {
  if (password != NULL) {
    
  } else {
    
  }
  password_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), password);
  // @@protoc_insertion_point(field_set_allocated:authpb.User.password)
}

// repeated string roles = 3;
inline int User::roles_size() const {
  return roles_.size();
}
inline void User::clear_roles() {
  roles_.Clear();
}
inline const ::std::string& User::roles(int index) const {
  // @@protoc_insertion_point(field_get:authpb.User.roles)
  return roles_.Get(index);
}
inline ::std::string* User::mutable_roles(int index) {
  // @@protoc_insertion_point(field_mutable:authpb.User.roles)
  return roles_.Mutable(index);
}
inline void User::set_roles(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:authpb.User.roles)
  roles_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void User::set_roles(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:authpb.User.roles)
  roles_.Mutable(index)->assign(std::move(value));
}
#endif
inline void User::set_roles(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  roles_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:authpb.User.roles)
}
inline void User::set_roles(int index, const char* value, size_t size) {
  roles_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:authpb.User.roles)
}
inline ::std::string* User::add_roles() {
  // @@protoc_insertion_point(field_add_mutable:authpb.User.roles)
  return roles_.Add();
}
inline void User::add_roles(const ::std::string& value) {
  roles_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:authpb.User.roles)
}
#if LANG_CXX11
inline void User::add_roles(::std::string&& value) {
  roles_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:authpb.User.roles)
}
#endif
inline void User::add_roles(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  roles_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:authpb.User.roles)
}
inline void User::add_roles(const char* value, size_t size) {
  roles_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:authpb.User.roles)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
User::roles() const {
  // @@protoc_insertion_point(field_list:authpb.User.roles)
  return roles_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
User::mutable_roles() {
  // @@protoc_insertion_point(field_mutable_list:authpb.User.roles)
  return &roles_;
}

// -------------------------------------------------------------------

// Permission

// bytes key = 1;
inline void Permission::clear_key() {
  key_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Permission::key() const {
  // @@protoc_insertion_point(field_get:authpb.Permission.key)
  return key_.GetNoArena();
}
inline void Permission::set_key(const ::std::string& value) {
  
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:authpb.Permission.key)
}
#if LANG_CXX11
inline void Permission::set_key(::std::string&& value) {
  
  key_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:authpb.Permission.key)
}
#endif
inline void Permission::set_key(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:authpb.Permission.key)
}
inline void Permission::set_key(const void* value, size_t size) {
  
  key_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:authpb.Permission.key)
}
inline ::std::string* Permission::mutable_key() {
  
  // @@protoc_insertion_point(field_mutable:authpb.Permission.key)
  return key_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Permission::release_key() {
  // @@protoc_insertion_point(field_release:authpb.Permission.key)
  
  return key_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Permission::set_allocated_key(::std::string* key) {
  if (key != NULL) {
    
  } else {
    
  }
  key_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), key);
  // @@protoc_insertion_point(field_set_allocated:authpb.Permission.key)
}

// .authpb.Permission.Type permType = 2;
inline void Permission::clear_permtype() {
  permtype_ = 0;
}
inline ::authpb::Permission_Type Permission::permtype() const {
  // @@protoc_insertion_point(field_get:authpb.Permission.permType)
  return static_cast< ::authpb::Permission_Type >(permtype_);
}
inline void Permission::set_permtype(::authpb::Permission_Type value) {
  
  permtype_ = value;
  // @@protoc_insertion_point(field_set:authpb.Permission.permType)
}

// -------------------------------------------------------------------

// Role

// bytes name = 1;
inline void Role::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Role::name() const {
  // @@protoc_insertion_point(field_get:authpb.Role.name)
  return name_.GetNoArena();
}
inline void Role::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:authpb.Role.name)
}
#if LANG_CXX11
inline void Role::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:authpb.Role.name)
}
#endif
inline void Role::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:authpb.Role.name)
}
inline void Role::set_name(const void* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:authpb.Role.name)
}
inline ::std::string* Role::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:authpb.Role.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Role::release_name() {
  // @@protoc_insertion_point(field_release:authpb.Role.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Role::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:authpb.Role.name)
}

// repeated .authpb.Permission keyPermission = 2;
inline int Role::keypermission_size() const {
  return keypermission_.size();
}
inline void Role::clear_keypermission() {
  keypermission_.Clear();
}
inline const ::authpb::Permission& Role::keypermission(int index) const {
  // @@protoc_insertion_point(field_get:authpb.Role.keyPermission)
  return keypermission_.Get(index);
}
inline ::authpb::Permission* Role::mutable_keypermission(int index) {
  // @@protoc_insertion_point(field_mutable:authpb.Role.keyPermission)
  return keypermission_.Mutable(index);
}
inline ::authpb::Permission* Role::add_keypermission() {
  // @@protoc_insertion_point(field_add:authpb.Role.keyPermission)
  return keypermission_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::authpb::Permission >*
Role::mutable_keypermission() {
  // @@protoc_insertion_point(field_mutable_list:authpb.Role.keyPermission)
  return &keypermission_;
}
inline const ::google::protobuf::RepeatedPtrField< ::authpb::Permission >&
Role::keypermission() const {
  // @@protoc_insertion_point(field_list:authpb.Role.keyPermission)
  return keypermission_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace authpb

namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::authpb::Permission_Type> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::authpb::Permission_Type>() {
  return ::authpb::Permission_Type_descriptor();
}

}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_auth_2eproto__INCLUDED
