// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: etcdserver.proto

#include "etcdserver.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)
namespace etcdserverpb {
class RequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Request>
      _instance;
} _Request_default_instance_;
class MetadataDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Metadata>
      _instance;
} _Metadata_default_instance_;
}  // namespace etcdserverpb
namespace protobuf_etcdserver_2eproto {
void InitDefaultsRequestImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::etcdserverpb::_Request_default_instance_;
    new (ptr) ::etcdserverpb::Request();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::etcdserverpb::Request::InitAsDefaultInstance();
}

void InitDefaultsRequest() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsRequestImpl);
}

void InitDefaultsMetadataImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::etcdserverpb::_Metadata_default_instance_;
    new (ptr) ::etcdserverpb::Metadata();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::etcdserverpb::Metadata::InitAsDefaultInstance();
}

void InitDefaultsMetadata() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsMetadataImpl);
}

::google::protobuf::Metadata file_level_metadata[2];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::etcdserverpb::Request, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::etcdserverpb::Request, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::etcdserverpb::Request, id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::etcdserverpb::Request, method_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::etcdserverpb::Request, path_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::etcdserverpb::Request, val_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::etcdserverpb::Request, dir_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::etcdserverpb::Request, prevvalue_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::etcdserverpb::Request, previndex_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::etcdserverpb::Request, prevexist_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::etcdserverpb::Request, expiration_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::etcdserverpb::Request, wait_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::etcdserverpb::Request, since_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::etcdserverpb::Request, recursive_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::etcdserverpb::Request, sorted_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::etcdserverpb::Request, quorum_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::etcdserverpb::Request, time_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::etcdserverpb::Request, stream_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::etcdserverpb::Request, refresh_),
  4,
  0,
  1,
  2,
  8,
  3,
  5,
  9,
  6,
  10,
  7,
  11,
  12,
  13,
  16,
  14,
  15,
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::etcdserverpb::Metadata, _has_bits_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::etcdserverpb::Metadata, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::etcdserverpb::Metadata, nodeid_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::etcdserverpb::Metadata, clusterid_),
  0,
  1,
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, 22, sizeof(::etcdserverpb::Request)},
  { 39, 46, sizeof(::etcdserverpb::Metadata)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::etcdserverpb::_Request_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::etcdserverpb::_Metadata_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  ::google::protobuf::MessageFactory* factory = NULL;
  AssignDescriptors(
      "etcdserver.proto", schemas, file_default_instances, TableStruct::offsets, factory,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 2);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n\020etcdserver.proto\022\014etcdserverpb\"\231\002\n\007Req"
      "uest\022\n\n\002ID\030\001 \001(\004\022\016\n\006Method\030\002 \001(\t\022\014\n\004Path"
      "\030\003 \001(\t\022\013\n\003Val\030\004 \001(\t\022\013\n\003Dir\030\005 \001(\010\022\021\n\tPrev"
      "Value\030\006 \001(\t\022\021\n\tPrevIndex\030\007 \001(\004\022\021\n\tPrevEx"
      "ist\030\010 \001(\010\022\022\n\nExpiration\030\t \001(\003\022\014\n\004Wait\030\n "
      "\001(\010\022\r\n\005Since\030\013 \001(\004\022\021\n\tRecursive\030\014 \001(\010\022\016\n"
      "\006Sorted\030\r \001(\010\022\016\n\006Quorum\030\016 \001(\010\022\014\n\004Time\030\017 "
      "\001(\003\022\016\n\006Stream\030\020 \001(\010\022\017\n\007Refresh\030\021 \001(\010\"-\n\010"
      "Metadata\022\016\n\006NodeID\030\001 \001(\004\022\021\n\tClusterID\030\002 "
      "\001(\004"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 363);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "etcdserver.proto", &protobuf_RegisterTypes);
}

void AddDescriptors() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_etcdserver_2eproto
namespace etcdserverpb {

// ===================================================================

void Request::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Request::kIDFieldNumber;
const int Request::kMethodFieldNumber;
const int Request::kPathFieldNumber;
const int Request::kValFieldNumber;
const int Request::kDirFieldNumber;
const int Request::kPrevValueFieldNumber;
const int Request::kPrevIndexFieldNumber;
const int Request::kPrevExistFieldNumber;
const int Request::kExpirationFieldNumber;
const int Request::kWaitFieldNumber;
const int Request::kSinceFieldNumber;
const int Request::kRecursiveFieldNumber;
const int Request::kSortedFieldNumber;
const int Request::kQuorumFieldNumber;
const int Request::kTimeFieldNumber;
const int Request::kStreamFieldNumber;
const int Request::kRefreshFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Request::Request()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_etcdserver_2eproto::InitDefaultsRequest();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:etcdserverpb.Request)
}
Request::Request(const Request& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _has_bits_(from._has_bits_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  method_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.has_method()) {
    method_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.method_);
  }
  path_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.has_path()) {
    path_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.path_);
  }
  val_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.has_val()) {
    val_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.val_);
  }
  prevvalue_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.has_prevvalue()) {
    prevvalue_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.prevvalue_);
  }
  ::memcpy(&id_, &from.id_,
    static_cast<size_t>(reinterpret_cast<char*>(&time_) -
    reinterpret_cast<char*>(&id_)) + sizeof(time_));
  // @@protoc_insertion_point(copy_constructor:etcdserverpb.Request)
}

void Request::SharedCtor() {
  _cached_size_ = 0;
  method_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  path_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  val_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  prevvalue_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&time_) -
      reinterpret_cast<char*>(&id_)) + sizeof(time_));
}

Request::~Request() {
  // @@protoc_insertion_point(destructor:etcdserverpb.Request)
  SharedDtor();
}

void Request::SharedDtor() {
  method_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  path_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  val_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  prevvalue_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void Request::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Request::descriptor() {
  ::protobuf_etcdserver_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_etcdserver_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Request& Request::default_instance() {
  ::protobuf_etcdserver_2eproto::InitDefaultsRequest();
  return *internal_default_instance();
}

Request* Request::New(::google::protobuf::Arena* arena) const {
  Request* n = new Request;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Request::Clear() {
// @@protoc_insertion_point(message_clear_start:etcdserverpb.Request)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 15u) {
    if (cached_has_bits & 0x00000001u) {
      GOOGLE_DCHECK(!method_.IsDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited()));
      (*method_.UnsafeRawStringPointer())->clear();
    }
    if (cached_has_bits & 0x00000002u) {
      GOOGLE_DCHECK(!path_.IsDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited()));
      (*path_.UnsafeRawStringPointer())->clear();
    }
    if (cached_has_bits & 0x00000004u) {
      GOOGLE_DCHECK(!val_.IsDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited()));
      (*val_.UnsafeRawStringPointer())->clear();
    }
    if (cached_has_bits & 0x00000008u) {
      GOOGLE_DCHECK(!prevvalue_.IsDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited()));
      (*prevvalue_.UnsafeRawStringPointer())->clear();
    }
  }
  if (cached_has_bits & 240u) {
    ::memset(&id_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&since_) -
        reinterpret_cast<char*>(&id_)) + sizeof(since_));
  }
  if (cached_has_bits & 65280u) {
    ::memset(&dir_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&refresh_) -
        reinterpret_cast<char*>(&dir_)) + sizeof(refresh_));
  }
  time_ = GOOGLE_LONGLONG(0);
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

bool Request::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:etcdserverpb.Request)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(16383u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional uint64 ID = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          set_has_id();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional string Method = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_method()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->method().data(), static_cast<int>(this->method().length()),
            ::google::protobuf::internal::WireFormat::PARSE,
            "etcdserverpb.Request.Method");
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional string Path = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_path()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->path().data(), static_cast<int>(this->path().length()),
            ::google::protobuf::internal::WireFormat::PARSE,
            "etcdserverpb.Request.Path");
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional string Val = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_val()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->val().data(), static_cast<int>(this->val().length()),
            ::google::protobuf::internal::WireFormat::PARSE,
            "etcdserverpb.Request.Val");
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional bool Dir = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {
          set_has_dir();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &dir_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional string PrevValue = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_prevvalue()));
          ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
            this->prevvalue().data(), static_cast<int>(this->prevvalue().length()),
            ::google::protobuf::internal::WireFormat::PARSE,
            "etcdserverpb.Request.PrevValue");
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional uint64 PrevIndex = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(56u /* 56 & 0xFF */)) {
          set_has_previndex();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &previndex_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional bool PrevExist = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(64u /* 64 & 0xFF */)) {
          set_has_prevexist();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &prevexist_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional int64 Expiration = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(72u /* 72 & 0xFF */)) {
          set_has_expiration();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &expiration_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional bool Wait = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(80u /* 80 & 0xFF */)) {
          set_has_wait();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &wait_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional uint64 Since = 11;
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(88u /* 88 & 0xFF */)) {
          set_has_since();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &since_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional bool Recursive = 12;
      case 12: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(96u /* 96 & 0xFF */)) {
          set_has_recursive();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &recursive_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional bool Sorted = 13;
      case 13: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(104u /* 104 & 0xFF */)) {
          set_has_sorted();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &sorted_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional bool Quorum = 14;
      case 14: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(112u /* 112 & 0xFF */)) {
          set_has_quorum();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &quorum_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional int64 Time = 15;
      case 15: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(120u /* 120 & 0xFF */)) {
          set_has_time();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &time_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional bool Stream = 16;
      case 16: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(128u /* 128 & 0xFF */)) {
          set_has_stream();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &stream_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional bool Refresh = 17;
      case 17: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(136u /* 136 & 0xFF */)) {
          set_has_refresh();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &refresh_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:etcdserverpb.Request)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:etcdserverpb.Request)
  return false;
#undef DO_
}

void Request::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:etcdserverpb.Request)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional uint64 ID = 1;
  if (cached_has_bits & 0x00000010u) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(1, this->id(), output);
  }

  // optional string Method = 2;
  if (cached_has_bits & 0x00000001u) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->method().data(), static_cast<int>(this->method().length()),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "etcdserverpb.Request.Method");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->method(), output);
  }

  // optional string Path = 3;
  if (cached_has_bits & 0x00000002u) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->path().data(), static_cast<int>(this->path().length()),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "etcdserverpb.Request.Path");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->path(), output);
  }

  // optional string Val = 4;
  if (cached_has_bits & 0x00000004u) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->val().data(), static_cast<int>(this->val().length()),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "etcdserverpb.Request.Val");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->val(), output);
  }

  // optional bool Dir = 5;
  if (cached_has_bits & 0x00000100u) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(5, this->dir(), output);
  }

  // optional string PrevValue = 6;
  if (cached_has_bits & 0x00000008u) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->prevvalue().data(), static_cast<int>(this->prevvalue().length()),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "etcdserverpb.Request.PrevValue");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      6, this->prevvalue(), output);
  }

  // optional uint64 PrevIndex = 7;
  if (cached_has_bits & 0x00000020u) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(7, this->previndex(), output);
  }

  // optional bool PrevExist = 8;
  if (cached_has_bits & 0x00000200u) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(8, this->prevexist(), output);
  }

  // optional int64 Expiration = 9;
  if (cached_has_bits & 0x00000040u) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(9, this->expiration(), output);
  }

  // optional bool Wait = 10;
  if (cached_has_bits & 0x00000400u) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(10, this->wait(), output);
  }

  // optional uint64 Since = 11;
  if (cached_has_bits & 0x00000080u) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(11, this->since(), output);
  }

  // optional bool Recursive = 12;
  if (cached_has_bits & 0x00000800u) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(12, this->recursive(), output);
  }

  // optional bool Sorted = 13;
  if (cached_has_bits & 0x00001000u) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(13, this->sorted(), output);
  }

  // optional bool Quorum = 14;
  if (cached_has_bits & 0x00002000u) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(14, this->quorum(), output);
  }

  // optional int64 Time = 15;
  if (cached_has_bits & 0x00010000u) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(15, this->time(), output);
  }

  // optional bool Stream = 16;
  if (cached_has_bits & 0x00004000u) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(16, this->stream(), output);
  }

  // optional bool Refresh = 17;
  if (cached_has_bits & 0x00008000u) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(17, this->refresh(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:etcdserverpb.Request)
}

::google::protobuf::uint8* Request::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:etcdserverpb.Request)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional uint64 ID = 1;
  if (cached_has_bits & 0x00000010u) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(1, this->id(), target);
  }

  // optional string Method = 2;
  if (cached_has_bits & 0x00000001u) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->method().data(), static_cast<int>(this->method().length()),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "etcdserverpb.Request.Method");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->method(), target);
  }

  // optional string Path = 3;
  if (cached_has_bits & 0x00000002u) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->path().data(), static_cast<int>(this->path().length()),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "etcdserverpb.Request.Path");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->path(), target);
  }

  // optional string Val = 4;
  if (cached_has_bits & 0x00000004u) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->val().data(), static_cast<int>(this->val().length()),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "etcdserverpb.Request.Val");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->val(), target);
  }

  // optional bool Dir = 5;
  if (cached_has_bits & 0x00000100u) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(5, this->dir(), target);
  }

  // optional string PrevValue = 6;
  if (cached_has_bits & 0x00000008u) {
    ::google::protobuf::internal::WireFormat::VerifyUTF8StringNamedField(
      this->prevvalue().data(), static_cast<int>(this->prevvalue().length()),
      ::google::protobuf::internal::WireFormat::SERIALIZE,
      "etcdserverpb.Request.PrevValue");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        6, this->prevvalue(), target);
  }

  // optional uint64 PrevIndex = 7;
  if (cached_has_bits & 0x00000020u) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(7, this->previndex(), target);
  }

  // optional bool PrevExist = 8;
  if (cached_has_bits & 0x00000200u) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(8, this->prevexist(), target);
  }

  // optional int64 Expiration = 9;
  if (cached_has_bits & 0x00000040u) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(9, this->expiration(), target);
  }

  // optional bool Wait = 10;
  if (cached_has_bits & 0x00000400u) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(10, this->wait(), target);
  }

  // optional uint64 Since = 11;
  if (cached_has_bits & 0x00000080u) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(11, this->since(), target);
  }

  // optional bool Recursive = 12;
  if (cached_has_bits & 0x00000800u) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(12, this->recursive(), target);
  }

  // optional bool Sorted = 13;
  if (cached_has_bits & 0x00001000u) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(13, this->sorted(), target);
  }

  // optional bool Quorum = 14;
  if (cached_has_bits & 0x00002000u) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(14, this->quorum(), target);
  }

  // optional int64 Time = 15;
  if (cached_has_bits & 0x00010000u) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(15, this->time(), target);
  }

  // optional bool Stream = 16;
  if (cached_has_bits & 0x00004000u) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(16, this->stream(), target);
  }

  // optional bool Refresh = 17;
  if (cached_has_bits & 0x00008000u) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(17, this->refresh(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:etcdserverpb.Request)
  return target;
}

size_t Request::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:etcdserverpb.Request)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  if (_has_bits_[0 / 32] & 255u) {
    // optional string Method = 2;
    if (has_method()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->method());
    }

    // optional string Path = 3;
    if (has_path()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->path());
    }

    // optional string Val = 4;
    if (has_val()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->val());
    }

    // optional string PrevValue = 6;
    if (has_prevvalue()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->prevvalue());
    }

    // optional uint64 ID = 1;
    if (has_id()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->id());
    }

    // optional uint64 PrevIndex = 7;
    if (has_previndex()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->previndex());
    }

    // optional int64 Expiration = 9;
    if (has_expiration()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->expiration());
    }

    // optional uint64 Since = 11;
    if (has_since()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->since());
    }

  }
  if (_has_bits_[8 / 32] & 65280u) {
    // optional bool Dir = 5;
    if (has_dir()) {
      total_size += 1 + 1;
    }

    // optional bool PrevExist = 8;
    if (has_prevexist()) {
      total_size += 1 + 1;
    }

    // optional bool Wait = 10;
    if (has_wait()) {
      total_size += 1 + 1;
    }

    // optional bool Recursive = 12;
    if (has_recursive()) {
      total_size += 1 + 1;
    }

    // optional bool Sorted = 13;
    if (has_sorted()) {
      total_size += 1 + 1;
    }

    // optional bool Quorum = 14;
    if (has_quorum()) {
      total_size += 1 + 1;
    }

    // optional bool Stream = 16;
    if (has_stream()) {
      total_size += 2 + 1;
    }

    // optional bool Refresh = 17;
    if (has_refresh()) {
      total_size += 2 + 1;
    }

  }
  // optional int64 Time = 15;
  if (has_time()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->time());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Request::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:etcdserverpb.Request)
  GOOGLE_DCHECK_NE(&from, this);
  const Request* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Request>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:etcdserverpb.Request)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:etcdserverpb.Request)
    MergeFrom(*source);
  }
}

void Request::MergeFrom(const Request& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:etcdserverpb.Request)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 255u) {
    if (cached_has_bits & 0x00000001u) {
      set_has_method();
      method_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.method_);
    }
    if (cached_has_bits & 0x00000002u) {
      set_has_path();
      path_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.path_);
    }
    if (cached_has_bits & 0x00000004u) {
      set_has_val();
      val_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.val_);
    }
    if (cached_has_bits & 0x00000008u) {
      set_has_prevvalue();
      prevvalue_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.prevvalue_);
    }
    if (cached_has_bits & 0x00000010u) {
      id_ = from.id_;
    }
    if (cached_has_bits & 0x00000020u) {
      previndex_ = from.previndex_;
    }
    if (cached_has_bits & 0x00000040u) {
      expiration_ = from.expiration_;
    }
    if (cached_has_bits & 0x00000080u) {
      since_ = from.since_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  if (cached_has_bits & 65280u) {
    if (cached_has_bits & 0x00000100u) {
      dir_ = from.dir_;
    }
    if (cached_has_bits & 0x00000200u) {
      prevexist_ = from.prevexist_;
    }
    if (cached_has_bits & 0x00000400u) {
      wait_ = from.wait_;
    }
    if (cached_has_bits & 0x00000800u) {
      recursive_ = from.recursive_;
    }
    if (cached_has_bits & 0x00001000u) {
      sorted_ = from.sorted_;
    }
    if (cached_has_bits & 0x00002000u) {
      quorum_ = from.quorum_;
    }
    if (cached_has_bits & 0x00004000u) {
      stream_ = from.stream_;
    }
    if (cached_has_bits & 0x00008000u) {
      refresh_ = from.refresh_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
  if (cached_has_bits & 0x00010000u) {
    set_time(from.time());
  }
}

void Request::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:etcdserverpb.Request)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Request::CopyFrom(const Request& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:etcdserverpb.Request)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Request::IsInitialized() const {
  return true;
}

void Request::Swap(Request* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Request::InternalSwap(Request* other) {
  using std::swap;
  method_.Swap(&other->method_);
  path_.Swap(&other->path_);
  val_.Swap(&other->val_);
  prevvalue_.Swap(&other->prevvalue_);
  swap(id_, other->id_);
  swap(previndex_, other->previndex_);
  swap(expiration_, other->expiration_);
  swap(since_, other->since_);
  swap(dir_, other->dir_);
  swap(prevexist_, other->prevexist_);
  swap(wait_, other->wait_);
  swap(recursive_, other->recursive_);
  swap(sorted_, other->sorted_);
  swap(quorum_, other->quorum_);
  swap(stream_, other->stream_);
  swap(refresh_, other->refresh_);
  swap(time_, other->time_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Request::GetMetadata() const {
  protobuf_etcdserver_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_etcdserver_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Metadata::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Metadata::kNodeIDFieldNumber;
const int Metadata::kClusterIDFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Metadata::Metadata()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_etcdserver_2eproto::InitDefaultsMetadata();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:etcdserverpb.Metadata)
}
Metadata::Metadata(const Metadata& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _has_bits_(from._has_bits_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&nodeid_, &from.nodeid_,
    static_cast<size_t>(reinterpret_cast<char*>(&clusterid_) -
    reinterpret_cast<char*>(&nodeid_)) + sizeof(clusterid_));
  // @@protoc_insertion_point(copy_constructor:etcdserverpb.Metadata)
}

void Metadata::SharedCtor() {
  _cached_size_ = 0;
  ::memset(&nodeid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&clusterid_) -
      reinterpret_cast<char*>(&nodeid_)) + sizeof(clusterid_));
}

Metadata::~Metadata() {
  // @@protoc_insertion_point(destructor:etcdserverpb.Metadata)
  SharedDtor();
}

void Metadata::SharedDtor() {
}

void Metadata::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Metadata::descriptor() {
  ::protobuf_etcdserver_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_etcdserver_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Metadata& Metadata::default_instance() {
  ::protobuf_etcdserver_2eproto::InitDefaultsMetadata();
  return *internal_default_instance();
}

Metadata* Metadata::New(::google::protobuf::Arena* arena) const {
  Metadata* n = new Metadata;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Metadata::Clear() {
// @@protoc_insertion_point(message_clear_start:etcdserverpb.Metadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 3u) {
    ::memset(&nodeid_, 0, static_cast<size_t>(
        reinterpret_cast<char*>(&clusterid_) -
        reinterpret_cast<char*>(&nodeid_)) + sizeof(clusterid_));
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear();
}

bool Metadata::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:etcdserverpb.Metadata)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // optional uint64 NodeID = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          set_has_nodeid();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &nodeid_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // optional uint64 ClusterID = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {
          set_has_clusterid();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &clusterid_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:etcdserverpb.Metadata)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:etcdserverpb.Metadata)
  return false;
#undef DO_
}

void Metadata::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:etcdserverpb.Metadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional uint64 NodeID = 1;
  if (cached_has_bits & 0x00000001u) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(1, this->nodeid(), output);
  }

  // optional uint64 ClusterID = 2;
  if (cached_has_bits & 0x00000002u) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->clusterid(), output);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        _internal_metadata_.unknown_fields(), output);
  }
  // @@protoc_insertion_point(serialize_end:etcdserverpb.Metadata)
}

::google::protobuf::uint8* Metadata::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:etcdserverpb.Metadata)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  // optional uint64 NodeID = 1;
  if (cached_has_bits & 0x00000001u) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(1, this->nodeid(), target);
  }

  // optional uint64 ClusterID = 2;
  if (cached_has_bits & 0x00000002u) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->clusterid(), target);
  }

  if (_internal_metadata_.have_unknown_fields()) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields(), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:etcdserverpb.Metadata)
  return target;
}

size_t Metadata::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:etcdserverpb.Metadata)
  size_t total_size = 0;

  if (_internal_metadata_.have_unknown_fields()) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        _internal_metadata_.unknown_fields());
  }
  if (_has_bits_[0 / 32] & 3u) {
    // optional uint64 NodeID = 1;
    if (has_nodeid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->nodeid());
    }

    // optional uint64 ClusterID = 2;
    if (has_clusterid()) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->clusterid());
    }

  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Metadata::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:etcdserverpb.Metadata)
  GOOGLE_DCHECK_NE(&from, this);
  const Metadata* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Metadata>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:etcdserverpb.Metadata)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:etcdserverpb.Metadata)
    MergeFrom(*source);
  }
}

void Metadata::MergeFrom(const Metadata& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:etcdserverpb.Metadata)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  cached_has_bits = from._has_bits_[0];
  if (cached_has_bits & 3u) {
    if (cached_has_bits & 0x00000001u) {
      nodeid_ = from.nodeid_;
    }
    if (cached_has_bits & 0x00000002u) {
      clusterid_ = from.clusterid_;
    }
    _has_bits_[0] |= cached_has_bits;
  }
}

void Metadata::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:etcdserverpb.Metadata)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Metadata::CopyFrom(const Metadata& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:etcdserverpb.Metadata)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Metadata::IsInitialized() const {
  return true;
}

void Metadata::Swap(Metadata* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Metadata::InternalSwap(Metadata* other) {
  using std::swap;
  swap(nodeid_, other->nodeid_);
  swap(clusterid_, other->clusterid_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Metadata::GetMetadata() const {
  protobuf_etcdserver_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_etcdserver_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace etcdserverpb

// @@protoc_insertion_point(global_scope)
