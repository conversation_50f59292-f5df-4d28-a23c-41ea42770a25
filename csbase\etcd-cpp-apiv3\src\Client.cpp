#include <memory>
#include "etcd/Client.hpp"
#include "v3/include/action_constants.hpp"
#include "v3/include/AsyncTxnResponse.hpp"
#include "v3/include/AsyncRangeResponse.hpp"
#include "v3/include/AsyncWatchResponse.hpp"
#include "v3/include/AsyncDeleteRangeResponse.hpp"
#include "v3/include/Transaction.hpp"
#include <iostream>

#include "v3/include/AsyncSetAction.hpp"
#include "v3/include/AsyncCompareAndSwapAction.hpp"
#include "v3/include/AsyncCompareAndDeleteAction.hpp"
#include "v3/include/AsyncUpdateAction.hpp"
#include "v3/include/AsyncGetAction.hpp"
#include "v3/include/AsyncDeleteAction.hpp"
#include "v3/include/AsyncWatchAction.hpp"
#include "v3/include/AsyncLeaseGrantAction.hpp"
#include "v3/include/AsyncLeaseRevokeAction.hpp"
#include "v3/include/AsyncLeaseKeepAliveAction.hpp"

using grpc::Channel;

/*  grpc::Channel的用法如下:
namespace grpc {
/// Channels represent a connection to an endpoint. Created by \a CreateChannel.
class Channel final : public ChannelInterface,
                      public internal::CallHook,
                      public std::enable_shared_from_this<Channel>,
                      private GrpcLibraryCodegen {
 public:
  ~Channel();

  /// Get the current channel state. If the channel is in IDLE and
  /// \a try_to_connect is set to true, try to connect.
  grpc_connectivity_state GetState(bool try_to_connect) override;

 Connectivity state of a channel. 
typedef enum {
  // channel is idle 
  GRPC_CHANNEL_IDLE,
  // channel is connecting 
  GRPC_CHANNEL_CONNECTING,
  // channel is ready for work 
  GRPC_CHANNEL_READY,
  // channel has seen a failure but expects to recover 
  GRPC_CHANNEL_TRANSIENT_FAILURE,
  // channel has seen a failure that it cannot recover from 
  GRPC_CHANNEL_SHUTDOWN
} grpc_connectivity_state;
*/

//gRPC docs：wait-for-ready.md 提及: TRANSIENT_FAILURE 短暂失败的概念,此概念基于该通道一段时间后会自动回复的假定,需要注意
etcd::Client::Client(const std::vector<std::string>& addresss)
{
    SetUp();
    EtcdSrvConnUpdate(addresss);
/*
    addrs_ = addresss;
    KVStubPtr kv_stub;
	WatchStubPtr watch_stub;
	LeaseStubPtr lease_stub;
	for(const auto& addr : addresss)
	{
		//在这里拓展即可,参考:go.etcd.io/etcd/clientv3
		//通过channel可以判断通道的健康情况
		//addr支持dns的形式,eg: dns:///akcs.etcd.com
		// std::shared_ptr<Channel> channel = grpc::CreateChannel(addr, grpc::InsecureChannelCredentials());
        grpc::ChannelArguments channel_arg;
        channel_arg.SetInt(GRPC_ARG_ALLOW_REUSEPORT,1); //可以多个Setxxx一直往下添加参数
        
        std::shared_ptr<Channel> channel = grpc::CreateCustomChannel(addr, grpc::InsecureChannelCredentials(), channel_arg);
		channel->GetState(true);//启动连接
		kv_stub = KV::NewStub(channel);
		watch_stub = Watch::NewStub(channel);
		lease_stub = Lease::NewStub(channel);
		etcd_endpoints_channels_.insert(std::pair<std::string,std::shared_ptr<Channel>>(addr, channel));
		std::tuple<KVStubPtr,WatchStubPtr,LeaseStubPtr> stubs(kv_stub, watch_stub, lease_stub);
		etcd_endpoints_stubs_.insert(std::pair<std::string,std::tuple<KVStubPtr,WatchStubPtr,LeaseStubPtr>>(addr, stubs));
        etcd_healthy_node_uri_ = addr;
	}
    //先赋值一次,也就是说  在我们这个场景下,其实没有负载均衡的能力了,因为stub_是固定的,这个区别于gRPC内置的负载均衡器实现
    //且所有的etcd client统一连接到同一个etcd集群的节点上
	{
		std::lock_guard<std::mutex> lock(stub_mutex_);
		kv_stub_ = kv_stub;
		watch_stub_ = watch_stub;
		lease_stub_ = lease_stub;
	}
*/
}


void etcd::Client::SetUp()  {
  response_generator_ =
      grpc_core::MakeRefCounted<grpc_core::FakeResolverResponseGenerator>();
  ResetStub(500);
}

void etcd::Client::EtcdSrvConnUpdate(const std::vector<std::string>& etcd_addrs)
{
    std::vector<AddressData> addresses;
    for (const auto& etcdaddr : etcd_addrs) //ip:port的形式
    {
        std::string ip;
        std::string port;
        std::string::size_type pos = etcdaddr.find(":");
        if (std::string::npos != pos)
        {
            ip = etcdaddr.substr(0, pos);
            port = etcdaddr.substr(pos + 1);
        }
        addresses.emplace_back(AddressData{std::atoi(port.c_str()), false, "", ip.c_str()});//false 不是负载均衡器
    }
    SetNextResolution(addresses);
}


grpc_lb_addresses* etcd::Client::CreateLbAddressesFromAddressDataList(
    const std::vector<AddressData>& address_data) {
    grpc_lb_addresses* addresses =
      grpc_lb_addresses_create(address_data.size(), nullptr);
    for (size_t i = 0; i < address_data.size(); ++i) {
        char* lb_uri_str;
        gpr_asprintf(&lb_uri_str, "ipv4:%s:%d", address_data[i].host.c_str(), address_data[i].port);
        grpc_uri* lb_uri = grpc_uri_parse(lb_uri_str, true);
        GPR_ASSERT(lb_uri != nullptr);
        grpc_lb_addresses_set_address_from_uri(
            addresses, i, lb_uri, address_data[i].is_balancer,
            address_data[i].balancer_name.c_str(), nullptr);
        grpc_uri_destroy(lb_uri);
        gpr_free(lb_uri_str);
    }
  return addresses;
}

void etcd::Client::SetNextResolution(const std::vector<AddressData>& address_data) {
    grpc_core::ExecCtx exec_ctx;
    grpc_lb_addresses* addresses =
      CreateLbAddressesFromAddressDataList(address_data);
    grpc_arg fake_addresses = grpc_lb_addresses_create_channel_arg(addresses);
    grpc_channel_args fake_result = {1, &fake_addresses};
    response_generator_->SetResponse(&fake_result);
    grpc_lb_addresses_destroy(addresses);
}

void etcd::Client::SetNextReresolutionResponse(
    const std::vector<AddressData>& address_data) {
    grpc_core::ExecCtx exec_ctx;
    grpc_lb_addresses* addresses =
      CreateLbAddressesFromAddressDataList(address_data);
    grpc_arg fake_addresses = grpc_lb_addresses_create_channel_arg(addresses);
    grpc_channel_args fake_result = {1, &fake_addresses};
    response_generator_->SetReresolutionResponse(&fake_result);
    grpc_lb_addresses_destroy(addresses);
}


void etcd::Client::ResetStub(int fallback_timeout,
               const grpc::string& expected_targets) {
    grpc::ChannelArguments args;
    args.SetGrpclbFallbackTimeout(fallback_timeout);

    args.SetLoadBalancingPolicyName("round_robin");//pick_first/round_robin/grcplb自定义负载均衡
    args.SetPointer(GRPC_ARG_FAKE_RESOLVER_RESPONSE_GENERATOR,
                  response_generator_.get());
    if (!expected_targets.empty()) {
        args.SetString(GRPC_ARG_FAKE_SECURITY_EXPECTED_TARGETS, expected_targets);
    }
    std::ostringstream uri;
    uri << "fake:///" << kApplicationTargetName_;
    channel_ = grpc::CreateCustomChannel(uri.str(), grpc::InsecureChannelCredentials(), args);

    kv_stub_ = KV::NewStub(channel_);
    watch_stub_ = Watch::NewStub(channel_);
    lease_stub_ = Lease::NewStub(channel_);  
}


//更新集群节点信息
int etcd::Client::update_endpoints(const std::vector<std::string>& addrs)
{
    if(addrs.size() == 0)
    {
        return -1;
    }
    KVStubPtr kv_stub;
	WatchStubPtr watch_stub;
	LeaseStubPtr lease_stub;
    std::map<std::string, std::shared_ptr<Channel>> etcd_endpoints_channels_tmp;
    std::map<std::string, std::tuple<KVStubPtr,WatchStubPtr,LeaseStubPtr>> etcd_endpoints_stubs_tmp;
    
	for(const auto& addr : addrs)
	{
		std::shared_ptr<Channel> channel = grpc::CreateChannel(addr, grpc::InsecureChannelCredentials());
		channel->GetState(true);//启动连接
		kv_stub = KV::NewStub(channel);
		watch_stub = Watch::NewStub(channel);
		lease_stub = Lease::NewStub(channel);
       
		etcd_endpoints_channels_tmp.insert(std::pair<std::string,std::shared_ptr<Channel>>(addr, channel));
		std::tuple<KVStubPtr,WatchStubPtr,LeaseStubPtr> stubs(kv_stub, watch_stub, lease_stub);
		etcd_endpoints_stubs_tmp.insert(std::pair<std::string,std::tuple<KVStubPtr,WatchStubPtr,LeaseStubPtr>>(addr, stubs));
	}
    {
		std::lock_guard<std::mutex> lock(stub_mutex_);
		kv_stub_ = kv_stub;
		watch_stub_ = watch_stub;
		lease_stub_ = lease_stub;
	}
    etcd_endpoints_channels_.swap(etcd_endpoints_channels_tmp);
    etcd_endpoints_stubs_.swap(etcd_endpoints_stubs_tmp);
    etcd_endpoints_channels_tmp.clear();
    etcd_endpoints_stubs_tmp.clear();
    return 0;
}

pplx::task<etcd::Response> etcd::Client::get(std::string const & key)
{
  etcdv3::ActionParameters params;
  params.key.assign(key);
  params.withPrefix = false;
  params.kv_stub = kv_stub_.get();
  std::shared_ptr<etcdv3::AsyncGetAction> call(new etcdv3::AsyncGetAction(params));
  return Response::create(call);
}

pplx::task<etcd::Response> etcd::Client::set(std::string const & key, std::string const & value, int ttl)
{
  etcdv3::ActionParameters params;
  params.key.assign(key);
  params.value.assign(value);
  params.kv_stub = kv_stub_.get();

  if(ttl > 0)
  {
    auto res = leasegrant(ttl).get();
    if(!res.is_ok()) 
    {
      return pplx::task<etcd::Response>([res]()
      {
        return etcd::Response(res.error_code(), res.error_message().c_str());    
      });
    }
    else
    {
      params.lease_id = res.value().lease();
    }
  }

  std::shared_ptr<etcdv3::AsyncSetAction> call(new etcdv3::AsyncSetAction(params));
  return Response::create(call);
}

pplx::task<etcd::Response> etcd::Client::set(std::string const & key, std::string const & value, int64_t leaseid)
{
  etcdv3::ActionParameters params;
  params.key.assign(key);
  params.value.assign(value);
  params.lease_id = leaseid;
  params.kv_stub = kv_stub_.get();
  std::shared_ptr<etcdv3::AsyncSetAction> call(new etcdv3::AsyncSetAction(params));
  return Response::create(call);
}

pplx::task<etcd::Response> etcd::Client::add(std::string const & key, std::string const & value, int ttl)
{
  etcdv3::ActionParameters params;
  params.key.assign(key);
  params.value.assign(value);
  params.kv_stub = kv_stub_.get();

  if(ttl > 0)
  {
    auto res = leasegrant(ttl).get();
    if(!res.is_ok()) 
    {
      return pplx::task<etcd::Response>([res]()
      {
        return etcd::Response(res.error_code(), res.error_message().c_str());    
      });
    }
    else
    {
      params.lease_id = res.value().lease();
    }
  }
  std::shared_ptr<etcdv3::AsyncSetAction> call(new etcdv3::AsyncSetAction(params,true));
  return Response::create(call);
}

pplx::task<etcd::Response> etcd::Client::add(std::string const & key, std::string const & value, int64_t leaseid)
{
  etcdv3::ActionParameters params;
  params.key.assign(key);
  params.value.assign(value);
  params.lease_id = leaseid;
  params.kv_stub = kv_stub_.get();
  std::shared_ptr<etcdv3::AsyncSetAction> call(new etcdv3::AsyncSetAction(params,true));
  return Response::create(call);
}

pplx::task<etcd::Response> etcd::Client::modify(std::string const & key, std::string const & value, int ttl)
{
  etcdv3::ActionParameters params;
  params.key.assign(key);
  params.value.assign(value);
  params.kv_stub = kv_stub_.get();

  if(ttl > 0)
  {
    auto res = leasegrant(ttl).get();
    if(!res.is_ok()) 
    {
      return pplx::task<etcd::Response>([res]()
      {
        return etcd::Response(res.error_code(), res.error_message().c_str());    
      });
    }
    else
    {
      params.lease_id = res.value().lease();
    }
  }
  std::shared_ptr<etcdv3::AsyncUpdateAction> call(new etcdv3::AsyncUpdateAction(params));
  return Response::create(call);
}

pplx::task<etcd::Response> etcd::Client::modify(std::string const & key, std::string const & value, int64_t leaseid)
{
  etcdv3::ActionParameters params;
  params.key.assign(key);
  params.value.assign(value);
  params.lease_id = leaseid;
  params.kv_stub = kv_stub_.get();
  std::shared_ptr<etcdv3::AsyncUpdateAction> call(new etcdv3::AsyncUpdateAction(params));
  return Response::create(call);
}

pplx::task<etcd::Response> etcd::Client::modify_if(std::string const & key, std::string const & value, std::string const & old_value, int ttl)
{
  etcdv3::ActionParameters params;
  params.key.assign(key);
  params.value.assign(value);
  params.old_value.assign(old_value);
  params.kv_stub = kv_stub_.get();

  if(ttl > 0)
  {
    auto res = leasegrant(ttl).get();
    if(!res.is_ok()) 
    {
      return pplx::task<etcd::Response>([res]()
      {
        return etcd::Response(res.error_code(), res.error_message().c_str());    
      });
    }
    else
    {
      params.lease_id = res.value().lease();
    }
  }
  std::shared_ptr<etcdv3::AsyncCompareAndSwapAction> call(new etcdv3::AsyncCompareAndSwapAction(params,etcdv3::Atomicity_Type::PREV_VALUE));
  return Response::create(call);
}

pplx::task<etcd::Response> etcd::Client::modify_if(std::string const & key, std::string const & value, std::string const & old_value, int64_t leaseid)
{
  etcdv3::ActionParameters params;
  params.key.assign(key);
  params.value.assign(value);
  params.old_value.assign(old_value);
  params.lease_id = leaseid;
  params.kv_stub = kv_stub_.get();
  std::shared_ptr<etcdv3::AsyncCompareAndSwapAction> call(new etcdv3::AsyncCompareAndSwapAction(params,etcdv3::Atomicity_Type::PREV_VALUE));
  return Response::create(call);
}

pplx::task<etcd::Response> etcd::Client::modify_if(std::string const & key, std::string const & value, int old_index, int ttl)
{
  etcdv3::ActionParameters params;
  params.key.assign(key);
  params.value.assign(value);
  params.old_revision = old_index;
  params.kv_stub = kv_stub_.get();
  if(ttl > 0)
  {
    auto res = leasegrant(ttl).get();
    if(!res.is_ok()) 
    {
      return pplx::task<etcd::Response>([res]()
      {
        return etcd::Response(res.error_code(), res.error_message().c_str());    
      });
    }
    else
    {
      params.lease_id = res.value().lease();
    }
  }
  std::shared_ptr<etcdv3::AsyncCompareAndSwapAction> call(new etcdv3::AsyncCompareAndSwapAction(params,etcdv3::Atomicity_Type::PREV_INDEX));
  return Response::create(call);
}

pplx::task<etcd::Response> etcd::Client::modify_if(std::string const & key, std::string const & value, int old_index, int64_t leaseid)
{
  etcdv3::ActionParameters params;
  params.key.assign(key);
  params.value.assign(value);
  params.lease_id = leaseid;
  params.old_revision = old_index;
  params.kv_stub = kv_stub_.get();
  std::shared_ptr<etcdv3::AsyncCompareAndSwapAction> call(new etcdv3::AsyncCompareAndSwapAction(params,etcdv3::Atomicity_Type::PREV_INDEX));
  return Response::create(call);
}

pplx::task<etcd::Response> etcd::Client::rm(std::string const & key)
{
  etcdv3::ActionParameters params;
  params.key.assign(key);
  params.withPrefix = false;
  params.kv_stub = kv_stub_.get();
  std::shared_ptr<etcdv3::AsyncDeleteAction> call(new etcdv3::AsyncDeleteAction(params));
  return Response::create(call);
}

pplx::task<etcd::Response> etcd::Client::rm_if(std::string const & key, std::string const & old_value)
{
  etcdv3::ActionParameters params;
  params.key.assign(key);
  params.old_value.assign(old_value);
  params.kv_stub = kv_stub_.get();
  std::shared_ptr<etcdv3::AsyncCompareAndDeleteAction> call(new etcdv3::AsyncCompareAndDeleteAction(params,etcdv3::Atomicity_Type::PREV_VALUE));
  return Response::create(call);
}

pplx::task<etcd::Response> etcd::Client::rm_if(std::string const & key, int old_index)
{
  etcdv3::ActionParameters params;
  params.key.assign(key);
  params.old_revision = old_index;
  params.kv_stub = kv_stub_.get();
  std::shared_ptr<etcdv3::AsyncCompareAndDeleteAction> call(new etcdv3::AsyncCompareAndDeleteAction(params, etcdv3::Atomicity_Type::PREV_INDEX));;
  return Response::create(call);

}

pplx::task<etcd::Response> etcd::Client::rmdir(std::string const & key, bool recursive)
{
  etcdv3::ActionParameters params;
  params.key.assign(key);
  params.withPrefix = recursive;
  params.kv_stub = kv_stub_.get();
  std::shared_ptr<etcdv3::AsyncDeleteAction> call(new etcdv3::AsyncDeleteAction(params));
  return Response::create(call);
}

pplx::task<etcd::Response> etcd::Client::ls(std::string const & key)
{
  etcdv3::ActionParameters params;
  params.key.assign(key);
  params.withPrefix = true;
  params.kv_stub = kv_stub_.get();
  std::shared_ptr<etcdv3::AsyncGetAction> call(new etcdv3::AsyncGetAction(params));
  return Response::create(call);
}

pplx::task<etcd::Response> etcd::Client::watch(std::string const & key, bool recursive)
{
  etcdv3::ActionParameters params;
  params.key.assign(key);
  params.withPrefix = recursive;
  params.watch_stub = watch_stub_.get();
  std::shared_ptr<etcdv3::AsyncWatchAction> call(new etcdv3::AsyncWatchAction(params));
  return Response::create(call);
}

pplx::task<etcd::Response> etcd::Client::watch(std::string const & key, int fromIndex, bool recursive)
{
  etcdv3::ActionParameters params;
  params.key.assign(key);
  params.withPrefix = recursive;
  params.revision = fromIndex;
  params.watch_stub = watch_stub_.get();
  std::shared_ptr<etcdv3::AsyncWatchAction> call(new etcdv3::AsyncWatchAction(params));
  return Response::create(call);
}

pplx::task<etcd::Response> etcd::Client::leasegrant(int ttl)
{
  etcdv3::ActionParameters params;
  params.ttl = ttl;
  params.lease_stub = lease_stub_.get();
  std::shared_ptr<etcdv3::AsyncLeaseGrantAction> call(new etcdv3::AsyncLeaseGrantAction(params));
  return Response::create(call);
}

pplx::task<etcd::Response> etcd::Client::leaserevoke(int64_t id)
{
    etcdv3::ActionParameters params;
    params.lease_id = id;
    params.lease_stub = lease_stub_.get();
    std::shared_ptr<etcdv3::AsyncLeaseRevokeAction> call(new etcdv3::AsyncLeaseRevokeAction(params));
    return Response::create(call);
}

pplx::task<etcd::Response> etcd::Client::leasekeepalive(int64_t id)
{
    etcdv3::ActionParameters params;
    params.lease_id = id;
    params.lease_stub = lease_stub_.get();
    std::shared_ptr<etcdv3::AsyncLeaseKeepAliveAction> call(new etcdv3::AsyncLeaseKeepAliveAction(params));
    return Response::create(call);
}
//只要有连接中断,那么强制重新刷新server端的地址,并与之建立一一连接
void etcd::Client::node_failover()
{
	std::tuple<KVStubPtr,WatchStubPtr,LeaseStubPtr> stubs;
	std::shared_ptr<Channel> channel;
	for(auto& etcd_endpoints_channel : etcd_endpoints_channels_)
	{
		std::string endpoint = etcd_endpoints_channel.first;
		channel = etcd_endpoints_channel.second;
		int state = channel->GetState(true);
		if((state == GRPC_CHANNEL_READY) || (state == GRPC_CHANNEL_CONNECTING) || (state == GRPC_CHANNEL_IDLE))
		{
			stubs = etcd_endpoints_stubs_[endpoint];
			{
				std::lock_guard<std::mutex> lock(stub_mutex_);
				kv_stub_ = std::get<0>(stubs);
				watch_stub_ = std::get<1>(stubs);
				lease_stub_ = std::get<2>(stubs);
			}
            etcd_healthy_node_uri_ = endpoint;
			break;
		}
	}
}

const std::string etcd::Client::get_healthy_node_uri() const
{
    return etcd_healthy_node_uri_;
}

