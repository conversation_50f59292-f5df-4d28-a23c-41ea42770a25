#ifndef __ASYNC_LEASEREVOKEACTION_HPP__
#define __ASYNC_LEASEREVOKEACTION_HPP__

#include <grpc++/grpc++.h>
#include "proto/rpc.grpc.pb.h"
#include "v3/include/Action.hpp"
#include "v3/include/AsyncLeaseRevokeResponse.hpp"

using grpc::ClientAsyncResponseReader;
using etcdserverpb::LeaseRevokeResponse;

namespace etcdv3
{
  class AsyncLeaseRevokeAction : public etcdv3::Action
  {
    public:
      AsyncLeaseRevokeAction(etcdv3::ActionParameters param);
      AsyncLeaseRevokeResponse ParseResponse();
    private:
      LeaseRevokeResponse reply;
      std::unique_ptr<ClientAsyncResponseReader<LeaseRevokeResponse>> response_reader;
  };
}

#endif
