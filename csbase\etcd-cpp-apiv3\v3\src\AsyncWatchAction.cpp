#include "v3/include/AsyncWatchAction.hpp"
#include "v3/include/action_constants.hpp"

#include <iostream>

using etcdserverpb::RangeRequest;
using etcdserverpb::RangeResponse;
using etcdserverpb::WatchCreateRequest;
static bool isEtcdSrvOver = false;

etcdv3::AsyncWatchAction::AsyncWatchAction(etcdv3::ActionParameters param)
  : etcdv3::Action(param) 
{
  //isEtcdSrvOver = false;
  void* got_tag;
  bool ok = false;  
  isCancelled = false;
  //gpr_timespec timespec;
  //timespec.tv_sec = 10;//设置阻塞时间为10s,也就是watch必须10s返回一次.
  //timespec.tv_nsec = 0;
 // timespec.clock_type = GPR_TIMESPAN;
  //watch_context.set_deadline(timespec); 
  stream = parameters.watch_stub->AsyncWatch(&context,&cq_,(void*)"create");

  WatchRequest watch_req;
  WatchCreateRequest watch_create_req;
  watch_create_req.set_key(parameters.key);
  watch_create_req.set_prev_kv(true);
  watch_create_req.set_start_revision(parameters.revision);

  if(parameters.withPrefix)
  {
    std::string range_end(parameters.key); 
    int ascii = (int)range_end[range_end.length()-1];
    range_end.back() = ascii+1;
    watch_create_req.set_range_end(range_end);
  }

  watch_req.mutable_create_request()->CopyFrom(watch_create_req);//CopyFrom:结构体拷贝
  
  gpr_timespec deadline;
  deadline.clock_type = GPR_TIMESPAN;
  deadline.tv_sec = 0;
  deadline.tv_nsec = 10000000;
  //在这里可能有问题:
  //已经在这里:stream = parameters.watch_stub->AsyncWatch(&context,&cq_,(void*)"create");
  auto status = cq_.AsyncNext(&got_tag, &ok, deadline);
  //2019-03-18,解决掉etcd不起来的时候,应用崩溃的问题: assertion failed: GRPC_CALL_OK == grpc_call_start_batch(call->call(), cops, nops, ops, nullptr)
  //if (status != CompletionQueue::SHUTDOWN)
  if (status == CompletionQueue::GOT_EVENT)
  {
	  stream->Write(watch_req, (void*)"write");//当etcd挂掉的时候,其余应用挂在调用这个里面.
	  stream->Read(&reply, (void*)this);
  }
}

void etcdv3::AsyncWatchAction::waitForResponse() //call调用的是这个..
{
  //std::cout << "--------enter waitForResponse----" << std::endl;
  void* got_tag;
  bool ok = false;    
  if(!isEtcdSrvOver)
  {
	  while(cq_.Next(&got_tag, &ok))
	  {
		if(ok == true && (got_tag == (void*)"writes done"))
	    {
	        break; //读取结束
	    }
		else if(ok == false)//etcd死掉
		{
			//bool f = (got_tag == (void*)"writes done");
			//std::cout << "-------- now etcd is down----" << ",ok is:" << ok << ", got_tag == writes done is:" << f <<  std::endl;
			isEtcdSrvOver = true;
			break;
		}
		
	    if(got_tag == (void*)this) // read tag
	    {
	      if(reply.events_size()) //读到一个了,就结束掉流 当真的有监听对象改变时,就会返回大于等于1的数量
	      {
			
			//std::cout << "-------- reply.events_size()----" << reply.events_size() << std::endl;
			stream->WritesDone((void*)"writes done");  //这个之后，马上会收到对端发送过来的   "writes done" 
	      }
	      else
	      {
	        stream->Read(&reply, (void*)this);//这种情况下读取到的就是空的,eg:etcd watch something happend, action is:,value is:, watch index is:0
	        
	      } 
	    }  
  	}
  }
  else
  {
	  gpr_timespec deadline;
	  deadline.clock_type = GPR_TIMESPAN;
	  deadline.tv_sec = 10;
	  deadline.tv_nsec = 0;
	  //bool loop = true;
	  while(1)
	  {
		auto status = cq_.AsyncNext(&got_tag, &ok, deadline);
		if(ok == true)
	    {
			//std::cout << "-------- now etcd is up01----" << ",ok is:"  << ok << ",status is:" << status << std::endl;
			isEtcdSrvOver = false;
			break;
			
	    }
		if (status  == CompletionQueue::TIMEOUT)
		{
			std::cout << "--------over time now, etcd is still down----" << ",ok is:"  << ok << ",status is:" << status << std::endl;
			break;
		}
		//std::cout << "--------etcd is still down  02----" << ",ok is:"  << ok << ",status is:" << status << std::endl;
		//if (status  == CompletionQueue::GOT_EVENT)//当 etcd 真挂掉的时候, 进入会返回:GOT_EVEN
	      
	  }
  }
}


void etcdv3::AsyncWatchAction::CancelWatch() //不必理会
{
  if(isCancelled == false)
  {
    stream->WritesDone((void*)"writes done");
  }
}

void etcdv3::AsyncWatchAction::waitForResponse(std::function<void(etcd::Response)> callback) 
{
  void* got_tag;
  bool ok = false;    

  while(cq_.Next(&got_tag, &ok))
  {
    if(ok == false)
    {
      break;
    }
    if(got_tag == (void*)"writes done")
    {
      isCancelled = true;
      //break;
    }
    else if(got_tag == (void*)this) // read tag
    {
      if(reply.events_size())
      {
        auto resp = ParseResponse();
        callback(resp); 
      }
      stream->Read(&reply, (void*)this);
    }     
  }
}

etcdv3::AsyncWatchResponse etcdv3::AsyncWatchAction::ParseResponse()
{

  AsyncWatchResponse watch_resp;
  if(!status.ok())
  {
    watch_resp.set_error_code(status.error_code());
    watch_resp.set_error_message(status.error_message());
  }
  else
  { 
    watch_resp.ParseResponse(reply);
  }
  return watch_resp;
}
