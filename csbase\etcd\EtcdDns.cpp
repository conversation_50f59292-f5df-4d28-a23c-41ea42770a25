#include "EtcdDns.h"
#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "EtcdCliMng.h"
#include "AkLogging.h"
#include <sstream>

CEtcdDnsManager* g_etcd_dns_mng = nullptr;

int CEtcdDnsManager::OnDnsChange(const std::vector <std::string>& addrs)
{
    std::stringstream etcd_str;
    
    std::vector<std::string> addr_list;
    for (auto &ip : addrs)
    {
        std::string etcd_addr = ip;
        etcd_addr += ":8507";
        addr_list.push_back(etcd_addr);
        AK_LOG_INFO << "new ip: " << etcd_addr;
        
        etcd_str << etcd_addr << ",";
    }
    dns_res_ = 1;//解析过了    
    if (etcd_ && addr_list.size() > 0)
    {
        etcd_->UpdateEtcdAddrs(addr_list);
    }
    addr_list_ = etcd_str.str();
    return 0;
}

void CEtcdDnsManager::StartDnsResolver()
{
    addr_list_ = etcdaddr_;
    int need_res = 0;
    std::string etcd_net = etcdaddr_;
    for(unsigned int i = 0; i < etcd_net.size(); i++)
    {
        if(etcd_net[i] >= 'a' && etcd_net[i] <= 'z')
        {
            need_res = 1;
            break;
        }
    }
    
    if (need_res)
    {
        dns_res_ = 0;
    }
    else
    {
        //不需要解析
        dns_res_ = 1;
    }
    
    if (dns_res_ == 0)
    {
        evpp::EventLoop dns_loop;
        AkcsDNSResolver dns(etcdaddr_, &dns_loop);
        dns.SetOnChange(std::bind(&CEtcdDnsManager::OnDnsChange, this, std::placeholders::_1));
        dns_loop.Run();
    }
}






