#ifndef __ETCD_DNS_MANAGE_H__
#define __ETCD_DNS_MANAGE_H__

#include <iostream>
#include <etcd/Client.hpp>
#include <etcd/Watcher.hpp>
#include <boost/noncopyable.hpp>
#include <evpp/event_loop.h>
#include <set>
#include <mutex>
#include <map>
#include <vector>
#include "EtcdCliMng.h"
#include "AkcsDnsResolver.h"

class CEtcdDnsManager;
extern CEtcdDnsManager* g_etcd_dns_mng;

class CEtcdDnsManager : boost::noncopyable
{

public:
    CEtcdDnsManager(const std::string &etcdaddr)
    {
        etcd_ = nullptr;
        etcdaddr_ = etcdaddr;
        dns_res_ = 0;
    }
    void StartDnsResolver();
    int OnDnsChange(const std::vector <std::string>& addrs);
    void SetEtcdCli(CAkEtcdCliManager* etcd)
    {
        etcd_ = etcd;
    }
    int DnsIsOk()
    {
        return dns_res_;
    }

    std::string GetAddrs()
    {
        return addr_list_;
    }

    
    ~CEtcdDnsManager()
    {   
        
    }    
    
public:
    int dns_res_;
    CAkEtcdCliManager* etcd_;
    std::string etcdaddr_;
    std::string addr_list_;
};


#endif // __CSBASE_ETCD_CLI_MANAGE_H__

