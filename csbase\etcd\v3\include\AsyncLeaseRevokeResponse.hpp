#ifndef __ASYNC_LEASEREVOKERESPONSE_HPP__
#define __ASYNC_LEASEREVOKERESPONSE_HPP__

#include <grpc++/grpc++.h>
#include "proto/rpc.grpc.pb.h"
#include "v3/include/V3Response.hpp"


using etcdserverpb::LeaseRevokeResponse;

namespace etcdv3
{
  class AsyncLeaseRevokeResponse : public etcdv3::V3Response
  {
    public:
      AsyncLeaseRevokeResponse(){};
      void ParseResponse(LeaseRevokeResponse& resp);
  };
}

#endif
