#ifndef AKUVOX_H
#define AKUVOX_H

/*
csbase里面的代码更新后，怎样编译最新的库？ ---192.168.79.213:/home/<USER>/evpp-20190409/v0.7.0/ 替换里面的evpp文件夹 ps:如果要编译libevnsq 则也要修改/home/<USER>/evpp-20190409/v0.7.0/apps/evnsq里的内容
release版本:
mkdir -p /home/<USER>/evpp-20190409/v0.7.0/tools/../build-release
cd /home/<USER>/evpp-20190409/v0.7.0/tools/../build-release
cmake -DCMAKE_BUILD_TYPE=release /home/<USER>/evpp-20190409/v0.7.0/tools/..
make -j2

*/

#ifndef AKCS_NSQ_CONNECT_ERROR
/*
name:czw
date:20210129
note:新增NSQ连接失败回调函数
*/
#define AKCS_NSQ_CONNECT_ERROR
#endif

#ifndef AKCS_FIX_MEMORY_LEAK
/**
 * date:2021/3/22
 * author:chenweiwei
 * fix meomry leak,leak at Client::ConnectToLookupd function 
 */
#define AKCS_FIX_MEMORY_LEAK
#endif

#ifndef AKCS_FIX_IP_HASH
/**
 * date:2021/4/22
 * author:chenweiwei
 * fix evpp http server ip hash,because sock::ToIPPort returns empty string
 */
#define AKCS_FIX_IP_HASH
#endif

#ifndef AKCS_RATE_LIMIT
#define AKCS_RATE_LIMIT
/**
 * date:2021/10/21
 * author:chenweiwei
 * limit tcp connection
 */
#endif

#ifndef AKCS_EVENT_FILTER
#define AKCS_EVENT_FILTER
/**
 * date:2022/1/25
 * author:chenyicong
 * AKCS框架引入事件过滤机制,线上的环境不允许开启这个选项，必须使能 #undef AKCS_EVENT_FILTER
 */
#undef AKCS_EVENT_FILTER
#endif


#ifndef AKCS_NSQ_DISCONNECT
#define AKCS_NSQ_DISCONNECT
/**
 * date:2022/02/23
 * author:chenzhx
 * nsqd回复消息格式错误后，调用：OnConnectedFailed 没有重新连接。目前我们业务逻辑可以一直进行尝试
 */
#endif

#ifndef AKCS_UPDATE_NSQLOOKUPD
#define AKCS_UPDATE_NSQLOOKUPD
/**
 * date:2022/03/31
 * author:chenzhx
 * nsqlookupd服务器列表更新重置之前的timer
 */
#endif


#endif

