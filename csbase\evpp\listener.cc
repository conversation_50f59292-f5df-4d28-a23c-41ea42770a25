#include "evpp/inner_pre.h"

#include "evpp/listener.h"
#include "evpp/event_loop.h"
#include "evpp/fd_channel.h"
#include "evpp/libevent.h"
#include "evpp/sockets.h"

namespace evpp {
Listener::Listener(EventLoop* l, const std::string& addr)
    : loop_(l), addr_(addr) {
    DLOG_TRACE << "addr=" << addr;
}

Listener::~Listener() {
    DLOG_TRACE << "fd=" << chan_->fd();
    chan_.reset();
    EVUTIL_CLOSESOCKET(fd_);
    fd_ = INVALID_SOCKET;
}

void Listener::Listen() {
    DLOG_TRACE;
    std::string host;
    short port = 0;
    bool is_ipv6 = false;
    int ret;
    sock::ParseAddr(addr_, host, port, is_ipv6);
    if (!is_ipv6) //ipv4
    {
        fd_ = sock::CreateNonblockingSocket();
        if (fd_ < 0) {
            int serrno = errno;
            LOG_FATAL << "Create a nonblocking socket failed " << strerror(serrno);
            return;
        }
        //ipv4
        struct sockaddr_in addr;
        bzero(&addr, sizeof(addr));
        addr.sin_family = AF_INET;
        //TODO,chenyc,2019-03-15,这个应该根据inet_pton(addr_)来的,而不应该直接监听 INADDR_ANY, 另 INADDR_ANY已经是网络序了,没必要再htonl.
        addr.sin_port = htons(port);
		//addr.sin_addr.s_addr = htonl(INADDR_ANY);
		addr.sin_addr.s_addr = inet_addr(host.c_str());
        
        // TODO Add retry when failed
        ret = ::bind(fd_, sock::sockaddr_cast(&addr), static_cast<socklen_t>(sizeof addr));
        if (ret < 0) {
            int serrno = errno;
            LOG_FATAL << "bind error :" << strerror(serrno);
        }
    }
    else //ipv6
    {
        fd_ = sock::CreateNonblockingSocketIPv6(); 
        if (fd_ < 0) {
            int serrno = errno;
            LOG_FATAL << "Create a nonblocking socket failed " << strerror(serrno);
            return;
        }
        //ipv6
        struct sockaddr_in6 addr2;
        bzero(&addr2, sizeof(addr2));
        addr2.sin6_family = AF_INET6;
        addr2.sin6_addr = in6addr_any;
        addr2.sin6_port = htons(port);
        
        // TODO Add retry when failed
        ret = ::bind(fd_, sock::sockaddr_cast(&addr2), static_cast<socklen_t>(sizeof addr2));
        if (ret < 0) {
            int serrno = errno;
            LOG_FATAL << "bind error :" << strerror(serrno);
        }
    }

    ret = ::listen(fd_, SOMAXCONN);
    if (ret < 0) {
        int serrno = errno;
        LOG_FATAL << "Listen failed " << strerror(serrno);
    }
}

void Listener::Accept() {
    DLOG_TRACE;
    chan_.reset(new FdChannel(loop_, fd_, true, false));
    chan_->SetReadCallback(std::bind(&Listener::HandleAccept, this));
    loop_->RunInLoop(std::bind(&FdChannel::AttachToLoop, chan_.get()));
    LOG_INFO << "TCPServer is running at " << addr_;
}

void Listener::HandleAccept() {
    DLOG_TRACE << "A new connection is comming in";
    assert(loop_->IsInLoopThread());
    struct sockaddr_storage ss;
    socklen_t addrlen = sizeof(ss);
    int nfd = -1;
    if ((nfd = ::accept(fd_, sock::sockaddr_cast(&ss), &addrlen)) == -1) {
        int serrno = errno;
        if (serrno != EAGAIN && serrno != EINTR) {
            //modified by chenyc, 2021.10.09,将LOG_WARN改成LOG_ERROR(因为EVPP默认的日志打印级别跟上层应用没有统一，只打印error级别的日志),
            //允许accept失败的情况下,evpp的日志能够打印，方便定位问题. 避免在进程的文件描述符被限制的时候,日志无法打印错误情况
            LOG_ERROR << __FUNCTION__ << " bad accept " << strerror(serrno);
        }
        return;
    }

    if (evutil_make_socket_nonblocking(nfd) < 0) {
        LOG_ERROR << "set fd=" << nfd << " nonblocking failed.";
        EVUTIL_CLOSESOCKET(nfd);
        return;
    }

    sock::SetKeepAlive(nfd, true);

    std::string raddr = sock::ToIPPort(&ss);
    if (raddr.empty()) {
        LOG_ERROR << "sock::ToIPPort(&ss) failed.";
        EVUTIL_CLOSESOCKET(nfd);
        return;
    }

    DLOG_TRACE << "accepted a connection from " << raddr
        << ", listen fd=" << fd_
        << ", client fd=" << nfd;

    if (new_conn_fn_) {
        new_conn_fn_(nfd, raddr, sock::sockaddr_in_cast(&ss));
    }
}

void Listener::Stop() {
    assert(loop_->IsInLoopThread());
    chan_->DisableAllEvent();
    chan_->Close();
}
}
