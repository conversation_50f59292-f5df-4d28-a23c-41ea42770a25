#pragma once

#include "evpp/platform_config.h"

#ifdef __cplusplus
#define GOOGLE_GLOG_DLL_DECL           // 使用静态glog库时，必须定义这个
#define GLOG_NO_ABBREVIATED_SEVERITIES // 没这个编译会出错,传说因为和Windows.h冲突

#include <glog/logging.h>

#ifdef GOOGLE_STRIP_LOG

#if GOOGLE_STRIP_LOG == 0
#define LOG_TRACE LOG(INFO)
#define LOG_DEBUG LOG(INFO)
#define LOG_INFO  LOG(INFO)
#define DLOG_TRACE LOG(INFO) << __PRETTY_FUNCTION__ << " this=" << this << " "
#else
#define LOG_TRACE if (false) LOG(INFO)
#ifndef LOG_DEBUG
#define LOG_DEBUG if (false) LOG(INFO)
#endif
#define LOG_INFO  if (false) LOG(INFO)
#define DLOG_TRACE if (false) LOG(INFO)
#endif
//modified by chenyc,将告警视为info,避免过多的evpp内部日志打印.
#if GOOGLE_STRIP_LOG <= 1
#define LOG_WARN  LOG(INFO)
#define DLOG_WARN LOG(INFO) << __PRETTY_FUNCTION__ << " this=" << this << " "
#else
#define LOG_WARN  if (false) LOG(INFO)
#define DLOG_WARN if (false) LOG(INFO)
#endif

#define LOG_ERROR LOG(ERROR)
#define LOG_FATAL LOG(FATAL)

#else //#ifdef GOOGLE_STRIP_LOG
#define LOG_TRACE std::cout << __FILE__ << ":" << __LINE__ << " "
#ifndef LOG_DEBUG
#define LOG_DEBUG std::cout << __FILE__ << ":" << __LINE__ << " "
#endif
#define LOG_INFO  std::cout << __FILE__ << ":" << __LINE__ << " "
#define LOG_WARN  std::cout << __FILE__ << ":" << __LINE__ << " "
#define LOG_ERROR std::cout << __FILE__ << ":" << __LINE__ << " "
#define LOG_FATAL std::cout << __FILE__ << ":" << __LINE__ << " "
#define CHECK_NOTnullptr(val) LOG_ERROR << "'" #val "' Must be non nullptr";
#endif

/* delete by chenzhx
//added by chenyc,2017-11-29,增加AK日志宏

#define AK_LOG_INFO  LOG(INFO)
#define AK_DLOG_INFO LOG(INFO) << __PRETTY_FUNCTION__ << " this=" << this << " "
#define AK_LOG_WARN  LOG(WARNING)
#define AK_DLOG_WARN LOG(WARNING) << __PRETTY_FUNCTION__ << " this=" << this << " "
#define AK_LOG_ERROR LOG(ERROR)
#define AK_LOG_FATAL LOG(FATAL)
*/

#endif // end of define __cplusplus

//#ifdef _DEBUG
//#ifdef assert
//#undef assert
//#endif
//#define assert(expr)  { if (!(expr)) { LOG_FATAL << #expr ;} }
//#endif