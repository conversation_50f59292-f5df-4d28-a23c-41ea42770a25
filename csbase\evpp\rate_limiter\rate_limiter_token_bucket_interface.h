#ifndef __akcs_rate_limiter_token_bucket_interface_h__
#define __akcs_rate_limiter_token_bucket_interface_h__

//基于令牌桶算法实现的限流器
//最大qps为1,000,000,000,最小为1
//使用：
// RateLimiter r(100,100);
// r.Acquire();
//能通过r.acquire()函数即可保证流速

namespace evpp {
namespace rate_limiter {
class RateLimiterTokenBucketInterface 
{
public:
    virtual ~RateLimiterTokenBucketInterface() {};
    //对外接口，能返回说明流量在限定值内
    virtual void Acquire() = 0;
    //对外接口，尝试获取token,不管成不成功,都立即返回
    //返回值,获取成功:true, 获取失败:false
    virtual bool TryAcquire() = 0;
    //设置速率
    virtual void SetRate(uint32_t qps) = 0;

};
}// rate_limiter
}// evpp
#endif //__akcs_rate_limiter_token_bucket_interface_h__