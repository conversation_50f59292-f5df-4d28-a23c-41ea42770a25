#pragma once

#include "evpp/sys_addrinfo.h"
#include "evpp/sys_sockets.h"

#include <string.h>

namespace evpp {

class Duration;

EVPP_EXPORT std::string strerror(int e);

namespace sock {

EVPP_EXPORT evpp_socket_t CreateNonblockingSocket();
EVPP_EXPORT evpp_socket_t CreateNonblockingSocketIPv6();//added by chency

EVPP_EXPORT evpp_socket_t CreateUDPServer(int port);
EVPP_EXPORT void SetKeepAlive(evpp_socket_t fd, bool on);
EVPP_EXPORT void SetReuseAddr(evpp_socket_t fd);
EVPP_EXPORT void SetReusePort(evpp_socket_t fd);
EVPP_EXPORT void SetTCPNoDelay(evpp_socket_t fd, bool on);
EVPP_EXPORT void SetTimeout(evpp_socket_t fd, uint32_t timeout_ms);
EVPP_EXPORT void SetTimeout(evpp_socket_t fd, const Duration& timeout);
EVPP_EXPORT std::string ToIPPort(const struct sockaddr_storage* ss);
EVPP_EXPORT std::string ToIPPort(const struct sockaddr* ss);
EVPP_EXPORT std::string ToIPPort(const struct sockaddr_in* ss);
EVPP_EXPORT std::string ToIP(const struct sockaddr* ss);


// @brief Parse a literal network address and return an internet protocol family address
// @param[in] address - A network address of the form "host:port" or "[host]:port"
// @return bool - false if parse failed.
EVPP_EXPORT bool ParseFromIPPort(const char* address, struct sockaddr_storage& ss);

inline struct sockaddr_storage ParseFromIPPort(const char* address) {
    struct sockaddr_storage ss;
    memset(&ss, 0, sizeof(ss));
    ParseFromIPPort(address, ss);
    return ss;
}

//added by chenyc,确定应用层传进来的是否是ipv6的协议栈
inline int ParseAddr(const std::string& address,std::string& host, short& port, bool& is_ipv6) {
    if (address.empty()) {
        return -1;
    }

    size_t index = address.rfind(':');
    if (index == std::string::npos) {
        //LOG_ERROR << "Address specified error <" << address << ">. Cannot find ':'";
        return -1;
    }

    if (index == address.size() - 1) {
        return -1;
    }

    port = std::atoi(&address[index + 1]);

    host = std::string(address, 0, index);
    index = host.find(':');
    if (index != std::string::npos) {
        is_ipv6 = true;
    }
    
    if (host[0] == '[') { //查看是否是ipv6[:::]:xx的格式
        if (*host.rbegin() != ']') {
            //LOG_ERROR << "Address specified error <" << address << ">. '[' ']' is not pair.";
            return false;
        }

        // trim the leading '[' and trail ']'
        host = std::string(host.data() + 1, host.size() - 2);
    }

    return 0;
}


// @brief Splits a network address of the form "host:port" or "[host]:port"
//  into host and port. A literal address or host name for IPv6
// must be enclosed in square brackets, as in "[::1]:80" or "[ipv6-host]:80"
// @param[in] address - A network address of the form "host:port" or "[ipv6-host]:port"
// @param[out] host -
// @param[out] port - the port in local machine byte order
// @return bool - false if the network address is invalid format
EVPP_EXPORT bool SplitHostPort(const char* address, std::string& host, int& port);

EVPP_EXPORT struct sockaddr_storage GetLocalAddr(evpp_socket_t sockfd);

inline bool IsZeroAddress(const struct sockaddr_storage* ss) {
    const char* p = reinterpret_cast<const char*>(ss);
    for (size_t i = 0; i < sizeof(*ss); ++i) {
        if (p[i] != 0) {
            return false;
        }
    }
    return true;
}

template<typename To, typename From>
inline To implicit_cast(From const& f) {
    return f;
}

inline const struct sockaddr* sockaddr_cast(const struct sockaddr_in* addr) {
    return static_cast<const struct sockaddr*>(evpp::sock::implicit_cast<const void*>(addr));
}

inline struct sockaddr* sockaddr_cast(struct sockaddr_in* addr) {
    return static_cast<struct sockaddr*>(evpp::sock::implicit_cast<void*>(addr));
}

inline struct sockaddr* sockaddr_cast(struct sockaddr_in6* addr) {
    return static_cast<struct sockaddr*>(evpp::sock::implicit_cast<void*>(addr));
}

inline struct sockaddr* sockaddr_cast(struct sockaddr_storage* addr) {
    return static_cast<struct sockaddr*>(evpp::sock::implicit_cast<void*>(addr));
}

inline const struct sockaddr_in* sockaddr_in_cast(const struct sockaddr* addr) {
    return static_cast<const struct sockaddr_in*>(evpp::sock::implicit_cast<const void*>(addr));
}

inline struct sockaddr_in* sockaddr_in_cast(struct sockaddr* addr) {
    return static_cast<struct sockaddr_in*>(evpp::sock::implicit_cast<void*>(addr));
}

inline struct sockaddr_in* sockaddr_in_cast(struct sockaddr_storage* addr) {
    return static_cast<struct sockaddr_in*>(evpp::sock::implicit_cast<void*>(addr));
}

inline struct sockaddr_in6* sockaddr_in6_cast(struct sockaddr_storage* addr) {
    return static_cast<struct sockaddr_in6*>(evpp::sock::implicit_cast<void*>(addr));
}

inline const struct sockaddr_in* sockaddr_in_cast(const struct sockaddr_storage* addr) {
    return static_cast<const struct sockaddr_in*>(evpp::sock::implicit_cast<const void*>(addr));
}

inline const struct sockaddr_in6* sockaddr_in6_cast(const struct sockaddr_storage* addr) {
    return static_cast<const struct sockaddr_in6*>(evpp::sock::implicit_cast<const void*>(addr));
}

inline const struct sockaddr_storage* sockaddr_storage_cast(const struct sockaddr* addr) {
    return static_cast<const struct sockaddr_storage*>(evpp::sock::implicit_cast<const void*>(addr));
}

inline const struct sockaddr_storage* sockaddr_storage_cast(const struct sockaddr_in* addr) {
    return static_cast<const struct sockaddr_storage*>(evpp::sock::implicit_cast<const void*>(addr));
}

inline const struct sockaddr_storage* sockaddr_storage_cast(const struct sockaddr_in6* addr) {
    return static_cast<const struct sockaddr_storage*>(evpp::sock::implicit_cast<const void*>(addr));
}

}

}

#ifdef H_OS_WINDOWS
EVPP_EXPORT int readv(evpp_socket_t sockfd, struct iovec* iov, int iovcnt);
#endif
