//File: SnowFlakeGid.h
// Author: chency
// Date:   2018-09-13

#ifndef __AKCS_BESE_ID_WORKER_H__
#define __AKCS_BESE_ID_WORKER_H__

#include <mutex>
#include <atomic>
#include <chrono>
#include <exception>
#include <sstream>
#include "Singleton.h"

namespace AKCS {

//snowflake算法的数据中心
enum 
{
	AKCS_SF_DC_USA = 1,
	AKCS_SF_DC_EUR = 2,
	AKCS_SF_DC_ASIA = 3,
};
	
/**
 * 分布式id生成类
 * https://github.com/twitter/snowflake/blob/snowflake-2010/src/main/scala/com/twitter/service/snowflake/IdWorker.scala
 *
 * 64bit id: 0000  0000  0000  0000  0000  0000  0000  0000  0000  0000  0000  0000  0000  0000  0000  0000 
 *           ||                                                           ||     ||     |  |              | 
 *           |└---------------------------时间戳--------------------------┘└中心-┘└机器-┘         └----序列号----┘ 
 *           |
 *         不用
 * SnowFlake的优点: 整体上按照时间自增排序, 并且整个分布式系统内不会产生ID碰撞(由数据中心ID和机器ID作区分), 并且效率较高
 */
class SnowflakeIdWorker{

    // 实现单例
    friend class AKCS::Singleton<SnowflakeIdWorker>;
	// 用法:AKCS::Singleton<SnowflakeIdWorker>::instance().xx(),
	// 单例使用时，需要先初始化中心id跟机器id.推荐现在主线程初始化，然后在调用getId接口
	// AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().setWorkerId(10);
	// AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().setDatacenterId(10);
	// 然后在业务需要的地方调用:
	// uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();

public:
    typedef unsigned int UInt;
    typedef unsigned long long int UInt64;
    typedef std::atomic<UInt> AtomicUInt;
    typedef std::atomic<UInt64> AtomicUInt64;
    //使用之前先初始化 workerId 跟 datacenterId
    void setWorkerId(UInt workerId)
    {
        this->workerId = workerId;
    }

    void setDatacenterId(UInt datacenterId)
    {
        this->datacenterId = datacenterId;
    }

    UInt64 getId()
    {
        return nextId();
    }

    /**
     * 获得下一个ID (该方法是线程安全的)
     *
     * @return SnowflakeId
     */
    UInt64 nextId()
    {
        static AtomicUInt64 timestamp{ 0 };
        timestamp = timeGen();
        // 如果当前时间小于上一次ID生成的时间戳，说明系统时钟回退过这个时候应当抛出异常,即若发生时间回拨，则一定有一次当前时间戳小于上回时间戳的情况发生
        // modified by chenyc,2023-10-07, 解决csmain拆分csresid后，出现崩溃的问题.参考: http://192.168.10.102:8071/pages/viewpage.action?pageId=58786389
        // 当发生时间毫秒级别回退的时候,也不能抛出异常，否则上层应用没有捕获异常的时候会发生进程崩溃.
        // 逻辑修改如下: 当发生时间毫秒级回退时,原地等待直到机器时间跑到下一个毫秒.   
        // TODO，考虑一点，如果发生时间回拨超过几秒、几分钟、几小时怎么办?线程直接卡死在这里,此时只能重启进程解决.
        // 另外一种做法就是返回一个无效的id,但是如果上层应用假设这个id永远是递增的,并基于这个假设来做业务逻辑,岂不是造成业务的bug? 目前暂时没有两全的方案,暂时采用第一种原地等待的方案
        if (timestamp < lastTimestamp)
        {
            //std::ostringstream s;
            //s << "clock moved backwards.  Refusing to generate id for " << lastTimestamp - timestamp << " milliseconds";
            //throw std::exception(std::runtime_error(s.str()));
            timestamp = tilNextMillis(lastTimestamp);
        }

        if (lastTimestamp == timestamp)
		{
            // 如果是同一时间生成的，则进行毫秒内序列
            sequence = (sequence + 1) & sequenceMask;
            if (0 == sequence)
			{
                // 毫秒内序列溢出, 阻塞到下一个毫秒,获得新的时间戳
                timestamp = tilNextMillis(lastTimestamp);
            }
        }
		else
		{
            sequence = 0;
        }
        lastTimestamp = timestamp.load();

        // 移位并通过或运算拼到一起组成64位的ID
        return ((timestamp - twepoch) << timestampLeftShift)
		        | (datacenterId << datacenterIdShift)
		        | (workerId << workerIdShift)
		        | sequence;
    }

protected:
    SnowflakeIdWorker() : workerId(0), datacenterId(0), sequence(0), lastTimestamp(0) { }

    /**
     * 返回以毫秒为单位的当前时间
     *
     * @return 当前时间(毫秒)
     */
    UInt64 timeGen() const
    {
        auto t = std::chrono::time_point_cast<std::chrono::milliseconds>(std::chrono::high_resolution_clock::now());
        return t.time_since_epoch().count();
    }

    /**
     * 阻塞到下一个毫秒，直到获得新的时间戳
     *
     * @param lastTimestamp 上次生成ID的时间截
     * @return 当前时间戳
     */
    UInt64 tilNextMillis(UInt64 lastTimestamp) const
    {
        UInt64 timestamp = timeGen();
        while (timestamp <= lastTimestamp)
		{
            timestamp = timeGen();
        }
        return timestamp;
    }

private:

    /**
     * 开始时间截 (2018-01-01 00:00:00.000)
     */
    const UInt64 twepoch = 1514736000000;

    /**
     * 机器id所占的位数
     */
    const UInt workerIdBits = 5;

    /**
     * 数据中心id所占的位数
     */
    const UInt datacenterIdBits = 5;

    /**
     * 序列所占的位数
     */
    const UInt sequenceBits = 12;

    /**
     * 机器ID向左移12位
     */
    const UInt workerIdShift = sequenceBits;

    /**
     * 数据标识id向左移17位
     */
    const UInt datacenterIdShift = workerIdShift + workerIdBits;

    /**
     * 时间截向左移22位
     */
    const UInt timestampLeftShift = datacenterIdShift + datacenterIdBits;

    /**
     * 支持的最大机器id，结果是31
     */
    const UInt maxWorkerId = -1 ^ (-1 << workerIdBits);

    /**
     * 支持的最大数据中心id，结果是31
     */
    const UInt maxDatacenterId = -1 ^ (-1 << datacenterIdBits);

    /**
     * 生成序列的掩码，这里为4095,即：2^12 = 4096
     */
    const UInt sequenceMask = -1 ^ (-1 << sequenceBits);

    /**
     * 工作机器id(0~31)
     */
    UInt workerId;

    /**
     * 数据中心id(0~31)
     */
    UInt datacenterId;

    /**
     * 毫秒内序列(0~4095)
     */
    AtomicUInt sequence{ 0 };

    /**
     * 上次生成ID的时间截
     */
    AtomicUInt64 lastTimestamp{ 0 };

};

typedef SnowflakeIdWorker IdWorker;
}

#endif // __AKCS_BESE_ID_WORKER_H__
