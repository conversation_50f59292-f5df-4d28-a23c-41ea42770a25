// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: csvs.proto

#include "csvs.pb.h"
#include "csvs.grpc.pb.h"

#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/method_handler_impl.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace VideoStorage {

static const char* VideoStorageMsg_method_names[] = {
  "/VideoStorage.VideoStorageMsg/VideoStorageHandle",
  "/VideoStorage.VideoStorageMsg/DelVideoStorageHandle",
};

std::unique_ptr< VideoStorageMsg::Stub> VideoStorageMsg::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< VideoStorageMsg::Stub> stub(new VideoStorageMsg::Stub(channel));
  return stub;
}

VideoStorageMsg::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel)
  : channel_(channel), rpcmethod_VideoStorageHandle_(VideoStorageMsg_method_names[0], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_DelVideoStorageHandle_(VideoStorageMsg_method_names[1], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status VideoStorageMsg::Stub::VideoStorageHandle(::grpc::ClientContext* context, const ::VideoStorage::VsRequest& request, ::VideoStorage::VsReply* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_VideoStorageHandle_, context, request, response);
}

::grpc::ClientAsyncResponseReader< ::VideoStorage::VsReply>* VideoStorageMsg::Stub::AsyncVideoStorageHandleRaw(::grpc::ClientContext* context, const ::VideoStorage::VsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::VideoStorage::VsReply>::Create(channel_.get(), cq, rpcmethod_VideoStorageHandle_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::VideoStorage::VsReply>* VideoStorageMsg::Stub::PrepareAsyncVideoStorageHandleRaw(::grpc::ClientContext* context, const ::VideoStorage::VsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::VideoStorage::VsReply>::Create(channel_.get(), cq, rpcmethod_VideoStorageHandle_, context, request, false);
}

::grpc::Status VideoStorageMsg::Stub::DelVideoStorageHandle(::grpc::ClientContext* context, const ::VideoStorage::VsDelRequest& request, ::VideoStorage::VsDelReply* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_DelVideoStorageHandle_, context, request, response);
}

::grpc::ClientAsyncResponseReader< ::VideoStorage::VsDelReply>* VideoStorageMsg::Stub::AsyncDelVideoStorageHandleRaw(::grpc::ClientContext* context, const ::VideoStorage::VsDelRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::VideoStorage::VsDelReply>::Create(channel_.get(), cq, rpcmethod_DelVideoStorageHandle_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::VideoStorage::VsDelReply>* VideoStorageMsg::Stub::PrepareAsyncDelVideoStorageHandleRaw(::grpc::ClientContext* context, const ::VideoStorage::VsDelRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::VideoStorage::VsDelReply>::Create(channel_.get(), cq, rpcmethod_DelVideoStorageHandle_, context, request, false);
}

VideoStorageMsg::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      VideoStorageMsg_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< VideoStorageMsg::Service, ::VideoStorage::VsRequest, ::VideoStorage::VsReply>(
          std::mem_fn(&VideoStorageMsg::Service::VideoStorageHandle), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      VideoStorageMsg_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< VideoStorageMsg::Service, ::VideoStorage::VsDelRequest, ::VideoStorage::VsDelReply>(
          std::mem_fn(&VideoStorageMsg::Service::DelVideoStorageHandle), this)));
}

VideoStorageMsg::Service::~Service() {
}

::grpc::Status VideoStorageMsg::Service::VideoStorageHandle(::grpc::ServerContext* context, const ::VideoStorage::VsRequest* request, ::VideoStorage::VsReply* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status VideoStorageMsg::Service::DelVideoStorageHandle(::grpc::ServerContext* context, const ::VideoStorage::VsDelRequest* request, ::VideoStorage::VsDelReply* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace VideoStorage

