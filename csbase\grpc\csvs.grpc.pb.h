// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: csvs.proto
#ifndef GRPC_csvs_2eproto__INCLUDED
#define GRPC_csvs_2eproto__INCLUDED

#include "csvs.pb.h"

#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/method_handler_impl.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace grpc {
class CompletionQueue;
class Channel;
class ServerCompletionQueue;
class ServerContext;
}  // namespace grpc

namespace VideoStorage {

// The adding service definition.
class VideoStorageMsg final {
 public:
  static constexpr char const* service_full_name() {
    return "VideoStorage.VideoStorageMsg";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    // Sends a video storage action
    virtual ::grpc::Status VideoStorageHandle(::grpc::ClientContext* context, const ::VideoStorage::VsRequest& request, ::VideoStorage::VsReply* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::VideoStorage::VsReply>> AsyncVideoStorageHandle(::grpc::ClientContext* context, const ::VideoStorage::VsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::VideoStorage::VsReply>>(AsyncVideoStorageHandleRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::VideoStorage::VsReply>> PrepareAsyncVideoStorageHandle(::grpc::ClientContext* context, const ::VideoStorage::VsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::VideoStorage::VsReply>>(PrepareAsyncVideoStorageHandleRaw(context, request, cq));
    }
    virtual ::grpc::Status DelVideoStorageHandle(::grpc::ClientContext* context, const ::VideoStorage::VsDelRequest& request, ::VideoStorage::VsDelReply* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::VideoStorage::VsDelReply>> AsyncDelVideoStorageHandle(::grpc::ClientContext* context, const ::VideoStorage::VsDelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::VideoStorage::VsDelReply>>(AsyncDelVideoStorageHandleRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::VideoStorage::VsDelReply>> PrepareAsyncDelVideoStorageHandle(::grpc::ClientContext* context, const ::VideoStorage::VsDelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::VideoStorage::VsDelReply>>(PrepareAsyncDelVideoStorageHandleRaw(context, request, cq));
    }
  private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::VideoStorage::VsReply>* AsyncVideoStorageHandleRaw(::grpc::ClientContext* context, const ::VideoStorage::VsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::VideoStorage::VsReply>* PrepareAsyncVideoStorageHandleRaw(::grpc::ClientContext* context, const ::VideoStorage::VsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::VideoStorage::VsDelReply>* AsyncDelVideoStorageHandleRaw(::grpc::ClientContext* context, const ::VideoStorage::VsDelRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::VideoStorage::VsDelReply>* PrepareAsyncDelVideoStorageHandleRaw(::grpc::ClientContext* context, const ::VideoStorage::VsDelRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel);
    ::grpc::Status VideoStorageHandle(::grpc::ClientContext* context, const ::VideoStorage::VsRequest& request, ::VideoStorage::VsReply* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::VideoStorage::VsReply>> AsyncVideoStorageHandle(::grpc::ClientContext* context, const ::VideoStorage::VsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::VideoStorage::VsReply>>(AsyncVideoStorageHandleRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::VideoStorage::VsReply>> PrepareAsyncVideoStorageHandle(::grpc::ClientContext* context, const ::VideoStorage::VsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::VideoStorage::VsReply>>(PrepareAsyncVideoStorageHandleRaw(context, request, cq));
    }
    ::grpc::Status DelVideoStorageHandle(::grpc::ClientContext* context, const ::VideoStorage::VsDelRequest& request, ::VideoStorage::VsDelReply* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::VideoStorage::VsDelReply>> AsyncDelVideoStorageHandle(::grpc::ClientContext* context, const ::VideoStorage::VsDelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::VideoStorage::VsDelReply>>(AsyncDelVideoStorageHandleRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::VideoStorage::VsDelReply>> PrepareAsyncDelVideoStorageHandle(::grpc::ClientContext* context, const ::VideoStorage::VsDelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::VideoStorage::VsDelReply>>(PrepareAsyncDelVideoStorageHandleRaw(context, request, cq));
    }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    ::grpc::ClientAsyncResponseReader< ::VideoStorage::VsReply>* AsyncVideoStorageHandleRaw(::grpc::ClientContext* context, const ::VideoStorage::VsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::VideoStorage::VsReply>* PrepareAsyncVideoStorageHandleRaw(::grpc::ClientContext* context, const ::VideoStorage::VsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::VideoStorage::VsDelReply>* AsyncDelVideoStorageHandleRaw(::grpc::ClientContext* context, const ::VideoStorage::VsDelRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::VideoStorage::VsDelReply>* PrepareAsyncDelVideoStorageHandleRaw(::grpc::ClientContext* context, const ::VideoStorage::VsDelRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_VideoStorageHandle_;
    const ::grpc::internal::RpcMethod rpcmethod_DelVideoStorageHandle_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    // Sends a video storage action
    virtual ::grpc::Status VideoStorageHandle(::grpc::ServerContext* context, const ::VideoStorage::VsRequest* request, ::VideoStorage::VsReply* response);
    virtual ::grpc::Status DelVideoStorageHandle(::grpc::ServerContext* context, const ::VideoStorage::VsDelRequest* request, ::VideoStorage::VsDelReply* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_VideoStorageHandle : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_VideoStorageHandle() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_VideoStorageHandle() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status VideoStorageHandle(::grpc::ServerContext* context, const ::VideoStorage::VsRequest* request, ::VideoStorage::VsReply* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestVideoStorageHandle(::grpc::ServerContext* context, ::VideoStorage::VsRequest* request, ::grpc::ServerAsyncResponseWriter< ::VideoStorage::VsReply>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_DelVideoStorageHandle : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_DelVideoStorageHandle() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_DelVideoStorageHandle() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DelVideoStorageHandle(::grpc::ServerContext* context, const ::VideoStorage::VsDelRequest* request, ::VideoStorage::VsDelReply* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDelVideoStorageHandle(::grpc::ServerContext* context, ::VideoStorage::VsDelRequest* request, ::grpc::ServerAsyncResponseWriter< ::VideoStorage::VsDelReply>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_VideoStorageHandle<WithAsyncMethod_DelVideoStorageHandle<Service > > AsyncService;
  template <class BaseClass>
  class WithGenericMethod_VideoStorageHandle : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_VideoStorageHandle() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_VideoStorageHandle() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status VideoStorageHandle(::grpc::ServerContext* context, const ::VideoStorage::VsRequest* request, ::VideoStorage::VsReply* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_DelVideoStorageHandle : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_DelVideoStorageHandle() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_DelVideoStorageHandle() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DelVideoStorageHandle(::grpc::ServerContext* context, const ::VideoStorage::VsDelRequest* request, ::VideoStorage::VsDelReply* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_VideoStorageHandle : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_VideoStorageHandle() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_VideoStorageHandle() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status VideoStorageHandle(::grpc::ServerContext* context, const ::VideoStorage::VsRequest* request, ::VideoStorage::VsReply* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestVideoStorageHandle(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_DelVideoStorageHandle : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_DelVideoStorageHandle() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_DelVideoStorageHandle() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DelVideoStorageHandle(::grpc::ServerContext* context, const ::VideoStorage::VsDelRequest* request, ::VideoStorage::VsDelReply* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDelVideoStorageHandle(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_VideoStorageHandle : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithStreamedUnaryMethod_VideoStorageHandle() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler< ::VideoStorage::VsRequest, ::VideoStorage::VsReply>(std::bind(&WithStreamedUnaryMethod_VideoStorageHandle<BaseClass>::StreamedVideoStorageHandle, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_VideoStorageHandle() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status VideoStorageHandle(::grpc::ServerContext* context, const ::VideoStorage::VsRequest* request, ::VideoStorage::VsReply* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedVideoStorageHandle(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::VideoStorage::VsRequest,::VideoStorage::VsReply>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_DelVideoStorageHandle : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithStreamedUnaryMethod_DelVideoStorageHandle() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler< ::VideoStorage::VsDelRequest, ::VideoStorage::VsDelReply>(std::bind(&WithStreamedUnaryMethod_DelVideoStorageHandle<BaseClass>::StreamedDelVideoStorageHandle, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_DelVideoStorageHandle() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status DelVideoStorageHandle(::grpc::ServerContext* context, const ::VideoStorage::VsDelRequest* request, ::VideoStorage::VsDelReply* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedDelVideoStorageHandle(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::VideoStorage::VsDelRequest,::VideoStorage::VsDelReply>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_VideoStorageHandle<WithStreamedUnaryMethod_DelVideoStorageHandle<Service > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_VideoStorageHandle<WithStreamedUnaryMethod_DelVideoStorageHandle<Service > > StreamedService;
};

}  // namespace VideoStorage


#endif  // GRPC_csvs_2eproto__INCLUDED
