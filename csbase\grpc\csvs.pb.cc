// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: csvs.proto

#include "csvs.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)
namespace VideoStorage {
class VsRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<VsRequest>
      _instance;
} _VsRequest_default_instance_;
class VsReplyDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<VsReply>
      _instance;
} _VsReply_default_instance_;
class VsDelRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<VsDelRequest>
      _instance;
} _VsDelRequest_default_instance_;
class VsDelReplyDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<VsDelReply>
      _instance;
} _VsDelReply_default_instance_;
}  // namespace VideoStorage
namespace protobuf_csvs_2eproto {
void InitDefaultsVsRequestImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::VideoStorage::_VsRequest_default_instance_;
    new (ptr) ::VideoStorage::VsRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::VideoStorage::VsRequest::InitAsDefaultInstance();
}

void InitDefaultsVsRequest() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsVsRequestImpl);
}

void InitDefaultsVsReplyImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::VideoStorage::_VsReply_default_instance_;
    new (ptr) ::VideoStorage::VsReply();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::VideoStorage::VsReply::InitAsDefaultInstance();
}

void InitDefaultsVsReply() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsVsReplyImpl);
}

void InitDefaultsVsDelRequestImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::VideoStorage::_VsDelRequest_default_instance_;
    new (ptr) ::VideoStorage::VsDelRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::VideoStorage::VsDelRequest::InitAsDefaultInstance();
}

void InitDefaultsVsDelRequest() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsVsDelRequestImpl);
}

void InitDefaultsVsDelReplyImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::VideoStorage::_VsDelReply_default_instance_;
    new (ptr) ::VideoStorage::VsDelReply();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::VideoStorage::VsDelReply::InitAsDefaultInstance();
}

void InitDefaultsVsDelReply() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsVsDelReplyImpl);
}

::google::protobuf::Metadata file_level_metadata[4];
const ::google::protobuf::EnumDescriptor* file_level_enum_descriptors[1];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::VideoStorage::VsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::VideoStorage::VsRequest, storage_uid_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::VideoStorage::VsRequest, dev_rtsp_pwd_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::VideoStorage::VsRequest, rtsp_srv_ip_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::VideoStorage::VsRequest, storage_node_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::VideoStorage::VsRequest, act_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::VideoStorage::VsReply, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::VideoStorage::VsReply, hls_uri_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::VideoStorage::VsReply, global_video_id_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::VideoStorage::VsReply, resp_storage_mac_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::VideoStorage::VsReply, resp_storage_node_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::VideoStorage::VsDelRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::VideoStorage::VsDelRequest, global_video_id_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::VideoStorage::VsDelReply, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::VideoStorage::VsDelReply, del_reply_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::VideoStorage::VsRequest)},
  { 10, -1, sizeof(::VideoStorage::VsReply)},
  { 19, -1, sizeof(::VideoStorage::VsDelRequest)},
  { 25, -1, sizeof(::VideoStorage::VsDelReply)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::VideoStorage::_VsRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::VideoStorage::_VsReply_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::VideoStorage::_VsDelRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::VideoStorage::_VsDelReply_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  ::google::protobuf::MessageFactory* factory = NULL;
  AssignDescriptors(
      "csvs.proto", schemas, file_default_instances, TableStruct::offsets, factory,
      file_level_metadata, file_level_enum_descriptors, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 4);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n\ncsvs.proto\022\014VideoStorage\"\220\001\n\tVsRequest"
      "\022\023\n\013storage_uid\030\001 \001(\t\022\024\n\014dev_rtsp_pwd\030\002 "
      "\001(\t\022\023\n\013rtsp_srv_ip\030\003 \001(\t\022\024\n\014storage_node"
      "\030\004 \001(\t\022-\n\003act\030\005 \001(\0162 .VideoStorage.Video"
      "StorageAction\"h\n\007VsReply\022\017\n\007hls_uri\030\001 \001("
      "\t\022\027\n\017global_video_id\030\002 \001(\r\022\030\n\020resp_stora"
      "ge_mac\030\003 \001(\t\022\031\n\021resp_storage_node\030\004 \001(\t\""
      "\'\n\014VsDelRequest\022\027\n\017global_video_id\030\001 \001(\r"
      "\"\037\n\nVsDelReply\022\021\n\tdel_reply\030\001 \001(\t*]\n\022Vid"
      "eoStorageAction\022\026\n\022NULL_VIDEO_STORAGE\020\000\022"
      "\027\n\023START_VIDEO_STORAGE\020\001\022\026\n\022STOP_VIDEO_S"
      "TORAGE\020\0022\252\001\n\017VideoStorageMsg\022F\n\022VideoSto"
      "rageHandle\022\027.VideoStorage.VsRequest\032\025.Vi"
      "deoStorage.VsReply\"\000\022O\n\025DelVideoStorageH"
      "andle\022\032.VideoStorage.VsDelRequest\032\030.Vide"
      "oStorage.VsDelReply\"\000B\t\n\007ex.grpcb\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 640);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "csvs.proto", &protobuf_RegisterTypes);
}

void AddDescriptors() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_csvs_2eproto
namespace VideoStorage {
const ::google::protobuf::EnumDescriptor* VideoStorageAction_descriptor() {
  protobuf_csvs_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_csvs_2eproto::file_level_enum_descriptors[0];
}
bool VideoStorageAction_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}


// ===================================================================

void VsRequest::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int VsRequest::kStorageUidFieldNumber;
const int VsRequest::kDevRtspPwdFieldNumber;
const int VsRequest::kRtspSrvIpFieldNumber;
const int VsRequest::kStorageNodeFieldNumber;
const int VsRequest::kActFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

VsRequest::VsRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_csvs_2eproto::InitDefaultsVsRequest();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:VideoStorage.VsRequest)
}
VsRequest::VsRequest(const VsRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  storage_uid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.storage_uid().size() > 0) {
    storage_uid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.storage_uid_);
  }
  dev_rtsp_pwd_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.dev_rtsp_pwd().size() > 0) {
    dev_rtsp_pwd_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.dev_rtsp_pwd_);
  }
  rtsp_srv_ip_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.rtsp_srv_ip().size() > 0) {
    rtsp_srv_ip_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.rtsp_srv_ip_);
  }
  storage_node_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.storage_node().size() > 0) {
    storage_node_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.storage_node_);
  }
  act_ = from.act_;
  // @@protoc_insertion_point(copy_constructor:VideoStorage.VsRequest)
}

void VsRequest::SharedCtor() {
  storage_uid_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dev_rtsp_pwd_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rtsp_srv_ip_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  storage_node_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  act_ = 0;
  _cached_size_ = 0;
}

VsRequest::~VsRequest() {
  // @@protoc_insertion_point(destructor:VideoStorage.VsRequest)
  SharedDtor();
}

void VsRequest::SharedDtor() {
  storage_uid_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dev_rtsp_pwd_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rtsp_srv_ip_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  storage_node_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void VsRequest::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* VsRequest::descriptor() {
  ::protobuf_csvs_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_csvs_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const VsRequest& VsRequest::default_instance() {
  ::protobuf_csvs_2eproto::InitDefaultsVsRequest();
  return *internal_default_instance();
}

VsRequest* VsRequest::New(::google::protobuf::Arena* arena) const {
  VsRequest* n = new VsRequest;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void VsRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:VideoStorage.VsRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  storage_uid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  dev_rtsp_pwd_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  rtsp_srv_ip_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  storage_node_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  act_ = 0;
  _internal_metadata_.Clear();
}

bool VsRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:VideoStorage.VsRequest)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string storage_uid = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_storage_uid()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->storage_uid().data(), static_cast<int>(this->storage_uid().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "VideoStorage.VsRequest.storage_uid"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string dev_rtsp_pwd = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_dev_rtsp_pwd()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->dev_rtsp_pwd().data(), static_cast<int>(this->dev_rtsp_pwd().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "VideoStorage.VsRequest.dev_rtsp_pwd"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string rtsp_srv_ip = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_rtsp_srv_ip()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->rtsp_srv_ip().data(), static_cast<int>(this->rtsp_srv_ip().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "VideoStorage.VsRequest.rtsp_srv_ip"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string storage_node = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_storage_node()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->storage_node().data(), static_cast<int>(this->storage_node().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "VideoStorage.VsRequest.storage_node"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .VideoStorage.VideoStorageAction act = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_act(static_cast< ::VideoStorage::VideoStorageAction >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:VideoStorage.VsRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:VideoStorage.VsRequest)
  return false;
#undef DO_
}

void VsRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:VideoStorage.VsRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string storage_uid = 1;
  if (this->storage_uid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->storage_uid().data(), static_cast<int>(this->storage_uid().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "VideoStorage.VsRequest.storage_uid");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->storage_uid(), output);
  }

  // string dev_rtsp_pwd = 2;
  if (this->dev_rtsp_pwd().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->dev_rtsp_pwd().data(), static_cast<int>(this->dev_rtsp_pwd().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "VideoStorage.VsRequest.dev_rtsp_pwd");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->dev_rtsp_pwd(), output);
  }

  // string rtsp_srv_ip = 3;
  if (this->rtsp_srv_ip().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->rtsp_srv_ip().data(), static_cast<int>(this->rtsp_srv_ip().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "VideoStorage.VsRequest.rtsp_srv_ip");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->rtsp_srv_ip(), output);
  }

  // string storage_node = 4;
  if (this->storage_node().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->storage_node().data(), static_cast<int>(this->storage_node().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "VideoStorage.VsRequest.storage_node");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->storage_node(), output);
  }

  // .VideoStorage.VideoStorageAction act = 5;
  if (this->act() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      5, this->act(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:VideoStorage.VsRequest)
}

::google::protobuf::uint8* VsRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:VideoStorage.VsRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string storage_uid = 1;
  if (this->storage_uid().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->storage_uid().data(), static_cast<int>(this->storage_uid().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "VideoStorage.VsRequest.storage_uid");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->storage_uid(), target);
  }

  // string dev_rtsp_pwd = 2;
  if (this->dev_rtsp_pwd().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->dev_rtsp_pwd().data(), static_cast<int>(this->dev_rtsp_pwd().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "VideoStorage.VsRequest.dev_rtsp_pwd");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->dev_rtsp_pwd(), target);
  }

  // string rtsp_srv_ip = 3;
  if (this->rtsp_srv_ip().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->rtsp_srv_ip().data(), static_cast<int>(this->rtsp_srv_ip().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "VideoStorage.VsRequest.rtsp_srv_ip");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->rtsp_srv_ip(), target);
  }

  // string storage_node = 4;
  if (this->storage_node().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->storage_node().data(), static_cast<int>(this->storage_node().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "VideoStorage.VsRequest.storage_node");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->storage_node(), target);
  }

  // .VideoStorage.VideoStorageAction act = 5;
  if (this->act() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      5, this->act(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:VideoStorage.VsRequest)
  return target;
}

size_t VsRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:VideoStorage.VsRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string storage_uid = 1;
  if (this->storage_uid().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->storage_uid());
  }

  // string dev_rtsp_pwd = 2;
  if (this->dev_rtsp_pwd().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->dev_rtsp_pwd());
  }

  // string rtsp_srv_ip = 3;
  if (this->rtsp_srv_ip().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->rtsp_srv_ip());
  }

  // string storage_node = 4;
  if (this->storage_node().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->storage_node());
  }

  // .VideoStorage.VideoStorageAction act = 5;
  if (this->act() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->act());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void VsRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:VideoStorage.VsRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const VsRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const VsRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:VideoStorage.VsRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:VideoStorage.VsRequest)
    MergeFrom(*source);
  }
}

void VsRequest::MergeFrom(const VsRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:VideoStorage.VsRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.storage_uid().size() > 0) {

    storage_uid_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.storage_uid_);
  }
  if (from.dev_rtsp_pwd().size() > 0) {

    dev_rtsp_pwd_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.dev_rtsp_pwd_);
  }
  if (from.rtsp_srv_ip().size() > 0) {

    rtsp_srv_ip_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.rtsp_srv_ip_);
  }
  if (from.storage_node().size() > 0) {

    storage_node_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.storage_node_);
  }
  if (from.act() != 0) {
    set_act(from.act());
  }
}

void VsRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:VideoStorage.VsRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void VsRequest::CopyFrom(const VsRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:VideoStorage.VsRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VsRequest::IsInitialized() const {
  return true;
}

void VsRequest::Swap(VsRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void VsRequest::InternalSwap(VsRequest* other) {
  using std::swap;
  storage_uid_.Swap(&other->storage_uid_);
  dev_rtsp_pwd_.Swap(&other->dev_rtsp_pwd_);
  rtsp_srv_ip_.Swap(&other->rtsp_srv_ip_);
  storage_node_.Swap(&other->storage_node_);
  swap(act_, other->act_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata VsRequest::GetMetadata() const {
  protobuf_csvs_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_csvs_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void VsReply::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int VsReply::kHlsUriFieldNumber;
const int VsReply::kGlobalVideoIdFieldNumber;
const int VsReply::kRespStorageMacFieldNumber;
const int VsReply::kRespStorageNodeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

VsReply::VsReply()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_csvs_2eproto::InitDefaultsVsReply();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:VideoStorage.VsReply)
}
VsReply::VsReply(const VsReply& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  hls_uri_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.hls_uri().size() > 0) {
    hls_uri_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.hls_uri_);
  }
  resp_storage_mac_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.resp_storage_mac().size() > 0) {
    resp_storage_mac_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.resp_storage_mac_);
  }
  resp_storage_node_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.resp_storage_node().size() > 0) {
    resp_storage_node_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.resp_storage_node_);
  }
  global_video_id_ = from.global_video_id_;
  // @@protoc_insertion_point(copy_constructor:VideoStorage.VsReply)
}

void VsReply::SharedCtor() {
  hls_uri_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  resp_storage_mac_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  resp_storage_node_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  global_video_id_ = 0u;
  _cached_size_ = 0;
}

VsReply::~VsReply() {
  // @@protoc_insertion_point(destructor:VideoStorage.VsReply)
  SharedDtor();
}

void VsReply::SharedDtor() {
  hls_uri_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  resp_storage_mac_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  resp_storage_node_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void VsReply::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* VsReply::descriptor() {
  ::protobuf_csvs_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_csvs_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const VsReply& VsReply::default_instance() {
  ::protobuf_csvs_2eproto::InitDefaultsVsReply();
  return *internal_default_instance();
}

VsReply* VsReply::New(::google::protobuf::Arena* arena) const {
  VsReply* n = new VsReply;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void VsReply::Clear() {
// @@protoc_insertion_point(message_clear_start:VideoStorage.VsReply)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  hls_uri_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  resp_storage_mac_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  resp_storage_node_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  global_video_id_ = 0u;
  _internal_metadata_.Clear();
}

bool VsReply::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:VideoStorage.VsReply)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string hls_uri = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_hls_uri()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->hls_uri().data(), static_cast<int>(this->hls_uri().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "VideoStorage.VsReply.hls_uri"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint32 global_video_id = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &global_video_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string resp_storage_mac = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_resp_storage_mac()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->resp_storage_mac().data(), static_cast<int>(this->resp_storage_mac().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "VideoStorage.VsReply.resp_storage_mac"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string resp_storage_node = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_resp_storage_node()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->resp_storage_node().data(), static_cast<int>(this->resp_storage_node().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "VideoStorage.VsReply.resp_storage_node"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:VideoStorage.VsReply)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:VideoStorage.VsReply)
  return false;
#undef DO_
}

void VsReply::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:VideoStorage.VsReply)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string hls_uri = 1;
  if (this->hls_uri().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->hls_uri().data(), static_cast<int>(this->hls_uri().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "VideoStorage.VsReply.hls_uri");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->hls_uri(), output);
  }

  // uint32 global_video_id = 2;
  if (this->global_video_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(2, this->global_video_id(), output);
  }

  // string resp_storage_mac = 3;
  if (this->resp_storage_mac().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->resp_storage_mac().data(), static_cast<int>(this->resp_storage_mac().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "VideoStorage.VsReply.resp_storage_mac");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->resp_storage_mac(), output);
  }

  // string resp_storage_node = 4;
  if (this->resp_storage_node().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->resp_storage_node().data(), static_cast<int>(this->resp_storage_node().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "VideoStorage.VsReply.resp_storage_node");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->resp_storage_node(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:VideoStorage.VsReply)
}

::google::protobuf::uint8* VsReply::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:VideoStorage.VsReply)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string hls_uri = 1;
  if (this->hls_uri().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->hls_uri().data(), static_cast<int>(this->hls_uri().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "VideoStorage.VsReply.hls_uri");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->hls_uri(), target);
  }

  // uint32 global_video_id = 2;
  if (this->global_video_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(2, this->global_video_id(), target);
  }

  // string resp_storage_mac = 3;
  if (this->resp_storage_mac().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->resp_storage_mac().data(), static_cast<int>(this->resp_storage_mac().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "VideoStorage.VsReply.resp_storage_mac");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->resp_storage_mac(), target);
  }

  // string resp_storage_node = 4;
  if (this->resp_storage_node().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->resp_storage_node().data(), static_cast<int>(this->resp_storage_node().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "VideoStorage.VsReply.resp_storage_node");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->resp_storage_node(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:VideoStorage.VsReply)
  return target;
}

size_t VsReply::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:VideoStorage.VsReply)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string hls_uri = 1;
  if (this->hls_uri().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->hls_uri());
  }

  // string resp_storage_mac = 3;
  if (this->resp_storage_mac().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->resp_storage_mac());
  }

  // string resp_storage_node = 4;
  if (this->resp_storage_node().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->resp_storage_node());
  }

  // uint32 global_video_id = 2;
  if (this->global_video_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->global_video_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void VsReply::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:VideoStorage.VsReply)
  GOOGLE_DCHECK_NE(&from, this);
  const VsReply* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const VsReply>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:VideoStorage.VsReply)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:VideoStorage.VsReply)
    MergeFrom(*source);
  }
}

void VsReply::MergeFrom(const VsReply& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:VideoStorage.VsReply)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.hls_uri().size() > 0) {

    hls_uri_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.hls_uri_);
  }
  if (from.resp_storage_mac().size() > 0) {

    resp_storage_mac_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.resp_storage_mac_);
  }
  if (from.resp_storage_node().size() > 0) {

    resp_storage_node_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.resp_storage_node_);
  }
  if (from.global_video_id() != 0) {
    set_global_video_id(from.global_video_id());
  }
}

void VsReply::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:VideoStorage.VsReply)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void VsReply::CopyFrom(const VsReply& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:VideoStorage.VsReply)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VsReply::IsInitialized() const {
  return true;
}

void VsReply::Swap(VsReply* other) {
  if (other == this) return;
  InternalSwap(other);
}
void VsReply::InternalSwap(VsReply* other) {
  using std::swap;
  hls_uri_.Swap(&other->hls_uri_);
  resp_storage_mac_.Swap(&other->resp_storage_mac_);
  resp_storage_node_.Swap(&other->resp_storage_node_);
  swap(global_video_id_, other->global_video_id_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata VsReply::GetMetadata() const {
  protobuf_csvs_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_csvs_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void VsDelRequest::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int VsDelRequest::kGlobalVideoIdFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

VsDelRequest::VsDelRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_csvs_2eproto::InitDefaultsVsDelRequest();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:VideoStorage.VsDelRequest)
}
VsDelRequest::VsDelRequest(const VsDelRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  global_video_id_ = from.global_video_id_;
  // @@protoc_insertion_point(copy_constructor:VideoStorage.VsDelRequest)
}

void VsDelRequest::SharedCtor() {
  global_video_id_ = 0u;
  _cached_size_ = 0;
}

VsDelRequest::~VsDelRequest() {
  // @@protoc_insertion_point(destructor:VideoStorage.VsDelRequest)
  SharedDtor();
}

void VsDelRequest::SharedDtor() {
}

void VsDelRequest::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* VsDelRequest::descriptor() {
  ::protobuf_csvs_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_csvs_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const VsDelRequest& VsDelRequest::default_instance() {
  ::protobuf_csvs_2eproto::InitDefaultsVsDelRequest();
  return *internal_default_instance();
}

VsDelRequest* VsDelRequest::New(::google::protobuf::Arena* arena) const {
  VsDelRequest* n = new VsDelRequest;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void VsDelRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:VideoStorage.VsDelRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  global_video_id_ = 0u;
  _internal_metadata_.Clear();
}

bool VsDelRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:VideoStorage.VsDelRequest)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // uint32 global_video_id = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, &global_video_id_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:VideoStorage.VsDelRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:VideoStorage.VsDelRequest)
  return false;
#undef DO_
}

void VsDelRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:VideoStorage.VsDelRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 global_video_id = 1;
  if (this->global_video_id() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32(1, this->global_video_id(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:VideoStorage.VsDelRequest)
}

::google::protobuf::uint8* VsDelRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:VideoStorage.VsDelRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 global_video_id = 1;
  if (this->global_video_id() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt32ToArray(1, this->global_video_id(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:VideoStorage.VsDelRequest)
  return target;
}

size_t VsDelRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:VideoStorage.VsDelRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // uint32 global_video_id = 1;
  if (this->global_video_id() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt32Size(
        this->global_video_id());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void VsDelRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:VideoStorage.VsDelRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const VsDelRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const VsDelRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:VideoStorage.VsDelRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:VideoStorage.VsDelRequest)
    MergeFrom(*source);
  }
}

void VsDelRequest::MergeFrom(const VsDelRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:VideoStorage.VsDelRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.global_video_id() != 0) {
    set_global_video_id(from.global_video_id());
  }
}

void VsDelRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:VideoStorage.VsDelRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void VsDelRequest::CopyFrom(const VsDelRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:VideoStorage.VsDelRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VsDelRequest::IsInitialized() const {
  return true;
}

void VsDelRequest::Swap(VsDelRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void VsDelRequest::InternalSwap(VsDelRequest* other) {
  using std::swap;
  swap(global_video_id_, other->global_video_id_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata VsDelRequest::GetMetadata() const {
  protobuf_csvs_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_csvs_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void VsDelReply::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int VsDelReply::kDelReplyFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

VsDelReply::VsDelReply()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_csvs_2eproto::InitDefaultsVsDelReply();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:VideoStorage.VsDelReply)
}
VsDelReply::VsDelReply(const VsDelReply& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  del_reply_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.del_reply().size() > 0) {
    del_reply_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.del_reply_);
  }
  // @@protoc_insertion_point(copy_constructor:VideoStorage.VsDelReply)
}

void VsDelReply::SharedCtor() {
  del_reply_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

VsDelReply::~VsDelReply() {
  // @@protoc_insertion_point(destructor:VideoStorage.VsDelReply)
  SharedDtor();
}

void VsDelReply::SharedDtor() {
  del_reply_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void VsDelReply::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* VsDelReply::descriptor() {
  ::protobuf_csvs_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_csvs_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const VsDelReply& VsDelReply::default_instance() {
  ::protobuf_csvs_2eproto::InitDefaultsVsDelReply();
  return *internal_default_instance();
}

VsDelReply* VsDelReply::New(::google::protobuf::Arena* arena) const {
  VsDelReply* n = new VsDelReply;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void VsDelReply::Clear() {
// @@protoc_insertion_point(message_clear_start:VideoStorage.VsDelReply)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  del_reply_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _internal_metadata_.Clear();
}

bool VsDelReply::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:VideoStorage.VsDelReply)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string del_reply = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_del_reply()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->del_reply().data(), static_cast<int>(this->del_reply().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "VideoStorage.VsDelReply.del_reply"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:VideoStorage.VsDelReply)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:VideoStorage.VsDelReply)
  return false;
#undef DO_
}

void VsDelReply::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:VideoStorage.VsDelReply)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string del_reply = 1;
  if (this->del_reply().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->del_reply().data(), static_cast<int>(this->del_reply().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "VideoStorage.VsDelReply.del_reply");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->del_reply(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:VideoStorage.VsDelReply)
}

::google::protobuf::uint8* VsDelReply::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:VideoStorage.VsDelReply)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string del_reply = 1;
  if (this->del_reply().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->del_reply().data(), static_cast<int>(this->del_reply().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "VideoStorage.VsDelReply.del_reply");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->del_reply(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:VideoStorage.VsDelReply)
  return target;
}

size_t VsDelReply::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:VideoStorage.VsDelReply)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string del_reply = 1;
  if (this->del_reply().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->del_reply());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void VsDelReply::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:VideoStorage.VsDelReply)
  GOOGLE_DCHECK_NE(&from, this);
  const VsDelReply* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const VsDelReply>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:VideoStorage.VsDelReply)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:VideoStorage.VsDelReply)
    MergeFrom(*source);
  }
}

void VsDelReply::MergeFrom(const VsDelReply& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:VideoStorage.VsDelReply)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.del_reply().size() > 0) {

    del_reply_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.del_reply_);
  }
}

void VsDelReply::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:VideoStorage.VsDelReply)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void VsDelReply::CopyFrom(const VsDelReply& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:VideoStorage.VsDelReply)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VsDelReply::IsInitialized() const {
  return true;
}

void VsDelReply::Swap(VsDelReply* other) {
  if (other == this) return;
  InternalSwap(other);
}
void VsDelReply::InternalSwap(VsDelReply* other) {
  using std::swap;
  del_reply_.Swap(&other->del_reply_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata VsDelReply::GetMetadata() const {
  protobuf_csvs_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_csvs_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace VideoStorage

// @@protoc_insertion_point(global_scope)
