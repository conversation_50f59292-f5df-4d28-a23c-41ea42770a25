// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: csvs.proto

#ifndef PROTOBUF_csvs_2eproto__INCLUDED
#define PROTOBUF_csvs_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3005001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace protobuf_csvs_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[4];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
void InitDefaultsVsRequestImpl();
void InitDefaultsVsRequest();
void InitDefaultsVsReplyImpl();
void InitDefaultsVsReply();
void InitDefaultsVsDelRequestImpl();
void InitDefaultsVsDelRequest();
void InitDefaultsVsDelReplyImpl();
void InitDefaultsVsDelReply();
inline void InitDefaults() {
  InitDefaultsVsRequest();
  InitDefaultsVsReply();
  InitDefaultsVsDelRequest();
  InitDefaultsVsDelReply();
}
}  // namespace protobuf_csvs_2eproto
namespace VideoStorage {
class VsDelReply;
class VsDelReplyDefaultTypeInternal;
extern VsDelReplyDefaultTypeInternal _VsDelReply_default_instance_;
class VsDelRequest;
class VsDelRequestDefaultTypeInternal;
extern VsDelRequestDefaultTypeInternal _VsDelRequest_default_instance_;
class VsReply;
class VsReplyDefaultTypeInternal;
extern VsReplyDefaultTypeInternal _VsReply_default_instance_;
class VsRequest;
class VsRequestDefaultTypeInternal;
extern VsRequestDefaultTypeInternal _VsRequest_default_instance_;
}  // namespace VideoStorage
namespace VideoStorage {

enum VideoStorageAction {
  NULL_VIDEO_STORAGE = 0,
  START_VIDEO_STORAGE = 1,
  STOP_VIDEO_STORAGE = 2,
  VideoStorageAction_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  VideoStorageAction_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool VideoStorageAction_IsValid(int value);
const VideoStorageAction VideoStorageAction_MIN = NULL_VIDEO_STORAGE;
const VideoStorageAction VideoStorageAction_MAX = STOP_VIDEO_STORAGE;
const int VideoStorageAction_ARRAYSIZE = VideoStorageAction_MAX + 1;

const ::google::protobuf::EnumDescriptor* VideoStorageAction_descriptor();
inline const ::std::string& VideoStorageAction_Name(VideoStorageAction value) {
  return ::google::protobuf::internal::NameOfEnum(
    VideoStorageAction_descriptor(), value);
}
inline bool VideoStorageAction_Parse(
    const ::std::string& name, VideoStorageAction* value) {
  return ::google::protobuf::internal::ParseNamedEnum<VideoStorageAction>(
    VideoStorageAction_descriptor(), name, value);
}
// ===================================================================

class VsRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:VideoStorage.VsRequest) */ {
 public:
  VsRequest();
  virtual ~VsRequest();

  VsRequest(const VsRequest& from);

  inline VsRequest& operator=(const VsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  VsRequest(VsRequest&& from) noexcept
    : VsRequest() {
    *this = ::std::move(from);
  }

  inline VsRequest& operator=(VsRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const VsRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const VsRequest* internal_default_instance() {
    return reinterpret_cast<const VsRequest*>(
               &_VsRequest_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    0;

  void Swap(VsRequest* other);
  friend void swap(VsRequest& a, VsRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline VsRequest* New() const PROTOBUF_FINAL { return New(NULL); }

  VsRequest* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const VsRequest& from);
  void MergeFrom(const VsRequest& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(VsRequest* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string storage_uid = 1;
  void clear_storage_uid();
  static const int kStorageUidFieldNumber = 1;
  const ::std::string& storage_uid() const;
  void set_storage_uid(const ::std::string& value);
  #if LANG_CXX11
  void set_storage_uid(::std::string&& value);
  #endif
  void set_storage_uid(const char* value);
  void set_storage_uid(const char* value, size_t size);
  ::std::string* mutable_storage_uid();
  ::std::string* release_storage_uid();
  void set_allocated_storage_uid(::std::string* storage_uid);

  // string dev_rtsp_pwd = 2;
  void clear_dev_rtsp_pwd();
  static const int kDevRtspPwdFieldNumber = 2;
  const ::std::string& dev_rtsp_pwd() const;
  void set_dev_rtsp_pwd(const ::std::string& value);
  #if LANG_CXX11
  void set_dev_rtsp_pwd(::std::string&& value);
  #endif
  void set_dev_rtsp_pwd(const char* value);
  void set_dev_rtsp_pwd(const char* value, size_t size);
  ::std::string* mutable_dev_rtsp_pwd();
  ::std::string* release_dev_rtsp_pwd();
  void set_allocated_dev_rtsp_pwd(::std::string* dev_rtsp_pwd);

  // string rtsp_srv_ip = 3;
  void clear_rtsp_srv_ip();
  static const int kRtspSrvIpFieldNumber = 3;
  const ::std::string& rtsp_srv_ip() const;
  void set_rtsp_srv_ip(const ::std::string& value);
  #if LANG_CXX11
  void set_rtsp_srv_ip(::std::string&& value);
  #endif
  void set_rtsp_srv_ip(const char* value);
  void set_rtsp_srv_ip(const char* value, size_t size);
  ::std::string* mutable_rtsp_srv_ip();
  ::std::string* release_rtsp_srv_ip();
  void set_allocated_rtsp_srv_ip(::std::string* rtsp_srv_ip);

  // string storage_node = 4;
  void clear_storage_node();
  static const int kStorageNodeFieldNumber = 4;
  const ::std::string& storage_node() const;
  void set_storage_node(const ::std::string& value);
  #if LANG_CXX11
  void set_storage_node(::std::string&& value);
  #endif
  void set_storage_node(const char* value);
  void set_storage_node(const char* value, size_t size);
  ::std::string* mutable_storage_node();
  ::std::string* release_storage_node();
  void set_allocated_storage_node(::std::string* storage_node);

  // .VideoStorage.VideoStorageAction act = 5;
  void clear_act();
  static const int kActFieldNumber = 5;
  ::VideoStorage::VideoStorageAction act() const;
  void set_act(::VideoStorage::VideoStorageAction value);

  // @@protoc_insertion_point(class_scope:VideoStorage.VsRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr storage_uid_;
  ::google::protobuf::internal::ArenaStringPtr dev_rtsp_pwd_;
  ::google::protobuf::internal::ArenaStringPtr rtsp_srv_ip_;
  ::google::protobuf::internal::ArenaStringPtr storage_node_;
  int act_;
  mutable int _cached_size_;
  friend struct ::protobuf_csvs_2eproto::TableStruct;
  friend void ::protobuf_csvs_2eproto::InitDefaultsVsRequestImpl();
};
// -------------------------------------------------------------------

class VsReply : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:VideoStorage.VsReply) */ {
 public:
  VsReply();
  virtual ~VsReply();

  VsReply(const VsReply& from);

  inline VsReply& operator=(const VsReply& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  VsReply(VsReply&& from) noexcept
    : VsReply() {
    *this = ::std::move(from);
  }

  inline VsReply& operator=(VsReply&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const VsReply& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const VsReply* internal_default_instance() {
    return reinterpret_cast<const VsReply*>(
               &_VsReply_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    1;

  void Swap(VsReply* other);
  friend void swap(VsReply& a, VsReply& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline VsReply* New() const PROTOBUF_FINAL { return New(NULL); }

  VsReply* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const VsReply& from);
  void MergeFrom(const VsReply& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(VsReply* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string hls_uri = 1;
  void clear_hls_uri();
  static const int kHlsUriFieldNumber = 1;
  const ::std::string& hls_uri() const;
  void set_hls_uri(const ::std::string& value);
  #if LANG_CXX11
  void set_hls_uri(::std::string&& value);
  #endif
  void set_hls_uri(const char* value);
  void set_hls_uri(const char* value, size_t size);
  ::std::string* mutable_hls_uri();
  ::std::string* release_hls_uri();
  void set_allocated_hls_uri(::std::string* hls_uri);

  // string resp_storage_mac = 3;
  void clear_resp_storage_mac();
  static const int kRespStorageMacFieldNumber = 3;
  const ::std::string& resp_storage_mac() const;
  void set_resp_storage_mac(const ::std::string& value);
  #if LANG_CXX11
  void set_resp_storage_mac(::std::string&& value);
  #endif
  void set_resp_storage_mac(const char* value);
  void set_resp_storage_mac(const char* value, size_t size);
  ::std::string* mutable_resp_storage_mac();
  ::std::string* release_resp_storage_mac();
  void set_allocated_resp_storage_mac(::std::string* resp_storage_mac);

  // string resp_storage_node = 4;
  void clear_resp_storage_node();
  static const int kRespStorageNodeFieldNumber = 4;
  const ::std::string& resp_storage_node() const;
  void set_resp_storage_node(const ::std::string& value);
  #if LANG_CXX11
  void set_resp_storage_node(::std::string&& value);
  #endif
  void set_resp_storage_node(const char* value);
  void set_resp_storage_node(const char* value, size_t size);
  ::std::string* mutable_resp_storage_node();
  ::std::string* release_resp_storage_node();
  void set_allocated_resp_storage_node(::std::string* resp_storage_node);

  // uint32 global_video_id = 2;
  void clear_global_video_id();
  static const int kGlobalVideoIdFieldNumber = 2;
  ::google::protobuf::uint32 global_video_id() const;
  void set_global_video_id(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:VideoStorage.VsReply)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr hls_uri_;
  ::google::protobuf::internal::ArenaStringPtr resp_storage_mac_;
  ::google::protobuf::internal::ArenaStringPtr resp_storage_node_;
  ::google::protobuf::uint32 global_video_id_;
  mutable int _cached_size_;
  friend struct ::protobuf_csvs_2eproto::TableStruct;
  friend void ::protobuf_csvs_2eproto::InitDefaultsVsReplyImpl();
};
// -------------------------------------------------------------------

class VsDelRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:VideoStorage.VsDelRequest) */ {
 public:
  VsDelRequest();
  virtual ~VsDelRequest();

  VsDelRequest(const VsDelRequest& from);

  inline VsDelRequest& operator=(const VsDelRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  VsDelRequest(VsDelRequest&& from) noexcept
    : VsDelRequest() {
    *this = ::std::move(from);
  }

  inline VsDelRequest& operator=(VsDelRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const VsDelRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const VsDelRequest* internal_default_instance() {
    return reinterpret_cast<const VsDelRequest*>(
               &_VsDelRequest_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    2;

  void Swap(VsDelRequest* other);
  friend void swap(VsDelRequest& a, VsDelRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline VsDelRequest* New() const PROTOBUF_FINAL { return New(NULL); }

  VsDelRequest* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const VsDelRequest& from);
  void MergeFrom(const VsDelRequest& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(VsDelRequest* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // uint32 global_video_id = 1;
  void clear_global_video_id();
  static const int kGlobalVideoIdFieldNumber = 1;
  ::google::protobuf::uint32 global_video_id() const;
  void set_global_video_id(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:VideoStorage.VsDelRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::uint32 global_video_id_;
  mutable int _cached_size_;
  friend struct ::protobuf_csvs_2eproto::TableStruct;
  friend void ::protobuf_csvs_2eproto::InitDefaultsVsDelRequestImpl();
};
// -------------------------------------------------------------------

class VsDelReply : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:VideoStorage.VsDelReply) */ {
 public:
  VsDelReply();
  virtual ~VsDelReply();

  VsDelReply(const VsDelReply& from);

  inline VsDelReply& operator=(const VsDelReply& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  VsDelReply(VsDelReply&& from) noexcept
    : VsDelReply() {
    *this = ::std::move(from);
  }

  inline VsDelReply& operator=(VsDelReply&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const VsDelReply& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const VsDelReply* internal_default_instance() {
    return reinterpret_cast<const VsDelReply*>(
               &_VsDelReply_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    3;

  void Swap(VsDelReply* other);
  friend void swap(VsDelReply& a, VsDelReply& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline VsDelReply* New() const PROTOBUF_FINAL { return New(NULL); }

  VsDelReply* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const VsDelReply& from);
  void MergeFrom(const VsDelReply& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(VsDelReply* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string del_reply = 1;
  void clear_del_reply();
  static const int kDelReplyFieldNumber = 1;
  const ::std::string& del_reply() const;
  void set_del_reply(const ::std::string& value);
  #if LANG_CXX11
  void set_del_reply(::std::string&& value);
  #endif
  void set_del_reply(const char* value);
  void set_del_reply(const char* value, size_t size);
  ::std::string* mutable_del_reply();
  ::std::string* release_del_reply();
  void set_allocated_del_reply(::std::string* del_reply);

  // @@protoc_insertion_point(class_scope:VideoStorage.VsDelReply)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr del_reply_;
  mutable int _cached_size_;
  friend struct ::protobuf_csvs_2eproto::TableStruct;
  friend void ::protobuf_csvs_2eproto::InitDefaultsVsDelReplyImpl();
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// VsRequest

// string storage_uid = 1;
inline void VsRequest::clear_storage_uid() {
  storage_uid_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& VsRequest::storage_uid() const {
  // @@protoc_insertion_point(field_get:VideoStorage.VsRequest.storage_uid)
  return storage_uid_.GetNoArena();
}
inline void VsRequest::set_storage_uid(const ::std::string& value) {
  
  storage_uid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:VideoStorage.VsRequest.storage_uid)
}
#if LANG_CXX11
inline void VsRequest::set_storage_uid(::std::string&& value) {
  
  storage_uid_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:VideoStorage.VsRequest.storage_uid)
}
#endif
inline void VsRequest::set_storage_uid(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  storage_uid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:VideoStorage.VsRequest.storage_uid)
}
inline void VsRequest::set_storage_uid(const char* value, size_t size) {
  
  storage_uid_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:VideoStorage.VsRequest.storage_uid)
}
inline ::std::string* VsRequest::mutable_storage_uid() {
  
  // @@protoc_insertion_point(field_mutable:VideoStorage.VsRequest.storage_uid)
  return storage_uid_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* VsRequest::release_storage_uid() {
  // @@protoc_insertion_point(field_release:VideoStorage.VsRequest.storage_uid)
  
  return storage_uid_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void VsRequest::set_allocated_storage_uid(::std::string* storage_uid) {
  if (storage_uid != NULL) {
    
  } else {
    
  }
  storage_uid_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), storage_uid);
  // @@protoc_insertion_point(field_set_allocated:VideoStorage.VsRequest.storage_uid)
}

// string dev_rtsp_pwd = 2;
inline void VsRequest::clear_dev_rtsp_pwd() {
  dev_rtsp_pwd_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& VsRequest::dev_rtsp_pwd() const {
  // @@protoc_insertion_point(field_get:VideoStorage.VsRequest.dev_rtsp_pwd)
  return dev_rtsp_pwd_.GetNoArena();
}
inline void VsRequest::set_dev_rtsp_pwd(const ::std::string& value) {
  
  dev_rtsp_pwd_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:VideoStorage.VsRequest.dev_rtsp_pwd)
}
#if LANG_CXX11
inline void VsRequest::set_dev_rtsp_pwd(::std::string&& value) {
  
  dev_rtsp_pwd_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:VideoStorage.VsRequest.dev_rtsp_pwd)
}
#endif
inline void VsRequest::set_dev_rtsp_pwd(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  dev_rtsp_pwd_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:VideoStorage.VsRequest.dev_rtsp_pwd)
}
inline void VsRequest::set_dev_rtsp_pwd(const char* value, size_t size) {
  
  dev_rtsp_pwd_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:VideoStorage.VsRequest.dev_rtsp_pwd)
}
inline ::std::string* VsRequest::mutable_dev_rtsp_pwd() {
  
  // @@protoc_insertion_point(field_mutable:VideoStorage.VsRequest.dev_rtsp_pwd)
  return dev_rtsp_pwd_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* VsRequest::release_dev_rtsp_pwd() {
  // @@protoc_insertion_point(field_release:VideoStorage.VsRequest.dev_rtsp_pwd)
  
  return dev_rtsp_pwd_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void VsRequest::set_allocated_dev_rtsp_pwd(::std::string* dev_rtsp_pwd) {
  if (dev_rtsp_pwd != NULL) {
    
  } else {
    
  }
  dev_rtsp_pwd_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), dev_rtsp_pwd);
  // @@protoc_insertion_point(field_set_allocated:VideoStorage.VsRequest.dev_rtsp_pwd)
}

// string rtsp_srv_ip = 3;
inline void VsRequest::clear_rtsp_srv_ip() {
  rtsp_srv_ip_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& VsRequest::rtsp_srv_ip() const {
  // @@protoc_insertion_point(field_get:VideoStorage.VsRequest.rtsp_srv_ip)
  return rtsp_srv_ip_.GetNoArena();
}
inline void VsRequest::set_rtsp_srv_ip(const ::std::string& value) {
  
  rtsp_srv_ip_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:VideoStorage.VsRequest.rtsp_srv_ip)
}
#if LANG_CXX11
inline void VsRequest::set_rtsp_srv_ip(::std::string&& value) {
  
  rtsp_srv_ip_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:VideoStorage.VsRequest.rtsp_srv_ip)
}
#endif
inline void VsRequest::set_rtsp_srv_ip(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  rtsp_srv_ip_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:VideoStorage.VsRequest.rtsp_srv_ip)
}
inline void VsRequest::set_rtsp_srv_ip(const char* value, size_t size) {
  
  rtsp_srv_ip_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:VideoStorage.VsRequest.rtsp_srv_ip)
}
inline ::std::string* VsRequest::mutable_rtsp_srv_ip() {
  
  // @@protoc_insertion_point(field_mutable:VideoStorage.VsRequest.rtsp_srv_ip)
  return rtsp_srv_ip_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* VsRequest::release_rtsp_srv_ip() {
  // @@protoc_insertion_point(field_release:VideoStorage.VsRequest.rtsp_srv_ip)
  
  return rtsp_srv_ip_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void VsRequest::set_allocated_rtsp_srv_ip(::std::string* rtsp_srv_ip) {
  if (rtsp_srv_ip != NULL) {
    
  } else {
    
  }
  rtsp_srv_ip_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), rtsp_srv_ip);
  // @@protoc_insertion_point(field_set_allocated:VideoStorage.VsRequest.rtsp_srv_ip)
}

// string storage_node = 4;
inline void VsRequest::clear_storage_node() {
  storage_node_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& VsRequest::storage_node() const {
  // @@protoc_insertion_point(field_get:VideoStorage.VsRequest.storage_node)
  return storage_node_.GetNoArena();
}
inline void VsRequest::set_storage_node(const ::std::string& value) {
  
  storage_node_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:VideoStorage.VsRequest.storage_node)
}
#if LANG_CXX11
inline void VsRequest::set_storage_node(::std::string&& value) {
  
  storage_node_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:VideoStorage.VsRequest.storage_node)
}
#endif
inline void VsRequest::set_storage_node(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  storage_node_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:VideoStorage.VsRequest.storage_node)
}
inline void VsRequest::set_storage_node(const char* value, size_t size) {
  
  storage_node_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:VideoStorage.VsRequest.storage_node)
}
inline ::std::string* VsRequest::mutable_storage_node() {
  
  // @@protoc_insertion_point(field_mutable:VideoStorage.VsRequest.storage_node)
  return storage_node_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* VsRequest::release_storage_node() {
  // @@protoc_insertion_point(field_release:VideoStorage.VsRequest.storage_node)
  
  return storage_node_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void VsRequest::set_allocated_storage_node(::std::string* storage_node) {
  if (storage_node != NULL) {
    
  } else {
    
  }
  storage_node_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), storage_node);
  // @@protoc_insertion_point(field_set_allocated:VideoStorage.VsRequest.storage_node)
}

// .VideoStorage.VideoStorageAction act = 5;
inline void VsRequest::clear_act() {
  act_ = 0;
}
inline ::VideoStorage::VideoStorageAction VsRequest::act() const {
  // @@protoc_insertion_point(field_get:VideoStorage.VsRequest.act)
  return static_cast< ::VideoStorage::VideoStorageAction >(act_);
}
inline void VsRequest::set_act(::VideoStorage::VideoStorageAction value) {
  
  act_ = value;
  // @@protoc_insertion_point(field_set:VideoStorage.VsRequest.act)
}

// -------------------------------------------------------------------

// VsReply

// string hls_uri = 1;
inline void VsReply::clear_hls_uri() {
  hls_uri_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& VsReply::hls_uri() const {
  // @@protoc_insertion_point(field_get:VideoStorage.VsReply.hls_uri)
  return hls_uri_.GetNoArena();
}
inline void VsReply::set_hls_uri(const ::std::string& value) {
  
  hls_uri_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:VideoStorage.VsReply.hls_uri)
}
#if LANG_CXX11
inline void VsReply::set_hls_uri(::std::string&& value) {
  
  hls_uri_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:VideoStorage.VsReply.hls_uri)
}
#endif
inline void VsReply::set_hls_uri(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  hls_uri_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:VideoStorage.VsReply.hls_uri)
}
inline void VsReply::set_hls_uri(const char* value, size_t size) {
  
  hls_uri_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:VideoStorage.VsReply.hls_uri)
}
inline ::std::string* VsReply::mutable_hls_uri() {
  
  // @@protoc_insertion_point(field_mutable:VideoStorage.VsReply.hls_uri)
  return hls_uri_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* VsReply::release_hls_uri() {
  // @@protoc_insertion_point(field_release:VideoStorage.VsReply.hls_uri)
  
  return hls_uri_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void VsReply::set_allocated_hls_uri(::std::string* hls_uri) {
  if (hls_uri != NULL) {
    
  } else {
    
  }
  hls_uri_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), hls_uri);
  // @@protoc_insertion_point(field_set_allocated:VideoStorage.VsReply.hls_uri)
}

// uint32 global_video_id = 2;
inline void VsReply::clear_global_video_id() {
  global_video_id_ = 0u;
}
inline ::google::protobuf::uint32 VsReply::global_video_id() const {
  // @@protoc_insertion_point(field_get:VideoStorage.VsReply.global_video_id)
  return global_video_id_;
}
inline void VsReply::set_global_video_id(::google::protobuf::uint32 value) {
  
  global_video_id_ = value;
  // @@protoc_insertion_point(field_set:VideoStorage.VsReply.global_video_id)
}

// string resp_storage_mac = 3;
inline void VsReply::clear_resp_storage_mac() {
  resp_storage_mac_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& VsReply::resp_storage_mac() const {
  // @@protoc_insertion_point(field_get:VideoStorage.VsReply.resp_storage_mac)
  return resp_storage_mac_.GetNoArena();
}
inline void VsReply::set_resp_storage_mac(const ::std::string& value) {
  
  resp_storage_mac_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:VideoStorage.VsReply.resp_storage_mac)
}
#if LANG_CXX11
inline void VsReply::set_resp_storage_mac(::std::string&& value) {
  
  resp_storage_mac_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:VideoStorage.VsReply.resp_storage_mac)
}
#endif
inline void VsReply::set_resp_storage_mac(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  resp_storage_mac_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:VideoStorage.VsReply.resp_storage_mac)
}
inline void VsReply::set_resp_storage_mac(const char* value, size_t size) {
  
  resp_storage_mac_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:VideoStorage.VsReply.resp_storage_mac)
}
inline ::std::string* VsReply::mutable_resp_storage_mac() {
  
  // @@protoc_insertion_point(field_mutable:VideoStorage.VsReply.resp_storage_mac)
  return resp_storage_mac_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* VsReply::release_resp_storage_mac() {
  // @@protoc_insertion_point(field_release:VideoStorage.VsReply.resp_storage_mac)
  
  return resp_storage_mac_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void VsReply::set_allocated_resp_storage_mac(::std::string* resp_storage_mac) {
  if (resp_storage_mac != NULL) {
    
  } else {
    
  }
  resp_storage_mac_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), resp_storage_mac);
  // @@protoc_insertion_point(field_set_allocated:VideoStorage.VsReply.resp_storage_mac)
}

// string resp_storage_node = 4;
inline void VsReply::clear_resp_storage_node() {
  resp_storage_node_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& VsReply::resp_storage_node() const {
  // @@protoc_insertion_point(field_get:VideoStorage.VsReply.resp_storage_node)
  return resp_storage_node_.GetNoArena();
}
inline void VsReply::set_resp_storage_node(const ::std::string& value) {
  
  resp_storage_node_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:VideoStorage.VsReply.resp_storage_node)
}
#if LANG_CXX11
inline void VsReply::set_resp_storage_node(::std::string&& value) {
  
  resp_storage_node_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:VideoStorage.VsReply.resp_storage_node)
}
#endif
inline void VsReply::set_resp_storage_node(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  resp_storage_node_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:VideoStorage.VsReply.resp_storage_node)
}
inline void VsReply::set_resp_storage_node(const char* value, size_t size) {
  
  resp_storage_node_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:VideoStorage.VsReply.resp_storage_node)
}
inline ::std::string* VsReply::mutable_resp_storage_node() {
  
  // @@protoc_insertion_point(field_mutable:VideoStorage.VsReply.resp_storage_node)
  return resp_storage_node_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* VsReply::release_resp_storage_node() {
  // @@protoc_insertion_point(field_release:VideoStorage.VsReply.resp_storage_node)
  
  return resp_storage_node_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void VsReply::set_allocated_resp_storage_node(::std::string* resp_storage_node) {
  if (resp_storage_node != NULL) {
    
  } else {
    
  }
  resp_storage_node_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), resp_storage_node);
  // @@protoc_insertion_point(field_set_allocated:VideoStorage.VsReply.resp_storage_node)
}

// -------------------------------------------------------------------

// VsDelRequest

// uint32 global_video_id = 1;
inline void VsDelRequest::clear_global_video_id() {
  global_video_id_ = 0u;
}
inline ::google::protobuf::uint32 VsDelRequest::global_video_id() const {
  // @@protoc_insertion_point(field_get:VideoStorage.VsDelRequest.global_video_id)
  return global_video_id_;
}
inline void VsDelRequest::set_global_video_id(::google::protobuf::uint32 value) {
  
  global_video_id_ = value;
  // @@protoc_insertion_point(field_set:VideoStorage.VsDelRequest.global_video_id)
}

// -------------------------------------------------------------------

// VsDelReply

// string del_reply = 1;
inline void VsDelReply::clear_del_reply() {
  del_reply_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& VsDelReply::del_reply() const {
  // @@protoc_insertion_point(field_get:VideoStorage.VsDelReply.del_reply)
  return del_reply_.GetNoArena();
}
inline void VsDelReply::set_del_reply(const ::std::string& value) {
  
  del_reply_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:VideoStorage.VsDelReply.del_reply)
}
#if LANG_CXX11
inline void VsDelReply::set_del_reply(::std::string&& value) {
  
  del_reply_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:VideoStorage.VsDelReply.del_reply)
}
#endif
inline void VsDelReply::set_del_reply(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  del_reply_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:VideoStorage.VsDelReply.del_reply)
}
inline void VsDelReply::set_del_reply(const char* value, size_t size) {
  
  del_reply_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:VideoStorage.VsDelReply.del_reply)
}
inline ::std::string* VsDelReply::mutable_del_reply() {
  
  // @@protoc_insertion_point(field_mutable:VideoStorage.VsDelReply.del_reply)
  return del_reply_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* VsDelReply::release_del_reply() {
  // @@protoc_insertion_point(field_release:VideoStorage.VsDelReply.del_reply)
  
  return del_reply_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void VsDelReply::set_allocated_del_reply(::std::string* del_reply) {
  if (del_reply != NULL) {
    
  } else {
    
  }
  del_reply_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), del_reply);
  // @@protoc_insertion_point(field_set_allocated:VideoStorage.VsDelReply.del_reply)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace VideoStorage

namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::VideoStorage::VideoStorageAction> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::VideoStorage::VideoStorageAction>() {
  return ::VideoStorage::VideoStorageAction_descriptor();
}

}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_csvs_2eproto__INCLUDED
