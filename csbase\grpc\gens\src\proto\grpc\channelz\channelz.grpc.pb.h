// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: src/proto/grpc/channelz/channelz.proto
// Original file comments:
// Copyright 2018 The gRPC Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// This file defines an interface for exporting monitoring information
// out of gRPC servers.  See the full design at
// https://github.com/grpc/proposal/blob/master/A14-channelz.md
//
// The canonical version of this proto can be found at
// https://github.com/grpc/grpc-proto/blob/master/grpc/channelz/v1/channelz.proto
//
#ifndef GRPC_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto__INCLUDED
#define GRPC_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto__INCLUDED

#include "src/proto/grpc/channelz/channelz.pb.h"

#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/method_handler_impl.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace grpc {
class CompletionQueue;
class Channel;
class ServerCompletionQueue;
class ServerContext;
}  // namespace grpc

namespace grpc {
namespace channelz {
namespace v1 {

// Channelz is a service exposed by gRPC servers that provides detailed debug
// information.
class Channelz final {
 public:
  static constexpr char const* service_full_name() {
    return "grpc.channelz.v1.Channelz";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    // Gets all root channels (i.e. channels the application has directly
    // created). This does not include subchannels nor non-top level channels.
    virtual ::grpc::Status GetTopChannels(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetTopChannelsRequest& request, ::grpc::channelz::v1::GetTopChannelsResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetTopChannelsResponse>> AsyncGetTopChannels(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetTopChannelsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetTopChannelsResponse>>(AsyncGetTopChannelsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetTopChannelsResponse>> PrepareAsyncGetTopChannels(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetTopChannelsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetTopChannelsResponse>>(PrepareAsyncGetTopChannelsRaw(context, request, cq));
    }
    // Gets all servers that exist in the process.
    virtual ::grpc::Status GetServers(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetServersRequest& request, ::grpc::channelz::v1::GetServersResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetServersResponse>> AsyncGetServers(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetServersRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetServersResponse>>(AsyncGetServersRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetServersResponse>> PrepareAsyncGetServers(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetServersRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetServersResponse>>(PrepareAsyncGetServersRaw(context, request, cq));
    }
    // Gets all server sockets that exist in the process.
    virtual ::grpc::Status GetServerSockets(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetServerSocketsRequest& request, ::grpc::channelz::v1::GetServerSocketsResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetServerSocketsResponse>> AsyncGetServerSockets(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetServerSocketsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetServerSocketsResponse>>(AsyncGetServerSocketsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetServerSocketsResponse>> PrepareAsyncGetServerSockets(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetServerSocketsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetServerSocketsResponse>>(PrepareAsyncGetServerSocketsRaw(context, request, cq));
    }
    // Returns a single Channel, or else a NOT_FOUND code.
    virtual ::grpc::Status GetChannel(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetChannelRequest& request, ::grpc::channelz::v1::GetChannelResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetChannelResponse>> AsyncGetChannel(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetChannelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetChannelResponse>>(AsyncGetChannelRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetChannelResponse>> PrepareAsyncGetChannel(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetChannelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetChannelResponse>>(PrepareAsyncGetChannelRaw(context, request, cq));
    }
    // Returns a single Subchannel, or else a NOT_FOUND code.
    virtual ::grpc::Status GetSubchannel(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetSubchannelRequest& request, ::grpc::channelz::v1::GetSubchannelResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetSubchannelResponse>> AsyncGetSubchannel(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetSubchannelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetSubchannelResponse>>(AsyncGetSubchannelRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetSubchannelResponse>> PrepareAsyncGetSubchannel(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetSubchannelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetSubchannelResponse>>(PrepareAsyncGetSubchannelRaw(context, request, cq));
    }
    // Returns a single Socket or else a NOT_FOUND code.
    virtual ::grpc::Status GetSocket(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetSocketRequest& request, ::grpc::channelz::v1::GetSocketResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetSocketResponse>> AsyncGetSocket(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetSocketRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetSocketResponse>>(AsyncGetSocketRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetSocketResponse>> PrepareAsyncGetSocket(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetSocketRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetSocketResponse>>(PrepareAsyncGetSocketRaw(context, request, cq));
    }
  private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetTopChannelsResponse>* AsyncGetTopChannelsRaw(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetTopChannelsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetTopChannelsResponse>* PrepareAsyncGetTopChannelsRaw(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetTopChannelsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetServersResponse>* AsyncGetServersRaw(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetServersRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetServersResponse>* PrepareAsyncGetServersRaw(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetServersRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetServerSocketsResponse>* AsyncGetServerSocketsRaw(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetServerSocketsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetServerSocketsResponse>* PrepareAsyncGetServerSocketsRaw(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetServerSocketsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetChannelResponse>* AsyncGetChannelRaw(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetChannelRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetChannelResponse>* PrepareAsyncGetChannelRaw(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetChannelRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetSubchannelResponse>* AsyncGetSubchannelRaw(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetSubchannelRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetSubchannelResponse>* PrepareAsyncGetSubchannelRaw(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetSubchannelRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetSocketResponse>* AsyncGetSocketRaw(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetSocketRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetSocketResponse>* PrepareAsyncGetSocketRaw(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetSocketRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel);
    ::grpc::Status GetTopChannels(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetTopChannelsRequest& request, ::grpc::channelz::v1::GetTopChannelsResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetTopChannelsResponse>> AsyncGetTopChannels(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetTopChannelsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetTopChannelsResponse>>(AsyncGetTopChannelsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetTopChannelsResponse>> PrepareAsyncGetTopChannels(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetTopChannelsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetTopChannelsResponse>>(PrepareAsyncGetTopChannelsRaw(context, request, cq));
    }
    ::grpc::Status GetServers(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetServersRequest& request, ::grpc::channelz::v1::GetServersResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetServersResponse>> AsyncGetServers(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetServersRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetServersResponse>>(AsyncGetServersRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetServersResponse>> PrepareAsyncGetServers(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetServersRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetServersResponse>>(PrepareAsyncGetServersRaw(context, request, cq));
    }
    ::grpc::Status GetServerSockets(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetServerSocketsRequest& request, ::grpc::channelz::v1::GetServerSocketsResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetServerSocketsResponse>> AsyncGetServerSockets(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetServerSocketsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetServerSocketsResponse>>(AsyncGetServerSocketsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetServerSocketsResponse>> PrepareAsyncGetServerSockets(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetServerSocketsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetServerSocketsResponse>>(PrepareAsyncGetServerSocketsRaw(context, request, cq));
    }
    ::grpc::Status GetChannel(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetChannelRequest& request, ::grpc::channelz::v1::GetChannelResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetChannelResponse>> AsyncGetChannel(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetChannelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetChannelResponse>>(AsyncGetChannelRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetChannelResponse>> PrepareAsyncGetChannel(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetChannelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetChannelResponse>>(PrepareAsyncGetChannelRaw(context, request, cq));
    }
    ::grpc::Status GetSubchannel(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetSubchannelRequest& request, ::grpc::channelz::v1::GetSubchannelResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetSubchannelResponse>> AsyncGetSubchannel(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetSubchannelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetSubchannelResponse>>(AsyncGetSubchannelRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetSubchannelResponse>> PrepareAsyncGetSubchannel(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetSubchannelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetSubchannelResponse>>(PrepareAsyncGetSubchannelRaw(context, request, cq));
    }
    ::grpc::Status GetSocket(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetSocketRequest& request, ::grpc::channelz::v1::GetSocketResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetSocketResponse>> AsyncGetSocket(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetSocketRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetSocketResponse>>(AsyncGetSocketRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetSocketResponse>> PrepareAsyncGetSocket(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetSocketRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetSocketResponse>>(PrepareAsyncGetSocketRaw(context, request, cq));
    }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetTopChannelsResponse>* AsyncGetTopChannelsRaw(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetTopChannelsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetTopChannelsResponse>* PrepareAsyncGetTopChannelsRaw(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetTopChannelsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetServersResponse>* AsyncGetServersRaw(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetServersRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetServersResponse>* PrepareAsyncGetServersRaw(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetServersRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetServerSocketsResponse>* AsyncGetServerSocketsRaw(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetServerSocketsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetServerSocketsResponse>* PrepareAsyncGetServerSocketsRaw(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetServerSocketsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetChannelResponse>* AsyncGetChannelRaw(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetChannelRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetChannelResponse>* PrepareAsyncGetChannelRaw(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetChannelRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetSubchannelResponse>* AsyncGetSubchannelRaw(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetSubchannelRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetSubchannelResponse>* PrepareAsyncGetSubchannelRaw(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetSubchannelRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetSocketResponse>* AsyncGetSocketRaw(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetSocketRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::channelz::v1::GetSocketResponse>* PrepareAsyncGetSocketRaw(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetSocketRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_GetTopChannels_;
    const ::grpc::internal::RpcMethod rpcmethod_GetServers_;
    const ::grpc::internal::RpcMethod rpcmethod_GetServerSockets_;
    const ::grpc::internal::RpcMethod rpcmethod_GetChannel_;
    const ::grpc::internal::RpcMethod rpcmethod_GetSubchannel_;
    const ::grpc::internal::RpcMethod rpcmethod_GetSocket_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    // Gets all root channels (i.e. channels the application has directly
    // created). This does not include subchannels nor non-top level channels.
    virtual ::grpc::Status GetTopChannels(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetTopChannelsRequest* request, ::grpc::channelz::v1::GetTopChannelsResponse* response);
    // Gets all servers that exist in the process.
    virtual ::grpc::Status GetServers(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetServersRequest* request, ::grpc::channelz::v1::GetServersResponse* response);
    // Gets all server sockets that exist in the process.
    virtual ::grpc::Status GetServerSockets(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetServerSocketsRequest* request, ::grpc::channelz::v1::GetServerSocketsResponse* response);
    // Returns a single Channel, or else a NOT_FOUND code.
    virtual ::grpc::Status GetChannel(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetChannelRequest* request, ::grpc::channelz::v1::GetChannelResponse* response);
    // Returns a single Subchannel, or else a NOT_FOUND code.
    virtual ::grpc::Status GetSubchannel(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetSubchannelRequest* request, ::grpc::channelz::v1::GetSubchannelResponse* response);
    // Returns a single Socket or else a NOT_FOUND code.
    virtual ::grpc::Status GetSocket(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetSocketRequest* request, ::grpc::channelz::v1::GetSocketResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_GetTopChannels : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_GetTopChannels() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_GetTopChannels() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTopChannels(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetTopChannelsRequest* request, ::grpc::channelz::v1::GetTopChannelsResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetTopChannels(::grpc::ServerContext* context, ::grpc::channelz::v1::GetTopChannelsRequest* request, ::grpc::ServerAsyncResponseWriter< ::grpc::channelz::v1::GetTopChannelsResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetServers : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_GetServers() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_GetServers() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetServers(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetServersRequest* request, ::grpc::channelz::v1::GetServersResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetServers(::grpc::ServerContext* context, ::grpc::channelz::v1::GetServersRequest* request, ::grpc::ServerAsyncResponseWriter< ::grpc::channelz::v1::GetServersResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetServerSockets : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_GetServerSockets() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_GetServerSockets() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetServerSockets(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetServerSocketsRequest* request, ::grpc::channelz::v1::GetServerSocketsResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetServerSockets(::grpc::ServerContext* context, ::grpc::channelz::v1::GetServerSocketsRequest* request, ::grpc::ServerAsyncResponseWriter< ::grpc::channelz::v1::GetServerSocketsResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetChannel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_GetChannel() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_GetChannel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetChannel(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetChannelRequest* request, ::grpc::channelz::v1::GetChannelResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetChannel(::grpc::ServerContext* context, ::grpc::channelz::v1::GetChannelRequest* request, ::grpc::ServerAsyncResponseWriter< ::grpc::channelz::v1::GetChannelResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetSubchannel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_GetSubchannel() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_GetSubchannel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSubchannel(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetSubchannelRequest* request, ::grpc::channelz::v1::GetSubchannelResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetSubchannel(::grpc::ServerContext* context, ::grpc::channelz::v1::GetSubchannelRequest* request, ::grpc::ServerAsyncResponseWriter< ::grpc::channelz::v1::GetSubchannelResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetSocket : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_GetSocket() {
      ::grpc::Service::MarkMethodAsync(5);
    }
    ~WithAsyncMethod_GetSocket() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSocket(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetSocketRequest* request, ::grpc::channelz::v1::GetSocketResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetSocket(::grpc::ServerContext* context, ::grpc::channelz::v1::GetSocketRequest* request, ::grpc::ServerAsyncResponseWriter< ::grpc::channelz::v1::GetSocketResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_GetTopChannels<WithAsyncMethod_GetServers<WithAsyncMethod_GetServerSockets<WithAsyncMethod_GetChannel<WithAsyncMethod_GetSubchannel<WithAsyncMethod_GetSocket<Service > > > > > > AsyncService;
  template <class BaseClass>
  class WithGenericMethod_GetTopChannels : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_GetTopChannels() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_GetTopChannels() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTopChannels(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetTopChannelsRequest* request, ::grpc::channelz::v1::GetTopChannelsResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetServers : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_GetServers() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_GetServers() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetServers(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetServersRequest* request, ::grpc::channelz::v1::GetServersResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetServerSockets : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_GetServerSockets() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_GetServerSockets() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetServerSockets(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetServerSocketsRequest* request, ::grpc::channelz::v1::GetServerSocketsResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetChannel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_GetChannel() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_GetChannel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetChannel(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetChannelRequest* request, ::grpc::channelz::v1::GetChannelResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetSubchannel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_GetSubchannel() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_GetSubchannel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSubchannel(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetSubchannelRequest* request, ::grpc::channelz::v1::GetSubchannelResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetSocket : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_GetSocket() {
      ::grpc::Service::MarkMethodGeneric(5);
    }
    ~WithGenericMethod_GetSocket() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSocket(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetSocketRequest* request, ::grpc::channelz::v1::GetSocketResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetTopChannels : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_GetTopChannels() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_GetTopChannels() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTopChannels(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetTopChannelsRequest* request, ::grpc::channelz::v1::GetTopChannelsResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetTopChannels(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetServers : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_GetServers() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_GetServers() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetServers(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetServersRequest* request, ::grpc::channelz::v1::GetServersResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetServers(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetServerSockets : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_GetServerSockets() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_GetServerSockets() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetServerSockets(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetServerSocketsRequest* request, ::grpc::channelz::v1::GetServerSocketsResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetServerSockets(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetChannel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_GetChannel() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_GetChannel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetChannel(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetChannelRequest* request, ::grpc::channelz::v1::GetChannelResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetChannel(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetSubchannel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_GetSubchannel() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_GetSubchannel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSubchannel(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetSubchannelRequest* request, ::grpc::channelz::v1::GetSubchannelResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetSubchannel(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetSocket : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_GetSocket() {
      ::grpc::Service::MarkMethodRaw(5);
    }
    ~WithRawMethod_GetSocket() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSocket(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetSocketRequest* request, ::grpc::channelz::v1::GetSocketResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetSocket(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetTopChannels : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithStreamedUnaryMethod_GetTopChannels() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler< ::grpc::channelz::v1::GetTopChannelsRequest, ::grpc::channelz::v1::GetTopChannelsResponse>(std::bind(&WithStreamedUnaryMethod_GetTopChannels<BaseClass>::StreamedGetTopChannels, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_GetTopChannels() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetTopChannels(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetTopChannelsRequest* request, ::grpc::channelz::v1::GetTopChannelsResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetTopChannels(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::grpc::channelz::v1::GetTopChannelsRequest,::grpc::channelz::v1::GetTopChannelsResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetServers : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithStreamedUnaryMethod_GetServers() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler< ::grpc::channelz::v1::GetServersRequest, ::grpc::channelz::v1::GetServersResponse>(std::bind(&WithStreamedUnaryMethod_GetServers<BaseClass>::StreamedGetServers, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_GetServers() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetServers(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetServersRequest* request, ::grpc::channelz::v1::GetServersResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetServers(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::grpc::channelz::v1::GetServersRequest,::grpc::channelz::v1::GetServersResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetServerSockets : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithStreamedUnaryMethod_GetServerSockets() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler< ::grpc::channelz::v1::GetServerSocketsRequest, ::grpc::channelz::v1::GetServerSocketsResponse>(std::bind(&WithStreamedUnaryMethod_GetServerSockets<BaseClass>::StreamedGetServerSockets, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_GetServerSockets() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetServerSockets(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetServerSocketsRequest* request, ::grpc::channelz::v1::GetServerSocketsResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetServerSockets(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::grpc::channelz::v1::GetServerSocketsRequest,::grpc::channelz::v1::GetServerSocketsResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetChannel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithStreamedUnaryMethod_GetChannel() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler< ::grpc::channelz::v1::GetChannelRequest, ::grpc::channelz::v1::GetChannelResponse>(std::bind(&WithStreamedUnaryMethod_GetChannel<BaseClass>::StreamedGetChannel, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_GetChannel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetChannel(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetChannelRequest* request, ::grpc::channelz::v1::GetChannelResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetChannel(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::grpc::channelz::v1::GetChannelRequest,::grpc::channelz::v1::GetChannelResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetSubchannel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithStreamedUnaryMethod_GetSubchannel() {
      ::grpc::Service::MarkMethodStreamed(4,
        new ::grpc::internal::StreamedUnaryHandler< ::grpc::channelz::v1::GetSubchannelRequest, ::grpc::channelz::v1::GetSubchannelResponse>(std::bind(&WithStreamedUnaryMethod_GetSubchannel<BaseClass>::StreamedGetSubchannel, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_GetSubchannel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetSubchannel(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetSubchannelRequest* request, ::grpc::channelz::v1::GetSubchannelResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetSubchannel(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::grpc::channelz::v1::GetSubchannelRequest,::grpc::channelz::v1::GetSubchannelResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetSocket : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithStreamedUnaryMethod_GetSocket() {
      ::grpc::Service::MarkMethodStreamed(5,
        new ::grpc::internal::StreamedUnaryHandler< ::grpc::channelz::v1::GetSocketRequest, ::grpc::channelz::v1::GetSocketResponse>(std::bind(&WithStreamedUnaryMethod_GetSocket<BaseClass>::StreamedGetSocket, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_GetSocket() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetSocket(::grpc::ServerContext* context, const ::grpc::channelz::v1::GetSocketRequest* request, ::grpc::channelz::v1::GetSocketResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetSocket(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::grpc::channelz::v1::GetSocketRequest,::grpc::channelz::v1::GetSocketResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_GetTopChannels<WithStreamedUnaryMethod_GetServers<WithStreamedUnaryMethod_GetServerSockets<WithStreamedUnaryMethod_GetChannel<WithStreamedUnaryMethod_GetSubchannel<WithStreamedUnaryMethod_GetSocket<Service > > > > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_GetTopChannels<WithStreamedUnaryMethod_GetServers<WithStreamedUnaryMethod_GetServerSockets<WithStreamedUnaryMethod_GetChannel<WithStreamedUnaryMethod_GetSubchannel<WithStreamedUnaryMethod_GetSocket<Service > > > > > > StreamedService;
};

}  // namespace v1
}  // namespace channelz
}  // namespace grpc


#endif  // GRPC_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto__INCLUDED
