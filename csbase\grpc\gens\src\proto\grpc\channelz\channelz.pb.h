// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: src/proto/grpc/channelz/channelz.proto

#ifndef PROTOBUF_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto__INCLUDED
#define PROTOBUF_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3005001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/any.pb.h>
#include <google/protobuf/duration.pb.h>
#include <google/protobuf/timestamp.pb.h>
#include <google/protobuf/wrappers.pb.h>
// @@protoc_insertion_point(includes)

namespace protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[37];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
void InitDefaultsChannelImpl();
void InitDefaultsChannel();
void InitDefaultsSubchannelImpl();
void InitDefaultsSubchannel();
void InitDefaultsChannelConnectivityStateImpl();
void InitDefaultsChannelConnectivityState();
void InitDefaultsChannelDataImpl();
void InitDefaultsChannelData();
void InitDefaultsChannelTraceEventImpl();
void InitDefaultsChannelTraceEvent();
void InitDefaultsChannelTraceImpl();
void InitDefaultsChannelTrace();
void InitDefaultsChannelRefImpl();
void InitDefaultsChannelRef();
void InitDefaultsSubchannelRefImpl();
void InitDefaultsSubchannelRef();
void InitDefaultsSocketRefImpl();
void InitDefaultsSocketRef();
void InitDefaultsServerRefImpl();
void InitDefaultsServerRef();
void InitDefaultsServerImpl();
void InitDefaultsServer();
void InitDefaultsServerDataImpl();
void InitDefaultsServerData();
void InitDefaultsSocketImpl();
void InitDefaultsSocket();
void InitDefaultsSocketDataImpl();
void InitDefaultsSocketData();
void InitDefaultsAddress_TcpIpAddressImpl();
void InitDefaultsAddress_TcpIpAddress();
void InitDefaultsAddress_UdsAddressImpl();
void InitDefaultsAddress_UdsAddress();
void InitDefaultsAddress_OtherAddressImpl();
void InitDefaultsAddress_OtherAddress();
void InitDefaultsAddressImpl();
void InitDefaultsAddress();
void InitDefaultsSecurity_TlsImpl();
void InitDefaultsSecurity_Tls();
void InitDefaultsSecurity_OtherSecurityImpl();
void InitDefaultsSecurity_OtherSecurity();
void InitDefaultsSecurityImpl();
void InitDefaultsSecurity();
void InitDefaultsSocketOptionImpl();
void InitDefaultsSocketOption();
void InitDefaultsSocketOptionTimeoutImpl();
void InitDefaultsSocketOptionTimeout();
void InitDefaultsSocketOptionLingerImpl();
void InitDefaultsSocketOptionLinger();
void InitDefaultsSocketOptionTcpInfoImpl();
void InitDefaultsSocketOptionTcpInfo();
void InitDefaultsGetTopChannelsRequestImpl();
void InitDefaultsGetTopChannelsRequest();
void InitDefaultsGetTopChannelsResponseImpl();
void InitDefaultsGetTopChannelsResponse();
void InitDefaultsGetServersRequestImpl();
void InitDefaultsGetServersRequest();
void InitDefaultsGetServersResponseImpl();
void InitDefaultsGetServersResponse();
void InitDefaultsGetServerSocketsRequestImpl();
void InitDefaultsGetServerSocketsRequest();
void InitDefaultsGetServerSocketsResponseImpl();
void InitDefaultsGetServerSocketsResponse();
void InitDefaultsGetChannelRequestImpl();
void InitDefaultsGetChannelRequest();
void InitDefaultsGetChannelResponseImpl();
void InitDefaultsGetChannelResponse();
void InitDefaultsGetSubchannelRequestImpl();
void InitDefaultsGetSubchannelRequest();
void InitDefaultsGetSubchannelResponseImpl();
void InitDefaultsGetSubchannelResponse();
void InitDefaultsGetSocketRequestImpl();
void InitDefaultsGetSocketRequest();
void InitDefaultsGetSocketResponseImpl();
void InitDefaultsGetSocketResponse();
inline void InitDefaults() {
  InitDefaultsChannel();
  InitDefaultsSubchannel();
  InitDefaultsChannelConnectivityState();
  InitDefaultsChannelData();
  InitDefaultsChannelTraceEvent();
  InitDefaultsChannelTrace();
  InitDefaultsChannelRef();
  InitDefaultsSubchannelRef();
  InitDefaultsSocketRef();
  InitDefaultsServerRef();
  InitDefaultsServer();
  InitDefaultsServerData();
  InitDefaultsSocket();
  InitDefaultsSocketData();
  InitDefaultsAddress_TcpIpAddress();
  InitDefaultsAddress_UdsAddress();
  InitDefaultsAddress_OtherAddress();
  InitDefaultsAddress();
  InitDefaultsSecurity_Tls();
  InitDefaultsSecurity_OtherSecurity();
  InitDefaultsSecurity();
  InitDefaultsSocketOption();
  InitDefaultsSocketOptionTimeout();
  InitDefaultsSocketOptionLinger();
  InitDefaultsSocketOptionTcpInfo();
  InitDefaultsGetTopChannelsRequest();
  InitDefaultsGetTopChannelsResponse();
  InitDefaultsGetServersRequest();
  InitDefaultsGetServersResponse();
  InitDefaultsGetServerSocketsRequest();
  InitDefaultsGetServerSocketsResponse();
  InitDefaultsGetChannelRequest();
  InitDefaultsGetChannelResponse();
  InitDefaultsGetSubchannelRequest();
  InitDefaultsGetSubchannelResponse();
  InitDefaultsGetSocketRequest();
  InitDefaultsGetSocketResponse();
}
}  // namespace protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto
namespace grpc {
namespace channelz {
namespace v1 {
class Address;
class AddressDefaultTypeInternal;
extern AddressDefaultTypeInternal _Address_default_instance_;
class Address_OtherAddress;
class Address_OtherAddressDefaultTypeInternal;
extern Address_OtherAddressDefaultTypeInternal _Address_OtherAddress_default_instance_;
class Address_TcpIpAddress;
class Address_TcpIpAddressDefaultTypeInternal;
extern Address_TcpIpAddressDefaultTypeInternal _Address_TcpIpAddress_default_instance_;
class Address_UdsAddress;
class Address_UdsAddressDefaultTypeInternal;
extern Address_UdsAddressDefaultTypeInternal _Address_UdsAddress_default_instance_;
class Channel;
class ChannelDefaultTypeInternal;
extern ChannelDefaultTypeInternal _Channel_default_instance_;
class ChannelConnectivityState;
class ChannelConnectivityStateDefaultTypeInternal;
extern ChannelConnectivityStateDefaultTypeInternal _ChannelConnectivityState_default_instance_;
class ChannelData;
class ChannelDataDefaultTypeInternal;
extern ChannelDataDefaultTypeInternal _ChannelData_default_instance_;
class ChannelRef;
class ChannelRefDefaultTypeInternal;
extern ChannelRefDefaultTypeInternal _ChannelRef_default_instance_;
class ChannelTrace;
class ChannelTraceDefaultTypeInternal;
extern ChannelTraceDefaultTypeInternal _ChannelTrace_default_instance_;
class ChannelTraceEvent;
class ChannelTraceEventDefaultTypeInternal;
extern ChannelTraceEventDefaultTypeInternal _ChannelTraceEvent_default_instance_;
class GetChannelRequest;
class GetChannelRequestDefaultTypeInternal;
extern GetChannelRequestDefaultTypeInternal _GetChannelRequest_default_instance_;
class GetChannelResponse;
class GetChannelResponseDefaultTypeInternal;
extern GetChannelResponseDefaultTypeInternal _GetChannelResponse_default_instance_;
class GetServerSocketsRequest;
class GetServerSocketsRequestDefaultTypeInternal;
extern GetServerSocketsRequestDefaultTypeInternal _GetServerSocketsRequest_default_instance_;
class GetServerSocketsResponse;
class GetServerSocketsResponseDefaultTypeInternal;
extern GetServerSocketsResponseDefaultTypeInternal _GetServerSocketsResponse_default_instance_;
class GetServersRequest;
class GetServersRequestDefaultTypeInternal;
extern GetServersRequestDefaultTypeInternal _GetServersRequest_default_instance_;
class GetServersResponse;
class GetServersResponseDefaultTypeInternal;
extern GetServersResponseDefaultTypeInternal _GetServersResponse_default_instance_;
class GetSocketRequest;
class GetSocketRequestDefaultTypeInternal;
extern GetSocketRequestDefaultTypeInternal _GetSocketRequest_default_instance_;
class GetSocketResponse;
class GetSocketResponseDefaultTypeInternal;
extern GetSocketResponseDefaultTypeInternal _GetSocketResponse_default_instance_;
class GetSubchannelRequest;
class GetSubchannelRequestDefaultTypeInternal;
extern GetSubchannelRequestDefaultTypeInternal _GetSubchannelRequest_default_instance_;
class GetSubchannelResponse;
class GetSubchannelResponseDefaultTypeInternal;
extern GetSubchannelResponseDefaultTypeInternal _GetSubchannelResponse_default_instance_;
class GetTopChannelsRequest;
class GetTopChannelsRequestDefaultTypeInternal;
extern GetTopChannelsRequestDefaultTypeInternal _GetTopChannelsRequest_default_instance_;
class GetTopChannelsResponse;
class GetTopChannelsResponseDefaultTypeInternal;
extern GetTopChannelsResponseDefaultTypeInternal _GetTopChannelsResponse_default_instance_;
class Security;
class SecurityDefaultTypeInternal;
extern SecurityDefaultTypeInternal _Security_default_instance_;
class Security_OtherSecurity;
class Security_OtherSecurityDefaultTypeInternal;
extern Security_OtherSecurityDefaultTypeInternal _Security_OtherSecurity_default_instance_;
class Security_Tls;
class Security_TlsDefaultTypeInternal;
extern Security_TlsDefaultTypeInternal _Security_Tls_default_instance_;
class Server;
class ServerDefaultTypeInternal;
extern ServerDefaultTypeInternal _Server_default_instance_;
class ServerData;
class ServerDataDefaultTypeInternal;
extern ServerDataDefaultTypeInternal _ServerData_default_instance_;
class ServerRef;
class ServerRefDefaultTypeInternal;
extern ServerRefDefaultTypeInternal _ServerRef_default_instance_;
class Socket;
class SocketDefaultTypeInternal;
extern SocketDefaultTypeInternal _Socket_default_instance_;
class SocketData;
class SocketDataDefaultTypeInternal;
extern SocketDataDefaultTypeInternal _SocketData_default_instance_;
class SocketOption;
class SocketOptionDefaultTypeInternal;
extern SocketOptionDefaultTypeInternal _SocketOption_default_instance_;
class SocketOptionLinger;
class SocketOptionLingerDefaultTypeInternal;
extern SocketOptionLingerDefaultTypeInternal _SocketOptionLinger_default_instance_;
class SocketOptionTcpInfo;
class SocketOptionTcpInfoDefaultTypeInternal;
extern SocketOptionTcpInfoDefaultTypeInternal _SocketOptionTcpInfo_default_instance_;
class SocketOptionTimeout;
class SocketOptionTimeoutDefaultTypeInternal;
extern SocketOptionTimeoutDefaultTypeInternal _SocketOptionTimeout_default_instance_;
class SocketRef;
class SocketRefDefaultTypeInternal;
extern SocketRefDefaultTypeInternal _SocketRef_default_instance_;
class Subchannel;
class SubchannelDefaultTypeInternal;
extern SubchannelDefaultTypeInternal _Subchannel_default_instance_;
class SubchannelRef;
class SubchannelRefDefaultTypeInternal;
extern SubchannelRefDefaultTypeInternal _SubchannelRef_default_instance_;
}  // namespace v1
}  // namespace channelz
}  // namespace grpc
namespace grpc {
namespace channelz {
namespace v1 {

enum ChannelConnectivityState_State {
  ChannelConnectivityState_State_UNKNOWN = 0,
  ChannelConnectivityState_State_IDLE = 1,
  ChannelConnectivityState_State_CONNECTING = 2,
  ChannelConnectivityState_State_READY = 3,
  ChannelConnectivityState_State_TRANSIENT_FAILURE = 4,
  ChannelConnectivityState_State_SHUTDOWN = 5,
  ChannelConnectivityState_State_ChannelConnectivityState_State_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  ChannelConnectivityState_State_ChannelConnectivityState_State_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool ChannelConnectivityState_State_IsValid(int value);
const ChannelConnectivityState_State ChannelConnectivityState_State_State_MIN = ChannelConnectivityState_State_UNKNOWN;
const ChannelConnectivityState_State ChannelConnectivityState_State_State_MAX = ChannelConnectivityState_State_SHUTDOWN;
const int ChannelConnectivityState_State_State_ARRAYSIZE = ChannelConnectivityState_State_State_MAX + 1;

const ::google::protobuf::EnumDescriptor* ChannelConnectivityState_State_descriptor();
inline const ::std::string& ChannelConnectivityState_State_Name(ChannelConnectivityState_State value) {
  return ::google::protobuf::internal::NameOfEnum(
    ChannelConnectivityState_State_descriptor(), value);
}
inline bool ChannelConnectivityState_State_Parse(
    const ::std::string& name, ChannelConnectivityState_State* value) {
  return ::google::protobuf::internal::ParseNamedEnum<ChannelConnectivityState_State>(
    ChannelConnectivityState_State_descriptor(), name, value);
}
enum ChannelTraceEvent_Severity {
  ChannelTraceEvent_Severity_CT_UNKNOWN = 0,
  ChannelTraceEvent_Severity_CT_INFO = 1,
  ChannelTraceEvent_Severity_CT_WARNING = 2,
  ChannelTraceEvent_Severity_CT_ERROR = 3,
  ChannelTraceEvent_Severity_ChannelTraceEvent_Severity_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  ChannelTraceEvent_Severity_ChannelTraceEvent_Severity_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool ChannelTraceEvent_Severity_IsValid(int value);
const ChannelTraceEvent_Severity ChannelTraceEvent_Severity_Severity_MIN = ChannelTraceEvent_Severity_CT_UNKNOWN;
const ChannelTraceEvent_Severity ChannelTraceEvent_Severity_Severity_MAX = ChannelTraceEvent_Severity_CT_ERROR;
const int ChannelTraceEvent_Severity_Severity_ARRAYSIZE = ChannelTraceEvent_Severity_Severity_MAX + 1;

const ::google::protobuf::EnumDescriptor* ChannelTraceEvent_Severity_descriptor();
inline const ::std::string& ChannelTraceEvent_Severity_Name(ChannelTraceEvent_Severity value) {
  return ::google::protobuf::internal::NameOfEnum(
    ChannelTraceEvent_Severity_descriptor(), value);
}
inline bool ChannelTraceEvent_Severity_Parse(
    const ::std::string& name, ChannelTraceEvent_Severity* value) {
  return ::google::protobuf::internal::ParseNamedEnum<ChannelTraceEvent_Severity>(
    ChannelTraceEvent_Severity_descriptor(), name, value);
}
// ===================================================================

class Channel : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.Channel) */ {
 public:
  Channel();
  virtual ~Channel();

  Channel(const Channel& from);

  inline Channel& operator=(const Channel& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Channel(Channel&& from) noexcept
    : Channel() {
    *this = ::std::move(from);
  }

  inline Channel& operator=(Channel&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Channel& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Channel* internal_default_instance() {
    return reinterpret_cast<const Channel*>(
               &_Channel_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    0;

  void Swap(Channel* other);
  friend void swap(Channel& a, Channel& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Channel* New() const PROTOBUF_FINAL { return New(NULL); }

  Channel* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const Channel& from);
  void MergeFrom(const Channel& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(Channel* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .grpc.channelz.v1.ChannelRef channel_ref = 3;
  int channel_ref_size() const;
  void clear_channel_ref();
  static const int kChannelRefFieldNumber = 3;
  const ::grpc::channelz::v1::ChannelRef& channel_ref(int index) const;
  ::grpc::channelz::v1::ChannelRef* mutable_channel_ref(int index);
  ::grpc::channelz::v1::ChannelRef* add_channel_ref();
  ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::ChannelRef >*
      mutable_channel_ref();
  const ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::ChannelRef >&
      channel_ref() const;

  // repeated .grpc.channelz.v1.SubchannelRef subchannel_ref = 4;
  int subchannel_ref_size() const;
  void clear_subchannel_ref();
  static const int kSubchannelRefFieldNumber = 4;
  const ::grpc::channelz::v1::SubchannelRef& subchannel_ref(int index) const;
  ::grpc::channelz::v1::SubchannelRef* mutable_subchannel_ref(int index);
  ::grpc::channelz::v1::SubchannelRef* add_subchannel_ref();
  ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SubchannelRef >*
      mutable_subchannel_ref();
  const ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SubchannelRef >&
      subchannel_ref() const;

  // repeated .grpc.channelz.v1.SocketRef socket_ref = 5;
  int socket_ref_size() const;
  void clear_socket_ref();
  static const int kSocketRefFieldNumber = 5;
  const ::grpc::channelz::v1::SocketRef& socket_ref(int index) const;
  ::grpc::channelz::v1::SocketRef* mutable_socket_ref(int index);
  ::grpc::channelz::v1::SocketRef* add_socket_ref();
  ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SocketRef >*
      mutable_socket_ref();
  const ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SocketRef >&
      socket_ref() const;

  // .grpc.channelz.v1.ChannelRef ref = 1;
  bool has_ref() const;
  void clear_ref();
  static const int kRefFieldNumber = 1;
  const ::grpc::channelz::v1::ChannelRef& ref() const;
  ::grpc::channelz::v1::ChannelRef* release_ref();
  ::grpc::channelz::v1::ChannelRef* mutable_ref();
  void set_allocated_ref(::grpc::channelz::v1::ChannelRef* ref);

  // .grpc.channelz.v1.ChannelData data = 2;
  bool has_data() const;
  void clear_data();
  static const int kDataFieldNumber = 2;
  const ::grpc::channelz::v1::ChannelData& data() const;
  ::grpc::channelz::v1::ChannelData* release_data();
  ::grpc::channelz::v1::ChannelData* mutable_data();
  void set_allocated_data(::grpc::channelz::v1::ChannelData* data);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.Channel)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::ChannelRef > channel_ref_;
  ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SubchannelRef > subchannel_ref_;
  ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SocketRef > socket_ref_;
  ::grpc::channelz::v1::ChannelRef* ref_;
  ::grpc::channelz::v1::ChannelData* data_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsChannelImpl();
};
// -------------------------------------------------------------------

class Subchannel : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.Subchannel) */ {
 public:
  Subchannel();
  virtual ~Subchannel();

  Subchannel(const Subchannel& from);

  inline Subchannel& operator=(const Subchannel& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Subchannel(Subchannel&& from) noexcept
    : Subchannel() {
    *this = ::std::move(from);
  }

  inline Subchannel& operator=(Subchannel&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Subchannel& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Subchannel* internal_default_instance() {
    return reinterpret_cast<const Subchannel*>(
               &_Subchannel_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    1;

  void Swap(Subchannel* other);
  friend void swap(Subchannel& a, Subchannel& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Subchannel* New() const PROTOBUF_FINAL { return New(NULL); }

  Subchannel* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const Subchannel& from);
  void MergeFrom(const Subchannel& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(Subchannel* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .grpc.channelz.v1.ChannelRef channel_ref = 3;
  int channel_ref_size() const;
  void clear_channel_ref();
  static const int kChannelRefFieldNumber = 3;
  const ::grpc::channelz::v1::ChannelRef& channel_ref(int index) const;
  ::grpc::channelz::v1::ChannelRef* mutable_channel_ref(int index);
  ::grpc::channelz::v1::ChannelRef* add_channel_ref();
  ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::ChannelRef >*
      mutable_channel_ref();
  const ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::ChannelRef >&
      channel_ref() const;

  // repeated .grpc.channelz.v1.SubchannelRef subchannel_ref = 4;
  int subchannel_ref_size() const;
  void clear_subchannel_ref();
  static const int kSubchannelRefFieldNumber = 4;
  const ::grpc::channelz::v1::SubchannelRef& subchannel_ref(int index) const;
  ::grpc::channelz::v1::SubchannelRef* mutable_subchannel_ref(int index);
  ::grpc::channelz::v1::SubchannelRef* add_subchannel_ref();
  ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SubchannelRef >*
      mutable_subchannel_ref();
  const ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SubchannelRef >&
      subchannel_ref() const;

  // repeated .grpc.channelz.v1.SocketRef socket_ref = 5;
  int socket_ref_size() const;
  void clear_socket_ref();
  static const int kSocketRefFieldNumber = 5;
  const ::grpc::channelz::v1::SocketRef& socket_ref(int index) const;
  ::grpc::channelz::v1::SocketRef* mutable_socket_ref(int index);
  ::grpc::channelz::v1::SocketRef* add_socket_ref();
  ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SocketRef >*
      mutable_socket_ref();
  const ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SocketRef >&
      socket_ref() const;

  // .grpc.channelz.v1.SubchannelRef ref = 1;
  bool has_ref() const;
  void clear_ref();
  static const int kRefFieldNumber = 1;
  const ::grpc::channelz::v1::SubchannelRef& ref() const;
  ::grpc::channelz::v1::SubchannelRef* release_ref();
  ::grpc::channelz::v1::SubchannelRef* mutable_ref();
  void set_allocated_ref(::grpc::channelz::v1::SubchannelRef* ref);

  // .grpc.channelz.v1.ChannelData data = 2;
  bool has_data() const;
  void clear_data();
  static const int kDataFieldNumber = 2;
  const ::grpc::channelz::v1::ChannelData& data() const;
  ::grpc::channelz::v1::ChannelData* release_data();
  ::grpc::channelz::v1::ChannelData* mutable_data();
  void set_allocated_data(::grpc::channelz::v1::ChannelData* data);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.Subchannel)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::ChannelRef > channel_ref_;
  ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SubchannelRef > subchannel_ref_;
  ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SocketRef > socket_ref_;
  ::grpc::channelz::v1::SubchannelRef* ref_;
  ::grpc::channelz::v1::ChannelData* data_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsSubchannelImpl();
};
// -------------------------------------------------------------------

class ChannelConnectivityState : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.ChannelConnectivityState) */ {
 public:
  ChannelConnectivityState();
  virtual ~ChannelConnectivityState();

  ChannelConnectivityState(const ChannelConnectivityState& from);

  inline ChannelConnectivityState& operator=(const ChannelConnectivityState& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ChannelConnectivityState(ChannelConnectivityState&& from) noexcept
    : ChannelConnectivityState() {
    *this = ::std::move(from);
  }

  inline ChannelConnectivityState& operator=(ChannelConnectivityState&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ChannelConnectivityState& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ChannelConnectivityState* internal_default_instance() {
    return reinterpret_cast<const ChannelConnectivityState*>(
               &_ChannelConnectivityState_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    2;

  void Swap(ChannelConnectivityState* other);
  friend void swap(ChannelConnectivityState& a, ChannelConnectivityState& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ChannelConnectivityState* New() const PROTOBUF_FINAL { return New(NULL); }

  ChannelConnectivityState* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ChannelConnectivityState& from);
  void MergeFrom(const ChannelConnectivityState& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ChannelConnectivityState* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  typedef ChannelConnectivityState_State State;
  static const State UNKNOWN =
    ChannelConnectivityState_State_UNKNOWN;
  static const State IDLE =
    ChannelConnectivityState_State_IDLE;
  static const State CONNECTING =
    ChannelConnectivityState_State_CONNECTING;
  static const State READY =
    ChannelConnectivityState_State_READY;
  static const State TRANSIENT_FAILURE =
    ChannelConnectivityState_State_TRANSIENT_FAILURE;
  static const State SHUTDOWN =
    ChannelConnectivityState_State_SHUTDOWN;
  static inline bool State_IsValid(int value) {
    return ChannelConnectivityState_State_IsValid(value);
  }
  static const State State_MIN =
    ChannelConnectivityState_State_State_MIN;
  static const State State_MAX =
    ChannelConnectivityState_State_State_MAX;
  static const int State_ARRAYSIZE =
    ChannelConnectivityState_State_State_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  State_descriptor() {
    return ChannelConnectivityState_State_descriptor();
  }
  static inline const ::std::string& State_Name(State value) {
    return ChannelConnectivityState_State_Name(value);
  }
  static inline bool State_Parse(const ::std::string& name,
      State* value) {
    return ChannelConnectivityState_State_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // .grpc.channelz.v1.ChannelConnectivityState.State state = 1;
  void clear_state();
  static const int kStateFieldNumber = 1;
  ::grpc::channelz::v1::ChannelConnectivityState_State state() const;
  void set_state(::grpc::channelz::v1::ChannelConnectivityState_State value);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.ChannelConnectivityState)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  int state_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsChannelConnectivityStateImpl();
};
// -------------------------------------------------------------------

class ChannelData : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.ChannelData) */ {
 public:
  ChannelData();
  virtual ~ChannelData();

  ChannelData(const ChannelData& from);

  inline ChannelData& operator=(const ChannelData& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ChannelData(ChannelData&& from) noexcept
    : ChannelData() {
    *this = ::std::move(from);
  }

  inline ChannelData& operator=(ChannelData&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ChannelData& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ChannelData* internal_default_instance() {
    return reinterpret_cast<const ChannelData*>(
               &_ChannelData_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    3;

  void Swap(ChannelData* other);
  friend void swap(ChannelData& a, ChannelData& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ChannelData* New() const PROTOBUF_FINAL { return New(NULL); }

  ChannelData* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ChannelData& from);
  void MergeFrom(const ChannelData& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ChannelData* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string target = 2;
  void clear_target();
  static const int kTargetFieldNumber = 2;
  const ::std::string& target() const;
  void set_target(const ::std::string& value);
  #if LANG_CXX11
  void set_target(::std::string&& value);
  #endif
  void set_target(const char* value);
  void set_target(const char* value, size_t size);
  ::std::string* mutable_target();
  ::std::string* release_target();
  void set_allocated_target(::std::string* target);

  // .grpc.channelz.v1.ChannelConnectivityState state = 1;
  bool has_state() const;
  void clear_state();
  static const int kStateFieldNumber = 1;
  const ::grpc::channelz::v1::ChannelConnectivityState& state() const;
  ::grpc::channelz::v1::ChannelConnectivityState* release_state();
  ::grpc::channelz::v1::ChannelConnectivityState* mutable_state();
  void set_allocated_state(::grpc::channelz::v1::ChannelConnectivityState* state);

  // .grpc.channelz.v1.ChannelTrace trace = 3;
  bool has_trace() const;
  void clear_trace();
  static const int kTraceFieldNumber = 3;
  const ::grpc::channelz::v1::ChannelTrace& trace() const;
  ::grpc::channelz::v1::ChannelTrace* release_trace();
  ::grpc::channelz::v1::ChannelTrace* mutable_trace();
  void set_allocated_trace(::grpc::channelz::v1::ChannelTrace* trace);

  // .google.protobuf.Timestamp last_call_started_timestamp = 7;
  bool has_last_call_started_timestamp() const;
  void clear_last_call_started_timestamp();
  static const int kLastCallStartedTimestampFieldNumber = 7;
  const ::google::protobuf::Timestamp& last_call_started_timestamp() const;
  ::google::protobuf::Timestamp* release_last_call_started_timestamp();
  ::google::protobuf::Timestamp* mutable_last_call_started_timestamp();
  void set_allocated_last_call_started_timestamp(::google::protobuf::Timestamp* last_call_started_timestamp);

  // int64 calls_started = 4;
  void clear_calls_started();
  static const int kCallsStartedFieldNumber = 4;
  ::google::protobuf::int64 calls_started() const;
  void set_calls_started(::google::protobuf::int64 value);

  // int64 calls_succeeded = 5;
  void clear_calls_succeeded();
  static const int kCallsSucceededFieldNumber = 5;
  ::google::protobuf::int64 calls_succeeded() const;
  void set_calls_succeeded(::google::protobuf::int64 value);

  // int64 calls_failed = 6;
  void clear_calls_failed();
  static const int kCallsFailedFieldNumber = 6;
  ::google::protobuf::int64 calls_failed() const;
  void set_calls_failed(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.ChannelData)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr target_;
  ::grpc::channelz::v1::ChannelConnectivityState* state_;
  ::grpc::channelz::v1::ChannelTrace* trace_;
  ::google::protobuf::Timestamp* last_call_started_timestamp_;
  ::google::protobuf::int64 calls_started_;
  ::google::protobuf::int64 calls_succeeded_;
  ::google::protobuf::int64 calls_failed_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsChannelDataImpl();
};
// -------------------------------------------------------------------

class ChannelTraceEvent : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.ChannelTraceEvent) */ {
 public:
  ChannelTraceEvent();
  virtual ~ChannelTraceEvent();

  ChannelTraceEvent(const ChannelTraceEvent& from);

  inline ChannelTraceEvent& operator=(const ChannelTraceEvent& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ChannelTraceEvent(ChannelTraceEvent&& from) noexcept
    : ChannelTraceEvent() {
    *this = ::std::move(from);
  }

  inline ChannelTraceEvent& operator=(ChannelTraceEvent&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ChannelTraceEvent& default_instance();

  enum ChildRefCase {
    kChannelRef = 4,
    kSubchannelRef = 5,
    CHILD_REF_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ChannelTraceEvent* internal_default_instance() {
    return reinterpret_cast<const ChannelTraceEvent*>(
               &_ChannelTraceEvent_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    4;

  void Swap(ChannelTraceEvent* other);
  friend void swap(ChannelTraceEvent& a, ChannelTraceEvent& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ChannelTraceEvent* New() const PROTOBUF_FINAL { return New(NULL); }

  ChannelTraceEvent* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ChannelTraceEvent& from);
  void MergeFrom(const ChannelTraceEvent& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ChannelTraceEvent* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  typedef ChannelTraceEvent_Severity Severity;
  static const Severity CT_UNKNOWN =
    ChannelTraceEvent_Severity_CT_UNKNOWN;
  static const Severity CT_INFO =
    ChannelTraceEvent_Severity_CT_INFO;
  static const Severity CT_WARNING =
    ChannelTraceEvent_Severity_CT_WARNING;
  static const Severity CT_ERROR =
    ChannelTraceEvent_Severity_CT_ERROR;
  static inline bool Severity_IsValid(int value) {
    return ChannelTraceEvent_Severity_IsValid(value);
  }
  static const Severity Severity_MIN =
    ChannelTraceEvent_Severity_Severity_MIN;
  static const Severity Severity_MAX =
    ChannelTraceEvent_Severity_Severity_MAX;
  static const int Severity_ARRAYSIZE =
    ChannelTraceEvent_Severity_Severity_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  Severity_descriptor() {
    return ChannelTraceEvent_Severity_descriptor();
  }
  static inline const ::std::string& Severity_Name(Severity value) {
    return ChannelTraceEvent_Severity_Name(value);
  }
  static inline bool Severity_Parse(const ::std::string& name,
      Severity* value) {
    return ChannelTraceEvent_Severity_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // string description = 1;
  void clear_description();
  static const int kDescriptionFieldNumber = 1;
  const ::std::string& description() const;
  void set_description(const ::std::string& value);
  #if LANG_CXX11
  void set_description(::std::string&& value);
  #endif
  void set_description(const char* value);
  void set_description(const char* value, size_t size);
  ::std::string* mutable_description();
  ::std::string* release_description();
  void set_allocated_description(::std::string* description);

  // .google.protobuf.Timestamp timestamp = 3;
  bool has_timestamp() const;
  void clear_timestamp();
  static const int kTimestampFieldNumber = 3;
  const ::google::protobuf::Timestamp& timestamp() const;
  ::google::protobuf::Timestamp* release_timestamp();
  ::google::protobuf::Timestamp* mutable_timestamp();
  void set_allocated_timestamp(::google::protobuf::Timestamp* timestamp);

  // .grpc.channelz.v1.ChannelTraceEvent.Severity severity = 2;
  void clear_severity();
  static const int kSeverityFieldNumber = 2;
  ::grpc::channelz::v1::ChannelTraceEvent_Severity severity() const;
  void set_severity(::grpc::channelz::v1::ChannelTraceEvent_Severity value);

  // .grpc.channelz.v1.ChannelRef channel_ref = 4;
  bool has_channel_ref() const;
  void clear_channel_ref();
  static const int kChannelRefFieldNumber = 4;
  const ::grpc::channelz::v1::ChannelRef& channel_ref() const;
  ::grpc::channelz::v1::ChannelRef* release_channel_ref();
  ::grpc::channelz::v1::ChannelRef* mutable_channel_ref();
  void set_allocated_channel_ref(::grpc::channelz::v1::ChannelRef* channel_ref);

  // .grpc.channelz.v1.SubchannelRef subchannel_ref = 5;
  bool has_subchannel_ref() const;
  void clear_subchannel_ref();
  static const int kSubchannelRefFieldNumber = 5;
  const ::grpc::channelz::v1::SubchannelRef& subchannel_ref() const;
  ::grpc::channelz::v1::SubchannelRef* release_subchannel_ref();
  ::grpc::channelz::v1::SubchannelRef* mutable_subchannel_ref();
  void set_allocated_subchannel_ref(::grpc::channelz::v1::SubchannelRef* subchannel_ref);

  ChildRefCase child_ref_case() const;
  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.ChannelTraceEvent)
 private:
  void set_has_channel_ref();
  void set_has_subchannel_ref();

  inline bool has_child_ref() const;
  void clear_child_ref();
  inline void clear_has_child_ref();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr description_;
  ::google::protobuf::Timestamp* timestamp_;
  int severity_;
  union ChildRefUnion {
    ChildRefUnion() {}
    ::grpc::channelz::v1::ChannelRef* channel_ref_;
    ::grpc::channelz::v1::SubchannelRef* subchannel_ref_;
  } child_ref_;
  mutable int _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsChannelTraceEventImpl();
};
// -------------------------------------------------------------------

class ChannelTrace : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.ChannelTrace) */ {
 public:
  ChannelTrace();
  virtual ~ChannelTrace();

  ChannelTrace(const ChannelTrace& from);

  inline ChannelTrace& operator=(const ChannelTrace& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ChannelTrace(ChannelTrace&& from) noexcept
    : ChannelTrace() {
    *this = ::std::move(from);
  }

  inline ChannelTrace& operator=(ChannelTrace&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ChannelTrace& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ChannelTrace* internal_default_instance() {
    return reinterpret_cast<const ChannelTrace*>(
               &_ChannelTrace_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    5;

  void Swap(ChannelTrace* other);
  friend void swap(ChannelTrace& a, ChannelTrace& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ChannelTrace* New() const PROTOBUF_FINAL { return New(NULL); }

  ChannelTrace* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ChannelTrace& from);
  void MergeFrom(const ChannelTrace& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ChannelTrace* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .grpc.channelz.v1.ChannelTraceEvent events = 3;
  int events_size() const;
  void clear_events();
  static const int kEventsFieldNumber = 3;
  const ::grpc::channelz::v1::ChannelTraceEvent& events(int index) const;
  ::grpc::channelz::v1::ChannelTraceEvent* mutable_events(int index);
  ::grpc::channelz::v1::ChannelTraceEvent* add_events();
  ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::ChannelTraceEvent >*
      mutable_events();
  const ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::ChannelTraceEvent >&
      events() const;

  // .google.protobuf.Timestamp creation_timestamp = 2;
  bool has_creation_timestamp() const;
  void clear_creation_timestamp();
  static const int kCreationTimestampFieldNumber = 2;
  const ::google::protobuf::Timestamp& creation_timestamp() const;
  ::google::protobuf::Timestamp* release_creation_timestamp();
  ::google::protobuf::Timestamp* mutable_creation_timestamp();
  void set_allocated_creation_timestamp(::google::protobuf::Timestamp* creation_timestamp);

  // int64 num_events_logged = 1;
  void clear_num_events_logged();
  static const int kNumEventsLoggedFieldNumber = 1;
  ::google::protobuf::int64 num_events_logged() const;
  void set_num_events_logged(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.ChannelTrace)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::ChannelTraceEvent > events_;
  ::google::protobuf::Timestamp* creation_timestamp_;
  ::google::protobuf::int64 num_events_logged_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsChannelTraceImpl();
};
// -------------------------------------------------------------------

class ChannelRef : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.ChannelRef) */ {
 public:
  ChannelRef();
  virtual ~ChannelRef();

  ChannelRef(const ChannelRef& from);

  inline ChannelRef& operator=(const ChannelRef& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ChannelRef(ChannelRef&& from) noexcept
    : ChannelRef() {
    *this = ::std::move(from);
  }

  inline ChannelRef& operator=(ChannelRef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ChannelRef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ChannelRef* internal_default_instance() {
    return reinterpret_cast<const ChannelRef*>(
               &_ChannelRef_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    6;

  void Swap(ChannelRef* other);
  friend void swap(ChannelRef& a, ChannelRef& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ChannelRef* New() const PROTOBUF_FINAL { return New(NULL); }

  ChannelRef* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ChannelRef& from);
  void MergeFrom(const ChannelRef& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ChannelRef* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string name = 2;
  void clear_name();
  static const int kNameFieldNumber = 2;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // int64 channel_id = 1;
  void clear_channel_id();
  static const int kChannelIdFieldNumber = 1;
  ::google::protobuf::int64 channel_id() const;
  void set_channel_id(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.ChannelRef)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::int64 channel_id_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsChannelRefImpl();
};
// -------------------------------------------------------------------

class SubchannelRef : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.SubchannelRef) */ {
 public:
  SubchannelRef();
  virtual ~SubchannelRef();

  SubchannelRef(const SubchannelRef& from);

  inline SubchannelRef& operator=(const SubchannelRef& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SubchannelRef(SubchannelRef&& from) noexcept
    : SubchannelRef() {
    *this = ::std::move(from);
  }

  inline SubchannelRef& operator=(SubchannelRef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const SubchannelRef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SubchannelRef* internal_default_instance() {
    return reinterpret_cast<const SubchannelRef*>(
               &_SubchannelRef_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    7;

  void Swap(SubchannelRef* other);
  friend void swap(SubchannelRef& a, SubchannelRef& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SubchannelRef* New() const PROTOBUF_FINAL { return New(NULL); }

  SubchannelRef* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const SubchannelRef& from);
  void MergeFrom(const SubchannelRef& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(SubchannelRef* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string name = 8;
  void clear_name();
  static const int kNameFieldNumber = 8;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // int64 subchannel_id = 7;
  void clear_subchannel_id();
  static const int kSubchannelIdFieldNumber = 7;
  ::google::protobuf::int64 subchannel_id() const;
  void set_subchannel_id(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.SubchannelRef)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::int64 subchannel_id_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsSubchannelRefImpl();
};
// -------------------------------------------------------------------

class SocketRef : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.SocketRef) */ {
 public:
  SocketRef();
  virtual ~SocketRef();

  SocketRef(const SocketRef& from);

  inline SocketRef& operator=(const SocketRef& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SocketRef(SocketRef&& from) noexcept
    : SocketRef() {
    *this = ::std::move(from);
  }

  inline SocketRef& operator=(SocketRef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const SocketRef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SocketRef* internal_default_instance() {
    return reinterpret_cast<const SocketRef*>(
               &_SocketRef_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    8;

  void Swap(SocketRef* other);
  friend void swap(SocketRef& a, SocketRef& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SocketRef* New() const PROTOBUF_FINAL { return New(NULL); }

  SocketRef* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const SocketRef& from);
  void MergeFrom(const SocketRef& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(SocketRef* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string name = 4;
  void clear_name();
  static const int kNameFieldNumber = 4;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // int64 socket_id = 3;
  void clear_socket_id();
  static const int kSocketIdFieldNumber = 3;
  ::google::protobuf::int64 socket_id() const;
  void set_socket_id(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.SocketRef)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::int64 socket_id_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsSocketRefImpl();
};
// -------------------------------------------------------------------

class ServerRef : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.ServerRef) */ {
 public:
  ServerRef();
  virtual ~ServerRef();

  ServerRef(const ServerRef& from);

  inline ServerRef& operator=(const ServerRef& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ServerRef(ServerRef&& from) noexcept
    : ServerRef() {
    *this = ::std::move(from);
  }

  inline ServerRef& operator=(ServerRef&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ServerRef& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ServerRef* internal_default_instance() {
    return reinterpret_cast<const ServerRef*>(
               &_ServerRef_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    9;

  void Swap(ServerRef* other);
  friend void swap(ServerRef& a, ServerRef& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ServerRef* New() const PROTOBUF_FINAL { return New(NULL); }

  ServerRef* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ServerRef& from);
  void MergeFrom(const ServerRef& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ServerRef* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string name = 6;
  void clear_name();
  static const int kNameFieldNumber = 6;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // int64 server_id = 5;
  void clear_server_id();
  static const int kServerIdFieldNumber = 5;
  ::google::protobuf::int64 server_id() const;
  void set_server_id(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.ServerRef)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::int64 server_id_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsServerRefImpl();
};
// -------------------------------------------------------------------

class Server : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.Server) */ {
 public:
  Server();
  virtual ~Server();

  Server(const Server& from);

  inline Server& operator=(const Server& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Server(Server&& from) noexcept
    : Server() {
    *this = ::std::move(from);
  }

  inline Server& operator=(Server&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Server& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Server* internal_default_instance() {
    return reinterpret_cast<const Server*>(
               &_Server_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    10;

  void Swap(Server* other);
  friend void swap(Server& a, Server& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Server* New() const PROTOBUF_FINAL { return New(NULL); }

  Server* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const Server& from);
  void MergeFrom(const Server& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(Server* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .grpc.channelz.v1.SocketRef listen_socket = 3;
  int listen_socket_size() const;
  void clear_listen_socket();
  static const int kListenSocketFieldNumber = 3;
  const ::grpc::channelz::v1::SocketRef& listen_socket(int index) const;
  ::grpc::channelz::v1::SocketRef* mutable_listen_socket(int index);
  ::grpc::channelz::v1::SocketRef* add_listen_socket();
  ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SocketRef >*
      mutable_listen_socket();
  const ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SocketRef >&
      listen_socket() const;

  // .grpc.channelz.v1.ServerRef ref = 1;
  bool has_ref() const;
  void clear_ref();
  static const int kRefFieldNumber = 1;
  const ::grpc::channelz::v1::ServerRef& ref() const;
  ::grpc::channelz::v1::ServerRef* release_ref();
  ::grpc::channelz::v1::ServerRef* mutable_ref();
  void set_allocated_ref(::grpc::channelz::v1::ServerRef* ref);

  // .grpc.channelz.v1.ServerData data = 2;
  bool has_data() const;
  void clear_data();
  static const int kDataFieldNumber = 2;
  const ::grpc::channelz::v1::ServerData& data() const;
  ::grpc::channelz::v1::ServerData* release_data();
  ::grpc::channelz::v1::ServerData* mutable_data();
  void set_allocated_data(::grpc::channelz::v1::ServerData* data);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.Server)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SocketRef > listen_socket_;
  ::grpc::channelz::v1::ServerRef* ref_;
  ::grpc::channelz::v1::ServerData* data_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsServerImpl();
};
// -------------------------------------------------------------------

class ServerData : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.ServerData) */ {
 public:
  ServerData();
  virtual ~ServerData();

  ServerData(const ServerData& from);

  inline ServerData& operator=(const ServerData& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ServerData(ServerData&& from) noexcept
    : ServerData() {
    *this = ::std::move(from);
  }

  inline ServerData& operator=(ServerData&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ServerData& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ServerData* internal_default_instance() {
    return reinterpret_cast<const ServerData*>(
               &_ServerData_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    11;

  void Swap(ServerData* other);
  friend void swap(ServerData& a, ServerData& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ServerData* New() const PROTOBUF_FINAL { return New(NULL); }

  ServerData* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ServerData& from);
  void MergeFrom(const ServerData& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ServerData* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .grpc.channelz.v1.ChannelTrace trace = 1;
  bool has_trace() const;
  void clear_trace();
  static const int kTraceFieldNumber = 1;
  const ::grpc::channelz::v1::ChannelTrace& trace() const;
  ::grpc::channelz::v1::ChannelTrace* release_trace();
  ::grpc::channelz::v1::ChannelTrace* mutable_trace();
  void set_allocated_trace(::grpc::channelz::v1::ChannelTrace* trace);

  // .google.protobuf.Timestamp last_call_started_timestamp = 5;
  bool has_last_call_started_timestamp() const;
  void clear_last_call_started_timestamp();
  static const int kLastCallStartedTimestampFieldNumber = 5;
  const ::google::protobuf::Timestamp& last_call_started_timestamp() const;
  ::google::protobuf::Timestamp* release_last_call_started_timestamp();
  ::google::protobuf::Timestamp* mutable_last_call_started_timestamp();
  void set_allocated_last_call_started_timestamp(::google::protobuf::Timestamp* last_call_started_timestamp);

  // int64 calls_started = 2;
  void clear_calls_started();
  static const int kCallsStartedFieldNumber = 2;
  ::google::protobuf::int64 calls_started() const;
  void set_calls_started(::google::protobuf::int64 value);

  // int64 calls_succeeded = 3;
  void clear_calls_succeeded();
  static const int kCallsSucceededFieldNumber = 3;
  ::google::protobuf::int64 calls_succeeded() const;
  void set_calls_succeeded(::google::protobuf::int64 value);

  // int64 calls_failed = 4;
  void clear_calls_failed();
  static const int kCallsFailedFieldNumber = 4;
  ::google::protobuf::int64 calls_failed() const;
  void set_calls_failed(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.ServerData)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::grpc::channelz::v1::ChannelTrace* trace_;
  ::google::protobuf::Timestamp* last_call_started_timestamp_;
  ::google::protobuf::int64 calls_started_;
  ::google::protobuf::int64 calls_succeeded_;
  ::google::protobuf::int64 calls_failed_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsServerDataImpl();
};
// -------------------------------------------------------------------

class Socket : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.Socket) */ {
 public:
  Socket();
  virtual ~Socket();

  Socket(const Socket& from);

  inline Socket& operator=(const Socket& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Socket(Socket&& from) noexcept
    : Socket() {
    *this = ::std::move(from);
  }

  inline Socket& operator=(Socket&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Socket& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Socket* internal_default_instance() {
    return reinterpret_cast<const Socket*>(
               &_Socket_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    12;

  void Swap(Socket* other);
  friend void swap(Socket& a, Socket& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Socket* New() const PROTOBUF_FINAL { return New(NULL); }

  Socket* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const Socket& from);
  void MergeFrom(const Socket& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(Socket* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string remote_name = 6;
  void clear_remote_name();
  static const int kRemoteNameFieldNumber = 6;
  const ::std::string& remote_name() const;
  void set_remote_name(const ::std::string& value);
  #if LANG_CXX11
  void set_remote_name(::std::string&& value);
  #endif
  void set_remote_name(const char* value);
  void set_remote_name(const char* value, size_t size);
  ::std::string* mutable_remote_name();
  ::std::string* release_remote_name();
  void set_allocated_remote_name(::std::string* remote_name);

  // .grpc.channelz.v1.SocketRef ref = 1;
  bool has_ref() const;
  void clear_ref();
  static const int kRefFieldNumber = 1;
  const ::grpc::channelz::v1::SocketRef& ref() const;
  ::grpc::channelz::v1::SocketRef* release_ref();
  ::grpc::channelz::v1::SocketRef* mutable_ref();
  void set_allocated_ref(::grpc::channelz::v1::SocketRef* ref);

  // .grpc.channelz.v1.SocketData data = 2;
  bool has_data() const;
  void clear_data();
  static const int kDataFieldNumber = 2;
  const ::grpc::channelz::v1::SocketData& data() const;
  ::grpc::channelz::v1::SocketData* release_data();
  ::grpc::channelz::v1::SocketData* mutable_data();
  void set_allocated_data(::grpc::channelz::v1::SocketData* data);

  // .grpc.channelz.v1.Address local = 3;
  bool has_local() const;
  void clear_local();
  static const int kLocalFieldNumber = 3;
  const ::grpc::channelz::v1::Address& local() const;
  ::grpc::channelz::v1::Address* release_local();
  ::grpc::channelz::v1::Address* mutable_local();
  void set_allocated_local(::grpc::channelz::v1::Address* local);

  // .grpc.channelz.v1.Address remote = 4;
  bool has_remote() const;
  void clear_remote();
  static const int kRemoteFieldNumber = 4;
  const ::grpc::channelz::v1::Address& remote() const;
  ::grpc::channelz::v1::Address* release_remote();
  ::grpc::channelz::v1::Address* mutable_remote();
  void set_allocated_remote(::grpc::channelz::v1::Address* remote);

  // .grpc.channelz.v1.Security security = 5;
  bool has_security() const;
  void clear_security();
  static const int kSecurityFieldNumber = 5;
  const ::grpc::channelz::v1::Security& security() const;
  ::grpc::channelz::v1::Security* release_security();
  ::grpc::channelz::v1::Security* mutable_security();
  void set_allocated_security(::grpc::channelz::v1::Security* security);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.Socket)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr remote_name_;
  ::grpc::channelz::v1::SocketRef* ref_;
  ::grpc::channelz::v1::SocketData* data_;
  ::grpc::channelz::v1::Address* local_;
  ::grpc::channelz::v1::Address* remote_;
  ::grpc::channelz::v1::Security* security_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsSocketImpl();
};
// -------------------------------------------------------------------

class SocketData : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.SocketData) */ {
 public:
  SocketData();
  virtual ~SocketData();

  SocketData(const SocketData& from);

  inline SocketData& operator=(const SocketData& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SocketData(SocketData&& from) noexcept
    : SocketData() {
    *this = ::std::move(from);
  }

  inline SocketData& operator=(SocketData&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const SocketData& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SocketData* internal_default_instance() {
    return reinterpret_cast<const SocketData*>(
               &_SocketData_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    13;

  void Swap(SocketData* other);
  friend void swap(SocketData& a, SocketData& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SocketData* New() const PROTOBUF_FINAL { return New(NULL); }

  SocketData* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const SocketData& from);
  void MergeFrom(const SocketData& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(SocketData* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .grpc.channelz.v1.SocketOption option = 13;
  int option_size() const;
  void clear_option();
  static const int kOptionFieldNumber = 13;
  const ::grpc::channelz::v1::SocketOption& option(int index) const;
  ::grpc::channelz::v1::SocketOption* mutable_option(int index);
  ::grpc::channelz::v1::SocketOption* add_option();
  ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SocketOption >*
      mutable_option();
  const ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SocketOption >&
      option() const;

  // .google.protobuf.Timestamp last_local_stream_created_timestamp = 7;
  bool has_last_local_stream_created_timestamp() const;
  void clear_last_local_stream_created_timestamp();
  static const int kLastLocalStreamCreatedTimestampFieldNumber = 7;
  const ::google::protobuf::Timestamp& last_local_stream_created_timestamp() const;
  ::google::protobuf::Timestamp* release_last_local_stream_created_timestamp();
  ::google::protobuf::Timestamp* mutable_last_local_stream_created_timestamp();
  void set_allocated_last_local_stream_created_timestamp(::google::protobuf::Timestamp* last_local_stream_created_timestamp);

  // .google.protobuf.Timestamp last_remote_stream_created_timestamp = 8;
  bool has_last_remote_stream_created_timestamp() const;
  void clear_last_remote_stream_created_timestamp();
  static const int kLastRemoteStreamCreatedTimestampFieldNumber = 8;
  const ::google::protobuf::Timestamp& last_remote_stream_created_timestamp() const;
  ::google::protobuf::Timestamp* release_last_remote_stream_created_timestamp();
  ::google::protobuf::Timestamp* mutable_last_remote_stream_created_timestamp();
  void set_allocated_last_remote_stream_created_timestamp(::google::protobuf::Timestamp* last_remote_stream_created_timestamp);

  // .google.protobuf.Timestamp last_message_sent_timestamp = 9;
  bool has_last_message_sent_timestamp() const;
  void clear_last_message_sent_timestamp();
  static const int kLastMessageSentTimestampFieldNumber = 9;
  const ::google::protobuf::Timestamp& last_message_sent_timestamp() const;
  ::google::protobuf::Timestamp* release_last_message_sent_timestamp();
  ::google::protobuf::Timestamp* mutable_last_message_sent_timestamp();
  void set_allocated_last_message_sent_timestamp(::google::protobuf::Timestamp* last_message_sent_timestamp);

  // .google.protobuf.Timestamp last_message_received_timestamp = 10;
  bool has_last_message_received_timestamp() const;
  void clear_last_message_received_timestamp();
  static const int kLastMessageReceivedTimestampFieldNumber = 10;
  const ::google::protobuf::Timestamp& last_message_received_timestamp() const;
  ::google::protobuf::Timestamp* release_last_message_received_timestamp();
  ::google::protobuf::Timestamp* mutable_last_message_received_timestamp();
  void set_allocated_last_message_received_timestamp(::google::protobuf::Timestamp* last_message_received_timestamp);

  // .google.protobuf.Int64Value local_flow_control_window = 11;
  bool has_local_flow_control_window() const;
  void clear_local_flow_control_window();
  static const int kLocalFlowControlWindowFieldNumber = 11;
  const ::google::protobuf::Int64Value& local_flow_control_window() const;
  ::google::protobuf::Int64Value* release_local_flow_control_window();
  ::google::protobuf::Int64Value* mutable_local_flow_control_window();
  void set_allocated_local_flow_control_window(::google::protobuf::Int64Value* local_flow_control_window);

  // .google.protobuf.Int64Value remote_flow_control_window = 12;
  bool has_remote_flow_control_window() const;
  void clear_remote_flow_control_window();
  static const int kRemoteFlowControlWindowFieldNumber = 12;
  const ::google::protobuf::Int64Value& remote_flow_control_window() const;
  ::google::protobuf::Int64Value* release_remote_flow_control_window();
  ::google::protobuf::Int64Value* mutable_remote_flow_control_window();
  void set_allocated_remote_flow_control_window(::google::protobuf::Int64Value* remote_flow_control_window);

  // int64 streams_started = 1;
  void clear_streams_started();
  static const int kStreamsStartedFieldNumber = 1;
  ::google::protobuf::int64 streams_started() const;
  void set_streams_started(::google::protobuf::int64 value);

  // int64 streams_succeeded = 2;
  void clear_streams_succeeded();
  static const int kStreamsSucceededFieldNumber = 2;
  ::google::protobuf::int64 streams_succeeded() const;
  void set_streams_succeeded(::google::protobuf::int64 value);

  // int64 streams_failed = 3;
  void clear_streams_failed();
  static const int kStreamsFailedFieldNumber = 3;
  ::google::protobuf::int64 streams_failed() const;
  void set_streams_failed(::google::protobuf::int64 value);

  // int64 messages_sent = 4;
  void clear_messages_sent();
  static const int kMessagesSentFieldNumber = 4;
  ::google::protobuf::int64 messages_sent() const;
  void set_messages_sent(::google::protobuf::int64 value);

  // int64 messages_received = 5;
  void clear_messages_received();
  static const int kMessagesReceivedFieldNumber = 5;
  ::google::protobuf::int64 messages_received() const;
  void set_messages_received(::google::protobuf::int64 value);

  // int64 keep_alives_sent = 6;
  void clear_keep_alives_sent();
  static const int kKeepAlivesSentFieldNumber = 6;
  ::google::protobuf::int64 keep_alives_sent() const;
  void set_keep_alives_sent(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.SocketData)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SocketOption > option_;
  ::google::protobuf::Timestamp* last_local_stream_created_timestamp_;
  ::google::protobuf::Timestamp* last_remote_stream_created_timestamp_;
  ::google::protobuf::Timestamp* last_message_sent_timestamp_;
  ::google::protobuf::Timestamp* last_message_received_timestamp_;
  ::google::protobuf::Int64Value* local_flow_control_window_;
  ::google::protobuf::Int64Value* remote_flow_control_window_;
  ::google::protobuf::int64 streams_started_;
  ::google::protobuf::int64 streams_succeeded_;
  ::google::protobuf::int64 streams_failed_;
  ::google::protobuf::int64 messages_sent_;
  ::google::protobuf::int64 messages_received_;
  ::google::protobuf::int64 keep_alives_sent_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsSocketDataImpl();
};
// -------------------------------------------------------------------

class Address_TcpIpAddress : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.Address.TcpIpAddress) */ {
 public:
  Address_TcpIpAddress();
  virtual ~Address_TcpIpAddress();

  Address_TcpIpAddress(const Address_TcpIpAddress& from);

  inline Address_TcpIpAddress& operator=(const Address_TcpIpAddress& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Address_TcpIpAddress(Address_TcpIpAddress&& from) noexcept
    : Address_TcpIpAddress() {
    *this = ::std::move(from);
  }

  inline Address_TcpIpAddress& operator=(Address_TcpIpAddress&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Address_TcpIpAddress& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Address_TcpIpAddress* internal_default_instance() {
    return reinterpret_cast<const Address_TcpIpAddress*>(
               &_Address_TcpIpAddress_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    14;

  void Swap(Address_TcpIpAddress* other);
  friend void swap(Address_TcpIpAddress& a, Address_TcpIpAddress& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Address_TcpIpAddress* New() const PROTOBUF_FINAL { return New(NULL); }

  Address_TcpIpAddress* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const Address_TcpIpAddress& from);
  void MergeFrom(const Address_TcpIpAddress& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(Address_TcpIpAddress* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // bytes ip_address = 1;
  void clear_ip_address();
  static const int kIpAddressFieldNumber = 1;
  const ::std::string& ip_address() const;
  void set_ip_address(const ::std::string& value);
  #if LANG_CXX11
  void set_ip_address(::std::string&& value);
  #endif
  void set_ip_address(const char* value);
  void set_ip_address(const void* value, size_t size);
  ::std::string* mutable_ip_address();
  ::std::string* release_ip_address();
  void set_allocated_ip_address(::std::string* ip_address);

  // int32 port = 2;
  void clear_port();
  static const int kPortFieldNumber = 2;
  ::google::protobuf::int32 port() const;
  void set_port(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.Address.TcpIpAddress)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr ip_address_;
  ::google::protobuf::int32 port_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsAddress_TcpIpAddressImpl();
};
// -------------------------------------------------------------------

class Address_UdsAddress : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.Address.UdsAddress) */ {
 public:
  Address_UdsAddress();
  virtual ~Address_UdsAddress();

  Address_UdsAddress(const Address_UdsAddress& from);

  inline Address_UdsAddress& operator=(const Address_UdsAddress& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Address_UdsAddress(Address_UdsAddress&& from) noexcept
    : Address_UdsAddress() {
    *this = ::std::move(from);
  }

  inline Address_UdsAddress& operator=(Address_UdsAddress&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Address_UdsAddress& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Address_UdsAddress* internal_default_instance() {
    return reinterpret_cast<const Address_UdsAddress*>(
               &_Address_UdsAddress_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    15;

  void Swap(Address_UdsAddress* other);
  friend void swap(Address_UdsAddress& a, Address_UdsAddress& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Address_UdsAddress* New() const PROTOBUF_FINAL { return New(NULL); }

  Address_UdsAddress* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const Address_UdsAddress& from);
  void MergeFrom(const Address_UdsAddress& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(Address_UdsAddress* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string filename = 1;
  void clear_filename();
  static const int kFilenameFieldNumber = 1;
  const ::std::string& filename() const;
  void set_filename(const ::std::string& value);
  #if LANG_CXX11
  void set_filename(::std::string&& value);
  #endif
  void set_filename(const char* value);
  void set_filename(const char* value, size_t size);
  ::std::string* mutable_filename();
  ::std::string* release_filename();
  void set_allocated_filename(::std::string* filename);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.Address.UdsAddress)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr filename_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsAddress_UdsAddressImpl();
};
// -------------------------------------------------------------------

class Address_OtherAddress : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.Address.OtherAddress) */ {
 public:
  Address_OtherAddress();
  virtual ~Address_OtherAddress();

  Address_OtherAddress(const Address_OtherAddress& from);

  inline Address_OtherAddress& operator=(const Address_OtherAddress& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Address_OtherAddress(Address_OtherAddress&& from) noexcept
    : Address_OtherAddress() {
    *this = ::std::move(from);
  }

  inline Address_OtherAddress& operator=(Address_OtherAddress&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Address_OtherAddress& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Address_OtherAddress* internal_default_instance() {
    return reinterpret_cast<const Address_OtherAddress*>(
               &_Address_OtherAddress_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    16;

  void Swap(Address_OtherAddress* other);
  friend void swap(Address_OtherAddress& a, Address_OtherAddress& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Address_OtherAddress* New() const PROTOBUF_FINAL { return New(NULL); }

  Address_OtherAddress* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const Address_OtherAddress& from);
  void MergeFrom(const Address_OtherAddress& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(Address_OtherAddress* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // .google.protobuf.Any value = 2;
  bool has_value() const;
  void clear_value();
  static const int kValueFieldNumber = 2;
  const ::google::protobuf::Any& value() const;
  ::google::protobuf::Any* release_value();
  ::google::protobuf::Any* mutable_value();
  void set_allocated_value(::google::protobuf::Any* value);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.Address.OtherAddress)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::Any* value_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsAddress_OtherAddressImpl();
};
// -------------------------------------------------------------------

class Address : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.Address) */ {
 public:
  Address();
  virtual ~Address();

  Address(const Address& from);

  inline Address& operator=(const Address& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Address(Address&& from) noexcept
    : Address() {
    *this = ::std::move(from);
  }

  inline Address& operator=(Address&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Address& default_instance();

  enum AddressCase {
    kTcpipAddress = 1,
    kUdsAddress = 2,
    kOtherAddress = 3,
    ADDRESS_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Address* internal_default_instance() {
    return reinterpret_cast<const Address*>(
               &_Address_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    17;

  void Swap(Address* other);
  friend void swap(Address& a, Address& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Address* New() const PROTOBUF_FINAL { return New(NULL); }

  Address* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const Address& from);
  void MergeFrom(const Address& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(Address* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  typedef Address_TcpIpAddress TcpIpAddress;
  typedef Address_UdsAddress UdsAddress;
  typedef Address_OtherAddress OtherAddress;

  // accessors -------------------------------------------------------

  // .grpc.channelz.v1.Address.TcpIpAddress tcpip_address = 1;
  bool has_tcpip_address() const;
  void clear_tcpip_address();
  static const int kTcpipAddressFieldNumber = 1;
  const ::grpc::channelz::v1::Address_TcpIpAddress& tcpip_address() const;
  ::grpc::channelz::v1::Address_TcpIpAddress* release_tcpip_address();
  ::grpc::channelz::v1::Address_TcpIpAddress* mutable_tcpip_address();
  void set_allocated_tcpip_address(::grpc::channelz::v1::Address_TcpIpAddress* tcpip_address);

  // .grpc.channelz.v1.Address.UdsAddress uds_address = 2;
  bool has_uds_address() const;
  void clear_uds_address();
  static const int kUdsAddressFieldNumber = 2;
  const ::grpc::channelz::v1::Address_UdsAddress& uds_address() const;
  ::grpc::channelz::v1::Address_UdsAddress* release_uds_address();
  ::grpc::channelz::v1::Address_UdsAddress* mutable_uds_address();
  void set_allocated_uds_address(::grpc::channelz::v1::Address_UdsAddress* uds_address);

  // .grpc.channelz.v1.Address.OtherAddress other_address = 3;
  bool has_other_address() const;
  void clear_other_address();
  static const int kOtherAddressFieldNumber = 3;
  const ::grpc::channelz::v1::Address_OtherAddress& other_address() const;
  ::grpc::channelz::v1::Address_OtherAddress* release_other_address();
  ::grpc::channelz::v1::Address_OtherAddress* mutable_other_address();
  void set_allocated_other_address(::grpc::channelz::v1::Address_OtherAddress* other_address);

  AddressCase address_case() const;
  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.Address)
 private:
  void set_has_tcpip_address();
  void set_has_uds_address();
  void set_has_other_address();

  inline bool has_address() const;
  void clear_address();
  inline void clear_has_address();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  union AddressUnion {
    AddressUnion() {}
    ::grpc::channelz::v1::Address_TcpIpAddress* tcpip_address_;
    ::grpc::channelz::v1::Address_UdsAddress* uds_address_;
    ::grpc::channelz::v1::Address_OtherAddress* other_address_;
  } address_;
  mutable int _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsAddressImpl();
};
// -------------------------------------------------------------------

class Security_Tls : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.Security.Tls) */ {
 public:
  Security_Tls();
  virtual ~Security_Tls();

  Security_Tls(const Security_Tls& from);

  inline Security_Tls& operator=(const Security_Tls& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Security_Tls(Security_Tls&& from) noexcept
    : Security_Tls() {
    *this = ::std::move(from);
  }

  inline Security_Tls& operator=(Security_Tls&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Security_Tls& default_instance();

  enum CipherSuiteCase {
    kStandardName = 1,
    kOtherName = 2,
    CIPHER_SUITE_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Security_Tls* internal_default_instance() {
    return reinterpret_cast<const Security_Tls*>(
               &_Security_Tls_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    18;

  void Swap(Security_Tls* other);
  friend void swap(Security_Tls& a, Security_Tls& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Security_Tls* New() const PROTOBUF_FINAL { return New(NULL); }

  Security_Tls* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const Security_Tls& from);
  void MergeFrom(const Security_Tls& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(Security_Tls* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // bytes local_certificate = 3;
  void clear_local_certificate();
  static const int kLocalCertificateFieldNumber = 3;
  const ::std::string& local_certificate() const;
  void set_local_certificate(const ::std::string& value);
  #if LANG_CXX11
  void set_local_certificate(::std::string&& value);
  #endif
  void set_local_certificate(const char* value);
  void set_local_certificate(const void* value, size_t size);
  ::std::string* mutable_local_certificate();
  ::std::string* release_local_certificate();
  void set_allocated_local_certificate(::std::string* local_certificate);

  // bytes remote_certificate = 4;
  void clear_remote_certificate();
  static const int kRemoteCertificateFieldNumber = 4;
  const ::std::string& remote_certificate() const;
  void set_remote_certificate(const ::std::string& value);
  #if LANG_CXX11
  void set_remote_certificate(::std::string&& value);
  #endif
  void set_remote_certificate(const char* value);
  void set_remote_certificate(const void* value, size_t size);
  ::std::string* mutable_remote_certificate();
  ::std::string* release_remote_certificate();
  void set_allocated_remote_certificate(::std::string* remote_certificate);

  // string standard_name = 1;
  private:
  bool has_standard_name() const;
  public:
  void clear_standard_name();
  static const int kStandardNameFieldNumber = 1;
  const ::std::string& standard_name() const;
  void set_standard_name(const ::std::string& value);
  #if LANG_CXX11
  void set_standard_name(::std::string&& value);
  #endif
  void set_standard_name(const char* value);
  void set_standard_name(const char* value, size_t size);
  ::std::string* mutable_standard_name();
  ::std::string* release_standard_name();
  void set_allocated_standard_name(::std::string* standard_name);

  // string other_name = 2;
  private:
  bool has_other_name() const;
  public:
  void clear_other_name();
  static const int kOtherNameFieldNumber = 2;
  const ::std::string& other_name() const;
  void set_other_name(const ::std::string& value);
  #if LANG_CXX11
  void set_other_name(::std::string&& value);
  #endif
  void set_other_name(const char* value);
  void set_other_name(const char* value, size_t size);
  ::std::string* mutable_other_name();
  ::std::string* release_other_name();
  void set_allocated_other_name(::std::string* other_name);

  CipherSuiteCase cipher_suite_case() const;
  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.Security.Tls)
 private:
  void set_has_standard_name();
  void set_has_other_name();

  inline bool has_cipher_suite() const;
  void clear_cipher_suite();
  inline void clear_has_cipher_suite();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr local_certificate_;
  ::google::protobuf::internal::ArenaStringPtr remote_certificate_;
  union CipherSuiteUnion {
    CipherSuiteUnion() {}
    ::google::protobuf::internal::ArenaStringPtr standard_name_;
    ::google::protobuf::internal::ArenaStringPtr other_name_;
  } cipher_suite_;
  mutable int _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsSecurity_TlsImpl();
};
// -------------------------------------------------------------------

class Security_OtherSecurity : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.Security.OtherSecurity) */ {
 public:
  Security_OtherSecurity();
  virtual ~Security_OtherSecurity();

  Security_OtherSecurity(const Security_OtherSecurity& from);

  inline Security_OtherSecurity& operator=(const Security_OtherSecurity& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Security_OtherSecurity(Security_OtherSecurity&& from) noexcept
    : Security_OtherSecurity() {
    *this = ::std::move(from);
  }

  inline Security_OtherSecurity& operator=(Security_OtherSecurity&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Security_OtherSecurity& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Security_OtherSecurity* internal_default_instance() {
    return reinterpret_cast<const Security_OtherSecurity*>(
               &_Security_OtherSecurity_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    19;

  void Swap(Security_OtherSecurity* other);
  friend void swap(Security_OtherSecurity& a, Security_OtherSecurity& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Security_OtherSecurity* New() const PROTOBUF_FINAL { return New(NULL); }

  Security_OtherSecurity* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const Security_OtherSecurity& from);
  void MergeFrom(const Security_OtherSecurity& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(Security_OtherSecurity* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // .google.protobuf.Any value = 2;
  bool has_value() const;
  void clear_value();
  static const int kValueFieldNumber = 2;
  const ::google::protobuf::Any& value() const;
  ::google::protobuf::Any* release_value();
  ::google::protobuf::Any* mutable_value();
  void set_allocated_value(::google::protobuf::Any* value);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.Security.OtherSecurity)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::Any* value_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsSecurity_OtherSecurityImpl();
};
// -------------------------------------------------------------------

class Security : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.Security) */ {
 public:
  Security();
  virtual ~Security();

  Security(const Security& from);

  inline Security& operator=(const Security& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Security(Security&& from) noexcept
    : Security() {
    *this = ::std::move(from);
  }

  inline Security& operator=(Security&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Security& default_instance();

  enum ModelCase {
    kTls = 1,
    kOther = 2,
    MODEL_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Security* internal_default_instance() {
    return reinterpret_cast<const Security*>(
               &_Security_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    20;

  void Swap(Security* other);
  friend void swap(Security& a, Security& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Security* New() const PROTOBUF_FINAL { return New(NULL); }

  Security* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const Security& from);
  void MergeFrom(const Security& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(Security* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  typedef Security_Tls Tls;
  typedef Security_OtherSecurity OtherSecurity;

  // accessors -------------------------------------------------------

  // .grpc.channelz.v1.Security.Tls tls = 1;
  bool has_tls() const;
  void clear_tls();
  static const int kTlsFieldNumber = 1;
  const ::grpc::channelz::v1::Security_Tls& tls() const;
  ::grpc::channelz::v1::Security_Tls* release_tls();
  ::grpc::channelz::v1::Security_Tls* mutable_tls();
  void set_allocated_tls(::grpc::channelz::v1::Security_Tls* tls);

  // .grpc.channelz.v1.Security.OtherSecurity other = 2;
  bool has_other() const;
  void clear_other();
  static const int kOtherFieldNumber = 2;
  const ::grpc::channelz::v1::Security_OtherSecurity& other() const;
  ::grpc::channelz::v1::Security_OtherSecurity* release_other();
  ::grpc::channelz::v1::Security_OtherSecurity* mutable_other();
  void set_allocated_other(::grpc::channelz::v1::Security_OtherSecurity* other);

  ModelCase model_case() const;
  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.Security)
 private:
  void set_has_tls();
  void set_has_other();

  inline bool has_model() const;
  void clear_model();
  inline void clear_has_model();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  union ModelUnion {
    ModelUnion() {}
    ::grpc::channelz::v1::Security_Tls* tls_;
    ::grpc::channelz::v1::Security_OtherSecurity* other_;
  } model_;
  mutable int _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsSecurityImpl();
};
// -------------------------------------------------------------------

class SocketOption : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.SocketOption) */ {
 public:
  SocketOption();
  virtual ~SocketOption();

  SocketOption(const SocketOption& from);

  inline SocketOption& operator=(const SocketOption& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SocketOption(SocketOption&& from) noexcept
    : SocketOption() {
    *this = ::std::move(from);
  }

  inline SocketOption& operator=(SocketOption&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const SocketOption& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SocketOption* internal_default_instance() {
    return reinterpret_cast<const SocketOption*>(
               &_SocketOption_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    21;

  void Swap(SocketOption* other);
  friend void swap(SocketOption& a, SocketOption& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SocketOption* New() const PROTOBUF_FINAL { return New(NULL); }

  SocketOption* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const SocketOption& from);
  void MergeFrom(const SocketOption& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(SocketOption* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // string value = 2;
  void clear_value();
  static const int kValueFieldNumber = 2;
  const ::std::string& value() const;
  void set_value(const ::std::string& value);
  #if LANG_CXX11
  void set_value(::std::string&& value);
  #endif
  void set_value(const char* value);
  void set_value(const char* value, size_t size);
  ::std::string* mutable_value();
  ::std::string* release_value();
  void set_allocated_value(::std::string* value);

  // .google.protobuf.Any additional = 3;
  bool has_additional() const;
  void clear_additional();
  static const int kAdditionalFieldNumber = 3;
  const ::google::protobuf::Any& additional() const;
  ::google::protobuf::Any* release_additional();
  ::google::protobuf::Any* mutable_additional();
  void set_allocated_additional(::google::protobuf::Any* additional);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.SocketOption)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::google::protobuf::internal::ArenaStringPtr value_;
  ::google::protobuf::Any* additional_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsSocketOptionImpl();
};
// -------------------------------------------------------------------

class SocketOptionTimeout : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.SocketOptionTimeout) */ {
 public:
  SocketOptionTimeout();
  virtual ~SocketOptionTimeout();

  SocketOptionTimeout(const SocketOptionTimeout& from);

  inline SocketOptionTimeout& operator=(const SocketOptionTimeout& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SocketOptionTimeout(SocketOptionTimeout&& from) noexcept
    : SocketOptionTimeout() {
    *this = ::std::move(from);
  }

  inline SocketOptionTimeout& operator=(SocketOptionTimeout&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const SocketOptionTimeout& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SocketOptionTimeout* internal_default_instance() {
    return reinterpret_cast<const SocketOptionTimeout*>(
               &_SocketOptionTimeout_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    22;

  void Swap(SocketOptionTimeout* other);
  friend void swap(SocketOptionTimeout& a, SocketOptionTimeout& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SocketOptionTimeout* New() const PROTOBUF_FINAL { return New(NULL); }

  SocketOptionTimeout* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const SocketOptionTimeout& from);
  void MergeFrom(const SocketOptionTimeout& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(SocketOptionTimeout* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .google.protobuf.Duration duration = 1;
  bool has_duration() const;
  void clear_duration();
  static const int kDurationFieldNumber = 1;
  const ::google::protobuf::Duration& duration() const;
  ::google::protobuf::Duration* release_duration();
  ::google::protobuf::Duration* mutable_duration();
  void set_allocated_duration(::google::protobuf::Duration* duration);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.SocketOptionTimeout)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::Duration* duration_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsSocketOptionTimeoutImpl();
};
// -------------------------------------------------------------------

class SocketOptionLinger : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.SocketOptionLinger) */ {
 public:
  SocketOptionLinger();
  virtual ~SocketOptionLinger();

  SocketOptionLinger(const SocketOptionLinger& from);

  inline SocketOptionLinger& operator=(const SocketOptionLinger& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SocketOptionLinger(SocketOptionLinger&& from) noexcept
    : SocketOptionLinger() {
    *this = ::std::move(from);
  }

  inline SocketOptionLinger& operator=(SocketOptionLinger&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const SocketOptionLinger& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SocketOptionLinger* internal_default_instance() {
    return reinterpret_cast<const SocketOptionLinger*>(
               &_SocketOptionLinger_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    23;

  void Swap(SocketOptionLinger* other);
  friend void swap(SocketOptionLinger& a, SocketOptionLinger& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SocketOptionLinger* New() const PROTOBUF_FINAL { return New(NULL); }

  SocketOptionLinger* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const SocketOptionLinger& from);
  void MergeFrom(const SocketOptionLinger& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(SocketOptionLinger* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .google.protobuf.Duration duration = 2;
  bool has_duration() const;
  void clear_duration();
  static const int kDurationFieldNumber = 2;
  const ::google::protobuf::Duration& duration() const;
  ::google::protobuf::Duration* release_duration();
  ::google::protobuf::Duration* mutable_duration();
  void set_allocated_duration(::google::protobuf::Duration* duration);

  // bool active = 1;
  void clear_active();
  static const int kActiveFieldNumber = 1;
  bool active() const;
  void set_active(bool value);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.SocketOptionLinger)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::Duration* duration_;
  bool active_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsSocketOptionLingerImpl();
};
// -------------------------------------------------------------------

class SocketOptionTcpInfo : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.SocketOptionTcpInfo) */ {
 public:
  SocketOptionTcpInfo();
  virtual ~SocketOptionTcpInfo();

  SocketOptionTcpInfo(const SocketOptionTcpInfo& from);

  inline SocketOptionTcpInfo& operator=(const SocketOptionTcpInfo& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SocketOptionTcpInfo(SocketOptionTcpInfo&& from) noexcept
    : SocketOptionTcpInfo() {
    *this = ::std::move(from);
  }

  inline SocketOptionTcpInfo& operator=(SocketOptionTcpInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const SocketOptionTcpInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SocketOptionTcpInfo* internal_default_instance() {
    return reinterpret_cast<const SocketOptionTcpInfo*>(
               &_SocketOptionTcpInfo_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    24;

  void Swap(SocketOptionTcpInfo* other);
  friend void swap(SocketOptionTcpInfo& a, SocketOptionTcpInfo& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SocketOptionTcpInfo* New() const PROTOBUF_FINAL { return New(NULL); }

  SocketOptionTcpInfo* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const SocketOptionTcpInfo& from);
  void MergeFrom(const SocketOptionTcpInfo& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(SocketOptionTcpInfo* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // uint32 tcpi_state = 1;
  void clear_tcpi_state();
  static const int kTcpiStateFieldNumber = 1;
  ::google::protobuf::uint32 tcpi_state() const;
  void set_tcpi_state(::google::protobuf::uint32 value);

  // uint32 tcpi_ca_state = 2;
  void clear_tcpi_ca_state();
  static const int kTcpiCaStateFieldNumber = 2;
  ::google::protobuf::uint32 tcpi_ca_state() const;
  void set_tcpi_ca_state(::google::protobuf::uint32 value);

  // uint32 tcpi_retransmits = 3;
  void clear_tcpi_retransmits();
  static const int kTcpiRetransmitsFieldNumber = 3;
  ::google::protobuf::uint32 tcpi_retransmits() const;
  void set_tcpi_retransmits(::google::protobuf::uint32 value);

  // uint32 tcpi_probes = 4;
  void clear_tcpi_probes();
  static const int kTcpiProbesFieldNumber = 4;
  ::google::protobuf::uint32 tcpi_probes() const;
  void set_tcpi_probes(::google::protobuf::uint32 value);

  // uint32 tcpi_backoff = 5;
  void clear_tcpi_backoff();
  static const int kTcpiBackoffFieldNumber = 5;
  ::google::protobuf::uint32 tcpi_backoff() const;
  void set_tcpi_backoff(::google::protobuf::uint32 value);

  // uint32 tcpi_options = 6;
  void clear_tcpi_options();
  static const int kTcpiOptionsFieldNumber = 6;
  ::google::protobuf::uint32 tcpi_options() const;
  void set_tcpi_options(::google::protobuf::uint32 value);

  // uint32 tcpi_snd_wscale = 7;
  void clear_tcpi_snd_wscale();
  static const int kTcpiSndWscaleFieldNumber = 7;
  ::google::protobuf::uint32 tcpi_snd_wscale() const;
  void set_tcpi_snd_wscale(::google::protobuf::uint32 value);

  // uint32 tcpi_rcv_wscale = 8;
  void clear_tcpi_rcv_wscale();
  static const int kTcpiRcvWscaleFieldNumber = 8;
  ::google::protobuf::uint32 tcpi_rcv_wscale() const;
  void set_tcpi_rcv_wscale(::google::protobuf::uint32 value);

  // uint32 tcpi_rto = 9;
  void clear_tcpi_rto();
  static const int kTcpiRtoFieldNumber = 9;
  ::google::protobuf::uint32 tcpi_rto() const;
  void set_tcpi_rto(::google::protobuf::uint32 value);

  // uint32 tcpi_ato = 10;
  void clear_tcpi_ato();
  static const int kTcpiAtoFieldNumber = 10;
  ::google::protobuf::uint32 tcpi_ato() const;
  void set_tcpi_ato(::google::protobuf::uint32 value);

  // uint32 tcpi_snd_mss = 11;
  void clear_tcpi_snd_mss();
  static const int kTcpiSndMssFieldNumber = 11;
  ::google::protobuf::uint32 tcpi_snd_mss() const;
  void set_tcpi_snd_mss(::google::protobuf::uint32 value);

  // uint32 tcpi_rcv_mss = 12;
  void clear_tcpi_rcv_mss();
  static const int kTcpiRcvMssFieldNumber = 12;
  ::google::protobuf::uint32 tcpi_rcv_mss() const;
  void set_tcpi_rcv_mss(::google::protobuf::uint32 value);

  // uint32 tcpi_unacked = 13;
  void clear_tcpi_unacked();
  static const int kTcpiUnackedFieldNumber = 13;
  ::google::protobuf::uint32 tcpi_unacked() const;
  void set_tcpi_unacked(::google::protobuf::uint32 value);

  // uint32 tcpi_sacked = 14;
  void clear_tcpi_sacked();
  static const int kTcpiSackedFieldNumber = 14;
  ::google::protobuf::uint32 tcpi_sacked() const;
  void set_tcpi_sacked(::google::protobuf::uint32 value);

  // uint32 tcpi_lost = 15;
  void clear_tcpi_lost();
  static const int kTcpiLostFieldNumber = 15;
  ::google::protobuf::uint32 tcpi_lost() const;
  void set_tcpi_lost(::google::protobuf::uint32 value);

  // uint32 tcpi_retrans = 16;
  void clear_tcpi_retrans();
  static const int kTcpiRetransFieldNumber = 16;
  ::google::protobuf::uint32 tcpi_retrans() const;
  void set_tcpi_retrans(::google::protobuf::uint32 value);

  // uint32 tcpi_fackets = 17;
  void clear_tcpi_fackets();
  static const int kTcpiFacketsFieldNumber = 17;
  ::google::protobuf::uint32 tcpi_fackets() const;
  void set_tcpi_fackets(::google::protobuf::uint32 value);

  // uint32 tcpi_last_data_sent = 18;
  void clear_tcpi_last_data_sent();
  static const int kTcpiLastDataSentFieldNumber = 18;
  ::google::protobuf::uint32 tcpi_last_data_sent() const;
  void set_tcpi_last_data_sent(::google::protobuf::uint32 value);

  // uint32 tcpi_last_ack_sent = 19;
  void clear_tcpi_last_ack_sent();
  static const int kTcpiLastAckSentFieldNumber = 19;
  ::google::protobuf::uint32 tcpi_last_ack_sent() const;
  void set_tcpi_last_ack_sent(::google::protobuf::uint32 value);

  // uint32 tcpi_last_data_recv = 20;
  void clear_tcpi_last_data_recv();
  static const int kTcpiLastDataRecvFieldNumber = 20;
  ::google::protobuf::uint32 tcpi_last_data_recv() const;
  void set_tcpi_last_data_recv(::google::protobuf::uint32 value);

  // uint32 tcpi_last_ack_recv = 21;
  void clear_tcpi_last_ack_recv();
  static const int kTcpiLastAckRecvFieldNumber = 21;
  ::google::protobuf::uint32 tcpi_last_ack_recv() const;
  void set_tcpi_last_ack_recv(::google::protobuf::uint32 value);

  // uint32 tcpi_pmtu = 22;
  void clear_tcpi_pmtu();
  static const int kTcpiPmtuFieldNumber = 22;
  ::google::protobuf::uint32 tcpi_pmtu() const;
  void set_tcpi_pmtu(::google::protobuf::uint32 value);

  // uint32 tcpi_rcv_ssthresh = 23;
  void clear_tcpi_rcv_ssthresh();
  static const int kTcpiRcvSsthreshFieldNumber = 23;
  ::google::protobuf::uint32 tcpi_rcv_ssthresh() const;
  void set_tcpi_rcv_ssthresh(::google::protobuf::uint32 value);

  // uint32 tcpi_rtt = 24;
  void clear_tcpi_rtt();
  static const int kTcpiRttFieldNumber = 24;
  ::google::protobuf::uint32 tcpi_rtt() const;
  void set_tcpi_rtt(::google::protobuf::uint32 value);

  // uint32 tcpi_rttvar = 25;
  void clear_tcpi_rttvar();
  static const int kTcpiRttvarFieldNumber = 25;
  ::google::protobuf::uint32 tcpi_rttvar() const;
  void set_tcpi_rttvar(::google::protobuf::uint32 value);

  // uint32 tcpi_snd_ssthresh = 26;
  void clear_tcpi_snd_ssthresh();
  static const int kTcpiSndSsthreshFieldNumber = 26;
  ::google::protobuf::uint32 tcpi_snd_ssthresh() const;
  void set_tcpi_snd_ssthresh(::google::protobuf::uint32 value);

  // uint32 tcpi_snd_cwnd = 27;
  void clear_tcpi_snd_cwnd();
  static const int kTcpiSndCwndFieldNumber = 27;
  ::google::protobuf::uint32 tcpi_snd_cwnd() const;
  void set_tcpi_snd_cwnd(::google::protobuf::uint32 value);

  // uint32 tcpi_advmss = 28;
  void clear_tcpi_advmss();
  static const int kTcpiAdvmssFieldNumber = 28;
  ::google::protobuf::uint32 tcpi_advmss() const;
  void set_tcpi_advmss(::google::protobuf::uint32 value);

  // uint32 tcpi_reordering = 29;
  void clear_tcpi_reordering();
  static const int kTcpiReorderingFieldNumber = 29;
  ::google::protobuf::uint32 tcpi_reordering() const;
  void set_tcpi_reordering(::google::protobuf::uint32 value);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.SocketOptionTcpInfo)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::uint32 tcpi_state_;
  ::google::protobuf::uint32 tcpi_ca_state_;
  ::google::protobuf::uint32 tcpi_retransmits_;
  ::google::protobuf::uint32 tcpi_probes_;
  ::google::protobuf::uint32 tcpi_backoff_;
  ::google::protobuf::uint32 tcpi_options_;
  ::google::protobuf::uint32 tcpi_snd_wscale_;
  ::google::protobuf::uint32 tcpi_rcv_wscale_;
  ::google::protobuf::uint32 tcpi_rto_;
  ::google::protobuf::uint32 tcpi_ato_;
  ::google::protobuf::uint32 tcpi_snd_mss_;
  ::google::protobuf::uint32 tcpi_rcv_mss_;
  ::google::protobuf::uint32 tcpi_unacked_;
  ::google::protobuf::uint32 tcpi_sacked_;
  ::google::protobuf::uint32 tcpi_lost_;
  ::google::protobuf::uint32 tcpi_retrans_;
  ::google::protobuf::uint32 tcpi_fackets_;
  ::google::protobuf::uint32 tcpi_last_data_sent_;
  ::google::protobuf::uint32 tcpi_last_ack_sent_;
  ::google::protobuf::uint32 tcpi_last_data_recv_;
  ::google::protobuf::uint32 tcpi_last_ack_recv_;
  ::google::protobuf::uint32 tcpi_pmtu_;
  ::google::protobuf::uint32 tcpi_rcv_ssthresh_;
  ::google::protobuf::uint32 tcpi_rtt_;
  ::google::protobuf::uint32 tcpi_rttvar_;
  ::google::protobuf::uint32 tcpi_snd_ssthresh_;
  ::google::protobuf::uint32 tcpi_snd_cwnd_;
  ::google::protobuf::uint32 tcpi_advmss_;
  ::google::protobuf::uint32 tcpi_reordering_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsSocketOptionTcpInfoImpl();
};
// -------------------------------------------------------------------

class GetTopChannelsRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.GetTopChannelsRequest) */ {
 public:
  GetTopChannelsRequest();
  virtual ~GetTopChannelsRequest();

  GetTopChannelsRequest(const GetTopChannelsRequest& from);

  inline GetTopChannelsRequest& operator=(const GetTopChannelsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  GetTopChannelsRequest(GetTopChannelsRequest&& from) noexcept
    : GetTopChannelsRequest() {
    *this = ::std::move(from);
  }

  inline GetTopChannelsRequest& operator=(GetTopChannelsRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const GetTopChannelsRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GetTopChannelsRequest* internal_default_instance() {
    return reinterpret_cast<const GetTopChannelsRequest*>(
               &_GetTopChannelsRequest_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    25;

  void Swap(GetTopChannelsRequest* other);
  friend void swap(GetTopChannelsRequest& a, GetTopChannelsRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline GetTopChannelsRequest* New() const PROTOBUF_FINAL { return New(NULL); }

  GetTopChannelsRequest* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const GetTopChannelsRequest& from);
  void MergeFrom(const GetTopChannelsRequest& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(GetTopChannelsRequest* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 start_channel_id = 1;
  void clear_start_channel_id();
  static const int kStartChannelIdFieldNumber = 1;
  ::google::protobuf::int64 start_channel_id() const;
  void set_start_channel_id(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.GetTopChannelsRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int64 start_channel_id_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsGetTopChannelsRequestImpl();
};
// -------------------------------------------------------------------

class GetTopChannelsResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.GetTopChannelsResponse) */ {
 public:
  GetTopChannelsResponse();
  virtual ~GetTopChannelsResponse();

  GetTopChannelsResponse(const GetTopChannelsResponse& from);

  inline GetTopChannelsResponse& operator=(const GetTopChannelsResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  GetTopChannelsResponse(GetTopChannelsResponse&& from) noexcept
    : GetTopChannelsResponse() {
    *this = ::std::move(from);
  }

  inline GetTopChannelsResponse& operator=(GetTopChannelsResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const GetTopChannelsResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GetTopChannelsResponse* internal_default_instance() {
    return reinterpret_cast<const GetTopChannelsResponse*>(
               &_GetTopChannelsResponse_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    26;

  void Swap(GetTopChannelsResponse* other);
  friend void swap(GetTopChannelsResponse& a, GetTopChannelsResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline GetTopChannelsResponse* New() const PROTOBUF_FINAL { return New(NULL); }

  GetTopChannelsResponse* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const GetTopChannelsResponse& from);
  void MergeFrom(const GetTopChannelsResponse& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(GetTopChannelsResponse* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .grpc.channelz.v1.Channel channel = 1;
  int channel_size() const;
  void clear_channel();
  static const int kChannelFieldNumber = 1;
  const ::grpc::channelz::v1::Channel& channel(int index) const;
  ::grpc::channelz::v1::Channel* mutable_channel(int index);
  ::grpc::channelz::v1::Channel* add_channel();
  ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::Channel >*
      mutable_channel();
  const ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::Channel >&
      channel() const;

  // bool end = 2;
  void clear_end();
  static const int kEndFieldNumber = 2;
  bool end() const;
  void set_end(bool value);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.GetTopChannelsResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::Channel > channel_;
  bool end_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsGetTopChannelsResponseImpl();
};
// -------------------------------------------------------------------

class GetServersRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.GetServersRequest) */ {
 public:
  GetServersRequest();
  virtual ~GetServersRequest();

  GetServersRequest(const GetServersRequest& from);

  inline GetServersRequest& operator=(const GetServersRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  GetServersRequest(GetServersRequest&& from) noexcept
    : GetServersRequest() {
    *this = ::std::move(from);
  }

  inline GetServersRequest& operator=(GetServersRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const GetServersRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GetServersRequest* internal_default_instance() {
    return reinterpret_cast<const GetServersRequest*>(
               &_GetServersRequest_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    27;

  void Swap(GetServersRequest* other);
  friend void swap(GetServersRequest& a, GetServersRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline GetServersRequest* New() const PROTOBUF_FINAL { return New(NULL); }

  GetServersRequest* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const GetServersRequest& from);
  void MergeFrom(const GetServersRequest& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(GetServersRequest* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 start_server_id = 1;
  void clear_start_server_id();
  static const int kStartServerIdFieldNumber = 1;
  ::google::protobuf::int64 start_server_id() const;
  void set_start_server_id(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.GetServersRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int64 start_server_id_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsGetServersRequestImpl();
};
// -------------------------------------------------------------------

class GetServersResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.GetServersResponse) */ {
 public:
  GetServersResponse();
  virtual ~GetServersResponse();

  GetServersResponse(const GetServersResponse& from);

  inline GetServersResponse& operator=(const GetServersResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  GetServersResponse(GetServersResponse&& from) noexcept
    : GetServersResponse() {
    *this = ::std::move(from);
  }

  inline GetServersResponse& operator=(GetServersResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const GetServersResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GetServersResponse* internal_default_instance() {
    return reinterpret_cast<const GetServersResponse*>(
               &_GetServersResponse_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    28;

  void Swap(GetServersResponse* other);
  friend void swap(GetServersResponse& a, GetServersResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline GetServersResponse* New() const PROTOBUF_FINAL { return New(NULL); }

  GetServersResponse* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const GetServersResponse& from);
  void MergeFrom(const GetServersResponse& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(GetServersResponse* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .grpc.channelz.v1.Server server = 1;
  int server_size() const;
  void clear_server();
  static const int kServerFieldNumber = 1;
  const ::grpc::channelz::v1::Server& server(int index) const;
  ::grpc::channelz::v1::Server* mutable_server(int index);
  ::grpc::channelz::v1::Server* add_server();
  ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::Server >*
      mutable_server();
  const ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::Server >&
      server() const;

  // bool end = 2;
  void clear_end();
  static const int kEndFieldNumber = 2;
  bool end() const;
  void set_end(bool value);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.GetServersResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::Server > server_;
  bool end_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsGetServersResponseImpl();
};
// -------------------------------------------------------------------

class GetServerSocketsRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.GetServerSocketsRequest) */ {
 public:
  GetServerSocketsRequest();
  virtual ~GetServerSocketsRequest();

  GetServerSocketsRequest(const GetServerSocketsRequest& from);

  inline GetServerSocketsRequest& operator=(const GetServerSocketsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  GetServerSocketsRequest(GetServerSocketsRequest&& from) noexcept
    : GetServerSocketsRequest() {
    *this = ::std::move(from);
  }

  inline GetServerSocketsRequest& operator=(GetServerSocketsRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const GetServerSocketsRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GetServerSocketsRequest* internal_default_instance() {
    return reinterpret_cast<const GetServerSocketsRequest*>(
               &_GetServerSocketsRequest_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    29;

  void Swap(GetServerSocketsRequest* other);
  friend void swap(GetServerSocketsRequest& a, GetServerSocketsRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline GetServerSocketsRequest* New() const PROTOBUF_FINAL { return New(NULL); }

  GetServerSocketsRequest* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const GetServerSocketsRequest& from);
  void MergeFrom(const GetServerSocketsRequest& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(GetServerSocketsRequest* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 server_id = 1;
  void clear_server_id();
  static const int kServerIdFieldNumber = 1;
  ::google::protobuf::int64 server_id() const;
  void set_server_id(::google::protobuf::int64 value);

  // int64 start_socket_id = 2;
  void clear_start_socket_id();
  static const int kStartSocketIdFieldNumber = 2;
  ::google::protobuf::int64 start_socket_id() const;
  void set_start_socket_id(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.GetServerSocketsRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int64 server_id_;
  ::google::protobuf::int64 start_socket_id_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsGetServerSocketsRequestImpl();
};
// -------------------------------------------------------------------

class GetServerSocketsResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.GetServerSocketsResponse) */ {
 public:
  GetServerSocketsResponse();
  virtual ~GetServerSocketsResponse();

  GetServerSocketsResponse(const GetServerSocketsResponse& from);

  inline GetServerSocketsResponse& operator=(const GetServerSocketsResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  GetServerSocketsResponse(GetServerSocketsResponse&& from) noexcept
    : GetServerSocketsResponse() {
    *this = ::std::move(from);
  }

  inline GetServerSocketsResponse& operator=(GetServerSocketsResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const GetServerSocketsResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GetServerSocketsResponse* internal_default_instance() {
    return reinterpret_cast<const GetServerSocketsResponse*>(
               &_GetServerSocketsResponse_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    30;

  void Swap(GetServerSocketsResponse* other);
  friend void swap(GetServerSocketsResponse& a, GetServerSocketsResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline GetServerSocketsResponse* New() const PROTOBUF_FINAL { return New(NULL); }

  GetServerSocketsResponse* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const GetServerSocketsResponse& from);
  void MergeFrom(const GetServerSocketsResponse& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(GetServerSocketsResponse* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .grpc.channelz.v1.SocketRef socket_ref = 1;
  int socket_ref_size() const;
  void clear_socket_ref();
  static const int kSocketRefFieldNumber = 1;
  const ::grpc::channelz::v1::SocketRef& socket_ref(int index) const;
  ::grpc::channelz::v1::SocketRef* mutable_socket_ref(int index);
  ::grpc::channelz::v1::SocketRef* add_socket_ref();
  ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SocketRef >*
      mutable_socket_ref();
  const ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SocketRef >&
      socket_ref() const;

  // bool end = 2;
  void clear_end();
  static const int kEndFieldNumber = 2;
  bool end() const;
  void set_end(bool value);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.GetServerSocketsResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SocketRef > socket_ref_;
  bool end_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsGetServerSocketsResponseImpl();
};
// -------------------------------------------------------------------

class GetChannelRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.GetChannelRequest) */ {
 public:
  GetChannelRequest();
  virtual ~GetChannelRequest();

  GetChannelRequest(const GetChannelRequest& from);

  inline GetChannelRequest& operator=(const GetChannelRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  GetChannelRequest(GetChannelRequest&& from) noexcept
    : GetChannelRequest() {
    *this = ::std::move(from);
  }

  inline GetChannelRequest& operator=(GetChannelRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const GetChannelRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GetChannelRequest* internal_default_instance() {
    return reinterpret_cast<const GetChannelRequest*>(
               &_GetChannelRequest_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    31;

  void Swap(GetChannelRequest* other);
  friend void swap(GetChannelRequest& a, GetChannelRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline GetChannelRequest* New() const PROTOBUF_FINAL { return New(NULL); }

  GetChannelRequest* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const GetChannelRequest& from);
  void MergeFrom(const GetChannelRequest& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(GetChannelRequest* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 channel_id = 1;
  void clear_channel_id();
  static const int kChannelIdFieldNumber = 1;
  ::google::protobuf::int64 channel_id() const;
  void set_channel_id(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.GetChannelRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int64 channel_id_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsGetChannelRequestImpl();
};
// -------------------------------------------------------------------

class GetChannelResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.GetChannelResponse) */ {
 public:
  GetChannelResponse();
  virtual ~GetChannelResponse();

  GetChannelResponse(const GetChannelResponse& from);

  inline GetChannelResponse& operator=(const GetChannelResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  GetChannelResponse(GetChannelResponse&& from) noexcept
    : GetChannelResponse() {
    *this = ::std::move(from);
  }

  inline GetChannelResponse& operator=(GetChannelResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const GetChannelResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GetChannelResponse* internal_default_instance() {
    return reinterpret_cast<const GetChannelResponse*>(
               &_GetChannelResponse_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    32;

  void Swap(GetChannelResponse* other);
  friend void swap(GetChannelResponse& a, GetChannelResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline GetChannelResponse* New() const PROTOBUF_FINAL { return New(NULL); }

  GetChannelResponse* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const GetChannelResponse& from);
  void MergeFrom(const GetChannelResponse& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(GetChannelResponse* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .grpc.channelz.v1.Channel channel = 1;
  bool has_channel() const;
  void clear_channel();
  static const int kChannelFieldNumber = 1;
  const ::grpc::channelz::v1::Channel& channel() const;
  ::grpc::channelz::v1::Channel* release_channel();
  ::grpc::channelz::v1::Channel* mutable_channel();
  void set_allocated_channel(::grpc::channelz::v1::Channel* channel);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.GetChannelResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::grpc::channelz::v1::Channel* channel_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsGetChannelResponseImpl();
};
// -------------------------------------------------------------------

class GetSubchannelRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.GetSubchannelRequest) */ {
 public:
  GetSubchannelRequest();
  virtual ~GetSubchannelRequest();

  GetSubchannelRequest(const GetSubchannelRequest& from);

  inline GetSubchannelRequest& operator=(const GetSubchannelRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  GetSubchannelRequest(GetSubchannelRequest&& from) noexcept
    : GetSubchannelRequest() {
    *this = ::std::move(from);
  }

  inline GetSubchannelRequest& operator=(GetSubchannelRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const GetSubchannelRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GetSubchannelRequest* internal_default_instance() {
    return reinterpret_cast<const GetSubchannelRequest*>(
               &_GetSubchannelRequest_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    33;

  void Swap(GetSubchannelRequest* other);
  friend void swap(GetSubchannelRequest& a, GetSubchannelRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline GetSubchannelRequest* New() const PROTOBUF_FINAL { return New(NULL); }

  GetSubchannelRequest* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const GetSubchannelRequest& from);
  void MergeFrom(const GetSubchannelRequest& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(GetSubchannelRequest* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 subchannel_id = 1;
  void clear_subchannel_id();
  static const int kSubchannelIdFieldNumber = 1;
  ::google::protobuf::int64 subchannel_id() const;
  void set_subchannel_id(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.GetSubchannelRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int64 subchannel_id_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsGetSubchannelRequestImpl();
};
// -------------------------------------------------------------------

class GetSubchannelResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.GetSubchannelResponse) */ {
 public:
  GetSubchannelResponse();
  virtual ~GetSubchannelResponse();

  GetSubchannelResponse(const GetSubchannelResponse& from);

  inline GetSubchannelResponse& operator=(const GetSubchannelResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  GetSubchannelResponse(GetSubchannelResponse&& from) noexcept
    : GetSubchannelResponse() {
    *this = ::std::move(from);
  }

  inline GetSubchannelResponse& operator=(GetSubchannelResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const GetSubchannelResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GetSubchannelResponse* internal_default_instance() {
    return reinterpret_cast<const GetSubchannelResponse*>(
               &_GetSubchannelResponse_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    34;

  void Swap(GetSubchannelResponse* other);
  friend void swap(GetSubchannelResponse& a, GetSubchannelResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline GetSubchannelResponse* New() const PROTOBUF_FINAL { return New(NULL); }

  GetSubchannelResponse* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const GetSubchannelResponse& from);
  void MergeFrom(const GetSubchannelResponse& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(GetSubchannelResponse* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .grpc.channelz.v1.Subchannel subchannel = 1;
  bool has_subchannel() const;
  void clear_subchannel();
  static const int kSubchannelFieldNumber = 1;
  const ::grpc::channelz::v1::Subchannel& subchannel() const;
  ::grpc::channelz::v1::Subchannel* release_subchannel();
  ::grpc::channelz::v1::Subchannel* mutable_subchannel();
  void set_allocated_subchannel(::grpc::channelz::v1::Subchannel* subchannel);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.GetSubchannelResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::grpc::channelz::v1::Subchannel* subchannel_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsGetSubchannelResponseImpl();
};
// -------------------------------------------------------------------

class GetSocketRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.GetSocketRequest) */ {
 public:
  GetSocketRequest();
  virtual ~GetSocketRequest();

  GetSocketRequest(const GetSocketRequest& from);

  inline GetSocketRequest& operator=(const GetSocketRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  GetSocketRequest(GetSocketRequest&& from) noexcept
    : GetSocketRequest() {
    *this = ::std::move(from);
  }

  inline GetSocketRequest& operator=(GetSocketRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const GetSocketRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GetSocketRequest* internal_default_instance() {
    return reinterpret_cast<const GetSocketRequest*>(
               &_GetSocketRequest_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    35;

  void Swap(GetSocketRequest* other);
  friend void swap(GetSocketRequest& a, GetSocketRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline GetSocketRequest* New() const PROTOBUF_FINAL { return New(NULL); }

  GetSocketRequest* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const GetSocketRequest& from);
  void MergeFrom(const GetSocketRequest& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(GetSocketRequest* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 socket_id = 1;
  void clear_socket_id();
  static const int kSocketIdFieldNumber = 1;
  ::google::protobuf::int64 socket_id() const;
  void set_socket_id(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.GetSocketRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int64 socket_id_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsGetSocketRequestImpl();
};
// -------------------------------------------------------------------

class GetSocketResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.channelz.v1.GetSocketResponse) */ {
 public:
  GetSocketResponse();
  virtual ~GetSocketResponse();

  GetSocketResponse(const GetSocketResponse& from);

  inline GetSocketResponse& operator=(const GetSocketResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  GetSocketResponse(GetSocketResponse&& from) noexcept
    : GetSocketResponse() {
    *this = ::std::move(from);
  }

  inline GetSocketResponse& operator=(GetSocketResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const GetSocketResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GetSocketResponse* internal_default_instance() {
    return reinterpret_cast<const GetSocketResponse*>(
               &_GetSocketResponse_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    36;

  void Swap(GetSocketResponse* other);
  friend void swap(GetSocketResponse& a, GetSocketResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline GetSocketResponse* New() const PROTOBUF_FINAL { return New(NULL); }

  GetSocketResponse* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const GetSocketResponse& from);
  void MergeFrom(const GetSocketResponse& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(GetSocketResponse* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .grpc.channelz.v1.Socket socket = 1;
  bool has_socket() const;
  void clear_socket();
  static const int kSocketFieldNumber = 1;
  const ::grpc::channelz::v1::Socket& socket() const;
  ::grpc::channelz::v1::Socket* release_socket();
  ::grpc::channelz::v1::Socket* mutable_socket();
  void set_allocated_socket(::grpc::channelz::v1::Socket* socket);

  // @@protoc_insertion_point(class_scope:grpc.channelz.v1.GetSocketResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::grpc::channelz::v1::Socket* socket_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto::InitDefaultsGetSocketResponseImpl();
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Channel

// .grpc.channelz.v1.ChannelRef ref = 1;
inline bool Channel::has_ref() const {
  return this != internal_default_instance() && ref_ != NULL;
}
inline void Channel::clear_ref() {
  if (GetArenaNoVirtual() == NULL && ref_ != NULL) {
    delete ref_;
  }
  ref_ = NULL;
}
inline const ::grpc::channelz::v1::ChannelRef& Channel::ref() const {
  const ::grpc::channelz::v1::ChannelRef* p = ref_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Channel.ref)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::channelz::v1::ChannelRef*>(
      &::grpc::channelz::v1::_ChannelRef_default_instance_);
}
inline ::grpc::channelz::v1::ChannelRef* Channel::release_ref() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.Channel.ref)
  
  ::grpc::channelz::v1::ChannelRef* temp = ref_;
  ref_ = NULL;
  return temp;
}
inline ::grpc::channelz::v1::ChannelRef* Channel::mutable_ref() {
  
  if (ref_ == NULL) {
    ref_ = new ::grpc::channelz::v1::ChannelRef;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Channel.ref)
  return ref_;
}
inline void Channel::set_allocated_ref(::grpc::channelz::v1::ChannelRef* ref) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete ref_;
  }
  if (ref) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      ref = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, ref, submessage_arena);
    }
    
  } else {
    
  }
  ref_ = ref;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.Channel.ref)
}

// .grpc.channelz.v1.ChannelData data = 2;
inline bool Channel::has_data() const {
  return this != internal_default_instance() && data_ != NULL;
}
inline void Channel::clear_data() {
  if (GetArenaNoVirtual() == NULL && data_ != NULL) {
    delete data_;
  }
  data_ = NULL;
}
inline const ::grpc::channelz::v1::ChannelData& Channel::data() const {
  const ::grpc::channelz::v1::ChannelData* p = data_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Channel.data)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::channelz::v1::ChannelData*>(
      &::grpc::channelz::v1::_ChannelData_default_instance_);
}
inline ::grpc::channelz::v1::ChannelData* Channel::release_data() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.Channel.data)
  
  ::grpc::channelz::v1::ChannelData* temp = data_;
  data_ = NULL;
  return temp;
}
inline ::grpc::channelz::v1::ChannelData* Channel::mutable_data() {
  
  if (data_ == NULL) {
    data_ = new ::grpc::channelz::v1::ChannelData;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Channel.data)
  return data_;
}
inline void Channel::set_allocated_data(::grpc::channelz::v1::ChannelData* data) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete data_;
  }
  if (data) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      data = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, data, submessage_arena);
    }
    
  } else {
    
  }
  data_ = data;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.Channel.data)
}

// repeated .grpc.channelz.v1.ChannelRef channel_ref = 3;
inline int Channel::channel_ref_size() const {
  return channel_ref_.size();
}
inline void Channel::clear_channel_ref() {
  channel_ref_.Clear();
}
inline const ::grpc::channelz::v1::ChannelRef& Channel::channel_ref(int index) const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Channel.channel_ref)
  return channel_ref_.Get(index);
}
inline ::grpc::channelz::v1::ChannelRef* Channel::mutable_channel_ref(int index) {
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Channel.channel_ref)
  return channel_ref_.Mutable(index);
}
inline ::grpc::channelz::v1::ChannelRef* Channel::add_channel_ref() {
  // @@protoc_insertion_point(field_add:grpc.channelz.v1.Channel.channel_ref)
  return channel_ref_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::ChannelRef >*
Channel::mutable_channel_ref() {
  // @@protoc_insertion_point(field_mutable_list:grpc.channelz.v1.Channel.channel_ref)
  return &channel_ref_;
}
inline const ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::ChannelRef >&
Channel::channel_ref() const {
  // @@protoc_insertion_point(field_list:grpc.channelz.v1.Channel.channel_ref)
  return channel_ref_;
}

// repeated .grpc.channelz.v1.SubchannelRef subchannel_ref = 4;
inline int Channel::subchannel_ref_size() const {
  return subchannel_ref_.size();
}
inline void Channel::clear_subchannel_ref() {
  subchannel_ref_.Clear();
}
inline const ::grpc::channelz::v1::SubchannelRef& Channel::subchannel_ref(int index) const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Channel.subchannel_ref)
  return subchannel_ref_.Get(index);
}
inline ::grpc::channelz::v1::SubchannelRef* Channel::mutable_subchannel_ref(int index) {
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Channel.subchannel_ref)
  return subchannel_ref_.Mutable(index);
}
inline ::grpc::channelz::v1::SubchannelRef* Channel::add_subchannel_ref() {
  // @@protoc_insertion_point(field_add:grpc.channelz.v1.Channel.subchannel_ref)
  return subchannel_ref_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SubchannelRef >*
Channel::mutable_subchannel_ref() {
  // @@protoc_insertion_point(field_mutable_list:grpc.channelz.v1.Channel.subchannel_ref)
  return &subchannel_ref_;
}
inline const ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SubchannelRef >&
Channel::subchannel_ref() const {
  // @@protoc_insertion_point(field_list:grpc.channelz.v1.Channel.subchannel_ref)
  return subchannel_ref_;
}

// repeated .grpc.channelz.v1.SocketRef socket_ref = 5;
inline int Channel::socket_ref_size() const {
  return socket_ref_.size();
}
inline void Channel::clear_socket_ref() {
  socket_ref_.Clear();
}
inline const ::grpc::channelz::v1::SocketRef& Channel::socket_ref(int index) const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Channel.socket_ref)
  return socket_ref_.Get(index);
}
inline ::grpc::channelz::v1::SocketRef* Channel::mutable_socket_ref(int index) {
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Channel.socket_ref)
  return socket_ref_.Mutable(index);
}
inline ::grpc::channelz::v1::SocketRef* Channel::add_socket_ref() {
  // @@protoc_insertion_point(field_add:grpc.channelz.v1.Channel.socket_ref)
  return socket_ref_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SocketRef >*
Channel::mutable_socket_ref() {
  // @@protoc_insertion_point(field_mutable_list:grpc.channelz.v1.Channel.socket_ref)
  return &socket_ref_;
}
inline const ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SocketRef >&
Channel::socket_ref() const {
  // @@protoc_insertion_point(field_list:grpc.channelz.v1.Channel.socket_ref)
  return socket_ref_;
}

// -------------------------------------------------------------------

// Subchannel

// .grpc.channelz.v1.SubchannelRef ref = 1;
inline bool Subchannel::has_ref() const {
  return this != internal_default_instance() && ref_ != NULL;
}
inline void Subchannel::clear_ref() {
  if (GetArenaNoVirtual() == NULL && ref_ != NULL) {
    delete ref_;
  }
  ref_ = NULL;
}
inline const ::grpc::channelz::v1::SubchannelRef& Subchannel::ref() const {
  const ::grpc::channelz::v1::SubchannelRef* p = ref_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Subchannel.ref)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::channelz::v1::SubchannelRef*>(
      &::grpc::channelz::v1::_SubchannelRef_default_instance_);
}
inline ::grpc::channelz::v1::SubchannelRef* Subchannel::release_ref() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.Subchannel.ref)
  
  ::grpc::channelz::v1::SubchannelRef* temp = ref_;
  ref_ = NULL;
  return temp;
}
inline ::grpc::channelz::v1::SubchannelRef* Subchannel::mutable_ref() {
  
  if (ref_ == NULL) {
    ref_ = new ::grpc::channelz::v1::SubchannelRef;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Subchannel.ref)
  return ref_;
}
inline void Subchannel::set_allocated_ref(::grpc::channelz::v1::SubchannelRef* ref) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete ref_;
  }
  if (ref) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      ref = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, ref, submessage_arena);
    }
    
  } else {
    
  }
  ref_ = ref;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.Subchannel.ref)
}

// .grpc.channelz.v1.ChannelData data = 2;
inline bool Subchannel::has_data() const {
  return this != internal_default_instance() && data_ != NULL;
}
inline void Subchannel::clear_data() {
  if (GetArenaNoVirtual() == NULL && data_ != NULL) {
    delete data_;
  }
  data_ = NULL;
}
inline const ::grpc::channelz::v1::ChannelData& Subchannel::data() const {
  const ::grpc::channelz::v1::ChannelData* p = data_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Subchannel.data)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::channelz::v1::ChannelData*>(
      &::grpc::channelz::v1::_ChannelData_default_instance_);
}
inline ::grpc::channelz::v1::ChannelData* Subchannel::release_data() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.Subchannel.data)
  
  ::grpc::channelz::v1::ChannelData* temp = data_;
  data_ = NULL;
  return temp;
}
inline ::grpc::channelz::v1::ChannelData* Subchannel::mutable_data() {
  
  if (data_ == NULL) {
    data_ = new ::grpc::channelz::v1::ChannelData;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Subchannel.data)
  return data_;
}
inline void Subchannel::set_allocated_data(::grpc::channelz::v1::ChannelData* data) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete data_;
  }
  if (data) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      data = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, data, submessage_arena);
    }
    
  } else {
    
  }
  data_ = data;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.Subchannel.data)
}

// repeated .grpc.channelz.v1.ChannelRef channel_ref = 3;
inline int Subchannel::channel_ref_size() const {
  return channel_ref_.size();
}
inline void Subchannel::clear_channel_ref() {
  channel_ref_.Clear();
}
inline const ::grpc::channelz::v1::ChannelRef& Subchannel::channel_ref(int index) const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Subchannel.channel_ref)
  return channel_ref_.Get(index);
}
inline ::grpc::channelz::v1::ChannelRef* Subchannel::mutable_channel_ref(int index) {
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Subchannel.channel_ref)
  return channel_ref_.Mutable(index);
}
inline ::grpc::channelz::v1::ChannelRef* Subchannel::add_channel_ref() {
  // @@protoc_insertion_point(field_add:grpc.channelz.v1.Subchannel.channel_ref)
  return channel_ref_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::ChannelRef >*
Subchannel::mutable_channel_ref() {
  // @@protoc_insertion_point(field_mutable_list:grpc.channelz.v1.Subchannel.channel_ref)
  return &channel_ref_;
}
inline const ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::ChannelRef >&
Subchannel::channel_ref() const {
  // @@protoc_insertion_point(field_list:grpc.channelz.v1.Subchannel.channel_ref)
  return channel_ref_;
}

// repeated .grpc.channelz.v1.SubchannelRef subchannel_ref = 4;
inline int Subchannel::subchannel_ref_size() const {
  return subchannel_ref_.size();
}
inline void Subchannel::clear_subchannel_ref() {
  subchannel_ref_.Clear();
}
inline const ::grpc::channelz::v1::SubchannelRef& Subchannel::subchannel_ref(int index) const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Subchannel.subchannel_ref)
  return subchannel_ref_.Get(index);
}
inline ::grpc::channelz::v1::SubchannelRef* Subchannel::mutable_subchannel_ref(int index) {
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Subchannel.subchannel_ref)
  return subchannel_ref_.Mutable(index);
}
inline ::grpc::channelz::v1::SubchannelRef* Subchannel::add_subchannel_ref() {
  // @@protoc_insertion_point(field_add:grpc.channelz.v1.Subchannel.subchannel_ref)
  return subchannel_ref_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SubchannelRef >*
Subchannel::mutable_subchannel_ref() {
  // @@protoc_insertion_point(field_mutable_list:grpc.channelz.v1.Subchannel.subchannel_ref)
  return &subchannel_ref_;
}
inline const ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SubchannelRef >&
Subchannel::subchannel_ref() const {
  // @@protoc_insertion_point(field_list:grpc.channelz.v1.Subchannel.subchannel_ref)
  return subchannel_ref_;
}

// repeated .grpc.channelz.v1.SocketRef socket_ref = 5;
inline int Subchannel::socket_ref_size() const {
  return socket_ref_.size();
}
inline void Subchannel::clear_socket_ref() {
  socket_ref_.Clear();
}
inline const ::grpc::channelz::v1::SocketRef& Subchannel::socket_ref(int index) const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Subchannel.socket_ref)
  return socket_ref_.Get(index);
}
inline ::grpc::channelz::v1::SocketRef* Subchannel::mutable_socket_ref(int index) {
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Subchannel.socket_ref)
  return socket_ref_.Mutable(index);
}
inline ::grpc::channelz::v1::SocketRef* Subchannel::add_socket_ref() {
  // @@protoc_insertion_point(field_add:grpc.channelz.v1.Subchannel.socket_ref)
  return socket_ref_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SocketRef >*
Subchannel::mutable_socket_ref() {
  // @@protoc_insertion_point(field_mutable_list:grpc.channelz.v1.Subchannel.socket_ref)
  return &socket_ref_;
}
inline const ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SocketRef >&
Subchannel::socket_ref() const {
  // @@protoc_insertion_point(field_list:grpc.channelz.v1.Subchannel.socket_ref)
  return socket_ref_;
}

// -------------------------------------------------------------------

// ChannelConnectivityState

// .grpc.channelz.v1.ChannelConnectivityState.State state = 1;
inline void ChannelConnectivityState::clear_state() {
  state_ = 0;
}
inline ::grpc::channelz::v1::ChannelConnectivityState_State ChannelConnectivityState::state() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.ChannelConnectivityState.state)
  return static_cast< ::grpc::channelz::v1::ChannelConnectivityState_State >(state_);
}
inline void ChannelConnectivityState::set_state(::grpc::channelz::v1::ChannelConnectivityState_State value) {
  
  state_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.ChannelConnectivityState.state)
}

// -------------------------------------------------------------------

// ChannelData

// .grpc.channelz.v1.ChannelConnectivityState state = 1;
inline bool ChannelData::has_state() const {
  return this != internal_default_instance() && state_ != NULL;
}
inline void ChannelData::clear_state() {
  if (GetArenaNoVirtual() == NULL && state_ != NULL) {
    delete state_;
  }
  state_ = NULL;
}
inline const ::grpc::channelz::v1::ChannelConnectivityState& ChannelData::state() const {
  const ::grpc::channelz::v1::ChannelConnectivityState* p = state_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.ChannelData.state)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::channelz::v1::ChannelConnectivityState*>(
      &::grpc::channelz::v1::_ChannelConnectivityState_default_instance_);
}
inline ::grpc::channelz::v1::ChannelConnectivityState* ChannelData::release_state() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.ChannelData.state)
  
  ::grpc::channelz::v1::ChannelConnectivityState* temp = state_;
  state_ = NULL;
  return temp;
}
inline ::grpc::channelz::v1::ChannelConnectivityState* ChannelData::mutable_state() {
  
  if (state_ == NULL) {
    state_ = new ::grpc::channelz::v1::ChannelConnectivityState;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.ChannelData.state)
  return state_;
}
inline void ChannelData::set_allocated_state(::grpc::channelz::v1::ChannelConnectivityState* state) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete state_;
  }
  if (state) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      state = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, state, submessage_arena);
    }
    
  } else {
    
  }
  state_ = state;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.ChannelData.state)
}

// string target = 2;
inline void ChannelData::clear_target() {
  target_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ChannelData::target() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.ChannelData.target)
  return target_.GetNoArena();
}
inline void ChannelData::set_target(const ::std::string& value) {
  
  target_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.ChannelData.target)
}
#if LANG_CXX11
inline void ChannelData::set_target(::std::string&& value) {
  
  target_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.channelz.v1.ChannelData.target)
}
#endif
inline void ChannelData::set_target(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  target_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.channelz.v1.ChannelData.target)
}
inline void ChannelData::set_target(const char* value, size_t size) {
  
  target_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.channelz.v1.ChannelData.target)
}
inline ::std::string* ChannelData::mutable_target() {
  
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.ChannelData.target)
  return target_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ChannelData::release_target() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.ChannelData.target)
  
  return target_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ChannelData::set_allocated_target(::std::string* target) {
  if (target != NULL) {
    
  } else {
    
  }
  target_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), target);
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.ChannelData.target)
}

// .grpc.channelz.v1.ChannelTrace trace = 3;
inline bool ChannelData::has_trace() const {
  return this != internal_default_instance() && trace_ != NULL;
}
inline void ChannelData::clear_trace() {
  if (GetArenaNoVirtual() == NULL && trace_ != NULL) {
    delete trace_;
  }
  trace_ = NULL;
}
inline const ::grpc::channelz::v1::ChannelTrace& ChannelData::trace() const {
  const ::grpc::channelz::v1::ChannelTrace* p = trace_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.ChannelData.trace)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::channelz::v1::ChannelTrace*>(
      &::grpc::channelz::v1::_ChannelTrace_default_instance_);
}
inline ::grpc::channelz::v1::ChannelTrace* ChannelData::release_trace() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.ChannelData.trace)
  
  ::grpc::channelz::v1::ChannelTrace* temp = trace_;
  trace_ = NULL;
  return temp;
}
inline ::grpc::channelz::v1::ChannelTrace* ChannelData::mutable_trace() {
  
  if (trace_ == NULL) {
    trace_ = new ::grpc::channelz::v1::ChannelTrace;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.ChannelData.trace)
  return trace_;
}
inline void ChannelData::set_allocated_trace(::grpc::channelz::v1::ChannelTrace* trace) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete trace_;
  }
  if (trace) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      trace = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, trace, submessage_arena);
    }
    
  } else {
    
  }
  trace_ = trace;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.ChannelData.trace)
}

// int64 calls_started = 4;
inline void ChannelData::clear_calls_started() {
  calls_started_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ChannelData::calls_started() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.ChannelData.calls_started)
  return calls_started_;
}
inline void ChannelData::set_calls_started(::google::protobuf::int64 value) {
  
  calls_started_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.ChannelData.calls_started)
}

// int64 calls_succeeded = 5;
inline void ChannelData::clear_calls_succeeded() {
  calls_succeeded_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ChannelData::calls_succeeded() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.ChannelData.calls_succeeded)
  return calls_succeeded_;
}
inline void ChannelData::set_calls_succeeded(::google::protobuf::int64 value) {
  
  calls_succeeded_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.ChannelData.calls_succeeded)
}

// int64 calls_failed = 6;
inline void ChannelData::clear_calls_failed() {
  calls_failed_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ChannelData::calls_failed() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.ChannelData.calls_failed)
  return calls_failed_;
}
inline void ChannelData::set_calls_failed(::google::protobuf::int64 value) {
  
  calls_failed_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.ChannelData.calls_failed)
}

// .google.protobuf.Timestamp last_call_started_timestamp = 7;
inline bool ChannelData::has_last_call_started_timestamp() const {
  return this != internal_default_instance() && last_call_started_timestamp_ != NULL;
}
inline const ::google::protobuf::Timestamp& ChannelData::last_call_started_timestamp() const {
  const ::google::protobuf::Timestamp* p = last_call_started_timestamp_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.ChannelData.last_call_started_timestamp)
  return p != NULL ? *p : *reinterpret_cast<const ::google::protobuf::Timestamp*>(
      &::google::protobuf::_Timestamp_default_instance_);
}
inline ::google::protobuf::Timestamp* ChannelData::release_last_call_started_timestamp() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.ChannelData.last_call_started_timestamp)
  
  ::google::protobuf::Timestamp* temp = last_call_started_timestamp_;
  last_call_started_timestamp_ = NULL;
  return temp;
}
inline ::google::protobuf::Timestamp* ChannelData::mutable_last_call_started_timestamp() {
  
  if (last_call_started_timestamp_ == NULL) {
    last_call_started_timestamp_ = new ::google::protobuf::Timestamp;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.ChannelData.last_call_started_timestamp)
  return last_call_started_timestamp_;
}
inline void ChannelData::set_allocated_last_call_started_timestamp(::google::protobuf::Timestamp* last_call_started_timestamp) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(last_call_started_timestamp_);
  }
  if (last_call_started_timestamp) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast< ::google::protobuf::MessageLite*>(last_call_started_timestamp)->GetArena();
    if (message_arena != submessage_arena) {
      last_call_started_timestamp = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, last_call_started_timestamp, submessage_arena);
    }
    
  } else {
    
  }
  last_call_started_timestamp_ = last_call_started_timestamp;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.ChannelData.last_call_started_timestamp)
}

// -------------------------------------------------------------------

// ChannelTraceEvent

// string description = 1;
inline void ChannelTraceEvent::clear_description() {
  description_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ChannelTraceEvent::description() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.ChannelTraceEvent.description)
  return description_.GetNoArena();
}
inline void ChannelTraceEvent::set_description(const ::std::string& value) {
  
  description_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.ChannelTraceEvent.description)
}
#if LANG_CXX11
inline void ChannelTraceEvent::set_description(::std::string&& value) {
  
  description_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.channelz.v1.ChannelTraceEvent.description)
}
#endif
inline void ChannelTraceEvent::set_description(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  description_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.channelz.v1.ChannelTraceEvent.description)
}
inline void ChannelTraceEvent::set_description(const char* value, size_t size) {
  
  description_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.channelz.v1.ChannelTraceEvent.description)
}
inline ::std::string* ChannelTraceEvent::mutable_description() {
  
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.ChannelTraceEvent.description)
  return description_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ChannelTraceEvent::release_description() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.ChannelTraceEvent.description)
  
  return description_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ChannelTraceEvent::set_allocated_description(::std::string* description) {
  if (description != NULL) {
    
  } else {
    
  }
  description_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), description);
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.ChannelTraceEvent.description)
}

// .grpc.channelz.v1.ChannelTraceEvent.Severity severity = 2;
inline void ChannelTraceEvent::clear_severity() {
  severity_ = 0;
}
inline ::grpc::channelz::v1::ChannelTraceEvent_Severity ChannelTraceEvent::severity() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.ChannelTraceEvent.severity)
  return static_cast< ::grpc::channelz::v1::ChannelTraceEvent_Severity >(severity_);
}
inline void ChannelTraceEvent::set_severity(::grpc::channelz::v1::ChannelTraceEvent_Severity value) {
  
  severity_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.ChannelTraceEvent.severity)
}

// .google.protobuf.Timestamp timestamp = 3;
inline bool ChannelTraceEvent::has_timestamp() const {
  return this != internal_default_instance() && timestamp_ != NULL;
}
inline const ::google::protobuf::Timestamp& ChannelTraceEvent::timestamp() const {
  const ::google::protobuf::Timestamp* p = timestamp_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.ChannelTraceEvent.timestamp)
  return p != NULL ? *p : *reinterpret_cast<const ::google::protobuf::Timestamp*>(
      &::google::protobuf::_Timestamp_default_instance_);
}
inline ::google::protobuf::Timestamp* ChannelTraceEvent::release_timestamp() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.ChannelTraceEvent.timestamp)
  
  ::google::protobuf::Timestamp* temp = timestamp_;
  timestamp_ = NULL;
  return temp;
}
inline ::google::protobuf::Timestamp* ChannelTraceEvent::mutable_timestamp() {
  
  if (timestamp_ == NULL) {
    timestamp_ = new ::google::protobuf::Timestamp;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.ChannelTraceEvent.timestamp)
  return timestamp_;
}
inline void ChannelTraceEvent::set_allocated_timestamp(::google::protobuf::Timestamp* timestamp) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(timestamp_);
  }
  if (timestamp) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast< ::google::protobuf::MessageLite*>(timestamp)->GetArena();
    if (message_arena != submessage_arena) {
      timestamp = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, timestamp, submessage_arena);
    }
    
  } else {
    
  }
  timestamp_ = timestamp;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.ChannelTraceEvent.timestamp)
}

// .grpc.channelz.v1.ChannelRef channel_ref = 4;
inline bool ChannelTraceEvent::has_channel_ref() const {
  return child_ref_case() == kChannelRef;
}
inline void ChannelTraceEvent::set_has_channel_ref() {
  _oneof_case_[0] = kChannelRef;
}
inline void ChannelTraceEvent::clear_channel_ref() {
  if (has_channel_ref()) {
    delete child_ref_.channel_ref_;
    clear_has_child_ref();
  }
}
inline ::grpc::channelz::v1::ChannelRef* ChannelTraceEvent::release_channel_ref() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.ChannelTraceEvent.channel_ref)
  if (has_channel_ref()) {
    clear_has_child_ref();
      ::grpc::channelz::v1::ChannelRef* temp = child_ref_.channel_ref_;
    child_ref_.channel_ref_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::grpc::channelz::v1::ChannelRef& ChannelTraceEvent::channel_ref() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.ChannelTraceEvent.channel_ref)
  return has_channel_ref()
      ? *child_ref_.channel_ref_
      : *reinterpret_cast< ::grpc::channelz::v1::ChannelRef*>(&::grpc::channelz::v1::_ChannelRef_default_instance_);
}
inline ::grpc::channelz::v1::ChannelRef* ChannelTraceEvent::mutable_channel_ref() {
  if (!has_channel_ref()) {
    clear_child_ref();
    set_has_channel_ref();
    child_ref_.channel_ref_ = new ::grpc::channelz::v1::ChannelRef;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.ChannelTraceEvent.channel_ref)
  return child_ref_.channel_ref_;
}

// .grpc.channelz.v1.SubchannelRef subchannel_ref = 5;
inline bool ChannelTraceEvent::has_subchannel_ref() const {
  return child_ref_case() == kSubchannelRef;
}
inline void ChannelTraceEvent::set_has_subchannel_ref() {
  _oneof_case_[0] = kSubchannelRef;
}
inline void ChannelTraceEvent::clear_subchannel_ref() {
  if (has_subchannel_ref()) {
    delete child_ref_.subchannel_ref_;
    clear_has_child_ref();
  }
}
inline ::grpc::channelz::v1::SubchannelRef* ChannelTraceEvent::release_subchannel_ref() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.ChannelTraceEvent.subchannel_ref)
  if (has_subchannel_ref()) {
    clear_has_child_ref();
      ::grpc::channelz::v1::SubchannelRef* temp = child_ref_.subchannel_ref_;
    child_ref_.subchannel_ref_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::grpc::channelz::v1::SubchannelRef& ChannelTraceEvent::subchannel_ref() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.ChannelTraceEvent.subchannel_ref)
  return has_subchannel_ref()
      ? *child_ref_.subchannel_ref_
      : *reinterpret_cast< ::grpc::channelz::v1::SubchannelRef*>(&::grpc::channelz::v1::_SubchannelRef_default_instance_);
}
inline ::grpc::channelz::v1::SubchannelRef* ChannelTraceEvent::mutable_subchannel_ref() {
  if (!has_subchannel_ref()) {
    clear_child_ref();
    set_has_subchannel_ref();
    child_ref_.subchannel_ref_ = new ::grpc::channelz::v1::SubchannelRef;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.ChannelTraceEvent.subchannel_ref)
  return child_ref_.subchannel_ref_;
}

inline bool ChannelTraceEvent::has_child_ref() const {
  return child_ref_case() != CHILD_REF_NOT_SET;
}
inline void ChannelTraceEvent::clear_has_child_ref() {
  _oneof_case_[0] = CHILD_REF_NOT_SET;
}
inline ChannelTraceEvent::ChildRefCase ChannelTraceEvent::child_ref_case() const {
  return ChannelTraceEvent::ChildRefCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// ChannelTrace

// int64 num_events_logged = 1;
inline void ChannelTrace::clear_num_events_logged() {
  num_events_logged_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ChannelTrace::num_events_logged() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.ChannelTrace.num_events_logged)
  return num_events_logged_;
}
inline void ChannelTrace::set_num_events_logged(::google::protobuf::int64 value) {
  
  num_events_logged_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.ChannelTrace.num_events_logged)
}

// .google.protobuf.Timestamp creation_timestamp = 2;
inline bool ChannelTrace::has_creation_timestamp() const {
  return this != internal_default_instance() && creation_timestamp_ != NULL;
}
inline const ::google::protobuf::Timestamp& ChannelTrace::creation_timestamp() const {
  const ::google::protobuf::Timestamp* p = creation_timestamp_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.ChannelTrace.creation_timestamp)
  return p != NULL ? *p : *reinterpret_cast<const ::google::protobuf::Timestamp*>(
      &::google::protobuf::_Timestamp_default_instance_);
}
inline ::google::protobuf::Timestamp* ChannelTrace::release_creation_timestamp() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.ChannelTrace.creation_timestamp)
  
  ::google::protobuf::Timestamp* temp = creation_timestamp_;
  creation_timestamp_ = NULL;
  return temp;
}
inline ::google::protobuf::Timestamp* ChannelTrace::mutable_creation_timestamp() {
  
  if (creation_timestamp_ == NULL) {
    creation_timestamp_ = new ::google::protobuf::Timestamp;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.ChannelTrace.creation_timestamp)
  return creation_timestamp_;
}
inline void ChannelTrace::set_allocated_creation_timestamp(::google::protobuf::Timestamp* creation_timestamp) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(creation_timestamp_);
  }
  if (creation_timestamp) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast< ::google::protobuf::MessageLite*>(creation_timestamp)->GetArena();
    if (message_arena != submessage_arena) {
      creation_timestamp = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, creation_timestamp, submessage_arena);
    }
    
  } else {
    
  }
  creation_timestamp_ = creation_timestamp;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.ChannelTrace.creation_timestamp)
}

// repeated .grpc.channelz.v1.ChannelTraceEvent events = 3;
inline int ChannelTrace::events_size() const {
  return events_.size();
}
inline void ChannelTrace::clear_events() {
  events_.Clear();
}
inline const ::grpc::channelz::v1::ChannelTraceEvent& ChannelTrace::events(int index) const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.ChannelTrace.events)
  return events_.Get(index);
}
inline ::grpc::channelz::v1::ChannelTraceEvent* ChannelTrace::mutable_events(int index) {
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.ChannelTrace.events)
  return events_.Mutable(index);
}
inline ::grpc::channelz::v1::ChannelTraceEvent* ChannelTrace::add_events() {
  // @@protoc_insertion_point(field_add:grpc.channelz.v1.ChannelTrace.events)
  return events_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::ChannelTraceEvent >*
ChannelTrace::mutable_events() {
  // @@protoc_insertion_point(field_mutable_list:grpc.channelz.v1.ChannelTrace.events)
  return &events_;
}
inline const ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::ChannelTraceEvent >&
ChannelTrace::events() const {
  // @@protoc_insertion_point(field_list:grpc.channelz.v1.ChannelTrace.events)
  return events_;
}

// -------------------------------------------------------------------

// ChannelRef

// int64 channel_id = 1;
inline void ChannelRef::clear_channel_id() {
  channel_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ChannelRef::channel_id() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.ChannelRef.channel_id)
  return channel_id_;
}
inline void ChannelRef::set_channel_id(::google::protobuf::int64 value) {
  
  channel_id_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.ChannelRef.channel_id)
}

// string name = 2;
inline void ChannelRef::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ChannelRef::name() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.ChannelRef.name)
  return name_.GetNoArena();
}
inline void ChannelRef::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.ChannelRef.name)
}
#if LANG_CXX11
inline void ChannelRef::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.channelz.v1.ChannelRef.name)
}
#endif
inline void ChannelRef::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.channelz.v1.ChannelRef.name)
}
inline void ChannelRef::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.channelz.v1.ChannelRef.name)
}
inline ::std::string* ChannelRef::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.ChannelRef.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ChannelRef::release_name() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.ChannelRef.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ChannelRef::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.ChannelRef.name)
}

// -------------------------------------------------------------------

// SubchannelRef

// int64 subchannel_id = 7;
inline void SubchannelRef::clear_subchannel_id() {
  subchannel_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SubchannelRef::subchannel_id() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SubchannelRef.subchannel_id)
  return subchannel_id_;
}
inline void SubchannelRef::set_subchannel_id(::google::protobuf::int64 value) {
  
  subchannel_id_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SubchannelRef.subchannel_id)
}

// string name = 8;
inline void SubchannelRef::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SubchannelRef::name() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SubchannelRef.name)
  return name_.GetNoArena();
}
inline void SubchannelRef::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SubchannelRef.name)
}
#if LANG_CXX11
inline void SubchannelRef::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.channelz.v1.SubchannelRef.name)
}
#endif
inline void SubchannelRef::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.channelz.v1.SubchannelRef.name)
}
inline void SubchannelRef::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.channelz.v1.SubchannelRef.name)
}
inline ::std::string* SubchannelRef::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.SubchannelRef.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SubchannelRef::release_name() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.SubchannelRef.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SubchannelRef::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.SubchannelRef.name)
}

// -------------------------------------------------------------------

// SocketRef

// int64 socket_id = 3;
inline void SocketRef::clear_socket_id() {
  socket_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SocketRef::socket_id() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketRef.socket_id)
  return socket_id_;
}
inline void SocketRef::set_socket_id(::google::protobuf::int64 value) {
  
  socket_id_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketRef.socket_id)
}

// string name = 4;
inline void SocketRef::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SocketRef::name() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketRef.name)
  return name_.GetNoArena();
}
inline void SocketRef::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketRef.name)
}
#if LANG_CXX11
inline void SocketRef::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.channelz.v1.SocketRef.name)
}
#endif
inline void SocketRef::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.channelz.v1.SocketRef.name)
}
inline void SocketRef::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.channelz.v1.SocketRef.name)
}
inline ::std::string* SocketRef::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.SocketRef.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SocketRef::release_name() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.SocketRef.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SocketRef::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.SocketRef.name)
}

// -------------------------------------------------------------------

// ServerRef

// int64 server_id = 5;
inline void ServerRef::clear_server_id() {
  server_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ServerRef::server_id() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.ServerRef.server_id)
  return server_id_;
}
inline void ServerRef::set_server_id(::google::protobuf::int64 value) {
  
  server_id_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.ServerRef.server_id)
}

// string name = 6;
inline void ServerRef::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ServerRef::name() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.ServerRef.name)
  return name_.GetNoArena();
}
inline void ServerRef::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.ServerRef.name)
}
#if LANG_CXX11
inline void ServerRef::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.channelz.v1.ServerRef.name)
}
#endif
inline void ServerRef::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.channelz.v1.ServerRef.name)
}
inline void ServerRef::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.channelz.v1.ServerRef.name)
}
inline ::std::string* ServerRef::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.ServerRef.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ServerRef::release_name() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.ServerRef.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ServerRef::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.ServerRef.name)
}

// -------------------------------------------------------------------

// Server

// .grpc.channelz.v1.ServerRef ref = 1;
inline bool Server::has_ref() const {
  return this != internal_default_instance() && ref_ != NULL;
}
inline void Server::clear_ref() {
  if (GetArenaNoVirtual() == NULL && ref_ != NULL) {
    delete ref_;
  }
  ref_ = NULL;
}
inline const ::grpc::channelz::v1::ServerRef& Server::ref() const {
  const ::grpc::channelz::v1::ServerRef* p = ref_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Server.ref)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::channelz::v1::ServerRef*>(
      &::grpc::channelz::v1::_ServerRef_default_instance_);
}
inline ::grpc::channelz::v1::ServerRef* Server::release_ref() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.Server.ref)
  
  ::grpc::channelz::v1::ServerRef* temp = ref_;
  ref_ = NULL;
  return temp;
}
inline ::grpc::channelz::v1::ServerRef* Server::mutable_ref() {
  
  if (ref_ == NULL) {
    ref_ = new ::grpc::channelz::v1::ServerRef;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Server.ref)
  return ref_;
}
inline void Server::set_allocated_ref(::grpc::channelz::v1::ServerRef* ref) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete ref_;
  }
  if (ref) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      ref = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, ref, submessage_arena);
    }
    
  } else {
    
  }
  ref_ = ref;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.Server.ref)
}

// .grpc.channelz.v1.ServerData data = 2;
inline bool Server::has_data() const {
  return this != internal_default_instance() && data_ != NULL;
}
inline void Server::clear_data() {
  if (GetArenaNoVirtual() == NULL && data_ != NULL) {
    delete data_;
  }
  data_ = NULL;
}
inline const ::grpc::channelz::v1::ServerData& Server::data() const {
  const ::grpc::channelz::v1::ServerData* p = data_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Server.data)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::channelz::v1::ServerData*>(
      &::grpc::channelz::v1::_ServerData_default_instance_);
}
inline ::grpc::channelz::v1::ServerData* Server::release_data() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.Server.data)
  
  ::grpc::channelz::v1::ServerData* temp = data_;
  data_ = NULL;
  return temp;
}
inline ::grpc::channelz::v1::ServerData* Server::mutable_data() {
  
  if (data_ == NULL) {
    data_ = new ::grpc::channelz::v1::ServerData;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Server.data)
  return data_;
}
inline void Server::set_allocated_data(::grpc::channelz::v1::ServerData* data) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete data_;
  }
  if (data) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      data = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, data, submessage_arena);
    }
    
  } else {
    
  }
  data_ = data;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.Server.data)
}

// repeated .grpc.channelz.v1.SocketRef listen_socket = 3;
inline int Server::listen_socket_size() const {
  return listen_socket_.size();
}
inline void Server::clear_listen_socket() {
  listen_socket_.Clear();
}
inline const ::grpc::channelz::v1::SocketRef& Server::listen_socket(int index) const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Server.listen_socket)
  return listen_socket_.Get(index);
}
inline ::grpc::channelz::v1::SocketRef* Server::mutable_listen_socket(int index) {
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Server.listen_socket)
  return listen_socket_.Mutable(index);
}
inline ::grpc::channelz::v1::SocketRef* Server::add_listen_socket() {
  // @@protoc_insertion_point(field_add:grpc.channelz.v1.Server.listen_socket)
  return listen_socket_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SocketRef >*
Server::mutable_listen_socket() {
  // @@protoc_insertion_point(field_mutable_list:grpc.channelz.v1.Server.listen_socket)
  return &listen_socket_;
}
inline const ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SocketRef >&
Server::listen_socket() const {
  // @@protoc_insertion_point(field_list:grpc.channelz.v1.Server.listen_socket)
  return listen_socket_;
}

// -------------------------------------------------------------------

// ServerData

// .grpc.channelz.v1.ChannelTrace trace = 1;
inline bool ServerData::has_trace() const {
  return this != internal_default_instance() && trace_ != NULL;
}
inline void ServerData::clear_trace() {
  if (GetArenaNoVirtual() == NULL && trace_ != NULL) {
    delete trace_;
  }
  trace_ = NULL;
}
inline const ::grpc::channelz::v1::ChannelTrace& ServerData::trace() const {
  const ::grpc::channelz::v1::ChannelTrace* p = trace_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.ServerData.trace)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::channelz::v1::ChannelTrace*>(
      &::grpc::channelz::v1::_ChannelTrace_default_instance_);
}
inline ::grpc::channelz::v1::ChannelTrace* ServerData::release_trace() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.ServerData.trace)
  
  ::grpc::channelz::v1::ChannelTrace* temp = trace_;
  trace_ = NULL;
  return temp;
}
inline ::grpc::channelz::v1::ChannelTrace* ServerData::mutable_trace() {
  
  if (trace_ == NULL) {
    trace_ = new ::grpc::channelz::v1::ChannelTrace;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.ServerData.trace)
  return trace_;
}
inline void ServerData::set_allocated_trace(::grpc::channelz::v1::ChannelTrace* trace) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete trace_;
  }
  if (trace) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      trace = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, trace, submessage_arena);
    }
    
  } else {
    
  }
  trace_ = trace;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.ServerData.trace)
}

// int64 calls_started = 2;
inline void ServerData::clear_calls_started() {
  calls_started_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ServerData::calls_started() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.ServerData.calls_started)
  return calls_started_;
}
inline void ServerData::set_calls_started(::google::protobuf::int64 value) {
  
  calls_started_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.ServerData.calls_started)
}

// int64 calls_succeeded = 3;
inline void ServerData::clear_calls_succeeded() {
  calls_succeeded_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ServerData::calls_succeeded() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.ServerData.calls_succeeded)
  return calls_succeeded_;
}
inline void ServerData::set_calls_succeeded(::google::protobuf::int64 value) {
  
  calls_succeeded_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.ServerData.calls_succeeded)
}

// int64 calls_failed = 4;
inline void ServerData::clear_calls_failed() {
  calls_failed_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ServerData::calls_failed() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.ServerData.calls_failed)
  return calls_failed_;
}
inline void ServerData::set_calls_failed(::google::protobuf::int64 value) {
  
  calls_failed_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.ServerData.calls_failed)
}

// .google.protobuf.Timestamp last_call_started_timestamp = 5;
inline bool ServerData::has_last_call_started_timestamp() const {
  return this != internal_default_instance() && last_call_started_timestamp_ != NULL;
}
inline const ::google::protobuf::Timestamp& ServerData::last_call_started_timestamp() const {
  const ::google::protobuf::Timestamp* p = last_call_started_timestamp_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.ServerData.last_call_started_timestamp)
  return p != NULL ? *p : *reinterpret_cast<const ::google::protobuf::Timestamp*>(
      &::google::protobuf::_Timestamp_default_instance_);
}
inline ::google::protobuf::Timestamp* ServerData::release_last_call_started_timestamp() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.ServerData.last_call_started_timestamp)
  
  ::google::protobuf::Timestamp* temp = last_call_started_timestamp_;
  last_call_started_timestamp_ = NULL;
  return temp;
}
inline ::google::protobuf::Timestamp* ServerData::mutable_last_call_started_timestamp() {
  
  if (last_call_started_timestamp_ == NULL) {
    last_call_started_timestamp_ = new ::google::protobuf::Timestamp;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.ServerData.last_call_started_timestamp)
  return last_call_started_timestamp_;
}
inline void ServerData::set_allocated_last_call_started_timestamp(::google::protobuf::Timestamp* last_call_started_timestamp) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(last_call_started_timestamp_);
  }
  if (last_call_started_timestamp) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast< ::google::protobuf::MessageLite*>(last_call_started_timestamp)->GetArena();
    if (message_arena != submessage_arena) {
      last_call_started_timestamp = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, last_call_started_timestamp, submessage_arena);
    }
    
  } else {
    
  }
  last_call_started_timestamp_ = last_call_started_timestamp;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.ServerData.last_call_started_timestamp)
}

// -------------------------------------------------------------------

// Socket

// .grpc.channelz.v1.SocketRef ref = 1;
inline bool Socket::has_ref() const {
  return this != internal_default_instance() && ref_ != NULL;
}
inline void Socket::clear_ref() {
  if (GetArenaNoVirtual() == NULL && ref_ != NULL) {
    delete ref_;
  }
  ref_ = NULL;
}
inline const ::grpc::channelz::v1::SocketRef& Socket::ref() const {
  const ::grpc::channelz::v1::SocketRef* p = ref_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Socket.ref)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::channelz::v1::SocketRef*>(
      &::grpc::channelz::v1::_SocketRef_default_instance_);
}
inline ::grpc::channelz::v1::SocketRef* Socket::release_ref() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.Socket.ref)
  
  ::grpc::channelz::v1::SocketRef* temp = ref_;
  ref_ = NULL;
  return temp;
}
inline ::grpc::channelz::v1::SocketRef* Socket::mutable_ref() {
  
  if (ref_ == NULL) {
    ref_ = new ::grpc::channelz::v1::SocketRef;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Socket.ref)
  return ref_;
}
inline void Socket::set_allocated_ref(::grpc::channelz::v1::SocketRef* ref) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete ref_;
  }
  if (ref) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      ref = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, ref, submessage_arena);
    }
    
  } else {
    
  }
  ref_ = ref;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.Socket.ref)
}

// .grpc.channelz.v1.SocketData data = 2;
inline bool Socket::has_data() const {
  return this != internal_default_instance() && data_ != NULL;
}
inline void Socket::clear_data() {
  if (GetArenaNoVirtual() == NULL && data_ != NULL) {
    delete data_;
  }
  data_ = NULL;
}
inline const ::grpc::channelz::v1::SocketData& Socket::data() const {
  const ::grpc::channelz::v1::SocketData* p = data_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Socket.data)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::channelz::v1::SocketData*>(
      &::grpc::channelz::v1::_SocketData_default_instance_);
}
inline ::grpc::channelz::v1::SocketData* Socket::release_data() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.Socket.data)
  
  ::grpc::channelz::v1::SocketData* temp = data_;
  data_ = NULL;
  return temp;
}
inline ::grpc::channelz::v1::SocketData* Socket::mutable_data() {
  
  if (data_ == NULL) {
    data_ = new ::grpc::channelz::v1::SocketData;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Socket.data)
  return data_;
}
inline void Socket::set_allocated_data(::grpc::channelz::v1::SocketData* data) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete data_;
  }
  if (data) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      data = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, data, submessage_arena);
    }
    
  } else {
    
  }
  data_ = data;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.Socket.data)
}

// .grpc.channelz.v1.Address local = 3;
inline bool Socket::has_local() const {
  return this != internal_default_instance() && local_ != NULL;
}
inline void Socket::clear_local() {
  if (GetArenaNoVirtual() == NULL && local_ != NULL) {
    delete local_;
  }
  local_ = NULL;
}
inline const ::grpc::channelz::v1::Address& Socket::local() const {
  const ::grpc::channelz::v1::Address* p = local_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Socket.local)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::channelz::v1::Address*>(
      &::grpc::channelz::v1::_Address_default_instance_);
}
inline ::grpc::channelz::v1::Address* Socket::release_local() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.Socket.local)
  
  ::grpc::channelz::v1::Address* temp = local_;
  local_ = NULL;
  return temp;
}
inline ::grpc::channelz::v1::Address* Socket::mutable_local() {
  
  if (local_ == NULL) {
    local_ = new ::grpc::channelz::v1::Address;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Socket.local)
  return local_;
}
inline void Socket::set_allocated_local(::grpc::channelz::v1::Address* local) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete local_;
  }
  if (local) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      local = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, local, submessage_arena);
    }
    
  } else {
    
  }
  local_ = local;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.Socket.local)
}

// .grpc.channelz.v1.Address remote = 4;
inline bool Socket::has_remote() const {
  return this != internal_default_instance() && remote_ != NULL;
}
inline void Socket::clear_remote() {
  if (GetArenaNoVirtual() == NULL && remote_ != NULL) {
    delete remote_;
  }
  remote_ = NULL;
}
inline const ::grpc::channelz::v1::Address& Socket::remote() const {
  const ::grpc::channelz::v1::Address* p = remote_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Socket.remote)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::channelz::v1::Address*>(
      &::grpc::channelz::v1::_Address_default_instance_);
}
inline ::grpc::channelz::v1::Address* Socket::release_remote() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.Socket.remote)
  
  ::grpc::channelz::v1::Address* temp = remote_;
  remote_ = NULL;
  return temp;
}
inline ::grpc::channelz::v1::Address* Socket::mutable_remote() {
  
  if (remote_ == NULL) {
    remote_ = new ::grpc::channelz::v1::Address;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Socket.remote)
  return remote_;
}
inline void Socket::set_allocated_remote(::grpc::channelz::v1::Address* remote) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete remote_;
  }
  if (remote) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      remote = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, remote, submessage_arena);
    }
    
  } else {
    
  }
  remote_ = remote;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.Socket.remote)
}

// .grpc.channelz.v1.Security security = 5;
inline bool Socket::has_security() const {
  return this != internal_default_instance() && security_ != NULL;
}
inline void Socket::clear_security() {
  if (GetArenaNoVirtual() == NULL && security_ != NULL) {
    delete security_;
  }
  security_ = NULL;
}
inline const ::grpc::channelz::v1::Security& Socket::security() const {
  const ::grpc::channelz::v1::Security* p = security_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Socket.security)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::channelz::v1::Security*>(
      &::grpc::channelz::v1::_Security_default_instance_);
}
inline ::grpc::channelz::v1::Security* Socket::release_security() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.Socket.security)
  
  ::grpc::channelz::v1::Security* temp = security_;
  security_ = NULL;
  return temp;
}
inline ::grpc::channelz::v1::Security* Socket::mutable_security() {
  
  if (security_ == NULL) {
    security_ = new ::grpc::channelz::v1::Security;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Socket.security)
  return security_;
}
inline void Socket::set_allocated_security(::grpc::channelz::v1::Security* security) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete security_;
  }
  if (security) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      security = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, security, submessage_arena);
    }
    
  } else {
    
  }
  security_ = security;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.Socket.security)
}

// string remote_name = 6;
inline void Socket::clear_remote_name() {
  remote_name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Socket::remote_name() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Socket.remote_name)
  return remote_name_.GetNoArena();
}
inline void Socket::set_remote_name(const ::std::string& value) {
  
  remote_name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.Socket.remote_name)
}
#if LANG_CXX11
inline void Socket::set_remote_name(::std::string&& value) {
  
  remote_name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.channelz.v1.Socket.remote_name)
}
#endif
inline void Socket::set_remote_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  remote_name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.channelz.v1.Socket.remote_name)
}
inline void Socket::set_remote_name(const char* value, size_t size) {
  
  remote_name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.channelz.v1.Socket.remote_name)
}
inline ::std::string* Socket::mutable_remote_name() {
  
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Socket.remote_name)
  return remote_name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Socket::release_remote_name() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.Socket.remote_name)
  
  return remote_name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Socket::set_allocated_remote_name(::std::string* remote_name) {
  if (remote_name != NULL) {
    
  } else {
    
  }
  remote_name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), remote_name);
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.Socket.remote_name)
}

// -------------------------------------------------------------------

// SocketData

// int64 streams_started = 1;
inline void SocketData::clear_streams_started() {
  streams_started_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SocketData::streams_started() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketData.streams_started)
  return streams_started_;
}
inline void SocketData::set_streams_started(::google::protobuf::int64 value) {
  
  streams_started_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketData.streams_started)
}

// int64 streams_succeeded = 2;
inline void SocketData::clear_streams_succeeded() {
  streams_succeeded_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SocketData::streams_succeeded() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketData.streams_succeeded)
  return streams_succeeded_;
}
inline void SocketData::set_streams_succeeded(::google::protobuf::int64 value) {
  
  streams_succeeded_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketData.streams_succeeded)
}

// int64 streams_failed = 3;
inline void SocketData::clear_streams_failed() {
  streams_failed_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SocketData::streams_failed() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketData.streams_failed)
  return streams_failed_;
}
inline void SocketData::set_streams_failed(::google::protobuf::int64 value) {
  
  streams_failed_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketData.streams_failed)
}

// int64 messages_sent = 4;
inline void SocketData::clear_messages_sent() {
  messages_sent_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SocketData::messages_sent() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketData.messages_sent)
  return messages_sent_;
}
inline void SocketData::set_messages_sent(::google::protobuf::int64 value) {
  
  messages_sent_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketData.messages_sent)
}

// int64 messages_received = 5;
inline void SocketData::clear_messages_received() {
  messages_received_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SocketData::messages_received() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketData.messages_received)
  return messages_received_;
}
inline void SocketData::set_messages_received(::google::protobuf::int64 value) {
  
  messages_received_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketData.messages_received)
}

// int64 keep_alives_sent = 6;
inline void SocketData::clear_keep_alives_sent() {
  keep_alives_sent_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 SocketData::keep_alives_sent() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketData.keep_alives_sent)
  return keep_alives_sent_;
}
inline void SocketData::set_keep_alives_sent(::google::protobuf::int64 value) {
  
  keep_alives_sent_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketData.keep_alives_sent)
}

// .google.protobuf.Timestamp last_local_stream_created_timestamp = 7;
inline bool SocketData::has_last_local_stream_created_timestamp() const {
  return this != internal_default_instance() && last_local_stream_created_timestamp_ != NULL;
}
inline const ::google::protobuf::Timestamp& SocketData::last_local_stream_created_timestamp() const {
  const ::google::protobuf::Timestamp* p = last_local_stream_created_timestamp_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketData.last_local_stream_created_timestamp)
  return p != NULL ? *p : *reinterpret_cast<const ::google::protobuf::Timestamp*>(
      &::google::protobuf::_Timestamp_default_instance_);
}
inline ::google::protobuf::Timestamp* SocketData::release_last_local_stream_created_timestamp() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.SocketData.last_local_stream_created_timestamp)
  
  ::google::protobuf::Timestamp* temp = last_local_stream_created_timestamp_;
  last_local_stream_created_timestamp_ = NULL;
  return temp;
}
inline ::google::protobuf::Timestamp* SocketData::mutable_last_local_stream_created_timestamp() {
  
  if (last_local_stream_created_timestamp_ == NULL) {
    last_local_stream_created_timestamp_ = new ::google::protobuf::Timestamp;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.SocketData.last_local_stream_created_timestamp)
  return last_local_stream_created_timestamp_;
}
inline void SocketData::set_allocated_last_local_stream_created_timestamp(::google::protobuf::Timestamp* last_local_stream_created_timestamp) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(last_local_stream_created_timestamp_);
  }
  if (last_local_stream_created_timestamp) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast< ::google::protobuf::MessageLite*>(last_local_stream_created_timestamp)->GetArena();
    if (message_arena != submessage_arena) {
      last_local_stream_created_timestamp = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, last_local_stream_created_timestamp, submessage_arena);
    }
    
  } else {
    
  }
  last_local_stream_created_timestamp_ = last_local_stream_created_timestamp;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.SocketData.last_local_stream_created_timestamp)
}

// .google.protobuf.Timestamp last_remote_stream_created_timestamp = 8;
inline bool SocketData::has_last_remote_stream_created_timestamp() const {
  return this != internal_default_instance() && last_remote_stream_created_timestamp_ != NULL;
}
inline const ::google::protobuf::Timestamp& SocketData::last_remote_stream_created_timestamp() const {
  const ::google::protobuf::Timestamp* p = last_remote_stream_created_timestamp_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketData.last_remote_stream_created_timestamp)
  return p != NULL ? *p : *reinterpret_cast<const ::google::protobuf::Timestamp*>(
      &::google::protobuf::_Timestamp_default_instance_);
}
inline ::google::protobuf::Timestamp* SocketData::release_last_remote_stream_created_timestamp() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.SocketData.last_remote_stream_created_timestamp)
  
  ::google::protobuf::Timestamp* temp = last_remote_stream_created_timestamp_;
  last_remote_stream_created_timestamp_ = NULL;
  return temp;
}
inline ::google::protobuf::Timestamp* SocketData::mutable_last_remote_stream_created_timestamp() {
  
  if (last_remote_stream_created_timestamp_ == NULL) {
    last_remote_stream_created_timestamp_ = new ::google::protobuf::Timestamp;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.SocketData.last_remote_stream_created_timestamp)
  return last_remote_stream_created_timestamp_;
}
inline void SocketData::set_allocated_last_remote_stream_created_timestamp(::google::protobuf::Timestamp* last_remote_stream_created_timestamp) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(last_remote_stream_created_timestamp_);
  }
  if (last_remote_stream_created_timestamp) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast< ::google::protobuf::MessageLite*>(last_remote_stream_created_timestamp)->GetArena();
    if (message_arena != submessage_arena) {
      last_remote_stream_created_timestamp = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, last_remote_stream_created_timestamp, submessage_arena);
    }
    
  } else {
    
  }
  last_remote_stream_created_timestamp_ = last_remote_stream_created_timestamp;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.SocketData.last_remote_stream_created_timestamp)
}

// .google.protobuf.Timestamp last_message_sent_timestamp = 9;
inline bool SocketData::has_last_message_sent_timestamp() const {
  return this != internal_default_instance() && last_message_sent_timestamp_ != NULL;
}
inline const ::google::protobuf::Timestamp& SocketData::last_message_sent_timestamp() const {
  const ::google::protobuf::Timestamp* p = last_message_sent_timestamp_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketData.last_message_sent_timestamp)
  return p != NULL ? *p : *reinterpret_cast<const ::google::protobuf::Timestamp*>(
      &::google::protobuf::_Timestamp_default_instance_);
}
inline ::google::protobuf::Timestamp* SocketData::release_last_message_sent_timestamp() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.SocketData.last_message_sent_timestamp)
  
  ::google::protobuf::Timestamp* temp = last_message_sent_timestamp_;
  last_message_sent_timestamp_ = NULL;
  return temp;
}
inline ::google::protobuf::Timestamp* SocketData::mutable_last_message_sent_timestamp() {
  
  if (last_message_sent_timestamp_ == NULL) {
    last_message_sent_timestamp_ = new ::google::protobuf::Timestamp;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.SocketData.last_message_sent_timestamp)
  return last_message_sent_timestamp_;
}
inline void SocketData::set_allocated_last_message_sent_timestamp(::google::protobuf::Timestamp* last_message_sent_timestamp) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(last_message_sent_timestamp_);
  }
  if (last_message_sent_timestamp) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast< ::google::protobuf::MessageLite*>(last_message_sent_timestamp)->GetArena();
    if (message_arena != submessage_arena) {
      last_message_sent_timestamp = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, last_message_sent_timestamp, submessage_arena);
    }
    
  } else {
    
  }
  last_message_sent_timestamp_ = last_message_sent_timestamp;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.SocketData.last_message_sent_timestamp)
}

// .google.protobuf.Timestamp last_message_received_timestamp = 10;
inline bool SocketData::has_last_message_received_timestamp() const {
  return this != internal_default_instance() && last_message_received_timestamp_ != NULL;
}
inline const ::google::protobuf::Timestamp& SocketData::last_message_received_timestamp() const {
  const ::google::protobuf::Timestamp* p = last_message_received_timestamp_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketData.last_message_received_timestamp)
  return p != NULL ? *p : *reinterpret_cast<const ::google::protobuf::Timestamp*>(
      &::google::protobuf::_Timestamp_default_instance_);
}
inline ::google::protobuf::Timestamp* SocketData::release_last_message_received_timestamp() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.SocketData.last_message_received_timestamp)
  
  ::google::protobuf::Timestamp* temp = last_message_received_timestamp_;
  last_message_received_timestamp_ = NULL;
  return temp;
}
inline ::google::protobuf::Timestamp* SocketData::mutable_last_message_received_timestamp() {
  
  if (last_message_received_timestamp_ == NULL) {
    last_message_received_timestamp_ = new ::google::protobuf::Timestamp;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.SocketData.last_message_received_timestamp)
  return last_message_received_timestamp_;
}
inline void SocketData::set_allocated_last_message_received_timestamp(::google::protobuf::Timestamp* last_message_received_timestamp) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(last_message_received_timestamp_);
  }
  if (last_message_received_timestamp) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast< ::google::protobuf::MessageLite*>(last_message_received_timestamp)->GetArena();
    if (message_arena != submessage_arena) {
      last_message_received_timestamp = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, last_message_received_timestamp, submessage_arena);
    }
    
  } else {
    
  }
  last_message_received_timestamp_ = last_message_received_timestamp;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.SocketData.last_message_received_timestamp)
}

// .google.protobuf.Int64Value local_flow_control_window = 11;
inline bool SocketData::has_local_flow_control_window() const {
  return this != internal_default_instance() && local_flow_control_window_ != NULL;
}
inline const ::google::protobuf::Int64Value& SocketData::local_flow_control_window() const {
  const ::google::protobuf::Int64Value* p = local_flow_control_window_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketData.local_flow_control_window)
  return p != NULL ? *p : *reinterpret_cast<const ::google::protobuf::Int64Value*>(
      &::google::protobuf::_Int64Value_default_instance_);
}
inline ::google::protobuf::Int64Value* SocketData::release_local_flow_control_window() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.SocketData.local_flow_control_window)
  
  ::google::protobuf::Int64Value* temp = local_flow_control_window_;
  local_flow_control_window_ = NULL;
  return temp;
}
inline ::google::protobuf::Int64Value* SocketData::mutable_local_flow_control_window() {
  
  if (local_flow_control_window_ == NULL) {
    local_flow_control_window_ = new ::google::protobuf::Int64Value;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.SocketData.local_flow_control_window)
  return local_flow_control_window_;
}
inline void SocketData::set_allocated_local_flow_control_window(::google::protobuf::Int64Value* local_flow_control_window) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(local_flow_control_window_);
  }
  if (local_flow_control_window) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast< ::google::protobuf::MessageLite*>(local_flow_control_window)->GetArena();
    if (message_arena != submessage_arena) {
      local_flow_control_window = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, local_flow_control_window, submessage_arena);
    }
    
  } else {
    
  }
  local_flow_control_window_ = local_flow_control_window;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.SocketData.local_flow_control_window)
}

// .google.protobuf.Int64Value remote_flow_control_window = 12;
inline bool SocketData::has_remote_flow_control_window() const {
  return this != internal_default_instance() && remote_flow_control_window_ != NULL;
}
inline const ::google::protobuf::Int64Value& SocketData::remote_flow_control_window() const {
  const ::google::protobuf::Int64Value* p = remote_flow_control_window_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketData.remote_flow_control_window)
  return p != NULL ? *p : *reinterpret_cast<const ::google::protobuf::Int64Value*>(
      &::google::protobuf::_Int64Value_default_instance_);
}
inline ::google::protobuf::Int64Value* SocketData::release_remote_flow_control_window() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.SocketData.remote_flow_control_window)
  
  ::google::protobuf::Int64Value* temp = remote_flow_control_window_;
  remote_flow_control_window_ = NULL;
  return temp;
}
inline ::google::protobuf::Int64Value* SocketData::mutable_remote_flow_control_window() {
  
  if (remote_flow_control_window_ == NULL) {
    remote_flow_control_window_ = new ::google::protobuf::Int64Value;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.SocketData.remote_flow_control_window)
  return remote_flow_control_window_;
}
inline void SocketData::set_allocated_remote_flow_control_window(::google::protobuf::Int64Value* remote_flow_control_window) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(remote_flow_control_window_);
  }
  if (remote_flow_control_window) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast< ::google::protobuf::MessageLite*>(remote_flow_control_window)->GetArena();
    if (message_arena != submessage_arena) {
      remote_flow_control_window = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, remote_flow_control_window, submessage_arena);
    }
    
  } else {
    
  }
  remote_flow_control_window_ = remote_flow_control_window;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.SocketData.remote_flow_control_window)
}

// repeated .grpc.channelz.v1.SocketOption option = 13;
inline int SocketData::option_size() const {
  return option_.size();
}
inline void SocketData::clear_option() {
  option_.Clear();
}
inline const ::grpc::channelz::v1::SocketOption& SocketData::option(int index) const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketData.option)
  return option_.Get(index);
}
inline ::grpc::channelz::v1::SocketOption* SocketData::mutable_option(int index) {
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.SocketData.option)
  return option_.Mutable(index);
}
inline ::grpc::channelz::v1::SocketOption* SocketData::add_option() {
  // @@protoc_insertion_point(field_add:grpc.channelz.v1.SocketData.option)
  return option_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SocketOption >*
SocketData::mutable_option() {
  // @@protoc_insertion_point(field_mutable_list:grpc.channelz.v1.SocketData.option)
  return &option_;
}
inline const ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SocketOption >&
SocketData::option() const {
  // @@protoc_insertion_point(field_list:grpc.channelz.v1.SocketData.option)
  return option_;
}

// -------------------------------------------------------------------

// Address_TcpIpAddress

// bytes ip_address = 1;
inline void Address_TcpIpAddress::clear_ip_address() {
  ip_address_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Address_TcpIpAddress::ip_address() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Address.TcpIpAddress.ip_address)
  return ip_address_.GetNoArena();
}
inline void Address_TcpIpAddress::set_ip_address(const ::std::string& value) {
  
  ip_address_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.Address.TcpIpAddress.ip_address)
}
#if LANG_CXX11
inline void Address_TcpIpAddress::set_ip_address(::std::string&& value) {
  
  ip_address_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.channelz.v1.Address.TcpIpAddress.ip_address)
}
#endif
inline void Address_TcpIpAddress::set_ip_address(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  ip_address_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.channelz.v1.Address.TcpIpAddress.ip_address)
}
inline void Address_TcpIpAddress::set_ip_address(const void* value, size_t size) {
  
  ip_address_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.channelz.v1.Address.TcpIpAddress.ip_address)
}
inline ::std::string* Address_TcpIpAddress::mutable_ip_address() {
  
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Address.TcpIpAddress.ip_address)
  return ip_address_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Address_TcpIpAddress::release_ip_address() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.Address.TcpIpAddress.ip_address)
  
  return ip_address_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Address_TcpIpAddress::set_allocated_ip_address(::std::string* ip_address) {
  if (ip_address != NULL) {
    
  } else {
    
  }
  ip_address_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ip_address);
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.Address.TcpIpAddress.ip_address)
}

// int32 port = 2;
inline void Address_TcpIpAddress::clear_port() {
  port_ = 0;
}
inline ::google::protobuf::int32 Address_TcpIpAddress::port() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Address.TcpIpAddress.port)
  return port_;
}
inline void Address_TcpIpAddress::set_port(::google::protobuf::int32 value) {
  
  port_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.Address.TcpIpAddress.port)
}

// -------------------------------------------------------------------

// Address_UdsAddress

// string filename = 1;
inline void Address_UdsAddress::clear_filename() {
  filename_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Address_UdsAddress::filename() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Address.UdsAddress.filename)
  return filename_.GetNoArena();
}
inline void Address_UdsAddress::set_filename(const ::std::string& value) {
  
  filename_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.Address.UdsAddress.filename)
}
#if LANG_CXX11
inline void Address_UdsAddress::set_filename(::std::string&& value) {
  
  filename_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.channelz.v1.Address.UdsAddress.filename)
}
#endif
inline void Address_UdsAddress::set_filename(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  filename_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.channelz.v1.Address.UdsAddress.filename)
}
inline void Address_UdsAddress::set_filename(const char* value, size_t size) {
  
  filename_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.channelz.v1.Address.UdsAddress.filename)
}
inline ::std::string* Address_UdsAddress::mutable_filename() {
  
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Address.UdsAddress.filename)
  return filename_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Address_UdsAddress::release_filename() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.Address.UdsAddress.filename)
  
  return filename_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Address_UdsAddress::set_allocated_filename(::std::string* filename) {
  if (filename != NULL) {
    
  } else {
    
  }
  filename_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), filename);
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.Address.UdsAddress.filename)
}

// -------------------------------------------------------------------

// Address_OtherAddress

// string name = 1;
inline void Address_OtherAddress::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Address_OtherAddress::name() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Address.OtherAddress.name)
  return name_.GetNoArena();
}
inline void Address_OtherAddress::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.Address.OtherAddress.name)
}
#if LANG_CXX11
inline void Address_OtherAddress::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.channelz.v1.Address.OtherAddress.name)
}
#endif
inline void Address_OtherAddress::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.channelz.v1.Address.OtherAddress.name)
}
inline void Address_OtherAddress::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.channelz.v1.Address.OtherAddress.name)
}
inline ::std::string* Address_OtherAddress::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Address.OtherAddress.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Address_OtherAddress::release_name() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.Address.OtherAddress.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Address_OtherAddress::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.Address.OtherAddress.name)
}

// .google.protobuf.Any value = 2;
inline bool Address_OtherAddress::has_value() const {
  return this != internal_default_instance() && value_ != NULL;
}
inline const ::google::protobuf::Any& Address_OtherAddress::value() const {
  const ::google::protobuf::Any* p = value_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Address.OtherAddress.value)
  return p != NULL ? *p : *reinterpret_cast<const ::google::protobuf::Any*>(
      &::google::protobuf::_Any_default_instance_);
}
inline ::google::protobuf::Any* Address_OtherAddress::release_value() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.Address.OtherAddress.value)
  
  ::google::protobuf::Any* temp = value_;
  value_ = NULL;
  return temp;
}
inline ::google::protobuf::Any* Address_OtherAddress::mutable_value() {
  
  if (value_ == NULL) {
    value_ = new ::google::protobuf::Any;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Address.OtherAddress.value)
  return value_;
}
inline void Address_OtherAddress::set_allocated_value(::google::protobuf::Any* value) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(value_);
  }
  if (value) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      value = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, value, submessage_arena);
    }
    
  } else {
    
  }
  value_ = value;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.Address.OtherAddress.value)
}

// -------------------------------------------------------------------

// Address

// .grpc.channelz.v1.Address.TcpIpAddress tcpip_address = 1;
inline bool Address::has_tcpip_address() const {
  return address_case() == kTcpipAddress;
}
inline void Address::set_has_tcpip_address() {
  _oneof_case_[0] = kTcpipAddress;
}
inline void Address::clear_tcpip_address() {
  if (has_tcpip_address()) {
    delete address_.tcpip_address_;
    clear_has_address();
  }
}
inline ::grpc::channelz::v1::Address_TcpIpAddress* Address::release_tcpip_address() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.Address.tcpip_address)
  if (has_tcpip_address()) {
    clear_has_address();
      ::grpc::channelz::v1::Address_TcpIpAddress* temp = address_.tcpip_address_;
    address_.tcpip_address_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::grpc::channelz::v1::Address_TcpIpAddress& Address::tcpip_address() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Address.tcpip_address)
  return has_tcpip_address()
      ? *address_.tcpip_address_
      : *reinterpret_cast< ::grpc::channelz::v1::Address_TcpIpAddress*>(&::grpc::channelz::v1::_Address_TcpIpAddress_default_instance_);
}
inline ::grpc::channelz::v1::Address_TcpIpAddress* Address::mutable_tcpip_address() {
  if (!has_tcpip_address()) {
    clear_address();
    set_has_tcpip_address();
    address_.tcpip_address_ = new ::grpc::channelz::v1::Address_TcpIpAddress;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Address.tcpip_address)
  return address_.tcpip_address_;
}

// .grpc.channelz.v1.Address.UdsAddress uds_address = 2;
inline bool Address::has_uds_address() const {
  return address_case() == kUdsAddress;
}
inline void Address::set_has_uds_address() {
  _oneof_case_[0] = kUdsAddress;
}
inline void Address::clear_uds_address() {
  if (has_uds_address()) {
    delete address_.uds_address_;
    clear_has_address();
  }
}
inline ::grpc::channelz::v1::Address_UdsAddress* Address::release_uds_address() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.Address.uds_address)
  if (has_uds_address()) {
    clear_has_address();
      ::grpc::channelz::v1::Address_UdsAddress* temp = address_.uds_address_;
    address_.uds_address_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::grpc::channelz::v1::Address_UdsAddress& Address::uds_address() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Address.uds_address)
  return has_uds_address()
      ? *address_.uds_address_
      : *reinterpret_cast< ::grpc::channelz::v1::Address_UdsAddress*>(&::grpc::channelz::v1::_Address_UdsAddress_default_instance_);
}
inline ::grpc::channelz::v1::Address_UdsAddress* Address::mutable_uds_address() {
  if (!has_uds_address()) {
    clear_address();
    set_has_uds_address();
    address_.uds_address_ = new ::grpc::channelz::v1::Address_UdsAddress;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Address.uds_address)
  return address_.uds_address_;
}

// .grpc.channelz.v1.Address.OtherAddress other_address = 3;
inline bool Address::has_other_address() const {
  return address_case() == kOtherAddress;
}
inline void Address::set_has_other_address() {
  _oneof_case_[0] = kOtherAddress;
}
inline void Address::clear_other_address() {
  if (has_other_address()) {
    delete address_.other_address_;
    clear_has_address();
  }
}
inline ::grpc::channelz::v1::Address_OtherAddress* Address::release_other_address() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.Address.other_address)
  if (has_other_address()) {
    clear_has_address();
      ::grpc::channelz::v1::Address_OtherAddress* temp = address_.other_address_;
    address_.other_address_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::grpc::channelz::v1::Address_OtherAddress& Address::other_address() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Address.other_address)
  return has_other_address()
      ? *address_.other_address_
      : *reinterpret_cast< ::grpc::channelz::v1::Address_OtherAddress*>(&::grpc::channelz::v1::_Address_OtherAddress_default_instance_);
}
inline ::grpc::channelz::v1::Address_OtherAddress* Address::mutable_other_address() {
  if (!has_other_address()) {
    clear_address();
    set_has_other_address();
    address_.other_address_ = new ::grpc::channelz::v1::Address_OtherAddress;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Address.other_address)
  return address_.other_address_;
}

inline bool Address::has_address() const {
  return address_case() != ADDRESS_NOT_SET;
}
inline void Address::clear_has_address() {
  _oneof_case_[0] = ADDRESS_NOT_SET;
}
inline Address::AddressCase Address::address_case() const {
  return Address::AddressCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// Security_Tls

// string standard_name = 1;
inline bool Security_Tls::has_standard_name() const {
  return cipher_suite_case() == kStandardName;
}
inline void Security_Tls::set_has_standard_name() {
  _oneof_case_[0] = kStandardName;
}
inline void Security_Tls::clear_standard_name() {
  if (has_standard_name()) {
    cipher_suite_.standard_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    clear_has_cipher_suite();
  }
}
inline const ::std::string& Security_Tls::standard_name() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Security.Tls.standard_name)
  if (has_standard_name()) {
    return cipher_suite_.standard_name_.GetNoArena();
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void Security_Tls::set_standard_name(const ::std::string& value) {
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.Security.Tls.standard_name)
  if (!has_standard_name()) {
    clear_cipher_suite();
    set_has_standard_name();
    cipher_suite_.standard_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  cipher_suite_.standard_name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.Security.Tls.standard_name)
}
#if LANG_CXX11
inline void Security_Tls::set_standard_name(::std::string&& value) {
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.Security.Tls.standard_name)
  if (!has_standard_name()) {
    clear_cipher_suite();
    set_has_standard_name();
    cipher_suite_.standard_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  cipher_suite_.standard_name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.channelz.v1.Security.Tls.standard_name)
}
#endif
inline void Security_Tls::set_standard_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  if (!has_standard_name()) {
    clear_cipher_suite();
    set_has_standard_name();
    cipher_suite_.standard_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  cipher_suite_.standard_name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.channelz.v1.Security.Tls.standard_name)
}
inline void Security_Tls::set_standard_name(const char* value, size_t size) {
  if (!has_standard_name()) {
    clear_cipher_suite();
    set_has_standard_name();
    cipher_suite_.standard_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  cipher_suite_.standard_name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.channelz.v1.Security.Tls.standard_name)
}
inline ::std::string* Security_Tls::mutable_standard_name() {
  if (!has_standard_name()) {
    clear_cipher_suite();
    set_has_standard_name();
    cipher_suite_.standard_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Security.Tls.standard_name)
  return cipher_suite_.standard_name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Security_Tls::release_standard_name() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.Security.Tls.standard_name)
  if (has_standard_name()) {
    clear_has_cipher_suite();
    return cipher_suite_.standard_name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  } else {
    return NULL;
  }
}
inline void Security_Tls::set_allocated_standard_name(::std::string* standard_name) {
  if (!has_standard_name()) {
    cipher_suite_.standard_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_cipher_suite();
  if (standard_name != NULL) {
    set_has_standard_name();
    cipher_suite_.standard_name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        standard_name);
  }
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.Security.Tls.standard_name)
}

// string other_name = 2;
inline bool Security_Tls::has_other_name() const {
  return cipher_suite_case() == kOtherName;
}
inline void Security_Tls::set_has_other_name() {
  _oneof_case_[0] = kOtherName;
}
inline void Security_Tls::clear_other_name() {
  if (has_other_name()) {
    cipher_suite_.other_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    clear_has_cipher_suite();
  }
}
inline const ::std::string& Security_Tls::other_name() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Security.Tls.other_name)
  if (has_other_name()) {
    return cipher_suite_.other_name_.GetNoArena();
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void Security_Tls::set_other_name(const ::std::string& value) {
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.Security.Tls.other_name)
  if (!has_other_name()) {
    clear_cipher_suite();
    set_has_other_name();
    cipher_suite_.other_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  cipher_suite_.other_name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.Security.Tls.other_name)
}
#if LANG_CXX11
inline void Security_Tls::set_other_name(::std::string&& value) {
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.Security.Tls.other_name)
  if (!has_other_name()) {
    clear_cipher_suite();
    set_has_other_name();
    cipher_suite_.other_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  cipher_suite_.other_name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.channelz.v1.Security.Tls.other_name)
}
#endif
inline void Security_Tls::set_other_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  if (!has_other_name()) {
    clear_cipher_suite();
    set_has_other_name();
    cipher_suite_.other_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  cipher_suite_.other_name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.channelz.v1.Security.Tls.other_name)
}
inline void Security_Tls::set_other_name(const char* value, size_t size) {
  if (!has_other_name()) {
    clear_cipher_suite();
    set_has_other_name();
    cipher_suite_.other_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  cipher_suite_.other_name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.channelz.v1.Security.Tls.other_name)
}
inline ::std::string* Security_Tls::mutable_other_name() {
  if (!has_other_name()) {
    clear_cipher_suite();
    set_has_other_name();
    cipher_suite_.other_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Security.Tls.other_name)
  return cipher_suite_.other_name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Security_Tls::release_other_name() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.Security.Tls.other_name)
  if (has_other_name()) {
    clear_has_cipher_suite();
    return cipher_suite_.other_name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  } else {
    return NULL;
  }
}
inline void Security_Tls::set_allocated_other_name(::std::string* other_name) {
  if (!has_other_name()) {
    cipher_suite_.other_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_cipher_suite();
  if (other_name != NULL) {
    set_has_other_name();
    cipher_suite_.other_name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        other_name);
  }
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.Security.Tls.other_name)
}

// bytes local_certificate = 3;
inline void Security_Tls::clear_local_certificate() {
  local_certificate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Security_Tls::local_certificate() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Security.Tls.local_certificate)
  return local_certificate_.GetNoArena();
}
inline void Security_Tls::set_local_certificate(const ::std::string& value) {
  
  local_certificate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.Security.Tls.local_certificate)
}
#if LANG_CXX11
inline void Security_Tls::set_local_certificate(::std::string&& value) {
  
  local_certificate_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.channelz.v1.Security.Tls.local_certificate)
}
#endif
inline void Security_Tls::set_local_certificate(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  local_certificate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.channelz.v1.Security.Tls.local_certificate)
}
inline void Security_Tls::set_local_certificate(const void* value, size_t size) {
  
  local_certificate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.channelz.v1.Security.Tls.local_certificate)
}
inline ::std::string* Security_Tls::mutable_local_certificate() {
  
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Security.Tls.local_certificate)
  return local_certificate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Security_Tls::release_local_certificate() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.Security.Tls.local_certificate)
  
  return local_certificate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Security_Tls::set_allocated_local_certificate(::std::string* local_certificate) {
  if (local_certificate != NULL) {
    
  } else {
    
  }
  local_certificate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), local_certificate);
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.Security.Tls.local_certificate)
}

// bytes remote_certificate = 4;
inline void Security_Tls::clear_remote_certificate() {
  remote_certificate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Security_Tls::remote_certificate() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Security.Tls.remote_certificate)
  return remote_certificate_.GetNoArena();
}
inline void Security_Tls::set_remote_certificate(const ::std::string& value) {
  
  remote_certificate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.Security.Tls.remote_certificate)
}
#if LANG_CXX11
inline void Security_Tls::set_remote_certificate(::std::string&& value) {
  
  remote_certificate_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.channelz.v1.Security.Tls.remote_certificate)
}
#endif
inline void Security_Tls::set_remote_certificate(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  remote_certificate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.channelz.v1.Security.Tls.remote_certificate)
}
inline void Security_Tls::set_remote_certificate(const void* value, size_t size) {
  
  remote_certificate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.channelz.v1.Security.Tls.remote_certificate)
}
inline ::std::string* Security_Tls::mutable_remote_certificate() {
  
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Security.Tls.remote_certificate)
  return remote_certificate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Security_Tls::release_remote_certificate() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.Security.Tls.remote_certificate)
  
  return remote_certificate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Security_Tls::set_allocated_remote_certificate(::std::string* remote_certificate) {
  if (remote_certificate != NULL) {
    
  } else {
    
  }
  remote_certificate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), remote_certificate);
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.Security.Tls.remote_certificate)
}

inline bool Security_Tls::has_cipher_suite() const {
  return cipher_suite_case() != CIPHER_SUITE_NOT_SET;
}
inline void Security_Tls::clear_has_cipher_suite() {
  _oneof_case_[0] = CIPHER_SUITE_NOT_SET;
}
inline Security_Tls::CipherSuiteCase Security_Tls::cipher_suite_case() const {
  return Security_Tls::CipherSuiteCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// Security_OtherSecurity

// string name = 1;
inline void Security_OtherSecurity::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Security_OtherSecurity::name() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Security.OtherSecurity.name)
  return name_.GetNoArena();
}
inline void Security_OtherSecurity::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.Security.OtherSecurity.name)
}
#if LANG_CXX11
inline void Security_OtherSecurity::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.channelz.v1.Security.OtherSecurity.name)
}
#endif
inline void Security_OtherSecurity::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.channelz.v1.Security.OtherSecurity.name)
}
inline void Security_OtherSecurity::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.channelz.v1.Security.OtherSecurity.name)
}
inline ::std::string* Security_OtherSecurity::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Security.OtherSecurity.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Security_OtherSecurity::release_name() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.Security.OtherSecurity.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Security_OtherSecurity::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.Security.OtherSecurity.name)
}

// .google.protobuf.Any value = 2;
inline bool Security_OtherSecurity::has_value() const {
  return this != internal_default_instance() && value_ != NULL;
}
inline const ::google::protobuf::Any& Security_OtherSecurity::value() const {
  const ::google::protobuf::Any* p = value_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Security.OtherSecurity.value)
  return p != NULL ? *p : *reinterpret_cast<const ::google::protobuf::Any*>(
      &::google::protobuf::_Any_default_instance_);
}
inline ::google::protobuf::Any* Security_OtherSecurity::release_value() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.Security.OtherSecurity.value)
  
  ::google::protobuf::Any* temp = value_;
  value_ = NULL;
  return temp;
}
inline ::google::protobuf::Any* Security_OtherSecurity::mutable_value() {
  
  if (value_ == NULL) {
    value_ = new ::google::protobuf::Any;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Security.OtherSecurity.value)
  return value_;
}
inline void Security_OtherSecurity::set_allocated_value(::google::protobuf::Any* value) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(value_);
  }
  if (value) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      value = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, value, submessage_arena);
    }
    
  } else {
    
  }
  value_ = value;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.Security.OtherSecurity.value)
}

// -------------------------------------------------------------------

// Security

// .grpc.channelz.v1.Security.Tls tls = 1;
inline bool Security::has_tls() const {
  return model_case() == kTls;
}
inline void Security::set_has_tls() {
  _oneof_case_[0] = kTls;
}
inline void Security::clear_tls() {
  if (has_tls()) {
    delete model_.tls_;
    clear_has_model();
  }
}
inline ::grpc::channelz::v1::Security_Tls* Security::release_tls() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.Security.tls)
  if (has_tls()) {
    clear_has_model();
      ::grpc::channelz::v1::Security_Tls* temp = model_.tls_;
    model_.tls_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::grpc::channelz::v1::Security_Tls& Security::tls() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Security.tls)
  return has_tls()
      ? *model_.tls_
      : *reinterpret_cast< ::grpc::channelz::v1::Security_Tls*>(&::grpc::channelz::v1::_Security_Tls_default_instance_);
}
inline ::grpc::channelz::v1::Security_Tls* Security::mutable_tls() {
  if (!has_tls()) {
    clear_model();
    set_has_tls();
    model_.tls_ = new ::grpc::channelz::v1::Security_Tls;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Security.tls)
  return model_.tls_;
}

// .grpc.channelz.v1.Security.OtherSecurity other = 2;
inline bool Security::has_other() const {
  return model_case() == kOther;
}
inline void Security::set_has_other() {
  _oneof_case_[0] = kOther;
}
inline void Security::clear_other() {
  if (has_other()) {
    delete model_.other_;
    clear_has_model();
  }
}
inline ::grpc::channelz::v1::Security_OtherSecurity* Security::release_other() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.Security.other)
  if (has_other()) {
    clear_has_model();
      ::grpc::channelz::v1::Security_OtherSecurity* temp = model_.other_;
    model_.other_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::grpc::channelz::v1::Security_OtherSecurity& Security::other() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.Security.other)
  return has_other()
      ? *model_.other_
      : *reinterpret_cast< ::grpc::channelz::v1::Security_OtherSecurity*>(&::grpc::channelz::v1::_Security_OtherSecurity_default_instance_);
}
inline ::grpc::channelz::v1::Security_OtherSecurity* Security::mutable_other() {
  if (!has_other()) {
    clear_model();
    set_has_other();
    model_.other_ = new ::grpc::channelz::v1::Security_OtherSecurity;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.Security.other)
  return model_.other_;
}

inline bool Security::has_model() const {
  return model_case() != MODEL_NOT_SET;
}
inline void Security::clear_has_model() {
  _oneof_case_[0] = MODEL_NOT_SET;
}
inline Security::ModelCase Security::model_case() const {
  return Security::ModelCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// SocketOption

// string name = 1;
inline void SocketOption::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SocketOption::name() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOption.name)
  return name_.GetNoArena();
}
inline void SocketOption::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOption.name)
}
#if LANG_CXX11
inline void SocketOption::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.channelz.v1.SocketOption.name)
}
#endif
inline void SocketOption::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.channelz.v1.SocketOption.name)
}
inline void SocketOption::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.channelz.v1.SocketOption.name)
}
inline ::std::string* SocketOption::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.SocketOption.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SocketOption::release_name() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.SocketOption.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SocketOption::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.SocketOption.name)
}

// string value = 2;
inline void SocketOption::clear_value() {
  value_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SocketOption::value() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOption.value)
  return value_.GetNoArena();
}
inline void SocketOption::set_value(const ::std::string& value) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOption.value)
}
#if LANG_CXX11
inline void SocketOption::set_value(::std::string&& value) {
  
  value_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.channelz.v1.SocketOption.value)
}
#endif
inline void SocketOption::set_value(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.channelz.v1.SocketOption.value)
}
inline void SocketOption::set_value(const char* value, size_t size) {
  
  value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.channelz.v1.SocketOption.value)
}
inline ::std::string* SocketOption::mutable_value() {
  
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.SocketOption.value)
  return value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SocketOption::release_value() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.SocketOption.value)
  
  return value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SocketOption::set_allocated_value(::std::string* value) {
  if (value != NULL) {
    
  } else {
    
  }
  value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.SocketOption.value)
}

// .google.protobuf.Any additional = 3;
inline bool SocketOption::has_additional() const {
  return this != internal_default_instance() && additional_ != NULL;
}
inline const ::google::protobuf::Any& SocketOption::additional() const {
  const ::google::protobuf::Any* p = additional_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOption.additional)
  return p != NULL ? *p : *reinterpret_cast<const ::google::protobuf::Any*>(
      &::google::protobuf::_Any_default_instance_);
}
inline ::google::protobuf::Any* SocketOption::release_additional() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.SocketOption.additional)
  
  ::google::protobuf::Any* temp = additional_;
  additional_ = NULL;
  return temp;
}
inline ::google::protobuf::Any* SocketOption::mutable_additional() {
  
  if (additional_ == NULL) {
    additional_ = new ::google::protobuf::Any;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.SocketOption.additional)
  return additional_;
}
inline void SocketOption::set_allocated_additional(::google::protobuf::Any* additional) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(additional_);
  }
  if (additional) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      additional = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, additional, submessage_arena);
    }
    
  } else {
    
  }
  additional_ = additional;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.SocketOption.additional)
}

// -------------------------------------------------------------------

// SocketOptionTimeout

// .google.protobuf.Duration duration = 1;
inline bool SocketOptionTimeout::has_duration() const {
  return this != internal_default_instance() && duration_ != NULL;
}
inline const ::google::protobuf::Duration& SocketOptionTimeout::duration() const {
  const ::google::protobuf::Duration* p = duration_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTimeout.duration)
  return p != NULL ? *p : *reinterpret_cast<const ::google::protobuf::Duration*>(
      &::google::protobuf::_Duration_default_instance_);
}
inline ::google::protobuf::Duration* SocketOptionTimeout::release_duration() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.SocketOptionTimeout.duration)
  
  ::google::protobuf::Duration* temp = duration_;
  duration_ = NULL;
  return temp;
}
inline ::google::protobuf::Duration* SocketOptionTimeout::mutable_duration() {
  
  if (duration_ == NULL) {
    duration_ = new ::google::protobuf::Duration;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.SocketOptionTimeout.duration)
  return duration_;
}
inline void SocketOptionTimeout::set_allocated_duration(::google::protobuf::Duration* duration) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(duration_);
  }
  if (duration) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast< ::google::protobuf::MessageLite*>(duration)->GetArena();
    if (message_arena != submessage_arena) {
      duration = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, duration, submessage_arena);
    }
    
  } else {
    
  }
  duration_ = duration;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.SocketOptionTimeout.duration)
}

// -------------------------------------------------------------------

// SocketOptionLinger

// bool active = 1;
inline void SocketOptionLinger::clear_active() {
  active_ = false;
}
inline bool SocketOptionLinger::active() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionLinger.active)
  return active_;
}
inline void SocketOptionLinger::set_active(bool value) {
  
  active_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionLinger.active)
}

// .google.protobuf.Duration duration = 2;
inline bool SocketOptionLinger::has_duration() const {
  return this != internal_default_instance() && duration_ != NULL;
}
inline const ::google::protobuf::Duration& SocketOptionLinger::duration() const {
  const ::google::protobuf::Duration* p = duration_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionLinger.duration)
  return p != NULL ? *p : *reinterpret_cast<const ::google::protobuf::Duration*>(
      &::google::protobuf::_Duration_default_instance_);
}
inline ::google::protobuf::Duration* SocketOptionLinger::release_duration() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.SocketOptionLinger.duration)
  
  ::google::protobuf::Duration* temp = duration_;
  duration_ = NULL;
  return temp;
}
inline ::google::protobuf::Duration* SocketOptionLinger::mutable_duration() {
  
  if (duration_ == NULL) {
    duration_ = new ::google::protobuf::Duration;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.SocketOptionLinger.duration)
  return duration_;
}
inline void SocketOptionLinger::set_allocated_duration(::google::protobuf::Duration* duration) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(duration_);
  }
  if (duration) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast< ::google::protobuf::MessageLite*>(duration)->GetArena();
    if (message_arena != submessage_arena) {
      duration = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, duration, submessage_arena);
    }
    
  } else {
    
  }
  duration_ = duration;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.SocketOptionLinger.duration)
}

// -------------------------------------------------------------------

// SocketOptionTcpInfo

// uint32 tcpi_state = 1;
inline void SocketOptionTcpInfo::clear_tcpi_state() {
  tcpi_state_ = 0u;
}
inline ::google::protobuf::uint32 SocketOptionTcpInfo::tcpi_state() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_state)
  return tcpi_state_;
}
inline void SocketOptionTcpInfo::set_tcpi_state(::google::protobuf::uint32 value) {
  
  tcpi_state_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_state)
}

// uint32 tcpi_ca_state = 2;
inline void SocketOptionTcpInfo::clear_tcpi_ca_state() {
  tcpi_ca_state_ = 0u;
}
inline ::google::protobuf::uint32 SocketOptionTcpInfo::tcpi_ca_state() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_ca_state)
  return tcpi_ca_state_;
}
inline void SocketOptionTcpInfo::set_tcpi_ca_state(::google::protobuf::uint32 value) {
  
  tcpi_ca_state_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_ca_state)
}

// uint32 tcpi_retransmits = 3;
inline void SocketOptionTcpInfo::clear_tcpi_retransmits() {
  tcpi_retransmits_ = 0u;
}
inline ::google::protobuf::uint32 SocketOptionTcpInfo::tcpi_retransmits() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_retransmits)
  return tcpi_retransmits_;
}
inline void SocketOptionTcpInfo::set_tcpi_retransmits(::google::protobuf::uint32 value) {
  
  tcpi_retransmits_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_retransmits)
}

// uint32 tcpi_probes = 4;
inline void SocketOptionTcpInfo::clear_tcpi_probes() {
  tcpi_probes_ = 0u;
}
inline ::google::protobuf::uint32 SocketOptionTcpInfo::tcpi_probes() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_probes)
  return tcpi_probes_;
}
inline void SocketOptionTcpInfo::set_tcpi_probes(::google::protobuf::uint32 value) {
  
  tcpi_probes_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_probes)
}

// uint32 tcpi_backoff = 5;
inline void SocketOptionTcpInfo::clear_tcpi_backoff() {
  tcpi_backoff_ = 0u;
}
inline ::google::protobuf::uint32 SocketOptionTcpInfo::tcpi_backoff() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_backoff)
  return tcpi_backoff_;
}
inline void SocketOptionTcpInfo::set_tcpi_backoff(::google::protobuf::uint32 value) {
  
  tcpi_backoff_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_backoff)
}

// uint32 tcpi_options = 6;
inline void SocketOptionTcpInfo::clear_tcpi_options() {
  tcpi_options_ = 0u;
}
inline ::google::protobuf::uint32 SocketOptionTcpInfo::tcpi_options() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_options)
  return tcpi_options_;
}
inline void SocketOptionTcpInfo::set_tcpi_options(::google::protobuf::uint32 value) {
  
  tcpi_options_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_options)
}

// uint32 tcpi_snd_wscale = 7;
inline void SocketOptionTcpInfo::clear_tcpi_snd_wscale() {
  tcpi_snd_wscale_ = 0u;
}
inline ::google::protobuf::uint32 SocketOptionTcpInfo::tcpi_snd_wscale() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_snd_wscale)
  return tcpi_snd_wscale_;
}
inline void SocketOptionTcpInfo::set_tcpi_snd_wscale(::google::protobuf::uint32 value) {
  
  tcpi_snd_wscale_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_snd_wscale)
}

// uint32 tcpi_rcv_wscale = 8;
inline void SocketOptionTcpInfo::clear_tcpi_rcv_wscale() {
  tcpi_rcv_wscale_ = 0u;
}
inline ::google::protobuf::uint32 SocketOptionTcpInfo::tcpi_rcv_wscale() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_rcv_wscale)
  return tcpi_rcv_wscale_;
}
inline void SocketOptionTcpInfo::set_tcpi_rcv_wscale(::google::protobuf::uint32 value) {
  
  tcpi_rcv_wscale_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_rcv_wscale)
}

// uint32 tcpi_rto = 9;
inline void SocketOptionTcpInfo::clear_tcpi_rto() {
  tcpi_rto_ = 0u;
}
inline ::google::protobuf::uint32 SocketOptionTcpInfo::tcpi_rto() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_rto)
  return tcpi_rto_;
}
inline void SocketOptionTcpInfo::set_tcpi_rto(::google::protobuf::uint32 value) {
  
  tcpi_rto_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_rto)
}

// uint32 tcpi_ato = 10;
inline void SocketOptionTcpInfo::clear_tcpi_ato() {
  tcpi_ato_ = 0u;
}
inline ::google::protobuf::uint32 SocketOptionTcpInfo::tcpi_ato() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_ato)
  return tcpi_ato_;
}
inline void SocketOptionTcpInfo::set_tcpi_ato(::google::protobuf::uint32 value) {
  
  tcpi_ato_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_ato)
}

// uint32 tcpi_snd_mss = 11;
inline void SocketOptionTcpInfo::clear_tcpi_snd_mss() {
  tcpi_snd_mss_ = 0u;
}
inline ::google::protobuf::uint32 SocketOptionTcpInfo::tcpi_snd_mss() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_snd_mss)
  return tcpi_snd_mss_;
}
inline void SocketOptionTcpInfo::set_tcpi_snd_mss(::google::protobuf::uint32 value) {
  
  tcpi_snd_mss_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_snd_mss)
}

// uint32 tcpi_rcv_mss = 12;
inline void SocketOptionTcpInfo::clear_tcpi_rcv_mss() {
  tcpi_rcv_mss_ = 0u;
}
inline ::google::protobuf::uint32 SocketOptionTcpInfo::tcpi_rcv_mss() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_rcv_mss)
  return tcpi_rcv_mss_;
}
inline void SocketOptionTcpInfo::set_tcpi_rcv_mss(::google::protobuf::uint32 value) {
  
  tcpi_rcv_mss_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_rcv_mss)
}

// uint32 tcpi_unacked = 13;
inline void SocketOptionTcpInfo::clear_tcpi_unacked() {
  tcpi_unacked_ = 0u;
}
inline ::google::protobuf::uint32 SocketOptionTcpInfo::tcpi_unacked() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_unacked)
  return tcpi_unacked_;
}
inline void SocketOptionTcpInfo::set_tcpi_unacked(::google::protobuf::uint32 value) {
  
  tcpi_unacked_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_unacked)
}

// uint32 tcpi_sacked = 14;
inline void SocketOptionTcpInfo::clear_tcpi_sacked() {
  tcpi_sacked_ = 0u;
}
inline ::google::protobuf::uint32 SocketOptionTcpInfo::tcpi_sacked() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_sacked)
  return tcpi_sacked_;
}
inline void SocketOptionTcpInfo::set_tcpi_sacked(::google::protobuf::uint32 value) {
  
  tcpi_sacked_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_sacked)
}

// uint32 tcpi_lost = 15;
inline void SocketOptionTcpInfo::clear_tcpi_lost() {
  tcpi_lost_ = 0u;
}
inline ::google::protobuf::uint32 SocketOptionTcpInfo::tcpi_lost() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_lost)
  return tcpi_lost_;
}
inline void SocketOptionTcpInfo::set_tcpi_lost(::google::protobuf::uint32 value) {
  
  tcpi_lost_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_lost)
}

// uint32 tcpi_retrans = 16;
inline void SocketOptionTcpInfo::clear_tcpi_retrans() {
  tcpi_retrans_ = 0u;
}
inline ::google::protobuf::uint32 SocketOptionTcpInfo::tcpi_retrans() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_retrans)
  return tcpi_retrans_;
}
inline void SocketOptionTcpInfo::set_tcpi_retrans(::google::protobuf::uint32 value) {
  
  tcpi_retrans_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_retrans)
}

// uint32 tcpi_fackets = 17;
inline void SocketOptionTcpInfo::clear_tcpi_fackets() {
  tcpi_fackets_ = 0u;
}
inline ::google::protobuf::uint32 SocketOptionTcpInfo::tcpi_fackets() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_fackets)
  return tcpi_fackets_;
}
inline void SocketOptionTcpInfo::set_tcpi_fackets(::google::protobuf::uint32 value) {
  
  tcpi_fackets_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_fackets)
}

// uint32 tcpi_last_data_sent = 18;
inline void SocketOptionTcpInfo::clear_tcpi_last_data_sent() {
  tcpi_last_data_sent_ = 0u;
}
inline ::google::protobuf::uint32 SocketOptionTcpInfo::tcpi_last_data_sent() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_last_data_sent)
  return tcpi_last_data_sent_;
}
inline void SocketOptionTcpInfo::set_tcpi_last_data_sent(::google::protobuf::uint32 value) {
  
  tcpi_last_data_sent_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_last_data_sent)
}

// uint32 tcpi_last_ack_sent = 19;
inline void SocketOptionTcpInfo::clear_tcpi_last_ack_sent() {
  tcpi_last_ack_sent_ = 0u;
}
inline ::google::protobuf::uint32 SocketOptionTcpInfo::tcpi_last_ack_sent() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_last_ack_sent)
  return tcpi_last_ack_sent_;
}
inline void SocketOptionTcpInfo::set_tcpi_last_ack_sent(::google::protobuf::uint32 value) {
  
  tcpi_last_ack_sent_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_last_ack_sent)
}

// uint32 tcpi_last_data_recv = 20;
inline void SocketOptionTcpInfo::clear_tcpi_last_data_recv() {
  tcpi_last_data_recv_ = 0u;
}
inline ::google::protobuf::uint32 SocketOptionTcpInfo::tcpi_last_data_recv() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_last_data_recv)
  return tcpi_last_data_recv_;
}
inline void SocketOptionTcpInfo::set_tcpi_last_data_recv(::google::protobuf::uint32 value) {
  
  tcpi_last_data_recv_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_last_data_recv)
}

// uint32 tcpi_last_ack_recv = 21;
inline void SocketOptionTcpInfo::clear_tcpi_last_ack_recv() {
  tcpi_last_ack_recv_ = 0u;
}
inline ::google::protobuf::uint32 SocketOptionTcpInfo::tcpi_last_ack_recv() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_last_ack_recv)
  return tcpi_last_ack_recv_;
}
inline void SocketOptionTcpInfo::set_tcpi_last_ack_recv(::google::protobuf::uint32 value) {
  
  tcpi_last_ack_recv_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_last_ack_recv)
}

// uint32 tcpi_pmtu = 22;
inline void SocketOptionTcpInfo::clear_tcpi_pmtu() {
  tcpi_pmtu_ = 0u;
}
inline ::google::protobuf::uint32 SocketOptionTcpInfo::tcpi_pmtu() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_pmtu)
  return tcpi_pmtu_;
}
inline void SocketOptionTcpInfo::set_tcpi_pmtu(::google::protobuf::uint32 value) {
  
  tcpi_pmtu_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_pmtu)
}

// uint32 tcpi_rcv_ssthresh = 23;
inline void SocketOptionTcpInfo::clear_tcpi_rcv_ssthresh() {
  tcpi_rcv_ssthresh_ = 0u;
}
inline ::google::protobuf::uint32 SocketOptionTcpInfo::tcpi_rcv_ssthresh() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_rcv_ssthresh)
  return tcpi_rcv_ssthresh_;
}
inline void SocketOptionTcpInfo::set_tcpi_rcv_ssthresh(::google::protobuf::uint32 value) {
  
  tcpi_rcv_ssthresh_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_rcv_ssthresh)
}

// uint32 tcpi_rtt = 24;
inline void SocketOptionTcpInfo::clear_tcpi_rtt() {
  tcpi_rtt_ = 0u;
}
inline ::google::protobuf::uint32 SocketOptionTcpInfo::tcpi_rtt() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_rtt)
  return tcpi_rtt_;
}
inline void SocketOptionTcpInfo::set_tcpi_rtt(::google::protobuf::uint32 value) {
  
  tcpi_rtt_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_rtt)
}

// uint32 tcpi_rttvar = 25;
inline void SocketOptionTcpInfo::clear_tcpi_rttvar() {
  tcpi_rttvar_ = 0u;
}
inline ::google::protobuf::uint32 SocketOptionTcpInfo::tcpi_rttvar() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_rttvar)
  return tcpi_rttvar_;
}
inline void SocketOptionTcpInfo::set_tcpi_rttvar(::google::protobuf::uint32 value) {
  
  tcpi_rttvar_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_rttvar)
}

// uint32 tcpi_snd_ssthresh = 26;
inline void SocketOptionTcpInfo::clear_tcpi_snd_ssthresh() {
  tcpi_snd_ssthresh_ = 0u;
}
inline ::google::protobuf::uint32 SocketOptionTcpInfo::tcpi_snd_ssthresh() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_snd_ssthresh)
  return tcpi_snd_ssthresh_;
}
inline void SocketOptionTcpInfo::set_tcpi_snd_ssthresh(::google::protobuf::uint32 value) {
  
  tcpi_snd_ssthresh_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_snd_ssthresh)
}

// uint32 tcpi_snd_cwnd = 27;
inline void SocketOptionTcpInfo::clear_tcpi_snd_cwnd() {
  tcpi_snd_cwnd_ = 0u;
}
inline ::google::protobuf::uint32 SocketOptionTcpInfo::tcpi_snd_cwnd() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_snd_cwnd)
  return tcpi_snd_cwnd_;
}
inline void SocketOptionTcpInfo::set_tcpi_snd_cwnd(::google::protobuf::uint32 value) {
  
  tcpi_snd_cwnd_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_snd_cwnd)
}

// uint32 tcpi_advmss = 28;
inline void SocketOptionTcpInfo::clear_tcpi_advmss() {
  tcpi_advmss_ = 0u;
}
inline ::google::protobuf::uint32 SocketOptionTcpInfo::tcpi_advmss() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_advmss)
  return tcpi_advmss_;
}
inline void SocketOptionTcpInfo::set_tcpi_advmss(::google::protobuf::uint32 value) {
  
  tcpi_advmss_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_advmss)
}

// uint32 tcpi_reordering = 29;
inline void SocketOptionTcpInfo::clear_tcpi_reordering() {
  tcpi_reordering_ = 0u;
}
inline ::google::protobuf::uint32 SocketOptionTcpInfo::tcpi_reordering() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_reordering)
  return tcpi_reordering_;
}
inline void SocketOptionTcpInfo::set_tcpi_reordering(::google::protobuf::uint32 value) {
  
  tcpi_reordering_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.SocketOptionTcpInfo.tcpi_reordering)
}

// -------------------------------------------------------------------

// GetTopChannelsRequest

// int64 start_channel_id = 1;
inline void GetTopChannelsRequest::clear_start_channel_id() {
  start_channel_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GetTopChannelsRequest::start_channel_id() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.GetTopChannelsRequest.start_channel_id)
  return start_channel_id_;
}
inline void GetTopChannelsRequest::set_start_channel_id(::google::protobuf::int64 value) {
  
  start_channel_id_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.GetTopChannelsRequest.start_channel_id)
}

// -------------------------------------------------------------------

// GetTopChannelsResponse

// repeated .grpc.channelz.v1.Channel channel = 1;
inline int GetTopChannelsResponse::channel_size() const {
  return channel_.size();
}
inline void GetTopChannelsResponse::clear_channel() {
  channel_.Clear();
}
inline const ::grpc::channelz::v1::Channel& GetTopChannelsResponse::channel(int index) const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.GetTopChannelsResponse.channel)
  return channel_.Get(index);
}
inline ::grpc::channelz::v1::Channel* GetTopChannelsResponse::mutable_channel(int index) {
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.GetTopChannelsResponse.channel)
  return channel_.Mutable(index);
}
inline ::grpc::channelz::v1::Channel* GetTopChannelsResponse::add_channel() {
  // @@protoc_insertion_point(field_add:grpc.channelz.v1.GetTopChannelsResponse.channel)
  return channel_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::Channel >*
GetTopChannelsResponse::mutable_channel() {
  // @@protoc_insertion_point(field_mutable_list:grpc.channelz.v1.GetTopChannelsResponse.channel)
  return &channel_;
}
inline const ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::Channel >&
GetTopChannelsResponse::channel() const {
  // @@protoc_insertion_point(field_list:grpc.channelz.v1.GetTopChannelsResponse.channel)
  return channel_;
}

// bool end = 2;
inline void GetTopChannelsResponse::clear_end() {
  end_ = false;
}
inline bool GetTopChannelsResponse::end() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.GetTopChannelsResponse.end)
  return end_;
}
inline void GetTopChannelsResponse::set_end(bool value) {
  
  end_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.GetTopChannelsResponse.end)
}

// -------------------------------------------------------------------

// GetServersRequest

// int64 start_server_id = 1;
inline void GetServersRequest::clear_start_server_id() {
  start_server_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GetServersRequest::start_server_id() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.GetServersRequest.start_server_id)
  return start_server_id_;
}
inline void GetServersRequest::set_start_server_id(::google::protobuf::int64 value) {
  
  start_server_id_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.GetServersRequest.start_server_id)
}

// -------------------------------------------------------------------

// GetServersResponse

// repeated .grpc.channelz.v1.Server server = 1;
inline int GetServersResponse::server_size() const {
  return server_.size();
}
inline void GetServersResponse::clear_server() {
  server_.Clear();
}
inline const ::grpc::channelz::v1::Server& GetServersResponse::server(int index) const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.GetServersResponse.server)
  return server_.Get(index);
}
inline ::grpc::channelz::v1::Server* GetServersResponse::mutable_server(int index) {
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.GetServersResponse.server)
  return server_.Mutable(index);
}
inline ::grpc::channelz::v1::Server* GetServersResponse::add_server() {
  // @@protoc_insertion_point(field_add:grpc.channelz.v1.GetServersResponse.server)
  return server_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::Server >*
GetServersResponse::mutable_server() {
  // @@protoc_insertion_point(field_mutable_list:grpc.channelz.v1.GetServersResponse.server)
  return &server_;
}
inline const ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::Server >&
GetServersResponse::server() const {
  // @@protoc_insertion_point(field_list:grpc.channelz.v1.GetServersResponse.server)
  return server_;
}

// bool end = 2;
inline void GetServersResponse::clear_end() {
  end_ = false;
}
inline bool GetServersResponse::end() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.GetServersResponse.end)
  return end_;
}
inline void GetServersResponse::set_end(bool value) {
  
  end_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.GetServersResponse.end)
}

// -------------------------------------------------------------------

// GetServerSocketsRequest

// int64 server_id = 1;
inline void GetServerSocketsRequest::clear_server_id() {
  server_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GetServerSocketsRequest::server_id() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.GetServerSocketsRequest.server_id)
  return server_id_;
}
inline void GetServerSocketsRequest::set_server_id(::google::protobuf::int64 value) {
  
  server_id_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.GetServerSocketsRequest.server_id)
}

// int64 start_socket_id = 2;
inline void GetServerSocketsRequest::clear_start_socket_id() {
  start_socket_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GetServerSocketsRequest::start_socket_id() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.GetServerSocketsRequest.start_socket_id)
  return start_socket_id_;
}
inline void GetServerSocketsRequest::set_start_socket_id(::google::protobuf::int64 value) {
  
  start_socket_id_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.GetServerSocketsRequest.start_socket_id)
}

// -------------------------------------------------------------------

// GetServerSocketsResponse

// repeated .grpc.channelz.v1.SocketRef socket_ref = 1;
inline int GetServerSocketsResponse::socket_ref_size() const {
  return socket_ref_.size();
}
inline void GetServerSocketsResponse::clear_socket_ref() {
  socket_ref_.Clear();
}
inline const ::grpc::channelz::v1::SocketRef& GetServerSocketsResponse::socket_ref(int index) const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.GetServerSocketsResponse.socket_ref)
  return socket_ref_.Get(index);
}
inline ::grpc::channelz::v1::SocketRef* GetServerSocketsResponse::mutable_socket_ref(int index) {
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.GetServerSocketsResponse.socket_ref)
  return socket_ref_.Mutable(index);
}
inline ::grpc::channelz::v1::SocketRef* GetServerSocketsResponse::add_socket_ref() {
  // @@protoc_insertion_point(field_add:grpc.channelz.v1.GetServerSocketsResponse.socket_ref)
  return socket_ref_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SocketRef >*
GetServerSocketsResponse::mutable_socket_ref() {
  // @@protoc_insertion_point(field_mutable_list:grpc.channelz.v1.GetServerSocketsResponse.socket_ref)
  return &socket_ref_;
}
inline const ::google::protobuf::RepeatedPtrField< ::grpc::channelz::v1::SocketRef >&
GetServerSocketsResponse::socket_ref() const {
  // @@protoc_insertion_point(field_list:grpc.channelz.v1.GetServerSocketsResponse.socket_ref)
  return socket_ref_;
}

// bool end = 2;
inline void GetServerSocketsResponse::clear_end() {
  end_ = false;
}
inline bool GetServerSocketsResponse::end() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.GetServerSocketsResponse.end)
  return end_;
}
inline void GetServerSocketsResponse::set_end(bool value) {
  
  end_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.GetServerSocketsResponse.end)
}

// -------------------------------------------------------------------

// GetChannelRequest

// int64 channel_id = 1;
inline void GetChannelRequest::clear_channel_id() {
  channel_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GetChannelRequest::channel_id() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.GetChannelRequest.channel_id)
  return channel_id_;
}
inline void GetChannelRequest::set_channel_id(::google::protobuf::int64 value) {
  
  channel_id_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.GetChannelRequest.channel_id)
}

// -------------------------------------------------------------------

// GetChannelResponse

// .grpc.channelz.v1.Channel channel = 1;
inline bool GetChannelResponse::has_channel() const {
  return this != internal_default_instance() && channel_ != NULL;
}
inline void GetChannelResponse::clear_channel() {
  if (GetArenaNoVirtual() == NULL && channel_ != NULL) {
    delete channel_;
  }
  channel_ = NULL;
}
inline const ::grpc::channelz::v1::Channel& GetChannelResponse::channel() const {
  const ::grpc::channelz::v1::Channel* p = channel_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.GetChannelResponse.channel)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::channelz::v1::Channel*>(
      &::grpc::channelz::v1::_Channel_default_instance_);
}
inline ::grpc::channelz::v1::Channel* GetChannelResponse::release_channel() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.GetChannelResponse.channel)
  
  ::grpc::channelz::v1::Channel* temp = channel_;
  channel_ = NULL;
  return temp;
}
inline ::grpc::channelz::v1::Channel* GetChannelResponse::mutable_channel() {
  
  if (channel_ == NULL) {
    channel_ = new ::grpc::channelz::v1::Channel;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.GetChannelResponse.channel)
  return channel_;
}
inline void GetChannelResponse::set_allocated_channel(::grpc::channelz::v1::Channel* channel) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete channel_;
  }
  if (channel) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      channel = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, channel, submessage_arena);
    }
    
  } else {
    
  }
  channel_ = channel;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.GetChannelResponse.channel)
}

// -------------------------------------------------------------------

// GetSubchannelRequest

// int64 subchannel_id = 1;
inline void GetSubchannelRequest::clear_subchannel_id() {
  subchannel_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GetSubchannelRequest::subchannel_id() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.GetSubchannelRequest.subchannel_id)
  return subchannel_id_;
}
inline void GetSubchannelRequest::set_subchannel_id(::google::protobuf::int64 value) {
  
  subchannel_id_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.GetSubchannelRequest.subchannel_id)
}

// -------------------------------------------------------------------

// GetSubchannelResponse

// .grpc.channelz.v1.Subchannel subchannel = 1;
inline bool GetSubchannelResponse::has_subchannel() const {
  return this != internal_default_instance() && subchannel_ != NULL;
}
inline void GetSubchannelResponse::clear_subchannel() {
  if (GetArenaNoVirtual() == NULL && subchannel_ != NULL) {
    delete subchannel_;
  }
  subchannel_ = NULL;
}
inline const ::grpc::channelz::v1::Subchannel& GetSubchannelResponse::subchannel() const {
  const ::grpc::channelz::v1::Subchannel* p = subchannel_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.GetSubchannelResponse.subchannel)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::channelz::v1::Subchannel*>(
      &::grpc::channelz::v1::_Subchannel_default_instance_);
}
inline ::grpc::channelz::v1::Subchannel* GetSubchannelResponse::release_subchannel() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.GetSubchannelResponse.subchannel)
  
  ::grpc::channelz::v1::Subchannel* temp = subchannel_;
  subchannel_ = NULL;
  return temp;
}
inline ::grpc::channelz::v1::Subchannel* GetSubchannelResponse::mutable_subchannel() {
  
  if (subchannel_ == NULL) {
    subchannel_ = new ::grpc::channelz::v1::Subchannel;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.GetSubchannelResponse.subchannel)
  return subchannel_;
}
inline void GetSubchannelResponse::set_allocated_subchannel(::grpc::channelz::v1::Subchannel* subchannel) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete subchannel_;
  }
  if (subchannel) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      subchannel = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, subchannel, submessage_arena);
    }
    
  } else {
    
  }
  subchannel_ = subchannel;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.GetSubchannelResponse.subchannel)
}

// -------------------------------------------------------------------

// GetSocketRequest

// int64 socket_id = 1;
inline void GetSocketRequest::clear_socket_id() {
  socket_id_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 GetSocketRequest::socket_id() const {
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.GetSocketRequest.socket_id)
  return socket_id_;
}
inline void GetSocketRequest::set_socket_id(::google::protobuf::int64 value) {
  
  socket_id_ = value;
  // @@protoc_insertion_point(field_set:grpc.channelz.v1.GetSocketRequest.socket_id)
}

// -------------------------------------------------------------------

// GetSocketResponse

// .grpc.channelz.v1.Socket socket = 1;
inline bool GetSocketResponse::has_socket() const {
  return this != internal_default_instance() && socket_ != NULL;
}
inline void GetSocketResponse::clear_socket() {
  if (GetArenaNoVirtual() == NULL && socket_ != NULL) {
    delete socket_;
  }
  socket_ = NULL;
}
inline const ::grpc::channelz::v1::Socket& GetSocketResponse::socket() const {
  const ::grpc::channelz::v1::Socket* p = socket_;
  // @@protoc_insertion_point(field_get:grpc.channelz.v1.GetSocketResponse.socket)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::channelz::v1::Socket*>(
      &::grpc::channelz::v1::_Socket_default_instance_);
}
inline ::grpc::channelz::v1::Socket* GetSocketResponse::release_socket() {
  // @@protoc_insertion_point(field_release:grpc.channelz.v1.GetSocketResponse.socket)
  
  ::grpc::channelz::v1::Socket* temp = socket_;
  socket_ = NULL;
  return temp;
}
inline ::grpc::channelz::v1::Socket* GetSocketResponse::mutable_socket() {
  
  if (socket_ == NULL) {
    socket_ = new ::grpc::channelz::v1::Socket;
  }
  // @@protoc_insertion_point(field_mutable:grpc.channelz.v1.GetSocketResponse.socket)
  return socket_;
}
inline void GetSocketResponse::set_allocated_socket(::grpc::channelz::v1::Socket* socket) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete socket_;
  }
  if (socket) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      socket = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, socket, submessage_arena);
    }
    
  } else {
    
  }
  socket_ = socket;
  // @@protoc_insertion_point(field_set_allocated:grpc.channelz.v1.GetSocketResponse.socket)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace v1
}  // namespace channelz
}  // namespace grpc

namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::grpc::channelz::v1::ChannelConnectivityState_State> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::grpc::channelz::v1::ChannelConnectivityState_State>() {
  return ::grpc::channelz::v1::ChannelConnectivityState_State_descriptor();
}
template <> struct is_proto_enum< ::grpc::channelz::v1::ChannelTraceEvent_Severity> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::grpc::channelz::v1::ChannelTraceEvent_Severity>() {
  return ::grpc::channelz::v1::ChannelTraceEvent_Severity_descriptor();
}

}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_src_2fproto_2fgrpc_2fchannelz_2fchannelz_2eproto__INCLUDED
