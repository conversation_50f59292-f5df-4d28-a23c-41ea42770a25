// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: src/proto/grpc/channelz/channelz.proto

#include "src/proto/grpc/channelz/channelz.pb.h"
#include "src/proto/grpc/channelz/channelz.grpc.pb.h"

#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/sync_stream.h>
#include <gmock/gmock.h>
namespace grpc {
namespace channelz {
namespace v1 {

class MockChannelzStub : public Channelz::StubInterface {
 public:
  MOCK_METHOD3(GetTopChannels, ::grpc::Status(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetTopChannelsRequest& request, ::grpc::channelz::v1::GetTopChannelsResponse* response));
  MOCK_METHOD3(AsyncGetTopChannelsRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetTopChannelsResponse>*(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetTopChannelsRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncGetTopChannelsRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetTopChannelsResponse>*(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetTopChannelsRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(GetServers, ::grpc::Status(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetServersRequest& request, ::grpc::channelz::v1::GetServersResponse* response));
  MOCK_METHOD3(AsyncGetServersRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetServersResponse>*(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetServersRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncGetServersRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetServersResponse>*(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetServersRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(GetServerSockets, ::grpc::Status(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetServerSocketsRequest& request, ::grpc::channelz::v1::GetServerSocketsResponse* response));
  MOCK_METHOD3(AsyncGetServerSocketsRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetServerSocketsResponse>*(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetServerSocketsRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncGetServerSocketsRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetServerSocketsResponse>*(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetServerSocketsRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(GetChannel, ::grpc::Status(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetChannelRequest& request, ::grpc::channelz::v1::GetChannelResponse* response));
  MOCK_METHOD3(AsyncGetChannelRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetChannelResponse>*(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetChannelRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncGetChannelRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetChannelResponse>*(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetChannelRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(GetSubchannel, ::grpc::Status(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetSubchannelRequest& request, ::grpc::channelz::v1::GetSubchannelResponse* response));
  MOCK_METHOD3(AsyncGetSubchannelRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetSubchannelResponse>*(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetSubchannelRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncGetSubchannelRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetSubchannelResponse>*(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetSubchannelRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(GetSocket, ::grpc::Status(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetSocketRequest& request, ::grpc::channelz::v1::GetSocketResponse* response));
  MOCK_METHOD3(AsyncGetSocketRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetSocketResponse>*(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetSocketRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncGetSocketRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::channelz::v1::GetSocketResponse>*(::grpc::ClientContext* context, const ::grpc::channelz::v1::GetSocketRequest& request, ::grpc::CompletionQueue* cq));
};

} // namespace grpc
} // namespace channelz
} // namespace v1

