// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: src/proto/grpc/core/stats.proto

#include "src/proto/grpc/core/stats.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)
namespace grpc {
namespace core {
class BucketDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Bucket>
      _instance;
} _Bucket_default_instance_;
class HistogramDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Histogram>
      _instance;
} _Histogram_default_instance_;
class MetricDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Metric>
      _instance;
  ::google::protobuf::uint64 count_;
  const ::grpc::core::Histogram* histogram_;
} _Metric_default_instance_;
class StatsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Stats>
      _instance;
} _Stats_default_instance_;
}  // namespace core
}  // namespace grpc
namespace protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto {
void InitDefaultsBucketImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::core::_Bucket_default_instance_;
    new (ptr) ::grpc::core::Bucket();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::core::Bucket::InitAsDefaultInstance();
}

void InitDefaultsBucket() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsBucketImpl);
}

void InitDefaultsHistogramImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::InitDefaultsBucket();
  {
    void* ptr = &::grpc::core::_Histogram_default_instance_;
    new (ptr) ::grpc::core::Histogram();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::core::Histogram::InitAsDefaultInstance();
}

void InitDefaultsHistogram() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsHistogramImpl);
}

void InitDefaultsMetricImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::InitDefaultsHistogram();
  {
    void* ptr = &::grpc::core::_Metric_default_instance_;
    new (ptr) ::grpc::core::Metric();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::core::Metric::InitAsDefaultInstance();
}

void InitDefaultsMetric() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsMetricImpl);
}

void InitDefaultsStatsImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::InitDefaultsMetric();
  {
    void* ptr = &::grpc::core::_Stats_default_instance_;
    new (ptr) ::grpc::core::Stats();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::core::Stats::InitAsDefaultInstance();
}

void InitDefaultsStats() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsStatsImpl);
}

::google::protobuf::Metadata file_level_metadata[4];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::core::Bucket, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::core::Bucket, start_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::core::Bucket, count_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::core::Histogram, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::core::Histogram, buckets_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::core::Metric, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::core::Metric, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::core::Metric, name_),
  offsetof(::grpc::core::MetricDefaultTypeInternal, count_),
  offsetof(::grpc::core::MetricDefaultTypeInternal, histogram_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::core::Metric, value_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::core::Stats, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::core::Stats, metrics_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::grpc::core::Bucket)},
  { 7, -1, sizeof(::grpc::core::Histogram)},
  { 13, -1, sizeof(::grpc::core::Metric)},
  { 22, -1, sizeof(::grpc::core::Stats)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::core::_Bucket_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::core::_Histogram_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::core::_Metric_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::core::_Stats_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  ::google::protobuf::MessageFactory* factory = NULL;
  AssignDescriptors(
      "src/proto/grpc/core/stats.proto", schemas, file_default_instances, TableStruct::offsets, factory,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 4);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n\037src/proto/grpc/core/stats.proto\022\tgrpc."
      "core\"&\n\006Bucket\022\r\n\005start\030\001 \001(\001\022\r\n\005count\030\002"
      " \001(\004\"/\n\tHistogram\022\"\n\007buckets\030\001 \003(\0132\021.grp"
      "c.core.Bucket\"[\n\006Metric\022\014\n\004name\030\001 \001(\t\022\017\n"
      "\005count\030\n \001(\004H\000\022)\n\thistogram\030\013 \001(\0132\024.grpc"
      ".core.HistogramH\000B\007\n\005value\"+\n\005Stats\022\"\n\007m"
      "etrics\030\001 \003(\0132\021.grpc.core.Metricb\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 279);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "src/proto/grpc/core/stats.proto", &protobuf_RegisterTypes);
}

void AddDescriptors() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto
namespace grpc {
namespace core {

// ===================================================================

void Bucket::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Bucket::kStartFieldNumber;
const int Bucket::kCountFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Bucket::Bucket()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::InitDefaultsBucket();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.core.Bucket)
}
Bucket::Bucket(const Bucket& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&start_, &from.start_,
    static_cast<size_t>(reinterpret_cast<char*>(&count_) -
    reinterpret_cast<char*>(&start_)) + sizeof(count_));
  // @@protoc_insertion_point(copy_constructor:grpc.core.Bucket)
}

void Bucket::SharedCtor() {
  ::memset(&start_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&count_) -
      reinterpret_cast<char*>(&start_)) + sizeof(count_));
  _cached_size_ = 0;
}

Bucket::~Bucket() {
  // @@protoc_insertion_point(destructor:grpc.core.Bucket)
  SharedDtor();
}

void Bucket::SharedDtor() {
}

void Bucket::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Bucket::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Bucket& Bucket::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::InitDefaultsBucket();
  return *internal_default_instance();
}

Bucket* Bucket::New(::google::protobuf::Arena* arena) const {
  Bucket* n = new Bucket;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Bucket::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.core.Bucket)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&start_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&count_) -
      reinterpret_cast<char*>(&start_)) + sizeof(count_));
  _internal_metadata_.Clear();
}

bool Bucket::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.core.Bucket)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // double start = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(9u /* 9 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &start_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint64 count = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &count_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.core.Bucket)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.core.Bucket)
  return false;
#undef DO_
}

void Bucket::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.core.Bucket)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double start = 1;
  if (this->start() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(1, this->start(), output);
  }

  // uint64 count = 2;
  if (this->count() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(2, this->count(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.core.Bucket)
}

::google::protobuf::uint8* Bucket::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.core.Bucket)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double start = 1;
  if (this->start() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(1, this->start(), target);
  }

  // uint64 count = 2;
  if (this->count() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(2, this->count(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.core.Bucket)
  return target;
}

size_t Bucket::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.core.Bucket)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // double start = 1;
  if (this->start() != 0) {
    total_size += 1 + 8;
  }

  // uint64 count = 2;
  if (this->count() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->count());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Bucket::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.core.Bucket)
  GOOGLE_DCHECK_NE(&from, this);
  const Bucket* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Bucket>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.core.Bucket)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.core.Bucket)
    MergeFrom(*source);
  }
}

void Bucket::MergeFrom(const Bucket& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.core.Bucket)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.start() != 0) {
    set_start(from.start());
  }
  if (from.count() != 0) {
    set_count(from.count());
  }
}

void Bucket::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.core.Bucket)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Bucket::CopyFrom(const Bucket& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.core.Bucket)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Bucket::IsInitialized() const {
  return true;
}

void Bucket::Swap(Bucket* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Bucket::InternalSwap(Bucket* other) {
  using std::swap;
  swap(start_, other->start_);
  swap(count_, other->count_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Bucket::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Histogram::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Histogram::kBucketsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Histogram::Histogram()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::InitDefaultsHistogram();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.core.Histogram)
}
Histogram::Histogram(const Histogram& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      buckets_(from.buckets_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:grpc.core.Histogram)
}

void Histogram::SharedCtor() {
  _cached_size_ = 0;
}

Histogram::~Histogram() {
  // @@protoc_insertion_point(destructor:grpc.core.Histogram)
  SharedDtor();
}

void Histogram::SharedDtor() {
}

void Histogram::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Histogram::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Histogram& Histogram::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::InitDefaultsHistogram();
  return *internal_default_instance();
}

Histogram* Histogram::New(::google::protobuf::Arena* arena) const {
  Histogram* n = new Histogram;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Histogram::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.core.Histogram)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  buckets_.Clear();
  _internal_metadata_.Clear();
}

bool Histogram::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.core.Histogram)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .grpc.core.Bucket buckets = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(input, add_buckets()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.core.Histogram)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.core.Histogram)
  return false;
#undef DO_
}

void Histogram::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.core.Histogram)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .grpc.core.Bucket buckets = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->buckets_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->buckets(static_cast<int>(i)), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.core.Histogram)
}

::google::protobuf::uint8* Histogram::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.core.Histogram)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .grpc.core.Bucket buckets = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->buckets_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->buckets(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.core.Histogram)
  return target;
}

size_t Histogram::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.core.Histogram)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .grpc.core.Bucket buckets = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->buckets_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->buckets(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Histogram::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.core.Histogram)
  GOOGLE_DCHECK_NE(&from, this);
  const Histogram* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Histogram>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.core.Histogram)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.core.Histogram)
    MergeFrom(*source);
  }
}

void Histogram::MergeFrom(const Histogram& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.core.Histogram)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  buckets_.MergeFrom(from.buckets_);
}

void Histogram::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.core.Histogram)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Histogram::CopyFrom(const Histogram& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.core.Histogram)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Histogram::IsInitialized() const {
  return true;
}

void Histogram::Swap(Histogram* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Histogram::InternalSwap(Histogram* other) {
  using std::swap;
  buckets_.InternalSwap(&other->buckets_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Histogram::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Metric::InitAsDefaultInstance() {
  ::grpc::core::_Metric_default_instance_.count_ = GOOGLE_ULONGLONG(0);
  ::grpc::core::_Metric_default_instance_.histogram_ = const_cast< ::grpc::core::Histogram*>(
      ::grpc::core::Histogram::internal_default_instance());
}
void Metric::set_allocated_histogram(::grpc::core::Histogram* histogram) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_value();
  if (histogram) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      histogram = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, histogram, submessage_arena);
    }
    set_has_histogram();
    value_.histogram_ = histogram;
  }
  // @@protoc_insertion_point(field_set_allocated:grpc.core.Metric.histogram)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Metric::kNameFieldNumber;
const int Metric::kCountFieldNumber;
const int Metric::kHistogramFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Metric::Metric()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::InitDefaultsMetric();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.core.Metric)
}
Metric::Metric(const Metric& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  clear_has_value();
  switch (from.value_case()) {
    case kCount: {
      set_count(from.count());
      break;
    }
    case kHistogram: {
      mutable_histogram()->::grpc::core::Histogram::MergeFrom(from.histogram());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:grpc.core.Metric)
}

void Metric::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_value();
  _cached_size_ = 0;
}

Metric::~Metric() {
  // @@protoc_insertion_point(destructor:grpc.core.Metric)
  SharedDtor();
}

void Metric::SharedDtor() {
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (has_value()) {
    clear_value();
  }
}

void Metric::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Metric::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Metric& Metric::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::InitDefaultsMetric();
  return *internal_default_instance();
}

Metric* Metric::New(::google::protobuf::Arena* arena) const {
  Metric* n = new Metric;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Metric::clear_value() {
// @@protoc_insertion_point(one_of_clear_start:grpc.core.Metric)
  switch (value_case()) {
    case kCount: {
      // No need to clear
      break;
    }
    case kHistogram: {
      delete value_.histogram_;
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = VALUE_NOT_SET;
}


void Metric::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.core.Metric)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_value();
  _internal_metadata_.Clear();
}

bool Metric::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.core.Metric)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.core.Metric.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint64 count = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(80u /* 80 & 0xFF */)) {
          clear_value();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &value_.count_)));
          set_has_count();
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.core.Histogram histogram = 11;
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(90u /* 90 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_histogram()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.core.Metric)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.core.Metric)
  return false;
#undef DO_
}

void Metric::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.core.Metric)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.core.Metric.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // uint64 count = 10;
  if (has_count()) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(10, this->count(), output);
  }

  // .grpc.core.Histogram histogram = 11;
  if (has_histogram()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      11, *value_.histogram_, output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.core.Metric)
}

::google::protobuf::uint8* Metric::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.core.Metric)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.core.Metric.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // uint64 count = 10;
  if (has_count()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(10, this->count(), target);
  }

  // .grpc.core.Histogram histogram = 11;
  if (has_histogram()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        11, *value_.histogram_, deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.core.Metric)
  return target;
}

size_t Metric::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.core.Metric)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  switch (value_case()) {
    // uint64 count = 10;
    case kCount: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::UInt64Size(
          this->count());
      break;
    }
    // .grpc.core.Histogram histogram = 11;
    case kHistogram: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *value_.histogram_);
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Metric::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.core.Metric)
  GOOGLE_DCHECK_NE(&from, this);
  const Metric* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Metric>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.core.Metric)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.core.Metric)
    MergeFrom(*source);
  }
}

void Metric::MergeFrom(const Metric& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.core.Metric)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.name().size() > 0) {

    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  switch (from.value_case()) {
    case kCount: {
      set_count(from.count());
      break;
    }
    case kHistogram: {
      mutable_histogram()->::grpc::core::Histogram::MergeFrom(from.histogram());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
}

void Metric::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.core.Metric)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Metric::CopyFrom(const Metric& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.core.Metric)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Metric::IsInitialized() const {
  return true;
}

void Metric::Swap(Metric* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Metric::InternalSwap(Metric* other) {
  using std::swap;
  name_.Swap(&other->name_);
  swap(value_, other->value_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Metric::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Stats::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Stats::kMetricsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Stats::Stats()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::InitDefaultsStats();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.core.Stats)
}
Stats::Stats(const Stats& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      metrics_(from.metrics_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:grpc.core.Stats)
}

void Stats::SharedCtor() {
  _cached_size_ = 0;
}

Stats::~Stats() {
  // @@protoc_insertion_point(destructor:grpc.core.Stats)
  SharedDtor();
}

void Stats::SharedDtor() {
}

void Stats::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Stats::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Stats& Stats::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::InitDefaultsStats();
  return *internal_default_instance();
}

Stats* Stats::New(::google::protobuf::Arena* arena) const {
  Stats* n = new Stats;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Stats::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.core.Stats)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  metrics_.Clear();
  _internal_metadata_.Clear();
}

bool Stats::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.core.Stats)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .grpc.core.Metric metrics = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(input, add_metrics()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.core.Stats)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.core.Stats)
  return false;
#undef DO_
}

void Stats::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.core.Stats)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .grpc.core.Metric metrics = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->metrics_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->metrics(static_cast<int>(i)), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.core.Stats)
}

::google::protobuf::uint8* Stats::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.core.Stats)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .grpc.core.Metric metrics = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->metrics_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->metrics(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.core.Stats)
  return target;
}

size_t Stats::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.core.Stats)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .grpc.core.Metric metrics = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->metrics_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->metrics(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Stats::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.core.Stats)
  GOOGLE_DCHECK_NE(&from, this);
  const Stats* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Stats>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.core.Stats)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.core.Stats)
    MergeFrom(*source);
  }
}

void Stats::MergeFrom(const Stats& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.core.Stats)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  metrics_.MergeFrom(from.metrics_);
}

void Stats::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.core.Stats)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Stats::CopyFrom(const Stats& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.core.Stats)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Stats::IsInitialized() const {
  return true;
}

void Stats::Swap(Stats* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Stats::InternalSwap(Stats* other) {
  using std::swap;
  metrics_.InternalSwap(&other->metrics_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Stats::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace core
}  // namespace grpc

// @@protoc_insertion_point(global_scope)
