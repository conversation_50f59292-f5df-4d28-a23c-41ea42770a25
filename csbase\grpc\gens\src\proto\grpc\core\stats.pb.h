// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: src/proto/grpc/core/stats.proto

#ifndef PROTOBUF_src_2fproto_2fgrpc_2fcore_2fstats_2eproto__INCLUDED
#define PROTOBUF_src_2fproto_2fgrpc_2fcore_2fstats_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3005001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[4];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
void InitDefaultsBucketImpl();
void InitDefaultsBucket();
void InitDefaultsHistogramImpl();
void InitDefaultsHistogram();
void InitDefaultsMetricImpl();
void InitDefaultsMetric();
void InitDefaultsStatsImpl();
void InitDefaultsStats();
inline void InitDefaults() {
  InitDefaultsBucket();
  InitDefaultsHistogram();
  InitDefaultsMetric();
  InitDefaultsStats();
}
}  // namespace protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto
namespace grpc {
namespace core {
class Bucket;
class BucketDefaultTypeInternal;
extern BucketDefaultTypeInternal _Bucket_default_instance_;
class Histogram;
class HistogramDefaultTypeInternal;
extern HistogramDefaultTypeInternal _Histogram_default_instance_;
class Metric;
class MetricDefaultTypeInternal;
extern MetricDefaultTypeInternal _Metric_default_instance_;
class Stats;
class StatsDefaultTypeInternal;
extern StatsDefaultTypeInternal _Stats_default_instance_;
}  // namespace core
}  // namespace grpc
namespace grpc {
namespace core {

// ===================================================================

class Bucket : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.core.Bucket) */ {
 public:
  Bucket();
  virtual ~Bucket();

  Bucket(const Bucket& from);

  inline Bucket& operator=(const Bucket& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Bucket(Bucket&& from) noexcept
    : Bucket() {
    *this = ::std::move(from);
  }

  inline Bucket& operator=(Bucket&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Bucket& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Bucket* internal_default_instance() {
    return reinterpret_cast<const Bucket*>(
               &_Bucket_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    0;

  void Swap(Bucket* other);
  friend void swap(Bucket& a, Bucket& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Bucket* New() const PROTOBUF_FINAL { return New(NULL); }

  Bucket* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const Bucket& from);
  void MergeFrom(const Bucket& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(Bucket* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // double start = 1;
  void clear_start();
  static const int kStartFieldNumber = 1;
  double start() const;
  void set_start(double value);

  // uint64 count = 2;
  void clear_count();
  static const int kCountFieldNumber = 2;
  ::google::protobuf::uint64 count() const;
  void set_count(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:grpc.core.Bucket)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  double start_;
  ::google::protobuf::uint64 count_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::InitDefaultsBucketImpl();
};
// -------------------------------------------------------------------

class Histogram : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.core.Histogram) */ {
 public:
  Histogram();
  virtual ~Histogram();

  Histogram(const Histogram& from);

  inline Histogram& operator=(const Histogram& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Histogram(Histogram&& from) noexcept
    : Histogram() {
    *this = ::std::move(from);
  }

  inline Histogram& operator=(Histogram&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Histogram& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Histogram* internal_default_instance() {
    return reinterpret_cast<const Histogram*>(
               &_Histogram_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    1;

  void Swap(Histogram* other);
  friend void swap(Histogram& a, Histogram& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Histogram* New() const PROTOBUF_FINAL { return New(NULL); }

  Histogram* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const Histogram& from);
  void MergeFrom(const Histogram& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(Histogram* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .grpc.core.Bucket buckets = 1;
  int buckets_size() const;
  void clear_buckets();
  static const int kBucketsFieldNumber = 1;
  const ::grpc::core::Bucket& buckets(int index) const;
  ::grpc::core::Bucket* mutable_buckets(int index);
  ::grpc::core::Bucket* add_buckets();
  ::google::protobuf::RepeatedPtrField< ::grpc::core::Bucket >*
      mutable_buckets();
  const ::google::protobuf::RepeatedPtrField< ::grpc::core::Bucket >&
      buckets() const;

  // @@protoc_insertion_point(class_scope:grpc.core.Histogram)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::grpc::core::Bucket > buckets_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::InitDefaultsHistogramImpl();
};
// -------------------------------------------------------------------

class Metric : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.core.Metric) */ {
 public:
  Metric();
  virtual ~Metric();

  Metric(const Metric& from);

  inline Metric& operator=(const Metric& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Metric(Metric&& from) noexcept
    : Metric() {
    *this = ::std::move(from);
  }

  inline Metric& operator=(Metric&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Metric& default_instance();

  enum ValueCase {
    kCount = 10,
    kHistogram = 11,
    VALUE_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Metric* internal_default_instance() {
    return reinterpret_cast<const Metric*>(
               &_Metric_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    2;

  void Swap(Metric* other);
  friend void swap(Metric& a, Metric& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Metric* New() const PROTOBUF_FINAL { return New(NULL); }

  Metric* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const Metric& from);
  void MergeFrom(const Metric& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(Metric* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // uint64 count = 10;
  private:
  bool has_count() const;
  public:
  void clear_count();
  static const int kCountFieldNumber = 10;
  ::google::protobuf::uint64 count() const;
  void set_count(::google::protobuf::uint64 value);

  // .grpc.core.Histogram histogram = 11;
  bool has_histogram() const;
  void clear_histogram();
  static const int kHistogramFieldNumber = 11;
  const ::grpc::core::Histogram& histogram() const;
  ::grpc::core::Histogram* release_histogram();
  ::grpc::core::Histogram* mutable_histogram();
  void set_allocated_histogram(::grpc::core::Histogram* histogram);

  ValueCase value_case() const;
  // @@protoc_insertion_point(class_scope:grpc.core.Metric)
 private:
  void set_has_count();
  void set_has_histogram();

  inline bool has_value() const;
  void clear_value();
  inline void clear_has_value();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  union ValueUnion {
    ValueUnion() {}
    ::google::protobuf::uint64 count_;
    ::grpc::core::Histogram* histogram_;
  } value_;
  mutable int _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::InitDefaultsMetricImpl();
};
// -------------------------------------------------------------------

class Stats : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.core.Stats) */ {
 public:
  Stats();
  virtual ~Stats();

  Stats(const Stats& from);

  inline Stats& operator=(const Stats& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Stats(Stats&& from) noexcept
    : Stats() {
    *this = ::std::move(from);
  }

  inline Stats& operator=(Stats&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Stats& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Stats* internal_default_instance() {
    return reinterpret_cast<const Stats*>(
               &_Stats_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    3;

  void Swap(Stats* other);
  friend void swap(Stats& a, Stats& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Stats* New() const PROTOBUF_FINAL { return New(NULL); }

  Stats* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const Stats& from);
  void MergeFrom(const Stats& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(Stats* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .grpc.core.Metric metrics = 1;
  int metrics_size() const;
  void clear_metrics();
  static const int kMetricsFieldNumber = 1;
  const ::grpc::core::Metric& metrics(int index) const;
  ::grpc::core::Metric* mutable_metrics(int index);
  ::grpc::core::Metric* add_metrics();
  ::google::protobuf::RepeatedPtrField< ::grpc::core::Metric >*
      mutable_metrics();
  const ::google::protobuf::RepeatedPtrField< ::grpc::core::Metric >&
      metrics() const;

  // @@protoc_insertion_point(class_scope:grpc.core.Stats)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::grpc::core::Metric > metrics_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::InitDefaultsStatsImpl();
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Bucket

// double start = 1;
inline void Bucket::clear_start() {
  start_ = 0;
}
inline double Bucket::start() const {
  // @@protoc_insertion_point(field_get:grpc.core.Bucket.start)
  return start_;
}
inline void Bucket::set_start(double value) {
  
  start_ = value;
  // @@protoc_insertion_point(field_set:grpc.core.Bucket.start)
}

// uint64 count = 2;
inline void Bucket::clear_count() {
  count_ = GOOGLE_ULONGLONG(0);
}
inline ::google::protobuf::uint64 Bucket::count() const {
  // @@protoc_insertion_point(field_get:grpc.core.Bucket.count)
  return count_;
}
inline void Bucket::set_count(::google::protobuf::uint64 value) {
  
  count_ = value;
  // @@protoc_insertion_point(field_set:grpc.core.Bucket.count)
}

// -------------------------------------------------------------------

// Histogram

// repeated .grpc.core.Bucket buckets = 1;
inline int Histogram::buckets_size() const {
  return buckets_.size();
}
inline void Histogram::clear_buckets() {
  buckets_.Clear();
}
inline const ::grpc::core::Bucket& Histogram::buckets(int index) const {
  // @@protoc_insertion_point(field_get:grpc.core.Histogram.buckets)
  return buckets_.Get(index);
}
inline ::grpc::core::Bucket* Histogram::mutable_buckets(int index) {
  // @@protoc_insertion_point(field_mutable:grpc.core.Histogram.buckets)
  return buckets_.Mutable(index);
}
inline ::grpc::core::Bucket* Histogram::add_buckets() {
  // @@protoc_insertion_point(field_add:grpc.core.Histogram.buckets)
  return buckets_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::grpc::core::Bucket >*
Histogram::mutable_buckets() {
  // @@protoc_insertion_point(field_mutable_list:grpc.core.Histogram.buckets)
  return &buckets_;
}
inline const ::google::protobuf::RepeatedPtrField< ::grpc::core::Bucket >&
Histogram::buckets() const {
  // @@protoc_insertion_point(field_list:grpc.core.Histogram.buckets)
  return buckets_;
}

// -------------------------------------------------------------------

// Metric

// string name = 1;
inline void Metric::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Metric::name() const {
  // @@protoc_insertion_point(field_get:grpc.core.Metric.name)
  return name_.GetNoArena();
}
inline void Metric::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.core.Metric.name)
}
#if LANG_CXX11
inline void Metric::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.core.Metric.name)
}
#endif
inline void Metric::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.core.Metric.name)
}
inline void Metric::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.core.Metric.name)
}
inline ::std::string* Metric::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:grpc.core.Metric.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Metric::release_name() {
  // @@protoc_insertion_point(field_release:grpc.core.Metric.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Metric::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:grpc.core.Metric.name)
}

// uint64 count = 10;
inline bool Metric::has_count() const {
  return value_case() == kCount;
}
inline void Metric::set_has_count() {
  _oneof_case_[0] = kCount;
}
inline void Metric::clear_count() {
  if (has_count()) {
    value_.count_ = GOOGLE_ULONGLONG(0);
    clear_has_value();
  }
}
inline ::google::protobuf::uint64 Metric::count() const {
  // @@protoc_insertion_point(field_get:grpc.core.Metric.count)
  if (has_count()) {
    return value_.count_;
  }
  return GOOGLE_ULONGLONG(0);
}
inline void Metric::set_count(::google::protobuf::uint64 value) {
  if (!has_count()) {
    clear_value();
    set_has_count();
  }
  value_.count_ = value;
  // @@protoc_insertion_point(field_set:grpc.core.Metric.count)
}

// .grpc.core.Histogram histogram = 11;
inline bool Metric::has_histogram() const {
  return value_case() == kHistogram;
}
inline void Metric::set_has_histogram() {
  _oneof_case_[0] = kHistogram;
}
inline void Metric::clear_histogram() {
  if (has_histogram()) {
    delete value_.histogram_;
    clear_has_value();
  }
}
inline ::grpc::core::Histogram* Metric::release_histogram() {
  // @@protoc_insertion_point(field_release:grpc.core.Metric.histogram)
  if (has_histogram()) {
    clear_has_value();
      ::grpc::core::Histogram* temp = value_.histogram_;
    value_.histogram_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::grpc::core::Histogram& Metric::histogram() const {
  // @@protoc_insertion_point(field_get:grpc.core.Metric.histogram)
  return has_histogram()
      ? *value_.histogram_
      : *reinterpret_cast< ::grpc::core::Histogram*>(&::grpc::core::_Histogram_default_instance_);
}
inline ::grpc::core::Histogram* Metric::mutable_histogram() {
  if (!has_histogram()) {
    clear_value();
    set_has_histogram();
    value_.histogram_ = new ::grpc::core::Histogram;
  }
  // @@protoc_insertion_point(field_mutable:grpc.core.Metric.histogram)
  return value_.histogram_;
}

inline bool Metric::has_value() const {
  return value_case() != VALUE_NOT_SET;
}
inline void Metric::clear_has_value() {
  _oneof_case_[0] = VALUE_NOT_SET;
}
inline Metric::ValueCase Metric::value_case() const {
  return Metric::ValueCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// Stats

// repeated .grpc.core.Metric metrics = 1;
inline int Stats::metrics_size() const {
  return metrics_.size();
}
inline void Stats::clear_metrics() {
  metrics_.Clear();
}
inline const ::grpc::core::Metric& Stats::metrics(int index) const {
  // @@protoc_insertion_point(field_get:grpc.core.Stats.metrics)
  return metrics_.Get(index);
}
inline ::grpc::core::Metric* Stats::mutable_metrics(int index) {
  // @@protoc_insertion_point(field_mutable:grpc.core.Stats.metrics)
  return metrics_.Mutable(index);
}
inline ::grpc::core::Metric* Stats::add_metrics() {
  // @@protoc_insertion_point(field_add:grpc.core.Stats.metrics)
  return metrics_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::grpc::core::Metric >*
Stats::mutable_metrics() {
  // @@protoc_insertion_point(field_mutable_list:grpc.core.Stats.metrics)
  return &metrics_;
}
inline const ::google::protobuf::RepeatedPtrField< ::grpc::core::Metric >&
Stats::metrics() const {
  // @@protoc_insertion_point(field_list:grpc.core.Stats.metrics)
  return metrics_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace core
}  // namespace grpc

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_src_2fproto_2fgrpc_2fcore_2fstats_2eproto__INCLUDED
