// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: src/proto/grpc/core/stats.proto

#include "src/proto/grpc/core/stats.pb.h"
#include "src/proto/grpc/core/stats.grpc.pb.h"

#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/sync_stream.h>
#include <gmock/gmock.h>
namespace grpc {
namespace core {

} // namespace grpc
} // namespace core

