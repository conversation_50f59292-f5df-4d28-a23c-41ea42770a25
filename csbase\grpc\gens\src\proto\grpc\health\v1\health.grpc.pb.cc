// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: src/proto/grpc/health/v1/health.proto

#include "src/proto/grpc/health/v1/health.pb.h"
#include "src/proto/grpc/health/v1/health.grpc.pb.h"

#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/method_handler_impl.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace grpc {
namespace health {
namespace v1 {

static const char* Health_method_names[] = {
  "/grpc.health.v1.Health/Check",
};

std::unique_ptr< Health::Stub> Health::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< Health::Stub> stub(new Health::Stub(channel));
  return stub;
}

Health::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel)
  : channel_(channel), rpcmethod_Check_(Health_method_names[0], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status Health::Stub::Check(::grpc::ClientContext* context, const ::grpc::health::v1::HealthCheckRequest& request, ::grpc::health::v1::HealthCheckResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_Check_, context, request, response);
}

::grpc::ClientAsyncResponseReader< ::grpc::health::v1::HealthCheckResponse>* Health::Stub::AsyncCheckRaw(::grpc::ClientContext* context, const ::grpc::health::v1::HealthCheckRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::health::v1::HealthCheckResponse>::Create(channel_.get(), cq, rpcmethod_Check_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::grpc::health::v1::HealthCheckResponse>* Health::Stub::PrepareAsyncCheckRaw(::grpc::ClientContext* context, const ::grpc::health::v1::HealthCheckRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::health::v1::HealthCheckResponse>::Create(channel_.get(), cq, rpcmethod_Check_, context, request, false);
}

Health::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      Health_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< Health::Service, ::grpc::health::v1::HealthCheckRequest, ::grpc::health::v1::HealthCheckResponse>(
          std::mem_fn(&Health::Service::Check), this)));
}

Health::Service::~Service() {
}

::grpc::Status Health::Service::Check(::grpc::ServerContext* context, const ::grpc::health::v1::HealthCheckRequest* request, ::grpc::health::v1::HealthCheckResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace grpc
}  // namespace health
}  // namespace v1

