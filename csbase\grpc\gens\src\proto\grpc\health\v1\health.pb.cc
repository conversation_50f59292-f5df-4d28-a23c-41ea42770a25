// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: src/proto/grpc/health/v1/health.proto

#include "src/proto/grpc/health/v1/health.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)
namespace grpc {
namespace health {
namespace v1 {
class HealthCheckRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<HealthCheckRequest>
      _instance;
} _HealthCheckRequest_default_instance_;
class HealthCheckResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<HealthCheckResponse>
      _instance;
} _HealthCheckResponse_default_instance_;
}  // namespace v1
}  // namespace health
}  // namespace grpc
namespace protobuf_src_2fproto_2fgrpc_2fhealth_2fv1_2fhealth_2eproto {
void InitDefaultsHealthCheckRequestImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::health::v1::_HealthCheckRequest_default_instance_;
    new (ptr) ::grpc::health::v1::HealthCheckRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::health::v1::HealthCheckRequest::InitAsDefaultInstance();
}

void InitDefaultsHealthCheckRequest() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsHealthCheckRequestImpl);
}

void InitDefaultsHealthCheckResponseImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::health::v1::_HealthCheckResponse_default_instance_;
    new (ptr) ::grpc::health::v1::HealthCheckResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::health::v1::HealthCheckResponse::InitAsDefaultInstance();
}

void InitDefaultsHealthCheckResponse() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsHealthCheckResponseImpl);
}

::google::protobuf::Metadata file_level_metadata[2];
const ::google::protobuf::EnumDescriptor* file_level_enum_descriptors[1];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::health::v1::HealthCheckRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::health::v1::HealthCheckRequest, service_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::health::v1::HealthCheckResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::health::v1::HealthCheckResponse, status_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::grpc::health::v1::HealthCheckRequest)},
  { 6, -1, sizeof(::grpc::health::v1::HealthCheckResponse)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::health::v1::_HealthCheckRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::health::v1::_HealthCheckResponse_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  ::google::protobuf::MessageFactory* factory = NULL;
  AssignDescriptors(
      "src/proto/grpc/health/v1/health.proto", schemas, file_default_instances, TableStruct::offsets, factory,
      file_level_metadata, file_level_enum_descriptors, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 2);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n%src/proto/grpc/health/v1/health.proto\022"
      "\016grpc.health.v1\"%\n\022HealthCheckRequest\022\017\n"
      "\007service\030\001 \001(\t\"\224\001\n\023HealthCheckResponse\022A"
      "\n\006status\030\001 \001(\01621.grpc.health.v1.HealthCh"
      "eckResponse.ServingStatus\":\n\rServingStat"
      "us\022\013\n\007UNKNOWN\020\000\022\013\n\007SERVING\020\001\022\017\n\013NOT_SERV"
      "ING\020\0022Z\n\006Health\022P\n\005Check\022\".grpc.health.v"
      "1.HealthCheckRequest\032#.grpc.health.v1.He"
      "althCheckResponseBa\n\021io.grpc.health.v1B\013"
      "HealthProtoP\001Z,google.golang.org/grpc/he"
      "alth/grpc_health_v1\252\002\016Grpc.Health.V1b\006pr"
      "oto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 444);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "src/proto/grpc/health/v1/health.proto", &protobuf_RegisterTypes);
}

void AddDescriptors() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_src_2fproto_2fgrpc_2fhealth_2fv1_2fhealth_2eproto
namespace grpc {
namespace health {
namespace v1 {
const ::google::protobuf::EnumDescriptor* HealthCheckResponse_ServingStatus_descriptor() {
  protobuf_src_2fproto_2fgrpc_2fhealth_2fv1_2fhealth_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_src_2fproto_2fgrpc_2fhealth_2fv1_2fhealth_2eproto::file_level_enum_descriptors[0];
}
bool HealthCheckResponse_ServingStatus_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

#if !defined(_MSC_VER) || _MSC_VER >= 1900
const HealthCheckResponse_ServingStatus HealthCheckResponse::UNKNOWN;
const HealthCheckResponse_ServingStatus HealthCheckResponse::SERVING;
const HealthCheckResponse_ServingStatus HealthCheckResponse::NOT_SERVING;
const HealthCheckResponse_ServingStatus HealthCheckResponse::ServingStatus_MIN;
const HealthCheckResponse_ServingStatus HealthCheckResponse::ServingStatus_MAX;
const int HealthCheckResponse::ServingStatus_ARRAYSIZE;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

// ===================================================================

void HealthCheckRequest::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int HealthCheckRequest::kServiceFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

HealthCheckRequest::HealthCheckRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2fhealth_2fv1_2fhealth_2eproto::InitDefaultsHealthCheckRequest();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.health.v1.HealthCheckRequest)
}
HealthCheckRequest::HealthCheckRequest(const HealthCheckRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  service_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.service().size() > 0) {
    service_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.service_);
  }
  // @@protoc_insertion_point(copy_constructor:grpc.health.v1.HealthCheckRequest)
}

void HealthCheckRequest::SharedCtor() {
  service_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

HealthCheckRequest::~HealthCheckRequest() {
  // @@protoc_insertion_point(destructor:grpc.health.v1.HealthCheckRequest)
  SharedDtor();
}

void HealthCheckRequest::SharedDtor() {
  service_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void HealthCheckRequest::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* HealthCheckRequest::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2fhealth_2fv1_2fhealth_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2fhealth_2fv1_2fhealth_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const HealthCheckRequest& HealthCheckRequest::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2fhealth_2fv1_2fhealth_2eproto::InitDefaultsHealthCheckRequest();
  return *internal_default_instance();
}

HealthCheckRequest* HealthCheckRequest::New(::google::protobuf::Arena* arena) const {
  HealthCheckRequest* n = new HealthCheckRequest;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void HealthCheckRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.health.v1.HealthCheckRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  service_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _internal_metadata_.Clear();
}

bool HealthCheckRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.health.v1.HealthCheckRequest)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string service = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_service()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->service().data(), static_cast<int>(this->service().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.health.v1.HealthCheckRequest.service"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.health.v1.HealthCheckRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.health.v1.HealthCheckRequest)
  return false;
#undef DO_
}

void HealthCheckRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.health.v1.HealthCheckRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string service = 1;
  if (this->service().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->service().data(), static_cast<int>(this->service().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.health.v1.HealthCheckRequest.service");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->service(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.health.v1.HealthCheckRequest)
}

::google::protobuf::uint8* HealthCheckRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.health.v1.HealthCheckRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string service = 1;
  if (this->service().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->service().data(), static_cast<int>(this->service().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.health.v1.HealthCheckRequest.service");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->service(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.health.v1.HealthCheckRequest)
  return target;
}

size_t HealthCheckRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.health.v1.HealthCheckRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string service = 1;
  if (this->service().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->service());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void HealthCheckRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.health.v1.HealthCheckRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const HealthCheckRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const HealthCheckRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.health.v1.HealthCheckRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.health.v1.HealthCheckRequest)
    MergeFrom(*source);
  }
}

void HealthCheckRequest::MergeFrom(const HealthCheckRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.health.v1.HealthCheckRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.service().size() > 0) {

    service_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.service_);
  }
}

void HealthCheckRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.health.v1.HealthCheckRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void HealthCheckRequest::CopyFrom(const HealthCheckRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.health.v1.HealthCheckRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HealthCheckRequest::IsInitialized() const {
  return true;
}

void HealthCheckRequest::Swap(HealthCheckRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void HealthCheckRequest::InternalSwap(HealthCheckRequest* other) {
  using std::swap;
  service_.Swap(&other->service_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata HealthCheckRequest::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2fhealth_2fv1_2fhealth_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2fhealth_2fv1_2fhealth_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void HealthCheckResponse::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int HealthCheckResponse::kStatusFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

HealthCheckResponse::HealthCheckResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2fhealth_2fv1_2fhealth_2eproto::InitDefaultsHealthCheckResponse();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.health.v1.HealthCheckResponse)
}
HealthCheckResponse::HealthCheckResponse(const HealthCheckResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  status_ = from.status_;
  // @@protoc_insertion_point(copy_constructor:grpc.health.v1.HealthCheckResponse)
}

void HealthCheckResponse::SharedCtor() {
  status_ = 0;
  _cached_size_ = 0;
}

HealthCheckResponse::~HealthCheckResponse() {
  // @@protoc_insertion_point(destructor:grpc.health.v1.HealthCheckResponse)
  SharedDtor();
}

void HealthCheckResponse::SharedDtor() {
}

void HealthCheckResponse::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* HealthCheckResponse::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2fhealth_2fv1_2fhealth_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2fhealth_2fv1_2fhealth_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const HealthCheckResponse& HealthCheckResponse::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2fhealth_2fv1_2fhealth_2eproto::InitDefaultsHealthCheckResponse();
  return *internal_default_instance();
}

HealthCheckResponse* HealthCheckResponse::New(::google::protobuf::Arena* arena) const {
  HealthCheckResponse* n = new HealthCheckResponse;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void HealthCheckResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.health.v1.HealthCheckResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  status_ = 0;
  _internal_metadata_.Clear();
}

bool HealthCheckResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.health.v1.HealthCheckResponse)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .grpc.health.v1.HealthCheckResponse.ServingStatus status = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_status(static_cast< ::grpc::health::v1::HealthCheckResponse_ServingStatus >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.health.v1.HealthCheckResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.health.v1.HealthCheckResponse)
  return false;
#undef DO_
}

void HealthCheckResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.health.v1.HealthCheckResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.health.v1.HealthCheckResponse.ServingStatus status = 1;
  if (this->status() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->status(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.health.v1.HealthCheckResponse)
}

::google::protobuf::uint8* HealthCheckResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.health.v1.HealthCheckResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.health.v1.HealthCheckResponse.ServingStatus status = 1;
  if (this->status() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->status(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.health.v1.HealthCheckResponse)
  return target;
}

size_t HealthCheckResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.health.v1.HealthCheckResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .grpc.health.v1.HealthCheckResponse.ServingStatus status = 1;
  if (this->status() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->status());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void HealthCheckResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.health.v1.HealthCheckResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const HealthCheckResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const HealthCheckResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.health.v1.HealthCheckResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.health.v1.HealthCheckResponse)
    MergeFrom(*source);
  }
}

void HealthCheckResponse::MergeFrom(const HealthCheckResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.health.v1.HealthCheckResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.status() != 0) {
    set_status(from.status());
  }
}

void HealthCheckResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.health.v1.HealthCheckResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void HealthCheckResponse::CopyFrom(const HealthCheckResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.health.v1.HealthCheckResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HealthCheckResponse::IsInitialized() const {
  return true;
}

void HealthCheckResponse::Swap(HealthCheckResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void HealthCheckResponse::InternalSwap(HealthCheckResponse* other) {
  using std::swap;
  swap(status_, other->status_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata HealthCheckResponse::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2fhealth_2fv1_2fhealth_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2fhealth_2fv1_2fhealth_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace v1
}  // namespace health
}  // namespace grpc

// @@protoc_insertion_point(global_scope)
