// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: src/proto/grpc/health/v1/health.proto

#ifndef PROTOBUF_src_2fproto_2fgrpc_2fhealth_2fv1_2fhealth_2eproto__INCLUDED
#define PROTOBUF_src_2fproto_2fgrpc_2fhealth_2fv1_2fhealth_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3005001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace protobuf_src_2fproto_2fgrpc_2fhealth_2fv1_2fhealth_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[2];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
void InitDefaultsHealthCheckRequestImpl();
void InitDefaultsHealthCheckRequest();
void InitDefaultsHealthCheckResponseImpl();
void InitDefaultsHealthCheckResponse();
inline void InitDefaults() {
  InitDefaultsHealthCheckRequest();
  InitDefaultsHealthCheckResponse();
}
}  // namespace protobuf_src_2fproto_2fgrpc_2fhealth_2fv1_2fhealth_2eproto
namespace grpc {
namespace health {
namespace v1 {
class HealthCheckRequest;
class HealthCheckRequestDefaultTypeInternal;
extern HealthCheckRequestDefaultTypeInternal _HealthCheckRequest_default_instance_;
class HealthCheckResponse;
class HealthCheckResponseDefaultTypeInternal;
extern HealthCheckResponseDefaultTypeInternal _HealthCheckResponse_default_instance_;
}  // namespace v1
}  // namespace health
}  // namespace grpc
namespace grpc {
namespace health {
namespace v1 {

enum HealthCheckResponse_ServingStatus {
  HealthCheckResponse_ServingStatus_UNKNOWN = 0,
  HealthCheckResponse_ServingStatus_SERVING = 1,
  HealthCheckResponse_ServingStatus_NOT_SERVING = 2,
  HealthCheckResponse_ServingStatus_HealthCheckResponse_ServingStatus_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  HealthCheckResponse_ServingStatus_HealthCheckResponse_ServingStatus_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool HealthCheckResponse_ServingStatus_IsValid(int value);
const HealthCheckResponse_ServingStatus HealthCheckResponse_ServingStatus_ServingStatus_MIN = HealthCheckResponse_ServingStatus_UNKNOWN;
const HealthCheckResponse_ServingStatus HealthCheckResponse_ServingStatus_ServingStatus_MAX = HealthCheckResponse_ServingStatus_NOT_SERVING;
const int HealthCheckResponse_ServingStatus_ServingStatus_ARRAYSIZE = HealthCheckResponse_ServingStatus_ServingStatus_MAX + 1;

const ::google::protobuf::EnumDescriptor* HealthCheckResponse_ServingStatus_descriptor();
inline const ::std::string& HealthCheckResponse_ServingStatus_Name(HealthCheckResponse_ServingStatus value) {
  return ::google::protobuf::internal::NameOfEnum(
    HealthCheckResponse_ServingStatus_descriptor(), value);
}
inline bool HealthCheckResponse_ServingStatus_Parse(
    const ::std::string& name, HealthCheckResponse_ServingStatus* value) {
  return ::google::protobuf::internal::ParseNamedEnum<HealthCheckResponse_ServingStatus>(
    HealthCheckResponse_ServingStatus_descriptor(), name, value);
}
// ===================================================================

class HealthCheckRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.health.v1.HealthCheckRequest) */ {
 public:
  HealthCheckRequest();
  virtual ~HealthCheckRequest();

  HealthCheckRequest(const HealthCheckRequest& from);

  inline HealthCheckRequest& operator=(const HealthCheckRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  HealthCheckRequest(HealthCheckRequest&& from) noexcept
    : HealthCheckRequest() {
    *this = ::std::move(from);
  }

  inline HealthCheckRequest& operator=(HealthCheckRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const HealthCheckRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const HealthCheckRequest* internal_default_instance() {
    return reinterpret_cast<const HealthCheckRequest*>(
               &_HealthCheckRequest_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    0;

  void Swap(HealthCheckRequest* other);
  friend void swap(HealthCheckRequest& a, HealthCheckRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline HealthCheckRequest* New() const PROTOBUF_FINAL { return New(NULL); }

  HealthCheckRequest* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const HealthCheckRequest& from);
  void MergeFrom(const HealthCheckRequest& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(HealthCheckRequest* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string service = 1;
  void clear_service();
  static const int kServiceFieldNumber = 1;
  const ::std::string& service() const;
  void set_service(const ::std::string& value);
  #if LANG_CXX11
  void set_service(::std::string&& value);
  #endif
  void set_service(const char* value);
  void set_service(const char* value, size_t size);
  ::std::string* mutable_service();
  ::std::string* release_service();
  void set_allocated_service(::std::string* service);

  // @@protoc_insertion_point(class_scope:grpc.health.v1.HealthCheckRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr service_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fhealth_2fv1_2fhealth_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fhealth_2fv1_2fhealth_2eproto::InitDefaultsHealthCheckRequestImpl();
};
// -------------------------------------------------------------------

class HealthCheckResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.health.v1.HealthCheckResponse) */ {
 public:
  HealthCheckResponse();
  virtual ~HealthCheckResponse();

  HealthCheckResponse(const HealthCheckResponse& from);

  inline HealthCheckResponse& operator=(const HealthCheckResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  HealthCheckResponse(HealthCheckResponse&& from) noexcept
    : HealthCheckResponse() {
    *this = ::std::move(from);
  }

  inline HealthCheckResponse& operator=(HealthCheckResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const HealthCheckResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const HealthCheckResponse* internal_default_instance() {
    return reinterpret_cast<const HealthCheckResponse*>(
               &_HealthCheckResponse_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    1;

  void Swap(HealthCheckResponse* other);
  friend void swap(HealthCheckResponse& a, HealthCheckResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline HealthCheckResponse* New() const PROTOBUF_FINAL { return New(NULL); }

  HealthCheckResponse* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const HealthCheckResponse& from);
  void MergeFrom(const HealthCheckResponse& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(HealthCheckResponse* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  typedef HealthCheckResponse_ServingStatus ServingStatus;
  static const ServingStatus UNKNOWN =
    HealthCheckResponse_ServingStatus_UNKNOWN;
  static const ServingStatus SERVING =
    HealthCheckResponse_ServingStatus_SERVING;
  static const ServingStatus NOT_SERVING =
    HealthCheckResponse_ServingStatus_NOT_SERVING;
  static inline bool ServingStatus_IsValid(int value) {
    return HealthCheckResponse_ServingStatus_IsValid(value);
  }
  static const ServingStatus ServingStatus_MIN =
    HealthCheckResponse_ServingStatus_ServingStatus_MIN;
  static const ServingStatus ServingStatus_MAX =
    HealthCheckResponse_ServingStatus_ServingStatus_MAX;
  static const int ServingStatus_ARRAYSIZE =
    HealthCheckResponse_ServingStatus_ServingStatus_ARRAYSIZE;
  static inline const ::google::protobuf::EnumDescriptor*
  ServingStatus_descriptor() {
    return HealthCheckResponse_ServingStatus_descriptor();
  }
  static inline const ::std::string& ServingStatus_Name(ServingStatus value) {
    return HealthCheckResponse_ServingStatus_Name(value);
  }
  static inline bool ServingStatus_Parse(const ::std::string& name,
      ServingStatus* value) {
    return HealthCheckResponse_ServingStatus_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  // .grpc.health.v1.HealthCheckResponse.ServingStatus status = 1;
  void clear_status();
  static const int kStatusFieldNumber = 1;
  ::grpc::health::v1::HealthCheckResponse_ServingStatus status() const;
  void set_status(::grpc::health::v1::HealthCheckResponse_ServingStatus value);

  // @@protoc_insertion_point(class_scope:grpc.health.v1.HealthCheckResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  int status_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2fhealth_2fv1_2fhealth_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2fhealth_2fv1_2fhealth_2eproto::InitDefaultsHealthCheckResponseImpl();
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// HealthCheckRequest

// string service = 1;
inline void HealthCheckRequest::clear_service() {
  service_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& HealthCheckRequest::service() const {
  // @@protoc_insertion_point(field_get:grpc.health.v1.HealthCheckRequest.service)
  return service_.GetNoArena();
}
inline void HealthCheckRequest::set_service(const ::std::string& value) {
  
  service_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.health.v1.HealthCheckRequest.service)
}
#if LANG_CXX11
inline void HealthCheckRequest::set_service(::std::string&& value) {
  
  service_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.health.v1.HealthCheckRequest.service)
}
#endif
inline void HealthCheckRequest::set_service(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  service_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.health.v1.HealthCheckRequest.service)
}
inline void HealthCheckRequest::set_service(const char* value, size_t size) {
  
  service_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.health.v1.HealthCheckRequest.service)
}
inline ::std::string* HealthCheckRequest::mutable_service() {
  
  // @@protoc_insertion_point(field_mutable:grpc.health.v1.HealthCheckRequest.service)
  return service_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* HealthCheckRequest::release_service() {
  // @@protoc_insertion_point(field_release:grpc.health.v1.HealthCheckRequest.service)
  
  return service_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void HealthCheckRequest::set_allocated_service(::std::string* service) {
  if (service != NULL) {
    
  } else {
    
  }
  service_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), service);
  // @@protoc_insertion_point(field_set_allocated:grpc.health.v1.HealthCheckRequest.service)
}

// -------------------------------------------------------------------

// HealthCheckResponse

// .grpc.health.v1.HealthCheckResponse.ServingStatus status = 1;
inline void HealthCheckResponse::clear_status() {
  status_ = 0;
}
inline ::grpc::health::v1::HealthCheckResponse_ServingStatus HealthCheckResponse::status() const {
  // @@protoc_insertion_point(field_get:grpc.health.v1.HealthCheckResponse.status)
  return static_cast< ::grpc::health::v1::HealthCheckResponse_ServingStatus >(status_);
}
inline void HealthCheckResponse::set_status(::grpc::health::v1::HealthCheckResponse_ServingStatus value) {
  
  status_ = value;
  // @@protoc_insertion_point(field_set:grpc.health.v1.HealthCheckResponse.status)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace v1
}  // namespace health
}  // namespace grpc

namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::grpc::health::v1::HealthCheckResponse_ServingStatus> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::grpc::health::v1::HealthCheckResponse_ServingStatus>() {
  return ::grpc::health::v1::HealthCheckResponse_ServingStatus_descriptor();
}

}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_src_2fproto_2fgrpc_2fhealth_2fv1_2fhealth_2eproto__INCLUDED
