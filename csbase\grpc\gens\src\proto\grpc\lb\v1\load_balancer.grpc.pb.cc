// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: src/proto/grpc/lb/v1/load_balancer.proto

#include "src/proto/grpc/lb/v1/load_balancer.pb.h"
#include "src/proto/grpc/lb/v1/load_balancer.grpc.pb.h"

#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/method_handler_impl.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace grpc {
namespace lb {
namespace v1 {

static const char* LoadBalancer_method_names[] = {
  "/grpc.lb.v1.LoadBalancer/BalanceLoad",
};

std::unique_ptr< LoadBalancer::Stub> LoadBalancer::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< LoadBalancer::Stub> stub(new LoadBalancer::Stub(channel));
  return stub;
}

LoadBalancer::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel)
  : channel_(channel), rpcmethod_BalanceLoad_(LoadBalancer_method_names[0], ::grpc::internal::RpcMethod::BIDI_STREAMING, channel)
  {}

::grpc::ClientReaderWriter< ::grpc::lb::v1::LoadBalanceRequest, ::grpc::lb::v1::LoadBalanceResponse>* LoadBalancer::Stub::BalanceLoadRaw(::grpc::ClientContext* context) {
  return ::grpc::internal::ClientReaderWriterFactory< ::grpc::lb::v1::LoadBalanceRequest, ::grpc::lb::v1::LoadBalanceResponse>::Create(channel_.get(), rpcmethod_BalanceLoad_, context);
}

::grpc::ClientAsyncReaderWriter< ::grpc::lb::v1::LoadBalanceRequest, ::grpc::lb::v1::LoadBalanceResponse>* LoadBalancer::Stub::AsyncBalanceLoadRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncReaderWriterFactory< ::grpc::lb::v1::LoadBalanceRequest, ::grpc::lb::v1::LoadBalanceResponse>::Create(channel_.get(), cq, rpcmethod_BalanceLoad_, context, true, tag);
}

::grpc::ClientAsyncReaderWriter< ::grpc::lb::v1::LoadBalanceRequest, ::grpc::lb::v1::LoadBalanceResponse>* LoadBalancer::Stub::PrepareAsyncBalanceLoadRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncReaderWriterFactory< ::grpc::lb::v1::LoadBalanceRequest, ::grpc::lb::v1::LoadBalanceResponse>::Create(channel_.get(), cq, rpcmethod_BalanceLoad_, context, false, nullptr);
}

LoadBalancer::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      LoadBalancer_method_names[0],
      ::grpc::internal::RpcMethod::BIDI_STREAMING,
      new ::grpc::internal::BidiStreamingHandler< LoadBalancer::Service, ::grpc::lb::v1::LoadBalanceRequest, ::grpc::lb::v1::LoadBalanceResponse>(
          std::mem_fn(&LoadBalancer::Service::BalanceLoad), this)));
}

LoadBalancer::Service::~Service() {
}

::grpc::Status LoadBalancer::Service::BalanceLoad(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::lb::v1::LoadBalanceResponse, ::grpc::lb::v1::LoadBalanceRequest>* stream) {
  (void) context;
  (void) stream;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace grpc
}  // namespace lb
}  // namespace v1

