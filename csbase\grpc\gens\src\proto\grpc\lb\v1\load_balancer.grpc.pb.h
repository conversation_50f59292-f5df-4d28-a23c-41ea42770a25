// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: src/proto/grpc/lb/v1/load_balancer.proto
// Original file comments:
// Copyright 2015 The gRPC Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// This file defines the GRPCLB LoadBalancing protocol.
//
// The canonical version of this proto can be found at
// https://github.com/grpc/grpc-proto/blob/master/grpc/lb/v1/load_balancer.proto
#ifndef GRPC_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto__INCLUDED
#define GRPC_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto__INCLUDED

#include "src/proto/grpc/lb/v1/load_balancer.pb.h"

#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/method_handler_impl.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace grpc {
class CompletionQueue;
class Channel;
class ServerCompletionQueue;
class ServerContext;
}  // namespace grpc

namespace grpc {
namespace lb {
namespace v1 {

class LoadBalancer final {
 public:
  static constexpr char const* service_full_name() {
    return "grpc.lb.v1.LoadBalancer";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    // Bidirectional rpc to get a list of servers.
    std::unique_ptr< ::grpc::ClientReaderWriterInterface< ::grpc::lb::v1::LoadBalanceRequest, ::grpc::lb::v1::LoadBalanceResponse>> BalanceLoad(::grpc::ClientContext* context) {
      return std::unique_ptr< ::grpc::ClientReaderWriterInterface< ::grpc::lb::v1::LoadBalanceRequest, ::grpc::lb::v1::LoadBalanceResponse>>(BalanceLoadRaw(context));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::grpc::lb::v1::LoadBalanceRequest, ::grpc::lb::v1::LoadBalanceResponse>> AsyncBalanceLoad(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::grpc::lb::v1::LoadBalanceRequest, ::grpc::lb::v1::LoadBalanceResponse>>(AsyncBalanceLoadRaw(context, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::grpc::lb::v1::LoadBalanceRequest, ::grpc::lb::v1::LoadBalanceResponse>> PrepareAsyncBalanceLoad(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::grpc::lb::v1::LoadBalanceRequest, ::grpc::lb::v1::LoadBalanceResponse>>(PrepareAsyncBalanceLoadRaw(context, cq));
    }
  private:
    virtual ::grpc::ClientReaderWriterInterface< ::grpc::lb::v1::LoadBalanceRequest, ::grpc::lb::v1::LoadBalanceResponse>* BalanceLoadRaw(::grpc::ClientContext* context) = 0;
    virtual ::grpc::ClientAsyncReaderWriterInterface< ::grpc::lb::v1::LoadBalanceRequest, ::grpc::lb::v1::LoadBalanceResponse>* AsyncBalanceLoadRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) = 0;
    virtual ::grpc::ClientAsyncReaderWriterInterface< ::grpc::lb::v1::LoadBalanceRequest, ::grpc::lb::v1::LoadBalanceResponse>* PrepareAsyncBalanceLoadRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel);
    std::unique_ptr< ::grpc::ClientReaderWriter< ::grpc::lb::v1::LoadBalanceRequest, ::grpc::lb::v1::LoadBalanceResponse>> BalanceLoad(::grpc::ClientContext* context) {
      return std::unique_ptr< ::grpc::ClientReaderWriter< ::grpc::lb::v1::LoadBalanceRequest, ::grpc::lb::v1::LoadBalanceResponse>>(BalanceLoadRaw(context));
    }
    std::unique_ptr<  ::grpc::ClientAsyncReaderWriter< ::grpc::lb::v1::LoadBalanceRequest, ::grpc::lb::v1::LoadBalanceResponse>> AsyncBalanceLoad(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriter< ::grpc::lb::v1::LoadBalanceRequest, ::grpc::lb::v1::LoadBalanceResponse>>(AsyncBalanceLoadRaw(context, cq, tag));
    }
    std::unique_ptr<  ::grpc::ClientAsyncReaderWriter< ::grpc::lb::v1::LoadBalanceRequest, ::grpc::lb::v1::LoadBalanceResponse>> PrepareAsyncBalanceLoad(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriter< ::grpc::lb::v1::LoadBalanceRequest, ::grpc::lb::v1::LoadBalanceResponse>>(PrepareAsyncBalanceLoadRaw(context, cq));
    }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    ::grpc::ClientReaderWriter< ::grpc::lb::v1::LoadBalanceRequest, ::grpc::lb::v1::LoadBalanceResponse>* BalanceLoadRaw(::grpc::ClientContext* context) override;
    ::grpc::ClientAsyncReaderWriter< ::grpc::lb::v1::LoadBalanceRequest, ::grpc::lb::v1::LoadBalanceResponse>* AsyncBalanceLoadRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) override;
    ::grpc::ClientAsyncReaderWriter< ::grpc::lb::v1::LoadBalanceRequest, ::grpc::lb::v1::LoadBalanceResponse>* PrepareAsyncBalanceLoadRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_BalanceLoad_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    // Bidirectional rpc to get a list of servers.
    virtual ::grpc::Status BalanceLoad(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::lb::v1::LoadBalanceResponse, ::grpc::lb::v1::LoadBalanceRequest>* stream);
  };
  template <class BaseClass>
  class WithAsyncMethod_BalanceLoad : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_BalanceLoad() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_BalanceLoad() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status BalanceLoad(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::lb::v1::LoadBalanceResponse, ::grpc::lb::v1::LoadBalanceRequest>* stream)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestBalanceLoad(::grpc::ServerContext* context, ::grpc::ServerAsyncReaderWriter< ::grpc::lb::v1::LoadBalanceResponse, ::grpc::lb::v1::LoadBalanceRequest>* stream, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncBidiStreaming(0, context, stream, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_BalanceLoad<Service > AsyncService;
  template <class BaseClass>
  class WithGenericMethod_BalanceLoad : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_BalanceLoad() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_BalanceLoad() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status BalanceLoad(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::lb::v1::LoadBalanceResponse, ::grpc::lb::v1::LoadBalanceRequest>* stream)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_BalanceLoad : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_BalanceLoad() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_BalanceLoad() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status BalanceLoad(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::lb::v1::LoadBalanceResponse, ::grpc::lb::v1::LoadBalanceRequest>* stream)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestBalanceLoad(::grpc::ServerContext* context, ::grpc::ServerAsyncReaderWriter< ::grpc::ByteBuffer, ::grpc::ByteBuffer>* stream, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncBidiStreaming(0, context, stream, new_call_cq, notification_cq, tag);
    }
  };
  typedef Service StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef Service StreamedService;
};

}  // namespace v1
}  // namespace lb
}  // namespace grpc


#endif  // GRPC_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto__INCLUDED
