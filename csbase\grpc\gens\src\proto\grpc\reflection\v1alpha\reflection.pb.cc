// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: src/proto/grpc/reflection/v1alpha/reflection.proto

#include "src/proto/grpc/reflection/v1alpha/reflection.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)
namespace grpc {
namespace reflection {
namespace v1alpha {
class ServerReflectionRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ServerReflectionRequest>
      _instance;
  ::google::protobuf::internal::ArenaStringPtr file_by_filename_;
  ::google::protobuf::internal::ArenaStringPtr file_containing_symbol_;
  const ::grpc::reflection::v1alpha::ExtensionRequest* file_containing_extension_;
  ::google::protobuf::internal::ArenaStringPtr all_extension_numbers_of_type_;
  ::google::protobuf::internal::ArenaStringPtr list_services_;
} _ServerReflectionRequest_default_instance_;
class ExtensionRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ExtensionRequest>
      _instance;
} _ExtensionRequest_default_instance_;
class ServerReflectionResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ServerReflectionResponse>
      _instance;
  const ::grpc::reflection::v1alpha::FileDescriptorResponse* file_descriptor_response_;
  const ::grpc::reflection::v1alpha::ExtensionNumberResponse* all_extension_numbers_response_;
  const ::grpc::reflection::v1alpha::ListServiceResponse* list_services_response_;
  const ::grpc::reflection::v1alpha::ErrorResponse* error_response_;
} _ServerReflectionResponse_default_instance_;
class FileDescriptorResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<FileDescriptorResponse>
      _instance;
} _FileDescriptorResponse_default_instance_;
class ExtensionNumberResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ExtensionNumberResponse>
      _instance;
} _ExtensionNumberResponse_default_instance_;
class ListServiceResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ListServiceResponse>
      _instance;
} _ListServiceResponse_default_instance_;
class ServiceResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ServiceResponse>
      _instance;
} _ServiceResponse_default_instance_;
class ErrorResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ErrorResponse>
      _instance;
} _ErrorResponse_default_instance_;
}  // namespace v1alpha
}  // namespace reflection
}  // namespace grpc
namespace protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto {
void InitDefaultsServerReflectionRequestImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsExtensionRequest();
  {
    void* ptr = &::grpc::reflection::v1alpha::_ServerReflectionRequest_default_instance_;
    new (ptr) ::grpc::reflection::v1alpha::ServerReflectionRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::reflection::v1alpha::ServerReflectionRequest::InitAsDefaultInstance();
}

void InitDefaultsServerReflectionRequest() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsServerReflectionRequestImpl);
}

void InitDefaultsExtensionRequestImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::reflection::v1alpha::_ExtensionRequest_default_instance_;
    new (ptr) ::grpc::reflection::v1alpha::ExtensionRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::reflection::v1alpha::ExtensionRequest::InitAsDefaultInstance();
}

void InitDefaultsExtensionRequest() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsExtensionRequestImpl);
}

void InitDefaultsServerReflectionResponseImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsServerReflectionRequest();
  protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsFileDescriptorResponse();
  protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsExtensionNumberResponse();
  protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsListServiceResponse();
  protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsErrorResponse();
  {
    void* ptr = &::grpc::reflection::v1alpha::_ServerReflectionResponse_default_instance_;
    new (ptr) ::grpc::reflection::v1alpha::ServerReflectionResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::reflection::v1alpha::ServerReflectionResponse::InitAsDefaultInstance();
}

void InitDefaultsServerReflectionResponse() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsServerReflectionResponseImpl);
}

void InitDefaultsFileDescriptorResponseImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::reflection::v1alpha::_FileDescriptorResponse_default_instance_;
    new (ptr) ::grpc::reflection::v1alpha::FileDescriptorResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::reflection::v1alpha::FileDescriptorResponse::InitAsDefaultInstance();
}

void InitDefaultsFileDescriptorResponse() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsFileDescriptorResponseImpl);
}

void InitDefaultsExtensionNumberResponseImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::reflection::v1alpha::_ExtensionNumberResponse_default_instance_;
    new (ptr) ::grpc::reflection::v1alpha::ExtensionNumberResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::reflection::v1alpha::ExtensionNumberResponse::InitAsDefaultInstance();
}

void InitDefaultsExtensionNumberResponse() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsExtensionNumberResponseImpl);
}

void InitDefaultsListServiceResponseImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsServiceResponse();
  {
    void* ptr = &::grpc::reflection::v1alpha::_ListServiceResponse_default_instance_;
    new (ptr) ::grpc::reflection::v1alpha::ListServiceResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::reflection::v1alpha::ListServiceResponse::InitAsDefaultInstance();
}

void InitDefaultsListServiceResponse() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsListServiceResponseImpl);
}

void InitDefaultsServiceResponseImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::reflection::v1alpha::_ServiceResponse_default_instance_;
    new (ptr) ::grpc::reflection::v1alpha::ServiceResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::reflection::v1alpha::ServiceResponse::InitAsDefaultInstance();
}

void InitDefaultsServiceResponse() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsServiceResponseImpl);
}

void InitDefaultsErrorResponseImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::reflection::v1alpha::_ErrorResponse_default_instance_;
    new (ptr) ::grpc::reflection::v1alpha::ErrorResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::reflection::v1alpha::ErrorResponse::InitAsDefaultInstance();
}

void InitDefaultsErrorResponse() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsErrorResponseImpl);
}

::google::protobuf::Metadata file_level_metadata[8];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::reflection::v1alpha::ServerReflectionRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::reflection::v1alpha::ServerReflectionRequest, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::reflection::v1alpha::ServerReflectionRequest, host_),
  offsetof(::grpc::reflection::v1alpha::ServerReflectionRequestDefaultTypeInternal, file_by_filename_),
  offsetof(::grpc::reflection::v1alpha::ServerReflectionRequestDefaultTypeInternal, file_containing_symbol_),
  offsetof(::grpc::reflection::v1alpha::ServerReflectionRequestDefaultTypeInternal, file_containing_extension_),
  offsetof(::grpc::reflection::v1alpha::ServerReflectionRequestDefaultTypeInternal, all_extension_numbers_of_type_),
  offsetof(::grpc::reflection::v1alpha::ServerReflectionRequestDefaultTypeInternal, list_services_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::reflection::v1alpha::ServerReflectionRequest, message_request_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::reflection::v1alpha::ExtensionRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::reflection::v1alpha::ExtensionRequest, containing_type_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::reflection::v1alpha::ExtensionRequest, extension_number_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::reflection::v1alpha::ServerReflectionResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::reflection::v1alpha::ServerReflectionResponse, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::reflection::v1alpha::ServerReflectionResponse, valid_host_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::reflection::v1alpha::ServerReflectionResponse, original_request_),
  offsetof(::grpc::reflection::v1alpha::ServerReflectionResponseDefaultTypeInternal, file_descriptor_response_),
  offsetof(::grpc::reflection::v1alpha::ServerReflectionResponseDefaultTypeInternal, all_extension_numbers_response_),
  offsetof(::grpc::reflection::v1alpha::ServerReflectionResponseDefaultTypeInternal, list_services_response_),
  offsetof(::grpc::reflection::v1alpha::ServerReflectionResponseDefaultTypeInternal, error_response_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::reflection::v1alpha::ServerReflectionResponse, message_response_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::reflection::v1alpha::FileDescriptorResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::reflection::v1alpha::FileDescriptorResponse, file_descriptor_proto_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::reflection::v1alpha::ExtensionNumberResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::reflection::v1alpha::ExtensionNumberResponse, base_type_name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::reflection::v1alpha::ExtensionNumberResponse, extension_number_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::reflection::v1alpha::ListServiceResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::reflection::v1alpha::ListServiceResponse, service_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::reflection::v1alpha::ServiceResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::reflection::v1alpha::ServiceResponse, name_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::reflection::v1alpha::ErrorResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::reflection::v1alpha::ErrorResponse, error_code_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::reflection::v1alpha::ErrorResponse, error_message_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::grpc::reflection::v1alpha::ServerReflectionRequest)},
  { 12, -1, sizeof(::grpc::reflection::v1alpha::ExtensionRequest)},
  { 19, -1, sizeof(::grpc::reflection::v1alpha::ServerReflectionResponse)},
  { 31, -1, sizeof(::grpc::reflection::v1alpha::FileDescriptorResponse)},
  { 37, -1, sizeof(::grpc::reflection::v1alpha::ExtensionNumberResponse)},
  { 44, -1, sizeof(::grpc::reflection::v1alpha::ListServiceResponse)},
  { 50, -1, sizeof(::grpc::reflection::v1alpha::ServiceResponse)},
  { 56, -1, sizeof(::grpc::reflection::v1alpha::ErrorResponse)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::reflection::v1alpha::_ServerReflectionRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::reflection::v1alpha::_ExtensionRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::reflection::v1alpha::_ServerReflectionResponse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::reflection::v1alpha::_FileDescriptorResponse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::reflection::v1alpha::_ExtensionNumberResponse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::reflection::v1alpha::_ListServiceResponse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::reflection::v1alpha::_ServiceResponse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::reflection::v1alpha::_ErrorResponse_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  ::google::protobuf::MessageFactory* factory = NULL;
  AssignDescriptors(
      "src/proto/grpc/reflection/v1alpha/reflection.proto", schemas, file_default_instances, TableStruct::offsets, factory,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 8);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n2src/proto/grpc/reflection/v1alpha/refl"
      "ection.proto\022\027grpc.reflection.v1alpha\"\212\002"
      "\n\027ServerReflectionRequest\022\014\n\004host\030\001 \001(\t\022"
      "\032\n\020file_by_filename\030\003 \001(\tH\000\022 \n\026file_cont"
      "aining_symbol\030\004 \001(\tH\000\022N\n\031file_containing"
      "_extension\030\005 \001(\0132).grpc.reflection.v1alp"
      "ha.ExtensionRequestH\000\022\'\n\035all_extension_n"
      "umbers_of_type\030\006 \001(\tH\000\022\027\n\rlist_services\030"
      "\007 \001(\tH\000B\021\n\017message_request\"E\n\020ExtensionR"
      "equest\022\027\n\017containing_type\030\001 \001(\t\022\030\n\020exten"
      "sion_number\030\002 \001(\005\"\321\003\n\030ServerReflectionRe"
      "sponse\022\022\n\nvalid_host\030\001 \001(\t\022J\n\020original_r"
      "equest\030\002 \001(\01320.grpc.reflection.v1alpha.S"
      "erverReflectionRequest\022S\n\030file_descripto"
      "r_response\030\004 \001(\0132/.grpc.reflection.v1alp"
      "ha.FileDescriptorResponseH\000\022Z\n\036all_exten"
      "sion_numbers_response\030\005 \001(\01320.grpc.refle"
      "ction.v1alpha.ExtensionNumberResponseH\000\022"
      "N\n\026list_services_response\030\006 \001(\0132,.grpc.r"
      "eflection.v1alpha.ListServiceResponseH\000\022"
      "@\n\016error_response\030\007 \001(\0132&.grpc.reflectio"
      "n.v1alpha.ErrorResponseH\000B\022\n\020message_res"
      "ponse\"7\n\026FileDescriptorResponse\022\035\n\025file_"
      "descriptor_proto\030\001 \003(\014\"K\n\027ExtensionNumbe"
      "rResponse\022\026\n\016base_type_name\030\001 \001(\t\022\030\n\020ext"
      "ension_number\030\002 \003(\005\"P\n\023ListServiceRespon"
      "se\0229\n\007service\030\001 \003(\0132(.grpc.reflection.v1"
      "alpha.ServiceResponse\"\037\n\017ServiceResponse"
      "\022\014\n\004name\030\001 \001(\t\":\n\rErrorResponse\022\022\n\nerror"
      "_code\030\001 \001(\005\022\025\n\rerror_message\030\002 \001(\t2\223\001\n\020S"
      "erverReflection\022\177\n\024ServerReflectionInfo\022"
      "0.grpc.reflection.v1alpha.ServerReflecti"
      "onRequest\0321.grpc.reflection.v1alpha.Serv"
      "erReflectionResponse(\0010\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 1352);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "src/proto/grpc/reflection/v1alpha/reflection.proto", &protobuf_RegisterTypes);
}

void AddDescriptors() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto
namespace grpc {
namespace reflection {
namespace v1alpha {

// ===================================================================

void ServerReflectionRequest::InitAsDefaultInstance() {
  ::grpc::reflection::v1alpha::_ServerReflectionRequest_default_instance_.file_by_filename_.UnsafeSetDefault(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::grpc::reflection::v1alpha::_ServerReflectionRequest_default_instance_.file_containing_symbol_.UnsafeSetDefault(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::grpc::reflection::v1alpha::_ServerReflectionRequest_default_instance_.file_containing_extension_ = const_cast< ::grpc::reflection::v1alpha::ExtensionRequest*>(
      ::grpc::reflection::v1alpha::ExtensionRequest::internal_default_instance());
  ::grpc::reflection::v1alpha::_ServerReflectionRequest_default_instance_.all_extension_numbers_of_type_.UnsafeSetDefault(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::grpc::reflection::v1alpha::_ServerReflectionRequest_default_instance_.list_services_.UnsafeSetDefault(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
void ServerReflectionRequest::set_allocated_file_containing_extension(::grpc::reflection::v1alpha::ExtensionRequest* file_containing_extension) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_message_request();
  if (file_containing_extension) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      file_containing_extension = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, file_containing_extension, submessage_arena);
    }
    set_has_file_containing_extension();
    message_request_.file_containing_extension_ = file_containing_extension;
  }
  // @@protoc_insertion_point(field_set_allocated:grpc.reflection.v1alpha.ServerReflectionRequest.file_containing_extension)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ServerReflectionRequest::kHostFieldNumber;
const int ServerReflectionRequest::kFileByFilenameFieldNumber;
const int ServerReflectionRequest::kFileContainingSymbolFieldNumber;
const int ServerReflectionRequest::kFileContainingExtensionFieldNumber;
const int ServerReflectionRequest::kAllExtensionNumbersOfTypeFieldNumber;
const int ServerReflectionRequest::kListServicesFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ServerReflectionRequest::ServerReflectionRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsServerReflectionRequest();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.reflection.v1alpha.ServerReflectionRequest)
}
ServerReflectionRequest::ServerReflectionRequest(const ServerReflectionRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  host_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.host().size() > 0) {
    host_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.host_);
  }
  clear_has_message_request();
  switch (from.message_request_case()) {
    case kFileByFilename: {
      set_file_by_filename(from.file_by_filename());
      break;
    }
    case kFileContainingSymbol: {
      set_file_containing_symbol(from.file_containing_symbol());
      break;
    }
    case kFileContainingExtension: {
      mutable_file_containing_extension()->::grpc::reflection::v1alpha::ExtensionRequest::MergeFrom(from.file_containing_extension());
      break;
    }
    case kAllExtensionNumbersOfType: {
      set_all_extension_numbers_of_type(from.all_extension_numbers_of_type());
      break;
    }
    case kListServices: {
      set_list_services(from.list_services());
      break;
    }
    case MESSAGE_REQUEST_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:grpc.reflection.v1alpha.ServerReflectionRequest)
}

void ServerReflectionRequest::SharedCtor() {
  host_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_message_request();
  _cached_size_ = 0;
}

ServerReflectionRequest::~ServerReflectionRequest() {
  // @@protoc_insertion_point(destructor:grpc.reflection.v1alpha.ServerReflectionRequest)
  SharedDtor();
}

void ServerReflectionRequest::SharedDtor() {
  host_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (has_message_request()) {
    clear_message_request();
  }
}

void ServerReflectionRequest::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ServerReflectionRequest::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ServerReflectionRequest& ServerReflectionRequest::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsServerReflectionRequest();
  return *internal_default_instance();
}

ServerReflectionRequest* ServerReflectionRequest::New(::google::protobuf::Arena* arena) const {
  ServerReflectionRequest* n = new ServerReflectionRequest;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ServerReflectionRequest::clear_message_request() {
// @@protoc_insertion_point(one_of_clear_start:grpc.reflection.v1alpha.ServerReflectionRequest)
  switch (message_request_case()) {
    case kFileByFilename: {
      message_request_.file_by_filename_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
      break;
    }
    case kFileContainingSymbol: {
      message_request_.file_containing_symbol_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
      break;
    }
    case kFileContainingExtension: {
      delete message_request_.file_containing_extension_;
      break;
    }
    case kAllExtensionNumbersOfType: {
      message_request_.all_extension_numbers_of_type_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
      break;
    }
    case kListServices: {
      message_request_.list_services_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
      break;
    }
    case MESSAGE_REQUEST_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = MESSAGE_REQUEST_NOT_SET;
}


void ServerReflectionRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.reflection.v1alpha.ServerReflectionRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  host_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_message_request();
  _internal_metadata_.Clear();
}

bool ServerReflectionRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.reflection.v1alpha.ServerReflectionRequest)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string host = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_host()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->host().data(), static_cast<int>(this->host().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.reflection.v1alpha.ServerReflectionRequest.host"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string file_by_filename = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_file_by_filename()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->file_by_filename().data(), static_cast<int>(this->file_by_filename().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.reflection.v1alpha.ServerReflectionRequest.file_by_filename"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string file_containing_symbol = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_file_containing_symbol()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->file_containing_symbol().data(), static_cast<int>(this->file_containing_symbol().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.reflection.v1alpha.ServerReflectionRequest.file_containing_symbol"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.reflection.v1alpha.ExtensionRequest file_containing_extension = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_file_containing_extension()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string all_extension_numbers_of_type = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_all_extension_numbers_of_type()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->all_extension_numbers_of_type().data(), static_cast<int>(this->all_extension_numbers_of_type().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.reflection.v1alpha.ServerReflectionRequest.all_extension_numbers_of_type"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string list_services = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_list_services()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->list_services().data(), static_cast<int>(this->list_services().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.reflection.v1alpha.ServerReflectionRequest.list_services"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.reflection.v1alpha.ServerReflectionRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.reflection.v1alpha.ServerReflectionRequest)
  return false;
#undef DO_
}

void ServerReflectionRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.reflection.v1alpha.ServerReflectionRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string host = 1;
  if (this->host().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->host().data(), static_cast<int>(this->host().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.reflection.v1alpha.ServerReflectionRequest.host");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->host(), output);
  }

  // string file_by_filename = 3;
  if (has_file_by_filename()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->file_by_filename().data(), static_cast<int>(this->file_by_filename().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.reflection.v1alpha.ServerReflectionRequest.file_by_filename");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->file_by_filename(), output);
  }

  // string file_containing_symbol = 4;
  if (has_file_containing_symbol()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->file_containing_symbol().data(), static_cast<int>(this->file_containing_symbol().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.reflection.v1alpha.ServerReflectionRequest.file_containing_symbol");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->file_containing_symbol(), output);
  }

  // .grpc.reflection.v1alpha.ExtensionRequest file_containing_extension = 5;
  if (has_file_containing_extension()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, *message_request_.file_containing_extension_, output);
  }

  // string all_extension_numbers_of_type = 6;
  if (has_all_extension_numbers_of_type()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->all_extension_numbers_of_type().data(), static_cast<int>(this->all_extension_numbers_of_type().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.reflection.v1alpha.ServerReflectionRequest.all_extension_numbers_of_type");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      6, this->all_extension_numbers_of_type(), output);
  }

  // string list_services = 7;
  if (has_list_services()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->list_services().data(), static_cast<int>(this->list_services().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.reflection.v1alpha.ServerReflectionRequest.list_services");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      7, this->list_services(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.reflection.v1alpha.ServerReflectionRequest)
}

::google::protobuf::uint8* ServerReflectionRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.reflection.v1alpha.ServerReflectionRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string host = 1;
  if (this->host().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->host().data(), static_cast<int>(this->host().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.reflection.v1alpha.ServerReflectionRequest.host");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->host(), target);
  }

  // string file_by_filename = 3;
  if (has_file_by_filename()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->file_by_filename().data(), static_cast<int>(this->file_by_filename().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.reflection.v1alpha.ServerReflectionRequest.file_by_filename");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->file_by_filename(), target);
  }

  // string file_containing_symbol = 4;
  if (has_file_containing_symbol()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->file_containing_symbol().data(), static_cast<int>(this->file_containing_symbol().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.reflection.v1alpha.ServerReflectionRequest.file_containing_symbol");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->file_containing_symbol(), target);
  }

  // .grpc.reflection.v1alpha.ExtensionRequest file_containing_extension = 5;
  if (has_file_containing_extension()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        5, *message_request_.file_containing_extension_, deterministic, target);
  }

  // string all_extension_numbers_of_type = 6;
  if (has_all_extension_numbers_of_type()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->all_extension_numbers_of_type().data(), static_cast<int>(this->all_extension_numbers_of_type().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.reflection.v1alpha.ServerReflectionRequest.all_extension_numbers_of_type");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        6, this->all_extension_numbers_of_type(), target);
  }

  // string list_services = 7;
  if (has_list_services()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->list_services().data(), static_cast<int>(this->list_services().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.reflection.v1alpha.ServerReflectionRequest.list_services");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        7, this->list_services(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.reflection.v1alpha.ServerReflectionRequest)
  return target;
}

size_t ServerReflectionRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.reflection.v1alpha.ServerReflectionRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string host = 1;
  if (this->host().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->host());
  }

  switch (message_request_case()) {
    // string file_by_filename = 3;
    case kFileByFilename: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->file_by_filename());
      break;
    }
    // string file_containing_symbol = 4;
    case kFileContainingSymbol: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->file_containing_symbol());
      break;
    }
    // .grpc.reflection.v1alpha.ExtensionRequest file_containing_extension = 5;
    case kFileContainingExtension: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *message_request_.file_containing_extension_);
      break;
    }
    // string all_extension_numbers_of_type = 6;
    case kAllExtensionNumbersOfType: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->all_extension_numbers_of_type());
      break;
    }
    // string list_services = 7;
    case kListServices: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->list_services());
      break;
    }
    case MESSAGE_REQUEST_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ServerReflectionRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.reflection.v1alpha.ServerReflectionRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const ServerReflectionRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ServerReflectionRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.reflection.v1alpha.ServerReflectionRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.reflection.v1alpha.ServerReflectionRequest)
    MergeFrom(*source);
  }
}

void ServerReflectionRequest::MergeFrom(const ServerReflectionRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.reflection.v1alpha.ServerReflectionRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.host().size() > 0) {

    host_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.host_);
  }
  switch (from.message_request_case()) {
    case kFileByFilename: {
      set_file_by_filename(from.file_by_filename());
      break;
    }
    case kFileContainingSymbol: {
      set_file_containing_symbol(from.file_containing_symbol());
      break;
    }
    case kFileContainingExtension: {
      mutable_file_containing_extension()->::grpc::reflection::v1alpha::ExtensionRequest::MergeFrom(from.file_containing_extension());
      break;
    }
    case kAllExtensionNumbersOfType: {
      set_all_extension_numbers_of_type(from.all_extension_numbers_of_type());
      break;
    }
    case kListServices: {
      set_list_services(from.list_services());
      break;
    }
    case MESSAGE_REQUEST_NOT_SET: {
      break;
    }
  }
}

void ServerReflectionRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.reflection.v1alpha.ServerReflectionRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ServerReflectionRequest::CopyFrom(const ServerReflectionRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.reflection.v1alpha.ServerReflectionRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ServerReflectionRequest::IsInitialized() const {
  return true;
}

void ServerReflectionRequest::Swap(ServerReflectionRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ServerReflectionRequest::InternalSwap(ServerReflectionRequest* other) {
  using std::swap;
  host_.Swap(&other->host_);
  swap(message_request_, other->message_request_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ServerReflectionRequest::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ExtensionRequest::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ExtensionRequest::kContainingTypeFieldNumber;
const int ExtensionRequest::kExtensionNumberFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ExtensionRequest::ExtensionRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsExtensionRequest();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.reflection.v1alpha.ExtensionRequest)
}
ExtensionRequest::ExtensionRequest(const ExtensionRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  containing_type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.containing_type().size() > 0) {
    containing_type_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.containing_type_);
  }
  extension_number_ = from.extension_number_;
  // @@protoc_insertion_point(copy_constructor:grpc.reflection.v1alpha.ExtensionRequest)
}

void ExtensionRequest::SharedCtor() {
  containing_type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  extension_number_ = 0;
  _cached_size_ = 0;
}

ExtensionRequest::~ExtensionRequest() {
  // @@protoc_insertion_point(destructor:grpc.reflection.v1alpha.ExtensionRequest)
  SharedDtor();
}

void ExtensionRequest::SharedDtor() {
  containing_type_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void ExtensionRequest::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ExtensionRequest::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ExtensionRequest& ExtensionRequest::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsExtensionRequest();
  return *internal_default_instance();
}

ExtensionRequest* ExtensionRequest::New(::google::protobuf::Arena* arena) const {
  ExtensionRequest* n = new ExtensionRequest;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ExtensionRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.reflection.v1alpha.ExtensionRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  containing_type_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  extension_number_ = 0;
  _internal_metadata_.Clear();
}

bool ExtensionRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.reflection.v1alpha.ExtensionRequest)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string containing_type = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_containing_type()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->containing_type().data(), static_cast<int>(this->containing_type().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.reflection.v1alpha.ExtensionRequest.containing_type"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 extension_number = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &extension_number_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.reflection.v1alpha.ExtensionRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.reflection.v1alpha.ExtensionRequest)
  return false;
#undef DO_
}

void ExtensionRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.reflection.v1alpha.ExtensionRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string containing_type = 1;
  if (this->containing_type().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->containing_type().data(), static_cast<int>(this->containing_type().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.reflection.v1alpha.ExtensionRequest.containing_type");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->containing_type(), output);
  }

  // int32 extension_number = 2;
  if (this->extension_number() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->extension_number(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.reflection.v1alpha.ExtensionRequest)
}

::google::protobuf::uint8* ExtensionRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.reflection.v1alpha.ExtensionRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string containing_type = 1;
  if (this->containing_type().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->containing_type().data(), static_cast<int>(this->containing_type().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.reflection.v1alpha.ExtensionRequest.containing_type");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->containing_type(), target);
  }

  // int32 extension_number = 2;
  if (this->extension_number() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->extension_number(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.reflection.v1alpha.ExtensionRequest)
  return target;
}

size_t ExtensionRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.reflection.v1alpha.ExtensionRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string containing_type = 1;
  if (this->containing_type().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->containing_type());
  }

  // int32 extension_number = 2;
  if (this->extension_number() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->extension_number());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ExtensionRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.reflection.v1alpha.ExtensionRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const ExtensionRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ExtensionRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.reflection.v1alpha.ExtensionRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.reflection.v1alpha.ExtensionRequest)
    MergeFrom(*source);
  }
}

void ExtensionRequest::MergeFrom(const ExtensionRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.reflection.v1alpha.ExtensionRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.containing_type().size() > 0) {

    containing_type_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.containing_type_);
  }
  if (from.extension_number() != 0) {
    set_extension_number(from.extension_number());
  }
}

void ExtensionRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.reflection.v1alpha.ExtensionRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ExtensionRequest::CopyFrom(const ExtensionRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.reflection.v1alpha.ExtensionRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ExtensionRequest::IsInitialized() const {
  return true;
}

void ExtensionRequest::Swap(ExtensionRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ExtensionRequest::InternalSwap(ExtensionRequest* other) {
  using std::swap;
  containing_type_.Swap(&other->containing_type_);
  swap(extension_number_, other->extension_number_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ExtensionRequest::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ServerReflectionResponse::InitAsDefaultInstance() {
  ::grpc::reflection::v1alpha::_ServerReflectionResponse_default_instance_._instance.get_mutable()->original_request_ = const_cast< ::grpc::reflection::v1alpha::ServerReflectionRequest*>(
      ::grpc::reflection::v1alpha::ServerReflectionRequest::internal_default_instance());
  ::grpc::reflection::v1alpha::_ServerReflectionResponse_default_instance_.file_descriptor_response_ = const_cast< ::grpc::reflection::v1alpha::FileDescriptorResponse*>(
      ::grpc::reflection::v1alpha::FileDescriptorResponse::internal_default_instance());
  ::grpc::reflection::v1alpha::_ServerReflectionResponse_default_instance_.all_extension_numbers_response_ = const_cast< ::grpc::reflection::v1alpha::ExtensionNumberResponse*>(
      ::grpc::reflection::v1alpha::ExtensionNumberResponse::internal_default_instance());
  ::grpc::reflection::v1alpha::_ServerReflectionResponse_default_instance_.list_services_response_ = const_cast< ::grpc::reflection::v1alpha::ListServiceResponse*>(
      ::grpc::reflection::v1alpha::ListServiceResponse::internal_default_instance());
  ::grpc::reflection::v1alpha::_ServerReflectionResponse_default_instance_.error_response_ = const_cast< ::grpc::reflection::v1alpha::ErrorResponse*>(
      ::grpc::reflection::v1alpha::ErrorResponse::internal_default_instance());
}
void ServerReflectionResponse::set_allocated_file_descriptor_response(::grpc::reflection::v1alpha::FileDescriptorResponse* file_descriptor_response) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_message_response();
  if (file_descriptor_response) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      file_descriptor_response = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, file_descriptor_response, submessage_arena);
    }
    set_has_file_descriptor_response();
    message_response_.file_descriptor_response_ = file_descriptor_response;
  }
  // @@protoc_insertion_point(field_set_allocated:grpc.reflection.v1alpha.ServerReflectionResponse.file_descriptor_response)
}
void ServerReflectionResponse::set_allocated_all_extension_numbers_response(::grpc::reflection::v1alpha::ExtensionNumberResponse* all_extension_numbers_response) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_message_response();
  if (all_extension_numbers_response) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      all_extension_numbers_response = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, all_extension_numbers_response, submessage_arena);
    }
    set_has_all_extension_numbers_response();
    message_response_.all_extension_numbers_response_ = all_extension_numbers_response;
  }
  // @@protoc_insertion_point(field_set_allocated:grpc.reflection.v1alpha.ServerReflectionResponse.all_extension_numbers_response)
}
void ServerReflectionResponse::set_allocated_list_services_response(::grpc::reflection::v1alpha::ListServiceResponse* list_services_response) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_message_response();
  if (list_services_response) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      list_services_response = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, list_services_response, submessage_arena);
    }
    set_has_list_services_response();
    message_response_.list_services_response_ = list_services_response;
  }
  // @@protoc_insertion_point(field_set_allocated:grpc.reflection.v1alpha.ServerReflectionResponse.list_services_response)
}
void ServerReflectionResponse::set_allocated_error_response(::grpc::reflection::v1alpha::ErrorResponse* error_response) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_message_response();
  if (error_response) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      error_response = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, error_response, submessage_arena);
    }
    set_has_error_response();
    message_response_.error_response_ = error_response;
  }
  // @@protoc_insertion_point(field_set_allocated:grpc.reflection.v1alpha.ServerReflectionResponse.error_response)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ServerReflectionResponse::kValidHostFieldNumber;
const int ServerReflectionResponse::kOriginalRequestFieldNumber;
const int ServerReflectionResponse::kFileDescriptorResponseFieldNumber;
const int ServerReflectionResponse::kAllExtensionNumbersResponseFieldNumber;
const int ServerReflectionResponse::kListServicesResponseFieldNumber;
const int ServerReflectionResponse::kErrorResponseFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ServerReflectionResponse::ServerReflectionResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsServerReflectionResponse();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.reflection.v1alpha.ServerReflectionResponse)
}
ServerReflectionResponse::ServerReflectionResponse(const ServerReflectionResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  valid_host_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.valid_host().size() > 0) {
    valid_host_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.valid_host_);
  }
  if (from.has_original_request()) {
    original_request_ = new ::grpc::reflection::v1alpha::ServerReflectionRequest(*from.original_request_);
  } else {
    original_request_ = NULL;
  }
  clear_has_message_response();
  switch (from.message_response_case()) {
    case kFileDescriptorResponse: {
      mutable_file_descriptor_response()->::grpc::reflection::v1alpha::FileDescriptorResponse::MergeFrom(from.file_descriptor_response());
      break;
    }
    case kAllExtensionNumbersResponse: {
      mutable_all_extension_numbers_response()->::grpc::reflection::v1alpha::ExtensionNumberResponse::MergeFrom(from.all_extension_numbers_response());
      break;
    }
    case kListServicesResponse: {
      mutable_list_services_response()->::grpc::reflection::v1alpha::ListServiceResponse::MergeFrom(from.list_services_response());
      break;
    }
    case kErrorResponse: {
      mutable_error_response()->::grpc::reflection::v1alpha::ErrorResponse::MergeFrom(from.error_response());
      break;
    }
    case MESSAGE_RESPONSE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:grpc.reflection.v1alpha.ServerReflectionResponse)
}

void ServerReflectionResponse::SharedCtor() {
  valid_host_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  original_request_ = NULL;
  clear_has_message_response();
  _cached_size_ = 0;
}

ServerReflectionResponse::~ServerReflectionResponse() {
  // @@protoc_insertion_point(destructor:grpc.reflection.v1alpha.ServerReflectionResponse)
  SharedDtor();
}

void ServerReflectionResponse::SharedDtor() {
  valid_host_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete original_request_;
  if (has_message_response()) {
    clear_message_response();
  }
}

void ServerReflectionResponse::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ServerReflectionResponse::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ServerReflectionResponse& ServerReflectionResponse::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsServerReflectionResponse();
  return *internal_default_instance();
}

ServerReflectionResponse* ServerReflectionResponse::New(::google::protobuf::Arena* arena) const {
  ServerReflectionResponse* n = new ServerReflectionResponse;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ServerReflectionResponse::clear_message_response() {
// @@protoc_insertion_point(one_of_clear_start:grpc.reflection.v1alpha.ServerReflectionResponse)
  switch (message_response_case()) {
    case kFileDescriptorResponse: {
      delete message_response_.file_descriptor_response_;
      break;
    }
    case kAllExtensionNumbersResponse: {
      delete message_response_.all_extension_numbers_response_;
      break;
    }
    case kListServicesResponse: {
      delete message_response_.list_services_response_;
      break;
    }
    case kErrorResponse: {
      delete message_response_.error_response_;
      break;
    }
    case MESSAGE_RESPONSE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = MESSAGE_RESPONSE_NOT_SET;
}


void ServerReflectionResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.reflection.v1alpha.ServerReflectionResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  valid_host_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && original_request_ != NULL) {
    delete original_request_;
  }
  original_request_ = NULL;
  clear_message_response();
  _internal_metadata_.Clear();
}

bool ServerReflectionResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.reflection.v1alpha.ServerReflectionResponse)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string valid_host = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_valid_host()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->valid_host().data(), static_cast<int>(this->valid_host().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.reflection.v1alpha.ServerReflectionResponse.valid_host"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.reflection.v1alpha.ServerReflectionRequest original_request = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_original_request()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.reflection.v1alpha.FileDescriptorResponse file_descriptor_response = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_file_descriptor_response()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.reflection.v1alpha.ExtensionNumberResponse all_extension_numbers_response = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_all_extension_numbers_response()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.reflection.v1alpha.ListServiceResponse list_services_response = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_list_services_response()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.reflection.v1alpha.ErrorResponse error_response = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_error_response()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.reflection.v1alpha.ServerReflectionResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.reflection.v1alpha.ServerReflectionResponse)
  return false;
#undef DO_
}

void ServerReflectionResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.reflection.v1alpha.ServerReflectionResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string valid_host = 1;
  if (this->valid_host().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->valid_host().data(), static_cast<int>(this->valid_host().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.reflection.v1alpha.ServerReflectionResponse.valid_host");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->valid_host(), output);
  }

  // .grpc.reflection.v1alpha.ServerReflectionRequest original_request = 2;
  if (this->has_original_request()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->original_request_, output);
  }

  // .grpc.reflection.v1alpha.FileDescriptorResponse file_descriptor_response = 4;
  if (has_file_descriptor_response()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, *message_response_.file_descriptor_response_, output);
  }

  // .grpc.reflection.v1alpha.ExtensionNumberResponse all_extension_numbers_response = 5;
  if (has_all_extension_numbers_response()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, *message_response_.all_extension_numbers_response_, output);
  }

  // .grpc.reflection.v1alpha.ListServiceResponse list_services_response = 6;
  if (has_list_services_response()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, *message_response_.list_services_response_, output);
  }

  // .grpc.reflection.v1alpha.ErrorResponse error_response = 7;
  if (has_error_response()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7, *message_response_.error_response_, output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.reflection.v1alpha.ServerReflectionResponse)
}

::google::protobuf::uint8* ServerReflectionResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.reflection.v1alpha.ServerReflectionResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string valid_host = 1;
  if (this->valid_host().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->valid_host().data(), static_cast<int>(this->valid_host().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.reflection.v1alpha.ServerReflectionResponse.valid_host");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->valid_host(), target);
  }

  // .grpc.reflection.v1alpha.ServerReflectionRequest original_request = 2;
  if (this->has_original_request()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, *this->original_request_, deterministic, target);
  }

  // .grpc.reflection.v1alpha.FileDescriptorResponse file_descriptor_response = 4;
  if (has_file_descriptor_response()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, *message_response_.file_descriptor_response_, deterministic, target);
  }

  // .grpc.reflection.v1alpha.ExtensionNumberResponse all_extension_numbers_response = 5;
  if (has_all_extension_numbers_response()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        5, *message_response_.all_extension_numbers_response_, deterministic, target);
  }

  // .grpc.reflection.v1alpha.ListServiceResponse list_services_response = 6;
  if (has_list_services_response()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        6, *message_response_.list_services_response_, deterministic, target);
  }

  // .grpc.reflection.v1alpha.ErrorResponse error_response = 7;
  if (has_error_response()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        7, *message_response_.error_response_, deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.reflection.v1alpha.ServerReflectionResponse)
  return target;
}

size_t ServerReflectionResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.reflection.v1alpha.ServerReflectionResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string valid_host = 1;
  if (this->valid_host().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->valid_host());
  }

  // .grpc.reflection.v1alpha.ServerReflectionRequest original_request = 2;
  if (this->has_original_request()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->original_request_);
  }

  switch (message_response_case()) {
    // .grpc.reflection.v1alpha.FileDescriptorResponse file_descriptor_response = 4;
    case kFileDescriptorResponse: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *message_response_.file_descriptor_response_);
      break;
    }
    // .grpc.reflection.v1alpha.ExtensionNumberResponse all_extension_numbers_response = 5;
    case kAllExtensionNumbersResponse: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *message_response_.all_extension_numbers_response_);
      break;
    }
    // .grpc.reflection.v1alpha.ListServiceResponse list_services_response = 6;
    case kListServicesResponse: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *message_response_.list_services_response_);
      break;
    }
    // .grpc.reflection.v1alpha.ErrorResponse error_response = 7;
    case kErrorResponse: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *message_response_.error_response_);
      break;
    }
    case MESSAGE_RESPONSE_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ServerReflectionResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.reflection.v1alpha.ServerReflectionResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const ServerReflectionResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ServerReflectionResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.reflection.v1alpha.ServerReflectionResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.reflection.v1alpha.ServerReflectionResponse)
    MergeFrom(*source);
  }
}

void ServerReflectionResponse::MergeFrom(const ServerReflectionResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.reflection.v1alpha.ServerReflectionResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.valid_host().size() > 0) {

    valid_host_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.valid_host_);
  }
  if (from.has_original_request()) {
    mutable_original_request()->::grpc::reflection::v1alpha::ServerReflectionRequest::MergeFrom(from.original_request());
  }
  switch (from.message_response_case()) {
    case kFileDescriptorResponse: {
      mutable_file_descriptor_response()->::grpc::reflection::v1alpha::FileDescriptorResponse::MergeFrom(from.file_descriptor_response());
      break;
    }
    case kAllExtensionNumbersResponse: {
      mutable_all_extension_numbers_response()->::grpc::reflection::v1alpha::ExtensionNumberResponse::MergeFrom(from.all_extension_numbers_response());
      break;
    }
    case kListServicesResponse: {
      mutable_list_services_response()->::grpc::reflection::v1alpha::ListServiceResponse::MergeFrom(from.list_services_response());
      break;
    }
    case kErrorResponse: {
      mutable_error_response()->::grpc::reflection::v1alpha::ErrorResponse::MergeFrom(from.error_response());
      break;
    }
    case MESSAGE_RESPONSE_NOT_SET: {
      break;
    }
  }
}

void ServerReflectionResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.reflection.v1alpha.ServerReflectionResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ServerReflectionResponse::CopyFrom(const ServerReflectionResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.reflection.v1alpha.ServerReflectionResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ServerReflectionResponse::IsInitialized() const {
  return true;
}

void ServerReflectionResponse::Swap(ServerReflectionResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ServerReflectionResponse::InternalSwap(ServerReflectionResponse* other) {
  using std::swap;
  valid_host_.Swap(&other->valid_host_);
  swap(original_request_, other->original_request_);
  swap(message_response_, other->message_response_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ServerReflectionResponse::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void FileDescriptorResponse::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int FileDescriptorResponse::kFileDescriptorProtoFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

FileDescriptorResponse::FileDescriptorResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsFileDescriptorResponse();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.reflection.v1alpha.FileDescriptorResponse)
}
FileDescriptorResponse::FileDescriptorResponse(const FileDescriptorResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      file_descriptor_proto_(from.file_descriptor_proto_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:grpc.reflection.v1alpha.FileDescriptorResponse)
}

void FileDescriptorResponse::SharedCtor() {
  _cached_size_ = 0;
}

FileDescriptorResponse::~FileDescriptorResponse() {
  // @@protoc_insertion_point(destructor:grpc.reflection.v1alpha.FileDescriptorResponse)
  SharedDtor();
}

void FileDescriptorResponse::SharedDtor() {
}

void FileDescriptorResponse::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* FileDescriptorResponse::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const FileDescriptorResponse& FileDescriptorResponse::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsFileDescriptorResponse();
  return *internal_default_instance();
}

FileDescriptorResponse* FileDescriptorResponse::New(::google::protobuf::Arena* arena) const {
  FileDescriptorResponse* n = new FileDescriptorResponse;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void FileDescriptorResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.reflection.v1alpha.FileDescriptorResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  file_descriptor_proto_.Clear();
  _internal_metadata_.Clear();
}

bool FileDescriptorResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.reflection.v1alpha.FileDescriptorResponse)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated bytes file_descriptor_proto = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->add_file_descriptor_proto()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.reflection.v1alpha.FileDescriptorResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.reflection.v1alpha.FileDescriptorResponse)
  return false;
#undef DO_
}

void FileDescriptorResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.reflection.v1alpha.FileDescriptorResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated bytes file_descriptor_proto = 1;
  for (int i = 0, n = this->file_descriptor_proto_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteBytes(
      1, this->file_descriptor_proto(i), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.reflection.v1alpha.FileDescriptorResponse)
}

::google::protobuf::uint8* FileDescriptorResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.reflection.v1alpha.FileDescriptorResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated bytes file_descriptor_proto = 1;
  for (int i = 0, n = this->file_descriptor_proto_size(); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      WriteBytesToArray(1, this->file_descriptor_proto(i), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.reflection.v1alpha.FileDescriptorResponse)
  return target;
}

size_t FileDescriptorResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.reflection.v1alpha.FileDescriptorResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated bytes file_descriptor_proto = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->file_descriptor_proto_size());
  for (int i = 0, n = this->file_descriptor_proto_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::BytesSize(
      this->file_descriptor_proto(i));
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void FileDescriptorResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.reflection.v1alpha.FileDescriptorResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const FileDescriptorResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const FileDescriptorResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.reflection.v1alpha.FileDescriptorResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.reflection.v1alpha.FileDescriptorResponse)
    MergeFrom(*source);
  }
}

void FileDescriptorResponse::MergeFrom(const FileDescriptorResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.reflection.v1alpha.FileDescriptorResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  file_descriptor_proto_.MergeFrom(from.file_descriptor_proto_);
}

void FileDescriptorResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.reflection.v1alpha.FileDescriptorResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void FileDescriptorResponse::CopyFrom(const FileDescriptorResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.reflection.v1alpha.FileDescriptorResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FileDescriptorResponse::IsInitialized() const {
  return true;
}

void FileDescriptorResponse::Swap(FileDescriptorResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void FileDescriptorResponse::InternalSwap(FileDescriptorResponse* other) {
  using std::swap;
  file_descriptor_proto_.InternalSwap(&other->file_descriptor_proto_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata FileDescriptorResponse::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ExtensionNumberResponse::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ExtensionNumberResponse::kBaseTypeNameFieldNumber;
const int ExtensionNumberResponse::kExtensionNumberFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ExtensionNumberResponse::ExtensionNumberResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsExtensionNumberResponse();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.reflection.v1alpha.ExtensionNumberResponse)
}
ExtensionNumberResponse::ExtensionNumberResponse(const ExtensionNumberResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      extension_number_(from.extension_number_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  base_type_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.base_type_name().size() > 0) {
    base_type_name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.base_type_name_);
  }
  // @@protoc_insertion_point(copy_constructor:grpc.reflection.v1alpha.ExtensionNumberResponse)
}

void ExtensionNumberResponse::SharedCtor() {
  base_type_name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

ExtensionNumberResponse::~ExtensionNumberResponse() {
  // @@protoc_insertion_point(destructor:grpc.reflection.v1alpha.ExtensionNumberResponse)
  SharedDtor();
}

void ExtensionNumberResponse::SharedDtor() {
  base_type_name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void ExtensionNumberResponse::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ExtensionNumberResponse::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ExtensionNumberResponse& ExtensionNumberResponse::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsExtensionNumberResponse();
  return *internal_default_instance();
}

ExtensionNumberResponse* ExtensionNumberResponse::New(::google::protobuf::Arena* arena) const {
  ExtensionNumberResponse* n = new ExtensionNumberResponse;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ExtensionNumberResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.reflection.v1alpha.ExtensionNumberResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  extension_number_.Clear();
  base_type_name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _internal_metadata_.Clear();
}

bool ExtensionNumberResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.reflection.v1alpha.ExtensionNumberResponse)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string base_type_name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_base_type_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->base_type_name().data(), static_cast<int>(this->base_type_name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.reflection.v1alpha.ExtensionNumberResponse.base_type_name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated int32 extension_number = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_extension_number())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 1, 18u, input, this->mutable_extension_number())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.reflection.v1alpha.ExtensionNumberResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.reflection.v1alpha.ExtensionNumberResponse)
  return false;
#undef DO_
}

void ExtensionNumberResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.reflection.v1alpha.ExtensionNumberResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string base_type_name = 1;
  if (this->base_type_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->base_type_name().data(), static_cast<int>(this->base_type_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.reflection.v1alpha.ExtensionNumberResponse.base_type_name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->base_type_name(), output);
  }

  // repeated int32 extension_number = 2;
  if (this->extension_number_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(2, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _extension_number_cached_byte_size_));
  }
  for (int i = 0, n = this->extension_number_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32NoTag(
      this->extension_number(i), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.reflection.v1alpha.ExtensionNumberResponse)
}

::google::protobuf::uint8* ExtensionNumberResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.reflection.v1alpha.ExtensionNumberResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string base_type_name = 1;
  if (this->base_type_name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->base_type_name().data(), static_cast<int>(this->base_type_name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.reflection.v1alpha.ExtensionNumberResponse.base_type_name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->base_type_name(), target);
  }

  // repeated int32 extension_number = 2;
  if (this->extension_number_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      2,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _extension_number_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt32NoTagToArray(this->extension_number_, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.reflection.v1alpha.ExtensionNumberResponse)
  return target;
}

size_t ExtensionNumberResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.reflection.v1alpha.ExtensionNumberResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated int32 extension_number = 2;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int32Size(this->extension_number_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _extension_number_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // string base_type_name = 1;
  if (this->base_type_name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->base_type_name());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ExtensionNumberResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.reflection.v1alpha.ExtensionNumberResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const ExtensionNumberResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ExtensionNumberResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.reflection.v1alpha.ExtensionNumberResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.reflection.v1alpha.ExtensionNumberResponse)
    MergeFrom(*source);
  }
}

void ExtensionNumberResponse::MergeFrom(const ExtensionNumberResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.reflection.v1alpha.ExtensionNumberResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  extension_number_.MergeFrom(from.extension_number_);
  if (from.base_type_name().size() > 0) {

    base_type_name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.base_type_name_);
  }
}

void ExtensionNumberResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.reflection.v1alpha.ExtensionNumberResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ExtensionNumberResponse::CopyFrom(const ExtensionNumberResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.reflection.v1alpha.ExtensionNumberResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ExtensionNumberResponse::IsInitialized() const {
  return true;
}

void ExtensionNumberResponse::Swap(ExtensionNumberResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ExtensionNumberResponse::InternalSwap(ExtensionNumberResponse* other) {
  using std::swap;
  extension_number_.InternalSwap(&other->extension_number_);
  base_type_name_.Swap(&other->base_type_name_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ExtensionNumberResponse::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ListServiceResponse::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ListServiceResponse::kServiceFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ListServiceResponse::ListServiceResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsListServiceResponse();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.reflection.v1alpha.ListServiceResponse)
}
ListServiceResponse::ListServiceResponse(const ListServiceResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      service_(from.service_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:grpc.reflection.v1alpha.ListServiceResponse)
}

void ListServiceResponse::SharedCtor() {
  _cached_size_ = 0;
}

ListServiceResponse::~ListServiceResponse() {
  // @@protoc_insertion_point(destructor:grpc.reflection.v1alpha.ListServiceResponse)
  SharedDtor();
}

void ListServiceResponse::SharedDtor() {
}

void ListServiceResponse::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ListServiceResponse::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ListServiceResponse& ListServiceResponse::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsListServiceResponse();
  return *internal_default_instance();
}

ListServiceResponse* ListServiceResponse::New(::google::protobuf::Arena* arena) const {
  ListServiceResponse* n = new ListServiceResponse;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ListServiceResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.reflection.v1alpha.ListServiceResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  service_.Clear();
  _internal_metadata_.Clear();
}

bool ListServiceResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.reflection.v1alpha.ListServiceResponse)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .grpc.reflection.v1alpha.ServiceResponse service = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(input, add_service()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.reflection.v1alpha.ListServiceResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.reflection.v1alpha.ListServiceResponse)
  return false;
#undef DO_
}

void ListServiceResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.reflection.v1alpha.ListServiceResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .grpc.reflection.v1alpha.ServiceResponse service = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->service_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->service(static_cast<int>(i)), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.reflection.v1alpha.ListServiceResponse)
}

::google::protobuf::uint8* ListServiceResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.reflection.v1alpha.ListServiceResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .grpc.reflection.v1alpha.ServiceResponse service = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->service_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->service(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.reflection.v1alpha.ListServiceResponse)
  return target;
}

size_t ListServiceResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.reflection.v1alpha.ListServiceResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .grpc.reflection.v1alpha.ServiceResponse service = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->service_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->service(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ListServiceResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.reflection.v1alpha.ListServiceResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const ListServiceResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ListServiceResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.reflection.v1alpha.ListServiceResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.reflection.v1alpha.ListServiceResponse)
    MergeFrom(*source);
  }
}

void ListServiceResponse::MergeFrom(const ListServiceResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.reflection.v1alpha.ListServiceResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  service_.MergeFrom(from.service_);
}

void ListServiceResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.reflection.v1alpha.ListServiceResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ListServiceResponse::CopyFrom(const ListServiceResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.reflection.v1alpha.ListServiceResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ListServiceResponse::IsInitialized() const {
  return true;
}

void ListServiceResponse::Swap(ListServiceResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ListServiceResponse::InternalSwap(ListServiceResponse* other) {
  using std::swap;
  service_.InternalSwap(&other->service_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ListServiceResponse::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ServiceResponse::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ServiceResponse::kNameFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ServiceResponse::ServiceResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsServiceResponse();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.reflection.v1alpha.ServiceResponse)
}
ServiceResponse::ServiceResponse(const ServiceResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  // @@protoc_insertion_point(copy_constructor:grpc.reflection.v1alpha.ServiceResponse)
}

void ServiceResponse::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

ServiceResponse::~ServiceResponse() {
  // @@protoc_insertion_point(destructor:grpc.reflection.v1alpha.ServiceResponse)
  SharedDtor();
}

void ServiceResponse::SharedDtor() {
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void ServiceResponse::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ServiceResponse::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ServiceResponse& ServiceResponse::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsServiceResponse();
  return *internal_default_instance();
}

ServiceResponse* ServiceResponse::New(::google::protobuf::Arena* arena) const {
  ServiceResponse* n = new ServiceResponse;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ServiceResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.reflection.v1alpha.ServiceResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _internal_metadata_.Clear();
}

bool ServiceResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.reflection.v1alpha.ServiceResponse)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.reflection.v1alpha.ServiceResponse.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.reflection.v1alpha.ServiceResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.reflection.v1alpha.ServiceResponse)
  return false;
#undef DO_
}

void ServiceResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.reflection.v1alpha.ServiceResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.reflection.v1alpha.ServiceResponse.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.reflection.v1alpha.ServiceResponse)
}

::google::protobuf::uint8* ServiceResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.reflection.v1alpha.ServiceResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.reflection.v1alpha.ServiceResponse.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.reflection.v1alpha.ServiceResponse)
  return target;
}

size_t ServiceResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.reflection.v1alpha.ServiceResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ServiceResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.reflection.v1alpha.ServiceResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const ServiceResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ServiceResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.reflection.v1alpha.ServiceResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.reflection.v1alpha.ServiceResponse)
    MergeFrom(*source);
  }
}

void ServiceResponse::MergeFrom(const ServiceResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.reflection.v1alpha.ServiceResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.name().size() > 0) {

    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
}

void ServiceResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.reflection.v1alpha.ServiceResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ServiceResponse::CopyFrom(const ServiceResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.reflection.v1alpha.ServiceResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ServiceResponse::IsInitialized() const {
  return true;
}

void ServiceResponse::Swap(ServiceResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ServiceResponse::InternalSwap(ServiceResponse* other) {
  using std::swap;
  name_.Swap(&other->name_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ServiceResponse::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ErrorResponse::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ErrorResponse::kErrorCodeFieldNumber;
const int ErrorResponse::kErrorMessageFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ErrorResponse::ErrorResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsErrorResponse();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.reflection.v1alpha.ErrorResponse)
}
ErrorResponse::ErrorResponse(const ErrorResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  error_message_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.error_message().size() > 0) {
    error_message_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.error_message_);
  }
  error_code_ = from.error_code_;
  // @@protoc_insertion_point(copy_constructor:grpc.reflection.v1alpha.ErrorResponse)
}

void ErrorResponse::SharedCtor() {
  error_message_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  error_code_ = 0;
  _cached_size_ = 0;
}

ErrorResponse::~ErrorResponse() {
  // @@protoc_insertion_point(destructor:grpc.reflection.v1alpha.ErrorResponse)
  SharedDtor();
}

void ErrorResponse::SharedDtor() {
  error_message_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void ErrorResponse::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ErrorResponse::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ErrorResponse& ErrorResponse::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsErrorResponse();
  return *internal_default_instance();
}

ErrorResponse* ErrorResponse::New(::google::protobuf::Arena* arena) const {
  ErrorResponse* n = new ErrorResponse;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ErrorResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.reflection.v1alpha.ErrorResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  error_message_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  error_code_ = 0;
  _internal_metadata_.Clear();
}

bool ErrorResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.reflection.v1alpha.ErrorResponse)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 error_code = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &error_code_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string error_message = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_error_message()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->error_message().data(), static_cast<int>(this->error_message().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.reflection.v1alpha.ErrorResponse.error_message"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.reflection.v1alpha.ErrorResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.reflection.v1alpha.ErrorResponse)
  return false;
#undef DO_
}

void ErrorResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.reflection.v1alpha.ErrorResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 error_code = 1;
  if (this->error_code() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->error_code(), output);
  }

  // string error_message = 2;
  if (this->error_message().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->error_message().data(), static_cast<int>(this->error_message().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.reflection.v1alpha.ErrorResponse.error_message");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->error_message(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.reflection.v1alpha.ErrorResponse)
}

::google::protobuf::uint8* ErrorResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.reflection.v1alpha.ErrorResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 error_code = 1;
  if (this->error_code() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->error_code(), target);
  }

  // string error_message = 2;
  if (this->error_message().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->error_message().data(), static_cast<int>(this->error_message().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.reflection.v1alpha.ErrorResponse.error_message");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->error_message(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.reflection.v1alpha.ErrorResponse)
  return target;
}

size_t ErrorResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.reflection.v1alpha.ErrorResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string error_message = 2;
  if (this->error_message().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->error_message());
  }

  // int32 error_code = 1;
  if (this->error_code() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->error_code());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ErrorResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.reflection.v1alpha.ErrorResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const ErrorResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ErrorResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.reflection.v1alpha.ErrorResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.reflection.v1alpha.ErrorResponse)
    MergeFrom(*source);
  }
}

void ErrorResponse::MergeFrom(const ErrorResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.reflection.v1alpha.ErrorResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.error_message().size() > 0) {

    error_message_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.error_message_);
  }
  if (from.error_code() != 0) {
    set_error_code(from.error_code());
  }
}

void ErrorResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.reflection.v1alpha.ErrorResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ErrorResponse::CopyFrom(const ErrorResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.reflection.v1alpha.ErrorResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ErrorResponse::IsInitialized() const {
  return true;
}

void ErrorResponse::Swap(ErrorResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ErrorResponse::InternalSwap(ErrorResponse* other) {
  using std::swap;
  error_message_.Swap(&other->error_message_);
  swap(error_code_, other->error_code_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ErrorResponse::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace v1alpha
}  // namespace reflection
}  // namespace grpc

// @@protoc_insertion_point(global_scope)
