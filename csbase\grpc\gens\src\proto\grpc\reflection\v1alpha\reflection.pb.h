// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: src/proto/grpc/reflection/v1alpha/reflection.proto

#ifndef PROTOBUF_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto__INCLUDED
#define PROTOBUF_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3005001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[8];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
void InitDefaultsServerReflectionRequestImpl();
void InitDefaultsServerReflectionRequest();
void InitDefaultsExtensionRequestImpl();
void InitDefaultsExtensionRequest();
void InitDefaultsServerReflectionResponseImpl();
void InitDefaultsServerReflectionResponse();
void InitDefaultsFileDescriptorResponseImpl();
void InitDefaultsFileDescriptorResponse();
void InitDefaultsExtensionNumberResponseImpl();
void InitDefaultsExtensionNumberResponse();
void InitDefaultsListServiceResponseImpl();
void InitDefaultsListServiceResponse();
void InitDefaultsServiceResponseImpl();
void InitDefaultsServiceResponse();
void InitDefaultsErrorResponseImpl();
void InitDefaultsErrorResponse();
inline void InitDefaults() {
  InitDefaultsServerReflectionRequest();
  InitDefaultsExtensionRequest();
  InitDefaultsServerReflectionResponse();
  InitDefaultsFileDescriptorResponse();
  InitDefaultsExtensionNumberResponse();
  InitDefaultsListServiceResponse();
  InitDefaultsServiceResponse();
  InitDefaultsErrorResponse();
}
}  // namespace protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto
namespace grpc {
namespace reflection {
namespace v1alpha {
class ErrorResponse;
class ErrorResponseDefaultTypeInternal;
extern ErrorResponseDefaultTypeInternal _ErrorResponse_default_instance_;
class ExtensionNumberResponse;
class ExtensionNumberResponseDefaultTypeInternal;
extern ExtensionNumberResponseDefaultTypeInternal _ExtensionNumberResponse_default_instance_;
class ExtensionRequest;
class ExtensionRequestDefaultTypeInternal;
extern ExtensionRequestDefaultTypeInternal _ExtensionRequest_default_instance_;
class FileDescriptorResponse;
class FileDescriptorResponseDefaultTypeInternal;
extern FileDescriptorResponseDefaultTypeInternal _FileDescriptorResponse_default_instance_;
class ListServiceResponse;
class ListServiceResponseDefaultTypeInternal;
extern ListServiceResponseDefaultTypeInternal _ListServiceResponse_default_instance_;
class ServerReflectionRequest;
class ServerReflectionRequestDefaultTypeInternal;
extern ServerReflectionRequestDefaultTypeInternal _ServerReflectionRequest_default_instance_;
class ServerReflectionResponse;
class ServerReflectionResponseDefaultTypeInternal;
extern ServerReflectionResponseDefaultTypeInternal _ServerReflectionResponse_default_instance_;
class ServiceResponse;
class ServiceResponseDefaultTypeInternal;
extern ServiceResponseDefaultTypeInternal _ServiceResponse_default_instance_;
}  // namespace v1alpha
}  // namespace reflection
}  // namespace grpc
namespace grpc {
namespace reflection {
namespace v1alpha {

// ===================================================================

class ServerReflectionRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.reflection.v1alpha.ServerReflectionRequest) */ {
 public:
  ServerReflectionRequest();
  virtual ~ServerReflectionRequest();

  ServerReflectionRequest(const ServerReflectionRequest& from);

  inline ServerReflectionRequest& operator=(const ServerReflectionRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ServerReflectionRequest(ServerReflectionRequest&& from) noexcept
    : ServerReflectionRequest() {
    *this = ::std::move(from);
  }

  inline ServerReflectionRequest& operator=(ServerReflectionRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ServerReflectionRequest& default_instance();

  enum MessageRequestCase {
    kFileByFilename = 3,
    kFileContainingSymbol = 4,
    kFileContainingExtension = 5,
    kAllExtensionNumbersOfType = 6,
    kListServices = 7,
    MESSAGE_REQUEST_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ServerReflectionRequest* internal_default_instance() {
    return reinterpret_cast<const ServerReflectionRequest*>(
               &_ServerReflectionRequest_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    0;

  void Swap(ServerReflectionRequest* other);
  friend void swap(ServerReflectionRequest& a, ServerReflectionRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ServerReflectionRequest* New() const PROTOBUF_FINAL { return New(NULL); }

  ServerReflectionRequest* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ServerReflectionRequest& from);
  void MergeFrom(const ServerReflectionRequest& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ServerReflectionRequest* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string host = 1;
  void clear_host();
  static const int kHostFieldNumber = 1;
  const ::std::string& host() const;
  void set_host(const ::std::string& value);
  #if LANG_CXX11
  void set_host(::std::string&& value);
  #endif
  void set_host(const char* value);
  void set_host(const char* value, size_t size);
  ::std::string* mutable_host();
  ::std::string* release_host();
  void set_allocated_host(::std::string* host);

  // string file_by_filename = 3;
  private:
  bool has_file_by_filename() const;
  public:
  void clear_file_by_filename();
  static const int kFileByFilenameFieldNumber = 3;
  const ::std::string& file_by_filename() const;
  void set_file_by_filename(const ::std::string& value);
  #if LANG_CXX11
  void set_file_by_filename(::std::string&& value);
  #endif
  void set_file_by_filename(const char* value);
  void set_file_by_filename(const char* value, size_t size);
  ::std::string* mutable_file_by_filename();
  ::std::string* release_file_by_filename();
  void set_allocated_file_by_filename(::std::string* file_by_filename);

  // string file_containing_symbol = 4;
  private:
  bool has_file_containing_symbol() const;
  public:
  void clear_file_containing_symbol();
  static const int kFileContainingSymbolFieldNumber = 4;
  const ::std::string& file_containing_symbol() const;
  void set_file_containing_symbol(const ::std::string& value);
  #if LANG_CXX11
  void set_file_containing_symbol(::std::string&& value);
  #endif
  void set_file_containing_symbol(const char* value);
  void set_file_containing_symbol(const char* value, size_t size);
  ::std::string* mutable_file_containing_symbol();
  ::std::string* release_file_containing_symbol();
  void set_allocated_file_containing_symbol(::std::string* file_containing_symbol);

  // .grpc.reflection.v1alpha.ExtensionRequest file_containing_extension = 5;
  bool has_file_containing_extension() const;
  void clear_file_containing_extension();
  static const int kFileContainingExtensionFieldNumber = 5;
  const ::grpc::reflection::v1alpha::ExtensionRequest& file_containing_extension() const;
  ::grpc::reflection::v1alpha::ExtensionRequest* release_file_containing_extension();
  ::grpc::reflection::v1alpha::ExtensionRequest* mutable_file_containing_extension();
  void set_allocated_file_containing_extension(::grpc::reflection::v1alpha::ExtensionRequest* file_containing_extension);

  // string all_extension_numbers_of_type = 6;
  private:
  bool has_all_extension_numbers_of_type() const;
  public:
  void clear_all_extension_numbers_of_type();
  static const int kAllExtensionNumbersOfTypeFieldNumber = 6;
  const ::std::string& all_extension_numbers_of_type() const;
  void set_all_extension_numbers_of_type(const ::std::string& value);
  #if LANG_CXX11
  void set_all_extension_numbers_of_type(::std::string&& value);
  #endif
  void set_all_extension_numbers_of_type(const char* value);
  void set_all_extension_numbers_of_type(const char* value, size_t size);
  ::std::string* mutable_all_extension_numbers_of_type();
  ::std::string* release_all_extension_numbers_of_type();
  void set_allocated_all_extension_numbers_of_type(::std::string* all_extension_numbers_of_type);

  // string list_services = 7;
  private:
  bool has_list_services() const;
  public:
  void clear_list_services();
  static const int kListServicesFieldNumber = 7;
  const ::std::string& list_services() const;
  void set_list_services(const ::std::string& value);
  #if LANG_CXX11
  void set_list_services(::std::string&& value);
  #endif
  void set_list_services(const char* value);
  void set_list_services(const char* value, size_t size);
  ::std::string* mutable_list_services();
  ::std::string* release_list_services();
  void set_allocated_list_services(::std::string* list_services);

  MessageRequestCase message_request_case() const;
  // @@protoc_insertion_point(class_scope:grpc.reflection.v1alpha.ServerReflectionRequest)
 private:
  void set_has_file_by_filename();
  void set_has_file_containing_symbol();
  void set_has_file_containing_extension();
  void set_has_all_extension_numbers_of_type();
  void set_has_list_services();

  inline bool has_message_request() const;
  void clear_message_request();
  inline void clear_has_message_request();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr host_;
  union MessageRequestUnion {
    MessageRequestUnion() {}
    ::google::protobuf::internal::ArenaStringPtr file_by_filename_;
    ::google::protobuf::internal::ArenaStringPtr file_containing_symbol_;
    ::grpc::reflection::v1alpha::ExtensionRequest* file_containing_extension_;
    ::google::protobuf::internal::ArenaStringPtr all_extension_numbers_of_type_;
    ::google::protobuf::internal::ArenaStringPtr list_services_;
  } message_request_;
  mutable int _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsServerReflectionRequestImpl();
};
// -------------------------------------------------------------------

class ExtensionRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.reflection.v1alpha.ExtensionRequest) */ {
 public:
  ExtensionRequest();
  virtual ~ExtensionRequest();

  ExtensionRequest(const ExtensionRequest& from);

  inline ExtensionRequest& operator=(const ExtensionRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ExtensionRequest(ExtensionRequest&& from) noexcept
    : ExtensionRequest() {
    *this = ::std::move(from);
  }

  inline ExtensionRequest& operator=(ExtensionRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ExtensionRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ExtensionRequest* internal_default_instance() {
    return reinterpret_cast<const ExtensionRequest*>(
               &_ExtensionRequest_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    1;

  void Swap(ExtensionRequest* other);
  friend void swap(ExtensionRequest& a, ExtensionRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ExtensionRequest* New() const PROTOBUF_FINAL { return New(NULL); }

  ExtensionRequest* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ExtensionRequest& from);
  void MergeFrom(const ExtensionRequest& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ExtensionRequest* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string containing_type = 1;
  void clear_containing_type();
  static const int kContainingTypeFieldNumber = 1;
  const ::std::string& containing_type() const;
  void set_containing_type(const ::std::string& value);
  #if LANG_CXX11
  void set_containing_type(::std::string&& value);
  #endif
  void set_containing_type(const char* value);
  void set_containing_type(const char* value, size_t size);
  ::std::string* mutable_containing_type();
  ::std::string* release_containing_type();
  void set_allocated_containing_type(::std::string* containing_type);

  // int32 extension_number = 2;
  void clear_extension_number();
  static const int kExtensionNumberFieldNumber = 2;
  ::google::protobuf::int32 extension_number() const;
  void set_extension_number(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:grpc.reflection.v1alpha.ExtensionRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr containing_type_;
  ::google::protobuf::int32 extension_number_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsExtensionRequestImpl();
};
// -------------------------------------------------------------------

class ServerReflectionResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.reflection.v1alpha.ServerReflectionResponse) */ {
 public:
  ServerReflectionResponse();
  virtual ~ServerReflectionResponse();

  ServerReflectionResponse(const ServerReflectionResponse& from);

  inline ServerReflectionResponse& operator=(const ServerReflectionResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ServerReflectionResponse(ServerReflectionResponse&& from) noexcept
    : ServerReflectionResponse() {
    *this = ::std::move(from);
  }

  inline ServerReflectionResponse& operator=(ServerReflectionResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ServerReflectionResponse& default_instance();

  enum MessageResponseCase {
    kFileDescriptorResponse = 4,
    kAllExtensionNumbersResponse = 5,
    kListServicesResponse = 6,
    kErrorResponse = 7,
    MESSAGE_RESPONSE_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ServerReflectionResponse* internal_default_instance() {
    return reinterpret_cast<const ServerReflectionResponse*>(
               &_ServerReflectionResponse_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    2;

  void Swap(ServerReflectionResponse* other);
  friend void swap(ServerReflectionResponse& a, ServerReflectionResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ServerReflectionResponse* New() const PROTOBUF_FINAL { return New(NULL); }

  ServerReflectionResponse* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ServerReflectionResponse& from);
  void MergeFrom(const ServerReflectionResponse& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ServerReflectionResponse* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string valid_host = 1;
  void clear_valid_host();
  static const int kValidHostFieldNumber = 1;
  const ::std::string& valid_host() const;
  void set_valid_host(const ::std::string& value);
  #if LANG_CXX11
  void set_valid_host(::std::string&& value);
  #endif
  void set_valid_host(const char* value);
  void set_valid_host(const char* value, size_t size);
  ::std::string* mutable_valid_host();
  ::std::string* release_valid_host();
  void set_allocated_valid_host(::std::string* valid_host);

  // .grpc.reflection.v1alpha.ServerReflectionRequest original_request = 2;
  bool has_original_request() const;
  void clear_original_request();
  static const int kOriginalRequestFieldNumber = 2;
  const ::grpc::reflection::v1alpha::ServerReflectionRequest& original_request() const;
  ::grpc::reflection::v1alpha::ServerReflectionRequest* release_original_request();
  ::grpc::reflection::v1alpha::ServerReflectionRequest* mutable_original_request();
  void set_allocated_original_request(::grpc::reflection::v1alpha::ServerReflectionRequest* original_request);

  // .grpc.reflection.v1alpha.FileDescriptorResponse file_descriptor_response = 4;
  bool has_file_descriptor_response() const;
  void clear_file_descriptor_response();
  static const int kFileDescriptorResponseFieldNumber = 4;
  const ::grpc::reflection::v1alpha::FileDescriptorResponse& file_descriptor_response() const;
  ::grpc::reflection::v1alpha::FileDescriptorResponse* release_file_descriptor_response();
  ::grpc::reflection::v1alpha::FileDescriptorResponse* mutable_file_descriptor_response();
  void set_allocated_file_descriptor_response(::grpc::reflection::v1alpha::FileDescriptorResponse* file_descriptor_response);

  // .grpc.reflection.v1alpha.ExtensionNumberResponse all_extension_numbers_response = 5;
  bool has_all_extension_numbers_response() const;
  void clear_all_extension_numbers_response();
  static const int kAllExtensionNumbersResponseFieldNumber = 5;
  const ::grpc::reflection::v1alpha::ExtensionNumberResponse& all_extension_numbers_response() const;
  ::grpc::reflection::v1alpha::ExtensionNumberResponse* release_all_extension_numbers_response();
  ::grpc::reflection::v1alpha::ExtensionNumberResponse* mutable_all_extension_numbers_response();
  void set_allocated_all_extension_numbers_response(::grpc::reflection::v1alpha::ExtensionNumberResponse* all_extension_numbers_response);

  // .grpc.reflection.v1alpha.ListServiceResponse list_services_response = 6;
  bool has_list_services_response() const;
  void clear_list_services_response();
  static const int kListServicesResponseFieldNumber = 6;
  const ::grpc::reflection::v1alpha::ListServiceResponse& list_services_response() const;
  ::grpc::reflection::v1alpha::ListServiceResponse* release_list_services_response();
  ::grpc::reflection::v1alpha::ListServiceResponse* mutable_list_services_response();
  void set_allocated_list_services_response(::grpc::reflection::v1alpha::ListServiceResponse* list_services_response);

  // .grpc.reflection.v1alpha.ErrorResponse error_response = 7;
  bool has_error_response() const;
  void clear_error_response();
  static const int kErrorResponseFieldNumber = 7;
  const ::grpc::reflection::v1alpha::ErrorResponse& error_response() const;
  ::grpc::reflection::v1alpha::ErrorResponse* release_error_response();
  ::grpc::reflection::v1alpha::ErrorResponse* mutable_error_response();
  void set_allocated_error_response(::grpc::reflection::v1alpha::ErrorResponse* error_response);

  MessageResponseCase message_response_case() const;
  // @@protoc_insertion_point(class_scope:grpc.reflection.v1alpha.ServerReflectionResponse)
 private:
  void set_has_file_descriptor_response();
  void set_has_all_extension_numbers_response();
  void set_has_list_services_response();
  void set_has_error_response();

  inline bool has_message_response() const;
  void clear_message_response();
  inline void clear_has_message_response();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr valid_host_;
  ::grpc::reflection::v1alpha::ServerReflectionRequest* original_request_;
  union MessageResponseUnion {
    MessageResponseUnion() {}
    ::grpc::reflection::v1alpha::FileDescriptorResponse* file_descriptor_response_;
    ::grpc::reflection::v1alpha::ExtensionNumberResponse* all_extension_numbers_response_;
    ::grpc::reflection::v1alpha::ListServiceResponse* list_services_response_;
    ::grpc::reflection::v1alpha::ErrorResponse* error_response_;
  } message_response_;
  mutable int _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsServerReflectionResponseImpl();
};
// -------------------------------------------------------------------

class FileDescriptorResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.reflection.v1alpha.FileDescriptorResponse) */ {
 public:
  FileDescriptorResponse();
  virtual ~FileDescriptorResponse();

  FileDescriptorResponse(const FileDescriptorResponse& from);

  inline FileDescriptorResponse& operator=(const FileDescriptorResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  FileDescriptorResponse(FileDescriptorResponse&& from) noexcept
    : FileDescriptorResponse() {
    *this = ::std::move(from);
  }

  inline FileDescriptorResponse& operator=(FileDescriptorResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const FileDescriptorResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const FileDescriptorResponse* internal_default_instance() {
    return reinterpret_cast<const FileDescriptorResponse*>(
               &_FileDescriptorResponse_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    3;

  void Swap(FileDescriptorResponse* other);
  friend void swap(FileDescriptorResponse& a, FileDescriptorResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline FileDescriptorResponse* New() const PROTOBUF_FINAL { return New(NULL); }

  FileDescriptorResponse* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const FileDescriptorResponse& from);
  void MergeFrom(const FileDescriptorResponse& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(FileDescriptorResponse* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated bytes file_descriptor_proto = 1;
  int file_descriptor_proto_size() const;
  void clear_file_descriptor_proto();
  static const int kFileDescriptorProtoFieldNumber = 1;
  const ::std::string& file_descriptor_proto(int index) const;
  ::std::string* mutable_file_descriptor_proto(int index);
  void set_file_descriptor_proto(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_file_descriptor_proto(int index, ::std::string&& value);
  #endif
  void set_file_descriptor_proto(int index, const char* value);
  void set_file_descriptor_proto(int index, const void* value, size_t size);
  ::std::string* add_file_descriptor_proto();
  void add_file_descriptor_proto(const ::std::string& value);
  #if LANG_CXX11
  void add_file_descriptor_proto(::std::string&& value);
  #endif
  void add_file_descriptor_proto(const char* value);
  void add_file_descriptor_proto(const void* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& file_descriptor_proto() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_file_descriptor_proto();

  // @@protoc_insertion_point(class_scope:grpc.reflection.v1alpha.FileDescriptorResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::std::string> file_descriptor_proto_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsFileDescriptorResponseImpl();
};
// -------------------------------------------------------------------

class ExtensionNumberResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.reflection.v1alpha.ExtensionNumberResponse) */ {
 public:
  ExtensionNumberResponse();
  virtual ~ExtensionNumberResponse();

  ExtensionNumberResponse(const ExtensionNumberResponse& from);

  inline ExtensionNumberResponse& operator=(const ExtensionNumberResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ExtensionNumberResponse(ExtensionNumberResponse&& from) noexcept
    : ExtensionNumberResponse() {
    *this = ::std::move(from);
  }

  inline ExtensionNumberResponse& operator=(ExtensionNumberResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ExtensionNumberResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ExtensionNumberResponse* internal_default_instance() {
    return reinterpret_cast<const ExtensionNumberResponse*>(
               &_ExtensionNumberResponse_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    4;

  void Swap(ExtensionNumberResponse* other);
  friend void swap(ExtensionNumberResponse& a, ExtensionNumberResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ExtensionNumberResponse* New() const PROTOBUF_FINAL { return New(NULL); }

  ExtensionNumberResponse* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ExtensionNumberResponse& from);
  void MergeFrom(const ExtensionNumberResponse& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ExtensionNumberResponse* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int32 extension_number = 2;
  int extension_number_size() const;
  void clear_extension_number();
  static const int kExtensionNumberFieldNumber = 2;
  ::google::protobuf::int32 extension_number(int index) const;
  void set_extension_number(int index, ::google::protobuf::int32 value);
  void add_extension_number(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      extension_number() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_extension_number();

  // string base_type_name = 1;
  void clear_base_type_name();
  static const int kBaseTypeNameFieldNumber = 1;
  const ::std::string& base_type_name() const;
  void set_base_type_name(const ::std::string& value);
  #if LANG_CXX11
  void set_base_type_name(::std::string&& value);
  #endif
  void set_base_type_name(const char* value);
  void set_base_type_name(const char* value, size_t size);
  ::std::string* mutable_base_type_name();
  ::std::string* release_base_type_name();
  void set_allocated_base_type_name(::std::string* base_type_name);

  // @@protoc_insertion_point(class_scope:grpc.reflection.v1alpha.ExtensionNumberResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > extension_number_;
  mutable int _extension_number_cached_byte_size_;
  ::google::protobuf::internal::ArenaStringPtr base_type_name_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsExtensionNumberResponseImpl();
};
// -------------------------------------------------------------------

class ListServiceResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.reflection.v1alpha.ListServiceResponse) */ {
 public:
  ListServiceResponse();
  virtual ~ListServiceResponse();

  ListServiceResponse(const ListServiceResponse& from);

  inline ListServiceResponse& operator=(const ListServiceResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ListServiceResponse(ListServiceResponse&& from) noexcept
    : ListServiceResponse() {
    *this = ::std::move(from);
  }

  inline ListServiceResponse& operator=(ListServiceResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ListServiceResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ListServiceResponse* internal_default_instance() {
    return reinterpret_cast<const ListServiceResponse*>(
               &_ListServiceResponse_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    5;

  void Swap(ListServiceResponse* other);
  friend void swap(ListServiceResponse& a, ListServiceResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ListServiceResponse* New() const PROTOBUF_FINAL { return New(NULL); }

  ListServiceResponse* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ListServiceResponse& from);
  void MergeFrom(const ListServiceResponse& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ListServiceResponse* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .grpc.reflection.v1alpha.ServiceResponse service = 1;
  int service_size() const;
  void clear_service();
  static const int kServiceFieldNumber = 1;
  const ::grpc::reflection::v1alpha::ServiceResponse& service(int index) const;
  ::grpc::reflection::v1alpha::ServiceResponse* mutable_service(int index);
  ::grpc::reflection::v1alpha::ServiceResponse* add_service();
  ::google::protobuf::RepeatedPtrField< ::grpc::reflection::v1alpha::ServiceResponse >*
      mutable_service();
  const ::google::protobuf::RepeatedPtrField< ::grpc::reflection::v1alpha::ServiceResponse >&
      service() const;

  // @@protoc_insertion_point(class_scope:grpc.reflection.v1alpha.ListServiceResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::grpc::reflection::v1alpha::ServiceResponse > service_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsListServiceResponseImpl();
};
// -------------------------------------------------------------------

class ServiceResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.reflection.v1alpha.ServiceResponse) */ {
 public:
  ServiceResponse();
  virtual ~ServiceResponse();

  ServiceResponse(const ServiceResponse& from);

  inline ServiceResponse& operator=(const ServiceResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ServiceResponse(ServiceResponse&& from) noexcept
    : ServiceResponse() {
    *this = ::std::move(from);
  }

  inline ServiceResponse& operator=(ServiceResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ServiceResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ServiceResponse* internal_default_instance() {
    return reinterpret_cast<const ServiceResponse*>(
               &_ServiceResponse_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    6;

  void Swap(ServiceResponse* other);
  friend void swap(ServiceResponse& a, ServiceResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ServiceResponse* New() const PROTOBUF_FINAL { return New(NULL); }

  ServiceResponse* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ServiceResponse& from);
  void MergeFrom(const ServiceResponse& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ServiceResponse* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // @@protoc_insertion_point(class_scope:grpc.reflection.v1alpha.ServiceResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsServiceResponseImpl();
};
// -------------------------------------------------------------------

class ErrorResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.reflection.v1alpha.ErrorResponse) */ {
 public:
  ErrorResponse();
  virtual ~ErrorResponse();

  ErrorResponse(const ErrorResponse& from);

  inline ErrorResponse& operator=(const ErrorResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ErrorResponse(ErrorResponse&& from) noexcept
    : ErrorResponse() {
    *this = ::std::move(from);
  }

  inline ErrorResponse& operator=(ErrorResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ErrorResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ErrorResponse* internal_default_instance() {
    return reinterpret_cast<const ErrorResponse*>(
               &_ErrorResponse_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    7;

  void Swap(ErrorResponse* other);
  friend void swap(ErrorResponse& a, ErrorResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ErrorResponse* New() const PROTOBUF_FINAL { return New(NULL); }

  ErrorResponse* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ErrorResponse& from);
  void MergeFrom(const ErrorResponse& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ErrorResponse* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string error_message = 2;
  void clear_error_message();
  static const int kErrorMessageFieldNumber = 2;
  const ::std::string& error_message() const;
  void set_error_message(const ::std::string& value);
  #if LANG_CXX11
  void set_error_message(::std::string&& value);
  #endif
  void set_error_message(const char* value);
  void set_error_message(const char* value, size_t size);
  ::std::string* mutable_error_message();
  ::std::string* release_error_message();
  void set_allocated_error_message(::std::string* error_message);

  // int32 error_code = 1;
  void clear_error_code();
  static const int kErrorCodeFieldNumber = 1;
  ::google::protobuf::int32 error_code() const;
  void set_error_code(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:grpc.reflection.v1alpha.ErrorResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr error_message_;
  ::google::protobuf::int32 error_code_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto::InitDefaultsErrorResponseImpl();
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ServerReflectionRequest

// string host = 1;
inline void ServerReflectionRequest::clear_host() {
  host_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ServerReflectionRequest::host() const {
  // @@protoc_insertion_point(field_get:grpc.reflection.v1alpha.ServerReflectionRequest.host)
  return host_.GetNoArena();
}
inline void ServerReflectionRequest::set_host(const ::std::string& value) {
  
  host_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.reflection.v1alpha.ServerReflectionRequest.host)
}
#if LANG_CXX11
inline void ServerReflectionRequest::set_host(::std::string&& value) {
  
  host_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.reflection.v1alpha.ServerReflectionRequest.host)
}
#endif
inline void ServerReflectionRequest::set_host(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  host_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.reflection.v1alpha.ServerReflectionRequest.host)
}
inline void ServerReflectionRequest::set_host(const char* value, size_t size) {
  
  host_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.reflection.v1alpha.ServerReflectionRequest.host)
}
inline ::std::string* ServerReflectionRequest::mutable_host() {
  
  // @@protoc_insertion_point(field_mutable:grpc.reflection.v1alpha.ServerReflectionRequest.host)
  return host_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ServerReflectionRequest::release_host() {
  // @@protoc_insertion_point(field_release:grpc.reflection.v1alpha.ServerReflectionRequest.host)
  
  return host_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ServerReflectionRequest::set_allocated_host(::std::string* host) {
  if (host != NULL) {
    
  } else {
    
  }
  host_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), host);
  // @@protoc_insertion_point(field_set_allocated:grpc.reflection.v1alpha.ServerReflectionRequest.host)
}

// string file_by_filename = 3;
inline bool ServerReflectionRequest::has_file_by_filename() const {
  return message_request_case() == kFileByFilename;
}
inline void ServerReflectionRequest::set_has_file_by_filename() {
  _oneof_case_[0] = kFileByFilename;
}
inline void ServerReflectionRequest::clear_file_by_filename() {
  if (has_file_by_filename()) {
    message_request_.file_by_filename_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    clear_has_message_request();
  }
}
inline const ::std::string& ServerReflectionRequest::file_by_filename() const {
  // @@protoc_insertion_point(field_get:grpc.reflection.v1alpha.ServerReflectionRequest.file_by_filename)
  if (has_file_by_filename()) {
    return message_request_.file_by_filename_.GetNoArena();
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void ServerReflectionRequest::set_file_by_filename(const ::std::string& value) {
  // @@protoc_insertion_point(field_set:grpc.reflection.v1alpha.ServerReflectionRequest.file_by_filename)
  if (!has_file_by_filename()) {
    clear_message_request();
    set_has_file_by_filename();
    message_request_.file_by_filename_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  message_request_.file_by_filename_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.reflection.v1alpha.ServerReflectionRequest.file_by_filename)
}
#if LANG_CXX11
inline void ServerReflectionRequest::set_file_by_filename(::std::string&& value) {
  // @@protoc_insertion_point(field_set:grpc.reflection.v1alpha.ServerReflectionRequest.file_by_filename)
  if (!has_file_by_filename()) {
    clear_message_request();
    set_has_file_by_filename();
    message_request_.file_by_filename_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  message_request_.file_by_filename_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.reflection.v1alpha.ServerReflectionRequest.file_by_filename)
}
#endif
inline void ServerReflectionRequest::set_file_by_filename(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  if (!has_file_by_filename()) {
    clear_message_request();
    set_has_file_by_filename();
    message_request_.file_by_filename_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  message_request_.file_by_filename_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.reflection.v1alpha.ServerReflectionRequest.file_by_filename)
}
inline void ServerReflectionRequest::set_file_by_filename(const char* value, size_t size) {
  if (!has_file_by_filename()) {
    clear_message_request();
    set_has_file_by_filename();
    message_request_.file_by_filename_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  message_request_.file_by_filename_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.reflection.v1alpha.ServerReflectionRequest.file_by_filename)
}
inline ::std::string* ServerReflectionRequest::mutable_file_by_filename() {
  if (!has_file_by_filename()) {
    clear_message_request();
    set_has_file_by_filename();
    message_request_.file_by_filename_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_mutable:grpc.reflection.v1alpha.ServerReflectionRequest.file_by_filename)
  return message_request_.file_by_filename_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ServerReflectionRequest::release_file_by_filename() {
  // @@protoc_insertion_point(field_release:grpc.reflection.v1alpha.ServerReflectionRequest.file_by_filename)
  if (has_file_by_filename()) {
    clear_has_message_request();
    return message_request_.file_by_filename_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  } else {
    return NULL;
  }
}
inline void ServerReflectionRequest::set_allocated_file_by_filename(::std::string* file_by_filename) {
  if (!has_file_by_filename()) {
    message_request_.file_by_filename_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_message_request();
  if (file_by_filename != NULL) {
    set_has_file_by_filename();
    message_request_.file_by_filename_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        file_by_filename);
  }
  // @@protoc_insertion_point(field_set_allocated:grpc.reflection.v1alpha.ServerReflectionRequest.file_by_filename)
}

// string file_containing_symbol = 4;
inline bool ServerReflectionRequest::has_file_containing_symbol() const {
  return message_request_case() == kFileContainingSymbol;
}
inline void ServerReflectionRequest::set_has_file_containing_symbol() {
  _oneof_case_[0] = kFileContainingSymbol;
}
inline void ServerReflectionRequest::clear_file_containing_symbol() {
  if (has_file_containing_symbol()) {
    message_request_.file_containing_symbol_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    clear_has_message_request();
  }
}
inline const ::std::string& ServerReflectionRequest::file_containing_symbol() const {
  // @@protoc_insertion_point(field_get:grpc.reflection.v1alpha.ServerReflectionRequest.file_containing_symbol)
  if (has_file_containing_symbol()) {
    return message_request_.file_containing_symbol_.GetNoArena();
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void ServerReflectionRequest::set_file_containing_symbol(const ::std::string& value) {
  // @@protoc_insertion_point(field_set:grpc.reflection.v1alpha.ServerReflectionRequest.file_containing_symbol)
  if (!has_file_containing_symbol()) {
    clear_message_request();
    set_has_file_containing_symbol();
    message_request_.file_containing_symbol_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  message_request_.file_containing_symbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.reflection.v1alpha.ServerReflectionRequest.file_containing_symbol)
}
#if LANG_CXX11
inline void ServerReflectionRequest::set_file_containing_symbol(::std::string&& value) {
  // @@protoc_insertion_point(field_set:grpc.reflection.v1alpha.ServerReflectionRequest.file_containing_symbol)
  if (!has_file_containing_symbol()) {
    clear_message_request();
    set_has_file_containing_symbol();
    message_request_.file_containing_symbol_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  message_request_.file_containing_symbol_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.reflection.v1alpha.ServerReflectionRequest.file_containing_symbol)
}
#endif
inline void ServerReflectionRequest::set_file_containing_symbol(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  if (!has_file_containing_symbol()) {
    clear_message_request();
    set_has_file_containing_symbol();
    message_request_.file_containing_symbol_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  message_request_.file_containing_symbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.reflection.v1alpha.ServerReflectionRequest.file_containing_symbol)
}
inline void ServerReflectionRequest::set_file_containing_symbol(const char* value, size_t size) {
  if (!has_file_containing_symbol()) {
    clear_message_request();
    set_has_file_containing_symbol();
    message_request_.file_containing_symbol_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  message_request_.file_containing_symbol_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.reflection.v1alpha.ServerReflectionRequest.file_containing_symbol)
}
inline ::std::string* ServerReflectionRequest::mutable_file_containing_symbol() {
  if (!has_file_containing_symbol()) {
    clear_message_request();
    set_has_file_containing_symbol();
    message_request_.file_containing_symbol_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_mutable:grpc.reflection.v1alpha.ServerReflectionRequest.file_containing_symbol)
  return message_request_.file_containing_symbol_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ServerReflectionRequest::release_file_containing_symbol() {
  // @@protoc_insertion_point(field_release:grpc.reflection.v1alpha.ServerReflectionRequest.file_containing_symbol)
  if (has_file_containing_symbol()) {
    clear_has_message_request();
    return message_request_.file_containing_symbol_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  } else {
    return NULL;
  }
}
inline void ServerReflectionRequest::set_allocated_file_containing_symbol(::std::string* file_containing_symbol) {
  if (!has_file_containing_symbol()) {
    message_request_.file_containing_symbol_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_message_request();
  if (file_containing_symbol != NULL) {
    set_has_file_containing_symbol();
    message_request_.file_containing_symbol_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        file_containing_symbol);
  }
  // @@protoc_insertion_point(field_set_allocated:grpc.reflection.v1alpha.ServerReflectionRequest.file_containing_symbol)
}

// .grpc.reflection.v1alpha.ExtensionRequest file_containing_extension = 5;
inline bool ServerReflectionRequest::has_file_containing_extension() const {
  return message_request_case() == kFileContainingExtension;
}
inline void ServerReflectionRequest::set_has_file_containing_extension() {
  _oneof_case_[0] = kFileContainingExtension;
}
inline void ServerReflectionRequest::clear_file_containing_extension() {
  if (has_file_containing_extension()) {
    delete message_request_.file_containing_extension_;
    clear_has_message_request();
  }
}
inline ::grpc::reflection::v1alpha::ExtensionRequest* ServerReflectionRequest::release_file_containing_extension() {
  // @@protoc_insertion_point(field_release:grpc.reflection.v1alpha.ServerReflectionRequest.file_containing_extension)
  if (has_file_containing_extension()) {
    clear_has_message_request();
      ::grpc::reflection::v1alpha::ExtensionRequest* temp = message_request_.file_containing_extension_;
    message_request_.file_containing_extension_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::grpc::reflection::v1alpha::ExtensionRequest& ServerReflectionRequest::file_containing_extension() const {
  // @@protoc_insertion_point(field_get:grpc.reflection.v1alpha.ServerReflectionRequest.file_containing_extension)
  return has_file_containing_extension()
      ? *message_request_.file_containing_extension_
      : *reinterpret_cast< ::grpc::reflection::v1alpha::ExtensionRequest*>(&::grpc::reflection::v1alpha::_ExtensionRequest_default_instance_);
}
inline ::grpc::reflection::v1alpha::ExtensionRequest* ServerReflectionRequest::mutable_file_containing_extension() {
  if (!has_file_containing_extension()) {
    clear_message_request();
    set_has_file_containing_extension();
    message_request_.file_containing_extension_ = new ::grpc::reflection::v1alpha::ExtensionRequest;
  }
  // @@protoc_insertion_point(field_mutable:grpc.reflection.v1alpha.ServerReflectionRequest.file_containing_extension)
  return message_request_.file_containing_extension_;
}

// string all_extension_numbers_of_type = 6;
inline bool ServerReflectionRequest::has_all_extension_numbers_of_type() const {
  return message_request_case() == kAllExtensionNumbersOfType;
}
inline void ServerReflectionRequest::set_has_all_extension_numbers_of_type() {
  _oneof_case_[0] = kAllExtensionNumbersOfType;
}
inline void ServerReflectionRequest::clear_all_extension_numbers_of_type() {
  if (has_all_extension_numbers_of_type()) {
    message_request_.all_extension_numbers_of_type_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    clear_has_message_request();
  }
}
inline const ::std::string& ServerReflectionRequest::all_extension_numbers_of_type() const {
  // @@protoc_insertion_point(field_get:grpc.reflection.v1alpha.ServerReflectionRequest.all_extension_numbers_of_type)
  if (has_all_extension_numbers_of_type()) {
    return message_request_.all_extension_numbers_of_type_.GetNoArena();
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void ServerReflectionRequest::set_all_extension_numbers_of_type(const ::std::string& value) {
  // @@protoc_insertion_point(field_set:grpc.reflection.v1alpha.ServerReflectionRequest.all_extension_numbers_of_type)
  if (!has_all_extension_numbers_of_type()) {
    clear_message_request();
    set_has_all_extension_numbers_of_type();
    message_request_.all_extension_numbers_of_type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  message_request_.all_extension_numbers_of_type_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.reflection.v1alpha.ServerReflectionRequest.all_extension_numbers_of_type)
}
#if LANG_CXX11
inline void ServerReflectionRequest::set_all_extension_numbers_of_type(::std::string&& value) {
  // @@protoc_insertion_point(field_set:grpc.reflection.v1alpha.ServerReflectionRequest.all_extension_numbers_of_type)
  if (!has_all_extension_numbers_of_type()) {
    clear_message_request();
    set_has_all_extension_numbers_of_type();
    message_request_.all_extension_numbers_of_type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  message_request_.all_extension_numbers_of_type_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.reflection.v1alpha.ServerReflectionRequest.all_extension_numbers_of_type)
}
#endif
inline void ServerReflectionRequest::set_all_extension_numbers_of_type(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  if (!has_all_extension_numbers_of_type()) {
    clear_message_request();
    set_has_all_extension_numbers_of_type();
    message_request_.all_extension_numbers_of_type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  message_request_.all_extension_numbers_of_type_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.reflection.v1alpha.ServerReflectionRequest.all_extension_numbers_of_type)
}
inline void ServerReflectionRequest::set_all_extension_numbers_of_type(const char* value, size_t size) {
  if (!has_all_extension_numbers_of_type()) {
    clear_message_request();
    set_has_all_extension_numbers_of_type();
    message_request_.all_extension_numbers_of_type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  message_request_.all_extension_numbers_of_type_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.reflection.v1alpha.ServerReflectionRequest.all_extension_numbers_of_type)
}
inline ::std::string* ServerReflectionRequest::mutable_all_extension_numbers_of_type() {
  if (!has_all_extension_numbers_of_type()) {
    clear_message_request();
    set_has_all_extension_numbers_of_type();
    message_request_.all_extension_numbers_of_type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_mutable:grpc.reflection.v1alpha.ServerReflectionRequest.all_extension_numbers_of_type)
  return message_request_.all_extension_numbers_of_type_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ServerReflectionRequest::release_all_extension_numbers_of_type() {
  // @@protoc_insertion_point(field_release:grpc.reflection.v1alpha.ServerReflectionRequest.all_extension_numbers_of_type)
  if (has_all_extension_numbers_of_type()) {
    clear_has_message_request();
    return message_request_.all_extension_numbers_of_type_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  } else {
    return NULL;
  }
}
inline void ServerReflectionRequest::set_allocated_all_extension_numbers_of_type(::std::string* all_extension_numbers_of_type) {
  if (!has_all_extension_numbers_of_type()) {
    message_request_.all_extension_numbers_of_type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_message_request();
  if (all_extension_numbers_of_type != NULL) {
    set_has_all_extension_numbers_of_type();
    message_request_.all_extension_numbers_of_type_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        all_extension_numbers_of_type);
  }
  // @@protoc_insertion_point(field_set_allocated:grpc.reflection.v1alpha.ServerReflectionRequest.all_extension_numbers_of_type)
}

// string list_services = 7;
inline bool ServerReflectionRequest::has_list_services() const {
  return message_request_case() == kListServices;
}
inline void ServerReflectionRequest::set_has_list_services() {
  _oneof_case_[0] = kListServices;
}
inline void ServerReflectionRequest::clear_list_services() {
  if (has_list_services()) {
    message_request_.list_services_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    clear_has_message_request();
  }
}
inline const ::std::string& ServerReflectionRequest::list_services() const {
  // @@protoc_insertion_point(field_get:grpc.reflection.v1alpha.ServerReflectionRequest.list_services)
  if (has_list_services()) {
    return message_request_.list_services_.GetNoArena();
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void ServerReflectionRequest::set_list_services(const ::std::string& value) {
  // @@protoc_insertion_point(field_set:grpc.reflection.v1alpha.ServerReflectionRequest.list_services)
  if (!has_list_services()) {
    clear_message_request();
    set_has_list_services();
    message_request_.list_services_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  message_request_.list_services_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.reflection.v1alpha.ServerReflectionRequest.list_services)
}
#if LANG_CXX11
inline void ServerReflectionRequest::set_list_services(::std::string&& value) {
  // @@protoc_insertion_point(field_set:grpc.reflection.v1alpha.ServerReflectionRequest.list_services)
  if (!has_list_services()) {
    clear_message_request();
    set_has_list_services();
    message_request_.list_services_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  message_request_.list_services_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.reflection.v1alpha.ServerReflectionRequest.list_services)
}
#endif
inline void ServerReflectionRequest::set_list_services(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  if (!has_list_services()) {
    clear_message_request();
    set_has_list_services();
    message_request_.list_services_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  message_request_.list_services_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.reflection.v1alpha.ServerReflectionRequest.list_services)
}
inline void ServerReflectionRequest::set_list_services(const char* value, size_t size) {
  if (!has_list_services()) {
    clear_message_request();
    set_has_list_services();
    message_request_.list_services_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  message_request_.list_services_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.reflection.v1alpha.ServerReflectionRequest.list_services)
}
inline ::std::string* ServerReflectionRequest::mutable_list_services() {
  if (!has_list_services()) {
    clear_message_request();
    set_has_list_services();
    message_request_.list_services_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_mutable:grpc.reflection.v1alpha.ServerReflectionRequest.list_services)
  return message_request_.list_services_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ServerReflectionRequest::release_list_services() {
  // @@protoc_insertion_point(field_release:grpc.reflection.v1alpha.ServerReflectionRequest.list_services)
  if (has_list_services()) {
    clear_has_message_request();
    return message_request_.list_services_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  } else {
    return NULL;
  }
}
inline void ServerReflectionRequest::set_allocated_list_services(::std::string* list_services) {
  if (!has_list_services()) {
    message_request_.list_services_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_message_request();
  if (list_services != NULL) {
    set_has_list_services();
    message_request_.list_services_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        list_services);
  }
  // @@protoc_insertion_point(field_set_allocated:grpc.reflection.v1alpha.ServerReflectionRequest.list_services)
}

inline bool ServerReflectionRequest::has_message_request() const {
  return message_request_case() != MESSAGE_REQUEST_NOT_SET;
}
inline void ServerReflectionRequest::clear_has_message_request() {
  _oneof_case_[0] = MESSAGE_REQUEST_NOT_SET;
}
inline ServerReflectionRequest::MessageRequestCase ServerReflectionRequest::message_request_case() const {
  return ServerReflectionRequest::MessageRequestCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// ExtensionRequest

// string containing_type = 1;
inline void ExtensionRequest::clear_containing_type() {
  containing_type_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ExtensionRequest::containing_type() const {
  // @@protoc_insertion_point(field_get:grpc.reflection.v1alpha.ExtensionRequest.containing_type)
  return containing_type_.GetNoArena();
}
inline void ExtensionRequest::set_containing_type(const ::std::string& value) {
  
  containing_type_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.reflection.v1alpha.ExtensionRequest.containing_type)
}
#if LANG_CXX11
inline void ExtensionRequest::set_containing_type(::std::string&& value) {
  
  containing_type_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.reflection.v1alpha.ExtensionRequest.containing_type)
}
#endif
inline void ExtensionRequest::set_containing_type(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  containing_type_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.reflection.v1alpha.ExtensionRequest.containing_type)
}
inline void ExtensionRequest::set_containing_type(const char* value, size_t size) {
  
  containing_type_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.reflection.v1alpha.ExtensionRequest.containing_type)
}
inline ::std::string* ExtensionRequest::mutable_containing_type() {
  
  // @@protoc_insertion_point(field_mutable:grpc.reflection.v1alpha.ExtensionRequest.containing_type)
  return containing_type_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ExtensionRequest::release_containing_type() {
  // @@protoc_insertion_point(field_release:grpc.reflection.v1alpha.ExtensionRequest.containing_type)
  
  return containing_type_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ExtensionRequest::set_allocated_containing_type(::std::string* containing_type) {
  if (containing_type != NULL) {
    
  } else {
    
  }
  containing_type_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), containing_type);
  // @@protoc_insertion_point(field_set_allocated:grpc.reflection.v1alpha.ExtensionRequest.containing_type)
}

// int32 extension_number = 2;
inline void ExtensionRequest::clear_extension_number() {
  extension_number_ = 0;
}
inline ::google::protobuf::int32 ExtensionRequest::extension_number() const {
  // @@protoc_insertion_point(field_get:grpc.reflection.v1alpha.ExtensionRequest.extension_number)
  return extension_number_;
}
inline void ExtensionRequest::set_extension_number(::google::protobuf::int32 value) {
  
  extension_number_ = value;
  // @@protoc_insertion_point(field_set:grpc.reflection.v1alpha.ExtensionRequest.extension_number)
}

// -------------------------------------------------------------------

// ServerReflectionResponse

// string valid_host = 1;
inline void ServerReflectionResponse::clear_valid_host() {
  valid_host_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ServerReflectionResponse::valid_host() const {
  // @@protoc_insertion_point(field_get:grpc.reflection.v1alpha.ServerReflectionResponse.valid_host)
  return valid_host_.GetNoArena();
}
inline void ServerReflectionResponse::set_valid_host(const ::std::string& value) {
  
  valid_host_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.reflection.v1alpha.ServerReflectionResponse.valid_host)
}
#if LANG_CXX11
inline void ServerReflectionResponse::set_valid_host(::std::string&& value) {
  
  valid_host_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.reflection.v1alpha.ServerReflectionResponse.valid_host)
}
#endif
inline void ServerReflectionResponse::set_valid_host(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  valid_host_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.reflection.v1alpha.ServerReflectionResponse.valid_host)
}
inline void ServerReflectionResponse::set_valid_host(const char* value, size_t size) {
  
  valid_host_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.reflection.v1alpha.ServerReflectionResponse.valid_host)
}
inline ::std::string* ServerReflectionResponse::mutable_valid_host() {
  
  // @@protoc_insertion_point(field_mutable:grpc.reflection.v1alpha.ServerReflectionResponse.valid_host)
  return valid_host_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ServerReflectionResponse::release_valid_host() {
  // @@protoc_insertion_point(field_release:grpc.reflection.v1alpha.ServerReflectionResponse.valid_host)
  
  return valid_host_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ServerReflectionResponse::set_allocated_valid_host(::std::string* valid_host) {
  if (valid_host != NULL) {
    
  } else {
    
  }
  valid_host_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), valid_host);
  // @@protoc_insertion_point(field_set_allocated:grpc.reflection.v1alpha.ServerReflectionResponse.valid_host)
}

// .grpc.reflection.v1alpha.ServerReflectionRequest original_request = 2;
inline bool ServerReflectionResponse::has_original_request() const {
  return this != internal_default_instance() && original_request_ != NULL;
}
inline void ServerReflectionResponse::clear_original_request() {
  if (GetArenaNoVirtual() == NULL && original_request_ != NULL) {
    delete original_request_;
  }
  original_request_ = NULL;
}
inline const ::grpc::reflection::v1alpha::ServerReflectionRequest& ServerReflectionResponse::original_request() const {
  const ::grpc::reflection::v1alpha::ServerReflectionRequest* p = original_request_;
  // @@protoc_insertion_point(field_get:grpc.reflection.v1alpha.ServerReflectionResponse.original_request)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::reflection::v1alpha::ServerReflectionRequest*>(
      &::grpc::reflection::v1alpha::_ServerReflectionRequest_default_instance_);
}
inline ::grpc::reflection::v1alpha::ServerReflectionRequest* ServerReflectionResponse::release_original_request() {
  // @@protoc_insertion_point(field_release:grpc.reflection.v1alpha.ServerReflectionResponse.original_request)
  
  ::grpc::reflection::v1alpha::ServerReflectionRequest* temp = original_request_;
  original_request_ = NULL;
  return temp;
}
inline ::grpc::reflection::v1alpha::ServerReflectionRequest* ServerReflectionResponse::mutable_original_request() {
  
  if (original_request_ == NULL) {
    original_request_ = new ::grpc::reflection::v1alpha::ServerReflectionRequest;
  }
  // @@protoc_insertion_point(field_mutable:grpc.reflection.v1alpha.ServerReflectionResponse.original_request)
  return original_request_;
}
inline void ServerReflectionResponse::set_allocated_original_request(::grpc::reflection::v1alpha::ServerReflectionRequest* original_request) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete original_request_;
  }
  if (original_request) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      original_request = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, original_request, submessage_arena);
    }
    
  } else {
    
  }
  original_request_ = original_request;
  // @@protoc_insertion_point(field_set_allocated:grpc.reflection.v1alpha.ServerReflectionResponse.original_request)
}

// .grpc.reflection.v1alpha.FileDescriptorResponse file_descriptor_response = 4;
inline bool ServerReflectionResponse::has_file_descriptor_response() const {
  return message_response_case() == kFileDescriptorResponse;
}
inline void ServerReflectionResponse::set_has_file_descriptor_response() {
  _oneof_case_[0] = kFileDescriptorResponse;
}
inline void ServerReflectionResponse::clear_file_descriptor_response() {
  if (has_file_descriptor_response()) {
    delete message_response_.file_descriptor_response_;
    clear_has_message_response();
  }
}
inline ::grpc::reflection::v1alpha::FileDescriptorResponse* ServerReflectionResponse::release_file_descriptor_response() {
  // @@protoc_insertion_point(field_release:grpc.reflection.v1alpha.ServerReflectionResponse.file_descriptor_response)
  if (has_file_descriptor_response()) {
    clear_has_message_response();
      ::grpc::reflection::v1alpha::FileDescriptorResponse* temp = message_response_.file_descriptor_response_;
    message_response_.file_descriptor_response_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::grpc::reflection::v1alpha::FileDescriptorResponse& ServerReflectionResponse::file_descriptor_response() const {
  // @@protoc_insertion_point(field_get:grpc.reflection.v1alpha.ServerReflectionResponse.file_descriptor_response)
  return has_file_descriptor_response()
      ? *message_response_.file_descriptor_response_
      : *reinterpret_cast< ::grpc::reflection::v1alpha::FileDescriptorResponse*>(&::grpc::reflection::v1alpha::_FileDescriptorResponse_default_instance_);
}
inline ::grpc::reflection::v1alpha::FileDescriptorResponse* ServerReflectionResponse::mutable_file_descriptor_response() {
  if (!has_file_descriptor_response()) {
    clear_message_response();
    set_has_file_descriptor_response();
    message_response_.file_descriptor_response_ = new ::grpc::reflection::v1alpha::FileDescriptorResponse;
  }
  // @@protoc_insertion_point(field_mutable:grpc.reflection.v1alpha.ServerReflectionResponse.file_descriptor_response)
  return message_response_.file_descriptor_response_;
}

// .grpc.reflection.v1alpha.ExtensionNumberResponse all_extension_numbers_response = 5;
inline bool ServerReflectionResponse::has_all_extension_numbers_response() const {
  return message_response_case() == kAllExtensionNumbersResponse;
}
inline void ServerReflectionResponse::set_has_all_extension_numbers_response() {
  _oneof_case_[0] = kAllExtensionNumbersResponse;
}
inline void ServerReflectionResponse::clear_all_extension_numbers_response() {
  if (has_all_extension_numbers_response()) {
    delete message_response_.all_extension_numbers_response_;
    clear_has_message_response();
  }
}
inline ::grpc::reflection::v1alpha::ExtensionNumberResponse* ServerReflectionResponse::release_all_extension_numbers_response() {
  // @@protoc_insertion_point(field_release:grpc.reflection.v1alpha.ServerReflectionResponse.all_extension_numbers_response)
  if (has_all_extension_numbers_response()) {
    clear_has_message_response();
      ::grpc::reflection::v1alpha::ExtensionNumberResponse* temp = message_response_.all_extension_numbers_response_;
    message_response_.all_extension_numbers_response_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::grpc::reflection::v1alpha::ExtensionNumberResponse& ServerReflectionResponse::all_extension_numbers_response() const {
  // @@protoc_insertion_point(field_get:grpc.reflection.v1alpha.ServerReflectionResponse.all_extension_numbers_response)
  return has_all_extension_numbers_response()
      ? *message_response_.all_extension_numbers_response_
      : *reinterpret_cast< ::grpc::reflection::v1alpha::ExtensionNumberResponse*>(&::grpc::reflection::v1alpha::_ExtensionNumberResponse_default_instance_);
}
inline ::grpc::reflection::v1alpha::ExtensionNumberResponse* ServerReflectionResponse::mutable_all_extension_numbers_response() {
  if (!has_all_extension_numbers_response()) {
    clear_message_response();
    set_has_all_extension_numbers_response();
    message_response_.all_extension_numbers_response_ = new ::grpc::reflection::v1alpha::ExtensionNumberResponse;
  }
  // @@protoc_insertion_point(field_mutable:grpc.reflection.v1alpha.ServerReflectionResponse.all_extension_numbers_response)
  return message_response_.all_extension_numbers_response_;
}

// .grpc.reflection.v1alpha.ListServiceResponse list_services_response = 6;
inline bool ServerReflectionResponse::has_list_services_response() const {
  return message_response_case() == kListServicesResponse;
}
inline void ServerReflectionResponse::set_has_list_services_response() {
  _oneof_case_[0] = kListServicesResponse;
}
inline void ServerReflectionResponse::clear_list_services_response() {
  if (has_list_services_response()) {
    delete message_response_.list_services_response_;
    clear_has_message_response();
  }
}
inline ::grpc::reflection::v1alpha::ListServiceResponse* ServerReflectionResponse::release_list_services_response() {
  // @@protoc_insertion_point(field_release:grpc.reflection.v1alpha.ServerReflectionResponse.list_services_response)
  if (has_list_services_response()) {
    clear_has_message_response();
      ::grpc::reflection::v1alpha::ListServiceResponse* temp = message_response_.list_services_response_;
    message_response_.list_services_response_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::grpc::reflection::v1alpha::ListServiceResponse& ServerReflectionResponse::list_services_response() const {
  // @@protoc_insertion_point(field_get:grpc.reflection.v1alpha.ServerReflectionResponse.list_services_response)
  return has_list_services_response()
      ? *message_response_.list_services_response_
      : *reinterpret_cast< ::grpc::reflection::v1alpha::ListServiceResponse*>(&::grpc::reflection::v1alpha::_ListServiceResponse_default_instance_);
}
inline ::grpc::reflection::v1alpha::ListServiceResponse* ServerReflectionResponse::mutable_list_services_response() {
  if (!has_list_services_response()) {
    clear_message_response();
    set_has_list_services_response();
    message_response_.list_services_response_ = new ::grpc::reflection::v1alpha::ListServiceResponse;
  }
  // @@protoc_insertion_point(field_mutable:grpc.reflection.v1alpha.ServerReflectionResponse.list_services_response)
  return message_response_.list_services_response_;
}

// .grpc.reflection.v1alpha.ErrorResponse error_response = 7;
inline bool ServerReflectionResponse::has_error_response() const {
  return message_response_case() == kErrorResponse;
}
inline void ServerReflectionResponse::set_has_error_response() {
  _oneof_case_[0] = kErrorResponse;
}
inline void ServerReflectionResponse::clear_error_response() {
  if (has_error_response()) {
    delete message_response_.error_response_;
    clear_has_message_response();
  }
}
inline ::grpc::reflection::v1alpha::ErrorResponse* ServerReflectionResponse::release_error_response() {
  // @@protoc_insertion_point(field_release:grpc.reflection.v1alpha.ServerReflectionResponse.error_response)
  if (has_error_response()) {
    clear_has_message_response();
      ::grpc::reflection::v1alpha::ErrorResponse* temp = message_response_.error_response_;
    message_response_.error_response_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::grpc::reflection::v1alpha::ErrorResponse& ServerReflectionResponse::error_response() const {
  // @@protoc_insertion_point(field_get:grpc.reflection.v1alpha.ServerReflectionResponse.error_response)
  return has_error_response()
      ? *message_response_.error_response_
      : *reinterpret_cast< ::grpc::reflection::v1alpha::ErrorResponse*>(&::grpc::reflection::v1alpha::_ErrorResponse_default_instance_);
}
inline ::grpc::reflection::v1alpha::ErrorResponse* ServerReflectionResponse::mutable_error_response() {
  if (!has_error_response()) {
    clear_message_response();
    set_has_error_response();
    message_response_.error_response_ = new ::grpc::reflection::v1alpha::ErrorResponse;
  }
  // @@protoc_insertion_point(field_mutable:grpc.reflection.v1alpha.ServerReflectionResponse.error_response)
  return message_response_.error_response_;
}

inline bool ServerReflectionResponse::has_message_response() const {
  return message_response_case() != MESSAGE_RESPONSE_NOT_SET;
}
inline void ServerReflectionResponse::clear_has_message_response() {
  _oneof_case_[0] = MESSAGE_RESPONSE_NOT_SET;
}
inline ServerReflectionResponse::MessageResponseCase ServerReflectionResponse::message_response_case() const {
  return ServerReflectionResponse::MessageResponseCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// FileDescriptorResponse

// repeated bytes file_descriptor_proto = 1;
inline int FileDescriptorResponse::file_descriptor_proto_size() const {
  return file_descriptor_proto_.size();
}
inline void FileDescriptorResponse::clear_file_descriptor_proto() {
  file_descriptor_proto_.Clear();
}
inline const ::std::string& FileDescriptorResponse::file_descriptor_proto(int index) const {
  // @@protoc_insertion_point(field_get:grpc.reflection.v1alpha.FileDescriptorResponse.file_descriptor_proto)
  return file_descriptor_proto_.Get(index);
}
inline ::std::string* FileDescriptorResponse::mutable_file_descriptor_proto(int index) {
  // @@protoc_insertion_point(field_mutable:grpc.reflection.v1alpha.FileDescriptorResponse.file_descriptor_proto)
  return file_descriptor_proto_.Mutable(index);
}
inline void FileDescriptorResponse::set_file_descriptor_proto(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:grpc.reflection.v1alpha.FileDescriptorResponse.file_descriptor_proto)
  file_descriptor_proto_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void FileDescriptorResponse::set_file_descriptor_proto(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:grpc.reflection.v1alpha.FileDescriptorResponse.file_descriptor_proto)
  file_descriptor_proto_.Mutable(index)->assign(std::move(value));
}
#endif
inline void FileDescriptorResponse::set_file_descriptor_proto(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  file_descriptor_proto_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:grpc.reflection.v1alpha.FileDescriptorResponse.file_descriptor_proto)
}
inline void FileDescriptorResponse::set_file_descriptor_proto(int index, const void* value, size_t size) {
  file_descriptor_proto_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:grpc.reflection.v1alpha.FileDescriptorResponse.file_descriptor_proto)
}
inline ::std::string* FileDescriptorResponse::add_file_descriptor_proto() {
  // @@protoc_insertion_point(field_add_mutable:grpc.reflection.v1alpha.FileDescriptorResponse.file_descriptor_proto)
  return file_descriptor_proto_.Add();
}
inline void FileDescriptorResponse::add_file_descriptor_proto(const ::std::string& value) {
  file_descriptor_proto_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:grpc.reflection.v1alpha.FileDescriptorResponse.file_descriptor_proto)
}
#if LANG_CXX11
inline void FileDescriptorResponse::add_file_descriptor_proto(::std::string&& value) {
  file_descriptor_proto_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:grpc.reflection.v1alpha.FileDescriptorResponse.file_descriptor_proto)
}
#endif
inline void FileDescriptorResponse::add_file_descriptor_proto(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  file_descriptor_proto_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:grpc.reflection.v1alpha.FileDescriptorResponse.file_descriptor_proto)
}
inline void FileDescriptorResponse::add_file_descriptor_proto(const void* value, size_t size) {
  file_descriptor_proto_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:grpc.reflection.v1alpha.FileDescriptorResponse.file_descriptor_proto)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
FileDescriptorResponse::file_descriptor_proto() const {
  // @@protoc_insertion_point(field_list:grpc.reflection.v1alpha.FileDescriptorResponse.file_descriptor_proto)
  return file_descriptor_proto_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
FileDescriptorResponse::mutable_file_descriptor_proto() {
  // @@protoc_insertion_point(field_mutable_list:grpc.reflection.v1alpha.FileDescriptorResponse.file_descriptor_proto)
  return &file_descriptor_proto_;
}

// -------------------------------------------------------------------

// ExtensionNumberResponse

// string base_type_name = 1;
inline void ExtensionNumberResponse::clear_base_type_name() {
  base_type_name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ExtensionNumberResponse::base_type_name() const {
  // @@protoc_insertion_point(field_get:grpc.reflection.v1alpha.ExtensionNumberResponse.base_type_name)
  return base_type_name_.GetNoArena();
}
inline void ExtensionNumberResponse::set_base_type_name(const ::std::string& value) {
  
  base_type_name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.reflection.v1alpha.ExtensionNumberResponse.base_type_name)
}
#if LANG_CXX11
inline void ExtensionNumberResponse::set_base_type_name(::std::string&& value) {
  
  base_type_name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.reflection.v1alpha.ExtensionNumberResponse.base_type_name)
}
#endif
inline void ExtensionNumberResponse::set_base_type_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  base_type_name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.reflection.v1alpha.ExtensionNumberResponse.base_type_name)
}
inline void ExtensionNumberResponse::set_base_type_name(const char* value, size_t size) {
  
  base_type_name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.reflection.v1alpha.ExtensionNumberResponse.base_type_name)
}
inline ::std::string* ExtensionNumberResponse::mutable_base_type_name() {
  
  // @@protoc_insertion_point(field_mutable:grpc.reflection.v1alpha.ExtensionNumberResponse.base_type_name)
  return base_type_name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ExtensionNumberResponse::release_base_type_name() {
  // @@protoc_insertion_point(field_release:grpc.reflection.v1alpha.ExtensionNumberResponse.base_type_name)
  
  return base_type_name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ExtensionNumberResponse::set_allocated_base_type_name(::std::string* base_type_name) {
  if (base_type_name != NULL) {
    
  } else {
    
  }
  base_type_name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), base_type_name);
  // @@protoc_insertion_point(field_set_allocated:grpc.reflection.v1alpha.ExtensionNumberResponse.base_type_name)
}

// repeated int32 extension_number = 2;
inline int ExtensionNumberResponse::extension_number_size() const {
  return extension_number_.size();
}
inline void ExtensionNumberResponse::clear_extension_number() {
  extension_number_.Clear();
}
inline ::google::protobuf::int32 ExtensionNumberResponse::extension_number(int index) const {
  // @@protoc_insertion_point(field_get:grpc.reflection.v1alpha.ExtensionNumberResponse.extension_number)
  return extension_number_.Get(index);
}
inline void ExtensionNumberResponse::set_extension_number(int index, ::google::protobuf::int32 value) {
  extension_number_.Set(index, value);
  // @@protoc_insertion_point(field_set:grpc.reflection.v1alpha.ExtensionNumberResponse.extension_number)
}
inline void ExtensionNumberResponse::add_extension_number(::google::protobuf::int32 value) {
  extension_number_.Add(value);
  // @@protoc_insertion_point(field_add:grpc.reflection.v1alpha.ExtensionNumberResponse.extension_number)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
ExtensionNumberResponse::extension_number() const {
  // @@protoc_insertion_point(field_list:grpc.reflection.v1alpha.ExtensionNumberResponse.extension_number)
  return extension_number_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
ExtensionNumberResponse::mutable_extension_number() {
  // @@protoc_insertion_point(field_mutable_list:grpc.reflection.v1alpha.ExtensionNumberResponse.extension_number)
  return &extension_number_;
}

// -------------------------------------------------------------------

// ListServiceResponse

// repeated .grpc.reflection.v1alpha.ServiceResponse service = 1;
inline int ListServiceResponse::service_size() const {
  return service_.size();
}
inline void ListServiceResponse::clear_service() {
  service_.Clear();
}
inline const ::grpc::reflection::v1alpha::ServiceResponse& ListServiceResponse::service(int index) const {
  // @@protoc_insertion_point(field_get:grpc.reflection.v1alpha.ListServiceResponse.service)
  return service_.Get(index);
}
inline ::grpc::reflection::v1alpha::ServiceResponse* ListServiceResponse::mutable_service(int index) {
  // @@protoc_insertion_point(field_mutable:grpc.reflection.v1alpha.ListServiceResponse.service)
  return service_.Mutable(index);
}
inline ::grpc::reflection::v1alpha::ServiceResponse* ListServiceResponse::add_service() {
  // @@protoc_insertion_point(field_add:grpc.reflection.v1alpha.ListServiceResponse.service)
  return service_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::grpc::reflection::v1alpha::ServiceResponse >*
ListServiceResponse::mutable_service() {
  // @@protoc_insertion_point(field_mutable_list:grpc.reflection.v1alpha.ListServiceResponse.service)
  return &service_;
}
inline const ::google::protobuf::RepeatedPtrField< ::grpc::reflection::v1alpha::ServiceResponse >&
ListServiceResponse::service() const {
  // @@protoc_insertion_point(field_list:grpc.reflection.v1alpha.ListServiceResponse.service)
  return service_;
}

// -------------------------------------------------------------------

// ServiceResponse

// string name = 1;
inline void ServiceResponse::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ServiceResponse::name() const {
  // @@protoc_insertion_point(field_get:grpc.reflection.v1alpha.ServiceResponse.name)
  return name_.GetNoArena();
}
inline void ServiceResponse::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.reflection.v1alpha.ServiceResponse.name)
}
#if LANG_CXX11
inline void ServiceResponse::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.reflection.v1alpha.ServiceResponse.name)
}
#endif
inline void ServiceResponse::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.reflection.v1alpha.ServiceResponse.name)
}
inline void ServiceResponse::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.reflection.v1alpha.ServiceResponse.name)
}
inline ::std::string* ServiceResponse::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:grpc.reflection.v1alpha.ServiceResponse.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ServiceResponse::release_name() {
  // @@protoc_insertion_point(field_release:grpc.reflection.v1alpha.ServiceResponse.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ServiceResponse::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:grpc.reflection.v1alpha.ServiceResponse.name)
}

// -------------------------------------------------------------------

// ErrorResponse

// int32 error_code = 1;
inline void ErrorResponse::clear_error_code() {
  error_code_ = 0;
}
inline ::google::protobuf::int32 ErrorResponse::error_code() const {
  // @@protoc_insertion_point(field_get:grpc.reflection.v1alpha.ErrorResponse.error_code)
  return error_code_;
}
inline void ErrorResponse::set_error_code(::google::protobuf::int32 value) {
  
  error_code_ = value;
  // @@protoc_insertion_point(field_set:grpc.reflection.v1alpha.ErrorResponse.error_code)
}

// string error_message = 2;
inline void ErrorResponse::clear_error_message() {
  error_message_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ErrorResponse::error_message() const {
  // @@protoc_insertion_point(field_get:grpc.reflection.v1alpha.ErrorResponse.error_message)
  return error_message_.GetNoArena();
}
inline void ErrorResponse::set_error_message(const ::std::string& value) {
  
  error_message_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.reflection.v1alpha.ErrorResponse.error_message)
}
#if LANG_CXX11
inline void ErrorResponse::set_error_message(::std::string&& value) {
  
  error_message_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.reflection.v1alpha.ErrorResponse.error_message)
}
#endif
inline void ErrorResponse::set_error_message(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  error_message_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.reflection.v1alpha.ErrorResponse.error_message)
}
inline void ErrorResponse::set_error_message(const char* value, size_t size) {
  
  error_message_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.reflection.v1alpha.ErrorResponse.error_message)
}
inline ::std::string* ErrorResponse::mutable_error_message() {
  
  // @@protoc_insertion_point(field_mutable:grpc.reflection.v1alpha.ErrorResponse.error_message)
  return error_message_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ErrorResponse::release_error_message() {
  // @@protoc_insertion_point(field_release:grpc.reflection.v1alpha.ErrorResponse.error_message)
  
  return error_message_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ErrorResponse::set_allocated_error_message(::std::string* error_message) {
  if (error_message != NULL) {
    
  } else {
    
  }
  error_message_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), error_message);
  // @@protoc_insertion_point(field_set_allocated:grpc.reflection.v1alpha.ErrorResponse.error_message)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace v1alpha
}  // namespace reflection
}  // namespace grpc

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_src_2fproto_2fgrpc_2freflection_2fv1alpha_2freflection_2eproto__INCLUDED
