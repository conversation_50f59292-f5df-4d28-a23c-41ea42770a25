// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: src/proto/grpc/status/status.proto

#include "src/proto/grpc/status/status.pb.h"
#include "src/proto/grpc/status/status.grpc.pb.h"

#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/sync_stream.h>
#include <gmock/gmock.h>
namespace google {
namespace rpc {

} // namespace google
} // namespace rpc

