// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: src/proto/grpc/testing/benchmark_service.proto

#include "src/proto/grpc/testing/benchmark_service.pb.h"
#include "src/proto/grpc/testing/benchmark_service.grpc.pb.h"

#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/method_handler_impl.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace grpc {
namespace testing {

static const char* BenchmarkService_method_names[] = {
  "/grpc.testing.BenchmarkService/UnaryCall",
  "/grpc.testing.BenchmarkService/StreamingCall",
  "/grpc.testing.BenchmarkService/StreamingFromClient",
  "/grpc.testing.BenchmarkService/StreamingFromServer",
  "/grpc.testing.BenchmarkService/StreamingBothWays",
};

std::unique_ptr< BenchmarkService::Stub> BenchmarkService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< BenchmarkService::Stub> stub(new BenchmarkService::Stub(channel));
  return stub;
}

BenchmarkService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel)
  : channel_(channel), rpcmethod_UnaryCall_(BenchmarkService_method_names[0], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StreamingCall_(BenchmarkService_method_names[1], ::grpc::internal::RpcMethod::BIDI_STREAMING, channel)
  , rpcmethod_StreamingFromClient_(BenchmarkService_method_names[2], ::grpc::internal::RpcMethod::CLIENT_STREAMING, channel)
  , rpcmethod_StreamingFromServer_(BenchmarkService_method_names[3], ::grpc::internal::RpcMethod::SERVER_STREAMING, channel)
  , rpcmethod_StreamingBothWays_(BenchmarkService_method_names[4], ::grpc::internal::RpcMethod::BIDI_STREAMING, channel)
  {}

::grpc::Status BenchmarkService::Stub::UnaryCall(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::testing::SimpleResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_UnaryCall_, context, request, response);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::SimpleResponse>* BenchmarkService::Stub::AsyncUnaryCallRaw(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::SimpleResponse>::Create(channel_.get(), cq, rpcmethod_UnaryCall_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::SimpleResponse>* BenchmarkService::Stub::PrepareAsyncUnaryCallRaw(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::SimpleResponse>::Create(channel_.get(), cq, rpcmethod_UnaryCall_, context, request, false);
}

::grpc::ClientReaderWriter< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>* BenchmarkService::Stub::StreamingCallRaw(::grpc::ClientContext* context) {
  return ::grpc::internal::ClientReaderWriterFactory< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>::Create(channel_.get(), rpcmethod_StreamingCall_, context);
}

::grpc::ClientAsyncReaderWriter< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>* BenchmarkService::Stub::AsyncStreamingCallRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncReaderWriterFactory< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>::Create(channel_.get(), cq, rpcmethod_StreamingCall_, context, true, tag);
}

::grpc::ClientAsyncReaderWriter< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>* BenchmarkService::Stub::PrepareAsyncStreamingCallRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncReaderWriterFactory< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>::Create(channel_.get(), cq, rpcmethod_StreamingCall_, context, false, nullptr);
}

::grpc::ClientWriter< ::grpc::testing::SimpleRequest>* BenchmarkService::Stub::StreamingFromClientRaw(::grpc::ClientContext* context, ::grpc::testing::SimpleResponse* response) {
  return ::grpc::internal::ClientWriterFactory< ::grpc::testing::SimpleRequest>::Create(channel_.get(), rpcmethod_StreamingFromClient_, context, response);
}

::grpc::ClientAsyncWriter< ::grpc::testing::SimpleRequest>* BenchmarkService::Stub::AsyncStreamingFromClientRaw(::grpc::ClientContext* context, ::grpc::testing::SimpleResponse* response, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncWriterFactory< ::grpc::testing::SimpleRequest>::Create(channel_.get(), cq, rpcmethod_StreamingFromClient_, context, response, true, tag);
}

::grpc::ClientAsyncWriter< ::grpc::testing::SimpleRequest>* BenchmarkService::Stub::PrepareAsyncStreamingFromClientRaw(::grpc::ClientContext* context, ::grpc::testing::SimpleResponse* response, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncWriterFactory< ::grpc::testing::SimpleRequest>::Create(channel_.get(), cq, rpcmethod_StreamingFromClient_, context, response, false, nullptr);
}

::grpc::ClientReader< ::grpc::testing::SimpleResponse>* BenchmarkService::Stub::StreamingFromServerRaw(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request) {
  return ::grpc::internal::ClientReaderFactory< ::grpc::testing::SimpleResponse>::Create(channel_.get(), rpcmethod_StreamingFromServer_, context, request);
}

::grpc::ClientAsyncReader< ::grpc::testing::SimpleResponse>* BenchmarkService::Stub::AsyncStreamingFromServerRaw(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncReaderFactory< ::grpc::testing::SimpleResponse>::Create(channel_.get(), cq, rpcmethod_StreamingFromServer_, context, request, true, tag);
}

::grpc::ClientAsyncReader< ::grpc::testing::SimpleResponse>* BenchmarkService::Stub::PrepareAsyncStreamingFromServerRaw(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncReaderFactory< ::grpc::testing::SimpleResponse>::Create(channel_.get(), cq, rpcmethod_StreamingFromServer_, context, request, false, nullptr);
}

::grpc::ClientReaderWriter< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>* BenchmarkService::Stub::StreamingBothWaysRaw(::grpc::ClientContext* context) {
  return ::grpc::internal::ClientReaderWriterFactory< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>::Create(channel_.get(), rpcmethod_StreamingBothWays_, context);
}

::grpc::ClientAsyncReaderWriter< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>* BenchmarkService::Stub::AsyncStreamingBothWaysRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncReaderWriterFactory< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>::Create(channel_.get(), cq, rpcmethod_StreamingBothWays_, context, true, tag);
}

::grpc::ClientAsyncReaderWriter< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>* BenchmarkService::Stub::PrepareAsyncStreamingBothWaysRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncReaderWriterFactory< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>::Create(channel_.get(), cq, rpcmethod_StreamingBothWays_, context, false, nullptr);
}

BenchmarkService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      BenchmarkService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< BenchmarkService::Service, ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>(
          std::mem_fn(&BenchmarkService::Service::UnaryCall), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      BenchmarkService_method_names[1],
      ::grpc::internal::RpcMethod::BIDI_STREAMING,
      new ::grpc::internal::BidiStreamingHandler< BenchmarkService::Service, ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>(
          std::mem_fn(&BenchmarkService::Service::StreamingCall), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      BenchmarkService_method_names[2],
      ::grpc::internal::RpcMethod::CLIENT_STREAMING,
      new ::grpc::internal::ClientStreamingHandler< BenchmarkService::Service, ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>(
          std::mem_fn(&BenchmarkService::Service::StreamingFromClient), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      BenchmarkService_method_names[3],
      ::grpc::internal::RpcMethod::SERVER_STREAMING,
      new ::grpc::internal::ServerStreamingHandler< BenchmarkService::Service, ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>(
          std::mem_fn(&BenchmarkService::Service::StreamingFromServer), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      BenchmarkService_method_names[4],
      ::grpc::internal::RpcMethod::BIDI_STREAMING,
      new ::grpc::internal::BidiStreamingHandler< BenchmarkService::Service, ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>(
          std::mem_fn(&BenchmarkService::Service::StreamingBothWays), this)));
}

BenchmarkService::Service::~Service() {
}

::grpc::Status BenchmarkService::Service::UnaryCall(::grpc::ServerContext* context, const ::grpc::testing::SimpleRequest* request, ::grpc::testing::SimpleResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status BenchmarkService::Service::StreamingCall(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::SimpleResponse, ::grpc::testing::SimpleRequest>* stream) {
  (void) context;
  (void) stream;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status BenchmarkService::Service::StreamingFromClient(::grpc::ServerContext* context, ::grpc::ServerReader< ::grpc::testing::SimpleRequest>* reader, ::grpc::testing::SimpleResponse* response) {
  (void) context;
  (void) reader;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status BenchmarkService::Service::StreamingFromServer(::grpc::ServerContext* context, const ::grpc::testing::SimpleRequest* request, ::grpc::ServerWriter< ::grpc::testing::SimpleResponse>* writer) {
  (void) context;
  (void) request;
  (void) writer;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status BenchmarkService::Service::StreamingBothWays(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::SimpleResponse, ::grpc::testing::SimpleRequest>* stream) {
  (void) context;
  (void) stream;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace grpc
}  // namespace testing

