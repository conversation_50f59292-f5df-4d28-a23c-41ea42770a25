// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: src/proto/grpc/testing/benchmark_service.proto
// Original file comments:
// Copyright 2015 gRPC authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// An integration test service that covers all the method signature permutations
// of unary/streaming requests/responses.
#ifndef GRPC_src_2fproto_2fgrpc_2ftesting_2fbenchmark_5fservice_2eproto__INCLUDED
#define GRPC_src_2fproto_2fgrpc_2ftesting_2fbenchmark_5fservice_2eproto__INCLUDED

#include "src/proto/grpc/testing/benchmark_service.pb.h"

#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/method_handler_impl.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace grpc {
class CompletionQueue;
class Channel;
class ServerCompletionQueue;
class ServerContext;
}  // namespace grpc

namespace grpc {
namespace testing {

class BenchmarkService final {
 public:
  static constexpr char const* service_full_name() {
    return "grpc.testing.BenchmarkService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    // One request followed by one response.
    // The server returns the client payload as-is.
    virtual ::grpc::Status UnaryCall(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::testing::SimpleResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::SimpleResponse>> AsyncUnaryCall(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::SimpleResponse>>(AsyncUnaryCallRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::SimpleResponse>> PrepareAsyncUnaryCall(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::SimpleResponse>>(PrepareAsyncUnaryCallRaw(context, request, cq));
    }
    // Repeated sequence of one request followed by one response.
    // Should be called streaming ping-pong
    // The server returns the client payload as-is on each response
    std::unique_ptr< ::grpc::ClientReaderWriterInterface< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>> StreamingCall(::grpc::ClientContext* context) {
      return std::unique_ptr< ::grpc::ClientReaderWriterInterface< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>>(StreamingCallRaw(context));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>> AsyncStreamingCall(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>>(AsyncStreamingCallRaw(context, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>> PrepareAsyncStreamingCall(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>>(PrepareAsyncStreamingCallRaw(context, cq));
    }
    // Single-sided unbounded streaming from client to server
    // The server returns the client payload as-is once the client does WritesDone
    std::unique_ptr< ::grpc::ClientWriterInterface< ::grpc::testing::SimpleRequest>> StreamingFromClient(::grpc::ClientContext* context, ::grpc::testing::SimpleResponse* response) {
      return std::unique_ptr< ::grpc::ClientWriterInterface< ::grpc::testing::SimpleRequest>>(StreamingFromClientRaw(context, response));
    }
    std::unique_ptr< ::grpc::ClientAsyncWriterInterface< ::grpc::testing::SimpleRequest>> AsyncStreamingFromClient(::grpc::ClientContext* context, ::grpc::testing::SimpleResponse* response, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncWriterInterface< ::grpc::testing::SimpleRequest>>(AsyncStreamingFromClientRaw(context, response, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncWriterInterface< ::grpc::testing::SimpleRequest>> PrepareAsyncStreamingFromClient(::grpc::ClientContext* context, ::grpc::testing::SimpleResponse* response, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncWriterInterface< ::grpc::testing::SimpleRequest>>(PrepareAsyncStreamingFromClientRaw(context, response, cq));
    }
    // Single-sided unbounded streaming from server to client
    // The server repeatedly returns the client payload as-is
    std::unique_ptr< ::grpc::ClientReaderInterface< ::grpc::testing::SimpleResponse>> StreamingFromServer(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request) {
      return std::unique_ptr< ::grpc::ClientReaderInterface< ::grpc::testing::SimpleResponse>>(StreamingFromServerRaw(context, request));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::grpc::testing::SimpleResponse>> AsyncStreamingFromServer(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::grpc::testing::SimpleResponse>>(AsyncStreamingFromServerRaw(context, request, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::grpc::testing::SimpleResponse>> PrepareAsyncStreamingFromServer(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::grpc::testing::SimpleResponse>>(PrepareAsyncStreamingFromServerRaw(context, request, cq));
    }
    // Two-sided unbounded streaming between server to client
    // Both sides send the content of their own choice to the other
    std::unique_ptr< ::grpc::ClientReaderWriterInterface< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>> StreamingBothWays(::grpc::ClientContext* context) {
      return std::unique_ptr< ::grpc::ClientReaderWriterInterface< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>>(StreamingBothWaysRaw(context));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>> AsyncStreamingBothWays(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>>(AsyncStreamingBothWaysRaw(context, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>> PrepareAsyncStreamingBothWays(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>>(PrepareAsyncStreamingBothWaysRaw(context, cq));
    }
  private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::SimpleResponse>* AsyncUnaryCallRaw(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::SimpleResponse>* PrepareAsyncUnaryCallRaw(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientReaderWriterInterface< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>* StreamingCallRaw(::grpc::ClientContext* context) = 0;
    virtual ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>* AsyncStreamingCallRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) = 0;
    virtual ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>* PrepareAsyncStreamingCallRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientWriterInterface< ::grpc::testing::SimpleRequest>* StreamingFromClientRaw(::grpc::ClientContext* context, ::grpc::testing::SimpleResponse* response) = 0;
    virtual ::grpc::ClientAsyncWriterInterface< ::grpc::testing::SimpleRequest>* AsyncStreamingFromClientRaw(::grpc::ClientContext* context, ::grpc::testing::SimpleResponse* response, ::grpc::CompletionQueue* cq, void* tag) = 0;
    virtual ::grpc::ClientAsyncWriterInterface< ::grpc::testing::SimpleRequest>* PrepareAsyncStreamingFromClientRaw(::grpc::ClientContext* context, ::grpc::testing::SimpleResponse* response, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientReaderInterface< ::grpc::testing::SimpleResponse>* StreamingFromServerRaw(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request) = 0;
    virtual ::grpc::ClientAsyncReaderInterface< ::grpc::testing::SimpleResponse>* AsyncStreamingFromServerRaw(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq, void* tag) = 0;
    virtual ::grpc::ClientAsyncReaderInterface< ::grpc::testing::SimpleResponse>* PrepareAsyncStreamingFromServerRaw(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientReaderWriterInterface< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>* StreamingBothWaysRaw(::grpc::ClientContext* context) = 0;
    virtual ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>* AsyncStreamingBothWaysRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) = 0;
    virtual ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>* PrepareAsyncStreamingBothWaysRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel);
    ::grpc::Status UnaryCall(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::testing::SimpleResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::SimpleResponse>> AsyncUnaryCall(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::SimpleResponse>>(AsyncUnaryCallRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::SimpleResponse>> PrepareAsyncUnaryCall(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::SimpleResponse>>(PrepareAsyncUnaryCallRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientReaderWriter< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>> StreamingCall(::grpc::ClientContext* context) {
      return std::unique_ptr< ::grpc::ClientReaderWriter< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>>(StreamingCallRaw(context));
    }
    std::unique_ptr<  ::grpc::ClientAsyncReaderWriter< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>> AsyncStreamingCall(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriter< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>>(AsyncStreamingCallRaw(context, cq, tag));
    }
    std::unique_ptr<  ::grpc::ClientAsyncReaderWriter< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>> PrepareAsyncStreamingCall(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriter< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>>(PrepareAsyncStreamingCallRaw(context, cq));
    }
    std::unique_ptr< ::grpc::ClientWriter< ::grpc::testing::SimpleRequest>> StreamingFromClient(::grpc::ClientContext* context, ::grpc::testing::SimpleResponse* response) {
      return std::unique_ptr< ::grpc::ClientWriter< ::grpc::testing::SimpleRequest>>(StreamingFromClientRaw(context, response));
    }
    std::unique_ptr< ::grpc::ClientAsyncWriter< ::grpc::testing::SimpleRequest>> AsyncStreamingFromClient(::grpc::ClientContext* context, ::grpc::testing::SimpleResponse* response, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncWriter< ::grpc::testing::SimpleRequest>>(AsyncStreamingFromClientRaw(context, response, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncWriter< ::grpc::testing::SimpleRequest>> PrepareAsyncStreamingFromClient(::grpc::ClientContext* context, ::grpc::testing::SimpleResponse* response, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncWriter< ::grpc::testing::SimpleRequest>>(PrepareAsyncStreamingFromClientRaw(context, response, cq));
    }
    std::unique_ptr< ::grpc::ClientReader< ::grpc::testing::SimpleResponse>> StreamingFromServer(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request) {
      return std::unique_ptr< ::grpc::ClientReader< ::grpc::testing::SimpleResponse>>(StreamingFromServerRaw(context, request));
    }
    std::unique_ptr< ::grpc::ClientAsyncReader< ::grpc::testing::SimpleResponse>> AsyncStreamingFromServer(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReader< ::grpc::testing::SimpleResponse>>(AsyncStreamingFromServerRaw(context, request, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReader< ::grpc::testing::SimpleResponse>> PrepareAsyncStreamingFromServer(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReader< ::grpc::testing::SimpleResponse>>(PrepareAsyncStreamingFromServerRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientReaderWriter< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>> StreamingBothWays(::grpc::ClientContext* context) {
      return std::unique_ptr< ::grpc::ClientReaderWriter< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>>(StreamingBothWaysRaw(context));
    }
    std::unique_ptr<  ::grpc::ClientAsyncReaderWriter< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>> AsyncStreamingBothWays(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriter< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>>(AsyncStreamingBothWaysRaw(context, cq, tag));
    }
    std::unique_ptr<  ::grpc::ClientAsyncReaderWriter< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>> PrepareAsyncStreamingBothWays(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriter< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>>(PrepareAsyncStreamingBothWaysRaw(context, cq));
    }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    ::grpc::ClientAsyncResponseReader< ::grpc::testing::SimpleResponse>* AsyncUnaryCallRaw(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::testing::SimpleResponse>* PrepareAsyncUnaryCallRaw(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientReaderWriter< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>* StreamingCallRaw(::grpc::ClientContext* context) override;
    ::grpc::ClientAsyncReaderWriter< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>* AsyncStreamingCallRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) override;
    ::grpc::ClientAsyncReaderWriter< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>* PrepareAsyncStreamingCallRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientWriter< ::grpc::testing::SimpleRequest>* StreamingFromClientRaw(::grpc::ClientContext* context, ::grpc::testing::SimpleResponse* response) override;
    ::grpc::ClientAsyncWriter< ::grpc::testing::SimpleRequest>* AsyncStreamingFromClientRaw(::grpc::ClientContext* context, ::grpc::testing::SimpleResponse* response, ::grpc::CompletionQueue* cq, void* tag) override;
    ::grpc::ClientAsyncWriter< ::grpc::testing::SimpleRequest>* PrepareAsyncStreamingFromClientRaw(::grpc::ClientContext* context, ::grpc::testing::SimpleResponse* response, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientReader< ::grpc::testing::SimpleResponse>* StreamingFromServerRaw(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request) override;
    ::grpc::ClientAsyncReader< ::grpc::testing::SimpleResponse>* AsyncStreamingFromServerRaw(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq, void* tag) override;
    ::grpc::ClientAsyncReader< ::grpc::testing::SimpleResponse>* PrepareAsyncStreamingFromServerRaw(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientReaderWriter< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>* StreamingBothWaysRaw(::grpc::ClientContext* context) override;
    ::grpc::ClientAsyncReaderWriter< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>* AsyncStreamingBothWaysRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) override;
    ::grpc::ClientAsyncReaderWriter< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>* PrepareAsyncStreamingBothWaysRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_UnaryCall_;
    const ::grpc::internal::RpcMethod rpcmethod_StreamingCall_;
    const ::grpc::internal::RpcMethod rpcmethod_StreamingFromClient_;
    const ::grpc::internal::RpcMethod rpcmethod_StreamingFromServer_;
    const ::grpc::internal::RpcMethod rpcmethod_StreamingBothWays_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    // One request followed by one response.
    // The server returns the client payload as-is.
    virtual ::grpc::Status UnaryCall(::grpc::ServerContext* context, const ::grpc::testing::SimpleRequest* request, ::grpc::testing::SimpleResponse* response);
    // Repeated sequence of one request followed by one response.
    // Should be called streaming ping-pong
    // The server returns the client payload as-is on each response
    virtual ::grpc::Status StreamingCall(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::SimpleResponse, ::grpc::testing::SimpleRequest>* stream);
    // Single-sided unbounded streaming from client to server
    // The server returns the client payload as-is once the client does WritesDone
    virtual ::grpc::Status StreamingFromClient(::grpc::ServerContext* context, ::grpc::ServerReader< ::grpc::testing::SimpleRequest>* reader, ::grpc::testing::SimpleResponse* response);
    // Single-sided unbounded streaming from server to client
    // The server repeatedly returns the client payload as-is
    virtual ::grpc::Status StreamingFromServer(::grpc::ServerContext* context, const ::grpc::testing::SimpleRequest* request, ::grpc::ServerWriter< ::grpc::testing::SimpleResponse>* writer);
    // Two-sided unbounded streaming between server to client
    // Both sides send the content of their own choice to the other
    virtual ::grpc::Status StreamingBothWays(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::SimpleResponse, ::grpc::testing::SimpleRequest>* stream);
  };
  template <class BaseClass>
  class WithAsyncMethod_UnaryCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_UnaryCall() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_UnaryCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UnaryCall(::grpc::ServerContext* context, const ::grpc::testing::SimpleRequest* request, ::grpc::testing::SimpleResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUnaryCall(::grpc::ServerContext* context, ::grpc::testing::SimpleRequest* request, ::grpc::ServerAsyncResponseWriter< ::grpc::testing::SimpleResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_StreamingCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_StreamingCall() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_StreamingCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StreamingCall(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::SimpleResponse, ::grpc::testing::SimpleRequest>* stream)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStreamingCall(::grpc::ServerContext* context, ::grpc::ServerAsyncReaderWriter< ::grpc::testing::SimpleResponse, ::grpc::testing::SimpleRequest>* stream, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncBidiStreaming(1, context, stream, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_StreamingFromClient : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_StreamingFromClient() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_StreamingFromClient() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StreamingFromClient(::grpc::ServerContext* context, ::grpc::ServerReader< ::grpc::testing::SimpleRequest>* reader, ::grpc::testing::SimpleResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStreamingFromClient(::grpc::ServerContext* context, ::grpc::ServerAsyncReader< ::grpc::testing::SimpleResponse, ::grpc::testing::SimpleRequest>* reader, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncClientStreaming(2, context, reader, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_StreamingFromServer : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_StreamingFromServer() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_StreamingFromServer() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StreamingFromServer(::grpc::ServerContext* context, const ::grpc::testing::SimpleRequest* request, ::grpc::ServerWriter< ::grpc::testing::SimpleResponse>* writer) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStreamingFromServer(::grpc::ServerContext* context, ::grpc::testing::SimpleRequest* request, ::grpc::ServerAsyncWriter< ::grpc::testing::SimpleResponse>* writer, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncServerStreaming(3, context, request, writer, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_StreamingBothWays : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_StreamingBothWays() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_StreamingBothWays() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StreamingBothWays(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::SimpleResponse, ::grpc::testing::SimpleRequest>* stream)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStreamingBothWays(::grpc::ServerContext* context, ::grpc::ServerAsyncReaderWriter< ::grpc::testing::SimpleResponse, ::grpc::testing::SimpleRequest>* stream, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncBidiStreaming(4, context, stream, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_UnaryCall<WithAsyncMethod_StreamingCall<WithAsyncMethod_StreamingFromClient<WithAsyncMethod_StreamingFromServer<WithAsyncMethod_StreamingBothWays<Service > > > > > AsyncService;
  template <class BaseClass>
  class WithGenericMethod_UnaryCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_UnaryCall() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_UnaryCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UnaryCall(::grpc::ServerContext* context, const ::grpc::testing::SimpleRequest* request, ::grpc::testing::SimpleResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_StreamingCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_StreamingCall() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_StreamingCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StreamingCall(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::SimpleResponse, ::grpc::testing::SimpleRequest>* stream)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_StreamingFromClient : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_StreamingFromClient() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_StreamingFromClient() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StreamingFromClient(::grpc::ServerContext* context, ::grpc::ServerReader< ::grpc::testing::SimpleRequest>* reader, ::grpc::testing::SimpleResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_StreamingFromServer : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_StreamingFromServer() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_StreamingFromServer() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StreamingFromServer(::grpc::ServerContext* context, const ::grpc::testing::SimpleRequest* request, ::grpc::ServerWriter< ::grpc::testing::SimpleResponse>* writer) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_StreamingBothWays : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_StreamingBothWays() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_StreamingBothWays() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StreamingBothWays(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::SimpleResponse, ::grpc::testing::SimpleRequest>* stream)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_UnaryCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_UnaryCall() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_UnaryCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UnaryCall(::grpc::ServerContext* context, const ::grpc::testing::SimpleRequest* request, ::grpc::testing::SimpleResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUnaryCall(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_StreamingCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_StreamingCall() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_StreamingCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StreamingCall(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::SimpleResponse, ::grpc::testing::SimpleRequest>* stream)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStreamingCall(::grpc::ServerContext* context, ::grpc::ServerAsyncReaderWriter< ::grpc::ByteBuffer, ::grpc::ByteBuffer>* stream, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncBidiStreaming(1, context, stream, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_StreamingFromClient : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_StreamingFromClient() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_StreamingFromClient() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StreamingFromClient(::grpc::ServerContext* context, ::grpc::ServerReader< ::grpc::testing::SimpleRequest>* reader, ::grpc::testing::SimpleResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStreamingFromClient(::grpc::ServerContext* context, ::grpc::ServerAsyncReader< ::grpc::ByteBuffer, ::grpc::ByteBuffer>* reader, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncClientStreaming(2, context, reader, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_StreamingFromServer : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_StreamingFromServer() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_StreamingFromServer() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StreamingFromServer(::grpc::ServerContext* context, const ::grpc::testing::SimpleRequest* request, ::grpc::ServerWriter< ::grpc::testing::SimpleResponse>* writer) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStreamingFromServer(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncWriter< ::grpc::ByteBuffer>* writer, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncServerStreaming(3, context, request, writer, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_StreamingBothWays : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_StreamingBothWays() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_StreamingBothWays() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StreamingBothWays(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::SimpleResponse, ::grpc::testing::SimpleRequest>* stream)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStreamingBothWays(::grpc::ServerContext* context, ::grpc::ServerAsyncReaderWriter< ::grpc::ByteBuffer, ::grpc::ByteBuffer>* stream, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncBidiStreaming(4, context, stream, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_UnaryCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithStreamedUnaryMethod_UnaryCall() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>(std::bind(&WithStreamedUnaryMethod_UnaryCall<BaseClass>::StreamedUnaryCall, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_UnaryCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status UnaryCall(::grpc::ServerContext* context, const ::grpc::testing::SimpleRequest* request, ::grpc::testing::SimpleResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedUnaryCall(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::grpc::testing::SimpleRequest,::grpc::testing::SimpleResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_UnaryCall<Service > StreamedUnaryService;
  template <class BaseClass>
  class WithSplitStreamingMethod_StreamingFromServer : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithSplitStreamingMethod_StreamingFromServer() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::SplitServerStreamingHandler< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>(std::bind(&WithSplitStreamingMethod_StreamingFromServer<BaseClass>::StreamedStreamingFromServer, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithSplitStreamingMethod_StreamingFromServer() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status StreamingFromServer(::grpc::ServerContext* context, const ::grpc::testing::SimpleRequest* request, ::grpc::ServerWriter< ::grpc::testing::SimpleResponse>* writer) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with split streamed
    virtual ::grpc::Status StreamedStreamingFromServer(::grpc::ServerContext* context, ::grpc::ServerSplitStreamer< ::grpc::testing::SimpleRequest,::grpc::testing::SimpleResponse>* server_split_streamer) = 0;
  };
  typedef WithSplitStreamingMethod_StreamingFromServer<Service > SplitStreamedService;
  typedef WithStreamedUnaryMethod_UnaryCall<WithSplitStreamingMethod_StreamingFromServer<Service > > StreamedService;
};

}  // namespace testing
}  // namespace grpc


#endif  // GRPC_src_2fproto_2fgrpc_2ftesting_2fbenchmark_5fservice_2eproto__INCLUDED
