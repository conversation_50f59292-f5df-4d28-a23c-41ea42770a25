// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: src/proto/grpc/testing/benchmark_service.proto

#include "src/proto/grpc/testing/benchmark_service.pb.h"
#include "src/proto/grpc/testing/benchmark_service.grpc.pb.h"

#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/sync_stream.h>
#include <gmock/gmock.h>
namespace grpc {
namespace testing {

class MockBenchmarkServiceStub : public BenchmarkService::StubInterface {
 public:
  MOCK_METHOD3(UnaryCall, ::grpc::Status(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::testing::SimpleResponse* response));
  MOCK_METHOD3(AsyncUnaryCallRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::SimpleResponse>*(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncUnaryCallRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::SimpleResponse>*(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD1(StreamingCallRaw, ::grpc::ClientReaderWriterInterface< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>*(::grpc::ClientContext* context));
  MOCK_METHOD3(AsyncStreamingCallRaw, ::grpc::ClientAsyncReaderWriterInterface<::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>*(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag));
  MOCK_METHOD2(PrepareAsyncStreamingCallRaw, ::grpc::ClientAsyncReaderWriterInterface<::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>*(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq));
  MOCK_METHOD2(StreamingFromClientRaw, ::grpc::ClientWriterInterface< ::grpc::testing::SimpleRequest>*(::grpc::ClientContext* context, ::grpc::testing::SimpleResponse* response));
  MOCK_METHOD4(AsyncStreamingFromClientRaw, ::grpc::ClientAsyncWriterInterface< ::grpc::testing::SimpleRequest>*(::grpc::ClientContext* context, ::grpc::testing::SimpleResponse* response, ::grpc::CompletionQueue* cq, void* tag));
  MOCK_METHOD3(PrepareAsyncStreamingFromClientRaw, ::grpc::ClientAsyncWriterInterface< ::grpc::testing::SimpleRequest>*(::grpc::ClientContext* context, ::grpc::testing::SimpleResponse* response, ::grpc::CompletionQueue* cq));
  MOCK_METHOD2(StreamingFromServerRaw, ::grpc::ClientReaderInterface< ::grpc::testing::SimpleResponse>*(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request));
  MOCK_METHOD4(AsyncStreamingFromServerRaw, ::grpc::ClientAsyncReaderInterface< ::grpc::testing::SimpleResponse>*(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq, void* tag));
  MOCK_METHOD3(PrepareAsyncStreamingFromServerRaw, ::grpc::ClientAsyncReaderInterface< ::grpc::testing::SimpleResponse>*(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD1(StreamingBothWaysRaw, ::grpc::ClientReaderWriterInterface< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>*(::grpc::ClientContext* context));
  MOCK_METHOD3(AsyncStreamingBothWaysRaw, ::grpc::ClientAsyncReaderWriterInterface<::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>*(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag));
  MOCK_METHOD2(PrepareAsyncStreamingBothWaysRaw, ::grpc::ClientAsyncReaderWriterInterface<::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>*(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq));
};

} // namespace grpc
} // namespace testing

