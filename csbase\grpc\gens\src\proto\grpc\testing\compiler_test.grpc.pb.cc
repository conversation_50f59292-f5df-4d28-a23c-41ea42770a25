// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: src/proto/grpc/testing/compiler_test.proto

#include "src/proto/grpc/testing/compiler_test.pb.h"
#include "src/proto/grpc/testing/compiler_test.grpc.pb.h"

#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/method_handler_impl.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace grpc {
namespace testing {

static const char* ServiceA_method_names[] = {
  "/grpc.testing.ServiceA/MethodA1",
  "/grpc.testing.ServiceA/MethodA2",
  "/grpc.testing.ServiceA/MethodA3",
  "/grpc.testing.ServiceA/MethodA4",
};

std::unique_ptr< ServiceA::Stub> ServiceA::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< ServiceA::Stub> stub(new ServiceA::Stub(channel));
  return stub;
}

ServiceA::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel)
  : channel_(channel), rpcmethod_MethodA1_(ServiceA_method_names[0], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_MethodA2_(ServiceA_method_names[1], ::grpc::internal::RpcMethod::CLIENT_STREAMING, channel)
  , rpcmethod_MethodA3_(ServiceA_method_names[2], ::grpc::internal::RpcMethod::SERVER_STREAMING, channel)
  , rpcmethod_MethodA4_(ServiceA_method_names[3], ::grpc::internal::RpcMethod::BIDI_STREAMING, channel)
  {}

::grpc::Status ServiceA::Stub::MethodA1(::grpc::ClientContext* context, const ::grpc::testing::Request& request, ::grpc::testing::Response* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_MethodA1_, context, request, response);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::Response>* ServiceA::Stub::AsyncMethodA1Raw(::grpc::ClientContext* context, const ::grpc::testing::Request& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::Response>::Create(channel_.get(), cq, rpcmethod_MethodA1_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::Response>* ServiceA::Stub::PrepareAsyncMethodA1Raw(::grpc::ClientContext* context, const ::grpc::testing::Request& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::Response>::Create(channel_.get(), cq, rpcmethod_MethodA1_, context, request, false);
}

::grpc::ClientWriter< ::grpc::testing::Request>* ServiceA::Stub::MethodA2Raw(::grpc::ClientContext* context, ::grpc::testing::Response* response) {
  return ::grpc::internal::ClientWriterFactory< ::grpc::testing::Request>::Create(channel_.get(), rpcmethod_MethodA2_, context, response);
}

::grpc::ClientAsyncWriter< ::grpc::testing::Request>* ServiceA::Stub::AsyncMethodA2Raw(::grpc::ClientContext* context, ::grpc::testing::Response* response, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncWriterFactory< ::grpc::testing::Request>::Create(channel_.get(), cq, rpcmethod_MethodA2_, context, response, true, tag);
}

::grpc::ClientAsyncWriter< ::grpc::testing::Request>* ServiceA::Stub::PrepareAsyncMethodA2Raw(::grpc::ClientContext* context, ::grpc::testing::Response* response, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncWriterFactory< ::grpc::testing::Request>::Create(channel_.get(), cq, rpcmethod_MethodA2_, context, response, false, nullptr);
}

::grpc::ClientReader< ::grpc::testing::Response>* ServiceA::Stub::MethodA3Raw(::grpc::ClientContext* context, const ::grpc::testing::Request& request) {
  return ::grpc::internal::ClientReaderFactory< ::grpc::testing::Response>::Create(channel_.get(), rpcmethod_MethodA3_, context, request);
}

::grpc::ClientAsyncReader< ::grpc::testing::Response>* ServiceA::Stub::AsyncMethodA3Raw(::grpc::ClientContext* context, const ::grpc::testing::Request& request, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncReaderFactory< ::grpc::testing::Response>::Create(channel_.get(), cq, rpcmethod_MethodA3_, context, request, true, tag);
}

::grpc::ClientAsyncReader< ::grpc::testing::Response>* ServiceA::Stub::PrepareAsyncMethodA3Raw(::grpc::ClientContext* context, const ::grpc::testing::Request& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncReaderFactory< ::grpc::testing::Response>::Create(channel_.get(), cq, rpcmethod_MethodA3_, context, request, false, nullptr);
}

::grpc::ClientReaderWriter< ::grpc::testing::Request, ::grpc::testing::Response>* ServiceA::Stub::MethodA4Raw(::grpc::ClientContext* context) {
  return ::grpc::internal::ClientReaderWriterFactory< ::grpc::testing::Request, ::grpc::testing::Response>::Create(channel_.get(), rpcmethod_MethodA4_, context);
}

::grpc::ClientAsyncReaderWriter< ::grpc::testing::Request, ::grpc::testing::Response>* ServiceA::Stub::AsyncMethodA4Raw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncReaderWriterFactory< ::grpc::testing::Request, ::grpc::testing::Response>::Create(channel_.get(), cq, rpcmethod_MethodA4_, context, true, tag);
}

::grpc::ClientAsyncReaderWriter< ::grpc::testing::Request, ::grpc::testing::Response>* ServiceA::Stub::PrepareAsyncMethodA4Raw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncReaderWriterFactory< ::grpc::testing::Request, ::grpc::testing::Response>::Create(channel_.get(), cq, rpcmethod_MethodA4_, context, false, nullptr);
}

ServiceA::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ServiceA_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ServiceA::Service, ::grpc::testing::Request, ::grpc::testing::Response>(
          std::mem_fn(&ServiceA::Service::MethodA1), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ServiceA_method_names[1],
      ::grpc::internal::RpcMethod::CLIENT_STREAMING,
      new ::grpc::internal::ClientStreamingHandler< ServiceA::Service, ::grpc::testing::Request, ::grpc::testing::Response>(
          std::mem_fn(&ServiceA::Service::MethodA2), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ServiceA_method_names[2],
      ::grpc::internal::RpcMethod::SERVER_STREAMING,
      new ::grpc::internal::ServerStreamingHandler< ServiceA::Service, ::grpc::testing::Request, ::grpc::testing::Response>(
          std::mem_fn(&ServiceA::Service::MethodA3), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ServiceA_method_names[3],
      ::grpc::internal::RpcMethod::BIDI_STREAMING,
      new ::grpc::internal::BidiStreamingHandler< ServiceA::Service, ::grpc::testing::Request, ::grpc::testing::Response>(
          std::mem_fn(&ServiceA::Service::MethodA4), this)));
}

ServiceA::Service::~Service() {
}

::grpc::Status ServiceA::Service::MethodA1(::grpc::ServerContext* context, const ::grpc::testing::Request* request, ::grpc::testing::Response* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ServiceA::Service::MethodA2(::grpc::ServerContext* context, ::grpc::ServerReader< ::grpc::testing::Request>* reader, ::grpc::testing::Response* response) {
  (void) context;
  (void) reader;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ServiceA::Service::MethodA3(::grpc::ServerContext* context, const ::grpc::testing::Request* request, ::grpc::ServerWriter< ::grpc::testing::Response>* writer) {
  (void) context;
  (void) request;
  (void) writer;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ServiceA::Service::MethodA4(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::Response, ::grpc::testing::Request>* stream) {
  (void) context;
  (void) stream;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


static const char* ServiceB_method_names[] = {
  "/grpc.testing.ServiceB/MethodB1",
};

std::unique_ptr< ServiceB::Stub> ServiceB::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< ServiceB::Stub> stub(new ServiceB::Stub(channel));
  return stub;
}

ServiceB::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel)
  : channel_(channel), rpcmethod_MethodB1_(ServiceB_method_names[0], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status ServiceB::Stub::MethodB1(::grpc::ClientContext* context, const ::grpc::testing::Request& request, ::grpc::testing::Response* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_MethodB1_, context, request, response);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::Response>* ServiceB::Stub::AsyncMethodB1Raw(::grpc::ClientContext* context, const ::grpc::testing::Request& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::Response>::Create(channel_.get(), cq, rpcmethod_MethodB1_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::Response>* ServiceB::Stub::PrepareAsyncMethodB1Raw(::grpc::ClientContext* context, const ::grpc::testing::Request& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::Response>::Create(channel_.get(), cq, rpcmethod_MethodB1_, context, request, false);
}

ServiceB::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ServiceB_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ServiceB::Service, ::grpc::testing::Request, ::grpc::testing::Response>(
          std::mem_fn(&ServiceB::Service::MethodB1), this)));
}

ServiceB::Service::~Service() {
}

::grpc::Status ServiceB::Service::MethodB1(::grpc::ServerContext* context, const ::grpc::testing::Request* request, ::grpc::testing::Response* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace grpc
}  // namespace testing

