// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: src/proto/grpc/testing/control.proto

#include "src/proto/grpc/testing/control.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)
namespace grpc {
namespace testing {
class PoissonParamsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<PoissonParams>
      _instance;
} _PoissonParams_default_instance_;
class ClosedLoopParamsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ClosedLoopParams>
      _instance;
} _ClosedLoopParams_default_instance_;
class LoadParamsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<LoadParams>
      _instance;
  const ::grpc::testing::ClosedLoopParams* closed_loop_;
  const ::grpc::testing::PoissonParams* poisson_;
} _LoadParams_default_instance_;
class SecurityParamsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SecurityParams>
      _instance;
} _SecurityParams_default_instance_;
class ChannelArgDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ChannelArg>
      _instance;
  ::google::protobuf::internal::ArenaStringPtr str_value_;
  ::google::protobuf::int32 int_value_;
} _ChannelArg_default_instance_;
class ClientConfigDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ClientConfig>
      _instance;
} _ClientConfig_default_instance_;
class ClientStatusDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ClientStatus>
      _instance;
} _ClientStatus_default_instance_;
class MarkDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Mark>
      _instance;
} _Mark_default_instance_;
class ClientArgsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ClientArgs>
      _instance;
  const ::grpc::testing::ClientConfig* setup_;
  const ::grpc::testing::Mark* mark_;
} _ClientArgs_default_instance_;
class ServerConfigDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ServerConfig>
      _instance;
} _ServerConfig_default_instance_;
class ServerArgsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ServerArgs>
      _instance;
  const ::grpc::testing::ServerConfig* setup_;
  const ::grpc::testing::Mark* mark_;
} _ServerArgs_default_instance_;
class ServerStatusDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ServerStatus>
      _instance;
} _ServerStatus_default_instance_;
class CoreRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CoreRequest>
      _instance;
} _CoreRequest_default_instance_;
class CoreResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<CoreResponse>
      _instance;
} _CoreResponse_default_instance_;
class VoidDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Void>
      _instance;
} _Void_default_instance_;
class ScenarioDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Scenario>
      _instance;
} _Scenario_default_instance_;
class ScenariosDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Scenarios>
      _instance;
} _Scenarios_default_instance_;
class ScenarioResultSummaryDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ScenarioResultSummary>
      _instance;
} _ScenarioResultSummary_default_instance_;
class ScenarioResultDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ScenarioResult>
      _instance;
} _ScenarioResult_default_instance_;
}  // namespace testing
}  // namespace grpc
namespace protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto {
void InitDefaultsPoissonParamsImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::testing::_PoissonParams_default_instance_;
    new (ptr) ::grpc::testing::PoissonParams();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::PoissonParams::InitAsDefaultInstance();
}

void InitDefaultsPoissonParams() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsPoissonParamsImpl);
}

void InitDefaultsClosedLoopParamsImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::testing::_ClosedLoopParams_default_instance_;
    new (ptr) ::grpc::testing::ClosedLoopParams();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::ClosedLoopParams::InitAsDefaultInstance();
}

void InitDefaultsClosedLoopParams() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsClosedLoopParamsImpl);
}

void InitDefaultsLoadParamsImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsClosedLoopParams();
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsPoissonParams();
  {
    void* ptr = &::grpc::testing::_LoadParams_default_instance_;
    new (ptr) ::grpc::testing::LoadParams();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::LoadParams::InitAsDefaultInstance();
}

void InitDefaultsLoadParams() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsLoadParamsImpl);
}

void InitDefaultsSecurityParamsImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::testing::_SecurityParams_default_instance_;
    new (ptr) ::grpc::testing::SecurityParams();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::SecurityParams::InitAsDefaultInstance();
}

void InitDefaultsSecurityParams() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsSecurityParamsImpl);
}

void InitDefaultsChannelArgImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::testing::_ChannelArg_default_instance_;
    new (ptr) ::grpc::testing::ChannelArg();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::ChannelArg::InitAsDefaultInstance();
}

void InitDefaultsChannelArg() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsChannelArgImpl);
}

void InitDefaultsClientConfigImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsSecurityParams();
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsLoadParams();
  protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::InitDefaultsPayloadConfig();
  protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::InitDefaultsHistogramParams();
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsChannelArg();
  {
    void* ptr = &::grpc::testing::_ClientConfig_default_instance_;
    new (ptr) ::grpc::testing::ClientConfig();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::ClientConfig::InitAsDefaultInstance();
}

void InitDefaultsClientConfig() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsClientConfigImpl);
}

void InitDefaultsClientStatusImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::InitDefaultsClientStats();
  {
    void* ptr = &::grpc::testing::_ClientStatus_default_instance_;
    new (ptr) ::grpc::testing::ClientStatus();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::ClientStatus::InitAsDefaultInstance();
}

void InitDefaultsClientStatus() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsClientStatusImpl);
}

void InitDefaultsMarkImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::testing::_Mark_default_instance_;
    new (ptr) ::grpc::testing::Mark();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::Mark::InitAsDefaultInstance();
}

void InitDefaultsMark() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsMarkImpl);
}

void InitDefaultsClientArgsImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsClientConfig();
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsMark();
  {
    void* ptr = &::grpc::testing::_ClientArgs_default_instance_;
    new (ptr) ::grpc::testing::ClientArgs();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::ClientArgs::InitAsDefaultInstance();
}

void InitDefaultsClientArgs() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsClientArgsImpl);
}

void InitDefaultsServerConfigImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsSecurityParams();
  protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::InitDefaultsPayloadConfig();
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsChannelArg();
  {
    void* ptr = &::grpc::testing::_ServerConfig_default_instance_;
    new (ptr) ::grpc::testing::ServerConfig();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::ServerConfig::InitAsDefaultInstance();
}

void InitDefaultsServerConfig() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsServerConfigImpl);
}

void InitDefaultsServerArgsImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsServerConfig();
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsMark();
  {
    void* ptr = &::grpc::testing::_ServerArgs_default_instance_;
    new (ptr) ::grpc::testing::ServerArgs();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::ServerArgs::InitAsDefaultInstance();
}

void InitDefaultsServerArgs() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsServerArgsImpl);
}

void InitDefaultsServerStatusImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::InitDefaultsServerStats();
  {
    void* ptr = &::grpc::testing::_ServerStatus_default_instance_;
    new (ptr) ::grpc::testing::ServerStatus();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::ServerStatus::InitAsDefaultInstance();
}

void InitDefaultsServerStatus() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsServerStatusImpl);
}

void InitDefaultsCoreRequestImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::testing::_CoreRequest_default_instance_;
    new (ptr) ::grpc::testing::CoreRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::CoreRequest::InitAsDefaultInstance();
}

void InitDefaultsCoreRequest() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsCoreRequestImpl);
}

void InitDefaultsCoreResponseImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::testing::_CoreResponse_default_instance_;
    new (ptr) ::grpc::testing::CoreResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::CoreResponse::InitAsDefaultInstance();
}

void InitDefaultsCoreResponse() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsCoreResponseImpl);
}

void InitDefaultsVoidImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::testing::_Void_default_instance_;
    new (ptr) ::grpc::testing::Void();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::Void::InitAsDefaultInstance();
}

void InitDefaultsVoid() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsVoidImpl);
}

void InitDefaultsScenarioImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsClientConfig();
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsServerConfig();
  {
    void* ptr = &::grpc::testing::_Scenario_default_instance_;
    new (ptr) ::grpc::testing::Scenario();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::Scenario::InitAsDefaultInstance();
}

void InitDefaultsScenario() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsScenarioImpl);
}

void InitDefaultsScenariosImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsScenario();
  {
    void* ptr = &::grpc::testing::_Scenarios_default_instance_;
    new (ptr) ::grpc::testing::Scenarios();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::Scenarios::InitAsDefaultInstance();
}

void InitDefaultsScenarios() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsScenariosImpl);
}

void InitDefaultsScenarioResultSummaryImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::testing::_ScenarioResultSummary_default_instance_;
    new (ptr) ::grpc::testing::ScenarioResultSummary();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::ScenarioResultSummary::InitAsDefaultInstance();
}

void InitDefaultsScenarioResultSummary() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsScenarioResultSummaryImpl);
}

void InitDefaultsScenarioResultImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsScenario();
  protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::InitDefaultsHistogramData();
  protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::InitDefaultsClientStats();
  protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::InitDefaultsServerStats();
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsScenarioResultSummary();
  protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::InitDefaultsRequestResultCount();
  {
    void* ptr = &::grpc::testing::_ScenarioResult_default_instance_;
    new (ptr) ::grpc::testing::ScenarioResult();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::ScenarioResult::InitAsDefaultInstance();
}

void InitDefaultsScenarioResult() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsScenarioResultImpl);
}

::google::protobuf::Metadata file_level_metadata[19];
const ::google::protobuf::EnumDescriptor* file_level_enum_descriptors[3];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::PoissonParams, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::PoissonParams, offered_load_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClosedLoopParams, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::LoadParams, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::LoadParams, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  offsetof(::grpc::testing::LoadParamsDefaultTypeInternal, closed_loop_),
  offsetof(::grpc::testing::LoadParamsDefaultTypeInternal, poisson_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::LoadParams, load_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::SecurityParams, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::SecurityParams, use_test_ca_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::SecurityParams, server_host_override_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::SecurityParams, cred_type_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ChannelArg, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ChannelArg, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ChannelArg, name_),
  offsetof(::grpc::testing::ChannelArgDefaultTypeInternal, str_value_),
  offsetof(::grpc::testing::ChannelArgDefaultTypeInternal, int_value_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ChannelArg, value_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientConfig, server_targets_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientConfig, client_type_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientConfig, security_params_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientConfig, outstanding_rpcs_per_channel_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientConfig, client_channels_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientConfig, async_client_threads_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientConfig, rpc_type_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientConfig, load_params_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientConfig, payload_config_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientConfig, histogram_params_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientConfig, core_list_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientConfig, core_limit_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientConfig, other_client_api_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientConfig, channel_args_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientConfig, threads_per_cq_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientConfig, messages_per_stream_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientConfig, use_coalesce_api_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientStatus, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientStatus, stats_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::Mark, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::Mark, reset_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientArgs, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientArgs, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  offsetof(::grpc::testing::ClientArgsDefaultTypeInternal, setup_),
  offsetof(::grpc::testing::ClientArgsDefaultTypeInternal, mark_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientArgs, argtype_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ServerConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ServerConfig, server_type_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ServerConfig, security_params_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ServerConfig, port_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ServerConfig, async_server_threads_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ServerConfig, core_limit_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ServerConfig, payload_config_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ServerConfig, core_list_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ServerConfig, other_server_api_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ServerConfig, threads_per_cq_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ServerConfig, resource_quota_size_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ServerConfig, channel_args_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ServerArgs, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ServerArgs, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  offsetof(::grpc::testing::ServerArgsDefaultTypeInternal, setup_),
  offsetof(::grpc::testing::ServerArgsDefaultTypeInternal, mark_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ServerArgs, argtype_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ServerStatus, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ServerStatus, stats_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ServerStatus, port_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ServerStatus, cores_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::CoreRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::CoreResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::CoreResponse, cores_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::Void, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::Scenario, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::Scenario, name_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::Scenario, client_config_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::Scenario, num_clients_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::Scenario, server_config_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::Scenario, num_servers_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::Scenario, warmup_seconds_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::Scenario, benchmark_seconds_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::Scenario, spawn_local_worker_count_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::Scenarios, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::Scenarios, scenarios_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ScenarioResultSummary, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ScenarioResultSummary, qps_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ScenarioResultSummary, qps_per_server_core_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ScenarioResultSummary, server_system_time_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ScenarioResultSummary, server_user_time_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ScenarioResultSummary, client_system_time_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ScenarioResultSummary, client_user_time_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ScenarioResultSummary, latency_50_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ScenarioResultSummary, latency_90_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ScenarioResultSummary, latency_95_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ScenarioResultSummary, latency_99_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ScenarioResultSummary, latency_999_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ScenarioResultSummary, server_cpu_usage_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ScenarioResultSummary, successful_requests_per_second_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ScenarioResultSummary, failed_requests_per_second_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ScenarioResultSummary, client_polls_per_request_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ScenarioResultSummary, server_polls_per_request_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ScenarioResultSummary, server_queries_per_cpu_sec_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ScenarioResultSummary, client_queries_per_cpu_sec_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ScenarioResult, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ScenarioResult, scenario_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ScenarioResult, latencies_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ScenarioResult, client_stats_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ScenarioResult, server_stats_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ScenarioResult, server_cores_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ScenarioResult, summary_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ScenarioResult, client_success_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ScenarioResult, server_success_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ScenarioResult, request_results_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::grpc::testing::PoissonParams)},
  { 6, -1, sizeof(::grpc::testing::ClosedLoopParams)},
  { 11, -1, sizeof(::grpc::testing::LoadParams)},
  { 19, -1, sizeof(::grpc::testing::SecurityParams)},
  { 27, -1, sizeof(::grpc::testing::ChannelArg)},
  { 36, -1, sizeof(::grpc::testing::ClientConfig)},
  { 58, -1, sizeof(::grpc::testing::ClientStatus)},
  { 64, -1, sizeof(::grpc::testing::Mark)},
  { 70, -1, sizeof(::grpc::testing::ClientArgs)},
  { 78, -1, sizeof(::grpc::testing::ServerConfig)},
  { 94, -1, sizeof(::grpc::testing::ServerArgs)},
  { 102, -1, sizeof(::grpc::testing::ServerStatus)},
  { 110, -1, sizeof(::grpc::testing::CoreRequest)},
  { 115, -1, sizeof(::grpc::testing::CoreResponse)},
  { 121, -1, sizeof(::grpc::testing::Void)},
  { 126, -1, sizeof(::grpc::testing::Scenario)},
  { 139, -1, sizeof(::grpc::testing::Scenarios)},
  { 145, -1, sizeof(::grpc::testing::ScenarioResultSummary)},
  { 168, -1, sizeof(::grpc::testing::ScenarioResult)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_PoissonParams_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_ClosedLoopParams_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_LoadParams_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_SecurityParams_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_ChannelArg_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_ClientConfig_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_ClientStatus_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_Mark_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_ClientArgs_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_ServerConfig_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_ServerArgs_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_ServerStatus_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_CoreRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_CoreResponse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_Void_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_Scenario_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_Scenarios_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_ScenarioResultSummary_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_ScenarioResult_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  ::google::protobuf::MessageFactory* factory = NULL;
  AssignDescriptors(
      "src/proto/grpc/testing/control.proto", schemas, file_default_instances, TableStruct::offsets, factory,
      file_level_metadata, file_level_enum_descriptors, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 19);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n$src/proto/grpc/testing/control.proto\022\014"
      "grpc.testing\032%src/proto/grpc/testing/pay"
      "loads.proto\032\"src/proto/grpc/testing/stat"
      "s.proto\"%\n\rPoissonParams\022\024\n\014offered_load"
      "\030\001 \001(\001\"\022\n\020ClosedLoopParams\"{\n\nLoadParams"
      "\0225\n\013closed_loop\030\001 \001(\0132\036.grpc.testing.Clo"
      "sedLoopParamsH\000\022.\n\007poisson\030\002 \001(\0132\033.grpc."
      "testing.PoissonParamsH\000B\006\n\004load\"V\n\016Secur"
      "ityParams\022\023\n\013use_test_ca\030\001 \001(\010\022\034\n\024server"
      "_host_override\030\002 \001(\t\022\021\n\tcred_type\030\003 \001(\t\""
      "M\n\nChannelArg\022\014\n\004name\030\001 \001(\t\022\023\n\tstr_value"
      "\030\002 \001(\tH\000\022\023\n\tint_value\030\003 \001(\005H\000B\007\n\005value\"\357"
      "\004\n\014ClientConfig\022\026\n\016server_targets\030\001 \003(\t\022"
      "-\n\013client_type\030\002 \001(\0162\030.grpc.testing.Clie"
      "ntType\0225\n\017security_params\030\003 \001(\0132\034.grpc.t"
      "esting.SecurityParams\022$\n\034outstanding_rpc"
      "s_per_channel\030\004 \001(\005\022\027\n\017client_channels\030\005"
      " \001(\005\022\034\n\024async_client_threads\030\007 \001(\005\022\'\n\010rp"
      "c_type\030\010 \001(\0162\025.grpc.testing.RpcType\022-\n\013l"
      "oad_params\030\n \001(\0132\030.grpc.testing.LoadPara"
      "ms\0223\n\016payload_config\030\013 \001(\0132\033.grpc.testin"
      "g.PayloadConfig\0227\n\020histogram_params\030\014 \001("
      "\0132\035.grpc.testing.HistogramParams\022\021\n\tcore"
      "_list\030\r \003(\005\022\022\n\ncore_limit\030\016 \001(\005\022\030\n\020other"
      "_client_api\030\017 \001(\t\022.\n\014channel_args\030\020 \003(\0132"
      "\030.grpc.testing.ChannelArg\022\026\n\016threads_per"
      "_cq\030\021 \001(\005\022\033\n\023messages_per_stream\030\022 \001(\005\022\030"
      "\n\020use_coalesce_api\030\023 \001(\010\"8\n\014ClientStatus"
      "\022(\n\005stats\030\001 \001(\0132\031.grpc.testing.ClientSta"
      "ts\"\025\n\004Mark\022\r\n\005reset\030\001 \001(\010\"h\n\nClientArgs\022"
      "+\n\005setup\030\001 \001(\0132\032.grpc.testing.ClientConf"
      "igH\000\022\"\n\004mark\030\002 \001(\0132\022.grpc.testing.MarkH\000"
      "B\t\n\007argtype\"\375\002\n\014ServerConfig\022-\n\013server_t"
      "ype\030\001 \001(\0162\030.grpc.testing.ServerType\0225\n\017s"
      "ecurity_params\030\002 \001(\0132\034.grpc.testing.Secu"
      "rityParams\022\014\n\004port\030\004 \001(\005\022\034\n\024async_server"
      "_threads\030\007 \001(\005\022\022\n\ncore_limit\030\010 \001(\005\0223\n\016pa"
      "yload_config\030\t \001(\0132\033.grpc.testing.Payloa"
      "dConfig\022\021\n\tcore_list\030\n \003(\005\022\030\n\020other_serv"
      "er_api\030\013 \001(\t\022\026\n\016threads_per_cq\030\014 \001(\005\022\034\n\023"
      "resource_quota_size\030\351\007 \001(\005\022/\n\014channel_ar"
      "gs\030\352\007 \003(\0132\030.grpc.testing.ChannelArg\"h\n\nS"
      "erverArgs\022+\n\005setup\030\001 \001(\0132\032.grpc.testing."
      "ServerConfigH\000\022\"\n\004mark\030\002 \001(\0132\022.grpc.test"
      "ing.MarkH\000B\t\n\007argtype\"U\n\014ServerStatus\022(\n"
      "\005stats\030\001 \001(\0132\031.grpc.testing.ServerStats\022"
      "\014\n\004port\030\002 \001(\005\022\r\n\005cores\030\003 \001(\005\"\r\n\013CoreRequ"
      "est\"\035\n\014CoreResponse\022\r\n\005cores\030\001 \001(\005\"\006\n\004Vo"
      "id\"\375\001\n\010Scenario\022\014\n\004name\030\001 \001(\t\0221\n\rclient_"
      "config\030\002 \001(\0132\032.grpc.testing.ClientConfig"
      "\022\023\n\013num_clients\030\003 \001(\005\0221\n\rserver_config\030\004"
      " \001(\0132\032.grpc.testing.ServerConfig\022\023\n\013num_"
      "servers\030\005 \001(\005\022\026\n\016warmup_seconds\030\006 \001(\005\022\031\n"
      "\021benchmark_seconds\030\007 \001(\005\022 \n\030spawn_local_"
      "worker_count\030\010 \001(\005\"6\n\tScenarios\022)\n\tscena"
      "rios\030\001 \003(\0132\026.grpc.testing.Scenario\"\204\004\n\025S"
      "cenarioResultSummary\022\013\n\003qps\030\001 \001(\001\022\033\n\023qps"
      "_per_server_core\030\002 \001(\001\022\032\n\022server_system_"
      "time\030\003 \001(\001\022\030\n\020server_user_time\030\004 \001(\001\022\032\n\022"
      "client_system_time\030\005 \001(\001\022\030\n\020client_user_"
      "time\030\006 \001(\001\022\022\n\nlatency_50\030\007 \001(\001\022\022\n\nlatenc"
      "y_90\030\010 \001(\001\022\022\n\nlatency_95\030\t \001(\001\022\022\n\nlatenc"
      "y_99\030\n \001(\001\022\023\n\013latency_999\030\013 \001(\001\022\030\n\020serve"
      "r_cpu_usage\030\014 \001(\001\022&\n\036successful_requests"
      "_per_second\030\r \001(\001\022\"\n\032failed_requests_per"
      "_second\030\016 \001(\001\022 \n\030client_polls_per_reques"
      "t\030\017 \001(\001\022 \n\030server_polls_per_request\030\020 \001("
      "\001\022\"\n\032server_queries_per_cpu_sec\030\021 \001(\001\022\"\n"
      "\032client_queries_per_cpu_sec\030\022 \001(\001\"\203\003\n\016Sc"
      "enarioResult\022(\n\010scenario\030\001 \001(\0132\026.grpc.te"
      "sting.Scenario\022.\n\tlatencies\030\002 \001(\0132\033.grpc"
      ".testing.HistogramData\022/\n\014client_stats\030\003"
      " \003(\0132\031.grpc.testing.ClientStats\022/\n\014serve"
      "r_stats\030\004 \003(\0132\031.grpc.testing.ServerStats"
      "\022\024\n\014server_cores\030\005 \003(\005\0224\n\007summary\030\006 \001(\0132"
      "#.grpc.testing.ScenarioResultSummary\022\026\n\016"
      "client_success\030\007 \003(\010\022\026\n\016server_success\030\010"
      " \003(\010\0229\n\017request_results\030\t \003(\0132 .grpc.tes"
      "ting.RequestResultCount*A\n\nClientType\022\017\n"
      "\013SYNC_CLIENT\020\000\022\020\n\014ASYNC_CLIENT\020\001\022\020\n\014OTHE"
      "R_CLIENT\020\002*[\n\nServerType\022\017\n\013SYNC_SERVER\020"
      "\000\022\020\n\014ASYNC_SERVER\020\001\022\030\n\024ASYNC_GENERIC_SER"
      "VER\020\002\022\020\n\014OTHER_SERVER\020\003*r\n\007RpcType\022\t\n\005UN"
      "ARY\020\000\022\r\n\tSTREAMING\020\001\022\031\n\025STREAMING_FROM_C"
      "LIENT\020\002\022\031\n\025STREAMING_FROM_SERVER\020\003\022\027\n\023ST"
      "REAMING_BOTH_WAYS\020\004b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 3427);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "src/proto/grpc/testing/control.proto", &protobuf_RegisterTypes);
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::AddDescriptors();
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto
namespace grpc {
namespace testing {
const ::google::protobuf::EnumDescriptor* ClientType_descriptor() {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_enum_descriptors[0];
}
bool ClientType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}

const ::google::protobuf::EnumDescriptor* ServerType_descriptor() {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_enum_descriptors[1];
}
bool ServerType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

const ::google::protobuf::EnumDescriptor* RpcType_descriptor() {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_enum_descriptors[2];
}
bool RpcType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
      return true;
    default:
      return false;
  }
}


// ===================================================================

void PoissonParams::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int PoissonParams::kOfferedLoadFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

PoissonParams::PoissonParams()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsPoissonParams();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.PoissonParams)
}
PoissonParams::PoissonParams(const PoissonParams& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  offered_load_ = from.offered_load_;
  // @@protoc_insertion_point(copy_constructor:grpc.testing.PoissonParams)
}

void PoissonParams::SharedCtor() {
  offered_load_ = 0;
  _cached_size_ = 0;
}

PoissonParams::~PoissonParams() {
  // @@protoc_insertion_point(destructor:grpc.testing.PoissonParams)
  SharedDtor();
}

void PoissonParams::SharedDtor() {
}

void PoissonParams::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* PoissonParams::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const PoissonParams& PoissonParams::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsPoissonParams();
  return *internal_default_instance();
}

PoissonParams* PoissonParams::New(::google::protobuf::Arena* arena) const {
  PoissonParams* n = new PoissonParams;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void PoissonParams::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.PoissonParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  offered_load_ = 0;
  _internal_metadata_.Clear();
}

bool PoissonParams::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.PoissonParams)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // double offered_load = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(9u /* 9 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &offered_load_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.PoissonParams)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.PoissonParams)
  return false;
#undef DO_
}

void PoissonParams::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.PoissonParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double offered_load = 1;
  if (this->offered_load() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(1, this->offered_load(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.PoissonParams)
}

::google::protobuf::uint8* PoissonParams::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.PoissonParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double offered_load = 1;
  if (this->offered_load() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(1, this->offered_load(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.PoissonParams)
  return target;
}

size_t PoissonParams::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.PoissonParams)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // double offered_load = 1;
  if (this->offered_load() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void PoissonParams::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.PoissonParams)
  GOOGLE_DCHECK_NE(&from, this);
  const PoissonParams* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const PoissonParams>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.PoissonParams)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.PoissonParams)
    MergeFrom(*source);
  }
}

void PoissonParams::MergeFrom(const PoissonParams& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.PoissonParams)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.offered_load() != 0) {
    set_offered_load(from.offered_load());
  }
}

void PoissonParams::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.PoissonParams)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PoissonParams::CopyFrom(const PoissonParams& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.PoissonParams)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PoissonParams::IsInitialized() const {
  return true;
}

void PoissonParams::Swap(PoissonParams* other) {
  if (other == this) return;
  InternalSwap(other);
}
void PoissonParams::InternalSwap(PoissonParams* other) {
  using std::swap;
  swap(offered_load_, other->offered_load_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata PoissonParams::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ClosedLoopParams::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ClosedLoopParams::ClosedLoopParams()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsClosedLoopParams();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.ClosedLoopParams)
}
ClosedLoopParams::ClosedLoopParams(const ClosedLoopParams& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:grpc.testing.ClosedLoopParams)
}

void ClosedLoopParams::SharedCtor() {
  _cached_size_ = 0;
}

ClosedLoopParams::~ClosedLoopParams() {
  // @@protoc_insertion_point(destructor:grpc.testing.ClosedLoopParams)
  SharedDtor();
}

void ClosedLoopParams::SharedDtor() {
}

void ClosedLoopParams::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ClosedLoopParams::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ClosedLoopParams& ClosedLoopParams::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsClosedLoopParams();
  return *internal_default_instance();
}

ClosedLoopParams* ClosedLoopParams::New(::google::protobuf::Arena* arena) const {
  ClosedLoopParams* n = new ClosedLoopParams;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ClosedLoopParams::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.ClosedLoopParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_metadata_.Clear();
}

bool ClosedLoopParams::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.ClosedLoopParams)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, _internal_metadata_.mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.ClosedLoopParams)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.ClosedLoopParams)
  return false;
#undef DO_
}

void ClosedLoopParams::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.ClosedLoopParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.ClosedLoopParams)
}

::google::protobuf::uint8* ClosedLoopParams::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.ClosedLoopParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.ClosedLoopParams)
  return target;
}

size_t ClosedLoopParams::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.ClosedLoopParams)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ClosedLoopParams::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.ClosedLoopParams)
  GOOGLE_DCHECK_NE(&from, this);
  const ClosedLoopParams* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ClosedLoopParams>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.ClosedLoopParams)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.ClosedLoopParams)
    MergeFrom(*source);
  }
}

void ClosedLoopParams::MergeFrom(const ClosedLoopParams& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.ClosedLoopParams)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

}

void ClosedLoopParams::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.ClosedLoopParams)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ClosedLoopParams::CopyFrom(const ClosedLoopParams& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.ClosedLoopParams)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ClosedLoopParams::IsInitialized() const {
  return true;
}

void ClosedLoopParams::Swap(ClosedLoopParams* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ClosedLoopParams::InternalSwap(ClosedLoopParams* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ClosedLoopParams::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void LoadParams::InitAsDefaultInstance() {
  ::grpc::testing::_LoadParams_default_instance_.closed_loop_ = const_cast< ::grpc::testing::ClosedLoopParams*>(
      ::grpc::testing::ClosedLoopParams::internal_default_instance());
  ::grpc::testing::_LoadParams_default_instance_.poisson_ = const_cast< ::grpc::testing::PoissonParams*>(
      ::grpc::testing::PoissonParams::internal_default_instance());
}
void LoadParams::set_allocated_closed_loop(::grpc::testing::ClosedLoopParams* closed_loop) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_load();
  if (closed_loop) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      closed_loop = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, closed_loop, submessage_arena);
    }
    set_has_closed_loop();
    load_.closed_loop_ = closed_loop;
  }
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.LoadParams.closed_loop)
}
void LoadParams::set_allocated_poisson(::grpc::testing::PoissonParams* poisson) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_load();
  if (poisson) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      poisson = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, poisson, submessage_arena);
    }
    set_has_poisson();
    load_.poisson_ = poisson;
  }
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.LoadParams.poisson)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int LoadParams::kClosedLoopFieldNumber;
const int LoadParams::kPoissonFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

LoadParams::LoadParams()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsLoadParams();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.LoadParams)
}
LoadParams::LoadParams(const LoadParams& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  clear_has_load();
  switch (from.load_case()) {
    case kClosedLoop: {
      mutable_closed_loop()->::grpc::testing::ClosedLoopParams::MergeFrom(from.closed_loop());
      break;
    }
    case kPoisson: {
      mutable_poisson()->::grpc::testing::PoissonParams::MergeFrom(from.poisson());
      break;
    }
    case LOAD_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:grpc.testing.LoadParams)
}

void LoadParams::SharedCtor() {
  clear_has_load();
  _cached_size_ = 0;
}

LoadParams::~LoadParams() {
  // @@protoc_insertion_point(destructor:grpc.testing.LoadParams)
  SharedDtor();
}

void LoadParams::SharedDtor() {
  if (has_load()) {
    clear_load();
  }
}

void LoadParams::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* LoadParams::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const LoadParams& LoadParams::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsLoadParams();
  return *internal_default_instance();
}

LoadParams* LoadParams::New(::google::protobuf::Arena* arena) const {
  LoadParams* n = new LoadParams;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void LoadParams::clear_load() {
// @@protoc_insertion_point(one_of_clear_start:grpc.testing.LoadParams)
  switch (load_case()) {
    case kClosedLoop: {
      delete load_.closed_loop_;
      break;
    }
    case kPoisson: {
      delete load_.poisson_;
      break;
    }
    case LOAD_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = LOAD_NOT_SET;
}


void LoadParams::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.LoadParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_load();
  _internal_metadata_.Clear();
}

bool LoadParams::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.LoadParams)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .grpc.testing.ClosedLoopParams closed_loop = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_closed_loop()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.testing.PoissonParams poisson = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_poisson()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.LoadParams)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.LoadParams)
  return false;
#undef DO_
}

void LoadParams::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.LoadParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.ClosedLoopParams closed_loop = 1;
  if (has_closed_loop()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *load_.closed_loop_, output);
  }

  // .grpc.testing.PoissonParams poisson = 2;
  if (has_poisson()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *load_.poisson_, output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.LoadParams)
}

::google::protobuf::uint8* LoadParams::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.LoadParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.ClosedLoopParams closed_loop = 1;
  if (has_closed_loop()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, *load_.closed_loop_, deterministic, target);
  }

  // .grpc.testing.PoissonParams poisson = 2;
  if (has_poisson()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, *load_.poisson_, deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.LoadParams)
  return target;
}

size_t LoadParams::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.LoadParams)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  switch (load_case()) {
    // .grpc.testing.ClosedLoopParams closed_loop = 1;
    case kClosedLoop: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *load_.closed_loop_);
      break;
    }
    // .grpc.testing.PoissonParams poisson = 2;
    case kPoisson: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *load_.poisson_);
      break;
    }
    case LOAD_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void LoadParams::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.LoadParams)
  GOOGLE_DCHECK_NE(&from, this);
  const LoadParams* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const LoadParams>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.LoadParams)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.LoadParams)
    MergeFrom(*source);
  }
}

void LoadParams::MergeFrom(const LoadParams& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.LoadParams)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.load_case()) {
    case kClosedLoop: {
      mutable_closed_loop()->::grpc::testing::ClosedLoopParams::MergeFrom(from.closed_loop());
      break;
    }
    case kPoisson: {
      mutable_poisson()->::grpc::testing::PoissonParams::MergeFrom(from.poisson());
      break;
    }
    case LOAD_NOT_SET: {
      break;
    }
  }
}

void LoadParams::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.LoadParams)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void LoadParams::CopyFrom(const LoadParams& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.LoadParams)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LoadParams::IsInitialized() const {
  return true;
}

void LoadParams::Swap(LoadParams* other) {
  if (other == this) return;
  InternalSwap(other);
}
void LoadParams::InternalSwap(LoadParams* other) {
  using std::swap;
  swap(load_, other->load_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata LoadParams::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void SecurityParams::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SecurityParams::kUseTestCaFieldNumber;
const int SecurityParams::kServerHostOverrideFieldNumber;
const int SecurityParams::kCredTypeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SecurityParams::SecurityParams()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsSecurityParams();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.SecurityParams)
}
SecurityParams::SecurityParams(const SecurityParams& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  server_host_override_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.server_host_override().size() > 0) {
    server_host_override_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.server_host_override_);
  }
  cred_type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.cred_type().size() > 0) {
    cred_type_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.cred_type_);
  }
  use_test_ca_ = from.use_test_ca_;
  // @@protoc_insertion_point(copy_constructor:grpc.testing.SecurityParams)
}

void SecurityParams::SharedCtor() {
  server_host_override_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  cred_type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  use_test_ca_ = false;
  _cached_size_ = 0;
}

SecurityParams::~SecurityParams() {
  // @@protoc_insertion_point(destructor:grpc.testing.SecurityParams)
  SharedDtor();
}

void SecurityParams::SharedDtor() {
  server_host_override_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  cred_type_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void SecurityParams::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SecurityParams::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const SecurityParams& SecurityParams::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsSecurityParams();
  return *internal_default_instance();
}

SecurityParams* SecurityParams::New(::google::protobuf::Arena* arena) const {
  SecurityParams* n = new SecurityParams;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void SecurityParams::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.SecurityParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  server_host_override_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  cred_type_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  use_test_ca_ = false;
  _internal_metadata_.Clear();
}

bool SecurityParams::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.SecurityParams)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // bool use_test_ca = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &use_test_ca_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string server_host_override = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_server_host_override()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->server_host_override().data(), static_cast<int>(this->server_host_override().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.testing.SecurityParams.server_host_override"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string cred_type = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_cred_type()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->cred_type().data(), static_cast<int>(this->cred_type().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.testing.SecurityParams.cred_type"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.SecurityParams)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.SecurityParams)
  return false;
#undef DO_
}

void SecurityParams::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.SecurityParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bool use_test_ca = 1;
  if (this->use_test_ca() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(1, this->use_test_ca(), output);
  }

  // string server_host_override = 2;
  if (this->server_host_override().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->server_host_override().data(), static_cast<int>(this->server_host_override().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.SecurityParams.server_host_override");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->server_host_override(), output);
  }

  // string cred_type = 3;
  if (this->cred_type().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->cred_type().data(), static_cast<int>(this->cred_type().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.SecurityParams.cred_type");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->cred_type(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.SecurityParams)
}

::google::protobuf::uint8* SecurityParams::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.SecurityParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bool use_test_ca = 1;
  if (this->use_test_ca() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(1, this->use_test_ca(), target);
  }

  // string server_host_override = 2;
  if (this->server_host_override().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->server_host_override().data(), static_cast<int>(this->server_host_override().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.SecurityParams.server_host_override");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->server_host_override(), target);
  }

  // string cred_type = 3;
  if (this->cred_type().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->cred_type().data(), static_cast<int>(this->cred_type().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.SecurityParams.cred_type");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->cred_type(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.SecurityParams)
  return target;
}

size_t SecurityParams::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.SecurityParams)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string server_host_override = 2;
  if (this->server_host_override().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->server_host_override());
  }

  // string cred_type = 3;
  if (this->cred_type().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->cred_type());
  }

  // bool use_test_ca = 1;
  if (this->use_test_ca() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SecurityParams::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.SecurityParams)
  GOOGLE_DCHECK_NE(&from, this);
  const SecurityParams* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SecurityParams>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.SecurityParams)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.SecurityParams)
    MergeFrom(*source);
  }
}

void SecurityParams::MergeFrom(const SecurityParams& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.SecurityParams)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.server_host_override().size() > 0) {

    server_host_override_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.server_host_override_);
  }
  if (from.cred_type().size() > 0) {

    cred_type_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.cred_type_);
  }
  if (from.use_test_ca() != 0) {
    set_use_test_ca(from.use_test_ca());
  }
}

void SecurityParams::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.SecurityParams)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SecurityParams::CopyFrom(const SecurityParams& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.SecurityParams)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SecurityParams::IsInitialized() const {
  return true;
}

void SecurityParams::Swap(SecurityParams* other) {
  if (other == this) return;
  InternalSwap(other);
}
void SecurityParams::InternalSwap(SecurityParams* other) {
  using std::swap;
  server_host_override_.Swap(&other->server_host_override_);
  cred_type_.Swap(&other->cred_type_);
  swap(use_test_ca_, other->use_test_ca_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata SecurityParams::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ChannelArg::InitAsDefaultInstance() {
  ::grpc::testing::_ChannelArg_default_instance_.str_value_.UnsafeSetDefault(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::grpc::testing::_ChannelArg_default_instance_.int_value_ = 0;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ChannelArg::kNameFieldNumber;
const int ChannelArg::kStrValueFieldNumber;
const int ChannelArg::kIntValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ChannelArg::ChannelArg()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsChannelArg();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.ChannelArg)
}
ChannelArg::ChannelArg(const ChannelArg& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  clear_has_value();
  switch (from.value_case()) {
    case kStrValue: {
      set_str_value(from.str_value());
      break;
    }
    case kIntValue: {
      set_int_value(from.int_value());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:grpc.testing.ChannelArg)
}

void ChannelArg::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_value();
  _cached_size_ = 0;
}

ChannelArg::~ChannelArg() {
  // @@protoc_insertion_point(destructor:grpc.testing.ChannelArg)
  SharedDtor();
}

void ChannelArg::SharedDtor() {
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (has_value()) {
    clear_value();
  }
}

void ChannelArg::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ChannelArg::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ChannelArg& ChannelArg::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsChannelArg();
  return *internal_default_instance();
}

ChannelArg* ChannelArg::New(::google::protobuf::Arena* arena) const {
  ChannelArg* n = new ChannelArg;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ChannelArg::clear_value() {
// @@protoc_insertion_point(one_of_clear_start:grpc.testing.ChannelArg)
  switch (value_case()) {
    case kStrValue: {
      value_.str_value_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
      break;
    }
    case kIntValue: {
      // No need to clear
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = VALUE_NOT_SET;
}


void ChannelArg::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.ChannelArg)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_value();
  _internal_metadata_.Clear();
}

bool ChannelArg::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.ChannelArg)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.testing.ChannelArg.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string str_value = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_str_value()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->str_value().data(), static_cast<int>(this->str_value().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.testing.ChannelArg.str_value"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 int_value = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {
          clear_value();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &value_.int_value_)));
          set_has_int_value();
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.ChannelArg)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.ChannelArg)
  return false;
#undef DO_
}

void ChannelArg::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.ChannelArg)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.ChannelArg.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // string str_value = 2;
  if (has_str_value()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->str_value().data(), static_cast<int>(this->str_value().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.ChannelArg.str_value");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->str_value(), output);
  }

  // int32 int_value = 3;
  if (has_int_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->int_value(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.ChannelArg)
}

::google::protobuf::uint8* ChannelArg::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.ChannelArg)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.ChannelArg.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // string str_value = 2;
  if (has_str_value()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->str_value().data(), static_cast<int>(this->str_value().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.ChannelArg.str_value");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->str_value(), target);
  }

  // int32 int_value = 3;
  if (has_int_value()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->int_value(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.ChannelArg)
  return target;
}

size_t ChannelArg::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.ChannelArg)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  switch (value_case()) {
    // string str_value = 2;
    case kStrValue: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->str_value());
      break;
    }
    // int32 int_value = 3;
    case kIntValue: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
          this->int_value());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ChannelArg::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.ChannelArg)
  GOOGLE_DCHECK_NE(&from, this);
  const ChannelArg* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ChannelArg>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.ChannelArg)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.ChannelArg)
    MergeFrom(*source);
  }
}

void ChannelArg::MergeFrom(const ChannelArg& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.ChannelArg)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.name().size() > 0) {

    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  switch (from.value_case()) {
    case kStrValue: {
      set_str_value(from.str_value());
      break;
    }
    case kIntValue: {
      set_int_value(from.int_value());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
}

void ChannelArg::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.ChannelArg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ChannelArg::CopyFrom(const ChannelArg& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.ChannelArg)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ChannelArg::IsInitialized() const {
  return true;
}

void ChannelArg::Swap(ChannelArg* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ChannelArg::InternalSwap(ChannelArg* other) {
  using std::swap;
  name_.Swap(&other->name_);
  swap(value_, other->value_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ChannelArg::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ClientConfig::InitAsDefaultInstance() {
  ::grpc::testing::_ClientConfig_default_instance_._instance.get_mutable()->security_params_ = const_cast< ::grpc::testing::SecurityParams*>(
      ::grpc::testing::SecurityParams::internal_default_instance());
  ::grpc::testing::_ClientConfig_default_instance_._instance.get_mutable()->load_params_ = const_cast< ::grpc::testing::LoadParams*>(
      ::grpc::testing::LoadParams::internal_default_instance());
  ::grpc::testing::_ClientConfig_default_instance_._instance.get_mutable()->payload_config_ = const_cast< ::grpc::testing::PayloadConfig*>(
      ::grpc::testing::PayloadConfig::internal_default_instance());
  ::grpc::testing::_ClientConfig_default_instance_._instance.get_mutable()->histogram_params_ = const_cast< ::grpc::testing::HistogramParams*>(
      ::grpc::testing::HistogramParams::internal_default_instance());
}
void ClientConfig::clear_payload_config() {
  if (GetArenaNoVirtual() == NULL && payload_config_ != NULL) {
    delete payload_config_;
  }
  payload_config_ = NULL;
}
void ClientConfig::clear_histogram_params() {
  if (GetArenaNoVirtual() == NULL && histogram_params_ != NULL) {
    delete histogram_params_;
  }
  histogram_params_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ClientConfig::kServerTargetsFieldNumber;
const int ClientConfig::kClientTypeFieldNumber;
const int ClientConfig::kSecurityParamsFieldNumber;
const int ClientConfig::kOutstandingRpcsPerChannelFieldNumber;
const int ClientConfig::kClientChannelsFieldNumber;
const int ClientConfig::kAsyncClientThreadsFieldNumber;
const int ClientConfig::kRpcTypeFieldNumber;
const int ClientConfig::kLoadParamsFieldNumber;
const int ClientConfig::kPayloadConfigFieldNumber;
const int ClientConfig::kHistogramParamsFieldNumber;
const int ClientConfig::kCoreListFieldNumber;
const int ClientConfig::kCoreLimitFieldNumber;
const int ClientConfig::kOtherClientApiFieldNumber;
const int ClientConfig::kChannelArgsFieldNumber;
const int ClientConfig::kThreadsPerCqFieldNumber;
const int ClientConfig::kMessagesPerStreamFieldNumber;
const int ClientConfig::kUseCoalesceApiFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ClientConfig::ClientConfig()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsClientConfig();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.ClientConfig)
}
ClientConfig::ClientConfig(const ClientConfig& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      server_targets_(from.server_targets_),
      core_list_(from.core_list_),
      channel_args_(from.channel_args_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  other_client_api_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.other_client_api().size() > 0) {
    other_client_api_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.other_client_api_);
  }
  if (from.has_security_params()) {
    security_params_ = new ::grpc::testing::SecurityParams(*from.security_params_);
  } else {
    security_params_ = NULL;
  }
  if (from.has_load_params()) {
    load_params_ = new ::grpc::testing::LoadParams(*from.load_params_);
  } else {
    load_params_ = NULL;
  }
  if (from.has_payload_config()) {
    payload_config_ = new ::grpc::testing::PayloadConfig(*from.payload_config_);
  } else {
    payload_config_ = NULL;
  }
  if (from.has_histogram_params()) {
    histogram_params_ = new ::grpc::testing::HistogramParams(*from.histogram_params_);
  } else {
    histogram_params_ = NULL;
  }
  ::memcpy(&client_type_, &from.client_type_,
    static_cast<size_t>(reinterpret_cast<char*>(&use_coalesce_api_) -
    reinterpret_cast<char*>(&client_type_)) + sizeof(use_coalesce_api_));
  // @@protoc_insertion_point(copy_constructor:grpc.testing.ClientConfig)
}

void ClientConfig::SharedCtor() {
  other_client_api_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&security_params_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&use_coalesce_api_) -
      reinterpret_cast<char*>(&security_params_)) + sizeof(use_coalesce_api_));
  _cached_size_ = 0;
}

ClientConfig::~ClientConfig() {
  // @@protoc_insertion_point(destructor:grpc.testing.ClientConfig)
  SharedDtor();
}

void ClientConfig::SharedDtor() {
  other_client_api_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete security_params_;
  if (this != internal_default_instance()) delete load_params_;
  if (this != internal_default_instance()) delete payload_config_;
  if (this != internal_default_instance()) delete histogram_params_;
}

void ClientConfig::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ClientConfig::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ClientConfig& ClientConfig::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsClientConfig();
  return *internal_default_instance();
}

ClientConfig* ClientConfig::New(::google::protobuf::Arena* arena) const {
  ClientConfig* n = new ClientConfig;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ClientConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.ClientConfig)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  server_targets_.Clear();
  core_list_.Clear();
  channel_args_.Clear();
  other_client_api_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && security_params_ != NULL) {
    delete security_params_;
  }
  security_params_ = NULL;
  if (GetArenaNoVirtual() == NULL && load_params_ != NULL) {
    delete load_params_;
  }
  load_params_ = NULL;
  if (GetArenaNoVirtual() == NULL && payload_config_ != NULL) {
    delete payload_config_;
  }
  payload_config_ = NULL;
  if (GetArenaNoVirtual() == NULL && histogram_params_ != NULL) {
    delete histogram_params_;
  }
  histogram_params_ = NULL;
  ::memset(&client_type_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&use_coalesce_api_) -
      reinterpret_cast<char*>(&client_type_)) + sizeof(use_coalesce_api_));
  _internal_metadata_.Clear();
}

bool ClientConfig::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.ClientConfig)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(16383u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated string server_targets = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_server_targets()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->server_targets(this->server_targets_size() - 1).data(),
            static_cast<int>(this->server_targets(this->server_targets_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.testing.ClientConfig.server_targets"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.testing.ClientType client_type = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_client_type(static_cast< ::grpc::testing::ClientType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.testing.SecurityParams security_params = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_security_params()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 outstanding_rpcs_per_channel = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &outstanding_rpcs_per_channel_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 client_channels = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &client_channels_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 async_client_threads = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(56u /* 56 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &async_client_threads_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.testing.RpcType rpc_type = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(64u /* 64 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_rpc_type(static_cast< ::grpc::testing::RpcType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.testing.LoadParams load_params = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(82u /* 82 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_load_params()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.testing.PayloadConfig payload_config = 11;
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(90u /* 90 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_payload_config()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.testing.HistogramParams histogram_params = 12;
      case 12: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(98u /* 98 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_histogram_params()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated int32 core_list = 13;
      case 13: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(106u /* 106 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_core_list())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(104u /* 104 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 1, 106u, input, this->mutable_core_list())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 core_limit = 14;
      case 14: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(112u /* 112 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &core_limit_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string other_client_api = 15;
      case 15: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(122u /* 122 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_other_client_api()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->other_client_api().data(), static_cast<int>(this->other_client_api().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.testing.ClientConfig.other_client_api"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .grpc.testing.ChannelArg channel_args = 16;
      case 16: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(130u /* 130 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(input, add_channel_args()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 threads_per_cq = 17;
      case 17: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(136u /* 136 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &threads_per_cq_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 messages_per_stream = 18;
      case 18: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(144u /* 144 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &messages_per_stream_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool use_coalesce_api = 19;
      case 19: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(152u /* 152 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &use_coalesce_api_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.ClientConfig)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.ClientConfig)
  return false;
#undef DO_
}

void ClientConfig::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.ClientConfig)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string server_targets = 1;
  for (int i = 0, n = this->server_targets_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->server_targets(i).data(), static_cast<int>(this->server_targets(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.ClientConfig.server_targets");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->server_targets(i), output);
  }

  // .grpc.testing.ClientType client_type = 2;
  if (this->client_type() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      2, this->client_type(), output);
  }

  // .grpc.testing.SecurityParams security_params = 3;
  if (this->has_security_params()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, *this->security_params_, output);
  }

  // int32 outstanding_rpcs_per_channel = 4;
  if (this->outstanding_rpcs_per_channel() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->outstanding_rpcs_per_channel(), output);
  }

  // int32 client_channels = 5;
  if (this->client_channels() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(5, this->client_channels(), output);
  }

  // int32 async_client_threads = 7;
  if (this->async_client_threads() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(7, this->async_client_threads(), output);
  }

  // .grpc.testing.RpcType rpc_type = 8;
  if (this->rpc_type() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      8, this->rpc_type(), output);
  }

  // .grpc.testing.LoadParams load_params = 10;
  if (this->has_load_params()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      10, *this->load_params_, output);
  }

  // .grpc.testing.PayloadConfig payload_config = 11;
  if (this->has_payload_config()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      11, *this->payload_config_, output);
  }

  // .grpc.testing.HistogramParams histogram_params = 12;
  if (this->has_histogram_params()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      12, *this->histogram_params_, output);
  }

  // repeated int32 core_list = 13;
  if (this->core_list_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(13, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _core_list_cached_byte_size_));
  }
  for (int i = 0, n = this->core_list_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32NoTag(
      this->core_list(i), output);
  }

  // int32 core_limit = 14;
  if (this->core_limit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(14, this->core_limit(), output);
  }

  // string other_client_api = 15;
  if (this->other_client_api().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->other_client_api().data(), static_cast<int>(this->other_client_api().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.ClientConfig.other_client_api");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      15, this->other_client_api(), output);
  }

  // repeated .grpc.testing.ChannelArg channel_args = 16;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->channel_args_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      16, this->channel_args(static_cast<int>(i)), output);
  }

  // int32 threads_per_cq = 17;
  if (this->threads_per_cq() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(17, this->threads_per_cq(), output);
  }

  // int32 messages_per_stream = 18;
  if (this->messages_per_stream() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(18, this->messages_per_stream(), output);
  }

  // bool use_coalesce_api = 19;
  if (this->use_coalesce_api() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(19, this->use_coalesce_api(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.ClientConfig)
}

::google::protobuf::uint8* ClientConfig::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.ClientConfig)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string server_targets = 1;
  for (int i = 0, n = this->server_targets_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->server_targets(i).data(), static_cast<int>(this->server_targets(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.ClientConfig.server_targets");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(1, this->server_targets(i), target);
  }

  // .grpc.testing.ClientType client_type = 2;
  if (this->client_type() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      2, this->client_type(), target);
  }

  // .grpc.testing.SecurityParams security_params = 3;
  if (this->has_security_params()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, *this->security_params_, deterministic, target);
  }

  // int32 outstanding_rpcs_per_channel = 4;
  if (this->outstanding_rpcs_per_channel() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->outstanding_rpcs_per_channel(), target);
  }

  // int32 client_channels = 5;
  if (this->client_channels() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(5, this->client_channels(), target);
  }

  // int32 async_client_threads = 7;
  if (this->async_client_threads() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(7, this->async_client_threads(), target);
  }

  // .grpc.testing.RpcType rpc_type = 8;
  if (this->rpc_type() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      8, this->rpc_type(), target);
  }

  // .grpc.testing.LoadParams load_params = 10;
  if (this->has_load_params()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        10, *this->load_params_, deterministic, target);
  }

  // .grpc.testing.PayloadConfig payload_config = 11;
  if (this->has_payload_config()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        11, *this->payload_config_, deterministic, target);
  }

  // .grpc.testing.HistogramParams histogram_params = 12;
  if (this->has_histogram_params()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        12, *this->histogram_params_, deterministic, target);
  }

  // repeated int32 core_list = 13;
  if (this->core_list_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      13,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _core_list_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt32NoTagToArray(this->core_list_, target);
  }

  // int32 core_limit = 14;
  if (this->core_limit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(14, this->core_limit(), target);
  }

  // string other_client_api = 15;
  if (this->other_client_api().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->other_client_api().data(), static_cast<int>(this->other_client_api().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.ClientConfig.other_client_api");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        15, this->other_client_api(), target);
  }

  // repeated .grpc.testing.ChannelArg channel_args = 16;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->channel_args_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        16, this->channel_args(static_cast<int>(i)), deterministic, target);
  }

  // int32 threads_per_cq = 17;
  if (this->threads_per_cq() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(17, this->threads_per_cq(), target);
  }

  // int32 messages_per_stream = 18;
  if (this->messages_per_stream() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(18, this->messages_per_stream(), target);
  }

  // bool use_coalesce_api = 19;
  if (this->use_coalesce_api() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(19, this->use_coalesce_api(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.ClientConfig)
  return target;
}

size_t ClientConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.ClientConfig)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated string server_targets = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->server_targets_size());
  for (int i = 0, n = this->server_targets_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->server_targets(i));
  }

  // repeated int32 core_list = 13;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int32Size(this->core_list_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _core_list_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated .grpc.testing.ChannelArg channel_args = 16;
  {
    unsigned int count = static_cast<unsigned int>(this->channel_args_size());
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->channel_args(static_cast<int>(i)));
    }
  }

  // string other_client_api = 15;
  if (this->other_client_api().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->other_client_api());
  }

  // .grpc.testing.SecurityParams security_params = 3;
  if (this->has_security_params()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->security_params_);
  }

  // .grpc.testing.LoadParams load_params = 10;
  if (this->has_load_params()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->load_params_);
  }

  // .grpc.testing.PayloadConfig payload_config = 11;
  if (this->has_payload_config()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->payload_config_);
  }

  // .grpc.testing.HistogramParams histogram_params = 12;
  if (this->has_histogram_params()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->histogram_params_);
  }

  // .grpc.testing.ClientType client_type = 2;
  if (this->client_type() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->client_type());
  }

  // int32 outstanding_rpcs_per_channel = 4;
  if (this->outstanding_rpcs_per_channel() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->outstanding_rpcs_per_channel());
  }

  // int32 client_channels = 5;
  if (this->client_channels() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->client_channels());
  }

  // int32 async_client_threads = 7;
  if (this->async_client_threads() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->async_client_threads());
  }

  // .grpc.testing.RpcType rpc_type = 8;
  if (this->rpc_type() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->rpc_type());
  }

  // int32 core_limit = 14;
  if (this->core_limit() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->core_limit());
  }

  // int32 threads_per_cq = 17;
  if (this->threads_per_cq() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->threads_per_cq());
  }

  // int32 messages_per_stream = 18;
  if (this->messages_per_stream() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->messages_per_stream());
  }

  // bool use_coalesce_api = 19;
  if (this->use_coalesce_api() != 0) {
    total_size += 2 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ClientConfig::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.ClientConfig)
  GOOGLE_DCHECK_NE(&from, this);
  const ClientConfig* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ClientConfig>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.ClientConfig)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.ClientConfig)
    MergeFrom(*source);
  }
}

void ClientConfig::MergeFrom(const ClientConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.ClientConfig)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  server_targets_.MergeFrom(from.server_targets_);
  core_list_.MergeFrom(from.core_list_);
  channel_args_.MergeFrom(from.channel_args_);
  if (from.other_client_api().size() > 0) {

    other_client_api_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.other_client_api_);
  }
  if (from.has_security_params()) {
    mutable_security_params()->::grpc::testing::SecurityParams::MergeFrom(from.security_params());
  }
  if (from.has_load_params()) {
    mutable_load_params()->::grpc::testing::LoadParams::MergeFrom(from.load_params());
  }
  if (from.has_payload_config()) {
    mutable_payload_config()->::grpc::testing::PayloadConfig::MergeFrom(from.payload_config());
  }
  if (from.has_histogram_params()) {
    mutable_histogram_params()->::grpc::testing::HistogramParams::MergeFrom(from.histogram_params());
  }
  if (from.client_type() != 0) {
    set_client_type(from.client_type());
  }
  if (from.outstanding_rpcs_per_channel() != 0) {
    set_outstanding_rpcs_per_channel(from.outstanding_rpcs_per_channel());
  }
  if (from.client_channels() != 0) {
    set_client_channels(from.client_channels());
  }
  if (from.async_client_threads() != 0) {
    set_async_client_threads(from.async_client_threads());
  }
  if (from.rpc_type() != 0) {
    set_rpc_type(from.rpc_type());
  }
  if (from.core_limit() != 0) {
    set_core_limit(from.core_limit());
  }
  if (from.threads_per_cq() != 0) {
    set_threads_per_cq(from.threads_per_cq());
  }
  if (from.messages_per_stream() != 0) {
    set_messages_per_stream(from.messages_per_stream());
  }
  if (from.use_coalesce_api() != 0) {
    set_use_coalesce_api(from.use_coalesce_api());
  }
}

void ClientConfig::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.ClientConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ClientConfig::CopyFrom(const ClientConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.ClientConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ClientConfig::IsInitialized() const {
  return true;
}

void ClientConfig::Swap(ClientConfig* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ClientConfig::InternalSwap(ClientConfig* other) {
  using std::swap;
  server_targets_.InternalSwap(&other->server_targets_);
  core_list_.InternalSwap(&other->core_list_);
  channel_args_.InternalSwap(&other->channel_args_);
  other_client_api_.Swap(&other->other_client_api_);
  swap(security_params_, other->security_params_);
  swap(load_params_, other->load_params_);
  swap(payload_config_, other->payload_config_);
  swap(histogram_params_, other->histogram_params_);
  swap(client_type_, other->client_type_);
  swap(outstanding_rpcs_per_channel_, other->outstanding_rpcs_per_channel_);
  swap(client_channels_, other->client_channels_);
  swap(async_client_threads_, other->async_client_threads_);
  swap(rpc_type_, other->rpc_type_);
  swap(core_limit_, other->core_limit_);
  swap(threads_per_cq_, other->threads_per_cq_);
  swap(messages_per_stream_, other->messages_per_stream_);
  swap(use_coalesce_api_, other->use_coalesce_api_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ClientConfig::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ClientStatus::InitAsDefaultInstance() {
  ::grpc::testing::_ClientStatus_default_instance_._instance.get_mutable()->stats_ = const_cast< ::grpc::testing::ClientStats*>(
      ::grpc::testing::ClientStats::internal_default_instance());
}
void ClientStatus::clear_stats() {
  if (GetArenaNoVirtual() == NULL && stats_ != NULL) {
    delete stats_;
  }
  stats_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ClientStatus::kStatsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ClientStatus::ClientStatus()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsClientStatus();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.ClientStatus)
}
ClientStatus::ClientStatus(const ClientStatus& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_stats()) {
    stats_ = new ::grpc::testing::ClientStats(*from.stats_);
  } else {
    stats_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:grpc.testing.ClientStatus)
}

void ClientStatus::SharedCtor() {
  stats_ = NULL;
  _cached_size_ = 0;
}

ClientStatus::~ClientStatus() {
  // @@protoc_insertion_point(destructor:grpc.testing.ClientStatus)
  SharedDtor();
}

void ClientStatus::SharedDtor() {
  if (this != internal_default_instance()) delete stats_;
}

void ClientStatus::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ClientStatus::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ClientStatus& ClientStatus::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsClientStatus();
  return *internal_default_instance();
}

ClientStatus* ClientStatus::New(::google::protobuf::Arena* arena) const {
  ClientStatus* n = new ClientStatus;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ClientStatus::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.ClientStatus)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && stats_ != NULL) {
    delete stats_;
  }
  stats_ = NULL;
  _internal_metadata_.Clear();
}

bool ClientStatus::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.ClientStatus)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .grpc.testing.ClientStats stats = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_stats()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.ClientStatus)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.ClientStatus)
  return false;
#undef DO_
}

void ClientStatus::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.ClientStatus)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.ClientStats stats = 1;
  if (this->has_stats()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->stats_, output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.ClientStatus)
}

::google::protobuf::uint8* ClientStatus::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.ClientStatus)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.ClientStats stats = 1;
  if (this->has_stats()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, *this->stats_, deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.ClientStatus)
  return target;
}

size_t ClientStatus::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.ClientStatus)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .grpc.testing.ClientStats stats = 1;
  if (this->has_stats()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->stats_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ClientStatus::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.ClientStatus)
  GOOGLE_DCHECK_NE(&from, this);
  const ClientStatus* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ClientStatus>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.ClientStatus)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.ClientStatus)
    MergeFrom(*source);
  }
}

void ClientStatus::MergeFrom(const ClientStatus& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.ClientStatus)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_stats()) {
    mutable_stats()->::grpc::testing::ClientStats::MergeFrom(from.stats());
  }
}

void ClientStatus::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.ClientStatus)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ClientStatus::CopyFrom(const ClientStatus& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.ClientStatus)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ClientStatus::IsInitialized() const {
  return true;
}

void ClientStatus::Swap(ClientStatus* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ClientStatus::InternalSwap(ClientStatus* other) {
  using std::swap;
  swap(stats_, other->stats_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ClientStatus::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Mark::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Mark::kResetFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Mark::Mark()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsMark();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.Mark)
}
Mark::Mark(const Mark& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  reset_ = from.reset_;
  // @@protoc_insertion_point(copy_constructor:grpc.testing.Mark)
}

void Mark::SharedCtor() {
  reset_ = false;
  _cached_size_ = 0;
}

Mark::~Mark() {
  // @@protoc_insertion_point(destructor:grpc.testing.Mark)
  SharedDtor();
}

void Mark::SharedDtor() {
}

void Mark::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Mark::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Mark& Mark::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsMark();
  return *internal_default_instance();
}

Mark* Mark::New(::google::protobuf::Arena* arena) const {
  Mark* n = new Mark;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Mark::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.Mark)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  reset_ = false;
  _internal_metadata_.Clear();
}

bool Mark::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.Mark)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // bool reset = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &reset_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.Mark)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.Mark)
  return false;
#undef DO_
}

void Mark::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.Mark)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bool reset = 1;
  if (this->reset() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(1, this->reset(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.Mark)
}

::google::protobuf::uint8* Mark::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.Mark)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bool reset = 1;
  if (this->reset() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(1, this->reset(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.Mark)
  return target;
}

size_t Mark::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.Mark)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // bool reset = 1;
  if (this->reset() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Mark::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.Mark)
  GOOGLE_DCHECK_NE(&from, this);
  const Mark* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Mark>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.Mark)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.Mark)
    MergeFrom(*source);
  }
}

void Mark::MergeFrom(const Mark& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.Mark)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.reset() != 0) {
    set_reset(from.reset());
  }
}

void Mark::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.Mark)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Mark::CopyFrom(const Mark& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.Mark)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Mark::IsInitialized() const {
  return true;
}

void Mark::Swap(Mark* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Mark::InternalSwap(Mark* other) {
  using std::swap;
  swap(reset_, other->reset_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Mark::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ClientArgs::InitAsDefaultInstance() {
  ::grpc::testing::_ClientArgs_default_instance_.setup_ = const_cast< ::grpc::testing::ClientConfig*>(
      ::grpc::testing::ClientConfig::internal_default_instance());
  ::grpc::testing::_ClientArgs_default_instance_.mark_ = const_cast< ::grpc::testing::Mark*>(
      ::grpc::testing::Mark::internal_default_instance());
}
void ClientArgs::set_allocated_setup(::grpc::testing::ClientConfig* setup) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_argtype();
  if (setup) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      setup = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, setup, submessage_arena);
    }
    set_has_setup();
    argtype_.setup_ = setup;
  }
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.ClientArgs.setup)
}
void ClientArgs::set_allocated_mark(::grpc::testing::Mark* mark) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_argtype();
  if (mark) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      mark = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, mark, submessage_arena);
    }
    set_has_mark();
    argtype_.mark_ = mark;
  }
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.ClientArgs.mark)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ClientArgs::kSetupFieldNumber;
const int ClientArgs::kMarkFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ClientArgs::ClientArgs()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsClientArgs();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.ClientArgs)
}
ClientArgs::ClientArgs(const ClientArgs& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  clear_has_argtype();
  switch (from.argtype_case()) {
    case kSetup: {
      mutable_setup()->::grpc::testing::ClientConfig::MergeFrom(from.setup());
      break;
    }
    case kMark: {
      mutable_mark()->::grpc::testing::Mark::MergeFrom(from.mark());
      break;
    }
    case ARGTYPE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:grpc.testing.ClientArgs)
}

void ClientArgs::SharedCtor() {
  clear_has_argtype();
  _cached_size_ = 0;
}

ClientArgs::~ClientArgs() {
  // @@protoc_insertion_point(destructor:grpc.testing.ClientArgs)
  SharedDtor();
}

void ClientArgs::SharedDtor() {
  if (has_argtype()) {
    clear_argtype();
  }
}

void ClientArgs::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ClientArgs::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ClientArgs& ClientArgs::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsClientArgs();
  return *internal_default_instance();
}

ClientArgs* ClientArgs::New(::google::protobuf::Arena* arena) const {
  ClientArgs* n = new ClientArgs;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ClientArgs::clear_argtype() {
// @@protoc_insertion_point(one_of_clear_start:grpc.testing.ClientArgs)
  switch (argtype_case()) {
    case kSetup: {
      delete argtype_.setup_;
      break;
    }
    case kMark: {
      delete argtype_.mark_;
      break;
    }
    case ARGTYPE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = ARGTYPE_NOT_SET;
}


void ClientArgs::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.ClientArgs)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_argtype();
  _internal_metadata_.Clear();
}

bool ClientArgs::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.ClientArgs)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .grpc.testing.ClientConfig setup = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_setup()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.testing.Mark mark = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_mark()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.ClientArgs)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.ClientArgs)
  return false;
#undef DO_
}

void ClientArgs::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.ClientArgs)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.ClientConfig setup = 1;
  if (has_setup()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *argtype_.setup_, output);
  }

  // .grpc.testing.Mark mark = 2;
  if (has_mark()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *argtype_.mark_, output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.ClientArgs)
}

::google::protobuf::uint8* ClientArgs::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.ClientArgs)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.ClientConfig setup = 1;
  if (has_setup()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, *argtype_.setup_, deterministic, target);
  }

  // .grpc.testing.Mark mark = 2;
  if (has_mark()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, *argtype_.mark_, deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.ClientArgs)
  return target;
}

size_t ClientArgs::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.ClientArgs)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  switch (argtype_case()) {
    // .grpc.testing.ClientConfig setup = 1;
    case kSetup: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *argtype_.setup_);
      break;
    }
    // .grpc.testing.Mark mark = 2;
    case kMark: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *argtype_.mark_);
      break;
    }
    case ARGTYPE_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ClientArgs::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.ClientArgs)
  GOOGLE_DCHECK_NE(&from, this);
  const ClientArgs* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ClientArgs>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.ClientArgs)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.ClientArgs)
    MergeFrom(*source);
  }
}

void ClientArgs::MergeFrom(const ClientArgs& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.ClientArgs)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.argtype_case()) {
    case kSetup: {
      mutable_setup()->::grpc::testing::ClientConfig::MergeFrom(from.setup());
      break;
    }
    case kMark: {
      mutable_mark()->::grpc::testing::Mark::MergeFrom(from.mark());
      break;
    }
    case ARGTYPE_NOT_SET: {
      break;
    }
  }
}

void ClientArgs::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.ClientArgs)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ClientArgs::CopyFrom(const ClientArgs& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.ClientArgs)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ClientArgs::IsInitialized() const {
  return true;
}

void ClientArgs::Swap(ClientArgs* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ClientArgs::InternalSwap(ClientArgs* other) {
  using std::swap;
  swap(argtype_, other->argtype_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ClientArgs::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ServerConfig::InitAsDefaultInstance() {
  ::grpc::testing::_ServerConfig_default_instance_._instance.get_mutable()->security_params_ = const_cast< ::grpc::testing::SecurityParams*>(
      ::grpc::testing::SecurityParams::internal_default_instance());
  ::grpc::testing::_ServerConfig_default_instance_._instance.get_mutable()->payload_config_ = const_cast< ::grpc::testing::PayloadConfig*>(
      ::grpc::testing::PayloadConfig::internal_default_instance());
}
void ServerConfig::clear_payload_config() {
  if (GetArenaNoVirtual() == NULL && payload_config_ != NULL) {
    delete payload_config_;
  }
  payload_config_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ServerConfig::kServerTypeFieldNumber;
const int ServerConfig::kSecurityParamsFieldNumber;
const int ServerConfig::kPortFieldNumber;
const int ServerConfig::kAsyncServerThreadsFieldNumber;
const int ServerConfig::kCoreLimitFieldNumber;
const int ServerConfig::kPayloadConfigFieldNumber;
const int ServerConfig::kCoreListFieldNumber;
const int ServerConfig::kOtherServerApiFieldNumber;
const int ServerConfig::kThreadsPerCqFieldNumber;
const int ServerConfig::kResourceQuotaSizeFieldNumber;
const int ServerConfig::kChannelArgsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ServerConfig::ServerConfig()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsServerConfig();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.ServerConfig)
}
ServerConfig::ServerConfig(const ServerConfig& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      core_list_(from.core_list_),
      channel_args_(from.channel_args_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  other_server_api_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.other_server_api().size() > 0) {
    other_server_api_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.other_server_api_);
  }
  if (from.has_security_params()) {
    security_params_ = new ::grpc::testing::SecurityParams(*from.security_params_);
  } else {
    security_params_ = NULL;
  }
  if (from.has_payload_config()) {
    payload_config_ = new ::grpc::testing::PayloadConfig(*from.payload_config_);
  } else {
    payload_config_ = NULL;
  }
  ::memcpy(&server_type_, &from.server_type_,
    static_cast<size_t>(reinterpret_cast<char*>(&resource_quota_size_) -
    reinterpret_cast<char*>(&server_type_)) + sizeof(resource_quota_size_));
  // @@protoc_insertion_point(copy_constructor:grpc.testing.ServerConfig)
}

void ServerConfig::SharedCtor() {
  other_server_api_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&security_params_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&resource_quota_size_) -
      reinterpret_cast<char*>(&security_params_)) + sizeof(resource_quota_size_));
  _cached_size_ = 0;
}

ServerConfig::~ServerConfig() {
  // @@protoc_insertion_point(destructor:grpc.testing.ServerConfig)
  SharedDtor();
}

void ServerConfig::SharedDtor() {
  other_server_api_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete security_params_;
  if (this != internal_default_instance()) delete payload_config_;
}

void ServerConfig::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ServerConfig::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ServerConfig& ServerConfig::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsServerConfig();
  return *internal_default_instance();
}

ServerConfig* ServerConfig::New(::google::protobuf::Arena* arena) const {
  ServerConfig* n = new ServerConfig;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ServerConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.ServerConfig)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  core_list_.Clear();
  channel_args_.Clear();
  other_server_api_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && security_params_ != NULL) {
    delete security_params_;
  }
  security_params_ = NULL;
  if (GetArenaNoVirtual() == NULL && payload_config_ != NULL) {
    delete payload_config_;
  }
  payload_config_ = NULL;
  ::memset(&server_type_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&resource_quota_size_) -
      reinterpret_cast<char*>(&server_type_)) + sizeof(resource_quota_size_));
  _internal_metadata_.Clear();
}

bool ServerConfig::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.ServerConfig)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(16383u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .grpc.testing.ServerType server_type = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_server_type(static_cast< ::grpc::testing::ServerType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.testing.SecurityParams security_params = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_security_params()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 port = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &port_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 async_server_threads = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(56u /* 56 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &async_server_threads_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 core_limit = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(64u /* 64 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &core_limit_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.testing.PayloadConfig payload_config = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(74u /* 74 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_payload_config()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated int32 core_list = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(82u /* 82 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_core_list())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(80u /* 80 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 1, 82u, input, this->mutable_core_list())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string other_server_api = 11;
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(90u /* 90 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_other_server_api()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->other_server_api().data(), static_cast<int>(this->other_server_api().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.testing.ServerConfig.other_server_api"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 threads_per_cq = 12;
      case 12: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(96u /* 96 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &threads_per_cq_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 resource_quota_size = 1001;
      case 1001: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(72u /* 8008 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &resource_quota_size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .grpc.testing.ChannelArg channel_args = 1002;
      case 1002: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(82u /* 8018 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(input, add_channel_args()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.ServerConfig)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.ServerConfig)
  return false;
#undef DO_
}

void ServerConfig::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.ServerConfig)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.ServerType server_type = 1;
  if (this->server_type() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->server_type(), output);
  }

  // .grpc.testing.SecurityParams security_params = 2;
  if (this->has_security_params()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->security_params_, output);
  }

  // int32 port = 4;
  if (this->port() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(4, this->port(), output);
  }

  // int32 async_server_threads = 7;
  if (this->async_server_threads() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(7, this->async_server_threads(), output);
  }

  // int32 core_limit = 8;
  if (this->core_limit() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(8, this->core_limit(), output);
  }

  // .grpc.testing.PayloadConfig payload_config = 9;
  if (this->has_payload_config()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, *this->payload_config_, output);
  }

  // repeated int32 core_list = 10;
  if (this->core_list_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(10, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _core_list_cached_byte_size_));
  }
  for (int i = 0, n = this->core_list_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32NoTag(
      this->core_list(i), output);
  }

  // string other_server_api = 11;
  if (this->other_server_api().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->other_server_api().data(), static_cast<int>(this->other_server_api().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.ServerConfig.other_server_api");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      11, this->other_server_api(), output);
  }

  // int32 threads_per_cq = 12;
  if (this->threads_per_cq() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(12, this->threads_per_cq(), output);
  }

  // int32 resource_quota_size = 1001;
  if (this->resource_quota_size() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1001, this->resource_quota_size(), output);
  }

  // repeated .grpc.testing.ChannelArg channel_args = 1002;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->channel_args_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1002, this->channel_args(static_cast<int>(i)), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.ServerConfig)
}

::google::protobuf::uint8* ServerConfig::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.ServerConfig)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.ServerType server_type = 1;
  if (this->server_type() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->server_type(), target);
  }

  // .grpc.testing.SecurityParams security_params = 2;
  if (this->has_security_params()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, *this->security_params_, deterministic, target);
  }

  // int32 port = 4;
  if (this->port() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(4, this->port(), target);
  }

  // int32 async_server_threads = 7;
  if (this->async_server_threads() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(7, this->async_server_threads(), target);
  }

  // int32 core_limit = 8;
  if (this->core_limit() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(8, this->core_limit(), target);
  }

  // .grpc.testing.PayloadConfig payload_config = 9;
  if (this->has_payload_config()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        9, *this->payload_config_, deterministic, target);
  }

  // repeated int32 core_list = 10;
  if (this->core_list_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      10,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _core_list_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt32NoTagToArray(this->core_list_, target);
  }

  // string other_server_api = 11;
  if (this->other_server_api().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->other_server_api().data(), static_cast<int>(this->other_server_api().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.ServerConfig.other_server_api");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        11, this->other_server_api(), target);
  }

  // int32 threads_per_cq = 12;
  if (this->threads_per_cq() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(12, this->threads_per_cq(), target);
  }

  // int32 resource_quota_size = 1001;
  if (this->resource_quota_size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1001, this->resource_quota_size(), target);
  }

  // repeated .grpc.testing.ChannelArg channel_args = 1002;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->channel_args_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1002, this->channel_args(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.ServerConfig)
  return target;
}

size_t ServerConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.ServerConfig)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated int32 core_list = 10;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int32Size(this->core_list_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _core_list_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated .grpc.testing.ChannelArg channel_args = 1002;
  {
    unsigned int count = static_cast<unsigned int>(this->channel_args_size());
    total_size += 2UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->channel_args(static_cast<int>(i)));
    }
  }

  // string other_server_api = 11;
  if (this->other_server_api().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->other_server_api());
  }

  // .grpc.testing.SecurityParams security_params = 2;
  if (this->has_security_params()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->security_params_);
  }

  // .grpc.testing.PayloadConfig payload_config = 9;
  if (this->has_payload_config()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->payload_config_);
  }

  // .grpc.testing.ServerType server_type = 1;
  if (this->server_type() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->server_type());
  }

  // int32 port = 4;
  if (this->port() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->port());
  }

  // int32 async_server_threads = 7;
  if (this->async_server_threads() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->async_server_threads());
  }

  // int32 core_limit = 8;
  if (this->core_limit() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->core_limit());
  }

  // int32 threads_per_cq = 12;
  if (this->threads_per_cq() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->threads_per_cq());
  }

  // int32 resource_quota_size = 1001;
  if (this->resource_quota_size() != 0) {
    total_size += 2 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->resource_quota_size());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ServerConfig::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.ServerConfig)
  GOOGLE_DCHECK_NE(&from, this);
  const ServerConfig* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ServerConfig>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.ServerConfig)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.ServerConfig)
    MergeFrom(*source);
  }
}

void ServerConfig::MergeFrom(const ServerConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.ServerConfig)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  core_list_.MergeFrom(from.core_list_);
  channel_args_.MergeFrom(from.channel_args_);
  if (from.other_server_api().size() > 0) {

    other_server_api_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.other_server_api_);
  }
  if (from.has_security_params()) {
    mutable_security_params()->::grpc::testing::SecurityParams::MergeFrom(from.security_params());
  }
  if (from.has_payload_config()) {
    mutable_payload_config()->::grpc::testing::PayloadConfig::MergeFrom(from.payload_config());
  }
  if (from.server_type() != 0) {
    set_server_type(from.server_type());
  }
  if (from.port() != 0) {
    set_port(from.port());
  }
  if (from.async_server_threads() != 0) {
    set_async_server_threads(from.async_server_threads());
  }
  if (from.core_limit() != 0) {
    set_core_limit(from.core_limit());
  }
  if (from.threads_per_cq() != 0) {
    set_threads_per_cq(from.threads_per_cq());
  }
  if (from.resource_quota_size() != 0) {
    set_resource_quota_size(from.resource_quota_size());
  }
}

void ServerConfig::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.ServerConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ServerConfig::CopyFrom(const ServerConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.ServerConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ServerConfig::IsInitialized() const {
  return true;
}

void ServerConfig::Swap(ServerConfig* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ServerConfig::InternalSwap(ServerConfig* other) {
  using std::swap;
  core_list_.InternalSwap(&other->core_list_);
  channel_args_.InternalSwap(&other->channel_args_);
  other_server_api_.Swap(&other->other_server_api_);
  swap(security_params_, other->security_params_);
  swap(payload_config_, other->payload_config_);
  swap(server_type_, other->server_type_);
  swap(port_, other->port_);
  swap(async_server_threads_, other->async_server_threads_);
  swap(core_limit_, other->core_limit_);
  swap(threads_per_cq_, other->threads_per_cq_);
  swap(resource_quota_size_, other->resource_quota_size_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ServerConfig::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ServerArgs::InitAsDefaultInstance() {
  ::grpc::testing::_ServerArgs_default_instance_.setup_ = const_cast< ::grpc::testing::ServerConfig*>(
      ::grpc::testing::ServerConfig::internal_default_instance());
  ::grpc::testing::_ServerArgs_default_instance_.mark_ = const_cast< ::grpc::testing::Mark*>(
      ::grpc::testing::Mark::internal_default_instance());
}
void ServerArgs::set_allocated_setup(::grpc::testing::ServerConfig* setup) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_argtype();
  if (setup) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      setup = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, setup, submessage_arena);
    }
    set_has_setup();
    argtype_.setup_ = setup;
  }
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.ServerArgs.setup)
}
void ServerArgs::set_allocated_mark(::grpc::testing::Mark* mark) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_argtype();
  if (mark) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      mark = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, mark, submessage_arena);
    }
    set_has_mark();
    argtype_.mark_ = mark;
  }
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.ServerArgs.mark)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ServerArgs::kSetupFieldNumber;
const int ServerArgs::kMarkFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ServerArgs::ServerArgs()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsServerArgs();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.ServerArgs)
}
ServerArgs::ServerArgs(const ServerArgs& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  clear_has_argtype();
  switch (from.argtype_case()) {
    case kSetup: {
      mutable_setup()->::grpc::testing::ServerConfig::MergeFrom(from.setup());
      break;
    }
    case kMark: {
      mutable_mark()->::grpc::testing::Mark::MergeFrom(from.mark());
      break;
    }
    case ARGTYPE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:grpc.testing.ServerArgs)
}

void ServerArgs::SharedCtor() {
  clear_has_argtype();
  _cached_size_ = 0;
}

ServerArgs::~ServerArgs() {
  // @@protoc_insertion_point(destructor:grpc.testing.ServerArgs)
  SharedDtor();
}

void ServerArgs::SharedDtor() {
  if (has_argtype()) {
    clear_argtype();
  }
}

void ServerArgs::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ServerArgs::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ServerArgs& ServerArgs::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsServerArgs();
  return *internal_default_instance();
}

ServerArgs* ServerArgs::New(::google::protobuf::Arena* arena) const {
  ServerArgs* n = new ServerArgs;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ServerArgs::clear_argtype() {
// @@protoc_insertion_point(one_of_clear_start:grpc.testing.ServerArgs)
  switch (argtype_case()) {
    case kSetup: {
      delete argtype_.setup_;
      break;
    }
    case kMark: {
      delete argtype_.mark_;
      break;
    }
    case ARGTYPE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = ARGTYPE_NOT_SET;
}


void ServerArgs::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.ServerArgs)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_argtype();
  _internal_metadata_.Clear();
}

bool ServerArgs::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.ServerArgs)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .grpc.testing.ServerConfig setup = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_setup()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.testing.Mark mark = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_mark()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.ServerArgs)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.ServerArgs)
  return false;
#undef DO_
}

void ServerArgs::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.ServerArgs)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.ServerConfig setup = 1;
  if (has_setup()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *argtype_.setup_, output);
  }

  // .grpc.testing.Mark mark = 2;
  if (has_mark()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *argtype_.mark_, output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.ServerArgs)
}

::google::protobuf::uint8* ServerArgs::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.ServerArgs)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.ServerConfig setup = 1;
  if (has_setup()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, *argtype_.setup_, deterministic, target);
  }

  // .grpc.testing.Mark mark = 2;
  if (has_mark()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, *argtype_.mark_, deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.ServerArgs)
  return target;
}

size_t ServerArgs::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.ServerArgs)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  switch (argtype_case()) {
    // .grpc.testing.ServerConfig setup = 1;
    case kSetup: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *argtype_.setup_);
      break;
    }
    // .grpc.testing.Mark mark = 2;
    case kMark: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *argtype_.mark_);
      break;
    }
    case ARGTYPE_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ServerArgs::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.ServerArgs)
  GOOGLE_DCHECK_NE(&from, this);
  const ServerArgs* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ServerArgs>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.ServerArgs)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.ServerArgs)
    MergeFrom(*source);
  }
}

void ServerArgs::MergeFrom(const ServerArgs& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.ServerArgs)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.argtype_case()) {
    case kSetup: {
      mutable_setup()->::grpc::testing::ServerConfig::MergeFrom(from.setup());
      break;
    }
    case kMark: {
      mutable_mark()->::grpc::testing::Mark::MergeFrom(from.mark());
      break;
    }
    case ARGTYPE_NOT_SET: {
      break;
    }
  }
}

void ServerArgs::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.ServerArgs)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ServerArgs::CopyFrom(const ServerArgs& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.ServerArgs)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ServerArgs::IsInitialized() const {
  return true;
}

void ServerArgs::Swap(ServerArgs* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ServerArgs::InternalSwap(ServerArgs* other) {
  using std::swap;
  swap(argtype_, other->argtype_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ServerArgs::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ServerStatus::InitAsDefaultInstance() {
  ::grpc::testing::_ServerStatus_default_instance_._instance.get_mutable()->stats_ = const_cast< ::grpc::testing::ServerStats*>(
      ::grpc::testing::ServerStats::internal_default_instance());
}
void ServerStatus::clear_stats() {
  if (GetArenaNoVirtual() == NULL && stats_ != NULL) {
    delete stats_;
  }
  stats_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ServerStatus::kStatsFieldNumber;
const int ServerStatus::kPortFieldNumber;
const int ServerStatus::kCoresFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ServerStatus::ServerStatus()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsServerStatus();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.ServerStatus)
}
ServerStatus::ServerStatus(const ServerStatus& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_stats()) {
    stats_ = new ::grpc::testing::ServerStats(*from.stats_);
  } else {
    stats_ = NULL;
  }
  ::memcpy(&port_, &from.port_,
    static_cast<size_t>(reinterpret_cast<char*>(&cores_) -
    reinterpret_cast<char*>(&port_)) + sizeof(cores_));
  // @@protoc_insertion_point(copy_constructor:grpc.testing.ServerStatus)
}

void ServerStatus::SharedCtor() {
  ::memset(&stats_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&cores_) -
      reinterpret_cast<char*>(&stats_)) + sizeof(cores_));
  _cached_size_ = 0;
}

ServerStatus::~ServerStatus() {
  // @@protoc_insertion_point(destructor:grpc.testing.ServerStatus)
  SharedDtor();
}

void ServerStatus::SharedDtor() {
  if (this != internal_default_instance()) delete stats_;
}

void ServerStatus::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ServerStatus::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ServerStatus& ServerStatus::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsServerStatus();
  return *internal_default_instance();
}

ServerStatus* ServerStatus::New(::google::protobuf::Arena* arena) const {
  ServerStatus* n = new ServerStatus;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ServerStatus::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.ServerStatus)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && stats_ != NULL) {
    delete stats_;
  }
  stats_ = NULL;
  ::memset(&port_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&cores_) -
      reinterpret_cast<char*>(&port_)) + sizeof(cores_));
  _internal_metadata_.Clear();
}

bool ServerStatus::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.ServerStatus)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .grpc.testing.ServerStats stats = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_stats()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 port = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &port_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 cores = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &cores_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.ServerStatus)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.ServerStatus)
  return false;
#undef DO_
}

void ServerStatus::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.ServerStatus)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.ServerStats stats = 1;
  if (this->has_stats()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->stats_, output);
  }

  // int32 port = 2;
  if (this->port() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->port(), output);
  }

  // int32 cores = 3;
  if (this->cores() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->cores(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.ServerStatus)
}

::google::protobuf::uint8* ServerStatus::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.ServerStatus)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.ServerStats stats = 1;
  if (this->has_stats()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, *this->stats_, deterministic, target);
  }

  // int32 port = 2;
  if (this->port() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->port(), target);
  }

  // int32 cores = 3;
  if (this->cores() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->cores(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.ServerStatus)
  return target;
}

size_t ServerStatus::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.ServerStatus)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .grpc.testing.ServerStats stats = 1;
  if (this->has_stats()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->stats_);
  }

  // int32 port = 2;
  if (this->port() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->port());
  }

  // int32 cores = 3;
  if (this->cores() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->cores());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ServerStatus::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.ServerStatus)
  GOOGLE_DCHECK_NE(&from, this);
  const ServerStatus* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ServerStatus>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.ServerStatus)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.ServerStatus)
    MergeFrom(*source);
  }
}

void ServerStatus::MergeFrom(const ServerStatus& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.ServerStatus)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_stats()) {
    mutable_stats()->::grpc::testing::ServerStats::MergeFrom(from.stats());
  }
  if (from.port() != 0) {
    set_port(from.port());
  }
  if (from.cores() != 0) {
    set_cores(from.cores());
  }
}

void ServerStatus::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.ServerStatus)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ServerStatus::CopyFrom(const ServerStatus& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.ServerStatus)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ServerStatus::IsInitialized() const {
  return true;
}

void ServerStatus::Swap(ServerStatus* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ServerStatus::InternalSwap(ServerStatus* other) {
  using std::swap;
  swap(stats_, other->stats_);
  swap(port_, other->port_);
  swap(cores_, other->cores_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ServerStatus::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void CoreRequest::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CoreRequest::CoreRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsCoreRequest();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.CoreRequest)
}
CoreRequest::CoreRequest(const CoreRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:grpc.testing.CoreRequest)
}

void CoreRequest::SharedCtor() {
  _cached_size_ = 0;
}

CoreRequest::~CoreRequest() {
  // @@protoc_insertion_point(destructor:grpc.testing.CoreRequest)
  SharedDtor();
}

void CoreRequest::SharedDtor() {
}

void CoreRequest::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* CoreRequest::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CoreRequest& CoreRequest::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsCoreRequest();
  return *internal_default_instance();
}

CoreRequest* CoreRequest::New(::google::protobuf::Arena* arena) const {
  CoreRequest* n = new CoreRequest;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void CoreRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.CoreRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_metadata_.Clear();
}

bool CoreRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.CoreRequest)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, _internal_metadata_.mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.CoreRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.CoreRequest)
  return false;
#undef DO_
}

void CoreRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.CoreRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.CoreRequest)
}

::google::protobuf::uint8* CoreRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.CoreRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.CoreRequest)
  return target;
}

size_t CoreRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.CoreRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void CoreRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.CoreRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const CoreRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CoreRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.CoreRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.CoreRequest)
    MergeFrom(*source);
  }
}

void CoreRequest::MergeFrom(const CoreRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.CoreRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

}

void CoreRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.CoreRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CoreRequest::CopyFrom(const CoreRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.CoreRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CoreRequest::IsInitialized() const {
  return true;
}

void CoreRequest::Swap(CoreRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void CoreRequest::InternalSwap(CoreRequest* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata CoreRequest::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void CoreResponse::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int CoreResponse::kCoresFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

CoreResponse::CoreResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsCoreResponse();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.CoreResponse)
}
CoreResponse::CoreResponse(const CoreResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  cores_ = from.cores_;
  // @@protoc_insertion_point(copy_constructor:grpc.testing.CoreResponse)
}

void CoreResponse::SharedCtor() {
  cores_ = 0;
  _cached_size_ = 0;
}

CoreResponse::~CoreResponse() {
  // @@protoc_insertion_point(destructor:grpc.testing.CoreResponse)
  SharedDtor();
}

void CoreResponse::SharedDtor() {
}

void CoreResponse::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* CoreResponse::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const CoreResponse& CoreResponse::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsCoreResponse();
  return *internal_default_instance();
}

CoreResponse* CoreResponse::New(::google::protobuf::Arena* arena) const {
  CoreResponse* n = new CoreResponse;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void CoreResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.CoreResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cores_ = 0;
  _internal_metadata_.Clear();
}

bool CoreResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.CoreResponse)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 cores = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &cores_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.CoreResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.CoreResponse)
  return false;
#undef DO_
}

void CoreResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.CoreResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 cores = 1;
  if (this->cores() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->cores(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.CoreResponse)
}

::google::protobuf::uint8* CoreResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.CoreResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 cores = 1;
  if (this->cores() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->cores(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.CoreResponse)
  return target;
}

size_t CoreResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.CoreResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int32 cores = 1;
  if (this->cores() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->cores());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void CoreResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.CoreResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const CoreResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const CoreResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.CoreResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.CoreResponse)
    MergeFrom(*source);
  }
}

void CoreResponse::MergeFrom(const CoreResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.CoreResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.cores() != 0) {
    set_cores(from.cores());
  }
}

void CoreResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.CoreResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void CoreResponse::CopyFrom(const CoreResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.CoreResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CoreResponse::IsInitialized() const {
  return true;
}

void CoreResponse::Swap(CoreResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void CoreResponse::InternalSwap(CoreResponse* other) {
  using std::swap;
  swap(cores_, other->cores_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata CoreResponse::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Void::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Void::Void()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsVoid();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.Void)
}
Void::Void(const Void& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:grpc.testing.Void)
}

void Void::SharedCtor() {
  _cached_size_ = 0;
}

Void::~Void() {
  // @@protoc_insertion_point(destructor:grpc.testing.Void)
  SharedDtor();
}

void Void::SharedDtor() {
}

void Void::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Void::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Void& Void::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsVoid();
  return *internal_default_instance();
}

Void* Void::New(::google::protobuf::Arena* arena) const {
  Void* n = new Void;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Void::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.Void)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_metadata_.Clear();
}

bool Void::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.Void)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, _internal_metadata_.mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.Void)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.Void)
  return false;
#undef DO_
}

void Void::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.Void)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.Void)
}

::google::protobuf::uint8* Void::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.Void)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.Void)
  return target;
}

size_t Void::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.Void)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Void::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.Void)
  GOOGLE_DCHECK_NE(&from, this);
  const Void* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Void>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.Void)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.Void)
    MergeFrom(*source);
  }
}

void Void::MergeFrom(const Void& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.Void)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

}

void Void::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.Void)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Void::CopyFrom(const Void& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.Void)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Void::IsInitialized() const {
  return true;
}

void Void::Swap(Void* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Void::InternalSwap(Void* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Void::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Scenario::InitAsDefaultInstance() {
  ::grpc::testing::_Scenario_default_instance_._instance.get_mutable()->client_config_ = const_cast< ::grpc::testing::ClientConfig*>(
      ::grpc::testing::ClientConfig::internal_default_instance());
  ::grpc::testing::_Scenario_default_instance_._instance.get_mutable()->server_config_ = const_cast< ::grpc::testing::ServerConfig*>(
      ::grpc::testing::ServerConfig::internal_default_instance());
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Scenario::kNameFieldNumber;
const int Scenario::kClientConfigFieldNumber;
const int Scenario::kNumClientsFieldNumber;
const int Scenario::kServerConfigFieldNumber;
const int Scenario::kNumServersFieldNumber;
const int Scenario::kWarmupSecondsFieldNumber;
const int Scenario::kBenchmarkSecondsFieldNumber;
const int Scenario::kSpawnLocalWorkerCountFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Scenario::Scenario()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsScenario();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.Scenario)
}
Scenario::Scenario(const Scenario& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  if (from.has_client_config()) {
    client_config_ = new ::grpc::testing::ClientConfig(*from.client_config_);
  } else {
    client_config_ = NULL;
  }
  if (from.has_server_config()) {
    server_config_ = new ::grpc::testing::ServerConfig(*from.server_config_);
  } else {
    server_config_ = NULL;
  }
  ::memcpy(&num_clients_, &from.num_clients_,
    static_cast<size_t>(reinterpret_cast<char*>(&spawn_local_worker_count_) -
    reinterpret_cast<char*>(&num_clients_)) + sizeof(spawn_local_worker_count_));
  // @@protoc_insertion_point(copy_constructor:grpc.testing.Scenario)
}

void Scenario::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&client_config_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&spawn_local_worker_count_) -
      reinterpret_cast<char*>(&client_config_)) + sizeof(spawn_local_worker_count_));
  _cached_size_ = 0;
}

Scenario::~Scenario() {
  // @@protoc_insertion_point(destructor:grpc.testing.Scenario)
  SharedDtor();
}

void Scenario::SharedDtor() {
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete client_config_;
  if (this != internal_default_instance()) delete server_config_;
}

void Scenario::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Scenario::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Scenario& Scenario::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsScenario();
  return *internal_default_instance();
}

Scenario* Scenario::New(::google::protobuf::Arena* arena) const {
  Scenario* n = new Scenario;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Scenario::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.Scenario)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && client_config_ != NULL) {
    delete client_config_;
  }
  client_config_ = NULL;
  if (GetArenaNoVirtual() == NULL && server_config_ != NULL) {
    delete server_config_;
  }
  server_config_ = NULL;
  ::memset(&num_clients_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&spawn_local_worker_count_) -
      reinterpret_cast<char*>(&num_clients_)) + sizeof(spawn_local_worker_count_));
  _internal_metadata_.Clear();
}

bool Scenario::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.Scenario)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.testing.Scenario.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.testing.ClientConfig client_config = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_client_config()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 num_clients = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &num_clients_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.testing.ServerConfig server_config = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_server_config()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 num_servers = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &num_servers_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 warmup_seconds = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(48u /* 48 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &warmup_seconds_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 benchmark_seconds = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(56u /* 56 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &benchmark_seconds_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 spawn_local_worker_count = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(64u /* 64 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &spawn_local_worker_count_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.Scenario)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.Scenario)
  return false;
#undef DO_
}

void Scenario::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.Scenario)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.Scenario.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // .grpc.testing.ClientConfig client_config = 2;
  if (this->has_client_config()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->client_config_, output);
  }

  // int32 num_clients = 3;
  if (this->num_clients() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->num_clients(), output);
  }

  // .grpc.testing.ServerConfig server_config = 4;
  if (this->has_server_config()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, *this->server_config_, output);
  }

  // int32 num_servers = 5;
  if (this->num_servers() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(5, this->num_servers(), output);
  }

  // int32 warmup_seconds = 6;
  if (this->warmup_seconds() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(6, this->warmup_seconds(), output);
  }

  // int32 benchmark_seconds = 7;
  if (this->benchmark_seconds() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(7, this->benchmark_seconds(), output);
  }

  // int32 spawn_local_worker_count = 8;
  if (this->spawn_local_worker_count() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(8, this->spawn_local_worker_count(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.Scenario)
}

::google::protobuf::uint8* Scenario::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.Scenario)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.Scenario.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // .grpc.testing.ClientConfig client_config = 2;
  if (this->has_client_config()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, *this->client_config_, deterministic, target);
  }

  // int32 num_clients = 3;
  if (this->num_clients() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->num_clients(), target);
  }

  // .grpc.testing.ServerConfig server_config = 4;
  if (this->has_server_config()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, *this->server_config_, deterministic, target);
  }

  // int32 num_servers = 5;
  if (this->num_servers() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(5, this->num_servers(), target);
  }

  // int32 warmup_seconds = 6;
  if (this->warmup_seconds() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(6, this->warmup_seconds(), target);
  }

  // int32 benchmark_seconds = 7;
  if (this->benchmark_seconds() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(7, this->benchmark_seconds(), target);
  }

  // int32 spawn_local_worker_count = 8;
  if (this->spawn_local_worker_count() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(8, this->spawn_local_worker_count(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.Scenario)
  return target;
}

size_t Scenario::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.Scenario)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  // .grpc.testing.ClientConfig client_config = 2;
  if (this->has_client_config()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->client_config_);
  }

  // .grpc.testing.ServerConfig server_config = 4;
  if (this->has_server_config()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->server_config_);
  }

  // int32 num_clients = 3;
  if (this->num_clients() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->num_clients());
  }

  // int32 num_servers = 5;
  if (this->num_servers() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->num_servers());
  }

  // int32 warmup_seconds = 6;
  if (this->warmup_seconds() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->warmup_seconds());
  }

  // int32 benchmark_seconds = 7;
  if (this->benchmark_seconds() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->benchmark_seconds());
  }

  // int32 spawn_local_worker_count = 8;
  if (this->spawn_local_worker_count() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->spawn_local_worker_count());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Scenario::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.Scenario)
  GOOGLE_DCHECK_NE(&from, this);
  const Scenario* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Scenario>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.Scenario)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.Scenario)
    MergeFrom(*source);
  }
}

void Scenario::MergeFrom(const Scenario& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.Scenario)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.name().size() > 0) {

    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  if (from.has_client_config()) {
    mutable_client_config()->::grpc::testing::ClientConfig::MergeFrom(from.client_config());
  }
  if (from.has_server_config()) {
    mutable_server_config()->::grpc::testing::ServerConfig::MergeFrom(from.server_config());
  }
  if (from.num_clients() != 0) {
    set_num_clients(from.num_clients());
  }
  if (from.num_servers() != 0) {
    set_num_servers(from.num_servers());
  }
  if (from.warmup_seconds() != 0) {
    set_warmup_seconds(from.warmup_seconds());
  }
  if (from.benchmark_seconds() != 0) {
    set_benchmark_seconds(from.benchmark_seconds());
  }
  if (from.spawn_local_worker_count() != 0) {
    set_spawn_local_worker_count(from.spawn_local_worker_count());
  }
}

void Scenario::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.Scenario)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Scenario::CopyFrom(const Scenario& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.Scenario)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Scenario::IsInitialized() const {
  return true;
}

void Scenario::Swap(Scenario* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Scenario::InternalSwap(Scenario* other) {
  using std::swap;
  name_.Swap(&other->name_);
  swap(client_config_, other->client_config_);
  swap(server_config_, other->server_config_);
  swap(num_clients_, other->num_clients_);
  swap(num_servers_, other->num_servers_);
  swap(warmup_seconds_, other->warmup_seconds_);
  swap(benchmark_seconds_, other->benchmark_seconds_);
  swap(spawn_local_worker_count_, other->spawn_local_worker_count_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Scenario::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Scenarios::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Scenarios::kScenariosFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Scenarios::Scenarios()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsScenarios();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.Scenarios)
}
Scenarios::Scenarios(const Scenarios& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      scenarios_(from.scenarios_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:grpc.testing.Scenarios)
}

void Scenarios::SharedCtor() {
  _cached_size_ = 0;
}

Scenarios::~Scenarios() {
  // @@protoc_insertion_point(destructor:grpc.testing.Scenarios)
  SharedDtor();
}

void Scenarios::SharedDtor() {
}

void Scenarios::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Scenarios::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Scenarios& Scenarios::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsScenarios();
  return *internal_default_instance();
}

Scenarios* Scenarios::New(::google::protobuf::Arena* arena) const {
  Scenarios* n = new Scenarios;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Scenarios::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.Scenarios)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  scenarios_.Clear();
  _internal_metadata_.Clear();
}

bool Scenarios::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.Scenarios)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .grpc.testing.Scenario scenarios = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(input, add_scenarios()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.Scenarios)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.Scenarios)
  return false;
#undef DO_
}

void Scenarios::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.Scenarios)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .grpc.testing.Scenario scenarios = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->scenarios_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->scenarios(static_cast<int>(i)), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.Scenarios)
}

::google::protobuf::uint8* Scenarios::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.Scenarios)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .grpc.testing.Scenario scenarios = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->scenarios_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->scenarios(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.Scenarios)
  return target;
}

size_t Scenarios::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.Scenarios)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .grpc.testing.Scenario scenarios = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->scenarios_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->scenarios(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Scenarios::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.Scenarios)
  GOOGLE_DCHECK_NE(&from, this);
  const Scenarios* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Scenarios>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.Scenarios)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.Scenarios)
    MergeFrom(*source);
  }
}

void Scenarios::MergeFrom(const Scenarios& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.Scenarios)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  scenarios_.MergeFrom(from.scenarios_);
}

void Scenarios::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.Scenarios)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Scenarios::CopyFrom(const Scenarios& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.Scenarios)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Scenarios::IsInitialized() const {
  return true;
}

void Scenarios::Swap(Scenarios* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Scenarios::InternalSwap(Scenarios* other) {
  using std::swap;
  scenarios_.InternalSwap(&other->scenarios_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Scenarios::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ScenarioResultSummary::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ScenarioResultSummary::kQpsFieldNumber;
const int ScenarioResultSummary::kQpsPerServerCoreFieldNumber;
const int ScenarioResultSummary::kServerSystemTimeFieldNumber;
const int ScenarioResultSummary::kServerUserTimeFieldNumber;
const int ScenarioResultSummary::kClientSystemTimeFieldNumber;
const int ScenarioResultSummary::kClientUserTimeFieldNumber;
const int ScenarioResultSummary::kLatency50FieldNumber;
const int ScenarioResultSummary::kLatency90FieldNumber;
const int ScenarioResultSummary::kLatency95FieldNumber;
const int ScenarioResultSummary::kLatency99FieldNumber;
const int ScenarioResultSummary::kLatency999FieldNumber;
const int ScenarioResultSummary::kServerCpuUsageFieldNumber;
const int ScenarioResultSummary::kSuccessfulRequestsPerSecondFieldNumber;
const int ScenarioResultSummary::kFailedRequestsPerSecondFieldNumber;
const int ScenarioResultSummary::kClientPollsPerRequestFieldNumber;
const int ScenarioResultSummary::kServerPollsPerRequestFieldNumber;
const int ScenarioResultSummary::kServerQueriesPerCpuSecFieldNumber;
const int ScenarioResultSummary::kClientQueriesPerCpuSecFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ScenarioResultSummary::ScenarioResultSummary()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsScenarioResultSummary();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.ScenarioResultSummary)
}
ScenarioResultSummary::ScenarioResultSummary(const ScenarioResultSummary& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&qps_, &from.qps_,
    static_cast<size_t>(reinterpret_cast<char*>(&client_queries_per_cpu_sec_) -
    reinterpret_cast<char*>(&qps_)) + sizeof(client_queries_per_cpu_sec_));
  // @@protoc_insertion_point(copy_constructor:grpc.testing.ScenarioResultSummary)
}

void ScenarioResultSummary::SharedCtor() {
  ::memset(&qps_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&client_queries_per_cpu_sec_) -
      reinterpret_cast<char*>(&qps_)) + sizeof(client_queries_per_cpu_sec_));
  _cached_size_ = 0;
}

ScenarioResultSummary::~ScenarioResultSummary() {
  // @@protoc_insertion_point(destructor:grpc.testing.ScenarioResultSummary)
  SharedDtor();
}

void ScenarioResultSummary::SharedDtor() {
}

void ScenarioResultSummary::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ScenarioResultSummary::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ScenarioResultSummary& ScenarioResultSummary::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsScenarioResultSummary();
  return *internal_default_instance();
}

ScenarioResultSummary* ScenarioResultSummary::New(::google::protobuf::Arena* arena) const {
  ScenarioResultSummary* n = new ScenarioResultSummary;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ScenarioResultSummary::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.ScenarioResultSummary)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&qps_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&client_queries_per_cpu_sec_) -
      reinterpret_cast<char*>(&qps_)) + sizeof(client_queries_per_cpu_sec_));
  _internal_metadata_.Clear();
}

bool ScenarioResultSummary::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.ScenarioResultSummary)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(16383u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // double qps = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(9u /* 9 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &qps_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double qps_per_server_core = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(17u /* 17 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &qps_per_server_core_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double server_system_time = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(25u /* 25 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &server_system_time_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double server_user_time = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(33u /* 33 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &server_user_time_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double client_system_time = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(41u /* 41 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &client_system_time_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double client_user_time = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(49u /* 49 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &client_user_time_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double latency_50 = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(57u /* 57 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &latency_50_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double latency_90 = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(65u /* 65 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &latency_90_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double latency_95 = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(73u /* 73 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &latency_95_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double latency_99 = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(81u /* 81 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &latency_99_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double latency_999 = 11;
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(89u /* 89 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &latency_999_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double server_cpu_usage = 12;
      case 12: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(97u /* 97 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &server_cpu_usage_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double successful_requests_per_second = 13;
      case 13: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(105u /* 105 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &successful_requests_per_second_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double failed_requests_per_second = 14;
      case 14: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(113u /* 113 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &failed_requests_per_second_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double client_polls_per_request = 15;
      case 15: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(121u /* 121 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &client_polls_per_request_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double server_polls_per_request = 16;
      case 16: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(129u /* 129 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &server_polls_per_request_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double server_queries_per_cpu_sec = 17;
      case 17: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(137u /* 137 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &server_queries_per_cpu_sec_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double client_queries_per_cpu_sec = 18;
      case 18: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(145u /* 145 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &client_queries_per_cpu_sec_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.ScenarioResultSummary)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.ScenarioResultSummary)
  return false;
#undef DO_
}

void ScenarioResultSummary::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.ScenarioResultSummary)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double qps = 1;
  if (this->qps() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(1, this->qps(), output);
  }

  // double qps_per_server_core = 2;
  if (this->qps_per_server_core() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(2, this->qps_per_server_core(), output);
  }

  // double server_system_time = 3;
  if (this->server_system_time() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(3, this->server_system_time(), output);
  }

  // double server_user_time = 4;
  if (this->server_user_time() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(4, this->server_user_time(), output);
  }

  // double client_system_time = 5;
  if (this->client_system_time() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(5, this->client_system_time(), output);
  }

  // double client_user_time = 6;
  if (this->client_user_time() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(6, this->client_user_time(), output);
  }

  // double latency_50 = 7;
  if (this->latency_50() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(7, this->latency_50(), output);
  }

  // double latency_90 = 8;
  if (this->latency_90() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(8, this->latency_90(), output);
  }

  // double latency_95 = 9;
  if (this->latency_95() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(9, this->latency_95(), output);
  }

  // double latency_99 = 10;
  if (this->latency_99() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(10, this->latency_99(), output);
  }

  // double latency_999 = 11;
  if (this->latency_999() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(11, this->latency_999(), output);
  }

  // double server_cpu_usage = 12;
  if (this->server_cpu_usage() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(12, this->server_cpu_usage(), output);
  }

  // double successful_requests_per_second = 13;
  if (this->successful_requests_per_second() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(13, this->successful_requests_per_second(), output);
  }

  // double failed_requests_per_second = 14;
  if (this->failed_requests_per_second() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(14, this->failed_requests_per_second(), output);
  }

  // double client_polls_per_request = 15;
  if (this->client_polls_per_request() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(15, this->client_polls_per_request(), output);
  }

  // double server_polls_per_request = 16;
  if (this->server_polls_per_request() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(16, this->server_polls_per_request(), output);
  }

  // double server_queries_per_cpu_sec = 17;
  if (this->server_queries_per_cpu_sec() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(17, this->server_queries_per_cpu_sec(), output);
  }

  // double client_queries_per_cpu_sec = 18;
  if (this->client_queries_per_cpu_sec() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(18, this->client_queries_per_cpu_sec(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.ScenarioResultSummary)
}

::google::protobuf::uint8* ScenarioResultSummary::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.ScenarioResultSummary)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double qps = 1;
  if (this->qps() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(1, this->qps(), target);
  }

  // double qps_per_server_core = 2;
  if (this->qps_per_server_core() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(2, this->qps_per_server_core(), target);
  }

  // double server_system_time = 3;
  if (this->server_system_time() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(3, this->server_system_time(), target);
  }

  // double server_user_time = 4;
  if (this->server_user_time() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(4, this->server_user_time(), target);
  }

  // double client_system_time = 5;
  if (this->client_system_time() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(5, this->client_system_time(), target);
  }

  // double client_user_time = 6;
  if (this->client_user_time() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(6, this->client_user_time(), target);
  }

  // double latency_50 = 7;
  if (this->latency_50() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(7, this->latency_50(), target);
  }

  // double latency_90 = 8;
  if (this->latency_90() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(8, this->latency_90(), target);
  }

  // double latency_95 = 9;
  if (this->latency_95() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(9, this->latency_95(), target);
  }

  // double latency_99 = 10;
  if (this->latency_99() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(10, this->latency_99(), target);
  }

  // double latency_999 = 11;
  if (this->latency_999() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(11, this->latency_999(), target);
  }

  // double server_cpu_usage = 12;
  if (this->server_cpu_usage() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(12, this->server_cpu_usage(), target);
  }

  // double successful_requests_per_second = 13;
  if (this->successful_requests_per_second() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(13, this->successful_requests_per_second(), target);
  }

  // double failed_requests_per_second = 14;
  if (this->failed_requests_per_second() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(14, this->failed_requests_per_second(), target);
  }

  // double client_polls_per_request = 15;
  if (this->client_polls_per_request() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(15, this->client_polls_per_request(), target);
  }

  // double server_polls_per_request = 16;
  if (this->server_polls_per_request() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(16, this->server_polls_per_request(), target);
  }

  // double server_queries_per_cpu_sec = 17;
  if (this->server_queries_per_cpu_sec() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(17, this->server_queries_per_cpu_sec(), target);
  }

  // double client_queries_per_cpu_sec = 18;
  if (this->client_queries_per_cpu_sec() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(18, this->client_queries_per_cpu_sec(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.ScenarioResultSummary)
  return target;
}

size_t ScenarioResultSummary::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.ScenarioResultSummary)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // double qps = 1;
  if (this->qps() != 0) {
    total_size += 1 + 8;
  }

  // double qps_per_server_core = 2;
  if (this->qps_per_server_core() != 0) {
    total_size += 1 + 8;
  }

  // double server_system_time = 3;
  if (this->server_system_time() != 0) {
    total_size += 1 + 8;
  }

  // double server_user_time = 4;
  if (this->server_user_time() != 0) {
    total_size += 1 + 8;
  }

  // double client_system_time = 5;
  if (this->client_system_time() != 0) {
    total_size += 1 + 8;
  }

  // double client_user_time = 6;
  if (this->client_user_time() != 0) {
    total_size += 1 + 8;
  }

  // double latency_50 = 7;
  if (this->latency_50() != 0) {
    total_size += 1 + 8;
  }

  // double latency_90 = 8;
  if (this->latency_90() != 0) {
    total_size += 1 + 8;
  }

  // double latency_95 = 9;
  if (this->latency_95() != 0) {
    total_size += 1 + 8;
  }

  // double latency_99 = 10;
  if (this->latency_99() != 0) {
    total_size += 1 + 8;
  }

  // double latency_999 = 11;
  if (this->latency_999() != 0) {
    total_size += 1 + 8;
  }

  // double server_cpu_usage = 12;
  if (this->server_cpu_usage() != 0) {
    total_size += 1 + 8;
  }

  // double successful_requests_per_second = 13;
  if (this->successful_requests_per_second() != 0) {
    total_size += 1 + 8;
  }

  // double failed_requests_per_second = 14;
  if (this->failed_requests_per_second() != 0) {
    total_size += 1 + 8;
  }

  // double client_polls_per_request = 15;
  if (this->client_polls_per_request() != 0) {
    total_size += 1 + 8;
  }

  // double server_polls_per_request = 16;
  if (this->server_polls_per_request() != 0) {
    total_size += 2 + 8;
  }

  // double server_queries_per_cpu_sec = 17;
  if (this->server_queries_per_cpu_sec() != 0) {
    total_size += 2 + 8;
  }

  // double client_queries_per_cpu_sec = 18;
  if (this->client_queries_per_cpu_sec() != 0) {
    total_size += 2 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ScenarioResultSummary::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.ScenarioResultSummary)
  GOOGLE_DCHECK_NE(&from, this);
  const ScenarioResultSummary* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ScenarioResultSummary>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.ScenarioResultSummary)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.ScenarioResultSummary)
    MergeFrom(*source);
  }
}

void ScenarioResultSummary::MergeFrom(const ScenarioResultSummary& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.ScenarioResultSummary)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.qps() != 0) {
    set_qps(from.qps());
  }
  if (from.qps_per_server_core() != 0) {
    set_qps_per_server_core(from.qps_per_server_core());
  }
  if (from.server_system_time() != 0) {
    set_server_system_time(from.server_system_time());
  }
  if (from.server_user_time() != 0) {
    set_server_user_time(from.server_user_time());
  }
  if (from.client_system_time() != 0) {
    set_client_system_time(from.client_system_time());
  }
  if (from.client_user_time() != 0) {
    set_client_user_time(from.client_user_time());
  }
  if (from.latency_50() != 0) {
    set_latency_50(from.latency_50());
  }
  if (from.latency_90() != 0) {
    set_latency_90(from.latency_90());
  }
  if (from.latency_95() != 0) {
    set_latency_95(from.latency_95());
  }
  if (from.latency_99() != 0) {
    set_latency_99(from.latency_99());
  }
  if (from.latency_999() != 0) {
    set_latency_999(from.latency_999());
  }
  if (from.server_cpu_usage() != 0) {
    set_server_cpu_usage(from.server_cpu_usage());
  }
  if (from.successful_requests_per_second() != 0) {
    set_successful_requests_per_second(from.successful_requests_per_second());
  }
  if (from.failed_requests_per_second() != 0) {
    set_failed_requests_per_second(from.failed_requests_per_second());
  }
  if (from.client_polls_per_request() != 0) {
    set_client_polls_per_request(from.client_polls_per_request());
  }
  if (from.server_polls_per_request() != 0) {
    set_server_polls_per_request(from.server_polls_per_request());
  }
  if (from.server_queries_per_cpu_sec() != 0) {
    set_server_queries_per_cpu_sec(from.server_queries_per_cpu_sec());
  }
  if (from.client_queries_per_cpu_sec() != 0) {
    set_client_queries_per_cpu_sec(from.client_queries_per_cpu_sec());
  }
}

void ScenarioResultSummary::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.ScenarioResultSummary)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ScenarioResultSummary::CopyFrom(const ScenarioResultSummary& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.ScenarioResultSummary)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ScenarioResultSummary::IsInitialized() const {
  return true;
}

void ScenarioResultSummary::Swap(ScenarioResultSummary* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ScenarioResultSummary::InternalSwap(ScenarioResultSummary* other) {
  using std::swap;
  swap(qps_, other->qps_);
  swap(qps_per_server_core_, other->qps_per_server_core_);
  swap(server_system_time_, other->server_system_time_);
  swap(server_user_time_, other->server_user_time_);
  swap(client_system_time_, other->client_system_time_);
  swap(client_user_time_, other->client_user_time_);
  swap(latency_50_, other->latency_50_);
  swap(latency_90_, other->latency_90_);
  swap(latency_95_, other->latency_95_);
  swap(latency_99_, other->latency_99_);
  swap(latency_999_, other->latency_999_);
  swap(server_cpu_usage_, other->server_cpu_usage_);
  swap(successful_requests_per_second_, other->successful_requests_per_second_);
  swap(failed_requests_per_second_, other->failed_requests_per_second_);
  swap(client_polls_per_request_, other->client_polls_per_request_);
  swap(server_polls_per_request_, other->server_polls_per_request_);
  swap(server_queries_per_cpu_sec_, other->server_queries_per_cpu_sec_);
  swap(client_queries_per_cpu_sec_, other->client_queries_per_cpu_sec_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ScenarioResultSummary::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ScenarioResult::InitAsDefaultInstance() {
  ::grpc::testing::_ScenarioResult_default_instance_._instance.get_mutable()->scenario_ = const_cast< ::grpc::testing::Scenario*>(
      ::grpc::testing::Scenario::internal_default_instance());
  ::grpc::testing::_ScenarioResult_default_instance_._instance.get_mutable()->latencies_ = const_cast< ::grpc::testing::HistogramData*>(
      ::grpc::testing::HistogramData::internal_default_instance());
  ::grpc::testing::_ScenarioResult_default_instance_._instance.get_mutable()->summary_ = const_cast< ::grpc::testing::ScenarioResultSummary*>(
      ::grpc::testing::ScenarioResultSummary::internal_default_instance());
}
void ScenarioResult::clear_latencies() {
  if (GetArenaNoVirtual() == NULL && latencies_ != NULL) {
    delete latencies_;
  }
  latencies_ = NULL;
}
void ScenarioResult::clear_client_stats() {
  client_stats_.Clear();
}
void ScenarioResult::clear_server_stats() {
  server_stats_.Clear();
}
void ScenarioResult::clear_request_results() {
  request_results_.Clear();
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ScenarioResult::kScenarioFieldNumber;
const int ScenarioResult::kLatenciesFieldNumber;
const int ScenarioResult::kClientStatsFieldNumber;
const int ScenarioResult::kServerStatsFieldNumber;
const int ScenarioResult::kServerCoresFieldNumber;
const int ScenarioResult::kSummaryFieldNumber;
const int ScenarioResult::kClientSuccessFieldNumber;
const int ScenarioResult::kServerSuccessFieldNumber;
const int ScenarioResult::kRequestResultsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ScenarioResult::ScenarioResult()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsScenarioResult();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.ScenarioResult)
}
ScenarioResult::ScenarioResult(const ScenarioResult& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      client_stats_(from.client_stats_),
      server_stats_(from.server_stats_),
      server_cores_(from.server_cores_),
      client_success_(from.client_success_),
      server_success_(from.server_success_),
      request_results_(from.request_results_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_scenario()) {
    scenario_ = new ::grpc::testing::Scenario(*from.scenario_);
  } else {
    scenario_ = NULL;
  }
  if (from.has_latencies()) {
    latencies_ = new ::grpc::testing::HistogramData(*from.latencies_);
  } else {
    latencies_ = NULL;
  }
  if (from.has_summary()) {
    summary_ = new ::grpc::testing::ScenarioResultSummary(*from.summary_);
  } else {
    summary_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:grpc.testing.ScenarioResult)
}

void ScenarioResult::SharedCtor() {
  ::memset(&scenario_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&summary_) -
      reinterpret_cast<char*>(&scenario_)) + sizeof(summary_));
  _cached_size_ = 0;
}

ScenarioResult::~ScenarioResult() {
  // @@protoc_insertion_point(destructor:grpc.testing.ScenarioResult)
  SharedDtor();
}

void ScenarioResult::SharedDtor() {
  if (this != internal_default_instance()) delete scenario_;
  if (this != internal_default_instance()) delete latencies_;
  if (this != internal_default_instance()) delete summary_;
}

void ScenarioResult::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ScenarioResult::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ScenarioResult& ScenarioResult::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsScenarioResult();
  return *internal_default_instance();
}

ScenarioResult* ScenarioResult::New(::google::protobuf::Arena* arena) const {
  ScenarioResult* n = new ScenarioResult;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ScenarioResult::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.ScenarioResult)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  client_stats_.Clear();
  server_stats_.Clear();
  server_cores_.Clear();
  client_success_.Clear();
  server_success_.Clear();
  request_results_.Clear();
  if (GetArenaNoVirtual() == NULL && scenario_ != NULL) {
    delete scenario_;
  }
  scenario_ = NULL;
  if (GetArenaNoVirtual() == NULL && latencies_ != NULL) {
    delete latencies_;
  }
  latencies_ = NULL;
  if (GetArenaNoVirtual() == NULL && summary_ != NULL) {
    delete summary_;
  }
  summary_ = NULL;
  _internal_metadata_.Clear();
}

bool ScenarioResult::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.ScenarioResult)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .grpc.testing.Scenario scenario = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_scenario()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.testing.HistogramData latencies = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_latencies()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .grpc.testing.ClientStats client_stats = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(input, add_client_stats()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .grpc.testing.ServerStats server_stats = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(input, add_server_stats()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated int32 server_cores = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_server_cores())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 1, 42u, input, this->mutable_server_cores())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.testing.ScenarioResultSummary summary = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_summary()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated bool client_success = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, this->mutable_client_success())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(56u /* 56 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 1, 58u, input, this->mutable_client_success())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated bool server_success = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(66u /* 66 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, this->mutable_server_success())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(64u /* 64 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 1, 66u, input, this->mutable_server_success())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .grpc.testing.RequestResultCount request_results = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(74u /* 74 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(input, add_request_results()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.ScenarioResult)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.ScenarioResult)
  return false;
#undef DO_
}

void ScenarioResult::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.ScenarioResult)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.Scenario scenario = 1;
  if (this->has_scenario()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->scenario_, output);
  }

  // .grpc.testing.HistogramData latencies = 2;
  if (this->has_latencies()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->latencies_, output);
  }

  // repeated .grpc.testing.ClientStats client_stats = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->client_stats_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, this->client_stats(static_cast<int>(i)), output);
  }

  // repeated .grpc.testing.ServerStats server_stats = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->server_stats_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      4, this->server_stats(static_cast<int>(i)), output);
  }

  // repeated int32 server_cores = 5;
  if (this->server_cores_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(5, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _server_cores_cached_byte_size_));
  }
  for (int i = 0, n = this->server_cores_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32NoTag(
      this->server_cores(i), output);
  }

  // .grpc.testing.ScenarioResultSummary summary = 6;
  if (this->has_summary()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, *this->summary_, output);
  }

  // repeated bool client_success = 7;
  if (this->client_success_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(7, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _client_success_cached_byte_size_));
    ::google::protobuf::internal::WireFormatLite::WriteBoolArray(
      this->client_success().data(), this->client_success_size(), output);
  }

  // repeated bool server_success = 8;
  if (this->server_success_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(8, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _server_success_cached_byte_size_));
    ::google::protobuf::internal::WireFormatLite::WriteBoolArray(
      this->server_success().data(), this->server_success_size(), output);
  }

  // repeated .grpc.testing.RequestResultCount request_results = 9;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->request_results_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      9, this->request_results(static_cast<int>(i)), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.ScenarioResult)
}

::google::protobuf::uint8* ScenarioResult::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.ScenarioResult)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.Scenario scenario = 1;
  if (this->has_scenario()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, *this->scenario_, deterministic, target);
  }

  // .grpc.testing.HistogramData latencies = 2;
  if (this->has_latencies()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, *this->latencies_, deterministic, target);
  }

  // repeated .grpc.testing.ClientStats client_stats = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->client_stats_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, this->client_stats(static_cast<int>(i)), deterministic, target);
  }

  // repeated .grpc.testing.ServerStats server_stats = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->server_stats_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        4, this->server_stats(static_cast<int>(i)), deterministic, target);
  }

  // repeated int32 server_cores = 5;
  if (this->server_cores_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      5,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _server_cores_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt32NoTagToArray(this->server_cores_, target);
  }

  // .grpc.testing.ScenarioResultSummary summary = 6;
  if (this->has_summary()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        6, *this->summary_, deterministic, target);
  }

  // repeated bool client_success = 7;
  if (this->client_success_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      7,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _client_success_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteBoolNoTagToArray(this->client_success_, target);
  }

  // repeated bool server_success = 8;
  if (this->server_success_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      8,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _server_success_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteBoolNoTagToArray(this->server_success_, target);
  }

  // repeated .grpc.testing.RequestResultCount request_results = 9;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->request_results_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        9, this->request_results(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.ScenarioResult)
  return target;
}

size_t ScenarioResult::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.ScenarioResult)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .grpc.testing.ClientStats client_stats = 3;
  {
    unsigned int count = static_cast<unsigned int>(this->client_stats_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->client_stats(static_cast<int>(i)));
    }
  }

  // repeated .grpc.testing.ServerStats server_stats = 4;
  {
    unsigned int count = static_cast<unsigned int>(this->server_stats_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->server_stats(static_cast<int>(i)));
    }
  }

  // repeated int32 server_cores = 5;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int32Size(this->server_cores_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _server_cores_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated bool client_success = 7;
  {
    unsigned int count = static_cast<unsigned int>(this->client_success_size());
    size_t data_size = 1UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _client_success_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated bool server_success = 8;
  {
    unsigned int count = static_cast<unsigned int>(this->server_success_size());
    size_t data_size = 1UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _server_success_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // repeated .grpc.testing.RequestResultCount request_results = 9;
  {
    unsigned int count = static_cast<unsigned int>(this->request_results_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->request_results(static_cast<int>(i)));
    }
  }

  // .grpc.testing.Scenario scenario = 1;
  if (this->has_scenario()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->scenario_);
  }

  // .grpc.testing.HistogramData latencies = 2;
  if (this->has_latencies()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->latencies_);
  }

  // .grpc.testing.ScenarioResultSummary summary = 6;
  if (this->has_summary()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->summary_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ScenarioResult::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.ScenarioResult)
  GOOGLE_DCHECK_NE(&from, this);
  const ScenarioResult* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ScenarioResult>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.ScenarioResult)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.ScenarioResult)
    MergeFrom(*source);
  }
}

void ScenarioResult::MergeFrom(const ScenarioResult& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.ScenarioResult)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  client_stats_.MergeFrom(from.client_stats_);
  server_stats_.MergeFrom(from.server_stats_);
  server_cores_.MergeFrom(from.server_cores_);
  client_success_.MergeFrom(from.client_success_);
  server_success_.MergeFrom(from.server_success_);
  request_results_.MergeFrom(from.request_results_);
  if (from.has_scenario()) {
    mutable_scenario()->::grpc::testing::Scenario::MergeFrom(from.scenario());
  }
  if (from.has_latencies()) {
    mutable_latencies()->::grpc::testing::HistogramData::MergeFrom(from.latencies());
  }
  if (from.has_summary()) {
    mutable_summary()->::grpc::testing::ScenarioResultSummary::MergeFrom(from.summary());
  }
}

void ScenarioResult::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.ScenarioResult)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ScenarioResult::CopyFrom(const ScenarioResult& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.ScenarioResult)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ScenarioResult::IsInitialized() const {
  return true;
}

void ScenarioResult::Swap(ScenarioResult* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ScenarioResult::InternalSwap(ScenarioResult* other) {
  using std::swap;
  client_stats_.InternalSwap(&other->client_stats_);
  server_stats_.InternalSwap(&other->server_stats_);
  server_cores_.InternalSwap(&other->server_cores_);
  client_success_.InternalSwap(&other->client_success_);
  server_success_.InternalSwap(&other->server_success_);
  request_results_.InternalSwap(&other->request_results_);
  swap(scenario_, other->scenario_);
  swap(latencies_, other->latencies_);
  swap(summary_, other->summary_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ScenarioResult::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace testing
}  // namespace grpc

// @@protoc_insertion_point(global_scope)
