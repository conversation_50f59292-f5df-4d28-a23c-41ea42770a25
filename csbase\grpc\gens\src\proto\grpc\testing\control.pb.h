// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: src/proto/grpc/testing/control.proto

#ifndef PROTOBUF_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto__INCLUDED
#define PROTOBUF_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3005001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "src/proto/grpc/testing/payloads.pb.h"
#include "src/proto/grpc/testing/stats.pb.h"
// @@protoc_insertion_point(includes)

namespace protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[19];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
void InitDefaultsPoissonParamsImpl();
void InitDefaultsPoissonParams();
void InitDefaultsClosedLoopParamsImpl();
void InitDefaultsClosedLoopParams();
void InitDefaultsLoadParamsImpl();
void InitDefaultsLoadParams();
void InitDefaultsSecurityParamsImpl();
void InitDefaultsSecurityParams();
void InitDefaultsChannelArgImpl();
void InitDefaultsChannelArg();
void InitDefaultsClientConfigImpl();
void InitDefaultsClientConfig();
void InitDefaultsClientStatusImpl();
void InitDefaultsClientStatus();
void InitDefaultsMarkImpl();
void InitDefaultsMark();
void InitDefaultsClientArgsImpl();
void InitDefaultsClientArgs();
void InitDefaultsServerConfigImpl();
void InitDefaultsServerConfig();
void InitDefaultsServerArgsImpl();
void InitDefaultsServerArgs();
void InitDefaultsServerStatusImpl();
void InitDefaultsServerStatus();
void InitDefaultsCoreRequestImpl();
void InitDefaultsCoreRequest();
void InitDefaultsCoreResponseImpl();
void InitDefaultsCoreResponse();
void InitDefaultsVoidImpl();
void InitDefaultsVoid();
void InitDefaultsScenarioImpl();
void InitDefaultsScenario();
void InitDefaultsScenariosImpl();
void InitDefaultsScenarios();
void InitDefaultsScenarioResultSummaryImpl();
void InitDefaultsScenarioResultSummary();
void InitDefaultsScenarioResultImpl();
void InitDefaultsScenarioResult();
inline void InitDefaults() {
  InitDefaultsPoissonParams();
  InitDefaultsClosedLoopParams();
  InitDefaultsLoadParams();
  InitDefaultsSecurityParams();
  InitDefaultsChannelArg();
  InitDefaultsClientConfig();
  InitDefaultsClientStatus();
  InitDefaultsMark();
  InitDefaultsClientArgs();
  InitDefaultsServerConfig();
  InitDefaultsServerArgs();
  InitDefaultsServerStatus();
  InitDefaultsCoreRequest();
  InitDefaultsCoreResponse();
  InitDefaultsVoid();
  InitDefaultsScenario();
  InitDefaultsScenarios();
  InitDefaultsScenarioResultSummary();
  InitDefaultsScenarioResult();
}
}  // namespace protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto
namespace grpc {
namespace testing {
class ChannelArg;
class ChannelArgDefaultTypeInternal;
extern ChannelArgDefaultTypeInternal _ChannelArg_default_instance_;
class ClientArgs;
class ClientArgsDefaultTypeInternal;
extern ClientArgsDefaultTypeInternal _ClientArgs_default_instance_;
class ClientConfig;
class ClientConfigDefaultTypeInternal;
extern ClientConfigDefaultTypeInternal _ClientConfig_default_instance_;
class ClientStatus;
class ClientStatusDefaultTypeInternal;
extern ClientStatusDefaultTypeInternal _ClientStatus_default_instance_;
class ClosedLoopParams;
class ClosedLoopParamsDefaultTypeInternal;
extern ClosedLoopParamsDefaultTypeInternal _ClosedLoopParams_default_instance_;
class CoreRequest;
class CoreRequestDefaultTypeInternal;
extern CoreRequestDefaultTypeInternal _CoreRequest_default_instance_;
class CoreResponse;
class CoreResponseDefaultTypeInternal;
extern CoreResponseDefaultTypeInternal _CoreResponse_default_instance_;
class LoadParams;
class LoadParamsDefaultTypeInternal;
extern LoadParamsDefaultTypeInternal _LoadParams_default_instance_;
class Mark;
class MarkDefaultTypeInternal;
extern MarkDefaultTypeInternal _Mark_default_instance_;
class PoissonParams;
class PoissonParamsDefaultTypeInternal;
extern PoissonParamsDefaultTypeInternal _PoissonParams_default_instance_;
class Scenario;
class ScenarioDefaultTypeInternal;
extern ScenarioDefaultTypeInternal _Scenario_default_instance_;
class ScenarioResult;
class ScenarioResultDefaultTypeInternal;
extern ScenarioResultDefaultTypeInternal _ScenarioResult_default_instance_;
class ScenarioResultSummary;
class ScenarioResultSummaryDefaultTypeInternal;
extern ScenarioResultSummaryDefaultTypeInternal _ScenarioResultSummary_default_instance_;
class Scenarios;
class ScenariosDefaultTypeInternal;
extern ScenariosDefaultTypeInternal _Scenarios_default_instance_;
class SecurityParams;
class SecurityParamsDefaultTypeInternal;
extern SecurityParamsDefaultTypeInternal _SecurityParams_default_instance_;
class ServerArgs;
class ServerArgsDefaultTypeInternal;
extern ServerArgsDefaultTypeInternal _ServerArgs_default_instance_;
class ServerConfig;
class ServerConfigDefaultTypeInternal;
extern ServerConfigDefaultTypeInternal _ServerConfig_default_instance_;
class ServerStatus;
class ServerStatusDefaultTypeInternal;
extern ServerStatusDefaultTypeInternal _ServerStatus_default_instance_;
class Void;
class VoidDefaultTypeInternal;
extern VoidDefaultTypeInternal _Void_default_instance_;
}  // namespace testing
}  // namespace grpc
namespace grpc {
namespace testing {

enum ClientType {
  SYNC_CLIENT = 0,
  ASYNC_CLIENT = 1,
  OTHER_CLIENT = 2,
  ClientType_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  ClientType_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool ClientType_IsValid(int value);
const ClientType ClientType_MIN = SYNC_CLIENT;
const ClientType ClientType_MAX = OTHER_CLIENT;
const int ClientType_ARRAYSIZE = ClientType_MAX + 1;

const ::google::protobuf::EnumDescriptor* ClientType_descriptor();
inline const ::std::string& ClientType_Name(ClientType value) {
  return ::google::protobuf::internal::NameOfEnum(
    ClientType_descriptor(), value);
}
inline bool ClientType_Parse(
    const ::std::string& name, ClientType* value) {
  return ::google::protobuf::internal::ParseNamedEnum<ClientType>(
    ClientType_descriptor(), name, value);
}
enum ServerType {
  SYNC_SERVER = 0,
  ASYNC_SERVER = 1,
  ASYNC_GENERIC_SERVER = 2,
  OTHER_SERVER = 3,
  ServerType_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  ServerType_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool ServerType_IsValid(int value);
const ServerType ServerType_MIN = SYNC_SERVER;
const ServerType ServerType_MAX = OTHER_SERVER;
const int ServerType_ARRAYSIZE = ServerType_MAX + 1;

const ::google::protobuf::EnumDescriptor* ServerType_descriptor();
inline const ::std::string& ServerType_Name(ServerType value) {
  return ::google::protobuf::internal::NameOfEnum(
    ServerType_descriptor(), value);
}
inline bool ServerType_Parse(
    const ::std::string& name, ServerType* value) {
  return ::google::protobuf::internal::ParseNamedEnum<ServerType>(
    ServerType_descriptor(), name, value);
}
enum RpcType {
  UNARY = 0,
  STREAMING = 1,
  STREAMING_FROM_CLIENT = 2,
  STREAMING_FROM_SERVER = 3,
  STREAMING_BOTH_WAYS = 4,
  RpcType_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  RpcType_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool RpcType_IsValid(int value);
const RpcType RpcType_MIN = UNARY;
const RpcType RpcType_MAX = STREAMING_BOTH_WAYS;
const int RpcType_ARRAYSIZE = RpcType_MAX + 1;

const ::google::protobuf::EnumDescriptor* RpcType_descriptor();
inline const ::std::string& RpcType_Name(RpcType value) {
  return ::google::protobuf::internal::NameOfEnum(
    RpcType_descriptor(), value);
}
inline bool RpcType_Parse(
    const ::std::string& name, RpcType* value) {
  return ::google::protobuf::internal::ParseNamedEnum<RpcType>(
    RpcType_descriptor(), name, value);
}
// ===================================================================

class PoissonParams : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.PoissonParams) */ {
 public:
  PoissonParams();
  virtual ~PoissonParams();

  PoissonParams(const PoissonParams& from);

  inline PoissonParams& operator=(const PoissonParams& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  PoissonParams(PoissonParams&& from) noexcept
    : PoissonParams() {
    *this = ::std::move(from);
  }

  inline PoissonParams& operator=(PoissonParams&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const PoissonParams& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const PoissonParams* internal_default_instance() {
    return reinterpret_cast<const PoissonParams*>(
               &_PoissonParams_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    0;

  void Swap(PoissonParams* other);
  friend void swap(PoissonParams& a, PoissonParams& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline PoissonParams* New() const PROTOBUF_FINAL { return New(NULL); }

  PoissonParams* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const PoissonParams& from);
  void MergeFrom(const PoissonParams& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(PoissonParams* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // double offered_load = 1;
  void clear_offered_load();
  static const int kOfferedLoadFieldNumber = 1;
  double offered_load() const;
  void set_offered_load(double value);

  // @@protoc_insertion_point(class_scope:grpc.testing.PoissonParams)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  double offered_load_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsPoissonParamsImpl();
};
// -------------------------------------------------------------------

class ClosedLoopParams : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.ClosedLoopParams) */ {
 public:
  ClosedLoopParams();
  virtual ~ClosedLoopParams();

  ClosedLoopParams(const ClosedLoopParams& from);

  inline ClosedLoopParams& operator=(const ClosedLoopParams& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ClosedLoopParams(ClosedLoopParams&& from) noexcept
    : ClosedLoopParams() {
    *this = ::std::move(from);
  }

  inline ClosedLoopParams& operator=(ClosedLoopParams&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ClosedLoopParams& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ClosedLoopParams* internal_default_instance() {
    return reinterpret_cast<const ClosedLoopParams*>(
               &_ClosedLoopParams_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    1;

  void Swap(ClosedLoopParams* other);
  friend void swap(ClosedLoopParams& a, ClosedLoopParams& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ClosedLoopParams* New() const PROTOBUF_FINAL { return New(NULL); }

  ClosedLoopParams* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ClosedLoopParams& from);
  void MergeFrom(const ClosedLoopParams& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ClosedLoopParams* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:grpc.testing.ClosedLoopParams)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsClosedLoopParamsImpl();
};
// -------------------------------------------------------------------

class LoadParams : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.LoadParams) */ {
 public:
  LoadParams();
  virtual ~LoadParams();

  LoadParams(const LoadParams& from);

  inline LoadParams& operator=(const LoadParams& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  LoadParams(LoadParams&& from) noexcept
    : LoadParams() {
    *this = ::std::move(from);
  }

  inline LoadParams& operator=(LoadParams&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const LoadParams& default_instance();

  enum LoadCase {
    kClosedLoop = 1,
    kPoisson = 2,
    LOAD_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const LoadParams* internal_default_instance() {
    return reinterpret_cast<const LoadParams*>(
               &_LoadParams_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    2;

  void Swap(LoadParams* other);
  friend void swap(LoadParams& a, LoadParams& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline LoadParams* New() const PROTOBUF_FINAL { return New(NULL); }

  LoadParams* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const LoadParams& from);
  void MergeFrom(const LoadParams& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(LoadParams* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .grpc.testing.ClosedLoopParams closed_loop = 1;
  bool has_closed_loop() const;
  void clear_closed_loop();
  static const int kClosedLoopFieldNumber = 1;
  const ::grpc::testing::ClosedLoopParams& closed_loop() const;
  ::grpc::testing::ClosedLoopParams* release_closed_loop();
  ::grpc::testing::ClosedLoopParams* mutable_closed_loop();
  void set_allocated_closed_loop(::grpc::testing::ClosedLoopParams* closed_loop);

  // .grpc.testing.PoissonParams poisson = 2;
  bool has_poisson() const;
  void clear_poisson();
  static const int kPoissonFieldNumber = 2;
  const ::grpc::testing::PoissonParams& poisson() const;
  ::grpc::testing::PoissonParams* release_poisson();
  ::grpc::testing::PoissonParams* mutable_poisson();
  void set_allocated_poisson(::grpc::testing::PoissonParams* poisson);

  LoadCase load_case() const;
  // @@protoc_insertion_point(class_scope:grpc.testing.LoadParams)
 private:
  void set_has_closed_loop();
  void set_has_poisson();

  inline bool has_load() const;
  void clear_load();
  inline void clear_has_load();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  union LoadUnion {
    LoadUnion() {}
    ::grpc::testing::ClosedLoopParams* closed_loop_;
    ::grpc::testing::PoissonParams* poisson_;
  } load_;
  mutable int _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsLoadParamsImpl();
};
// -------------------------------------------------------------------

class SecurityParams : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.SecurityParams) */ {
 public:
  SecurityParams();
  virtual ~SecurityParams();

  SecurityParams(const SecurityParams& from);

  inline SecurityParams& operator=(const SecurityParams& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SecurityParams(SecurityParams&& from) noexcept
    : SecurityParams() {
    *this = ::std::move(from);
  }

  inline SecurityParams& operator=(SecurityParams&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const SecurityParams& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SecurityParams* internal_default_instance() {
    return reinterpret_cast<const SecurityParams*>(
               &_SecurityParams_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    3;

  void Swap(SecurityParams* other);
  friend void swap(SecurityParams& a, SecurityParams& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SecurityParams* New() const PROTOBUF_FINAL { return New(NULL); }

  SecurityParams* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const SecurityParams& from);
  void MergeFrom(const SecurityParams& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(SecurityParams* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string server_host_override = 2;
  void clear_server_host_override();
  static const int kServerHostOverrideFieldNumber = 2;
  const ::std::string& server_host_override() const;
  void set_server_host_override(const ::std::string& value);
  #if LANG_CXX11
  void set_server_host_override(::std::string&& value);
  #endif
  void set_server_host_override(const char* value);
  void set_server_host_override(const char* value, size_t size);
  ::std::string* mutable_server_host_override();
  ::std::string* release_server_host_override();
  void set_allocated_server_host_override(::std::string* server_host_override);

  // string cred_type = 3;
  void clear_cred_type();
  static const int kCredTypeFieldNumber = 3;
  const ::std::string& cred_type() const;
  void set_cred_type(const ::std::string& value);
  #if LANG_CXX11
  void set_cred_type(::std::string&& value);
  #endif
  void set_cred_type(const char* value);
  void set_cred_type(const char* value, size_t size);
  ::std::string* mutable_cred_type();
  ::std::string* release_cred_type();
  void set_allocated_cred_type(::std::string* cred_type);

  // bool use_test_ca = 1;
  void clear_use_test_ca();
  static const int kUseTestCaFieldNumber = 1;
  bool use_test_ca() const;
  void set_use_test_ca(bool value);

  // @@protoc_insertion_point(class_scope:grpc.testing.SecurityParams)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr server_host_override_;
  ::google::protobuf::internal::ArenaStringPtr cred_type_;
  bool use_test_ca_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsSecurityParamsImpl();
};
// -------------------------------------------------------------------

class ChannelArg : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.ChannelArg) */ {
 public:
  ChannelArg();
  virtual ~ChannelArg();

  ChannelArg(const ChannelArg& from);

  inline ChannelArg& operator=(const ChannelArg& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ChannelArg(ChannelArg&& from) noexcept
    : ChannelArg() {
    *this = ::std::move(from);
  }

  inline ChannelArg& operator=(ChannelArg&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ChannelArg& default_instance();

  enum ValueCase {
    kStrValue = 2,
    kIntValue = 3,
    VALUE_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ChannelArg* internal_default_instance() {
    return reinterpret_cast<const ChannelArg*>(
               &_ChannelArg_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    4;

  void Swap(ChannelArg* other);
  friend void swap(ChannelArg& a, ChannelArg& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ChannelArg* New() const PROTOBUF_FINAL { return New(NULL); }

  ChannelArg* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ChannelArg& from);
  void MergeFrom(const ChannelArg& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ChannelArg* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // string str_value = 2;
  private:
  bool has_str_value() const;
  public:
  void clear_str_value();
  static const int kStrValueFieldNumber = 2;
  const ::std::string& str_value() const;
  void set_str_value(const ::std::string& value);
  #if LANG_CXX11
  void set_str_value(::std::string&& value);
  #endif
  void set_str_value(const char* value);
  void set_str_value(const char* value, size_t size);
  ::std::string* mutable_str_value();
  ::std::string* release_str_value();
  void set_allocated_str_value(::std::string* str_value);

  // int32 int_value = 3;
  private:
  bool has_int_value() const;
  public:
  void clear_int_value();
  static const int kIntValueFieldNumber = 3;
  ::google::protobuf::int32 int_value() const;
  void set_int_value(::google::protobuf::int32 value);

  ValueCase value_case() const;
  // @@protoc_insertion_point(class_scope:grpc.testing.ChannelArg)
 private:
  void set_has_str_value();
  void set_has_int_value();

  inline bool has_value() const;
  void clear_value();
  inline void clear_has_value();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  union ValueUnion {
    ValueUnion() {}
    ::google::protobuf::internal::ArenaStringPtr str_value_;
    ::google::protobuf::int32 int_value_;
  } value_;
  mutable int _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsChannelArgImpl();
};
// -------------------------------------------------------------------

class ClientConfig : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.ClientConfig) */ {
 public:
  ClientConfig();
  virtual ~ClientConfig();

  ClientConfig(const ClientConfig& from);

  inline ClientConfig& operator=(const ClientConfig& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ClientConfig(ClientConfig&& from) noexcept
    : ClientConfig() {
    *this = ::std::move(from);
  }

  inline ClientConfig& operator=(ClientConfig&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ClientConfig& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ClientConfig* internal_default_instance() {
    return reinterpret_cast<const ClientConfig*>(
               &_ClientConfig_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    5;

  void Swap(ClientConfig* other);
  friend void swap(ClientConfig& a, ClientConfig& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ClientConfig* New() const PROTOBUF_FINAL { return New(NULL); }

  ClientConfig* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ClientConfig& from);
  void MergeFrom(const ClientConfig& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ClientConfig* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string server_targets = 1;
  int server_targets_size() const;
  void clear_server_targets();
  static const int kServerTargetsFieldNumber = 1;
  const ::std::string& server_targets(int index) const;
  ::std::string* mutable_server_targets(int index);
  void set_server_targets(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_server_targets(int index, ::std::string&& value);
  #endif
  void set_server_targets(int index, const char* value);
  void set_server_targets(int index, const char* value, size_t size);
  ::std::string* add_server_targets();
  void add_server_targets(const ::std::string& value);
  #if LANG_CXX11
  void add_server_targets(::std::string&& value);
  #endif
  void add_server_targets(const char* value);
  void add_server_targets(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& server_targets() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_server_targets();

  // repeated int32 core_list = 13;
  int core_list_size() const;
  void clear_core_list();
  static const int kCoreListFieldNumber = 13;
  ::google::protobuf::int32 core_list(int index) const;
  void set_core_list(int index, ::google::protobuf::int32 value);
  void add_core_list(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      core_list() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_core_list();

  // repeated .grpc.testing.ChannelArg channel_args = 16;
  int channel_args_size() const;
  void clear_channel_args();
  static const int kChannelArgsFieldNumber = 16;
  const ::grpc::testing::ChannelArg& channel_args(int index) const;
  ::grpc::testing::ChannelArg* mutable_channel_args(int index);
  ::grpc::testing::ChannelArg* add_channel_args();
  ::google::protobuf::RepeatedPtrField< ::grpc::testing::ChannelArg >*
      mutable_channel_args();
  const ::google::protobuf::RepeatedPtrField< ::grpc::testing::ChannelArg >&
      channel_args() const;

  // string other_client_api = 15;
  void clear_other_client_api();
  static const int kOtherClientApiFieldNumber = 15;
  const ::std::string& other_client_api() const;
  void set_other_client_api(const ::std::string& value);
  #if LANG_CXX11
  void set_other_client_api(::std::string&& value);
  #endif
  void set_other_client_api(const char* value);
  void set_other_client_api(const char* value, size_t size);
  ::std::string* mutable_other_client_api();
  ::std::string* release_other_client_api();
  void set_allocated_other_client_api(::std::string* other_client_api);

  // .grpc.testing.SecurityParams security_params = 3;
  bool has_security_params() const;
  void clear_security_params();
  static const int kSecurityParamsFieldNumber = 3;
  const ::grpc::testing::SecurityParams& security_params() const;
  ::grpc::testing::SecurityParams* release_security_params();
  ::grpc::testing::SecurityParams* mutable_security_params();
  void set_allocated_security_params(::grpc::testing::SecurityParams* security_params);

  // .grpc.testing.LoadParams load_params = 10;
  bool has_load_params() const;
  void clear_load_params();
  static const int kLoadParamsFieldNumber = 10;
  const ::grpc::testing::LoadParams& load_params() const;
  ::grpc::testing::LoadParams* release_load_params();
  ::grpc::testing::LoadParams* mutable_load_params();
  void set_allocated_load_params(::grpc::testing::LoadParams* load_params);

  // .grpc.testing.PayloadConfig payload_config = 11;
  bool has_payload_config() const;
  void clear_payload_config();
  static const int kPayloadConfigFieldNumber = 11;
  const ::grpc::testing::PayloadConfig& payload_config() const;
  ::grpc::testing::PayloadConfig* release_payload_config();
  ::grpc::testing::PayloadConfig* mutable_payload_config();
  void set_allocated_payload_config(::grpc::testing::PayloadConfig* payload_config);

  // .grpc.testing.HistogramParams histogram_params = 12;
  bool has_histogram_params() const;
  void clear_histogram_params();
  static const int kHistogramParamsFieldNumber = 12;
  const ::grpc::testing::HistogramParams& histogram_params() const;
  ::grpc::testing::HistogramParams* release_histogram_params();
  ::grpc::testing::HistogramParams* mutable_histogram_params();
  void set_allocated_histogram_params(::grpc::testing::HistogramParams* histogram_params);

  // .grpc.testing.ClientType client_type = 2;
  void clear_client_type();
  static const int kClientTypeFieldNumber = 2;
  ::grpc::testing::ClientType client_type() const;
  void set_client_type(::grpc::testing::ClientType value);

  // int32 outstanding_rpcs_per_channel = 4;
  void clear_outstanding_rpcs_per_channel();
  static const int kOutstandingRpcsPerChannelFieldNumber = 4;
  ::google::protobuf::int32 outstanding_rpcs_per_channel() const;
  void set_outstanding_rpcs_per_channel(::google::protobuf::int32 value);

  // int32 client_channels = 5;
  void clear_client_channels();
  static const int kClientChannelsFieldNumber = 5;
  ::google::protobuf::int32 client_channels() const;
  void set_client_channels(::google::protobuf::int32 value);

  // int32 async_client_threads = 7;
  void clear_async_client_threads();
  static const int kAsyncClientThreadsFieldNumber = 7;
  ::google::protobuf::int32 async_client_threads() const;
  void set_async_client_threads(::google::protobuf::int32 value);

  // .grpc.testing.RpcType rpc_type = 8;
  void clear_rpc_type();
  static const int kRpcTypeFieldNumber = 8;
  ::grpc::testing::RpcType rpc_type() const;
  void set_rpc_type(::grpc::testing::RpcType value);

  // int32 core_limit = 14;
  void clear_core_limit();
  static const int kCoreLimitFieldNumber = 14;
  ::google::protobuf::int32 core_limit() const;
  void set_core_limit(::google::protobuf::int32 value);

  // int32 threads_per_cq = 17;
  void clear_threads_per_cq();
  static const int kThreadsPerCqFieldNumber = 17;
  ::google::protobuf::int32 threads_per_cq() const;
  void set_threads_per_cq(::google::protobuf::int32 value);

  // int32 messages_per_stream = 18;
  void clear_messages_per_stream();
  static const int kMessagesPerStreamFieldNumber = 18;
  ::google::protobuf::int32 messages_per_stream() const;
  void set_messages_per_stream(::google::protobuf::int32 value);

  // bool use_coalesce_api = 19;
  void clear_use_coalesce_api();
  static const int kUseCoalesceApiFieldNumber = 19;
  bool use_coalesce_api() const;
  void set_use_coalesce_api(bool value);

  // @@protoc_insertion_point(class_scope:grpc.testing.ClientConfig)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::std::string> server_targets_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > core_list_;
  mutable int _core_list_cached_byte_size_;
  ::google::protobuf::RepeatedPtrField< ::grpc::testing::ChannelArg > channel_args_;
  ::google::protobuf::internal::ArenaStringPtr other_client_api_;
  ::grpc::testing::SecurityParams* security_params_;
  ::grpc::testing::LoadParams* load_params_;
  ::grpc::testing::PayloadConfig* payload_config_;
  ::grpc::testing::HistogramParams* histogram_params_;
  int client_type_;
  ::google::protobuf::int32 outstanding_rpcs_per_channel_;
  ::google::protobuf::int32 client_channels_;
  ::google::protobuf::int32 async_client_threads_;
  int rpc_type_;
  ::google::protobuf::int32 core_limit_;
  ::google::protobuf::int32 threads_per_cq_;
  ::google::protobuf::int32 messages_per_stream_;
  bool use_coalesce_api_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsClientConfigImpl();
};
// -------------------------------------------------------------------

class ClientStatus : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.ClientStatus) */ {
 public:
  ClientStatus();
  virtual ~ClientStatus();

  ClientStatus(const ClientStatus& from);

  inline ClientStatus& operator=(const ClientStatus& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ClientStatus(ClientStatus&& from) noexcept
    : ClientStatus() {
    *this = ::std::move(from);
  }

  inline ClientStatus& operator=(ClientStatus&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ClientStatus& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ClientStatus* internal_default_instance() {
    return reinterpret_cast<const ClientStatus*>(
               &_ClientStatus_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    6;

  void Swap(ClientStatus* other);
  friend void swap(ClientStatus& a, ClientStatus& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ClientStatus* New() const PROTOBUF_FINAL { return New(NULL); }

  ClientStatus* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ClientStatus& from);
  void MergeFrom(const ClientStatus& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ClientStatus* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .grpc.testing.ClientStats stats = 1;
  bool has_stats() const;
  void clear_stats();
  static const int kStatsFieldNumber = 1;
  const ::grpc::testing::ClientStats& stats() const;
  ::grpc::testing::ClientStats* release_stats();
  ::grpc::testing::ClientStats* mutable_stats();
  void set_allocated_stats(::grpc::testing::ClientStats* stats);

  // @@protoc_insertion_point(class_scope:grpc.testing.ClientStatus)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::grpc::testing::ClientStats* stats_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsClientStatusImpl();
};
// -------------------------------------------------------------------

class Mark : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.Mark) */ {
 public:
  Mark();
  virtual ~Mark();

  Mark(const Mark& from);

  inline Mark& operator=(const Mark& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Mark(Mark&& from) noexcept
    : Mark() {
    *this = ::std::move(from);
  }

  inline Mark& operator=(Mark&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Mark& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Mark* internal_default_instance() {
    return reinterpret_cast<const Mark*>(
               &_Mark_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    7;

  void Swap(Mark* other);
  friend void swap(Mark& a, Mark& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Mark* New() const PROTOBUF_FINAL { return New(NULL); }

  Mark* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const Mark& from);
  void MergeFrom(const Mark& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(Mark* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // bool reset = 1;
  void clear_reset();
  static const int kResetFieldNumber = 1;
  bool reset() const;
  void set_reset(bool value);

  // @@protoc_insertion_point(class_scope:grpc.testing.Mark)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  bool reset_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsMarkImpl();
};
// -------------------------------------------------------------------

class ClientArgs : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.ClientArgs) */ {
 public:
  ClientArgs();
  virtual ~ClientArgs();

  ClientArgs(const ClientArgs& from);

  inline ClientArgs& operator=(const ClientArgs& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ClientArgs(ClientArgs&& from) noexcept
    : ClientArgs() {
    *this = ::std::move(from);
  }

  inline ClientArgs& operator=(ClientArgs&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ClientArgs& default_instance();

  enum ArgtypeCase {
    kSetup = 1,
    kMark = 2,
    ARGTYPE_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ClientArgs* internal_default_instance() {
    return reinterpret_cast<const ClientArgs*>(
               &_ClientArgs_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    8;

  void Swap(ClientArgs* other);
  friend void swap(ClientArgs& a, ClientArgs& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ClientArgs* New() const PROTOBUF_FINAL { return New(NULL); }

  ClientArgs* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ClientArgs& from);
  void MergeFrom(const ClientArgs& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ClientArgs* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .grpc.testing.ClientConfig setup = 1;
  bool has_setup() const;
  void clear_setup();
  static const int kSetupFieldNumber = 1;
  const ::grpc::testing::ClientConfig& setup() const;
  ::grpc::testing::ClientConfig* release_setup();
  ::grpc::testing::ClientConfig* mutable_setup();
  void set_allocated_setup(::grpc::testing::ClientConfig* setup);

  // .grpc.testing.Mark mark = 2;
  bool has_mark() const;
  void clear_mark();
  static const int kMarkFieldNumber = 2;
  const ::grpc::testing::Mark& mark() const;
  ::grpc::testing::Mark* release_mark();
  ::grpc::testing::Mark* mutable_mark();
  void set_allocated_mark(::grpc::testing::Mark* mark);

  ArgtypeCase argtype_case() const;
  // @@protoc_insertion_point(class_scope:grpc.testing.ClientArgs)
 private:
  void set_has_setup();
  void set_has_mark();

  inline bool has_argtype() const;
  void clear_argtype();
  inline void clear_has_argtype();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  union ArgtypeUnion {
    ArgtypeUnion() {}
    ::grpc::testing::ClientConfig* setup_;
    ::grpc::testing::Mark* mark_;
  } argtype_;
  mutable int _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsClientArgsImpl();
};
// -------------------------------------------------------------------

class ServerConfig : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.ServerConfig) */ {
 public:
  ServerConfig();
  virtual ~ServerConfig();

  ServerConfig(const ServerConfig& from);

  inline ServerConfig& operator=(const ServerConfig& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ServerConfig(ServerConfig&& from) noexcept
    : ServerConfig() {
    *this = ::std::move(from);
  }

  inline ServerConfig& operator=(ServerConfig&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ServerConfig& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ServerConfig* internal_default_instance() {
    return reinterpret_cast<const ServerConfig*>(
               &_ServerConfig_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    9;

  void Swap(ServerConfig* other);
  friend void swap(ServerConfig& a, ServerConfig& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ServerConfig* New() const PROTOBUF_FINAL { return New(NULL); }

  ServerConfig* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ServerConfig& from);
  void MergeFrom(const ServerConfig& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ServerConfig* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int32 core_list = 10;
  int core_list_size() const;
  void clear_core_list();
  static const int kCoreListFieldNumber = 10;
  ::google::protobuf::int32 core_list(int index) const;
  void set_core_list(int index, ::google::protobuf::int32 value);
  void add_core_list(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      core_list() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_core_list();

  // repeated .grpc.testing.ChannelArg channel_args = 1002;
  int channel_args_size() const;
  void clear_channel_args();
  static const int kChannelArgsFieldNumber = 1002;
  const ::grpc::testing::ChannelArg& channel_args(int index) const;
  ::grpc::testing::ChannelArg* mutable_channel_args(int index);
  ::grpc::testing::ChannelArg* add_channel_args();
  ::google::protobuf::RepeatedPtrField< ::grpc::testing::ChannelArg >*
      mutable_channel_args();
  const ::google::protobuf::RepeatedPtrField< ::grpc::testing::ChannelArg >&
      channel_args() const;

  // string other_server_api = 11;
  void clear_other_server_api();
  static const int kOtherServerApiFieldNumber = 11;
  const ::std::string& other_server_api() const;
  void set_other_server_api(const ::std::string& value);
  #if LANG_CXX11
  void set_other_server_api(::std::string&& value);
  #endif
  void set_other_server_api(const char* value);
  void set_other_server_api(const char* value, size_t size);
  ::std::string* mutable_other_server_api();
  ::std::string* release_other_server_api();
  void set_allocated_other_server_api(::std::string* other_server_api);

  // .grpc.testing.SecurityParams security_params = 2;
  bool has_security_params() const;
  void clear_security_params();
  static const int kSecurityParamsFieldNumber = 2;
  const ::grpc::testing::SecurityParams& security_params() const;
  ::grpc::testing::SecurityParams* release_security_params();
  ::grpc::testing::SecurityParams* mutable_security_params();
  void set_allocated_security_params(::grpc::testing::SecurityParams* security_params);

  // .grpc.testing.PayloadConfig payload_config = 9;
  bool has_payload_config() const;
  void clear_payload_config();
  static const int kPayloadConfigFieldNumber = 9;
  const ::grpc::testing::PayloadConfig& payload_config() const;
  ::grpc::testing::PayloadConfig* release_payload_config();
  ::grpc::testing::PayloadConfig* mutable_payload_config();
  void set_allocated_payload_config(::grpc::testing::PayloadConfig* payload_config);

  // .grpc.testing.ServerType server_type = 1;
  void clear_server_type();
  static const int kServerTypeFieldNumber = 1;
  ::grpc::testing::ServerType server_type() const;
  void set_server_type(::grpc::testing::ServerType value);

  // int32 port = 4;
  void clear_port();
  static const int kPortFieldNumber = 4;
  ::google::protobuf::int32 port() const;
  void set_port(::google::protobuf::int32 value);

  // int32 async_server_threads = 7;
  void clear_async_server_threads();
  static const int kAsyncServerThreadsFieldNumber = 7;
  ::google::protobuf::int32 async_server_threads() const;
  void set_async_server_threads(::google::protobuf::int32 value);

  // int32 core_limit = 8;
  void clear_core_limit();
  static const int kCoreLimitFieldNumber = 8;
  ::google::protobuf::int32 core_limit() const;
  void set_core_limit(::google::protobuf::int32 value);

  // int32 threads_per_cq = 12;
  void clear_threads_per_cq();
  static const int kThreadsPerCqFieldNumber = 12;
  ::google::protobuf::int32 threads_per_cq() const;
  void set_threads_per_cq(::google::protobuf::int32 value);

  // int32 resource_quota_size = 1001;
  void clear_resource_quota_size();
  static const int kResourceQuotaSizeFieldNumber = 1001;
  ::google::protobuf::int32 resource_quota_size() const;
  void set_resource_quota_size(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:grpc.testing.ServerConfig)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > core_list_;
  mutable int _core_list_cached_byte_size_;
  ::google::protobuf::RepeatedPtrField< ::grpc::testing::ChannelArg > channel_args_;
  ::google::protobuf::internal::ArenaStringPtr other_server_api_;
  ::grpc::testing::SecurityParams* security_params_;
  ::grpc::testing::PayloadConfig* payload_config_;
  int server_type_;
  ::google::protobuf::int32 port_;
  ::google::protobuf::int32 async_server_threads_;
  ::google::protobuf::int32 core_limit_;
  ::google::protobuf::int32 threads_per_cq_;
  ::google::protobuf::int32 resource_quota_size_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsServerConfigImpl();
};
// -------------------------------------------------------------------

class ServerArgs : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.ServerArgs) */ {
 public:
  ServerArgs();
  virtual ~ServerArgs();

  ServerArgs(const ServerArgs& from);

  inline ServerArgs& operator=(const ServerArgs& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ServerArgs(ServerArgs&& from) noexcept
    : ServerArgs() {
    *this = ::std::move(from);
  }

  inline ServerArgs& operator=(ServerArgs&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ServerArgs& default_instance();

  enum ArgtypeCase {
    kSetup = 1,
    kMark = 2,
    ARGTYPE_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ServerArgs* internal_default_instance() {
    return reinterpret_cast<const ServerArgs*>(
               &_ServerArgs_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    10;

  void Swap(ServerArgs* other);
  friend void swap(ServerArgs& a, ServerArgs& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ServerArgs* New() const PROTOBUF_FINAL { return New(NULL); }

  ServerArgs* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ServerArgs& from);
  void MergeFrom(const ServerArgs& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ServerArgs* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .grpc.testing.ServerConfig setup = 1;
  bool has_setup() const;
  void clear_setup();
  static const int kSetupFieldNumber = 1;
  const ::grpc::testing::ServerConfig& setup() const;
  ::grpc::testing::ServerConfig* release_setup();
  ::grpc::testing::ServerConfig* mutable_setup();
  void set_allocated_setup(::grpc::testing::ServerConfig* setup);

  // .grpc.testing.Mark mark = 2;
  bool has_mark() const;
  void clear_mark();
  static const int kMarkFieldNumber = 2;
  const ::grpc::testing::Mark& mark() const;
  ::grpc::testing::Mark* release_mark();
  ::grpc::testing::Mark* mutable_mark();
  void set_allocated_mark(::grpc::testing::Mark* mark);

  ArgtypeCase argtype_case() const;
  // @@protoc_insertion_point(class_scope:grpc.testing.ServerArgs)
 private:
  void set_has_setup();
  void set_has_mark();

  inline bool has_argtype() const;
  void clear_argtype();
  inline void clear_has_argtype();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  union ArgtypeUnion {
    ArgtypeUnion() {}
    ::grpc::testing::ServerConfig* setup_;
    ::grpc::testing::Mark* mark_;
  } argtype_;
  mutable int _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsServerArgsImpl();
};
// -------------------------------------------------------------------

class ServerStatus : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.ServerStatus) */ {
 public:
  ServerStatus();
  virtual ~ServerStatus();

  ServerStatus(const ServerStatus& from);

  inline ServerStatus& operator=(const ServerStatus& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ServerStatus(ServerStatus&& from) noexcept
    : ServerStatus() {
    *this = ::std::move(from);
  }

  inline ServerStatus& operator=(ServerStatus&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ServerStatus& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ServerStatus* internal_default_instance() {
    return reinterpret_cast<const ServerStatus*>(
               &_ServerStatus_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    11;

  void Swap(ServerStatus* other);
  friend void swap(ServerStatus& a, ServerStatus& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ServerStatus* New() const PROTOBUF_FINAL { return New(NULL); }

  ServerStatus* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ServerStatus& from);
  void MergeFrom(const ServerStatus& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ServerStatus* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .grpc.testing.ServerStats stats = 1;
  bool has_stats() const;
  void clear_stats();
  static const int kStatsFieldNumber = 1;
  const ::grpc::testing::ServerStats& stats() const;
  ::grpc::testing::ServerStats* release_stats();
  ::grpc::testing::ServerStats* mutable_stats();
  void set_allocated_stats(::grpc::testing::ServerStats* stats);

  // int32 port = 2;
  void clear_port();
  static const int kPortFieldNumber = 2;
  ::google::protobuf::int32 port() const;
  void set_port(::google::protobuf::int32 value);

  // int32 cores = 3;
  void clear_cores();
  static const int kCoresFieldNumber = 3;
  ::google::protobuf::int32 cores() const;
  void set_cores(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:grpc.testing.ServerStatus)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::grpc::testing::ServerStats* stats_;
  ::google::protobuf::int32 port_;
  ::google::protobuf::int32 cores_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsServerStatusImpl();
};
// -------------------------------------------------------------------

class CoreRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.CoreRequest) */ {
 public:
  CoreRequest();
  virtual ~CoreRequest();

  CoreRequest(const CoreRequest& from);

  inline CoreRequest& operator=(const CoreRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CoreRequest(CoreRequest&& from) noexcept
    : CoreRequest() {
    *this = ::std::move(from);
  }

  inline CoreRequest& operator=(CoreRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const CoreRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CoreRequest* internal_default_instance() {
    return reinterpret_cast<const CoreRequest*>(
               &_CoreRequest_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    12;

  void Swap(CoreRequest* other);
  friend void swap(CoreRequest& a, CoreRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CoreRequest* New() const PROTOBUF_FINAL { return New(NULL); }

  CoreRequest* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const CoreRequest& from);
  void MergeFrom(const CoreRequest& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(CoreRequest* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:grpc.testing.CoreRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsCoreRequestImpl();
};
// -------------------------------------------------------------------

class CoreResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.CoreResponse) */ {
 public:
  CoreResponse();
  virtual ~CoreResponse();

  CoreResponse(const CoreResponse& from);

  inline CoreResponse& operator=(const CoreResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  CoreResponse(CoreResponse&& from) noexcept
    : CoreResponse() {
    *this = ::std::move(from);
  }

  inline CoreResponse& operator=(CoreResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const CoreResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const CoreResponse* internal_default_instance() {
    return reinterpret_cast<const CoreResponse*>(
               &_CoreResponse_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    13;

  void Swap(CoreResponse* other);
  friend void swap(CoreResponse& a, CoreResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline CoreResponse* New() const PROTOBUF_FINAL { return New(NULL); }

  CoreResponse* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const CoreResponse& from);
  void MergeFrom(const CoreResponse& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(CoreResponse* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int32 cores = 1;
  void clear_cores();
  static const int kCoresFieldNumber = 1;
  ::google::protobuf::int32 cores() const;
  void set_cores(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:grpc.testing.CoreResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int32 cores_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsCoreResponseImpl();
};
// -------------------------------------------------------------------

class Void : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.Void) */ {
 public:
  Void();
  virtual ~Void();

  Void(const Void& from);

  inline Void& operator=(const Void& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Void(Void&& from) noexcept
    : Void() {
    *this = ::std::move(from);
  }

  inline Void& operator=(Void&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Void& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Void* internal_default_instance() {
    return reinterpret_cast<const Void*>(
               &_Void_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    14;

  void Swap(Void* other);
  friend void swap(Void& a, Void& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Void* New() const PROTOBUF_FINAL { return New(NULL); }

  Void* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const Void& from);
  void MergeFrom(const Void& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(Void* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:grpc.testing.Void)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsVoidImpl();
};
// -------------------------------------------------------------------

class Scenario : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.Scenario) */ {
 public:
  Scenario();
  virtual ~Scenario();

  Scenario(const Scenario& from);

  inline Scenario& operator=(const Scenario& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Scenario(Scenario&& from) noexcept
    : Scenario() {
    *this = ::std::move(from);
  }

  inline Scenario& operator=(Scenario&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Scenario& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Scenario* internal_default_instance() {
    return reinterpret_cast<const Scenario*>(
               &_Scenario_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    15;

  void Swap(Scenario* other);
  friend void swap(Scenario& a, Scenario& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Scenario* New() const PROTOBUF_FINAL { return New(NULL); }

  Scenario* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const Scenario& from);
  void MergeFrom(const Scenario& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(Scenario* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // .grpc.testing.ClientConfig client_config = 2;
  bool has_client_config() const;
  void clear_client_config();
  static const int kClientConfigFieldNumber = 2;
  const ::grpc::testing::ClientConfig& client_config() const;
  ::grpc::testing::ClientConfig* release_client_config();
  ::grpc::testing::ClientConfig* mutable_client_config();
  void set_allocated_client_config(::grpc::testing::ClientConfig* client_config);

  // .grpc.testing.ServerConfig server_config = 4;
  bool has_server_config() const;
  void clear_server_config();
  static const int kServerConfigFieldNumber = 4;
  const ::grpc::testing::ServerConfig& server_config() const;
  ::grpc::testing::ServerConfig* release_server_config();
  ::grpc::testing::ServerConfig* mutable_server_config();
  void set_allocated_server_config(::grpc::testing::ServerConfig* server_config);

  // int32 num_clients = 3;
  void clear_num_clients();
  static const int kNumClientsFieldNumber = 3;
  ::google::protobuf::int32 num_clients() const;
  void set_num_clients(::google::protobuf::int32 value);

  // int32 num_servers = 5;
  void clear_num_servers();
  static const int kNumServersFieldNumber = 5;
  ::google::protobuf::int32 num_servers() const;
  void set_num_servers(::google::protobuf::int32 value);

  // int32 warmup_seconds = 6;
  void clear_warmup_seconds();
  static const int kWarmupSecondsFieldNumber = 6;
  ::google::protobuf::int32 warmup_seconds() const;
  void set_warmup_seconds(::google::protobuf::int32 value);

  // int32 benchmark_seconds = 7;
  void clear_benchmark_seconds();
  static const int kBenchmarkSecondsFieldNumber = 7;
  ::google::protobuf::int32 benchmark_seconds() const;
  void set_benchmark_seconds(::google::protobuf::int32 value);

  // int32 spawn_local_worker_count = 8;
  void clear_spawn_local_worker_count();
  static const int kSpawnLocalWorkerCountFieldNumber = 8;
  ::google::protobuf::int32 spawn_local_worker_count() const;
  void set_spawn_local_worker_count(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:grpc.testing.Scenario)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  ::grpc::testing::ClientConfig* client_config_;
  ::grpc::testing::ServerConfig* server_config_;
  ::google::protobuf::int32 num_clients_;
  ::google::protobuf::int32 num_servers_;
  ::google::protobuf::int32 warmup_seconds_;
  ::google::protobuf::int32 benchmark_seconds_;
  ::google::protobuf::int32 spawn_local_worker_count_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsScenarioImpl();
};
// -------------------------------------------------------------------

class Scenarios : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.Scenarios) */ {
 public:
  Scenarios();
  virtual ~Scenarios();

  Scenarios(const Scenarios& from);

  inline Scenarios& operator=(const Scenarios& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Scenarios(Scenarios&& from) noexcept
    : Scenarios() {
    *this = ::std::move(from);
  }

  inline Scenarios& operator=(Scenarios&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Scenarios& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Scenarios* internal_default_instance() {
    return reinterpret_cast<const Scenarios*>(
               &_Scenarios_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    16;

  void Swap(Scenarios* other);
  friend void swap(Scenarios& a, Scenarios& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Scenarios* New() const PROTOBUF_FINAL { return New(NULL); }

  Scenarios* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const Scenarios& from);
  void MergeFrom(const Scenarios& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(Scenarios* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .grpc.testing.Scenario scenarios = 1;
  int scenarios_size() const;
  void clear_scenarios();
  static const int kScenariosFieldNumber = 1;
  const ::grpc::testing::Scenario& scenarios(int index) const;
  ::grpc::testing::Scenario* mutable_scenarios(int index);
  ::grpc::testing::Scenario* add_scenarios();
  ::google::protobuf::RepeatedPtrField< ::grpc::testing::Scenario >*
      mutable_scenarios();
  const ::google::protobuf::RepeatedPtrField< ::grpc::testing::Scenario >&
      scenarios() const;

  // @@protoc_insertion_point(class_scope:grpc.testing.Scenarios)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::grpc::testing::Scenario > scenarios_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsScenariosImpl();
};
// -------------------------------------------------------------------

class ScenarioResultSummary : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.ScenarioResultSummary) */ {
 public:
  ScenarioResultSummary();
  virtual ~ScenarioResultSummary();

  ScenarioResultSummary(const ScenarioResultSummary& from);

  inline ScenarioResultSummary& operator=(const ScenarioResultSummary& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ScenarioResultSummary(ScenarioResultSummary&& from) noexcept
    : ScenarioResultSummary() {
    *this = ::std::move(from);
  }

  inline ScenarioResultSummary& operator=(ScenarioResultSummary&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ScenarioResultSummary& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ScenarioResultSummary* internal_default_instance() {
    return reinterpret_cast<const ScenarioResultSummary*>(
               &_ScenarioResultSummary_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    17;

  void Swap(ScenarioResultSummary* other);
  friend void swap(ScenarioResultSummary& a, ScenarioResultSummary& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ScenarioResultSummary* New() const PROTOBUF_FINAL { return New(NULL); }

  ScenarioResultSummary* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ScenarioResultSummary& from);
  void MergeFrom(const ScenarioResultSummary& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ScenarioResultSummary* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // double qps = 1;
  void clear_qps();
  static const int kQpsFieldNumber = 1;
  double qps() const;
  void set_qps(double value);

  // double qps_per_server_core = 2;
  void clear_qps_per_server_core();
  static const int kQpsPerServerCoreFieldNumber = 2;
  double qps_per_server_core() const;
  void set_qps_per_server_core(double value);

  // double server_system_time = 3;
  void clear_server_system_time();
  static const int kServerSystemTimeFieldNumber = 3;
  double server_system_time() const;
  void set_server_system_time(double value);

  // double server_user_time = 4;
  void clear_server_user_time();
  static const int kServerUserTimeFieldNumber = 4;
  double server_user_time() const;
  void set_server_user_time(double value);

  // double client_system_time = 5;
  void clear_client_system_time();
  static const int kClientSystemTimeFieldNumber = 5;
  double client_system_time() const;
  void set_client_system_time(double value);

  // double client_user_time = 6;
  void clear_client_user_time();
  static const int kClientUserTimeFieldNumber = 6;
  double client_user_time() const;
  void set_client_user_time(double value);

  // double latency_50 = 7;
  void clear_latency_50();
  static const int kLatency50FieldNumber = 7;
  double latency_50() const;
  void set_latency_50(double value);

  // double latency_90 = 8;
  void clear_latency_90();
  static const int kLatency90FieldNumber = 8;
  double latency_90() const;
  void set_latency_90(double value);

  // double latency_95 = 9;
  void clear_latency_95();
  static const int kLatency95FieldNumber = 9;
  double latency_95() const;
  void set_latency_95(double value);

  // double latency_99 = 10;
  void clear_latency_99();
  static const int kLatency99FieldNumber = 10;
  double latency_99() const;
  void set_latency_99(double value);

  // double latency_999 = 11;
  void clear_latency_999();
  static const int kLatency999FieldNumber = 11;
  double latency_999() const;
  void set_latency_999(double value);

  // double server_cpu_usage = 12;
  void clear_server_cpu_usage();
  static const int kServerCpuUsageFieldNumber = 12;
  double server_cpu_usage() const;
  void set_server_cpu_usage(double value);

  // double successful_requests_per_second = 13;
  void clear_successful_requests_per_second();
  static const int kSuccessfulRequestsPerSecondFieldNumber = 13;
  double successful_requests_per_second() const;
  void set_successful_requests_per_second(double value);

  // double failed_requests_per_second = 14;
  void clear_failed_requests_per_second();
  static const int kFailedRequestsPerSecondFieldNumber = 14;
  double failed_requests_per_second() const;
  void set_failed_requests_per_second(double value);

  // double client_polls_per_request = 15;
  void clear_client_polls_per_request();
  static const int kClientPollsPerRequestFieldNumber = 15;
  double client_polls_per_request() const;
  void set_client_polls_per_request(double value);

  // double server_polls_per_request = 16;
  void clear_server_polls_per_request();
  static const int kServerPollsPerRequestFieldNumber = 16;
  double server_polls_per_request() const;
  void set_server_polls_per_request(double value);

  // double server_queries_per_cpu_sec = 17;
  void clear_server_queries_per_cpu_sec();
  static const int kServerQueriesPerCpuSecFieldNumber = 17;
  double server_queries_per_cpu_sec() const;
  void set_server_queries_per_cpu_sec(double value);

  // double client_queries_per_cpu_sec = 18;
  void clear_client_queries_per_cpu_sec();
  static const int kClientQueriesPerCpuSecFieldNumber = 18;
  double client_queries_per_cpu_sec() const;
  void set_client_queries_per_cpu_sec(double value);

  // @@protoc_insertion_point(class_scope:grpc.testing.ScenarioResultSummary)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  double qps_;
  double qps_per_server_core_;
  double server_system_time_;
  double server_user_time_;
  double client_system_time_;
  double client_user_time_;
  double latency_50_;
  double latency_90_;
  double latency_95_;
  double latency_99_;
  double latency_999_;
  double server_cpu_usage_;
  double successful_requests_per_second_;
  double failed_requests_per_second_;
  double client_polls_per_request_;
  double server_polls_per_request_;
  double server_queries_per_cpu_sec_;
  double client_queries_per_cpu_sec_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsScenarioResultSummaryImpl();
};
// -------------------------------------------------------------------

class ScenarioResult : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.ScenarioResult) */ {
 public:
  ScenarioResult();
  virtual ~ScenarioResult();

  ScenarioResult(const ScenarioResult& from);

  inline ScenarioResult& operator=(const ScenarioResult& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ScenarioResult(ScenarioResult&& from) noexcept
    : ScenarioResult() {
    *this = ::std::move(from);
  }

  inline ScenarioResult& operator=(ScenarioResult&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ScenarioResult& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ScenarioResult* internal_default_instance() {
    return reinterpret_cast<const ScenarioResult*>(
               &_ScenarioResult_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    18;

  void Swap(ScenarioResult* other);
  friend void swap(ScenarioResult& a, ScenarioResult& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ScenarioResult* New() const PROTOBUF_FINAL { return New(NULL); }

  ScenarioResult* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ScenarioResult& from);
  void MergeFrom(const ScenarioResult& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ScenarioResult* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .grpc.testing.ClientStats client_stats = 3;
  int client_stats_size() const;
  void clear_client_stats();
  static const int kClientStatsFieldNumber = 3;
  const ::grpc::testing::ClientStats& client_stats(int index) const;
  ::grpc::testing::ClientStats* mutable_client_stats(int index);
  ::grpc::testing::ClientStats* add_client_stats();
  ::google::protobuf::RepeatedPtrField< ::grpc::testing::ClientStats >*
      mutable_client_stats();
  const ::google::protobuf::RepeatedPtrField< ::grpc::testing::ClientStats >&
      client_stats() const;

  // repeated .grpc.testing.ServerStats server_stats = 4;
  int server_stats_size() const;
  void clear_server_stats();
  static const int kServerStatsFieldNumber = 4;
  const ::grpc::testing::ServerStats& server_stats(int index) const;
  ::grpc::testing::ServerStats* mutable_server_stats(int index);
  ::grpc::testing::ServerStats* add_server_stats();
  ::google::protobuf::RepeatedPtrField< ::grpc::testing::ServerStats >*
      mutable_server_stats();
  const ::google::protobuf::RepeatedPtrField< ::grpc::testing::ServerStats >&
      server_stats() const;

  // repeated int32 server_cores = 5;
  int server_cores_size() const;
  void clear_server_cores();
  static const int kServerCoresFieldNumber = 5;
  ::google::protobuf::int32 server_cores(int index) const;
  void set_server_cores(int index, ::google::protobuf::int32 value);
  void add_server_cores(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      server_cores() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_server_cores();

  // repeated bool client_success = 7;
  int client_success_size() const;
  void clear_client_success();
  static const int kClientSuccessFieldNumber = 7;
  bool client_success(int index) const;
  void set_client_success(int index, bool value);
  void add_client_success(bool value);
  const ::google::protobuf::RepeatedField< bool >&
      client_success() const;
  ::google::protobuf::RepeatedField< bool >*
      mutable_client_success();

  // repeated bool server_success = 8;
  int server_success_size() const;
  void clear_server_success();
  static const int kServerSuccessFieldNumber = 8;
  bool server_success(int index) const;
  void set_server_success(int index, bool value);
  void add_server_success(bool value);
  const ::google::protobuf::RepeatedField< bool >&
      server_success() const;
  ::google::protobuf::RepeatedField< bool >*
      mutable_server_success();

  // repeated .grpc.testing.RequestResultCount request_results = 9;
  int request_results_size() const;
  void clear_request_results();
  static const int kRequestResultsFieldNumber = 9;
  const ::grpc::testing::RequestResultCount& request_results(int index) const;
  ::grpc::testing::RequestResultCount* mutable_request_results(int index);
  ::grpc::testing::RequestResultCount* add_request_results();
  ::google::protobuf::RepeatedPtrField< ::grpc::testing::RequestResultCount >*
      mutable_request_results();
  const ::google::protobuf::RepeatedPtrField< ::grpc::testing::RequestResultCount >&
      request_results() const;

  // .grpc.testing.Scenario scenario = 1;
  bool has_scenario() const;
  void clear_scenario();
  static const int kScenarioFieldNumber = 1;
  const ::grpc::testing::Scenario& scenario() const;
  ::grpc::testing::Scenario* release_scenario();
  ::grpc::testing::Scenario* mutable_scenario();
  void set_allocated_scenario(::grpc::testing::Scenario* scenario);

  // .grpc.testing.HistogramData latencies = 2;
  bool has_latencies() const;
  void clear_latencies();
  static const int kLatenciesFieldNumber = 2;
  const ::grpc::testing::HistogramData& latencies() const;
  ::grpc::testing::HistogramData* release_latencies();
  ::grpc::testing::HistogramData* mutable_latencies();
  void set_allocated_latencies(::grpc::testing::HistogramData* latencies);

  // .grpc.testing.ScenarioResultSummary summary = 6;
  bool has_summary() const;
  void clear_summary();
  static const int kSummaryFieldNumber = 6;
  const ::grpc::testing::ScenarioResultSummary& summary() const;
  ::grpc::testing::ScenarioResultSummary* release_summary();
  ::grpc::testing::ScenarioResultSummary* mutable_summary();
  void set_allocated_summary(::grpc::testing::ScenarioResultSummary* summary);

  // @@protoc_insertion_point(class_scope:grpc.testing.ScenarioResult)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::grpc::testing::ClientStats > client_stats_;
  ::google::protobuf::RepeatedPtrField< ::grpc::testing::ServerStats > server_stats_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > server_cores_;
  mutable int _server_cores_cached_byte_size_;
  ::google::protobuf::RepeatedField< bool > client_success_;
  mutable int _client_success_cached_byte_size_;
  ::google::protobuf::RepeatedField< bool > server_success_;
  mutable int _server_success_cached_byte_size_;
  ::google::protobuf::RepeatedPtrField< ::grpc::testing::RequestResultCount > request_results_;
  ::grpc::testing::Scenario* scenario_;
  ::grpc::testing::HistogramData* latencies_;
  ::grpc::testing::ScenarioResultSummary* summary_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto::InitDefaultsScenarioResultImpl();
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// PoissonParams

// double offered_load = 1;
inline void PoissonParams::clear_offered_load() {
  offered_load_ = 0;
}
inline double PoissonParams::offered_load() const {
  // @@protoc_insertion_point(field_get:grpc.testing.PoissonParams.offered_load)
  return offered_load_;
}
inline void PoissonParams::set_offered_load(double value) {
  
  offered_load_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.PoissonParams.offered_load)
}

// -------------------------------------------------------------------

// ClosedLoopParams

// -------------------------------------------------------------------

// LoadParams

// .grpc.testing.ClosedLoopParams closed_loop = 1;
inline bool LoadParams::has_closed_loop() const {
  return load_case() == kClosedLoop;
}
inline void LoadParams::set_has_closed_loop() {
  _oneof_case_[0] = kClosedLoop;
}
inline void LoadParams::clear_closed_loop() {
  if (has_closed_loop()) {
    delete load_.closed_loop_;
    clear_has_load();
  }
}
inline ::grpc::testing::ClosedLoopParams* LoadParams::release_closed_loop() {
  // @@protoc_insertion_point(field_release:grpc.testing.LoadParams.closed_loop)
  if (has_closed_loop()) {
    clear_has_load();
      ::grpc::testing::ClosedLoopParams* temp = load_.closed_loop_;
    load_.closed_loop_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::grpc::testing::ClosedLoopParams& LoadParams::closed_loop() const {
  // @@protoc_insertion_point(field_get:grpc.testing.LoadParams.closed_loop)
  return has_closed_loop()
      ? *load_.closed_loop_
      : *reinterpret_cast< ::grpc::testing::ClosedLoopParams*>(&::grpc::testing::_ClosedLoopParams_default_instance_);
}
inline ::grpc::testing::ClosedLoopParams* LoadParams::mutable_closed_loop() {
  if (!has_closed_loop()) {
    clear_load();
    set_has_closed_loop();
    load_.closed_loop_ = new ::grpc::testing::ClosedLoopParams;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.LoadParams.closed_loop)
  return load_.closed_loop_;
}

// .grpc.testing.PoissonParams poisson = 2;
inline bool LoadParams::has_poisson() const {
  return load_case() == kPoisson;
}
inline void LoadParams::set_has_poisson() {
  _oneof_case_[0] = kPoisson;
}
inline void LoadParams::clear_poisson() {
  if (has_poisson()) {
    delete load_.poisson_;
    clear_has_load();
  }
}
inline ::grpc::testing::PoissonParams* LoadParams::release_poisson() {
  // @@protoc_insertion_point(field_release:grpc.testing.LoadParams.poisson)
  if (has_poisson()) {
    clear_has_load();
      ::grpc::testing::PoissonParams* temp = load_.poisson_;
    load_.poisson_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::grpc::testing::PoissonParams& LoadParams::poisson() const {
  // @@protoc_insertion_point(field_get:grpc.testing.LoadParams.poisson)
  return has_poisson()
      ? *load_.poisson_
      : *reinterpret_cast< ::grpc::testing::PoissonParams*>(&::grpc::testing::_PoissonParams_default_instance_);
}
inline ::grpc::testing::PoissonParams* LoadParams::mutable_poisson() {
  if (!has_poisson()) {
    clear_load();
    set_has_poisson();
    load_.poisson_ = new ::grpc::testing::PoissonParams;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.LoadParams.poisson)
  return load_.poisson_;
}

inline bool LoadParams::has_load() const {
  return load_case() != LOAD_NOT_SET;
}
inline void LoadParams::clear_has_load() {
  _oneof_case_[0] = LOAD_NOT_SET;
}
inline LoadParams::LoadCase LoadParams::load_case() const {
  return LoadParams::LoadCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// SecurityParams

// bool use_test_ca = 1;
inline void SecurityParams::clear_use_test_ca() {
  use_test_ca_ = false;
}
inline bool SecurityParams::use_test_ca() const {
  // @@protoc_insertion_point(field_get:grpc.testing.SecurityParams.use_test_ca)
  return use_test_ca_;
}
inline void SecurityParams::set_use_test_ca(bool value) {
  
  use_test_ca_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.SecurityParams.use_test_ca)
}

// string server_host_override = 2;
inline void SecurityParams::clear_server_host_override() {
  server_host_override_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SecurityParams::server_host_override() const {
  // @@protoc_insertion_point(field_get:grpc.testing.SecurityParams.server_host_override)
  return server_host_override_.GetNoArena();
}
inline void SecurityParams::set_server_host_override(const ::std::string& value) {
  
  server_host_override_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.testing.SecurityParams.server_host_override)
}
#if LANG_CXX11
inline void SecurityParams::set_server_host_override(::std::string&& value) {
  
  server_host_override_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.testing.SecurityParams.server_host_override)
}
#endif
inline void SecurityParams::set_server_host_override(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  server_host_override_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.testing.SecurityParams.server_host_override)
}
inline void SecurityParams::set_server_host_override(const char* value, size_t size) {
  
  server_host_override_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.testing.SecurityParams.server_host_override)
}
inline ::std::string* SecurityParams::mutable_server_host_override() {
  
  // @@protoc_insertion_point(field_mutable:grpc.testing.SecurityParams.server_host_override)
  return server_host_override_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SecurityParams::release_server_host_override() {
  // @@protoc_insertion_point(field_release:grpc.testing.SecurityParams.server_host_override)
  
  return server_host_override_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SecurityParams::set_allocated_server_host_override(::std::string* server_host_override) {
  if (server_host_override != NULL) {
    
  } else {
    
  }
  server_host_override_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), server_host_override);
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.SecurityParams.server_host_override)
}

// string cred_type = 3;
inline void SecurityParams::clear_cred_type() {
  cred_type_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SecurityParams::cred_type() const {
  // @@protoc_insertion_point(field_get:grpc.testing.SecurityParams.cred_type)
  return cred_type_.GetNoArena();
}
inline void SecurityParams::set_cred_type(const ::std::string& value) {
  
  cred_type_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.testing.SecurityParams.cred_type)
}
#if LANG_CXX11
inline void SecurityParams::set_cred_type(::std::string&& value) {
  
  cred_type_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.testing.SecurityParams.cred_type)
}
#endif
inline void SecurityParams::set_cred_type(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  cred_type_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.testing.SecurityParams.cred_type)
}
inline void SecurityParams::set_cred_type(const char* value, size_t size) {
  
  cred_type_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.testing.SecurityParams.cred_type)
}
inline ::std::string* SecurityParams::mutable_cred_type() {
  
  // @@protoc_insertion_point(field_mutable:grpc.testing.SecurityParams.cred_type)
  return cred_type_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SecurityParams::release_cred_type() {
  // @@protoc_insertion_point(field_release:grpc.testing.SecurityParams.cred_type)
  
  return cred_type_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SecurityParams::set_allocated_cred_type(::std::string* cred_type) {
  if (cred_type != NULL) {
    
  } else {
    
  }
  cred_type_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), cred_type);
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.SecurityParams.cred_type)
}

// -------------------------------------------------------------------

// ChannelArg

// string name = 1;
inline void ChannelArg::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ChannelArg::name() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ChannelArg.name)
  return name_.GetNoArena();
}
inline void ChannelArg::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.testing.ChannelArg.name)
}
#if LANG_CXX11
inline void ChannelArg::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.testing.ChannelArg.name)
}
#endif
inline void ChannelArg::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.testing.ChannelArg.name)
}
inline void ChannelArg::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.testing.ChannelArg.name)
}
inline ::std::string* ChannelArg::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:grpc.testing.ChannelArg.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ChannelArg::release_name() {
  // @@protoc_insertion_point(field_release:grpc.testing.ChannelArg.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ChannelArg::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.ChannelArg.name)
}

// string str_value = 2;
inline bool ChannelArg::has_str_value() const {
  return value_case() == kStrValue;
}
inline void ChannelArg::set_has_str_value() {
  _oneof_case_[0] = kStrValue;
}
inline void ChannelArg::clear_str_value() {
  if (has_str_value()) {
    value_.str_value_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    clear_has_value();
  }
}
inline const ::std::string& ChannelArg::str_value() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ChannelArg.str_value)
  if (has_str_value()) {
    return value_.str_value_.GetNoArena();
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void ChannelArg::set_str_value(const ::std::string& value) {
  // @@protoc_insertion_point(field_set:grpc.testing.ChannelArg.str_value)
  if (!has_str_value()) {
    clear_value();
    set_has_str_value();
    value_.str_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  value_.str_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.testing.ChannelArg.str_value)
}
#if LANG_CXX11
inline void ChannelArg::set_str_value(::std::string&& value) {
  // @@protoc_insertion_point(field_set:grpc.testing.ChannelArg.str_value)
  if (!has_str_value()) {
    clear_value();
    set_has_str_value();
    value_.str_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  value_.str_value_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.testing.ChannelArg.str_value)
}
#endif
inline void ChannelArg::set_str_value(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  if (!has_str_value()) {
    clear_value();
    set_has_str_value();
    value_.str_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  value_.str_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.testing.ChannelArg.str_value)
}
inline void ChannelArg::set_str_value(const char* value, size_t size) {
  if (!has_str_value()) {
    clear_value();
    set_has_str_value();
    value_.str_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  value_.str_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.testing.ChannelArg.str_value)
}
inline ::std::string* ChannelArg::mutable_str_value() {
  if (!has_str_value()) {
    clear_value();
    set_has_str_value();
    value_.str_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.ChannelArg.str_value)
  return value_.str_value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ChannelArg::release_str_value() {
  // @@protoc_insertion_point(field_release:grpc.testing.ChannelArg.str_value)
  if (has_str_value()) {
    clear_has_value();
    return value_.str_value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  } else {
    return NULL;
  }
}
inline void ChannelArg::set_allocated_str_value(::std::string* str_value) {
  if (!has_str_value()) {
    value_.str_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_value();
  if (str_value != NULL) {
    set_has_str_value();
    value_.str_value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        str_value);
  }
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.ChannelArg.str_value)
}

// int32 int_value = 3;
inline bool ChannelArg::has_int_value() const {
  return value_case() == kIntValue;
}
inline void ChannelArg::set_has_int_value() {
  _oneof_case_[0] = kIntValue;
}
inline void ChannelArg::clear_int_value() {
  if (has_int_value()) {
    value_.int_value_ = 0;
    clear_has_value();
  }
}
inline ::google::protobuf::int32 ChannelArg::int_value() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ChannelArg.int_value)
  if (has_int_value()) {
    return value_.int_value_;
  }
  return 0;
}
inline void ChannelArg::set_int_value(::google::protobuf::int32 value) {
  if (!has_int_value()) {
    clear_value();
    set_has_int_value();
  }
  value_.int_value_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ChannelArg.int_value)
}

inline bool ChannelArg::has_value() const {
  return value_case() != VALUE_NOT_SET;
}
inline void ChannelArg::clear_has_value() {
  _oneof_case_[0] = VALUE_NOT_SET;
}
inline ChannelArg::ValueCase ChannelArg::value_case() const {
  return ChannelArg::ValueCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// ClientConfig

// repeated string server_targets = 1;
inline int ClientConfig::server_targets_size() const {
  return server_targets_.size();
}
inline void ClientConfig::clear_server_targets() {
  server_targets_.Clear();
}
inline const ::std::string& ClientConfig::server_targets(int index) const {
  // @@protoc_insertion_point(field_get:grpc.testing.ClientConfig.server_targets)
  return server_targets_.Get(index);
}
inline ::std::string* ClientConfig::mutable_server_targets(int index) {
  // @@protoc_insertion_point(field_mutable:grpc.testing.ClientConfig.server_targets)
  return server_targets_.Mutable(index);
}
inline void ClientConfig::set_server_targets(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:grpc.testing.ClientConfig.server_targets)
  server_targets_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void ClientConfig::set_server_targets(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:grpc.testing.ClientConfig.server_targets)
  server_targets_.Mutable(index)->assign(std::move(value));
}
#endif
inline void ClientConfig::set_server_targets(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  server_targets_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:grpc.testing.ClientConfig.server_targets)
}
inline void ClientConfig::set_server_targets(int index, const char* value, size_t size) {
  server_targets_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:grpc.testing.ClientConfig.server_targets)
}
inline ::std::string* ClientConfig::add_server_targets() {
  // @@protoc_insertion_point(field_add_mutable:grpc.testing.ClientConfig.server_targets)
  return server_targets_.Add();
}
inline void ClientConfig::add_server_targets(const ::std::string& value) {
  server_targets_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:grpc.testing.ClientConfig.server_targets)
}
#if LANG_CXX11
inline void ClientConfig::add_server_targets(::std::string&& value) {
  server_targets_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:grpc.testing.ClientConfig.server_targets)
}
#endif
inline void ClientConfig::add_server_targets(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  server_targets_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:grpc.testing.ClientConfig.server_targets)
}
inline void ClientConfig::add_server_targets(const char* value, size_t size) {
  server_targets_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:grpc.testing.ClientConfig.server_targets)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
ClientConfig::server_targets() const {
  // @@protoc_insertion_point(field_list:grpc.testing.ClientConfig.server_targets)
  return server_targets_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
ClientConfig::mutable_server_targets() {
  // @@protoc_insertion_point(field_mutable_list:grpc.testing.ClientConfig.server_targets)
  return &server_targets_;
}

// .grpc.testing.ClientType client_type = 2;
inline void ClientConfig::clear_client_type() {
  client_type_ = 0;
}
inline ::grpc::testing::ClientType ClientConfig::client_type() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ClientConfig.client_type)
  return static_cast< ::grpc::testing::ClientType >(client_type_);
}
inline void ClientConfig::set_client_type(::grpc::testing::ClientType value) {
  
  client_type_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ClientConfig.client_type)
}

// .grpc.testing.SecurityParams security_params = 3;
inline bool ClientConfig::has_security_params() const {
  return this != internal_default_instance() && security_params_ != NULL;
}
inline void ClientConfig::clear_security_params() {
  if (GetArenaNoVirtual() == NULL && security_params_ != NULL) {
    delete security_params_;
  }
  security_params_ = NULL;
}
inline const ::grpc::testing::SecurityParams& ClientConfig::security_params() const {
  const ::grpc::testing::SecurityParams* p = security_params_;
  // @@protoc_insertion_point(field_get:grpc.testing.ClientConfig.security_params)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::testing::SecurityParams*>(
      &::grpc::testing::_SecurityParams_default_instance_);
}
inline ::grpc::testing::SecurityParams* ClientConfig::release_security_params() {
  // @@protoc_insertion_point(field_release:grpc.testing.ClientConfig.security_params)
  
  ::grpc::testing::SecurityParams* temp = security_params_;
  security_params_ = NULL;
  return temp;
}
inline ::grpc::testing::SecurityParams* ClientConfig::mutable_security_params() {
  
  if (security_params_ == NULL) {
    security_params_ = new ::grpc::testing::SecurityParams;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.ClientConfig.security_params)
  return security_params_;
}
inline void ClientConfig::set_allocated_security_params(::grpc::testing::SecurityParams* security_params) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete security_params_;
  }
  if (security_params) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      security_params = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, security_params, submessage_arena);
    }
    
  } else {
    
  }
  security_params_ = security_params;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.ClientConfig.security_params)
}

// int32 outstanding_rpcs_per_channel = 4;
inline void ClientConfig::clear_outstanding_rpcs_per_channel() {
  outstanding_rpcs_per_channel_ = 0;
}
inline ::google::protobuf::int32 ClientConfig::outstanding_rpcs_per_channel() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ClientConfig.outstanding_rpcs_per_channel)
  return outstanding_rpcs_per_channel_;
}
inline void ClientConfig::set_outstanding_rpcs_per_channel(::google::protobuf::int32 value) {
  
  outstanding_rpcs_per_channel_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ClientConfig.outstanding_rpcs_per_channel)
}

// int32 client_channels = 5;
inline void ClientConfig::clear_client_channels() {
  client_channels_ = 0;
}
inline ::google::protobuf::int32 ClientConfig::client_channels() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ClientConfig.client_channels)
  return client_channels_;
}
inline void ClientConfig::set_client_channels(::google::protobuf::int32 value) {
  
  client_channels_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ClientConfig.client_channels)
}

// int32 async_client_threads = 7;
inline void ClientConfig::clear_async_client_threads() {
  async_client_threads_ = 0;
}
inline ::google::protobuf::int32 ClientConfig::async_client_threads() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ClientConfig.async_client_threads)
  return async_client_threads_;
}
inline void ClientConfig::set_async_client_threads(::google::protobuf::int32 value) {
  
  async_client_threads_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ClientConfig.async_client_threads)
}

// .grpc.testing.RpcType rpc_type = 8;
inline void ClientConfig::clear_rpc_type() {
  rpc_type_ = 0;
}
inline ::grpc::testing::RpcType ClientConfig::rpc_type() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ClientConfig.rpc_type)
  return static_cast< ::grpc::testing::RpcType >(rpc_type_);
}
inline void ClientConfig::set_rpc_type(::grpc::testing::RpcType value) {
  
  rpc_type_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ClientConfig.rpc_type)
}

// .grpc.testing.LoadParams load_params = 10;
inline bool ClientConfig::has_load_params() const {
  return this != internal_default_instance() && load_params_ != NULL;
}
inline void ClientConfig::clear_load_params() {
  if (GetArenaNoVirtual() == NULL && load_params_ != NULL) {
    delete load_params_;
  }
  load_params_ = NULL;
}
inline const ::grpc::testing::LoadParams& ClientConfig::load_params() const {
  const ::grpc::testing::LoadParams* p = load_params_;
  // @@protoc_insertion_point(field_get:grpc.testing.ClientConfig.load_params)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::testing::LoadParams*>(
      &::grpc::testing::_LoadParams_default_instance_);
}
inline ::grpc::testing::LoadParams* ClientConfig::release_load_params() {
  // @@protoc_insertion_point(field_release:grpc.testing.ClientConfig.load_params)
  
  ::grpc::testing::LoadParams* temp = load_params_;
  load_params_ = NULL;
  return temp;
}
inline ::grpc::testing::LoadParams* ClientConfig::mutable_load_params() {
  
  if (load_params_ == NULL) {
    load_params_ = new ::grpc::testing::LoadParams;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.ClientConfig.load_params)
  return load_params_;
}
inline void ClientConfig::set_allocated_load_params(::grpc::testing::LoadParams* load_params) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete load_params_;
  }
  if (load_params) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      load_params = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, load_params, submessage_arena);
    }
    
  } else {
    
  }
  load_params_ = load_params;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.ClientConfig.load_params)
}

// .grpc.testing.PayloadConfig payload_config = 11;
inline bool ClientConfig::has_payload_config() const {
  return this != internal_default_instance() && payload_config_ != NULL;
}
inline const ::grpc::testing::PayloadConfig& ClientConfig::payload_config() const {
  const ::grpc::testing::PayloadConfig* p = payload_config_;
  // @@protoc_insertion_point(field_get:grpc.testing.ClientConfig.payload_config)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::testing::PayloadConfig*>(
      &::grpc::testing::_PayloadConfig_default_instance_);
}
inline ::grpc::testing::PayloadConfig* ClientConfig::release_payload_config() {
  // @@protoc_insertion_point(field_release:grpc.testing.ClientConfig.payload_config)
  
  ::grpc::testing::PayloadConfig* temp = payload_config_;
  payload_config_ = NULL;
  return temp;
}
inline ::grpc::testing::PayloadConfig* ClientConfig::mutable_payload_config() {
  
  if (payload_config_ == NULL) {
    payload_config_ = new ::grpc::testing::PayloadConfig;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.ClientConfig.payload_config)
  return payload_config_;
}
inline void ClientConfig::set_allocated_payload_config(::grpc::testing::PayloadConfig* payload_config) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(payload_config_);
  }
  if (payload_config) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      payload_config = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, payload_config, submessage_arena);
    }
    
  } else {
    
  }
  payload_config_ = payload_config;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.ClientConfig.payload_config)
}

// .grpc.testing.HistogramParams histogram_params = 12;
inline bool ClientConfig::has_histogram_params() const {
  return this != internal_default_instance() && histogram_params_ != NULL;
}
inline const ::grpc::testing::HistogramParams& ClientConfig::histogram_params() const {
  const ::grpc::testing::HistogramParams* p = histogram_params_;
  // @@protoc_insertion_point(field_get:grpc.testing.ClientConfig.histogram_params)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::testing::HistogramParams*>(
      &::grpc::testing::_HistogramParams_default_instance_);
}
inline ::grpc::testing::HistogramParams* ClientConfig::release_histogram_params() {
  // @@protoc_insertion_point(field_release:grpc.testing.ClientConfig.histogram_params)
  
  ::grpc::testing::HistogramParams* temp = histogram_params_;
  histogram_params_ = NULL;
  return temp;
}
inline ::grpc::testing::HistogramParams* ClientConfig::mutable_histogram_params() {
  
  if (histogram_params_ == NULL) {
    histogram_params_ = new ::grpc::testing::HistogramParams;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.ClientConfig.histogram_params)
  return histogram_params_;
}
inline void ClientConfig::set_allocated_histogram_params(::grpc::testing::HistogramParams* histogram_params) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(histogram_params_);
  }
  if (histogram_params) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      histogram_params = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, histogram_params, submessage_arena);
    }
    
  } else {
    
  }
  histogram_params_ = histogram_params;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.ClientConfig.histogram_params)
}

// repeated int32 core_list = 13;
inline int ClientConfig::core_list_size() const {
  return core_list_.size();
}
inline void ClientConfig::clear_core_list() {
  core_list_.Clear();
}
inline ::google::protobuf::int32 ClientConfig::core_list(int index) const {
  // @@protoc_insertion_point(field_get:grpc.testing.ClientConfig.core_list)
  return core_list_.Get(index);
}
inline void ClientConfig::set_core_list(int index, ::google::protobuf::int32 value) {
  core_list_.Set(index, value);
  // @@protoc_insertion_point(field_set:grpc.testing.ClientConfig.core_list)
}
inline void ClientConfig::add_core_list(::google::protobuf::int32 value) {
  core_list_.Add(value);
  // @@protoc_insertion_point(field_add:grpc.testing.ClientConfig.core_list)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
ClientConfig::core_list() const {
  // @@protoc_insertion_point(field_list:grpc.testing.ClientConfig.core_list)
  return core_list_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
ClientConfig::mutable_core_list() {
  // @@protoc_insertion_point(field_mutable_list:grpc.testing.ClientConfig.core_list)
  return &core_list_;
}

// int32 core_limit = 14;
inline void ClientConfig::clear_core_limit() {
  core_limit_ = 0;
}
inline ::google::protobuf::int32 ClientConfig::core_limit() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ClientConfig.core_limit)
  return core_limit_;
}
inline void ClientConfig::set_core_limit(::google::protobuf::int32 value) {
  
  core_limit_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ClientConfig.core_limit)
}

// string other_client_api = 15;
inline void ClientConfig::clear_other_client_api() {
  other_client_api_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ClientConfig::other_client_api() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ClientConfig.other_client_api)
  return other_client_api_.GetNoArena();
}
inline void ClientConfig::set_other_client_api(const ::std::string& value) {
  
  other_client_api_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.testing.ClientConfig.other_client_api)
}
#if LANG_CXX11
inline void ClientConfig::set_other_client_api(::std::string&& value) {
  
  other_client_api_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.testing.ClientConfig.other_client_api)
}
#endif
inline void ClientConfig::set_other_client_api(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  other_client_api_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.testing.ClientConfig.other_client_api)
}
inline void ClientConfig::set_other_client_api(const char* value, size_t size) {
  
  other_client_api_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.testing.ClientConfig.other_client_api)
}
inline ::std::string* ClientConfig::mutable_other_client_api() {
  
  // @@protoc_insertion_point(field_mutable:grpc.testing.ClientConfig.other_client_api)
  return other_client_api_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ClientConfig::release_other_client_api() {
  // @@protoc_insertion_point(field_release:grpc.testing.ClientConfig.other_client_api)
  
  return other_client_api_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ClientConfig::set_allocated_other_client_api(::std::string* other_client_api) {
  if (other_client_api != NULL) {
    
  } else {
    
  }
  other_client_api_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), other_client_api);
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.ClientConfig.other_client_api)
}

// repeated .grpc.testing.ChannelArg channel_args = 16;
inline int ClientConfig::channel_args_size() const {
  return channel_args_.size();
}
inline void ClientConfig::clear_channel_args() {
  channel_args_.Clear();
}
inline const ::grpc::testing::ChannelArg& ClientConfig::channel_args(int index) const {
  // @@protoc_insertion_point(field_get:grpc.testing.ClientConfig.channel_args)
  return channel_args_.Get(index);
}
inline ::grpc::testing::ChannelArg* ClientConfig::mutable_channel_args(int index) {
  // @@protoc_insertion_point(field_mutable:grpc.testing.ClientConfig.channel_args)
  return channel_args_.Mutable(index);
}
inline ::grpc::testing::ChannelArg* ClientConfig::add_channel_args() {
  // @@protoc_insertion_point(field_add:grpc.testing.ClientConfig.channel_args)
  return channel_args_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::grpc::testing::ChannelArg >*
ClientConfig::mutable_channel_args() {
  // @@protoc_insertion_point(field_mutable_list:grpc.testing.ClientConfig.channel_args)
  return &channel_args_;
}
inline const ::google::protobuf::RepeatedPtrField< ::grpc::testing::ChannelArg >&
ClientConfig::channel_args() const {
  // @@protoc_insertion_point(field_list:grpc.testing.ClientConfig.channel_args)
  return channel_args_;
}

// int32 threads_per_cq = 17;
inline void ClientConfig::clear_threads_per_cq() {
  threads_per_cq_ = 0;
}
inline ::google::protobuf::int32 ClientConfig::threads_per_cq() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ClientConfig.threads_per_cq)
  return threads_per_cq_;
}
inline void ClientConfig::set_threads_per_cq(::google::protobuf::int32 value) {
  
  threads_per_cq_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ClientConfig.threads_per_cq)
}

// int32 messages_per_stream = 18;
inline void ClientConfig::clear_messages_per_stream() {
  messages_per_stream_ = 0;
}
inline ::google::protobuf::int32 ClientConfig::messages_per_stream() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ClientConfig.messages_per_stream)
  return messages_per_stream_;
}
inline void ClientConfig::set_messages_per_stream(::google::protobuf::int32 value) {
  
  messages_per_stream_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ClientConfig.messages_per_stream)
}

// bool use_coalesce_api = 19;
inline void ClientConfig::clear_use_coalesce_api() {
  use_coalesce_api_ = false;
}
inline bool ClientConfig::use_coalesce_api() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ClientConfig.use_coalesce_api)
  return use_coalesce_api_;
}
inline void ClientConfig::set_use_coalesce_api(bool value) {
  
  use_coalesce_api_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ClientConfig.use_coalesce_api)
}

// -------------------------------------------------------------------

// ClientStatus

// .grpc.testing.ClientStats stats = 1;
inline bool ClientStatus::has_stats() const {
  return this != internal_default_instance() && stats_ != NULL;
}
inline const ::grpc::testing::ClientStats& ClientStatus::stats() const {
  const ::grpc::testing::ClientStats* p = stats_;
  // @@protoc_insertion_point(field_get:grpc.testing.ClientStatus.stats)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::testing::ClientStats*>(
      &::grpc::testing::_ClientStats_default_instance_);
}
inline ::grpc::testing::ClientStats* ClientStatus::release_stats() {
  // @@protoc_insertion_point(field_release:grpc.testing.ClientStatus.stats)
  
  ::grpc::testing::ClientStats* temp = stats_;
  stats_ = NULL;
  return temp;
}
inline ::grpc::testing::ClientStats* ClientStatus::mutable_stats() {
  
  if (stats_ == NULL) {
    stats_ = new ::grpc::testing::ClientStats;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.ClientStatus.stats)
  return stats_;
}
inline void ClientStatus::set_allocated_stats(::grpc::testing::ClientStats* stats) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(stats_);
  }
  if (stats) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      stats = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, stats, submessage_arena);
    }
    
  } else {
    
  }
  stats_ = stats;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.ClientStatus.stats)
}

// -------------------------------------------------------------------

// Mark

// bool reset = 1;
inline void Mark::clear_reset() {
  reset_ = false;
}
inline bool Mark::reset() const {
  // @@protoc_insertion_point(field_get:grpc.testing.Mark.reset)
  return reset_;
}
inline void Mark::set_reset(bool value) {
  
  reset_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.Mark.reset)
}

// -------------------------------------------------------------------

// ClientArgs

// .grpc.testing.ClientConfig setup = 1;
inline bool ClientArgs::has_setup() const {
  return argtype_case() == kSetup;
}
inline void ClientArgs::set_has_setup() {
  _oneof_case_[0] = kSetup;
}
inline void ClientArgs::clear_setup() {
  if (has_setup()) {
    delete argtype_.setup_;
    clear_has_argtype();
  }
}
inline ::grpc::testing::ClientConfig* ClientArgs::release_setup() {
  // @@protoc_insertion_point(field_release:grpc.testing.ClientArgs.setup)
  if (has_setup()) {
    clear_has_argtype();
      ::grpc::testing::ClientConfig* temp = argtype_.setup_;
    argtype_.setup_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::grpc::testing::ClientConfig& ClientArgs::setup() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ClientArgs.setup)
  return has_setup()
      ? *argtype_.setup_
      : *reinterpret_cast< ::grpc::testing::ClientConfig*>(&::grpc::testing::_ClientConfig_default_instance_);
}
inline ::grpc::testing::ClientConfig* ClientArgs::mutable_setup() {
  if (!has_setup()) {
    clear_argtype();
    set_has_setup();
    argtype_.setup_ = new ::grpc::testing::ClientConfig;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.ClientArgs.setup)
  return argtype_.setup_;
}

// .grpc.testing.Mark mark = 2;
inline bool ClientArgs::has_mark() const {
  return argtype_case() == kMark;
}
inline void ClientArgs::set_has_mark() {
  _oneof_case_[0] = kMark;
}
inline void ClientArgs::clear_mark() {
  if (has_mark()) {
    delete argtype_.mark_;
    clear_has_argtype();
  }
}
inline ::grpc::testing::Mark* ClientArgs::release_mark() {
  // @@protoc_insertion_point(field_release:grpc.testing.ClientArgs.mark)
  if (has_mark()) {
    clear_has_argtype();
      ::grpc::testing::Mark* temp = argtype_.mark_;
    argtype_.mark_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::grpc::testing::Mark& ClientArgs::mark() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ClientArgs.mark)
  return has_mark()
      ? *argtype_.mark_
      : *reinterpret_cast< ::grpc::testing::Mark*>(&::grpc::testing::_Mark_default_instance_);
}
inline ::grpc::testing::Mark* ClientArgs::mutable_mark() {
  if (!has_mark()) {
    clear_argtype();
    set_has_mark();
    argtype_.mark_ = new ::grpc::testing::Mark;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.ClientArgs.mark)
  return argtype_.mark_;
}

inline bool ClientArgs::has_argtype() const {
  return argtype_case() != ARGTYPE_NOT_SET;
}
inline void ClientArgs::clear_has_argtype() {
  _oneof_case_[0] = ARGTYPE_NOT_SET;
}
inline ClientArgs::ArgtypeCase ClientArgs::argtype_case() const {
  return ClientArgs::ArgtypeCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// ServerConfig

// .grpc.testing.ServerType server_type = 1;
inline void ServerConfig::clear_server_type() {
  server_type_ = 0;
}
inline ::grpc::testing::ServerType ServerConfig::server_type() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ServerConfig.server_type)
  return static_cast< ::grpc::testing::ServerType >(server_type_);
}
inline void ServerConfig::set_server_type(::grpc::testing::ServerType value) {
  
  server_type_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ServerConfig.server_type)
}

// .grpc.testing.SecurityParams security_params = 2;
inline bool ServerConfig::has_security_params() const {
  return this != internal_default_instance() && security_params_ != NULL;
}
inline void ServerConfig::clear_security_params() {
  if (GetArenaNoVirtual() == NULL && security_params_ != NULL) {
    delete security_params_;
  }
  security_params_ = NULL;
}
inline const ::grpc::testing::SecurityParams& ServerConfig::security_params() const {
  const ::grpc::testing::SecurityParams* p = security_params_;
  // @@protoc_insertion_point(field_get:grpc.testing.ServerConfig.security_params)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::testing::SecurityParams*>(
      &::grpc::testing::_SecurityParams_default_instance_);
}
inline ::grpc::testing::SecurityParams* ServerConfig::release_security_params() {
  // @@protoc_insertion_point(field_release:grpc.testing.ServerConfig.security_params)
  
  ::grpc::testing::SecurityParams* temp = security_params_;
  security_params_ = NULL;
  return temp;
}
inline ::grpc::testing::SecurityParams* ServerConfig::mutable_security_params() {
  
  if (security_params_ == NULL) {
    security_params_ = new ::grpc::testing::SecurityParams;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.ServerConfig.security_params)
  return security_params_;
}
inline void ServerConfig::set_allocated_security_params(::grpc::testing::SecurityParams* security_params) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete security_params_;
  }
  if (security_params) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      security_params = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, security_params, submessage_arena);
    }
    
  } else {
    
  }
  security_params_ = security_params;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.ServerConfig.security_params)
}

// int32 port = 4;
inline void ServerConfig::clear_port() {
  port_ = 0;
}
inline ::google::protobuf::int32 ServerConfig::port() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ServerConfig.port)
  return port_;
}
inline void ServerConfig::set_port(::google::protobuf::int32 value) {
  
  port_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ServerConfig.port)
}

// int32 async_server_threads = 7;
inline void ServerConfig::clear_async_server_threads() {
  async_server_threads_ = 0;
}
inline ::google::protobuf::int32 ServerConfig::async_server_threads() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ServerConfig.async_server_threads)
  return async_server_threads_;
}
inline void ServerConfig::set_async_server_threads(::google::protobuf::int32 value) {
  
  async_server_threads_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ServerConfig.async_server_threads)
}

// int32 core_limit = 8;
inline void ServerConfig::clear_core_limit() {
  core_limit_ = 0;
}
inline ::google::protobuf::int32 ServerConfig::core_limit() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ServerConfig.core_limit)
  return core_limit_;
}
inline void ServerConfig::set_core_limit(::google::protobuf::int32 value) {
  
  core_limit_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ServerConfig.core_limit)
}

// .grpc.testing.PayloadConfig payload_config = 9;
inline bool ServerConfig::has_payload_config() const {
  return this != internal_default_instance() && payload_config_ != NULL;
}
inline const ::grpc::testing::PayloadConfig& ServerConfig::payload_config() const {
  const ::grpc::testing::PayloadConfig* p = payload_config_;
  // @@protoc_insertion_point(field_get:grpc.testing.ServerConfig.payload_config)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::testing::PayloadConfig*>(
      &::grpc::testing::_PayloadConfig_default_instance_);
}
inline ::grpc::testing::PayloadConfig* ServerConfig::release_payload_config() {
  // @@protoc_insertion_point(field_release:grpc.testing.ServerConfig.payload_config)
  
  ::grpc::testing::PayloadConfig* temp = payload_config_;
  payload_config_ = NULL;
  return temp;
}
inline ::grpc::testing::PayloadConfig* ServerConfig::mutable_payload_config() {
  
  if (payload_config_ == NULL) {
    payload_config_ = new ::grpc::testing::PayloadConfig;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.ServerConfig.payload_config)
  return payload_config_;
}
inline void ServerConfig::set_allocated_payload_config(::grpc::testing::PayloadConfig* payload_config) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(payload_config_);
  }
  if (payload_config) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      payload_config = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, payload_config, submessage_arena);
    }
    
  } else {
    
  }
  payload_config_ = payload_config;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.ServerConfig.payload_config)
}

// repeated int32 core_list = 10;
inline int ServerConfig::core_list_size() const {
  return core_list_.size();
}
inline void ServerConfig::clear_core_list() {
  core_list_.Clear();
}
inline ::google::protobuf::int32 ServerConfig::core_list(int index) const {
  // @@protoc_insertion_point(field_get:grpc.testing.ServerConfig.core_list)
  return core_list_.Get(index);
}
inline void ServerConfig::set_core_list(int index, ::google::protobuf::int32 value) {
  core_list_.Set(index, value);
  // @@protoc_insertion_point(field_set:grpc.testing.ServerConfig.core_list)
}
inline void ServerConfig::add_core_list(::google::protobuf::int32 value) {
  core_list_.Add(value);
  // @@protoc_insertion_point(field_add:grpc.testing.ServerConfig.core_list)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
ServerConfig::core_list() const {
  // @@protoc_insertion_point(field_list:grpc.testing.ServerConfig.core_list)
  return core_list_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
ServerConfig::mutable_core_list() {
  // @@protoc_insertion_point(field_mutable_list:grpc.testing.ServerConfig.core_list)
  return &core_list_;
}

// string other_server_api = 11;
inline void ServerConfig::clear_other_server_api() {
  other_server_api_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ServerConfig::other_server_api() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ServerConfig.other_server_api)
  return other_server_api_.GetNoArena();
}
inline void ServerConfig::set_other_server_api(const ::std::string& value) {
  
  other_server_api_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.testing.ServerConfig.other_server_api)
}
#if LANG_CXX11
inline void ServerConfig::set_other_server_api(::std::string&& value) {
  
  other_server_api_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.testing.ServerConfig.other_server_api)
}
#endif
inline void ServerConfig::set_other_server_api(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  other_server_api_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.testing.ServerConfig.other_server_api)
}
inline void ServerConfig::set_other_server_api(const char* value, size_t size) {
  
  other_server_api_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.testing.ServerConfig.other_server_api)
}
inline ::std::string* ServerConfig::mutable_other_server_api() {
  
  // @@protoc_insertion_point(field_mutable:grpc.testing.ServerConfig.other_server_api)
  return other_server_api_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ServerConfig::release_other_server_api() {
  // @@protoc_insertion_point(field_release:grpc.testing.ServerConfig.other_server_api)
  
  return other_server_api_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ServerConfig::set_allocated_other_server_api(::std::string* other_server_api) {
  if (other_server_api != NULL) {
    
  } else {
    
  }
  other_server_api_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), other_server_api);
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.ServerConfig.other_server_api)
}

// int32 threads_per_cq = 12;
inline void ServerConfig::clear_threads_per_cq() {
  threads_per_cq_ = 0;
}
inline ::google::protobuf::int32 ServerConfig::threads_per_cq() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ServerConfig.threads_per_cq)
  return threads_per_cq_;
}
inline void ServerConfig::set_threads_per_cq(::google::protobuf::int32 value) {
  
  threads_per_cq_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ServerConfig.threads_per_cq)
}

// int32 resource_quota_size = 1001;
inline void ServerConfig::clear_resource_quota_size() {
  resource_quota_size_ = 0;
}
inline ::google::protobuf::int32 ServerConfig::resource_quota_size() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ServerConfig.resource_quota_size)
  return resource_quota_size_;
}
inline void ServerConfig::set_resource_quota_size(::google::protobuf::int32 value) {
  
  resource_quota_size_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ServerConfig.resource_quota_size)
}

// repeated .grpc.testing.ChannelArg channel_args = 1002;
inline int ServerConfig::channel_args_size() const {
  return channel_args_.size();
}
inline void ServerConfig::clear_channel_args() {
  channel_args_.Clear();
}
inline const ::grpc::testing::ChannelArg& ServerConfig::channel_args(int index) const {
  // @@protoc_insertion_point(field_get:grpc.testing.ServerConfig.channel_args)
  return channel_args_.Get(index);
}
inline ::grpc::testing::ChannelArg* ServerConfig::mutable_channel_args(int index) {
  // @@protoc_insertion_point(field_mutable:grpc.testing.ServerConfig.channel_args)
  return channel_args_.Mutable(index);
}
inline ::grpc::testing::ChannelArg* ServerConfig::add_channel_args() {
  // @@protoc_insertion_point(field_add:grpc.testing.ServerConfig.channel_args)
  return channel_args_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::grpc::testing::ChannelArg >*
ServerConfig::mutable_channel_args() {
  // @@protoc_insertion_point(field_mutable_list:grpc.testing.ServerConfig.channel_args)
  return &channel_args_;
}
inline const ::google::protobuf::RepeatedPtrField< ::grpc::testing::ChannelArg >&
ServerConfig::channel_args() const {
  // @@protoc_insertion_point(field_list:grpc.testing.ServerConfig.channel_args)
  return channel_args_;
}

// -------------------------------------------------------------------

// ServerArgs

// .grpc.testing.ServerConfig setup = 1;
inline bool ServerArgs::has_setup() const {
  return argtype_case() == kSetup;
}
inline void ServerArgs::set_has_setup() {
  _oneof_case_[0] = kSetup;
}
inline void ServerArgs::clear_setup() {
  if (has_setup()) {
    delete argtype_.setup_;
    clear_has_argtype();
  }
}
inline ::grpc::testing::ServerConfig* ServerArgs::release_setup() {
  // @@protoc_insertion_point(field_release:grpc.testing.ServerArgs.setup)
  if (has_setup()) {
    clear_has_argtype();
      ::grpc::testing::ServerConfig* temp = argtype_.setup_;
    argtype_.setup_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::grpc::testing::ServerConfig& ServerArgs::setup() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ServerArgs.setup)
  return has_setup()
      ? *argtype_.setup_
      : *reinterpret_cast< ::grpc::testing::ServerConfig*>(&::grpc::testing::_ServerConfig_default_instance_);
}
inline ::grpc::testing::ServerConfig* ServerArgs::mutable_setup() {
  if (!has_setup()) {
    clear_argtype();
    set_has_setup();
    argtype_.setup_ = new ::grpc::testing::ServerConfig;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.ServerArgs.setup)
  return argtype_.setup_;
}

// .grpc.testing.Mark mark = 2;
inline bool ServerArgs::has_mark() const {
  return argtype_case() == kMark;
}
inline void ServerArgs::set_has_mark() {
  _oneof_case_[0] = kMark;
}
inline void ServerArgs::clear_mark() {
  if (has_mark()) {
    delete argtype_.mark_;
    clear_has_argtype();
  }
}
inline ::grpc::testing::Mark* ServerArgs::release_mark() {
  // @@protoc_insertion_point(field_release:grpc.testing.ServerArgs.mark)
  if (has_mark()) {
    clear_has_argtype();
      ::grpc::testing::Mark* temp = argtype_.mark_;
    argtype_.mark_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::grpc::testing::Mark& ServerArgs::mark() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ServerArgs.mark)
  return has_mark()
      ? *argtype_.mark_
      : *reinterpret_cast< ::grpc::testing::Mark*>(&::grpc::testing::_Mark_default_instance_);
}
inline ::grpc::testing::Mark* ServerArgs::mutable_mark() {
  if (!has_mark()) {
    clear_argtype();
    set_has_mark();
    argtype_.mark_ = new ::grpc::testing::Mark;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.ServerArgs.mark)
  return argtype_.mark_;
}

inline bool ServerArgs::has_argtype() const {
  return argtype_case() != ARGTYPE_NOT_SET;
}
inline void ServerArgs::clear_has_argtype() {
  _oneof_case_[0] = ARGTYPE_NOT_SET;
}
inline ServerArgs::ArgtypeCase ServerArgs::argtype_case() const {
  return ServerArgs::ArgtypeCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// ServerStatus

// .grpc.testing.ServerStats stats = 1;
inline bool ServerStatus::has_stats() const {
  return this != internal_default_instance() && stats_ != NULL;
}
inline const ::grpc::testing::ServerStats& ServerStatus::stats() const {
  const ::grpc::testing::ServerStats* p = stats_;
  // @@protoc_insertion_point(field_get:grpc.testing.ServerStatus.stats)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::testing::ServerStats*>(
      &::grpc::testing::_ServerStats_default_instance_);
}
inline ::grpc::testing::ServerStats* ServerStatus::release_stats() {
  // @@protoc_insertion_point(field_release:grpc.testing.ServerStatus.stats)
  
  ::grpc::testing::ServerStats* temp = stats_;
  stats_ = NULL;
  return temp;
}
inline ::grpc::testing::ServerStats* ServerStatus::mutable_stats() {
  
  if (stats_ == NULL) {
    stats_ = new ::grpc::testing::ServerStats;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.ServerStatus.stats)
  return stats_;
}
inline void ServerStatus::set_allocated_stats(::grpc::testing::ServerStats* stats) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(stats_);
  }
  if (stats) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      stats = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, stats, submessage_arena);
    }
    
  } else {
    
  }
  stats_ = stats;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.ServerStatus.stats)
}

// int32 port = 2;
inline void ServerStatus::clear_port() {
  port_ = 0;
}
inline ::google::protobuf::int32 ServerStatus::port() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ServerStatus.port)
  return port_;
}
inline void ServerStatus::set_port(::google::protobuf::int32 value) {
  
  port_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ServerStatus.port)
}

// int32 cores = 3;
inline void ServerStatus::clear_cores() {
  cores_ = 0;
}
inline ::google::protobuf::int32 ServerStatus::cores() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ServerStatus.cores)
  return cores_;
}
inline void ServerStatus::set_cores(::google::protobuf::int32 value) {
  
  cores_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ServerStatus.cores)
}

// -------------------------------------------------------------------

// CoreRequest

// -------------------------------------------------------------------

// CoreResponse

// int32 cores = 1;
inline void CoreResponse::clear_cores() {
  cores_ = 0;
}
inline ::google::protobuf::int32 CoreResponse::cores() const {
  // @@protoc_insertion_point(field_get:grpc.testing.CoreResponse.cores)
  return cores_;
}
inline void CoreResponse::set_cores(::google::protobuf::int32 value) {
  
  cores_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.CoreResponse.cores)
}

// -------------------------------------------------------------------

// Void

// -------------------------------------------------------------------

// Scenario

// string name = 1;
inline void Scenario::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Scenario::name() const {
  // @@protoc_insertion_point(field_get:grpc.testing.Scenario.name)
  return name_.GetNoArena();
}
inline void Scenario::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.testing.Scenario.name)
}
#if LANG_CXX11
inline void Scenario::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.testing.Scenario.name)
}
#endif
inline void Scenario::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.testing.Scenario.name)
}
inline void Scenario::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.testing.Scenario.name)
}
inline ::std::string* Scenario::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:grpc.testing.Scenario.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Scenario::release_name() {
  // @@protoc_insertion_point(field_release:grpc.testing.Scenario.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Scenario::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.Scenario.name)
}

// .grpc.testing.ClientConfig client_config = 2;
inline bool Scenario::has_client_config() const {
  return this != internal_default_instance() && client_config_ != NULL;
}
inline void Scenario::clear_client_config() {
  if (GetArenaNoVirtual() == NULL && client_config_ != NULL) {
    delete client_config_;
  }
  client_config_ = NULL;
}
inline const ::grpc::testing::ClientConfig& Scenario::client_config() const {
  const ::grpc::testing::ClientConfig* p = client_config_;
  // @@protoc_insertion_point(field_get:grpc.testing.Scenario.client_config)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::testing::ClientConfig*>(
      &::grpc::testing::_ClientConfig_default_instance_);
}
inline ::grpc::testing::ClientConfig* Scenario::release_client_config() {
  // @@protoc_insertion_point(field_release:grpc.testing.Scenario.client_config)
  
  ::grpc::testing::ClientConfig* temp = client_config_;
  client_config_ = NULL;
  return temp;
}
inline ::grpc::testing::ClientConfig* Scenario::mutable_client_config() {
  
  if (client_config_ == NULL) {
    client_config_ = new ::grpc::testing::ClientConfig;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.Scenario.client_config)
  return client_config_;
}
inline void Scenario::set_allocated_client_config(::grpc::testing::ClientConfig* client_config) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete client_config_;
  }
  if (client_config) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      client_config = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, client_config, submessage_arena);
    }
    
  } else {
    
  }
  client_config_ = client_config;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.Scenario.client_config)
}

// int32 num_clients = 3;
inline void Scenario::clear_num_clients() {
  num_clients_ = 0;
}
inline ::google::protobuf::int32 Scenario::num_clients() const {
  // @@protoc_insertion_point(field_get:grpc.testing.Scenario.num_clients)
  return num_clients_;
}
inline void Scenario::set_num_clients(::google::protobuf::int32 value) {
  
  num_clients_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.Scenario.num_clients)
}

// .grpc.testing.ServerConfig server_config = 4;
inline bool Scenario::has_server_config() const {
  return this != internal_default_instance() && server_config_ != NULL;
}
inline void Scenario::clear_server_config() {
  if (GetArenaNoVirtual() == NULL && server_config_ != NULL) {
    delete server_config_;
  }
  server_config_ = NULL;
}
inline const ::grpc::testing::ServerConfig& Scenario::server_config() const {
  const ::grpc::testing::ServerConfig* p = server_config_;
  // @@protoc_insertion_point(field_get:grpc.testing.Scenario.server_config)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::testing::ServerConfig*>(
      &::grpc::testing::_ServerConfig_default_instance_);
}
inline ::grpc::testing::ServerConfig* Scenario::release_server_config() {
  // @@protoc_insertion_point(field_release:grpc.testing.Scenario.server_config)
  
  ::grpc::testing::ServerConfig* temp = server_config_;
  server_config_ = NULL;
  return temp;
}
inline ::grpc::testing::ServerConfig* Scenario::mutable_server_config() {
  
  if (server_config_ == NULL) {
    server_config_ = new ::grpc::testing::ServerConfig;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.Scenario.server_config)
  return server_config_;
}
inline void Scenario::set_allocated_server_config(::grpc::testing::ServerConfig* server_config) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete server_config_;
  }
  if (server_config) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      server_config = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, server_config, submessage_arena);
    }
    
  } else {
    
  }
  server_config_ = server_config;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.Scenario.server_config)
}

// int32 num_servers = 5;
inline void Scenario::clear_num_servers() {
  num_servers_ = 0;
}
inline ::google::protobuf::int32 Scenario::num_servers() const {
  // @@protoc_insertion_point(field_get:grpc.testing.Scenario.num_servers)
  return num_servers_;
}
inline void Scenario::set_num_servers(::google::protobuf::int32 value) {
  
  num_servers_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.Scenario.num_servers)
}

// int32 warmup_seconds = 6;
inline void Scenario::clear_warmup_seconds() {
  warmup_seconds_ = 0;
}
inline ::google::protobuf::int32 Scenario::warmup_seconds() const {
  // @@protoc_insertion_point(field_get:grpc.testing.Scenario.warmup_seconds)
  return warmup_seconds_;
}
inline void Scenario::set_warmup_seconds(::google::protobuf::int32 value) {
  
  warmup_seconds_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.Scenario.warmup_seconds)
}

// int32 benchmark_seconds = 7;
inline void Scenario::clear_benchmark_seconds() {
  benchmark_seconds_ = 0;
}
inline ::google::protobuf::int32 Scenario::benchmark_seconds() const {
  // @@protoc_insertion_point(field_get:grpc.testing.Scenario.benchmark_seconds)
  return benchmark_seconds_;
}
inline void Scenario::set_benchmark_seconds(::google::protobuf::int32 value) {
  
  benchmark_seconds_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.Scenario.benchmark_seconds)
}

// int32 spawn_local_worker_count = 8;
inline void Scenario::clear_spawn_local_worker_count() {
  spawn_local_worker_count_ = 0;
}
inline ::google::protobuf::int32 Scenario::spawn_local_worker_count() const {
  // @@protoc_insertion_point(field_get:grpc.testing.Scenario.spawn_local_worker_count)
  return spawn_local_worker_count_;
}
inline void Scenario::set_spawn_local_worker_count(::google::protobuf::int32 value) {
  
  spawn_local_worker_count_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.Scenario.spawn_local_worker_count)
}

// -------------------------------------------------------------------

// Scenarios

// repeated .grpc.testing.Scenario scenarios = 1;
inline int Scenarios::scenarios_size() const {
  return scenarios_.size();
}
inline void Scenarios::clear_scenarios() {
  scenarios_.Clear();
}
inline const ::grpc::testing::Scenario& Scenarios::scenarios(int index) const {
  // @@protoc_insertion_point(field_get:grpc.testing.Scenarios.scenarios)
  return scenarios_.Get(index);
}
inline ::grpc::testing::Scenario* Scenarios::mutable_scenarios(int index) {
  // @@protoc_insertion_point(field_mutable:grpc.testing.Scenarios.scenarios)
  return scenarios_.Mutable(index);
}
inline ::grpc::testing::Scenario* Scenarios::add_scenarios() {
  // @@protoc_insertion_point(field_add:grpc.testing.Scenarios.scenarios)
  return scenarios_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::grpc::testing::Scenario >*
Scenarios::mutable_scenarios() {
  // @@protoc_insertion_point(field_mutable_list:grpc.testing.Scenarios.scenarios)
  return &scenarios_;
}
inline const ::google::protobuf::RepeatedPtrField< ::grpc::testing::Scenario >&
Scenarios::scenarios() const {
  // @@protoc_insertion_point(field_list:grpc.testing.Scenarios.scenarios)
  return scenarios_;
}

// -------------------------------------------------------------------

// ScenarioResultSummary

// double qps = 1;
inline void ScenarioResultSummary::clear_qps() {
  qps_ = 0;
}
inline double ScenarioResultSummary::qps() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ScenarioResultSummary.qps)
  return qps_;
}
inline void ScenarioResultSummary::set_qps(double value) {
  
  qps_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ScenarioResultSummary.qps)
}

// double qps_per_server_core = 2;
inline void ScenarioResultSummary::clear_qps_per_server_core() {
  qps_per_server_core_ = 0;
}
inline double ScenarioResultSummary::qps_per_server_core() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ScenarioResultSummary.qps_per_server_core)
  return qps_per_server_core_;
}
inline void ScenarioResultSummary::set_qps_per_server_core(double value) {
  
  qps_per_server_core_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ScenarioResultSummary.qps_per_server_core)
}

// double server_system_time = 3;
inline void ScenarioResultSummary::clear_server_system_time() {
  server_system_time_ = 0;
}
inline double ScenarioResultSummary::server_system_time() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ScenarioResultSummary.server_system_time)
  return server_system_time_;
}
inline void ScenarioResultSummary::set_server_system_time(double value) {
  
  server_system_time_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ScenarioResultSummary.server_system_time)
}

// double server_user_time = 4;
inline void ScenarioResultSummary::clear_server_user_time() {
  server_user_time_ = 0;
}
inline double ScenarioResultSummary::server_user_time() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ScenarioResultSummary.server_user_time)
  return server_user_time_;
}
inline void ScenarioResultSummary::set_server_user_time(double value) {
  
  server_user_time_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ScenarioResultSummary.server_user_time)
}

// double client_system_time = 5;
inline void ScenarioResultSummary::clear_client_system_time() {
  client_system_time_ = 0;
}
inline double ScenarioResultSummary::client_system_time() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ScenarioResultSummary.client_system_time)
  return client_system_time_;
}
inline void ScenarioResultSummary::set_client_system_time(double value) {
  
  client_system_time_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ScenarioResultSummary.client_system_time)
}

// double client_user_time = 6;
inline void ScenarioResultSummary::clear_client_user_time() {
  client_user_time_ = 0;
}
inline double ScenarioResultSummary::client_user_time() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ScenarioResultSummary.client_user_time)
  return client_user_time_;
}
inline void ScenarioResultSummary::set_client_user_time(double value) {
  
  client_user_time_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ScenarioResultSummary.client_user_time)
}

// double latency_50 = 7;
inline void ScenarioResultSummary::clear_latency_50() {
  latency_50_ = 0;
}
inline double ScenarioResultSummary::latency_50() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ScenarioResultSummary.latency_50)
  return latency_50_;
}
inline void ScenarioResultSummary::set_latency_50(double value) {
  
  latency_50_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ScenarioResultSummary.latency_50)
}

// double latency_90 = 8;
inline void ScenarioResultSummary::clear_latency_90() {
  latency_90_ = 0;
}
inline double ScenarioResultSummary::latency_90() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ScenarioResultSummary.latency_90)
  return latency_90_;
}
inline void ScenarioResultSummary::set_latency_90(double value) {
  
  latency_90_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ScenarioResultSummary.latency_90)
}

// double latency_95 = 9;
inline void ScenarioResultSummary::clear_latency_95() {
  latency_95_ = 0;
}
inline double ScenarioResultSummary::latency_95() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ScenarioResultSummary.latency_95)
  return latency_95_;
}
inline void ScenarioResultSummary::set_latency_95(double value) {
  
  latency_95_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ScenarioResultSummary.latency_95)
}

// double latency_99 = 10;
inline void ScenarioResultSummary::clear_latency_99() {
  latency_99_ = 0;
}
inline double ScenarioResultSummary::latency_99() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ScenarioResultSummary.latency_99)
  return latency_99_;
}
inline void ScenarioResultSummary::set_latency_99(double value) {
  
  latency_99_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ScenarioResultSummary.latency_99)
}

// double latency_999 = 11;
inline void ScenarioResultSummary::clear_latency_999() {
  latency_999_ = 0;
}
inline double ScenarioResultSummary::latency_999() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ScenarioResultSummary.latency_999)
  return latency_999_;
}
inline void ScenarioResultSummary::set_latency_999(double value) {
  
  latency_999_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ScenarioResultSummary.latency_999)
}

// double server_cpu_usage = 12;
inline void ScenarioResultSummary::clear_server_cpu_usage() {
  server_cpu_usage_ = 0;
}
inline double ScenarioResultSummary::server_cpu_usage() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ScenarioResultSummary.server_cpu_usage)
  return server_cpu_usage_;
}
inline void ScenarioResultSummary::set_server_cpu_usage(double value) {
  
  server_cpu_usage_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ScenarioResultSummary.server_cpu_usage)
}

// double successful_requests_per_second = 13;
inline void ScenarioResultSummary::clear_successful_requests_per_second() {
  successful_requests_per_second_ = 0;
}
inline double ScenarioResultSummary::successful_requests_per_second() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ScenarioResultSummary.successful_requests_per_second)
  return successful_requests_per_second_;
}
inline void ScenarioResultSummary::set_successful_requests_per_second(double value) {
  
  successful_requests_per_second_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ScenarioResultSummary.successful_requests_per_second)
}

// double failed_requests_per_second = 14;
inline void ScenarioResultSummary::clear_failed_requests_per_second() {
  failed_requests_per_second_ = 0;
}
inline double ScenarioResultSummary::failed_requests_per_second() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ScenarioResultSummary.failed_requests_per_second)
  return failed_requests_per_second_;
}
inline void ScenarioResultSummary::set_failed_requests_per_second(double value) {
  
  failed_requests_per_second_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ScenarioResultSummary.failed_requests_per_second)
}

// double client_polls_per_request = 15;
inline void ScenarioResultSummary::clear_client_polls_per_request() {
  client_polls_per_request_ = 0;
}
inline double ScenarioResultSummary::client_polls_per_request() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ScenarioResultSummary.client_polls_per_request)
  return client_polls_per_request_;
}
inline void ScenarioResultSummary::set_client_polls_per_request(double value) {
  
  client_polls_per_request_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ScenarioResultSummary.client_polls_per_request)
}

// double server_polls_per_request = 16;
inline void ScenarioResultSummary::clear_server_polls_per_request() {
  server_polls_per_request_ = 0;
}
inline double ScenarioResultSummary::server_polls_per_request() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ScenarioResultSummary.server_polls_per_request)
  return server_polls_per_request_;
}
inline void ScenarioResultSummary::set_server_polls_per_request(double value) {
  
  server_polls_per_request_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ScenarioResultSummary.server_polls_per_request)
}

// double server_queries_per_cpu_sec = 17;
inline void ScenarioResultSummary::clear_server_queries_per_cpu_sec() {
  server_queries_per_cpu_sec_ = 0;
}
inline double ScenarioResultSummary::server_queries_per_cpu_sec() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ScenarioResultSummary.server_queries_per_cpu_sec)
  return server_queries_per_cpu_sec_;
}
inline void ScenarioResultSummary::set_server_queries_per_cpu_sec(double value) {
  
  server_queries_per_cpu_sec_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ScenarioResultSummary.server_queries_per_cpu_sec)
}

// double client_queries_per_cpu_sec = 18;
inline void ScenarioResultSummary::clear_client_queries_per_cpu_sec() {
  client_queries_per_cpu_sec_ = 0;
}
inline double ScenarioResultSummary::client_queries_per_cpu_sec() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ScenarioResultSummary.client_queries_per_cpu_sec)
  return client_queries_per_cpu_sec_;
}
inline void ScenarioResultSummary::set_client_queries_per_cpu_sec(double value) {
  
  client_queries_per_cpu_sec_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ScenarioResultSummary.client_queries_per_cpu_sec)
}

// -------------------------------------------------------------------

// ScenarioResult

// .grpc.testing.Scenario scenario = 1;
inline bool ScenarioResult::has_scenario() const {
  return this != internal_default_instance() && scenario_ != NULL;
}
inline void ScenarioResult::clear_scenario() {
  if (GetArenaNoVirtual() == NULL && scenario_ != NULL) {
    delete scenario_;
  }
  scenario_ = NULL;
}
inline const ::grpc::testing::Scenario& ScenarioResult::scenario() const {
  const ::grpc::testing::Scenario* p = scenario_;
  // @@protoc_insertion_point(field_get:grpc.testing.ScenarioResult.scenario)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::testing::Scenario*>(
      &::grpc::testing::_Scenario_default_instance_);
}
inline ::grpc::testing::Scenario* ScenarioResult::release_scenario() {
  // @@protoc_insertion_point(field_release:grpc.testing.ScenarioResult.scenario)
  
  ::grpc::testing::Scenario* temp = scenario_;
  scenario_ = NULL;
  return temp;
}
inline ::grpc::testing::Scenario* ScenarioResult::mutable_scenario() {
  
  if (scenario_ == NULL) {
    scenario_ = new ::grpc::testing::Scenario;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.ScenarioResult.scenario)
  return scenario_;
}
inline void ScenarioResult::set_allocated_scenario(::grpc::testing::Scenario* scenario) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete scenario_;
  }
  if (scenario) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      scenario = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, scenario, submessage_arena);
    }
    
  } else {
    
  }
  scenario_ = scenario;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.ScenarioResult.scenario)
}

// .grpc.testing.HistogramData latencies = 2;
inline bool ScenarioResult::has_latencies() const {
  return this != internal_default_instance() && latencies_ != NULL;
}
inline const ::grpc::testing::HistogramData& ScenarioResult::latencies() const {
  const ::grpc::testing::HistogramData* p = latencies_;
  // @@protoc_insertion_point(field_get:grpc.testing.ScenarioResult.latencies)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::testing::HistogramData*>(
      &::grpc::testing::_HistogramData_default_instance_);
}
inline ::grpc::testing::HistogramData* ScenarioResult::release_latencies() {
  // @@protoc_insertion_point(field_release:grpc.testing.ScenarioResult.latencies)
  
  ::grpc::testing::HistogramData* temp = latencies_;
  latencies_ = NULL;
  return temp;
}
inline ::grpc::testing::HistogramData* ScenarioResult::mutable_latencies() {
  
  if (latencies_ == NULL) {
    latencies_ = new ::grpc::testing::HistogramData;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.ScenarioResult.latencies)
  return latencies_;
}
inline void ScenarioResult::set_allocated_latencies(::grpc::testing::HistogramData* latencies) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(latencies_);
  }
  if (latencies) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      latencies = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, latencies, submessage_arena);
    }
    
  } else {
    
  }
  latencies_ = latencies;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.ScenarioResult.latencies)
}

// repeated .grpc.testing.ClientStats client_stats = 3;
inline int ScenarioResult::client_stats_size() const {
  return client_stats_.size();
}
inline const ::grpc::testing::ClientStats& ScenarioResult::client_stats(int index) const {
  // @@protoc_insertion_point(field_get:grpc.testing.ScenarioResult.client_stats)
  return client_stats_.Get(index);
}
inline ::grpc::testing::ClientStats* ScenarioResult::mutable_client_stats(int index) {
  // @@protoc_insertion_point(field_mutable:grpc.testing.ScenarioResult.client_stats)
  return client_stats_.Mutable(index);
}
inline ::grpc::testing::ClientStats* ScenarioResult::add_client_stats() {
  // @@protoc_insertion_point(field_add:grpc.testing.ScenarioResult.client_stats)
  return client_stats_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::grpc::testing::ClientStats >*
ScenarioResult::mutable_client_stats() {
  // @@protoc_insertion_point(field_mutable_list:grpc.testing.ScenarioResult.client_stats)
  return &client_stats_;
}
inline const ::google::protobuf::RepeatedPtrField< ::grpc::testing::ClientStats >&
ScenarioResult::client_stats() const {
  // @@protoc_insertion_point(field_list:grpc.testing.ScenarioResult.client_stats)
  return client_stats_;
}

// repeated .grpc.testing.ServerStats server_stats = 4;
inline int ScenarioResult::server_stats_size() const {
  return server_stats_.size();
}
inline const ::grpc::testing::ServerStats& ScenarioResult::server_stats(int index) const {
  // @@protoc_insertion_point(field_get:grpc.testing.ScenarioResult.server_stats)
  return server_stats_.Get(index);
}
inline ::grpc::testing::ServerStats* ScenarioResult::mutable_server_stats(int index) {
  // @@protoc_insertion_point(field_mutable:grpc.testing.ScenarioResult.server_stats)
  return server_stats_.Mutable(index);
}
inline ::grpc::testing::ServerStats* ScenarioResult::add_server_stats() {
  // @@protoc_insertion_point(field_add:grpc.testing.ScenarioResult.server_stats)
  return server_stats_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::grpc::testing::ServerStats >*
ScenarioResult::mutable_server_stats() {
  // @@protoc_insertion_point(field_mutable_list:grpc.testing.ScenarioResult.server_stats)
  return &server_stats_;
}
inline const ::google::protobuf::RepeatedPtrField< ::grpc::testing::ServerStats >&
ScenarioResult::server_stats() const {
  // @@protoc_insertion_point(field_list:grpc.testing.ScenarioResult.server_stats)
  return server_stats_;
}

// repeated int32 server_cores = 5;
inline int ScenarioResult::server_cores_size() const {
  return server_cores_.size();
}
inline void ScenarioResult::clear_server_cores() {
  server_cores_.Clear();
}
inline ::google::protobuf::int32 ScenarioResult::server_cores(int index) const {
  // @@protoc_insertion_point(field_get:grpc.testing.ScenarioResult.server_cores)
  return server_cores_.Get(index);
}
inline void ScenarioResult::set_server_cores(int index, ::google::protobuf::int32 value) {
  server_cores_.Set(index, value);
  // @@protoc_insertion_point(field_set:grpc.testing.ScenarioResult.server_cores)
}
inline void ScenarioResult::add_server_cores(::google::protobuf::int32 value) {
  server_cores_.Add(value);
  // @@protoc_insertion_point(field_add:grpc.testing.ScenarioResult.server_cores)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
ScenarioResult::server_cores() const {
  // @@protoc_insertion_point(field_list:grpc.testing.ScenarioResult.server_cores)
  return server_cores_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
ScenarioResult::mutable_server_cores() {
  // @@protoc_insertion_point(field_mutable_list:grpc.testing.ScenarioResult.server_cores)
  return &server_cores_;
}

// .grpc.testing.ScenarioResultSummary summary = 6;
inline bool ScenarioResult::has_summary() const {
  return this != internal_default_instance() && summary_ != NULL;
}
inline void ScenarioResult::clear_summary() {
  if (GetArenaNoVirtual() == NULL && summary_ != NULL) {
    delete summary_;
  }
  summary_ = NULL;
}
inline const ::grpc::testing::ScenarioResultSummary& ScenarioResult::summary() const {
  const ::grpc::testing::ScenarioResultSummary* p = summary_;
  // @@protoc_insertion_point(field_get:grpc.testing.ScenarioResult.summary)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::testing::ScenarioResultSummary*>(
      &::grpc::testing::_ScenarioResultSummary_default_instance_);
}
inline ::grpc::testing::ScenarioResultSummary* ScenarioResult::release_summary() {
  // @@protoc_insertion_point(field_release:grpc.testing.ScenarioResult.summary)
  
  ::grpc::testing::ScenarioResultSummary* temp = summary_;
  summary_ = NULL;
  return temp;
}
inline ::grpc::testing::ScenarioResultSummary* ScenarioResult::mutable_summary() {
  
  if (summary_ == NULL) {
    summary_ = new ::grpc::testing::ScenarioResultSummary;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.ScenarioResult.summary)
  return summary_;
}
inline void ScenarioResult::set_allocated_summary(::grpc::testing::ScenarioResultSummary* summary) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete summary_;
  }
  if (summary) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      summary = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, summary, submessage_arena);
    }
    
  } else {
    
  }
  summary_ = summary;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.ScenarioResult.summary)
}

// repeated bool client_success = 7;
inline int ScenarioResult::client_success_size() const {
  return client_success_.size();
}
inline void ScenarioResult::clear_client_success() {
  client_success_.Clear();
}
inline bool ScenarioResult::client_success(int index) const {
  // @@protoc_insertion_point(field_get:grpc.testing.ScenarioResult.client_success)
  return client_success_.Get(index);
}
inline void ScenarioResult::set_client_success(int index, bool value) {
  client_success_.Set(index, value);
  // @@protoc_insertion_point(field_set:grpc.testing.ScenarioResult.client_success)
}
inline void ScenarioResult::add_client_success(bool value) {
  client_success_.Add(value);
  // @@protoc_insertion_point(field_add:grpc.testing.ScenarioResult.client_success)
}
inline const ::google::protobuf::RepeatedField< bool >&
ScenarioResult::client_success() const {
  // @@protoc_insertion_point(field_list:grpc.testing.ScenarioResult.client_success)
  return client_success_;
}
inline ::google::protobuf::RepeatedField< bool >*
ScenarioResult::mutable_client_success() {
  // @@protoc_insertion_point(field_mutable_list:grpc.testing.ScenarioResult.client_success)
  return &client_success_;
}

// repeated bool server_success = 8;
inline int ScenarioResult::server_success_size() const {
  return server_success_.size();
}
inline void ScenarioResult::clear_server_success() {
  server_success_.Clear();
}
inline bool ScenarioResult::server_success(int index) const {
  // @@protoc_insertion_point(field_get:grpc.testing.ScenarioResult.server_success)
  return server_success_.Get(index);
}
inline void ScenarioResult::set_server_success(int index, bool value) {
  server_success_.Set(index, value);
  // @@protoc_insertion_point(field_set:grpc.testing.ScenarioResult.server_success)
}
inline void ScenarioResult::add_server_success(bool value) {
  server_success_.Add(value);
  // @@protoc_insertion_point(field_add:grpc.testing.ScenarioResult.server_success)
}
inline const ::google::protobuf::RepeatedField< bool >&
ScenarioResult::server_success() const {
  // @@protoc_insertion_point(field_list:grpc.testing.ScenarioResult.server_success)
  return server_success_;
}
inline ::google::protobuf::RepeatedField< bool >*
ScenarioResult::mutable_server_success() {
  // @@protoc_insertion_point(field_mutable_list:grpc.testing.ScenarioResult.server_success)
  return &server_success_;
}

// repeated .grpc.testing.RequestResultCount request_results = 9;
inline int ScenarioResult::request_results_size() const {
  return request_results_.size();
}
inline const ::grpc::testing::RequestResultCount& ScenarioResult::request_results(int index) const {
  // @@protoc_insertion_point(field_get:grpc.testing.ScenarioResult.request_results)
  return request_results_.Get(index);
}
inline ::grpc::testing::RequestResultCount* ScenarioResult::mutable_request_results(int index) {
  // @@protoc_insertion_point(field_mutable:grpc.testing.ScenarioResult.request_results)
  return request_results_.Mutable(index);
}
inline ::grpc::testing::RequestResultCount* ScenarioResult::add_request_results() {
  // @@protoc_insertion_point(field_add:grpc.testing.ScenarioResult.request_results)
  return request_results_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::grpc::testing::RequestResultCount >*
ScenarioResult::mutable_request_results() {
  // @@protoc_insertion_point(field_mutable_list:grpc.testing.ScenarioResult.request_results)
  return &request_results_;
}
inline const ::google::protobuf::RepeatedPtrField< ::grpc::testing::RequestResultCount >&
ScenarioResult::request_results() const {
  // @@protoc_insertion_point(field_list:grpc.testing.ScenarioResult.request_results)
  return request_results_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace testing
}  // namespace grpc

namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::grpc::testing::ClientType> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::grpc::testing::ClientType>() {
  return ::grpc::testing::ClientType_descriptor();
}
template <> struct is_proto_enum< ::grpc::testing::ServerType> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::grpc::testing::ServerType>() {
  return ::grpc::testing::ServerType_descriptor();
}
template <> struct is_proto_enum< ::grpc::testing::RpcType> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::grpc::testing::RpcType>() {
  return ::grpc::testing::RpcType_descriptor();
}

}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_src_2fproto_2fgrpc_2ftesting_2fcontrol_2eproto__INCLUDED
