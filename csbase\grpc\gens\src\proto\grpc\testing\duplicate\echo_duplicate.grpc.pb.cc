// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: src/proto/grpc/testing/duplicate/echo_duplicate.proto

#include "src/proto/grpc/testing/duplicate/echo_duplicate.pb.h"
#include "src/proto/grpc/testing/duplicate/echo_duplicate.grpc.pb.h"

#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/method_handler_impl.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace grpc {
namespace testing {
namespace duplicate {

static const char* EchoTestService_method_names[] = {
  "/grpc.testing.duplicate.EchoTestService/Echo",
  "/grpc.testing.duplicate.EchoTestService/ResponseStream",
};

std::unique_ptr< EchoTestService::Stub> EchoTestService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< EchoTestService::Stub> stub(new EchoTestService::Stub(channel));
  return stub;
}

EchoTestService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel)
  : channel_(channel), rpcmethod_Echo_(EchoTestService_method_names[0], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ResponseStream_(EchoTestService_method_names[1], ::grpc::internal::RpcMethod::SERVER_STREAMING, channel)
  {}

::grpc::Status EchoTestService::Stub::Echo(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::testing::EchoResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_Echo_, context, request, response);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::EchoResponse>* EchoTestService::Stub::AsyncEchoRaw(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::EchoResponse>::Create(channel_.get(), cq, rpcmethod_Echo_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::EchoResponse>* EchoTestService::Stub::PrepareAsyncEchoRaw(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::EchoResponse>::Create(channel_.get(), cq, rpcmethod_Echo_, context, request, false);
}

::grpc::ClientReader< ::grpc::testing::EchoResponse>* EchoTestService::Stub::ResponseStreamRaw(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request) {
  return ::grpc::internal::ClientReaderFactory< ::grpc::testing::EchoResponse>::Create(channel_.get(), rpcmethod_ResponseStream_, context, request);
}

::grpc::ClientAsyncReader< ::grpc::testing::EchoResponse>* EchoTestService::Stub::AsyncResponseStreamRaw(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncReaderFactory< ::grpc::testing::EchoResponse>::Create(channel_.get(), cq, rpcmethod_ResponseStream_, context, request, true, tag);
}

::grpc::ClientAsyncReader< ::grpc::testing::EchoResponse>* EchoTestService::Stub::PrepareAsyncResponseStreamRaw(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncReaderFactory< ::grpc::testing::EchoResponse>::Create(channel_.get(), cq, rpcmethod_ResponseStream_, context, request, false, nullptr);
}

EchoTestService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      EchoTestService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< EchoTestService::Service, ::grpc::testing::EchoRequest, ::grpc::testing::EchoResponse>(
          std::mem_fn(&EchoTestService::Service::Echo), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      EchoTestService_method_names[1],
      ::grpc::internal::RpcMethod::SERVER_STREAMING,
      new ::grpc::internal::ServerStreamingHandler< EchoTestService::Service, ::grpc::testing::EchoRequest, ::grpc::testing::EchoResponse>(
          std::mem_fn(&EchoTestService::Service::ResponseStream), this)));
}

EchoTestService::Service::~Service() {
}

::grpc::Status EchoTestService::Service::Echo(::grpc::ServerContext* context, const ::grpc::testing::EchoRequest* request, ::grpc::testing::EchoResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status EchoTestService::Service::ResponseStream(::grpc::ServerContext* context, const ::grpc::testing::EchoRequest* request, ::grpc::ServerWriter< ::grpc::testing::EchoResponse>* writer) {
  (void) context;
  (void) request;
  (void) writer;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace grpc
}  // namespace testing
}  // namespace duplicate

