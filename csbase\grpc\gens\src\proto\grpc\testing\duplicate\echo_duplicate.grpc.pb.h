// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: src/proto/grpc/testing/duplicate/echo_duplicate.proto
// Original file comments:
// Copyright 2015 gRPC authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// This is a partial copy of echo.proto with a different package name.
//
#ifndef GRPC_src_2fproto_2fgrpc_2ftesting_2fduplicate_2fecho_5fduplicate_2eproto__INCLUDED
#define GRPC_src_2fproto_2fgrpc_2ftesting_2fduplicate_2fecho_5fduplicate_2eproto__INCLUDED

#include "src/proto/grpc/testing/duplicate/echo_duplicate.pb.h"

#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/method_handler_impl.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace grpc {
class CompletionQueue;
class Channel;
class ServerCompletionQueue;
class ServerContext;
}  // namespace grpc

namespace grpc {
namespace testing {
namespace duplicate {

class EchoTestService final {
 public:
  static constexpr char const* service_full_name() {
    return "grpc.testing.duplicate.EchoTestService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status Echo(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::testing::EchoResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::EchoResponse>> AsyncEcho(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::EchoResponse>>(AsyncEchoRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::EchoResponse>> PrepareAsyncEcho(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::EchoResponse>>(PrepareAsyncEchoRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientReaderInterface< ::grpc::testing::EchoResponse>> ResponseStream(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request) {
      return std::unique_ptr< ::grpc::ClientReaderInterface< ::grpc::testing::EchoResponse>>(ResponseStreamRaw(context, request));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::grpc::testing::EchoResponse>> AsyncResponseStream(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::grpc::testing::EchoResponse>>(AsyncResponseStreamRaw(context, request, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::grpc::testing::EchoResponse>> PrepareAsyncResponseStream(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::grpc::testing::EchoResponse>>(PrepareAsyncResponseStreamRaw(context, request, cq));
    }
  private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::EchoResponse>* AsyncEchoRaw(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::EchoResponse>* PrepareAsyncEchoRaw(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientReaderInterface< ::grpc::testing::EchoResponse>* ResponseStreamRaw(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request) = 0;
    virtual ::grpc::ClientAsyncReaderInterface< ::grpc::testing::EchoResponse>* AsyncResponseStreamRaw(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq, void* tag) = 0;
    virtual ::grpc::ClientAsyncReaderInterface< ::grpc::testing::EchoResponse>* PrepareAsyncResponseStreamRaw(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel);
    ::grpc::Status Echo(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::testing::EchoResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::EchoResponse>> AsyncEcho(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::EchoResponse>>(AsyncEchoRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::EchoResponse>> PrepareAsyncEcho(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::EchoResponse>>(PrepareAsyncEchoRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientReader< ::grpc::testing::EchoResponse>> ResponseStream(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request) {
      return std::unique_ptr< ::grpc::ClientReader< ::grpc::testing::EchoResponse>>(ResponseStreamRaw(context, request));
    }
    std::unique_ptr< ::grpc::ClientAsyncReader< ::grpc::testing::EchoResponse>> AsyncResponseStream(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReader< ::grpc::testing::EchoResponse>>(AsyncResponseStreamRaw(context, request, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReader< ::grpc::testing::EchoResponse>> PrepareAsyncResponseStream(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReader< ::grpc::testing::EchoResponse>>(PrepareAsyncResponseStreamRaw(context, request, cq));
    }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    ::grpc::ClientAsyncResponseReader< ::grpc::testing::EchoResponse>* AsyncEchoRaw(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::testing::EchoResponse>* PrepareAsyncEchoRaw(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientReader< ::grpc::testing::EchoResponse>* ResponseStreamRaw(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request) override;
    ::grpc::ClientAsyncReader< ::grpc::testing::EchoResponse>* AsyncResponseStreamRaw(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq, void* tag) override;
    ::grpc::ClientAsyncReader< ::grpc::testing::EchoResponse>* PrepareAsyncResponseStreamRaw(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_Echo_;
    const ::grpc::internal::RpcMethod rpcmethod_ResponseStream_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status Echo(::grpc::ServerContext* context, const ::grpc::testing::EchoRequest* request, ::grpc::testing::EchoResponse* response);
    virtual ::grpc::Status ResponseStream(::grpc::ServerContext* context, const ::grpc::testing::EchoRequest* request, ::grpc::ServerWriter< ::grpc::testing::EchoResponse>* writer);
  };
  template <class BaseClass>
  class WithAsyncMethod_Echo : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_Echo() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_Echo() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Echo(::grpc::ServerContext* context, const ::grpc::testing::EchoRequest* request, ::grpc::testing::EchoResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestEcho(::grpc::ServerContext* context, ::grpc::testing::EchoRequest* request, ::grpc::ServerAsyncResponseWriter< ::grpc::testing::EchoResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_ResponseStream : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_ResponseStream() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_ResponseStream() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ResponseStream(::grpc::ServerContext* context, const ::grpc::testing::EchoRequest* request, ::grpc::ServerWriter< ::grpc::testing::EchoResponse>* writer) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestResponseStream(::grpc::ServerContext* context, ::grpc::testing::EchoRequest* request, ::grpc::ServerAsyncWriter< ::grpc::testing::EchoResponse>* writer, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncServerStreaming(1, context, request, writer, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_Echo<WithAsyncMethod_ResponseStream<Service > > AsyncService;
  template <class BaseClass>
  class WithGenericMethod_Echo : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_Echo() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_Echo() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Echo(::grpc::ServerContext* context, const ::grpc::testing::EchoRequest* request, ::grpc::testing::EchoResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_ResponseStream : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_ResponseStream() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_ResponseStream() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ResponseStream(::grpc::ServerContext* context, const ::grpc::testing::EchoRequest* request, ::grpc::ServerWriter< ::grpc::testing::EchoResponse>* writer) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_Echo : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_Echo() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_Echo() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Echo(::grpc::ServerContext* context, const ::grpc::testing::EchoRequest* request, ::grpc::testing::EchoResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestEcho(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_ResponseStream : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_ResponseStream() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_ResponseStream() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ResponseStream(::grpc::ServerContext* context, const ::grpc::testing::EchoRequest* request, ::grpc::ServerWriter< ::grpc::testing::EchoResponse>* writer) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestResponseStream(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncWriter< ::grpc::ByteBuffer>* writer, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncServerStreaming(1, context, request, writer, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_Echo : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithStreamedUnaryMethod_Echo() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler< ::grpc::testing::EchoRequest, ::grpc::testing::EchoResponse>(std::bind(&WithStreamedUnaryMethod_Echo<BaseClass>::StreamedEcho, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_Echo() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status Echo(::grpc::ServerContext* context, const ::grpc::testing::EchoRequest* request, ::grpc::testing::EchoResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedEcho(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::grpc::testing::EchoRequest,::grpc::testing::EchoResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_Echo<Service > StreamedUnaryService;
  template <class BaseClass>
  class WithSplitStreamingMethod_ResponseStream : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithSplitStreamingMethod_ResponseStream() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::SplitServerStreamingHandler< ::grpc::testing::EchoRequest, ::grpc::testing::EchoResponse>(std::bind(&WithSplitStreamingMethod_ResponseStream<BaseClass>::StreamedResponseStream, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithSplitStreamingMethod_ResponseStream() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status ResponseStream(::grpc::ServerContext* context, const ::grpc::testing::EchoRequest* request, ::grpc::ServerWriter< ::grpc::testing::EchoResponse>* writer) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with split streamed
    virtual ::grpc::Status StreamedResponseStream(::grpc::ServerContext* context, ::grpc::ServerSplitStreamer< ::grpc::testing::EchoRequest,::grpc::testing::EchoResponse>* server_split_streamer) = 0;
  };
  typedef WithSplitStreamingMethod_ResponseStream<Service > SplitStreamedService;
  typedef WithStreamedUnaryMethod_Echo<WithSplitStreamingMethod_ResponseStream<Service > > StreamedService;
};

}  // namespace duplicate
}  // namespace testing
}  // namespace grpc


#endif  // GRPC_src_2fproto_2fgrpc_2ftesting_2fduplicate_2fecho_5fduplicate_2eproto__INCLUDED
