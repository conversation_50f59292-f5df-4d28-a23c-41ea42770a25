// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: src/proto/grpc/testing/duplicate/echo_duplicate.proto

#include "src/proto/grpc/testing/duplicate/echo_duplicate.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)
namespace grpc {
namespace testing {
namespace duplicate {
}  // namespace duplicate
}  // namespace testing
}  // namespace grpc
namespace protobuf_src_2fproto_2fgrpc_2ftesting_2fduplicate_2fecho_5fduplicate_2eproto {
const ::google::protobuf::uint32 TableStruct::offsets[1] = {};
static const ::google::protobuf::internal::MigrationSchema* schemas = NULL;
static const ::google::protobuf::Message* const* file_default_instances = NULL;

void protobuf_AssignDescriptors() {
  AddDescriptors();
  ::google::protobuf::MessageFactory* factory = NULL;
  AssignDescriptors(
      "src/proto/grpc/testing/duplicate/echo_duplicate.proto", schemas, file_default_instances, TableStruct::offsets, factory,
      NULL, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n5src/proto/grpc/testing/duplicate/echo_"
      "duplicate.proto\022\026grpc.testing.duplicate\032"
      "*src/proto/grpc/testing/echo_messages.pr"
      "oto2\233\001\n\017EchoTestService\022=\n\004Echo\022\031.grpc.t"
      "esting.EchoRequest\032\032.grpc.testing.EchoRe"
      "sponse\022I\n\016ResponseStream\022\031.grpc.testing."
      "EchoRequest\032\032.grpc.testing.EchoResponse0"
      "\001b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 289);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "src/proto/grpc/testing/duplicate/echo_duplicate.proto", &protobuf_RegisterTypes);
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_src_2fproto_2fgrpc_2ftesting_2fduplicate_2fecho_5fduplicate_2eproto
namespace grpc {
namespace testing {
namespace duplicate {

// @@protoc_insertion_point(namespace_scope)
}  // namespace duplicate
}  // namespace testing
}  // namespace grpc

// @@protoc_insertion_point(global_scope)
