// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: src/proto/grpc/testing/duplicate/echo_duplicate.proto

#include "src/proto/grpc/testing/duplicate/echo_duplicate.pb.h"
#include "src/proto/grpc/testing/duplicate/echo_duplicate.grpc.pb.h"

#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/sync_stream.h>
#include <gmock/gmock.h>
namespace grpc {
namespace testing {
namespace duplicate {

class MockEchoTestServiceStub : public EchoTestService::StubInterface {
 public:
  MOCK_METHOD3(Echo, ::grpc::Status(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::testing::EchoResponse* response));
  MOCK_METHOD3(AsyncEchoRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::EchoResponse>*(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncEchoRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::EchoResponse>*(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD2(ResponseStreamRaw, ::grpc::ClientReaderInterface< ::grpc::testing::EchoResponse>*(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request));
  MOCK_METHOD4(AsyncResponseStreamRaw, ::grpc::ClientAsyncReaderInterface< ::grpc::testing::EchoResponse>*(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq, void* tag));
  MOCK_METHOD3(PrepareAsyncResponseStreamRaw, ::grpc::ClientAsyncReaderInterface< ::grpc::testing::EchoResponse>*(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq));
};

} // namespace grpc
} // namespace testing
} // namespace duplicate

