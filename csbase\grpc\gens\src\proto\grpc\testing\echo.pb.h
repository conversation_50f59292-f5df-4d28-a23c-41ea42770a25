// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: src/proto/grpc/testing/echo.proto

#ifndef PROTOBUF_src_2fproto_2fgrpc_2ftesting_2fecho_2eproto__INCLUDED
#define PROTOBUF_src_2fproto_2fgrpc_2ftesting_2fecho_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3005001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include "src/proto/grpc/testing/echo_messages.pb.h"
// @@protoc_insertion_point(includes)

namespace protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[1];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
inline void InitDefaults() {
}
}  // namespace protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_2eproto
namespace grpc {
namespace testing {
}  // namespace testing
}  // namespace grpc
namespace grpc {
namespace testing {

// ===================================================================


// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace testing
}  // namespace grpc

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_src_2fproto_2fgrpc_2ftesting_2fecho_2eproto__INCLUDED
