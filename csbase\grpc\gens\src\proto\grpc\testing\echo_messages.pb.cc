// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: src/proto/grpc/testing/echo_messages.proto

#include "src/proto/grpc/testing/echo_messages.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)
namespace grpc {
namespace testing {
class DebugInfoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<DebugInfo>
      _instance;
} _DebugInfo_default_instance_;
class ErrorStatusDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ErrorStatus>
      _instance;
} _ErrorStatus_default_instance_;
class RequestParamsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<RequestParams>
      _instance;
} _RequestParams_default_instance_;
class EchoRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<EchoRequest>
      _instance;
} _EchoRequest_default_instance_;
class ResponseParamsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ResponseParams>
      _instance;
} _ResponseParams_default_instance_;
class EchoResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<EchoResponse>
      _instance;
} _EchoResponse_default_instance_;
}  // namespace testing
}  // namespace grpc
namespace protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto {
void InitDefaultsDebugInfoImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::testing::_DebugInfo_default_instance_;
    new (ptr) ::grpc::testing::DebugInfo();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::DebugInfo::InitAsDefaultInstance();
}

void InitDefaultsDebugInfo() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsDebugInfoImpl);
}

void InitDefaultsErrorStatusImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::testing::_ErrorStatus_default_instance_;
    new (ptr) ::grpc::testing::ErrorStatus();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::ErrorStatus::InitAsDefaultInstance();
}

void InitDefaultsErrorStatus() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsErrorStatusImpl);
}

void InitDefaultsRequestParamsImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::InitDefaultsDebugInfo();
  protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::InitDefaultsErrorStatus();
  {
    void* ptr = &::grpc::testing::_RequestParams_default_instance_;
    new (ptr) ::grpc::testing::RequestParams();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::RequestParams::InitAsDefaultInstance();
}

void InitDefaultsRequestParams() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsRequestParamsImpl);
}

void InitDefaultsEchoRequestImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::InitDefaultsRequestParams();
  {
    void* ptr = &::grpc::testing::_EchoRequest_default_instance_;
    new (ptr) ::grpc::testing::EchoRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::EchoRequest::InitAsDefaultInstance();
}

void InitDefaultsEchoRequest() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsEchoRequestImpl);
}

void InitDefaultsResponseParamsImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::testing::_ResponseParams_default_instance_;
    new (ptr) ::grpc::testing::ResponseParams();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::ResponseParams::InitAsDefaultInstance();
}

void InitDefaultsResponseParams() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsResponseParamsImpl);
}

void InitDefaultsEchoResponseImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::InitDefaultsResponseParams();
  {
    void* ptr = &::grpc::testing::_EchoResponse_default_instance_;
    new (ptr) ::grpc::testing::EchoResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::EchoResponse::InitAsDefaultInstance();
}

void InitDefaultsEchoResponse() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsEchoResponseImpl);
}

::google::protobuf::Metadata file_level_metadata[6];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::DebugInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::DebugInfo, stack_entries_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::DebugInfo, detail_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ErrorStatus, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ErrorStatus, code_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ErrorStatus, error_message_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ErrorStatus, binary_error_details_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::RequestParams, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::RequestParams, echo_deadline_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::RequestParams, client_cancel_after_us_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::RequestParams, server_cancel_after_us_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::RequestParams, echo_metadata_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::RequestParams, check_auth_context_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::RequestParams, response_message_length_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::RequestParams, echo_peer_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::RequestParams, expected_client_identity_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::RequestParams, skip_cancelled_check_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::RequestParams, expected_transport_security_type_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::RequestParams, debug_info_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::RequestParams, server_die_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::RequestParams, binary_error_details_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::RequestParams, expected_error_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::RequestParams, server_sleep_us_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::EchoRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::EchoRequest, message_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::EchoRequest, param_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ResponseParams, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ResponseParams, request_deadline_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ResponseParams, host_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ResponseParams, peer_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::EchoResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::EchoResponse, message_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::EchoResponse, param_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::grpc::testing::DebugInfo)},
  { 7, -1, sizeof(::grpc::testing::ErrorStatus)},
  { 15, -1, sizeof(::grpc::testing::RequestParams)},
  { 35, -1, sizeof(::grpc::testing::EchoRequest)},
  { 42, -1, sizeof(::grpc::testing::ResponseParams)},
  { 50, -1, sizeof(::grpc::testing::EchoResponse)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_DebugInfo_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_ErrorStatus_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_RequestParams_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_EchoRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_ResponseParams_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_EchoResponse_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  ::google::protobuf::MessageFactory* factory = NULL;
  AssignDescriptors(
      "src/proto/grpc/testing/echo_messages.proto", schemas, file_default_instances, TableStruct::offsets, factory,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 6);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n*src/proto/grpc/testing/echo_messages.p"
      "roto\022\014grpc.testing\"2\n\tDebugInfo\022\025\n\rstack"
      "_entries\030\001 \003(\t\022\016\n\006detail\030\002 \001(\t\"P\n\013ErrorS"
      "tatus\022\014\n\004code\030\001 \001(\005\022\025\n\rerror_message\030\002 \001"
      "(\t\022\034\n\024binary_error_details\030\003 \001(\t\"\342\003\n\rReq"
      "uestParams\022\025\n\recho_deadline\030\001 \001(\010\022\036\n\026cli"
      "ent_cancel_after_us\030\002 \001(\005\022\036\n\026server_canc"
      "el_after_us\030\003 \001(\005\022\025\n\recho_metadata\030\004 \001(\010"
      "\022\032\n\022check_auth_context\030\005 \001(\010\022\037\n\027response"
      "_message_length\030\006 \001(\005\022\021\n\techo_peer\030\007 \001(\010"
      "\022 \n\030expected_client_identity\030\010 \001(\t\022\034\n\024sk"
      "ip_cancelled_check\030\t \001(\010\022(\n expected_tra"
      "nsport_security_type\030\n \001(\t\022+\n\ndebug_info"
      "\030\013 \001(\0132\027.grpc.testing.DebugInfo\022\022\n\nserve"
      "r_die\030\014 \001(\010\022\034\n\024binary_error_details\030\r \001("
      "\t\0221\n\016expected_error\030\016 \001(\0132\031.grpc.testing"
      ".ErrorStatus\022\027\n\017server_sleep_us\030\017 \001(\005\"J\n"
      "\013EchoRequest\022\017\n\007message\030\001 \001(\t\022*\n\005param\030\002"
      " \001(\0132\033.grpc.testing.RequestParams\"F\n\016Res"
      "ponseParams\022\030\n\020request_deadline\030\001 \001(\003\022\014\n"
      "\004host\030\002 \001(\t\022\014\n\004peer\030\003 \001(\t\"L\n\014EchoRespons"
      "e\022\017\n\007message\030\001 \001(\t\022+\n\005param\030\002 \001(\0132\034.grpc"
      ".testing.ResponseParamsb\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 911);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "src/proto/grpc/testing/echo_messages.proto", &protobuf_RegisterTypes);
}

void AddDescriptors() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto
namespace grpc {
namespace testing {

// ===================================================================

void DebugInfo::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int DebugInfo::kStackEntriesFieldNumber;
const int DebugInfo::kDetailFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

DebugInfo::DebugInfo()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::InitDefaultsDebugInfo();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.DebugInfo)
}
DebugInfo::DebugInfo(const DebugInfo& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      stack_entries_(from.stack_entries_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  detail_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.detail().size() > 0) {
    detail_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.detail_);
  }
  // @@protoc_insertion_point(copy_constructor:grpc.testing.DebugInfo)
}

void DebugInfo::SharedCtor() {
  detail_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

DebugInfo::~DebugInfo() {
  // @@protoc_insertion_point(destructor:grpc.testing.DebugInfo)
  SharedDtor();
}

void DebugInfo::SharedDtor() {
  detail_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void DebugInfo::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* DebugInfo::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const DebugInfo& DebugInfo::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::InitDefaultsDebugInfo();
  return *internal_default_instance();
}

DebugInfo* DebugInfo::New(::google::protobuf::Arena* arena) const {
  DebugInfo* n = new DebugInfo;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void DebugInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.DebugInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  stack_entries_.Clear();
  detail_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _internal_metadata_.Clear();
}

bool DebugInfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.DebugInfo)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated string stack_entries = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->add_stack_entries()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->stack_entries(this->stack_entries_size() - 1).data(),
            static_cast<int>(this->stack_entries(this->stack_entries_size() - 1).length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.testing.DebugInfo.stack_entries"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string detail = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_detail()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->detail().data(), static_cast<int>(this->detail().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.testing.DebugInfo.detail"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.DebugInfo)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.DebugInfo)
  return false;
#undef DO_
}

void DebugInfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.DebugInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string stack_entries = 1;
  for (int i = 0, n = this->stack_entries_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->stack_entries(i).data(), static_cast<int>(this->stack_entries(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.DebugInfo.stack_entries");
    ::google::protobuf::internal::WireFormatLite::WriteString(
      1, this->stack_entries(i), output);
  }

  // string detail = 2;
  if (this->detail().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->detail().data(), static_cast<int>(this->detail().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.DebugInfo.detail");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->detail(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.DebugInfo)
}

::google::protobuf::uint8* DebugInfo::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.DebugInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string stack_entries = 1;
  for (int i = 0, n = this->stack_entries_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->stack_entries(i).data(), static_cast<int>(this->stack_entries(i).length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.DebugInfo.stack_entries");
    target = ::google::protobuf::internal::WireFormatLite::
      WriteStringToArray(1, this->stack_entries(i), target);
  }

  // string detail = 2;
  if (this->detail().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->detail().data(), static_cast<int>(this->detail().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.DebugInfo.detail");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->detail(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.DebugInfo)
  return target;
}

size_t DebugInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.DebugInfo)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated string stack_entries = 1;
  total_size += 1 *
      ::google::protobuf::internal::FromIntSize(this->stack_entries_size());
  for (int i = 0, n = this->stack_entries_size(); i < n; i++) {
    total_size += ::google::protobuf::internal::WireFormatLite::StringSize(
      this->stack_entries(i));
  }

  // string detail = 2;
  if (this->detail().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->detail());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void DebugInfo::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.DebugInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const DebugInfo* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const DebugInfo>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.DebugInfo)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.DebugInfo)
    MergeFrom(*source);
  }
}

void DebugInfo::MergeFrom(const DebugInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.DebugInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  stack_entries_.MergeFrom(from.stack_entries_);
  if (from.detail().size() > 0) {

    detail_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.detail_);
  }
}

void DebugInfo::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.DebugInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void DebugInfo::CopyFrom(const DebugInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.DebugInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DebugInfo::IsInitialized() const {
  return true;
}

void DebugInfo::Swap(DebugInfo* other) {
  if (other == this) return;
  InternalSwap(other);
}
void DebugInfo::InternalSwap(DebugInfo* other) {
  using std::swap;
  stack_entries_.InternalSwap(&other->stack_entries_);
  detail_.Swap(&other->detail_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata DebugInfo::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ErrorStatus::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ErrorStatus::kCodeFieldNumber;
const int ErrorStatus::kErrorMessageFieldNumber;
const int ErrorStatus::kBinaryErrorDetailsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ErrorStatus::ErrorStatus()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::InitDefaultsErrorStatus();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.ErrorStatus)
}
ErrorStatus::ErrorStatus(const ErrorStatus& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  error_message_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.error_message().size() > 0) {
    error_message_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.error_message_);
  }
  binary_error_details_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.binary_error_details().size() > 0) {
    binary_error_details_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.binary_error_details_);
  }
  code_ = from.code_;
  // @@protoc_insertion_point(copy_constructor:grpc.testing.ErrorStatus)
}

void ErrorStatus::SharedCtor() {
  error_message_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  binary_error_details_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  code_ = 0;
  _cached_size_ = 0;
}

ErrorStatus::~ErrorStatus() {
  // @@protoc_insertion_point(destructor:grpc.testing.ErrorStatus)
  SharedDtor();
}

void ErrorStatus::SharedDtor() {
  error_message_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  binary_error_details_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void ErrorStatus::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ErrorStatus::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ErrorStatus& ErrorStatus::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::InitDefaultsErrorStatus();
  return *internal_default_instance();
}

ErrorStatus* ErrorStatus::New(::google::protobuf::Arena* arena) const {
  ErrorStatus* n = new ErrorStatus;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ErrorStatus::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.ErrorStatus)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  error_message_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  binary_error_details_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  code_ = 0;
  _internal_metadata_.Clear();
}

bool ErrorStatus::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.ErrorStatus)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 code = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &code_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string error_message = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_error_message()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->error_message().data(), static_cast<int>(this->error_message().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.testing.ErrorStatus.error_message"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string binary_error_details = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_binary_error_details()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->binary_error_details().data(), static_cast<int>(this->binary_error_details().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.testing.ErrorStatus.binary_error_details"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.ErrorStatus)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.ErrorStatus)
  return false;
#undef DO_
}

void ErrorStatus::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.ErrorStatus)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 code = 1;
  if (this->code() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->code(), output);
  }

  // string error_message = 2;
  if (this->error_message().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->error_message().data(), static_cast<int>(this->error_message().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.ErrorStatus.error_message");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->error_message(), output);
  }

  // string binary_error_details = 3;
  if (this->binary_error_details().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->binary_error_details().data(), static_cast<int>(this->binary_error_details().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.ErrorStatus.binary_error_details");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->binary_error_details(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.ErrorStatus)
}

::google::protobuf::uint8* ErrorStatus::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.ErrorStatus)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 code = 1;
  if (this->code() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->code(), target);
  }

  // string error_message = 2;
  if (this->error_message().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->error_message().data(), static_cast<int>(this->error_message().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.ErrorStatus.error_message");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->error_message(), target);
  }

  // string binary_error_details = 3;
  if (this->binary_error_details().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->binary_error_details().data(), static_cast<int>(this->binary_error_details().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.ErrorStatus.binary_error_details");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->binary_error_details(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.ErrorStatus)
  return target;
}

size_t ErrorStatus::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.ErrorStatus)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string error_message = 2;
  if (this->error_message().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->error_message());
  }

  // string binary_error_details = 3;
  if (this->binary_error_details().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->binary_error_details());
  }

  // int32 code = 1;
  if (this->code() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->code());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ErrorStatus::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.ErrorStatus)
  GOOGLE_DCHECK_NE(&from, this);
  const ErrorStatus* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ErrorStatus>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.ErrorStatus)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.ErrorStatus)
    MergeFrom(*source);
  }
}

void ErrorStatus::MergeFrom(const ErrorStatus& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.ErrorStatus)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.error_message().size() > 0) {

    error_message_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.error_message_);
  }
  if (from.binary_error_details().size() > 0) {

    binary_error_details_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.binary_error_details_);
  }
  if (from.code() != 0) {
    set_code(from.code());
  }
}

void ErrorStatus::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.ErrorStatus)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ErrorStatus::CopyFrom(const ErrorStatus& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.ErrorStatus)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ErrorStatus::IsInitialized() const {
  return true;
}

void ErrorStatus::Swap(ErrorStatus* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ErrorStatus::InternalSwap(ErrorStatus* other) {
  using std::swap;
  error_message_.Swap(&other->error_message_);
  binary_error_details_.Swap(&other->binary_error_details_);
  swap(code_, other->code_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ErrorStatus::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void RequestParams::InitAsDefaultInstance() {
  ::grpc::testing::_RequestParams_default_instance_._instance.get_mutable()->debug_info_ = const_cast< ::grpc::testing::DebugInfo*>(
      ::grpc::testing::DebugInfo::internal_default_instance());
  ::grpc::testing::_RequestParams_default_instance_._instance.get_mutable()->expected_error_ = const_cast< ::grpc::testing::ErrorStatus*>(
      ::grpc::testing::ErrorStatus::internal_default_instance());
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RequestParams::kEchoDeadlineFieldNumber;
const int RequestParams::kClientCancelAfterUsFieldNumber;
const int RequestParams::kServerCancelAfterUsFieldNumber;
const int RequestParams::kEchoMetadataFieldNumber;
const int RequestParams::kCheckAuthContextFieldNumber;
const int RequestParams::kResponseMessageLengthFieldNumber;
const int RequestParams::kEchoPeerFieldNumber;
const int RequestParams::kExpectedClientIdentityFieldNumber;
const int RequestParams::kSkipCancelledCheckFieldNumber;
const int RequestParams::kExpectedTransportSecurityTypeFieldNumber;
const int RequestParams::kDebugInfoFieldNumber;
const int RequestParams::kServerDieFieldNumber;
const int RequestParams::kBinaryErrorDetailsFieldNumber;
const int RequestParams::kExpectedErrorFieldNumber;
const int RequestParams::kServerSleepUsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RequestParams::RequestParams()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::InitDefaultsRequestParams();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.RequestParams)
}
RequestParams::RequestParams(const RequestParams& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  expected_client_identity_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.expected_client_identity().size() > 0) {
    expected_client_identity_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.expected_client_identity_);
  }
  expected_transport_security_type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.expected_transport_security_type().size() > 0) {
    expected_transport_security_type_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.expected_transport_security_type_);
  }
  binary_error_details_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.binary_error_details().size() > 0) {
    binary_error_details_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.binary_error_details_);
  }
  if (from.has_debug_info()) {
    debug_info_ = new ::grpc::testing::DebugInfo(*from.debug_info_);
  } else {
    debug_info_ = NULL;
  }
  if (from.has_expected_error()) {
    expected_error_ = new ::grpc::testing::ErrorStatus(*from.expected_error_);
  } else {
    expected_error_ = NULL;
  }
  ::memcpy(&client_cancel_after_us_, &from.client_cancel_after_us_,
    static_cast<size_t>(reinterpret_cast<char*>(&server_sleep_us_) -
    reinterpret_cast<char*>(&client_cancel_after_us_)) + sizeof(server_sleep_us_));
  // @@protoc_insertion_point(copy_constructor:grpc.testing.RequestParams)
}

void RequestParams::SharedCtor() {
  expected_client_identity_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  expected_transport_security_type_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  binary_error_details_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&debug_info_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&server_sleep_us_) -
      reinterpret_cast<char*>(&debug_info_)) + sizeof(server_sleep_us_));
  _cached_size_ = 0;
}

RequestParams::~RequestParams() {
  // @@protoc_insertion_point(destructor:grpc.testing.RequestParams)
  SharedDtor();
}

void RequestParams::SharedDtor() {
  expected_client_identity_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  expected_transport_security_type_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  binary_error_details_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete debug_info_;
  if (this != internal_default_instance()) delete expected_error_;
}

void RequestParams::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RequestParams::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const RequestParams& RequestParams::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::InitDefaultsRequestParams();
  return *internal_default_instance();
}

RequestParams* RequestParams::New(::google::protobuf::Arena* arena) const {
  RequestParams* n = new RequestParams;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void RequestParams::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.RequestParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  expected_client_identity_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  expected_transport_security_type_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  binary_error_details_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && debug_info_ != NULL) {
    delete debug_info_;
  }
  debug_info_ = NULL;
  if (GetArenaNoVirtual() == NULL && expected_error_ != NULL) {
    delete expected_error_;
  }
  expected_error_ = NULL;
  ::memset(&client_cancel_after_us_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&server_sleep_us_) -
      reinterpret_cast<char*>(&client_cancel_after_us_)) + sizeof(server_sleep_us_));
  _internal_metadata_.Clear();
}

bool RequestParams::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.RequestParams)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // bool echo_deadline = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &echo_deadline_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 client_cancel_after_us = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &client_cancel_after_us_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 server_cancel_after_us = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &server_cancel_after_us_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool echo_metadata = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &echo_metadata_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool check_auth_context = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &check_auth_context_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 response_message_length = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(48u /* 48 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &response_message_length_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool echo_peer = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(56u /* 56 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &echo_peer_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string expected_client_identity = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(66u /* 66 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_expected_client_identity()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->expected_client_identity().data(), static_cast<int>(this->expected_client_identity().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.testing.RequestParams.expected_client_identity"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool skip_cancelled_check = 9;
      case 9: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(72u /* 72 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &skip_cancelled_check_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string expected_transport_security_type = 10;
      case 10: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(82u /* 82 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_expected_transport_security_type()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->expected_transport_security_type().data(), static_cast<int>(this->expected_transport_security_type().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.testing.RequestParams.expected_transport_security_type"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.testing.DebugInfo debug_info = 11;
      case 11: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(90u /* 90 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_debug_info()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool server_die = 12;
      case 12: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(96u /* 96 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &server_die_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string binary_error_details = 13;
      case 13: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(106u /* 106 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_binary_error_details()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->binary_error_details().data(), static_cast<int>(this->binary_error_details().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.testing.RequestParams.binary_error_details"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.testing.ErrorStatus expected_error = 14;
      case 14: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(114u /* 114 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_expected_error()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 server_sleep_us = 15;
      case 15: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(120u /* 120 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &server_sleep_us_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.RequestParams)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.RequestParams)
  return false;
#undef DO_
}

void RequestParams::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.RequestParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bool echo_deadline = 1;
  if (this->echo_deadline() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(1, this->echo_deadline(), output);
  }

  // int32 client_cancel_after_us = 2;
  if (this->client_cancel_after_us() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->client_cancel_after_us(), output);
  }

  // int32 server_cancel_after_us = 3;
  if (this->server_cancel_after_us() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(3, this->server_cancel_after_us(), output);
  }

  // bool echo_metadata = 4;
  if (this->echo_metadata() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(4, this->echo_metadata(), output);
  }

  // bool check_auth_context = 5;
  if (this->check_auth_context() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(5, this->check_auth_context(), output);
  }

  // int32 response_message_length = 6;
  if (this->response_message_length() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(6, this->response_message_length(), output);
  }

  // bool echo_peer = 7;
  if (this->echo_peer() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(7, this->echo_peer(), output);
  }

  // string expected_client_identity = 8;
  if (this->expected_client_identity().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->expected_client_identity().data(), static_cast<int>(this->expected_client_identity().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.RequestParams.expected_client_identity");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      8, this->expected_client_identity(), output);
  }

  // bool skip_cancelled_check = 9;
  if (this->skip_cancelled_check() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(9, this->skip_cancelled_check(), output);
  }

  // string expected_transport_security_type = 10;
  if (this->expected_transport_security_type().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->expected_transport_security_type().data(), static_cast<int>(this->expected_transport_security_type().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.RequestParams.expected_transport_security_type");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      10, this->expected_transport_security_type(), output);
  }

  // .grpc.testing.DebugInfo debug_info = 11;
  if (this->has_debug_info()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      11, *this->debug_info_, output);
  }

  // bool server_die = 12;
  if (this->server_die() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(12, this->server_die(), output);
  }

  // string binary_error_details = 13;
  if (this->binary_error_details().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->binary_error_details().data(), static_cast<int>(this->binary_error_details().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.RequestParams.binary_error_details");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      13, this->binary_error_details(), output);
  }

  // .grpc.testing.ErrorStatus expected_error = 14;
  if (this->has_expected_error()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      14, *this->expected_error_, output);
  }

  // int32 server_sleep_us = 15;
  if (this->server_sleep_us() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(15, this->server_sleep_us(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.RequestParams)
}

::google::protobuf::uint8* RequestParams::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.RequestParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bool echo_deadline = 1;
  if (this->echo_deadline() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(1, this->echo_deadline(), target);
  }

  // int32 client_cancel_after_us = 2;
  if (this->client_cancel_after_us() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->client_cancel_after_us(), target);
  }

  // int32 server_cancel_after_us = 3;
  if (this->server_cancel_after_us() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(3, this->server_cancel_after_us(), target);
  }

  // bool echo_metadata = 4;
  if (this->echo_metadata() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(4, this->echo_metadata(), target);
  }

  // bool check_auth_context = 5;
  if (this->check_auth_context() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(5, this->check_auth_context(), target);
  }

  // int32 response_message_length = 6;
  if (this->response_message_length() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(6, this->response_message_length(), target);
  }

  // bool echo_peer = 7;
  if (this->echo_peer() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(7, this->echo_peer(), target);
  }

  // string expected_client_identity = 8;
  if (this->expected_client_identity().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->expected_client_identity().data(), static_cast<int>(this->expected_client_identity().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.RequestParams.expected_client_identity");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        8, this->expected_client_identity(), target);
  }

  // bool skip_cancelled_check = 9;
  if (this->skip_cancelled_check() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(9, this->skip_cancelled_check(), target);
  }

  // string expected_transport_security_type = 10;
  if (this->expected_transport_security_type().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->expected_transport_security_type().data(), static_cast<int>(this->expected_transport_security_type().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.RequestParams.expected_transport_security_type");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        10, this->expected_transport_security_type(), target);
  }

  // .grpc.testing.DebugInfo debug_info = 11;
  if (this->has_debug_info()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        11, *this->debug_info_, deterministic, target);
  }

  // bool server_die = 12;
  if (this->server_die() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(12, this->server_die(), target);
  }

  // string binary_error_details = 13;
  if (this->binary_error_details().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->binary_error_details().data(), static_cast<int>(this->binary_error_details().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.RequestParams.binary_error_details");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        13, this->binary_error_details(), target);
  }

  // .grpc.testing.ErrorStatus expected_error = 14;
  if (this->has_expected_error()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        14, *this->expected_error_, deterministic, target);
  }

  // int32 server_sleep_us = 15;
  if (this->server_sleep_us() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(15, this->server_sleep_us(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.RequestParams)
  return target;
}

size_t RequestParams::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.RequestParams)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string expected_client_identity = 8;
  if (this->expected_client_identity().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->expected_client_identity());
  }

  // string expected_transport_security_type = 10;
  if (this->expected_transport_security_type().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->expected_transport_security_type());
  }

  // string binary_error_details = 13;
  if (this->binary_error_details().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->binary_error_details());
  }

  // .grpc.testing.DebugInfo debug_info = 11;
  if (this->has_debug_info()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->debug_info_);
  }

  // .grpc.testing.ErrorStatus expected_error = 14;
  if (this->has_expected_error()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->expected_error_);
  }

  // int32 client_cancel_after_us = 2;
  if (this->client_cancel_after_us() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->client_cancel_after_us());
  }

  // int32 server_cancel_after_us = 3;
  if (this->server_cancel_after_us() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->server_cancel_after_us());
  }

  // bool echo_deadline = 1;
  if (this->echo_deadline() != 0) {
    total_size += 1 + 1;
  }

  // bool echo_metadata = 4;
  if (this->echo_metadata() != 0) {
    total_size += 1 + 1;
  }

  // bool check_auth_context = 5;
  if (this->check_auth_context() != 0) {
    total_size += 1 + 1;
  }

  // bool echo_peer = 7;
  if (this->echo_peer() != 0) {
    total_size += 1 + 1;
  }

  // int32 response_message_length = 6;
  if (this->response_message_length() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->response_message_length());
  }

  // bool skip_cancelled_check = 9;
  if (this->skip_cancelled_check() != 0) {
    total_size += 1 + 1;
  }

  // bool server_die = 12;
  if (this->server_die() != 0) {
    total_size += 1 + 1;
  }

  // int32 server_sleep_us = 15;
  if (this->server_sleep_us() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->server_sleep_us());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RequestParams::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.RequestParams)
  GOOGLE_DCHECK_NE(&from, this);
  const RequestParams* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RequestParams>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.RequestParams)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.RequestParams)
    MergeFrom(*source);
  }
}

void RequestParams::MergeFrom(const RequestParams& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.RequestParams)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.expected_client_identity().size() > 0) {

    expected_client_identity_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.expected_client_identity_);
  }
  if (from.expected_transport_security_type().size() > 0) {

    expected_transport_security_type_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.expected_transport_security_type_);
  }
  if (from.binary_error_details().size() > 0) {

    binary_error_details_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.binary_error_details_);
  }
  if (from.has_debug_info()) {
    mutable_debug_info()->::grpc::testing::DebugInfo::MergeFrom(from.debug_info());
  }
  if (from.has_expected_error()) {
    mutable_expected_error()->::grpc::testing::ErrorStatus::MergeFrom(from.expected_error());
  }
  if (from.client_cancel_after_us() != 0) {
    set_client_cancel_after_us(from.client_cancel_after_us());
  }
  if (from.server_cancel_after_us() != 0) {
    set_server_cancel_after_us(from.server_cancel_after_us());
  }
  if (from.echo_deadline() != 0) {
    set_echo_deadline(from.echo_deadline());
  }
  if (from.echo_metadata() != 0) {
    set_echo_metadata(from.echo_metadata());
  }
  if (from.check_auth_context() != 0) {
    set_check_auth_context(from.check_auth_context());
  }
  if (from.echo_peer() != 0) {
    set_echo_peer(from.echo_peer());
  }
  if (from.response_message_length() != 0) {
    set_response_message_length(from.response_message_length());
  }
  if (from.skip_cancelled_check() != 0) {
    set_skip_cancelled_check(from.skip_cancelled_check());
  }
  if (from.server_die() != 0) {
    set_server_die(from.server_die());
  }
  if (from.server_sleep_us() != 0) {
    set_server_sleep_us(from.server_sleep_us());
  }
}

void RequestParams::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.RequestParams)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RequestParams::CopyFrom(const RequestParams& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.RequestParams)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RequestParams::IsInitialized() const {
  return true;
}

void RequestParams::Swap(RequestParams* other) {
  if (other == this) return;
  InternalSwap(other);
}
void RequestParams::InternalSwap(RequestParams* other) {
  using std::swap;
  expected_client_identity_.Swap(&other->expected_client_identity_);
  expected_transport_security_type_.Swap(&other->expected_transport_security_type_);
  binary_error_details_.Swap(&other->binary_error_details_);
  swap(debug_info_, other->debug_info_);
  swap(expected_error_, other->expected_error_);
  swap(client_cancel_after_us_, other->client_cancel_after_us_);
  swap(server_cancel_after_us_, other->server_cancel_after_us_);
  swap(echo_deadline_, other->echo_deadline_);
  swap(echo_metadata_, other->echo_metadata_);
  swap(check_auth_context_, other->check_auth_context_);
  swap(echo_peer_, other->echo_peer_);
  swap(response_message_length_, other->response_message_length_);
  swap(skip_cancelled_check_, other->skip_cancelled_check_);
  swap(server_die_, other->server_die_);
  swap(server_sleep_us_, other->server_sleep_us_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata RequestParams::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void EchoRequest::InitAsDefaultInstance() {
  ::grpc::testing::_EchoRequest_default_instance_._instance.get_mutable()->param_ = const_cast< ::grpc::testing::RequestParams*>(
      ::grpc::testing::RequestParams::internal_default_instance());
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int EchoRequest::kMessageFieldNumber;
const int EchoRequest::kParamFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

EchoRequest::EchoRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::InitDefaultsEchoRequest();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.EchoRequest)
}
EchoRequest::EchoRequest(const EchoRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  message_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.message().size() > 0) {
    message_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.message_);
  }
  if (from.has_param()) {
    param_ = new ::grpc::testing::RequestParams(*from.param_);
  } else {
    param_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:grpc.testing.EchoRequest)
}

void EchoRequest::SharedCtor() {
  message_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  param_ = NULL;
  _cached_size_ = 0;
}

EchoRequest::~EchoRequest() {
  // @@protoc_insertion_point(destructor:grpc.testing.EchoRequest)
  SharedDtor();
}

void EchoRequest::SharedDtor() {
  message_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete param_;
}

void EchoRequest::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* EchoRequest::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const EchoRequest& EchoRequest::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::InitDefaultsEchoRequest();
  return *internal_default_instance();
}

EchoRequest* EchoRequest::New(::google::protobuf::Arena* arena) const {
  EchoRequest* n = new EchoRequest;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void EchoRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.EchoRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  message_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && param_ != NULL) {
    delete param_;
  }
  param_ = NULL;
  _internal_metadata_.Clear();
}

bool EchoRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.EchoRequest)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string message = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_message()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->message().data(), static_cast<int>(this->message().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.testing.EchoRequest.message"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.testing.RequestParams param = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_param()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.EchoRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.EchoRequest)
  return false;
#undef DO_
}

void EchoRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.EchoRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string message = 1;
  if (this->message().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->message().data(), static_cast<int>(this->message().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.EchoRequest.message");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->message(), output);
  }

  // .grpc.testing.RequestParams param = 2;
  if (this->has_param()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->param_, output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.EchoRequest)
}

::google::protobuf::uint8* EchoRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.EchoRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string message = 1;
  if (this->message().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->message().data(), static_cast<int>(this->message().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.EchoRequest.message");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->message(), target);
  }

  // .grpc.testing.RequestParams param = 2;
  if (this->has_param()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, *this->param_, deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.EchoRequest)
  return target;
}

size_t EchoRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.EchoRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string message = 1;
  if (this->message().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->message());
  }

  // .grpc.testing.RequestParams param = 2;
  if (this->has_param()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->param_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void EchoRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.EchoRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const EchoRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const EchoRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.EchoRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.EchoRequest)
    MergeFrom(*source);
  }
}

void EchoRequest::MergeFrom(const EchoRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.EchoRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.message().size() > 0) {

    message_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.message_);
  }
  if (from.has_param()) {
    mutable_param()->::grpc::testing::RequestParams::MergeFrom(from.param());
  }
}

void EchoRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.EchoRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void EchoRequest::CopyFrom(const EchoRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.EchoRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EchoRequest::IsInitialized() const {
  return true;
}

void EchoRequest::Swap(EchoRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void EchoRequest::InternalSwap(EchoRequest* other) {
  using std::swap;
  message_.Swap(&other->message_);
  swap(param_, other->param_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata EchoRequest::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ResponseParams::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ResponseParams::kRequestDeadlineFieldNumber;
const int ResponseParams::kHostFieldNumber;
const int ResponseParams::kPeerFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ResponseParams::ResponseParams()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::InitDefaultsResponseParams();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.ResponseParams)
}
ResponseParams::ResponseParams(const ResponseParams& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  host_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.host().size() > 0) {
    host_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.host_);
  }
  peer_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.peer().size() > 0) {
    peer_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.peer_);
  }
  request_deadline_ = from.request_deadline_;
  // @@protoc_insertion_point(copy_constructor:grpc.testing.ResponseParams)
}

void ResponseParams::SharedCtor() {
  host_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  peer_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  request_deadline_ = GOOGLE_LONGLONG(0);
  _cached_size_ = 0;
}

ResponseParams::~ResponseParams() {
  // @@protoc_insertion_point(destructor:grpc.testing.ResponseParams)
  SharedDtor();
}

void ResponseParams::SharedDtor() {
  host_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  peer_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void ResponseParams::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ResponseParams::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ResponseParams& ResponseParams::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::InitDefaultsResponseParams();
  return *internal_default_instance();
}

ResponseParams* ResponseParams::New(::google::protobuf::Arena* arena) const {
  ResponseParams* n = new ResponseParams;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ResponseParams::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.ResponseParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  host_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  peer_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  request_deadline_ = GOOGLE_LONGLONG(0);
  _internal_metadata_.Clear();
}

bool ResponseParams::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.ResponseParams)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int64 request_deadline = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &request_deadline_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string host = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_host()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->host().data(), static_cast<int>(this->host().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.testing.ResponseParams.host"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string peer = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_peer()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->peer().data(), static_cast<int>(this->peer().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.testing.ResponseParams.peer"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.ResponseParams)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.ResponseParams)
  return false;
#undef DO_
}

void ResponseParams::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.ResponseParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 request_deadline = 1;
  if (this->request_deadline() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(1, this->request_deadline(), output);
  }

  // string host = 2;
  if (this->host().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->host().data(), static_cast<int>(this->host().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.ResponseParams.host");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->host(), output);
  }

  // string peer = 3;
  if (this->peer().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->peer().data(), static_cast<int>(this->peer().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.ResponseParams.peer");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->peer(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.ResponseParams)
}

::google::protobuf::uint8* ResponseParams::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.ResponseParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 request_deadline = 1;
  if (this->request_deadline() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(1, this->request_deadline(), target);
  }

  // string host = 2;
  if (this->host().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->host().data(), static_cast<int>(this->host().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.ResponseParams.host");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->host(), target);
  }

  // string peer = 3;
  if (this->peer().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->peer().data(), static_cast<int>(this->peer().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.ResponseParams.peer");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->peer(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.ResponseParams)
  return target;
}

size_t ResponseParams::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.ResponseParams)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string host = 2;
  if (this->host().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->host());
  }

  // string peer = 3;
  if (this->peer().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->peer());
  }

  // int64 request_deadline = 1;
  if (this->request_deadline() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->request_deadline());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ResponseParams::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.ResponseParams)
  GOOGLE_DCHECK_NE(&from, this);
  const ResponseParams* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ResponseParams>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.ResponseParams)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.ResponseParams)
    MergeFrom(*source);
  }
}

void ResponseParams::MergeFrom(const ResponseParams& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.ResponseParams)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.host().size() > 0) {

    host_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.host_);
  }
  if (from.peer().size() > 0) {

    peer_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.peer_);
  }
  if (from.request_deadline() != 0) {
    set_request_deadline(from.request_deadline());
  }
}

void ResponseParams::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.ResponseParams)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ResponseParams::CopyFrom(const ResponseParams& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.ResponseParams)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ResponseParams::IsInitialized() const {
  return true;
}

void ResponseParams::Swap(ResponseParams* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ResponseParams::InternalSwap(ResponseParams* other) {
  using std::swap;
  host_.Swap(&other->host_);
  peer_.Swap(&other->peer_);
  swap(request_deadline_, other->request_deadline_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ResponseParams::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void EchoResponse::InitAsDefaultInstance() {
  ::grpc::testing::_EchoResponse_default_instance_._instance.get_mutable()->param_ = const_cast< ::grpc::testing::ResponseParams*>(
      ::grpc::testing::ResponseParams::internal_default_instance());
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int EchoResponse::kMessageFieldNumber;
const int EchoResponse::kParamFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

EchoResponse::EchoResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::InitDefaultsEchoResponse();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.EchoResponse)
}
EchoResponse::EchoResponse(const EchoResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  message_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.message().size() > 0) {
    message_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.message_);
  }
  if (from.has_param()) {
    param_ = new ::grpc::testing::ResponseParams(*from.param_);
  } else {
    param_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:grpc.testing.EchoResponse)
}

void EchoResponse::SharedCtor() {
  message_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  param_ = NULL;
  _cached_size_ = 0;
}

EchoResponse::~EchoResponse() {
  // @@protoc_insertion_point(destructor:grpc.testing.EchoResponse)
  SharedDtor();
}

void EchoResponse::SharedDtor() {
  message_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete param_;
}

void EchoResponse::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* EchoResponse::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const EchoResponse& EchoResponse::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::InitDefaultsEchoResponse();
  return *internal_default_instance();
}

EchoResponse* EchoResponse::New(::google::protobuf::Arena* arena) const {
  EchoResponse* n = new EchoResponse;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void EchoResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.EchoResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  message_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && param_ != NULL) {
    delete param_;
  }
  param_ = NULL;
  _internal_metadata_.Clear();
}

bool EchoResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.EchoResponse)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string message = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_message()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->message().data(), static_cast<int>(this->message().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.testing.EchoResponse.message"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.testing.ResponseParams param = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_param()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.EchoResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.EchoResponse)
  return false;
#undef DO_
}

void EchoResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.EchoResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string message = 1;
  if (this->message().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->message().data(), static_cast<int>(this->message().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.EchoResponse.message");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->message(), output);
  }

  // .grpc.testing.ResponseParams param = 2;
  if (this->has_param()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->param_, output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.EchoResponse)
}

::google::protobuf::uint8* EchoResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.EchoResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string message = 1;
  if (this->message().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->message().data(), static_cast<int>(this->message().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.EchoResponse.message");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->message(), target);
  }

  // .grpc.testing.ResponseParams param = 2;
  if (this->has_param()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, *this->param_, deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.EchoResponse)
  return target;
}

size_t EchoResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.EchoResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string message = 1;
  if (this->message().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->message());
  }

  // .grpc.testing.ResponseParams param = 2;
  if (this->has_param()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->param_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void EchoResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.EchoResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const EchoResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const EchoResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.EchoResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.EchoResponse)
    MergeFrom(*source);
  }
}

void EchoResponse::MergeFrom(const EchoResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.EchoResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.message().size() > 0) {

    message_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.message_);
  }
  if (from.has_param()) {
    mutable_param()->::grpc::testing::ResponseParams::MergeFrom(from.param());
  }
}

void EchoResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.EchoResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void EchoResponse::CopyFrom(const EchoResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.EchoResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EchoResponse::IsInitialized() const {
  return true;
}

void EchoResponse::Swap(EchoResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void EchoResponse::InternalSwap(EchoResponse* other) {
  using std::swap;
  message_.Swap(&other->message_);
  swap(param_, other->param_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata EchoResponse::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace testing
}  // namespace grpc

// @@protoc_insertion_point(global_scope)
