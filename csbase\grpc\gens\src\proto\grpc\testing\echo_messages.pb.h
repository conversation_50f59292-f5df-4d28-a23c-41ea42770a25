// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: src/proto/grpc/testing/echo_messages.proto

#ifndef PROTOBUF_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto__INCLUDED
#define PROTOBUF_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3005001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[6];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
void InitDefaultsDebugInfoImpl();
void InitDefaultsDebugInfo();
void InitDefaultsErrorStatusImpl();
void InitDefaultsErrorStatus();
void InitDefaultsRequestParamsImpl();
void InitDefaultsRequestParams();
void InitDefaultsEchoRequestImpl();
void InitDefaultsEchoRequest();
void InitDefaultsResponseParamsImpl();
void InitDefaultsResponseParams();
void InitDefaultsEchoResponseImpl();
void InitDefaultsEchoResponse();
inline void InitDefaults() {
  InitDefaultsDebugInfo();
  InitDefaultsErrorStatus();
  InitDefaultsRequestParams();
  InitDefaultsEchoRequest();
  InitDefaultsResponseParams();
  InitDefaultsEchoResponse();
}
}  // namespace protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto
namespace grpc {
namespace testing {
class DebugInfo;
class DebugInfoDefaultTypeInternal;
extern DebugInfoDefaultTypeInternal _DebugInfo_default_instance_;
class EchoRequest;
class EchoRequestDefaultTypeInternal;
extern EchoRequestDefaultTypeInternal _EchoRequest_default_instance_;
class EchoResponse;
class EchoResponseDefaultTypeInternal;
extern EchoResponseDefaultTypeInternal _EchoResponse_default_instance_;
class ErrorStatus;
class ErrorStatusDefaultTypeInternal;
extern ErrorStatusDefaultTypeInternal _ErrorStatus_default_instance_;
class RequestParams;
class RequestParamsDefaultTypeInternal;
extern RequestParamsDefaultTypeInternal _RequestParams_default_instance_;
class ResponseParams;
class ResponseParamsDefaultTypeInternal;
extern ResponseParamsDefaultTypeInternal _ResponseParams_default_instance_;
}  // namespace testing
}  // namespace grpc
namespace grpc {
namespace testing {

// ===================================================================

class DebugInfo : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.DebugInfo) */ {
 public:
  DebugInfo();
  virtual ~DebugInfo();

  DebugInfo(const DebugInfo& from);

  inline DebugInfo& operator=(const DebugInfo& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  DebugInfo(DebugInfo&& from) noexcept
    : DebugInfo() {
    *this = ::std::move(from);
  }

  inline DebugInfo& operator=(DebugInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const DebugInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const DebugInfo* internal_default_instance() {
    return reinterpret_cast<const DebugInfo*>(
               &_DebugInfo_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    0;

  void Swap(DebugInfo* other);
  friend void swap(DebugInfo& a, DebugInfo& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline DebugInfo* New() const PROTOBUF_FINAL { return New(NULL); }

  DebugInfo* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const DebugInfo& from);
  void MergeFrom(const DebugInfo& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(DebugInfo* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated string stack_entries = 1;
  int stack_entries_size() const;
  void clear_stack_entries();
  static const int kStackEntriesFieldNumber = 1;
  const ::std::string& stack_entries(int index) const;
  ::std::string* mutable_stack_entries(int index);
  void set_stack_entries(int index, const ::std::string& value);
  #if LANG_CXX11
  void set_stack_entries(int index, ::std::string&& value);
  #endif
  void set_stack_entries(int index, const char* value);
  void set_stack_entries(int index, const char* value, size_t size);
  ::std::string* add_stack_entries();
  void add_stack_entries(const ::std::string& value);
  #if LANG_CXX11
  void add_stack_entries(::std::string&& value);
  #endif
  void add_stack_entries(const char* value);
  void add_stack_entries(const char* value, size_t size);
  const ::google::protobuf::RepeatedPtrField< ::std::string>& stack_entries() const;
  ::google::protobuf::RepeatedPtrField< ::std::string>* mutable_stack_entries();

  // string detail = 2;
  void clear_detail();
  static const int kDetailFieldNumber = 2;
  const ::std::string& detail() const;
  void set_detail(const ::std::string& value);
  #if LANG_CXX11
  void set_detail(::std::string&& value);
  #endif
  void set_detail(const char* value);
  void set_detail(const char* value, size_t size);
  ::std::string* mutable_detail();
  ::std::string* release_detail();
  void set_allocated_detail(::std::string* detail);

  // @@protoc_insertion_point(class_scope:grpc.testing.DebugInfo)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::std::string> stack_entries_;
  ::google::protobuf::internal::ArenaStringPtr detail_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::InitDefaultsDebugInfoImpl();
};
// -------------------------------------------------------------------

class ErrorStatus : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.ErrorStatus) */ {
 public:
  ErrorStatus();
  virtual ~ErrorStatus();

  ErrorStatus(const ErrorStatus& from);

  inline ErrorStatus& operator=(const ErrorStatus& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ErrorStatus(ErrorStatus&& from) noexcept
    : ErrorStatus() {
    *this = ::std::move(from);
  }

  inline ErrorStatus& operator=(ErrorStatus&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ErrorStatus& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ErrorStatus* internal_default_instance() {
    return reinterpret_cast<const ErrorStatus*>(
               &_ErrorStatus_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    1;

  void Swap(ErrorStatus* other);
  friend void swap(ErrorStatus& a, ErrorStatus& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ErrorStatus* New() const PROTOBUF_FINAL { return New(NULL); }

  ErrorStatus* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ErrorStatus& from);
  void MergeFrom(const ErrorStatus& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ErrorStatus* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string error_message = 2;
  void clear_error_message();
  static const int kErrorMessageFieldNumber = 2;
  const ::std::string& error_message() const;
  void set_error_message(const ::std::string& value);
  #if LANG_CXX11
  void set_error_message(::std::string&& value);
  #endif
  void set_error_message(const char* value);
  void set_error_message(const char* value, size_t size);
  ::std::string* mutable_error_message();
  ::std::string* release_error_message();
  void set_allocated_error_message(::std::string* error_message);

  // string binary_error_details = 3;
  void clear_binary_error_details();
  static const int kBinaryErrorDetailsFieldNumber = 3;
  const ::std::string& binary_error_details() const;
  void set_binary_error_details(const ::std::string& value);
  #if LANG_CXX11
  void set_binary_error_details(::std::string&& value);
  #endif
  void set_binary_error_details(const char* value);
  void set_binary_error_details(const char* value, size_t size);
  ::std::string* mutable_binary_error_details();
  ::std::string* release_binary_error_details();
  void set_allocated_binary_error_details(::std::string* binary_error_details);

  // int32 code = 1;
  void clear_code();
  static const int kCodeFieldNumber = 1;
  ::google::protobuf::int32 code() const;
  void set_code(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:grpc.testing.ErrorStatus)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr error_message_;
  ::google::protobuf::internal::ArenaStringPtr binary_error_details_;
  ::google::protobuf::int32 code_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::InitDefaultsErrorStatusImpl();
};
// -------------------------------------------------------------------

class RequestParams : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.RequestParams) */ {
 public:
  RequestParams();
  virtual ~RequestParams();

  RequestParams(const RequestParams& from);

  inline RequestParams& operator=(const RequestParams& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  RequestParams(RequestParams&& from) noexcept
    : RequestParams() {
    *this = ::std::move(from);
  }

  inline RequestParams& operator=(RequestParams&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const RequestParams& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RequestParams* internal_default_instance() {
    return reinterpret_cast<const RequestParams*>(
               &_RequestParams_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    2;

  void Swap(RequestParams* other);
  friend void swap(RequestParams& a, RequestParams& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RequestParams* New() const PROTOBUF_FINAL { return New(NULL); }

  RequestParams* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const RequestParams& from);
  void MergeFrom(const RequestParams& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(RequestParams* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string expected_client_identity = 8;
  void clear_expected_client_identity();
  static const int kExpectedClientIdentityFieldNumber = 8;
  const ::std::string& expected_client_identity() const;
  void set_expected_client_identity(const ::std::string& value);
  #if LANG_CXX11
  void set_expected_client_identity(::std::string&& value);
  #endif
  void set_expected_client_identity(const char* value);
  void set_expected_client_identity(const char* value, size_t size);
  ::std::string* mutable_expected_client_identity();
  ::std::string* release_expected_client_identity();
  void set_allocated_expected_client_identity(::std::string* expected_client_identity);

  // string expected_transport_security_type = 10;
  void clear_expected_transport_security_type();
  static const int kExpectedTransportSecurityTypeFieldNumber = 10;
  const ::std::string& expected_transport_security_type() const;
  void set_expected_transport_security_type(const ::std::string& value);
  #if LANG_CXX11
  void set_expected_transport_security_type(::std::string&& value);
  #endif
  void set_expected_transport_security_type(const char* value);
  void set_expected_transport_security_type(const char* value, size_t size);
  ::std::string* mutable_expected_transport_security_type();
  ::std::string* release_expected_transport_security_type();
  void set_allocated_expected_transport_security_type(::std::string* expected_transport_security_type);

  // string binary_error_details = 13;
  void clear_binary_error_details();
  static const int kBinaryErrorDetailsFieldNumber = 13;
  const ::std::string& binary_error_details() const;
  void set_binary_error_details(const ::std::string& value);
  #if LANG_CXX11
  void set_binary_error_details(::std::string&& value);
  #endif
  void set_binary_error_details(const char* value);
  void set_binary_error_details(const char* value, size_t size);
  ::std::string* mutable_binary_error_details();
  ::std::string* release_binary_error_details();
  void set_allocated_binary_error_details(::std::string* binary_error_details);

  // .grpc.testing.DebugInfo debug_info = 11;
  bool has_debug_info() const;
  void clear_debug_info();
  static const int kDebugInfoFieldNumber = 11;
  const ::grpc::testing::DebugInfo& debug_info() const;
  ::grpc::testing::DebugInfo* release_debug_info();
  ::grpc::testing::DebugInfo* mutable_debug_info();
  void set_allocated_debug_info(::grpc::testing::DebugInfo* debug_info);

  // .grpc.testing.ErrorStatus expected_error = 14;
  bool has_expected_error() const;
  void clear_expected_error();
  static const int kExpectedErrorFieldNumber = 14;
  const ::grpc::testing::ErrorStatus& expected_error() const;
  ::grpc::testing::ErrorStatus* release_expected_error();
  ::grpc::testing::ErrorStatus* mutable_expected_error();
  void set_allocated_expected_error(::grpc::testing::ErrorStatus* expected_error);

  // int32 client_cancel_after_us = 2;
  void clear_client_cancel_after_us();
  static const int kClientCancelAfterUsFieldNumber = 2;
  ::google::protobuf::int32 client_cancel_after_us() const;
  void set_client_cancel_after_us(::google::protobuf::int32 value);

  // int32 server_cancel_after_us = 3;
  void clear_server_cancel_after_us();
  static const int kServerCancelAfterUsFieldNumber = 3;
  ::google::protobuf::int32 server_cancel_after_us() const;
  void set_server_cancel_after_us(::google::protobuf::int32 value);

  // bool echo_deadline = 1;
  void clear_echo_deadline();
  static const int kEchoDeadlineFieldNumber = 1;
  bool echo_deadline() const;
  void set_echo_deadline(bool value);

  // bool echo_metadata = 4;
  void clear_echo_metadata();
  static const int kEchoMetadataFieldNumber = 4;
  bool echo_metadata() const;
  void set_echo_metadata(bool value);

  // bool check_auth_context = 5;
  void clear_check_auth_context();
  static const int kCheckAuthContextFieldNumber = 5;
  bool check_auth_context() const;
  void set_check_auth_context(bool value);

  // bool echo_peer = 7;
  void clear_echo_peer();
  static const int kEchoPeerFieldNumber = 7;
  bool echo_peer() const;
  void set_echo_peer(bool value);

  // int32 response_message_length = 6;
  void clear_response_message_length();
  static const int kResponseMessageLengthFieldNumber = 6;
  ::google::protobuf::int32 response_message_length() const;
  void set_response_message_length(::google::protobuf::int32 value);

  // bool skip_cancelled_check = 9;
  void clear_skip_cancelled_check();
  static const int kSkipCancelledCheckFieldNumber = 9;
  bool skip_cancelled_check() const;
  void set_skip_cancelled_check(bool value);

  // bool server_die = 12;
  void clear_server_die();
  static const int kServerDieFieldNumber = 12;
  bool server_die() const;
  void set_server_die(bool value);

  // int32 server_sleep_us = 15;
  void clear_server_sleep_us();
  static const int kServerSleepUsFieldNumber = 15;
  ::google::protobuf::int32 server_sleep_us() const;
  void set_server_sleep_us(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:grpc.testing.RequestParams)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr expected_client_identity_;
  ::google::protobuf::internal::ArenaStringPtr expected_transport_security_type_;
  ::google::protobuf::internal::ArenaStringPtr binary_error_details_;
  ::grpc::testing::DebugInfo* debug_info_;
  ::grpc::testing::ErrorStatus* expected_error_;
  ::google::protobuf::int32 client_cancel_after_us_;
  ::google::protobuf::int32 server_cancel_after_us_;
  bool echo_deadline_;
  bool echo_metadata_;
  bool check_auth_context_;
  bool echo_peer_;
  ::google::protobuf::int32 response_message_length_;
  bool skip_cancelled_check_;
  bool server_die_;
  ::google::protobuf::int32 server_sleep_us_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::InitDefaultsRequestParamsImpl();
};
// -------------------------------------------------------------------

class EchoRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.EchoRequest) */ {
 public:
  EchoRequest();
  virtual ~EchoRequest();

  EchoRequest(const EchoRequest& from);

  inline EchoRequest& operator=(const EchoRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  EchoRequest(EchoRequest&& from) noexcept
    : EchoRequest() {
    *this = ::std::move(from);
  }

  inline EchoRequest& operator=(EchoRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const EchoRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const EchoRequest* internal_default_instance() {
    return reinterpret_cast<const EchoRequest*>(
               &_EchoRequest_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    3;

  void Swap(EchoRequest* other);
  friend void swap(EchoRequest& a, EchoRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline EchoRequest* New() const PROTOBUF_FINAL { return New(NULL); }

  EchoRequest* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const EchoRequest& from);
  void MergeFrom(const EchoRequest& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(EchoRequest* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string message = 1;
  void clear_message();
  static const int kMessageFieldNumber = 1;
  const ::std::string& message() const;
  void set_message(const ::std::string& value);
  #if LANG_CXX11
  void set_message(::std::string&& value);
  #endif
  void set_message(const char* value);
  void set_message(const char* value, size_t size);
  ::std::string* mutable_message();
  ::std::string* release_message();
  void set_allocated_message(::std::string* message);

  // .grpc.testing.RequestParams param = 2;
  bool has_param() const;
  void clear_param();
  static const int kParamFieldNumber = 2;
  const ::grpc::testing::RequestParams& param() const;
  ::grpc::testing::RequestParams* release_param();
  ::grpc::testing::RequestParams* mutable_param();
  void set_allocated_param(::grpc::testing::RequestParams* param);

  // @@protoc_insertion_point(class_scope:grpc.testing.EchoRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr message_;
  ::grpc::testing::RequestParams* param_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::InitDefaultsEchoRequestImpl();
};
// -------------------------------------------------------------------

class ResponseParams : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.ResponseParams) */ {
 public:
  ResponseParams();
  virtual ~ResponseParams();

  ResponseParams(const ResponseParams& from);

  inline ResponseParams& operator=(const ResponseParams& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ResponseParams(ResponseParams&& from) noexcept
    : ResponseParams() {
    *this = ::std::move(from);
  }

  inline ResponseParams& operator=(ResponseParams&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ResponseParams& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ResponseParams* internal_default_instance() {
    return reinterpret_cast<const ResponseParams*>(
               &_ResponseParams_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    4;

  void Swap(ResponseParams* other);
  friend void swap(ResponseParams& a, ResponseParams& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ResponseParams* New() const PROTOBUF_FINAL { return New(NULL); }

  ResponseParams* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ResponseParams& from);
  void MergeFrom(const ResponseParams& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ResponseParams* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string host = 2;
  void clear_host();
  static const int kHostFieldNumber = 2;
  const ::std::string& host() const;
  void set_host(const ::std::string& value);
  #if LANG_CXX11
  void set_host(::std::string&& value);
  #endif
  void set_host(const char* value);
  void set_host(const char* value, size_t size);
  ::std::string* mutable_host();
  ::std::string* release_host();
  void set_allocated_host(::std::string* host);

  // string peer = 3;
  void clear_peer();
  static const int kPeerFieldNumber = 3;
  const ::std::string& peer() const;
  void set_peer(const ::std::string& value);
  #if LANG_CXX11
  void set_peer(::std::string&& value);
  #endif
  void set_peer(const char* value);
  void set_peer(const char* value, size_t size);
  ::std::string* mutable_peer();
  ::std::string* release_peer();
  void set_allocated_peer(::std::string* peer);

  // int64 request_deadline = 1;
  void clear_request_deadline();
  static const int kRequestDeadlineFieldNumber = 1;
  ::google::protobuf::int64 request_deadline() const;
  void set_request_deadline(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:grpc.testing.ResponseParams)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr host_;
  ::google::protobuf::internal::ArenaStringPtr peer_;
  ::google::protobuf::int64 request_deadline_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::InitDefaultsResponseParamsImpl();
};
// -------------------------------------------------------------------

class EchoResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.EchoResponse) */ {
 public:
  EchoResponse();
  virtual ~EchoResponse();

  EchoResponse(const EchoResponse& from);

  inline EchoResponse& operator=(const EchoResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  EchoResponse(EchoResponse&& from) noexcept
    : EchoResponse() {
    *this = ::std::move(from);
  }

  inline EchoResponse& operator=(EchoResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const EchoResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const EchoResponse* internal_default_instance() {
    return reinterpret_cast<const EchoResponse*>(
               &_EchoResponse_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    5;

  void Swap(EchoResponse* other);
  friend void swap(EchoResponse& a, EchoResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline EchoResponse* New() const PROTOBUF_FINAL { return New(NULL); }

  EchoResponse* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const EchoResponse& from);
  void MergeFrom(const EchoResponse& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(EchoResponse* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string message = 1;
  void clear_message();
  static const int kMessageFieldNumber = 1;
  const ::std::string& message() const;
  void set_message(const ::std::string& value);
  #if LANG_CXX11
  void set_message(::std::string&& value);
  #endif
  void set_message(const char* value);
  void set_message(const char* value, size_t size);
  ::std::string* mutable_message();
  ::std::string* release_message();
  void set_allocated_message(::std::string* message);

  // .grpc.testing.ResponseParams param = 2;
  bool has_param() const;
  void clear_param();
  static const int kParamFieldNumber = 2;
  const ::grpc::testing::ResponseParams& param() const;
  ::grpc::testing::ResponseParams* release_param();
  ::grpc::testing::ResponseParams* mutable_param();
  void set_allocated_param(::grpc::testing::ResponseParams* param);

  // @@protoc_insertion_point(class_scope:grpc.testing.EchoResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr message_;
  ::grpc::testing::ResponseParams* param_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto::InitDefaultsEchoResponseImpl();
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// DebugInfo

// repeated string stack_entries = 1;
inline int DebugInfo::stack_entries_size() const {
  return stack_entries_.size();
}
inline void DebugInfo::clear_stack_entries() {
  stack_entries_.Clear();
}
inline const ::std::string& DebugInfo::stack_entries(int index) const {
  // @@protoc_insertion_point(field_get:grpc.testing.DebugInfo.stack_entries)
  return stack_entries_.Get(index);
}
inline ::std::string* DebugInfo::mutable_stack_entries(int index) {
  // @@protoc_insertion_point(field_mutable:grpc.testing.DebugInfo.stack_entries)
  return stack_entries_.Mutable(index);
}
inline void DebugInfo::set_stack_entries(int index, const ::std::string& value) {
  // @@protoc_insertion_point(field_set:grpc.testing.DebugInfo.stack_entries)
  stack_entries_.Mutable(index)->assign(value);
}
#if LANG_CXX11
inline void DebugInfo::set_stack_entries(int index, ::std::string&& value) {
  // @@protoc_insertion_point(field_set:grpc.testing.DebugInfo.stack_entries)
  stack_entries_.Mutable(index)->assign(std::move(value));
}
#endif
inline void DebugInfo::set_stack_entries(int index, const char* value) {
  GOOGLE_DCHECK(value != NULL);
  stack_entries_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:grpc.testing.DebugInfo.stack_entries)
}
inline void DebugInfo::set_stack_entries(int index, const char* value, size_t size) {
  stack_entries_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:grpc.testing.DebugInfo.stack_entries)
}
inline ::std::string* DebugInfo::add_stack_entries() {
  // @@protoc_insertion_point(field_add_mutable:grpc.testing.DebugInfo.stack_entries)
  return stack_entries_.Add();
}
inline void DebugInfo::add_stack_entries(const ::std::string& value) {
  stack_entries_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:grpc.testing.DebugInfo.stack_entries)
}
#if LANG_CXX11
inline void DebugInfo::add_stack_entries(::std::string&& value) {
  stack_entries_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:grpc.testing.DebugInfo.stack_entries)
}
#endif
inline void DebugInfo::add_stack_entries(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  stack_entries_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:grpc.testing.DebugInfo.stack_entries)
}
inline void DebugInfo::add_stack_entries(const char* value, size_t size) {
  stack_entries_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:grpc.testing.DebugInfo.stack_entries)
}
inline const ::google::protobuf::RepeatedPtrField< ::std::string>&
DebugInfo::stack_entries() const {
  // @@protoc_insertion_point(field_list:grpc.testing.DebugInfo.stack_entries)
  return stack_entries_;
}
inline ::google::protobuf::RepeatedPtrField< ::std::string>*
DebugInfo::mutable_stack_entries() {
  // @@protoc_insertion_point(field_mutable_list:grpc.testing.DebugInfo.stack_entries)
  return &stack_entries_;
}

// string detail = 2;
inline void DebugInfo::clear_detail() {
  detail_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& DebugInfo::detail() const {
  // @@protoc_insertion_point(field_get:grpc.testing.DebugInfo.detail)
  return detail_.GetNoArena();
}
inline void DebugInfo::set_detail(const ::std::string& value) {
  
  detail_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.testing.DebugInfo.detail)
}
#if LANG_CXX11
inline void DebugInfo::set_detail(::std::string&& value) {
  
  detail_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.testing.DebugInfo.detail)
}
#endif
inline void DebugInfo::set_detail(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  detail_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.testing.DebugInfo.detail)
}
inline void DebugInfo::set_detail(const char* value, size_t size) {
  
  detail_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.testing.DebugInfo.detail)
}
inline ::std::string* DebugInfo::mutable_detail() {
  
  // @@protoc_insertion_point(field_mutable:grpc.testing.DebugInfo.detail)
  return detail_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* DebugInfo::release_detail() {
  // @@protoc_insertion_point(field_release:grpc.testing.DebugInfo.detail)
  
  return detail_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void DebugInfo::set_allocated_detail(::std::string* detail) {
  if (detail != NULL) {
    
  } else {
    
  }
  detail_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), detail);
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.DebugInfo.detail)
}

// -------------------------------------------------------------------

// ErrorStatus

// int32 code = 1;
inline void ErrorStatus::clear_code() {
  code_ = 0;
}
inline ::google::protobuf::int32 ErrorStatus::code() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ErrorStatus.code)
  return code_;
}
inline void ErrorStatus::set_code(::google::protobuf::int32 value) {
  
  code_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ErrorStatus.code)
}

// string error_message = 2;
inline void ErrorStatus::clear_error_message() {
  error_message_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ErrorStatus::error_message() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ErrorStatus.error_message)
  return error_message_.GetNoArena();
}
inline void ErrorStatus::set_error_message(const ::std::string& value) {
  
  error_message_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.testing.ErrorStatus.error_message)
}
#if LANG_CXX11
inline void ErrorStatus::set_error_message(::std::string&& value) {
  
  error_message_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.testing.ErrorStatus.error_message)
}
#endif
inline void ErrorStatus::set_error_message(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  error_message_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.testing.ErrorStatus.error_message)
}
inline void ErrorStatus::set_error_message(const char* value, size_t size) {
  
  error_message_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.testing.ErrorStatus.error_message)
}
inline ::std::string* ErrorStatus::mutable_error_message() {
  
  // @@protoc_insertion_point(field_mutable:grpc.testing.ErrorStatus.error_message)
  return error_message_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ErrorStatus::release_error_message() {
  // @@protoc_insertion_point(field_release:grpc.testing.ErrorStatus.error_message)
  
  return error_message_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ErrorStatus::set_allocated_error_message(::std::string* error_message) {
  if (error_message != NULL) {
    
  } else {
    
  }
  error_message_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), error_message);
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.ErrorStatus.error_message)
}

// string binary_error_details = 3;
inline void ErrorStatus::clear_binary_error_details() {
  binary_error_details_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ErrorStatus::binary_error_details() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ErrorStatus.binary_error_details)
  return binary_error_details_.GetNoArena();
}
inline void ErrorStatus::set_binary_error_details(const ::std::string& value) {
  
  binary_error_details_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.testing.ErrorStatus.binary_error_details)
}
#if LANG_CXX11
inline void ErrorStatus::set_binary_error_details(::std::string&& value) {
  
  binary_error_details_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.testing.ErrorStatus.binary_error_details)
}
#endif
inline void ErrorStatus::set_binary_error_details(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  binary_error_details_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.testing.ErrorStatus.binary_error_details)
}
inline void ErrorStatus::set_binary_error_details(const char* value, size_t size) {
  
  binary_error_details_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.testing.ErrorStatus.binary_error_details)
}
inline ::std::string* ErrorStatus::mutable_binary_error_details() {
  
  // @@protoc_insertion_point(field_mutable:grpc.testing.ErrorStatus.binary_error_details)
  return binary_error_details_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ErrorStatus::release_binary_error_details() {
  // @@protoc_insertion_point(field_release:grpc.testing.ErrorStatus.binary_error_details)
  
  return binary_error_details_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ErrorStatus::set_allocated_binary_error_details(::std::string* binary_error_details) {
  if (binary_error_details != NULL) {
    
  } else {
    
  }
  binary_error_details_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), binary_error_details);
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.ErrorStatus.binary_error_details)
}

// -------------------------------------------------------------------

// RequestParams

// bool echo_deadline = 1;
inline void RequestParams::clear_echo_deadline() {
  echo_deadline_ = false;
}
inline bool RequestParams::echo_deadline() const {
  // @@protoc_insertion_point(field_get:grpc.testing.RequestParams.echo_deadline)
  return echo_deadline_;
}
inline void RequestParams::set_echo_deadline(bool value) {
  
  echo_deadline_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.RequestParams.echo_deadline)
}

// int32 client_cancel_after_us = 2;
inline void RequestParams::clear_client_cancel_after_us() {
  client_cancel_after_us_ = 0;
}
inline ::google::protobuf::int32 RequestParams::client_cancel_after_us() const {
  // @@protoc_insertion_point(field_get:grpc.testing.RequestParams.client_cancel_after_us)
  return client_cancel_after_us_;
}
inline void RequestParams::set_client_cancel_after_us(::google::protobuf::int32 value) {
  
  client_cancel_after_us_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.RequestParams.client_cancel_after_us)
}

// int32 server_cancel_after_us = 3;
inline void RequestParams::clear_server_cancel_after_us() {
  server_cancel_after_us_ = 0;
}
inline ::google::protobuf::int32 RequestParams::server_cancel_after_us() const {
  // @@protoc_insertion_point(field_get:grpc.testing.RequestParams.server_cancel_after_us)
  return server_cancel_after_us_;
}
inline void RequestParams::set_server_cancel_after_us(::google::protobuf::int32 value) {
  
  server_cancel_after_us_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.RequestParams.server_cancel_after_us)
}

// bool echo_metadata = 4;
inline void RequestParams::clear_echo_metadata() {
  echo_metadata_ = false;
}
inline bool RequestParams::echo_metadata() const {
  // @@protoc_insertion_point(field_get:grpc.testing.RequestParams.echo_metadata)
  return echo_metadata_;
}
inline void RequestParams::set_echo_metadata(bool value) {
  
  echo_metadata_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.RequestParams.echo_metadata)
}

// bool check_auth_context = 5;
inline void RequestParams::clear_check_auth_context() {
  check_auth_context_ = false;
}
inline bool RequestParams::check_auth_context() const {
  // @@protoc_insertion_point(field_get:grpc.testing.RequestParams.check_auth_context)
  return check_auth_context_;
}
inline void RequestParams::set_check_auth_context(bool value) {
  
  check_auth_context_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.RequestParams.check_auth_context)
}

// int32 response_message_length = 6;
inline void RequestParams::clear_response_message_length() {
  response_message_length_ = 0;
}
inline ::google::protobuf::int32 RequestParams::response_message_length() const {
  // @@protoc_insertion_point(field_get:grpc.testing.RequestParams.response_message_length)
  return response_message_length_;
}
inline void RequestParams::set_response_message_length(::google::protobuf::int32 value) {
  
  response_message_length_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.RequestParams.response_message_length)
}

// bool echo_peer = 7;
inline void RequestParams::clear_echo_peer() {
  echo_peer_ = false;
}
inline bool RequestParams::echo_peer() const {
  // @@protoc_insertion_point(field_get:grpc.testing.RequestParams.echo_peer)
  return echo_peer_;
}
inline void RequestParams::set_echo_peer(bool value) {
  
  echo_peer_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.RequestParams.echo_peer)
}

// string expected_client_identity = 8;
inline void RequestParams::clear_expected_client_identity() {
  expected_client_identity_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& RequestParams::expected_client_identity() const {
  // @@protoc_insertion_point(field_get:grpc.testing.RequestParams.expected_client_identity)
  return expected_client_identity_.GetNoArena();
}
inline void RequestParams::set_expected_client_identity(const ::std::string& value) {
  
  expected_client_identity_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.testing.RequestParams.expected_client_identity)
}
#if LANG_CXX11
inline void RequestParams::set_expected_client_identity(::std::string&& value) {
  
  expected_client_identity_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.testing.RequestParams.expected_client_identity)
}
#endif
inline void RequestParams::set_expected_client_identity(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  expected_client_identity_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.testing.RequestParams.expected_client_identity)
}
inline void RequestParams::set_expected_client_identity(const char* value, size_t size) {
  
  expected_client_identity_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.testing.RequestParams.expected_client_identity)
}
inline ::std::string* RequestParams::mutable_expected_client_identity() {
  
  // @@protoc_insertion_point(field_mutable:grpc.testing.RequestParams.expected_client_identity)
  return expected_client_identity_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* RequestParams::release_expected_client_identity() {
  // @@protoc_insertion_point(field_release:grpc.testing.RequestParams.expected_client_identity)
  
  return expected_client_identity_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void RequestParams::set_allocated_expected_client_identity(::std::string* expected_client_identity) {
  if (expected_client_identity != NULL) {
    
  } else {
    
  }
  expected_client_identity_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), expected_client_identity);
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.RequestParams.expected_client_identity)
}

// bool skip_cancelled_check = 9;
inline void RequestParams::clear_skip_cancelled_check() {
  skip_cancelled_check_ = false;
}
inline bool RequestParams::skip_cancelled_check() const {
  // @@protoc_insertion_point(field_get:grpc.testing.RequestParams.skip_cancelled_check)
  return skip_cancelled_check_;
}
inline void RequestParams::set_skip_cancelled_check(bool value) {
  
  skip_cancelled_check_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.RequestParams.skip_cancelled_check)
}

// string expected_transport_security_type = 10;
inline void RequestParams::clear_expected_transport_security_type() {
  expected_transport_security_type_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& RequestParams::expected_transport_security_type() const {
  // @@protoc_insertion_point(field_get:grpc.testing.RequestParams.expected_transport_security_type)
  return expected_transport_security_type_.GetNoArena();
}
inline void RequestParams::set_expected_transport_security_type(const ::std::string& value) {
  
  expected_transport_security_type_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.testing.RequestParams.expected_transport_security_type)
}
#if LANG_CXX11
inline void RequestParams::set_expected_transport_security_type(::std::string&& value) {
  
  expected_transport_security_type_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.testing.RequestParams.expected_transport_security_type)
}
#endif
inline void RequestParams::set_expected_transport_security_type(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  expected_transport_security_type_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.testing.RequestParams.expected_transport_security_type)
}
inline void RequestParams::set_expected_transport_security_type(const char* value, size_t size) {
  
  expected_transport_security_type_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.testing.RequestParams.expected_transport_security_type)
}
inline ::std::string* RequestParams::mutable_expected_transport_security_type() {
  
  // @@protoc_insertion_point(field_mutable:grpc.testing.RequestParams.expected_transport_security_type)
  return expected_transport_security_type_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* RequestParams::release_expected_transport_security_type() {
  // @@protoc_insertion_point(field_release:grpc.testing.RequestParams.expected_transport_security_type)
  
  return expected_transport_security_type_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void RequestParams::set_allocated_expected_transport_security_type(::std::string* expected_transport_security_type) {
  if (expected_transport_security_type != NULL) {
    
  } else {
    
  }
  expected_transport_security_type_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), expected_transport_security_type);
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.RequestParams.expected_transport_security_type)
}

// .grpc.testing.DebugInfo debug_info = 11;
inline bool RequestParams::has_debug_info() const {
  return this != internal_default_instance() && debug_info_ != NULL;
}
inline void RequestParams::clear_debug_info() {
  if (GetArenaNoVirtual() == NULL && debug_info_ != NULL) {
    delete debug_info_;
  }
  debug_info_ = NULL;
}
inline const ::grpc::testing::DebugInfo& RequestParams::debug_info() const {
  const ::grpc::testing::DebugInfo* p = debug_info_;
  // @@protoc_insertion_point(field_get:grpc.testing.RequestParams.debug_info)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::testing::DebugInfo*>(
      &::grpc::testing::_DebugInfo_default_instance_);
}
inline ::grpc::testing::DebugInfo* RequestParams::release_debug_info() {
  // @@protoc_insertion_point(field_release:grpc.testing.RequestParams.debug_info)
  
  ::grpc::testing::DebugInfo* temp = debug_info_;
  debug_info_ = NULL;
  return temp;
}
inline ::grpc::testing::DebugInfo* RequestParams::mutable_debug_info() {
  
  if (debug_info_ == NULL) {
    debug_info_ = new ::grpc::testing::DebugInfo;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.RequestParams.debug_info)
  return debug_info_;
}
inline void RequestParams::set_allocated_debug_info(::grpc::testing::DebugInfo* debug_info) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete debug_info_;
  }
  if (debug_info) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      debug_info = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, debug_info, submessage_arena);
    }
    
  } else {
    
  }
  debug_info_ = debug_info;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.RequestParams.debug_info)
}

// bool server_die = 12;
inline void RequestParams::clear_server_die() {
  server_die_ = false;
}
inline bool RequestParams::server_die() const {
  // @@protoc_insertion_point(field_get:grpc.testing.RequestParams.server_die)
  return server_die_;
}
inline void RequestParams::set_server_die(bool value) {
  
  server_die_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.RequestParams.server_die)
}

// string binary_error_details = 13;
inline void RequestParams::clear_binary_error_details() {
  binary_error_details_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& RequestParams::binary_error_details() const {
  // @@protoc_insertion_point(field_get:grpc.testing.RequestParams.binary_error_details)
  return binary_error_details_.GetNoArena();
}
inline void RequestParams::set_binary_error_details(const ::std::string& value) {
  
  binary_error_details_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.testing.RequestParams.binary_error_details)
}
#if LANG_CXX11
inline void RequestParams::set_binary_error_details(::std::string&& value) {
  
  binary_error_details_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.testing.RequestParams.binary_error_details)
}
#endif
inline void RequestParams::set_binary_error_details(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  binary_error_details_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.testing.RequestParams.binary_error_details)
}
inline void RequestParams::set_binary_error_details(const char* value, size_t size) {
  
  binary_error_details_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.testing.RequestParams.binary_error_details)
}
inline ::std::string* RequestParams::mutable_binary_error_details() {
  
  // @@protoc_insertion_point(field_mutable:grpc.testing.RequestParams.binary_error_details)
  return binary_error_details_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* RequestParams::release_binary_error_details() {
  // @@protoc_insertion_point(field_release:grpc.testing.RequestParams.binary_error_details)
  
  return binary_error_details_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void RequestParams::set_allocated_binary_error_details(::std::string* binary_error_details) {
  if (binary_error_details != NULL) {
    
  } else {
    
  }
  binary_error_details_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), binary_error_details);
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.RequestParams.binary_error_details)
}

// .grpc.testing.ErrorStatus expected_error = 14;
inline bool RequestParams::has_expected_error() const {
  return this != internal_default_instance() && expected_error_ != NULL;
}
inline void RequestParams::clear_expected_error() {
  if (GetArenaNoVirtual() == NULL && expected_error_ != NULL) {
    delete expected_error_;
  }
  expected_error_ = NULL;
}
inline const ::grpc::testing::ErrorStatus& RequestParams::expected_error() const {
  const ::grpc::testing::ErrorStatus* p = expected_error_;
  // @@protoc_insertion_point(field_get:grpc.testing.RequestParams.expected_error)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::testing::ErrorStatus*>(
      &::grpc::testing::_ErrorStatus_default_instance_);
}
inline ::grpc::testing::ErrorStatus* RequestParams::release_expected_error() {
  // @@protoc_insertion_point(field_release:grpc.testing.RequestParams.expected_error)
  
  ::grpc::testing::ErrorStatus* temp = expected_error_;
  expected_error_ = NULL;
  return temp;
}
inline ::grpc::testing::ErrorStatus* RequestParams::mutable_expected_error() {
  
  if (expected_error_ == NULL) {
    expected_error_ = new ::grpc::testing::ErrorStatus;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.RequestParams.expected_error)
  return expected_error_;
}
inline void RequestParams::set_allocated_expected_error(::grpc::testing::ErrorStatus* expected_error) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete expected_error_;
  }
  if (expected_error) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      expected_error = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, expected_error, submessage_arena);
    }
    
  } else {
    
  }
  expected_error_ = expected_error;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.RequestParams.expected_error)
}

// int32 server_sleep_us = 15;
inline void RequestParams::clear_server_sleep_us() {
  server_sleep_us_ = 0;
}
inline ::google::protobuf::int32 RequestParams::server_sleep_us() const {
  // @@protoc_insertion_point(field_get:grpc.testing.RequestParams.server_sleep_us)
  return server_sleep_us_;
}
inline void RequestParams::set_server_sleep_us(::google::protobuf::int32 value) {
  
  server_sleep_us_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.RequestParams.server_sleep_us)
}

// -------------------------------------------------------------------

// EchoRequest

// string message = 1;
inline void EchoRequest::clear_message() {
  message_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& EchoRequest::message() const {
  // @@protoc_insertion_point(field_get:grpc.testing.EchoRequest.message)
  return message_.GetNoArena();
}
inline void EchoRequest::set_message(const ::std::string& value) {
  
  message_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.testing.EchoRequest.message)
}
#if LANG_CXX11
inline void EchoRequest::set_message(::std::string&& value) {
  
  message_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.testing.EchoRequest.message)
}
#endif
inline void EchoRequest::set_message(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  message_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.testing.EchoRequest.message)
}
inline void EchoRequest::set_message(const char* value, size_t size) {
  
  message_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.testing.EchoRequest.message)
}
inline ::std::string* EchoRequest::mutable_message() {
  
  // @@protoc_insertion_point(field_mutable:grpc.testing.EchoRequest.message)
  return message_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* EchoRequest::release_message() {
  // @@protoc_insertion_point(field_release:grpc.testing.EchoRequest.message)
  
  return message_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void EchoRequest::set_allocated_message(::std::string* message) {
  if (message != NULL) {
    
  } else {
    
  }
  message_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), message);
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.EchoRequest.message)
}

// .grpc.testing.RequestParams param = 2;
inline bool EchoRequest::has_param() const {
  return this != internal_default_instance() && param_ != NULL;
}
inline void EchoRequest::clear_param() {
  if (GetArenaNoVirtual() == NULL && param_ != NULL) {
    delete param_;
  }
  param_ = NULL;
}
inline const ::grpc::testing::RequestParams& EchoRequest::param() const {
  const ::grpc::testing::RequestParams* p = param_;
  // @@protoc_insertion_point(field_get:grpc.testing.EchoRequest.param)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::testing::RequestParams*>(
      &::grpc::testing::_RequestParams_default_instance_);
}
inline ::grpc::testing::RequestParams* EchoRequest::release_param() {
  // @@protoc_insertion_point(field_release:grpc.testing.EchoRequest.param)
  
  ::grpc::testing::RequestParams* temp = param_;
  param_ = NULL;
  return temp;
}
inline ::grpc::testing::RequestParams* EchoRequest::mutable_param() {
  
  if (param_ == NULL) {
    param_ = new ::grpc::testing::RequestParams;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.EchoRequest.param)
  return param_;
}
inline void EchoRequest::set_allocated_param(::grpc::testing::RequestParams* param) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete param_;
  }
  if (param) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      param = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, param, submessage_arena);
    }
    
  } else {
    
  }
  param_ = param;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.EchoRequest.param)
}

// -------------------------------------------------------------------

// ResponseParams

// int64 request_deadline = 1;
inline void ResponseParams::clear_request_deadline() {
  request_deadline_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ResponseParams::request_deadline() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ResponseParams.request_deadline)
  return request_deadline_;
}
inline void ResponseParams::set_request_deadline(::google::protobuf::int64 value) {
  
  request_deadline_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ResponseParams.request_deadline)
}

// string host = 2;
inline void ResponseParams::clear_host() {
  host_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ResponseParams::host() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ResponseParams.host)
  return host_.GetNoArena();
}
inline void ResponseParams::set_host(const ::std::string& value) {
  
  host_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.testing.ResponseParams.host)
}
#if LANG_CXX11
inline void ResponseParams::set_host(::std::string&& value) {
  
  host_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.testing.ResponseParams.host)
}
#endif
inline void ResponseParams::set_host(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  host_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.testing.ResponseParams.host)
}
inline void ResponseParams::set_host(const char* value, size_t size) {
  
  host_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.testing.ResponseParams.host)
}
inline ::std::string* ResponseParams::mutable_host() {
  
  // @@protoc_insertion_point(field_mutable:grpc.testing.ResponseParams.host)
  return host_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ResponseParams::release_host() {
  // @@protoc_insertion_point(field_release:grpc.testing.ResponseParams.host)
  
  return host_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ResponseParams::set_allocated_host(::std::string* host) {
  if (host != NULL) {
    
  } else {
    
  }
  host_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), host);
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.ResponseParams.host)
}

// string peer = 3;
inline void ResponseParams::clear_peer() {
  peer_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ResponseParams::peer() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ResponseParams.peer)
  return peer_.GetNoArena();
}
inline void ResponseParams::set_peer(const ::std::string& value) {
  
  peer_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.testing.ResponseParams.peer)
}
#if LANG_CXX11
inline void ResponseParams::set_peer(::std::string&& value) {
  
  peer_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.testing.ResponseParams.peer)
}
#endif
inline void ResponseParams::set_peer(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  peer_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.testing.ResponseParams.peer)
}
inline void ResponseParams::set_peer(const char* value, size_t size) {
  
  peer_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.testing.ResponseParams.peer)
}
inline ::std::string* ResponseParams::mutable_peer() {
  
  // @@protoc_insertion_point(field_mutable:grpc.testing.ResponseParams.peer)
  return peer_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ResponseParams::release_peer() {
  // @@protoc_insertion_point(field_release:grpc.testing.ResponseParams.peer)
  
  return peer_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ResponseParams::set_allocated_peer(::std::string* peer) {
  if (peer != NULL) {
    
  } else {
    
  }
  peer_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), peer);
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.ResponseParams.peer)
}

// -------------------------------------------------------------------

// EchoResponse

// string message = 1;
inline void EchoResponse::clear_message() {
  message_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& EchoResponse::message() const {
  // @@protoc_insertion_point(field_get:grpc.testing.EchoResponse.message)
  return message_.GetNoArena();
}
inline void EchoResponse::set_message(const ::std::string& value) {
  
  message_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.testing.EchoResponse.message)
}
#if LANG_CXX11
inline void EchoResponse::set_message(::std::string&& value) {
  
  message_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.testing.EchoResponse.message)
}
#endif
inline void EchoResponse::set_message(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  message_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.testing.EchoResponse.message)
}
inline void EchoResponse::set_message(const char* value, size_t size) {
  
  message_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.testing.EchoResponse.message)
}
inline ::std::string* EchoResponse::mutable_message() {
  
  // @@protoc_insertion_point(field_mutable:grpc.testing.EchoResponse.message)
  return message_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* EchoResponse::release_message() {
  // @@protoc_insertion_point(field_release:grpc.testing.EchoResponse.message)
  
  return message_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void EchoResponse::set_allocated_message(::std::string* message) {
  if (message != NULL) {
    
  } else {
    
  }
  message_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), message);
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.EchoResponse.message)
}

// .grpc.testing.ResponseParams param = 2;
inline bool EchoResponse::has_param() const {
  return this != internal_default_instance() && param_ != NULL;
}
inline void EchoResponse::clear_param() {
  if (GetArenaNoVirtual() == NULL && param_ != NULL) {
    delete param_;
  }
  param_ = NULL;
}
inline const ::grpc::testing::ResponseParams& EchoResponse::param() const {
  const ::grpc::testing::ResponseParams* p = param_;
  // @@protoc_insertion_point(field_get:grpc.testing.EchoResponse.param)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::testing::ResponseParams*>(
      &::grpc::testing::_ResponseParams_default_instance_);
}
inline ::grpc::testing::ResponseParams* EchoResponse::release_param() {
  // @@protoc_insertion_point(field_release:grpc.testing.EchoResponse.param)
  
  ::grpc::testing::ResponseParams* temp = param_;
  param_ = NULL;
  return temp;
}
inline ::grpc::testing::ResponseParams* EchoResponse::mutable_param() {
  
  if (param_ == NULL) {
    param_ = new ::grpc::testing::ResponseParams;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.EchoResponse.param)
  return param_;
}
inline void EchoResponse::set_allocated_param(::grpc::testing::ResponseParams* param) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete param_;
  }
  if (param) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      param = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, param, submessage_arena);
    }
    
  } else {
    
  }
  param_ = param;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.EchoResponse.param)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace testing
}  // namespace grpc

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_src_2fproto_2fgrpc_2ftesting_2fecho_5fmessages_2eproto__INCLUDED
