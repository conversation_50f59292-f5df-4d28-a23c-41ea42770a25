// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: src/proto/grpc/testing/echo.proto

#include "src/proto/grpc/testing/echo.pb.h"
#include "src/proto/grpc/testing/echo.grpc.pb.h"

#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/sync_stream.h>
#include <gmock/gmock.h>
namespace grpc {
namespace testing {

class MockEchoTestServiceStub : public EchoTestService::StubInterface {
 public:
  MOCK_METHOD3(Echo, ::grpc::Status(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::testing::EchoResponse* response));
  MOCK_METHOD3(AsyncEchoRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::EchoResponse>*(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncEchoRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::EchoResponse>*(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD2(RequestStreamRaw, ::grpc::ClientWriterInterface< ::grpc::testing::EchoRequest>*(::grpc::ClientContext* context, ::grpc::testing::EchoResponse* response));
  MOCK_METHOD4(AsyncRequestStreamRaw, ::grpc::ClientAsyncWriterInterface< ::grpc::testing::EchoRequest>*(::grpc::ClientContext* context, ::grpc::testing::EchoResponse* response, ::grpc::CompletionQueue* cq, void* tag));
  MOCK_METHOD3(PrepareAsyncRequestStreamRaw, ::grpc::ClientAsyncWriterInterface< ::grpc::testing::EchoRequest>*(::grpc::ClientContext* context, ::grpc::testing::EchoResponse* response, ::grpc::CompletionQueue* cq));
  MOCK_METHOD2(ResponseStreamRaw, ::grpc::ClientReaderInterface< ::grpc::testing::EchoResponse>*(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request));
  MOCK_METHOD4(AsyncResponseStreamRaw, ::grpc::ClientAsyncReaderInterface< ::grpc::testing::EchoResponse>*(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq, void* tag));
  MOCK_METHOD3(PrepareAsyncResponseStreamRaw, ::grpc::ClientAsyncReaderInterface< ::grpc::testing::EchoResponse>*(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD1(BidiStreamRaw, ::grpc::ClientReaderWriterInterface< ::grpc::testing::EchoRequest, ::grpc::testing::EchoResponse>*(::grpc::ClientContext* context));
  MOCK_METHOD3(AsyncBidiStreamRaw, ::grpc::ClientAsyncReaderWriterInterface<::grpc::testing::EchoRequest, ::grpc::testing::EchoResponse>*(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag));
  MOCK_METHOD2(PrepareAsyncBidiStreamRaw, ::grpc::ClientAsyncReaderWriterInterface<::grpc::testing::EchoRequest, ::grpc::testing::EchoResponse>*(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(Unimplemented, ::grpc::Status(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::testing::EchoResponse* response));
  MOCK_METHOD3(AsyncUnimplementedRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::EchoResponse>*(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncUnimplementedRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::EchoResponse>*(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq));
};

class MockUnimplementedEchoServiceStub : public UnimplementedEchoService::StubInterface {
 public:
  MOCK_METHOD3(Unimplemented, ::grpc::Status(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::testing::EchoResponse* response));
  MOCK_METHOD3(AsyncUnimplementedRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::EchoResponse>*(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncUnimplementedRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::EchoResponse>*(::grpc::ClientContext* context, const ::grpc::testing::EchoRequest& request, ::grpc::CompletionQueue* cq));
};

class MockNoRpcServiceStub : public NoRpcService::StubInterface {
 public:
};

} // namespace grpc
} // namespace testing

