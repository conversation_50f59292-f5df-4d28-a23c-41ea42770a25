// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: src/proto/grpc/testing/messages.proto

#include "src/proto/grpc/testing/messages.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)
namespace grpc {
namespace testing {
class BoolValueDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<BoolValue>
      _instance;
} _BoolValue_default_instance_;
class PayloadDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Payload>
      _instance;
} _Payload_default_instance_;
class EchoStatusDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<EchoStatus>
      _instance;
} _EchoStatus_default_instance_;
class SimpleRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SimpleRequest>
      _instance;
} _SimpleRequest_default_instance_;
class SimpleResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SimpleResponse>
      _instance;
} _SimpleResponse_default_instance_;
class StreamingInputCallRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<StreamingInputCallRequest>
      _instance;
} _StreamingInputCallRequest_default_instance_;
class StreamingInputCallResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<StreamingInputCallResponse>
      _instance;
} _StreamingInputCallResponse_default_instance_;
class ResponseParametersDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ResponseParameters>
      _instance;
} _ResponseParameters_default_instance_;
class StreamingOutputCallRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<StreamingOutputCallRequest>
      _instance;
} _StreamingOutputCallRequest_default_instance_;
class StreamingOutputCallResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<StreamingOutputCallResponse>
      _instance;
} _StreamingOutputCallResponse_default_instance_;
class ReconnectParamsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ReconnectParams>
      _instance;
} _ReconnectParams_default_instance_;
class ReconnectInfoDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ReconnectInfo>
      _instance;
} _ReconnectInfo_default_instance_;
}  // namespace testing
}  // namespace grpc
namespace protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto {
void InitDefaultsBoolValueImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::testing::_BoolValue_default_instance_;
    new (ptr) ::grpc::testing::BoolValue();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::BoolValue::InitAsDefaultInstance();
}

void InitDefaultsBoolValue() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsBoolValueImpl);
}

void InitDefaultsPayloadImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::testing::_Payload_default_instance_;
    new (ptr) ::grpc::testing::Payload();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::Payload::InitAsDefaultInstance();
}

void InitDefaultsPayload() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsPayloadImpl);
}

void InitDefaultsEchoStatusImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::testing::_EchoStatus_default_instance_;
    new (ptr) ::grpc::testing::EchoStatus();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::EchoStatus::InitAsDefaultInstance();
}

void InitDefaultsEchoStatus() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsEchoStatusImpl);
}

void InitDefaultsSimpleRequestImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsPayload();
  protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsBoolValue();
  protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsEchoStatus();
  {
    void* ptr = &::grpc::testing::_SimpleRequest_default_instance_;
    new (ptr) ::grpc::testing::SimpleRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::SimpleRequest::InitAsDefaultInstance();
}

void InitDefaultsSimpleRequest() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsSimpleRequestImpl);
}

void InitDefaultsSimpleResponseImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsPayload();
  {
    void* ptr = &::grpc::testing::_SimpleResponse_default_instance_;
    new (ptr) ::grpc::testing::SimpleResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::SimpleResponse::InitAsDefaultInstance();
}

void InitDefaultsSimpleResponse() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsSimpleResponseImpl);
}

void InitDefaultsStreamingInputCallRequestImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsPayload();
  protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsBoolValue();
  {
    void* ptr = &::grpc::testing::_StreamingInputCallRequest_default_instance_;
    new (ptr) ::grpc::testing::StreamingInputCallRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::StreamingInputCallRequest::InitAsDefaultInstance();
}

void InitDefaultsStreamingInputCallRequest() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsStreamingInputCallRequestImpl);
}

void InitDefaultsStreamingInputCallResponseImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::testing::_StreamingInputCallResponse_default_instance_;
    new (ptr) ::grpc::testing::StreamingInputCallResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::StreamingInputCallResponse::InitAsDefaultInstance();
}

void InitDefaultsStreamingInputCallResponse() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsStreamingInputCallResponseImpl);
}

void InitDefaultsResponseParametersImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsBoolValue();
  {
    void* ptr = &::grpc::testing::_ResponseParameters_default_instance_;
    new (ptr) ::grpc::testing::ResponseParameters();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::ResponseParameters::InitAsDefaultInstance();
}

void InitDefaultsResponseParameters() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsResponseParametersImpl);
}

void InitDefaultsStreamingOutputCallRequestImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsResponseParameters();
  protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsPayload();
  protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsEchoStatus();
  {
    void* ptr = &::grpc::testing::_StreamingOutputCallRequest_default_instance_;
    new (ptr) ::grpc::testing::StreamingOutputCallRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::StreamingOutputCallRequest::InitAsDefaultInstance();
}

void InitDefaultsStreamingOutputCallRequest() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsStreamingOutputCallRequestImpl);
}

void InitDefaultsStreamingOutputCallResponseImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsPayload();
  {
    void* ptr = &::grpc::testing::_StreamingOutputCallResponse_default_instance_;
    new (ptr) ::grpc::testing::StreamingOutputCallResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::StreamingOutputCallResponse::InitAsDefaultInstance();
}

void InitDefaultsStreamingOutputCallResponse() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsStreamingOutputCallResponseImpl);
}

void InitDefaultsReconnectParamsImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::testing::_ReconnectParams_default_instance_;
    new (ptr) ::grpc::testing::ReconnectParams();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::ReconnectParams::InitAsDefaultInstance();
}

void InitDefaultsReconnectParams() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsReconnectParamsImpl);
}

void InitDefaultsReconnectInfoImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::testing::_ReconnectInfo_default_instance_;
    new (ptr) ::grpc::testing::ReconnectInfo();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::ReconnectInfo::InitAsDefaultInstance();
}

void InitDefaultsReconnectInfo() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsReconnectInfoImpl);
}

::google::protobuf::Metadata file_level_metadata[12];
const ::google::protobuf::EnumDescriptor* file_level_enum_descriptors[1];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::BoolValue, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::BoolValue, value_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::Payload, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::Payload, type_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::Payload, body_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::EchoStatus, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::EchoStatus, code_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::EchoStatus, message_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::SimpleRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::SimpleRequest, response_type_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::SimpleRequest, response_size_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::SimpleRequest, payload_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::SimpleRequest, fill_username_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::SimpleRequest, fill_oauth_scope_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::SimpleRequest, response_compressed_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::SimpleRequest, response_status_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::SimpleRequest, expect_compressed_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::SimpleResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::SimpleResponse, payload_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::SimpleResponse, username_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::SimpleResponse, oauth_scope_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::StreamingInputCallRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::StreamingInputCallRequest, payload_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::StreamingInputCallRequest, expect_compressed_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::StreamingInputCallResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::StreamingInputCallResponse, aggregated_payload_size_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ResponseParameters, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ResponseParameters, size_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ResponseParameters, interval_us_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ResponseParameters, compressed_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::StreamingOutputCallRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::StreamingOutputCallRequest, response_type_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::StreamingOutputCallRequest, response_parameters_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::StreamingOutputCallRequest, payload_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::StreamingOutputCallRequest, response_status_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::StreamingOutputCallResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::StreamingOutputCallResponse, payload_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ReconnectParams, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ReconnectParams, max_reconnect_backoff_ms_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ReconnectInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ReconnectInfo, passed_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ReconnectInfo, backoff_ms_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::grpc::testing::BoolValue)},
  { 6, -1, sizeof(::grpc::testing::Payload)},
  { 13, -1, sizeof(::grpc::testing::EchoStatus)},
  { 20, -1, sizeof(::grpc::testing::SimpleRequest)},
  { 33, -1, sizeof(::grpc::testing::SimpleResponse)},
  { 41, -1, sizeof(::grpc::testing::StreamingInputCallRequest)},
  { 48, -1, sizeof(::grpc::testing::StreamingInputCallResponse)},
  { 54, -1, sizeof(::grpc::testing::ResponseParameters)},
  { 62, -1, sizeof(::grpc::testing::StreamingOutputCallRequest)},
  { 71, -1, sizeof(::grpc::testing::StreamingOutputCallResponse)},
  { 77, -1, sizeof(::grpc::testing::ReconnectParams)},
  { 83, -1, sizeof(::grpc::testing::ReconnectInfo)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_BoolValue_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_Payload_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_EchoStatus_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_SimpleRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_SimpleResponse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_StreamingInputCallRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_StreamingInputCallResponse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_ResponseParameters_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_StreamingOutputCallRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_StreamingOutputCallResponse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_ReconnectParams_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_ReconnectInfo_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  ::google::protobuf::MessageFactory* factory = NULL;
  AssignDescriptors(
      "src/proto/grpc/testing/messages.proto", schemas, file_default_instances, TableStruct::offsets, factory,
      file_level_metadata, file_level_enum_descriptors, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 12);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n%src/proto/grpc/testing/messages.proto\022"
      "\014grpc.testing\"\032\n\tBoolValue\022\r\n\005value\030\001 \001("
      "\010\"@\n\007Payload\022\'\n\004type\030\001 \001(\0162\031.grpc.testin"
      "g.PayloadType\022\014\n\004body\030\002 \001(\014\"+\n\nEchoStatu"
      "s\022\014\n\004code\030\001 \001(\005\022\017\n\007message\030\002 \001(\t\"\316\002\n\rSim"
      "pleRequest\0220\n\rresponse_type\030\001 \001(\0162\031.grpc"
      ".testing.PayloadType\022\025\n\rresponse_size\030\002 "
      "\001(\005\022&\n\007payload\030\003 \001(\0132\025.grpc.testing.Payl"
      "oad\022\025\n\rfill_username\030\004 \001(\010\022\030\n\020fill_oauth"
      "_scope\030\005 \001(\010\0224\n\023response_compressed\030\006 \001("
      "\0132\027.grpc.testing.BoolValue\0221\n\017response_s"
      "tatus\030\007 \001(\0132\030.grpc.testing.EchoStatus\0222\n"
      "\021expect_compressed\030\010 \001(\0132\027.grpc.testing."
      "BoolValue\"_\n\016SimpleResponse\022&\n\007payload\030\001"
      " \001(\0132\025.grpc.testing.Payload\022\020\n\010username\030"
      "\002 \001(\t\022\023\n\013oauth_scope\030\003 \001(\t\"w\n\031StreamingI"
      "nputCallRequest\022&\n\007payload\030\001 \001(\0132\025.grpc."
      "testing.Payload\0222\n\021expect_compressed\030\002 \001"
      "(\0132\027.grpc.testing.BoolValue\"=\n\032Streaming"
      "InputCallResponse\022\037\n\027aggregated_payload_"
      "size\030\001 \001(\005\"d\n\022ResponseParameters\022\014\n\004size"
      "\030\001 \001(\005\022\023\n\013interval_us\030\002 \001(\005\022+\n\ncompresse"
      "d\030\003 \001(\0132\027.grpc.testing.BoolValue\"\350\001\n\032Str"
      "eamingOutputCallRequest\0220\n\rresponse_type"
      "\030\001 \001(\0162\031.grpc.testing.PayloadType\022=\n\023res"
      "ponse_parameters\030\002 \003(\0132 .grpc.testing.Re"
      "sponseParameters\022&\n\007payload\030\003 \001(\0132\025.grpc"
      ".testing.Payload\0221\n\017response_status\030\007 \001("
      "\0132\030.grpc.testing.EchoStatus\"E\n\033Streaming"
      "OutputCallResponse\022&\n\007payload\030\001 \001(\0132\025.gr"
      "pc.testing.Payload\"3\n\017ReconnectParams\022 \n"
      "\030max_reconnect_backoff_ms\030\001 \001(\005\"3\n\rRecon"
      "nectInfo\022\016\n\006passed\030\001 \001(\010\022\022\n\nbackoff_ms\030\002"
      " \003(\005*\037\n\013PayloadType\022\020\n\014COMPRESSABLE\020\000b\006p"
      "roto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 1365);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "src/proto/grpc/testing/messages.proto", &protobuf_RegisterTypes);
}

void AddDescriptors() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto
namespace grpc {
namespace testing {
const ::google::protobuf::EnumDescriptor* PayloadType_descriptor() {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::file_level_enum_descriptors[0];
}
bool PayloadType_IsValid(int value) {
  switch (value) {
    case 0:
      return true;
    default:
      return false;
  }
}


// ===================================================================

void BoolValue::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int BoolValue::kValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

BoolValue::BoolValue()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsBoolValue();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.BoolValue)
}
BoolValue::BoolValue(const BoolValue& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  value_ = from.value_;
  // @@protoc_insertion_point(copy_constructor:grpc.testing.BoolValue)
}

void BoolValue::SharedCtor() {
  value_ = false;
  _cached_size_ = 0;
}

BoolValue::~BoolValue() {
  // @@protoc_insertion_point(destructor:grpc.testing.BoolValue)
  SharedDtor();
}

void BoolValue::SharedDtor() {
}

void BoolValue::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* BoolValue::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const BoolValue& BoolValue::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsBoolValue();
  return *internal_default_instance();
}

BoolValue* BoolValue::New(::google::protobuf::Arena* arena) const {
  BoolValue* n = new BoolValue;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void BoolValue::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.BoolValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  value_ = false;
  _internal_metadata_.Clear();
}

bool BoolValue::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.BoolValue)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // bool value = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &value_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.BoolValue)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.BoolValue)
  return false;
#undef DO_
}

void BoolValue::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.BoolValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bool value = 1;
  if (this->value() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(1, this->value(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.BoolValue)
}

::google::protobuf::uint8* BoolValue::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.BoolValue)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bool value = 1;
  if (this->value() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(1, this->value(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.BoolValue)
  return target;
}

size_t BoolValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.BoolValue)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // bool value = 1;
  if (this->value() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void BoolValue::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.BoolValue)
  GOOGLE_DCHECK_NE(&from, this);
  const BoolValue* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const BoolValue>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.BoolValue)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.BoolValue)
    MergeFrom(*source);
  }
}

void BoolValue::MergeFrom(const BoolValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.BoolValue)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.value() != 0) {
    set_value(from.value());
  }
}

void BoolValue::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.BoolValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void BoolValue::CopyFrom(const BoolValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.BoolValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BoolValue::IsInitialized() const {
  return true;
}

void BoolValue::Swap(BoolValue* other) {
  if (other == this) return;
  InternalSwap(other);
}
void BoolValue::InternalSwap(BoolValue* other) {
  using std::swap;
  swap(value_, other->value_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata BoolValue::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Payload::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Payload::kTypeFieldNumber;
const int Payload::kBodyFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Payload::Payload()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsPayload();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.Payload)
}
Payload::Payload(const Payload& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  body_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.body().size() > 0) {
    body_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.body_);
  }
  type_ = from.type_;
  // @@protoc_insertion_point(copy_constructor:grpc.testing.Payload)
}

void Payload::SharedCtor() {
  body_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  type_ = 0;
  _cached_size_ = 0;
}

Payload::~Payload() {
  // @@protoc_insertion_point(destructor:grpc.testing.Payload)
  SharedDtor();
}

void Payload::SharedDtor() {
  body_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void Payload::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Payload::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Payload& Payload::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsPayload();
  return *internal_default_instance();
}

Payload* Payload::New(::google::protobuf::Arena* arena) const {
  Payload* n = new Payload;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Payload::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.Payload)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  body_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  type_ = 0;
  _internal_metadata_.Clear();
}

bool Payload::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.Payload)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .grpc.testing.PayloadType type = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_type(static_cast< ::grpc::testing::PayloadType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bytes body = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_body()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.Payload)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.Payload)
  return false;
#undef DO_
}

void Payload::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.Payload)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.PayloadType type = 1;
  if (this->type() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->type(), output);
  }

  // bytes body = 2;
  if (this->body().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      2, this->body(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.Payload)
}

::google::protobuf::uint8* Payload::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.Payload)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.PayloadType type = 1;
  if (this->type() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->type(), target);
  }

  // bytes body = 2;
  if (this->body().size() > 0) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        2, this->body(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.Payload)
  return target;
}

size_t Payload::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.Payload)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // bytes body = 2;
  if (this->body().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::BytesSize(
        this->body());
  }

  // .grpc.testing.PayloadType type = 1;
  if (this->type() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->type());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Payload::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.Payload)
  GOOGLE_DCHECK_NE(&from, this);
  const Payload* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Payload>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.Payload)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.Payload)
    MergeFrom(*source);
  }
}

void Payload::MergeFrom(const Payload& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.Payload)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.body().size() > 0) {

    body_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.body_);
  }
  if (from.type() != 0) {
    set_type(from.type());
  }
}

void Payload::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.Payload)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Payload::CopyFrom(const Payload& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.Payload)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Payload::IsInitialized() const {
  return true;
}

void Payload::Swap(Payload* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Payload::InternalSwap(Payload* other) {
  using std::swap;
  body_.Swap(&other->body_);
  swap(type_, other->type_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Payload::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void EchoStatus::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int EchoStatus::kCodeFieldNumber;
const int EchoStatus::kMessageFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

EchoStatus::EchoStatus()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsEchoStatus();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.EchoStatus)
}
EchoStatus::EchoStatus(const EchoStatus& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  message_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.message().size() > 0) {
    message_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.message_);
  }
  code_ = from.code_;
  // @@protoc_insertion_point(copy_constructor:grpc.testing.EchoStatus)
}

void EchoStatus::SharedCtor() {
  message_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  code_ = 0;
  _cached_size_ = 0;
}

EchoStatus::~EchoStatus() {
  // @@protoc_insertion_point(destructor:grpc.testing.EchoStatus)
  SharedDtor();
}

void EchoStatus::SharedDtor() {
  message_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void EchoStatus::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* EchoStatus::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const EchoStatus& EchoStatus::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsEchoStatus();
  return *internal_default_instance();
}

EchoStatus* EchoStatus::New(::google::protobuf::Arena* arena) const {
  EchoStatus* n = new EchoStatus;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void EchoStatus::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.EchoStatus)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  message_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  code_ = 0;
  _internal_metadata_.Clear();
}

bool EchoStatus::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.EchoStatus)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 code = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &code_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string message = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_message()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->message().data(), static_cast<int>(this->message().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.testing.EchoStatus.message"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.EchoStatus)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.EchoStatus)
  return false;
#undef DO_
}

void EchoStatus::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.EchoStatus)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 code = 1;
  if (this->code() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->code(), output);
  }

  // string message = 2;
  if (this->message().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->message().data(), static_cast<int>(this->message().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.EchoStatus.message");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->message(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.EchoStatus)
}

::google::protobuf::uint8* EchoStatus::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.EchoStatus)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 code = 1;
  if (this->code() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->code(), target);
  }

  // string message = 2;
  if (this->message().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->message().data(), static_cast<int>(this->message().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.EchoStatus.message");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->message(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.EchoStatus)
  return target;
}

size_t EchoStatus::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.EchoStatus)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string message = 2;
  if (this->message().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->message());
  }

  // int32 code = 1;
  if (this->code() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->code());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void EchoStatus::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.EchoStatus)
  GOOGLE_DCHECK_NE(&from, this);
  const EchoStatus* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const EchoStatus>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.EchoStatus)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.EchoStatus)
    MergeFrom(*source);
  }
}

void EchoStatus::MergeFrom(const EchoStatus& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.EchoStatus)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.message().size() > 0) {

    message_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.message_);
  }
  if (from.code() != 0) {
    set_code(from.code());
  }
}

void EchoStatus::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.EchoStatus)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void EchoStatus::CopyFrom(const EchoStatus& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.EchoStatus)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EchoStatus::IsInitialized() const {
  return true;
}

void EchoStatus::Swap(EchoStatus* other) {
  if (other == this) return;
  InternalSwap(other);
}
void EchoStatus::InternalSwap(EchoStatus* other) {
  using std::swap;
  message_.Swap(&other->message_);
  swap(code_, other->code_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata EchoStatus::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void SimpleRequest::InitAsDefaultInstance() {
  ::grpc::testing::_SimpleRequest_default_instance_._instance.get_mutable()->payload_ = const_cast< ::grpc::testing::Payload*>(
      ::grpc::testing::Payload::internal_default_instance());
  ::grpc::testing::_SimpleRequest_default_instance_._instance.get_mutable()->response_compressed_ = const_cast< ::grpc::testing::BoolValue*>(
      ::grpc::testing::BoolValue::internal_default_instance());
  ::grpc::testing::_SimpleRequest_default_instance_._instance.get_mutable()->response_status_ = const_cast< ::grpc::testing::EchoStatus*>(
      ::grpc::testing::EchoStatus::internal_default_instance());
  ::grpc::testing::_SimpleRequest_default_instance_._instance.get_mutable()->expect_compressed_ = const_cast< ::grpc::testing::BoolValue*>(
      ::grpc::testing::BoolValue::internal_default_instance());
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SimpleRequest::kResponseTypeFieldNumber;
const int SimpleRequest::kResponseSizeFieldNumber;
const int SimpleRequest::kPayloadFieldNumber;
const int SimpleRequest::kFillUsernameFieldNumber;
const int SimpleRequest::kFillOauthScopeFieldNumber;
const int SimpleRequest::kResponseCompressedFieldNumber;
const int SimpleRequest::kResponseStatusFieldNumber;
const int SimpleRequest::kExpectCompressedFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SimpleRequest::SimpleRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsSimpleRequest();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.SimpleRequest)
}
SimpleRequest::SimpleRequest(const SimpleRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_payload()) {
    payload_ = new ::grpc::testing::Payload(*from.payload_);
  } else {
    payload_ = NULL;
  }
  if (from.has_response_compressed()) {
    response_compressed_ = new ::grpc::testing::BoolValue(*from.response_compressed_);
  } else {
    response_compressed_ = NULL;
  }
  if (from.has_response_status()) {
    response_status_ = new ::grpc::testing::EchoStatus(*from.response_status_);
  } else {
    response_status_ = NULL;
  }
  if (from.has_expect_compressed()) {
    expect_compressed_ = new ::grpc::testing::BoolValue(*from.expect_compressed_);
  } else {
    expect_compressed_ = NULL;
  }
  ::memcpy(&response_type_, &from.response_type_,
    static_cast<size_t>(reinterpret_cast<char*>(&fill_oauth_scope_) -
    reinterpret_cast<char*>(&response_type_)) + sizeof(fill_oauth_scope_));
  // @@protoc_insertion_point(copy_constructor:grpc.testing.SimpleRequest)
}

void SimpleRequest::SharedCtor() {
  ::memset(&payload_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&fill_oauth_scope_) -
      reinterpret_cast<char*>(&payload_)) + sizeof(fill_oauth_scope_));
  _cached_size_ = 0;
}

SimpleRequest::~SimpleRequest() {
  // @@protoc_insertion_point(destructor:grpc.testing.SimpleRequest)
  SharedDtor();
}

void SimpleRequest::SharedDtor() {
  if (this != internal_default_instance()) delete payload_;
  if (this != internal_default_instance()) delete response_compressed_;
  if (this != internal_default_instance()) delete response_status_;
  if (this != internal_default_instance()) delete expect_compressed_;
}

void SimpleRequest::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SimpleRequest::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const SimpleRequest& SimpleRequest::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsSimpleRequest();
  return *internal_default_instance();
}

SimpleRequest* SimpleRequest::New(::google::protobuf::Arena* arena) const {
  SimpleRequest* n = new SimpleRequest;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void SimpleRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.SimpleRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && payload_ != NULL) {
    delete payload_;
  }
  payload_ = NULL;
  if (GetArenaNoVirtual() == NULL && response_compressed_ != NULL) {
    delete response_compressed_;
  }
  response_compressed_ = NULL;
  if (GetArenaNoVirtual() == NULL && response_status_ != NULL) {
    delete response_status_;
  }
  response_status_ = NULL;
  if (GetArenaNoVirtual() == NULL && expect_compressed_ != NULL) {
    delete expect_compressed_;
  }
  expect_compressed_ = NULL;
  ::memset(&response_type_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&fill_oauth_scope_) -
      reinterpret_cast<char*>(&response_type_)) + sizeof(fill_oauth_scope_));
  _internal_metadata_.Clear();
}

bool SimpleRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.SimpleRequest)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .grpc.testing.PayloadType response_type = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_response_type(static_cast< ::grpc::testing::PayloadType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 response_size = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &response_size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.testing.Payload payload = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_payload()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool fill_username = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &fill_username_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool fill_oauth_scope = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &fill_oauth_scope_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.testing.BoolValue response_compressed = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(50u /* 50 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_response_compressed()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.testing.EchoStatus response_status = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_response_status()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.testing.BoolValue expect_compressed = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(66u /* 66 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_expect_compressed()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.SimpleRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.SimpleRequest)
  return false;
#undef DO_
}

void SimpleRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.SimpleRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.PayloadType response_type = 1;
  if (this->response_type() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->response_type(), output);
  }

  // int32 response_size = 2;
  if (this->response_size() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->response_size(), output);
  }

  // .grpc.testing.Payload payload = 3;
  if (this->has_payload()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, *this->payload_, output);
  }

  // bool fill_username = 4;
  if (this->fill_username() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(4, this->fill_username(), output);
  }

  // bool fill_oauth_scope = 5;
  if (this->fill_oauth_scope() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(5, this->fill_oauth_scope(), output);
  }

  // .grpc.testing.BoolValue response_compressed = 6;
  if (this->has_response_compressed()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      6, *this->response_compressed_, output);
  }

  // .grpc.testing.EchoStatus response_status = 7;
  if (this->has_response_status()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7, *this->response_status_, output);
  }

  // .grpc.testing.BoolValue expect_compressed = 8;
  if (this->has_expect_compressed()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8, *this->expect_compressed_, output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.SimpleRequest)
}

::google::protobuf::uint8* SimpleRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.SimpleRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.PayloadType response_type = 1;
  if (this->response_type() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->response_type(), target);
  }

  // int32 response_size = 2;
  if (this->response_size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->response_size(), target);
  }

  // .grpc.testing.Payload payload = 3;
  if (this->has_payload()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, *this->payload_, deterministic, target);
  }

  // bool fill_username = 4;
  if (this->fill_username() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(4, this->fill_username(), target);
  }

  // bool fill_oauth_scope = 5;
  if (this->fill_oauth_scope() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(5, this->fill_oauth_scope(), target);
  }

  // .grpc.testing.BoolValue response_compressed = 6;
  if (this->has_response_compressed()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        6, *this->response_compressed_, deterministic, target);
  }

  // .grpc.testing.EchoStatus response_status = 7;
  if (this->has_response_status()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        7, *this->response_status_, deterministic, target);
  }

  // .grpc.testing.BoolValue expect_compressed = 8;
  if (this->has_expect_compressed()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        8, *this->expect_compressed_, deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.SimpleRequest)
  return target;
}

size_t SimpleRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.SimpleRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .grpc.testing.Payload payload = 3;
  if (this->has_payload()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->payload_);
  }

  // .grpc.testing.BoolValue response_compressed = 6;
  if (this->has_response_compressed()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->response_compressed_);
  }

  // .grpc.testing.EchoStatus response_status = 7;
  if (this->has_response_status()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->response_status_);
  }

  // .grpc.testing.BoolValue expect_compressed = 8;
  if (this->has_expect_compressed()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->expect_compressed_);
  }

  // .grpc.testing.PayloadType response_type = 1;
  if (this->response_type() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->response_type());
  }

  // int32 response_size = 2;
  if (this->response_size() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->response_size());
  }

  // bool fill_username = 4;
  if (this->fill_username() != 0) {
    total_size += 1 + 1;
  }

  // bool fill_oauth_scope = 5;
  if (this->fill_oauth_scope() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SimpleRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.SimpleRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const SimpleRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SimpleRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.SimpleRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.SimpleRequest)
    MergeFrom(*source);
  }
}

void SimpleRequest::MergeFrom(const SimpleRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.SimpleRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_payload()) {
    mutable_payload()->::grpc::testing::Payload::MergeFrom(from.payload());
  }
  if (from.has_response_compressed()) {
    mutable_response_compressed()->::grpc::testing::BoolValue::MergeFrom(from.response_compressed());
  }
  if (from.has_response_status()) {
    mutable_response_status()->::grpc::testing::EchoStatus::MergeFrom(from.response_status());
  }
  if (from.has_expect_compressed()) {
    mutable_expect_compressed()->::grpc::testing::BoolValue::MergeFrom(from.expect_compressed());
  }
  if (from.response_type() != 0) {
    set_response_type(from.response_type());
  }
  if (from.response_size() != 0) {
    set_response_size(from.response_size());
  }
  if (from.fill_username() != 0) {
    set_fill_username(from.fill_username());
  }
  if (from.fill_oauth_scope() != 0) {
    set_fill_oauth_scope(from.fill_oauth_scope());
  }
}

void SimpleRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.SimpleRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SimpleRequest::CopyFrom(const SimpleRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.SimpleRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SimpleRequest::IsInitialized() const {
  return true;
}

void SimpleRequest::Swap(SimpleRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void SimpleRequest::InternalSwap(SimpleRequest* other) {
  using std::swap;
  swap(payload_, other->payload_);
  swap(response_compressed_, other->response_compressed_);
  swap(response_status_, other->response_status_);
  swap(expect_compressed_, other->expect_compressed_);
  swap(response_type_, other->response_type_);
  swap(response_size_, other->response_size_);
  swap(fill_username_, other->fill_username_);
  swap(fill_oauth_scope_, other->fill_oauth_scope_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata SimpleRequest::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void SimpleResponse::InitAsDefaultInstance() {
  ::grpc::testing::_SimpleResponse_default_instance_._instance.get_mutable()->payload_ = const_cast< ::grpc::testing::Payload*>(
      ::grpc::testing::Payload::internal_default_instance());
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SimpleResponse::kPayloadFieldNumber;
const int SimpleResponse::kUsernameFieldNumber;
const int SimpleResponse::kOauthScopeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SimpleResponse::SimpleResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsSimpleResponse();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.SimpleResponse)
}
SimpleResponse::SimpleResponse(const SimpleResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  username_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.username().size() > 0) {
    username_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.username_);
  }
  oauth_scope_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.oauth_scope().size() > 0) {
    oauth_scope_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.oauth_scope_);
  }
  if (from.has_payload()) {
    payload_ = new ::grpc::testing::Payload(*from.payload_);
  } else {
    payload_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:grpc.testing.SimpleResponse)
}

void SimpleResponse::SharedCtor() {
  username_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  oauth_scope_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  payload_ = NULL;
  _cached_size_ = 0;
}

SimpleResponse::~SimpleResponse() {
  // @@protoc_insertion_point(destructor:grpc.testing.SimpleResponse)
  SharedDtor();
}

void SimpleResponse::SharedDtor() {
  username_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  oauth_scope_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete payload_;
}

void SimpleResponse::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SimpleResponse::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const SimpleResponse& SimpleResponse::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsSimpleResponse();
  return *internal_default_instance();
}

SimpleResponse* SimpleResponse::New(::google::protobuf::Arena* arena) const {
  SimpleResponse* n = new SimpleResponse;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void SimpleResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.SimpleResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  username_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  oauth_scope_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && payload_ != NULL) {
    delete payload_;
  }
  payload_ = NULL;
  _internal_metadata_.Clear();
}

bool SimpleResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.SimpleResponse)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .grpc.testing.Payload payload = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_payload()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string username = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_username()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->username().data(), static_cast<int>(this->username().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.testing.SimpleResponse.username"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string oauth_scope = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_oauth_scope()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->oauth_scope().data(), static_cast<int>(this->oauth_scope().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.testing.SimpleResponse.oauth_scope"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.SimpleResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.SimpleResponse)
  return false;
#undef DO_
}

void SimpleResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.SimpleResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.Payload payload = 1;
  if (this->has_payload()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->payload_, output);
  }

  // string username = 2;
  if (this->username().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->username().data(), static_cast<int>(this->username().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.SimpleResponse.username");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      2, this->username(), output);
  }

  // string oauth_scope = 3;
  if (this->oauth_scope().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->oauth_scope().data(), static_cast<int>(this->oauth_scope().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.SimpleResponse.oauth_scope");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->oauth_scope(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.SimpleResponse)
}

::google::protobuf::uint8* SimpleResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.SimpleResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.Payload payload = 1;
  if (this->has_payload()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, *this->payload_, deterministic, target);
  }

  // string username = 2;
  if (this->username().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->username().data(), static_cast<int>(this->username().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.SimpleResponse.username");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        2, this->username(), target);
  }

  // string oauth_scope = 3;
  if (this->oauth_scope().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->oauth_scope().data(), static_cast<int>(this->oauth_scope().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.SimpleResponse.oauth_scope");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->oauth_scope(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.SimpleResponse)
  return target;
}

size_t SimpleResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.SimpleResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string username = 2;
  if (this->username().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->username());
  }

  // string oauth_scope = 3;
  if (this->oauth_scope().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->oauth_scope());
  }

  // .grpc.testing.Payload payload = 1;
  if (this->has_payload()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->payload_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SimpleResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.SimpleResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const SimpleResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SimpleResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.SimpleResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.SimpleResponse)
    MergeFrom(*source);
  }
}

void SimpleResponse::MergeFrom(const SimpleResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.SimpleResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.username().size() > 0) {

    username_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.username_);
  }
  if (from.oauth_scope().size() > 0) {

    oauth_scope_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.oauth_scope_);
  }
  if (from.has_payload()) {
    mutable_payload()->::grpc::testing::Payload::MergeFrom(from.payload());
  }
}

void SimpleResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.SimpleResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SimpleResponse::CopyFrom(const SimpleResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.SimpleResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SimpleResponse::IsInitialized() const {
  return true;
}

void SimpleResponse::Swap(SimpleResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void SimpleResponse::InternalSwap(SimpleResponse* other) {
  using std::swap;
  username_.Swap(&other->username_);
  oauth_scope_.Swap(&other->oauth_scope_);
  swap(payload_, other->payload_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata SimpleResponse::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void StreamingInputCallRequest::InitAsDefaultInstance() {
  ::grpc::testing::_StreamingInputCallRequest_default_instance_._instance.get_mutable()->payload_ = const_cast< ::grpc::testing::Payload*>(
      ::grpc::testing::Payload::internal_default_instance());
  ::grpc::testing::_StreamingInputCallRequest_default_instance_._instance.get_mutable()->expect_compressed_ = const_cast< ::grpc::testing::BoolValue*>(
      ::grpc::testing::BoolValue::internal_default_instance());
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int StreamingInputCallRequest::kPayloadFieldNumber;
const int StreamingInputCallRequest::kExpectCompressedFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

StreamingInputCallRequest::StreamingInputCallRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsStreamingInputCallRequest();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.StreamingInputCallRequest)
}
StreamingInputCallRequest::StreamingInputCallRequest(const StreamingInputCallRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_payload()) {
    payload_ = new ::grpc::testing::Payload(*from.payload_);
  } else {
    payload_ = NULL;
  }
  if (from.has_expect_compressed()) {
    expect_compressed_ = new ::grpc::testing::BoolValue(*from.expect_compressed_);
  } else {
    expect_compressed_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:grpc.testing.StreamingInputCallRequest)
}

void StreamingInputCallRequest::SharedCtor() {
  ::memset(&payload_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&expect_compressed_) -
      reinterpret_cast<char*>(&payload_)) + sizeof(expect_compressed_));
  _cached_size_ = 0;
}

StreamingInputCallRequest::~StreamingInputCallRequest() {
  // @@protoc_insertion_point(destructor:grpc.testing.StreamingInputCallRequest)
  SharedDtor();
}

void StreamingInputCallRequest::SharedDtor() {
  if (this != internal_default_instance()) delete payload_;
  if (this != internal_default_instance()) delete expect_compressed_;
}

void StreamingInputCallRequest::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* StreamingInputCallRequest::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const StreamingInputCallRequest& StreamingInputCallRequest::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsStreamingInputCallRequest();
  return *internal_default_instance();
}

StreamingInputCallRequest* StreamingInputCallRequest::New(::google::protobuf::Arena* arena) const {
  StreamingInputCallRequest* n = new StreamingInputCallRequest;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void StreamingInputCallRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.StreamingInputCallRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && payload_ != NULL) {
    delete payload_;
  }
  payload_ = NULL;
  if (GetArenaNoVirtual() == NULL && expect_compressed_ != NULL) {
    delete expect_compressed_;
  }
  expect_compressed_ = NULL;
  _internal_metadata_.Clear();
}

bool StreamingInputCallRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.StreamingInputCallRequest)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .grpc.testing.Payload payload = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_payload()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.testing.BoolValue expect_compressed = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_expect_compressed()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.StreamingInputCallRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.StreamingInputCallRequest)
  return false;
#undef DO_
}

void StreamingInputCallRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.StreamingInputCallRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.Payload payload = 1;
  if (this->has_payload()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->payload_, output);
  }

  // .grpc.testing.BoolValue expect_compressed = 2;
  if (this->has_expect_compressed()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->expect_compressed_, output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.StreamingInputCallRequest)
}

::google::protobuf::uint8* StreamingInputCallRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.StreamingInputCallRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.Payload payload = 1;
  if (this->has_payload()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, *this->payload_, deterministic, target);
  }

  // .grpc.testing.BoolValue expect_compressed = 2;
  if (this->has_expect_compressed()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, *this->expect_compressed_, deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.StreamingInputCallRequest)
  return target;
}

size_t StreamingInputCallRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.StreamingInputCallRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .grpc.testing.Payload payload = 1;
  if (this->has_payload()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->payload_);
  }

  // .grpc.testing.BoolValue expect_compressed = 2;
  if (this->has_expect_compressed()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->expect_compressed_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void StreamingInputCallRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.StreamingInputCallRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const StreamingInputCallRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const StreamingInputCallRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.StreamingInputCallRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.StreamingInputCallRequest)
    MergeFrom(*source);
  }
}

void StreamingInputCallRequest::MergeFrom(const StreamingInputCallRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.StreamingInputCallRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_payload()) {
    mutable_payload()->::grpc::testing::Payload::MergeFrom(from.payload());
  }
  if (from.has_expect_compressed()) {
    mutable_expect_compressed()->::grpc::testing::BoolValue::MergeFrom(from.expect_compressed());
  }
}

void StreamingInputCallRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.StreamingInputCallRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StreamingInputCallRequest::CopyFrom(const StreamingInputCallRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.StreamingInputCallRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StreamingInputCallRequest::IsInitialized() const {
  return true;
}

void StreamingInputCallRequest::Swap(StreamingInputCallRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void StreamingInputCallRequest::InternalSwap(StreamingInputCallRequest* other) {
  using std::swap;
  swap(payload_, other->payload_);
  swap(expect_compressed_, other->expect_compressed_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata StreamingInputCallRequest::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void StreamingInputCallResponse::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int StreamingInputCallResponse::kAggregatedPayloadSizeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

StreamingInputCallResponse::StreamingInputCallResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsStreamingInputCallResponse();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.StreamingInputCallResponse)
}
StreamingInputCallResponse::StreamingInputCallResponse(const StreamingInputCallResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  aggregated_payload_size_ = from.aggregated_payload_size_;
  // @@protoc_insertion_point(copy_constructor:grpc.testing.StreamingInputCallResponse)
}

void StreamingInputCallResponse::SharedCtor() {
  aggregated_payload_size_ = 0;
  _cached_size_ = 0;
}

StreamingInputCallResponse::~StreamingInputCallResponse() {
  // @@protoc_insertion_point(destructor:grpc.testing.StreamingInputCallResponse)
  SharedDtor();
}

void StreamingInputCallResponse::SharedDtor() {
}

void StreamingInputCallResponse::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* StreamingInputCallResponse::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const StreamingInputCallResponse& StreamingInputCallResponse::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsStreamingInputCallResponse();
  return *internal_default_instance();
}

StreamingInputCallResponse* StreamingInputCallResponse::New(::google::protobuf::Arena* arena) const {
  StreamingInputCallResponse* n = new StreamingInputCallResponse;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void StreamingInputCallResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.StreamingInputCallResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  aggregated_payload_size_ = 0;
  _internal_metadata_.Clear();
}

bool StreamingInputCallResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.StreamingInputCallResponse)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 aggregated_payload_size = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &aggregated_payload_size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.StreamingInputCallResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.StreamingInputCallResponse)
  return false;
#undef DO_
}

void StreamingInputCallResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.StreamingInputCallResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 aggregated_payload_size = 1;
  if (this->aggregated_payload_size() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->aggregated_payload_size(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.StreamingInputCallResponse)
}

::google::protobuf::uint8* StreamingInputCallResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.StreamingInputCallResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 aggregated_payload_size = 1;
  if (this->aggregated_payload_size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->aggregated_payload_size(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.StreamingInputCallResponse)
  return target;
}

size_t StreamingInputCallResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.StreamingInputCallResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int32 aggregated_payload_size = 1;
  if (this->aggregated_payload_size() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->aggregated_payload_size());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void StreamingInputCallResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.StreamingInputCallResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const StreamingInputCallResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const StreamingInputCallResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.StreamingInputCallResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.StreamingInputCallResponse)
    MergeFrom(*source);
  }
}

void StreamingInputCallResponse::MergeFrom(const StreamingInputCallResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.StreamingInputCallResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.aggregated_payload_size() != 0) {
    set_aggregated_payload_size(from.aggregated_payload_size());
  }
}

void StreamingInputCallResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.StreamingInputCallResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StreamingInputCallResponse::CopyFrom(const StreamingInputCallResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.StreamingInputCallResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StreamingInputCallResponse::IsInitialized() const {
  return true;
}

void StreamingInputCallResponse::Swap(StreamingInputCallResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void StreamingInputCallResponse::InternalSwap(StreamingInputCallResponse* other) {
  using std::swap;
  swap(aggregated_payload_size_, other->aggregated_payload_size_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata StreamingInputCallResponse::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ResponseParameters::InitAsDefaultInstance() {
  ::grpc::testing::_ResponseParameters_default_instance_._instance.get_mutable()->compressed_ = const_cast< ::grpc::testing::BoolValue*>(
      ::grpc::testing::BoolValue::internal_default_instance());
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ResponseParameters::kSizeFieldNumber;
const int ResponseParameters::kIntervalUsFieldNumber;
const int ResponseParameters::kCompressedFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ResponseParameters::ResponseParameters()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsResponseParameters();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.ResponseParameters)
}
ResponseParameters::ResponseParameters(const ResponseParameters& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_compressed()) {
    compressed_ = new ::grpc::testing::BoolValue(*from.compressed_);
  } else {
    compressed_ = NULL;
  }
  ::memcpy(&size_, &from.size_,
    static_cast<size_t>(reinterpret_cast<char*>(&interval_us_) -
    reinterpret_cast<char*>(&size_)) + sizeof(interval_us_));
  // @@protoc_insertion_point(copy_constructor:grpc.testing.ResponseParameters)
}

void ResponseParameters::SharedCtor() {
  ::memset(&compressed_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&interval_us_) -
      reinterpret_cast<char*>(&compressed_)) + sizeof(interval_us_));
  _cached_size_ = 0;
}

ResponseParameters::~ResponseParameters() {
  // @@protoc_insertion_point(destructor:grpc.testing.ResponseParameters)
  SharedDtor();
}

void ResponseParameters::SharedDtor() {
  if (this != internal_default_instance()) delete compressed_;
}

void ResponseParameters::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ResponseParameters::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ResponseParameters& ResponseParameters::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsResponseParameters();
  return *internal_default_instance();
}

ResponseParameters* ResponseParameters::New(::google::protobuf::Arena* arena) const {
  ResponseParameters* n = new ResponseParameters;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ResponseParameters::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.ResponseParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && compressed_ != NULL) {
    delete compressed_;
  }
  compressed_ = NULL;
  ::memset(&size_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&interval_us_) -
      reinterpret_cast<char*>(&size_)) + sizeof(interval_us_));
  _internal_metadata_.Clear();
}

bool ResponseParameters::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.ResponseParameters)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 size = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 interval_us = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &interval_us_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.testing.BoolValue compressed = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_compressed()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.ResponseParameters)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.ResponseParameters)
  return false;
#undef DO_
}

void ResponseParameters::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.ResponseParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 size = 1;
  if (this->size() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->size(), output);
  }

  // int32 interval_us = 2;
  if (this->interval_us() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->interval_us(), output);
  }

  // .grpc.testing.BoolValue compressed = 3;
  if (this->has_compressed()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, *this->compressed_, output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.ResponseParameters)
}

::google::protobuf::uint8* ResponseParameters::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.ResponseParameters)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 size = 1;
  if (this->size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->size(), target);
  }

  // int32 interval_us = 2;
  if (this->interval_us() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->interval_us(), target);
  }

  // .grpc.testing.BoolValue compressed = 3;
  if (this->has_compressed()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, *this->compressed_, deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.ResponseParameters)
  return target;
}

size_t ResponseParameters::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.ResponseParameters)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .grpc.testing.BoolValue compressed = 3;
  if (this->has_compressed()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->compressed_);
  }

  // int32 size = 1;
  if (this->size() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->size());
  }

  // int32 interval_us = 2;
  if (this->interval_us() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->interval_us());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ResponseParameters::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.ResponseParameters)
  GOOGLE_DCHECK_NE(&from, this);
  const ResponseParameters* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ResponseParameters>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.ResponseParameters)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.ResponseParameters)
    MergeFrom(*source);
  }
}

void ResponseParameters::MergeFrom(const ResponseParameters& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.ResponseParameters)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_compressed()) {
    mutable_compressed()->::grpc::testing::BoolValue::MergeFrom(from.compressed());
  }
  if (from.size() != 0) {
    set_size(from.size());
  }
  if (from.interval_us() != 0) {
    set_interval_us(from.interval_us());
  }
}

void ResponseParameters::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.ResponseParameters)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ResponseParameters::CopyFrom(const ResponseParameters& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.ResponseParameters)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ResponseParameters::IsInitialized() const {
  return true;
}

void ResponseParameters::Swap(ResponseParameters* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ResponseParameters::InternalSwap(ResponseParameters* other) {
  using std::swap;
  swap(compressed_, other->compressed_);
  swap(size_, other->size_);
  swap(interval_us_, other->interval_us_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ResponseParameters::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void StreamingOutputCallRequest::InitAsDefaultInstance() {
  ::grpc::testing::_StreamingOutputCallRequest_default_instance_._instance.get_mutable()->payload_ = const_cast< ::grpc::testing::Payload*>(
      ::grpc::testing::Payload::internal_default_instance());
  ::grpc::testing::_StreamingOutputCallRequest_default_instance_._instance.get_mutable()->response_status_ = const_cast< ::grpc::testing::EchoStatus*>(
      ::grpc::testing::EchoStatus::internal_default_instance());
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int StreamingOutputCallRequest::kResponseTypeFieldNumber;
const int StreamingOutputCallRequest::kResponseParametersFieldNumber;
const int StreamingOutputCallRequest::kPayloadFieldNumber;
const int StreamingOutputCallRequest::kResponseStatusFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

StreamingOutputCallRequest::StreamingOutputCallRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsStreamingOutputCallRequest();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.StreamingOutputCallRequest)
}
StreamingOutputCallRequest::StreamingOutputCallRequest(const StreamingOutputCallRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      response_parameters_(from.response_parameters_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_payload()) {
    payload_ = new ::grpc::testing::Payload(*from.payload_);
  } else {
    payload_ = NULL;
  }
  if (from.has_response_status()) {
    response_status_ = new ::grpc::testing::EchoStatus(*from.response_status_);
  } else {
    response_status_ = NULL;
  }
  response_type_ = from.response_type_;
  // @@protoc_insertion_point(copy_constructor:grpc.testing.StreamingOutputCallRequest)
}

void StreamingOutputCallRequest::SharedCtor() {
  ::memset(&payload_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&response_type_) -
      reinterpret_cast<char*>(&payload_)) + sizeof(response_type_));
  _cached_size_ = 0;
}

StreamingOutputCallRequest::~StreamingOutputCallRequest() {
  // @@protoc_insertion_point(destructor:grpc.testing.StreamingOutputCallRequest)
  SharedDtor();
}

void StreamingOutputCallRequest::SharedDtor() {
  if (this != internal_default_instance()) delete payload_;
  if (this != internal_default_instance()) delete response_status_;
}

void StreamingOutputCallRequest::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* StreamingOutputCallRequest::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const StreamingOutputCallRequest& StreamingOutputCallRequest::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsStreamingOutputCallRequest();
  return *internal_default_instance();
}

StreamingOutputCallRequest* StreamingOutputCallRequest::New(::google::protobuf::Arena* arena) const {
  StreamingOutputCallRequest* n = new StreamingOutputCallRequest;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void StreamingOutputCallRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.StreamingOutputCallRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  response_parameters_.Clear();
  if (GetArenaNoVirtual() == NULL && payload_ != NULL) {
    delete payload_;
  }
  payload_ = NULL;
  if (GetArenaNoVirtual() == NULL && response_status_ != NULL) {
    delete response_status_;
  }
  response_status_ = NULL;
  response_type_ = 0;
  _internal_metadata_.Clear();
}

bool StreamingOutputCallRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.StreamingOutputCallRequest)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .grpc.testing.PayloadType response_type = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          int value;
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   int, ::google::protobuf::internal::WireFormatLite::TYPE_ENUM>(
                 input, &value)));
          set_response_type(static_cast< ::grpc::testing::PayloadType >(value));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .grpc.testing.ResponseParameters response_parameters = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(input, add_response_parameters()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.testing.Payload payload = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_payload()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.testing.EchoStatus response_status = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_response_status()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.StreamingOutputCallRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.StreamingOutputCallRequest)
  return false;
#undef DO_
}

void StreamingOutputCallRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.StreamingOutputCallRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.PayloadType response_type = 1;
  if (this->response_type() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteEnum(
      1, this->response_type(), output);
  }

  // repeated .grpc.testing.ResponseParameters response_parameters = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->response_parameters_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, this->response_parameters(static_cast<int>(i)), output);
  }

  // .grpc.testing.Payload payload = 3;
  if (this->has_payload()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, *this->payload_, output);
  }

  // .grpc.testing.EchoStatus response_status = 7;
  if (this->has_response_status()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7, *this->response_status_, output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.StreamingOutputCallRequest)
}

::google::protobuf::uint8* StreamingOutputCallRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.StreamingOutputCallRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.PayloadType response_type = 1;
  if (this->response_type() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteEnumToArray(
      1, this->response_type(), target);
  }

  // repeated .grpc.testing.ResponseParameters response_parameters = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->response_parameters_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, this->response_parameters(static_cast<int>(i)), deterministic, target);
  }

  // .grpc.testing.Payload payload = 3;
  if (this->has_payload()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, *this->payload_, deterministic, target);
  }

  // .grpc.testing.EchoStatus response_status = 7;
  if (this->has_response_status()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        7, *this->response_status_, deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.StreamingOutputCallRequest)
  return target;
}

size_t StreamingOutputCallRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.StreamingOutputCallRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .grpc.testing.ResponseParameters response_parameters = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->response_parameters_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->response_parameters(static_cast<int>(i)));
    }
  }

  // .grpc.testing.Payload payload = 3;
  if (this->has_payload()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->payload_);
  }

  // .grpc.testing.EchoStatus response_status = 7;
  if (this->has_response_status()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->response_status_);
  }

  // .grpc.testing.PayloadType response_type = 1;
  if (this->response_type() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::EnumSize(this->response_type());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void StreamingOutputCallRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.StreamingOutputCallRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const StreamingOutputCallRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const StreamingOutputCallRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.StreamingOutputCallRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.StreamingOutputCallRequest)
    MergeFrom(*source);
  }
}

void StreamingOutputCallRequest::MergeFrom(const StreamingOutputCallRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.StreamingOutputCallRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  response_parameters_.MergeFrom(from.response_parameters_);
  if (from.has_payload()) {
    mutable_payload()->::grpc::testing::Payload::MergeFrom(from.payload());
  }
  if (from.has_response_status()) {
    mutable_response_status()->::grpc::testing::EchoStatus::MergeFrom(from.response_status());
  }
  if (from.response_type() != 0) {
    set_response_type(from.response_type());
  }
}

void StreamingOutputCallRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.StreamingOutputCallRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StreamingOutputCallRequest::CopyFrom(const StreamingOutputCallRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.StreamingOutputCallRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StreamingOutputCallRequest::IsInitialized() const {
  return true;
}

void StreamingOutputCallRequest::Swap(StreamingOutputCallRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void StreamingOutputCallRequest::InternalSwap(StreamingOutputCallRequest* other) {
  using std::swap;
  response_parameters_.InternalSwap(&other->response_parameters_);
  swap(payload_, other->payload_);
  swap(response_status_, other->response_status_);
  swap(response_type_, other->response_type_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata StreamingOutputCallRequest::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void StreamingOutputCallResponse::InitAsDefaultInstance() {
  ::grpc::testing::_StreamingOutputCallResponse_default_instance_._instance.get_mutable()->payload_ = const_cast< ::grpc::testing::Payload*>(
      ::grpc::testing::Payload::internal_default_instance());
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int StreamingOutputCallResponse::kPayloadFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

StreamingOutputCallResponse::StreamingOutputCallResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsStreamingOutputCallResponse();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.StreamingOutputCallResponse)
}
StreamingOutputCallResponse::StreamingOutputCallResponse(const StreamingOutputCallResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_payload()) {
    payload_ = new ::grpc::testing::Payload(*from.payload_);
  } else {
    payload_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:grpc.testing.StreamingOutputCallResponse)
}

void StreamingOutputCallResponse::SharedCtor() {
  payload_ = NULL;
  _cached_size_ = 0;
}

StreamingOutputCallResponse::~StreamingOutputCallResponse() {
  // @@protoc_insertion_point(destructor:grpc.testing.StreamingOutputCallResponse)
  SharedDtor();
}

void StreamingOutputCallResponse::SharedDtor() {
  if (this != internal_default_instance()) delete payload_;
}

void StreamingOutputCallResponse::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* StreamingOutputCallResponse::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const StreamingOutputCallResponse& StreamingOutputCallResponse::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsStreamingOutputCallResponse();
  return *internal_default_instance();
}

StreamingOutputCallResponse* StreamingOutputCallResponse::New(::google::protobuf::Arena* arena) const {
  StreamingOutputCallResponse* n = new StreamingOutputCallResponse;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void StreamingOutputCallResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.StreamingOutputCallResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && payload_ != NULL) {
    delete payload_;
  }
  payload_ = NULL;
  _internal_metadata_.Clear();
}

bool StreamingOutputCallResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.StreamingOutputCallResponse)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .grpc.testing.Payload payload = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_payload()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.StreamingOutputCallResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.StreamingOutputCallResponse)
  return false;
#undef DO_
}

void StreamingOutputCallResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.StreamingOutputCallResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.Payload payload = 1;
  if (this->has_payload()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->payload_, output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.StreamingOutputCallResponse)
}

::google::protobuf::uint8* StreamingOutputCallResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.StreamingOutputCallResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.Payload payload = 1;
  if (this->has_payload()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, *this->payload_, deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.StreamingOutputCallResponse)
  return target;
}

size_t StreamingOutputCallResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.StreamingOutputCallResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .grpc.testing.Payload payload = 1;
  if (this->has_payload()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->payload_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void StreamingOutputCallResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.StreamingOutputCallResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const StreamingOutputCallResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const StreamingOutputCallResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.StreamingOutputCallResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.StreamingOutputCallResponse)
    MergeFrom(*source);
  }
}

void StreamingOutputCallResponse::MergeFrom(const StreamingOutputCallResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.StreamingOutputCallResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_payload()) {
    mutable_payload()->::grpc::testing::Payload::MergeFrom(from.payload());
  }
}

void StreamingOutputCallResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.StreamingOutputCallResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void StreamingOutputCallResponse::CopyFrom(const StreamingOutputCallResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.StreamingOutputCallResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StreamingOutputCallResponse::IsInitialized() const {
  return true;
}

void StreamingOutputCallResponse::Swap(StreamingOutputCallResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void StreamingOutputCallResponse::InternalSwap(StreamingOutputCallResponse* other) {
  using std::swap;
  swap(payload_, other->payload_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata StreamingOutputCallResponse::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ReconnectParams::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ReconnectParams::kMaxReconnectBackoffMsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ReconnectParams::ReconnectParams()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsReconnectParams();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.ReconnectParams)
}
ReconnectParams::ReconnectParams(const ReconnectParams& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  max_reconnect_backoff_ms_ = from.max_reconnect_backoff_ms_;
  // @@protoc_insertion_point(copy_constructor:grpc.testing.ReconnectParams)
}

void ReconnectParams::SharedCtor() {
  max_reconnect_backoff_ms_ = 0;
  _cached_size_ = 0;
}

ReconnectParams::~ReconnectParams() {
  // @@protoc_insertion_point(destructor:grpc.testing.ReconnectParams)
  SharedDtor();
}

void ReconnectParams::SharedDtor() {
}

void ReconnectParams::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ReconnectParams::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ReconnectParams& ReconnectParams::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsReconnectParams();
  return *internal_default_instance();
}

ReconnectParams* ReconnectParams::New(::google::protobuf::Arena* arena) const {
  ReconnectParams* n = new ReconnectParams;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ReconnectParams::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.ReconnectParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  max_reconnect_backoff_ms_ = 0;
  _internal_metadata_.Clear();
}

bool ReconnectParams::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.ReconnectParams)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 max_reconnect_backoff_ms = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &max_reconnect_backoff_ms_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.ReconnectParams)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.ReconnectParams)
  return false;
#undef DO_
}

void ReconnectParams::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.ReconnectParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 max_reconnect_backoff_ms = 1;
  if (this->max_reconnect_backoff_ms() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->max_reconnect_backoff_ms(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.ReconnectParams)
}

::google::protobuf::uint8* ReconnectParams::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.ReconnectParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 max_reconnect_backoff_ms = 1;
  if (this->max_reconnect_backoff_ms() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->max_reconnect_backoff_ms(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.ReconnectParams)
  return target;
}

size_t ReconnectParams::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.ReconnectParams)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int32 max_reconnect_backoff_ms = 1;
  if (this->max_reconnect_backoff_ms() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->max_reconnect_backoff_ms());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ReconnectParams::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.ReconnectParams)
  GOOGLE_DCHECK_NE(&from, this);
  const ReconnectParams* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ReconnectParams>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.ReconnectParams)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.ReconnectParams)
    MergeFrom(*source);
  }
}

void ReconnectParams::MergeFrom(const ReconnectParams& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.ReconnectParams)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.max_reconnect_backoff_ms() != 0) {
    set_max_reconnect_backoff_ms(from.max_reconnect_backoff_ms());
  }
}

void ReconnectParams::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.ReconnectParams)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ReconnectParams::CopyFrom(const ReconnectParams& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.ReconnectParams)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ReconnectParams::IsInitialized() const {
  return true;
}

void ReconnectParams::Swap(ReconnectParams* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ReconnectParams::InternalSwap(ReconnectParams* other) {
  using std::swap;
  swap(max_reconnect_backoff_ms_, other->max_reconnect_backoff_ms_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ReconnectParams::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ReconnectInfo::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ReconnectInfo::kPassedFieldNumber;
const int ReconnectInfo::kBackoffMsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ReconnectInfo::ReconnectInfo()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsReconnectInfo();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.ReconnectInfo)
}
ReconnectInfo::ReconnectInfo(const ReconnectInfo& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      backoff_ms_(from.backoff_ms_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  passed_ = from.passed_;
  // @@protoc_insertion_point(copy_constructor:grpc.testing.ReconnectInfo)
}

void ReconnectInfo::SharedCtor() {
  passed_ = false;
  _cached_size_ = 0;
}

ReconnectInfo::~ReconnectInfo() {
  // @@protoc_insertion_point(destructor:grpc.testing.ReconnectInfo)
  SharedDtor();
}

void ReconnectInfo::SharedDtor() {
}

void ReconnectInfo::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ReconnectInfo::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ReconnectInfo& ReconnectInfo::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsReconnectInfo();
  return *internal_default_instance();
}

ReconnectInfo* ReconnectInfo::New(::google::protobuf::Arena* arena) const {
  ReconnectInfo* n = new ReconnectInfo;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ReconnectInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.ReconnectInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  backoff_ms_.Clear();
  passed_ = false;
  _internal_metadata_.Clear();
}

bool ReconnectInfo::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.ReconnectInfo)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // bool passed = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &passed_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated int32 backoff_ms = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, this->mutable_backoff_ms())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 1, 18u, input, this->mutable_backoff_ms())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.ReconnectInfo)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.ReconnectInfo)
  return false;
#undef DO_
}

void ReconnectInfo::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.ReconnectInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bool passed = 1;
  if (this->passed() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(1, this->passed(), output);
  }

  // repeated int32 backoff_ms = 2;
  if (this->backoff_ms_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(2, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _backoff_ms_cached_byte_size_));
  }
  for (int i = 0, n = this->backoff_ms_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32NoTag(
      this->backoff_ms(i), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.ReconnectInfo)
}

::google::protobuf::uint8* ReconnectInfo::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.ReconnectInfo)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bool passed = 1;
  if (this->passed() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(1, this->passed(), target);
  }

  // repeated int32 backoff_ms = 2;
  if (this->backoff_ms_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      2,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _backoff_ms_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteInt32NoTagToArray(this->backoff_ms_, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.ReconnectInfo)
  return target;
}

size_t ReconnectInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.ReconnectInfo)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated int32 backoff_ms = 2;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      Int32Size(this->backoff_ms_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _backoff_ms_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // bool passed = 1;
  if (this->passed() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ReconnectInfo::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.ReconnectInfo)
  GOOGLE_DCHECK_NE(&from, this);
  const ReconnectInfo* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ReconnectInfo>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.ReconnectInfo)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.ReconnectInfo)
    MergeFrom(*source);
  }
}

void ReconnectInfo::MergeFrom(const ReconnectInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.ReconnectInfo)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  backoff_ms_.MergeFrom(from.backoff_ms_);
  if (from.passed() != 0) {
    set_passed(from.passed());
  }
}

void ReconnectInfo::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.ReconnectInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ReconnectInfo::CopyFrom(const ReconnectInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.ReconnectInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ReconnectInfo::IsInitialized() const {
  return true;
}

void ReconnectInfo::Swap(ReconnectInfo* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ReconnectInfo::InternalSwap(ReconnectInfo* other) {
  using std::swap;
  backoff_ms_.InternalSwap(&other->backoff_ms_);
  swap(passed_, other->passed_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ReconnectInfo::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace testing
}  // namespace grpc

// @@protoc_insertion_point(global_scope)
