// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: src/proto/grpc/testing/messages.proto

#ifndef PROTOBUF_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto__INCLUDED
#define PROTOBUF_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3005001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[12];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
void InitDefaultsBoolValueImpl();
void InitDefaultsBoolValue();
void InitDefaultsPayloadImpl();
void InitDefaultsPayload();
void InitDefaultsEchoStatusImpl();
void InitDefaultsEchoStatus();
void InitDefaultsSimpleRequestImpl();
void InitDefaultsSimpleRequest();
void InitDefaultsSimpleResponseImpl();
void InitDefaultsSimpleResponse();
void InitDefaultsStreamingInputCallRequestImpl();
void InitDefaultsStreamingInputCallRequest();
void InitDefaultsStreamingInputCallResponseImpl();
void InitDefaultsStreamingInputCallResponse();
void InitDefaultsResponseParametersImpl();
void InitDefaultsResponseParameters();
void InitDefaultsStreamingOutputCallRequestImpl();
void InitDefaultsStreamingOutputCallRequest();
void InitDefaultsStreamingOutputCallResponseImpl();
void InitDefaultsStreamingOutputCallResponse();
void InitDefaultsReconnectParamsImpl();
void InitDefaultsReconnectParams();
void InitDefaultsReconnectInfoImpl();
void InitDefaultsReconnectInfo();
inline void InitDefaults() {
  InitDefaultsBoolValue();
  InitDefaultsPayload();
  InitDefaultsEchoStatus();
  InitDefaultsSimpleRequest();
  InitDefaultsSimpleResponse();
  InitDefaultsStreamingInputCallRequest();
  InitDefaultsStreamingInputCallResponse();
  InitDefaultsResponseParameters();
  InitDefaultsStreamingOutputCallRequest();
  InitDefaultsStreamingOutputCallResponse();
  InitDefaultsReconnectParams();
  InitDefaultsReconnectInfo();
}
}  // namespace protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto
namespace grpc {
namespace testing {
class BoolValue;
class BoolValueDefaultTypeInternal;
extern BoolValueDefaultTypeInternal _BoolValue_default_instance_;
class EchoStatus;
class EchoStatusDefaultTypeInternal;
extern EchoStatusDefaultTypeInternal _EchoStatus_default_instance_;
class Payload;
class PayloadDefaultTypeInternal;
extern PayloadDefaultTypeInternal _Payload_default_instance_;
class ReconnectInfo;
class ReconnectInfoDefaultTypeInternal;
extern ReconnectInfoDefaultTypeInternal _ReconnectInfo_default_instance_;
class ReconnectParams;
class ReconnectParamsDefaultTypeInternal;
extern ReconnectParamsDefaultTypeInternal _ReconnectParams_default_instance_;
class ResponseParameters;
class ResponseParametersDefaultTypeInternal;
extern ResponseParametersDefaultTypeInternal _ResponseParameters_default_instance_;
class SimpleRequest;
class SimpleRequestDefaultTypeInternal;
extern SimpleRequestDefaultTypeInternal _SimpleRequest_default_instance_;
class SimpleResponse;
class SimpleResponseDefaultTypeInternal;
extern SimpleResponseDefaultTypeInternal _SimpleResponse_default_instance_;
class StreamingInputCallRequest;
class StreamingInputCallRequestDefaultTypeInternal;
extern StreamingInputCallRequestDefaultTypeInternal _StreamingInputCallRequest_default_instance_;
class StreamingInputCallResponse;
class StreamingInputCallResponseDefaultTypeInternal;
extern StreamingInputCallResponseDefaultTypeInternal _StreamingInputCallResponse_default_instance_;
class StreamingOutputCallRequest;
class StreamingOutputCallRequestDefaultTypeInternal;
extern StreamingOutputCallRequestDefaultTypeInternal _StreamingOutputCallRequest_default_instance_;
class StreamingOutputCallResponse;
class StreamingOutputCallResponseDefaultTypeInternal;
extern StreamingOutputCallResponseDefaultTypeInternal _StreamingOutputCallResponse_default_instance_;
}  // namespace testing
}  // namespace grpc
namespace grpc {
namespace testing {

enum PayloadType {
  COMPRESSABLE = 0,
  PayloadType_INT_MIN_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32min,
  PayloadType_INT_MAX_SENTINEL_DO_NOT_USE_ = ::google::protobuf::kint32max
};
bool PayloadType_IsValid(int value);
const PayloadType PayloadType_MIN = COMPRESSABLE;
const PayloadType PayloadType_MAX = COMPRESSABLE;
const int PayloadType_ARRAYSIZE = PayloadType_MAX + 1;

const ::google::protobuf::EnumDescriptor* PayloadType_descriptor();
inline const ::std::string& PayloadType_Name(PayloadType value) {
  return ::google::protobuf::internal::NameOfEnum(
    PayloadType_descriptor(), value);
}
inline bool PayloadType_Parse(
    const ::std::string& name, PayloadType* value) {
  return ::google::protobuf::internal::ParseNamedEnum<PayloadType>(
    PayloadType_descriptor(), name, value);
}
// ===================================================================

class BoolValue : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.BoolValue) */ {
 public:
  BoolValue();
  virtual ~BoolValue();

  BoolValue(const BoolValue& from);

  inline BoolValue& operator=(const BoolValue& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  BoolValue(BoolValue&& from) noexcept
    : BoolValue() {
    *this = ::std::move(from);
  }

  inline BoolValue& operator=(BoolValue&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const BoolValue& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const BoolValue* internal_default_instance() {
    return reinterpret_cast<const BoolValue*>(
               &_BoolValue_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    0;

  void Swap(BoolValue* other);
  friend void swap(BoolValue& a, BoolValue& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline BoolValue* New() const PROTOBUF_FINAL { return New(NULL); }

  BoolValue* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const BoolValue& from);
  void MergeFrom(const BoolValue& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(BoolValue* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // bool value = 1;
  void clear_value();
  static const int kValueFieldNumber = 1;
  bool value() const;
  void set_value(bool value);

  // @@protoc_insertion_point(class_scope:grpc.testing.BoolValue)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  bool value_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsBoolValueImpl();
};
// -------------------------------------------------------------------

class Payload : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.Payload) */ {
 public:
  Payload();
  virtual ~Payload();

  Payload(const Payload& from);

  inline Payload& operator=(const Payload& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Payload(Payload&& from) noexcept
    : Payload() {
    *this = ::std::move(from);
  }

  inline Payload& operator=(Payload&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Payload& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Payload* internal_default_instance() {
    return reinterpret_cast<const Payload*>(
               &_Payload_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    1;

  void Swap(Payload* other);
  friend void swap(Payload& a, Payload& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Payload* New() const PROTOBUF_FINAL { return New(NULL); }

  Payload* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const Payload& from);
  void MergeFrom(const Payload& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(Payload* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // bytes body = 2;
  void clear_body();
  static const int kBodyFieldNumber = 2;
  const ::std::string& body() const;
  void set_body(const ::std::string& value);
  #if LANG_CXX11
  void set_body(::std::string&& value);
  #endif
  void set_body(const char* value);
  void set_body(const void* value, size_t size);
  ::std::string* mutable_body();
  ::std::string* release_body();
  void set_allocated_body(::std::string* body);

  // .grpc.testing.PayloadType type = 1;
  void clear_type();
  static const int kTypeFieldNumber = 1;
  ::grpc::testing::PayloadType type() const;
  void set_type(::grpc::testing::PayloadType value);

  // @@protoc_insertion_point(class_scope:grpc.testing.Payload)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr body_;
  int type_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsPayloadImpl();
};
// -------------------------------------------------------------------

class EchoStatus : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.EchoStatus) */ {
 public:
  EchoStatus();
  virtual ~EchoStatus();

  EchoStatus(const EchoStatus& from);

  inline EchoStatus& operator=(const EchoStatus& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  EchoStatus(EchoStatus&& from) noexcept
    : EchoStatus() {
    *this = ::std::move(from);
  }

  inline EchoStatus& operator=(EchoStatus&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const EchoStatus& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const EchoStatus* internal_default_instance() {
    return reinterpret_cast<const EchoStatus*>(
               &_EchoStatus_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    2;

  void Swap(EchoStatus* other);
  friend void swap(EchoStatus& a, EchoStatus& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline EchoStatus* New() const PROTOBUF_FINAL { return New(NULL); }

  EchoStatus* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const EchoStatus& from);
  void MergeFrom(const EchoStatus& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(EchoStatus* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string message = 2;
  void clear_message();
  static const int kMessageFieldNumber = 2;
  const ::std::string& message() const;
  void set_message(const ::std::string& value);
  #if LANG_CXX11
  void set_message(::std::string&& value);
  #endif
  void set_message(const char* value);
  void set_message(const char* value, size_t size);
  ::std::string* mutable_message();
  ::std::string* release_message();
  void set_allocated_message(::std::string* message);

  // int32 code = 1;
  void clear_code();
  static const int kCodeFieldNumber = 1;
  ::google::protobuf::int32 code() const;
  void set_code(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:grpc.testing.EchoStatus)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr message_;
  ::google::protobuf::int32 code_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsEchoStatusImpl();
};
// -------------------------------------------------------------------

class SimpleRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.SimpleRequest) */ {
 public:
  SimpleRequest();
  virtual ~SimpleRequest();

  SimpleRequest(const SimpleRequest& from);

  inline SimpleRequest& operator=(const SimpleRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SimpleRequest(SimpleRequest&& from) noexcept
    : SimpleRequest() {
    *this = ::std::move(from);
  }

  inline SimpleRequest& operator=(SimpleRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const SimpleRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SimpleRequest* internal_default_instance() {
    return reinterpret_cast<const SimpleRequest*>(
               &_SimpleRequest_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    3;

  void Swap(SimpleRequest* other);
  friend void swap(SimpleRequest& a, SimpleRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SimpleRequest* New() const PROTOBUF_FINAL { return New(NULL); }

  SimpleRequest* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const SimpleRequest& from);
  void MergeFrom(const SimpleRequest& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(SimpleRequest* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .grpc.testing.Payload payload = 3;
  bool has_payload() const;
  void clear_payload();
  static const int kPayloadFieldNumber = 3;
  const ::grpc::testing::Payload& payload() const;
  ::grpc::testing::Payload* release_payload();
  ::grpc::testing::Payload* mutable_payload();
  void set_allocated_payload(::grpc::testing::Payload* payload);

  // .grpc.testing.BoolValue response_compressed = 6;
  bool has_response_compressed() const;
  void clear_response_compressed();
  static const int kResponseCompressedFieldNumber = 6;
  const ::grpc::testing::BoolValue& response_compressed() const;
  ::grpc::testing::BoolValue* release_response_compressed();
  ::grpc::testing::BoolValue* mutable_response_compressed();
  void set_allocated_response_compressed(::grpc::testing::BoolValue* response_compressed);

  // .grpc.testing.EchoStatus response_status = 7;
  bool has_response_status() const;
  void clear_response_status();
  static const int kResponseStatusFieldNumber = 7;
  const ::grpc::testing::EchoStatus& response_status() const;
  ::grpc::testing::EchoStatus* release_response_status();
  ::grpc::testing::EchoStatus* mutable_response_status();
  void set_allocated_response_status(::grpc::testing::EchoStatus* response_status);

  // .grpc.testing.BoolValue expect_compressed = 8;
  bool has_expect_compressed() const;
  void clear_expect_compressed();
  static const int kExpectCompressedFieldNumber = 8;
  const ::grpc::testing::BoolValue& expect_compressed() const;
  ::grpc::testing::BoolValue* release_expect_compressed();
  ::grpc::testing::BoolValue* mutable_expect_compressed();
  void set_allocated_expect_compressed(::grpc::testing::BoolValue* expect_compressed);

  // .grpc.testing.PayloadType response_type = 1;
  void clear_response_type();
  static const int kResponseTypeFieldNumber = 1;
  ::grpc::testing::PayloadType response_type() const;
  void set_response_type(::grpc::testing::PayloadType value);

  // int32 response_size = 2;
  void clear_response_size();
  static const int kResponseSizeFieldNumber = 2;
  ::google::protobuf::int32 response_size() const;
  void set_response_size(::google::protobuf::int32 value);

  // bool fill_username = 4;
  void clear_fill_username();
  static const int kFillUsernameFieldNumber = 4;
  bool fill_username() const;
  void set_fill_username(bool value);

  // bool fill_oauth_scope = 5;
  void clear_fill_oauth_scope();
  static const int kFillOauthScopeFieldNumber = 5;
  bool fill_oauth_scope() const;
  void set_fill_oauth_scope(bool value);

  // @@protoc_insertion_point(class_scope:grpc.testing.SimpleRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::grpc::testing::Payload* payload_;
  ::grpc::testing::BoolValue* response_compressed_;
  ::grpc::testing::EchoStatus* response_status_;
  ::grpc::testing::BoolValue* expect_compressed_;
  int response_type_;
  ::google::protobuf::int32 response_size_;
  bool fill_username_;
  bool fill_oauth_scope_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsSimpleRequestImpl();
};
// -------------------------------------------------------------------

class SimpleResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.SimpleResponse) */ {
 public:
  SimpleResponse();
  virtual ~SimpleResponse();

  SimpleResponse(const SimpleResponse& from);

  inline SimpleResponse& operator=(const SimpleResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SimpleResponse(SimpleResponse&& from) noexcept
    : SimpleResponse() {
    *this = ::std::move(from);
  }

  inline SimpleResponse& operator=(SimpleResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const SimpleResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SimpleResponse* internal_default_instance() {
    return reinterpret_cast<const SimpleResponse*>(
               &_SimpleResponse_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    4;

  void Swap(SimpleResponse* other);
  friend void swap(SimpleResponse& a, SimpleResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SimpleResponse* New() const PROTOBUF_FINAL { return New(NULL); }

  SimpleResponse* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const SimpleResponse& from);
  void MergeFrom(const SimpleResponse& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(SimpleResponse* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string username = 2;
  void clear_username();
  static const int kUsernameFieldNumber = 2;
  const ::std::string& username() const;
  void set_username(const ::std::string& value);
  #if LANG_CXX11
  void set_username(::std::string&& value);
  #endif
  void set_username(const char* value);
  void set_username(const char* value, size_t size);
  ::std::string* mutable_username();
  ::std::string* release_username();
  void set_allocated_username(::std::string* username);

  // string oauth_scope = 3;
  void clear_oauth_scope();
  static const int kOauthScopeFieldNumber = 3;
  const ::std::string& oauth_scope() const;
  void set_oauth_scope(const ::std::string& value);
  #if LANG_CXX11
  void set_oauth_scope(::std::string&& value);
  #endif
  void set_oauth_scope(const char* value);
  void set_oauth_scope(const char* value, size_t size);
  ::std::string* mutable_oauth_scope();
  ::std::string* release_oauth_scope();
  void set_allocated_oauth_scope(::std::string* oauth_scope);

  // .grpc.testing.Payload payload = 1;
  bool has_payload() const;
  void clear_payload();
  static const int kPayloadFieldNumber = 1;
  const ::grpc::testing::Payload& payload() const;
  ::grpc::testing::Payload* release_payload();
  ::grpc::testing::Payload* mutable_payload();
  void set_allocated_payload(::grpc::testing::Payload* payload);

  // @@protoc_insertion_point(class_scope:grpc.testing.SimpleResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr username_;
  ::google::protobuf::internal::ArenaStringPtr oauth_scope_;
  ::grpc::testing::Payload* payload_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsSimpleResponseImpl();
};
// -------------------------------------------------------------------

class StreamingInputCallRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.StreamingInputCallRequest) */ {
 public:
  StreamingInputCallRequest();
  virtual ~StreamingInputCallRequest();

  StreamingInputCallRequest(const StreamingInputCallRequest& from);

  inline StreamingInputCallRequest& operator=(const StreamingInputCallRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  StreamingInputCallRequest(StreamingInputCallRequest&& from) noexcept
    : StreamingInputCallRequest() {
    *this = ::std::move(from);
  }

  inline StreamingInputCallRequest& operator=(StreamingInputCallRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const StreamingInputCallRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const StreamingInputCallRequest* internal_default_instance() {
    return reinterpret_cast<const StreamingInputCallRequest*>(
               &_StreamingInputCallRequest_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    5;

  void Swap(StreamingInputCallRequest* other);
  friend void swap(StreamingInputCallRequest& a, StreamingInputCallRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline StreamingInputCallRequest* New() const PROTOBUF_FINAL { return New(NULL); }

  StreamingInputCallRequest* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const StreamingInputCallRequest& from);
  void MergeFrom(const StreamingInputCallRequest& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(StreamingInputCallRequest* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .grpc.testing.Payload payload = 1;
  bool has_payload() const;
  void clear_payload();
  static const int kPayloadFieldNumber = 1;
  const ::grpc::testing::Payload& payload() const;
  ::grpc::testing::Payload* release_payload();
  ::grpc::testing::Payload* mutable_payload();
  void set_allocated_payload(::grpc::testing::Payload* payload);

  // .grpc.testing.BoolValue expect_compressed = 2;
  bool has_expect_compressed() const;
  void clear_expect_compressed();
  static const int kExpectCompressedFieldNumber = 2;
  const ::grpc::testing::BoolValue& expect_compressed() const;
  ::grpc::testing::BoolValue* release_expect_compressed();
  ::grpc::testing::BoolValue* mutable_expect_compressed();
  void set_allocated_expect_compressed(::grpc::testing::BoolValue* expect_compressed);

  // @@protoc_insertion_point(class_scope:grpc.testing.StreamingInputCallRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::grpc::testing::Payload* payload_;
  ::grpc::testing::BoolValue* expect_compressed_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsStreamingInputCallRequestImpl();
};
// -------------------------------------------------------------------

class StreamingInputCallResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.StreamingInputCallResponse) */ {
 public:
  StreamingInputCallResponse();
  virtual ~StreamingInputCallResponse();

  StreamingInputCallResponse(const StreamingInputCallResponse& from);

  inline StreamingInputCallResponse& operator=(const StreamingInputCallResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  StreamingInputCallResponse(StreamingInputCallResponse&& from) noexcept
    : StreamingInputCallResponse() {
    *this = ::std::move(from);
  }

  inline StreamingInputCallResponse& operator=(StreamingInputCallResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const StreamingInputCallResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const StreamingInputCallResponse* internal_default_instance() {
    return reinterpret_cast<const StreamingInputCallResponse*>(
               &_StreamingInputCallResponse_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    6;

  void Swap(StreamingInputCallResponse* other);
  friend void swap(StreamingInputCallResponse& a, StreamingInputCallResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline StreamingInputCallResponse* New() const PROTOBUF_FINAL { return New(NULL); }

  StreamingInputCallResponse* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const StreamingInputCallResponse& from);
  void MergeFrom(const StreamingInputCallResponse& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(StreamingInputCallResponse* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int32 aggregated_payload_size = 1;
  void clear_aggregated_payload_size();
  static const int kAggregatedPayloadSizeFieldNumber = 1;
  ::google::protobuf::int32 aggregated_payload_size() const;
  void set_aggregated_payload_size(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:grpc.testing.StreamingInputCallResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int32 aggregated_payload_size_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsStreamingInputCallResponseImpl();
};
// -------------------------------------------------------------------

class ResponseParameters : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.ResponseParameters) */ {
 public:
  ResponseParameters();
  virtual ~ResponseParameters();

  ResponseParameters(const ResponseParameters& from);

  inline ResponseParameters& operator=(const ResponseParameters& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ResponseParameters(ResponseParameters&& from) noexcept
    : ResponseParameters() {
    *this = ::std::move(from);
  }

  inline ResponseParameters& operator=(ResponseParameters&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ResponseParameters& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ResponseParameters* internal_default_instance() {
    return reinterpret_cast<const ResponseParameters*>(
               &_ResponseParameters_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    7;

  void Swap(ResponseParameters* other);
  friend void swap(ResponseParameters& a, ResponseParameters& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ResponseParameters* New() const PROTOBUF_FINAL { return New(NULL); }

  ResponseParameters* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ResponseParameters& from);
  void MergeFrom(const ResponseParameters& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ResponseParameters* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .grpc.testing.BoolValue compressed = 3;
  bool has_compressed() const;
  void clear_compressed();
  static const int kCompressedFieldNumber = 3;
  const ::grpc::testing::BoolValue& compressed() const;
  ::grpc::testing::BoolValue* release_compressed();
  ::grpc::testing::BoolValue* mutable_compressed();
  void set_allocated_compressed(::grpc::testing::BoolValue* compressed);

  // int32 size = 1;
  void clear_size();
  static const int kSizeFieldNumber = 1;
  ::google::protobuf::int32 size() const;
  void set_size(::google::protobuf::int32 value);

  // int32 interval_us = 2;
  void clear_interval_us();
  static const int kIntervalUsFieldNumber = 2;
  ::google::protobuf::int32 interval_us() const;
  void set_interval_us(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:grpc.testing.ResponseParameters)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::grpc::testing::BoolValue* compressed_;
  ::google::protobuf::int32 size_;
  ::google::protobuf::int32 interval_us_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsResponseParametersImpl();
};
// -------------------------------------------------------------------

class StreamingOutputCallRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.StreamingOutputCallRequest) */ {
 public:
  StreamingOutputCallRequest();
  virtual ~StreamingOutputCallRequest();

  StreamingOutputCallRequest(const StreamingOutputCallRequest& from);

  inline StreamingOutputCallRequest& operator=(const StreamingOutputCallRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  StreamingOutputCallRequest(StreamingOutputCallRequest&& from) noexcept
    : StreamingOutputCallRequest() {
    *this = ::std::move(from);
  }

  inline StreamingOutputCallRequest& operator=(StreamingOutputCallRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const StreamingOutputCallRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const StreamingOutputCallRequest* internal_default_instance() {
    return reinterpret_cast<const StreamingOutputCallRequest*>(
               &_StreamingOutputCallRequest_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    8;

  void Swap(StreamingOutputCallRequest* other);
  friend void swap(StreamingOutputCallRequest& a, StreamingOutputCallRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline StreamingOutputCallRequest* New() const PROTOBUF_FINAL { return New(NULL); }

  StreamingOutputCallRequest* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const StreamingOutputCallRequest& from);
  void MergeFrom(const StreamingOutputCallRequest& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(StreamingOutputCallRequest* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .grpc.testing.ResponseParameters response_parameters = 2;
  int response_parameters_size() const;
  void clear_response_parameters();
  static const int kResponseParametersFieldNumber = 2;
  const ::grpc::testing::ResponseParameters& response_parameters(int index) const;
  ::grpc::testing::ResponseParameters* mutable_response_parameters(int index);
  ::grpc::testing::ResponseParameters* add_response_parameters();
  ::google::protobuf::RepeatedPtrField< ::grpc::testing::ResponseParameters >*
      mutable_response_parameters();
  const ::google::protobuf::RepeatedPtrField< ::grpc::testing::ResponseParameters >&
      response_parameters() const;

  // .grpc.testing.Payload payload = 3;
  bool has_payload() const;
  void clear_payload();
  static const int kPayloadFieldNumber = 3;
  const ::grpc::testing::Payload& payload() const;
  ::grpc::testing::Payload* release_payload();
  ::grpc::testing::Payload* mutable_payload();
  void set_allocated_payload(::grpc::testing::Payload* payload);

  // .grpc.testing.EchoStatus response_status = 7;
  bool has_response_status() const;
  void clear_response_status();
  static const int kResponseStatusFieldNumber = 7;
  const ::grpc::testing::EchoStatus& response_status() const;
  ::grpc::testing::EchoStatus* release_response_status();
  ::grpc::testing::EchoStatus* mutable_response_status();
  void set_allocated_response_status(::grpc::testing::EchoStatus* response_status);

  // .grpc.testing.PayloadType response_type = 1;
  void clear_response_type();
  static const int kResponseTypeFieldNumber = 1;
  ::grpc::testing::PayloadType response_type() const;
  void set_response_type(::grpc::testing::PayloadType value);

  // @@protoc_insertion_point(class_scope:grpc.testing.StreamingOutputCallRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::grpc::testing::ResponseParameters > response_parameters_;
  ::grpc::testing::Payload* payload_;
  ::grpc::testing::EchoStatus* response_status_;
  int response_type_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsStreamingOutputCallRequestImpl();
};
// -------------------------------------------------------------------

class StreamingOutputCallResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.StreamingOutputCallResponse) */ {
 public:
  StreamingOutputCallResponse();
  virtual ~StreamingOutputCallResponse();

  StreamingOutputCallResponse(const StreamingOutputCallResponse& from);

  inline StreamingOutputCallResponse& operator=(const StreamingOutputCallResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  StreamingOutputCallResponse(StreamingOutputCallResponse&& from) noexcept
    : StreamingOutputCallResponse() {
    *this = ::std::move(from);
  }

  inline StreamingOutputCallResponse& operator=(StreamingOutputCallResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const StreamingOutputCallResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const StreamingOutputCallResponse* internal_default_instance() {
    return reinterpret_cast<const StreamingOutputCallResponse*>(
               &_StreamingOutputCallResponse_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    9;

  void Swap(StreamingOutputCallResponse* other);
  friend void swap(StreamingOutputCallResponse& a, StreamingOutputCallResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline StreamingOutputCallResponse* New() const PROTOBUF_FINAL { return New(NULL); }

  StreamingOutputCallResponse* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const StreamingOutputCallResponse& from);
  void MergeFrom(const StreamingOutputCallResponse& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(StreamingOutputCallResponse* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .grpc.testing.Payload payload = 1;
  bool has_payload() const;
  void clear_payload();
  static const int kPayloadFieldNumber = 1;
  const ::grpc::testing::Payload& payload() const;
  ::grpc::testing::Payload* release_payload();
  ::grpc::testing::Payload* mutable_payload();
  void set_allocated_payload(::grpc::testing::Payload* payload);

  // @@protoc_insertion_point(class_scope:grpc.testing.StreamingOutputCallResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::grpc::testing::Payload* payload_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsStreamingOutputCallResponseImpl();
};
// -------------------------------------------------------------------

class ReconnectParams : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.ReconnectParams) */ {
 public:
  ReconnectParams();
  virtual ~ReconnectParams();

  ReconnectParams(const ReconnectParams& from);

  inline ReconnectParams& operator=(const ReconnectParams& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ReconnectParams(ReconnectParams&& from) noexcept
    : ReconnectParams() {
    *this = ::std::move(from);
  }

  inline ReconnectParams& operator=(ReconnectParams&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ReconnectParams& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ReconnectParams* internal_default_instance() {
    return reinterpret_cast<const ReconnectParams*>(
               &_ReconnectParams_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    10;

  void Swap(ReconnectParams* other);
  friend void swap(ReconnectParams& a, ReconnectParams& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ReconnectParams* New() const PROTOBUF_FINAL { return New(NULL); }

  ReconnectParams* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ReconnectParams& from);
  void MergeFrom(const ReconnectParams& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ReconnectParams* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int32 max_reconnect_backoff_ms = 1;
  void clear_max_reconnect_backoff_ms();
  static const int kMaxReconnectBackoffMsFieldNumber = 1;
  ::google::protobuf::int32 max_reconnect_backoff_ms() const;
  void set_max_reconnect_backoff_ms(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:grpc.testing.ReconnectParams)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int32 max_reconnect_backoff_ms_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsReconnectParamsImpl();
};
// -------------------------------------------------------------------

class ReconnectInfo : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.ReconnectInfo) */ {
 public:
  ReconnectInfo();
  virtual ~ReconnectInfo();

  ReconnectInfo(const ReconnectInfo& from);

  inline ReconnectInfo& operator=(const ReconnectInfo& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ReconnectInfo(ReconnectInfo&& from) noexcept
    : ReconnectInfo() {
    *this = ::std::move(from);
  }

  inline ReconnectInfo& operator=(ReconnectInfo&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ReconnectInfo& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ReconnectInfo* internal_default_instance() {
    return reinterpret_cast<const ReconnectInfo*>(
               &_ReconnectInfo_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    11;

  void Swap(ReconnectInfo* other);
  friend void swap(ReconnectInfo& a, ReconnectInfo& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ReconnectInfo* New() const PROTOBUF_FINAL { return New(NULL); }

  ReconnectInfo* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ReconnectInfo& from);
  void MergeFrom(const ReconnectInfo& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ReconnectInfo* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated int32 backoff_ms = 2;
  int backoff_ms_size() const;
  void clear_backoff_ms();
  static const int kBackoffMsFieldNumber = 2;
  ::google::protobuf::int32 backoff_ms(int index) const;
  void set_backoff_ms(int index, ::google::protobuf::int32 value);
  void add_backoff_ms(::google::protobuf::int32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
      backoff_ms() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
      mutable_backoff_ms();

  // bool passed = 1;
  void clear_passed();
  static const int kPassedFieldNumber = 1;
  bool passed() const;
  void set_passed(bool value);

  // @@protoc_insertion_point(class_scope:grpc.testing.ReconnectInfo)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedField< ::google::protobuf::int32 > backoff_ms_;
  mutable int _backoff_ms_cached_byte_size_;
  bool passed_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::InitDefaultsReconnectInfoImpl();
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// BoolValue

// bool value = 1;
inline void BoolValue::clear_value() {
  value_ = false;
}
inline bool BoolValue::value() const {
  // @@protoc_insertion_point(field_get:grpc.testing.BoolValue.value)
  return value_;
}
inline void BoolValue::set_value(bool value) {
  
  value_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.BoolValue.value)
}

// -------------------------------------------------------------------

// Payload

// .grpc.testing.PayloadType type = 1;
inline void Payload::clear_type() {
  type_ = 0;
}
inline ::grpc::testing::PayloadType Payload::type() const {
  // @@protoc_insertion_point(field_get:grpc.testing.Payload.type)
  return static_cast< ::grpc::testing::PayloadType >(type_);
}
inline void Payload::set_type(::grpc::testing::PayloadType value) {
  
  type_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.Payload.type)
}

// bytes body = 2;
inline void Payload::clear_body() {
  body_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Payload::body() const {
  // @@protoc_insertion_point(field_get:grpc.testing.Payload.body)
  return body_.GetNoArena();
}
inline void Payload::set_body(const ::std::string& value) {
  
  body_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.testing.Payload.body)
}
#if LANG_CXX11
inline void Payload::set_body(::std::string&& value) {
  
  body_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.testing.Payload.body)
}
#endif
inline void Payload::set_body(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  body_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.testing.Payload.body)
}
inline void Payload::set_body(const void* value, size_t size) {
  
  body_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.testing.Payload.body)
}
inline ::std::string* Payload::mutable_body() {
  
  // @@protoc_insertion_point(field_mutable:grpc.testing.Payload.body)
  return body_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Payload::release_body() {
  // @@protoc_insertion_point(field_release:grpc.testing.Payload.body)
  
  return body_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Payload::set_allocated_body(::std::string* body) {
  if (body != NULL) {
    
  } else {
    
  }
  body_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), body);
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.Payload.body)
}

// -------------------------------------------------------------------

// EchoStatus

// int32 code = 1;
inline void EchoStatus::clear_code() {
  code_ = 0;
}
inline ::google::protobuf::int32 EchoStatus::code() const {
  // @@protoc_insertion_point(field_get:grpc.testing.EchoStatus.code)
  return code_;
}
inline void EchoStatus::set_code(::google::protobuf::int32 value) {
  
  code_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.EchoStatus.code)
}

// string message = 2;
inline void EchoStatus::clear_message() {
  message_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& EchoStatus::message() const {
  // @@protoc_insertion_point(field_get:grpc.testing.EchoStatus.message)
  return message_.GetNoArena();
}
inline void EchoStatus::set_message(const ::std::string& value) {
  
  message_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.testing.EchoStatus.message)
}
#if LANG_CXX11
inline void EchoStatus::set_message(::std::string&& value) {
  
  message_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.testing.EchoStatus.message)
}
#endif
inline void EchoStatus::set_message(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  message_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.testing.EchoStatus.message)
}
inline void EchoStatus::set_message(const char* value, size_t size) {
  
  message_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.testing.EchoStatus.message)
}
inline ::std::string* EchoStatus::mutable_message() {
  
  // @@protoc_insertion_point(field_mutable:grpc.testing.EchoStatus.message)
  return message_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* EchoStatus::release_message() {
  // @@protoc_insertion_point(field_release:grpc.testing.EchoStatus.message)
  
  return message_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void EchoStatus::set_allocated_message(::std::string* message) {
  if (message != NULL) {
    
  } else {
    
  }
  message_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), message);
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.EchoStatus.message)
}

// -------------------------------------------------------------------

// SimpleRequest

// .grpc.testing.PayloadType response_type = 1;
inline void SimpleRequest::clear_response_type() {
  response_type_ = 0;
}
inline ::grpc::testing::PayloadType SimpleRequest::response_type() const {
  // @@protoc_insertion_point(field_get:grpc.testing.SimpleRequest.response_type)
  return static_cast< ::grpc::testing::PayloadType >(response_type_);
}
inline void SimpleRequest::set_response_type(::grpc::testing::PayloadType value) {
  
  response_type_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.SimpleRequest.response_type)
}

// int32 response_size = 2;
inline void SimpleRequest::clear_response_size() {
  response_size_ = 0;
}
inline ::google::protobuf::int32 SimpleRequest::response_size() const {
  // @@protoc_insertion_point(field_get:grpc.testing.SimpleRequest.response_size)
  return response_size_;
}
inline void SimpleRequest::set_response_size(::google::protobuf::int32 value) {
  
  response_size_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.SimpleRequest.response_size)
}

// .grpc.testing.Payload payload = 3;
inline bool SimpleRequest::has_payload() const {
  return this != internal_default_instance() && payload_ != NULL;
}
inline void SimpleRequest::clear_payload() {
  if (GetArenaNoVirtual() == NULL && payload_ != NULL) {
    delete payload_;
  }
  payload_ = NULL;
}
inline const ::grpc::testing::Payload& SimpleRequest::payload() const {
  const ::grpc::testing::Payload* p = payload_;
  // @@protoc_insertion_point(field_get:grpc.testing.SimpleRequest.payload)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::testing::Payload*>(
      &::grpc::testing::_Payload_default_instance_);
}
inline ::grpc::testing::Payload* SimpleRequest::release_payload() {
  // @@protoc_insertion_point(field_release:grpc.testing.SimpleRequest.payload)
  
  ::grpc::testing::Payload* temp = payload_;
  payload_ = NULL;
  return temp;
}
inline ::grpc::testing::Payload* SimpleRequest::mutable_payload() {
  
  if (payload_ == NULL) {
    payload_ = new ::grpc::testing::Payload;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.SimpleRequest.payload)
  return payload_;
}
inline void SimpleRequest::set_allocated_payload(::grpc::testing::Payload* payload) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete payload_;
  }
  if (payload) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      payload = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, payload, submessage_arena);
    }
    
  } else {
    
  }
  payload_ = payload;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.SimpleRequest.payload)
}

// bool fill_username = 4;
inline void SimpleRequest::clear_fill_username() {
  fill_username_ = false;
}
inline bool SimpleRequest::fill_username() const {
  // @@protoc_insertion_point(field_get:grpc.testing.SimpleRequest.fill_username)
  return fill_username_;
}
inline void SimpleRequest::set_fill_username(bool value) {
  
  fill_username_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.SimpleRequest.fill_username)
}

// bool fill_oauth_scope = 5;
inline void SimpleRequest::clear_fill_oauth_scope() {
  fill_oauth_scope_ = false;
}
inline bool SimpleRequest::fill_oauth_scope() const {
  // @@protoc_insertion_point(field_get:grpc.testing.SimpleRequest.fill_oauth_scope)
  return fill_oauth_scope_;
}
inline void SimpleRequest::set_fill_oauth_scope(bool value) {
  
  fill_oauth_scope_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.SimpleRequest.fill_oauth_scope)
}

// .grpc.testing.BoolValue response_compressed = 6;
inline bool SimpleRequest::has_response_compressed() const {
  return this != internal_default_instance() && response_compressed_ != NULL;
}
inline void SimpleRequest::clear_response_compressed() {
  if (GetArenaNoVirtual() == NULL && response_compressed_ != NULL) {
    delete response_compressed_;
  }
  response_compressed_ = NULL;
}
inline const ::grpc::testing::BoolValue& SimpleRequest::response_compressed() const {
  const ::grpc::testing::BoolValue* p = response_compressed_;
  // @@protoc_insertion_point(field_get:grpc.testing.SimpleRequest.response_compressed)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::testing::BoolValue*>(
      &::grpc::testing::_BoolValue_default_instance_);
}
inline ::grpc::testing::BoolValue* SimpleRequest::release_response_compressed() {
  // @@protoc_insertion_point(field_release:grpc.testing.SimpleRequest.response_compressed)
  
  ::grpc::testing::BoolValue* temp = response_compressed_;
  response_compressed_ = NULL;
  return temp;
}
inline ::grpc::testing::BoolValue* SimpleRequest::mutable_response_compressed() {
  
  if (response_compressed_ == NULL) {
    response_compressed_ = new ::grpc::testing::BoolValue;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.SimpleRequest.response_compressed)
  return response_compressed_;
}
inline void SimpleRequest::set_allocated_response_compressed(::grpc::testing::BoolValue* response_compressed) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete response_compressed_;
  }
  if (response_compressed) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      response_compressed = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, response_compressed, submessage_arena);
    }
    
  } else {
    
  }
  response_compressed_ = response_compressed;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.SimpleRequest.response_compressed)
}

// .grpc.testing.EchoStatus response_status = 7;
inline bool SimpleRequest::has_response_status() const {
  return this != internal_default_instance() && response_status_ != NULL;
}
inline void SimpleRequest::clear_response_status() {
  if (GetArenaNoVirtual() == NULL && response_status_ != NULL) {
    delete response_status_;
  }
  response_status_ = NULL;
}
inline const ::grpc::testing::EchoStatus& SimpleRequest::response_status() const {
  const ::grpc::testing::EchoStatus* p = response_status_;
  // @@protoc_insertion_point(field_get:grpc.testing.SimpleRequest.response_status)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::testing::EchoStatus*>(
      &::grpc::testing::_EchoStatus_default_instance_);
}
inline ::grpc::testing::EchoStatus* SimpleRequest::release_response_status() {
  // @@protoc_insertion_point(field_release:grpc.testing.SimpleRequest.response_status)
  
  ::grpc::testing::EchoStatus* temp = response_status_;
  response_status_ = NULL;
  return temp;
}
inline ::grpc::testing::EchoStatus* SimpleRequest::mutable_response_status() {
  
  if (response_status_ == NULL) {
    response_status_ = new ::grpc::testing::EchoStatus;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.SimpleRequest.response_status)
  return response_status_;
}
inline void SimpleRequest::set_allocated_response_status(::grpc::testing::EchoStatus* response_status) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete response_status_;
  }
  if (response_status) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      response_status = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, response_status, submessage_arena);
    }
    
  } else {
    
  }
  response_status_ = response_status;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.SimpleRequest.response_status)
}

// .grpc.testing.BoolValue expect_compressed = 8;
inline bool SimpleRequest::has_expect_compressed() const {
  return this != internal_default_instance() && expect_compressed_ != NULL;
}
inline void SimpleRequest::clear_expect_compressed() {
  if (GetArenaNoVirtual() == NULL && expect_compressed_ != NULL) {
    delete expect_compressed_;
  }
  expect_compressed_ = NULL;
}
inline const ::grpc::testing::BoolValue& SimpleRequest::expect_compressed() const {
  const ::grpc::testing::BoolValue* p = expect_compressed_;
  // @@protoc_insertion_point(field_get:grpc.testing.SimpleRequest.expect_compressed)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::testing::BoolValue*>(
      &::grpc::testing::_BoolValue_default_instance_);
}
inline ::grpc::testing::BoolValue* SimpleRequest::release_expect_compressed() {
  // @@protoc_insertion_point(field_release:grpc.testing.SimpleRequest.expect_compressed)
  
  ::grpc::testing::BoolValue* temp = expect_compressed_;
  expect_compressed_ = NULL;
  return temp;
}
inline ::grpc::testing::BoolValue* SimpleRequest::mutable_expect_compressed() {
  
  if (expect_compressed_ == NULL) {
    expect_compressed_ = new ::grpc::testing::BoolValue;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.SimpleRequest.expect_compressed)
  return expect_compressed_;
}
inline void SimpleRequest::set_allocated_expect_compressed(::grpc::testing::BoolValue* expect_compressed) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete expect_compressed_;
  }
  if (expect_compressed) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      expect_compressed = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, expect_compressed, submessage_arena);
    }
    
  } else {
    
  }
  expect_compressed_ = expect_compressed;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.SimpleRequest.expect_compressed)
}

// -------------------------------------------------------------------

// SimpleResponse

// .grpc.testing.Payload payload = 1;
inline bool SimpleResponse::has_payload() const {
  return this != internal_default_instance() && payload_ != NULL;
}
inline void SimpleResponse::clear_payload() {
  if (GetArenaNoVirtual() == NULL && payload_ != NULL) {
    delete payload_;
  }
  payload_ = NULL;
}
inline const ::grpc::testing::Payload& SimpleResponse::payload() const {
  const ::grpc::testing::Payload* p = payload_;
  // @@protoc_insertion_point(field_get:grpc.testing.SimpleResponse.payload)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::testing::Payload*>(
      &::grpc::testing::_Payload_default_instance_);
}
inline ::grpc::testing::Payload* SimpleResponse::release_payload() {
  // @@protoc_insertion_point(field_release:grpc.testing.SimpleResponse.payload)
  
  ::grpc::testing::Payload* temp = payload_;
  payload_ = NULL;
  return temp;
}
inline ::grpc::testing::Payload* SimpleResponse::mutable_payload() {
  
  if (payload_ == NULL) {
    payload_ = new ::grpc::testing::Payload;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.SimpleResponse.payload)
  return payload_;
}
inline void SimpleResponse::set_allocated_payload(::grpc::testing::Payload* payload) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete payload_;
  }
  if (payload) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      payload = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, payload, submessage_arena);
    }
    
  } else {
    
  }
  payload_ = payload;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.SimpleResponse.payload)
}

// string username = 2;
inline void SimpleResponse::clear_username() {
  username_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SimpleResponse::username() const {
  // @@protoc_insertion_point(field_get:grpc.testing.SimpleResponse.username)
  return username_.GetNoArena();
}
inline void SimpleResponse::set_username(const ::std::string& value) {
  
  username_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.testing.SimpleResponse.username)
}
#if LANG_CXX11
inline void SimpleResponse::set_username(::std::string&& value) {
  
  username_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.testing.SimpleResponse.username)
}
#endif
inline void SimpleResponse::set_username(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  username_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.testing.SimpleResponse.username)
}
inline void SimpleResponse::set_username(const char* value, size_t size) {
  
  username_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.testing.SimpleResponse.username)
}
inline ::std::string* SimpleResponse::mutable_username() {
  
  // @@protoc_insertion_point(field_mutable:grpc.testing.SimpleResponse.username)
  return username_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SimpleResponse::release_username() {
  // @@protoc_insertion_point(field_release:grpc.testing.SimpleResponse.username)
  
  return username_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SimpleResponse::set_allocated_username(::std::string* username) {
  if (username != NULL) {
    
  } else {
    
  }
  username_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), username);
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.SimpleResponse.username)
}

// string oauth_scope = 3;
inline void SimpleResponse::clear_oauth_scope() {
  oauth_scope_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& SimpleResponse::oauth_scope() const {
  // @@protoc_insertion_point(field_get:grpc.testing.SimpleResponse.oauth_scope)
  return oauth_scope_.GetNoArena();
}
inline void SimpleResponse::set_oauth_scope(const ::std::string& value) {
  
  oauth_scope_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.testing.SimpleResponse.oauth_scope)
}
#if LANG_CXX11
inline void SimpleResponse::set_oauth_scope(::std::string&& value) {
  
  oauth_scope_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.testing.SimpleResponse.oauth_scope)
}
#endif
inline void SimpleResponse::set_oauth_scope(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  oauth_scope_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.testing.SimpleResponse.oauth_scope)
}
inline void SimpleResponse::set_oauth_scope(const char* value, size_t size) {
  
  oauth_scope_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.testing.SimpleResponse.oauth_scope)
}
inline ::std::string* SimpleResponse::mutable_oauth_scope() {
  
  // @@protoc_insertion_point(field_mutable:grpc.testing.SimpleResponse.oauth_scope)
  return oauth_scope_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* SimpleResponse::release_oauth_scope() {
  // @@protoc_insertion_point(field_release:grpc.testing.SimpleResponse.oauth_scope)
  
  return oauth_scope_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void SimpleResponse::set_allocated_oauth_scope(::std::string* oauth_scope) {
  if (oauth_scope != NULL) {
    
  } else {
    
  }
  oauth_scope_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), oauth_scope);
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.SimpleResponse.oauth_scope)
}

// -------------------------------------------------------------------

// StreamingInputCallRequest

// .grpc.testing.Payload payload = 1;
inline bool StreamingInputCallRequest::has_payload() const {
  return this != internal_default_instance() && payload_ != NULL;
}
inline void StreamingInputCallRequest::clear_payload() {
  if (GetArenaNoVirtual() == NULL && payload_ != NULL) {
    delete payload_;
  }
  payload_ = NULL;
}
inline const ::grpc::testing::Payload& StreamingInputCallRequest::payload() const {
  const ::grpc::testing::Payload* p = payload_;
  // @@protoc_insertion_point(field_get:grpc.testing.StreamingInputCallRequest.payload)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::testing::Payload*>(
      &::grpc::testing::_Payload_default_instance_);
}
inline ::grpc::testing::Payload* StreamingInputCallRequest::release_payload() {
  // @@protoc_insertion_point(field_release:grpc.testing.StreamingInputCallRequest.payload)
  
  ::grpc::testing::Payload* temp = payload_;
  payload_ = NULL;
  return temp;
}
inline ::grpc::testing::Payload* StreamingInputCallRequest::mutable_payload() {
  
  if (payload_ == NULL) {
    payload_ = new ::grpc::testing::Payload;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.StreamingInputCallRequest.payload)
  return payload_;
}
inline void StreamingInputCallRequest::set_allocated_payload(::grpc::testing::Payload* payload) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete payload_;
  }
  if (payload) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      payload = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, payload, submessage_arena);
    }
    
  } else {
    
  }
  payload_ = payload;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.StreamingInputCallRequest.payload)
}

// .grpc.testing.BoolValue expect_compressed = 2;
inline bool StreamingInputCallRequest::has_expect_compressed() const {
  return this != internal_default_instance() && expect_compressed_ != NULL;
}
inline void StreamingInputCallRequest::clear_expect_compressed() {
  if (GetArenaNoVirtual() == NULL && expect_compressed_ != NULL) {
    delete expect_compressed_;
  }
  expect_compressed_ = NULL;
}
inline const ::grpc::testing::BoolValue& StreamingInputCallRequest::expect_compressed() const {
  const ::grpc::testing::BoolValue* p = expect_compressed_;
  // @@protoc_insertion_point(field_get:grpc.testing.StreamingInputCallRequest.expect_compressed)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::testing::BoolValue*>(
      &::grpc::testing::_BoolValue_default_instance_);
}
inline ::grpc::testing::BoolValue* StreamingInputCallRequest::release_expect_compressed() {
  // @@protoc_insertion_point(field_release:grpc.testing.StreamingInputCallRequest.expect_compressed)
  
  ::grpc::testing::BoolValue* temp = expect_compressed_;
  expect_compressed_ = NULL;
  return temp;
}
inline ::grpc::testing::BoolValue* StreamingInputCallRequest::mutable_expect_compressed() {
  
  if (expect_compressed_ == NULL) {
    expect_compressed_ = new ::grpc::testing::BoolValue;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.StreamingInputCallRequest.expect_compressed)
  return expect_compressed_;
}
inline void StreamingInputCallRequest::set_allocated_expect_compressed(::grpc::testing::BoolValue* expect_compressed) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete expect_compressed_;
  }
  if (expect_compressed) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      expect_compressed = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, expect_compressed, submessage_arena);
    }
    
  } else {
    
  }
  expect_compressed_ = expect_compressed;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.StreamingInputCallRequest.expect_compressed)
}

// -------------------------------------------------------------------

// StreamingInputCallResponse

// int32 aggregated_payload_size = 1;
inline void StreamingInputCallResponse::clear_aggregated_payload_size() {
  aggregated_payload_size_ = 0;
}
inline ::google::protobuf::int32 StreamingInputCallResponse::aggregated_payload_size() const {
  // @@protoc_insertion_point(field_get:grpc.testing.StreamingInputCallResponse.aggregated_payload_size)
  return aggregated_payload_size_;
}
inline void StreamingInputCallResponse::set_aggregated_payload_size(::google::protobuf::int32 value) {
  
  aggregated_payload_size_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.StreamingInputCallResponse.aggregated_payload_size)
}

// -------------------------------------------------------------------

// ResponseParameters

// int32 size = 1;
inline void ResponseParameters::clear_size() {
  size_ = 0;
}
inline ::google::protobuf::int32 ResponseParameters::size() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ResponseParameters.size)
  return size_;
}
inline void ResponseParameters::set_size(::google::protobuf::int32 value) {
  
  size_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ResponseParameters.size)
}

// int32 interval_us = 2;
inline void ResponseParameters::clear_interval_us() {
  interval_us_ = 0;
}
inline ::google::protobuf::int32 ResponseParameters::interval_us() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ResponseParameters.interval_us)
  return interval_us_;
}
inline void ResponseParameters::set_interval_us(::google::protobuf::int32 value) {
  
  interval_us_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ResponseParameters.interval_us)
}

// .grpc.testing.BoolValue compressed = 3;
inline bool ResponseParameters::has_compressed() const {
  return this != internal_default_instance() && compressed_ != NULL;
}
inline void ResponseParameters::clear_compressed() {
  if (GetArenaNoVirtual() == NULL && compressed_ != NULL) {
    delete compressed_;
  }
  compressed_ = NULL;
}
inline const ::grpc::testing::BoolValue& ResponseParameters::compressed() const {
  const ::grpc::testing::BoolValue* p = compressed_;
  // @@protoc_insertion_point(field_get:grpc.testing.ResponseParameters.compressed)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::testing::BoolValue*>(
      &::grpc::testing::_BoolValue_default_instance_);
}
inline ::grpc::testing::BoolValue* ResponseParameters::release_compressed() {
  // @@protoc_insertion_point(field_release:grpc.testing.ResponseParameters.compressed)
  
  ::grpc::testing::BoolValue* temp = compressed_;
  compressed_ = NULL;
  return temp;
}
inline ::grpc::testing::BoolValue* ResponseParameters::mutable_compressed() {
  
  if (compressed_ == NULL) {
    compressed_ = new ::grpc::testing::BoolValue;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.ResponseParameters.compressed)
  return compressed_;
}
inline void ResponseParameters::set_allocated_compressed(::grpc::testing::BoolValue* compressed) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete compressed_;
  }
  if (compressed) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      compressed = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, compressed, submessage_arena);
    }
    
  } else {
    
  }
  compressed_ = compressed;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.ResponseParameters.compressed)
}

// -------------------------------------------------------------------

// StreamingOutputCallRequest

// .grpc.testing.PayloadType response_type = 1;
inline void StreamingOutputCallRequest::clear_response_type() {
  response_type_ = 0;
}
inline ::grpc::testing::PayloadType StreamingOutputCallRequest::response_type() const {
  // @@protoc_insertion_point(field_get:grpc.testing.StreamingOutputCallRequest.response_type)
  return static_cast< ::grpc::testing::PayloadType >(response_type_);
}
inline void StreamingOutputCallRequest::set_response_type(::grpc::testing::PayloadType value) {
  
  response_type_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.StreamingOutputCallRequest.response_type)
}

// repeated .grpc.testing.ResponseParameters response_parameters = 2;
inline int StreamingOutputCallRequest::response_parameters_size() const {
  return response_parameters_.size();
}
inline void StreamingOutputCallRequest::clear_response_parameters() {
  response_parameters_.Clear();
}
inline const ::grpc::testing::ResponseParameters& StreamingOutputCallRequest::response_parameters(int index) const {
  // @@protoc_insertion_point(field_get:grpc.testing.StreamingOutputCallRequest.response_parameters)
  return response_parameters_.Get(index);
}
inline ::grpc::testing::ResponseParameters* StreamingOutputCallRequest::mutable_response_parameters(int index) {
  // @@protoc_insertion_point(field_mutable:grpc.testing.StreamingOutputCallRequest.response_parameters)
  return response_parameters_.Mutable(index);
}
inline ::grpc::testing::ResponseParameters* StreamingOutputCallRequest::add_response_parameters() {
  // @@protoc_insertion_point(field_add:grpc.testing.StreamingOutputCallRequest.response_parameters)
  return response_parameters_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::grpc::testing::ResponseParameters >*
StreamingOutputCallRequest::mutable_response_parameters() {
  // @@protoc_insertion_point(field_mutable_list:grpc.testing.StreamingOutputCallRequest.response_parameters)
  return &response_parameters_;
}
inline const ::google::protobuf::RepeatedPtrField< ::grpc::testing::ResponseParameters >&
StreamingOutputCallRequest::response_parameters() const {
  // @@protoc_insertion_point(field_list:grpc.testing.StreamingOutputCallRequest.response_parameters)
  return response_parameters_;
}

// .grpc.testing.Payload payload = 3;
inline bool StreamingOutputCallRequest::has_payload() const {
  return this != internal_default_instance() && payload_ != NULL;
}
inline void StreamingOutputCallRequest::clear_payload() {
  if (GetArenaNoVirtual() == NULL && payload_ != NULL) {
    delete payload_;
  }
  payload_ = NULL;
}
inline const ::grpc::testing::Payload& StreamingOutputCallRequest::payload() const {
  const ::grpc::testing::Payload* p = payload_;
  // @@protoc_insertion_point(field_get:grpc.testing.StreamingOutputCallRequest.payload)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::testing::Payload*>(
      &::grpc::testing::_Payload_default_instance_);
}
inline ::grpc::testing::Payload* StreamingOutputCallRequest::release_payload() {
  // @@protoc_insertion_point(field_release:grpc.testing.StreamingOutputCallRequest.payload)
  
  ::grpc::testing::Payload* temp = payload_;
  payload_ = NULL;
  return temp;
}
inline ::grpc::testing::Payload* StreamingOutputCallRequest::mutable_payload() {
  
  if (payload_ == NULL) {
    payload_ = new ::grpc::testing::Payload;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.StreamingOutputCallRequest.payload)
  return payload_;
}
inline void StreamingOutputCallRequest::set_allocated_payload(::grpc::testing::Payload* payload) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete payload_;
  }
  if (payload) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      payload = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, payload, submessage_arena);
    }
    
  } else {
    
  }
  payload_ = payload;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.StreamingOutputCallRequest.payload)
}

// .grpc.testing.EchoStatus response_status = 7;
inline bool StreamingOutputCallRequest::has_response_status() const {
  return this != internal_default_instance() && response_status_ != NULL;
}
inline void StreamingOutputCallRequest::clear_response_status() {
  if (GetArenaNoVirtual() == NULL && response_status_ != NULL) {
    delete response_status_;
  }
  response_status_ = NULL;
}
inline const ::grpc::testing::EchoStatus& StreamingOutputCallRequest::response_status() const {
  const ::grpc::testing::EchoStatus* p = response_status_;
  // @@protoc_insertion_point(field_get:grpc.testing.StreamingOutputCallRequest.response_status)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::testing::EchoStatus*>(
      &::grpc::testing::_EchoStatus_default_instance_);
}
inline ::grpc::testing::EchoStatus* StreamingOutputCallRequest::release_response_status() {
  // @@protoc_insertion_point(field_release:grpc.testing.StreamingOutputCallRequest.response_status)
  
  ::grpc::testing::EchoStatus* temp = response_status_;
  response_status_ = NULL;
  return temp;
}
inline ::grpc::testing::EchoStatus* StreamingOutputCallRequest::mutable_response_status() {
  
  if (response_status_ == NULL) {
    response_status_ = new ::grpc::testing::EchoStatus;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.StreamingOutputCallRequest.response_status)
  return response_status_;
}
inline void StreamingOutputCallRequest::set_allocated_response_status(::grpc::testing::EchoStatus* response_status) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete response_status_;
  }
  if (response_status) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      response_status = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, response_status, submessage_arena);
    }
    
  } else {
    
  }
  response_status_ = response_status;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.StreamingOutputCallRequest.response_status)
}

// -------------------------------------------------------------------

// StreamingOutputCallResponse

// .grpc.testing.Payload payload = 1;
inline bool StreamingOutputCallResponse::has_payload() const {
  return this != internal_default_instance() && payload_ != NULL;
}
inline void StreamingOutputCallResponse::clear_payload() {
  if (GetArenaNoVirtual() == NULL && payload_ != NULL) {
    delete payload_;
  }
  payload_ = NULL;
}
inline const ::grpc::testing::Payload& StreamingOutputCallResponse::payload() const {
  const ::grpc::testing::Payload* p = payload_;
  // @@protoc_insertion_point(field_get:grpc.testing.StreamingOutputCallResponse.payload)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::testing::Payload*>(
      &::grpc::testing::_Payload_default_instance_);
}
inline ::grpc::testing::Payload* StreamingOutputCallResponse::release_payload() {
  // @@protoc_insertion_point(field_release:grpc.testing.StreamingOutputCallResponse.payload)
  
  ::grpc::testing::Payload* temp = payload_;
  payload_ = NULL;
  return temp;
}
inline ::grpc::testing::Payload* StreamingOutputCallResponse::mutable_payload() {
  
  if (payload_ == NULL) {
    payload_ = new ::grpc::testing::Payload;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.StreamingOutputCallResponse.payload)
  return payload_;
}
inline void StreamingOutputCallResponse::set_allocated_payload(::grpc::testing::Payload* payload) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete payload_;
  }
  if (payload) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      payload = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, payload, submessage_arena);
    }
    
  } else {
    
  }
  payload_ = payload;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.StreamingOutputCallResponse.payload)
}

// -------------------------------------------------------------------

// ReconnectParams

// int32 max_reconnect_backoff_ms = 1;
inline void ReconnectParams::clear_max_reconnect_backoff_ms() {
  max_reconnect_backoff_ms_ = 0;
}
inline ::google::protobuf::int32 ReconnectParams::max_reconnect_backoff_ms() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ReconnectParams.max_reconnect_backoff_ms)
  return max_reconnect_backoff_ms_;
}
inline void ReconnectParams::set_max_reconnect_backoff_ms(::google::protobuf::int32 value) {
  
  max_reconnect_backoff_ms_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ReconnectParams.max_reconnect_backoff_ms)
}

// -------------------------------------------------------------------

// ReconnectInfo

// bool passed = 1;
inline void ReconnectInfo::clear_passed() {
  passed_ = false;
}
inline bool ReconnectInfo::passed() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ReconnectInfo.passed)
  return passed_;
}
inline void ReconnectInfo::set_passed(bool value) {
  
  passed_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ReconnectInfo.passed)
}

// repeated int32 backoff_ms = 2;
inline int ReconnectInfo::backoff_ms_size() const {
  return backoff_ms_.size();
}
inline void ReconnectInfo::clear_backoff_ms() {
  backoff_ms_.Clear();
}
inline ::google::protobuf::int32 ReconnectInfo::backoff_ms(int index) const {
  // @@protoc_insertion_point(field_get:grpc.testing.ReconnectInfo.backoff_ms)
  return backoff_ms_.Get(index);
}
inline void ReconnectInfo::set_backoff_ms(int index, ::google::protobuf::int32 value) {
  backoff_ms_.Set(index, value);
  // @@protoc_insertion_point(field_set:grpc.testing.ReconnectInfo.backoff_ms)
}
inline void ReconnectInfo::add_backoff_ms(::google::protobuf::int32 value) {
  backoff_ms_.Add(value);
  // @@protoc_insertion_point(field_add:grpc.testing.ReconnectInfo.backoff_ms)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::int32 >&
ReconnectInfo::backoff_ms() const {
  // @@protoc_insertion_point(field_list:grpc.testing.ReconnectInfo.backoff_ms)
  return backoff_ms_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::int32 >*
ReconnectInfo::mutable_backoff_ms() {
  // @@protoc_insertion_point(field_mutable_list:grpc.testing.ReconnectInfo.backoff_ms)
  return &backoff_ms_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace testing
}  // namespace grpc

namespace google {
namespace protobuf {

template <> struct is_proto_enum< ::grpc::testing::PayloadType> : ::google::protobuf::internal::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::grpc::testing::PayloadType>() {
  return ::grpc::testing::PayloadType_descriptor();
}

}  // namespace protobuf
}  // namespace google

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto__INCLUDED
