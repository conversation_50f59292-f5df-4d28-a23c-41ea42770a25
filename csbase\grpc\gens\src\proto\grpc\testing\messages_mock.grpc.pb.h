// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: src/proto/grpc/testing/messages.proto

#include "src/proto/grpc/testing/messages.pb.h"
#include "src/proto/grpc/testing/messages.grpc.pb.h"

#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/sync_stream.h>
#include <gmock/gmock.h>
namespace grpc {
namespace testing {

} // namespace grpc
} // namespace testing

