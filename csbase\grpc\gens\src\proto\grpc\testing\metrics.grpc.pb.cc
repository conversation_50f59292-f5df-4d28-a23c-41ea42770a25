// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: src/proto/grpc/testing/metrics.proto

#include "src/proto/grpc/testing/metrics.pb.h"
#include "src/proto/grpc/testing/metrics.grpc.pb.h"

#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/method_handler_impl.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace grpc {
namespace testing {

static const char* MetricsService_method_names[] = {
  "/grpc.testing.MetricsService/GetAllGauges",
  "/grpc.testing.MetricsService/GetGauge",
};

std::unique_ptr< MetricsService::Stub> MetricsService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< MetricsService::Stub> stub(new MetricsService::Stub(channel));
  return stub;
}

MetricsService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel)
  : channel_(channel), rpcmethod_GetAllGauges_(MetricsService_method_names[0], ::grpc::internal::RpcMethod::SERVER_STREAMING, channel)
  , rpcmethod_GetGauge_(MetricsService_method_names[1], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::ClientReader< ::grpc::testing::GaugeResponse>* MetricsService::Stub::GetAllGaugesRaw(::grpc::ClientContext* context, const ::grpc::testing::EmptyMessage& request) {
  return ::grpc::internal::ClientReaderFactory< ::grpc::testing::GaugeResponse>::Create(channel_.get(), rpcmethod_GetAllGauges_, context, request);
}

::grpc::ClientAsyncReader< ::grpc::testing::GaugeResponse>* MetricsService::Stub::AsyncGetAllGaugesRaw(::grpc::ClientContext* context, const ::grpc::testing::EmptyMessage& request, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncReaderFactory< ::grpc::testing::GaugeResponse>::Create(channel_.get(), cq, rpcmethod_GetAllGauges_, context, request, true, tag);
}

::grpc::ClientAsyncReader< ::grpc::testing::GaugeResponse>* MetricsService::Stub::PrepareAsyncGetAllGaugesRaw(::grpc::ClientContext* context, const ::grpc::testing::EmptyMessage& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncReaderFactory< ::grpc::testing::GaugeResponse>::Create(channel_.get(), cq, rpcmethod_GetAllGauges_, context, request, false, nullptr);
}

::grpc::Status MetricsService::Stub::GetGauge(::grpc::ClientContext* context, const ::grpc::testing::GaugeRequest& request, ::grpc::testing::GaugeResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_GetGauge_, context, request, response);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::GaugeResponse>* MetricsService::Stub::AsyncGetGaugeRaw(::grpc::ClientContext* context, const ::grpc::testing::GaugeRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::GaugeResponse>::Create(channel_.get(), cq, rpcmethod_GetGauge_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::GaugeResponse>* MetricsService::Stub::PrepareAsyncGetGaugeRaw(::grpc::ClientContext* context, const ::grpc::testing::GaugeRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::GaugeResponse>::Create(channel_.get(), cq, rpcmethod_GetGauge_, context, request, false);
}

MetricsService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      MetricsService_method_names[0],
      ::grpc::internal::RpcMethod::SERVER_STREAMING,
      new ::grpc::internal::ServerStreamingHandler< MetricsService::Service, ::grpc::testing::EmptyMessage, ::grpc::testing::GaugeResponse>(
          std::mem_fn(&MetricsService::Service::GetAllGauges), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      MetricsService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< MetricsService::Service, ::grpc::testing::GaugeRequest, ::grpc::testing::GaugeResponse>(
          std::mem_fn(&MetricsService::Service::GetGauge), this)));
}

MetricsService::Service::~Service() {
}

::grpc::Status MetricsService::Service::GetAllGauges(::grpc::ServerContext* context, const ::grpc::testing::EmptyMessage* request, ::grpc::ServerWriter< ::grpc::testing::GaugeResponse>* writer) {
  (void) context;
  (void) request;
  (void) writer;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status MetricsService::Service::GetGauge(::grpc::ServerContext* context, const ::grpc::testing::GaugeRequest* request, ::grpc::testing::GaugeResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace grpc
}  // namespace testing

