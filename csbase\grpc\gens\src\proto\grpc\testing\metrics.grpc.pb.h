// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: src/proto/grpc/testing/metrics.proto
// Original file comments:
// Copyright 2015-2016 gRPC authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// Contains the definitions for a metrics service and the type of metrics
// exposed by the service.
//
// Currently, 'Gauge' (i.e a metric that represents the measured value of
// something at an instant of time) is the only metric type supported by the
// service.
#ifndef GRPC_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto__INCLUDED
#define GRPC_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto__INCLUDED

#include "src/proto/grpc/testing/metrics.pb.h"

#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/method_handler_impl.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace grpc {
class CompletionQueue;
class Channel;
class ServerCompletionQueue;
class ServerContext;
}  // namespace grpc

namespace grpc {
namespace testing {

class MetricsService final {
 public:
  static constexpr char const* service_full_name() {
    return "grpc.testing.MetricsService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    // Returns the values of all the gauges that are currently being maintained by
    // the service
    std::unique_ptr< ::grpc::ClientReaderInterface< ::grpc::testing::GaugeResponse>> GetAllGauges(::grpc::ClientContext* context, const ::grpc::testing::EmptyMessage& request) {
      return std::unique_ptr< ::grpc::ClientReaderInterface< ::grpc::testing::GaugeResponse>>(GetAllGaugesRaw(context, request));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::grpc::testing::GaugeResponse>> AsyncGetAllGauges(::grpc::ClientContext* context, const ::grpc::testing::EmptyMessage& request, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::grpc::testing::GaugeResponse>>(AsyncGetAllGaugesRaw(context, request, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::grpc::testing::GaugeResponse>> PrepareAsyncGetAllGauges(::grpc::ClientContext* context, const ::grpc::testing::EmptyMessage& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::grpc::testing::GaugeResponse>>(PrepareAsyncGetAllGaugesRaw(context, request, cq));
    }
    // Returns the value of one gauge
    virtual ::grpc::Status GetGauge(::grpc::ClientContext* context, const ::grpc::testing::GaugeRequest& request, ::grpc::testing::GaugeResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::GaugeResponse>> AsyncGetGauge(::grpc::ClientContext* context, const ::grpc::testing::GaugeRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::GaugeResponse>>(AsyncGetGaugeRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::GaugeResponse>> PrepareAsyncGetGauge(::grpc::ClientContext* context, const ::grpc::testing::GaugeRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::GaugeResponse>>(PrepareAsyncGetGaugeRaw(context, request, cq));
    }
  private:
    virtual ::grpc::ClientReaderInterface< ::grpc::testing::GaugeResponse>* GetAllGaugesRaw(::grpc::ClientContext* context, const ::grpc::testing::EmptyMessage& request) = 0;
    virtual ::grpc::ClientAsyncReaderInterface< ::grpc::testing::GaugeResponse>* AsyncGetAllGaugesRaw(::grpc::ClientContext* context, const ::grpc::testing::EmptyMessage& request, ::grpc::CompletionQueue* cq, void* tag) = 0;
    virtual ::grpc::ClientAsyncReaderInterface< ::grpc::testing::GaugeResponse>* PrepareAsyncGetAllGaugesRaw(::grpc::ClientContext* context, const ::grpc::testing::EmptyMessage& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::GaugeResponse>* AsyncGetGaugeRaw(::grpc::ClientContext* context, const ::grpc::testing::GaugeRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::GaugeResponse>* PrepareAsyncGetGaugeRaw(::grpc::ClientContext* context, const ::grpc::testing::GaugeRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel);
    std::unique_ptr< ::grpc::ClientReader< ::grpc::testing::GaugeResponse>> GetAllGauges(::grpc::ClientContext* context, const ::grpc::testing::EmptyMessage& request) {
      return std::unique_ptr< ::grpc::ClientReader< ::grpc::testing::GaugeResponse>>(GetAllGaugesRaw(context, request));
    }
    std::unique_ptr< ::grpc::ClientAsyncReader< ::grpc::testing::GaugeResponse>> AsyncGetAllGauges(::grpc::ClientContext* context, const ::grpc::testing::EmptyMessage& request, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReader< ::grpc::testing::GaugeResponse>>(AsyncGetAllGaugesRaw(context, request, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReader< ::grpc::testing::GaugeResponse>> PrepareAsyncGetAllGauges(::grpc::ClientContext* context, const ::grpc::testing::EmptyMessage& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReader< ::grpc::testing::GaugeResponse>>(PrepareAsyncGetAllGaugesRaw(context, request, cq));
    }
    ::grpc::Status GetGauge(::grpc::ClientContext* context, const ::grpc::testing::GaugeRequest& request, ::grpc::testing::GaugeResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::GaugeResponse>> AsyncGetGauge(::grpc::ClientContext* context, const ::grpc::testing::GaugeRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::GaugeResponse>>(AsyncGetGaugeRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::GaugeResponse>> PrepareAsyncGetGauge(::grpc::ClientContext* context, const ::grpc::testing::GaugeRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::GaugeResponse>>(PrepareAsyncGetGaugeRaw(context, request, cq));
    }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    ::grpc::ClientReader< ::grpc::testing::GaugeResponse>* GetAllGaugesRaw(::grpc::ClientContext* context, const ::grpc::testing::EmptyMessage& request) override;
    ::grpc::ClientAsyncReader< ::grpc::testing::GaugeResponse>* AsyncGetAllGaugesRaw(::grpc::ClientContext* context, const ::grpc::testing::EmptyMessage& request, ::grpc::CompletionQueue* cq, void* tag) override;
    ::grpc::ClientAsyncReader< ::grpc::testing::GaugeResponse>* PrepareAsyncGetAllGaugesRaw(::grpc::ClientContext* context, const ::grpc::testing::EmptyMessage& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::testing::GaugeResponse>* AsyncGetGaugeRaw(::grpc::ClientContext* context, const ::grpc::testing::GaugeRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::testing::GaugeResponse>* PrepareAsyncGetGaugeRaw(::grpc::ClientContext* context, const ::grpc::testing::GaugeRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_GetAllGauges_;
    const ::grpc::internal::RpcMethod rpcmethod_GetGauge_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    // Returns the values of all the gauges that are currently being maintained by
    // the service
    virtual ::grpc::Status GetAllGauges(::grpc::ServerContext* context, const ::grpc::testing::EmptyMessage* request, ::grpc::ServerWriter< ::grpc::testing::GaugeResponse>* writer);
    // Returns the value of one gauge
    virtual ::grpc::Status GetGauge(::grpc::ServerContext* context, const ::grpc::testing::GaugeRequest* request, ::grpc::testing::GaugeResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_GetAllGauges : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_GetAllGauges() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_GetAllGauges() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetAllGauges(::grpc::ServerContext* context, const ::grpc::testing::EmptyMessage* request, ::grpc::ServerWriter< ::grpc::testing::GaugeResponse>* writer) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetAllGauges(::grpc::ServerContext* context, ::grpc::testing::EmptyMessage* request, ::grpc::ServerAsyncWriter< ::grpc::testing::GaugeResponse>* writer, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncServerStreaming(0, context, request, writer, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetGauge : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_GetGauge() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_GetGauge() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetGauge(::grpc::ServerContext* context, const ::grpc::testing::GaugeRequest* request, ::grpc::testing::GaugeResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetGauge(::grpc::ServerContext* context, ::grpc::testing::GaugeRequest* request, ::grpc::ServerAsyncResponseWriter< ::grpc::testing::GaugeResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_GetAllGauges<WithAsyncMethod_GetGauge<Service > > AsyncService;
  template <class BaseClass>
  class WithGenericMethod_GetAllGauges : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_GetAllGauges() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_GetAllGauges() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetAllGauges(::grpc::ServerContext* context, const ::grpc::testing::EmptyMessage* request, ::grpc::ServerWriter< ::grpc::testing::GaugeResponse>* writer) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetGauge : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_GetGauge() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_GetGauge() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetGauge(::grpc::ServerContext* context, const ::grpc::testing::GaugeRequest* request, ::grpc::testing::GaugeResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetAllGauges : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_GetAllGauges() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_GetAllGauges() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetAllGauges(::grpc::ServerContext* context, const ::grpc::testing::EmptyMessage* request, ::grpc::ServerWriter< ::grpc::testing::GaugeResponse>* writer) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetAllGauges(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncWriter< ::grpc::ByteBuffer>* writer, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncServerStreaming(0, context, request, writer, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetGauge : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_GetGauge() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_GetGauge() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetGauge(::grpc::ServerContext* context, const ::grpc::testing::GaugeRequest* request, ::grpc::testing::GaugeResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetGauge(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetGauge : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithStreamedUnaryMethod_GetGauge() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler< ::grpc::testing::GaugeRequest, ::grpc::testing::GaugeResponse>(std::bind(&WithStreamedUnaryMethod_GetGauge<BaseClass>::StreamedGetGauge, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_GetGauge() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetGauge(::grpc::ServerContext* context, const ::grpc::testing::GaugeRequest* request, ::grpc::testing::GaugeResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetGauge(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::grpc::testing::GaugeRequest,::grpc::testing::GaugeResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_GetGauge<Service > StreamedUnaryService;
  template <class BaseClass>
  class WithSplitStreamingMethod_GetAllGauges : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithSplitStreamingMethod_GetAllGauges() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::SplitServerStreamingHandler< ::grpc::testing::EmptyMessage, ::grpc::testing::GaugeResponse>(std::bind(&WithSplitStreamingMethod_GetAllGauges<BaseClass>::StreamedGetAllGauges, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithSplitStreamingMethod_GetAllGauges() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetAllGauges(::grpc::ServerContext* context, const ::grpc::testing::EmptyMessage* request, ::grpc::ServerWriter< ::grpc::testing::GaugeResponse>* writer) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with split streamed
    virtual ::grpc::Status StreamedGetAllGauges(::grpc::ServerContext* context, ::grpc::ServerSplitStreamer< ::grpc::testing::EmptyMessage,::grpc::testing::GaugeResponse>* server_split_streamer) = 0;
  };
  typedef WithSplitStreamingMethod_GetAllGauges<Service > SplitStreamedService;
  typedef WithSplitStreamingMethod_GetAllGauges<WithStreamedUnaryMethod_GetGauge<Service > > StreamedService;
};

}  // namespace testing
}  // namespace grpc


#endif  // GRPC_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto__INCLUDED
