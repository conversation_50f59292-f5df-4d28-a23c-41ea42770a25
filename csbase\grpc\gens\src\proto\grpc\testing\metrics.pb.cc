// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: src/proto/grpc/testing/metrics.proto

#include "src/proto/grpc/testing/metrics.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)
namespace grpc {
namespace testing {
class GaugeResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<GaugeResponse>
      _instance;
  ::google::protobuf::int64 long_value_;
  double double_value_;
  ::google::protobuf::internal::ArenaStringPtr string_value_;
} _GaugeResponse_default_instance_;
class GaugeRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<GaugeRequest>
      _instance;
} _GaugeRequest_default_instance_;
class EmptyMessageDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<EmptyMessage>
      _instance;
} _EmptyMessage_default_instance_;
}  // namespace testing
}  // namespace grpc
namespace protobuf_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto {
void InitDefaultsGaugeResponseImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::testing::_GaugeResponse_default_instance_;
    new (ptr) ::grpc::testing::GaugeResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::GaugeResponse::InitAsDefaultInstance();
}

void InitDefaultsGaugeResponse() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsGaugeResponseImpl);
}

void InitDefaultsGaugeRequestImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::testing::_GaugeRequest_default_instance_;
    new (ptr) ::grpc::testing::GaugeRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::GaugeRequest::InitAsDefaultInstance();
}

void InitDefaultsGaugeRequest() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsGaugeRequestImpl);
}

void InitDefaultsEmptyMessageImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::testing::_EmptyMessage_default_instance_;
    new (ptr) ::grpc::testing::EmptyMessage();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::EmptyMessage::InitAsDefaultInstance();
}

void InitDefaultsEmptyMessage() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsEmptyMessageImpl);
}

::google::protobuf::Metadata file_level_metadata[3];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::GaugeResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::GaugeResponse, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::GaugeResponse, name_),
  offsetof(::grpc::testing::GaugeResponseDefaultTypeInternal, long_value_),
  offsetof(::grpc::testing::GaugeResponseDefaultTypeInternal, double_value_),
  offsetof(::grpc::testing::GaugeResponseDefaultTypeInternal, string_value_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::GaugeResponse, value_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::GaugeRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::GaugeRequest, name_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::EmptyMessage, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::grpc::testing::GaugeResponse)},
  { 10, -1, sizeof(::grpc::testing::GaugeRequest)},
  { 16, -1, sizeof(::grpc::testing::EmptyMessage)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_GaugeResponse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_GaugeRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_EmptyMessage_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  ::google::protobuf::MessageFactory* factory = NULL;
  AssignDescriptors(
      "src/proto/grpc/testing/metrics.proto", schemas, file_default_instances, TableStruct::offsets, factory,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 3);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n$src/proto/grpc/testing/metrics.proto\022\014"
      "grpc.testing\"l\n\rGaugeResponse\022\014\n\004name\030\001 "
      "\001(\t\022\024\n\nlong_value\030\002 \001(\003H\000\022\026\n\014double_valu"
      "e\030\003 \001(\001H\000\022\026\n\014string_value\030\004 \001(\tH\000B\007\n\005val"
      "ue\"\034\n\014GaugeRequest\022\014\n\004name\030\001 \001(\t\"\016\n\014Empt"
      "yMessage2\240\001\n\016MetricsService\022I\n\014GetAllGau"
      "ges\022\032.grpc.testing.EmptyMessage\032\033.grpc.t"
      "esting.GaugeResponse0\001\022C\n\010GetGauge\022\032.grp"
      "c.testing.GaugeRequest\032\033.grpc.testing.Ga"
      "ugeResponseb\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 379);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "src/proto/grpc/testing/metrics.proto", &protobuf_RegisterTypes);
}

void AddDescriptors() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto
namespace grpc {
namespace testing {

// ===================================================================

void GaugeResponse::InitAsDefaultInstance() {
  ::grpc::testing::_GaugeResponse_default_instance_.long_value_ = GOOGLE_LONGLONG(0);
  ::grpc::testing::_GaugeResponse_default_instance_.double_value_ = 0;
  ::grpc::testing::_GaugeResponse_default_instance_.string_value_.UnsafeSetDefault(
      &::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GaugeResponse::kNameFieldNumber;
const int GaugeResponse::kLongValueFieldNumber;
const int GaugeResponse::kDoubleValueFieldNumber;
const int GaugeResponse::kStringValueFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GaugeResponse::GaugeResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto::InitDefaultsGaugeResponse();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.GaugeResponse)
}
GaugeResponse::GaugeResponse(const GaugeResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  clear_has_value();
  switch (from.value_case()) {
    case kLongValue: {
      set_long_value(from.long_value());
      break;
    }
    case kDoubleValue: {
      set_double_value(from.double_value());
      break;
    }
    case kStringValue: {
      set_string_value(from.string_value());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:grpc.testing.GaugeResponse)
}

void GaugeResponse::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_has_value();
  _cached_size_ = 0;
}

GaugeResponse::~GaugeResponse() {
  // @@protoc_insertion_point(destructor:grpc.testing.GaugeResponse)
  SharedDtor();
}

void GaugeResponse::SharedDtor() {
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (has_value()) {
    clear_value();
  }
}

void GaugeResponse::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GaugeResponse::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const GaugeResponse& GaugeResponse::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto::InitDefaultsGaugeResponse();
  return *internal_default_instance();
}

GaugeResponse* GaugeResponse::New(::google::protobuf::Arena* arena) const {
  GaugeResponse* n = new GaugeResponse;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void GaugeResponse::clear_value() {
// @@protoc_insertion_point(one_of_clear_start:grpc.testing.GaugeResponse)
  switch (value_case()) {
    case kLongValue: {
      // No need to clear
      break;
    }
    case kDoubleValue: {
      // No need to clear
      break;
    }
    case kStringValue: {
      value_.string_value_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = VALUE_NOT_SET;
}


void GaugeResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.GaugeResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  clear_value();
  _internal_metadata_.Clear();
}

bool GaugeResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.GaugeResponse)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.testing.GaugeResponse.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 long_value = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {
          clear_value();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &value_.long_value_)));
          set_has_long_value();
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double double_value = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(25u /* 25 & 0xFF */)) {
          clear_value();
          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &value_.double_value_)));
          set_has_double_value();
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string string_value = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(34u /* 34 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_string_value()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->string_value().data(), static_cast<int>(this->string_value().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.testing.GaugeResponse.string_value"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.GaugeResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.GaugeResponse)
  return false;
#undef DO_
}

void GaugeResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.GaugeResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.GaugeResponse.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  // int64 long_value = 2;
  if (has_long_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->long_value(), output);
  }

  // double double_value = 3;
  if (has_double_value()) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(3, this->double_value(), output);
  }

  // string string_value = 4;
  if (has_string_value()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->string_value().data(), static_cast<int>(this->string_value().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.GaugeResponse.string_value");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      4, this->string_value(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.GaugeResponse)
}

::google::protobuf::uint8* GaugeResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.GaugeResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.GaugeResponse.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  // int64 long_value = 2;
  if (has_long_value()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->long_value(), target);
  }

  // double double_value = 3;
  if (has_double_value()) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(3, this->double_value(), target);
  }

  // string string_value = 4;
  if (has_string_value()) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->string_value().data(), static_cast<int>(this->string_value().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.GaugeResponse.string_value");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        4, this->string_value(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.GaugeResponse)
  return target;
}

size_t GaugeResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.GaugeResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  switch (value_case()) {
    // int64 long_value = 2;
    case kLongValue: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int64Size(
          this->long_value());
      break;
    }
    // double double_value = 3;
    case kDoubleValue: {
      total_size += 1 + 8;
      break;
    }
    // string string_value = 4;
    case kStringValue: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::StringSize(
          this->string_value());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GaugeResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.GaugeResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const GaugeResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GaugeResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.GaugeResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.GaugeResponse)
    MergeFrom(*source);
  }
}

void GaugeResponse::MergeFrom(const GaugeResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.GaugeResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.name().size() > 0) {

    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  switch (from.value_case()) {
    case kLongValue: {
      set_long_value(from.long_value());
      break;
    }
    case kDoubleValue: {
      set_double_value(from.double_value());
      break;
    }
    case kStringValue: {
      set_string_value(from.string_value());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
}

void GaugeResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.GaugeResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GaugeResponse::CopyFrom(const GaugeResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.GaugeResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GaugeResponse::IsInitialized() const {
  return true;
}

void GaugeResponse::Swap(GaugeResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void GaugeResponse::InternalSwap(GaugeResponse* other) {
  using std::swap;
  name_.Swap(&other->name_);
  swap(value_, other->value_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata GaugeResponse::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void GaugeRequest::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int GaugeRequest::kNameFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

GaugeRequest::GaugeRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto::InitDefaultsGaugeRequest();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.GaugeRequest)
}
GaugeRequest::GaugeRequest(const GaugeRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  // @@protoc_insertion_point(copy_constructor:grpc.testing.GaugeRequest)
}

void GaugeRequest::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

GaugeRequest::~GaugeRequest() {
  // @@protoc_insertion_point(destructor:grpc.testing.GaugeRequest)
  SharedDtor();
}

void GaugeRequest::SharedDtor() {
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void GaugeRequest::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* GaugeRequest::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const GaugeRequest& GaugeRequest::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto::InitDefaultsGaugeRequest();
  return *internal_default_instance();
}

GaugeRequest* GaugeRequest::New(::google::protobuf::Arena* arena) const {
  GaugeRequest* n = new GaugeRequest;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void GaugeRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.GaugeRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _internal_metadata_.Clear();
}

bool GaugeRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.GaugeRequest)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.testing.GaugeRequest.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.GaugeRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.GaugeRequest)
  return false;
#undef DO_
}

void GaugeRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.GaugeRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.GaugeRequest.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.GaugeRequest)
}

::google::protobuf::uint8* GaugeRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.GaugeRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.testing.GaugeRequest.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.GaugeRequest)
  return target;
}

size_t GaugeRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.GaugeRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void GaugeRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.GaugeRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const GaugeRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const GaugeRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.GaugeRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.GaugeRequest)
    MergeFrom(*source);
  }
}

void GaugeRequest::MergeFrom(const GaugeRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.GaugeRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.name().size() > 0) {

    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
}

void GaugeRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.GaugeRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void GaugeRequest::CopyFrom(const GaugeRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.GaugeRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GaugeRequest::IsInitialized() const {
  return true;
}

void GaugeRequest::Swap(GaugeRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void GaugeRequest::InternalSwap(GaugeRequest* other) {
  using std::swap;
  name_.Swap(&other->name_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata GaugeRequest::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void EmptyMessage::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

EmptyMessage::EmptyMessage()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto::InitDefaultsEmptyMessage();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.EmptyMessage)
}
EmptyMessage::EmptyMessage(const EmptyMessage& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:grpc.testing.EmptyMessage)
}

void EmptyMessage::SharedCtor() {
  _cached_size_ = 0;
}

EmptyMessage::~EmptyMessage() {
  // @@protoc_insertion_point(destructor:grpc.testing.EmptyMessage)
  SharedDtor();
}

void EmptyMessage::SharedDtor() {
}

void EmptyMessage::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* EmptyMessage::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const EmptyMessage& EmptyMessage::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto::InitDefaultsEmptyMessage();
  return *internal_default_instance();
}

EmptyMessage* EmptyMessage::New(::google::protobuf::Arena* arena) const {
  EmptyMessage* n = new EmptyMessage;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void EmptyMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.EmptyMessage)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_metadata_.Clear();
}

bool EmptyMessage::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.EmptyMessage)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, _internal_metadata_.mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.EmptyMessage)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.EmptyMessage)
  return false;
#undef DO_
}

void EmptyMessage::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.EmptyMessage)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.EmptyMessage)
}

::google::protobuf::uint8* EmptyMessage::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.EmptyMessage)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.EmptyMessage)
  return target;
}

size_t EmptyMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.EmptyMessage)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void EmptyMessage::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.EmptyMessage)
  GOOGLE_DCHECK_NE(&from, this);
  const EmptyMessage* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const EmptyMessage>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.EmptyMessage)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.EmptyMessage)
    MergeFrom(*source);
  }
}

void EmptyMessage::MergeFrom(const EmptyMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.EmptyMessage)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

}

void EmptyMessage::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.EmptyMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void EmptyMessage::CopyFrom(const EmptyMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.EmptyMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EmptyMessage::IsInitialized() const {
  return true;
}

void EmptyMessage::Swap(EmptyMessage* other) {
  if (other == this) return;
  InternalSwap(other);
}
void EmptyMessage::InternalSwap(EmptyMessage* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata EmptyMessage::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace testing
}  // namespace grpc

// @@protoc_insertion_point(global_scope)
