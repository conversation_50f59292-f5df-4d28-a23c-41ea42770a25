// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: src/proto/grpc/testing/metrics.proto

#ifndef PROTOBUF_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto__INCLUDED
#define PROTOBUF_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3005001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace protobuf_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[3];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
void InitDefaultsGaugeResponseImpl();
void InitDefaultsGaugeResponse();
void InitDefaultsGaugeRequestImpl();
void InitDefaultsGaugeRequest();
void InitDefaultsEmptyMessageImpl();
void InitDefaultsEmptyMessage();
inline void InitDefaults() {
  InitDefaultsGaugeResponse();
  InitDefaultsGaugeRequest();
  InitDefaultsEmptyMessage();
}
}  // namespace protobuf_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto
namespace grpc {
namespace testing {
class EmptyMessage;
class EmptyMessageDefaultTypeInternal;
extern EmptyMessageDefaultTypeInternal _EmptyMessage_default_instance_;
class GaugeRequest;
class GaugeRequestDefaultTypeInternal;
extern GaugeRequestDefaultTypeInternal _GaugeRequest_default_instance_;
class GaugeResponse;
class GaugeResponseDefaultTypeInternal;
extern GaugeResponseDefaultTypeInternal _GaugeResponse_default_instance_;
}  // namespace testing
}  // namespace grpc
namespace grpc {
namespace testing {

// ===================================================================

class GaugeResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.GaugeResponse) */ {
 public:
  GaugeResponse();
  virtual ~GaugeResponse();

  GaugeResponse(const GaugeResponse& from);

  inline GaugeResponse& operator=(const GaugeResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  GaugeResponse(GaugeResponse&& from) noexcept
    : GaugeResponse() {
    *this = ::std::move(from);
  }

  inline GaugeResponse& operator=(GaugeResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const GaugeResponse& default_instance();

  enum ValueCase {
    kLongValue = 2,
    kDoubleValue = 3,
    kStringValue = 4,
    VALUE_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GaugeResponse* internal_default_instance() {
    return reinterpret_cast<const GaugeResponse*>(
               &_GaugeResponse_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    0;

  void Swap(GaugeResponse* other);
  friend void swap(GaugeResponse& a, GaugeResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline GaugeResponse* New() const PROTOBUF_FINAL { return New(NULL); }

  GaugeResponse* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const GaugeResponse& from);
  void MergeFrom(const GaugeResponse& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(GaugeResponse* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // int64 long_value = 2;
  private:
  bool has_long_value() const;
  public:
  void clear_long_value();
  static const int kLongValueFieldNumber = 2;
  ::google::protobuf::int64 long_value() const;
  void set_long_value(::google::protobuf::int64 value);

  // double double_value = 3;
  private:
  bool has_double_value() const;
  public:
  void clear_double_value();
  static const int kDoubleValueFieldNumber = 3;
  double double_value() const;
  void set_double_value(double value);

  // string string_value = 4;
  private:
  bool has_string_value() const;
  public:
  void clear_string_value();
  static const int kStringValueFieldNumber = 4;
  const ::std::string& string_value() const;
  void set_string_value(const ::std::string& value);
  #if LANG_CXX11
  void set_string_value(::std::string&& value);
  #endif
  void set_string_value(const char* value);
  void set_string_value(const char* value, size_t size);
  ::std::string* mutable_string_value();
  ::std::string* release_string_value();
  void set_allocated_string_value(::std::string* string_value);

  ValueCase value_case() const;
  // @@protoc_insertion_point(class_scope:grpc.testing.GaugeResponse)
 private:
  void set_has_long_value();
  void set_has_double_value();
  void set_has_string_value();

  inline bool has_value() const;
  void clear_value();
  inline void clear_has_value();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  union ValueUnion {
    ValueUnion() {}
    ::google::protobuf::int64 long_value_;
    double double_value_;
    ::google::protobuf::internal::ArenaStringPtr string_value_;
  } value_;
  mutable int _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto::InitDefaultsGaugeResponseImpl();
};
// -------------------------------------------------------------------

class GaugeRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.GaugeRequest) */ {
 public:
  GaugeRequest();
  virtual ~GaugeRequest();

  GaugeRequest(const GaugeRequest& from);

  inline GaugeRequest& operator=(const GaugeRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  GaugeRequest(GaugeRequest&& from) noexcept
    : GaugeRequest() {
    *this = ::std::move(from);
  }

  inline GaugeRequest& operator=(GaugeRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const GaugeRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const GaugeRequest* internal_default_instance() {
    return reinterpret_cast<const GaugeRequest*>(
               &_GaugeRequest_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    1;

  void Swap(GaugeRequest* other);
  friend void swap(GaugeRequest& a, GaugeRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline GaugeRequest* New() const PROTOBUF_FINAL { return New(NULL); }

  GaugeRequest* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const GaugeRequest& from);
  void MergeFrom(const GaugeRequest& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(GaugeRequest* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // @@protoc_insertion_point(class_scope:grpc.testing.GaugeRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto::InitDefaultsGaugeRequestImpl();
};
// -------------------------------------------------------------------

class EmptyMessage : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.EmptyMessage) */ {
 public:
  EmptyMessage();
  virtual ~EmptyMessage();

  EmptyMessage(const EmptyMessage& from);

  inline EmptyMessage& operator=(const EmptyMessage& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  EmptyMessage(EmptyMessage&& from) noexcept
    : EmptyMessage() {
    *this = ::std::move(from);
  }

  inline EmptyMessage& operator=(EmptyMessage&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const EmptyMessage& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const EmptyMessage* internal_default_instance() {
    return reinterpret_cast<const EmptyMessage*>(
               &_EmptyMessage_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    2;

  void Swap(EmptyMessage* other);
  friend void swap(EmptyMessage& a, EmptyMessage& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline EmptyMessage* New() const PROTOBUF_FINAL { return New(NULL); }

  EmptyMessage* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const EmptyMessage& from);
  void MergeFrom(const EmptyMessage& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(EmptyMessage* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:grpc.testing.EmptyMessage)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto::InitDefaultsEmptyMessageImpl();
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GaugeResponse

// string name = 1;
inline void GaugeResponse::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& GaugeResponse::name() const {
  // @@protoc_insertion_point(field_get:grpc.testing.GaugeResponse.name)
  return name_.GetNoArena();
}
inline void GaugeResponse::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.testing.GaugeResponse.name)
}
#if LANG_CXX11
inline void GaugeResponse::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.testing.GaugeResponse.name)
}
#endif
inline void GaugeResponse::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.testing.GaugeResponse.name)
}
inline void GaugeResponse::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.testing.GaugeResponse.name)
}
inline ::std::string* GaugeResponse::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:grpc.testing.GaugeResponse.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* GaugeResponse::release_name() {
  // @@protoc_insertion_point(field_release:grpc.testing.GaugeResponse.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void GaugeResponse::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.GaugeResponse.name)
}

// int64 long_value = 2;
inline bool GaugeResponse::has_long_value() const {
  return value_case() == kLongValue;
}
inline void GaugeResponse::set_has_long_value() {
  _oneof_case_[0] = kLongValue;
}
inline void GaugeResponse::clear_long_value() {
  if (has_long_value()) {
    value_.long_value_ = GOOGLE_LONGLONG(0);
    clear_has_value();
  }
}
inline ::google::protobuf::int64 GaugeResponse::long_value() const {
  // @@protoc_insertion_point(field_get:grpc.testing.GaugeResponse.long_value)
  if (has_long_value()) {
    return value_.long_value_;
  }
  return GOOGLE_LONGLONG(0);
}
inline void GaugeResponse::set_long_value(::google::protobuf::int64 value) {
  if (!has_long_value()) {
    clear_value();
    set_has_long_value();
  }
  value_.long_value_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.GaugeResponse.long_value)
}

// double double_value = 3;
inline bool GaugeResponse::has_double_value() const {
  return value_case() == kDoubleValue;
}
inline void GaugeResponse::set_has_double_value() {
  _oneof_case_[0] = kDoubleValue;
}
inline void GaugeResponse::clear_double_value() {
  if (has_double_value()) {
    value_.double_value_ = 0;
    clear_has_value();
  }
}
inline double GaugeResponse::double_value() const {
  // @@protoc_insertion_point(field_get:grpc.testing.GaugeResponse.double_value)
  if (has_double_value()) {
    return value_.double_value_;
  }
  return 0;
}
inline void GaugeResponse::set_double_value(double value) {
  if (!has_double_value()) {
    clear_value();
    set_has_double_value();
  }
  value_.double_value_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.GaugeResponse.double_value)
}

// string string_value = 4;
inline bool GaugeResponse::has_string_value() const {
  return value_case() == kStringValue;
}
inline void GaugeResponse::set_has_string_value() {
  _oneof_case_[0] = kStringValue;
}
inline void GaugeResponse::clear_string_value() {
  if (has_string_value()) {
    value_.string_value_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
    clear_has_value();
  }
}
inline const ::std::string& GaugeResponse::string_value() const {
  // @@protoc_insertion_point(field_get:grpc.testing.GaugeResponse.string_value)
  if (has_string_value()) {
    return value_.string_value_.GetNoArena();
  }
  return *&::google::protobuf::internal::GetEmptyStringAlreadyInited();
}
inline void GaugeResponse::set_string_value(const ::std::string& value) {
  // @@protoc_insertion_point(field_set:grpc.testing.GaugeResponse.string_value)
  if (!has_string_value()) {
    clear_value();
    set_has_string_value();
    value_.string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  value_.string_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.testing.GaugeResponse.string_value)
}
#if LANG_CXX11
inline void GaugeResponse::set_string_value(::std::string&& value) {
  // @@protoc_insertion_point(field_set:grpc.testing.GaugeResponse.string_value)
  if (!has_string_value()) {
    clear_value();
    set_has_string_value();
    value_.string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  value_.string_value_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.testing.GaugeResponse.string_value)
}
#endif
inline void GaugeResponse::set_string_value(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  if (!has_string_value()) {
    clear_value();
    set_has_string_value();
    value_.string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  value_.string_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.testing.GaugeResponse.string_value)
}
inline void GaugeResponse::set_string_value(const char* value, size_t size) {
  if (!has_string_value()) {
    clear_value();
    set_has_string_value();
    value_.string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  value_.string_value_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(
      reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.testing.GaugeResponse.string_value)
}
inline ::std::string* GaugeResponse::mutable_string_value() {
  if (!has_string_value()) {
    clear_value();
    set_has_string_value();
    value_.string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.GaugeResponse.string_value)
  return value_.string_value_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* GaugeResponse::release_string_value() {
  // @@protoc_insertion_point(field_release:grpc.testing.GaugeResponse.string_value)
  if (has_string_value()) {
    clear_has_value();
    return value_.string_value_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  } else {
    return NULL;
  }
}
inline void GaugeResponse::set_allocated_string_value(::std::string* string_value) {
  if (!has_string_value()) {
    value_.string_value_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  }
  clear_value();
  if (string_value != NULL) {
    set_has_string_value();
    value_.string_value_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
        string_value);
  }
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.GaugeResponse.string_value)
}

inline bool GaugeResponse::has_value() const {
  return value_case() != VALUE_NOT_SET;
}
inline void GaugeResponse::clear_has_value() {
  _oneof_case_[0] = VALUE_NOT_SET;
}
inline GaugeResponse::ValueCase GaugeResponse::value_case() const {
  return GaugeResponse::ValueCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// GaugeRequest

// string name = 1;
inline void GaugeRequest::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& GaugeRequest::name() const {
  // @@protoc_insertion_point(field_get:grpc.testing.GaugeRequest.name)
  return name_.GetNoArena();
}
inline void GaugeRequest::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.testing.GaugeRequest.name)
}
#if LANG_CXX11
inline void GaugeRequest::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.testing.GaugeRequest.name)
}
#endif
inline void GaugeRequest::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.testing.GaugeRequest.name)
}
inline void GaugeRequest::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.testing.GaugeRequest.name)
}
inline ::std::string* GaugeRequest::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:grpc.testing.GaugeRequest.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* GaugeRequest::release_name() {
  // @@protoc_insertion_point(field_release:grpc.testing.GaugeRequest.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void GaugeRequest::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.GaugeRequest.name)
}

// -------------------------------------------------------------------

// EmptyMessage

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace testing
}  // namespace grpc

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_src_2fproto_2fgrpc_2ftesting_2fmetrics_2eproto__INCLUDED
