// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: src/proto/grpc/testing/metrics.proto

#include "src/proto/grpc/testing/metrics.pb.h"
#include "src/proto/grpc/testing/metrics.grpc.pb.h"

#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/sync_stream.h>
#include <gmock/gmock.h>
namespace grpc {
namespace testing {

class MockMetricsServiceStub : public MetricsService::StubInterface {
 public:
  MOCK_METHOD2(GetAllGaugesRaw, ::grpc::ClientReaderInterface< ::grpc::testing::GaugeResponse>*(::grpc::ClientContext* context, const ::grpc::testing::EmptyMessage& request));
  MOCK_METHOD4(AsyncGetAllGaugesRaw, ::grpc::ClientAsyncReaderInterface< ::grpc::testing::GaugeResponse>*(::grpc::ClientContext* context, const ::grpc::testing::EmptyMessage& request, ::grpc::CompletionQueue* cq, void* tag));
  MOCK_METHOD3(PrepareAsyncGetAllGaugesRaw, ::grpc::ClientAsyncReaderInterface< ::grpc::testing::GaugeResponse>*(::grpc::ClientContext* context, const ::grpc::testing::EmptyMessage& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(GetGauge, ::grpc::Status(::grpc::ClientContext* context, const ::grpc::testing::GaugeRequest& request, ::grpc::testing::GaugeResponse* response));
  MOCK_METHOD3(AsyncGetGaugeRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::GaugeResponse>*(::grpc::ClientContext* context, const ::grpc::testing::GaugeRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncGetGaugeRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::GaugeResponse>*(::grpc::ClientContext* context, const ::grpc::testing::GaugeRequest& request, ::grpc::CompletionQueue* cq));
};

} // namespace grpc
} // namespace testing

