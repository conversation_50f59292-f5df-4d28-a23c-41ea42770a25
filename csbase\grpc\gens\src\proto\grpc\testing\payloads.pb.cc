// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: src/proto/grpc/testing/payloads.proto

#include "src/proto/grpc/testing/payloads.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)
namespace grpc {
namespace testing {
class ByteBufferParamsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ByteBufferParams>
      _instance;
} _ByteBufferParams_default_instance_;
class SimpleProtoParamsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<SimpleProtoParams>
      _instance;
} _SimpleProtoParams_default_instance_;
class ComplexProtoParamsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ComplexProtoParams>
      _instance;
} _ComplexProtoParams_default_instance_;
class PayloadConfigDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<PayloadConfig>
      _instance;
  const ::grpc::testing::ByteBufferParams* bytebuf_params_;
  const ::grpc::testing::SimpleProtoParams* simple_params_;
  const ::grpc::testing::ComplexProtoParams* complex_params_;
} _PayloadConfig_default_instance_;
}  // namespace testing
}  // namespace grpc
namespace protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto {
void InitDefaultsByteBufferParamsImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::testing::_ByteBufferParams_default_instance_;
    new (ptr) ::grpc::testing::ByteBufferParams();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::ByteBufferParams::InitAsDefaultInstance();
}

void InitDefaultsByteBufferParams() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsByteBufferParamsImpl);
}

void InitDefaultsSimpleProtoParamsImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::testing::_SimpleProtoParams_default_instance_;
    new (ptr) ::grpc::testing::SimpleProtoParams();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::SimpleProtoParams::InitAsDefaultInstance();
}

void InitDefaultsSimpleProtoParams() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsSimpleProtoParamsImpl);
}

void InitDefaultsComplexProtoParamsImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::testing::_ComplexProtoParams_default_instance_;
    new (ptr) ::grpc::testing::ComplexProtoParams();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::ComplexProtoParams::InitAsDefaultInstance();
}

void InitDefaultsComplexProtoParams() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsComplexProtoParamsImpl);
}

void InitDefaultsPayloadConfigImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::InitDefaultsByteBufferParams();
  protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::InitDefaultsSimpleProtoParams();
  protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::InitDefaultsComplexProtoParams();
  {
    void* ptr = &::grpc::testing::_PayloadConfig_default_instance_;
    new (ptr) ::grpc::testing::PayloadConfig();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::PayloadConfig::InitAsDefaultInstance();
}

void InitDefaultsPayloadConfig() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsPayloadConfigImpl);
}

::google::protobuf::Metadata file_level_metadata[4];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ByteBufferParams, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ByteBufferParams, req_size_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ByteBufferParams, resp_size_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::SimpleProtoParams, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::SimpleProtoParams, req_size_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::SimpleProtoParams, resp_size_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ComplexProtoParams, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::PayloadConfig, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::PayloadConfig, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  offsetof(::grpc::testing::PayloadConfigDefaultTypeInternal, bytebuf_params_),
  offsetof(::grpc::testing::PayloadConfigDefaultTypeInternal, simple_params_),
  offsetof(::grpc::testing::PayloadConfigDefaultTypeInternal, complex_params_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::PayloadConfig, payload_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::grpc::testing::ByteBufferParams)},
  { 7, -1, sizeof(::grpc::testing::SimpleProtoParams)},
  { 14, -1, sizeof(::grpc::testing::ComplexProtoParams)},
  { 19, -1, sizeof(::grpc::testing::PayloadConfig)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_ByteBufferParams_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_SimpleProtoParams_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_ComplexProtoParams_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_PayloadConfig_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  ::google::protobuf::MessageFactory* factory = NULL;
  AssignDescriptors(
      "src/proto/grpc/testing/payloads.proto", schemas, file_default_instances, TableStruct::offsets, factory,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 4);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n%src/proto/grpc/testing/payloads.proto\022"
      "\014grpc.testing\"7\n\020ByteBufferParams\022\020\n\010req"
      "_size\030\001 \001(\005\022\021\n\tresp_size\030\002 \001(\005\"8\n\021Simple"
      "ProtoParams\022\020\n\010req_size\030\001 \001(\005\022\021\n\tresp_si"
      "ze\030\002 \001(\005\"\024\n\022ComplexProtoParams\"\312\001\n\rPaylo"
      "adConfig\0228\n\016bytebuf_params\030\001 \001(\0132\036.grpc."
      "testing.ByteBufferParamsH\000\0228\n\rsimple_par"
      "ams\030\002 \001(\0132\037.grpc.testing.SimpleProtoPara"
      "msH\000\022:\n\016complex_params\030\003 \001(\0132 .grpc.test"
      "ing.ComplexProtoParamsH\000B\t\n\007payloadb\006pro"
      "to3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 403);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "src/proto/grpc/testing/payloads.proto", &protobuf_RegisterTypes);
}

void AddDescriptors() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto
namespace grpc {
namespace testing {

// ===================================================================

void ByteBufferParams::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ByteBufferParams::kReqSizeFieldNumber;
const int ByteBufferParams::kRespSizeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ByteBufferParams::ByteBufferParams()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::InitDefaultsByteBufferParams();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.ByteBufferParams)
}
ByteBufferParams::ByteBufferParams(const ByteBufferParams& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&req_size_, &from.req_size_,
    static_cast<size_t>(reinterpret_cast<char*>(&resp_size_) -
    reinterpret_cast<char*>(&req_size_)) + sizeof(resp_size_));
  // @@protoc_insertion_point(copy_constructor:grpc.testing.ByteBufferParams)
}

void ByteBufferParams::SharedCtor() {
  ::memset(&req_size_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&resp_size_) -
      reinterpret_cast<char*>(&req_size_)) + sizeof(resp_size_));
  _cached_size_ = 0;
}

ByteBufferParams::~ByteBufferParams() {
  // @@protoc_insertion_point(destructor:grpc.testing.ByteBufferParams)
  SharedDtor();
}

void ByteBufferParams::SharedDtor() {
}

void ByteBufferParams::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ByteBufferParams::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ByteBufferParams& ByteBufferParams::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::InitDefaultsByteBufferParams();
  return *internal_default_instance();
}

ByteBufferParams* ByteBufferParams::New(::google::protobuf::Arena* arena) const {
  ByteBufferParams* n = new ByteBufferParams;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ByteBufferParams::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.ByteBufferParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&req_size_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&resp_size_) -
      reinterpret_cast<char*>(&req_size_)) + sizeof(resp_size_));
  _internal_metadata_.Clear();
}

bool ByteBufferParams::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.ByteBufferParams)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 req_size = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &req_size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 resp_size = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &resp_size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.ByteBufferParams)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.ByteBufferParams)
  return false;
#undef DO_
}

void ByteBufferParams::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.ByteBufferParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 req_size = 1;
  if (this->req_size() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->req_size(), output);
  }

  // int32 resp_size = 2;
  if (this->resp_size() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->resp_size(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.ByteBufferParams)
}

::google::protobuf::uint8* ByteBufferParams::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.ByteBufferParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 req_size = 1;
  if (this->req_size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->req_size(), target);
  }

  // int32 resp_size = 2;
  if (this->resp_size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->resp_size(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.ByteBufferParams)
  return target;
}

size_t ByteBufferParams::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.ByteBufferParams)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int32 req_size = 1;
  if (this->req_size() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->req_size());
  }

  // int32 resp_size = 2;
  if (this->resp_size() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->resp_size());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ByteBufferParams::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.ByteBufferParams)
  GOOGLE_DCHECK_NE(&from, this);
  const ByteBufferParams* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ByteBufferParams>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.ByteBufferParams)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.ByteBufferParams)
    MergeFrom(*source);
  }
}

void ByteBufferParams::MergeFrom(const ByteBufferParams& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.ByteBufferParams)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.req_size() != 0) {
    set_req_size(from.req_size());
  }
  if (from.resp_size() != 0) {
    set_resp_size(from.resp_size());
  }
}

void ByteBufferParams::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.ByteBufferParams)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ByteBufferParams::CopyFrom(const ByteBufferParams& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.ByteBufferParams)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ByteBufferParams::IsInitialized() const {
  return true;
}

void ByteBufferParams::Swap(ByteBufferParams* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ByteBufferParams::InternalSwap(ByteBufferParams* other) {
  using std::swap;
  swap(req_size_, other->req_size_);
  swap(resp_size_, other->resp_size_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ByteBufferParams::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void SimpleProtoParams::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int SimpleProtoParams::kReqSizeFieldNumber;
const int SimpleProtoParams::kRespSizeFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

SimpleProtoParams::SimpleProtoParams()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::InitDefaultsSimpleProtoParams();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.SimpleProtoParams)
}
SimpleProtoParams::SimpleProtoParams(const SimpleProtoParams& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&req_size_, &from.req_size_,
    static_cast<size_t>(reinterpret_cast<char*>(&resp_size_) -
    reinterpret_cast<char*>(&req_size_)) + sizeof(resp_size_));
  // @@protoc_insertion_point(copy_constructor:grpc.testing.SimpleProtoParams)
}

void SimpleProtoParams::SharedCtor() {
  ::memset(&req_size_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&resp_size_) -
      reinterpret_cast<char*>(&req_size_)) + sizeof(resp_size_));
  _cached_size_ = 0;
}

SimpleProtoParams::~SimpleProtoParams() {
  // @@protoc_insertion_point(destructor:grpc.testing.SimpleProtoParams)
  SharedDtor();
}

void SimpleProtoParams::SharedDtor() {
}

void SimpleProtoParams::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* SimpleProtoParams::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const SimpleProtoParams& SimpleProtoParams::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::InitDefaultsSimpleProtoParams();
  return *internal_default_instance();
}

SimpleProtoParams* SimpleProtoParams::New(::google::protobuf::Arena* arena) const {
  SimpleProtoParams* n = new SimpleProtoParams;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void SimpleProtoParams::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.SimpleProtoParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&req_size_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&resp_size_) -
      reinterpret_cast<char*>(&req_size_)) + sizeof(resp_size_));
  _internal_metadata_.Clear();
}

bool SimpleProtoParams::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.SimpleProtoParams)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 req_size = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &req_size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 resp_size = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &resp_size_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.SimpleProtoParams)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.SimpleProtoParams)
  return false;
#undef DO_
}

void SimpleProtoParams::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.SimpleProtoParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 req_size = 1;
  if (this->req_size() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->req_size(), output);
  }

  // int32 resp_size = 2;
  if (this->resp_size() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->resp_size(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.SimpleProtoParams)
}

::google::protobuf::uint8* SimpleProtoParams::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.SimpleProtoParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 req_size = 1;
  if (this->req_size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->req_size(), target);
  }

  // int32 resp_size = 2;
  if (this->resp_size() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->resp_size(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.SimpleProtoParams)
  return target;
}

size_t SimpleProtoParams::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.SimpleProtoParams)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int32 req_size = 1;
  if (this->req_size() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->req_size());
  }

  // int32 resp_size = 2;
  if (this->resp_size() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->resp_size());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void SimpleProtoParams::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.SimpleProtoParams)
  GOOGLE_DCHECK_NE(&from, this);
  const SimpleProtoParams* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const SimpleProtoParams>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.SimpleProtoParams)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.SimpleProtoParams)
    MergeFrom(*source);
  }
}

void SimpleProtoParams::MergeFrom(const SimpleProtoParams& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.SimpleProtoParams)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.req_size() != 0) {
    set_req_size(from.req_size());
  }
  if (from.resp_size() != 0) {
    set_resp_size(from.resp_size());
  }
}

void SimpleProtoParams::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.SimpleProtoParams)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void SimpleProtoParams::CopyFrom(const SimpleProtoParams& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.SimpleProtoParams)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SimpleProtoParams::IsInitialized() const {
  return true;
}

void SimpleProtoParams::Swap(SimpleProtoParams* other) {
  if (other == this) return;
  InternalSwap(other);
}
void SimpleProtoParams::InternalSwap(SimpleProtoParams* other) {
  using std::swap;
  swap(req_size_, other->req_size_);
  swap(resp_size_, other->resp_size_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata SimpleProtoParams::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ComplexProtoParams::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ComplexProtoParams::ComplexProtoParams()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::InitDefaultsComplexProtoParams();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.ComplexProtoParams)
}
ComplexProtoParams::ComplexProtoParams(const ComplexProtoParams& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:grpc.testing.ComplexProtoParams)
}

void ComplexProtoParams::SharedCtor() {
  _cached_size_ = 0;
}

ComplexProtoParams::~ComplexProtoParams() {
  // @@protoc_insertion_point(destructor:grpc.testing.ComplexProtoParams)
  SharedDtor();
}

void ComplexProtoParams::SharedDtor() {
}

void ComplexProtoParams::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ComplexProtoParams::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ComplexProtoParams& ComplexProtoParams::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::InitDefaultsComplexProtoParams();
  return *internal_default_instance();
}

ComplexProtoParams* ComplexProtoParams::New(::google::protobuf::Arena* arena) const {
  ComplexProtoParams* n = new ComplexProtoParams;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ComplexProtoParams::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.ComplexProtoParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _internal_metadata_.Clear();
}

bool ComplexProtoParams::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.ComplexProtoParams)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
  handle_unusual:
    if (tag == 0) {
      goto success;
    }
    DO_(::google::protobuf::internal::WireFormat::SkipField(
          input, tag, _internal_metadata_.mutable_unknown_fields()));
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.ComplexProtoParams)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.ComplexProtoParams)
  return false;
#undef DO_
}

void ComplexProtoParams::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.ComplexProtoParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.ComplexProtoParams)
}

::google::protobuf::uint8* ComplexProtoParams::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.ComplexProtoParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.ComplexProtoParams)
  return target;
}

size_t ComplexProtoParams::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.ComplexProtoParams)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ComplexProtoParams::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.ComplexProtoParams)
  GOOGLE_DCHECK_NE(&from, this);
  const ComplexProtoParams* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ComplexProtoParams>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.ComplexProtoParams)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.ComplexProtoParams)
    MergeFrom(*source);
  }
}

void ComplexProtoParams::MergeFrom(const ComplexProtoParams& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.ComplexProtoParams)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

}

void ComplexProtoParams::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.ComplexProtoParams)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ComplexProtoParams::CopyFrom(const ComplexProtoParams& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.ComplexProtoParams)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ComplexProtoParams::IsInitialized() const {
  return true;
}

void ComplexProtoParams::Swap(ComplexProtoParams* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ComplexProtoParams::InternalSwap(ComplexProtoParams* other) {
  using std::swap;
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ComplexProtoParams::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void PayloadConfig::InitAsDefaultInstance() {
  ::grpc::testing::_PayloadConfig_default_instance_.bytebuf_params_ = const_cast< ::grpc::testing::ByteBufferParams*>(
      ::grpc::testing::ByteBufferParams::internal_default_instance());
  ::grpc::testing::_PayloadConfig_default_instance_.simple_params_ = const_cast< ::grpc::testing::SimpleProtoParams*>(
      ::grpc::testing::SimpleProtoParams::internal_default_instance());
  ::grpc::testing::_PayloadConfig_default_instance_.complex_params_ = const_cast< ::grpc::testing::ComplexProtoParams*>(
      ::grpc::testing::ComplexProtoParams::internal_default_instance());
}
void PayloadConfig::set_allocated_bytebuf_params(::grpc::testing::ByteBufferParams* bytebuf_params) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_payload();
  if (bytebuf_params) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      bytebuf_params = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, bytebuf_params, submessage_arena);
    }
    set_has_bytebuf_params();
    payload_.bytebuf_params_ = bytebuf_params;
  }
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.PayloadConfig.bytebuf_params)
}
void PayloadConfig::set_allocated_simple_params(::grpc::testing::SimpleProtoParams* simple_params) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_payload();
  if (simple_params) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      simple_params = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, simple_params, submessage_arena);
    }
    set_has_simple_params();
    payload_.simple_params_ = simple_params;
  }
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.PayloadConfig.simple_params)
}
void PayloadConfig::set_allocated_complex_params(::grpc::testing::ComplexProtoParams* complex_params) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_payload();
  if (complex_params) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      complex_params = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, complex_params, submessage_arena);
    }
    set_has_complex_params();
    payload_.complex_params_ = complex_params;
  }
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.PayloadConfig.complex_params)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int PayloadConfig::kBytebufParamsFieldNumber;
const int PayloadConfig::kSimpleParamsFieldNumber;
const int PayloadConfig::kComplexParamsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

PayloadConfig::PayloadConfig()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::InitDefaultsPayloadConfig();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.PayloadConfig)
}
PayloadConfig::PayloadConfig(const PayloadConfig& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  clear_has_payload();
  switch (from.payload_case()) {
    case kBytebufParams: {
      mutable_bytebuf_params()->::grpc::testing::ByteBufferParams::MergeFrom(from.bytebuf_params());
      break;
    }
    case kSimpleParams: {
      mutable_simple_params()->::grpc::testing::SimpleProtoParams::MergeFrom(from.simple_params());
      break;
    }
    case kComplexParams: {
      mutable_complex_params()->::grpc::testing::ComplexProtoParams::MergeFrom(from.complex_params());
      break;
    }
    case PAYLOAD_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:grpc.testing.PayloadConfig)
}

void PayloadConfig::SharedCtor() {
  clear_has_payload();
  _cached_size_ = 0;
}

PayloadConfig::~PayloadConfig() {
  // @@protoc_insertion_point(destructor:grpc.testing.PayloadConfig)
  SharedDtor();
}

void PayloadConfig::SharedDtor() {
  if (has_payload()) {
    clear_payload();
  }
}

void PayloadConfig::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* PayloadConfig::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const PayloadConfig& PayloadConfig::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::InitDefaultsPayloadConfig();
  return *internal_default_instance();
}

PayloadConfig* PayloadConfig::New(::google::protobuf::Arena* arena) const {
  PayloadConfig* n = new PayloadConfig;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void PayloadConfig::clear_payload() {
// @@protoc_insertion_point(one_of_clear_start:grpc.testing.PayloadConfig)
  switch (payload_case()) {
    case kBytebufParams: {
      delete payload_.bytebuf_params_;
      break;
    }
    case kSimpleParams: {
      delete payload_.simple_params_;
      break;
    }
    case kComplexParams: {
      delete payload_.complex_params_;
      break;
    }
    case PAYLOAD_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = PAYLOAD_NOT_SET;
}


void PayloadConfig::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.PayloadConfig)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_payload();
  _internal_metadata_.Clear();
}

bool PayloadConfig::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.PayloadConfig)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .grpc.testing.ByteBufferParams bytebuf_params = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_bytebuf_params()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.testing.SimpleProtoParams simple_params = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_simple_params()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.testing.ComplexProtoParams complex_params = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_complex_params()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.PayloadConfig)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.PayloadConfig)
  return false;
#undef DO_
}

void PayloadConfig::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.PayloadConfig)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.ByteBufferParams bytebuf_params = 1;
  if (has_bytebuf_params()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *payload_.bytebuf_params_, output);
  }

  // .grpc.testing.SimpleProtoParams simple_params = 2;
  if (has_simple_params()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *payload_.simple_params_, output);
  }

  // .grpc.testing.ComplexProtoParams complex_params = 3;
  if (has_complex_params()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      3, *payload_.complex_params_, output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.PayloadConfig)
}

::google::protobuf::uint8* PayloadConfig::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.PayloadConfig)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.ByteBufferParams bytebuf_params = 1;
  if (has_bytebuf_params()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, *payload_.bytebuf_params_, deterministic, target);
  }

  // .grpc.testing.SimpleProtoParams simple_params = 2;
  if (has_simple_params()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, *payload_.simple_params_, deterministic, target);
  }

  // .grpc.testing.ComplexProtoParams complex_params = 3;
  if (has_complex_params()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        3, *payload_.complex_params_, deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.PayloadConfig)
  return target;
}

size_t PayloadConfig::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.PayloadConfig)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  switch (payload_case()) {
    // .grpc.testing.ByteBufferParams bytebuf_params = 1;
    case kBytebufParams: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *payload_.bytebuf_params_);
      break;
    }
    // .grpc.testing.SimpleProtoParams simple_params = 2;
    case kSimpleParams: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *payload_.simple_params_);
      break;
    }
    // .grpc.testing.ComplexProtoParams complex_params = 3;
    case kComplexParams: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *payload_.complex_params_);
      break;
    }
    case PAYLOAD_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void PayloadConfig::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.PayloadConfig)
  GOOGLE_DCHECK_NE(&from, this);
  const PayloadConfig* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const PayloadConfig>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.PayloadConfig)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.PayloadConfig)
    MergeFrom(*source);
  }
}

void PayloadConfig::MergeFrom(const PayloadConfig& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.PayloadConfig)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.payload_case()) {
    case kBytebufParams: {
      mutable_bytebuf_params()->::grpc::testing::ByteBufferParams::MergeFrom(from.bytebuf_params());
      break;
    }
    case kSimpleParams: {
      mutable_simple_params()->::grpc::testing::SimpleProtoParams::MergeFrom(from.simple_params());
      break;
    }
    case kComplexParams: {
      mutable_complex_params()->::grpc::testing::ComplexProtoParams::MergeFrom(from.complex_params());
      break;
    }
    case PAYLOAD_NOT_SET: {
      break;
    }
  }
}

void PayloadConfig::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.PayloadConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void PayloadConfig::CopyFrom(const PayloadConfig& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.PayloadConfig)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PayloadConfig::IsInitialized() const {
  return true;
}

void PayloadConfig::Swap(PayloadConfig* other) {
  if (other == this) return;
  InternalSwap(other);
}
void PayloadConfig::InternalSwap(PayloadConfig* other) {
  using std::swap;
  swap(payload_, other->payload_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata PayloadConfig::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace testing
}  // namespace grpc

// @@protoc_insertion_point(global_scope)
