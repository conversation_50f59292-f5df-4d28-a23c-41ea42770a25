// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: src/proto/grpc/testing/payloads.proto

#ifndef PROTOBUF_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto__INCLUDED
#define PROTOBUF_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3005001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)

namespace protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[4];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
void InitDefaultsByteBufferParamsImpl();
void InitDefaultsByteBufferParams();
void InitDefaultsSimpleProtoParamsImpl();
void InitDefaultsSimpleProtoParams();
void InitDefaultsComplexProtoParamsImpl();
void InitDefaultsComplexProtoParams();
void InitDefaultsPayloadConfigImpl();
void InitDefaultsPayloadConfig();
inline void InitDefaults() {
  InitDefaultsByteBufferParams();
  InitDefaultsSimpleProtoParams();
  InitDefaultsComplexProtoParams();
  InitDefaultsPayloadConfig();
}
}  // namespace protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto
namespace grpc {
namespace testing {
class ByteBufferParams;
class ByteBufferParamsDefaultTypeInternal;
extern ByteBufferParamsDefaultTypeInternal _ByteBufferParams_default_instance_;
class ComplexProtoParams;
class ComplexProtoParamsDefaultTypeInternal;
extern ComplexProtoParamsDefaultTypeInternal _ComplexProtoParams_default_instance_;
class PayloadConfig;
class PayloadConfigDefaultTypeInternal;
extern PayloadConfigDefaultTypeInternal _PayloadConfig_default_instance_;
class SimpleProtoParams;
class SimpleProtoParamsDefaultTypeInternal;
extern SimpleProtoParamsDefaultTypeInternal _SimpleProtoParams_default_instance_;
}  // namespace testing
}  // namespace grpc
namespace grpc {
namespace testing {

// ===================================================================

class ByteBufferParams : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.ByteBufferParams) */ {
 public:
  ByteBufferParams();
  virtual ~ByteBufferParams();

  ByteBufferParams(const ByteBufferParams& from);

  inline ByteBufferParams& operator=(const ByteBufferParams& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ByteBufferParams(ByteBufferParams&& from) noexcept
    : ByteBufferParams() {
    *this = ::std::move(from);
  }

  inline ByteBufferParams& operator=(ByteBufferParams&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ByteBufferParams& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ByteBufferParams* internal_default_instance() {
    return reinterpret_cast<const ByteBufferParams*>(
               &_ByteBufferParams_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    0;

  void Swap(ByteBufferParams* other);
  friend void swap(ByteBufferParams& a, ByteBufferParams& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ByteBufferParams* New() const PROTOBUF_FINAL { return New(NULL); }

  ByteBufferParams* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ByteBufferParams& from);
  void MergeFrom(const ByteBufferParams& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ByteBufferParams* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int32 req_size = 1;
  void clear_req_size();
  static const int kReqSizeFieldNumber = 1;
  ::google::protobuf::int32 req_size() const;
  void set_req_size(::google::protobuf::int32 value);

  // int32 resp_size = 2;
  void clear_resp_size();
  static const int kRespSizeFieldNumber = 2;
  ::google::protobuf::int32 resp_size() const;
  void set_resp_size(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:grpc.testing.ByteBufferParams)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int32 req_size_;
  ::google::protobuf::int32 resp_size_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::InitDefaultsByteBufferParamsImpl();
};
// -------------------------------------------------------------------

class SimpleProtoParams : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.SimpleProtoParams) */ {
 public:
  SimpleProtoParams();
  virtual ~SimpleProtoParams();

  SimpleProtoParams(const SimpleProtoParams& from);

  inline SimpleProtoParams& operator=(const SimpleProtoParams& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  SimpleProtoParams(SimpleProtoParams&& from) noexcept
    : SimpleProtoParams() {
    *this = ::std::move(from);
  }

  inline SimpleProtoParams& operator=(SimpleProtoParams&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const SimpleProtoParams& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const SimpleProtoParams* internal_default_instance() {
    return reinterpret_cast<const SimpleProtoParams*>(
               &_SimpleProtoParams_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    1;

  void Swap(SimpleProtoParams* other);
  friend void swap(SimpleProtoParams& a, SimpleProtoParams& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline SimpleProtoParams* New() const PROTOBUF_FINAL { return New(NULL); }

  SimpleProtoParams* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const SimpleProtoParams& from);
  void MergeFrom(const SimpleProtoParams& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(SimpleProtoParams* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int32 req_size = 1;
  void clear_req_size();
  static const int kReqSizeFieldNumber = 1;
  ::google::protobuf::int32 req_size() const;
  void set_req_size(::google::protobuf::int32 value);

  // int32 resp_size = 2;
  void clear_resp_size();
  static const int kRespSizeFieldNumber = 2;
  ::google::protobuf::int32 resp_size() const;
  void set_resp_size(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:grpc.testing.SimpleProtoParams)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int32 req_size_;
  ::google::protobuf::int32 resp_size_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::InitDefaultsSimpleProtoParamsImpl();
};
// -------------------------------------------------------------------

class ComplexProtoParams : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.ComplexProtoParams) */ {
 public:
  ComplexProtoParams();
  virtual ~ComplexProtoParams();

  ComplexProtoParams(const ComplexProtoParams& from);

  inline ComplexProtoParams& operator=(const ComplexProtoParams& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ComplexProtoParams(ComplexProtoParams&& from) noexcept
    : ComplexProtoParams() {
    *this = ::std::move(from);
  }

  inline ComplexProtoParams& operator=(ComplexProtoParams&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ComplexProtoParams& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ComplexProtoParams* internal_default_instance() {
    return reinterpret_cast<const ComplexProtoParams*>(
               &_ComplexProtoParams_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    2;

  void Swap(ComplexProtoParams* other);
  friend void swap(ComplexProtoParams& a, ComplexProtoParams& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ComplexProtoParams* New() const PROTOBUF_FINAL { return New(NULL); }

  ComplexProtoParams* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ComplexProtoParams& from);
  void MergeFrom(const ComplexProtoParams& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ComplexProtoParams* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:grpc.testing.ComplexProtoParams)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::InitDefaultsComplexProtoParamsImpl();
};
// -------------------------------------------------------------------

class PayloadConfig : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.PayloadConfig) */ {
 public:
  PayloadConfig();
  virtual ~PayloadConfig();

  PayloadConfig(const PayloadConfig& from);

  inline PayloadConfig& operator=(const PayloadConfig& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  PayloadConfig(PayloadConfig&& from) noexcept
    : PayloadConfig() {
    *this = ::std::move(from);
  }

  inline PayloadConfig& operator=(PayloadConfig&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const PayloadConfig& default_instance();

  enum PayloadCase {
    kBytebufParams = 1,
    kSimpleParams = 2,
    kComplexParams = 3,
    PAYLOAD_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const PayloadConfig* internal_default_instance() {
    return reinterpret_cast<const PayloadConfig*>(
               &_PayloadConfig_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    3;

  void Swap(PayloadConfig* other);
  friend void swap(PayloadConfig& a, PayloadConfig& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline PayloadConfig* New() const PROTOBUF_FINAL { return New(NULL); }

  PayloadConfig* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const PayloadConfig& from);
  void MergeFrom(const PayloadConfig& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(PayloadConfig* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .grpc.testing.ByteBufferParams bytebuf_params = 1;
  bool has_bytebuf_params() const;
  void clear_bytebuf_params();
  static const int kBytebufParamsFieldNumber = 1;
  const ::grpc::testing::ByteBufferParams& bytebuf_params() const;
  ::grpc::testing::ByteBufferParams* release_bytebuf_params();
  ::grpc::testing::ByteBufferParams* mutable_bytebuf_params();
  void set_allocated_bytebuf_params(::grpc::testing::ByteBufferParams* bytebuf_params);

  // .grpc.testing.SimpleProtoParams simple_params = 2;
  bool has_simple_params() const;
  void clear_simple_params();
  static const int kSimpleParamsFieldNumber = 2;
  const ::grpc::testing::SimpleProtoParams& simple_params() const;
  ::grpc::testing::SimpleProtoParams* release_simple_params();
  ::grpc::testing::SimpleProtoParams* mutable_simple_params();
  void set_allocated_simple_params(::grpc::testing::SimpleProtoParams* simple_params);

  // .grpc.testing.ComplexProtoParams complex_params = 3;
  bool has_complex_params() const;
  void clear_complex_params();
  static const int kComplexParamsFieldNumber = 3;
  const ::grpc::testing::ComplexProtoParams& complex_params() const;
  ::grpc::testing::ComplexProtoParams* release_complex_params();
  ::grpc::testing::ComplexProtoParams* mutable_complex_params();
  void set_allocated_complex_params(::grpc::testing::ComplexProtoParams* complex_params);

  PayloadCase payload_case() const;
  // @@protoc_insertion_point(class_scope:grpc.testing.PayloadConfig)
 private:
  void set_has_bytebuf_params();
  void set_has_simple_params();
  void set_has_complex_params();

  inline bool has_payload() const;
  void clear_payload();
  inline void clear_has_payload();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  union PayloadUnion {
    PayloadUnion() {}
    ::grpc::testing::ByteBufferParams* bytebuf_params_;
    ::grpc::testing::SimpleProtoParams* simple_params_;
    ::grpc::testing::ComplexProtoParams* complex_params_;
  } payload_;
  mutable int _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto::InitDefaultsPayloadConfigImpl();
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ByteBufferParams

// int32 req_size = 1;
inline void ByteBufferParams::clear_req_size() {
  req_size_ = 0;
}
inline ::google::protobuf::int32 ByteBufferParams::req_size() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ByteBufferParams.req_size)
  return req_size_;
}
inline void ByteBufferParams::set_req_size(::google::protobuf::int32 value) {
  
  req_size_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ByteBufferParams.req_size)
}

// int32 resp_size = 2;
inline void ByteBufferParams::clear_resp_size() {
  resp_size_ = 0;
}
inline ::google::protobuf::int32 ByteBufferParams::resp_size() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ByteBufferParams.resp_size)
  return resp_size_;
}
inline void ByteBufferParams::set_resp_size(::google::protobuf::int32 value) {
  
  resp_size_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ByteBufferParams.resp_size)
}

// -------------------------------------------------------------------

// SimpleProtoParams

// int32 req_size = 1;
inline void SimpleProtoParams::clear_req_size() {
  req_size_ = 0;
}
inline ::google::protobuf::int32 SimpleProtoParams::req_size() const {
  // @@protoc_insertion_point(field_get:grpc.testing.SimpleProtoParams.req_size)
  return req_size_;
}
inline void SimpleProtoParams::set_req_size(::google::protobuf::int32 value) {
  
  req_size_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.SimpleProtoParams.req_size)
}

// int32 resp_size = 2;
inline void SimpleProtoParams::clear_resp_size() {
  resp_size_ = 0;
}
inline ::google::protobuf::int32 SimpleProtoParams::resp_size() const {
  // @@protoc_insertion_point(field_get:grpc.testing.SimpleProtoParams.resp_size)
  return resp_size_;
}
inline void SimpleProtoParams::set_resp_size(::google::protobuf::int32 value) {
  
  resp_size_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.SimpleProtoParams.resp_size)
}

// -------------------------------------------------------------------

// ComplexProtoParams

// -------------------------------------------------------------------

// PayloadConfig

// .grpc.testing.ByteBufferParams bytebuf_params = 1;
inline bool PayloadConfig::has_bytebuf_params() const {
  return payload_case() == kBytebufParams;
}
inline void PayloadConfig::set_has_bytebuf_params() {
  _oneof_case_[0] = kBytebufParams;
}
inline void PayloadConfig::clear_bytebuf_params() {
  if (has_bytebuf_params()) {
    delete payload_.bytebuf_params_;
    clear_has_payload();
  }
}
inline ::grpc::testing::ByteBufferParams* PayloadConfig::release_bytebuf_params() {
  // @@protoc_insertion_point(field_release:grpc.testing.PayloadConfig.bytebuf_params)
  if (has_bytebuf_params()) {
    clear_has_payload();
      ::grpc::testing::ByteBufferParams* temp = payload_.bytebuf_params_;
    payload_.bytebuf_params_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::grpc::testing::ByteBufferParams& PayloadConfig::bytebuf_params() const {
  // @@protoc_insertion_point(field_get:grpc.testing.PayloadConfig.bytebuf_params)
  return has_bytebuf_params()
      ? *payload_.bytebuf_params_
      : *reinterpret_cast< ::grpc::testing::ByteBufferParams*>(&::grpc::testing::_ByteBufferParams_default_instance_);
}
inline ::grpc::testing::ByteBufferParams* PayloadConfig::mutable_bytebuf_params() {
  if (!has_bytebuf_params()) {
    clear_payload();
    set_has_bytebuf_params();
    payload_.bytebuf_params_ = new ::grpc::testing::ByteBufferParams;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.PayloadConfig.bytebuf_params)
  return payload_.bytebuf_params_;
}

// .grpc.testing.SimpleProtoParams simple_params = 2;
inline bool PayloadConfig::has_simple_params() const {
  return payload_case() == kSimpleParams;
}
inline void PayloadConfig::set_has_simple_params() {
  _oneof_case_[0] = kSimpleParams;
}
inline void PayloadConfig::clear_simple_params() {
  if (has_simple_params()) {
    delete payload_.simple_params_;
    clear_has_payload();
  }
}
inline ::grpc::testing::SimpleProtoParams* PayloadConfig::release_simple_params() {
  // @@protoc_insertion_point(field_release:grpc.testing.PayloadConfig.simple_params)
  if (has_simple_params()) {
    clear_has_payload();
      ::grpc::testing::SimpleProtoParams* temp = payload_.simple_params_;
    payload_.simple_params_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::grpc::testing::SimpleProtoParams& PayloadConfig::simple_params() const {
  // @@protoc_insertion_point(field_get:grpc.testing.PayloadConfig.simple_params)
  return has_simple_params()
      ? *payload_.simple_params_
      : *reinterpret_cast< ::grpc::testing::SimpleProtoParams*>(&::grpc::testing::_SimpleProtoParams_default_instance_);
}
inline ::grpc::testing::SimpleProtoParams* PayloadConfig::mutable_simple_params() {
  if (!has_simple_params()) {
    clear_payload();
    set_has_simple_params();
    payload_.simple_params_ = new ::grpc::testing::SimpleProtoParams;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.PayloadConfig.simple_params)
  return payload_.simple_params_;
}

// .grpc.testing.ComplexProtoParams complex_params = 3;
inline bool PayloadConfig::has_complex_params() const {
  return payload_case() == kComplexParams;
}
inline void PayloadConfig::set_has_complex_params() {
  _oneof_case_[0] = kComplexParams;
}
inline void PayloadConfig::clear_complex_params() {
  if (has_complex_params()) {
    delete payload_.complex_params_;
    clear_has_payload();
  }
}
inline ::grpc::testing::ComplexProtoParams* PayloadConfig::release_complex_params() {
  // @@protoc_insertion_point(field_release:grpc.testing.PayloadConfig.complex_params)
  if (has_complex_params()) {
    clear_has_payload();
      ::grpc::testing::ComplexProtoParams* temp = payload_.complex_params_;
    payload_.complex_params_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::grpc::testing::ComplexProtoParams& PayloadConfig::complex_params() const {
  // @@protoc_insertion_point(field_get:grpc.testing.PayloadConfig.complex_params)
  return has_complex_params()
      ? *payload_.complex_params_
      : *reinterpret_cast< ::grpc::testing::ComplexProtoParams*>(&::grpc::testing::_ComplexProtoParams_default_instance_);
}
inline ::grpc::testing::ComplexProtoParams* PayloadConfig::mutable_complex_params() {
  if (!has_complex_params()) {
    clear_payload();
    set_has_complex_params();
    payload_.complex_params_ = new ::grpc::testing::ComplexProtoParams;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.PayloadConfig.complex_params)
  return payload_.complex_params_;
}

inline bool PayloadConfig::has_payload() const {
  return payload_case() != PAYLOAD_NOT_SET;
}
inline void PayloadConfig::clear_has_payload() {
  _oneof_case_[0] = PAYLOAD_NOT_SET;
}
inline PayloadConfig::PayloadCase PayloadConfig::payload_case() const {
  return PayloadConfig::PayloadCase(_oneof_case_[0]);
}
#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace testing
}  // namespace grpc

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_src_2fproto_2fgrpc_2ftesting_2fpayloads_2eproto__INCLUDED
