// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: src/proto/grpc/testing/report_qps_scenario_service.proto

#include "src/proto/grpc/testing/report_qps_scenario_service.pb.h"
#include "src/proto/grpc/testing/report_qps_scenario_service.grpc.pb.h"

#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/method_handler_impl.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace grpc {
namespace testing {

static const char* ReportQpsScenarioService_method_names[] = {
  "/grpc.testing.ReportQpsScenarioService/ReportScenario",
};

std::unique_ptr< ReportQpsScenarioService::Stub> ReportQpsScenarioService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< ReportQpsScenarioService::Stub> stub(new ReportQpsScenarioService::Stub(channel));
  return stub;
}

ReportQpsScenarioService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel)
  : channel_(channel), rpcmethod_ReportScenario_(ReportQpsScenarioService_method_names[0], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status ReportQpsScenarioService::Stub::ReportScenario(::grpc::ClientContext* context, const ::grpc::testing::ScenarioResult& request, ::grpc::testing::Void* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_ReportScenario_, context, request, response);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::Void>* ReportQpsScenarioService::Stub::AsyncReportScenarioRaw(::grpc::ClientContext* context, const ::grpc::testing::ScenarioResult& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::Void>::Create(channel_.get(), cq, rpcmethod_ReportScenario_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::Void>* ReportQpsScenarioService::Stub::PrepareAsyncReportScenarioRaw(::grpc::ClientContext* context, const ::grpc::testing::ScenarioResult& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::Void>::Create(channel_.get(), cq, rpcmethod_ReportScenario_, context, request, false);
}

ReportQpsScenarioService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ReportQpsScenarioService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ReportQpsScenarioService::Service, ::grpc::testing::ScenarioResult, ::grpc::testing::Void>(
          std::mem_fn(&ReportQpsScenarioService::Service::ReportScenario), this)));
}

ReportQpsScenarioService::Service::~Service() {
}

::grpc::Status ReportQpsScenarioService::Service::ReportScenario(::grpc::ServerContext* context, const ::grpc::testing::ScenarioResult* request, ::grpc::testing::Void* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace grpc
}  // namespace testing

