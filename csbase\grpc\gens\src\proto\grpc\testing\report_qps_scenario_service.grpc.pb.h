// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: src/proto/grpc/testing/report_qps_scenario_service.proto
// Original file comments:
// Copyright 2015 gRPC authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// An integration test service that covers all the method signature permutations
// of unary/streaming requests/responses.
#ifndef GRPC_src_2fproto_2fgrpc_2ftesting_2freport_5fqps_5fscenario_5fservice_2eproto__INCLUDED
#define GRPC_src_2fproto_2fgrpc_2ftesting_2freport_5fqps_5fscenario_5fservice_2eproto__INCLUDED

#include "src/proto/grpc/testing/report_qps_scenario_service.pb.h"

#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/method_handler_impl.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace grpc {
class CompletionQueue;
class Channel;
class ServerCompletionQueue;
class ServerContext;
}  // namespace grpc

namespace grpc {
namespace testing {

class ReportQpsScenarioService final {
 public:
  static constexpr char const* service_full_name() {
    return "grpc.testing.ReportQpsScenarioService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    // Report results of a QPS test benchmark scenario.
    virtual ::grpc::Status ReportScenario(::grpc::ClientContext* context, const ::grpc::testing::ScenarioResult& request, ::grpc::testing::Void* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Void>> AsyncReportScenario(::grpc::ClientContext* context, const ::grpc::testing::ScenarioResult& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Void>>(AsyncReportScenarioRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Void>> PrepareAsyncReportScenario(::grpc::ClientContext* context, const ::grpc::testing::ScenarioResult& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Void>>(PrepareAsyncReportScenarioRaw(context, request, cq));
    }
  private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Void>* AsyncReportScenarioRaw(::grpc::ClientContext* context, const ::grpc::testing::ScenarioResult& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Void>* PrepareAsyncReportScenarioRaw(::grpc::ClientContext* context, const ::grpc::testing::ScenarioResult& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel);
    ::grpc::Status ReportScenario(::grpc::ClientContext* context, const ::grpc::testing::ScenarioResult& request, ::grpc::testing::Void* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::Void>> AsyncReportScenario(::grpc::ClientContext* context, const ::grpc::testing::ScenarioResult& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::Void>>(AsyncReportScenarioRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::Void>> PrepareAsyncReportScenario(::grpc::ClientContext* context, const ::grpc::testing::ScenarioResult& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::Void>>(PrepareAsyncReportScenarioRaw(context, request, cq));
    }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    ::grpc::ClientAsyncResponseReader< ::grpc::testing::Void>* AsyncReportScenarioRaw(::grpc::ClientContext* context, const ::grpc::testing::ScenarioResult& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::testing::Void>* PrepareAsyncReportScenarioRaw(::grpc::ClientContext* context, const ::grpc::testing::ScenarioResult& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_ReportScenario_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    // Report results of a QPS test benchmark scenario.
    virtual ::grpc::Status ReportScenario(::grpc::ServerContext* context, const ::grpc::testing::ScenarioResult* request, ::grpc::testing::Void* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_ReportScenario : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_ReportScenario() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_ReportScenario() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReportScenario(::grpc::ServerContext* context, const ::grpc::testing::ScenarioResult* request, ::grpc::testing::Void* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestReportScenario(::grpc::ServerContext* context, ::grpc::testing::ScenarioResult* request, ::grpc::ServerAsyncResponseWriter< ::grpc::testing::Void>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_ReportScenario<Service > AsyncService;
  template <class BaseClass>
  class WithGenericMethod_ReportScenario : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_ReportScenario() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_ReportScenario() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReportScenario(::grpc::ServerContext* context, const ::grpc::testing::ScenarioResult* request, ::grpc::testing::Void* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_ReportScenario : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_ReportScenario() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_ReportScenario() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReportScenario(::grpc::ServerContext* context, const ::grpc::testing::ScenarioResult* request, ::grpc::testing::Void* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestReportScenario(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_ReportScenario : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithStreamedUnaryMethod_ReportScenario() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler< ::grpc::testing::ScenarioResult, ::grpc::testing::Void>(std::bind(&WithStreamedUnaryMethod_ReportScenario<BaseClass>::StreamedReportScenario, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_ReportScenario() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status ReportScenario(::grpc::ServerContext* context, const ::grpc::testing::ScenarioResult* request, ::grpc::testing::Void* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedReportScenario(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::grpc::testing::ScenarioResult,::grpc::testing::Void>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_ReportScenario<Service > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_ReportScenario<Service > StreamedService;
};

}  // namespace testing
}  // namespace grpc


#endif  // GRPC_src_2fproto_2fgrpc_2ftesting_2freport_5fqps_5fscenario_5fservice_2eproto__INCLUDED
