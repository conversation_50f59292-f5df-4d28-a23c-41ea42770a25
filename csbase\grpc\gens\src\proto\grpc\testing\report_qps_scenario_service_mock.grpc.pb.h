// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: src/proto/grpc/testing/report_qps_scenario_service.proto

#include "src/proto/grpc/testing/report_qps_scenario_service.pb.h"
#include "src/proto/grpc/testing/report_qps_scenario_service.grpc.pb.h"

#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/sync_stream.h>
#include <gmock/gmock.h>
namespace grpc {
namespace testing {

class MockReportQpsScenarioServiceStub : public ReportQpsScenarioService::StubInterface {
 public:
  MOCK_METHOD3(ReportScenario, ::grpc::Status(::grpc::ClientContext* context, const ::grpc::testing::ScenarioResult& request, ::grpc::testing::Void* response));
  MOCK_METHOD3(AsyncReportScenarioRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Void>*(::grpc::ClientContext* context, const ::grpc::testing::ScenarioResult& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncReportScenarioRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Void>*(::grpc::ClientContext* context, const ::grpc::testing::ScenarioResult& request, ::grpc::CompletionQueue* cq));
};

} // namespace grpc
} // namespace testing

