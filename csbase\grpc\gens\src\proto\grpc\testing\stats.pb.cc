// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: src/proto/grpc/testing/stats.proto

#include "src/proto/grpc/testing/stats.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)
namespace grpc {
namespace testing {
class ServerStatsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ServerStats>
      _instance;
} _ServerStats_default_instance_;
class HistogramParamsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<HistogramParams>
      _instance;
} _HistogramParams_default_instance_;
class HistogramDataDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<HistogramData>
      _instance;
} _HistogramData_default_instance_;
class RequestResultCountDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<RequestResultCount>
      _instance;
} _RequestResultCount_default_instance_;
class ClientStatsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ClientStats>
      _instance;
} _ClientStats_default_instance_;
}  // namespace testing
}  // namespace grpc
namespace protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto {
void InitDefaultsServerStatsImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::InitDefaultsStats();
  {
    void* ptr = &::grpc::testing::_ServerStats_default_instance_;
    new (ptr) ::grpc::testing::ServerStats();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::ServerStats::InitAsDefaultInstance();
}

void InitDefaultsServerStats() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsServerStatsImpl);
}

void InitDefaultsHistogramParamsImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::testing::_HistogramParams_default_instance_;
    new (ptr) ::grpc::testing::HistogramParams();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::HistogramParams::InitAsDefaultInstance();
}

void InitDefaultsHistogramParams() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsHistogramParamsImpl);
}

void InitDefaultsHistogramDataImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::testing::_HistogramData_default_instance_;
    new (ptr) ::grpc::testing::HistogramData();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::HistogramData::InitAsDefaultInstance();
}

void InitDefaultsHistogramData() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsHistogramDataImpl);
}

void InitDefaultsRequestResultCountImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::testing::_RequestResultCount_default_instance_;
    new (ptr) ::grpc::testing::RequestResultCount();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::RequestResultCount::InitAsDefaultInstance();
}

void InitDefaultsRequestResultCount() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsRequestResultCountImpl);
}

void InitDefaultsClientStatsImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::InitDefaultsHistogramData();
  protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::InitDefaultsRequestResultCount();
  protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::InitDefaultsStats();
  {
    void* ptr = &::grpc::testing::_ClientStats_default_instance_;
    new (ptr) ::grpc::testing::ClientStats();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::testing::ClientStats::InitAsDefaultInstance();
}

void InitDefaultsClientStats() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsClientStatsImpl);
}

::google::protobuf::Metadata file_level_metadata[5];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ServerStats, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ServerStats, time_elapsed_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ServerStats, time_user_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ServerStats, time_system_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ServerStats, total_cpu_time_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ServerStats, idle_cpu_time_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ServerStats, cq_poll_count_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ServerStats, core_stats_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::HistogramParams, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::HistogramParams, resolution_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::HistogramParams, max_possible_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::HistogramData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::HistogramData, bucket_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::HistogramData, min_seen_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::HistogramData, max_seen_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::HistogramData, sum_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::HistogramData, sum_of_squares_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::HistogramData, count_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::RequestResultCount, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::RequestResultCount, status_code_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::RequestResultCount, count_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientStats, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientStats, latencies_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientStats, time_elapsed_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientStats, time_user_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientStats, time_system_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientStats, request_results_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientStats, cq_poll_count_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::testing::ClientStats, core_stats_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::grpc::testing::ServerStats)},
  { 12, -1, sizeof(::grpc::testing::HistogramParams)},
  { 19, -1, sizeof(::grpc::testing::HistogramData)},
  { 30, -1, sizeof(::grpc::testing::RequestResultCount)},
  { 37, -1, sizeof(::grpc::testing::ClientStats)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_ServerStats_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_HistogramParams_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_HistogramData_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_RequestResultCount_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::testing::_ClientStats_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  ::google::protobuf::MessageFactory* factory = NULL;
  AssignDescriptors(
      "src/proto/grpc/testing/stats.proto", schemas, file_default_instances, TableStruct::offsets, factory,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 5);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n\"src/proto/grpc/testing/stats.proto\022\014gr"
      "pc.testing\032\037src/proto/grpc/core/stats.pr"
      "oto\"\267\001\n\013ServerStats\022\024\n\014time_elapsed\030\001 \001("
      "\001\022\021\n\ttime_user\030\002 \001(\001\022\023\n\013time_system\030\003 \001("
      "\001\022\026\n\016total_cpu_time\030\004 \001(\004\022\025\n\ridle_cpu_ti"
      "me\030\005 \001(\004\022\025\n\rcq_poll_count\030\006 \001(\004\022$\n\ncore_"
      "stats\030\007 \001(\0132\020.grpc.core.Stats\";\n\017Histogr"
      "amParams\022\022\n\nresolution\030\001 \001(\001\022\024\n\014max_poss"
      "ible\030\002 \001(\001\"w\n\rHistogramData\022\016\n\006bucket\030\001 "
      "\003(\r\022\020\n\010min_seen\030\002 \001(\001\022\020\n\010max_seen\030\003 \001(\001\022"
      "\013\n\003sum\030\004 \001(\001\022\026\n\016sum_of_squares\030\005 \001(\001\022\r\n\005"
      "count\030\006 \001(\001\"8\n\022RequestResultCount\022\023\n\013sta"
      "tus_code\030\001 \001(\005\022\r\n\005count\030\002 \001(\003\"\363\001\n\013Client"
      "Stats\022.\n\tlatencies\030\001 \001(\0132\033.grpc.testing."
      "HistogramData\022\024\n\014time_elapsed\030\002 \001(\001\022\021\n\tt"
      "ime_user\030\003 \001(\001\022\023\n\013time_system\030\004 \001(\001\0229\n\017r"
      "equest_results\030\005 \003(\0132 .grpc.testing.Requ"
      "estResultCount\022\025\n\rcq_poll_count\030\006 \001(\004\022$\n"
      "\ncore_stats\030\007 \001(\0132\020.grpc.core.Statsb\006pro"
      "to3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 763);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "src/proto/grpc/testing/stats.proto", &protobuf_RegisterTypes);
  ::protobuf_src_2fproto_2fgrpc_2fcore_2fstats_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto
namespace grpc {
namespace testing {

// ===================================================================

void ServerStats::InitAsDefaultInstance() {
  ::grpc::testing::_ServerStats_default_instance_._instance.get_mutable()->core_stats_ = const_cast< ::grpc::core::Stats*>(
      ::grpc::core::Stats::internal_default_instance());
}
void ServerStats::clear_core_stats() {
  if (GetArenaNoVirtual() == NULL && core_stats_ != NULL) {
    delete core_stats_;
  }
  core_stats_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ServerStats::kTimeElapsedFieldNumber;
const int ServerStats::kTimeUserFieldNumber;
const int ServerStats::kTimeSystemFieldNumber;
const int ServerStats::kTotalCpuTimeFieldNumber;
const int ServerStats::kIdleCpuTimeFieldNumber;
const int ServerStats::kCqPollCountFieldNumber;
const int ServerStats::kCoreStatsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ServerStats::ServerStats()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::InitDefaultsServerStats();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.ServerStats)
}
ServerStats::ServerStats(const ServerStats& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_core_stats()) {
    core_stats_ = new ::grpc::core::Stats(*from.core_stats_);
  } else {
    core_stats_ = NULL;
  }
  ::memcpy(&time_elapsed_, &from.time_elapsed_,
    static_cast<size_t>(reinterpret_cast<char*>(&cq_poll_count_) -
    reinterpret_cast<char*>(&time_elapsed_)) + sizeof(cq_poll_count_));
  // @@protoc_insertion_point(copy_constructor:grpc.testing.ServerStats)
}

void ServerStats::SharedCtor() {
  ::memset(&core_stats_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&cq_poll_count_) -
      reinterpret_cast<char*>(&core_stats_)) + sizeof(cq_poll_count_));
  _cached_size_ = 0;
}

ServerStats::~ServerStats() {
  // @@protoc_insertion_point(destructor:grpc.testing.ServerStats)
  SharedDtor();
}

void ServerStats::SharedDtor() {
  if (this != internal_default_instance()) delete core_stats_;
}

void ServerStats::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ServerStats::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ServerStats& ServerStats::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::InitDefaultsServerStats();
  return *internal_default_instance();
}

ServerStats* ServerStats::New(::google::protobuf::Arena* arena) const {
  ServerStats* n = new ServerStats;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ServerStats::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.ServerStats)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaNoVirtual() == NULL && core_stats_ != NULL) {
    delete core_stats_;
  }
  core_stats_ = NULL;
  ::memset(&time_elapsed_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&cq_poll_count_) -
      reinterpret_cast<char*>(&time_elapsed_)) + sizeof(cq_poll_count_));
  _internal_metadata_.Clear();
}

bool ServerStats::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.ServerStats)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // double time_elapsed = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(9u /* 9 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &time_elapsed_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double time_user = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(17u /* 17 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &time_user_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double time_system = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(25u /* 25 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &time_system_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint64 total_cpu_time = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &total_cpu_time_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint64 idle_cpu_time = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(40u /* 40 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &idle_cpu_time_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint64 cq_poll_count = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(48u /* 48 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &cq_poll_count_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.core.Stats core_stats = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_core_stats()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.ServerStats)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.ServerStats)
  return false;
#undef DO_
}

void ServerStats::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.ServerStats)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double time_elapsed = 1;
  if (this->time_elapsed() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(1, this->time_elapsed(), output);
  }

  // double time_user = 2;
  if (this->time_user() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(2, this->time_user(), output);
  }

  // double time_system = 3;
  if (this->time_system() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(3, this->time_system(), output);
  }

  // uint64 total_cpu_time = 4;
  if (this->total_cpu_time() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(4, this->total_cpu_time(), output);
  }

  // uint64 idle_cpu_time = 5;
  if (this->idle_cpu_time() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(5, this->idle_cpu_time(), output);
  }

  // uint64 cq_poll_count = 6;
  if (this->cq_poll_count() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(6, this->cq_poll_count(), output);
  }

  // .grpc.core.Stats core_stats = 7;
  if (this->has_core_stats()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7, *this->core_stats_, output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.ServerStats)
}

::google::protobuf::uint8* ServerStats::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.ServerStats)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double time_elapsed = 1;
  if (this->time_elapsed() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(1, this->time_elapsed(), target);
  }

  // double time_user = 2;
  if (this->time_user() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(2, this->time_user(), target);
  }

  // double time_system = 3;
  if (this->time_system() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(3, this->time_system(), target);
  }

  // uint64 total_cpu_time = 4;
  if (this->total_cpu_time() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(4, this->total_cpu_time(), target);
  }

  // uint64 idle_cpu_time = 5;
  if (this->idle_cpu_time() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(5, this->idle_cpu_time(), target);
  }

  // uint64 cq_poll_count = 6;
  if (this->cq_poll_count() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(6, this->cq_poll_count(), target);
  }

  // .grpc.core.Stats core_stats = 7;
  if (this->has_core_stats()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        7, *this->core_stats_, deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.ServerStats)
  return target;
}

size_t ServerStats::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.ServerStats)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // .grpc.core.Stats core_stats = 7;
  if (this->has_core_stats()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->core_stats_);
  }

  // double time_elapsed = 1;
  if (this->time_elapsed() != 0) {
    total_size += 1 + 8;
  }

  // double time_user = 2;
  if (this->time_user() != 0) {
    total_size += 1 + 8;
  }

  // double time_system = 3;
  if (this->time_system() != 0) {
    total_size += 1 + 8;
  }

  // uint64 total_cpu_time = 4;
  if (this->total_cpu_time() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->total_cpu_time());
  }

  // uint64 idle_cpu_time = 5;
  if (this->idle_cpu_time() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->idle_cpu_time());
  }

  // uint64 cq_poll_count = 6;
  if (this->cq_poll_count() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->cq_poll_count());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ServerStats::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.ServerStats)
  GOOGLE_DCHECK_NE(&from, this);
  const ServerStats* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ServerStats>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.ServerStats)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.ServerStats)
    MergeFrom(*source);
  }
}

void ServerStats::MergeFrom(const ServerStats& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.ServerStats)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.has_core_stats()) {
    mutable_core_stats()->::grpc::core::Stats::MergeFrom(from.core_stats());
  }
  if (from.time_elapsed() != 0) {
    set_time_elapsed(from.time_elapsed());
  }
  if (from.time_user() != 0) {
    set_time_user(from.time_user());
  }
  if (from.time_system() != 0) {
    set_time_system(from.time_system());
  }
  if (from.total_cpu_time() != 0) {
    set_total_cpu_time(from.total_cpu_time());
  }
  if (from.idle_cpu_time() != 0) {
    set_idle_cpu_time(from.idle_cpu_time());
  }
  if (from.cq_poll_count() != 0) {
    set_cq_poll_count(from.cq_poll_count());
  }
}

void ServerStats::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.ServerStats)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ServerStats::CopyFrom(const ServerStats& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.ServerStats)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ServerStats::IsInitialized() const {
  return true;
}

void ServerStats::Swap(ServerStats* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ServerStats::InternalSwap(ServerStats* other) {
  using std::swap;
  swap(core_stats_, other->core_stats_);
  swap(time_elapsed_, other->time_elapsed_);
  swap(time_user_, other->time_user_);
  swap(time_system_, other->time_system_);
  swap(total_cpu_time_, other->total_cpu_time_);
  swap(idle_cpu_time_, other->idle_cpu_time_);
  swap(cq_poll_count_, other->cq_poll_count_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ServerStats::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void HistogramParams::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int HistogramParams::kResolutionFieldNumber;
const int HistogramParams::kMaxPossibleFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

HistogramParams::HistogramParams()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::InitDefaultsHistogramParams();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.HistogramParams)
}
HistogramParams::HistogramParams(const HistogramParams& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&resolution_, &from.resolution_,
    static_cast<size_t>(reinterpret_cast<char*>(&max_possible_) -
    reinterpret_cast<char*>(&resolution_)) + sizeof(max_possible_));
  // @@protoc_insertion_point(copy_constructor:grpc.testing.HistogramParams)
}

void HistogramParams::SharedCtor() {
  ::memset(&resolution_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&max_possible_) -
      reinterpret_cast<char*>(&resolution_)) + sizeof(max_possible_));
  _cached_size_ = 0;
}

HistogramParams::~HistogramParams() {
  // @@protoc_insertion_point(destructor:grpc.testing.HistogramParams)
  SharedDtor();
}

void HistogramParams::SharedDtor() {
}

void HistogramParams::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* HistogramParams::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const HistogramParams& HistogramParams::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::InitDefaultsHistogramParams();
  return *internal_default_instance();
}

HistogramParams* HistogramParams::New(::google::protobuf::Arena* arena) const {
  HistogramParams* n = new HistogramParams;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void HistogramParams::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.HistogramParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&resolution_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&max_possible_) -
      reinterpret_cast<char*>(&resolution_)) + sizeof(max_possible_));
  _internal_metadata_.Clear();
}

bool HistogramParams::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.HistogramParams)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // double resolution = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(9u /* 9 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &resolution_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double max_possible = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(17u /* 17 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &max_possible_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.HistogramParams)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.HistogramParams)
  return false;
#undef DO_
}

void HistogramParams::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.HistogramParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double resolution = 1;
  if (this->resolution() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(1, this->resolution(), output);
  }

  // double max_possible = 2;
  if (this->max_possible() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(2, this->max_possible(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.HistogramParams)
}

::google::protobuf::uint8* HistogramParams::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.HistogramParams)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // double resolution = 1;
  if (this->resolution() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(1, this->resolution(), target);
  }

  // double max_possible = 2;
  if (this->max_possible() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(2, this->max_possible(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.HistogramParams)
  return target;
}

size_t HistogramParams::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.HistogramParams)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // double resolution = 1;
  if (this->resolution() != 0) {
    total_size += 1 + 8;
  }

  // double max_possible = 2;
  if (this->max_possible() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void HistogramParams::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.HistogramParams)
  GOOGLE_DCHECK_NE(&from, this);
  const HistogramParams* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const HistogramParams>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.HistogramParams)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.HistogramParams)
    MergeFrom(*source);
  }
}

void HistogramParams::MergeFrom(const HistogramParams& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.HistogramParams)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.resolution() != 0) {
    set_resolution(from.resolution());
  }
  if (from.max_possible() != 0) {
    set_max_possible(from.max_possible());
  }
}

void HistogramParams::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.HistogramParams)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void HistogramParams::CopyFrom(const HistogramParams& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.HistogramParams)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HistogramParams::IsInitialized() const {
  return true;
}

void HistogramParams::Swap(HistogramParams* other) {
  if (other == this) return;
  InternalSwap(other);
}
void HistogramParams::InternalSwap(HistogramParams* other) {
  using std::swap;
  swap(resolution_, other->resolution_);
  swap(max_possible_, other->max_possible_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata HistogramParams::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void HistogramData::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int HistogramData::kBucketFieldNumber;
const int HistogramData::kMinSeenFieldNumber;
const int HistogramData::kMaxSeenFieldNumber;
const int HistogramData::kSumFieldNumber;
const int HistogramData::kSumOfSquaresFieldNumber;
const int HistogramData::kCountFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

HistogramData::HistogramData()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::InitDefaultsHistogramData();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.HistogramData)
}
HistogramData::HistogramData(const HistogramData& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      bucket_(from.bucket_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&min_seen_, &from.min_seen_,
    static_cast<size_t>(reinterpret_cast<char*>(&count_) -
    reinterpret_cast<char*>(&min_seen_)) + sizeof(count_));
  // @@protoc_insertion_point(copy_constructor:grpc.testing.HistogramData)
}

void HistogramData::SharedCtor() {
  ::memset(&min_seen_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&count_) -
      reinterpret_cast<char*>(&min_seen_)) + sizeof(count_));
  _cached_size_ = 0;
}

HistogramData::~HistogramData() {
  // @@protoc_insertion_point(destructor:grpc.testing.HistogramData)
  SharedDtor();
}

void HistogramData::SharedDtor() {
}

void HistogramData::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* HistogramData::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const HistogramData& HistogramData::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::InitDefaultsHistogramData();
  return *internal_default_instance();
}

HistogramData* HistogramData::New(::google::protobuf::Arena* arena) const {
  HistogramData* n = new HistogramData;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void HistogramData::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.HistogramData)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  bucket_.Clear();
  ::memset(&min_seen_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&count_) -
      reinterpret_cast<char*>(&min_seen_)) + sizeof(count_));
  _internal_metadata_.Clear();
}

bool HistogramData::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.HistogramData)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated uint32 bucket = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadPackedPrimitive<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 input, this->mutable_bucket())));
        } else if (
            static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {
          DO_((::google::protobuf::internal::WireFormatLite::ReadRepeatedPrimitiveNoInline<
                   ::google::protobuf::uint32, ::google::protobuf::internal::WireFormatLite::TYPE_UINT32>(
                 1, 10u, input, this->mutable_bucket())));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double min_seen = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(17u /* 17 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &min_seen_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double max_seen = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(25u /* 25 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &max_seen_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double sum = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(33u /* 33 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &sum_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double sum_of_squares = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(41u /* 41 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &sum_of_squares_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double count = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(49u /* 49 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &count_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.HistogramData)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.HistogramData)
  return false;
#undef DO_
}

void HistogramData::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.HistogramData)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated uint32 bucket = 1;
  if (this->bucket_size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteTag(1, ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED, output);
    output->WriteVarint32(static_cast< ::google::protobuf::uint32>(
        _bucket_cached_byte_size_));
  }
  for (int i = 0, n = this->bucket_size(); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt32NoTag(
      this->bucket(i), output);
  }

  // double min_seen = 2;
  if (this->min_seen() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(2, this->min_seen(), output);
  }

  // double max_seen = 3;
  if (this->max_seen() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(3, this->max_seen(), output);
  }

  // double sum = 4;
  if (this->sum() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(4, this->sum(), output);
  }

  // double sum_of_squares = 5;
  if (this->sum_of_squares() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(5, this->sum_of_squares(), output);
  }

  // double count = 6;
  if (this->count() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(6, this->count(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.HistogramData)
}

::google::protobuf::uint8* HistogramData::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.HistogramData)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated uint32 bucket = 1;
  if (this->bucket_size() > 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteTagToArray(
      1,
      ::google::protobuf::internal::WireFormatLite::WIRETYPE_LENGTH_DELIMITED,
      target);
    target = ::google::protobuf::io::CodedOutputStream::WriteVarint32ToArray(
        static_cast< ::google::protobuf::int32>(
            _bucket_cached_byte_size_), target);
    target = ::google::protobuf::internal::WireFormatLite::
      WriteUInt32NoTagToArray(this->bucket_, target);
  }

  // double min_seen = 2;
  if (this->min_seen() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(2, this->min_seen(), target);
  }

  // double max_seen = 3;
  if (this->max_seen() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(3, this->max_seen(), target);
  }

  // double sum = 4;
  if (this->sum() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(4, this->sum(), target);
  }

  // double sum_of_squares = 5;
  if (this->sum_of_squares() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(5, this->sum_of_squares(), target);
  }

  // double count = 6;
  if (this->count() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(6, this->count(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.HistogramData)
  return target;
}

size_t HistogramData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.HistogramData)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated uint32 bucket = 1;
  {
    size_t data_size = ::google::protobuf::internal::WireFormatLite::
      UInt32Size(this->bucket_);
    if (data_size > 0) {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::Int32Size(
            static_cast< ::google::protobuf::int32>(data_size));
    }
    int cached_size = ::google::protobuf::internal::ToCachedSize(data_size);
    GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
    _bucket_cached_byte_size_ = cached_size;
    GOOGLE_SAFE_CONCURRENT_WRITES_END();
    total_size += data_size;
  }

  // double min_seen = 2;
  if (this->min_seen() != 0) {
    total_size += 1 + 8;
  }

  // double max_seen = 3;
  if (this->max_seen() != 0) {
    total_size += 1 + 8;
  }

  // double sum = 4;
  if (this->sum() != 0) {
    total_size += 1 + 8;
  }

  // double sum_of_squares = 5;
  if (this->sum_of_squares() != 0) {
    total_size += 1 + 8;
  }

  // double count = 6;
  if (this->count() != 0) {
    total_size += 1 + 8;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void HistogramData::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.HistogramData)
  GOOGLE_DCHECK_NE(&from, this);
  const HistogramData* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const HistogramData>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.HistogramData)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.HistogramData)
    MergeFrom(*source);
  }
}

void HistogramData::MergeFrom(const HistogramData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.HistogramData)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  bucket_.MergeFrom(from.bucket_);
  if (from.min_seen() != 0) {
    set_min_seen(from.min_seen());
  }
  if (from.max_seen() != 0) {
    set_max_seen(from.max_seen());
  }
  if (from.sum() != 0) {
    set_sum(from.sum());
  }
  if (from.sum_of_squares() != 0) {
    set_sum_of_squares(from.sum_of_squares());
  }
  if (from.count() != 0) {
    set_count(from.count());
  }
}

void HistogramData::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.HistogramData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void HistogramData::CopyFrom(const HistogramData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.HistogramData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HistogramData::IsInitialized() const {
  return true;
}

void HistogramData::Swap(HistogramData* other) {
  if (other == this) return;
  InternalSwap(other);
}
void HistogramData::InternalSwap(HistogramData* other) {
  using std::swap;
  bucket_.InternalSwap(&other->bucket_);
  swap(min_seen_, other->min_seen_);
  swap(max_seen_, other->max_seen_);
  swap(sum_, other->sum_);
  swap(sum_of_squares_, other->sum_of_squares_);
  swap(count_, other->count_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata HistogramData::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void RequestResultCount::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int RequestResultCount::kStatusCodeFieldNumber;
const int RequestResultCount::kCountFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

RequestResultCount::RequestResultCount()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::InitDefaultsRequestResultCount();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.RequestResultCount)
}
RequestResultCount::RequestResultCount(const RequestResultCount& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::memcpy(&count_, &from.count_,
    static_cast<size_t>(reinterpret_cast<char*>(&status_code_) -
    reinterpret_cast<char*>(&count_)) + sizeof(status_code_));
  // @@protoc_insertion_point(copy_constructor:grpc.testing.RequestResultCount)
}

void RequestResultCount::SharedCtor() {
  ::memset(&count_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&status_code_) -
      reinterpret_cast<char*>(&count_)) + sizeof(status_code_));
  _cached_size_ = 0;
}

RequestResultCount::~RequestResultCount() {
  // @@protoc_insertion_point(destructor:grpc.testing.RequestResultCount)
  SharedDtor();
}

void RequestResultCount::SharedDtor() {
}

void RequestResultCount::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* RequestResultCount::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const RequestResultCount& RequestResultCount::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::InitDefaultsRequestResultCount();
  return *internal_default_instance();
}

RequestResultCount* RequestResultCount::New(::google::protobuf::Arena* arena) const {
  RequestResultCount* n = new RequestResultCount;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void RequestResultCount::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.RequestResultCount)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&count_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&status_code_) -
      reinterpret_cast<char*>(&count_)) + sizeof(status_code_));
  _internal_metadata_.Clear();
}

bool RequestResultCount::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.RequestResultCount)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // int32 status_code = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(8u /* 8 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &status_code_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 count = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &count_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.RequestResultCount)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.RequestResultCount)
  return false;
#undef DO_
}

void RequestResultCount::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.RequestResultCount)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 status_code = 1;
  if (this->status_code() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(1, this->status_code(), output);
  }

  // int64 count = 2;
  if (this->count() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->count(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.RequestResultCount)
}

::google::protobuf::uint8* RequestResultCount::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.RequestResultCount)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 status_code = 1;
  if (this->status_code() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(1, this->status_code(), target);
  }

  // int64 count = 2;
  if (this->count() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->count(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.RequestResultCount)
  return target;
}

size_t RequestResultCount::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.RequestResultCount)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // int64 count = 2;
  if (this->count() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->count());
  }

  // int32 status_code = 1;
  if (this->status_code() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->status_code());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void RequestResultCount::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.RequestResultCount)
  GOOGLE_DCHECK_NE(&from, this);
  const RequestResultCount* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const RequestResultCount>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.RequestResultCount)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.RequestResultCount)
    MergeFrom(*source);
  }
}

void RequestResultCount::MergeFrom(const RequestResultCount& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.RequestResultCount)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.count() != 0) {
    set_count(from.count());
  }
  if (from.status_code() != 0) {
    set_status_code(from.status_code());
  }
}

void RequestResultCount::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.RequestResultCount)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void RequestResultCount::CopyFrom(const RequestResultCount& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.RequestResultCount)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RequestResultCount::IsInitialized() const {
  return true;
}

void RequestResultCount::Swap(RequestResultCount* other) {
  if (other == this) return;
  InternalSwap(other);
}
void RequestResultCount::InternalSwap(RequestResultCount* other) {
  using std::swap;
  swap(count_, other->count_);
  swap(status_code_, other->status_code_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata RequestResultCount::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ClientStats::InitAsDefaultInstance() {
  ::grpc::testing::_ClientStats_default_instance_._instance.get_mutable()->latencies_ = const_cast< ::grpc::testing::HistogramData*>(
      ::grpc::testing::HistogramData::internal_default_instance());
  ::grpc::testing::_ClientStats_default_instance_._instance.get_mutable()->core_stats_ = const_cast< ::grpc::core::Stats*>(
      ::grpc::core::Stats::internal_default_instance());
}
void ClientStats::clear_core_stats() {
  if (GetArenaNoVirtual() == NULL && core_stats_ != NULL) {
    delete core_stats_;
  }
  core_stats_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ClientStats::kLatenciesFieldNumber;
const int ClientStats::kTimeElapsedFieldNumber;
const int ClientStats::kTimeUserFieldNumber;
const int ClientStats::kTimeSystemFieldNumber;
const int ClientStats::kRequestResultsFieldNumber;
const int ClientStats::kCqPollCountFieldNumber;
const int ClientStats::kCoreStatsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ClientStats::ClientStats()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::InitDefaultsClientStats();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.testing.ClientStats)
}
ClientStats::ClientStats(const ClientStats& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      request_results_(from.request_results_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_latencies()) {
    latencies_ = new ::grpc::testing::HistogramData(*from.latencies_);
  } else {
    latencies_ = NULL;
  }
  if (from.has_core_stats()) {
    core_stats_ = new ::grpc::core::Stats(*from.core_stats_);
  } else {
    core_stats_ = NULL;
  }
  ::memcpy(&time_elapsed_, &from.time_elapsed_,
    static_cast<size_t>(reinterpret_cast<char*>(&cq_poll_count_) -
    reinterpret_cast<char*>(&time_elapsed_)) + sizeof(cq_poll_count_));
  // @@protoc_insertion_point(copy_constructor:grpc.testing.ClientStats)
}

void ClientStats::SharedCtor() {
  ::memset(&latencies_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&cq_poll_count_) -
      reinterpret_cast<char*>(&latencies_)) + sizeof(cq_poll_count_));
  _cached_size_ = 0;
}

ClientStats::~ClientStats() {
  // @@protoc_insertion_point(destructor:grpc.testing.ClientStats)
  SharedDtor();
}

void ClientStats::SharedDtor() {
  if (this != internal_default_instance()) delete latencies_;
  if (this != internal_default_instance()) delete core_stats_;
}

void ClientStats::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ClientStats::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ClientStats& ClientStats::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::InitDefaultsClientStats();
  return *internal_default_instance();
}

ClientStats* ClientStats::New(::google::protobuf::Arena* arena) const {
  ClientStats* n = new ClientStats;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ClientStats::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.testing.ClientStats)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  request_results_.Clear();
  if (GetArenaNoVirtual() == NULL && latencies_ != NULL) {
    delete latencies_;
  }
  latencies_ = NULL;
  if (GetArenaNoVirtual() == NULL && core_stats_ != NULL) {
    delete core_stats_;
  }
  core_stats_ = NULL;
  ::memset(&time_elapsed_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&cq_poll_count_) -
      reinterpret_cast<char*>(&time_elapsed_)) + sizeof(cq_poll_count_));
  _internal_metadata_.Clear();
}

bool ClientStats::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.testing.ClientStats)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .grpc.testing.HistogramData latencies = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_latencies()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double time_elapsed = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(17u /* 17 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &time_elapsed_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double time_user = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(25u /* 25 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &time_user_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // double time_system = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(33u /* 33 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   double, ::google::protobuf::internal::WireFormatLite::TYPE_DOUBLE>(
                 input, &time_system_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .grpc.testing.RequestResultCount request_results = 5;
      case 5: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(42u /* 42 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(input, add_request_results()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // uint64 cq_poll_count = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(48u /* 48 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::uint64, ::google::protobuf::internal::WireFormatLite::TYPE_UINT64>(
                 input, &cq_poll_count_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.core.Stats core_stats = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(58u /* 58 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_core_stats()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.testing.ClientStats)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.testing.ClientStats)
  return false;
#undef DO_
}

void ClientStats::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.testing.ClientStats)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.HistogramData latencies = 1;
  if (this->has_latencies()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->latencies_, output);
  }

  // double time_elapsed = 2;
  if (this->time_elapsed() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(2, this->time_elapsed(), output);
  }

  // double time_user = 3;
  if (this->time_user() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(3, this->time_user(), output);
  }

  // double time_system = 4;
  if (this->time_system() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteDouble(4, this->time_system(), output);
  }

  // repeated .grpc.testing.RequestResultCount request_results = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->request_results_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      5, this->request_results(static_cast<int>(i)), output);
  }

  // uint64 cq_poll_count = 6;
  if (this->cq_poll_count() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteUInt64(6, this->cq_poll_count(), output);
  }

  // .grpc.core.Stats core_stats = 7;
  if (this->has_core_stats()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      7, *this->core_stats_, output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.testing.ClientStats)
}

::google::protobuf::uint8* ClientStats::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.testing.ClientStats)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.testing.HistogramData latencies = 1;
  if (this->has_latencies()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, *this->latencies_, deterministic, target);
  }

  // double time_elapsed = 2;
  if (this->time_elapsed() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(2, this->time_elapsed(), target);
  }

  // double time_user = 3;
  if (this->time_user() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(3, this->time_user(), target);
  }

  // double time_system = 4;
  if (this->time_system() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteDoubleToArray(4, this->time_system(), target);
  }

  // repeated .grpc.testing.RequestResultCount request_results = 5;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->request_results_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        5, this->request_results(static_cast<int>(i)), deterministic, target);
  }

  // uint64 cq_poll_count = 6;
  if (this->cq_poll_count() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteUInt64ToArray(6, this->cq_poll_count(), target);
  }

  // .grpc.core.Stats core_stats = 7;
  if (this->has_core_stats()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        7, *this->core_stats_, deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.testing.ClientStats)
  return target;
}

size_t ClientStats::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.testing.ClientStats)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .grpc.testing.RequestResultCount request_results = 5;
  {
    unsigned int count = static_cast<unsigned int>(this->request_results_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->request_results(static_cast<int>(i)));
    }
  }

  // .grpc.testing.HistogramData latencies = 1;
  if (this->has_latencies()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->latencies_);
  }

  // .grpc.core.Stats core_stats = 7;
  if (this->has_core_stats()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->core_stats_);
  }

  // double time_elapsed = 2;
  if (this->time_elapsed() != 0) {
    total_size += 1 + 8;
  }

  // double time_user = 3;
  if (this->time_user() != 0) {
    total_size += 1 + 8;
  }

  // double time_system = 4;
  if (this->time_system() != 0) {
    total_size += 1 + 8;
  }

  // uint64 cq_poll_count = 6;
  if (this->cq_poll_count() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::UInt64Size(
        this->cq_poll_count());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ClientStats::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.testing.ClientStats)
  GOOGLE_DCHECK_NE(&from, this);
  const ClientStats* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ClientStats>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.testing.ClientStats)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.testing.ClientStats)
    MergeFrom(*source);
  }
}

void ClientStats::MergeFrom(const ClientStats& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.testing.ClientStats)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  request_results_.MergeFrom(from.request_results_);
  if (from.has_latencies()) {
    mutable_latencies()->::grpc::testing::HistogramData::MergeFrom(from.latencies());
  }
  if (from.has_core_stats()) {
    mutable_core_stats()->::grpc::core::Stats::MergeFrom(from.core_stats());
  }
  if (from.time_elapsed() != 0) {
    set_time_elapsed(from.time_elapsed());
  }
  if (from.time_user() != 0) {
    set_time_user(from.time_user());
  }
  if (from.time_system() != 0) {
    set_time_system(from.time_system());
  }
  if (from.cq_poll_count() != 0) {
    set_cq_poll_count(from.cq_poll_count());
  }
}

void ClientStats::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.testing.ClientStats)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ClientStats::CopyFrom(const ClientStats& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.testing.ClientStats)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ClientStats::IsInitialized() const {
  return true;
}

void ClientStats::Swap(ClientStats* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ClientStats::InternalSwap(ClientStats* other) {
  using std::swap;
  request_results_.InternalSwap(&other->request_results_);
  swap(latencies_, other->latencies_);
  swap(core_stats_, other->core_stats_);
  swap(time_elapsed_, other->time_elapsed_);
  swap(time_user_, other->time_user_);
  swap(time_system_, other->time_system_);
  swap(cq_poll_count_, other->cq_poll_count_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ClientStats::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace testing
}  // namespace grpc

// @@protoc_insertion_point(global_scope)
