// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: src/proto/grpc/testing/stats.proto

#ifndef PROTOBUF_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto__INCLUDED
#define PROTOBUF_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3005001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "src/proto/grpc/core/stats.pb.h"
// @@protoc_insertion_point(includes)

namespace protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[5];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
void InitDefaultsServerStatsImpl();
void InitDefaultsServerStats();
void InitDefaultsHistogramParamsImpl();
void InitDefaultsHistogramParams();
void InitDefaultsHistogramDataImpl();
void InitDefaultsHistogramData();
void InitDefaultsRequestResultCountImpl();
void InitDefaultsRequestResultCount();
void InitDefaultsClientStatsImpl();
void InitDefaultsClientStats();
inline void InitDefaults() {
  InitDefaultsServerStats();
  InitDefaultsHistogramParams();
  InitDefaultsHistogramData();
  InitDefaultsRequestResultCount();
  InitDefaultsClientStats();
}
}  // namespace protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto
namespace grpc {
namespace testing {
class ClientStats;
class ClientStatsDefaultTypeInternal;
extern ClientStatsDefaultTypeInternal _ClientStats_default_instance_;
class HistogramData;
class HistogramDataDefaultTypeInternal;
extern HistogramDataDefaultTypeInternal _HistogramData_default_instance_;
class HistogramParams;
class HistogramParamsDefaultTypeInternal;
extern HistogramParamsDefaultTypeInternal _HistogramParams_default_instance_;
class RequestResultCount;
class RequestResultCountDefaultTypeInternal;
extern RequestResultCountDefaultTypeInternal _RequestResultCount_default_instance_;
class ServerStats;
class ServerStatsDefaultTypeInternal;
extern ServerStatsDefaultTypeInternal _ServerStats_default_instance_;
}  // namespace testing
}  // namespace grpc
namespace grpc {
namespace testing {

// ===================================================================

class ServerStats : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.ServerStats) */ {
 public:
  ServerStats();
  virtual ~ServerStats();

  ServerStats(const ServerStats& from);

  inline ServerStats& operator=(const ServerStats& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ServerStats(ServerStats&& from) noexcept
    : ServerStats() {
    *this = ::std::move(from);
  }

  inline ServerStats& operator=(ServerStats&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ServerStats& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ServerStats* internal_default_instance() {
    return reinterpret_cast<const ServerStats*>(
               &_ServerStats_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    0;

  void Swap(ServerStats* other);
  friend void swap(ServerStats& a, ServerStats& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ServerStats* New() const PROTOBUF_FINAL { return New(NULL); }

  ServerStats* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ServerStats& from);
  void MergeFrom(const ServerStats& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ServerStats* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .grpc.core.Stats core_stats = 7;
  bool has_core_stats() const;
  void clear_core_stats();
  static const int kCoreStatsFieldNumber = 7;
  const ::grpc::core::Stats& core_stats() const;
  ::grpc::core::Stats* release_core_stats();
  ::grpc::core::Stats* mutable_core_stats();
  void set_allocated_core_stats(::grpc::core::Stats* core_stats);

  // double time_elapsed = 1;
  void clear_time_elapsed();
  static const int kTimeElapsedFieldNumber = 1;
  double time_elapsed() const;
  void set_time_elapsed(double value);

  // double time_user = 2;
  void clear_time_user();
  static const int kTimeUserFieldNumber = 2;
  double time_user() const;
  void set_time_user(double value);

  // double time_system = 3;
  void clear_time_system();
  static const int kTimeSystemFieldNumber = 3;
  double time_system() const;
  void set_time_system(double value);

  // uint64 total_cpu_time = 4;
  void clear_total_cpu_time();
  static const int kTotalCpuTimeFieldNumber = 4;
  ::google::protobuf::uint64 total_cpu_time() const;
  void set_total_cpu_time(::google::protobuf::uint64 value);

  // uint64 idle_cpu_time = 5;
  void clear_idle_cpu_time();
  static const int kIdleCpuTimeFieldNumber = 5;
  ::google::protobuf::uint64 idle_cpu_time() const;
  void set_idle_cpu_time(::google::protobuf::uint64 value);

  // uint64 cq_poll_count = 6;
  void clear_cq_poll_count();
  static const int kCqPollCountFieldNumber = 6;
  ::google::protobuf::uint64 cq_poll_count() const;
  void set_cq_poll_count(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:grpc.testing.ServerStats)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::grpc::core::Stats* core_stats_;
  double time_elapsed_;
  double time_user_;
  double time_system_;
  ::google::protobuf::uint64 total_cpu_time_;
  ::google::protobuf::uint64 idle_cpu_time_;
  ::google::protobuf::uint64 cq_poll_count_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::InitDefaultsServerStatsImpl();
};
// -------------------------------------------------------------------

class HistogramParams : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.HistogramParams) */ {
 public:
  HistogramParams();
  virtual ~HistogramParams();

  HistogramParams(const HistogramParams& from);

  inline HistogramParams& operator=(const HistogramParams& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  HistogramParams(HistogramParams&& from) noexcept
    : HistogramParams() {
    *this = ::std::move(from);
  }

  inline HistogramParams& operator=(HistogramParams&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const HistogramParams& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const HistogramParams* internal_default_instance() {
    return reinterpret_cast<const HistogramParams*>(
               &_HistogramParams_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    1;

  void Swap(HistogramParams* other);
  friend void swap(HistogramParams& a, HistogramParams& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline HistogramParams* New() const PROTOBUF_FINAL { return New(NULL); }

  HistogramParams* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const HistogramParams& from);
  void MergeFrom(const HistogramParams& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(HistogramParams* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // double resolution = 1;
  void clear_resolution();
  static const int kResolutionFieldNumber = 1;
  double resolution() const;
  void set_resolution(double value);

  // double max_possible = 2;
  void clear_max_possible();
  static const int kMaxPossibleFieldNumber = 2;
  double max_possible() const;
  void set_max_possible(double value);

  // @@protoc_insertion_point(class_scope:grpc.testing.HistogramParams)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  double resolution_;
  double max_possible_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::InitDefaultsHistogramParamsImpl();
};
// -------------------------------------------------------------------

class HistogramData : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.HistogramData) */ {
 public:
  HistogramData();
  virtual ~HistogramData();

  HistogramData(const HistogramData& from);

  inline HistogramData& operator=(const HistogramData& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  HistogramData(HistogramData&& from) noexcept
    : HistogramData() {
    *this = ::std::move(from);
  }

  inline HistogramData& operator=(HistogramData&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const HistogramData& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const HistogramData* internal_default_instance() {
    return reinterpret_cast<const HistogramData*>(
               &_HistogramData_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    2;

  void Swap(HistogramData* other);
  friend void swap(HistogramData& a, HistogramData& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline HistogramData* New() const PROTOBUF_FINAL { return New(NULL); }

  HistogramData* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const HistogramData& from);
  void MergeFrom(const HistogramData& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(HistogramData* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated uint32 bucket = 1;
  int bucket_size() const;
  void clear_bucket();
  static const int kBucketFieldNumber = 1;
  ::google::protobuf::uint32 bucket(int index) const;
  void set_bucket(int index, ::google::protobuf::uint32 value);
  void add_bucket(::google::protobuf::uint32 value);
  const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
      bucket() const;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
      mutable_bucket();

  // double min_seen = 2;
  void clear_min_seen();
  static const int kMinSeenFieldNumber = 2;
  double min_seen() const;
  void set_min_seen(double value);

  // double max_seen = 3;
  void clear_max_seen();
  static const int kMaxSeenFieldNumber = 3;
  double max_seen() const;
  void set_max_seen(double value);

  // double sum = 4;
  void clear_sum();
  static const int kSumFieldNumber = 4;
  double sum() const;
  void set_sum(double value);

  // double sum_of_squares = 5;
  void clear_sum_of_squares();
  static const int kSumOfSquaresFieldNumber = 5;
  double sum_of_squares() const;
  void set_sum_of_squares(double value);

  // double count = 6;
  void clear_count();
  static const int kCountFieldNumber = 6;
  double count() const;
  void set_count(double value);

  // @@protoc_insertion_point(class_scope:grpc.testing.HistogramData)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedField< ::google::protobuf::uint32 > bucket_;
  mutable int _bucket_cached_byte_size_;
  double min_seen_;
  double max_seen_;
  double sum_;
  double sum_of_squares_;
  double count_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::InitDefaultsHistogramDataImpl();
};
// -------------------------------------------------------------------

class RequestResultCount : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.RequestResultCount) */ {
 public:
  RequestResultCount();
  virtual ~RequestResultCount();

  RequestResultCount(const RequestResultCount& from);

  inline RequestResultCount& operator=(const RequestResultCount& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  RequestResultCount(RequestResultCount&& from) noexcept
    : RequestResultCount() {
    *this = ::std::move(from);
  }

  inline RequestResultCount& operator=(RequestResultCount&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const RequestResultCount& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const RequestResultCount* internal_default_instance() {
    return reinterpret_cast<const RequestResultCount*>(
               &_RequestResultCount_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    3;

  void Swap(RequestResultCount* other);
  friend void swap(RequestResultCount& a, RequestResultCount& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline RequestResultCount* New() const PROTOBUF_FINAL { return New(NULL); }

  RequestResultCount* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const RequestResultCount& from);
  void MergeFrom(const RequestResultCount& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(RequestResultCount* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // int64 count = 2;
  void clear_count();
  static const int kCountFieldNumber = 2;
  ::google::protobuf::int64 count() const;
  void set_count(::google::protobuf::int64 value);

  // int32 status_code = 1;
  void clear_status_code();
  static const int kStatusCodeFieldNumber = 1;
  ::google::protobuf::int32 status_code() const;
  void set_status_code(::google::protobuf::int32 value);

  // @@protoc_insertion_point(class_scope:grpc.testing.RequestResultCount)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::int64 count_;
  ::google::protobuf::int32 status_code_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::InitDefaultsRequestResultCountImpl();
};
// -------------------------------------------------------------------

class ClientStats : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.testing.ClientStats) */ {
 public:
  ClientStats();
  virtual ~ClientStats();

  ClientStats(const ClientStats& from);

  inline ClientStats& operator=(const ClientStats& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ClientStats(ClientStats&& from) noexcept
    : ClientStats() {
    *this = ::std::move(from);
  }

  inline ClientStats& operator=(ClientStats&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ClientStats& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ClientStats* internal_default_instance() {
    return reinterpret_cast<const ClientStats*>(
               &_ClientStats_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    4;

  void Swap(ClientStats* other);
  friend void swap(ClientStats& a, ClientStats& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ClientStats* New() const PROTOBUF_FINAL { return New(NULL); }

  ClientStats* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ClientStats& from);
  void MergeFrom(const ClientStats& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ClientStats* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .grpc.testing.RequestResultCount request_results = 5;
  int request_results_size() const;
  void clear_request_results();
  static const int kRequestResultsFieldNumber = 5;
  const ::grpc::testing::RequestResultCount& request_results(int index) const;
  ::grpc::testing::RequestResultCount* mutable_request_results(int index);
  ::grpc::testing::RequestResultCount* add_request_results();
  ::google::protobuf::RepeatedPtrField< ::grpc::testing::RequestResultCount >*
      mutable_request_results();
  const ::google::protobuf::RepeatedPtrField< ::grpc::testing::RequestResultCount >&
      request_results() const;

  // .grpc.testing.HistogramData latencies = 1;
  bool has_latencies() const;
  void clear_latencies();
  static const int kLatenciesFieldNumber = 1;
  const ::grpc::testing::HistogramData& latencies() const;
  ::grpc::testing::HistogramData* release_latencies();
  ::grpc::testing::HistogramData* mutable_latencies();
  void set_allocated_latencies(::grpc::testing::HistogramData* latencies);

  // .grpc.core.Stats core_stats = 7;
  bool has_core_stats() const;
  void clear_core_stats();
  static const int kCoreStatsFieldNumber = 7;
  const ::grpc::core::Stats& core_stats() const;
  ::grpc::core::Stats* release_core_stats();
  ::grpc::core::Stats* mutable_core_stats();
  void set_allocated_core_stats(::grpc::core::Stats* core_stats);

  // double time_elapsed = 2;
  void clear_time_elapsed();
  static const int kTimeElapsedFieldNumber = 2;
  double time_elapsed() const;
  void set_time_elapsed(double value);

  // double time_user = 3;
  void clear_time_user();
  static const int kTimeUserFieldNumber = 3;
  double time_user() const;
  void set_time_user(double value);

  // double time_system = 4;
  void clear_time_system();
  static const int kTimeSystemFieldNumber = 4;
  double time_system() const;
  void set_time_system(double value);

  // uint64 cq_poll_count = 6;
  void clear_cq_poll_count();
  static const int kCqPollCountFieldNumber = 6;
  ::google::protobuf::uint64 cq_poll_count() const;
  void set_cq_poll_count(::google::protobuf::uint64 value);

  // @@protoc_insertion_point(class_scope:grpc.testing.ClientStats)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::grpc::testing::RequestResultCount > request_results_;
  ::grpc::testing::HistogramData* latencies_;
  ::grpc::core::Stats* core_stats_;
  double time_elapsed_;
  double time_user_;
  double time_system_;
  ::google::protobuf::uint64 cq_poll_count_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto::InitDefaultsClientStatsImpl();
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ServerStats

// double time_elapsed = 1;
inline void ServerStats::clear_time_elapsed() {
  time_elapsed_ = 0;
}
inline double ServerStats::time_elapsed() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ServerStats.time_elapsed)
  return time_elapsed_;
}
inline void ServerStats::set_time_elapsed(double value) {
  
  time_elapsed_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ServerStats.time_elapsed)
}

// double time_user = 2;
inline void ServerStats::clear_time_user() {
  time_user_ = 0;
}
inline double ServerStats::time_user() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ServerStats.time_user)
  return time_user_;
}
inline void ServerStats::set_time_user(double value) {
  
  time_user_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ServerStats.time_user)
}

// double time_system = 3;
inline void ServerStats::clear_time_system() {
  time_system_ = 0;
}
inline double ServerStats::time_system() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ServerStats.time_system)
  return time_system_;
}
inline void ServerStats::set_time_system(double value) {
  
  time_system_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ServerStats.time_system)
}

// uint64 total_cpu_time = 4;
inline void ServerStats::clear_total_cpu_time() {
  total_cpu_time_ = GOOGLE_ULONGLONG(0);
}
inline ::google::protobuf::uint64 ServerStats::total_cpu_time() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ServerStats.total_cpu_time)
  return total_cpu_time_;
}
inline void ServerStats::set_total_cpu_time(::google::protobuf::uint64 value) {
  
  total_cpu_time_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ServerStats.total_cpu_time)
}

// uint64 idle_cpu_time = 5;
inline void ServerStats::clear_idle_cpu_time() {
  idle_cpu_time_ = GOOGLE_ULONGLONG(0);
}
inline ::google::protobuf::uint64 ServerStats::idle_cpu_time() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ServerStats.idle_cpu_time)
  return idle_cpu_time_;
}
inline void ServerStats::set_idle_cpu_time(::google::protobuf::uint64 value) {
  
  idle_cpu_time_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ServerStats.idle_cpu_time)
}

// uint64 cq_poll_count = 6;
inline void ServerStats::clear_cq_poll_count() {
  cq_poll_count_ = GOOGLE_ULONGLONG(0);
}
inline ::google::protobuf::uint64 ServerStats::cq_poll_count() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ServerStats.cq_poll_count)
  return cq_poll_count_;
}
inline void ServerStats::set_cq_poll_count(::google::protobuf::uint64 value) {
  
  cq_poll_count_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ServerStats.cq_poll_count)
}

// .grpc.core.Stats core_stats = 7;
inline bool ServerStats::has_core_stats() const {
  return this != internal_default_instance() && core_stats_ != NULL;
}
inline const ::grpc::core::Stats& ServerStats::core_stats() const {
  const ::grpc::core::Stats* p = core_stats_;
  // @@protoc_insertion_point(field_get:grpc.testing.ServerStats.core_stats)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::core::Stats*>(
      &::grpc::core::_Stats_default_instance_);
}
inline ::grpc::core::Stats* ServerStats::release_core_stats() {
  // @@protoc_insertion_point(field_release:grpc.testing.ServerStats.core_stats)
  
  ::grpc::core::Stats* temp = core_stats_;
  core_stats_ = NULL;
  return temp;
}
inline ::grpc::core::Stats* ServerStats::mutable_core_stats() {
  
  if (core_stats_ == NULL) {
    core_stats_ = new ::grpc::core::Stats;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.ServerStats.core_stats)
  return core_stats_;
}
inline void ServerStats::set_allocated_core_stats(::grpc::core::Stats* core_stats) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(core_stats_);
  }
  if (core_stats) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      core_stats = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, core_stats, submessage_arena);
    }
    
  } else {
    
  }
  core_stats_ = core_stats;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.ServerStats.core_stats)
}

// -------------------------------------------------------------------

// HistogramParams

// double resolution = 1;
inline void HistogramParams::clear_resolution() {
  resolution_ = 0;
}
inline double HistogramParams::resolution() const {
  // @@protoc_insertion_point(field_get:grpc.testing.HistogramParams.resolution)
  return resolution_;
}
inline void HistogramParams::set_resolution(double value) {
  
  resolution_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.HistogramParams.resolution)
}

// double max_possible = 2;
inline void HistogramParams::clear_max_possible() {
  max_possible_ = 0;
}
inline double HistogramParams::max_possible() const {
  // @@protoc_insertion_point(field_get:grpc.testing.HistogramParams.max_possible)
  return max_possible_;
}
inline void HistogramParams::set_max_possible(double value) {
  
  max_possible_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.HistogramParams.max_possible)
}

// -------------------------------------------------------------------

// HistogramData

// repeated uint32 bucket = 1;
inline int HistogramData::bucket_size() const {
  return bucket_.size();
}
inline void HistogramData::clear_bucket() {
  bucket_.Clear();
}
inline ::google::protobuf::uint32 HistogramData::bucket(int index) const {
  // @@protoc_insertion_point(field_get:grpc.testing.HistogramData.bucket)
  return bucket_.Get(index);
}
inline void HistogramData::set_bucket(int index, ::google::protobuf::uint32 value) {
  bucket_.Set(index, value);
  // @@protoc_insertion_point(field_set:grpc.testing.HistogramData.bucket)
}
inline void HistogramData::add_bucket(::google::protobuf::uint32 value) {
  bucket_.Add(value);
  // @@protoc_insertion_point(field_add:grpc.testing.HistogramData.bucket)
}
inline const ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >&
HistogramData::bucket() const {
  // @@protoc_insertion_point(field_list:grpc.testing.HistogramData.bucket)
  return bucket_;
}
inline ::google::protobuf::RepeatedField< ::google::protobuf::uint32 >*
HistogramData::mutable_bucket() {
  // @@protoc_insertion_point(field_mutable_list:grpc.testing.HistogramData.bucket)
  return &bucket_;
}

// double min_seen = 2;
inline void HistogramData::clear_min_seen() {
  min_seen_ = 0;
}
inline double HistogramData::min_seen() const {
  // @@protoc_insertion_point(field_get:grpc.testing.HistogramData.min_seen)
  return min_seen_;
}
inline void HistogramData::set_min_seen(double value) {
  
  min_seen_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.HistogramData.min_seen)
}

// double max_seen = 3;
inline void HistogramData::clear_max_seen() {
  max_seen_ = 0;
}
inline double HistogramData::max_seen() const {
  // @@protoc_insertion_point(field_get:grpc.testing.HistogramData.max_seen)
  return max_seen_;
}
inline void HistogramData::set_max_seen(double value) {
  
  max_seen_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.HistogramData.max_seen)
}

// double sum = 4;
inline void HistogramData::clear_sum() {
  sum_ = 0;
}
inline double HistogramData::sum() const {
  // @@protoc_insertion_point(field_get:grpc.testing.HistogramData.sum)
  return sum_;
}
inline void HistogramData::set_sum(double value) {
  
  sum_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.HistogramData.sum)
}

// double sum_of_squares = 5;
inline void HistogramData::clear_sum_of_squares() {
  sum_of_squares_ = 0;
}
inline double HistogramData::sum_of_squares() const {
  // @@protoc_insertion_point(field_get:grpc.testing.HistogramData.sum_of_squares)
  return sum_of_squares_;
}
inline void HistogramData::set_sum_of_squares(double value) {
  
  sum_of_squares_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.HistogramData.sum_of_squares)
}

// double count = 6;
inline void HistogramData::clear_count() {
  count_ = 0;
}
inline double HistogramData::count() const {
  // @@protoc_insertion_point(field_get:grpc.testing.HistogramData.count)
  return count_;
}
inline void HistogramData::set_count(double value) {
  
  count_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.HistogramData.count)
}

// -------------------------------------------------------------------

// RequestResultCount

// int32 status_code = 1;
inline void RequestResultCount::clear_status_code() {
  status_code_ = 0;
}
inline ::google::protobuf::int32 RequestResultCount::status_code() const {
  // @@protoc_insertion_point(field_get:grpc.testing.RequestResultCount.status_code)
  return status_code_;
}
inline void RequestResultCount::set_status_code(::google::protobuf::int32 value) {
  
  status_code_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.RequestResultCount.status_code)
}

// int64 count = 2;
inline void RequestResultCount::clear_count() {
  count_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 RequestResultCount::count() const {
  // @@protoc_insertion_point(field_get:grpc.testing.RequestResultCount.count)
  return count_;
}
inline void RequestResultCount::set_count(::google::protobuf::int64 value) {
  
  count_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.RequestResultCount.count)
}

// -------------------------------------------------------------------

// ClientStats

// .grpc.testing.HistogramData latencies = 1;
inline bool ClientStats::has_latencies() const {
  return this != internal_default_instance() && latencies_ != NULL;
}
inline void ClientStats::clear_latencies() {
  if (GetArenaNoVirtual() == NULL && latencies_ != NULL) {
    delete latencies_;
  }
  latencies_ = NULL;
}
inline const ::grpc::testing::HistogramData& ClientStats::latencies() const {
  const ::grpc::testing::HistogramData* p = latencies_;
  // @@protoc_insertion_point(field_get:grpc.testing.ClientStats.latencies)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::testing::HistogramData*>(
      &::grpc::testing::_HistogramData_default_instance_);
}
inline ::grpc::testing::HistogramData* ClientStats::release_latencies() {
  // @@protoc_insertion_point(field_release:grpc.testing.ClientStats.latencies)
  
  ::grpc::testing::HistogramData* temp = latencies_;
  latencies_ = NULL;
  return temp;
}
inline ::grpc::testing::HistogramData* ClientStats::mutable_latencies() {
  
  if (latencies_ == NULL) {
    latencies_ = new ::grpc::testing::HistogramData;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.ClientStats.latencies)
  return latencies_;
}
inline void ClientStats::set_allocated_latencies(::grpc::testing::HistogramData* latencies) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete latencies_;
  }
  if (latencies) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      latencies = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, latencies, submessage_arena);
    }
    
  } else {
    
  }
  latencies_ = latencies;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.ClientStats.latencies)
}

// double time_elapsed = 2;
inline void ClientStats::clear_time_elapsed() {
  time_elapsed_ = 0;
}
inline double ClientStats::time_elapsed() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ClientStats.time_elapsed)
  return time_elapsed_;
}
inline void ClientStats::set_time_elapsed(double value) {
  
  time_elapsed_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ClientStats.time_elapsed)
}

// double time_user = 3;
inline void ClientStats::clear_time_user() {
  time_user_ = 0;
}
inline double ClientStats::time_user() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ClientStats.time_user)
  return time_user_;
}
inline void ClientStats::set_time_user(double value) {
  
  time_user_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ClientStats.time_user)
}

// double time_system = 4;
inline void ClientStats::clear_time_system() {
  time_system_ = 0;
}
inline double ClientStats::time_system() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ClientStats.time_system)
  return time_system_;
}
inline void ClientStats::set_time_system(double value) {
  
  time_system_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ClientStats.time_system)
}

// repeated .grpc.testing.RequestResultCount request_results = 5;
inline int ClientStats::request_results_size() const {
  return request_results_.size();
}
inline void ClientStats::clear_request_results() {
  request_results_.Clear();
}
inline const ::grpc::testing::RequestResultCount& ClientStats::request_results(int index) const {
  // @@protoc_insertion_point(field_get:grpc.testing.ClientStats.request_results)
  return request_results_.Get(index);
}
inline ::grpc::testing::RequestResultCount* ClientStats::mutable_request_results(int index) {
  // @@protoc_insertion_point(field_mutable:grpc.testing.ClientStats.request_results)
  return request_results_.Mutable(index);
}
inline ::grpc::testing::RequestResultCount* ClientStats::add_request_results() {
  // @@protoc_insertion_point(field_add:grpc.testing.ClientStats.request_results)
  return request_results_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::grpc::testing::RequestResultCount >*
ClientStats::mutable_request_results() {
  // @@protoc_insertion_point(field_mutable_list:grpc.testing.ClientStats.request_results)
  return &request_results_;
}
inline const ::google::protobuf::RepeatedPtrField< ::grpc::testing::RequestResultCount >&
ClientStats::request_results() const {
  // @@protoc_insertion_point(field_list:grpc.testing.ClientStats.request_results)
  return request_results_;
}

// uint64 cq_poll_count = 6;
inline void ClientStats::clear_cq_poll_count() {
  cq_poll_count_ = GOOGLE_ULONGLONG(0);
}
inline ::google::protobuf::uint64 ClientStats::cq_poll_count() const {
  // @@protoc_insertion_point(field_get:grpc.testing.ClientStats.cq_poll_count)
  return cq_poll_count_;
}
inline void ClientStats::set_cq_poll_count(::google::protobuf::uint64 value) {
  
  cq_poll_count_ = value;
  // @@protoc_insertion_point(field_set:grpc.testing.ClientStats.cq_poll_count)
}

// .grpc.core.Stats core_stats = 7;
inline bool ClientStats::has_core_stats() const {
  return this != internal_default_instance() && core_stats_ != NULL;
}
inline const ::grpc::core::Stats& ClientStats::core_stats() const {
  const ::grpc::core::Stats* p = core_stats_;
  // @@protoc_insertion_point(field_get:grpc.testing.ClientStats.core_stats)
  return p != NULL ? *p : *reinterpret_cast<const ::grpc::core::Stats*>(
      &::grpc::core::_Stats_default_instance_);
}
inline ::grpc::core::Stats* ClientStats::release_core_stats() {
  // @@protoc_insertion_point(field_release:grpc.testing.ClientStats.core_stats)
  
  ::grpc::core::Stats* temp = core_stats_;
  core_stats_ = NULL;
  return temp;
}
inline ::grpc::core::Stats* ClientStats::mutable_core_stats() {
  
  if (core_stats_ == NULL) {
    core_stats_ = new ::grpc::core::Stats;
  }
  // @@protoc_insertion_point(field_mutable:grpc.testing.ClientStats.core_stats)
  return core_stats_;
}
inline void ClientStats::set_allocated_core_stats(::grpc::core::Stats* core_stats) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(core_stats_);
  }
  if (core_stats) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      core_stats = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, core_stats, submessage_arena);
    }
    
  } else {
    
  }
  core_stats_ = core_stats;
  // @@protoc_insertion_point(field_set_allocated:grpc.testing.ClientStats.core_stats)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace testing
}  // namespace grpc

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_src_2fproto_2fgrpc_2ftesting_2fstats_2eproto__INCLUDED
