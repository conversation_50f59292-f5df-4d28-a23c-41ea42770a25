// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: src/proto/grpc/testing/test.proto

#include "src/proto/grpc/testing/test.pb.h"
#include "src/proto/grpc/testing/test.grpc.pb.h"

#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/method_handler_impl.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace grpc {
namespace testing {

static const char* TestService_method_names[] = {
  "/grpc.testing.TestService/EmptyCall",
  "/grpc.testing.TestService/UnaryCall",
  "/grpc.testing.TestService/CacheableUnaryCall",
  "/grpc.testing.TestService/StreamingOutputCall",
  "/grpc.testing.TestService/StreamingInputCall",
  "/grpc.testing.TestService/FullDuplexCall",
  "/grpc.testing.TestService/HalfDuplexCall",
  "/grpc.testing.TestService/UnimplementedCall",
};

std::unique_ptr< TestService::Stub> TestService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< TestService::Stub> stub(new TestService::Stub(channel));
  return stub;
}

TestService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel)
  : channel_(channel), rpcmethod_EmptyCall_(TestService_method_names[0], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_UnaryCall_(TestService_method_names[1], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_CacheableUnaryCall_(TestService_method_names[2], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StreamingOutputCall_(TestService_method_names[3], ::grpc::internal::RpcMethod::SERVER_STREAMING, channel)
  , rpcmethod_StreamingInputCall_(TestService_method_names[4], ::grpc::internal::RpcMethod::CLIENT_STREAMING, channel)
  , rpcmethod_FullDuplexCall_(TestService_method_names[5], ::grpc::internal::RpcMethod::BIDI_STREAMING, channel)
  , rpcmethod_HalfDuplexCall_(TestService_method_names[6], ::grpc::internal::RpcMethod::BIDI_STREAMING, channel)
  , rpcmethod_UnimplementedCall_(TestService_method_names[7], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status TestService::Stub::EmptyCall(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::testing::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_EmptyCall_, context, request, response);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>* TestService::Stub::AsyncEmptyCallRaw(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::Empty>::Create(channel_.get(), cq, rpcmethod_EmptyCall_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>* TestService::Stub::PrepareAsyncEmptyCallRaw(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::Empty>::Create(channel_.get(), cq, rpcmethod_EmptyCall_, context, request, false);
}

::grpc::Status TestService::Stub::UnaryCall(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::testing::SimpleResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_UnaryCall_, context, request, response);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::SimpleResponse>* TestService::Stub::AsyncUnaryCallRaw(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::SimpleResponse>::Create(channel_.get(), cq, rpcmethod_UnaryCall_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::SimpleResponse>* TestService::Stub::PrepareAsyncUnaryCallRaw(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::SimpleResponse>::Create(channel_.get(), cq, rpcmethod_UnaryCall_, context, request, false);
}

::grpc::Status TestService::Stub::CacheableUnaryCall(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::testing::SimpleResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_CacheableUnaryCall_, context, request, response);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::SimpleResponse>* TestService::Stub::AsyncCacheableUnaryCallRaw(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::SimpleResponse>::Create(channel_.get(), cq, rpcmethod_CacheableUnaryCall_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::SimpleResponse>* TestService::Stub::PrepareAsyncCacheableUnaryCallRaw(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::SimpleResponse>::Create(channel_.get(), cq, rpcmethod_CacheableUnaryCall_, context, request, false);
}

::grpc::ClientReader< ::grpc::testing::StreamingOutputCallResponse>* TestService::Stub::StreamingOutputCallRaw(::grpc::ClientContext* context, const ::grpc::testing::StreamingOutputCallRequest& request) {
  return ::grpc::internal::ClientReaderFactory< ::grpc::testing::StreamingOutputCallResponse>::Create(channel_.get(), rpcmethod_StreamingOutputCall_, context, request);
}

::grpc::ClientAsyncReader< ::grpc::testing::StreamingOutputCallResponse>* TestService::Stub::AsyncStreamingOutputCallRaw(::grpc::ClientContext* context, const ::grpc::testing::StreamingOutputCallRequest& request, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncReaderFactory< ::grpc::testing::StreamingOutputCallResponse>::Create(channel_.get(), cq, rpcmethod_StreamingOutputCall_, context, request, true, tag);
}

::grpc::ClientAsyncReader< ::grpc::testing::StreamingOutputCallResponse>* TestService::Stub::PrepareAsyncStreamingOutputCallRaw(::grpc::ClientContext* context, const ::grpc::testing::StreamingOutputCallRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncReaderFactory< ::grpc::testing::StreamingOutputCallResponse>::Create(channel_.get(), cq, rpcmethod_StreamingOutputCall_, context, request, false, nullptr);
}

::grpc::ClientWriter< ::grpc::testing::StreamingInputCallRequest>* TestService::Stub::StreamingInputCallRaw(::grpc::ClientContext* context, ::grpc::testing::StreamingInputCallResponse* response) {
  return ::grpc::internal::ClientWriterFactory< ::grpc::testing::StreamingInputCallRequest>::Create(channel_.get(), rpcmethod_StreamingInputCall_, context, response);
}

::grpc::ClientAsyncWriter< ::grpc::testing::StreamingInputCallRequest>* TestService::Stub::AsyncStreamingInputCallRaw(::grpc::ClientContext* context, ::grpc::testing::StreamingInputCallResponse* response, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncWriterFactory< ::grpc::testing::StreamingInputCallRequest>::Create(channel_.get(), cq, rpcmethod_StreamingInputCall_, context, response, true, tag);
}

::grpc::ClientAsyncWriter< ::grpc::testing::StreamingInputCallRequest>* TestService::Stub::PrepareAsyncStreamingInputCallRaw(::grpc::ClientContext* context, ::grpc::testing::StreamingInputCallResponse* response, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncWriterFactory< ::grpc::testing::StreamingInputCallRequest>::Create(channel_.get(), cq, rpcmethod_StreamingInputCall_, context, response, false, nullptr);
}

::grpc::ClientReaderWriter< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>* TestService::Stub::FullDuplexCallRaw(::grpc::ClientContext* context) {
  return ::grpc::internal::ClientReaderWriterFactory< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>::Create(channel_.get(), rpcmethod_FullDuplexCall_, context);
}

::grpc::ClientAsyncReaderWriter< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>* TestService::Stub::AsyncFullDuplexCallRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncReaderWriterFactory< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>::Create(channel_.get(), cq, rpcmethod_FullDuplexCall_, context, true, tag);
}

::grpc::ClientAsyncReaderWriter< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>* TestService::Stub::PrepareAsyncFullDuplexCallRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncReaderWriterFactory< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>::Create(channel_.get(), cq, rpcmethod_FullDuplexCall_, context, false, nullptr);
}

::grpc::ClientReaderWriter< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>* TestService::Stub::HalfDuplexCallRaw(::grpc::ClientContext* context) {
  return ::grpc::internal::ClientReaderWriterFactory< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>::Create(channel_.get(), rpcmethod_HalfDuplexCall_, context);
}

::grpc::ClientAsyncReaderWriter< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>* TestService::Stub::AsyncHalfDuplexCallRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncReaderWriterFactory< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>::Create(channel_.get(), cq, rpcmethod_HalfDuplexCall_, context, true, tag);
}

::grpc::ClientAsyncReaderWriter< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>* TestService::Stub::PrepareAsyncHalfDuplexCallRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncReaderWriterFactory< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>::Create(channel_.get(), cq, rpcmethod_HalfDuplexCall_, context, false, nullptr);
}

::grpc::Status TestService::Stub::UnimplementedCall(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::testing::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_UnimplementedCall_, context, request, response);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>* TestService::Stub::AsyncUnimplementedCallRaw(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::Empty>::Create(channel_.get(), cq, rpcmethod_UnimplementedCall_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>* TestService::Stub::PrepareAsyncUnimplementedCallRaw(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::Empty>::Create(channel_.get(), cq, rpcmethod_UnimplementedCall_, context, request, false);
}

TestService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      TestService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< TestService::Service, ::grpc::testing::Empty, ::grpc::testing::Empty>(
          std::mem_fn(&TestService::Service::EmptyCall), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      TestService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< TestService::Service, ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>(
          std::mem_fn(&TestService::Service::UnaryCall), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      TestService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< TestService::Service, ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>(
          std::mem_fn(&TestService::Service::CacheableUnaryCall), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      TestService_method_names[3],
      ::grpc::internal::RpcMethod::SERVER_STREAMING,
      new ::grpc::internal::ServerStreamingHandler< TestService::Service, ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>(
          std::mem_fn(&TestService::Service::StreamingOutputCall), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      TestService_method_names[4],
      ::grpc::internal::RpcMethod::CLIENT_STREAMING,
      new ::grpc::internal::ClientStreamingHandler< TestService::Service, ::grpc::testing::StreamingInputCallRequest, ::grpc::testing::StreamingInputCallResponse>(
          std::mem_fn(&TestService::Service::StreamingInputCall), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      TestService_method_names[5],
      ::grpc::internal::RpcMethod::BIDI_STREAMING,
      new ::grpc::internal::BidiStreamingHandler< TestService::Service, ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>(
          std::mem_fn(&TestService::Service::FullDuplexCall), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      TestService_method_names[6],
      ::grpc::internal::RpcMethod::BIDI_STREAMING,
      new ::grpc::internal::BidiStreamingHandler< TestService::Service, ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>(
          std::mem_fn(&TestService::Service::HalfDuplexCall), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      TestService_method_names[7],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< TestService::Service, ::grpc::testing::Empty, ::grpc::testing::Empty>(
          std::mem_fn(&TestService::Service::UnimplementedCall), this)));
}

TestService::Service::~Service() {
}

::grpc::Status TestService::Service::EmptyCall(::grpc::ServerContext* context, const ::grpc::testing::Empty* request, ::grpc::testing::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status TestService::Service::UnaryCall(::grpc::ServerContext* context, const ::grpc::testing::SimpleRequest* request, ::grpc::testing::SimpleResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status TestService::Service::CacheableUnaryCall(::grpc::ServerContext* context, const ::grpc::testing::SimpleRequest* request, ::grpc::testing::SimpleResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status TestService::Service::StreamingOutputCall(::grpc::ServerContext* context, const ::grpc::testing::StreamingOutputCallRequest* request, ::grpc::ServerWriter< ::grpc::testing::StreamingOutputCallResponse>* writer) {
  (void) context;
  (void) request;
  (void) writer;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status TestService::Service::StreamingInputCall(::grpc::ServerContext* context, ::grpc::ServerReader< ::grpc::testing::StreamingInputCallRequest>* reader, ::grpc::testing::StreamingInputCallResponse* response) {
  (void) context;
  (void) reader;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status TestService::Service::FullDuplexCall(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::StreamingOutputCallResponse, ::grpc::testing::StreamingOutputCallRequest>* stream) {
  (void) context;
  (void) stream;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status TestService::Service::HalfDuplexCall(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::StreamingOutputCallResponse, ::grpc::testing::StreamingOutputCallRequest>* stream) {
  (void) context;
  (void) stream;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status TestService::Service::UnimplementedCall(::grpc::ServerContext* context, const ::grpc::testing::Empty* request, ::grpc::testing::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


static const char* UnimplementedService_method_names[] = {
  "/grpc.testing.UnimplementedService/UnimplementedCall",
};

std::unique_ptr< UnimplementedService::Stub> UnimplementedService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< UnimplementedService::Stub> stub(new UnimplementedService::Stub(channel));
  return stub;
}

UnimplementedService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel)
  : channel_(channel), rpcmethod_UnimplementedCall_(UnimplementedService_method_names[0], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status UnimplementedService::Stub::UnimplementedCall(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::testing::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_UnimplementedCall_, context, request, response);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>* UnimplementedService::Stub::AsyncUnimplementedCallRaw(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::Empty>::Create(channel_.get(), cq, rpcmethod_UnimplementedCall_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>* UnimplementedService::Stub::PrepareAsyncUnimplementedCallRaw(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::Empty>::Create(channel_.get(), cq, rpcmethod_UnimplementedCall_, context, request, false);
}

UnimplementedService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      UnimplementedService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< UnimplementedService::Service, ::grpc::testing::Empty, ::grpc::testing::Empty>(
          std::mem_fn(&UnimplementedService::Service::UnimplementedCall), this)));
}

UnimplementedService::Service::~Service() {
}

::grpc::Status UnimplementedService::Service::UnimplementedCall(::grpc::ServerContext* context, const ::grpc::testing::Empty* request, ::grpc::testing::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


static const char* ReconnectService_method_names[] = {
  "/grpc.testing.ReconnectService/Start",
  "/grpc.testing.ReconnectService/Stop",
};

std::unique_ptr< ReconnectService::Stub> ReconnectService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< ReconnectService::Stub> stub(new ReconnectService::Stub(channel));
  return stub;
}

ReconnectService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel)
  : channel_(channel), rpcmethod_Start_(ReconnectService_method_names[0], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_Stop_(ReconnectService_method_names[1], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status ReconnectService::Stub::Start(::grpc::ClientContext* context, const ::grpc::testing::ReconnectParams& request, ::grpc::testing::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_Start_, context, request, response);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>* ReconnectService::Stub::AsyncStartRaw(::grpc::ClientContext* context, const ::grpc::testing::ReconnectParams& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::Empty>::Create(channel_.get(), cq, rpcmethod_Start_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>* ReconnectService::Stub::PrepareAsyncStartRaw(::grpc::ClientContext* context, const ::grpc::testing::ReconnectParams& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::Empty>::Create(channel_.get(), cq, rpcmethod_Start_, context, request, false);
}

::grpc::Status ReconnectService::Stub::Stop(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::testing::ReconnectInfo* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_Stop_, context, request, response);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::ReconnectInfo>* ReconnectService::Stub::AsyncStopRaw(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::ReconnectInfo>::Create(channel_.get(), cq, rpcmethod_Stop_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::ReconnectInfo>* ReconnectService::Stub::PrepareAsyncStopRaw(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::ReconnectInfo>::Create(channel_.get(), cq, rpcmethod_Stop_, context, request, false);
}

ReconnectService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ReconnectService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ReconnectService::Service, ::grpc::testing::ReconnectParams, ::grpc::testing::Empty>(
          std::mem_fn(&ReconnectService::Service::Start), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ReconnectService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ReconnectService::Service, ::grpc::testing::Empty, ::grpc::testing::ReconnectInfo>(
          std::mem_fn(&ReconnectService::Service::Stop), this)));
}

ReconnectService::Service::~Service() {
}

::grpc::Status ReconnectService::Service::Start(::grpc::ServerContext* context, const ::grpc::testing::ReconnectParams* request, ::grpc::testing::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ReconnectService::Service::Stop(::grpc::ServerContext* context, const ::grpc::testing::Empty* request, ::grpc::testing::ReconnectInfo* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace grpc
}  // namespace testing

