// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: src/proto/grpc/testing/test.proto
// Original file comments:
// Copyright 2015-2016 gRPC authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// An integration test service that covers all the method signature permutations
// of unary/streaming requests/responses.
//
#ifndef GRPC_src_2fproto_2fgrpc_2ftesting_2ftest_2eproto__INCLUDED
#define GRPC_src_2fproto_2fgrpc_2ftesting_2ftest_2eproto__INCLUDED

#include "src/proto/grpc/testing/test.pb.h"

#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/method_handler_impl.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace grpc {
class CompletionQueue;
class Channel;
class ServerCompletionQueue;
class ServerContext;
}  // namespace grpc

namespace grpc {
namespace testing {

// A simple service to test the various types of RPCs and experiment with
// performance with various types of payload.
class TestService final {
 public:
  static constexpr char const* service_full_name() {
    return "grpc.testing.TestService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    // One empty request followed by one empty response.
    virtual ::grpc::Status EmptyCall(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::testing::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>> AsyncEmptyCall(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>>(AsyncEmptyCallRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>> PrepareAsyncEmptyCall(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>>(PrepareAsyncEmptyCallRaw(context, request, cq));
    }
    // One request followed by one response.
    virtual ::grpc::Status UnaryCall(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::testing::SimpleResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::SimpleResponse>> AsyncUnaryCall(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::SimpleResponse>>(AsyncUnaryCallRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::SimpleResponse>> PrepareAsyncUnaryCall(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::SimpleResponse>>(PrepareAsyncUnaryCallRaw(context, request, cq));
    }
    // One request followed by one response. Response has cache control
    // headers set such that a caching HTTP proxy (such as GFE) can
    // satisfy subsequent requests.
    virtual ::grpc::Status CacheableUnaryCall(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::testing::SimpleResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::SimpleResponse>> AsyncCacheableUnaryCall(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::SimpleResponse>>(AsyncCacheableUnaryCallRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::SimpleResponse>> PrepareAsyncCacheableUnaryCall(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::SimpleResponse>>(PrepareAsyncCacheableUnaryCallRaw(context, request, cq));
    }
    // One request followed by a sequence of responses (streamed download).
    // The server returns the payload with client desired type and sizes.
    std::unique_ptr< ::grpc::ClientReaderInterface< ::grpc::testing::StreamingOutputCallResponse>> StreamingOutputCall(::grpc::ClientContext* context, const ::grpc::testing::StreamingOutputCallRequest& request) {
      return std::unique_ptr< ::grpc::ClientReaderInterface< ::grpc::testing::StreamingOutputCallResponse>>(StreamingOutputCallRaw(context, request));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::grpc::testing::StreamingOutputCallResponse>> AsyncStreamingOutputCall(::grpc::ClientContext* context, const ::grpc::testing::StreamingOutputCallRequest& request, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::grpc::testing::StreamingOutputCallResponse>>(AsyncStreamingOutputCallRaw(context, request, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::grpc::testing::StreamingOutputCallResponse>> PrepareAsyncStreamingOutputCall(::grpc::ClientContext* context, const ::grpc::testing::StreamingOutputCallRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::grpc::testing::StreamingOutputCallResponse>>(PrepareAsyncStreamingOutputCallRaw(context, request, cq));
    }
    // A sequence of requests followed by one response (streamed upload).
    // The server returns the aggregated size of client payload as the result.
    std::unique_ptr< ::grpc::ClientWriterInterface< ::grpc::testing::StreamingInputCallRequest>> StreamingInputCall(::grpc::ClientContext* context, ::grpc::testing::StreamingInputCallResponse* response) {
      return std::unique_ptr< ::grpc::ClientWriterInterface< ::grpc::testing::StreamingInputCallRequest>>(StreamingInputCallRaw(context, response));
    }
    std::unique_ptr< ::grpc::ClientAsyncWriterInterface< ::grpc::testing::StreamingInputCallRequest>> AsyncStreamingInputCall(::grpc::ClientContext* context, ::grpc::testing::StreamingInputCallResponse* response, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncWriterInterface< ::grpc::testing::StreamingInputCallRequest>>(AsyncStreamingInputCallRaw(context, response, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncWriterInterface< ::grpc::testing::StreamingInputCallRequest>> PrepareAsyncStreamingInputCall(::grpc::ClientContext* context, ::grpc::testing::StreamingInputCallResponse* response, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncWriterInterface< ::grpc::testing::StreamingInputCallRequest>>(PrepareAsyncStreamingInputCallRaw(context, response, cq));
    }
    // A sequence of requests with each request served by the server immediately.
    // As one request could lead to multiple responses, this interface
    // demonstrates the idea of full duplexing.
    std::unique_ptr< ::grpc::ClientReaderWriterInterface< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>> FullDuplexCall(::grpc::ClientContext* context) {
      return std::unique_ptr< ::grpc::ClientReaderWriterInterface< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>>(FullDuplexCallRaw(context));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>> AsyncFullDuplexCall(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>>(AsyncFullDuplexCallRaw(context, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>> PrepareAsyncFullDuplexCall(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>>(PrepareAsyncFullDuplexCallRaw(context, cq));
    }
    // A sequence of requests followed by a sequence of responses.
    // The server buffers all the client requests and then serves them in order. A
    // stream of responses are returned to the client when the server starts with
    // first request.
    std::unique_ptr< ::grpc::ClientReaderWriterInterface< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>> HalfDuplexCall(::grpc::ClientContext* context) {
      return std::unique_ptr< ::grpc::ClientReaderWriterInterface< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>>(HalfDuplexCallRaw(context));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>> AsyncHalfDuplexCall(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>>(AsyncHalfDuplexCallRaw(context, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>> PrepareAsyncHalfDuplexCall(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>>(PrepareAsyncHalfDuplexCallRaw(context, cq));
    }
    // The test server will not implement this method. It will be used
    // to test the behavior when clients call unimplemented methods.
    virtual ::grpc::Status UnimplementedCall(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::testing::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>> AsyncUnimplementedCall(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>>(AsyncUnimplementedCallRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>> PrepareAsyncUnimplementedCall(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>>(PrepareAsyncUnimplementedCallRaw(context, request, cq));
    }
  private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>* AsyncEmptyCallRaw(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>* PrepareAsyncEmptyCallRaw(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::SimpleResponse>* AsyncUnaryCallRaw(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::SimpleResponse>* PrepareAsyncUnaryCallRaw(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::SimpleResponse>* AsyncCacheableUnaryCallRaw(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::SimpleResponse>* PrepareAsyncCacheableUnaryCallRaw(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientReaderInterface< ::grpc::testing::StreamingOutputCallResponse>* StreamingOutputCallRaw(::grpc::ClientContext* context, const ::grpc::testing::StreamingOutputCallRequest& request) = 0;
    virtual ::grpc::ClientAsyncReaderInterface< ::grpc::testing::StreamingOutputCallResponse>* AsyncStreamingOutputCallRaw(::grpc::ClientContext* context, const ::grpc::testing::StreamingOutputCallRequest& request, ::grpc::CompletionQueue* cq, void* tag) = 0;
    virtual ::grpc::ClientAsyncReaderInterface< ::grpc::testing::StreamingOutputCallResponse>* PrepareAsyncStreamingOutputCallRaw(::grpc::ClientContext* context, const ::grpc::testing::StreamingOutputCallRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientWriterInterface< ::grpc::testing::StreamingInputCallRequest>* StreamingInputCallRaw(::grpc::ClientContext* context, ::grpc::testing::StreamingInputCallResponse* response) = 0;
    virtual ::grpc::ClientAsyncWriterInterface< ::grpc::testing::StreamingInputCallRequest>* AsyncStreamingInputCallRaw(::grpc::ClientContext* context, ::grpc::testing::StreamingInputCallResponse* response, ::grpc::CompletionQueue* cq, void* tag) = 0;
    virtual ::grpc::ClientAsyncWriterInterface< ::grpc::testing::StreamingInputCallRequest>* PrepareAsyncStreamingInputCallRaw(::grpc::ClientContext* context, ::grpc::testing::StreamingInputCallResponse* response, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientReaderWriterInterface< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>* FullDuplexCallRaw(::grpc::ClientContext* context) = 0;
    virtual ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>* AsyncFullDuplexCallRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) = 0;
    virtual ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>* PrepareAsyncFullDuplexCallRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientReaderWriterInterface< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>* HalfDuplexCallRaw(::grpc::ClientContext* context) = 0;
    virtual ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>* AsyncHalfDuplexCallRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) = 0;
    virtual ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>* PrepareAsyncHalfDuplexCallRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>* AsyncUnimplementedCallRaw(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>* PrepareAsyncUnimplementedCallRaw(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel);
    ::grpc::Status EmptyCall(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::testing::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>> AsyncEmptyCall(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>>(AsyncEmptyCallRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>> PrepareAsyncEmptyCall(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>>(PrepareAsyncEmptyCallRaw(context, request, cq));
    }
    ::grpc::Status UnaryCall(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::testing::SimpleResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::SimpleResponse>> AsyncUnaryCall(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::SimpleResponse>>(AsyncUnaryCallRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::SimpleResponse>> PrepareAsyncUnaryCall(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::SimpleResponse>>(PrepareAsyncUnaryCallRaw(context, request, cq));
    }
    ::grpc::Status CacheableUnaryCall(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::testing::SimpleResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::SimpleResponse>> AsyncCacheableUnaryCall(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::SimpleResponse>>(AsyncCacheableUnaryCallRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::SimpleResponse>> PrepareAsyncCacheableUnaryCall(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::SimpleResponse>>(PrepareAsyncCacheableUnaryCallRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientReader< ::grpc::testing::StreamingOutputCallResponse>> StreamingOutputCall(::grpc::ClientContext* context, const ::grpc::testing::StreamingOutputCallRequest& request) {
      return std::unique_ptr< ::grpc::ClientReader< ::grpc::testing::StreamingOutputCallResponse>>(StreamingOutputCallRaw(context, request));
    }
    std::unique_ptr< ::grpc::ClientAsyncReader< ::grpc::testing::StreamingOutputCallResponse>> AsyncStreamingOutputCall(::grpc::ClientContext* context, const ::grpc::testing::StreamingOutputCallRequest& request, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReader< ::grpc::testing::StreamingOutputCallResponse>>(AsyncStreamingOutputCallRaw(context, request, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReader< ::grpc::testing::StreamingOutputCallResponse>> PrepareAsyncStreamingOutputCall(::grpc::ClientContext* context, const ::grpc::testing::StreamingOutputCallRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReader< ::grpc::testing::StreamingOutputCallResponse>>(PrepareAsyncStreamingOutputCallRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientWriter< ::grpc::testing::StreamingInputCallRequest>> StreamingInputCall(::grpc::ClientContext* context, ::grpc::testing::StreamingInputCallResponse* response) {
      return std::unique_ptr< ::grpc::ClientWriter< ::grpc::testing::StreamingInputCallRequest>>(StreamingInputCallRaw(context, response));
    }
    std::unique_ptr< ::grpc::ClientAsyncWriter< ::grpc::testing::StreamingInputCallRequest>> AsyncStreamingInputCall(::grpc::ClientContext* context, ::grpc::testing::StreamingInputCallResponse* response, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncWriter< ::grpc::testing::StreamingInputCallRequest>>(AsyncStreamingInputCallRaw(context, response, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncWriter< ::grpc::testing::StreamingInputCallRequest>> PrepareAsyncStreamingInputCall(::grpc::ClientContext* context, ::grpc::testing::StreamingInputCallResponse* response, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncWriter< ::grpc::testing::StreamingInputCallRequest>>(PrepareAsyncStreamingInputCallRaw(context, response, cq));
    }
    std::unique_ptr< ::grpc::ClientReaderWriter< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>> FullDuplexCall(::grpc::ClientContext* context) {
      return std::unique_ptr< ::grpc::ClientReaderWriter< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>>(FullDuplexCallRaw(context));
    }
    std::unique_ptr<  ::grpc::ClientAsyncReaderWriter< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>> AsyncFullDuplexCall(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriter< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>>(AsyncFullDuplexCallRaw(context, cq, tag));
    }
    std::unique_ptr<  ::grpc::ClientAsyncReaderWriter< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>> PrepareAsyncFullDuplexCall(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriter< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>>(PrepareAsyncFullDuplexCallRaw(context, cq));
    }
    std::unique_ptr< ::grpc::ClientReaderWriter< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>> HalfDuplexCall(::grpc::ClientContext* context) {
      return std::unique_ptr< ::grpc::ClientReaderWriter< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>>(HalfDuplexCallRaw(context));
    }
    std::unique_ptr<  ::grpc::ClientAsyncReaderWriter< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>> AsyncHalfDuplexCall(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriter< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>>(AsyncHalfDuplexCallRaw(context, cq, tag));
    }
    std::unique_ptr<  ::grpc::ClientAsyncReaderWriter< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>> PrepareAsyncHalfDuplexCall(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriter< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>>(PrepareAsyncHalfDuplexCallRaw(context, cq));
    }
    ::grpc::Status UnimplementedCall(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::testing::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>> AsyncUnimplementedCall(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>>(AsyncUnimplementedCallRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>> PrepareAsyncUnimplementedCall(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>>(PrepareAsyncUnimplementedCallRaw(context, request, cq));
    }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    ::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>* AsyncEmptyCallRaw(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>* PrepareAsyncEmptyCallRaw(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::testing::SimpleResponse>* AsyncUnaryCallRaw(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::testing::SimpleResponse>* PrepareAsyncUnaryCallRaw(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::testing::SimpleResponse>* AsyncCacheableUnaryCallRaw(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::testing::SimpleResponse>* PrepareAsyncCacheableUnaryCallRaw(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientReader< ::grpc::testing::StreamingOutputCallResponse>* StreamingOutputCallRaw(::grpc::ClientContext* context, const ::grpc::testing::StreamingOutputCallRequest& request) override;
    ::grpc::ClientAsyncReader< ::grpc::testing::StreamingOutputCallResponse>* AsyncStreamingOutputCallRaw(::grpc::ClientContext* context, const ::grpc::testing::StreamingOutputCallRequest& request, ::grpc::CompletionQueue* cq, void* tag) override;
    ::grpc::ClientAsyncReader< ::grpc::testing::StreamingOutputCallResponse>* PrepareAsyncStreamingOutputCallRaw(::grpc::ClientContext* context, const ::grpc::testing::StreamingOutputCallRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientWriter< ::grpc::testing::StreamingInputCallRequest>* StreamingInputCallRaw(::grpc::ClientContext* context, ::grpc::testing::StreamingInputCallResponse* response) override;
    ::grpc::ClientAsyncWriter< ::grpc::testing::StreamingInputCallRequest>* AsyncStreamingInputCallRaw(::grpc::ClientContext* context, ::grpc::testing::StreamingInputCallResponse* response, ::grpc::CompletionQueue* cq, void* tag) override;
    ::grpc::ClientAsyncWriter< ::grpc::testing::StreamingInputCallRequest>* PrepareAsyncStreamingInputCallRaw(::grpc::ClientContext* context, ::grpc::testing::StreamingInputCallResponse* response, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientReaderWriter< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>* FullDuplexCallRaw(::grpc::ClientContext* context) override;
    ::grpc::ClientAsyncReaderWriter< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>* AsyncFullDuplexCallRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) override;
    ::grpc::ClientAsyncReaderWriter< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>* PrepareAsyncFullDuplexCallRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientReaderWriter< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>* HalfDuplexCallRaw(::grpc::ClientContext* context) override;
    ::grpc::ClientAsyncReaderWriter< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>* AsyncHalfDuplexCallRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) override;
    ::grpc::ClientAsyncReaderWriter< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>* PrepareAsyncHalfDuplexCallRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>* AsyncUnimplementedCallRaw(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>* PrepareAsyncUnimplementedCallRaw(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_EmptyCall_;
    const ::grpc::internal::RpcMethod rpcmethod_UnaryCall_;
    const ::grpc::internal::RpcMethod rpcmethod_CacheableUnaryCall_;
    const ::grpc::internal::RpcMethod rpcmethod_StreamingOutputCall_;
    const ::grpc::internal::RpcMethod rpcmethod_StreamingInputCall_;
    const ::grpc::internal::RpcMethod rpcmethod_FullDuplexCall_;
    const ::grpc::internal::RpcMethod rpcmethod_HalfDuplexCall_;
    const ::grpc::internal::RpcMethod rpcmethod_UnimplementedCall_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    // One empty request followed by one empty response.
    virtual ::grpc::Status EmptyCall(::grpc::ServerContext* context, const ::grpc::testing::Empty* request, ::grpc::testing::Empty* response);
    // One request followed by one response.
    virtual ::grpc::Status UnaryCall(::grpc::ServerContext* context, const ::grpc::testing::SimpleRequest* request, ::grpc::testing::SimpleResponse* response);
    // One request followed by one response. Response has cache control
    // headers set such that a caching HTTP proxy (such as GFE) can
    // satisfy subsequent requests.
    virtual ::grpc::Status CacheableUnaryCall(::grpc::ServerContext* context, const ::grpc::testing::SimpleRequest* request, ::grpc::testing::SimpleResponse* response);
    // One request followed by a sequence of responses (streamed download).
    // The server returns the payload with client desired type and sizes.
    virtual ::grpc::Status StreamingOutputCall(::grpc::ServerContext* context, const ::grpc::testing::StreamingOutputCallRequest* request, ::grpc::ServerWriter< ::grpc::testing::StreamingOutputCallResponse>* writer);
    // A sequence of requests followed by one response (streamed upload).
    // The server returns the aggregated size of client payload as the result.
    virtual ::grpc::Status StreamingInputCall(::grpc::ServerContext* context, ::grpc::ServerReader< ::grpc::testing::StreamingInputCallRequest>* reader, ::grpc::testing::StreamingInputCallResponse* response);
    // A sequence of requests with each request served by the server immediately.
    // As one request could lead to multiple responses, this interface
    // demonstrates the idea of full duplexing.
    virtual ::grpc::Status FullDuplexCall(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::StreamingOutputCallResponse, ::grpc::testing::StreamingOutputCallRequest>* stream);
    // A sequence of requests followed by a sequence of responses.
    // The server buffers all the client requests and then serves them in order. A
    // stream of responses are returned to the client when the server starts with
    // first request.
    virtual ::grpc::Status HalfDuplexCall(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::StreamingOutputCallResponse, ::grpc::testing::StreamingOutputCallRequest>* stream);
    // The test server will not implement this method. It will be used
    // to test the behavior when clients call unimplemented methods.
    virtual ::grpc::Status UnimplementedCall(::grpc::ServerContext* context, const ::grpc::testing::Empty* request, ::grpc::testing::Empty* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_EmptyCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_EmptyCall() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_EmptyCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status EmptyCall(::grpc::ServerContext* context, const ::grpc::testing::Empty* request, ::grpc::testing::Empty* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestEmptyCall(::grpc::ServerContext* context, ::grpc::testing::Empty* request, ::grpc::ServerAsyncResponseWriter< ::grpc::testing::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_UnaryCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_UnaryCall() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_UnaryCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UnaryCall(::grpc::ServerContext* context, const ::grpc::testing::SimpleRequest* request, ::grpc::testing::SimpleResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUnaryCall(::grpc::ServerContext* context, ::grpc::testing::SimpleRequest* request, ::grpc::ServerAsyncResponseWriter< ::grpc::testing::SimpleResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_CacheableUnaryCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_CacheableUnaryCall() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_CacheableUnaryCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CacheableUnaryCall(::grpc::ServerContext* context, const ::grpc::testing::SimpleRequest* request, ::grpc::testing::SimpleResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCacheableUnaryCall(::grpc::ServerContext* context, ::grpc::testing::SimpleRequest* request, ::grpc::ServerAsyncResponseWriter< ::grpc::testing::SimpleResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_StreamingOutputCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_StreamingOutputCall() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_StreamingOutputCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StreamingOutputCall(::grpc::ServerContext* context, const ::grpc::testing::StreamingOutputCallRequest* request, ::grpc::ServerWriter< ::grpc::testing::StreamingOutputCallResponse>* writer) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStreamingOutputCall(::grpc::ServerContext* context, ::grpc::testing::StreamingOutputCallRequest* request, ::grpc::ServerAsyncWriter< ::grpc::testing::StreamingOutputCallResponse>* writer, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncServerStreaming(3, context, request, writer, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_StreamingInputCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_StreamingInputCall() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_StreamingInputCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StreamingInputCall(::grpc::ServerContext* context, ::grpc::ServerReader< ::grpc::testing::StreamingInputCallRequest>* reader, ::grpc::testing::StreamingInputCallResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStreamingInputCall(::grpc::ServerContext* context, ::grpc::ServerAsyncReader< ::grpc::testing::StreamingInputCallResponse, ::grpc::testing::StreamingInputCallRequest>* reader, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncClientStreaming(4, context, reader, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_FullDuplexCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_FullDuplexCall() {
      ::grpc::Service::MarkMethodAsync(5);
    }
    ~WithAsyncMethod_FullDuplexCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FullDuplexCall(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::StreamingOutputCallResponse, ::grpc::testing::StreamingOutputCallRequest>* stream)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestFullDuplexCall(::grpc::ServerContext* context, ::grpc::ServerAsyncReaderWriter< ::grpc::testing::StreamingOutputCallResponse, ::grpc::testing::StreamingOutputCallRequest>* stream, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncBidiStreaming(5, context, stream, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_HalfDuplexCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_HalfDuplexCall() {
      ::grpc::Service::MarkMethodAsync(6);
    }
    ~WithAsyncMethod_HalfDuplexCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status HalfDuplexCall(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::StreamingOutputCallResponse, ::grpc::testing::StreamingOutputCallRequest>* stream)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestHalfDuplexCall(::grpc::ServerContext* context, ::grpc::ServerAsyncReaderWriter< ::grpc::testing::StreamingOutputCallResponse, ::grpc::testing::StreamingOutputCallRequest>* stream, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncBidiStreaming(6, context, stream, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_UnimplementedCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_UnimplementedCall() {
      ::grpc::Service::MarkMethodAsync(7);
    }
    ~WithAsyncMethod_UnimplementedCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UnimplementedCall(::grpc::ServerContext* context, const ::grpc::testing::Empty* request, ::grpc::testing::Empty* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUnimplementedCall(::grpc::ServerContext* context, ::grpc::testing::Empty* request, ::grpc::ServerAsyncResponseWriter< ::grpc::testing::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_EmptyCall<WithAsyncMethod_UnaryCall<WithAsyncMethod_CacheableUnaryCall<WithAsyncMethod_StreamingOutputCall<WithAsyncMethod_StreamingInputCall<WithAsyncMethod_FullDuplexCall<WithAsyncMethod_HalfDuplexCall<WithAsyncMethod_UnimplementedCall<Service > > > > > > > > AsyncService;
  template <class BaseClass>
  class WithGenericMethod_EmptyCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_EmptyCall() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_EmptyCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status EmptyCall(::grpc::ServerContext* context, const ::grpc::testing::Empty* request, ::grpc::testing::Empty* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_UnaryCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_UnaryCall() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_UnaryCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UnaryCall(::grpc::ServerContext* context, const ::grpc::testing::SimpleRequest* request, ::grpc::testing::SimpleResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_CacheableUnaryCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_CacheableUnaryCall() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_CacheableUnaryCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CacheableUnaryCall(::grpc::ServerContext* context, const ::grpc::testing::SimpleRequest* request, ::grpc::testing::SimpleResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_StreamingOutputCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_StreamingOutputCall() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_StreamingOutputCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StreamingOutputCall(::grpc::ServerContext* context, const ::grpc::testing::StreamingOutputCallRequest* request, ::grpc::ServerWriter< ::grpc::testing::StreamingOutputCallResponse>* writer) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_StreamingInputCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_StreamingInputCall() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_StreamingInputCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StreamingInputCall(::grpc::ServerContext* context, ::grpc::ServerReader< ::grpc::testing::StreamingInputCallRequest>* reader, ::grpc::testing::StreamingInputCallResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_FullDuplexCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_FullDuplexCall() {
      ::grpc::Service::MarkMethodGeneric(5);
    }
    ~WithGenericMethod_FullDuplexCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FullDuplexCall(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::StreamingOutputCallResponse, ::grpc::testing::StreamingOutputCallRequest>* stream)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_HalfDuplexCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_HalfDuplexCall() {
      ::grpc::Service::MarkMethodGeneric(6);
    }
    ~WithGenericMethod_HalfDuplexCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status HalfDuplexCall(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::StreamingOutputCallResponse, ::grpc::testing::StreamingOutputCallRequest>* stream)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_UnimplementedCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_UnimplementedCall() {
      ::grpc::Service::MarkMethodGeneric(7);
    }
    ~WithGenericMethod_UnimplementedCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UnimplementedCall(::grpc::ServerContext* context, const ::grpc::testing::Empty* request, ::grpc::testing::Empty* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_EmptyCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_EmptyCall() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_EmptyCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status EmptyCall(::grpc::ServerContext* context, const ::grpc::testing::Empty* request, ::grpc::testing::Empty* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestEmptyCall(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_UnaryCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_UnaryCall() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_UnaryCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UnaryCall(::grpc::ServerContext* context, const ::grpc::testing::SimpleRequest* request, ::grpc::testing::SimpleResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUnaryCall(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_CacheableUnaryCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_CacheableUnaryCall() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_CacheableUnaryCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CacheableUnaryCall(::grpc::ServerContext* context, const ::grpc::testing::SimpleRequest* request, ::grpc::testing::SimpleResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCacheableUnaryCall(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_StreamingOutputCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_StreamingOutputCall() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_StreamingOutputCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StreamingOutputCall(::grpc::ServerContext* context, const ::grpc::testing::StreamingOutputCallRequest* request, ::grpc::ServerWriter< ::grpc::testing::StreamingOutputCallResponse>* writer) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStreamingOutputCall(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncWriter< ::grpc::ByteBuffer>* writer, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncServerStreaming(3, context, request, writer, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_StreamingInputCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_StreamingInputCall() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_StreamingInputCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StreamingInputCall(::grpc::ServerContext* context, ::grpc::ServerReader< ::grpc::testing::StreamingInputCallRequest>* reader, ::grpc::testing::StreamingInputCallResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStreamingInputCall(::grpc::ServerContext* context, ::grpc::ServerAsyncReader< ::grpc::ByteBuffer, ::grpc::ByteBuffer>* reader, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncClientStreaming(4, context, reader, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_FullDuplexCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_FullDuplexCall() {
      ::grpc::Service::MarkMethodRaw(5);
    }
    ~WithRawMethod_FullDuplexCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FullDuplexCall(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::StreamingOutputCallResponse, ::grpc::testing::StreamingOutputCallRequest>* stream)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestFullDuplexCall(::grpc::ServerContext* context, ::grpc::ServerAsyncReaderWriter< ::grpc::ByteBuffer, ::grpc::ByteBuffer>* stream, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncBidiStreaming(5, context, stream, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_HalfDuplexCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_HalfDuplexCall() {
      ::grpc::Service::MarkMethodRaw(6);
    }
    ~WithRawMethod_HalfDuplexCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status HalfDuplexCall(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::StreamingOutputCallResponse, ::grpc::testing::StreamingOutputCallRequest>* stream)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestHalfDuplexCall(::grpc::ServerContext* context, ::grpc::ServerAsyncReaderWriter< ::grpc::ByteBuffer, ::grpc::ByteBuffer>* stream, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncBidiStreaming(6, context, stream, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_UnimplementedCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_UnimplementedCall() {
      ::grpc::Service::MarkMethodRaw(7);
    }
    ~WithRawMethod_UnimplementedCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UnimplementedCall(::grpc::ServerContext* context, const ::grpc::testing::Empty* request, ::grpc::testing::Empty* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUnimplementedCall(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_EmptyCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithStreamedUnaryMethod_EmptyCall() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler< ::grpc::testing::Empty, ::grpc::testing::Empty>(std::bind(&WithStreamedUnaryMethod_EmptyCall<BaseClass>::StreamedEmptyCall, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_EmptyCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status EmptyCall(::grpc::ServerContext* context, const ::grpc::testing::Empty* request, ::grpc::testing::Empty* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedEmptyCall(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::grpc::testing::Empty,::grpc::testing::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_UnaryCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithStreamedUnaryMethod_UnaryCall() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>(std::bind(&WithStreamedUnaryMethod_UnaryCall<BaseClass>::StreamedUnaryCall, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_UnaryCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status UnaryCall(::grpc::ServerContext* context, const ::grpc::testing::SimpleRequest* request, ::grpc::testing::SimpleResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedUnaryCall(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::grpc::testing::SimpleRequest,::grpc::testing::SimpleResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_CacheableUnaryCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithStreamedUnaryMethod_CacheableUnaryCall() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler< ::grpc::testing::SimpleRequest, ::grpc::testing::SimpleResponse>(std::bind(&WithStreamedUnaryMethod_CacheableUnaryCall<BaseClass>::StreamedCacheableUnaryCall, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_CacheableUnaryCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status CacheableUnaryCall(::grpc::ServerContext* context, const ::grpc::testing::SimpleRequest* request, ::grpc::testing::SimpleResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedCacheableUnaryCall(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::grpc::testing::SimpleRequest,::grpc::testing::SimpleResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_UnimplementedCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithStreamedUnaryMethod_UnimplementedCall() {
      ::grpc::Service::MarkMethodStreamed(7,
        new ::grpc::internal::StreamedUnaryHandler< ::grpc::testing::Empty, ::grpc::testing::Empty>(std::bind(&WithStreamedUnaryMethod_UnimplementedCall<BaseClass>::StreamedUnimplementedCall, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_UnimplementedCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status UnimplementedCall(::grpc::ServerContext* context, const ::grpc::testing::Empty* request, ::grpc::testing::Empty* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedUnimplementedCall(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::grpc::testing::Empty,::grpc::testing::Empty>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_EmptyCall<WithStreamedUnaryMethod_UnaryCall<WithStreamedUnaryMethod_CacheableUnaryCall<WithStreamedUnaryMethod_UnimplementedCall<Service > > > > StreamedUnaryService;
  template <class BaseClass>
  class WithSplitStreamingMethod_StreamingOutputCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithSplitStreamingMethod_StreamingOutputCall() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::SplitServerStreamingHandler< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>(std::bind(&WithSplitStreamingMethod_StreamingOutputCall<BaseClass>::StreamedStreamingOutputCall, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithSplitStreamingMethod_StreamingOutputCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status StreamingOutputCall(::grpc::ServerContext* context, const ::grpc::testing::StreamingOutputCallRequest* request, ::grpc::ServerWriter< ::grpc::testing::StreamingOutputCallResponse>* writer) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with split streamed
    virtual ::grpc::Status StreamedStreamingOutputCall(::grpc::ServerContext* context, ::grpc::ServerSplitStreamer< ::grpc::testing::StreamingOutputCallRequest,::grpc::testing::StreamingOutputCallResponse>* server_split_streamer) = 0;
  };
  typedef WithSplitStreamingMethod_StreamingOutputCall<Service > SplitStreamedService;
  typedef WithStreamedUnaryMethod_EmptyCall<WithStreamedUnaryMethod_UnaryCall<WithStreamedUnaryMethod_CacheableUnaryCall<WithSplitStreamingMethod_StreamingOutputCall<WithStreamedUnaryMethod_UnimplementedCall<Service > > > > > StreamedService;
};

// A simple service NOT implemented at servers so clients can test for
// that case.
class UnimplementedService final {
 public:
  static constexpr char const* service_full_name() {
    return "grpc.testing.UnimplementedService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    // A call that no server should implement
    virtual ::grpc::Status UnimplementedCall(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::testing::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>> AsyncUnimplementedCall(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>>(AsyncUnimplementedCallRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>> PrepareAsyncUnimplementedCall(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>>(PrepareAsyncUnimplementedCallRaw(context, request, cq));
    }
  private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>* AsyncUnimplementedCallRaw(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>* PrepareAsyncUnimplementedCallRaw(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel);
    ::grpc::Status UnimplementedCall(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::testing::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>> AsyncUnimplementedCall(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>>(AsyncUnimplementedCallRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>> PrepareAsyncUnimplementedCall(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>>(PrepareAsyncUnimplementedCallRaw(context, request, cq));
    }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    ::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>* AsyncUnimplementedCallRaw(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>* PrepareAsyncUnimplementedCallRaw(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_UnimplementedCall_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    // A call that no server should implement
    virtual ::grpc::Status UnimplementedCall(::grpc::ServerContext* context, const ::grpc::testing::Empty* request, ::grpc::testing::Empty* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_UnimplementedCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_UnimplementedCall() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_UnimplementedCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UnimplementedCall(::grpc::ServerContext* context, const ::grpc::testing::Empty* request, ::grpc::testing::Empty* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUnimplementedCall(::grpc::ServerContext* context, ::grpc::testing::Empty* request, ::grpc::ServerAsyncResponseWriter< ::grpc::testing::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_UnimplementedCall<Service > AsyncService;
  template <class BaseClass>
  class WithGenericMethod_UnimplementedCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_UnimplementedCall() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_UnimplementedCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UnimplementedCall(::grpc::ServerContext* context, const ::grpc::testing::Empty* request, ::grpc::testing::Empty* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_UnimplementedCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_UnimplementedCall() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_UnimplementedCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UnimplementedCall(::grpc::ServerContext* context, const ::grpc::testing::Empty* request, ::grpc::testing::Empty* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUnimplementedCall(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_UnimplementedCall : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithStreamedUnaryMethod_UnimplementedCall() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler< ::grpc::testing::Empty, ::grpc::testing::Empty>(std::bind(&WithStreamedUnaryMethod_UnimplementedCall<BaseClass>::StreamedUnimplementedCall, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_UnimplementedCall() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status UnimplementedCall(::grpc::ServerContext* context, const ::grpc::testing::Empty* request, ::grpc::testing::Empty* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedUnimplementedCall(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::grpc::testing::Empty,::grpc::testing::Empty>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_UnimplementedCall<Service > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_UnimplementedCall<Service > StreamedService;
};

// A service used to control reconnect server.
class ReconnectService final {
 public:
  static constexpr char const* service_full_name() {
    return "grpc.testing.ReconnectService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status Start(::grpc::ClientContext* context, const ::grpc::testing::ReconnectParams& request, ::grpc::testing::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>> AsyncStart(::grpc::ClientContext* context, const ::grpc::testing::ReconnectParams& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>>(AsyncStartRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>> PrepareAsyncStart(::grpc::ClientContext* context, const ::grpc::testing::ReconnectParams& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>>(PrepareAsyncStartRaw(context, request, cq));
    }
    virtual ::grpc::Status Stop(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::testing::ReconnectInfo* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::ReconnectInfo>> AsyncStop(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::ReconnectInfo>>(AsyncStopRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::ReconnectInfo>> PrepareAsyncStop(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::ReconnectInfo>>(PrepareAsyncStopRaw(context, request, cq));
    }
  private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>* AsyncStartRaw(::grpc::ClientContext* context, const ::grpc::testing::ReconnectParams& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>* PrepareAsyncStartRaw(::grpc::ClientContext* context, const ::grpc::testing::ReconnectParams& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::ReconnectInfo>* AsyncStopRaw(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::ReconnectInfo>* PrepareAsyncStopRaw(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel);
    ::grpc::Status Start(::grpc::ClientContext* context, const ::grpc::testing::ReconnectParams& request, ::grpc::testing::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>> AsyncStart(::grpc::ClientContext* context, const ::grpc::testing::ReconnectParams& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>>(AsyncStartRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>> PrepareAsyncStart(::grpc::ClientContext* context, const ::grpc::testing::ReconnectParams& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>>(PrepareAsyncStartRaw(context, request, cq));
    }
    ::grpc::Status Stop(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::testing::ReconnectInfo* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::ReconnectInfo>> AsyncStop(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::ReconnectInfo>>(AsyncStopRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::ReconnectInfo>> PrepareAsyncStop(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::ReconnectInfo>>(PrepareAsyncStopRaw(context, request, cq));
    }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    ::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>* AsyncStartRaw(::grpc::ClientContext* context, const ::grpc::testing::ReconnectParams& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::testing::Empty>* PrepareAsyncStartRaw(::grpc::ClientContext* context, const ::grpc::testing::ReconnectParams& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::testing::ReconnectInfo>* AsyncStopRaw(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::testing::ReconnectInfo>* PrepareAsyncStopRaw(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_Start_;
    const ::grpc::internal::RpcMethod rpcmethod_Stop_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status Start(::grpc::ServerContext* context, const ::grpc::testing::ReconnectParams* request, ::grpc::testing::Empty* response);
    virtual ::grpc::Status Stop(::grpc::ServerContext* context, const ::grpc::testing::Empty* request, ::grpc::testing::ReconnectInfo* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_Start : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_Start() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_Start() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Start(::grpc::ServerContext* context, const ::grpc::testing::ReconnectParams* request, ::grpc::testing::Empty* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStart(::grpc::ServerContext* context, ::grpc::testing::ReconnectParams* request, ::grpc::ServerAsyncResponseWriter< ::grpc::testing::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_Stop : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_Stop() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_Stop() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Stop(::grpc::ServerContext* context, const ::grpc::testing::Empty* request, ::grpc::testing::ReconnectInfo* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStop(::grpc::ServerContext* context, ::grpc::testing::Empty* request, ::grpc::ServerAsyncResponseWriter< ::grpc::testing::ReconnectInfo>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_Start<WithAsyncMethod_Stop<Service > > AsyncService;
  template <class BaseClass>
  class WithGenericMethod_Start : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_Start() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_Start() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Start(::grpc::ServerContext* context, const ::grpc::testing::ReconnectParams* request, ::grpc::testing::Empty* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_Stop : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_Stop() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_Stop() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Stop(::grpc::ServerContext* context, const ::grpc::testing::Empty* request, ::grpc::testing::ReconnectInfo* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_Start : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_Start() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_Start() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Start(::grpc::ServerContext* context, const ::grpc::testing::ReconnectParams* request, ::grpc::testing::Empty* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStart(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_Stop : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_Stop() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_Stop() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Stop(::grpc::ServerContext* context, const ::grpc::testing::Empty* request, ::grpc::testing::ReconnectInfo* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStop(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_Start : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithStreamedUnaryMethod_Start() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler< ::grpc::testing::ReconnectParams, ::grpc::testing::Empty>(std::bind(&WithStreamedUnaryMethod_Start<BaseClass>::StreamedStart, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_Start() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status Start(::grpc::ServerContext* context, const ::grpc::testing::ReconnectParams* request, ::grpc::testing::Empty* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedStart(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::grpc::testing::ReconnectParams,::grpc::testing::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_Stop : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithStreamedUnaryMethod_Stop() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler< ::grpc::testing::Empty, ::grpc::testing::ReconnectInfo>(std::bind(&WithStreamedUnaryMethod_Stop<BaseClass>::StreamedStop, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_Stop() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status Stop(::grpc::ServerContext* context, const ::grpc::testing::Empty* request, ::grpc::testing::ReconnectInfo* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedStop(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::grpc::testing::Empty,::grpc::testing::ReconnectInfo>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_Start<WithStreamedUnaryMethod_Stop<Service > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_Start<WithStreamedUnaryMethod_Stop<Service > > StreamedService;
};

}  // namespace testing
}  // namespace grpc


#endif  // GRPC_src_2fproto_2fgrpc_2ftesting_2ftest_2eproto__INCLUDED
