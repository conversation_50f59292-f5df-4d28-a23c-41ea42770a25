// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: src/proto/grpc/testing/test.proto

#include "src/proto/grpc/testing/test.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)
namespace grpc {
namespace testing {
}  // namespace testing
}  // namespace grpc
namespace protobuf_src_2fproto_2fgrpc_2ftesting_2ftest_2eproto {
const ::google::protobuf::uint32 TableStruct::offsets[1] = {};
static const ::google::protobuf::internal::MigrationSchema* schemas = NULL;
static const ::google::protobuf::Message* const* file_default_instances = NULL;

void protobuf_AssignDescriptors() {
  AddDescriptors();
  ::google::protobuf::MessageFactory* factory = NULL;
  AssignDescriptors(
      "src/proto/grpc/testing/test.proto", schemas, file_default_instances, TableStruct::offsets, factory,
      NULL, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n!src/proto/grpc/testing/test.proto\022\014grp"
      "c.testing\032\"src/proto/grpc/testing/empty."
      "proto\032%src/proto/grpc/testing/messages.p"
      "roto2\313\005\n\013TestService\0225\n\tEmptyCall\022\023.grpc"
      ".testing.Empty\032\023.grpc.testing.Empty\022F\n\tU"
      "naryCall\022\033.grpc.testing.SimpleRequest\032\034."
      "grpc.testing.SimpleResponse\022O\n\022Cacheable"
      "UnaryCall\022\033.grpc.testing.SimpleRequest\032\034"
      ".grpc.testing.SimpleResponse\022l\n\023Streamin"
      "gOutputCall\022(.grpc.testing.StreamingOutp"
      "utCallRequest\032).grpc.testing.StreamingOu"
      "tputCallResponse0\001\022i\n\022StreamingInputCall"
      "\022\'.grpc.testing.StreamingInputCallReques"
      "t\032(.grpc.testing.StreamingInputCallRespo"
      "nse(\001\022i\n\016FullDuplexCall\022(.grpc.testing.S"
      "treamingOutputCallRequest\032).grpc.testing"
      ".StreamingOutputCallResponse(\0010\001\022i\n\016Half"
      "DuplexCall\022(.grpc.testing.StreamingOutpu"
      "tCallRequest\032).grpc.testing.StreamingOut"
      "putCallResponse(\0010\001\022=\n\021UnimplementedCall"
      "\022\023.grpc.testing.Empty\032\023.grpc.testing.Emp"
      "ty2U\n\024UnimplementedService\022=\n\021Unimplemen"
      "tedCall\022\023.grpc.testing.Empty\032\023.grpc.test"
      "ing.Empty2\211\001\n\020ReconnectService\022;\n\005Start\022"
      "\035.grpc.testing.ReconnectParams\032\023.grpc.te"
      "sting.Empty\0228\n\004Stop\022\023.grpc.testing.Empty"
      "\032\033.grpc.testing.ReconnectInfob\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 1077);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "src/proto/grpc/testing/test.proto", &protobuf_RegisterTypes);
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fempty_2eproto::AddDescriptors();
  ::protobuf_src_2fproto_2fgrpc_2ftesting_2fmessages_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_src_2fproto_2fgrpc_2ftesting_2ftest_2eproto
namespace grpc {
namespace testing {

// @@protoc_insertion_point(namespace_scope)
}  // namespace testing
}  // namespace grpc

// @@protoc_insertion_point(global_scope)
