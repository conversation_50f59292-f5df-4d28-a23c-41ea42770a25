// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: src/proto/grpc/testing/test.proto

#include "src/proto/grpc/testing/test.pb.h"
#include "src/proto/grpc/testing/test.grpc.pb.h"

#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/sync_stream.h>
#include <gmock/gmock.h>
namespace grpc {
namespace testing {

class MockTestServiceStub : public TestService::StubInterface {
 public:
  MOCK_METHOD3(EmptyCall, ::grpc::Status(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::testing::Empty* response));
  MOCK_METHOD3(AsyncEmptyCallRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>*(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncEmptyCallRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>*(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(UnaryCall, ::grpc::Status(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::testing::SimpleResponse* response));
  MOCK_METHOD3(AsyncUnaryCallRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::SimpleResponse>*(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncUnaryCallRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::SimpleResponse>*(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(CacheableUnaryCall, ::grpc::Status(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::testing::SimpleResponse* response));
  MOCK_METHOD3(AsyncCacheableUnaryCallRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::SimpleResponse>*(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncCacheableUnaryCallRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::SimpleResponse>*(::grpc::ClientContext* context, const ::grpc::testing::SimpleRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD2(StreamingOutputCallRaw, ::grpc::ClientReaderInterface< ::grpc::testing::StreamingOutputCallResponse>*(::grpc::ClientContext* context, const ::grpc::testing::StreamingOutputCallRequest& request));
  MOCK_METHOD4(AsyncStreamingOutputCallRaw, ::grpc::ClientAsyncReaderInterface< ::grpc::testing::StreamingOutputCallResponse>*(::grpc::ClientContext* context, const ::grpc::testing::StreamingOutputCallRequest& request, ::grpc::CompletionQueue* cq, void* tag));
  MOCK_METHOD3(PrepareAsyncStreamingOutputCallRaw, ::grpc::ClientAsyncReaderInterface< ::grpc::testing::StreamingOutputCallResponse>*(::grpc::ClientContext* context, const ::grpc::testing::StreamingOutputCallRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD2(StreamingInputCallRaw, ::grpc::ClientWriterInterface< ::grpc::testing::StreamingInputCallRequest>*(::grpc::ClientContext* context, ::grpc::testing::StreamingInputCallResponse* response));
  MOCK_METHOD4(AsyncStreamingInputCallRaw, ::grpc::ClientAsyncWriterInterface< ::grpc::testing::StreamingInputCallRequest>*(::grpc::ClientContext* context, ::grpc::testing::StreamingInputCallResponse* response, ::grpc::CompletionQueue* cq, void* tag));
  MOCK_METHOD3(PrepareAsyncStreamingInputCallRaw, ::grpc::ClientAsyncWriterInterface< ::grpc::testing::StreamingInputCallRequest>*(::grpc::ClientContext* context, ::grpc::testing::StreamingInputCallResponse* response, ::grpc::CompletionQueue* cq));
  MOCK_METHOD1(FullDuplexCallRaw, ::grpc::ClientReaderWriterInterface< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>*(::grpc::ClientContext* context));
  MOCK_METHOD3(AsyncFullDuplexCallRaw, ::grpc::ClientAsyncReaderWriterInterface<::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>*(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag));
  MOCK_METHOD2(PrepareAsyncFullDuplexCallRaw, ::grpc::ClientAsyncReaderWriterInterface<::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>*(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq));
  MOCK_METHOD1(HalfDuplexCallRaw, ::grpc::ClientReaderWriterInterface< ::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>*(::grpc::ClientContext* context));
  MOCK_METHOD3(AsyncHalfDuplexCallRaw, ::grpc::ClientAsyncReaderWriterInterface<::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>*(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag));
  MOCK_METHOD2(PrepareAsyncHalfDuplexCallRaw, ::grpc::ClientAsyncReaderWriterInterface<::grpc::testing::StreamingOutputCallRequest, ::grpc::testing::StreamingOutputCallResponse>*(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(UnimplementedCall, ::grpc::Status(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::testing::Empty* response));
  MOCK_METHOD3(AsyncUnimplementedCallRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>*(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncUnimplementedCallRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>*(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq));
};

class MockUnimplementedServiceStub : public UnimplementedService::StubInterface {
 public:
  MOCK_METHOD3(UnimplementedCall, ::grpc::Status(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::testing::Empty* response));
  MOCK_METHOD3(AsyncUnimplementedCallRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>*(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncUnimplementedCallRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>*(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq));
};

class MockReconnectServiceStub : public ReconnectService::StubInterface {
 public:
  MOCK_METHOD3(Start, ::grpc::Status(::grpc::ClientContext* context, const ::grpc::testing::ReconnectParams& request, ::grpc::testing::Empty* response));
  MOCK_METHOD3(AsyncStartRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>*(::grpc::ClientContext* context, const ::grpc::testing::ReconnectParams& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncStartRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Empty>*(::grpc::ClientContext* context, const ::grpc::testing::ReconnectParams& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(Stop, ::grpc::Status(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::testing::ReconnectInfo* response));
  MOCK_METHOD3(AsyncStopRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::ReconnectInfo>*(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncStopRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::ReconnectInfo>*(::grpc::ClientContext* context, const ::grpc::testing::Empty& request, ::grpc::CompletionQueue* cq));
};

} // namespace grpc
} // namespace testing

