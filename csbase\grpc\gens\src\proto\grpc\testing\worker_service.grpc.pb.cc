// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: src/proto/grpc/testing/worker_service.proto

#include "src/proto/grpc/testing/worker_service.pb.h"
#include "src/proto/grpc/testing/worker_service.grpc.pb.h"

#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/method_handler_impl.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace grpc {
namespace testing {

static const char* WorkerService_method_names[] = {
  "/grpc.testing.WorkerService/RunServer",
  "/grpc.testing.WorkerService/RunClient",
  "/grpc.testing.WorkerService/CoreCount",
  "/grpc.testing.WorkerService/QuitWorker",
};

std::unique_ptr< WorkerService::Stub> WorkerService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< WorkerService::Stub> stub(new WorkerService::Stub(channel));
  return stub;
}

WorkerService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel)
  : channel_(channel), rpcmethod_RunServer_(WorkerService_method_names[0], ::grpc::internal::RpcMethod::BIDI_STREAMING, channel)
  , rpcmethod_RunClient_(WorkerService_method_names[1], ::grpc::internal::RpcMethod::BIDI_STREAMING, channel)
  , rpcmethod_CoreCount_(WorkerService_method_names[2], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_QuitWorker_(WorkerService_method_names[3], ::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::ClientReaderWriter< ::grpc::testing::ServerArgs, ::grpc::testing::ServerStatus>* WorkerService::Stub::RunServerRaw(::grpc::ClientContext* context) {
  return ::grpc::internal::ClientReaderWriterFactory< ::grpc::testing::ServerArgs, ::grpc::testing::ServerStatus>::Create(channel_.get(), rpcmethod_RunServer_, context);
}

::grpc::ClientAsyncReaderWriter< ::grpc::testing::ServerArgs, ::grpc::testing::ServerStatus>* WorkerService::Stub::AsyncRunServerRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncReaderWriterFactory< ::grpc::testing::ServerArgs, ::grpc::testing::ServerStatus>::Create(channel_.get(), cq, rpcmethod_RunServer_, context, true, tag);
}

::grpc::ClientAsyncReaderWriter< ::grpc::testing::ServerArgs, ::grpc::testing::ServerStatus>* WorkerService::Stub::PrepareAsyncRunServerRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncReaderWriterFactory< ::grpc::testing::ServerArgs, ::grpc::testing::ServerStatus>::Create(channel_.get(), cq, rpcmethod_RunServer_, context, false, nullptr);
}

::grpc::ClientReaderWriter< ::grpc::testing::ClientArgs, ::grpc::testing::ClientStatus>* WorkerService::Stub::RunClientRaw(::grpc::ClientContext* context) {
  return ::grpc::internal::ClientReaderWriterFactory< ::grpc::testing::ClientArgs, ::grpc::testing::ClientStatus>::Create(channel_.get(), rpcmethod_RunClient_, context);
}

::grpc::ClientAsyncReaderWriter< ::grpc::testing::ClientArgs, ::grpc::testing::ClientStatus>* WorkerService::Stub::AsyncRunClientRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncReaderWriterFactory< ::grpc::testing::ClientArgs, ::grpc::testing::ClientStatus>::Create(channel_.get(), cq, rpcmethod_RunClient_, context, true, tag);
}

::grpc::ClientAsyncReaderWriter< ::grpc::testing::ClientArgs, ::grpc::testing::ClientStatus>* WorkerService::Stub::PrepareAsyncRunClientRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncReaderWriterFactory< ::grpc::testing::ClientArgs, ::grpc::testing::ClientStatus>::Create(channel_.get(), cq, rpcmethod_RunClient_, context, false, nullptr);
}

::grpc::Status WorkerService::Stub::CoreCount(::grpc::ClientContext* context, const ::grpc::testing::CoreRequest& request, ::grpc::testing::CoreResponse* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_CoreCount_, context, request, response);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::CoreResponse>* WorkerService::Stub::AsyncCoreCountRaw(::grpc::ClientContext* context, const ::grpc::testing::CoreRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::CoreResponse>::Create(channel_.get(), cq, rpcmethod_CoreCount_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::CoreResponse>* WorkerService::Stub::PrepareAsyncCoreCountRaw(::grpc::ClientContext* context, const ::grpc::testing::CoreRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::CoreResponse>::Create(channel_.get(), cq, rpcmethod_CoreCount_, context, request, false);
}

::grpc::Status WorkerService::Stub::QuitWorker(::grpc::ClientContext* context, const ::grpc::testing::Void& request, ::grpc::testing::Void* response) {
  return ::grpc::internal::BlockingUnaryCall(channel_.get(), rpcmethod_QuitWorker_, context, request, response);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::Void>* WorkerService::Stub::AsyncQuitWorkerRaw(::grpc::ClientContext* context, const ::grpc::testing::Void& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::Void>::Create(channel_.get(), cq, rpcmethod_QuitWorker_, context, request, true);
}

::grpc::ClientAsyncResponseReader< ::grpc::testing::Void>* WorkerService::Stub::PrepareAsyncQuitWorkerRaw(::grpc::ClientContext* context, const ::grpc::testing::Void& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderFactory< ::grpc::testing::Void>::Create(channel_.get(), cq, rpcmethod_QuitWorker_, context, request, false);
}

WorkerService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WorkerService_method_names[0],
      ::grpc::internal::RpcMethod::BIDI_STREAMING,
      new ::grpc::internal::BidiStreamingHandler< WorkerService::Service, ::grpc::testing::ServerArgs, ::grpc::testing::ServerStatus>(
          std::mem_fn(&WorkerService::Service::RunServer), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WorkerService_method_names[1],
      ::grpc::internal::RpcMethod::BIDI_STREAMING,
      new ::grpc::internal::BidiStreamingHandler< WorkerService::Service, ::grpc::testing::ClientArgs, ::grpc::testing::ClientStatus>(
          std::mem_fn(&WorkerService::Service::RunClient), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WorkerService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WorkerService::Service, ::grpc::testing::CoreRequest, ::grpc::testing::CoreResponse>(
          std::mem_fn(&WorkerService::Service::CoreCount), this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      WorkerService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< WorkerService::Service, ::grpc::testing::Void, ::grpc::testing::Void>(
          std::mem_fn(&WorkerService::Service::QuitWorker), this)));
}

WorkerService::Service::~Service() {
}

::grpc::Status WorkerService::Service::RunServer(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::ServerStatus, ::grpc::testing::ServerArgs>* stream) {
  (void) context;
  (void) stream;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WorkerService::Service::RunClient(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::ClientStatus, ::grpc::testing::ClientArgs>* stream) {
  (void) context;
  (void) stream;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WorkerService::Service::CoreCount(::grpc::ServerContext* context, const ::grpc::testing::CoreRequest* request, ::grpc::testing::CoreResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status WorkerService::Service::QuitWorker(::grpc::ServerContext* context, const ::grpc::testing::Void* request, ::grpc::testing::Void* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace grpc
}  // namespace testing

