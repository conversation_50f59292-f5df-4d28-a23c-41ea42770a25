// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: src/proto/grpc/testing/worker_service.proto
// Original file comments:
// Copyright 2015 gRPC authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// An integration test service that covers all the method signature permutations
// of unary/streaming requests/responses.
#ifndef GRPC_src_2fproto_2fgrpc_2ftesting_2fworker_5fservice_2eproto__INCLUDED
#define GRPC_src_2fproto_2fgrpc_2ftesting_2fworker_5fservice_2eproto__INCLUDED

#include "src/proto/grpc/testing/worker_service.pb.h"

#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/method_handler_impl.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace grpc {
class CompletionQueue;
class Channel;
class ServerCompletionQueue;
class ServerContext;
}  // namespace grpc

namespace grpc {
namespace testing {

class WorkerService final {
 public:
  static constexpr char const* service_full_name() {
    return "grpc.testing.WorkerService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    // Start server with specified workload.
    // First request sent specifies the ServerConfig followed by ServerStatus
    // response. After that, a "Mark" can be sent anytime to request the latest
    // stats. Closing the stream will initiate shutdown of the test server
    // and once the shutdown has finished, the OK status is sent to terminate
    // this RPC.
    std::unique_ptr< ::grpc::ClientReaderWriterInterface< ::grpc::testing::ServerArgs, ::grpc::testing::ServerStatus>> RunServer(::grpc::ClientContext* context) {
      return std::unique_ptr< ::grpc::ClientReaderWriterInterface< ::grpc::testing::ServerArgs, ::grpc::testing::ServerStatus>>(RunServerRaw(context));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::ServerArgs, ::grpc::testing::ServerStatus>> AsyncRunServer(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::ServerArgs, ::grpc::testing::ServerStatus>>(AsyncRunServerRaw(context, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::ServerArgs, ::grpc::testing::ServerStatus>> PrepareAsyncRunServer(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::ServerArgs, ::grpc::testing::ServerStatus>>(PrepareAsyncRunServerRaw(context, cq));
    }
    // Start client with specified workload.
    // First request sent specifies the ClientConfig followed by ClientStatus
    // response. After that, a "Mark" can be sent anytime to request the latest
    // stats. Closing the stream will initiate shutdown of the test client
    // and once the shutdown has finished, the OK status is sent to terminate
    // this RPC.
    std::unique_ptr< ::grpc::ClientReaderWriterInterface< ::grpc::testing::ClientArgs, ::grpc::testing::ClientStatus>> RunClient(::grpc::ClientContext* context) {
      return std::unique_ptr< ::grpc::ClientReaderWriterInterface< ::grpc::testing::ClientArgs, ::grpc::testing::ClientStatus>>(RunClientRaw(context));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::ClientArgs, ::grpc::testing::ClientStatus>> AsyncRunClient(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::ClientArgs, ::grpc::testing::ClientStatus>>(AsyncRunClientRaw(context, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::ClientArgs, ::grpc::testing::ClientStatus>> PrepareAsyncRunClient(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::ClientArgs, ::grpc::testing::ClientStatus>>(PrepareAsyncRunClientRaw(context, cq));
    }
    // Just return the core count - unary call
    virtual ::grpc::Status CoreCount(::grpc::ClientContext* context, const ::grpc::testing::CoreRequest& request, ::grpc::testing::CoreResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::CoreResponse>> AsyncCoreCount(::grpc::ClientContext* context, const ::grpc::testing::CoreRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::CoreResponse>>(AsyncCoreCountRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::CoreResponse>> PrepareAsyncCoreCount(::grpc::ClientContext* context, const ::grpc::testing::CoreRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::CoreResponse>>(PrepareAsyncCoreCountRaw(context, request, cq));
    }
    // Quit this worker
    virtual ::grpc::Status QuitWorker(::grpc::ClientContext* context, const ::grpc::testing::Void& request, ::grpc::testing::Void* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Void>> AsyncQuitWorker(::grpc::ClientContext* context, const ::grpc::testing::Void& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Void>>(AsyncQuitWorkerRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Void>> PrepareAsyncQuitWorker(::grpc::ClientContext* context, const ::grpc::testing::Void& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Void>>(PrepareAsyncQuitWorkerRaw(context, request, cq));
    }
  private:
    virtual ::grpc::ClientReaderWriterInterface< ::grpc::testing::ServerArgs, ::grpc::testing::ServerStatus>* RunServerRaw(::grpc::ClientContext* context) = 0;
    virtual ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::ServerArgs, ::grpc::testing::ServerStatus>* AsyncRunServerRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) = 0;
    virtual ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::ServerArgs, ::grpc::testing::ServerStatus>* PrepareAsyncRunServerRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientReaderWriterInterface< ::grpc::testing::ClientArgs, ::grpc::testing::ClientStatus>* RunClientRaw(::grpc::ClientContext* context) = 0;
    virtual ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::ClientArgs, ::grpc::testing::ClientStatus>* AsyncRunClientRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) = 0;
    virtual ::grpc::ClientAsyncReaderWriterInterface< ::grpc::testing::ClientArgs, ::grpc::testing::ClientStatus>* PrepareAsyncRunClientRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::CoreResponse>* AsyncCoreCountRaw(::grpc::ClientContext* context, const ::grpc::testing::CoreRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::CoreResponse>* PrepareAsyncCoreCountRaw(::grpc::ClientContext* context, const ::grpc::testing::CoreRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Void>* AsyncQuitWorkerRaw(::grpc::ClientContext* context, const ::grpc::testing::Void& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Void>* PrepareAsyncQuitWorkerRaw(::grpc::ClientContext* context, const ::grpc::testing::Void& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel);
    std::unique_ptr< ::grpc::ClientReaderWriter< ::grpc::testing::ServerArgs, ::grpc::testing::ServerStatus>> RunServer(::grpc::ClientContext* context) {
      return std::unique_ptr< ::grpc::ClientReaderWriter< ::grpc::testing::ServerArgs, ::grpc::testing::ServerStatus>>(RunServerRaw(context));
    }
    std::unique_ptr<  ::grpc::ClientAsyncReaderWriter< ::grpc::testing::ServerArgs, ::grpc::testing::ServerStatus>> AsyncRunServer(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriter< ::grpc::testing::ServerArgs, ::grpc::testing::ServerStatus>>(AsyncRunServerRaw(context, cq, tag));
    }
    std::unique_ptr<  ::grpc::ClientAsyncReaderWriter< ::grpc::testing::ServerArgs, ::grpc::testing::ServerStatus>> PrepareAsyncRunServer(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriter< ::grpc::testing::ServerArgs, ::grpc::testing::ServerStatus>>(PrepareAsyncRunServerRaw(context, cq));
    }
    std::unique_ptr< ::grpc::ClientReaderWriter< ::grpc::testing::ClientArgs, ::grpc::testing::ClientStatus>> RunClient(::grpc::ClientContext* context) {
      return std::unique_ptr< ::grpc::ClientReaderWriter< ::grpc::testing::ClientArgs, ::grpc::testing::ClientStatus>>(RunClientRaw(context));
    }
    std::unique_ptr<  ::grpc::ClientAsyncReaderWriter< ::grpc::testing::ClientArgs, ::grpc::testing::ClientStatus>> AsyncRunClient(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriter< ::grpc::testing::ClientArgs, ::grpc::testing::ClientStatus>>(AsyncRunClientRaw(context, cq, tag));
    }
    std::unique_ptr<  ::grpc::ClientAsyncReaderWriter< ::grpc::testing::ClientArgs, ::grpc::testing::ClientStatus>> PrepareAsyncRunClient(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderWriter< ::grpc::testing::ClientArgs, ::grpc::testing::ClientStatus>>(PrepareAsyncRunClientRaw(context, cq));
    }
    ::grpc::Status CoreCount(::grpc::ClientContext* context, const ::grpc::testing::CoreRequest& request, ::grpc::testing::CoreResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::CoreResponse>> AsyncCoreCount(::grpc::ClientContext* context, const ::grpc::testing::CoreRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::CoreResponse>>(AsyncCoreCountRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::CoreResponse>> PrepareAsyncCoreCount(::grpc::ClientContext* context, const ::grpc::testing::CoreRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::CoreResponse>>(PrepareAsyncCoreCountRaw(context, request, cq));
    }
    ::grpc::Status QuitWorker(::grpc::ClientContext* context, const ::grpc::testing::Void& request, ::grpc::testing::Void* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::Void>> AsyncQuitWorker(::grpc::ClientContext* context, const ::grpc::testing::Void& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::Void>>(AsyncQuitWorkerRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::Void>> PrepareAsyncQuitWorker(::grpc::ClientContext* context, const ::grpc::testing::Void& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::grpc::testing::Void>>(PrepareAsyncQuitWorkerRaw(context, request, cq));
    }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    ::grpc::ClientReaderWriter< ::grpc::testing::ServerArgs, ::grpc::testing::ServerStatus>* RunServerRaw(::grpc::ClientContext* context) override;
    ::grpc::ClientAsyncReaderWriter< ::grpc::testing::ServerArgs, ::grpc::testing::ServerStatus>* AsyncRunServerRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) override;
    ::grpc::ClientAsyncReaderWriter< ::grpc::testing::ServerArgs, ::grpc::testing::ServerStatus>* PrepareAsyncRunServerRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientReaderWriter< ::grpc::testing::ClientArgs, ::grpc::testing::ClientStatus>* RunClientRaw(::grpc::ClientContext* context) override;
    ::grpc::ClientAsyncReaderWriter< ::grpc::testing::ClientArgs, ::grpc::testing::ClientStatus>* AsyncRunClientRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag) override;
    ::grpc::ClientAsyncReaderWriter< ::grpc::testing::ClientArgs, ::grpc::testing::ClientStatus>* PrepareAsyncRunClientRaw(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::testing::CoreResponse>* AsyncCoreCountRaw(::grpc::ClientContext* context, const ::grpc::testing::CoreRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::testing::CoreResponse>* PrepareAsyncCoreCountRaw(::grpc::ClientContext* context, const ::grpc::testing::CoreRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::testing::Void>* AsyncQuitWorkerRaw(::grpc::ClientContext* context, const ::grpc::testing::Void& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::grpc::testing::Void>* PrepareAsyncQuitWorkerRaw(::grpc::ClientContext* context, const ::grpc::testing::Void& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_RunServer_;
    const ::grpc::internal::RpcMethod rpcmethod_RunClient_;
    const ::grpc::internal::RpcMethod rpcmethod_CoreCount_;
    const ::grpc::internal::RpcMethod rpcmethod_QuitWorker_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    // Start server with specified workload.
    // First request sent specifies the ServerConfig followed by ServerStatus
    // response. After that, a "Mark" can be sent anytime to request the latest
    // stats. Closing the stream will initiate shutdown of the test server
    // and once the shutdown has finished, the OK status is sent to terminate
    // this RPC.
    virtual ::grpc::Status RunServer(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::ServerStatus, ::grpc::testing::ServerArgs>* stream);
    // Start client with specified workload.
    // First request sent specifies the ClientConfig followed by ClientStatus
    // response. After that, a "Mark" can be sent anytime to request the latest
    // stats. Closing the stream will initiate shutdown of the test client
    // and once the shutdown has finished, the OK status is sent to terminate
    // this RPC.
    virtual ::grpc::Status RunClient(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::ClientStatus, ::grpc::testing::ClientArgs>* stream);
    // Just return the core count - unary call
    virtual ::grpc::Status CoreCount(::grpc::ServerContext* context, const ::grpc::testing::CoreRequest* request, ::grpc::testing::CoreResponse* response);
    // Quit this worker
    virtual ::grpc::Status QuitWorker(::grpc::ServerContext* context, const ::grpc::testing::Void* request, ::grpc::testing::Void* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_RunServer : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_RunServer() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_RunServer() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RunServer(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::ServerStatus, ::grpc::testing::ServerArgs>* stream)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestRunServer(::grpc::ServerContext* context, ::grpc::ServerAsyncReaderWriter< ::grpc::testing::ServerStatus, ::grpc::testing::ServerArgs>* stream, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncBidiStreaming(0, context, stream, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_RunClient : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_RunClient() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_RunClient() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RunClient(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::ClientStatus, ::grpc::testing::ClientArgs>* stream)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestRunClient(::grpc::ServerContext* context, ::grpc::ServerAsyncReaderWriter< ::grpc::testing::ClientStatus, ::grpc::testing::ClientArgs>* stream, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncBidiStreaming(1, context, stream, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_CoreCount : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_CoreCount() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_CoreCount() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CoreCount(::grpc::ServerContext* context, const ::grpc::testing::CoreRequest* request, ::grpc::testing::CoreResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCoreCount(::grpc::ServerContext* context, ::grpc::testing::CoreRequest* request, ::grpc::ServerAsyncResponseWriter< ::grpc::testing::CoreResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_QuitWorker : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithAsyncMethod_QuitWorker() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_QuitWorker() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status QuitWorker(::grpc::ServerContext* context, const ::grpc::testing::Void* request, ::grpc::testing::Void* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestQuitWorker(::grpc::ServerContext* context, ::grpc::testing::Void* request, ::grpc::ServerAsyncResponseWriter< ::grpc::testing::Void>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_RunServer<WithAsyncMethod_RunClient<WithAsyncMethod_CoreCount<WithAsyncMethod_QuitWorker<Service > > > > AsyncService;
  template <class BaseClass>
  class WithGenericMethod_RunServer : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_RunServer() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_RunServer() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RunServer(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::ServerStatus, ::grpc::testing::ServerArgs>* stream)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_RunClient : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_RunClient() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_RunClient() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RunClient(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::ClientStatus, ::grpc::testing::ClientArgs>* stream)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_CoreCount : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_CoreCount() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_CoreCount() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CoreCount(::grpc::ServerContext* context, const ::grpc::testing::CoreRequest* request, ::grpc::testing::CoreResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_QuitWorker : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithGenericMethod_QuitWorker() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_QuitWorker() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status QuitWorker(::grpc::ServerContext* context, const ::grpc::testing::Void* request, ::grpc::testing::Void* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_RunServer : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_RunServer() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_RunServer() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RunServer(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::ServerStatus, ::grpc::testing::ServerArgs>* stream)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestRunServer(::grpc::ServerContext* context, ::grpc::ServerAsyncReaderWriter< ::grpc::ByteBuffer, ::grpc::ByteBuffer>* stream, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncBidiStreaming(0, context, stream, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_RunClient : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_RunClient() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_RunClient() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RunClient(::grpc::ServerContext* context, ::grpc::ServerReaderWriter< ::grpc::testing::ClientStatus, ::grpc::testing::ClientArgs>* stream)  override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestRunClient(::grpc::ServerContext* context, ::grpc::ServerAsyncReaderWriter< ::grpc::ByteBuffer, ::grpc::ByteBuffer>* stream, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncBidiStreaming(1, context, stream, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_CoreCount : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_CoreCount() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_CoreCount() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CoreCount(::grpc::ServerContext* context, const ::grpc::testing::CoreRequest* request, ::grpc::testing::CoreResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCoreCount(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_QuitWorker : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithRawMethod_QuitWorker() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_QuitWorker() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status QuitWorker(::grpc::ServerContext* context, const ::grpc::testing::Void* request, ::grpc::testing::Void* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestQuitWorker(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_CoreCount : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithStreamedUnaryMethod_CoreCount() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler< ::grpc::testing::CoreRequest, ::grpc::testing::CoreResponse>(std::bind(&WithStreamedUnaryMethod_CoreCount<BaseClass>::StreamedCoreCount, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_CoreCount() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status CoreCount(::grpc::ServerContext* context, const ::grpc::testing::CoreRequest* request, ::grpc::testing::CoreResponse* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedCoreCount(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::grpc::testing::CoreRequest,::grpc::testing::CoreResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_QuitWorker : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service *service) {}
   public:
    WithStreamedUnaryMethod_QuitWorker() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler< ::grpc::testing::Void, ::grpc::testing::Void>(std::bind(&WithStreamedUnaryMethod_QuitWorker<BaseClass>::StreamedQuitWorker, this, std::placeholders::_1, std::placeholders::_2)));
    }
    ~WithStreamedUnaryMethod_QuitWorker() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status QuitWorker(::grpc::ServerContext* context, const ::grpc::testing::Void* request, ::grpc::testing::Void* response) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedQuitWorker(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::grpc::testing::Void,::grpc::testing::Void>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_CoreCount<WithStreamedUnaryMethod_QuitWorker<Service > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_CoreCount<WithStreamedUnaryMethod_QuitWorker<Service > > StreamedService;
};

}  // namespace testing
}  // namespace grpc


#endif  // GRPC_src_2fproto_2fgrpc_2ftesting_2fworker_5fservice_2eproto__INCLUDED
