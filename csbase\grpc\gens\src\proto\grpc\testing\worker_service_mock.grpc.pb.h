// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: src/proto/grpc/testing/worker_service.proto

#include "src/proto/grpc/testing/worker_service.pb.h"
#include "src/proto/grpc/testing/worker_service.grpc.pb.h"

#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/sync_stream.h>
#include <gmock/gmock.h>
namespace grpc {
namespace testing {

class MockWorkerServiceStub : public WorkerService::StubInterface {
 public:
  MOCK_METHOD1(RunServerRaw, ::grpc::ClientReaderWriterInterface< ::grpc::testing::ServerArgs, ::grpc::testing::ServerStatus>*(::grpc::ClientContext* context));
  MOCK_METHOD3(AsyncRunServerRaw, ::grpc::ClientAsyncReaderWriterInterface<::grpc::testing::ServerArgs, ::grpc::testing::ServerStatus>*(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag));
  MOCK_METHOD2(PrepareAsyncRunServerRaw, ::grpc::ClientAsyncReaderWriterInterface<::grpc::testing::ServerArgs, ::grpc::testing::ServerStatus>*(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq));
  MOCK_METHOD1(RunClientRaw, ::grpc::ClientReaderWriterInterface< ::grpc::testing::ClientArgs, ::grpc::testing::ClientStatus>*(::grpc::ClientContext* context));
  MOCK_METHOD3(AsyncRunClientRaw, ::grpc::ClientAsyncReaderWriterInterface<::grpc::testing::ClientArgs, ::grpc::testing::ClientStatus>*(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag));
  MOCK_METHOD2(PrepareAsyncRunClientRaw, ::grpc::ClientAsyncReaderWriterInterface<::grpc::testing::ClientArgs, ::grpc::testing::ClientStatus>*(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(CoreCount, ::grpc::Status(::grpc::ClientContext* context, const ::grpc::testing::CoreRequest& request, ::grpc::testing::CoreResponse* response));
  MOCK_METHOD3(AsyncCoreCountRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::CoreResponse>*(::grpc::ClientContext* context, const ::grpc::testing::CoreRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncCoreCountRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::CoreResponse>*(::grpc::ClientContext* context, const ::grpc::testing::CoreRequest& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(QuitWorker, ::grpc::Status(::grpc::ClientContext* context, const ::grpc::testing::Void& request, ::grpc::testing::Void* response));
  MOCK_METHOD3(AsyncQuitWorkerRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Void>*(::grpc::ClientContext* context, const ::grpc::testing::Void& request, ::grpc::CompletionQueue* cq));
  MOCK_METHOD3(PrepareAsyncQuitWorkerRaw, ::grpc::ClientAsyncResponseReaderInterface< ::grpc::testing::Void>*(::grpc::ClientContext* context, const ::grpc::testing::Void& request, ::grpc::CompletionQueue* cq));
};

} // namespace grpc
} // namespace testing

