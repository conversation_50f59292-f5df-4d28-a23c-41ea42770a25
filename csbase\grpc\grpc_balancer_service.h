/*
 *
 * Copyright 2015 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
 #ifndef GRPC_BALANCER_SERVICE_H_
 #define GRPC_BALANCER_SERVICE_H_

#include <iostream>
#include <memory>
#include <string>

#include <grpcpp/grpcpp.h>
#include <grpc/support/log.h>
#include <thread>
#include <unistd.h>


#include <memory>
#include <mutex>
#include <sstream>
#include <thread>

#include <grpc/grpc.h>
#include <grpc/support/alloc.h>
#include <grpc/support/log.h>
#include <grpc/support/string_util.h>
#include <grpc/support/time.h>
#include <grpcpp/channel.h>
#include <grpcpp/client_context.h>
#include <grpcpp/create_channel.h>
#include <grpcpp/server.h>
#include <grpcpp/server_builder.h>

#include "src/core/ext/filters/client_channel/resolver/fake/fake_resolver.h"
#include "src/core/lib/gpr/env.h"
#include "src/core/lib/gprpp/ref_counted_ptr.h"
#include "src/core/lib/iomgr/sockaddr.h"
#include "src/core/lib/security/credentials/fake/fake_credentials.h"
#include "src/cpp/server/secure_server_credentials.h"

#include "src/cpp/client/secure_credentials.h"

//#include "test/core/util/port.h"
//#include "test/core/util/test_config.h"
//#include "test/cpp/end2end/test_service_impl.h"

#include "load_balancer.grpc.pb.h"
//#include "src/proto/grpc/lb/v1/load_balancer.grpc.pb.h"
//#include "src/proto/grpc/testing/echo.grpc.pb.h"



const char g_kCallCredsMdKey[] = "Balancer should not ...";
const char g_kCallCredsMdValue[] = "... receive me";

#include "AkLogging.h"

using grpc::Channel;
using grpc::ClientAsyncResponseReader;
using grpc::ClientContext;
using grpc::CompletionQueue;
using grpc::Status;


using grpc::lb::v1::LoadBalanceRequest;
using grpc::lb::v1::LoadBalanceResponse;
using grpc::lb::v1::LoadBalancer;



struct AddressData {
  int port;
  bool is_balancer;
  grpc::string balancer_name;
  std::string host;
};


grpc::string Ip4ToPackedString(const char* ip_str);

struct ClientStats {
  size_t num_calls_started = 0;
  size_t num_calls_finished = 0;
  size_t num_calls_finished_with_client_failed_to_send = 0;
  size_t num_calls_finished_known_received = 0;
  std::map<grpc::string, size_t> drop_token_counts;

  ClientStats& operator+=(const ClientStats& other) {
    num_calls_started += other.num_calls_started;
    num_calls_finished += other.num_calls_finished;
    num_calls_finished_with_client_failed_to_send +=
        other.num_calls_finished_with_client_failed_to_send;
    num_calls_finished_known_received +=
        other.num_calls_finished_known_received;
    for (const auto& p : other.drop_token_counts) {
      drop_token_counts[p.first] += p.second;
    }
    return *this;
  }
};



template <typename ServiceType>
class CountedService : public ServiceType {
 public:
  size_t request_count() {
    std::unique_lock<std::mutex> lock(mu_);
    return request_count_;
  }

  size_t response_count() {
    std::unique_lock<std::mutex> lock(mu_);
    return response_count_;
  }

  void IncreaseResponseCount() {
    std::unique_lock<std::mutex> lock(mu_);
    ++response_count_;
  }
  void IncreaseRequestCount() {
    std::unique_lock<std::mutex> lock(mu_);
    ++request_count_;
  }

  void ResetCounters() {
    std::unique_lock<std::mutex> lock(mu_);
    request_count_ = 0;
    response_count_ = 0;
  }

 protected:
  std::mutex mu_;

 private:
  size_t request_count_ = 0;
  size_t response_count_ = 0;
};


template <typename T>
struct ServerThread {
  explicit ServerThread(const grpc::string& type,
                        const grpc::string& server_host, T* service)
      : type_(type), service_(service) {
    std::mutex mu;
    // We need to acquire the lock here in order to prevent the notify_one
    // by ServerThread::Start from firing before the wait below is hit.
    std::unique_lock<std::mutex> lock(mu);
    port_ = 999;//grpc::grpc_pick_unused_port_or_die();
    gpr_log(GPR_INFO, "starting %s server on port %d", type_.c_str(), port_);
    std::condition_variable cond;
    thread_.reset(new std::thread(
        std::bind(&ServerThread::Start, this, server_host, &mu, &cond)));
    cond.wait(lock);
    gpr_log(GPR_INFO, "%s server startup complete", type_.c_str());
  }

  void Start(const grpc::string& server_host, std::mutex* mu,
             std::condition_variable* cond) {
    // We need to acquire the lock here in order to prevent the notify_one
    // below from firing before its corresponding wait is executed.
    std::lock_guard<std::mutex> lock(*mu);
    std::ostringstream server_address;
    server_address << server_host << ":" << port_;
    grpc::ServerBuilder builder;
    std::shared_ptr<grpc::ServerCredentials> creds(new grpc::SecureServerCredentials(
        grpc_fake_transport_security_server_credentials_create()));
    builder.AddListeningPort(server_address.str(), creds);
    builder.RegisterService(service_);
    server_ = builder.BuildAndStart();
    cond->notify_one();
  }

  void Shutdown() {
    gpr_log(GPR_INFO, "%s about to shutdown", type_.c_str());
    //todo:chenzhx 先注释掉
    //server_->Shutdown(grpc_timeout_milliseconds_to_deadline(0));
    thread_->join();
    gpr_log(GPR_INFO, "%s shutdown completed", type_.c_str());
  }

  int port_;
  grpc::string type_;
  std::unique_ptr<grpc::Server> server_;
  T* service_;
  std::unique_ptr<std::thread> thread_;
};



using BalancerService = CountedService<LoadBalancer::Service>;


class BalancerServiceImpl : public BalancerService {
 public:
  using Stream = grpc::ServerReaderWriter<LoadBalanceResponse, LoadBalanceRequest>;
  using ResponseDelayPair = std::pair<LoadBalanceResponse, int>;

  explicit BalancerServiceImpl(int client_load_reporting_interval_seconds)
      : client_load_reporting_interval_seconds_(
            client_load_reporting_interval_seconds),
        shutdown_(false) {}

  Status BalanceLoad(grpc::ServerContext* context, Stream* stream) override {
    // Balancer shouldn't receive the call credentials metadata.
    //EXPECT_EQ(context->client_metadata().find(g_kCallCredsMdKey),
    //          context->client_metadata().end());
    gpr_log(GPR_INFO, "LB[%p]: BalanceLoad", this);
    LoadBalanceRequest request;
    std::vector<ResponseDelayPair> responses_and_delays;

    if (!stream->Read(&request)) {
      goto done;
    }
    IncreaseRequestCount();
    gpr_log(GPR_INFO, "LB[%p]: received initial message '%s'", this,
            request.DebugString().c_str());

    // TODO(juanlishen): Initial response should always be the first response.
    if (client_load_reporting_interval_seconds_ > 0) {
      LoadBalanceResponse initial_response;
      initial_response.mutable_initial_response()
          ->mutable_client_stats_report_interval()
          ->set_seconds(client_load_reporting_interval_seconds_);
      stream->Write(initial_response);
    }

    {
      std::unique_lock<std::mutex> lock(mu_);
      responses_and_delays = responses_and_delays_;
    }
    for (const auto& response_and_delay : responses_and_delays) {
      {
        std::unique_lock<std::mutex> lock(mu_);
        if (shutdown_) goto done;
      }
      SendResponse(stream, response_and_delay.first, response_and_delay.second);
    }
    {
      std::unique_lock<std::mutex> lock(mu_);
      if (shutdown_) goto done;
      serverlist_cond_.wait(lock, [this] { return serverlist_ready_; });
    }

    if (client_load_reporting_interval_seconds_ > 0) {
      request.Clear();
      if (stream->Read(&request)) {
        gpr_log(GPR_INFO, "LB[%p]: received client load report message '%s'",
                this, request.DebugString().c_str());
        GPR_ASSERT(request.has_client_stats());
        // We need to acquire the lock here in order to prevent the notify_one
        // below from firing before its corresponding wait is executed.
        std::lock_guard<std::mutex> lock(mu_);
        client_stats_.num_calls_started +=
            request.client_stats().num_calls_started();
        client_stats_.num_calls_finished +=
            request.client_stats().num_calls_finished();
        client_stats_.num_calls_finished_with_client_failed_to_send +=
            request.client_stats()
                .num_calls_finished_with_client_failed_to_send();
        client_stats_.num_calls_finished_known_received +=
            request.client_stats().num_calls_finished_known_received();
        for (const auto& drop_token_count :
             request.client_stats().calls_finished_with_drop()) {
          client_stats_
              .drop_token_counts[drop_token_count.load_balance_token()] +=
              drop_token_count.num_calls();
        }
        load_report_ready_ = true;
        load_report_cond_.notify_one();
      }
    }
  done:
    gpr_log(GPR_INFO, "LB[%p]: done", this);
    return Status::OK;
  }

  void add_response(const LoadBalanceResponse& response, int send_after_ms) {
    std::unique_lock<std::mutex> lock(mu_);
    responses_and_delays_.push_back(std::make_pair(response, send_after_ms));
  }

  // Returns true on its first invocation, false otherwise.
  bool Shutdown() {
    NotifyDoneWithServerlists();
    std::unique_lock<std::mutex> lock(mu_);
    const bool prev = !shutdown_;
    shutdown_ = true;
    gpr_log(GPR_INFO, "LB[%p]: shut down", this);
    return prev;
  }

  static LoadBalanceResponse BuildResponseForBackends(
      const std::vector<int>& backend_ports,
      const std::map<grpc::string, size_t>& drop_token_counts) {
    LoadBalanceResponse response;
    for (const auto& drop_token_count : drop_token_counts) {
      for (size_t i = 0; i < drop_token_count.second; ++i) {
        auto* server = response.mutable_server_list()->add_servers();
        server->set_drop(true);
        server->set_load_balance_token(drop_token_count.first);
      }
    }
    for (const int& backend_port : backend_ports) {
      auto* server = response.mutable_server_list()->add_servers();
      server->set_ip_address(Ip4ToPackedString("127.0.0.1"));
      server->set_port(backend_port);
    }
    return response;
  }

  const ClientStats& WaitForLoadReport() {
    std::unique_lock<std::mutex> lock(mu_);
    load_report_cond_.wait(lock, [this] { return load_report_ready_; });
    load_report_ready_ = false;
    return client_stats_;
  }

  void NotifyDoneWithServerlists() {
    std::lock_guard<std::mutex> lock(mu_);
    serverlist_ready_ = true;
    serverlist_cond_.notify_all();
  }

 private:
  void SendResponse(Stream* stream, const LoadBalanceResponse& response,
                    int delay_ms) {
    gpr_log(GPR_INFO, "LB[%p]: sleeping for %d ms...", this, delay_ms);
    if (delay_ms > 0) {
      //todo:chenzhx 先注释掉
      //gpr_sleep_until(grpc_timeout_milliseconds_to_deadline(delay_ms));
    }
    gpr_log(GPR_INFO, "LB[%p]: Woke up! Sending response '%s'", this,
            response.DebugString().c_str());
    IncreaseResponseCount();
    stream->Write(response);
  }

  const int client_load_reporting_interval_seconds_;
  std::vector<ResponseDelayPair> responses_and_delays_;
  std::mutex mu_;
  std::condition_variable load_report_cond_;
  bool load_report_ready_ = false;
  std::condition_variable serverlist_cond_;
  bool serverlist_ready_ = false;
  ClientStats client_stats_;
  bool shutdown_;
};


#endif
