/*
 *
 * Copyright 2017 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

#ifndef GRPCPP_SUPPORT_ERROR_DETAILS_H
#define GRPCPP_SUPPORT_ERROR_DETAILS_H

#include <grpcpp/support/error_details_impl.h>

namespace google {
namespace rpc {
class Status;
}  // namespace rpc
}  // namespace google

namespace grpc {

static inline Status ExtractErrorDetails(const Status& from,
                                         ::google::rpc::Status* to) {
  return ::grpc_impl::ExtractErrorDetails(from, to);
}

static inline Status SetErrorDetails(const ::google::rpc::Status& from,
                                     Status* to) {
  return ::grpc_impl::SetErrorDetails(from, to);
}

}  // namespace grpc

#endif  // GRPCPP_SUPPORT_ERROR_DETAILS_H
