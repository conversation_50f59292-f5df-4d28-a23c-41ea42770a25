// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: src/proto/grpc/lb/v1/load_balancer.proto

#include "src/proto/grpc/lb/v1/load_balancer.pb.h"

#include <algorithm>

#include <google/protobuf/stubs/common.h>
#include <google/protobuf/stubs/port.h>
#include <google/protobuf/stubs/once.h>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/wire_format_lite_inl.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// This is a temporary google only hack
#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
#include "third_party/protobuf/version.h"
#endif
// @@protoc_insertion_point(includes)
namespace grpc {
namespace lb {
namespace v1 {
class LoadBalanceRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<LoadBalanceRequest>
      _instance;
  const ::grpc::lb::v1::InitialLoadBalanceRequest* initial_request_;
  const ::grpc::lb::v1::ClientStats* client_stats_;
} _LoadBalanceRequest_default_instance_;
class InitialLoadBalanceRequestDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<InitialLoadBalanceRequest>
      _instance;
} _InitialLoadBalanceRequest_default_instance_;
class ClientStatsPerTokenDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ClientStatsPerToken>
      _instance;
} _ClientStatsPerToken_default_instance_;
class ClientStatsDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ClientStats>
      _instance;
} _ClientStats_default_instance_;
class LoadBalanceResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<LoadBalanceResponse>
      _instance;
  const ::grpc::lb::v1::InitialLoadBalanceResponse* initial_response_;
  const ::grpc::lb::v1::ServerList* server_list_;
} _LoadBalanceResponse_default_instance_;
class InitialLoadBalanceResponseDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<InitialLoadBalanceResponse>
      _instance;
} _InitialLoadBalanceResponse_default_instance_;
class ServerListDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<ServerList>
      _instance;
} _ServerList_default_instance_;
class ServerDefaultTypeInternal {
 public:
  ::google::protobuf::internal::ExplicitlyConstructed<Server>
      _instance;
} _Server_default_instance_;
}  // namespace v1
}  // namespace lb
}  // namespace grpc
namespace protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto {
void InitDefaultsLoadBalanceRequestImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsInitialLoadBalanceRequest();
  protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsClientStats();
  {
    void* ptr = &::grpc::lb::v1::_LoadBalanceRequest_default_instance_;
    new (ptr) ::grpc::lb::v1::LoadBalanceRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::lb::v1::LoadBalanceRequest::InitAsDefaultInstance();
}

void InitDefaultsLoadBalanceRequest() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsLoadBalanceRequestImpl);
}

void InitDefaultsInitialLoadBalanceRequestImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::lb::v1::_InitialLoadBalanceRequest_default_instance_;
    new (ptr) ::grpc::lb::v1::InitialLoadBalanceRequest();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::lb::v1::InitialLoadBalanceRequest::InitAsDefaultInstance();
}

void InitDefaultsInitialLoadBalanceRequest() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsInitialLoadBalanceRequestImpl);
}

void InitDefaultsClientStatsPerTokenImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::lb::v1::_ClientStatsPerToken_default_instance_;
    new (ptr) ::grpc::lb::v1::ClientStatsPerToken();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::lb::v1::ClientStatsPerToken::InitAsDefaultInstance();
}

void InitDefaultsClientStatsPerToken() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsClientStatsPerTokenImpl);
}

void InitDefaultsClientStatsImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_google_2fprotobuf_2ftimestamp_2eproto::InitDefaultsTimestamp();
  protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsClientStatsPerToken();
  {
    void* ptr = &::grpc::lb::v1::_ClientStats_default_instance_;
    new (ptr) ::grpc::lb::v1::ClientStats();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::lb::v1::ClientStats::InitAsDefaultInstance();
}

void InitDefaultsClientStats() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsClientStatsImpl);
}

void InitDefaultsLoadBalanceResponseImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsInitialLoadBalanceResponse();
  protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsServerList();
  {
    void* ptr = &::grpc::lb::v1::_LoadBalanceResponse_default_instance_;
    new (ptr) ::grpc::lb::v1::LoadBalanceResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::lb::v1::LoadBalanceResponse::InitAsDefaultInstance();
}

void InitDefaultsLoadBalanceResponse() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsLoadBalanceResponseImpl);
}

void InitDefaultsInitialLoadBalanceResponseImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_google_2fprotobuf_2fduration_2eproto::InitDefaultsDuration();
  {
    void* ptr = &::grpc::lb::v1::_InitialLoadBalanceResponse_default_instance_;
    new (ptr) ::grpc::lb::v1::InitialLoadBalanceResponse();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::lb::v1::InitialLoadBalanceResponse::InitAsDefaultInstance();
}

void InitDefaultsInitialLoadBalanceResponse() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsInitialLoadBalanceResponseImpl);
}

void InitDefaultsServerListImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsServer();
  {
    void* ptr = &::grpc::lb::v1::_ServerList_default_instance_;
    new (ptr) ::grpc::lb::v1::ServerList();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::lb::v1::ServerList::InitAsDefaultInstance();
}

void InitDefaultsServerList() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsServerListImpl);
}

void InitDefaultsServerImpl() {
  GOOGLE_PROTOBUF_VERIFY_VERSION;

#ifdef GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  ::google::protobuf::internal::InitProtobufDefaultsForceUnique();
#else
  ::google::protobuf::internal::InitProtobufDefaults();
#endif  // GOOGLE_PROTOBUF_ENFORCE_UNIQUENESS
  {
    void* ptr = &::grpc::lb::v1::_Server_default_instance_;
    new (ptr) ::grpc::lb::v1::Server();
    ::google::protobuf::internal::OnShutdownDestroyMessage(ptr);
  }
  ::grpc::lb::v1::Server::InitAsDefaultInstance();
}

void InitDefaultsServer() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &InitDefaultsServerImpl);
}

::google::protobuf::Metadata file_level_metadata[8];

const ::google::protobuf::uint32 TableStruct::offsets[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::lb::v1::LoadBalanceRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::lb::v1::LoadBalanceRequest, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  offsetof(::grpc::lb::v1::LoadBalanceRequestDefaultTypeInternal, initial_request_),
  offsetof(::grpc::lb::v1::LoadBalanceRequestDefaultTypeInternal, client_stats_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::lb::v1::LoadBalanceRequest, load_balance_request_type_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::lb::v1::InitialLoadBalanceRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::lb::v1::InitialLoadBalanceRequest, name_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::lb::v1::ClientStatsPerToken, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::lb::v1::ClientStatsPerToken, load_balance_token_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::lb::v1::ClientStatsPerToken, num_calls_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::lb::v1::ClientStats, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::lb::v1::ClientStats, timestamp_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::lb::v1::ClientStats, num_calls_started_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::lb::v1::ClientStats, num_calls_finished_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::lb::v1::ClientStats, num_calls_finished_with_client_failed_to_send_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::lb::v1::ClientStats, num_calls_finished_known_received_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::lb::v1::ClientStats, calls_finished_with_drop_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::lb::v1::LoadBalanceResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::lb::v1::LoadBalanceResponse, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  offsetof(::grpc::lb::v1::LoadBalanceResponseDefaultTypeInternal, initial_response_),
  offsetof(::grpc::lb::v1::LoadBalanceResponseDefaultTypeInternal, server_list_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::lb::v1::LoadBalanceResponse, load_balance_response_type_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::lb::v1::InitialLoadBalanceResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::lb::v1::InitialLoadBalanceResponse, load_balancer_delegate_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::lb::v1::InitialLoadBalanceResponse, client_stats_report_interval_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::lb::v1::ServerList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::lb::v1::ServerList, servers_),
  ~0u,  // no _has_bits_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::lb::v1::Server, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::lb::v1::Server, ip_address_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::lb::v1::Server, port_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::lb::v1::Server, load_balance_token_),
  GOOGLE_PROTOBUF_GENERATED_MESSAGE_FIELD_OFFSET(::grpc::lb::v1::Server, drop_),
};
static const ::google::protobuf::internal::MigrationSchema schemas[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, sizeof(::grpc::lb::v1::LoadBalanceRequest)},
  { 8, -1, sizeof(::grpc::lb::v1::InitialLoadBalanceRequest)},
  { 14, -1, sizeof(::grpc::lb::v1::ClientStatsPerToken)},
  { 21, -1, sizeof(::grpc::lb::v1::ClientStats)},
  { 32, -1, sizeof(::grpc::lb::v1::LoadBalanceResponse)},
  { 40, -1, sizeof(::grpc::lb::v1::InitialLoadBalanceResponse)},
  { 47, -1, sizeof(::grpc::lb::v1::ServerList)},
  { 53, -1, sizeof(::grpc::lb::v1::Server)},
};

static ::google::protobuf::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::lb::v1::_LoadBalanceRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::lb::v1::_InitialLoadBalanceRequest_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::lb::v1::_ClientStatsPerToken_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::lb::v1::_ClientStats_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::lb::v1::_LoadBalanceResponse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::lb::v1::_InitialLoadBalanceResponse_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::lb::v1::_ServerList_default_instance_),
  reinterpret_cast<const ::google::protobuf::Message*>(&::grpc::lb::v1::_Server_default_instance_),
};

void protobuf_AssignDescriptors() {
  AddDescriptors();
  ::google::protobuf::MessageFactory* factory = NULL;
  AssignDescriptors(
      "src/proto/grpc/lb/v1/load_balancer.proto", schemas, file_default_instances, TableStruct::offsets, factory,
      file_level_metadata, NULL, NULL);
}

void protobuf_AssignDescriptorsOnce() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &protobuf_AssignDescriptors);
}

void protobuf_RegisterTypes(const ::std::string&) GOOGLE_PROTOBUF_ATTRIBUTE_COLD;
void protobuf_RegisterTypes(const ::std::string&) {
  protobuf_AssignDescriptorsOnce();
  ::google::protobuf::internal::RegisterAllTypes(file_level_metadata, 8);
}

void AddDescriptorsImpl() {
  InitDefaults();
  static const char descriptor[] GOOGLE_PROTOBUF_ATTRIBUTE_SECTION_VARIABLE(protodesc_cold) = {
      "\n(src/proto/grpc/lb/v1/load_balancer.pro"
      "to\022\ngrpc.lb.v1\032\036google/protobuf/duration"
      ".proto\032\037google/protobuf/timestamp.proto\""
      "\244\001\n\022LoadBalanceRequest\022@\n\017initial_reques"
      "t\030\001 \001(\0132%.grpc.lb.v1.InitialLoadBalanceR"
      "equestH\000\022/\n\014client_stats\030\002 \001(\0132\027.grpc.lb"
      ".v1.ClientStatsH\000B\033\n\031load_balance_reques"
      "t_type\")\n\031InitialLoadBalanceRequest\022\014\n\004n"
      "ame\030\001 \001(\t\"D\n\023ClientStatsPerToken\022\032\n\022load"
      "_balance_token\030\001 \001(\t\022\021\n\tnum_calls\030\002 \001(\003\""
      "\244\002\n\013ClientStats\022-\n\ttimestamp\030\001 \001(\0132\032.goo"
      "gle.protobuf.Timestamp\022\031\n\021num_calls_star"
      "ted\030\002 \001(\003\022\032\n\022num_calls_finished\030\003 \001(\003\0225\n"
      "-num_calls_finished_with_client_failed_t"
      "o_send\030\006 \001(\003\022)\n!num_calls_finished_known"
      "_received\030\007 \001(\003\022A\n\030calls_finished_with_d"
      "rop\030\010 \003(\0132\037.grpc.lb.v1.ClientStatsPerTok"
      "enJ\004\010\004\020\005J\004\010\005\020\006\"\246\001\n\023LoadBalanceResponse\022B"
      "\n\020initial_response\030\001 \001(\0132&.grpc.lb.v1.In"
      "itialLoadBalanceResponseH\000\022-\n\013server_lis"
      "t\030\002 \001(\0132\026.grpc.lb.v1.ServerListH\000B\034\n\032loa"
      "d_balance_response_type\"}\n\032InitialLoadBa"
      "lanceResponse\022\036\n\026load_balancer_delegate\030"
      "\001 \001(\t\022\?\n\034client_stats_report_interval\030\002 "
      "\001(\0132\031.google.protobuf.Duration\"7\n\nServer"
      "List\022#\n\007servers\030\001 \003(\0132\022.grpc.lb.v1.Serve"
      "rJ\004\010\003\020\004\"Z\n\006Server\022\022\n\nip_address\030\001 \001(\014\022\014\n"
      "\004port\030\002 \001(\005\022\032\n\022load_balance_token\030\003 \001(\t\022"
      "\014\n\004drop\030\004 \001(\010J\004\010\005\020\0062b\n\014LoadBalancer\022R\n\013B"
      "alanceLoad\022\036.grpc.lb.v1.LoadBalanceReque"
      "st\032\037.grpc.lb.v1.LoadBalanceResponse(\0010\001B"
      "X\n\016io.grpc.grpclbB\021LoadBalancerProtoP\001Z1"
      "google.golang.org/grpc/balancer/grpclb/g"
      "rpc_lb_v1b\006proto3"
  };
  ::google::protobuf::DescriptorPool::InternalAddGeneratedFile(
      descriptor, 1337);
  ::google::protobuf::MessageFactory::InternalRegisterGeneratedFile(
    "src/proto/grpc/lb/v1/load_balancer.proto", &protobuf_RegisterTypes);
  ::protobuf_google_2fprotobuf_2fduration_2eproto::AddDescriptors();
  ::protobuf_google_2fprotobuf_2ftimestamp_2eproto::AddDescriptors();
}

void AddDescriptors() {
  static GOOGLE_PROTOBUF_DECLARE_ONCE(once);
  ::google::protobuf::GoogleOnceInit(&once, &AddDescriptorsImpl);
}
// Force AddDescriptors() to be called at dynamic initialization time.
struct StaticDescriptorInitializer {
  StaticDescriptorInitializer() {
    AddDescriptors();
  }
} static_descriptor_initializer;
}  // namespace protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto
namespace grpc {
namespace lb {
namespace v1 {

// ===================================================================

void LoadBalanceRequest::InitAsDefaultInstance() {
  ::grpc::lb::v1::_LoadBalanceRequest_default_instance_.initial_request_ = const_cast< ::grpc::lb::v1::InitialLoadBalanceRequest*>(
      ::grpc::lb::v1::InitialLoadBalanceRequest::internal_default_instance());
  ::grpc::lb::v1::_LoadBalanceRequest_default_instance_.client_stats_ = const_cast< ::grpc::lb::v1::ClientStats*>(
      ::grpc::lb::v1::ClientStats::internal_default_instance());
}
void LoadBalanceRequest::set_allocated_initial_request(::grpc::lb::v1::InitialLoadBalanceRequest* initial_request) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_load_balance_request_type();
  if (initial_request) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      initial_request = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, initial_request, submessage_arena);
    }
    set_has_initial_request();
    load_balance_request_type_.initial_request_ = initial_request;
  }
  // @@protoc_insertion_point(field_set_allocated:grpc.lb.v1.LoadBalanceRequest.initial_request)
}
void LoadBalanceRequest::set_allocated_client_stats(::grpc::lb::v1::ClientStats* client_stats) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_load_balance_request_type();
  if (client_stats) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      client_stats = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, client_stats, submessage_arena);
    }
    set_has_client_stats();
    load_balance_request_type_.client_stats_ = client_stats;
  }
  // @@protoc_insertion_point(field_set_allocated:grpc.lb.v1.LoadBalanceRequest.client_stats)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int LoadBalanceRequest::kInitialRequestFieldNumber;
const int LoadBalanceRequest::kClientStatsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

LoadBalanceRequest::LoadBalanceRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsLoadBalanceRequest();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.lb.v1.LoadBalanceRequest)
}
LoadBalanceRequest::LoadBalanceRequest(const LoadBalanceRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  clear_has_load_balance_request_type();
  switch (from.load_balance_request_type_case()) {
    case kInitialRequest: {
      mutable_initial_request()->::grpc::lb::v1::InitialLoadBalanceRequest::MergeFrom(from.initial_request());
      break;
    }
    case kClientStats: {
      mutable_client_stats()->::grpc::lb::v1::ClientStats::MergeFrom(from.client_stats());
      break;
    }
    case LOAD_BALANCE_REQUEST_TYPE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:grpc.lb.v1.LoadBalanceRequest)
}

void LoadBalanceRequest::SharedCtor() {
  clear_has_load_balance_request_type();
  _cached_size_ = 0;
}

LoadBalanceRequest::~LoadBalanceRequest() {
  // @@protoc_insertion_point(destructor:grpc.lb.v1.LoadBalanceRequest)
  SharedDtor();
}

void LoadBalanceRequest::SharedDtor() {
  if (has_load_balance_request_type()) {
    clear_load_balance_request_type();
  }
}

void LoadBalanceRequest::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* LoadBalanceRequest::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const LoadBalanceRequest& LoadBalanceRequest::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsLoadBalanceRequest();
  return *internal_default_instance();
}

LoadBalanceRequest* LoadBalanceRequest::New(::google::protobuf::Arena* arena) const {
  LoadBalanceRequest* n = new LoadBalanceRequest;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void LoadBalanceRequest::clear_load_balance_request_type() {
// @@protoc_insertion_point(one_of_clear_start:grpc.lb.v1.LoadBalanceRequest)
  switch (load_balance_request_type_case()) {
    case kInitialRequest: {
      delete load_balance_request_type_.initial_request_;
      break;
    }
    case kClientStats: {
      delete load_balance_request_type_.client_stats_;
      break;
    }
    case LOAD_BALANCE_REQUEST_TYPE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = LOAD_BALANCE_REQUEST_TYPE_NOT_SET;
}


void LoadBalanceRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.lb.v1.LoadBalanceRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_load_balance_request_type();
  _internal_metadata_.Clear();
}

bool LoadBalanceRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.lb.v1.LoadBalanceRequest)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .grpc.lb.v1.InitialLoadBalanceRequest initial_request = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_initial_request()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.lb.v1.ClientStats client_stats = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_client_stats()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.lb.v1.LoadBalanceRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.lb.v1.LoadBalanceRequest)
  return false;
#undef DO_
}

void LoadBalanceRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.lb.v1.LoadBalanceRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.lb.v1.InitialLoadBalanceRequest initial_request = 1;
  if (has_initial_request()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *load_balance_request_type_.initial_request_, output);
  }

  // .grpc.lb.v1.ClientStats client_stats = 2;
  if (has_client_stats()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *load_balance_request_type_.client_stats_, output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.lb.v1.LoadBalanceRequest)
}

::google::protobuf::uint8* LoadBalanceRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.lb.v1.LoadBalanceRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.lb.v1.InitialLoadBalanceRequest initial_request = 1;
  if (has_initial_request()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, *load_balance_request_type_.initial_request_, deterministic, target);
  }

  // .grpc.lb.v1.ClientStats client_stats = 2;
  if (has_client_stats()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, *load_balance_request_type_.client_stats_, deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.lb.v1.LoadBalanceRequest)
  return target;
}

size_t LoadBalanceRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.lb.v1.LoadBalanceRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  switch (load_balance_request_type_case()) {
    // .grpc.lb.v1.InitialLoadBalanceRequest initial_request = 1;
    case kInitialRequest: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *load_balance_request_type_.initial_request_);
      break;
    }
    // .grpc.lb.v1.ClientStats client_stats = 2;
    case kClientStats: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *load_balance_request_type_.client_stats_);
      break;
    }
    case LOAD_BALANCE_REQUEST_TYPE_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void LoadBalanceRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.lb.v1.LoadBalanceRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const LoadBalanceRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const LoadBalanceRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.lb.v1.LoadBalanceRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.lb.v1.LoadBalanceRequest)
    MergeFrom(*source);
  }
}

void LoadBalanceRequest::MergeFrom(const LoadBalanceRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.lb.v1.LoadBalanceRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.load_balance_request_type_case()) {
    case kInitialRequest: {
      mutable_initial_request()->::grpc::lb::v1::InitialLoadBalanceRequest::MergeFrom(from.initial_request());
      break;
    }
    case kClientStats: {
      mutable_client_stats()->::grpc::lb::v1::ClientStats::MergeFrom(from.client_stats());
      break;
    }
    case LOAD_BALANCE_REQUEST_TYPE_NOT_SET: {
      break;
    }
  }
}

void LoadBalanceRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.lb.v1.LoadBalanceRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void LoadBalanceRequest::CopyFrom(const LoadBalanceRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.lb.v1.LoadBalanceRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LoadBalanceRequest::IsInitialized() const {
  return true;
}

void LoadBalanceRequest::Swap(LoadBalanceRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void LoadBalanceRequest::InternalSwap(LoadBalanceRequest* other) {
  using std::swap;
  swap(load_balance_request_type_, other->load_balance_request_type_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata LoadBalanceRequest::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void InitialLoadBalanceRequest::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int InitialLoadBalanceRequest::kNameFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

InitialLoadBalanceRequest::InitialLoadBalanceRequest()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsInitialLoadBalanceRequest();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.lb.v1.InitialLoadBalanceRequest)
}
InitialLoadBalanceRequest::InitialLoadBalanceRequest(const InitialLoadBalanceRequest& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.name().size() > 0) {
    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
  // @@protoc_insertion_point(copy_constructor:grpc.lb.v1.InitialLoadBalanceRequest)
}

void InitialLoadBalanceRequest::SharedCtor() {
  name_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _cached_size_ = 0;
}

InitialLoadBalanceRequest::~InitialLoadBalanceRequest() {
  // @@protoc_insertion_point(destructor:grpc.lb.v1.InitialLoadBalanceRequest)
  SharedDtor();
}

void InitialLoadBalanceRequest::SharedDtor() {
  name_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void InitialLoadBalanceRequest::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* InitialLoadBalanceRequest::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const InitialLoadBalanceRequest& InitialLoadBalanceRequest::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsInitialLoadBalanceRequest();
  return *internal_default_instance();
}

InitialLoadBalanceRequest* InitialLoadBalanceRequest::New(::google::protobuf::Arena* arena) const {
  InitialLoadBalanceRequest* n = new InitialLoadBalanceRequest;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void InitialLoadBalanceRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.lb.v1.InitialLoadBalanceRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  _internal_metadata_.Clear();
}

bool InitialLoadBalanceRequest::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.lb.v1.InitialLoadBalanceRequest)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string name = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_name()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->name().data(), static_cast<int>(this->name().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.lb.v1.InitialLoadBalanceRequest.name"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.lb.v1.InitialLoadBalanceRequest)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.lb.v1.InitialLoadBalanceRequest)
  return false;
#undef DO_
}

void InitialLoadBalanceRequest::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.lb.v1.InitialLoadBalanceRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.lb.v1.InitialLoadBalanceRequest.name");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->name(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.lb.v1.InitialLoadBalanceRequest)
}

::google::protobuf::uint8* InitialLoadBalanceRequest::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.lb.v1.InitialLoadBalanceRequest)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (this->name().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->name().data(), static_cast<int>(this->name().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.lb.v1.InitialLoadBalanceRequest.name");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->name(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.lb.v1.InitialLoadBalanceRequest)
  return target;
}

size_t InitialLoadBalanceRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.lb.v1.InitialLoadBalanceRequest)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string name = 1;
  if (this->name().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->name());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void InitialLoadBalanceRequest::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.lb.v1.InitialLoadBalanceRequest)
  GOOGLE_DCHECK_NE(&from, this);
  const InitialLoadBalanceRequest* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const InitialLoadBalanceRequest>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.lb.v1.InitialLoadBalanceRequest)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.lb.v1.InitialLoadBalanceRequest)
    MergeFrom(*source);
  }
}

void InitialLoadBalanceRequest::MergeFrom(const InitialLoadBalanceRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.lb.v1.InitialLoadBalanceRequest)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.name().size() > 0) {

    name_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.name_);
  }
}

void InitialLoadBalanceRequest::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.lb.v1.InitialLoadBalanceRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void InitialLoadBalanceRequest::CopyFrom(const InitialLoadBalanceRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.lb.v1.InitialLoadBalanceRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool InitialLoadBalanceRequest::IsInitialized() const {
  return true;
}

void InitialLoadBalanceRequest::Swap(InitialLoadBalanceRequest* other) {
  if (other == this) return;
  InternalSwap(other);
}
void InitialLoadBalanceRequest::InternalSwap(InitialLoadBalanceRequest* other) {
  using std::swap;
  name_.Swap(&other->name_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata InitialLoadBalanceRequest::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ClientStatsPerToken::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ClientStatsPerToken::kLoadBalanceTokenFieldNumber;
const int ClientStatsPerToken::kNumCallsFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ClientStatsPerToken::ClientStatsPerToken()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsClientStatsPerToken();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.lb.v1.ClientStatsPerToken)
}
ClientStatsPerToken::ClientStatsPerToken(const ClientStatsPerToken& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  load_balance_token_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.load_balance_token().size() > 0) {
    load_balance_token_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.load_balance_token_);
  }
  num_calls_ = from.num_calls_;
  // @@protoc_insertion_point(copy_constructor:grpc.lb.v1.ClientStatsPerToken)
}

void ClientStatsPerToken::SharedCtor() {
  load_balance_token_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  num_calls_ = GOOGLE_LONGLONG(0);
  _cached_size_ = 0;
}

ClientStatsPerToken::~ClientStatsPerToken() {
  // @@protoc_insertion_point(destructor:grpc.lb.v1.ClientStatsPerToken)
  SharedDtor();
}

void ClientStatsPerToken::SharedDtor() {
  load_balance_token_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void ClientStatsPerToken::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ClientStatsPerToken::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ClientStatsPerToken& ClientStatsPerToken::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsClientStatsPerToken();
  return *internal_default_instance();
}

ClientStatsPerToken* ClientStatsPerToken::New(::google::protobuf::Arena* arena) const {
  ClientStatsPerToken* n = new ClientStatsPerToken;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ClientStatsPerToken::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.lb.v1.ClientStatsPerToken)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  load_balance_token_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  num_calls_ = GOOGLE_LONGLONG(0);
  _internal_metadata_.Clear();
}

bool ClientStatsPerToken::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.lb.v1.ClientStatsPerToken)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string load_balance_token = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_load_balance_token()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->load_balance_token().data(), static_cast<int>(this->load_balance_token().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.lb.v1.ClientStatsPerToken.load_balance_token"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 num_calls = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &num_calls_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.lb.v1.ClientStatsPerToken)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.lb.v1.ClientStatsPerToken)
  return false;
#undef DO_
}

void ClientStatsPerToken::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.lb.v1.ClientStatsPerToken)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string load_balance_token = 1;
  if (this->load_balance_token().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->load_balance_token().data(), static_cast<int>(this->load_balance_token().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.lb.v1.ClientStatsPerToken.load_balance_token");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->load_balance_token(), output);
  }

  // int64 num_calls = 2;
  if (this->num_calls() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->num_calls(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.lb.v1.ClientStatsPerToken)
}

::google::protobuf::uint8* ClientStatsPerToken::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.lb.v1.ClientStatsPerToken)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string load_balance_token = 1;
  if (this->load_balance_token().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->load_balance_token().data(), static_cast<int>(this->load_balance_token().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.lb.v1.ClientStatsPerToken.load_balance_token");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->load_balance_token(), target);
  }

  // int64 num_calls = 2;
  if (this->num_calls() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->num_calls(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.lb.v1.ClientStatsPerToken)
  return target;
}

size_t ClientStatsPerToken::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.lb.v1.ClientStatsPerToken)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string load_balance_token = 1;
  if (this->load_balance_token().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->load_balance_token());
  }

  // int64 num_calls = 2;
  if (this->num_calls() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->num_calls());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ClientStatsPerToken::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.lb.v1.ClientStatsPerToken)
  GOOGLE_DCHECK_NE(&from, this);
  const ClientStatsPerToken* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ClientStatsPerToken>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.lb.v1.ClientStatsPerToken)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.lb.v1.ClientStatsPerToken)
    MergeFrom(*source);
  }
}

void ClientStatsPerToken::MergeFrom(const ClientStatsPerToken& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.lb.v1.ClientStatsPerToken)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.load_balance_token().size() > 0) {

    load_balance_token_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.load_balance_token_);
  }
  if (from.num_calls() != 0) {
    set_num_calls(from.num_calls());
  }
}

void ClientStatsPerToken::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.lb.v1.ClientStatsPerToken)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ClientStatsPerToken::CopyFrom(const ClientStatsPerToken& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.lb.v1.ClientStatsPerToken)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ClientStatsPerToken::IsInitialized() const {
  return true;
}

void ClientStatsPerToken::Swap(ClientStatsPerToken* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ClientStatsPerToken::InternalSwap(ClientStatsPerToken* other) {
  using std::swap;
  load_balance_token_.Swap(&other->load_balance_token_);
  swap(num_calls_, other->num_calls_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ClientStatsPerToken::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ClientStats::InitAsDefaultInstance() {
  ::grpc::lb::v1::_ClientStats_default_instance_._instance.get_mutable()->timestamp_ = const_cast< ::google::protobuf::Timestamp*>(
      ::google::protobuf::Timestamp::internal_default_instance());
}
void ClientStats::clear_timestamp() {
  if (GetArenaNoVirtual() == NULL && timestamp_ != NULL) {
    delete timestamp_;
  }
  timestamp_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ClientStats::kTimestampFieldNumber;
const int ClientStats::kNumCallsStartedFieldNumber;
const int ClientStats::kNumCallsFinishedFieldNumber;
const int ClientStats::kNumCallsFinishedWithClientFailedToSendFieldNumber;
const int ClientStats::kNumCallsFinishedKnownReceivedFieldNumber;
const int ClientStats::kCallsFinishedWithDropFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ClientStats::ClientStats()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsClientStats();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.lb.v1.ClientStats)
}
ClientStats::ClientStats(const ClientStats& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      calls_finished_with_drop_(from.calls_finished_with_drop_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  if (from.has_timestamp()) {
    timestamp_ = new ::google::protobuf::Timestamp(*from.timestamp_);
  } else {
    timestamp_ = NULL;
  }
  ::memcpy(&num_calls_started_, &from.num_calls_started_,
    static_cast<size_t>(reinterpret_cast<char*>(&num_calls_finished_known_received_) -
    reinterpret_cast<char*>(&num_calls_started_)) + sizeof(num_calls_finished_known_received_));
  // @@protoc_insertion_point(copy_constructor:grpc.lb.v1.ClientStats)
}

void ClientStats::SharedCtor() {
  ::memset(&timestamp_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&num_calls_finished_known_received_) -
      reinterpret_cast<char*>(&timestamp_)) + sizeof(num_calls_finished_known_received_));
  _cached_size_ = 0;
}

ClientStats::~ClientStats() {
  // @@protoc_insertion_point(destructor:grpc.lb.v1.ClientStats)
  SharedDtor();
}

void ClientStats::SharedDtor() {
  if (this != internal_default_instance()) delete timestamp_;
}

void ClientStats::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ClientStats::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ClientStats& ClientStats::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsClientStats();
  return *internal_default_instance();
}

ClientStats* ClientStats::New(::google::protobuf::Arena* arena) const {
  ClientStats* n = new ClientStats;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ClientStats::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.lb.v1.ClientStats)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  calls_finished_with_drop_.Clear();
  if (GetArenaNoVirtual() == NULL && timestamp_ != NULL) {
    delete timestamp_;
  }
  timestamp_ = NULL;
  ::memset(&num_calls_started_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&num_calls_finished_known_received_) -
      reinterpret_cast<char*>(&num_calls_started_)) + sizeof(num_calls_finished_known_received_));
  _internal_metadata_.Clear();
}

bool ClientStats::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.lb.v1.ClientStats)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .google.protobuf.Timestamp timestamp = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_timestamp()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 num_calls_started = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &num_calls_started_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 num_calls_finished = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(24u /* 24 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &num_calls_finished_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 num_calls_finished_with_client_failed_to_send = 6;
      case 6: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(48u /* 48 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &num_calls_finished_with_client_failed_to_send_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int64 num_calls_finished_known_received = 7;
      case 7: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(56u /* 56 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int64, ::google::protobuf::internal::WireFormatLite::TYPE_INT64>(
                 input, &num_calls_finished_known_received_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // repeated .grpc.lb.v1.ClientStatsPerToken calls_finished_with_drop = 8;
      case 8: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(66u /* 66 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(input, add_calls_finished_with_drop()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.lb.v1.ClientStats)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.lb.v1.ClientStats)
  return false;
#undef DO_
}

void ClientStats::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.lb.v1.ClientStats)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .google.protobuf.Timestamp timestamp = 1;
  if (this->has_timestamp()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *this->timestamp_, output);
  }

  // int64 num_calls_started = 2;
  if (this->num_calls_started() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(2, this->num_calls_started(), output);
  }

  // int64 num_calls_finished = 3;
  if (this->num_calls_finished() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(3, this->num_calls_finished(), output);
  }

  // int64 num_calls_finished_with_client_failed_to_send = 6;
  if (this->num_calls_finished_with_client_failed_to_send() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(6, this->num_calls_finished_with_client_failed_to_send(), output);
  }

  // int64 num_calls_finished_known_received = 7;
  if (this->num_calls_finished_known_received() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt64(7, this->num_calls_finished_known_received(), output);
  }

  // repeated .grpc.lb.v1.ClientStatsPerToken calls_finished_with_drop = 8;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->calls_finished_with_drop_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      8, this->calls_finished_with_drop(static_cast<int>(i)), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.lb.v1.ClientStats)
}

::google::protobuf::uint8* ClientStats::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.lb.v1.ClientStats)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .google.protobuf.Timestamp timestamp = 1;
  if (this->has_timestamp()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, *this->timestamp_, deterministic, target);
  }

  // int64 num_calls_started = 2;
  if (this->num_calls_started() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(2, this->num_calls_started(), target);
  }

  // int64 num_calls_finished = 3;
  if (this->num_calls_finished() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(3, this->num_calls_finished(), target);
  }

  // int64 num_calls_finished_with_client_failed_to_send = 6;
  if (this->num_calls_finished_with_client_failed_to_send() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(6, this->num_calls_finished_with_client_failed_to_send(), target);
  }

  // int64 num_calls_finished_known_received = 7;
  if (this->num_calls_finished_known_received() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt64ToArray(7, this->num_calls_finished_known_received(), target);
  }

  // repeated .grpc.lb.v1.ClientStatsPerToken calls_finished_with_drop = 8;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->calls_finished_with_drop_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        8, this->calls_finished_with_drop(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.lb.v1.ClientStats)
  return target;
}

size_t ClientStats::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.lb.v1.ClientStats)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .grpc.lb.v1.ClientStatsPerToken calls_finished_with_drop = 8;
  {
    unsigned int count = static_cast<unsigned int>(this->calls_finished_with_drop_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->calls_finished_with_drop(static_cast<int>(i)));
    }
  }

  // .google.protobuf.Timestamp timestamp = 1;
  if (this->has_timestamp()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->timestamp_);
  }

  // int64 num_calls_started = 2;
  if (this->num_calls_started() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->num_calls_started());
  }

  // int64 num_calls_finished = 3;
  if (this->num_calls_finished() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->num_calls_finished());
  }

  // int64 num_calls_finished_with_client_failed_to_send = 6;
  if (this->num_calls_finished_with_client_failed_to_send() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->num_calls_finished_with_client_failed_to_send());
  }

  // int64 num_calls_finished_known_received = 7;
  if (this->num_calls_finished_known_received() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int64Size(
        this->num_calls_finished_known_received());
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ClientStats::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.lb.v1.ClientStats)
  GOOGLE_DCHECK_NE(&from, this);
  const ClientStats* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ClientStats>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.lb.v1.ClientStats)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.lb.v1.ClientStats)
    MergeFrom(*source);
  }
}

void ClientStats::MergeFrom(const ClientStats& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.lb.v1.ClientStats)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  calls_finished_with_drop_.MergeFrom(from.calls_finished_with_drop_);
  if (from.has_timestamp()) {
    mutable_timestamp()->::google::protobuf::Timestamp::MergeFrom(from.timestamp());
  }
  if (from.num_calls_started() != 0) {
    set_num_calls_started(from.num_calls_started());
  }
  if (from.num_calls_finished() != 0) {
    set_num_calls_finished(from.num_calls_finished());
  }
  if (from.num_calls_finished_with_client_failed_to_send() != 0) {
    set_num_calls_finished_with_client_failed_to_send(from.num_calls_finished_with_client_failed_to_send());
  }
  if (from.num_calls_finished_known_received() != 0) {
    set_num_calls_finished_known_received(from.num_calls_finished_known_received());
  }
}

void ClientStats::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.lb.v1.ClientStats)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ClientStats::CopyFrom(const ClientStats& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.lb.v1.ClientStats)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ClientStats::IsInitialized() const {
  return true;
}

void ClientStats::Swap(ClientStats* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ClientStats::InternalSwap(ClientStats* other) {
  using std::swap;
  calls_finished_with_drop_.InternalSwap(&other->calls_finished_with_drop_);
  swap(timestamp_, other->timestamp_);
  swap(num_calls_started_, other->num_calls_started_);
  swap(num_calls_finished_, other->num_calls_finished_);
  swap(num_calls_finished_with_client_failed_to_send_, other->num_calls_finished_with_client_failed_to_send_);
  swap(num_calls_finished_known_received_, other->num_calls_finished_known_received_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ClientStats::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void LoadBalanceResponse::InitAsDefaultInstance() {
  ::grpc::lb::v1::_LoadBalanceResponse_default_instance_.initial_response_ = const_cast< ::grpc::lb::v1::InitialLoadBalanceResponse*>(
      ::grpc::lb::v1::InitialLoadBalanceResponse::internal_default_instance());
  ::grpc::lb::v1::_LoadBalanceResponse_default_instance_.server_list_ = const_cast< ::grpc::lb::v1::ServerList*>(
      ::grpc::lb::v1::ServerList::internal_default_instance());
}
void LoadBalanceResponse::set_allocated_initial_response(::grpc::lb::v1::InitialLoadBalanceResponse* initial_response) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_load_balance_response_type();
  if (initial_response) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      initial_response = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, initial_response, submessage_arena);
    }
    set_has_initial_response();
    load_balance_response_type_.initial_response_ = initial_response;
  }
  // @@protoc_insertion_point(field_set_allocated:grpc.lb.v1.LoadBalanceResponse.initial_response)
}
void LoadBalanceResponse::set_allocated_server_list(::grpc::lb::v1::ServerList* server_list) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  clear_load_balance_response_type();
  if (server_list) {
    ::google::protobuf::Arena* submessage_arena = NULL;
    if (message_arena != submessage_arena) {
      server_list = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, server_list, submessage_arena);
    }
    set_has_server_list();
    load_balance_response_type_.server_list_ = server_list;
  }
  // @@protoc_insertion_point(field_set_allocated:grpc.lb.v1.LoadBalanceResponse.server_list)
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int LoadBalanceResponse::kInitialResponseFieldNumber;
const int LoadBalanceResponse::kServerListFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

LoadBalanceResponse::LoadBalanceResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsLoadBalanceResponse();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.lb.v1.LoadBalanceResponse)
}
LoadBalanceResponse::LoadBalanceResponse(const LoadBalanceResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  clear_has_load_balance_response_type();
  switch (from.load_balance_response_type_case()) {
    case kInitialResponse: {
      mutable_initial_response()->::grpc::lb::v1::InitialLoadBalanceResponse::MergeFrom(from.initial_response());
      break;
    }
    case kServerList: {
      mutable_server_list()->::grpc::lb::v1::ServerList::MergeFrom(from.server_list());
      break;
    }
    case LOAD_BALANCE_RESPONSE_TYPE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:grpc.lb.v1.LoadBalanceResponse)
}

void LoadBalanceResponse::SharedCtor() {
  clear_has_load_balance_response_type();
  _cached_size_ = 0;
}

LoadBalanceResponse::~LoadBalanceResponse() {
  // @@protoc_insertion_point(destructor:grpc.lb.v1.LoadBalanceResponse)
  SharedDtor();
}

void LoadBalanceResponse::SharedDtor() {
  if (has_load_balance_response_type()) {
    clear_load_balance_response_type();
  }
}

void LoadBalanceResponse::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* LoadBalanceResponse::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const LoadBalanceResponse& LoadBalanceResponse::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsLoadBalanceResponse();
  return *internal_default_instance();
}

LoadBalanceResponse* LoadBalanceResponse::New(::google::protobuf::Arena* arena) const {
  LoadBalanceResponse* n = new LoadBalanceResponse;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void LoadBalanceResponse::clear_load_balance_response_type() {
// @@protoc_insertion_point(one_of_clear_start:grpc.lb.v1.LoadBalanceResponse)
  switch (load_balance_response_type_case()) {
    case kInitialResponse: {
      delete load_balance_response_type_.initial_response_;
      break;
    }
    case kServerList: {
      delete load_balance_response_type_.server_list_;
      break;
    }
    case LOAD_BALANCE_RESPONSE_TYPE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = LOAD_BALANCE_RESPONSE_TYPE_NOT_SET;
}


void LoadBalanceResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.lb.v1.LoadBalanceResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_load_balance_response_type();
  _internal_metadata_.Clear();
}

bool LoadBalanceResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.lb.v1.LoadBalanceResponse)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // .grpc.lb.v1.InitialLoadBalanceResponse initial_response = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_initial_response()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .grpc.lb.v1.ServerList server_list = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_server_list()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.lb.v1.LoadBalanceResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.lb.v1.LoadBalanceResponse)
  return false;
#undef DO_
}

void LoadBalanceResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.lb.v1.LoadBalanceResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.lb.v1.InitialLoadBalanceResponse initial_response = 1;
  if (has_initial_response()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, *load_balance_response_type_.initial_response_, output);
  }

  // .grpc.lb.v1.ServerList server_list = 2;
  if (has_server_list()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *load_balance_response_type_.server_list_, output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.lb.v1.LoadBalanceResponse)
}

::google::protobuf::uint8* LoadBalanceResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.lb.v1.LoadBalanceResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // .grpc.lb.v1.InitialLoadBalanceResponse initial_response = 1;
  if (has_initial_response()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, *load_balance_response_type_.initial_response_, deterministic, target);
  }

  // .grpc.lb.v1.ServerList server_list = 2;
  if (has_server_list()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, *load_balance_response_type_.server_list_, deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.lb.v1.LoadBalanceResponse)
  return target;
}

size_t LoadBalanceResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.lb.v1.LoadBalanceResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  switch (load_balance_response_type_case()) {
    // .grpc.lb.v1.InitialLoadBalanceResponse initial_response = 1;
    case kInitialResponse: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *load_balance_response_type_.initial_response_);
      break;
    }
    // .grpc.lb.v1.ServerList server_list = 2;
    case kServerList: {
      total_size += 1 +
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          *load_balance_response_type_.server_list_);
      break;
    }
    case LOAD_BALANCE_RESPONSE_TYPE_NOT_SET: {
      break;
    }
  }
  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void LoadBalanceResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.lb.v1.LoadBalanceResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const LoadBalanceResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const LoadBalanceResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.lb.v1.LoadBalanceResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.lb.v1.LoadBalanceResponse)
    MergeFrom(*source);
  }
}

void LoadBalanceResponse::MergeFrom(const LoadBalanceResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.lb.v1.LoadBalanceResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.load_balance_response_type_case()) {
    case kInitialResponse: {
      mutable_initial_response()->::grpc::lb::v1::InitialLoadBalanceResponse::MergeFrom(from.initial_response());
      break;
    }
    case kServerList: {
      mutable_server_list()->::grpc::lb::v1::ServerList::MergeFrom(from.server_list());
      break;
    }
    case LOAD_BALANCE_RESPONSE_TYPE_NOT_SET: {
      break;
    }
  }
}

void LoadBalanceResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.lb.v1.LoadBalanceResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void LoadBalanceResponse::CopyFrom(const LoadBalanceResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.lb.v1.LoadBalanceResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LoadBalanceResponse::IsInitialized() const {
  return true;
}

void LoadBalanceResponse::Swap(LoadBalanceResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void LoadBalanceResponse::InternalSwap(LoadBalanceResponse* other) {
  using std::swap;
  swap(load_balance_response_type_, other->load_balance_response_type_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata LoadBalanceResponse::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void InitialLoadBalanceResponse::InitAsDefaultInstance() {
  ::grpc::lb::v1::_InitialLoadBalanceResponse_default_instance_._instance.get_mutable()->client_stats_report_interval_ = const_cast< ::google::protobuf::Duration*>(
      ::google::protobuf::Duration::internal_default_instance());
}
void InitialLoadBalanceResponse::clear_client_stats_report_interval() {
  if (GetArenaNoVirtual() == NULL && client_stats_report_interval_ != NULL) {
    delete client_stats_report_interval_;
  }
  client_stats_report_interval_ = NULL;
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int InitialLoadBalanceResponse::kLoadBalancerDelegateFieldNumber;
const int InitialLoadBalanceResponse::kClientStatsReportIntervalFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

InitialLoadBalanceResponse::InitialLoadBalanceResponse()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsInitialLoadBalanceResponse();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.lb.v1.InitialLoadBalanceResponse)
}
InitialLoadBalanceResponse::InitialLoadBalanceResponse(const InitialLoadBalanceResponse& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  load_balancer_delegate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.load_balancer_delegate().size() > 0) {
    load_balancer_delegate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.load_balancer_delegate_);
  }
  if (from.has_client_stats_report_interval()) {
    client_stats_report_interval_ = new ::google::protobuf::Duration(*from.client_stats_report_interval_);
  } else {
    client_stats_report_interval_ = NULL;
  }
  // @@protoc_insertion_point(copy_constructor:grpc.lb.v1.InitialLoadBalanceResponse)
}

void InitialLoadBalanceResponse::SharedCtor() {
  load_balancer_delegate_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  client_stats_report_interval_ = NULL;
  _cached_size_ = 0;
}

InitialLoadBalanceResponse::~InitialLoadBalanceResponse() {
  // @@protoc_insertion_point(destructor:grpc.lb.v1.InitialLoadBalanceResponse)
  SharedDtor();
}

void InitialLoadBalanceResponse::SharedDtor() {
  load_balancer_delegate_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete client_stats_report_interval_;
}

void InitialLoadBalanceResponse::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* InitialLoadBalanceResponse::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const InitialLoadBalanceResponse& InitialLoadBalanceResponse::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsInitialLoadBalanceResponse();
  return *internal_default_instance();
}

InitialLoadBalanceResponse* InitialLoadBalanceResponse::New(::google::protobuf::Arena* arena) const {
  InitialLoadBalanceResponse* n = new InitialLoadBalanceResponse;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void InitialLoadBalanceResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.lb.v1.InitialLoadBalanceResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  load_balancer_delegate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (GetArenaNoVirtual() == NULL && client_stats_report_interval_ != NULL) {
    delete client_stats_report_interval_;
  }
  client_stats_report_interval_ = NULL;
  _internal_metadata_.Clear();
}

bool InitialLoadBalanceResponse::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.lb.v1.InitialLoadBalanceResponse)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // string load_balancer_delegate = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_load_balancer_delegate()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->load_balancer_delegate().data(), static_cast<int>(this->load_balancer_delegate().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.lb.v1.InitialLoadBalanceResponse.load_balancer_delegate"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // .google.protobuf.Duration client_stats_report_interval = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(18u /* 18 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(
               input, mutable_client_stats_report_interval()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.lb.v1.InitialLoadBalanceResponse)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.lb.v1.InitialLoadBalanceResponse)
  return false;
#undef DO_
}

void InitialLoadBalanceResponse::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.lb.v1.InitialLoadBalanceResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string load_balancer_delegate = 1;
  if (this->load_balancer_delegate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->load_balancer_delegate().data(), static_cast<int>(this->load_balancer_delegate().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.lb.v1.InitialLoadBalanceResponse.load_balancer_delegate");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      1, this->load_balancer_delegate(), output);
  }

  // .google.protobuf.Duration client_stats_report_interval = 2;
  if (this->has_client_stats_report_interval()) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      2, *this->client_stats_report_interval_, output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.lb.v1.InitialLoadBalanceResponse)
}

::google::protobuf::uint8* InitialLoadBalanceResponse::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.lb.v1.InitialLoadBalanceResponse)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // string load_balancer_delegate = 1;
  if (this->load_balancer_delegate().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->load_balancer_delegate().data(), static_cast<int>(this->load_balancer_delegate().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.lb.v1.InitialLoadBalanceResponse.load_balancer_delegate");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        1, this->load_balancer_delegate(), target);
  }

  // .google.protobuf.Duration client_stats_report_interval = 2;
  if (this->has_client_stats_report_interval()) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        2, *this->client_stats_report_interval_, deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.lb.v1.InitialLoadBalanceResponse)
  return target;
}

size_t InitialLoadBalanceResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.lb.v1.InitialLoadBalanceResponse)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // string load_balancer_delegate = 1;
  if (this->load_balancer_delegate().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->load_balancer_delegate());
  }

  // .google.protobuf.Duration client_stats_report_interval = 2;
  if (this->has_client_stats_report_interval()) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::MessageSize(
        *this->client_stats_report_interval_);
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void InitialLoadBalanceResponse::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.lb.v1.InitialLoadBalanceResponse)
  GOOGLE_DCHECK_NE(&from, this);
  const InitialLoadBalanceResponse* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const InitialLoadBalanceResponse>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.lb.v1.InitialLoadBalanceResponse)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.lb.v1.InitialLoadBalanceResponse)
    MergeFrom(*source);
  }
}

void InitialLoadBalanceResponse::MergeFrom(const InitialLoadBalanceResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.lb.v1.InitialLoadBalanceResponse)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.load_balancer_delegate().size() > 0) {

    load_balancer_delegate_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.load_balancer_delegate_);
  }
  if (from.has_client_stats_report_interval()) {
    mutable_client_stats_report_interval()->::google::protobuf::Duration::MergeFrom(from.client_stats_report_interval());
  }
}

void InitialLoadBalanceResponse::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.lb.v1.InitialLoadBalanceResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void InitialLoadBalanceResponse::CopyFrom(const InitialLoadBalanceResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.lb.v1.InitialLoadBalanceResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool InitialLoadBalanceResponse::IsInitialized() const {
  return true;
}

void InitialLoadBalanceResponse::Swap(InitialLoadBalanceResponse* other) {
  if (other == this) return;
  InternalSwap(other);
}
void InitialLoadBalanceResponse::InternalSwap(InitialLoadBalanceResponse* other) {
  using std::swap;
  load_balancer_delegate_.Swap(&other->load_balancer_delegate_);
  swap(client_stats_report_interval_, other->client_stats_report_interval_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata InitialLoadBalanceResponse::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void ServerList::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int ServerList::kServersFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

ServerList::ServerList()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsServerList();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.lb.v1.ServerList)
}
ServerList::ServerList(const ServerList& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      servers_(from.servers_),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:grpc.lb.v1.ServerList)
}

void ServerList::SharedCtor() {
  _cached_size_ = 0;
}

ServerList::~ServerList() {
  // @@protoc_insertion_point(destructor:grpc.lb.v1.ServerList)
  SharedDtor();
}

void ServerList::SharedDtor() {
}

void ServerList::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* ServerList::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const ServerList& ServerList::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsServerList();
  return *internal_default_instance();
}

ServerList* ServerList::New(::google::protobuf::Arena* arena) const {
  ServerList* n = new ServerList;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void ServerList::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.lb.v1.ServerList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  servers_.Clear();
  _internal_metadata_.Clear();
}

bool ServerList::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.lb.v1.ServerList)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // repeated .grpc.lb.v1.Server servers = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadMessage(input, add_servers()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.lb.v1.ServerList)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.lb.v1.ServerList)
  return false;
#undef DO_
}

void ServerList::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.lb.v1.ServerList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .grpc.lb.v1.Server servers = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->servers_size()); i < n; i++) {
    ::google::protobuf::internal::WireFormatLite::WriteMessageMaybeToArray(
      1, this->servers(static_cast<int>(i)), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.lb.v1.ServerList)
}

::google::protobuf::uint8* ServerList::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.lb.v1.ServerList)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .grpc.lb.v1.Server servers = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->servers_size()); i < n; i++) {
    target = ::google::protobuf::internal::WireFormatLite::
      InternalWriteMessageToArray(
        1, this->servers(static_cast<int>(i)), deterministic, target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.lb.v1.ServerList)
  return target;
}

size_t ServerList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.lb.v1.ServerList)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // repeated .grpc.lb.v1.Server servers = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->servers_size());
    total_size += 1UL * count;
    for (unsigned int i = 0; i < count; i++) {
      total_size +=
        ::google::protobuf::internal::WireFormatLite::MessageSize(
          this->servers(static_cast<int>(i)));
    }
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void ServerList::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.lb.v1.ServerList)
  GOOGLE_DCHECK_NE(&from, this);
  const ServerList* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const ServerList>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.lb.v1.ServerList)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.lb.v1.ServerList)
    MergeFrom(*source);
  }
}

void ServerList::MergeFrom(const ServerList& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.lb.v1.ServerList)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  servers_.MergeFrom(from.servers_);
}

void ServerList::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.lb.v1.ServerList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void ServerList::CopyFrom(const ServerList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.lb.v1.ServerList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ServerList::IsInitialized() const {
  return true;
}

void ServerList::Swap(ServerList* other) {
  if (other == this) return;
  InternalSwap(other);
}
void ServerList::InternalSwap(ServerList* other) {
  using std::swap;
  servers_.InternalSwap(&other->servers_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata ServerList::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::file_level_metadata[kIndexInFileMessages];
}


// ===================================================================

void Server::InitAsDefaultInstance() {
}
#if !defined(_MSC_VER) || _MSC_VER >= 1900
const int Server::kIpAddressFieldNumber;
const int Server::kPortFieldNumber;
const int Server::kLoadBalanceTokenFieldNumber;
const int Server::kDropFieldNumber;
#endif  // !defined(_MSC_VER) || _MSC_VER >= 1900

Server::Server()
  : ::google::protobuf::Message(), _internal_metadata_(NULL) {
  if (GOOGLE_PREDICT_TRUE(this != internal_default_instance())) {
    ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsServer();
  }
  SharedCtor();
  // @@protoc_insertion_point(constructor:grpc.lb.v1.Server)
}
Server::Server(const Server& from)
  : ::google::protobuf::Message(),
      _internal_metadata_(NULL),
      _cached_size_(0) {
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ip_address_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.ip_address().size() > 0) {
    ip_address_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.ip_address_);
  }
  load_balance_token_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  if (from.load_balance_token().size() > 0) {
    load_balance_token_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.load_balance_token_);
  }
  ::memcpy(&port_, &from.port_,
    static_cast<size_t>(reinterpret_cast<char*>(&drop_) -
    reinterpret_cast<char*>(&port_)) + sizeof(drop_));
  // @@protoc_insertion_point(copy_constructor:grpc.lb.v1.Server)
}

void Server::SharedCtor() {
  ip_address_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  load_balance_token_.UnsafeSetDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&port_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&drop_) -
      reinterpret_cast<char*>(&port_)) + sizeof(drop_));
  _cached_size_ = 0;
}

Server::~Server() {
  // @@protoc_insertion_point(destructor:grpc.lb.v1.Server)
  SharedDtor();
}

void Server::SharedDtor() {
  ip_address_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  load_balance_token_.DestroyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}

void Server::SetCachedSize(int size) const {
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
}
const ::google::protobuf::Descriptor* Server::descriptor() {
  ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::file_level_metadata[kIndexInFileMessages].descriptor;
}

const Server& Server::default_instance() {
  ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsServer();
  return *internal_default_instance();
}

Server* Server::New(::google::protobuf::Arena* arena) const {
  Server* n = new Server;
  if (arena != NULL) {
    arena->Own(n);
  }
  return n;
}

void Server::Clear() {
// @@protoc_insertion_point(message_clear_start:grpc.lb.v1.Server)
  ::google::protobuf::uint32 cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ip_address_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  load_balance_token_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
  ::memset(&port_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&drop_) -
      reinterpret_cast<char*>(&port_)) + sizeof(drop_));
  _internal_metadata_.Clear();
}

bool Server::MergePartialFromCodedStream(
    ::google::protobuf::io::CodedInputStream* input) {
#define DO_(EXPRESSION) if (!GOOGLE_PREDICT_TRUE(EXPRESSION)) goto failure
  ::google::protobuf::uint32 tag;
  // @@protoc_insertion_point(parse_start:grpc.lb.v1.Server)
  for (;;) {
    ::std::pair< ::google::protobuf::uint32, bool> p = input->ReadTagWithCutoffNoLastTag(127u);
    tag = p.first;
    if (!p.second) goto handle_unusual;
    switch (::google::protobuf::internal::WireFormatLite::GetTagFieldNumber(tag)) {
      // bytes ip_address = 1;
      case 1: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(10u /* 10 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadBytes(
                input, this->mutable_ip_address()));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // int32 port = 2;
      case 2: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(16u /* 16 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   ::google::protobuf::int32, ::google::protobuf::internal::WireFormatLite::TYPE_INT32>(
                 input, &port_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // string load_balance_token = 3;
      case 3: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(26u /* 26 & 0xFF */)) {
          DO_(::google::protobuf::internal::WireFormatLite::ReadString(
                input, this->mutable_load_balance_token()));
          DO_(::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
            this->load_balance_token().data(), static_cast<int>(this->load_balance_token().length()),
            ::google::protobuf::internal::WireFormatLite::PARSE,
            "grpc.lb.v1.Server.load_balance_token"));
        } else {
          goto handle_unusual;
        }
        break;
      }

      // bool drop = 4;
      case 4: {
        if (static_cast< ::google::protobuf::uint8>(tag) ==
            static_cast< ::google::protobuf::uint8>(32u /* 32 & 0xFF */)) {

          DO_((::google::protobuf::internal::WireFormatLite::ReadPrimitive<
                   bool, ::google::protobuf::internal::WireFormatLite::TYPE_BOOL>(
                 input, &drop_)));
        } else {
          goto handle_unusual;
        }
        break;
      }

      default: {
      handle_unusual:
        if (tag == 0) {
          goto success;
        }
        DO_(::google::protobuf::internal::WireFormat::SkipField(
              input, tag, _internal_metadata_.mutable_unknown_fields()));
        break;
      }
    }
  }
success:
  // @@protoc_insertion_point(parse_success:grpc.lb.v1.Server)
  return true;
failure:
  // @@protoc_insertion_point(parse_failure:grpc.lb.v1.Server)
  return false;
#undef DO_
}

void Server::SerializeWithCachedSizes(
    ::google::protobuf::io::CodedOutputStream* output) const {
  // @@protoc_insertion_point(serialize_start:grpc.lb.v1.Server)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bytes ip_address = 1;
  if (this->ip_address().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBytesMaybeAliased(
      1, this->ip_address(), output);
  }

  // int32 port = 2;
  if (this->port() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteInt32(2, this->port(), output);
  }

  // string load_balance_token = 3;
  if (this->load_balance_token().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->load_balance_token().data(), static_cast<int>(this->load_balance_token().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.lb.v1.Server.load_balance_token");
    ::google::protobuf::internal::WireFormatLite::WriteStringMaybeAliased(
      3, this->load_balance_token(), output);
  }

  // bool drop = 4;
  if (this->drop() != 0) {
    ::google::protobuf::internal::WireFormatLite::WriteBool(4, this->drop(), output);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    ::google::protobuf::internal::WireFormat::SerializeUnknownFields(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), output);
  }
  // @@protoc_insertion_point(serialize_end:grpc.lb.v1.Server)
}

::google::protobuf::uint8* Server::InternalSerializeWithCachedSizesToArray(
    bool deterministic, ::google::protobuf::uint8* target) const {
  (void)deterministic; // Unused
  // @@protoc_insertion_point(serialize_to_array_start:grpc.lb.v1.Server)
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  // bytes ip_address = 1;
  if (this->ip_address().size() > 0) {
    target =
      ::google::protobuf::internal::WireFormatLite::WriteBytesToArray(
        1, this->ip_address(), target);
  }

  // int32 port = 2;
  if (this->port() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteInt32ToArray(2, this->port(), target);
  }

  // string load_balance_token = 3;
  if (this->load_balance_token().size() > 0) {
    ::google::protobuf::internal::WireFormatLite::VerifyUtf8String(
      this->load_balance_token().data(), static_cast<int>(this->load_balance_token().length()),
      ::google::protobuf::internal::WireFormatLite::SERIALIZE,
      "grpc.lb.v1.Server.load_balance_token");
    target =
      ::google::protobuf::internal::WireFormatLite::WriteStringToArray(
        3, this->load_balance_token(), target);
  }

  // bool drop = 4;
  if (this->drop() != 0) {
    target = ::google::protobuf::internal::WireFormatLite::WriteBoolToArray(4, this->drop(), target);
  }

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    target = ::google::protobuf::internal::WireFormat::SerializeUnknownFieldsToArray(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:grpc.lb.v1.Server)
  return target;
}

size_t Server::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:grpc.lb.v1.Server)
  size_t total_size = 0;

  if ((_internal_metadata_.have_unknown_fields() &&  ::google::protobuf::internal::GetProto3PreserveUnknownsDefault())) {
    total_size +=
      ::google::protobuf::internal::WireFormat::ComputeUnknownFieldsSize(
        (::google::protobuf::internal::GetProto3PreserveUnknownsDefault()   ? _internal_metadata_.unknown_fields()   : _internal_metadata_.default_instance()));
  }
  // bytes ip_address = 1;
  if (this->ip_address().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::BytesSize(
        this->ip_address());
  }

  // string load_balance_token = 3;
  if (this->load_balance_token().size() > 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::StringSize(
        this->load_balance_token());
  }

  // int32 port = 2;
  if (this->port() != 0) {
    total_size += 1 +
      ::google::protobuf::internal::WireFormatLite::Int32Size(
        this->port());
  }

  // bool drop = 4;
  if (this->drop() != 0) {
    total_size += 1 + 1;
  }

  int cached_size = ::google::protobuf::internal::ToCachedSize(total_size);
  GOOGLE_SAFE_CONCURRENT_WRITES_BEGIN();
  _cached_size_ = cached_size;
  GOOGLE_SAFE_CONCURRENT_WRITES_END();
  return total_size;
}

void Server::MergeFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_merge_from_start:grpc.lb.v1.Server)
  GOOGLE_DCHECK_NE(&from, this);
  const Server* source =
      ::google::protobuf::internal::DynamicCastToGenerated<const Server>(
          &from);
  if (source == NULL) {
  // @@protoc_insertion_point(generalized_merge_from_cast_fail:grpc.lb.v1.Server)
    ::google::protobuf::internal::ReflectionOps::Merge(from, this);
  } else {
  // @@protoc_insertion_point(generalized_merge_from_cast_success:grpc.lb.v1.Server)
    MergeFrom(*source);
  }
}

void Server::MergeFrom(const Server& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:grpc.lb.v1.Server)
  GOOGLE_DCHECK_NE(&from, this);
  _internal_metadata_.MergeFrom(from._internal_metadata_);
  ::google::protobuf::uint32 cached_has_bits = 0;
  (void) cached_has_bits;

  if (from.ip_address().size() > 0) {

    ip_address_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.ip_address_);
  }
  if (from.load_balance_token().size() > 0) {

    load_balance_token_.AssignWithDefault(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), from.load_balance_token_);
  }
  if (from.port() != 0) {
    set_port(from.port());
  }
  if (from.drop() != 0) {
    set_drop(from.drop());
  }
}

void Server::CopyFrom(const ::google::protobuf::Message& from) {
// @@protoc_insertion_point(generalized_copy_from_start:grpc.lb.v1.Server)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

void Server::CopyFrom(const Server& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:grpc.lb.v1.Server)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Server::IsInitialized() const {
  return true;
}

void Server::Swap(Server* other) {
  if (other == this) return;
  InternalSwap(other);
}
void Server::InternalSwap(Server* other) {
  using std::swap;
  ip_address_.Swap(&other->ip_address_);
  load_balance_token_.Swap(&other->load_balance_token_);
  swap(port_, other->port_);
  swap(drop_, other->drop_);
  _internal_metadata_.Swap(&other->_internal_metadata_);
  swap(_cached_size_, other->_cached_size_);
}

::google::protobuf::Metadata Server::GetMetadata() const {
  protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::protobuf_AssignDescriptorsOnce();
  return ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::file_level_metadata[kIndexInFileMessages];
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace v1
}  // namespace lb
}  // namespace grpc

// @@protoc_insertion_point(global_scope)
