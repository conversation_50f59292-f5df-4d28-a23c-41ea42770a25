// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: src/proto/grpc/lb/v1/load_balancer.proto

#ifndef PROTOBUF_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto__INCLUDED
#define PROTOBUF_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto__INCLUDED

#include <string>

#include <google/protobuf/stubs/common.h>

#if GOOGLE_PROTOBUF_VERSION < 3005000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please update
#error your headers.
#endif
#if 3005001 < GOOGLE_PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers.  Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include <google/protobuf/duration.pb.h>
#include <google/protobuf/timestamp.pb.h>
// @@protoc_insertion_point(includes)

namespace protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto {
// Internal implementation detail -- do not use these members.
struct TableStruct {
  static const ::google::protobuf::internal::ParseTableField entries[];
  static const ::google::protobuf::internal::AuxillaryParseTableField aux[];
  static const ::google::protobuf::internal::ParseTable schema[8];
  static const ::google::protobuf::internal::FieldMetadata field_metadata[];
  static const ::google::protobuf::internal::SerializationTable serialization_table[];
  static const ::google::protobuf::uint32 offsets[];
};
void AddDescriptors();
void InitDefaultsLoadBalanceRequestImpl();
void InitDefaultsLoadBalanceRequest();
void InitDefaultsInitialLoadBalanceRequestImpl();
void InitDefaultsInitialLoadBalanceRequest();
void InitDefaultsClientStatsPerTokenImpl();
void InitDefaultsClientStatsPerToken();
void InitDefaultsClientStatsImpl();
void InitDefaultsClientStats();
void InitDefaultsLoadBalanceResponseImpl();
void InitDefaultsLoadBalanceResponse();
void InitDefaultsInitialLoadBalanceResponseImpl();
void InitDefaultsInitialLoadBalanceResponse();
void InitDefaultsServerListImpl();
void InitDefaultsServerList();
void InitDefaultsServerImpl();
void InitDefaultsServer();
inline void InitDefaults() {
  InitDefaultsLoadBalanceRequest();
  InitDefaultsInitialLoadBalanceRequest();
  InitDefaultsClientStatsPerToken();
  InitDefaultsClientStats();
  InitDefaultsLoadBalanceResponse();
  InitDefaultsInitialLoadBalanceResponse();
  InitDefaultsServerList();
  InitDefaultsServer();
}
}  // namespace protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto
namespace grpc {
namespace lb {
namespace v1 {
class ClientStats;
class ClientStatsDefaultTypeInternal;
extern ClientStatsDefaultTypeInternal _ClientStats_default_instance_;
class ClientStatsPerToken;
class ClientStatsPerTokenDefaultTypeInternal;
extern ClientStatsPerTokenDefaultTypeInternal _ClientStatsPerToken_default_instance_;
class InitialLoadBalanceRequest;
class InitialLoadBalanceRequestDefaultTypeInternal;
extern InitialLoadBalanceRequestDefaultTypeInternal _InitialLoadBalanceRequest_default_instance_;
class InitialLoadBalanceResponse;
class InitialLoadBalanceResponseDefaultTypeInternal;
extern InitialLoadBalanceResponseDefaultTypeInternal _InitialLoadBalanceResponse_default_instance_;
class LoadBalanceRequest;
class LoadBalanceRequestDefaultTypeInternal;
extern LoadBalanceRequestDefaultTypeInternal _LoadBalanceRequest_default_instance_;
class LoadBalanceResponse;
class LoadBalanceResponseDefaultTypeInternal;
extern LoadBalanceResponseDefaultTypeInternal _LoadBalanceResponse_default_instance_;
class Server;
class ServerDefaultTypeInternal;
extern ServerDefaultTypeInternal _Server_default_instance_;
class ServerList;
class ServerListDefaultTypeInternal;
extern ServerListDefaultTypeInternal _ServerList_default_instance_;
}  // namespace v1
}  // namespace lb
}  // namespace grpc
namespace grpc {
namespace lb {
namespace v1 {

// ===================================================================

class LoadBalanceRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.lb.v1.LoadBalanceRequest) */ {
 public:
  LoadBalanceRequest();
  virtual ~LoadBalanceRequest();

  LoadBalanceRequest(const LoadBalanceRequest& from);

  inline LoadBalanceRequest& operator=(const LoadBalanceRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  LoadBalanceRequest(LoadBalanceRequest&& from) noexcept
    : LoadBalanceRequest() {
    *this = ::std::move(from);
  }

  inline LoadBalanceRequest& operator=(LoadBalanceRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const LoadBalanceRequest& default_instance();

  enum LoadBalanceRequestTypeCase {
    kInitialRequest = 1,
    kClientStats = 2,
    LOAD_BALANCE_REQUEST_TYPE_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const LoadBalanceRequest* internal_default_instance() {
    return reinterpret_cast<const LoadBalanceRequest*>(
               &_LoadBalanceRequest_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    0;

  void Swap(LoadBalanceRequest* other);
  friend void swap(LoadBalanceRequest& a, LoadBalanceRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline LoadBalanceRequest* New() const PROTOBUF_FINAL { return New(NULL); }

  LoadBalanceRequest* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const LoadBalanceRequest& from);
  void MergeFrom(const LoadBalanceRequest& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(LoadBalanceRequest* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .grpc.lb.v1.InitialLoadBalanceRequest initial_request = 1;
  bool has_initial_request() const;
  void clear_initial_request();
  static const int kInitialRequestFieldNumber = 1;
  const ::grpc::lb::v1::InitialLoadBalanceRequest& initial_request() const;
  ::grpc::lb::v1::InitialLoadBalanceRequest* release_initial_request();
  ::grpc::lb::v1::InitialLoadBalanceRequest* mutable_initial_request();
  void set_allocated_initial_request(::grpc::lb::v1::InitialLoadBalanceRequest* initial_request);

  // .grpc.lb.v1.ClientStats client_stats = 2;
  bool has_client_stats() const;
  void clear_client_stats();
  static const int kClientStatsFieldNumber = 2;
  const ::grpc::lb::v1::ClientStats& client_stats() const;
  ::grpc::lb::v1::ClientStats* release_client_stats();
  ::grpc::lb::v1::ClientStats* mutable_client_stats();
  void set_allocated_client_stats(::grpc::lb::v1::ClientStats* client_stats);

  LoadBalanceRequestTypeCase load_balance_request_type_case() const;
  // @@protoc_insertion_point(class_scope:grpc.lb.v1.LoadBalanceRequest)
 private:
  void set_has_initial_request();
  void set_has_client_stats();

  inline bool has_load_balance_request_type() const;
  void clear_load_balance_request_type();
  inline void clear_has_load_balance_request_type();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  union LoadBalanceRequestTypeUnion {
    LoadBalanceRequestTypeUnion() {}
    ::grpc::lb::v1::InitialLoadBalanceRequest* initial_request_;
    ::grpc::lb::v1::ClientStats* client_stats_;
  } load_balance_request_type_;
  mutable int _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsLoadBalanceRequestImpl();
};
// -------------------------------------------------------------------

class InitialLoadBalanceRequest : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.lb.v1.InitialLoadBalanceRequest) */ {
 public:
  InitialLoadBalanceRequest();
  virtual ~InitialLoadBalanceRequest();

  InitialLoadBalanceRequest(const InitialLoadBalanceRequest& from);

  inline InitialLoadBalanceRequest& operator=(const InitialLoadBalanceRequest& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  InitialLoadBalanceRequest(InitialLoadBalanceRequest&& from) noexcept
    : InitialLoadBalanceRequest() {
    *this = ::std::move(from);
  }

  inline InitialLoadBalanceRequest& operator=(InitialLoadBalanceRequest&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const InitialLoadBalanceRequest& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const InitialLoadBalanceRequest* internal_default_instance() {
    return reinterpret_cast<const InitialLoadBalanceRequest*>(
               &_InitialLoadBalanceRequest_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    1;

  void Swap(InitialLoadBalanceRequest* other);
  friend void swap(InitialLoadBalanceRequest& a, InitialLoadBalanceRequest& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline InitialLoadBalanceRequest* New() const PROTOBUF_FINAL { return New(NULL); }

  InitialLoadBalanceRequest* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const InitialLoadBalanceRequest& from);
  void MergeFrom(const InitialLoadBalanceRequest& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(InitialLoadBalanceRequest* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string name = 1;
  void clear_name();
  static const int kNameFieldNumber = 1;
  const ::std::string& name() const;
  void set_name(const ::std::string& value);
  #if LANG_CXX11
  void set_name(::std::string&& value);
  #endif
  void set_name(const char* value);
  void set_name(const char* value, size_t size);
  ::std::string* mutable_name();
  ::std::string* release_name();
  void set_allocated_name(::std::string* name);

  // @@protoc_insertion_point(class_scope:grpc.lb.v1.InitialLoadBalanceRequest)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr name_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsInitialLoadBalanceRequestImpl();
};
// -------------------------------------------------------------------

class ClientStatsPerToken : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.lb.v1.ClientStatsPerToken) */ {
 public:
  ClientStatsPerToken();
  virtual ~ClientStatsPerToken();

  ClientStatsPerToken(const ClientStatsPerToken& from);

  inline ClientStatsPerToken& operator=(const ClientStatsPerToken& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ClientStatsPerToken(ClientStatsPerToken&& from) noexcept
    : ClientStatsPerToken() {
    *this = ::std::move(from);
  }

  inline ClientStatsPerToken& operator=(ClientStatsPerToken&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ClientStatsPerToken& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ClientStatsPerToken* internal_default_instance() {
    return reinterpret_cast<const ClientStatsPerToken*>(
               &_ClientStatsPerToken_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    2;

  void Swap(ClientStatsPerToken* other);
  friend void swap(ClientStatsPerToken& a, ClientStatsPerToken& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ClientStatsPerToken* New() const PROTOBUF_FINAL { return New(NULL); }

  ClientStatsPerToken* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ClientStatsPerToken& from);
  void MergeFrom(const ClientStatsPerToken& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ClientStatsPerToken* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string load_balance_token = 1;
  void clear_load_balance_token();
  static const int kLoadBalanceTokenFieldNumber = 1;
  const ::std::string& load_balance_token() const;
  void set_load_balance_token(const ::std::string& value);
  #if LANG_CXX11
  void set_load_balance_token(::std::string&& value);
  #endif
  void set_load_balance_token(const char* value);
  void set_load_balance_token(const char* value, size_t size);
  ::std::string* mutable_load_balance_token();
  ::std::string* release_load_balance_token();
  void set_allocated_load_balance_token(::std::string* load_balance_token);

  // int64 num_calls = 2;
  void clear_num_calls();
  static const int kNumCallsFieldNumber = 2;
  ::google::protobuf::int64 num_calls() const;
  void set_num_calls(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:grpc.lb.v1.ClientStatsPerToken)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr load_balance_token_;
  ::google::protobuf::int64 num_calls_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsClientStatsPerTokenImpl();
};
// -------------------------------------------------------------------

class ClientStats : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.lb.v1.ClientStats) */ {
 public:
  ClientStats();
  virtual ~ClientStats();

  ClientStats(const ClientStats& from);

  inline ClientStats& operator=(const ClientStats& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ClientStats(ClientStats&& from) noexcept
    : ClientStats() {
    *this = ::std::move(from);
  }

  inline ClientStats& operator=(ClientStats&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ClientStats& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ClientStats* internal_default_instance() {
    return reinterpret_cast<const ClientStats*>(
               &_ClientStats_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    3;

  void Swap(ClientStats* other);
  friend void swap(ClientStats& a, ClientStats& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ClientStats* New() const PROTOBUF_FINAL { return New(NULL); }

  ClientStats* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ClientStats& from);
  void MergeFrom(const ClientStats& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ClientStats* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .grpc.lb.v1.ClientStatsPerToken calls_finished_with_drop = 8;
  int calls_finished_with_drop_size() const;
  void clear_calls_finished_with_drop();
  static const int kCallsFinishedWithDropFieldNumber = 8;
  const ::grpc::lb::v1::ClientStatsPerToken& calls_finished_with_drop(int index) const;
  ::grpc::lb::v1::ClientStatsPerToken* mutable_calls_finished_with_drop(int index);
  ::grpc::lb::v1::ClientStatsPerToken* add_calls_finished_with_drop();
  ::google::protobuf::RepeatedPtrField< ::grpc::lb::v1::ClientStatsPerToken >*
      mutable_calls_finished_with_drop();
  const ::google::protobuf::RepeatedPtrField< ::grpc::lb::v1::ClientStatsPerToken >&
      calls_finished_with_drop() const;

  // .google.protobuf.Timestamp timestamp = 1;
  bool has_timestamp() const;
  void clear_timestamp();
  static const int kTimestampFieldNumber = 1;
  const ::google::protobuf::Timestamp& timestamp() const;
  ::google::protobuf::Timestamp* release_timestamp();
  ::google::protobuf::Timestamp* mutable_timestamp();
  void set_allocated_timestamp(::google::protobuf::Timestamp* timestamp);

  // int64 num_calls_started = 2;
  void clear_num_calls_started();
  static const int kNumCallsStartedFieldNumber = 2;
  ::google::protobuf::int64 num_calls_started() const;
  void set_num_calls_started(::google::protobuf::int64 value);

  // int64 num_calls_finished = 3;
  void clear_num_calls_finished();
  static const int kNumCallsFinishedFieldNumber = 3;
  ::google::protobuf::int64 num_calls_finished() const;
  void set_num_calls_finished(::google::protobuf::int64 value);

  // int64 num_calls_finished_with_client_failed_to_send = 6;
  void clear_num_calls_finished_with_client_failed_to_send();
  static const int kNumCallsFinishedWithClientFailedToSendFieldNumber = 6;
  ::google::protobuf::int64 num_calls_finished_with_client_failed_to_send() const;
  void set_num_calls_finished_with_client_failed_to_send(::google::protobuf::int64 value);

  // int64 num_calls_finished_known_received = 7;
  void clear_num_calls_finished_known_received();
  static const int kNumCallsFinishedKnownReceivedFieldNumber = 7;
  ::google::protobuf::int64 num_calls_finished_known_received() const;
  void set_num_calls_finished_known_received(::google::protobuf::int64 value);

  // @@protoc_insertion_point(class_scope:grpc.lb.v1.ClientStats)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::grpc::lb::v1::ClientStatsPerToken > calls_finished_with_drop_;
  ::google::protobuf::Timestamp* timestamp_;
  ::google::protobuf::int64 num_calls_started_;
  ::google::protobuf::int64 num_calls_finished_;
  ::google::protobuf::int64 num_calls_finished_with_client_failed_to_send_;
  ::google::protobuf::int64 num_calls_finished_known_received_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsClientStatsImpl();
};
// -------------------------------------------------------------------

class LoadBalanceResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.lb.v1.LoadBalanceResponse) */ {
 public:
  LoadBalanceResponse();
  virtual ~LoadBalanceResponse();

  LoadBalanceResponse(const LoadBalanceResponse& from);

  inline LoadBalanceResponse& operator=(const LoadBalanceResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  LoadBalanceResponse(LoadBalanceResponse&& from) noexcept
    : LoadBalanceResponse() {
    *this = ::std::move(from);
  }

  inline LoadBalanceResponse& operator=(LoadBalanceResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const LoadBalanceResponse& default_instance();

  enum LoadBalanceResponseTypeCase {
    kInitialResponse = 1,
    kServerList = 2,
    LOAD_BALANCE_RESPONSE_TYPE_NOT_SET = 0,
  };

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const LoadBalanceResponse* internal_default_instance() {
    return reinterpret_cast<const LoadBalanceResponse*>(
               &_LoadBalanceResponse_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    4;

  void Swap(LoadBalanceResponse* other);
  friend void swap(LoadBalanceResponse& a, LoadBalanceResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline LoadBalanceResponse* New() const PROTOBUF_FINAL { return New(NULL); }

  LoadBalanceResponse* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const LoadBalanceResponse& from);
  void MergeFrom(const LoadBalanceResponse& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(LoadBalanceResponse* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // .grpc.lb.v1.InitialLoadBalanceResponse initial_response = 1;
  bool has_initial_response() const;
  void clear_initial_response();
  static const int kInitialResponseFieldNumber = 1;
  const ::grpc::lb::v1::InitialLoadBalanceResponse& initial_response() const;
  ::grpc::lb::v1::InitialLoadBalanceResponse* release_initial_response();
  ::grpc::lb::v1::InitialLoadBalanceResponse* mutable_initial_response();
  void set_allocated_initial_response(::grpc::lb::v1::InitialLoadBalanceResponse* initial_response);

  // .grpc.lb.v1.ServerList server_list = 2;
  bool has_server_list() const;
  void clear_server_list();
  static const int kServerListFieldNumber = 2;
  const ::grpc::lb::v1::ServerList& server_list() const;
  ::grpc::lb::v1::ServerList* release_server_list();
  ::grpc::lb::v1::ServerList* mutable_server_list();
  void set_allocated_server_list(::grpc::lb::v1::ServerList* server_list);

  LoadBalanceResponseTypeCase load_balance_response_type_case() const;
  // @@protoc_insertion_point(class_scope:grpc.lb.v1.LoadBalanceResponse)
 private:
  void set_has_initial_response();
  void set_has_server_list();

  inline bool has_load_balance_response_type() const;
  void clear_load_balance_response_type();
  inline void clear_has_load_balance_response_type();

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  union LoadBalanceResponseTypeUnion {
    LoadBalanceResponseTypeUnion() {}
    ::grpc::lb::v1::InitialLoadBalanceResponse* initial_response_;
    ::grpc::lb::v1::ServerList* server_list_;
  } load_balance_response_type_;
  mutable int _cached_size_;
  ::google::protobuf::uint32 _oneof_case_[1];

  friend struct ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsLoadBalanceResponseImpl();
};
// -------------------------------------------------------------------

class InitialLoadBalanceResponse : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.lb.v1.InitialLoadBalanceResponse) */ {
 public:
  InitialLoadBalanceResponse();
  virtual ~InitialLoadBalanceResponse();

  InitialLoadBalanceResponse(const InitialLoadBalanceResponse& from);

  inline InitialLoadBalanceResponse& operator=(const InitialLoadBalanceResponse& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  InitialLoadBalanceResponse(InitialLoadBalanceResponse&& from) noexcept
    : InitialLoadBalanceResponse() {
    *this = ::std::move(from);
  }

  inline InitialLoadBalanceResponse& operator=(InitialLoadBalanceResponse&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const InitialLoadBalanceResponse& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const InitialLoadBalanceResponse* internal_default_instance() {
    return reinterpret_cast<const InitialLoadBalanceResponse*>(
               &_InitialLoadBalanceResponse_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    5;

  void Swap(InitialLoadBalanceResponse* other);
  friend void swap(InitialLoadBalanceResponse& a, InitialLoadBalanceResponse& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline InitialLoadBalanceResponse* New() const PROTOBUF_FINAL { return New(NULL); }

  InitialLoadBalanceResponse* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const InitialLoadBalanceResponse& from);
  void MergeFrom(const InitialLoadBalanceResponse& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(InitialLoadBalanceResponse* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // string load_balancer_delegate = 1;
  void clear_load_balancer_delegate();
  static const int kLoadBalancerDelegateFieldNumber = 1;
  const ::std::string& load_balancer_delegate() const;
  void set_load_balancer_delegate(const ::std::string& value);
  #if LANG_CXX11
  void set_load_balancer_delegate(::std::string&& value);
  #endif
  void set_load_balancer_delegate(const char* value);
  void set_load_balancer_delegate(const char* value, size_t size);
  ::std::string* mutable_load_balancer_delegate();
  ::std::string* release_load_balancer_delegate();
  void set_allocated_load_balancer_delegate(::std::string* load_balancer_delegate);

  // .google.protobuf.Duration client_stats_report_interval = 2;
  bool has_client_stats_report_interval() const;
  void clear_client_stats_report_interval();
  static const int kClientStatsReportIntervalFieldNumber = 2;
  const ::google::protobuf::Duration& client_stats_report_interval() const;
  ::google::protobuf::Duration* release_client_stats_report_interval();
  ::google::protobuf::Duration* mutable_client_stats_report_interval();
  void set_allocated_client_stats_report_interval(::google::protobuf::Duration* client_stats_report_interval);

  // @@protoc_insertion_point(class_scope:grpc.lb.v1.InitialLoadBalanceResponse)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr load_balancer_delegate_;
  ::google::protobuf::Duration* client_stats_report_interval_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsInitialLoadBalanceResponseImpl();
};
// -------------------------------------------------------------------

class ServerList : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.lb.v1.ServerList) */ {
 public:
  ServerList();
  virtual ~ServerList();

  ServerList(const ServerList& from);

  inline ServerList& operator=(const ServerList& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  ServerList(ServerList&& from) noexcept
    : ServerList() {
    *this = ::std::move(from);
  }

  inline ServerList& operator=(ServerList&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const ServerList& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const ServerList* internal_default_instance() {
    return reinterpret_cast<const ServerList*>(
               &_ServerList_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    6;

  void Swap(ServerList* other);
  friend void swap(ServerList& a, ServerList& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline ServerList* New() const PROTOBUF_FINAL { return New(NULL); }

  ServerList* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const ServerList& from);
  void MergeFrom(const ServerList& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(ServerList* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // repeated .grpc.lb.v1.Server servers = 1;
  int servers_size() const;
  void clear_servers();
  static const int kServersFieldNumber = 1;
  const ::grpc::lb::v1::Server& servers(int index) const;
  ::grpc::lb::v1::Server* mutable_servers(int index);
  ::grpc::lb::v1::Server* add_servers();
  ::google::protobuf::RepeatedPtrField< ::grpc::lb::v1::Server >*
      mutable_servers();
  const ::google::protobuf::RepeatedPtrField< ::grpc::lb::v1::Server >&
      servers() const;

  // @@protoc_insertion_point(class_scope:grpc.lb.v1.ServerList)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::RepeatedPtrField< ::grpc::lb::v1::Server > servers_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsServerListImpl();
};
// -------------------------------------------------------------------

class Server : public ::google::protobuf::Message /* @@protoc_insertion_point(class_definition:grpc.lb.v1.Server) */ {
 public:
  Server();
  virtual ~Server();

  Server(const Server& from);

  inline Server& operator=(const Server& from) {
    CopyFrom(from);
    return *this;
  }
  #if LANG_CXX11
  Server(Server&& from) noexcept
    : Server() {
    *this = ::std::move(from);
  }

  inline Server& operator=(Server&& from) noexcept {
    if (GetArenaNoVirtual() == from.GetArenaNoVirtual()) {
      if (this != &from) InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }
  #endif
  static const ::google::protobuf::Descriptor* descriptor();
  static const Server& default_instance();

  static void InitAsDefaultInstance();  // FOR INTERNAL USE ONLY
  static inline const Server* internal_default_instance() {
    return reinterpret_cast<const Server*>(
               &_Server_default_instance_);
  }
  static PROTOBUF_CONSTEXPR int const kIndexInFileMessages =
    7;

  void Swap(Server* other);
  friend void swap(Server& a, Server& b) {
    a.Swap(&b);
  }

  // implements Message ----------------------------------------------

  inline Server* New() const PROTOBUF_FINAL { return New(NULL); }

  Server* New(::google::protobuf::Arena* arena) const PROTOBUF_FINAL;
  void CopyFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void MergeFrom(const ::google::protobuf::Message& from) PROTOBUF_FINAL;
  void CopyFrom(const Server& from);
  void MergeFrom(const Server& from);
  void Clear() PROTOBUF_FINAL;
  bool IsInitialized() const PROTOBUF_FINAL;

  size_t ByteSizeLong() const PROTOBUF_FINAL;
  bool MergePartialFromCodedStream(
      ::google::protobuf::io::CodedInputStream* input) PROTOBUF_FINAL;
  void SerializeWithCachedSizes(
      ::google::protobuf::io::CodedOutputStream* output) const PROTOBUF_FINAL;
  ::google::protobuf::uint8* InternalSerializeWithCachedSizesToArray(
      bool deterministic, ::google::protobuf::uint8* target) const PROTOBUF_FINAL;
  int GetCachedSize() const PROTOBUF_FINAL { return _cached_size_; }
  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const PROTOBUF_FINAL;
  void InternalSwap(Server* other);
  private:
  inline ::google::protobuf::Arena* GetArenaNoVirtual() const {
    return NULL;
  }
  inline void* MaybeArenaPtr() const {
    return NULL;
  }
  public:

  ::google::protobuf::Metadata GetMetadata() const PROTOBUF_FINAL;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // bytes ip_address = 1;
  void clear_ip_address();
  static const int kIpAddressFieldNumber = 1;
  const ::std::string& ip_address() const;
  void set_ip_address(const ::std::string& value);
  #if LANG_CXX11
  void set_ip_address(::std::string&& value);
  #endif
  void set_ip_address(const char* value);
  void set_ip_address(const void* value, size_t size);
  ::std::string* mutable_ip_address();
  ::std::string* release_ip_address();
  void set_allocated_ip_address(::std::string* ip_address);

  // string load_balance_token = 3;
  void clear_load_balance_token();
  static const int kLoadBalanceTokenFieldNumber = 3;
  const ::std::string& load_balance_token() const;
  void set_load_balance_token(const ::std::string& value);
  #if LANG_CXX11
  void set_load_balance_token(::std::string&& value);
  #endif
  void set_load_balance_token(const char* value);
  void set_load_balance_token(const char* value, size_t size);
  ::std::string* mutable_load_balance_token();
  ::std::string* release_load_balance_token();
  void set_allocated_load_balance_token(::std::string* load_balance_token);

  // int32 port = 2;
  void clear_port();
  static const int kPortFieldNumber = 2;
  ::google::protobuf::int32 port() const;
  void set_port(::google::protobuf::int32 value);

  // bool drop = 4;
  void clear_drop();
  static const int kDropFieldNumber = 4;
  bool drop() const;
  void set_drop(bool value);

  // @@protoc_insertion_point(class_scope:grpc.lb.v1.Server)
 private:

  ::google::protobuf::internal::InternalMetadataWithArena _internal_metadata_;
  ::google::protobuf::internal::ArenaStringPtr ip_address_;
  ::google::protobuf::internal::ArenaStringPtr load_balance_token_;
  ::google::protobuf::int32 port_;
  bool drop_;
  mutable int _cached_size_;
  friend struct ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::TableStruct;
  friend void ::protobuf_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto::InitDefaultsServerImpl();
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// LoadBalanceRequest

// .grpc.lb.v1.InitialLoadBalanceRequest initial_request = 1;
inline bool LoadBalanceRequest::has_initial_request() const {
  return load_balance_request_type_case() == kInitialRequest;
}
inline void LoadBalanceRequest::set_has_initial_request() {
  _oneof_case_[0] = kInitialRequest;
}
inline void LoadBalanceRequest::clear_initial_request() {
  if (has_initial_request()) {
    delete load_balance_request_type_.initial_request_;
    clear_has_load_balance_request_type();
  }
}
inline ::grpc::lb::v1::InitialLoadBalanceRequest* LoadBalanceRequest::release_initial_request() {
  // @@protoc_insertion_point(field_release:grpc.lb.v1.LoadBalanceRequest.initial_request)
  if (has_initial_request()) {
    clear_has_load_balance_request_type();
      ::grpc::lb::v1::InitialLoadBalanceRequest* temp = load_balance_request_type_.initial_request_;
    load_balance_request_type_.initial_request_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::grpc::lb::v1::InitialLoadBalanceRequest& LoadBalanceRequest::initial_request() const {
  // @@protoc_insertion_point(field_get:grpc.lb.v1.LoadBalanceRequest.initial_request)
  return has_initial_request()
      ? *load_balance_request_type_.initial_request_
      : *reinterpret_cast< ::grpc::lb::v1::InitialLoadBalanceRequest*>(&::grpc::lb::v1::_InitialLoadBalanceRequest_default_instance_);
}
inline ::grpc::lb::v1::InitialLoadBalanceRequest* LoadBalanceRequest::mutable_initial_request() {
  if (!has_initial_request()) {
    clear_load_balance_request_type();
    set_has_initial_request();
    load_balance_request_type_.initial_request_ = new ::grpc::lb::v1::InitialLoadBalanceRequest;
  }
  // @@protoc_insertion_point(field_mutable:grpc.lb.v1.LoadBalanceRequest.initial_request)
  return load_balance_request_type_.initial_request_;
}

// .grpc.lb.v1.ClientStats client_stats = 2;
inline bool LoadBalanceRequest::has_client_stats() const {
  return load_balance_request_type_case() == kClientStats;
}
inline void LoadBalanceRequest::set_has_client_stats() {
  _oneof_case_[0] = kClientStats;
}
inline void LoadBalanceRequest::clear_client_stats() {
  if (has_client_stats()) {
    delete load_balance_request_type_.client_stats_;
    clear_has_load_balance_request_type();
  }
}
inline ::grpc::lb::v1::ClientStats* LoadBalanceRequest::release_client_stats() {
  // @@protoc_insertion_point(field_release:grpc.lb.v1.LoadBalanceRequest.client_stats)
  if (has_client_stats()) {
    clear_has_load_balance_request_type();
      ::grpc::lb::v1::ClientStats* temp = load_balance_request_type_.client_stats_;
    load_balance_request_type_.client_stats_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::grpc::lb::v1::ClientStats& LoadBalanceRequest::client_stats() const {
  // @@protoc_insertion_point(field_get:grpc.lb.v1.LoadBalanceRequest.client_stats)
  return has_client_stats()
      ? *load_balance_request_type_.client_stats_
      : *reinterpret_cast< ::grpc::lb::v1::ClientStats*>(&::grpc::lb::v1::_ClientStats_default_instance_);
}
inline ::grpc::lb::v1::ClientStats* LoadBalanceRequest::mutable_client_stats() {
  if (!has_client_stats()) {
    clear_load_balance_request_type();
    set_has_client_stats();
    load_balance_request_type_.client_stats_ = new ::grpc::lb::v1::ClientStats;
  }
  // @@protoc_insertion_point(field_mutable:grpc.lb.v1.LoadBalanceRequest.client_stats)
  return load_balance_request_type_.client_stats_;
}

inline bool LoadBalanceRequest::has_load_balance_request_type() const {
  return load_balance_request_type_case() != LOAD_BALANCE_REQUEST_TYPE_NOT_SET;
}
inline void LoadBalanceRequest::clear_has_load_balance_request_type() {
  _oneof_case_[0] = LOAD_BALANCE_REQUEST_TYPE_NOT_SET;
}
inline LoadBalanceRequest::LoadBalanceRequestTypeCase LoadBalanceRequest::load_balance_request_type_case() const {
  return LoadBalanceRequest::LoadBalanceRequestTypeCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// InitialLoadBalanceRequest

// string name = 1;
inline void InitialLoadBalanceRequest::clear_name() {
  name_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& InitialLoadBalanceRequest::name() const {
  // @@protoc_insertion_point(field_get:grpc.lb.v1.InitialLoadBalanceRequest.name)
  return name_.GetNoArena();
}
inline void InitialLoadBalanceRequest::set_name(const ::std::string& value) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.lb.v1.InitialLoadBalanceRequest.name)
}
#if LANG_CXX11
inline void InitialLoadBalanceRequest::set_name(::std::string&& value) {
  
  name_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.lb.v1.InitialLoadBalanceRequest.name)
}
#endif
inline void InitialLoadBalanceRequest::set_name(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.lb.v1.InitialLoadBalanceRequest.name)
}
inline void InitialLoadBalanceRequest::set_name(const char* value, size_t size) {
  
  name_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.lb.v1.InitialLoadBalanceRequest.name)
}
inline ::std::string* InitialLoadBalanceRequest::mutable_name() {
  
  // @@protoc_insertion_point(field_mutable:grpc.lb.v1.InitialLoadBalanceRequest.name)
  return name_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* InitialLoadBalanceRequest::release_name() {
  // @@protoc_insertion_point(field_release:grpc.lb.v1.InitialLoadBalanceRequest.name)
  
  return name_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void InitialLoadBalanceRequest::set_allocated_name(::std::string* name) {
  if (name != NULL) {
    
  } else {
    
  }
  name_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), name);
  // @@protoc_insertion_point(field_set_allocated:grpc.lb.v1.InitialLoadBalanceRequest.name)
}

// -------------------------------------------------------------------

// ClientStatsPerToken

// string load_balance_token = 1;
inline void ClientStatsPerToken::clear_load_balance_token() {
  load_balance_token_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& ClientStatsPerToken::load_balance_token() const {
  // @@protoc_insertion_point(field_get:grpc.lb.v1.ClientStatsPerToken.load_balance_token)
  return load_balance_token_.GetNoArena();
}
inline void ClientStatsPerToken::set_load_balance_token(const ::std::string& value) {
  
  load_balance_token_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.lb.v1.ClientStatsPerToken.load_balance_token)
}
#if LANG_CXX11
inline void ClientStatsPerToken::set_load_balance_token(::std::string&& value) {
  
  load_balance_token_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.lb.v1.ClientStatsPerToken.load_balance_token)
}
#endif
inline void ClientStatsPerToken::set_load_balance_token(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  load_balance_token_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.lb.v1.ClientStatsPerToken.load_balance_token)
}
inline void ClientStatsPerToken::set_load_balance_token(const char* value, size_t size) {
  
  load_balance_token_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.lb.v1.ClientStatsPerToken.load_balance_token)
}
inline ::std::string* ClientStatsPerToken::mutable_load_balance_token() {
  
  // @@protoc_insertion_point(field_mutable:grpc.lb.v1.ClientStatsPerToken.load_balance_token)
  return load_balance_token_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* ClientStatsPerToken::release_load_balance_token() {
  // @@protoc_insertion_point(field_release:grpc.lb.v1.ClientStatsPerToken.load_balance_token)
  
  return load_balance_token_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void ClientStatsPerToken::set_allocated_load_balance_token(::std::string* load_balance_token) {
  if (load_balance_token != NULL) {
    
  } else {
    
  }
  load_balance_token_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), load_balance_token);
  // @@protoc_insertion_point(field_set_allocated:grpc.lb.v1.ClientStatsPerToken.load_balance_token)
}

// int64 num_calls = 2;
inline void ClientStatsPerToken::clear_num_calls() {
  num_calls_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ClientStatsPerToken::num_calls() const {
  // @@protoc_insertion_point(field_get:grpc.lb.v1.ClientStatsPerToken.num_calls)
  return num_calls_;
}
inline void ClientStatsPerToken::set_num_calls(::google::protobuf::int64 value) {
  
  num_calls_ = value;
  // @@protoc_insertion_point(field_set:grpc.lb.v1.ClientStatsPerToken.num_calls)
}

// -------------------------------------------------------------------

// ClientStats

// .google.protobuf.Timestamp timestamp = 1;
inline bool ClientStats::has_timestamp() const {
  return this != internal_default_instance() && timestamp_ != NULL;
}
inline const ::google::protobuf::Timestamp& ClientStats::timestamp() const {
  const ::google::protobuf::Timestamp* p = timestamp_;
  // @@protoc_insertion_point(field_get:grpc.lb.v1.ClientStats.timestamp)
  return p != NULL ? *p : *reinterpret_cast<const ::google::protobuf::Timestamp*>(
      &::google::protobuf::_Timestamp_default_instance_);
}
inline ::google::protobuf::Timestamp* ClientStats::release_timestamp() {
  // @@protoc_insertion_point(field_release:grpc.lb.v1.ClientStats.timestamp)
  
  ::google::protobuf::Timestamp* temp = timestamp_;
  timestamp_ = NULL;
  return temp;
}
inline ::google::protobuf::Timestamp* ClientStats::mutable_timestamp() {
  
  if (timestamp_ == NULL) {
    timestamp_ = new ::google::protobuf::Timestamp;
  }
  // @@protoc_insertion_point(field_mutable:grpc.lb.v1.ClientStats.timestamp)
  return timestamp_;
}
inline void ClientStats::set_allocated_timestamp(::google::protobuf::Timestamp* timestamp) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(timestamp_);
  }
  if (timestamp) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast< ::google::protobuf::MessageLite*>(timestamp)->GetArena();
    if (message_arena != submessage_arena) {
      timestamp = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, timestamp, submessage_arena);
    }
    
  } else {
    
  }
  timestamp_ = timestamp;
  // @@protoc_insertion_point(field_set_allocated:grpc.lb.v1.ClientStats.timestamp)
}

// int64 num_calls_started = 2;
inline void ClientStats::clear_num_calls_started() {
  num_calls_started_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ClientStats::num_calls_started() const {
  // @@protoc_insertion_point(field_get:grpc.lb.v1.ClientStats.num_calls_started)
  return num_calls_started_;
}
inline void ClientStats::set_num_calls_started(::google::protobuf::int64 value) {
  
  num_calls_started_ = value;
  // @@protoc_insertion_point(field_set:grpc.lb.v1.ClientStats.num_calls_started)
}

// int64 num_calls_finished = 3;
inline void ClientStats::clear_num_calls_finished() {
  num_calls_finished_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ClientStats::num_calls_finished() const {
  // @@protoc_insertion_point(field_get:grpc.lb.v1.ClientStats.num_calls_finished)
  return num_calls_finished_;
}
inline void ClientStats::set_num_calls_finished(::google::protobuf::int64 value) {
  
  num_calls_finished_ = value;
  // @@protoc_insertion_point(field_set:grpc.lb.v1.ClientStats.num_calls_finished)
}

// int64 num_calls_finished_with_client_failed_to_send = 6;
inline void ClientStats::clear_num_calls_finished_with_client_failed_to_send() {
  num_calls_finished_with_client_failed_to_send_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ClientStats::num_calls_finished_with_client_failed_to_send() const {
  // @@protoc_insertion_point(field_get:grpc.lb.v1.ClientStats.num_calls_finished_with_client_failed_to_send)
  return num_calls_finished_with_client_failed_to_send_;
}
inline void ClientStats::set_num_calls_finished_with_client_failed_to_send(::google::protobuf::int64 value) {
  
  num_calls_finished_with_client_failed_to_send_ = value;
  // @@protoc_insertion_point(field_set:grpc.lb.v1.ClientStats.num_calls_finished_with_client_failed_to_send)
}

// int64 num_calls_finished_known_received = 7;
inline void ClientStats::clear_num_calls_finished_known_received() {
  num_calls_finished_known_received_ = GOOGLE_LONGLONG(0);
}
inline ::google::protobuf::int64 ClientStats::num_calls_finished_known_received() const {
  // @@protoc_insertion_point(field_get:grpc.lb.v1.ClientStats.num_calls_finished_known_received)
  return num_calls_finished_known_received_;
}
inline void ClientStats::set_num_calls_finished_known_received(::google::protobuf::int64 value) {
  
  num_calls_finished_known_received_ = value;
  // @@protoc_insertion_point(field_set:grpc.lb.v1.ClientStats.num_calls_finished_known_received)
}

// repeated .grpc.lb.v1.ClientStatsPerToken calls_finished_with_drop = 8;
inline int ClientStats::calls_finished_with_drop_size() const {
  return calls_finished_with_drop_.size();
}
inline void ClientStats::clear_calls_finished_with_drop() {
  calls_finished_with_drop_.Clear();
}
inline const ::grpc::lb::v1::ClientStatsPerToken& ClientStats::calls_finished_with_drop(int index) const {
  // @@protoc_insertion_point(field_get:grpc.lb.v1.ClientStats.calls_finished_with_drop)
  return calls_finished_with_drop_.Get(index);
}
inline ::grpc::lb::v1::ClientStatsPerToken* ClientStats::mutable_calls_finished_with_drop(int index) {
  // @@protoc_insertion_point(field_mutable:grpc.lb.v1.ClientStats.calls_finished_with_drop)
  return calls_finished_with_drop_.Mutable(index);
}
inline ::grpc::lb::v1::ClientStatsPerToken* ClientStats::add_calls_finished_with_drop() {
  // @@protoc_insertion_point(field_add:grpc.lb.v1.ClientStats.calls_finished_with_drop)
  return calls_finished_with_drop_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::grpc::lb::v1::ClientStatsPerToken >*
ClientStats::mutable_calls_finished_with_drop() {
  // @@protoc_insertion_point(field_mutable_list:grpc.lb.v1.ClientStats.calls_finished_with_drop)
  return &calls_finished_with_drop_;
}
inline const ::google::protobuf::RepeatedPtrField< ::grpc::lb::v1::ClientStatsPerToken >&
ClientStats::calls_finished_with_drop() const {
  // @@protoc_insertion_point(field_list:grpc.lb.v1.ClientStats.calls_finished_with_drop)
  return calls_finished_with_drop_;
}

// -------------------------------------------------------------------

// LoadBalanceResponse

// .grpc.lb.v1.InitialLoadBalanceResponse initial_response = 1;
inline bool LoadBalanceResponse::has_initial_response() const {
  return load_balance_response_type_case() == kInitialResponse;
}
inline void LoadBalanceResponse::set_has_initial_response() {
  _oneof_case_[0] = kInitialResponse;
}
inline void LoadBalanceResponse::clear_initial_response() {
  if (has_initial_response()) {
    delete load_balance_response_type_.initial_response_;
    clear_has_load_balance_response_type();
  }
}
inline ::grpc::lb::v1::InitialLoadBalanceResponse* LoadBalanceResponse::release_initial_response() {
  // @@protoc_insertion_point(field_release:grpc.lb.v1.LoadBalanceResponse.initial_response)
  if (has_initial_response()) {
    clear_has_load_balance_response_type();
      ::grpc::lb::v1::InitialLoadBalanceResponse* temp = load_balance_response_type_.initial_response_;
    load_balance_response_type_.initial_response_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::grpc::lb::v1::InitialLoadBalanceResponse& LoadBalanceResponse::initial_response() const {
  // @@protoc_insertion_point(field_get:grpc.lb.v1.LoadBalanceResponse.initial_response)
  return has_initial_response()
      ? *load_balance_response_type_.initial_response_
      : *reinterpret_cast< ::grpc::lb::v1::InitialLoadBalanceResponse*>(&::grpc::lb::v1::_InitialLoadBalanceResponse_default_instance_);
}
inline ::grpc::lb::v1::InitialLoadBalanceResponse* LoadBalanceResponse::mutable_initial_response() {
  if (!has_initial_response()) {
    clear_load_balance_response_type();
    set_has_initial_response();
    load_balance_response_type_.initial_response_ = new ::grpc::lb::v1::InitialLoadBalanceResponse;
  }
  // @@protoc_insertion_point(field_mutable:grpc.lb.v1.LoadBalanceResponse.initial_response)
  return load_balance_response_type_.initial_response_;
}

// .grpc.lb.v1.ServerList server_list = 2;
inline bool LoadBalanceResponse::has_server_list() const {
  return load_balance_response_type_case() == kServerList;
}
inline void LoadBalanceResponse::set_has_server_list() {
  _oneof_case_[0] = kServerList;
}
inline void LoadBalanceResponse::clear_server_list() {
  if (has_server_list()) {
    delete load_balance_response_type_.server_list_;
    clear_has_load_balance_response_type();
  }
}
inline ::grpc::lb::v1::ServerList* LoadBalanceResponse::release_server_list() {
  // @@protoc_insertion_point(field_release:grpc.lb.v1.LoadBalanceResponse.server_list)
  if (has_server_list()) {
    clear_has_load_balance_response_type();
      ::grpc::lb::v1::ServerList* temp = load_balance_response_type_.server_list_;
    load_balance_response_type_.server_list_ = NULL;
    return temp;
  } else {
    return NULL;
  }
}
inline const ::grpc::lb::v1::ServerList& LoadBalanceResponse::server_list() const {
  // @@protoc_insertion_point(field_get:grpc.lb.v1.LoadBalanceResponse.server_list)
  return has_server_list()
      ? *load_balance_response_type_.server_list_
      : *reinterpret_cast< ::grpc::lb::v1::ServerList*>(&::grpc::lb::v1::_ServerList_default_instance_);
}
inline ::grpc::lb::v1::ServerList* LoadBalanceResponse::mutable_server_list() {
  if (!has_server_list()) {
    clear_load_balance_response_type();
    set_has_server_list();
    load_balance_response_type_.server_list_ = new ::grpc::lb::v1::ServerList;
  }
  // @@protoc_insertion_point(field_mutable:grpc.lb.v1.LoadBalanceResponse.server_list)
  return load_balance_response_type_.server_list_;
}

inline bool LoadBalanceResponse::has_load_balance_response_type() const {
  return load_balance_response_type_case() != LOAD_BALANCE_RESPONSE_TYPE_NOT_SET;
}
inline void LoadBalanceResponse::clear_has_load_balance_response_type() {
  _oneof_case_[0] = LOAD_BALANCE_RESPONSE_TYPE_NOT_SET;
}
inline LoadBalanceResponse::LoadBalanceResponseTypeCase LoadBalanceResponse::load_balance_response_type_case() const {
  return LoadBalanceResponse::LoadBalanceResponseTypeCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// InitialLoadBalanceResponse

// string load_balancer_delegate = 1;
inline void InitialLoadBalanceResponse::clear_load_balancer_delegate() {
  load_balancer_delegate_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& InitialLoadBalanceResponse::load_balancer_delegate() const {
  // @@protoc_insertion_point(field_get:grpc.lb.v1.InitialLoadBalanceResponse.load_balancer_delegate)
  return load_balancer_delegate_.GetNoArena();
}
inline void InitialLoadBalanceResponse::set_load_balancer_delegate(const ::std::string& value) {
  
  load_balancer_delegate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.lb.v1.InitialLoadBalanceResponse.load_balancer_delegate)
}
#if LANG_CXX11
inline void InitialLoadBalanceResponse::set_load_balancer_delegate(::std::string&& value) {
  
  load_balancer_delegate_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.lb.v1.InitialLoadBalanceResponse.load_balancer_delegate)
}
#endif
inline void InitialLoadBalanceResponse::set_load_balancer_delegate(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  load_balancer_delegate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.lb.v1.InitialLoadBalanceResponse.load_balancer_delegate)
}
inline void InitialLoadBalanceResponse::set_load_balancer_delegate(const char* value, size_t size) {
  
  load_balancer_delegate_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.lb.v1.InitialLoadBalanceResponse.load_balancer_delegate)
}
inline ::std::string* InitialLoadBalanceResponse::mutable_load_balancer_delegate() {
  
  // @@protoc_insertion_point(field_mutable:grpc.lb.v1.InitialLoadBalanceResponse.load_balancer_delegate)
  return load_balancer_delegate_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* InitialLoadBalanceResponse::release_load_balancer_delegate() {
  // @@protoc_insertion_point(field_release:grpc.lb.v1.InitialLoadBalanceResponse.load_balancer_delegate)
  
  return load_balancer_delegate_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void InitialLoadBalanceResponse::set_allocated_load_balancer_delegate(::std::string* load_balancer_delegate) {
  if (load_balancer_delegate != NULL) {
    
  } else {
    
  }
  load_balancer_delegate_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), load_balancer_delegate);
  // @@protoc_insertion_point(field_set_allocated:grpc.lb.v1.InitialLoadBalanceResponse.load_balancer_delegate)
}

// .google.protobuf.Duration client_stats_report_interval = 2;
inline bool InitialLoadBalanceResponse::has_client_stats_report_interval() const {
  return this != internal_default_instance() && client_stats_report_interval_ != NULL;
}
inline const ::google::protobuf::Duration& InitialLoadBalanceResponse::client_stats_report_interval() const {
  const ::google::protobuf::Duration* p = client_stats_report_interval_;
  // @@protoc_insertion_point(field_get:grpc.lb.v1.InitialLoadBalanceResponse.client_stats_report_interval)
  return p != NULL ? *p : *reinterpret_cast<const ::google::protobuf::Duration*>(
      &::google::protobuf::_Duration_default_instance_);
}
inline ::google::protobuf::Duration* InitialLoadBalanceResponse::release_client_stats_report_interval() {
  // @@protoc_insertion_point(field_release:grpc.lb.v1.InitialLoadBalanceResponse.client_stats_report_interval)
  
  ::google::protobuf::Duration* temp = client_stats_report_interval_;
  client_stats_report_interval_ = NULL;
  return temp;
}
inline ::google::protobuf::Duration* InitialLoadBalanceResponse::mutable_client_stats_report_interval() {
  
  if (client_stats_report_interval_ == NULL) {
    client_stats_report_interval_ = new ::google::protobuf::Duration;
  }
  // @@protoc_insertion_point(field_mutable:grpc.lb.v1.InitialLoadBalanceResponse.client_stats_report_interval)
  return client_stats_report_interval_;
}
inline void InitialLoadBalanceResponse::set_allocated_client_stats_report_interval(::google::protobuf::Duration* client_stats_report_interval) {
  ::google::protobuf::Arena* message_arena = GetArenaNoVirtual();
  if (message_arena == NULL) {
    delete reinterpret_cast< ::google::protobuf::MessageLite*>(client_stats_report_interval_);
  }
  if (client_stats_report_interval) {
    ::google::protobuf::Arena* submessage_arena =
      reinterpret_cast< ::google::protobuf::MessageLite*>(client_stats_report_interval)->GetArena();
    if (message_arena != submessage_arena) {
      client_stats_report_interval = ::google::protobuf::internal::GetOwnedMessage(
          message_arena, client_stats_report_interval, submessage_arena);
    }
    
  } else {
    
  }
  client_stats_report_interval_ = client_stats_report_interval;
  // @@protoc_insertion_point(field_set_allocated:grpc.lb.v1.InitialLoadBalanceResponse.client_stats_report_interval)
}

// -------------------------------------------------------------------

// ServerList

// repeated .grpc.lb.v1.Server servers = 1;
inline int ServerList::servers_size() const {
  return servers_.size();
}
inline void ServerList::clear_servers() {
  servers_.Clear();
}
inline const ::grpc::lb::v1::Server& ServerList::servers(int index) const {
  // @@protoc_insertion_point(field_get:grpc.lb.v1.ServerList.servers)
  return servers_.Get(index);
}
inline ::grpc::lb::v1::Server* ServerList::mutable_servers(int index) {
  // @@protoc_insertion_point(field_mutable:grpc.lb.v1.ServerList.servers)
  return servers_.Mutable(index);
}
inline ::grpc::lb::v1::Server* ServerList::add_servers() {
  // @@protoc_insertion_point(field_add:grpc.lb.v1.ServerList.servers)
  return servers_.Add();
}
inline ::google::protobuf::RepeatedPtrField< ::grpc::lb::v1::Server >*
ServerList::mutable_servers() {
  // @@protoc_insertion_point(field_mutable_list:grpc.lb.v1.ServerList.servers)
  return &servers_;
}
inline const ::google::protobuf::RepeatedPtrField< ::grpc::lb::v1::Server >&
ServerList::servers() const {
  // @@protoc_insertion_point(field_list:grpc.lb.v1.ServerList.servers)
  return servers_;
}

// -------------------------------------------------------------------

// Server

// bytes ip_address = 1;
inline void Server::clear_ip_address() {
  ip_address_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Server::ip_address() const {
  // @@protoc_insertion_point(field_get:grpc.lb.v1.Server.ip_address)
  return ip_address_.GetNoArena();
}
inline void Server::set_ip_address(const ::std::string& value) {
  
  ip_address_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.lb.v1.Server.ip_address)
}
#if LANG_CXX11
inline void Server::set_ip_address(::std::string&& value) {
  
  ip_address_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.lb.v1.Server.ip_address)
}
#endif
inline void Server::set_ip_address(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  ip_address_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.lb.v1.Server.ip_address)
}
inline void Server::set_ip_address(const void* value, size_t size) {
  
  ip_address_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.lb.v1.Server.ip_address)
}
inline ::std::string* Server::mutable_ip_address() {
  
  // @@protoc_insertion_point(field_mutable:grpc.lb.v1.Server.ip_address)
  return ip_address_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Server::release_ip_address() {
  // @@protoc_insertion_point(field_release:grpc.lb.v1.Server.ip_address)
  
  return ip_address_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Server::set_allocated_ip_address(::std::string* ip_address) {
  if (ip_address != NULL) {
    
  } else {
    
  }
  ip_address_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ip_address);
  // @@protoc_insertion_point(field_set_allocated:grpc.lb.v1.Server.ip_address)
}

// int32 port = 2;
inline void Server::clear_port() {
  port_ = 0;
}
inline ::google::protobuf::int32 Server::port() const {
  // @@protoc_insertion_point(field_get:grpc.lb.v1.Server.port)
  return port_;
}
inline void Server::set_port(::google::protobuf::int32 value) {
  
  port_ = value;
  // @@protoc_insertion_point(field_set:grpc.lb.v1.Server.port)
}

// string load_balance_token = 3;
inline void Server::clear_load_balance_token() {
  load_balance_token_.ClearToEmptyNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline const ::std::string& Server::load_balance_token() const {
  // @@protoc_insertion_point(field_get:grpc.lb.v1.Server.load_balance_token)
  return load_balance_token_.GetNoArena();
}
inline void Server::set_load_balance_token(const ::std::string& value) {
  
  load_balance_token_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), value);
  // @@protoc_insertion_point(field_set:grpc.lb.v1.Server.load_balance_token)
}
#if LANG_CXX11
inline void Server::set_load_balance_token(::std::string&& value) {
  
  load_balance_token_.SetNoArena(
    &::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::move(value));
  // @@protoc_insertion_point(field_set_rvalue:grpc.lb.v1.Server.load_balance_token)
}
#endif
inline void Server::set_load_balance_token(const char* value) {
  GOOGLE_DCHECK(value != NULL);
  
  load_balance_token_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), ::std::string(value));
  // @@protoc_insertion_point(field_set_char:grpc.lb.v1.Server.load_balance_token)
}
inline void Server::set_load_balance_token(const char* value, size_t size) {
  
  load_balance_token_.SetNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(),
      ::std::string(reinterpret_cast<const char*>(value), size));
  // @@protoc_insertion_point(field_set_pointer:grpc.lb.v1.Server.load_balance_token)
}
inline ::std::string* Server::mutable_load_balance_token() {
  
  // @@protoc_insertion_point(field_mutable:grpc.lb.v1.Server.load_balance_token)
  return load_balance_token_.MutableNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline ::std::string* Server::release_load_balance_token() {
  // @@protoc_insertion_point(field_release:grpc.lb.v1.Server.load_balance_token)
  
  return load_balance_token_.ReleaseNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited());
}
inline void Server::set_allocated_load_balance_token(::std::string* load_balance_token) {
  if (load_balance_token != NULL) {
    
  } else {
    
  }
  load_balance_token_.SetAllocatedNoArena(&::google::protobuf::internal::GetEmptyStringAlreadyInited(), load_balance_token);
  // @@protoc_insertion_point(field_set_allocated:grpc.lb.v1.Server.load_balance_token)
}

// bool drop = 4;
inline void Server::clear_drop() {
  drop_ = false;
}
inline bool Server::drop() const {
  // @@protoc_insertion_point(field_get:grpc.lb.v1.Server.drop)
  return drop_;
}
inline void Server::set_drop(bool value) {
  
  drop_ = value;
  // @@protoc_insertion_point(field_set:grpc.lb.v1.Server.drop)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace v1
}  // namespace lb
}  // namespace grpc

// @@protoc_insertion_point(global_scope)

#endif  // PROTOBUF_src_2fproto_2fgrpc_2flb_2fv1_2fload_5fbalancer_2eproto__INCLUDED
