// Copyright 2015 The gRPC Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// This file defines the GRPCLB LoadBalancing protocol.
//
// The canonical version of this proto can be found at
// https://github.com/grpc/grpc-proto/blob/master/grpc/lb/v1/load_balancer.proto
syntax = "proto3";

package grpc.lb.v1;

import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";

option go_package = "google.golang.org/grpc/balancer/grpclb/grpc_lb_v1";
option java_multiple_files = true;
option java_outer_classname = "LoadBalancerProto";
option java_package = "io.grpc.grpclb";

service LoadBalancer {
  // Bidirectional rpc to get a list of servers.
  rpc BalanceLoad(stream LoadBalanceRequest) returns (stream LoadBalanceResponse);
}

message LoadBalanceRequest {
  oneof load_balance_request_type {
    // This message should be sent on the first request to the load balancer.
    InitialLoadBalanceRequest initial_request = 1;

    // The client stats should be periodically reported to the load balancer
    // based on the duration defined in the InitialLoadBalanceResponse.
    ClientStats client_stats = 2;
  }
}

message InitialLoadBalanceRequest {
  // The name of the load balanced service (e.g., service.googleapis.com). Its
  // length should be less than 256 bytes.
  // The name might include a port number. How to handle the port number is up
  // to the balancer.
  string name = 1;
}

// Contains the number of calls finished for a particular load balance token.
message ClientStatsPerToken {
  // See Server.load_balance_token.
  string load_balance_token = 1;

  // The total number of RPCs that finished associated with the token.
  int64 num_calls = 2;
}

// Contains client level statistics that are useful to load balancing. Each
// count except the timestamp should be reset to zero after reporting the stats.
message ClientStats {
  // The timestamp of generating the report.
  google.protobuf.Timestamp timestamp = 1;

  // The total number of RPCs that started.
  int64 num_calls_started = 2;

  // The total number of RPCs that finished.
  int64 num_calls_finished = 3;

  // The total number of RPCs that failed to reach a server except dropped RPCs.
  int64 num_calls_finished_with_client_failed_to_send = 6;

  // The total number of RPCs that finished and are known to have been received
  // by a server.
  int64 num_calls_finished_known_received = 7;

  // The list of dropped calls.
  repeated ClientStatsPerToken calls_finished_with_drop = 8;

  reserved 4, 5;
}

message LoadBalanceResponse {
  oneof load_balance_response_type {
    // This message should be sent on the first response to the client.
    InitialLoadBalanceResponse initial_response = 1;

    // Contains the list of servers selected by the load balancer. The client
    // should send requests to these servers in the specified order.
    ServerList server_list = 2;
  }
}

message InitialLoadBalanceResponse {
  // This is an application layer redirect that indicates the client should use
  // the specified server for load balancing. When this field is non-empty in
  // the response, the client should open a separate connection to the
  // load_balancer_delegate and call the BalanceLoad method. Its length should
  // be less than 64 bytes.
  string load_balancer_delegate = 1;

  // This interval defines how often the client should send the client stats
  // to the load balancer. Stats should only be reported when the duration is
  // positive.
  google.protobuf.Duration client_stats_report_interval = 2;
}

message ServerList {
  // Contains a list of servers selected by the load balancer. The list will
  // be updated when server resolutions change or as needed to balance load
  // across more servers. The client should consume the server list in order
  // unless instructed otherwise via the client_config.
  repeated Server servers = 1;

  // Was google.protobuf.Duration expiration_interval.
  reserved 3;
}

// Contains server information. When the drop field is not true, use the other
// fields.
message Server {
  // A resolved address for the server, serialized in network-byte-order. It may
  // either be an IPv4 or IPv6 address.
  bytes ip_address = 1;

  // A resolved port number for the server.
  int32 port = 2;

  // An opaque but printable token for load reporting. The client must include
  // the token of the picked server into the initial metadata when it starts a
  // call to that server. The token is used by the server to verify the request
  // and to allow the server to report load to the gRPC LB system. The token is
  // also used in client stats for reporting dropped calls.
  //
  // Its length can be variable but must be less than 50 bytes.
  string load_balance_token = 3;

  // Indicates whether this particular request should be dropped by the client.
  // If the request is dropped, there will be a corresponding entry in
  // ClientStats.calls_finished_with_drop.
  bool drop = 4;

  reserved 5;
}
