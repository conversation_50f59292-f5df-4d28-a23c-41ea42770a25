// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: src/proto/grpc/lb/v1/load_balancer.proto

#include "src/proto/grpc/lb/v1/load_balancer.pb.h"
#include "src/proto/grpc/lb/v1/load_balancer.grpc.pb.h"

#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/sync_stream.h>
#include <gmock/gmock.h>
namespace grpc {
namespace lb {
namespace v1 {

class MockLoadBalancerStub : public LoadBalancer::StubInterface {
 public:
  MOCK_METHOD1(BalanceLoadRaw, ::grpc::ClientReaderWriterInterface< ::grpc::lb::v1::LoadBalanceRequest, ::grpc::lb::v1::LoadBalanceResponse>*(::grpc::ClientContext* context));
  MOCK_METHOD3(AsyncBalanceLoadRaw, ::grpc::ClientAsyncReaderWriterInterface<::grpc::lb::v1::LoadBalanceRequest, ::grpc::lb::v1::LoadBalanceResponse>*(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq, void* tag));
  MOCK_METHOD2(PrepareAsyncBalanceLoadRaw, ::grpc::ClientAsyncReaderWriterInterface<::grpc::lb::v1::LoadBalanceRequest, ::grpc::lb::v1::LoadBalanceResponse>*(::grpc::ClientContext* context, ::grpc::CompletionQueue* cq));
};

} // namespace grpc
} // namespace lb
} // namespace v1

