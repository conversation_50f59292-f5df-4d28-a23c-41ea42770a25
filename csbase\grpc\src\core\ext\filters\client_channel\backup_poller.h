/*
 *
 * Copyright 2017 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

#ifndef GRPC_CORE_EXT_FILTERS_CLIENT_CHANNEL_BACKUP_POLLER_H
#define GRPC_CORE_EXT_FILTERS_CLIENT_CHANNEL_BACKUP_POLLER_H

#include <grpc/support/port_platform.h>

#include <grpc/grpc.h>
#include "src/core/lib/channel/channel_stack.h"

/* Start polling \a interested_parties periodically in the timer thread  */
void grpc_client_channel_start_backup_polling(
    grpc_pollset_set* interested_parties);

/* Stop polling \a interested_parties */
void grpc_client_channel_stop_backup_polling(
    grpc_pollset_set* interested_parties);

#endif /* GRPC_CORE_EXT_FILTERS_CLIENT_CHANNEL_BACKUP_POLLER_H */
