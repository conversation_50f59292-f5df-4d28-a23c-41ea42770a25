/* Automatically generated nanopb header */
/* Generated by nanopb-0.3.7-dev */

#ifndef PB_GOOGLE_PROTOBUF_DURATION_PB_H_INCLUDED
#define PB_GOOGLE_PROTOBUF_DURATION_PB_H_INCLUDED
#include "pb.h"
/* @@protoc_insertion_point(includes) */
#if PB_PROTO_HEADER_VERSION != 30
#error Regenerate this file with the current version of nanopb generator.
#endif

#ifdef __cplusplus
extern "C" {
#endif

/* Struct definitions */
typedef struct _google_protobuf_Duration {
    bool has_seconds;
    int64_t seconds;
    bool has_nanos;
    int32_t nanos;
/* @@protoc_insertion_point(struct:google_protobuf_Duration) */
} google_protobuf_Duration;

/* Default values for struct fields */

/* Initializer values for message structs */
#define google_protobuf_Duration_init_default    {false, 0, false, 0}
#define google_protobuf_Duration_init_zero       {false, 0, false, 0}

/* Field tags (for use in manual encoding/decoding) */
#define google_protobuf_Duration_seconds_tag     1
#define google_protobuf_Duration_nanos_tag       2

/* Struct field encoding specification for nanopb */
extern const pb_field_t google_protobuf_Duration_fields[3];

/* Maximum encoded size of messages (where known) */
#define google_protobuf_Duration_size            22

/* Message IDs (where set with "msgid" option) */
#ifdef PB_MSGID

#define DURATION_MESSAGES \


#endif

#ifdef __cplusplus
} /* extern "C" */
#endif
/* @@protoc_insertion_point(eof) */

#endif
