/*
 *
 * Copyright 2018 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

#ifndef GRPC_CORE_LIB_IOMGR_TIMER_CUSTOM_H
#define GRPC_CORE_LIB_IOMGR_TIMER_CUSTOM_H

#include <grpc/support/port_platform.h>

#include "src/core/lib/iomgr/timer.h"

typedef struct grpc_custom_timer {
  // Implementation defined
  void* timer;
  uint64_t timeout_ms;

  grpc_timer* original;
} grpc_custom_timer;

typedef struct grpc_custom_timer_vtable {
  void (*start)(grpc_custom_timer* t);
  void (*stop)(grpc_custom_timer* t);
} grpc_custom_timer_vtable;

void grpc_custom_timer_init(grpc_custom_timer_vtable* impl);

void grpc_custom_timer_callback(grpc_custom_timer* t, grpc_error* error);

#endif /* GRPC_CORE_LIB_IOMGR_TIMER_CUSTOM_H */
