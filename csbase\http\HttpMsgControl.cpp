#include "http/HttpMsgControl.h"
#include "json/json.h"

#define RESULT                   "result"
#define MESSAGE                  "message"
#define DATAS                    "datas"

using namespace Akcs;

static struct http_state_table HTTPSTATE_CHART[] = {
	{HTTP_CODE_SUC, "success"}
};

const char *httpRespState2str(int state)
{
    int x;
    const char *str = "UNKNOWN";
    int len = sizeof(HTTPSTATE_CHART) / sizeof(struct http_state_table);
	for (x = 0; x < len - 1; x++) {
		if (HTTPSTATE_CHART[x].state == state) {
			str = HTTPSTATE_CHART[x].message;
			break;
		}
	}

	return str;
}


std::string buildCommHttpMsg(int code,  const HttpRespKV& kv)
{
    Json::Value item;
    Json::FastWriter w;
    Json::Value itemData;
    Json::FastWriter wData;

    item[RESULT] = code;
    item[MESSAGE] = httpRespState2str(code);
    for (const auto& tmpkv : kv)
    {
        itemData[tmpkv.first] = tmpkv.second;
    }
    item[DATAS] = itemData;

    std::string msg_json = w.write(item);

    return msg_json;
}

std::string buildErrorHttpMsg(int code, const std::string &msg)
{
    Json::Value item;
    Json::FastWriter w;
    Json::Value itemData;
    Json::FastWriter wData;

    item[RESULT] = code;
    item[MESSAGE] = msg;

    std::string msg_json = w.write(item);

    return msg_json;
}


