#ifndef __HTTP_MSG_CONTORL_H__
#define __HTTP_MSG_CONTORL_H__
#include <map>
#include <string>



typedef std::map<std::string/*key*/, std::string/*value*/> HttpRespKV;
typedef std::map<std::string/*key*/, int/*value*/> HttpRespIntKV;


#define HTTP_CODE_ERR_TOKEN_INVALID -1
enum HTTP_CODE
{
    HTTP_CODE_SUC = 0,
};


struct http_state_table {
    int state;
    const char *message;
};

std::string buildCommHttpMsg(int code, const HttpRespKV& kv);
std::string buildErrorHttpMsg(int code, const std::string &msg);

#endif //__HTTP_MSG_CONTORL_H__
