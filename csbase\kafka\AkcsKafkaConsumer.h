#ifndef CSBASE_KAFKA_CONSUMER_H_
#define CSBASE_KAFKA_CONSUMER_H_

#include<thread>
#include "cppkafka/consumer.h"
#include "cppkafka/configuration.h"
#include "cppkafka/topic_partition_list.h"
#include "cppkafka/topic.h"




typedef std::function<bool(uint64_t partition, uint64_t offset, const std::string& key, const std::string& msg)> KafkaConsumerFunc;

typedef std::function<bool(const std::vector<cppkafka::Message>&, uint64_t)> KafkaBatchConsumerFunc;

class AkcsKafkaConsumer
{
public:
    enum COMMIT_TYPE
    {
        CALLBACK_OK_COMMIT,
        READ_OK_COMMIT_,
    };
    AkcsKafkaConsumer();

    void SetParma(const std::string &ip, const std::string& topic, const std::string& comsumer_group, int thread_num)
    {
        ip_ = ip;
        topic_ = topic;
        comsumer_group_ = comsumer_group;
        thread_num_ = thread_num;
    }
    
    void SetConsumerCb(const KafkaConsumerFunc &cb)
    {
        cb_ = cb;
    }
    
    void SetBatchConsumerCb(const KafkaBatchConsumerFunc &cb)
    {
        batch_cb_ = cb;
    }

    void setBatchReadeNumber(uint64_t value) {
        comsumer_batch_num_ = value;
    }

    uint64_t getBatchReadeNumber() const {
        return comsumer_batch_num_;
    }


    
    //回调的处理函数返回成功后提交
    void SetHandleOkCommit()
    {
        commit_type_ = CALLBACK_OK_COMMIT;
    }
    
    void Start();
    void StartConsumerBatch();    
    bool Status() {return status_;}
    
public:

    static thread_local uint64_t comsumer_batch_num_;//每个线程有自己的    
    
private:
    void StartInnerConsumerBatch();
    void StartInner();
    //kafka信息
    std::string ip_;
    std::string topic_;
    std::string comsumer_group_; 

    int thread_num_;
    bool status_ ;
    KafkaConsumerFunc cb_;
    KafkaBatchConsumerFunc batch_cb_;
    COMMIT_TYPE commit_type_ = READ_OK_COMMIT_;
};






#endif
