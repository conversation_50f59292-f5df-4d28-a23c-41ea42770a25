#include "AkcsKafkaProducer.h"
#include <boost/functional/hash.hpp>
#include <boost/format.hpp>
#include <boost/crc.hpp>

AkcsKafkaProducer::AkcsKafkaProducer(const std::string& topic, const std::string& kafka_broker_ip_list, int ack_mode)
{
    Init(topic, kafka_broker_ip_list, ack_mode);
}

void AkcsKafkaProducer::Init(const std::string& topic, const std::string& broker_ip_list, int ack_mod)
{
    topic_ = topic;
    broker_ip_list_ = broker_ip_list;
    ack_mode_ = ack_mod;    
    builder_ = std::make_shared<MessageBuilder>(topic_);
    Configuration config =
    {
        {"metadata.broker.list", broker_ip_list_ },
        {"request.required.acks", std::to_string(ack_mode_)}
    };
    producer_ = std::make_shared<BufferedProducer<std::string>>(config);
}


void AkcsKafkaProducer::ProduceMsg(const std::string& key, const std::string& value)
{      
    builder_->payload(value);
    builder_->key(key);
    producer_->produce(*builder_);
    producer_->async_flush();
    std::chrono::milliseconds mscond(1000 * 15);
    if(ack_mode_ != 0 && !producer_->wait_for_acks(mscond)) //需等待kafka回复的模式
    {
        std::string worker_node = "AKCS";
        std::stringstream alarm_msg;
        alarm_msg << "Produce kafka message wait akcs timeout! key=" << key;
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, alarm_msg.str(), AKCS_MONITOR_ALARM_PRODUCE_KAFKA_EMAIL);
        AK_LOG_FATAL << "Produce kafka message wait akcs timeout! key=" << key;
    }
}

void AkcsKafkaProducer::ProduceMsgWithLock(const std::string& key, const std::string& value)
{
    std::unique_lock<std::mutex> lock(producer_mutex_);
    ProduceMsg(key, value);
}


