#ifndef AKCS_KAFAK_PRODUCER_
#define AKCS_KAFAK_PRODUCER_

#include <stdio.h>
#include <stdlib.h>
#include <string>
#include <boost/algorithm/string.hpp>
#include <stdexcept>
#include <iostream>
#include "cppkafka/utils/buffered_producer.h"
#include "cppkafka/configuration.h"
#include "AkLogging.h"
#include "AkcsMonitor.h"


using cppkafka::BufferedProducer;
using cppkafka::Configuration;
using cppkafka::Topic;
using cppkafka::MessageBuilder;
using cppkafka::Message;

class AkcsKafkaProducer
{

public:
    AkcsKafkaProducer(const std::string& topic, const std::string& broker_ip_list, int ack_mod = 0);
    AkcsKafkaProducer() {};
    //构造时没初始化时需要调Init函数初始化
    void Init(const std::string& topic, const std::string& broker_ip_list, int ack_mod = 0);
    ~AkcsKafkaProducer() {};

    void ProduceMsg(const std::string& key, const std::string& value);
    void ProduceMsgWithLock(const std::string& key, const std::string& value);
        
private:
    std::string topic_;
    std::mutex  producer_mutex_;
    std::string broker_ip_list_;
    int ack_mode_; //0代表最多一次，不等待kafka返回结果
    std::shared_ptr<BufferedProducer<std::string>> producer_;
    std::shared_ptr<MessageBuilder> builder_;
};

#endif
