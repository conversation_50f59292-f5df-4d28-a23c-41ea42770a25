#include "KafkaConsumerTopicHandleBase.h"
#include "AkLogging.h"

HandleKafkaTopicMsg::HandleKafkaTopicMsg()
{

}

void HandleKafkaTopicMsg::StartKafkaConsumer(const std::string& kafka_broker_ip, const std::string& kafka_topic, const std::string& kafka_topic_group, int thread)
{
    kafak_.SetParma(kafka_broker_ip, kafka_topic,
        kafka_topic_group, thread);
    kafak_.SetBatchConsumerCb(
         std::bind(&HandleKafkaTopicMsg::BatchKafkaMessage, this, 
                   std::placeholders::_1, std::placeholders::_2)
    );
    kafak_.StartConsumerBatch();    
}


