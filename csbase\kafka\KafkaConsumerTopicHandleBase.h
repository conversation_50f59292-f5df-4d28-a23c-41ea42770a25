#ifndef __KAFKA_CONSUMER_HANDLE_BASE_H__
#define __KAFKA_CONSUMER_HANDLE_BASE_H__

#include <vector>
#include <map>
#include <set>
#include <unordered_set>
#include <memory>
#include <functional>
#include "AkcsKafkaConsumer.h"

class HandleKafkaTopicMsg
{
public:
    HandleKafkaTopicMsg();

    void StartKafkaConsumer(const std::string& kafka_broker_ip, const std::string& kafka_topic, const std::string& kafka_topic_group, int thread);
    virtual bool BatchKafkaMessage(const std::vector<cppkafka::Message> &message, uint64_t unread) = 0;
    bool Status()
    {
        return kafak_.Status();
    }

protected:
    AkcsKafkaConsumer kafak_;
};


#endif 