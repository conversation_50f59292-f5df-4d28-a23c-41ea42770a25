#include "AkLogging.h"
#include "KafkaParseWebMsg.h"
#include "ThreadLocalSingleton.h"

KafkaWebMsgParse::KafkaWebMsgParse(const std::string& msg) :status_(FAIL)
{
    Parse(msg);
}

int KafkaWebMsgParse::Parse(const std::string& msg)
{
    Json::Reader reader;
    Json::Value root;
    Json::Value data;

    AK_LOG_INFO << "KafkaWebMsgParse msg = " << msg;
    if (!reader.parse(msg, root))
    {
        AK_LOG_WARN << "Parse json error.data=" << msg << " error msg=" << reader.getFormatedErrorMessages();
        return -1;
    }

    if (!root.isMember("msg_type"))
    {
        AK_LOG_WARN << "Parse json error. no have section msg_type. data=" << msg;
        return -1;
    }
    msg_type_ = root["msg_type"].asString();
    if (msg_type_ == "data_analysis")
    {
        status_ = OK;
        return 0;
    }

    if (!root.isMember("trace_id"))
    {
        AK_LOG_WARN << "Parse json error. no have section trace_id. data=" << msg;
        return -1;
    }
    trace_id_ = root["trace_id"].asString();

    if (!root.isMember("timestamp"))
    {
        AK_LOG_WARN << "Parse json error. no have section timestamp. data=" << msg;
        return -1;
    }
    timestamp_us_ = root["timestamp"].asDulLong();

    if (!root.isMember("data"))
    {
        AK_LOG_WARN << "Parse json error. no have section data. data=" << msg;
        return -1;
    }
    data = root["data"];

    Json::FastWriter fastWriter;
    Json::Value::Members members = data.getMemberNames();
    for (auto const& key : members)
    {
        if (data[key].isObject() || data[key].isArray())
        {
            //如果是对象或者数组，转换成json字符串
            kv_.emplace(key, fastWriter.write(data[key]));
        }
        else if (data[key].isString())
        {
            kv_.emplace(key, data[key].asString());
        }
        else if (data[key].isInt())
        {
            kv_.emplace(key, std::to_string(data[key].asInt()));
        }
    }

    status_ = OK;
    return 0;
}


//检查key是否都存在
bool KafkaWebMsgParse::CheckKeysExist(const KakfaMsgKV& kv, const std::vector<std::string>& keys)
{
    std::vector<std::string> missing_keys;

    for (const auto& key : keys)
    {
        if (kv.find(key) == kv.end())
        {
            missing_keys.push_back(key);
            AK_LOG_WARN << "CheckKeysExist miss key: " << key;
        }
    }

    return missing_keys.empty();
}

bool KafkaWebMsgParse::CheckKeyExist(const KakfaMsgKV& kv, const std::string& key)
{
    return kv.find(key) != kv.end();
}
