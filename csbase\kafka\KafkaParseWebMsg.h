#ifndef KAFKA_CONSUMER_WEB_MSG_PARSE_H_
#define KAFKA_CONSUMER_WEB_MSG_PARSE_H_

#include <map> 
#include "json/json.h"


typedef std::map<std::string, std::string> KakfaMsgKV;

class KafkaWebMsgParse
{
public:
    enum ParseMsgStatus
    {
        FAIL = 0,
        OK = 1,
    };

    KafkaWebMsgParse(const std::string& msg);
    bool ParseOk()
    {
        return status_ == OK;
    }
    int Parse(const std::string& msg);
    static bool CheckKeysExist(const KakfaMsgKV &kv, const std::vector<std::string> &keys);
    static bool CheckKeyExist(const KakfaMsgKV &kv, const std::string &key);
    
    std::string msg_type_;
    std::string trace_id_;
    uint64_t timestamp_us_;
    KakfaMsgKV kv_;
    ParseMsgStatus status_;
};




#endif

