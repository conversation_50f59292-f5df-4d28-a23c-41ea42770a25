#ifndef __KAFKA_TRANSACTION_DEFINE_H__
#define __KAFKA_TRANSACTION_DEFINE_H__


#define CSPDUKAFKA_MQ_CONF_FILEPATH        "/usr/local/akcs/cspdu2kafkamq/conf/cspdu2kafkamq.conf"
#define CSPDUKAFKA_MQ_PID "/var/run/cspdu2kafkamq.pid" 

#define CSMSIP_CONF_FILEPATH        "/usr/local/akcs/csmsip/conf/csmsip.conf"
#define CSMSIP_PIDFILE "/var/run/csmsip.pid" 



#define CSADAPT_CONF_COMMON_LEN 64

typedef struct KAFKA_CONSUMER_CONF_T
{
    /* DB配置项 */
	char db_ip[CSADAPT_CONF_COMMON_LEN];    
	char db1_ip[CSADAPT_CONF_COMMON_LEN];
	char db_user<PERSON><PERSON><PERSON>[CSADAPT_CONF_COMMON_LEN];
	char db_password[CSADAPT_CONF_COMMON_LEN];
	char db_database[CSADAPT_CONF_COMMON_LEN];
    int  db_port;

    char kafka_broker_ip[CSADAPT_CONF_COMMON_LEN];
	char kafka_consumer_topic_name[CSADAPT_CONF_COMMON_LEN];
	char kafka_consumer_group[CSADAPT_CONF_COMMON_LEN];

	int enable_unread_special_offset;
	//,123,3455, 注意前后逗号
	char unread_special_offset_list[512];
    int db_num;
	
}KAFKA_CONSUMER_CONF;

typedef struct KAFKA_PRODUCER_CONF_T
{
    /* DB配置项 */
	char db_ip[CSADAPT_CONF_COMMON_LEN];
	char db_usernmae[CSADAPT_CONF_COMMON_LEN];
	char db_password[CSADAPT_CONF_COMMON_LEN];
	char db_database[CSADAPT_CONF_COMMON_LEN];
    int  db_port;

    char kafka_broker_ip[CSADAPT_CONF_COMMON_LEN];
	char kafka_consumer_topic_name[CSADAPT_CONF_COMMON_LEN];
	char kafka_consumer_group[CSADAPT_CONF_COMMON_LEN];
    
    char etcd_server[128];
}KAFKA_PRODUCER_CONF;



typedef struct KAFKA_CONSUMER_MSG_T
{
    uint64_t id;
    int message_status;
    char sip[16];
    char message[1024];    
}KAFKA_CONSUMER_MSG;

enum CONSUMER_MSG_TYPE
{
    CONSUMER_MSG_TYPE_INSERT,
    CONSUMER_MSG_TYPE_UPDATE_GROUP,
    CONSUMER_MSG_TYPE_DELETE_SIP,
    CONSUMER_MSG_TYPE_UPDATE_SIPENABLE,
};

enum CONSUMER_HANDLE_MSG_TYPE
{
    CONSUMER_HANDLE_MSG_TYPE_UNHANDLE,//未处理
    CONSUMER_HANDLE_MSG_TYPE_OK,//成功处理
    CONSUMER_HANDLE_MSG_TYPE_ERROR,//处理时候发现消息错误
};


typedef struct KAFKA_CONSUMER_MSG_DETAIL_T
{
    int message_type;//CONSUMER_MSG_TYPE
    char username[16];
    char pwd[64];
    char group[16];
    int groupring;
    char devicenode[64];
    int type;
    int communityid;
    int community_type;
    int device_attribute;
    int sip_enable;
}KAFKA_CONSUMER_MSG_DETAIL;





#endif// __KAFKA_CONSUMER_DEFINE_H__

