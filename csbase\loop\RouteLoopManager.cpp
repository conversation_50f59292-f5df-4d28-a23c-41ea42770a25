#include "RouteLoopManager.h"

RouteLoopManager* RouteLoopManager::instance_ = nullptr;
std::mutex RouteLoopManager::mutex_;

RouteLoopManager::RouteLoopManager() 
    : route_loop_(std::make_shared<evpp::EventLoop>())
    , is_running_(false)
{
}

RouteLoopManager::~RouteLoopManager()
{
    StopLoop();
}

RouteLoopManager* RouteLoopManager::GetInstance()
{
    std::lock_guard<std::mutex> lock(mutex_);
    if (instance_ == nullptr)
    {
        instance_ = new RouteLoopManager();
    }
    return instance_;
}

RouteLoopManager* GetRouteLoopManagerInstance()
{
    return RouteLoopManager::GetInstance();
}

evpp::EventLoop* RouteLoopManager::GetRouteLoop()
{
    return route_loop_.get();
}

void RouteLoopManager::StartLoop()
{
    std::lock_guard<std::mutex> lock(loop_mutex_);
    if (!is_running_ && route_loop_)
    {
        is_running_ = true;
        loop_thread_.reset(new std::thread(&RouteLoopManager::RunLoop, this));
    }
}

void RouteLoopManager::StopLoop()
{
    std::lock_guard<std::mutex> lock(loop_mutex_);
    if (is_running_ && route_loop_)
    {
        route_loop_->Stop();
        is_running_ = false;
        
        if (loop_thread_ && loop_thread_->joinable())
        {
            loop_thread_->join();
        }
        loop_thread_.reset();
    }
}

void RouteLoopManager::RunLoop()
{
    if (route_loop_)
    {
        route_loop_->Run();
    }
}