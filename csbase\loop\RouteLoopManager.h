#pragma once

#include <memory>
#include <thread>
#include <mutex>
#include "evpp/event_loop.h"

class RouteLoopManager
{
public:
    static RouteLoopManager* GetInstance();
    
    // 获取route_loop
    evpp::EventLoop* GetRouteLoop();
    
    // 启动loop线程
    void StartLoop();
    
    // 停止loop线程
    void StopLoop();
    
    ~RouteLoopManager();

private:
    // 私有构造函数
    RouteLoopManager();
    
    // 线程运行函数
    void RunLoop();

private:
    static RouteLoopManager* instance_;
    static std::mutex mutex_;
    
    std::shared_ptr<evpp::EventLoop> route_loop_;
    std::unique_ptr<std::thread> loop_thread_;
    bool is_running_;
    std::mutex loop_mutex_;
};

RouteLoopManager* GetRouteLoopManagerInstance();