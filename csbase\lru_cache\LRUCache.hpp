#ifndef _AKCS_BASE_LRU_CACHE_HPP_
#define _AKCS_BASE_LRU_CACHE_HPP_

#include <list>
#include <unordered_map>
#include <string>
#include <functional> 

#include <list>
#include <unordered_map>
#include <mutex>

namespace akcs_base_lru_cache
{
    
// key value类型
template <typename KeyType, typename ValueType>
class LRUCache {
public:
    LRUCache() : cache_list_size_(0) {}
    
    void InitSize(size_t size) 
    {
        cache_list_size_ = size;
        return;
    }

    bool CacheCheckAndAdd(const KeyType& key, const ValueType& value);
    bool GetCacheValue(const KeyType& key, ValueType& value);
    void RemoveCache(const KeyType& key);
    int GetCacheSize();
    std::string GetCacheList();

private:
    size_t cache_list_size_;
    std::list<std::pair<KeyType, ValueType>> cache_list_;
    std::unordered_map<KeyType, typename std::list<std::pair<KeyType, ValueType>>::iterator> cache_map_;
    std::mutex mutex_;
};

template <typename KeyType, typename ValueType>
bool LRUCache<KeyType, ValueType>::CacheCheckAndAdd(const KeyType& key, const ValueType& value)
{
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = cache_map_.find(key);
    if (it != cache_map_.end())
    {
        // Key存在则移动到列表头部
        cache_list_.splice(cache_list_.begin(), cache_list_, it->second);
        return false;
    }

    // 如果缓存已满，删除最旧的 key
    if (cache_list_.size() >= cache_list_size_) 
    {
        // 找到末尾元素并删除
        auto last = cache_list_.end();
        last--;
        cache_map_.erase(last->first);
        cache_list_.pop_back();
    }

    // key不存在 添加到缓存中
    cache_list_.emplace_front(key, value);
    cache_map_[key] = cache_list_.begin();

    return true;
}

template <typename KeyType, typename ValueType>
bool LRUCache<KeyType, ValueType>::GetCacheValue(const KeyType& key, ValueType& value) 
{
    auto it = cache_map_.find(key);
    if (it == cache_map_.end()) 
    {
        return false;
    }
    // Key存在，移动到列表头部并赋值
    cache_list_.splice(cache_list_.begin(), cache_list_, it->second);
    value = it->second->second;
    return true;
}

template <typename KeyType, typename ValueType>
void LRUCache<KeyType, ValueType>::RemoveCache(const KeyType& key) 
{
    std::lock_guard<std::mutex> lock(mutex_);
    auto it = cache_map_.find(key);
    if (it == cache_map_.end()) 
    {
        return;
    }
    cache_list_.erase(it->second);
    cache_map_.erase(it);
    return;
}

template <typename KeyType, typename ValueType>
int LRUCache<KeyType, ValueType>::GetCacheSize() 
{
    std::lock_guard<std::mutex> lock(mutex_);
    return cache_list_.size();
}

template <typename KeyType, typename ValueType>
std::string LRUCache<KeyType, ValueType>::GetCacheList()
{
    std::string list;
    std::lock_guard<std::mutex> lock(mutex_);
    for (const auto& pair : cache_list_)
    {
        list += pair.first + ";";
    }
    if (!list.empty())
    {
        list.pop_back(); // 移除最后一个分号
    }
    return list;
}

} // namespace akcs_base_lru_cache


#endif //_AKCS_BASE_LRU_CACHE_HPP_
