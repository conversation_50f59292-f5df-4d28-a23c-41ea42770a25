#ifndef _CSBASE_METRIC_LATENCY_SERVICE_H_
#define _CSBASE_METRIC_LATENCY_SERVICE_H_

#include <iostream>
#include <chrono>
#include <vector>
#include <atomic>
#include <stdexcept>
#include <sstream>
#include <unordered_map>
#include <mutex>
#include <memory>
// 1. 可配置的延时统计类
class MetricLatency {
private:
    std::vector<uint32_t> thresholds_; // ms
    std::unordered_map<std::string, std::vector<std::atomic<int>>> counters_; // 按消息 ID 统计
    std::string labels_; // 标签，显示指标的属性
    std::mutex mutex_; // 保护 counters_ 的互斥锁

public:
    MetricLatency(const std::vector<uint32_t> latency_ms, const std::string &labels)
        : thresholds_(latency_ms) {
        if (latency_ms.empty()) {
            throw std::invalid_argument("Thresholds cannot be empty");
        }
        labels_ = labels;
    }

    // 记录延时
    void Record(const std::string &msg_id, uint32_t latency_ms) {
        std::lock_guard<std::mutex> lock(mutex_); // 保护访问 counters_
        // 如果该消息 ID 不存在，初始化其计数器
        if (counters_.find(msg_id) == counters_.end()) {
            counters_[msg_id] = std::vector<std::atomic<int>>(thresholds_.size() + 1);
        }

        size_t index = 0;
        // 查找第一个超过当前延时的阈值
        while (index < thresholds_.size() && latency_ms > thresholds_[index]) {
            ++index;
        }
        // 原子递增对应计数器
        counters_[msg_id][index].fetch_add(1, std::memory_order_relaxed);
    }

    void Record(uint32_t latency_ms) {
        std::lock_guard<std::mutex> lock(mutex_); // 保护访问 counters_
        // 如果该消息 ID 不存在，初始化其计数器
        static std::string msg_id = "default";
        if (counters_.find(msg_id) == counters_.end()) {
            counters_[msg_id] = std::vector<std::atomic<int>>(thresholds_.size() + 1);
        }

        size_t index = 0;
        // 查找第一个超过当前延时的阈值
        while (index < thresholds_.size() && latency_ms > thresholds_[index]) {
            ++index;
        }
        // 原子递增对应计数器
        counters_[msg_id][index].fetch_add(1, std::memory_order_relaxed);
    }    

    std::string ToFormateString() {
        std::stringstream formated_string;
        std::lock_guard<std::mutex> lock(mutex_); // 保护访问 counters_

        for (const auto& entry : counters_) {
            const std::string& msg_id = entry.first;
            const auto& counter = entry.second;

            for (size_t i = 0; i < thresholds_.size() + 1; ++i) {
                uint64_t count = counter[i].load(std::memory_order_relaxed);
                if (count == 0) 
                {
                    continue;
                }
                formated_string << labels_ << "{msg_id=\"" << msg_id << "\", quantile=\"";
                if (i == 0) {
                    formated_string << "≤" << thresholds_[i] << "ms";
                } else if (i < thresholds_.size()) {
                    formated_string << thresholds_[i - 1] << "ms~" << thresholds_[i] << "ms";
                } else {
                    formated_string << ">" << thresholds_.back() << "ms";
                }
                formated_string << "\"} ";
                formated_string << count << "\n";
            }
        }
        return formated_string.str();
    }
};

typedef std::shared_ptr<MetricLatency> MetricLatencyPtr;

#endif
