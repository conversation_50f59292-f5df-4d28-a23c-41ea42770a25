#include <iostream>
#include "AkLogging.h"
#include "MetricService.h"

// 初始化静态成员变量
std::mutex MetricService::instance_mtx_;
MetricService* MetricService::instance_ = nullptr;

// 获取单例实例
MetricService* MetricService::GetInstance()
{
    std::lock_guard<std::mutex> lock(instance_mtx_);
    if (instance_ == nullptr)
    {
        instance_ = new MetricService();
    }
    return instance_;
}

// 添加一个 MetricNode 指标
void MetricService::AddMetric(const std::string& name, const std::string& desc, const std::string& lbls, std::function<long()> callback)
{
    std::lock_guard<std::mutex> lock(data_mtx_);
    metrics_map_.emplace(name, MetricNode(desc, lbls, callback));
}

// 添加一个 Lantency 指标
void MetricService::AddLatencyMetric(const std::string& name, 
    MetricLatencyPtr &latency)
{
    std::lock_guard<std::mutex> lock(latency_mtx_);
    metrics_latency_map_.emplace(name, latency);
}

void MetricService::AddLatencyLatencyValue(const std::string& name, 
    uint32_t latency_ms)
{
    std::lock_guard<std::mutex> lock(latency_mtx_);
    auto it = metrics_latency_map_.find(name);
    if (it != metrics_latency_map_.end())
    {
        it->second->Record(latency_ms);
    }
}

void MetricService::AddLatencyLatencyValue(const std::string& name, 
    uint32_t latency_ms, const std::string &msg_id)
{
    std::lock_guard<std::mutex> lock(latency_mtx_);
    auto it = metrics_latency_map_.find(name);
    if (it != metrics_latency_map_.end())
    {
        it->second->Record(msg_id, latency_ms);
    }
}

// 更新 MetricNode 指标
void MetricService::UpdateMetrics()
{
    std::lock_guard<std::mutex> lock(data_mtx_);
    for (auto& pair : metrics_map_)
    {
        pair.second.UpdateValue();
    }
}

// 添加指标值
void MetricService::AddValue(const std::string& name, long delta)
{
    std::lock_guard<std::mutex> lock(data_mtx_);
    auto it = metrics_map_.find(name);
    if (it != metrics_map_.end())
    {
        it->second.AddValue(delta);
    }
}

// 减少指标值
void MetricService::SubValue(const std::string& name, long delta)
{
    std::lock_guard<std::mutex> lock(data_mtx_);
    auto it = metrics_map_.find(name);
    if (it!= metrics_map_.end())
    {
        it->second.SubValue(delta);
    }
}

// 设置指标值
void MetricService::SetValue(const std::string& name, long value)
{
    std::lock_guard<std::mutex> lock(data_mtx_);
    auto it = metrics_map_.find(name);
    if (it!= metrics_map_.end())
    {
        it->second.SetValue(value);
    }
}

// 将所有指标及其信息格式化成 格式化的字符串输出
std::string MetricService::ToFormateString() const
{
    std::string formated_string;
    {
        std::lock_guard<std::mutex> lock(data_mtx_);
        for (auto& pair : metrics_map_)
        {
            formated_string += pair.second.ToFormateString();
        }
    }
    {
        std::lock_guard<std::mutex> lock(latency_mtx_);
        for (auto& pair : metrics_latency_map_)
        {
            formated_string += pair.second->ToFormateString();
        }
    }    


    return formated_string;
}

