#ifndef _CSBASE_METRIC_SERVICE_H_
#define _CSBASE_METRIC_SERVICE_H_

#include <mutex>
#include <string>
#include <functional>
#include <unordered_map>
#include "MetricStruct.h"
#include "MetricLatency.h"

// 定义单例 MetricNode 服务类
class MetricService
{
    static std::mutex instance_mtx_;
    static MetricService* instance_;

    mutable std::mutex data_mtx_;
    std::unordered_map<std::string, MetricNode> metrics_map_;

    mutable std::mutex latency_mtx_;
    std::unordered_map<std::string, MetricLatencyPtr> metrics_latency_map_;

private:
    MetricService() = default;
    MetricService(const MetricService&) = delete;
    MetricService& operator=(const MetricService&) = delete;

public:
    static MetricService* GetInstance();

    void AddMetric(
        const std::string& name,
        const std::string& desc,
        const std::string& lbls,
        std::function<long()> callback = nullptr
    );
    void AddLatencyMetric(const std::string& name, 
        MetricLatencyPtr &latency);           

    void UpdateMetrics();
    void AddValue(const std::string& name, long delta);
    void SubValue(const std::string& name, long delta);
    void SetValue(const std::string& name, long value);
    
    void AddLatencyLatencyValue(const std::string& name, uint32_t latency_ms);
    void AddLatencyLatencyValue(const std::string& name, uint32_t latency_ms, const std::string &msg_id);

    std::string ToFormateString() const;
};

#endif
