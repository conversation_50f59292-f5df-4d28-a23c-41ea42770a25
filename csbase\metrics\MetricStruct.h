#ifndef _CSBASE_METRIC_H_
#define _CSBASE_METRIC_H_

#include <string>
#include <atomic>
#include <memory>
#include <functional>

enum class MetricSeverityLevel {
    NORMAL = 0,
    WARNING,   //通常是高水位邮件告警
    ERROR      //通常是电话告警
};

// 定义 MetricNode 结构体
struct MetricNode
{
    std::atomic<long>       value;          // 指标值
    std::string             labels;         // 标签，显示指标的属性
    std::string             description;    // 指标描述
    std::function<long()>   callback;       // 更新值的回调函数

    MetricNode(MetricNode&& other)
    {
        if (this != &other)
        {
            value.store(other.value.load(std::memory_order_acquire), std::memory_order_release);
            labels = std::move(other.labels);
            description = std::move(other.description);
            callback = std::move(other.callback);
        }
    }

    MetricNode(const std::string& desc, const std::string& lbls, std::function<long()> cb = nullptr)
    {
        callback = cb;
        labels = lbls;
        description = desc;
        value.store(0L, std::memory_order_release);
    }

    std::string GetLabels() { return labels; }
    std::string GetDescription() { return description; }

    long GetValue() { return value.load(std::memory_order_acquire); }
    void SetValue(long v) { value.store(v, std::memory_order_release); }
    void AddValue(long delta) { value.fetch_add(delta, std::memory_order_acq_rel); }
    void SubValue(long delta) { value.fetch_sub(delta, std::memory_order_acq_rel); }
    void UpdateValue() { if (callback) { SetValue(callback()); } }

    std::string ToFormateString() const
    {
        std::string formated_string = "# " + description + "\n";
        formated_string += labels + " " + std::to_string(value) + "\n";
        return formated_string;
    }
};

#endif
