#ifndef _AKCS_HTTP_REQUEST_H_
#define _AKCS_HTTP_REQUEST_H_

#include <boost/noncopyable.hpp>
#include <map>
#include <string>

namespace model
{
#define HTTP_CLIENT_ID "akcs-csmain-client"
#define HTTP_CLIENT_SECRET "8DkVZSK0N81fsZkle1lhc1eHibsCWJpUGRGAec9F8rSYIsW0yc8tSO080odJiHst"
#define HTTP_CLIENT_ID_SECRET HTTP_CLIENT_ID "_" HTTP_CLIENT_SECRET
#define HTTP_DATA_TYPE_JSON 1

typedef std::map<std::string, std::string> HttpRespuestKV;

const std::string HTTP_HEAD_API_VERSION             =  "api-version:6200";
const std::string HTTP_PARAM_TYPE                   =  "Type";
const std::string HTTP_PARAM_MAC                    =  "MAC";
const std::string HTTP_PARAM_LOCATION               =  "Location";
const std::string HTTP_PARAM_NODE                   =  "Node";
const std::string HTTP_PARAM_RELAY                  =  "Relay";
const std::string HTTP_PARAM_ID                     =  "ID";
const std::string DEFAULT_INDOOR_REALY              =  "[{\"name\":\"Relay1\",\"dtmf\":\"#\",\"enable\":0,\"showHome\":1,\"showTalking\":1,\"accessControl\":{\"pin\":1,\"rf\":1,\"face\":1,\"ble\":1,\"nfc\":1},\"schedule\":{\"enable\":0,\"access\":[]}},{\"name\":\"Relay2\",\"dtmf\":\"0\",\"enable\":0,\"showHome\":1,\"showTalking\":1,\"accessControl\":{\"pin\":1,\"rf\":1,\"face\":1,\"ble\":1,\"nfc\":1},\"schedule\":{\"enable\":0,\"access\":[]}}]";
const std::string DEFAULT_DOOR_REALY                =  "[{\"name\":\"Relay1\",\"dtmf\":\"#\",\"enable\":1,\"showHome\":1,\"showTalking\":1,\"accessControl\":{\"pin\":1,\"rf\":1,\"face\":1,\"ble\":1,\"nfc\":1},\"schedule\":{\"enable\":0,\"access\":[]}}]";



struct MemoryStruct
{
    char *memory;
    size_t size;
    MemoryStruct()
    {
        memory = (char *)malloc(1);
        size = 0;
    }

    ~MemoryStruct()
    {
        free(memory);
        memory = NULL;
        size = 0;
    }
};


class HttpRequest : private boost::noncopyable
{
public:
	static HttpRequest& GetInstance();
    int Get(const std::string &urlhead, const HttpRespuestKV &kv,  std::string &respone);
    int Post(const std::string &url, const std::string &data,  std::string &respone, int data_type);
};

}

#endif

