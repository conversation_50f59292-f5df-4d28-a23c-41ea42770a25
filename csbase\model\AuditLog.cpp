﻿#include "AuditLog.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "AkLogging.h"
#include "AkcsCommonDef.h"
using namespace model;

AuditLog& AuditLog::GetInstance()
{
    static AuditLog audit_log;
    return audit_log;
}

int AuditLog::InsertAuditLog(const AuditLogInfo& audit_log_info)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* rldb_conn = conn.get();
    if (NULL == rldb_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }

    char stream_sql[1024];
    snprintf(stream_sql, sizeof(stream_sql), "insert into AuditLog(IP, Operator, CreateTime, Type, KeyInfo, OperaType, Distributor, Installer, Community) "
             " values('%s', '%s', now(), %d, '%s', '%s', '%s', '%s', '%s')", audit_log_info.ip, audit_log_info.audit_operator, audit_log_info.type, audit_log_info.key_info, audit_log_info.opera_type, audit_log_info.distributor, audit_log_info.installer, audit_log_info.community);

    if (rldb_conn->Execute(stream_sql) < 0)
    {
        AK_LOG_WARN << "insert AuditLog failed.ip=" << audit_log_info.ip << ";operator=" << audit_log_info.audit_operator << ";key_info=" << audit_log_info.key_info << ";opera_type=" << audit_log_info.opera_type;
        ReleaseDBConn(conn);
        return -1;
    }

    ReleaseDBConn(conn);
    return 0;
}

int AuditLog::GetDistributor(AuditLogInfo& audit_log_info, const int role, const int parent_id)
{
    if (role == ACCOUNT_ROLE_PERSONNAL_MAIN || role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT)
    {
        return GetPersonalDistributor(audit_log_info, parent_id);
    }
    else if (role == ACCOUNT_ROLE_COMMUNITY_MAIN || role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT || role == ACCOUNT_ROLE_COMMUNITY_PM)
    {
        return GetCommunityDistributor(audit_log_info, parent_id);
    }
    else
    {
        AK_LOG_WARN << "Invalid role=" << role;
        return -1;
    }
}

int AuditLog::GetPersonalDistributor(AuditLogInfo& audit_log_info, const int installer_id)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* rldb_conn = conn.get();
    if (NULL == rldb_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    std::stringstream sql;
    sql << "SELECT a.Account Installer, b.Account Distributor FROM Account a "
           "  LEFT JOIN Account b ON a.ParentID = b.ID "
           " WHERE a.id = " << installer_id << " LIMIT 1";

    CRldbQuery query(rldb_conn);
    query.Query(sql.str());

    if (query.MoveToNextRow())
    {
        strncpy(audit_log_info.installer, query.GetRowData(0), sizeof(audit_log_info.installer) - 1);
        strncpy(audit_log_info.distributor, query.GetRowData(1), sizeof(audit_log_info.distributor) - 1);
    }

    ReleaseDBConn(conn);
    return 0;
}

int AuditLog::GetCommunityDistributor(AuditLogInfo& audit_log_info, const int community_id)
{
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* rldb_conn = conn.get();
    if (NULL == rldb_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    std::stringstream sql;
    sql << "SELECT A.Account Community, B.Account Installer  FROM Account A "
           "  LEFT JOIN Account B ON A.ManageGroup = B.ManageGroup "
           " WHERE A.ID = " << community_id  << " and B.Grade = " << AccountGrade::PERSONAL_MANEGER_GRADE << " LIMIT 1";

    CRldbQuery query(rldb_conn);
    query.Query(sql.str());

    if (query.MoveToNextRow())
    {
        strncpy(audit_log_info.community, query.GetRowData(0), sizeof(audit_log_info.community) - 1);
        strncpy(audit_log_info.installer, query.GetRowData(1), sizeof(audit_log_info.installer) - 1);
    }

    std::stringstream query_sql;
    query_sql << "SELECT  b.Account Distributor FROM Account a "
           "  LEFT JOIN Account b ON a.ParentID = b.ID "
           " WHERE a.account = '" << audit_log_info.installer << "' LIMIT 1";

    query.Query(query_sql.str());

    if (query.MoveToNextRow())
    {
        strncpy(audit_log_info.distributor, query.GetRowData(0), sizeof(audit_log_info.distributor) - 1);
    }

    ReleaseDBConn(conn);
    return 0;

}

const char* AuditLog::GetOperaType(int role)
{
    switch (role)
    {
    case ACCOUNT_ROLE_PERSONNAL_MAIN:
        return "SingleMaster";
    case ACCOUNT_ROLE_PERSONNAL_ATTENDANT:
        return "SingleMember";
    case ACCOUNT_ROLE_COMMUNITY_MAIN:
        return "CommunityMaster";
    case ACCOUNT_ROLE_COMMUNITY_ATTENDANT:
        return "CommunityMember";
    case ACCOUNT_ROLE_COMMUNITY_PM:
        return "PM";
    default:
        return "";
    }
}

