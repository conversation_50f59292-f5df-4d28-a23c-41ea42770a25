﻿#ifndef _AUDIT_LOG_H_
#define _AUDIT_LOG_H_

#include <boost/noncopyable.hpp>

namespace model
{
enum AuditType
{
    AUDIT_TYPE_LOG_IN = 1,
    AUDIT_TYPE_LOG_OUT = 2
};

enum
{
    ACCOUNT_ROLE_MANAGEMENT = 1,            //管理员
    ACCOUNT_ROLE_PROPERTY = 2,              //物业
    ACCOUNT_ROLE_INSTALL = 3,               //安装者
    ACCOUNT_ROLE_PERSONNAL_MAIN = 10,        //个人终端用户主账号
    ACCOUNT_ROLE_PERSONNAL_ATTENDANT = 11,   //个人终端用户从账号
    ACCOUNT_ROLE_PERSONNAL_V_PUB = 12,         //个人虚拟的账号，用于公共设备
    ACCOUNT_ROLE_COMMUNITY_MAIN = 20,        //社区用户主账号
    ACCOUNT_ROLE_COMMUNITY_ATTENDANT = 21,   //社区用户从账号
    ACCOUNT_ROLE_COMMUNITY_PM = 40,    //社区用户PM账号
    ACCOUNT_ROLE_MAX,
};


struct AuditLogInfo
{
    int64_t id;
    char ip[65];
    char audit_operator[65];
    char create_time[15];
    int type;
    char key_info[1025];
    char opera_type[32];
    char distributor[65];
    char installer[65];
    char community[65];
};

class AuditLog : private boost::noncopyable
{
public:
    static AuditLog& GetInstance();
    int InsertAuditLog(const AuditLogInfo& audit_log_info);
    const char* GetOperaType(int role);
    int GetDistributor(AuditLogInfo& audit_log_info, const int role, const int parent_id);
private:
    int GetPersonalDistributor(AuditLogInfo& audit_log_info, const int installer_id);
    int GetCommunityDistributor(AuditLogInfo& audit_log_info, const int community_id);
};

}

#endif

