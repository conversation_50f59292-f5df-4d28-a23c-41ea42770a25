﻿#ifndef _COMMON_MODEL_H_
#define _COMMON_MODEL_H_

#include "BasicDefine.h"
#include "DclientMsgSt.h"


#ifndef _T
#define _T(str) str
#endif


//社区终端用户设备列表结构体
typedef struct COMMUNITY_DEVICE_SIP_T
{
    char szName[USER_SIZE];
    char szSIPAccount[SIP_SIZE];
    char szIP[IP_SIZE];
    char szMac[MAC_SIZE];
    char szRtspPWd[RTSP_PWD_SIZE];
    char szRoomNum[16];
    int  nType;
} COMMUNITY_DEVICE_SIP;


//个人终端用户设备列表结构体
typedef struct PERSONNAL_DEVICE_SIP_T
{
    char szName[USER_SIZE];
    char szSIPAccount[SIP_SIZE];
    char szIP[IP_SIZE];
    char szMac[MAC_SIZE];
    char szRtspPWd[RTSP_PWD_SIZE];
    int  nType;
} PERSONNAL_DEVICE_SIP;

//added by chenyc,2017-08-25,个人终端用户功能开发
//个人终端用户app信息结构体
typedef struct SOCKET_MSG_PERSONNAL_APP_CONF_T
{
    char szProtocal[PROTOCAL_SIZE];
    char szNode[NODE_SIZE];  //指个人用户联动系统单元
    char szUser[USER_EMAIL_SIZE];  //解析xml完之后,可以是uid或者email
    char szPasswd[MD5_SIZE];
    char szToken[TOKEN_SIZE];//端外推送的token
    char szFcmToken[TOKEN_SIZE];
    char szVoipToken[TOKEN_SIZE];
    char szAppToken[TOKEN_SIZE]; // 网关登陆验证的token
    char szSip[SIP_SIZE];  //2017-11-17,支持pbx对接,索引sip->uid时新增
    char szUserName[USER_EMAIL_SIZE];
    int nVersion;//app上传的版本号，用于兼容处理 通用的版本号ios/android一样
    int  nMobileType;   //参见 csmain::AppType
    char szMsgSeq[MSG_SEQ_SIZE];
    uint32_t nRole;//add by chenzhx ********
    int  nLastReadMsgID;
    int  nIsExpire;
    char szAppVersion[32]; //app版本号，是各个上架时候的版本
    int  nIsActive;
    uint32_t nMngAccountID;//add by chenzhx ********
    uint32_t nUnitID;
    char language[32]; //app语言
    char szOemName[USER_SIZE];  //OEM
    char email[65];
    char mobile_number[25];
    int is_office;//add by chenzhx ********
} SOCKET_MSG_PERSONNAL_APP_CONF;

//账号信息
typedef struct ACCOUNT_MSG_T
{
    char szName[USER_SIZE];
    char szSIPAccount[SIP_SIZE];
    char szRtspPWd[RTSP_PWD_SIZE];
    char szRoomNum[16];
    char szNode[NODE_SIZE];
    int nRole;
    uint32_t nMngID;
    uint32_t nUnitID;
    uint32_t  nType;
} ACCOUNT_MSG;

typedef struct OpenDoorPrivateKeyInfo_T
{
    uint32_t unit_id;
    uint32_t room_id;
    char account_name[128];
    char node[32];
} OpenDoorPrivateKeyInfo;

#endif

