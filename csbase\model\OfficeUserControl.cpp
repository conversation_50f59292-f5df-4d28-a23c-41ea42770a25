#include <sstream>
#include <string.h>
#include "Md5.h"
#include "Rldb.h"
#include "Caesar.h"
#include "RldbQuery.h"
#include "AkLogging.h"
#include "AkcsCommonDef.h"
#include "OfficeUserControl.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/ProjectUserManage.h"


namespace dbcontrol
{

const static char KPasswordSalt[] = "YQNas*1698+zc-";

OfficeUserControl::OfficeUserControl()
{
    
}

OfficeUserControl::~OfficeUserControl()
{
    
}


int OfficeUserControl::CheckOfficeUser(OfficeAccount &account, const std::string &post_passwd)
{
    if (!PasswordCorrect(post_passwd, account.passwd))
    {
        return csgate::ERR_PASSWD_INVALID;
    }
    
    //账号过期 err_code==3
    if ((account.active) && (account.is_expire))
    {
        return csgate::ERR_APP_EXPIRE;
    }

    //主账号未激活
    if (!account.active)
    {
        return csgate::ERR_APP_UNACTIVE;
    }

    return csgate::ERR_SUCCESS;
}

int OfficeUserControl::CheckOfficePhone(OfficeAccount &account, VerificationPtr &code_info, const std::string &code, const std::string &area_code)
{
    // 校验区号
    if (area_code.length() > 0 && strcmp(area_code.c_str(), account.phone_code) != 0)
    {
        return csgate::ERR_USER_NOT_EXIT;
    }

    //账号过期 err_code==3
    if ((account.active) && (account.is_expire))
    {
        return csgate::ERR_APP_EXPIRE;
    }

    //主账号未激活
    if (!account.active)
    {
        return csgate::ERR_APP_UNACTIVE;
    }

    //验证码错误 err_code==2
    if (strcmp(code_info->code.c_str(), code.c_str()) || code_info->is_expire)
    {
        return csgate::ERR_PASSWD_INVALID;
    }

    return csgate::ERR_SUCCESS;
}

bool OfficeUserControl::PasswordCorrect(const std::string& post_passwd, const std::string& db_passwd)
{
    // app发上来的是  md5(md5(passwd)),    mysql存储的是md5(salt + md5(md5(passwd)))
    std::string post_passwd_salty = akuvox_encrypt::MD5(std::string(KPasswordSalt) + post_passwd).toStr();

    return post_passwd_salty == db_passwd;
}


}
