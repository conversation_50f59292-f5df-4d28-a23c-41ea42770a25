#ifndef __OFFICE_USER_CONTROL_H__
#define __OFFICE_USER_CONTROL_H__
#include <string>
#include <memory>
#include <vector>
#include "AkcsCommonDef.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/Verification.h"
#include "dbinterface/resident/PersonalAccountUserInfo.h"


namespace dbcontrol
{

class OfficeUserControl
{
public:
    OfficeUserControl();
    ~OfficeUserControl();
    static int CheckOfficeUser(OfficeAccount &account, const std::string &passwd_md5);
    static int CheckOfficePhone(OfficeAccount &account, VerificationPtr &code_info, const std::string &code, const std::string &area_code);
private:
    static bool PasswordCorrect(const std::string& post_passwd, const std::string& db_passwd);
};



}
#endif
