#include "stdlib.h"
#include <functional>
#include "AkcsPduBase.h"
#include "util.h"
#include <evnsq/consumer.h>
#include <event_loop.h>
#include "NsqConsumer.h"
#include "AkcsMonitor.h"
#include "EtcdCliMng.h"


extern CAkEtcdCliManager* g_etcd_cli_mng;
const static uint32_t kAkMsgHoldLen = sizeof(int32_t);

NsqConsumer::NsqConsumer(const    std::string topic, const std::string &channel, 
   const std::string &srv_id, int num)
{
    srv_id_ = srv_id;
    handle_thread_num_ = num;
    cust_ = new evnsq::Consumer(&nsq_loop_, topic, channel, evnsq::Option());
    cust_->SetConnectErrorCallback(std::bind(&NsqConsumer::OnConnectError, this,  std::placeholders::_1));
    cust_->SetMessageCallback(std::bind(&NsqConsumer::OnMQMessage, this, std::placeholders::_1));

    topic_ = topic;
}

void NsqConsumer::Start()
{
   InitCust();
   nsq_loop_.Run(); 
}


void NsqConsumer::OnConnectError(const std::string& addr)
{   
    AK_LOG_WARN << "Connect nsqd-" << addr << " error.";
    ConnectNsqErrorMutt(addr, "csroute");
}


void NsqConsumer::InitCust()
{
    for (int i = 0; i < handle_thread_num_; i++)
    {
        std::thread t = std::thread(&NsqConsumer::ProcessPduMsg, this);
        t.detach();
    }    
}

//msg不需要再判断消息
//参考: NSQConn::OnMessage的代码片段,如果OnRouteMQMessage返回非0值,则消息会被重新投递进队列中,重新被消费
//if (msg_fn_(&msg) == 0) {
//    Finish(msg.id);
//} else {
//    Requeue(msg.id);
//}
int NsqConsumer::OnMQMessage(const evnsq::Message* msg) //
{
    const char* data = msg->body.data();
    const uint32_t size = msg->body.size();
    if (size >= kAkMsgHoldLen)
    {
        const int32_t pb_msg_len = PeekInt32(data, size);
        if (size != (uint32_t)pb_msg_len)
        {
            AK_LOG_WARN << "Invalid evnsq length " << size;
            return 0;
        }
        else
        {
            std::shared_ptr<CAkcsPdu> pPdu(new CAkcsPdu());
            pPdu->Write(data, size); //包整体长度全部整进去
            char tmp_buf[sizeof(PduHeader_t)] = {0};
            memcpy(tmp_buf, data, sizeof(tmp_buf));
            if (pPdu->ReadPduHeader(tmp_buf, sizeof(PduHeader_t)) != 0)
            {
                AK_LOG_WARN << "Pdu packet header len is invalid";
                return 0;
            }
            else
            {
                AddMessage(pPdu);
            }
        }
    }
    else
    {
        AK_LOG_WARN << "Invalid evnsq length " << size;
        return 0;
    }
    return 0;
}


void NsqConsumer::AddMessage(const std::shared_ptr<CAkcsPdu>& pdu)
{
    std::list<std::shared_ptr<CAkcsPdu> > msg_pdus_tmp;
    {
        std::unique_lock<std::mutex> lock(msg_pdus_mtx_);
        msg_pdus_tmp =  msg_pdus_;;
    }
    if (msg_pdus_tmp.size() > 1000)
    {
        std::string worker_node = srv_id_;
        std::string msg = "the num of  mq msg more than 1000, length:";
        msg += std::to_string(msg_pdus_tmp.size());
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, msg, AKCS_MONITOR_ALARM_QUEUE_OVERFLOW_CSROUTE_MQ);
        AK_LOG_WARN << "too many msg to handle, discard new msg ";
        return;
    }
    std::unique_lock<std::mutex> lock(msg_pdus_mtx_);
    msg_pdus_.push_front(pdu);
    msg_pdus_cv_.notify_all();
}

void NsqConsumer::ProcessPduMsg()
{
    while (1)
    {
        std::shared_ptr<CAkcsPdu> pdu;
        {
            std::unique_lock<std::mutex> lock(msg_pdus_mtx_);
            while (msg_pdus_.size() == 0)
            {
                msg_pdus_cv_.wait(lock);
            }
            pdu = msg_pdus_.back();
            msg_pdus_.pop_back();
        }
        OnMessage(pdu);
    }
}

void NsqConsumer::NsqLookupdSrvInit(const std::set<std::string>& nsqLookupd_addrs)
{
    std::vector<std::string> urls;
    for (const auto& nsqLookupd : nsqLookupd_addrs) //ip:port的形式
    {
        std::stringstream lookupd_http_url;
        lookupd_http_url << "http://" << nsqLookupd << "/lookup?topic=" << GetTopic();
        urls.push_back(lookupd_http_url.str());
        
    }
    GetNsqConsumer()->ConnectToLookupds(urls);
}

void NsqConsumer::UpdateLookupdSrvList()
{
    //nsqlookupd
    std::set<std::string> nsqlookupd_addrs;
    if (g_etcd_cli_mng->GetAllNsqlookupdHttpSrvs(nsqlookupd_addrs) == 0)
    {
        GetNsqConsumer()->reset_nsqlookupd();
        NsqLookupdSrvInit(nsqlookupd_addrs);
    }
}


