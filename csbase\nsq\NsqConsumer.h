#ifndef __NSQD_MQ_CONSUMER_H__
#define __NSQD_MQ_CONSUMER_H__

#include <vector>
#include <map>
#include <set>
#include <unordered_set>
#include <memory>
#include <functional>
#include <evpp/buffer.h>
#include <evpp/any.h>
#include "AkcsPduBase.h"
#include <vector>
#include <map>
#include <set>
#include <unordered_set>
#include <memory>
#include <functional>
#include <evpp/buffer.h>
#include <evpp/any.h>
#include <evnsq/producer.h>
#include <evpp/evnsq/consumer.h>
#include <evpp/event_loop.h>
#include <evpp/evnsq/client.h>
#include <thread>
#include <mutex>
#include <condition_variable>


class NsqConsumer {
public:
    NsqConsumer(const std::string topic, const std::string &channel, 
        const std::string &srvid, int handle_thread_num);
    ~NsqConsumer(){}

    virtual void OnMessage(const std::shared_ptr<CAkcsPdu>& pdu){};
    
    evnsq::Consumer* GetNsqConsumer()
    {
        return cust_;
    }
    std::string GetTopic()
    {
        return topic_;
    }    
    void NsqLookupdSrvInit(const std::set<std::string>& nsqLookupd_addrs);
    void UpdateLookupdSrvList();    
    void Start();    
private:
    int OnMQMessage(const evnsq::Message* msg);
    void AddMessage(const std::shared_ptr<CAkcsPdu>& pdu);
    void ProcessPduMsg();
    void OnConnectError(const std::string& addr);
    void OnPduNSQReady();
    void InitCust();
    
    std::list<std::shared_ptr<CAkcsPdu> > msg_pdus_;
    std::mutex msg_pdus_mtx_;
    std::condition_variable msg_pdus_cv_;
    std::thread cust_thread_;
    evnsq::Consumer* cust_;
    evpp::EventLoop nsq_loop_;
    std::string srv_id_;
    int handle_thread_num_;
    std::string topic_;
};



#endif //__NSQD_MQ_PRODUCE_H__

