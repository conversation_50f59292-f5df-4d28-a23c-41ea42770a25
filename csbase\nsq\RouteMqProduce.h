#ifndef __NSQD_MQ_PRODUCE_H__
#define __NSQD_MQ_PRODUCE_H__

#include <vector>
#include <map>
#include <set>
#include <unordered_set>
#include <memory>
#include <functional>
#include <evpp/buffer.h>
#include <evpp/any.h>
#include "AkcsPduBase.h"
#include <evnsq/producer.h>

int OnRouteMQMessage(const evnsq::Message* msg);
class RouteMQProduce {
public:
    RouteMQProduce(evnsq::Producer* producer)
    :client_(producer)
    {}
    ~RouteMQProduce(){}

    public:
        bool OnPublish(CAkcsPdu& pdu, const std::string& topic);
        void OnNSQReady();
        void OnConnectError(const std::string& addr);
        bool Status(){return nsq_status_;};
    private:
        bool nsq_status_ = false;
        evnsq::Producer* client_;

};

void MQProduceInit();


#endif //__NSQD_MQ_PRODUCE_H__

