/**
 * Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.
 * SPDX-License-Identifier: Apache-2.0.
 */

#pragma once

#include <aws/core/auth/signer/AWSAuthSignerBase.h>
#include <aws/core/auth/signer/AWSAuthSignerCommon.h>

#include <aws/core/auth/signer/AWSAuthV4Signer.h>
#include <aws/core/auth/signer/AWSAuthEventStreamV4Signer.h>
#include <aws/core/auth/signer/AWSNullSigner.h>

// This is a header that represents old legacy all-in-one header to maintain backward compatibility
