/**AWSProfileConfigLoaderBaseAWSProfileConfigLoader
 * Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.
 * SPDX-License-Identifier: Apache-2.0.
 */

#pragma once

#include <aws/core/config/AWSProfileConfig.h>
#include <aws/core/config/AWSProfileConfigLoaderBase.h>
#include <aws/core/config/AWSConfigFileProfileConfigLoader.h>
#include <aws/core/config/EC2InstanceProfileConfigLoader.h>
#include <aws/core/config/ConfigAndCredentialsCacheManager.h>

// This is a header that represents old legacy all-in-one header to maintain backward compatibility
