#ifndef AWS_HTTP_STATUS_CODE_H
#define AWS_HTTP_STATUS_CODE_H

/**
 * Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.
 * SPDX-License-Identifier: Apache-2.0.
 */

/*
 * Define most of the http response codes we probably will use.
 * https://www.iana.org/assignments/http-status-codes/http-status-codes.txt
 * This is NOT a definitive list of codes.
 */
enum aws_http_status_code {
    /*
     * This is a special response code defined for convenience in error processing,
     * indicating processing of http request met error and didn't reach server.
     */
    AWS_HTTP_STATUS_CODE_UNKNOWN = -1,
    AWS_HTTP_STATUS_CODE_100_CONTINUE = 100,
    AWS_HTTP_STATUS_CODE_101_SWITCHING_PROTOCOLS = 101,
    AWS_HTTP_STATUS_CODE_102_PROCESSING = 102,
    AWS_HTTP_STATUS_CODE_103_EARLY_HINTS = 103,
    AWS_HTTP_STATUS_CODE_200_OK = 200,
    AWS_HTTP_STATUS_CODE_201_CREATED = 201,
    AWS_HTTP_STATUS_CODE_202_ACCEPTED = 202,
    AWS_HTTP_STATUS_CODE_203_NON_AUTHORITATIVE_INFORMATION = 203,
    AWS_HTTP_STATUS_CODE_204_NO_CONTENT = 204,
    AWS_HTTP_STATUS_CODE_205_RESET_CONTENT = 205,
    AWS_HTTP_STATUS_CODE_206_PARTIAL_CONTENT = 206,
    AWS_HTTP_STATUS_CODE_207_MULTI_STATUS = 207,
    AWS_HTTP_STATUS_CODE_208_ALREADY_REPORTED = 208,
    AWS_HTTP_STATUS_CODE_226_IM_USED = 226,
    AWS_HTTP_STATUS_CODE_300_MULTIPLE_CHOICES = 300,
    AWS_HTTP_STATUS_CODE_301_MOVED_PERMANENTLY = 301,
    AWS_HTTP_STATUS_CODE_302_FOUND = 302,
    AWS_HTTP_STATUS_CODE_303_SEE_OTHER = 303,
    AWS_HTTP_STATUS_CODE_304_NOT_MODIFIED = 304,
    AWS_HTTP_STATUS_CODE_305_USE_PROXY = 305,
    AWS_HTTP_STATUS_CODE_307_TEMPORARY_REDIRECT = 307,
    AWS_HTTP_STATUS_CODE_308_PERMANENT_REDIRECT = 308,
    AWS_HTTP_STATUS_CODE_400_BAD_REQUEST = 400,
    AWS_HTTP_STATUS_CODE_401_UNAUTHORIZED = 401,
    AWS_HTTP_STATUS_CODE_402_PAYMENT_REQUIRED = 402,
    AWS_HTTP_STATUS_CODE_403_FORBIDDEN = 403,
    AWS_HTTP_STATUS_CODE_404_NOT_FOUND = 404,
    AWS_HTTP_STATUS_CODE_405_METHOD_NOT_ALLOWED = 405,
    AWS_HTTP_STATUS_CODE_406_NOT_ACCEPTABLE = 406,
    AWS_HTTP_STATUS_CODE_407_PROXY_AUTHENTICATION_REQUIRED = 407,
    AWS_HTTP_STATUS_CODE_408_REQUEST_TIMEOUT = 408,
    AWS_HTTP_STATUS_CODE_409_CONFLICT = 409,
    AWS_HTTP_STATUS_CODE_410_GONE = 410,
    AWS_HTTP_STATUS_CODE_411_LENGTH_REQUIRED = 411,
    AWS_HTTP_STATUS_CODE_412_PRECONDITION_FAILED = 412,
    AWS_HTTP_STATUS_CODE_413_REQUEST_ENTITY_TOO_LARGE = 413,
    AWS_HTTP_STATUS_CODE_414_REQUEST_URI_TOO_LONG = 414,
    AWS_HTTP_STATUS_CODE_415_UNSUPPORTED_MEDIA_TYPE = 415,
    AWS_HTTP_STATUS_CODE_416_REQUESTED_RANGE_NOT_SATISFIABLE = 416,
    AWS_HTTP_STATUS_CODE_417_EXPECTATION_FAILED = 417,
    AWS_HTTP_STATUS_CODE_421_MISDIRECTED_REQUEST = 421,
    AWS_HTTP_STATUS_CODE_422_UNPROCESSABLE_ENTITY = 422,
    AWS_HTTP_STATUS_CODE_423_LOCKED = 423,
    AWS_HTTP_STATUS_CODE_424_FAILED_DEPENDENCY = 424,
    AWS_HTTP_STATUS_CODE_425_TOO_EARLY = 425,
    AWS_HTTP_STATUS_CODE_426_UPGRADE_REQUIRED = 426,
    AWS_HTTP_STATUS_CODE_428_PRECONDITION_REQUIRED = 428,
    AWS_HTTP_STATUS_CODE_429_TOO_MANY_REQUESTS = 429,
    AWS_HTTP_STATUS_CODE_431_REQUEST_HEADER_FIELDS_TOO_LARGE = 431,
    AWS_HTTP_STATUS_CODE_451_UNAVAILABLE_FOR_LEGAL_REASON = 451,
    AWS_HTTP_STATUS_CODE_500_INTERNAL_SERVER_ERROR = 500,
    AWS_HTTP_STATUS_CODE_501_NOT_IMPLEMENTED = 501,
    AWS_HTTP_STATUS_CODE_502_BAD_GATEWAY = 502,
    AWS_HTTP_STATUS_CODE_503_SERVICE_UNAVAILABLE = 503,
    AWS_HTTP_STATUS_CODE_504_GATEWAY_TIMEOUT = 504,
    AWS_HTTP_STATUS_CODE_505_HTTP_VERSION_NOT_SUPPORTED = 505,
    AWS_HTTP_STATUS_CODE_506_VARIANT_ALSO_NEGOTIATES = 506,
    AWS_HTTP_STATUS_CODE_507_INSUFFICIENT_STORAGE = 507,
    AWS_HTTP_STATUS_CODE_508_LOOP_DETECTED = 508,
    AWS_HTTP_STATUS_CODE_510_NOT_EXTENDED = 510,
    AWS_HTTP_STATUS_CODE_511_NETWORK_AUTHENTICATION_REQUIRED = 511,
};
#endif /* AWS_HTTP_STATUS_CODE_H */
