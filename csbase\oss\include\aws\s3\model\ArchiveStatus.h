﻿/**
 * Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.
 * SPDX-License-Identifier: Apache-2.0.
 */

#pragma once
#include <aws/s3/S3_EXPORTS.h>
#include <aws/core/utils/memory/stl/AWSString.h>

namespace Aws
{
namespace S3
{
namespace Model
{
  enum class ArchiveStatus
  {
    NOT_SET,
    ARCHIVE_ACCESS,
    DEEP_ARCHIVE_ACCESS
  };

namespace ArchiveStatusMapper
{
AWS_S3_API ArchiveStatus GetArchiveStatusForName(const Aws::String& name);

AWS_S3_API Aws::String GetNameForArchiveStatus(ArchiveStatus value);
} // namespace ArchiveStatusMapper
} // namespace Model
} // namespace S3
} // namespace Aws
