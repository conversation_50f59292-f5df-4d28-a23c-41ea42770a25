﻿/**
 * Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.
 * SPDX-License-Identifier: Apache-2.0.
 */

#pragma once
#include <aws/s3/S3_EXPORTS.h>
#include <aws/core/utils/memory/stl/AWSString.h>

namespace Aws
{
namespace S3
{
namespace Model
{
  enum class ChecksumAlgorithm
  {
    NOT_SET,
    CRC32,
    CRC32C,
    SHA1,
    SHA256
  };

namespace ChecksumAlgorithmMapper
{
AWS_S3_API ChecksumAlgorithm GetChecksumAlgorithmForName(const Aws::String& name);

AWS_S3_API Aws::String GetNameForChecksumAlgorithm(ChecksumAlgorithm value);
} // namespace ChecksumAlgorithmMapper
} // namespace Model
} // namespace S3
} // namespace Aws
