#ifndef _UFILESDK_CPP_UCLOUD_API_PUT_
#define _UFILESDK_CPP_UCLOUD_API_PUT_

#include <istream>
#include <ufile-cppsdk/apibase.h>

namespace ucloud {
namespace cppsdk {
namespace api {

class UFilePut : public APIBase {

public:
  UFilePut();
  ~UFilePut();

  /*
   * @brief: 上传文件
   * @bucket: 目标Bucket名称
   * @key: 保存在Bucket上的文件对象名称
   * @is: 输入流
   * @return: 0=成功，非0=失败
   */
  int Put(const std::string &bucket, const std::string &key, std::istream &is);
  /*
   * @brief: 上传文件
   * @bucket: 目标Bucket名称
   * @key: 保存在Bucket上的文件对象名称
   * @is: 输入流
   * @return: 0=成功，非0=失败
   */
  int PutFile(const std::string &bucket, const std::string &key,
              const std::string &filepath);

protected:
  std::string m_filename;
};

class UFileClient : public UFilePut {

public:
  /*
   * @brief: 初始化
   * @publickey:API公钥
   * @privatekey:API私钥
  */
  void InitUFileClient(const std::string& public_key, const std::string& private_key,
                       const std::string& proxy_host); 

  /*
   * @brief: 对象存储上传文件
   * @bucket: 目标Bucket名称
   * @key: 保存在Bucket上的文件对象名称
   * @filepath: 上传文件的本地存储路径
  */
  int PutObject(const std::string &bucket, const std::string &key,
                const std::string &filepath);

  /*
   * @brief: 设置连接超时时间
   * @timeout_ms: 超时时间，单位为毫秒
  */
  void SetConnectionTimeoutMs(uint32_t timeout_ms);

  /*
   * @brief: 设置请求超时时间
   * @timeout_ms: 超时时间，单位为毫秒
  */
  void SetRequestTimeoutMs(uint32_t timeout_ms);
};

} // namespace api
} // namespace cppsdk
} // namespace ucloud

#endif
