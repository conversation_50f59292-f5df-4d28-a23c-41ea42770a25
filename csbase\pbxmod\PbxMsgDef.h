#ifndef _PBX_MSG_DEF_H_
#define _PBX_MSG_DEF_H_
#include <set>
#include <string>

#define IP_ADDR_LEN     32
#define AKCS_SIP_SIZE   64

typedef struct AKCS_CALL_HISTORY_T
{
#define AKCS_CALL_HISTORY_DATETIME_SIZE     32
#define AKCS_CALL_HISTORY_HANGUP_CAUSE_SIZE 32
#define AKCS_CALL_HISTORY_GROUP_CALL_LIST_SIZE 1024
#define AKCS_CALL_HISTORY_GLOCAL_CALLEE_NAME_SIZE 128

    int bill_second;
    char caller_sip[AKCS_SIP_SIZE];/*主叫*/
    char callee_sip[AKCS_SIP_SIZE];/*被叫*/
    char called_sip[AKCS_SIP_SIZE];/*接听*/
    char caller_name[AKCS_SIP_SIZE];/*主叫名称*/
    char callee_name[AKCS_SIP_SIZE];/*被叫名称*/
    char start_time[AKCS_CALL_HISTORY_DATETIME_SIZE];
    char answer_time[AKCS_CALL_HISTORY_DATETIME_SIZE];
    char hangup_cause[AKCS_CALL_HISTORY_HANGUP_CAUSE_SIZE];
    char freeswitch_node[IP_ADDR_LEN];
    char caller_ops_node[64];/*主叫ops节点*/
    char callee_ops_node[64];/*被叫ops节点*/
    char group_call_list[AKCS_CALL_HISTORY_GROUP_CALL_LIST_SIZE];
    char call_trace_id[64];
    int is_ip_call;
    char db_delivery_uuid[64];
    char glocal_callee_name[AKCS_CALL_HISTORY_GLOCAL_CALLEE_NAME_SIZE];
} AKCS_CALL_HISTORY;

typedef struct AKCS_WAKEUP_APP_T
{
    int  app_type;/*app类型 smartplus还是sdk*/
    char caller_sip[AKCS_SIP_SIZE];/*主叫*/
    char callee_sip[AKCS_SIP_SIZE];/*被叫*/
    char nick_name_location[128];/*主叫名称*/
    char x_caller[AKCS_SIP_SIZE];/*室内机转接时候 门口机的sip*/
    char x_name[64];/*hager项目内网门口机的name*/
    char original_callee[AKCS_SIP_SIZE];/*多套房中间篡改前的原始被叫，若无多套房，则和callee_sip值一样的*/
    char timestamp[AKCS_SIP_SIZE];/*呼叫开始时间*/
} AKCS_WAKEUP_APP;

typedef struct AKCS_HANGUP_APP_T
{
    char caller_sip[AKCS_SIP_SIZE];/*主叫*/
    char callee_sip[AKCS_SIP_SIZE];/*被叫*/
    char nick_name_location[128];/*主叫名称*/
    char x_caller[AKCS_SIP_SIZE];/*室内机转接时候 门口机的sip*/
    char x_name[64];/*hager项目内网门口机的name*/
    int  app_type;/*app类型 smartplus还是sdk*/
    char original_callee[AKCS_SIP_SIZE];/*多套房中间篡改前的原始被叫，若无多套房，则和callee_sip值一样的*/
} AKCS_HANGUP_APP;

typedef struct AKCS_LANDLINE_STATUS_T
{
    char caller_sip[AKCS_SIP_SIZE];
    char phone_number[32];
} AKCS_LANDLINE_STATUS;

typedef struct AKCS_LANDLINE_NUMBER_T
{
    char sip[AKCS_SIP_SIZE];
    char phone_number[32];
    char phone_code[9];
    int  type;//0-sip号 1-群组号
} AKCS_LANDLINE_NUMBER;

typedef struct CSPBX_CONF_T
{
    char nsq_route_topic[32];
    char etcd_server[32];
    char pbx_out_ip[32];
    char opensips_out_ip[32];
    char szBeansTalkdIP[32];
    int nBeansTalkdPort;
    
    char http_head_url[512];//http://localhost:9000
    char smarthome_http_head_url[512];//http://localhost:9000
    int is_nearby_instance; //是否是就近部署实例，是：走http接口
    char freeswitch_node[32];
    int enable_smarthome_gateway;//是否启用家居网关
    int freeswitch_db_num;
} CSPBX_CONF;

typedef struct AKCS_UID_STATUS_T
{
    char uid[64];
    uint64_t traceid;
    int callee_app_type;
    char caller[64];
    int original_caller_app_type;
    char callee_type[8]; 
    char caller_type[8];
    char original_callee[64];
    int main_site_caller_app_type;
}AKCS_UID_STATUS;


typedef enum
{
    PBX_MOD_FREESWITCH,
    PBX_MOD_OPENSIPS,
} PBX_INIT_MOD_TYPE;


typedef std::set<std::string> AKCS_OPENSIPS_INFO;

#endif //end _PBX_MSG_DEF_H_
