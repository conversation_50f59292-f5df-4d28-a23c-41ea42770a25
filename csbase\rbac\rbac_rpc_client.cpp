#include "rbac_rpc_client.h"
#include "AkcsMonitor.h"
#include <grpcpp/impl/codegen/status_code_enum.h> 
#include "rbac_rpc_client.h"


void RbacRpcClient::SetNextResolution(const std::vector<AddressData>& address_data)
{
    grpc_core::ExecCtx exec_ctx;
    grpc_lb_addresses* addresses = CreateLbAddressesFromAddressDataList(address_data);
    grpc_arg fake_addresses = grpc_lb_addresses_create_channel_arg(addresses);
    grpc_channel_args fake_result = { 1, &fake_addresses };
    response_generator_->SetResponse(&fake_result);
    grpc_lb_addresses_destroy(addresses);
}

void RbacRpcClient::SetNextReresolutionResponse(const std::vector<AddressData>& address_data)
{
    grpc_core::ExecCtx exec_ctx;
    grpc_lb_addresses* addresses = CreateLbAddressesFromAddressDataList(address_data);
    grpc_arg fake_addresses = grpc_lb_addresses_create_channel_arg(addresses);
    grpc_channel_args fake_result = { 1, &fake_addresses };
    response_generator_->SetReresolutionResponse(&fake_result);
    grpc_lb_addresses_destroy(addresses);
}

void RbacRpcClient::ResetStub(int fallback_timeout, const grpc::string& expected_targets)
{
    grpc::ChannelArguments args;
    args.SetGrpclbFallbackTimeout(fallback_timeout);
    args.SetLoadBalancingPolicyName("round_robin");//pick_first/round_robin/grcplb自定义负载均衡
    args.SetPointer(GRPC_ARG_FAKE_RESOLVER_RESPONSE_GENERATOR, response_generator_.get());
    if (!expected_targets.empty())
    {
        args.SetString(GRPC_ARG_FAKE_SECURITY_EXPECTED_TARGETS, expected_targets);
    }

    std::ostringstream uri;
    uri << "fake:///" << kApplicationTargetName_;
    channel_ = grpc::CreateCustomChannel(uri.str(), grpc::InsecureChannelCredentials(), args);
    stub_ = RBAC::NewStub(channel_);

    AK_LOG_INFO << "RbacRpcClient ResetStub uri=" << uri.str();
}

grpc_lb_addresses* RbacRpcClient::CreateLbAddressesFromAddressDataList(const std::vector<AddressData>& address_data)
{
    grpc_lb_addresses* addresses = grpc_lb_addresses_create(address_data.size(), nullptr);
    for (size_t i = 0; i < address_data.size(); ++i)
    {
        char* lb_uri_str;
        gpr_asprintf(&lb_uri_str, "ipv4:%s:%d", address_data[i].host.c_str(), address_data[i].port);
        grpc_uri* lb_uri = grpc_uri_parse(lb_uri_str, true);
        GPR_ASSERT(lb_uri != nullptr);
        grpc_lb_addresses_set_address_from_uri(
            addresses, i, lb_uri, address_data[i].is_balancer,
            address_data[i].balancer_name.c_str(), nullptr);
        grpc_uri_destroy(lb_uri);
        gpr_free(lb_uri_str);
    }
    return addresses;
}



RbacRpcClient::RbacRpcClient()
{
    response_generator_ = grpc_core::MakeRefCounted<grpc_core::FakeResolverResponseGenerator>();
    ResetStub(500);
}

int RbacRpcClient::GetEndUserDataGroup(const GetEndUserDataGroupRequest& request, GetEndUserDataGroupResponse& response)
{
    ClientContext client_context;
    client_context.set_deadline(std::chrono::system_clock::now() + std::chrono::milliseconds(500));
    Status status = stub_->GetEndUserDataGroup(&client_context, request, &response);
    if (status.ok() == false)
    {
        AK_LOG_WARN << "RBAC rpc client GetEndUserDataGroup failed: code=" << status.error_code()
            << ", message=" << status.error_message()
            << ", details=" << status.error_details();
        return -1;
    }

    return 0;
}

std::string RbacRpcClient::GetEndUserRbacUUID(const std::string& from, const std::string& uuid, const std::string& trace_id)
{
    // init request.
    domainServer_rbac::GetEndUserDataGroupRequest request;
    request.set_user(uuid);

    // init context.
    domainServer_rbac::Context* context = request.mutable_context();
    context->set_trace_id(trace_id);
    context->set_originname(APPBACKEND_NAME);
    context->set_originrequest("url");
    context->set_from(from);
    context->set_time(time(nullptr));
    context->set_type("normal");
    context->set_level(0);
    context->set_issync(true);

    domainServer_rbac::GetEndUserDataGroupResponse response;
    if (GetEndUserDataGroup(request, response) == 0)
    {
        return response.data().rbacdatagroupuuid();
    }
 
    AK_LOG_WARN << "RbacRpcClient GetEndUserDataGroup failed: from=" << from << ", uuid=" << uuid << ", trace_id=" << trace_id;
    return "";
}
