/*
 *
 * Copyright 2015 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
#ifndef _RBAC_RPC_CLIENT_H_
#define _RBAC_RPC_CLIENT_H_

#include <unistd.h>

#include <memory>
#include <string>
#include <chrono>
#include <thread>
#include <iostream>

#include <grpcpp/grpcpp.h>
#include <grpc/support/log.h>
#include "grpc_balancer_service.h"

#include "AkLogging.h"
#include "AkcsMsgDef.h"
#include "AK.RBAC.grpc.pb.h"

using grpc::Status;
using grpc::Channel;
using grpc::ClientContext;
using grpc::CompletionQueue;
using grpc::ClientAsyncResponseReader;

using domainServer_rbac::Context;
using domainServer_rbac::ErrorDetail;
using domainServer_rbac::UserData;
using domainServer_rbac::CheckAuthData;
using domainServer_rbac::CheckAuthRequest;
using domainServer_rbac::CheckAuthResponse;
using domainServer_rbac::MenuData;
using domainServer_rbac::GetMenusData;
using domainServer_rbac::GetMenusRequest;
using domainServer_rbac::GetMenusResponse;
using domainServer_rbac::UserDataGroup;
using domainServer_rbac::CreateDataGroupData;
using domainServer_rbac::GetEndUserDataGroupRequest;
using domainServer_rbac::GetEndUserDataGroupResponse;
using domainServer_rbac::GetAdminDataGroupRequest;
using domainServer_rbac::GetAdminDataGroupResponse;
using domainServer_rbac::GetCompanyDataGroupRequest;
using domainServer_rbac::GetCompanyDataGroupResponse;
using domainServer_rbac::CreateEndUserDataGroupRequest;
using domainServer_rbac::CreateEndUserDataGroupResponse;
using domainServer_rbac::CreateAdminDataGroupRequest;
using domainServer_rbac::CreateAdminDataGroupResponse;

using domainServer_rbac::RBAC; //rpc服务名

const std::string APPBACKEND_NAME = "app_backend";    // 应用后端标识

class RbacRpcClient
{
    const std::string kApplicationTargetName_ = "rbac";

    std::shared_ptr<Channel> channel_;
    std::unique_ptr<RBAC::Stub> stub_;
    grpc_core::RefCountedPtr<grpc_core::FakeResolverResponseGenerator> response_generator_;
    
public:
    // rpc 负载均衡相关实现
    void SetNextResolution(const std::vector<AddressData>& address_data);
    void SetNextReresolutionResponse(const std::vector<AddressData>& address_data);
    void ResetStub(int fallback_timeout = 0, const grpc::string& expected_targets = "");
    grpc_lb_addresses* CreateLbAddressesFromAddressDataList(const std::vector<AddressData>& address_data);
    
public:
    explicit RbacRpcClient();
    RbacRpcClient(const RbacRpcClient&) = delete;

    // rpc 服务
    int GetEndUserDataGroup(const GetEndUserDataGroupRequest& request, GetEndUserDataGroupResponse& response);
    std::string GetEndUserRbacUUID(const std::string& from, const std::string& uuid, const std::string& trace_id);
};

#endif
