/*
 * CachePool.cpp
 * Created on: 2017-12-25
 * Author: chenyc
 */
#include <assert.h>
#include "CachePool.h"
#include "ConfigFileReader.h"
#include "MetricService.h"


#define MAX_CACHE_CONN_CNT	10
#define MASTER_SLAVE_SWITCH_TIME  30 //sentinel判断主从切换的时间
static const char redis_pwd[20] = "Akcs#xm2610*";

CacheManager* CacheManager::s_cache_manager = NULL;

CacheConn::CacheConn(CachePool* pCachePool)
{
	m_pCachePool = pCachePool;
	m_pContext = NULL;
	m_last_connect_time = 0;
    m_first_connect_time = 0;
    m_is_first_connect_error = 0;
}

CacheConn::~CacheConn()
{
	if (m_pContext) {
		redisFree(m_pContext);
		m_pContext = NULL;
	}
}

/*
 * redis初始化连接和重连操作，类似mysql_ping()
 */
int CacheConn::Init()
{
	if (m_pContext) {  //在这里进行tcp连接是否断开的判断.若已经断开了,则进行重连
		return 0;
	}

	// 4s 尝试重连一次
	uint64_t cur_time = (uint64_t)time(NULL);
	if (cur_time < m_last_connect_time + 4) {
		return 1;
	}

	m_last_connect_time = cur_time;

	// 200ms超时
	struct timeval timeout = {0, 200000};
	m_pContext = redisConnectWithTimeout(m_pCachePool->GetServerIP(), m_pCachePool->GetServerPort(), timeout);
	if (!m_pContext || m_pContext->err) {
		if (m_pContext) {
			AK_LOG_WARN << "redisConnect failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
			redisFree(m_pContext);
			m_pContext = NULL;
		} else {
			AK_LOG_WARN << "redisConnect failed." << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
		}
        if (m_is_first_connect_error == 0)
        {
            m_is_first_connect_error = 1;
            m_first_connect_time = (uint64_t)time(NULL);
        }
        else
        {
        	uint64_t cur_time = (uint64_t)time(NULL);
        	//三十秒连接错误，从新走sentinel，因为可能这个地址的服务器已经挂了。虽然sentinel订阅会触发，但是redis官方说并不会保证一定会让客户端收到
        	if (cur_time > m_first_connect_time + MASTER_SLAVE_SWITCH_TIME) {
        	    //TODO：每个连接都会重新通知上一层从新初始化
        	    m_first_connect_time = cur_time - MASTER_SLAVE_SWITCH_TIME;//防止还未切换完成，等待下一轮
        		conn_error_cb_(MASTER_SLAVE_SWITCH_CONNECT_TIMEOUT);
        	}
        }
		return 1;
	}
	m_is_first_connect_error = 0;
    //若redis需要鉴权,则在这里通过redisCommand命令进行鉴权,也就是先连接再鉴权.
    
    //密码鉴权,暂时写死了..
    redisReply *redis_reply_ = (redisReply *)redisCommand(m_pContext, "auth %s", redis_pwd);
    if (redis_reply_->type == REDIS_REPLY_ERROR)
    {
        AK_LOG_WARN << "redis Authentication failure";
		redisFree(m_pContext);
		m_pContext = NULL;
		return 1;
    }
    else
    {
        //LOG_INFO << "Authentication success";
        
    }
    freeReplyObject(redis_reply_);

    //判处sentinel返回的主从，是否真正的主从
    redis_reply_ = (redisReply *)redisCommand(m_pContext, "INFO", "%s", "Replication");
    if (redis_reply_->type == REDIS_REPLY_ERROR)
    {
        AK_LOG_WARN << "redis INFO replication failure. " << redis_reply_->str;
    }
    else
    {
        size_t found = std::string(redis_reply_->str).find("role:master");
        if (found != std::string::npos)//主
        {
            //要求连接从，结果连接到主的，程序还是允许运行，但是最好做下切换 
        }
        else//从
        {
            if (m_pCachePool->GetIsMaster())//要求连接主服务器，结果连接的是从的，那么就需要全部重新连接
            {
                //注意:如果本身配置是从服务器，而其实sentinel里面记录的这台是主服务器---redis sentinel官方文档要求处理这种情况
                AK_LOG_WARN << "request connect master redis " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort() << ",but real connect is slave,maybe master/slave is translated!";
                freeReplyObject(redis_reply_);
        		redisFree(m_pContext);
        		m_pContext = NULL;
        		sleep(1);//防止错误时候一直占用cpu
        		conn_error_cb_(MASTER_OR_SLATE_ROLE_ERROR);
        		return 1;                
            }        
        }
    }
    freeReplyObject(redis_reply_);

    
    //切换数据库
	redisReply* reply = (redisReply *)redisCommand(m_pContext, "SELECT %d", m_pCachePool->GetDBNum());
	if (reply && (reply->type == REDIS_REPLY_STATUS) && (strncmp(reply->str, "OK", 2) == 0)) {
		freeReplyObject(reply);
		return 0;
	} else {
		AK_LOG_WARN << "select cache db failed";
		return 2;
	}
}


const char* CacheConn::GetPoolName()
{
	return m_pCachePool->GetPoolName();
}


string CacheConn::get(string key)
{
	string value;

	if (Init()) {
		return value;
	}

	redisReply* reply = (redisReply *)redisCommand(m_pContext, "GET %s", key.c_str());
	if (!reply) {
        AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
		redisFree(m_pContext);
		m_pContext = NULL;
		return value;
	}
    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }

	if (reply->type == REDIS_REPLY_STRING) {
		value.append(reply->str, reply->len);
	}

	freeReplyObject(reply);
	return value;
}
//带过期时间的set 以秒为单位
string CacheConn::setex(string key, int timeout, string value)
{
	string ret_value;

	if (Init()) {
		return ret_value;
	}

	redisReply* reply = (redisReply *)redisCommand(m_pContext, "SETEX %s %d %s", key.c_str(), timeout, value.c_str());
	if (!reply) {
		AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
		redisFree(m_pContext);
		m_pContext = NULL;
		return ret_value;
	}
    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }

	ret_value.append(reply->str, reply->len);
	freeReplyObject(reply);
	return ret_value;
}

string CacheConn::set(const string &key, const string &value)
{
    string ret_value;
    
    if (Init()) {
        return ret_value;
    }
    
    redisReply* reply = (redisReply *)redisCommand(m_pContext, "SET %s %s", key.c_str(), value.c_str());
    if (!reply) {
        AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr  << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
        redisFree(m_pContext);
        m_pContext = NULL;
        return ret_value;
    }
    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
        freeReplyObject(reply);
        return ret_value;
    }
    ret_value.append(reply->str, reply->len);
    freeReplyObject(reply);
    return ret_value;
}

long CacheConn::del(string key)
{
	if (Init()) {
		return 0;
	}

	redisReply* reply = (redisReply *)redisCommand(m_pContext, "DEL %s", key.c_str());
	if (!reply) {
		AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
		redisFree(m_pContext);
		m_pContext = NULL;
		return 0;
	}
    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }
    

	long ret_value = reply->integer;
	freeReplyObject(reply);
	return ret_value;
}

bool CacheConn::mget(const vector<string>& keys, map<string, string>& ret_value)
{
    if(Init())
    {
        return false;
    }
    if(keys.empty())
    {
        return false;
    }
    
    string strKey;
    bool bFirst = true;
    for (vector<string>::const_iterator it=keys.begin(); it!=keys.end(); ++it) {
        if(bFirst)
        {
            bFirst = false;
            strKey = *it;
        }
        else
        {
            strKey += " " + *it;
        }
    }
    
    if(strKey.empty())
    {
        return false;
    }
    strKey = "MGET " + strKey;
    redisReply* reply = (redisReply*) redisCommand(m_pContext, strKey.c_str());
    if (!reply) {
        AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
        redisFree(m_pContext);
        m_pContext = NULL;
        return false;
    }
    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }
    
    if(reply->type == REDIS_REPLY_ARRAY)
    {
        for(size_t i=0; i<reply->elements; ++i)
        {
            redisReply* child_reply = reply->element[i];
            if (child_reply->type == REDIS_REPLY_STRING) {
                ret_value[keys[i]] = child_reply->str;
            }
        }
    }
    freeReplyObject(reply);
    return true;
}

string CacheConn::eval(const std::string& script, const std::vector<std::string>& keys, const std::vector<std::string>& args) 
{
    string ret_value;

    if (Init()) {
        return ret_value;
    }

    int argc = 3 + keys.size() + args.size();

    const char** argv = new const char*[argc];
    size_t* argvlen = new size_t[argc];
    if (!argv || !argvlen) 
    {
        delete[] argv;
        delete[] argvlen;
        return ret_value;
    }

    argv[0] = "eval";
    argvlen[0] = strlen(argv[0]);
    argv[1] = script.c_str();
    argvlen[1] = script.size();

    std::string keysCount = std::to_string(keys.size());
    const char* keysCountPtr = keysCount.c_str();
    argv[2] = keysCountPtr;
    argvlen[2] = keysCount.size();

    int i = 3;
    for (const auto& key : keys) 
    {
        argv[i] = key.c_str();
        argvlen[i] = key.size();
        i++;
    }

    for (const auto& arg : args) 
    {
        argv[i] = arg.c_str();
        argvlen[i] = arg.size();
        i++;
    }

    redisReply* reply = (redisReply*)redisCommandArgv(m_pContext, argc, (const char**)argv, (const size_t*)argvlen);
    if (!reply) 
    {
        AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
        delete[] argv;
        delete[] argvlen;

        redisFree(m_pContext);
        m_pContext = NULL;
        return ret_value;
    }

    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }

    if (REDIS_REPLY_STRING == reply->type) 
    {
        ret_value.append(reply->str, reply->len);
    }

    delete[] argv;
    delete[] argvlen;
    freeReplyObject(reply);
    return ret_value;
}

// 返回array
bool CacheConn::eval(const std::string& script, const std::vector<std::string>& keys, const std::vector<std::string>& args, std::map<string, string>& ret_value)
{
    if (Init()) {  
        return false;
    }

    int argc = 3 + keys.size() + args.size();

    const char** argv = new const char*[argc];
    size_t* argvlen = new size_t[argc];
    if (!argv || !argvlen) 
    {
        delete[] argv;
        delete[] argvlen;

        return false;
    }

    argv[0] = "eval";
    argvlen[0] = strlen(argv[0]);
    argv[1] = script.c_str();
    argvlen[1] = script.size();

    std::string keysCount = std::to_string(keys.size());
    const char* keysCountPtr = keysCount.c_str();
    argv[2] = keysCountPtr;
    argvlen[2] = keysCount.size();

    int i = 3;
    for (const auto& key : keys) 
    {
        argv[i] = key.c_str();
        argvlen[i] = key.size();
        i++;
    }

    for (const auto& arg : args) 
    {
        argv[i] = arg.c_str();
        argvlen[i] = arg.size();
        i++;
    }

    redisReply* reply = (redisReply*)redisCommandArgv(m_pContext, argc, (const char**)argv, (const size_t*)argvlen);
    if (!reply) 
    {
        AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
        delete[] argv;
        delete[] argvlen;

        redisFree(m_pContext);
        m_pContext = NULL;
        return false;
    }

    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }

    if ( (reply->type == REDIS_REPLY_ARRAY) && (reply->elements % 2 == 0) ) {
        for (size_t i = 0; i < reply->elements; i += 2) {
            redisReply* field_reply = reply->element[i];
            redisReply* value_reply = reply->element[i + 1];

            string field(field_reply->str, field_reply->len);
            string value(value_reply->str, value_reply->len);

            ret_value.insert(make_pair(field, value));
        }
    }

    delete[] argv;
    delete[] argvlen;
    freeReplyObject(reply);

    return true;
    }

bool CacheConn::isExists(const string &key)
{
    if (Init()) {
        return false;
    }
    
    redisReply* reply = (redisReply*) redisCommand(m_pContext, "EXISTS %s", key.c_str());
    if(!reply)
    {
        AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
        redisFree(m_pContext);
        m_pContext = NULL;
        return false;
    }
    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }
    
    long ret_value = reply->integer;
    freeReplyObject(reply);
    if(0 == ret_value)
    {
        return false;
    }
    else
    {
        return true;
    }
}
long CacheConn::hdel(string key, string field)
{
	if (Init()) {
		return 0;
	}

	redisReply* reply = (redisReply *)redisCommand(m_pContext, "HDEL %s %s", key.c_str(), field.c_str());
	if (!reply) {
		AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
		redisFree(m_pContext);
		m_pContext = NULL;
		return 0;
	}
    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }

	long ret_value = reply->integer;
	freeReplyObject(reply);
	return ret_value;
}

string CacheConn::hget(string key, string field)
{
	string ret_value;
	if (Init()) {
		return ret_value;
	}

	redisReply* reply = (redisReply *)redisCommand(m_pContext, "HGET %s %s", key.c_str(), field.c_str());
	if (!reply) {
        AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
		redisFree(m_pContext);
		m_pContext = NULL;
		return ret_value;
	}
    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }

	if (reply->type == REDIS_REPLY_STRING) {
		ret_value.append(reply->str, reply->len);
	}

	freeReplyObject(reply);
	return ret_value;
}

bool CacheConn::hgetAll(string key, map<string, string>& ret_value)
{
	if (Init()) {
		return false;
	}

	redisReply* reply = (redisReply *)redisCommand(m_pContext, "HGETALL %s", key.c_str());
	if (!reply) {
		AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
		redisFree(m_pContext);
		m_pContext = NULL;
		return false;
	}
    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }

	if ( (reply->type == REDIS_REPLY_ARRAY) && (reply->elements % 2 == 0) ) {
		for (size_t i = 0; i < reply->elements; i += 2) {
			redisReply* field_reply = reply->element[i];
			redisReply* value_reply = reply->element[i + 1];

			string field(field_reply->str, field_reply->len);
			string value(value_reply->str, value_reply->len);
			ret_value.insert(make_pair(field, value));
		}
	}

	freeReplyObject(reply);
	return true;
}

//返回-1,表示插入错误
long CacheConn::hset(string key, string field, string value)
{
	if (Init()) {
		return -1;
	}

	redisReply* reply = (redisReply *)redisCommand(m_pContext, "HSET %s %s %s", key.c_str(), field.c_str(), value.c_str());
	if (!reply) {
		AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
		redisFree(m_pContext);
		m_pContext = NULL;
		return -1;
	}
    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }

	long ret_value = reply->integer;
	freeReplyObject(reply);
	return ret_value;
}

long CacheConn::expire(string key, int second)
{
	if (Init()) {
		return -1;
	}

	redisReply* reply = (redisReply *)redisCommand(m_pContext, "EXPIRE %s %d", key.c_str(), second);
	if (!reply) {
		AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
		redisFree(m_pContext);
		m_pContext = NULL;
		return -1;
	}
    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }

	long ret_value = reply->integer;
	freeReplyObject(reply);
	return ret_value;
}

long CacheConn::hincrBy(string key, string field, long value)
{
	if (Init()) {
		return -1;
	}

	redisReply* reply = (redisReply *)redisCommand(m_pContext, "HINCRBY %s %s %ld", key.c_str(), field.c_str(), value);
	if (!reply) {
		AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
		redisFree(m_pContext);
		m_pContext = NULL;
		return -1;
	}
    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }

	long ret_value = reply->integer;
	freeReplyObject(reply);
	return ret_value;
}

long CacheConn::incrBy(string key, long value)
{
    if(Init())
    {
        return -1;
    }
    
    redisReply* reply = (redisReply*)redisCommand(m_pContext, "INCRBY %s %ld", key.c_str(), value);
    if(!reply)
    {
        AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
        redisFree(m_pContext);
        m_pContext = NULL;
        return -1;
    }
    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }
    
    long ret_value = reply->integer;
    freeReplyObject(reply);
    return ret_value;
}

string CacheConn::hmset(string key, map<string, string>& hash)
{
	string ret_value;

	if (Init()) {
		return ret_value;
	}

	int argc = hash.size() * 2 + 2;
	const char** argv = new const char* [argc];
	if (!argv) {
		return ret_value;
	}

	argv[0] = "HMSET";
	argv[1] = key.c_str();
	int i = 2;
	for (map<string, string>::iterator it = hash.begin(); it != hash.end(); it++) {
		argv[i++] = it->first.c_str();
		argv[i++] = it->second.c_str();
	}

	redisReply* reply = (redisReply *)redisCommandArgv(m_pContext, argc, argv, NULL);
	if (!reply) {
		AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
		delete [] argv;

		redisFree(m_pContext);
		m_pContext = NULL;
		return ret_value;
	}
    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }

	ret_value.append(reply->str, reply->len);

	delete [] argv;
	freeReplyObject(reply);
	return ret_value;
}
//返回指定key的hash集指定的一个或个字段的值
bool CacheConn::hmget(string key, list<string>& fields, list<string>& ret_value)
{
	if (Init()) {
		return false;
	}

	int argc = fields.size() + 2;
	const char** argv = new const char* [argc];
	if (!argv) {
		return false;
	}

	argv[0] = "HMGET";
	argv[1] = key.c_str();
	int i = 2;
	for (list<string>::iterator it = fields.begin(); it != fields.end(); it++) {
		argv[i++] = it->c_str();
	}

	redisReply* reply = (redisReply *)redisCommandArgv(m_pContext, argc, (const char**)argv, NULL);
	if (!reply) {
		AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
		delete [] argv;

		redisFree(m_pContext);
		m_pContext = NULL;

		return false;
	}
    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }

	if (reply->type == REDIS_REPLY_ARRAY) {
		for (size_t i = 0; i < reply->elements; i++) {
			redisReply* value_reply = reply->element[i];
			string value(value_reply->str, value_reply->len);
			ret_value.push_back(value);
		}
	}

	delete [] argv;
	freeReplyObject(reply);
	return true;
}

long CacheConn::incr(string key)
{
    if(Init())
    {
        return -1;
    }
    
    redisReply* reply = (redisReply*)redisCommand(m_pContext, "INCR %s", key.c_str());
    if(!reply)
    {
        AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
        redisFree(m_pContext);
        m_pContext = NULL;
        return -1;
    }
    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }
    
    long ret_value = reply->integer;
    freeReplyObject(reply);
    return ret_value;
}

long CacheConn::decr(string key)
{
    if(Init())
    {
        return -1;
    }
    
    redisReply* reply = (redisReply*)redisCommand(m_pContext, "DECR %s", key.c_str());
    if(!reply)
    {
        AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
        redisFree(m_pContext);
        m_pContext = NULL;
        return -1;
    }
    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }
    
    long ret_value = reply->integer;
    freeReplyObject(reply);
    return ret_value;
}

long CacheConn::lpush(string key, string value)
{
	if (Init()) {
		return -1;
	}

	redisReply* reply = (redisReply *)redisCommand(m_pContext, "LPUSH %s %s", key.c_str(), value.c_str());
	if (!reply) {
		AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
		redisFree(m_pContext);
		m_pContext = NULL;
		return -1;
	}
    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }

	long ret_value = reply->integer;
	freeReplyObject(reply);
	return ret_value;
}

long CacheConn::rpush(string key, string value)
{
	if (Init()) {
		return -1;
	}

	redisReply* reply = (redisReply *)redisCommand(m_pContext, "RPUSH %s %s", key.c_str(), value.c_str());
	if (!reply) {
		AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
		redisFree(m_pContext);
		m_pContext = NULL;
		return -1;
	}
	
    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }

	long ret_value = reply->integer;
	freeReplyObject(reply);
	return ret_value;
}

long CacheConn::llen(string key)
{
	if (Init()) {
		return -1;
	}

	redisReply* reply = (redisReply *)redisCommand(m_pContext, "LLEN %s", key.c_str());
	if (!reply) {
		AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
		redisFree(m_pContext);
		m_pContext = NULL;
		return -1;
	}
    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }

	long ret_value = reply->integer;
	freeReplyObject(reply);
	return ret_value;
}

bool CacheConn::lrange(string key, long start, long end, list<string>& ret_value)
{
	if (Init()) {
		return false;
	}

	redisReply* reply = (redisReply *)redisCommand(m_pContext, "LRANGE %s %d %d", key.c_str(), start, end);
	if (!reply) {
		AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
		redisFree(m_pContext);
		m_pContext = NULL;
		return false;
	}
    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }

	if (reply->type == REDIS_REPLY_ARRAY) {
		for (size_t i = 0; i < reply->elements; i++) {
			redisReply* value_reply = reply->element[i];
			string value(value_reply->str, value_reply->len);
			ret_value.push_back(value);
		}
	}

	freeReplyObject(reply);
	return true;
}

long CacheConn::sadd(const string& key, const string& value)
{
	if (Init()) {
		return -1;
	}

	redisReply* reply = (redisReply *)redisCommand(m_pContext, "sadd %s %s", key.c_str(), value.c_str());
	if (!reply) {
		AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
		redisFree(m_pContext);
		m_pContext = NULL;
		return -1;
	}
    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }

	long ret_value = reply->integer;
	freeReplyObject(reply);
	return ret_value;
}

long CacheConn::srem(const string& key, const string& value)
{
	if (Init()) {
		return -1;
	}

	redisReply* reply = (redisReply *)redisCommand(m_pContext, "srem %s %s", key.c_str(), value.c_str());
	if (!reply) {
		AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
		redisFree(m_pContext);
		m_pContext = NULL;
		return -1;
	}
    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }

	long ret_value = reply->integer;
	freeReplyObject(reply);
	return ret_value;

}
bool CacheConn::smembers(const string& key, std::set<string>& ret_value)
{
	if (Init()) {
		return false;
	}

	redisReply* reply = (redisReply *)redisCommand(m_pContext, "smembers %s", key.c_str());
	if (!reply) {
		AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
		redisFree(m_pContext);
		m_pContext = NULL;
		return false;
	}
    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }

	if (reply->type == REDIS_REPLY_ARRAY) {
		for (size_t i = 0; i < reply->elements; i++) {
			redisReply* value_reply = reply->element[i];
			string value(value_reply->str, value_reply->len);
			ret_value.insert(value);
		}
	}

	freeReplyObject(reply);
	return true;

}


bool CacheConn::sinter(const string& key1, const string& key2, list<string>& ret_value)
{
	if (Init()) {
		return false;
	}

	redisReply* reply = (redisReply *)redisCommand(m_pContext, "sinter %s %s", key1.c_str(), key2.c_str());
	if (!reply) {
		AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
		redisFree(m_pContext);
		m_pContext = NULL;
		return false;
	}
    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }

	if (reply->type == REDIS_REPLY_ARRAY) {
		for (size_t i = 0; i < reply->elements; i++) {
			redisReply* value_reply = reply->element[i];
			string value(value_reply->str, value_reply->len);
			ret_value.push_back(value);
		}
	}

	freeReplyObject(reply);
	return true;
}

string CacheConn::zscore(const std::string& key, const std::string& member)
{
    string ret_value;
    if (Init()) {
        return ret_value;
    }

    redisReply* reply = (redisReply *)redisCommand(m_pContext, "zscore %s %s", key.c_str(), member.c_str());
    if (!reply) {
        AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
        redisFree(m_pContext);
        m_pContext = NULL;
        return ret_value;
    }
    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }

    if (reply->type == REDIS_REPLY_STRING) {
        ret_value.append(reply->str, reply->len);
    }

    freeReplyObject(reply);
    return ret_value;
}

long CacheConn::zadd(const string& key, const string& score, const string& member)
{
    if (Init()) {
        return -1;
    }

    redisReply* reply = (redisReply *)redisCommand(m_pContext, "zadd %s %s %s", key.c_str(), score.c_str(), member.c_str());
    if (!reply) {
        AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
        redisFree(m_pContext);
        m_pContext = NULL;
        return -1;
    }
    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }

    long ret_value = reply->integer;
    freeReplyObject(reply);
    return ret_value;
}

long CacheConn::zrem(const string& key, const string& member)
{
    if (Init()) {
        return -1;
    }

    redisReply* reply = (redisReply *)redisCommand(m_pContext, "zrem %s %s", key.c_str(), member.c_str());
    if (!reply) {
        AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
        redisFree(m_pContext);
        m_pContext = NULL;
        return -1;
    }
    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }

    long ret_value = reply->integer;
    freeReplyObject(reply);
    return ret_value;
}

std::vector<std::string> CacheConn::zrangebyscore(const std::string& key, uint64_t min, uint64_t max)
{
    std::vector<std::string> result;

    if (Init()) {
        return result;
    }

    redisReply* reply = (redisReply *)redisCommand(m_pContext, "ZRANGEBYSCORE %s %llu %llu", key.c_str(), min, max);
    if (!reply) {
        AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
        redisFree(m_pContext);
        m_pContext = NULL;
        return result;
    }

    // 检查返回类型是否为数组
    if (reply->type == REDIS_REPLY_ARRAY) 
    {
        for (size_t i = 0; i < reply->elements; i++) 
        {
            redisReply* element = reply->element[i];
            if (element->type == REDIS_REPLY_STRING) 
            {
                result.push_back(element->str);  // 将每个元素的字符串部分添加到结果向量中
            } 
            else 
            {
                AK_LOG_WARN << "Unexpected element type in ZRANGEBYSCORE result: " << element->type;
            }
       }
    } 
    else if (reply->type == REDIS_REPLY_ERROR) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    } 
    else 
    {
        AK_LOG_WARN << "Unexpected reply type: " << reply->type;
    }

    // 释放Redis回复对象
    freeReplyObject(reply);
    return result;
}


bool CacheConn::flushdb()
{
	if (Init()) {
		return false;
	}

	redisReply* reply = (redisReply *)redisCommand(m_pContext, "flushdb");
	if (!reply) {
        AK_LOG_WARN << "redisCommand failed: " << m_pContext->errstr << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
		redisFree(m_pContext);
		m_pContext = NULL;
		return false;
	}
    if (REDIS_REPLY_ERROR == reply->type) 
    {
        AK_LOG_WARN << "redisCommand failed: " << reply->str << " " << m_pCachePool->GetServerIP() << ":" << m_pCachePool->GetServerPort();
    }

	freeReplyObject(reply);
	return true;
}

///////////////
CachePool::CachePool(const char* pool_name, const char* server_ip, int server_port, int db_num, int init_conn_cnt, int is_master)
{
	m_pool_name = pool_name;
	m_server_ip = server_ip;
	m_server_port = server_port;
	m_db_num = db_num;
	m_max_conn_cnt = MAX_CACHE_CONN_CNT;
    init_conn_cnt_ = init_conn_cnt;
	m_cur_conn_cnt = init_conn_cnt;
	m_is_conn_to_master = is_master;
}

CachePool::~CachePool()
{
	m_free_notify.Lock();
	for (list<CacheConn*>::iterator it = m_free_list.begin(); it != m_free_list.end(); it++) {
		CacheConn* pConn = *it;
		delete pConn;
	}

	m_free_list.clear();
	m_cur_conn_cnt = 0;
	m_free_notify.Unlock();
}

int CachePool::Init()
{
	for (int i = 0; i < m_cur_conn_cnt; i++) {
		CacheConn* pConn = new CacheConn(this);
		pConn->setErrorCallback(std::bind(&CachePool::onErrorCallback, this, std::placeholders::_1));
		if (pConn->Init()) { //进行redis的连接
			delete pConn;
			return 1;
		}
		pConn->conn_statu_ = CacheConn::CACHE_CONN_STATU_OK;
        m_all_list.push_back(pConn);
		m_free_list.push_back(pConn);
	}
	//AK_LOG_INFO << "cache pool:" << m_pool_name.c_str() <<  " list size:" << m_free_list.size();
	return 0;
}

CacheConn* CachePool::GetCacheConn()
{
	m_free_notify.Lock();

	while (m_free_list.empty()) {
        //超过最大连接池限额，则等待
		if (m_cur_conn_cnt >= m_max_conn_cnt) {
			m_free_notify.Wait();
		} 
        //如果没超过最大连接池限额，就可以动态扩展
        else {
			CacheConn* pCacheConn = new CacheConn(this);
			int ret = pCacheConn->Init();
			if (ret) {
				AK_LOG_WARN << "Init CacheConn failed";
				delete pCacheConn;
				m_free_notify.Unlock();
				return nullptr;
			} else {
			    pCacheConn->conn_statu_ = CacheConn::CACHE_CONN_STATU_OK;
			    m_all_list.push_back(pCacheConn);
				m_free_list.push_back(pCacheConn);
				m_cur_conn_cnt++;
				AK_LOG_INFO << "new cache connection:" << m_pool_name.c_str() << " conn_cnt: " << m_cur_conn_cnt;
			}
		}
	}

	CacheConn* pConn = m_free_list.front();
	m_free_list.pop_front();
	//删除掉不可以用的连接
	while (pConn && pConn->conn_statu_ == CacheConn::CACHE_CONN_STATU_ERROR)
	{
        for (list<CacheConn *>::iterator it = m_all_list.begin(); it != m_all_list.end(); it++)
        {
            if (pConn == *it)
            {
                AK_LOG_INFO << "because of reconnet, delete all  error connect";
                m_all_list.erase(it++);
                break;
            }
        }       

        delete pConn;
        //置为null 避免悬空指针被使用
        pConn = nullptr;
        if (m_free_list.size() == 0)
        {
            break;
        }        
        pConn = m_free_list.front();
        m_free_list.pop_front();
	}

	m_free_notify.Unlock();
	return pConn;
}

void CachePool::RelCacheConn(CacheConn* pCacheConn)
{
	assert(pCacheConn);
    m_free_notify.Lock();

	list<CacheConn*>::iterator it = m_free_list.begin();
	for (; it != m_free_list.end(); it++) {
		if (*it == pCacheConn) {
			break;
		}
	}

	if (it == m_free_list.end()) {
	    m_free_list.push_back(pCacheConn);
	}

	m_free_notify.Signal();
	m_free_notify.Unlock();
}

void CachePool::DelAllCacheConn()
{
    //当有连接错误时候，那么标识所有的连接都已经不可以用。
    //这里不能直接删除，因为连接可能已经被申请了，只能等到下次分配时候清空掉。
    for (list<CacheConn*>::iterator it = m_all_list.begin(); it != m_all_list.end(); it++) {
        CacheConn* pConn = *it;
        pConn->conn_statu_ = CacheConn::CACHE_CONN_STATU_ERROR;
    }
    m_cur_conn_cnt = init_conn_cnt_;

}

int CachePool::CheckCachePool()
{
    if(m_cur_conn_cnt >= m_max_conn_cnt)
    {
        //电话告警
        return (int)MetricSeverityLevel::ERROR;
    }
    //80%
    else if(m_cur_conn_cnt >= (m_max_conn_cnt*8/10)) 
    {
        //高水位邮件告警
        return (int)MetricSeverityLevel::WARNING;
    }
    return (int)MetricSeverityLevel::NORMAL;
}


///////////
CacheManager::CacheManager()
{

}

CacheManager::~CacheManager()
{

}

CacheManager* CacheManager::getInstance()
{
	if (!s_cache_manager) {
		s_cache_manager = new CacheManager();
	}

	return s_cache_manager;
}

//修改下,配置文件的实际地址、缓存的名称通过参数传递过去
int CacheManager::Init(const string &conf_file, const string &cache_instance)
{
    enable_sentinel_ = false;
	CConfigFileReader config_file(conf_file.c_str());
	const char* sentinels = config_file.GetConfigName("sentinels");
	if (strlen(sentinels) > 1)
	{
        //获取sentinel地址列表
    	CStrExplode sentinels_name(sentinels, ',');
    	for (uint32_t i = 0; i < sentinels_name.GetItemCnt(); i++)
    	{
    	    if (::strlen(sentinels_name.GetItem(i)) > 1)
    	    {
                sentinels_addr_.push_back(sentinels_name.GetItem(i));
    	    }
    	}
    	if (sentinels_addr_.size() > 0)
    	{
            enable_sentinel_ = true;
    	}    
	}

	const char* cache_instances = config_file.GetConfigName(cache_instance.c_str());
	if (!cache_instances)
	{
		AK_LOG_FATAL << "not configure " << cache_instance << " CacheIntance";
		return -1;
	}

	char db[64];
	char host[64];
	char port[64];
    char maxconncnt[64];
    char is_master[64];
	CStrExplode instances_name(cache_instances, ',');
    //初始化库对应的连接信息。连接主还是从的redis实例、连接数
	for (uint32_t i = 0; i < instances_name.GetItemCnt(); i++)
	{
		char* pool_name = instances_name.GetItem(i);
		snprintf(db, 64, "%s_db", pool_name);
        snprintf(maxconncnt, 64, "%s_maxconncnt", pool_name);
        snprintf(is_master, 64, "%s_conn_master", pool_name);
		snprintf(host, 64, "%s_host", pool_name);
		snprintf(port, 64, "%s_port", pool_name);

		const char* cache_host = config_file.GetConfigName(host);
		const char* str_cache_port = config_file.GetConfigName(port);
		const char* str_cache_db = config_file.GetConfigName(db);
        const char* str_max_conn_cnt = config_file.GetConfigName(maxconncnt);
        const char* str_is_master = config_file.GetConfigName(is_master);
        if (str_is_master && strlen(str_is_master) == 0) 
        {
            str_is_master = "1";
        }
		if (!str_is_master || !str_cache_db || !str_max_conn_cnt)
		{
			AK_LOG_FATAL <<  "not configure cache instance: " << pool_name;
			return -1;
		}
        CacheConnectInfo info;
		memset(&info, 0, sizeof(info));
        info.is_master = ATOI(str_is_master);
        info.max_conn_cnt = ATOI(str_max_conn_cnt);
        info.cache_db = ATOI(str_cache_db);
        info.port = ATOI(str_cache_port);
        snprintf(info.pool_name, sizeof(info.pool_name), "%s", pool_name);
        snprintf(info.host, sizeof(info.host), "%s", cache_host);
        conns_info_.push_back(info);
	}
    if (enable_sentinel_)
    {
        getSentinelManagerInstance()->Init(sentinels_addr_);
        getSentinelManagerInstance()->setMasterChangeCallback(std::bind(&CacheManager::MasterChange, this, std::placeholders::_1));
        getSentinelManagerInstance()->startSentinelSub();
    }
    //连接redis
	for (const auto&conn: conns_info_)
	{
        std::pair<std::string, int> rds_addr;
        if (enable_sentinel_)
        {
            int ret = getRedisInstanceAddr(conn.is_master, rds_addr);
    	    if (ret != 0)
    	    {
    	        AK_LOG_FATAL <<  "error can get redis instance. ";
                return -1;
    	    }
        }
        else
        {
            rds_addr = std::make_pair(conn.host, conn.port);
        }
	    AK_LOG_INFO <<  "redis poolname: " << conn.pool_name << " conn:" << rds_addr.first << ":" << rds_addr.second << ". connect master=" << conn.is_master << " db number=" << conn.cache_db;
		CachePool* p_cache_pool = new CachePool(conn.pool_name, rds_addr.first.c_str(), rds_addr.second,
				conn.cache_db, conn.max_conn_cnt, conn.is_master);
	    p_cache_pool->setErrorCallback(std::bind(&CacheManager::OnCachePoolError, this, std::placeholders::_1));
		if (p_cache_pool->Init())
		{
			AK_LOG_FATAL << "Init cache pool failed";
			return -1;
		}
		m_cache_pool_map.insert(std::make_pair(conn.pool_name, p_cache_pool));
	}
    redis_stat_ = true;

    //初始化metric指标
    MetricService* metric_service = MetricService::GetInstance();
    if(metric_service) {
        std::string metric_name = cache_instance;
        metric_name += "_redis_pool_check_error";
        metric_service->AddMetric(
            metric_name,
            "redis pool status",
            metric_name,
            []() -> long { return (long)(CacheManager::getInstance()->CheckCachePool()); }
        );
    }

	return 0;
}

int CacheManager::CheckCachePool()
{
    for (const auto&conn: conns_info_)
    {
        map<string, CachePool*>::iterator it = m_cache_pool_map.find(conn.pool_name);
        if (it != m_cache_pool_map.end())
        {
            int status = it->second->CheckCachePool();
            if(status != 0)
            {
                return status;
            }
        }
    }
    return 0;
}

CacheConn* CacheManager::GetCacheConn(const char* pool_name)
{
	map<string, CachePool*>::iterator it = m_cache_pool_map.find(pool_name);
	if (it != m_cache_pool_map.end()) {
		return it->second->GetCacheConn();
	} else {
		return NULL;
	}
}

void CacheManager::RelCacheConn(CacheConn* pCacheConn)
{
	if (!pCacheConn) {
		return;
	}

	map<string, CachePool*>::iterator it = m_cache_pool_map.find(pCacheConn->GetPoolName());
	if (it != m_cache_pool_map.end()) {
        it->second->RelCacheConn(pCacheConn);
	}
	return;
}

int CacheManager::getRedisInstanceAddr(int is_master, IpAddrInfo &rds_addr)
{
    int ret = 0;
    if (is_master)
    {
        int try_times = 3;
        while ((ret=getSentinelManagerInstance()->getRedisMasterInstance(rds_addr)) && try_times) 
        {
            AK_LOG_WARN <<  "waiting sentinel know the master redis instance!";
            try_times--;
            usleep(1000 * 100);
        }
    }
    else
    {
        ret=getSentinelManagerInstance()->getRedisSlaveInstance(rds_addr);
        if (ret != 0)
        {
            AK_LOG_WARN <<  "cannot get slave redis,try get master redis to use!";
            //如果从的redis获取不到，那么可以用主的redis。
            int try_times = 3;
            while ((ret=getSentinelManagerInstance()->getRedisMasterInstance(rds_addr)) && try_times) 
            {
                AK_LOG_WARN <<  "waiting sentinel know the master redis instance!";
                try_times--;
                usleep(1000 * 100);
            }
        }	        

    }   
    return ret;	    
}


//TODO:chenzhx，如果redis挂掉了，没有马上起来，或者在主从切换时候，主机没有那么快起来。
//那么需要另外起线程尝试，不能阻塞调用者。
void CacheManager::ReInitRedisCachePool()
{
    //分两种情况：
    //1、redis挂掉马上重启完成//已经在conn位置处理了
    //2、redis挂掉了，主从切换，需要等到从新选举新的主redis。时间默认是30秒，这时候应该返回连接不可用给调用者
    //然后每次使用redis时候都会判断是否已经30秒过去了。
    for (const auto&pool : m_cache_pool_map)
    {
        for (const auto&conn: conns_info_) 
        {
            if (!strcmp(conn.pool_name, pool.first.c_str()))
            {
                std::pair<std::string, int> rds_addr;
                if (enable_sentinel_)
                {
                    int ret = getRedisInstanceAddr(conn.is_master, rds_addr);
                    if (ret != 0)
                    {
                        AK_LOG_FATAL <<  "error can get redis instance. ";
                        return;
                    }
                }
                else
                {
                    rds_addr = std::make_pair(conn.host, conn.port);
                }

                
                AK_LOG_INFO <<  "reconnect redis poolname: " << conn.pool_name << " conn:" << rds_addr.first << ":" << rds_addr.second << ". connect master=" << conn.is_master << " db number=" << conn.cache_db;                
                pool.second->ReInitPoolAddrInfo(rds_addr.first.c_str(), rds_addr.second);
                if (pool.second->Init())
                {
                    AK_LOG_WARN <<  "cache pool init faile!";
                }
                else
                {
                    //重新初始化成功
                    redis_stat_ = true;
                }
                break;
            } 
        }
    }


}

void CacheManager::MasterChange(int code)
{
	(void)code; //warning: unused parameter
    AK_LOG_INFO << "Master Slave Switch!!!!!!";
    OnCachePoolError(SENTINEL_NOTIFY_MASTER_SLAVE_SWITCH);
}


void CacheManager::OnCachePoolError(int code)
{
    AK_LOG_INFO << "redis connect error! 0.translate master/slave for sentinel. 1.connect timeout 30s. 2.master/slave conn attribute error,error code=" << code;
    for (const auto&pool : m_cache_pool_map)
    {
        pool.second->DelAllCacheConn();
    }
    redis_stat_ = false;
    ReInitRedisCachePool();
}

bool CacheManager::CheckRedisNormal()
{
    return redis_stat_;
}

/*当外部引用redis组件，但是没有初始化glog时候，调用这个初始化*/
void redisGlogInit(const char* name, char *filepath)
{
	std::string mkdir="mkdir -p ";
	mkdir += filepath;
	system(mkdir.c_str());

	std::string info=filepath;
	info += "/INFO";
	std::string warn=filepath;
	warn += "/WARN";
	std::string error=filepath;
	error += "/ERROR";
	std::string fatal=filepath;
	fatal += "/FATAL";	

	google::InitGoogleLogging(name);
    google::SetLogDestination(google::GLOG_INFO,  info.c_str());
    google::SetLogDestination(google::GLOG_WARNING, warn.c_str());
    google::SetLogDestination(google::GLOG_ERROR, error.c_str());
    google::SetLogDestination(google::GLOG_FATAL, fatal.c_str());
	FLAGS_logbufsecs = 0;
    google::SetStderrLogging(google::GLOG_WARNING); // 输出到标准输出的时候大于INFO级别的都输出;
    FLAGS_max_log_size = 50;    //单日志文件最大50M
}
