/*
 * CachePool.h
 * Created on: 2017-12-25
 * Author: chenyc
 */

#ifndef CACHEPOOL_H_
#define CACHEPOOL_H_

#include <set>
#include <vector>
#include <functional>
#include "util.h"
#include "Sentinel.h"
#include "RedisUtil.h"
#include "AkLogging.h"
#include "ThreadPool.h"
#include "hiredis/hiredis.h"

class CachePool;
class CacheManager;
class CThreadNotify;

class CacheConn {
public:
enum {
    CACHE_CONN_STATU_OK,
    CACHE_CONN_STATU_ERROR //连接已经错误
};

public:
	CacheConn(CachePool* pCachePool);
	virtual ~CacheConn();

	void setErrorCallback(const ErrorCallback &cb)
	{
        conn_error_cb_ = cb;
	}

	int Init();
	const char* GetPoolName();
    //字符串数据结构
	string get(string key);
	string setex(string key, int timeout, string value); //setnx则是实现分布式锁的重点
    string set(const string &key, const string& value);
    string eval(const std::string& script, const std::vector<std::string>& keys, const std::vector<std::string>& args);
    bool eval(const std::string& script, const std::vector<std::string>& keys, const std::vector<std::string>& args, map<string, string>& ret_value);
    long del(string key);
    
    //批量获取
    bool mget(const vector<string>& keys, map<string, string>& ret_value);
    // 判断一个key是否存在
    bool isExists(const string &key);

	// Redis hash structure
	//哈希表中的字段操作,key是哈希表名, filed-value是内容,相当于key->map<>的结构
	long hdel(string key, string field);
	string hget(string key, string field);
	bool hgetAll(string key, map<string, string>& ret_value);
	long hset(string key, string field, string value);

    /*设置second秒后自动清除key*/
    long expire(string key, int second);

	long hincrBy(string key, string field, long value);
    long incrBy(string key, long value);
	string hmset(string key, map<string, string>& hash);
	bool hmget(string key, list<string>& fields, list<string>& ret_value);
    
    //原子加减1
    long incr(string key);
    long decr(string key);

	// Redis list structure
	long lpush(string key, string value);
	long rpush(string key, string value);
	long llen(string key);
	bool lrange(string key, long start, long end, list<string>& ret_value);
    //缺少set的相关操作,只能自己封装
    long sadd(const string& key, const string& value);
    long srem(const string& key, const string& value);
    bool smembers(const string& key, std::set<string>& ret_value);
    bool sinter(const string& key1, const string& key2, list<string>& ret_value);
    bool flushdb();
    string zscore(const std::string& key, const std::string& member);
    long zadd(const string& key, const string& score, const string& member);
    long zrem(const string& key, const string& member);
    std::vector<std::string> zrangebyscore(const std::string& key, uint64_t min, uint64_t max);
        
    int conn_statu_;    
private:
    CachePool* 		m_pCachePool;
    redisContext* 	m_pContext;
    uint64_t		m_last_connect_time;
    uint64_t		m_first_connect_time;
    int             m_is_first_connect_error;
    ErrorCallback conn_error_cb_;

private:
    void *CacheCommand(redisContext *c, const char *format, ...);
    void *CacheCommandArgv(redisContext *c, int argc, const char **argv, const size_t *argvlen);

};

class CachePool {

enum {
    CACHE_POOL_STATU_IDLE,
    CACHE_POOL_STATU_INIT,
    CACHE_POOL_STATU_CLEAN,
    CACHE_POOL_STATU_OK
};

public:
	CachePool(const char* pool_name, const char* server_ip, int server_port, int db_num, int max_conn_cnt, int is_conn_to_master);
	virtual ~CachePool();

	int Init();
	//从新初始化pool的配置
	int ReInitPoolAddrInfo(const char* server_ip, int server_port)
	{
        m_server_ip = server_ip;
        m_server_port = server_port;
		return 0;
	}
	void setErrorCallback(const ErrorCallback &cb)
	{
	    //cb=OnCachePoolError
        pool_error_cb_ = cb;
	}
    void onErrorCallback(int code)
    {
        pool_error_cb_(code);
    }
	CacheConn* GetCacheConn();
	void RelCacheConn(CacheConn* pCacheConn);
	void DelAllCacheConn();
	int CheckCachePool();

	const char* GetPoolName() { return m_pool_name.c_str(); }
	const char* GetServerIP() { return m_server_ip.c_str(); }
	int GetServerPort() { return m_server_port; }
	int GetDBNum() { return m_db_num; }
	int GetIsMaster() { return m_is_conn_to_master; }

private:
	string 		m_pool_name;
	string		m_server_ip;
	int			m_server_port;
	int			m_db_num;
	int         m_is_conn_to_master;

	int			m_cur_conn_cnt;
	int 		m_max_conn_cnt;
    int         init_conn_cnt_;
	list<CacheConn*>	m_free_list;//因为多个线程来访问这个,如果共用的话,造成数据交叉,所以必须每次访问都需要获取-释放的过程
	list<CacheConn*>	m_all_list;//所有的连接列表，在断开重连时候，因为free列表可能已经弹出正在使用，导致数据不全
	CThreadNotify		m_free_notify;
	ErrorCallback pool_error_cb_;
};

class CacheManager {

struct CacheConnectInfo{
    char pool_name[64];
    char host[64];
    int port;
    int max_conn_cnt;
    int is_master;//是否连接到主服务器
    int cache_db;
};

public:
	virtual ~CacheManager();

	static CacheManager* getInstance();

	int Init(const string &conf_file, const string &cache_instance); //初始化redis数据库连接
	CacheConn* GetCacheConn(const char* pool_name);
	void RelCacheConn(CacheConn* pCacheConn);
	void OnCachePoolError(int code);
	void ReInitRedisCachePool();
	void MasterChange(int code);
	bool CheckRedisNormal();
	int CheckCachePool();

private:
	CacheManager();
	int getRedisInstanceAddr(int is_master, IpAddrInfo &ipaddr);

private:
    SentinelManager sentinel_;
    bool enable_sentinel_;
    std::vector<CacheConnectInfo> conns_info_;
    std::vector<std::string> sentinels_addr_;
	static CacheManager* 	s_cache_manager;
	map<string, CachePool*>	m_cache_pool_map;
	bool redis_stat_;
};


void redisGlogInit(const char* name, char *filepath);


#endif /* CACHEPOOL_H_ */
