#ifndef __AK_BASE_PUBSUB_MNG_H__
#define __AK_BASE_PUBSUB_MNG_H__

#include "hiredis/hiredis.h"
#include "hiredis/async.h"
#include <string>

class PubSubManager
{
public:
	PubSubManager(std::string ip, int port);
	virtual ~PubSubManager()
    {
        redisAsyncFree(redis_ctx_);
    }   
    int Init();
    void Start();
    int SubscribeOpt(redisCallbackFn *OnMessageCallback, const std::string channel);
	int UnSubscribe(const std::string channel);
	int Publish(const char *channel, const char *message);
    bool IsConncted() {return connected_ == true;}
	static void RedisCallback(redisAsyncContext *ctx, void *reply, void *priv);

private:
    static void ConnectCallback(const redisAsyncContext *c, int status);
    static void DisconnectCallback(const redisAsyncContext *c, int status);
	void PrintReply( redisReply *reply );
private:
	redisAsyncContext *redis_ctx_;
    std::string redis_ip_;
    int redis_port_;
    static bool connected_; //
    struct event_base *base_;
};

#endif //__AK_BASE_PUBSUB_MNG_H__
