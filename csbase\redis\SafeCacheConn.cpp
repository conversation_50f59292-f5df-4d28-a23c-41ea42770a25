#include "SafeCacheConn.h"

std::string SafeCacheConn::set(const string &key, const string& value) 
{
    std::string ret;
    if (connection_) 
    {
        ret = connection_->set(key, value);
    }
    else
    {
        AK_LOG_WARN << "get redis connection error, set key = " << key << ", value = " << value;    
    }
    return ret;
}

std::string SafeCacheConn::setex(const std::string& key, int timeout, const std::string& value)
{
    std::string ret;
    if (connection_) 
    {
        ret = connection_->setex(key, timeout, value);
    }
    else
    {
        AK_LOG_WARN << "get redis connection error, set key = " << key << ", timeout = " <<  timeout << ", value = " << value;    
    }
    return ret;
}

std::string SafeCacheConn::get(const std::string& key)
{
    std::string ret;
    if (connection_) 
    {
        ret = connection_->get(key);
    }
    else
    {
        AK_LOG_WARN << "get redis connection error, get key = " << key;    
    }
    return ret;
}

long SafeCacheConn::expire(const std::string& key, int second)
{
    long ret = 0;
    if (connection_) 
    {
        ret = connection_->expire(key, second);
    }
    else
    {
        AK_LOG_WARN << "get redis connection error, expire key = " << key << ", second = " << second;    
    }
    return ret;
}

bool SafeCacheConn::isExists(const std::string &key)
{
    bool ret = false;
    if (connection_) 
    {
        ret = connection_->isExists(key);
    }
    else
    {
        AK_LOG_WARN << "get redis connection error, isExists key = " << key;    
    }

    return ret;
}

std::string SafeCacheConn::eval(const std::string& script, const std::vector<std::string>& keys, const std::vector<std::string>& args) 
{
    std::string ret;
    if (connection_) 
    {
        ret = connection_->eval(script, keys, args);
    }
    else
    {
        AK_LOG_WARN << "get redis connection error, eval script = " << script;    
    }
    return ret;
}

bool SafeCacheConn::eval(const std::string& script, const std::vector<std::string>& keys, const std::vector<std::string>& args, map<string, string>& ret_value)
{
    bool ret = false;
    if (connection_) 
    {
        ret = connection_->eval(script, keys, args, ret_value);
    }
    else
    {
        AK_LOG_WARN << "get redis connection error, eval script = " << script;    
    }
    return ret;
}

long SafeCacheConn::del(const std::string& key)
{
    long ret = 0;
    if (connection_) 
    {
        ret = connection_->del(key);
    }
    else
    {
        AK_LOG_WARN << "get redis connection error, del key = " << key;    
    }
    return ret;
}

bool SafeCacheConn::mget(const std::vector<string>& keys, std::map<std::string, std::string>& ret_value)
{
    bool ret = false;
    if (connection_) 
    {
        ret = connection_->mget(keys, ret_value);
    }
    else
    {
        AK_LOG_WARN << "get redis connection error, mget operation";    
    }
    return ret;
}

long SafeCacheConn::hdel(const std::string& key, const std::string& field)
{
    long ret = 0;
    if (connection_) 
    {
        ret = connection_->hdel(key, field);
    }
    else
    {
        AK_LOG_WARN << "get redis connection error, hdel key = " << key << ", field = " << field;    
    }
    return ret;
}

std::string SafeCacheConn::hget(const std::string& key, const std::string& field)
{    
    std::string ret;
    if (connection_) 
    {
        ret = connection_->hget(key, field);
    }
    else
    {
        AK_LOG_WARN << "get redis connection error, hget key = " << key << ", field = " << field;    
    }
    return ret;
}

bool SafeCacheConn::hgetAll(const std::string& key, map<std::string, std::string>& ret_value)
{
    bool ret = false;
    if (connection_) 
    {
        ret = connection_->hgetAll(key, ret_value);
    }
    else
    {
        AK_LOG_WARN << "get redis connection error, hgetAll key = " << key;    
    }
    return ret;
}

long SafeCacheConn::hset(const std::string& key, const std::string& field, const std::string& value)
{
    long ret = 0;
    if (connection_) 
    {
        ret = connection_->hset(key, field, value);
    }
    else
    {
        AK_LOG_WARN << "get redis connection error, hset key = " << key << ", field = " << field << ", value = " << value;    
    }
    return ret;
}

long SafeCacheConn::incr(const std::string& key)
{
    long ret = 0;
    if (connection_) 
    {
        ret = connection_->incr(key);
    }
    else
    {
        AK_LOG_WARN << "get redis connection error, incr key = " << key;    
    }
    return ret;
}

bool SafeCacheConn::flushdb()
{
    bool ret = false;
    if (connection_) 
    {
        ret = connection_->flushdb();
    }
    else
    {
        AK_LOG_WARN << "get redis connection error, flushdb";    
    }
    return ret;
}

std::string SafeCacheConn::zscore(const std::string& key, const std::string& member)
{
    std::string ret;
    if (connection_) 
    {
        ret = connection_->zscore(key, member);
    }
    else
    {
        AK_LOG_WARN << "get redis connection error, zscore key = " << key << ", member = " << member;    
    }
    return ret;
}

// 成功 返回1, 失败返回 0
long SafeCacheConn::zadd(const std::string& key, const std::string& score, const std::string& member)
{
    if (connection_) 
    {
        return connection_->zadd(key, score, member);
    }
    
    AK_LOG_WARN << "get redis connection error, zadd key = " << key << ", score = " << score << ", member = " << member;    
    return -1;
}

// 成功 返回1, 失败 返回0
long SafeCacheConn::zrem(const std::string& key, const std::string& member)
{
    if (connection_) 
    {
        return connection_->zrem(key, member);
    }
    
    AK_LOG_WARN << "get redis connection error, zrem key = " << key << ", member = " << member;    
    return -1;
}

std::vector<std::string> SafeCacheConn::zrangebyscore(const std::string& key, uint64_t min, uint64_t max)
{
    std::vector<std::string> result;
    if (connection_) 
    {
        result = connection_->zrangebyscore(key, min, max);
    }
    
    return result;
}
