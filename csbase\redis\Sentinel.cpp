/*
 * Sentinel.cpp
 * Created on: 2019-07-10
 * Author: chenzhx
 */
#include <assert.h>
#include "hiredis/hiredis.h"
#include "hiredis/adapters/libevent.h"
#include "Sentinel.h"
#include "ConfigFileReader.h"
#include "AkLogging.h"

SentinelManager* g_sentinel_instance = nullptr;
typedef std::pair<std::string, std::string/*key value*/> RedisKV;
static int redisReply2VectKV(redisReply* reply, std::vector<std::vector<RedisKV>>	&kv) 
{
	if (reply->type == REDIS_REPLY_ARRAY)
	{
		for (size_t i = 0; i < reply->elements; i++)
		{
			redisReply* value_reply = reply->element[i];
            std::vector<RedisKV> vect_kv;
            for (size_t j = 0; j < value_reply->elements; j=j+2)
            {
                RedisKV kv(value_reply->element[j]->str, value_reply->element[j + 1]->str);
                vect_kv.push_back(kv);
            }
			kv.push_back(vect_kv);
		}
	}

    return 0;
}

SentinelManager::SentinelManager()
{

}

SentinelManager* getSentinelManagerInstance()
{
    if (!g_sentinel_instance) {
        g_sentinel_instance = new SentinelManager();
    }

    return g_sentinel_instance;
}

void SentinelManager::Init(std::vector<std::string/*ip:port*/> &addrs)
{
    for (const auto&addr : addrs)
    {
        std::size_t found = addr.find_first_of(':');
        std::string ip = addr.substr(0, found);
        std::string port = addr.substr(found + 1);
        IpAddrInfo ip_pair = std::make_pair(ip, ATOI(port.c_str()));
        redis_pairs_.push_back(ip_pair);
    }
}


int SentinelManager::getRedisMasterInstance(IpAddrInfo &ipaddr)
{
    //TODO：随机
    for (const auto& addr : redis_pairs_)
    {
        // 200ms超时
        struct timeval timeout = {0, 200000};
        redisContext* 	context = redisConnectWithTimeout(addr.first.c_str(), addr.second, timeout);
        if (context == NULL || context->err) 
        {
            AK_LOG_WARN << "Connection error: can't allocate redis sentinel context,will find next";
            redisFree(context);//断开连接并释放redisContext空间
            continue;
        }
        
        //获取master的ip和port
        redisReply* reply = static_cast<redisReply*> ( redisCommand(context,"SENTINEL get-master-addr-by-name mymaster") );
        if(reply->type != REDIS_REPLY_ARRAY || reply->elements != 2)
        {
            AK_LOG_WARN << "use sentinel " << addr.first.c_str()<< ":" << addr.second <<" to get-master-addr-by-name failure, will find next;"; 
            freeReplyObject(reply); 
            continue;
        }
        ipaddr = std::make_pair(reply->element[0]->str, ATOI(reply->element[1]->str));
        freeReplyObject(reply);
        redisFree(context);
        return 0;  
    }
    AK_LOG_WARN << "all sentinel get master are failure, please waiting and try again";
    return -1;
}

int SentinelManager::getRedisSlaveInstance(IpAddrInfo &ipaddr)
{
    for (const auto& addr : redis_pairs_)
    {
        // 200ms超时
        struct timeval timeout = {0, 200000};
        redisContext* 	context = redisConnectWithTimeout(addr.first.c_str(), addr.second, timeout);
        if (context == NULL || context->err) 
        {
            AK_LOG_WARN << "Connection error: can't allocate redis sentinel context,will find next";
            redisFree(context);//断开连接并释放redisContext空间
            continue;
        }
        
        //获取master的ip和port
        redisReply* reply = static_cast<redisReply*> ( redisCommand(context, "SENTINEL slaves mymaster") );
        if(reply == nullptr)
        {
            AK_LOG_WARN << "use sentinel " << addr.first.c_str()<< ":" << addr.second <<" to SENTINEL slaves mymaster failure, will find next. ";
            freeReplyObject(reply); 
            redisFree(context);
            continue;
        }
        std::string ip;
        std::string port;
        std::vector<std::vector<RedisKV>> kvs;
        redisReply2VectKV(reply, kvs);
        int is_disconnect = 0; 
        for (const auto&array: kvs)
        {
            for (const auto&kv: array)
            {
                //TODO:chenzhx 目前是一主一从，所以也不会太浪费资源，不用找到了就直接退出
                if (kv.first == "ip")
                {
                    ip = kv.second;
                }
                else if (kv.first == "port")
                {
                    port = kv.second;
                }
                else if (kv.first == "flags")
                {
                   if(kv.second.find("disconnected") != std::string::npos)
                   {
                        is_disconnect = 1;                         
                   }
                }
            }
        }
        if (ip.length() == 0 ||  port.length() ==  0 || is_disconnect)
        {
            AK_LOG_WARN << "use sentinel " << addr.first.c_str()<< ":" << addr.second <<" to SENTINEL slaves mymaster failure2, will find next. ";
            freeReplyObject(reply);
            redisFree(context);
            continue;            
        }
        ipaddr = std::make_pair(ip, ATOI(port.c_str()));
        freeReplyObject(reply);
        redisFree(context);
        return 0;          
    }
    AK_LOG_WARN << "all sentinel get slave are failure, please waiting and try again";
    return -1;
}



void SentinelManager::onSubMessage(redisAsyncContext *c, void *reply, void *privdata)
{
	if ((reply == nullptr) || (c == nullptr))
	{
		return;
	}
    redisReply *r = static_cast<redisReply*>(reply);

    if (r->type == REDIS_REPLY_ARRAY)
    {
        redisReply *file_url = r->element[2];
        if (file_url->type == REDIS_REPLY_INTEGER)
        {
			return;
        }
        else if (file_url->type == REDIS_REPLY_STRING)
        {
			//AK_LOG_INFO << " file_url->type ==" << file_url->type;
			AK_LOG_INFO << "file_url->str ==" << file_url->str;
            //HandlePicReq(file_url->str);
        }
    }
	SentinelManager *This = (SentinelManager *)privdata;
	This->master_change_cb_(1);    
}


void thread_for_sentinel_sub(SentinelManager *stnl)
{
    while (1) {
         for (const auto& addr : stnl->redis_pairs_)
         {
        	 stnl->base_ = event_base_new();
             stnl->redis_ctx_ = redisAsyncConnect(addr.first.c_str(), addr.second);
             AK_LOG_INFO << "redis Connect sentinel, " <<  addr.first << ":" << addr.second;
             if (stnl->redis_ctx_->err)
             {
                 AK_LOG_WARN << "redisAsyncConnect sentinel error [" << stnl->redis_ctx_->errstr << "] " << addr.first << ":" << addr.second;
                 continue;
             }
             stnl->redis_ctx_->data = (void*)stnl;
             redisLibeventAttach(stnl->redis_ctx_,stnl->base_);
             redisAsyncSetConnectCallback(stnl->redis_ctx_, SentinelConnectCallback);
             redisAsyncSetDisconnectCallback(stnl->redis_ctx_, SentinelDisconnectCallback); 
             redisAsyncCommand(stnl->redis_ctx_, SentinelManager::onSubMessage, stnl, "SUBSCRIBE +switch-master");
             event_base_dispatch(stnl->base_);// It continues to run until there are no more registered events or you call event_base_loopexit() / event_base_loopbreak().
             //当连接出错时候，会往下执行，需要释放资源。连接错误时候不需要调用event_base_loopexit
             //redisAsyncFree(stnl->redis_ctx_); //hredis底层会去释放了
             event_base_free(stnl->base_);
         }    
         sleep(1);
         AK_LOG_WARN << "redis Connect sentinel all faile!!!!! retry!";
     }
}

//TODO:增加标识，防止多次调用
int SentinelManager::startSentinelSub()
{
    AK_LOG_INFO << "start redis sentinel subscribe!";
    SentinelThread = new std::thread(thread_for_sentinel_sub, this);
    SentinelThread->detach();
     return 0;
}

void SentinelManager::PrintReply( redisReply *reply )
{
	switch (reply->type)
	{
	case REDIS_REPLY_INTEGER:
        AK_LOG_INFO << "[Integer] " << reply->integer;
		break;

	case REDIS_REPLY_ERROR:
	case REDIS_REPLY_STRING:
        AK_LOG_INFO << reply->str;
		break;

	case REDIS_REPLY_ARRAY:
		if (reply->element)
		{
			for (size_t i = 0; i < reply->elements; i++)
			{
				PrintReply(reply->element[i]);
			}
		}
		break;
	}
}



void SentinelManager::RedisCallback( redisAsyncContext *ctx, void *reply, void *priv )
{
	if (!ctx || !reply || !priv)
		return;

	SentinelManager *This = (SentinelManager *)priv;

	redisReply *r = (redisReply *)reply;

	This->PrintReply(r);
}

void SentinelConnectCallback(const redisAsyncContext *c, int status)
{
    if (status != REDIS_OK)
    {
		AK_LOG_WARN << "Sentinel:connect error [" << c->errstr << "]";		
        return;
    }
}


void SentinelDisconnectCallback(const redisAsyncContext *c, int status)
{
    if (status != REDIS_OK)
    {
		AK_LOG_WARN << "Sentinel:disconnect error [" << c->errstr << "]";
        return;
    }
}




