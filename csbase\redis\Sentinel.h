/*
 * Sentinel.h
 * Created on: 2019-07-10
 * Author: chenzhx
 */

#ifndef REDIS_SENTINEL_H_
#define REDIS_SENTINEL_H_

#include <vector>
#include <set>
#include <string>
#include "util.h"
#include "ThreadPool.h"
#include "hiredis/hiredis.h"
#include "hiredis/async.h"
//#include "hiredis/adapters/libevent.h"
#include <string>
#include "RedisUtil.h"
#include<thread>
#include <functional>

typedef std::pair<std::string, int/*ip port*/> IpAddrInfo;
class SentinelManager {
public:
	
	SentinelManager();
    ~SentinelManager()
    {

    }
    
    void Init(std::vector<std::string/*ip:port*/> &addrs);
    int getRedisMasterInstance(IpAddrInfo &ipaddr);
    int getRedisSlaveInstance(IpAddrInfo &ipaddr);
    
    int startSentinelSub();
    static void RedisCallback( redisAsyncContext *ctx, void *reply, void *priv );
    static void ConnectCallback(const redisAsyncContext *c, int status);
    static void DisconnectCallback(const redisAsyncContext *c, int status);
    void PrintReply( redisReply *reply );
    static void onSubMessage(redisAsyncContext *c, void *reply, void *privdata);

    void setMasterChangeCallback(const MasterChangeCallback &cb)
    {
        master_change_cb_ = cb;
    }

	std::vector<IpAddrInfo>	redis_pairs_;    
    redisAsyncContext *redis_ctx_;
	struct event_base *base_;
private:
    std::thread *SentinelThread;
    
    MasterChangeCallback master_change_cb_;
};

SentinelManager* getSentinelManagerInstance(); 
void SentinelConnectCallback(const redisAsyncContext *c, int status);
void SentinelDisconnectCallback(const redisAsyncContext *c, int status);


#endif /* REDIS_SENTINEL_H_ */
