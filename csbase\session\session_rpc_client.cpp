#include "session_rpc_client.h"
#include "AkcsMonitor.h"
#include <grpcpp/impl/codegen/status_code_enum.h> 

void SmRpcClient::RegisterNode(const std::string &node)
{
    client_node_ = node;
}
int SmRpcClient::RegDev(const std::string& mac, const std::string& sid, const std::string& uuid)
{
    // Data we are sending to the server.
    RegDevRequest reg_dev_request;
    reg_dev_request.set_dev_mac(mac);
    reg_dev_request.set_srv_id(sid);
    reg_dev_request.set_dev_uuid(uuid);
    // Call object to store rpc data
    AsyncClientCall* call = new AsyncClientCall;
    call->s_type_ = REG_DEV_SID;
    // stub_->PrepareAsyncSayHello() creates an RPC object, returning
    // an instance to store in "call" but does not actually start the RPC
    // Because we are using the asynchronous API, we need to hold on to
    // the "call" instance in order to get updates on the ongoing RPC.
    call->reg_dev_response_reader =
      stub_->PrepareAsyncRegDevSidHandle(&call->context, reg_dev_request, &cq_);

    // StartCall initiates the RPC call
    call->reg_dev_response_reader->StartCall();

    // Request that, upon completion of the RPC, "reply" be updated with the
    // server's response; "status" with the indication of whether the operation
    // was successful. Tag the request with the memory address of the call object.
    call->reg_dev_response_reader->Finish(&call->reg_dev_reply_, &call->status, (void*)call);
    return 0;
}

//查询设备
std::string SmRpcClient::QueryDev(const std::string& mac)
{
    std::string sid;
    QueryDevRequest query_dev_request;
    query_dev_request.set_dev_mac(mac);
    gpr_timespec timespec;
    timespec.tv_sec = 2;//设置同步阻塞时间为2秒
    timespec.tv_nsec = 0;
    timespec.clock_type = GPR_TIMESPAN;
    ClientContext context;
    context.set_deadline(timespec);
    QueryDevReply query_dev_reply;
    Status status = stub_->QueryDevSidHandle(&context, query_dev_request, &query_dev_reply);
    if (status.ok())
    {
        sid = query_dev_reply.srv_id();
        return sid;
    }
    else if (status.error_code() == grpc::DEADLINE_EXCEEDED)//rpc超时，证明下游服务已经过载了
    {
        AK_LOG_WARN << "RPC DEADLINE_EXCEEDED, query dev failed, dev is [" << mac << "]";
        //触发监控告警
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(client_node_, "QueryDevSidHandle deadline exceeded, the backend server is cssession", AKCS_MONITOR_ALARM_GRPC_TIMEOUT_CSSESSION);
        return sid;
    }
    else
    {
        AK_LOG_WARN << "RPC failed, query dev [" << mac << "]'s srv id err: " <<status.error_code() << ": " << status.error_message();
        return sid;
    }
}

//通过uuid查询设备
std::string SmRpcClient::QueryDevByUUID(const std::string& uuid)
{
    std::string sid;
    QueryDevUUIDRequest query_dev_request;
    query_dev_request.set_dev_uuid(uuid);
    gpr_timespec timespec;
    timespec.tv_sec = 2;//设置同步阻塞时间为2秒
    timespec.tv_nsec = 0;
    timespec.clock_type = GPR_TIMESPAN;
    ClientContext context;
    context.set_deadline(timespec);
    QueryDevReply query_dev_reply;
    Status status = stub_->QueryDevUUIDSidHandle(&context, query_dev_request, &query_dev_reply);
    if (status.ok())
    {
        sid = query_dev_reply.srv_id();
        return sid;
    }
    else if (status.error_code() == grpc::DEADLINE_EXCEEDED)//rpc超时，证明下游服务已经过载了
    {
        AK_LOG_WARN << "RPC DEADLINE_EXCEEDED, query dev failed, uuid is [" << uuid << "]";
        //触发监控告警
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(client_node_, "QueryDevUUIDSidHandle deadline exceeded, the backend server is cssession", AKCS_MONITOR_ALARM_GRPC_TIMEOUT_CSSESSION);
        return sid;
    }
    else
    {
        AK_LOG_WARN << "RPC failed, query dev uuid [" << uuid << "]'s srv id err: " <<status.error_code() << ": " << status.error_message();
        return sid;
    }
}


//异步接口
int SmRpcClient::RegUid(const std::string &uid, const std::string &sid, const std::string& uuid)
{
    RegUidRequest reg_uid_request;
    reg_uid_request.set_uid(uid);
    reg_uid_request.set_srv_id(sid);
    reg_uid_request.set_uuid(uuid);
    //reg_uid_request.set_node(node);
    AsyncClientCall* call = new AsyncClientCall;
    call->s_type_ = REG_UID_SID;
    call->reg_uid_response_reader =
      stub_->PrepareAsyncRegUidSidHandle(&call->context, reg_uid_request, &cq_);
    call->reg_uid_response_reader->StartCall();
    call->reg_uid_response_reader->Finish(&call->reg_uid_reply_, &call->status, (void*)call);
    return 0;
}

//查询uid,同步接口
std::string SmRpcClient::QueryUid(const std::string &uid)
{
    std::string sid;
    QueryUidRequest query_uid_request;
    query_uid_request.set_uid(uid);
    gpr_timespec timespec;
    timespec.tv_sec = 2;//设置同步阻塞时间为2秒
    timespec.tv_nsec = 0;
    timespec.clock_type = GPR_TIMESPAN;
    ClientContext context;
    context.set_deadline(timespec);
    QueryUidReply query_uid_reply;
    Status status = stub_->QueryUidSidHandle(&context, query_uid_request, &query_uid_reply);
    if (status.ok())
    {
        sid = query_uid_reply.srv_id();
        return sid;
    }
    else if ( status.error_code() == grpc::DEADLINE_EXCEEDED)//rpc超时,证明下游服务已经过载了或者节点服务失效
    {
        AK_LOG_WARN << "RPC DEADLINE_EXCEEDED, query uid failed, uid is [" << uid << "]";
        //触发监控告警
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(client_node_, "QueryUidSidHandle deadline exceeded, the backend server is cssession", AKCS_MONITOR_ALARM_GRPC_TIMEOUT_CSSESSION);
        return sid;
    }
    else
    {
        AK_LOG_WARN << "RPC failed, query uid [" << uid << "]'s srv id err: " <<status.error_code() << ": " << status.error_message();
        return sid;
    }

}

//查询uuid,同步接口
std::string SmRpcClient::QueryAccountUUID(const std::string &uuid)
{
    std::string sid;
    QueryUUIDRequest query_uuid_request;
    query_uuid_request.set_uuid(uuid);
    gpr_timespec timespec;
    timespec.tv_sec = 2;//设置同步阻塞时间为2秒
    timespec.tv_nsec = 0;
    timespec.clock_type = GPR_TIMESPAN;
    ClientContext context;
    context.set_deadline(timespec);
    QueryUidReply query_uid_reply;
    Status status = stub_->QueryUUIDSidHandle(&context, query_uuid_request, &query_uid_reply);
    if (status.ok())
    {
        sid = query_uid_reply.srv_id();
        return sid;
    }
    else if ( status.error_code() == grpc::DEADLINE_EXCEEDED)//rpc超时,证明下游服务已经过载了或者节点服务失效
    {
        AK_LOG_WARN << "RPC DEADLINE_EXCEEDED, query uuid failed, uuid is [" << uuid << "]";
        //触发监控告警
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(client_node_, "QueryUUIDSidHandle deadline exceeded, the backend server is cssession", AKCS_MONITOR_ALARM_GRPC_TIMEOUT_CSSESSION);
        return sid;
    }
    else
    {
        AK_LOG_WARN << "RPC failed, query uuid [" << uuid << "]'s srv id err: " <<status.error_code() << ": " << status.error_message();
        return sid;
    }

}

//异步接口.
int SmRpcClient::RemoveUid(const std::string &uid, const std::string &sid)
{
    RemoveUidRequest remove_uid_request;
    remove_uid_request.set_uid(uid);
    remove_uid_request.set_srv_id(sid);
    AsyncClientCall* call = new AsyncClientCall;
    call->s_type_ = REM_UID_SID;
    call->remove_uid_response_reader =
      stub_->PrepareAsyncRemoveUidSidHandle(&call->context, remove_uid_request, &cq_);
    call->remove_uid_response_reader->StartCall();
    call->remove_uid_response_reader->Finish(&call->remove_uid_reply_, &call->status, (void*)call);
    return 0;
}
//cssession 在这个时候再取数据库或者redis中取数据. 同步接口
int SmRpcClient::QueryUidsBySidNode(const std::string &sid, const std::string &node, std::set<std::string>& uids)
{
    QueryUidsBySidNodeRequest query_uids_by_sid_node_request;
    query_uids_by_sid_node_request.set_sid(sid);
    query_uids_by_sid_node_request.set_node(node);
    QueryUidsBySidNodeReply query_uids_by_sid_node_reply;
    //added by chenyc,2019-08-20,通过rpc的超时来判断下游服务是否过载.
    gpr_timespec timespec;
    timespec.tv_sec = 2;//设置同步阻塞时间为2秒
    timespec.tv_nsec = 0;
    timespec.clock_type = GPR_TIMESPAN;
    ClientContext context;
    context.set_deadline(timespec);
    Status status = stub_->QueryUidsBySidNodeHandle(&context, query_uids_by_sid_node_request, &query_uids_by_sid_node_reply);
    if (status.ok())
    {
        uint32_t uids_cnt = query_uids_by_sid_node_reply.uid_list_size();
        for(uint32_t i = 0; i < uids_cnt; ++i)
        {
            std::string uid = query_uids_by_sid_node_reply.uid_list(i);
            uids.insert(uid);
        }
        return 0;
    }
    else if ( status.error_code() == grpc::DEADLINE_EXCEEDED)//rpc超时，证明下游服务已经过载了
    {
        AK_LOG_WARN << "RPC DEADLINE_EXCEEDED, query uids failed, sid is [" << sid << "]";
        //触发监控告警
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(client_node_, "QueryUidsBySidNodeHandle deadline exceeded, the backend server is cssession", AKCS_MONITOR_ALARM_GRPC_TIMEOUT_CSSESSION);
        return -1;
    }
    else
    {
        AK_LOG_WARN << "RPC failed, query uids failed, sid is [" << sid << "], err: " <<status.error_code() << ": " << status.error_message();
        return -1;
    }
}

//异步注册
void SmRpcClient::AsyncCompleteRpc()
{
    void* got_tag;
    bool ok = false;

    // Block until the next result is available in the completion queue "cq".
    while (cq_.Next(&got_tag, &ok)) {
        // The tag in this example is the memory location of the call object
        AsyncClientCall* call = static_cast<AsyncClientCall*>(got_tag);

        // Verify that the request was completed successfully. Note that "ok"
        // corresponds solely to the request for updates introduced by Finish().
        GPR_ASSERT(ok);

        if (call->status.ok())
        {
            if(call->s_type_ == REG_DEV_SID)
            {
                //不需要返回
            }

            else if(call->s_type_ == REG_UID_SID)
            {

            }
            else if(call->s_type_ == REM_UID_SID) //删除uid
            {
            	
            }
            else if(call->s_type_ == QUERY_UIDS_BY_SID_NODE)//查询uid列表,已改成同步接口了.
            {
            	
            }

        }
        else
        {
            AK_LOG_WARN << "RPC failed, please check rpc server";
        }
        delete call; 
    }
}


