/*
 *
 * Copyright 2015 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */
 #ifndef _SESSION_RPC_CLIENT_H_
 #define _SESSION_RPC_CLIENT_H_

#include <iostream>
#include <memory>
#include <string>

#include <grpcpp/grpcpp.h>
#include <grpc/support/log.h>
#include <thread>
#include <unistd.h>
#include <grpcpp/grpcpp.h>
#include "grpc_balancer_service.h"

#include "AK.Session.grpc.pb.h"
#include "AkLogging.h"
#include "AkcsMsgDef.h"

using grpc::Channel;
using grpc::ClientAsyncResponseReader;
using grpc::ClientContext;
using grpc::CompletionQueue;
using grpc::Status;

using AK::Session::RegDevRequest;
using AK::Session::RegDevReply;
using AK::Session::QueryDevRequest;
using AK::Session::QueryDevUUIDRequest;
using AK::Session::QueryDevReply;

using AK::Session::RegUidRequest;
using AK::Session::RegUidReply;
using AK::Session::QueryUidRequest;
using AK::Session::QueryUUIDRequest;
using AK::Session::QueryUidReply;
using AK::Session::RemoveUidRequest;
using AK::Session::RemoveUidReply;
using AK::Session::QueryUidsBySidNodeRequest;
using AK::Session::QueryUidsBySidNodeReply;

using AK::Session::SmRpcSrv; //rpc服务名

//对rpc内部接口的封装
class SmRpcClient {
  public:
    explicit SmRpcClient() {
        SetUp();
    }

    void SetUp()  {
      response_generator_ =
          grpc_core::MakeRefCounted<grpc_core::FakeResolverResponseGenerator>();
      ResetStub(500);
    }

    grpc_lb_addresses* CreateLbAddressesFromAddressDataList(
        const std::vector<AddressData>& address_data) {
      grpc_lb_addresses* addresses =
          grpc_lb_addresses_create(address_data.size(), nullptr);
      for (size_t i = 0; i < address_data.size(); ++i) {
        char* lb_uri_str;
        gpr_asprintf(&lb_uri_str, "ipv4:%s:%d", address_data[i].host.c_str(), address_data[i].port);
        grpc_uri* lb_uri = grpc_uri_parse(lb_uri_str, true);
        GPR_ASSERT(lb_uri != nullptr);
        grpc_lb_addresses_set_address_from_uri(
            addresses, i, lb_uri, address_data[i].is_balancer,
            address_data[i].balancer_name.c_str(), nullptr);
        grpc_uri_destroy(lb_uri);
        gpr_free(lb_uri_str);
      }
      return addresses;
    }
    
    void SetNextResolution(const std::vector<AddressData>& address_data) {
      grpc_core::ExecCtx exec_ctx;
      grpc_lb_addresses* addresses =
          CreateLbAddressesFromAddressDataList(address_data);
      grpc_arg fake_addresses = grpc_lb_addresses_create_channel_arg(addresses);
      grpc_channel_args fake_result = {1, &fake_addresses};
      response_generator_->SetResponse(&fake_result);
      grpc_lb_addresses_destroy(addresses);
    }
    
    void SetNextReresolutionResponse(
        const std::vector<AddressData>& address_data) {
      grpc_core::ExecCtx exec_ctx;
      grpc_lb_addresses* addresses =
          CreateLbAddressesFromAddressDataList(address_data);
      grpc_arg fake_addresses = grpc_lb_addresses_create_channel_arg(addresses);
      grpc_channel_args fake_result = {1, &fake_addresses};
      response_generator_->SetReresolutionResponse(&fake_result);
      grpc_lb_addresses_destroy(addresses);
    }


    void ResetStub(int fallback_timeout = 0,
                   const grpc::string& expected_targets = "") {
      grpc::ChannelArguments args;
      args.SetGrpclbFallbackTimeout(fallback_timeout);
      
      
      args.SetLoadBalancingPolicyName("round_robin");//pick_first/round_robin/grcplb自定义负载均衡
      args.SetPointer(GRPC_ARG_FAKE_RESOLVER_RESPONSE_GENERATOR,
                      response_generator_.get());
      if (!expected_targets.empty()) {
        args.SetString(GRPC_ARG_FAKE_SECURITY_EXPECTED_TARGETS, expected_targets);
      }
      std::ostringstream uri;
      uri << "fake:///" << kApplicationTargetName_;
      channel_ = grpc::CreateCustomChannel(uri.str(), grpc::InsecureChannelCredentials(), args);
      stub_ = SmRpcSrv::NewStub(channel_);
    }

    //不启动外部负载均衡模块
    //std::vector<std::unique_ptr<BalancerServiceImpl>> balancers_;
    //std::vector<ServerThread<BalancerService>> balancer_servers_;
    std::shared_ptr<Channel> channel_;
    grpc_core::RefCountedPtr<grpc_core::FakeResolverResponseGenerator> response_generator_;
    const grpc::string kApplicationTargetName_ = "session";
	
	//added by chenyc,2019-12-10,调用者主动注册信息进来
    void RegisterNode(const std::string &node);
    // Assembles the client's payload and sends it to the server.
    int RegDev(const std::string& mac, const std::string& sid, const std::string& uuid="");
    std::string QueryDev(const std::string& mac);
    std::string QueryDevByUUID(const std::string& uuid);
    
    int RegUid(const std::string &uid, const std::string &sid, const std::string& uuid);
    std::string QueryUid(const std::string &uid);
    std::string QueryAccountUUID(const std::string& uuid);
    int RemoveUid(const std::string &uid, const std::string &sid);

    int QueryUidsBySidNode(const std::string &sid, const std::string &node, std::set<std::string>& uids);

    // Loop while listening for completed responses.
    // Prints out the response from the server.
    void AsyncCompleteRpc();

  private:

    // struct for keeping state and data information
    // TODO,通过多态来处理AsyncClientCall的逻辑
    struct AsyncClientCall {
        
        SessionSrvType s_type_;
        // Container for the data we expect from the server.
        RegDevReply reg_dev_reply_;
        
        RegUidReply reg_uid_reply_;
        
        RemoveUidReply remove_uid_reply_;
        // Context for the client. It could be used to convey extra information to
        // the server and/or tweak certain RPC behaviors.
        ClientContext context;

        // Storage for the status of the RPC upon completion.
        Status status;

        //ClientAsyncResponseReader<HelloReply> 客户端异步响应读取对象
        std::unique_ptr<ClientAsyncResponseReader<RegDevReply>> reg_dev_response_reader;
        //std::unique_ptr<ClientAsyncResponseReader<QueryDevReply>> query_dev_response_reader;
        std::unique_ptr<ClientAsyncResponseReader<RegUidReply>> reg_uid_response_reader;

        std::unique_ptr<ClientAsyncResponseReader<RemoveUidReply>> remove_uid_response_reader;
        //std::unique_ptr<ClientAsyncResponseReader<QueryUidReply>> query_uid_response_reader;
    };

    // Out of the passed in Channel comes the stub, stored here, our view of the
    // server's exposed services.
    std::unique_ptr<SmRpcSrv::Stub> stub_;
    //QueryUidReply query_uid_reply_;

    // The producer-consumer queue we use to communicate asynchronously with the
    // gRPC runtime.
    CompletionQueue cq_;

	std::string client_node_;
};


#endif
