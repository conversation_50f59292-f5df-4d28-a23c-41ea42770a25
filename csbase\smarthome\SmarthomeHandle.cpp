﻿#include "SmarthomeHandle.h"
#include "SmarthomeRequest.h"
#include "AkLogging.h"
#include "json/json.h"
#include "AkcsPbxHttpMsg.h"

SmarthomeHandle& SmarthomeHandle::GetInstance()
{
	static SmarthomeHandle handle;
	return handle;
}

void SmarthomeHandle::SetSmgConf(std::string& smg_addr)
{ 
    char addr_tmp[128];
    snprintf(addr_tmp, sizeof(addr_tmp), "http://%s/command/handle", smg_addr.c_str());
    smarthome_gateway_addr_ =  addr_tmp;
}


int SmarthomeHandle::GetRtspPasswd(const std::string& access_id, std::string& passwd)
{
    Json::Value item_param;
    item_param["access_id"] = access_id;
    std :: string response;
    if (0 != smarthome::Request::Command(smarthome_gateway_addr_, SMARTHOME_HANDLE_RTSP_CHECK_RTSP_ACCESS, item_param, response))
    {
        AK_LOG_WARN << "request smarthome gateway timeout";
    }
    
    Json::Reader reader;
    Json::Value root;
    Json::Value result;
    if (!reader.parse(response, root))
    {
        AK_LOG_WARN << "smarthome check_rtsp_access respone error," << " data:" << response;
        return -1;
    }
    
    if (!root.isMember("result"))
    {
        AK_LOG_WARN << "smarthome check_rtsp_access respone error," << " data:" << response;
        return -1;
    }
    
    result = root["result"];
    
    if (!result.isMember("password"))
    {
        AK_LOG_WARN << "smarthome check_rtsp_access respone error," << " data:" << response;
        return -1;
    }

    passwd = (result["password"].asString());
    return 0;
}

int SmarthomeHandle::NotifyDevBeginRtp(const std::string& device_id, const std::string& rtp_ip, int rtp_port)
{
    Json::Value item_param;
    item_param["device_id"] = device_id;
    item_param["rtsp_url"] = rtp_ip;
    item_param["rtsp_port"] = rtp_port;
    item_param["keepalive"] = 60;
    
    std::string response;
    if (0 != smarthome::Request::Command(smarthome_gateway_addr_, SMARTHOME_HANDLE_RTSP_OPEN_RTSP, item_param, response))
    {
        AK_LOG_WARN << "request smarthome gateway timeout";
    }
    
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(response, root))
    {
        AK_LOG_WARN << "smarthome open_rtsp respone error," << " data:" << response;
        return -1;
    }
    
    if (!root.isMember("success"))
    {
        AK_LOG_WARN << "smarthome open_rtsp respone error," << " data:" << response;
        return -1;
    }

    bool success = root["success"].asBool();
    if(success == false){
        AK_LOG_WARN << "smarthome open_rtsp respone error," << " data:" << response;
        return -1;
    }

    return 0;
}

int SmarthomeHandle::NotifyDevStopRtp(const std::string& device_id)
{
    Json::Value item_param;
    item_param["device_id"] = device_id;
    
    std::string response;
    if (0 != smarthome::Request::Command(smarthome_gateway_addr_, SMARTHOME_HANDLE_RTSP_CLOSE_RTSP, item_param, response))
    {
        AK_LOG_WARN << "request smarthome gateway timeout";
    }
    
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(response, root))
    {
        AK_LOG_WARN << "smarthome close_rtsp respone error," << " data:" << response;
        return -1;
    }
    
    if (!root.isMember("success"))
    {
        AK_LOG_WARN << "smarthome close_rtsp respone error," << " data:" << response;
        return -1;
    }

    bool success = root["success"].asBool();
    if(success == false){
        AK_LOG_WARN << "smarthome close_rtsp respone error," << " data:" << response;
        return -1;
    }

    return 0;
}

int SmarthomeHandle::NotifyDevKeepalive(const std::string& device_id)
{
    Json::Value item_param;
    item_param["device_id"] = device_id;
    item_param["keepalive"] = 60;
    
    std::string response;
    if (0 !=smarthome::Request::Command(smarthome_gateway_addr_, SMARTHOME_HANDLE_RTSP_KEEPALIVE_RTSP, item_param, response))
    {
        AK_LOG_WARN << "request smarthome gateway timeout";
    }
    
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(response, root))
    {
        AK_LOG_WARN << "smarthome keepalive_rtsp respone error," << " data:" << response;
        return -1;
    }
    
    if (!root.isMember("success"))
    {
        AK_LOG_WARN << "smarthome keepalive_rtsp respone error," << " data:" << response;
        return -1;
    }

    bool success = root["success"].asBool();
    if(success == false){
        AK_LOG_WARN << "smarthome keepalive_rtsp respone error," << " data:" << response;
        return -1;
    }

    return 0;
}

//返回值:-1数据格式错误,0离线,1在线
int SmarthomeHandle::GetMobileStatus(uint64_t traceid, const std::string& callee, const std::string &caller, const std::string &real_callee, const std::string& command)
{
    Json::Value item_param;
    item_param["sip"] = callee;
    item_param["caller"] = caller; //主叫
    item_param["real_callee"] = real_callee; //真实被叫
    
    std::string response;
    if (0 != smarthome::Request::PbxCommand(traceid, smarthome_gateway_addr_, command, item_param, response))
    {
        AK_LOG_WARN << "request smarthome gateway timeout";
        return -1;
    }

    
    AK_LOG_INFO << "smarthome get_mobile_status, respone: " << response;
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(response, root))
    {
        AK_LOG_WARN << "smarthome get_mobile_status respone error, data:" << response;
        return -1;
    }
    
    if (!root.isMember("success"))
    {
        AK_LOG_WARN << "smarthome get_mobile_status respone error, data:" << response;
        return -1;
    }

    //"success": false, code=500不重试;  code=其他重试
    bool success = root["success"].asBool();
    if(success == false)
    {
        if (!root.isMember(HTTP_RET_PARMA_CODE) || root["code"].asInt() != HTTP_RET_RESULT_INTERNAL_SERVER_ERROR)
        {
            AK_LOG_WARN << "smarthome get_mobile_status respone error, data:" << response;
            return -1;
        }
    }

    if (!root.isMember(HTTP_RET_PARMA_RESULT_CODE) || !root[HTTP_RET_PARMA_RESULT_CODE].isMember(HTTP_RET_PARMA_STATUS))
    {
        AK_LOG_WARN << "smarthome get_mobile_status respone error, status not exist.";
        return -1;
    }

    std::string sip_status = root[HTTP_RET_PARMA_RESULT_CODE][HTTP_RET_PARMA_STATUS].asCString();
    if (strcmp(sip_status.c_str(), HTTP_RET_VALUE_ONLINE) == 0)
    {
        return 1;
    }
    else if (strcmp(sip_status.c_str(), HTTP_RET_VALUE_OFFLINE) == 0)
    {
        return 0;
    }
    else if (strcmp(sip_status.c_str(), HTTP_RET_VALUE_DND) == 0)
    {
        return 2;
    }    

    return -1;
}

int SmarthomeHandle::WakeupMobile(uint64_t traceid, const std::string& caller_sip, const std::string& callee_sip, const std::string& caller_name, const std::string& command)
{
    Json::Value item_param;
    item_param["caller_sip"] = caller_sip;
    item_param["callee_sip"] = callee_sip;
    item_param["caller_name"] = caller_name;
    
    std::string response;
    if (0 != smarthome::Request::PbxCommand(traceid, smarthome_gateway_addr_, command, item_param, response))
    {
        AK_LOG_WARN << "request smarthome gateway timeout";
    }
    
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(response, root))
    {
        AK_LOG_WARN << "smarthome wakeup_mobile respone error," << " data:" << response;
        return -1;
    }
    
    if (!root.isMember("success"))
    {
        AK_LOG_WARN << "smarthome wakeup_mobile respone error," << " data:" << response;
        return -1;
    }

    //"success": false, code=500不重试;  code=其他重试
    bool success = root["success"].asBool();
    if(success == false)
    {
        if (!root.isMember(HTTP_RET_PARMA_CODE) || root[HTTP_RET_PARMA_CODE].asInt() != HTTP_RET_RESULT_INTERNAL_SERVER_ERROR)
        {
            AK_LOG_WARN << "smarthome wakeup_mobile respone error," << " data:" << response;
            return -1;
        }
    }

    return 0;
}

int SmarthomeHandle::HangupMobile(uint64_t traceid, const std::string& caller_sip, const std::string& callee_sip, const std::string& caller_name, const std::string& command)
{
    Json::Value item_param;
    item_param["caller_sip"] = caller_sip;
    item_param["callee_sip"] = callee_sip;
    item_param["caller_name"] = caller_name;

    std::string response;
    if (0 != smarthome::Request::PbxCommand(traceid, smarthome_gateway_addr_, command, item_param, response))
    {
        AK_LOG_WARN << "request smarthome gateway timeout";
    }

    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(response, root))
    {
        AK_LOG_WARN << "smarthome hangup_mobile respone error," << " data:" << response;
        return -1;
    }

    if (!root.isMember("success"))
    {
        AK_LOG_WARN << "smarthome hangup_mobile respone error," << " data:" << response;
        return -1;
    }

    //"success": false, code=500不重试;  code=其他重试
    bool success = root["success"].asBool();
    if(!success)
    {
        if (!root.isMember(HTTP_RET_PARMA_CODE) || root[HTTP_RET_PARMA_CODE].asInt() != HTTP_RET_RESULT_INTERNAL_SERVER_ERROR)
        {
            AK_LOG_WARN << "smarthome hangup_mobile respone error," << " data:" << response;
            return -1;
        }
    }

    return 0;
}
