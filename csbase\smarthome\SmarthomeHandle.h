﻿#ifndef __SMARTHOME_HANDLE_H__
#define __SMARTHOME_HANDLE_H__

#include <boost/noncopyable.hpp>
#include <string>


#define SMARTHOME_HANDLE_RTSP_CHECK_RTSP_ACCESS      "check_rtsp_access"
#define SMARTHOME_HANDLE_RTSP_OPEN_RTSP              "open_rtsp"
#define SMARTHOME_HANDLE_RTSP_CLOSE_RTSP             "close_rtsp"
#define SMARTHOME_HANDLE_RTSP_KEEPALIVE_RTSP         "keepalive_rtsp"
#define SMARTHOME_HANDLE_PBX_WAKEUP_MOBILE           "wakeup_mobile"
#define SMARTHOME_HANDLE_PBX_WAKEUP_SMARTLOCK        "wakeup_device"
#define SMARTHOME_HANDLE_PBX_WAKEUP_SDK               "wakeup_sdk"


#define SMARTHOME_HANDLE_PBX_HANGUP_MOBILE           "hangup_mobile"
#define SMARTHOME_HANDLE_PBX_HANGUP_SDK           "hangup_sdk"
#define SMARTHOME_HANDLE_PBX_GET_MOBILE_STATUS       "get_mobile_status"
#define SMARTHOME_HANDLE_PBX_GET_SDK_STATUS       "get_sdk_status"
class SmarthomeHandle : private boost::noncopyable
{

public:
    static SmarthomeHandle& GetInstance();

    int GetRtspPasswd(const std::string& access_id, std::string& passwd);
    
    int NotifyDevBeginRtp(const std::string& device_id, const std::string& rtp_ip, int rtp_port);

    int NotifyDevStopRtp(const std::string& device_id);

    int NotifyDevKeepalive(const std::string& device_id);

    int GetMobileStatus(uint64_t traceid, const std::string& sip,const std::string &caller, const std::string &real_callee, const std::string& command);

    int WakeupMobile(uint64_t traceid, const std::string& caller_sip, const std::string& callee_sip, const std::string& caller_name, const std::string& command);

    int HangupMobile(uint64_t traceid, const std::string& caller_sip, const std::string& callee_sip, const std::string& caller_name, const std::string& command);

    void SetSmgConf(std::string& smg_addr); 

private:

    std::string smarthome_gateway_addr_;

};

#endif

