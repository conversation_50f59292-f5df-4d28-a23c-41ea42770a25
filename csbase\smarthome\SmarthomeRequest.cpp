#include <sys/time.h>
#include "SmarthomeRequest.h"
#include "AkLogging.h"
#include "SnowFlakeGid.h"
#include "AkcsHttpRequest.h"


namespace smarthome
{

Request::Request()
{

}

int Request::Command(const std::string& addr, const std::string& command, const Json::Value& param, std::string& response)
{  
    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    
    Json::Value item_data;
    item_data["id"] = std::to_string(traceid);
    item_data["command"] = command;
    struct timeval now_time;
    gettimeofday(&now_time, NULL);
    long long now_timestamp = now_time.tv_sec;
    item_data["timestamp"] = now_timestamp;
    item_data["param"] = param; 

    Json::Value item_body;
    item_body["trace_id"] = std::to_string(traceid);
    item_body["timestamp"] = now_timestamp;
    item_body["data"] = item_data;
    
    Json::FastWriter w_json_item;
    std::string json_data = w_json_item.write(item_body);

    return model::HttpRequest::GetInstance().Post(addr, json_data, response, HTTP_DATA_TYPE_JSON);
}

/*
{
    "trace_id":"c45e846ca23ab42c9ae469d988ae32a96",
    "timestamp":"1681884307",
    "command":"get_mobile_status",
    "data":{
        "command":"get_mobile_status",
        "id":"c45e846ca23ab42c9ae469d988ae32a96",
        "param":{
            "sip":"6149100006"
        }
    }
}
*/
int Request::PbxCommand(uint64_t traceid, const std::string& addr, const std::string& command, const Json::Value& param, std::string& response)
{  
    struct timeval now_time;
    gettimeofday(&now_time, NULL);
    long long now_timestamp = now_time.tv_sec;

    Json::Value item_body;
    item_body["trace_id"] = std::to_string(traceid);
    item_body["timestamp"] = now_timestamp;
    item_body["command"] = command;
    
    Json::Value item_data;
    item_data["command"] = command;
    item_data["id"] = std::to_string(traceid);
    item_data["param"] = param; 
    item_body["data"] = item_data;
    
    Json::FastWriter w_json_item;
    std::string json_data = w_json_item.write(item_body);

    return model::HttpRequest::GetInstance().Post(addr, json_data, response, HTTP_DATA_TYPE_JSON);
}



}
