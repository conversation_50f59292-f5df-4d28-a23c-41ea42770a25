#ifndef __SMARTHOME_REQUEST_H__
#define __SMARTHOME_REQUEST_H__
#include <string>
#include "json/json.h"


namespace smarthome
{

class Request
{
public:
    Request();
    ~Request();

    static int Command(const std::string& addr, const std::string& command, const Json::Value& param, std::string& response);
    static int PbxCommand(uint64_t traceid, const std::string& addr, const std::string& command, const Json::Value& param, std::string& response);
    static int Notice();

private:
    
};


}
#endif
