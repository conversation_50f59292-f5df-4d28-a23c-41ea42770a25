﻿//	100% Public Domain.
//
//	Original C Code
//	 -- <PERSON> <<EMAIL>>
//	Small changes to fit into bglibs
//	  -- <PERSON> <<EMAIL>>
//	Translation to simpler C++ Code
//	  -- <PERSON><PERSON> <<EMAIL>>
//	Safety fixes
//	  -- <PERSON> <slowriot at voxelstorm dot com>
//  Adapt for project
//      <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
//
// File created on: 2017.02.25

// SHA1.h

#pragma once

#include <cstdint>
#include <iostream>
#include <string>

namespace toolkit {

class SHA1 final
{
public:
    SHA1();

    void update(const std::string &s);
    void update(std::istream &is);
    std::string final();
    std::string final_bin();

    static std::string from_file(const std::string &filename);

    static std::string encode(const std::string &s);
    static std::string encode_bin(const std::string &s);

private:
    uint32_t digest[5];
    std::string buffer;
    uint64_t transforms;
};

}//namespace toolkit