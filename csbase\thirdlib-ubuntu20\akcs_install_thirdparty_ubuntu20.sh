#!/bin/bash
# ****************************************************************************
# Author        :   buhuai.su
# Last modified :   2022-08-05
# Filename      :   build.sh
# Version       :
# Description   :   系统初始化执行脚本
# Modifier      :
# ****************************************************************************

set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径
MIDDLEWARE=$3

#csroute安装脚本,含csroute、nsq等两个组件
AKCS_INSTALL_PATH=/usr/local/lib
PAKCAGES_ROOT=$RSYNC_PATH
PAKCAGES_LIB_ROOT=$RSYNC_PATH/thirdlib-ubuntu20


function fCreatLinks()
{
    ln -sf /usr/local/lib/libmysqlclient.so /usr/local/lib/libmysqlclient.so.18 2>/dev/null
    ln -sf /usr/local/lib/libevpp.so /usr/local/lib/libevpp.so.0.7 2>/dev/null
    ln -sf /usr/local/lib/libevpp_concurrentqueue.so /usr/local/lib/libevpp_concurrentqueue.so.0.7 2>/dev/null
    ln -sf /usr/local/lib/libevent.so /usr/local/lib/libevent-2.1.so.6 2>/dev/null
    ln -sf /usr/local/lib/libglog.so /usr/local/lib/libglog.so.0 2>/dev/null
    ln -sf /usr/local/lib/librdkafka.so /usr/local/lib/librdkafka.so.1 2>/dev/null
    ln -sf /usr/local/lib/libcares.so /usr/local/lib/libcares.so.2 2>/dev/null
    ln -sf /usr/local/lib/libboost_system.so /usr/local/lib/libboost_system.so.1.54.0 2>/dev/null
}

function checkChange()
{
    new_path=$1
    old_path=$2
    new=`md5sum $new_path | awk '{print $1}'`
    old=`md5sum $old_path | awk '{print $1}'`
    if [ $new"x" == $old"x" ];then
        return 0
    else
        return 1
    fi
}

chmod 666 $PAKCAGES_LIB_ROOT/*

libs="libcpprest.so.2.10  libetcd-cpp-api.so  libfastcommon.so  libgpr.so.6 \
libgrpc.so.6 libprotobuf.so.15 libboost_system.so  libssl.so.1.0.0  libcrypto.so.1.0.0 \
libevent.so libglog.so libgrpc++.so.1  libiconv.so   libmysqlclient.so \
libevpp.so libevpp_concurrentqueue.so libipc.so libfdfsclient.so librdkafka.so libcares.so"
for i in $libs
do
    if [ -f $PAKCAGES_LIB_ROOT/$i ] && [ -f /usr/local/lib/$i ];then
        if checkChange $PAKCAGES_LIB_ROOT/$i /usr/local/lib/$i;then
            echo "$i md5  change. copy file..."
            cp -rvf $PAKCAGES_LIB_ROOT/$i /usr/local/lib/$i
        fi
    fi

    if [ -f $PAKCAGES_LIB_ROOT/$i ] && [ ! -f /usr/local/lib/$i ];then
        echo "$i file no exist. copy file..."
        cp -rvf $PAKCAGES_LIB_ROOT/$i /usr/local/lib/$i
    fi
done

if [ ! -f /usr/lib/x86_64-linux-gnu/libstdc++.so.6.0.24 ];then
    if [ -f /usr/lib/x86_64-linux-gnu/libstdc++.so.6.0.19 ];then rm -rf /usr/lib/x86_64-linux-gnu/libstdc++.so.6.0.19; fi

    cp -rf ${PAKCAGES_ROOT}/thirdlib/libstdc++.so.6.0.24 /usr/lib/x86_64-linux-gnu/    
    
    if [ -L /usr/lib/x86_64-linux-gnu/libstdc++.so.6 ];then rm -rf /usr/lib/x86_64-linux-gnu/libstdc++.so.6; fi
    ln -sf /usr/lib/x86_64-linux-gnu/libstdc++.so.6.0.24 /usr/lib/x86_64-linux-gnu/libstdc++.so.6
fi
#只是ln连接,不会使得程序死掉
fCreatLinks


#修改系统的动态库查找路径
if [ -z `grep "/usr/local/lib" /etc/ld.so.conf` ];then
    echo "/usr/local/lib" >> /etc/ld.so.conf
    ldconfig
fi


echo "install ok!"
