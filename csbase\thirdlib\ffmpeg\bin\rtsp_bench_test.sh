#!/bin/bash

# RTSP stream URL
rtsp_url="rtsp://user:<EMAIL>:554/0A0203200117"

# Number of concurrent instances
instances=20

# Run a single instance of ffmpeg to pull the RTSP stream and simulate real-time playback
./ffmpeg -rtsp_transport udp -i "$rtsp_url" -c:v copy -c:a aac -f null -re /dev/null &

# Sleep for a short duration to ensure the first instance is running
sleep 2

# Run additional instances of ffmpeg to consume the stream
for ((i=2; i<=$instances; i++)); do
    ./ffmpeg -rtsp_transport udp -i "$rtsp_url" -c:v copy -c:a aac -f null -re /dev/null &
done

# Wait for all background processes to finish
wait

