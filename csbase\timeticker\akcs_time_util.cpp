﻿/*
 * Copyright (c) 2016 The ZLToolKit project authors. All Rights Reserved.
 *
 * This file is part of ZLToolKit(https://github.com/ZLMediaKit/ZLToolKit).
 *
 * Use of this source code is governed by MIT license that can be found in the
 * LICENSE file in the root of the source tree. All contributing project authors
 * may be found in the AUTHORS file in the root of the source tree.
 */

#include <cassert>
#include <cstdio>
#include <cstdlib>
#include <cstring>
#include <string>
#include <algorithm>
#include <random>
#include <thread>
#include "akcs_time_util.h"
#include "akcs_local_time.h"
#include <atomic>

namespace akcs_toolkit {

class onceToken {
public:
    using task = std::function<void(void)>;

    template<typename FUNC>
    onceToken(const FUNC &onConstructed, task onDestructed = nullptr) {
        onConstructed();
        _onDestructed = std::move(onDestructed);
    }

    onceToken(std::nullptr_t, task onDestructed = nullptr) {
        _onDestructed = std::move(onDestructed);
    }

    ~onceToken() {
        if (_onDestructed) {
            _onDestructed();
        }
    }

private:
    onceToken() = delete;
    onceToken(const onceToken &) = delete;
    onceToken(onceToken &&) = delete;
    onceToken &operator=(const onceToken &) = delete;
    onceToken &operator=(onceToken &&) = delete;

private:
    task _onDestructed;
};

struct tm getLocalTime(time_t sec) {
    struct tm tm;
    no_locks_localtime(&tm, sec);
    return tm;
}


static long s_gmtoff = 0; //时间差
static onceToken s_token([]() {
    local_time_init();
    s_gmtoff = getLocalTime(time(nullptr)).tm_gmtoff;
});

static inline uint64_t getCurrentMicrosecondOrigin() {
#if !defined(_WIN32)
    struct timeval tv;
    gettimeofday(&tv, nullptr);
    return tv.tv_sec * 1000000LL + tv.tv_usec;
#else
    return  std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
#endif
}

static std::atomic<uint64_t> s_currentMicrosecond(0);
static std::atomic<uint64_t> s_currentMillisecond(0);
static std::atomic<uint64_t> s_currentMicrosecond_system(getCurrentMicrosecondOrigin());
static std::atomic<uint64_t> s_currentMillisecond_system(getCurrentMicrosecondOrigin() / 1000);

static inline bool initMillisecondThread() {
    static std::thread s_thread([]() {
        uint64_t last = getCurrentMicrosecondOrigin();
        uint64_t now;
        uint64_t microsecond = 0;
        while (true) {
            now = getCurrentMicrosecondOrigin();
            //记录系统时间戳，可回退
            s_currentMicrosecond_system.store(now, std::memory_order_release);
            s_currentMillisecond_system.store(now / 1000, std::memory_order_release);

            //记录流逝时间戳，不可回退
            int64_t expired = now - last;
            last = now;
            if (expired > 0 && expired < 1000 * 1000) {
                //流逝时间处于0~1000ms之间，那么是合理的，说明没有调整系统时间
                microsecond += expired;
                s_currentMicrosecond.store(microsecond, std::memory_order_release);
                s_currentMillisecond.store(microsecond / 1000, std::memory_order_release);
            } else if (expired != 0) {
                //WarnL << "Stamp expired is abnormal: " << expired;
            }
            //休眠0.5 ms
            usleep(500);
        }
    });
    static onceToken s_token([]() {
        s_thread.detach();
    });
    return true;
}

uint64_t getCurrentMillisecond(bool system_time) {
    initMillisecondThread();
    //static bool flag = initMillisecondThread();
    if (system_time) {
        return s_currentMillisecond_system.load(std::memory_order_acquire);
    }
    return s_currentMillisecond.load(std::memory_order_acquire);
}

uint64_t getCurrentMicrosecond(bool system_time) {
    initMillisecondThread();
    //static bool flag = initMillisecondThread();
    if (system_time) {
        return s_currentMicrosecond_system.load(std::memory_order_acquire);
    }
    return s_currentMicrosecond.load(std::memory_order_acquire);
}

}  // namespace toolkit

