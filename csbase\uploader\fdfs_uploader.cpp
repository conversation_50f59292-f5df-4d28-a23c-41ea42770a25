#include "fdfs_uploader.h"
#include "fdfs_global.h"
#include "fdfs_client.h"
#include "util.h"
#include "AkLogging.h"

#include <assert.h>

#define LOCAL_FILE_KEY "local_filepath" //本地文件(未上传fdfs前的)路径
#define FDFS_LOG_LEVEL 7
#define FDFS_LOG_FILE_NAME "fdfs_client.log"


FdfsUploader::~FdfsUploader()
{
    tracker_disconnect_server_ex(tracker_server_, true);
    fdfs_client_destroy();
}

bool FdfsUploader::GetFdfsStat()
{
    if(fdfs_stat_)
    {
        return fdfs_stat_;
    }
    else
    {
        //可能是与fdfs的连接已经断开，此时如果这个连接业务还未重新使用，不会变更状态，因此主动重连下
        //解决metrics无法自动恢复的问题
        ReInit();
        return fdfs_stat_;
    }
}

int FdfsUploader::Init(const std::string& config_filepath)
{
    AK_LOG_INFO << "Init config_filepath = " << config_filepath;
    config_file_path_ = config_filepath;

    log_init();
    g_log_context.log_level = FDFS_LOG_LEVEL;
    
    int result = -1;
    if((result = fdfs_client_init_ex(&tracker_group_, config_file_path_.c_str())) != 0)
    {
        return result;
    }
    //建立与tracker ser的tcp连接,注意连接断开的重连
    tracker_server_ = tracker_get_connection_ex(&tracker_group_); //针对目前只有一台tracker-ser的情况时可以的.
    if (tracker_server_ == nullptr)
    {
        fdfs_client_destroy_ex(&tracker_group_);
        AK_LOG_WARN << "get tracker connection failed";
        return errno != 0 ? errno : ECONNREFUSED;
    }
    fdfs_stat_ = true;
    return 0;
}

int FdfsUploader::ReInit()
{
    std::lock_guard<std::mutex> lock(tracker_conn_mutex_);
    if (tracker_server_)
    {
        tracker_disconnect_server_ex(tracker_server_, true);
        fdfs_stat_ = false;
    }
    fdfs_client_destroy_ex(&tracker_group_);//设置状态
    int result;
    if ((result = fdfs_client_init_ex(&tracker_group_, config_file_path_.c_str())) != 0)
    {
        return result;
    } 
    //建立与tracker ser的tcp连接,注意连接断开的重连
    tracker_server_ = tracker_get_connection_ex(&tracker_group_); //针对目前只有一台tracker-ser的情况时可以的.
    if (tracker_server_ == nullptr)
    {
        fdfs_client_destroy_ex(&tracker_group_);
        return errno != 0 ? errno : ECONNREFUSED;
    }
    fdfs_stat_ = true;
    return 0;
}

int FdfsUploader::RefreshTrackerConn()
{
    {
        std::lock_guard<std::mutex> lock(tracker_conn_mutex_);
        tracker_disconnect_server_ex(tracker_server_, true);
        fdfs_stat_ = false;
        tracker_server_ = tracker_get_connection_ex(&tracker_group_); //针对目前只有一台tracker-ser的情况时可以的.
    }
    if (tracker_server_ == nullptr)
    {
        AK_LOG_WARN << "Refresh tracker conn failed, then ReInit";
        return ReInit();
    }
    fdfs_stat_ = true;
    return 0;
}

int FdfsUploader::DeleteFile(const std::string& remote_filepath)
{
    /**
     * remote_filepath formate：/group1/M00/00/00/rBI9elovePuAF4DoAACqsICOt3g580_big.jpg
     */

    if(remote_filepath.length() == 0 || tracker_server_ == nullptr)
    {
        return -1;
    }

    // get file string as: group1/M00/00/00/rBI9elovePuAF4DoAACqsICOt3g580_big.jpg
    size_t position = remote_filepath.find_first_of("group");
    if (std::string::npos == position)
    {
        return -1;
    }

    std::string group_path = remote_filepath.substr(position);

    //get remote_file string as: M00/00/00/rBI9elovePuAF4DoAACqsICOt3g580_big.jpg
    position = group_path.find_first_of("/M");
    if (std::string::npos == position)
    {
        return -1;
    }

    std::string remote_file = group_path.substr(position + 1);

    // get group name as: group1
    std::string group_name = group_path.substr(0, position);

    int result = 0;
    {
        std::lock_guard<std::mutex> lock(tracker_conn_mutex_);
        result = storage_delete_file(tracker_server_, NULL, group_name.c_str(), remote_file.c_str());
    }
    if (result == 0)
    {
        AK_LOG_INFO << "delete file succeed, remote_file:" << remote_filepath;
    }
    else
    {
        //与tracker-ser断开链接了,重新连接
        if ((result = RefreshTrackerConn()) != 0)
        {
            AK_LOG_WARN << "Refresh, error no:" << result << " error info:" << STRERROR(result);
         }
        else
        {
            std::lock_guard<std::mutex> lock(tracker_conn_mutex_);
            result = storage_delete_file(tracker_server_, NULL, group_name.c_str(), remote_file.c_str());
        }
    }


    if(result != 0)
    {
        AK_LOG_WARN << "delete file " << remote_filepath << " fail, error no:" << result << "error info:" << STRERROR(result);
    }
    return result;
}

//retry_times:失败重试次数  eg:retry_times = 2, 代表最多进行三次上传尝试，到第一次成功为止
int FdfsUploader::UploadFile(const std::string& local_filepath, std::string& remote_filepath, int retry_times)
{
    assert(local_filepath.c_str());
    int result = 0;
    int store_path_index = 0;
    int meta_count;
    ConnectionInfo storage_server_info;
    ConnectionInfo* storage_server;
    FDFSMetaData meta_list[32];
    char remote_filename[256];
    const char* file_ext_name;
    
    result = GetStorageInfoFromTracker(storage_server_info, store_path_index);
    if(result != 0)
    {
        return result;
    }

    //与storage ser建立tcp连接
    if ((storage_server = tracker_connect_server(&storage_server_info, &result)) == NULL)
    {
        AK_LOG_WARN << "tracker_connect_server fail, error no:" << result << " error info:" << STRERROR(result)
                     << " storage info is: " << storage_server_info.ip_addr;
        if ((result = ReInit()) != 0)
        {
            AK_LOG_WARN << "ReInit, error no:" << result << " error info:" << STRERROR(result);
        }
        return -1;
    }
    memset(&meta_list, 0, sizeof(meta_list));
    meta_count = 0;
    //将本地文件路径 写入fdfs文件系统的meta(key-value)中,与已存入fdfs的文件地址 建立映射关系
    Snprintf(meta_list[meta_count].name, sizeof(meta_list[meta_count].name), LOCAL_FILE_KEY);
    Snprintf(meta_list[meta_count].value, sizeof(meta_list[meta_count].value), local_filepath.c_str());
    meta_count++;

    {
        std::lock_guard<std::mutex> lock(tracker_conn_mutex_);
        file_ext_name = fdfs_get_file_ext_name(local_filepath.c_str());
        
        for(int i = 0; i < retry_times + 1; i++)
        {
            char group_name[FDFS_GROUP_NAME_MAX_LEN + 1] = {0};
            Snprintf(group_name, sizeof(group_name), group_name_.c_str());
            result = storage_upload_by_filename(tracker_server_, storage_server, store_path_index,
                                                local_filepath.c_str(), file_ext_name, meta_list,
                                                meta_count, group_name, remote_filename);
            if (result == 0) // 上传成功
            {
                break;
            }
            AK_LOG_WARN << "fdfs upload failed, try times: " << i + 1;
        }
    }
    
    if (result != 0)
    {
        AK_LOG_WARN << "upload file fail, error no:" << result << " error info:" << STRERROR(result)
                     << " StorageServer info, ip:" << storage_server->ip_addr << " port:" << storage_server->port;

        tracker_disconnect_server_ex(storage_server, true);
        return result;
    }

    tracker_disconnect_server_ex(storage_server, true);
    
    remote_filepath = "/" + group_name_ + "/" + remote_filename;

    AK_LOG_INFO << "instance:" << this << ", upload success, localpath:" << local_filepath << " remote filepath: " << remote_filepath;

    return result;
}

bool FdfsUploader::DownloadFile(const std::string& remote_filepath, const std::string& local_filepath)
{
    if (remote_filepath.empty() || tracker_server_ == nullptr) 
    {
        AK_LOG_WARN << "Invalid remote filepath or uninitialized tracker server.";
        return false;
    }
    
    // 解析 group name 和 remote filename
    size_t position = remote_filepath.find("group");
    if (position == std::string::npos) 
    {
        AK_LOG_WARN << "Failed to find 'group' in remote filepath: " << remote_filepath;
        return false;
    }
    std::string group_path = remote_filepath.substr(position);

    position = group_path.find("/M");
    if (position == std::string::npos) 
    {
        AK_LOG_WARN << "Failed to find '/M' in group path: " << group_path;
        return false;
    }

    std::string remote_file = group_path.substr(position + 1); // 文件路径
    std::string group_name = group_path.substr(0, position);   // 组名

    AK_LOG_INFO << "FdfsDownloadFile group_name = " << group_name << ", remote_file = " << remote_file;

    // 获取 storage server 信息
    int result = 0;
    int store_path_index = 0;
    ConnectionInfo storage_server_info;

    if ((result = GetStorageInfoFromTracker(storage_server_info, store_path_index)) != 0) 
    {
        AK_LOG_WARN << "Failed to get storage info from tracker. Error code: " << result;
        return false;
    }

    // 与 storage server 建立连接
    ConnectionInfo* storage_server = tracker_connect_server(&storage_server_info, &result);
    if (storage_server == nullptr) 
    {
        AK_LOG_WARN << "Failed to connect to storage server, error no:" << result 
                     << " error info:" << STRERROR(result)
                     << " storage server info: " << storage_server_info.ip_addr;
        if ((result = ReInit()) != 0) 
        {
            AK_LOG_WARN << "ReInit failed, error no:" << result << " error info:" << STRERROR(result);
        }
        return false;
    }

    if (tracker_server_ == nullptr || storage_server == nullptr) 
    {
        AK_LOG_WARN << "tracker_server_ or storage_server is NULL. Cannot proceed with download.";
        if (storage_server != nullptr) 
        {
            tracker_disconnect_server_ex(storage_server, true);
        }
        return false;
    }

    // 下载文件，带重试机制
    {
        std::lock_guard<std::mutex> lock(tracker_conn_mutex_);
        for (int i = 0; i < 3; ++i) 
        {
            int64_t file_size = 0;
            result = storage_download_file_to_file(tracker_server_, storage_server,
                                                   group_name.c_str(), remote_file.c_str(),
                                                   local_filepath.c_str(), &file_size);

            if (result == 0) 
            {
                AK_LOG_INFO << "File downloaded successfully. Remote filepath = " 
                            << remote_filepath << ", local filepath = " << local_filepath;
                tracker_disconnect_server_ex(storage_server, true);
                return true;
            }

            AK_LOG_WARN << "Download attempt " << (i + 1) << " failed. Error code: "  << result << " Error info: " << STRERROR(result);
        }
    }

    AK_LOG_WARN << "FdfsDownloadFile failed after 3 attempts.";
    tracker_disconnect_server_ex(storage_server, true);  // 断开连接
    return false;
}

void FdfsUploader::SetUploadGroupName(const std::string& group_name)
{
    group_name_ = group_name;
}

int FdfsUploader::GetStorageInfoFromTracker(ConnectionInfo& storage_server_info, int& store_path_index)
{   
    int result = 0;
    if(tracker_server_ == nullptr)
    {   
        AK_LOG_WARN << "tracker server is null";
        if((result = ReInit()) != 0)
        {
            AK_LOG_WARN << "ReInit, error no:" << result << " error info:" << STRERROR(result);
            return -1;
        }
    }
    {
        std::lock_guard<std::mutex> lock(tracker_conn_mutex_);
        if ((result = tracker_query_storage_store_with_group(tracker_server_,
                group_name_.c_str(), &storage_server_info, &store_path_index)) != 0)
        {
            AK_LOG_WARN << "tracker_query_storage fail, error no: " << result
                         << ", error info: " << STRERROR(result)
                         << ", group name: " << group_name_.c_str()
                         << ", storage info is: " << storage_server_info.ip_addr;
        }
    }
    if (result != 0)
    {
        //与tracker-ser断开链接了,重新连接
        if ((result = RefreshTrackerConn()) != 0)
        {
            AK_LOG_WARN << "Refresh, error no:" << result << " error info:" << STRERROR(result);
            return -1;
        }

        {
            std::lock_guard<std::mutex> lock(tracker_conn_mutex_);
            if((result = tracker_query_storage_store_with_group(tracker_server_,
                    group_name_.c_str(), &storage_server_info, &store_path_index)) != 0)    //重连后需再query
            {
                AK_LOG_WARN << "tracker_query_storage fail, error no: " << result
                             << ", error info: " << STRERROR(result)
                             << ", group name: " << group_name_.c_str()
                             << ", storage info is: " << storage_server_info.ip_addr;
            }
        }
        if (result != 0) //重连后query还是失败，直接ReInit()
        {
            if((result = ReInit()) != 0)
            {
                AK_LOG_WARN << "ReInit, error no:" << result << " error info:" << STRERROR(result);
            }
            return -1;
        }
    }

    return 0;
}
