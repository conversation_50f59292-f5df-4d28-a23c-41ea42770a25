#ifndef __AKCS_BASE_FDFS_UPLOADER_H__
#define __AKCS_BASE_FDFS_UPLOADER_H__

#include "uploader.h"
#include "connection_pool.h"
#include "tracker_types.h"
#include <mutex>
#include <memory>


#define CONFIG_FILE_SIZE 64
#define FDFS_GROUP_NAME_MAX_LEN 16

class FdfsUploader : public Uploader
{
public:
    FdfsUploader():tracker_server_(nullptr),fdfs_stat_(false){}
    ~FdfsUploader();
    int Init(const std::string& config_filepath) override;
    int UploadFile(const std::string& local_filepath, std::string& remote_filepath, int retry_times) override;
    int DeleteFile(const std::string& remote_filepath);
    bool DownloadFile(const std::string& remote_filepath, const std::string& local_filepath);
    void SetUploadGroupName(const std::string& group_name);
    int ReInit();
    int RefreshTrackerConn();
    bool GetFdfsStat();
    
protected:
    std::mutex tracker_conn_mutex_;
    ConnectionInfo* tracker_server_;
    std::string config_file_path_;
    std::string group_name_;
    TrackerServerGroup tracker_group_;

private:
    bool fdfs_stat_;
    int GetStorageInfoFromTracker(ConnectionInfo& storage_server_info, int& store_path_index);
};

#endif //__AKCS_BASE_FDFS_UPLOADER_H__
