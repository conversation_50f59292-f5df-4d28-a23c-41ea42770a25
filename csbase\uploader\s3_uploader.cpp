#include "util.h"
#include <fstream>
#include "AkLogging.h"
#include "s3_uploader.h"
#include "ConfigFileReader.h"
#include <boost/filesystem/operations.hpp>

S3Uploader::~S3Uploader()
{
    Aws::ShutdownAPI(options_);
    
    AlibabaCloud::OSS::ShutdownSdk();
    if (s3_config_ != nullptr) {
        delete s3_config_;
        s3_config_ = nullptr;
    }
}

int S3Uploader::Init(const std::string& config_filepath)
{
    InitS3Conf(config_filepath);

    if(s3_config_->storage_type == CloudStorageType::OSS)
    {
        InitAliyunClient();
    }
    else if(s3_config_->storage_type == CloudStorageType::S3)
    {        
        InitS3Client();
    }
    else if(s3_config_->storage_type == CloudStorageType::UKD)
    {
        InitUkdClient();
    }

    return 0;
}

void S3Uploader::InitS3Conf(const std::string& config_filepath)
{
    s3_config_ = new S3_CONFIG;
    memset(s3_config_, 0, sizeof(S3_CONFIG));
    CConfigFileReader config_file_s3(config_filepath.c_str());
    //bucket目前只用到了"BucketForPic"，暂时先写死，后续要用别的再新增
    Snprintf(s3_config_->bucket_name, sizeof(s3_config_->bucket_name), config_file_s3.GetConfigName("BucketForPic"));
    Snprintf(s3_config_->video_bucket_name, sizeof(s3_config_->video_bucket_name), config_file_s3.GetConfigName("BucketForVideo"));
    Snprintf(s3_config_->endpoint, sizeof(s3_config_->endpoint), config_file_s3.GetConfigName("Endpoint"));
    Snprintf(s3_config_->region_id, sizeof(s3_config_->region_id), config_file_s3.GetConfigName("RegionID"));
    Snprintf(s3_config_->user, sizeof(s3_config_->user), config_file_s3.GetConfigName("User"));
    Snprintf(s3_config_->password, sizeof(s3_config_->password), config_file_s3.GetConfigName("Password"));
    Snprintf(s3_config_->s3_tag, sizeof(s3_config_->s3_tag), config_file_s3.GetConfigName("OSS_TAG"));
    const char* storage_type_cstr = config_file_s3.GetConfigName("STORAGE_TYPE");
    s3_config_->storage_type = ATOI(storage_type_cstr);
}

void S3Uploader::InitAliyunClient()
{
    AlibabaCloud::OSS::InitializeSdk();
    AlibabaCloud::OSS::ClientConfiguration conf;
    conf.connectTimeoutMs = 3000;
    conf.requestTimeoutMs = 3000;

    aliyun_client_ = std::make_shared<AlibabaCloud::OSS::OssClient>(s3_config_->endpoint, s3_config_->user, s3_config_->password, conf);
}

void S3Uploader::InitS3Client()
{
    options_.loggingOptions.logLevel = Aws::Utils::Logging::LogLevel::Error;
    Aws::InitAPI(options_);

    //s3连接 默认连接超时1s
    Aws::Client::ClientConfiguration cfg;
    cfg.endpointOverride = s3_config_->endpoint;  // S3服务器地址和端口
    cfg.scheme = Aws::Http::Scheme::HTTP;
    cfg.verifySSL = false;
    cfg.region = s3_config_->region_id;
    cfg.connectTimeoutMs = 3000;
    cfg.requestTimeoutMs = 3000;//Socket read timeouts 3s

    Aws::Auth::AWSCredentials cred(s3_config_->user, s3_config_->password);
    s3_client_ = std::make_shared<Aws::S3::S3Client>(cred, cfg, Aws::Client::AWSAuthV4Signer::PayloadSigningPolicy::Never,false);    
}

void S3Uploader::InitUkdClient()
{
    ukd_client_ = std::make_shared<ucloud::cppsdk::api::UFileClient>();
    //user:publicKey  password:privateKey  endpoint:proxySuffix
    ukd_client_->InitUFileClient(s3_config_->user, s3_config_->password, s3_config_->endpoint);
    ukd_client_->SetConnectionTimeoutMs(3000);
    ukd_client_->SetRequestTimeoutMs(3000);
}

//retry_times:失败重试次数  eg:retry_times = 2, 代表最多进行三次上传尝试，到第一次成功为止
int S3Uploader::UploadFile(const std::string& local_filepath, std::string& remote_filepath, int retry_times)
{   
    if (strlen(s3_config_->bucket_name) <= 0)
    {
        AK_LOG_FATAL << "s3 upload failed. bucket name is empty";
    }

    // motion限流会把本地文件删除,再进行上传会报错No response body,直接返回成功
    if (!fileExist(local_filepath.c_str()))
    {
        AK_LOG_WARN << "S3UploadFile Failed, file already deleted, local_filepath = " << local_filepath;
        return 0;
    }

    if(s3_config_->storage_type == CloudStorageType::OSS)
    {
        return OSSUploadFile(local_filepath, remote_filepath, retry_times, s3_config_->bucket_name);
    }
    else if(s3_config_->storage_type == CloudStorageType::S3)
    {
        return S3UploadFile(local_filepath, remote_filepath, retry_times, s3_config_->bucket_name, Aws::S3::Model::StorageClass::STANDARD);
    }
    else if(s3_config_->storage_type == CloudStorageType::UKD)
    {
        return UkdUploadFile(local_filepath, remote_filepath, retry_times, s3_config_->bucket_name); 
    }
    
    AK_LOG_WARN << "storage not found, type is " << s3_config_->storage_type;
    return -1;
}

//retry_times:失败重试次数  eg:retry_times = 2, 代表最多进行三次上传尝试，到第一次成功为止
int S3Uploader::UploadVideoFile(const std::string& local_filepath, std::string& remote_filepath, int retry_times)
{   
    if (strlen(s3_config_->video_bucket_name) <= 0)
    {
        AK_LOG_FATAL << "UploadVideoFile failed, bucket name is empty";
    }

    if(s3_config_->storage_type == CloudStorageType::OSS)
    {
        return OSSUploadFile(local_filepath, remote_filepath, retry_times, s3_config_->video_bucket_name);
    }
    else if(s3_config_->storage_type == CloudStorageType::S3)
    {
        return S3UploadFile(local_filepath, remote_filepath, retry_times, s3_config_->video_bucket_name, Aws::S3::Model::StorageClass::STANDARD_IA); //低频存储
    }
    else if(s3_config_->storage_type == CloudStorageType::UKD)
    {
        return UkdUploadFile(local_filepath, remote_filepath, retry_times, s3_config_->video_bucket_name); 
    }
    
    AK_LOG_WARN << "storage not found, type is " << s3_config_->storage_type;
    return -1;
}

int S3Uploader::S3UploadFile(const std::string& local_filepath, std::string& remote_filepath, int retry_times, 
                                const std::string& bucket_name, const Aws::S3::Model::StorageClass& storage_class)
{
    bool upload_success = false;
    
    Aws::S3::Model::PutObjectOutcome put_object_outcome;
    Aws::S3::Model::PutObjectRequest object_request;
    object_request.SetBucket(bucket_name);
    object_request.SetStorageClass(storage_class);
    
    object_request.SetKey(remote_filepath);
    const std::shared_ptr<Aws::IOStream> input_data = Aws::MakeShared<Aws::FStream>("SampleAllocationTag", local_filepath, std::ios_base::in | std::ios_base::binary);
    object_request.SetBody(input_data);

    //上传文件
    for(int i = 0; i < retry_times + 1; i++)
    {
        put_object_outcome = s3_client_->PutObject(object_request);
        if(put_object_outcome.IsSuccess())
        {
            upload_success = true;
            break;
        }
        AK_LOG_WARN << "s3 upload failed, try times: " << i + 1;
    }
    if (!upload_success)
    {
        auto error = put_object_outcome.GetError();
        AK_LOG_WARN << "upload file error. already try times:" << retry_times + 1 << "filePath:" << local_filepath 
                        <<  "   " <<  error.GetExceptionName() << ": "<< error.GetMessage();
        return -1;
    }
    return 0;
}
int S3Uploader::OSSUploadFile(const std::string& local_filepath, std::string& remote_filepath, int retry_times, const std::string& bucket_name)
{
    bool upload_success = false;

    AlibabaCloud::OSS::PutObjectOutcome put_object_outcome;
    for(int i = 0; i < retry_times + 1; i++)
    {
        put_object_outcome = aliyun_client_->PutObject(bucket_name, remote_filepath, local_filepath);
        if(put_object_outcome.isSuccess())
        {
            upload_success = true;
            break;
        }
        AK_LOG_WARN << "oss upload failed, try times: " << i + 1;
    }
    if (!upload_success)
    {
        AK_LOG_WARN << "upload file error.already retry once, filePath:" << local_filepath << " PutObject fail" << 
        ",code:" <<  put_object_outcome.error().Code() << ",message:" <<  put_object_outcome.error().Message() << ",requestId:" <<  put_object_outcome.error().RequestId();
        return -1;
    }
    return 0;
}

int S3Uploader::UkdUploadFile(const std::string& local_filepath, std::string& remote_filepath, int retry_times, const std::string& bucket_name)
{
    bool upload_success = false;
    int ret = 0;
    for(int i = 0; i < retry_times + 1; i++)
    {
        ret = ukd_client_->PutObject(bucket_name, remote_filepath, local_filepath);
        if(ret == 0)
        {
            upload_success = true;
            break;
        }
        AK_LOG_WARN << "ucloud upload failed, try times: " << i + 1;
    }
    
    if (!upload_success)
    {
        AK_LOG_WARN << "upload file error, error code=" << UFILE_LAST_RETCODE() << ", errmsg=" << UFILE_LAST_ERRMSG();
        return -1;
    }
    return 0;
}

//retry_times:失败重试次数  eg:retry_times = 2, 代表最多进行三次上传尝试，到第一次成功为止
bool S3Uploader::DownloadFile(const std::string& remote_filepath, const std::string& local_filepath)
{   
    if (strlen(s3_config_->bucket_name) <= 0)
    {
        AK_LOG_FATAL << "DownloadFile failed, bucket name is empty";
        return false;
    }

    // 创建本地文件的目录（如果不存在）
    std::string dir_path = local_filepath.substr(0, local_filepath.find_last_of("/\\"));
    if (!dir_path.empty()) {
        boost::filesystem::create_directories(dir_path);
    }

    if (s3_config_->storage_type == CloudStorageType::OSS)
    {
        return OSSDownloadFile(remote_filepath, local_filepath, s3_config_->bucket_name);
    }
    else if(s3_config_->storage_type == CloudStorageType::S3)
    {
        return S3DownloadFile(remote_filepath, local_filepath, s3_config_->bucket_name);
    }
    else if(s3_config_->storage_type == CloudStorageType::UKD)
    {
        return UkdDownloadFile(remote_filepath, local_filepath, s3_config_->bucket_name); 
    }

    return false;
}

//retry_times:失败重试次数  eg:retry_times = 2, 代表最多进行三次上传尝试，到第一次成功为止
bool S3Uploader::DownloadVideoFile(const std::string& remote_filepath, const std::string& local_filepath)
{   
    if (strlen(s3_config_->video_bucket_name) <= 0)
    {
        AK_LOG_FATAL << "DownloadVideoFile failed, bucket name is empty";
        return false;
    }

    // 创建本地文件的目录（如果不存在）
    std::string dir_path = local_filepath.substr(0, local_filepath.find_last_of("/\\"));
    if (!dir_path.empty()) {
        boost::filesystem::create_directories(dir_path);
    }

    if (s3_config_->storage_type == CloudStorageType::OSS)
    {
        return OSSDownloadFile(remote_filepath, local_filepath, s3_config_->video_bucket_name);
    }
    else if(s3_config_->storage_type == CloudStorageType::S3)
    {
        return S3DownloadFile(remote_filepath, local_filepath, s3_config_->video_bucket_name);
    }
    else if(s3_config_->storage_type == CloudStorageType::UKD)
    {
        return UkdDownloadFile(remote_filepath, local_filepath, s3_config_->video_bucket_name); 
    }

    return false;
}

bool S3Uploader::OSSDownloadFile(const std::string& remote_filepath, const std::string& local_filepath, const std::string& bucket_name)
{
    bool download_success = false;

    for (int i = 0; i < 3; i++)
    {
        auto outcome = aliyun_client_->GetObject(bucket_name, remote_filepath, local_filepath);
        
        if (outcome.isSuccess())
        {
            download_success = true;
            break;
        }
        AK_LOG_WARN << "Aliyun OSS download failed, try times: " << i + 1 
                     << ", error code: " << outcome.error().Code()
                     << ", message: " << outcome.error().Message()
                     << ", request id: " << outcome.error().RequestId();
    }

    if (!download_success)
    {
        AK_LOG_WARN << "Aliyun OSS download file failed after 3 attempts."
                     << " Bucket: " << bucket_name 
                     << " Remote path: " << remote_filepath 
                     << " Local path: " << local_filepath;
        return false;
    }

    AK_LOG_INFO << "Aliyun OSS download success. remote_filepath = " << remote_filepath << ", local_filepath = " << local_filepath;
    return true;
}

bool S3Uploader::S3DownloadFile(const std::string& remote_filepath, const std::string& local_filepath, const std::string& bucket_name)
{
    bool download_success = false;
            
    Aws::S3::Model::GetObjectRequest object_request;
    object_request.SetBucket(bucket_name);
    object_request.SetKey(remote_filepath);

    for(int i = 0; i < 3; i++)
    {
        auto get_object_outcome = s3_client_->GetObject(object_request);
        if (get_object_outcome.IsSuccess())
        {
            Aws::OFStream local_file;
            local_file.open(local_filepath.c_str(), std::ios::out | std::ios::binary);
            local_file << get_object_outcome.GetResult().GetBody().rdbuf();
            
            download_success = true;
            break;
        }
        auto error = get_object_outcome.GetError();
        AK_LOG_WARN << "AWS S3 download failed, try times: " << i + 1 
                     << ", error: " << error.GetExceptionName() 
                     << ", message: " << error.GetMessage();
    }

    if (!download_success)
    {
        AK_LOG_WARN << "AWS S3 download file failed after 3 attempts. Bucket: " << bucket_name
                     << ", Remote path: " << remote_filepath << ", Local path: " << local_filepath;
        return false;
    }
    
    AK_LOG_INFO << "AWS S3 download success, Remote path: " << remote_filepath << ", Local path: " << local_filepath;
    return true;
}

bool S3Uploader::UkdDownloadFile(const std::string& remote_filepath, const std::string& local_filepath, const std::string& bucket_name)
{
    bool download_success = false;

    for(int i = 0; i < 3; i++)
    {
        int ret = ukd_downloader_.DownloadAsFile(bucket_name, remote_filepath, local_filepath);
        if(ret == 0) 
        {
            download_success = true;
            break;
        }
        
        AK_LOG_WARN << "Ucloud download failed, try times: " << i + 1 
                     << ", error code: " << UFILE_LAST_RETCODE() 
                     << ", error message: " << UFILE_LAST_ERRMSG();
    }

    if (!download_success)
    {
        AK_LOG_WARN << "Ucloud download file failed after 3 attempts."
                     << " Bucket: " << bucket_name 
                     << " Remote path: " << remote_filepath 
                     << " Local path: " << local_filepath
                     << " Error code: " << UFILE_LAST_RETCODE()
                     << " Error message: " << UFILE_LAST_ERRMSG();
        return false;
    }

    AK_LOG_INFO << "Ucloud download success, Remote path: " << remote_filepath << ", Local path: " << local_filepath;
    return true;
}
