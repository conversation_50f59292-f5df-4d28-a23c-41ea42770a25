#ifndef __AKCS_BASE_S3_UPLOADER_H__
#define __AKCS_BASE_S3_UPLOADER_H__

#include "uploader.h"
//aws
#include <aws/core/Aws.h>
#include <aws/core/auth/AWSCredentialsProvider.h>
#include <aws/s3/S3Client.h>
#include <aws/s3/model/PutObjectRequest.h>
#include <iostream>
#include <fstream>
#include <sys/stat.h>
#include <aws/s3/model/GetObjectRequest.h>
#include <aws/s3/model/StorageClass.h>
#include <aws/core/http/HttpTypes.h>
//aliyun
#include <alibabacloud/oss/OssClient.h>
#include <ufile-cppsdk/api.h>

enum CloudStorageType
{
    OSS = 0,
    S3 = 1,
    UKD = 2,
};

typedef struct S3_CONFIG_T
{
    char endpoint[256];
    char region_id[64];
    char bucket_name[64];
    char video_bucket_name[64];
    char user[128];
    char password[128];
    char s3_tag[32];//上传的前缀 保证同个bucket 不同服务器能区分
    int storage_type;//存储类型 0-oss 1-aws 2-ucloud
}S3_CONFIG;

class S3Uploader : public Uploader
{
public:
    S3Uploader(): s3_config_(nullptr){}
    ~S3Uploader();
    int Init(const std::string& config_filepath) override;
    int UploadFile(const std::string& local_filepath, std::string& remote_filepath, int retry_times) override;
    int UploadVideoFile(const std::string& local_filepath, std::string& remote_filepath, int retry_times);
    bool DownloadFile(const std::string& remote_filepath, const std::string& local_filepath);
    bool DownloadVideoFile(const std::string& remote_filepath, const std::string& local_filepath);
    void InitS3Conf(const std::string& config_filepath);
    void InitAliyunClient();
    void InitS3Client();
    void InitUkdClient();
protected:
    S3_CONFIG* s3_config_;

    std::shared_ptr<Aws::S3::S3Client> s3_client_;
    std::shared_ptr<AlibabaCloud::OSS::OssClient> aliyun_client_;
    std::shared_ptr<ucloud::cppsdk::api::UFileClient> ukd_client_; //优刻得对象存储
    ucloud::cppsdk::api::UFileDownload ukd_downloader_; //优刻得对象存储
    Aws::SDKOptions options_;

private:
    int S3UploadFile(const std::string& local_filepath, std::string& remote_filepath, int retry_times, const std::string& bucket_name, const Aws::S3::Model::StorageClass& storage_class);
    int OSSUploadFile(const std::string& local_filepath, std::string& remote_filepath, int retry_times, const std::string& bucket_name);
    int UkdUploadFile(const std::string& local_filepath, std::string& remote_filepath, int retry_times, const std::string& bucket_name);

    bool S3DownloadFile(const std::string& remote_filepath, const std::string& local_filepath, const std::string& bucket_name);
    bool OSSDownloadFile(const std::string& remote_filepath, const std::string& local_filepath, const std::string& bucket_name);
    bool UkdDownloadFile(const std::string& remote_filepath, const std::string& local_filepath, const std::string& bucket_name);
};

#endif // __AKCS_BASE_S3_UPLOADER_H__
