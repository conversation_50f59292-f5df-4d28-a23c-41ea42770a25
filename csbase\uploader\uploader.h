#ifndef __AKCS_BASE_UPLOADER_H__
#define __AKCS_BASE_UPLOADER_H__

#include <string>

class Uploader
{
public:
    virtual ~Uploader(){}
    /**
     * @brief 初始化上传客户端
     * 
     * @param config_filepath 配置文件路径
     * @return int
     */
    virtual int Init(const std::string& config_filepath) = 0;
    /**
     * @brief 上传文件
     * 
     * @param local_filepath        本地文件路径
     * @param remote_filepath       上传到服务器的路径
     * @param retry_times           上传失败的重试次数 eg:retry_times = 2, 代表最多进行三次上传尝试，到第一次成功为止
     * @return int 
     */
    virtual int UploadFile(const std::string& local_filepath, std::string& remote_filepath, int retry_times) = 0;
};

#endif //__AKCS_BASE_UPLOADER_H__
