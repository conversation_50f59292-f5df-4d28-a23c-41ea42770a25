#include "util.h"
#include <sstream>
#include <unistd.h>
#include <net/if.h>
#include <sys/ioctl.h>
#include <chrono>
#include "AkLogging.h"
#include <ctime>
#include <boost/algorithm/string.hpp>
#include <boost/functional/hash.hpp>
#include <boost/format.hpp>
#include <boost/crc.hpp>
#include <encrypt/Md5.h>
#include <json/json.h>
#include "util_cstring.h"
#include "ConfigFileReader.h"
#include <arpa/inet.h>

int AkParseAddr(const std::string& address,std::string& host, int& port)
{
    if (address.empty()) {
        return -1;
    }

    size_t index = address.rfind(':');
    if (index == std::string::npos) {
        return -1;
    }

    if (index == address.size() - 1) {
        return -1;
    }

    port = ATOI(&address[index + 1]);
    host = std::string(address, 0, index);
    return 0;
}


//类似system调用,可用于接收cmd运行中printf的内容
int AkSystem(const char * cmd, std::string& ret)
{
    FILE * fp;   
    if ((fp = popen(cmd, "r")) == NULL)
    {
        return -1;
    }
    else
    {
        char temp_str[4096];
        memset(temp_str, 0, 4096);
        while (fgets(temp_str, 4096, fp) != NULL)
        {
            ret += temp_str;
        }
        pclose(fp);
        return 0;
    }
}

bool fileExist(const char *filename)
{
	return access(filename, 0) == 0;
}


// 从而 /etc/ip 获取本地内网的IP地址
const std::string GetInnerIPAddr()
{
    CConfigFileReader config_file("/etc/ip");
    
    // inner ip.
    const char* value = config_file.GetConfigName("SERVER_INNER_IP");
    if (value != nullptr)
    {
        return std::string(value);
    }

    return std::string();
}


const std::string GetOuterIPv6Addr()
{
    CConfigFileReader config_file("/etc/ip");
    
    // 获取 IPv6 地址
    const char* value = config_file.GetConfigName("SERVERIPV6");
    if (value != nullptr)
    {
        return std::string(value);
    }

    return std::string();
}

const std::string GetOuterIPv4Addr()
{
    CConfigFileReader config_file("/etc/ip");
    
    // 获取 IPv4 地址
    const char* value = config_file.GetConfigName("SERVERIP");
    if (value != nullptr)
    {
        return std::string(value);
    }

    return std::string();
}

//获取本地网卡0的IP地址
const std::string GetEth0IPAddr()
{
    int inet_sock;
    struct sockaddr_in sin;
    struct ifreq ifr;  
    inet_sock = socket(AF_INET, SOCK_DGRAM, 0);  
    ::strncpy(ifr.ifr_name, "eth0", IFNAMSIZ);
    ifr.ifr_name[IFNAMSIZ - 1] = 0;
    ::ioctl(inet_sock, SIOCGIFADDR, &ifr);  
    ::close(inet_sock);
    memcpy(&sin, &ifr.ifr_addr, sizeof(sin));
    return inet_ntoa(sin.sin_addr);
}

int GetRandomNum(int range)
{
    if (range < 1) //容错
    {
        return 0;
    }
    ::srand((unsigned)time(nullptr));
    int random = ::rand() % range;
    return random;
}

//获取n位随机数
int GetNbitRandomNum(int n_bit)
{
    if (n_bit < 1) //容错
    {
        return 0;
    }
    ::srand((unsigned)time(nullptr));
    int random_num = ::rand() % (int)(pow(10, n_bit) - pow(10, n_bit - 1)) + pow(10, n_bit - 1);
    return random_num;
}

uint32_t PeekInt32(const char* data, const uint32_t len)
{
	assert(len >= sizeof(int32_t));
    int32_t be32 = 0;
    ::memcpy(&be32, data, sizeof be32);
    return ::ntohl(be32);
}

int SwitchHandle(int value, int pos)
{
    return value & (1 << pos) ? 1 : 0;
}


bool HttpCheckSqlParam(std::string param)
{
    string key[8] = {"*", "=", "%", "/", "|", "&", "^", "#"};
    for (int i = 0; i < 8; i++)
    {
        if (param.find(key[i]) != std::string::npos)
        {
            return false;
        }
    }
    return true;
}

void ConnectNsqErrorMutt(const std::string& addr, const std::string& app)
{
    char error[512] = "";
    snprintf(error, sizeof(error), "Connect nsqd-%s error pid=%d app=%s",  
    addr.c_str(), getpid(), app.c_str());
    
    //nsq已经异常，通过mutt发送
    char cmd[1024] = "";
    snprintf(cmd, sizeof(cmd), "echo \"%s\" | mutt -s \"Connect NSQ Error\"  -b <EMAIL> -c  <EMAIL> -c <EMAIL> &", error);
    system(cmd);
}


//判断保留ip
bool IsReservedIp(const std::string& ip)
{
    if(ip.size() == 0)
    {
        return true;
    }

    //以保留ip的前两位来判断
    const std::string address_blocks[] = {
        "0.0",
        "169.254"
    };
    const int num = sizeof(address_blocks) / sizeof(*address_blocks);

    for (int i = 0; i < num; i++) 
    {
        //匹配开头
        if (ip.compare(0, address_blocks[i].size(), address_blocks[i]) == 0)
        {
            return true;
        }
    }
    return false;
}


int ATOI(const char* str)
{
    if ((str == NULL) || (strlen(str) <= 0))
    {
        return 0;
    }

    return atoi(str);
}


float STOF(const char* str)
{
    //防止传入空指针或空字符串
    if ((str == NULL) || (strlen(str) == 0))
    {
        return 0.0f;
    }
    //防止传入非数字字符串
    try
    {
        return std::stof(str);
    } 
    catch (...) 
    {
        return 0.0f;
    }
}

uint64_t STOULL(const std::string str)
{
    if (str.size() == 0)
    {
        return 0;
    }

    return std::stoull(str);
}

// 将 IP 地址字符串转换为整数
unsigned int IpStringToInt(const std::string& ip) {
    unsigned int result = 0;
    unsigned int octet = 0;

    for (char c : ip) {
        if (c == '.') {
            result = (result << 8) + octet;
            octet = 0;
        } else {
            octet = octet * 10 + (c - '0');
        }
    }

    result = (result << 8) + octet; // 处理最后一个 octet

    return result;
}

// 判断是否属于 10.0.0.0/8, **********/12, ***********/16 网段
bool IsInLocalNetSegment(const std::string& ip) {
    unsigned int ipInt = IpStringToInt(ip);

    // 10.0.0.0/8 的范围是 167772160 到 184549375
    if (ipInt >= 167772160 && ipInt <= 184549375){
        return true;
    }
    
    // **********/12 的范围是 2886729728 到 2887778303
    if(ipInt >= 2886729728 && ipInt <= 2887778303){
        return true;
    }

    // ***********/16 的范围是 3232235520 到 3232301055
    if(ipInt >= 3232235520 && ipInt <= 3232301055){
        return true;
    }

    return false;
}


std::string GetAccessibleFloor(const std::string &origin_floor, const std::string &append_floor)
{
    if (strstr(origin_floor.c_str(), kAllFloor.c_str()) || strstr(append_floor.c_str(), kAllFloor.c_str()))
    {
        return kAllFloor;
    }

    // 若floor为空,默认下发0
    if (origin_floor.empty() && append_floor.empty())
    {
        return kDefaultFloor;
    }

    // floor去重操作: 若apt_floor为"5",access_floor为"5;6;7",取并集为"5;6;7"
    std::set<std::string> accessible_floor_set;
    SplitString(origin_floor, ";", accessible_floor_set);
    SplitString(append_floor, ";", accessible_floor_set);

    std::ostringstream accessible_floor_stream;
    for (const auto &accessible_floor : accessible_floor_set) 
    {
        if (accessible_floor != *accessible_floor_set.rbegin()) 
        {
            accessible_floor_stream << accessible_floor << ";";
        } 
    	else 
        {
            accessible_floor_stream << accessible_floor;
        }
    }

    return accessible_floor_stream.str();
}

std::string GetIntersectionFloor(const std::string &temp_key_floor, const std::string &apt_user_floor, const int &is_follow_my_access) {
    if (apt_user_floor.empty()) {
        return kDefaultFloor; 
    }
    // 若is_follow_my_access为1则跟随用户可达楼层
    if (is_follow_my_access == 1) {
        return apt_user_floor;
    } else if (temp_key_floor.empty()) {
        return kDefaultFloor;
    }

    // 如果apt_user_floor是全部楼层，直接返回temp_key_floor勾选的楼层
    if (apt_user_floor == kAllFloor) {
        return temp_key_floor;
    }

    std::set<std::string> temp_key_set;
    std::set<std::string> apt_user_set;
    SplitString(temp_key_floor, ";", temp_key_set);
    SplitString(apt_user_floor, ";", apt_user_set);

    std::set<std::string> intersection_set;
    std::set_intersection(temp_key_set.begin(), temp_key_set.end(),
                          apt_user_set.begin(), apt_user_set.end(),
                          std::inserter(intersection_set, intersection_set.begin()));
    if (intersection_set.empty()) {
        return kDefaultFloor;
    }

    std::ostringstream intersection_stream;
    for (const auto &floor : intersection_set) {
        if (floor != *intersection_set.rbegin()) {
            intersection_stream << floor << ";";
        } else {
            intersection_stream << floor;
        }
    }

    return intersection_stream.str();
}

bool InList(const std::list<int>& lists, int val) 
{
    for (const int& element : lists) {
        if (element == val) {
            return true;
        }
    }
    return false;
}

void Snprintf(char *dst, int len, const char* src)
{
    snprintf(dst, len, "%s", src);
}

std::string GetHttpResponseMsg(int result, const std::string& message, const std::string& data)
{
    Json::Value item;
    Json::FastWriter writer;
    item["result"] = result;
    item["message"] = message;

    if(data.size() > 0)
    {
        Json::Value datas;
        datas.append(data);
        item["datas"] = datas;
    }

    std::string msg_json = writer.write(item);
    return msg_json;
}

uint32_t GenRandUint32TraceId()
{
    std::srand(static_cast<unsigned int>(std::time(nullptr)));
    uint32_t random_id = (static_cast<uint32_t>(rand()) << 16) | rand();
    return random_id;
}

uint32_t ATOUI(const char* str)
{
    if(str == nullptr)
    {
        return 0;
    }    
    // 用于存储转换后的结束位置
    char* end_ptr;

    // 10表示使用十进制
    uint32_t result = std::strtoul(str, &end_ptr, 10); 

    //转换失败
    if (*end_ptr != '\0') {
        return 0;
    }

    return result;
}

uint64_t ATOULL(const char* str)
{
    if(str == nullptr)
    {
        return 0;
    }
    char* end_ptr; // 用于标识转换结束的指针
    uint64_t result = std::strtoull(str, &end_ptr, 10); // 10 表示十进制
    // end_ptr 将指向字符串的第一个非数字字符
    // 如果转换成功，end_ptr 将指向字符串的结尾处
    if (*end_ptr != '\0') {
        return 0;
    }

    return result;
}

// ipv6 = "2409:8934:1ab4:3e12:17ca:e374:f082:ff48";
bool IsIpv6(const std::string& ip) 
{
    return std::count(ip.begin(), ip.end(), ':') >= 2;
}


bool IsOfficeRole(int role) 
{
    return  role == ACCOUNT_ROLE_OFFICE_ADMIN || role == ACCOUNT_ROLE_OFFICE_MAIN || role == ACCOUNT_ROLE_OFFICE_NEW_PER || role == ACCOUNT_ROLE_OFFICE_NEW_ADMIN;
}

bool IsOfficeNewRole(int role) 
{
    return  role == ACCOUNT_ROLE_OFFICE_NEW_PER || role == ACCOUNT_ROLE_OFFICE_NEW_ADMIN;
}

std::string GetOfficeRoleStr() 
{
    std::stringstream ss;
    ss << ACCOUNT_ROLE_OFFICE_ADMIN<< "," << ACCOUNT_ROLE_OFFICE_MAIN << "," << ACCOUNT_ROLE_OFFICE_NEW_PER << "," << ACCOUNT_ROLE_OFFICE_NEW_ADMIN;
    return ss.str();
}

bool IsCommunityRole(int role) 
{
    return  role == ACCOUNT_ROLE_COMMUNITY_MAIN || role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT;
}

bool IsResidentMainRole(int role) 
{
    return  role == ACCOUNT_ROLE_COMMUNITY_MAIN || role == ACCOUNT_ROLE_PERSONNAL_MAIN;
}

//通过从账号来找主账户，来明确node
bool IsResidentSlaveRole(int role) 
{
    return  role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT || role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT;
}

int GetCommunityMainRole() 
{
    return ACCOUNT_ROLE_COMMUNITY_MAIN;
}

std::string GetCameraNameByChannelID(uint8_t channel_id)
{
    switch(channel_id)
    {
        case 0:
            return "Main";
        case 1:
            return "Auxiliary"; //辅助摄像头
        default : 
            return "Main"; //默认主摄像头
    }
}

// channel_id：0=主摄像头 1=辅摄像头
uint8_t GetChannelIDByCameraName(const std::string& camera_name)
{
    if (camera_name == "Main")
    {
        return 0;
    }
    else if (camera_name == "Auxiliary")
    {
        return 1;
    }
    //默认主摄像头
    else 
    {
        return 0;
    }
}

std::string GetFlowUUID(const std::string& mac, const std::string& camera, int stream_id)
{
    uint8_t channel_id = GetChannelIDByCameraName(camera);
    int true_stream_id = stream_id;
    //默认主码流
    if (true_stream_id <= 0)
    {
        true_stream_id = 1;
    }
    std::string flow_uuid = mac + "ch" + std::to_string(channel_id) + "stm" + std::to_string(true_stream_id);
    return flow_uuid;
}

bool IsTestServer(const std::string &cloud_env)
{
    if(cloud_env == "test_aws" || cloud_env == "test84" || cloud_env == "test_hz")
    {
        return true;
    }
    return false;
}

bool GetFirmwareInfo(const std::string &firmware, int &model, int &big_ver, int &small_ver)
{
    std::vector<std::string> vec;
    SplitString(firmware, ".", vec);
    if (vec.size()  != 4)
    {
        return false;
    }

    model = ATOI(vec[0].c_str());
    big_ver = ATOI(vec[2].c_str());
    small_ver = ATOI(vec[3].c_str());
    return true;
}

uint32_t GetSubnetValue(const std::string& ip, const std::string& mask)
{
    if (ip.empty() || mask.empty())
    {
        return 0;
    }
    
    struct in_addr tmp_ip, tmp_mask, subnet;

    // 将IP和子网掩码字符串转换为网络字节序整数
    if (inet_pton(AF_INET, ip.c_str(), &tmp_ip) != 1 || 
        inet_pton(AF_INET, mask.c_str(), &tmp_mask) != 1) {
        AK_LOG_WARN << "GetSubnetValue failed, ip: " << ip << ", mask: " << mask;
        return 0;
    }
    
    // 计算网段：IP & 掩码
    subnet.s_addr = tmp_ip.s_addr & tmp_mask.s_addr;
    
    // 返回网段值（主机字节序）
    return ntohl(subnet.s_addr);

}

// 设置线程名称函数声明
void SetThreadName(pthread_t thread, const char* name)
{
    // 线程名称长度限制为16个字符（包括结束符）
    pthread_setname_np(thread, name);
}
