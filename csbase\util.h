#ifndef __AKCS_BASE_UTIL_H__
#define __AKCS_BASE_UTIL_H__
#include <set>
#include <vector>
#include <map>
#include <list>

#include "UtilPdu.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>
#include <assert.h>
#include <stdarg.h>
#include <pthread.h>
#include <time.h>
#include <sys/time.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <random>
#include <ctime>
#include <chrono>
#include "Singleton.h"
#include <mutex>
#include <math.h>
#include "BasicDefine.h"
#include "util_string.h"
#include "util_time.h"
#include "util_relay.h"


#define PHONE_DETECT_NUM    (7)

static const std::string kAllFloor = "129";
static const std::string kDefaultFloor = "0";

using AkcsStringSet = std::set<std::string>;

int AkParseAddr(const std::string& address,std::string& host, int& port);
int AkSystem(const char * cmd, std::string& ret);

bool fileExist(const char *filename);

// 从而 /etc/ip 获取本地内网的IP地址
const std::string GetInnerIPAddr();

const std::string GetOuterIPv4Addr();
const std::string GetOuterIPv6Addr();


//获取本地网卡0的IP地址
const std::string GetEth0IPAddr();
int GetRandomNum(int range);
int GetNbitRandomNum(int n_bit);
uint32_t PeekInt32(const char* data, const uint32_t len);

int SwitchHandle(int value, int pos);   //数据库switch字段处理 按位与
bool HttpCheckSqlParam(std::string param);
void ConnectNsqErrorMutt(const std::string& addr, const std::string& app);

bool IsReservedIp(const std::string& ip);

int ATOI(const char* str);
float STOF(const char* str);
uint32_t ATOUI(const char* str);
uint64_t STOULL(const std::string str);
uint32_t ATOUI(const char* str);

// 将 IP 地址字符串转换为整数
unsigned int IpStringToInt(const std::string& ip);

// 判断IP是否属于本地网段：10.0.0.0/8, **********/12, ***********/16
bool IsInLocalNetSegment(const std::string& ip);

//apt_floor和access_floor去重操作
std::string GetAccessibleFloor(const std::string &origin_floor, const std::string &append_floor);
//temp_key_floor和unit_apt_floor取交集操作
std::string GetIntersectionFloor(const std::string &temp_key_floor, const std::string &unit_apt_floor, const int &is_follow_my_access);

bool InList(const std::list<int>& lists, int val);

void Snprintf(char *dst, int len, const char* src);

uint64_t ATOULL(const char* str);
std::string GetHttpResponseMsg(int result, const std::string& message, const std::string& data = "");
uint32_t GenRandUint32TraceId();

bool IsIpv6(const std::string& ip);
bool IsOfficeRole(int role);
bool IsOfficeNewRole(int role);
std::string GetOfficeRoleStr();
bool IsCommunityRole(int role);
bool IsResidentMainRole(int role);
//通过从账号来找主账户，来明确node
bool IsResidentSlaveRole(int role);
int GetCommunityMainRole();

std::string GetCameraNameByChannelID(uint8_t channel_id);
uint8_t GetChannelIDByCameraName(const std::string& camera_name);
std::string GetFlowUUID(const std::string& mac, const std::string& camera, int stream_id);

//added by chenyc,2021.12.23
#ifndef DISALLOW_COPY_MOVE_AND_ASSIGN
#define DISALLOW_COPY_MOVE_AND_ASSIGN(TypeName) TypeName(const TypeName&) = delete; TypeName(const TypeName&&) = delete;  TypeName& operator=(const TypeName&) = delete
#endif //DISALLOW_COPY_MOVE_AND_ASSIGN


bool IsTestServer(const std::string &cloud_env);
bool GetFirmwareInfo(const std::string &firmware, int &model, int &big_ver, int &small_ver);

uint32_t GetSubnetValue(const std::string& ip, const std::string& mask);
void SetThreadName(pthread_t thread, const char* name);

#endif //__AKCS_BASE_UTIL_H__

