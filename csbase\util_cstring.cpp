/// \file waString.cpp
///  String类实现文件

#include <cstdio>
#include <cstdarg>
#include <cstring>
#include <set>
#include <fstream>
#include <algorithm>
#include "util_cstring.h"
#include <assert.h>
//#include <boost/algorithm/string.hpp>
////////////////////////////////////////////////////////////////////////////////

/// \defgroup waString waString相关全局函数

/// \ingroup waString
/// \fn std::string itos( const long i, const ios::fmtflags base )
/// long int转换为string
/// \param i long int或者int
/// \param base 转换进制参数,可选
/// - ios::dec 10进制
/// - ios::oct 8进制
/// - ios::hex 16进制
/// - 默认为10进制
/// \return 返回结果string,转换失败返回"0"
std::string itos(const long i, const std::ios::fmtflags base)
{
    char format[] = "%ld";
    if (base == std::ios::oct)
    {
        strcpy(format, "%o");
    }
    else if (base == std::ios::hex)
    {
        strcpy(format, "%X");
    }

    // try
    int strlen = 32;
    char* buf = new char[strlen];
    memset(buf, 0, strlen);
    int size = snprintf(buf, strlen, format, i);
    if (size >= strlen)
    {
        // retry
        delete[] buf;
        buf = new char[size + 1];
        memset(buf, 0, size + 1);
        snprintf(buf, size + 1, format, i);
    }

    std::string result(buf);
    delete[] buf;
    return result;
}

/// \ingroup waString
/// \fn long stoi( const std::string &s, const ios::fmtflags base )
/// string转换为long int
/// \param s std::string
/// \param base 转换进制参数,可选
/// - ios::dec 10进制
/// - ios::oct 8进制
/// - ios::hex 16进制
/// - 默认为10进制
/// \return 返回结果long int,转换失败返回0
long stoi(const std::string& s, const std::ios::fmtflags base)
{
    int ibase = 10;
    char* ep;

    if (base == std::ios::hex)
    {
        ibase = 16;
    }
    else if (base == std::ios::oct)
    {
        ibase = 8;
    }

    CString ps = s;
    ps.Trim();
    return strtol(ps.c_str(), &ep, ibase);
}

/// \ingroup waString
/// \fn std::string ftos( const doule f, const int ndigit )
/// double转换为string
/// \param f double
/// \param ndigit 小数点后保留位数,默认为2
/// \return 转换成功返回string,否则返回"0"
std::string ftos(const double f, const int ndigit)
{
    int fmtlen = 10;
    int strlen = 64;
    int buflen;

    // create format std::string
    char* fmt = new char[fmtlen];
    memset(fmt, 0, fmtlen);
    buflen = snprintf(fmt, fmtlen, "%%.%df", ndigit);
    if (buflen >= fmtlen)
    {
        delete[] fmt;
        fmt = new char[buflen + 1];
        memset(fmt, 0, buflen + 1);
        snprintf(fmt, buflen + 1, "%%.%df", ndigit);
    }

    // convert
    char* str = new char[strlen];
    memset(str, 0, strlen);
    buflen = snprintf(str, strlen, fmt, f);
    if (buflen >= strlen)
    {
        delete[] str;
        str = new char[buflen + 1];
        memset(str, 0, buflen + 1);
        snprintf(str, buflen + 1, fmt, f);
    }

    std::string s = str;
    delete[] fmt;
    delete[] str;
    return s;
}

/// \ingroup waString
/// \fn double stof( const std::string &s )
/// string转换为double
/// \param s std::string
/// \return 转换成功返回double,否则返回0
double stof(const std::string& s)
{
    char* ep;
    return strtod(s.c_str(), &ep);
}

/// \ingroup waString
/// \fn bool isgbk( const unsigned char c1, const unsigned char c2 )
/// 判断一个双字节字符是否是GBK编码汉字
/// \param c1 双字节字符1
/// \param c2 双字节字符2
/// \retval true 是
/// \retval false 否
bool isgbk(const unsigned char c1, const unsigned char c2)
{
    if ((c1 >= 0x81 && c1 <= 0xFE) && ((c2 >= 0x40 && c2 <= 0x7E) || (c2 >= 0xA1 && c2 <= 0xFE)))
    {
        return true;
    }
    else
    {
        return false;
    }
}

/// \ingroup waString
/// \fn std::string va_sprintf( va_list ap, const std::string &format )
/// 可变参数字符串格式化，与va_start()、va_end()宏配合使用
/// \param format 字符串格式
/// \param ap 可变参数列表
/// \return 格式化字符串结果
std::string va_sprintf(va_list ap, const std::string& format)
{
    int strlen = 2560;
    char* buf = new char[strlen];
    memset(buf, 0, strlen);

    int size = vsnprintf(buf, strlen, format.c_str(), ap);
    if (size >= strlen)
    {
        delete[] buf;
        buf = NULL;
        buf = new char[size + 1];
        memset(buf, 0, size + 1);
        vsnprintf(buf, size + 1, format.c_str(), ap);
    }

    std::string result = buf;
    delete[] buf;
    buf = NULL;
    return result;
}

/// \ingroup waString
/// \fn std::string va_str( const char *format, ... )
/// 格式化字符串并返回，各参数定义与标准sprintf()函数完全相同
/// \return 格式化字符串结果
std::string va_str(const char* format, ...)
{
    va_list ap;
    va_start(ap, format);
    std::string result = va_sprintf(ap, format);
    va_end(ap);
    return result;
}

/// 返回 char* 型结果，调用者必须调用 delete[] 释放所返回内存
/// \return char*类型数据结果
char* CString::c_char() const
{
    size_t size = this->length();
    char* buf = new char[ size + 1 ];
    memset(buf, 0, size);
    strncpy(buf, this->c_str(), size);
    return buf;
}

/// 返回字符数量，GBK汉字算作一个字符
/// \return 字符数量
std::string::size_type CString::w_length() const
{
    size_t wlen = 0;
    size_t len = this->length();

    for (size_t i = 0; i < len; ++i)
    {
        if (i < (len - 1) && isgbk(this->at(i), this->at(i + 1)))
        {
            ++i;
        }
        ++wlen;
    }

    return wlen;
}

/// 截取子字符串,避免出现半个汉字
/// 若截取结果的首尾为半个汉字则删除,删除半个汉字后结果可能为空字符串,
/// 该函数避免在截取时将一个完整汉字分开,对字符串中原有的不完整汉字字符不作处理
/// \param pos 起始位置,默认为0,单字节计数方式
/// \param n 要截取的字符串长度,默认为到末尾,单字节计数方式
/// \return 所截取的字符串
CString CString::w_substr(const std::string::size_type pos,
                          const std::string::size_type n) const
{
    size_t len = this->length();
    if (len <= 0 || pos >= len || n <= 0)
    {
        return CString("");
    }

    size_t from = pos;
    size_t to = std::min(pos + n, len);

    // location
    for (size_t i = 0; i < to; ++i)
    {
        if ((i + 1) < len && isgbk(this->at(i), this->at(i + 1)))
        {
            if (i == from - 1)
            {
                ++from;
            }
            else if (i == to - 1)
            {
                --to;
            }
            ++i;
        }
    }

    // substr
    if (to > from)
    {
        return CString(this->substr(from, to - from));
    }
    else
    {
        return CString("");
    }
}

/// 清除左侧空白字符
/// \param blank 要过滤掉的空白字符列表,默认为webapp::BLANK_CHARS
void CString::TrimLeft(const std::string& blank)
{
    while (this->length() > 0 && blank.find(this->at(0)) != npos)
    {
        this->erase(0, 1);
    }
}

/// 清除右侧空白字符
/// \param blank 要过滤掉的空白字符列表,默认为webapp::BLANK_CHARS
void CString::TrimRight(const std::string& blank)
{
    while (this->length() > 0 && blank.find(this->at(length() - 1)) != npos)
    {
        erase(this->length() - 1, 1);
    }
}

/// 清除两侧空白字符
/// \param blank 要过滤掉的空白字符列表,默认为webapp::BLANK_CHARS
void CString::Trim(const std::string& blank)
{
    this->TrimLeft(blank);
    this->TrimRight(blank);
}

/// 从左边截取指定长度子串
/// \param n 要截取的字符串长度,若长度超出则返回原字符串
/// \return 所截取的字符串
CString CString::Left(const std::string::size_type n) const
{
    size_t len = this->length();
    len = (n > len) ? len : n;
    return CString(this->substr(0, len));
}

/// 从中间截取指定长度子串
/// \param pos 开始截取的位置
/// \param n 要截取的字符串长度,若长度超出则返回原字符串,默认为到末尾
/// \return 所截取的字符串
CString CString::Mid(const std::string::size_type pos,
                     const std::string::size_type n) const
{
    if (pos > this->length())
    {
        return CString("");
    }
    return CString(this->substr(pos, n));
}

/// 从右边截取指定长度子串
/// \param n 要截取的字符串长度,若长度超出则返回原字符串
/// \return 所截取的字符串
CString CString::Right(const std::string::size_type n) const
{
    size_t len = this->length();
    len = (n > len) ? n : len;
    return CString(this->substr(len - n, n));
}

/// 调整字符串长度
/// \param n 新字符串长度,若小于当前长度则截断,若大于当前长度则补充空白字符
void CString::resize(const std::string::size_type n)
{
    size_t len = this->length();
    if (n < len)
    {
        *this = this->substr(0, n);
    }
    else if (n > len)
    {
        for (size_t i = 0; i < (n - len); ++i)
        {
            this->append(" ");
        }
    }
}

/// 统计指定子串出现的次数
/// \param str 要查找的子串
/// \return 子串不重复出现的次数
int CString::count(const std::string& str) const
{
    size_t pos = 0;
    size_t count = 0;
    size_t step = str.length();

    while ((pos = this->find(str, pos)) != npos)
    {
        ++count;
        pos += step;
    }

    return count;
}

/// 根据分割符分割字符串
/// \param tag 分割标记字符串
/// \param limit 分割次数限制,默认为0即不限制
/// \param mode 结果返回模式,可选
/// - CString::SPLIT_IGNORE_BLANK 忽略连续多个分隔符，返回结果不含空字段
/// - CString::SPLIT_KEEP_BLANK 不忽略连续多个分隔符，返回结果包含空字段
/// - 默认为String::SPLIT_IGNORE_BLANK
/// \return 分割结果字符串数组 vector<CString>
std::vector<CString> CString::split(const std::string& tag, const int limit,
                                    const split_mode mode) const
{
    std::string src = *this;
    std::string curelm;
    std::vector<CString> list;
    int count = 0;

    list.clear();
    if (tag.length() > 0 && src.length() > 0)
    {
        // how to split
        size_t pos = src.find(tag);

        while (pos < src.length())
        {
            curelm = src.substr(0, pos);

            // is keep blank
            if (!(mode == SPLIT_IGNORE_BLANK && curelm.length() == 0))
            {
                list.push_back(curelm);
                ++count;
            }

            // split
            src = src.substr(pos + tag.length());
            pos = src.find(tag);

            if (limit > 0 && count >= limit)
            {
                break;
            }
        }

        // is keep blank
        if (!(mode == SPLIT_IGNORE_BLANK && src.length() == 0))
        {
            list.push_back(src);
        }
    }

    return list;
}

/// 转换字符串为MAP结构(map<std::string,std::string>)
/// \param itemtag 表达式之间的分隔符,默认为"&"
/// \param exptag 表达式中变量名与变量值之间的分隔符,默认为"="
/// \return 转换结果 map<std::string,std::string>
std::map<std::string, std::string> CString::tomap(const std::string& itemtag,
        const std::string& exptag) const
{
    std::map<std::string, std::string> hashmap;

    if (itemtag != "" && exptag != "")
    {
        std::vector<CString> items = this->split(itemtag);
        std::string name, value;
        std::size_t pos;
        for (std::size_t i = 0; i < items.size(); ++i)
        {
            pos = items[i].find(exptag);
            name = (items[i]).substr(0, pos);
            value = (items[i]).substr(pos + exptag.length());
            if (name != "")
            {
                hashmap.insert(std::map<std::string, std::string>::value_type(name, value));
            }
        }
    }

    return hashmap;
}

/// 组合字符串,与split()相反
/// \param strings 字符串数组
/// \param tag 组合分隔符
void CString::join(const std::vector<std::string>& strings, const std::string& tag)
{
    if (strings.size() > 0)
    {
        this->erase();
        *this = strings[0];

        for (size_t i = 1; i < strings.size(); ++i)
        {
            *this += (tag + strings[i]);
        }
    }
}
// for vector<CString>
void CString::join(const std::vector<CString>& strings, const std::string& tag)
{
    if (strings.size() > 0)
    {
        this->erase();
        *this = strings[0];

        for (size_t i = 1; i < strings.size(); ++i)
        {
            *this += (tag + strings[i]);
        }
    }
}

/// 格式化赋值
/// 各参数定义与标准sprintf()函数完全相同
/// \retval true 执行成功
/// \retval false 失败
bool CString::sprintf(const char* format, ...)
{
    va_list ap;
    va_start(ap, format);
    *this = va_sprintf(ap, format);
    va_end(ap);
    return true;
}

/// 替换
/// 该函数重载了string::replace()
/// \param oldstr 被替换掉的字符串
/// \param newstr 用来替换旧字符串的新字符串
/// \retval 1 替换成功
/// \retval 0 失败
int CString::Replace(const std::string& oldstr, const std::string& newstr)
{
    size_t pos = 0;
    if (oldstr != "" && (pos = this->find(oldstr)) != npos)
    {
        std::string::replace(pos, oldstr.length(), newstr);
        return 1;
    }
    return 0;
}

/// 全文替换
/// \param oldstr 被替换掉的字符串
/// \param newstr 用来替换旧字符串的新字符串
/// \return 执行替换的次数
int CString::Replace_all(const std::string& oldstr, const std::string& newstr)
{
    if (oldstr == "")
    {
        return 0;
    }

    int i = 0;
    size_t pos = 0;
    size_t curpos = 0;
    while ((pos = this->find(oldstr, curpos)) != npos)
    {
        std::string::replace(pos, oldstr.length(), newstr);
        curpos = pos + newstr.length();
        ++i;
    }
    return i;
}

/// 转换为大写字母
void CString::MakeUpper()
{
    for (size_t i = 0; i < this->length(); i++)
    {
        (*this)[i] = ::toupper((*this)[i]);
    }
}

/// 转换为小写字母
void CString::MakeLower()
{
    for (size_t i = 0; i < this->length(); i++)
    {
        (*this)[i] = ::tolower((*this)[i]);
    }
}

/// 字符串是否完全由数字组成
/// \retval true 是
/// \retval false 否
bool CString::isnum() const
{
    if (this->length() == 0)
    {
        return false;
    }

    for (size_t i = 0; i < this->length(); ++i)
    {
        if (!isdigit((*this)[i]))
        {
            return false;
        }
    }
    return true;
}

/// 读取文件到字符串
/// \param filename 要读取的文件完整路径名称
/// \retval true 读取成功
/// \retval false 失败
bool CString::load_file(const std::string& filename)
{
    FILE* fp = fopen(filename.c_str(), "rb");
    if (fp == NULL)
    {
        return false;
    }

    // read file size
    fseek(fp, 0, SEEK_END);
    int bufsize = ftell(fp);
    rewind(fp);

    char* buf = new char[bufsize + 1];
    memset(buf, 0, bufsize + 1);
    fread(buf, 1, bufsize, fp);
    fclose(fp);
    *this = std::string(buf, bufsize);
    delete[] buf;
    return true;
}

/// 保存字符串到文件
/// \param filename 要写入的文件路径名称
/// \param mode 写入方式,默认为ios::trunc|ios::out
/// \param permission 文件属性参数，默认为0666
/// \retval true 写入成功
/// \retval false 失败
bool CString::save_file(const std::string& filename, const std::ios::openmode mode,
                        const mode_t permission) const
{
    std::ofstream outfile(filename.c_str(), mode);
    if (outfile)
    {
        outfile << *this;
        outfile.close();

        // chmod
        mode_t mask = umask(0);
        chmod(filename.c_str(), permission);
        umask(mask);
        return true;
    }
    return false;
}


/// 格式化赋值
/// 各参数定义与标准sprintf()函数完全相同
/// \retval true 执行成功
/// \retval false 失败
bool CString::Format(const char* format, ...)
{
    va_list ap;
    va_start(ap, format);
    *this = va_sprintf(ap, format);
    va_end(ap);
    return true;
}

// 获取c形式的常量字符串
const char* CString::GetBuffer() const
{
    return this->c_str();
}

// 反向获取c形式的常量字符串
int CString::ReverseFind(char ch) const
{
    std::size_t found = this->find_last_of(ch);
    return (std::string::npos == found) ? -1 : (int)(found);
}

int CString::Find(char ch) const
{
    return Find(ch, 0);
}

int CString::Find(char ch, int nStart) const
{
    std::size_t found = this->find(ch, (size_t) nStart);
    if (found != std::string::npos)
    {
        return (int)(found);
    }

    return -1;
}

char CString::GetAt(int nIndex)
{
    assert(nIndex >= 0);
    assert((size_t)nIndex < this->length());
    return this->at(nIndex);
}

int CString::Remove(const char* cstr)
{
    Replace_all(cstr, "");
    return 0;
}
