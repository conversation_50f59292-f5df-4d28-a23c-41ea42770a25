/// \file waString.h
/// webapp::String类头文件
/// 继承自string的字符串类
/// <a href=std_string.html>基类string使用说明文档</a>

#ifndef _WEBAPPLIB_STRING_H_
#define _WEBAPPLIB_STRING_H_

#include <sys/stat.h>
#include <cstdarg>
#include <iostream>
#include <string>
#include <vector>
#include <algorithm>
#include <map>
#include <string.h>

////////////////////////////////////////////////////////////////////////////////
// 空白字符列表
const char BLANK_CHARS[] = " \t\n\r\v\f";

////////////////////////////////////////////////////////////////////////////////
/// long int转换为string
std::string itos(const long i, const std::ios::fmtflags base = std::ios::dec);
/// string转换为int
long stoi(const std::string& s, const std::ios::fmtflags base = std::ios::dec);

/// double转换为string
std::string ftos(const double f, const int ndigit = 2);
/// string转换为double
double stof(const std::string& s);

/// 判断一个双字节字符是否是GBK编码汉字
bool isgbk(const unsigned char c1, const unsigned char c2);

/// 可变参数字符串格式化，与va_start()、va_end()宏配合使用
std::string va_sprintf(va_list ap, const std::string& format);
/// 格式化字符串并返回
std::string va_str(const char* format, ...);

////////////////////////////////////////////////////////////////////////////////
/// 继承自string的字符串类
/// <a href="std_string.html">基类string使用说明文档</a>
class CString : public std::string
{
public:

    ////////////////////////////////////////////////////////////////////////////
    /// 默认构造函数
    CString() {}

    /// 参数为char*的构造函数
    CString(const char* s)
    {
        if (s)
        {
            this->assign(s);
        }
        else
        {
            this->erase();
        }
    }

    /// 参数为string的构造函数
    CString(const std::string& s)
    {
        this->assign(s);
    }

    /// 析构函数
    virtual ~CString() {}

    ////////////////////////////////////////////////////////////////////////////
    /// \enum 函数String::split()分割结果返回方式
    enum split_mode
    {
        /// 忽略连续多个分隔符，返回结果不含空字段
        SPLIT_IGNORE_BLANK,
        /// 不忽略连续多个分隔符，返回结果包含空字段
        SPLIT_KEEP_BLANK
    };

    ////////////////////////////////////////////////////////////////////////////
    /// 返回 char* 型结果，调用者必须调用 delete[] 释放所返回内存
    char* c_char() const;

    /// 返回字符数量，支持全角字符
    std::string::size_type w_length() const;
    /// 截取子字符串，支持全角字符
    CString w_substr(const std::string::size_type pos = 0,
                     const std::string::size_type n = npos) const;

    /// 清除左侧空白字符
    void TrimLeft(const std::string& blank = BLANK_CHARS);
    /// 清除右侧空白字符
    void TrimRight(const std::string& blank = BLANK_CHARS);
    /// 清除两侧空白字符
    void Trim(const std::string& blank = BLANK_CHARS);

    /// 从左边截取指定长度子串
    CString Left(const std::string::size_type n) const;
    /// 从中间截取指定长度子串
    CString Mid(const std::string::size_type pos, const std::string::size_type n = npos) const;

    /// 从右边截取指定长度子串
    CString Right(const std::string::size_type n) const;

    /// 调整字符串长度
    void resize(const std::string::size_type n);

    /// 统计指定子串出现的次数
    int count(const std::string& str) const;

    /// 根据分割符分割字符串
    std::vector<CString> split(const std::string& tag, const int limit = 0, const split_mode mode = SPLIT_IGNORE_BLANK) const;

    /// 转换字符串为MAP结构(map<std::string,std::string>)
    std::map<std::string, std::string> tomap(const std::string& itemtag = "&", const std::string& exptag = "=") const;

    /// 组合字符串
    void join(const std::vector<std::string>& strings, const std::string& tag);
    void join(const std::vector<CString>& strings, const std::string& tag);

    /// 格式化赋值
    bool sprintf(const char* format, ...);

    /// 替换
    int Replace(const std::string& oldstr, const std::string& newstr);
    /// 全文替换
    int Replace_all(const std::string& oldstr, const std::string& newstr);

    /// 转换为大写字母
    void MakeUpper();
    /// 转换为小写字母
    void MakeLower();

    /// 字符串是否完全由数字组成
    bool isnum() const;

    /// 读取文件到字符串
    bool load_file(const std::string& filename);
    /// 保存字符串到文件
    bool save_file(const std::string& filename, const std::ios::openmode mode = std::ios::trunc | std::ios::out,
                   const mode_t permission = S_IRUSR | S_IWUSR | S_IRGRP | S_IWGRP | S_IROTH | S_IWOTH) const;

    /// 格式化赋值
    /// 各参数定义与标准sprintf()函数完全相同
    /// \retval true 执行成功
    /// \retval false 失败
    bool Format(const char* format, ...);

    /// 获取c形式的常量字符串
    const char* GetBuffer() const;

    //反向查找
    int ReverseFind(char ch) const;

    //是否为空串
    inline bool IsEmpty() const
    {
        return (strlen(this->c_str()) == 0);
    }

    int Find(char ch) const;

    int Find(char ch, int nStart) const;
    inline int GetLength() const
    {
        return this->length();
    }

    char GetAt(int nIndex);

    int Remove(const char* cstr);
};


#endif //_WEBAPPLIB_STRING_H_

