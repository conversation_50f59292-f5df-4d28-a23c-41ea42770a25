#include "util_judge.h"
#include <sstream>
#include <unistd.h>
#include "AkLogging.h"
#include <json/json.h>
#include "util.h"
#include "AkcsCommonDef.h"

namespace akjudge
{



bool DevIndoorType(int dev_type)
{
    if (dev_type == DEVICE_TYPE_INDOOR)
    {
       return true;
    }
    return false;
}


bool DevDoorType(int dev_type)
{
    if ((dev_type == DEVICE_TYPE_DOOR)  ||
        (dev_type == DEVICE_TYPE_STAIR) ||
        (dev_type == DEVICE_TYPE_WALL)  ||
        (dev_type == DEVICE_TYPE_ACCESS))
    {
       return true;
    }
    return false;
}

bool DeviceSupportLimitFlow(int firmware)
{
    if (firmware ==  DEVICES_SOFTWARE_VERSION::SOFTWARE_R29 ||
        firmware ==  DEVICES_SOFTWARE_VERSION::SOFTWARE_X916)
    {
        return true;
    }

    return false;
}

int IsCommunityPublicDev(int dev_garde)
{
    if (dev_garde == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT //梯口
         ||  dev_garde == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)//公共
    {
       return 1;
    }
    return 0;
}

bool IsNodeAccountRole(int role)
{
     if (role == ACCOUNT_ROLE_COMMUNITY_MAIN || role == ACCOUNT_ROLE_PERSONNAL_MAIN || role == ACCOUNT_ROLE_COMMUNITY_PM
        || role == ACCOUNT_ROLE_OFFICE_MAIN || role == ACCOUNT_ROLE_OFFICE_ADMIN || role == ACCOUNT_ROLE_OFFICE_NEW_PER
        || role == ACCOUNT_ROLE_OFFICE_NEW_ADMIN)
     {
        return true;
     }
     
     return false;
}

bool IsCommunityEndUserRole(int role)
{
    if (role == ACCOUNT_ROLE_COMMUNITY_MAIN || role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT)
    {
        return true;
    }
    return false;
}

bool DevSupportRemoteOpenDoorAck(int func)
{
    return SwitchHandle(func, FUNC_DEV_SUPPORT_OPENDOOR_ACK);
}

bool IsInstallerKitProject(int project_creator_type)
{
    return project_creator_type == (int)ProjectCreatorType::INSTALLER_KIT;
}

bool IsDevDclientOnline(int online_status)
{
    return online_status == (int)DevOnlineStatus::ONLINE;
}

bool IsEndUserRole(int role)
{
    if (role == ACCOUNT_ROLE_PERSONNAL_MAIN || role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT
        || role == ACCOUNT_ROLE_COMMUNITY_MAIN || role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT
        || role == ACCOUNT_ROLE_OFFICE_MAIN || role == ACCOUNT_ROLE_OFFICE_ADMIN 
        || role == ACCOUNT_ROLE_OFFICE_NEW_PER || role == ACCOUNT_ROLE_OFFICE_NEW_ADMIN)
    {
        return true;
    }
    return false;
}

bool IsCommunity(int garde)
{
    return garde == AccountGrade::COMMUNITY_MANEGER_GRADE ? true : false;
}

bool IsOffice(int garde)
{
    return garde == AccountGrade::OFFICE_MANEGER_GRADE ? true : false;
}



}
