#ifndef __AKCS_BASE_UTIL_JUDGE_H__
#define __AKCS_BASE_UTIL_JUDGE_H__
#include <set>
#include <vector>
#include <map>
#include <list>
#include <string.h>
#include <math.h>


//add by chenzhx 对设备的一些类判断等函数，这里不能添加数据的操作, 只能根据传入的一些类型进行判断
namespace akjudge
{

bool DevIndoorType(int dev_type);
bool DevDoorType(int dev_type);
bool DeviceSupportLimitFlow(int firmware);
int IsCommunityPublicDev(int dev_garde);
bool IsNodeAccountRole(int role);
bool IsCommunityEndUserRole(int role);

bool DevSupportRemoteOpenDoorAck(int func);
bool IsInstallerKitProject(int project_creator_type);
bool IsDevDclientOnline(int online_status);
bool IsEndUserRole(int role);
bool IsCommunity(int garde);
bool IsOffice(int garde);


}


#endif //__AKCS_BASE_UTIL_RELAY_H__

