#include "util_relay.h"
#include <sstream>
#include <unistd.h>
#include "AkLogging.h"
#include <json/json.h>
#include "util.h"
#include <cctype>

void GetValueByRelay(const std::string& relay, int& relay_value)
{
    relay_value = 0;
    if(relay.empty()) 
    {
        return;
    }
    //6.7 relay存储改为json字段
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(relay, root))
    {
        return;
    }
    int index = 0;
    for(const auto& element : root) {
       int enable = element["enable"].asInt();
       if(enable) {
           relay_value += pow(2, index);
       }
       index++;
    }
    return;
}

//由7=1+2+4 转为123
std::string RelayToString(const int &relay)
{
	std::stringstream doornum;

	for (int i = 0; i < DEVICE_RELAY_NUM; i++)
	{
		if ((relay)&(1 << i))
		{
			doornum << i + 1;
		}
	}

	return doornum.str();
}

//由设备relay 1234的形式 转成 从低到高位二进制计算的数值
//如123 -> 1+2+4=7
int DoornumToRelayStatus(const char* doornum)
{
    if(doornum == nullptr)
    {
        return 0;
    }
    
    int relay_status = 0;
    for(unsigned int i = 0; i < strlen(doornum); i++)
    {
        if(doornum[i] != '0')   //保护
        {
            //字符转数字后再做幂运算
            relay_status += pow(2, (doornum[i]-'0')-1);
        }
    }
    return relay_status;
}

//室内机外接relay最多支持48个,1~9A~Za~m.
//由设备relay 1234的形式 转成 从低到高位二进制计算的数值
//如123 -> 1+2+4=7,A -> 2^(10-1),a -> 2^(36-1),以此类推,
//映射规则: 1-9(relay1-9), A-Z(relay10-35), a-m(relay36-48), 总共48个
uint64_t ExternDoornumToRelayStatus(const char* doornum)
{
    if(doornum == nullptr)
    {
        return 0;
    }
    
    uint64_t relay_status = 0;
    for(unsigned int i = 0; i < strlen(doornum); i++)
    {
        if(doornum[i] != '0')   //保护
        {
            if(doornum[i] >= '1' && doornum[i] <= '9')
            {
                //数字1-9对应继电器1-9
                relay_status += (uint64_t)1 << ((doornum[i]-'0')-1);
            }
            else if(doornum[i] >= 'A' && doornum[i] <= 'Z') 
            {
                // 大写字母A-Z对应继电器10-35
                relay_status += (uint64_t)1 << (doornum[i] - 'A' + 9); 
            }
            else if(doornum[i] >= 'a' && doornum[i] <= 'm') 
            {
                // 小写字母a-m对应继电器36-48
                relay_status += (uint64_t)1 << (doornum[i] - 'a' + 35);
            }
        }
    }
    return relay_status;
}


//由设备relay 十进制3的形式 转成 从低到高位二进制计算的数值
int ConvertDoornumToRelayStatus(int doornum) 
{
    if (doornum <= 0) {
        return 0;
    }
    return 1 << (doornum - 1); // 计算对应的二进制值
}

    

void GetDtmfValue(const std::string& dtmf, RELAY_INFO& relay_info)
{
    if (dtmf == "#")
    {
        relay_info.dtmf = 11;
    }
    else if (dtmf == "*")
    {
        relay_info.dtmf = 10;
    }
    else
    {
        relay_info.dtmf = ATOI(dtmf.c_str());
    }
    
    return;
}

void GetRelayAccessControlInfo(const Json::Value& access_control, RELAY_INFO& relay_info)
{
    if (access_control.isNull() || !access_control.isObject()) 
    {
        AK_LOG_INFO << "relay access control is null, relay name:" << relay_info.name;
        return;
    }

    int pin = access_control["pin"].asInt();
    int rf = access_control["rf"].asInt();
    int face = access_control["face"].asInt();
    int ble = access_control["ble"].asInt();
    int nfc = access_control["nfc"].asInt();
    int plate = access_control["plate"].asInt();
    
    relay_info.access_control = pin | (face << 1) | (rf << 2) | (ble << 3) | (nfc << 4) | (plate << 5);

    return;
}

void GetRelayScheduleInfo(const Json::Value& relay_schedule, RELAY_INFO& relay_info)
{
    if (relay_schedule.isNull() || !relay_schedule.isObject()) 
    {
        AK_LOG_INFO << "relay schedule is null, relay name:" << relay_info.name;
        return;
    }

    relay_info.enable_schedule = relay_schedule["enable"].asInt();
    
    std::string access_string = "";
    const Json::Value& access_array = relay_schedule["access"];

    // 拼接 access 元素为字符串
    for (const auto& access_element : access_array) 
    {
        std::string ag_id = access_element.asString();
        //没有设备MAC，无法获取对应自定义权限组，因此只做解析和写入，不做relay schedule过滤
        access_string += ag_id;
        access_string += "/";
    }
    
    Snprintf(relay_info.schedule, sizeof(relay_info.schedule), access_string.c_str());
    
    return;
}

void GetRelaySensorAlarmInfo(const Json::Value& sensor_alarm, RELAY_INFO& relay_info)
{    
    if (sensor_alarm.isNull() || !sensor_alarm.isObject()) 
    {
        AK_LOG_INFO << "relay sensor_alarm is null, relay name:" << relay_info.name;
        return;
    }

    // 数据库存的是A/B/C/D, 设备上报的是Input A/Input B/Input C/Input D, kxl不想改
    std::string input = "Input " + sensor_alarm["input"].asString();
    relay_info.sensor_alarm_timeout = sensor_alarm["timeout"].asInt();
    relay_info.sensor_alarm_enable = sensor_alarm["enable"].asInt();
    Snprintf(relay_info.sensor_alarm_input, sizeof(relay_info.sensor_alarm_input), input.c_str());

    return;
}

//relay格式如: [{
//     "name": "relay name",
//     "dtmf": "#",
//     "enable": 1,
//     "showHome": 0,
//     "showTaking": 1,
//     "accessControl": {
//         "pin": 1,
//         "rf": 1,
//         "face": 1,
//         "ble": 1,
//         "nfc":0
//     },
//     "schedule": {
//         "enable": 0,
//         "access": ["111"]
//     },
//     "sensorAlarm": {
//         "enable": 0,
//         "input": "Input A",
//         "timeout": 60
//     },
//     "IsEmergencyDoor":1
//]
void ParseRelay(const std::string &relays, std::vector<RELAY_INFO> &relay_item) 
{
    if(relays.empty())
    {
        return;
    }
    
    //6.7 relay存储改为json字段
    Json::Reader reader;
    Json::Value root;
    if (!reader.parse(relays, root))
    {
        return;
    }
    
    int tmp_index = 1;

    for(const auto& element : root) 
    {
        RELAY_INFO relay_info;

        relay_info.enable =  element["enable"].asInt();
        relay_info.hold_delay = element.get("hold_delay", -1).asInt();
        relay_info.IsEmergencyDoor = element["IsEmergencyDoor"].asInt();
        relay_info.relay_index = tmp_index;
        Snprintf(relay_info.name, sizeof(relay_info.name), element["name"].asString().c_str());

        GetDtmfValue(element["dtmf"].asString(), relay_info);

        GetRelayAccessControlInfo(element["accessControl"], relay_info);

        GetRelayScheduleInfo(element["schedule"], relay_info);

        GetRelaySensorAlarmInfo(element["sensorAlarm"], relay_info);
               
        relay_item.push_back(relay_info);
        tmp_index++;
    }
    return;
}


//把室内机上报的7=1+2+4 转为app的格式，支持起始偏移量和数量限制
int ChangeIndoorExternRelayStatusToAppUse(uint64_t status, std::map</*value*/std::string, /*attr*/std::map<std::string, std::string>> &relays, int start_relay_id)
{
    for (int i = 0; i < RELAYS_PER_EXTRA_DEVICE; i++)
    {
        int relay_id = start_relay_id + i;
        if (relay_id >= INDOOR_EXT_RELAY_NUM) break; // 防止越界
        
        if ((status)&(1ULL << relay_id))
        {
            relays[std::to_string(relay_id)] = {{"Type", "Extern"}, {"Status", "1"}};
        }
        else
        {
            relays[std::to_string(relay_id)] = {{"Type", "Extern"}, {"Status", "0"}};
        }
    }
    return 0;
}

//输入数据库存的relays字段 输出如{relay_id},{relay_dtmf};{relay_id},{relay_dtmf}的形式，如0,#;1,*;2,0
std::string GetRelayDtmfPairStr(const std::string& relays)
{
    std::vector<RELAY_INFO> relay_info_vec;
    ParseRelay(relays, relay_info_vec);

    std::string relay_dtmf_pair_str;
    int relay_id = 0;
    for (const auto& relay_info : relay_info_vec)
    {
        if (relay_info.enable)
        {
            std::string pair_str;
            pair_str += std::to_string(relay_id) + "," + GetDtmfValue(relay_info.dtmf) + ";";
            relay_dtmf_pair_str += pair_str;
        }
        relay_id++;
    }

    //去掉末尾的";"
    if (relay_dtmf_pair_str.size() > 0)
    {
        relay_dtmf_pair_str.pop_back();
    }

    return relay_dtmf_pair_str;
}

std::string GetDtmfValue(int dtmf)
{
    std::string dtmf_str;

    if (10 == dtmf)
    {
        dtmf_str = "*";
    }
    else if (11 == dtmf)
    {
        dtmf_str = "#";
    }
    else
    {
        dtmf_str = std::to_string(dtmf);
    }

    return dtmf_str;
}

std::string GetDtmfValue(const std::string& dtmf)
{
    std::string dtmf_str;
    if (dtmf == "#")
    {
        dtmf_str = "11";
    }
    else if (dtmf == "*")
    {
        dtmf_str = "10";
    }
    else
    {
        dtmf_str = dtmf;
    }
    return dtmf_str;
}

int GetRelayItem(const std::string& relay, int relay_index, RELAY_INFO& relay_info)
{
    memset(&relay_info, 0, sizeof(relay_info));

    if (relay.empty() || relay_index <= 0)
    {
        return -1;
    }

    std::vector<RELAY_INFO> relay_info_vec;
    ParseRelay(relay, relay_info_vec);

    int array_index = relay_index - 1;
    if (array_index < 0 || array_index >= static_cast<int>(relay_info_vec.size()))
    {
        return -1;
    }

    relay_info = relay_info_vec[array_index];

    return 0;
}

// 从output/input字段提取对应的数字值：K1-K8(1-8), OT1-OT8(9-16), IT1-IT8(1-8)
int GetRelayOutputValue(const std::string& output)
{
    if (output.length() >= 2 && output[0] == 'K' && output[1] >= '1' && output[1] <= '8') 
    {
        return output[1] - '0'; // K1-K8 → 1-8
    } 
    else if (output.length() >= 3 && output.substr(0, 2) == "OT" && output[2] >= '1' && output[2] <= '8') 
    {
        return 8 + (output[2] - '0'); // OT1-OT8 → 9-16
    } 
    else if (output.length() >= 3 && output.substr(0, 2) == "IT" && output[2] >= '1' && output[2] <= '8') 
    {
        return output[2] - '0'; // IT1-IT8 → 1-8
    }
    return 0; // 无效值
}

// 转换web端的hold_delay值为设备端格式
// web端：0=Never, 1~100=延时秒数, -1不配置
// 设备端：-1=Never, 1~100=延时秒数
// 返回值：true=需要下发配置, false=跳过配置
bool ConvertLocalRelayHoldDelayForDevice(int web_hold_delay, int& device_hold_delay)
{
    if (web_hold_delay == -1) 
    {
        // web端不存在hold_delay字段表示不配置（后台读取的时候赋默认值-1），不下发
        return false;
    } 
    else if (web_hold_delay == 0) 
    {
        // web端0表示Never，设备端用-1表示Never
        device_hold_delay = -1;
        return true;
    } 
    else if (web_hold_delay >= 1 && web_hold_delay <= 100) 
    {
        // 1~100秒延时，直接使用
        device_hold_delay = web_hold_delay;
        return true;
    } 
    else 
    {
        // 无效值，不下发
        return false;
    }
}

// 直接返回转换后的hold_delay值，用于配置下发
int ConvertExtraRelayHoldDelayForDevice(int web_hold_delay)
{
    if (web_hold_delay == 0) 
    {
        // web端0表示Never，设备端用-1表示Never
        return -1;
    } 
    else 
    {
        // 直接使用web_hold_delay
        return web_hold_delay;
    }
}

// 解析Output字段，区分K1-K8(外接继电器)和OT1-OT8(数字输出)
int GetRelayTypeAndIndex(const std::string& output, bool& is_extern_relay, int& output_index)
{
    // 如果Output是K1-K8，则是TYPE_EXTERN_RELAY
    if (output.length() >= 2 && output[0] == 'K' && 
        output[1] >= '1' && output[1] <= '8')
    {
        is_extern_relay = true;
        output_index = output[1] - '0'; // 从字符转为数字，K1->1, K2->2, ...
        return 0;
    }
    // 如果Output是OT1-OT8，则是TYPE_DIGITAL_OUTPUT
    else if (output.length() >= 3 && output.substr(0, 2) == "OT" && 
            output[2] >= '1' && output[2] <= '8')
    {
        is_extern_relay = false;
        output_index = output[2] - '0'; // 从字符转为数字，OT1->1, OT2->2, ...
        return 0;
    }
    else
    {
        // 无效的output格式
        return -1;
    }
}
