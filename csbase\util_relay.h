#ifndef __AKCS_BASE_UTIL_RELAY_H__
#define __AKCS_BASE_UTIL_RELAY_H__
#include <set>
#include <vector>
#include <map>
#include <list>
#include <string.h>
#include <math.h>
#include <sstream>


//6.7Relay字段格式变更修改
typedef struct RELAY_INFO_T
{
    int dtmf;
    char name[64];
    int show_homepage;
    int show_talking;
    int enable;
    int hold_delay; // 继电器延时时间(秒)
    int access_control;
    int enable_schedule;
    char schedule[2048]; //newoffice放的是uuid
    int sensor_alarm_timeout;
    int sensor_alarm_enable;
    char sensor_alarm_input[32];
    int IsEmergencyDoor; //触发紧急告警时是否要开启
    int relay_index;

    RELAY_INFO_T() {
        memset(this, 0, sizeof(*this));
    }
} RELAY_INFO;

void GetValueByRelay(const std::string& relay, int& relay_value);
/**
 * 将GetValueByRelay转换后的值转为字符串
 * 比如1,门1,1,1,1;2,门2,1,1,0;3,门3,1,1,0;4,门4,1,1,1
 * GetValueByRelay 后的值为9
 * RelayStoString(9)为1,4，业务上的含义是支持开门1和门4
 * <AUTHOR> (2021/3/18)
 * 
 * @param default_relay 
 * 
 * @return std::string 
 */
std::string RelayToString(const int &default_relay);

/*
 * 将Relay1234 -> 对应从低到高的二进制位置1
 * 如1234->15 123->7
 */
int DoornumToRelayStatus(const char* doornum);
uint64_t ExternDoornumToRelayStatus(const char* doornum);
int ConvertDoornumToRelayStatus(int doornum);

//解析Relay的每一项
void ParseRelay(const std::string& relays, std::vector<RELAY_INFO>& relay_item);

int  ChangeIndoorExternRelayStatusToAppUse(uint64_t status, std::map<std::string, std::map<std::string, std::string>> &relays, int start_relay_id);
#define DEVICE_RELAY_NUM 4

//获取relay_id,DTMF串
std::string GetRelayDtmfPairStr(const std::string& relays);

std::string GetDtmfValue(int dtmf);
std::string GetDtmfValue(const std::string& dtmf);

int GetRelayItem(const std::string& relay, int relay_index, RELAY_INFO& relay_info);

// 从output字段提取对应的数字值：K1-K8(1-8), OT1-OT8(9-16)
int GetRelayOutputValue(const std::string& output);

bool ConvertLocalRelayHoldDelayForDevice(int web_hold_delay, int& device_hold_delay);

int ConvertExtraRelayHoldDelayForDevice(int web_hold_delay);

// 解析Output字段，区分K1-K8(外接继电器)和OT1-OT8(数字输出)
// 返回值：true=解析成功, false=解析失败
int GetRelayTypeAndIndex(const std::string& output, bool& is_extern_relay, int& output_index);

#define RELAYS_PER_EXTRA_DEVICE 16  //室内机外接relay每块板个数
#define TOTAL_EXTRA_DEVICES_NUM 3  //室内机外接relay总块数
#define INDOOR_EXT_RELAY_NUM  48  //室内机外接relay个数

#endif //__AKCS_BASE_UTIL_RELAY_H__

