#include "util_string.h"
#include <sstream>
#include <unistd.h>
#include <net/if.h>
#include <sys/ioctl.h>
#include <chrono>
#include "AkLogging.h"
#include <ctime>
#include <boost/algorithm/string.hpp>
#include <boost/functional/hash.hpp>
#include <boost/format.hpp>
#include <boost/crc.hpp>
#include <encrypt/Md5.h>
#include <json/json.h>
#include "util.h"
#include "encrypt/Base64.h"
#include "encrypt/Caesar.h"

using std::random_device;
using std::default_random_engine;

CStrExplode::CStrExplode(const char* str, char seperator)
{
	m_item_cnt = 1;
	const char* pos = str;
	while (*pos) {
		if (*pos == seperator) {
			m_item_cnt++;
		}

		pos++;
	}

	m_item_list = new char* [m_item_cnt];

	int idx = 0;
	const char* start = pos = str;
	while (*pos) {
		if ( pos != start && *pos == seperator) {
			uint32_t len = pos - start;
			m_item_list[idx] = new char [len + 1];
			strncpy(m_item_list[idx], start, len);
			m_item_list[idx][len]  = '\0';
			idx++;

			start = pos + 1;
		}

		pos++;
	}

	uint32_t len = pos - start;
    if(len != 0)
    {
        m_item_list[idx] = new char [len + 1];
        strncpy(m_item_list[idx], start, len);
        m_item_list[idx][len]  = '\0';
    }
}

CStrExplode::~CStrExplode()
{
	for (uint32_t i = 0; i < m_item_cnt; i++) {
		delete [] m_item_list[i];
	}

	delete [] m_item_list;
}


void StringReplace(std::string &replace_string, const std::string &src_string, const std::string &dst_string)
{
	size_t pos = 0;
	size_t src_size = src_string.size();
	size_t dst_size = dst_string.size();
	while ((pos = replace_string.find(src_string, pos)) != std::string::npos)
	{
		replace_string.replace(pos, src_size, dst_string);
		pos += dst_size;
	}
} 

std::string Int2String(uint32_t user_id)
{
    std::stringstream ss;
    ss << user_id;
    return ss.str();
}

uint32_t String2Int(const std::string& value)
{
    return (uint32_t)ATOI(value.c_str());
}


inline unsigned char ToHex(const unsigned char &x)
{
    return x > 9 ? x -10 + 'A': x + '0';
}

inline unsigned char FromHex(const unsigned char &x)
{
    return isdigit(x) ? x-'0' : x-'A'+10;
}

std::string URLEncode(const std::string &sIn)
{
    std::string sOut;
    for( size_t ix = 0; ix < sIn.size(); ix++ )
    {
        unsigned char buf[4];
        memset( buf, 0, 4 );
        if( isalnum( (unsigned char)sIn[ix] ) )
        {
            buf[0] = sIn[ix];
        }
        //else if ( isspace( (unsigned char)sIn[ix] ) ) //貌似把空格编码成%20或者+都可以
        //{
        //    buf[0] = '+';
        //}
        else
        {
            buf[0] = '%';
            buf[1] = ToHex( (unsigned char)sIn[ix] >> 4 );
            buf[2] = ToHex( (unsigned char)sIn[ix] % 16);
        }
        sOut += (char *)buf;
    }
    return sOut;
}

std::string URLDecode(const std::string &sIn)
{
    std::string sOut;
    for( size_t ix = 0; ix < sIn.size(); ix++ )
    {
        unsigned char ch = 0;
        if(sIn[ix]=='%')
        {
            ch = (FromHex(sIn[ix+1])<<4);
            ch |= FromHex(sIn[ix+2]);
            ix += 2;
        }
        else if(sIn[ix] == '+')
        {
            ch = ' ';
        }
        else
        {
            ch = sIn[ix];
        }
        sOut += (char)ch;
    }
    return sOut;
}

//s:待分割的源字符串,C:分隔符,V:分割后的结果集,vector<>
void SplitString(const std::string& s, const std::string& c, std::vector<std::string>& oVec)
{
  std::string::size_type pos1 = 0;
  auto pos2 = s.find(c);
  while(std::string::npos != pos2)
  {
	//容错，防止加入一个空串,形如:12,,23,42 或者:12,23, 或者:12,23,,
    if (pos2 > pos1)
    {
    	oVec.push_back(s.substr(pos1, pos2-pos1));
    }
    pos1 = pos2 + c.size();
    pos2 = s.find(c, pos1);
  }
  //将最后一个字符串压入容器中
  if(pos1 != s.length())
  {
      oVec.push_back(s.substr(pos1));
  }
}

//s:待分割的源字符串,C:分隔符,V:分割后的结果集,set<>
void SplitString(const std::string& s, const std::string& c, std::set<std::string>& oVec)
{
  std::string::size_type pos1 = 0;
  auto pos2 = s.find(c);
  while(std::string::npos != pos2)
  {
	//容错，防止加入一个空串,形如:12,,23,42 或者:12,23, 或者:12,23,,
    if (pos2 > pos1)
    {
    	oVec.insert(s.substr(pos1, pos2-pos1));
    }
    pos1 = pos2 + c.size();
    pos2 = s.find(c, pos1);
  }
  //将最后一个字符串压入容器中
  if(pos1 != s.length())
  {
      oVec.insert(s.substr(pos1));
  }
}

//s:待分割的源字符串,C:分隔符,V:分割后的结果集,list<>
void SplitString(const std::string& s, const std::string& c, std::list<std::string>& oList)
{
  std::string::size_type pos1 = 0;
  auto pos2 = s.find(c);
  while(std::string::npos != pos2)
  {
	//容错，防止加入一个空串,形如:12,,23,42 或者:12,23, 或者:12,23,,
    if (pos2 > pos1)
    {
    	oList.push_back(s.substr(pos1, pos2-pos1));
    }
    pos1 = pos2 + c.size();
    pos2 = s.find(c, pos1);
  }
  //将最后一个字符串压入容器中
  if(pos1 != s.length())
  {
      oList.push_back(s.substr(pos1));
  }
}


unsigned int HashtabHashString(const void *obj)
{
	unsigned char *str = (unsigned char *) obj;
	unsigned int total;

	for (total = 0; *str; str++) {
		unsigned int tmp = total;
		total <<= 1; /* multiply by 2 */
		total += tmp; /* multiply by 3 */
		total <<= 2; /* multiply by 12 */
		total += tmp; /* multiply by 13 */

		total += ((unsigned int)(*str));
	}
	return total;
}

std::string GetSubstrFromBehind(const std::string &src, int nPos)
{
   if (src.length() <= (std::size_t)nPos)
   {
        return src;
   }
   else
   {
       return src.substr(src.length() - nPos); 
   }
   return "";
}

//获取从后面开始匹配的最长匹配位数
int GetStrMatchNumFromBehind(const std::string& src, const std::string& dect)
{
    int src_len = src.length();
    int dect_len = dect.length();
    int num = 0;
    if (src_len >= dect_len)
    {
        int i = 0;
        int j = dect_len;
        while (j--)
        {

            if (src[src_len - 1 - i] == dect[dect_len - 1 - i])
            {
                num++;
            }
            else
            {
                return num;
            }
            i++;
        }
    }
    else
    {
        int i = 0;
        int j = src_len;
        while (j--)
        {

            if (src[src_len - 1 - i] == dect[dect_len - 1 - i])
            {
                num++;
            }
            else
            {
                return num;
            }
            i++;
        }
    }
    return num;
}


int StringAllisNum(std::string str)
{
    for (uint32_t i = 0; i < str.size(); i++)
    {
        int tmp = (int)str[i];
        if ((tmp >= 48 && tmp <= 57) || tmp == 40 || tmp == 41 || tmp == 43 || tmp == 45/*()+-*/)
        {
            continue;
        }
        else
        {
            return 0;
        }
    }
    return 1;
}

//使用,作为分隔符
bool CheckStrInFilter(const std::string& filter, const std::string& str)
{
    if(filter.size() == 0 || str.size() == 0)
    {
        return false;
    }
    
    char tmp[64] = "";
    snprintf(tmp, sizeof(tmp), ",%s,", str.c_str());
    if (strstr(filter.c_str(), tmp))
    {
        return true;
    }
    
    return false;
}

//dest:转义后的字符串,dest_len:字符串的长度限制  ;src:  转义前的字符串;src_len=需要转义的字符串长度
void ChangeSpecialXmlChar(char* dest, size_t dest_len, const char* src, size_t src_len)
{
    if (src_len > dest_len)
    {
        return;
    }
    const char* tmp = src;
    while (*tmp)
    {
        char cTmp = *tmp;
        switch (cTmp)
        {
            case '&':
            {
                strncat(dest, "&amp;", 5);
            }
            break;
            case '<':
            {
                strncat(dest, "&lt;", 4);
            }
            break;
            case '>':
            {
                strncat(dest, "&gt;", 4);
            }
            break;
            case '\'':
            {
                strncat(dest, "&apos;", 6);
            }
            break;
            case '\"':
            {
                strncat(dest, "&quot;", 6);
            }
            break;
            default:
                snprintf(dest + strlen(dest), 2, "%c", cTmp);
                break;
        }
        tmp++;
    }
}


int StrHash(const std::string &str, int mod)
{
    std::hash<std::string> hash_fn; // 定义哈希函数对象
    std::size_t hash_value = hash_fn(str); // 计算哈希值
    std::size_t hash_mod = hash_value % mod;// 取模
    return hash_mod;
}

//给数据拼接上'',sql查询时才能用索引
std::string ListToSeparatedFormatString(const std::vector<std::string> &datas)
{
    if (datas.size() == 0)
    {
        return "";  
    }
    
    std::string serial;
    for (const auto &data : datas) 
    {
        if (data.size() > 0)
        {
            // 防止出现给空字符串拼接上''的情况
            serial += "'" + data + "'" + ",";
        }
        
    }
    
    if (!serial.empty())
    {
        serial.pop_back(); // 删除最后一个逗号
    }
    return serial;
}

std::string ListToSeparatedFormatString(const std::set<std::string> &datas)
{
    if (datas.size() == 0)
    {
        return "";
    }
    
    std::string serial;
    for (const auto &data : datas) 
    {
        // 防止出现给空字符串拼接上''的情况
        if (data.size() > 0)
        {
            serial += "'" + data + "'" + ",";
        }
    }
    
    if (!serial.empty())
    {
        serial.pop_back(); // 删除最后一个逗号
    }
    return serial;
}

std::string ListToSeparatedFormatString(const std::vector<int> &datas)
{
    if (datas.size() == 0)
    {
        return "";
    }
    
    std::stringstream serial_ss;
    for (const auto &data : datas) 
    {
    	serial_ss << "'" << data << "'" << ",";
    }
    std::string serial_str = serial_ss.str();
    
    if (!serial_str.empty())
    {
        serial_str.pop_back(); // 删除最后一个逗号
    }
    return serial_str;
}

std::string ListToSeparatedFormatString(const std::set<int> &datas)
{
    if (datas.size() == 0)
    {
        return "";
    }
    
    std::stringstream serial_ss;
    for (const auto &data : datas) 
    {
    	serial_ss << "'" << data << "'" << ",";
    }
    std::string serial_str = serial_ss.str();
    
    if (!serial_str.empty())
    {
        serial_str.pop_back(); // 删除最后一个逗号
    }
    return serial_str;
}

std::string ListToSeparatedFormatString(const std::vector<unsigned int> &datas)
{
    if (datas.size() == 0)
    {
        return "";
    }
    
    std::stringstream serial_ss;
    for (const auto &data : datas) 
    {
    	serial_ss << "'" << data << "'" << ",";
    }
    std::string serial_str = serial_ss.str();

    if (!serial_str.empty())
    {
        serial_str.pop_back(); // 删除最后一个逗号
    }
    return serial_str;
}

int GetFirstDiffBit(int num1, int num2) 
{
    int diff = num1 ^ num2;
    if (diff == 0) 
    {
        return -1; // 两个数字完全相同
    }

    int bit = 0;
    while (diff != 0) 
    {
        if (diff & 1) 
        {
            return bit;
        }
        bit++;
        diff >>= 1;
    }
    return bit;
}

uint32_t crc32_hash(const std::string &key)
{
    boost::crc_32_type ret;
    ret.process_bytes(key.c_str(), key.size());
    return ret.checksum();
}

void DataMasking(std::string& message)
{
    // 查找包含 "Pwd=" 的位置
    std::size_t pwd_pos = message.find("Pwd=");
    if (pwd_pos != std::string::npos)
    {
        std::size_t space_pos = message.find_first_of(" ", pwd_pos);
        if (space_pos == std::string::npos) 
        {
            message.replace(pwd_pos + 4, message.length() - pwd_pos - 4, "********");
        } 
        else 
        {
            message.replace(pwd_pos + 4, space_pos - pwd_pos - 4, "********");
        }
    }

    // 查找包含 "Password=" 的位置
    pwd_pos = message.find("Password=");
    if (pwd_pos != std::string::npos)
    {
        std::size_t space_pos = message.find_first_of(" ", pwd_pos);
        if (space_pos == std::string::npos) 
        {
            message.replace(pwd_pos + 9, message.length() - pwd_pos - 9, "********");
        } 
        else 
        {
            message.replace(pwd_pos + 9, space_pos - pwd_pos - 9, "********");
        }
    }    
}


std::string GenerateNFCCode()
{
    std::string str = "ABCDEF1234567890";
    std::random_device rd;
    std::mt19937 gen(rd());
    std::shuffle(str.begin(),str.end(),gen);

    //获取前14个字符
    std::string randNFCcode = "F0" + str.substr(0,14);

    return randNFCcode;
}

std::string GenerateBLECode()
{
    std::string str = "ABCDEF1234567890";
    std::random_device rd;
    std::mt19937 gen(rd());
    std::shuffle(str.begin(),str.end(),gen);

    //获取前15个字符
    std::string ble_code = "B" + str.substr(0,15);

    return ble_code;
}

//通过最后一个分隔符分隔字符串
void SplitStringFromLastFilter(const std::string& src_str, const std::string& filter, std::string& str_before, std::string& str_after)
{
    //找到最后一个分隔符所在位置
    std::size_t last_filter_index = src_str.find_last_of(filter);

    if (last_filter_index != std::string::npos)
    {
        str_before =  src_str.substr(0, last_filter_index);
        str_after = src_str.substr(last_filter_index + 1);
    }
    else
    {
        //未找到分隔符 则str_before为整个字符串，str_after为空
        str_before = src_str;
        str_after = "";
    }
}

//去除字符串前后的空格
void TrimString(std::string& str)
{
    if (str.size() > 0)
    {
        int s = str.find_first_not_of(" ");
        int e = str.find_last_not_of(" ");
        str = str.substr(s, e - s + 1);
    }
    return;
}


bool StringEndsWith(const std::string& full_string, const std::string& ending) 
{
    if (full_string.length() < ending.length()) 
    {
        return false;
    }
    return (full_string.compare(full_string.length() - ending.length(), ending.length(), ending) == 0);
}


//用指定字符分隔数组成员，拼接成字符串
std::string ListToSeparatedFormatString(const std::vector<std::string> &datas, char separator)
{
    if (datas.size() == 0)
    {
        return "";
    }
    
    std::string serial;
    for (const auto &data : datas) 
    {
        // 防止出现给空字符串拼接分隔符的情况
        if (data.size() > 0)
        {
            serial += data;
            serial += separator;
        }
    }
    
    if (!serial.empty())
    {
        serial.pop_back(); // 删除最后一个分隔符
    }
    return serial;
}

std::string GetNbitRandomString(int length) 
{			
    char tmp;							
    string buffer;						
    
    // 下面这两行比较重要:
    random_device rd;					
    default_random_engine random(rd());	
    
    for (int i = 0; i < length; i++) 
    {
        tmp = random() % 36;	
        if (tmp < 10) 
        {			
            tmp += '0';
        } 
        else 
        {				
            tmp -= 10;
            tmp += 'A';
        }
        buffer += tmp;
    }
    return buffer;
}

bool StringStartsWith(const std::string& str, const std::string prefix)
{
    return (str.rfind(prefix, 0) == 0);
}

std::string GetHashValStr(const std::string& str)
{
    std::hash<std::string> hash_fn; // 定义哈希函数对象
    std::size_t hash_value = hash_fn(str); // 计算哈希值
    return std::to_string(hash_value);
}

std::string GetJsonString(const std::map<std::string, std::string>& str_datas, const std::map<std::string, int>& int_datas, const std::map<std::string, bool>& bool_datas)
{
    Json::Value data;

    for(const auto& str_pair : str_datas)
    {
        data[str_pair.first] = str_pair.second;
    }

    for(const auto& int_pair : int_datas)
    {
        data[int_pair.first] = int_pair.second;
    }

    for(const auto& bool_pair : bool_datas)
    {
        data[bool_pair.first] = bool_pair.second;
    }

    Json::FastWriter writer;
    std::string msg;
    try 
    {
        msg = writer.write(data);
    } 
    catch (const std::exception& e) 
    {
        AK_LOG_WARN << "get json string failed.";
        return "";
    }

    return msg;
}

std::string GetLogicServerIp(const std::string& logic_server_id)
{
    size_t pos = logic_server_id.find('_');
    if (pos != std::string::npos) {
        return logic_server_id.substr(pos + 1);
    }
    return "";
}

std::string GetMsgIDStr(int msg_id)
{
    std::stringstream msg_id_ss;
    msg_id_ss << "0x" << std::hex << std::setw(4) << std::setfill('0') << msg_id;
    return msg_id_ss.str();
}

std::string GetBase64Str(const std::string& origin_str) 
{

    int origin_str_size = origin_str.size();
    
    int base64_out_len = (origin_str_size + 2) / 3 * 4; //base64的结果长度

    std::vector<char> base64_out(base64_out_len + 1, '\0');

    char* result = Base64Encode(origin_str.c_str(), origin_str_size, base64_out.data(), &base64_out_len);
    
    if (!result) 
    {
        return "";
    }

    return std::string(base64_out.data());
}

std::string GetCaeSarStr(const std::string& str, int offset_num)
{
    if (str.empty()) {
        return "";
    }
    
    int origin_str_size = str.size();
    std::vector<char> caesar_output_str(origin_str_size + 1, '\0');
    
    // 将原始字符串复制到缓冲区
    Snprintf(caesar_output_str.data(), origin_str_size + 1, str.c_str());
    
    if (!offset_num)
    {
        akuvox_encrypt::CaesarDecry(caesar_output_str.data());
    } 
    else
    {
        akuvox_encrypt::CaesarDecry(caesar_output_str.data(), offset_num);
    } 
    
    return std::string(caesar_output_str.data());
}

// 字符串中提取第一段出现的数字
std::string ExtractFirstNumber(const std::string& input) {
    std::string number;
    bool number_started = false;

    for (char ch : input) {
        if (std::isdigit(ch)) {
            number += ch;
            number_started = true;
        } else if (number_started) {
            // 如果数字已经开始，并且遇到非数字字符，则停止
            break;
        }
    }

    return number.empty() ? "0" : number;
}