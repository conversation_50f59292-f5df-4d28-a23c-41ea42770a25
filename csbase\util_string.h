#ifndef __AKCS_BASE_UTIL_STRING_H__
#define __AKCS_BASE_UTIL_STRING_H__
#include <set>
#include <vector>
#include <map>
#include <list>
#include <iomanip> 
#include <sstream> 

#define PHONE_DETECT_NUM    (7)


class CStrExplode
{
public:
	CStrExplode(const char* str, char seperator);
	virtual ~CStrExplode();

	uint32_t GetItemCnt() { return m_item_cnt; }
	char* GetItem(uint32_t idx) { return m_item_list[idx]; }
private:
	uint32_t	m_item_cnt;
	char** 		m_item_list;
};


void StringReplace(std::string &replace_string, const std::string &src_string, const std::string &dst_string);
std::string Int2String(uint32_t user_id);
uint32_t String2Int(const std::string& value);

inline unsigned char ToHex(const unsigned char &x);
inline unsigned char FromHex(const unsigned char &x);
std::string URLEncode(const std::string &sIn);
std::string URLDecode(const std::string &sIn);

///////////////////////////////////////

void SplitString(const std::string& s, const std::string& c, std::vector<std::string>& oVec);
void SplitString(const std::string& s, const std::string& c, std::set<std::string>& oVec);
void SplitString(const std::string& s, const std::string& c, std::list<std::string>& oList);

unsigned int HashtabHashString(const void *obj);
std::string GetSubstrFromBehind(const std::string &src, int nPos);
int GetStrMatchNumFromBehind(const std::string& src, const std::string& dect);
int StringAllisNum(std::string str);
bool CheckStrInFilter(const std::string& filter, const std::string& str);

//转义操作
void ChangeSpecialXmlChar(char* dest, size_t dest_len, const char* src, size_t src_len);
int StrHash(const std::string &str, int mod);
std::string GetHashValStr(const std::string& str);
int GetFirstDiffBit(int num1, int num2);
std::string ListToSeparatedFormatString(const std::vector<std::string> &datas);
std::string ListToSeparatedFormatString(const std::set<std::string> &datas);
std::string ListToSeparatedFormatString(const std::vector<int> &datas);
std::string ListToSeparatedFormatString(const std::vector<unsigned int> &datas);
std::string ListToSeparatedFormatString(const std::set<int> &datas);
uint32_t crc32_hash(const std::string &key);
void DataMasking(std::string& message);
std::string GenerateNFCCode();
std::string GenerateBLECode();
std::string GetNbitRandomString(int length);

//通过最后一个分隔符分隔字符串
void SplitStringFromLastFilter(const std::string& src_str, const std::string& filter, std::string& str_before, std::string& str_after);
void TrimString(std::string& str);

bool StringEndsWith(const std::string& full_string, const std::string& ending);
bool StringStartsWith(const std::string& str, const std::string prefix);

std::string ListToSeparatedFormatString(const std::vector<std::string> &datas, char separator);

std::string GetMsgIDStr(int msg_id);
std::string GetJsonString(const std::map<std::string, std::string>& str_datas, const std::map<std::string, int>& int_datas, const std::map<std::string, bool>& bool_datas);
std::string GetLogicServerIp(const std::string& logic_server_id);

std::string GetBase64Str(const std::string& strA);
std::string GetCaeSarStr(const std::string& str, int offset_num = 0);

std::string ExtractFirstNumber(const std::string& input);
#endif //__AKCS_BASE_UTIL_STRING_H__

