#include "util_time.h"
#include <sstream>
#include <unistd.h>
#include <net/if.h>
#include <sys/ioctl.h>
#include <ctime>
#include <boost/algorithm/string.hpp>
#include <boost/functional/hash.hpp>
#include <boost/format.hpp>
#include <boost/crc.hpp>
#include <encrypt/Md5.h>
#include <json/json.h>

AkSecSeqMng g_sec_seq_mng;
///时间相关工具
int GetCurrentDateTime(DateTime *pDateTime)
{
	if (pDateTime == nullptr)
	{
		return -1;
	}

    time_t timep;
	struct tm *p;
	time(&timep);
	p=localtime(&timep);
    
	pDateTime->nYear = 1900 + p->tm_year;
	pDateTime->nMonth = p->tm_mon + 1;
	pDateTime->nDay = p->tm_mday;
	pDateTime->nDayOfWeek = p->tm_wday; //星期数从 0（星期天）到 6（星期六）
	pDateTime->nHour = p->tm_hour;
	pDateTime->nMin = p->tm_min;
	pDateTime->nSec = p->tm_sec;

	return 0;
}

//蔡勒公式 计算是星期几 1~7
int GetWeekDayByTime(int year, int month, int day)
{
    if(month<3)
    {
        year-=1;
        month+=12;
    }
    int c=year/100;
    int y=year%100;
    int w=((c/4)-2*c+(y+y/4)+(13*(month+1)/5)+day-1)%7;
    if(0 == w)  //周日
    {
        w = 7;
    }
    return w;   
}

int WeekDayConvertToMonthDay(int which_one, int week_day, int month)
{
    int day;
    time_t timet = time(NULL);
    tm* now_time = localtime(&timet);
    int year = 1900 + now_time->tm_year;    //获取当前年份
    int week_day_first = GetWeekDayByTime(year, month, 1);   //获取本月1号是周几
    if((week_day - week_day_first) < 0) //第二周才有week_day
    {
        day = 7 - week_day_first + week_day + 1 + 7*(which_one-1) ;
    }
    else
    {
        day = week_day - week_day_first +  1 + 7*(which_one-1); //本月的第一个week_day是几号：week_day - week_day_first +  1
    }
    
    int month_total_days = 30;
    switch(month)
    {
        case 2:
            month_total_days = 29;//有可能28，在这里有可能会差一天进入夏令时。TODO：后期在处理
            break;       
        case 1:
        case 3:
        case 5:
        case 7:
        case 8:
        case 10:
        case 12:
            month_total_days = 31;
            break;
        default:
            month_total_days = 30;
    }
    if (day > month_total_days)
    {
        day = day - 7;//例如:当月没有5个周天，取4个周天
    }
    return day;   
}

int DateTimeStr2DataTime(const char *pszDateTime, DateTime *pTimeData)
{
	if (pszDateTime == nullptr|| pTimeData == nullptr)
	{
		return -1;
	}
	::sscanf(pszDateTime, "%d-%d-%d %d:%d:%d", &pTimeData->nYear, &pTimeData->nMonth, &pTimeData->nDay, &pTimeData->nHour, &pTimeData->nMin, &pTimeData->nSec);
	return 0;
}

int DayTimeStr2DayTime(const char *pszDayTime, DayTime *pTimeData)
{
	if (pszDayTime == nullptr|| pTimeData == nullptr)
	{
		return -1;
	}
	::sscanf(pszDayTime, "%d:%d:%d", &pTimeData->nHour, &pTimeData->nMin, &pTimeData->nSec);
    return 0;
}


//a<b
//inline bool CDateTimeCmp::operator<(const CDateTimeCmp& rhs) const
//inline函数有静态链接属性，不被其他文件看到,所以当外部文件调用这个接口时,编译报错.
bool CDateTimeCmp::operator<(const CDateTimeCmp& rhs) const
{
    if (date_time_.nYear > rhs.date_time_.nYear)
    {
        return false;
    }
    else if (date_time_.nYear < rhs.date_time_.nYear)
    {
        return true;
    }

    if (date_time_.nMonth > rhs.date_time_.nMonth)
    {
        return false;
    }
    else if (date_time_.nMonth < rhs.date_time_.nMonth)
    {
        return true;
    }

    if (date_time_.nDay > rhs.date_time_.nDay)
    {
        return false;
    }
    else if (date_time_.nDay < rhs.date_time_.nDay)
    {
        return true;
    }

    if (date_time_.nHour > rhs.date_time_.nHour)
    {
        return false;
    }
    else if (date_time_.nHour < rhs.date_time_.nHour)
    {
        return true;
    }

    if (date_time_.nMin > rhs.date_time_.nMin)
    {
        return false;
    }
    else if (date_time_.nMin < rhs.date_time_.nMin)
    {
        return true;
    }

    if (date_time_.nSec > rhs.date_time_.nSec)
    {
        return false;
    }
    else if (date_time_.nSec < rhs.date_time_.nSec)
    {
        return true;
    }
	//都相等
	return false;

}

bool CTimeCmp::operator<(const CTimeCmp& rhs) const
{

    if (time_.nHour > rhs.time_.nHour)
    {
        return false;
    }
    else if (time_.nHour < rhs.time_.nHour)
    {
        return true;
    }

    if (time_.nMin > rhs.time_.nMin)
    {
        return false;
    }
    else if (time_.nMin < rhs.time_.nMin)
    {
        return true;
    }

    if (time_.nSec > rhs.time_.nSec)
    {
        return false;
    }
    else if (time_.nSec < rhs.time_.nSec)
    {
        return true;
    }
    //都相等
    return false;

}

//时区转换相关
int GetOffsetTimeSec(const string& time_zone)
{
	auto pos = time_zone.find(' ');//时区格式: -10:00 Tahiti
	if(std::string::npos == pos)
	{
		return 0;
	}
	std::string time_str = time_zone.substr(0, pos);//获取到 '-10:45'
	auto pos2 = time_str.find(':');//时区格式: -10:00 Tahiti
	if(std::string::npos == pos2)
	{
		return 0;
	}
	std::string time_flag = time_str.substr(0, 1);//获取到 '-/+'
	int time_flag_int = time_flag == "-" ? -1 : 1;
	std::string time_hour = time_str.substr(1, pos2 - 1);//获取到 '10'
	std::string time_minute = time_str.substr(pos2 + 1);//获取到 '45'
	return time_flag_int * (String2Int(time_hour) * kOneHourSecond + String2Int(time_minute) * kOneMinuteSecond);
	
}


//added by chenyc,2019-09-17,v4.6, 字符串形式的转换函数
//平台端v4.6时区形式作出修改,由原来的 20 50等数值格式，改成:+0:00 Abidjan 字符格式.
std::string GetDateByTimeZoneStr(const string& time_zone, std::time_t time_stamp_beijing)
{
    int offset_time = GetOffsetTimeSec(time_zone);
    int time_diff  = offset_time - kSysTimezoneSecond;
	std::time_t time_stamp_dst = time_stamp_beijing + time_diff;
	struct tm* tm_dst = std::localtime(&time_stamp_dst);
	char time[100];
	strftime(time, sizeof(time), "%Y-%m-%d", tm_dst);
	return time;
}

//根据timezone 获取当前对应的时间, eg : 11:34:33
std::string GetDailyDateTimeByTimeZoneStr(const string& time_zone, const std::map<std::string, AKCS_DST>& dst)
{
    using namespace std::chrono;
    system_clock::time_point now = system_clock::now();

    int nOffsetTime = 0;
    nOffsetTime = GetOffsetTimeSec(time_zone);
    std::chrono::seconds t2(nOffsetTime - kSysTimezoneSecond);
    std::time_t zonetime = system_clock::to_time_t(now + t2);
    
    struct tm *newtime;
    char time[128];
    newtime=std::localtime(&zonetime);

    std::map<string, AKCS_DST>::const_iterator it = dst.find(time_zone);
    if(it != dst.end())
    {
        AKCS_DST tmp_dst = it->second;
        //按权重计算后进行日期比较
        int now_time = (newtime->tm_mon+1)*10000 + newtime->tm_mday*100 + newtime->tm_hour;
        int start_time = tmp_dst.start_month*10000 + tmp_dst.start_day*100 + tmp_dst.start_hour;
        int end_time = tmp_dst.end_month*10000 + tmp_dst.end_day*100 + tmp_dst.end_hour;
        int cross_year = (start_time > end_time)?1:0; //夏令时是否跨年
        if((0 == cross_year && (now_time > start_time && now_time < end_time)) || \
           (1 == cross_year && (now_time > start_time || now_time < end_time)))  
        {
            std::chrono::seconds t3(nOffsetTime - kSysTimezoneSecond + (tmp_dst.offset*60));
            std::time_t dsttime = system_clock::to_time_t(now + t3);
            newtime=std::localtime(&dsttime);
        }
    }
    
    strftime(time, 128, "%T", newtime);   
    return time;
}

// 处理时区和夏令时调整
std::string FormatDateTimeWithTimeZone(std::time_t time_stamp, const string& time_zone, const std::map<std::string, AKCS_DST>& dst)
{
    int nOffsetTime = GetOffsetTimeSec(time_zone);
    std::time_t zonetime = time_stamp + (nOffsetTime - kSysTimezoneSecond);
    
    struct tm *newtime;
    char time[128];
    newtime=std::localtime(&zonetime);

    std::map<string, AKCS_DST>::const_iterator it = dst.find(time_zone);
    if(it != dst.end())
    {
        AKCS_DST tmp_dst = it->second;
        //按权重计算后进行日期比较
        int now_time = (newtime->tm_mon+1)*10000 + newtime->tm_mday*100 + newtime->tm_hour;
        int start_time = tmp_dst.start_month*10000 + tmp_dst.start_day*100 + tmp_dst.start_hour;
        int end_time = tmp_dst.end_month*10000 + tmp_dst.end_day*100 + tmp_dst.end_hour;
        int cross_year = (start_time > end_time)?1:0; //夏令时是否跨年
        if((0 == cross_year && (now_time > start_time && now_time < end_time)) || \
           (1 == cross_year && (now_time > start_time || now_time < end_time)))  
        {
            std::time_t dsttime = time_stamp + (nOffsetTime - kSysTimezoneSecond + (tmp_dst.offset*60));
            newtime=std::localtime(&dsttime);
        }
    }
    
    strftime(time, 128, "%F %T", newtime);   
    return time;
}

//根据timezone 获取当前对应的时间, eg : 2024-08-07 11:34:33
std::string GetNodeNowDateTimeByTimeZoneStr(const string& time_zone, const std::map<std::string, AKCS_DST>& dst)
{
    using namespace std::chrono;
    system_clock::time_point now = system_clock::now();
    std::time_t current_time = system_clock::to_time_t(now);
    
    return FormatDateTimeWithTimeZone(current_time, time_zone, dst);
}

std::string GetDateTimeByTimeZoneStr(std::time_t time_stamp, const string& time_zone, const std::map<std::string, AKCS_DST>& dst)
{
    return FormatDateTimeWithTimeZone(time_stamp, time_zone, dst);
}

//根据timezone 获取当前周几和时:分:秒
int GetWeekDayAndTimeByTimeZoneStr(const string& time_zone, string& time_ret, std::map<string, AKCS_DST>& dst)
{
    using namespace std::chrono;
    system_clock::time_point now = system_clock::now();

    int nOffsetTime = 0;
    nOffsetTime = GetOffsetTimeSec(time_zone);
    std::chrono::seconds t2(nOffsetTime - kSysTimezoneSecond);
    std::time_t zonetime = system_clock::to_time_t(now + t2);
    
    struct tm *newtime;
    char time[128];
    newtime=std::localtime(&zonetime);

    std::map<string, AKCS_DST>::iterator it = dst.find(time_zone);
    if(it != dst.end())
    {
        AKCS_DST tmp_dst = it->second;
        //按权重计算后进行日期比较
        int now_time = (newtime->tm_mon+1)*10000 + newtime->tm_mday*100 + newtime->tm_hour;
        int start_time = tmp_dst.start_month*10000 + tmp_dst.start_day*100 + tmp_dst.start_hour;
        int end_time = tmp_dst.end_month*10000 + tmp_dst.end_day*100 + tmp_dst.end_hour;
        int cross_year = (start_time > end_time)?1:0; //夏令时是否跨年
        if((0 == cross_year && (now_time > start_time && now_time < end_time)) || \
           (1 == cross_year && (now_time > start_time || now_time < end_time)))          
        {
            std::chrono::seconds t3(nOffsetTime - kSysTimezoneSecond + (tmp_dst.offset*60));
            std::time_t dsttime = system_clock::to_time_t(now + t3);
            newtime=std::localtime(&dsttime);
        }
    }
    
    strftime(time, 128, "%T", newtime);
    time_ret = time;
    return newtime->tm_wday;
}

std::string GetOffsetDateTimeByTimeZoneStr(const string& time_zone, int offset_sec)
{
    using namespace std::chrono;
    system_clock::time_point now = system_clock::now();

    int nOffsetTime = 0;
    nOffsetTime = GetOffsetTimeSec(time_zone);
    std::chrono::seconds t2(nOffsetTime - kSysTimezoneSecond + offset_sec);
    std::time_t zonetime = system_clock::to_time_t(now + t2);
    
    struct tm *newtime;
    char time[128];
    newtime=std::localtime(&zonetime);
    strftime(time, 128, "%F %T", newtime);
    
    return time;
}

void GetNowTime(char *pszDate, int size)
{
	if (pszDate == nullptr)
	{
		return;
	}
    time_t timep;
	struct tm *p;
	time(&timep);
	p=localtime(&timep);
	snprintf (pszDate, size, "%d-%02d-%02d %02d:%02d:%02d", (1900+p->tm_year),(p->tm_mon + 1), p->tm_mday,
	          p->tm_hour, p->tm_min, p->tm_sec);
	return;
}

std::string GetNowDate()
{
    time_t timep;
	struct tm *p;
	time(&timep);
	p=localtime(&timep);
    char str[32] = "";
	snprintf (str, sizeof(str), "%d-%02d-%02d", (1900+p->tm_year),(p->tm_mon + 1), p->tm_mday);
	return str;
}

std::string GetNowDateStr()
{
    char buf[64];
    time_t tt = time(NULL);
    strftime(buf, sizeof buf, "%a, %b %d %Y %H:%M:%S GMT", gmtime(&tt));
    return buf;
}

std::string GetNowTime()
{
    time_t timep;
	struct tm *p;
	time(&timep);
	p=localtime(&timep);
    char str[32] = "";
	snprintf (str, sizeof(str), "%02d%02d%02d", p->tm_hour, p->tm_min, p->tm_sec);
	return str;
}

// 数据库的值 -->> 设备配置的值
// type: 1 - 12h/24h格式   2 - 年月日或日月年等格式
int CustomizeDateFormatToDeviceConfigValue(int customize_form, int type)
{
    CUSTOMIZE_TIME_FORMAT time_type;
    CUSTOMIZE_DATE_FORMAT date_type;
    ParseCustomizeDateFormat(customize_form, time_type, date_type);
    if(type == 1)
    {
        if(time_type == CUSTOMIZE_TIME_FORMAT::AKCS_TIME_FORMAT_12H)
        {
            return 0;   //12h
        }
        else
        {
            return 1;   //24h
        }
    }
    else
    {
        if(date_type == CUSTOMIZE_DATE_FORMAT::AKCS_TIME_FORMAT_YMD)
        {
            return 0;  //Y-m-d
        }
        else if(date_type == CUSTOMIZE_DATE_FORMAT::AKCS_TIME_FORMAT_MDY)
        {
            return 6;  //m-d-Y
        }
        else
        {
            return 2;  //d-m-Y
        }
    }
}

int ParseCustomizeDateFormat(int customize_form, CUSTOMIZE_TIME_FORMAT& time_type, CUSTOMIZE_DATE_FORMAT& date_type)
{
    switch (customize_form)
    {
        case 2:
            time_type = CUSTOMIZE_TIME_FORMAT::AKCS_TIME_FORMAT_12H;
            date_type = CUSTOMIZE_DATE_FORMAT::AKCS_TIME_FORMAT_YMD;
            break;
        case 3:
            time_type = CUSTOMIZE_TIME_FORMAT::AKCS_TIME_FORMAT_24H;
            date_type = CUSTOMIZE_DATE_FORMAT::AKCS_TIME_FORMAT_YMD;
            break;
        case 4:
            time_type = CUSTOMIZE_TIME_FORMAT::AKCS_TIME_FORMAT_12H;
            date_type = CUSTOMIZE_DATE_FORMAT::AKCS_TIME_FORMAT_MDY;
            break;
        case 5:
            time_type = CUSTOMIZE_TIME_FORMAT::AKCS_TIME_FORMAT_24H;
            date_type = CUSTOMIZE_DATE_FORMAT::AKCS_TIME_FORMAT_MDY;
            break;
        case 6:
            time_type = CUSTOMIZE_TIME_FORMAT::AKCS_TIME_FORMAT_12H;
            date_type = CUSTOMIZE_DATE_FORMAT::AKCS_TIME_FORMAT_DMY;
            break;
        case 7:
            time_type = CUSTOMIZE_TIME_FORMAT::AKCS_TIME_FORMAT_24H;
            date_type = CUSTOMIZE_DATE_FORMAT::AKCS_TIME_FORMAT_DMY;
            break;
        default:
            time_type = CUSTOMIZE_TIME_FORMAT::AKCS_TIME_FORMAT_12H;
            date_type = CUSTOMIZE_DATE_FORMAT::AKCS_TIME_FORMAT_YMD;
            break;
    }

    return 0;
}

/*
    * @brief:                 将时间字符串转换成 Account表的CustomizeForm字段的指定格式
    * @param time_str:        时间字符串 eg: 2016-08-02 12:12:30
    * @param customize_form:  Account表的CustomizeForm字段
    * @return:                转换后的时间字符串
*/
std::string TimeFormateConvert(const std::string& time_str, int customize_form)
{
    CUSTOMIZE_TIME_FORMAT time_type;
    CUSTOMIZE_DATE_FORMAT date_type;
    ParseCustomizeDateFormat(customize_form, time_type, date_type);
    return TimeFormateConvert(time_str, time_type, date_type);
}

std::tm ParseTimeString(const std::string& time_str)
{
    std::tm tmp_tm;
    memset(&tmp_tm, 0, sizeof(tmp_tm));
    const char* format = "%Y-%m-%d %H:%M:%S";
    if (strptime(time_str.c_str(), format, &tmp_tm) == nullptr)
    {
        AK_LOG_WARN << "Failed to parse time string: time_str=" << time_str;
    }
    return tmp_tm;
}

// 将时间字符串转换成 Account表的CustomizeForm字段的指定格式
std::string TimeFormateConvert(const std::string& time_str, CUSTOMIZE_TIME_FORMAT time_type, CUSTOMIZE_DATE_FORMAT date_type)
{
    std::tm tm = ParseTimeString(time_str);
    if (tm.tm_year == 0)
    {
        AK_LOG_WARN << "Failed to parse time string: time_str=" << time_str;
        return time_str;
    }

    char buffer[256] = { 0 };
    std::string date_format = "";
    if (date_type == CUSTOMIZE_DATE_FORMAT::AKCS_TIME_FORMAT_YMD)
    {
        date_format = "%Y-%m-%d";
    }
    else if (date_type == CUSTOMIZE_DATE_FORMAT::AKCS_TIME_FORMAT_MDY)
    {
        date_format = "%m-%d-%Y";
    }
    else
    {
        date_format = "%d-%m-%Y";
    }

    if (time_type == CUSTOMIZE_TIME_FORMAT::AKCS_TIME_FORMAT_12H)
    {
        date_format += " %I:%M:%S %p";
    }
    else
    {
        date_format += " %H:%M:%S";
    }

    if (strftime(buffer, sizeof(buffer), date_format.c_str(), &tm) == 0)
    {
        AK_LOG_WARN << "Failed to format time: time_str=" << time_str;
        return time_str;
    }
    return std::string(buffer);
}


std::string WeekBinaryToString(const int &binary)
{
    const int week_number = 7;
	std::stringstream week;

	for (int i = 0; i < week_number; i++)
	{
		if ((binary)&(1 << i))
		{
			week << i;
		}
	}

	return week.str();
}



//标准时间格式例如:2016:08:02 12:12:30, 转换成时间戳:1470111150
int StandardTimeToStamp(const char *time)
{
    struct tm stm;
    int year, month, day, hour, min, sec;
    
    memset(&stm, 0, sizeof(stm));
    year = ATOI(time);
    month = ATOI(time + 5);
    day = ATOI(time + 8);
    hour = ATOI(time + 11);
    min = ATOI(time + 14);
    sec = ATOI(time + 17);
    
    stm.tm_year = year - 1900;
    stm.tm_mon = month - 1;
    stm.tm_mday = day;
    stm.tm_hour = hour;
    stm.tm_min = min;
    stm.tm_sec = sec;
    return (int)mktime(&stm);
}

//时间戳例如1470111150, 转换成标准时间格式:2016-08-02 12:12:30
std::string StampToStandardTime(int time_stamp)
{
    time_t time_tmp = static_cast<time_t>(time_stamp);
    struct tm *time = localtime(&time_tmp);

    char time_buffer[256] = {0};
    snprintf(time_buffer, sizeof(time_buffer), "%04d-%02d-%02d %02d:%02d:%02d", 
             time->tm_year + 1900, time->tm_mon + 1, time->tm_mday, 
             time->tm_hour, time->tm_min, time->tm_sec);

    return std::string(time_buffer);
}

int CheckOnceSchedulerTime(const std::string &now_day, const std::string &begin_day, const std::string &end_day)
{
    int begin_time = StandardTimeToStamp(begin_day.c_str());
    int end_time = StandardTimeToStamp(end_day.c_str());
    int now_time = StandardTimeToStamp(now_day.c_str());
    if(begin_time < now_time && now_time < end_time)
    {
        return 1;
    }
    else
    {
        return 0;
    }
}

long long GetCurrentMilliTimeStamp()
{
	return std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
}

int GetCurrentTimeStamp()
{
    return std::time(0);
}

//秒级序列号生成器,每秒都会周期刷新
uint32_t SecSeqCreate()
{
	return g_sec_seq_mng.GetSeq();
}

void ParseDSTByWeekDay(const std::vector<std::string>& starts, const std::vector<std::string>& ends, AKCS_DST* dst)
{
    int which_one;
    int week_day;
    
    dst->start_month = ATOI(starts[0].c_str());
    which_one = ATOI(starts[1].c_str());
    week_day = ATOI(starts[2].c_str());
    dst->start_hour = ATOI(starts[3].c_str());
    dst->start_day = WeekDayConvertToMonthDay(which_one, week_day, dst->start_month);

    dst->end_month = ATOI(ends[0].c_str());
    which_one = ATOI(ends[1].c_str());
    week_day = ATOI(ends[2].c_str());
    dst->end_hour = ATOI(ends[3].c_str());
    dst->end_day = WeekDayConvertToMonthDay(which_one, week_day, dst->end_month);

}

void ParseDST(const AKCS_TIME_ZONE& timezone, AKCS_DST& timedst)
{
    timedst.offset = timezone.offset;
    std::vector<std::string> starts;
    std::vector<std::string> ends;
    SplitString(timezone.start, "/", starts);   //如 3/2/7/2
    SplitString(timezone.end, "/", ends);

    if(0 == timezone.type)  //年月日的类型
    {
        timedst.start_month = ATOI(starts[0].c_str());
        timedst.end_month = ATOI(ends[0].c_str());
        timedst.start_day = ATOI(starts[1].c_str());
        timedst.end_day = ATOI(ends[1].c_str());
        timedst.start_hour = ATOI(starts[2].c_str());
        timedst.end_hour = ATOI(ends[2].c_str());        
    }
    else    //第几个周几的类型，进行转换
    {
        ParseDSTByWeekDay(starts, ends, &timedst);
    }
}

int ParseTimeZone(const std::string& timezone_xml_path, std::map<string, AKCS_DST>& time_dst)
{
    TiXmlDocument doc;
    if (!doc.LoadFile(timezone_xml_path))
    {
        AK_LOG_WARN << "XML LoadFile failed.";
        return -1;
    }
    TiXmlElement* root = doc.RootElement();
    TiXmlElement* node = NULL;
    if (NULL == root)
    {
        AK_LOG_WARN << "ROOT Node is NULL";
        return -1;
    }

    for (node = root->FirstChildElement(); node; node = node->NextSiblingElement())
    {
        AKCS_TIME_ZONE tmp_timezone;
        memset(&tmp_timezone, 0, sizeof(AKCS_TIME_ZONE));
        for (TiXmlAttribute* pAttribute = node->FirstAttribute(); pAttribute; pAttribute = pAttribute->Next())
        {
            if (strcmp(pAttribute->Name(), "TimeZone") == 0)
            {  
                Snprintf(tmp_timezone.time_zone, sizeof(tmp_timezone.time_zone), pAttribute->Value());
            }
            else if (strcmp(pAttribute->Name(), "Name") == 0)
            {
                Snprintf(tmp_timezone.name, sizeof(tmp_timezone.name), pAttribute->Value());
            }
            else if (strcmp(pAttribute->Name(), "Type") == 0)
            {
                tmp_timezone.type = ATOI(pAttribute->Value());
            }
            else if (strcmp(pAttribute->Name(), "Start") == 0)
            {
                Snprintf(tmp_timezone.start, sizeof(tmp_timezone.start), pAttribute->Value());
            }
            else if (strcmp(pAttribute->Name(), "End") == 0)
            {
                Snprintf(tmp_timezone.end, sizeof(tmp_timezone.end), pAttribute->Value());
            }
            else if (strcmp(pAttribute->Name(), "Offset") == 0)
            {
                tmp_timezone.offset = ATOI(pAttribute->Value());
            }            
        }


        if(0 == tmp_timezone.offset)
        {
            continue;
        }
        
        std::string str_time_zone = tmp_timezone.time_zone;
        str_time_zone += " ";
        str_time_zone += tmp_timezone.name;
        AKCS_DST timedst;
        memset(&timedst, 0, sizeof(AKCS_DST));
        ParseDST(tmp_timezone, timedst);   
        time_dst.insert(std::pair<std::string, AKCS_DST>(str_time_zone, timedst));       
    }
    return 0;
}

//获取当前时间
CString BaseGetCurTime()
{
    time_t timep;
    struct tm* p;
    time(&timep);
    p = localtime(&timep);

    CString strCurTime;
    //strCurTime.Format(_T(FORMATE_GET_DATE_TIME_FROM_INT), curTime.wYear, curTime.wMonth, curTime.wDay, curTime.wHour, curTime.wMinute, curTime.wSecond);
    strCurTime.Format(_T(FORMATE_GET_DATE_TIME_FROM_INT), (1900 + p->tm_year), (p->tm_mon + 1), p->tm_mday, p->tm_hour, p->tm_min, p->tm_sec);
    return strCurTime;
}
