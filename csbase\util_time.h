#ifndef __AKCS_BASE_UTIL_TIME_H__
#define __AKCS_BASE_UTIL_TIME_H__
#include <set>
#include <vector>
#include <map>
#include <list>
#include "util.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/stat.h>
#include <assert.h>
#include <stdarg.h>
#include <pthread.h>
#include <time.h>
#include <sys/time.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <random>
#include <ctime>
#include <chrono>
#include <mutex>
#include <iostream>
#include <sstream>
#include <math.h>
#include "BasicDefine.h"
#include "util_string.h"
#include "AkcsCommonSt.h"
#include "Tinyxml/tinyxml.h"
#include "util_cstring.h"
#include "AkLogging.h"

static const int kSysTimezoneSecond = 8*3600;//北京东八区
static const int kOneHourSecond = 3600;
static const int kThirtyMinSecond = 1800;
static const int kOneMinuteSecond = 60;

// Account 表的CustomizeForm字段 - 时间格式
enum class CUSTOMIZE_TIME_FORMAT
{
    AKCS_TIME_FORMAT_12H = 1,   // 12小时制
    AKCS_TIME_FORMAT_24H = 2,   // 24小时制
};

// Account 表的CustomizeForm字段 - 日期格式
enum class CUSTOMIZE_DATE_FORMAT
{
    AKCS_TIME_FORMAT_YMD = 1,   // y-m-d
    AKCS_TIME_FORMAT_MDY = 3,   // m-d-y
    AKCS_TIME_FORMAT_DMY = 5,   // d-m-y
};

//时间相关操作工具
typedef struct DATE_TIME_T
{
	int nYear;
	int nMonth;
	int nDay;
	int nDayOfWeek;
	int nHour;
	int nMin;
	int nSec;
}DateTime;

typedef struct TIME_T
{
	int nHour;
	int nMin;
	int nSec;
}DayTime;

typedef struct AKCS_DST_T
{
    int start_month;
    int start_hour;
    int start_day;
    int end_month;
    int end_hour;
    int end_day;
    int offset;
} AKCS_DST;

int GetCurrentDateTime(DateTime *pDateTime);
int GetWeekDayByTime(int year, int month, int day);
int WeekDayConvertToMonthDay(int which_one, int week_day, int month);
int DateTimeStr2DataTime(const char *pszDateTime, DateTime *pTimeData);
int DayTimeStr2DayTime(const char *pszDayTime, DayTime *pTimeData);

class CDateTimeCmp
{
public:
	CDateTimeCmp(const DateTime& date_time)
    {
        memcpy(&date_time_, &date_time, sizeof(date_time));
    }
	virtual ~CDateTimeCmp()
    {}   
    bool operator< (const CDateTimeCmp& rhs) const;
private:
    CDateTimeCmp();
    
public:
	DateTime date_time_;
};

class CTimeCmp
{
public:
	CTimeCmp(const DayTime& time)
    {
        memcpy(&time_, &time, sizeof(time));
    }
	virtual ~CTimeCmp()
    {}   
    bool operator< (const CTimeCmp& rhs) const;
private:
    CTimeCmp();
    
public:
	DayTime time_;
};

int GetOffsetTimeSec(const string& time_zone);
int StandardTimeToStamp(const char *time);
std::string StampToStandardTime(int time_stamp);

std::string GetDailyDateTimeByTimeZoneStr(const string& time_zone, const std::map<std::string, AKCS_DST>& dst);
std::string GetDateByTimeZoneStr(const string& time_zone, std::time_t time_stamp_beijing);
std::string FormatDateTimeWithTimeZone(std::time_t time_stamp, const string& time_zone, const std::map<std::string, AKCS_DST>& dst);
std::string GetNodeNowDateTimeByTimeZoneStr(const string& time_zone, const std::map<std::string, AKCS_DST>& dst);
std::string GetDateTimeByTimeZoneStr(std::time_t time_stamp, const string& time_zone, const std::map<std::string, AKCS_DST>& dst);
int GetWeekDayAndTimeByTimeZoneStr(const string& time_zone, string& time_ret, std::map<std::string, AKCS_DST>& dst);
std::string GetOffsetDateTimeByTimeZoneStr(const string& time_zone, int offset_sec);
void GetNowTime(char *pszDate, int size);
std::string GetNowDateStr();
std::string GetNowDate();
std::string GetNowTime();
std::string WeekBinaryToString(const int &binary);
int CheckOnceSchedulerTime(const std::string &now_day, const std::string &begin_day, const std::string &end_day);

//          以秒为单位获取时间戳
int         GetCurrentTimeStamp();
//          以秒为单位获取毫秒时间戳
long long   GetCurrentMilliTimeStamp();

//          解析时间字符串
std::tm     ParseTimeString(const std::string& time_str);
//          数据库的值 -->> 设备配置的值
int         CustomizeDateFormatToDeviceConfigValue(int time_format, int type);
//          解析Account表的CustomizeForm字段成为格式类型
int         ParseCustomizeDateFormat(int customize_form, CUSTOMIZE_TIME_FORMAT& time_type, CUSTOMIZE_DATE_FORMAT& date_type);
//          时间字符串转换成 Account表的CustomizeForm字段的指定格式
std::string TimeFormateConvert(const std::string& time_str, int customize_form);
//          时间字符串转换成指定格式
std::string TimeFormateConvert(const std::string& time_str, CUSTOMIZE_TIME_FORMAT time_type, CUSTOMIZE_DATE_FORMAT date_type);

//秒级序列号维护类,不用单例模式,因为作为公共库,无法在主线程中初始化,故只能加锁保证线程安全
class AkSecSeqMng
{
public:
	AkSecSeqMng()
    : seq_sec_(0)
    , timer_sec_(0)
    {

    }
	~AkSecSeqMng()
    {
    }   
	uint32_t GetSeq()
    {   
        time_t timer;
        timer = time(nullptr);
        if (timer == timer_sec_)
        {
            seq_sec_++;
        }
        else
        {
            seq_sec_ = 0;
            timer_sec_ = timer;
        }
        return seq_sec_;
    }
private:
	uint32_t	seq_sec_;
	time_t      timer_sec_;
};

//秒级序列号生成器,每秒都会周期刷新
uint32_t SecSeqCreate();
void ParseDSTByWeekDay(const std::vector<std::string>& starts, const std::vector<std::string>& ends, AKCS_DST* dst);
void ParseDST(const AKCS_TIME_ZONE& timezone, AKCS_DST& timedst);
int ParseTimeZone(const std::string& timezone_xml_path, std::map<string, AKCS_DST>& time_dst);
CString BaseGetCurTime();


class FunctionCostTicker {
public:
     FunctionCostTicker(const std::string &funcinfo, const std::string& flag)
        :funcinfo_(funcinfo),flag_(flag) {
            start_  = std::chrono::steady_clock::now();
    }

    ~FunctionCostTicker() {
        std::chrono::steady_clock::time_point end  = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start_);
        int time = duration.count()/1000;
        //if (time > 5)
        {
            AK_LOG_INFO  << "FunctionCost " << funcinfo_ <<" flag:"<< flag_ << "cost:" << time << "s";
        }
    }

private:
    std::string funcinfo_;
    std::string flag_;
    std::chrono::steady_clock::time_point start_;
};


#endif //__AKCS_BASE_UTIL_H__

