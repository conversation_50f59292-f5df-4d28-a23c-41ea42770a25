#include <math.h>
#include "util_string.h"
#include "util_virtual_door.h"
#include "AkLogging.h"

// door是否在付费期内
bool IsSubscribedDoor(int door_enable, int door_active, int door_expire)
{
    return door_enable && door_active && !door_expire;
}

// door是否过期
bool IsDoorInactiveOrExpired(int door_enable, int door_active, int door_expire)
{
    // 判断enable为1的door 是否active为1 且 expire为0   
    if (!door_enable)
    {
        return false;
    }

    // door 没激活 或者 过期 就算过期
    return !door_active || door_expire;
}

// A B C D 转换成 1 2 4 8
int GetRelayValueByControlledRelay(const std::string& controlled_relay)
{
    if (KRelay2PowValueMap.find(controlled_relay) != KRelay2PowValueMap.end())
    {
        return KRelay2PowValueMap.at(controlled_relay);
    }
    return 0;
}

// A B C D 转换成 1 2 3 4
int GetRelayIndexByControlledRelay(const std::string& controlled_relay)
{
    if (KRelay2IndexMap.find(controlled_relay) != KRelay2IndexMap.end())
    {
        return KRelay2IndexMap.at(controlled_relay);
    }
    return 0;
}

// 1 2 3 4 转换成 A B C D
std::string GetControlledRelayByRelayIndex(int relay_index)
{
    std::string controlled_relay;
    if (KIndex2RelayMap.find(relay_index) != KIndex2RelayMap.end())
    {
        controlled_relay = KIndex2RelayMap.at(relay_index);
    }
    return controlled_relay;
}

int GetDoorAccessControlValue(int pin_enable, int face_enable, int rf_card_enable, int ble_enable, int nfc_enable, int license_plate_enable, int finger_print_enable)
{
    return pin_enable | face_enable << 1 | rf_card_enable << 2 | ble_enable << 3 | nfc_enable << 4 | license_plate_enable << 5 | finger_print_enable << 6;
}

bool IsRs485ReaderType(const std::string& reader_type)
{
    return strstr(reader_type.c_str(), "RS485");
}

bool IsWeigenReaderType(const std::string& reader_type)
{
    return strstr(reader_type.c_str(), "Wiegand");
}

std::string GetRs485AddressConfig(const std::string& rs485_type, const std::string& rs485_type_value, const std::string& rs485_address)
{
    std::vector<std::string> rs485_address_list;
    SplitString(rs485_address, ",", rs485_address_list);

    std::stringstream rs485_address_config;
    for (const auto& rs485_address : rs485_address_list)
    {
        // 未填地址时，值默认为0
        std::string rs485_address_str = rs485_address.empty() ? "0" : rs485_address;
        rs485_address_config << rs485_type << "_" << rs485_type_value << "_" << rs485_address_str << ";";
    }

    // 去掉最后一个分号 
    std::string rs485_address_config_str = rs485_address_config.str();
    if (!rs485_address_config_str.empty())
    {
        rs485_address_config_str.pop_back();
    }
    return rs485_address_config_str;
}

std::string GetReaderConfigValue(const std::string& reader_type, const std::string& reader_type_value, const std::string& rs485_address)
{
    std::stringstream reader_config_type;
    if (IsRs485ReaderType(reader_type))
    {   
        reader_config_type << GetRs485AddressConfig(reader_type, reader_type_value, rs485_address);    
    }
    else if (IsWeigenReaderType(reader_type))
    {
        reader_config_type << reader_type << "_" << reader_type_value;
    }
    else
    {
        reader_config_type << reader_type;
    }
    return reader_config_type.str();
}

// OFF ON 转换成 0 1
int GetLockDownMode(const std::string& mode)
{
    if (KLockDownModeString2NumMap.find(mode) != KLockDownModeString2NumMap.end())
    {
        return KLockDownModeString2NumMap.at(mode);
    }
    return 0;
}

// 0 1 转换成 OFF ON
std::string GetLockDownMode(int lockdonw_switch)
{
    std::string lockdown_mode;
    if (KLockDownModeNum2StringMap.find(lockdonw_switch) != KLockDownModeNum2StringMap.end())
    {
        lockdown_mode = KLockDownModeNum2StringMap.at(lockdonw_switch);
    }
    return lockdown_mode;
}

std::string GetDoorStatusInput(const std::string& door_status_input)
{
    std::string door_status_mode;
    const char* MAGNETIC = "magnetic"; 
    
    // A094门磁状态为magneticA, magneticB, magneticC, magneticD
    // 对于A094来说，门磁状态选择magneticA，上报alarms的时候Input为Input A
    if (door_status_input.find(MAGNETIC) == 0) 
    {
        if (door_status_input.length() > strlen(MAGNETIC)) 
        {
            door_status_mode = door_status_input.substr(strlen(MAGNETIC)); // 从magneticA中提取出A
        } 
        else 
        {
            AK_LOG_WARN << "Invalid door status input: " << door_status_input;
            door_status_mode = ""; 
        }
    } 
    else 
    {
        door_status_mode = door_status_input;
    }

    return "Input " + door_status_mode;
}


