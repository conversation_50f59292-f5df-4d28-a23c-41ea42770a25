#ifndef __AKCS_BASE_UTIL_VIRTUAL_DOOR_H__
#define __AKCS_BASE_UTIL_VIRTUAL_DOOR_H__
#include <set>
#include <vector>
#include <map>
#include <list>
#include <string.h>

static const std::map<std::string, int> KRelay2IndexMap = { {"A", 1}, {"B", 2}, {"C", 3}, {"D", 4} };
static const std::map<std::string, int> KRelay2ValueMap = { {"A", 0}, {"B", 1}, {"C", 2}, {"D", 3} };
static const std::map<std::string, int> KRelay2PowValueMap = { {"A", 1}, {"B", 2}, {"C", 4}, {"D", 8} };
static const std::map<int, std::string> KIndex2RelayMap = { {1, "A"}, {2, "B"}, {3, "C"}, {4, "D"} };
static const std::map<std::string, int> KLockDownModeString2NumMap = { {"ON", 1}, {"OFF", 0} };
static const std::map<int, std::string> KLockDownModeNum2StringMap = { {1, "ON"}, { 0,"OFF"} };

bool IsSubscribedDoor(int door_enable, int door_active, int door_expire);
bool IsDoorInactiveOrExpired(int door_enable, int door_active, int door_expire);
std::string GetControlledRelayByRelayIndex(int relay_index);
int GetRelayValueByControlledRelay(const std::string& controlled_relay);
int GetRelayIndexByControlledRelay(const std::string& controlled_relay);
std::string GetRs485AddressConfig(const std::string& rs485_type, const std::string& rs485_type_value, const std::string& rs485_address);
int GetDoorAccessControlValue(int pin_enable, int face_enable, int rf_card_enable, int ble_enable, int nfc_enable, int license_plate_enable, int finger_print_enable);
std::string GetReaderConfigValue(const std::string& reader_type, const std::string& reader_type_value, const std::string& rs485_address);
int GetLockDownMode(const std::string& mode);
std::string GetLockDownMode(int lockdonw_switch);
std::string GetDoorStatusInput(const std::string& door_status_input);

#endif //__AKCS_BASE_UTIL_VIRTUAL_DOOR_H__
