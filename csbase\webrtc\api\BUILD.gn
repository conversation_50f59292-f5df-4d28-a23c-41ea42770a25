# Copyright (c) 2015 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("../webrtc.gni")
if (is_android) {
  import("//build/config/android/config.gni")
  import("//build/config/android/rules.gni")
}

group("api") {
  visibility = [ "*" ]
  deps = []

  if (!build_with_mozilla) {
    deps += [ ":libjingle_peerconnection_api" ]
  }
}

rtc_source_set("call_api") {
  visibility = [ "*" ]
  sources = [
    "call/audio_sink.h",
  ]
}

rtc_source_set("callfactory_api") {
  visibility = [ "*" ]
  sources = [
    "call/call_factory_interface.h",
  ]
}

rtc_static_library("create_peerconnection_factory") {
  visibility = [ "*" ]
  sources = [
    "create_peerconnection_factory.cc",
    "create_peerconnection_factory.h",
  ]
  deps = [
    ":callfactory_api",
    ":fec_controller_api",
    ":libjingle_peerconnection_api",
    ":network_state_predictor_api",
    ":scoped_refptr",
    "../logging:rtc_event_log_api",
    "../logging:rtc_event_log_impl_base",
    "../media:rtc_audio_video",
    "../media:rtc_media_base",
    "../modules/audio_device:audio_device_api",
    "../modules/audio_processing:api",
    "../pc:peerconnection",
    "../rtc_base",
    "../rtc_base:deprecation",
    "../rtc_base:rtc_base_approved",
    "audio:audio_mixer_api",
    "audio_codecs:audio_codecs_api",
    "transport:network_control",
    "video_codecs:video_codecs_api",
  ]
}

rtc_source_set("rtp_headers") {
  visibility = [ "*" ]
  sources = [
    "rtp_headers.cc",
    "rtp_headers.h",
  ]
  deps = [
    ":array_view",
    "..:webrtc_common",
    "video:video_frame",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_static_library("libjingle_peerconnection_api") {
  visibility = [ "*" ]
  cflags = []
  sources = [
    "async_resolver_factory.h",
    "bitrate_constraints.h",
    "candidate.cc",
    "candidate.h",
    "congestion_control_interface.h",
    "crypto/crypto_options.cc",
    "crypto/crypto_options.h",
    "crypto/frame_decryptor_interface.h",
    "crypto/frame_encryptor_interface.h",
    "crypto_params.h",
    "data_channel_interface.cc",
    "data_channel_interface.h",
    "datagram_transport_interface.h",
    "dtls_transport_interface.cc",
    "dtls_transport_interface.h",
    "dtmf_sender_interface.h",
    "ice_transport_interface.h",
    "jsep.cc",
    "jsep.h",
    "jsep_ice_candidate.cc",
    "jsep_ice_candidate.h",
    "jsep_session_description.h",
    "media_stream_interface.cc",
    "media_stream_interface.h",
    "media_stream_proxy.h",
    "media_stream_track_proxy.h",
    "media_transport_config.cc",
    "media_transport_config.h",
    "media_transport_interface.cc",
    "media_transport_interface.h",
    "media_types.cc",
    "media_types.h",
    "notifier.h",
    "peer_connection_factory_proxy.h",
    "peer_connection_interface.cc",
    "peer_connection_interface.h",
    "peer_connection_proxy.h",
    "proxy.cc",
    "proxy.h",
    "rtc_error.cc",
    "rtc_error.h",
    "rtp_parameters.cc",
    "rtp_parameters.h",
    "rtp_receiver_interface.cc",
    "rtp_receiver_interface.h",
    "rtp_sender_interface.cc",
    "rtp_sender_interface.h",
    "rtp_transceiver_interface.cc",
    "rtp_transceiver_interface.h",
    "sctp_transport_interface.cc",
    "sctp_transport_interface.h",
    "set_remote_description_observer_interface.h",
    "stats_types.cc",
    "stats_types.h",
    "turn_customizer.h",
    "uma_metrics.h",
    "video_track_source_proxy.h",
  ]
  deps = [
    ":array_view",
    ":audio_options_api",
    ":callfactory_api",
    ":fec_controller_api",
    ":libjingle_logging_api",
    ":network_state_predictor_api",
    ":rtc_stats_api",
    ":scoped_refptr",
    "audio:audio_mixer_api",
    "audio_codecs:audio_codecs_api",
    "rtc_event_log",
    "task_queue",
    "transport:bitrate_settings",
    "transport:network_control",
    "transport/media:audio_interfaces",
    "transport/media:video_interfaces",
    "units:data_rate",
    "units:timestamp",
    "video:encoded_image",
    "video:video_frame",
    "//third_party/abseil-cpp/absl/algorithm:container",
    "//third_party/abseil-cpp/absl/strings",
    "//third_party/abseil-cpp/absl/types:optional",

    # Basically, don't add stuff here. You might break sensitive downstream
    # targets like pnacl. API should not depend on anything outside of this
    # file, really. All these should arguably go away in time.
    "..:webrtc_common",
    "../media:rtc_media_config",
    "../modules/audio_processing:audio_processing_statistics",
    "../rtc_base",
    "../rtc_base:checks",
    "../rtc_base:deprecation",
    "../rtc_base:rtc_base_approved",
    "../rtc_base/system:rtc_export",
  ]
}

rtc_source_set("scoped_refptr") {
  visibility = [ "*" ]
  sources = [
    "scoped_refptr.h",
  ]
}

rtc_source_set("video_quality_test_fixture_api") {
  visibility = [ "*" ]
  testonly = true
  sources = [
    "test/video_quality_test_fixture.h",
  ]
  deps = [
    ":fec_controller_api",
    ":libjingle_peerconnection_api",
    ":network_state_predictor_api",
    ":simulated_network_api",
    "../call:fake_network",
    "../call:rtp_interfaces",
    "../test:test_common",
    "../test:video_test_common",
    "transport:network_control",
    "video_codecs:video_codecs_api",
  ]
}

rtc_source_set("video_quality_analyzer_api") {
  visibility = [ "*" ]
  testonly = true
  sources = [
    "test/video_quality_analyzer_interface.h",
  ]

  deps = [
    ":stats_observer_interface",
    "video:encoded_image",
    "video:video_frame",
    "video_codecs:video_codecs_api",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_source_set("track_id_stream_label_map") {
  visibility = [ "*" ]
  sources = [
    "test/track_id_stream_label_map.h",
  ]
}

rtc_source_set("audio_quality_analyzer_api") {
  visibility = [ "*" ]
  testonly = true
  sources = [
    "test/audio_quality_analyzer_interface.h",
  ]

  deps = [
    ":stats_observer_interface",
    ":track_id_stream_label_map",
  ]
}

rtc_source_set("stats_observer_interface") {
  visibility = [ "*" ]
  testonly = true
  sources = [
    "test/stats_observer_interface.h",
  ]

  deps = [
    ":libjingle_peerconnection_api",
  ]
}

rtc_source_set("peer_connection_quality_test_fixture_api") {
  visibility = [ "*" ]
  testonly = true
  sources = [
    "test/peerconnection_quality_test_fixture.h",
  ]

  deps = [
    ":audio_quality_analyzer_api",
    ":callfactory_api",
    ":fec_controller_api",
    ":function_view",
    ":libjingle_peerconnection_api",
    ":network_state_predictor_api",
    ":simulated_network_api",
    ":video_quality_analyzer_api",
    "../logging:rtc_event_log_api",
    "../media:rtc_media_base",
    "../rtc_base:rtc_base",
    "transport:network_control",
    "units:time_delta",
    "video_codecs:video_codecs_api",
    "//third_party/abseil-cpp/absl/memory",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_source_set("test_dependency_factory") {
  visibility = [ "*" ]
  testonly = true
  sources = [
    "test/test_dependency_factory.cc",
    "test/test_dependency_factory.h",
  ]
  deps = [
    ":video_quality_test_fixture_api",
    "../rtc_base:checks",
    "../rtc_base:thread_checker",
    "//third_party/abseil-cpp/absl/memory",
  ]
}

if (rtc_include_tests) {
  rtc_source_set("create_video_quality_test_fixture_api") {
    visibility = [ "*" ]
    testonly = true
    sources = [
      "test/create_video_quality_test_fixture.cc",
      "test/create_video_quality_test_fixture.h",
    ]
    deps = [
      ":fec_controller_api",
      ":network_state_predictor_api",
      ":scoped_refptr",
      ":video_quality_test_fixture_api",
      "../video:video_quality_test",
      "//third_party/abseil-cpp/absl/memory",
    ]
  }

  rtc_source_set("create_network_emulation_manager") {
    visibility = [ "*" ]
    testonly = true
    sources = [
      "test/create_network_emulation_manager.cc",
      "test/create_network_emulation_manager.h",
    ]
    deps = [
      ":network_emulation_manager_api",
      "../test/scenario/network:emulated_network",
      "//third_party/abseil-cpp/absl/memory",
    ]
  }

  rtc_source_set("create_peerconnection_quality_test_fixture") {
    visibility = [ "*" ]
    testonly = true
    sources = [
      "test/create_peerconnection_quality_test_fixture.cc",
      "test/create_peerconnection_quality_test_fixture.h",
    ]

    deps = [
      ":audio_quality_analyzer_api",
      ":peer_connection_quality_test_fixture_api",
      ":video_quality_analyzer_api",
      "../test/pc/e2e:peerconnection_quality_test",
      "//third_party/abseil-cpp/absl/memory",
    ]
  }
}

rtc_source_set("libjingle_logging_api") {
  visibility = [ "*" ]
  sources = [
    "rtc_event_log_output.h",
  ]
}

rtc_source_set("rtc_event_log_output_file") {
  visibility = [ "*" ]
  sources = [
    "rtc_event_log_output_file.cc",
    "rtc_event_log_output_file.h",
  ]

  deps = [
    ":libjingle_logging_api",
    "../rtc_base:checks",
    "../rtc_base:rtc_base_approved",
    "../rtc_base/system:file_wrapper",
    "rtc_event_log",
  ]
}

rtc_source_set("rtc_stats_api") {
  visibility = [ "*" ]
  cflags = []
  sources = [
    "stats/rtc_stats.h",
    "stats/rtc_stats_collector_callback.h",
    "stats/rtc_stats_report.h",
    "stats/rtcstats_objects.h",
  ]

  deps = [
    ":scoped_refptr",
    "../rtc_base:checks",
    "../rtc_base:rtc_base_approved",
    "../rtc_base/system:rtc_export",
  ]
}

rtc_source_set("audio_options_api") {
  visibility = [ "*" ]
  sources = [
    "audio_options.cc",
    "audio_options.h",
  ]

  deps = [
    ":array_view",
    "../rtc_base:stringutils",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_source_set("transport_api") {
  visibility = [ "*" ]
  sources = [
    "call/transport.cc",
    "call/transport.h",
  ]
}

rtc_source_set("bitrate_allocation") {
  visibility = [ "*" ]
  sources = [
    "call/bitrate_allocation.h",
  ]
  deps = [
    "units:data_rate",
    "units:time_delta",
  ]
}

rtc_source_set("simulated_network_api") {
  visibility = [ "*" ]
  sources = [
    "test/simulated_network.h",
  ]
  deps = [
    "../rtc_base",
    "../rtc_base:criticalsection",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_source_set("network_emulation_manager_api") {
  visibility = [ "*" ]
  sources = [
    "test/network_emulation_manager.h",
  ]
  deps = [
    ":simulated_network_api",
    "../rtc_base",
    "units:data_rate",
    "units:data_size",
    "units:timestamp",
  ]
}

rtc_source_set("fec_controller_api") {
  visibility = [ "*" ]
  sources = [
    "fec_controller.h",
  ]

  deps = [
    "../modules:module_fec_api",
    "video:video_frame_type",
  ]
}

rtc_source_set("network_state_predictor_api") {
  visibility = [ "*" ]
  sources = [
    "network_state_predictor.h",
  ]
}

rtc_source_set("array_view") {
  visibility = [ "*" ]
  sources = [
    "array_view.h",
  ]
  deps = [
    "../rtc_base:checks",
    "../rtc_base:type_traits",
  ]
}

rtc_source_set("refcountedbase") {
  visibility = [ "*" ]
  sources = [
    "ref_counted_base.h",
  ]
  deps = [
    "../rtc_base:rtc_base_approved",
  ]
}

rtc_source_set("ice_transport_factory") {
  visibility = [ "*" ]
  sources = [
    "ice_transport_factory.cc",
    "ice_transport_factory.h",
  ]
  deps = [
    ":libjingle_peerconnection_api",
    ":scoped_refptr",
    "../p2p:rtc_p2p",
    "../rtc_base",
    "../rtc_base/system:rtc_export",
    "//third_party/abseil-cpp/absl/memory",
  ]
}

rtc_source_set("neteq_simulator_api") {
  visibility = [ "*" ]
  sources = [
    "test/neteq_simulator.cc",
    "test/neteq_simulator.h",
  ]
}

rtc_source_set("function_view") {
  visibility = [ "*" ]
  sources = [
    "function_view.h",
  ]
  deps = [
    "../rtc_base:checks",
  ]
}

if (rtc_include_tests) {
  if (rtc_enable_protobuf) {
    rtc_source_set("audioproc_f_api") {
      visibility = [ "*" ]
      testonly = true
      sources = [
        "test/audioproc_float.cc",
        "test/audioproc_float.h",
      ]

      deps = [
        "../modules/audio_processing",
        "../modules/audio_processing:api",
        "../modules/audio_processing:audioproc_f_impl",
      ]
    }

    rtc_source_set("neteq_simulator_factory") {
      visibility = [ "*" ]
      testonly = true
      sources = [
        "test/neteq_simulator_factory.cc",
        "test/neteq_simulator_factory.h",
      ]
      deps = [
        ":neteq_simulator_api",
        "../modules/audio_coding:neteq_test_factory",
        "../rtc_base:checks",
        "../rtc_base:rtc_base_approved",
        "//third_party/abseil-cpp/absl/memory",
        "//third_party/abseil-cpp/absl/strings",
      ]
    }
  }

  rtc_source_set("simulcast_test_fixture_api") {
    visibility = [ "*" ]
    testonly = true
    sources = [
      "test/simulcast_test_fixture.h",
    ]
  }

  rtc_source_set("create_simulcast_test_fixture_api") {
    visibility = [ "*" ]
    testonly = true
    sources = [
      "test/create_simulcast_test_fixture.cc",
      "test/create_simulcast_test_fixture.h",
    ]
    deps = [
      ":simulcast_test_fixture_api",
      "../modules/video_coding:simulcast_test_fixture_impl",
      "../rtc_base:rtc_base_approved",
      "video_codecs:video_codecs_api",
      "//third_party/abseil-cpp/absl/memory",
    ]
  }

  rtc_source_set("videocodec_test_fixture_api") {
    visibility = [ "*" ]
    testonly = true
    sources = [
      "test/videocodec_test_fixture.h",
      "test/videocodec_test_stats.cc",
      "test/videocodec_test_stats.h",
    ]
    deps = [
      "..:webrtc_common",
      "../modules/video_coding:video_codec_interface",
      "../rtc_base:stringutils",
      "video:video_frame_type",
      "video_codecs:video_codecs_api",
    ]
  }

  rtc_source_set("create_videocodec_test_fixture_api") {
    visibility = [ "*" ]
    testonly = true
    sources = [
      "test/create_videocodec_test_fixture.cc",
      "test/create_videocodec_test_fixture.h",
    ]
    deps = [
      ":videocodec_test_fixture_api",
      "../modules/video_coding:video_codecs_test_framework",
      "../modules/video_coding:videocodec_test_impl",
      "../rtc_base:rtc_base_approved",
      "video_codecs:video_codecs_api",
      "//third_party/abseil-cpp/absl/memory",
    ]
  }

  rtc_source_set("mock_audio_mixer") {
    testonly = true
    sources = [
      "test/mock_audio_mixer.h",
    ]

    deps = [
      "../test:test_support",
      "audio:audio_mixer_api",
    ]
  }

  rtc_source_set("mock_frame_encryptor") {
    testonly = true
    sources = [
      "test/mock_frame_encryptor.cc",
      "test/mock_frame_encryptor.h",
    ]
    deps = [
      ":libjingle_peerconnection_api",
      "../test:test_support",
    ]
  }

  rtc_source_set("mock_frame_decryptor") {
    testonly = true
    sources = [
      "test/mock_frame_decryptor.cc",
      "test/mock_frame_decryptor.h",
    ]
    deps = [
      ":libjingle_peerconnection_api",
      "../test:test_support",
    ]
  }

  rtc_source_set("fake_frame_encryptor") {
    testonly = true
    sources = [
      "test/fake_frame_encryptor.cc",
      "test/fake_frame_encryptor.h",
    ]
    deps = [
      ":array_view",
      ":libjingle_peerconnection_api",
      "..:webrtc_common",
      "../rtc_base:checks",
      "../rtc_base:rtc_base_approved",
    ]
  }

  rtc_source_set("fake_frame_decryptor") {
    testonly = true
    sources = [
      "test/fake_frame_decryptor.cc",
      "test/fake_frame_decryptor.h",
    ]
    deps = [
      ":array_view",
      ":libjingle_peerconnection_api",
      "..:webrtc_common",
      "../rtc_base:checks",
      "../rtc_base:rtc_base_approved",
    ]
  }

  rtc_source_set("mock_peerconnectioninterface") {
    testonly = true
    sources = [
      "test/mock_peerconnectioninterface.h",
    ]

    deps = [
      ":libjingle_peerconnection_api",
      "../test:test_support",
    ]
  }

  rtc_source_set("mock_rtp") {
    testonly = true
    sources = [
      "test/mock_rtpreceiver.h",
      "test/mock_rtpsender.h",
    ]

    deps = [
      ":libjingle_peerconnection_api",
      "../test:test_support",
    ]
  }

  rtc_source_set("mock_video_bitrate_allocator") {
    testonly = true
    sources = [
      "test/mock_video_bitrate_allocator.h",
    ]

    deps = [
      "../api/video:video_bitrate_allocator",
      "../test:test_support",
    ]
  }

  rtc_source_set("mock_video_bitrate_allocator_factory") {
    testonly = true
    sources = [
      "test/mock_video_bitrate_allocator_factory.h",
    ]

    deps = [
      "../api/video:video_bitrate_allocator_factory",
      "../test:test_support",
    ]
  }

  rtc_source_set("mock_video_codec_factory") {
    testonly = true
    sources = [
      "test/mock_video_decoder_factory.h",
      "test/mock_video_encoder_factory.h",
    ]

    deps = [
      "../api/video_codecs:video_codecs_api",
      "../test:test_support",
    ]
  }

  rtc_source_set("mock_video_decoder") {
    visibility = [ "*" ]

    testonly = true
    sources = [
      "test/mock_video_decoder.cc",
      "test/mock_video_decoder.h",
    ]

    deps = [
      "../api/video_codecs:video_codecs_api",
      "../test:test_support",
    ]
  }

  rtc_source_set("mock_video_encoder") {
    visibility = [ "*" ]

    testonly = true
    sources = [
      "test/mock_video_encoder.cc",
      "test/mock_video_encoder.h",
    ]

    deps = [
      "../api/video_codecs:video_codecs_api",
      "../test:test_support",
    ]
  }

  rtc_source_set("fake_media_transport") {
    testonly = true

    sources = [
      "test/fake_media_transport.h",
    ]

    deps = [
      ":libjingle_peerconnection_api",
      "../rtc_base:checks",
      "//third_party/abseil-cpp/absl/algorithm:container",
      "//third_party/abseil-cpp/absl/memory",
    ]
  }

  rtc_source_set("loopback_media_transport") {
    testonly = true

    sources = [
      "test/loopback_media_transport.cc",
      "test/loopback_media_transport.h",
    ]

    deps = [
      ":libjingle_peerconnection_api",
      "../rtc_base",
      "../rtc_base:checks",
      "//third_party/abseil-cpp/absl/algorithm:container",
      "//third_party/abseil-cpp/absl/memory",
    ]
  }

  rtc_source_set("rtc_api_unittests") {
    testonly = true

    sources = [
      "array_view_unittest.cc",
      "function_view_unittest.cc",
      "rtc_error_unittest.cc",
      "rtc_event_log_output_file_unittest.cc",
      "rtp_parameters_unittest.cc",
      "test/loopback_media_transport_unittest.cc",
    ]

    deps = [
      ":array_view",
      ":function_view",
      ":libjingle_peerconnection_api",
      ":loopback_media_transport",
      ":rtc_event_log_output_file",
      "../rtc_base:checks",
      "../rtc_base:gunit_helpers",
      "../rtc_base:rtc_base_approved",
      "../test:fileutils",
      "../test:test_support",
      "task_queue:task_queue_default_factory_unittests",
      "units:units_unittests",
      "video:video_unittests",
      "//third_party/abseil-cpp/absl/memory",
    ]
  }

  rtc_source_set("compile_all_headers") {
    testonly = true

    sources = [
      "test/compile_all_headers.cc",
    ]

    deps = [
      ":fake_frame_decryptor",
      ":fake_frame_encryptor",
      ":fake_media_transport",
      ":loopback_media_transport",
      ":mock_audio_mixer",
      ":mock_frame_decryptor",
      ":mock_frame_encryptor",
      ":mock_peerconnectioninterface",
      ":mock_rtp",
      ":mock_video_bitrate_allocator",
      ":mock_video_bitrate_allocator_factory",
      ":mock_video_codec_factory",
      ":mock_video_decoder",
      ":mock_video_encoder",
      ":rtc_api_unittests",
      "units:units_unittests",
    ]
  }
}
