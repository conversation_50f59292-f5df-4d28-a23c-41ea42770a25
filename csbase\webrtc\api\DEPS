# This is supposed to be a complete list of top-level directories,
# excepting only api/ itself.
include_rules = [
  "-audio",
  "-base",
  "-build",
  "-buildtools",
  "-build_overrides",
  "-call",
  "-common_audio",
  "-common_video",
  "-crypto",
  "-data",
  "-examples",
  "-ios",
  "-infra",
  "-logging",
  "-media",
  "-modules",
  "-out",
  "-p2p",
  "-pc",
  "-resources",
  "-rtc_base",
  "-rtc_tools",
  "-sdk",
  "-stats",
  "-style-guide",
  "-system_wrappers",
  "-test",
  "-testing",
  "-third_party",
  "-tools",
  "-tools_webrtc",
  "-video",
  "-external/webrtc/webrtc",  # Android platform build.
  "-libyuv",
  "-common_types.h",
  "-WebRTC",
]

specific_include_rules = {
  # Some internal headers are allowed even in API headers:
  ".*\.h": [
    "+rtc_base/checks.h",
    "+rtc_base/system/rtc_export.h",
    "+rtc_base/units/unit_base.h",
    "+rtc_base/deprecation.h",
  ],

  "array_view\.h": [
    "+rtc_base/type_traits.h",
  ],

  # Needed because AudioEncoderOpus is in the wrong place for
  # backwards compatibilty reasons. See
  # https://bugs.chromium.org/p/webrtc/issues/detail?id=7847
  "audio_encoder_opus\.h": [
    "+modules/audio_coding/codecs/opus/audio_encoder_opus.h",
  ],

  "async_resolver_factory\.h": [
    "+rtc_base/async_resolver_interface.h",
  ],

  "candidate\.h": [
    "+rtc_base/network_constants.h",
    "+rtc_base/socket_address.h",
  ],

  "data_channel_interface\.h": [
    "+rtc_base/copy_on_write_buffer.h",
    "+rtc_base/ref_count.h",
  ],

  "dtls_transport_interface\.h": [
    "+rtc_base/ref_count.h",
    "+rtc_base/ssl_certificate.h",
  ],

  "dtmf_sender_interface\.h": [
    "+rtc_base/ref_count.h",
  ],

  "fec_controller\.h": [
    "+modules/include/module_fec_types.h",
  ],

  "ice_transport_interface\.h": [
    "+rtc_base/ref_count.h",
  ],

  "jsep\.h": [
    "+rtc_base/ref_count.h",
  ],

  "jsep_ice_candidate\.h": [
    "+rtc_base/constructor_magic.h",
  ],

  "jsep_session_description\.h": [
    "+rtc_base/constructor_magic.h",
  ],

  "media_stream_interface\.h": [
    "+modules/audio_processing/include/audio_processing_statistics.h",
    "+rtc_base/ref_count.h",
  ],

  "media_transport_interface\.h": [
    "+rtc_base/copy_on_write_buffer.h",  # As used by datachannelinterface.h
    "+rtc_base/network_route.h",
  ],

  "peer_connection_factory_proxy\.h": [
    "+rtc_base/bind.h",
  ],

  "peer_connection_interface\.h": [
    "+media/base/media_config.h",
    "+media/base/media_engine.h",
    "+p2p/base/port_allocator.h",
    "+rtc_base/bitrate_allocation_strategy.h",
    "+rtc_base/network.h",
    "+rtc_base/platform_file.h",
    "+rtc_base/rtc_certificate.h",
    "+rtc_base/rtc_certificate_generator.h",
    "+rtc_base/socket_address.h",
    "+rtc_base/ssl_certificate.h",
    "+rtc_base/ssl_stream_adapter.h",
  ],

  "proxy\.h": [
    "+rtc_base/event.h",
    "+rtc_base/message_handler.h",  # Inherits from it.
    "+rtc_base/message_queue.h",    # Inherits from MessageData.
    "+rtc_base/ref_counted_object.h",
    "+rtc_base/thread.h",
  ],

  "ref_counted_base\.h": [
    "+rtc_base/constructor_magic.h",
    "+rtc_base/ref_count.h",
    "+rtc_base/ref_counter.h",
  ],

  "rtc_error\.h": [
    "+rtc_base/logging.h",
  ],
  "rtc_event_log_output_file.h": [
    # TODO(bugs.webrtc.org/6463): Delete this dependency.
    "+rtc_base/platform_file.h",
    # For private member and constructor.
    "+rtc_base/system/file_wrapper.h",
  ],
  "rtp_receiver_interface\.h": [
    "+rtc_base/ref_count.h",
  ],

  "rtp_sender_interface\.h": [
    "+rtc_base/ref_count.h",
  ],

  "rtp_transceiver_interface\.h": [
    "+rtc_base/ref_count.h",
  ],

  "sctp_transport_interface\.h": [
    "+rtc_base/ref_count.h",
  ],

  "set_remote_description_observer_interface\.h": [
    "+rtc_base/ref_count.h",
  ],

  "stats_types\.h": [
    "+rtc_base/constructor_magic.h",
    "+rtc_base/ref_count.h",
    "+rtc_base/string_encode.h",
    "+rtc_base/thread_checker.h",
  ],

  "uma_metrics\.h": [
    "+rtc_base/ref_count.h",
  ],

  "audio_frame\.h": [
    "+rtc_base/constructor_magic.h",
  ],

  "audio_mixer\.h": [
    "+rtc_base/ref_count.h",
  ],

  "audio_decoder\.h": [
    "+rtc_base/buffer.h",
    "+rtc_base/constructor_magic.h",
  ],

  "audio_decoder_factory\.h": [
    "+rtc_base/ref_count.h",
  ],

  "audio_decoder_factory_template\.h": [
    "+rtc_base/ref_counted_object.h",
  ],

  "audio_encoder\.h": [
    "+rtc_base/buffer.h",
  ],

  "audio_encoder_factory\.h": [
    "+rtc_base/ref_count.h",
  ],

  "audio_encoder_factory_template\.h": [
    "+rtc_base/ref_counted_object.h",
  ],

  "frame_decryptor_interface\.h": [
    "+rtc_base/ref_count.h",
  ],

  "frame_encryptor_interface\.h": [
    "+rtc_base/ref_count.h",
  ],

  "rtc_stats_collector_callback\.h": [
    "+rtc_base/ref_count.h",
  ],

  "rtc_stats_report\.h": [
    "+rtc_base/ref_count.h",
    "+rtc_base/ref_counted_object.h",
  ],

  "audioproc_float\.h": [
    "+modules/audio_processing/include/audio_processing.h",
  ],

  "fake_frame_decryptor\.h": [
    "+rtc_base/ref_counted_object.h",
  ],

  "fake_frame_encryptor\.h": [
    "+rtc_base/ref_counted_object.h",
  ],

  "mock.*\.h": [
    "+test/gmock.h",
  ],

  "simulated_network\.h": [
    "+rtc_base/critical_section.h",
    "+rtc_base/random.h",
    "+rtc_base/thread_annotations.h",
  ],

  "test_dependency_factory\.h": [
    "+rtc_base/thread_checker.h",
  ],

  "videocodec_test_fixture\.h": [
    "+modules/video_coding/include/video_codec_interface.h"
  ],

  "video_encoder_config\.h": [
    "+rtc_base/ref_count.h",
  ],

  # .cc files in api/ should not be restricted in what they can #include,
  # so we re-add all the top-level directories here. (That's because .h
  # files leak their #includes to whoever's #including them, but .cc files
  # do not since no one #includes them.)
  ".*\.cc": [
    "+audio",
    "+call",
    "+common_audio",
    "+common_video",
    "+examples",
    "+logging",
    "+media",
    "+modules",
    "+p2p",
    "+pc",
    "+rtc_base",
    "+rtc_tools",
    "+sdk",
    "+stats",
    "+system_wrappers",
    "+test",
    "+tools",
    "+tools_webrtc",
    "+video",
    "+third_party",
  ],
}
