# Copyright (c) 2018 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("../../webrtc.gni")

rtc_source_set("audio_frame_api") {
  visibility = [ "*" ]
  sources = [
    "audio_frame.cc",
    "audio_frame.h",
  ]

  deps = [
    "../../rtc_base:checks",
    "../../rtc_base:rtc_base_approved",
  ]
}

rtc_source_set("audio_mixer_api") {
  visibility = [ "*" ]
  sources = [
    "audio_mixer.h",
  ]

  deps = [
    ":audio_frame_api",
    "../../rtc_base:rtc_base_approved",
  ]
}

rtc_source_set("aec3_config") {
  visibility = [ "*" ]
  sources = [
    "echo_canceller3_config.cc",
    "echo_canceller3_config.h",
  ]
  deps = [
    "../../rtc_base:checks",
    "../../rtc_base:rtc_base_approved",
    "../../rtc_base:safe_minmax",
    "../../rtc_base/system:rtc_export",
  ]
}

rtc_source_set("aec3_config_json") {
  visibility = [ "*" ]
  sources = [
    "echo_canceller3_config_json.cc",
    "echo_canceller3_config_json.h",
  ]
  deps = [
    ":aec3_config",
    "../../rtc_base:checks",
    "../../rtc_base:rtc_base_approved",
    "../../rtc_base:rtc_json",
    "../../rtc_base/system:rtc_export",
    "//third_party/abseil-cpp/absl/strings",
  ]
}

rtc_source_set("aec3_factory") {
  visibility = [ "*" ]
  configs += [ "../../modules/audio_processing:apm_debug_dump" ]
  sources = [
    "echo_canceller3_factory.cc",
    "echo_canceller3_factory.h",
  ]

  deps = [
    ":aec3_config",
    ":echo_control",
    "../../modules/audio_processing/aec3",
    "../../rtc_base:rtc_base_approved",
    "../../rtc_base/system:rtc_export",
    "//third_party/abseil-cpp/absl/memory",
  ]
}

rtc_source_set("echo_control") {
  visibility = [ "*" ]
  sources = [
    "echo_control.h",
  ]
}
