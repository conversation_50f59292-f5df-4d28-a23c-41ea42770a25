# Copyright (c) 2017 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("../../../webrtc.gni")
if (is_android) {
  import("//build/config/android/config.gni")
  import("//build/config/android/rules.gni")
}

rtc_static_library("audio_encoder_L16") {
  visibility = [ "*" ]
  poisonous = [ "audio_codecs" ]
  sources = [
    "audio_encoder_L16.cc",
    "audio_encoder_L16.h",
  ]
  deps = [
    "..:audio_codecs_api",
    "../../../modules/audio_coding:pcm16b",
    "../../../rtc_base:rtc_base_approved",
    "../../../rtc_base:safe_minmax",
    "../../../rtc_base/system:rtc_export",
    "//third_party/abseil-cpp/absl/memory",
    "//third_party/abseil-cpp/absl/strings",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_static_library("audio_decoder_L16") {
  visibility = [ "*" ]
  poisonous = [ "audio_codecs" ]
  sources = [
    "audio_decoder_L16.cc",
    "audio_decoder_L16.h",
  ]
  deps = [
    "..:audio_codecs_api",
    "../../../modules/audio_coding:pcm16b",
    "../../../rtc_base:rtc_base_approved",
    "../../../rtc_base/system:rtc_export",
    "//third_party/abseil-cpp/absl/memory",
    "//third_party/abseil-cpp/absl/strings",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}
