# Copyright (c) 2017 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("../../../webrtc.gni")
if (is_android) {
  import("//build/config/android/config.gni")
  import("//build/config/android/rules.gni")
}

rtc_source_set("audio_encoder_ilbc_config") {
  visibility = [ "*" ]
  sources = [
    "audio_encoder_ilbc_config.h",
  ]
}

rtc_static_library("audio_encoder_ilbc") {
  visibility = [ "*" ]
  poisonous = [ "audio_codecs" ]
  sources = [
    "audio_encoder_ilbc.cc",
    "audio_encoder_ilbc.h",
  ]
  deps = [
    ":audio_encoder_ilbc_config",
    "..:audio_codecs_api",
    "../../../modules/audio_coding:ilbc",
    "../../../rtc_base:rtc_base_approved",
    "../../../rtc_base:safe_minmax",
    "//third_party/abseil-cpp/absl/memory",
    "//third_party/abseil-cpp/absl/strings",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_static_library("audio_decoder_ilbc") {
  visibility = [ "*" ]
  poisonous = [ "audio_codecs" ]
  sources = [
    "audio_decoder_ilbc.cc",
    "audio_decoder_ilbc.h",
  ]
  deps = [
    "..:audio_codecs_api",
    "../../../modules/audio_coding:ilbc",
    "../../../rtc_base:rtc_base_approved",
    "//third_party/abseil-cpp/absl/memory",
    "//third_party/abseil-cpp/absl/strings",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}
