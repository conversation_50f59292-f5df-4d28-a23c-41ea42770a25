# Copyright (c) 2017 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("../../../webrtc.gni")
if (is_android) {
  import("//build/config/android/config.gni")
  import("//build/config/android/rules.gni")
}

# The targets with _fix and _float suffixes unconditionally use the
# fixed-point and floating-point iSAC implementations, respectively.
# The targets without suffixes pick one of the implementations based
# on cleverly chosen criteria.

rtc_source_set("audio_encoder_isac") {
  visibility = [ "*" ]
  poisonous = [ "audio_codecs" ]
  public = [
    "audio_encoder_isac.h",
  ]
  public_configs = [ ":isac_config" ]
  if (current_cpu == "arm") {
    deps = [
      ":audio_encoder_isac_fix",
    ]
  } else {
    deps = [
      ":audio_encoder_isac_float",
    ]
  }
}

rtc_source_set("audio_decoder_isac") {
  visibility = [ "*" ]
  poisonous = [ "audio_codecs" ]
  public = [
    "audio_decoder_isac.h",
  ]
  public_configs = [ ":isac_config" ]
  if (current_cpu == "arm") {
    deps = [
      ":audio_decoder_isac_fix",
    ]
  } else {
    deps = [
      ":audio_decoder_isac_float",
    ]
  }
}

config("isac_config") {
  visibility = [ ":*" ]
  if (current_cpu == "arm") {
    defines = [
      "WEBRTC_USE_BUILTIN_ISAC_FIX=1",
      "WEBRTC_USE_BUILTIN_ISAC_FLOAT=0",
    ]
  } else {
    defines = [
      "WEBRTC_USE_BUILTIN_ISAC_FIX=0",
      "WEBRTC_USE_BUILTIN_ISAC_FLOAT=1",
    ]
  }
}

rtc_static_library("audio_encoder_isac_fix") {
  visibility = [ "*" ]
  poisonous = [ "audio_codecs" ]
  sources = [
    "audio_encoder_isac_fix.cc",
    "audio_encoder_isac_fix.h",
  ]
  deps = [
    "..:audio_codecs_api",
    "../../../modules/audio_coding:isac_fix",
    "../../../rtc_base:rtc_base_approved",
    "//third_party/abseil-cpp/absl/memory",
    "//third_party/abseil-cpp/absl/strings",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_static_library("audio_decoder_isac_fix") {
  visibility = [ "*" ]
  poisonous = [ "audio_codecs" ]
  sources = [
    "audio_decoder_isac_fix.cc",
    "audio_decoder_isac_fix.h",
  ]
  deps = [
    "..:audio_codecs_api",
    "../../../modules/audio_coding:isac_fix",
    "../../../rtc_base:rtc_base_approved",
    "//third_party/abseil-cpp/absl/memory",
    "//third_party/abseil-cpp/absl/strings",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_static_library("audio_encoder_isac_float") {
  visibility = [ "*" ]
  poisonous = [ "audio_codecs" ]
  sources = [
    "audio_encoder_isac_float.cc",
    "audio_encoder_isac_float.h",
  ]
  deps = [
    "..:audio_codecs_api",
    "../../../modules/audio_coding:isac",
    "../../../rtc_base:rtc_base_approved",
    "../../../rtc_base/system:rtc_export",
    "//third_party/abseil-cpp/absl/memory",
    "//third_party/abseil-cpp/absl/strings",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_static_library("audio_decoder_isac_float") {
  visibility = [ "*" ]
  poisonous = [ "audio_codecs" ]
  sources = [
    "audio_decoder_isac_float.cc",
    "audio_decoder_isac_float.h",
  ]
  deps = [
    "..:audio_codecs_api",
    "../../../modules/audio_coding:isac",
    "../../../rtc_base:rtc_base_approved",
    "../../../rtc_base/system:rtc_export",
    "//third_party/abseil-cpp/absl/memory",
    "//third_party/abseil-cpp/absl/strings",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}
