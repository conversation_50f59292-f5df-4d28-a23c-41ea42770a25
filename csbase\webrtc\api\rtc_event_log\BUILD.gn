# Copyright (c) 2019 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("../../webrtc.gni")

rtc_source_set("rtc_event_log") {
  visibility = [ "*" ]
  sources = [
    "rtc_event.cc",
    "rtc_event.h",
    "rtc_event_log.cc",
    "rtc_event_log.h",
    "rtc_event_log_factory_interface.h",
  ]

  deps = [
    "..:libjingle_logging_api",
    "../../rtc_base:checks",
    "../../rtc_base:timeutils",
    "../task_queue",
  ]
}

rtc_source_set("rtc_event_log_factory") {
  visibility = [ "*" ]
  sources = [
    "rtc_event_log_factory.cc",
    "rtc_event_log_factory.h",
  ]

  deps = [
    ":rtc_event_log",
    "../../rtc_base:checks",
    "../task_queue",
    "//third_party/abseil-cpp/absl/memory",
  ]

  if (rtc_enable_protobuf) {
    defines = [ "ENABLE_RTC_EVENT_LOG" ]
    deps += [ "../../logging:rtc_event_log_impl" ]
  }
}
