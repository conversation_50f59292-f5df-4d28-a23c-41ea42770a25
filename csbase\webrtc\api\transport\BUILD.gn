# Copyright (c) 2018 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("../../webrtc.gni")

rtc_source_set("bitrate_settings") {
  visibility = [ "*" ]
  sources = [
    "bitrate_settings.cc",
    "bitrate_settings.h",
  ]
  deps = [
    "../../rtc_base/system:rtc_export",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_source_set("enums") {
  visibility = [ "*" ]
  sources = [
    "enums.h",
  ]
}

rtc_static_library("network_control") {
  visibility = [ "*" ]
  sources = [
    "network_control.h",
    "network_types.cc",
    "network_types.h",
  ]

  deps = [
    ":webrtc_key_value_config",
    "../../rtc_base:deprecation",
    "../units:data_rate",
    "../units:data_size",
    "../units:time_delta",
    "../units:timestamp",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_source_set("webrtc_key_value_config") {
  visibility = [ "*" ]
  sources = [
    "webrtc_key_value_config.h",
  ]
  deps = [
    "//third_party/abseil-cpp/absl/strings",
  ]
}

rtc_source_set("field_trial_based_config") {
  visibility = [ "*" ]
  sources = [
    "field_trial_based_config.cc",
    "field_trial_based_config.h",
  ]
  deps = [
    ":webrtc_key_value_config",
    "../../system_wrappers:field_trial",
    "//third_party/abseil-cpp/absl/strings",
  ]
}

rtc_static_library("goog_cc") {
  visibility = [ "*" ]
  sources = [
    "goog_cc_factory.cc",
    "goog_cc_factory.h",
  ]
  deps = [
    ":network_control",
    ":webrtc_key_value_config",
    "..:network_state_predictor_api",
    "../../modules/congestion_controller/goog_cc",
    "//third_party/abseil-cpp/absl/memory",
  ]
}

if (rtc_include_tests) {
  rtc_source_set("test_feedback_generator_interface") {
    testonly = true
    sources = [
      "test/feedback_generator_interface.h",
    ]
    deps = [
      ":network_control",
      "..:simulated_network_api",
    ]
  }
  rtc_source_set("test_feedback_generator") {
    testonly = true
    sources = [
      "test/create_feedback_generator.cc",
      "test/create_feedback_generator.h",
    ]
    visibility = [ "*" ]
    deps = [
      ":network_control",
      ":test_feedback_generator_interface",
      "../../test/scenario/network:feedback_generator",
      "//third_party/abseil-cpp/absl/memory",
    ]
  }
  rtc_source_set("network_control_test") {
    testonly = true
    sources = [
      "test/mock_network_control.h",
    ]
    deps = [
      ":network_control",
      "../../rtc_base:checks",
      "../../rtc_base:rtc_base_approved",
      "../../test:test_support",
      "//third_party/abseil-cpp/absl/types:optional",
    ]
  }
}
