specific_include_rules = {
  # Until the new VideoStreamDecoder is implemented the current decoding
  # pipeline will be used, and therefore EncodedFrame needs to inherit
  # VCMEncodedFrame.
  "encoded_frame.h": [
    "+modules/video_coding/encoded_frame.h",
  ],

  # Used for a private member variable.
  "encoded_image\.h": [
    "+rtc_base/copy_on_write_buffer.h",
  ],

  "i010_buffer\.h": [
    "+rtc_base/memory/aligned_malloc.h",
  ],

  "i420_buffer\.h": [
    "+rtc_base/memory/aligned_malloc.h",
  ],

  "video_frame\.h": [
  ],

  "video_frame_buffer\.h": [
    "+rtc_base/ref_count.h",
  ],

  "video_stream_decoder_create.cc": [
    "+video/video_stream_decoder_impl.h",
  ],

  "video_stream_encoder_create.cc": [
    "+video/video_stream_encoder.h",
  ],
}
