# Copyright (c) 2017 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("../../webrtc.gni")
if (is_android) {
  import("//build/config/android/config.gni")
  import("//build/config/android/rules.gni")
}

rtc_source_set("video_codecs_api") {
  visibility = [ "*" ]
  sources = [
    "sdp_video_format.cc",
    "sdp_video_format.h",
    "video_codec.cc",
    "video_codec.h",
    "video_decoder.cc",
    "video_decoder.h",
    "video_decoder_factory.cc",
    "video_decoder_factory.h",
    "video_encoder.cc",
    "video_encoder.h",
    "video_encoder_config.cc",
    "video_encoder_config.h",
    "video_encoder_factory.h",
    "vp8_frame_buffer_controller.h",
    "vp8_frame_config.cc",
    "vp8_frame_config.h",
    "vp8_temporal_layers.cc",
    "vp8_temporal_layers.h",
  ]

  deps = [
    "..:scoped_refptr",
    "../..:webrtc_common",
    "../../modules/video_coding:codec_globals_headers",
    "../../rtc_base:checks",
    "../../rtc_base:rtc_base_approved",
    "../../rtc_base/system:rtc_export",
    "../units:data_rate",
    "../video:encoded_image",
    "../video:video_bitrate_allocation",
    "../video:video_codec_constants",
    "../video:video_frame",
    "//third_party/abseil-cpp/absl/algorithm:container",
    "//third_party/abseil-cpp/absl/container:inlined_vector",
    "//third_party/abseil-cpp/absl/strings",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_source_set("bitstream_parser_api") {
  visibility = [ "*" ]
  sources = [
    "bitstream_parser.h",
  ]
  deps = [
    "..:array_view",
  ]
}

rtc_static_library("builtin_video_decoder_factory") {
  visibility = [ "*" ]
  allow_poison = [
    "audio_codecs",  # TODO(bugs.webrtc.org/8396): Remove.
    "software_video_codecs",
  ]
  sources = [
    "builtin_video_decoder_factory.cc",
    "builtin_video_decoder_factory.h",
  ]

  deps = [
    ":video_codecs_api",
    "../../api:scoped_refptr",
    "../../media:rtc_internal_video_codecs",
    "../../rtc_base/system:rtc_export",
    "//third_party/abseil-cpp/absl/memory",
  ]
}

rtc_static_library("builtin_video_encoder_factory") {
  visibility = [ "*" ]
  allow_poison = [
    "audio_codecs",  # TODO(bugs.webrtc.org/8396): Remove.
    "software_video_codecs",
  ]
  sources = [
    "builtin_video_encoder_factory.cc",
    "builtin_video_encoder_factory.h",
  ]

  deps = [
    ":video_codecs_api",
    "../../api:scoped_refptr",
    "../../media:rtc_encoder_simulcast_proxy",
    "../../media:rtc_internal_video_codecs",
    "../../media:rtc_media_base",
    "../../rtc_base:checks",
    "../../rtc_base/system:rtc_export",
    "//third_party/abseil-cpp/absl/memory",
    "//third_party/abseil-cpp/absl/strings",
  ]
}

rtc_static_library("vp8_temporal_layers_factory") {
  visibility = [ "*" ]
  allow_poison = [ "software_video_codecs" ]
  sources = [
    "vp8_temporal_layers_factory.cc",
    "vp8_temporal_layers_factory.h",
  ]

  deps = [
    ":video_codecs_api",
    "../../modules/video_coding:video_coding_utility",
    "../../modules/video_coding:webrtc_vp8_temporal_layers",
    "../../rtc_base:checks",
    "//third_party/abseil-cpp/absl/memory",
  ]
}

rtc_static_library("rtc_software_fallback_wrappers") {
  visibility = [ "*" ]

  sources = [
    "video_decoder_software_fallback_wrapper.cc",
    "video_decoder_software_fallback_wrapper.h",
    "video_encoder_software_fallback_wrapper.cc",
    "video_encoder_software_fallback_wrapper.h",
  ]

  deps = [
    ":video_codecs_api",
    "../../media:rtc_h264_profile_id",
    "../../media:rtc_media_base",
    "../../modules/video_coding:video_codec_interface",
    "../../rtc_base:checks",
    "../../rtc_base:rtc_base_approved",
    "../../rtc_base/system:fallthrough",
    "../../rtc_base/system:rtc_export",
    "../../system_wrappers:field_trial",
    "../video:encoded_image",
    "../video:video_bitrate_allocation",
    "../video:video_frame",
    "//third_party/abseil-cpp/absl/memory",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}
