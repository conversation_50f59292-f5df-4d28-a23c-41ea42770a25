include_rules = [
  "+audio",
  "+logging/rtc_event_log",
  "+modules/audio_coding",
  "+modules/audio_device",
  "+modules/audio_mixer",
  "+modules/audio_processing",
  "+modules/bitrate_controller",
  "+modules/congestion_controller",
  "+modules/video_coding",
  "+modules/pacing",
  "+modules/rtp_rtcp",
  "+modules/utility",
  "+system_wrappers",
  "+video",
]

specific_include_rules = {
  "video_receive_stream\.h": [
    "+common_video/include",
    "+media/base",
  ],
  "video_send_stream\.h": [
    "+common_video/include",
    "+media/base",
  ],
}
