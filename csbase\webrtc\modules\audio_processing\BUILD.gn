# Copyright (c) 2014 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("../../webrtc.gni")
if (rtc_enable_protobuf) {
  import("//third_party/protobuf/proto_library.gni")
}

declare_args() {
  # Disables the usual mode where we trust the reported system delay
  # values the AEC receives. The corresponding define is set appropriately
  # in the code, but it can be force-enabled here for testing.
  aec_untrusted_delay_for_testing = false
}

config("apm_debug_dump") {
  if (apm_debug_dump) {
    defines = [ "WEBRTC_APM_DEBUG_DUMP=1" ]
  } else {
    defines = [ "WEBRTC_APM_DEBUG_DUMP=0" ]
  }
}

rtc_static_library("config") {
  visibility = [ ":*" ]
  sources = [
    "include/config.cc",
    "include/config.h",
  ]
  deps = [
    "../../rtc_base:macromagic",
    "../../rtc_base/system:rtc_export",
  ]
}

rtc_source_set("api") {
  visibility = [ "*" ]
  sources = [
    "include/audio_processing.cc",
    "include/audio_processing.h",
  ]
  deps = [
    ":audio_frame_view",
    ":audio_generator_interface",
    ":audio_processing_statistics",
    ":config",
    ":gain_control_interface",
    "../../api:scoped_refptr",
    "../../api/audio:aec3_config",
    "../../api/audio:echo_control",
    "../../rtc_base:deprecation",
    "../../rtc_base:macromagic",
    "../../rtc_base:rtc_base_approved",
    "../../rtc_base/system:rtc_export",
    "//third_party/abseil-cpp/absl/memory",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_static_library("audio_buffer") {
  visibility = [ "*" ]

  configs += [ ":apm_debug_dump" ]

  sources = [
    "audio_buffer.cc",
    "audio_buffer.h",
    "splitting_filter.cc",
    "splitting_filter.h",
    "three_band_filter_bank.cc",
    "three_band_filter_bank.h",
  ]

  defines = []

  deps = [
    ":api",
    "../../api/audio:audio_frame_api",
    "../../common_audio",
    "../../common_audio:common_audio_c",
    "../../rtc_base:checks",
  ]
}

rtc_static_library("audio_processing") {
  visibility = [ "*" ]
  configs += [ ":apm_debug_dump" ]
  sources = [
    "audio_processing_impl.cc",
    "audio_processing_impl.h",
    "common.h",
    "echo_cancellation_impl.cc",
    "echo_cancellation_impl.h",
    "echo_control_mobile_impl.cc",
    "echo_control_mobile_impl.h",
    "echo_detector/circular_buffer.cc",
    "echo_detector/circular_buffer.h",
    "echo_detector/mean_variance_estimator.cc",
    "echo_detector/mean_variance_estimator.h",
    "echo_detector/moving_max.cc",
    "echo_detector/moving_max.h",
    "echo_detector/normalized_covariance_estimator.cc",
    "echo_detector/normalized_covariance_estimator.h",
    "gain_control_for_experimental_agc.cc",
    "gain_control_for_experimental_agc.h",
    "gain_control_impl.cc",
    "gain_control_impl.h",
    "gain_controller2.cc",
    "gain_controller2.h",
    "include/aec_dump.cc",
    "include/aec_dump.h",
    "level_estimator_impl.cc",
    "level_estimator_impl.h",
    "low_cut_filter.cc",
    "low_cut_filter.h",
    "noise_suppression_impl.cc",
    "noise_suppression_impl.h",
    "render_queue_item_verifier.h",
    "residual_echo_detector.cc",
    "residual_echo_detector.h",
    "rms_level.cc",
    "rms_level.h",
    "transient/common.h",
    "transient/daubechies_8_wavelet_coeffs.h",
    "transient/dyadic_decimator.h",
    "transient/moving_moments.cc",
    "transient/moving_moments.h",
    "transient/transient_detector.cc",
    "transient/transient_detector.h",
    "transient/transient_suppressor.cc",
    "transient/transient_suppressor.h",
    "transient/wpd_node.cc",
    "transient/wpd_node.h",
    "transient/wpd_tree.cc",
    "transient/wpd_tree.h",
    "typing_detection.cc",
    "typing_detection.h",
    "voice_detection_impl.cc",
    "voice_detection_impl.h",
  ]

  defines = []
  deps = [
    ":api",
    ":apm_logging",
    ":audio_buffer",
    ":audio_frame_view",
    ":audio_generator_interface",
    ":audio_processing_c",
    ":audio_processing_statistics",
    ":config",
    ":gain_control_config_proxy",
    ":gain_control_interface",
    ":noise_suppression_proxy",
    "../../api:array_view",
    "../../api:function_view",
    "../../api/audio:aec3_config",
    "../../api/audio:audio_frame_api",
    "../../api/audio:echo_control",
    "../../audio/utility:audio_frame_operations",
    "../../common_audio:common_audio_c",
    "../../common_audio/third_party/fft4g",
    "../../rtc_base:checks",
    "../../rtc_base:deprecation",
    "../../rtc_base:gtest_prod",
    "../../rtc_base:safe_minmax",
    "../../rtc_base:sanitizer",
    "../../rtc_base/system:arch",
    "../../rtc_base/system:rtc_export",
    "../../system_wrappers:cpu_features_api",
    "../../system_wrappers:field_trial",
    "../../system_wrappers:metrics",
    "aec",
    "aec:aec_core",
    "aec3",
    "aecm:aecm_core",
    "agc",
    "agc:agc_legacy_c",
    "agc2:adaptive_digital",
    "agc2:fixed_digital",
    "agc2:gain_applier",
    "vad",
    "//third_party/abseil-cpp/absl/memory",
    "//third_party/abseil-cpp/absl/types:optional",
  ]

  if (aec_untrusted_delay_for_testing) {
    defines += [ "WEBRTC_UNTRUSTED_DELAY" ]
  }

  if (rtc_prefer_fixed_point) {
    defines += [ "WEBRTC_NS_FIXED" ]
  } else {
    defines += [ "WEBRTC_NS_FLOAT" ]
  }

  deps += [
    "../../common_audio",
    "../../common_audio:fir_filter",
    "../../common_audio:fir_filter_factory",
    "../../rtc_base:rtc_base_approved",
    "../../system_wrappers",
  ]
}

rtc_source_set("gain_control_interface") {
  sources = [
    "include/gain_control.h",
  ]
}

rtc_source_set("gain_control_config_proxy") {
  sources = [
    "gain_control_config_proxy.cc",
    "gain_control_config_proxy.h",
  ]
  deps = [
    ":api",
    ":gain_control_interface",
    "../../rtc_base:criticalsection",
    "../../rtc_base:macromagic",
  ]
}

rtc_source_set("noise_suppression_proxy") {
  sources = [
    "noise_suppression_proxy.cc",
    "noise_suppression_proxy.h",
  ]
  deps = [
    ":api",
    "../../rtc_base:macromagic",
  ]
}

rtc_source_set("audio_processing_statistics") {
  visibility = [ "*" ]
  sources = [
    "include/audio_processing_statistics.cc",
    "include/audio_processing_statistics.h",
  ]
  deps = [
    "../../rtc_base/system:rtc_export",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_source_set("audio_frame_view") {
  sources = [
    "include/audio_frame_view.h",
  ]
  deps = [
    "../../api:array_view",
  ]
}

rtc_source_set("audio_generator_interface") {
  visibility = [ "*" ]
  sources = [
    "include/audio_generator.h",
  ]
  deps = [
    ":audio_frame_view",
    "../../rtc_base:rtc_base_approved",
    "../../system_wrappers",
  ]
}

rtc_source_set("audio_generator_factory") {
  visibility = [ "*" ]
  sources = [
    "include/audio_generator_factory.cc",
    "include/audio_generator_factory.h",
  ]
  deps = [
    ":audio_generator_interface",
    ":file_audio_generator",
    "../../common_audio",
    "../../rtc_base:rtc_base_approved",
    "../../system_wrappers",
    "//third_party/abseil-cpp/absl/memory",
  ]
}

rtc_source_set("file_audio_generator") {
  sources = [
    "audio_generator/file_audio_generator.cc",
    "audio_generator/file_audio_generator.h",
  ]
  deps = [
    ":audio_generator_interface",
    "../../common_audio",
    "../../rtc_base:rtc_base_approved",
    "../../system_wrappers",
  ]
}

rtc_source_set("audio_processing_c") {
  visibility = [ ":*" ]  # Only targets in this file can depend on this.
  sources = []

  if (rtc_prefer_fixed_point) {
    sources += [
      "ns/noise_suppression_x.c",
      "ns/noise_suppression_x.h",
      "ns/nsx_core.c",
      "ns/nsx_core.h",
      "ns/nsx_defines.h",
    ]
    if (current_cpu == "mipsel") {
      sources += [ "ns/nsx_core_mips.c" ]
    } else {
      sources += [ "ns/nsx_core_c.c" ]
    }
  } else {
    sources += [
      "ns/defines.h",
      "ns/noise_suppression.c",
      "ns/noise_suppression.h",
      "ns/ns_core.c",
      "ns/ns_core.h",
      "ns/windows_private.h",
    ]
  }

  deps = [
    "../../common_audio",
    "../../common_audio:common_audio_c",
    "../../common_audio/third_party/fft4g",
    "../../rtc_base:checks",
    "../../rtc_base:rtc_base_approved",
    "../../system_wrappers:cpu_features_api",
    "agc:agc_legacy_c",
  ]

  if (rtc_build_with_neon) {
    sources += [ "ns/nsx_core_neon.c" ]

    if (current_cpu != "arm64") {
      # Enable compilation for the NEON instruction set.
      suppressed_configs += [ "//build/config/compiler:compiler_arm_fpu" ]
      cflags = [ "-mfpu=neon" ]
    }

    # Disable LTO on NEON targets due to compiler bug.
    # TODO(fdegans): Enable this. See crbug.com/408997.
    if (rtc_use_lto) {
      cflags -= [
        "-flto",
        "-ffat-lto-objects",
      ]
    }
  }
}

if (rtc_enable_protobuf) {
  proto_library("audioproc_debug_proto") {
    sources = [
      "debug.proto",
    ]

    proto_out_dir = "modules/audio_processing"
  }
}

rtc_source_set("apm_logging") {
  configs += [ ":apm_debug_dump" ]
  sources = [
    "logging/apm_data_dumper.cc",
    "logging/apm_data_dumper.h",
  ]
  deps = [
    "../../api:array_view",
    "../../common_audio",
    "../../rtc_base:checks",
    "../../rtc_base:rtc_base_approved",
  ]
  defines = []
}

if (rtc_include_tests) {
  rtc_source_set("mocks") {
    testonly = true
    sources = [
      "include/mock_audio_processing.h",
    ]
    deps = [
      ":api",
      ":audio_processing",
      ":audio_processing_statistics",
      "../../test:test_support",
    ]
  }

  group("audio_processing_tests") {
    testonly = true
    deps = [
      ":audioproc_test_utils",
      ":click_annotate",
      ":transient_suppression_test",
    ]

    if (rtc_enable_protobuf) {
      deps += [
        ":audioproc_f",
        ":audioproc_unittest_proto",
        "aec_dump:aec_dump_unittests",
        "test/conversational_speech",
        "test/py_quality_assessment",
      ]
    }
  }

  rtc_source_set("audio_processing_unittests") {
    testonly = true

    configs += [ ":apm_debug_dump" ]
    sources = [
      "audio_buffer_unittest.cc",
      "audio_frame_view_unittest.cc",
      "config_unittest.cc",
      "echo_cancellation_impl_unittest.cc",
      "echo_control_mobile_unittest.cc",
      "gain_control_config_proxy_unittest.cc",
      "gain_controller2_unittest.cc",
      "splitting_filter_unittest.cc",
      "test/fake_recording_device_unittest.cc",
      "transient/dyadic_decimator_unittest.cc",
      "transient/file_utils.cc",
      "transient/file_utils.h",
      "transient/file_utils_unittest.cc",
      "transient/moving_moments_unittest.cc",
      "transient/transient_detector_unittest.cc",
      "transient/transient_suppressor_unittest.cc",
      "transient/wpd_node_unittest.cc",
      "transient/wpd_tree_unittest.cc",
    ]

    deps = [
      ":analog_mic_simulation",
      ":api",
      ":apm_logging",
      ":audio_buffer",
      ":audio_frame_view",
      ":audio_processing",
      ":audioproc_test_utils",
      ":config",
      ":file_audio_generator_unittests",
      ":gain_control_config_proxy",
      ":mocks",
      "../../api:array_view",
      "../../api:scoped_refptr",
      "../../api/audio:aec3_config",
      "../../api/audio:aec3_factory",
      "../../common_audio",
      "../../common_audio:common_audio_c",
      "../../rtc_base",
      "../../rtc_base:checks",
      "../../rtc_base:gtest_prod",
      "../../rtc_base:protobuf_utils",
      "../../rtc_base:rtc_base_approved",
      "../../rtc_base:rtc_base_tests_utils",
      "../../rtc_base:safe_minmax",
      "../../rtc_base:task_queue_for_test",
      "../../rtc_base/system:arch",
      "../../rtc_base/system:file_wrapper",
      "../../system_wrappers",
      "../../system_wrappers:cpu_features_api",
      "../../test:fileutils",
      "../../test:test_support",
      "../audio_coding:neteq_input_audio_tools",
      "aec:aec_core",
      "aec:aec_unittests",
      "aec_dump:mock_aec_dump_unittests",
      "agc:agc_unittests",
      "agc2:adaptive_digital_unittests",
      "agc2:biquad_filter_unittests",
      "agc2:fixed_digital_unittests",
      "agc2:noise_estimator_unittests",
      "agc2:rnn_vad_with_level_unittests",
      "agc2:test_utils",
      "agc2/rnn_vad:unittests",
      "test/conversational_speech:unittest",
      "utility:block_mean_calculator_unittest",
      "utility:legacy_delay_estimator_unittest",
      "utility:pffft_wrapper_unittest",
      "vad:vad_unittests",
      "//testing/gtest",
      "//third_party/abseil-cpp/absl/memory",
      "//third_party/abseil-cpp/absl/types:optional",
    ]

    defines = []

    if (rtc_prefer_fixed_point) {
      defines += [ "WEBRTC_AUDIOPROC_FIXED_PROFILE" ]
    } else {
      defines += [ "WEBRTC_AUDIOPROC_FLOAT_PROFILE" ]
    }

    if (rtc_enable_protobuf) {
      defines += [ "WEBRTC_AUDIOPROC_DEBUG_DUMP" ]
      deps += [
        ":audioproc_debug_proto",
        ":audioproc_protobuf_utils",
        ":audioproc_test_utils",
        ":audioproc_unittest_proto",
        ":runtime_settings_protobuf_utils",
        "../../api/audio:audio_frame_api",
        "../../api/audio:echo_control",
        "../../rtc_base:rtc_base_tests_utils",
        "../../rtc_base:rtc_task_queue",
        "aec_dump",
        "aec_dump:aec_dump_unittests",
      ]
      sources += [
        "audio_processing_impl_locking_unittest.cc",
        "audio_processing_impl_unittest.cc",
        "audio_processing_unittest.cc",
        "echo_cancellation_bit_exact_unittest.cc",
        "echo_control_mobile_bit_exact_unittest.cc",
        "echo_detector/circular_buffer_unittest.cc",
        "echo_detector/mean_variance_estimator_unittest.cc",
        "echo_detector/moving_max_unittest.cc",
        "echo_detector/normalized_covariance_estimator_unittest.cc",
        "gain_control_unittest.cc",
        "level_estimator_unittest.cc",
        "low_cut_filter_unittest.cc",
        "noise_suppression_unittest.cc",
        "residual_echo_detector_unittest.cc",
        "rms_level_unittest.cc",
        "test/debug_dump_replayer.cc",
        "test/debug_dump_replayer.h",
        "test/debug_dump_test.cc",
        "test/echo_canceller_test_tools.cc",
        "test/echo_canceller_test_tools.h",
        "test/echo_canceller_test_tools_unittest.cc",
        "test/echo_control_mock.h",
        "test/test_utils.h",
        "voice_detection_unittest.cc",
      ]
    }
  }

  rtc_source_set("audio_processing_perf_tests") {
    testonly = true

    sources = [
      "audio_processing_performance_unittest.cc",
    ]
    deps = [
      ":audio_processing",
      ":audioproc_test_utils",
      "../../api:array_view",
      "../../rtc_base:protobuf_utils",
      "../../rtc_base:rtc_base_approved",
      "../../system_wrappers",
      "../../test:perf_test",
      "../../test:test_support",
    ]
  }

  rtc_source_set("file_audio_generator_unittests") {
    testonly = true

    sources = [
      "audio_generator/file_audio_generator_unittest.cc",
    ]

    deps = [
      ":api",
      ":audio_generator_factory",
      ":audio_processing",
      ":file_audio_generator",
      "../../rtc_base:rtc_base_approved",
      "../../test:fileutils",
      "../../test:test_support",
    ]
  }

  rtc_source_set("analog_mic_simulation") {
    sources = [
      "test/fake_recording_device.cc",
      "test/fake_recording_device.h",
    ]
    deps = [
      "../../api:array_view",
      "../../api/audio:audio_frame_api",
      "../../common_audio",
      "../../rtc_base:checks",
      "../../rtc_base:rtc_base_approved",
      "../../rtc_base:safe_minmax",
      "agc:gain_map",
      "//third_party/abseil-cpp/absl/memory",
      "//third_party/abseil-cpp/absl/types:optional",
    ]
  }

  if (rtc_enable_protobuf) {
    rtc_source_set("audioproc_f_impl") {
      testonly = true
      configs += [ ":apm_debug_dump" ]
      sources = [
        "test/aec_dump_based_simulator.cc",
        "test/aec_dump_based_simulator.h",
        "test/api_call_statistics.cc",
        "test/api_call_statistics.h",
        "test/audio_processing_simulator.cc",
        "test/audio_processing_simulator.h",
        "test/audioproc_float_impl.cc",
        "test/audioproc_float_impl.h",
        "test/wav_based_simulator.cc",
        "test/wav_based_simulator.h",
      ]

      deps = [
        ":analog_mic_simulation",
        ":api",
        ":apm_logging",
        ":audio_processing",
        ":audioproc_debug_proto",
        ":audioproc_protobuf_utils",
        ":audioproc_test_utils",
        ":runtime_settings_protobuf_utils",
        "../../api/audio:aec3_config_json",
        "../../api/audio:aec3_factory",
        "../../common_audio",
        "../../rtc_base:checks",
        "../../rtc_base:protobuf_utils",
        "../../rtc_base:rtc_base_approved",
        "../../rtc_base:rtc_json",
        "../../rtc_base:task_queue_for_test",
        "../../system_wrappers",
        "../../test:test_support",
        "aec_dump",
        "aec_dump:aec_dump_impl",
        "//testing/gtest",
        "//third_party/abseil-cpp/absl/memory",
        "//third_party/abseil-cpp/absl/strings",
        "//third_party/abseil-cpp/absl/types:optional",
      ]
    }  # audioproc_f_impl
    rtc_executable("audioproc_f") {
      testonly = true
      sources = [
        "test/audioproc_float_main.cc",
      ]
      deps = [
        ":api",
        ":audio_processing",
        "../../api:audioproc_f_api",
        "../../rtc_base:rtc_base_approved",
        "//third_party/abseil-cpp/absl/memory",
      ]
    }  # audioproc_f
  }

  rtc_source_set("audioproc_test_utils") {
    visibility = [ "*" ]
    testonly = true
    sources = [
      "test/audio_buffer_tools.cc",
      "test/audio_buffer_tools.h",
      "test/bitexactness_tools.cc",
      "test/bitexactness_tools.h",
      "test/performance_timer.cc",
      "test/performance_timer.h",
      "test/simulator_buffers.cc",
      "test/simulator_buffers.h",
      "test/test_utils.cc",
      "test/test_utils.h",
    ]

    deps = [
      ":api",
      ":audio_buffer",
      ":audio_processing",
      "../../api:array_view",
      "../../api/audio:audio_frame_api",
      "../../common_audio",
      "../../rtc_base:checks",
      "../../rtc_base:rtc_base_approved",
      "../../rtc_base/system:arch",
      "../../system_wrappers",
      "../../test:fileutils",
      "../../test:test_support",
      "../audio_coding:neteq_input_audio_tools",
      "//testing/gtest",
      "//third_party/abseil-cpp/absl/types:optional",
    ]
  }

  rtc_executable("transient_suppression_test") {
    testonly = true
    sources = [
      "transient/file_utils.cc",
      "transient/file_utils.h",
      "transient/transient_suppression_test.cc",
    ]
    deps = [
      ":audio_processing",
      "../../common_audio",
      "../../rtc_base:rtc_base_approved",
      "../../rtc_base/system:file_wrapper",
      "../../system_wrappers",
      "../../test:fileutils",
      "../../test:test_support",
      "agc:level_estimation",
      "//testing/gtest",
    ]
  }

  rtc_executable("click_annotate") {
    testonly = true
    sources = [
      "transient/click_annotate.cc",
      "transient/file_utils.cc",
      "transient/file_utils.h",
    ]
    deps = [
      ":audio_processing",
      "../../rtc_base/system:file_wrapper",
      "../../system_wrappers",
    ]
  }

  if (rtc_enable_protobuf) {
    proto_library("audioproc_unittest_proto") {
      sources = [
        "test/unittest.proto",
      ]
      proto_out_dir = "modules/audio_processing/test"
    }

    rtc_static_library("audioproc_protobuf_utils") {
      sources = [
        "test/protobuf_utils.cc",
        "test/protobuf_utils.h",
      ]

      deps = [
        ":audioproc_debug_proto",
        "../../rtc_base:checks",
        "../../rtc_base:protobuf_utils",
        "../../rtc_base:rtc_base_approved",
        "../../rtc_base/system:arch",
      ]
    }

    rtc_static_library("runtime_settings_protobuf_utils") {
      testonly = true
      sources = [
        "test/runtime_setting_util.cc",
        "test/runtime_setting_util.h",
      ]

      deps = [
        ":api",
        ":audio_processing",
        ":audioproc_debug_proto",
        ":audioproc_protobuf_utils",
        "../../rtc_base:checks",
      ]
    }
  }
}
