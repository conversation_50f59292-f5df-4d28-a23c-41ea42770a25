# Copyright (c) 2018 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("../../../webrtc.gni")

rtc_static_library("aec3") {
  visibility = [ "*" ]
  configs += [ "..:apm_debug_dump" ]
  sources = [
    "adaptive_fir_filter.cc",
    "adaptive_fir_filter.h",
    "aec3_common.cc",
    "aec3_common.h",
    "aec3_fft.cc",
    "aec3_fft.h",
    "aec_state.cc",
    "aec_state.h",
    "api_call_jitter_metrics.cc",
    "api_call_jitter_metrics.h",
    "block_delay_buffer.cc",
    "block_delay_buffer.h",
    "block_framer.cc",
    "block_framer.h",
    "block_processor.cc",
    "block_processor.h",
    "block_processor_metrics.cc",
    "block_processor_metrics.h",
    "cascaded_biquad_filter.cc",
    "cascaded_biquad_filter.h",
    "clockdrift_detector.cc",
    "clockdrift_detector.h",
    "comfort_noise_generator.cc",
    "comfort_noise_generator.h",
    "decimator.cc",
    "decimator.h",
    "delay_estimate.h",
    "downsampled_render_buffer.cc",
    "downsampled_render_buffer.h",
    "echo_audibility.cc",
    "echo_audibility.h",
    "echo_canceller3.cc",
    "echo_canceller3.h",
    "echo_path_delay_estimator.cc",
    "echo_path_delay_estimator.h",
    "echo_path_variability.cc",
    "echo_path_variability.h",
    "echo_remover.cc",
    "echo_remover.h",
    "echo_remover_metrics.cc",
    "echo_remover_metrics.h",
    "erl_estimator.cc",
    "erl_estimator.h",
    "erle_estimator.cc",
    "erle_estimator.h",
    "fft_buffer.cc",
    "fft_buffer.h",
    "fft_data.h",
    "filter_analyzer.cc",
    "filter_analyzer.h",
    "frame_blocker.cc",
    "frame_blocker.h",
    "fullband_erle_estimator.cc",
    "fullband_erle_estimator.h",
    "main_filter_update_gain.cc",
    "main_filter_update_gain.h",
    "matched_filter.cc",
    "matched_filter.h",
    "matched_filter_lag_aggregator.cc",
    "matched_filter_lag_aggregator.h",
    "matrix_buffer.cc",
    "matrix_buffer.h",
    "moving_average.cc",
    "moving_average.h",
    "render_buffer.cc",
    "render_buffer.h",
    "render_delay_buffer.cc",
    "render_delay_buffer.h",
    "render_delay_controller.cc",
    "render_delay_controller.h",
    "render_delay_controller_metrics.cc",
    "render_delay_controller_metrics.h",
    "render_reverb_model.cc",
    "render_reverb_model.h",
    "render_signal_analyzer.cc",
    "render_signal_analyzer.h",
    "residual_echo_estimator.cc",
    "residual_echo_estimator.h",
    "reverb_decay_estimator.cc",
    "reverb_decay_estimator.h",
    "reverb_frequency_response.cc",
    "reverb_frequency_response.h",
    "reverb_model.cc",
    "reverb_model.h",
    "reverb_model_estimator.cc",
    "reverb_model_estimator.h",
    "reverb_model_fallback.cc",
    "reverb_model_fallback.h",
    "shadow_filter_update_gain.cc",
    "shadow_filter_update_gain.h",
    "signal_dependent_erle_estimator.cc",
    "signal_dependent_erle_estimator.h",
    "stationarity_estimator.cc",
    "stationarity_estimator.h",
    "subband_erle_estimator.cc",
    "subband_erle_estimator.h",
    "subtractor.cc",
    "subtractor.h",
    "subtractor_output.cc",
    "subtractor_output.h",
    "subtractor_output_analyzer.cc",
    "subtractor_output_analyzer.h",
    "suppression_filter.cc",
    "suppression_filter.h",
    "suppression_gain.cc",
    "suppression_gain.h",
    "vector_buffer.cc",
    "vector_buffer.h",
    "vector_math.h",
  ]

  defines = []
  if (rtc_build_with_neon && current_cpu != "arm64") {
    suppressed_configs += [ "//build/config/compiler:compiler_arm_fpu" ]
    cflags = [ "-mfpu=neon" ]
  }

  deps = [
    "..:apm_logging",
    "..:audio_buffer",
    "../../../api:array_view",
    "../../../api/audio:aec3_config",
    "../../../api/audio:echo_control",
    "../../../common_audio:common_audio_c",
    "../../../rtc_base:checks",
    "../../../rtc_base:rtc_base_approved",
    "../../../rtc_base:safe_minmax",
    "../../../rtc_base/system:arch",
    "../../../system_wrappers:cpu_features_api",
    "../../../system_wrappers:field_trial",
    "../../../system_wrappers:metrics",
    "../utility:ooura_fft",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

if (rtc_include_tests) {
  rtc_source_set("aec3_unittests") {
    testonly = true

    configs += [ "..:apm_debug_dump" ]
    sources = [
      "mock/mock_block_processor.cc",
      "mock/mock_block_processor.h",
      "mock/mock_echo_remover.cc",
      "mock/mock_echo_remover.h",
      "mock/mock_render_delay_buffer.cc",
      "mock/mock_render_delay_buffer.h",
      "mock/mock_render_delay_controller.cc",
      "mock/mock_render_delay_controller.h",
    ]

    deps = [
      ":aec3",
      "..:apm_logging",
      "..:audio_buffer",
      "..:audio_processing",
      "..:audio_processing_unittests",
      "../../../api:array_view",
      "../../../api/audio:aec3_config",
      "../../../rtc_base:checks",
      "../../../rtc_base:rtc_base_approved",
      "../../../rtc_base:safe_minmax",
      "../../../rtc_base/system:arch",
      "../../../system_wrappers:cpu_features_api",
      "../../../test:test_support",
      "//third_party/abseil-cpp/absl/types:optional",
    ]

    defines = []

    if (rtc_enable_protobuf) {
      sources += [
        "adaptive_fir_filter_unittest.cc",
        "aec3_fft_unittest.cc",
        "aec_state_unittest.cc",
        "api_call_jitter_metrics_unittest.cc",
        "block_delay_buffer_unittest.cc",
        "block_framer_unittest.cc",
        "block_processor_metrics_unittest.cc",
        "block_processor_unittest.cc",
        "cascaded_biquad_filter_unittest.cc",
        "clockdrift_detector_unittest.cc",
        "comfort_noise_generator_unittest.cc",
        "decimator_unittest.cc",
        "echo_canceller3_unittest.cc",
        "echo_path_delay_estimator_unittest.cc",
        "echo_path_variability_unittest.cc",
        "echo_remover_metrics_unittest.cc",
        "echo_remover_unittest.cc",
        "erl_estimator_unittest.cc",
        "erle_estimator_unittest.cc",
        "fft_data_unittest.cc",
        "filter_analyzer_unittest.cc",
        "frame_blocker_unittest.cc",
        "main_filter_update_gain_unittest.cc",
        "matched_filter_lag_aggregator_unittest.cc",
        "matched_filter_unittest.cc",
        "moving_average_unittest.cc",
        "render_buffer_unittest.cc",
        "render_delay_buffer_unittest.cc",
        "render_delay_controller_metrics_unittest.cc",
        "render_delay_controller_unittest.cc",
        "render_signal_analyzer_unittest.cc",
        "residual_echo_estimator_unittest.cc",
        "reverb_model_estimator_unittest.cc",
        "shadow_filter_update_gain_unittest.cc",
        "signal_dependent_erle_estimator_unittest.cc",
        "subtractor_unittest.cc",
        "suppression_filter_unittest.cc",
        "suppression_gain_unittest.cc",
        "vector_math_unittest.cc",
      ]
    }
  }
}
