/*
 *  Copyright (c) 2011 The WebRTC project authors. All Rights Reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS.  All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */

#ifndef MODULES_AUDIO_PROCESSING_NS_MAIN_SOURCE_WINDOWS_PRIVATE_H_
#define MODULES_AUDIO_PROCESSING_NS_MAIN_SOURCE_WINDOWS_PRIVATE_H_

// Hanning window for 4ms 16kHz
static const float kHanning64w128[128] = {
    0.00000000000000f, 0.02454122852291f, 0.04906767432742f, 0.07356456359967f,
    0.09801714032956f, 0.12241067519922f, 0.14673047445536f, 0.17096188876030f,
    0.19509032201613f, 0.21910124015687f, 0.24298017990326f, 0.26671275747490f,
    0.29028467725446f, 0.31368174039889f, 0.33688985339222f, 0.35989503653499f,
    0.38268343236509f, 0.40524131400499f, 0.42755509343028f, 0.44961132965461f,
    0.47139673682600f, 0.49289819222978f, 0.51410274419322f, 0.53499761988710f,
    0.55557023301960f, 0.57580819141785f, 0.59569930449243f, 0.61523159058063f,
    0.63439328416365f, 0.65317284295378f, 0.67155895484702f, 0.68954054473707f,
    0.70710678118655f, 0.72424708295147f, 0.74095112535496f, 0.75720884650648f,
    0.77301045336274f, 0.78834642762661f, 0.80320753148064f, 0.81758481315158f,
    0.83146961230255f, 0.84485356524971f, 0.85772861000027f, 0.87008699110871f,
    0.88192126434835f, 0.89322430119552f, 0.90398929312344f, 0.91420975570353f,
    0.92387953251129f, 0.93299279883474f, 0.94154406518302f, 0.94952818059304f,
    0.95694033573221f, 0.96377606579544f, 0.97003125319454f, 0.97570213003853f,
    0.98078528040323f, 0.98527764238894f, 0.98917650996478f, 0.99247953459871f,
    0.99518472667220f, 0.99729045667869f, 0.99879545620517f, 0.99969881869620f,
    1.00000000000000f, 0.99969881869620f, 0.99879545620517f, 0.99729045667869f,
    0.99518472667220f, 0.99247953459871f, 0.98917650996478f, 0.98527764238894f,
    0.98078528040323f, 0.97570213003853f, 0.97003125319454f, 0.96377606579544f,
    0.95694033573221f, 0.94952818059304f, 0.94154406518302f, 0.93299279883474f,
    0.92387953251129f, 0.91420975570353f, 0.90398929312344f, 0.89322430119552f,
    0.88192126434835f, 0.87008699110871f, 0.85772861000027f, 0.84485356524971f,
    0.83146961230255f, 0.81758481315158f, 0.80320753148064f, 0.78834642762661f,
    0.77301045336274f, 0.75720884650648f, 0.74095112535496f, 0.72424708295147f,
    0.70710678118655f, 0.68954054473707f, 0.67155895484702f, 0.65317284295378f,
    0.63439328416365f, 0.61523159058063f, 0.59569930449243f, 0.57580819141785f,
    0.55557023301960f, 0.53499761988710f, 0.51410274419322f, 0.49289819222978f,
    0.47139673682600f, 0.44961132965461f, 0.42755509343028f, 0.40524131400499f,
    0.38268343236509f, 0.35989503653499f, 0.33688985339222f, 0.31368174039889f,
    0.29028467725446f, 0.26671275747490f, 0.24298017990326f, 0.21910124015687f,
    0.19509032201613f, 0.17096188876030f, 0.14673047445536f, 0.12241067519922f,
    0.09801714032956f, 0.07356456359967f, 0.04906767432742f, 0.02454122852291f};

// hybrib Hanning & flat window
static const float kBlocks80w128[128] = {
    (float)0.00000000, (float)0.03271908, (float)0.06540313, (float)0.09801714,
    (float)0.13052619, (float)0.16289547, (float)0.19509032, (float)0.22707626,
    (float)0.25881905, (float)0.29028468, (float)0.32143947, (float)0.35225005,
    (float)0.38268343, (float)0.41270703, (float)0.44228869, (float)0.47139674,
    (float)0.50000000, (float)0.52806785, (float)0.55557023, (float)0.58247770,
    (float)0.60876143, (float)0.63439328, (float)0.65934582, (float)0.68359230,
    (float)0.70710678, (float)0.72986407, (float)0.75183981, (float)0.77301045,
    (float)0.79335334, (float)0.81284668, (float)0.83146961, (float)0.84920218,
    (float)0.86602540, (float)0.88192126, (float)0.89687274, (float)0.91086382,
    (float)0.92387953, (float)0.93590593, (float)0.94693013, (float)0.95694034,
    (float)0.96592583, (float)0.97387698, (float)0.98078528, (float)0.98664333,
    (float)0.99144486, (float)0.99518473, (float)0.99785892, (float)0.99946459,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)0.99946459, (float)0.99785892, (float)0.99518473,
    (float)0.99144486, (float)0.98664333, (float)0.98078528, (float)0.97387698,
    (float)0.96592583, (float)0.95694034, (float)0.94693013, (float)0.93590593,
    (float)0.92387953, (float)0.91086382, (float)0.89687274, (float)0.88192126,
    (float)0.86602540, (float)0.84920218, (float)0.83146961, (float)0.81284668,
    (float)0.79335334, (float)0.77301045, (float)0.75183981, (float)0.72986407,
    (float)0.70710678, (float)0.68359230, (float)0.65934582, (float)0.63439328,
    (float)0.60876143, (float)0.58247770, (float)0.55557023, (float)0.52806785,
    (float)0.50000000, (float)0.47139674, (float)0.44228869, (float)0.41270703,
    (float)0.38268343, (float)0.35225005, (float)0.32143947, (float)0.29028468,
    (float)0.25881905, (float)0.22707626, (float)0.19509032, (float)0.16289547,
    (float)0.13052619, (float)0.09801714, (float)0.06540313, (float)0.03271908};

// hybrib Hanning & flat window
static const float kBlocks160w256[256] = {
    (float)0.00000000, (float)0.01636173, (float)0.03271908, (float)0.04906767,
    (float)0.06540313, (float)0.08172107, (float)0.09801714, (float)0.11428696,
    (float)0.13052619, (float)0.14673047, (float)0.16289547, (float)0.17901686,
    (float)0.19509032, (float)0.21111155, (float)0.22707626, (float)0.24298018,
    (float)0.25881905, (float)0.27458862, (float)0.29028468, (float)0.30590302,
    (float)0.32143947, (float)0.33688985, (float)0.35225005, (float)0.36751594,
    (float)0.38268343, (float)0.39774847, (float)0.41270703, (float)0.42755509,
    (float)0.44228869, (float)0.45690388, (float)0.47139674, (float)0.48576339,
    (float)0.50000000, (float)0.51410274, (float)0.52806785, (float)0.54189158,
    (float)0.55557023, (float)0.56910015, (float)0.58247770, (float)0.59569930,
    (float)0.60876143, (float)0.62166057, (float)0.63439328, (float)0.64695615,
    (float)0.65934582, (float)0.67155895, (float)0.68359230, (float)0.69544264,
    (float)0.70710678, (float)0.71858162, (float)0.72986407, (float)0.74095113,
    (float)0.75183981, (float)0.76252720, (float)0.77301045, (float)0.78328675,
    (float)0.79335334, (float)0.80320753, (float)0.81284668, (float)0.82226822,
    (float)0.83146961, (float)0.84044840, (float)0.84920218, (float)0.85772861,
    (float)0.86602540, (float)0.87409034, (float)0.88192126, (float)0.88951608,
    (float)0.89687274, (float)0.90398929, (float)0.91086382, (float)0.91749450,
    (float)0.92387953, (float)0.93001722, (float)0.93590593, (float)0.94154407,
    (float)0.94693013, (float)0.95206268, (float)0.95694034, (float)0.96156180,
    (float)0.96592583, (float)0.97003125, (float)0.97387698, (float)0.97746197,
    (float)0.98078528, (float)0.98384601, (float)0.98664333, (float)0.98917651,
    (float)0.99144486, (float)0.99344778, (float)0.99518473, (float)0.99665524,
    (float)0.99785892, (float)0.99879546, (float)0.99946459, (float)0.99986614,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)0.99986614, (float)0.99946459, (float)0.99879546,
    (float)0.99785892, (float)0.99665524, (float)0.99518473, (float)0.99344778,
    (float)0.99144486, (float)0.98917651, (float)0.98664333, (float)0.98384601,
    (float)0.98078528, (float)0.97746197, (float)0.97387698, (float)0.97003125,
    (float)0.96592583, (float)0.96156180, (float)0.95694034, (float)0.95206268,
    (float)0.94693013, (float)0.94154407, (float)0.93590593, (float)0.93001722,
    (float)0.92387953, (float)0.91749450, (float)0.91086382, (float)0.90398929,
    (float)0.89687274, (float)0.88951608, (float)0.88192126, (float)0.87409034,
    (float)0.86602540, (float)0.85772861, (float)0.84920218, (float)0.84044840,
    (float)0.83146961, (float)0.82226822, (float)0.81284668, (float)0.80320753,
    (float)0.79335334, (float)0.78328675, (float)0.77301045, (float)0.76252720,
    (float)0.75183981, (float)0.74095113, (float)0.72986407, (float)0.71858162,
    (float)0.70710678, (float)0.69544264, (float)0.68359230, (float)0.67155895,
    (float)0.65934582, (float)0.64695615, (float)0.63439328, (float)0.62166057,
    (float)0.60876143, (float)0.59569930, (float)0.58247770, (float)0.56910015,
    (float)0.55557023, (float)0.54189158, (float)0.52806785, (float)0.51410274,
    (float)0.50000000, (float)0.48576339, (float)0.47139674, (float)0.45690388,
    (float)0.44228869, (float)0.42755509, (float)0.41270703, (float)0.39774847,
    (float)0.38268343, (float)0.36751594, (float)0.35225005, (float)0.33688985,
    (float)0.32143947, (float)0.30590302, (float)0.29028468, (float)0.27458862,
    (float)0.25881905, (float)0.24298018, (float)0.22707626, (float)0.21111155,
    (float)0.19509032, (float)0.17901686, (float)0.16289547, (float)0.14673047,
    (float)0.13052619, (float)0.11428696, (float)0.09801714, (float)0.08172107,
    (float)0.06540313, (float)0.04906767, (float)0.03271908, (float)0.01636173};

// hybrib Hanning & flat window: for 20ms
static const float kBlocks320w512[512] = {
    (float)0.00000000, (float)0.00818114, (float)0.01636173, (float)0.02454123,
    (float)0.03271908, (float)0.04089475, (float)0.04906767, (float)0.05723732,
    (float)0.06540313, (float)0.07356456, (float)0.08172107, (float)0.08987211,
    (float)0.09801714, (float)0.10615561, (float)0.11428696, (float)0.12241068,
    (float)0.13052619, (float)0.13863297, (float)0.14673047, (float)0.15481816,
    (float)0.16289547, (float)0.17096189, (float)0.17901686, (float)0.18705985,
    (float)0.19509032, (float)0.20310773, (float)0.21111155, (float)0.21910124,
    (float)0.22707626, (float)0.23503609, (float)0.24298018, (float)0.25090801,
    (float)0.25881905, (float)0.26671276, (float)0.27458862, (float)0.28244610,
    (float)0.29028468, (float)0.29810383, (float)0.30590302, (float)0.31368174,
    (float)0.32143947, (float)0.32917568, (float)0.33688985, (float)0.34458148,
    (float)0.35225005, (float)0.35989504, (float)0.36751594, (float)0.37511224,
    (float)0.38268343, (float)0.39022901, (float)0.39774847, (float)0.40524131,
    (float)0.41270703, (float)0.42014512, (float)0.42755509, (float)0.43493645,
    (float)0.44228869, (float)0.44961133, (float)0.45690388, (float)0.46416584,
    (float)0.47139674, (float)0.47859608, (float)0.48576339, (float)0.49289819,
    (float)0.50000000, (float)0.50706834, (float)0.51410274, (float)0.52110274,
    (float)0.52806785, (float)0.53499762, (float)0.54189158, (float)0.54874927,
    (float)0.55557023, (float)0.56235401, (float)0.56910015, (float)0.57580819,
    (float)0.58247770, (float)0.58910822, (float)0.59569930, (float)0.60225052,
    (float)0.60876143, (float)0.61523159, (float)0.62166057, (float)0.62804795,
    (float)0.63439328, (float)0.64069616, (float)0.64695615, (float)0.65317284,
    (float)0.65934582, (float)0.66547466, (float)0.67155895, (float)0.67759830,
    (float)0.68359230, (float)0.68954054, (float)0.69544264, (float)0.70129818,
    (float)0.70710678, (float)0.71286806, (float)0.71858162, (float)0.72424708,
    (float)0.72986407, (float)0.73543221, (float)0.74095113, (float)0.74642045,
    (float)0.75183981, (float)0.75720885, (float)0.76252720, (float)0.76779452,
    (float)0.77301045, (float)0.77817464, (float)0.78328675, (float)0.78834643,
    (float)0.79335334, (float)0.79830715, (float)0.80320753, (float)0.80805415,
    (float)0.81284668, (float)0.81758481, (float)0.82226822, (float)0.82689659,
    (float)0.83146961, (float)0.83598698, (float)0.84044840, (float)0.84485357,
    (float)0.84920218, (float)0.85349396, (float)0.85772861, (float)0.86190585,
    (float)0.86602540, (float)0.87008699, (float)0.87409034, (float)0.87803519,
    (float)0.88192126, (float)0.88574831, (float)0.88951608, (float)0.89322430,
    (float)0.89687274, (float)0.90046115, (float)0.90398929, (float)0.90745693,
    (float)0.91086382, (float)0.91420976, (float)0.91749450, (float)0.92071783,
    (float)0.92387953, (float)0.92697940, (float)0.93001722, (float)0.93299280,
    (float)0.93590593, (float)0.93875641, (float)0.94154407, (float)0.94426870,
    (float)0.94693013, (float)0.94952818, (float)0.95206268, (float)0.95453345,
    (float)0.95694034, (float)0.95928317, (float)0.96156180, (float)0.96377607,
    (float)0.96592583, (float)0.96801094, (float)0.97003125, (float)0.97198664,
    (float)0.97387698, (float)0.97570213, (float)0.97746197, (float)0.97915640,
    (float)0.98078528, (float)0.98234852, (float)0.98384601, (float)0.98527764,
    (float)0.98664333, (float)0.98794298, (float)0.98917651, (float)0.99034383,
    (float)0.99144486, (float)0.99247953, (float)0.99344778, (float)0.99434953,
    (float)0.99518473, (float)0.99595331, (float)0.99665524, (float)0.99729046,
    (float)0.99785892, (float)0.99836060, (float)0.99879546, (float)0.99916346,
    (float)0.99946459, (float)0.99969882, (float)0.99986614, (float)0.99996653,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)1.00000000, (float)1.00000000, (float)1.00000000,
    (float)1.00000000, (float)0.99996653, (float)0.99986614, (float)0.99969882,
    (float)0.99946459, (float)0.99916346, (float)0.99879546, (float)0.99836060,
    (float)0.99785892, (float)0.99729046, (float)0.99665524, (float)0.99595331,
    (float)0.99518473, (float)0.99434953, (float)0.99344778, (float)0.99247953,
    (float)0.99144486, (float)0.99034383, (float)0.98917651, (float)0.98794298,
    (float)0.98664333, (float)0.98527764, (float)0.98384601, (float)0.98234852,
    (float)0.98078528, (float)0.97915640, (float)0.97746197, (float)0.97570213,
    (float)0.97387698, (float)0.97198664, (float)0.97003125, (float)0.96801094,
    (float)0.96592583, (float)0.96377607, (float)0.96156180, (float)0.95928317,
    (float)0.95694034, (float)0.95453345, (float)0.95206268, (float)0.94952818,
    (float)0.94693013, (float)0.94426870, (float)0.94154407, (float)0.93875641,
    (float)0.93590593, (float)0.93299280, (float)0.93001722, (float)0.92697940,
    (float)0.92387953, (float)0.92071783, (float)0.91749450, (float)0.91420976,
    (float)0.91086382, (float)0.90745693, (float)0.90398929, (float)0.90046115,
    (float)0.89687274, (float)0.89322430, (float)0.88951608, (float)0.88574831,
    (float)0.88192126, (float)0.87803519, (float)0.87409034, (float)0.87008699,
    (float)0.86602540, (float)0.86190585, (float)0.85772861, (float)0.85349396,
    (float)0.84920218, (float)0.84485357, (float)0.84044840, (float)0.83598698,
    (float)0.83146961, (float)0.82689659, (float)0.82226822, (float)0.81758481,
    (float)0.81284668, (float)0.80805415, (float)0.80320753, (float)0.79830715,
    (float)0.79335334, (float)0.78834643, (float)0.78328675, (float)0.77817464,
    (float)0.77301045, (float)0.76779452, (float)0.76252720, (float)0.75720885,
    (float)0.75183981, (float)0.74642045, (float)0.74095113, (float)0.73543221,
    (float)0.72986407, (float)0.72424708, (float)0.71858162, (float)0.71286806,
    (float)0.70710678, (float)0.70129818, (float)0.69544264, (float)0.68954054,
    (float)0.68359230, (float)0.67759830, (float)0.67155895, (float)0.66547466,
    (float)0.65934582, (float)0.65317284, (float)0.64695615, (float)0.64069616,
    (float)0.63439328, (float)0.62804795, (float)0.62166057, (float)0.61523159,
    (float)0.60876143, (float)0.60225052, (float)0.59569930, (float)0.58910822,
    (float)0.58247770, (float)0.57580819, (float)0.56910015, (float)0.56235401,
    (float)0.55557023, (float)0.54874927, (float)0.54189158, (float)0.53499762,
    (float)0.52806785, (float)0.52110274, (float)0.51410274, (float)0.50706834,
    (float)0.50000000, (float)0.49289819, (float)0.48576339, (float)0.47859608,
    (float)0.47139674, (float)0.46416584, (float)0.45690388, (float)0.44961133,
    (float)0.44228869, (float)0.43493645, (float)0.42755509, (float)0.42014512,
    (float)0.41270703, (float)0.40524131, (float)0.39774847, (float)0.39022901,
    (float)0.38268343, (float)0.37511224, (float)0.36751594, (float)0.35989504,
    (float)0.35225005, (float)0.34458148, (float)0.33688985, (float)0.32917568,
    (float)0.32143947, (float)0.31368174, (float)0.30590302, (float)0.29810383,
    (float)0.29028468, (float)0.28244610, (float)0.27458862, (float)0.26671276,
    (float)0.25881905, (float)0.25090801, (float)0.24298018, (float)0.23503609,
    (float)0.22707626, (float)0.21910124, (float)0.21111155, (float)0.20310773,
    (float)0.19509032, (float)0.18705985, (float)0.17901686, (float)0.17096189,
    (float)0.16289547, (float)0.15481816, (float)0.14673047, (float)0.13863297,
    (float)0.13052619, (float)0.12241068, (float)0.11428696, (float)0.10615561,
    (float)0.09801714, (float)0.08987211, (float)0.08172107, (float)0.07356456,
    (float)0.06540313, (float)0.05723732, (float)0.04906767, (float)0.04089475,
    (float)0.03271908, (float)0.02454123, (float)0.01636173, (float)0.00818114};

// Hanning window: for 15ms at 16kHz with symmetric zeros
static const float kBlocks240w512[512] = {
    (float)0.00000000, (float)0.00000000, (float)0.00000000, (float)0.00000000,
    (float)0.00000000, (float)0.00000000, (float)0.00000000, (float)0.00000000,
    (float)0.00000000, (float)0.00000000, (float)0.00000000, (float)0.00000000,
    (float)0.00000000, (float)0.00000000, (float)0.00000000, (float)0.00000000,
    (float)0.00000000, (float)0.00654494, (float)0.01308960, (float)0.01963369,
    (float)0.02617695, (float)0.03271908, (float)0.03925982, (float)0.04579887,
    (float)0.05233596, (float)0.05887080, (float)0.06540313, (float)0.07193266,
    (float)0.07845910, (float)0.08498218, (float)0.09150162, (float)0.09801714,
    (float)0.10452846, (float)0.11103531, (float)0.11753740, (float)0.12403446,
    (float)0.13052620, (float)0.13701233, (float)0.14349262, (float)0.14996676,
    (float)0.15643448, (float)0.16289547, (float)0.16934951, (float)0.17579629,
    (float)0.18223552, (float)0.18866697, (float)0.19509032, (float)0.20150533,
    (float)0.20791170, (float)0.21430916, (float)0.22069745, (float)0.22707628,
    (float)0.23344538, (float)0.23980446, (float)0.24615330, (float)0.25249159,
    (float)0.25881904, (float)0.26513544, (float)0.27144045, (float)0.27773386,
    (float)0.28401536, (float)0.29028466, (float)0.29654160, (float)0.30278578,
    (float)0.30901700, (float)0.31523499, (float)0.32143945, (float)0.32763019,
    (float)0.33380687, (float)0.33996925, (float)0.34611708, (float)0.35225007,
    (float)0.35836795, (float)0.36447051, (float)0.37055743, (float)0.37662852,
    (float)0.38268346, (float)0.38872197, (float)0.39474389, (float)0.40074885,
    (float)0.40673664, (float)0.41270703, (float)0.41865975, (float)0.42459452,
    (float)0.43051112, (float)0.43640924, (float)0.44228873, (float)0.44814920,
    (float)0.45399052, (float)0.45981237, (float)0.46561453, (float)0.47139674,
    (float)0.47715878, (float)0.48290035, (float)0.48862126, (float)0.49432120,
    (float)0.50000000, (float)0.50565743, (float)0.51129311, (float)0.51690692,
    (float)0.52249855, (float)0.52806789, (float)0.53361452, (float)0.53913832,
    (float)0.54463905, (float)0.55011642, (float)0.55557024, (float)0.56100029,
    (float)0.56640625, (float)0.57178795, (float)0.57714522, (float)0.58247769,
    (float)0.58778524, (float)0.59306765, (float)0.59832460, (float)0.60355598,
    (float)0.60876143, (float)0.61394083, (float)0.61909395, (float)0.62422055,
    (float)0.62932038, (float)0.63439333, (float)0.63943899, (float)0.64445734,
    (float)0.64944810, (float)0.65441096, (float)0.65934587, (float)0.66425246,
    (float)0.66913062, (float)0.67398012, (float)0.67880076, (float)0.68359232,
    (float)0.68835455, (float)0.69308740, (float)0.69779050, (float)0.70246369,
    (float)0.70710677, (float)0.71171963, (float)0.71630198, (float)0.72085363,
    (float)0.72537440, (float)0.72986406, (float)0.73432255, (float)0.73874950,
    (float)0.74314487, (float)0.74750835, (float)0.75183982, (float)0.75613910,
    (float)0.76040596, (float)0.76464027, (float)0.76884186, (float)0.77301043,
    (float)0.77714598, (float)0.78124821, (float)0.78531694, (float)0.78935206,
    (float)0.79335338, (float)0.79732066, (float)0.80125386, (float)0.80515265,
    (float)0.80901700, (float)0.81284672, (float)0.81664157, (float)0.82040149,
    (float)0.82412618, (float)0.82781565, (float)0.83146966, (float)0.83508795,
    (float)0.83867061, (float)0.84221727, (float)0.84572780, (float)0.84920216,
    (float)0.85264021, (float)0.85604161, (float)0.85940641, (float)0.86273444,
    (float)0.86602545, (float)0.86927933, (float)0.87249607, (float)0.87567532,
    (float)0.87881714, (float)0.88192129, (float)0.88498765, (float)0.88801610,
    (float)0.89100653, (float)0.89395881, (float)0.89687276, (float)0.89974827,
    (float)0.90258533, (float)0.90538365, (float)0.90814316, (float)0.91086388,
    (float)0.91354549, (float)0.91618794, (float)0.91879123, (float)0.92135513,
    (float)0.92387950, (float)0.92636442, (float)0.92880958, (float)0.93121493,
    (float)0.93358046, (float)0.93590593, (float)0.93819135, (float)0.94043654,
    (float)0.94264150, (float)0.94480604, (float)0.94693011, (float)0.94901365,
    (float)0.95105654, (float)0.95305866, (float)0.95501995, (float)0.95694035,
    (float)0.95881975, (float)0.96065807, (float)0.96245527, (float)0.96421117,
    (float)0.96592581, (float)0.96759909, (float)0.96923089, (float)0.97082120,
    (float)0.97236991, (float)0.97387701, (float)0.97534233, (float)0.97676587,
    (float)0.97814763, (float)0.97948742, (float)0.98078531, (float)0.98204112,
    (float)0.98325491, (float)0.98442656, (float)0.98555607, (float)0.98664331,
    (float)0.98768836, (float)0.98869103, (float)0.98965138, (float)0.99056935,
    (float)0.99144489, (float)0.99227792, (float)0.99306846, (float)0.99381649,
    (float)0.99452192, (float)0.99518472, (float)0.99580491, (float)0.99638247,
    (float)0.99691731, (float)0.99740952, (float)0.99785894, (float)0.99826562,
    (float)0.99862951, (float)0.99895066, (float)0.99922901, (float)0.99946457,
    (float)0.99965733, (float)0.99980724, (float)0.99991435, (float)0.99997860,
    (float)1.00000000, (float)0.99997860, (float)0.99991435, (float)0.99980724,
    (float)0.99965733, (float)0.99946457, (float)0.99922901, (float)0.99895066,
    (float)0.99862951, (float)0.99826562, (float)0.99785894, (float)0.99740946,
    (float)0.99691731, (float)0.99638247, (float)0.99580491, (float)0.99518472,
    (float)0.99452192, (float)0.99381644, (float)0.99306846, (float)0.99227792,
    (float)0.99144489, (float)0.99056935, (float)0.98965138, (float)0.98869103,
    (float)0.98768836, (float)0.98664331, (float)0.98555607, (float)0.98442656,
    (float)0.98325491, (float)0.98204112, (float)0.98078525, (float)0.97948742,
    (float)0.97814757, (float)0.97676587, (float)0.97534227, (float)0.97387695,
    (float)0.97236991, (float)0.97082120, (float)0.96923089, (float)0.96759909,
    (float)0.96592581, (float)0.96421117, (float)0.96245521, (float)0.96065807,
    (float)0.95881969, (float)0.95694029, (float)0.95501995, (float)0.95305860,
    (float)0.95105648, (float)0.94901365, (float)0.94693011, (float)0.94480604,
    (float)0.94264150, (float)0.94043654, (float)0.93819129, (float)0.93590593,
    (float)0.93358046, (float)0.93121493, (float)0.92880952, (float)0.92636436,
    (float)0.92387950, (float)0.92135507, (float)0.91879123, (float)0.91618794,
    (float)0.91354543, (float)0.91086382, (float)0.90814310, (float)0.90538365,
    (float)0.90258527, (float)0.89974827, (float)0.89687276, (float)0.89395875,
    (float)0.89100647, (float)0.88801610, (float)0.88498759, (float)0.88192123,
    (float)0.87881714, (float)0.87567532, (float)0.87249595, (float)0.86927933,
    (float)0.86602539, (float)0.86273432, (float)0.85940641, (float)0.85604161,
    (float)0.85264009, (float)0.84920216, (float)0.84572780, (float)0.84221715,
    (float)0.83867055, (float)0.83508795, (float)0.83146954, (float)0.82781565,
    (float)0.82412612, (float)0.82040137, (float)0.81664157, (float)0.81284660,
    (float)0.80901700, (float)0.80515265, (float)0.80125374, (float)0.79732066,
    (float)0.79335332, (float)0.78935200, (float)0.78531694, (float)0.78124815,
    (float)0.77714586, (float)0.77301049, (float)0.76884180, (float)0.76464021,
    (float)0.76040596, (float)0.75613904, (float)0.75183970, (float)0.74750835,
    (float)0.74314481, (float)0.73874938, (float)0.73432249, (float)0.72986400,
    (float)0.72537428, (float)0.72085363, (float)0.71630186, (float)0.71171951,
    (float)0.70710677, (float)0.70246363, (float)0.69779032, (float)0.69308734,
    (float)0.68835449, (float)0.68359220, (float)0.67880070, (float)0.67398006,
    (float)0.66913044, (float)0.66425240, (float)0.65934575, (float)0.65441096,
    (float)0.64944804, (float)0.64445722, (float)0.63943905, (float)0.63439327,
    (float)0.62932026, (float)0.62422055, (float)0.61909389, (float)0.61394072,
    (float)0.60876143, (float)0.60355592, (float)0.59832448, (float)0.59306765,
    (float)0.58778518, (float)0.58247757, (float)0.57714522, (float)0.57178789,
    (float)0.56640613, (float)0.56100023, (float)0.55557019, (float)0.55011630,
    (float)0.54463905, (float)0.53913826, (float)0.53361434, (float)0.52806783,
    (float)0.52249849, (float)0.51690674, (float)0.51129305, (float)0.50565726,
    (float)0.50000006, (float)0.49432117, (float)0.48862115, (float)0.48290038,
    (float)0.47715873, (float)0.47139663, (float)0.46561456, (float)0.45981231,
    (float)0.45399037, (float)0.44814920, (float)0.44228864, (float)0.43640912,
    (float)0.43051112, (float)0.42459446, (float)0.41865960, (float)0.41270703,
    (float)0.40673658, (float)0.40074870, (float)0.39474386, (float)0.38872188,
    (float)0.38268328, (float)0.37662849, (float)0.37055734, (float)0.36447033,
    (float)0.35836792, (float)0.35224995, (float)0.34611690, (float)0.33996922,
    (float)0.33380675, (float)0.32763001, (float)0.32143945, (float)0.31523487,
    (float)0.30901679, (float)0.30278572, (float)0.29654145, (float)0.29028472,
    (float)0.28401530, (float)0.27773371, (float)0.27144048, (float)0.26513538,
    (float)0.25881892, (float)0.25249159, (float)0.24615324, (float)0.23980433,
    (float)0.23344538, (float)0.22707619, (float)0.22069728, (float)0.21430916,
    (float)0.20791161, (float)0.20150517, (float)0.19509031, (float)0.18866688,
    (float)0.18223536, (float)0.17579627, (float)0.16934940, (float)0.16289529,
    (float)0.15643445, (float)0.14996666, (float)0.14349243, (float)0.13701232,
    (float)0.13052608, (float)0.12403426, (float)0.11753736, (float)0.11103519,
    (float)0.10452849, (float)0.09801710, (float)0.09150149, (float)0.08498220,
    (float)0.07845904, (float)0.07193252, (float)0.06540315, (float)0.05887074,
    (float)0.05233581, (float)0.04579888, (float)0.03925974, (float)0.03271893,
    (float)0.02617695, (float)0.01963361, (float)0.01308943, (float)0.00654493,
    (float)0.00000000, (float)0.00000000, (float)0.00000000, (float)0.00000000,
    (float)0.00000000, (float)0.00000000, (float)0.00000000, (float)0.00000000,
    (float)0.00000000, (float)0.00000000, (float)0.00000000, (float)0.00000000,
    (float)0.00000000, (float)0.00000000, (float)0.00000000, (float)0.00000000};

// Hanning window: for 30ms with 1024 fft with symmetric zeros at 16kHz
static const float kBlocks480w1024[1024] = {
    (float)0.00000000, (float)0.00000000, (float)0.00000000, (float)0.00000000,
    (float)0.00000000, (float)0.00000000, (float)0.00000000, (float)0.00000000,
    (float)0.00000000, (float)0.00000000, (float)0.00000000, (float)0.00000000,
    (float)0.00000000, (float)0.00000000, (float)0.00000000, (float)0.00000000,
    (float)0.00000000, (float)0.00000000, (float)0.00000000, (float)0.00000000,
    (float)0.00000000, (float)0.00000000, (float)0.00000000, (float)0.00000000,
    (float)0.00000000, (float)0.00000000, (float)0.00000000, (float)0.00000000,
    (float)0.00000000, (float)0.00000000, (float)0.00000000, (float)0.00000000,
    (float)0.00000000, (float)0.00327249, (float)0.00654494, (float)0.00981732,
    (float)0.01308960, (float)0.01636173, (float)0.01963369, (float)0.02290544,
    (float)0.02617695, (float)0.02944817, (float)0.03271908, (float)0.03598964,
    (float)0.03925982, (float)0.04252957, (float)0.04579887, (float)0.04906768,
    (float)0.05233596, (float)0.05560368, (float)0.05887080, (float)0.06213730,
    (float)0.06540313, (float)0.06866825, (float)0.07193266, (float)0.07519628,
    (float)0.07845910, (float)0.08172107, (float)0.08498218, (float)0.08824237,
    (float)0.09150162, (float)0.09475989, (float)0.09801714, (float)0.10127335,
    (float)0.10452846, (float)0.10778246, (float)0.11103531, (float)0.11428697,
    (float)0.11753740, (float)0.12078657, (float)0.12403446, (float)0.12728101,
    (float)0.13052620, (float)0.13376999, (float)0.13701233, (float)0.14025325,
    (float)0.14349262, (float)0.14673047, (float)0.14996676, (float)0.15320145,
    (float)0.15643448, (float)0.15966582, (float)0.16289547, (float)0.16612339,
    (float)0.16934951, (float)0.17257382, (float)0.17579629, (float)0.17901687,
    (float)0.18223552, (float)0.18545224, (float)0.18866697, (float)0.19187967,
    (float)0.19509032, (float)0.19829889, (float)0.20150533, (float)0.20470962,
    (float)0.20791170, (float)0.21111156, (float)0.21430916, (float)0.21750447,
    (float)0.22069745, (float)0.22388805, (float)0.22707628, (float)0.23026206,
    (float)0.23344538, (float)0.23662618, (float)0.23980446, (float)0.24298020,
    (float)0.24615330, (float)0.24932377, (float)0.25249159, (float)0.25565669,
    (float)0.25881904, (float)0.26197866, (float)0.26513544, (float)0.26828939,
    (float)0.27144045, (float)0.27458861, (float)0.27773386, (float)0.28087610,
    (float)0.28401536, (float)0.28715158, (float)0.29028466, (float)0.29341471,
    (float)0.29654160, (float)0.29966527, (float)0.30278578, (float)0.30590302,
    (float)0.30901700, (float)0.31212768, (float)0.31523499, (float)0.31833893,
    (float)0.32143945, (float)0.32453656, (float)0.32763019, (float)0.33072028,
    (float)0.33380687, (float)0.33688986, (float)0.33996925, (float)0.34304500,
    (float)0.34611708, (float)0.34918544, (float)0.35225007, (float)0.35531089,
    (float)0.35836795, (float)0.36142117, (float)0.36447051, (float)0.36751595,
    (float)0.37055743, (float)0.37359497, (float)0.37662852, (float)0.37965801,
    (float)0.38268346, (float)0.38570479, (float)0.38872197, (float)0.39173502,
    (float)0.39474389, (float)0.39774847, (float)0.40074885, (float)0.40374491,
    (float)0.40673664, (float)0.40972406, (float)0.41270703, (float)0.41568562,
    (float)0.41865975, (float)0.42162940, (float)0.42459452, (float)0.42755508,
    (float)0.43051112, (float)0.43346250, (float)0.43640924, (float)0.43935132,
    (float)0.44228873, (float)0.44522133, (float)0.44814920, (float)0.45107228,
    (float)0.45399052, (float)0.45690390, (float)0.45981237, (float)0.46271592,
    (float)0.46561453, (float)0.46850815, (float)0.47139674, (float)0.47428030,
    (float)0.47715878, (float)0.48003215, (float)0.48290035, (float)0.48576337,
    (float)0.48862126, (float)0.49147385, (float)0.49432120, (float)0.49716330,
    (float)0.50000000, (float)0.50283140, (float)0.50565743, (float)0.50847799,
    (float)0.51129311, (float)0.51410276, (float)0.51690692, (float)0.51970553,
    (float)0.52249855, (float)0.52528602, (float)0.52806789, (float)0.53084403,
    (float)0.53361452, (float)0.53637928, (float)0.53913832, (float)0.54189163,
    (float)0.54463905, (float)0.54738063, (float)0.55011642, (float)0.55284631,
    (float)0.55557024, (float)0.55828828, (float)0.56100029, (float)0.56370628,
    (float)0.56640625, (float)0.56910014, (float)0.57178795, (float)0.57446963,
    (float)0.57714522, (float)0.57981455, (float)0.58247769, (float)0.58513463,
    (float)0.58778524, (float)0.59042960, (float)0.59306765, (float)0.59569931,
    (float)0.59832460, (float)0.60094351, (float)0.60355598, (float)0.60616195,
    (float)0.60876143, (float)0.61135441, (float)0.61394083, (float)0.61652070,
    (float)0.61909395, (float)0.62166059, (float)0.62422055, (float)0.62677383,
    (float)0.62932038, (float)0.63186020, (float)0.63439333, (float)0.63691956,
    (float)0.63943899, (float)0.64195162, (float)0.64445734, (float)0.64695615,
    (float)0.64944810, (float)0.65193301, (float)0.65441096, (float)0.65688187,
    (float)0.65934587, (float)0.66180271, (float)0.66425246, (float)0.66669512,
    (float)0.66913062, (float)0.67155898, (float)0.67398012, (float)0.67639405,
    (float)0.67880076, (float)0.68120021, (float)0.68359232, (float)0.68597710,
    (float)0.68835455, (float)0.69072467, (float)0.69308740, (float)0.69544262,
    (float)0.69779050, (float)0.70013082, (float)0.70246369, (float)0.70478904,
    (float)0.70710677, (float)0.70941699, (float)0.71171963, (float)0.71401459,
    (float)0.71630198, (float)0.71858168, (float)0.72085363, (float)0.72311789,
    (float)0.72537440, (float)0.72762316, (float)0.72986406, (float)0.73209721,
    (float)0.73432255, (float)0.73653996, (float)0.73874950, (float)0.74095118,
    (float)0.74314487, (float)0.74533057, (float)0.74750835, (float)0.74967808,
    (float)0.75183982, (float)0.75399351, (float)0.75613910, (float)0.75827658,
    (float)0.76040596, (float)0.76252723, (float)0.76464027, (float)0.76674515,
    (float)0.76884186, (float)0.77093029, (float)0.77301043, (float)0.77508241,
    (float)0.77714598, (float)0.77920127, (float)0.78124821, (float)0.78328675,
    (float)0.78531694, (float)0.78733873, (float)0.78935206, (float)0.79135692,
    (float)0.79335338, (float)0.79534125, (float)0.79732066, (float)0.79929149,
    (float)0.80125386, (float)0.80320752, (float)0.80515265, (float)0.80708915,
    (float)0.80901700, (float)0.81093621, (float)0.81284672, (float)0.81474853,
    (float)0.81664157, (float)0.81852591, (float)0.82040149, (float)0.82226825,
    (float)0.82412618, (float)0.82597536, (float)0.82781565, (float)0.82964706,
    (float)0.83146966, (float)0.83328325, (float)0.83508795, (float)0.83688378,
    (float)0.83867061, (float)0.84044838, (float)0.84221727, (float)0.84397703,
    (float)0.84572780, (float)0.84746957, (float)0.84920216, (float)0.85092574,
    (float)0.85264021, (float)0.85434544, (float)0.85604161, (float)0.85772866,
    (float)0.85940641, (float)0.86107504, (float)0.86273444, (float)0.86438453,
    (float)0.86602545, (float)0.86765707, (float)0.86927933, (float)0.87089235,
    (float)0.87249607, (float)0.87409031, (float)0.87567532, (float)0.87725097,
    (float)0.87881714, (float)0.88037390, (float)0.88192129, (float)0.88345921,
    (float)0.88498765, (float)0.88650668, (float)0.88801610, (float)0.88951612,
    (float)0.89100653, (float)0.89248741, (float)0.89395881, (float)0.89542055,
    (float)0.89687276, (float)0.89831537, (float)0.89974827, (float)0.90117162,
    (float)0.90258533, (float)0.90398932, (float)0.90538365, (float)0.90676826,
    (float)0.90814316, (float)0.90950841, (float)0.91086388, (float)0.91220951,
    (float)0.91354549, (float)0.91487163, (float)0.91618794, (float)0.91749454,
    (float)0.91879123, (float)0.92007810, (float)0.92135513, (float)0.92262226,
    (float)0.92387950, (float)0.92512691, (float)0.92636442, (float)0.92759192,
    (float)0.92880958, (float)0.93001723, (float)0.93121493, (float)0.93240267,
    (float)0.93358046, (float)0.93474817, (float)0.93590593, (float)0.93705362,
    (float)0.93819135, (float)0.93931901, (float)0.94043654, (float)0.94154406,
    (float)0.94264150, (float)0.94372880, (float)0.94480604, (float)0.94587320,
    (float)0.94693011, (float)0.94797695, (float)0.94901365, (float)0.95004016,
    (float)0.95105654, (float)0.95206273, (float)0.95305866, (float)0.95404440,
    (float)0.95501995, (float)0.95598525, (float)0.95694035, (float)0.95788521,
    (float)0.95881975, (float)0.95974404, (float)0.96065807, (float)0.96156180,
    (float)0.96245527, (float)0.96333838, (float)0.96421117, (float)0.96507370,
    (float)0.96592581, (float)0.96676767, (float)0.96759909, (float)0.96842021,
    (float)0.96923089, (float)0.97003126, (float)0.97082120, (float)0.97160077,
    (float)0.97236991, (float)0.97312868, (float)0.97387701, (float)0.97461486,
    (float)0.97534233, (float)0.97605932, (float)0.97676587, (float)0.97746199,
    (float)0.97814763, (float)0.97882277, (float)0.97948742, (float)0.98014158,
    (float)0.98078531, (float)0.98141843, (float)0.98204112, (float)0.98265332,
    (float)0.98325491, (float)0.98384601, (float)0.98442656, (float)0.98499662,
    (float)0.98555607, (float)0.98610497, (float)0.98664331, (float)0.98717111,
    (float)0.98768836, (float)0.98819500, (float)0.98869103, (float)0.98917651,
    (float)0.98965138, (float)0.99011570, (float)0.99056935, (float)0.99101239,
    (float)0.99144489, (float)0.99186671, (float)0.99227792, (float)0.99267852,
    (float)0.99306846, (float)0.99344778, (float)0.99381649, (float)0.99417448,
    (float)0.99452192, (float)0.99485862, (float)0.99518472, (float)0.99550015,
    (float)0.99580491, (float)0.99609905, (float)0.99638247, (float)0.99665523,
    (float)0.99691731, (float)0.99716878, (float)0.99740952, (float)0.99763954,
    (float)0.99785894, (float)0.99806762, (float)0.99826562, (float)0.99845290,
    (float)0.99862951, (float)0.99879545, (float)0.99895066, (float)0.99909520,
    (float)0.99922901, (float)0.99935216, (float)0.99946457, (float)0.99956632,
    (float)0.99965733, (float)0.99973762, (float)0.99980724, (float)0.99986613,
    (float)0.99991435, (float)0.99995178, (float)0.99997860, (float)0.99999464,
    (float)1.00000000, (float)0.99999464, (float)0.99997860, (float)0.99995178,
    (float)0.99991435, (float)0.99986613, (float)0.99980724, (float)0.99973762,
    (float)0.99965733, (float)0.99956632, (float)0.99946457, (float)0.99935216,
    (float)0.99922901, (float)0.99909520, (float)0.99895066, (float)0.99879545,
    (float)0.99862951, (float)0.99845290, (float)0.99826562, (float)0.99806762,
    (float)0.99785894, (float)0.99763954, (float)0.99740946, (float)0.99716872,
    (float)0.99691731, (float)0.99665523, (float)0.99638247, (float)0.99609905,
    (float)0.99580491, (float)0.99550015, (float)0.99518472, (float)0.99485862,
    (float)0.99452192, (float)0.99417448, (float)0.99381644, (float)0.99344778,
    (float)0.99306846, (float)0.99267852, (float)0.99227792, (float)0.99186671,
    (float)0.99144489, (float)0.99101239, (float)0.99056935, (float)0.99011564,
    (float)0.98965138, (float)0.98917651, (float)0.98869103, (float)0.98819494,
    (float)0.98768836, (float)0.98717111, (float)0.98664331, (float)0.98610497,
    (float)0.98555607, (float)0.98499656, (float)0.98442656, (float)0.98384601,
    (float)0.98325491, (float)0.98265326, (float)0.98204112, (float)0.98141843,
    (float)0.98078525, (float)0.98014158, (float)0.97948742, (float)0.97882277,
    (float)0.97814757, (float)0.97746193, (float)0.97676587, (float)0.97605932,
    (float)0.97534227, (float)0.97461486, (float)0.97387695, (float)0.97312862,
    (float)0.97236991, (float)0.97160077, (float)0.97082120, (float)0.97003126,
    (float)0.96923089, (float)0.96842015, (float)0.96759909, (float)0.96676761,
    (float)0.96592581, (float)0.96507365, (float)0.96421117, (float)0.96333838,
    (float)0.96245521, (float)0.96156180, (float)0.96065807, (float)0.95974404,
    (float)0.95881969, (float)0.95788515, (float)0.95694029, (float)0.95598525,
    (float)0.95501995, (float)0.95404440, (float)0.95305860, (float)0.95206267,
    (float)0.95105648, (float)0.95004016, (float)0.94901365, (float)0.94797695,
    (float)0.94693011, (float)0.94587314, (float)0.94480604, (float)0.94372880,
    (float)0.94264150, (float)0.94154406, (float)0.94043654, (float)0.93931895,
    (float)0.93819129, (float)0.93705362, (float)0.93590593, (float)0.93474817,
    (float)0.93358046, (float)0.93240267, (float)0.93121493, (float)0.93001723,
    (float)0.92880952, (float)0.92759192, (float)0.92636436, (float)0.92512691,
    (float)0.92387950, (float)0.92262226, (float)0.92135507, (float)0.92007804,
    (float)0.91879123, (float)0.91749448, (float)0.91618794, (float)0.91487157,
    (float)0.91354543, (float)0.91220951, (float)0.91086382, (float)0.90950835,
    (float)0.90814310, (float)0.90676820, (float)0.90538365, (float)0.90398932,
    (float)0.90258527, (float)0.90117157, (float)0.89974827, (float)0.89831525,
    (float)0.89687276, (float)0.89542055, (float)0.89395875, (float)0.89248741,
    (float)0.89100647, (float)0.88951600, (float)0.88801610, (float)0.88650662,
    (float)0.88498759, (float)0.88345915, (float)0.88192123, (float)0.88037384,
    (float)0.87881714, (float)0.87725091, (float)0.87567532, (float)0.87409031,
    (float)0.87249595, (float)0.87089223, (float)0.86927933, (float)0.86765701,
    (float)0.86602539, (float)0.86438447, (float)0.86273432, (float)0.86107504,
    (float)0.85940641, (float)0.85772860, (float)0.85604161, (float)0.85434544,
    (float)0.85264009, (float)0.85092574, (float)0.84920216, (float)0.84746951,
    (float)0.84572780, (float)0.84397697, (float)0.84221715, (float)0.84044844,
    (float)0.83867055, (float)0.83688372, (float)0.83508795, (float)0.83328319,
    (float)0.83146954, (float)0.82964706, (float)0.82781565, (float)0.82597530,
    (float)0.82412612, (float)0.82226813, (float)0.82040137, (float)0.81852591,
    (float)0.81664157, (float)0.81474847, (float)0.81284660, (float)0.81093609,
    (float)0.80901700, (float)0.80708915, (float)0.80515265, (float)0.80320752,
    (float)0.80125374, (float)0.79929143, (float)0.79732066, (float)0.79534125,
    (float)0.79335332, (float)0.79135686, (float)0.78935200, (float)0.78733861,
    (float)0.78531694, (float)0.78328675, (float)0.78124815, (float)0.77920121,
    (float)0.77714586, (float)0.77508223, (float)0.77301049, (float)0.77093029,
    (float)0.76884180, (float)0.76674509, (float)0.76464021, (float)0.76252711,
    (float)0.76040596, (float)0.75827658, (float)0.75613904, (float)0.75399339,
    (float)0.75183970, (float)0.74967796, (float)0.74750835, (float)0.74533057,
    (float)0.74314481, (float)0.74095106, (float)0.73874938, (float)0.73653996,
    (float)0.73432249, (float)0.73209721, (float)0.72986400, (float)0.72762305,
    (float)0.72537428, (float)0.72311789, (float)0.72085363, (float)0.71858162,
    (float)0.71630186, (float)0.71401453, (float)0.71171951, (float)0.70941705,
    (float)0.70710677, (float)0.70478898, (float)0.70246363, (float)0.70013070,
    (float)0.69779032, (float)0.69544268, (float)0.69308734, (float)0.69072461,
    (float)0.68835449, (float)0.68597704, (float)0.68359220, (float)0.68120021,
    (float)0.67880070, (float)0.67639399, (float)0.67398006, (float)0.67155886,
    (float)0.66913044, (float)0.66669512, (float)0.66425240, (float)0.66180259,
    (float)0.65934575, (float)0.65688181, (float)0.65441096, (float)0.65193301,
    (float)0.64944804, (float)0.64695609, (float)0.64445722, (float)0.64195150,
    (float)0.63943905, (float)0.63691956, (float)0.63439327, (float)0.63186014,
    (float)0.62932026, (float)0.62677372, (float)0.62422055, (float)0.62166059,
    (float)0.61909389, (float)0.61652064, (float)0.61394072, (float)0.61135429,
    (float)0.60876143, (float)0.60616189, (float)0.60355592, (float)0.60094339,
    (float)0.59832448, (float)0.59569913, (float)0.59306765, (float)0.59042960,
    (float)0.58778518, (float)0.58513451, (float)0.58247757, (float)0.57981461,
    (float)0.57714522, (float)0.57446963, (float)0.57178789, (float)0.56910002,
    (float)0.56640613, (float)0.56370628, (float)0.56100023, (float)0.55828822,
    (float)0.55557019, (float)0.55284619, (float)0.55011630, (float)0.54738069,
    (float)0.54463905, (float)0.54189152, (float)0.53913826, (float)0.53637916,
    (float)0.53361434, (float)0.53084403, (float)0.52806783, (float)0.52528596,
    (float)0.52249849, (float)0.51970541, (float)0.51690674, (float)0.51410276,
    (float)0.51129305, (float)0.50847787, (float)0.50565726, (float)0.50283122,
    (float)0.50000006, (float)0.49716327, (float)0.49432117, (float)0.49147379,
    (float)0.48862115, (float)0.48576325, (float)0.48290038, (float)0.48003212,
    (float)0.47715873, (float)0.47428021, (float)0.47139663, (float)0.46850798,
    (float)0.46561456, (float)0.46271589, (float)0.45981231, (float)0.45690379,
    (float)0.45399037, (float)0.45107210, (float)0.44814920, (float)0.44522130,
    (float)0.44228864, (float)0.43935123, (float)0.43640912, (float)0.43346232,
    (float)0.43051112, (float)0.42755505, (float)0.42459446, (float)0.42162928,
    (float)0.41865960, (float)0.41568545, (float)0.41270703, (float)0.40972400,
    (float)0.40673658, (float)0.40374479, (float)0.40074870, (float)0.39774850,
    (float)0.39474386, (float)0.39173496, (float)0.38872188, (float)0.38570464,
    (float)0.38268328, (float)0.37965804, (float)0.37662849, (float)0.37359491,
    (float)0.37055734, (float)0.36751580, (float)0.36447033, (float)0.36142117,
    (float)0.35836792, (float)0.35531086, (float)0.35224995, (float)0.34918529,
    (float)0.34611690, (float)0.34304500, (float)0.33996922, (float)0.33688980,
    (float)0.33380675, (float)0.33072016, (float)0.32763001, (float)0.32453656,
    (float)0.32143945, (float)0.31833887, (float)0.31523487, (float)0.31212750,
    (float)0.30901679, (float)0.30590302, (float)0.30278572, (float)0.29966521,
    (float)0.29654145, (float)0.29341453, (float)0.29028472, (float)0.28715155,
    (float)0.28401530, (float)0.28087601, (float)0.27773371, (float)0.27458847,
    (float)0.27144048, (float)0.26828936, (float)0.26513538, (float)0.26197854,
    (float)0.25881892, (float)0.25565651, (float)0.25249159, (float)0.24932374,
    (float)0.24615324, (float)0.24298008, (float)0.23980433, (float)0.23662600,
    (float)0.23344538, (float)0.23026201, (float)0.22707619, (float)0.22388794,
    (float)0.22069728, (float)0.21750426, (float)0.21430916, (float)0.21111152,
    (float)0.20791161, (float)0.20470949, (float)0.20150517, (float)0.19829892,
    (float)0.19509031, (float)0.19187963, (float)0.18866688, (float)0.18545210,
    (float)0.18223536, (float)0.17901689, (float)0.17579627, (float)0.17257376,
    (float)0.16934940, (float)0.16612324, (float)0.16289529, (float)0.15966584,
    (float)0.15643445, (float)0.15320137, (float)0.14996666, (float)0.14673033,
    (float)0.14349243, (float)0.14025325, (float)0.13701232, (float)0.13376991,
    (float)0.13052608, (float)0.12728085, (float)0.12403426, (float)0.12078657,
    (float)0.11753736, (float)0.11428688, (float)0.11103519, (float)0.10778230,
    (float)0.10452849, (float)0.10127334, (float)0.09801710, (float)0.09475980,
    (float)0.09150149, (float)0.08824220, (float)0.08498220, (float)0.08172106,
    (float)0.07845904, (float)0.07519618, (float)0.07193252, (float)0.06866808,
    (float)0.06540315, (float)0.06213728, (float)0.05887074, (float)0.05560357,
    (float)0.05233581, (float)0.04906749, (float)0.04579888, (float)0.04252954,
    (float)0.03925974, (float)0.03598953, (float)0.03271893, (float)0.02944798,
    (float)0.02617695, (float)0.02290541, (float)0.01963361, (float)0.01636161,
    (float)0.01308943, (float)0.00981712, (float)0.00654493, (float)0.00327244,
    (float)0.00000000, (float)0.00000000, (float)0.00000000, (float)0.00000000,
    (float)0.00000000, (float)0.00000000, (float)0.00000000, (float)0.00000000,
    (float)0.00000000, (float)0.00000000, (float)0.00000000, (float)0.00000000,
    (float)0.00000000, (float)0.00000000, (float)0.00000000, (float)0.00000000,
    (float)0.00000000, (float)0.00000000, (float)0.00000000, (float)0.00000000,
    (float)0.00000000, (float)0.00000000, (float)0.00000000, (float)0.00000000,
    (float)0.00000000, (float)0.00000000, (float)0.00000000, (float)0.00000000,
    (float)0.00000000, (float)0.00000000, (float)0.00000000, (float)0.00000000};

#endif  // MODULES_AUDIO_PROCESSING_NS_MAIN_SOURCE_WINDOWS_PRIVATE_H_
