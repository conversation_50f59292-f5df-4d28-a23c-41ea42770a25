# Copyright (c) 2018 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("../../../webrtc.gni")

config("bwe_test_logging") {
  if (rtc_enable_bwe_test_logging) {
    defines = [ "BWE_TEST_LOGGING_COMPILE_TIME_ENABLE=1" ]
  } else {
    defines = [ "BWE_TEST_LOGGING_COMPILE_TIME_ENABLE=0" ]
  }
}

rtc_static_library("goog_cc") {
  configs += [ ":bwe_test_logging" ]
  sources = [
    "goog_cc_network_control.cc",
    "goog_cc_network_control.h",
    "overuse_predictor.cc",
    "overuse_predictor.h",
  ]

  deps = [
    ":alr_detector",
    ":delay_based_bwe",
    ":estimators",
    ":probe_controller",
    ":pushback_controller",
    "../..:module_api",
    "../../..:webrtc_common",
    "../../../api:network_state_predictor_api",
    "../../../api/transport:field_trial_based_config",
    "../../../api/transport:network_control",
    "../../../api/transport:webrtc_key_value_config",
    "../../../api/units:data_rate",
    "../../../api/units:data_size",
    "../../../api/units:time_delta",
    "../../../api/units:timestamp",
    "../../../logging:rtc_event_log_api",
    "../../../logging:rtc_event_pacing",
    "../../../rtc_base:checks",
    "../../../rtc_base:logging",
    "../../../rtc_base:macromagic",
    "../../../rtc_base/experiments:alr_experiment",
    "../../../rtc_base/experiments:field_trial_parser",
    "../../../rtc_base/experiments:rate_control_settings",
    "../../../system_wrappers",
    "../../bitrate_controller",
    "../../remote_bitrate_estimator",
    "../../rtp_rtcp:rtp_rtcp_format",
    "//third_party/abseil-cpp/absl/memory",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_source_set("link_capacity_estimator") {
  sources = [
    "link_capacity_estimator.cc",
    "link_capacity_estimator.h",
  ]
  deps = [
    "../../../api/units:data_rate",
    "../../../rtc_base:safe_minmax",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_source_set("pushback_controller") {
  sources = [
    "congestion_window_pushback_controller.cc",
    "congestion_window_pushback_controller.h",
  ]
  deps = [
    "../../../api/transport:network_control",
    "../../../api/transport:webrtc_key_value_config",
    "../../../api/units:data_size",
    "../../../rtc_base:checks",
    "../../../rtc_base/experiments:rate_control_settings",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_source_set("alr_detector") {
  sources = [
    "alr_detector.cc",
    "alr_detector.h",
  ]
  deps = [
    "../../../api/transport:field_trial_based_config",
    "../../../api/transport:webrtc_key_value_config",
    "../../../logging:rtc_event_log_api",
    "../../../logging:rtc_event_pacing",
    "../../../rtc_base:checks",
    "../../../rtc_base:safe_conversions",
    "../../../rtc_base:timeutils",
    "../../../rtc_base/experiments:alr_experiment",
    "../../../rtc_base/experiments:field_trial_parser",
    "../../pacing:interval_budget",
    "//third_party/abseil-cpp/absl/memory",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}
rtc_source_set("estimators") {
  configs += [ ":bwe_test_logging" ]
  sources = [
    "acknowledged_bitrate_estimator.cc",
    "acknowledged_bitrate_estimator.h",
    "bitrate_estimator.cc",
    "bitrate_estimator.h",
    "delay_increase_detector_interface.h",
    "median_slope_estimator.cc",
    "median_slope_estimator.h",
    "probe_bitrate_estimator.cc",
    "probe_bitrate_estimator.h",
    "trendline_estimator.cc",
    "trendline_estimator.h",
  ]

  deps = [
    "../../../api:network_state_predictor_api",
    "../../../api/transport:webrtc_key_value_config",
    "../../../api/units:data_rate",
    "../../../logging:rtc_event_bwe",
    "../../../logging:rtc_event_log_api",
    "../../../rtc_base:checks",
    "../../../rtc_base:logging",
    "../../../rtc_base:macromagic",
    "../../../rtc_base:rtc_numerics",
    "../../../rtc_base:safe_conversions",
    "../../../rtc_base:safe_minmax",
    "../../../rtc_base/experiments:field_trial_parser",
    "../../remote_bitrate_estimator",
    "../../rtp_rtcp:rtp_rtcp_format",
    "//third_party/abseil-cpp/absl/memory",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_source_set("delay_based_bwe") {
  configs += [ ":bwe_test_logging" ]
  sources = [
    "delay_based_bwe.cc",
    "delay_based_bwe.h",
  ]

  deps = [
    ":estimators",
    "../../../api:network_state_predictor_api",
    "../../../api/transport:network_control",
    "../../../api/transport:webrtc_key_value_config",
    "../../../logging:rtc_event_bwe",
    "../../../logging:rtc_event_log_api",
    "../../../rtc_base:checks",
    "../../../rtc_base:rtc_base_approved",
    "../../../rtc_base/experiments:field_trial_parser",
    "../../../system_wrappers:metrics",
    "../../pacing",
    "../../remote_bitrate_estimator",
    "../../rtp_rtcp:rtp_rtcp_format",
    "//third_party/abseil-cpp/absl/memory",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_source_set("probe_controller") {
  sources = [
    "probe_controller.cc",
    "probe_controller.h",
  ]

  deps = [
    "../../../api/transport:network_control",
    "../../../api/transport:webrtc_key_value_config",
    "../../../api/units:data_rate",
    "../../../api/units:time_delta",
    "../../../api/units:timestamp",
    "../../../logging:rtc_event_bwe",
    "../../../logging:rtc_event_log_api",
    "../../../logging:rtc_event_pacing",
    "../../../rtc_base:checks",
    "../../../rtc_base:logging",
    "../../../rtc_base:macromagic",
    "../../../rtc_base:safe_conversions",
    "../../../rtc_base/experiments:field_trial_parser",
    "../../../rtc_base/system:unused",
    "../../../system_wrappers:metrics",
    "//third_party/abseil-cpp/absl/memory",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

if (rtc_include_tests) {
  rtc_source_set("test_goog_cc_printer") {
    testonly = true
    sources = [
      "test/goog_cc_printer.cc",
      "test/goog_cc_printer.h",
    ]
    deps = [
      ":alr_detector",
      ":delay_based_bwe",
      ":estimators",
      ":goog_cc",
      "../../../api/transport:goog_cc",
      "../../../api/transport:network_control",
      "../../../api/units:timestamp",
      "../../../logging:rtc_event_log_api",
      "../../../rtc_base:checks",
      "../../../test/logging:log_writer",
      "../../remote_bitrate_estimator",
      "//third_party/abseil-cpp/absl/types:optional",
    ]
  }
  rtc_source_set("goog_cc_unittests") {
    testonly = true

    sources = [
      "acknowledged_bitrate_estimator_unittest.cc",
      "alr_detector_unittest.cc",
      "congestion_window_pushback_controller_unittest.cc",
      "delay_based_bwe_unittest.cc",
      "delay_based_bwe_unittest_helper.cc",
      "delay_based_bwe_unittest_helper.h",
      "goog_cc_network_control_unittest.cc",
      "median_slope_estimator_unittest.cc",
      "probe_bitrate_estimator_unittest.cc",
      "probe_controller_unittest.cc",
      "trendline_estimator_unittest.cc",
    ]
    deps = [
      ":alr_detector",
      ":delay_based_bwe",
      ":estimators",
      ":goog_cc",
      ":probe_controller",
      ":pushback_controller",
      "../../../api/transport:field_trial_based_config",
      "../../../api/transport:goog_cc",
      "../../../api/transport:network_control",
      "../../../api/transport:network_control_test",
      "../../../api/transport:webrtc_key_value_config",
      "../../../api/units:data_rate",
      "../../../api/units:timestamp",
      "../../../logging:mocks",
      "../../../rtc_base:checks",
      "../../../rtc_base:rtc_base_approved",
      "../../../rtc_base:rtc_base_tests_utils",
      "../../../rtc_base/experiments:alr_experiment",
      "../../../system_wrappers",
      "../../../test:field_trial",
      "../../../test:test_support",
      "../../../test/scenario",
      "../../pacing",
      "../../remote_bitrate_estimator",
      "../../rtp_rtcp:rtp_rtcp_format",
      "//testing/gmock",
      "//third_party/abseil-cpp/absl/memory",
    ]
  }

  # TODO(srte): Remove this target when dependency in root BUILD is gone.
  rtc_source_set("goog_cc_slow_tests") {
  }
}
