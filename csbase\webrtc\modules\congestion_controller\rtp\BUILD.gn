# Copyright (c) 2014 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("../../../webrtc.gni")

config("bwe_test_logging") {
  if (rtc_enable_bwe_test_logging) {
    defines = [ "BWE_TEST_LOGGING_COMPILE_TIME_ENABLE=1" ]
  } else {
    defines = [ "BWE_TEST_LOGGING_COMPILE_TIME_ENABLE=0" ]
  }
}

rtc_source_set("control_handler") {
  visibility = [ "*" ]
  sources = [
    "control_handler.cc",
    "control_handler.h",
  ]

  deps = [
    "../:congestion_controller",
    "../../../api/transport:network_control",
    "../../../api/units:data_rate",
    "../../../api/units:data_size",
    "../../../api/units:time_delta",
    "../../../rtc_base:checks",
    "../../../rtc_base:rate_limiter",
    "../../../rtc_base:safe_minmax",
    "../../../rtc_base/synchronization:sequence_checker",
    "../../../system_wrappers",
    "../../../system_wrappers:field_trial",
    "../../pacing",
    "../../remote_bitrate_estimator",
    "../../rtp_rtcp:rtp_rtcp_format",
    "//third_party/abseil-cpp/absl/memory",
    "//third_party/abseil-cpp/absl/types:optional",
  ]

  if (!build_with_mozilla) {
    deps += [ "../../../rtc_base" ]
  }
}
rtc_static_library("transport_feedback") {
  visibility = [ "*" ]
  sources = [
    "send_time_history.cc",
    "send_time_history.h",
    "transport_feedback_adapter.cc",
    "transport_feedback_adapter.h",
  ]

  deps = [
    "../..:module_api",
    "../../../api/transport:network_control",
    "../../../api/units:data_size",
    "../../../api/units:timestamp",
    "../../../rtc_base:checks",
    "../../../rtc_base:rtc_base_approved",
    "../../../rtc_base/network:sent_packet",
    "../../../system_wrappers",
    "../../../system_wrappers:field_trial",
    "../../rtp_rtcp:rtp_rtcp_format",
  ]
}

if (rtc_include_tests) {
  rtc_source_set("congestion_controller_unittests") {
    testonly = true

    sources = [
      "congestion_controller_unittests_helper.cc",
      "congestion_controller_unittests_helper.h",
      "send_time_history_unittest.cc",
      "transport_feedback_adapter_unittest.cc",
    ]
    deps = [
      ":transport_feedback",
      "../:congestion_controller",
      "../:mock_congestion_controller",
      "../../../api/transport:network_control",
      "../../../logging:mocks",
      "../../../rtc_base",
      "../../../rtc_base:checks",
      "../../../rtc_base:rtc_base_approved",
      "../../../rtc_base/network:sent_packet",
      "../../../system_wrappers",
      "../../../test:field_trial",
      "../../../test:test_support",
      "../../pacing",
      "../../pacing:mock_paced_sender",
      "../../remote_bitrate_estimator",
      "../../rtp_rtcp:rtp_rtcp_format",
      "//testing/gmock",
    ]
  }
}
