# Copyright (c) 2014 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("//build/config/linux/pkg_config.gni")
import("//build/config/ui.gni")
import("//tools/generate_stubs/rules.gni")
import("../../webrtc.gni")

use_desktop_capture_differ_sse2 = current_cpu == "x86" || current_cpu == "x64"

rtc_static_library("primitives") {
  visibility = [ "*" ]
  sources = [
    "desktop_capture_types.h",
    "desktop_frame.cc",
    "desktop_frame.h",
    "desktop_geometry.cc",
    "desktop_geometry.h",
    "desktop_region.cc",
    "desktop_region.h",
    "shared_desktop_frame.cc",
    "shared_desktop_frame.h",
    "shared_memory.cc",
    "shared_memory.h",
  ]

  deps = [
    "../../api:scoped_refptr",
    "../../rtc_base:checks",
    "../../rtc_base/system:rtc_export",
    "//third_party/abseil-cpp/absl/memory",
  ]

  if (!build_with_mozilla) {
    deps += [ "../../rtc_base" ]  # TODO(kjellander): Cleanup in bugs.webrtc.org/3806.
  }
}

if (rtc_include_tests) {
  rtc_source_set("desktop_capture_modules_tests") {
    testonly = true

    sources = []
    deps = [
      "../../api:function_view",
      "../../api:scoped_refptr",
      "../../rtc_base:checks",
      "//third_party/abseil-cpp/absl/memory",
    ]
    if (rtc_desktop_capture_supported) {
      deps += [
        ":desktop_capture_mock",
        ":primitives",
        ":screen_drawer",
        "../../rtc_base",
        "../../rtc_base:rtc_base_approved",
        "../../rtc_base/third_party/base64",
        "../../system_wrappers",
        "../../test:test_support",
        "../../test:video_test_support",
      ]
      sources += [
        "screen_capturer_integration_test.cc",
        "screen_drawer_unittest.cc",
        "window_finder_unittest.cc",
      ]
    }
  }

  rtc_source_set("desktop_capture_unittests") {
    testonly = true

    sources = [
      "blank_detector_desktop_capturer_wrapper_unittest.cc",
      "cropped_desktop_frame_unittest.cc",
      "desktop_and_cursor_composer_unittest.cc",
      "desktop_capturer_differ_wrapper_unittest.cc",
      "desktop_frame_rotation_unittest.cc",
      "desktop_geometry_unittest.cc",
      "desktop_region_unittest.cc",
      "differ_block_unittest.cc",
      "fallback_desktop_capturer_wrapper_unittest.cc",
      "mouse_cursor_monitor_unittest.cc",
      "rgba_color_unittest.cc",
      "test_utils.cc",
      "test_utils.h",
      "test_utils_unittest.cc",
      "win/cursor_unittest.cc",
      "win/cursor_unittest_resources.h",
      "win/cursor_unittest_resources.rc",
      "win/screen_capture_utils_unittest.cc",
      "win/screen_capturer_win_directx_unittest.cc",
    ]
    deps = [
      ":desktop_capture",
      ":desktop_capture_mock",
      ":primitives",
      "../../rtc_base:checks",
      "../../rtc_base:rtc_base_approved",
      "../../system_wrappers:cpu_features_api",
      "../../test:test_support",
      "//third_party/abseil-cpp/absl/memory",
    ]
    if (rtc_desktop_capture_supported) {
      sources += [
        "screen_capturer_helper_unittest.cc",
        "screen_capturer_mac_unittest.cc",
        "screen_capturer_unittest.cc",
        "window_capturer_unittest.cc",
      ]
      deps += [ ":desktop_capture_mock" ]
    }
  }

  rtc_source_set("screen_drawer") {
    testonly = true

    public_deps = [
      ":desktop_capture",
    ]

    sources = [
      "screen_drawer.cc",
      "screen_drawer.h",
      "screen_drawer_linux.cc",
      "screen_drawer_mac.cc",
      "screen_drawer_win.cc",
    ]

    deps = [
      ":primitives",
      "../../api:scoped_refptr",
      "../../rtc_base:checks",
      "../../rtc_base:rtc_base_approved",
      "../../system_wrappers",
      "//third_party/abseil-cpp/absl/memory",
    ]

    if (is_posix || is_fuchsia) {
      sources += [
        "screen_drawer_lock_posix.cc",
        "screen_drawer_lock_posix.h",
      ]
    }
  }

  rtc_source_set("desktop_capture_mock") {
    testonly = true

    public_deps = [
      ":desktop_capture",
    ]

    sources = [
      "mock_desktop_capturer_callback.cc",
      "mock_desktop_capturer_callback.h",
    ]

    deps = [
      ":primitives",
      "../../rtc_base:rtc_base_approved",
      "../../test:test_support",
    ]
  }
}

if (is_linux) {
  if (rtc_use_pipewire) {
    pkg_config("gio") {
      packages = [
        "gio-2.0",
        "gio-unix-2.0",
      ]
    }

    if (rtc_link_pipewire) {
      pkg_config("pipewire") {
        packages = [ "libpipewire-0.2" ]
      }
    } else {
      # When libpipewire is not directly linked, use stubs to allow for dlopening of
      # the binary.
      generate_stubs("pipewire_stubs") {
        configs = [ "../../:common_config" ]
        deps = [
          "../../rtc_base",
        ]
        extra_header = "linux/pipewire_stub_header.fragment"
        logging_function = "RTC_LOG(LS_VERBOSE)"
        logging_include = "rtc_base/logging.h"
        output_name = "linux/pipewire_stubs"
        path_from_source = "modules/desktop_capture/linux"
        sigs = [ "linux/pipewire.sigs" ]
      }
    }

    config("pipewire_config") {
      defines = [ "WEBRTC_USE_PIPEWIRE" ]
      if (!rtc_link_pipewire) {
        defines += [ "WEBRTC_DLOPEN_PIPEWIRE" ]
      }
    }
  }
}

rtc_source_set("desktop_capture") {
  visibility = [ "*" ]
  public_deps = [
    ":desktop_capture_generic",
  ]
  if (is_mac) {
    public_deps += [ ":desktop_capture_objc" ]
  }
}

if (is_mac) {
  rtc_source_set("desktop_capture_objc") {
    visibility = [ ":desktop_capture" ]
    sources = [
      "mac/desktop_configuration.mm",
      "mac/desktop_frame_cgimage.h",
      "mac/desktop_frame_cgimage.mm",
      "mac/desktop_frame_iosurface.h",
      "mac/desktop_frame_iosurface.mm",
      "mac/desktop_frame_provider.h",
      "mac/desktop_frame_provider.mm",
      "mac/screen_capturer_mac.h",
      "mac/screen_capturer_mac.mm",
      "mouse_cursor_monitor_mac.mm",
      "screen_capturer_darwin.mm",
      "window_capturer_mac.mm",
      "window_finder_mac.h",
      "window_finder_mac.mm",
    ]
    deps = [
      ":desktop_capture_generic",
      ":primitives",
      "../../api:scoped_refptr",
      "../../rtc_base",
      "../../rtc_base:checks",
      "../../rtc_base:rtc_base_approved",
      "../../rtc_base/synchronization:rw_lock_wrapper",
      "../../sdk:helpers_objc",
      "//third_party/abseil-cpp/absl/memory",
    ]
    libs = [
      "AppKit.framework",
      "IOKit.framework",
      "IOSurface.framework",
    ]
  }
}

rtc_static_library("desktop_capture_generic") {
  visibility = [
    ":desktop_capture",
    ":desktop_capture_objc",
  ]
  sources = [
    "blank_detector_desktop_capturer_wrapper.cc",
    "blank_detector_desktop_capturer_wrapper.h",
    "capture_result_desktop_capturer_wrapper.cc",
    "capture_result_desktop_capturer_wrapper.h",
    "cropped_desktop_frame.cc",
    "cropped_desktop_frame.h",
    "cropping_window_capturer.cc",
    "cropping_window_capturer.h",
    "cropping_window_capturer_win.cc",
    "desktop_and_cursor_composer.cc",
    "desktop_and_cursor_composer.h",
    "desktop_capture_options.cc",
    "desktop_capture_options.h",
    "desktop_capturer.cc",
    "desktop_capturer.h",
    "desktop_capturer_differ_wrapper.cc",
    "desktop_capturer_differ_wrapper.h",
    "desktop_capturer_wrapper.cc",
    "desktop_capturer_wrapper.h",
    "desktop_frame_generator.cc",
    "desktop_frame_generator.h",
    "desktop_frame_rotation.cc",
    "desktop_frame_rotation.h",
    "desktop_frame_win.cc",
    "desktop_frame_win.h",
    "differ_block.cc",
    "differ_block.h",
    "fake_desktop_capturer.cc",
    "fake_desktop_capturer.h",
    "fallback_desktop_capturer_wrapper.cc",
    "fallback_desktop_capturer_wrapper.h",
    "mac/desktop_configuration.h",
    "mac/desktop_configuration_monitor.cc",
    "mac/desktop_configuration_monitor.h",
    "mac/full_screen_chrome_window_detector.cc",
    "mac/full_screen_chrome_window_detector.h",
    "mac/window_list_utils.cc",
    "mac/window_list_utils.h",
    "mouse_cursor.cc",
    "mouse_cursor.h",
    "mouse_cursor_monitor.h",
    "mouse_cursor_monitor_win.cc",
    "resolution_tracker.cc",
    "resolution_tracker.h",
    "rgba_color.cc",
    "rgba_color.h",
    "screen_capture_frame_queue.h",
    "screen_capturer_helper.cc",
    "screen_capturer_helper.h",
    "screen_capturer_win.cc",
    "win/cursor.cc",
    "win/cursor.h",
    "win/d3d_device.cc",
    "win/d3d_device.h",
    "win/desktop.cc",
    "win/desktop.h",
    "win/display_configuration_monitor.cc",
    "win/display_configuration_monitor.h",
    "win/dxgi_adapter_duplicator.cc",
    "win/dxgi_adapter_duplicator.h",
    "win/dxgi_context.cc",
    "win/dxgi_context.h",
    "win/dxgi_duplicator_controller.cc",
    "win/dxgi_duplicator_controller.h",
    "win/dxgi_frame.cc",
    "win/dxgi_frame.h",
    "win/dxgi_output_duplicator.cc",
    "win/dxgi_output_duplicator.h",
    "win/dxgi_texture.cc",
    "win/dxgi_texture.h",
    "win/dxgi_texture_mapping.cc",
    "win/dxgi_texture_mapping.h",
    "win/dxgi_texture_staging.cc",
    "win/dxgi_texture_staging.h",
    "win/scoped_gdi_object.h",
    "win/scoped_thread_desktop.cc",
    "win/scoped_thread_desktop.h",
    "win/screen_capture_utils.cc",
    "win/screen_capture_utils.h",
    "win/screen_capturer_win_directx.cc",
    "win/screen_capturer_win_directx.h",
    "win/screen_capturer_win_gdi.cc",
    "win/screen_capturer_win_gdi.h",
    "win/screen_capturer_win_magnifier.cc",
    "win/screen_capturer_win_magnifier.h",
    "win/window_capture_utils.cc",
    "win/window_capture_utils.h",
    "window_capturer_win.cc",
    "window_finder.cc",
    "window_finder.h",
    "window_finder_win.cc",
    "window_finder_win.h",
  ]

  if (build_with_mozilla) {
    sources += [
      "app_capturer_win.cc",
      "desktop_device_info.cc",
      "desktop_device_info.h",
      "win/desktop_device_info_win.cc",
      "win/win_shared.cc",
    ]
  }

  if (rtc_use_x11 || rtc_use_pipewire) {
    sources += [
      "mouse_cursor_monitor_linux.cc",
      "screen_capturer_linux.cc",
      "window_capturer_linux.cc",
    ]

    if (build_with_mozilla) {
      sources += [ "app_capturer_linux.cc" ]
    }
  }

  if (rtc_use_x11) {
    sources += [
      "linux/mouse_cursor_monitor_x11.cc",
      "linux/mouse_cursor_monitor_x11.h",
      "linux/screen_capturer_x11.cc",
      "linux/screen_capturer_x11.h",
      "linux/shared_x_display.cc",
      "linux/shared_x_display.h",
      "linux/window_capturer_x11.cc",
      "linux/window_capturer_x11.h",
      "linux/window_finder_x11.cc",
      "linux/window_finder_x11.h",
      "linux/window_list_utils.cc",
      "linux/window_list_utils.h",
      "linux/x_atom_cache.cc",
      "linux/x_atom_cache.h",
      "linux/x_error_trap.cc",
      "linux/x_error_trap.h",
      "linux/x_server_pixel_buffer.cc",
      "linux/x_server_pixel_buffer.h",
      "linux/x_window_property.cc",
      "linux/x_window_property.h",
    ]
    configs += [ "//build/config/linux:x11" ]

    if (build_with_mozilla) {
      sources += [
        "linux/app_capturer_x11.cc",
        "linux/desktop_device_info_linux.cc",
        "linux/desktop_device_info_linux.h",
        "linux/shared_x_util.cc",
        "linux/shared_x_util.h",
      ]
    }
  }

  if (!is_win && !is_mac && !rtc_use_x11 && !rtc_use_pipewire) {
    sources += [
      "mouse_cursor_monitor_null.cc",
      "screen_capturer_null.cc",
      "window_capturer_null.cc",
    ]
  }

  if (is_win) {
    libs = [
      "d3d11.lib",
      "dxgi.lib",
    ]
  }

  deps = [
    ":primitives",
    "../../api:function_view",
    "../../api:refcountedbase",
    "../../api:scoped_refptr",
    "../../rtc_base",  # TODO(kjellander): Cleanup in bugs.webrtc.org/3806.
    "../../rtc_base:checks",
    "../../rtc_base/synchronization:rw_lock_wrapper",
    "../../rtc_base/system:arch",
    "../../rtc_base/system:rtc_export",
    "../../system_wrappers",
    "../../system_wrappers:cpu_features_api",
    "../../system_wrappers:metrics",
    "//third_party/abseil-cpp/absl/memory",
  ]

  if (build_with_mozilla) {
    deps += [ "../../rtc_base:rtc_base_approved" ]
  } else {
    deps += [ "//third_party/libyuv" ]
  }

  if (use_desktop_capture_differ_sse2) {
    deps += [ ":desktop_capture_differ_sse2" ]
  }

  if (rtc_use_pipewire) {
    sources += [
      "linux/base_capturer_pipewire.cc",
      "linux/base_capturer_pipewire.h",
      "linux/screen_capturer_pipewire.cc",
      "linux/screen_capturer_pipewire.h",
      "linux/window_capturer_pipewire.cc",
      "linux/window_capturer_pipewire.h",
    ]

    configs += [
      ":pipewire_config",
      ":gio",
    ]

    if (rtc_link_pipewire) {
      configs += [ ":pipewire" ]
    } else {
      deps += [ ":pipewire_stubs" ]
    }
  }
}

if (use_desktop_capture_differ_sse2) {
  # Have to be compiled as a separate target because it needs to be compiled
  # with SSE2 enabled.
  rtc_static_library("desktop_capture_differ_sse2") {
    visibility = [ ":*" ]
    sources = [
      "differ_vector_sse2.cc",
      "differ_vector_sse2.h",
    ]

    if (is_posix || is_fuchsia) {
      cflags = [ "-msse2" ]
    }
  }
}
