# Copyright (c) 2014 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("../../webrtc.gni")

rtc_static_library("pacing") {
  # Client code SHOULD NOT USE THIS TARGET, but for now it needs to be public
  # because there exists client code that uses it.
  # TODO(bugs.webrtc.org/9808): Move to private visibility as soon as that
  # client code gets updated.
  visibility = [ "*" ]
  sources = [
    "bitrate_prober.cc",
    "bitrate_prober.h",
    "paced_sender.cc",
    "paced_sender.h",
    "pacer.h",
    "packet_router.cc",
    "packet_router.h",
    "round_robin_packet_queue.cc",
    "round_robin_packet_queue.h",
  ]

  deps = [
    ":interval_budget",
    "..:module_api",
    "../../api/transport:field_trial_based_config",
    "../../api/transport:network_control",
    "../../api/transport:webrtc_key_value_config",
    "../../logging:rtc_event_bwe",
    "../../logging:rtc_event_log_api",
    "../../logging:rtc_event_pacing",
    "../../rtc_base:checks",
    "../../rtc_base:deprecation",
    "../../rtc_base:rtc_base_approved",
    "../../rtc_base/experiments:alr_experiment",
    "../../rtc_base/experiments:field_trial_parser",
    "../../system_wrappers",
    "../../system_wrappers:field_trial",
    "../../system_wrappers:metrics",
    "../congestion_controller/goog_cc:alr_detector",
    "../remote_bitrate_estimator",
    "../rtp_rtcp",
    "../rtp_rtcp:rtp_rtcp_format",
    "../utility",
    "//third_party/abseil-cpp/absl/memory",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_source_set("interval_budget") {
  sources = [
    "interval_budget.cc",
    "interval_budget.h",
  ]

  deps = [
    "../../rtc_base:checks",
    "../../rtc_base:rtc_base_approved",
  ]
}

if (rtc_include_tests) {
  rtc_source_set("pacing_unittests") {
    testonly = true

    sources = [
      "bitrate_prober_unittest.cc",
      "interval_budget_unittest.cc",
      "paced_sender_unittest.cc",
      "packet_router_unittest.cc",
    ]
    deps = [
      ":interval_budget",
      ":pacing",
      "../../api/units:time_delta",
      "../../rtc_base:checks",
      "../../rtc_base:rtc_base_approved",
      "../../rtc_base:rtc_base_tests_utils",
      "../../rtc_base/experiments:alr_experiment",
      "../../system_wrappers",
      "../../system_wrappers:field_trial",
      "../../test:field_trial",
      "../../test:test_support",
      "../rtp_rtcp",
      "../rtp_rtcp:mock_rtp_rtcp",
      "../rtp_rtcp:rtp_rtcp_format",
    ]
  }

  rtc_source_set("mock_paced_sender") {
    testonly = true
    sources = [
      "mock/mock_paced_sender.h",
    ]
    deps = [
      ":pacing",
      "../../system_wrappers",
      "../../test:test_support",
    ]
  }
}
