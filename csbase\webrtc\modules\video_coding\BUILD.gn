# Copyright (c) 2014 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("../../webrtc.gni")

rtc_static_library("encoded_frame") {
  visibility = [ "*" ]
  sources = [
    "encoded_frame.cc",
    "encoded_frame.h",
  ]
  deps = [
    ":codec_globals_headers",
    ":video_codec_interface",
    "../../api/video:encoded_image",
    "../../api/video:video_frame",
    "../../api/video:video_frame_i420",
    "../../modules:module_api",
    "../../modules:module_api_public",
    "../../rtc_base:checks",
    "../../rtc_base:rtc_base_approved",
    "../../rtc_base/experiments:alr_experiment",
    "../../rtc_base/experiments:rtt_mult_experiment",
    "../../system_wrappers",
    "//third_party/abseil-cpp/absl/types:optional",
    "//third_party/abseil-cpp/absl/types:variant",
  ]
}

rtc_static_library("nack_module") {
  visibility = [ "*" ]
  sources = [
    "histogram.cc",
    "histogram.h",
    "nack_module.cc",
    "nack_module.h",
  ]

  deps = [
    ":packet",
    "..:module_api",
    "../../rtc_base:checks",
    "../../rtc_base:rtc_base_approved",
    "../../rtc_base:rtc_numerics",
    "../../system_wrappers",
    "../../system_wrappers:field_trial",
    "../utility",
  ]
}

rtc_static_library("packet") {
  visibility = [ "*" ]
  sources = [
    "packet.cc",
    "packet.h",
  ]
  deps = [
    "..:module_api",
    "../../:webrtc_common",
    "../../api:rtp_headers",
    "../../api/video:video_frame_type",
    "../rtp_rtcp:rtp_rtcp_format",
    "../rtp_rtcp:rtp_video_header",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_static_library("video_coding") {
  visibility = [ "*" ]
  deps = [
    "..:module_fec_api",
    "../../api:scoped_refptr",
    "../../api/video:encoded_image",
    "../../api/video:video_bitrate_allocation",
    "../../api/video:video_bitrate_allocator_factory",
    "../../system_wrappers:field_trial",
    "../../system_wrappers:metrics",
    "../rtp_rtcp:rtp_video_header",
    "//third_party/abseil-cpp/absl/memory",
  ]

  sources = [
    "codec_timer.cc",
    "codec_timer.h",
    "decoder_database.cc",
    "decoder_database.h",
    "decoding_state.cc",
    "decoding_state.h",
    "fec_controller_default.cc",
    "fec_controller_default.h",
    "fec_rate_table.h",
    "frame_buffer.cc",
    "frame_buffer.h",
    "frame_buffer2.cc",
    "frame_buffer2.h",
    "frame_object.cc",
    "frame_object.h",
    "generic_decoder.cc",
    "generic_decoder.h",
    "h264_sprop_parameter_sets.cc",
    "h264_sprop_parameter_sets.h",
    "h264_sps_pps_tracker.cc",
    "h264_sps_pps_tracker.h",
    "include/video_codec_initializer.h",
    "include/video_coding.h",
    "inter_frame_delay.cc",
    "inter_frame_delay.h",
    "internal_defines.h",
    "jitter_buffer.cc",
    "jitter_buffer.h",
    "jitter_buffer_common.h",
    "jitter_estimator.cc",
    "jitter_estimator.h",
    "loss_notification_controller.cc",
    "loss_notification_controller.h",
    "media_opt_util.cc",
    "media_opt_util.h",
    "packet_buffer.cc",
    "packet_buffer.h",
    "receiver.cc",
    "receiver.h",
    "rtp_frame_reference_finder.cc",
    "rtp_frame_reference_finder.h",
    "rtt_filter.cc",
    "rtt_filter.h",
    "session_info.cc",
    "session_info.h",
    "timestamp_map.cc",
    "timestamp_map.h",
    "timing.cc",
    "timing.h",
    "video_codec_initializer.cc",
    "video_coding_impl.cc",
    "video_coding_impl.h",
    "video_receiver.cc",
  ]

  deps += [
    ":codec_globals_headers",
    ":encoded_frame",
    ":packet",
    ":video_codec_interface",
    ":video_coding_utility",
    ":webrtc_vp9_helpers",
    "..:module_api",
    "..:module_api_public",
    "../../api:fec_controller_api",
    "../../api:rtp_headers",
    "../../api/task_queue:global_task_queue_factory",
    "../../api/units:data_rate",
    "../../api/video:builtin_video_bitrate_allocator_factory",
    "../../api/video:encoded_frame",
    "../../api/video:video_bitrate_allocator",
    "../../api/video:video_frame",
    "../../api/video:video_frame_i420",
    "../../api/video_codecs:video_codecs_api",
    "../../common_video",
    "../../rtc_base",
    "../../rtc_base:checks",
    "../../rtc_base:deprecation",
    "../../rtc_base:rtc_base_approved",
    "../../rtc_base:rtc_numerics",
    "../../rtc_base:rtc_task_queue",
    "../../rtc_base/experiments:alr_experiment",
    "../../rtc_base/experiments:jitter_upper_bound_experiment",
    "../../rtc_base/experiments:rtt_mult_experiment",
    "../../rtc_base/synchronization:sequence_checker",
    "../../rtc_base/system:fallthrough",
    "../../rtc_base/task_utils:repeating_task",
    "../../rtc_base/third_party/base64",
    "../../rtc_base/time:timestamp_extrapolator",
    "../../system_wrappers",
    "../../system_wrappers:event_wrapper",
    "../rtp_rtcp:rtp_rtcp_format",
    "../utility",
    "//third_party/abseil-cpp/absl/container:inlined_vector",
    "//third_party/abseil-cpp/absl/types:optional",
    "//third_party/abseil-cpp/absl/types:variant",
  ]
}

rtc_source_set("video_codec_interface") {
  visibility = [ "*" ]
  sources = [
    "include/video_codec_interface.cc",
    "include/video_codec_interface.h",
    "include/video_coding_defines.h",
    "include/video_error_codes.h",
    "video_coding_defines.cc",
  ]
  deps = [
    "..:module_api",
    "../../api/video:video_frame",
    "../../api/video_codecs:video_codecs_api",
    "../../common_video",
    "../../common_video/generic_frame_descriptor",
    "../../rtc_base/system:rtc_export",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_source_set("codec_globals_headers") {
  visibility = [ "*" ]
  sources = [
    "codecs/h264/include/h264_globals.h",
    "codecs/interface/common_constants.h",
    "codecs/vp8/include/vp8_globals.h",
    "codecs/vp9/include/vp9_globals.h",
  ]

  deps = [
    "../../rtc_base:checks",
  ]
}

rtc_source_set("video_coding_utility") {
  visibility = [ "*" ]
  sources = [
    "utility/decoded_frames_history.cc",
    "utility/decoded_frames_history.h",
    "utility/default_video_bitrate_allocator.cc",
    "utility/default_video_bitrate_allocator.h",
    "utility/frame_dropper.cc",
    "utility/frame_dropper.h",
    "utility/framerate_controller.cc",
    "utility/framerate_controller.h",
    "utility/ivf_file_writer.cc",
    "utility/ivf_file_writer.h",
    "utility/quality_scaler.cc",
    "utility/quality_scaler.h",
    "utility/simulcast_rate_allocator.cc",
    "utility/simulcast_rate_allocator.h",
    "utility/simulcast_utility.cc",
    "utility/simulcast_utility.h",
    "utility/vp8_header_parser.cc",
    "utility/vp8_header_parser.h",
    "utility/vp9_uncompressed_header_parser.cc",
    "utility/vp9_uncompressed_header_parser.h",
  ]

  deps = [
    ":video_codec_interface",
    "..:module_api",
    "../../api/video:encoded_frame",
    "../../api/video:encoded_image",
    "../../api/video:video_bitrate_allocation",
    "../../api/video:video_bitrate_allocator",
    "../../api/video_codecs:video_codecs_api",
    "../../common_video",
    "../../modules/rtp_rtcp",
    "../../rtc_base:checks",
    "../../rtc_base:rtc_base_approved",
    "../../rtc_base:rtc_numerics",
    "../../rtc_base:rtc_task_queue",
    "../../rtc_base/experiments:quality_scaler_settings",
    "../../rtc_base/experiments:quality_scaling_experiment",
    "../../rtc_base/experiments:rate_control_settings",
    "../../rtc_base/synchronization:sequence_checker",
    "../../rtc_base/system:arch",
    "../../rtc_base/system:file_wrapper",
    "../../rtc_base/task_utils:repeating_task",
    "../../system_wrappers:field_trial",
    "../rtp_rtcp:rtp_rtcp_format",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_static_library("webrtc_h264") {
  visibility = [ "*" ]
  sources = [
    "codecs/h264/h264.cc",
    "codecs/h264/h264_color_space.cc",
    "codecs/h264/h264_color_space.h",
    "codecs/h264/h264_decoder_impl.cc",
    "codecs/h264/h264_decoder_impl.h",
    "codecs/h264/h264_encoder_impl.cc",
    "codecs/h264/h264_encoder_impl.h",
    "codecs/h264/include/h264.h",
  ]

  defines = []
  deps = [
    ":video_codec_interface",
    ":video_coding_utility",
    "../../api/video:video_frame",
    "../../api/video:video_frame_i010",
    "../../api/video:video_frame_i420",
    "../../api/video_codecs:video_codecs_api",
    "../../common_video",
    "../../media:rtc_h264_profile_id",
    "../../media:rtc_media_base",
    "../../rtc_base",
    "../../rtc_base:checks",
    "../../rtc_base/system:rtc_export",
    "../../system_wrappers:field_trial",
    "../../system_wrappers:metrics",
    "//third_party/abseil-cpp/absl/memory",
    "//third_party/abseil-cpp/absl/strings",
    "//third_party/abseil-cpp/absl/types:optional",
    "//third_party/libyuv",
  ]

  if (rtc_use_h264) {
    deps += [
      "//third_party/ffmpeg",
      "//third_party/openh264:encoder",
    ]
    if (!build_with_mozilla) {
      deps += [ "../../media:rtc_media_base" ]
    }
  }
}

rtc_static_library("webrtc_multiplex") {
  sources = [
    "codecs/multiplex/augmented_video_frame_buffer.cc",
    "codecs/multiplex/include/augmented_video_frame_buffer.h",
    "codecs/multiplex/include/multiplex_decoder_adapter.h",
    "codecs/multiplex/include/multiplex_encoder_adapter.h",
    "codecs/multiplex/multiplex_decoder_adapter.cc",
    "codecs/multiplex/multiplex_encoded_image_packer.cc",
    "codecs/multiplex/multiplex_encoded_image_packer.h",
    "codecs/multiplex/multiplex_encoder_adapter.cc",
  ]

  deps = [
    ":video_codec_interface",
    ":video_coding_utility",
    "..:module_api",
    "../../api:scoped_refptr",
    "../../api/video:encoded_image",
    "../../api/video:video_frame",
    "../../api/video:video_frame_i420",
    "../../api/video_codecs:video_codecs_api",
    "../../common_video",
    "../../rtc_base",
    "../../rtc_base:checks",
    "../rtp_rtcp:rtp_rtcp_format",
  ]
}

# This target includes the internal SW codec.
rtc_static_library("webrtc_vp8") {
  visibility = [ "*" ]
  poisonous = [ "software_video_codecs" ]
  sources = [
    "codecs/vp8/include/vp8.h",
    "codecs/vp8/libvpx_interface.cc",
    "codecs/vp8/libvpx_interface.h",
    "codecs/vp8/libvpx_vp8_decoder.cc",
    "codecs/vp8/libvpx_vp8_decoder.h",
    "codecs/vp8/libvpx_vp8_encoder.cc",
    "codecs/vp8/libvpx_vp8_encoder.h",
  ]

  deps = [
    ":codec_globals_headers",
    ":video_codec_interface",
    ":video_coding_utility",
    ":webrtc_vp8_temporal_layers",
    "..:module_api",
    "../..:webrtc_common",
    "../../api:scoped_refptr",
    "../../api/video:encoded_image",
    "../../api/video:video_frame",
    "../../api/video:video_frame_i420",
    "../../api/video_codecs:video_codecs_api",
    "../../api/video_codecs:vp8_temporal_layers_factory",
    "../../common_video",
    "../../rtc_base:checks",
    "../../rtc_base:rtc_base_approved",
    "../../rtc_base:rtc_numerics",
    "../../rtc_base/experiments:cpu_speed_experiment",
    "../../rtc_base/experiments:field_trial_parser",
    "../../rtc_base/experiments:rate_control_settings",
    "../../system_wrappers:field_trial",
    "../../system_wrappers:metrics",
    "//third_party/abseil-cpp/absl/memory",
    "//third_party/abseil-cpp/absl/types:optional",
    "//third_party/libyuv",
  ]
  if (rtc_build_libvpx) {
    deps += [ rtc_libvpx_dir ]
  }
}

rtc_static_library("webrtc_vp8_temporal_layers") {
  visibility = [ "*" ]
  sources = [
    "codecs/vp8/default_temporal_layers.cc",
    "codecs/vp8/default_temporal_layers.h",
    "codecs/vp8/include/temporal_layers_checker.h",
    "codecs/vp8/screenshare_layers.cc",
    "codecs/vp8/screenshare_layers.h",
    "codecs/vp8/temporal_layers.h",
    "codecs/vp8/temporal_layers_checker.cc",
  ]

  deps = [
    ":codec_globals_headers",
    ":video_codec_interface",
    ":video_coding_utility",
    "..:module_api",
    "../..:webrtc_common",
    "../../api/video_codecs:video_codecs_api",
    "../../rtc_base:checks",
    "../../rtc_base:rtc_base_approved",
    "../../rtc_base:rtc_numerics",
    "../../system_wrappers:field_trial",
    "../../system_wrappers:metrics",
    "//third_party/abseil-cpp/absl/memory",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

# This target includes VP9 files that may be used for any VP9 codec, internal SW or external HW.
rtc_static_library("webrtc_vp9_helpers") {
  sources = [
    "codecs/vp9/svc_config.cc",
    "codecs/vp9/svc_config.h",
    "codecs/vp9/svc_rate_allocator.cc",
    "codecs/vp9/svc_rate_allocator.h",
  ]

  deps = [
    ":codec_globals_headers",
    ":video_codec_interface",
    "../..:webrtc_common",
    "../../api/video:video_bitrate_allocation",
    "../../api/video:video_bitrate_allocator",
    "../../api/video_codecs:video_codecs_api",
    "../../common_video",
    "../../rtc_base:checks",
  ]
}

rtc_static_library("webrtc_vp9") {
  visibility = [ "*" ]
  poisonous = [ "software_video_codecs" ]
  sources = [
    "codecs/vp9/include/vp9.h",
    "codecs/vp9/vp9.cc",
    "codecs/vp9/vp9_frame_buffer_pool.cc",
    "codecs/vp9/vp9_frame_buffer_pool.h",
    "codecs/vp9/vp9_impl.cc",
    "codecs/vp9/vp9_impl.h",
  ]

  deps = [
    ":video_codec_interface",
    ":video_coding_utility",
    ":webrtc_vp9_helpers",
    "..:module_api",
    "../..:webrtc_common",
    "../../api:scoped_refptr",
    "../../api/video:video_frame",
    "../../api/video:video_frame_i010",
    "../../api/video_codecs:video_codecs_api",
    "../../common_video",
    "../../media:rtc_media_base",
    "../../media:rtc_vp9_profile",
    "../../rtc_base",
    "../../rtc_base:checks",
    "../../rtc_base/experiments:rate_control_settings",
    "../../system_wrappers:field_trial",
    "../rtp_rtcp:rtp_rtcp_format",
    "//third_party/abseil-cpp/absl/memory",
  ]
  if (rtc_build_libvpx) {
    deps += [ rtc_libvpx_dir ]
  }
}

if (rtc_include_tests) {
  if (is_android) {
    rtc_static_library("android_codec_factory_helper") {
      sources = [
        "codecs/test/android_codec_factory_helper.cc",
        "codecs/test/android_codec_factory_helper.h",
      ]

      deps = [
        "../../api/video_codecs:video_codecs_api",
        "../../rtc_base:checks",
        "../../rtc_base:rtc_base_approved",
        "../../sdk/android:native_api_base",
        "../../sdk/android:native_api_codecs",
        "../../sdk/android:native_api_jni",
        "//base",
      ]
    }
  }

  if (is_ios || is_mac) {
    rtc_static_library("objc_codec_factory_helper") {
      sources = [
        "codecs/test/objc_codec_factory_helper.h",
        "codecs/test/objc_codec_factory_helper.mm",
      ]

      deps = [
        "../../api/video:video_frame",
        "../../api/video_codecs:video_codecs_api",
        "../../media:rtc_audio_video",
        "../../media:rtc_media_base",
        "../../modules:module_api",
        "../../rtc_base:rtc_base_approved",
        "../../sdk:native_api",
        "../../sdk:peerconnectionfactory_base_objc",
        "../../sdk:videocodec_objc",
        "../../sdk:videosource_objc",
        "../../sdk:videotoolbox_objc",
      ]
    }
  }

  rtc_source_set("simulcast_test_fixture_impl") {
    testonly = true
    sources = [
      "utility/simulcast_test_fixture_impl.cc",
      "utility/simulcast_test_fixture_impl.h",
    ]

    deps = [
      ":video_codec_interface",
      ":video_coding",
      ":video_coding_utility",
      "../../:webrtc_common",
      "../../api:mock_video_decoder",
      "../../api:mock_video_encoder",
      "../../api:simulcast_test_fixture_api",
      "../../api/video:encoded_image",
      "../../api/video:video_frame",
      "../../api/video:video_frame_i420",
      "../../api/video_codecs:video_codecs_api",
      "../../common_video",
      "../../rtc_base:checks",
      "../../rtc_base:rtc_base_approved",
      "../../test:test_support",
      "//third_party/abseil-cpp/absl/memory",
    ]
  }

  rtc_source_set("video_codecs_test_framework") {
    testonly = true
    sources = [
      "codecs/test/video_codec_unittest.cc",
      "codecs/test/video_codec_unittest.h",
      "codecs/test/videoprocessor.cc",
      "codecs/test/videoprocessor.h",
    ]

    deps = [
      ":codec_globals_headers",
      ":video_codec_interface",
      ":video_coding",
      ":video_coding_utility",
      ":videocodec_test_stats_impl",
      ":webrtc_vp9_helpers",
      "..:module_api",
      "../../api:scoped_refptr",
      "../../api:videocodec_test_fixture_api",
      "../../api/task_queue",
      "../../api/video:builtin_video_bitrate_allocator_factory",
      "../../api/video:encoded_image",
      "../../api/video:video_bitrate_allocation",
      "../../api/video:video_bitrate_allocator",
      "../../api/video:video_bitrate_allocator_factory",
      "../../api/video:video_frame",
      "../../api/video:video_frame_i420",
      "../../api/video_codecs:video_codecs_api",
      "../../common_video",
      "../../rtc_base:checks",
      "../../rtc_base:rtc_base_approved",
      "../../rtc_base:rtc_task_queue",
      "../../rtc_base/synchronization:sequence_checker",
      "../../rtc_base/task_utils:to_queued_task",
      "../../test:test_support",
      "../../test:video_test_common",
      "../../test:video_test_support",
      "../rtp_rtcp:rtp_rtcp_format",
      "//third_party/abseil-cpp/absl/memory",
      "//third_party/abseil-cpp/absl/types:optional",
      "//third_party/libyuv",
    ]
  }

  video_coding_modules_tests_resources = []
  if (is_android) {
    video_coding_modules_tests_resources += [
      "../../resources/foreman_128x96.yuv",
      "../../resources/foreman_160x120.yuv",
      "../../resources/foreman_176x144.yuv",
      "../../resources/foreman_240x136.yuv",
      "../../resources/foreman_320x240.yuv",
      "../../resources/foreman_480x272.yuv",
    ]
  }
  if (!is_android) {
    video_coding_modules_tests_resources += [
      "../../resources/FourPeople_1280x720_30.yuv",
      "../../resources/ConferenceMotion_1280_720_50.yuv",
    ]
  }

  num_video_coding_modules_tests_resources = 0
  foreach(i, video_coding_modules_tests_resources) {
    num_video_coding_modules_tests_resources += 1
  }

  if (num_video_coding_modules_tests_resources > 0) {
    if (is_ios || is_mac) {
      bundle_data("video_coding_modules_tests_resources_bundle_data") {
        testonly = true
        sources = video_coding_modules_tests_resources
        outputs = [
          "{{bundle_resources_dir}}/{{source_file_part}}",
        ]
      }
    }
  }

  rtc_source_set("videocodec_test_impl") {
    testonly = true
    sources = [
      "codecs/test/videocodec_test_fixture_impl.cc",
      "codecs/test/videocodec_test_fixture_impl.h",
    ]
    deps = [
      ":codec_globals_headers",
      ":video_codec_interface",
      ":video_codecs_test_framework",
      ":video_coding_utility",
      ":videocodec_test_stats_impl",
      ":webrtc_vp9_helpers",
      "../..:webrtc_common",
      "../../api:array_view",
      "../../api:videocodec_test_fixture_api",
      "../../api/test/video:function_video_factory",
      "../../api/video:video_bitrate_allocation",
      "../../api/video_codecs:video_codecs_api",
      "../../call:video_stream_api",
      "../../common_video",
      "../../media:rtc_audio_video",
      "../../media:rtc_h264_profile_id",
      "../../media:rtc_internal_video_codecs",
      "../../media:rtc_media_base",
      "../../rtc_base:checks",
      "../../rtc_base:rtc_base_approved",
      "../../rtc_base:rtc_base_tests_utils",
      "../../rtc_base:task_queue_for_test",
      "../../system_wrappers",
      "../../test:fileutils",
      "../../test:perf_test",
      "../../test:test_support",
      "../../test:video_test_common",
      "../../test:video_test_support",
      "//third_party/abseil-cpp/absl/memory",
      "//third_party/abseil-cpp/absl/types:optional",
    ]
  }

  rtc_source_set("videocodec_test_stats_impl") {
    testonly = true
    sources = [
      "codecs/test/videocodec_test_stats_impl.cc",
      "codecs/test/videocodec_test_stats_impl.h",
    ]
    deps = [
      "../../api:videocodec_test_fixture_api",
      "../../rtc_base:checks",
      "../../rtc_base:rtc_numerics",
      "../../rtc_base:stringutils",
      "../../test:test_common",
      "../rtp_rtcp:rtp_rtcp_format",
    ]
  }

  rtc_source_set("video_coding_modules_tests") {
    testonly = true
    defines = []

    sources = [
      "codecs/h264/test/h264_impl_unittest.cc",
      "codecs/multiplex/test/multiplex_adapter_unittest.cc",
      "codecs/test/video_encoder_decoder_instantiation_tests.cc",
      "codecs/test/videocodec_test_libvpx.cc",
      "codecs/vp8/test/mock_libvpx_interface.h",
      "codecs/vp8/test/vp8_impl_unittest.cc",
      "codecs/vp9/test/vp9_impl_unittest.cc",
    ]
    if (rtc_use_h264) {
      sources += [ "codecs/test/videocodec_test_openh264.cc" ]
    }

    deps = [
      ":video_codec_interface",
      ":video_codecs_test_framework",
      ":video_coding_utility",
      ":videocodec_test_impl",
      ":webrtc_h264",
      ":webrtc_multiplex",
      ":webrtc_vp8",
      ":webrtc_vp9",
      ":webrtc_vp9_helpers",
      "../..:webrtc_common",
      "../../api:create_videocodec_test_fixture_api",
      "../../api:mock_video_codec_factory",
      "../../api:mock_video_decoder",
      "../../api:mock_video_encoder",
      "../../api:scoped_refptr",
      "../../api:videocodec_test_fixture_api",
      "../../api/test/video:function_video_factory",
      "../../api/video:encoded_image",
      "../../api/video:video_frame",
      "../../api/video:video_frame_i420",
      "../../api/video_codecs:rtc_software_fallback_wrappers",
      "../../api/video_codecs:video_codecs_api",
      "../../common_video",
      "../../common_video/test:utilities",
      "../../media:rtc_h264_profile_id",
      "../../media:rtc_internal_video_codecs",
      "../../media:rtc_media_base",
      "../../media:rtc_simulcast_encoder_adapter",
      "../../media:rtc_vp9_profile",
      "../../rtc_base",
      "../../test:field_trial",
      "../../test:fileutils",
      "../../test:test_support",
      "../../test:video_test_common",
      "../rtp_rtcp:rtp_rtcp_format",
      "//third_party/abseil-cpp/absl/memory",
      "//third_party/abseil-cpp/absl/types:optional",
      "//third_party/libyuv",
    ]

    data = video_coding_modules_tests_resources

    if (is_android) {
      sources += [ "codecs/test/videocodec_test_mediacodec.cc" ]

      deps += [ ":android_codec_factory_helper" ]
    }

    if (is_ios || is_mac) {
      sources += [ "codecs/test/videocodec_test_videotoolbox.cc" ]

      deps += [ ":objc_codec_factory_helper" ]

      if (num_video_coding_modules_tests_resources > 0) {
        deps += [ ":video_coding_modules_tests_resources_bundle_data" ]
      }
    }

    if (rtc_build_libvpx) {
      deps += [ rtc_libvpx_dir ]
    }
  }

  rtc_source_set("video_coding_unittests") {
    testonly = true

    sources = [
      "codecs/test/videocodec_test_fixture_config_unittest.cc",
      "codecs/test/videocodec_test_stats_impl_unittest.cc",
      "codecs/test/videoprocessor_unittest.cc",
      "codecs/vp8/default_temporal_layers_unittest.cc",
      "codecs/vp8/libvpx_vp8_simulcast_test.cc",
      "codecs/vp8/screenshare_layers_unittest.cc",
      "codecs/vp9/svc_config_unittest.cc",
      "codecs/vp9/svc_rate_allocator_unittest.cc",
      "decoding_state_unittest.cc",
      "fec_controller_unittest.cc",
      "frame_buffer2_unittest.cc",
      "generic_decoder_unittest.cc",
      "h264_sprop_parameter_sets_unittest.cc",
      "h264_sps_pps_tracker_unittest.cc",
      "histogram_unittest.cc",
      "include/mock/mock_vcm_callbacks.h",
      "jitter_buffer_unittest.cc",
      "jitter_estimator_tests.cc",
      "loss_notification_controller_unittest.cc",
      "nack_module_unittest.cc",
      "receiver_unittest.cc",
      "rtp_frame_reference_finder_unittest.cc",
      "session_info_unittest.cc",
      "test/stream_generator.cc",
      "test/stream_generator.h",
      "timing_unittest.cc",
      "utility/decoded_frames_history_unittest.cc",
      "utility/default_video_bitrate_allocator_unittest.cc",
      "utility/frame_dropper_unittest.cc",
      "utility/framerate_controller_unittest.cc",
      "utility/ivf_file_writer_unittest.cc",
      "utility/quality_scaler_unittest.cc",
      "utility/simulcast_rate_allocator_unittest.cc",
      "video_codec_initializer_unittest.cc",
      "video_packet_buffer_unittest.cc",
      "video_receiver_unittest.cc",
    ]
    if (rtc_use_h264) {
      sources += [
        "codecs/h264/h264_encoder_impl_unittest.cc",
        "codecs/h264/h264_simulcast_unittest.cc",
      ]
    }

    deps = [
      ":codec_globals_headers",
      ":encoded_frame",
      ":nack_module",
      ":packet",
      ":simulcast_test_fixture_impl",
      ":video_codec_interface",
      ":video_codecs_test_framework",
      ":video_coding",
      ":video_coding_utility",
      ":videocodec_test_impl",
      ":videocodec_test_stats_impl",
      ":webrtc_h264",
      ":webrtc_vp8",
      ":webrtc_vp8_temporal_layers",
      ":webrtc_vp9",
      ":webrtc_vp9_helpers",
      "..:module_api",
      "..:module_fec_api",
      "../../api:array_view",
      "../../api:create_simulcast_test_fixture_api",
      "../../api:fec_controller_api",
      "../../api:mock_video_decoder",
      "../../api:mock_video_encoder",
      "../../api:scoped_refptr",
      "../../api:simulcast_test_fixture_api",
      "../../api:videocodec_test_fixture_api",
      "../../api/task_queue:default_task_queue_factory",
      "../../api/test/video:function_video_factory",
      "../../api/video:builtin_video_bitrate_allocator_factory",
      "../../api/video:video_bitrate_allocation",
      "../../api/video:video_bitrate_allocator",
      "../../api/video:video_bitrate_allocator_factory",
      "../../api/video:video_frame",
      "../../api/video:video_frame_i420",
      "../../api/video_codecs:video_codecs_api",
      "../../api/video_codecs:vp8_temporal_layers_factory",
      "../../common_video",
      "../../common_video/test:utilities",
      "../../media:rtc_media_base",
      "../../rtc_base",
      "../../rtc_base:checks",
      "../../rtc_base:rtc_base_approved",
      "../../rtc_base:rtc_base_tests_utils",
      "../../rtc_base:rtc_numerics",
      "../../rtc_base:rtc_task_queue",
      "../../rtc_base:task_queue_for_test",
      "../../rtc_base/experiments:jitter_upper_bound_experiment",
      "../../system_wrappers",
      "../../system_wrappers:event_wrapper",
      "../../system_wrappers:field_trial",
      "../../system_wrappers:metrics",
      "../../test:fake_video_codecs",
      "../../test:field_trial",
      "../../test:fileutils",
      "../../test:test_common",
      "../../test:test_support",
      "../../test:video_test_common",
      "../../test:video_test_support",
      "../rtp_rtcp:rtp_rtcp_format",
      "../rtp_rtcp:rtp_video_header",
      "//third_party/abseil-cpp/absl/memory",
      "//third_party/abseil-cpp/absl/types:optional",
      "//third_party/abseil-cpp/absl/types:variant",
    ]
    if (rtc_build_libvpx) {
      deps += [ rtc_libvpx_dir ]
    }
  }
}
