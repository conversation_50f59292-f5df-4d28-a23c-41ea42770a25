# Copyright (c) 2018 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("../../webrtc.gni")

rtc_static_library("alr_experiment") {
  sources = [
    "alr_experiment.cc",
    "alr_experiment.h",
  ]
  deps = [
    "../:rtc_base_approved",
    "../../api/transport:field_trial_based_config",
    "../../api/transport:webrtc_key_value_config",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_static_library("audio_allocation_settings") {
  sources = [
    "audio_allocation_settings.cc",
    "audio_allocation_settings.h",
  ]
  defines = []
  if (rtc_opus_support_120ms_ptime) {
    defines += [ "WEBRTC_OPUS_SUPPORT_120MS_PTIME=1" ]
  } else {
    defines += [ "WEBRTC_OPUS_SUPPORT_120MS_PTIME=0" ]
  }
  deps = [
    ":field_trial_parser",
    "../:rtc_base_approved",
    "../../system_wrappers:field_trial",
  ]
}

rtc_static_library("field_trial_parser") {
  sources = [
    "field_trial_list.cc",
    "field_trial_list.h",
    "field_trial_parser.cc",
    "field_trial_parser.h",
    "field_trial_units.cc",
    "field_trial_units.h",
  ]
  deps = [
    "../../api/units:data_rate",
    "../../api/units:data_size",
    "../../api/units:time_delta",
    "../../rtc_base:checks",
    "../../rtc_base:logging",
    "../../rtc_base:stringutils",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_static_library("quality_scaler_settings") {
  sources = [
    "quality_scaler_settings.cc",
    "quality_scaler_settings.h",
  ]
  deps = [
    ":field_trial_parser",
    "../:rtc_base_approved",
    "../../api/transport:field_trial_based_config",
    "../../api/transport:webrtc_key_value_config",
    "../../system_wrappers:field_trial",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_static_library("quality_scaling_experiment") {
  sources = [
    "quality_scaling_experiment.cc",
    "quality_scaling_experiment.h",
  ]
  deps = [
    "../:rtc_base_approved",
    "../../api/video_codecs:video_codecs_api",
    "../../system_wrappers:field_trial",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_static_library("normalize_simulcast_size_experiment") {
  sources = [
    "normalize_simulcast_size_experiment.cc",
    "normalize_simulcast_size_experiment.h",
  ]
  deps = [
    "../:rtc_base_approved",
    "../../system_wrappers:field_trial",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_static_library("balanced_degradation_settings") {
  sources = [
    "balanced_degradation_settings.cc",
    "balanced_degradation_settings.h",
  ]
  deps = [
    ":field_trial_parser",
    "../:rtc_base_approved",
    "../../system_wrappers:field_trial",
  ]
}

rtc_static_library("cpu_speed_experiment") {
  sources = [
    "cpu_speed_experiment.cc",
    "cpu_speed_experiment.h",
  ]
  deps = [
    "../:rtc_base_approved",
    "../../system_wrappers:field_trial",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_static_library("rtt_mult_experiment") {
  sources = [
    "rtt_mult_experiment.cc",
    "rtt_mult_experiment.h",
  ]
  deps = [
    "../:rtc_base_approved",
    "../../system_wrappers:field_trial",
  ]
}

rtc_static_library("jitter_upper_bound_experiment") {
  sources = [
    "jitter_upper_bound_experiment.cc",
    "jitter_upper_bound_experiment.h",
  ]
  deps = [
    "../:rtc_base_approved",
    "../../system_wrappers:field_trial",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_static_library("rate_control_settings") {
  sources = [
    "rate_control_settings.cc",
    "rate_control_settings.h",
  ]
  deps = [
    ":field_trial_parser",
    "../:rtc_base_approved",
    "../../api/transport:field_trial_based_config",
    "../../api/transport:webrtc_key_value_config",
    "../../api/video_codecs:video_codecs_api",
    "../../system_wrappers:field_trial",
    "//third_party/abseil-cpp/absl/memory",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

rtc_static_library("keyframe_interval_settings_experiment") {
  sources = [
    "keyframe_interval_settings.cc",
    "keyframe_interval_settings.h",
  ]
  deps = [
    ":field_trial_parser",
    "../../api/transport:field_trial_based_config",
    "../../api/transport:webrtc_key_value_config",
    "//third_party/abseil-cpp/absl/types:optional",
  ]
}

if (rtc_include_tests) {
  rtc_source_set("experiments_unittests") {
    testonly = true

    sources = [
      "balanced_degradation_settings_unittest.cc",
      "cpu_speed_experiment_unittest.cc",
      "field_trial_list_unittest.cc",
      "field_trial_parser_unittest.cc",
      "field_trial_units_unittest.cc",
      "keyframe_interval_settings_unittest.cc",
      "normalize_simulcast_size_experiment_unittest.cc",
      "quality_scaler_settings_unittest.cc",
      "quality_scaling_experiment_unittest.cc",
      "rate_control_settings_unittest.cc",
      "rtt_mult_experiment_unittest.cc",
    ]
    deps = [
      ":balanced_degradation_settings",
      ":cpu_speed_experiment",
      ":field_trial_parser",
      ":keyframe_interval_settings_experiment",
      ":normalize_simulcast_size_experiment",
      ":quality_scaler_settings",
      ":quality_scaling_experiment",
      ":rate_control_settings",
      ":rtt_mult_experiment",
      "..:gunit_helpers",
      "../:rtc_base_tests_utils",
      "../../api/video_codecs:video_codecs_api",
      "../../system_wrappers:field_trial",
      "../../test:field_trial",
      "../../test:test_main",
      "../../test:test_support",
      "//third_party/abseil-cpp/absl/types:optional",
    ]
  }
}
