/*
 *  Copyright 2016 The WebRTC Project Authors. All rights reserved.
 *
 *  Use of this source code is governed by a BSD-style license
 *  that can be found in the LICENSE file in the root of the source
 *  tree. An additional intellectual property rights grant can be found
 *  in the file PATENTS.  All contributing project authors may
 *  be found in the AUTHORS file in the root of the source tree.
 */

#ifndef RTC_BASE_FUNCTION_VIEW_H_
#define RTC_BASE_FUNCTION_VIEW_H_

// This header is deprecated and will be removed. Please use the one,
// that is specified below instead.

#include "api/function_view.h"

#endif  // RTC_BASE_FUNCTION_VIEW_H_
