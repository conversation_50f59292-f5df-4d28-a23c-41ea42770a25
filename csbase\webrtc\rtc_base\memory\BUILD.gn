# Copyright (c) 2018 The WebRTC project authors. All Rights Reserved.
#
# Use of this source code is governed by a BSD-style license
# that can be found in the LICENSE file in the root of the source
# tree. An additional intellectual property rights grant can be found
# in the file PATENTS.  All contributing project authors may
# be found in the AUTHORS file in the root of the source tree.

import("../../webrtc.gni")
if (is_android) {
  import("//build/config/android/config.gni")
  import("//build/config/android/rules.gni")
}

rtc_source_set("aligned_array") {
  sources = [
    "aligned_array.h",
  ]
  deps = [
    ":aligned_malloc",
    "..:checks",
  ]
}

rtc_source_set("aligned_malloc") {
  sources = [
    "aligned_malloc.cc",
    "aligned_malloc.h",
  ]
  deps = [
    "..:checks",
  ]
}

rtc_source_set("fifo_buffer") {
  visibility = [
    "../../p2p:rtc_p2p",
    "..:rtc_base_tests_utils",
    "..:rtc_base_unittests",
    ":unittests",
  ]
  sources = [
    "fifo_buffer.cc",
    "fifo_buffer.h",
  ]
  deps = [
    "..:rtc_base",
  ]
}

rtc_source_set("unittests") {
  testonly = true
  sources = [
    "aligned_array_unittest.cc",
    "aligned_malloc_unittest.cc",
    "fifo_buffer_unittest.cc",
  ]
  deps = [
    ":aligned_array",
    ":aligned_malloc",
    ":fifo_buffer",
    "../../test:test_support",
  ]
}
