#include <sys/time.h>
#include "AkcsZipkin.h"
#include "AkLogging.h"
#include "json/json.h"
#include "gid/SnowFlakeGid.h"
#include "kafka/AkcsKafkaProducer.h"
#include "ThreadVariable.h"
#include "AkcsMsgDef.h"
#include "ZipkinConf.h"
#include "ZipkinAsyn.h"


extern AkcsKafkaProducer* g_zipin_kafka;


MsgNameMap csadapt_msgname_map = {
    //msg_id与msg_name的映射关系
    {MSG_P2A_NOTIFY_REMOTE_OPENDOOR,"MSG_P2A_NOTIFY_REMOTE_OPENDOOR"}
};

MsgNameMap csroute_msgname_map = {
    //msg_id与msg_name的映射关系
    {MSG_C2S_REMOTE_OPENDOOR,"MSG_C2S_REMOTE_OPENDOOR"}
};

MsgNameMap csmain_msgname_map = {
    //msg_id与msg_name的映射关系
    {MSG_C2S_REMOTE_OPENDOOR,"MSG_C2S_REMOTE_OPENDOOR"}
};


static std::string IntegerToStr(uint64_t id)
{
    //spanid长度限制
    //uint64转16位长字符串
    std::string id_str;
    id_str = std::to_string(id);
    if(id_str.size() >= 16)
    {
        return id_str.substr(id_str.size() - 16);
    }
    else
    {
        return id_str.substr(0,16);
    }
}

AkcsZipkin::AkcsZipkin(uint64_t trace_id, uint64_t parent_id, const std::string& service_name, const std::string& server_outer_ip, int msg_id)
{
    std::string name;
    if(MsgFilter(service_name, msg_id, name) != 0)
    {
        return;
    }
    trace_id_ = std::to_string(trace_id);
    parent_id_ = IntegerToStr(parent_id);
    service_name_ = service_name;
    server_outer_ip_ = server_outer_ip;   
    name_ = name;
    
    struct timeval start;
    gettimeofday(&start, NULL);
    timestamp_start_ = start.tv_sec*1000000 + start.tv_usec;

    uint64_t span_id = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    span_id_ = IntegerToStr(span_id);    

    is_send_zipkin_ = true;

    ThreadVariable::GetInstance().SetKeyValue("zipkin_trace_id", trace_id);
    ThreadVariable::GetInstance().SetKeyValue("zipkin_span_id", span_id);
    
}
AkcsZipkin::~AkcsZipkin()
{
    if(is_send_zipkin_)
    {
        SendToZipkin();
    } 
}

int AkcsZipkin::MsgFilter(const std::string& service_name, int msg_id, std::string& msg_name)
{
    MsgNameMap msgname_map;
    if (service_name == "csadapt") 
    {
        msgname_map = csadapt_msgname_map;
    } 
    else if(service_name == "csroute")
    {
        msgname_map = csroute_msgname_map;
    } 
    else if(service_name == "csmain")
    {
        msgname_map = csmain_msgname_map;
    }

    const auto& it = msgname_map.find(msg_id);
    if (it != msgname_map.end()) {
        msg_name = it->second;
        return 0;
    } else {
        is_send_zipkin_ = 0;
        return -1;
    }
}

void AkcsZipkin::SetTags(const std::string& key, const std::string& value)
{
    tags_.insert(std::pair<std::string, std::string>(key, value));
}

void AkcsZipkin::SendToZipkin()
{
    Json::Value item_local;
    item_local["serviceName"] = service_name_;
    item_local["ipv4"] = server_outer_ip_;
    item_local["port"] = 0;
        
    Json::Value item_remote;
    item_remote["ipv4"] = "0.0.0.0";
    item_remote["port"] = 0;

    Json::Value item_tags;
    for(const auto &tag : tags_)
    {
        item_tags[tag.first] = tag.second;
    }
    

    Json::Value item;

    item["id"] = span_id_;
    item["traceId"] = trace_id_;
    item["parentId"] = parent_id_;
    item["name"] = name_;

    struct timeval end;
    gettimeofday(&end, NULL);
    timestamp_end_ = end.tv_sec*1000000 + end.tv_usec;
    item["timestamp"] = timestamp_end_;
    item["duration"] = timestamp_end_ - timestamp_start_; 
    item["kind"] = "SERVER";
    item["localEndpoint"] = item_local;
    item["remoteEndpoint"] = item_remote;
    item["tags"] = item_tags;

    Json::Value item_body;
    item_body.append(item);

    Json::FastWriter w_json_item;
    std::string json_data = w_json_item.write(item_body);

    //异步
    GetZipkinAsynInstance()->AddMsg(json_data);
    //同步
    //ZipkinKafka(span_id_, json_data);

}

void AkcsZipkin::ZipkinKafka(const std::string& key, const std::string& content)
{
    if(g_zipin_kafka != nullptr)
    {
        g_zipin_kafka->ProduceMsg(key, content);
    }
}


