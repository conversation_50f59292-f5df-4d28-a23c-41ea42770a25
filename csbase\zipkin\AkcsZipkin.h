#ifndef __AKCS_ZIPKIN_H__
#define __AKCS_ZIPKIN_H__
#include <string>
#include <map>
#include <memory>
#include "gid/SnowFlakeGid.h"

typedef std::map<int, std::string> MsgNameMap;

class AkcsZipkin
{
public:
    Akcs<PERSON><PERSON>kin(uint64_t trace_id, uint64_t parent_id, const std::string& service_name, const std::string& server_outer_ip, int msg_id);
    ~Akcs<PERSON>ipkin();
    
    void SetTags(const std::string& key, const std::string& value);
        
private:

    std::string trace_id_;
    std::string parent_id_;
    std::string span_id_;
    std::string name_;
    std::string service_name_;
    std::string server_outer_ip_;
    long long timestamp_start_;
    long long timestamp_end_;
    std::string kind_;
    std::map<std::string, std::string> tags_;
    bool is_send_zipkin_;
    
    void SendToZipkin();
    void ZipkinKafka(const std::string& key, const std::string& content);
    
    int MsgFilter(const std::string& service_name, int msg_id, std::string& msg_name);

};

typedef std::shared_ptr<AkcsZipkin> AkcsZipkinPtr;



#endif
