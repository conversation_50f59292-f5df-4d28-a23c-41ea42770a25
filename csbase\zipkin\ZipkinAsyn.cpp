#include "ZipkinAsyn.h"
#include "kafka/AkcsKafkaProducer.h"
#include "AkcsMonitor.h"
#include "AkLogging.h"

extern AkcsKafkaProducer* g_zipin_kafka;


ZipkinAsyn* GetZipkinAsynInstance()
{
    return ZipkinAsyn::GetInstance();
}

ZipkinAsyn::ZipkinAsyn()
{
    
}

ZipkinAsyn::~ZipkinAsyn()
{
    list_.clear();
}

ZipkinAsyn* ZipkinAsyn::instance = NULL;

ZipkinAsyn* ZipkinAsyn::GetInstance()
{
    if (instance == NULL)
    {
        instance = new ZipkinAsyn();
    }

    return instance;
}

void ZipkinAsyn::Init()
{
    std::thread thread= std::thread(&ZipkinAsyn::ProcessMsg, this);
    thread.detach();
}

void ZipkinAsyn::AddMsg(const std::string& msg)
{
    std::unique_lock<std::mutex> lock(mtx_);
    list_.push_front(msg);
    cv_.notify_all();
}

void ZipkinAsyn::ProcessMsg()
{        
    while (1)
    {
        std::list<std::string> tmp_list;    
        {
            std::unique_lock<std::mutex> lock(mtx_);
            cv_.wait(lock);
            list_.swap(tmp_list);
        }

        if (tmp_list.size() > 5000) 
        {
            std::string msg = "zipkin msg queue overflow, the length now is:";
            msg += std::to_string(tmp_list.size());
            std::string worker_node = "zipkin";
            AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, msg, AKCS_MONITOR_ALARM_QUEUE_OVERFLOW_ZIPKIN_NOTIFY);
            AK_LOG_WARN << msg;
        }

        
        while (tmp_list.size() > 0)
        {
            std::string msg = tmp_list.back();
            tmp_list.pop_back();
            std::string key;
            
            if(g_zipin_kafka != nullptr)
            {
                g_zipin_kafka->ProduceMsg(key, msg);
            }
        }            
    }
}

