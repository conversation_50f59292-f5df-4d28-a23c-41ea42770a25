#ifndef __ZIPKIN_ASYN__
#define __ZIPKIN_ASYN__
#include <mutex>
#include <condition_variable>
#include <list>
#include <thread>
#include <string>


class ZipkinAsyn
{
public:
    ZipkinAsyn();
    ~ZipkinAsyn();

    static ZipkinAsyn* GetInstance();

    void AddMsg(const std::string& msg);

    void ProcessMsg();

    void Init();

private:
    static ZipkinAsyn* instance;

    std::mutex mtx_;   
    std::list<std::string> list_;     
    std::condition_variable cv_;
};

ZipkinAsyn* GetZipkinAsynInstance();

#endif
