#include "ZipkinConf.h"
#include "kafka/AkcsKafkaProducer.h"
#include "EtcdCliMng.h"

AkcsKafkaProducer* g_zipin_kafka;
CAkEtcdCliManager* g_etcd_zipkinconf_cli;
AKCS_ZIPKIN_CONF g_zipkin_conf;
extern const char *g_ak_srv_zipkin_kafka;


void UpdateZipkinConfFromConfSrv()
{
    std::string zipkin_kafka_addr;
    g_etcd_zipkinconf_cli->LoadSrvZipkinConf(zipkin_kafka_addr);
    ::strncpy(g_zipkin_conf.zipkin_kafka_addr, zipkin_kafka_addr.c_str(), sizeof(g_zipkin_conf.zipkin_kafka_addr) - 1);

    if(g_zipin_kafka != nullptr) 
    {
        delete g_zipin_kafka;
        g_zipin_kafka = nullptr;
    }

    if(zipkin_kafka_addr.size() == 0)
    {
        g_zipkin_conf.is_support_zipkin = 0;
    }
    else
    {
        g_zipkin_conf.is_support_zipkin = 1;    
        g_zipin_kafka = new AkcsKafkaProducer("zipkin", zipkin_kafka_addr);
    }
}

