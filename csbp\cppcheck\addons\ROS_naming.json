{"RE_FILE": [".*[A-Z]"], "RE_NAMESPACE": {".*[A-Z]": [true, "under_scored"], ".*\\_$": [true, "under_scored"]}, "RE_FUNCTIONNAME": {".*\\_": [true, "camelCase"], ".*^[a-z]": [false, "camelCase"]}, "RE_CLASS_NAME": {".*^[A-Z]": [false, "CamelCase"], ".*\\_": [true, "CamelCase"]}, "RE_GLOBAL_VARNAME": {".*^([g]\\_)": [false, "g_under_scored"], ".*[A-Z]": [true, "g_under_scored"], ".*\\_$": [true, "g_under_scored"]}, "RE_VARNAME": {".*^([g]\\_)": [true, "under_scored"], ".*[A-Z]": [true, "under_scored"], ".*\\_$": [true, "under_scored"]}, "RE_PRIVATE_MEMBER_VARIABLE": {".*\\_$": [false, "under_scored_"], ".*[A-Z]": [true, "under_scored_"]}, "RE_PUBLIC_MEMBER_VARIABLE": {".*\\_$": [false, "under_scored_"], ".*[A-Z]": [true, "under_scored_"]}, "var_prefixes": {"uint32_t": "ui32", "int*": "intp"}, "function_prefixes": {"uint16_t": "ui16", "uint32_t": "ui32"}, "skip_one_char_variables": false}