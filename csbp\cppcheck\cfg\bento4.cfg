<?xml version="1.0"?>
<def format="2">
  <define name="AP4_DEFINE_DYNAMIC_CAST_ANCHOR" value=""/>
  <define name="AP4_IMPLEMENT_DYNAMIC_CAST_D" value=""/>
  <define name="AP4_FAILED(C)" value="C"/>
  <define name="AP4_ATOM_HEADER_SIZE_64" value="16"/>
  <define name="AP4_ATOM_HEADER_SIZE" value="8"/>
  <define name="AP4_ATOM_MAX_NAME_SIZE" value="256"/>
  <define name="AP4_ATOM_MAX_URI_SIZE" value="512"/>
  <define name="AP4_AVC_PROFILE_BASELINE" value="66"/>
  <define name="AP4_AVC_PROFILE_EXTENDED" value="88"/>
  <define name="AP4_AVC_PROFILE_HIGH_10" value="110"/>
  <define name="AP4_AVC_PROFILE_HIGH_422" value="122"/>
  <define name="AP4_AVC_PROFILE_HIGH_444" value="144"/>
  <define name="AP4_AVC_PROFILE_HIGH" value="100"/>
  <define name="AP4_AVC_PROFILE_MAIN" value="77"/>
  <define name="AP4_CENC_CIPHER_AES_128_CBC" value="2"/>
  <define name="AP4_CENC_CIPHER_AES_128_CTR" value="1"/>
  <define name="AP4_CENC_CIPHER_NONE" value="0"/>
  <define name="AP4_CENC_SAMPLE_ENCRYPTION_FLAG_OVERRIDE_TRACK_ENCRYPTION_DEFAULTS" value="1"/>
  <define name="AP4_CENC_SAMPLE_ENCRYPTION_FLAG_USE_SUB_SAMPLE_ENCRYPTION" value="2"/>
  <define name="AP4_COMMAND_TAG_ES_DESCRIPTOR_REMOVE_REF" value="0x07"/>
  <define name="AP4_COMMAND_TAG_ES_DESCRIPTOR_REMOVE" value="0x04"/>
  <define name="AP4_COMMAND_TAG_ES_DESCRIPTOR_UPDATE" value="0x03"/>
  <define name="AP4_COMMAND_TAG_IPMP_DESCRIPTOR_REMOVE" value="0x06"/>
  <define name="AP4_COMMAND_TAG_IPMP_DESCRIPTOR_UPDATE" value="0x05"/>
  <define name="AP4_COMMAND_TAG_OBJECT_DESCRIPTOR_EXECUTE" value="0x08"/>
  <define name="AP4_COMMAND_TAG_OBJECT_DESCRIPTOR_REMOVE" value="0x02"/>
  <define name="AP4_COMMAND_TAG_OBJECT_DESCRIPTOR_UPDATE" value="0x01"/>
  <define name="AP4_DESCRIPTOR_TAG_DECODER_CONFIG" value="0x04"/>
  <define name="AP4_DESCRIPTOR_TAG_DECODER_SPECIFIC_INFO" value="0x05"/>
  <define name="AP4_DESCRIPTOR_TAG_ES" value="0x03"/>
  <define name="AP4_DESCRIPTOR_TAG_IOD" value="0x02"/>
  <define name="AP4_DESCRIPTOR_TAG_MP4_IOD" value="0x10"/>
  <define name="AP4_DESCRIPTOR_TAG_MP4_OD" value="0x11"/>
  <define name="AP4_DESCRIPTOR_TAG_OD" value="0x01"/>
  <define name="AP4_DESCRIPTOR_TAG_SL_CONFIG" value="0x06"/>
  <define name="AP4_DV_PROFILE_DVAV_PEN" value="1"/>
  <define name="AP4_DV_PROFILE_DVAV_PER" value="0"/>
  <define name="AP4_DV_PROFILE_DVHE_DEN" value="3"/>
  <define name="AP4_DV_PROFILE_DVHE_DER" value="2"/>
  <define name="AP4_DV_PROFILE_DVHE_DTB" value="7"/>
  <define name="AP4_DV_PROFILE_DVHE_DTH" value="6"/>
  <define name="AP4_DV_PROFILE_DVHE_DTR" value="4"/>
  <define name="AP4_DV_PROFILE_DVHE_STN" value="5"/>
  <define name="AP4_FRAG_FLAG_SAMPLE_IS_DIFFERENCE" value="0x00010000"/>
  <define name="AP4_FULL_ATOM_HEADER_SIZE_64" value="20"/>
  <define name="AP4_FULL_ATOM_HEADER_SIZE" value="12"/>
  <define name="AP4_HEVC_CHROMA_FORMAT_420" value="1"/>
  <define name="AP4_HEVC_CHROMA_FORMAT_422" value="2"/>
  <define name="AP4_HEVC_CHROMA_FORMAT_444" value="3"/>
  <define name="AP4_HEVC_CHROMA_FORMAT_MONOCHROME" value="0"/>
  <define name="AP4_HEVC_PROFILE_MAIN_10" value="2"/>
  <define name="AP4_HEVC_PROFILE_MAIN_STILL_PICTURE" value="3"/>
  <define name="AP4_HEVC_PROFILE_MAIN" value="1"/>
  <define name="AP4_HEVC_PROFILE_REXT" value="4"/>
  <define name="AP4_MDHD_DEFAULT_GENERIC_TIMESCALE" value="1000"/>
  <define name="AP4_MDHD_DEFAULT_VIDEO_TIMESCALE" value="90000"/>
  <define name="AP4_MPEG2_STREAM_TYPE_ATSC_AC3" value="0x81"/>
  <define name="AP4_MPEG2_STREAM_TYPE_ATSC_EAC3" value="0x81"/>
  <define name="AP4_MPEG2_STREAM_TYPE_HEVC" value="0x24"/>
  <define name="AP4_MPEG2_STREAM_TYPE_ISO_IEC_13818_1_PES" value="0x06"/>
  <define name="AP4_MPEG2_TS_DEFAULT_PID_AUDIO" value="0x101"/>
  <define name="AP4_MPEG2_TS_DEFAULT_PID_PMT" value="0x100"/>
  <define name="AP4_MPEG2_TS_DEFAULT_PID_VIDEO" value="0x102"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_AAC_LC" value="2"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_AAC_LTP" value="4"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_AAC_MAIN" value="1"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_AAC_SCALABLE" value="6"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_AAC_SSR" value="3"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_ALGORITHMIC_SYNTHESIS" value="16"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_ALS" value="36"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_CELP" value="8"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_DST" value="35"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_ER_AAC_ELD" value="39"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_ER_AAC_LC" value="17"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_ER_AAC_LD" value="23"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_ER_AAC_LTP" value="19"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_ER_AAC_SCALABLE" value="20"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_ER_BSAC" value="22"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_ER_CELP" value="24"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_ER_HILN" value="26"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_ER_HVXC" value="25"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_ER_PARAMETRIC" value="27"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_ER_TWINVQ" value="21"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_GENERAL_MIDI" value="15"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_HVXC" value="9"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_LAYER_1" value="32"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_LAYER_2" value="33"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_LAYER_3" value="34"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_MAIN_SYNTHETIC" value="13"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_MPEG_SURROUND" value="30"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_PS" value="29"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_SBR" value="5"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_SLS_NON_CORE" value="38"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_SLS" value="37"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_SMR_MAIN" value="41"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_SMR_SIMPLE" value="40"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_SSC" value="28"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_TTSI" value="12"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_TWINVQ" value="7"/>
  <define name="AP4_MPEG4_AUDIO_OBJECT_TYPE_WAVETABLE_SYNTHESIS" value="14"/>
  <define name="AP4_OMA_DCF_ENCRYPTION_METHOD_AES_CBC" value="1"/>
  <define name="AP4_OMA_DCF_ENCRYPTION_METHOD_AES_CTR" value="2"/>
  <define name="AP4_OMA_DCF_ENCRYPTION_METHOD_NULL" value="0"/>
  <define name="AP4_OMA_DCF_PADDING_SCHEME_NONE" value="0"/>
  <define name="AP4_OMA_DCF_PADDING_SCHEME_RFC_2630" value="1"/>
  <define name="AP4_PROTECTION_SCHEME_VERSION_CENC_10" value="0x00010000"/>
  <define name="AP4_PROTECTION_SCHEME_VERSION_OMA_20" value="0x00000200"/>
  <define name="AP4_PROTECTION_SCHEME_VERSION_PIFF_10" value="0x00010000"/>
  <define name="AP4_PROTECTION_SCHEME_VERSION_PIFF_11" value="0x00010001"/>
  <define name="AP4_TFHD_FLAG_BASE_DATA_OFFSET_PRESENT" value="0x00001"/>
  <define name="AP4_TFHD_FLAG_DEFAULT_BASE_IS_MOOF" value="0x20000"/>
  <define name="AP4_TFHD_FLAG_DEFAULT_SAMPLE_DURATION_PRESENT" value="0x00008"/>
  <define name="AP4_TFHD_FLAG_DEFAULT_SAMPLE_FLAGS_PRESENT" value="0x00020"/>
  <define name="AP4_TFHD_FLAG_DEFAULT_SAMPLE_SIZE_PRESENT" value="0x00010"/>
  <define name="AP4_TFHD_FLAG_DURATION_IS_EMPTY" value="0x10000"/>
  <define name="AP4_TFHD_FLAG_SAMPLE_DESCRIPTION_INDEX_PRESENT" value="0x00002"/>
  <define name="AP4_TRACK_DEFAULT_MOVIE_TIMESCALE" value="1000"/>
  <define name="AP4_TRACK_FLAG_ENABLED" value="0x0001"/>
  <define name="AP4_TRACK_FLAG_IN_MOVIE" value="0x0002"/>
  <define name="AP4_TRACK_FLAG_IN_PREVIEW" value="0x0004"/>
  <define name="AP4_TRUN_FLAG_DATA_OFFSET_PRESENT" value="0x0001"/>
  <define name="AP4_TRUN_FLAG_FIRST_SAMPLE_FLAGS_PRESENT" value="0x0004"/>
  <define name="AP4_TRUN_FLAG_SAMPLE_COMPOSITION_TIME_OFFSET_PRESENT" value="0x0800"/>
  <define name="AP4_TRUN_FLAG_SAMPLE_DURATION_PRESENT" value="0x0100"/>
  <define name="AP4_TRUN_FLAG_SAMPLE_FLAGS_PRESENT" value="0x0400"/>
  <define name="AP4_TRUN_FLAG_SAMPLE_SIZE_PRESENT" value="0x0200"/>
  <podtype name="AP4_UI08" sign="u" size="1"/>
  <podtype name="AP4_UI16" sign="u" size="2"/>
  <podtype name="AP4_UI32" sign="u" size="4"/>
  <podtype name="AP4_Result" sign="s" size="4"/>
  <function name="BitWriter::Write">
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
  </function>
</def>
