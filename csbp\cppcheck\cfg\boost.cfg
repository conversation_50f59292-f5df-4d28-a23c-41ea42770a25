<?xml version="1.0"?>
<def format="2">
  <!-- Boost Library Configuration -->
  <!-- Documentation: https://www.boost.org/doc/libs/ -->
  <!-- ########## Boost defines / macros ########## -->
  <define name="BOOST_GLOBAL_FIXTURE(name)" value=""/>
  <define name="BOOST_TEST_GLOBAL_FIXTURE(name)" value=""/>
  <define name="BOOST_CLASS_EXPORT_IMPLEMENT(T)" value=""/>
  <define name="BOOST_STRONG_TYPEDEF(a,b)" value=""/>
  <define name="BOOST_SERIALIZATION_SPLIT_MEMBER()" value=""/>
  <define name="BOOST_CLASS_VERSION(class,version)" value=""/>
  <define name="BOOST_TYPEOF_REGISTER_TYPE(x)" value=""/>
  <define name="BOOST_TYPEOF_REGISTER_TEMPLATE(x, params)" value=""/>
  <define name="BOOST_PP_LIST_FOR_EACH(macro, data, list)" value=""/>
  <define name="BOOST_PP_SEQ_FOR_EACH(macro, data, list)" value=""/>
  <define name="BOOST_PP_SEQ_TRANSFORM(macro, data, list)" value=""/>
  <define name="BOOST_PP_CAT(a, b)" value=""/>
  <define name="BOOST_PP_REPEAT(a,b,c)" value=""/>
  <define name="BOOST_PP_STRINGIZE(t)" value=""/>
  <!-- https://www.boost.org/doc/libs/master/doc/html/foreach.html -->
  <define name="BOOST_FOREACH(A,B)" value="for (A: B)"/>
  <!-- https://www.boost.org/doc/libs/master/libs/config/doc/html/boost_config/boost_macro_reference.html -->
  <define name="BOOST_FORCEINLINE" value="inline"/>
  <define name="BOOST_NOINLINE" value=""/>
  <define name="BOOST_NORETURN" value="[[noreturn]]"/>
  <define name="BOOST_THROW_EXCEPTION(X)" value="boost::throw_exception(X)"/>
  <define name="BOOST_LIKELY(X)" value="(X)"/>
  <define name="BOOST_UNLIKELY(X)" value="(X)"/>
  <define name="BOOST_FIXTURE_TEST_SUITE(name, fixture, ...)" value="struct name : fixture {" />
  <define name="BOOST_FIXTURE_TEST_SUITE_END()" value="};"/>
  <define name="BOOST_PYTHON_FUNCTION_OVERLOADS(foo_overloads, foo, x, y)" value=""/>
  <define name="BOOST_AUTO_TEST_SUITE(name, ...)" value="namespace name {" />
  <define name="BOOST_AUTO_TEST_SUITE_END()" value="}" />
  <define name="BOOST_ASSERT(condition)" value="assert(condition)"/>
  <define name="BOOST_TEST(condition, ...)" value="static_cast&lt;void&gt;(static_cast&lt;bool&gt;(condition))" />
  <define name="BOOST_TEST_REQUIRE(condition, ...)" value="static_cast&lt;void&gt;(static_cast&lt;bool&gt;(condition))" />
  <define name="BOOST_WARN(condition)" value="static_cast&lt;void&gt;(static_cast&lt;bool&gt;(condition))" />
  <define name="BOOST_WARN_MESSAGE(condition, msg)" value="static_cast&lt;void&gt;(static_cast&lt;bool&gt;(condition)); std::string(msg)" />
  <define name="BOOST_WARN_EQUAL(a, b)" value="static_cast&lt;void&gt;((a) == (b))" />
  <define name="BOOST_WARN_NE(a, b)" value="static_cast&lt;void&gt;((a) != (b))" />
  <define name="BOOST_WARN_GT(a, b)" value="static_cast&lt;void&gt;((a) &gt; (b))" />
  <define name="BOOST_WARN_GE(a, b)" value="static_cast&lt;void&gt;((a) &gt;= (b))" />
  <define name="BOOST_WARN_LT(a, b)" value="static_cast&lt;void&gt;((a) &lt; (b))" />
  <define name="BOOST_WARN_LE(a, b)" value="static_cast&lt;void&gt;((a) &lt;= (b))" />
  <define name="BOOST_CHECK(condition)" value="static_cast&lt;void&gt;(static_cast&lt;bool&gt;(condition))" />
  <define name="BOOST_CHECK_MESSAGE(condition, msg)" value="static_cast&lt;void&gt;(static_cast&lt;bool&gt;(condition)); std::string(msg)" />
  <define name="BOOST_CHECK_EQUAL(a, b)" value="static_cast&lt;void&gt;((a) == (b))" />
  <define name="BOOST_CHECK_NE(a, b)" value="static_cast&lt;void&gt;((a) != (b))" />
  <define name="BOOST_CHECK_GT(a, b)" value="static_cast&lt;void&gt;((a) &gt; (b))" />
  <define name="BOOST_CHECK_GE(a, b)" value="static_cast&lt;void&gt;((a) &gt;= (b))" />
  <define name="BOOST_CHECK_LT(a, b)" value="static_cast&lt;void&gt;((a) &lt; (b))" />
  <define name="BOOST_CHECK_LE(a, b)" value="static_cast&lt;void&gt;((a) &lt;= (b))" />
  <define name="BOOST_REQUIRE(condition)" value="static_cast&lt;void&gt;(static_cast&lt;bool&gt;(condition))" />
  <define name="BOOST_REQUIRE_MESSAGE(condition, msg)" value="static_cast&lt;void&gt;(static_cast&lt;bool&gt;(condition)); std::string(msg)" />
  <define name="BOOST_REQUIRE_EQUAL(a, b)" value="static_cast&lt;void&gt;(static_cast&lt;bool&gt;((a) == (b)))" />
  <define name="BOOST_REQUIRE_NE(a, b)" value="static_cast&lt;void&gt;((a) != (b))" />
  <define name="BOOST_REQUIRE_GT(a, b)" value="static_cast&lt;void&gt;((a) &gt; (b))" />
  <define name="BOOST_REQUIRE_GE(a, b)" value="static_cast&lt;void&gt;((a) &gt;= (b))" />
  <define name="BOOST_REQUIRE_LT(a, b)" value="static_cast&lt;void&gt;((a) &lt; (b))" />
  <define name="BOOST_REQUIRE_LE(a, b)" value="static_cast&lt;void&gt;((a) &lt;= (b))" />
  <define name="BOOST_LOG_ATTRIBUTE_KEYWORD(keyword, name_, value_type_)" value="value_type_ keyword;"/>
  <define name="BOOST_TEST_DONT_PRINT_LOG_VALUE(the_type)" value="" />
  <!--Boost.Math Macros -->
  <define name="BOOST_MATH_NO_LONG_DOUBLE_MATH_FUNCTIONS" value=""/>
  <define name="BOOST_MATH_USE_FLOAT128" value=""/>
  <define name="BOOST_MATH_DISABLE_FLOAT128" value=""/>
  <define name="BOOST_MATH_NO_REAL_CONCEPT_TESTS" value=""/>
  <define name="BOOST_MATH_CONTROL_FP" value=""/>
  <define name="BOOST_MATH_NO_DEDUCED_FUNCTION_POINTERS" value=""/>
  <define name="BOOST_MATH_USE_C99" value=""/>
  <define name="BOOST_MATH_SMALL_CONSTANT(x)" value=""/>
  <define name="BOOST_MATH_BUGGY_LARGE_FLOAT_CONSTANTS" value=""/>
  <define name="BOOST_MATH_STD_USING" value=""/>
  <define name="BOOST_FPU_EXCEPTION_GUARD" value=""/>
  <define name="BOOST_MATH_INSTRUMENT" value=""/>
  <define name="BOOST_MATH_INSTRUMENT_CODE(x)" value=""/>
  <define name="BOOST_MATH_INSTRUMENT_VARIABLE(name)" value=""/>
  <define name="BOOST_MATH_INSTRUMENT_FPU" value=""/>
  <!--Boost.Math Tuning -->
  <define name="BOOST_MATH_POLY_METHOD" value=""/>
  <define name="BOOST_MATH_RATIONAL_METHOD" value=""/>
  <define name="BOOST_MATH_MAX_POLY_ORDER" value=""/>
  <define name="BOOST_MATH_INT_TABLE_TYPE" value=""/>
  <define name="BOOST_MATH_INT_VALUE_SUFFIX" value=""/>
  <!-- Tell cppcheck to interpret BOOST_AUTO_TEST_CASE as a function definition -->
  <define name="BOOST_AUTO_TEST_CASE(...)" value="void BOOST_AUTO_TEST_CASE_run(__VA_ARGS__)"/>
  <define name="BOOST_FIXTURE_TEST_CASE(name, fixture, ...)" value="struct name : fixture { void test_method(); }; void name::test_method()" />
  <define name="BOOST_FIXTURE_TEST_CASE_TEMPLATE(test_name, type_name, TL, F)" value="template&lt;typename type_name&gt; struct test_name : public F { void test_method(); }; template&lt;typename type_name&gt; void test_name&lt;type_name&gt;::test_method()" />
  <define name="BOOST_DATA_TEST_CASE(...)" value="void BOOST_DATA_TEST_CASE_run(__VA_ARGS__)"/>
  <define name="BOOST_DATA_TEST_CASE_F(...)" value="void BOOST_DATA_TEST_CASE_F_run(__VA_ARGS__)"/>
  <define name="BOOST_PYTHON_MODULE(str)" value="void BOOST_PYTHON_MODULE_definition(str)"/>
  <define name="BOOST_SCOPED_ENUM_DECLARE_BEGIN(x)" value=""/>
  <define name="BOOST_SCOPED_ENUM_DECLARE_END(x)" value=""/>
  <define name="BOOST_TTI_HAS_TYPE(x)" value=""/>
  <define name="BOOST_TTI_MEMBER_TYPE(x)" value=""/>
  <define name="BOOST_TTI_HAS_STATIC_MEMBER_FUNCTION(x)" value=""/>
  <!-- ########## Boost containers ########## -->
  <!-- boost containers that are similar to std containers -->
  <container id="boostArray" startPattern="boost :: array &lt;" inherits="stdArray"/>
  <container id="boostCircularBuffer" startPattern="boost :: circular_buffer &lt;" inherits="stdContainer"/>
  <container id="boostList" startPattern="boost :: list|slist &lt;" inherits="stdList"/>
  <container id="boostMultiMap" startPattern="boost :: flat_multimap|multimap|unordered_multimap &lt;" inherits="stdMultiMap"/>
  <container id="boostMultiSet" startPattern="boost :: flat_multiset|multiset &lt;" inherits="stdMultiSet"/>
  <container id="boostMap" startPattern="boost :: map|flat_map|unordered_map &lt;" inherits="stdMap"/>
  <container id="boostSet" startPattern="boost :: set|flat_set|unordered_set &lt;" inherits="stdSet"/>
  <container id="boostVector" startPattern="boost :: vector|small_vector &lt;" inherits="stdVector"/>
  <container id="boostStableVector" startPattern="boost :: stable_vector|static_vector &lt;" inherits="stdVectorDeque"/>
  <container id="boostDeque" startPattern="boost :: deque &lt;" inherits="stdDeque"/>
  <container id="boostStringView" startPattern="boost :: string_view" inherits="stdStringView"/>
  <!-- ########## Boost smart pointers ########## -->
  <!-- https://www.boost.org/doc/libs/1_70_0/libs/smart_ptr/doc/html/smart_ptr.html -->
  <smart-pointer class-name="boost::scoped_ptr">
    <unique/>
  </smart-pointer>
  <smart-pointer class-name="boost::scoped_array"/>
  <smart-pointer class-name="boost::shared_ptr"/>
  <smart-pointer class-name="boost::weak_ptr"/>
  <smart-pointer class-name="boost::intrusive_ptr"/>
  <smart-pointer class-name="boost::local_shared_ptr"/>
  <!-- https://www.boost.org/doc/libs/1_70_0/doc/html/boost/movelib/unique_ptr.html -->
  <smart-pointer class-name="boost::movelib::unique_ptr">
    <unique/>
  </smart-pointer>
  <!-- ########## Boost functions ########## -->
  <!-- https://www.boost.org/doc/libs/1_69_0/doc/html/boost/algorithm/join.html -->
  <!-- template<typename SequenceSequenceT, typename Range1T>
       range_value< SequenceSequenceT >::type join(const SequenceSequenceT & Input, const Range1T & Separator); -->
  <function name="boost::algorithm::join">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-null/>
    </arg>
  </function>
  <!-- https://www.boost.org/doc/libs/1_71_0/libs/bind/doc/html/bind.html -->
  <function name="boost::bind">
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="any"/>
  </function>
  <!-- boost::math classification functions -->
  <!-- https://www.boost.org/doc/libs/1_69_0/libs/math/doc/html/math_toolkit/fpclass.html -->
  <!-- template <class T> int fpclassify(T t); -->
  <function name="boost::math::fpclassify">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- template <class T> bool isfinite(T z); -->
  <!-- template <class T> bool isinf(T t); -->
  <!-- template <class T> bool isnan(T t); -->
  <!-- template <class T> bool isnormal(T t); -->
  <function name="boost::math::isfinite,boost::math::isinf,boost::math::isnan,boost::math::isnormal">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- boost::math sign functions -->
  <!-- https://www.boost.org/doc/libs/1_69_0/libs/math/doc/html/math_toolkit/sign_functions.html -->
  <!-- template <class T> int sign (const T& z); -->
  <function name="boost::math::sign">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue type="int">arg1==0?0:(arg1&lt;0?-1:1)</returnValue>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- template<class T> int signbit(T x); -->
  <function name="boost::math::signbit">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue type="int">arg1&lt;0?1:0</returnValue>
    <arg nr="1">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- template <class T, class U> calculated-result-type copysign (const T& x, const U& y); -->
  <function name="boost::math::copysign">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- template <class T> inline T changesign (const T& z); -->
  <function name="boost::math::changesign">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- boost::math constants -->
  <!-- https://www.boost.org/doc/libs/1_69_0/libs/math/doc/html/math_toolkit/constants.html -->
  <function name="boost::math::constants::half">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>5.000000000000000000000000000000000000e-01</returnValue>
  </function>
  <function name="boost::math::constants::third">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>3.333333333333333333333333333333333333e-01</returnValue>
  </function>
  <function name="boost::math::constants::twothirds">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>6.666666666666666666666666666666666666e-01</returnValue>
  </function>
  <function name="boost::math::constants::two_thirds">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>6.666666666666666666666666666666666666e-01</returnValue>
  </function>
  <function name="boost::math::constants::sixth">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>1.66666666666666666666666666666666666666666e-01</returnValue>
  </function>
  <function name="boost::math::constants::three_quarters">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>7.500000000000000000000000000000000000e-01</returnValue>
  </function>
  <function name="boost::math::constants::root_two">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>1.414213562373095048801688724209698078e+00</returnValue>
  </function>
  <function name="boost::math::constants::root_three">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>1.732050807568877293527446341505872366e+00</returnValue>
  </function>
  <function name="boost::math::constants::half_root_two">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>7.071067811865475244008443621048490392e-01</returnValue>
  </function>
  <function name="boost::math::constants::ln_two">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>6.931471805599453094172321214581765680e-01</returnValue>
  </function>
  <function name="boost::math::constants::ln_ln_two">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>-3.665129205816643270124391582326694694e-01</returnValue>
  </function>
  <function name="boost::math::constants::root_ln_four">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>1.177410022515474691011569326459699637e+00</returnValue>
  </function>
  <function name="boost::math::constants::one_div_root_two">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>7.071067811865475244008443621048490392e-01</returnValue>
  </function>
  <function name="boost::math::constants::pi">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>3.141592653589793238462643383279502884e+00</returnValue>
  </function>
  <function name="boost::math::constants::half_pi">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>1.570796326794896619231321691639751442e+00</returnValue>
  </function>
  <function name="boost::math::constants::third_pi">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>1.047197551196597746154214461093167628e+00</returnValue>
  </function>
  <function name="boost::math::constants::sixth_pi">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>5.235987755982988730771072305465838140e-01</returnValue>
  </function>
  <function name="boost::math::constants::two_pi">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>6.283185307179586476925286766559005768e+00</returnValue>
  </function>
  <function name="boost::math::constants::two_thirds_pi">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>2.094395102393195492308428922186335256e+00</returnValue>
  </function>
  <function name="boost::math::constants::three_quarters_pi">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>2.356194490192344928846982537459627163e+00</returnValue>
  </function>
  <function name="boost::math::constants::four_thirds_pi">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>4.188790204786390984616857844372670512e+00</returnValue>
  </function>
  <function name="boost::math::constants::one_div_two_pi">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>1.591549430918953357688837633725143620e-01</returnValue>
  </function>
  <function name="boost::math::constants::one_div_root_two_pi">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>3.989422804014326779399460599343818684e-01</returnValue>
  </function>
  <function name="boost::math::constants::root_pi">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>1.772453850905516027298167483341145182e+00</returnValue>
  </function>
  <function name="boost::math::constants::root_half_pi">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>1.253314137315500251207882642405522626e+00</returnValue>
  </function>
  <function name="boost::math::constants::root_two_pi">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>2.506628274631000502415765284811045253e+00</returnValue>
  </function>
  <function name="boost::math::constants::log_root_two_pi">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>9.189385332046727417803297364056176398e-01</returnValue>
  </function>
  <function name="boost::math::constants::one_div_root_pi">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>5.641895835477562869480794515607725858e-01</returnValue>
  </function>
  <function name="boost::math::constants::root_one_div_pi">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>5.641895835477562869480794515607725858e-01</returnValue>
  </function>
  <function name="boost::math::constants::pi_minus_three">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>1.415926535897932384626433832795028841e-01</returnValue>
  </function>
  <function name="boost::math::constants::four_minus_pi">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>8.584073464102067615373566167204971158e-01</returnValue>
  </function>
  <function name="boost::math::constants::pow23_four_minus_pi">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>7.953167673715975443483953350568065807e-01</returnValue>
  </function>
  <function name="boost::math::constants::pi_pow_e">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>2.245915771836104547342715220454373502e+01</returnValue>
  </function>
  <function name="boost::math::constants::pi_sqr">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>9.869604401089358618834490999876151135e+00</returnValue>
  </function>
  <function name="boost::math::constants::pi_sqr_div_six">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>1.644934066848226436472415166646025189e+00</returnValue>
  </function>
  <function name="boost::math::constants::pi_cubed">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>3.100627668029982017547631506710139520e+01</returnValue>
  </function>
  <function name="boost::math::constants::cbrt_pi">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>1.464591887561523263020142527263790391e+00</returnValue>
  </function>
  <function name="boost::math::constants::one_div_cbrt_pi">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>6.827840632552956814670208331581645981e-01</returnValue>
  </function>
  <function name="boost::math::constants::e">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>2.718281828459045235360287471352662497e+00</returnValue>
  </function>
  <function name="boost::math::constants::exp_minus_half">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>6.065306597126334236037995349911804534e-01</returnValue>
  </function>
  <function name="boost::math::constants::e_pow_pi">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>2.314069263277926900572908636794854738e+01</returnValue>
  </function>
  <function name="boost::math::constants::root_e">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>1.648721270700128146848650787814163571e+00</returnValue>
  </function>
  <function name="boost::math::constants::log10_e">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>4.342944819032518276511289189166050822e-01</returnValue>
  </function>
  <function name="boost::math::constants::one_div_log10_e">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>2.302585092994045684017991454684364207e+00</returnValue>
  </function>
  <function name="boost::math::constants::ln_ten">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>2.302585092994045684017991454684364207e+00</returnValue>
  </function>
  <function name="boost::math::constants::degree">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>1.745329251994329576923690768488612713e-02</returnValue>
  </function>
  <function name="boost::math::constants::radian">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>5.729577951308232087679815481410517033e+01</returnValue>
  </function>
  <function name="boost::math::constants::sin_one">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>8.414709848078965066525023216302989996e-01</returnValue>
  </function>
  <function name="boost::math::constants::cos_one">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>5.403023058681397174009366074429766037e-01</returnValue>
  </function>
  <function name="boost::math::constants::sinh_one">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>1.175201193643801456882381850595600815e+00</returnValue>
  </function>
  <function name="boost::math::constants::cosh_one">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>1.543080634815243778477905620757061682e+00</returnValue>
  </function>
  <function name="boost::math::constants::phi">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>1.618033988749894848204586834365638117e+00</returnValue>
  </function>
  <function name="boost::math::constants::ln_phi">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>4.812118250596034474977589134243684231e-01</returnValue>
  </function>
  <function name="boost::math::constants::one_div_ln_phi">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>2.078086921235027537601322606117795767e+00</returnValue>
  </function>
  <function name="boost::math::constants::euler">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>5.772156649015328606065120900824024310e-01</returnValue>
  </function>
  <function name="boost::math::constants::one_div_euler">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>1.732454714600633473583025315860829681e+00</returnValue>
  </function>
  <function name="boost::math::constants::euler_sqr">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>3.331779238077186743183761363552442266e-01</returnValue>
  </function>
  <function name="boost::math::constants::zeta_two">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>1.644934066848226436472415166646025189e+00</returnValue>
  </function>
  <function name="boost::math::constants::zeta_three">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>1.202056903159594285399738161511449990e+00</returnValue>
  </function>
  <function name="boost::math::constants::catalan">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>9.159655941772190150546035149323841107e-01</returnValue>
  </function>
  <function name="boost::math::constants::glaisher">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>1.282427129100622636875342568869791727e+00</returnValue>
  </function>
  <function name="boost::math::constants::khinchin">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>2.685452001065306445309714835481795693e+00</returnValue>
  </function>
  <function name="boost::math::constants::extreme_value_skewness">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>1.139547099404648657492793019389846112e+00</returnValue>
  </function>
  <function name="boost::math::constants::rayleigh_skewness">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>6.311106578189371381918993515442277798e-01</returnValue>
  </function>
  <function name="boost::math::constants::rayleigh_kurtosis">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>3.245089300687638062848660410619754415e+00</returnValue>
  </function>
  <function name="boost::math::constants::rayleigh_kurtosis_excess">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>2.450893006876380628486604106197544154e-01</returnValue>
  </function>
  <function name="boost::math::constants::two_div_pi">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>6.366197723675813430755350534900574481e-01</returnValue>
  </function>
  <function name="boost::math::constants::root_two_div_pi">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue>7.978845608028653558798921198687637369e-01</returnValue>
  </function>
  <!-- boost::math rounding functions -->
  <!-- https://www.boost.org/doc/libs/1_69_0/libs/math/doc/html/math_toolkit/rounding/round.html -->
  <!-- template <class T> T round(const T& v); -->
  <!-- template <class T, class Policy> T round(const T& v, const Policy&); -->
  <function name="boost::math::round">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- template <class T> int iround(const T& v); -->
  <!-- template <class T, class Policy> int iround(const T& v, const Policy&); -->
  <function name="boost::math::iround">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- template <class T> long lround(const T& v); -->
  <!-- template <class T, class Policy> long lround(const T& v, const Policy&); -->
  <function name="boost::math::lround">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue type="long int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- template <class T> long long llround(const T& v); -->
  <!-- template <class T, class Policy> long long llround(const T& v, const Policy&); -->
  <function name="boost::math::llround">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue type="long long int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- boost::filesystem query functions -->
  <!-- https://www.boost.org/doc/libs/1_69_0/libs/filesystem/doc/reference.html -->
  <!-- bool exists(file_status s) noexcept; -->
  <!-- bool exists(const path& p); -->
  <!-- bool exists(const path& p, system::error_code& ec) noexcept; -->
  <!-- bool is_directory(file_status s) noexcept; -->
  <!-- bool is_directory(const path& p); -->
  <!-- bool is_directory(const path& p, system::error_code& ec) noexcept; -->
  <!-- The other functions are similar ... -->
  <function name="boost::filesystem::exists,boost::filesystem::is_directory,boost::filesystem::is_empty,boost::filesystem::is_other,boost::filesystem::is_regular_file,boost::filesystem::is_symlink">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" default="0" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- std::time_t last_write_time(const path& p); -->
  <!-- std::time_t last_write_time(const path& p, system::error_code& ec); -->
  <function name="boost::filesystem::last_write_time">
    <noreturn>false</noreturn>
    <pure/>
    <leak-ignore/>
    <returnValue type="time_t"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- void permissions(const path& p, perms prms); -->
  <!-- void permissions(const path& p, perms prms, system::error_code& ec); -->
  <function name="boost::filesystem::permissions">
    <noreturn>false</noreturn>
    <pure/>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" default="0" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- path read_symlink(const path& p); -->
  <!-- path read_symlink(const path& p, system::error_code& ec); -->
  <function name="boost::filesystem::read_symlink">
    <noreturn>false</noreturn>
    <pure/>
    <leak-ignore/>
    <returnValue type="path"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- path relative(const path& p, system::error_code& ec); -->
  <!-- path relative(const path& p, const path& base=current_path());          -->
  <!-- path relative(const path& p, const path& base, system::error_code& ec); -->
  <function name="boost::filesystem::relative">
    <noreturn>false</noreturn>
    <pure/>
    <leak-ignore/>
    <returnValue type="path"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool equivalent(const path& p1, const path& p2); -->
  <!-- bool equivalent(const path& p1, const path& p2, system::error_code& ec); -->
  <function name="boost::filesystem::equivalent">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" default="0" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- uintmax_t file_size(const path& p); -->
  <!-- uintmax_t file_size(const path& p, system::error_code& ec); -->
  <!-- uintmax_t hard_link_count(const path& p); -->
  <!-- uintmax_t hard_link_count(const path& p, system::error_code& ec); -->
  <function name="boost::filesystem::file_size,boost::filesystem::hard_link_count">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue type="uintmax_t"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- const path& initial_path(); -->
  <!-- const path& initial_path(system::error_code& ec); -->
  <function name="boost::filesystem::initial_path">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <returnValue type="const path &amp;"/>
    <arg nr="1" default="0" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- boost::filesystem actions -->
  <!-- bool remove(const path& p); -->
  <!-- bool remove(const path& p, system::error_code& ec); -->
  <function name="boost::filesystem::remove">
    <noreturn>false</noreturn>
    <pure/>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" default="0" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- uintmax_t remove_all(const path& p); -->
  <!-- uintmax_t remove_all(const path& p, system::error_code& ec); -->
  <function name="boost::filesystem::remove_all">
    <noreturn>false</noreturn>
    <pure/>
    <leak-ignore/>
    <returnValue type="uintmax_t"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" default="0" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- void rename(const path& old_p, const path& new_p); -->
  <!-- void rename(const path& old_p, const path& new_p, system::error_code& ec); -->
  <function name="boost::filesystem::rename">
    <noreturn>false</noreturn>
    <pure/>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" default="0" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- void resize_file(const path& p, uintmax_t new_size); -->
  <!-- void resize_file(const path& p, uintmax_t new_size, system::error_code& ec); -->
  <function name="boost::filesystem::resize_file">
    <noreturn>false</noreturn>
    <pure/>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" default="0" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- space_info space(const path& p); -->
  <!-- space_info space(const path& p, system::error_code& ec); -->
  <function name="boost::filesystem::space">
    <noreturn>false</noreturn>
    <pure/>
    <leak-ignore/>
    <returnValue type="space_info"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" default="0" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- file_status status(const path& p); -->
  <!-- file_status status(const path& p, system::error_code& ec) noexcept; -->
  <function name="boost::filesystem::status">
    <noreturn>false</noreturn>
    <pure/>
    <leak-ignore/>
    <returnValue type="file_status"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- bool status_known(file_status s) noexcept; -->
  <function name="boost::filesystem::status_known">
    <noreturn>false</noreturn>
    <pure/>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- file_status symlink_status(const path& p); -->
  <!-- file_status symlink_status(const path& p, system::error_code& ec) noexcept; -->
  <function name="boost::filesystem::symlink_status">
    <noreturn>false</noreturn>
    <pure/>
    <leak-ignore/>
    <returnValue type="file_status"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- path system_complete(const path& p); -->
  <!-- path system_complete(const path& p, system::error_code& ec); -->
  <function name="boost::filesystem::system_complete">
    <noreturn>false</noreturn>
    <pure/>
    <leak-ignore/>
    <returnValue type="path"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- path temp_directory_path(); -->
  <!-- path temp_directory_path(system::error_code& ec); -->
  <function name="boost::filesystem::temp_directory_path">
    <noreturn>false</noreturn>
    <pure/>
    <leak-ignore/>
    <returnValue type="path"/>
    <arg nr="1" default="0" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- path unique_path(const path& model="%%%%-%%%%-%%%%-%%%%"); -->
  <!-- path unique_path(const path& model, system::error_code& ec); -->
  <function name="boost::filesystem::unique_path">
    <noreturn>false</noreturn>
    <pure/>
    <leak-ignore/>
    <returnValue type="path"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- path weakly_canonical(const path& p); -->
  <!-- path weakly_canonical(const path& p, system::error_code& ec); -->
  <function name="boost::filesystem::weakly_canonical">
    <noreturn>false</noreturn>
    <pure/>
    <leak-ignore/>
    <returnValue type="path"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- path absolute(const path& p, const path& base=current_path()); -->
  <function name="boost::filesystem::absolute">
    <noreturn>false</noreturn>
    <pure/>
    <leak-ignore/>
    <returnValue type="path"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="current_path()" direction="in">
      <not-bool/>
    </arg>
  </function>
  <!-- path canonical(const path& p, const path& base = current_path()); -->
  <!-- path canonical(const path& p, system::error_code& ec); -->
  <!-- path canonical(const path& p, const path& base, system::error_code& ec); -->
  <function name="boost::filesystem::canonical">
    <noreturn>false</noreturn>
    <pure/>
    <leak-ignore/>
    <returnValue type="path"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void copy(const path& from, const path& to); -->
  <!-- void copy(const path& from, const path& to, system::error_code& ec); -->
  <!-- void copy_directory(const path& from, const path& to); -->
  <!-- void copy_directory(const path& from, const path& to, system::error_code& ec); -->
  <!-- void copy_symlink(const path& existing_symlink, const path& new_symlink); -->
  <!-- void copy_symlink(const path& existing_symlink, const path& new_symlink, system::error_code& ec); -->
  <function name="boost::filesystem::copy,boost::filesystem::copy_directory,boost::filesystem::copy_symlinks">
    <noreturn>false</noreturn>
    <pure/>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" default="0" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- void copy_file(const path& from, const path& to); -->
  <!-- void copy_file(const path& from, const path& to, system::error_code& ec); -->
  <!-- void copy_file(const path& from, const path& to, copy_option option); -->
  <!-- void copy_file(const path& from, const path& to, copy_option option, system::error_code& ec); -->
  <function name="boost::filesystem::copy_file">
    <noreturn>false</noreturn>
    <pure/>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" default="0">
      <not-bool/>
    </arg>
    <arg nr="4" default="0" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- bool create_directories(const path& p); -->
  <!-- bool create_directories(const path& p, system::error_code& ec); -->
  <!-- bool create_directory(const path& p); -->
  <!-- bool create_directory(const path& p, system::error_code& ec); -->
  <function name="boost::filesystem::create_directories,boost::filesystem::create_directory">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <pure/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" default="0" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- void create_directory_symlink(const path& to, const path& new_symlink); -->
  <!-- void create_directory_symlink(const path& to, const path& new_symlink, system::error_code& ec); -->
  <!-- void create_hard_link(const path& to, const path& new_hard_link); -->
  <!-- void create_hard_link(const path& to, const path& new_hard_link, system::error_code& ec); -->
  <!-- void create_symlink(const path& to, const path& new_symlink); -->
  <!-- void create_symlink(const path& to, const path& new_symlink, system::error_code& ec); -->
  <!-- void rename(const path& old_p, const path& new_p); -->
  <!-- void rename(const path& old_p, const path& new_p, system::error_code& ec); -->
  <function name="boost::filesystem::create_directory_symlink,boost::filesystem::create_hard_link,boost::filesystem::create_symlink,boost::filesystem::rename">
    <noreturn>false</noreturn>
    <pure/>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" default="0" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- endian conversion functions -->
  <!-- https://www.boost.org/doc/libs/1_69_0/libs/endian/doc/conversion.html -->
  <!-- int8_t   endian_reverse(int8_t x) noexcept; -->
  <!-- int16_t  endian_reverse(int16_t x) noexcept; -->
  <!-- int32_t  endian_reverse(int32_t x) noexcept; -->
  <!-- int64_t  endian_reverse(int64_t x) noexcept; -->
  <!-- uint8_t  endian_reverse(uint8_t x) noexcept; -->
  <!-- uint16_t endian_reverse(uint16_t x) noexcept; -->
  <!-- uint32_t endian_reverse(uint32_t x) noexcept; -->
  <!-- uint64_t endian_reverse(uint64_t x) noexcept; -->
  <!-- template <class EndianReversible> EndianReversible big_to_native(EndianReversible x) noexcept; -->
  <!-- template <class EndianReversible> EndianReversible native_to_big(EndianReversible x) noexcept; -->
  <!-- template <class EndianReversible> EndianReversible little_to_native(EndianReversible x) noexcept; -->
  <!-- template <class EndianReversible> EndianReversible native_to_little(EndianReversible x) noexcept; -->
  <!-- template <order O1, order O2, class EndianReversible> EndianReversible conditional_reverse(EndianReversible x) noexcept; -->
  <!-- template <class EndianReversible> EndianReversible conditional_reverse(EndianReversible x, order order1, order order2) noexcept;  -->
  <function name="boost::endian::endian_reverse,boost::endian::big_to_native,boost::endian::native_to_big,boost::endian::little_to_native,boost::endian::native_to_little,boost::endian::conditional_reverse">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="2" default="0" direction="in">
      <not-bool/>
    </arg>
    <arg nr="3" default="0" direction="in">
      <not-bool/>
    </arg>
  </function>
  <!-- template <class EndianReversible> void endian_reverse_inplace(EndianReversible& x) noexcept; -->
  <!-- template <class EndianReversibleInplace> void big_to_native_inplace(EndianReversibleInplace& x) noexcept; -->
  <!-- template <class EndianReversibleInplace> void native_to_big_inplace(EndianReversibleInplace& x) noexcept; -->
  <!-- template <class EndianReversibleInplace> void little_to_native_inplace(EndianReversibleInplace& x) noexcept; -->
  <!-- template <class EndianReversibleInplace> void native_to_little_inplace(EndianReversibleInplace& x) noexcept; -->
  <!-- template <order O1, order O2, class EndianReversibleInplace> void conditional_reverse_inplace(EndianReversibleInplace& x) noexcept; -->
  <!-- template <class EndianReversibleInplace> void conditional_reverse_inplace(EndianReversibleInplace& x, order order1, order order2) noexcept; -->
  <function name="boost::endian::endian_reverse_inplace,boost::endian::big_to_native_inplace,boost::endian::native_to_big,boost::endian::little_to_native_inplace,boost::endian::native_to_little_inplace,boost::endian::conditional_reverse_inplace">
    <noreturn>false</noreturn>
    <pure/>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="2" default="0" direction="in">
      <not-bool/>
    </arg>
    <arg nr="3" default="0" direction="in">
      <not-bool/>
    </arg>
  </function>
  <!-- boost::lexical_cast -->
  <!-- https://www.boost.org/doc/libs/1_69_0/doc/html/boost_lexical_cast.html -->
  <!-- template<typename Target, typename Source> Target lexical_cast(const Source& arg); -->
  <!-- template <typename Target> Target lexical_cast(const AnyCharacterType* chars, std::size_t count); -->
  <function name="boost::lexical_cast">
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <function name="boost::throw_exception">
    <noreturn>true</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <type-checks>
    <unusedvar>
      <checkFiniteLifetime>boost::lock_guard</checkFiniteLifetime>
      <checkFiniteLifetime>boost::mutex::scoped_lock</checkFiniteLifetime>
      <checkFiniteLifetime>boost::recursive_mutex::scoped_lock</checkFiniteLifetime>
      <checkFiniteLifetime>boost::unique_lock</checkFiniteLifetime>
      <checkFiniteLifetime>boost::shared_lock</checkFiniteLifetime>
    </unusedvar>
    <operatorEqVarError>
      <suppress>boost::mutex</suppress>
      <suppress>boost::recursive_mutex</suppress>
    </operatorEqVarError>
  </type-checks>
</def>
