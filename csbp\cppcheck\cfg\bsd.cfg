<?xml version="1.0"?>
<def format="2">
  <!-- int timerisset(struct timeval *tvp); -->
  <function name="timerisset">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
    </arg>
  </function>
  <!-- void timerclear(struct timeval *tvp); -->
  <function name="timerclear">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout">
      <not-null/>
    </arg>
  </function>
  <!-- void timeradd(struct timeval *a, struct timeval *b, struct timeval *res);-->
  <!-- void timersub(struct timeval *a, struct timeval *b, struct timeval *res);-->
  <function name="timeradd,timersub">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
    </arg>
  </function>
  <!-- int timercmp(struct timeval *a, struct timeval *b, CMP)-->
  <define name="timercmp(a,b,CMP)" value="(((a)-&gt;tv_sec == (b)-&gt;tv_sec) ? ((a)-&gt;tv_usec CMP (b)-&gt;tv_usec) : ((a)-&gt;tv_sec CMP (b)-&gt;tv_sec))"/>
  <!-- https://www.freebsd.org/cgi/man.cgi?query=fts&sektion=3 -->
  <!-- FTS * fts_open(char * const *path_argv, int options, int (*compar)(const FTSENT * const *, const FTSENT * const *)); -->
  <function name="fts_open">
    <returnValue type="FTS *"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
    <arg nr="3">
      <not-uninit/>
      <not-null/>
    </arg>
  </function>
  <!-- FTSENT * fts_read(FTS *ftsp); -->
  <function name="fts_read">
    <returnValue type="FTSENT *"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
      <not-null/>
    </arg>
  </function>
  <!-- FTSENT * fts_children(FTS *ftsp, int options); -->
  <function name="fts_children">
    <returnValue type="FTSENT *"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
  </function>
  <!-- int fts_set(FTS *ftsp, FTSENT *f, int options); -->
  <function name="fts_set">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="3">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
  </function>
  <!-- void fts_set_clientptr(FTS *ftsp, void *clientdata); -->
  <function name="fts_set_clientptr">
    <returnValue type="void"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
  </function>
  <!-- void * fts_get_clientptr(FTS *ftsp); -->
  <function name="fts_get_clientptr">
    <returnValue type="void *"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- FTS * fts_get_stream(FTSENT *f); -->
  <function name="fts_get_stream">
    <returnValue type="FTS *"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- int fts_close(FTS *ftsp); -->
  <function name="fts_close">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- https://www.freebsd.org/cgi/man.cgi?query=readpassphrase
       char *readpassphrase(const char *prompt,	char *buf, size_t bufsiz, int flags); -->
  <function name="readpassphrase">
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-null/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3">
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="4">
      <not-uninit/>
    </arg>
    <leak-ignore/>
  </function>
  <!-- https://www.freebsd.org/cgi/man.cgi?query=setfib&sektion=2 
      int setfib(int fib); -->
  <function name="setfib">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- https://www.freebsd.org/cgi/man.cgi?query=strtonum
       long long strtonum(const char *nptr, long long minval, long long maxval, const char **errstr); -->
  <function name="strtonum">
    <returnValue type="long long"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
    <arg nr="3">
      <not-uninit/>
    </arg>
    <arg nr="4">
      <not-uninit/>
    </arg>
    <leak-ignore/>
  </function>
  <!-- https://www.freebsd.org/cgi/man.cgi?query=strlcat
       size_t strlcat(char *dst, const char *src, size_t size); -->
  <function name="strlcat">
    <returnValue type="size_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <not-overlapping-data ptr1-arg="1" ptr2-arg="2" size-arg="3"/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
      <strz/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="2">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="3">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- https://www.freebsd.org/cgi/man.cgi?query=strlcpy
       size_t strlcpy(char * restrict dst, const char * restrict src, size_t dstsize); -->
  <function name="strlcpy">
    <returnValue type="size_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <not-overlapping-data ptr1-arg="1" ptr2-arg="2" size-arg="3"/>
    <arg nr="1">
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="2">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="3">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- https://linux.die.net/man/3/setbuffer -->
  <!-- void setbuffer(FILE *stream, char *buf, size_t size); -->
  <function name="setbuffer">
    <returnValue type="void"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- https://linux.die.net/man/3/setbuffer -->
  <!-- void setlinebuf(FILE *stream); -->
  <function name="setlinebuf">
    <returnValue type="void"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- https://linux.die.net/man/2/writev -->
  <!-- ssize_t readv(int fd, const struct iovec *iov, int iovcnt); -->
  <function name="readv">
    <leak-ignore/>
    <returnValue type="ssize_t"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- https://linux.die.net/man/2/writev -->
  <!-- ssize_t writev(int fd, const struct iovec *iov, int iovcnt); -->
  <function name="writev">
    <leak-ignore/>
    <returnValue type="ssize_t"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- https://linux.die.net/man/2/writev -->
  <!-- ssize_t preadv(int fd, const struct iovec *iov, int iovcnt, off_t offset); -->
  <function name="preadv">
    <leak-ignore/>
    <returnValue type="ssize_t"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- https://linux.die.net/man/2/writev -->
  <!-- ssize_t preadv2(int fd, const struct iovec *iov, int iovcnt, off_t offset, int flags); -->
  <function name="preadv2">
    <leak-ignore/>
    <returnValue type="ssize_t"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- https://linux.die.net/man/2/writev -->
  <!-- ssize_t pwritev(int fd, const struct iovec *iov, int iovcnt, off_t offset); -->
  <function name="pwritev">
    <leak-ignore/>
    <returnValue type="ssize_t"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- https://linux.die.net/man/2/writev -->
  <!-- ssize_t pwritev2(int fd, const struct iovec *iov, int iovcnt, off_t offset, int flags); -->
  <function name="pwritev2">
    <leak-ignore/>
    <returnValue type="ssize_t"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- https://www.freebsd.org/cgi/man.cgi?query=arc4random -->
  <!-- uint32_t arc4random(void); -->
  <function name="arc4random">
    <noreturn>false</noreturn>
    <returnValue type="uint32_t"/>
    <use-retval/>
  </function>
  <!-- void arc4random_buf(void *buf, size_t nbytes); -->
  <function name="arc4random_buf">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <valid>1:</valid>
    </arg>
  </function>
  <!-- uint32_t arc4random_uniform(uint32_t upper_bound); -->
  <function name="arc4random_uniform">
    <noreturn>false</noreturn>
    <returnValue type="uint32_t"/>
    <use-retval/>
    <arg nr="1">
      <not-uninit/>
      <valid>2:</valid>
    </arg>
  </function>
  <!-- void arc4random_stir(void); -->
  <function name="arc4random_stir">
    <noreturn>false</noreturn>
  </function>
  <!-- void arc4random_addrandom(unsigned char *dat, int datlen); -->
  <function name="arc4random_addrandom">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- https://www.freebsd.org/cgi/man.cgi?query=mmap -->
  <!-- void *mmap(void *addr, size_t length, int prot, int flags, int fd, off_t offset); -->
  <!-- In contrast to POSIX, the BSD mmap requires fd to be -1 if MAP_ANONYMOUS or MAP_ANON is specified -->
  <function name="mmap">
    <use-retval/>
    <returnValue type="void *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>1:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
      <valid>-1:</valid>
    </arg>
    <arg nr="6" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <function name="err,errx">
    <noreturn>true</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <formatstr/>
    <arg nr="2" direction="in">
      <formatstr/>
      <strz/>
      <not-uninit/>
    </arg>
  </function>
  <function name="errc">
    <noreturn>true</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <formatstr/>
    <arg nr="3" direction="in">
      <formatstr/>
      <strz/>
      <not-uninit/>
    </arg>
  </function>
  <function name="verr,verrx">
    <noreturn>true</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <strz/>
      <not-uninit/>
    </arg>
    <arg nr="3"/>
  </function>
  <function name="verrc">
    <noreturn>true</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <formatstr/>
    <arg nr="3" direction="in">
      <strz/>
      <not-uninit/>
    </arg>
    <arg nr="4"/>
  </function>
  <!-- void *reallocarray(void *ptr, size_t nmemb, size_t size); -->
  <function name="reallocarray">
    <returnValue type="void *"/>
    <use-retval/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <memory>
    <realloc init="false" buffer-size="calloc:2,3">reallocarray</realloc>
    <dealloc>free</dealloc>
  </memory>
  <podtype name="FTS"/>
  <podtype name="FTSENT"/>
  <!-- This type definitions refer to https://mandoc.bsd.lv/includes/sys/types.h.html -->
  <podtype name="u_char" sign="u" stdtype="char"/>
  <podtype name="u_short" sign="u" stdtype="short"/>
  <podtype name="u_int" sign="u" stdtype="int"/>
  <podtype name="u_long" sign="u" stdtype="long"/>
  <!-- Sys V compatibility types -->
  <podtype name="unchar" sign="u" stdtype="char"/>
  <podtype name="ushort" sign="u" stdtype="short"/>
  <podtype name="uint" sign="u" stdtype="int"/>
  <podtype name="ulong" sign="u" stdtype="long"/>
  <!-- https://linux.die.net/man/3/tailq_head -->
  <define name="TAILQ_HEAD(name,type)" value="struct name { type *first; type *last; }"/>
  <!-- https://man7.org/linux/man-pages/man2/lseek.2.html :
       Conforming to:
       SEEK_DATA and SEEK_HOLE are nonstandard extensions also present
       in Solaris, FreeBSD, and DragonFly BSD; they are proposed for
       inclusion in the next POSIX revision (Issue 8).-->
  <define name="SEEK_DATA" value="3"/>
  <define name="SEEK_HOLE" value="4"/>
  <!-- Old BSD names for the same constants; just for compatibility. -->
  <define name="L_INCR" value="SEEK_CUR"/>
  <define name="L_SET" value="SEEK_SET"/>
  <define name="L_XTND" value="SEEK_END"/>
</def>
