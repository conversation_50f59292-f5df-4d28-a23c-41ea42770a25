<?xml version="1.0"?>
<def format="2">
  <!-- cairo Library Configuration https://www.cairographics.org/ -->
  <!-- The cairo library is typically included by "#include <cairo.h>" -->
  <!-- ########## cairo Types ########## -->
  <define name="cairo_bool_t" value="int"/>
  <!-- TODO: Configure cairo_status_t as an enum when this is implemented in Cppcheck -->
  <podtype name="cairo_status_t"/>
  <define name="CAIRO_STATUS_READ_ERROR" value="10"/>
  <!-- ########## cairo Macros / Defines ########## -->
  <define name="CAIRO_HAS_MIME_SURFACE" value="1"/>
  <define name="CAIRO_MIME_TYPE_CCITT_FAX" value="&quot;image/g3fax&quot;"/>
  <define name="CAIRO_MIME_TYPE_CCITT_FAX_PARAMS" value="&quot;application/x-cairo.ccitt.params&quot;"/>
  <define name="CAIRO_MIME_TYPE_EPS" value="&quot;application/postscript&quot;"/>
  <define name="CAIRO_MIME_TYPE_EPS_PARAMS" value="&quot;application/x-cairo.eps.params&quot;"/>
  <define name="CAIRO_MIME_TYPE_JBIG2" value="&quot;application/x-cairo.jbig2&quot;"/>
  <define name="CAIRO_MIME_TYPE_JBIG2_GLOBAL" value="&quot;application/x-cairo.jbig2-global&quot;"/>
  <define name="CAIRO_MIME_TYPE_JBIG2_GLOBAL_ID" value="&quot;application/x-cairo.jbig2-global-id&quot;"/>
  <define name="CAIRO_MIME_TYPE_JP2" value="&quot;image/jp2&quot;"/>
  <define name="CAIRO_MIME_TYPE_JPEG" value="&quot;image/jpeg&quot;"/>
  <define name="CAIRO_MIME_TYPE_PNG" value="&quot;image/png&quot;"/>
  <define name="CAIRO_MIME_TYPE_URI" value="&quot;text/x-uri&quot;"/>
  <define name="CAIRO_MIME_TYPE_UNIQUE_ID" value="&quot;application/x-cairo.uuid&quot;"/>
  <!-- ########## cairo Allocation / Deallocation ########## -->
  <!-- ########## cairo Functions ########## -->
  <!-- cairo_t * cairo_create (cairo_surface_t *target); -->
  <function name="cairo_create">
    <noreturn>false</noreturn>
    <returnValue type="cairo_t *"/>
    <use-retval/>
    <arg nr="1">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void cairo_destroy (cairo_t *cr); -->
  <function name="cairo_destroy">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void cairo_line_to (cairo_t *cr, double x, double y); -->
  <!-- void cairo_move_to (cairo_t *cr, double x, double y); -->
  <function name="cairo_line_to,cairo_move_to">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- cairo_status_t cairo_status (cairo_t *cr); -->
  <function name="cairo_status">
    <noreturn>false</noreturn>
    <returnValue type="cairo_status_t"/>
    <use-retval/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- const char * cairo_status_to_string (cairo_status_t status); -->
  <function name="cairo_status_to_string">
    <noreturn>false</noreturn>
    <returnValue type="const char *"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void cairo_stroke (cairo_t *cr); -->
  <function name="cairo_stroke">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
</def>
