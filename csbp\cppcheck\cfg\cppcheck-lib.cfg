<?xml version="1.0"?>
<def>
  <function name="Token::astOperand1,Token::astOperand2,Token::astParent,Token::fileIndex,Token::linenr,Token::function,Token::link,Token::next,Token::previous,Token::scope,Token::str,Token::varId,Token::variable">
    <noreturn>false</noreturn>
    <use-retval/>
  </function>
  <!-- static bool Match(const Token *tok, const char pattern[], unsigned int varid = 0); -->
  <function name="Token::Match">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-null/>
      <strz/>
    </arg>
    <arg nr="3" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- static bool simpleMatch(const Token *tok, const char pattern[]); -->
  <function name="Token::simpleMatch">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-null/>
      <strz/>
    </arg>
  </function>
  <!-- const Token *linkAt(int index) const; -->
  <!-- const Token *tokAt(int index) const; -->
  <function name="Token::linkAt,Token::tokAt">
    <noreturn>false</noreturn>
    <returnValue type="const Token *"/>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>-50:50</valid>
      <not-bool/>
    </arg>
  </function>
  <!-- const std::string &strAt(int index) const; -->
  <function name="Token::strAt">
    <noreturn>false</noreturn>
    <returnValue type="const std::string &amp;"/>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>-50:50</valid>
      <not-bool/>
    </arg>
  </function>
  <!-- const ValueType *valueType() const; -->
  <function name="Token::valueType">
    <noreturn>false</noreturn>
    <returnValue type="const ValueType *"/>
    <use-retval/>
    <const/>
    <leak-ignore/>
  </function>
</def>
