<?xml version="1.0"?>
<def format="2">
  <!-- CppUnit sourceforge.net/projects/cppunit -->
  <define name="CPPUNIT_TEST_SUITE(testsuite)" value=""/>
  <define name="CPPUNIT_TEST(test)" value=""/>
  <define name="CPPUNIT_TEST_SUITE_END()" value=""/>
  <!-- <PERSON><PERSON> see http://cppunit.sourceforge.net/doc/cvs/group___assertions.html  -->
  <define name="CPPUNIT_ASSERT(condition)" value="{if (!(condition)) throw false;}"/>
  <define name="CPPUNIT_ASSERT_MESSAGE(message, condition)" value="{if (!(condition)) throw message;}"/>
  <define name="CPPUNIT_FAIL(message)" value="throw message;"/>
  <define name="CPPUNIT_ASSERT_EQUAL(expected, actual)" value="{if ((expected) != (actual)) throw false;}"/>
  <define name="CPPUNIT_ASSERT_EQUAL_MESSAGE(message, expected, actual)" value="{if ((expected) != (actual)) throw message;}"/>
  <define name="CPPUNIT_ASSERT_DOUBLES_EQUAL(expected, actual, delta)" value="{if (std::abs((expected) - (actual)) &gt; delta) throw false;}"/>
  <define name="CPPUNIT_ASSERT_DOUBLES_EQUAL_MESSAGE(message, expected, actual, delta)" value="{if (std::abs((expected) - (actual)) &gt; delta) throw message;}"/>
  <define name="CPPUNIT_ASSERT_THROW(expression, ExceptionType)" value=""/>
  <define name="CPPUNIT_ASSERT_THROW_MESSAGE(message, expression, ExceptionType)" value=""/>
  <define name="CPPUNIT_ASSERT_NO_THROW(expression)" value=""/>
  <define name="CPPUNIT_ASSERT_NO_THROW_MESSAGE(message, expression)" value=""/>
  <define name="CPPUNIT_ASSERT_ASSERTION_FAIL(assertion)" value="CPPUNIT_ASSERT_THROW( assertion, CPPUNIT_NS::Exception )"/>
  <define name="CPPUNIT_ASSERT_ASSERTION_FAIL_MESSAGE(message, assertion)" value="CPPUNIT_ASSERT_THROW_MESSAGE( message, assertion, CPPUNIT_NS::Exception )"/>
  <define name="CPPUNIT_ASSERT_ASSERTION_PASS(assertion)" value="CPPUNIT_ASSERT_NO_THROW( assertion )"/>
  <define name="CPPUNIT_ASSERT_ASSERTION_PASS_MESSAGE(message, assertion)" value="CPPUNIT_ASSERT_NO_THROW_MESSAGE( message, assertion )"/>
  <function name="CppUnit::TestCase::setUp,CppUnit::TestCase::tearDown">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
  </function>
  <!-- virtual void	addTest (Test *test) -->
  <function name="CppUnit::TestRunner::addTest">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- virtual void	run (TestResult &controller, const std::string &testPath="") -->
  <function name="CppUnit::TestRunner::run">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
  </function>
</def>
