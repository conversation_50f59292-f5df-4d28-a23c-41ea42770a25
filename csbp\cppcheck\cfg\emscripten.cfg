<?xml version="1.0"?>
<def format="2">
  <define name="EM_ASM(...)" value=""/>
  <define name="EM_ASM_DOUBLE(...)" value=""/>
  <define name="EM_ASM_INT(...)" value=""/>
  <define name="EM_ASM_PTR(...)" value=""/>
  <define name="EM_JS(...)" value=""/>
  <define name="MAIN_THREAD_ASYNC_EM_ASM(...)" value=""/>
  <define name="MAIN_THREAD_EM_ASM(...)" value=""/>
  <define name="MAIN_THREAD_EM_ASM_DOUBLE(...)" value=""/>
  <define name="MAIN_THREAD_EM_ASM_INT(...)" value=""/>
  <define name="MAIN_THREAD_EM_ASM_PTR(...)" value=""/>
  <define name="EMSCRIPTEN_BINDINGS(...)" value=""/>
</def>
