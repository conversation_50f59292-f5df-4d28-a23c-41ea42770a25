<?xml version="1.0"?>
<def format="2">
  <!-- ex GiNaC::mul::algebraic_subs_mul( const exmap & m, unsigned options ) const -->
  <function name="GiNaC::mul::algebraic_subs_mul">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool GiNaC::mul::can_be_further_expanded( const ex & e ) -->
  <function name="GiNaC::mul::can_be_further_expanded">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::mul::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::mul::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::mul::do_print_csrc( const print_csrc & c, unsigned level ) const -->
  <function name="GiNaC::mul::do_print_csrc">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::mul::do_print_latex( const print_latex & c, unsigned level ) const -->
  <function name="GiNaC::mul::do_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::mul::do_print_python_repr( const print_python_repr & c, unsigned level ) const -->
  <function name="GiNaC::mul::do_print_python_repr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- epvector GiNaC::mul::expandchildren( unsigned options ) const -->
  <function name="GiNaC::mul::expandchildren">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="epvector"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::mul::find_real_imag( ex & , ex &  ) const -->
  <function name="GiNaC::mul::find_real_imag">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- void GiNaC::mul::print_overall_coeff( const print_context & c, const char * mul_sym ) const -->
  <function name="GiNaC::mul::print_overall_coeff">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::integral::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::integral::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::integral::do_print_latex( const print_latex & c, unsigned level ) const -->
  <function name="GiNaC::integral::do_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- const paramset & GiNaC::fderivative::derivatives( void ) const -->
  <function name="GiNaC::fderivative::derivatives">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const paramset &amp;"/>
    <use-retval/>
    <const/>
  </function>
  <!-- void GiNaC::fderivative::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::fderivative::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::fderivative::do_print_csrc( const print_csrc & c, unsigned level ) const -->
  <function name="GiNaC::fderivative::do_print_csrc">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::fderivative::do_print_latex( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::fderivative::do_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::fderivative::do_print_tree( const print_tree & c, unsigned level ) const -->
  <function name="GiNaC::fderivative::do_print_tree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- const symmetry & GiNaC::antisymmetric2( void ) -->
  <function name="GiNaC::antisymmetric2">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const symmetry &amp;"/>
    <use-retval/>
  </function>
  <!-- const symmetry & GiNaC::antisymmetric3( void ) -->
  <function name="GiNaC::antisymmetric3">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const symmetry &amp;"/>
    <use-retval/>
  </function>
  <!-- const symmetry & GiNaC::antisymmetric4( void ) -->
  <function name="GiNaC::antisymmetric4">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const symmetry &amp;"/>
    <use-retval/>
  </function>
  <!-- int GiNaC::canonicalize( exvector::iterator v, const symmetry & symm ) -->
  <function name="GiNaC::canonicalize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- const symmetry & GiNaC::not_symmetric( void ) -->
  <function name="GiNaC::not_symmetric">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const symmetry &amp;"/>
    <use-retval/>
  </function>
  <!-- const symmetry & GiNaC::symmetric2( void ) -->
  <function name="GiNaC::symmetric2">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const symmetry &amp;"/>
    <use-retval/>
  </function>
  <!-- const symmetry & GiNaC::symmetric3( void ) -->
  <function name="GiNaC::symmetric3">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const symmetry &amp;"/>
    <use-retval/>
  </function>
  <!-- const symmetry & GiNaC::symmetric4( void ) -->
  <function name="GiNaC::symmetric4">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const symmetry &amp;"/>
    <use-retval/>
  </function>
  <!-- symmetry & GiNaC::symmetry::add( const symmetry & c ) -->
  <function name="GiNaC::symmetry::add">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="symmetry &amp;"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- int GiNaC::symmetry::canonicalize( exvector::iterator v, const symmetry & symm ) -->
  <function name="GiNaC::symmetry::canonicalize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void GiNaC::symmetry::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::symmetry::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::symmetry::do_print_tree( const print_tree & c, unsigned level ) const -->
  <function name="GiNaC::symmetry::do_print_tree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- symmetry_type GiNaC::symmetry::get_type( void ) -->
  <function name="GiNaC::symmetry::get_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="symmetry_type"/>
    <use-retval/>
  </function>
  <!-- bool GiNaC::symmetry::has_cyclic( void ) const -->
  <function name="GiNaC::symmetry::has_cyclic">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool GiNaC::symmetry::has_nonsymmetric( void ) const -->
  <function name="GiNaC::symmetry::has_nonsymmetric">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool GiNaC::symmetry::has_symmetry( void ) -->
  <function name="GiNaC::symmetry::has_symmetry">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- void GiNaC::symmetry::set_type( symmetry_type t ) -->
  <function name="GiNaC::symmetry::set_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::symmetry::validate( unsigned n ) -->
  <function name="GiNaC::symmetry::validate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int GiNaC::compare_all_equal::struct_compare( const T * t1, const T * t2 ) -->
  <function name="GiNaC::compare_all_equal::struct_compare">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- bool GiNaC::compare_all_equal::struct_is_equal( const T * t1, const T * t2 ) -->
  <function name="GiNaC::compare_all_equal::struct_is_equal">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- int GiNaC::compare_bitwise::struct_compare( const T * t1, const T * t2 ) -->
  <function name="GiNaC::compare_bitwise::struct_compare">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- bool GiNaC::compare_bitwise::struct_is_equal( const T * t1, const T * t2 ) -->
  <function name="GiNaC::compare_bitwise::struct_is_equal">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- int GiNaC::compare_std_less::struct_compare( const T * t1, const T * t2 ) -->
  <function name="GiNaC::compare_std_less::struct_compare">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- bool GiNaC::compare_std_less::struct_is_equal( const T * t1, const T * t2 ) -->
  <function name="GiNaC::compare_std_less::struct_is_equal">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- const char * GiNaC::structure::get_class_name( void ) -->
  <function name="GiNaC::structure::get_class_name">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const char *"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- cln::cl_I GiNaC::to_cl_I( const ex & e ) -->
  <function name="GiNaC::to_cl_I">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="cln::cl_I"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- exvector GiNaC::gcd_optimal_variables_order( const ex & A, const ex & B ) -->
  <function name="GiNaC::gcd_optimal_variables_order">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="exvector"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- bool GiNaC::primes_factory::has_primes( void ) -->
  <function name="GiNaC::primes_factory::has_primes">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- void GiNaC::mod_gcd( upoly & result, upoly A, upoly B ) -->
  <function name="GiNaC::mod_gcd">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- exp_vector_t GiNaC::degree_vector( ex e, const exvector & vars ) -->
  <function name="GiNaC::degree_vector">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="exp_vector_t"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- cln::cl_I GiNaC::integer_lcoeff( const ex & e, const exvector & vars ) -->
  <function name="GiNaC::integer_lcoeff">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="cln::cl_I"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::lcoeff_wrt( ex e, const exvector & x ) -->
  <function name="GiNaC::lcoeff_wrt">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- bool GiNaC::zerop( const exp_vector_t & v ) -->
  <function name="GiNaC::zerop">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::normalize_in_field( umodpoly & a, cln::cl_MI * content_ = nullptr ) -->
  <function name="GiNaC::normalize_in_field">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" default="nullptr" direction="inout"/>
  </function>
  <!-- T cln::the_one( const T & sample ) -->
  <function name="cln::the_one">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="T"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::euclid_gcd( ex A, ex B, const ex & var, const long p ) -->
  <function name="GiNaC::euclid_gcd">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::ex2upoly( umodpoly & u, ex e, const ex & var, const long p ) -->
  <function name="GiNaC::ex2upoly">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::umodpoly2ex( const umodpoly & a, const ex & var, const long p ) -->
  <function name="GiNaC::umodpoly2ex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- cln::cl_I cln::smod( const cln::cl_I & x, const cln::cl_I y ) -->
  <function name="cln::smod">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="cln::cl_I"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- bool GiNaC::remainder_in_field( umodpoly & r, const umodpoly & a, const umodpoly & b ) -->
  <function name="GiNaC::remainder_in_field">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- bool GiNaC::remainder_in_ring( T & r, const T & a, const T & b ) -->
  <function name="GiNaC::remainder_in_ring">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void GiNaC::power::do_print_csrc( const print_csrc & c, unsigned level ) const -->
  <function name="GiNaC::power::do_print_csrc">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::power::do_print_csrc_cl_N( const print_csrc_cl_N & c, unsigned level ) const -->
  <function name="GiNaC::power::do_print_csrc_cl_N">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::power::do_print_dflt( const print_dflt & c, unsigned level ) const -->
  <function name="GiNaC::power::do_print_dflt">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::power::do_print_latex( const print_latex & c, unsigned level ) const -->
  <function name="GiNaC::power::do_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::power::do_print_python( const print_python & c, unsigned level ) const -->
  <function name="GiNaC::power::do_print_python">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::power::do_print_python_repr( const print_python_repr & c, unsigned level ) const -->
  <function name="GiNaC::power::do_print_python_repr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::power::expand_add( const add & a, long n, unsigned options ) -->
  <function name="GiNaC::power::expand_add">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::power::expand_add_2( const add & a, unsigned options ) -->
  <function name="GiNaC::power::expand_add_2">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::power::expand_mul( const mul & m, const numeric & n, unsigned options, bool from_expand = false ) -->
  <function name="GiNaC::power::expand_mul">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" default="false" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void GiNaC::power::print_power( const print_context & c, const char * powersymbol, const char * openbrace, const char * closebrace, unsigned level ) const -->
  <function name="GiNaC::power::print_power">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::ptr::makewritable( void ) -->
  <function name="GiNaC::ptr::makewritable">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- int GiNaC::lexer::gettok( void ) -->
  <function name="GiNaC::lexer::gettok">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
  </function>
  <!-- void GiNaC::lexer::switch_input( std::istream * in ) -->
  <function name="GiNaC::lexer::switch_input">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- std::string GiNaC::lexer::tok2str( const int tok ) const -->
  <function name="GiNaC::lexer::tok2str">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::string"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- const prototype_table & GiNaC::get_builtin_reader( void ) -->
  <function name="GiNaC::get_builtin_reader">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const prototype_table &amp;"/>
    <use-retval/>
  </function>
  <!-- const prototype_table & GiNaC::get_default_reader( void ) -->
  <function name="GiNaC::get_default_reader">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const prototype_table &amp;"/>
    <use-retval/>
  </function>
  <!-- int GiNaC::parser::get_next_tok( void ) -->
  <function name="GiNaC::parser::get_next_tok">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
  </function>
  <!-- symtab GiNaC::parser::get_syms( void ) -->
  <function name="GiNaC::parser::get_syms">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="symtab"/>
    <use-retval/>
  </function>
  <!-- symtab & GiNaC::parser::get_syms( void ) -->
  <function name="GiNaC::parser::get_syms">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="symtab &amp;"/>
    <use-retval/>
  </function>
  <!-- ex GiNaC::parser::parse_expression( void ) -->
  <function name="GiNaC::parser::parse_expression">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex GiNaC::parser::parse_identifier_expr( void ) -->
  <function name="GiNaC::parser::parse_identifier_expr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex GiNaC::parser::parse_literal_expr( void ) -->
  <function name="GiNaC::parser::parse_literal_expr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex GiNaC::parser::parse_lst_expr( void ) -->
  <function name="GiNaC::parser::parse_lst_expr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex GiNaC::parser::parse_number_expr( void ) -->
  <function name="GiNaC::parser::parse_number_expr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex GiNaC::parser::parse_paren_expr( void ) -->
  <function name="GiNaC::parser::parse_paren_expr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex GiNaC::parser::parse_primary( void ) -->
  <function name="GiNaC::parser::parse_primary">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex GiNaC::parser::parse_unary_expr( void ) -->
  <function name="GiNaC::parser::parse_unary_expr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex GiNaC::collect_common_factors( const ex & e ) -->
  <function name="GiNaC::collect_common_factors">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::decomp_rational( const ex & a, const ex & x ) -->
  <function name="GiNaC::decomp_rational">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- bool GiNaC::divide( const ex & a, const ex & b, ex & q, bool check_args = true ) -->
  <function name="GiNaC::divide">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
    <arg nr="4" default="true" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::prem( const ex & a, const ex & b, const ex & x, bool check_args = true ) -->
  <function name="GiNaC::prem">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" default="true" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::quo( const ex & a, const ex & b, const ex & x, bool check_args = true ) -->
  <function name="GiNaC::quo">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" default="true" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::rem( const ex & a, const ex & b, const ex & x, bool check_args = true ) -->
  <function name="GiNaC::rem">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" default="true" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::resultant( const ex & e1, const ex & e2, const ex & s ) -->
  <function name="GiNaC::resultant">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- ex GiNaC::sprem( const ex & a, const ex & b, const ex & x, bool check_args = true ) -->
  <function name="GiNaC::sprem">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" default="true" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::sqrfree_parfrac( const ex & a, const symbol & x ) -->
  <function name="GiNaC::sqrfree_parfrac">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::delta_tensor( const ex & i1, const ex & i2 ) -->
  <function name="GiNaC::delta_tensor">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::lorentz_eps( const ex & i1, const ex & i2, const ex & i3, const ex & i4, bool pos_sig = false ) -->
  <function name="GiNaC::lorentz_eps">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in"/>
    <arg nr="5" default="false" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::lorentz_g( const ex & i1, const ex & i2, bool pos_sig = false ) -->
  <function name="GiNaC::lorentz_g">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" default="false" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::metric_tensor( const ex & i1, const ex & i2 ) -->
  <function name="GiNaC::metric_tensor">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void GiNaC::minkmetric::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::minkmetric::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::minkmetric::do_print_latex( const print_latex & c, unsigned level ) const -->
  <function name="GiNaC::minkmetric::do_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::spinmetric::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::spinmetric::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::spinmetric::do_print_latex( const print_latex & c, unsigned level ) const -->
  <function name="GiNaC::spinmetric::do_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::spinor_metric( const ex & i1, const ex & i2 ) -->
  <function name="GiNaC::spinor_metric">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void GiNaC::tensdelta::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::tensdelta::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::tensdelta::do_print_latex( const print_latex & c, unsigned level ) const -->
  <function name="GiNaC::tensdelta::do_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::tensepsilon::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::tensepsilon::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::tensepsilon::do_print_latex( const print_latex & c, unsigned level ) const -->
  <function name="GiNaC::tensepsilon::do_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::tensmetric::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::tensmetric::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool GiNaC::tensor::replace_contr_index( exvector::iterator self, exvector::iterator other ) const -->
  <function name="GiNaC::tensor::replace_contr_index">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- unsigned char GiNaC::color::get_representation_label( void ) -->
  <function name="GiNaC::color::get_representation_label">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned char"/>
    <use-retval/>
  </function>
  <!-- ex GiNaC::color_ONE( unsigned char rl = 0 ) -->
  <function name="GiNaC::color_ONE">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::color_T( const ex & a, unsigned char rl = 0 ) -->
  <function name="GiNaC::color_T">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::color_d( const ex & a, const ex & b, const ex & c ) -->
  <function name="GiNaC::color_d">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- ex GiNaC::color_f( const ex & a, const ex & b, const ex & c ) -->
  <function name="GiNaC::color_f">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- ex GiNaC::color_h( const ex & a, const ex & b, const ex & c ) -->
  <function name="GiNaC::color_h">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void GiNaC::su3d::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::su3d::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::su3d::do_print_latex( const print_latex & c, unsigned level ) const -->
  <function name="GiNaC::su3d::do_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::su3f::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::su3f::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::su3f::do_print_latex( const print_latex & c, unsigned level ) const -->
  <function name="GiNaC::su3f::do_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::su3one::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::su3one::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::su3one::do_print_latex( const print_latex & c, unsigned level ) const -->
  <function name="GiNaC::su3one::do_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::su3t::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::su3t::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::su3t::do_print_latex( const print_latex & c, unsigned level ) const -->
  <function name="GiNaC::su3t::do_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::basic::accept( GiNaC::visitor & v ) -->
  <function name="GiNaC::basic::accept">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- ex GiNaC::basic::add_indexed( const ex & self, const ex & other ) const -->
  <function name="GiNaC::basic::add_indexed">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void GiNaC::basic::archive( archive_node & n ) const -->
  <function name="GiNaC::basic::archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- unsigned GiNaC::basic::calchash( void ) const -->
  <function name="GiNaC::basic::calchash">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <use-retval/>
    <const/>
  </function>
  <!-- const basic & GiNaC::basic::clearflag( unsigned f ) -->
  <function name="GiNaC::basic::clearflag">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const basic &amp;"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::basic::coeff( const ex & s, int n = 1 ) const -->
  <function name="GiNaC::basic::coeff">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" default="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::basic::collect( const ex & s, bool distributed = false ) const -->
  <function name="GiNaC::basic::collect">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" default="false" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int GiNaC::basic::compare( const basic & other ) const -->
  <function name="GiNaC::basic::compare">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- int GiNaC::basic::compare_same_type( const basic & other ) const -->
  <function name="GiNaC::basic::compare_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::basic::conjugate( void ) const -->
  <function name="GiNaC::basic::conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool GiNaC::basic::contract_with( exvector::iterator self, exvector::iterator other, exvector & v ) const -->
  <function name="GiNaC::basic::contract_with">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void GiNaC::basic::dbgprint( void ) const -->
  <function name="GiNaC::basic::dbgprint">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
  </function>
  <!-- void GiNaC::basic::dbgprinttree( void ) const -->
  <function name="GiNaC::basic::dbgprinttree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
  </function>
  <!-- int GiNaC::basic::degree( const ex & s ) const -->
  <function name="GiNaC::basic::degree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::basic::derivative( const symbol & s ) const -->
  <function name="GiNaC::basic::derivative">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::basic::diff( const symbol & s, unsigned nth = 1 ) const -->
  <function name="GiNaC::basic::diff">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" default="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::basic::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::basic::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::basic::do_print_python_repr( const print_python_repr & c, unsigned level ) const -->
  <function name="GiNaC::basic::do_print_python_repr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::basic::do_print_tree( const print_tree & c, unsigned level ) const -->
  <function name="GiNaC::basic::do_print_tree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- basic * GiNaC::basic::duplicate( void ) -->
  <function name="GiNaC::basic::duplicate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="basic *"/>
    <use-retval/>
  </function>
  <!-- void GiNaC::basic::ensure_if_modifiable( void ) const -->
  <function name="GiNaC::basic::ensure_if_modifiable">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
  </function>
  <!-- ex GiNaC::basic::eval( void ) const -->
  <function name="GiNaC::basic::eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
    <const/>
  </function>
  <!-- ex GiNaC::basic::eval_indexed( const basic & i ) const -->
  <function name="GiNaC::basic::eval_indexed">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::basic::eval_integ( void ) const -->
  <function name="GiNaC::basic::eval_integ">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
    <const/>
  </function>
  <!-- ex GiNaC::basic::eval_ncmul( const exvector & v ) const -->
  <function name="GiNaC::basic::eval_ncmul">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::basic::evalf( void ) const -->
  <function name="GiNaC::basic::evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
    <const/>
  </function>
  <!-- ex GiNaC::basic::evalm( void ) const -->
  <function name="GiNaC::basic::evalm">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
    <const/>
  </function>
  <!-- ex GiNaC::basic::expand( unsigned options = 0 ) const -->
  <function name="GiNaC::basic::expand">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- exvector GiNaC::basic::get_free_indices( void ) const -->
  <function name="GiNaC::basic::get_free_indices">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="exvector"/>
    <use-retval/>
    <const/>
  </function>
  <!-- unsigned GiNaC::basic::gethash( void ) -->
  <function name="GiNaC::basic::gethash">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <use-retval/>
  </function>
  <!-- bool GiNaC::basic::has( const ex & other, unsigned options = 0 ) const -->
  <function name="GiNaC::basic::has">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- const basic & GiNaC::basic::hold( void ) const -->
  <function name="GiNaC::basic::hold">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const basic &amp;"/>
    <use-retval/>
    <const/>
  </function>
  <!-- ex GiNaC::basic::imag_part( void ) const -->
  <function name="GiNaC::basic::imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool GiNaC::basic::info( unsigned inf ) const -->
  <function name="GiNaC::basic::info">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- numeric GiNaC::basic::integer_content( void ) const -->
  <function name="GiNaC::basic::integer_content">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="numeric"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool GiNaC::basic::is_equal( const basic & other ) const -->
  <function name="GiNaC::basic::is_equal">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::basic::is_equal_same_type( const basic & other ) const -->
  <function name="GiNaC::basic::is_equal_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::basic::is_polynomial( const ex & var ) const -->
  <function name="GiNaC::basic::is_polynomial">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- int GiNaC::basic::ldegree( const ex & s ) const -->
  <function name="GiNaC::basic::ldegree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex & GiNaC::basic::let_op( size_t i ) -->
  <function name="GiNaC::basic::let_op">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex &amp;"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::basic::map( map_function & f ) const -->
  <function name="GiNaC::basic::map">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- bool GiNaC::basic::match( const ex & pattern, exmap & repls ) const -->
  <function name="GiNaC::basic::match">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- bool GiNaC::basic::match_same_type( const basic & other ) const -->
  <function name="GiNaC::basic::match_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- numeric GiNaC::basic::max_coefficient( void ) const -->
  <function name="GiNaC::basic::max_coefficient">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="numeric"/>
    <use-retval/>
    <const/>
  </function>
  <!-- size_t GiNaC::basic::nops( void ) const -->
  <function name="GiNaC::basic::nops">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="size_t"/>
    <use-retval/>
    <const/>
  </function>
  <!-- ex GiNaC::basic::normal( exmap & repl, exmap & rev_lookup, lst & modifier ) const -->
  <function name="GiNaC::basic::normal">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- ex GiNaC::basic::op( size_t i ) const -->
  <function name="GiNaC::basic::op">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- unsigned GiNaC::basic::precedence( void ) const -->
  <function name="GiNaC::basic::precedence">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <use-retval/>
    <const/>
  </function>
  <!-- void GiNaC::basic::print( const print_context & c, unsigned level = 0 ) const -->
  <function name="GiNaC::basic::print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::basic::read_archive( const archive_node & n, lst & syms ) -->
  <function name="GiNaC::basic::read_archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- ex GiNaC::basic::real_part( void ) const -->
  <function name="GiNaC::basic::real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
    <const/>
  </function>
  <!-- unsigned GiNaC::basic::return_type( void ) const -->
  <function name="GiNaC::basic::return_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <use-retval/>
    <const/>
  </function>
  <!-- return_type_t GiNaC::basic::return_type_tinfo( void ) const -->
  <function name="GiNaC::basic::return_type_tinfo">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="return_type_t"/>
    <use-retval/>
    <const/>
  </function>
  <!-- ex GiNaC::basic::scalar_mul_indexed( const ex & self, const numeric & other ) const -->
  <function name="GiNaC::basic::scalar_mul_indexed">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::basic::series( const relational & r, int order, unsigned options = 0 ) const -->
  <function name="GiNaC::basic::series">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- const basic & GiNaC::basic::setflag( unsigned f ) -->
  <function name="GiNaC::basic::setflag">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const basic &amp;"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::basic::smod( const numeric & xi ) const -->
  <function name="GiNaC::basic::smod">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::basic::subs( const exmap & m, unsigned options = 0 ) const -->
  <function name="GiNaC::basic::subs">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::basic::subs_one_level( const exmap & m, unsigned options ) const -->
  <function name="GiNaC::basic::subs_one_level">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::basic::to_polynomial( exmap & repl ) const -->
  <function name="GiNaC::basic::to_polynomial">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- ex GiNaC::basic::to_rational( exmap & repl ) const -->
  <function name="GiNaC::basic::to_rational">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- std::ostream & GiNaC::csrc( std::ostream & os ) -->
  <function name="GiNaC::csrc">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::ostream &amp;"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- std::ostream & GiNaC::csrc_cl_N( std::ostream & os ) -->
  <function name="GiNaC::csrc_cl_N">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::ostream &amp;"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- std::ostream & GiNaC::csrc_double( std::ostream & os ) -->
  <function name="GiNaC::csrc_double">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::ostream &amp;"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- std::ostream & GiNaC::csrc_float( std::ostream & os ) -->
  <function name="GiNaC::csrc_float">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::ostream &amp;"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- std::ostream & GiNaC::dflt( std::ostream & os ) -->
  <function name="GiNaC::dflt">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::ostream &amp;"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- std::ostream & GiNaC::index_dimensions( std::ostream & os ) -->
  <function name="GiNaC::index_dimensions">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::ostream &amp;"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- std::ostream & GiNaC::latex( std::ostream & os ) -->
  <function name="GiNaC::latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::ostream &amp;"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- std::ostream & GiNaC::no_index_dimensions( std::ostream & os ) -->
  <function name="GiNaC::no_index_dimensions">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::ostream &amp;"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- std::ostream & GiNaC::python( std::ostream & os ) -->
  <function name="GiNaC::python">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::ostream &amp;"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- std::ostream & GiNaC::python_repr( std::ostream & os ) -->
  <function name="GiNaC::python_repr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::ostream &amp;"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- std::ostream & GiNaC::tree( std::ostream & os ) -->
  <function name="GiNaC::tree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::ostream &amp;"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void GiNaC::relational::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::relational::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::relational::do_print_python_repr( const print_python_repr & c, unsigned level ) const -->
  <function name="GiNaC::relational::do_print_python_repr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::relational::lhs( void ) -->
  <function name="GiNaC::relational::lhs">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex GiNaC::relational::rhs( void ) -->
  <function name="GiNaC::relational::rhs">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- void GiNaC::relational::safe_bool_helper::nonnull( void ) -->
  <function name="GiNaC::relational::safe_bool_helper::nonnull">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- ex GiNaC::CatalanEvalf( void ) -->
  <function name="GiNaC::CatalanEvalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex GiNaC::EulerEvalf( void ) -->
  <function name="GiNaC::EulerEvalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- const numeric GiNaC::Li2( const numeric & x ) -->
  <function name="GiNaC::Li2">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::PiEvalf( void ) -->
  <function name="GiNaC::PiEvalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- void GiNaC::_numeric_digits::add_callback( digits_changed_callback callback ) -->
  <function name="GiNaC::_numeric_digits::add_callback">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::_numeric_digits::print( std::ostream & os ) const -->
  <function name="GiNaC::_numeric_digits::print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- const numeric GiNaC::abs( const numeric & x ) -->
  <function name="GiNaC::abs">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const numeric GiNaC::acos( const numeric & x ) -->
  <function name="GiNaC::acos">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const numeric GiNaC::acosh( const numeric & x ) -->
  <function name="GiNaC::acosh">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const numeric GiNaC::asin( const numeric & x ) -->
  <function name="GiNaC::asin">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const numeric GiNaC::asinh( const numeric & x ) -->
  <function name="GiNaC::asinh">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const numeric GiNaC::atanh( const numeric & x ) -->
  <function name="GiNaC::atanh">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const numeric GiNaC::bernoulli( const numeric & n ) -->
  <function name="GiNaC::bernoulli">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const numeric GiNaC::binomial( const numeric & n, const numeric & k ) -->
  <function name="GiNaC::binomial">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- const numeric GiNaC::cos( const numeric & x ) -->
  <function name="GiNaC::cos">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const numeric GiNaC::cosh( const numeric & x ) -->
  <function name="GiNaC::cosh">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- int GiNaC::csgn( const numeric & x ) -->
  <function name="GiNaC::csgn">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const numeric GiNaC::doublefactorial( const numeric & n ) -->
  <function name="GiNaC::doublefactorial">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const numeric GiNaC::exp( const numeric & x ) -->
  <function name="GiNaC::exp">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const numeric GiNaC::factorial( const numeric & n ) -->
  <function name="GiNaC::factorial">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const numeric GiNaC::fibonacci( const numeric & n ) -->
  <function name="GiNaC::fibonacci">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const numeric GiNaC::imag( const numeric & x ) -->
  <function name="GiNaC::imag">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::is_cinteger( const numeric & x ) -->
  <function name="GiNaC::is_cinteger">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::is_crational( const numeric & x ) -->
  <function name="GiNaC::is_crational">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::is_even( const numeric & x ) -->
  <function name="GiNaC::is_even">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::is_integer( const numeric & x ) -->
  <function name="GiNaC::is_integer">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::is_negative( const numeric & x ) -->
  <function name="GiNaC::is_negative">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::is_nonneg_integer( const numeric & x ) -->
  <function name="GiNaC::is_nonneg_integer">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::is_odd( const numeric & x ) -->
  <function name="GiNaC::is_odd">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::is_pos_integer( const numeric & x ) -->
  <function name="GiNaC::is_pos_integer">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::is_positive( const numeric & x ) -->
  <function name="GiNaC::is_positive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::is_prime( const numeric & x ) -->
  <function name="GiNaC::is_prime">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::is_rational( const numeric & x ) -->
  <function name="GiNaC::is_rational">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::is_real( const numeric & x ) -->
  <function name="GiNaC::is_real">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const numeric GiNaC::isqrt( const numeric & x ) -->
  <function name="GiNaC::isqrt">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const numeric GiNaC::log( const numeric & x ) -->
  <function name="GiNaC::log">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const numeric GiNaC::mod( const numeric & a, const numeric & b ) -->
  <function name="GiNaC::mod">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- const numeric GiNaC::numeric::add( const numeric & other ) const -->
  <function name="GiNaC::numeric::add">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const numeric & GiNaC::numeric::add_dyn( const numeric & other ) const -->
  <function name="GiNaC::numeric::add_dyn">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric &amp;"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- int GiNaC::numeric::compare( const numeric & other ) const -->
  <function name="GiNaC::numeric::compare">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- int GiNaC::numeric::csgn( void ) const -->
  <function name="GiNaC::numeric::csgn">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!-- const numeric GiNaC::numeric::denom( void ) const -->
  <function name="GiNaC::numeric::denom">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <use-retval/>
    <const/>
  </function>
  <!-- const numeric GiNaC::numeric::div( const numeric & other ) const -->
  <function name="GiNaC::numeric::div">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const numeric & GiNaC::numeric::div_dyn( const numeric & other ) const -->
  <function name="GiNaC::numeric::div_dyn">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric &amp;"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::numeric::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::numeric::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::numeric::do_print_csrc( const print_csrc & c, unsigned level ) const -->
  <function name="GiNaC::numeric::do_print_csrc">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::numeric::do_print_csrc_cl_N( const print_csrc_cl_N & c, unsigned level ) const -->
  <function name="GiNaC::numeric::do_print_csrc_cl_N">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::numeric::do_print_latex( const print_latex & c, unsigned level ) const -->
  <function name="GiNaC::numeric::do_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::numeric::do_print_python_repr( const print_python_repr & c, unsigned level ) const -->
  <function name="GiNaC::numeric::do_print_python_repr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::numeric::do_print_tree( const print_tree & c, unsigned level ) const -->
  <function name="GiNaC::numeric::do_print_tree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- const numeric GiNaC::numeric::imag( void ) const -->
  <function name="GiNaC::numeric::imag">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <use-retval/>
    <const/>
  </function>
  <!-- int GiNaC::numeric::int_length( void ) const -->
  <function name="GiNaC::numeric::int_length">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!-- const numeric GiNaC::numeric::inverse( void ) const -->
  <function name="GiNaC::numeric::inverse">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool GiNaC::numeric::is_cinteger( void ) const -->
  <function name="GiNaC::numeric::is_cinteger">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool GiNaC::numeric::is_crational( void ) const -->
  <function name="GiNaC::numeric::is_crational">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool GiNaC::numeric::is_equal( const numeric & other ) const -->
  <function name="GiNaC::numeric::is_equal">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::numeric::is_even( void ) const -->
  <function name="GiNaC::numeric::is_even">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool GiNaC::numeric::is_integer( void ) const -->
  <function name="GiNaC::numeric::is_integer">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool GiNaC::numeric::is_negative( void ) const -->
  <function name="GiNaC::numeric::is_negative">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool GiNaC::numeric::is_nonneg_integer( void ) const -->
  <function name="GiNaC::numeric::is_nonneg_integer">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool GiNaC::numeric::is_odd( void ) const -->
  <function name="GiNaC::numeric::is_odd">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool GiNaC::numeric::is_pos_integer( void ) const -->
  <function name="GiNaC::numeric::is_pos_integer">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool GiNaC::numeric::is_positive( void ) const -->
  <function name="GiNaC::numeric::is_positive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool GiNaC::numeric::is_prime( void ) const -->
  <function name="GiNaC::numeric::is_prime">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool GiNaC::numeric::is_rational( void ) const -->
  <function name="GiNaC::numeric::is_rational">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool GiNaC::numeric::is_real( void ) const -->
  <function name="GiNaC::numeric::is_real">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool GiNaC::numeric::is_zero( void ) const -->
  <function name="GiNaC::numeric::is_zero">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- const numeric GiNaC::numeric::mul( const numeric & other ) const -->
  <function name="GiNaC::numeric::mul">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const numeric & GiNaC::numeric::mul_dyn( const numeric & other ) const -->
  <function name="GiNaC::numeric::mul_dyn">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric &amp;"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const numeric GiNaC::numeric::numer( void ) const -->
  <function name="GiNaC::numeric::numer">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <use-retval/>
    <const/>
  </function>
  <!-- const numeric GiNaC::numeric::power( const numeric & other ) const -->
  <function name="GiNaC::numeric::power">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const numeric & GiNaC::numeric::power_dyn( const numeric & other ) const -->
  <function name="GiNaC::numeric::power_dyn">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric &amp;"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::numeric::print_numeric( const print_context & c, const char * par_open, const char * par_close, const char * imag_sym, const char * mul_sym, unsigned level ) const -->
  <function name="GiNaC::numeric::print_numeric">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="5" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="6" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- const numeric GiNaC::numeric::real( void ) const -->
  <function name="GiNaC::numeric::real">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <use-retval/>
    <const/>
  </function>
  <!-- numeric GiNaC::numeric::step( void ) const -->
  <function name="GiNaC::numeric::step">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="numeric"/>
    <use-retval/>
    <const/>
  </function>
  <!-- const numeric GiNaC::numeric::sub( const numeric & other ) const -->
  <function name="GiNaC::numeric::sub">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const numeric & GiNaC::numeric::sub_dyn( const numeric & other ) const -->
  <function name="GiNaC::numeric::sub_dyn">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric &amp;"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- cln::cl_N GiNaC::numeric::to_cl_N( void ) const -->
  <function name="GiNaC::numeric::to_cl_N">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="cln::cl_N"/>
    <use-retval/>
    <const/>
  </function>
  <!-- double GiNaC::numeric::to_double( void ) const -->
  <function name="GiNaC::numeric::to_double">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="double"/>
    <use-retval/>
    <const/>
  </function>
  <!-- int GiNaC::numeric::to_int( void ) const -->
  <function name="GiNaC::numeric::to_int">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!-- long GiNaC::numeric::to_long( void ) const -->
  <function name="GiNaC::numeric::to_long">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
    <const/>
  </function>
  <!-- int GiNaC::pole_error::degree( void ) const -->
  <function name="GiNaC::pole_error::degree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!-- const numeric GiNaC::real( const numeric & x ) -->
  <function name="GiNaC::real">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const numeric GiNaC::sin( const numeric & x ) -->
  <function name="GiNaC::sin">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const numeric GiNaC::sinh( const numeric & x ) -->
  <function name="GiNaC::sinh">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- numeric GiNaC::step( const numeric & x ) -->
  <function name="GiNaC::step">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="numeric"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const numeric GiNaC::tan( const numeric & x ) -->
  <function name="GiNaC::tan">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const numeric GiNaC::tanh( const numeric & x ) -->
  <function name="GiNaC::tanh">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- double GiNaC::to_double( const numeric & x ) -->
  <function name="GiNaC::to_double">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="double"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- int GiNaC::to_int( const numeric & x ) -->
  <function name="GiNaC::to_int">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- long GiNaC::to_long( const numeric & x ) -->
  <function name="GiNaC::to_long">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- unsigned GiNaC::make_hash_seed( const std::type_info & tinfo ) -->
  <function name="GiNaC::make_hash_seed">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- int GiNaC::expair::compare( const expair & other ) -->
  <function name="GiNaC::expair::compare">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const expair GiNaC::expair::conjugate( void ) const -->
  <function name="GiNaC::expair::conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const expair"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool GiNaC::expair::is_canonical_numeric( void ) -->
  <function name="GiNaC::expair::is_canonical_numeric">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- bool GiNaC::expair::is_equal( const expair & other ) -->
  <function name="GiNaC::expair::is_equal">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::expair::is_less( const expair & other ) -->
  <function name="GiNaC::expair::is_less">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::expair::print( std::ostream & os ) const -->
  <function name="GiNaC::expair::print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void GiNaC::expair::swap( expair & other ) -->
  <function name="GiNaC::expair::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- bool GiNaC::is_terminating( const pseries & s ) -->
  <function name="GiNaC::is_terminating">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::pseries::add_series( const pseries & other ) const -->
  <function name="GiNaC::pseries::add_series">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::pseries::coeffop( size_t i ) const -->
  <function name="GiNaC::pseries::coeffop">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::pseries::convert_to_poly( bool no_order = false ) const -->
  <function name="GiNaC::pseries::convert_to_poly">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" default="false" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void GiNaC::pseries::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::pseries::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::pseries::do_print_latex( const print_latex & c, unsigned level ) const -->
  <function name="GiNaC::pseries::do_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::pseries::do_print_python( const print_python & c, unsigned level ) const -->
  <function name="GiNaC::pseries::do_print_python">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::pseries::do_print_python_repr( const print_python_repr & c, unsigned level ) const -->
  <function name="GiNaC::pseries::do_print_python_repr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::pseries::do_print_tree( const print_tree & c, unsigned level ) const -->
  <function name="GiNaC::pseries::do_print_tree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::pseries::exponop( size_t i ) const -->
  <function name="GiNaC::pseries::exponop">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::pseries::get_point( void ) -->
  <function name="GiNaC::pseries::get_point">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex GiNaC::pseries::get_var( void ) -->
  <function name="GiNaC::pseries::get_var">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- bool GiNaC::pseries::is_compatible_to( const pseries & other ) -->
  <function name="GiNaC::pseries::is_compatible_to">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::pseries::is_terminating( void ) const -->
  <function name="GiNaC::pseries::is_terminating">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool GiNaC::pseries::is_zero( void ) -->
  <function name="GiNaC::pseries::is_zero">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- ex GiNaC::pseries::mul_const( const numeric & other ) const -->
  <function name="GiNaC::pseries::mul_const">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::pseries::mul_series( const pseries & other ) const -->
  <function name="GiNaC::pseries::mul_series">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::pseries::power_const( const numeric & p, int deg ) const -->
  <function name="GiNaC::pseries::power_const">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::pseries::print_series( const print_context & c, const char * openbrace, const char * closebrace, const char * mul_sym, const char * pow_sym, unsigned level ) const -->
  <function name="GiNaC::pseries::print_series">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="5" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="6" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- pseries GiNaC::pseries::shift_exponents( int deg ) const -->
  <function name="GiNaC::pseries::shift_exponents">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="pseries"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::series_to_poly( const ex & e ) -->
  <function name="GiNaC::series_to_poly">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::hold_ncmul( const exvector & v ) -->
  <function name="GiNaC::hold_ncmul">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::ncmul::append_factors( exvector & v, const ex & e ) const -->
  <function name="GiNaC::ncmul::append_factors">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- size_t GiNaC::ncmul::count_factors( const ex & e ) const -->
  <function name="GiNaC::ncmul::count_factors">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="size_t"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::ncmul::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::ncmul::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::ncmul::do_print_csrc( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::ncmul::do_print_csrc">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- exvector GiNaC::ncmul::expandchildren( unsigned options ) const -->
  <function name="GiNaC::ncmul::expandchildren">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="exvector"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- const exvector & GiNaC::ncmul::get_factors( void ) const -->
  <function name="GiNaC::ncmul::get_factors">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const exvector &amp;"/>
    <use-retval/>
    <const/>
  </function>
  <!-- ex GiNaC::ncmul::hold_ncmul( const exvector & v ) -->
  <function name="GiNaC::ncmul::hold_ncmul">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::ncmul::reeval_ncmul( const exvector & v ) -->
  <function name="GiNaC::ncmul::reeval_ncmul">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::reeval_ncmul( const exvector & v ) -->
  <function name="GiNaC::reeval_ncmul">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- container & GiNaC::container::append( const ex & b ) -->
  <function name="GiNaC::container::append">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="container &amp;"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const_iterator GiNaC::container::begin( void ) -->
  <function name="GiNaC::container::begin">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const_iterator"/>
    <use-retval/>
  </function>
  <!-- void GiNaC::container::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::container::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::container::do_print_python( const print_python & c, unsigned level ) const -->
  <function name="GiNaC::container::do_print_python">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::container::do_print_python_repr( const print_python_repr & c, unsigned level ) const -->
  <function name="GiNaC::container::do_print_python_repr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::container::do_print_tree( const print_tree & c, unsigned level ) const -->
  <function name="GiNaC::container::do_print_tree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- const_iterator GiNaC::container::end( void ) -->
  <function name="GiNaC::container::end">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const_iterator"/>
    <use-retval/>
  </function>
  <!-- char GiNaC::container::get_close_delim( void ) -->
  <function name="GiNaC::container::get_close_delim">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="char"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- unsigned GiNaC::container::get_default_flags( void ) -->
  <function name="GiNaC::container::get_default_flags">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- char GiNaC::container::get_open_delim( void ) -->
  <function name="GiNaC::container::get_open_delim">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="char"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- container & GiNaC::container::prepend( const ex & b ) -->
  <function name="GiNaC::container::prepend">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="container &amp;"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const_reverse_iterator GiNaC::container::rbegin( void ) -->
  <function name="GiNaC::container::rbegin">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const_reverse_iterator"/>
    <use-retval/>
  </function>
  <!-- container & GiNaC::container::remove_all( void ) -->
  <function name="GiNaC::container::remove_all">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="container &amp;"/>
    <use-retval/>
  </function>
  <!-- container & GiNaC::container::remove_first( void ) -->
  <function name="GiNaC::container::remove_first">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="container &amp;"/>
    <use-retval/>
  </function>
  <!-- container & GiNaC::container::remove_last( void ) -->
  <function name="GiNaC::container::remove_last">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="container &amp;"/>
    <use-retval/>
  </function>
  <!-- const_reverse_iterator GiNaC::container::rend( void ) -->
  <function name="GiNaC::container::rend">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const_reverse_iterator"/>
    <use-retval/>
  </function>
  <!-- container & GiNaC::container::sort( void ) -->
  <function name="GiNaC::container::sort">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="container &amp;"/>
    <use-retval/>
  </function>
  <!-- STLT GiNaC::container::subschildren( const exmap & m, unsigned options = 0 ) const -->
  <function name="GiNaC::container::subschildren">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="STLT"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::container::thiscontainer( const STLT & v ) -->
  <function name="GiNaC::container::thiscontainer">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- container & GiNaC::container::unique( void ) -->
  <function name="GiNaC::container::unique">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="container &amp;"/>
    <use-retval/>
  </function>
  <!-- void GiNaC::container::unique_( void ) -->
  <function name="GiNaC::container::unique_">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- epvector * GiNaC::conjugateepvector( const epvector &  ) -->
  <function name="GiNaC::conjugateepvector">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="epvector *"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::expairseq::can_make_flat( const expair & p ) const -->
  <function name="GiNaC::expairseq::can_make_flat">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::expairseq::canonicalize( void ) -->
  <function name="GiNaC::expairseq::canonicalize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void GiNaC::expairseq::combine_same_terms_sorted_seq( void ) -->
  <function name="GiNaC::expairseq::combine_same_terms_sorted_seq">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void GiNaC::expairseq::construct_from_2_ex( const ex & lh, const ex & rh ) -->
  <function name="GiNaC::expairseq::construct_from_2_ex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void GiNaC::expairseq::construct_from_epvector( const epvector & v, bool do_index_renaming = false ) -->
  <function name="GiNaC::expairseq::construct_from_epvector">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" default="false" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void GiNaC::expairseq::construct_from_exvector( const exvector & v ) -->
  <function name="GiNaC::expairseq::construct_from_exvector">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::expairseq::default_overall_coeff( void ) const -->
  <function name="GiNaC::expairseq::default_overall_coeff">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
    <const/>
  </function>
  <!-- void GiNaC::expairseq::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::expairseq::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::expairseq::do_print_tree( const print_tree & c, unsigned level ) const -->
  <function name="GiNaC::expairseq::do_print_tree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- epvector GiNaC::expairseq::evalchildren( void ) const -->
  <function name="GiNaC::expairseq::evalchildren">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="epvector"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool GiNaC::expairseq::expair_needs_further_processing( epp it ) -->
  <function name="GiNaC::expairseq::expair_needs_further_processing">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- epvector GiNaC::expairseq::expandchildren( unsigned options ) const -->
  <function name="GiNaC::expairseq::expandchildren">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="epvector"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool GiNaC::expairseq::is_canonical( void ) const -->
  <function name="GiNaC::expairseq::is_canonical">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- ex GiNaC::expairseq::recombine_pair_to_ex( const expair & p ) const -->
  <function name="GiNaC::expairseq::recombine_pair_to_ex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- expair GiNaC::expairseq::split_ex_to_pair( const ex & e ) const -->
  <function name="GiNaC::expairseq::split_ex_to_pair">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="expair"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- epvector GiNaC::expairseq::subschildren( const exmap & m, unsigned options = 0 ) const -->
  <function name="GiNaC::expairseq::subschildren">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="epvector"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::expairseq::thisexpairseq( const epvector & v, const ex & oc, bool do_index_renaming = false ) const -->
  <function name="GiNaC::expairseq::thisexpairseq">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" default="false" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void GiNaC::make_flat_inserter::combine_indices( const exvector & dummies_of_factor ) -->
  <function name="GiNaC::make_flat_inserter::combine_indices">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::make_flat_inserter::handle_factor( const ex & x, const ex & coeff ) -->
  <function name="GiNaC::make_flat_inserter::handle_factor">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- return_type_t GiNaC::make_return_type_t( const unsigned rl = 0 ) -->
  <function name="GiNaC::make_return_type_t">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="return_type_t"/>
    <arg nr="1" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- const char * GiNaC::registered_class_options::get_name( void ) -->
  <function name="GiNaC::registered_class_options::get_name">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const char *"/>
    <use-retval/>
  </function>
  <!-- const char * GiNaC::registered_class_options::get_parent_name( void ) -->
  <function name="GiNaC::registered_class_options::get_parent_name">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const char *"/>
    <use-retval/>
  </function>
  <!-- const std::vector<print_functor> & GiNaC::registered_class_options::get_print_dispatch_table( void ) -->
  <function name="GiNaC::registered_class_options::get_print_dispatch_table">
    <noreturn>false</noreturn>
    <leak-ignore/>
  </function>
  <!-- void GiNaC::registered_class_options::set_print_func( unsigned id, const print_functor & f ) -->
  <function name="GiNaC::registered_class_options::set_print_func">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in"/>
  </function>
  <!-- std::type_info const * GiNaC::registered_class_options::std::get_id( void ) -->
  <function name="GiNaC::registered_class_options::std::get_id">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::type_info const *"/>
    <use-retval/>
  </function>
  <!-- unsigned GiNaC::print_context_options::get_id( void ) -->
  <function name="GiNaC::print_context_options::get_id">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <use-retval/>
  </function>
  <!-- const char * GiNaC::print_context_options::get_name( void ) -->
  <function name="GiNaC::print_context_options::get_name">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const char *"/>
    <use-retval/>
  </function>
  <!-- const char * GiNaC::print_context_options::get_parent_name( void ) -->
  <function name="GiNaC::print_context_options::get_parent_name">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const char *"/>
    <use-retval/>
  </function>
  <!-- bool GiNaC::print_functor::is_valid( void ) -->
  <function name="GiNaC::print_functor::is_valid">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- unsigned GiNaC::crc32( const char * c, const unsigned len, const unsigned crcinit ) -->
  <function name="GiNaC::crc32">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <pure/>
    <arg nr="1" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool GiNaC::basic_partition_generator::mpartition2::next_partition( void ) -->
  <function name="GiNaC::basic_partition_generator::mpartition2::next_partition">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- int GiNaC::compare_pointers( const T * a, const T * b ) -->
  <function name="GiNaC::compare_pointers">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- bool GiNaC::composition_generator::coolmulti::finished( void ) -->
  <function name="GiNaC::composition_generator::coolmulti::finished">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- void GiNaC::composition_generator::coolmulti::next_permutation( void ) -->
  <function name="GiNaC::composition_generator::coolmulti::next_permutation">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- const std::vector<unsigned> & GiNaC::composition_generator::get( void ) -->
  <function name="GiNaC::composition_generator::get">
    <noreturn>false</noreturn>
    <leak-ignore/>
  </function>
  <!-- bool GiNaC::composition_generator::next( void ) -->
  <function name="GiNaC::composition_generator::next">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- void GiNaC::cyclic_permutation( It first, It last, It new_first, Swap swapit ) -->
  <function name="GiNaC::cyclic_permutation">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in"/>
  </function>
  <!-- unsigned GiNaC::golden_ratio_hash( uintptr_t n ) -->
  <function name="GiNaC::golden_ratio_hash">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- unsigned GiNaC::log2( unsigned n ) -->
  <function name="GiNaC::log2">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- const std::vector<unsigned> & GiNaC::partition_generator::get( void ) -->
  <function name="GiNaC::partition_generator::get">
    <noreturn>false</noreturn>
    <leak-ignore/>
  </function>
  <!-- bool GiNaC::partition_generator::next( void ) -->
  <function name="GiNaC::partition_generator::next">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- const std::vector<unsigned> & GiNaC::partition_with_zero_parts_generator::get( void ) -->
  <function name="GiNaC::partition_with_zero_parts_generator::get">
    <noreturn>false</noreturn>
    <leak-ignore/>
  </function>
  <!-- bool GiNaC::partition_with_zero_parts_generator::next( void ) -->
  <function name="GiNaC::partition_with_zero_parts_generator::next">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- unsigned GiNaC::rotate_left( unsigned n ) -->
  <function name="GiNaC::rotate_left">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::shaker_sort( It first, It last, Cmp comp, Swap swapit ) -->
  <function name="GiNaC::shaker_sort">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in"/>
  </function>
  <!-- void GiNaC::symbol::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::symbol::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::symbol::do_print_latex( const print_latex & c, unsigned level ) const -->
  <function name="GiNaC::symbol::do_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::symbol::do_print_python_repr( const print_python_repr & c, unsigned level ) const -->
  <function name="GiNaC::symbol::do_print_python_repr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::symbol::do_print_tree( const print_tree & c, unsigned level ) const -->
  <function name="GiNaC::symbol::do_print_tree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- std::string GiNaC::symbol::get_TeX_name( void ) const -->
  <function name="GiNaC::symbol::get_TeX_name">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::string"/>
    <use-retval/>
    <const/>
  </function>
  <!-- unsigned GiNaC::symbol::get_domain( void ) -->
  <function name="GiNaC::symbol::get_domain">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <use-retval/>
  </function>
  <!-- std::string GiNaC::symbol::get_name( void ) const -->
  <function name="GiNaC::symbol::get_name">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::string"/>
    <use-retval/>
    <const/>
  </function>
  <!-- void GiNaC::symbol::set_TeX_name( const std::string & n ) -->
  <function name="GiNaC::symbol::set_TeX_name">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::symbol::set_name( const std::string & n ) -->
  <function name="GiNaC::symbol::set_name">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::unlink_ex( const std::string filename ) -->
  <function name="GiNaC::unlink_ex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::class_info::dump_hierarchy( bool verbose = false ) -->
  <function name="GiNaC::class_info::dump_hierarchy">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" default="false" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void GiNaC::class_info::dump_tree( tree_node * n, const std::string & prefix, bool verbose ) -->
  <function name="GiNaC::class_info::dump_tree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- const class_info * GiNaC::class_info::find( const std::string & class_name ) -->
  <function name="GiNaC::class_info::find">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const class_info *"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::class_info::identify_parents( void ) -->
  <function name="GiNaC::class_info::identify_parents">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
  </function>
  <!-- void GiNaC::class_info::tree_node::add_child( tree_node * n ) -->
  <function name="GiNaC::class_info::tree_node::add_child">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- ex GiNaC::factor( const ex & poly, unsigned options = 0 ) -->
  <function name="GiNaC::factor">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::add::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::add::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::add::do_print_csrc( const print_csrc & c, unsigned level ) const -->
  <function name="GiNaC::add::do_print_csrc">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::add::do_print_latex( const print_latex & c, unsigned level ) const -->
  <function name="GiNaC::add::do_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::add::do_print_python_repr( const print_python_repr & c, unsigned level ) const -->
  <function name="GiNaC::add::do_print_python_repr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::add::print_add( const print_context & c, const char * openbrace, const char * closebrace, const char * mul_sym, unsigned level ) const -->
  <function name="GiNaC::add::print_add">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::canonicalize_clifford( const ex & e ) -->
  <function name="GiNaC::canonicalize_clifford">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::clifford::do_print_dflt( const print_dflt & c, unsigned level ) const -->
  <function name="GiNaC::clifford::do_print_dflt">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::clifford::do_print_latex( const print_latex & c, unsigned level ) const -->
  <function name="GiNaC::clifford::do_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::clifford::do_print_tree( const print_tree & c, unsigned level ) const -->
  <function name="GiNaC::clifford::do_print_tree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int GiNaC::clifford::get_commutator_sign( void ) -->
  <function name="GiNaC::clifford::get_commutator_sign">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
  </function>
  <!-- unsigned char GiNaC::clifford::get_representation_label( void ) -->
  <function name="GiNaC::clifford::get_representation_label">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned char"/>
    <use-retval/>
  </function>
  <!-- bool GiNaC::clifford::same_metric( const ex & other ) const -->
  <function name="GiNaC::clifford::same_metric">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::clifford_bar( const ex & e ) -->
  <function name="GiNaC::clifford_bar">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::clifford_inverse( const ex & e ) -->
  <function name="GiNaC::clifford_inverse">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- int GiNaC::clifford_max_label( const ex & e, bool ignore_ONE = false ) -->
  <function name="GiNaC::clifford_max_label">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" default="false" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::clifford_norm( const ex & e ) -->
  <function name="GiNaC::clifford_norm">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::clifford_prime( const ex & e ) -->
  <function name="GiNaC::clifford_prime">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::clifford_star( const ex & e ) -->
  <function name="GiNaC::clifford_star">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::clifford_star_bar( const ex & e, bool do_bar, unsigned options ) -->
  <function name="GiNaC::clifford_star_bar">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- lst GiNaC::clifford_to_lst( const ex & e, const ex & c, bool algebraic = true ) -->
  <function name="GiNaC::clifford_to_lst">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="lst"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" default="true" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::clifford_unit( const ex & mu, const ex & metr, unsigned char rl = 0 ) -->
  <function name="GiNaC::clifford_unit">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::cliffordunit::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::cliffordunit::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::cliffordunit::do_print_latex( const print_latex & c, unsigned level ) const -->
  <function name="GiNaC::cliffordunit::do_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::dirac_ONE( unsigned char rl = 0 ) -->
  <function name="GiNaC::dirac_ONE">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::dirac_gamma( const ex & mu, unsigned char rl = 0 ) -->
  <function name="GiNaC::dirac_gamma">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::dirac_gamma5( unsigned char rl = 0 ) -->
  <function name="GiNaC::dirac_gamma5">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::dirac_gammaL( unsigned char rl = 0 ) -->
  <function name="GiNaC::dirac_gammaL">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::dirac_gammaR( unsigned char rl = 0 ) -->
  <function name="GiNaC::dirac_gammaR">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::dirac_slash( const ex & e, const ex & dim, unsigned char rl = 0 ) -->
  <function name="GiNaC::dirac_slash">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::diracgamma5::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::diracgamma5::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::diracgamma5::do_print_latex( const print_latex & c, unsigned level ) const -->
  <function name="GiNaC::diracgamma5::do_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::diracgamma::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::diracgamma::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::diracgamma::do_print_latex( const print_latex & c, unsigned level ) const -->
  <function name="GiNaC::diracgamma::do_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::diracgammaL::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::diracgammaL::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::diracgammaL::do_print_latex( const print_latex & c, unsigned level ) const -->
  <function name="GiNaC::diracgammaL::do_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::diracgammaR::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::diracgammaR::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::diracgammaR::do_print_latex( const print_latex & c, unsigned level ) const -->
  <function name="GiNaC::diracgammaR::do_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::diracone::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::diracone::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::diracone::do_print_latex( const print_latex & c, unsigned level ) const -->
  <function name="GiNaC::diracone::do_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool GiNaC::is_clifford_tinfo( const return_type_t & ti ) -->
  <function name="GiNaC::is_clifford_tinfo">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::remove_dirac_ONE( const ex & e, unsigned char rl = 0, unsigned options = 0 ) -->
  <function name="GiNaC::remove_dirac_ONE">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- const numeric GiNaC::fsolve( const ex & f, const symbol & x, const numeric & x1, const numeric & x2 ) -->
  <function name="GiNaC::fsolve">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const numeric"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in"/>
  </function>
  <!-- bool GiNaC::is_order_function( const ex & e ) -->
  <function name="GiNaC::is_order_function">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::lsolve( const ex & eqns, const ex & symbols, unsigned options = solve_algo::automatic ) -->
  <function name="GiNaC::lsolve">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" default="solve_algo::automatic" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::remember_table::clear_all_entries( void ) -->
  <function name="GiNaC::remember_table::clear_all_entries">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void GiNaC::remember_table::init_table( void ) -->
  <function name="GiNaC::remember_table::init_table">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void GiNaC::remember_table::show_statistics( std::ostream & os, unsigned level ) const -->
  <function name="GiNaC::remember_table::show_statistics">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- unsigned long GiNaC::remember_table_entry::get_last_access( void ) -->
  <function name="GiNaC::remember_table_entry::get_last_access">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned long"/>
    <use-retval/>
  </function>
  <!-- ex GiNaC::remember_table_entry::get_result( void ) -->
  <function name="GiNaC::remember_table_entry::get_result">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- unsigned long GiNaC::remember_table_entry::get_successful_hits( void ) -->
  <function name="GiNaC::remember_table_entry::get_successful_hits">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned long"/>
    <use-retval/>
  </function>
  <!-- void GiNaC::fail::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::fail::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::constant::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::constant::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::constant::do_print_latex( const print_latex & c, unsigned level ) const -->
  <function name="GiNaC::constant::do_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::constant::do_print_python_repr( const print_python_repr & c, unsigned level ) const -->
  <function name="GiNaC::constant::do_print_python_repr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::constant::do_print_tree( const print_tree & c, unsigned level ) const -->
  <function name="GiNaC::constant::do_print_tree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool GiNaC::basic_multi_iterator::overflow( void ) const -->
  <function name="GiNaC::basic_multi_iterator::overflow">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- size_t GiNaC::basic_multi_iterator::size( void ) const -->
  <function name="GiNaC::basic_multi_iterator::size">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="size_t"/>
    <use-retval/>
    <const/>
  </function>
  <!-- int GiNaC::multi_iterator_permutation::get_sign( void ) const -->
  <function name="GiNaC::multi_iterator_permutation::get_sign">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool GiNaC::are_ex_trivially_equal( const ex & e1, const ex & e2 ) -->
  <function name="GiNaC::are_ex_trivially_equal">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::coeff( const ex & thisex, const ex & s, int n = 1 ) -->
  <function name="GiNaC::coeff">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" default="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::collect( const ex & thisex, const ex & s, bool distributed = false ) -->
  <function name="GiNaC::collect">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" default="false" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::conjugate( const ex & thisex ) -->
  <function name="GiNaC::conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::const_postorder_iterator::descend( void ) -->
  <function name="GiNaC::const_postorder_iterator::descend">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void GiNaC::const_postorder_iterator::increment( void ) -->
  <function name="GiNaC::const_postorder_iterator::increment">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void GiNaC::const_preorder_iterator::increment( void ) -->
  <function name="GiNaC::const_preorder_iterator::increment">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- ex GiNaC::diff( const ex & thisex, const symbol & s, unsigned nth = 1 ) -->
  <function name="GiNaC::diff">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" default="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::eval( const ex & thisex ) -->
  <function name="GiNaC::eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::eval_integ( const ex & thisex ) -->
  <function name="GiNaC::eval_integ">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::evalm( const ex & thisex ) -->
  <function name="GiNaC::evalm">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::ex::accept( visitor & v ) -->
  <function name="GiNaC::ex::accept">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- bool GiNaC::ex::are_ex_trivially_equal( const ex & , const ex &  ) -->
  <function name="GiNaC::ex::are_ex_trivially_equal">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::ex::coeff( const ex & s, int n = 1 ) -->
  <function name="GiNaC::ex::coeff">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" default="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::ex::collect( const ex & s, bool distributed = false ) -->
  <function name="GiNaC::ex::collect">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" default="false" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int GiNaC::ex::compare( const ex & other ) const -->
  <function name="GiNaC::ex::compare">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::ex::conjugate( void ) -->
  <function name="GiNaC::ex::conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- basic & GiNaC::ex::construct_from_double( double d ) -->
  <function name="GiNaC::ex::construct_from_double">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="basic &amp;"/>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>-1.79769e+308:1.79769e+308</valid>
    </arg>
  </function>
  <!-- basic & GiNaC::ex::construct_from_int( int i ) -->
  <function name="GiNaC::ex::construct_from_int">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="basic &amp;"/>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- basic & GiNaC::ex::construct_from_long( long i ) -->
  <function name="GiNaC::ex::construct_from_long">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="basic &amp;"/>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- basic & GiNaC::ex::construct_from_longlong( long long i ) -->
  <function name="GiNaC::ex::construct_from_longlong">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="basic &amp;"/>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- basic & GiNaC::ex::construct_from_uint( unsigned int i ) -->
  <function name="GiNaC::ex::construct_from_uint">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="basic &amp;"/>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- basic & GiNaC::ex::construct_from_ulong( unsigned long i ) -->
  <function name="GiNaC::ex::construct_from_ulong">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="basic &amp;"/>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- basic & GiNaC::ex::construct_from_ulonglong( unsigned long long i ) -->
  <function name="GiNaC::ex::construct_from_ulonglong">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="basic &amp;"/>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::ex::content( const ex & x ) const -->
  <function name="GiNaC::ex::content">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::ex::dbgprint( void ) const -->
  <function name="GiNaC::ex::dbgprint">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
  </function>
  <!-- void GiNaC::ex::dbgprinttree( void ) const -->
  <function name="GiNaC::ex::dbgprinttree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
  </function>
  <!-- int GiNaC::ex::degree( const ex & s ) -->
  <function name="GiNaC::ex::degree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::ex::denom( void ) const -->
  <function name="GiNaC::ex::denom">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
    <const/>
  </function>
  <!-- ex GiNaC::ex::diff( const symbol & s, unsigned nth = 1 ) const -->
  <function name="GiNaC::ex::diff">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" default="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::ex::eval( void ) -->
  <function name="GiNaC::ex::eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex GiNaC::ex::eval_integ( void ) -->
  <function name="GiNaC::ex::eval_integ">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex GiNaC::ex::eval_ncmul( const exvector & v ) -->
  <function name="GiNaC::ex::eval_ncmul">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::ex::evalf( void ) -->
  <function name="GiNaC::ex::evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex GiNaC::ex::evalm( void ) -->
  <function name="GiNaC::ex::evalm">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex GiNaC::ex::expand( unsigned options = 0 ) const -->
  <function name="GiNaC::ex::expand">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool GiNaC::ex::find( const ex & pattern, exset & found ) const -->
  <function name="GiNaC::ex::find">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- exvector GiNaC::ex::get_free_indices( void ) -->
  <function name="GiNaC::ex::get_free_indices">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="exvector"/>
    <use-retval/>
  </function>
  <!-- unsigned GiNaC::ex::gethash( void ) -->
  <function name="GiNaC::ex::gethash">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <use-retval/>
  </function>
  <!-- bool GiNaC::ex::has( const ex & pattern, unsigned options = 0 ) -->
  <function name="GiNaC::ex::has">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::ex::imag_part( void ) -->
  <function name="GiNaC::ex::imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- bool GiNaC::ex::info( unsigned inf ) -->
  <function name="GiNaC::ex::info">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- numeric GiNaC::ex::integer_content( void ) const -->
  <function name="GiNaC::ex::integer_content">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="numeric"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool GiNaC::ex::is_a( const ex &  ) -->
  <function name="GiNaC::ex::is_a">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::ex::is_equal( const ex & other ) const -->
  <function name="GiNaC::ex::is_equal">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::ex::is_exactly_a( const ex &  ) -->
  <function name="GiNaC::ex::is_exactly_a">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::ex::is_polynomial( const ex & vars ) const -->
  <function name="GiNaC::ex::is_polynomial">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::ex::is_zero( void ) -->
  <function name="GiNaC::ex::is_zero">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- bool GiNaC::ex::is_zero_matrix( void ) const -->
  <function name="GiNaC::ex::is_zero_matrix">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- ex GiNaC::ex::lcoeff( const ex & s ) -->
  <function name="GiNaC::ex::lcoeff">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- int GiNaC::ex::ldegree( const ex & s ) -->
  <function name="GiNaC::ex::ldegree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex & GiNaC::ex::let_op( size_t i ) -->
  <function name="GiNaC::ex::let_op">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex &amp;"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::ex::lhs( void ) const -->
  <function name="GiNaC::ex::lhs">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
    <const/>
  </function>
  <!-- void GiNaC::ex::makewriteable( void ) -->
  <function name="GiNaC::ex::makewriteable">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- ex GiNaC::ex::map( map_function & f ) -->
  <function name="GiNaC::ex::map">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- numeric GiNaC::ex::max_coefficient( void ) const -->
  <function name="GiNaC::ex::max_coefficient">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="numeric"/>
    <use-retval/>
    <const/>
  </function>
  <!-- size_t GiNaC::ex::nops( void ) -->
  <function name="GiNaC::ex::nops">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="size_t"/>
    <use-retval/>
  </function>
  <!-- ex GiNaC::ex::normal( void ) const -->
  <function name="GiNaC::ex::normal">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
    <const/>
  </function>
  <!-- ex GiNaC::ex::numer( void ) const -->
  <function name="GiNaC::ex::numer">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
    <const/>
  </function>
  <!-- ex GiNaC::ex::numer_denom( void ) const -->
  <function name="GiNaC::ex::numer_denom">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
    <const/>
  </function>
  <!-- ex GiNaC::ex::op( size_t i ) -->
  <function name="GiNaC::ex::op">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- const_postorder_iterator GiNaC::ex::postorder_begin( void ) const -->
  <function name="GiNaC::ex::postorder_begin">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const_postorder_iterator"/>
    <use-retval/>
    <const/>
  </function>
  <!-- const_preorder_iterator GiNaC::ex::preorder_begin( void ) const -->
  <function name="GiNaC::ex::preorder_begin">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const_preorder_iterator"/>
    <use-retval/>
    <const/>
  </function>
  <!-- void GiNaC::ex::print( const print_context & c, unsigned level = 0 ) const -->
  <function name="GiNaC::ex::print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::ex::real_part( void ) -->
  <function name="GiNaC::ex::real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- unsigned GiNaC::ex::return_type( void ) -->
  <function name="GiNaC::ex::return_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <use-retval/>
  </function>
  <!-- return_type_t GiNaC::ex::return_type_tinfo( void ) -->
  <function name="GiNaC::ex::return_type_tinfo">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="return_type_t"/>
    <use-retval/>
  </function>
  <!-- ex GiNaC::ex::rhs( void ) const -->
  <function name="GiNaC::ex::rhs">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
    <const/>
  </function>
  <!-- ex GiNaC::ex::series( const ex & r, int order, unsigned options = 0 ) const -->
  <function name="GiNaC::ex::series">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::ex::share( const ex & other ) const -->
  <function name="GiNaC::ex::share">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::ex::smod( const numeric & xi ) -->
  <function name="GiNaC::ex::smod">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::ex::tcoeff( const ex & s ) -->
  <function name="GiNaC::ex::tcoeff">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::ex::to_polynomial( exmap & repl ) const -->
  <function name="GiNaC::ex::to_polynomial">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- ex GiNaC::ex::to_rational( exmap & repl ) const -->
  <function name="GiNaC::ex::to_rational">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void GiNaC::ex::traverse( visitor & v ) -->
  <function name="GiNaC::ex::traverse">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void GiNaC::ex::traverse_postorder( visitor & v ) const -->
  <function name="GiNaC::ex::traverse_postorder">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void GiNaC::ex::traverse_preorder( visitor & v ) const -->
  <function name="GiNaC::ex::traverse_preorder">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- ex GiNaC::ex::unit( const ex & x ) const -->
  <function name="GiNaC::ex::unit">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::ex::unitcontprim( const ex & x, ex & u, ex & c, ex & p ) const -->
  <function name="GiNaC::ex::unitcontprim">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="inout"/>
    <arg nr="4" direction="inout"/>
  </function>
  <!-- bool GiNaC::find( const ex & thisex, const ex & pattern, exset & found ) -->
  <function name="GiNaC::find">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- bool GiNaC::has( const ex & thisex, const ex & pattern, unsigned options = 0 ) -->
  <function name="GiNaC::has">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::imag_part( const ex & thisex ) -->
  <function name="GiNaC::imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::is_polynomial( const ex & thisex, const ex & vars ) -->
  <function name="GiNaC::is_polynomial">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- int GiNaC::ldegree( const ex & thisex, const ex & s ) -->
  <function name="GiNaC::ldegree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::lhs( const ex & thisex ) -->
  <function name="GiNaC::lhs">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::library_init::init_unarchivers( void ) -->
  <function name="GiNaC::library_init::init_unarchivers">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
  </function>
  <!-- bool GiNaC::match( const ex & thisex, const ex & pattern, exmap & repl_lst ) -->
  <function name="GiNaC::match">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- ex GiNaC::normal( const ex & thisex ) -->
  <function name="GiNaC::normal">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::numer_denom( const ex & thisex ) -->
  <function name="GiNaC::numer_denom">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::op( const ex & thisex, size_t i ) -->
  <function name="GiNaC::op">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::real_part( const ex & thisex ) -->
  <function name="GiNaC::real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::rhs( const ex & thisex ) -->
  <function name="GiNaC::rhs">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::series( const ex & thisex, const ex & r, int order, unsigned options = 0 ) -->
  <function name="GiNaC::series">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::to_polynomial( const ex & thisex, exmap & repl ) -->
  <function name="GiNaC::to_polynomial">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- ex GiNaC::to_rational( const ex & thisex, exmap & repl ) -->
  <function name="GiNaC::to_rational">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- int ex::compare( const ex & other ) -->
  <function name="ex::compare">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool ex::is_equal( const ex & other ) -->
  <function name="ex::is_equal">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const_postorder_iterator ex::postorder_begin( void ) -->
  <function name="ex::postorder_begin">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const_postorder_iterator"/>
    <use-retval/>
  </function>
  <!-- const_preorder_iterator ex::preorder_begin( void ) -->
  <function name="ex::preorder_begin">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const_preorder_iterator"/>
    <use-retval/>
  </function>
  <!-- void std::swap( GiNaC::ex & a, GiNaC::ex & b ) -->
  <function name="std::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- ex ex::subs( const exmap & m, unsigned options ) -->
  <function name="ex::subs">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::Bernoulli_polynomial( const numeric & k, const ex & x ) -->
  <function name="GiNaC::Bernoulli_polynomial">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void GiNaC::ELi_kernel::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::ELi_kernel::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::Ebar_kernel::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::Ebar_kernel::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::Eisenstein_h_kernel::coefficient_a0( const numeric & k, const numeric & r, const numeric & s, const numeric & N ) const -->
  <function name="GiNaC::Eisenstein_h_kernel::coefficient_a0">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in"/>
  </function>
  <!-- ex GiNaC::Eisenstein_h_kernel::coefficient_an( const numeric & n, const numeric & k, const numeric & r, const numeric & s, const numeric & N ) const -->
  <function name="GiNaC::Eisenstein_h_kernel::coefficient_an">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in"/>
    <arg nr="5" direction="in"/>
  </function>
  <!-- void GiNaC::Eisenstein_h_kernel::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::Eisenstein_h_kernel::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::Eisenstein_h_kernel::q_expansion_modular_form( const ex & q, int order ) const -->
  <function name="GiNaC::Eisenstein_h_kernel::q_expansion_modular_form">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::Eisenstein_kernel::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::Eisenstein_kernel::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::Eisenstein_kernel::q_expansion_modular_form( const ex & q, int order ) const -->
  <function name="GiNaC::Eisenstein_kernel::q_expansion_modular_form">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::Kronecker_dtau_kernel::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::Kronecker_dtau_kernel::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::Kronecker_dz_kernel::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::Kronecker_dz_kernel::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::basic_log_kernel::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::basic_log_kernel::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- numeric GiNaC::dirichlet_character( const numeric & n, const numeric & a, const numeric & N ) -->
  <function name="GiNaC::dirichlet_character">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="numeric"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- numeric GiNaC::generalised_Bernoulli_number( const numeric & k, const numeric & b ) -->
  <function name="GiNaC::generalised_Bernoulli_number">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="numeric"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::ifactor( const numeric & n ) -->
  <function name="GiNaC::ifactor">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::integration_kernel::Laurent_series( const ex & x, int order ) const -->
  <function name="GiNaC::integration_kernel::Laurent_series">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::integration_kernel::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::integration_kernel::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- size_t GiNaC::integration_kernel::get_cache_size( void ) const -->
  <function name="GiNaC::integration_kernel::get_cache_size">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="size_t"/>
    <use-retval/>
    <const/>
  </function>
  <!-- ex GiNaC::integration_kernel::get_numerical_value( const ex & lambda, int N_trunc = 0 ) const -->
  <function name="GiNaC::integration_kernel::get_numerical_value">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::integration_kernel::get_numerical_value_impl( const ex & lambda, const ex & pre, int shift, int N_trunc ) const -->
  <function name="GiNaC::integration_kernel::get_numerical_value_impl">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::integration_kernel::get_series_coeff( int i ) const -->
  <function name="GiNaC::integration_kernel::get_series_coeff">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool GiNaC::integration_kernel::has_trailing_zero( void ) const -->
  <function name="GiNaC::integration_kernel::has_trailing_zero">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool GiNaC::integration_kernel::is_numeric( void ) const -->
  <function name="GiNaC::integration_kernel::is_numeric">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- cln::cl_N GiNaC::integration_kernel::series_coeff( int i ) const -->
  <function name="GiNaC::integration_kernel::series_coeff">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="cln::cl_N"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- cln::cl_N GiNaC::integration_kernel::series_coeff_impl( int i ) const -->
  <function name="GiNaC::integration_kernel::series_coeff_impl">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="cln::cl_N"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::integration_kernel::set_cache_step( int cache_steps ) const -->
  <function name="GiNaC::integration_kernel::set_cache_step">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool GiNaC::integration_kernel::uses_Laurent_series( void ) const -->
  <function name="GiNaC::integration_kernel::uses_Laurent_series">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool GiNaC::is_discriminant_of_quadratic_number_field( const numeric & n ) -->
  <function name="GiNaC::is_discriminant_of_quadratic_number_field">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- numeric GiNaC::kronecker_symbol( const numeric & a, const numeric & n ) -->
  <function name="GiNaC::kronecker_symbol">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="numeric"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void GiNaC::modular_form_kernel::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::modular_form_kernel::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::modular_form_kernel::q_expansion_modular_form( const ex & q, int order ) const -->
  <function name="GiNaC::modular_form_kernel::q_expansion_modular_form">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::multiple_polylog_kernel::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::multiple_polylog_kernel::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- numeric GiNaC::primitive_dirichlet_character( const numeric & n, const numeric & a ) -->
  <function name="GiNaC::primitive_dirichlet_character">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="numeric"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void GiNaC::user_defined_kernel::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::user_defined_kernel::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::expand_dummy_sum( const ex & e, bool subs_idx = false ) -->
  <function name="GiNaC::expand_dummy_sum">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" default="false" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- exvector GiNaC::get_all_dummy_indices( const ex & e ) -->
  <function name="GiNaC::get_all_dummy_indices">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="exvector"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- exvector GiNaC::get_all_dummy_indices_safely( const ex & e ) -->
  <function name="GiNaC::get_all_dummy_indices_safely">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="exvector"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::indexed::all_index_values_are( unsigned inf ) const -->
  <function name="GiNaC::indexed::all_index_values_are">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::indexed::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::indexed::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::indexed::do_print_latex( const print_latex & c, unsigned level ) const -->
  <function name="GiNaC::indexed::do_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::indexed::do_print_tree( const print_tree & c, unsigned level ) const -->
  <function name="GiNaC::indexed::do_print_tree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- exvector GiNaC::indexed::get_indices( void ) const -->
  <function name="GiNaC::indexed::get_indices">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="exvector"/>
    <use-retval/>
    <const/>
  </function>
  <!-- ex GiNaC::indexed::get_symmetry( void ) -->
  <function name="GiNaC::indexed::get_symmetry">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- bool GiNaC::indexed::has_dummy_index_for( const ex & i ) const -->
  <function name="GiNaC::indexed::has_dummy_index_for">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::indexed::print_indexed( const print_context & c, const char * openbrace, const char * closebrace, unsigned level ) const -->
  <function name="GiNaC::indexed::print_indexed">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::indexed::printindices( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::indexed::printindices">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool GiNaC::indexed::reposition_dummy_indices( ex & e, exvector & variant_dummy_indices, exvector & moved_indices ) -->
  <function name="GiNaC::indexed::reposition_dummy_indices">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- ex GiNaC::indexed::simplify_indexed( const ex & e, exvector & free_indices, exvector & dummy_indices, const scalar_products & sp ) -->
  <function name="GiNaC::indexed::simplify_indexed">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="inout"/>
    <arg nr="4" direction="in"/>
  </function>
  <!-- ex GiNaC::indexed::simplify_indexed_product( const ex & e, exvector & free_indices, exvector & dummy_indices, const scalar_products & sp ) -->
  <function name="GiNaC::indexed::simplify_indexed_product">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="inout"/>
    <arg nr="4" direction="in"/>
  </function>
  <!-- void GiNaC::indexed::validate( void ) const -->
  <function name="GiNaC::indexed::validate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
  </function>
  <!-- void GiNaC::scalar_products::clear( void ) -->
  <function name="GiNaC::scalar_products::clear">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void GiNaC::scalar_products::debugprint( void ) const -->
  <function name="GiNaC::scalar_products::debugprint">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
  </function>
  <!-- ex GiNaC::scalar_products::evaluate( const ex & v1, const ex & v2, const ex & dim ) const -->
  <function name="GiNaC::scalar_products::evaluate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- bool GiNaC::scalar_products::is_defined( const ex & v1, const ex & v2, const ex & dim ) const -->
  <function name="GiNaC::scalar_products::is_defined">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void GiNaC::spmapkey::debugprint( void ) const -->
  <function name="GiNaC::spmapkey::debugprint">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
  </function>
  <!-- archive_node_id GiNaC::archive::add_node( const archive_node & n ) -->
  <function name="GiNaC::archive::add_node">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="archive_node_id"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::archive::archive_ex( const ex & e, const char * name ) -->
  <function name="GiNaC::archive::archive_ex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- archive_atom GiNaC::archive::atomize( const std::string & s ) const -->
  <function name="GiNaC::archive::atomize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="archive_atom"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::archive::clear( void ) -->
  <function name="GiNaC::archive::clear">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void GiNaC::archive::forget( void ) -->
  <function name="GiNaC::archive::forget">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- archive_node & GiNaC::archive::get_node( archive_node_id id ) -->
  <function name="GiNaC::archive::get_node">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="archive_node &amp;"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const archive_node & GiNaC::archive::get_top_node( unsigned index = 0 ) const -->
  <function name="GiNaC::archive::get_top_node">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const archive_node &amp;"/>
    <const/>
    <arg nr="1" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- unsigned GiNaC::archive::num_expressions( void ) const -->
  <function name="GiNaC::archive::num_expressions">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <use-retval/>
    <const/>
  </function>
  <!-- void GiNaC::archive::printraw( std::ostream & os ) const -->
  <function name="GiNaC::archive::printraw">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- const std::string & GiNaC::archive::unatomize( archive_atom id ) const -->
  <function name="GiNaC::archive::unatomize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const std::string &amp;"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::archive_node::add_bool( const std::string & name, bool value ) -->
  <function name="GiNaC::archive_node::add_bool">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void GiNaC::archive_node::add_ex( const std::string & name, const ex & value ) -->
  <function name="GiNaC::archive_node::add_ex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void GiNaC::archive_node::add_string( const std::string & name, const std::string & value ) -->
  <function name="GiNaC::archive_node::add_string">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void GiNaC::archive_node::add_unsigned( const std::string & name, unsigned value ) -->
  <function name="GiNaC::archive_node::add_unsigned">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool GiNaC::archive_node::find_bool( const std::string & name, bool & ret, unsigned index = 0 ) const -->
  <function name="GiNaC::archive_node::find_bool">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool GiNaC::archive_node::find_ex( const std::string & name, ex & ret, lst & sym_lst, unsigned index = 0 ) const -->
  <function name="GiNaC::archive_node::find_ex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="inout"/>
    <arg nr="4" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::archive_node::find_ex_by_loc( archive_node_cit loc, ex & ret, lst & sym_lst ) const -->
  <function name="GiNaC::archive_node::find_ex_by_loc">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- const archive_node & GiNaC::archive_node::find_ex_node( const std::string & name, unsigned index = 0 ) const -->
  <function name="GiNaC::archive_node::find_ex_node">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const archive_node &amp;"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- archive_node_cit GiNaC::archive_node::find_first( const std::string & name ) const -->
  <function name="GiNaC::archive_node::find_first">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="archive_node_cit"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- archive_node_cit GiNaC::archive_node::find_last( const std::string & name ) const -->
  <function name="GiNaC::archive_node::find_last">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="archive_node_cit"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- archive_node_cit_range GiNaC::archive_node::find_property_range( const std::string & name1, const std::string & name2 ) const -->
  <function name="GiNaC::archive_node::find_property_range">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="archive_node_cit_range"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- bool GiNaC::archive_node::find_string( const std::string & name, std::string & ret, unsigned index = 0 ) const -->
  <function name="GiNaC::archive_node::find_string">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool GiNaC::archive_node::find_unsigned( const std::string & name, unsigned & ret, unsigned index = 0 ) const -->
  <function name="GiNaC::archive_node::find_unsigned">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::archive_node::forget( void ) -->
  <function name="GiNaC::archive_node::forget">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- ex GiNaC::archive_node::get_ex( void ) -->
  <function name="GiNaC::archive_node::get_ex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- void GiNaC::archive_node::get_properties( propinfovector & v ) const -->
  <function name="GiNaC::archive_node::get_properties">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- bool GiNaC::archive_node::has_ex( void ) -->
  <function name="GiNaC::archive_node::has_ex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- bool GiNaC::archive_node::has_same_ex_as( const archive_node & other ) const -->
  <function name="GiNaC::archive_node::has_same_ex_as">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::archive_node::printraw( std::ostream & os ) const -->
  <function name="GiNaC::archive_node::printraw">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- ex GiNaC::archive_node::unarchive( lst & sym_lst ) const -->
  <function name="GiNaC::archive_node::unarchive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- synthesize_func GiNaC::unarchive_table_t::find( const std::string & classname ) const -->
  <function name="GiNaC::unarchive_table_t::find">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="synthesize_func"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::unarchive_table_t::insert( const std::string & classname, synthesize_func f ) -->
  <function name="GiNaC::unarchive_table_t::insert">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- size_t GiNaC::count_dummy_indices( const exvector & v ) -->
  <function name="GiNaC::count_dummy_indices">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="size_t"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- size_t GiNaC::count_free_indices( const exvector & v ) -->
  <function name="GiNaC::count_free_indices">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="size_t"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::find_dummy_indices( const exvector & v, exvector & out_dummy ) -->
  <function name="GiNaC::find_dummy_indices">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- void GiNaC::idx::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::idx::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::idx::do_print_csrc( const print_csrc & c, unsigned level ) const -->
  <function name="GiNaC::idx::do_print_csrc">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::idx::do_print_latex( const print_latex & c, unsigned level ) const -->
  <function name="GiNaC::idx::do_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::idx::do_print_tree( const print_tree & c, unsigned level ) const -->
  <function name="GiNaC::idx::do_print_tree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::idx::get_dim( void ) -->
  <function name="GiNaC::idx::get_dim">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex GiNaC::idx::get_value( void ) -->
  <function name="GiNaC::idx::get_value">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- bool GiNaC::idx::is_dim_numeric( void ) -->
  <function name="GiNaC::idx::is_dim_numeric">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- bool GiNaC::idx::is_dim_symbolic( void ) -->
  <function name="GiNaC::idx::is_dim_symbolic">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- bool GiNaC::idx::is_dummy_pair_same_type( const basic & other ) const -->
  <function name="GiNaC::idx::is_dummy_pair_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::idx::is_numeric( void ) -->
  <function name="GiNaC::idx::is_numeric">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- bool GiNaC::idx::is_symbolic( void ) -->
  <function name="GiNaC::idx::is_symbolic">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- ex GiNaC::idx::minimal_dim( const idx & other ) const -->
  <function name="GiNaC::idx::minimal_dim">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::idx::print_index( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::idx::print_index">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::idx::replace_dim( const ex & new_dim ) const -->
  <function name="GiNaC::idx::replace_dim">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::minimal_dim( const ex & dim1, const ex & dim2 ) -->
  <function name="GiNaC::minimal_dim">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void GiNaC::spinidx::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::spinidx::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::spinidx::do_print_latex( const print_latex & c, unsigned level ) const -->
  <function name="GiNaC::spinidx::do_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::spinidx::do_print_tree( const print_tree & c, unsigned level ) const -->
  <function name="GiNaC::spinidx::do_print_tree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool GiNaC::spinidx::is_dotted( void ) -->
  <function name="GiNaC::spinidx::is_dotted">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- bool GiNaC::spinidx::is_undotted( void ) -->
  <function name="GiNaC::spinidx::is_undotted">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- ex GiNaC::spinidx::toggle_dot( void ) const -->
  <function name="GiNaC::spinidx::toggle_dot">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
    <const/>
  </function>
  <!-- ex GiNaC::spinidx::toggle_variance_dot( void ) const -->
  <function name="GiNaC::spinidx::toggle_variance_dot">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
    <const/>
  </function>
  <!-- void GiNaC::varidx::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::varidx::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::varidx::do_print_tree( const print_tree & c, unsigned level ) const -->
  <function name="GiNaC::varidx::do_print_tree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool GiNaC::varidx::is_contravariant( void ) -->
  <function name="GiNaC::varidx::is_contravariant">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- bool GiNaC::varidx::is_covariant( void ) -->
  <function name="GiNaC::varidx::is_covariant">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- ex GiNaC::varidx::toggle_variance( void ) const -->
  <function name="GiNaC::varidx::toggle_variance">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool GiNaC::haswild( const ex & x ) -->
  <function name="GiNaC::haswild">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::wild( unsigned label = 0 ) -->
  <function name="GiNaC::wild">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::wildcard::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::wildcard::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::wildcard::do_print_python_repr( const print_python_repr & c, unsigned level ) const -->
  <function name="GiNaC::wildcard::do_print_python_repr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::wildcard::do_print_tree( const print_tree & c, unsigned level ) const -->
  <function name="GiNaC::wildcard::do_print_tree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- unsigned GiNaC::wildcard::get_label( void ) -->
  <function name="GiNaC::wildcard::get_label">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <use-retval/>
  </function>
  <!-- ex GiNaC::charpoly( const matrix & m, const ex & lambda ) -->
  <function name="GiNaC::charpoly">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- unsigned GiNaC::cols( const matrix & m ) -->
  <function name="GiNaC::cols">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::determinant( const matrix & m, unsigned options = determinant_algo::automatic ) -->
  <function name="GiNaC::determinant">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" default="determinant_algo::automatic" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::lst_to_matrix( const lst & l ) -->
  <function name="GiNaC::lst_to_matrix">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- matrix GiNaC::matrix::add( const matrix & other ) const -->
  <function name="GiNaC::matrix::add">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="matrix"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::matrix::charpoly( const ex & lambda ) const -->
  <function name="GiNaC::matrix::charpoly">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- unsigned GiNaC::matrix::cols( void ) -->
  <function name="GiNaC::matrix::cols">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <use-retval/>
  </function>
  <!-- ex GiNaC::matrix::determinant( unsigned algo = determinant_algo::automatic ) const -->
  <function name="GiNaC::matrix::determinant">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <const/>
    <arg nr="1" default="determinant_algo::automatic" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::matrix::determinant_minor( void ) const -->
  <function name="GiNaC::matrix::determinant_minor">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
    <const/>
  </function>
  <!-- int GiNaC::matrix::division_free_elimination( const bool det = false ) -->
  <function name="GiNaC::matrix::division_free_elimination">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" default="false" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void GiNaC::matrix::do_print( const print_context & c, unsigned level ) const -->
  <function name="GiNaC::matrix::do_print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::matrix::do_print_latex( const print_latex & c, unsigned level ) const -->
  <function name="GiNaC::matrix::do_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GiNaC::matrix::do_print_python_repr( const print_python_repr & c, unsigned level ) const -->
  <function name="GiNaC::matrix::do_print_python_repr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int GiNaC::matrix::fraction_free_elimination( const bool det = false ) -->
  <function name="GiNaC::matrix::fraction_free_elimination">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" default="false" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int GiNaC::matrix::gauss_elimination( const bool det = false ) -->
  <function name="GiNaC::matrix::gauss_elimination">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" default="false" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool GiNaC::matrix::is_zero_matrix( void ) const -->
  <function name="GiNaC::matrix::is_zero_matrix">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- matrix GiNaC::matrix::mul_scalar( const ex & other ) const -->
  <function name="GiNaC::matrix::mul_scalar">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="matrix"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- int GiNaC::matrix::pivot( unsigned ro, unsigned co, bool symbolic = true ) -->
  <function name="GiNaC::matrix::pivot">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" default="true" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- matrix GiNaC::matrix::pow( const ex & expn ) const -->
  <function name="GiNaC::matrix::pow">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="matrix"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::matrix::print_elements( const print_context & c, const char * row_start, const char * row_end, const char * row_sep, const char * col_sep ) const -->
  <function name="GiNaC::matrix::print_elements">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="5" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- unsigned GiNaC::matrix::rows( void ) -->
  <function name="GiNaC::matrix::rows">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <use-retval/>
  </function>
  <!-- matrix & GiNaC::matrix::set( unsigned ro, unsigned co, const ex & value ) -->
  <function name="GiNaC::matrix::set">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="matrix &amp;"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in"/>
  </function>
  <!-- matrix GiNaC::matrix::sub( const matrix & other ) const -->
  <function name="GiNaC::matrix::sub">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="matrix"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::matrix::trace( void ) const -->
  <function name="GiNaC::matrix::trace">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
    <const/>
  </function>
  <!-- matrix GiNaC::matrix::transpose( void ) const -->
  <function name="GiNaC::matrix::transpose">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="matrix"/>
    <use-retval/>
    <const/>
  </function>
  <!-- ex GiNaC::reduced_matrix( const matrix & m, unsigned r, unsigned c ) -->
  <function name="GiNaC::reduced_matrix">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- unsigned GiNaC::rows( const matrix & m ) -->
  <function name="GiNaC::rows">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::sub_matrix( const matrix & m, unsigned r, unsigned nr, unsigned c, unsigned nc ) -->
  <function name="GiNaC::sub_matrix">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ex GiNaC::trace( const matrix & m ) -->
  <function name="GiNaC::trace">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- matrix GiNaC::transpose( const matrix & m ) -->
  <function name="GiNaC::transpose">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="matrix"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void wildcard::archive( archive_node & n ) -->
  <function name="wildcard::archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- unsigned wildcard::calchash( void ) -->
  <function name="wildcard::calchash">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <use-retval/>
  </function>
  <!-- int wildcard::compare_same_type( const basic & other ) -->
  <function name="wildcard::compare_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool wildcard::match( const ex & pattern, exmap & repl_lst ) -->
  <function name="wildcard::match">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- void wildcard::read_archive( const archive_node & n, lst & sym_lst ) -->
  <function name="wildcard::read_archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- int pole_error::degree( void ) -->
  <function name="pole_error::degree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
  </function>
  <!-- void library_init::init_unarchivers( void ) -->
  <function name="library_init::init_unarchivers">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void GiNaC::base_and_index( const ex & c, ex & b, ex & i ) -->
  <function name="GiNaC::base_and_index">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- ex GiNaC::get_clifford_comp( const ex & e, const ex & c, bool root = true ) -->
  <function name="GiNaC::get_clifford_comp">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" default="true" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- unsigned GiNaC::get_dim_uint( const ex & e ) -->
  <function name="GiNaC::get_dim_uint">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::is_dirac_slash( const ex & seq0 ) -->
  <function name="GiNaC::is_dirac_slash">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::trace_string( exvector::const_iterator ix, size_t num ) -->
  <function name="GiNaC::trace_string">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void clifford::archive( archive_node & n ) -->
  <function name="clifford::archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- int clifford::compare_same_type( const basic & other ) -->
  <function name="clifford::compare_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex diracgamma5::conjugate( void ) -->
  <function name="diracgamma5::conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- bool diracgamma::contract_with( exvector::iterator self, exvector::iterator other, exvector & v ) -->
  <function name="diracgamma::contract_with">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- ex clifford::eval_ncmul( const exvector & v ) -->
  <function name="clifford::eval_ncmul">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex & clifford::let_op( size_t i ) -->
  <function name="clifford::let_op">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex &amp;"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool clifford::match_same_type( const basic & other ) -->
  <function name="clifford::match_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex clifford::op( size_t i ) -->
  <function name="clifford::op">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void clifford::read_archive( const archive_node & n, lst & sym_lst ) -->
  <function name="clifford::read_archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- return_type_t clifford::return_type_tinfo( void ) -->
  <function name="clifford::return_type_tinfo">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="return_type_t"/>
    <use-retval/>
  </function>
  <!-- ex clifford::subs( const exmap & m, unsigned options ) -->
  <function name="clifford::subs">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex clifford::thiscontainer( const exvector & v ) -->
  <function name="clifford::thiscontainer">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::divide_in_z_p( const ex & a, const ex & b, ex & q, const exvector & vars, const long p ) -->
  <function name="GiNaC::divide_in_z_p">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
    <arg nr="4" direction="in"/>
    <arg nr="5" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::pgcd( const ex & A, const ex & B, const exvector & vars, const long p ) -->
  <function name="GiNaC::pgcd">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool GiNaC::heur_gcd_z( upoly & g, const upoly & a, const upoly & b ) -->
  <function name="GiNaC::heur_gcd_z">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void GiNaC::collect_vargs( ex_collect_t & ec, const ex & e, const exvector & vars ) -->
  <function name="GiNaC::collect_vargs">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void GiNaC::wipe_out_zeros( ex_collect_priv_t & m ) -->
  <function name="GiNaC::wipe_out_zeros">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void GiNaC::find_next_prime( cln::cl_I & p, const cln::cl_I & g ) -->
  <function name="GiNaC::find_next_prime">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void GiNaC::remove_content( upoly & A, upoly & B, upoly::value_type & c ) -->
  <function name="GiNaC::remove_content">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void GiNaC::__anon1::add_symbol( const ex & s, sym_desc_vec & v ) -->
  <function name="GiNaC::__anon1::add_symbol">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- void GiNaC::__anon1::collect_symbols( const ex & e, sym_desc_vec & v ) -->
  <function name="GiNaC::__anon1::collect_symbols">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- void GiNaC::__anon1::get_symbol_stats( const ex & a, const ex & b, sym_desc_vec & v ) -->
  <function name="GiNaC::__anon1::get_symbol_stats">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- cln::cl_I GiNaC::extract_integer_content( ex & Apr, const ex & A ) -->
  <function name="GiNaC::extract_integer_content">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="cln::cl_I"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void expairseq::archive( archive_node & n ) -->
  <function name="expairseq::archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- unsigned expairseq::calchash( void ) -->
  <function name="expairseq::calchash">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <use-retval/>
  </function>
  <!-- int expairseq::compare_same_type( const basic & other ) -->
  <function name="expairseq::compare_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex expairseq::conjugate( void ) -->
  <function name="expairseq::conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex expairseq::eval( void ) -->
  <function name="expairseq::eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex expairseq::expand( unsigned options ) -->
  <function name="expairseq::expand">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool expairseq::info( unsigned inf ) -->
  <function name="expairseq::info">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool expairseq::is_equal_same_type( const basic & other ) -->
  <function name="expairseq::is_equal_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex expairseq::map( map_function & f ) -->
  <function name="expairseq::map">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- bool expairseq::match( const ex & pattern, exmap & repl_lst ) -->
  <function name="expairseq::match">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- size_t expairseq::nops( void ) -->
  <function name="expairseq::nops">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="size_t"/>
    <use-retval/>
  </function>
  <!-- ex expairseq::op( size_t i ) -->
  <function name="expairseq::op">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void expairseq::printpair( const print_context & c, const expair & p, unsigned upper_precedence ) -->
  <function name="expairseq::printpair">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void expairseq::read_archive( const archive_node & n, lst & sym_lst ) -->
  <function name="expairseq::read_archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- unsigned expairseq::return_type( void ) -->
  <function name="expairseq::return_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <use-retval/>
  </function>
  <!-- ex expairseq::subs( const exmap & m, unsigned options ) -->
  <function name="expairseq::subs">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::acos_conjugate( const ex & x ) -->
  <function name="GiNaC::acos_conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::acos_deriv( const ex & x, unsigned deriv_param ) -->
  <function name="GiNaC::acos_deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::acos_eval( const ex & x ) -->
  <function name="GiNaC::acos_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::acos_evalf( const ex & x ) -->
  <function name="GiNaC::acos_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::acosh_conjugate( const ex & x ) -->
  <function name="GiNaC::acosh_conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::acosh_deriv( const ex & x, unsigned deriv_param ) -->
  <function name="GiNaC::acosh_deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::acosh_eval( const ex & x ) -->
  <function name="GiNaC::acosh_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::acosh_evalf( const ex & x ) -->
  <function name="GiNaC::acosh_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::asin_conjugate( const ex & x ) -->
  <function name="GiNaC::asin_conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::asin_deriv( const ex & x, unsigned deriv_param ) -->
  <function name="GiNaC::asin_deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::asin_eval( const ex & x ) -->
  <function name="GiNaC::asin_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::asin_evalf( const ex & x ) -->
  <function name="GiNaC::asin_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::asinh_conjugate( const ex & x ) -->
  <function name="GiNaC::asinh_conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::asinh_deriv( const ex & x, unsigned deriv_param ) -->
  <function name="GiNaC::asinh_deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::asinh_eval( const ex & x ) -->
  <function name="GiNaC::asinh_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::asinh_evalf( const ex & x ) -->
  <function name="GiNaC::asinh_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::atan2_deriv( const ex & y, const ex & x, unsigned deriv_param ) -->
  <function name="GiNaC::atan2_deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::atan2_eval( const ex & y, const ex & x ) -->
  <function name="GiNaC::atan2_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::atan2_evalf( const ex & y, const ex & x ) -->
  <function name="GiNaC::atan2_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::atan_conjugate( const ex & x ) -->
  <function name="GiNaC::atan_conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::atan_deriv( const ex & x, unsigned deriv_param ) -->
  <function name="GiNaC::atan_deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::atan_eval( const ex & x ) -->
  <function name="GiNaC::atan_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::atan_evalf( const ex & x ) -->
  <function name="GiNaC::atan_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::atanh_conjugate( const ex & x ) -->
  <function name="GiNaC::atanh_conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::atanh_deriv( const ex & x, unsigned deriv_param ) -->
  <function name="GiNaC::atanh_deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::atanh_eval( const ex & x ) -->
  <function name="GiNaC::atanh_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::atanh_evalf( const ex & x ) -->
  <function name="GiNaC::atanh_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::cos_conjugate( const ex & x ) -->
  <function name="GiNaC::cos_conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::cos_deriv( const ex & x, unsigned deriv_param ) -->
  <function name="GiNaC::cos_deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::cos_eval( const ex & x ) -->
  <function name="GiNaC::cos_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::cos_evalf( const ex & x ) -->
  <function name="GiNaC::cos_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::cos_imag_part( const ex & x ) -->
  <function name="GiNaC::cos_imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::cos_real_part( const ex & x ) -->
  <function name="GiNaC::cos_real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::cosh_conjugate( const ex & x ) -->
  <function name="GiNaC::cosh_conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::cosh_deriv( const ex & x, unsigned deriv_param ) -->
  <function name="GiNaC::cosh_deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::cosh_eval( const ex & x ) -->
  <function name="GiNaC::cosh_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::cosh_evalf( const ex & x ) -->
  <function name="GiNaC::cosh_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::cosh_imag_part( const ex & x ) -->
  <function name="GiNaC::cosh_imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::cosh_real_part( const ex & x ) -->
  <function name="GiNaC::cosh_real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::exp_conjugate( const ex & x ) -->
  <function name="GiNaC::exp_conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::exp_deriv( const ex & x, unsigned deriv_param ) -->
  <function name="GiNaC::exp_deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::exp_eval( const ex & x ) -->
  <function name="GiNaC::exp_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::exp_evalf( const ex & x ) -->
  <function name="GiNaC::exp_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::exp_expand( const ex & arg, unsigned options ) -->
  <function name="GiNaC::exp_expand">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::exp_imag_part( const ex & x ) -->
  <function name="GiNaC::exp_imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::exp_power( const ex & x, const ex & a ) -->
  <function name="GiNaC::exp_power">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::exp_real_part( const ex & x ) -->
  <function name="GiNaC::exp_real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::log_conjugate( const ex & x ) -->
  <function name="GiNaC::log_conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::log_deriv( const ex & x, unsigned deriv_param ) -->
  <function name="GiNaC::log_deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::log_eval( const ex & x ) -->
  <function name="GiNaC::log_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::log_evalf( const ex & x ) -->
  <function name="GiNaC::log_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::log_expand( const ex & arg, unsigned options ) -->
  <function name="GiNaC::log_expand">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::log_imag_part( const ex & x ) -->
  <function name="GiNaC::log_imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::log_real_part( const ex & x ) -->
  <function name="GiNaC::log_real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::sin_conjugate( const ex & x ) -->
  <function name="GiNaC::sin_conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::sin_deriv( const ex & x, unsigned deriv_param ) -->
  <function name="GiNaC::sin_deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::sin_eval( const ex & x ) -->
  <function name="GiNaC::sin_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::sin_evalf( const ex & x ) -->
  <function name="GiNaC::sin_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::sin_imag_part( const ex & x ) -->
  <function name="GiNaC::sin_imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::sin_real_part( const ex & x ) -->
  <function name="GiNaC::sin_real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::sinh_conjugate( const ex & x ) -->
  <function name="GiNaC::sinh_conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::sinh_deriv( const ex & x, unsigned deriv_param ) -->
  <function name="GiNaC::sinh_deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::sinh_eval( const ex & x ) -->
  <function name="GiNaC::sinh_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::sinh_evalf( const ex & x ) -->
  <function name="GiNaC::sinh_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::sinh_imag_part( const ex & x ) -->
  <function name="GiNaC::sinh_imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::sinh_real_part( const ex & x ) -->
  <function name="GiNaC::sinh_real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::tan_conjugate( const ex & x ) -->
  <function name="GiNaC::tan_conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::tan_deriv( const ex & x, unsigned deriv_param ) -->
  <function name="GiNaC::tan_deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::tan_eval( const ex & x ) -->
  <function name="GiNaC::tan_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::tan_evalf( const ex & x ) -->
  <function name="GiNaC::tan_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::tan_imag_part( const ex & x ) -->
  <function name="GiNaC::tan_imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::tan_real_part( const ex & x ) -->
  <function name="GiNaC::tan_real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::tanh_conjugate( const ex & x ) -->
  <function name="GiNaC::tanh_conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::tanh_deriv( const ex & x, unsigned deriv_param ) -->
  <function name="GiNaC::tanh_deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::tanh_eval( const ex & x ) -->
  <function name="GiNaC::tanh_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::tanh_evalf( const ex & x ) -->
  <function name="GiNaC::tanh_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::tanh_imag_part( const ex & x ) -->
  <function name="GiNaC::tanh_imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::tanh_real_part( const ex & x ) -->
  <function name="GiNaC::tanh_real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- int GiNaC::get_tok_prec( const int c ) -->
  <function name="GiNaC::get_tok_prec">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool GiNaC::is_binop( const int c ) -->
  <function name="GiNaC::is_binop">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::make_binop_expr( const int binop, const exvector & args ) -->
  <function name="GiNaC::make_binop_expr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::make_divide_expr( const exvector & args ) -->
  <function name="GiNaC::make_divide_expr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::make_minus_expr( const exvector & args ) -->
  <function name="GiNaC::make_minus_expr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex parser::parse_binop_rhs( int expr_prec, ex & lhs ) -->
  <function name="parser::parse_binop_rhs">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- bool GiNaC::decode_serial( unsigned & serial, const reader_func ptr ) -->
  <function name="GiNaC::decode_serial">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::dispatch_reader_fcn( const reader_func ptr, const exvector & args ) -->
  <function name="GiNaC::dispatch_reader_fcn">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- reader_func GiNaC::encode_serial_as_reader_func( unsigned serial ) -->
  <function name="GiNaC::encode_serial_as_reader_func">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="reader_func"/>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- const prototype_table & GiNaC::get_builtin_reader( void ) -->
  <function name="GiNaC::get_builtin_reader">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const prototype_table &amp;"/>
    <use-retval/>
  </function>
  <!-- const prototype_table & GiNaC::get_default_reader( void ) -->
  <function name="GiNaC::get_default_reader">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const prototype_table &amp;"/>
    <use-retval/>
  </function>
  <!-- ex GiNaC::lst_reader( const exvector & ev ) -->
  <function name="GiNaC::lst_reader">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::pow_reader( const exvector & ev ) -->
  <function name="GiNaC::pow_reader">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::power_reader( const exvector & ev ) -->
  <function name="GiNaC::power_reader">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const std::vector<function_options> & GiNaC::registered_functions_hack::get_registered_functions( void ) -->
  <function name="GiNaC::registered_functions_hack::get_registered_functions">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <pure/>
  </function>
  <!-- ex GiNaC::sqrt_reader( const exvector & ev ) -->
  <function name="GiNaC::sqrt_reader">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- std::string GiNaC::get_symbol_name( const ex & s ) -->
  <function name="GiNaC::get_symbol_name">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::string"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- symtab GiNaC::make_symtab( const ex & l ) -->
  <function name="GiNaC::make_symtab">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="symtab"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::literal_p( const std::string & name ) -->
  <function name="GiNaC::literal_p">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- int GiNaC::skipline( std::istream * s ) -->
  <function name="GiNaC::skipline">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <pure/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- int GiNaC::skipspace( std::istream * s, int c, std::size_t & line ) -->
  <function name="GiNaC::skipspace">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- ex GiNaC::Li2_conjugate( const ex & x ) -->
  <function name="GiNaC::Li2_conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::Li2_deriv( const ex & x, unsigned deriv_param ) -->
  <function name="GiNaC::Li2_deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::Li2_eval( const ex & x ) -->
  <function name="GiNaC::Li2_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::Li2_evalf( const ex & x ) -->
  <function name="GiNaC::Li2_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::Li2_series( const ex & x, const relational & rel, int order, unsigned options ) -->
  <function name="GiNaC::Li2_series">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::Li3_eval( const ex & x ) -->
  <function name="GiNaC::Li3_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::Order_conjugate( const ex & x ) -->
  <function name="GiNaC::Order_conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::Order_eval( const ex & x ) -->
  <function name="GiNaC::Order_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::Order_expl_derivative( const ex & arg, const symbol & s ) -->
  <function name="GiNaC::Order_expl_derivative">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::Order_imag_part( const ex & x ) -->
  <function name="GiNaC::Order_imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::Order_real_part( const ex & x ) -->
  <function name="GiNaC::Order_real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::Order_series( const ex & x, const relational & r, int order, unsigned options ) -->
  <function name="GiNaC::Order_series">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::abs_conjugate( const ex & arg ) -->
  <function name="GiNaC::abs_conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::abs_eval( const ex & arg ) -->
  <function name="GiNaC::abs_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::abs_evalf( const ex & arg ) -->
  <function name="GiNaC::abs_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::abs_expand( const ex & arg, unsigned options ) -->
  <function name="GiNaC::abs_expand">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::abs_expl_derivative( const ex & arg, const symbol & s ) -->
  <function name="GiNaC::abs_expl_derivative">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::abs_imag_part( const ex & arg ) -->
  <function name="GiNaC::abs_imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::abs_info( const ex & arg, unsigned inf ) -->
  <function name="GiNaC::abs_info">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::abs_power( const ex & arg, const ex & exp ) -->
  <function name="GiNaC::abs_power">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void GiNaC::abs_print_csrc_float( const ex & arg, const print_context & c ) -->
  <function name="GiNaC::abs_print_csrc_float">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void GiNaC::abs_print_latex( const ex & arg, const print_context & c ) -->
  <function name="GiNaC::abs_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::abs_real_part( const ex & arg ) -->
  <function name="GiNaC::abs_real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::binomial_conjugate( const ex & x, const ex & y ) -->
  <function name="GiNaC::binomial_conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::binomial_eval( const ex & x, const ex & y ) -->
  <function name="GiNaC::binomial_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::binomial_evalf( const ex & x, const ex & y ) -->
  <function name="GiNaC::binomial_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::binomial_imag_part( const ex & x, const ex & y ) -->
  <function name="GiNaC::binomial_imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::binomial_real_part( const ex & x, const ex & y ) -->
  <function name="GiNaC::binomial_real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::binomial_sym( const ex & x, const numeric & y ) -->
  <function name="GiNaC::binomial_sym">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::conjugate_conjugate( const ex & arg ) -->
  <function name="GiNaC::conjugate_conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::conjugate_eval( const ex & arg ) -->
  <function name="GiNaC::conjugate_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::conjugate_evalf( const ex & arg ) -->
  <function name="GiNaC::conjugate_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::conjugate_expl_derivative( const ex & arg, const symbol & s ) -->
  <function name="GiNaC::conjugate_expl_derivative">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::conjugate_imag_part( const ex & arg ) -->
  <function name="GiNaC::conjugate_imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::conjugate_info( const ex & arg, unsigned inf ) -->
  <function name="GiNaC::conjugate_info">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void GiNaC::conjugate_print_latex( const ex & arg, const print_context & c ) -->
  <function name="GiNaC::conjugate_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::conjugate_real_part( const ex & arg ) -->
  <function name="GiNaC::conjugate_real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::csgn_conjugate( const ex & arg ) -->
  <function name="GiNaC::csgn_conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::csgn_eval( const ex & arg ) -->
  <function name="GiNaC::csgn_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::csgn_evalf( const ex & arg ) -->
  <function name="GiNaC::csgn_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::csgn_imag_part( const ex & arg ) -->
  <function name="GiNaC::csgn_imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::csgn_power( const ex & arg, const ex & exp ) -->
  <function name="GiNaC::csgn_power">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::csgn_real_part( const ex & arg ) -->
  <function name="GiNaC::csgn_real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::eta_conjugate( const ex & x, const ex & y ) -->
  <function name="GiNaC::eta_conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::eta_eval( const ex & x, const ex & y ) -->
  <function name="GiNaC::eta_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::eta_evalf( const ex & x, const ex & y ) -->
  <function name="GiNaC::eta_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::eta_imag_part( const ex & x, const ex & y ) -->
  <function name="GiNaC::eta_imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::eta_real_part( const ex & x, const ex & y ) -->
  <function name="GiNaC::eta_real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::factorial_conjugate( const ex & x ) -->
  <function name="GiNaC::factorial_conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::factorial_eval( const ex & x ) -->
  <function name="GiNaC::factorial_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::factorial_evalf( const ex & x ) -->
  <function name="GiNaC::factorial_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::factorial_imag_part( const ex & x ) -->
  <function name="GiNaC::factorial_imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::factorial_print_dflt_latex( const ex & x, const print_context & c ) -->
  <function name="GiNaC::factorial_print_dflt_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::factorial_real_part( const ex & x ) -->
  <function name="GiNaC::factorial_real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::func_arg_info( const ex & arg, unsigned inf ) -->
  <function name="GiNaC::func_arg_info">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::imag_part_conjugate( const ex & arg ) -->
  <function name="GiNaC::imag_part_conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::imag_part_eval( const ex & arg ) -->
  <function name="GiNaC::imag_part_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::imag_part_evalf( const ex & arg ) -->
  <function name="GiNaC::imag_part_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::imag_part_expl_derivative( const ex & arg, const symbol & s ) -->
  <function name="GiNaC::imag_part_expl_derivative">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::imag_part_imag_part( const ex & arg ) -->
  <function name="GiNaC::imag_part_imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::imag_part_print_latex( const ex & arg, const print_context & c ) -->
  <function name="GiNaC::imag_part_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::imag_part_real_part( const ex & arg ) -->
  <function name="GiNaC::imag_part_real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::real_part_conjugate( const ex & arg ) -->
  <function name="GiNaC::real_part_conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::real_part_eval( const ex & arg ) -->
  <function name="GiNaC::real_part_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::real_part_evalf( const ex & arg ) -->
  <function name="GiNaC::real_part_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::real_part_expl_derivative( const ex & arg, const symbol & s ) -->
  <function name="GiNaC::real_part_expl_derivative">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::real_part_imag_part( const ex & arg ) -->
  <function name="GiNaC::real_part_imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::real_part_print_latex( const ex & arg, const print_context & c ) -->
  <function name="GiNaC::real_part_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::real_part_real_part( const ex & arg ) -->
  <function name="GiNaC::real_part_real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::step_conjugate( const ex & arg ) -->
  <function name="GiNaC::step_conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::step_eval( const ex & arg ) -->
  <function name="GiNaC::step_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::step_evalf( const ex & arg ) -->
  <function name="GiNaC::step_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::step_imag_part( const ex & arg ) -->
  <function name="GiNaC::step_imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::step_real_part( const ex & arg ) -->
  <function name="GiNaC::step_real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::symbolset::has( const ex & e ) -->
  <function name="GiNaC::symbolset::has">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::symbolset::insert_symbols( const ex & e ) -->
  <function name="GiNaC::symbolset::insert_symbols">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::zetaderiv_deriv( const ex & n, const ex & x, unsigned deriv_param ) -->
  <function name="GiNaC::zetaderiv_deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::zetaderiv_eval( const ex & n, const ex & x ) -->
  <function name="GiNaC::zetaderiv_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void GiNaC::print_sym_pow( const print_context & c, const symbol & x, int exp ) -->
  <function name="GiNaC::print_sym_pow">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void power::archive( archive_node & n ) -->
  <function name="power::archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- ex power::coeff( const ex & s, int n ) -->
  <function name="power::coeff">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int power::compare_same_type( const basic & other ) -->
  <function name="power::compare_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex power::conjugate( void ) -->
  <function name="power::conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- int power::degree( const ex & s ) -->
  <function name="power::degree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex power::derivative( const symbol & s ) -->
  <function name="power::derivative">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex power::eval( void ) -->
  <function name="power::eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex power::eval_ncmul( const exvector & v ) -->
  <function name="power::eval_ncmul">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex power::evalf( void ) -->
  <function name="power::evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex power::evalm( void ) -->
  <function name="power::evalm">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex power::expand( unsigned options ) -->
  <function name="power::expand">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool power::has( const ex & other, unsigned options ) -->
  <function name="power::has">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex power::imag_part( void ) -->
  <function name="power::imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- bool power::info( unsigned inf ) -->
  <function name="power::info">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool power::is_polynomial( const ex & var ) -->
  <function name="power::is_polynomial">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- int power::ldegree( const ex & s ) -->
  <function name="power::ldegree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex power::map( map_function & f ) -->
  <function name="power::map">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- size_t power::nops( void ) -->
  <function name="power::nops">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="size_t"/>
    <use-retval/>
  </function>
  <!-- ex power::op( size_t i ) -->
  <function name="power::op">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void power::read_archive( const archive_node & n, lst & sym_lst ) -->
  <function name="power::read_archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- ex power::real_part( void ) -->
  <function name="power::real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- unsigned power::return_type( void ) -->
  <function name="power::return_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <use-retval/>
  </function>
  <!-- return_type_t power::return_type_tinfo( void ) -->
  <function name="power::return_type_tinfo">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="return_type_t"/>
    <use-retval/>
  </function>
  <!-- ex power::subs( const exmap & m, unsigned options ) -->
  <function name="power::subs">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::beta_deriv( const ex & x, const ex & y, unsigned deriv_param ) -->
  <function name="GiNaC::beta_deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::beta_eval( const ex & x, const ex & y ) -->
  <function name="GiNaC::beta_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::beta_evalf( const ex & x, const ex & y ) -->
  <function name="GiNaC::beta_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::lgamma_conjugate( const ex & x ) -->
  <function name="GiNaC::lgamma_conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::lgamma_deriv( const ex & x, unsigned deriv_param ) -->
  <function name="GiNaC::lgamma_deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::lgamma_eval( const ex & x ) -->
  <function name="GiNaC::lgamma_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::lgamma_evalf( const ex & x ) -->
  <function name="GiNaC::lgamma_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::psi1_deriv( const ex & x, unsigned deriv_param ) -->
  <function name="GiNaC::psi1_deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::psi1_eval( const ex & x ) -->
  <function name="GiNaC::psi1_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::psi1_evalf( const ex & x ) -->
  <function name="GiNaC::psi1_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::psi2_deriv( const ex & n, const ex & x, unsigned deriv_param ) -->
  <function name="GiNaC::psi2_deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::psi2_eval( const ex & n, const ex & x ) -->
  <function name="GiNaC::psi2_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::psi2_evalf( const ex & n, const ex & x ) -->
  <function name="GiNaC::psi2_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::tgamma_conjugate( const ex & x ) -->
  <function name="GiNaC::tgamma_conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::tgamma_deriv( const ex & x, unsigned deriv_param ) -->
  <function name="GiNaC::tgamma_deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::tgamma_eval( const ex & x ) -->
  <function name="GiNaC::tgamma_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::tgamma_evalf( const ex & x ) -->
  <function name="GiNaC::tgamma_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- synthesize_func GiNaC::find_factory_fcn( const std::string & name ) -->
  <function name="GiNaC::find_factory_fcn">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="synthesize_func"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- unsigned GiNaC::read_unsigned( std::istream & is ) -->
  <function name="GiNaC::read_unsigned">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <pure/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void GiNaC::write_unsigned( std::ostream & os, unsigned val ) -->
  <function name="GiNaC::write_unsigned">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void fderivative::archive( archive_node & n ) -->
  <function name="fderivative::archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- int fderivative::compare_same_type( const basic & other ) -->
  <function name="fderivative::compare_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex fderivative::derivative( const symbol & s ) -->
  <function name="fderivative::derivative">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex fderivative::eval( void ) -->
  <function name="fderivative::eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- bool fderivative::is_equal_same_type( const basic & other ) -->
  <function name="fderivative::is_equal_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool fderivative::match_same_type( const basic & other ) -->
  <function name="fderivative::match_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void fderivative::print( const print_context & c, unsigned level ) -->
  <function name="fderivative::print">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void fderivative::read_archive( const archive_node & n, lst & sym_lst ) -->
  <function name="fderivative::read_archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- ex fderivative::series( const relational & r, int order, unsigned options ) -->
  <function name="fderivative::series">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex fderivative::thiscontainer( const exvector & v ) -->
  <function name="fderivative::thiscontainer">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::EllipticE_deriv( const ex & k, unsigned deriv_param ) -->
  <function name="GiNaC::EllipticE_deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::EllipticE_eval( const ex & k ) -->
  <function name="GiNaC::EllipticE_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::EllipticE_evalf( const ex & k ) -->
  <function name="GiNaC::EllipticE_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::EllipticE_print_latex( const ex & k, const print_context & c ) -->
  <function name="GiNaC::EllipticE_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::EllipticE_series( const ex & k, const relational & rel, int order, unsigned options ) -->
  <function name="GiNaC::EllipticE_series">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::EllipticK_deriv( const ex & k, unsigned deriv_param ) -->
  <function name="GiNaC::EllipticK_deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::EllipticK_eval( const ex & k ) -->
  <function name="GiNaC::EllipticK_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::EllipticK_evalf( const ex & k ) -->
  <function name="GiNaC::EllipticK_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::EllipticK_print_latex( const ex & k, const print_context & c ) -->
  <function name="GiNaC::EllipticK_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::EllipticK_series( const ex & k, const relational & rel, int order, unsigned options ) -->
  <function name="GiNaC::EllipticK_series">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- cln::cl_N GiNaC::__anon1::agm_helper_second_kind( const cln::cl_N & a_0, const cln::cl_N & b_0, const cln::cl_N & c_0 ) -->
  <function name="GiNaC::__anon1::agm_helper_second_kind">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="cln::cl_N"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- cln::cl_N GiNaC::__anon1::arithmetic_geometric_mean( const cln::cl_N & a_0, const cln::cl_N & b_0 ) -->
  <function name="GiNaC::__anon1::arithmetic_geometric_mean">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="cln::cl_N"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::iterated_integral2_eval( const ex & kernel_lst, const ex & lambda ) -->
  <function name="GiNaC::iterated_integral2_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::iterated_integral2_evalf( const ex & kernel_lst, const ex & lambda ) -->
  <function name="GiNaC::iterated_integral2_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::iterated_integral3_eval( const ex & kernel_lst, const ex & lambda, const ex & N_trunc ) -->
  <function name="GiNaC::iterated_integral3_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- ex GiNaC::iterated_integral3_evalf( const ex & kernel_lst, const ex & lambda, const ex & N_trunc ) -->
  <function name="GiNaC::iterated_integral3_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- ex GiNaC::iterated_integral_evalf_impl( const ex & kernel_lst, const ex & lambda, const ex & N_trunc ) -->
  <function name="GiNaC::iterated_integral_evalf_impl">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void GiNaC::find_variant_indices( const exvector & v, exvector & variant_indices ) -->
  <function name="GiNaC::find_variant_indices">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- bool GiNaC::hasindex( const ex & x, const ex & sym ) -->
  <function name="GiNaC::hasindex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::idx_symmetrization( const ex & r, const exvector & local_dummy_indices ) -->
  <function name="GiNaC::idx_symmetrization">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- bool GiNaC::indices_consistent( const exvector & v1, const exvector & v2 ) -->
  <function name="GiNaC::indices_consistent">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- size_t GiNaC::number_of_type( const exvector & v ) -->
  <function name="GiNaC::number_of_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="size_t"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::product_to_exvector( const ex & e, exvector & v, bool & non_commutative ) -->
  <function name="GiNaC::product_to_exvector">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- ex GiNaC::rename_dummy_indices( const ex & e, exvector & global_dummy_indices, exvector & local_dummy_indices ) -->
  <function name="GiNaC::rename_dummy_indices">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void scalar_products::add_vectors( const lst & l, const ex & dim ) -->
  <function name="scalar_products::add_vectors">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex ex::antisymmetrize( void ) -->
  <function name="ex::antisymmetrize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- void indexed::archive( archive_node & n ) -->
  <function name="indexed::archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- int indexed::compare_same_type( const basic & other ) -->
  <function name="indexed::compare_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex indexed::derivative( const symbol & s ) -->
  <function name="indexed::derivative">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex indexed::eval( void ) -->
  <function name="indexed::eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex indexed::expand( unsigned options ) -->
  <function name="indexed::expand">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- exvector indexed::get_free_indices( void ) -->
  <function name="indexed::get_free_indices">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="exvector"/>
    <use-retval/>
  </function>
  <!-- ex indexed::imag_part( void ) -->
  <function name="indexed::imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- bool indexed::info( unsigned inf ) -->
  <function name="indexed::info">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void indexed::read_archive( const archive_node & n, lst & sym_lst ) -->
  <function name="indexed::read_archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- ex indexed::real_part( void ) -->
  <function name="indexed::real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- unsigned indexed::return_type( void ) -->
  <function name="indexed::return_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <use-retval/>
  </function>
  <!-- ex ex::symmetrize( void ) -->
  <function name="ex::symmetrize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex ex::symmetrize_cyclic( void ) -->
  <function name="ex::symmetrize_cyclic">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex indexed::thiscontainer( const exvector & v ) -->
  <function name="indexed::thiscontainer">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::adaptivesimpson( const ex & x, const ex & a_in, const ex & b_in, const ex & f, const ex & error ) -->
  <function name="GiNaC::adaptivesimpson">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in"/>
    <arg nr="5" direction="in"/>
  </function>
  <!-- ex GiNaC::subsvalue( const ex & var, const ex & value, const ex & fun ) -->
  <function name="GiNaC::subsvalue">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void integral::archive( archive_node & n ) -->
  <function name="integral::archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- int integral::compare_same_type( const basic & other ) -->
  <function name="integral::compare_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex integral::conjugate( void ) -->
  <function name="integral::conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- int integral::degree( const ex & s ) -->
  <function name="integral::degree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex integral::derivative( const symbol & s ) -->
  <function name="integral::derivative">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex integral::eval( void ) -->
  <function name="integral::eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex integral::eval_integ( void ) -->
  <function name="integral::eval_integ">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex integral::eval_ncmul( const exvector & v ) -->
  <function name="integral::eval_ncmul">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex integral::evalf( void ) -->
  <function name="integral::evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex integral::expand( unsigned options ) -->
  <function name="integral::expand">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int integral::ldegree( const ex & s ) -->
  <function name="integral::ldegree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex & integral::let_op( size_t i ) -->
  <function name="integral::let_op">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex &amp;"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- size_t integral::nops( void ) -->
  <function name="integral::nops">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="size_t"/>
    <use-retval/>
  </function>
  <!-- ex integral::op( size_t i ) -->
  <function name="integral::op">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void integral::read_archive( const archive_node & n, lst & sym_lst ) -->
  <function name="integral::read_archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- unsigned integral::return_type( void ) -->
  <function name="integral::return_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <use-retval/>
  </function>
  <!-- return_type_t integral::return_type_tinfo( void ) -->
  <function name="integral::return_type_tinfo">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="return_type_t"/>
    <use-retval/>
  </function>
  <!-- bool mul::can_make_flat( const expair & p ) -->
  <function name="mul::can_make_flat">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex mul::coeff( const ex & s, int n ) -->
  <function name="mul::coeff">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void mul::combine_overall_coeff( const ex & c ) -->
  <function name="mul::combine_overall_coeff">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- int mul::compare_same_type( const basic & other ) -->
  <function name="mul::compare_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex mul::conjugate( void ) -->
  <function name="mul::conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex mul::default_overall_coeff( void ) -->
  <function name="mul::default_overall_coeff">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- int mul::degree( const ex & s ) -->
  <function name="mul::degree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex mul::derivative( const symbol & s ) -->
  <function name="mul::derivative">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex mul::eval( void ) -->
  <function name="mul::eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex mul::eval_ncmul( const exvector & v ) -->
  <function name="mul::eval_ncmul">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex mul::evalf( void ) -->
  <function name="mul::evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex mul::evalm( void ) -->
  <function name="mul::evalm">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- bool mul::expair_needs_further_processing( epp it ) -->
  <function name="mul::expair_needs_further_processing">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex mul::expand( unsigned options ) -->
  <function name="mul::expand">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool mul::has( const ex & pattern, unsigned options ) -->
  <function name="mul::has">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex mul::imag_part( void ) -->
  <function name="mul::imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- bool mul::info( unsigned inf ) -->
  <function name="mul::info">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool mul::is_polynomial( const ex & var ) -->
  <function name="mul::is_polynomial">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- int mul::ldegree( const ex & s ) -->
  <function name="mul::ldegree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex mul::real_part( void ) -->
  <function name="mul::real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex mul::recombine_pair_to_ex( const expair & p ) -->
  <function name="mul::recombine_pair_to_ex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- unsigned mul::return_type( void ) -->
  <function name="mul::return_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <use-retval/>
  </function>
  <!-- return_type_t mul::return_type_tinfo( void ) -->
  <function name="mul::return_type_tinfo">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="return_type_t"/>
    <use-retval/>
  </function>
  <!-- expair mul::split_ex_to_pair( const ex & e ) -->
  <function name="mul::split_ex_to_pair">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="expair"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex mul::thisexpairseq( const epvector & v, const ex & oc, bool do_index_renaming ) -->
  <function name="mul::thisexpairseq">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void constant::archive( archive_node & n ) -->
  <function name="constant::archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- unsigned constant::calchash( void ) -->
  <function name="constant::calchash">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <use-retval/>
  </function>
  <!-- int constant::compare_same_type( const basic & other ) -->
  <function name="constant::compare_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex constant::conjugate( void ) -->
  <function name="constant::conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex constant::derivative( const symbol & s ) -->
  <function name="constant::derivative">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex constant::evalf( void ) -->
  <function name="constant::evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex constant::imag_part( void ) -->
  <function name="constant::imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- bool constant::info( unsigned inf ) -->
  <function name="constant::info">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool constant::is_equal_same_type( const basic & other ) -->
  <function name="constant::is_equal_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool constant::is_polynomial( const ex & var ) -->
  <function name="constant::is_polynomial">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void constant::read_archive( const archive_node & n, lst & sym_lst ) -->
  <function name="constant::read_archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- ex constant::real_part( void ) -->
  <function name="constant::real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- const cln::cl_N GiNaC::Li2_( const cln::cl_N & value ) -->
  <function name="GiNaC::Li2_">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const cln::cl_N"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::coerce( T1 & dst, const T2 & arg ) -->
  <function name="GiNaC::coerce">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- cln::float_format_t GiNaC::guess_precision( const cln::cl_N & x ) -->
  <function name="GiNaC::guess_precision">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="cln::float_format_t"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- cln::cl_N GiNaC::lanczos_coeffs::calc_lanczos_A( const cln::cl_N &  ) const -->
  <function name="GiNaC::lanczos_coeffs::calc_lanczos_A">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="cln::cl_N"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- int GiNaC::lanczos_coeffs::get_order( void ) -->
  <function name="GiNaC::lanczos_coeffs::get_order">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
  </function>
  <!-- bool GiNaC::lanczos_coeffs::sufficiently_accurate( int digits ) -->
  <function name="GiNaC::lanczos_coeffs::sufficiently_accurate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- const cln::cl_F GiNaC::make_real_float( const cln::cl_idecoded_float & dec ) -->
  <function name="GiNaC::make_real_float">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const cln::cl_F"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::print_integer_csrc( const print_context & c, const cln::cl_I & x ) -->
  <function name="GiNaC::print_integer_csrc">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void GiNaC::print_real_cl_N( const print_context & c, const cln::cl_R & x ) -->
  <function name="GiNaC::print_real_cl_N">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void GiNaC::print_real_csrc( const print_context & c, const cln::cl_R & x ) -->
  <function name="GiNaC::print_real_csrc">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void GiNaC::print_real_number( const print_context & c, const cln::cl_R & x ) -->
  <function name="GiNaC::print_real_number">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- const cln::cl_F GiNaC::read_real_float( std::istream & s ) -->
  <function name="GiNaC::read_real_float">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const cln::cl_F"/>
    <pure/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void GiNaC::write_real_float( std::ostream & s, const cln::cl_R & n ) -->
  <function name="GiNaC::write_real_float">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void numeric::archive( archive_node & n ) -->
  <function name="numeric::archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- unsigned numeric::calchash( void ) -->
  <function name="numeric::calchash">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <use-retval/>
  </function>
  <!-- ex numeric::coeff( const ex & s, int n ) -->
  <function name="numeric::coeff">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int numeric::compare_same_type( const basic & other ) -->
  <function name="numeric::compare_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex numeric::conjugate( void ) -->
  <function name="numeric::conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex numeric::eval( void ) -->
  <function name="numeric::eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex numeric::evalf( void ) -->
  <function name="numeric::evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- bool numeric::has( const ex & other, unsigned options ) -->
  <function name="numeric::has">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex numeric::imag_part( void ) -->
  <function name="numeric::imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- bool numeric::info( unsigned inf ) -->
  <function name="numeric::info">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool numeric::is_equal_same_type( const basic & other ) -->
  <function name="numeric::is_equal_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool numeric::is_polynomial( const ex & var ) -->
  <function name="numeric::is_polynomial">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- int numeric::ldegree( const ex & s ) -->
  <function name="numeric::ldegree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void numeric::read_archive( const archive_node & n, lst & sym_lst ) -->
  <function name="numeric::read_archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- ex numeric::real_part( void ) -->
  <function name="numeric::real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex add::coeff( const ex & s, int n ) -->
  <function name="add::coeff">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int add::compare_same_type( const basic & other ) -->
  <function name="add::compare_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex add::conjugate( void ) -->
  <function name="add::conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- int add::degree( const ex & s ) -->
  <function name="add::degree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex add::derivative( const symbol & y ) -->
  <function name="add::derivative">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex add::eval( void ) -->
  <function name="add::eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex add::eval_ncmul( const exvector & v ) -->
  <function name="add::eval_ncmul">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex add::evalm( void ) -->
  <function name="add::evalm">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex add::expand( unsigned options ) -->
  <function name="add::expand">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex add::imag_part( void ) -->
  <function name="add::imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- bool add::info( unsigned inf ) -->
  <function name="add::info">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool add::is_polynomial( const ex & var ) -->
  <function name="add::is_polynomial">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- int add::ldegree( const ex & s ) -->
  <function name="add::ldegree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex add::real_part( void ) -->
  <function name="add::real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex add::recombine_pair_to_ex( const expair & p ) -->
  <function name="add::recombine_pair_to_ex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- unsigned add::return_type( void ) -->
  <function name="add::return_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <use-retval/>
  </function>
  <!-- return_type_t add::return_type_tinfo( void ) -->
  <function name="add::return_type_tinfo">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="return_type_t"/>
    <use-retval/>
  </function>
  <!-- expair add::split_ex_to_pair( const ex & e ) -->
  <function name="add::split_ex_to_pair">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="expair"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex add::thisexpairseq( const epvector & v, const ex & oc, bool do_index_renaming ) -->
  <function name="add::thisexpairseq">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- const std::string & GiNaC::get_default_TeX_name( const std::string & name ) -->
  <function name="GiNaC::get_default_TeX_name">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const std::string &amp;"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void symbol::archive( archive_node & n ) -->
  <function name="symbol::archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- unsigned symbol::calchash( void ) -->
  <function name="symbol::calchash">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <use-retval/>
  </function>
  <!-- int symbol::compare_same_type( const basic & other ) -->
  <function name="symbol::compare_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex symbol::conjugate( void ) -->
  <function name="symbol::conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex symbol::derivative( const symbol & s ) -->
  <function name="symbol::derivative">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex symbol::imag_part( void ) -->
  <function name="symbol::imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- bool symbol::info( unsigned inf ) -->
  <function name="symbol::info">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool symbol::is_equal_same_type( const basic & other ) -->
  <function name="symbol::is_equal_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool symbol::is_polynomial( const ex & var ) -->
  <function name="symbol::is_polynomial">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void symbol::read_archive( const archive_node & n, lst & sym_lst ) -->
  <function name="symbol::read_archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- ex symbol::real_part( void ) -->
  <function name="symbol::real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex ncmul::coeff( const ex & s, int n ) -->
  <function name="ncmul::coeff">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int ncmul::compare_same_type( const basic & other ) -->
  <function name="ncmul::compare_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex ncmul::conjugate( void ) -->
  <function name="ncmul::conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- int ncmul::degree( const ex & s ) -->
  <function name="ncmul::degree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex ncmul::derivative( const symbol & s ) -->
  <function name="ncmul::derivative">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex ncmul::eval( void ) -->
  <function name="ncmul::eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex ncmul::evalm( void ) -->
  <function name="ncmul::evalm">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex ncmul::expand( unsigned options ) -->
  <function name="ncmul::expand">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex ncmul::imag_part( void ) -->
  <function name="ncmul::imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- bool ncmul::info( unsigned inf ) -->
  <function name="ncmul::info">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int ncmul::ldegree( const ex & s ) -->
  <function name="ncmul::ldegree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex ncmul::real_part( void ) -->
  <function name="ncmul::real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- unsigned ncmul::return_type( void ) -->
  <function name="ncmul::return_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <use-retval/>
  </function>
  <!-- return_type_t ncmul::return_type_tinfo( void ) -->
  <function name="ncmul::return_type_tinfo">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="return_type_t"/>
    <use-retval/>
  </function>
  <!-- ex ncmul::thiscontainer( const exvector & v ) -->
  <function name="ncmul::thiscontainer">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::__anon1::B_eisenstein_series( const ex & q, const numeric & N_level, const numeric & K, const numeric & N_order ) -->
  <function name="GiNaC::__anon1::B_eisenstein_series">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in"/>
  </function>
  <!-- ex GiNaC::__anon1::E_eisenstein_series( const ex & q, const numeric & k, const numeric & N_level, const numeric & a, const numeric & b, const numeric & K, const numeric & N_order ) -->
  <function name="GiNaC::__anon1::E_eisenstein_series">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in"/>
    <arg nr="5" direction="in"/>
    <arg nr="6" direction="in"/>
    <arg nr="7" direction="in"/>
  </function>
  <!-- ex GiNaC::__anon1::Li_negative::get_symbolic_value( int n, const ex & x_val ) -->
  <function name="GiNaC::__anon1::Li_negative::get_symbolic_value">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in"/>
  </function>
  <!-- numeric GiNaC::__anon1::coefficient_a0( const numeric & k, const numeric & a, const numeric & b ) -->
  <function name="GiNaC::__anon1::coefficient_a0">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="numeric"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- numeric GiNaC::__anon1::divisor_function( const numeric & n, const numeric & a, const numeric & b, const numeric & k ) -->
  <function name="GiNaC::__anon1::divisor_function">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="numeric"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in"/>
  </function>
  <!-- ex GiNaC::__anon1::eisenstein_series( const numeric & k, const ex & q, const numeric & a, const numeric & b, const numeric & N ) -->
  <function name="GiNaC::__anon1::eisenstein_series">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in"/>
    <arg nr="5" direction="in"/>
  </function>
  <!-- numeric GiNaC::__anon1::kronecker_symbol_prime( const numeric & a, const numeric & n ) -->
  <function name="GiNaC::__anon1::kronecker_symbol_prime">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="numeric"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- int integration_kernel::compare_same_type( const basic & other ) -->
  <function name="integration_kernel::compare_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex & multiple_polylog_kernel::let_op( size_t i ) -->
  <function name="multiple_polylog_kernel::let_op">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex &amp;"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- size_t multiple_polylog_kernel::nops( void ) -->
  <function name="multiple_polylog_kernel::nops">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="size_t"/>
    <use-retval/>
  </function>
  <!-- ex multiple_polylog_kernel::op( size_t i ) -->
  <function name="multiple_polylog_kernel::op">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex integration_kernel::series( const relational & r, int order, unsigned options ) -->
  <function name="integration_kernel::series">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::G2_eval( const ex & x_, const ex & y ) -->
  <function name="GiNaC::G2_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::G2_evalf( const ex & x_, const ex & y ) -->
  <function name="GiNaC::G2_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::G3_eval( const ex & x_, const ex & s_, const ex & y ) -->
  <function name="GiNaC::G3_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- ex GiNaC::G3_evalf( const ex & x_, const ex & s_, const ex & y ) -->
  <function name="GiNaC::G3_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- ex GiNaC::H_deriv( const ex & m_, const ex & x, unsigned deriv_param ) -->
  <function name="GiNaC::H_deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::H_eval( const ex & m_, const ex & x ) -->
  <function name="GiNaC::H_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::H_evalf( const ex & x1, const ex & x2 ) -->
  <function name="GiNaC::H_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void GiNaC::H_print_latex( const ex & m_, const ex & x, const print_context & c ) -->
  <function name="GiNaC::H_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- ex GiNaC::H_series( const ex & m, const ex & x, const relational & rel, int order, unsigned options ) -->
  <function name="GiNaC::H_series">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::Li_deriv( const ex & m_, const ex & x_, unsigned deriv_param ) -->
  <function name="GiNaC::Li_deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::Li_eval( const ex & m_, const ex & x_ ) -->
  <function name="GiNaC::Li_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::Li_evalf( const ex & m_, const ex & x_ ) -->
  <function name="GiNaC::Li_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void GiNaC::Li_print_latex( const ex & m_, const ex & x_, const print_context & c ) -->
  <function name="GiNaC::Li_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- ex GiNaC::Li_series( const ex & m, const ex & x, const relational & rel, int order, unsigned options ) -->
  <function name="GiNaC::Li_series">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::S_deriv( const ex & n, const ex & p, const ex & x, unsigned deriv_param ) -->
  <function name="GiNaC::S_deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::S_eval( const ex & n, const ex & p, const ex & x ) -->
  <function name="GiNaC::S_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- ex GiNaC::S_evalf( const ex & n, const ex & p, const ex & x ) -->
  <function name="GiNaC::S_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void GiNaC::S_print_latex( const ex & n, const ex & p, const ex & x, const print_context & c ) -->
  <function name="GiNaC::S_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in"/>
  </function>
  <!-- ex GiNaC::S_series( const ex & n, const ex & p, const ex & x, const relational & rel, int order, unsigned options ) -->
  <function name="GiNaC::S_series">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in"/>
    <arg nr="5" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="6" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- cln::cl_N GiNaC::__anon1::Li2_do_sum( const cln::cl_N & x ) -->
  <function name="GiNaC::__anon1::Li2_do_sum">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="cln::cl_N"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- cln::cl_N GiNaC::__anon1::Li2_do_sum_Xn( const cln::cl_N & x ) -->
  <function name="GiNaC::__anon1::Li2_do_sum_Xn">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="cln::cl_N"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- cln::cl_N GiNaC::__anon1::Li_projection( int n, const cln::cl_N & x, const cln::float_format_t & prec ) -->
  <function name="GiNaC::__anon1::Li_projection">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="cln::cl_N"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- cln::cl_N GiNaC::__anon1::Lin_do_sum( int n, const cln::cl_N & x ) -->
  <function name="GiNaC::__anon1::Lin_do_sum">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="cln::cl_N"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in"/>
  </function>
  <!-- cln::cl_N GiNaC::__anon1::Lin_do_sum_Xn( int n, const cln::cl_N & x ) -->
  <function name="GiNaC::__anon1::Lin_do_sum_Xn">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="cln::cl_N"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in"/>
  </function>
  <!-- const cln::cl_N GiNaC::__anon1::Lin_numeric( const int n, const cln::cl_N & x ) -->
  <function name="GiNaC::__anon1::Lin_numeric">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const cln::cl_N"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in"/>
  </function>
  <!-- const cln::cl_N GiNaC::__anon1::S_num( int n, int p, const cln::cl_N & x ) -->
  <function name="GiNaC::__anon1::S_num">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const cln::cl_N"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void GiNaC::__anon1::double_Xn( void ) -->
  <function name="GiNaC::__anon1::double_Xn">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void GiNaC::__anon1::fill_Xn( int n ) -->
  <function name="GiNaC::__anon1::fill_Xn">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::__anon2::G_eval( const Gparameter & a, int scale, const exvector & gsyms ) -->
  <function name="GiNaC::__anon2::G_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in"/>
  </function>
  <!-- ex GiNaC::__anon2::G_eval1( int a, int scale, const exvector & gsyms ) -->
  <function name="GiNaC::__anon2::G_eval1">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in"/>
  </function>
  <!-- ex GiNaC::__anon2::G_eval_to_G( const Gparameter & a, int scale, const exvector & gsyms ) -->
  <function name="GiNaC::__anon2::G_eval_to_G">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in"/>
  </function>
  <!-- lst GiNaC::__anon2::convert_parameter_Li_to_H( const lst & m, const lst & x, ex & pf ) -->
  <function name="GiNaC::__anon2::convert_parameter_Li_to_H">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="lst"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- Gparameter GiNaC::__anon2::convert_pending_integrals_G( const Gparameter & pending_integrals ) -->
  <function name="GiNaC::__anon2::convert_pending_integrals_G">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="Gparameter"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::__anon2::depth_one_trafo_G( const Gparameter & pending_integrals, const Gparameter & a, int scale, const exvector & gsyms ) -->
  <function name="GiNaC::__anon2::depth_one_trafo_G">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in"/>
  </function>
  <!-- ex GiNaC::__anon2::mLi_numeric( const lst & m, const lst & x ) -->
  <function name="GiNaC::__anon2::mLi_numeric">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- cln::cl_N GiNaC::__anon2::multipleLi_do_sum( const std::vector<int> & s, const std::vector<cln::cl_N> & x ) -->
  <function name="GiNaC::__anon2::multipleLi_do_sum">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="cln::cl_N"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- Gparameter GiNaC::__anon2::prepare_pending_integrals( const Gparameter & pending_integrals, int scale ) -->
  <function name="GiNaC::__anon2::prepare_pending_integrals">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="Gparameter"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::__anon2::trailing_zeros_G( const Gparameter & a, int scale, const exvector & gsyms ) -->
  <function name="GiNaC::__anon2::trailing_zeros_G">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in"/>
  </function>
  <!-- cln::cl_N GiNaC::__anon3::C( int n, int p ) -->
  <function name="GiNaC::__anon3::C">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="cln::cl_N"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- cln::cl_N GiNaC::__anon3::S_do_sum( int n, int p, const cln::cl_N & x, const cln::float_format_t & prec ) -->
  <function name="GiNaC::__anon3::S_do_sum">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="cln::cl_N"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in"/>
  </function>
  <!-- cln::cl_N GiNaC::__anon3::S_projection( int n, int p, const cln::cl_N & x, const cln::float_format_t & prec ) -->
  <function name="GiNaC::__anon3::S_projection">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="cln::cl_N"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in"/>
  </function>
  <!-- cln::cl_N GiNaC::__anon3::a_k( int k ) -->
  <function name="GiNaC::__anon3::a_k">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="cln::cl_N"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- cln::cl_N GiNaC::__anon3::b_k( int k ) -->
  <function name="GiNaC::__anon3::b_k">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="cln::cl_N"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void GiNaC::__anon3::fill_Yn( int n, const cln::float_format_t & prec ) -->
  <function name="GiNaC::__anon3::fill_Yn">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void GiNaC::__anon3::make_Yn_longer( int newsize, const cln::float_format_t & prec ) -->
  <function name="GiNaC::__anon3::make_Yn_longer">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in"/>
  </function>
  <!-- cln::cl_N GiNaC::__anon4::H_do_sum( const std::vector<int> & m, const cln::cl_N & x ) -->
  <function name="GiNaC::__anon4::H_do_sum">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="cln::cl_N"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::__anon4::convert_H_to_zeta( const lst & m ) -->
  <function name="GiNaC::__anon4::convert_H_to_zeta">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::__anon4::convert_parameter_H_to_Li( const lst & l, lst & m, lst & s, ex & pf ) -->
  <function name="GiNaC::__anon4::convert_parameter_H_to_Li">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="inout"/>
    <arg nr="4" direction="inout"/>
  </function>
  <!-- ex GiNaC::__anon4::trafo_H_1mxt1px_prepend_minusone( const ex & e, const ex & arg ) -->
  <function name="GiNaC::__anon4::trafo_H_1mxt1px_prepend_minusone">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::__anon4::trafo_H_1mxt1px_prepend_one( const ex & e, const ex & arg ) -->
  <function name="GiNaC::__anon4::trafo_H_1mxt1px_prepend_one">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::__anon4::trafo_H_1tx_prepend_minusone( const ex & e, const ex & arg ) -->
  <function name="GiNaC::__anon4::trafo_H_1tx_prepend_minusone">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::__anon4::trafo_H_1tx_prepend_zero( const ex & e, const ex & arg ) -->
  <function name="GiNaC::__anon4::trafo_H_1tx_prepend_zero">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::__anon4::trafo_H_mult( const ex & h1, const ex & h2 ) -->
  <function name="GiNaC::__anon4::trafo_H_mult">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::__anon4::trafo_H_prepend_one( const ex & e, const ex & arg ) -->
  <function name="GiNaC::__anon4::trafo_H_prepend_one">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void GiNaC::__anon5::halfcyclic_convolute( const std::vector<cln::cl_N> & a, const std::vector<cln::cl_N> & b, std::vector<cln::cl_N> & c ) -->
  <function name="GiNaC::__anon5::halfcyclic_convolute">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- cln::cl_N GiNaC::__anon5::zeta_do_Hoelder_convolution( const std::vector<int> & m_, const std::vector<int> & s_ ) -->
  <function name="GiNaC::__anon5::zeta_do_Hoelder_convolution">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="cln::cl_N"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- cln::cl_N GiNaC::__anon5::zeta_do_sum_Crandall( const std::vector<int> & s ) -->
  <function name="GiNaC::__anon5::zeta_do_sum_Crandall">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="cln::cl_N"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- cln::cl_N GiNaC::__anon5::zeta_do_sum_simple( const std::vector<int> & r ) -->
  <function name="GiNaC::__anon5::zeta_do_sum_simple">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="cln::cl_N"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::zeta1_deriv( const ex & m, unsigned deriv_param ) -->
  <function name="GiNaC::zeta1_deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::zeta1_eval( const ex & m ) -->
  <function name="GiNaC::zeta1_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::zeta1_evalf( const ex & x ) -->
  <function name="GiNaC::zeta1_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::zeta1_print_latex( const ex & m_, const print_context & c ) -->
  <function name="GiNaC::zeta1_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::zeta2_deriv( const ex & m, const ex & s, unsigned deriv_param ) -->
  <function name="GiNaC::zeta2_deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex GiNaC::zeta2_eval( const ex & m, const ex & s_ ) -->
  <function name="GiNaC::zeta2_eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::zeta2_evalf( const ex & x, const ex & s ) -->
  <function name="GiNaC::zeta2_evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void GiNaC::zeta2_print_latex( const ex & m_, const ex & s_, const print_context & c ) -->
  <function name="GiNaC::zeta2_print_latex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void GiNaC::excompiler::add_opened_module( void * module, const std::string & name, bool clean_up ) -->
  <function name="GiNaC::excompiler::add_opened_module">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void GiNaC::excompiler::clean_up( const std::vector<filedesc>::const_iterator it ) -->
  <function name="GiNaC::excompiler::clean_up">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::excompiler::compile_src_file( const std::string filename, bool clean_up ) -->
  <function name="GiNaC::excompiler::compile_src_file">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void GiNaC::excompiler::create_src_file( std::string & filename, std::ofstream & ofs ) -->
  <function name="GiNaC::excompiler::create_src_file">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- void * GiNaC::excompiler::link_so_file( const std::string filename, bool clean_up ) -->
  <function name="GiNaC::excompiler::link_so_file">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void *"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void GiNaC::excompiler::unlink( const std::string filename ) -->
  <function name="GiNaC::excompiler::unlink">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::__anon1::berlekamp( const umodpoly & a, upvec & upv ) -->
  <function name="GiNaC::__anon1::berlekamp">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- cl_I GiNaC::__anon1::calc_bound( const ex & a, const ex & x, int maxdeg ) -->
  <function name="GiNaC::__anon1::calc_bound">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="cl_I"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void GiNaC::__anon1::change_modulus( const cl_modint_ring & R, umodpoly & a ) -->
  <function name="GiNaC::__anon1::change_modulus">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- bool GiNaC::__anon1::checkdivisors( const lst & f ) -->
  <function name="GiNaC::__anon1::checkdivisors">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- int GiNaC::__anon1::degree( const T & p ) -->
  <function name="GiNaC::__anon1::degree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::__anon1::deriv( const umodpoly & a, umodpoly & d ) -->
  <function name="GiNaC::__anon1::deriv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- void GiNaC::__anon1::distinct_degree_factor( const umodpoly & a_, vector<int> & degrees, upvec & ddfactors ) -->
  <function name="GiNaC::__anon1::distinct_degree_factor">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void GiNaC::__anon1::div( const umodpoly & a, const umodpoly & b, umodpoly & q ) -->
  <function name="GiNaC::__anon1::div">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void GiNaC::__anon1::eea_lift( const umodpoly & a, const umodpoly & b, const ex & x, unsigned int p, unsigned int k, umodpoly & s_, umodpoly & t_ ) -->
  <function name="GiNaC::__anon1::eea_lift">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="6" direction="inout"/>
    <arg nr="7" direction="inout"/>
  </function>
  <!-- bool GiNaC::__anon1::equal_one( const umodpoly & a ) -->
  <function name="GiNaC::__anon1::equal_one">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::__anon1::expt_pos( umodpoly & a, unsigned int q ) -->
  <function name="GiNaC::__anon1::expt_pos">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void GiNaC::__anon1::exteuclid( const umodpoly & a, const umodpoly & b, umodpoly & s, umodpoly & t ) -->
  <function name="GiNaC::__anon1::exteuclid">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
    <arg nr="4" direction="inout"/>
  </function>
  <!-- ex GiNaC::__anon1::factor1( const ex & poly, unsigned options ) -->
  <function name="GiNaC::__anon1::factor1">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void GiNaC::__anon1::factor_modular( const umodpoly & p, upvec & upv ) -->
  <function name="GiNaC::__anon1::factor_modular">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- ex GiNaC::__anon1::factor_multivariate( const ex & poly, const exset & syms ) -->
  <function name="GiNaC::__anon1::factor_multivariate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- umodpoly & GiNaC::__anon1::factor_partition::left( void ) -->
  <function name="GiNaC::__anon1::factor_partition::left">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="umodpoly &amp;"/>
    <use-retval/>
  </function>
  <!-- bool GiNaC::__anon1::factor_partition::next( void ) -->
  <function name="GiNaC::__anon1::factor_partition::next">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- umodpoly & GiNaC::__anon1::factor_partition::right( void ) -->
  <function name="GiNaC::__anon1::factor_partition::right">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="umodpoly &amp;"/>
    <use-retval/>
  </function>
  <!-- size_t GiNaC::__anon1::factor_partition::size( void ) -->
  <function name="GiNaC::__anon1::factor_partition::size">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="size_t"/>
    <use-retval/>
  </function>
  <!-- size_t GiNaC::__anon1::factor_partition::size_left( void ) -->
  <function name="GiNaC::__anon1::factor_partition::size_left">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="size_t"/>
    <use-retval/>
  </function>
  <!-- size_t GiNaC::__anon1::factor_partition::size_right( void ) -->
  <function name="GiNaC::__anon1::factor_partition::size_right">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="size_t"/>
    <use-retval/>
  </function>
  <!-- void GiNaC::__anon1::factor_partition::split( void ) -->
  <function name="GiNaC::__anon1::factor_partition::split">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void GiNaC::__anon1::factor_partition::split_cached( void ) -->
  <function name="GiNaC::__anon1::factor_partition::split_cached">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- ex GiNaC::__anon1::factor_sqrfree( const ex & poly ) -->
  <function name="GiNaC::__anon1::factor_sqrfree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::__anon1::factor_univariate( const ex & poly, const ex & x, unsigned int & prime ) -->
  <function name="GiNaC::__anon1::factor_univariate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void GiNaC::__anon1::gcd( const umodpoly & a, const umodpoly & b, umodpoly & c ) -->
  <function name="GiNaC::__anon1::gcd">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void GiNaC::__anon1::hensel_univar( const upoly & a_, unsigned int p, const umodpoly & u1_, const umodpoly & w1_, upoly & u, upoly & w ) -->
  <function name="GiNaC::__anon1::hensel_univar">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in"/>
    <arg nr="5" direction="inout"/>
    <arg nr="6" direction="inout"/>
  </function>
  <!-- ex GiNaC::__anon1::make_modular( const ex & e, const cl_modint_ring & R ) -->
  <function name="GiNaC::__anon1::make_modular">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- size_t GiNaC::__anon1::modular_matrix::colsize( void ) -->
  <function name="GiNaC::__anon1::modular_matrix::colsize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="size_t"/>
    <use-retval/>
  </function>
  <!-- bool GiNaC::__anon1::modular_matrix::is_col_zero( size_t col ) -->
  <function name="GiNaC::__anon1::modular_matrix::is_col_zero">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool GiNaC::__anon1::modular_matrix::is_row_zero( size_t row ) -->
  <function name="GiNaC::__anon1::modular_matrix::is_row_zero">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void GiNaC::__anon1::modular_matrix::mul_col( size_t col, const cl_MI x ) -->
  <function name="GiNaC::__anon1::modular_matrix::mul_col">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void GiNaC::__anon1::modular_matrix::mul_row( size_t row, const cl_MI x ) -->
  <function name="GiNaC::__anon1::modular_matrix::mul_row">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in"/>
  </function>
  <!-- mvec::const_iterator GiNaC::__anon1::modular_matrix::row_begin( size_t row ) -->
  <function name="GiNaC::__anon1::modular_matrix::row_begin">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="mvec::const_iterator"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- mvec::const_iterator GiNaC::__anon1::modular_matrix::row_end( size_t row ) -->
  <function name="GiNaC::__anon1::modular_matrix::row_end">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="mvec::const_iterator"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- size_t GiNaC::__anon1::modular_matrix::rowsize( void ) -->
  <function name="GiNaC::__anon1::modular_matrix::rowsize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="size_t"/>
    <use-retval/>
  </function>
  <!-- void GiNaC::__anon1::modular_matrix::set_row( size_t row, const vector<cl_MI> & newrow ) -->
  <function name="GiNaC::__anon1::modular_matrix::set_row">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void GiNaC::__anon1::modular_matrix::sub_col( size_t col1, size_t col2, const cl_MI fac ) -->
  <function name="GiNaC::__anon1::modular_matrix::sub_col">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void GiNaC::__anon1::modular_matrix::sub_row( size_t row1, size_t row2, const cl_MI fac ) -->
  <function name="GiNaC::__anon1::modular_matrix::sub_row">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void GiNaC::__anon1::modular_matrix::switch_col( size_t col1, size_t col2 ) -->
  <function name="GiNaC::__anon1::modular_matrix::switch_col">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void GiNaC::__anon1::modular_matrix::switch_row( size_t row1, size_t row2 ) -->
  <function name="GiNaC::__anon1::modular_matrix::switch_row">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- upvec GiNaC::__anon1::multiterm_eea_lift( const upvec & a, const ex & x, unsigned int p, unsigned int k ) -->
  <function name="GiNaC::__anon1::multiterm_eea_lift">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="upvec"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- unsigned int GiNaC::__anon1::next_prime( unsigned int p ) -->
  <function name="GiNaC::__anon1::next_prime">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned int"/>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool GiNaC::__anon1::normalize_in_field( umodpoly & a ) -->
  <function name="GiNaC::__anon1::normalize_in_field">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <pure/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void GiNaC::__anon1::nullspace( modular_matrix & M, vector<mvec> & basis ) -->
  <function name="GiNaC::__anon1::nullspace">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- ex GiNaC::__anon1::put_factors_into_lst( const ex & e ) -->
  <function name="GiNaC::__anon1::put_factors_into_lst">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::__anon1::q_matrix( const umodpoly & a_, modular_matrix & Q ) -->
  <function name="GiNaC::__anon1::q_matrix">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- void GiNaC::__anon1::reduce_coeff( umodpoly & a, const cl_I & x ) -->
  <function name="GiNaC::__anon1::reduce_coeff">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void GiNaC::__anon1::rem( const umodpoly & a, const umodpoly & b, umodpoly & r ) -->
  <function name="GiNaC::__anon1::rem">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void GiNaC::__anon1::remdiv( const umodpoly & a, const umodpoly & b, umodpoly & r, umodpoly & q ) -->
  <function name="GiNaC::__anon1::remdiv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
    <arg nr="4" direction="inout"/>
  </function>
  <!-- upoly GiNaC::__anon1::replace_lc( const upoly & poly, const cl_I & lc ) -->
  <function name="GiNaC::__anon1::replace_lc">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="upoly"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void GiNaC::__anon1::same_degree_factor( const umodpoly & a, upvec & upv ) -->
  <function name="GiNaC::__anon1::same_degree_factor">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- bool GiNaC::__anon1::squarefree( const umodpoly & a ) -->
  <function name="GiNaC::__anon1::squarefree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GiNaC::__anon1::umodpoly_from_ex( umodpoly & ump, const ex & e, const ex & x, const cl_modint_ring & R ) -->
  <function name="GiNaC::__anon1::umodpoly_from_ex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in"/>
  </function>
  <!-- void GiNaC::__anon1::umodpoly_from_upoly( umodpoly & ump, const upoly & e, const cl_modint_ring & R ) -->
  <function name="GiNaC::__anon1::umodpoly_from_upoly">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- ex GiNaC::__anon1::umodpoly_to_ex( const umodpoly & a, const ex & x ) -->
  <function name="GiNaC::__anon1::umodpoly_to_ex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- umodpoly GiNaC::__anon1::umodpoly_to_umodpoly( const umodpoly & a, const cl_modint_ring & R, unsigned int m ) -->
  <function name="GiNaC::__anon1::umodpoly_to_umodpoly">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="umodpoly"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- upoly GiNaC::__anon1::umodpoly_to_upoly( const umodpoly & a ) -->
  <function name="GiNaC::__anon1::umodpoly_to_upoly">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="upoly"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool GiNaC::__anon1::unequal_one( const umodpoly & a ) -->
  <function name="GiNaC::__anon1::unequal_one">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- upvec GiNaC::__anon1::univar_diophant( const upvec & a, const ex & x, unsigned int m, unsigned int p, unsigned int k ) -->
  <function name="GiNaC::__anon1::univar_diophant">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="upvec"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void GiNaC::__anon1::upoly_from_ex( upoly & up, const ex & e, const ex & x ) -->
  <function name="GiNaC::__anon1::upoly_from_ex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- ex GiNaC::__anon1::upoly_to_ex( const upoly & a, const ex & x ) -->
  <function name="GiNaC::__anon1::upoly_to_ex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- bool GiNaC::is_color_tinfo( const return_type_t & ti ) -->
  <function name="GiNaC::is_color_tinfo">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex GiNaC::permute_free_index_to_front( const exvector & iv3, const exvector & iv2, int & sig ) -->
  <function name="GiNaC::permute_free_index_to_front">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void color::archive( archive_node & n ) -->
  <function name="color::archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- int color::compare_same_type( const basic & other ) -->
  <function name="color::compare_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool su3t::contract_with( exvector::iterator self, exvector::iterator other, exvector & v ) -->
  <function name="su3t::contract_with">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- ex su3d::eval_indexed( const basic & i ) -->
  <function name="su3d::eval_indexed">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex color::eval_ncmul( const exvector & v ) -->
  <function name="color::eval_ncmul">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool color::match_same_type( const basic & other ) -->
  <function name="color::match_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void color::read_archive( const archive_node & n, lst & sym_lst ) -->
  <function name="color::read_archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- return_type_t color::return_type_tinfo( void ) -->
  <function name="color::return_type_tinfo">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="return_type_t"/>
    <use-retval/>
  </function>
  <!-- ex color::thiscontainer( const exvector & v ) -->
  <function name="color::thiscontainer">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex matrix::add_indexed( const ex & self, const ex & other ) -->
  <function name="matrix::add_indexed">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void matrix::archive( archive_node & n ) -->
  <function name="matrix::archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- int matrix::compare_same_type( const basic & other ) -->
  <function name="matrix::compare_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex matrix::conjugate( void ) -->
  <function name="matrix::conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- bool matrix::contract_with( exvector::iterator self, exvector::iterator other, exvector & v ) -->
  <function name="matrix::contract_with">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- ex matrix::eval_indexed( const basic & i ) -->
  <function name="matrix::eval_indexed">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex matrix::imag_part( void ) -->
  <function name="matrix::imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex & matrix::let_op( size_t i ) -->
  <function name="matrix::let_op">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex &amp;"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool matrix::match_same_type( const basic & other ) -->
  <function name="matrix::match_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex matrix::op( size_t i ) -->
  <function name="matrix::op">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void matrix::read_archive( const archive_node & n, lst & sym_lst ) -->
  <function name="matrix::read_archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- ex matrix::real_part( void ) -->
  <function name="matrix::real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex matrix::scalar_mul_indexed( const ex & self, const numeric & other ) -->
  <function name="matrix::scalar_mul_indexed">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex matrix::subs( const exmap & mp, unsigned options ) -->
  <function name="matrix::subs">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- const ex GiNaC::exadd( const ex & lh, const ex & rh ) -->
  <function name="GiNaC::exadd">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- const ex GiNaC::exminus( const ex & lh ) -->
  <function name="GiNaC::exminus">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const ex GiNaC::exmul( const ex & lh, const ex & rh ) -->
  <function name="GiNaC::exmul">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- print_context * GiNaC::get_print_context( std::ios_base & s ) -->
  <function name="GiNaC::get_print_context">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="print_context *"/>
    <pure/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- unsigned GiNaC::get_print_options( std::ios_base & s ) -->
  <function name="GiNaC::get_print_options">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <pure/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void GiNaC::my_ios_callback( std::ios_base::event ev, std::ios_base & s, int i ) -->
  <function name="GiNaC::my_ios_callback">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int GiNaC::my_ios_index( void ) -->
  <function name="GiNaC::my_ios_index">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- void GiNaC::set_print_context( std::ios_base & s, const print_context & c ) -->
  <function name="GiNaC::set_print_context">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void GiNaC::set_print_options( std::ostream & s, unsigned options ) -->
  <function name="GiNaC::set_print_options">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool lst::info( unsigned inf ) -->
  <function name="lst::info">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void GiNaC::add_symbol( const ex & s, sym_desc_vec & v ) -->
  <function name="GiNaC::add_symbol">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- void GiNaC::collect_symbols( const ex & e, sym_desc_vec & v ) -->
  <function name="GiNaC::collect_symbols">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- bool GiNaC::divide_in_z( const ex & a, const ex & b, ex & q, sym_desc_vec::const_iterator var ) -->
  <function name="GiNaC::divide_in_z">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
    <arg nr="4" direction="in"/>
  </function>
  <!-- ex GiNaC::find_common_factor( const ex & e, ex & factor, exmap & repl ) -->
  <function name="GiNaC::find_common_factor">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- ex GiNaC::frac_cancel( const ex & n, const ex & d ) -->
  <function name="GiNaC::frac_cancel">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::gcd_pf_mul( const ex & a, const ex & b, ex * ca, ex * cb ) -->
  <function name="GiNaC::gcd_pf_mul">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
    <arg nr="4" direction="inout"/>
  </function>
  <!-- ex GiNaC::gcd_pf_pow( const ex & a, const ex & b, ex * ca, ex * cb ) -->
  <function name="GiNaC::gcd_pf_pow">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
    <arg nr="4" direction="inout"/>
  </function>
  <!-- ex GiNaC::gcd_pf_pow_pow( const ex & a, const ex & b, ex * ca, ex * cb ) -->
  <function name="GiNaC::gcd_pf_pow_pow">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
    <arg nr="4" direction="inout"/>
  </function>
  <!-- bool GiNaC::get_first_symbol( const ex & e, ex & x ) -->
  <function name="GiNaC::get_first_symbol">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- void GiNaC::get_symbol_stats( const ex & a, const ex & b, sym_desc_vec & v ) -->
  <function name="GiNaC::get_symbol_stats">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- ex GiNaC::interpolate( const ex & gamma, const numeric & xi, const ex & x, int degree_hint = 1 ) -->
  <function name="GiNaC::interpolate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" default="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- numeric GiNaC::lcm_of_coefficients_denominators( const ex & e ) -->
  <function name="GiNaC::lcm_of_coefficients_denominators">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="numeric"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- numeric GiNaC::lcmcoeff( const ex & e, const numeric & l ) -->
  <function name="GiNaC::lcmcoeff">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="numeric"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::multiply_lcm( const ex & e, const numeric & lcm ) -->
  <function name="GiNaC::multiply_lcm">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex GiNaC::replace_with_symbol( const ex & e, exmap & repl, exmap & rev_lookup, lst & modifier ) -->
  <function name="GiNaC::replace_with_symbol">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="inout"/>
    <arg nr="4" direction="inout"/>
  </function>
  <!-- ex GiNaC::sqrfree( const ex & a, const lst & l ) -->
  <function name="GiNaC::sqrfree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- epvector GiNaC::sqrfree_yun( const ex & a, const symbol & x ) -->
  <function name="GiNaC::sqrfree_yun">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="epvector"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ex ex::content( const ex & x ) -->
  <function name="ex::content">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex ex::denom( void ) -->
  <function name="ex::denom">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- numeric ex::integer_content( void ) -->
  <function name="ex::integer_content">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="numeric"/>
    <use-retval/>
  </function>
  <!-- numeric ex::max_coefficient( void ) -->
  <function name="ex::max_coefficient">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="numeric"/>
    <use-retval/>
  </function>
  <!-- ex basic::normal( exmap & repl, exmap & rev_lookup, lst & modifier ) -->
  <function name="basic::normal">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- ex ex::numer( void ) -->
  <function name="ex::numer">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex ex::numer_denom( void ) -->
  <function name="ex::numer_denom">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex ex::primpart( const ex & x ) -->
  <function name="ex::primpart">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex basic::smod( const numeric & xi ) -->
  <function name="basic::smod">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex ex::to_polynomial( exmap & repl ) -->
  <function name="ex::to_polynomial">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- ex ex::to_rational( exmap & repl ) -->
  <function name="ex::to_rational">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- ex ex::unit( const ex & x ) -->
  <function name="ex::unit">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void ex::unitcontprim( const ex & x, ex & u, ex & c, ex & p ) -->
  <function name="ex::unitcontprim">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="inout"/>
    <arg nr="4" direction="inout"/>
  </function>
  <!-- void minkmetric::archive( archive_node & n ) -->
  <function name="minkmetric::archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- int minkmetric::compare_same_type( const basic & other ) -->
  <function name="minkmetric::compare_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool tensdelta::contract_with( exvector::iterator self, exvector::iterator other, exvector & v ) -->
  <function name="tensdelta::contract_with">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- ex tensdelta::eval_indexed( const basic & i ) -->
  <function name="tensdelta::eval_indexed">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool tensdelta::info( unsigned inf ) -->
  <function name="tensdelta::info">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void minkmetric::read_archive( const archive_node & n, lst & sym_lst ) -->
  <function name="minkmetric::read_archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- void idx::archive( archive_node & n ) -->
  <function name="idx::archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- unsigned idx::calchash( void ) -->
  <function name="idx::calchash">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <use-retval/>
  </function>
  <!-- int idx::compare_same_type( const basic & other ) -->
  <function name="idx::compare_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex idx::derivative( const symbol & s ) -->
  <function name="idx::derivative">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex idx::evalf( void ) -->
  <function name="idx::evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- bool idx::info( unsigned inf ) -->
  <function name="idx::info">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex idx::map( map_function & f ) -->
  <function name="idx::map">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- bool idx::match_same_type( const basic & other ) -->
  <function name="idx::match_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- size_t idx::nops( void ) -->
  <function name="idx::nops">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="size_t"/>
    <use-retval/>
  </function>
  <!-- ex idx::op( size_t i ) -->
  <function name="idx::op">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void idx::read_archive( const archive_node & n, lst & sym_lst ) -->
  <function name="idx::read_archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- ex idx::subs( const exmap & m, unsigned options ) -->
  <function name="idx::subs">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- basic * GiNaC::dummy_func( void ) -->
  <function name="GiNaC::dummy_func">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="basic *"/>
    <use-retval/>
  </function>
  <!-- bool exprseq::info( unsigned inf ) -->
  <function name="exprseq::info">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void GiNaC::print_operator( const print_context & c, relational::operators o ) -->
  <function name="GiNaC::print_operator">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void relational::archive( archive_node & n ) -->
  <function name="relational::archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- unsigned relational::calchash( void ) -->
  <function name="relational::calchash">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <use-retval/>
  </function>
  <!-- int relational::compare_same_type( const basic & other ) -->
  <function name="relational::compare_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex relational::eval_ncmul( const exvector & v ) -->
  <function name="relational::eval_ncmul">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool relational::info( unsigned inf ) -->
  <function name="relational::info">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- relational::safe_bool relational::make_safe_bool( bool cond ) -->
  <function name="relational::make_safe_bool">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="relational::safe_bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex relational::map( map_function & f ) -->
  <function name="relational::map">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- bool relational::match_same_type( const basic & other ) -->
  <function name="relational::match_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- size_t relational::nops( void ) -->
  <function name="relational::nops">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="size_t"/>
    <use-retval/>
  </function>
  <!-- ex relational::op( size_t i ) -->
  <function name="relational::op">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void relational::read_archive( const archive_node & n, lst & sym_lst ) -->
  <function name="relational::read_archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- unsigned relational::return_type( void ) -->
  <function name="relational::return_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <use-retval/>
  </function>
  <!-- return_type_t relational::return_type_tinfo( void ) -->
  <function name="relational::return_type_tinfo">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="return_type_t"/>
    <use-retval/>
  </function>
  <!-- ex relational::subs( const exmap & m, unsigned options ) -->
  <function name="relational::subs">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void pseries::archive( archive_node & n ) -->
  <function name="pseries::archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- ex pseries::coeff( const ex & s, int n ) -->
  <function name="pseries::coeff">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex pseries::collect( const ex & s, bool distributed ) -->
  <function name="pseries::collect">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int pseries::compare_same_type( const basic & other ) -->
  <function name="pseries::compare_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex pseries::conjugate( void ) -->
  <function name="pseries::conjugate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- int pseries::degree( const ex & s ) -->
  <function name="pseries::degree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex pseries::derivative( const symbol & s ) -->
  <function name="pseries::derivative">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ex pseries::eval( void ) -->
  <function name="pseries::eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex pseries::eval_integ( void ) -->
  <function name="pseries::eval_integ">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex pseries::evalf( void ) -->
  <function name="pseries::evalf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex pseries::evalm( void ) -->
  <function name="pseries::evalm">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex pseries::expand( unsigned options ) -->
  <function name="pseries::expand">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex pseries::imag_part( void ) -->
  <function name="pseries::imag_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- int pseries::ldegree( const ex & s ) -->
  <function name="pseries::ldegree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- size_t pseries::nops( void ) -->
  <function name="pseries::nops">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="size_t"/>
    <use-retval/>
  </function>
  <!-- ex pseries::op( size_t i ) -->
  <function name="pseries::op">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void pseries::read_archive( const archive_node & n, lst & sym_lst ) -->
  <function name="pseries::read_archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- ex pseries::real_part( void ) -->
  <function name="pseries::real_part">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <use-retval/>
  </function>
  <!-- ex basic::series( const relational & r, int order, unsigned options ) -->
  <function name="basic::series">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ex pseries::subs( const exmap & m, unsigned options ) -->
  <function name="pseries::subs">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- const symmetry & GiNaC::index0( void ) -->
  <function name="GiNaC::index0">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const symmetry &amp;"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- const symmetry & GiNaC::index1( void ) -->
  <function name="GiNaC::index1">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const symmetry &amp;"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- const symmetry & GiNaC::index2( void ) -->
  <function name="GiNaC::index2">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const symmetry &amp;"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- const symmetry & GiNaC::index3( void ) -->
  <function name="GiNaC::index3">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const symmetry &amp;"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- ex GiNaC::symm( const ex & e, exvector::const_iterator first, exvector::const_iterator last, bool asymmetric ) -->
  <function name="GiNaC::symm">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ex"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void symmetry::archive( archive_node & n ) -->
  <function name="symmetry::archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- unsigned symmetry::calchash( void ) -->
  <function name="symmetry::calchash">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned"/>
    <use-retval/>
  </function>
  <!-- int symmetry::compare_same_type( const basic & other ) -->
  <function name="symmetry::compare_same_type">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void symmetry::read_archive( const archive_node & n, lst & sym_lst ) -->
  <function name="symmetry::read_archive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
</def>
