<?xml version="1.0"?>
<def format="2">
  <memory>
    <dealloc>free</dealloc>
    <alloc init="true">get_current_dir_name</alloc>
  </memory>
  <memory>
    <alloc init="true" arg="1">asprintf</alloc>
    <dealloc>free</dealloc>
  </memory>
  <memory>
    <alloc init="false" buffer-size="malloc">xmalloc</alloc>
    <alloc init="true" buffer-size="calloc">xcalloc</alloc>
    <alloc init="true" buffer-size="strdup">xstrdup</alloc>
    <realloc init="false" buffer-size="malloc:2">xrealloc</realloc>
    <dealloc>free</dealloc>
    <dealloc>xfree</dealloc>
  </memory>
  <!-- https://linux.die.net/man/3/backtrace -->
  <memory>
    <alloc init="true">backtrace_symbols</alloc>
    <dealloc>free</dealloc>
  </memory>
  <memory>
    <alloc init="false" buffer-size="malloc">pvalloc</alloc>
    <dealloc>free</dealloc>
  </memory>
  <function name="bswap_16">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="uint16_t">((arg1 &amp; 0xff00u) &gt;&gt; 8) | ((arg1 &amp; 0x00ffu) &lt;&lt; 8)</returnValue>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <define name="__bswap_constant_16(x)" value="bswap_16(x)"/>
  <define name="__builtin_bswap16(x)" value="bswap_16(x)"/>
  <define name="__bswap_16(x)" value="bswap_16(x)"/>
  <function name="bswap_32">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="uint32_t">((arg1 &amp; 0xff000000ul) &gt;&gt; 24) | ((arg1 &amp; 0x00ff0000ul) &gt;&gt; 8) | ((arg1 &amp; 0x0000ff00ul) &lt;&lt; 8) | ((arg1 &amp; 0x000000fful) &lt;&lt; 24)</returnValue>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <define name="__bswap_constant_32(x)" value="bswap_32(x)"/>
  <define name="__builtin_bswap32(x)" value="bswap_32(x)"/>
  <define name="__bswap_32(x)" value="bswap_32(x)"/>
  <function name="bswap_64">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="uint64_t">((arg1 &amp; 0xff00000000000000ull) &gt;&gt; 56) | ((arg1 &amp; 0x00ff000000000000ull) &gt;&gt; 40) | ((arg1 &amp; 0x0000ff0000000000ull) &gt;&gt; 24) | ((arg1 &amp; 0x000000ff00000000ull) &gt;&gt; 8) | ((arg1 &amp; 0x00000000ff000000ull) &lt;&lt; 8) | ((arg1 &amp; 0x0000000000ff0000ull) &lt;&lt; 24) | ((arg1 &amp; 0x000000000000ff00ull) &lt;&lt; 40) | ((arg1 &amp; 0x00000000000000ffull) &lt;&lt; 56)</returnValue>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <define name="__bswap_constant_64(x)" value="bswap_64(x)"/>
  <define name="__builtin_bswap64(x)" value="bswap_64(x)"/>
  <define name="__bswap_64(x)" value="bswap_64(x)"/>
  <!-- https://gcc.gnu.org/onlinedocs/gcc/Other-Builtins.html -->
  <define name="__builtin_alloca(size)" value="alloca(size)"/>
  <define name="__builtin_memcpy(dest, source, size)" value="memcpy(dest, source, size)"/>
  <define name="__builtin_signbit(value)" value="signbit(value)"/>
  <define name="__builtin_offsetof(st, m)" value="offsetof(st, m)"/>
  <define name="__builtin_nan(str)" value="nan(str)"/>
  <define name="__builtin_nanf(str)" value="nanf(str)"/>
  <define name="__builtin_nanl(str)" value="nanl(str)"/>
  <define name="__extension__" value=""/>
  <define name="__typeof__(T)" value="typeof(T)"/>
  <define name="__glibc_unlikely(x)" value="(x)"/>
  <define name="__glibc_likely(x)" value="(x)"/>
  <!-- void *__builtin_alloca (size_t size) -->
  <!-- alloca() is often defined as __builtin_alloca() so the define above does not work always -->
  <!-- void *alloca(size_t size); -->
  <function name="__builtin_alloca">
    <use-retval/>
    <returnValue type="void *"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man2/semop.2.html -->
  <!-- int semtimedop(int semid, struct sembuf *sops, size_t nsops, const struct timespec *timeout); -->
  <function name="semtimedop">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="in">
      <!--  If the timeout argument is NULL, then semtimedop() behaves exactly like semop() -->
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void * __builtin_assume_aligned (const void *exp, size_t align, ...) -->
  <!-- This function can have either two or three arguments, if it has three, the third argument should have integer type. -->
  <function name="__builtin_assume_aligned">
    <noreturn>false</noreturn>
    <returnValue type="void *">arg1</returnValue>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in" default="0">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void * __builtin_memcpy(void *dest, const void *source, size_t size);-->
  <!-- memcpy() is sometimes defined as __builtin_memcpy() so the define above does not work -->
  <function name="__builtin_memcpy">
    <noreturn>false</noreturn>
    <returnValue type="void *"/>
    <leak-ignore/>
    <not-overlapping-data ptr1-arg="1" ptr2-arg="2" size-arg="3"/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- void __builtin_prefetch (const void *addr, ...) -->
  <function name="__builtin_prefetch">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in"/>
    <arg nr="variadic" direction="in">
      <not-uninit/>
    </arg>
  </function>
    <!-- https://man7.org/linux/man-pages/man3/getenv.3.html -->
    <!-- char * secure_getenv(const char *name); -->
  <function name="secure_getenv">
    <use-retval/>
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- https://gcc.gnu.org/onlinedocs/gcc-6.2.0/gcc/Alignment.html -->
  <!-- __alignof__ is used like sizeof -->
  <!-- Declaration for this GNU compiler extension could look like this: -->
  <!-- size_t __alignof__(type); -->
  <!-- size_t __alignof__(expression); -->
  <function name="__alignof__">
    <noreturn>false</noreturn>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/getpw.3.html -->
  <!-- int getpw(uid_t uid, char *buf); -->
    <function name="getpw">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
    <warn severity="style" reason="Obsolescent" alternatives="getpwnam"/>
  </function>
  <!-- int __builtin_types_compatible_p (type1, type2) -->
  <function name="__builtin_types_compatible_p">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1"/>
    <arg nr="2"/>
  </function>
  <!-- void __builtin_trap (void) -->
  <function name="__builtin_trap">
    <noreturn>true</noreturn>
  </function>
  <!-- int __builtin_popcount (unsigned int x) -->
  <!-- int __builtin_parity (unsigned int x) -->
  <!-- int __builtin_ffs (int x) -->
  <!-- int __builtin_ffsl (long) -->
  <!-- int __builtin_ffsll (long long) -->
  <!-- int __builtin_clz (unsigned int x) -->
  <function name="__builtin_popcount,__builtin_parity,__builtin_ffs,__builtin_ffsl,__builtin_ffsll,__builtin_clz">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/mempcpy.3.html-->
  <!-- wchar_t *wmempcpy(wchar_t *restrict dest, const wchar_t *restrict src, size_t n);-->
  <function name="wmempcpy">
    <returnValue type="wchar_t *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <not-overlapping-data ptr1-arg="1" ptr2-arg="2" size-arg="3"/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/mempcpy.3.html-->
  <!-- void *mempcpy(void *restrict dest, const void *restrict src, size_t n);-->
  <function name="mempcpy">
    <returnValue type="void *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <not-overlapping-data ptr1-arg="1" ptr2-arg="2" size-arg="3"/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- void xexit(int status); -->
  <function name="xexit">
    <noreturn>true</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void * xmalloc(size_t size); -->
  <function name="xmalloc">
    <use-retval/>
    <returnValue type="void *"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
      <not-bool/>
    </arg>
  </function>
  <!-- char *xstrdup(const char *s); -->
  <function name="xstrdup">
    <returnValue type="char *"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- void xfree(void *block); -->
  <function name="xfree">
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- ssize_t splice(int fd_in, loff_t *off_in, int fd_out, loff_t *off_out, size_t len, unsigned int flags); -->
  <function name="splice">
    <leak-ignore/>
    <returnValue type="ssize_t"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="inout"/>
    <arg nr="5" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>1:</valid>
    </arg>
    <arg nr="6" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ssize_t tee(int fd_in, int fd_out, size_t len, unsigned int flags); -->
  <function name="tee">
    <returnValue type="ssize_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>1:</valid>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int pipe2(int pipefd[2],int flags); -->
  <function name="pipe2">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1" direction="out">
      <not-null/>
      <not-bool/>
      <minsize type="value" value="2" baseType="int"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void * xcalloc(size_t nitems, size_t size); -->
  <function name="xcalloc">
    <use-retval/>
    <returnValue type="void *"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>1:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
      <not-bool/>
    </arg>
  </function>
  <!-- void * xrealloc(void *block, size_t newsize); -->
  <function name="xrealloc">
    <use-retval/>
    <returnValue type="void *"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
      <not-bool/>
    </arg>
  </function>
  <!-- https://linux.die.net/man/3/pvalloc -->
  <!-- void *pvalloc(size_t size); -->
  <function name="pvalloc">
    <use-retval/>
    <returnValue type="void *"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man3/asprintf.3.html -->
  <!-- int asprintf(char **strp, const char *fmt, ...); -->
  <function name="asprintf">
    <returnValue type="int"/>
    <use-retval/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="out">
      <not-null/>
    </arg>
    <formatstr/>
    <arg nr="2" direction="in">
      <formatstr/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- int isascii_l(int c, locale_t locale); -->
  <function name="isascii_l">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;=0 &amp;&amp; arg1&lt;=0x7F</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:255</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man3/asprintf.3.html -->
  <!-- int vasprintf(char **strp, const char *fmt, va_list ap); -->
  <function name="vasprintf">
    <returnValue type="int"/>
    <use-retval/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="out">
      <not-null/>
    </arg>
    <arg nr="2" direction="in">
      <formatstr/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man2/accept.2.html -->
  <!-- int accept4(int sockfd, struct sockaddr *addr, socklen_t *addrlen, int flags); -->
  <function name="accept4">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="out">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="inout">
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <leak-ignore/>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/gethostbyaddr_r.3.html -->
  <!-- int gethostbyaddr_r(const void *addr, socklen_t len, int type, struct hostent *restrict ret, char *restrict buf, size_t buflen, struct hostent **restrict result, int *restrict h_errnop); -->
  <function name="gethostbyaddr_r">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="out">
      <not-null/>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
      <not-null/>
      <minsize type="argvalue" arg="6"/>
    </arg>
    <arg nr="6" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="7" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="8" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/gethostbyname2_r.3.html -->
  <!-- int gethostbyname2_r(const char *name, int af, struct hostent *ret, char *buf, size_t buflen, struct hostent **result, int *h_errnop); -->
  <function name="gethostbyname2_r">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-null/>
      <minsize type="argvalue" arg="5"/>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="6" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="7" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/gethostbyname_r.3.html -->
  <!-- int gethostbyname_r(const char *name, struct hostent *ret, char *buf, size_t buflen, struct hostent **result, int *h_errnop); -->
  <function name="gethostbyname_r">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
      <strz/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-null/>
      <minsize type="argvalue" arg="4"/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="5" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="6" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>  
  <!-- https://www.gnu.org/software/libc/manual/html_node/Getopt-Long-Options.html#Getopt-Long-Options -->
  <!-- int getopt_long(int argc, char * const argv[],
           const char *optstring,
           const struct option *longopts, int *longindex); -->
  <function name="getopt_long,getopt_long_only">
    <leak-ignore/>
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="3" direction="in">
      <not-null/>
      <strz/>
    </arg>
    <arg nr="4" direction="in">
      <not-null/>
    </arg>
    <arg nr="5" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- void *__builtin_memset(void *s, int c, size_t n); -->
  <define name="__builtin_memset" value="memset"/>
  <!-- long long atoq(const char *nptr); -->
  <function name="atoq">
    <returnValue type="long long"/>
    <use-retval/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- char *strndupa(const char *s, size_t n); -->
  <function name="strndupa">
    <returnValue type="char *"/>
    <use-retval/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
      <not-bool/>
    </arg>
  </function>
  <!-- int dup3(int oldfd, int newfd, int flags); -->
  <function name="dup3">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man3/strsep.3.html -->
  <!-- char *strsep(char **stringp, const char *delim); -->
  <function name="strsep">
    <use-retval/>
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-null/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- char *strdupa(const char *s); -->
  <function name="strdupa">
    <use-retval/>
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- http://www.gnu.org/software/libc/manual/html_node/Backtraces.html -->
  <!-- int backtrace (void **buffer, int size) -->
  <function name="backtrace">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- char ** backtrace_symbols (void *const *buffer, int size) -->
  <function name="backtrace_symbols">
    <returnValue type="char **"/>
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void backtrace_symbols_fd (void *const *buffer, int size, int fd) -->
  <function name="backtrace_symbols_fd">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- char *qecvt(long double value, int ndigit, int * decpt, int * sign); -->
  <function name="qecvt">
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
    </arg>
    <arg nr="4" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- char *qfcvt(double value, int ndigit, int * decpt, int * sign); -->
  <function name="qfcvt">
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
    </arg>
    <arg nr="4" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- char *qgcvt(long double value, int ndigit, char *buf); -->
  <function name="qgcvt">
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/getservent_r.3.html -->
  <!-- int getservent_r(struct servent *restrict result_buf, char *restrict buf, size_t buflen, struct servent **restrict result); -->
  <function name="getservent_r">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <arg nr="1" direction="out">
      <not-null/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-null/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- https://linux.die.net/man/3/ecvt_r -->
  <!-- int ecvt_r (double value, int ndigit, int *decpt, int *neg, char *buf, size_t len); -->
  <function name="ecvt_r">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
    </arg>
    <arg nr="4" direction="out">
      <not-null/>
    </arg>
    <arg nr="5" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="6"/>
    </arg>
    <arg nr="6" direction="in">
      <not-uninit/>
      <valid>0:</valid>
      <not-bool/>
    </arg>
    <warn severity="style" reason="Obsolescent" alternatives="sprintf"/>
  </function>
  <!-- int fcvt_r (double value, int ndigit, int *decpt, int *neg, char *buf, size_t len); -->
  <function name="fcvt_r">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
    </arg>
    <arg nr="4" direction="out">
      <not-null/>
    </arg>
    <arg nr="5" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="6"/>
    </arg>
    <arg nr="6" direction="in">
      <not-uninit/>
      <valid>0:</valid>
      <not-bool/>
    </arg>
    <warn severity="style" reason="Obsolescent" alternatives="sprintf"/>
  </function>
  <!-- int qecvt_r (long double value, int ndigit, int *decpt, int *neg, char *buf, size_t len); -->
  <function name="qecvt_r">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
    </arg>
    <arg nr="4" direction="out">
      <not-null/>
    </arg>
    <arg nr="5" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="6"/>
    </arg>
    <arg nr="6" direction="in">
      <not-uninit/>
      <valid>0:</valid>
      <not-bool/>
    </arg>
    <warn severity="style" reason="Obsolescent" alternatives="sprintf"/>
  </function>
  <!-- int qfcvt_r (long double value, int ndigit, int *decpt, int *neg, char *buf, size_t len); -->
  <function name="qfcvt_r">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
    </arg>
    <arg nr="4" direction="out">
      <not-null/>
    </arg>
    <arg nr="5" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="6"/>
    </arg>
    <arg nr="6" direction="in">
      <not-uninit/>
      <valid>0:</valid>
      <not-bool/>
    </arg>
    <warn severity="style" reason="Obsolescent" alternatives="sprintf"/>
  </function>
  <!-- https://www.gnu.org/software/gnulib/manual/html_node/c_002dstrcasestr.html -->
  <!-- char* strcasestr(const char *s1, const char *s2); -->
  <function name="strcasestr">
    <use-retval/>
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <pure/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- http://linux.die.net/man/2/getresuid -->
  <!-- int getresuid(uid_t *ruid, uid_t *euid, uid_t *suid); -->
  <function name="getresuid">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <pure/>
    <arg nr="1" direction="out">
      <not-null/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://linux.die.net/man/2/getresuid -->
  <!-- int getresgid(gid_t *rgid, gid_t *egid, gid_t *sgid); -->
  <function name="getresgid">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <pure/>
    <arg nr="1" direction="out">
      <not-null/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man2/setresuid.2.html -->
  <!-- int setresuid(uid_t ruid, uid_t euid, uid_t suid);-->
  <function name="setresuid">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <leak-ignore/>
  </function>
  <!-- http://man7.org/linux/man-pages/man2/setresuid.2.html -->
  <!-- int setresgid(gid_t rgid, gid_t egid, gid_t sgid);-->
  <function name="setresgid">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <leak-ignore/>
  </function>
  <!-- int inet_aton (const char *name, struct in_addr *addr); -->
  <function name="inet_aton">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
      <not-null/>
      <strz/>
    </arg>
    <arg nr="2">
      <not-null/>
    </arg>
    <leak-ignore/>
  </function>
  <!-- uint32_t inet_network (const char *name); -->
  <function name="inet_network">
    <returnValue type="uint32_t"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
      <not-bool/>
      <strz/>
    </arg>
    <leak-ignore/>
  </function>
  <!-- struct in_addr inet_makeaddr (uint32_t net, uint32_t local); -->
  <function name="inet_makeaddr">
    <returnValue type="struct in_addr"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:4294967295</valid>
      <!-- 0:UINT32_MAX-->
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:4294967295</valid>
      <!-- 0:UINT32_MAX-->
      <not-bool/>
    </arg>
    <leak-ignore/>
  </function>
  <!-- https://linux.die.net/man/3/inet_lnaof -->
  <!-- uint32_t inet_lnaof (struct in_addr addr); -->
  <function name="inet_lnaof">
    <returnValue type="uint32_t"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <leak-ignore/>
  </function>
  <!-- uint32_t inet_netof (struct in_addr addr); -->
  <function name="inet_netof">
    <returnValue type="uint32_t"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <leak-ignore/>
  </function>
  <!-- int inet_pton (int af, const char *cp, void *buf); -->
  <function name="inet_pton">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-null/>
      <strz/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
    <leak-ignore/>
  </function>
  <!-- https://linux.die.net/man/3/inet_ntop -->
  <!-- const char * inet_ntop (int af, const void *cp, char *buf, socklen_t len);-->
  <function name="inet_ntop">
    <returnValue type="const char *"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="4"/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <leak-ignore/>
  </function>
  <!-- char *canonicalize_file_name(const char *path); -->
  <function name="canonicalize_file_name">
    <returnValue type="char *"/>
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- https://linux.die.net/man/3/execvpe -->
  <!-- int execvpe(const char *file, char *const argv[], char *const envp[]); -->
  <function name="execvpe">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://linux.die.net/man/3/mkostemp -->
  <!-- int mkostemp(char *template, int flags); 
       int mkstemps(char *template, int suffixlen);-->
  <function name="mkostemp,mkstemps">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int mkostemps(char *template, int suffixlen, int flags); -->
  <function name="mkostemps">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- see http://man7.org/linux/man-pages/man3/memmem.3.html -->
  <!-- void *memmem(const void *haystack, size_t haystacklen,
                    const void *needle, size_t needlelen);-->
  <function name="memmem">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <use-retval/>
    <returnValue type="void *"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="4"/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <valid>0:</valid>
      <not-bool/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man3/memrchr.3.html -->
  <!-- void *memrchr(const void *s, int c, size_t n); -->
  <function name="memrchr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void *"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man3/rawmemchr.3.html -->
  <!-- void *rawmemchr(const void *s, int c); -->
  <function name="rawmemchr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void *"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/qsort.3.html -->
  <!-- void qsort_r(void *base, size_t n, size_t size, int (*cmp)(const void *, const void *, void *), void *arg); -->
  <function name="qsort_r">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>  
  <!-- http://man7.org/linux/man-pages/man3/ffs.3.html -->
  <!-- int ffsl(long int i); 
       int ffsll(long long int i); -->
  <function name="ffsl,ffsll">
    <use-retval/>
    <!-- It returns the position of the first bit set, or 0 if no bits are set in i. -->
    <returnValue type="int">arg1==0 &amp;0</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man3/strchr.3.html -->
  <!-- char *strchrnul(const char *s, int c); -->
  <function name="strchrnul">
    <use-retval/>
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man2/setrlimit.2.html -->
  <!-- int prlimit(pid_t pid, int resource, const struct rlimit *new_limit,
                   struct rlimit *old_limit); -->
  <function name="prlimit">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- see https://gcc.gnu.org/onlinedocs/gcc/Other-Builtins.html -->
  <define name="__builtin_expect(X,Y)" value="(X)"/>
  <define name="__builtin_expect_with_probability(exp,c,probability)" value="(exp)"/>
  <!-- see http://kernelnewbies.org/FAQ/LikelyUnlikely -->
  <define name="likely(X)" value="(X)"/>
  <define name="unlikely(X)" value="(X)"/>
  <!-- int epoll_create(int size); -->
  <function name="epoll_create">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-bool/>
      <not-uninit/>
      <!-- Since Linux 2.6.8, the size argument is ignored, but must be greater than zero in order to
           ensure backward compatibility when new epoll applications are run on older kernels. -->
      <valid>1:</valid>
    </arg>
  </function>
  <!-- int epoll_create1(int flags); -->
  <function name="epoll_create1">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
  </function>
  <!-- https://linux.die.net/man/2/epoll_ctl -->
  <!-- int epoll_ctl(int epfd, int op, int fd, struct epoll_event *event); -->
  <function name="epoll_ctl">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int epoll_wait(int epfd, struct epoll_event *events, int maxevents, int timeout); -->
  <function name="epoll_wait">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="out">
      <not-bool/>
      <not-null/>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>1:</valid>
    </arg>
    <arg nr="4" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
  </function>
  <!-- int epoll_pwait(int epfd, struct epoll_event *events, int maxevents, int timeout, const sigset_t *sigmask); -->
  <function name="epoll_pwait">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="out">
      <not-bool/>
      <not-null/>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>1:</valid>
    </arg>
    <arg nr="4" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
    <arg nr="5" direction="in">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- int sethostname(const char *name, size_t len); -->
  <function name="sethostname">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="2"/>
      <strz/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- https://www.gnu.org/software/libc/manual/html_node/Translation-with-gettext.html -->
  <!-- char * gettext (const char *msgid); -->
  <function name="gettext">
    <noreturn>false</noreturn>
    <returnValue type="char *"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
      <not-bool/>
    </arg>
  </function>
  <!-- _(string) is often defined as gettext(string) or string somewhere in projects. -->
  <!-- See https://www.gnu.org/software/gettext//manual/html_node/Mark-Keywords.html -->
  <!-- <define name="_(string)" value="string"/> does not work always, so use a very conservative
       function configuration instead if nothing else is seen by Cppcheck -->
  <function name="_">
    <noreturn>false</noreturn>
    <arg nr="1"/>
  </function>
  <!-- For N_ often something like "#define N_(String) gettext_noop (String)" is used. To be safe
       a conservative function configuration is used here like for "_" -->
  <function name="N_">
    <noreturn>false</noreturn>
    <arg nr="1"/>
  </function>
  <!-- https://linux.die.net/man/3/dgettext -->
  <!-- char * dgettext (const char *domainname, const char *msgid); -->
  <function name="dgettext">
    <noreturn>false</noreturn>
    <returnValue type="char *"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
      <not-bool/>
    </arg>
  </function>
  <!-- int euidaccess(const char *pathname, int mode); -->
  <!-- int eaccess(const char *pathname, int mode); -->
  <!-- Ref: https://man7.org/linux/man-pages/man3/euidaccess.3.html
         Conforming to: These functions are nonstandard. Some other systems have an eaccess() function. -->
  <function name="euidaccess,eaccess">
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- char * dcgettext (const char *domainname, const char *msgid, int category); -->
  <function name="dcgettext">
    <noreturn>false</noreturn>
    <returnValue type="char *"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int __builtin_constant_p (exp) -->
  <function name="__builtin_constant_p">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1"/>
  </function>
  <!-- https://gcc.gnu.org/onlinedocs/gcc/Return-Address.html -->
  <!-- void * __builtin_return_address (unsigned int level) -->
  <!-- void * __builtin_frame_address (unsigned int level) -->
  <function name="__builtin_return_address,__builtin_frame_address">
    <noreturn>false</noreturn>
    <returnValue type="void *"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void * __builtin_extract_return_addr (void *addr) -->
  <!-- void * __builtin_frob_return_address (void *addr) -->
  <function name="__builtin_extract_return_addr,__builtin_frob_return_address">
    <noreturn>false</noreturn>
    <returnValue type="void *"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-bool/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man2/mmap.2.html -->
  <!-- void *mmap(void *addr, size_t length, int prot, int flags, int fd, off_t offset); -->
  <!-- In contrast to POSIX, the GNU mmap allows/requires (depends on the implementation) fd to be -1 if MAP_ANONYMOUS or MAP_ANON is specified -->
  <function name="mmap">
    <use-retval/>
    <returnValue type="void *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>1:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
      <valid>-1:</valid>
    </arg>
    <arg nr="6" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- Note: fstatat64() is a large-file version of the fstatat() function -->
  <!-- int fstatat64(int dirfd, const char *pathname, struct stat64  *statbuf, int flags); -->
  <function name="fstatat64">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
  </function>
  <!-- int __fxstatat64 (int __ver, int __fildes, const char *__filename, struct stat64 *__stat_buf, int __flag); -->
  <function name="__fxstatat64">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="4" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
    <arg nr="5" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/error.3.html -->
  <!-- void error(int status, int errnum, const char *format, ...); -->
  <!-- void error_at_line(int status, int errnum, const char *filename, unsigned int linenum, const char *format, ...); -->
  <function name="error,error_at_line">
    <noreturn>true</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="any" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ########## Resource allocation ########## -->
  <resource>
    <alloc init="true">mkostemp</alloc>
    <alloc init="true">mkstemps</alloc>
    <alloc init="true">mkostemps</alloc>
    <dealloc>close</dealloc>
  </resource>
  <resource>
    <dealloc>close</dealloc>
    <alloc init="true">epoll_create</alloc>
  </resource>
  <resource>
    <dealloc>close</dealloc>
    <alloc init="true">epoll_create1</alloc>
  </resource>
  <podtype name="epoll_event"/>
  <!-- see http://man7.org/linux/man-pages/man7/signal.7.html -->
  <define name="SIGIOT" value="6"/>
  <define name="SIGSTKFLT" value="16"/>
  <define name="SIGWINCH" value="28"/>
  <define name="SIGIO" value="29"/>
  <define name="SIGPWR" value="30"/>
  <define name="SIGUNUSED" value="31"/>
  <!-- see https://man7.org/linux/man-pages/man3/stdin.3.html -->
  <define name="STDIN_FILENO" value="0"/>
  <define name="STDOUT_FILENO" value="1"/>
  <define name="STDERR_FILENO" value="2"/>
  <entrypoint name="_init"/>
  <entrypoint name="_fini"/>
</def>
