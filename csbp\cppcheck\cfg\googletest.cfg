<?xml version="1.0"?>
<def format="2">
  <!-- see https://github.com/google/googletest/blob/main/docs/primer.md -->
  <define name="ASSERT_TRUE(cond)" value="{if (!(cond)) throw false;}"/>
  <define name="EXPECT_TRUE(cond)" value="(void)(cond)"/>
  <define name="ASSERT_FALSE(cond)" value="{if (cond) throw false;}"/>
  <define name="EXPECT_FALSE(cond)" value="(void)(cond)"/>
  <define name="ASSERT_EQ(val1,val2)" value="{if (!((val1) == (val2))) throw false;}"/>
  <define name="EXPECT_EQ(val1,val2)" value="(void)((val1)==(val2))"/>
  <define name="ASSERT_NE(val1,val2)" value="{if (!((val1) != (val2))) throw false;}"/>
  <define name="EXPECT_NE(val1,val2)" value="(void)((val1)!=(val2))"/>
  <define name="ASSERT_LT(val1,val2)" value="{if (!((val1) &lt; (val2))) throw false;}"/>
  <define name="EXPECT_LT(val1,val2)" value="(void)((val1)&lt;(val2))"/>
  <define name="ASSERT_LE(val1,val2)" value="{if (!((val1) &lt;= (val2))) throw false;}"/>
  <define name="EXPECT_LE(val1,val2)" value="(void)((val1)&lt;=(val2))"/>
  <define name="ASSERT_GT(val1,val2)" value="{if (!((val1) &gt; (val2))) throw false;}"/>
  <define name="EXPECT_GT(val1,val2)" value="(void)((val1)&gt;(val2))"/>
  <define name="ASSERT_GE(val1,val2)" value="{if (!((val1) &gt;= (val2))) throw false;}"/>
  <define name="EXPECT_GE(val1,val2)" value="(void)((val1)&gt;=(val2))"/>
  <define name="ASSERT_STREQ(str1,str2)" value="{if (!(strcmp(str1, str2) == 0)) throw false;}"/>
  <define name="EXPECT_STREQ(str1,str2)" value="(void)(*(str1)==*(str2))"/>
  <define name="ASSERT_STRNE(str1,str2)" value="{if (!(strcmp(str1, str2) != 0)) throw false;}"/>
  <define name="EXPECT_STRNE(str1,str2)" value="(void)(*(str1)!=*(str2))"/>
  <define name="ASSERT_STRCASEEQ(str1,str2)" value="{if (!(stricmp(str1, str2) == 0)) throw false;}"/>
  <define name="EXPECT_STRCASEEQ(str1,str2)" value="(void)(*(str1)==*(str2))"/>
  <define name="ASSERT_STRCASENE(str1,str2)" value="{if (!(stricmp(str1, str2) != 0)) throw false;}"/>
  <define name="EXPECT_STRCASENE(str1,str2)" value="(void)(*(str1)!=*(str2))"/>
  <define name="ASSERT_THROW(code, e)" value="try{code;}catch(e){}"/>
  <define name="EXPECT_THROW(code, e)" value="try{code;}catch(e){}"/>
  <define name="ASSERT_NO_THROW(code)" value="code"/>
  <define name="EXPECT_NO_THROW(code)" value="code"/>
  <define name="TEST(A,B)" value="void __ ## A ## _ ## B ( )"/>
  <define name="TEST_F(A,B)" value="void __ ## A ## _ ## B ( )"/>
  <define name="TEST_P(A,B)" value="void __ ## A ## _ ## B ( )"/>
  <define name="GTEST_API_" value=""/>
  <define name="GTEST_FLAG(name)" value="FLAGS_gtest_##name"/>
  <define name="GTEST_DECLARE_bool_(name)" value="GTEST_API_ extern bool GTEST_FLAG(name)"/>
  <define name="GTEST_DECLARE_int32_(name)" value="GTEST_API_ extern ::testing::internal::Int32 GTEST_FLAG(name)"/>
  <define name="GTEST_DECLARE_string_(name)" value="GTEST_API_ extern ::std::string GTEST_FLAG(name)"/>
  <define name="GTEST_DEFINE_bool_(name, default_val, doc)" value="GTEST_API_ bool GTEST_FLAG(name) = (default_val)"/>
  <define name="GTEST_DEFINE_int32_(name, default_val, doc)" value="GTEST_API_ ::testing::internal::Int32 GTEST_FLAG(name) = (default_val)"/>
  <define name="GTEST_DEFINE_string_(name, default_val, doc)" value="GTEST_API_ ::std::string GTEST_FLAG(name) = (default_val)"/>
  <define name="GTEST_CONCAT_TOKEN_(foo, bar)" value="GTEST_CONCAT_TOKEN_IMPL_(foo, bar)"/>
  <define name="GTEST_CONCAT_TOKEN_IMPL_(foo, bar)" value="foo ## bar"/>
  <define name="GTEST_STRINGIFY_(name)" value="#name"/>
  <define name="GTEST_REFERENCE_TO_CONST_(T)" value="typename ::testing::internal::ConstRef&lt;T&gt;::type"/>
  <define name="GTEST_ATTRIBUTE_UNUSED_" value="__attribute__ ((unused))"/>
  <define name="MATCHER(name, description,...)" value="bool name##Matcher&lt;_type&gt;::gmock_Impl&lt;arg_type&gt;::MatchAndExplain( GTEST_REFERENCE_TO_CONST_(arg_type) arg, ::testing::MatchResultListener* result_listener GTEST_ATTRIBUTE_UNUSED_) const"/>
  <define name="MATCHER_P(name, p0, description)" value="bool name##MatcherP&lt;p0##_type&gt;::gmock_Impl&lt;arg_type&gt;::MatchAndExplain( GTEST_REFERENCE_TO_CONST_(arg_type) arg, ::testing::MatchResultListener* result_listener GTEST_ATTRIBUTE_UNUSED_) const"/>
  <define name="MATCHER_P2(name, p0, p1, description)" value="bool name##MatcherP2&lt;p0##_type, p1##_type&gt;::gmock_Impl&lt;arg_type&gt;::MatchAndExplain(GTEST_REFERENCE_TO_CONST_(arg_type) arg,::testing::MatchResultListener* result_listener GTEST_ATTRIBUTE_UNUSED_) const"/>
  <define name="MATCHER_P3(name, p0, p1, p2, description)" value="bool name##MatcherP3&lt;p0##_type, p1##_type, p2##_type&gt;::gmock_Impl&lt;arg_type&gt;::MatchAndExplain(GTEST_REFERENCE_TO_CONST_(arg_type) arg,::testing::MatchResultListener* result_listener GTEST_ATTRIBUTE_UNUSED_) const"/>
  <define name="MATCHER_P4(name, p0, p1, p2, p3, description)" value="bool name##MatcherP4&lt;p0##_type, p1##_type, p2##_type, p3##_type&gt;::gmock_Impl&lt;arg_type&gt;::MatchAndExplain(GTEST_REFERENCE_TO_CONST_(arg_type) arg,::testing::MatchResultListener* result_listener GTEST_ATTRIBUTE_UNUSED_) const"/>
  <define name="MATCHER_P5(name, p0, p1, p2, p3, p4, description)" value="bool name##MatcherP5&lt;p0##_type, p1##_type, p2##_type, p3##_type, p4##_type&gt;::gmock_Impl&lt;arg_type&gt;::MatchAndExplain(GTEST_REFERENCE_TO_CONST_(arg_type) arg,::testing::MatchResultListener* result_listener GTEST_ATTRIBUTE_UNUSED_) const"/>
  <define name="MATCHER_P6(name, p0, p1, p2, p3, p4, p5, description)" value="bool name##MatcherP6&lt;p0##_type, p1##_type, p2##_type, p3##_type, p4##_type, p5##_type&gt;::gmock_Impl&lt;arg_type&gt;::MatchAndExplain(GTEST_REFERENCE_TO_CONST_(arg_type) arg,::testing::MatchResultListener* result_listener GTEST_ATTRIBUTE_UNUSED_) const"/>
  <define name="MATCHER_P7(name, p0, p1, p2, p3, p4, p5, p6, description)" value="bool name##MatcherP7&lt;p0##_type, p1##_type, p2##_type, p3##_type, p4##_type, p5##_type, p6##_type&gt;::gmock_Impl&lt;arg_type&gt;::MatchAndExplain(GTEST_REFERENCE_TO_CONST_(arg_type) arg,::testing::MatchResultListener* result_listener GTEST_ATTRIBUTE_UNUSED_) const"/>
  <define name="MATCHER_P8(name, p0, p1, p2, p3, p4, p5, p6, p7, description)" value="bool name##MatcherP8&lt;p0##_type, p1##_type, p2##_type, p3##_type, p4##_type, p5##_type, p6##_type, p7##_type&gt;::gmock_Impl&lt;arg_type&gt;::MatchAndExplain(GTEST_REFERENCE_TO_CONST_(arg_type) arg,::testing::MatchResultListener* result_listener GTEST_ATTRIBUTE_UNUSED_) const"/>
  <define name="MATCHER_P9(name, p0, p1, p2, p3, p4, p5, p6, p7, p8, description)" value="bool name##MatcherP9&lt;p0##_type, p1##_type, p2##_type, p3##_type, p4##_type, p5##_type, p6##_type, p7##_type, p8##_type&gt;::gmock_Impl&lt;arg_type&gt;::MatchAndExplain(GTEST_REFERENCE_TO_CONST_(arg_type) arg,::testing::MatchResultListener* result_listener GTEST_ATTRIBUTE_UNUSED_) const"/>
  <define name="MATCHER_P10(name, p0, p1, p2, p3, p4, p5, p6, p7, p8, p9, description)" value="bool name##MatcherP10&lt;p0##_type, p1##_type, p2##_type, p3##_type, p4##_type, p5##_type, p6##_type, p7##_type, p8##_type, p9##_type&gt;::gmock_Impl&lt;arg_type&gt;::MatchAndExplain(GTEST_REFERENCE_TO_CONST_(arg_type) arg,::testing::MatchResultListener* result_listener GTEST_ATTRIBUTE_UNUSED_) const"/>
  <define name="MOCK_METHOD(...)" value=""/>
</def>
