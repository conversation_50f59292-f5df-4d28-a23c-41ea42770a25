<?xml version="1.0"?>
<def format="2">
  <!-- This library contains configuration of ICU-project (http://userguide.icu-project.org/) -->
  <!-- https://unicode-org.github.io/icu-docs/apidoc/dev/icu4c/utypes_8h.html -->
  <define name="U_SUCCESS(x)" value="((x)&lt;=0)"/>
  <define name="U_FAILURE(x)" value="((x)&gt;0)"/>
  <define name="U_STANDARD_CPP_NAMESPACE" value=""/>
  <define name="U_IO_API" value=""/>
  <define name="U_EXPORT" value=""/>
  <define name="U_EXPORT2" value=""/>
  <define name="U_IMPORT" value=""/>
  <define name="U_HIDDEN" value=""/>
  <define name="U_LAYOUT_API" value=""/>
  <define name="U_I18N_API" value=""/>
  <define name="U_LAYOUTEX_API" value=""/>
  <define name="U_DATA_API" value=""/>
  <define name="U_SHOW_CPLUSPLUS_API" value="0"/>
  <define name="U_ICUDATA_TYPE_LITLETTER" value="l"/>
  <define name="U_ICUDATA_NAME" value="&quot;icudt&quot;U_ICU_VERSION_SHORTU_ICUDATA_TYPE_LETTER"/>
  <define name="U_TOOLUTIL_API" value=""/>
  <define name="U_DATE_MIN" value="-U_DATE_MAX"/>
  <define name="U_MILLIS_PER_SECOND" value="(1000)"/>
  <define name="U_ICUDATA_ENTRY_POINT" value="U_DEF2_ICUDATA_ENTRY_POINT(U_ICU_VERSION_MAJOR_NUM,U_LIB_SUFFIX_C_NAME)"/>
  <define name="U_MILLIS_PER_MINUTE" value="(60000)"/>
  <define name="U_MILLIS_PER_HOUR" value="(3600000)"/>
  <define name="U_COMMON_API" value=""/>
  <define name="U_MILLIS_PER_DAY" value="(86400000)"/>
  <define name="U_ICUDATA_TYPE_LETTER" value="&quot;l&quot;"/>
  <define name="U_DATE_MAX" value="DBL_MAX"/>
  <define name="U_COMBINED_IMPLEMENTATION" value="1"/>
  <define name="U_HIDE_INTERNAL_API" value="1"/>
  <define name="U_HIDE_DRAFT_API" value="1"/>
  <!-- enum UErrorCode -->
  <define name="U_USING_FALLBACK_WARNING" value="-128"/>
  <define name="U_ERROR_WARNING_START" value="-128"/>
  <define name="U_USING_DEFAULT_WARNING" value="-127"/>
  <define name="U_SAFECLONE_ALLOCATED_WARNING" value="-126"/>
  <define name="U_STATE_OLD_WARNING" value="-125"/>
  <define name="U_STRING_NOT_TERMINATED_WARNING" value="-124"/>
  <define name="U_SORT_KEY_TOO_SHORT_WARNING" value="-123"/>
  <define name="U_AMBIGUOUS_ALIAS_WARNING" value="-122"/>
  <define name="U_DIFFERENT_UCA_VERSION" value="-121"/>
  <define name="U_PLUGIN_CHANGED_LEVEL_WARNING" value="-120"/>
  <define name="U_ERROR_WARNING_LIMIT" value="-119"/>
  <define name="U_ZERO_ERROR" value="0"/>
  <define name="U_ILLEGAL_ARGUMENT_ERROR" value="1"/>
  <define name="U_MISSING_RESOURCE_ERROR" value="2"/>
  <define name="U_INVALID_FORMAT_ERROR" value="3"/>
  <define name="U_FILE_ACCESS_ERROR" value="4"/>
  <define name="U_INTERNAL_PROGRAM_ERROR" value="5"/>
  <define name="U_MESSAGE_PARSE_ERROR" value="6"/>
  <define name="U_MEMORY_ALLOCATION_ERROR" value="7"/>
  <define name="U_INDEX_OUTOFBOUNDS_ERROR" value="8"/>
  <define name="U_PARSE_ERROR" value="9"/>
  <define name="U_INVALID_CHAR_FOUND" value="10"/>
  <define name="U_TRUNCATED_CHAR_FOUND" value="11"/>
  <define name="U_ILLEGAL_CHAR_FOUND" value="12"/>
  <define name="U_INVALID_TABLE_FORMAT" value="13"/>
  <define name="U_INVALID_TABLE_FILE" value="14"/>
  <define name="U_BUFFER_OVERFLOW_ERROR" value="15"/>
  <define name="U_UNSUPPORTED_ERROR" value="16"/>
  <define name="U_RESOURCE_TYPE_MISMATCH" value="17"/>
  <define name="U_ILLEGAL_ESCAPE_SEQUENCE" value="18"/>
  <define name="U_UNSUPPORTED_ESCAPE_SEQUENCE" value="19"/>
  <define name="U_NO_SPACE_AVAILABLE" value="20"/>
  <define name="U_CE_NOT_FOUND_ERROR" value="21"/>
  <define name="U_PRIMARY_TOO_LONG_ERROR" value="22"/>
  <define name="U_STATE_TOO_OLD_ERROR" value="23"/>
  <define name="U_TOO_MANY_ALIASES_ERROR" value="24"/>
  <define name="U_ENUM_OUT_OF_SYNC_ERROR" value="25"/>
  <define name="U_INVARIANT_CONVERSION_ERROR" value="26"/>
  <define name="U_INVALID_STATE_ERROR" value="27"/>
  <define name="U_COLLATOR_VERSION_MISMATCH" value="28"/>
  <define name="U_USELESS_COLLATOR_ERROR" value="29"/>
  <define name="U_NO_WRITE_PERMISSION" value="30"/>
  <define name="U_INPUT_TOO_LONG_ERROR" value="31"/>
  <define name="U_STANDARD_ERROR_LIMIT" value="32"/>
  <define name="U_BAD_VARIABLE_DEFINITION" value="0x10000"/>
  <define name="U_PARSE_ERROR_START" value="0x10000"/>
  <define name="U_MALFORMED_RULE" value="65537"/>
  <define name="U_MALFORMED_SET" value="65538"/>
  <define name="U_MALFORMED_SYMBOL_REFERENCE" value="65539"/>
  <define name="U_MALFORMED_UNICODE_ESCAPE" value="65540"/>
  <define name="U_MALFORMED_VARIABLE_DEFINITION" value="65541"/>
  <define name="U_MALFORMED_VARIABLE_REFERENCE" value="65542"/>
  <define name="U_MISMATCHED_SEGMENT_DELIMITERS" value="65543"/>
  <define name="U_MISPLACED_ANCHOR_START" value="65544"/>
  <define name="U_MISPLACED_CURSOR_OFFSET" value="65545"/>
  <define name="U_MISPLACED_QUANTIFIER" value="65546"/>
  <define name="U_MISSING_OPERATOR" value="65547"/>
  <define name="U_MISSING_SEGMENT_CLOSE" value="65548"/>
  <define name="U_MULTIPLE_ANTE_CONTEXTS" value="65549"/>
  <define name="U_MULTIPLE_CURSORS" value="65550"/>
  <define name="U_MULTIPLE_POST_CONTEXTS" value="65551"/>
  <define name="U_TRAILING_BACKSLASH" value="65552"/>
  <define name="U_UNDEFINED_SEGMENT_REFERENCE" value="65553"/>
  <define name="U_UNDEFINED_VARIABLE" value="65554"/>
  <define name="U_UNQUOTED_SPECIAL" value="65555"/>
  <define name="U_UNTERMINATED_QUOTE" value="65556"/>
  <define name="U_RULE_MASK_ERROR" value="65557"/>
  <define name="U_MISPLACED_COMPOUND_FILTER" value="65558"/>
  <define name="U_MULTIPLE_COMPOUND_FILTERS" value="65559"/>
  <define name="U_INVALID_RBT_SYNTAX" value="65560"/>
  <define name="U_INVALID_PROPERTY_PATTERN" value="65561"/>
  <define name="U_MALFORMED_PRAGMA" value="65562"/>
  <define name="U_UNCLOSED_SEGMENT" value="65563"/>
  <define name="U_ILLEGAL_CHAR_IN_SEGMENT" value="65564"/>
  <define name="U_VARIABLE_RANGE_EXHAUSTED" value="65565"/>
  <define name="U_VARIABLE_RANGE_OVERLAP" value="65566"/>
  <define name="U_ILLEGAL_CHARACTER" value="65567"/>
  <define name="U_INTERNAL_TRANSLITERATOR_ERROR" value="65568"/>
  <define name="U_INVALID_ID" value="65569"/>
  <define name="U_INVALID_FUNCTION" value="65570"/>
  <define name="U_PARSE_ERROR_LIMIT" value="65571"/>
  <define name="U_UNEXPECTED_TOKEN" value="0x10100"/>
  <define name="U_FMT_PARSE_ERROR_START" value="0x10100"/>
  <define name="U_MULTIPLE_DECIMAL_SEPARATORS" value="65793"/>
  <define name="U_MULTIPLE_DECIMAL_SEPERATORS" value="65793"/>
  <define name="U_MULTIPLE_EXPONENTIAL_SYMBOLS" value="65794"/>
  <define name="U_MALFORMED_EXPONENTIAL_PATTERN" value="65795"/>
  <define name="U_MULTIPLE_PERCENT_SYMBOLS" value="65796"/>
  <define name="U_MULTIPLE_PERMILL_SYMBOLS" value="65797"/>
  <define name="U_MULTIPLE_PAD_SPECIFIERS" value="65798"/>
  <define name="U_PATTERN_SYNTAX_ERROR" value="65799"/>
  <define name="U_ILLEGAL_PAD_POSITION" value="65800"/>
  <define name="U_UNMATCHED_BRACES" value="65801"/>
  <define name="U_UNSUPPORTED_PROPERTY" value="65802"/>
  <define name="U_UNSUPPORTED_ATTRIBUTE" value="65803"/>
  <define name="U_ARGUMENT_TYPE_MISMATCH" value="65804"/>
  <define name="U_DUPLICATE_KEYWORD" value="65805"/>
  <define name="U_UNDEFINED_KEYWORD" value="65806"/>
  <define name="U_DEFAULT_KEYWORD_MISSING" value="65807"/>
  <define name="U_DECIMAL_NUMBER_SYNTAX_ERROR" value="65808"/>
  <define name="U_FORMAT_INEXACT_ERROR" value="65809"/>
  <define name="U_NUMBER_ARG_OUTOFBOUNDS_ERROR" value="65810"/>
  <define name="U_NUMBER_SKELETON_SYNTAX_ERROR" value="65811"/>
  <define name="U_MF_UNRESOLVED_VARIABLE_ERROR" value="65812"/>
  <define name="U_MF_SYNTAX_ERROR" value="65813"/>
  <define name="U_MF_UNKNOWN_FUNCTION_ERROR" value="65814"/>
  <define name="U_MF_VARIANT_KEY_MISMATCH_ERROR" value="65815"/>
  <define name="U_MF_FORMATTING_ERROR" value="65816"/>
  <define name="U_MF_NONEXHAUSTIVE_PATTERN_ERROR" value="65817"/>
  <define name="U_MF_DUPLICATE_OPTION_NAME_ERROR" value="65818"/>
  <define name="U_MF_SELECTOR_ERROR" value="65819"/>
  <define name="U_MF_MISSING_SELECTOR_ANNOTATION_ERROR" value="65820"/>
  <define name="U_MF_DUPLICATE_DECLARATION_ERROR" value="65821"/>
  <define name="U_MF_OPERAND_MISMATCH_ERROR" value="65822"/>
  <define name="U_MF_UNSUPPORTED_STATEMENT_ERROR" value="65823"/>
  <define name="U_MF_UNSUPPORTED_EXPRESSION_ERROR" value="65824"/>
  <define name="U_FMT_PARSE_ERROR_LIMIT" value="0x10121"/>
  <define name="U_BRK_INTERNAL_ERROR" value="0x10200"/>
  <define name="U_BRK_ERROR_START" value="0x10200"/>
  <define name="U_BRK_HEX_DIGITS_EXPECTED" value="66049"/>
  <define name="U_BRK_SEMICOLON_EXPECTED" value="66050"/>
  <define name="U_BRK_RULE_SYNTAX" value="66051"/>
  <define name="U_BRK_UNCLOSED_SET" value="66052"/>
  <define name="U_BRK_ASSIGN_ERROR" value="66053"/>
  <define name="U_BRK_VARIABLE_REDFINITION" value="66054"/>
  <define name="U_BRK_MISMATCHED_PAREN" value="66055"/>
  <define name="U_BRK_NEW_LINE_IN_QUOTED_STRING" value="66056"/>
  <define name="U_BRK_UNDEFINED_VARIABLE" value="66057"/>
  <define name="U_BRK_INIT_ERROR" value="66058"/>
  <define name="U_BRK_RULE_EMPTY_SET" value="66059"/>
  <define name="U_BRK_UNRECOGNIZED_OPTION" value="66060"/>
  <define name="U_BRK_MALFORMED_RULE_TAG" value="66061"/>
  <define name="U_BRK_ERROR_LIMIT" value="66062"/>
  <define name="U_REGEX_INTERNAL_ERROR" value="0x10300"/>
  <define name="U_REGEX_ERROR_START" value="0x10300"/>
  <define name="U_REGEX_RULE_SYNTAX" value="66305"/>
  <define name="U_REGEX_INVALID_STATE" value="66306"/>
  <define name="U_REGEX_BAD_ESCAPE_SEQUENCE" value="66307"/>
  <define name="U_REGEX_PROPERTY_SYNTAX" value="66308"/>
  <define name="U_REGEX_UNIMPLEMENTED" value="66309"/>
  <define name="U_REGEX_MISMATCHED_PAREN" value="66310"/>
  <define name="U_REGEX_NUMBER_TOO_BIG" value="66311"/>
  <define name="U_REGEX_BAD_INTERVAL" value="66312"/>
  <define name="U_REGEX_MAX_LT_MIN" value="66313"/>
  <define name="U_REGEX_INVALID_BACK_REF" value="66314"/>
  <define name="U_REGEX_INVALID_FLAG" value="66315"/>
  <define name="U_REGEX_LOOK_BEHIND_LIMIT" value="66316"/>
  <define name="U_REGEX_SET_CONTAINS_STRING" value="66317"/>
  <define name="U_REGEX_OCTAL_TOO_BIG" value="66318"/>
  <define name="U_REGEX_MISSING_CLOSE_BRACKET" value="U_REGEX_SET_CONTAINS_STRING+2"/>
  <define name="U_REGEX_INVALID_RANGE" value="66319"/>
  <define name="U_REGEX_STACK_OVERFLOW" value="66320"/>
  <define name="U_REGEX_TIME_OUT" value="66321"/>
  <define name="U_REGEX_STOPPED_BY_CALLER" value="66322"/>
  <define name="U_REGEX_PATTERN_TOO_BIG" value="66323"/>
  <define name="U_REGEX_INVALID_CAPTURE_GROUP_NAME" value="66324"/>
  <define name="U_REGEX_ERROR_LIMIT" value="U_REGEX_STOPPED_BY_CALLER+3"/>
  <define name="U_IDNA_PROHIBITED_ERROR" value="0x10400"/>
  <define name="U_IDNA_ERROR_START" value="0x10400"/>
  <define name="U_IDNA_UNASSIGNED_ERROR" value="66561"/>
  <define name="U_IDNA_CHECK_BIDI_ERROR" value="66562"/>
  <define name="U_IDNA_STD3_ASCII_RULES_ERROR" value="66563"/>
  <define name="U_IDNA_ACE_PREFIX_ERROR" value="66564"/>
  <define name="U_IDNA_VERIFICATION_ERROR" value="66565"/>
  <define name="U_IDNA_LABEL_TOO_LONG_ERROR" value="66566"/>
  <define name="U_IDNA_ZERO_LENGTH_LABEL_ERROR" value="66567"/>
  <define name="U_IDNA_DOMAIN_NAME_TOO_LONG_ERROR" value="66568"/>
  <define name="U_IDNA_ERROR_LIMIT" value="66569"/>
  <define name="U_STRINGPREP_PROHIBITED_ERROR" value="0x10400"/>
  <define name="U_STRINGPREP_UNASSIGNED_ERROR" value="66561"/>
  <define name="U_STRINGPREP_CHECK_BIDI_ERROR" value="66562"/>
  <define name="U_PLUGIN_ERROR_START" value="0x10500"/>
  <define name="U_PLUGIN_TOO_HIGH" value="0x10500"/>
  <define name="U_PLUGIN_DIDNT_SET_LEVEL" value="66817"/>
  <define name="U_PLUGIN_ERROR_LIMIT" value="66818"/>
  <define name="U_ERROR_LIMIT" value="66818"/>
  <!-- utf8.h -->
  <!-- https://unicode-org.github.io/icu-docs/apidoc/dev/icu4c/utf_8h.html#a7d794df2276384e748f2836b25b58f7d -->
  <define name="U_IS_SURROGATE_TRAIL(c)" value="(((c)&amp;0x400)!=0)"/>
  <define name="U_IS_SURROGATE(c)" value="(((c)&amp;0xfffff800)==0xd800)"/>
  <define name="U_IS_TRAIL(c)" value="(((c)&amp;0xfffffc00)==0xdc00)"/>
  <define name="U_IS_SURROGATE_LEAD(c)" value="(((c)&amp;0x400)==0)"/>
  <define name="U_IS_LEAD(c)" value="(((c)&amp;0xfffffc00)==0xd800)"/>
  <define name="U_IS_SUPPLEMENTARY(c)" value="((uint32_t)((c)-0x10000)&lt;=0xfffff)"/>
  <define name="U_IS_BMP(c)" value="((uint32_t)(c)&lt;=0xffff)"/>
  <define name="U_IS_UNICODE_CHAR(c)" value="((uint32_t)(c)&lt;0xd800||(0xdfff&lt;(c)&amp;&amp;(c)&lt;=0x10ffff&amp;&amp;!U_IS_UNICODE_NONCHAR(c))"/>
  <define name="U_IS_UNICODE_NONCHAR(c)" value="((c)&gt;=0xfdd0&amp;&amp;((c)&lt;=0xfdef||((c)&amp;0xfffe)==0xfffe)&amp;&amp;(c)&lt;=0x10ffff)"/> 
</def>
