<?xml version="1.0"?>
<def format="2">
  <!-- KDE Library Configuration https://kde.org -->
  <!-- The KDE library is typically not included by one specific header file, there are many -->
  <!-- different ones, like for Qt. Examples: "#include <KApplication>" or "#include <KMessageBox>" -->
  <!-- ########## KDE Types ########## -->
  <!-- ########## KDE Macros / Defines ########## -->
  <define name="K_GLOBAL_STATIC_STRUCT_NAME(NAME)" value="_k_##NAME##__LINE__"/>
  <define name="K_GLOBAL_STATIC(TYPE, NAME)" value="K_GLOBAL_STATIC_WITH_ARGS(TYPE, NAME, ())"/>
  <define name="K_GLOBAL_STATIC_WITH_ARGS(TYPE, NAME, ARGS)" value="static struct K_GLOBAL_STATIC_STRUCT_NAME(NAME) NAME;"/>
  <define name="KDE_ISLIKELY(x)" value="(x)"/>
  <define name="KDE_ISUNLIKELY(x)" value="(x)"/>
  <define name="KDE_IS_VERSION(a, b, c)" value="( KDE_VERSION &gt;= KDE_MAKE_VERSION(a,b,c) )"/>
  <define name="KDE_EXPORT" value=""/>
  <define name="KDE_IMPORT" value=""/>
  <define name="KDE_MAKE_VERSION(a, b, c)" value="(((a) &lt;&lt; 16) | ((b) &lt;&lt; 8) | (c))"/>
  <define name="KDE_MUST_USE_RESULT" value="__attribute__((__warn_unused_result__))"/>
  <define name="KDE_NO_DEPRECATED" value=""/>
  <define name="KDE_NO_EXPORT" value=""/>
  <define name="KDE_PACKED" value=""/>
  <define name="KDE_VERSION" value="KDE_MAKE_VERSION(KDE_VERSION_MAJOR,KDE_VERSION_MINOR,KDE_VERSION_RELEASE)"/>
  <define name="KDE_WEAK_SYMBOL" value=""/>
  <!-- ########## KDE Allocation / Deallocation ########## -->
  <!-- ########## KDE Functions ########## -->
  <!-- template<typename T > T KConfigGroup::readEntry ( const QString & key, const T & aDefault ) const -->
  <!-- template<typename T > T KConfigGroup::readEntry ( const char * key, const T & aDefault ) const -->
  <!-- QVariant KConfigGroup::readEntry ( const QString & key, const QVariant & aDefault ) const -->
  <!-- QVariant KConfigGroup::readEntry ( const char * key, const QVariant & aDefault ) const -->
  <!-- QString KConfigGroup::readEntry ( const QString & key, const QString & aDefault ) const -->
  <!-- QString KConfigGroup::readEntry ( const char * key, const QString & aDefault ) const -->
  <!-- QString KConfigGroup::readEntry ( const QString & key, const char * aDefault = 0 ) const -->
  <!-- QString KConfigGroup::readEntry ( const char * key, const char * aDefault = 0 ) const -->
  <!-- QVariantList KConfigGroup::readEntry ( const QString & key, const QVariantList & aDefault ) const -->
  <!-- QVariantList KConfigGroup::readEntry ( const char * key, const QVariantList & aDefault ) const -->
  <!-- QStringList KConfigGroup::readEntry ( const QString & key, const QStringList & aDefault ) const -->
  <!-- QStringList KConfigGroup::readEntry ( const char * key, const QStringList & aDefault ) const -->
  <!-- template<typename T > QList<T> KConfigGroup::readEntry ( const QString & key, const QList< T > & aDefault ) const -->
  <!-- template<typename T > QList<T> KConfigGroup::readEntry ( const char * key, const QList< T > & aDefault ) const -->
  <function name="KConfigGroup::readEntry">
    <noreturn>false</noreturn>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="0">
      <not-uninit/>
    </arg>
  </function>
</def>
