<?xml version="1.0"?>
<!-- Official repository: https://github.com/libyal/libcerror -->
<!-- Typically included by: #include <libcerror.h> -->
<def format="2">
  <!-- int libcerror_error_initialize( libcerror_error_t **error,
                                       int error_domain,
                                       int error_code ); -->
  <function name="libcerror_error_initialize">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
    <arg nr="3">
      <not-uninit/>
    </arg>
  </function>
  <!-- void libcerror_error_set( libcerror_error_t **error,
                                 int error_domain,
                                 int error_code,
                                 const char *format_string,
                                 ... ); -->
  <function name="libcerror_error_set">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
    <arg nr="3">
      <not-uninit/>
    </arg>
    <formatstr/>
    <arg nr="4">
      <not-null/>
      <not-uninit/>
      <formatstr/>
      <strz/>
    </arg>
  </function>
  <!-- void libcerror_error_free( libcerror_error_t **error ); -->
  <function name="libcerror_error_free">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- int libcerror_error_resize( libcerror_internal_error_t *internal_error ); -->
  <function name="libcerror_error_resize">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- int libcerror_error_fprint( libcerror_error_t *error, FILE *stream ); -->
  <!-- int libcerror_error_backtrace_fprint( libcerror_error_t *error, FILE *stream ); -->
  <function name="libcerror_error_fprint,libcerror_error_backtrace_fprint">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- int libcerror_error_sprint( libcerror_error_t *error, char *string, size_t size ); -->
  <!-- int libcerror_error_backtrace_sprint( libcerror_error_t *error, char *string, size_t size ); -->
  <function name="libcerror_error_sprint,libcerror_error_backtrace_sprint">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-null/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- void libcerror_system_set_error( libcerror_error_t **error,
                                        int error_domain,
                                        int error_code,
                                        uint32_t system_error_code,
                                        const char *format_string,
                                        ... ); -->
  <function name="libcerror_system_set_error">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
    <arg nr="3">
      <not-uninit/>
    </arg>
    <arg nr="4">
      <not-uninit/>
    </arg>
    <formatstr/>
    <arg nr="5">
      <not-null/>
      <not-uninit/>
      <formatstr/>
      <strz/>
    </arg>
  </function>
  <!-- enum LIBCERROR_ERROR_DOMAINS: -->
  <define name="LIBCERROR_ERROR_DOMAIN_ARGUMENTS" value="(int)&apos;a&apos;"/>
  <define name="LIBCERROR_ERROR_DOMAIN_CONVERSION" value="(int)&apos;c&apos;"/>
  <define name="LIBCERROR_ERROR_DOMAIN_COMPRESSION" value="(int)&apos;C&apos;"/>
  <define name="LIBCERROR_ERROR_DOMAIN_ENCRYPTION" value="(int)&apos;E&apos;"/>
  <define name="LIBCERROR_ERROR_DOMAIN_IO" value="(int)&apos;I&apos;"/>
  <define name="LIBCERROR_ERROR_DOMAIN_INPUT" value="(int)&apos;i&apos;"/>
  <define name="LIBCERROR_ERROR_DOMAIN_MEMORY" value="(int)&apos;m&apos;"/>
  <define name="LIBCERROR_ERROR_DOMAIN_OUTPUT" value="(int)&apos;o&apos;"/>
  <define name="LIBCERROR_ERROR_DOMAIN_RUNTIME" value="(int)&apos;r&apos;"/>
  <!-- enum LIBCERROR_ARGUMENT_ERROR: -->
  <define name="LIBCERROR_ARGUMENT_ERROR_GENERIC" value="0"/>
  <define name="LIBCERROR_ARGUMENT_ERROR_INVALID_VALUE" value="1"/>
  <define name="LIBCERROR_ARGUMENT_ERROR_VALUE_LESS_THAN_ZERO" value="2"/>
  <define name="LIBCERROR_ARGUMENT_ERROR_VALUE_ZERO_OR_LESS" value="3"/>
  <define name="LIBCERROR_ARGUMENT_ERROR_VALUE_EXCEEDS_MAXIMUM" value="4"/>
  <define name="LIBCERROR_ARGUMENT_ERROR_VALUE_TOO_SMALL" value="5"/>
  <define name="LIBCERROR_ARGUMENT_ERROR_VALUE_TOO_LARGE" value="6"/>
  <define name="LIBCERROR_ARGUMENT_ERROR_VALUE_OUT_OF_BOUNDS" value="7"/>
  <define name="LIBCERROR_ARGUMENT_ERROR_UNSUPPORTED_VALUE" value="8"/>
  <define name="LIBCERROR_ARGUMENT_ERROR_CONFLICTING_VALUE" value="9"/>
  <!-- enum LIBCERROR_CONVERSION_ERROR: -->
  <define name="LIBCERROR_CONVERSION_ERROR_GENERIC" value="0"/>
  <define name="LIBCERROR_CONVERSION_ERROR_INPUT_FAILED" value="1"/>
  <define name="LIBCERROR_CONVERSION_ERROR_OUTPUT_FAILED" value="2"/>
  <!-- enum LIBCERROR_COMPRESSION_ERROR: -->
  <define name="LIBCERROR_COMPRESSION_ERROR_GENERIC" value="0"/>
  <define name="LIBCERROR_COMPRESSION_ERROR_COMPRESS_FAILED" value="1"/>
  <define name="LIBCERROR_COMPRESSION_ERROR_DECOMPRESS_FAILED" value="2"/>
  <!-- enum LIBCERROR_ENCRYPTION_ERROR: -->
  <define name="LIBCERROR_ENCRYPTION_ERROR_GENERIC" value="0"/>
  <define name="LIBCERROR_ENCRYPTION_ERROR_ENCRYPT_FAILED" value="1"/>
  <define name="LIBCERROR_ENCRYPTION_ERROR_DECRYPT_FAILED" value="2"/>
  <!-- enum LIBCERROR_IO_ERROR: -->
  <define name="LIBCERROR_IO_ERROR_GENERIC" value="0"/>
  <define name="LIBCERROR_IO_ERROR_OPEN_FAILED" value="1"/>
  <define name="LIBCERROR_IO_ERROR_CLOSE_FAILED" value="2"/>
  <define name="LIBCERROR_IO_ERROR_SEEK_FAILED" value="3"/>
  <define name="LIBCERROR_IO_ERROR_READ_FAILED" value="4"/>
  <define name="LIBCERROR_IO_ERROR_WRITE_FAILED" value="5"/>
  <define name="LIBCERROR_IO_ERROR_ACCESS_DENIED" value="6"/>
  <define name="LIBCERROR_IO_ERROR_INVALID_RESOURCE" value="7"/>
  <define name="LIBCERROR_IO_ERROR_IOCTL_FAILED" value="8"/>
  <define name="LIBCERROR_IO_ERROR_UNLINK_FAILED" value="9"/>
  <!-- enum LIBCERROR_INPUT_ERROR: -->
  <define name="LIBCERROR_INPUT_ERROR_GENERIC" value="0"/>
  <define name="LIBCERROR_INPUT_ERROR_INVALID_DATA" value="1"/>
  <define name="LIBCERROR_INPUT_ERROR_SIGNATURE_MISMATCH" value="2"/>
  <define name="LIBCERROR_INPUT_ERROR_CHECKSUM_MISMATCH" value="3"/>
  <define name="LIBCERROR_INPUT_ERROR_VALUE_MISMATCH" value="4"/>
  <!-- enum LIBCERROR_MEMORY_ERROR: -->
  <define name="LIBCERROR_MEMORY_ERROR_GENERIC" value="0"/>
  <define name="LIBCERROR_MEMORY_ERROR_INSUFFICIENT" value="1"/>
  <define name="LIBCERROR_MEMORY_ERROR_COPY_FAILED" value="2"/>
  <define name="LIBCERROR_MEMORY_ERROR_SET_FAILED" value="3"/>
  <!-- enum LIBCERROR_OUTPUT_ERROR: -->
  <define name="LIBCERROR_OUTPUT_ERROR_GENERIC" value="0"/>
  <define name="LIBCERROR_OUTPUT_ERROR_INSUFFICIENT_SPACE" value="1"/>
  <!-- enum LIBCERROR_RUNTIME_ERROR: -->
  <define name="LIBCERROR_RUNTIME_ERROR_GENERIC" value="0"/>
  <define name="LIBCERROR_RUNTIME_ERROR_VALUE_MISSING" value="1"/>
  <define name="LIBCERROR_RUNTIME_ERROR_VALUE_ALREADY_SET" value="2"/>
  <define name="LIBCERROR_RUNTIME_ERROR_INITIALIZE_FAILED" value="3"/>
  <define name="LIBCERROR_RUNTIME_ERROR_RESIZE_FAILED" value="4"/>
  <define name="LIBCERROR_RUNTIME_ERROR_FINALIZE_FAILED" value="5"/>
  <define name="LIBCERROR_RUNTIME_ERROR_GET_FAILED" value="6"/>
  <define name="LIBCERROR_RUNTIME_ERROR_SET_FAILED" value="7"/>
  <define name="LIBCERROR_RUNTIME_ERROR_APPEND_FAILED" value="8"/>
  <define name="LIBCERROR_RUNTIME_ERROR_COPY_FAILED" value="9"/>
  <define name="LIBCERROR_RUNTIME_ERROR_REMOVE_FAILED" value="10"/>
  <define name="LIBCERROR_RUNTIME_ERROR_PRINT_FAILED" value="11"/>
  <define name="LIBCERROR_RUNTIME_ERROR_VALUE_OUT_OF_BOUNDS" value="12"/>
  <define name="LIBCERROR_RUNTIME_ERROR_VALUE_EXCEEDS_MAXIMUM" value="13"/>
  <define name="LIBCERROR_RUNTIME_ERROR_UNSUPPORTED_VALUE" value="14"/>
  <define name="LIBCERROR_RUNTIME_ERROR_ABORT_REQUESTED" value="15"/>
</def>
