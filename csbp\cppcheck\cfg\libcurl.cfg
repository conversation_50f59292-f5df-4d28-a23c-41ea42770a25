<?xml version="1.0"?>
<def format="2">
  <!-- libcurl C API. See https://curl.haxx.se/libcurl/c/ -->
  <!-- The libcurl C API is typically include by '#include <curl/curl.h>' -->
  <!-- ########## libcurl Types ########## -->
  <podtype name="CURLcode"/>
  <!-- ########## libcurl defines / macros ########## -->
  <define name="CURLE_OK" value="(0)"/>
  <!-- ########## libcurl Allocation / Deallocation ########## -->
  <resource>
    <alloc init="true">curl_easy_init</alloc>
    <alloc init="true">curl_easy_duphandle</alloc>
    <dealloc>curl_easy_cleanup</dealloc>
  </resource>
  <memory>
    <alloc init="true">curl_easy_escape</alloc>
    <alloc init="true">curl_easy_unescape</alloc>
    <alloc init="true">curl_escape</alloc>
    <alloc init="true">curl_unescape</alloc>
    <dealloc>curl_free</dealloc>
  </memory>
  <memory>
    <alloc init="true">curl_getenv</alloc>
    <alloc init="true">curl_maprintf</alloc>
    <alloc init="true">curl_mvaprintf</alloc>
    <dealloc>free</dealloc>
  </memory>
  <!-- ########## libcurl C API Functions ########## -->
  <!-- void curl_easy_cleanup(CURL * handle ); -->
  <function name="curl_easy_cleanup">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- CURL *curl_easy_duphandle(CURL *handle ); -->
  <function name="curl_easy_duphandle">
    <noreturn>false</noreturn>
    <returnValue type="CURL *"/>
    <use-retval/>
    <arg nr="1">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- char *curl_easy_escape( CURL * curl , const char * string , int length ); -->
  <function name="curl_easy_escape">
    <noreturn>false</noreturn>
    <returnValue type="char *"/>
    <use-retval/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- CURLcode curl_easy_getinfo(CURL *curl, CURLINFO info, ... ); -->
  <function name="curl_easy_getinfo">
    <noreturn>false</noreturn>
    <returnValue type="CURLcode"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- CURL *curl_easy_init( ); -->
  <function name="curl_easy_init">
    <noreturn>false</noreturn>
    <returnValue type="CURL *"/>
    <use-retval/>
  </function>
  <!-- CURLcode curl_easy_pause(CURL *handle , int bitmask ); -->
  <function name="curl_easy_pause">
    <noreturn>false</noreturn>
    <returnValue type="CURLcode"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- CURLcode curl_easy_perform(CURL * easy_handle ); -->
  <function name="curl_easy_perform">
    <noreturn>false</noreturn>
    <returnValue type="CURLcode"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- CURLcode curl_easy_recv( CURL * curl , void * buffer , size_t buflen , size_t * n ); -->
  <function name="curl_easy_recv">
    <noreturn>false</noreturn>
    <returnValue type="CURLcode"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- void curl_easy_reset(CURL *handle ); -->
  <function name="curl_easy_reset">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- CURLcode curl_easy_send( CURL * curl , const void * buffer , size_t buflen , size_t * n ); -->
  <function name="curl_easy_send">
    <noreturn>false</noreturn>
    <returnValue type="CURLcode"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <minsize type="argvalue" arg="3"/>
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- CURLcode curl_easy_setopt(CURL *handle, CURLoption option, parameter); -->
  <function name="curl_easy_setopt">
    <noreturn>false</noreturn>
    <returnValue type="CURLcode"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- const char *curl_easy_strerror(CURLcode errornum); -->
  <function name="curl_easy_strerror">
    <noreturn>false</noreturn>
    <returnValue type="const char *"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- char *curl_easy_unescape( CURL * curl , const char * url , int inlength , int * outlength ); -->
  <function name="curl_easy_unescape">
    <noreturn>false</noreturn>
    <returnValue type="char *"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- CURLcode curl_easy_upkeep(CURL * handle ); -->
  <function name="curl_easy_upkeep">
    <noreturn>false</noreturn>
    <returnValue type="CURLcode"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- char *curl_escape( const char * url , int length ); -->
  <function name="curl_escape">
    <noreturn>false</noreturn>
    <returnValue type="char *"/>
    <use-retval/>
    <warn severity="style" alternatives="curl_easy_escape" reason="Obsolete"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- void curl_free( char * ptr ); -->
  <function name="curl_free">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1"/>
  </function>
  <!-- char *curl_getenv(const char * name ); -->
  <function name="curl_getenv">
    <noreturn>false</noreturn>
    <returnValue type="char *"/>
    <use-retval/>
    <warn severity="style">This function will be removed from the public libcurl API in a near future. It will instead be made "available" by source code access only, and then as curlx_getenv().</warn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- char *curl_maprintf(const char * format , ...); -->
  <function name="curl_maprintf">
    <noreturn>false</noreturn>
    <returnValue type="char *"/>
    <use-retval/>
    <warn severity="style">These functions will be removed from the public libcurl API in the future. Do not use them in any new programs or projects.</warn>
    <formatstr/>
    <arg nr="1" direction="in">
      <formatstr/>
      <not-uninit/>
    </arg>
  </function>
  <!-- int curl_mfprintf(FILE * fd , const char * format , ...); -->
  <function name="curl_mfprintf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <warn severity="style" alternatives="fprintf" reason="Obsolete"/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
    <formatstr/>
    <arg nr="2" direction="in">
      <formatstr/>
      <not-uninit/>
    </arg>
  </function>
  <!-- int curl_mprintf(const char * format , ...); -->
  <function name="curl_mprintf">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <warn severity="style" alternatives="printf" reason="Obsolete"/>
    <formatstr/>
    <arg nr="1" direction="in">
      <formatstr/>
      <not-uninit/>
    </arg>
  </function>
  <!-- int curl_msnprintf(char * buffer , size_t maxlength , const char * format , ...);  -->
  <function name="curl_msnprintf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <warn severity="style" alternatives="snprintf" reason="Obsolete"/>
    <arg nr="1" direction="out">
      <not-uninit/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <formatstr/>
    <arg nr="3" direction="in">
      <formatstr/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- int curl_msprintf(char * buffer , const char * format , ...); -->
  <function name="curl_msprintf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <warn severity="style" alternatives="sprintf" reason="Obsolete"/>
    <arg nr="1" direction="out">
      <not-uninit/>
      <minsize type="strlen" arg="2"/>
    </arg>
    <formatstr/>
    <arg nr="2" direction="in">
      <formatstr/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- CURLMcode curl_multi_socket(CURLM * multi_handle, curl_socket_t sockfd, int *running_handles); -->
  <function name="curl_multi_socket">
    <noreturn>false</noreturn>
    <returnValue type="CURLMcode"/>
    <leak-ignore/>
    <warn severity="style" alternatives="curl_multi_socket_action" reason="Obsolete">Usage of curl_multi_socket is deprecated, whereas the function is equivalent to curl_multi_socket_action with ev_bitmask set to 0.</warn>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- CURLMcode curl_multi_socket_all(CURLM *multi_handle, int *running_handles); -->
  <function name="curl_multi_socket_all">
    <noreturn>false</noreturn>
    <returnValue type="CURLMcode"/>
    <leak-ignore/>
    <warn severity="style" alternatives="curl_multi_socket_action" reason="Obsolete"/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- char *curl_mvaprintf(const char * format , va_list args ); -->
  <function name="curl_mvaprintf">
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <use-retval/>
    <warn severity="style">These functions will be removed from the public libcurl API in the future. Do not use them in any new programs or projects.</warn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <formatstr/>
    </arg>
    <arg nr="2"/>
  </function>
  <!-- int curl_mvfprintf(FILE * fd , const char * format , va_list args ); -->
  <function name="curl_mvfprintf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <warn severity="style" alternatives="vfprintf" reason="Obsolete"/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <formatstr/>
    </arg>
    <arg nr="3"/>
  </function>
  <!-- int curl_mvprintf(const char * format , va_list args ); -->
  <function name="curl_mvprintf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <warn severity="style" alternatives="vprintf" reason="Obsolete"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <formatstr/>
    </arg>
    <arg nr="2"/>
  </function>
  <!-- int curl_mvsnprintf(char * buffer , size_t maxlength , const char * format , va_list args ); -->
  <function name="curl_mvsnprintf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <warn severity="style" alternatives="vsnprintf" reason="Obsolete"/>
    <arg nr="1" direction="out">
      <not-uninit/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-null/>
      <not-uninit/>
      <formatstr/>
    </arg>
    <arg nr="4"/>
  </function>
  <!-- int curl_mvsprintf(char * buffer , const char * format , va_list args ); -->
  <function name="curl_mvsprintf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <warn severity="style" alternatives="vsprintf" reason="Obsolete"/>
    <arg nr="1" direction="out">
      <not-null/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <formatstr/>
    </arg>
    <arg nr="3"/>
  </function>
  <!-- int curl_strequal(char * str1 , char * str2 ); -->
  <function name="curl_strequal">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <warn severity="style">These functions will be removed from the public libcurl API in a near future. They will instead be made "available" by source code access only, and then as curlx_strequal() and curlx_strenqual().</warn>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- int curl_strnequal(char * str1 , char * str2 , size_t len ); -->
  <function name="curl_strnequal">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <pure/>
    <leak-ignore/>
    <warn severity="style">These functions will be removed from the public libcurl API in a near future. They will instead be made "available" by source code access only, and then as curlx_strequal() and curlx_strenqual().</warn>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- char *curl_unescape( const char * url , int length ); -->
  <function name="curl_unescape">
    <noreturn>false</noreturn>
    <returnValue type="char *"/>
    <use-retval/>
    <warn severity="style" alternatives="curl_easy_unescape" reason="Obsolete"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
</def>
