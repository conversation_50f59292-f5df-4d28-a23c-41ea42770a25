<?xml version="1.0"?>
<def format="2">
  <!-- libsigc++ Library Configuration https://libsigcplusplus.github.io/libsigcplusplus/ -->
  <!-- On GitHub: https://github.com/libsigcplusplus/libsigcplusplus -->
  <!-- Typically included via: "#include <sigc++/sigc++.h>" -->
  <!-- ########## libsigc++ Types ########## -->
  <!-- ########## libsigc++ Macros / Defines ########## -->
  <!-- ########## libsigc++ Allocation / Deallocation ########## -->
  <!-- ########## libsigc++ Functions ########## -->
  <!-- sigc::mem_fun for libsigc++ 2.0: -->
  <!-- template<class T_return , class T_obj >
           mem_functor0< T_return, T_obj > sigc::mem_fun (T_return(T_obj::* _A_func)()) -->
  <!-- template<class T_arg1 , class T_return , class T_obj >
           mem_functor1< T_return, T_obj, T_arg1 > sigc::mem_fun (T_return(T_obj::* _A_func)(T_arg1)) -->
  <!-- template<class T_arg1 , class T_arg2 , class T_return , class T_obj >
           mem_functor2< T_return, T_obj, T_arg1, T_arg2 > sigc::mem_fun (T_return(T_obj::* _A_func)(T_arg1, T_arg2)) -->
  <!-- and so on, until:
       template<class T_arg1 , class T_arg2 , class T_arg3 , class T_arg4 , class T_arg5 , class T_arg6 , class T_arg7 , class T_return , class T_obj >
           mem_functor7< T_return, T_obj, T_arg1, T_arg2, T_arg3, T_arg4, T_arg5, T_arg6, T_arg7 > sigc::mem_fun (T_return(T_obj::* _A_func)(T_arg1, T_arg2, T_arg3, T_arg4, T_arg5, T_arg6, T_arg7)) -->
  <!-- template<class T_return , class T_obj , class T_obj2 >
           bound_mem_functor0< T_return, T_obj > sigc::mem_fun (T_obj* _A_obj, T_return(T_obj2::* _A_func)()) -->
  <!-- and so on, until:
       template<class T_arg1 , class T_arg2 , class T_arg3 , class T_arg4 , class T_arg5 , class T_arg6 , class T_arg7 , class T_return , class T_obj , class T_obj2 >
           bound_mem_functor7< T_return, T_obj, T_arg1, T_arg2, T_arg3, T_arg4, T_arg5, T_arg6, T_arg7 > sigc::mem_fun (T_obj& _A_obj, T_return(T_obj2::* _A_func)(T_arg1, T_arg2, T_arg3, T_arg4, T_arg5, T_arg6, T_arg7)) -->
  <!-- sigc::mem_fun since libsigc++ 3.0 -->
  <!-- template<typename T_return , typename T_obj , typename... T_arg>
           decltype(auto) sigc::mem_fun (T_return(T_obj::* func)(T_arg...)) -->
  <!-- template<typename T_return , typename T_obj , typename T_obj2 , typename... T_arg>
           decltype(auto) sigc::mem_fun (T_obj& obj, T_return(T_obj2::* func)(T_arg...)) -->
  <function name="sigc::mem_fun">
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1"/>
    <arg nr="2" default=""/>
  </function>
</def>
