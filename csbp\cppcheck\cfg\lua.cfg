<?xml version="1.0"?>
<def format="2">
  <!-- Lua C API. See https://www.lua.org/manual/5.3/manual.html#4 -->
  <!-- The Lua C API is typically include by '#include <lua.h>' or '#include "lua.h"' -->
  <!-- ########## Lua C API Types ########## -->
  <!-- https://www.lua.org/manual/5.3/manual.html#lua_Number -->
  <define name="lua_Number" value="double"/>
  <!-- https://www.lua.org/manual/5.3/manual.html#lua_Integer -->
  <podtype name="lua_Integer" sign="s"/>
  <!-- https://www.lua.org/manual/5.3/manual.html#lua_Unsigned -->
  <podtype name="lua_Unsigned" sign="u"/>
  <!-- ########## Lua C API Defines / Macros ########## -->
  <define name="define lua_setglobal(L,s)" value="lua_setfield(L, LUA_GLOBALSINDEX, s)"/>
  <!-- ########## Lua C API Functions ########## -->
  <!-- int lua_error (lua_State *L); -->
  <function name="lua_error">
    <noreturn>true</noreturn>
    <returnValue type="int"/>
    <arg nr="1"/>
  </function>
  <!-- int lua_gettop (lua_State *L); -->
  <function name="lua_gettop">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <arg nr="1"/>
  </function>
  <!-- int lua_isboolean (lua_State *L, int index); -->
  <!-- int lua_iscfunction (lua_State *L, int index); -->
  <!-- int lua_isfunction (lua_State *L, int index); -->
  <!-- int lua_isinteger (lua_State *L, int index); -->
  <!-- int lua_islightuserdata (lua_State *L, int index); -->
  <!-- int lua_isnil (lua_State *L, int index); -->
  <!-- int lua_isnone (lua_State *L, int index); -->
  <!-- int lua_isnoneornil (lua_State *L, int index); -->
  <!-- int lua_isnumber (lua_State *L, int index); -->
  <!-- int lua_isstring (lua_State *L, int index); -->
  <!-- int lua_istable (lua_State *L, int index); -->
  <!-- int lua_isthread (lua_State *L, int index); -->
  <!-- int lua_isuserdata (lua_State *L, int index); -->
  <function name="lua_isboolean,lua_iscfunction,lua_isfunction,lua_isinteger,lua_islightuserdata,lua_isnil,lua_isnone,lua_isnoneornil,lua_isnumber,lua_isstring,lua_istable,lua_isthread,lua_isuserdata">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <arg nr="1"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int lua_isyieldable (lua_State *L); -->
  <function name="lua_isyieldable">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <arg nr="1"/>
  </function>
  <!-- lua_State *lua_newstate (lua_Alloc f, void *ud); -->
  <function name="lua_newstate">
    <noreturn>false</noreturn>
    <returnValue type="lua_State *"/>
    <use-retval/>
    <arg nr="1"/>
    <arg nr="2"/>
  </function>
  <!-- void lua_pop (lua_State *L, int n); -->
  <function name="lua_pop">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>1:</valid>
    </arg>
  </function>
  <!-- void lua_pushboolean (lua_State *L, int b); -->
  <function name="lua_pushboolean">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- const char *lua_pushfstring (lua_State *L, const char *fmt, ...); -->
  <function name="lua_pushfstring">
    <noreturn>false</noreturn>
    <returnValue type="const char *"/>
    <arg nr="1"/>
    <formatstr/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <formatstr/>
    </arg>
  </function>
  <!-- void lua_pushglobaltable (lua_State *L); -->
  <function name="lua_pushglobaltable">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1"/>
  </function>
  <!-- void lua_pushinteger (lua_State *L, lua_Integer n); -->
  <function name="lua_pushinteger">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- const char *lua_pushliteral (lua_State *L, const char *s); -->
  <function name="lua_pushliteral">
    <noreturn>false</noreturn>
    <returnValue type="const char *"/>
    <arg nr="1"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- const char *lua_pushlstring (lua_State *L, const char *s, size_t len); -->
  <function name="lua_pushlstring">
    <noreturn>false</noreturn>
    <returnValue type="const char *"/>
    <arg nr="1"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- void lua_pushnil (lua_State *L); -->
  <function name="lua_pushnil">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1"/>
  </function>
  <!-- void lua_pushnumber (lua_State *L, lua_Number n); -->
  <function name="lua_pushnumber">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- const char *lua_pushstring (lua_State *L, const char *s); -->
  <function name="lua_pushstring">
    <noreturn>false</noreturn>
    <returnValue type="const char *"/>
    <arg nr="1"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- int lua_pushthread (lua_State *L); -->
  <function name="lua_pushthread">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1"/>
  </function>
  <!-- void lua_pushvalue (lua_State *L, int index); -->
  <function name="lua_pushvalue">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- const char *lua_pushvfstring (lua_State *L, const char *fmt, va_list argp); -->
  <function name="lua_pushvfstring">
    <noreturn>false</noreturn>
    <returnValue type="const char *"/>
    <arg nr="1"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <formatstr/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int lua_rawequal (lua_State *L, int index1, int index2); -->
  <function name="lua_rawequal">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void lua_rawgeti (lua_State *L, int index, int n); -->
  <function name="lua_rawgeti">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void lua_setfield (lua_State *L, int index, const char *k); -->
  <function name="lua_setfield">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- const char *lua_tolstring (lua_State *L, int index, size_t *len); -->
  <function name="lua_tolstring">
    <noreturn>false</noreturn>
    <returnValue type="const char *"/>
    <arg nr="1"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- lua_Number lua_tonumber (lua_State *L, int index); -->
  <function name="lua_tonumber">
    <noreturn>false</noreturn>
    <returnValue type="lua_Number"/>
    <use-retval/>
    <arg nr="1"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- lua_Number lua_tonumberx (lua_State *L, int index, int *isnum); -->
  <function name="lua_tonumberx">
    <noreturn>false</noreturn>
    <returnValue type="lua_Number"/>
    <arg nr="1"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- const char *lua_tostring (lua_State *L, int index); -->
  <function name="lua_tostring">
    <noreturn>false</noreturn>
    <returnValue type="const char *"/>
    <use-retval/>
    <arg nr="1"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int lua_type (lua_State *L, int index); -->
  <function name="lua_type">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- const char *lua_typename (lua_State *L, int tp); -->
  <function name="lua_typename">
    <noreturn>false</noreturn>
    <returnValue type="const char *"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int luaL_error (lua_State *L, const char *fmt, ...); -->
  <function name="luaL_error">
    <noreturn>true</noreturn>
    <returnValue type="int"/>
    <arg nr="1"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="variadic" direction="in">
      <not-uninit/>
    </arg>
  </function>
</def>
