<?xml version="1.0"?>
<def format="2">
  <define name="DECLARE_MESSAGE_MAP()" value=""/>
  <define name="BEGIN_MESSAGE_MAP(x,y)" value=""/>
  <define name="BEGIN_TEMPLATE_MESSAGE_MAP(x,y,z)" value=""/>
  <define name="ON_COMMAND(x,y)" value=""/>
  <define name="ON_COMMAND_RANGE(x,y,z)" value=""/>
  <define name="ON_COMMAND_EX(x,y)" value=""/>
  <define name="ON_COMMAND_EX_RANGE(x,y,z)" value=""/>
  <define name="ON_UPDATE_COMMAND_UI(x,y)" value=""/>
  <define name="ON_UPDATE_COMMAND_UI_RANGE(x,y,z)" value=""/>
  <define name="ON_NOTIFY(x,y,z)" value=""/>
  <define name="ON_NOTIFY_RANGE(w,x,y,z)" value=""/>
  <define name="ON_NOTIFY_EX(x,y,z)" value=""/>
  <define name="ON_NOTIFY_EX_RANGE(w,x,y,z)" value=""/>
  <define name="ON_CONTROL(x,y,z)" value=""/>
  <define name="ON_CONTROL_RANGE(w,x,y,z)" value=""/>
  <define name="ON_CONTROL_REFLECT(x,y)" value=""/>
  <define name="ON_CONTROL_REFLECT_EX(x,y)" value=""/>
  <define name="ON_NOTIFY_REFLECT(x,y)" value=""/>
  <define name="ON_NOTIFY_REFLECT_EX(x,y)" value=""/>
  <define name="ON_UPDATE_COMMAND_UI_REFLECT(x)" value=""/>
  <define name="ON_WM_CREATE()" value=""/>
  <define name="ON_WM_COPYDATA()" value=""/>
  <define name="ON_WM_DESTROY()" value=""/>
  <define name="ON_WM_MOVE()" value=""/>
  <define name="ON_WM_SIZE()" value=""/>
  <define name="ON_WM_ACTIVATE()" value=""/>
  <define name="ON_WM_SETFOCUS()" value=""/>
  <define name="ON_WM_KILLFOCUS()" value=""/>
  <define name="ON_WM_ENABLE()" value=""/>
  <define name="ON_WM_HELPINFO()" value=""/>
  <define name="ON_WM_PAINT()" value=""/>
  <define name="ON_WM_SYNCPAINT()" value=""/>
  <define name="ON_WM_CLOSE()" value=""/>
  <define name="ON_WM_QUERYENDSESSION()" value=""/>
  <define name="ON_WM_QUERYOPEN()" value=""/>
  <define name="ON_WM_ERASEBKGND()" value=""/>
  <define name="ON_WM_PRINTCLIENT()" value=""/>
  <define name="ON_WM_SYSCOLORCHANGE()" value=""/>
  <define name="ON_WM_ENDSESSION()" value=""/>
  <define name="ON_WM_SETTEXT()" value=""/>
  <define name="ON_WM_GETTEXT()" value=""/>
  <define name="ON_WM_GETTEXTLENGTH()" value=""/>
  <define name="ON_WM_SETFONT()" value=""/>
  <define name="ON_WM_GETFONT()" value=""/>
  <define name="ON_WM_SETICON()" value=""/>
  <define name="ON_WM_MDISETMENU()" value=""/>
  <define name="ON_WM_MDIREFRESHMENU()" value=""/>
  <define name="ON_WM_MDIDESTROY()" value=""/>
  <define name="ON_WM_MDINEXT()" value=""/>
  <define name="ON_WM_CUT()" value=""/>
  <define name="ON_WM_COPY()" value=""/>
  <define name="ON_WM_PASTE()" value=""/>
  <define name="ON_WM_CLEAR()" value=""/>
  <define name="ON_WM_DISPLAYCHANGE()" value=""/>
  <define name="ON_WM_DDE_INITIATE()" value=""/>
  <define name="ON_WM_DDE_EXECUTE()" value=""/>
  <define name="ON_WM_DDE_TERMINATE()" value=""/>
  <define name="ON_WM_WTSSESSION_CHANGE()" value=""/>
  <define name="ON_WM_SHOWWINDOW()" value=""/>
  <define name="ON_WM_CTLCOLOR()" value=""/>
  <define name="ON_WM_CTLCOLOR_REFLECT()" value=""/>
  <define name="ON_WM_SETTINGCHANGE()" value=""/>
  <define name="ON_WM_WININICHANGE()" value=""/>
  <define name="ON_WM_DEVMODECHANGE()" value=""/>
  <define name="ON_WM_ACTIVATEAPP()" value=""/>
  <define name="ON_WM_FONTCHANGE()" value=""/>
  <define name="ON_WM_TIMECHANGE()" value=""/>
  <define name="ON_WM_CANCELMODE()" value=""/>
  <define name="ON_WM_SETCURSOR()" value=""/>
  <define name="ON_WM_MOUSEACTIVATE()" value=""/>
  <define name="ON_WM_CHILDACTIVATE()" value=""/>
  <define name="ON_WM_GETMINMAXINFO()" value=""/>
  <define name="ON_WM_ICONERASEBKGND()" value=""/>
  <define name="ON_WM_SPOOLERSTATUS()" value=""/>
  <define name="ON_WM_DRAWITEM()" value=""/>
  <define name="ON_WM_DRAWITEM_REFLECT()" value=""/>
  <define name="ON_WM_MEASUREITEM()" value=""/>
  <define name="ON_WM_MEASUREITEM_REFLECT()" value=""/>
  <define name="ON_WM_DELETEITEM()" value=""/>
  <define name="ON_WM_DELETEITEM_REFLECT()" value=""/>
  <define name="ON_WM_CHARTOITEM()" value=""/>
  <define name="ON_WM_CHARTOITEM_REFLECT()" value=""/>
  <define name="ON_WM_VKEYTOITEM()" value=""/>
  <define name="ON_WM_VKEYTOITEM_REFLECT()" value=""/>
  <define name="ON_WM_QUERYDRAGICON()" value=""/>
  <define name="ON_WM_COMPAREITEM()" value=""/>
  <define name="ON_WM_COMPAREITEM_REFLECT()" value=""/>
  <define name="ON_WM_COMPACTING()" value=""/>
  <define name="ON_WM_NCCREATE()" value=""/>
  <define name="ON_WM_NCDESTROY()" value=""/>
  <define name="ON_WM_NCCALCSIZE()" value=""/>
  <define name="ON_WM_NCHITTEST()" value=""/>
  <define name="ON_WM_NCPAINT()" value=""/>
  <define name="ON_WM_NCACTIVATE()" value=""/>
  <define name="ON_WM_GETDLGCODE()" value=""/>
  <define name="ON_WM_NCMOUSEMOVE()" value=""/>
  <define name="ON_WM_NCMOUSEHOVER()" value=""/>
  <define name="ON_WM_NCMOUSELEAVE()" value=""/>
  <define name="ON_WM_NCLBUTTONDOWN()" value=""/>
  <define name="ON_WM_NCLBUTTONUP()" value=""/>
  <define name="ON_WM_NCLBUTTONDBLCLK()" value=""/>
  <define name="ON_WM_NCRBUTTONDOWN()" value=""/>
  <define name="ON_WM_NCRBUTTONUP()" value=""/>
  <define name="ON_WM_NCRBUTTONDBLCLK()" value=""/>
  <define name="ON_WM_NCMBUTTONDOWN()" value=""/>
  <define name="ON_WM_NCMBUTTONUP()" value=""/>
  <define name="ON_WM_NCMBUTTONDBLCLK()" value=""/>
  <define name="ON_WM_NCXBUTTONDOWN()" value=""/>
  <define name="ON_WM_NCXBUTTONUP()" value=""/>
  <define name="ON_WM_NCXBUTTONDBLCLK()" value=""/>
  <define name="ON_WM_KEYDOWN()" value=""/>
  <define name="ON_WM_KEYUP()" value=""/>
  <define name="ON_WM_HOTKEY()" value=""/>
  <define name="ON_WM_CHAR()" value=""/>
  <define name="ON_WM_UNICHAR()" value=""/>
  <define name="ON_WM_DEADCHAR()" value=""/>
  <define name="ON_WM_SYSKEYDOWN()" value=""/>
  <define name="ON_WM_SYSKEYUP()" value=""/>
  <define name="ON_WM_SYSCHAR()" value=""/>
  <define name="ON_WM_SYSDEADCHAR()" value=""/>
  <define name="ON_WM_SYSCOMMAND()" value=""/>
  <define name="ON_WM_INPUTLANGCHANGE()" value=""/>
  <define name="ON_WM_INPUTLANGCHANGEREQUEST()" value=""/>
  <define name="ON_WM_APPCOMMAND()" value=""/>
  <define name="ON_WM_INPUT()" value=""/>
  <define name="ON_WM_INPUT_DEVICE_CHANGE()" value=""/>
  <define name="ON_WM_TCARD()" value=""/>
  <define name="ON_WM_TIMER()" value=""/>
  <define name="ON_WM_HSCROLL()" value=""/>
  <define name="ON_WM_HSCROLL_REFLECT()" value=""/>
  <define name="ON_WM_VSCROLL()" value=""/>
  <define name="ON_WM_VSCROLL_REFLECT()" value=""/>
  <define name="ON_WM_INITMENU()" value=""/>
  <define name="ON_WM_INITMENUPOPUP()" value=""/>
  <define name="ON_WM_MENUSELECT()" value=""/>
  <define name="ON_WM_MENUCHAR()" value=""/>
  <define name="ON_WM_MENURBUTTONUP()" value=""/>
  <define name="ON_WM_MENUDRAG()" value=""/>
  <define name="ON_WM_MENUGETOBJECT()" value=""/>
  <define name="ON_WM_UNINITMENUPOPUP()" value=""/>
  <define name="ON_WM_NEXTMENU()" value=""/>
  <define name="ON_WM_ENTERIDLE()" value=""/>
  <define name="ON_WM_MOUSEMOVE()" value=""/>
  <define name="ON_WM_MOUSEHOVER()" value=""/>
  <define name="ON_WM_MOUSELEAVE()" value=""/>
  <define name="ON_WM_MOUSEWHEEL()" value=""/>
  <define name="ON_WM_MOUSEHWHEEL()" value=""/>
  <define name="ON_WM_LBUTTONDOWN()" value=""/>
  <define name="ON_WM_LBUTTONUP()" value=""/>
  <define name="ON_WM_LBUTTONDBLCLK()" value=""/>
  <define name="ON_WM_RBUTTONDOWN()" value=""/>
  <define name="ON_WM_RBUTTONUP()" value=""/>
  <define name="ON_WM_RBUTTONDBLCLK()" value=""/>
  <define name="ON_WM_MBUTTONDOWN()" value=""/>
  <define name="ON_WM_MBUTTONUP()" value=""/>
  <define name="ON_WM_MBUTTONDBLCLK()" value=""/>
  <define name="ON_WM_XBUTTONDOWN()" value=""/>
  <define name="ON_WM_XBUTTONUP()" value=""/>
  <define name="ON_WM_XBUTTONDBLCLK()" value=""/>
  <define name="ON_WM_PARENTNOTIFY()" value=""/>
  <define name="ON_WM_PARENTNOTIFY_REFLECT()" value=""/>
  <define name="ON_WM_NOTIFYFORMAT()" value=""/>
  <define name="ON_WM_MDIACTIVATE()" value=""/>
  <define name="ON_WM_RENDERFORMAT()" value=""/>
  <define name="ON_WM_RENDERALLFORMATS()" value=""/>
  <define name="ON_WM_DESTROYCLIPBOARD()" value=""/>
  <define name="ON_WM_DRAWCLIPBOARD()" value=""/>
  <define name="ON_WM_PAINTCLIPBOARD()" value=""/>
  <define name="ON_WM_VSCROLLCLIPBOARD()" value=""/>
  <define name="ON_WM_CONTEXTMENU()" value=""/>
  <define name="ON_WM_SIZECLIPBOARD()" value=""/>
  <define name="ON_WM_ASKCBFORMATNAME()" value=""/>
  <define name="ON_WM_CHANGECBCHAIN()" value=""/>
  <define name="ON_WM_HSCROLLCLIPBOARD()" value=""/>
  <define name="ON_WM_CLIPBOARDUPDATE()" value=""/>
  <define name="ON_WM_QUERYNEWPALETTE()" value=""/>
  <define name="ON_WM_PALETTECHANGED()" value=""/>
  <define name="ON_WM_PALETTEISCHANGING()" value=""/>
  <define name="ON_WM_DROPFILES()" value=""/>
  <define name="ON_WM_WINDOWPOSCHANGING()" value=""/>
  <define name="ON_WM_WINDOWPOSCHANGED()" value=""/>
  <define name="ON_WM_EXITMENULOOP()" value=""/>
  <define name="ON_WM_ENTERMENULOOP()" value=""/>
  <define name="ON_WM_STYLECHANGED()" value=""/>
  <define name="ON_WM_STYLECHANGING()" value=""/>
  <define name="ON_WM_SIZING()" value=""/>
  <define name="ON_WM_MOVING()" value=""/>
  <define name="ON_WM_ENTERSIZEMOVE()" value=""/>
  <define name="ON_WM_EXITSIZEMOVE()" value=""/>
  <define name="ON_WM_CAPTURECHANGED()" value=""/>
  <define name="ON_WM_DEVICECHANGE()" value=""/>
  <define name="ON_WM_POWERBROADCAST()" value=""/>
  <define name="ON_WM_USERCHANGED()" value=""/>
  <define name="ON_WM_CHANGEUISTATE()" value=""/>
  <define name="ON_WM_UPDATEUISTATE()" value=""/>
  <define name="ON_WM_QUERYUISTATE()" value=""/>
  <define name="ON_WM_THEMECHANGED()" value=""/>
  <define name="ON_WM_DWMCOMPOSITIONCHANGED()" value=""/>
  <define name="ON_WM_DWMNCRENDERINGCHANGED()" value=""/>
  <define name="ON_WM_DWMCOLORIZATIONCOLORCHANGED()" value=""/>
  <define name="ON_WM_DWMWINDOWMAXIMIZEDCHANGE()" value=""/>
  <define name="ON_WM_DWMSENDICONICTHUMBNAIL()" value=""/>
  <define name="ON_WM_DWMSENDICONICLIVEPREVIEWBITMAP()" value=""/>
  <define name="ON_STN_CLICKED(x,y)" value=""/>
  <define name="ON_STN_DBLCLK(x,y)" value=""/>
  <define name="ON_STN_ENABLE(x,y)" value=""/>
  <define name="ON_STN_DISABLE(x,y)" value=""/>
  <define name="ON_EN_SETFOCUS(x,y)" value=""/>
  <define name="ON_EN_KILLFOCUS(x,y)" value=""/>
  <define name="ON_EN_CHANGE(x,y)" value=""/>
  <define name="ON_EN_UPDATE(x,y)" value=""/>
  <define name="ON_EN_ERRSPACE(x,y)" value=""/>
  <define name="ON_EN_MAXTEXT(x,y)" value=""/>
  <define name="ON_EN_HSCROLL(x,y)" value=""/>
  <define name="ON_EN_VSCROLL(x,y)" value=""/>
  <define name="ON_EN_ALIGN_LTR_EC(x,y)" value=""/>
  <define name="ON_EN_ALIGN_RTL_EC(x,y)" value=""/>
  <define name="ON_EN_IMECHANGE(x,y)" value=""/>
  <define name="ON_EN_ALIGNLTR(x,y)" value=""/>
  <define name="ON_EN_ALIGNRTL(x,y)" value=""/>
  <define name="ON_ACN_START(x,y)" value=""/>
  <define name="ON_ACN_STOP(x,y)" value=""/>
  <define name="ON_BN_CLICKED(x,y)" value=""/>
  <define name="ON_BN_DOUBLECLICKED(x,y)" value=""/>
  <define name="ON_BN_SETFOCUS(x,y)" value=""/>
  <define name="ON_BN_KILLFOCUS(x,y)" value=""/>
  <define name="ON_BN_PAINT(x,y)" value=""/>
  <define name="ON_BN_HILITE(x,y)" value=""/>
  <define name="ON_BN_UNHILITE(x,y)" value=""/>
  <define name="ON_BN_DISABLE(x,y)" value=""/>
  <define name="ON_LBN_ERRSPACE(x,y)" value=""/>
  <define name="ON_LBN_SELCHANGE(x,y)" value=""/>
  <define name="ON_LBN_DBLCLK(x,y)" value=""/>
  <define name="ON_LBN_SELCANCEL(x,y)" value=""/>
  <define name="ON_LBN_SETFOCUS(x,y)" value=""/>
  <define name="ON_LBN_KILLFOCUS(x,y)" value=""/>
  <define name="ON_CLBN_CHKCHANGE(x,y)" value=""/>
  <define name="ON_CBN_ERRSPACE(x,y)" value=""/>
  <define name="ON_CBN_SELCHANGE(x,y)" value=""/>
  <define name="ON_CBN_DBLCLK(x,y)" value=""/>
  <define name="ON_CBN_SETFOCUS(x,y)" value=""/>
  <define name="ON_CBN_KILLFOCUS(x,y)" value=""/>
  <define name="ON_CBN_EDITCHANGE(x,y)" value=""/>
  <define name="ON_CBN_EDITUPDATE(x,y)" value=""/>
  <define name="ON_CBN_DROPDOWN(x,y)" value=""/>
  <define name="ON_CBN_CLOSEUP(x,y)" value=""/>
  <define name="ON_CBN_SELENDOK(x,y)" value=""/>
  <define name="ON_CBN_SELENDCANCEL(x,y)" value=""/>
  <define name="ON_MESSAGE(x,y)" value=""/>
  <define name="ON_REGISTERED_MESSAGE(x,y)" value=""/>
  <define name="ON_THREAD_MESSAGE(x,y)" value=""/>
  <define name="ON_REGISTERED_THREAD_MESSAGE(x,y)" value=""/>
  <define name="END_MESSAGE_MAP()" value=""/>
  <define name="DECLARE_DYNAMIC(x)" value=""/>
  <define name="IMPLEMENT_DYNAMIC(x,y)" value=""/>
  <define name="DECLARE_DYNCREATE(x)" value=""/>
  <define name="IMPLEMENT_DYNCREATE(x,y)" value=""/>
  <define name="DECLARE_SERIAL(x)" value=""/>
  <define name="IMPLEMENT_SERIAL(x,y,z)" value=""/>
  <define name="DECLARE_EVENTSINK_MAP()" value=""/>
  <define name="END_EVENTSINK_MAP()" value=""/>
  <define name="BEGIN_EVENTSINK_MAP(x,y)" value=""/>
  <define name="ON_EVENT(a,b,c,d,e)" value=""/>
  <define name="ON_EVENT_RANGE(a,b,c,d,e,f)" value=""/>
  <define name="ON_EVENT_REFLECT(a,b,c,d)" value=""/>
  <define name="ON_PROPNOTIFY(a,b,c,d,e)" value=""/>
  <define name="ON_PROPNOTIFY_RANGE(a,b,c,d,e,f)" value=""/>
  <define name="ON_PROPNOTIFY_REFLECT(a,b,c,d)" value=""/>
</def>
