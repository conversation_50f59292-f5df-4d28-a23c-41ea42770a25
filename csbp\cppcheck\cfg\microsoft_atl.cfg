<?xml version="1.0"?>
<def format="2">
  <!-- Microsoft Windows ATL (Active Template Library) https://docs.microsoft.com/en-us/cpp/atl/atl-com-desktop-components -->
  <define name="BEGIN_COM_MAP(x)" value=""/>
  <define name="END_COM_MAP()" value=""/>
  <define name="BEGIN_MSG_MAP(x)" value=""/>
  <define name="MESSAGE_HANDLER(msg, func)" value=""/>
  <define name="COMMAND_HANDLER(id, code, func)" value=""/>
  <define name="CHAIN_MSG_MAP(theChainClass)" value=""/>
  <define name="END_MSG_MAP()" value=""/>
  <define name="COM_INTERFACE_ENTRY(x)" value=""/>
  <define name="COM_INTERFACE_ENTRY2(x, x2)" value=""/>
  <define name="COM_INTERFACE_ENTRY_IID(iid, x)" value=""/>
  <define name="COM_INTERFACE_ENTRY2_IID(iid, x, x2)" value=""/>
  <define name="COM_INTERFACE_ENTRY_AGGREGATE(iid, punk)" value=""/>
  <define name="COM_INTERFACE_ENTRY_AGGREGATE_BLIND(punk)" value=""/>
  <define name="COM_INTERFACE_ENTRY_AUTOAGGREGATE(iid, punk, clsid)" value=""/>
  <define name="COM_INTERFACE_ENTRY_AUTOAGGREGATE_BLIND(punk, clsid)" value=""/>
  <define name="COM_INTERFACE_ENTRY_BREAK(x)" value=""/>
  <define name="COM_INTERFACE_ENTRY_CACHED_TEAR_OFF(iid, x, punk)" value=""/>
  <define name="COM_INTERFACE_ENTRY_TEAR_OFF(iid, x)" value=""/>
  <define name="COM_INTERFACE_ENTRY_CHAIN(classname)" value=""/>
  <define name="COM_INTERFACE_ENTRY_FUNC(iid, dw, func)" value=""/>
  <define name="COM_INTERFACE_ENTRY_FUNC_BLIND(dw, func)" value=""/>
  <define name="COM_INTERFACE_ENTRY_NOINTERFACE(x)" value=""/>
  <define name="DECLARE_LIBID(libid)" value=""/>
  <define name="DECLARE_NO_REGISTRY()" value=""/>
  <define name="DECLARE_NOT_AGGREGATABLE(x)" value=""/>
  <define name="DECLARE_OBJECT_DESCRIPTION(x)" value=""/>
  <define name="DECLARE_PROTECT_FINAL_CONSTRUCT()" value=""/>
  <define name="DECLARE_REGISTRY(class,pid,vpid,nid,flags)" value=""/>
  <define name="DECLARE_REGISTRY_APPID_RESOURCEID(resid,appid)" value=""/>
  <define name="DECLARE_REGISTRY_RESOURCE(x)" value=""/>
  <define name="DECLARE_REGISTRY_RESOURCEID(x)" value=""/>
  <define name="STDMETHOD(method)" value="HRESULT STDMETHODCALLTYPE method"/>
  <define name="STDMETHOD_(type,method)" value="type STDMETHODCALLTYPE method"/>
  <define name="IFACEMETHOD(method)" value="STDMETHOD(method)"/>
  <define name="IFACEMETHOD_(type,method)" value="STDMETHOD_(type,method)"/>
  <define name="OBJECT_ENTRY_AUTO(clsid, class)" value=""/>
</def>
