<?xml version="1.0"?>
<def format="2">
  <!-- Microsoft SAL Annotations (see http://msdn.microsoft.com/en-us/library/ms182032(v=vs.110).aspx -->
  <!-- Input Parameters -->
  <define name="_In_" value=""/>
  <define name="_In_z_" value=""/>
  <define name="_In_opt_" value=""/>
  <define name="_In_opt_z_" value=""/>
  <define name="_In_reads_opt_(s)" value=""/>
  <define name="_In_reads_bytes_opt_(s)" value=""/>
  <define name="_In_reads_opt_z_(s)" value=""/>
  <define name="_In_reads_or_z_opt_(s)" value=""/>
  <define name="_In_reads_to_ptr_opt_(p)" value=""/>
  <define name="_In_reads_to_ptr_opt_z_(p)" value=""/>
  <define name="_In_reads_(s)" value=""/>
  <define name="_In_reads_bytes_(s)" value=""/>
  <define name="_In_reads_z_(s)" value=""/>
  <define name="_In_reads_or_z_(s)" value=""/>
  <define name="_In_reads_to_ptr_(p)" value=""/>
  <define name="_In_reads_to_ptr_z_(p)" value=""/>
  <!-- In- and Output Parameters -->
  <define name="_Inout_" value=""/>
  <define name="_Inout_z_" value=""/>
  <define name="_Inout_opt_" value=""/>
  <define name="_Inout_opt_z_" value=""/>
  <define name="_Inout_updates_opt_(s)" value=""/>
  <define name="_Inout_updates_bytes_opt_(s)" value=""/>
  <define name="_Inout_updates_to_opt_(s, c)" value=""/>
  <define name="_Inout_updates_bytes_to_opt_(s, c)" value=""/>
  <define name="_Inout_updates_all_opt_(s)" value=""/>
  <define name="_Inout_updates_bytes_all_opt_(s)" value=""/>
  <define name="_Inout_updates_(s)" value=""/>
  <define name="_Inout_updates_bytes_(s)" value=""/>
  <define name="_Inout_updates_z_(s)" value=""/>
  <define name="_Inout_updates_to_(s, c)" value=""/>
  <define name="_Inout_updates_bytes_to_(s, c)" value=""/>
  <define name="_Inout_updates_all_(s)" value=""/>
  <define name="_Inout_updates_bytes_all_(s)" value=""/>
  <!-- Output Parameters -->
  <define name="_Out_" value=""/>
  <define name="_Out_opt_" value=""/>
  <define name="_Out_writes_opt_(s)" value=""/>
  <define name="_Out_writes_opt_z_(s)" value=""/>
  <define name="_Out_writes_to_opt_(s, c)" value=""/>
  <define name="_Out_writes_bytes_to_opt_(s, c)" value=""/>
  <define name="_Out_writes_all_opt_(s)" value=""/>
  <define name="_Out_writes_bytes_all_opt_(s)" value=""/>
  <define name="_Out_writes_to_ptr_opt_(p)" value=""/>
  <define name="_Out_writes_to_ptr_opt_z_(p)" value=""/>
  <define name="_Outptr_" value=""/>
  <define name="_Outptr_opt_" value=""/>
  <define name="_Outptr_result_maybenull_" value=""/>
  <define name="_Outptr_opt_result_maybenull_" value=""/>
  <define name="_Outptr_result_z_" value=""/>
  <define name="_Outptr_opt_result_z_" value=""/>
  <define name="_Outptr_result_maybenull_z_" value=""/>
  <define name="_Ouptr_opt_result_maybenull_z_" value=""/>
  <define name="_COM_Outptr_" value=""/>
  <define name="_COM_Outptr_opt_" value=""/>
  <define name="_COM_Outptr_result_maybenull_" value=""/>
  <define name="_COM_Outptr_opt_result_maybenull_" value=""/>
  <define name="_Out_writes_(s)" value=""/>
  <define name="_Out_writes_bytes_(s)" value=""/>
  <define name="_Out_writes_z_(s)" value=""/>
  <define name="_Out_writes_to_(s, c)" value=""/>
  <define name="_Out_writes_bytes_to_(s, c)" value=""/>
  <define name="_Out_writes_all_(s)" value=""/>
  <define name="_Out_writes_bytes_all_(s)" value=""/>
  <define name="_Out_writes_to_ptr_(p)" value=""/>
  <define name="_Out_writes_to_ptr_z_(p)" value=""/>
  <define name="_Outptr_result_buffer_(s)" value=""/>
  <define name="_Outptr_result_bytebuffer_(s)" value=""/>
  <define name="_Outptr_opt_result_buffer_(s)" value=""/>
  <define name="_Outptr_opt_result_bytebuffer_(s)" value=""/>
  <define name="_Outptr_result_buffer_to_(s, c)" value=""/>
  <define name="_Outptr_result_bytebuffer_to_(s, c)" value=""/>
  <define name="_Outptr_opt_result_buffer_to_(s, c)" value=""/>
  <define name="_Outptr_opt_result_bytebuffer_to_(s, c)" value=""/>
  <!-- Output Reference Parameters -->
  <define name="_Outref_" value=""/>
  <define name="_Outref_result_maybenull_" value=""/>
  <define name="_Outref_result_buffer_(s)" value=""/>
  <define name="_Outref_result_bytebuffer_(s)" value=""/>
  <define name="_Outref_result_buffer_to_(s, c)" value=""/>
  <define name="_Outref_result_bytebuffer_to_(s, c)" value=""/>
  <define name="_Outref_result_buffer_all_(s)" value=""/>
  <define name="_Outref_result_bytebuffer_all_(s)" value=""/>
  <define name="_Outref_result_buffer_maybenull_(s)" value=""/>
  <define name="_Outref_result_bytebuffer_maybenull_(s)" value=""/>
  <define name="_Outref_result_buffer_to_maybenull_(s, c)" value=""/>
  <define name="_Outref_result_bytebuffer_to_maybenull_(s, c)" value=""/>
  <define name="_Outref_result_buffer_all_maybenull_(s)" value=""/>
  <define name="_Outref_result_bytebuffer_all_maybenull_(s)" value=""/>
  <!-- Return Values -->
  <define name="_Ret_z_" value=""/>
  <define name="_Ret_maybenull_z_" value=""/>
  <define name="_Ret_notnull_" value=""/>
  <define name="_Ret_maybenull_" value=""/>
  <define name="_Ret_null_" value=""/>
  <define name="_Ret_valid_" value=""/>
  <define name="_Ret_writes_(s)" value=""/>
  <define name="_Ret_writes_bytes_(s)" value=""/>
  <define name="_Ret_writes_bytes_to_(s, c)" value=""/>
  <define name="_Ret_writes_z_(s)" value=""/>
  <define name="_Ret_writes_to_(s, c)" value=""/>
  <define name="_Ret_writes_maybenull_(s)" value=""/>
  <define name="_Ret_writes_to_maybenull_(s, c)" value=""/>
  <define name="_Ret_writes_maybenull_z_(s)" value=""/>
  <define name="_Ret_writes_bytes_to_maybenull_(s, c)" value=""/>
  <!-- Other Common Annotations -->
  <define name="_In_range_(low, hi)" value="__cppcheck_low__(low) __cppcheck_high__(hi)"/>
  <define name="_Out_range_(low, hi)" value=""/>
  <define name="_Ret_range_(low, hi)" value=""/>
  <define name="_Deref_in_range_(low, hi)" value=""/>
  <define name="_Deref_out_range_(low, hi)" value=""/>
  <define name="_Deref_ret_range_(low, hi)" value=""/>
  <define name="_Pre_equal_to_(expr)" value=""/>
  <define name="_Post_equal_to_(expr)" value=""/>
  <define name="_Struct_size_bytes_(size)" value=""/>
  <!-- Function Annotations -->
  <define name="_Called_from_function_class_(name)" value=""/>
  <define name="_Check_return_" value=""/>
  <define name="_Function_class_(name)" value=""/>
  <define name="_Raises_SEH_exception_" value=""/>
  <define name="_Maybe_raises_SEH_exception_" value=""/>
  <define name="_Must_inspect_result_" value=""/>
  <define name="_Use_decl_annotations_" value=""/>
  <!-- Success/Failure of Function Annotations -->
  <define name="_Always_(anno_list)" value=""/>
  <define name="_On_failure_(anno_list)" value=""/>
  <define name="_Return_type_success_(expr)" value=""/>
  <define name="_Success_(expr)" value=""/>
  <!-- Struct and Class Annotations -->
  <define name="_Field_range_(low, high)" value="__cppcheck_low__(low) __cppcheck_high__(high)"/>
  <define name="_Field_size_(size)" value=""/>
  <define name="_Field_size_opt_(size)" value=""/>
  <define name="_Field_size_bytes_(size)" value=""/>
  <define name="_Field_size_bytes_opt_(size)" value=""/>
  <define name="_Field_size_part_(size, count)" value=""/>
  <define name="_Field_size_part_opt_(size, count)" value=""/>
  <define name="_Field_size_bytes_part_(size, count)" value=""/>
  <define name="_Field_size_bytes_part_opt_(size, count)" value=""/>
  <define name="_Field_size_full_(size)" value=""/>
  <define name="_Field_size_full_opt_(size)" value=""/>
  <define name="_Field_size_bytes_full_(size)" value=""/>
  <define name="_Field_size_bytes_full_opt_(size)" value=""/>
  <!-- Locking Annotations -->
  <define name="_Acquires_exclusive_lock_(expr)" value=""/>
  <define name="_Acquires_lock_(expr)" value=""/>
  <define name="_Acquires_nonreentrant_lock_(expr)" value=""/>
  <define name="_Acquires_shared_lock_(expr)" value=""/>
  <define name="_Create_lock_level_(name)" value=""/>
  <define name="_Has_lock_kind_(kind)" value=""/>
  <define name="_Has_lock_level_(name)" value=""/>
  <define name="_Lock_level_order_(name1, name2)" value=""/>
  <define name="_Post_same_lock_(expr1, expr2)" value=""/>
  <define name="_Releases_exclusive_lock_(expr)" value=""/>
  <define name="_Releases_lock_(expr)" value=""/>
  <define name="_Releases_nonreentrant_lock_(expr)" value=""/>
  <define name="_Releases_shared_lock_(expr)" value=""/>
  <define name="_Requires_lock_held_(expr)" value=""/>
  <define name="_Requires_lock_not_held_(expr)" value=""/>
  <define name="_Requires_no_locks_held_" value=""/>
  <define name="_Requires_shared_lock_held_(expr)" value=""/>
  <define name="_Requires_exclusive_lock_held_(expr)" value=""/>
  <define name="_Global_cancel_spin_lock_" value=""/>
  <define name="_Global_critical_region_" value=""/>
  <define name="_Global_interlock_" value=""/>
  <define name="_Global_priority_region_" value=""/>
  <define name="_Guarded_by_(expr)" value=""/>
  <define name="_Interlocked_" value=""/>
  <define name="_Interlocked_operand_" value=""/>
  <define name="_Write_guarded_by_(expr)" value=""/>
  <!-- When and Where Annotations -->
  <define name="_At_(expr, anno_list)" value=""/>
  <define name="_At_buffer_(expr, iter, elem_count, anno_list)" value=""/>
  <define name="_Group_(anno_list)" value=""/>
  <define name="_When_(expr, anno_list)" value=""/>
  <!-- Annotations not or poorly documented by MSDN (see sal.h) -->
  <!-- Format string parameters -->
  <define name="_Printf_format_string_" value=""/>
  <define name="_Scanf_format_string_" value=""/>
  <define name="_Scanf_s_format_string_" value=""/>
  <!-- Annotations for strict type checking -->
  <define name="_Points_to_data_" value=""/>
  <define name="_Literal_" value=""/>
  <define name="_Notliteral_" value=""/>
  <!-- Annotations for defensive programming -->
  <define name="_Pre_defensive_" value=""/>
  <define name="_Post_defensive_" value=""/>
  <define name="_In_defensive_(annotes)" value=""/>
  <define name="_Out_defensive_(annotes)" value=""/>
  <define name="_Inout_defensive_(annotes)" value=""/>
  <!-- _In_\_Out_ Layer -->
  <define name="_Reserved_" value=""/>
  <define name="_Const_" value=""/>
  <define name="_Unchanged_(e)" value=""/>
  <!-- Output Parameters -->
  <define name="_Outref_result_nullonfailure_" value=""/>
  <define name="_Result_nullonfailure_" value=""/>
  <define name="_Result_zeroonfailure_" value=""/>
  <!-- _Pre_\_Post_ Layer -->
  <define name="_Pre_" value=""/>
  <define name="_Post_" value=""/>
  <define name="_Valid_" value=""/>
  <define name="_Notvalid_" value=""/>
  <define name="_Maybevalid_" value=""/>
  <!-- Pointer null-ness properties -->
  <define name="_Null_" value=""/>
  <define name="_Notnull_" value=""/>
  <define name="_Maybenull_" value=""/>
  <!-- (old) Windows Header Annotations (see http://msdn.microsoft.com/en-us/library/windows/desktop/aa383701%28v=vs.85%29.aspx) -->
  <define name="__bcount(size)" value=""/>
  <define name="__bcount_opt(size)" value=""/>
  <define name="__deref_bcount(size)" value=""/>
  <define name="__deref_bcount_opt(size)" value=""/>
  <define name="__deref_ecount(size)" value=""/>
  <define name="__deref_ecount_opt(size)" value=""/>
  <define name="__deref_in" value=""/>
  <define name="__deref_in_bcount(size)" value=""/>
  <define name="__deref_in_bcount_opt(size)" value=""/>
  <define name="__deref_in_ecount(size)" value=""/>
  <define name="__deref_in_ecount_opt(size)" value=""/>
  <define name="__deref_in_opt" value=""/>
  <define name="__deref_inout" value=""/>
  <define name="__deref_inout_bcount(size)" value=""/>
  <define name="__deref_inout_bcount_full(size)" value=""/>
  <define name="__deref_inout_bcount_full_opt(size)" value=""/>
  <define name="__deref_inout_bcount_opt(size)" value=""/>
  <define name="__deref_inout_bcount_part(size,length)" value=""/>
  <define name="__deref_inout_bcount_part_opt(size,length)" value=""/>
  <define name="__deref_inout_ecount(size)" value=""/>
  <define name="__deref_inout_ecount_full(size)" value=""/>
  <define name="__deref_inout_ecount_full_opt(size)" value=""/>
  <define name="__deref_inout_ecount_opt(size)" value=""/>
  <define name="__deref_inout_ecount_part(size,length)" value=""/>
  <define name="__deref_inout_ecount_part_opt(size,length)" value=""/>
  <define name="__deref_inout_opt" value=""/>
  <define name="__deref_opt_bcount(size)" value=""/>
  <define name="__deref_opt_bcount_opt(size)" value=""/>
  <define name="__deref_opt_ecount(size)" value=""/>
  <define name="__deref_opt_ecount_opt(size)" value=""/>
  <define name="__deref_opt_in" value=""/>
  <define name="__deref_opt_in_bcount(size)" value=""/>
  <define name="__deref_opt_in_bcount_opt(size)" value=""/>
  <define name="__deref_opt_in_ecount(size)" value=""/>
  <define name="__deref_opt_in_ecount_opt(size)" value=""/>
  <define name="__deref_opt_in_opt" value=""/>
  <define name="__deref_opt_inout" value=""/>
  <define name="__deref_opt_inout_bcount(size)" value=""/>
  <define name="__deref_opt_inout_bcount_full(size)" value=""/>
  <define name="__deref_opt_inout_bcount_full_opt(size)" value=""/>
  <define name="__deref_opt_inout_bcount_opt(size)" value=""/>
  <define name="__deref_opt_inout_bcount_part(size,length)" value=""/>
  <define name="__deref_opt_inout_bcount_part_opt(size,length)" value=""/>
  <define name="__deref_opt_inout_ecount(size)" value=""/>
  <define name="__deref_opt_inout_ecount_full(size)" value=""/>
  <define name="__deref_opt_inout_ecount_full_opt(size)" value=""/>
  <define name="__deref_opt_inout_ecount_opt(size)" value=""/>
  <define name="__deref_opt_inout_ecount_part(size,length)" value=""/>
  <define name="__deref_opt_inout_ecount_part_opt(size,length)" value=""/>
  <define name="__deref_opt_inout_opt" value=""/>
  <define name="__deref_opt_out" value=""/>
  <define name="__deref_opt_out_bcount(size)" value=""/>
  <define name="__deref_opt_out_bcount_full(size)" value=""/>
  <define name="__deref_opt_out_bcount_full_opt(size)" value=""/>
  <define name="__deref_opt_out_bcount_opt(size)" value=""/>
  <define name="__deref_opt_out_bcount_part(size,length)" value=""/>
  <define name="__deref_opt_out_bcount_part_opt(size,length)" value=""/>
  <define name="__deref_opt_out_ecount(size)" value=""/>
  <define name="__deref_opt_out_ecount_full(size)" value=""/>
  <define name="__deref_opt_out_ecount_full_opt(size)" value=""/>
  <define name="__deref_opt_out_ecount_opt(size)" value=""/>
  <define name="__deref_opt_out_ecount_part(size,length)" value=""/>
  <define name="__deref_opt_out_ecount_part_opt(size,length)" value=""/>
  <define name="__deref_opt_out_opt" value=""/>
  <define name="__deref_out" value=""/>
  <define name="__deref_out_bcount(size)" value=""/>
  <define name="__deref_out_bcount_full(size)" value=""/>
  <define name="__deref_out_bcount_full_opt(size)" value=""/>
  <define name="__deref_out_bcount_opt(size)" value=""/>
  <define name="__deref_out_bcount_part(size,length)" value=""/>
  <define name="__deref_out_bcount_part_opt(size,length)" value=""/>
  <define name="__deref_out_ecount(size)" value=""/>
  <define name="__deref_out_ecount_full(size)" value=""/>
  <define name="__deref_out_ecount_full_opt(size)" value=""/>
  <define name="__deref_out_ecount_opt(size)" value=""/>
  <define name="__deref_out_ecount_part(size,length)" value=""/>
  <define name="__deref_out_ecount_part_opt(size,length)" value=""/>
  <define name="__deref_out_opt" value=""/>
  <define name="__ecount(size)" value=""/>
  <define name="__ecount_opt(size)" value=""/>
  <define name="__in" value=""/>
  <define name="__in_bcount(size)" value=""/>
  <define name="__in_bcount_opt(size)" value=""/>
  <define name="__in_ecount(size)" value=""/>
  <define name="__in_ecount_opt(size)" value=""/>
  <define name="__in_nz" value=""/>
  <define name="__in_nz_opt" value=""/>
  <define name="__in_opt" value=""/>
  <define name="__in_z" value=""/>
  <define name="__in_z_opt" value=""/>
  <define name="__inout" value=""/>
  <define name="__inout_bcount(size)" value=""/>
  <define name="__inout_bcount_full(size)" value=""/>
  <define name="__inout_bcount_full_opt(size)" value=""/>
  <define name="__inout_bcount_opt(size)" value=""/>
  <define name="__inout_bcount_part(size,length)" value=""/>
  <define name="__inout_bcount_part_opt(size,length)" value=""/>
  <define name="__inout_ecount(size)" value=""/>
  <define name="__inout_ecount_full(size)" value=""/>
  <define name="__inout_ecount_full_opt(size)" value=""/>
  <define name="__inout_ecount_opt(size)" value=""/>
  <define name="__inout_ecount_part(size,length)" value=""/>
  <define name="__inout_ecount_part_opt(size,length)" value=""/>
  <define name="__inout_opt" value=""/>
  <define name="__inout_nz" value=""/>
  <define name="__inout_z" value=""/>
  <define name="__out" value=""/>
  <define name="__out_bcount(size)" value=""/>
  <define name="__out_bcount_full(size)" value=""/>
  <define name="__out_bcount_full_opt(size)" value=""/>
  <define name="__out_bcount_opt(size)" value=""/>
  <define name="__out_bcount_part(size,length)" value=""/>
  <define name="__out_bcount_part_opt(size,length)" value=""/>
  <define name="__out_ecount(size)" value=""/>
  <define name="__out_ecount_full(size)" value=""/>
  <define name="__out_ecount_full_opt(size)" value=""/>
  <define name="__out_ecount_opt(size)" value=""/>
  <define name="__out_ecount_part(size,length)" value=""/>
  <define name="__out_ecount_part_opt(size,length)" value=""/>
  <define name="__out_opt" value=""/>
  <define name="__out_nz" value=""/>
  <define name="__out_nz_opt" value=""/>
  <define name="__out_z" value=""/>
  <define name="__out_z_opt" value=""/>
  <define name="__format_string" value=""/>
  <define name="__blocksOn(resource)" value=""/>
  <define name="__callback" value=""/>
  <define name="__checkReturn" value=""/>
  <define name="__in_awcount(expr,size)" value=""/>
  <define name="__nullnullterminated" value=""/>
  <define name="__nullterminated" value=""/>
  <define name="__out_awcount(expr,size)" value=""/>
  <define name="__override" value=""/>
  <define name="__reserved" value=""/>
  <define name="__success(expr)" value=""/>
  <define name="__typefix(ctype)" value=""/>
  <!-- RPC SAL Annotations (rpcsal.h) -->
  <define name="__RPC__in" value=""/>
  <define name="__RPC__in_string" value=""/>
  <define name="__RPC__in_ecount(size)" value=""/>
  <define name="__RPC__in_ecount_full(size)" value=""/>
  <define name="__RPC__in_ecount_full_string(size)" value=""/>
  <define name="__RPC__in_ecount_part(size, length)" value=""/>
  <define name="__RPC__in_xcount(size)" value=""/>
  <define name="__RPC__in_xcount_full(size)" value=""/>
  <define name="__RPC__in_xcount_full_string(size)" value=""/>
  <define name="__RPC__in_xcount_part(size, length)" value=""/>
  <define name="__RPC__deref_in" value=""/>
  <define name="__RPC__deref_in_string" value=""/>
  <define name="__RPC__deref_in_opt" value=""/>
  <define name="__RPC__deref_in_opt_string" value=""/>
  <define name="__RPC__deref_opt_in" value=""/>
  <define name="__RPC__deref_opt_in_string" value=""/>
  <define name="__RPC__deref_opt_in_opt" value=""/>
  <define name="__RPC__deref_opt_in_opt_string" value=""/>
  <define name="__RPC__deref_in_ecount(size)" value=""/>
  <define name="__RPC__deref_in_ecount_part(size, length)" value=""/>
  <define name="__RPC__deref_in_ecount_full(size)" value=""/>
  <define name="__RPC__deref_in_ecount_full_opt(size)" value=""/>
  <define name="__RPC__deref_in_ecount_full_opt_string(size)" value=""/>
  <define name="__RPC__deref_in_ecount_full_string(size)" value=""/>
  <define name="__RPC__deref_in_ecount_opt(size)" value=""/>
  <define name="__RPC__deref_in_ecount_opt_string(size)" value=""/>
  <define name="__RPC__deref_in_ecount_part_opt(size, length)" value=""/>
  <define name="__RPC__deref_in_xcount(size)" value=""/>
  <define name="__RPC__deref_in_xcount_part(size, length)" value=""/>
  <define name="__RPC__deref_in_xcount_full(size)" value=""/>
  <define name="__RPC__deref_in_xcount_full_opt(size)" value=""/>
  <define name="__RPC__deref_in_xcount_full_opt_string(size)" value=""/>
  <define name="__RPC__deref_in_xcount_full_string(size)" value=""/>
  <define name="__RPC__deref_in_xcount_opt(size)" value=""/>
  <define name="__RPC__deref_in_xcount_opt_string(size)" value=""/>
  <define name="__RPC__deref_in_xcount_part_opt(size, length)" value=""/>
  <define name="__RPC__out" value=""/>
  <define name="__RPC__out_ecount(size)" value=""/>
  <define name="__RPC__out_ecount_string(size)" value=""/>
  <define name="__RPC__out_ecount_part(size, length)" value=""/>
  <define name="__RPC__out_ecount_full(size)" value=""/>
  <define name="__RPC__out_ecount_full_string(size)" value=""/>
  <define name="__RPC__out_xcount(size)" value=""/>
  <define name="__RPC__out_xcount_string(size)" value=""/>
  <define name="__RPC__out_xcount_part(size, length)" value=""/>
  <define name="__RPC__out_xcount_full(size)" value=""/>
  <define name="__RPC__out_xcount_full_string(size)" value=""/>
  <define name="__RPC__inout" value=""/>
  <define name="__RPC__inout_string" value=""/>
  <define name="__RPC__inout_ecount(size)" value=""/>
  <define name="__RPC__inout_ecount_part(size, length)" value=""/>
  <define name="__RPC__inout_ecount_full(size)" value=""/>
  <define name="__RPC__inout_ecount_full_string(size)" value=""/>
  <define name="__RPC__inout_xcount(size)" value=""/>
  <define name="__RPC__inout_xcount_part(size, length)" value=""/>
  <define name="__RPC__inout_xcount_full(size)" value=""/>
  <define name="__RPC__inout_xcount_full_string(size)" value=""/>
  <define name="__RPC__in_opt" value=""/>
  <define name="__RPC__in_opt_string" value=""/>
  <define name="__RPC__in_ecount_opt(size)" value=""/>
  <define name="__RPC__in_ecount_opt_string(size)" value=""/>
  <define name="__RPC__in_ecount_full_opt(size)" value=""/>
  <define name="__RPC__in_ecount_full_opt_string(size)" value=""/>
  <define name="__RPC__in_ecount_part_opt(size, length)" value=""/>
  <define name="__RPC__in_xcount_full_opt(size)" value=""/>
  <define name="__RPC__in_xcount_full_opt_string(size)" value=""/>
  <define name="__RPC__in_xcount_part_opt(size, length)" value=""/>
  <define name="__RPC__in_xcount_opt(size)" value=""/>
  <define name="__RPC__in_xcount_opt_string(size)" value=""/>
  <define name="__RPC__inout_opt" value=""/>
  <define name="__RPC__inout_opt_string" value=""/>
  <define name="__RPC__inout_ecount_opt(size)" value=""/>
  <define name="__RPC__inout_ecount_part_opt(size, length)" value=""/>
  <define name="__RPC__inout_ecount_full_opt(size)" value=""/>
  <define name="__RPC__inout_ecount_full_opt_string(size)" value=""/>
  <define name="__RPC__inout_xcount_opt(size)" value=""/>
  <define name="__RPC__inout_xcount_part_opt(size, length)" value=""/>
  <define name="__RPC__inout_xcount_full_opt(size)" value=""/>
  <define name="__RPC__inout_xcount_full_opt_string(size)" value=""/>
  <define name="__RPC__deref_out" value=""/>
  <define name="__RPC__deref_out_string" value=""/>
  <define name="__RPC__deref_out_opt" value=""/>
  <define name="__RPC__deref_out_opt_string" value=""/>
  <define name="__RPC__deref_out_ecount(size)" value=""/>
  <define name="__RPC__deref_out_ecount_part(size, length)" value=""/>
  <define name="__RPC__deref_out_ecount_full(size)" value=""/>
  <define name="__RPC__deref_out_ecount_full_string(size)" value=""/>
  <define name="__RPC__deref_out_xcount(size)" value=""/>
  <define name="__RPC__deref_out_xcount_part(size, length)" value=""/>
  <define name="__RPC__deref_out_xcount_full(size)" value=""/>
  <define name="__RPC__deref_out_xcount_full_string(size)" value=""/>
  <define name="__RPC__deref_inout" value=""/>
  <define name="__RPC__deref_inout_string" value=""/>
  <define name="__RPC__deref_inout_opt" value=""/>
  <define name="__RPC__deref_inout_opt_string" value=""/>
  <define name="__RPC__deref_inout_ecount_opt(size)" value=""/>
  <define name="__RPC__deref_inout_ecount_part_opt(size, length)" value=""/>
  <define name="__RPC__deref_inout_ecount_full_opt(size)" value=""/>
  <define name="__RPC__deref_inout_ecount_full(size)" value=""/>
  <define name="__RPC__deref_inout_ecount_full_string(size)" value=""/>
  <define name="__RPC__deref_inout_ecount_full_opt_string(size)" value=""/>
  <define name="__RPC__deref_inout_xcount_opt(size)" value=""/>
  <define name="__RPC__deref_inout_xcount_part_opt(size, length)" value=""/>
  <define name="__RPC__deref_inout_xcount_full_opt(size)" value=""/>
  <define name="__RPC__deref_inout_xcount_full(size)" value=""/>
  <define name="__RPC__deref_inout_xcount_full_string(size)" value=""/>
  <define name="__RPC__deref_inout_xcount_full_opt_string(size)" value=""/>
  <define name="__RPC__deref_opt_inout" value=""/>
  <define name="__RPC__deref_opt_inout_ecount(size)" value=""/>
  <define name="__RPC__deref_opt_inout_string" value=""/>
  <define name="__RPC__deref_opt_inout_ecount_part(size, length)" value=""/>
  <define name="__RPC__deref_opt_inout_ecount_full(size)" value=""/>
  <define name="__RPC__deref_opt_inout_ecount_full_string(size)" value=""/>
  <define name="__RPC__deref_opt_inout_xcount_part(size, length)" value=""/>
  <define name="__RPC__deref_opt_inout_xcount_full(size)" value=""/>
  <define name="__RPC__deref_opt_inout_xcount_full_string(size)" value=""/>
  <define name="__RPC__deref_out_ecount_opt(size)" value=""/>
  <define name="__RPC__deref_out_ecount_part_opt(size, length)" value=""/>
  <define name="__RPC__deref_out_ecount_full_opt(size)" value=""/>
  <define name="__RPC__deref_out_ecount_full_opt_string(size)" value=""/>
  <define name="__RPC__deref_out_xcount_opt(size)" value=""/>
  <define name="__RPC__deref_out_xcount_part_opt(size, length)" value=""/>
  <define name="__RPC__deref_out_xcount_full_opt(size)" value=""/>
  <define name="__RPC__deref_out_xcount_full_opt_string(size)" value=""/>
  <define name="__RPC__deref_opt_inout_opt" value=""/>
  <define name="__RPC__deref_opt_inout_opt_string" value=""/>
  <define name="__RPC__deref_opt_inout_ecount_opt(size)" value=""/>
  <define name="__RPC__deref_opt_inout_ecount_part_opt(size, length)" value=""/>
  <define name="__RPC__deref_opt_inout_ecount_full_opt(size)" value=""/>
  <define name="__RPC__deref_opt_inout_ecount_full_opt_string(size)" value=""/>
  <define name="__RPC__deref_opt_inout_xcount_opt(size)" value=""/>
  <define name="__RPC__deref_opt_inout_xcount_part_opt(size, length)" value=""/>
  <define name="__RPC__deref_opt_inout_xcount_full_opt(size)" value=""/>
  <define name="__RPC__deref_opt_inout_xcount_full_opt_string(size)" value=""/>
  <define name="__RPC_full_pointer" value=""/>
  <define name="__RPC_unique_pointer" value=""/>
  <define name="__RPC_ref_pointer" value=""/>
  <define name="__RPC_string" value=""/>
  <define name="__RPC__range(min,max)" value=""/>
  <define name="__RPC__in_range(min,max)" value=""/>
</def>
