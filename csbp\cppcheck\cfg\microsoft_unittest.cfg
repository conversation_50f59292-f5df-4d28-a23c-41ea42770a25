<?xml version="1.0"?>
<def format="2">
  <define name="TEST_CLASS(className)" value="class className : public ::Microsoft::VisualStudio::CppUnitTestFramework::TestClass&lt;className&gt;"/>
  <define name="TEST_METHOD(methodName)" value="void methodName()"/>
  <define name="TEST_MODULE_INITIALIZE(methodName)" value="void methodName()"/>
  <define name="TEST_MODULE_CLEANUP(methodName)" value="void methodName()"/>
  <define name="TEST_CLASS_INITIALIZE(methodName)" value="static void methodName()"/>
  <define name="TEST_CLASS_CLEANUP(methodName)" value="static void methodName()"/>
  <define name="TEST_METHOD_INITIALIZE(methodName)" value="void methodName()"/>
  <define name="TEST_METHOD_CLEANUP(methodName)" value="void methodName()"/>
  <define name="BEGIN_TEST_CLASS_ATTRIBUTE()" value=""/>
  <define name="TEST_CLASS_ATTRIBUTE(attributeName, attributeValue)" value=""/>
  <define name="END_TEST_CLASS_ATTRIBUTE()" value=""/>
  <define name="BEGIN_TEST_METHOD_ATTRIBUTE(methodName)" value=""/>
  <define name="TEST_METHOD_ATTRIBUTE(attributeName, attributeValue)" value=""/>
  <define name="END_TEST_METHOD_ATTRIBUTE()" value=""/>
  <define name="BEGIN_TEST_MODULE_ATTRIBUTE()" value=""/>
  <define name="TEST_MODULE_ATTRIBUTE(attributeName, attributeValue)" value=""/>
  <define name="END_TEST_MODULE_ATTRIBUTE()" value=""/>
</def>
