<?xml version="1.0"?>
<def format="2">
  <function name="MrmCloseHierarchy">
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <function name="MrmOpenHierarchy">
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="4"/>
  </function>
  <function name="MrmOpenHierarchyPerDisplay">
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="5"/>
  </function>
  <function name="XmFontListEntryFree">
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <function name="XmFontListCreate,XmFontListAppendEntry">
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
  </function>
  <function name="XmStringCreateLocalized">
    <noreturn>false</noreturn>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <function name="XmStringCreateSimple">
    <noreturn>false</noreturn>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <function name="XmStringFree">
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <function name="XmStringGenerate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="4"/>
  </function>
  <function name="XmTextGetString,XmTextGetStringWcs">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <resource>
    <dealloc>MrmCloseHierarchy</dealloc>
    <alloc init="true">MrmOpenHierarchy</alloc>
    <alloc init="true">MrmOpenHierarchyPerDisplay</alloc>
  </resource>
  <!-- May cause false positives:
  <resource>
    <dealloc>XmClipboardUnlock</dealloc>
    <alloc>XmClipboardLock</alloc>
  </resource>
  -->
  <resource>
    <dealloc>XmStringFree</dealloc>
    <alloc init="true">XmStringCreateLocalized</alloc>
    <alloc init="true">XmStringCreateSimple</alloc>
    <alloc init="true">XmStringGenerate</alloc>
    <alloc>XmCvtCTToXmString</alloc>
  </resource>
  <resource>
    <dealloc>XtFree</dealloc>
    <alloc>XmFontListEntryGetTag</alloc>
    <alloc>XmTextGetString</alloc>
    <alloc>XmCvtXmStringToCT</alloc>
    <alloc>XmWidgetGetBaselines</alloc>
  </resource>
  <resource>
    <dealloc>XmFontListEntryFree</dealloc>
    <alloc init="true">XmFontListCreate</alloc>
    <alloc init="true">XmFontListAppendEntry</alloc>
  </resource>
  <!-- Intrinsics, not Motif... -->
  <function name="XtAddCallback">
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
    <arg nr="4"/>
  </function>
  <function name="XtAddCallbacks">
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
    <arg nr="3"/>
  </function>
  <function name="XtAsprintf">
    <leak-ignore/>
    <returnValue type="Cardinal"/>
    <arg nr="1"/>
    <arg nr="2">
      <formatstr/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <function name="XtCalloc">
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
  </function>
  <function name="XtDisplay,XtDisplayOfObject">
    <leak-ignore/>
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="Display *"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <function name="XtFree">
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- int XtSetArg(Arg arg, String name, XtArgVal value);  -->
  <function name="XtSetArg">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1"/>
    <arg nr="2">
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3">
      <not-uninit/>
    </arg>
  </function>
  <function name="XtSetValues,XtGetValues">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="3"/>
  </function>
  <function name="XtSetSubvalues,XtGetSubvalues">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="5"/>
  </function>
  <function name="XtMalloc,XtNew,XtNewString">
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <function name="XtRemoveAllCallbacks">
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
  </function>
  <function name="XtRemoveCallback">
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
    <arg nr="4"/>
  </function>
  <function name="XtRemoveCallbacks">
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
    <arg nr="3"/>
  </function>
  <function name="XtWindow,XtWindowOfObject">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="Window"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <function name="XtVaGetValues,XtVaSetValues">
    <leak-ignore/>
    <use-retval/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <resource>
    <dealloc>XtFree</dealloc>
    <alloc init="true">XtMalloc</alloc>
    <alloc init="true">XtCalloc</alloc>
    <alloc init="true">XtRealloc</alloc>
    <alloc init="true">XtNew</alloc>
    <alloc init="true">XtNewString</alloc>
  </resource>
  <define name="ClipboardFail" value="0"/>
  <define name="ClipboardSuccess" value="1"/>
  <define name="True" value="1"/>
  <define name="False" value="0"/>
  <podtype name="Boolean" sign="u"/>
  <podtype name="Cardinal" sign="u"/>
  <podtype name="Dimension" sign="u"/>
  <podtype name="Pixel" sign="u"/>
  <podtype name="Position" sign="u"/>
  <podtype name="Widget"/>
  <podtype name="XtValueMask" sign="u"/>
  <podtype name="XtIntervalId" sign="u"/>
  <podtype name="XtInputId" sign="u"/>
  <podtype name="XtSignalId" sign="u"/>
  <podtype name="XtGeometryMask" sign="u"/>
  <podtype name="XtGCMask" sign="u"/>
  <!-- X11... -->
  <function name="XOpenDisplay">
    <use-retval/>
    <noreturn>false</noreturn>
    <returnValue type="Display *"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <function name="XCloseDisplay">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- https://www.x.org/releases/X11R7.7/doc/libX11/libX11/libX11.html#id2656808 -->
  <!-- XFree(void *data); -->
  <function name="XFree">
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <function name="XtDatabase">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <function name="XtScreen,XtScreenOfObject">
    <noreturn>false</noreturn>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <function name="XtScreenDatabase">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <resource>
    <alloc init="true">XOpenDisplay</alloc>
    <dealloc>XCloseDisplay</dealloc>
  </resource>
  <podtype name="XrmDatabase"/>
</def>
