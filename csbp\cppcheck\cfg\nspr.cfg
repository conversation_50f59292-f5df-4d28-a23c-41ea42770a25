<?xml version="1.0"?>
<!-- Netscape Portable Runtime (NSPR) provides a platform-neutral API for system level and libc-like functions. 
     The API is used in the Mozilla clients, many of Red Hat's and Oracle's server applications, and other software offerings.
     Ref.: 
      https://developer.mozilla.org/en-US/docs/Mozilla/Projects/NSPR 
-->
<def>
  <define name="PR_EXPORT(__type)" value=""/>
  <define name="PR_EXPORT_DATA(__type)" value=""/>
  <define name="PR_IMPORT(__type)" value=""/>
  <define name="PR_IMPORT_DATA(__type)" value=""/>
  <define name="PR_EXTERN(__type)" value=""/>
  <define name="PR_IMPLEMENT(__type)" value=""/>
  <define name="PR_EXTERN_DATA(__type)" value=""/>
  <define name="PR_IMPLEMENT_DATA(__type)" value=""/>
  <define name="PR_STATIC_CALLBACK(__x)" value=""/>
  <define name="PR_BIT(n)" value="((PRUint32)1 &lt;&lt; (n))"/>
  <define name="PR_BITMASK(n)" value="(PR_BIT(n) - 1)"/>
  <define name="PR_ROUNDUP(x,y)" value="((((x)+((y)-1))/(y))*(y))"/>
  <define name="PR_MIN(x,y)" value="((x)&lt;(y)?(x):(y))"/>
  <define name="PR_MAX(x,y)" value="((x)&gt;(y)?(x):(y))"/>
  <define name="PR_ABS(x)" value="((x)&lt;0?-(x):(x))"/>
  <define name="PR_ARRAY_SIZE(a)" value="(sizeof(a)/sizeof((a)[0]))"/>
  <define name="PR_INT16_MAX" value="32767"/>
  <define name="PR_INT16_MIN" value="(-32768)"/>
  <define name="PR_UINT16_MAX" value="65535U"/>
  <define name="PR_INT8_MAX" value="127"/>
  <define name="PR_INT8_MIN" value="(-128)"/>
  <define name="PR_UINT8_MAX" value="255U"/>
  <podtype name="PRUint8" sign="u" size="1"/>
  <podtype name="PRInt8" sign="s" size="1"/>
  <podtype name="PRUint16" sign="u" size="2"/>
  <podtype name="PRInt16" sign="s" size="2"/>
  <podtype name="PRUint32" sign="u" size="4"/>
  <podtype name="PRInt32" sign="s" size="4"/>
  <podtype name="PRUint64" sign="u" size="8"/>
  <podtype name="PRInt64" sign="s" size="8"/>
</def>
