<?xml version="1.0"?>
<def format="2">
  <!-- void Vec::AdjustAlloc( long n ) -->
  <function name="Vec::AdjustAlloc">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void Vec::AdjustLength( long n ) -->
  <function name="Vec::AdjustLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void Vec::AdjustMaxLength( long n ) -->
  <function name="Vec::AdjustMaxLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void Vec::AllocateTo( long n ) -->
  <function name="Vec::AllocateTo">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void Vec::BlockConstruct( T * p, long n ) -->
  <function name="Vec::BlockConstruct">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void Vec::BlockConstructFromObj( T * p, long n, const T & q ) -->
  <function name="Vec::BlockConstructFromObj">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void Vec::BlockConstructFromVec( T * p, long n, const T * q ) -->
  <function name="Vec::BlockConstructFromVec">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void Vec::BlockDestroy( T * p, long n ) -->
  <function name="Vec::BlockDestroy">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void Vec::DoSetLengthAndApply( long n, F & f ) -->
  <function name="Vec::DoSetLengthAndApply">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- void Vec::FixAtCurrentLength( void ) -->
  <function name="Vec::FixAtCurrentLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void Vec::FixLength( long n ) -->
  <function name="Vec::FixLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void Vec::InitAndApply( long n, F & f ) -->
  <function name="Vec::InitAndApply">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- long Vec::MaxLength( void ) -->
  <function name="Vec::MaxLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- void Vec::QuickSetLength( long n ) -->
  <function name="Vec::QuickSetLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void Vec::SetLengthAndApply( long n, F f ) -->
  <function name="Vec::SetLengthAndApply">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void Vec::SetMaxLength( long n ) -->
  <function name="Vec::SetMaxLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void Vec::_vec_deleter::apply( T * p ) -->
  <function name="Vec::_vec_deleter::apply">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- long Vec::allocated( void ) -->
  <function name="Vec::allocated">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- long Vec::fixed( void ) -->
  <function name="Vec::fixed">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- void Vec::kill( void ) -->
  <function name="Vec::kill">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- long Vec::length( void ) -->
  <function name="Vec::length">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- long Vec::position( const T & a ) const -->
  <function name="Vec::position">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- long Vec::position1( const T & a ) const -->
  <function name="Vec::position1">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void VecStrategy::do_BlockConstruct( T * p, long n ) -->
  <function name="VecStrategy::do_BlockConstruct">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void VecStrategy::do_BlockConstruct( T * p, long n ) -->
  <function name="VecStrategy::do_BlockConstruct">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void VecStrategy::do_BlockConstructFromObj( T * p, long n, const T & q ) -->
  <function name="VecStrategy::do_BlockConstructFromObj">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void VecStrategy::do_BlockConstructFromObj( T * p, long n, const T & q ) -->
  <function name="VecStrategy::do_BlockConstructFromObj">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void VecStrategy::do_BlockConstructFromVec( T * p, long n, const T * q ) -->
  <function name="VecStrategy::do_BlockConstructFromVec">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void VecStrategy::do_BlockConstructFromVec( T * p, long n, const T * q ) -->
  <function name="VecStrategy::do_BlockConstructFromVec">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void VecStrategy::do_BlockDestroy( T * p, long n ) -->
  <function name="VecStrategy::do_BlockDestroy">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void VecStrategy::do_BlockDestroy( T * p, long n ) -->
  <function name="VecStrategy::do_BlockDestroy">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long mat_ZZ_p_opaque::NumCols( void ) -->
  <function name="mat_ZZ_p_opaque::NumCols">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- long mat_ZZ_p_opaque::NumRows( void ) -->
  <function name="mat_ZZ_p_opaque::NumRows">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- bool mat_ZZ_p_opaque::initialized( void ) -->
  <function name="mat_ZZ_p_opaque::initialized">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- void mat_ZZ_p_opaque::move( mat_ZZ_p & A ) -->
  <function name="mat_ZZ_p_opaque::move">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- long LazyTable::Builder::amt( void ) -->
  <function name="LazyTable::Builder::amt">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- void LazyTable::Builder::move( UniquePtr<T> & p ) -->
  <function name="LazyTable::Builder::move">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- long LazyTable::length( void ) -->
  <function name="LazyTable::length">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- long xdouble::OutputPrecision( void ) -->
  <function name="xdouble::OutputPrecision">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- void xdouble::SetOutputPrecision( long p ) -->
  <function name="xdouble::SetOutputPrecision">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long xdouble::exponent( void ) -->
  <function name="xdouble::exponent">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- double xdouble::mantissa( void ) -->
  <function name="xdouble::mantissa">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="double"/>
    <use-retval/>
  </function>
  <!-- void xdouble::normalize( void ) -->
  <function name="xdouble::normalize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void GF2EX::SetLength( long n ) -->
  <function name="GF2EX::SetLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GF2EX::SetMaxLength( long n ) -->
  <function name="GF2EX::SetMaxLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GF2EX::kill( void ) -->
  <function name="GF2EX::kill">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void GF2EX::normalize( void ) -->
  <function name="GF2EX::normalize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void GF2EX::swap( GF2EX & x ) -->
  <function name="GF2EX::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- long ZZVec::BaseSize( void ) -->
  <function name="ZZVec::BaseSize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- void ZZVec::SetSize( long n, long d ) -->
  <function name="ZZVec::SetSize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void ZZVec::kill( void ) -->
  <function name="ZZVec::kill">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- long ZZVec::length( void ) -->
  <function name="ZZVec::length">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- void ZZVec::move( ZZVec & other ) -->
  <function name="ZZVec::move">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void ZZVec::swap( ZZVec & x ) -->
  <function name="ZZVec::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void WordVector::DoSetLength( long n ) -->
  <function name="WordVector::DoSetLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void WordVector::KillBig( void ) -->
  <function name="WordVector::KillBig">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- long WordVector::MaxLength( void ) -->
  <function name="WordVector::MaxLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- void WordVector::QuickSetLength( long n ) -->
  <function name="WordVector::QuickSetLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void WordVector::SetLength( long n ) -->
  <function name="WordVector::SetLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void WordVector::SetMaxLength( long n ) -->
  <function name="WordVector::SetMaxLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void WordVector::ZeroLength( void ) -->
  <function name="WordVector::ZeroLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- const _ntl_ulong * WordVector::elts( void ) -->
  <function name="WordVector::elts">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const _ntl_ulong *"/>
    <use-retval/>
  </function>
  <!-- _ntl_ulong * WordVector::elts( void ) -->
  <function name="WordVector::elts">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="_ntl_ulong *"/>
    <use-retval/>
  </function>
  <!-- void WordVector::kill( void ) -->
  <function name="WordVector::kill">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- long WordVector::length( void ) -->
  <function name="WordVector::length">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- bool WordVector::pinned( void ) -->
  <function name="WordVector::pinned">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- void WordVector::swap( WordVector & y ) -->
  <function name="WordVector::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void WordVector::unpinned_move( WordVector & other ) -->
  <function name="WordVector::unpinned_move">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void WordVector::unpinned_swap( WordVector & other ) -->
  <function name="WordVector::unpinned_swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void DummyScopeGuard::relax( void ) -->
  <function name="DummyScopeGuard::relax">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void WrappedPtr::kill( void ) -->
  <function name="WrappedPtr::kill">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void WrappedPtr::move( WrappedPtr & other ) -->
  <function name="WrappedPtr::move">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void WrappedPtr::swap( WrappedPtr & other ) -->
  <function name="WrappedPtr::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void scope_guard::relax( void ) -->
  <function name="scope_guard::relax">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void FileList::AddFile( const char * name ) -->
  <function name="FileList::AddFile">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void FileList::RemoveLast( void ) -->
  <function name="FileList::RemoveLast">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void zz_pEX::SetLength( long n ) -->
  <function name="zz_pEX::SetLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void zz_pEX::SetMaxLength( long n ) -->
  <function name="zz_pEX::SetMaxLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void zz_pEX::kill( void ) -->
  <function name="zz_pEX::kill">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void zz_pEX::normalize( void ) -->
  <function name="zz_pEX::normalize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void zz_pEX::swap( zz_pEX & x ) -->
  <function name="zz_pEX::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- const zz_pEX & zz_pEX::zero( void ) -->
  <function name="zz_pEX::zero">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const zz_pEX &amp;"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- const zz_pEX & zz_pEXModulus::val( void ) -->
  <function name="zz_pEXModulus::val">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const zz_pEX &amp;"/>
    <use-retval/>
  </function>
  <!-- void AlignedArray::SetLength( long n ) -->
  <function name="AlignedArray::SetLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool AlignedArray::cannot_compare_these_types( void ) -->
  <function name="AlignedArray::cannot_compare_these_types">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- void AlignedArray::move( AlignedArray & other ) -->
  <function name="AlignedArray::move">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- char * AlignedArray::release( void ) -->
  <function name="AlignedArray::release">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="char *"/>
    <use-retval/>
  </function>
  <!-- void AlignedArray::swap( AlignedArray & other ) -->
  <function name="AlignedArray::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void CloneablePtr::AddRef( void ) -->
  <function name="CloneablePtr::AddRef">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void CloneablePtr::RemoveRef( void ) -->
  <function name="CloneablePtr::RemoveRef">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- CloneablePtr CloneablePtr::clone( void ) -->
  <function name="CloneablePtr::clone">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="CloneablePtr"/>
    <use-retval/>
  </function>
  <!-- long CloneablePtr::get_count( void ) -->
  <function name="CloneablePtr::get_count">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- void CloneablePtr::swap( CloneablePtr & other ) -->
  <function name="CloneablePtr::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- CloneablePtrControl * CloneablePtrControlDerived::clone( void ) -->
  <function name="CloneablePtrControlDerived::clone">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="CloneablePtrControl *"/>
    <use-retval/>
  </function>
  <!-- void * CloneablePtrControlDerived::get( void ) -->
  <function name="CloneablePtrControlDerived::get">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void *"/>
    <use-retval/>
  </function>
  <!-- bool CopiedPtr::cannot_compare_these_types( void ) -->
  <function name="CopiedPtr::cannot_compare_these_types">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- void CopiedPtr::make( void ) -->
  <function name="CopiedPtr::make">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void CopiedPtr::move( CopiedPtr & other ) -->
  <function name="CopiedPtr::move">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void CopiedPtr::reset( T * p = 0 ) -->
  <function name="CopiedPtr::reset">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" default="0" direction="inout"/>
  </function>
  <!-- void CopiedPtr::swap( CopiedPtr & other ) -->
  <function name="CopiedPtr::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void DefaultDeleterPolicy::deleter( T * p ) -->
  <function name="DefaultDeleterPolicy::deleter">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- CloneablePtrControl * MakeCloneableAux::clone( void ) -->
  <function name="MakeCloneableAux::clone">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="CloneablePtrControl *"/>
    <use-retval/>
  </function>
  <!-- void * MakeCloneableAux::get( void ) -->
  <function name="MakeCloneableAux::get">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void *"/>
    <use-retval/>
  </function>
  <!-- bool OptionalVal::exists( void ) -->
  <function name="OptionalVal::exists">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- void OptionalVal::make( void ) -->
  <function name="OptionalVal::make">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void OptionalVal::move( OptionalVal & other ) -->
  <function name="OptionalVal::move">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void OptionalVal::reset( T * p = 0 ) -->
  <function name="OptionalVal::reset">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" default="0" direction="inout"/>
  </function>
  <!-- void OptionalVal::swap( OptionalVal & other ) -->
  <function name="OptionalVal::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void SmartPtr::AddRef( void ) -->
  <function name="SmartPtr::AddRef">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void SmartPtr::RemoveRef( void ) -->
  <function name="SmartPtr::RemoveRef">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- long SmartPtr::get_count( void ) -->
  <function name="SmartPtr::get_count">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- void SmartPtr::swap( SmartPtr & other ) -->
  <function name="SmartPtr::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void Unique2DArray::SetDims( long n, long m ) -->
  <function name="Unique2DArray::SetDims">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void Unique2DArray::SetDimsFrom1( long n, long m ) -->
  <function name="Unique2DArray::SetDimsFrom1">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void Unique2DArray::SetLength( long n ) -->
  <function name="Unique2DArray::SetLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool Unique2DArray::cannot_compare_these_types( void ) -->
  <function name="Unique2DArray::cannot_compare_these_types">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- T_ptr * Unique2DArray::get( void ) -->
  <function name="Unique2DArray::get">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="T_ptr *"/>
    <use-retval/>
  </function>
  <!-- void Unique2DArray::move( Unique2DArray & other ) -->
  <function name="Unique2DArray::move">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- T_ptr * Unique2DArray::release( void ) -->
  <function name="Unique2DArray::release">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="T_ptr *"/>
    <use-retval/>
  </function>
  <!-- void Unique2DArray::reset( void ) -->
  <function name="Unique2DArray::reset">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void Unique2DArray::swap( Unique2DArray & other ) -->
  <function name="Unique2DArray::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void UniqueArray::SetLength( long n ) -->
  <function name="UniqueArray::SetLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool UniqueArray::cannot_compare_these_types( void ) -->
  <function name="UniqueArray::cannot_compare_these_types">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- void UniqueArray::move( UniqueArray & other ) -->
  <function name="UniqueArray::move">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void UniqueArray::reset( T * p = 0 ) -->
  <function name="UniqueArray::reset">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" default="0" direction="inout"/>
  </function>
  <!-- void UniqueArray::swap( UniqueArray & other ) -->
  <function name="UniqueArray::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- bool UniquePtr::cannot_compare_these_types( void ) -->
  <function name="UniquePtr::cannot_compare_these_types">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- void UniquePtr::make( void ) -->
  <function name="UniquePtr::make">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void UniquePtr::move( UniquePtr & other ) -->
  <function name="UniquePtr::move">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void UniquePtr::reset( T * p = 0 ) -->
  <function name="UniquePtr::reset">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" default="0" direction="inout"/>
  </function>
  <!-- void UniquePtr::swap( UniquePtr & other ) -->
  <function name="UniquePtr::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- double MatPrime_crt_helper::GetCost( void ) -->
  <function name="MatPrime_crt_helper::GetCost">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="double"/>
    <use-retval/>
  </function>
  <!-- double MatPrime_crt_helper::GetCost( void ) -->
  <function name="MatPrime_crt_helper::GetCost">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="double"/>
    <use-retval/>
  </function>
  <!-- long MatPrime_crt_helper::GetNumPrimes( void ) -->
  <function name="MatPrime_crt_helper::GetNumPrimes">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- long MatPrime_crt_helper::GetNumPrimes( void ) -->
  <function name="MatPrime_crt_helper::GetNumPrimes">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- void FFTMulTabsDeleterPolicy::deleter( FFTMulTabs * p ) -->
  <function name="FFTMulTabsDeleterPolicy::deleter">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void BasicThreadPool::CompositeSignal::send( T _val, T1 _val1 ) -->
  <function name="BasicThreadPool::CompositeSignal::send">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- T BasicThreadPool::CompositeSignal::wait( T1 & _val1 ) -->
  <function name="BasicThreadPool::CompositeSignal::wait">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="T"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- BasicThreadPool * BasicThreadPool::ConcurrentTask::getBasicThreadPool( void ) -->
  <function name="BasicThreadPool::ConcurrentTask::getBasicThreadPool">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="BasicThreadPool *"/>
    <use-retval/>
  </function>
  <!-- void BasicThreadPool::ConcurrentTaskFct1::run( long index ) -->
  <function name="BasicThreadPool::ConcurrentTaskFct1::run">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void BasicThreadPool::ConcurrentTaskFct::run( long index ) -->
  <function name="BasicThreadPool::ConcurrentTaskFct::run">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void BasicThreadPool::ConcurrentTaskTerminate::run( long index ) -->
  <function name="BasicThreadPool::ConcurrentTaskTerminate::run">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long BasicThreadPool::NumThreads( void ) -->
  <function name="BasicThreadPool::NumThreads">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- void BasicThreadPool::SimpleSignal::send( T new_val ) -->
  <function name="BasicThreadPool::SimpleSignal::send">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- T BasicThreadPool::SimpleSignal::wait( void ) -->
  <function name="BasicThreadPool::SimpleSignal::wait">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="T"/>
    <use-retval/>
  </function>
  <!-- bool BasicThreadPool::active( void ) -->
  <function name="BasicThreadPool::active">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- void BasicThreadPool::add( long n = 1 ) -->
  <function name="BasicThreadPool::add">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" default="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void BasicThreadPool::begin( long cnt ) -->
  <function name="BasicThreadPool::begin">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void BasicThreadPool::end( void ) -->
  <function name="BasicThreadPool::end">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void BasicThreadPool::exec_index( long cnt, const Fct & fct ) -->
  <function name="BasicThreadPool::exec_index">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void BasicThreadPool::exec_range( long sz, const Fct & fct ) -->
  <function name="BasicThreadPool::exec_range">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void BasicThreadPool::launch( ConcurrentTask * task, long index ) -->
  <function name="BasicThreadPool::launch">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void BasicThreadPool::move( BasicThreadPool & other, long n = 1 ) -->
  <function name="BasicThreadPool::move">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" default="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void BasicThreadPool::relaxed_exec_index( BasicThreadPool * pool, long cnt, const Fct & fct ) -->
  <function name="BasicThreadPool::relaxed_exec_index">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void BasicThreadPool::relaxed_exec_range( BasicThreadPool * pool, long sz, const Fct & fct ) -->
  <function name="BasicThreadPool::relaxed_exec_range">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void BasicThreadPool::remove( long n = 1 ) -->
  <function name="BasicThreadPool::remove">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" default="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void BasicThreadPool::runOneTask( ConcurrentTask * task, long index ) -->
  <function name="BasicThreadPool::runOneTask">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long PartitionInfo::NumIntervals( void ) -->
  <function name="PartitionInfo::NumIntervals">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- void PartitionInfo::interval( long & first, long & last, long i ) -->
  <function name="PartitionInfo::interval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void RecursiveThreadPool::exec_pair( long mid, const Fct0 & fct0, const Fct1 & fct1 ) -->
  <function name="RecursiveThreadPool::exec_pair">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- bool RecursiveThreadPoolHelper::concurrent( void ) -->
  <function name="RecursiveThreadPoolHelper::concurrent">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- RecursiveThreadPool * RecursiveThreadPoolHelper::subpool( long i ) -->
  <function name="RecursiveThreadPoolHelper::subpool">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="RecursiveThreadPool *"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long ZZ_pE::DivCross( void ) -->
  <function name="ZZ_pE::DivCross">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- ZZ_pX & ZZ_pE::LoopHole( void ) -->
  <function name="ZZ_pE::LoopHole">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ZZ_pX &amp;"/>
    <use-retval/>
  </function>
  <!-- long ZZ_pE::ModCross( void ) -->
  <function name="ZZ_pE::ModCross">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- void ZZ_pE::allocate( void ) -->
  <function name="ZZ_pE::allocate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- long ZZ_pE::degree( void ) -->
  <function name="ZZ_pE::degree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- void ZZ_pE::init( const ZZ_pX &  ) -->
  <function name="ZZ_pE::init">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- long ZZ_pE::initialized( void ) -->
  <function name="ZZ_pE::initialized">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- const ZZ_pXModulus & ZZ_pE::modulus( void ) -->
  <function name="ZZ_pE::modulus">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const ZZ_pXModulus &amp;"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- void ZZ_pE::swap( ZZ_pE & x ) -->
  <function name="ZZ_pE::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- const ZZ_pE & ZZ_pE::zero( void ) -->
  <function name="ZZ_pE::zero">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const ZZ_pE &amp;"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- void ZZ_pEBak::restore( void ) -->
  <function name="ZZ_pEBak::restore">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void ZZ_pEBak::save( void ) -->
  <function name="ZZ_pEBak::save">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void ZZ_pEContext::restore( void ) const -->
  <function name="ZZ_pEContext::restore">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
  </function>
  <!-- void ZZ_pEContext::save( void ) -->
  <function name="ZZ_pEContext::save">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- long PrimeSeq::next( void ) -->
  <function name="PrimeSeq::next">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- void PrimeSeq::reset( long b ) -->
  <function name="PrimeSeq::reset">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void PrimeSeq::start( void ) -->
  <function name="PrimeSeq::start">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void RandomBndGenerator::build( long _p ) -->
  <function name="RandomBndGenerator::build">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long RandomBndGenerator::next( void ) -->
  <function name="RandomBndGenerator::next">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- void RandomStream::get( unsigned char * res, long n ) -->
  <function name="RandomStream::get">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void RandomStream::set_nonce( unsigned long nonce ) -->
  <function name="RandomStream::set_nonce">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void RandomStream_impl_deleter::deleter( RandomStream_impl * p ) -->
  <function name="RandomStream_impl_deleter::deleter">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void ZZ::Deleter::apply( _ntl_gbigint p ) -->
  <function name="ZZ::Deleter::apply">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void ZZ::KillBig( void ) -->
  <function name="ZZ::KillBig">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- long ZZ::MaxAlloc( void ) -->
  <function name="ZZ::MaxAlloc">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- void ZZ::SetSize( long k ) -->
  <function name="ZZ::SetSize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long ZZ::SinglePrecision( void ) -->
  <function name="ZZ::SinglePrecision">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- long ZZ::WideSinglePrecision( void ) -->
  <function name="ZZ::WideSinglePrecision">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- void ZZ::kill( void ) -->
  <function name="ZZ::kill">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- long ZZ::null( void ) -->
  <function name="ZZ::null">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- bool ZZ::pinned( void ) -->
  <function name="ZZ::pinned">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- long ZZ::size( void ) -->
  <function name="ZZ::size">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- void ZZ::swap( ZZ & x ) -->
  <function name="ZZ::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- long ZZ::validate( void ) -->
  <function name="ZZ::validate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- void ZZ_CRTStructAdapter::eval( ZZ & t, const long * a, ZZ_TmpVecAdapter & tmp_vec ) -->
  <function name="ZZ_CRTStructAdapter::eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void ZZ_CRTStructAdapter::insert( long i, const ZZ & m ) -->
  <function name="ZZ_CRTStructAdapter::insert">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in"/>
  </function>
  <!-- bool ZZ_CRTStructAdapter::special( void ) -->
  <function name="ZZ_CRTStructAdapter::special">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- void ZZ_ReduceStructAdapter::adjust( ZZ & x ) -->
  <function name="ZZ_ReduceStructAdapter::adjust">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void ZZ_ReduceStructAdapter::eval( ZZ & x, ZZ & a ) -->
  <function name="ZZ_ReduceStructAdapter::eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- void ZZ_ReduceStructAdapter::init( const ZZ & p, const ZZ & excess ) -->
  <function name="ZZ_ReduceStructAdapter::init">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void ZZ_RemStructAdapter::eval( long * x, const ZZ & a, ZZ_TmpVecAdapter & tmp_vec ) -->
  <function name="ZZ_RemStructAdapter::eval">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void old_RandomStream::do_get( unsigned char * res, long n ) -->
  <function name="old_RandomStream::do_get">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void old_RandomStream::get( unsigned char * res, long n ) -->
  <function name="old_RandomStream::get">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void sp_ZZ_reduce_struct::build( long _p ) -->
  <function name="sp_ZZ_reduce_struct::build">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long sp_ZZ_reduce_struct::rem( const ZZ & a ) -->
  <function name="sp_ZZ_reduce_struct::rem">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void sp_ZZ_reduce_struct_policy::deleter( _ntl_general_rem_one_struct * pinfo ) -->
  <function name="sp_ZZ_reduce_struct_policy::deleter">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void FFTRep::DoSetSize( long NewK, long NewNumPrimes ) -->
  <function name="FFTRep::DoSetSize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void FFTRep::SetSize( long NewK ) -->
  <function name="FFTRep::SetSize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void ZZ_pX::SetLength( long n ) -->
  <function name="ZZ_pX::SetLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void ZZ_pX::SetMaxLength( long n ) -->
  <function name="ZZ_pX::SetMaxLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void ZZ_pX::kill( void ) -->
  <function name="ZZ_pX::kill">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void ZZ_pX::normalize( void ) -->
  <function name="ZZ_pX::normalize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void ZZ_pX::swap( ZZ_pX & x ) -->
  <function name="ZZ_pX::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- const ZZ_pX & ZZ_pX::zero( void ) -->
  <function name="ZZ_pX::zero">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const ZZ_pX &amp;"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- void ZZ_pXModRep::SetSize( long NewN ) -->
  <function name="ZZ_pXModRep::SetSize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- const ZZ_pX & ZZ_pXModulus::val( void ) -->
  <function name="ZZ_pXModulus::val">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const ZZ_pX &amp;"/>
    <use-retval/>
  </function>
  <!-- const ZZ_pX & ZZ_pXMultiplier::val( void ) -->
  <function name="ZZ_pXMultiplier::val">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const ZZ_pX &amp;"/>
    <use-retval/>
  </function>
  <!-- PD PD::load( const double * p ) -->
  <function name="PD::load">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="PD"/>
    <pure/>
    <arg nr="1" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- PD PD::load( const double * p ) -->
  <function name="PD::load">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="PD"/>
    <pure/>
    <arg nr="1" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- PD PD::load( const double * p ) -->
  <function name="PD::load">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="PD"/>
    <pure/>
    <arg nr="1" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- PD PD::loadu( const double * p ) -->
  <function name="PD::loadu">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="PD"/>
    <pure/>
    <arg nr="1" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- PD PD::loadu( const double * p ) -->
  <function name="PD::loadu">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="PD"/>
    <pure/>
    <arg nr="1" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- PD PD::loadu( const double * p ) -->
  <function name="PD::loadu">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="PD"/>
    <pure/>
    <arg nr="1" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void fftRep::DoSetSize( long NewK, long NewNumPrimes ) -->
  <function name="fftRep::DoSetSize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void fftRep::SetSize( long NewK ) -->
  <function name="fftRep::SetSize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void zz_pX::SetLength( long n ) -->
  <function name="zz_pX::SetLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void zz_pX::SetMaxLength( long n ) -->
  <function name="zz_pX::SetMaxLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void zz_pX::kill( void ) -->
  <function name="zz_pX::kill">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void zz_pX::normalize( void ) -->
  <function name="zz_pX::normalize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void zz_pX::swap( zz_pX & x ) -->
  <function name="zz_pX::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- const zz_pX & zz_pX::zero( void ) -->
  <function name="zz_pX::zero">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const zz_pX &amp;"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- const zz_pX & zz_pXModulus::val( void ) -->
  <function name="zz_pXModulus::val">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const zz_pX &amp;"/>
    <use-retval/>
  </function>
  <!-- const zz_pX & zz_pXMultiplier::val( void ) -->
  <function name="zz_pXMultiplier::val">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const zz_pX &amp;"/>
    <use-retval/>
  </function>
  <!-- void ZZX::SetLength( long n ) -->
  <function name="ZZX::SetLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void ZZX::SetMaxLength( long n ) -->
  <function name="ZZX::SetMaxLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void ZZX::kill( void ) -->
  <function name="ZZX::kill">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void ZZX::normalize( void ) -->
  <function name="ZZX::normalize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void ZZX::swap( ZZX & x ) -->
  <function name="ZZX::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void Vec::FixAtCurrentLength( void ) -->
  <function name="Vec::FixAtCurrentLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void Vec::FixLength( long n ) -->
  <function name="Vec::FixLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long Vec::MaxLength( void ) -->
  <function name="Vec::MaxLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- void Vec::SetMaxLength( long n ) -->
  <function name="Vec::SetMaxLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long Vec::allocated( void ) -->
  <function name="Vec::allocated">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- ref_GF2 Vec::at( long i ) -->
  <function name="Vec::at">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ref_GF2"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- const_iterator Vec::begin( void ) -->
  <function name="Vec::begin">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const_iterator"/>
    <use-retval/>
  </function>
  <!-- iterator Vec::begin( void ) -->
  <function name="Vec::begin">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="iterator"/>
    <use-retval/>
  </function>
  <!-- const_iterator Vec::end( void ) -->
  <function name="Vec::end">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const_iterator"/>
    <use-retval/>
  </function>
  <!-- iterator Vec::end( void ) -->
  <function name="Vec::end">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="iterator"/>
    <use-retval/>
  </function>
  <!-- long Vec::fixed( void ) -->
  <function name="Vec::fixed">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- void Vec::kill( void ) -->
  <function name="Vec::kill">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- long Vec::length( void ) -->
  <function name="Vec::length">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- void Vec::proxy_iterator_impl::add( long x ) -->
  <function name="Vec::proxy_iterator_impl::add">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void Vec::proxy_iterator_impl::dec( void ) -->
  <function name="Vec::proxy_iterator_impl::dec">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- long Vec::proxy_iterator_impl::diff( const proxy_iterator_impl & other ) -->
  <function name="Vec::proxy_iterator_impl::diff">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool Vec::proxy_iterator_impl::eq( const proxy_iterator_impl & other ) -->
  <function name="Vec::proxy_iterator_impl::eq">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void Vec::proxy_iterator_impl::inc( void ) -->
  <function name="Vec::proxy_iterator_impl::inc">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- ref_GF2 Vec::proxy_iterator_impl::make_ref_GF2( void ) -->
  <function name="Vec::proxy_iterator_impl::make_ref_GF2">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ref_GF2"/>
    <use-retval/>
  </function>
  <!-- long RR::OutputPrecision( void ) -->
  <function name="RR::OutputPrecision">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- void RR::SetOutputPrecision( long p ) -->
  <function name="RR::SetOutputPrecision">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void RR::SetPrecision( long p ) -->
  <function name="RR::SetPrecision">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long RR::exponent( void ) -->
  <function name="RR::exponent">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- long RR::precision( void ) -->
  <function name="RR::precision">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- void RR::swap( RR & z ) -->
  <function name="RR::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- long quad_float::OutputPrecision( void ) -->
  <function name="quad_float::OutputPrecision">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- void quad_float::SetOutputPrecision( long p ) -->
  <function name="quad_float::SetOutputPrecision">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- unsigned long AtomicCounter::inc( void ) -->
  <function name="AtomicCounter::inc">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned long"/>
    <use-retval/>
  </function>
  <!-- void AtomicLowWaterMark::UpdateMin( unsigned long val ) -->
  <function name="AtomicLowWaterMark::UpdateMin">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool AtomicRefCount::dec( void ) -->
  <function name="AtomicRefCount::dec">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- long AtomicRefCount::get_count( void ) -->
  <function name="AtomicRefCount::get_count">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- void AtomicRefCount::inc( void ) -->
  <function name="AtomicRefCount::inc">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void GuardProxy::lock( void ) -->
  <function name="GuardProxy::lock">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- long GF2E::DivCross( void ) -->
  <function name="GF2E::DivCross">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- long GF2E::GCDCross( void ) -->
  <function name="GF2E::GCDCross">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- long GF2E::KarCross( void ) -->
  <function name="GF2E::KarCross">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- long GF2E::ModCross( void ) -->
  <function name="GF2E::ModCross">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- long GF2E::WordLength( void ) -->
  <function name="GF2E::WordLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- void GF2E::allocate( void ) -->
  <function name="GF2E::allocate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- long GF2E::degree( void ) -->
  <function name="GF2E::degree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- void GF2E::init( const GF2X & NewP ) -->
  <function name="GF2E::init">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const GF2XModulus & GF2E::modulus( void ) -->
  <function name="GF2E::modulus">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const GF2XModulus &amp;"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- long GF2E::storage( void ) -->
  <function name="GF2E::storage">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- void GF2E::swap( GF2E & y ) -->
  <function name="GF2E::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void GF2EBak::restore( void ) -->
  <function name="GF2EBak::restore">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void GF2EBak::save( void ) -->
  <function name="GF2EBak::save">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void GF2EContext::restore( void ) const -->
  <function name="GF2EContext::restore">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
  </function>
  <!-- void GF2EContext::save( void ) -->
  <function name="GF2EContext::save">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void zz_p::FFTInit( long index ) -->
  <function name="zz_p::FFTInit">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool zz_p::IsFFTPrime( void ) -->
  <function name="zz_p::IsFFTPrime">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- long & zz_p::LoopHole( void ) -->
  <function name="zz_p::LoopHole">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long &amp;"/>
    <use-retval/>
  </function>
  <!-- mulmod_t zz_p::ModulusInverse( void ) -->
  <function name="zz_p::ModulusInverse">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="mulmod_t"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- long zz_p::PrimeCnt( void ) -->
  <function name="zz_p::PrimeCnt">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- void zz_p::UserFFTInit( long q ) -->
  <function name="zz_p::UserFFTInit">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- const sp_ZZ_reduce_struct & zz_p::ZZ_red_struct( void ) -->
  <function name="zz_p::ZZ_red_struct">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const sp_ZZ_reduce_struct &amp;"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- void zz_p::allocate( void ) -->
  <function name="zz_p::allocate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void zz_p::init( long NewP, long maxroot = NTL_FFTMaxRoot ) -->
  <function name="zz_p::init">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" default="NTL_FFTMaxRoot" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- sp_ll_reduce_struct zz_p::ll_red_struct( void ) -->
  <function name="zz_p::ll_red_struct">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="sp_ll_reduce_struct"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- long zz_p::modulus( void ) -->
  <function name="zz_p::modulus">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- sp_reduce_struct zz_p::red_struct( void ) -->
  <function name="zz_p::red_struct">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="sp_reduce_struct"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- long zz_p::storage( void ) -->
  <function name="zz_p::storage">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- zz_p zz_p::zero( void ) -->
  <function name="zz_p::zero">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="zz_p"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- void zz_pBak::restore( void ) -->
  <function name="zz_pBak::restore">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void zz_pBak::save( void ) -->
  <function name="zz_pBak::save">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- mulmod_t zz_pContext::ModulusInverse( void ) -->
  <function name="zz_pContext::ModulusInverse">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="mulmod_t"/>
    <use-retval/>
  </function>
  <!-- const sp_ZZ_reduce_struct & zz_pContext::ZZ_red_struct( void ) -->
  <function name="zz_pContext::ZZ_red_struct">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const sp_ZZ_reduce_struct &amp;"/>
    <use-retval/>
  </function>
  <!-- bool zz_pContext::equals( const zz_pContext & other ) -->
  <function name="zz_pContext::equals">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- long zz_pContext::modulus( void ) -->
  <function name="zz_pContext::modulus">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- bool zz_pContext::null( void ) -->
  <function name="zz_pContext::null">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- sp_reduce_struct zz_pContext::red_struct( void ) -->
  <function name="zz_pContext::red_struct">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="sp_reduce_struct"/>
    <use-retval/>
  </function>
  <!-- void zz_pContext::restore( void ) const -->
  <function name="zz_pContext::restore">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
  </function>
  <!-- void zz_pContext::save( void ) -->
  <function name="zz_pContext::save">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void ZZ_pEX::SetLength( long n ) -->
  <function name="ZZ_pEX::SetLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void ZZ_pEX::SetMaxLength( long n ) -->
  <function name="ZZ_pEX::SetMaxLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void ZZ_pEX::kill( void ) -->
  <function name="ZZ_pEX::kill">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void ZZ_pEX::normalize( void ) -->
  <function name="ZZ_pEX::normalize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void ZZ_pEX::swap( ZZ_pEX & x ) -->
  <function name="ZZ_pEX::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- const ZZ_pEX & ZZ_pEX::zero( void ) -->
  <function name="ZZ_pEX::zero">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const ZZ_pEX &amp;"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- const ZZ_pEX & ZZ_pEXModulus::val( void ) -->
  <function name="ZZ_pEXModulus::val">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const ZZ_pEX &amp;"/>
    <use-retval/>
  </function>
  <!-- void ZZ_p::DoInstall( void ) -->
  <function name="ZZ_p::DoInstall">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
  </function>
  <!-- long ZZ_p::ExtendedModulusSize( void ) -->
  <function name="ZZ_p::ExtendedModulusSize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- const ZZ_pFFTInfoT * ZZ_p::GetFFTInfo( void ) -->
  <function name="ZZ_p::GetFFTInfo">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const ZZ_pFFTInfoT *"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- ZZ_pTmpSpaceT * ZZ_p::GetTmpSpace( void ) -->
  <function name="ZZ_p::GetTmpSpace">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ZZ_pTmpSpaceT *"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- void ZZ_p::KillBig( void ) -->
  <function name="ZZ_p::KillBig">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- long ZZ_p::ModulusSize( void ) -->
  <function name="ZZ_p::ModulusSize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- void ZZ_p::allocate( void ) -->
  <function name="ZZ_p::allocate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void ZZ_p::init( const ZZ &  ) -->
  <function name="ZZ_p::init">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void ZZ_p::install( void ) -->
  <function name="ZZ_p::install">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
  </function>
  <!-- long ZZ_p::storage( void ) -->
  <function name="ZZ_p::storage">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- void ZZ_p::swap( ZZ_p & x ) -->
  <function name="ZZ_p::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- const ZZ_p & ZZ_p::zero( void ) -->
  <function name="ZZ_p::zero">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const ZZ_p &amp;"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- void ZZ_pBak::restore( void ) -->
  <function name="ZZ_pBak::restore">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void ZZ_pBak::save( void ) -->
  <function name="ZZ_pBak::save">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void ZZ_pContext::restore( void ) const -->
  <function name="ZZ_pContext::restore">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
  </function>
  <!-- void ZZ_pContext::save( void ) -->
  <function name="ZZ_pContext::save">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void ZZ_pInfoT::MatPrime_crt_helper_deleter_policy::deleter( MatPrime_crt_helper * p ) -->
  <function name="ZZ_pInfoT::MatPrime_crt_helper_deleter_policy::deleter">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void GF2::allocate( void ) -->
  <function name="GF2::allocate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- long GF2::modulus( void ) -->
  <function name="GF2::modulus">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- void GF2::swap( GF2 & x ) -->
  <function name="GF2::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- GF2 GF2::zero( void ) -->
  <function name="GF2::zero">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="GF2"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- void GF2Bak::restore( void ) -->
  <function name="GF2Bak::restore">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void GF2Bak::save( void ) -->
  <function name="GF2Bak::save">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void GF2Context::restore( void ) -->
  <function name="GF2Context::restore">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void GF2Context::save( void ) -->
  <function name="GF2Context::save">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void ref_GF2::swap( ref_GF2 x ) -->
  <function name="ref_GF2::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void GF2X::KillBig( void ) -->
  <function name="GF2X::KillBig">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void GF2X::SetLength( long n ) -->
  <function name="GF2X::SetLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GF2X::SetMaxLength( long n ) -->
  <function name="GF2X::SetMaxLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GF2X::kill( void ) -->
  <function name="GF2X::kill">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void GF2X::normalize( void ) -->
  <function name="GF2X::normalize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void GF2X::swap( GF2X & x ) -->
  <function name="GF2X::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- long GF2XModulus::WordLength( void ) -->
  <function name="GF2XModulus::WordLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- long GF2XVec::BaseSize( void ) -->
  <function name="GF2XVec::BaseSize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- void GF2XVec::SetSize( long n, long d ) -->
  <function name="GF2XVec::SetSize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GF2XVec::kill( void ) -->
  <function name="GF2XVec::kill">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- long GF2XVec::length( void ) -->
  <function name="GF2XVec::length">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- void GF2XVec::move( GF2XVec & other ) -->
  <function name="GF2XVec::move">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void GF2XVec::swap( GF2XVec & x ) -->
  <function name="GF2XVec::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- bool Lazy::built( void ) -->
  <function name="Lazy::built">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- void Lazy::kill( void ) -->
  <function name="Lazy::kill">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- long zz_pE::DivCross( void ) -->
  <function name="zz_pE::DivCross">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- zz_pX & zz_pE::LoopHole( void ) -->
  <function name="zz_pE::LoopHole">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="zz_pX &amp;"/>
    <use-retval/>
  </function>
  <!-- long zz_pE::ModCross( void ) -->
  <function name="zz_pE::ModCross">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- void zz_pE::allocate( void ) -->
  <function name="zz_pE::allocate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- long zz_pE::degree( void ) -->
  <function name="zz_pE::degree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- void zz_pE::init( const zz_pX &  ) -->
  <function name="zz_pE::init">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- long zz_pE::initialized( void ) -->
  <function name="zz_pE::initialized">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- const zz_pXModulus & zz_pE::modulus( void ) -->
  <function name="zz_pE::modulus">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const zz_pXModulus &amp;"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- void zz_pE::swap( zz_pE & x ) -->
  <function name="zz_pE::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- const zz_pE & zz_pE::zero( void ) -->
  <function name="zz_pE::zero">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const zz_pE &amp;"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- void zz_pEBak::restore( void ) -->
  <function name="zz_pEBak::restore">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void zz_pEBak::save( void ) -->
  <function name="zz_pEBak::save">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void zz_pEContext::restore( void ) const -->
  <function name="zz_pEContext::restore">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
  </function>
  <!-- void zz_pEContext::save( void ) -->
  <function name="zz_pEContext::save">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- long Mat::NumCols( void ) -->
  <function name="Mat::NumCols">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- long Mat::NumRows( void ) -->
  <function name="Mat::NumRows">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- void Mat::SetDims( long n, long m ) -->
  <function name="Mat::SetDims">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long Mat::alias( const Vec<T> & a ) -->
  <function name="Mat::alias">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const_reference Mat::get( long i, long j ) -->
  <function name="Mat::get">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const_reference"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void Mat::kill( void ) -->
  <function name="Mat::kill">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void Mat::move( Mat & other ) -->
  <function name="Mat::move">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- long Mat::position( const Vec<T> & a ) -->
  <function name="Mat::position">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- long Mat::position1( const Vec<T> & a ) -->
  <function name="Mat::position1">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void Mat::swap( Mat & other ) -->
  <function name="Mat::swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void default_BlockConstruct( T * p, long n ) -->
  <function name="default_BlockConstruct">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void default_BlockConstructFromObj( T * p, long n, const T & q ) -->
  <function name="default_BlockConstructFromObj">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void default_BlockConstructFromVec( T * p, long n, const T * q ) -->
  <function name="default_BlockConstructFromVec">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void default_BlockDestroy( T * p, long n ) -->
  <function name="default_BlockDestroy">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- mat_ZZ_p ident_mat_ZZ_p( long n ) -->
  <function name="ident_mat_ZZ_p">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="mat_ZZ_p"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- mat_ZZ_p_opaque_body * mat_ZZ_p_opaque_body_move( mat_ZZ_p & A ) -->
  <function name="mat_ZZ_p_opaque_body_move">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="mat_ZZ_p_opaque_body *"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void mul_transpose( mat_ZZ_p & X, const mat_ZZ_p & A, const mat_ZZ_p_opaque & B ) -->
  <function name="mul_transpose">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- mat_ZZ_p random_mat_ZZ_p( long n, long m ) -->
  <function name="random_mat_ZZ_p">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="mat_ZZ_p"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- mat_GF2E ident_mat_GF2E( long n ) -->
  <function name="ident_mat_GF2E">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="mat_GF2E"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- mat_GF2E random_mat_GF2E( long n, long m ) -->
  <function name="random_mat_GF2E">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="mat_GF2E"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void MulAdd( xdouble & z, const xdouble & a, const xdouble & b, const xdouble & c ) -->
  <function name="MulAdd">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in"/>
  </function>
  <!-- void MulSub( xdouble & z, const xdouble & a, const xdouble & b, const xdouble & c ) -->
  <function name="MulSub">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in"/>
  </function>
  <!-- xdouble power2_xdouble( long e ) -->
  <function name="power2_xdouble">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="xdouble"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- xdouble xexp( double x ) -->
  <function name="xexp">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="xdouble"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>-1.79769e+308:1.79769e+308</valid>
    </arg>
  </function>
  <!-- GF2EX random_GF2EX( long n ) -->
  <function name="random_GF2EX">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="GF2EX"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- GF2EX to_GF2EX( long a ) -->
  <function name="to_GF2EX">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="GF2EX"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void CharPoly( zz_pX & f, const mat_zz_p & M ) -->
  <function name="CharPoly">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ZZ_pEX BuildIrred_ZZ_pEX( long n ) -->
  <function name="BuildIrred_ZZ_pEX">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ZZ_pEX"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long WV_BlockConstructAlloc( WordVector & x, long d, long n ) -->
  <function name="WV_BlockConstructAlloc">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void WV_BlockConstructSet( WordVector & x, WordVector & y, long i ) -->
  <function name="WV_BlockConstructSet">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long WV_BlockDestroy( WordVector & x ) -->
  <function name="WV_BlockDestroy">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- long WV_storage( long d ) -->
  <function name="WV_storage">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- vec_ZZ_p random_vec_ZZ_p( long n ) -->
  <function name="random_vec_ZZ_p">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="vec_ZZ_p"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- vec_ZZ to_vec_ZZ( const vec_ZZ_p & a ) -->
  <function name="to_vec_ZZ">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="vec_ZZ"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- vec_ZZ_p to_vec_ZZ_p( const vec_ZZ & a ) -->
  <function name="to_vec_ZZ_p">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="vec_ZZ_p"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- mat_GF2 ident_mat_GF2( long n ) -->
  <function name="ident_mat_GF2">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="mat_GF2"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- mat_GF2 random_mat_GF2( long n, long m ) -->
  <function name="random_mat_GF2">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="mat_GF2"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- mat_GF2 to_mat_GF2( const vec_vec_GF2 & a ) -->
  <function name="to_mat_GF2">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="mat_GF2"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void ArithmeticError( const char * msg ) -->
  <function name="ArithmeticError">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long CharToIntVal( long c ) -->
  <function name="CharToIntVal">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void Error( const char * msg ) -->
  <function name="Error">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void FileError( const char * msg ) -->
  <function name="FileError">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void ForceToMem( double * p ) -->
  <function name="ForceToMem">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- unsigned long GetPID( void ) -->
  <function name="GetPID">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned long"/>
    <use-retval/>
  </function>
  <!-- double GetTime( void ) -->
  <function name="GetTime">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="double"/>
    <use-retval/>
  </function>
  <!-- double GetWallTime( void ) -->
  <function name="GetWallTime">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="double"/>
    <use-retval/>
  </function>
  <!-- void InputError( const char * msg ) -->
  <function name="InputError">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- char IntValToChar( long a ) -->
  <function name="IntValToChar">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="char"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long IsEOFChar( long c ) -->
  <function name="IsEOFChar">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long IsWhiteSpace( long c ) -->
  <function name="IsWhiteSpace">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void LogicError( const char * msg ) -->
  <function name="LogicError">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void MemoryError( void ) -->
  <function name="MemoryError">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void ResourceError( const char * msg ) -->
  <function name="ResourceError">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void TerminalError( const char * s ) -->
  <function name="TerminalError">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- double TrueDouble( double x ) -->
  <function name="TrueDouble">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="double"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>-1.79769e+308:1.79769e+308</valid>
    </arg>
  </function>
  <!-- unsigned long _ntl_GetPID( void ) -->
  <function name="_ntl_GetPID">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned long"/>
    <use-retval/>
  </function>
  <!-- double _ntl_GetTime( void ) -->
  <function name="_ntl_GetTime">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="double"/>
    <use-retval/>
  </function>
  <!-- const char * FileName( const char * stem, long d ) -->
  <function name="FileName">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const char *"/>
    <arg nr="1" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- zz_pEX random_zz_pEX( long n ) -->
  <function name="random_zz_pEX">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="zz_pEX"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- zz_pEX to_zz_pEX( long a ) -->
  <function name="to_zz_pEX">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="zz_pEX"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- mat_zz_p ident_mat_zz_p( long n ) -->
  <function name="ident_mat_zz_p">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="mat_zz_p"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- mat_zz_p random_mat_zz_p( long n, long m ) -->
  <function name="random_mat_zz_p">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="mat_zz_p"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void relaxed_solve( zz_p & d, vec_zz_p & x, const mat_zz_p & A, const vec_zz_p & b, bool relax = true ) -->
  <function name="relaxed_solve">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in"/>
    <arg nr="5" default="true" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long MulDivRem( long & qres, long a, long b, long n, wide_double bninv ) -->
  <function name="MulDivRem">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="5" direction="in"/>
  </function>
  <!-- long MulMod2( long a, long b, long n, wide_double bninv ) -->
  <function name="MulMod2">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in"/>
  </function>
  <!-- long MulMod2_legacy( long a, long b, long n, wide_double bninv ) -->
  <function name="MulMod2_legacy">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in"/>
  </function>
  <!-- long MulModPrecon( long a, long b, long n, unsigned long bninv ) -->
  <function name="MulModPrecon">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long MulModPreconWithQuo( long & qres, long a, long b, long n, unsigned long bninv ) -->
  <function name="MulModPreconWithQuo">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long MulModWithQuo( long & qres, long a, long b, long n, wide_double ninv ) -->
  <function name="MulModWithQuo">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="5" direction="in"/>
  </function>
  <!-- bool NormalizedModulus( wide_double ninv ) -->
  <function name="NormalizedModulus">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- long NormalizedMulMod( long a, long b, long n, wide_double ninv ) -->
  <function name="NormalizedMulMod">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in"/>
  </function>
  <!-- wide_double PrepMulMod( long n ) -->
  <function name="PrepMulMod">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wide_double"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void VectorMulMod( long k, long * x, const long * a, long b, long n ) -->
  <function name="VectorMulMod">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long sp_CorrectDeficit( long a, long n ) -->
  <function name="sp_CorrectDeficit">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long sp_CorrectDeficitQuo( T & q, long a, long n, long amt = 1 ) -->
  <function name="sp_CorrectDeficitQuo">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" default="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long sp_CorrectExcess( long a, long n ) -->
  <function name="sp_CorrectExcess">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long sp_CorrectExcessQuo( T & q, long a, long n, long amt = 1 ) -->
  <function name="sp_CorrectExcessQuo">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" default="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool sp_Negative( unsigned long a ) -->
  <function name="sp_Negative">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- sp_reduce_struct sp_PrepRem( long n ) -->
  <function name="sp_PrepRem">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="sp_reduce_struct"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long sp_SignMask( long a ) -->
  <function name="sp_SignMask">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- vec_ZZX SFFactor( const ZZX & f, long verbose = 0, long bnd = 0 ) -->
  <function name="SFFactor">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="vec_ZZX"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long GetMatPrime( long i ) -->
  <function name="GetMatPrime">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void InitMatPrimeInfo( MatPrimeInfo & info, long q, long w ) -->
  <function name="InitMatPrimeInfo">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void RestoreMatPrime( long i ) -->
  <function name="RestoreMatPrime">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void UseMatPrime( long index ) -->
  <function name="UseMatPrime">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void init_scratch( const MatPrime_crt_helper & H, MatPrime_crt_helper_scratch & scratch ) -->
  <function name="init_scratch">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- long CalcMaxRoot( long p ) -->
  <function name="CalcMaxRoot">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void FFTFwd( long * A, const long * a, long k, const FFTPrimeInfo & info ) -->
  <function name="FFTFwd">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in"/>
  </function>
  <!-- void FFTFwd_trans( long * A, const long * a, long k, const FFTPrimeInfo & info ) -->
  <function name="FFTFwd_trans">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in"/>
  </function>
  <!-- void FFTFwd_trunc( long * A, const long * a, long k, long i, long yn, long xn ) -->
  <function name="FFTFwd_trunc">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="6" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void FFTRev1( long * A, const long * a, long k, const FFTPrimeInfo & info ) -->
  <function name="FFTRev1">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in"/>
  </function>
  <!-- void FFTRev1_trans( long * A, const long * a, long k, const FFTPrimeInfo & info ) -->
  <function name="FFTRev1_trans">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in"/>
  </function>
  <!-- void FFTRev1_trunc( long * A, const long * a, long k, long i, long yn ) -->
  <function name="FFTRev1_trunc">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long GetFFTPrime( long i ) -->
  <function name="GetFFTPrime">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- mulmod_t GetFFTPrimeInv( long i ) -->
  <function name="GetFFTPrimeInv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="mulmod_t"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- double GetFFTPrimeRecip( long i ) -->
  <function name="GetFFTPrimeRecip">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="double"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void InitFFTPrimeInfo( FFTPrimeInfo & info, long q, long w, long bigtab_index ) -->
  <function name="InitFFTPrimeInfo">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long IsFFTPrime( long n, long & w ) -->
  <function name="IsFFTPrime">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- void UseFFTPrime( long index ) -->
  <function name="UseFFTPrime">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- vec_GF2E random_vec_GF2E( long n ) -->
  <function name="random_vec_GF2E">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="vec_GF2E"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long AvailableThreads( void ) -->
  <function name="AvailableThreads">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <use-retval/>
  </function>
  <!-- BasicThreadPool * GetThreadPool( void ) -->
  <function name="GetThreadPool">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="BasicThreadPool *"/>
    <use-retval/>
  </function>
  <!-- BasicThreadPool * ReleaseThreadPool( void ) -->
  <function name="ReleaseThreadPool">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="BasicThreadPool *"/>
    <use-retval/>
  </function>
  <!-- void ResetThreadPool( BasicThreadPool * pool = 0 ) -->
  <function name="ResetThreadPool">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" default="0" direction="inout"/>
  </function>
  <!-- void SetNumThreads( long n ) -->
  <function name="SetNumThreads">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- vec_ZZ_pE random_vec_ZZ_pE( long n ) -->
  <function name="random_vec_ZZ_pE">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="vec_ZZ_pE"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ZZ_pX BuildIrred_ZZ_pX( long n ) -->
  <function name="BuildIrred_ZZ_pX">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ZZ_pX"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ZZ_pE random_ZZ_pE( void ) -->
  <function name="random_ZZ_pE">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ZZ_pE"/>
    <use-retval/>
  </function>
  <!-- ZZ_pE to_ZZ_pE( const ZZ_pX & a ) -->
  <function name="to_ZZ_pE">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ZZ_pE"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void BytesFromZZ( unsigned char * p, const ZZ & a, long n ) -->
  <function name="BytesFromZZ">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long CRTInRange( const ZZ & gg, const ZZ & aa ) -->
  <function name="CRTInRange">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void GCD_alt( ZZ & d, const ZZ & a, const ZZ & b ) -->
  <function name="GCD_alt">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void GenGermainPrime( ZZ & n, long l, long err = 80 ) -->
  <function name="GenGermainPrime">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" default="80" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ZZ GenGermainPrime_ZZ( long l, long err = 80 ) -->
  <function name="GenGermainPrime_ZZ">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ZZ"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" default="80" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long GenGermainPrime_long( long l, long err = 80 ) -->
  <function name="GenGermainPrime_long">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" default="80" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void GenPrime( ZZ & n, long l, long err = 80 ) -->
  <function name="GenPrime">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" default="80" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ZZ GenPrime_ZZ( long l, long err = 80 ) -->
  <function name="GenPrime_ZZ">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ZZ"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" default="80" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long GenPrime_long( long l, long err = 80 ) -->
  <function name="GenPrime_long">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" default="80" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- RandomStream & GetCurrentRandomStream( void ) -->
  <function name="GetCurrentRandomStream">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="RandomStream &amp;"/>
    <use-retval/>
  </function>
  <!-- long IsOdd( const ZZ & a ) -->
  <function name="IsOdd">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- long Jacobi( const ZZ & a, const ZZ & n ) -->
  <function name="Jacobi">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void LowLevelPowerMod( ZZ & x, const ZZ & a, const ZZ & e, const ZZ & n ) -->
  <function name="LowLevelPowerMod">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in"/>
  </function>
  <!-- long MakeOdd( ZZ & x ) -->
  <function name="MakeOdd">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- long MillerWitness( const ZZ & n, const ZZ & w ) -->
  <function name="MillerWitness">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- long NextPowerOfTwo( long m ) -->
  <function name="NextPowerOfTwo">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long NumTwos( const ZZ & x ) -->
  <function name="NumTwos">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- long ProbPrime( const ZZ & n, long NumTrials = 10 ) -->
  <function name="ProbPrime">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" default="10" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QuickAccumBegin( ZZ & x, long sz ) -->
  <function name="QuickAccumBegin">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QuickAccumEnd( ZZ & x ) -->
  <function name="QuickAccumEnd">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void QuickAccumMulAdd( ZZ & x, const ZZ & y, long b ) -->
  <function name="QuickAccumMulAdd">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QuickRem( ZZ & r, const ZZ & b ) -->
  <function name="QuickRem">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void RandomBits( long & x, long l ) -->
  <function name="RandomBits">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ZZ RandomBits_ZZ( long NumBits ) -->
  <function name="RandomBits_ZZ">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ZZ"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long RandomBits_long( long l ) -->
  <function name="RandomBits_long">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- unsigned long RandomBits_ulong( long l ) -->
  <function name="RandomBits_ulong">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void RandomLen( long & x, long l ) -->
  <function name="RandomLen">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ZZ RandomLen_ZZ( long NumBits ) -->
  <function name="RandomLen_ZZ">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ZZ"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long RandomLen_long( long l ) -->
  <function name="RandomLen_long">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void RandomPrime( ZZ & n, long l, long NumTrials = 10 ) -->
  <function name="RandomPrime">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" default="10" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ZZ RandomPrime_ZZ( long l, long NumTrials = 10 ) -->
  <function name="RandomPrime_ZZ">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ZZ"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" default="10" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long RandomPrime_long( long l, long NumTrials = 10 ) -->
  <function name="RandomPrime_long">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" default="10" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void RandomStream_impl_set_nonce( RandomStream_impl & impl, unsigned long nonce ) -->
  <function name="RandomStream_impl_set_nonce">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- unsigned long RandomWord( void ) -->
  <function name="RandomWord">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned long"/>
    <use-retval/>
  </function>
  <!-- long SetBit( ZZ & x, long p ) -->
  <function name="SetBit">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void SetSeed( const ZZ & s ) -->
  <function name="SetSeed">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void SubPos( ZZ & x, const ZZ & a, const ZZ & b ) -->
  <function name="SubPos">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- long SwitchBit( ZZ & x, long p ) -->
  <function name="SwitchBit">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void VectorRandomBnd( long k, long * x, long n ) -->
  <function name="VectorRandomBnd">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void VectorRandomWord( long k, unsigned long * x ) -->
  <function name="VectorRandomWord">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- long ZZ_BlockConstructAlloc( ZZ & x, long d, long n ) -->
  <function name="ZZ_BlockConstructAlloc">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void ZZ_BlockConstructSet( ZZ & x, ZZ & y, long i ) -->
  <function name="ZZ_BlockConstructSet">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long ZZ_BlockDestroy( ZZ & x ) -->
  <function name="ZZ_BlockDestroy">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- long ZZ_RoundCorrection( const ZZ & a, long k, long residual ) -->
  <function name="ZZ_RoundCorrection">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long ZZ_storage( long d ) -->
  <function name="ZZ_storage">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long bit( const ZZ & a, long k ) -->
  <function name="bit">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void bit_and( ZZ & x, const ZZ & a, const ZZ & b ) -->
  <function name="bit_and">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void bit_or( ZZ & x, const ZZ & a, const ZZ & b ) -->
  <function name="bit_or">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void bit_xor( ZZ & x, const ZZ & a, const ZZ & b ) -->
  <function name="bit_xor">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- long digit( const ZZ & a, long k ) -->
  <function name="digit">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ZZ power2_ZZ( long e ) -->
  <function name="power2_ZZ">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ZZ"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ZZ power_ZZ( long a, long e ) -->
  <function name="power_ZZ">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ZZ"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long power_long( long a, long e ) -->
  <function name="power_long">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- unsigned int to_uint( const ZZ & a ) -->
  <function name="to_uint">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned int"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- unsigned long to_ulong( const ZZ & a ) -->
  <function name="to_ulong">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned long"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ZZ trunc_ZZ( const ZZ & a, long k ) -->
  <function name="trunc_ZZ">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ZZ"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long trunc_long( const ZZ & a, long k ) -->
  <function name="trunc_long">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void FromFFTRep( ZZ_pX & x, FFTRep & y, long lo, long hi ) -->
  <function name="FromFFTRep">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void FromZZ_pXModRep( ZZ_pX & x, const ZZ_pXModRep & a, long lo, long hi ) -->
  <function name="FromZZ_pXModRep">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void NDFromFFTRep( ZZ_pX & x, const FFTRep & y, long lo, long hi, FFTRep & temp ) -->
  <function name="NDFromFFTRep">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="5" direction="inout"/>
  </function>
  <!-- void RevFromFFTRep( vec_ZZ_p & x, FFTRep & y, long lo, long hi ) -->
  <function name="RevFromFFTRep">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void ToFFTRep( FFTRep & y, const ZZ_pX & x, long k, long lo, long hi ) -->
  <function name="ToFFTRep">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void ToFFTRep_trunc( FFTRep & y, const ZZ_pX & x, long k, long len ) -->
  <function name="ToFFTRep_trunc">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void ToZZ_pXModRep( ZZ_pXModRep & x, const ZZ_pX & a, long lo, long hi ) -->
  <function name="ToZZ_pXModRep">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ZZ_pX random_ZZ_pX( long n ) -->
  <function name="random_ZZ_pX">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ZZ_pX"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long & _ntl_ALLOC( _ntl_gbigint p ) -->
  <function name="_ntl_ALLOC">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long &amp;"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- long _ntl_PINNED( _ntl_gbigint p ) -->
  <function name="_ntl_PINNED">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- long & _ntl_SIZE( _ntl_gbigint p ) -->
  <function name="_ntl_SIZE">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long &amp;"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- long _ntl_ZEROP( _ntl_gbigint p ) -->
  <function name="_ntl_ZEROP">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- long _ntl_g2log( _ntl_gbigint a ) -->
  <function name="_ntl_g2log">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- long _ntl_g2logs( long a ) -->
  <function name="_ntl_g2logs">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void _ntl_gabs( _ntl_gbigint * a ) -->
  <function name="_ntl_gabs">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void _ntl_gadd( _ntl_gbigint a, _ntl_gbigint b, _ntl_gbigint * c ) -->
  <function name="_ntl_gadd">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void _ntl_gaddmod( _ntl_gbigint a, _ntl_gbigint b, _ntl_gbigint n, _ntl_gbigint * c ) -->
  <function name="_ntl_gaddmod">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="inout"/>
  </function>
  <!-- void _ntl_gaddmul( _ntl_gbigint x, _ntl_gbigint y, _ntl_gbigint * ww ) -->
  <function name="_ntl_gaddmul">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void _ntl_gand( _ntl_gbigint a, _ntl_gbigint b, _ntl_gbigint * c ) -->
  <function name="_ntl_gand">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- long _ntl_gbit( _ntl_gbigint a, long p ) -->
  <function name="_ntl_gbit">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long _ntl_gblock_construct_alloc( _ntl_gbigint * x, long d, long n ) -->
  <function name="_ntl_gblock_construct_alloc">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void _ntl_gblock_construct_set( _ntl_gbigint x, _ntl_gbigint * y, long i ) -->
  <function name="_ntl_gblock_construct_set">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long _ntl_gblock_destroy( _ntl_gbigint x ) -->
  <function name="_ntl_gblock_destroy">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- long _ntl_gblock_storage( long d ) -->
  <function name="_ntl_gblock_storage">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void _ntl_gbytesfromz( unsigned char * p, _ntl_gbigint a, long nn ) -->
  <function name="_ntl_gbytesfromz">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long _ntl_gcompare( _ntl_gbigint a, _ntl_gbigint b ) -->
  <function name="_ntl_gcompare">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void _ntl_gcopy( _ntl_gbigint a, _ntl_gbigint * b ) -->
  <function name="_ntl_gcopy">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- long _ntl_gcrtinrange( _ntl_gbigint g, _ntl_gbigint a ) -->
  <function name="_ntl_gcrtinrange">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- long _ntl_gdigit( _ntl_gbigint a, long i ) -->
  <function name="_ntl_gdigit">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void _ntl_gdiv( _ntl_gbigint a, _ntl_gbigint b, _ntl_gbigint * q, _ntl_gbigint * r ) -->
  <function name="_ntl_gdiv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
    <arg nr="4" direction="inout"/>
  </function>
  <!-- double _ntl_gdoub( _ntl_gbigint n ) -->
  <function name="_ntl_gdoub">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="double"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void _ntl_gdoubtoz( double a, _ntl_gbigint * x ) -->
  <function name="_ntl_gdoubtoz">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>-1.79769e+308:1.79769e+308</valid>
    </arg>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- void _ntl_gexp( _ntl_gbigint a, long e, _ntl_gbigint * b ) -->
  <function name="_ntl_gexp">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void _ntl_gexps( long a, long e, _ntl_gbigint * b ) -->
  <function name="_ntl_gexps">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void _ntl_gfree( _ntl_gbigint x ) -->
  <function name="_ntl_gfree">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void _ntl_gfrombytes( _ntl_gbigint * x, const unsigned char * p, long n ) -->
  <function name="_ntl_gfrombytes">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void _ntl_ggcd( _ntl_gbigint m1, _ntl_gbigint m2, _ntl_gbigint * r ) -->
  <function name="_ntl_ggcd">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void _ntl_ggcd_alt( _ntl_gbigint m1, _ntl_gbigint m2, _ntl_gbigint * r ) -->
  <function name="_ntl_ggcd_alt">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void _ntl_gintoz( long d, _ntl_gbigint * a ) -->
  <function name="_ntl_gintoz">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- long _ntl_ginv( _ntl_gbigint a, _ntl_gbigint b, _ntl_gbigint * c ) -->
  <function name="_ntl_ginv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void _ntl_ginvmod( _ntl_gbigint a, _ntl_gbigint n, _ntl_gbigint * c ) -->
  <function name="_ntl_ginvmod">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- long _ntl_gisone( _ntl_gbigint n ) -->
  <function name="_ntl_gisone">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- long _ntl_giszero( _ntl_gbigint a ) -->
  <function name="_ntl_giszero">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- double _ntl_glog( _ntl_gbigint a ) -->
  <function name="_ntl_glog">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="double"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void _ntl_glowbits( _ntl_gbigint a, long k, _ntl_gbigint * b ) -->
  <function name="_ntl_glowbits">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void _ntl_glshift( _ntl_gbigint n, long k, _ntl_gbigint * a ) -->
  <function name="_ntl_glshift">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- long _ntl_gmakeodd( _ntl_gbigint * n ) -->
  <function name="_ntl_gmakeodd">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- long _ntl_gmaxalloc( _ntl_gbigint x ) -->
  <function name="_ntl_gmaxalloc">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void _ntl_gmod( _ntl_gbigint a, _ntl_gbigint b, _ntl_gbigint * r ) -->
  <function name="_ntl_gmod">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void _ntl_gmul( _ntl_gbigint a, _ntl_gbigint b, _ntl_gbigint * c ) -->
  <function name="_ntl_gmul">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void _ntl_gmulmod( _ntl_gbigint a, _ntl_gbigint b, _ntl_gbigint n, _ntl_gbigint * c ) -->
  <function name="_ntl_gmulmod">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="inout"/>
  </function>
  <!-- void _ntl_gnegate( _ntl_gbigint * a ) -->
  <function name="_ntl_gnegate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- long _ntl_gnumtwos( _ntl_gbigint n ) -->
  <function name="_ntl_gnumtwos">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- long _ntl_godd( _ntl_gbigint a ) -->
  <function name="_ntl_godd">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void _ntl_gone( _ntl_gbigint * a ) -->
  <function name="_ntl_gone">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void _ntl_gor( _ntl_gbigint a, _ntl_gbigint b, _ntl_gbigint * c ) -->
  <function name="_ntl_gor">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void _ntl_gquickmod( _ntl_gbigint * r, _ntl_gbigint b ) -->
  <function name="_ntl_gquickmod">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- long _ntl_ground_correction( _ntl_gbigint a, long k, long residual ) -->
  <function name="_ntl_ground_correction">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void _ntl_grshift( _ntl_gbigint n, long k, _ntl_gbigint * a ) -->
  <function name="_ntl_grshift">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void _ntl_gsadd( _ntl_gbigint a, long d, _ntl_gbigint * b ) -->
  <function name="_ntl_gsadd">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void _ntl_gsaddmul( _ntl_gbigint x, long y, _ntl_gbigint * ww ) -->
  <function name="_ntl_gsaddmul">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- long _ntl_gscompare( _ntl_gbigint a, long b ) -->
  <function name="_ntl_gscompare">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long _ntl_gsdiv( _ntl_gbigint a, long b, _ntl_gbigint * q ) -->
  <function name="_ntl_gsdiv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- long _ntl_gsetbit( _ntl_gbigint * a, long p ) -->
  <function name="_ntl_gsetbit">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void _ntl_gsetlength( _ntl_gbigint * v, long len ) -->
  <function name="_ntl_gsetlength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long _ntl_gsign( _ntl_gbigint a ) -->
  <function name="_ntl_gsign">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- long _ntl_gsize( _ntl_gbigint rep ) -->
  <function name="_ntl_gsize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- long _ntl_gslowbits( _ntl_gbigint a, long k ) -->
  <function name="_ntl_gslowbits">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long _ntl_gsmod( _ntl_gbigint a, long d ) -->
  <function name="_ntl_gsmod">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void _ntl_gsmul( _ntl_gbigint a, long d, _ntl_gbigint * b ) -->
  <function name="_ntl_gsmul">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void _ntl_gsmulmod( _ntl_gbigint a, long b, _ntl_gbigint n, _ntl_gbigint * c ) -->
  <function name="_ntl_gsmulmod">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="inout"/>
  </function>
  <!-- long _ntl_gsptest( _ntl_gbigint a ) -->
  <function name="_ntl_gsptest">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void _ntl_gsq( _ntl_gbigint a, _ntl_gbigint * c ) -->
  <function name="_ntl_gsq">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- void _ntl_gsqmod( _ntl_gbigint a, _ntl_gbigint n, _ntl_gbigint * c ) -->
  <function name="_ntl_gsqmod">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void _ntl_gsqrt( _ntl_gbigint n, _ntl_gbigint * r ) -->
  <function name="_ntl_gsqrt">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- long _ntl_gsqrts( long n ) -->
  <function name="_ntl_gsqrts">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void _ntl_gssub( _ntl_gbigint a, long d, _ntl_gbigint * b ) -->
  <function name="_ntl_gssub">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void _ntl_gssubmul( _ntl_gbigint x, long y, _ntl_gbigint * ww ) -->
  <function name="_ntl_gssubmul">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void _ntl_gsub( _ntl_gbigint a, _ntl_gbigint b, _ntl_gbigint * c ) -->
  <function name="_ntl_gsub">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void _ntl_gsubmod( _ntl_gbigint a, _ntl_gbigint b, _ntl_gbigint n, _ntl_gbigint * c ) -->
  <function name="_ntl_gsubmod">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="inout"/>
  </function>
  <!-- void _ntl_gsubmul( _ntl_gbigint x, _ntl_gbigint y, _ntl_gbigint * ww ) -->
  <function name="_ntl_gsubmul">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void _ntl_gsubpos( _ntl_gbigint a, _ntl_gbigint b, _ntl_gbigint * c ) -->
  <function name="_ntl_gsubpos">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void _ntl_gswap( _ntl_gbigint * a, _ntl_gbigint * b ) -->
  <function name="_ntl_gswap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- long _ntl_gswitchbit( _ntl_gbigint * a, long p ) -->
  <function name="_ntl_gswitchbit">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long _ntl_gtoint( _ntl_gbigint a ) -->
  <function name="_ntl_gtoint">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- unsigned long _ntl_gtouint( _ntl_gbigint a ) -->
  <function name="_ntl_gtouint">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned long"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void _ntl_guintoz( unsigned long d, _ntl_gbigint * a ) -->
  <function name="_ntl_guintoz">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- long _ntl_gvalidate( _ntl_gbigint a ) -->
  <function name="_ntl_gvalidate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- long _ntl_gweight( _ntl_gbigint a ) -->
  <function name="_ntl_gweight">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- long _ntl_gweights( long a ) -->
  <function name="_ntl_gweights">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long _ntl_gwsptest( _ntl_gbigint a ) -->
  <function name="_ntl_gwsptest">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void _ntl_gxor( _ntl_gbigint a, _ntl_gbigint b, _ntl_gbigint * c ) -->
  <function name="_ntl_gxor">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void _ntl_gzero( _ntl_gbigint * a ) -->
  <function name="_ntl_gzero">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void FromfftRep( zz_pX & x, fftRep & y, long lo, long hi ) -->
  <function name="FromfftRep">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void NDFromfftRep( zz_pX & x, const fftRep & y, long lo, long hi, fftRep & temp ) -->
  <function name="NDFromfftRep">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="5" direction="inout"/>
  </function>
  <!-- void RevFromfftRep( vec_zz_p & x, fftRep & y, long lo, long hi ) -->
  <function name="RevFromfftRep">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void TofftRep( fftRep & y, const zz_pX & x, long k, long lo, long hi ) -->
  <function name="TofftRep">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void TofftRep_trunc( fftRep & y, const zz_pX & x, long k, long len ) -->
  <function name="TofftRep_trunc">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- zz_pX random_zz_pX( long n ) -->
  <function name="random_zz_pX">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="zz_pX"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long CharPolyBound( const ZZX & a, const ZZX & f ) -->
  <function name="CharPolyBound">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- long HomDivide( ZZX & q, const ZZX & a, const ZZX & b ) -->
  <function name="HomDivide">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void HomMul( ZZX & x, const ZZX & a, const ZZX & b ) -->
  <function name="HomMul">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void HomPseudoDiv( ZZX & q, const ZZX & a, const ZZX & b ) -->
  <function name="HomPseudoDiv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void HomPseudoDivRem( ZZX & q, ZZX & r, const ZZX & a, const ZZX & b ) -->
  <function name="HomPseudoDivRem">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in"/>
  </function>
  <!-- void HomPseudoRem( ZZX & r, const ZZX & a, const ZZX & b ) -->
  <function name="HomPseudoRem">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void HomSqr( ZZX & x, const ZZX & a ) -->
  <function name="HomSqr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void KarMul( ZZX & x, const ZZX & a, const ZZX & b ) -->
  <function name="KarMul">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void KarSqr( ZZX & x, const ZZX & a ) -->
  <function name="KarSqr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- long MaxBits( const ZZX & f ) -->
  <function name="MaxBits">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- long PlainDivide( ZZX & q, const ZZX & a, const ZZX & b ) -->
  <function name="PlainDivide">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void PlainPseudoDiv( ZZX & q, const ZZX & a, const ZZX & b ) -->
  <function name="PlainPseudoDiv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void PlainPseudoDivRem( ZZX & q, ZZX & r, const ZZX & a, const ZZX & b ) -->
  <function name="PlainPseudoDivRem">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in"/>
  </function>
  <!-- void PlainPseudoRem( ZZX & r, const ZZX & a, const ZZX & b ) -->
  <function name="PlainPseudoRem">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void PseudoDivRem( ZZX & q, ZZX & r, const ZZX & a, const ZZX & b ) -->
  <function name="PseudoDivRem">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in"/>
  </function>
  <!-- void SSMul( ZZX & x, const ZZX & a, const ZZX & b ) -->
  <function name="SSMul">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- double SSRatio( long na, long maxa, long nb, long maxb ) -->
  <function name="SSRatio">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="double"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void SSSqr( ZZX & x, const ZZX & a ) -->
  <function name="SSSqr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- ZZX to_ZZX( long a ) -->
  <function name="to_ZZX">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ZZX"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- mat_ZZ_pE ident_mat_ZZ_pE( long n ) -->
  <function name="ident_mat_ZZ_pE">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="mat_ZZ_pE"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- mat_ZZ_pE random_mat_ZZ_pE( long n, long m ) -->
  <function name="random_mat_ZZ_pE">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="mat_ZZ_pE"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- vec_zz_p random_vec_zz_p( long n ) -->
  <function name="random_vec_zz_p">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="vec_zz_p"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- vec_ZZ to_vec_ZZ( const vec_zz_p & a ) -->
  <function name="to_vec_ZZ">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="vec_ZZ"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- vec_zz_p to_vec_zz_p( const vec_ZZ & a ) -->
  <function name="to_vec_zz_p">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="vec_zz_p"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- long FFTRoundUp( long xn, long k ) -->
  <function name="FFTRoundUp">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- mat_RR ident_mat_RR( long n ) -->
  <function name="ident_mat_RR">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="mat_RR"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- vec_GF2 random_vec_GF2( long n ) -->
  <function name="random_vec_GF2">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="vec_GF2"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void HNF( mat_ZZ & W, const mat_ZZ & A, const ZZ & D ) -->
  <function name="HNF">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void ComputePi( RR & res ) -->
  <function name="ComputePi">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- RR ComputePi_RR( void ) -->
  <function name="ComputePi_RR">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="RR"/>
    <use-retval/>
  </function>
  <!-- RR power2_RR( long e ) -->
  <function name="power2_RR">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="RR"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- RR random_RR( void ) -->
  <function name="random_RR">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="RR"/>
    <use-retval/>
  </function>
  <!-- RR to_RR( int a ) -->
  <function name="to_RR">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="RR"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- const ZZ_limb_t * ZZ_limbs_get( const ZZ & a ) -->
  <function name="ZZ_limbs_get">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const ZZ_limb_t *"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void ZZ_limbs_set( ZZ & x, const ZZ_limb_t * p, long n ) -->
  <function name="ZZ_limbs_set">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- const _ntl_limb_t * _ntl_glimbs_get( _ntl_gbigint p ) -->
  <function name="_ntl_glimbs_get">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const _ntl_limb_t *"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void _ntl_glimbs_set( const _ntl_limb_t * p, long n, _ntl_gbigint * x ) -->
  <function name="_ntl_glimbs_set">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- quad_float ldexp( const quad_float & x, long exp ) -->
  <function name="ldexp">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="quad_float"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- quad_float power2_quad_float( long e ) -->
  <function name="power2_quad_float">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="quad_float"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void quad_float_PrecisionOK( long & , const double &  ) -->
  <function name="quad_float_PrecisionOK">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>-1.79769e+308:1.79769e+308</valid>
    </arg>
  </function>
  <!-- void quad_float_in_place_add( quad_float & x, const quad_float & y ) -->
  <function name="quad_float_in_place_add">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void quad_float_in_place_div( quad_float & x, const quad_float & y ) -->
  <function name="quad_float_in_place_div">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void quad_float_in_place_mul( quad_float & x, const quad_float & y ) -->
  <function name="quad_float_in_place_mul">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void quad_float_in_place_negate( quad_float & x ) -->
  <function name="quad_float_in_place_negate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void quad_float_in_place_sqrt( quad_float & y, double & c_ref ) -->
  <function name="quad_float_in_place_sqrt">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- void quad_float_in_place_sub( quad_float & x, const quad_float & y ) -->
  <function name="quad_float_in_place_sub">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void quad_float_normalize( quad_float & z, const double & xhi, const double & xlo ) -->
  <function name="quad_float_normalize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>-1.79769e+308:1.79769e+308</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>-1.79769e+308:1.79769e+308</valid>
    </arg>
  </function>
  <!-- quad_float random_quad_float( void ) -->
  <function name="random_quad_float">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="quad_float"/>
    <use-retval/>
  </function>
  <!-- zz_pX BuildIrred_zz_pX( long n ) -->
  <function name="BuildIrred_zz_pX">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="zz_pX"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- GF2EX BuildIrred_GF2EX( long n ) -->
  <function name="BuildIrred_GF2EX">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="GF2EX"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void ComposeFrobeniusMap( GF2EX & y, const GF2EXModulus & F ) -->
  <function name="ComposeFrobeniusMap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void PlainFrobeniusMap( GF2EX & h, const GF2EXModulus & F ) -->
  <function name="PlainFrobeniusMap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- long UseComposeFrobenius( long d, long n ) -->
  <function name="UseComposeFrobenius">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void FactorInt( FacVec & fvec, long n ) -->
  <function name="FactorInt">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- GF2E random_GF2E( void ) -->
  <function name="random_GF2E">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="GF2E"/>
    <use-retval/>
  </function>
  <!-- GF2E to_GF2E( const GF2X & a ) -->
  <function name="to_GF2E">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="GF2E"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- const sp_ZZ_reduce_struct & GetFFT_ZZ_red_struct( long i ) -->
  <function name="GetFFT_ZZ_red_struct">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const sp_ZZ_reduce_struct &amp;"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void VectorConv( long k, zz_p * x, const long * a ) -->
  <function name="VectorConv">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void VectorRandom( long k, zz_p * x ) -->
  <function name="VectorRandom">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- zz_p random_zz_p( void ) -->
  <function name="random_zz_p">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="zz_p"/>
    <use-retval/>
  </function>
  <!-- void swap( zz_p & x, zz_p & y ) -->
  <function name="swap">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- zz_p to_zz_p( long a ) -->
  <function name="to_zz_p">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="zz_p"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- zz_pEX BuildIrred_zz_pEX( long n ) -->
  <function name="BuildIrred_zz_pEX">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="zz_pEX"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ZZ_pEX random_ZZ_pEX( long n ) -->
  <function name="random_ZZ_pEX">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ZZ_pEX"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ZZ_pEX to_ZZ_pEX( long a ) -->
  <function name="to_ZZ_pEX">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ZZ_pEX"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ZZ_p random_ZZ_p( void ) -->
  <function name="random_ZZ_p">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ZZ_p"/>
    <use-retval/>
  </function>
  <!-- ZZ_p to_ZZ_p( const ZZ & a ) -->
  <function name="to_ZZ_p">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ZZ_p"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- GF2 random_GF2( void ) -->
  <function name="random_GF2">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="GF2"/>
    <use-retval/>
  </function>
  <!-- GF2 to_GF2( long a ) -->
  <function name="to_GF2">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="GF2"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void HenselSolve1( ZZ & d_out, vec_ZZ & x_out, const mat_ZZ & A, const vec_ZZ & b ) -->
  <function name="HenselSolve1">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in"/>
  </function>
  <!-- mat_ZZ ident_mat_ZZ( long n ) -->
  <function name="ident_mat_ZZ">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="mat_ZZ"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void solve1( ZZ & d_out, vec_ZZ & x_out, const mat_ZZ & A, const vec_ZZ & b ) -->
  <function name="solve1">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in"/>
  </function>
  <!-- mat_ZZ_p to_mat_ZZ_p( const mat_ZZ & a ) -->
  <function name="to_mat_ZZ_p">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="mat_ZZ_p"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- mat_zz_p to_mat_zz_p( const mat_ZZ & a ) -->
  <function name="to_mat_zz_p">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="mat_zz_p"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void BytesFromGF2X( unsigned char * p, const GF2X & a, long n ) -->
  <function name="BytesFromGF2X">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void CopyReverse( GF2X & c, const GF2X & a, long hi ) -->
  <function name="CopyReverse">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void MinPolyInternal( GF2X & h, const GF2X & x, long m ) -->
  <function name="MinPolyInternal">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void OldGCD( GF2X & d, const GF2X & a, const GF2X & b ) -->
  <function name="OldGCD">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void OldMinPolyInternal( GF2X & h, const GF2X & x, long m ) -->
  <function name="OldMinPolyInternal">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void OldMul( GF2X & c, const GF2X & a, const GF2X & b ) -->
  <function name="OldMul">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void OldXGCD( GF2X & d, GF2X & s, GF2X & t, const GF2X & a, const GF2X & b ) -->
  <function name="OldXGCD">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="inout"/>
    <arg nr="4" direction="in"/>
    <arg nr="5" direction="in"/>
  </function>
  <!-- GF2X random_GF2X( long n ) -->
  <function name="random_GF2X">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="GF2X"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- GF2X to_GF2X( long a ) -->
  <function name="to_GF2X">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="GF2X"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- vec_GF2 to_vec_GF2( const GF2X & a ) -->
  <function name="to_vec_GF2">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="vec_GF2"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- vec_zz_pE random_vec_zz_pE( long n ) -->
  <function name="random_vec_zz_pE">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="vec_zz_pE"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- mat_zz_pE ident_mat_zz_pE( long n ) -->
  <function name="ident_mat_zz_pE">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="mat_zz_pE"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- mat_zz_pE random_mat_zz_pE( long n, long m ) -->
  <function name="random_mat_zz_pE">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="mat_zz_pE"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- const zz_pX & _zz_pE__rep( const zz_pE & a ) -->
  <function name="_zz_pE__rep">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const zz_pX &amp;"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- zz_pE random_zz_pE( void ) -->
  <function name="random_zz_pE">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="zz_pE"/>
    <use-retval/>
  </function>
  <!-- zz_pE to_zz_pE( const zz_pX & a ) -->
  <function name="to_zz_pE">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="zz_pE"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- GF2X BuildIrred_GF2X( long n ) -->
  <function name="BuildIrred_GF2X">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="GF2X"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void BuildSparseIrred( GF2X & f, long n ) -->
  <function name="BuildSparseIrred">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- GF2X BuildSparseIrred_GF2X( long n ) -->
  <function name="BuildSparseIrred_GF2X">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="GF2X"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void DDF( vec_pair_GF2X_long & factors, const GF2X & ff, long verbose = 0 ) -->
  <function name="DDF">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void EDF( vec_GF2X & factors, const GF2X & ff, long d, long verbose = 0 ) -->
  <function name="EDF">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void CharPoly( ZZX & f, const mat_ZZ & M, long deterministic = 0 ) -->
  <function name="CharPoly">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void _ntl_ForceToMem( double * p ) -->
  <function name="_ntl_ForceToMem">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- double _ntl_GetWallTime( void ) -->
  <function name="_ntl_GetWallTime">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="double"/>
    <use-retval/>
  </function>
  <!-- long _ntl_IsFinite( double * p ) -->
  <function name="_ntl_IsFinite">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- double _ntl_ldexp( double x, long e ) -->
  <function name="_ntl_ldexp">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="double"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>-1.79769e+308:1.79769e+308</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- char * _ntl_make_aligned( char * p, long align ) -->
  <function name="_ntl_make_aligned">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="char *"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long _ntl_vec_grow( long n ) -->
  <function name="_ntl_vec_grow">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void ComputeGS( const mat_ZZ & B, mat_RR & mu, vec_RR & c ) -->
  <function name="ComputeGS">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- long LLL( ZZ & det, mat_ZZ & B, long verbose = 0 ) -->
  <function name="LLL">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long LLL_plus( vec_ZZ & D, mat_ZZ & B, mat_ZZ & U, long verbose = 0 ) -->
  <function name="LLL_plus">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="inout"/>
    <arg nr="4" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long LatticeSolve( vec_ZZ & x, const mat_ZZ & A, const vec_ZZ & y, long reduce = 0 ) -->
  <function name="LatticeSolve">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void NearVector( vec_ZZ & ww, const mat_ZZ & BB, const vec_ZZ & a ) -->
  <function name="NearVector">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in"/>
  </function>
  <!-- void CharPoly( ZZ_pX & f, const mat_ZZ_p & M ) -->
  <function name="CharPoly">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
  </function>
</def>
