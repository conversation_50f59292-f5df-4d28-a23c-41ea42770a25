<?xml version="1.0"?>
<def format="2">
  <!-- OpenCV (Open Source Computer Vision Library) Library Configuration http://opencv.org) -->
  <!-- This configuration is for the OpenCV 2.x API (The C++ API, not the older C API) -->
  <!-- The OpenCV library is typically included by '#include <opencv2/*>' or -->
  <!-- '#include "opencv2/*"'. -->
  <!-- ########## OpenCV Types ########## -->
  <podtype name="cv::int8_t" sign="s" size="1"/>
  <podtype name="cv::int16_t" sign="s" size="2"/>
  <podtype name="cv::int32_t" sign="s" size="4"/>
  <podtype name="cv::int64_t" sign="s" size="8"/>
  <podtype name="cv::uint8_t" sign="u" size="1"/>
  <podtype name="cv::uint16_t" sign="u" size="2"/>
  <podtype name="cv::uint32_t" sign="u" size="4"/>
  <podtype name="cv::uint64_t" sign="u" size="8"/>
  <smart-pointer class-name="cv::Ptr"/>
  <!-- ########## OpenCV Macros / Defines ########## -->
  <define name="CV_ALWAYS_INLINE" value="inline"/>
  <define name="CV_EXTERN_C" value="extern &quot;C&quot;"/>
  <define name="CV_OVERRIDE" value="override"/>
  <define name="CV_PI" value="3.1415926535897932384626433832795"/>
  <define name="CV_2PI" value="6.283185307179586476925286766559"/>
  <define name="CV_LOG2" value="0.69314718055994530941723212145818"/>
  <define name="CV_MAT_CN_MASK" value="((CV_CN_MAX - 1) &lt;&lt; CV_CN_SHIFT)"/>
  <define name="CV_MAT_CN(flags)" value="((((flags) &amp; CV_MAT_CN_MASK) &gt;&gt; CV_CN_SHIFT) + 1)"/>
  <define name="CV_MAT_TYPE_MASK" value="(CV_DEPTH_MAX*CV_CN_MAX - 1)"/>
  <define name="CV_MAT_TYPE(flags)" value="((flags) &amp; CV_MAT_TYPE_MASK)"/>
  <define name="CV_MAT_CONT_FLAG_SHIFT" value="14"/>
  <define name="CV_MAT_CONT_FLAG" value="(1 &lt;&lt; CV_MAT_CONT_FLAG_SHIFT)"/>
  <define name="CV_IS_MAT_CONT(flags)" value="((flags) &amp; CV_MAT_CONT_FLAG)"/>
  <define name="CV_IS_CONT_MAT" value="CV_IS_MAT_CONT"/>
  <define name="CV_SUBMAT_FLAG_SHIFT" value="15"/>
  <define name="CV_SUBMAT_FLAG" value="(1 &lt;&lt; CV_SUBMAT_FLAG_SHIFT)"/>
  <define name="CV_IS_SUBMAT(flags)" value="((flags) &amp; CV_MAT_SUBMAT_FLAG)"/>
  <define name="MIN(a,b)" value="((a) &gt; (b) ? (b) : (a))"/>
  <define name="MAX(a,b)" value="((a) &lt; (b) ? (b) : (a))"/>
  <define name="CV_Error(code,msg)" value="cv::error( code, msg, CV_Func, __FILE__, __LINE__ )"/>
  <define name="CV_Assert(expr)" value="do { if(!!(expr)) ; else cv::error( cv::Error::StsAssert, #expr, CV_Func, __FILE__, __LINE__ ); } while(0)"/>
  <!-- ########## OpenCV containers ########## -->
  <!-- OpenCV containers that are similar to std containers -->
  <container id="cvString" startPattern="cv :: String" inherits="stdString"/>
  <!-- ########## OpenCV Allocation / Deallocation ########## -->
  <memory>
    <alloc init="false" buffer-size="malloc">cv::fastMalloc</alloc>
    <dealloc>cv::fastFree</dealloc>
  </memory>
  <!-- ########## OpenCV Functions ########## -->
  <!-- void cv::errorNoReturn	(int _code, const String & _err, const char * _func, const char * _file, int _line) -->
  <function name="cv::errorNoReturn">
    <noreturn>true</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void cv::fastFree (void *ptr) -->
  <function name="cv::fastFree">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- void * cv::fastMalloc (size_t bufSize) -->
  <function name="cv::fastMalloc">
    <noreturn>false</noreturn>
    <returnValue type="void *"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- Mat cv::imread ( const String & filename, int flags = IMREAD_COLOR ) -->
  <function name="cv::imread">
    <noreturn>false</noreturn>
    <returnValue type="cv::Mat"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="cv::IMREAD_COLOR">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void cv::imshow ( const String & winname, InputArray mat ) -->
  <!-- void cv::imshow ( const String & winname, const ogl::Texture2D & tex ) -->
  <function name="cv::imshow">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void cv::namedWindow ( const String & winname, int flags = WINDOW_AUTOSIZE ) -->
  <function name="cv::namedWindow">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="cv::WINDOW_AUTOSIZE">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int cv::waitKey ( int delay = 0 ) -->
  <function name="cv::waitKey">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1" direction="in" default="0">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
</def>
