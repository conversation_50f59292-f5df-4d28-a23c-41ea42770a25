<?xml version="1.0"?>
<def format="2">
  <!-- OpenGL Library Configuration -->
  <!-- OpenGL library is typically included by "#include <gl/GL.h>" or "#include <GL/gl.h>", but can
       also be indirectly include by the GLU or GLUT library ("#include <GL/glu.h>" or
       "#include <GL/glut.h>")-->
  <!-- ########## OpenGL Types ########## -->
  <!-- https://www.khronos.org/opengl/wiki/OpenGL_Type -->
  <podtype name="GLboolean" sign="u" size="1"/>
  <podtype name="GLbyte" sign="s" size="1"/>
  <podtype name="GLubyte" sign="u" size="1"/>
  <podtype name="GLshort" sign="s" size="2"/>
  <podtype name="GLushort" sign="u" size="2"/>
  <podtype name="GLint" sign="s" size="4"/>
  <podtype name="GLuint" sign="u" size="4"/>
  <!-- TODO: GLfixed -->
  <podtype name="GLint64" sign="s" size="8"/>
  <podtype name="GLuint64" sign="u" size="8"/>
  <podtype name="GLsizei" sign="u" size="4"/>
  <podtype name="GLenum" sign="u" size="4"/>
  <!-- TODO: GLintptr -->
  <!-- TODO: GLsizeiptr -->
  <!-- TODO: GLsync -->
  <podtype name="GLbitfield" sign="u" size="4"/>
  <!-- TODO: GLhalf -->
  <define name="GLfloat" value="float"/>
  <define name="GLclampf" value="float"/>
  <define name="GLdouble" value="double"/>
  <define name="GLclampd" value="double"/>
  <!-- ########## OpenGL values / defines ########## -->
  <define name="GL_TRUE" value="1"/>
  <define name="GL_FALSE" value="0"/>
  <!-- ########## OpenGL Functions ########## -->
  <!-- https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/glBegin.xml -->
  <!-- void glBegin( GLenum mode ); -->
  <function name="glBegin">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.khronos.org/registry/OpenGL-Refpages/gl4/html/glBindTexture.xhtml -->
  <!-- void glBindTexture( GLenum target, GLuint texture); -->
  <function name="glBindTexture">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/glClear.xml -->
  <!-- void glClear( GLbitfield mask ); -->
  <function name="glClear">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/glColor.xml -->
  <!-- void glColor3b( GLbyte red, GLbyte green, GLbyte blue ); -->
  <!-- void glColor3s( GLshort red, GLshort green, GLshort blue ); -->
  <!-- void glColor3i( GLint red, GLint green, GLint blue ); -->
  <!-- void glColor3f( GLfloat red, GLfloat green, GLfloat blue ); -->
  <!-- void glColor3d( GLdouble red, GLdouble green, GLdouble blue ); -->
  <!-- void glColor3ub( GLubyte red, GLubyte green, GLubyte blue ); -->
  <!-- void glColor3us( GLushort red, GLushort green, GLushort blue ); -->
  <!-- void glColor3ui( GLuint red, GLuint green, GLuint blue ); -->
  <function name="glColor3b,glColor3s,glColor3i,glColor3f,glColor3d,glColor3ub,glColor3us,glColor3ui">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/glColor.xml -->
  <!-- void glColor4b( GLbyte red, GLbyte green, GLbyte blue, GLbyte alpha ); -->
  <!-- void glColor4s( GLshort red, GLshort green, GLshort blue, GLshort alpha ); -->
  <!-- void glColor4i( GLint red, GLint green, GLint blue, GLint alpha ); -->
  <!-- void glColor4f( GLfloat red, GLfloat green, GLfloat blue, GLfloat alpha ); -->
  <!-- void glColor4d( GLdouble red, GLdouble green, GLdouble blue, GLdouble alpha ); -->
  <!-- void glColor4ub( GLubyte red, GLubyte green, GLubyte blue, GLubyte alpha ); -->
  <!-- void glColor4us( GLushort red, GLushort green, GLushort blue, GLushort alpha ); -->
  <!-- void glColor4ui( GLuint red, GLuint green, GLuint blue, GLuint alpha ); -->
  <function name="glColor4b,glColor4s,glColor4i,glColor4f,glColor4d,glColor4ub,glColor4us,glColor4ui">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/glColor.xml -->
  <!-- void glColor3bv( const GLbyte * v ); -->
  <!-- void glColor3sv( const GLshort * v ); -->
  <!-- void glColor3iv( const GLint * v ); -->
  <!-- void glColor3fv( const GLfloat * v ); -->
  <!-- void glColor3dv( const GLdouble * v ); -->
  <!-- void glColor3ubv( const GLubyte * v ); -->
  <!-- void glColor3usv( const GLushort * v ); -->
  <!-- void glColor3uiv( const GLuint * v ); -->
  <!-- void glColor4bv( const GLbyte * v ); -->
  <!-- void glColor4sv( const GLshort * v ); -->
  <!-- void glColor4iv( const GLint * v ); -->
  <!-- void glColor4fv( const GLfloat * v ); -->
  <!-- void glColor4dv( const GLdouble * v ); -->
  <!-- void glColor4ubv( const GLubyte * v ); -->
  <!-- void glColor4usv( const GLushort * v ); -->
  <!-- void glColor4uiv( const GLuint * v ); -->
  <function name="glColor3bv,glColor3sv,glColor3iv,glColor3fv,glColor3dv,glColor3ubv,glColor3usv,glColor3uiv,glColor4bv,glColor4sv,glColor4iv,glColor4fv,glColor4dv,glColor4ubv,glColor4usv,glColor4uiv">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/glEnable.xml -->
  <!-- void glDisable( GLenum cap); -->
  <function name="glDisable">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/glEnable.xml -->
  <!-- void glEnable( GLenum cap ); -->
  <function name="glEnable">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/glBegin.xml -->
  <!-- void glEnd(void); -->
  <function name="glEnd">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
  </function>
  <!-- https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/glGet.xml -->
  <!-- void glGetBooleanv( GLenum pname, GLboolean * params); -->
  <!-- void glGetDoublev( GLenum pname, GLdouble * params); -->
  <!-- void glGetFloatv( GLenum pname, GLfloat * params); -->
  <!-- void glGetIntegerv( GLenum pname, GLint * params); -->
  <function name="glGetBooleanv,glGetDoublev,glGetFloatv,glGetIntegerv">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.khronos.org/registry/OpenGL-Refpages/gl4/html/glGetString.xhtml -->
  <!-- const GLubyte *glGetString( GLenum name); -->
  <function name="glGetString">
    <noreturn>false</noreturn>
    <returnValue type="const GLubyte *"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- const GLubyte *glGetStringi( GLenum name, GLuint index); -->
  <function name="glGetStringi">
    <noreturn>false</noreturn>
    <returnValue type="const GLubyte *"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/glIsEnabled.xml -->
  <!-- GLboolean glIsEnabled( GLenum cap ); -->
  <function name="glIsEnabled">
    <noreturn>false</noreturn>
    <returnValue type="GLboolean"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/glLoadMatrix.xml -->
  <!-- void glLoadMatrixd( const GLdouble * m); -->
  <!-- void glLoadMatrixf( const GLfloat * m); -->
  <function name="glLoadMatrixd,glLoadMatrixf">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/glMaterial.xml -->
  <!-- void glMaterialf( GLenum face, GLenum pname, GLfloat param); -->
  <!-- void glMateriali( GLenum face, GLenum pname, GLint param); -->
  <function name="glMaterialf,glMateriali">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void glMaterialfv( GLenum face, GLenum pname, const GLfloat * params); -->
  <!-- void glMaterialiv( GLenum face, GLenum pname, const GLint * params); -->
  <function name="glMaterialfv,glMaterialiv">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/glMatrixMode.xml -->
  <!-- void glMatrixMode( GLenum mode); -->
  <function name="glMatrixMode">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/glNormal.xml -->
  <!-- void glNormal3b( GLbyte nx, GLbyte ny, GLbyte nz ); -->
  <!-- void glNormal3d( GLdouble nx, GLdouble ny, GLdouble nz ); -->
  <!-- void glNormal3f( GLfloat nx, GLfloat ny, GLfloat nz ); -->
  <!-- void glNormal3i( GLint nx, GLint ny, GLint nz ); -->
  <!-- void glNormal3s( GLshort nx, GLshort ny, GLshort nz ); -->
  <function name="glNormal3b,glNormal3d,glNormal3f,glNormal3i,glNormal3s">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/glNormal.xml -->
  <!-- void glNormal3bv( const GLbyte * v ); -->
  <!-- void glNormal3dv( const GLdouble * v ); -->
  <!-- void glNormal3fv( const GLfloat * v ); -->
  <!-- void glNormal3iv( const GLint * v ); -->
  <!-- void glNormal3sv( const GLshort * v ); -->
  <function name="glNormal3bv,glNormal3dv,glNormal3fv,glNormal3iv,glNormal3sv">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/glPushMatrix.xml -->
  <!-- void glPopMatrix( void); -->
  <function name="glPopMatrix">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
  </function>
  <!-- https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/glPushMatrix.xml -->
  <!-- void glPushMatrix( void); -->
  <function name="glPushMatrix">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
  </function>
  <!-- https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/glRotate.xml -->
  <!-- void glRotated( GLdouble angle, GLdouble x, GLdouble y, GLdouble z ); -->
  <!-- void glRotatef( GLfloat angle, GLfloat x, GLfloat y, GLfloat z ); -->
  <function name="glRotated,glRotatef">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/glTexCoord.xml -->
  <!-- void glTexCoord1s( GLshort s ); -->
  <!-- void glTexCoord1i( GLint s ); -->
  <!-- void glTexCoord1f( GLfloat s ); -->
  <!-- void glTexCoord1d( GLdouble s ); -->
  <function name="glTexCoord1s,glTexCoord1i,glTexCoord1f,glTexCoord1d">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void glTexCoord2s( GLshort s, GLshort t ); -->
  <!-- void glTexCoord2i( GLint s, GLint t ); -->
  <!-- void glTexCoord2f( GLfloat s, GLfloat t ); -->
  <!-- void glTexCoord2d( GLdouble s, GLdouble t ); -->
  <function name="glTexCoord2s,glTexCoord2i,glTexCoord2f,glTexCoord2d">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void glTexCoord3s( GLshort s, GLshort t, GLshort r ); -->
  <!-- void glTexCoord3i( GLint s, GLint t, GLint r ); -->
  <!-- void glTexCoord3f( GLfloat s, GLfloat t, GLfloat r ); -->
  <!-- void glTexCoord3d( GLdouble s, GLdouble t, GLdouble r ); -->
  <function name="glTexCoord3s,glTexCoord3i,glTexCoord3f,glTexCoord3d">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void glTexCoord4s( GLshort s, GLshort t, GLshort r, GLshort q ); -->
  <!-- void glTexCoord4i( GLint s, GLint t, GLint r, GLint q ); -->
  <!-- void glTexCoord4f( GLfloat s, GLfloat t, GLfloat r, GLfloat q ); -->
  <!-- void glTexCoord4d( GLdouble s, GLdouble t, GLdouble r, GLdouble q ); -->
  <function name="glTexCoord4s,glTexCoord4i,glTexCoord4f,glTexCoord4d">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void glTexCoord1sv( const GLshort * v ); -->
  <!-- void glTexCoord1iv( const GLint * v ); -->
  <!-- void glTexCoord1fv( const GLfloat * v ); -->
  <!-- void glTexCoord1dv( const GLdouble * v ); -->
  <!-- void glTexCoord2sv( const GLshort * v ); -->
  <!-- void glTexCoord2iv( const GLint * v ); -->
  <!-- void glTexCoord2fv( const GLfloat * v ); -->
  <!-- void glTexCoord2dv( const GLdouble * v ); -->
  <!-- void glTexCoord3sv( const GLshort * v ); -->
  <!-- void glTexCoord3iv( const GLint * v ); -->
  <!-- void glTexCoord3fv( const GLfloat * v ); -->
  <!-- void glTexCoord3dv( const GLdouble * v ); -->
  <!-- void glTexCoord4sv( const GLshort * v ); -->
  <!-- void glTexCoord4iv( const GLint * v ); -->
  <!-- void glTexCoord4fv( const GLfloat * v ); -->
  <!-- void glTexCoord4dv( const GLdouble * v ); -->
  <function name="glTexCoord1sv,glTexCoord1iv,glTexCoord1fv,glTexCoord1dv,glTexCoord2sv,glTexCoord2iv,glTexCoord2fv,glTexCoord2dv,glTexCoord3sv,glTexCoord3iv,glTexCoord3fv,glTexCoord4sv,glTexCoord4iv,glTexCoord4fv,glTexCoord4dv">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.khronos.org/registry/OpenGL-Refpages/gl4/html/glTexParameter.xhtml -->
  <!-- void glTexParameterf( GLenum target, GLenum pname, GLfloat param); -->
  <!-- void glTexParameteri( GLenum target, GLenum pname, GLint param); -->
  <!-- void glTextureParameterf( GLuint texture, GLenum pname, GLfloat param); -->
  <!-- void glTextureParameteri( GLuint texture, GLenum pname, GLint param); -->
  <function name="glTexParameterf,glTexParameteri,glTextureParameterf,glTextureParameteri">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- https://www.khronos.org/registry/OpenGL-Refpages/gl4/html/glTexParameter.xhtml -->
  <!-- void glTexParameterfv( GLenum target, GLenum pname, const GLfloat * params); -->
  <!-- void glTexParameteriv( GLenum target, GLenum pname, const GLint * params); -->
  <!-- void glTexParameterIiv( GLenum target, GLenum pname, const GLint * params); -->
  <!-- void glTexParameterIuiv( GLenum target, GLenum pname, const GLuint * params); -->
  <!-- void glTextureParameterfv( GLuint texture, GLenum pname, const GLfloat *params); -->
  <!-- void glTextureParameteriv( GLuint texture, GLenum pname, const GLint *params); -->
  <!-- void glTextureParameterIiv( GLuint texture, GLenum pname, const GLint *params); -->
  <!-- void glTextureParameterIuiv( GLuint texture, GLenum pname, const GLuint *params); -->
  <function name="glTexParameterfv,glTexParameteriv,glTexParameterIiv,glTexParameterIuiv,glTextureParameterfv,glTextureParameteriv,glTextureParameterIiv,glTextureParameterIuiv">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/glTranslate.xml -->
  <!-- void glTranslated( GLdouble x, GLdouble y, GLdouble z); -->
  <!-- void glTranslatef( GLfloat x, GLfloat y, GLfloat z); -->
  <function name="glTranslated,glTranslatef">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/glUseProgram.xml -->
  <!-- void glUseProgram( GLuint program); -->
  <function name="glUseProgram">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.khronos.org/registry/OpenGL-Refpages/gl2.1/xhtml/glVertex.xml -->
  <!-- void glVertex2s( GLshort x, GLshort y ); -->
  <!-- void glVertex2i( GLint x, GLint y ); -->
  <!-- void glVertex2f( GLfloat x, GLfloat y ); -->
  <!-- void glVertex2d( GLdouble x, GLdouble y );-->
  <function name="glVertex2s,glVertex2i,glVertex2f,glVertex2d">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void glVertex3s( GLshort x, GLshort y, GLshort z ); -->
  <!-- void glVertex3i(	GLint x, GLint y, GLint z ); -->
  <!-- void glVertex3f( GLfloat x, GLfloat y, GLfloat z ); -->
  <!-- void glVertex3d(	GLdouble x, GLdouble y, GLdouble z); -->
  <function name="glVertex3s,glVertex3i,glVertex3f,glVertex3d">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void glVertex4s( GLshort x, GLshort y, GLshort z, GLshort w ); -->
  <!-- void glVertex4i(	GLint x, GLint y, GLint z, GLint w ); -->
  <!-- void glVertex4f( GLfloat x, GLfloat y, GLfloat z, GLfloat w ); -->
  <!-- void glVertex4d(	GLdouble x, GLdouble y, GLdouble z, GLdouble w); -->
  <function name="glVertex4s,glVertex4i,glVertex4f,glVertex4d">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void glVertex2sv( const GLshort * v ); -->
  <!-- void glVertex2iv( const GLint * v ); -->
  <!-- void glVertex2fv( const GLfloat * v ); -->
  <!-- void glVertex2dv( const GLdouble * v ); -->
  <!-- void glVertex3sv( const GLshort * v ); -->
  <!-- void glVertex3iv( const GLint * v ); -->
  <!-- void glVertex3fv( const GLfloat * v ); -->
  <!-- void glVertex3dv( const GLdouble * v ); -->
  <!-- void glVertex4sv( const GLshort * v ); -->
  <!-- void glVertex4iv( const GLint * v ); -->
  <!-- void glVertex4fv( const GLfloat * v ); -->
  <!-- void glVertex4dv( const GLdouble * v ); -->
  <function name="glVertex2sv,glVertex2iv,glVertex2fv,glVertex2dv,glVertex3sv,glVertex3iv,glVertex3fv,glVertex3dv,glVertex4sv,glVertex4iv,glVertex4fv,glVertex4dv">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
</def>
