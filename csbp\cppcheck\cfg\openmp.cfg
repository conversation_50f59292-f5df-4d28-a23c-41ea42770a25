<?xml version="1.0"?>
<def format="2">
  <!-- OpenMP Library Configuration -->
  <!-- Specifications can be found here: https://www.openmp.org/specifications/ -->
  <!-- OpenMP library is typically included by "#include <omp.h>" -->
  <!-- ########## OpenMP Types ########## -->
  <podtype name="ompt_id_t" sign="u" size="8"/>
  <podtype name="ompt_device_time_t" sign="u" size="8"/>
  <podtype name="ompt_buffer_cursor_t" sign="u" size="8"/>
  <podtype name="ompt_hwid_t" sign="u" size="8"/>
  <podtype name="ompt_wait_id_t" sign="u" size="8"/>
  <podtype name="ompd_size_t" sign="u" size="8"/>
  <podtype name="ompd_wait_id_t" sign="u" size="8"/>
  <podtype name="ompd_addr_t" sign="u" size="8"/>
  <podtype name="ompd_word_t" sign="s" size="8"/>
  <podtype name="ompd_seg_t" sign="u" size="8"/>
  <podtype name="ompd_thread_id_t" sign="u" size="8"/>
  <podtype name="ompd_icv_id_t" sign="u" size="8"/>
  <!-- ########## OpenMP Macros / Defines ########## -->
  <!-- ########## OpenMP Allocation / Deallocation ########## -->
  <memory>
    <alloc buffer-size="malloc">omp_target_alloc</alloc>
    <dealloc>omp_target_free</dealloc>
  </memory>
  <memory>
    <alloc buffer-size="malloc">omp_alloc</alloc>
    <dealloc>omp_free</dealloc>
  </memory>
  <!-- ########## OpenMP Functions ########## -->
  <!-- int omp_get_cancellation(void); -->
  <function name="omp_get_cancellation">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
  </function>
  <!-- int omp_get_dynamic(void); -->
  <function name="omp_get_dynamic">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
  </function>
  <!-- int omp_get_max_threads(void); -->
  <function name="omp_get_max_threads">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
  </function>
  <!-- int omp_get_nested(void); -->
  <function name="omp_get_nested">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <warn severity="style">This routine has been deprecated. See OpenMP specification.</warn>
  </function>
  <!-- int omp_get_num_procs(void); -->
  <function name="omp_get_num_procs">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
  </function>
  <!-- int omp_get_num_threads(void); -->
  <function name="omp_get_num_threads">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
  </function>
  <!-- int omp_get_thread_num(void); -->
  <function name="omp_get_thread_num">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
  </function>
  <!-- double omp_get_wtick(void); -->
  <function name="omp_get_wtick">
    <noreturn>false</noreturn>
    <returnValue type="double"/>
    <use-retval/>
    <leak-ignore/>
  </function>
  <!-- double omp_get_wtime(void); -->
  <function name="omp_get_wtime">
    <noreturn>false</noreturn>
    <returnValue type="double"/>
    <use-retval/>
    <leak-ignore/>
  </function>
  <!-- int omp_in_parallel(void); -->
  <function name="omp_in_parallel">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
  </function>
  <!-- void omp_set_dynamic(int dynamic_threads); -->
  <function name="omp_set_dynamic">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void omp_set_nested(int nested); -->
  <function name="omp_set_nested">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <warn severity="style">This routine has been deprecated. See OpenMP specification.</warn>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void omp_set_num_threads(int num_threads); -->
  <function name="omp_set_num_threads">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>1:</valid>
    </arg>
  </function>
  <!-- void* omp_target_alloc(size_t size, int device_num); -->
  <function name="omp_target_alloc">
    <noreturn>false</noreturn>
    <returnValue type="void *"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- void omp_target_free(void *device_ptr, int device_num); -->
  <function name="omp_target_free">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
</def>
