<?xml version="1.0"?>
<def format="2">
  <!-- OpenSSL Library Configuration https://www.openssl.org/ -->
  <!-- The OpenSSL library is typically included by "#include <openssl/*.h>" -->
  <!-- ########## OpenSSL Types ########## -->
  <!-- ########## OpenSSL Macros / Defines ########## -->
  <!-- ########## OpenSSL Allocation / Deallocation ########## -->
  <resource>
    <alloc>EVP_CIPHER_CTX_new</alloc>
    <dealloc>EVP_CIPHER_CTX_free</dealloc>
  </resource>
  <!-- ########## OpenSSL Functions ########## -->
  <!-- int BIO_free(BIO *a); -->
  <function name="BIO_free">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- BIO * BIO_new(const BIO_METHOD *type); -->
  <function name="BIO_new">
    <noreturn>false</noreturn>
    <returnValue type="BIO *"/>
    <use-retval/>
    <arg nr="1"/>
  </function>
  <!-- int BIO_printf(BIO *bio, const char *format, ...) -->
  <function name="BIO_printf">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <formatstr/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <formatstr/>
    </arg>
  </function>
  <!-- int BIO_snprintf(char *buf, size_t n, const char *format, ...) -->
  <function name="BIO_snprintf">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <formatstr/>
    <arg nr="3" direction="in">
      <formatstr/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- void BIO_vfree(BIO *a); -->
  <function name="BIO_vfree">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- int BIO_vprintf(BIO *bio, const char *format, va_list args) -->
  <function name="BIO_vprintf">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <formatstr/>
    </arg>
    <arg nr="3"/>
  </function>
  <!-- int BIO_vsnprintf(char *buf, size_t n, const char *format, va_list args) -->
  <function name="BIO_vsnprintf">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-null/>
      <not-uninit/>
      <formatstr/>
    </arg>
    <arg nr="4"/>
  </function>
  <!-- void EVP_CIPHER_CTX_free(EVP_CIPHER_CTX *ctx); -->
  <function name="EVP_CIPHER_CTX_free">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1"/>
  </function>
  <!-- EVP_CIPHER_CTX *EVP_CIPHER_CTX_new(void); -->
  <function name="EVP_CIPHER_CTX_new">
    <noreturn>false</noreturn>
    <returnValue type="EVP_CIPHER_CTX *"/>
    <use-retval/>
  </function>
  <!-- int EVP_CIPHER_CTX_reset(EVP_CIPHER_CTX *ctx); -->
  <function name="EVP_CIPHER_CTX_reset">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1">
      <not-bool/>
    </arg>
  </function>
  <!-- int EVP_EncryptFinal_ex(EVP_CIPHER_CTX *ctx, unsigned char *out, int *outl); -->
  <function name="EVP_EncryptFinal_ex">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1"/>
    <arg nr="2" direction="out"/>
    <arg nr="3" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- int EVP_EncryptInit_ex(EVP_CIPHER_CTX *ctx, const EVP_CIPHER *type,
                              ENGINE *impl, const unsigned char *key, const unsigned char *iv); -->
  <function name="EVP_EncryptInit_ex">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int EVP_EncryptUpdate(EVP_CIPHER_CTX *ctx, unsigned char *out,
                             int *outl, const unsigned char *in, int inl); -->
  <function name="EVP_EncryptUpdate">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1"/>
    <arg nr="2" direction="out"/>
    <arg nr="3" direction="out"/>
    <arg nr="4" direction="in">
      <not-uninit/>
      <minsize type="argvalue" arg="5"/>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
</def>
