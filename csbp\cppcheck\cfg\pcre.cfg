<?xml version="1.0"?>
<def>
  <define name="PCRE_ANCHORED" value="0x00000010"/>
  <define name="PCRE_AUTO_CALLOUT" value="0x00004000"/>
  <define name="PCRE_BSR_ANYCRLF" value="0x00800000"/>
  <define name="PCRE_BSR_UNICODE" value="0x01000000"/>
  <define name="PCRE_CASELESS" value="0x00000001"/>
  <define name="PCRE_CONFIG_BSR" value="8"/>
  <define name="PCRE_CONFIG_JIT" value="9"/>
  <define name="PCRE_CONFIG_JITTARGET" value="11"/>
  <define name="PCRE_CONFIG_LINK_SIZE" value="2"/>
  <define name="PCRE_CONFIG_MATCH_LIMIT" value="4"/>
  <define name="PCRE_CONFIG_MATCH_LIMIT_RECURSION" value="7"/>
  <define name="PCRE_CONFIG_NEWLINE" value="1"/>
  <define name="PCRE_CONFIG_PARENS_LIMIT" value="13"/>
  <define name="PCRE_CONFIG_POSIX_MALLOC_THRESHOLD" value="3"/>
  <define name="PCRE_CONFIG_STACKRECURSE" value="5"/>
  <define name="PCRE_CONFIG_UNICODE_PROPERTIES" value="6"/>
  <define name="PCRE_CONFIG_UTF16" value="10"/>
  <define name="PCRE_CONFIG_UTF32" value="12"/>
  <define name="PCRE_CONFIG_UTF8" value="0"/>
  <define name="PCRE_DFA_RESTART" value="0x00020000"/>
  <define name="PCRE_DFA_SHORTEST" value="0x00010000"/>
  <define name="PCRE_DOLLAR_ENDONLY" value="0x00000020"/>
  <define name="PCRE_DOTALL" value="0x00000004"/>
  <define name="PCRE_DUPNAMES" value="0x00080000"/>
  <define name="PCRE_ERROR_BADCOUNT" value="(-15)"/>
  <define name="PCRE_ERROR_BADENDIANNESS" value="(-29)"/>
  <define name="PCRE_ERROR_BADLENGTH" value="(-32)"/>
  <define name="PCRE_ERROR_BADMAGIC" value="(-4)"/>
  <define name="PCRE_ERROR_BADMODE" value="(-28)"/>
  <define name="PCRE_ERROR_BADNEWLINE" value="(-23)"/>
  <define name="PCRE_ERROR_BADOFFSET" value="(-24)"/>
  <define name="PCRE_ERROR_BADOPTION" value="(-3)"/>
  <define name="PCRE_ERROR_BADPARTIAL" value="(-13)"/>
  <define name="PCRE_ERROR_BADUTF16" value="(-10)"/>
  <define name="PCRE_ERROR_BADUTF16_OFFSET" value="(-11)"/>
  <define name="PCRE_ERROR_BADUTF32" value="(-10)"/>
  <define name="PCRE_ERROR_BADUTF8" value="(-10)"/>
  <define name="PCRE_ERROR_BADUTF8_OFFSET" value="(-11)"/>
  <define name="PCRE_ERROR_CALLOUT" value="(-9)"/>
  <define name="PCRE_ERROR_DFA_BADRESTART" value="(-30)"/>
  <define name="PCRE_ERROR_DFA_RECURSE" value="(-20)"/>
  <define name="PCRE_ERROR_DFA_UCOND" value="(-17)"/>
  <define name="PCRE_ERROR_DFA_UITEM" value="(-16)"/>
  <define name="PCRE_ERROR_DFA_UMLIMIT" value="(-18)"/>
  <define name="PCRE_ERROR_DFA_WSSIZE" value="(-19)"/>
  <define name="PCRE_ERROR_INTERNAL" value="(-14)"/>
  <define name="PCRE_ERROR_JIT_BADOPTION" value="(-31)"/>
  <define name="PCRE_ERROR_JIT_STACKLIMIT" value="(-27)"/>
  <define name="PCRE_ERROR_MATCHLIMIT" value="(-8)"/>
  <define name="PCRE_ERROR_NOMATCH" value="(-1)"/>
  <define name="PCRE_ERROR_NOMEMORY" value="(-6)"/>
  <define name="PCRE_ERROR_NOSUBSTRING" value="(-7)"/>
  <define name="PCRE_ERROR_NULL" value="(-2)"/>
  <define name="PCRE_ERROR_NULLWSLIMIT" value="(-22)"/>
  <define name="PCRE_ERROR_PARTIAL" value="(-12)"/>
  <define name="PCRE_ERROR_RECURSELOOP" value="(-26)"/>
  <define name="PCRE_ERROR_RECURSIONLIMIT" value="(-21)"/>
  <define name="PCRE_ERROR_SHORTUTF16" value="(-25)"/>
  <define name="PCRE_ERROR_SHORTUTF8" value="(-25)"/>
  <define name="PCRE_ERROR_UNKNOWN_NODE" value="(-5)"/>
  <define name="PCRE_ERROR_UNKNOWN_OPCODE" value="(-5)"/>
  <define name="PCRE_ERROR_UNSET" value="(-33)"/>
  <define name="PCRE_EXTENDED" value="0x00000008"/>
  <define name="PCRE_EXTRA" value="0x00000040"/>
  <define name="PCRE_EXTRA_CALLOUT_DATA" value="0x0004"/>
  <define name="PCRE_EXTRA_EXECUTABLE_JIT" value="0x0040"/>
  <define name="PCRE_EXTRA_MARK" value="0x0020"/>
  <define name="PCRE_EXTRA_MATCH_LIMIT" value="0x0002"/>
  <define name="PCRE_EXTRA_MATCH_LIMIT_RECURSION" value="0x0010"/>
  <define name="PCRE_EXTRA_STUDY_DATA" value="0x0001"/>
  <define name="PCRE_EXTRA_TABLES" value="0x0008"/>
  <define name="PCRE_FIRSTLINE" value="0x00040000"/>
  <define name="PCRE_INFO_BACKREFMAX" value="3"/>
  <define name="PCRE_INFO_CAPTURECOUNT" value="2"/>
  <define name="PCRE_INFO_DEFAULT_TABLES" value="11"/>
  <define name="PCRE_INFO_FIRSTBYTE" value="4"/>
  <define name="PCRE_INFO_FIRSTCHAR" value="4"/>
  <define name="PCRE_INFO_FIRSTCHARACTER" value="19"/>
  <define name="PCRE_INFO_FIRSTCHARACTERFLAGS" value="20"/>
  <define name="PCRE_INFO_FIRSTTABLE" value="5"/>
  <define name="PCRE_INFO_HASCRORLF" value="14"/>
  <define name="PCRE_INFO_JCHANGED" value="13"/>
  <define name="PCRE_INFO_JIT" value="16"/>
  <define name="PCRE_INFO_JITSIZE" value="17"/>
  <define name="PCRE_INFO_LASTLITERAL" value="6"/>
  <define name="PCRE_INFO_MATCHLIMIT" value="23"/>
  <define name="PCRE_INFO_MATCH_EMPTY" value="25"/>
  <define name="PCRE_INFO_MAXLOOKBEHIND" value="18"/>
  <define name="PCRE_INFO_MINLENGTH" value="15"/>
  <define name="PCRE_INFO_NAMECOUNT" value="8"/>
  <define name="PCRE_INFO_NAMEENTRYSIZE" value="7"/>
  <define name="PCRE_INFO_NAMETABLE" value="9"/>
  <define name="PCRE_INFO_OKPARTIAL" value="12"/>
  <define name="PCRE_INFO_OPTIONS" value="0"/>
  <define name="PCRE_INFO_RECURSIONLIMIT" value="24"/>
  <define name="PCRE_INFO_REQUIREDCHAR" value="21"/>
  <define name="PCRE_INFO_REQUIREDCHARFLAGS" value="22"/>
  <define name="PCRE_INFO_SIZE" value="1"/>
  <define name="PCRE_INFO_STUDYSIZE" value="10"/>
  <define name="PCRE_JAVASCRIPT_COMPAT" value="0x02000000"/>
  <define name="PCRE_MULTILINE" value="0x00000002"/>
  <define name="PCRE_NEVER_UTF" value="0x00010000"/>
  <define name="PCRE_NEWLINE_ANY" value="0x00400000"/>
  <define name="PCRE_NEWLINE_ANYCRLF" value="0x00500000"/>
  <define name="PCRE_NEWLINE_CR" value="0x00100000"/>
  <define name="PCRE_NEWLINE_CRLF" value="0x00300000"/>
  <define name="PCRE_NEWLINE_LF" value="0x00200000"/>
  <define name="PCRE_NOTBOL" value="0x00000080"/>
  <define name="PCRE_NOTEMPTY" value="0x00000400"/>
  <define name="PCRE_NOTEMPTY_ATSTART" value="0x10000000"/>
  <define name="PCRE_NOTEOL" value="0x00000100"/>
  <define name="PCRE_NO_AUTO_CAPTURE" value="0x00001000"/>
  <define name="PCRE_NO_AUTO_POSSESS" value="0x00020000"/>
  <define name="PCRE_NO_START_OPTIMISE" value="0x04000000"/>
  <define name="PCRE_NO_START_OPTIMIZE" value="0x04000000"/>
  <define name="PCRE_NO_UTF16_CHECK" value="0x00002000"/>
  <define name="PCRE_NO_UTF32_CHECK" value="0x00002000"/>
  <define name="PCRE_NO_UTF8_CHECK" value="0x00002000"/>
  <define name="PCRE_PARTIAL" value="0x00008000"/>
  <define name="PCRE_PARTIAL_HARD" value="0x08000000"/>
  <define name="PCRE_PARTIAL_SOFT" value="0x00008000"/>
  <define name="PCRE_STUDY_EXTRA_NEEDED" value="0x0008"/>
  <define name="PCRE_STUDY_JIT_COMPILE" value="0x0001"/>
  <define name="PCRE_STUDY_JIT_PARTIAL_HARD_COMPILE" value="0x0004"/>
  <define name="PCRE_STUDY_JIT_PARTIAL_SOFT_COMPILE" value="0x0002"/>
  <define name="PCRE_UCP" value="0x20000000"/>
  <define name="PCRE_UNGREEDY" value="0x00000200"/>
  <define name="PCRE_UTF16" value="0x00000800"/>
  <define name="PCRE_UTF16_ERR0" value="0"/>
  <define name="PCRE_UTF16_ERR1" value="1"/>
  <define name="PCRE_UTF16_ERR2" value="2"/>
  <define name="PCRE_UTF16_ERR3" value="3"/>
  <define name="PCRE_UTF16_ERR4" value="4"/>
  <define name="PCRE_UTF32" value="0x00000800"/>
  <define name="PCRE_UTF32_ERR0" value="0"/>
  <define name="PCRE_UTF32_ERR1" value="1"/>
  <define name="PCRE_UTF32_ERR2" value="2"/>
  <define name="PCRE_UTF32_ERR3" value="3"/>
  <define name="PCRE_UTF8" value="0x00000800"/>
  <define name="PCRE_UTF8_ERR0" value="0"/>
  <define name="PCRE_UTF8_ERR1" value="1"/>
  <define name="PCRE_UTF8_ERR10" value="10"/>
  <define name="PCRE_UTF8_ERR11" value="11"/>
  <define name="PCRE_UTF8_ERR12" value="12"/>
  <define name="PCRE_UTF8_ERR13" value="13"/>
  <define name="PCRE_UTF8_ERR14" value="14"/>
  <define name="PCRE_UTF8_ERR15" value="15"/>
  <define name="PCRE_UTF8_ERR16" value="16"/>
  <define name="PCRE_UTF8_ERR17" value="17"/>
  <define name="PCRE_UTF8_ERR18" value="18"/>
  <define name="PCRE_UTF8_ERR19" value="19"/>
  <define name="PCRE_UTF8_ERR2" value="2"/>
  <define name="PCRE_UTF8_ERR20" value="20"/>
  <define name="PCRE_UTF8_ERR21" value="21"/>
  <define name="PCRE_UTF8_ERR22" value="22"/>
  <define name="PCRE_UTF8_ERR3" value="3"/>
  <define name="PCRE_UTF8_ERR4" value="4"/>
  <define name="PCRE_UTF8_ERR5" value="5"/>
  <define name="PCRE_UTF8_ERR6" value="6"/>
  <define name="PCRE_UTF8_ERR7" value="7"/>
  <define name="PCRE_UTF8_ERR8" value="8"/>
  <define name="PCRE_UTF8_ERR9" value="9"/>
</def>
