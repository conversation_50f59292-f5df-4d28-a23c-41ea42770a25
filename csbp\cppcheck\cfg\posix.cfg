<?xml version="1.0"?>
<!-- Description of the interfaces described by previous and current POSIX standards.
     Current reference: http://pubs.opengroup.org/onlinepubs/9699919799/ -->
<def format="2">
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/wait.html -->
  <!-- Note: These are defined as macros in POSIX system headers -->
  <function name="WIFEXITED,WEXITSTATUS,WIFSIGNALED,WTERMSIG,WIFSTOPPED,WSTOPSIG,WIFCONTINUED">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int semop(int semid, struct sembuf *sops, size_t nsops); -->
  <function name="semop">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/wcsnrtombs.3.html -->
  <!-- size_t wcsnrtombs(char *restrict dest, const wchar_t **restrict src, size_t nwc, size_t len, mbstate_t *restrict ps); -->
  <function name="wcsnrtombs">
    <use-retval/>
    <returnValue type="size_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-bool/>
      <minsize type="argvalue" arg="4"/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="5" direction="inout">
      <not-uninit/>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/aio_read.3.html -->
  <!-- int aio_read(struct aiocb *aiocbp); -->
  <!-- https://man7.org/linux/man-pages/man3/aio_write.3.html -->
  <!-- int aio_write(struct aiocb *aiocbp); -->
  <function name="aio_read,aio_write">
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/aio_return.3.html -->
  <!-- ssize_t aio_return(struct aiocb *aiocbp); -->
  <function name="aio_return">
    <use-retval/>
    <returnValue type="ssize_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/aio_error.3.html -->
  <!-- int aio_error(struct aiocb *aiocbp); -->
  <function name="aio_error">
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/aio_fsync.3.html -->
  <!-- int aio_fsync(int op, struct aiocb *aiocbp); -->
  <function name="aio_fsync">
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="inout">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/aio_cancel.3.html -->
  <!-- int aio_cancel(int fd, struct aiocb *aiocbp); -->
  <function name="aio_cancel">
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
      <not-bool/>
    </arg>
    <arg nr="2" direction="inout">
      <not-bool/>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/aio_suspend.3.html -->
  <!-- int aio_suspend(const struct aiocb *const aiocb_list[], int nitems, const struct timespec *restrict timeout); -->
  <function name="aio_suspend">
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man3/a64l.3.html -->
  <!-- long a64l(const char *str64); -->
  <function name="a64l">
    <use-retval/>
    <returnValue type="long"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man3/a64l.3.html -->
  <!-- char *l64a(long value); -->
  <function name="l64a">
    <use-retval/>
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <!-- The behavior of l64a() is undefined when value is negative. -->
      <!-- If value is zero, it returns an empty string. -->
      <valid>0:</valid>
    </arg>
  </function>
  <!-- int isalnum_l(int c, locale_t locale) -->
  <function name="isalnum_l">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;=0x30 &amp;&amp; arg1&lt;=0x39 || arg1&gt;=0x41 &amp;&amp; arg1 &lt;=0x5A || arg1&gt;=0x61 &amp;&amp; arg1 &lt;=0x7A</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:255</valid>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int isalpha_l(int c, locale_t locale); -->
  <function name="isalpha_l">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;='A' &amp;&amp; arg1&lt;='Z' || arg1&gt;='a' &amp;&amp; arg1 &lt;='z'</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:255</valid>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int isblank_l(int c, locale_t locale); -->
  <function name="isblank_l">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1==' ' || arg1=='\t'</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:255</valid>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int iscntrl_l(int c, locale_t locale); -->
  <function name="iscntrl_l">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1==0x7F || arg1&lt;=0x1F</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:255</valid>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int isdigit_l(int c, locale_t locale); -->
  <function name="isdigit_l">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;='0' &amp;&amp; arg1&lt;='9'</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:255</valid>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int isgraph_l(int c, locale_t locale); -->
  <function name="isgraph_l">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;=0x21 &amp;&amp; arg1&lt;=0x7E</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:255</valid>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int islower_l(int c, locale_t locale); -->
  <function name="islower_l">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;=0x61 &amp;&amp; arg1&lt;=0x7A</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:255</valid>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int isprint_l(int c, locale_t locale); -->
  <function name="isprint_l">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;=0x20 &amp;&amp; arg1&lt;=0x7E</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:255</valid>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int ispunct_l(int c, locale_t locale); -->
  <function name="ispunct_l">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;=0x21 &amp;&amp; arg1&lt;=0x2F || arg1&gt;=0x3A &amp;&amp; arg1&lt;=0x40 || arg1&gt;=0x5B &amp;&amp; arg1&lt;=0x60 || arg1&gt;=0x7B &amp;&amp; arg1&lt;=0x7E</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:255</valid>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int isspace_l(int c, locale_t locale); -->
  <function name="isspace_l">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;=0x09 &amp;&amp; arg1&lt;=0x0D || arg1==0x20</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:255</valid>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int isascii(int c); -->
  <function name="isascii">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;=0 &amp;&amp; arg1&lt;=0x7F</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:255</valid>
    </arg>
  </function>
  <!-- int isupper_l(int c, locale_t locale) -->
  <function name="isupper_l">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;=0x41 &amp;&amp; arg1&lt;=0x5A</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:255</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int isxdigit_l(int c, locale_t locale) -->
  <function name="isxdigit_l">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;=0x30 &amp;&amp; arg1&lt;=0x39 || arg1&gt;=0x41 &amp;&amp; arg1&lt;=0x46 || arg1&gt;=0x61 &amp;&amp; arg1&lt;=0x66</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:255</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man2/accept.2.html -->
  <!-- int accept(int sockfd, struct sockaddr *addr, socklen_t *addrlen); -->
  <function name="accept">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="out">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://pubs.opengroup.org/onlinepubs/9699919799/functions/access.html -->
  <!-- int access(const char *pathname, int amode); -->
  <function name="access">
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man3/adjtime.3.html -->
  <!-- int adjtime(const struct timeval *delta, struct timeval *olddelta); -->
  <function name="adjtime">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-bool/>
    </arg>
  </function>
  <resource>
    <alloc>dlopen</alloc>
    <dealloc>dlclose</dealloc>
  </resource>
  <!-- int bind(int socket, const struct sockaddr *address, socklen_t address_len); -->
  <function name="bind">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- int listen(int sockfd, int backlog); -->
  <function name="listen">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
  </function>
  <!-- int getpeername(int sockfd, struct sockaddr *addr, socklen_t *addrlen); -->
  <!-- int getsockname(int sockfd, struct sockaddr *addr, socklen_t *addrlen); -->
  <function name="getpeername,getsockname">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
    </arg>
    <arg nr="3" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- int connect(int socket, const struct sockaddr *address, socklen_t address_len); -->
  <function name="connect">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- see http://pubs.opengroup.org/onlinepubs/9699919799/functions/dlopen.html -->
  <!-- void *dlopen(const char *file, int mode); -->
  <function name="dlopen">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="void*"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- see http://pubs.opengroup.org/onlinepubs/9699919799/functions/dlsym.html -->
  <!-- void *dlsym(void *restrict handle, const char *restrict name); -->
  <function name="dlsym">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <use-retval/>
    <returnValue type="void*"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- see http://pubs.opengroup.org/onlinepubs/9699919799/functions/dlclose.html -->
  <!-- int dlclose(void *handle); -->
  <function name="dlclose">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- int dup(int fildes);
     see http://pubs.opengroup.org/onlinepubs/9699919799/functions/dup.html -->
  <function name="dup">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- int dup2(int fildes1, int filedes2);
     see http://pubs.opengroup.org/onlinepubs/9699919799/functions/dup.html -->
  <function name="dup2">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- int gettimeofday(struct timeval *tv, struct timezone *tz); -->
  <function name="gettimeofday">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="out"/>
    <arg nr="2" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- int settimeofday(const struct timeval *tv, const struct timezone *tz); -->
  <function name="settimeofday">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void FD_CLR(int fd, fd_set *set); -->
  <function name="FD_CLR">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="inout">
      <not-null/>
    </arg>
  </function>
  <!-- int FD_ISSET(int fd, fd_set *set); -->
  <function name="FD_ISSET">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- void FD_SET(int fd, fd_set *set); -->
  <function name="FD_SET">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="inout">
      <not-null/>
    </arg>
  </function>
  <!-- void FD_ZERO(fd_set *set);-->
  <function name="FD_ZERO">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="out">
      <not-null/>
    </arg>
  </function>
  <!-- http://pubs.opengroup.org/onlinepubs/009695399/functions/fdatasync.html -->
  <!-- int fdatasync(int fildes); -->
  <function name="fdatasync">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- http://pubs.opengroup.org/onlinepubs/9699919799/functions/fnmatch.html -->
  <!-- int fnmatch(const char *pattern, const char *string, int flags); -->
  <function name="fnmatch">
    <pure/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://pubs.opengroup.org/onlinepubs/009695399/functions/fsync.html -->
  <!-- int fsync(int fildes); -->
  <function name="fsync">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- http://pubs.opengroup.org/onlinepubs/9699919799/functions/truncate.html -->
  <!-- int truncate(const char *path, off_t length); -->
  <function name="truncate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int ftruncate(int fd, off_t length); -->
  <function name="ftruncate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int flock(int fd, int operation); -->
  <function name="flock">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int lockf(int fd, int cmd, off_t len); -->
  <function name="lockf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void flockfile(FILE *filehandle); -->
  <!-- void funlockfile(FILE *filehandle); -->
  <function name="flockfile,funlockfile">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="inout">
      <not-uninit/>
    </arg>
  </function>
  <!-- int ftrylockfile(FILE *filehandle); -->
  <function name="ftrylockfile">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="int"/>
    <arg nr="1" direction="inout">
      <not-uninit/>
    </arg>
  </function>
  <!-- int symlinkat(const char *oldpath, int newdirfd, const char *newpath); -->
  <function name="symlinkat">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <valid>0:</valid>
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- int symlink(const char *oldpath, const char *newpath); -->
  <function name="symlink">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- int open(const char *pathname, int flags) -->
  <!-- int open(const char *pathname, int flags, mode_t mode); -->
  <function name="open">
    <!-- TODO: add use-retval when cppcheck suppresses redundant messages
    because of violations to alloc/dealloc and use-retval configuration-->
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int openat(int dirfd, const char *pathname, int flags); -->
  <!-- int openat(int dirfd, const char *pathname, int flags, mode_t mode);  -->
  <function name="openat">
    <!-- TODO: add use-retval when cppcheck suppresses redundant messages
    because of violations to alloc/dealloc and use-retval configuration-->
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int creat(const char *pathname, mode_t mode); -->
  <function name="creat">
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- unsigned int sleep(unsigned int seconds); -->
  <function name="sleep">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- int usleep(useconds_t useconds); -->
  <function name="usleep">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-bool/>
      <valid>0:999999</valid>
    </arg>
    <warn severity="style">Obsolescent function 'usleep' called. It is recommended to use 'nanosleep' or 'setitimer' instead.
The obsolescent function 'usleep' is called. POSIX.1-2001 declares usleep() function obsolescent and POSIX.1-2008 removes it. It is recommended that new applications use the 'nanosleep' or 'setitimer' function.</warn>
  </function>
  <!-- void _exit(int status); -->
  <function name="_exit">
    <noreturn>true</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man3/dirfd.3.html -->
  <!-- int dirfd(DIR *dirp); -->
  <function name="dirfd">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man2/access.2.html -->
  <!-- int faccessat(int dirfd, const char *pathname, int mode, int flags); -->
  <function name="faccessat">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man2/acct.2.html -->
  <!-- int acct(const char *filename); -->
  <function name="acct">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man2/alarm.2.html -->
  <!-- unsigned int alarm(unsigned int seconds); -->
  <function name="alarm">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="unsigned int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- struct rpcent *getrpcent(void); -->
  <function name="getrpcent">
    <use-retval/>
    <returnValue type="struct rpcent *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <warn severity="portability">Non reentrant function 'getrpcent' called. For threadsafe applications it is recommended to use the reentrant replacement function 'getrpcent_r'.</warn>
  </function>
  <!-- struct rpcent *getrpcbyname(char *name); -->
  <function name="getrpcbyname">
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="struct rpcent *"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
    <warn severity="portability">Non reentrant function 'getrpcbyname' called. For threadsafe applications it is recommended to use the reentrant replacement function 'getrpcbyname_r'.</warn>
  </function>
  <!-- struct rpcent *getrpcbynumber(int number); -->
  <function name="getrpcbynumber">
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="struct rpcent *"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <warn severity="portability">Non reentrant function 'getrpcbynumber' called. For threadsafe applications it is recommended to use the reentrant replacement function 'getrpcbynumber_r'.</warn>
  </function>
  <!-- struct protoent *getprotoent(void); -->
  <function name="getprotoent">
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="struct protoent *"/>
    <warn severity="portability">Non reentrant function 'getprotoent' called. For threadsafe applications it is recommended to use the reentrant replacement function 'getprotoent_r'.</warn>
  </function>
  <!-- struct protoent *getprotobyname(const char *name); -->
  <function name="getprotobyname">
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="struct protoent *"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
    <warn severity="portability">Non reentrant function 'getprotobyname' called. For threadsafe applications it is recommended to use the reentrant replacement function 'getprotobyname_r'.</warn>
  </function>
  <!-- struct protoent *getprotobynumber(int proto); -->
  <function name="getprotobynumber">
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="struct protoent *"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <warn severity="portability">Non reentrant function 'getprotobynumber' called. For threadsafe applications it is recommended to use the reentrant replacement function 'getprotobynumber_r'.</warn>
  </function>
  <!-- struct servent *getservent(void); -->
  <function name="getservent">
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="struct servent *"/>
    <warn severity="portability">Non reentrant function 'getservent' called. For threadsafe applications it is recommended to use the reentrant replacement function 'getservent_r'.</warn>
  </function>
  <!-- struct servent *getservbyname(const char *name, const char *proto); -->
  <function name="getservbyname">
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="struct servent *"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
    <warn severity="portability">Non reentrant function 'getservbyname' called. For threadsafe applications it is recommended to use the reentrant replacement function 'getservbyname_r'.</warn>
  </function>
  <!-- struct servent *getservbyport(int port, const char *proto); -->
  <function name="getservbyport">
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="struct servent *"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
    <warn severity="portability">Non reentrant function 'getservbyport' called. For threadsafe applications it is recommended to use the reentrant replacement function 'getservbyport_r'.</warn>
  </function>
  <!-- struct netent  *getnetent(void); -->
  <function name="getnetent">
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="struct netent *"/>
    <warn severity="portability">Non reentrant function 'getnetent' called. For threadsafe applications it is recommended to use the reentrant replacement function 'getnetent_r'.</warn>
  </function>
  <!-- struct netent *getnetbyname(const char *name); -->
  <function name="getnetbyname">
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="struct netent *"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
    <warn severity="portability">Non reentrant function 'getnetbyname' called. For threadsafe applications it is recommended to use the reentrant replacement function 'getnetbyname_r'.</warn>
  </function>
  <!-- struct netent *getnetbyaddr(uint32_t net, int type); -->
  <function name="getnetbyaddr">
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="struct netent *"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <warn severity="portability">Non reentrant function 'getnetbyaddr' called. For threadsafe applications it is recommended to use the reentrant replacement function 'getnetbyaddr_r'.</warn>
  </function>
  <!-- struct hostent *gethostent(void); -->
  <function name="gethostent">
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="struct hostent *"/>
    <warn severity="portability">Non reentrant function 'gethostent' called. For threadsafe applications it is recommended to use the reentrant replacement function 'gethostent_r'.</warn>
  </function>
  <!-- struct hostent *gethostbyname(const char *name); -->
  <function name="gethostbyname">
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="struct hostent *"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
    <warn severity="style" reason="Obsolescent" alternatives="getaddrinfo"/>
  </function>
  <!-- struct hostent *gethostbyname2(const char *name, int af); -->
  <function name="gethostbyname2">
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="struct hostent *"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <warn severity="portability">Non reentrant function 'gethostbyname2' called. For threadsafe applications it is recommended to use the reentrant replacement function 'gethostbyname2_r'.</warn>
  </function>
  <!-- struct hostent *gethostbyaddr(const void *addr, socklen_t len, int type); -->
  <function name="gethostbyaddr">
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="struct hostent *"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <warn severity="style" reason="Obsolescent" alternatives="getnameinfo"/>
  </function>
  <!-- int brk(void *addr); -->
  <function name="brk">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- void *sbrk(intptr_t incr);-->
  <function name="sbrk">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void *"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- int closedir(DIR *dir); -->
  <function name="closedir">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- char *strfry(char *string); -->
  <function name="strfry">
    <noreturn>false</noreturn>
    <returnValue type="char *"/>
    <arg nr="1">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- char *strsep(char **stringp, const char *delim); -->
  <function name="strsep">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="char *"/>
    <arg nr="1">
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="2">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- char *strndup(const char *s, size_t n); -->
  <function name="strndup">
    <noreturn>false</noreturn>
    <returnValue type="char *"/>
    <use-retval/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- wchar_t *wcsdup(const wchar_t *s); -->
  <function name="wcsdup">
    <noreturn>false</noreturn>
    <returnValue type="wchar_t *"/>
    <use-retval/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- int mkstemp(char *template); -->
  <function name="mkstemp">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- char *mkdtemp(char *template); -->
  <function name="mkdtemp">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="char *"/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- char *mktemp(char *template); -->
  <function name="mktemp">
    <noreturn>false</noreturn>
    <returnValue type="char *"/>
    <leak-ignore/>
    <use-retval/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
    <warn severity="warning">Obsolete function 'mktemp' called. It is recommended to use 'mkstemp' or 'mkdtemp' instead.
The function 'mktemp' is considered to be dangerous due to race conditions and some implementations generating only up to 26 different filenames out of each template. This function has been removed in POSIX.1-2008. Use 'mkstemp' or 'mkdtemp' instead.</warn>
  </function>
  <!-- char *getcwd(char *buf, size_t size); -->
  <function name="getcwd">
    <noreturn>false</noreturn>
    <returnValue type="char *"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- int mkdir(const char *pathname, mode_t mode); -->
  <function name="mkdir">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int mknod(const char *pathname, mode_t mode, dev_t dev); -->
  <function name="mknod">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int mknodat(int dirfd, const char *pathname, mode_t mode, dev_t dev); -->
  <function name="mknodat">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int mkdirat(int dirfd, const char *pathname, mode_t mode); -->
  <function name="mkdirat">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int fchmodat(int dirfd, const char *pathname, mode_t mode, int flags); -->
  <function name="fchmodat">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int fchownat(int dirfd, const char *pathname, uid_t owner, gid_t group, int flags); -->
  <function name="fchownat">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int rmdir(const char *pathname); -->
  <function name="rmdir">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- int chdir(const char *path); -->
  <function name="chdir">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- int chroot(const char *path); -->
  <function name="chroot">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- int link(const char *oldpath, const char *newpath); -->
  <function name="link">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- int linkat(int fd1, const char *path1, int fd2, const char *path2, int flag); -->
  <function name="linkat">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- int unlinkat(int fd, const char *path, int flag); -->
  <function name="unlinkat">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int unlink(const char *pathname); -->
  <function name="unlink">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- int stat(const char *file_name, struct stat *buf); -->
  <function name="stat">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- int lstat(const char *file_name, struct stat *buf); -->
  <function name="lstat">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- int fstat(int fd, struct stat *statbuf); -->
  <function name="fstat">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <valid>0:</valid>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- int fstatat(int dirfd, const char *pathname, struct stat *statbuf, int flags); -->
  <function name="fstatat">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
  </function>
  <!-- int chmod(const char *path, mode_t mode); -->
  <function name="chmod">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- mode_t umask(mode_t mask); -->
  <function name="umask">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="mode_t"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int fchmod(int fildes, mode_t mode); -->
  <function name="fchmod">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int chown(const char *path, uid_t owner, gid_t group); -->
  <function name="chown">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int lchown(const char *path, uid_t owner, gid_t group); -->
  <function name="lchown">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int fchown(int fildes, uid_t owner, gid_t group); -->
  <function name="fchown">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- clock_t times(struct tms *buffer); -->
  <function name="times">
    <noreturn>false</noreturn>
    <returnValue type="clock_t"/>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int utime(const char *filename, struct utimbuf *buf); -->
  <function name="utime">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <warn severity="style" reason="Obsolescent" alternatives="utimensat"/>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/encrypt.3.html -->
  <!-- void encrypt(char block[64], int edflag);-->
  <function name="encrypt">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-null/>
      <minsize type="value" value="64" baseType="char"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int futimens(int fd, const struct timespec times[2]);-->
  <function name="futimens">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <minsize type="value" value="2" baseType="timespec"/>
    </arg>
  </function>
  <!-- int utimensat(int dirfd, const char *pathname, const struct timespec times[2], int flags);-->
  <function name="utimensat">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <minsize type="value" value="2" baseType="timespec"/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int utimes(const char *filename, const struct timeval times[2]); -->
  <function name="utimes">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <minsize type="value" value="2" baseType="timeval"/>
    </arg>
    <warn severity="style" reason="Obsolescent" alternatives="utimensat"/>
  </function>
  <!-- DIR *opendir(const char *name); -->
  <function name="opendir">
    <noreturn>false</noreturn>
    <returnValue type="DIR *"/>
    <use-retval/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- DIR *fdopendir(int fd); -->
  <function name="fdopendir">
    <use-retval/>
    <returnValue type="DIR *"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
      <valid>0:</valid>
      <not-bool/>
    </arg>
  </function>
  <!-- int isatty(int fildes); -->
  <function name="isatty">
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <valid>0:</valid>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- FILE *popen(const char *command, const char *type); -->
  <function name="popen">
    <use-retval/>
    <returnValue type="FILE *"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- int pclose(FILE *stream); -->
  <function name="pclose">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int socket(int domain, int type, int protocol); -->
  <function name="socket">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/setsockopt.html -->
  <!-- int setsockopt(int socket, int level, int option_name,
       const void *option_value, socklen_t option_len); -->
  <function name="setsockopt">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-null/>
      <minsize type="argvalue" arg="5"/>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/getsockopt.html -->
  <!-- int getsockopt(int socket, int level, int option_name,
       void *restrict option_value, socklen_t *restrict option_len); -->
  <function name="getsockopt">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="out">
      <not-null/>
    </arg>
    <arg nr="5" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- int nice(int incr); -->
  <function name="nice">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int pause(void); -->
  <function name="pause">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
  </function>
  <!-- int close(int fildes); -->
  <function name="close">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- size_t confstr(int, char *, size_t); -->
  <function name="confstr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="size_t"/>
    <arg nr="1">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
    <arg nr="3">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- long int fpathconf(int fildes, int name); -->
  <function name="fpathconf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long int pathconf(const char *path, int name); -->
  <function name="pathconf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long int"/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- long int sysconf(int name); -->
  <function name="sysconf">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="long int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- FILE *fdopen(int fd, const char *mode); -->
  <function name="fdopen">
    <use-retval/>
    <returnValue type="FILE *"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- long int random(void); -->
  <function name="random">
    <use-retval/>
    <returnValue type="long int"/>
    <noreturn>false</noreturn>
  </function>
  <!-- void srandom(unsigned int seed); -->
  <function name="srandom">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <!-- It is common practice to call srandom with an uninitialized
         variable. Therefore, no warning shall be generated in this
         very special case. -->
    <arg nr="1"/>
  </function>
  <!-- void rewinddir(DIR *dir); -->
  <function name="rewinddir">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- void seekdir(DIR *dirp, long loc); -->
  <function name="seekdir">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int rand_r(unsigned int *seedp); -->
  <function name="rand_r">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1">
      <not-null/>
      <not-bool/>
    </arg>
    <warn severity="style" reason="Obsolescent" alternatives="rand"/>
  </function>
  <!-- int strcasecmp(const char *s1, const char *s2); -->
  <function name="strcasecmp">
    <pure/>
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/strncasecmp.3.html -->
  <!-- int strncasecmp(const char *s1, const char *s2, size_t n); -->
  <function name="strncasecmp">
    <pure/>
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/wcsncasecmp.3.html -->
  <!-- int wcsncasecmp(const wchar_t *s1, const wchar_t *s2, size_t n) -->
  <function name="wcsncasecmp">
    <pure/>
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- ssize_t sendfile(int out_fd, int in_fd, off_t *offset, size_t count); -->
  <function name="sendfile">
    <leak-ignore/>
    <returnValue type="ssize_t"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-bool/>
      <valid>1:</valid>
    </arg>
  </function>
  <!-- void srand48(long int seedval); -->
  <function name="srand48">
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- unsigned short *seed48(unsigned short seed16v[3]); -->
  <function name="seed48">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="unsigned short *"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <minsize type="value" value="3" baseType="unsigned short"/>
    </arg>
  </function>
  <!-- long int jrand48(unsigned short xsubi[3]); -->
  <function name="jrand48">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="long int"/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <minsize type="value" value="3" baseType="unsigned short"/>
    </arg>
  </function>
  <!-- long int nrand48(unsigned short xsubi[3]); -->
  <function name="nrand48">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="long int"/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <minsize type="value" value="3" baseType="unsigned short"/>
    </arg>
  </function>
  <!-- double erand48(unsigned short xsubi[3]); -->
  <function name="erand48">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="double"/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <minsize type="value" value="3" baseType="unsigned short"/>
    </arg>
  </function>
  <!-- void lcong48(unsigned short param[7]); -->
  <function name="lcong48">
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <minsize type="value" value="7" baseType="unsigned short"/>
    </arg>
  </function>
  <!-- ssize_t read(int fd, void *buf, size_t count); -->
  <function name="read">
    <leak-ignore/>
    <returnValue type="ssize_t"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="out">
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/read.3p.html -->
  <!-- ssize_t pread(int fildes, void *buf, size_t nbyte, off_t offset); -->
  <function name="pread">
    <leak-ignore/>
    <returnValue type="ssize_t"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="out">
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ssize_t write(int fildes, const void *buf, size_t nbyte); -->
  <function name="write">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="ssize_t"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/write.3p.html -->
  <!-- ssize_t pwrite(int fd, const void *buf, size_t count, off_t offset); -->
  <function name="pwrite">
    <returnValue type="ssize_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <valid>0:</valid>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ssize_t recv(int sockfd, void *buf, size_t len, int flags); -->
  <function name="recv">
    <returnValue type="ssize_t"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ssize_t recvfrom(int sockfd, void *buf, size_t len, int flags,
                        struct sockaddr *src_addr, socklen_t *addrlen); -->
  <function name="recvfrom">
    <returnValue type="ssize_t"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="5" direction="out"/>
    <arg nr="6" direction="inout">
      <not-bool/>
    </arg>
  </function>
  <!-- ssize_t recvmsg(int sockfd, struct msghdr *msg, int flags); -->
  <function name="recvmsg">
    <returnValue type="ssize_t"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ssize_t send(int sockfd, const void *buf, size_t len, int flags); -->
  <function name="send">
    <returnValue type="ssize_t"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-null/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/sendto.3p.html -->
  <!-- ssize_t sendto(int sockfd, const void *buf, size_t len, int flags,
                      const struct sockaddr *dest_addr, socklen_t addrlen); -->
  <function name="sendto">
    <returnValue type="ssize_t"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="6" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- ssize_t sendmsg(int sockfd, const struct msghdr *msg, int flags); -->
  <function name="sendmsg">
    <returnValue type="ssize_t"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/mmap.html -->
  <!-- void *mmap(void *addr, size_t length, int prot, int flags, int fd, off_t offset); -->
  <function name="mmap">
    <use-retval/>
    <returnValue type="void *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>1:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="6" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void *mmap64(void *addr, size_t length, int prot, int flags, int fd, off64_t offset); -->
  <function name="mmap64">
    <use-retval/>
    <returnValue type="void *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>1:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="6" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/munmap.html -->
  <!-- int munmap(void *addr, size_t length); -->
  <function name="munmap">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>1:</valid>
    </arg>
  </function>
  <!-- void openlog(const char *ident, int option, int facility); -->
  <function name="openlog">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int fcntl(int fd, int cmd, ... /* arg */ ); -->
  <function name="fcntl">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man2/ioctl.2.html -->
  <!-- int ioctl(int fd, unsigned long request, ...); -->
  <function name="ioctl">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <!-- void syslog(int priority, const char *format, ...); -->
  <function name="syslog">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <formatstr/>
    <arg nr="2" direction="in">
      <formatstr/>
      <strz/>
    </arg>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <!-- void vsyslog(int priority, const char *format, va_list ap); -->
  <function name="vsyslog">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <formatstr/>
      <strz/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- gid_t getgid(void) -->
  <function name="getgid">
    <use-retval/>
    <returnValue type="gid_t"/>
    <noreturn>false</noreturn>
  </function>
  <!-- gid_t getegid(void) -->
  <function name="getegid">
    <use-retval/>
    <returnValue type="gid_t"/>
    <noreturn>false</noreturn>
  </function>
  <!-- uid_t getuid (void); -->
  <function name="getuid">
    <use-retval/>
    <returnValue type="uid_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
  </function>
  <!-- pid_t getsid(pid_t pid); -->
  <function name="getsid">
    <use-retval/>
    <returnValue type="pid_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- uid_t geteuid(void); -->
  <function name="geteuid">
    <use-retval/>
    <returnValue type="uid_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
  </function>
  <!-- pid_t getppid(void); -->
  <function name="getppid">
    <use-retval/>
    <returnValue type="pid_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
  </function>
  <!-- pid_t getpid(void); -->
  <function name="getpid">
    <use-retval/>
    <returnValue type="pid_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
  </function>
  <!-- pid_t getpgrp(void); -->
  <function name="getpgrp">
    <use-retval/>
    <returnValue type="pid_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
  </function>
  <!-- ppid_t getpgid(pid_t pid); -->
  <function name="getpgid">
    <use-retval/>
    <returnValue type="ppid_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int setuid(uid_t uid); -->
  <function name="setuid">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int seteuid(uid_t euid); -->
  <function name="seteuid">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int setgid(gid_t gid); -->
  <function name="setgid">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int setegid(gid_t egid); -->
  <function name="setegid">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int setpgid(pid_t pid, pid_t pgid); -->
  <function name="setpgid">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int socketpair(int domain, int type, int protocol, int sv[2]); -->
  <function name="socketpair">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="out">
      <not-null/>
      <not-bool/>
      <minsize type="value" value="2" baseType="int"/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man2/socketpair.2.html -->
  <!-- int pipe(int fildes[2]); -->
  <function name="pipe">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1" direction="out">
      <not-null/>
      <not-bool/>
      <minsize type="value" value="2" baseType="int"/>
    </arg>
  </function>
  <!-- int pselect(int nfds, fd_set *restrict readfds,
       fd_set *restrict writefds, fd_set *restrict errorfds,
       const struct timespec *restrict timeout,
       const sigset_t *restrict sigmask);
       see http://pubs.opengroup.org/onlinepubs/9699919799/functions/pselect.html -->
  <function name="pselect">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="inout">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="inout">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="inout">
      <not-uninit/>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="6" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int select(int nfds, fd_set *restrict readfds, fd_set * writefds, fd_set * errorfds, struct timeval *timeout);
       see http://pubs.opengroup.org/onlinepubs/9699919799/functions/pselect.html -->
  <function name="select">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="inout">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="inout">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="inout">
      <not-uninit/>
    </arg>
    <arg nr="5" direction="inout">
      <not-uninit/>
    </arg>
  </function>
  <!-- pid_t setpgrp(void); -->
  <function name="setpgrp">
    <returnValue type="pid_t"/>
    <noreturn>false</noreturn>
  </function>
  <!-- int setregid(gid_t rgid, gid_t egid); -->
  <function name="setregid">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int setreuid(uid_t ruid, uid_t euid); -->
  <function name="setreuid">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int setfsuid(uid_t fsuid); -->
  <function name="setfsuid">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int setfsgid(uid_t fsgid); -->
  <function name="setfsgid">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- pid_t setsid(void); -->
  <function name="setsid">
    <returnValue type="pid_t"/>
    <noreturn>false</noreturn>
    <!-- it is a good idea to do: <use-retval/> -->
  </function>
  <!-- char *getwd(char *path_name);-->
  <function name="getwd">
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
    <leak-ignore/>
    <warn severity="style" reason="Obsolescent" alternatives="getcwd"/>
  </function>
  <!-- http://pubs.opengroup.org/onlinepubs/009695399/basedefs/arpa/inet.h.html -->
  <!-- uint32_t htonl(uint32_t); -->
  <function name="htonl">
    <returnValue type="uint32_t"/>
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- uint16_t htons(uint16_t); -->
  <function name="htons">
    <returnValue type="uint16_t"/>
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- uint32_t ntohl(uint32_t); -->
  <function name="ntohl">
    <returnValue type="uint32_t"/>
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- uint16_t ntohs(uint16_t); -->
  <function name="ntohs">
    <returnValue type="uint16_t"/>
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- in_addr_t inet_addr(const char *cp);
       http://pubs.opengroup.org/onlinepubs/009695399/functions/inet_addr.html -->
  <function name="inet_addr">
    <leak-ignore/>
    <pure/>
    <returnValue type="in_addr_t"/>
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- char *inet_ntoa(struct in_addr in);
       http://pubs.opengroup.org/onlinepubs/009695399/functions/inet_addr.html -->
  <function name="inet_ntoa">
    <leak-ignore/>
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int mq_close(mqd_t); -->
  <function name="mq_close">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int mq_getattr(mqd_t, struct mq_attr *); -->
  <function name="mq_getattr">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- int mq_notify(mqd_t, const struct sigevent *); -->
  <function name="mq_notify">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/mq_open.html -->
  <!-- mqd_t mq_open(const char *name, int oflag, ...); -->
  <function name="mq_open">
    <returnValue type="mqd_t"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="any"/>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/mq_receive.html -->
  <!-- ssize_t mq_receive(mqd_t mqdes, char *msg_ptr, size_t msg_len, unsigned *msg_prio); -->
  <function name="mq_receive">
    <returnValue type="ssize_t"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/mq_send.html -->
  <!-- int mq_send(mqd_t mqdes, const char *msg_ptr, size_t msg_len, unsigned msg_prio); -->
  <function name="mq_send">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="in">
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/mq_setattr.html -->
  <!-- int mq_setattr(mqd_t mqdes, const struct mq_attr *restrict mqstat, struct mq_attr *restrict omqstat); -->
  <function name="mq_setattr">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/mq_receive.html -->
  <!-- ssize_t mq_timedreceive(mqd_t mqdes, char *restrict msg_ptr, size_t msg_len, unsigned *restrict msg_prio, const struct timespec *restrict abstime); -->
  <function name="mq_timedreceive">
    <returnValue type="ssize_t"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="out"/>
    <arg nr="5" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/mq_send.html -->
  <!-- int mq_timedsend(mqd_t mqdes, const char *msg_ptr, size_t msg_len, unsigned msg_prio, const struct timespec *abstime); -->
  <function name="mq_timedsend">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <not-null/>
      <minsize type="argvalue" arg="3"/>
      <strz/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="5" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int mq_unlink(const char *name); -->
  <function name="mq_unlink">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/dbm_clearerr.html -->
  <!-- int dbm_clearerr(DBM *db); -->
  <function name="dbm_clearerr">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void dbm_close(DBM *db); -->
  <function name="dbm_close">
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- int dbm_delete(DBM *db, datum key); -->
  <function name="dbm_delete">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int dbm_error(DBM *db); -->
  <function name="dbm_error">
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- datum dbm_fetch(DBM *db, datum key); -->
  <function name="dbm_fetch">
    <use-retval/>
    <returnValue type="datum"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- datum dbm_firstkey(DBM *db); -->
  <function name="dbm_firstkey">
    <use-retval/>
    <returnValue type="datum"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- datum dbm_nextkey(DBM *db); -->
  <function name="dbm_nextkey">
    <use-retval/>
    <returnValue type="datum"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- DBM *dbm_open(const char *file, int open_flags, mode_t file_mode); -->
  <function name="dbm_open">
    <returnValue type="DBM *"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int dbm_store(DBM *db, datum key, datum content, int store_mode); -->
  <function name="dbm_store">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://pubs.opengroup.org/onlinepubs/009695399/functions/freeaddrinfo.html -->
  <!-- void freeaddrinfo(struct addrinfo *ai); -->
  <function name="freeaddrinfo">
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
      <not-null/>
      <!-- not-null is not required by the resource above, but some systems will segfault -->
    </arg>
  </function>
  <!-- http://pubs.opengroup.org/onlinepubs/009695399/functions/freeaddrinfo.html -->
  <!-- int getaddrinfo(const char * nodename, const char * servname, const struct addrinfo * hints, struct addrinfo ** res); -->
  <function name="getaddrinfo">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- int getnameinfo(const struct sockaddr *restrict sa,
                       socklen_t salen,
                       char *restrict node,
                       socklen_t nodelen,
                       char *restrict service,
                       socklen_t servicelen,
                       int flags); -->
  <function name="getnameinfo">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="out">
      <minsize type="argvalue" arg="4"/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="5" direction="out">
      <minsize type="argvalue" arg="6"/>
    </arg>
    <arg nr="6" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="7" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- const char *gai_strerror(int ecode); -->
  <function name="gai_strerror">
    <noreturn>false</noreturn>
    <returnValue type="const char *"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void endhostent(void); -->
  <function name="endhostent">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
  </function>
  <!-- void sethostent(int stayopen); -->
  <function name="sethostent">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int uname(struct utsname *buf); -->
  <function name="uname">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!--void endpwent(void); -->
  <function name="endpwent">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
  </function>
  <!--struct passwd *getpwent(void); -->
  <function name="getpwent">
    <use-retval/>
    <returnValue type="struct passwd *"/>
    <noreturn>false</noreturn>
    <warn severity="portability">Non reentrant function 'getpwent' called. For threadsafe applications it is recommended to use the reentrant replacement function 'getpwent_r'.</warn>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/getpwent_r.3.html-->
  <!-- int fgetpwent_r(FILE *restrict stream, struct passwd *restrict pwbuf, char *restrict buf, size_t buflen, struct passwd **restrict pwbufp); -->
  <function name="fgetpwent_r">
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="5" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/getpwent_r.3.html-->
  <!-- int getpwent_r(struct passwd *restrict pwbuf, char *restrict buf, size_t buflen, struct passwd **restrict pwbufp); -->
  <function name="getpwent_r">
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!--struct passwd *getpwnam(const char *); -->
  <function name="getpwnam">
    <use-retval/>
    <returnValue type="struct passwd *"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
    <warn severity="portability">Non reentrant function 'getpwnam' called. For threadsafe applications it is recommended to use the reentrant replacement function 'getpwnam_r'.</warn>
  </function>
  <!-- char *strtok(char *s, const char *ct); -->
  <function name="strtok,std::strtok">
    <!-- Already configured in std.cfg. Add only a warning for POSIX that a
    threadsafe function exists that should be used. -->
    <warn severity="portability">Non reentrant function 'strtok' called. For threadsafe applications it is recommended to use the reentrant replacement function 'strtok_r'.</warn>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/strtok.html -->
  <!-- char *strtok_r(char *str, const char *delim, char **saveptr); -->
  <function name="strtok_r">
    <!-- strtok may modify the first argument, so using the return value is not mandatory -->
    <noreturn>false</noreturn>
    <returnValue type="char *"/>
    <pure/>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="inout">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/getpwnam.html -->
  <!-- int getpwnam_r(const char *name, struct passwd *pwd, char *buffer, size_t bufsize, struct passwd **result); -->
  <function name="getpwnam_r">
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
      <strz/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="4"/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="5" direction="out">
      <not-bool/>
      <not-null/>
    </arg>
  </function>
  <!-- struct passwd *getpwuid(uid_t uid); -->
  <function name="getpwuid">
    <use-retval/>
    <returnValue type="struct passwd *"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <warn severity="portability">Non reentrant function 'getpwuid' called. For threadsafe applications it is recommended to use the reentrant replacement function 'getpwuid_r'.</warn>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/getpwnam.3.html -->
  <!-- int getpwuid_r(uid_t uid, struct passwd *restrict pwd, char *restrict buf, size_t buflen, struct passwd **restrict result) -->
  <function name="getpwuid_r">
    <returnValue type="int"/>
    <use-retval/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="4"/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="5" direction="out">
      <not-bool/>
      <not-null/>
    </arg>
  </function>
  <!-- void setpwent(void); -->
  <function name="setpwent">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/catclose.html -->
  <!-- int catclose(nl_catd catd); -->
  <function name="catclose">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/catgets.html -->
  <!-- char *catgets(nl_catd catd, int set_id, int msg_id, const char *s); -->
  <function name="catgets">
    <returnValue type="char *"/>
    <use-retval/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/catopen.html -->
  <!-- nl_catd catopen(const char *name, int oflag); -->
  <function name="catopen">
    <returnValue type="nl_catd"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://pubs.opengroup.org/onlinepubs/9699919799/basedefs/poll.h.html -->
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/poll.html -->
  <!-- int poll(struct pollfd fds[], nfds_t nfds, int timeout); -->
  <function name="poll">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/lsearch.3.html -->
  <!-- void *lfind(const void *key, const void *base, size_t *nmemb, size_t size, int(*compar)(const void *, const void *)); -->
  <function name="lfind">
    <returnValue type="void *"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-null/>
      <minsize type="argvalue" arg="4"/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>1:</valid>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
      <not-null/>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/lsearch.3.html -->
  <!-- void *lsearch(const void *key, void *base, size_t *nmemb, size_t size, int(*compar)(const void *, const void *)); -->
  <function name="lsearch">
    <returnValue type="void *"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-null/>
      <minsize type="argvalue" arg="4"/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>1:</valid>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
      <not-null/>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/mbsnrtowcs.3.html -->
  <!-- size_t mbsnrtowcs(wchar_t *restrict dest, const char **restrict src, size_t nms, size_t len, mbstate_t *restrict ps) -->
  <function name="mbsnrtowcs">
    <returnValue type="size_t"/>
    <use-retval/>
    <noreturn>false</noreturn>
    <!-- If dest is NULL, len is ignored, and the conversion proceeds as
       above, except that the converted wide characters are not written
       out to memory, and that no length limit exists. -->
    <arg nr="1" direction="out"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-null/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-null/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <!-- In both of the above cases, if ps is NULL, a static anonymous
       state known only to the mbsrtowcs() function is used instead. -->
    <arg nr="5" direction="in"/>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/regcomp.html -->
  <!-- int regcomp(regex_t *restrict preg, const char *restrict pattern, int cflags); -->
  <function name="regcomp">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="out">
      <not-null/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- size_t regerror(int errcode, const regex_t *restrict preg, char *restrict errbuf, size_t errbuf_size); -->
  <function name="regerror">
    <returnValue type="size_t"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="out">
      <minsize type="argvalue" arg="4"/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <valid>0:</valid>
      <not-bool/>
    </arg>
  </function>
  <!-- int regexec(const regex_t *restrict preg, const char *restrict string, size_t nmatch, regmatch_t pmatch[restrict], int eflags); -->
  <function name="regexec">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="out"/>
    <arg nr="5" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void regfree(regex_t *preg); -->
  <function name="regfree">
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/sched_get_priority_max.html -->
  <!-- int sched_get_priority_max(int policy); -->
  <function name="sched_get_priority_max">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int sched_get_priority_min(int policy); -->
  <function name="sched_get_priority_min">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/sched_getparam.html -->
  <!-- int sched_getparam(pid_t pid, struct sched_param *param); -->
  <function name="sched_getparam">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/sched_getscheduler.html -->
  <!-- int sched_getscheduler(pid_t pid); -->
  <function name="sched_getscheduler">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/sched_rr_get_interval.html -->
  <!-- int sched_rr_get_interval(pid_t pid, struct timespec *interval); -->
  <function name="sched_rr_get_interval">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/sched_setparam.html# -->
  <!-- int sched_setparam(pid_t pid, const struct sched_param *param); -->
  <function name="sched_setparam">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/sched_setscheduler.html -->
  <!-- int sched_setscheduler(pid_t pid, int policy, const struct sched_param *param); -->
  <function name="sched_setscheduler">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/sched_yield.html -->
  <!-- int sched_yield(void); -->
  <function name="sched_yield">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/009696799/functions/ecvt.html -->
  <!-- char *ecvt(double value, int ndigit, int *restrict decpt, int *restrict sign);-->
  <!--      LEGACY in POSIX.1-2001, removed in POSIX.1-2008-->
  <function name="ecvt">
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
    </arg>
    <arg nr="4" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
    <warn severity="style" reason="Obsolescent" alternatives="sprintf"/>
  </function>
  <!-- char *fcvt(double value, int ndigit, int *restrict decpt, int *restrict sign);-->
  <!--    LEGACY in POSIX.1-2001, removed in POSIX.1-2008-->
  <function name="fcvt">
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
    </arg>
    <arg nr="4" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
    <warn severity="style" reason="Obsolescent" alternatives="sprintf"/>
  </function>
  <!-- char *gcvt(double value, int ndigit, char *buf);-->
  <!--    LEGACY in POSIX.1-2001, removed in POSIX.1-2008-->
  <function name="gcvt">
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
    <warn severity="style" reason="Obsolescent" alternatives="sprintf"/>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/lseek.html -->
  <!-- off_t lseek(int fildes, off_t offset, int whence); -->
  <function name="lseek">
    <returnValue type="off_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:2</valid>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/lseek64.3.html -->
  <!-- off64_t lseek64(int fildes, off64_t offset, int whence); defined in POSIX.1 (1996). -->
  <function name="lseek64">
    <returnValue type="off64_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:2</valid>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/lseek64.3.html -->
  <!-- loff_t llseek(int fd, loff_t offset, int whence); -->
  <function name="llseek">
    <returnValue type="loff_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:2</valid>
    </arg>
    <!-- Note: Since glibc 2.28, this function symbol is no longer available to newly linked applications. -->
    <warn severity="style" reason="Obsolescent" alternatives="lseek64"/>
  </function>
  <!-- int nanosleep(const struct timespec *rqtp, struct timespec *rmtp); -->
  <function name="nanosleep">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
    </arg>
    <arg nr="2" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- void setkey(const char *key); -->
  <function name="setkey">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- char *getpass(const char *prompt); -->
  <function name="getpass">
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- http://pubs.opengroup.org/onlinepubs/009695399/basedefs/stdlib.h.html -->
  <!-- double drand48(void); -->
  <function name="drand48">
    <use-retval/>
    <returnValue type="double"/>
    <noreturn>false</noreturn>
  </function>
  <!-- long int lrand48(void); -->
  <!-- long int mrand48(void); -->
  <function name="lrand48,mrand48">
    <use-retval/>
    <returnValue type="long int"/>
    <noreturn>false</noreturn>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/putenv.html -->
  <!-- int putenv(char *string); -->
  <function name="putenv">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/setenv.html -->
  <!-- int setenv(const char *envname, const char *envval, int overwrite); -->
  <function name="setenv">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int unsetenv(const char *name); -->
  <function name="unsetenv">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- struct tm * localtime(const time_t *tp); -->
  <function name="localtime,std::localtime">
    <noreturn>false</noreturn>
    <returnValue type="struct tm *"/>
    <leak-ignore/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
    <warn severity="portability">Non reentrant function 'localtime' called. For threadsafe applications it is recommended to use the reentrant replacement function 'localtime_r'.</warn>
  </function>
  <!-- struct tm *localtime_r(const time_t *timep, struct tm *result); -->
  <function name="localtime_r">
    <noreturn>false</noreturn>
    <returnValue type="struct tm *"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/readdir.html -->
  <!-- struct dirent *readdir(DIR *dirp); -->
  <function name="readdir">
    <returnValue type="struct dirent *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
    <warn severity="portability">Non reentrant function 'readdir' called. For threadsafe applications it is recommended to use the reentrant replacement function 'readdir_r'.</warn>
  </function>
  <!-- int readdir_r(DIR *dirp, struct dirent *entry, struct dirent **result); -->
  <function name="readdir_r">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/readlink.html -->
  <!-- ssize_t readlink(const char *path, char *buf, size_t bufsiz); -->
  <function name="readlink">
    <returnValue type="ssize_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- int renameat(int olddirfd, const char *oldpath, int newdirfd, const char *newpath); -->
  <function name="renameat">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man2/readlink.2.html -->
  <!-- int readlinkat(int dirfd, const char *pathname, char *buf, size_t bufsiz); -->
  <function name="readlinkat">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="4"/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/strxfrm_l.html -->
  <!-- size_t strxfrm_l(char *restrict s1, const char *restrict s2, size_t n, locale_t locale) -->
  <function name="strxfrm_l">
    <returnValue type="size_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <!-- In case the 3rd argument is 0, the 1st argument is permitted to be a null pointer. (#6306) -->
    <arg nr="1" direction="out">
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
  </function>  
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/asctime.html -->
  <!-- char *asctime_r(const struct tm *tm, char *buf); -->
  <function name="asctime_r">
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
      <not-bool/>
      <minsize type="value" value="26"/>
    </arg>
    <warn severity="style" reason="Obsolescent" alternatives="strftime"/>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/ctime.html -->
  <!-- char *ctime_r(const time_t *timep, char *buf); -->
  <function name="ctime_r">
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
      <not-bool/>
      <minsize type="value" value="26"/>
    </arg>
    <warn severity="style" reason="Obsolescent" alternatives="strftime"/>
  </function>
  <!-- struct tm *gmtime_r(const time_t *timep, struct tm *result); -->
  <function name="gmtime_r">
    <returnValue type="struct tm *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/gmtime.html -->
  <!-- struct tm * gmtime(const time_t *tp); -->
  <function name="gmtime">
    <returnValue type="struct tm *"/>
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
    <warn severity="portability">Non reentrant function 'gmtime' called. For threadsafe applications it is recommended to use the reentrant replacement function 'gmtime_r'.</warn>
  </function>
  <!-- http://pubs.opengroup.org/onlinepubs/007908799/xsh/time.h.html -->
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/clock_settime.html -->
  <!-- int clock_settime(clockid_t clock_id, const struct timespec *tp); -->
  <function name="clock_settime">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int killpg(int pgrp, int sig); -->
  <function name="killpg">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int kill(pid_t pid, int sig); -->
  <function name="kill">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int clock_gettime(clockid_t clock_id, struct timespec *tp); -->
  <function name="clock_gettime">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- int clock_getres(clockid_t clock_id, struct timespec *res); -->
  <function name="clock_getres">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/tmpnam.html -->
  <!-- char* tmpnam(char *s); -->
  <function name="tmpnam,tmpnam_r">
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-bool/>
    </arg>
    <warn severity="style" reason="Obsolescent" alternatives="tmpfile,mkstemp,mkdtemp"/>
  </function>
  <!-- void makecontext(ucontext_t *ucp, void (*func)(), int argc, ...); -->
  <function name="makecontext">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
    </arg>
    <arg nr="2">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="3">
      <not-bool/>
      <not-uninit/>
    </arg>
    <arg nr="any"/>
    <warn severity="portability">Obsolescent function 'makecontext' called. Applications are recommended to be rewritten to use POSIX threads.</warn>
  </function>
  <!-- void swapcontext(ucontext_t *restrict oucp, const ucontext_t *restrict ucp); -->
  <function name="swapcontext">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
    </arg>
    <arg nr="2">
      <not-null/>
    </arg>
    <warn severity="portability">Obsolescent function 'swapcontext' called. Applications are recommended to be rewritten to use POSIX threads.</warn>
  </function>
  <!-- void getcontext(ucontext_t *ucp); -->
  <function name="getcontext">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
    </arg>
    <warn severity="portability">Obsolescent function 'getcontext' called. Applications are recommended to be rewritten to use POSIX threads.</warn>
  </function>
  <!-- useconds_t ualarm(useconds_t useconds, useconds_t interval); -->
  <function name="ualarm">
    <returnValue type="useconds_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <warn severity="style" reason="Obsolescent" alternatives="timer_create,timer_delete,timer_getoverrun,timer_gettime,timer_settime"/>
  </function>
  <!-- double scalb(double x, double n); -->
  <function name="scalb">
    <returnValue type="double"/>
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <warn severity="style" reason="Obsolescent" alternatives="scalbln,scalblnf,scalbln,scalbn,scalbnf,scalbnl"/>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/bcopy.3.html -->
  <!-- void bcopy(const void *s1, void *s2, size_t n); -->
  <function name="bcopy">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <warn severity="style" reason="Obsolescent" alternatives="memcpy,memmove"/>
  </function>
  <!-- int bcmp(const void *s1, const void *s2, size_t n); -->
  <function name="bcmp">
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <warn severity="style" reason="Obsolescent" alternatives="memcmp"/>
  </function>
  <!-- void bzero(void *s, size_t n); -->
  <function name="bzero">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <warn severity="style" reason="Obsolescent" alternatives="memset"/>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/007904875/functions/ftime.html -->
  <!-- int ftime(struct timeb *tp); -->
  <function name="ftime">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
    <warn severity="style" reason="Obsolescent" alternatives="time,gettimeofday,clock_gettime"/>
  </function>
  <!-- wchar_t *wcswcs(const wchar_t *ws1, const wchar_t *ws2); -->
  <function name="wcswcs">
    <use-retval/>
    <returnValue type="wchar_t *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
    <warn severity="style" reason="Obsolescent" alternatives="wcsstr"/>
  </function>
  <!-- char *stpcpy(char *desstr, const char *srcstr); -->
  <function name="stpcpy">
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <not-overlapping-data ptr1-arg="1" ptr2-arg="2" strlen-arg="2"/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="strlen" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
      <not-bool/>
    </arg>
  </function>
  <!-- char *index(const char *s, int c); -->
  <function name="index">
    <use-retval/>
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <warn severity="style" reason="Obsolescent" alternatives="strchr"/>
  </function>
  <!-- char *rindex(const char *s, int c); -->
  <function name="rindex">
    <use-retval/>
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <warn severity="style" reason="Obsolescent" alternatives="strrchr"/>
  </function>
  <!-- sighandler_t bsd_signal(int signum, sighandler_t handler); -->
  <function name="bsd_signal">
    <returnValue type="sighandler_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <not-bool/>
    </arg>
    <warn severity="style" reason="Obsolescent" alternatives="sigaction"/>
  </function>
  <!-- pid_t fork(void); -->
  <function name="fork">
    <returnValue type="pid_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
  </function>
  <!-- pid_t vfork(void); -->
  <function name="vfork">
    <returnValue type="pid_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <warn severity="style" reason="Obsolescent" alternatives="fork"/>
  </function>
  <!-- int pthread_atfork(void (*prepare)(void), void (*parent)(void), void (*child)(void)); -->
  <function name="pthread_atfork">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="3"/>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/pthread_cond_broadcast.html -->
  <!-- int pthread_cond_signal(pthread_cond_t *cond);
       int pthread_cond_broadcast(pthread_cond_t *cond); -->
  <function name="pthread_cond_signal,pthread_cond_broadcast">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/pthread_create.html -->
  <!-- int pthread_create(pthread_t * thread, const pthread_attr_t * attr, void *(*start_routine)(void*), void * arg); -->
  <function name="pthread_create">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="out">
      <not-null/>
    </arg>
    <arg nr="2" direction="in"/>
    <arg nr="3">
      <not-null/>
    </arg>
    <arg nr="4"/>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/pthread_detach.html -->
  <!-- int pthread_detach(pthread_t thread); -->
  <function name="pthread_detach">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/pthread_equal.html -->
  <!-- int pthread_equal(pthread_t t1, pthread_t t2); -->
  <function name="pthread_equal">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <function name="pthread_exit">
    <noreturn>true</noreturn>
    <arg nr="1"/>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/pthread_join.html -->
  <!-- int pthread_join(pthread_t thread, void **value_ptr); -->
  <function name="pthread_join">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/pthread_kill.html -->
  <!-- int pthread_kill(pthread_t thread, int sig); -->
  <function name="pthread_kill">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/pthread_self.html -->
  <!-- pthread_t pthread_self(void); -->
  <function name="pthread_self">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="pthread_t"/>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/pthread_attr_destroy.html -->
  <!-- int pthread_attr_destroy(pthread_attr_t *attr); -->
  <function name="pthread_attr_destroy">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1" direction="inout">
      <not-null/>
    </arg>
  </function>
  <!-- int pthread_attr_init(pthread_attr_t *attr); -->
  <function name="pthread_attr_init">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1" direction="out">
      <not-null/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/009695399/functions/pthread_attr_getstackaddr.html -->
  <!-- int pthread_attr_setstackaddr(pthread_attr_t *attr, void *stackaddr); -->
  <function name="pthread_attr_setstackaddr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
    <warn severity="style" reason="Obsolescent" alternatives="pthread_attr_setstack"/>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/pthread_attr_setstack.3.html -->
  <!-- int pthread_attr_setstack(pthread_attr_t *attr, void *stackaddr, size_t stacksize); -->
  <function name="pthread_attr_setstack">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int pthread_attr_getstackaddr(const pthread_attr_t *attr, void **stackaddr); -->
  <function name="pthread_attr_getstackaddr">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-null/>
      <not-bool/>
    </arg>
    <warn severity="style" reason="Obsolescent" alternatives="pthread_attr_getstack"/>
  </function>
  <!-- int pthread_attr_setstacksize(pthread_attr_t *attr, size_t stacksize); -->
  <!-- int pthread_attr_setguardsize(pthread_attr_t *attr, size_t guardsize); -->
  <function name="pthread_attr_setstacksize,pthread_attr_setguardsize">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/pthread_attr_getstack.3.html -->
  <!-- int pthread_attr_getstack(const pthread_attr_t *restrict attr, void **restrict stackaddr, size_t *restrict stacksize); -->
  <function name="pthread_attr_getstack">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- int posix_trace_trygetnext_event(trace_id_t trid,
           struct posix_trace_event_info *restrict event,
           void *restrict data, size_t num_bytes,
           size_t *restrict data_len, int *restrict unavailable); -->
  <function name="posix_trace_trygetnext_event">
    <returnValue type="int"/>
    <use-retval/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
    </arg>
    <arg nr="4" direction="in">
      <valid>0:</valid>
    </arg>
    <arg nr="5" direction="out">
      <not-null/>
    </arg>
    <arg nr="6" direction="out">
      <not-null/>
    </arg>
  </function>
  <!-- int posix_trace_timedgetnext_event(trace_id_t trid, struct posix_trace_event_info *restrict event, void *restrict data, size_t num_bytes, size_t *restrict data_len, int *restrict unavailable, const struct timespec *restrict abstime); -->
  <function name="posix_trace_timedgetnext_event">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <use-retval/>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="5" direction="out">
      <not-null/>
    </arg>
    <arg nr="6" direction="out">
      <not-null/>
    </arg>
    <arg nr="7" direction="in">
      <not-null/>
    </arg>
  </function>
  <!-- void posix_trace_event(trace_event_id_t event_id,
       const void *restrictdata_ptr, size_t data_len); -->
  <function name="posix_trace_event">
    <leak-ignore/>
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>1:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- int posix_trace_getnext_event(trace_id_t trid,
           struct posix_trace_event_info *restrict event,
           void *restrict data, size_t num_bytes,
           size_t *restrict data_len, int *restrict unavailable); -->
  <function name="posix_trace_getnext_event">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-null/>
      <minsize type="argvalue" arg="4"/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="5" direction="out">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="6" direction="out">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- int pthread_attr_getstacksize(const pthread_attr_t *attr, size_t *stacksize); -->
  <!-- int pthread_attr_getguardsize(const pthread_attr_t *attr, size_t *guardsize); -->
  <function name="pthread_attr_getstacksize,pthread_attr_getguardsize">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/pthread_mutex_init.html -->
  <!-- int pthread_mutex_init(pthread_mutex_t *restrict mutex, const pthread_mutexattr_t *restrict attr); -->
  <function name="pthread_mutex_init">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1" direction="out">
      <not-null/>
    </arg>
    <arg nr="2" direction="in">
      <!-- NULL is valid -->
    </arg>
  </function>
  <!-- int pthread_mutex_destroy(pthread_mutex_t *mutex); -->
  <function name="pthread_mutex_destroy">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/pthread_mutex_lock.html -->
  <!-- int pthread_mutex_lock(pthread_mutex_t *mutex); -->
  <function name="pthread_mutex_lock">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int pthread_mutex_trylock(pthread_mutex_t *mutex); -->
  <function name="pthread_mutex_trylock">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int pthread_mutex_unlock(pthread_mutex_t *mutex); -->
  <function name="pthread_mutex_unlock">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- char *tempnam(const char *dir, const char *pfx); -->
  <function name="tempnam">
    <use-retval/>
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
    <warn severity="portability">Non reentrant function 'tempnam' called. For threadsafe applications it is recommended to use the reentrant replacement function 'tempnam_r'.</warn>
  </function>
  <!-- char *crypt(const char *key, const char *salt); -->
  <function name="crypt">
    <use-retval/>
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
    <warn severity="portability">Non reentrant function 'crypt' called. For threadsafe applications it is recommended to use the reentrant replacement function 'crypt_r'.</warn>
  </function>
  <!-- char *ttyname(int fd); -->
  <function name="ttyname">
    <use-retval/>
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <valid>0:</valid>
      <not-uninit/>
      <not-bool/>
    </arg>
    <warn severity="portability">Non reentrant function 'ttyname' called. For threadsafe applications it is recommended to use the reentrant replacement function 'ttyname_r'.</warn>
  </function>
  <!-- int ttyname_r(int fd, char *buf, size_t buflen); -->
  <function name="ttyname_r">
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <valid>0:</valid>
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="inout">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- struct spwd *getspnam(const char *name); -->
  <function name="getspnam">
    <use-retval/>
    <returnValue type="struct spwd *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
    <warn severity="portability">Non reentrant function 'getspnam' called. For threadsafe applications it is recommended to use the reentrant replacement function 'getspnam_r'.</warn>
  </function>
  <!-- struct spwd *getspent(void); -->
  <function name="getspent">
    <use-retval/>
    <returnValue type="struct spwd *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <warn severity="portability">Non reentrant function 'getspent' called. For threadsafe applications it is recommended to use the reentrant replacement function 'getspent_r'.</warn>
  </function>
  <!-- struct spwd *fgetspent(FILE *fp); -->
  <function name="fgetspent">
    <use-retval/>
    <returnValue type="struct spwd *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
    <warn severity="portability">Non reentrant function 'fgetspent' called. For threadsafe applications it is recommended to use the reentrant replacement function 'fgetspent_r'.</warn>
  </function>
  <!-- struct spwd *sgetspent(const char *s); -->
  <function name="sgetspent">
    <use-retval/>
    <returnValue type="struct spwd *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
    <warn severity="portability">Non reentrant function 'sgetspent' called. For threadsafe applications it is recommended to use the reentrant replacement function 'sgetspent_r'.</warn>
  </function>
  <!-- struct passwd *fgetpwent(FILE *stream); -->
  <function name="fgetpwent">
    <use-retval/>
    <returnValue type="struct passwd *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
    <warn severity="portability">Non reentrant function 'fgetpwent' called. For threadsafe applications it is recommended to use the reentrant replacement function 'fgetpwent_r'.</warn>
  </function>
  <!-- struct group *getgrent(void); -->
  <function name="getgrent">
    <use-retval/>
    <returnValue type="struct group *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <warn severity="portability">Non reentrant function 'getgrent' called. For threadsafe applications it is recommended to use the reentrant replacement function 'getgrent_r'.</warn>
  </function>
  <!-- int getgrent_r(struct group *gbuf, char *buf, size_t buflen, struct group **gbufp) -->
  <function name="getgrent_r">
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out"/>
    <arg nr="2" direction="out">
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- struct group *fgetgrent(FILE *stream); -->
  <function name="fgetgrent">
    <use-retval/>
    <returnValue type="struct group *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
    <warn severity="portability">Non reentrant function 'fgetgrent' called. For threadsafe applications it is recommended to use the reentrant replacement function 'fgetgrent_r'.</warn>
  </function>
  <!-- int getnetgrent(char **host, char **user, char **domain); -->
  <function name="getnetgrent">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="out">
      <not-null/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
    <warn severity="portability">Non reentrant function 'getnetgrent' called. For threadsafe applications it is recommended to use the reentrant replacement function 'getnetgrent_r'.</warn>
  </function>
  <!-- struct group *getgrnam(const char *name); -->
  <function name="getgrnam">
    <use-retval/>
    <returnValue type="struct group *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
    <warn severity="portability">Non reentrant function 'getgrnam' called. For threadsafe applications it is recommended to use the reentrant replacement function 'getgrnam_r'.</warn>
  </function>
  <!-- struct group *getgrgid(gid_t gid); -->
  <function name="getgrgid">
    <returnValue type="struct group *"/>
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <warn severity="portability">Non reentrant function 'getgrgid' called. For threadsafe applications it is recommended to use the reentrant replacement function 'getgrgid_r'.</warn>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/getgrnam.3.html -->
  <!-- int getgrnam_r(const char *restrict name, struct group *restrict grp, char *restrict buf, size_t buflen, struct group **restrict result); -->
  <function name="getgrnam_r">
    <returnValue type="int"/>
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="4"/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="5" direction="out">
      <not-null/>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/getgrnam.3.html -->
  <!-- int getgrgid_r(gid_t gid, struct group *restrict grp, char *restrict buf, size_t buflen, struct group **restrict result); -->
  <function name="getgrgid_r">
    <returnValue type="int"/>
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="4"/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="5" direction="out">
      <not-null/>
    </arg>
  </function>
  <!-- char *getlogin(void); -->
  <function name="getlogin">
    <returnValue type="char *"/>
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <warn severity="portability">Non reentrant function 'getlogin' called. For threadsafe applications it is recommended to use the reentrant replacement function 'getlogin_r'.</warn>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/getline.3.html -->
  <!-- ssize_t getline(char **lineptr, size_t *n, FILE *stream); -->
  <function name="getline">
    <returnValue type="ssize_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <!-- If *lineptr is set to NULL before the call, then getline() will
         allocate a buffer for storing the line.  This buffer should be
         freed by the user program even if getline() failed.-->
    <arg nr="1" direction="out"/>
    <arg nr="2" direction="inout">
      <not-null/>
    </arg>
    <arg nr="3" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/getline.3.html -->
  <!-- ssize_t getdelim(char **lineptr, size_t *n, int delim, FILE *stream); -->
  <function name="getdelim">
    <returnValue type="ssize_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <!-- If *lineptr is set to NULL before the call, then getdelim() will
         allocate a buffer for storing the line.  This buffer should be
         freed by the user program even if getdelim() failed.-->
    <arg nr="1" direction="out"/>
    <arg nr="2" direction="inout">
      <not-null/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0:255</valid>
    </arg>
    <arg nr="4" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/getlogin.3.html -->
  <!-- int getlogin_r(char *buf, size_t bufsize); -->
  <function name="getlogin_r">
    <returnValue type="int"/>
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- char *ctermid(char *s); -->
  <function name="ctermid">
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-bool/>
    </arg>
    <warn severity="portability">Non reentrant function 'ctermid' called. For threadsafe applications it is recommended to use the reentrant replacement function 'ctermid_r'.</warn>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/realpath.html -->
  <!-- char *realpath(const char *path, char *resolved_path); -->
  <function name="realpath">
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
      <strz/>
    </arg>
    <arg nr="2" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- long telldir(DIR *dirp); -->
  <function name="telldir">
    <returnValue type="long"/>
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/wcswidth.3.html -->
  <!-- int wcswidth(const wchar_t *s, size_t n); -->
  <function name="wcswidth">
    <returnValue type="int"/>
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <minsize type="argvalue" arg="2"/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man3/scandir.3.html -->
  <!-- int scandir(const char *dirp,
                    struct dirent ***namelist,
                    int (*filter)(const struct dirent *),
                    int (*compar)(const struct dirent **, const struct dirent **));-->
  <function name="scandir">
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man3/fileno.3p.html -->
  <!-- int fileno(FILE *stream); -->
  <function name="fileno">
    <returnValue type="int"/>
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int fseeko  (FILE *stream, off_t offset  , int whence); -->
  <!-- int fseeko64(FILE *stream, off64_t offset, int whence); -->
  <function name="fseeko,fseeko64">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:2</valid>
    </arg>
  </function>
  <!-- off_t ftello(FILE *stream); -->
  <function name="ftello">
    <use-retval/>
    <returnValue type="off_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/exec.html -->
  <!-- int execv(const char *path, char *const argv[]);
       int execvp(const char *file, char *const argv[]); -->
  <function name="execv,execvp">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- pid_t wait(int *stat_loc); -->
  <function name="wait">
    <returnValue type="pid_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- pid_t waitpid(pid_t pid, int *stat_loc, int options); -->
  <function name="waitpid">
    <returnValue type="pid_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="out"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- see http://man7.org/linux/man-pages/man3/wcsnlen.3.html-->
  <!-- size_t wcsnlen(const wchar_t *s, size_t maxlen);-->
  <!-- see http://man7.org/linux/man-pages/man3/strnlen.3.html-->
  <!-- size_t strnlen(const char *s, size_t maxlen); -->
  <function name="strnlen,wcsnlen">
    <use-retval/>
    <returnValue type="size_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- https://man7.org/linux/man-pages/man3/wcwidth.3.html -->
  <!-- int wcwidth(wchar_t c); -->
  <function name="wcwidth">
    <returnValue type="int"/>
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man3/ffs.3.html -->
  <!-- int ffs(int i); -->
  <function name="ffs">
    <!-- It returns the position of the first bit set, or 0 if no bits are set in i. -->
    <returnValue type="int">arg1==0 &amp;0</returnValue>
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://pubs.opengroup.org/onlinepubs/9699919799/functions/shmctl.html -->
  <!-- int shmctl(int shmid, int cmd, struct shmid_ds *buf); -->
  <function name="shmctl">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3">
      <!-- direction (in/out) depends on the value of arg2 "cmd" -->
      <not-bool/>
    </arg>
  </function>
  <!-- http://pubs.opengroup.org/onlinepubs/009695399/functions/shmget.html -->
  <!-- int shmget(key_t key, size_t size, int shmflg); -->
  <function name="shmget">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man2/shmat.2.html -->
  <!-- void *shmat(int shmid, const void *shmaddr, int shmflg); -->
  <function name="shmat">
    <noreturn>false</noreturn>
    <returnValue type="void *"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man2/shmat.2.html -->
  <!-- int shmdt(const void *shmaddr);-->
  <function name="shmdt">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-bool/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man2/setrlimit.2.html -->
  <!-- int getrlimit(int resource, struct rlimit *rlim); -->
  <function name="getrlimit">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man2/setrlimit.2.html -->
  <!-- int setrlimit(int resource, const struct rlimit *rlim); -->
  <function name="setrlimit">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man3/glob.3.html -->
  <!-- int glob(const char *pattern, int flags, int (*errfunc) (const char *epath, int eerrno), glob_t *pglob);-->
  <function name="glob">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man3/glob.3.html -->
  <!--  void globfree(glob_t *pglob)-->
  <function name="globfree">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
      <not-null/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/wcpncpy.html -->
  <!-- http://man7.org/linux/man-pages/man3/wcpncpy.3.html -->
  <!-- wchar_t *wcpncpy(wchar_t *dest, const wchar_t *src, size_t n); -->
  <function name="wcpncpy">
    <returnValue type="wchar_t *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <not-overlapping-data ptr1-arg="1" ptr2-arg="2" size-arg="3"/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="3" baseType="wchar_t"/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man3/stpncpy.3.html -->
  <!-- char *stpncpy(char *dest, const char *src, size_t n); -->
  <function name="stpncpy">
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <not-overlapping-data ptr1-arg="1" ptr2-arg="2" size-arg="3"/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man3/memccpy.3.html -->
  <!-- void *memccpy(void *dest, const void *src, int c, size_t n);-->
  <function name="memccpy">
    <leak-ignore/>
    <returnValue type="void *"/>
    <noreturn>false</noreturn>
    <not-overlapping-data ptr1-arg="1" ptr2-arg="2" size-arg="4"/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="sizeof" arg="2"/>
      <minsize type="argvalue" arg="4"/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man3/opterr.3.html-->
  <!-- int getopt(int argc, char * const argv[], const char *optstring); -->
  <function name="getopt">
    <leak-ignore/>
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-null/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man2/setitimer.2.html-->
  <!-- int getitimer(int which, struct itimerval *curr_value); -->
  <function name="getitimer">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man2/setitimer.2.html-->
  <!-- int setitimer(int which, const struct itimerval *new_value, struct itimerval *old_value); -->
  <function name="setitimer">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="out">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man2/sigaction.2.html -->
  <!-- int sigaction(int signum, const struct sigaction *act, struct sigaction *oldact); -->
  <function name="sigaction">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- http://pubs.opengroup.org/onlinepubs/9699919799/functions/sigaltstack.html -->
  <!-- int sigaltstack(const stack_t *restrict ss, stack_t *restrict oss); -->
  <function name="sigaltstack">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/siglongjmp.html -->
  <!-- void siglongjmp(sigjmp_buf env, int val); -->
  <function name="siglongjmp,_longjmp">
    <returnValue type="void"/>
    <noreturn>true</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://pubs.opengroup.org/onlinepubs/9699919799/functions/sigsetjmp.html -->
  <!-- int sigsetjmp(sigjmp_buf env, int savemask); -->
  <function name="sigsetjmp,_setjmp">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="out"/>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int sigsuspend(const sigset_t *mask);-->
  <function name="sigsuspend">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://pubs.opengroup.org/onlinepubs/9699919799/functions/sigprocmask.html -->
  <!-- int pthread_sigmask(int how, const sigset_t *restrict set, sigset_t *restrict oset);
        int sigprocmask(int how, const sigset_t *restrict set, sigset_t *restrict oset); -->
  <function name="pthread_sigmask,sigprocmask">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="out">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- pubs.opengroup.org/onlinepubs/9699919799/functions/sighold.html -->
  <function name="sighold,sigignore,sigpause,sigrelse">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <function name="sigset">
    <noreturn>false</noreturn>
    <returnValue type="void*"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/getrusage.html -->
  <!-- http://man7.org/linux/man-pages/man2/getrusage.2.html-->
  <!-- int getrusage(int who, struct rusage *usage);-->
  <function name="getrusage">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/sigemptyset.html -->
  <!-- http://man7.org/linux/man-pages/man3/sigsetops.3.html -->
  <!-- int sigemptyset(sigset_t *set); -->
  <function name="sigemptyset">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/sigfillset.html -->
  <!-- http://man7.org/linux/man-pages/man3/sigsetops.3.html -->
  <!-- int sigfillset(sigset_t *set); -->
  <function name="sigfillset">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/sigaddset.html -->
  <!-- http://man7.org/linux/man-pages/man3/sigsetops.3.html -->
  <!-- int sigaddset(sigset_t *set, int signum); -->
  <function name="sigaddset">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/sigdelset.html -->
  <!-- http://man7.org/linux/man-pages/man3/sigsetops.3.html -->
  <!-- int sigdelset(sigset_t *set, int signum);-->
  <function name="sigdelset">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://pubs.opengroup.org/onlinepubs/000095399/functions/sigismember.html -->
  <!-- int sigismember(const sigset_t *set, int signum);-->
  <function name="sigismember">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/posix_spawn.html -->
  <!-- int posix_spawn(pid_t *restrict pid, const char *restrict path,
                       const posix_spawn_file_actions_t *file_actions,
                       const posix_spawnattr_t *restrict attrp,
                       char *const argv[restrict], char *const envp[restrict]);
       int posix_spawnp(pid_t *restrict pid, const char *restrict file,
                       const posix_spawn_file_actions_t *file_actions,
                       const posix_spawnattr_t *restrict attrp,
                       char *const argv[restrict], char * const envp[restrict]); -->
  <function name="posix_spawn,posix_spawnp">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out"/>
    <arg nr="2" direction="in">
        <not-uninit/>
        <strz/>
    </arg>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in"/>
    <arg nr="5"/>
    <arg nr="6">
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/msgctl.html -->
  <!-- int msgctl(int msqid, int cmd, struct msqid_ds *buf); -->
  <function name="msgctl">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3">
      <!-- direction (in/out) depends on the value of arg2 "cmd" -->
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/msgget.html -->
  <!-- int msgget(key_t key, int msgflg);-->
  <function name="msgget">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/msgrcv.html -->
  <!-- ssize_t msgrcv(int msqid, void *msgp, size_t msgsz, long msgtyp,
               int msgflg); -->
  <function name="msgrcv">
    <returnValue type="ssize_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="3"/>
      <!-- TODO: sizeof(long) must be added to this value, but this can not be configured yet -->
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/msgsnd.html -->
  <!-- int msgsnd(int msqid, const void *msgp, size_t msgsz, int msgflg); -->
  <function name="msgsnd">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
      <!-- TODO: sizeof(long) must be added to this value, but this can not be configured yet -->
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man3/tcflow.3p.html -->
  <!-- int tcflow(int fildes, int action); -->
  <function name="tcflow">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man3/tcflush.3p.html -->
  <!-- int tcflush(int fildes, int queue_selector); -->
  <function name="tcflush">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/tcsendbreak.html -->
  <!-- http://man7.org/linux/man-pages/man3/tcsendbreak.3p.html -->
  <!-- int tcsendbreak(int fildes, int duration); -->
  <function name="tcsendbreak">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/tcgetattr.html -->
  <!-- http://man7.org/linux/man-pages/man3/tcgetattr.3p.html -->
  <!-- int tcgetattr(int fildes, struct termios *termios_p); -->
  <function name="tcgetattr">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man3/tcsetattr.3p.html -->
  <!-- int tcsetattr(int fildes, int optional_actions, const struct termios *termios_p); -->
  <function name="tcsetattr">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man3/cfsetospeed.3p.html -->
  <!-- int cfsetospeed(struct termios *termios_p, speed_t speed); -->
  <!-- http://man7.org/linux/man-pages/man3/cfsetispeed.3p.html -->
  <!-- int cfsetispeed(struct termios *termios_p, speed_t speed); -->
  <function name="cfsetospeed,cfsetispeed">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- http://man7.org/linux/man-pages/man3/tcdrain.3p.html -->
  <!-- int tcdrain(int fildes); -->
  <function name="tcdrain">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- These are defined as macros http://man7.org/linux/man-pages/man7/inode.7.html -->
  <!-- It is better to configure them as functions -->
  <function name="S_ISREG,S_ISDIR,S_ISCHR,S_ISBLK,S_ISFIFO,S_ISLNK,S_ISSOCK">
    <returnValue type="int"/>
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/swab.html -->
  <!-- void swab(const void * src, void* dest, ssize_t bytes); -->
  <function name="swab">
    <returnValue type="void"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <not-overlapping-data ptr1-arg="1" ptr2-arg="2" size-arg="3"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- void sync(void); -->
  <function name="sync">
    <returnValue type="void"/>
    <noreturn>false</noreturn>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/gethostname.html -->
  <!-- int gethostname(char *name, size_t len); -->
  <function name="gethostname">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>1:</valid>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/9699919799/functions/posix_memalign.html -->
  <!-- int posix_memalign(void **memptr, size_t alignment, size_t size); -->
  <function name="posix_memalign">
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="out">
      <not-null/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- https://pubs.opengroup.org/onlinepubs/7908799/xsh/valloc.html -->
  <!-- void *valloc(size_t size); -->
  <function name="valloc">
    <use-retval/>
    <returnValue type="void *"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <warn severity="style" alternatives="malloc,mmap" reason="Obsolete"/>
  </function>
  <memory>
    <alloc init="false" buffer-size="malloc">valloc</alloc>
    <dealloc>free</dealloc>
  </memory>
  <memory>
    <alloc init="false" arg="1">posix_memalign</alloc>
    <dealloc>free</dealloc>
  </memory>
  <memory>
    <alloc init="false" arg="2">scandir</alloc>
    <dealloc>free</dealloc>
  </memory>
  <memory>
    <alloc init="true">strndup</alloc>
    <alloc init="true">wcsdup</alloc>
    <dealloc>free</dealloc>
  </memory>
  <memory>
    <alloc init="true" arg="1">getline</alloc>
    <alloc init="true" arg="1">getdelim</alloc>
    <dealloc>free</dealloc>
  </memory>
  <memory>
    <alloc init="true">mmap</alloc>
    <alloc init="true">mmap64</alloc>
    <dealloc>munmap</dealloc>
  </memory>
  <resource>
    <alloc init="true">open</alloc>
    <alloc init="true">mkstemp</alloc>
    <alloc init="true">creat</alloc>
    <alloc init="true">openat</alloc>
    <alloc init="true">socket</alloc>
    <dealloc>close</dealloc>
    <dealloc>fdopen</dealloc>
  </resource>
  <resource>
    <alloc init="true">opendir</alloc>
    <alloc init="true">fdopendir</alloc>
    <dealloc>closedir</dealloc>
  </resource>
  <resource>
    <alloc init="true">fdopen</alloc>
    <dealloc>fclose</dealloc>
  </resource>
  <resource>
    <alloc init="true">popen</alloc>
    <dealloc>pclose</dealloc>
  </resource>
  <resource>
    <alloc init="true">mq_open</alloc>
    <dealloc>mq_close</dealloc>
  </resource>
  <memory>
    <alloc init="true" arg="4">getaddrinfo</alloc>
    <dealloc>freeaddrinfo</dealloc>
  </memory>
  <!-- This type definitions refer to http://pubs.opengroup.org/onlinepubs/009695399/basedefs/sys/types.h.html-->
  <podtype name="in_port_t" sign="u" size="2"/>
  <podtype name="in_addr_t" sign="u" size="4"/>
  <podtype name="nfds_t" sign="u"/>
  <podtype name="socklen_t" sign="u" size="4"/>
  <podtype name="sa_family_t" sign="u" size="2"/>
  <podtype name="nlink_t" sign="s"/>
  <podtype name="mode_t" sign="s"/>
  <podtype name="pid_t" sign="s"/>
  <podtype name="uid_t" sign="s"/>
  <podtype name="gid_t" sign="s"/>
  <podtype name="id_t" sign="s"/>
  <podtype name="blkcnt_t" sign="s"/>
  <podtype name="off_t" sign="s"/>
  <podtype name="loff_t" sign="s" size="8"/>
  <podtype name="off64_t" sign="s" size="8"/>
  <podtype name="fsblkcnt_t" sign="u"/>
  <podtype name="fsfilcnt_t" sign="u"/>
  <podtype name="ino_t" sign="u"/>
  <podtype name="useconds_t" sign="u"/>
  <podtype name="suseconds_t" sign="u"/>
  <podtype name="blksize_t" sign="s"/>
  <podtype name="ssize_t" sign="s"/>
  <!-- struct: <podtype name="dirent"/> -->
  <!-- struct: <podtype name="DIR"/> -->
  <podtype name="clockid_t"/>
  <podtype name="dev_t"/>
  <podtype name="key_t"/>
  <podtype name="tms"/>
  <!-- struct: <podtype name="utsname"/> -->
  <podtype name="pthread_attr_t"/>
  <podtype name="pthread_cond_t"/>
  <podtype name="pthread_condattr_t"/>
  <podtype name="pthread_key_t"/>
  <podtype name="pthread_mutexattr_t"/>
  <podtype name="pthread_mutex_t"/>
  <podtype name="pthread_barrier_t"/>
  <podtype name="pthread_barrierattr_t"/>
  <podtype name="pthread_once_t"/>
  <podtype name="pthread_rwlock_t"/>
  <podtype name="pthread_rwlockattr_t"/>
  <podtype name="pthread_spinlock_t"/>
  <podtype name="pthread_t"/>
  <podtype name="timer_t"/>
  <podtype name="trace_attr_t"/>
  <podtype name="trace_event_id_t"/>
  <podtype name="trace_event_set_t"/>
  <podtype name="trace_id_t"/>
  <podtype name="stat"/>
  <podtype name="sigjmp_buf"/>
  <podtype name="sigset_t"/>
  <podtype name="termios"/>
  <podtype name="intptr_t"/>
  <podtype name="timespec"/>
  <!-- struct: <podtype name="timeval"/> -->
  <podtype name="timezone"/>
  <!-- struct: <podtype name="sigaction"/> -->
  <podtype name="stack_t"/>
  <podtype name="mq_attr"/>
  <podtype name="mqd_t"/>
  <podtype name="datum"/>
  <podtype name="DBM"/>
  <podtype name="addrinfo"/>
  <podtype name="hostent"/>
  <podtype name="netent"/>
  <podtype name="protoent"/>
  <podtype name="servent"/>
  <podtype name="passwd"/>
  <podtype name="nl_catd"/>
  <podtype name="nl_item"/>
  <podtype name="regex_t"/>
  <podtype name="sched_param"/>
  <podtype name="glob_t"/>
  <podtype name="locale_t"/>
  <define name="MAP_FAILED" value="-1"/>
  <define name="RTLD_LAZY" value="0x01"/>
  <define name="RTLD_NOW" value="0x02"/>
  <define name="RTLD_GLOBAL" value="0x04"/>
  <define name="RTLD_LOCAL" value="0x08"/>
  <define name="S_IFMT" value="0170000"/>
  <define name="S_IFSOCK" value="0140000"/>
  <define name="S_IFLNK" value="0120000"/>
  <define name="S_IFREG" value="0100000"/>
  <define name="S_IFBLK" value="0060000"/>
  <define name="S_IFDIR" value="0040000"/>
  <define name="S_IFCHR" value="0020000"/>
  <define name="S_IFIFO" value="0010000"/>
  <!-- see http://pubs.opengroup.org/onlinepubs/9699919799/basedefs/signal.h.html
       Some other signals are defined in std.cfg! -->
  <define name="SIGHUP" value="1"/>
  <define name="SIGQUIT" value="3"/>
  <define name="SIGILL" value="4"/>
  <define name="SIGTRAP" value="5"/>
  <define name="SIGBUS" value="7"/>
  <define name="SIGUSR1" value="10"/>
  <define name="SIGUSR2" value="12"/>
  <define name="SIGPIPE" value="13"/>
  <define name="SIGALRM" value="14"/>
  <define name="SIGCHLD" value="17"/>
  <define name="SIGCONT" value="18"/>
  <define name="SIGSTOP" value="19"/>
  <define name="SIGTSTP" value="20"/>
  <define name="SIGTTIN" value="21"/>
  <define name="SIGTTOU" value="22"/>
  <define name="SIGURG" value="23"/>
  <define name="SIGXCPU" value="24"/>
  <define name="SIGXFSZ" value="25"/>
  <define name="SIGVTALRM" value="26"/>
  <define name="SIGPROF" value="27"/>
  <define name="SIGPOLL" value="29"/>
  <define name="SIGSYS" value="31"/>
  <define name="F_OK" value="0"/>
  <define name="R_OK" value="4"/>
  <define name="W_OK" value="2"/>
  <define name="X_OK" value="1"/>
  <!-- https://man7.org/linux/man-pages/man3/lockf.3.html -->
  <define name="F_LOCK" value="1"/>
  <define name="F_TEST" value="3"/>
  <define name="F_TLOCK" value="2"/>
  <define name="F_ULOCK" value="0"/>
</def>
