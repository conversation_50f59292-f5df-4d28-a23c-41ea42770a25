<?xml version="1.0"?>
<def format="2">
  <define name="Py_RETURN_FALSE" value="return Py_INCREF(Py_False), Py_False"/>
  <define name="Py_RETURN_NONE" value="return Py_INCREF(Py_None), Py_None"/>
  <define name="Py_RETURN_TRUE" value="return Py_INCREF(Py_True), Py_True"/>

  <!-- Python C API. see https://docs.python.org/2/c-api/index.html -->
  <!-- ########## Python C API Types ########## -->
  <define name="PY_LONG_LONG" value="long long"/>
  <podtype name="Py_ssize_t" sign="s"/>
  <define name="Py_uintptr_t" value="uintptr_t"/>
  <define name="Py_intptr_t" value="intptr_t"/>
  <define name="PY_UINT32_T" value="uint32_t"/>
  <define name="PY_UINT64_T" value="uint64_t"/>
  <define name="PY_INT32_T" value="int32_t"/>
  <define name="PY_INT64_T" value="int64_t"/>
  <define name="Py_hash_t" value="Py_ssize_t"/>
  <define name="Py_uhash_t" value="size_t"/>
  <define name="Py_ssize_clean_t" value="Py_ssize_t"/>
  <!-- ########## Python C API Macros / Defines ########## -->
  <define name="PY_LLONG_MIN" value="LLONG_MIN"/>
  <define name="PY_LLONG_MAX" value="LLONG_MAX"/>
  <define name="PY_ULLONG_MAX" value="ULLONG_MAX"/>
  <define name="PY_SIZE_MAX" value="SIZE_MAX"/>
  <define name="PY_SSIZE_T_MAX" value="((Py_ssize_t)(((size_t)-1)&gt;&gt;1))"/>
  <define name="PY_SSIZE_T_MIN" value="(-PY_SSIZE_T_MAX-1)"/>
  <define name="Py_MEMCPY" value="memcpy"/>
  <define name="Py_FORCE_EXPANSION(X)" value="X"/>
  <define name="Py_SAFE_DOWNCAST(VALUE, WIDE, NARROW)" value="(NARROW)(VALUE)"/>
  <define name="Py_ALIGNED(x)" value=""/>
  <define name="Py_VA_COPY" value="va_copy"/>
  <define name="PyMem_MALLOC(n)" value="PyMem_Malloc(n)"/>
  <define name="PyMem_REALLOC(p, n)" value="PyMem_Realloc(p, n)"/>
  <define name="PyMem_FREE(p)" value="PyMem_Free(p)"/>
  <define name="PyMem_Del" value="PyMem_Free"/>
  <define name="PyMem_DEL" value="PyMem_FREE"/>
  <define name="PyMem_New(type, n)" value="( (type *) PyMem_Malloc((n) * sizeof(type)) )"/>
  <define name="PyMem_NEW(type, n)" value="( (type *) PyMem_MALLOC((n) * sizeof(type)) )"/>
  <define name="PyMem_Resize(p, type, n)" value="( (p) = ((size_t)(n) &gt; PY_SSIZE_T_MAX / sizeof(type)) ? NULL : (type *) PyMem_Realloc((p), (n) * sizeof(type)) )"/>
  <define name="PyMem_RESIZE(p, type, n)" value="( (p) = ((size_t)(n) &gt; PY_SSIZE_T_MAX / sizeof(type)) ? NULL : (type *) PyMem_REALLOC((p), (n) * sizeof(type)) )"/>
  <define name="PyObject_MALLOC" value="PyObject_Malloc"/>
  <define name="PyObject_REALLOC" value="PyObject_Realloc"/>
  <define name="PyObject_FREE" value="PyObject_Free"/>
  <define name="PyObject_Del" value="PyObject_Free"/>
  <define name="PyObject_DEL" value="PyObject_Free"/>
  <define name="PyObject_New(type, typeobj)" value="( (type *) _PyObject_New(typeobj) )"/>
  <define name="PyObject_NewVar(type, typeobj, n)" value="( (type *) _PyObject_NewVar((typeobj), (n)) )"/>
  <!-- ########## Python C API Allocation / Deallocation ########## -->
  <memory>
    <alloc init="false" buffer-size="malloc">PyMem_Malloc</alloc>
    <alloc init="true" buffer-size="calloc">PyMem_Calloc</alloc>
    <realloc init="false" buffer-size="malloc:2">PyMem_Realloc</realloc>
    <dealloc>PyMem_Free</dealloc>
  </memory>
  <memory>
    <alloc init="false" buffer-size="malloc">PyMem_RawMalloc</alloc>
    <alloc init="true" buffer-size="calloc">PyMem_RawCalloc</alloc>
    <realloc init="false" buffer-size="malloc:2">PyMem_RawRealloc</realloc>
    <dealloc>PyMem_RawFree</dealloc>
  </memory>
  <memory>
    <alloc init="false" buffer-size="malloc">PyObject_Malloc</alloc>
    <alloc init="true" buffer-size="calloc">PyObject_Calloc</alloc>
    <realloc init="false" buffer-size="malloc:2">PyObject_Realloc</realloc>
    <dealloc>PyObject_Free</dealloc>
  </memory>
  <!-- ########## Python C API Functions ########## -->
  <!-- Those are macros, but it's helpful to declare a function here -->
  <!-- void Py_INCREF(PyObject *o) -->
  <!-- void Py_DECREF(PyObject *o) -->
  <function name="Py_INCREF,Py_IncRef,Py_DECREF,Py_DecRef">
    <leak-ignore/>
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- Those are macros, but it's helpful to declare a function here -->
  <!-- void Py_XINCREF(PyObject *o) -->
  <!-- void Py_XDECREF(PyObject *o) -->
  <!-- void Py_CLEAR(PyObject *o) -->
  <function name="Py_XINCREF,Py_XDECREF,Py_CLEAR">
    <leak-ignore/>
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- void Py_Exit(int status) -->
  <function name="Py_Exit">
    <noreturn>true</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void Py_FatalError(const char *message) -->
  <function name="Py_FatalError">
    <noreturn>true</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void Py_Initialize() -->
  <!-- void Py_Finalize() -->
  <function name="Py_Initialize,Py_Finalize">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
  </function>
  <!-- void Py_InitializeEx(int initsigs) -->
  <function name="Py_InitializeEx">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-bool/>
      <not-uninit/>
    </arg>
  </function>
  <!-- PyObject* Py_InitModule(char *name, PyMethodDef *methods) -->
  <function name="Py_InitModule">
    <noreturn>false</noreturn>
    <returnValue type="PyObject*"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
  </function>
  <!-- PyObject* Py_InitModule3(char *name, PyMethodDef *methods, char *doc) -->
  <function name="Py_InitModule3">
    <noreturn>false</noreturn>
    <returnValue type="PyObject*"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
    <arg nr="3">
      <not-uninit/>
    </arg>
  </function>
  <!-- PyObject* Py_InitModule4(char *name, PyMethodDef *methods, char *doc, PyObject *self, int apiver) -->
  <function name="Py_InitModule4">
    <noreturn>false</noreturn>
    <returnValue type="PyObject*"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
    <arg nr="3">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int Py_IsInitialized() -->
  <function name="Py_IsInitialized">
    <noreturn>false</noreturn>
    <pure/>
    <returnValue type="int"/>
    <use-retval/>
  </function>
  <!-- int Py_Main(int argc, wchar_t **argv) -->
  <function name="Py_Main">
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
  </function>
  <!-- void Py_SetProgramName(const wchar_t *name) -->
  <!-- void Py_SetPythonHome(const wchar_t *home) -->
  <function name="Py_SetProgramName,Py_SetPythonHome">
    <leak-ignore/>
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- int PyArg_ParseTuple(PyObject *args, const char *format, ...) -->
  <function name="PyArg_ParseTuple">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="any" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- int PyArg_VaParse(PyObject *args, const char *format, va_list vargs) -->
  <function name="PyArg_VaParse">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int PyArg_ParseTupleAndKeywords(PyObject *args, PyObject *kw, const char *format, char *keywords[], ...) -->
  <function name="PyArg_ParseTupleAndKeywords">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="any" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- PyObject* PyDict_New() -->
  <function name="PyDict_New">
    <noreturn>false</noreturn>
    <returnValue type="PyObject*"/>
    <use-retval/>
  </function>
  <!-- int PyDict_SetItem(PyObject *p, PyObject *key, PyObject *val) -->
  <function name="PyDict_SetItem">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
    <arg nr="3">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int PyDict_SetItemString(PyObject *p, const char *key, PyObject *val) -->
  <function name="PyDict_SetItemString">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void PyErr_Clear() -->
  <function name="PyErr_Clear">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
  </function>
  <!-- PyObject* PyErr_Format(PyObject *exception, const char *format, ...) -->
  <function name="PyErr_Format">
    <noreturn>false</noreturn>
    <returnValue type="PyObject*">NULL</returnValue>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="variadic" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- PyObject* PyErr_FormatV(PyObject *exception, const char *format, va_list vargs) -->
  <function name="PyErr_FormatV">
    <noreturn>false</noreturn>
    <returnValue type="PyObject*">NULL</returnValue>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- PyObject* PyErr_NewException(const char *name, PyObject *base, PyObject *dict) -->
  <function name="PyErr_NewException">
    <noreturn>false</noreturn>
    <returnValue type="PyObject*"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
    <arg nr="3">
      <not-uninit/>
    </arg>
  </function>
  <!-- PyObject* PyErr_NewExceptionWithDoc(const char *name, const char *doc, PyObject *base, PyObject *dict) -->
  <function name="PyErr_NewExceptionWithDoc">
    <noreturn>false</noreturn>
    <returnValue type="PyObject*"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3">
      <not-uninit/>
    </arg>
    <arg nr="4">
      <not-uninit/>
    </arg>
  </function>
  <!-- PyObject* PyErr_SetFromErrno(PyObject *type) -->
  <function name="PyErr_SetFromErrno">
    <noreturn>false</noreturn>
    <returnValue type="PyObject*">NULL</returnValue>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- void PyErr_SetObject(PyObject *type, PyObject *value) -->
  <function name="PyErr_SetObject">
    <leak-ignore/>
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- void PyErr_SetString(PyObject *type, const char *message) -->
  <function name="PyErr_SetString">
    <leak-ignore/>
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <strz/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <function name="Py_GetProgramName,Py_GetExecPrefix,Py_GetProgramFullPath,Py_GetPath,Py_GetPythonHome">
    <noreturn>false</noreturn>
    <returnValue type="char *"/>
    <pure/>
    <use-retval/>
  </function>
  <function name="Py_GetVersion,Py_GetPlatform,Py_GetCopyright,Py_GetCompiler,Py_GetBuildInfo">
    <noreturn>false</noreturn>
    <returnValue type="const char *"/>
    <pure/>
    <use-retval/>
  </function>
  <!-- https://docs.python.org/2.0/ext/parseTuple.html
    PyObject *Py_BuildValue(char *format, ...); -->
  <function name="Py_BuildValue">
    <returnValue type="PyObject *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <use-retval/>
    <arg nr="1">
      <not-uninit/>
      <formatstr/>
    </arg>
    <arg nr="2"/>
  </function>
  <!-- PyObject* PyBytes_FromString(const char *v) -->
  <function name="PyBytes_FromString">
    <noreturn>false</noreturn>
    <returnValue type="PyObject*"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- double PyFloat_AsDouble(PyObject *pyfloat) -->
  <function name="PyFloat_AsDouble">
    <noreturn>false</noreturn>
    <returnValue type="double"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- double PyFloat_AS_DOUBLE(PyObject *pyfloat) -->
  <function name="PyFloat_AS_DOUBLE">
    <noreturn>false</noreturn>
    <returnValue type="double"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int PyFloat_Check(PyObject *p) -->
  <!-- int PyFloat_CheckExact(PyObject *p) -->
  <function name="PyFloat_Check,PyFloat_CheckExact">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- PyObject* PyFloat_FromDouble(double v) -->
  <function name="PyFloat_FromDouble">
    <noreturn>false</noreturn>
    <returnValue type="PyObject*"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- PyObject* PyFloat_FromString(PyObject *str) -->
  <function name="PyFloat_FromString">
    <noreturn>false</noreturn>
    <returnValue type="PyObject*"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int PyInt_Check(PyObject *o) -->
  <function name="PyInt_Check">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-bool/>
      <not-uninit/>
    </arg>
  </function>
  <!-- PyObject* PyInt_FromLong(long ival) -->
  <function name="PyInt_FromLong">
    <returnValue type="PyObject *"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-uninit/>
    </arg>
  </function>
  <!-- void* PyMem_Calloc(size_t nelem, size_t elsize) -->
  <!-- void* PyMem_RawCalloc(size_t nelem, size_t elsize) -->
  <function name="PyMem_Calloc,PyMem_RawCalloc">
    <use-retval/>
    <noreturn>false</noreturn>
    <returnValue type="void *"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>1:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- void PyMem_Free(void *p) -->
  <!-- void PyMem_RawFree(void *p) -->
  <function name="PyMem_Free,PyMem_RawFree">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- void* PyMem_Malloc(size_t n) -->
  <!-- void* PyMem_RawMalloc(size_t n) -->
  <function name="PyMem_Malloc,PyMem_RawMalloc">
    <noreturn>false</noreturn>
    <returnValue type="void *"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- void* PyMem_Realloc(void *p, size_t n) -->
  <!-- void* PyMem_RawRealloc(void *p, size_t n) -->
  <function name="PyMem_Realloc,PyMem_RawRealloc">
    <returnValue type="void *"/>
    <use-retval/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- int PyModule_AddIntConstant(PyObject *module, const char *name, long value) -->
  <function name="PyModule_AddIntConstant">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1" direction="inout">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int PyModule_AddObject(PyObject *module, const char *name, PyObject *value) -->
  <!-- The hint "This means that its return value must be checked, and calling code must Py_DECREF()
       value manually on error." in the documentation suggests to add use-retval. -->
  <function name="PyModule_AddObject">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- int PyModule_AddStringConstant(PyObject *module, const char *name, const char *value) -->
  <function name="PyModule_AddStringConstant">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1" direction="inout">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
      <not-bool/>
    </arg>
  </function>
  <!-- void PyObject_Del(void *op) -->
  <function name="PyObject_Del">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- PyObject* PyObject_GetAttrString(PyObject *o, const char *attr_name) -->
  <function name="PyObject_GetAttrString">
    <noreturn>false</noreturn>
    <returnValue type="PyObject*"/>
    <use-retval/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
      <not-bool/>
    </arg>
  </function>
  <!-- PyAPI_FUNC(PyObject *) _PyObject_New(PyTypeObject *); -->
  <function name="_PyObject_New">
    <noreturn>false</noreturn>
    <returnValue type="PyObject *"/>
    <use-retval/>
    <arg nr="1"/>
  </function>
  <!-- PyAPI_FUNC(PyVarObject *) _PyObject_NewVar(PyTypeObject *, Py_ssize_t); -->
  <function name="_PyObject_NewVar">
    <noreturn>false</noreturn>
    <returnValue type="PyVarObject *"/>
    <use-retval/>
    <arg nr="1"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- PyObject* PyString_FromString(const char *v) -->
  <function name="PyString_FromString">
    <noreturn>false</noreturn>
    <returnValue type="PyObject*"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- Deprecated DL_IMPORT and DL_EXPORT macros -->
  <define name="DL_IMPORT(RTYPE)" value="RTYPE"/>
  <define name="DL_EXPORT(RTYPE)" value="RTYPE"/>
  <define name="Py_BEGIN_ALLOW_THREADS" value=""/>
  <define name="Py_END_ALLOW_THREADS" value=""/>
  <define name="Py_BLOCK_THREADS" value=""/>
  <define name="Py_UNBLOCK_THREADS" value=""/>
</def>
