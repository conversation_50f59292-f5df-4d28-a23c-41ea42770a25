<?xml version="1.0"?>
<def format="2">
  <markup ext=".qml" reporterrors="false" aftercode="true">
    <!-- keywords in QML code to ignore -->
    <keywords>
      <keyword name="if"/>
      <keyword name="while"/>
      <keyword name="typeof"/>
      <keyword name="for"/>
    </keywords>
    <!-- code blocks are meta-code/pseudo code placed in the library
         that is used/called by the native c/c++ code -->
    <codeblocks>
      <!-- need to add all the QML function names below -->
      <block name="onClicked"/>
      <block name="onFinished"/>
      <block name="onTriggered"/>
      <block name="onRetrieveTriggered"/>
      <block name="onPressed"/>
      <block name="onTouch"/>
      <block name="onFocusedChanged"/>
      <block name="onSubmittedNewStatusChanged"/>
      <block name="onCreationCompleted"/>
      <block name="onFileSelected"/>
      <!-- code block structure in QML is:
            onClicked: {
              call(var)
            } -->
      <structure offset="3" start="{" end="}"/>
      <!-- the start block is '3' tokens after the
           name token so we skip them -->
    </codeblocks>
    <codeblocks>
      <block name="function"/>
      <!-- code block structure in QML is:
            funnction x(args): {
              call(var)
            } -->
      <structure offset="2" start="{" end="}"/>
    </codeblocks>
    <!-- Qt Properties have the format :
           Q_PROPERTY(<type> <name> READ <func> WRITE <func> NOTIFY <func>)
           the READ/WRITE/NOTIFY parts are optional -->
    <exported>
      <exporter prefix="Q_PROPERTY">
        <suffix>READ</suffix>
        <!-- catch the element before READ if present -->
        <prefix>READ</prefix>
        <prefix>WRITE</prefix>
        <prefix>NOTIFY</prefix>
      </exporter>
    </exported>
    <!-- qml files can call connect on the c++ code -->
    <imported>
      <importer>connect</importer>
    </imported>
  </markup>
  <!-- qt can call methods as strings using invokeMethod -->
  <reflection>
    <call arg="2">invokeMethod</call>
  </reflection>
  <define name="SIGNAL(X)" value="#X"/>
  <define name="SLOT(X)" value="#X"/>
  <!-- T qAbs(const T &t) -->
  <function name="qAbs">
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- const T &qBound(const T &min, const T &val, const T &max) -->
  <function name="qBound">
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void qDeleteAll(ForwardIterator begin, ForwardIterator end) -->
  <!-- void qDeleteAll(const Container &c) -->
  <function name="qDeleteAll">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" default="">
      <not-uninit/>
    </arg>
  </function>
  <!-- quint32 qFloatDistance(float a, float b) -->
  <!-- quint64 qFloatDistance(double a, double b) -->
  <function name="qFloatDistance">
    <noreturn>false</noreturn>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QString qFormatLogMessage(QtMsgType type, const QMessageLogContext &context, const QString &str) -->
  <function name="qFormatLogMessage">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool qFuzzyCompare(double p1, double p2) -->
  <!-- bool qFuzzyCompare(float p1, float p2) -->
  <function name="qFuzzyCompare">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool qFuzzyIsNull(double d) -->
  <!-- bool qFuzzyIsNull(float f) -->
  <function name="qFuzzyIsNull">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QByteArray qgetenv(const char *varName) -->
  <function name="qgetenv">
    <noreturn>false</noreturn>
    <returnValue type="QByteArray"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- double qInf() -->
  <function name="qInf">
    <noreturn>false</noreturn>
    <returnValue type="double"/>
    <use-retval/>
  </function>
  <!-- bool	qIsFinite(double d) -->
  <!-- bool qIsFinite(float f) -->
  <function name="qIsFinite">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool qIsInf(double d) -->
  <!-- bool qIsInf(float f) -->
  <function name="qIsInf">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool qIsNaN(double d) -->
  <!-- bool qIsNaN(float f) -->
  <function name="qIsNaN">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- const T &qMax(const T &a, const T &b) -->
  <function name="qMax">
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- const T &qMin(const T &a, const T &b) -->
  <function name="qMin">
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- const char *qPrintable(const QString &str) -->
  <function name="qPrintable">
    <noreturn>false</noreturn>
    <returnValue type="const char *"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool qputenv(const char *varName, const QByteArray &value) -->
  <function name="qputenv">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- double qQNaN() -->
  <function name="qQNaN">
    <noreturn>false</noreturn>
    <returnValue type="double"/>
    <use-retval/>
  </function>
  <!-- int qRound(double d) -->
  <!-- int qRound(float d) -->
  <function name="qRound">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- qint64 qRound64(double d) -->
  <!-- qint64 qRound64(float d) -->
  <function name="qRound64">
    <noreturn>false</noreturn>
    <returnValue type="qint64"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- double qSNaN() -->
  <function name="qSNaN">
    <noreturn>false</noreturn>
    <returnValue type="double"/>
    <use-retval/>
  </function>
  <!-- void qSort(RandomAccessIterator begin, RandomAccessIterator end) -->
  <!-- void qSort(RandomAccessIterator begin, RandomAccessIterator end, LessThan lessThan) -->
  <!-- void qSort(Container &container) -->
  <function name="qSort">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <warn severity="style" alternatives="std::sort" reason="Obsolete"/>
    <arg nr="1"/>
    <arg nr="2" default=""/>
    <arg nr="3" default=""/>
  </function>
  <!-- void qStableSort(RandomAccessIterator begin, RandomAccessIterator end) -->
  <!-- void qStableSort(RandomAccessIterator begin, RandomAccessIterator end, LessThan lessThan) -->
  <!-- void qStableSort(Container &container) -->
  <function name="qStableSort">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <warn severity="style" alternatives="std::stable_sort" reason="Obsolete"/>
    <arg nr="1"/>
    <arg nr="2" default=""/>
    <arg nr="3" default=""/>
  </function>
  <!-- void qSwap(T &var1, T &var2) -->
  <function name="qSwap">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <warn severity="style" alternatives="std::swap" reason="Obsolete"/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- QString qtTrId(const char *id, int n = ...) -->
  <function name="qtTrId">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in" default="">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool qunsetenv(const char *varName) -->
  <function name="qunsetenv">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- const char *qUtf8Printable(const QString &str) -->
  <function name="qUtf8Printable">
    <noreturn>false</noreturn>
    <returnValue type="const char *"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- const wchar_t *qUtf16Printable(const QString &str) -->
  <function name="qUtf16Printable">
    <noreturn>false</noreturn>
    <returnValue type="const wchar_t *"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- const char *qVersion() -->
  <function name="qVersion">
    <noreturn>false</noreturn>
    <returnValue type="const char *"/>
    <use-retval/>
  </function>
  <!-- QMetaObject::Connection QObject::connect(const QObject *sender, const char *signal, const char *method, Qt::ConnectionType type = Qt::AutoConnection) const -->
  <!-- QMetaObject::Connection QObject::connect(const QObject *sender, const char *signal, const QObject *receiver, const char *method, Qt::ConnectionType type = Qt::AutoConnection) // static -->
  <!-- QMetaObject::Connection QObject::connect(const QObject *sender, const QMetaMethod &signal, const QObject *receiver, const QMetaMethod &method, Qt::ConnectionType type = Qt::AutoConnection) // static -->
  <!-- QMetaObject::Connection QObject::connect(const QObject *sender, PointerToMemberFunction signal, const QObject *receiver, PointerToMemberFunction method, Qt::ConnectionType type = Qt::AutoConnection) // static -->
  <!-- QMetaObject::Connection QObject::connect(const QObject *sender, PointerToMemberFunction signal, Functor functor) // static -->
  <!-- QMetaObject::Connection QObject::connect(const QObject *sender, PointerToMemberFunction signal, const QObject *context, Functor functor, Qt::ConnectionType type = Qt::AutoConnection) // static -->
  <function name="QObject::connect">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" default="0">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="5" default="0">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool QObject::disconnect(const char *signal = Q_NULLPTR, const QObject *receiver = Q_NULLPTR, const char *method = Q_NULLPTR) const -->
  <!-- bool QObject::disconnect(const QObject *receiver, const char *method = Q_NULLPTR) const -->
  <!-- bool QObject::disconnect(const QObject *sender, const char *signal, const QObject *receiver, const char *method) // static -->
  <!-- bool	QObject::disconnect(const QObject *sender, const QMetaMethod &signal, const QObject *receiver, const QMetaMethod &method) // static -->
  <!-- bool QObject::disconnect(const QMetaObject::Connection &connection) // static -->
  <!-- bool QObject::disconnect(const QObject *sender, PointerToMemberFunction signal, const QObject *receiver, PointerToMemberFunction method) // static -->
  <function name="disconnect,QObject::disconnect">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" default="0">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" default="0">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" default="0">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QAction *QMenu::addAction(const QString &text) -->
  <!-- QAction *QMenu::addAction(const QIcon &icon, const QString &text) -->
  <!-- QAction *QMenu::addAction(const QString &text, const QObject *receiver, const char *member, const QKeySequence &shortcut = 0) -->
  <!-- QAction *QMenu::addAction(const QIcon &icon, const QString &text, const QObject *receiver, const char *member, const QKeySequence &shortcut = 0) -->
  <!-- QAction *QMenu::addAction(const QString &text, const QObject *receiver, PointerToMemberFunction method, const QKeySequence &shortcut = ...) -->
  <!-- QAction *QMenu::addAction(const QString &text, Functor functor, const QKeySequence &shortcut = ...) -->
  <!-- QAction *QMenu::addAction(const QString &text, const QObject *context, Functor functor, const QKeySequence &shortcut = 0) -->
  <!-- QAction *QMenu::addAction(const QIcon &icon, const QString &text, const QObject *receiver, PointerToMemberFunction method, const QKeySequence &shortcut = ...) -->
  <!-- QAction *QMenu::addAction(const QIcon &icon, const QString &text, Functor functor, const QKeySequence &shortcut = ...) -->
  <!-- QAction *QMenu::addAction(const QIcon &icon, const QString &text, const QObject *context, Functor functor, const QKeySequence &shortcut = 0) -->
  <!-- void QWidget::addAction(QAction *action) -->
  <function name="QMenu::addAction">
    <noreturn>false</noreturn>
    <returnValue type="QAction *"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <!-- QString QObject::tr(const char *sourceText, const char *disambiguation = Q_NULLPTR, int n = -1) //static -->
  <function name="tr,QObject::tr">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" default="-1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QSettings https://doc.qt.io/qt-5/qsettings.html -->
  <!-- QStringList QSettings::allKeys() const -->
  <function name="QSettings::allKeys">
    <noreturn>false</noreturn>
    <returnValue type="QStringList"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QString QSettings::applicationName() const -->
  <function name="QSettings::applicationName">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <const/>
  </function>
  <!-- void QSettings::beginGroup(const QString &prefix) -->
  <function name="QSettings::beginGroup">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int QSettings::beginReadArray(const QString &prefix) -->
  <function name="QSettings::beginReadArray">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QSettings::beginWriteArray(const QString &prefix, int size = -1) -->
  <function name="QSettings::beginWriteArray">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in" default="-1">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QStringList QSettings::childGroups() const -->
  <!-- QStringList QSettings::childKeys() const -->
  <function name="QSettings::childGroups,QSettings::childKeys">
    <noreturn>false</noreturn>
    <returnValue type="QStringList"/>
    <use-retval/>
    <const/>
  </function>
  <!-- void QSettings::clear() -->
  <function name="QSettings::clear">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
  </function>
  <!-- bool QSettings::contains(const QString &key) const -->
  <function name="QSettings::contains">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QSettings::Format QSettings::defaultFormat() // static -->
  <function name="QSettings::defaultFormat">
    <noreturn>false</noreturn>
    <returnValue type="QSettings::Format"/>
    <use-retval/>
    <const/>
  </function>
  <!-- void QSettings::endArray() -->
  <!-- void QSettings::endGroup() -->
  <function name="QSettings::endArray,QSettings::endGroup">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
  </function>
  <!-- bool QSettings::event(QEvent *event) -->
  <function name="QSettings::event">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <arg nr="1">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool QSettings::fallbacksEnabled() const -->
  <function name="QSettings::fallbacksEnabled">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QString QSettings::fileName() const -->
  <function name="QSettings::fileName">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QSettings::Format QSettings::format() const -->
  <function name="QSettings::format">
    <noreturn>false</noreturn>
    <returnValue type="QSettings::Format"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QString QSettings::group() const -->
  <function name="QSettings::group">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QTextCodec *QSettings::iniCodec() const -->
  <function name="QSettings::iniCodec">
    <noreturn>false</noreturn>
    <returnValue type="QTextCodec *"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool QSettings::isAtomicSyncRequired() const -->
  <!-- bool QSettings::isWritable() const -->
  <function name="QSettings::isAtomicSyncRequired,QSettings::isWritable">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QString QSettings::organizationName() const -->
  <function name="QSettings::organizationName">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QSettings::Format QSettings::registerFormat(const QString &extension, QSettings::ReadFunc readFunc, QSettings::WriteFunc writeFunc, Qt::CaseSensitivity caseSensitivity = Qt::CaseSensitive) // static -->
  <function name="QSettings::registerFormat">
    <noreturn>false</noreturn>
    <returnValue type="QSettings::Format"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2"/>
    <arg nr="3"/>
    <arg nr="4" direction="in" default="Qt::CaseSensitive">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QSettings::remove(const QString &key) -->
  <function name="QSettings::remove">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QSettings::Scope QSettings::scope() const -->
  <function name="QSettings::scope">
    <noreturn>false</noreturn>
    <returnValue type="QSettings::Scope"/>
    <use-retval/>
    <const/>
  </function>
  <!-- void QSettings::setArrayIndex(int i) -->
  <function name="QSettings::setArrayIndex">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QSettings::setAtomicSyncRequired(bool enable) -->
  <function name="QSettings::setAtomicSyncRequired">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QSettings::setDefaultFormat(QSettings::Format format) // static -->
  <function name="QSettings::setDefaultFormat">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QSettings::setFallbacksEnabled(bool b) -->
  <function name="QSettings::setFallbacksEnabled">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QSettings::setIniCodec(QTextCodec *codec) -->
  <!-- void QSettings::setIniCodec(const char *codecName) -->
  <function name="QSettings::setIniCodec">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QSettings::setPath(QSettings::Format format, QSettings::Scope scope, const QString &path) // static -->
  <function name="QSettings::setPath">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QSettings::setValue(const QString &key, const QVariant &value) -->
  <function name="QSettings::setValue">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <strz/>
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QSettings::Status QSettings::status() const -->
  <function name="QSettings::status">
    <noreturn>false</noreturn>
    <returnValue type="QSettings::Status"/>
    <use-retval/>
    <const/>
  </function>
  <!-- void QSettings::sync() -->
  <function name="QSettings::sync">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
  </function>
  <!-- QVariant QSettings::value(const QString &key, const QVariant &defaultValue = QVariant()) const -->
  <function name="QSettings::value">
    <noreturn>false</noreturn>
    <returnValue type="QVariant"/>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <strz/>
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="2" default="QVariant()" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool QString::isEmpty() const -->
  <!-- bool QString::isLower() const -->
  <!-- bool QString::isUpper() const -->
  <!-- bool QString::isNull() const -->
  <!-- bool QString::isRightToLeft() const -->
  <!-- bool QString::isValidUtf16() const -->
  <function name="QString::isEmpty,QString::isLower,QString::isNull,QString::isRightToLeft,QString::isUpper,QString::isValidUtf16">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
  </function>
  <!-- QString & QString::sprintf(const char * cformat, ...); -->
  <function name="QString::sprintf">
    <noreturn>false</noreturn>
    <returnValue type="QString &amp;"/>
    <leak-ignore/>
    <formatstr/>
    <arg nr="1" direction="in">
      <formatstr/>
      <not-uninit/>
    </arg>
    <warn severity="style" alternatives="QString::asprintf,QString::arg,QTextStream" reason="Obsolete"/>
  </function>
  <!-- void QList::append(const T &value) -->
  <!-- void QList::append(const QList<T> &value) -->
  <!-- QStringList inherits from QList -->
  <function name="QList::append,QStringList::append">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QString & QString::append(const QString &str); -->
  <!-- QString & QString::append(const QChar *str, int len); -->
  <!-- QString & QString::append(QChar ch); -->
  <!-- QString & QString::append(const QStringRef &reference); -->
  <!-- QString & QString::append(QLatin1String str); -->
  <!-- QString & QString::append(const char *str); -->
  <!-- QString & QString::append(const QByteArray &ba); -->
  <function name="QString::append">
    <noreturn>false</noreturn>
    <returnValue type="QString &amp;"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QStandardItem::appendRow(const QList<QStandardItem *> &items) -->
  <!-- void QStandardItem::appendRow(QStandardItem *item) -->
  <!-- void QStandardItem::appendRows(const QList<QStandardItem *> &items) -->
  <!-- void QStandardItemModel::appendRow(const QList<QStandardItem *> &items) -->
  <!-- void QStandardItemModel::appendRow(QStandardItem *item) -->
  <function name="QStandardItem::appendRow,QStandardItem::appendRows,QStandardItemModel::appendRow">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- QString QString::arg(const QString & a, int fieldWidth = 0, const QChar & fillChar = QLatin1Char( ' ' )) const -->
  <!-- QString QString::arg(const QString & a1, const QString & a2) const -->
  <!-- QString QString::arg(const QString & a1, const QString & a2, const QString & a3) const -->
  <!-- QString QString::arg(const QString & a1, const QString & a2, const QString & a3, const QString & a4) const -->
  <!-- QString QString::arg(const QString & a1, const QString & a2, const QString & a3, const QString & a4, const QString & a5) const -->
  <!-- QString QString::arg(const QString & a1, const QString & a2, const QString & a3, const QString & a4, const QString & a5, const QString & a6) const -->
  <!-- QString QString::arg(const QString & a1, const QString & a2, const QString & a3, const QString & a4, const QString & a5, const QString & a6, const QString & a7) const -->
  <!-- QString QString::arg(const QString & a1, const QString & a2, const QString & a3, const QString & a4, const QString & a5, const QString & a6, const QString & a7, const QString & a8) const -->
  <!-- QString QString::arg(const QString & a1, const QString & a2, const QString & a3, const QString & a4, const QString & a5, const QString & a6, const QString & a7, const QString & a8, const QString & a9) const -->
  <!-- QString QString::arg(int a, int fieldWidth = 0, int base = 10, const QChar & fillChar = QLatin1Char( ' ' )) const -->
  <!-- QString QString::arg(uint a, int fieldWidth = 0, int base = 10, const QChar & fillChar = QLatin1Char( ' ' )) const -->
  <!-- QString QString::arg(long a, int fieldWidth = 0, int base = 10, const QChar & fillChar = QLatin1Char( ' ' )) const -->
  <!-- QString QString::arg(ulong a, int fieldWidth = 0, int base = 10, const QChar & fillChar = QLatin1Char( ' ' )) const -->
  <!-- QString QString::arg(qlonglong a, int fieldWidth = 0, int base = 10, const QChar & fillChar = QLatin1Char( ' ' )) const -->
  <!-- QString QString::arg(qulonglong a, int fieldWidth = 0, int base = 10, const QChar & fillChar = QLatin1Char( ' ' )) const -->
  <!-- QString QString::arg(short a, int fieldWidth = 0, int base = 10, const QChar & fillChar = QLatin1Char( ' ' )) const -->
  <!-- QString QString::arg(ushort a, int fieldWidth = 0, int base = 10, const QChar & fillChar = QLatin1Char( ' ' )) const -->
  <!-- QString QString::arg(QChar a, int fieldWidth = 0, const QChar & fillChar = QLatin1Char( ' ' )) const -->
  <!-- QString QString::arg(char a, int fieldWidth = 0, const QChar & fillChar = QLatin1Char( ' ' )) const -->
  <!-- QString QString::arg(double a, int fieldWidth = 0, char format = 'g', int precision = -1, const QChar & fillChar = QLatin1Char( ' ' )) const -->
  <function name="QString::arg">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <!-- QString QString::asprintf(const char * cformat, ...); -->
  <function name="QString::asprintf">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <leak-ignore/>
    <formatstr/>
    <arg nr="1" direction="in">
      <formatstr/>
      <not-uninit/>
    </arg>
  </function>
  <!-- const QChar QString::at(int position) const -->
  <function name="QString::at">
    <noreturn>false</noreturn>
    <returnValue type="const QChar"/>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- const T &QList::at(int i) const -->
  <function name="QList::at">
    <noreturn>false</noreturn>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- QStringList::at is inherited from QList -->
  <function name="QStringList::at">
    <noreturn>false</noreturn>
    <returnValue type="const QString &amp;"/>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- int QString::capacity() const -->
  <function name="QString::capacity">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QStandardItem *QStandardItem::child(int row, int column = 0) const -->
  <function name="QStandardItem::child">
    <noreturn>false</noreturn>
    <returnValue type="QStandardItem *"/>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QString::chop(int n) -->
  <function name="QString::chop">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QString QString::chopped(int len) const -->
  <function name="QString::chopped">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- int QString::compare(const QString &s1, const QString &s2, Qt::CaseSensitivity cs = Qt::CaseSensitive) //static -->
  <!-- int QString::compare(QLatin1String other, Qt::CaseSensitivity cs = Qt::CaseSensitive) const -->
  <!-- int QString::compare(const QString &other, Qt::CaseSensitivity cs = Qt::CaseSensitive) const -->
  <!-- int QString::compare(const QString &s1, QLatin1String s2, Qt::CaseSensitivity cs = Qt::CaseSensitive) //static -->
  <!-- int QString::compare(QLatin1String s1, const QString &s2, Qt::CaseSensitivity cs = Qt::CaseSensitive) //static -->
  <!-- int QString::compare(const QStringRef &ref, Qt::CaseSensitivity cs = Qt::CaseSensitive) const -->
  <!-- int QString::compare(const QString &s1, const QStringRef &s2, Qt::CaseSensitivity cs = Qt::CaseSensitive) //static -->
  <function name="QString::compare">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <const/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" default="0" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool QList::contains(const T &value) const -->
  <!-- bool QList::startsWith(const T &value) const -->
  <!-- bool QList::endsWith(const T &value) const -->
  <function name="QList::contains,QList::startsWith,QList::endsWith">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="bool"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- template <typename AT> qsizetype QSignalSpy::count(const AT &value) const -->
  <!-- template <typename AT> qsizetype QSignalSpy::count() const -->
  <!-- template <typename AT> qsizetype QSignalSpy::size() const -->
  <function name="QSignalSpy::count,QSignalSpy::size">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="qsize"/>
    <const/>
    <arg nr="1" direction="in" default="">
      <not-uninit/>
    </arg>
  </function>
  <!-- QIcon QIcon::fromTheme(const QString &name)-->
  <function name="QIcon::fromTheme">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="QIcon"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- ##### QMap https://doc.qt.io/qt-5/qmap.html ##### -->
  <!-- QMap::const_iterator QMap::constFind(const Key &key) const -->
  <function name="QMap::constFind">
    <noreturn>false</noreturn>
    <returnValue type="QMap::const_iterator"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool QMap::contains(const Key &key) const -->
  <function name="QMap::contains">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="bool"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QMap::iterator QMap::erase(QMap::iterator pos) -->
  <function name="QMap::erase">
    <noreturn>false</noreturn>
    <returnValue type="QMap::iterator"/>
    <arg nr="1">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QMap::iterator QMap::find(const Key &key) -->
  <!-- QMap::const_iterator QMap::find(const Key &key) const -->
  <function name="QMap::find">
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QMap::iterator QMap::insert(const Key &key, const T &value) -->
  <!-- QMap::iterator QMap::insert(QMap::const_iterator pos, const Key &key, const T &value) -->
  <!-- QMap::iterator QMap::insertMulti(const Key &key, const T &value) -->
  <!-- QMap::iterator QMap::insertMulti(QMap::const_iterator pos, const Key &key, const T &value) -->
  <function name="QMap::insert,QMap::insertMulti">
    <noreturn>false</noreturn>
    <returnValue type="QMap::iterator"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in" default="">
      <not-uninit/>
    </arg>
  </function>
  <!-- const Key QMap::key(const T &value, const Key &defaultKey = Key()) const -->
  <function name="QMap::key">
    <noreturn>false</noreturn>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="">
      <not-uninit/>
    </arg>
  </function>
  <!-- int QMap::remove(const Key &key) -->
  <function name="QMap::remove">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int QMap::size() const -->
  <function name="QMap::size">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
  </function>
  <!-- T QMap::take(const Key &key) -->
  <function name="QMap::take">
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- const T QMap::value(const Key &key, const T &defaultValue = T()) const -->
  <function name="QMap::value">
    <noreturn>false</noreturn>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="">
      <not-uninit/>
    </arg>
  </function>
  <!-- QList<T> QMap::values() const -->
  <!-- QList<T> QMap::values(const Key &key) const -->
  <function name="QMap::values">
    <noreturn>false</noreturn>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in" default="">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool QString::contains(const QString &str, Qt::CaseSensitivity cs = Qt::CaseSensitive) const -->
  <!-- bool QString::startsWith(const QString &str, Qt::CaseSensitivity cs = Qt::CaseSensitive) const -->
  <!-- bool QString::endsWith(const QString &str, Qt::CaseSensitivity cs = Qt::CaseSensitive) const -->
  <function name="QString::contains,QString::startsWith,QString::endsWith">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="bool"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="Qt::CaseSensitive" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool QStringList::contains(const T &value) const -->
  <!-- bool QStringList::startsWith(const T &value) const -->
  <!-- bool QStringList::endsWith(const T &value) const -->
  <function name="QStringList::contains,QStringList::startsWith,QStringList::endsWith">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="bool"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QList::insert(int i, const T &value) -->
  <!-- iterator QList::insert(iterator before, const T &value) -->
  <!-- QStringList inherits from QList -->
  <function name="QList::insert,QStringList::insert">
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
  </function>
  <!-- QStandardItem *QStandardItemModel::item(int row, int column = 0) const -->
  <function name="QStandardItemModel::item">
    <noreturn>false</noreturn>
    <returnValue type="QStandardItem *"/>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QStandardItem *QStandardItemModel::itemFromIndex(const QModelIndex &index) const -->
  <function name="QStandardItemModel::itemFromIndex">
    <noreturn>false</noreturn>
    <returnValue type="QStandardItem *"/>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QString &QString::fill(QChar ch, int size = -1) -->
  <function name="QString::fill">
    <noreturn>false</noreturn>
    <returnValue type="QString &amp;"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="-1">
      <not-uninit/>
    </arg>
  </function>
  <!-- QString QString::fromAscii(const char *str, int size = -1) // static -->
  <!-- QString QString::fromAscii(const QByteArray &str) // static -->
  <function name="QString::fromAscii">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="QString"/>
    <leak-ignore/>
    <const/>
    <warn severity="style" alternatives="QString::fromLatin1" reason="Obsolete"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="-1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QString QString::fromLatin1(const char *str, int size = -1) //static -->
  <!-- QString QString::fromLatin1(const QByteArray &str) //static -->
  <function name="QString::fromLatin1">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="QString"/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="-1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QString QString::fromLocal8Bit(const char * str, int size = -1) // static -->
  <!-- QString QString::fromLocal8Bit(const QByteArray &str) // static -->
  <function name="QString::fromLocal8Bit">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="QString"/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" default="-1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QString QString::fromNSString(const NSString *string) // static -->
  <function name="QString::fromNSString">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- QString QString::fromRawData(const QChar *unicode, int size) // static -->
  <function name="QString::fromRawData">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QString QString::fromStdString(const std::string &str) //static -->
  <function name="QString::fromStdString">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="QString"/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- QString QString::fromStdU16String(const std::u16string &str) // static -->
  <!-- QString QString::fromStdU32String(const std::u32string &str) // static -->
  <!-- QString QString::fromStdWString(const std::wstring &str) // static -->
  <function name="QString::fromStdU16String,QString::fromStdU32String,QString::fromStdWString">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QString QString::fromUtf8(const char *str, int size = -1) //static -->
  <!-- QString QString::fromUtf8(const QByteArray &str) //static -->
  <function name="QString::fromUtf8">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="QString"/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="-1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QString QString::fromUtf16(const ushort *unicode, int size = -1) // static -->
  <!-- QString QString::fromUtf16(const char16_t *str, int size = -1) // static -->
  <function name="QString::fromUtf16">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="QString"/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="-1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QString QString::fromWCharArray(const wchar_t *string, int size = -1) // static -->
  <function name="QString::fromWCharArray">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="-1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QByteArray QString::toAscii() const -->
  <function name="QString::toAscii">
    <noreturn>false</noreturn>
    <returnValue type="QByteArray"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <warn severity="style" alternatives="QString::toLatin1" reason="Obsolete"/>
  </function>
  <!-- QByteArray QString::toLatin1() const -->
  <!-- QByteArray QString::toLocal8Bit() const -->
  <function name="QString::toLatin1,QString::toLocal8Bit">
    <noreturn>false</noreturn>
    <returnValue type="QByteArray"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
  </function>
  <!-- int QList::indexOf(const T &) const -->
  <!-- int QStringList::indexOf(const QString &) const -->
  <function name="QList::indexOf,QStringList::indexOf">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="signed int"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int QString::find(QChar c, int index = 0, bool cs = TRUE) const -->
  <!-- int QString::find(char c, int index = 0, bool cs = TRUE) const -->
  <!-- int QString::find(const QString & str, int index = 0, bool cs = TRUE) const -->
  <!-- int QString::find(const QRegExp & rx, int index = 0) const -->
  <!-- int QString::find(const char * str, int index = 0) const -->
  <function name="QString::find">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <warn severity="style" alternatives="QString::indexOf" reason="Obsolete"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="0">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in" default="">
      <not-uninit/>
    </arg>
  </function>
  <!-- int QString::indexOf(const QString &str, int from = 0, Qt::CaseSensitivity cs = Qt::CaseSensitive) const -->
  <!-- int QString::indexOf(QChar ch, int from = 0, Qt::CaseSensitivity cs = Qt::CaseSensitive) const -->
  <!-- int QString::indexOf(QLatin1String str, int from = 0, Qt::CaseSensitivity cs = Qt::CaseSensitive) const -->
  <!-- int QString::indexOf(const QStringRef &str, int from = 0, Qt::CaseSensitivity cs = Qt::CaseSensitive) const -->
  <!-- int QString::indexOf(const QRegExp &rx, int from = 0) const -->
  <!-- int QString::indexOf(QRegExp &rx, int from = 0) const -->
  <!-- int QString::indexOf(const QRegularExpression &re, int from = 0) const -->
  <!-- int QString::indexOf(const QRegularExpression &re, int from, QRegularExpressionMatch *rmatch) const -->
  <function name="QString::indexOf">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="signed int"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <!-- QString & QString::insert(int position, const QString & str) -->
  <!-- QString & QString::insert(int position, const QLatin1String & str) -->
  <!-- QString & QString::insert(int position, const QChar * unicode, int size) -->
  <!-- QString & QString::insert(int position, QChar ch) -->
  <function name="QString::insert">
    <noreturn>false</noreturn>
    <returnValue type="QString &amp;"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int QList::lastIndexOf(const T &value, int from = -1) const -->
  <!-- int QStringList::lastIndexOf(const QString &value, int from = -1) const -->
  <function name="QList::lastIndexOf,QStringList::lastIndexOf">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="int"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="-1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int QString::lastIndexOf(const QString &str, int from = -1, Qt::CaseSensitivity cs = Qt::CaseSensitive) const -->
  <function name="QString::lastIndexOf">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="signed int"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="-1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" default="Qt::CaseSensitive" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QString QString::left(int n) const -->
  <function name="QString::left">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- qsizetype QString::length() const -->
  <function name="QString::length">
    <noreturn>false</noreturn>
    <returnValue type="qsizetype"/>
    <use-retval/>
    <const/>
  </function>
  <!-- int QImage::width() const -->
  <!-- int QImage::height() const -->
  <!-- int QImage::dotsPerMeterX() const -->
  <!-- int QImage::dotsPerMeterY() const -->
  <function name="QImage::width,QImage::height,QImage::dotsPerMeterX,QImage::dotsPerMeterY">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QSize QImage::size() const -->
  <function name="QImage::size">
    <noreturn>false</noreturn>
    <returnValue type="QSize"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QString QString::leftJustified(int width, QChar fill = QLatin1Char(' '), bool truncate = false) const -->
  <function name="QString::leftJustified">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="QLatin1Char(' ')">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in" default="false">
      <not-uninit/>
    </arg>
  </function>
  <!-- QStringRef QString::leftRef(int n) const -->
  <function name="QString::leftRef">
    <noreturn>false</noreturn>
    <returnValue type="QStringRef"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int QString::localeAwareCompare(const QString &other) const -->
  <!-- int QString::localeAwareCompare(const QStringRef &other) const -->
  <!-- int QString::localeAwareCompare(const QString &s1, const QStringRef &s2) // static -->
  <function name="QString::localeAwareCompare">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in" default="">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!--  QList<T> QList::mid(int pos, int length = -1) const -->
  <function name="QList::mid">
    <noreturn>false</noreturn>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="-1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!--  QString QString::mid(int position, int n = -1) const -->
  <function name="QString::mid">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="-1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!--  QStringList QStringList::mid(int pos, int length = -1) const -->
  <function name="QStringList::mid">
    <noreturn>false</noreturn>
    <returnValue type="QStringList"/>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="-1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QStringRef QString::midRef(int position, int n = -1) const -->
  <function name="QString::midRef">
    <noreturn>false</noreturn>
    <returnValue type="QStringRef"/>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" default="-1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QString QString::normalized(QString::NormalizationForm mode, QChar::UnicodeVersion version = QChar::Unicode_Unassigned) const -->
  <function name="QString::normalized">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="QChar::Unicode_Unassigned">
      <not-uninit/>
    </arg>
  </function>
  <!-- QString QString::number(long n, int base = 10) -->
  <!-- QString QString::number(uint n, int base = 10) -->
  <!-- QString QString::number(int n, int base = 10) -->
  <!-- QString QString::number(ulong n, int base = 10) -->
  <!-- QString QString::number(qlonglong n, int base = 10) -->
  <!-- QString QString::number(qulonglong n, int base = 10) -->
  <!-- QString QString::number(double n, char format = 'g', int precision = 6) -->
  <function name="QString::number">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in"/>
    <arg nr="any" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QList::prepend(const T &value) -->
  <!-- QStringList inherits from QList -->
  <function name="QList::prepend,QStringList::prepend">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QString & QString::prepend(const QString & str) -->
  <!-- QString & QString::prepend(const QLatin1String & str) -->
  <!-- QString & QString::prepend(const QByteArray & ba) -->
  <!-- QString & QString::prepend(const char * str) -->
  <!-- QString & QString::prepend(QChar ch) -->
  <function name="QString::prepend">
    <noreturn>false</noreturn>
    <returnValue type="QString &amp;"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QList::push_back(const T &value) -->
  <!-- QStringList inherits from QList -->
  <function name="QList::push_back,QStringList::push_back">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QString::push_back(const QString &other) -->
  <!-- void QString::push_back(QChar ch) -->
  <function name="QString::push_back">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QList::push_front(const T &value) -->
  <!-- QStringList inherits from QList -->
  <function name="QList::push_front,QStringList::push_front">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QString::push_front(const QString &other) -->
  <!-- void QString::push_front(QChar ch) -->
  <function name="QString::push_front">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QtMessageHandler qInstallMessageHandler(QtMessageHandler handler) -->
  <function name="qInstallMessageHandler">
    <noreturn>false</noreturn>
    <returnValue type="QtMessageHandler"/>
    <arg nr="1">
      <not-bool/>
    </arg>
  </function>
  <!-- QtMsgHandler qInstallMsgHandler(QtMsgHandler handler) -->
  <function name="qInstallMsgHandler">
    <noreturn>false</noreturn>
    <returnValue type="QtMsgHandler"/>
    <arg nr="1">
      <not-bool/>
    </arg>
    <warn severity="style" alternatives="qInstallMessageHandler" reason="Obsolete"/>
  </function>
  <!-- int qrand() -->
  <function name="qrand">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="int"/>
    <warn severity="style" alternatives="QRandomGenerator" reason="Obsolete"/>
  </function>
  <!-- void qsrand(uint seed) -->
  <function name="qsrand">
    <noreturn>false</noreturn>
    <arg nr="1" direction="in"/>
    <warn severity="style" alternatives="QRandomGenerator" reason="Obsolete"/>
  </function>
  <!-- QString & QString::remove(int position, int n) -->
  <!-- QString & QString::remove(QChar ch, Qt::CaseSensitivity cs = Qt::CaseSensitive) -->
  <!-- QString & QString::remove(const QString & str, Qt::CaseSensitivity cs = Qt::CaseSensitive) -->
  <!-- QString & QString::remove(const QRegExp & rx) -->
  <function name="QString::remove">
    <noreturn>false</noreturn>
    <returnValue type="QString &amp;"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int QList::removeAll(const T &value) -->
  <!-- QStringList inherits from QList -->
  <function name="QList::removeAll,QStringList::removeAll">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QList::removeAt(int i) -->
  <!-- QStringList inherits from QList -->
  <function name="QList::removeAt,QStringList::removeAt">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- void QStandardItem::removeRow(int row) -->
  <function name="QStandardItem::removeRow">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QStandardItem::removeRows(int row, int count) -->
  <function name="QStandardItem::removeRows">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool QAbstractItemModel::removeRow(int row, const QModelIndex &parent = QModelIndex()) -->
  <!-- QStandardItemModel inherits from QAbstractItemModel -->
  <function name="QAbstractItemModel::removeRow,QStandardItemModel::removeRow">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool QStandardItemModel::removeRows(int row, int count, const QModelIndex &parent = QModelIndex()) -->
  <function name="QStandardItemModel::removeRows">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QString QString::repeated(int times) const -->
  <function name="QString::repeated">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QString & QString::replace(int position, int n, const QString & after) -->
  <!-- QString & QString::replace(int position, int n, const QChar * unicode, int size) -->
  <!-- QString & QString::replace(int position, int n, QChar after) -->
  <!-- QString & QString::replace(const QString & before, const QString & after, Qt::CaseSensitivity cs = Qt::CaseSensitive) -->
  <!-- QString & QString::replace(const QChar * before, int blen, const QChar * after, int alen, Qt::CaseSensitivity cs = Qt::CaseSensitive) -->
  <!-- QString & QString::replace(QChar ch, const QString & after, Qt::CaseSensitivity cs = Qt::CaseSensitive) -->
  <!-- QString & QString::replace(QChar before, QChar after, Qt::CaseSensitivity cs = Qt::CaseSensitive) -->
  <!-- QString & QString::replace(const QLatin1String & before, const QLatin1String & after, Qt::CaseSensitivity cs = Qt::CaseSensitive) -->
  <!-- QString & QString::replace(const QLatin1String & before, const QString & after, Qt::CaseSensitivity cs = Qt::CaseSensitive) -->
  <!-- QString & QString::replace(const QString & before, const QLatin1String & after, Qt::CaseSensitivity cs = Qt::CaseSensitive) -->
  <!-- QString & QString::replace(QChar c, const QLatin1String & after, Qt::CaseSensitivity cs = Qt::CaseSensitive) -->
  <!-- QString & QString::replace(const QRegExp & rx, const QString & after) -->
  <function name="QString::replace">
    <noreturn>false</noreturn>
    <returnValue type="QString &amp;"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" default="0" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" default="0" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="5" default="0" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QString::reserve(int size) -->
  <function name="QString::reserve">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QString::resize(int size) -->
  <!-- void QString::resize(int size, QChar fillChar) -->
  <function name="QString::resize">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="">
      <not-uninit/>
    </arg>
  </function>
  <!-- QString QString::right(int n) const -->
  <function name="QString::right">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- QString QString::rightJustified(int width, QChar fill = QLatin1Char(' '), bool truncate = false) const -->
  <function name="QString::rightJustified">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="QLatin1Char(' ')">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in" default="false">
      <not-uninit/>
    </arg>
  </function>
  <!-- QStringRef QString::rightRef(int n) const -->
  <function name="QString::rightRef">
    <noreturn>false</noreturn>
    <returnValue type="QStringRef"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QString QString::section(QChar sep, int start, int end = -1, SectionFlags flags = SectionDefault) const -->
  <!-- QString QString::section(const QString & sep, int start, int end = -1, SectionFlags flags = SectionDefault) const -->
  <!-- QString QString::section(const QRegExp & reg, int start, int end = -1, SectionFlags flags = SectionDefault) const -->
  <!-- QString QString::section(const QRegularExpression &re, int start, int end = -1, QString::SectionFlags flags = SectionDefault) const -->
  <function name="QString::section">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" default="-1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" default="SectionDefault" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QString & QString::setNum(int n, int base = 10) -->
  <!-- QString & QString::setNum(uint n, int base = 10) -->
  <!-- QString & QString::setNum(long n, int base = 10) -->
  <!-- QString & QString::setNum(ulong n, int base = 10) -->
  <!-- QString & QString::setNum(qlonglong n, int base = 10) -->
  <!-- QString & QString::setNum(qulonglong n, int base = 10) -->
  <!-- QString & QString::setNum(short n, int base = 10) -->
  <!-- QString & QString::setNum(ushort n, int base = 10) -->
  <!-- QString & QString::setNum(double n, char format = 'g', int precision = 6) -->
  <!-- QString & QString::setNum(float n, char format = 'g', int precision = 6) -->
  <function name="QString::setNum">
    <noreturn>false</noreturn>
    <returnValue type="QString &amp;"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" default="6" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QString &QString::setRawData(const QChar *unicode, int size) -->
  <!-- QString &QString::setUnicode(const QChar *unicode, int size) -->
  <!-- QString &QString::setUtf16(const ushort *unicode, int size) -->
  <function name="QString::setRawData,QString::setUnicode,QString::setUtf16">
    <noreturn>false</noreturn>
    <returnValue type="QString &amp;"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QString::shrink_to_fit() -->
  <!-- void QString::squeeze() -->
  <function name="QString::shrink_to_fit,QString::squeeze">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
  </function>
  <!-- QString QString::simplified() const -->
  <function name="QString::simplified">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QStringList QString::split(const QString &sep, SplitBehavior behavior = KeepEmptyParts, Qt::CaseSensitivity cs = Qt::CaseSensitive) const -->
  <!-- QStringList QString::split(QChar sep, SplitBehavior behavior = KeepEmptyParts, Qt::CaseSensitivity cs = Qt::CaseSensitive) const -->
  <!-- QStringList QString::split(const QRegExp &rx, SplitBehavior behavior = KeepEmptyParts) const -->
  <!-- QStringList QString::split(const QRegularExpression &re, SplitBehavior behavior = KeepEmptyParts) const -->
  <function name="QString::split">
    <noreturn>false</noreturn>
    <returnValue type="QStringList"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="QString::KeepEmptyParts" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" default="Qt::CaseSensitive" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QList::swap(QList<T> &other) -->
  <!-- void QList::swap(int i, int j) -->
  <!-- QStringList inherits from QList -->
  <function name="QList::swap,QStringList::swap">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QString::swap(QString &other) -->
  <function name="QString::swap">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1"/>
  </function>
  <!-- CFStringRef QString::toCFString() const -->
  <function name="QString::toCFString">
    <noreturn>false</noreturn>
    <returnValue type="CFStringRef"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QString QString::toCaseFolded() const -->
  <function name="QString::toCaseFolded">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <const/>
  </function>
  <!-- double QString::toDouble(bool * ok = 0) const -->
  <function name="QString::toDouble">
    <noreturn>false</noreturn>
    <returnValue type="double"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" default="0" direction="out">
    </arg>
  </function>
  <!-- float QString::toFloat(bool * ok = 0) const -->
  <function name="QString::toFloat">
    <noreturn>false</noreturn>
    <returnValue type="float"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" default="0" direction="out">
    </arg>
  </function>
  <!-- QString QString::toHtmlEscaped() const -->
  <function name="QString::toHtmlEscaped">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <const/>
  </function>
  <!-- int QString::toInt(bool *ok = Q_NULLPTR, int base = 10) const -->
  <function name="QString::toInt">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" default="0" direction="out">
    </arg>
    <arg nr="2" default="10" direction="in">
      <not-uninit/>
      <valid>0,2:36</valid>
      <not-bool/>
    </arg>
  </function>
  <!-- long QString::toLong(bool * ok = 0, int base = 10) const -->
  <function name="QString::toLong">
    <noreturn>false</noreturn>
    <returnValue type="long"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" default="0" direction="out">
    </arg>
    <arg nr="2" default="10" direction="in">
      <not-uninit/>
      <valid>0,2:36</valid>
      <not-bool/>
    </arg>
  </function>
  <!-- qlonglong QString::toLongLong(bool * ok = 0, int base = 10) const -->
  <function name="QString::toLongLong">
    <noreturn>false</noreturn>
    <returnValue type="qlonglong"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" default="0" direction="out">
      <not-uninit/>
    </arg>
    <arg nr="2" default="10" direction="in">
      <not-uninit/>
      <valid>0,2:36</valid>
      <not-bool/>
    </arg>
  </function>
  <!-- QString QString::toLower() const -->
  <function name="QString::toLower">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <const/>
  </function>
  <!-- short QString::toShort(bool * ok = 0, int base = 10) const -->
  <function name="QString::toShort">
    <noreturn>false</noreturn>
    <returnValue type="short"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" default="0" direction="out">
    </arg>
    <arg nr="2" default="10" direction="in">
      <not-uninit/>
      <valid>0,2:36</valid>
      <not-bool/>
    </arg>
  </function>
  <!-- std::string QString::toStdString() const -->
  <function name="QString::toStdString">
    <noreturn>false</noreturn>
    <returnValue type="std::string"/>
    <use-retval/>
    <const/>
  </function>
  <!-- std::u16string QString::toStdU16String() const -->
  <function name="QString::toStdU16String">
    <noreturn>false</noreturn>
    <returnValue type="std::u16string"/>
    <use-retval/>
    <const/>
  </function>
  <!-- std::u32string QString::toStdU32String() const -->
  <function name="QString::toStdU32String">
    <noreturn>false</noreturn>
    <returnValue type="std::u32string"/>
    <use-retval/>
    <const/>
  </function>
  <!-- std::wstring QString::toStdWString() const -->
  <function name="QString::toStdWString">
    <noreturn>false</noreturn>
    <returnValue type="std::wstring"/>
    <use-retval/>
    <const/>
  </function>
  <!-- uint QString::toUInt(bool * ok = 0, int base = 10) const -->
  <function name="QString::toUInt">
    <noreturn>false</noreturn>
    <returnValue type="uint"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" default="0" direction="out">
    </arg>
    <arg nr="2" default="10" direction="in">
      <not-uninit/>
      <valid>0,2:36</valid>
      <not-bool/>
    </arg>
  </function>
  <!-- ulong QString::toULong(bool * ok = 0, int base = 10) const -->
  <function name="QString::toULong">
    <noreturn>false</noreturn>
    <returnValue type="ulong"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" default="0" direction="out">
    </arg>
    <arg nr="2" default="10" direction="in">
      <not-uninit/>
      <valid>0,2:36</valid>
      <not-bool/>
    </arg>
  </function>
  <!-- qulonglong QString::toULongLong(bool * ok = 0, int base = 10) const -->
  <function name="QString::toULongLong">
    <noreturn>false</noreturn>
    <returnValue type="qulonglong"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" default="0" direction="out">
    </arg>
    <arg nr="2" default="10" direction="in">
      <not-uninit/>
      <valid>0,2:36</valid>
      <not-bool/>
    </arg>
  </function>
  <!-- ushort QString::toUShort(bool * ok = 0, int base = 10) const -->
  <function name="QString::toUShort">
    <noreturn>false</noreturn>
    <returnValue type="ushort"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" default="0" direction="out">
    </arg>
    <arg nr="2" default="10" direction="in">
      <not-uninit/>
      <valid>0,2:36</valid>
      <not-bool/>
    </arg>
  </function>
  <!-- QVector<uint> QString::toUcs4() const -->
  <function name="QString::toUcs4">
    <noreturn>false</noreturn>
    <!--<returnValue type="QVector&lt;uint&gt;"/> Currently not allowed by Relax-NG validation file -->
    <use-retval/>
    <const/>
  </function>
  <!-- QString QString::toUpper() const -->
  <function name="QString::toUpper">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QByteArray QString::toUtf8() const -->
  <function name="QString::toUtf8">
    <noreturn>false</noreturn>
    <returnValue type="QByteArray"/>
    <use-retval/>
    <const/>
  </function>
  <!-- int QString::toWCharArray(wchar_t *array) const -->
  <function name="QString::toWCharArray">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <const/>
    <arg nr="1" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- QString QString::trimmed() const -->
  <function name="QString::trimmed">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <const/>
  </function>
  <!-- void QString::truncate(int position) -->
  <function name="QString::truncate">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- const QChar *QString::unicode() const -->
  <function name="QString::unicode">
    <noreturn>false</noreturn>
    <returnValue type="const QChar *"/>
    <use-retval/>
    <const/>
  </function>
  <!-- const ushort *QString::utf16() const -->
  <function name="QString::utf16">
    <noreturn>false</noreturn>
    <returnValue type="const ushort *"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QString QString::vasprintf(const char *cformat, va_list ap) // static -->
  <function name="QString::vasprintf">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QString & QString::vsprintf(const char * cformat, va_list ap) -->
  <function name="QString::vsprintf">
    <noreturn>false</noreturn>
    <returnValue type="QString &amp;"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
    <warn severity="style" alternatives="QString::vasprintf,QString::arg,QTextStream" reason="Obsolete"/>
  </function>
  <!-- QString QStringList::join(const QString &separator) const -->
  <!-- QString QStringList::join(QLatin1String separator) const -->
  <!-- QString QStringList::join(QChar separator) const -->
  <function name="QStringList::join">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="QString"/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QMetaObject::connectSlotsByName(QObject *object) -->
  <function name="QMetaObject::connectSlotsByName">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
    </arg>
  </function>
  <!-- https://doc.qt.io/qt-5/qdir.html -->
  <!-- QString QDir::fromNativeSeparators(const QString &pathName) -->
  <!-- QString QDir::toNativeSeparators(const QString &pathName) -->
  <function name="QDir::fromNativeSeparators,QDir::toNativeSeparators">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QString absolutePath() const -->
  <!-- QString canonicalPath() const -->
  <!-- QString dirName() const -->
  <function name="QDir::absoluteFilePath,QDir::canonicalPath,QDir::dirName">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool QDir::cd(const QString &dirName) -->
  <function name="QDir::cd">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval type="error-code"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool QDir::cdUp() -->
  <function name="QDir::cdUp">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval type="error-code"/>
  </function>
  <!-- bool QDir::exists(QString &name) const -->
  <!-- bool QDir::exists() const -->
  <function name="QDir">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" default="&quot;&quot;" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- uint QDir::count() const -->
  <function name="QDir::count">
    <noreturn>false</noreturn>
    <returnValue type="uint"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool QFile::exists(const QString &fileName) // static -->
  <!-- bool QFile::exists() const -->
  <function name="QFile::exists">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" default="&quot;&quot;" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool QFile::open(OpenMode mode) -->
  <!-- bool QFile::open(FILE *fh, OpenMode mode, FileHandleFlags handleFlags = DontCloseHandle) -->
  <!-- bool QFile::open(int fd, OpenMode mode, FileHandleFlags handleFlags = DontCloseHandle) -->
  <function name="QFile::open">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <leak-ignore/>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool QFile::remove() -->
  <!-- bool QFile::remove(const QString &fileName) // static -->
  <function name="QFile::remove">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <leak-ignore/>
    <arg nr="1" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool QFile::rename(const QString &newName) -->
  <!-- bool QFile::rename(const QString &oldName, const QString &newName) // static -->
  <function name="QFile::rename">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QFile::setFileName(const QString &name) -->
  <function name="QFile::setFileName">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- qint64 QFile::size() const -->
  <!-- qint64 QIODevice::size() const -->
  <function name="QFile::size,QIODevice::size">
    <noreturn>false</noreturn>
    <returnValue type="qint64"/>
    <use-retval/>
    <const/>
  </function>
  <!-- https://doc.qt.io/qt-5/qlockfile.html -->
  <!-- bool QLockFile::lock() -->
  <!-- bool QLockFile::removeStaleLockFile() -->
  <function name="QLockFile::lock,QLockFile::removeStaleLockFile">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval type="error-code"/>
  </function>
  <!-- bool QLockFile::isLocked() const -->
  <function name="QLockFile::isLocked">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- int QLockFile::staleLockTime() const -->
  <function name="QLockFile::staleLockTime">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool QLockFile::tryLock(int timeout = 0) -->
  <function name="QLockFile::tryLock">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <arg nr="1" default="0" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QString QFileDialog::getOpenFileName(QWidget *parent = Q_NULLPTR, const QString &caption = QString(), const QString &dir = QString(), const QString &filter = QString(), QString *selectedFilter = Q_NULLPTR, Options options = Options()) //static -->
  <!-- QString QFileDialog::getSaveFileName(QWidget *parent = Q_NULLPTR, const QString &caption = QString(), const QString &dir = QString(), const QString &filter = QString(), QString *selectedFilter = Q_NULLPTR, Options options = Options()) //static -->
  <!-- QString QFileDialog::getExistingDirectory(QWidget *parent = Q_NULLPTR, const QString &caption = QString(), const QString &dir = QString(), Options options = ShowDirsOnly) //static -->
  <function name="QFileDialog::getOpenFileName,QFileDialog::getSaveFileName,QFileDialog::getExistingDirectory">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <!-- QStringList QFileDialog::getOpenFileNames(QWidget *parent = Q_NULLPTR, const QString &caption = QString(), const QString &dir = QString(), const QString &filter = QString(), QString *selectedFilter = Q_NULLPTR, Options options = Options()) //static -->
  <function name="QFileDialog::getOpenFileNames">
    <noreturn>false</noreturn>
    <returnValue type="QStringList"/>
    <use-retval/>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <!-- qFatal(const char *message, ...) -->
  <function name="qFatal">
    <noreturn>true</noreturn>
    <leak-ignore/>
    <formatstr/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <formatstr/>
      <strz/>
    </arg>
  </function>
  <!-- QString QCoreApplication::translate(const char *context, const char *sourceText, const char *disambiguation = nullptr, int n = -1) //static -->
  <!-- QApplication inherits from QCoreApplication -->
  <function name="QCoreApplication::translate,QApplication::translate">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
      <not-bool/>
    </arg>
    <arg nr="3" default="NULL" direction="in">
      <not-bool/>
    </arg>
    <arg nr="4" default="-1" direction="in">
    </arg>
  </function>
  <!-- https://doc.qt.io/qt-5/qtimer.html -->
  <!-- int QTimer::interval() const -->
  <function name="QTimer::interval">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!-- std::chrono::milliseconds QTimer::intervalAsDuration() const -->
  <function name="QTimer::intervalAsDuration">
    <noreturn>false</noreturn>
    <returnValue type="std::chrono::milliseconds"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool QTimer::isActive() const -->
  <function name="QTimer::isActive">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool QTimer::isSingleShot() const -->
  <function name="QTimer::isSingleShot">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- int QTimer::remainingTime() const -->
  <function name="QTimer::remainingTime">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!-- std::chrono::milliseconds QTimer::remainingTimeAsDuration() const -->
  <function name="QTimer::remainingTimeAsDuration">
    <noreturn>false</noreturn>
    <returnValue type="std::chrono::milliseconds"/>
    <use-retval/>
    <const/>
  </function>
  <!-- void QTimer::setInterval(int msec) -->
  <!-- void QTimer::setInterval(std::chrono::milliseconds value) -->
  <function name="QTimer::setInterval">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QTimer::setSingleShot(bool singleShot) -->
  <function name="QTimer::setSingleShot">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QTimer::start(std::chrono::milliseconds msec) -->
  <function name="QTimer::start">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QTimer::singleShot(int msec, const QObject *receiver, const char *member) // static -->
  <!-- void QTimer::singleShot(int msec, Qt::TimerType timerType, const QObject *receiver, const char *member) // static -->
  <!-- void QTimer::singleShot(int msec, const QObject *receiver, PointerToMemberFunction method) // static -->
  <!-- void QTimer::singleShot(int msec, Qt::TimerType timerType, const QObject *receiver, PointerToMemberFunction method) // static -->
  <!-- void QTimer::singleShot(int msec, Functor functor) // static -->
  <!-- void QTimer::singleShot(int msec, Qt::TimerType timerType, Functor functor) // static -->
  <!-- void QTimer::singleShot(int msec, const QObject *context, Functor functor) // static -->
  <!-- void QTimer::singleShot(int msec, Qt::TimerType timerType, const QObject *context, Functor functor) // static -->
  <!-- void QTimer::singleShot(std::chrono::milliseconds msec, const QObject *receiver, const char *member) // static -->
  <!-- void QTimer::singleShot(std::chrono::milliseconds msec, Qt::TimerType timerType, const QObject *receiver, const char *member) // static -->
  <function name="QTimer::singleShot">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QBoxLayout::addWidget(QWidget *widget, int stretch = 0, Qt::Alignment alignment = Qt::Alignment()) -->
  <!-- QVBoxLayout and  QHBoxLayout inherit from QBoxLayout -->
  <function name="QBoxLayout::addWidget,QVBoxLayout::addWidget,QHBoxLayout::addWidget">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" default="Qt::Alignment()" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QGridLayout::addWidget(QWidget *widget, int row, int column, Qt::Alignment alignment = Qt::Alignment()) -->
  <!-- void QGridLayout::addWidget(QWidget *widget, int fromRow, int fromColumn, int rowSpan, int columnSpan, Qt::Alignment alignment = Qt::Alignment()) -->
  <function name="QGridLayout::addWidget">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="any" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QDate QDate::addDays(qint64 ndays) const -->
  <!-- QDate QDate::addMonths(int nmonths) const -->
  <!-- QDate QDate::addYears(int nyears) const -->
  <function name="QDate::addDays,QDate::addMonths,QDate::addYears">
    <noreturn>false</noreturn>
    <returnValue type="QDate"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QDate QDate::currentDate() // static -->
  <function name="QDate::currentDate">
    <noreturn>false</noreturn>
    <returnValue type="QDate"/>
    <use-retval/>
    <leak-ignore/>
  </function>
  <!-- int QDate::day() const -->
  <!-- int QDate::dayOfWeek() const -->
  <!-- int QDate::dayOfYear() const -->
  <!-- int QDate::daysInMonth() const -->
  <!-- int QDate::daysInYear() const -->
  <function name="QDate::day,QDate::dayOfWeek,QDate::dayOfYear,QDate::daysInMonth,QDate::daysInYear">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
  </function>
  <!-- qint64 QDate::daysTo(const QDate &d) const -->
  <function name="QDate::daysTo">
    <noreturn>false</noreturn>
    <returnValue type="qint64"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QDate QDate::fromJulianDay(qint64 jd) // static -->
  <function name="QDate::fromJulianDay">
    <noreturn>false</noreturn>
    <returnValue type="QDate"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QDate QDate::fromString(const QString &string, Qt::DateFormat format = Qt::TextDate) // static -->
  <!-- QDate QDate::fromString(const QString &string, const QString &format) // static -->
  <function name="QDate::fromString">
    <noreturn>false</noreturn>
    <returnValue type="QDate"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="Qt::TextDate">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QDate::getDate(int *year, int *month, int *day) const -->
  <function name="QDate::getDate">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="out"/>
    <arg nr="2" direction="out"/>
    <arg nr="3" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- bool QDate::isLeapYear(int year) // static -->
  <function name="QDate::isLeapYear">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool QDate::isNull() const -->
  <function name="QDate::isNull">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool QDate::isValid() const -->
  <!-- bool QDate::isValid(int year, int month, int day) // static -->
  <function name="QDate::isValid">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <arg nr="1" direction="in" default=""/>
    <arg nr="2" direction="in" default=""/>
    <arg nr="3" direction="in" default=""/>
  </function>
  <!-- int QDate::month() const -->
  <function name="QDate::month">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool QDate::setDate(int year, int month, int day) -->
  <function name="QDate::setDate">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>:-1,1:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>1:12</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>1:31</valid>
    </arg>
  </function>
  <!-- qint64 QDate::toJulianDay() const -->
  <function name="QDate::toJulianDay">
    <noreturn>false</noreturn>
    <returnValue type="qint64"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QString QDate::toString(const QString &format) const -->
  <!-- QString QDate::toString(QStringView format) const -->
  <!-- QString QDate::toString(Qt::DateFormat format = Qt::TextDate) const -->
  <function name="QDate::toString">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in" default="Qt::TextDate">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int QDate::weekNumber(int *yearNumber = nullptr) const -->
  <function name="QDate::weekNumber">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <const/>
    <arg nr="1" direction="out" default="nullptr">
      <not-bool/>
    </arg>
  </function>
  <!-- int QModelIndex::column() const -->
  <!-- int QModelIndex::row() const -->
  <function name="QModelIndex::row,QModelIndex::column">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!-- int QDate::year() const -->
  <function name="QDate::year">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool QPainter::begin(QPaintDevice *device) -->
  <function name="QPainter::begin">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QPaintDevice *QPainter::device() const -->
  <function name="QPainter::device">
    <noreturn>false</noreturn>
    <returnValue type="QPaintDevice *"/>
    <use-retval/>
    <const/>
  </function>
  <!-- const QTransform &QPainter::deviceTransform() const -->
  <function name="QPainter::deviceTransform">
    <noreturn>false</noreturn>
    <returnValue type="const QTransform &amp;"/>
    <use-retval/>
    <const/>
  </function>
  <!--qreal QRectF::bottom () const -->
  <!--qreal QRectF::height () const -->
  <!--qreal QRectF::width () const -->
  <!--qreal QRectF::left () const -->
  <!--qreal QRectF::right () const -->
  <!--qreal QRectF::top () const -->
  <!--qreal QRectF::x () const -->
  <!--qreal QRectF::y () const -->
  <function name="QRectF::bottom,QRectF::height,QRectF::left,QRectF::right,QRectF::top,QRectF::width,QRectF::x,QRectF::y">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="qreal"/>
    <use-retval/>
    <const/>
  </function>
  <!--QSizeF QRectF::size () const -->
  <function name="QRectF::size">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="QSizeF"/>
    <use-retval/>
    <const/>
  </function>
  <!--bool QRectF::isEmpty () const -->
  <!--bool QRectF::isNull () const -->
  <!--bool QRectF::isValid () const -->
  <!--bool QModelIndex::isValid () const -->
  <function name="QRectF::isEmpty,QRectF::isNull,QRectF::isValid,QModelIndex::isValid">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!--QPointF QRectF::topLeft () const -->
  <!--QPointF QRectF::topRight () const -->
  <!--QPointF QRectF::bottomLeft () const -->
  <!--QPointF QRectF::bottomRight () const -->
  <!--QPointF QRectF::center () const -->
  <function name="QRectF::topLeft,QRectF::topRight,QRectF::bottomLeft,QRectF::bottomRight,QRectF::center">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="QPointF"/>
    <use-retval/>
    <const/>
  </function>
  <!--qreal QPointF::x () const -->
  <!--qreal QPointF::y () const -->
  <!--qreal QPointF::manhattanLength () const -->
  <function name="QPointF::x,QPointF::y,QPointF::manhattanLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="qreal"/>
    <use-retval/>
    <const/>
  </function>
  <!--qreal& QPointF::rx () const -->
  <!--qreal& QPointF::ry () const -->
  <function name="QPointF::rx,QPointF::ry">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="qreal &amp;"/>
    <use-retval/>
    <const/>
  </function>
  <!--int QRect::bottom () const -->
  <!--int QRect::height () const -->
  <!--int QRect::width () const -->
  <!--int QRect::left () const -->
  <!--int QRect::right () const -->
  <!--int QRect::top () const -->
  <!--int QRect::x () const -->
  <!--int QRect::y () const -->
  <!--int QSize::width () const -->
  <!--int QSize::height () const -->
  <function name="QRect::bottom,QRect::height,QRect::left,QRect::right,QRect::top,QRect::width,QRect::x,QRect::y,QSize::width,QSize::height">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!--QSize QRect::size () const -->
  <function name="QRect::size">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="QSize"/>
    <use-retval/>
    <const/>
  </function>
  <!--bool QRect::isEmpty () const -->
  <!--bool QRect::isNull () const -->
  <!--bool QRect::isValid () const -->
  <!--bool QSize::isValid () const -->
  <!--bool QSize::isEmpty () const -->
  <!--bool QSize::isNull () const -->
  <function name="QRect::isEmpty,QRect::isNull,QRect::isValid,QSize::isValid,QSize::isEmpty,QSize::isNull">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!--QPoint QRect::topLeft () const -->
  <!--QPoint QRect::topRight () const -->
  <!--QPoint QRect::bottomLeft () const -->
  <!--QPoint QRect::bottomRight () const -->
  <!--QPoint QRect::center () const -->
  <function name="QRect::topLeft,QRect::topRight,QRect::bottomLeft,QRect::bottomRight,QRect::center">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="QPoint"/>
    <use-retval/>
    <const/>
  </function>
  <!--int QPoint::x() const -->
  <!--int QPoint::y() const -->
  <!--int QPoint::manhattanLength() const -->
  <function name="QPoint::x,QPoint::y,QPoint::manhattanLength">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!--bool QPoint::isNull() const -->
  <!--bool QPointF::isNull() const -->
  <function name="QPoint::isNull,QPointF::isNull">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
     <const/>
  </function>
  <!--void QRect::setHeight(int h) -->
  <!--void QRect::setWidth(int w) -->
  <!--void QSize::setHeight(int h) -->
  <!--void QSize::setWidth(int w) -->
  <!--void QPoint::setX(int x) -->
  <!--void QPoint::setY(int y) -->
  <!--void QPointF::setX(qreal x) -->
  <!--void QPointF::setY(qreal y) -->
  <function name="QRect::setHeight,QRect::setWidth,QSize::setHeight,QSize::setWidth,QPoint::setX,QPoint::setY,QPointF::setX,QPointF::setY">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QPainter::drawEllipse(const QRectF &rectangle) -->
  <!-- void QPainter::drawEllipse(const QRect &rectangle) -->
  <!-- void QPainter::drawEllipse(int x, int y, int width, int height) -->
  <!-- void QPainter::drawEllipse(const QPointF &center, qreal rx, qreal ry) -->
  <!-- void QPainter::drawEllipse(const QPoint &center, int rx, int ry) -->
  <function name="QPainter::drawEllipse">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="any" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QPainter::drawImage(const QRectF &target, const QImage &image, const QRectF &source, Qt::ImageConversionFlags flags = Qt::AutoColor) -->
  <!-- void QPainter::drawImage(const QRect &target, const QImage &image, const QRect &source, Qt::ImageConversionFlags flags = Qt::AutoColor) -->
  <!-- void QPainter::drawImage(const QPointF &point, const QImage &image, const QRectF &source, Qt::ImageConversionFlags flags = ...) -->
  <!-- void QPainter::drawImage(const QPoint &point, const QImage &image, const QRect &source, Qt::ImageConversionFlags flags = ...) -->
  <!-- void QPainter::drawImage(const QRectF &rectangle, const QImage &image) -->
  <!-- void QPainter::drawImage(const QRect &rectangle, const QImage &image) -->
  <!-- void QPainter::drawImage(const QPointF &point, const QImage &image) -->
  <!-- void QPainter::drawImage(const QPoint &point, const QImage &image) -->
  <!-- void QPainter::drawImage(int x, int y, const QImage &image, int sx = 0, int sy = 0, int sw = -1, int sh = -1, Qt::ImageConversionFlags flags = Qt::AutoColor) -->
  <function name="QPainter::drawImage">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="any" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QPainter::drawLine(const QLineF &line) -->
  <!-- void QPainter::drawLine(const QLine &line) -->
  <!-- void QPainter::drawLine(int x1, int y1, int x2, int y2) -->
  <!-- void QPainter::drawLine(const QPoint &p1, const QPoint &p2) -->
  <!-- void QPainter::drawLine(const QPointF &p1, const QPointF &p2) -->
  <function name="QPainter::drawLine">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="any" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QPainter::drawPath(const QPainterPath &path) -->
  <function name="QPainter::drawPath">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QPainter::drawPixmap(const QRectF &target, const QPixmap &pixmap, const QRectF &source) -->
  <!-- void QPainter::drawPixmap(const QRect &target, const QPixmap &pixmap, const QRect &source) -->
  <!-- void QPainter::drawPixmap(int x, int y, int w, int h, const QPixmap &pixmap, int sx, int sy, int sw, int sh) -->
  <!-- void QPainter::drawPixmap(int x, int y, const QPixmap &pixmap, int sx, int sy, int sw, int sh) -->
  <!-- void QPainter::drawPixmap(const QPointF &point, const QPixmap &pixmap, const QRectF &source) -->
  <!-- void QPainter::drawPixmap(const QPoint &point, const QPixmap &pixmap, const QRect &source) -->
  <!-- void QPainter::drawPixmap(const QPointF &point, const QPixmap &pixmap) -->
  <!-- void QPainter::drawPixmap(const QPoint &point, const QPixmap &pixmap) -->
  <!-- void QPainter::drawPixmap(int x, int y, const QPixmap &pixmap) -->
  <!-- void QPainter::drawPixmap(const QRect &rectangle, const QPixmap &pixmap) -->
  <!-- void QPainter::drawPixmap(int x, int y, int width, int height, const QPixmap &pixmap) -->
  <function name="QPainter::drawPixmap">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="any" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QPainter::drawPoint(const QPointF &position) -->
  <!-- void QPainter::drawPoint(const QPoint &position) -->
  <!-- void QPainter::drawPoint(int x, int y) -->
  <function name="QPainter::drawPoint">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QPainter::drawRect(const QRectF &rectangle) -->
  <!-- void QPainter::drawRect(int x, int y, int width, int height) -->
  <!-- void QPainter::drawRect(const QRect &rectangle) -->
  <function name="QPainter::drawRect">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="any" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QPainter::drawText(const QRectF &rectangle, int flags, const QString &text, QRectF *boundingRect = nullptr) -->
  <!-- void QPainter::drawText(const QPointF &position, const QString &text) -->
  <!-- void QPainter::drawText(const QPoint &position, const QString &text) -->
  <!-- void QPainter::drawText(int x, int y, const QString &text) -->
  <!-- void QPainter::drawText(const QRect &rectangle, int flags, const QString &text, QRect *boundingRect = nullptr) -->
  <!-- void QPainter::drawText(int x, int y, int width, int height, int flags, const QString &text, QRect *boundingRect = nullptr) -->
  <!-- void QPainter::drawText(const QRectF &rectangle, const QString &text, const QTextOption &option = QTextOption()) -->
  <function name="QPainter::drawText">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="any" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QPainter::fillRect(const QRectF &rectangle, const QBrush &brush) -->
  <!-- void QPainter::fillRect(int x, int y, int width, int height, const QBrush &brush) -->
  <!-- void QPainter::fillRect(const QRect &rectangle, const QBrush &brush) -->
  <!-- void QPainter::fillRect(const QRectF &rectangle, const QColor &color) -->
  <!-- void QPainter::fillRect(int x, int y, int width, int height, const QColor &color) -->
  <!-- void QPainter::fillRect(const QRect &rectangle, const QColor &color) -->
  <!-- void QPainter::fillRect(int x, int y, int width, int height, Qt::GlobalColor color) -->
  <!-- void QPainter::fillRect(const QRect &rectangle, Qt::GlobalColor color) -->
  <!-- void QPainter::fillRect(const QRectF &rectangle, Qt::GlobalColor color) -->
  <!-- void QPainter::fillRect(int x, int y, int width, int height, Qt::BrushStyle style) -->
  <!-- void QPainter::fillRect(const QRect &rectangle, Qt::BrushStyle style) -->
  <!-- void QPainter::fillRect(const QRectF &rectangle, Qt::BrushStyle style) -->
  <!-- void QPainter::fillRect(int x, int y, int width, int height, QGradient::Preset preset) -->
  <!-- void QPainter::fillRect(const QRect &rectangle, QGradient::Preset preset) -->
  <!-- void QPainter::fillRect(const QRectF &rectangle, QGradient::Preset preset) -->
  <function name="QPainter::fillRect">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="any" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QPainter::setBrush(const QBrush &brush) -->
  <!-- void QPainter::setBrush(Qt::BrushStyle style) -->
  <function name="QPainter::setBrush">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QPainter::setCompositionMode(QPainter::CompositionMode mode) -->
  <function name="QPainter::setCompositionMode">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QPainter::setFont(const QFont &font) -->
  <function name="QPainter::setFont">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QPainter::setOpacity(qreal opacity) -->
  <function name="QPainter::setOpacity">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QPainter::setPen(const QPen &pen) -->
  <!-- void QPainter::setPen(const QColor &color) -->
  <!-- void QPainter::setPen(Qt::PenStyle style) -->
  <function name="QPainter::setPen">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QPainter::setRenderHint(QPainter::RenderHint hint, bool on = true) -->
  <function name="QPainter::setRenderHint">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="true">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QPainter::translate(const QPointF &offset) -->
  <!-- void QPainter::translate(const QPoint &offset) -->
  <!-- void QPainter::translate(qreal dx, qreal dy) -->
  <function name="QPainter::translate">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool QPainter::viewTransformEnabled() const -->
  <function name="QPainter::viewTransformEnabled">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QRect QPainter::viewport() const -->
  <function name="QPainter::viewport">
    <noreturn>false</noreturn>
    <returnValue type="QRect"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QRect QPainter::window() const -->
  <function name="QPainter::window">
    <noreturn>false</noreturn>
    <returnValue type="QRect"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool QPainter::worldMatrixEnabled() const -->
  <function name="QPainter::worldMatrixEnabled">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- const QTransform &QPainter::worldTransform() const -->
  <function name="QPainter::worldTransform">
    <noreturn>false</noreturn>
    <returnValue type="const QTransform &amp;"/>
    <use-retval/>
    <const/>
  </function>
  <!-- void QTest::addColumn(const char *name, T *dummy = ...) -->
  <function name="QTest::addColumn">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- QTestData &QTest::newRow(const char *dataTag) -->
  <function name="QTest::newRow">
    <noreturn>false</noreturn>
    <returnValue type="QTestData &amp;"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- void QHash::clear() -->
  <function name="QHash::clear">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
  </function>
  <!-- bool QHash::contains(const Key &key) const -->
  <function name="QHash::contains">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int QHash::count(const Key &key) const -->
  <!-- int QHash::count() const -->
  <function name="QHash::count">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in" default="">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool QHash::empty() const -->
  <function name="QHash::empty">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QHash::iterator QHash::find(const Key &key) -->
  <!-- QHash::const_iterator QHash::find(const Key &key) const -->
  <function name="QHash::find">
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QHash::iterator QHash::insert(const Key &key, const T &value) -->
  <function name="QHash::insert">
    <noreturn>false</noreturn>
    <returnValue type="QHash::iterator"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool QHash::isEmpty() const -->
  <function name="QHash::isEmpty">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- int QHash::remove(const Key &key) -->
  <function name="QHash::remove">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int QHash::size() const -->
  <function name="QHash::size">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!-- void QHash::squeeze() -->
  <function name="QHash::squeeze">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
  </function>
  <!-- const T QHash::value(const Key &key) const -->
  <!-- const T QHash::value(const Key &key, const T &defaultValue) const -->
  <function name="QHash::value">
    <noreturn>false</noreturn>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="">
      <not-uninit/>
    </arg>
  </function>
  <!-- QList<T> QHash::values() const -->
  <!-- QList<T> QHash::values(const Key &key) const -->
  <function name="QHash::values">
    <noreturn>false</noreturn>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in" default="">
      <not-uninit/>
    </arg>
  </function>
  <!-- QXmlStreamWriter https://doc.qt.io/qt-5/qxmlstreamwriter.html -->
  <!-- bool	QXmlStreamWriter::autoFormatting() const -->
  <function name="QXmlStreamWriter::autoFormatting">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
  </function>
  <!-- int QXmlStreamWriter::autoFormattingIndent() const -->
  <function name="QXmlStreamWriter::autoFormattingIndent">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
  </function>
  <!-- QTextCodec *QXmlStreamWriter::codec() const -->
  <function name="QXmlStreamWriter::codec">
    <noreturn>false</noreturn>
    <returnValue type="QTextCodec *"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
  </function>
  <!-- QIODevice *QXmlStreamWriter::device() const -->
  <function name="QXmlStreamWriter::device">
    <noreturn>false</noreturn>
    <returnValue type="QIODevice *"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
  </function>
  <!-- bool QXmlStreamWriter::hasError() const -->
  <function name="QXmlStreamWriter::hasError">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
  </function>
  <!-- void QXmlStreamWriter::setAutoFormatting(bool enable) -->
  <function name="QXmlStreamWriter::setAutoFormatting">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void	QXmlStreamWriter::setAutoFormattingIndent(int spacesOrTabs) -->
  <function name="QXmlStreamWriter::setAutoFormattingIndent">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QXmlStreamWriter::writeAttribute(const QString &namespaceUri, const QString &name, const QString &value) -->
  <!-- void QXmlStreamWriter::writeAttribute(const QString &qualifiedName, const QString &value) -->
  <!-- void QXmlStreamWriter::writeAttribute(const QXmlStreamAttribute &attribute) -->
  <function name="QXmlStreamWriter::writeAttribute">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in" default="">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QXmlStreamWriter::writeAttributes(const QXmlStreamAttributes &attributes) -->
  <function name="QXmlStreamWriter::writeAttributes">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QXmlStreamWriter::writeCharacters(const QString &text) -->
  <function name="QXmlStreamWriter::writeCharacters">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QXmlStreamWriter::writeComment(const QString &text) -->
  <function name="QXmlStreamWriter::writeComment">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QXmlStreamWriter::writeEmptyElement(const QString &namespaceUri, const QString &name) -->
  <!-- void QXmlStreamWriter::writeEmptyElement(const QString &qualifiedName) -->
  <function name="QXmlStreamWriter::writeEmptyElement">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QXmlStreamWriter::writeEndDocument() -->
  <function name="QXmlStreamWriter::writeEndDocument">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
  </function>
  <!-- void QXmlStreamWriter::writeEndElement() -->
  <function name="QXmlStreamWriter::writeEndElement">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
  </function>
  <!-- void QXmlStreamWriter::writeStartDocument(const QString &version) -->
  <!-- void QXmlStreamWriter::writeStartDocument() -->
  <!-- void QXmlStreamWriter::writeStartDocument(const QString &version, bool standalone) -->
  <function name="QXmlStreamWriter::writeStartDocument">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in" default="">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in" default="">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QXmlStreamWriter::writeStartElement(const QString &namespaceUri, const QString &name) -->
  <!-- void QXmlStreamWriter::writeStartElement(const QString &qualifiedName) -->
  <function name="QXmlStreamWriter::writeStartElement">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QXmlStreamWriter::writeTextElement(const QString &namespaceUri, const QString &name, const QString &text) -->
  <!-- void QXmlStreamWriter::writeTextElement(const QString &qualifiedName, const QString &text) -->
  <function name="QXmlStreamWriter::writeTextElement">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in" default="">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QXmlStreamAttributes https://doc.qt.io/qt-5/qxmlstreamattributes.html -->
  <!-- void QXmlStreamAttributes::append(const QString &namespaceUri, const QString &name, const QString &value) -->
  <!-- void QXmlStreamAttributes::append(const QString &qualifiedName, const QString &value) -->
  <function name="QXmlStreamAttributes::append">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in" default="">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool QXmlStreamAttributes::hasAttribute(const QString &qualifiedName) const -->
  <!-- bool QXmlStreamAttributes::hasAttribute(QLatin1String qualifiedName) const -->
  <!-- bool QXmlStreamAttributes::hasAttribute(const QString &namespaceUri, const QString &name) const -->
  <function name="QXmlStreamAttributes::hasAttribute">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="">
      <not-uninit/>
    </arg>
  </function>
  <!-- QStringRef QXmlStreamAttributes::value(const QString &namespaceUri, const QString &name) const -->
  <!-- QStringRef QXmlStreamAttributes::value(const QString &namespaceUri, QLatin1String name) const -->
  <!-- QStringRef QXmlStreamAttributes::value(QLatin1String namespaceUri, QLatin1String name) const -->
  <!-- QStringRef QXmlStreamAttributes::value(const QString &qualifiedName) const -->
  <!-- QStringRef QXmlStreamAttributes::value(QLatin1String qualifiedName) const -->
  <function name="QXmlStreamAttributes::value">
    <noreturn>false</noreturn>
    <returnValue type="QStringRef"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="">
      <not-uninit/>
    </arg>
  </function>
  <!-- QRegExp https://doc.qt.io/qt-5/qregexp.html -->
  <!-- QString QRegExp::cap(int nth = 0) const -->
  <function name="QRegExp::cap">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in" default="0">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- int QRegExp::captureCount() const -->
  <function name="QRegExp::captureCount">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
  </function>
  <!-- QStringList QRegExp::capturedTexts() const -->
  <function name="QRegExp::capturedTexts">
    <noreturn>false</noreturn>
    <returnValue type="QStringList"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
  </function>
  <!-- Qt::CaseSensitivity QRegExp::caseSensitivity() const -->
  <function name="QRegExp::caseSensitivity">
    <noreturn>false</noreturn>
    <returnValue type="Qt::CaseSensitivity"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
  </function>
  <!-- QString QRegExp::errorString() const -->
  <function name="QRegExp::errorString">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
  </function>
  <!-- QString QRegExp::escape(const QString &str) // static -->
  <function name="QRegExp::escape">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <leak-ignore/>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool QRegExp::exactMatch(const QString &str) const -->
  <function name="QRegExp::exactMatch">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int QRegExp::indexIn(const QString &str, int offset = 0, QRegExp::CaretMode caretMode = CaretAtZero) const -->
  <function name="QRegExp::indexIn">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in" default="0">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in" default="CaretAtZero">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool QRegExp::isEmpty() const -->
  <!-- bool QRegExp::isMinimal() const -->
  <!-- bool QRegExp::isValid() const -->
  <function name="QRegExp::isEmpty,QRegExp::isMinimal,QRegExp::isValid">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
  </function>
  <!-- int QRegExp::lastIndexIn(const QString &str, int offset = -1, QRegExp::CaretMode caretMode = CaretAtZero) const -->
  <function name="QRegExp::lastIndexIn">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in" default="-1">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in" default="CaretAtZero">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int QRegExp::matchedLength() const -->
  <function name="QRegExp::matchedLength">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
  </function>
  <!-- QString QRegExp::pattern() const -->
  <function name="QRegExp::pattern">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
  </function>
  <!-- QRegExp::PatternSyntax QRegExp::patternSyntax() const -->
  <function name="QRegExp::patternSyntax">
    <noreturn>false</noreturn>
    <returnValue type="QRegExp::PatternSyntax"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
  </function>
  <!-- int QRegExp::pos(int nth = 0) const -->
  <function name="QRegExp::pos">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in" default="0">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QRegExp::setCaseSensitivity(Qt::CaseSensitivity cs) -->
  <!-- void QRegExp::setMinimal(bool minimal) -->
  <!-- void QRegExp::setPattern(const QString &pattern) -->
  <!-- void QRegExp::setPatternSyntax(QRegExp::PatternSyntax syntax) -->
  <function name="QRegExp::setCaseSensitivity,QRegExp::setMinimal,QRegExp::setPattern,QRegExp::setPatternSyntax">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QRegExp::swap(QRegExp &other) -->
  <function name="QRegExp::swap">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1"/>
  </function>
  <!-- QAction https://doc.qt.io/qt-5/qaction.html -->
  <!-- QVariant QAction::property(const char *name) const -->
  <function name="QAction::property">
    <noreturn>false</noreturn>
    <returnValue type="QVariant"/>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QAction::setCheckable(bool) -->
  <function name="QAction::setCheckable">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QAction::setChecked(bool) -->
  <function name="QAction::setChecked">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QAction::setData(const QVariant &userData) -->
  <function name="QAction::setData">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QAction::setDisabled(bool b) -->
  <function name="QAction::setDisabled">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QAction::setEnabled(bool) -->
  <function name="QAction::setEnabled">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QAction::setIcon(const QIcon &icon) -->
  <function name="QAction::setIcon">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool QAction::setProperty(const char *name, const QVariant &value) -->
  <function name="QAction::setProperty">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QAction::setShortcut(const QKeySequence &shortcut) -->
  <function name="QAction::setShortcut">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QAction::setStatusTip(const QString &statusTip) -->
  <function name="QAction::setStatusTip">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QAction::setText(const QString &text) -->
  <function name="QAction::setText">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QAction::setToolTip(const QString &tip) -->
  <function name="QAction::setToolTip">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QAction::setVisible(bool) -->
  <function name="QAction::setVisible">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QAction::setWhatsThis(const QString &what) -->
  <function name="QAction::setWhatsThis">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QString QAction::text() const -->
  <function name="QAction::text">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QLabel https://doc.qt.io/qt-5/qlabel.html -->
  <!-- void QLabel::setAlignment(Qt::Alignment) -->
  <function name="QLabel::setAlignment">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QLabel::setBuddy(QWidget *buddy) -->
  <function name="QLabel::setBuddy">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QLabel::setEnabled(bool) -->
  <function name="QLabel::setEnabled">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QLabel::setFont(const QFont &) -->
  <function name="QLabel::setFont">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QLabel::setGeometry(int x, int y, int w, int h) -->
  <!-- void QLabel::setGeometry(const QRect &) -->
  <function name="QLabel::setGeometry">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in" default="">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in" default="">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="4" direction="in" default="">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QLabel::setPixmap(const QPixmap &) -->
  <function name="QLabel::setPixmap">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QLabel::setText(const QString &) -->
  <function name="QLabel::setText">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QLabel::setToolTip(const QString &) -->
  <function name="QLabel::setToolTip">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QLabel::setVisible(bool visible) -->
  <function name="QLabel::setVisible">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QLabel::setWordWrap(bool on) -->
  <function name="QLabel::setWordWrap">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QString QLabel::text() const -->
  <function name="QLabel::text">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QDomElement https://doc.qt.io/qt-5/qdomelement.html -->
  <!-- QDomNode QDomElement::appendChild(const QDomNode &newChild) -->
  <function name="QDomElement::appendChild">
    <noreturn>false</noreturn>
    <returnValue type="QDomNode"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QString QDomElement::attribute(const QString &name, const QString &defValue = QString()) const -->
  <function name="QDomElement::attribute">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in" default="QString()">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QDomNamedNodeMap QDomElement::attributes() const -->
  <function name="QDomElement::attributes">
    <noreturn>false</noreturn>
    <returnValue type="QDomNamedNodeMap"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QDomAttr QDomElement::attributeNode(const QString &name) -->
  <function name="QDomElement::attributeNode">
    <noreturn>false</noreturn>
    <returnValue type="QDomAttr"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QDomNodeList QDomElement::elementsByTagName(const QString &tagname) const -->
  <function name="QDomElement::elementsByTagName">
    <noreturn>false</noreturn>
    <returnValue type="QDomNodeList"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QDomElement QDomElement::firstChildElement(const QString &tagName = QString()) const -->
  <function name="QDomElement::firstChildElement">
    <noreturn>false</noreturn>
    <returnValue type="QDomElement"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in" default="QString()">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool QDomElement::hasAttribute(const QString &name) const -->
  <function name="QDomElement::hasAttribute">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QDomNode QDomElement::namedItem(const QString &name) const -->
  <function name="QDomElement::namedItem">
    <noreturn>false</noreturn>
    <returnValue type="QDomNode"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QDomElement QDomElement::nextSiblingElement(const QString &tagName = QString()) const -->
  <function name="QDomElement::nextSiblingElement">
    <noreturn>false</noreturn>
    <returnValue type="QDomElement"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in" default="QString()">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QDomElement::setAttribute(const QString &name, const QString &value) -->
  <!-- void QDomElement::setAttribute(const QString &name, qlonglong value) -->
  <!-- void QDomElement::setAttribute(const QString &name, qulonglong value) -->
  <!-- void QDomElement::setAttribute(const QString &name, int value) -->
  <!-- void QDomElement::setAttribute(const QString &name, uint value) -->
  <!-- void QDomElement::setAttribute(const QString &name, float value) -->
  <!-- void QDomElement::setAttribute(const QString &name, double value) -->
  <function name="QDomElement::setAttribute">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QDomAttr QDomElement::setAttributeNode(const QDomAttr &newAttr) -->
  <function name="QDomElement::setAttributeNode">
    <noreturn>false</noreturn>
    <returnValue type="QDomAttr"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QDomElement::setTagName(const QString &name) -->
  <function name="QDomElement::setTagName">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QString QDomElement::tagName() const -->
  <function name="QDomElement::tagName">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QString QDomElement::text() const -->
  <function name="QDomElement::text">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QVector https://doc.qt.io/qt-5/qvector.html -->
  <!-- QStack inherits from QVector https://doc.qt.io/qt-5/qstack.html -->
  <!-- void QVector::append(const T &value) -->
  <!-- void QVector::append(T &&value) -->
  <!-- void QVector::append(const QVector<T> &value) -->
  <function name="QVector::append,QStack::append">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- const T &QVector::at(int i) const -->
  <function name="QVector::at,QStack::at">
    <noreturn>false</noreturn>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- bool QVector::contains(const T &value) const -->
  <function name="QVector::contains,QStack::contains">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int QVector::count(const T &value) const -->
  <!-- int QVector::count() const -->
  <function name="QVector::count,QStack::count">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in" default="">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool QVector::endsWith(const T &value) const -->
  <function name="QVector::endsWith,QStack::endsWith">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QVector<T> &QVector::fill(const T &value, int size = ...) -->
  <function name="QVector::fill,QStack::fill">
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="-1">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int QVector::indexOf(const T &value, int from = ...) const -->
  <function name="QVector::indexOf,QStack::indexOf">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QVector::insert(int i, int count, const T &value) -->
  <!-- QVector::iterator QVector::insert(QVector::iterator before, int count, const T &value) -->
  <function name="QVector::insert,QStack::insert">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int QVector::lastIndexOf(const T &value, int from = ...) const -->
  <function name="QVector::lastIndexOf,QStack::lastIndexOf">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="-1">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QVector::push_back(const T &value) -->
  <!-- void QVector::push_back(T &&value) -->
  <function name="QVector::push_back,QStack::push_back">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QVector::remove(int i) -->
  <!-- void QVector::remove(int i, int count) -->
  <function name="QVector::remove,QStack::remove">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in" default="">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int QVector::removeAll(const T &t) -->
  <function name="QVector::removeAll,QStack::removeAll">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QVector::removeAt(int i) -->
  <function name="QVector::removeAt,QStack::removeAt">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QVector::replace(int i, const T &value) -->
  <function name="QVector::replace,QStack::replace">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QVector::reserve(int size) -->
  <function name="QVector::reserve,QStack::reserve">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QVector::resize(int size) -->
  <function name="QVector::resize,QStack::resize">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int QVector::size() const -->
  <function name="QVector::size,QStack::size">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!-- void QVector::squeeze() -->
  <function name="QVector::squeeze,QStack::squeeze">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
  </function>
  <!-- bool QVector::startsWith(const T &value) const -->
  <function name="QVector::startsWith,QStack::startsWith">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QVector::swap(QVector<T> &other) -->
  <function name="QVector::swap,QStack::swap">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1"/>
  </function>
  <!-- T QVector::value(int i) const -->
  <!-- T QVector::value(int i, const T &defaultValue) const -->
  <function name="QVector::value,QStack::value">
    <noreturn>false</noreturn>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in" default="">
      <not-uninit/>
    </arg>
  </function>
  <!-- QStack https://doc.qt.io/qt-5/qstack.html -->
  <!-- T QStack::pop() -->
  <function name="QStack::pop">
    <noreturn>false</noreturn>
  </function>
  <!-- void QStack::push(const T &t) -->
  <function name="QStack::push">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- T &QStack::top() -->
  <!-- const T &QStack::top() const -->
  <function name="QStack::top">
    <noreturn>false</noreturn>
    <use-retval/>
  </function>
  <!-- QByteArray https://doc.qt.io/qt-5/qbytearray.html -->
  <!-- char QByteArray::at(int i) const -->
  <function name="QByteArray::at">
    <noreturn>false</noreturn>
    <returnValue type="char"/>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QLinkedList https://doc.qt.io/qt-5/qlinkedlist.html -->
  <!-- void QLinkedList::append(const T &value) -->
  <function name="QLinkedList::append">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int QLinkedList::count(const T &value) const -->
  <!-- int QLinkedList::count() const -->
  <function name="QLinkedList::count">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in" default="">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool QLinkedList::endsWith(const T &value) const -->
  <function name="QLinkedList::endsWith">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QLinkedList::iterator QLinkedList::erase(QLinkedList::iterator pos) -->
  <!-- QLinkedList::iterator QLinkedList::erase(QLinkedList::iterator begin, QLinkedList::iterator end) -->
  <function name="QLinkedList::erase">
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" default="">
      <not-uninit/>
    </arg>
  </function>
  <!-- int QLinkedList::size() const -->
  <function name="QLinkedList::size">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool QLinkedList::startsWith(const T &value) const -->
  <function name="QLinkedList::startsWith">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QDateTime https://doc.qt.io/qt-5/qdatetime.html -->
  <!-- QDateTime QDateTime::addDays(qint64 ndays) const -->
  <!-- QDateTime QDateTime::addMSecs(qint64 msecs) const -->
  <!-- QDateTime QDateTime::addMonths(int nmonths) const -->
  <!-- QDateTime QDateTime::addSecs(qint64 s) const -->
  <!-- QDateTime QDateTime::addYears(int nyears) const -->
  <function name="QDateTime::addDays,QDateTime::addMSecs,QDateTime::addMonths,QDateTime::addSecs,QDateTime::addYears">
    <noreturn>false</noreturn>
    <returnValue type="QDateTime"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QDateTime QDateTime::currentDateTime() // static -->
  <!-- QDateTime QDateTime::currentDateTimeUtc() // static -->
  <function name="QDateTime::currentDateTime,QDateTime::currentDateTimeUtc">
    <noreturn>false</noreturn>
    <returnValue type="QDateTime"/>
    <use-retval/>
    <const/>
  </function>
  <!-- qint64 QDateTime::currentMSecsSinceEpoch() // static -->
  <!-- qint64 QDateTime::currentSecsSinceEpoch() // static -->
  <function name="QDateTime::currentMSecsSinceEpoch,QDateTime::currentSecsSinceEpoch">
    <noreturn>false</noreturn>
    <returnValue type="qint64"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QDate QDateTime::date() const -->
  <function name="QDateTime::date">
    <noreturn>false</noreturn>
    <returnValue type="QDate"/>
    <use-retval/>
    <const/>
  </function>
  <!-- qint64 QDateTime::daysTo(const QDateTime &other) const -->
  <function name="QDateTime::daysTo">
    <noreturn>false</noreturn>
    <returnValue type="qint64"/>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QDateTime QDateTime::fromCFDate(CFDateRef date) // static -->
  <function name="QDateTime::fromCFDate">
    <noreturn>false</noreturn>
    <returnValue type="QDateTime"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QDateTime QDateTime::fromMSecsSinceEpoch(qint64 msecs) // static -->
  <!-- QDateTime QDateTime::fromMSecsSinceEpoch(qint64 msecs, Qt::TimeSpec spec, int offsetSeconds = 0) // static -->
  <!-- QDateTime QDateTime::fromMSecsSinceEpoch(qint64 msecs, const QTimeZone &timeZone) // static -->
  <function name="QDateTime::fromMSecsSinceEpoch">
    <noreturn>false</noreturn>
    <returnValue type="QDateTime"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in" default="">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in" default="0">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QDateTime QDateTime::fromNSDate(const NSDate *date) // static -->
  <function name="QDateTime::fromNSDate">
    <noreturn>false</noreturn>
    <returnValue type="QDateTime"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QDateTime QDateTime::fromSecsSinceEpoch(qint64 secs, Qt::TimeSpec spec = Qt::LocalTime, int offsetSeconds = 0) // static -->
  <!-- QDateTime QDateTime::fromSecsSinceEpoch(qint64 secs, const QTimeZone &timeZone) // static -->
  <function name="QDateTime::fromSecsSinceEpoch">
    <noreturn>false</noreturn>
    <returnValue type="QDateTime"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in" default="">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in" default="0">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QDateTime QDateTime::fromString(const QString &string, Qt::DateFormat format = Qt::TextDate) // static -->
  <!-- QDateTime QDateTime::fromString(const QString &string, const QString &format) // static -->
  <function name="QDateTime::fromString">
    <noreturn>false</noreturn>
    <returnValue type="QDateTime"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in" default="">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool QDateTime::isDaylightTime() const -->
  <!-- bool QDateTime::isNull() const -->
  <!-- bool QDateTime::isValid() const -->
  <function name="QDateTime::isDaylightTime,QDateTime::isNull,QDateTime::isValid">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- qint64 QDateTime::msecsTo(const QDateTime &other) const -->
  <function name="QDateTime::msecsTo">
    <noreturn>false</noreturn>
    <returnValue type="qint64"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int QDateTime::offsetFromUtc() const -->
  <function name="QDateTime::offsetFromUtc">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!-- qint64 QDateTime::secsTo(const QDateTime &other) const -->
  <function name="QDateTime::secsTo">
    <noreturn>false</noreturn>
    <returnValue type="qint64"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QDateTime::setDate(const QDate &date) -->
  <!-- void QDateTime::setMSecsSinceEpoch(qint64 msecs) -->
  <!-- void QDateTime::setOffsetFromUtc(int offsetSeconds) -->
  <!-- void QDateTime::setSecsSinceEpoch(qint64 secs) -->
  <!-- void QDateTime::setTime(const QTime &time) -->
  <!-- void QDateTime::setTimeSpec(Qt::TimeSpec spec) -->
  <!-- void QDateTime::setTimeZone(const QTimeZone &toZone) -->
  <function name="QDateTime::setDate,QDateTime::setMSecsSinceEpoch,QDateTime::setOffsetFromUtc,QDateTime::setSecsSinceEpoch,QDateTime::setTime,QDateTime::setTimeSpec,QDateTime::setTimeZone">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void QDateTime::swap(QDateTime &other) -->
  <function name="QDateTime::swap">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1"/>
  </function>
  <!-- QTime QDateTime::time() const -->
  <function name="QDateTime::time">
    <noreturn>false</noreturn>
    <returnValue type="QTime"/>
    <use-retval/>
    <const/>
  </function>
  <!-- Qt::TimeSpec QDateTime::timeSpec() const -->
  <function name="QDateTime::timeSpec">
    <noreturn>false</noreturn>
    <returnValue type="Qt::TimeSpec"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QTimeZone QDateTime::timeZone() const -->
  <function name="QDateTime::timeZone">
    <noreturn>false</noreturn>
    <returnValue type="QTimeZone"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QString QDateTime::timeZoneAbbreviation() const -->
  <function name="QDateTime::timeZoneAbbreviation">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <const/>
  </function>
  <!-- CFDateRef QDateTime::toCFDate() const -->
  <function name="QDateTime::toCFDate">
    <noreturn>false</noreturn>
    <returnValue type="CFDateRef"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QDateTime QDateTime::toLocalTime() const -->
  <function name="QDateTime::toLocalTime">
    <noreturn>false</noreturn>
    <returnValue type="QDateTime"/>
    <use-retval/>
    <const/>
  </function>
  <!-- qint64 QDateTime::toMSecsSinceEpoch() const -->
  <function name="QDateTime::toMSecsSinceEpoch">
    <noreturn>false</noreturn>
    <returnValue type="qint64"/>
    <use-retval/>
    <const/>
  </function>
  <!-- NSDate *QDateTime::toNSDate() const -->
  <function name="QDateTime::toNSDate">
    <noreturn>false</noreturn>
    <returnValue type="NSDate *"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QDateTime QDateTime::toOffsetFromUtc(int offsetSeconds) const -->
  <function name="QDateTime::toOffsetFromUtc">
    <noreturn>false</noreturn>
    <returnValue type="QDateTime"/>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- qint64 QDateTime::toSecsSinceEpoch() const -->
  <function name="QDateTime::toSecsSinceEpoch">
    <noreturn>false</noreturn>
    <returnValue type="qint64"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QString QDateTime::toString(Qt::DateFormat format = Qt::TextDate) const -->
  <function name="QDateTime::toString">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in" default="Qt::TextDate">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QDateTime QDateTime::toTimeSpec(Qt::TimeSpec spec) const -->
  <!-- QDateTime QDateTime::toTimeZone(const QTimeZone &timeZone) const -->
  <function name="QDateTime::toTimeSpec,QDateTime::toTimeZone">
    <noreturn>false</noreturn>
    <returnValue type="QDateTime"/>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QDateTime QDateTime::toUTC() const -->
  <function name="QDateTime::toUTC">
    <noreturn>false</noreturn>
    <returnValue type="QDateTime"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QSqlDatabase https://doc.qt.io/qt-5/qsqldatabase.html -->
  <!-- bool QSqlDatabase::commit() -->
  <!-- bool QSqlDatabase::rollback() -->
  <!-- bool QSqlDatabase::open() -->
  <!-- bool QSqlDatabase::transaction() -->
  <function name="QSqlDatabase::commit,QSqlDatabase::rollback,QSqlDatabase::open,QSqlDatabase::transaction">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval type="error-code"/>
    <leak-ignore/>
  </function>
  <!-- QSqlQuery https://doc.qt.io/qt-5/qsqlquery.html -->
  <!-- void QSqlQuery::addBindValue(const QVariant &val, QSql::ParamType paramType = QSql::In) -->
  <function name="QSqlQuery::addBindValue">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="QSql::In">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int QSqlQuery::at() const -->
  <function name="QSqlQuery::at">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
  </function>
  <!-- void QSqlQuery::bindValue(const QString &placeholder, const QVariant &val, QSql::ParamType paramType = QSql::In) -->
  <!-- void QSqlQuery::bindValue(int pos, const QVariant &val, QSql::ParamType paramType = QSql::In) -->
  <function name="QSqlQuery::bindValue">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in" default="QSql::In">
      <not-uninit/>
    </arg>
  </function>
  <!-- QVariant QSqlQuery::boundValue(const QString &placeholder) const -->
  <!-- QVariant QSqlQuery::boundValue(int pos) const -->
  <function name="QSqlQuery::boundValue">
    <noreturn>false</noreturn>
    <returnValue type="QVariant"/>
    <use-retval/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- QMap<QString, QVariant> QSqlQuery::boundValues() const -->
  <function name="QSqlQuery::boundValues">
    <noreturn>false</noreturn>
    <!-- <returnValue type="QMap&lt;QString, QVariant&gt;"/> TODO: enable when such return types are allowed #9229 -->
    <use-retval/>
    <const/>
  </function>
  <!-- void QSqlQuery::clear() -->
  <function name="QSqlQuery::clear">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
  </function>
  <!-- const QSqlDriver *QSqlQuery::driver() const -->
  <function name="QSqlQuery::driver">
    <noreturn>false</noreturn>
    <returnValue type="const QSqlDriver *"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool QSqlQuery::exec(const QString &query) -->
  <!-- bool QSqlQuery::exec() -->
  <function name="QSqlQuery::exec">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <leak-ignore/>
    <use-retval type="error-code"/>
    <arg nr="1" direction="in" default="">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool QSqlQuery::execBatch(QSqlQuery::BatchExecutionMode mode = ValuesAsRows) -->
  <function name="QSqlQuery::execBatch">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <leak-ignore/>
    <arg nr="1" direction="in" default="ValuesAsRows">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- QString QSqlQuery::executedQuery() const -->
  <function name="QSqlQuery::executedQuery">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <const/>
  </function>
  <!-- void QSqlQuery::finish() -->
  <function name="QSqlQuery::finish">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
  </function>
  <!-- bool QSqlQuery::first() -->
  <function name="QSqlQuery::first">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
  </function>
  <!-- bool QSqlQuery::isActive() const -->
  <!-- bool QSqlQuery::isForwardOnly() const -->
  <function name="QSqlQuery::isActive,QSqlQuery::isForwardOnly">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool QSqlQuery::isNull(int field) const -->
  <!-- bool QSqlQuery::isNull(const QString &name) const -->
  <function name="QSqlQuery::isNull">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool QSqlQuery::isSelect() const -->
  <!-- bool QSqlQuery::isValid() const -->
  <function name="QSqlQuery::isSelect,QSqlQuery::isValid">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool QSqlQuery::last() -->
  <function name="QSqlQuery::last">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
  </function>
  <!-- QSqlError QSqlQuery::lastError() const -->
  <function name="QSqlQuery::lastError">
    <noreturn>false</noreturn>
    <returnValue type="QSqlError"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QVariant QSqlQuery::lastInsertId() const -->
  <function name="QSqlQuery::lastInsertId">
    <noreturn>false</noreturn>
    <returnValue type="QVariant"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QString QSqlQuery::lastQuery() const -->
  <function name="QSqlQuery::lastQuery">
    <noreturn>false</noreturn>
    <returnValue type="QString"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool QSqlQuery::next() -->
  <!-- bool QSqlQuery::nextResult() -->
  <function name="QSqlQuery::next,QSqlQuery::nextResult">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
  </function>
  <!-- int QSqlQuery::numRowsAffected() const -->
  <function name="QSqlQuery::numRowsAffected">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QSql::NumericalPrecisionPolicy QSqlQuery::numericalPrecisionPolicy() const -->
  <function name="QSqlQuery::numericalPrecisionPolicy">
    <noreturn>false</noreturn>
    <returnValue type="QSql::NumericalPrecisionPolicy"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool QSqlQuery::prepare(const QString &query) -->
  <function name="QSqlQuery::prepare">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <leak-ignore/>
    <use-retval type="error-code"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- bool QSqlQuery::previous() -->
  <function name="QSqlQuery::previous">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
  </function>
  <!-- QSqlRecord QSqlQuery::record() const -->
  <function name="QSqlQuery::record">
    <noreturn>false</noreturn>
    <returnValue type="QSqlRecord"/>
    <use-retval/>
    <const/>
  </function>
  <!-- const QSqlResult *QSqlQuery::result() const -->
  <function name="QSqlQuery::result">
    <noreturn>false</noreturn>
    <returnValue type="const QSqlResult *"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool QSqlQuery::seek(int index, bool relative = false) -->
  <function name="QSqlQuery::seek">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="false">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QSqlQuery::setForwardOnly(bool forward) -->
  <function name="QSqlQuery::setForwardOnly">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void QSqlQuery::setNumericalPrecisionPolicy(QSql::NumericalPrecisionPolicy precisionPolicy) -->
  <function name="QSqlQuery::setNumericalPrecisionPolicy">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int QSqlQuery::size() const -->
  <function name="QSqlQuery::size">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!-- QVariant QSqlQuery::value(int index) const -->
  <!-- QVariant QSqlQuery::value(const QString &name) const -->
  <function name="QSqlQuery::value">
    <noreturn>false</noreturn>
    <returnValue type="QVariant"/>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- ##### Container ##### -->
  <container id="qtContainer" endPattern="&gt; !!::" opLessAllowed="false" itEndPattern="&gt; :: iterator|const_iterator">
    <type templateParameter="0"/>
    <size>
      <function name="append" action="push"/>
      <function name="clear" action="clear"/>
      <function name="count" yields="size"/>
      <function name="erase" action="erase"/>
      <function name="insert" action="insert"/>
      <function name="isEmpty" yields="empty"/>
      <function name="length" yields="size"/>
      <function name="size" yields="size"/>
      <function name="swap" action="change"/>
    </size>
    <access>
      <function name="begin" yields="start-iterator"/>
      <function name="cbegin" yields="start-iterator"/>
      <function name="constBegin" yields="start-iterator"/>
      <function name="end" yields="end-iterator"/>
      <function name="cend" yields="end-iterator"/>
      <function name="constEnd" yields="end-iterator"/>
    </access>
  </container>
  <container id="qtList" startPattern="QList &lt;" inherits="qtContainer" opLessAllowed="true" itEndPattern="&gt; :: iterator|const_iterator|reverse_iterator|const_reverse_iterator">
    <size>
      <function name="erase" action="erase" yields="iterator"/>
      <function name="resize" action="resize"/>
      <function name="empty" yields="empty"/>
      <function name="push_back" action="push"/>
      <function name="push_front" action="push"/>
      <function name="prepend" action="push"/>
      <function name="pop_back" action="pop"/>
      <function name="pop_front" action="pop"/>
      <function name="removeAll" action="change"/>
      <function name="removeAt" action="pop"/>
      <function name="removeDuplicates" action="change"/>
      <function name="removeFirst" action="pop"/>
      <function name="removeLast" action="pop"/>
      <function name="takeAt" action="pop"/>
      <function name="takeFirst" action="pop"/>
      <function name="takeLast" action="pop"/>
    </size>
    <access indexOperator="array-like">
      <function name="at" yields="at_index"/>
      <function name="front" yields="item"/>
      <function name="first" yields="item"/>
      <function name="back" yields="item"/>
      <function name="last" yields="item"/>
      <function name="reserve" action="change-internal"/>
      <function name="rbegin" yields="start-iterator"/>
      <function name="crbegin" yields="start-iterator"/>
      <function name="rend" yields="end-iterator"/>
      <function name="crend" yields="end-iterator"/>
    </access>
  </container>
  <container id="qtLinkedList" startPattern="QLinkedList &lt;" inherits="qtContainer" itEndPattern="&gt; :: iterator|const_iterator|reverse_iterator|const_reverse_iterator">
    <size>
      <function name="erase" action="erase" yields="iterator"/>
      <function name="insert" action="insert" yields="iterator"/>
      <function name="empty" yields="empty"/>
      <function name="push_back" action="push"/>
      <function name="push_front" action="push"/>
      <function name="prepend" action="push"/>
      <function name="pop_back" action="pop"/>
      <function name="pop_front" action="pop"/>
      <function name="removeAll" action="change"/>
      <function name="removeAt" action="pop"/>
      <function name="removeFirst" action="pop"/>
      <function name="removeLast" action="pop"/>
      <function name="removeOne" action="pop"/>
      <function name="takeFirst" action="pop"/>
      <function name="takeLast" action="pop"/>
    </size>
    <access>
      <function name="front" yields="item"/>
      <function name="first" yields="item"/>
      <function name="back" yields="item"/>
      <function name="last" yields="item"/>
      <function name="rbegin" yields="start-iterator"/>
      <function name="crbegin" yields="start-iterator"/>
      <function name="rend" yields="end-iterator"/>
      <function name="crend" yields="end-iterator"/>
    </access>
  </container>
  <container id="qtVector" startPattern="QVector &lt;" inherits="qtContainer" opLessAllowed="true" itEndPattern="&gt; :: iterator|const_iterator|reverse_iterator|const_reverse_iterator">
    <size>
      <function name="erase" action="erase" yields="iterator"/>
      <function name="resize" action="resize"/>
      <function name="push_back" action="push"/>
      <function name="push_front" action="push"/>
      <function name="prepend" action="push"/>
      <function name="pop_back" action="pop"/>
      <function name="pop_front" action="pop"/>
      <function name="empty" yields="empty"/>
      <function name="shrink_to_fit" action="change-internal"/>
      <function name="squeeze" action="change-internal"/>
      <function name="reserve" action="change-internal"/>
    </size>
    <access indexOperator="array-like">
      <function name="at" yields="at_index"/>
      <function name="front" yields="item"/>
      <function name="first" yields="item"/>
      <function name="back" yields="item"/>
      <function name="last" yields="item"/>
      <function name="data" yields="buffer"/>
      <function name="constData" yields="buffer-nt"/>
      <function name="rbegin" yields="start-iterator"/>
      <function name="crbegin" yields="start-iterator"/>
      <function name="rend" yields="end-iterator"/>
      <function name="crend" yields="end-iterator"/>
    </access>
  </container>
  <container id="qtStack" startPattern="QStack &lt;" inherits="qtVector">
    <size>
      <function name="push" action="push"/>
      <function name="pop" action="pop"/>
    </size>
    <access indexOperator="array-like">
      <function name="top" yields="item"/>
    </access>
  </container>
  <container id="qtString" startPattern="QString" endPattern="" inherits="qtContainer" opLessAllowed="true" itEndPattern=":: iterator|const_iterator|reverse_iterator|const_reverse_iterator">
    <type string="std-like"/>
    <size>
      <function name="isNull" yields="empty"/>
      <function name="resize" action="resize"/>
      <function name="push_back" action="push"/>
      <function name="push_front" action="push"/>
      <function name="prepend" action="push"/>
      <function name="shrink_to_fit" action="change-internal"/>
      <function name="squeeze" action="change-internal"/>
      <function name="reserve" action="change-internal"/>
      <function name="chop" action="change"/>
      <function name="remove" action="change"/>
    </size>
    <access indexOperator="array-like">
      <function name="at" yields="at_index"/>
      <function name="front" yields="item"/>
      <function name="back" yields="item"/>
      <function name="rbegin" yields="start-iterator"/>
      <function name="crbegin" yields="start-iterator"/>
      <function name="rend" yields="end-iterator"/>
      <function name="crend" yields="end-iterator"/>
    </access>
  </container>
  <container id="qtLatin1String" startPattern="QLatin1String" endPattern="" inherits="qtContainer" opLessAllowed="true" itEndPattern=":: const_iterator|const_reverse_iterator">
    <type string="std-like"/>
    <size>
      <function name="isNull" yields="empty"/>
      <function name="chop" action="change"/>
    </size>
    <access indexOperator="array-like">
      <function name="at" yields="at_index"/>
      <function name="front" yields="item"/>
      <function name="back" yields="item"/>
      <function name="rbegin" yields="start-iterator"/>
      <function name="crbegin" yields="start-iterator"/>
      <function name="rend" yields="end-iterator"/>
      <function name="crend" yields="end-iterator"/>
    </access>
  </container>
  <container id="qtByteArray" startPattern="QByteArray" endPattern="" inherits="qtContainer" opLessAllowed="true" itEndPattern=":: iterator|const_iterator|reverse_iterator|const_reverse_iterator">
    <size>
      <function name="isNull" yields="empty"/>
      <function name="resize" action="resize"/>
      <function name="push_back" action="push"/>
      <function name="push_front" action="push"/>
      <function name="prepend" action="push"/>
      <function name="shrink_to_fit" action="change-internal"/>
      <function name="squeeze" action="change-internal"/>
      <function name="reserve" action="change-internal"/>
    </size>
    <access indexOperator="array-like">
      <function name="at" yields="at_index"/>
      <function name="front" yields="item"/>
      <function name="back" yields="item"/>
      <function name="data" yields="buffer"/>
      <function name="constData" yields="buffer-nt"/>
      <function name="rbegin" yields="start-iterator"/>
      <function name="crbegin" yields="start-iterator"/>
      <function name="rend" yields="end-iterator"/>
      <function name="crend" yields="end-iterator"/>
    </access>
  </container>
  <container id="qtMap" startPattern="QMap|QMultiMap &lt;" inherits="qtContainer">
    <type templateParameter="1" associative="std-like"/>
    <rangeItemRecordType>
      <member name="first" templateParameter="0"/>
      <member name="second" templateParameter="1"/>
    </rangeItemRecordType>
    <access>
      <function name="count" action="find"/>
      <function name="find" action="find" yields="iterator"/>
      <function name="insert" action="push"/>
      <function name="lower_bound" yields="iterator"/>
      <function name="upper_bound" yields="iterator"/>
    </access>
  </container>
  <container id="qtSet" startPattern="QSet &lt;" inherits="qtContainer">
    <access>
      <function name="count" action="find"/>
      <function name="find" action="find" yields="iterator"/>
      <function name="insert" action="push"/>
    </access>
  </container>
  <container id="qtQueue" startPattern="QQueue &lt;" inherits="qtContainer">
    <access>
      <function name="at" yields="at_index"/>
      <function name="emplace" action="push" yields="item"/>
      <function name="push_front" action="push"/>
      <function name="push_back" action="push"/>
      <function name="pop_back" action="pop"/>
      <function name="pop_front" action="pop"/>
      <function name="front" yields="item"/>
      <function name="back" yields="item"/>
    </access>
  </container>
  <type-checks>
    <unusedvar>
      <suppress>QApplication</suppress>
      <suppress>QMutexLocker</suppress>
      <check>QRect</check>
      <check>QRectF</check>
      <check>QSize</check>
      <check>QSizeF</check>
      <check>QPoint</check>
      <check>QPointF</check>
      <check>QRegion</check>
      <check>QTransform</check>
    </unusedvar>
    <operatorEqVarError>
      <suppress>QMutex</suppress>
      <suppress>QRecursiveMutex</suppress>
      <suppress>QSemaphore</suppress>
      <suppress>QReadWriteLock</suppress>
    </operatorEqVarError>
  </type-checks>
  <!-- Treat QStringList as QList<QString> since we can't remove the template parameter when we inherit. -->
  <define name="QStringList" value="QList&lt;QString&gt;"/>
  <define name="Q_ARG(type, data)" value="QArgument&lt;type &gt;(#type, data)"/>
  <define name="Q_D(Class)" value="Class##Private * const d = d_func()"/>
  <define name="Q_CLASSINFO(Name, Value)" value=""/>
  <define name="Q_EXPORT_PLUGIN(ClassName)" value=""/>
  <define name="Q_EXPORT_PLUGIN2(a,ClassName)" value=""/>
  <define name="Q_IMPORT_PLUGIN(ClassName)" value=""/>
  <define name="Q_IMPORT_PLUGIN2(a,ClassName)" value=""/>
  <define name="Q_GLOBAL_STATIC(Type,VariableName)" value=""/>
  <define name="Q_GLOBAL_STATIC_WITH_ARGS(Type,VariableName,Arguments)" value=""/>
  <define name="Q_NAMESPACE_EXPORT" value=""/>
  <define name="Q_DECL_EXPORT" value=""/>
  <define name="Q_DECL_IMPORT" value=""/>
  <define name="QT_DECLARE_DEPRECATED_TR_FUNCTIONS(context)" value="static inline QString trUtf8(const char *sourceText, const char *disambiguation = Q_NULLPTR, int n = -1) { return QCoreApplication::translate(#context, sourceText, disambiguation, n); }"/>
  <define name="Q_DECLARE_FLAGS(x,y)" value=""/>
  <define name="Q_DECLARE_SEQUENTIAL_ITERATOR(C)" value=""/>
  <define name="Q_DECLARE_MUTABLE_SEQUENTIAL_ITERATOR(C)" value=""/>
  <define name="Q_DECLARE_ASSOCIATIVE_ITERATOR(C)" value=""/>
  <define name="Q_DECLARE_MUTABLE_ASSOCIATIVE_ITERATOR(C)" value=""/>
  <define name="Q_DECLARE_INTERFACE(ClassName,Identifier)" value=""/>
  <define name="Q_DECLARE_LOGGING_CATEGORY(name)" value="extern const QLoggingCategory &amp;name();"/>
  <define name="Q_DECLARE_OPERATORS_FOR_FLAGS(x)" value=""/>
  <define name="Q_DECLARE_METATYPE(x)" value=""/>
  <define name="Q_DECLARE_BUILTIN_METATYPE(TYPE, NAME)" value=""/>
  <define name="Q_DECLARE_PRIVATE(Class)" value="inline Class##Private* d_func() { return reinterpret_cast&lt;Class##Private*&gt;(qGetPtrHelper(d_ptr)); } inline const Class##Private d_func() const { return reinterpret_cast&lt;const Class##Private *&gt;(qGetPtrHelper(d_ptr)); } friend class Class##Private;"/>
  <define name="Q_DECLARE_PRIVATE_D(Dptr, Class)" value="inline Class##Private* d_func() { return reinterpret_cast&lt;Class##Private *&gt;(qGetPtrHelper(Dptr)); } inline const Class##Private* d_func() const { return reinterpret_cast&lt;const Class##Private *&gt;(qGetPtrHelper(Dptr)); } friend class Class##Private;"/>
  <define name="Q_DECLARE_PUBLIC(Class)" value="inline Class##Public* d_func() { return reinterpret_cast&lt;Class##Public*&gt;(qGetPtrHelper(d_ptr)); } inline const Class##Public d_func() const { return reinterpret_cast&lt;const Class##Public *&gt;(qGetPtrHelper(d_ptr)); } friend class Class##Public;"/>
  <define name="Q_DECLARE_TR_FUNCTIONS(context)" value="public: static inline QString tr(const char *sourceText, const char *disambiguation = Q_NULLPTR, int n = -1) { return QCoreApplication::translate(#context, sourceText, disambiguation, n); } QT_DECLARE_DEPRECATED_TR_FUNCTIONS(context) private:"/>
  <define name="Q_DISABLE_COPY(C)" value="C(C&amp;);C&amp; operator=(const C&amp;);"/>
  <define name="Q_EMIT" value=""/>
  <define name="Q_ENUM(X)" value=""/>
  <define name="Q_ENUMS(X)" value=""/>
  <define name="Q_ENUM_NS(X)" value=""/>
  <define name="Q_FLAG(X)" value=""/>
  <define name="Q_FLAGS(X)" value=""/>
  <define name="Q_FLAG_NS(X)" value=""/>
  <define name="Q_FOREVER" value="for (;;)"/>
  <define name="Q_INTERFACES(X)" value=""/>
  <define name="Q_LIKELY(expr)" value="expr"/>
  <define name="Q_NAMESPACE" value=""/>
  <define name="Q_NULLPTR" value="NULL"/>
  <define name="Q_OBJECT" value="static void qt_static_metacall(QObject*,QMetaObject::Call,int,void**);const MetaObject* metaObject() const;void* qt_metacast(const char*);int qt_metacall(QMetaObject::Call,int,void**);"/>
  <define name="Q_PRIVATE_SLOT(d, signature)" value=""/>
  <define name="Q_SLOTS" value=""/>
  <!-- Treat as variadic macro to avoid preprocessorErrorDirective -->
  <define name="Q_PROPERTY(...)" value=""/>
  <define name="Q_Q(Class)" value="Class * const q = q_func()"/>
  <define name="Q_RETURN_ARG(type, data)" value="QReturnArgument&lt;type &gt;(#type, data)"/>
  <define name="Q_UNLIKELY(expr)" value="expr"/>
  <define name="Q_UNUSED(X)" value="(void)(X);"/>
  <define name="QT_BEGIN_NAMESPACE" value=""/>
  <define name="QT_END_NAMESPACE" value=""/>
  <define name="QT_TR_NOOP(x)" value="x"/>
  <define name="QT_TR_NOOP_UTF8(x)" value="x"/>
  <define name="QT_TRANSLATE_NOOP(scope, x)" value="x"/>
  <define name="QT_TRANSLATE_NOOP_UTF8(scope, x)" value="x"/>
  <define name="QT_TRANSLATE_NOOP3(scope, x, comment)" value="{x, comment}"/>
  <define name="QT_TRANSLATE_NOOP3_UTF8(scope, x, comment)" value="{x, comment}"/>
  <define name="QT_WARNING_PUSH" value=""/>
  <define name="QT_WARNING_POP" value=""/>
  <define name="QT_STRINGIFY(x)" value="#x"/>
  <define name="QCOMPARE(actual, expected)" value="(void)((actual)==(expected))"/>
  <define name="QVERIFY(condition)" value="(void)(condition)"/>
  <define name="QVERIFY2(condition, message)" value="(void)(condition),(void)(message)"/>
  <define name="QBENCHMARK_ONCE" value=""/>
  <define name="QBENCHMARK" value=""/>
  <define name="QTRY_COMPARE(actual, expected)" value="(void)((actual)==(expected))"/>
  <define name="QTRY_COMPARE_WITH_TIMEOUT(actual, expected, timeout)" value="(void)((actual)==(expected))"/>
  <define name="QTRY_VERIFY2(condition, message)" value="(void)(condition),(void)(message)"/>
  <define name="QTRY_VERIFY(condition)" value="(void)(condition)"/>
  <define name="QTRY_VERIFY2_WITH_TIMEOUT(condition, message, timeout)" value="(void)(condition),(void)(message),(void)(timeout)"/>
  <define name="QTRY_VERIFY_WITH_TIMEOUT(condition, timeout)" value="(void)(condition),(void)(timeout)"/>
  <define name="QTEST_GUILESS_MAIN(testclass)" value=""/>
  <define name="QTEST_APPLESS_MAIN(testclass)" value=""/>
  <define name="QTEST_MAIN(testclass)" value=""/>
  <define name="Q_CONSTRUCTOR_FUNCTION0(AFUNC)" value=""/>
  <define name="Q_CONSTRUCTOR_FUNCTION(AFUNC)" value=""/>
  <define name="Q_FOREACH(A,B)" value="for(A:B)"/>
  <define name="foreach(A,B)" value="for(A:B)"/>
  <define name="forever" value="for (;;)"/>
  <define name="emit" value=""/>
  <define name="slots" value=""/>
  <define name="signals" value="protected"/>
  <define name="Q_SIGNALS" value="protected"/>
  <define name="Q_OVERRIDE(x)" value=""/>
  <define name="Q_PLUGIN_METADATA(x)" value=""/>
  <define name="Q_ASSERT(condition)" value="assert(condition)"/>
  <define name="Q_ASSERT_X(condition, where, what)" value="assert(condition)"/>
  <define name="QTC_ASSERT_STRINGIFY_HELPER(x)" value="#x"/>
  <define name="QTC_ASSERT_STRINGIFY(x)" value="QTC_ASSERT_STRINGIFY_HELPER(x)"/>
  <define name="QTC_ASSERT_STRING(cond)" value="::Utils::writeAssertLocation( &quot;\&quot;&quot; cond&quot;\&quot; in file &quot; __FILE__ &quot;, line &quot; QTC_ASSERT_STRINGIFY(__LINE__))"/>
  <define name="QTC_ASSERT(cond," value="action) if (Q_LIKELY(cond)) {} else { QTC_ASSERT_STRING(#cond); action; } do {} while (0)"/>
  <define name="QTC_CHECK(cond)" value="if (Q_LIKELY(cond)) {} else { QTC_ASSERT_STRING(#cond); } do {} while (0)"/>
  <define name="QTC_GUARD(cond)" value="((Q_LIKELY(cond)) ? true : (QTC_ASSERT_STRING(#cond), false))"/>
  <define name="Q_LOGGING_CATEGORY(name,...)" value=""/>
  <define name="QT_FORWARD_DECLARE_CLASS(name)" value="class name;"/>
  <define name="QT_FORWARD_DECLARE_STRUCT(name)" value="struct name;"/>
  <!-- https://doc.qt.io/qt-5/qstring.html#QStringLiteral -->
  <define name="QStringLiteral(str)" value="QString::fromUtf8(str, sizeof(str) - 1)"/>
  <define name="QByteArrayLiteral(str)" value="QByteArray(str)"/>
  <!-- https://doc.qt.io/qt-5/qtglobal.html#qreal-typedef -->
  <define name="qreal" value="double"/>
  <podtype name="qint8" sign="s" size="1"/>
  <podtype name="qint16" sign="s" size="2"/>
  <podtype name="qint32" sign="s" size="4"/>
  <podtype name="qint64" sign="s" size="8"/>
  <podtype name="quint8" sign="u" size="1"/>
  <podtype name="quint16" sign="u" size="2"/>
  <podtype name="quint32" sign="u" size="4"/>
  <podtype name="quint64" sign="u" size="8"/>
  <!-- https://doc.qt.io/qt-5/qtglobal.html#qlonglong-typedef "This is the same as qint64." -->
  <podtype name="qlonglong" sign="s" size="8"/>
  <!-- https://doc.qt.io/qt-5/qtglobal.html#qulonglong-typedef "This is the same as quint64." -->
  <podtype name="qulonglong" sign="u" size="8"/>
  <podtype name="uchar" sign="u" size="1"/>
  <podtype name="uint" sign="u"/>
  <podtype name="ulong" sign="u"/>
  <podtype name="ushort" sign="u"/>
  <podtype name="qptrdiff" sign="s"/>
  <podtype name="qintptr" sign="s"/>
  <podtype name="quintptr" sign="u"/>
  <!-- ##### Smart Pointers ##### -->
  <!-- https://wiki.qt.io/Smart_Pointers -->
  <smart-pointer class-name="QSharedDataPointer"/>
  <smart-pointer class-name="QExplicitlySharedDataPointer"/>
  <smart-pointer class-name="QSharedPointer"/>
  <smart-pointer class-name="QWeakPointer"/>
  <smart-pointer class-name="QPointer"/>
  <smart-pointer class-name="QScopedPointer">
    <unique/>
  </smart-pointer>
  <smart-pointer class-name="QScopedArrayPointer">
    <unique/>
  </smart-pointer>
  <!-- Internal Smart Pointers -->
  <smart-pointer class-name="QtPatternist::AutoPtr"/>
  <smart-pointer class-name="QGuard"/>
</def>
