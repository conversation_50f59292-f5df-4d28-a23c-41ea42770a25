<?xml version="1.0"?>
<def format="2">
  <!-- Ruby C API. see https://docs.ruby-lang.org/en/2.5.0/extension_rdoc.html -->
  <!-- INT2NUM() is a macro resulting in something like this: VALUE INT2NUM(int v) -->
  <!-- UINT2NUM() is something like this: VALUE UINT2NUM(unsigned int v) -->
  <!-- LONG2NUM() is something like this: VALUE LONG2NUM(long v) -->
  <!-- ULONG2NUM() is something like this: VALUE ULONG2NUM(unsigned long v) -->
  <!-- LL2NUM() is something like this: VALUE LL2NUM(long long v) -->
  <!-- ULL2NUM() is something like this: VALUE ULL2NUM(unsigned long long v) -->
  <function name="INT2NUM,UINT2NUM,LONG2NUM,ULONG2NUM,LL2NUM,ULL2NUM">
    <noreturn>false</noreturn>
    <returnValue type="VALUE"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- NUM2INT() is something like this: int NUM2INT(VALUE v) -->
  <!-- FIX2INT() is something like this: int FIX2INT(VALUE v) -->
  <function name="NUM2INT,FIX2INT">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- NUM2UINT() is something like this: unsigned int NUM2UINT(VALUE v) -->
  <!-- FIX2UINT() is something like this: unsigned int FIX2UINT(VALUE v) -->
  <function name="NUM2UINT,FIX2UINT">
    <noreturn>false</noreturn>
    <returnValue type="unsigned int"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- NUM2LONG() is something like this: long NUM2LONG(VALUE v) -->
  <!-- FIX2LONG() is something like this: long FIX2LONG(VALUE v) -->
  <function name="NUM2LONG,FIX2LONG">
    <noreturn>false</noreturn>
    <returnValue type="long"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- NUM2ULONG() is something like this: unsigned long NUM2ULONG(VALUE v) -->
  <!-- FIX2ULONG() is something like this: unsigned long FIX2ULONG(VALUE v) -->
  <function name="NUM2ULONG,FIX2ULONG">
    <noreturn>false</noreturn>
    <returnValue type="unsigned long"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- NUM2LL() is something like this: long long NUM2LL(VALUE v) -->
  <function name="NUM2LL">
    <noreturn>false</noreturn>
    <returnValue type="long long"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- NUM2ULL()  is something like this: unsigned long long NUM2ULL(VALUE v) -->
  <function name="NUM2ULL">
    <noreturn>false</noreturn>
    <returnValue type="unsigned long long"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void rb_define_const(VALUE klass, const char *name, VALUE val)  -->
  <function name="rb_define_const">
    <leak-ignore/>
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-null/>
      <strz/>
    </arg>
    <arg nr="3">
      <not-uninit/>
    </arg>
  </function>
  <!-- void rb_define_method(VALUE klass, const char *name, VALUE (*func)(ANYARGS), int argc) -->
  <function name="rb_define_method">
    <leak-ignore/>
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-null/>
      <strz/>
    </arg>
    <arg nr="4">
      <not-bool/>
      <not-uninit/>
    </arg>
  </function>
  <function name="rb_intern">
    <leak-ignore/>
    <noreturn>false</noreturn>
    <returnValue type="ID"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <function name="rb_str_new2">
    <leak-ignore/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- void rb_raise(rb_eRuntimeError, const char *fmt, …)  -->
  <function name="rb_raise">
    <leak-ignore/>
    <noreturn>true</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <formatstr/>
      <not-null/>
    </arg>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <podtype name="ID"/>
  <podtype name="VALUE" sign="u"/>
</def>
