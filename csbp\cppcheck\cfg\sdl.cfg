<?xml version="1.0"?>
<def format="2">
  <!-- SDL (Simple DirectMedia Layer) Library Configuration https://libsdl.org/ -->
  <!-- ########## SDL Types ########## -->
  <podtype name="Sint8" sign="s" size="1"/>
  <podtype name="Uint8" sign="u" size="1"/>
  <podtype name="Sint16" sign="s" size="2"/>
  <podtype name="Uint16" sign="u" size="2"/>
  <podtype name="Sint32" sign="s" size="4"/>
  <podtype name="Uint32" sign="u" size="4"/>
  <podtype name="Sint64" sign="s" size="8"/>
  <podtype name="Uint64" sign="u" size="8"/>
  <!-- ########## SDL defines / macros ########## -->
  <!-- From https://hg.libsdl.org/SDL/file/default/include/SDL_stdinc.h -->
  <define name="SDL_arraysize(array)" value="(sizeof(array)/sizeof(array[0]))"/>
  <define name="SDL_TABLESIZE(table)" value="SDL_arraysize(table)"/>
  <define name="SDL_min(x, y)" value="(((x) &lt; (y)) ? (x) : (y))"/>
  <define name="SDL_max(x, y)" value="(((x) &gt; (y)) ? (x) : (y))"/>
  <define name="SDL_zero(x)" value="SDL_memset(&amp;(x), 0, sizeof((x)))"/>
  <define name="SDL_zerop(x)" value="SDL_memset((x), 0, sizeof(*(x)))"/>
  <define name="SDL_zeroa(x)" value="SDL_memset((x), 0, sizeof((x)))"/>
  <define name="SDL_malloc" value="malloc"/>
  <define name="SDL_calloc" value="calloc"/>
  <define name="SDL_realloc" value="realloc"/>
  <define name="SDL_free" value="free"/>
  <define name="SDL_memset" value="memset"/>
  <define name="SDL_memcpy" value="memcpy"/>
  <define name="SDL_memmove" value="memmove"/>
  <define name="SDL_memcmp" value="memcmp"/>
  <define name="SDL_strlen" value="strlen"/>
  <define name="SDL_strlcpy" value="strlcpy"/>
  <define name="SDL_strlcat" value="strlcat"/>
  <define name="SDL_strdup" value="strdup"/>
  <define name="SDL_strchr" value="strchr"/>
  <define name="SDL_strrchr" value="strrchr"/>
  <define name="SDL_strstr" value="strstr"/>
  <define name="SDL_strcmp" value="strcmp"/>
  <define name="SDL_strncmp" value="strncmp"/>
  <define name="SDL_strcasecmp" value="strcasecmp"/>
  <define name="SDL_strncasecmp" value="strncasecmp"/>
  <define name="SDL_sscanf" value="sscanf"/>
  <define name="SDL_vsscanf" value="vsscanf"/>
  <define name="SDL_snprintf" value="snprintf"/>
  <define name="SDL_vsnprintf" value="vsnprintf"/>
  <!-- ########## SDL Functions ########## -->
  <!-- int SDL_BlitSurface(SDL_Surface*    src,
                           const SDL_Rect* srcrect,
                           SDL_Surface*    dst,
                           SDL_Rect*       dstrect) -->
  <function name="SDL_BlitSurface">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="out"/>
    <arg nr="4" direction="inout">
      <not-uninit/>
    </arg>
  </function>
  <!-- SDL_Surface* SDL_CreateRGBSurface(Uint32 flags,
                                         int    width,
                                         int    height,
                                         int    depth,
                                         Uint32 Rmask,
                                         Uint32 Gmask,
                                         Uint32 Bmask,
                                         Uint32 Amask) -->
  <function name="SDL_CreateRGBSurface">
    <noreturn>false</noreturn>
    <returnValue type="SDL_Surface*"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="5" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="6" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="7" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="8" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void SDL_Delay(Uint32 ms) -->
  <function name="SDL_Delay">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int SDL_FillRect(SDL_Surface*    dst,
                        const SDL_Rect* rect,
                        Uint32          color) -->
  <function name="SDL_FillRect">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- void SDL_FreeSurface(SDL_Surface *surface); -->
  <function name="SDL_FreeSurface">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int SDL_Init(Uint32 flags) -->
  <!-- int SDL_InitSubSystem(Uint32 flags) -->
  <function name="SDL_Init,SDL_InitSubSystem">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void SDL_Log(const char* fmt, ...) -->
  <function name="SDL_Log">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <formatstr/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <formatstr/>
    </arg>
  </function>
  <!-- void SDL_LogMessage(int             category,
                           SDL_LogPriority priority,
                           const char*     fmt,
                           ...) -->
  <function name="SDL_LogMessage">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <formatstr/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <formatstr/>
    </arg>
  </function>
  <!-- int SDL_LockSurface(SDL_Surface* surface) -->
  <function name="SDL_LockSurface">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- Uint32 SDL_MapRGB(const SDL_PixelFormat* format,
                         Uint8                  r, 
                         Uint8                  g, 
                         Uint8                  b) -->
  <function name="SDL_MapRGB">
    <noreturn>false</noreturn>
    <returnValue type="Uint32"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- SDL_bool SDL_MUSTLOCK(SDL_Surface* surface) -->
  <function name="SDL_MUSTLOCK">
    <noreturn>false</noreturn>
    <returnValue type="SDL_bool"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <function name="SDL_mutexP">
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
    </arg>
  </function>
  <function name="SDL_mutexV">
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
    </arg>
  </function>
  <!-- void SDL_Quit(void) -->
  <function name="SDL_Quit">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
  </function>
  <!-- void SDL_QuitSubSystem(Uint32 flags) -->
  <function name="SDL_QuitSubSystem">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- Sint64 SDL_RWsize(SDL_RWops* context) -->
  <function name="SDL_RWsize">
    <noreturn>false</noreturn>
    <returnValue type="Sint64"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1">
      <not-bool/>
      <not-null/>
    </arg>
  </function>
  <!-- size_t SDL_RWread(struct SDL_RWops* context,
                         void*             ptr,
                         size_t            size,
                         size_t            maxnum) -->
  <function name="SDL_RWread">
    <noreturn>false</noreturn>
    <returnValue type="size_t"/>
    <leak-ignore/>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-bool/>
      <not-null/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int SDL_SetError(const char* fmt, ...) -->
  <function name="SDL_SetError">
    <noreturn>false</noreturn>
    <returnValue type="int">-1</returnValue>
    <formatstr/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <formatstr/>
    </arg>
  </function>
  <!-- void SDL_UnlockSurface(SDL_Surface* surface) -->
  <function name="SDL_UnlockSurface">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <function name="IMG_isPNG">
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
    </arg>
  </function>
  <function name="IMG_isJPG">
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
    </arg>
  </function>
  <!-- ########## SDL Memory/Resource Allocation/Deallocation ########## -->
  <resource>
    <dealloc>SDL_FreeSurface</dealloc>
    <alloc init="true">SDL_CreateRGBSurface</alloc>
    <alloc init="true">SDL_CreateRGBSurfaceFrom</alloc>
    <alloc init="true">SDL_ConvertSurface</alloc>
    <alloc init="true">TTF_RenderUTF8_Blended</alloc>
    <alloc init="true">IMG_LoadPNG_RW</alloc>
    <alloc init="true">IMG_LoadJPG_RW</alloc>
    <alloc init="true">IMG_Load</alloc>
  </resource>
  <resource>
    <dealloc>SDL_DestroyMutex</dealloc>
    <alloc init="true">SDL_CreateMutex</alloc>
  </resource>
  <resource>
    <dealloc>SDL_WaitThread</dealloc>
    <alloc init="true">SDL_CreateThread</alloc>
  </resource>
  <resource>
    <dealloc>SDL_RWclose</dealloc>
    <alloc init="true">SDL_RWFromFile</alloc>
  </resource>
  <resource>
    <dealloc>SDL_FreeRW</dealloc>
    <alloc init="true">SDL_AllocRW</alloc>
  </resource>
  <resource>
    <dealloc>Mix_FreeMusic</dealloc>
    <alloc init="true">Mix_LoadMUSType_RW</alloc>
  </resource>
</def>
