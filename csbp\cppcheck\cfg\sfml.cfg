<?xml version="1.0"?>
<def format="2">
  <function name="sf::err">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <use-retval/>
  </function>
  <function name="sf::sleep">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <function name="sf::Time::asSeconds">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <use-retval/>
  </function>
  <function name="sf::Time::asMilliseconds">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <use-retval/>
  </function>
  <function name="sf::Time::asMicroseconds">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <use-retval/>
  </function>
  <function name="sf::String::clear">
    <noreturn>false</noreturn>
    <leak-ignore/>
  </function>
  <function name="sf::String::isEmpty">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <function name="sf::String::getSize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::size_t"/>
    <use-retval/>
  </function>
  <function name="sf::String::find">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" default="0">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <function name="sf::String::substring">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <use-retval/>
    <arg nr="1">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" default="0">
      <not-uninit/>
    </arg>
  </function>
  <function name="sf::String::erase">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" default="1">
      <not-uninit/>
    </arg>
  </function>
  <function name="sf::RenderTarget::clear,sf::RenderWindow::clear,sf::RenderTexture::clear">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" default="">
      <not-uninit/>
    </arg>
  </function>
  <function name="sf::RenderTarget::setView,sf::RenderWindow::setView,sf::RenderTexture::setView">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <function name="sf::RenderTarget::getView,sf::RenderTarget::getDefaultView,sf::RenderWindow::getView,sf::RenderWindow::getDefaultView,sf::RenderTexture::getView,sf::RenderTexture::getDefaultView">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <use-retval/>
  </function>
  <function name="sf::RenderTarget::getViewport,sf::RenderWindow::getViewport,sf::RenderTexture::getViewport">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <use-retval/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <function name="sf::RenderTarget::mapPixelToCoords,sf::RenderTarget::mapCoordsToPixel,sf::RenderWindow::mapPixelToCoords,sf::RenderWindow::mapCoordsToPixel,sf::RenderTexture::mapPixelToCoords,sf::RenderTexture::mapCoordsToPixel">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <use-retval/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" default="">
      <not-uninit/>
    </arg>
  </function>
  <function name="sf::RenderTarget::draw,sf::RenderWindow::draw,sf::RenderTexture::draw">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" default="">
      <not-uninit/>
    </arg>
    <arg nr="3" default="">
      <not-uninit/>
    </arg>
    <arg nr="4" default="">
      <not-uninit/>
    </arg>
  </function>
  <function name="sf::RenderTarget::getSize,sf::RenderWindow::getSize,sf::RenderTexture::getSize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <use-retval/>
  </function>
  <function name="sf::RenderTarget::setActive,sf::RenderWindow::setActive,sf::RenderTexture::setActive,sf::Window::setActive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" default="true">
      <not-uninit/>
    </arg>
  </function>
  <function name="sf::RenderTarget::pushGLStates,sf::RenderTarget::popGLStates,sf::RenderTarget::resetGLStates,sf::RenderWindow::pushGLStates,sf::RenderWindow::popGLStates,sf::RenderWindow::resetGLStates,sf::RenderTexture::pushGLStates,sf::RenderTexture::popGLStates,sf::RenderTexture::resetGLStates">
    <noreturn>false</noreturn>
    <leak-ignore/>
  </function>
  <function name="sf::Window::create,sf::RenderWindow::create">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" default="">
      <not-uninit/>
    </arg>
    <arg nr="3" default="">
      <not-uninit/>
    </arg>
    <arg nr="4" default="">
      <not-uninit/>
    </arg>
  </function>
  <function name="sf::Window::close,sf::RenderWindow::close">
    <noreturn>false</noreturn>
    <leak-ignore/>
  </function>
  <function name="sf::Window::isOpen,sf::RenderWindow::isOpen">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <use-retval/>
    <returnValue type="bool"/>
  </function>
  <function name="sf::Window::getSettings,sf::RenderWindow::getSettings">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <use-retval/>
  </function>
  <function name="sf::Window::pollEvent,sf::Window::waitEvent,sf::RenderWindow::pollEvent,sf::RenderWindow::waitEvent">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1"/>
  </function>
  <function name="sf::Window::getPosition,sf::RenderWindow::getPosition">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <use-retval/>
  </function>
  <function name="sf::Window::setPosition,sf::RenderWindow::setPosition">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <function name="sf::Window::getSize,sf::RenderWindow::getSize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <use-retval/>
  </function>
  <function name="sf::Window::setSize,sf::RenderWindow::setSize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <function name="sf::Window::setTitle,sf::RenderWindow::setTitle">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <function name="sf::Window::setIcon,sf::RenderWindow::setIcon">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
    <arg nr="3">
      <not-uninit/>
      <not-null/>
    </arg>
  </function>
  <function name="sf::Window::setVisible,sf::Window::setVerticalSyncEnabled,sf::Window::setMouseCursorVisible,sf::Window::setMouseCursorGrabbed,sf::Window::setKeyRepeatEnabled,sf::RenderWindow::setVisible,sf::RenderWindow::setVerticalSyncEnabled,sf::RenderWindow::setMouseCursorVisible,sf::RenderWindow::setMouseCursorGrabbed,sf::RenderWindow::setKeyRepeatEnabled">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <function name="sf::Window::setFramerateLimit,sf::RenderWindow::setFramerateLimit">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <function name="sf::Window::setJoystickThreshold,sf::RenderWindow::setJoystickThreshold">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
      <valid>0:100</valid>
    </arg>
  </function>
  <function name="sf::Window::requestFocus,sf::RenderWindow::requestFocus">
    <noreturn>false</noreturn>
    <leak-ignore/>
  </function>
  <function name="sf::Window::hasFocus,sf::RenderWindow::hasFocus">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <use-retval/>
    <returnValue type="bool"/>
  </function>
  <function name="sf::Window::display,sf::RenderWindow::display">
    <noreturn>false</noreturn>
    <leak-ignore/>
  </function>
  <function name="sf::Window::getSystemHandle,sf::RenderWindow::getSystemHandle">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <use-retval/>
  </function>
  <container id="sfmlString" startPattern="sf::String" endPattern="" opLessAllowed="true">
    <type string="std-like"/>
    <size>
      <function name="replace" action="change"/>
      <function name="insert" action="change"/>
      <function name="erase" action="change"/>
      <function name="isEmpty" yields="empty"/>
      <function name="getSize" yields="size"/>
      <function name="clear" action="clear"/>
    </size>
    <access indexOperator="array-like">
      <function name="begin" yields="start-iterator"/>
      <function name="end" yields="end-iterator"/>
      <function name="find" action="find"/>
      <function name="getData" yields="buffer-nt"/>
    </access>
  </container>
  <podtype name="sf::Int8" sign="s" size="1"/>
  <podtype name="sf::Uint8" sign="u" size="1"/>
  <podtype name="sf::Int16" sign="s" size="2"/>
  <podtype name="sf::Uint16" sign="u" size="2"/>
  <podtype name="sf::Int32" sign="s" size="4"/>
  <podtype name="sf::Uint32" sign="u" size="4"/>
  <podtype name="sf::Int64" sign="s" size="8"/>
  <podtype name="sf::Uint64" sign="u" size="8"/>
</def>
