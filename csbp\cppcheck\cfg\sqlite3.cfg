<?xml version="1.0"?>
<def format="2">
  <!-- SQLite Library Configuration -->
  <!-- C API Reference: https://www.sqlite.org/capi3ref.html -->
  <!-- ########## SQLite Types ########## -->
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_int64 -->
  <podtype name="sqlite3_int64" sign="s" size="8"/>
  <podtype name="sqlite_int64" sign="s" size="8"/>
  <podtype name="sqlite3_uint64" sign="u" size="8"/>
  <podtype name="sqlite_uint64" sign="u" size="8"/>
  <!-- ########## SQLite values / defines ########## -->
  <!-- Result Codes -->
  <define name="SQLITE_OK" value="0"/>
  <!-- beginning-of-error-codes -->
  <define name="SQLITE_ERROR" value="1"/>
  <define name="SQLITE_INTERNAL" value="2"/>
  <define name="SQLITE_PERM" value="3"/>
  <define name="SQLITE_ABORT" value="4"/>
  <define name="SQLITE_BUSY" value="5"/>
  <define name="SQLITE_LOCKED" value="6"/>
  <define name="SQLITE_NOMEM" value="7"/>
  <define name="SQLITE_READONLY" value="8"/>
  <define name="SQLITE_INTERRUPT" value="9"/>
  <define name="SQLITE_IOERR" value="10"/>
  <define name="SQLITE_CORRUPT" value="11"/>
  <define name="SQLITE_NOTFOUND" value="12"/>
  <define name="SQLITE_FULL" value="13"/>
  <define name="SQLITE_CANTOPEN" value="14"/>
  <define name="SQLITE_PROTOCOL" value="15"/>
  <define name="SQLITE_EMPTY" value="16"/>
  <define name="SQLITE_SCHEMA" value="17"/>
  <define name="SQLITE_TOOBIG" value="18"/>
  <define name="SQLITE_CONSTRAINT" value="19"/>
  <define name="SQLITE_MISMATCH" value="20"/>
  <define name="SQLITE_MISUSE" value="21"/>
  <define name="SQLITE_NOLFS" value="22"/>
  <define name="SQLITE_AUTH" value="23"/>
  <define name="SQLITE_FORMAT" value="24"/>
  <define name="SQLITE_RANGE" value="25"/>
  <define name="SQLITE_NOTADB" value="26"/>
  <define name="SQLITE_NOTICE" value="27"/>
  <define name="SQLITE_WARNING" value="28"/>
  <define name="SQLITE_ROW" value="100"/>
  <define name="SQLITE_DONE" value="101"/>
  <!-- end-of-error-codes -->
  <!-- Extended Result Codes -->
  <define name="SQLITE_ERROR_MISSING_COLLSEQ" value="(SQLITE_ERROR | (1&lt;&lt;8))"/>
  <define name="SQLITE_ERROR_RETRY" value="(SQLITE_ERROR | (2&lt;&lt;8))"/>
  <define name="SQLITE_ERROR_SNAPSHOT" value="(SQLITE_ERROR | (3&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_READ" value="(SQLITE_IOERR | (1&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_SHORT_READ" value="(SQLITE_IOERR | (2&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_WRITE" value="(SQLITE_IOERR | (3&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_FSYNC" value="(SQLITE_IOERR | (4&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_DIR_FSYNC" value="(SQLITE_IOERR | (5&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_TRUNCATE" value="(SQLITE_IOERR | (6&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_FSTAT" value="(SQLITE_IOERR | (7&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_UNLOCK" value="(SQLITE_IOERR | (8&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_RDLOCK" value="(SQLITE_IOERR | (9&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_DELETE" value="(SQLITE_IOERR | (10&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_BLOCKED" value="(SQLITE_IOERR | (11&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_NOMEM" value="(SQLITE_IOERR | (12&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_ACCESS" value="(SQLITE_IOERR | (13&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_CHECKRESERVEDLOCK" value="(SQLITE_IOERR | (14&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_LOCK" value="(SQLITE_IOERR | (15&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_CLOSE" value="(SQLITE_IOERR | (16&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_DIR_CLOSE" value="(SQLITE_IOERR | (17&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_SHMOPEN" value="(SQLITE_IOERR | (18&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_SHMSIZE" value="(SQLITE_IOERR | (19&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_SHMLOCK" value="(SQLITE_IOERR | (20&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_SHMMAP" value="(SQLITE_IOERR | (21&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_SEEK" value="(SQLITE_IOERR | (22&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_DELETE_NOENT" value="(SQLITE_IOERR | (23&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_MMAP" value="(SQLITE_IOERR | (24&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_GETTEMPPATH" value="(SQLITE_IOERR | (25&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_CONVPATH" value="(SQLITE_IOERR | (26&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_VNODE" value="(SQLITE_IOERR | (27&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_AUTH" value="(SQLITE_IOERR | (28&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_BEGIN_ATOMIC" value="(SQLITE_IOERR | (29&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_COMMIT_ATOMIC" value="(SQLITE_IOERR | (30&lt;&lt;8))"/>
  <define name="SQLITE_IOERR_ROLLBACK_ATOMIC" value="(SQLITE_IOERR | (31&lt;&lt;8))"/>
  <define name="SQLITE_LOCKED_SHAREDCACHE" value="(SQLITE_LOCKED |  (1&lt;&lt;8))"/>
  <define name="SQLITE_LOCKED_VTAB" value="(SQLITE_LOCKED |  (2&lt;&lt;8))"/>
  <define name="SQLITE_BUSY_RECOVERY" value="(SQLITE_BUSY   |  (1&lt;&lt;8))"/>
  <define name="SQLITE_BUSY_SNAPSHOT" value="(SQLITE_BUSY   |  (2&lt;&lt;8))"/>
  <define name="SQLITE_CANTOPEN_NOTEMPDIR" value="(SQLITE_CANTOPEN | (1&lt;&lt;8))"/>
  <define name="SQLITE_CANTOPEN_ISDIR" value="(SQLITE_CANTOPEN | (2&lt;&lt;8))"/>
  <define name="SQLITE_CANTOPEN_FULLPATH" value="(SQLITE_CANTOPEN | (3&lt;&lt;8))"/>
  <define name="SQLITE_CANTOPEN_CONVPATH" value="(SQLITE_CANTOPEN | (4&lt;&lt;8))"/>
  <define name="SQLITE_CANTOPEN_DIRTYWAL" value="(SQLITE_CANTOPEN | (5&lt;&lt;8))"/>
  <define name="SQLITE_CORRUPT_VTAB" value="(SQLITE_CORRUPT | (1&lt;&lt;8))"/>
  <define name="SQLITE_CORRUPT_SEQUENCE" value="(SQLITE_CORRUPT | (2&lt;&lt;8))"/>
  <define name="SQLITE_READONLY_RECOVERY" value="(SQLITE_READONLY | (1&lt;&lt;8))"/>
  <define name="SQLITE_READONLY_CANTLOCK" value="(SQLITE_READONLY | (2&lt;&lt;8))"/>
  <define name="SQLITE_READONLY_ROLLBACK" value="(SQLITE_READONLY | (3&lt;&lt;8))"/>
  <define name="SQLITE_READONLY_DBMOVED" value="(SQLITE_READONLY | (4&lt;&lt;8))"/>
  <define name="SQLITE_READONLY_CANTINIT" value="(SQLITE_READONLY | (5&lt;&lt;8))"/>
  <define name="SQLITE_READONLY_DIRECTORY" value="(SQLITE_READONLY | (6&lt;&lt;8))"/>
  <define name="SQLITE_ABORT_ROLLBACK" value="(SQLITE_ABORT | (2&lt;&lt;8))"/>
  <define name="SQLITE_CONSTRAINT_CHECK" value="(SQLITE_CONSTRAINT | (1&lt;&lt;8))"/>
  <define name="SQLITE_CONSTRAINT_COMMITHOOK" value="(SQLITE_CONSTRAINT | (2&lt;&lt;8))"/>
  <define name="SQLITE_CONSTRAINT_FOREIGNKEY" value="(SQLITE_CONSTRAINT | (3&lt;&lt;8))"/>
  <define name="SQLITE_CONSTRAINT_FUNCTION" value="(SQLITE_CONSTRAINT | (4&lt;&lt;8))"/>
  <define name="SQLITE_CONSTRAINT_NOTNULL" value="(SQLITE_CONSTRAINT | (5&lt;&lt;8))"/>
  <define name="SQLITE_CONSTRAINT_PRIMARYKEY" value="(SQLITE_CONSTRAINT | (6&lt;&lt;8))"/>
  <define name="SQLITE_CONSTRAINT_TRIGGER" value="(SQLITE_CONSTRAINT | (7&lt;&lt;8))"/>
  <define name="SQLITE_CONSTRAINT_UNIQUE" value="(SQLITE_CONSTRAINT | (8&lt;&lt;8))"/>
  <define name="SQLITE_CONSTRAINT_VTAB" value="(SQLITE_CONSTRAINT | (9&lt;&lt;8))"/>
  <define name="SQLITE_CONSTRAINT_ROWID" value="(SQLITE_CONSTRAINT |(10&lt;&lt;8))"/>
  <define name="SQLITE_NOTICE_RECOVER_WAL" value="(SQLITE_NOTICE | (1&lt;&lt;8))"/>
  <define name="SQLITE_NOTICE_RECOVER_ROLLBACK" value="(SQLITE_NOTICE | (2&lt;&lt;8))"/>
  <define name="SQLITE_WARNING_AUTOINDEX" value="(SQLITE_WARNING | (1&lt;&lt;8))"/>
  <define name="SQLITE_AUTH_USER" value="(SQLITE_AUTH | (1&lt;&lt;8))"/>
  <define name="SQLITE_OK_LOAD_PERMANENTLY" value="(SQLITE_OK | (1&lt;&lt;8))"/>
  <!-- Flags for the xAccess VFS method -->
  <define name="SQLITE_ACCESS_EXISTS" value="0"/>
  <define name="SQLITE_ACCESS_READWRITE" value="1"/>
  <define name="SQLITE_ACCESS_READ" value="2"/>
  <!-- Authorizer Action Codes -->
  <define name="SQLITE_CREATE_INDEX" value="1"/>
  <define name="SQLITE_CREATE_TABLE" value="2"/>
  <define name="SQLITE_CREATE_TEMP_INDEX" value="3"/>
  <define name="SQLITE_CREATE_TEMP_TABLE" value="4"/>
  <define name="SQLITE_CREATE_TEMP_TRIGGER" value="5"/>
  <define name="SQLITE_CREATE_TEMP_VIEW" value="6"/>
  <define name="SQLITE_CREATE_TRIGGER" value="7"/>
  <define name="SQLITE_CREATE_VIEW" value="8"/>
  <define name="SQLITE_DELETE" value="9"/>
  <define name="SQLITE_DROP_INDEX" value="10"/>
  <define name="SQLITE_DROP_TABLE" value="11"/>
  <define name="SQLITE_DROP_TEMP_INDEX" value="12"/>
  <define name="SQLITE_DROP_TEMP_TABLE" value="13"/>
  <define name="SQLITE_DROP_TEMP_TRIGGER" value="14"/>
  <define name="SQLITE_DROP_TEMP_VIEW" value="15"/>
  <define name="SQLITE_DROP_TRIGGER" value="16"/>
  <define name="SQLITE_DROP_VIEW" value="17"/>
  <define name="SQLITE_INSERT" value="18"/>
  <define name="SQLITE_PRAGMA" value="19"/>
  <define name="SQLITE_READ" value="20"/>
  <define name="SQLITE_SELECT" value="21"/>
  <define name="SQLITE_TRANSACTION" value="22"/>
  <define name="SQLITE_UPDATE" value="23"/>
  <define name="SQLITE_ATTACH" value="24"/>
  <define name="SQLITE_DETACH" value="25"/>
  <define name="SQLITE_ALTER_TABLE" value="26"/>
  <define name="SQLITE_REINDEX" value="27"/>
  <define name="SQLITE_ANALYZE" value="28"/>
  <define name="SQLITE_CREATE_VTABLE" value="29"/>
  <define name="SQLITE_DROP_VTABLE" value="30"/>
  <define name="SQLITE_FUNCTION" value="31"/>
  <define name="SQLITE_SAVEPOINT" value="32"/>
  <define name="SQLITE_COPY" value="0"/>
  <define name="SQLITE_RECURSIVE" value="33"/>
  <!-- Text Encodings -->
  <define name="SQLITE_UTF8" value="1"/>
  <define name="SQLITE_UTF16LE" value="2"/>
  <define name="SQLITE_UTF16BE" value="3"/>
  <define name="SQLITE_UTF16" value="4"/>
  <define name="SQLITE_ANY" value="5"/>
  <define name="SQLITE_UTF16_ALIGNED" value="8"/>
  <!-- Fundamental Datatypes -->
  <define name="SQLITE_INTEGER" value="1"/>
  <define name="SQLITE_FLOAT" value="2"/>
  <define name="SQLITE_BLOB" value="4"/>
  <define name="SQLITE_NULL" value="5"/>
  <define name="SQLITE_TEXT" value="3"/>
  <define name="SQLITE3_TEXT" value="3"/>
  <!-- Checkpoint Mode Values -->
  <define name="SQLITE_CHECKPOINT_PASSIVE" value="0"/>
  <define name="SQLITE_CHECKPOINT_FULL" value="1"/>
  <define name="SQLITE_CHECKPOINT_RESTART" value="2"/>
  <define name="SQLITE_CHECKPOINT_TRUNCATE" value="3"/>
  <!-- Configuration Options -->
  <define name="SQLITE_CONFIG_SINGLETHREAD" value="1"/>
  <define name="SQLITE_CONFIG_MULTITHREAD" value="2"/>
  <define name="SQLITE_CONFIG_SERIALIZED" value="3"/>
  <define name="SQLITE_CONFIG_MALLOC" value="4"/>
  <define name="SQLITE_CONFIG_GETMALLOC" value="5"/>
  <define name="SQLITE_CONFIG_SCRATCH" value="6"/>
  <define name="SQLITE_CONFIG_PAGECACHE" value="7"/>
  <define name="SQLITE_CONFIG_HEAP" value="8"/>
  <define name="SQLITE_CONFIG_MEMSTATUS" value="9"/>
  <define name="SQLITE_CONFIG_MUTEX" value="10"/>
  <define name="SQLITE_CONFIG_GETMUTEX" value="11"/>
  <define name="SQLITE_CONFIG_CHUNKALLOC" value="12"/>
  <define name="SQLITE_CONFIG_LOOKASIDE" value="13"/>
  <define name="SQLITE_CONFIG_PCACHE" value="14"/>
  <define name="SQLITE_CONFIG_GETPCACHE" value="15"/>
  <define name="SQLITE_CONFIG_LOG" value="16"/>
  <define name="SQLITE_CONFIG_URI" value="17"/>
  <define name="SQLITE_CONFIG_PCACHE2" value="18"/>
  <define name="SQLITE_CONFIG_GETPCACHE2" value="19"/>
  <define name="SQLITE_CONFIG_COVERING_INDEX_SCAN" value="20"/>
  <define name="SQLITE_CONFIG_SQLLOG" value="21"/>
  <define name="SQLITE_CONFIG_MMAP_SIZE" value="22"/>
  <define name="SQLITE_CONFIG_WIN32_HEAPSIZE" value="23"/>
  <define name="SQLITE_CONFIG_PCACHE_HDRSZ" value="24"/>
  <define name="SQLITE_CONFIG_PMASZ" value="25"/>
  <define name="SQLITE_CONFIG_STMTJRNL_SPILL" value="26"/>
  <define name="SQLITE_CONFIG_SMALL_MALLOC" value="27"/>
  <define name="SQLITE_CONFIG_SORTERREF_SIZE" value="28"/>
  <define name="SQLITE_CONFIG_MEMDB_MAXSIZE" value="29"/>
  <!-- Database Connection Configuration Options -->
  <define name="SQLITE_DBCONFIG_MAINDBNAME" value="1000"/>
  <define name="SQLITE_DBCONFIG_LOOKASIDE" value="1001"/>
  <define name="SQLITE_DBCONFIG_ENABLE_FKEY" value="1002"/>
  <define name="SQLITE_DBCONFIG_ENABLE_TRIGGER" value="1003"/>
  <define name="SQLITE_DBCONFIG_ENABLE_FTS3_TOKENIZER" value="1004"/>
  <define name="SQLITE_DBCONFIG_ENABLE_LOAD_EXTENSION" value="1005"/>
  <define name="SQLITE_DBCONFIG_NO_CKPT_ON_CLOSE" value="1006"/>
  <define name="SQLITE_DBCONFIG_ENABLE_QPSG" value="1007"/>
  <define name="SQLITE_DBCONFIG_TRIGGER_EQP" value="1008"/>
  <define name="SQLITE_DBCONFIG_RESET_DATABASE" value="1009"/>
  <define name="SQLITE_DBCONFIG_DEFENSIVE" value="1010"/>
  <define name="SQLITE_DBCONFIG_MAX" value="1010"/>
  <!-- Authorizer Return Codes -->
  <define name="SQLITE_DENY" value="1"/>
  <define name="SQLITE_IGNORE" value="2"/>
  <!-- Flags for sqlite3_deserialize() -->
  <define name="SQLITE_DESERIALIZE_FREEONCLOSE" value="1"/>
  <define name="SQLITE_DESERIALIZE_RESIZEABLE" value="2"/>
  <define name="SQLITE_DESERIALIZE_READONLY" value="4"/>
  <!-- Conflict resolution modes -->
  <define name="SQLITE_ROLLBACK" value="1"/>
  <define name="SQLITE_FAIL" value="3"/>
  <define name="SQLITE_REPLACE" value="5"/>
  <!-- Standard File Control Opcodes -->
  <define name="SQLITE_FCNTL_LOCKSTATE" value="1"/>
  <define name="SQLITE_FCNTL_GET_LOCKPROXYFILE" value="2"/>
  <define name="SQLITE_FCNTL_SET_LOCKPROXYFILE" value="3"/>
  <define name="SQLITE_FCNTL_LAST_ERRNO" value="4"/>
  <define name="SQLITE_FCNTL_SIZE_HINT" value="5"/>
  <define name="SQLITE_FCNTL_CHUNK_SIZE" value="6"/>
  <define name="SQLITE_FCNTL_FILE_POINTER" value="7"/>
  <define name="SQLITE_FCNTL_SYNC_OMITTED" value="8"/>
  <define name="SQLITE_FCNTL_WIN32_AV_RETRY" value="9"/>
  <define name="SQLITE_FCNTL_PERSIST_WAL" value="10"/>
  <define name="SQLITE_FCNTL_OVERWRITE" value="11"/>
  <define name="SQLITE_FCNTL_VFSNAME" value="12"/>
  <define name="SQLITE_FCNTL_POWERSAFE_OVERWRITE" value="13"/>
  <define name="SQLITE_FCNTL_PRAGMA" value="14"/>
  <define name="SQLITE_FCNTL_BUSYHANDLER" value="15"/>
  <define name="SQLITE_FCNTL_TEMPFILENAME" value="16"/>
  <define name="SQLITE_FCNTL_MMAP_SIZE" value="18"/>
  <define name="SQLITE_FCNTL_TRACE" value="19"/>
  <define name="SQLITE_FCNTL_HAS_MOVED" value="20"/>
  <define name="SQLITE_FCNTL_SYNC" value="21"/>
  <define name="SQLITE_FCNTL_COMMIT_PHASETWO" value="22"/>
  <define name="SQLITE_FCNTL_WIN32_SET_HANDLE" value="23"/>
  <define name="SQLITE_FCNTL_WAL_BLOCK" value="24"/>
  <define name="SQLITE_FCNTL_ZIPVFS" value="25"/>
  <define name="SQLITE_FCNTL_RBU" value="26"/>
  <define name="SQLITE_FCNTL_VFS_POINTER" value="27"/>
  <define name="SQLITE_FCNTL_JOURNAL_POINTER" value="28"/>
  <define name="SQLITE_FCNTL_WIN32_GET_HANDLE" value="29"/>
  <define name="SQLITE_FCNTL_PDB" value="30"/>
  <define name="SQLITE_FCNTL_BEGIN_ATOMIC_WRITE" value="31"/>
  <define name="SQLITE_FCNTL_COMMIT_ATOMIC_WRITE" value="32"/>
  <define name="SQLITE_FCNTL_ROLLBACK_ATOMIC_WRITE" value="33"/>
  <define name="SQLITE_FCNTL_LOCK_TIMEOUT" value="34"/>
  <define name="SQLITE_FCNTL_DATA_VERSION" value="35"/>
  <define name="SQLITE_FCNTL_SIZE_LIMIT" value="36"/>
  <!-- Virtual Table Constraint Operator Codes -->
  <define name="SQLITE_INDEX_CONSTRAINT_EQ" value="2"/>
  <define name="SQLITE_INDEX_CONSTRAINT_GT" value="4"/>
  <define name="SQLITE_INDEX_CONSTRAINT_LE" value="8"/>
  <define name="SQLITE_INDEX_CONSTRAINT_LT" value="16"/>
  <define name="SQLITE_INDEX_CONSTRAINT_GE" value="32"/>
  <define name="SQLITE_INDEX_CONSTRAINT_MATCH" value="64"/>
  <define name="SQLITE_INDEX_CONSTRAINT_LIKE" value="65"/>
  <define name="SQLITE_INDEX_CONSTRAINT_GLOB" value="66"/>
  <define name="SQLITE_INDEX_CONSTRAINT_REGEXP" value="67"/>
  <define name="SQLITE_INDEX_CONSTRAINT_NE" value="68"/>
  <define name="SQLITE_INDEX_CONSTRAINT_ISNOT" value="69"/>
  <define name="SQLITE_INDEX_CONSTRAINT_ISNOTNULL" value="70"/>
  <define name="SQLITE_INDEX_CONSTRAINT_ISNULL" value="71"/>
  <define name="SQLITE_INDEX_CONSTRAINT_IS" value="72"/>
  <define name="SQLITE_INDEX_CONSTRAINT_FUNCTION" value="150"/>
  <!-- Device Characteristics -->
  <define name="SQLITE_IOCAP_ATOMIC" value="0x00000001"/>
  <define name="SQLITE_IOCAP_ATOMIC512" value="0x00000002"/>
  <define name="SQLITE_IOCAP_ATOMIC1K" value="0x00000004"/>
  <define name="SQLITE_IOCAP_ATOMIC2K" value="0x00000008"/>
  <define name="SQLITE_IOCAP_ATOMIC4K" value="0x00000010"/>
  <define name="SQLITE_IOCAP_ATOMIC8K" value="0x00000020"/>
  <define name="SQLITE_IOCAP_ATOMIC16K" value="0x00000040"/>
  <define name="SQLITE_IOCAP_ATOMIC32K" value="0x00000080"/>
  <define name="SQLITE_IOCAP_ATOMIC64K" value="0x00000100"/>
  <define name="SQLITE_IOCAP_SAFE_APPEND" value="0x00000200"/>
  <define name="SQLITE_IOCAP_SEQUENTIAL" value="0x00000400"/>
  <define name="SQLITE_IOCAP_UNDELETABLE_WHEN_OPEN" value="0x00000800"/>
  <define name="SQLITE_IOCAP_POWERSAFE_OVERWRITE" value="0x00001000"/>
  <define name="SQLITE_IOCAP_IMMUTABLE" value="0x00002000"/>
  <define name="SQLITE_IOCAP_BATCH_ATOMIC" value="0x00004000"/>
  <!-- File Locking Levels -->
  <define name="SQLITE_LOCK_NONE" value="0"/>
  <define name="SQLITE_LOCK_SHARED" value="1"/>
  <define name="SQLITE_LOCK_RESERVED" value="2"/>
  <define name="SQLITE_LOCK_PENDING" value="3"/>
  <define name="SQLITE_LOCK_EXCLUSIVE" value="4"/>
  <!-- Mutex Types -->
  <define name="SQLITE_MUTEX_FAST" value="0"/>
  <define name="SQLITE_MUTEX_RECURSIVE" value="1"/>
  <define name="SQLITE_MUTEX_STATIC_MASTER" value="2"/>
  <define name="SQLITE_MUTEX_STATIC_MEM" value="3"/>
  <define name="SQLITE_MUTEX_STATIC_MEM2" value="4"/>
  <define name="SQLITE_MUTEX_STATIC_OPEN" value="4"/>
  <define name="SQLITE_MUTEX_STATIC_PRNG" value="5"/>
  <define name="SQLITE_MUTEX_STATIC_LRU" value="6"/>
  <define name="SQLITE_MUTEX_STATIC_LRU2" value="7"/>
  <define name="SQLITE_MUTEX_STATIC_PMEM" value="7"/>
  <define name="SQLITE_MUTEX_STATIC_APP1" value="8"/>
  <define name="SQLITE_MUTEX_STATIC_APP2" value="9"/>
  <define name="SQLITE_MUTEX_STATIC_APP3" value="10"/>
  <define name="SQLITE_MUTEX_STATIC_VFS1" value="11"/>
  <define name="SQLITE_MUTEX_STATIC_VFS2" value="12"/>
  <define name="SQLITE_MUTEX_STATIC_VFS3" value="13"/>
  <!-- Flags For File Open Operations -->
  <define name="SQLITE_OPEN_READONLY" value="0x00000001"/>
  <define name="SQLITE_OPEN_READWRITE" value="0x00000002"/>
  <define name="SQLITE_OPEN_CREATE" value="0x00000004"/>
  <define name="SQLITE_OPEN_DELETEONCLOSE" value="0x00000008"/>
  <define name="SQLITE_OPEN_EXCLUSIVE" value="0x00000010"/>
  <define name="SQLITE_OPEN_AUTOPROXY" value="0x00000020"/>
  <define name="SQLITE_OPEN_URI" value="0x00000040"/>
  <define name="SQLITE_OPEN_MEMORY" value="0x00000080"/>
  <define name="SQLITE_OPEN_MAIN_DB" value="0x00000100"/>
  <define name="SQLITE_OPEN_TEMP_DB" value="0x00000200"/>
  <define name="SQLITE_OPEN_TRANSIENT_DB" value="0x00000400"/>
  <define name="SQLITE_OPEN_MAIN_JOURNAL" value="0x00000800"/>
  <define name="SQLITE_OPEN_TEMP_JOURNAL" value="0x00001000"/>
  <define name="SQLITE_OPEN_SUBJOURNAL" value="0x00002000"/>
  <define name="SQLITE_OPEN_MASTER_JOURNAL" value="0x00004000"/>
  <define name="SQLITE_OPEN_NOMUTEX" value="0x00008000"/>
  <define name="SQLITE_OPEN_FULLMUTEX" value="0x00010000"/>
  <define name="SQLITE_OPEN_SHAREDCACHE" value="0x00020000"/>
  <define name="SQLITE_OPEN_PRIVATECACHE" value="0x00040000"/>
  <define name="SQLITE_OPEN_WAL" value="0x00080000"/>
  <!-- Prepare Flags -->
  <define name="SQLITE_PREPARE_PERSISTENT" value="0x01"/>
  <define name="SQLITE_PREPARE_NORMALIZE" value="0x02"/>
  <define name="SQLITE_PREPARE_NO_VTAB" value="0x04"/>
  <!-- Prepared Statement Scan Status Opcodes -->
  <define name="SQLITE_SCANSTAT_NLOOP" value="0"/>
  <define name="SQLITE_SCANSTAT_NVISIT" value="1"/>
  <define name="SQLITE_SCANSTAT_EST" value="2"/>
  <define name="SQLITE_SCANSTAT_NAME" value="3"/>
  <define name="SQLITE_SCANSTAT_EXPLAIN" value="4"/>
  <define name="SQLITE_SCANSTAT_SELECTID" value="5"/>
  <!-- Flags for the xShmLock VFS method -->
  <define name="SQLITE_SHM_UNLOCK" value="1"/>
  <define name="SQLITE_SHM_LOCK" value="2"/>
  <define name="SQLITE_SHM_SHARED" value="4"/>
  <define name="SQLITE_SHM_EXCLUSIVE" value="8"/>
  <!-- Status Parameters -->
  <define name="SQLITE_STATUS_MEMORY_USED" value="0"/>
  <define name="SQLITE_STATUS_PAGECACHE_USED" value="1"/>
  <define name="SQLITE_STATUS_PAGECACHE_OVERFLOW" value="2"/>
  <define name="SQLITE_STATUS_SCRATCH_USED" value="3"/>
  <define name="SQLITE_STATUS_SCRATCH_OVERFLOW" value="4"/>
  <define name="SQLITE_STATUS_MALLOC_SIZE" value="5"/>
  <define name="SQLITE_STATUS_PARSER_STACK" value="6"/>
  <define name="SQLITE_STATUS_PAGECACHE_SIZE" value="7"/>
  <define name="SQLITE_STATUS_SCRATCH_SIZE" value="8"/>
  <define name="SQLITE_STATUS_MALLOC_COUNT" value="9"/>
  <!-- Synchronization Type Flags -->
  <define name="SQLITE_SYNC_NORMAL" value="0x00002"/>
  <define name="SQLITE_SYNC_FULL" value="0x00003"/>
  <define name="SQLITE_SYNC_DATAONLY" value="0x00010"/>
  <!-- Testing Interface Operation Codes -->
  <define name="SQLITE_TESTCTRL_FIRST" value="5"/>
  <define name="SQLITE_TESTCTRL_PRNG_SAVE" value="5"/>
  <define name="SQLITE_TESTCTRL_PRNG_RESTORE" value="6"/>
  <define name="SQLITE_TESTCTRL_PRNG_RESET" value="7"/>
  <define name="SQLITE_TESTCTRL_BITVEC_TEST" value="8"/>
  <define name="SQLITE_TESTCTRL_FAULT_INSTALL" value="9"/>
  <define name="SQLITE_TESTCTRL_BENIGN_MALLOC_HOOKS" value="10"/>
  <define name="SQLITE_TESTCTRL_PENDING_BYTE" value="11"/>
  <define name="SQLITE_TESTCTRL_ASSERT" value="12"/>
  <define name="SQLITE_TESTCTRL_ALWAYS" value="13"/>
  <define name="SQLITE_TESTCTRL_RESERVE" value="14"/>
  <define name="SQLITE_TESTCTRL_OPTIMIZATIONS" value="15"/>
  <define name="SQLITE_TESTCTRL_ISKEYWORD" value="16"/>
  <define name="SQLITE_TESTCTRL_SCRATCHMALLOC" value="17"/>
  <define name="SQLITE_TESTCTRL_INTERNAL_FUNCTIONS" value="17"/>
  <define name="SQLITE_TESTCTRL_LOCALTIME_FAULT" value="18"/>
  <define name="SQLITE_TESTCTRL_EXPLAIN_STMT" value="19"/>
  <define name="SQLITE_TESTCTRL_ONCE_RESET_THRESHOLD" value="19"/>
  <define name="SQLITE_TESTCTRL_NEVER_CORRUPT" value="20"/>
  <define name="SQLITE_TESTCTRL_VDBE_COVERAGE" value="21"/>
  <define name="SQLITE_TESTCTRL_BYTEORDER" value="22"/>
  <define name="SQLITE_TESTCTRL_ISINIT" value="23"/>
  <define name="SQLITE_TESTCTRL_SORTER_MMAP" value="24"/>
  <define name="SQLITE_TESTCTRL_IMPOSTER" value="25"/>
  <define name="SQLITE_TESTCTRL_PARSER_COVERAGE" value="26"/>
  <define name="SQLITE_TESTCTRL_LAST" value="26"/>
  <!-- SQL Trace Event Codes -->
  <define name="SQLITE_TRACE_STMT" value="0x01"/>
  <define name="SQLITE_TRACE_PROFILE" value="0x02"/>
  <define name="SQLITE_TRACE_ROW" value="0x04"/>
  <define name="SQLITE_TRACE_CLOSE" value="0x08"/>
  <!-- Win32 Directory Types -->
  <define name="SQLITE_WIN32_DATA_DIRECTORY_TYPE" value="1"/>
  <define name="SQLITE_WIN32_TEMP_DIRECTORY_TYPE" value="2"/>
  <!-- Run-Time Limit Categories -->
  <define name="SQLITE_LIMIT_LENGTH" value="0"/>
  <define name="SQLITE_LIMIT_SQL_LENGTH" value="1"/>
  <define name="SQLITE_LIMIT_COLUMN" value="2"/>
  <define name="SQLITE_LIMIT_EXPR_DEPTH" value="3"/>
  <define name="SQLITE_LIMIT_COMPOUND_SELECT" value="4"/>
  <define name="SQLITE_LIMIT_VDBE_OP" value="5"/>
  <define name="SQLITE_LIMIT_FUNCTION_ARG" value="6"/>
  <define name="SQLITE_LIMIT_ATTACHED" value="7"/>
  <define name="SQLITE_LIMIT_LIKE_PATTERN_LENGTH" value="8"/>
  <define name="SQLITE_LIMIT_VARIABLE_NUMBER" value="9"/>
  <define name="SQLITE_LIMIT_TRIGGER_DEPTH" value="10"/>
  <define name="SQLITE_LIMIT_WORKER_THREADS" value="11"/>
  <!-- Status Parameters for database connections -->
  <define name="SQLITE_DBSTATUS_LOOKASIDE_USED" value="0"/>
  <define name="SQLITE_DBSTATUS_CACHE_USED" value="1"/>
  <define name="SQLITE_DBSTATUS_SCHEMA_USED" value="2"/>
  <define name="SQLITE_DBSTATUS_STMT_USED" value="3"/>
  <define name="SQLITE_DBSTATUS_LOOKASIDE_HIT" value="4"/>
  <define name="SQLITE_DBSTATUS_LOOKASIDE_MISS_SIZE" value="5"/>
  <define name="SQLITE_DBSTATUS_LOOKASIDE_MISS_FULL" value="6"/>
  <define name="SQLITE_DBSTATUS_CACHE_HIT" value="7"/>
  <define name="SQLITE_DBSTATUS_CACHE_MISS" value="8"/>
  <define name="SQLITE_DBSTATUS_CACHE_WRITE" value="9"/>
  <define name="SQLITE_DBSTATUS_DEFERRED_FKS" value="10"/>
  <define name="SQLITE_DBSTATUS_CACHE_USED_SHARED" value="11"/>
  <define name="SQLITE_DBSTATUS_CACHE_SPILL" value="12"/>
  <define name="SQLITE_DBSTATUS_MAX" value="12"/>
  <!-- Status Parameters for prepared statements -->
  <define name="SQLITE_STMTSTATUS_FULLSCAN_STEP" value="1"/>
  <define name="SQLITE_STMTSTATUS_SORT" value="2"/>
  <define name="SQLITE_STMTSTATUS_AUTOINDEX" value="3"/>
  <define name="SQLITE_STMTSTATUS_VM_STEP" value="4"/>
  <define name="SQLITE_STMTSTATUS_REPREPARE" value="5"/>
  <define name="SQLITE_STMTSTATUS_RUN" value="6"/>
  <define name="SQLITE_STMTSTATUS_MEMUSED" value="99"/>
  <!-- ########## SQLite Memory Allocation / Deallocation ########## -->
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_free -->
  <memory>
    <alloc init="false" buffer-size="malloc">sqlite3_malloc</alloc>
    <alloc init="false" buffer-size="malloc">sqlite3_malloc64</alloc>
    <dealloc>sqlite3_free</dealloc>
  </memory>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_str_new -->
  <memory>
    <alloc init="true">sqlite3_str_new</alloc>
    <dealloc>sqlite3_str_finish</dealloc>
  </memory>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_str_finish -->
  <memory>
    <alloc init="true">sqlite3_str_finish</alloc>
    <dealloc>sqlite3_free</dealloc>
  </memory>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_mprintf -->
  <memory>
    <alloc init="true">sqlite3_mprintf</alloc>
    <alloc init="true">sqlite3_vmprintf</alloc>
    <dealloc>sqlite3_free</dealloc>
  </memory>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_expanded_sql -->
  <memory>
    <alloc init="true">sqlite3_expanded_sql</alloc>
    <dealloc>sqlite3_free</dealloc>
  </memory>
  <!-- ########## SQLite Resource Allocation / Deallocation ########## -->
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3 -->
  <resource>
    <alloc arg="2">sqlite3_open</alloc>
    <alloc arg="2">sqlite3_open16</alloc>
    <alloc arg="2">sqlite3_open_v2</alloc>
    <dealloc>sqlite3_close</dealloc>
    <dealloc>sqlite3_close_v2</dealloc>
  </resource>
  <!-- ########## SQLite Functions ########## -->
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_bind_blob -->
  <!-- int sqlite3_bind_blob(sqlite3_stmt*, int, const void*, int n, void(*)(void*)); -->
  <function name="sqlite3_bind_blob">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>1:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="5">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_bind_blob -->
  <!-- int sqlite3_bind_double(sqlite3_stmt*, int, double); -->
  <function name="sqlite3_bind_double">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>1:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_bind_blob -->
  <!-- int sqlite3_bind_int(sqlite3_stmt*, int, int); -->
  <!-- int sqlite3_bind_int64(sqlite3_stmt*, int, sqlite3_int64); -->
  <function name="sqlite3_bind_int,sqlite3_bind_int64">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>1:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_bind_blob -->
  <!-- int sqlite3_bind_null(sqlite3_stmt*, int); -->
  <function name="sqlite3_bind_null">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>1:</valid>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_bind_blob -->
  <!-- int sqlite3_bind_text(sqlite3_stmt*, int, const char*, int, void(*)(void*)); -->
  <function name="sqlite3_bind_text">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>1:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="5">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_clear_bindings -->
  <!-- int sqlite3_clear_bindings(sqlite3_stmt*); -->
  <function name="sqlite3_clear_bindings">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_close -->
  <!-- int sqlite3_close(sqlite3*); -->
  <!-- int sqlite3_close_v2(sqlite3*); -->
  <function name="sqlite3_close,sqlite3_close_v2">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_column_blob -->
  <!-- const void *sqlite3_column_blob(sqlite3_stmt*, int iCol); -->
  <function name="sqlite3_column_blob">
    <noreturn>false</noreturn>
    <returnValue type="const void *"/>
    <use-retval/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_column_count -->
  <!-- int sqlite3_column_count(sqlite3_stmt *pStmt); -->
  <function name="sqlite3_column_count">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_column_blob -->
  <!-- double sqlite3_column_double(sqlite3_stmt*, int iCol); -->
  <function name="sqlite3_column_double">
    <noreturn>false</noreturn>
    <returnValue type="double"/>
    <use-retval/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
      <not-bool/>
    </arg>
  </function>
  <!-- int sqlite3_column_int(sqlite3_stmt*, int iCol); -->
  <function name="sqlite3_column_int">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
      <not-bool/>
    </arg>
  </function>
  <!-- sqlite3_int64 sqlite3_column_int64(sqlite3_stmt*, int iCol); -->
  <function name="sqlite3_column_int64">
    <noreturn>false</noreturn>
    <returnValue type="sqlite3_int64"/>
    <use-retval/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
      <not-bool/>
    </arg>
  </function>
  <!-- const unsigned char *sqlite3_column_text(sqlite3_stmt*, int iCol); -->
  <function name="sqlite3_column_text">
    <noreturn>false</noreturn>
    <returnValue type="const unsigned char *"/>
    <use-retval/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
      <not-bool/>
    </arg>
  </function>
  <!-- const void *sqlite3_column_text16(sqlite3_stmt*, int iCol); -->
  <function name="sqlite3_column_text16">
    <noreturn>false</noreturn>
    <returnValue type="const void *"/>
    <use-retval/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
      <not-bool/>
    </arg>
  </function>
  <!-- sqlite3_value *sqlite3_column_value(sqlite3_stmt*, int iCol); -->
  <function name="sqlite3_column_value">
    <noreturn>false</noreturn>
    <returnValue type="sqlite3_value *"/>
    <use-retval/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
      <not-bool/>
    </arg>
  </function>
  <!-- int sqlite3_column_bytes(sqlite3_stmt*, int iCol); -->
  <!-- int sqlite3_column_bytes16(sqlite3_stmt*, int iCol); -->
  <!-- int sqlite3_column_type(sqlite3_stmt*, int iCol); -->
  <function name="sqlite3_column_bytes,sqlite3_column_bytes16,sqlite3_column_type">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_context_db_handle -->
  <!-- sqlite3 *sqlite3_context_db_handle(sqlite3_context*); -->
  <function name="sqlite3_context_db_handle">
    <noreturn>false</noreturn>
    <returnValue type="sqlite3 *"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_create_function -->
  <!-- int sqlite3_create_function(sqlite3 *db,
                                   const char *zFunctionName,
                                   int nArg,
                                   int eTextRep,
                                   void *pApp,
                                   void (*xFunc)(sqlite3_context*,int,sqlite3_value**),
                                   void (*xStep)(sqlite3_context*,int,sqlite3_value**),
                                   void (*xFinal)(sqlite3_context*)); -->
  <!-- Declaration for sqlite3_create_function16 is nearly the same, but zFunctionName is "const void *" -->
  <function name="sqlite3_create_function,sqlite3_create_function16">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>-1:127</valid>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="5"/>
    <arg nr="6">
      <not-uninit/>
    </arg>
    <arg nr="7">
      <not-uninit/>
    </arg>
    <arg nr="8">
      <not-uninit/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_errcode -->
  <!-- int sqlite3_errcode(sqlite3 *db); -->
  <!-- int sqlite3_extended_errcode(sqlite3 *db); -->
  <function name="sqlite3_errcode,sqlite3_extended_errcode">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_errcode -->
  <!-- const char *sqlite3_errmsg(sqlite3*); -->
  <function name="sqlite3_errmsg">
    <noreturn>false</noreturn>
    <returnValue type="const char *"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_errcode -->
  <!-- const void *sqlite3_errmsg16(sqlite3*); -->
  <function name="sqlite3_errmsg16">
    <noreturn>false</noreturn>
    <returnValue type="const void *"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_errcode -->
  <!-- const char *sqlite3_errstr(int); -->
  <function name="sqlite3_errstr">
    <noreturn>false</noreturn>
    <returnValue type="const char *"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_exec -->
  <!-- int sqlite3_exec(sqlite3*,                                  /* An open database */
                        const char *sql,                           /* SQL to be evaluated */
                        int (*callback)(void*,int,char**,char**),  /* Callback function */
                        void *,                                    /* 1st argument to callback */
                        char **errmsg                              /* Error msg written here */); -->
  <function name="sqlite3_exec">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3"/>
    <arg nr="4"/>
    <arg nr="5" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_finalize -->
  <!-- int sqlite3_finalize(sqlite3_stmt *pStmt); -->
  <function name="sqlite3_finalize">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1">
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_free -->
  <!-- void sqlite3_free(void*); -->
  <function name="sqlite3_free">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_log -->
  <!-- void sqlite3_log(int iErrCode, const char *zFormat, ...); -->
  <function name="sqlite3_log">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="variadic" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_free -->
  <!-- void *sqlite3_malloc(int); -->
  <function name="sqlite3_malloc">
    <noreturn>false</noreturn>
    <returnValue type="void *"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>1:</valid>
    </arg>
  </function>
  <!-- void *sqlite3_malloc64(sqlite3_uint64); -->
  <function name="sqlite3_malloc64">
    <noreturn>false</noreturn>
    <returnValue type="void *"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>1:</valid>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_mprintf -->
  <!-- char *sqlite3_mprintf(const char*,...); -->
  <function name="sqlite3_mprintf">
    <noreturn>false</noreturn>
    <returnValue type="char *"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
    <arg nr="variadic" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- sqlite3_uint64 sqlite3_msize(void*); -->
  <function name="sqlite3_msize">
    <noreturn>false</noreturn>
    <returnValue type="sqlite3_uint64"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_mutex_alloc -->
  <!-- sqlite3_mutex *sqlite3_mutex_alloc(int); -->
  <function name="sqlite3_mutex_alloc">
    <noreturn>false</noreturn>
    <returnValue type="sqlite3_mutex *"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_mutex_alloc -->
  <!-- void sqlite3_mutex_enter(sqlite3_mutex*); -->
  <function name="sqlite3_mutex_enter">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_mutex_alloc -->
  <!-- void sqlite3_mutex_free(sqlite3_mutex*); -->
  <function name="sqlite3_mutex_free">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_mutex_held -->
  <!-- int sqlite3_mutex_held(sqlite3_mutex*); -->
  <function name="sqlite3_mutex_held">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_mutex_alloc -->
  <!-- void sqlite3_mutex_leave(sqlite3_mutex*); -->
  <function name="sqlite3_mutex_leave">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1">
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_mutex_held -->
  <!-- int sqlite3_mutex_notheld(sqlite3_mutex*); -->
  <function name="sqlite3_mutex_notheld">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_mutex_alloc -->
  <!-- int sqlite3_mutex_try(sqlite3_mutex*); -->
  <function name="sqlite3_mutex_try">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_open -->
  <!-- int sqlite3_open(const char *filename,   /* Database filename (UTF-8) */
                        sqlite3 **ppDb          /* OUT: SQLite db handle */); -->
  <function name="sqlite3_open">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_prepare -->
  <!-- int sqlite3_prepare(sqlite3 *db,            /* Database handle */
                           const char *zSql,       /* SQL statement, UTF-8 encoded */
                           int nByte,              /* Maximum length of zSql in bytes. */
                           sqlite3_stmt **ppStmt,  /* OUT: Statement handle */
                           const char **pzTail     /* OUT: Pointer to unused portion of zSql */); -->
  <function name="sqlite3_prepare">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <warn severity="style" alternatives="sqlite3_prepare_v2" reason="Obsolete">The sqlite3_prepare() interface is legacy and should be avoided</warn>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="out">
      <not-null/>
    </arg>
    <arg nr="5" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_prepare -->
  <!-- int sqlite3_prepare16(sqlite3 *db,            /* Database handle */
                             const void *zSql,       /* SQL statement, UTF-16 encoded */
                             int nByte,              /* Maximum length of zSql in bytes. */
                             sqlite3_stmt **ppStmt,  /* OUT: Statement handle */
                             const void **pzTail     /* OUT: Pointer to unused portion of zSql */); -->
  <function name="sqlite3_prepare16">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <warn severity="style" alternatives="sqlite3_prepare16_v2" reason="Obsolete">The sqlite3_prepare16() interface is legacy and should be avoided</warn>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="out">
      <not-null/>
    </arg>
    <arg nr="5" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_prepare -->
  <!-- int sqlite3_prepare_v2(sqlite3 *db,            /* Database handle */
                              const char *zSql,       /* SQL statement, UTF-8 encoded */
                              int nByte,              /* Maximum length of zSql in bytes. */
                              sqlite3_stmt **ppStmt,  /* OUT: Statement handle */
                              const char **pzTail     /* OUT: Pointer to unused portion of zSql */); -->
  <!-- int sqlite3_prepare16_v2(sqlite3 *db,            /* Database handle */
                                const void *zSql,       /* SQL statement, UTF-16 encoded */
                                int nByte,              /* Maximum length of zSql in bytes. */
                                sqlite3_stmt **ppStmt,  /* OUT: Statement handle */
                                const void **pzTail     /* OUT: Pointer to unused portion of zSql */); -->
  <function name="sqlite3_prepare_v2,sqlite3_prepare16_v2">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="out">
      <not-null/>
    </arg>
    <arg nr="5" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_prepare -->
  <!-- int sqlite3_prepare_v3(sqlite3 *db,            /* Database handle */
                              const char *zSql,       /* SQL statement, UTF-8 encoded */
                              int nByte,              /* Maximum length of zSql in bytes. */
                              unsigned int prepFlags, /* Zero or more SQLITE_PREPARE_ flags */
                              sqlite3_stmt **ppStmt,  /* OUT: Statement handle */
                              const char **pzTail     /* OUT: Pointer to unused portion of zSql */); -->
  <!-- int sqlite3_prepare16_v3(sqlite3 *db,            /* Database handle */
                                const void *zSql,       /* SQL statement, UTF-16 encoded */
                                int nByte,              /* Maximum length of zSql in bytes. */
                                unsigned int prepFlags, /* Zero or more SQLITE_PREPARE_ flags */
                                sqlite3_stmt **ppStmt,  /* OUT: Statement handle */
                                const void **pzTail     /* OUT: Pointer to unused portion of zSql */ ); -->
  <function name="sqlite3_prepare_v3,sqlite3_prepare16_v3">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="5" direction="out">
      <not-null/>
    </arg>
    <arg nr="6" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_reset -->
  <!-- int sqlite3_reset(sqlite3_stmt *pStmt); -->
  <function name="sqlite3_reset">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_result_blob -->
  <!-- void sqlite3_result_blob(sqlite3_context*, const void*, int, void(*)(void*)); -->
  <!-- void sqlite3_result_blob64(sqlite3_context*,const void*, sqlite3_uint64, void(*)(void*)); -->
  <function name="sqlite3_result_blob,sqlite3_result_blob64">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4">
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_result_blob -->
  <!-- void sqlite3_result_double(sqlite3_context*, double); -->
  <function name="sqlite3_result_double">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_result_blob -->
  <!-- void sqlite3_result_error(sqlite3_context*, const char*, int); -->
  <!-- void sqlite3_result_error16(sqlite3_context*, const void*, int); -->
  <function name="sqlite3_result_error,sqlite3_result_error16">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_result_blob -->
  <!-- void sqlite3_result_error_toobig(sqlite3_context*); -->
  <!-- void sqlite3_result_error_nomem(sqlite3_context*); -->
  <function name="sqlite3_result_error_toobig,sqlite3_result_error_nomem">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_result_blob -->
  <!-- void sqlite3_result_error_code(sqlite3_context*, int); -->
  <function name="sqlite3_result_error_code">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_result_blob -->
  <!-- void sqlite3_result_int(sqlite3_context*, int); -->
  <!-- void sqlite3_result_int64(sqlite3_context*, sqlite3_int64); -->
  <function name="sqlite3_result_text,sqlite3_result_int64">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_result_blob -->
  <!-- void sqlite3_result_null(sqlite3_context*); -->
  <function name="sqlite3_result_null">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_result_blob -->
  <!-- void sqlite3_result_text(sqlite3_context*, const char*, int, void(*)(void*)); -->
  <function name="sqlite3_result_text">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4">
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_result_blob -->
  <!-- void sqlite3_result_text64(sqlite3_context*, const char*,sqlite3_uint64, void(*)(void*), unsigned char encoding); -->
  <function name="sqlite3_result_text">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4"/>
    <arg nr="5" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_mprintf -->
  <!-- char *sqlite3_snprintf(int,char*,const char*, ...); -->
  <function name="sqlite3_snprintf">
    <noreturn>false</noreturn>
    <returnValue type="char *"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="out">
      <minsize type="argvalue" arg="1"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
    <arg nr="variadic" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_step -->
  <!-- int sqlite3_step(sqlite3_stmt*); -->
  <function name="sqlite3_step">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <arg nr="1">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_stricmp -->
  <!-- int sqlite3_stricmp(const char *, const char *); -->
  <function name="sqlite3_stricmp">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-null/>
      <strz/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_stricmp -->
  <!-- int sqlite3_strnicmp(const char *, const char *, int); -->
  <function name="sqlite3_stricmp">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
      <strz/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-null/>
      <strz/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_value_blob -->
  <!-- const void *sqlite3_value_blob(sqlite3_value*); -->
  <function name="sqlite3_value_blob">
    <noreturn>false</noreturn>
    <returnValue type="const void *"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_value_blob -->
  <!-- int sqlite3_value_bytes(sqlite3_value*); -->
  <!-- int sqlite3_value_bytes16(sqlite3_value*); -->
  <function name="sqlite3_value_bytes,sqlite3_value_bytes16">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_value_blob -->
  <!-- double sqlite3_value_double(sqlite3_value*); -->
  <function name="sqlite3_value_double">
    <noreturn>false</noreturn>
    <returnValue type="double"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_value_blob -->
  <!-- int sqlite3_value_frombind(sqlite3_value*); -->
  <function name="sqlite3_value_frombind">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_value_blob -->
  <!-- int sqlite3_value_int(sqlite3_value*); -->
  <function name="sqlite3_value_int">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_value_blob -->
  <!-- sqlite3_int64 sqlite3_value_int64(sqlite3_value*); -->
  <function name="sqlite3_value_int64">
    <noreturn>false</noreturn>
    <returnValue type="sqlite3_int64"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_value_blob -->
  <!-- int sqlite3_value_nochange(sqlite3_value*); -->
  <function name="sqlite3_value_nochange">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_value_blob -->
  <!-- int sqlite3_value_numeric_type(sqlite3_value*); -->
  <function name="sqlite3_value_numeric_type">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_value_blob -->
  <!-- void *sqlite3_value_pointer(sqlite3_value*, const char*); -->
  <function name="sqlite3_value_pointer">
    <noreturn>false</noreturn>
    <returnValue type="void *"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_value_blob -->
  <!-- const unsigned char *sqlite3_value_text(sqlite3_value*); -->
  <function name="sqlite3_value_text">
    <noreturn>false</noreturn>
    <returnValue type="const unsigned char *"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_value_blob -->
  <!-- const void *sqlite3_value_text16(sqlite3_value*); -->
  <!-- const void *sqlite3_value_text16le(sqlite3_value*); -->
  <!-- const void *sqlite3_value_text16be(sqlite3_value*); -->
  <function name="sqlite3_value_text16,sqlite3_value_text16le,sqlite3_value_text16be">
    <noreturn>false</noreturn>
    <returnValue type="const void *"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- https://www.sqlite.org/capi3ref.html#sqlite3_value_blob -->
  <!-- int sqlite3_value_type(sqlite3_value*); -->
  <function name="sqlite3_value_type">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
</def>
