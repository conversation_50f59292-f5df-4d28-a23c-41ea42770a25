<?xml version="1.0"?>
<def format="2">
  <!-- stdint.h -->
  <define name="INT8_MIN" value="-128"/>
  <define name="INT16_MIN" value="-32768"/>
  <define name="INT32_MIN" value="-2147483648"/>
  <define name="INT64_MIN" value="-9223372036854775808"/>
  <define name="INT_FAST8_MIN" value="-128"/>
  <define name="INT_FAST16_MIN" value="-9223372036854775808"/>
  <define name="INT_FAST32_MIN" value="-9223372036854775808"/>
  <define name="INT_FAST64_MIN" value="-9223372036854775808"/>
  <define name="INT_LEAST8_MIN" value="-128"/>
  <define name="INT_LEAST16_MIN" value="-32768"/>
  <define name="INT_LEAST32_MIN" value="-2147483648"/>
  <define name="INT_LEAST64_MIN" value="-9223372036854775808"/>
  <define name="INT8_MAX" value="127"/>
  <define name="INT16_MAX" value="32767"/>
  <define name="INT32_MAX" value="2147483647"/>
  <define name="INT64_MAX" value="9223372036854775807"/>
  <define name="INT_FAST8_MAX" value="127"/>
  <define name="INT_FAST16_MAX" value="9223372036854775807"/>
  <define name="INT_FAST32_MAX" value="9223372036854775807"/>
  <define name="INT_FAST64_MAX" value="9223372036854775807"/>
  <define name="INT_LEAST8_MAX" value="127"/>
  <define name="INT_LEAST16_MAX" value="32767"/>
  <define name="INT_LEAST32_MAX" value="2147483647"/>
  <define name="INT_LEAST64_MAX" value="9223372036854775807"/>
  <define name="UINT8_MAX" value="255U"/>
  <define name="UINT16_MAX" value="65535U"/>
  <define name="UINT32_MAX" value="4294967295U"/>
  <define name="UINT64_MAX" value="18446744073709551615U"/>
  <define name="UINT_FAST8_MAX" value="255U"/>
  <define name="UINT_FAST16_MAX" value="18446744073709551615U"/>
  <define name="UINT_FAST32_MAX" value="18446744073709551615U"/>
  <define name="UINT_FAST64_MAX" value="18446744073709551615U"/>
  <define name="UINT_LEAST8_MAX" value="255U"/>
  <define name="UINT_LEAST16_MAX" value="65535U"/>
  <define name="UINT_LEAST32_MAX" value="4294967295U"/>
  <define name="UINT_LEAST64_MAX" value="18446744073709551615U"/>
  <!-- math.h -->
  <define name="M_E" value="2.7182818284590452354"/>
  <define name="M_LOG2E" value="1.4426950408889634074"/>
  <define name="M_LOG10E" value="0.43429448190325182765"/>
  <define name="M_LN2" value="0.69314718055994530942"/>
  <define name="M_LN10" value="2.30258509299404568402"/>
  <define name="M_PI" value="3.14159265358979323846"/>
  <define name="M_PI_2" value="1.57079632679489661923"/>
  <define name="M_PI_4" value="0.78539816339744830962"/>
  <define name="M_1_PI" value="0.31830988618379067154"/>
  <define name="M_2_PI" value="0.63661977236758134308"/>
  <define name="M_2_SQRTPI" value="1.12837916709551257390"/>
  <define name="M_SQRT2" value="1.41421356237309504880"/>
  <define name="M_SQRT1_2" value="0.70710678118654752440"/>
  <define name="_IOFBF" value="0"/>
  <define name="_IOLBF" value="1"/>
  <define name="_IONBF" value="2"/>
  <!-- https://en.cppreference.com/w/cpp/numeric/math/FP_categories -->
  <define name="FP_NORMAL" value="1"/>
  <define name="FP_SUBNORMAL" value="2"/>
  <define name="FP_ZERO" value="3"/>
  <define name="FP_INFINITE" value="4"/>
  <define name="FP_NAN" value="5"/>
  <!-- errno_t is a typedef for int -->
  <define name="errno_t" value="int"/>
  <!-- void abort(void); -->
  <function name="abort,std::abort">
    <noreturn>true</noreturn>
  </function>
  <!-- int abs(int j); -->
  <function name="abs,std::abs">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;0?arg1:-arg1</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- intmax_t imaxabs(intmax_t n); -->
  <function name="imaxabs,std::imaxabs">
    <use-retval/>
    <pure/>
    <returnValue type="intmax_t">arg1&gt;0?arg1:-arg1</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- std::proj(std::complex) -->
  <function name="std::proj">
    <use-retval/>
    <pure/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double complex cproj(double complex x); -->
  <!-- float complex cprojf(float complex x); -->
  <!-- long double complex cprojl(long double complex x); -->
  <function name="cproj,cprojf,cprojl">
    <use-retval/>
    <pure/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double creal(double complex x); -->
  <!-- float crealf(float complex x); -->
  <!-- long double creall(long double complex x); -->
  <function name="creal,crealf,creall">
    <use-retval/>
    <pure/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double acos(double x); -->
  <function name="acos,std::acos">
    <use-retval/>
    <pure/>
    <returnValue type="double">acos(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>-1.0:1.0</valid>
    </arg>
  </function>
  <!-- float acosf(float x); -->
  <function name="acosf,std::acosf">
    <use-retval/>
    <pure/>
    <returnValue type="float">acos(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>-1.0:1.0</valid>
    </arg>
  </function>
  <!-- long double acosl(long double x); -->
  <function name="acosl,std::acosl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">acos(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>-1.0:1.0</valid>
    </arg>
  </function>
  <!-- double acosh(double x); -->
  <function name="acosh,std::acosh">
    <use-retval/>
    <pure/>
    <returnValue type="double">acosh(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>1.0:</valid>
    </arg>
  </function>
  <!-- float acoshf(float x); -->
  <function name="acoshf,std::acoshf">
    <use-retval/>
    <pure/>
    <returnValue type="float">acosh(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>1.0:</valid>
    </arg>
  </function>
  <!-- long double acoshl(long double x); -->
  <function name="acoshl,std::acoshl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">acosh(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>1.0:</valid>
    </arg>
  </function>
  <!-- char *asctime(const struct tm *tm) -->
  <function name="asctime,std::asctime">
    <use-retval/>
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <warn severity="style" cstd="c99" alternatives="strftime" reason="Obsolete"/>
  </function>
  <!-- errno_t asctime_s(char *buf, rsize_t bufsz, const struct tm *time_ptr); // since C11 -->
  <function name="asctime_s,std::asctime_s">
    <noreturn>false</noreturn>
    <returnValue type="errno_t"/>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>26:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
    <warn severity="style" alternatives="strftime" reason="Obsolete"/>
  </function>
  <!-- void assert(int expression) -->
  <function name="assert">
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- double sqrt(double x); -->
  <function name="sqrt,std::sqrt">
    <use-retval/>
    <pure/>
    <returnValue type="double">sqrt(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0.0:</valid>
    </arg>
  </function>
  <!-- float sqrtf(float x); -->
  <function name="sqrtf,std::sqrtf">
    <use-retval/>
    <pure/>
    <returnValue type="float">sqrt(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0.0:</valid>
    </arg>
  </function>
  <!-- long double sqrtl(long double x); -->
  <function name="sqrtl,std::sqrtl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">sqrt(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0.0:</valid>
    </arg>
  </function>
  <!-- double complex csqrt(double complex x); -->
  <!-- float complex csqrtf(float complex x); -->
  <!-- long double complex csqrtl(long double complex x); -->
  <function name="csqrt,csqrtf,csqrtl">
    <use-retval/>
    <pure/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double sinh(double x); -->
  <function name="sinh,std::sinh">
    <use-retval/>
    <pure/>
    <returnValue type="double">sinh(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float sinhf(float x); -->
  <function name="sinhf,std::sinhf">
    <use-retval/>
    <pure/>
    <returnValue type="float">sinh(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double sinhl(long double x); -->
  <function name="sinhl,std::sinhl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">sinh(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double sin(double x); -->
  <function name="sin,std::sin">
    <use-retval/>
    <pure/>
    <returnValue type="double">sin(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float sinf(float f); -->
  <function name="sinf,std::sinf">
    <use-retval/>
    <pure/>
    <returnValue type="float">sin(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double sinl(long double x); -->
  <function name="sinl,std::sinl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">sin(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double complex csin(double complex x); -->
  <!-- float complex csinf(float complex f); -->
  <!-- long double complex csinl(long double complex x); -->
  <function name="csin,csinf,csinl">
    <use-retval/>
    <pure/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double complex csinh(double complex x); -->
  <!-- float complex csinhf(float complex f); -->
  <!-- long double complex csinhl(long double complex x); -->
  <function name="csinh,csinhf,csinhl">
    <use-retval/>
    <pure/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double asin(double x); -->
  <function name="asin,std::asin">
    <use-retval/>
    <pure/>
    <returnValue type="double">asin(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <valid>-1.0:1.0</valid>
      <not-uninit/>
    </arg>
  </function>
  <!-- float asinf(float x); -->
  <function name="asinf,std::asinf">
    <use-retval/>
    <pure/>
    <returnValue type="float">asin(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <valid>-1.0:1.0</valid>
      <not-uninit/>
    </arg>
  </function>
  <!-- long double asinl(long double x); -->
  <function name="asinl,std::asinl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">asin(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <valid>-1.0:1.0</valid>
      <not-uninit/>
    </arg>
  </function>
  <!-- double complex casin(double complex x); -->
  <!-- float complex casinf(float complex x); -->
  <!-- long double complex casinl(long double complex x); -->
  <function name="casin,casinf,casinl">
    <use-retval/>
    <pure/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double asinh(double x); -->
  <function name="asinh,std::asinh">
    <use-retval/>
    <pure/>
    <returnValue type="double">asinh(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float asinhf(float x); -->
  <function name="asinhf,std::asinhf">
    <use-retval/>
    <pure/>
    <returnValue type="float">asinh(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double asinhl(long double x); -->
  <function name="asinhl,std::asinhl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">asinh(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double complex casinh(double complex x); -->
  <!-- float complex casinhf(float complex x); -->
  <!-- long double complex casinhl(long double complex x); -->
  <function name="casinh,casinhf,casinhl">
    <use-retval/>
    <pure/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- size_t wcsftime(wchar_t* ptr, size_t maxsize, const wchar_t* format, const struct tm* timeptr); -->
  <function name="wcsftime,std::wcsftime">
    <pure/>
    <returnValue type="size_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="4" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- double tan(double x); -->
  <function name="tan,std::tan">
    <use-retval/>
    <pure/>
    <returnValue type="double">tan(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float tanf(float x); -->
  <function name="tanf,std::tanf">
    <use-retval/>
    <pure/>
    <returnValue type="float">tan(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double tanl(long double x); -->
  <function name="tanl,std::tanl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">tan(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double complex ctan(double complex x); -->
  <!-- float complex ctanf(float complex x); -->
  <!-- long double complex ctanl(long double complex x); -->
  <function name="ctan,ctanf,ctanl">
    <use-retval/>
    <pure/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double tanh(double x); -->
  <function name="tanh,std::tanh">
    <use-retval/>
    <pure/>
    <returnValue type="double">tanh(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float tanhf(float x); -->
  <function name="tanhf,std::tanhf">
    <use-retval/>
    <pure/>
    <returnValue type="float">tanh(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double tanhl(long double x); -->
  <function name="tanhl,std::tanhl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">tanh(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double complex ctanh(double complex x); -->
  <!-- float complex ctanhf(float complex x); -->
  <!-- long double complex ctanhl(long double complex x); -->
  <function name="ctanh,ctanhf,ctanhl">
    <use-retval/>
    <pure/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int feclearexcept(int excepts); -->
  <function name="feclearexcept,std::feclearexcept">
    <pure/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int fegetenv(fenv_t* envp); -->
  <function name="fegetenv,std::fegetenv">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
    </arg>
  </function>
  <!-- int fegetexceptflag(fexcept_t* flagp, int excepts); -->
  <function name="fegetexceptflag,std::fegetexceptflag">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int fegetround(void); -->
  <function name="fegetround,std::fegetround">
    <use-retval/>
    <pure/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
  </function>
  <!-- int feholdexcept(fenv_t* envp); -->
  <function name="feholdexcept,std::feholdexcept">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
    </arg>
  </function>
  <!-- int feraiseexcept(int excepts); -->
  <function name="feraiseexcept,std::feraiseexcept">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int fesetenv(const fenv_t* envp); -->
  <function name="fesetenv,std::fesetenv">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- int fesetexceptflag(const fexcept_t* flagp, int excepts); -->
  <function name="fesetexceptflag,std::fesetexceptflag">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <!-- Note: flag values (FE_ALL_EXCEPT,FE_DIVBYZERO, etc.) are defined in this file -->
      <valid>1:31</valid>
    </arg>
  </function>
  <!-- int fesetround(int rdir); -->
  <function name="fesetround,std::fesetround">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int fetestexcept(int excepts); -->
  <function name="fetestexcept,std::fetestexcept">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <!-- Note: flag values (FE_ALL_EXCEPT,FE_DIVBYZERO, etc.) are defined in this file -->
      <valid>1:31</valid>
    </arg>
  </function>
  <!-- int feupdateenv(const fenv_t* envp); -->
  <function name="feupdateenv,std::feupdateenv">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- double atan(double x); -->
  <function name="atan,std::atan">
    <use-retval/>
    <pure/>
    <returnValue type="double">atan(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float atanf(float x); -->
  <function name="atanf,std::atanf">
    <use-retval/>
    <pure/>
    <returnValue type="float">atan(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double atanl(long double x); -->
  <function name="atanl,std::atanl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">atan(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double complex catan(double complex x); -->
  <!-- float complex catanf(float complex x); -->
  <!-- long double complex catanl(long double complex x); -->
  <function name="catan,catanf,catanl">
    <use-retval/>
    <pure/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- https://en.cppreference.com/w/cpp/numeric/math/tgamma -->
  <!-- double tgamma(double x); -->
  <function name="tgamma,std::tgamma">
    <use-retval/>
    <pure/>
    <returnValue type="double">tgamma(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <!-- If x is zero or a negative integer for which the function is asymptotic, it may cause a pole error (depending on implementation).-->
      <valid>!0.0:</valid>
    </arg>
  </function>
  <!-- https://en.cppreference.com/w/cpp/numeric/math/tgamma -->
  <!-- float tgammaf(float x); -->
  <function name="tgammaf,std::tgammaf">
    <use-retval/>
    <pure/>
    <returnValue type="float">tgamma(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <!-- If x is zero or a negative integer for which the function is asymptotic, it may cause a pole error (depending on implementation).-->
      <valid>!0.0:</valid>
    </arg>
  </function>
  <!-- https://en.cppreference.com/w/cpp/numeric/math/tgamma -->
  <!-- long double tgammal(long double x); -->
  <function name="tgammal,std::tgammal">
    <use-retval/>
    <pure/>
    <returnValue type="long double">tgamma(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <!-- If x is zero or a negative integer for which the function is asymptotic, it may cause a pole error (depending on implementation).-->
      <valid>!0.0:</valid>
    </arg>
  </function>
  <!-- double trunc(double x); -->
  <function name="trunc,std::trunc">
    <use-retval/>
    <pure/>
    <returnValue type="double">trunc(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float truncf(float x); -->
  <function name="truncf,std::truncf">
    <use-retval/>
    <pure/>
    <returnValue type="float">trunc(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double truncl(long double x); -->
  <function name="truncl,std::truncl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">trunc(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double atanh(double x); -->
  <function name="atanh,std::atanh">
    <use-retval/>
    <pure/>
    <returnValue type="double">atanh(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>-1.0:1.0</valid>
    </arg>
  </function>
  <!-- float atanhf(float x); -->
  <function name="atanhf,std::atanhf">
    <use-retval/>
    <pure/>
    <returnValue type="float">atanh(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>-1.0:1.0</valid>
    </arg>
  </function>
  <!-- long double atanhl(long double x); -->
  <function name="atanhl,std::atanhl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">atanh(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>-1.0:1.0</valid>
    </arg>
  </function>
  <!-- double complex catanh(double complex x); -->
  <!-- float complex catanhf(float complex x); -->
  <!-- long double complex catanhl(long double complex x); -->
  <function name="catanh,catanhf,catanhl">
    <use-retval/>
    <pure/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double atan2(double x, double y); -->
  <function name="atan2,std::atan2">
    <use-retval/>
    <pure/>
    <returnValue type="double">atan2(arg1, arg2)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float atan2f(float x, float y); -->
  <function name="atan2f,std::atan2f">
    <use-retval/>
    <pure/>
    <returnValue type="float">atan2(arg1, arg2)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double atan2l(long double x, long double y); -->
  <function name="atan2l,std::atan2l">
    <use-retval/>
    <pure/>
    <returnValue type="long double">atan2(arg1, arg2)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int atexit(void (*func)(void)); -->
  <function name="atexit,std::atexit">
    <pure/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
    </arg>
  </function>
  <!-- int atoi(const char *s); -->
  <function name="atoi,std::atoi">
    <use-retval/>
    <pure/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- long int atol(const char *s); -->
  <function name="atol,std::atol">
    <use-retval/>
    <pure/>
    <returnValue type="long int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- long long int atoll(const char *s); -->
  <function name="atoll,std::atoll">
    <use-retval/>
    <pure/>
    <returnValue type="long long int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- double atof(const char *s); -->
  <function name="atof,std::atof">
    <use-retval/>
    <pure/>
    <returnValue type="double"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- void * calloc(size_t nitems, size_t size); -->
  <function name="calloc,std::calloc">
    <use-retval/>
    <noreturn>false</noreturn>
    <returnValue type="void *"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- double ceil(double x); -->
  <function name="ceil,std::ceil">
    <use-retval/>
    <pure/>
    <returnValue type="double">ceil(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float ceilf(float x); -->
  <function name="ceilf,std::ceilf">
    <use-retval/>
    <pure/>
    <returnValue type="float">ceil(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double ceill(long double x); -->
  <function name="ceill,std::ceill">
    <use-retval/>
    <pure/>
    <returnValue type="long double">ceil(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double copysign(double x, double y); -->
  <function name="copysign,std::copysign">
    <use-retval/>
    <pure/>
    <returnValue type="double"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float copysignf(float x, float y); -->
  <function name="copysignf,std::copysignf">
    <use-retval/>
    <pure/>
    <returnValue type="float"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double copysign(long double x, long double y); -->
  <function name="copysignl,std::copysignl">
    <use-retval/>
    <pure/>
    <returnValue type="long double"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void clearerr(FILE * stream); -->
  <function name="clearerr,std::clearerr">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- clock_t clock(void); -->
  <function name="clock">
    <use-retval/>
    <returnValue type="clock_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
  </function>
  <!-- double cbrt(double x); -->
  <function name="cbrt,std::cbrt">
    <use-retval/>
    <pure/>
    <returnValue type="double">cbrt(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float cbrtf(float x); -->
  <function name="cbrtf,std::cbrtf">
    <use-retval/>
    <pure/>
    <returnValue type="float">cbrt(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double cbrtl(long double x); -->
  <function name="cbrtl,std::cbrtl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">cbrt(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double cos(double x); -->
  <function name="cos,std::cos">
    <use-retval/>
    <pure/>
    <returnValue type="double">cos(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float cosf(float x); -->
  <function name="cosf,std::cosf">
    <use-retval/>
    <pure/>
    <returnValue type="float">cos(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double cosl(long double x); -->
  <function name="cosl,std::cosl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">cos(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double complex ccos(double complex x); -->
  <!-- float complex ccosf(float complex x); -->
  <!-- long double complex ccosl(long double complex x); -->
  <function name="ccos,ccosf,ccosl">
    <use-retval/>
    <pure/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double cosh(double x); -->
  <function name="cosh,std::cosh">
    <use-retval/>
    <pure/>
    <returnValue type="double"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float coshf(float x); -->
  <function name="coshf,std::coshf">
    <use-retval/>
    <pure/>
    <returnValue type="float"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double coshl(long double x); -->
  <function name="coshl,std::coshl">
    <use-retval/>
    <pure/>
    <returnValue type="long double"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double complex ccosh(double complex x); -->
  <!-- float complex ccoshf(float complex x); -->
  <!-- long double ccoshl(long double complex x); -->
  <function name="ccosh,ccoshf,ccoshl">
    <use-retval/>
    <pure/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- char * ctime(const time_t *tp); -->
  <function name="ctime,std::ctime">
    <use-retval/>
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- errno_t ctime_s(char *buffer, rsize_t bufsz, const time_t *time); // since C11 -->
  <function name="ctime_s">
    <noreturn>false</noreturn>
    <returnValue type="errno_t"/>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>26:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- double difftime(time_t time2, time_t time1); -->
  <function name="difftime,std::difftime">
    <use-retval/>
    <returnValue type="double"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- div_t div(int num, int denom); -->
  <function name="div,std::div">
    <pure/>
    <returnValue type="div_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>:-1,1:</valid>
    </arg>
  </function>
  <!-- imaxdiv_t imaxdiv (intmax_t numer, intmax_t denom); -->
  <function name="imaxdiv,std::imaxdiv">
    <pure/>
    <returnValue type="imaxdiv_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>:-1,1:</valid>
    </arg>
  </function>
  <!-- void exit(int status); -->
  <function name="exit,std::exit">
    <noreturn>true</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <define name="EXIT_SUCCESS" value="0"/>
  <define name="EXIT_FAILURE" value="1"/>
  <!-- double erf(double x); -->
  <function name="erf,std::erf">
    <use-retval/>
    <pure/>
    <returnValue type="double">erf(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float erff(float f); -->
  <function name="erff,std::erff">
    <use-retval/>
    <pure/>
    <returnValue type="float">erf(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double erfl(long double x); -->
  <function name="erfl,std::erfl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">erf(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double erfc(double x); -->
  <function name="erfc,std::erfc">
    <use-retval/>
    <pure/>
    <returnValue type="double">erfc(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float erfcf(float x); -->
  <function name="erfcf,std::erfcf">
    <use-retval/>
    <pure/>
    <returnValue type="float">erfc(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double erfcl(long double x); -->
  <function name="erfcl,std::erfcl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">erfc(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double carg(double complex z);-->
  <function name="carg">
    <use-retval/>
    <pure/>
    <returnValue type="double"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float cargf(float complex z);-->
  <function name="cargf">
    <use-retval/>
    <pure/>
    <returnValue type="float"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double cargl(long double complex z);-->
  <function name="cargl">
    <use-retval/>
    <pure/>
    <returnValue type="long double"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double exp(double x); -->
  <function name="exp,std::exp">
    <use-retval/>
    <pure/>
    <returnValue type="double">exp(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float expf(float x); -->
  <function name="expf,std::expf">
    <use-retval/>
    <pure/>
    <returnValue type="float">exp(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double expl(long double x); -->
  <function name="expl,std::expl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">exp(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double complex cexp(double complex x); -->
  <!-- float complex cexpf(float complex x); -->
  <!-- long double complex cexpl(long double complex x); -->
  <function name="cexp,cexpf,cexpl">
    <use-retval/>
    <pure/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double complex cimag(double complex x); -->
  <!-- float complex cimagf(float complex x); -->
  <!-- long double complex cimagl(long double complex x); -->
  <function name="cimag,cimagf,cimagl">
    <use-retval/>
    <pure/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double exp2(double x); -->
  <function name="exp2,std::exp2">
    <use-retval/>
    <pure/>
    <returnValue type="double">exp2(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float exp2f(float x); -->
  <function name="exp2f,std::exp2f">
    <use-retval/>
    <pure/>
    <returnValue type="float">exp2(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double exp2l(long double x);-->
  <function name="exp2l,std::exp2l">
    <use-retval/>
    <pure/>
    <returnValue type="long double">exp2(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double expm1(double x); -->
  <function name="expm1,std::expm1">
    <use-retval/>
    <pure/>
    <returnValue type="double">expm1(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float expm1f(float x); -->
  <function name="expm1f,std::expm1f">
    <use-retval/>
    <pure/>
    <returnValue type="float">expm1(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double expm1l(long double x); -->
  <function name="expm1l,std::expm1l">
    <use-retval/>
    <pure/>
    <returnValue type="long double">expm1(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void _Exit(int status); -->
  <!-- void quick_exit(int exit_code); -->
  <function name="_Exit,std::_Exit,quick_exit,std::quick_exit">
    <noreturn>true</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int std::at_quick_exit( /*atexit-handler*/* func ) noexcept;-->
  <function name="std::at_quick_exit,at_quick_exit">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="int"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
    </arg>
  </function>
  <!-- double fabs(double x); -->
  <function name="fabs,std::fabs">
    <use-retval/>
    <pure/>
    <returnValue type="double">fabs(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float fabsf(float x); -->
  <function name="fabsf,std::fabsf">
    <use-retval/>
    <pure/>
    <returnValue type="float">fabs(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double fabsl(long double x); -->
  <function name="fabsl,std::fabsl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">fabs(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double fdim(double x, double y); -->
  <function name="fdim,std::fdim">
    <use-retval/>
    <pure/>
    <returnValue type="double">fdim(arg1, arg2)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float fdimf(float x, float y); -->
  <function name="fdimf,std::fdimf">
    <use-retval/>
    <pure/>
    <returnValue type="float">fdim(arg1, arg2)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double fdiml(long double x, long double y); -->
  <function name="fdiml,std::fdiml">
    <use-retval/>
    <pure/>
    <returnValue type="long double">fdim(arg1, arg2)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int fclose(FILE * stream); -->
  <function name="fclose,std::fclose">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- int feof(FILE *stream); -->
  <function name="feof,std::feof">
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- int ferror(FILE *stream);-->
  <function name="ferror,std::ferror">
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- int fflush(FILE *stream); -->
  <function name="fflush,std::fflush">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <!-- If stream is a null pointer, all streams are flushed. -->
      <not-uninit/>
    </arg>
  </function>
  <!-- int fgetc(FILE *stream); -->
  <!-- int getc(FILE *stream); -->
  <function name="fgetc,std::fgetc,getc,std::getc">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- wint_t fgetwc(FILE * stream); -->
  <!-- wint_t getwc(FILE* stream); -->
  <function name="fgetwc,std::fgetwc,getwc,std::getwc">
    <returnValue type="wint_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- int fgetpos(FILE* stream, fpos_t *ptr); -->
  <function name="fgetpos,std::fgetpos">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
    </arg>
  </function>
  <!-- double floor(double x); -->
  <function name="floor,std::floor">
    <use-retval/>
    <pure/>
    <returnValue type="double">floor(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float floorf(float x); -->
  <function name="floorf,std::floorf">
    <use-retval/>
    <pure/>
    <returnValue type="float">floor(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double floorl(long double x); -->
  <function name="floorl,std::floorl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">floor(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double fma(double x, double y, double z); -->
  <function name="fma,std::fma">
    <use-retval/>
    <pure/>
    <returnValue type="double"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float fmaf(float x, float y, float z); -->
  <function name="fmaf,std::fmaf">
    <use-retval/>
    <pure/>
    <returnValue type="float"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double fmal(long double x, long double y, long double z); -->
  <function name="fmal,std::fmal">
    <use-retval/>
    <pure/>
    <returnValue type="long double"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double fmax(double x, double y); -->
  <function name="fmax,std::fmax">
    <use-retval/>
    <pure/>
    <returnValue type="double">fmax(arg1,arg2)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float fmaxf(float x, float y); -->
  <function name="fmaxf,std::fmaxf">
    <use-retval/>
    <pure/>
    <returnValue type="float">fmax(arg1,arg2)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double fmaxl(long double x, long double y); -->
  <function name="fmaxl,std::fmaxl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">fmax(arg1,arg2)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double fmin(double x, double y); -->
  <function name="fmin,std::fmin">
    <use-retval/>
    <pure/>
    <returnValue type="double">fmin(arg1,arg2)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float fminf(float x, float y); -->
  <function name="fminf,std::fminf">
    <use-retval/>
    <pure/>
    <returnValue type="float">fmin(arg1,arg2)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double fminl(long double x, long double y); -->
  <function name="fminl,std::fminl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">fmin(arg1,arg2)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double fmod(double x, double y); -->
  <function name="fmod,std::fmod">
    <use-retval/>
    <pure/>
    <returnValue type="double">fmod(arg1,arg2)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>!0.0</valid>
    </arg>
  </function>
  <!-- float fmodf(float x, float y); -->
  <function name="fmodf,std::fmodf">
    <use-retval/>
    <pure/>
    <returnValue type="float">fmod(arg1,arg2)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>!0.0</valid>
    </arg>
  </function>
  <!-- long double fmodl(long double x, long double y); -->
  <function name="fmodl,std::fmodl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">fmod(arg1,arg2)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>!0.0</valid>
    </arg>
  </function>
  <!-- FILE * fopen(const char *filename, const char* mode); -->
  <function name="fopen,std::fopen">
    <use-retval/>
    <returnValue type="FILE *"/>
    <noreturn>false</noreturn>
      <leak-ignore/>
      <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- errno_t fopen_s(FILE *restrict *restrict streamptr,-->
  <!--                 const char *restrict filename,-->
  <!--                 const char *restrict mode); -->
  <function name="fopen_s">
    <returnValue type="errno_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- int fprintf(FILE *stream, const char *format, ...); -->
  <function name="fprintf,std::fprintf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
    <formatstr/>
    <arg nr="2" direction="in">
      <formatstr/>
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- int vfprintf(FILE *stream, const char *format, va_list arg); -->
  <function name="vfprintf,std::vfprintf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <formatstr/>
      <strz/>
    </arg>
    <arg nr="3" direction="in"/>
  </function>
  <!-- int vfwprintf(FILE *stream, const wchar_t *format, va_list arg); -->
  <function name="vfwprintf,std::vfwprintf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <formatstr/>
      <strz/>
    </arg>
    <arg nr="3" direction="in"/>
  </function>
  <!-- int fputc(int c, FILE *stream); -->
  <function name="fputc,std::fputc">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- wint_t fputwc(wchar_t wc, FILE * stream); -->
  <function name="fputwc,std::fputwc">
    <returnValue type="wint_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- int fputs(const char *string, FILE* stream); -->
  <function name="fputs,std::fputs">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- int fputws(const wchar_t* ws, FILE* stream); -->
  <function name="fputws,std::fputws">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- size_t fread(void *ptr, size_t size, size_t nobj, FILE *stream); -->
  <function name="fread,std::fread">
    <returnValue type="size_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="mul" arg="2" arg2="3"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- void free(void *block); -->
  <function name="free,std::free">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- FILE *freopen(const char *filename, const char *mode, FILE *stream); -->
  <function name="freopen,std::freopen">
    <use-retval/>
    <returnValue type="FILE *"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- errno_t freopen_s(FILE *restrict *restrict newstreamptr, const char *restrict filename, const char *restrict mode, FILE *restrict stream) -->
  <function name="freopen_s">
    <use-retval/>
    <returnValue type="errno_t"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="4">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- double frexp(double x, int *exp); -->
  <function name="frexp,std::frexp">
    <returnValue type="double"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
    </arg>
  </function>
  <!-- float frexpf(float x, int *exp); -->
  <function name="frexpf,std::frexpf">
    <returnValue type="float"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
    </arg>
  </function>
  <!-- long double frexpl(long double x, int *exp); -->
  <function name="frexpl,std::frexpl">
    <returnValue type="long double"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
    </arg>
  </function>
  <!-- double hypot(double x, double y); -->
  <!-- double hypot(double x, double y, double z); -->
  <function name="hypot,std::hypot">
    <use-retval/>
    <pure/>
    <returnValue type="double">hypot(arg1, arg2)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" default="0" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float hypotf(float x, float y); -->
  <function name="hypotf,std::hypotf">
    <use-retval/>
    <pure/>
    <returnValue type="float">hypot(arg1, arg2)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double hypotl(long double x, long double y); -->
  <function name="hypotl,std::hypotl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">hypot(arg1, arg2)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int fscanf(FILE *stream, const char *format, ...); -->
  <function name="fscanf,std::fscanf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <formatstr scan="true"/>
    <arg nr="2" direction="in">
      <formatstr/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- int vfscanf(FILE *stream, const char * format, va_list arg); -->
  <function name="vfscanf,std::vfscanf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3"/>
  </function>
  <!-- int vfwscanf(FILE *stream, const wchar_t * format, va_list arg); -->
  <function name="vfwscanf,std::vfwscanf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3"/>
  </function>
  <!-- int fseek(FILE* stream, long int offset, int origin); -->
  <function name="fseek,std::fseek">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:2</valid>
    </arg>
  </function>
  <!-- int fsetpos(FILE *stream, const fpos_t *ptr); -->
  <function name="fsetpos,std::fsetpos">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- char * fgets(char *buffer, int n, FILE *stream); -->
  <function name="fgets,std::fgets">
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- wchar_t* fgetws(wchar_t* ws, int num, FILE* stream); -->
  <function name="fgetws,std::fgetws">
    <returnValue type="wchar_t*"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- long int ftell(FILE *stream); -->
  <function name="ftell,std::ftell">
    <use-retval/>
    <returnValue type="long int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- int fwide(FILE* stream, int mode); -->
  <function name="fwide,std::fwide">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- size_t fwrite(const void *ptr, size_t size, size_t nobj, FILE *stream); -->
  <function name="fwrite,std::fwrite">
    <returnValue type="size_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="mul" arg="2" arg2="3"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- int mblen(const char *string, size_t size); -->
  <function name="mblen,std::mblen">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- int mbtowc(wchar_t* pwc, const char* pmb, size_t max); -->
  <function name="mbtowc,std::mbtowc">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- size_t mbrlen(const char* pmb, size_t max, mbstate_t* ps); -->
  <function name="mbrlen,std::mbrlen">
    <returnValue type="size_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="inout">
      <not-uninit/>
      <not-null/>
    </arg>
  </function>
  <!-- wint_t btowc(int c); -->
  <function name="btowc,std::btowc">
    <use-retval/>
    <pure/>
    <returnValue type="wint_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:255</valid>
    </arg>
  </function>
  <!-- int mbsinit(const mbstate_t* ps); -->
  <function name="mbsinit,std::mbsinit">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- wint_t getwchar(void); -->
  <function name="getwchar,std::getwchar">
    <returnValue type="wint_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
  </function>
  <!-- size_t mbstowcs(wchar_t *ws, const char *s, size_t n); -->
  <function name="mbstowcs,std::mbstowcs">
    <returnValue type="size_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- size_t mbsrtowcs(wchar_t* dest, const char** src, size_t max, mbstate_t* ps); -->
  <function name="mbsrtowcs,std::mbsrtowcs">
    <returnValue type="size_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-uninit indirect="1"/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="inout">
      <not-null/>
    </arg>
  </function>
  <!-- int wctob(wint_t wc); -->
  <function name="wctob,std::wctob">
    <use-retval/>
    <pure/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int wctomb(char *s, wchar_t wchar); -->
  <function name="wctomb,std::wctomb">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- size_t wcstombs(char *mbstr, const wchar_t *wcstr, size_t n);-->
  <function name="wcstombs,std::wcstombs">
    <returnValue type="size_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out"/>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- int getchar(void); -->
  <function name="getchar,std::getchar">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
  </function>
  <!-- int ungetc(int c, FILE *stream); -->
  <function name="ungetc,std::ungetc">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:255</valid>
    </arg>
    <arg nr="2">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- wint_t ungetwc(wint_t c, FILE *stream); -->
  <function name="ungetwc,std::ungetwc">
    <returnValue type="wint_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- char * getenv(const char *name); -->
  <function name="getenv,std::getenv">
    <use-retval/>
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- char *gets(char *buffer); -->
  <function name="gets,std::gets">
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
    </arg>
    <warn severity="warning">Obsolete function 'gets' called. It is recommended to use 'fgets' or 'gets_s' instead.
The obsolete function 'gets' is called. With 'gets' you'll get a buffer overrun if the input data exceeds the size of the buffer. It is recommended to use the functions 'fgets' or 'gets_s' instead.</warn>
  </function>
  <!-- char *gets_s(char *buffer, rsize_t size); -->
  <function name="gets_s,std::gets_s">
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- struct tm * gmtime(const time_t *tp); -->
  <function name="gmtime,std::gmtime">
    <use-retval/>
    <pure/>
    <noreturn>false</noreturn>
    <returnValue type="struct tm *"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- int isalnum(int c); -->
  <function name="isalnum,std::isalnum">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;=0x30 &amp;&amp; arg1&lt;=0x39 || arg1&gt;=0x41 &amp;&amp; arg1 &lt;=0x5A || arg1&gt;=0x61 &amp;&amp; arg1 &lt;=0x7A</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:255</valid>
    </arg>
  </function>
  <!-- int iswalnum(wint_t c); -->
  <function name="iswalnum,std::iswalnum">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;=0x30 &amp;&amp; arg1&lt;=0x39 || arg1&gt;=0x41 &amp;&amp; arg1 &lt;=0x5A || arg1&gt;=0x61 &amp;&amp; arg1 &lt;=0x7A</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int isalpha(int c); -->
  <function name="isalpha,std::isalpha">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;='A' &amp;&amp; arg1&lt;='Z' || arg1&gt;='a' &amp;&amp; arg1 &lt;='z'</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:255</valid>
    </arg>
  </function>
  <!-- int iswalpha(wint_t c);-->
  <function name="iswalpha,std::iswalpha">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;='A' &amp;&amp; arg1&lt;='Z' || arg1&gt;='a' &amp;&amp; arg1 &lt;='z'</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int isblank(int c); -->
  <function name="isblank,std::isblank">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1==' ' || arg1=='\t'</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:255</valid>
    </arg>
  </function>
  <!-- int iswblank(wint_t c); -->
  <function name="iswblank,std::iswblank">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1==' ' || arg1=='\t'</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int iscntrl(int c); -->
  <function name="iscntrl,std::iscntrl">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1==0x7F || arg1&lt;=0x1F</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:255</valid>
    </arg>
  </function>
  <!-- int iswcntrl(wint_t c); -->
  <function name="iswcntrl,std::iswcntrl">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1==0x7F || arg1&lt;=0x1F</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int iswctype( wint_t wc, wctype_t desc ); -->
  <function name="iswctype,std::iswctype">
    <use-retval/>
    <pure/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int isdigit(int c); -->
  <function name="isdigit,std::isdigit">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;='0' &amp;&amp; arg1&lt;='9'</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:255</valid>
    </arg>
  </function>
  <!-- int iswdigit(wint_t c); -->
  <function name="iswdigit,std::iswdigit">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;='0' &amp;&amp; arg1&lt;='9'</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int isgraph(int c); -->
  <function name="isgraph,std::isgraph">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;=0x21 &amp;&amp; arg1&lt;=0x7E</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:255</valid>
    </arg>
  </function>
  <!-- int iswgraph(wint_t c); -->
  <function name="iswgraph,std::iswgraph">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;=0x21 &amp;&amp; arg1&lt;=0x7E</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int islower(int c); -->
  <function name="islower,std::islower">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;=0x61 &amp;&amp; arg1&lt;=0x7A</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:255</valid>
    </arg>
  </function>
  <!-- int iswlower(wint_t c); -->
  <function name="iswlower,std::iswlower">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;=0x61 &amp;&amp; arg1&lt;=0x7A</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int isprint(int c); -->
  <function name="isprint,std::isprint">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;=0x20 &amp;&amp; arg1&lt;=0x7E</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:255</valid>
    </arg>
  </function>
  <!-- int iswprint(wint_t c); -->
  <function name="iswprint,std::iswprint">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;=0x20 &amp;&amp; arg1&lt;=0x7E</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int ispunct(int c); -->
  <function name="ispunct,std::ispunct">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;=0x21 &amp;&amp; arg1&lt;=0x2F || arg1&gt;=0x3A &amp;&amp; arg1&lt;=0x40 || arg1&gt;=0x5B &amp;&amp; arg1&lt;=0x60 || arg1&gt;=0x7B &amp;&amp; arg1&lt;=0x7E</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:255</valid>
    </arg>
  </function>
  <!-- int iswpunct(wint_t c); -->
  <function name="iswpunct,std::iswpunct">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;=0x21 &amp;&amp; arg1&lt;=0x2F || arg1&gt;=0x3A &amp;&amp; arg1&lt;=0x40 || arg1&gt;=0x5B &amp;&amp; arg1&lt;=0x60 || arg1&gt;=0x7B &amp;&amp; arg1&lt;=0x7E</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int isspace(int c); -->
  <function name="isspace,std::isspace">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;=0x09 &amp;&amp; arg1&lt;=0x0D || arg1==0x20</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:255</valid>
    </arg>
  </function>
  <!-- int iswspace(wint_t c); -->
  <function name="iswspace,std::iswspace">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;=0x09 &amp;&amp; arg1&lt;=0x0D || arg1==0x20</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int isupper(int c); -->
  <function name="isupper,std::isupper">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;=0x41 &amp;&amp; arg1&lt;=0x5A</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:255</valid>
    </arg>
  </function>
  <!-- int iswupper(wint_t c); -->
  <function name="iswupper,std::iswupper">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;=0x41 &amp;&amp; arg1&lt;=0x5A</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int isxdigit(int c); -->
  <function name="isxdigit,std::isxdigit">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;=0x30 &amp;&amp; arg1&lt;=0x39 || arg1&gt;=0x41 &amp;&amp; arg1&lt;=0x46 || arg1&gt;=0x61 &amp;&amp; arg1&lt;=0x66</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:255</valid>
    </arg>
  </function>
  <!-- int iswxdigit(wint_t c); -->
  <function name="iswxdigit,std::iswxdigit">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;=0x30 &amp;&amp; arg1&lt;=0x39 || arg1&gt;=0x41 &amp;&amp; arg1&lt;=0x46 || arg1&gt;=0x61 &amp;&amp; arg1&lt;=0x66</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- wint_t towctrans(wint_t c, wctrans_t desc); -->
  <function name="towctrans,std::towctrans">
    <use-retval/>
    <pure/>
    <returnValue type="wint_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- wint_t towlower(wint_t c); -->
  <function name="towlower,std::towlower">
    <use-retval/>
    <pure/>
    <returnValue type="wint_t">arg1 &lt; 'A' || arg1 &gt; 'Z' ? arg1 : arg1 + 32</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- wint_t towupper(wint_t c); -->
  <function name="towupper,std::towupper">
    <use-retval/>
    <pure/>
    <returnValue type="wint_t">arg1 &lt; 'a' || arg1 &gt; 'z' ? arg1 : arg1 - 32</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- wctrans_t wctrans(const char* property); -->
  <function name="wctrans,std::wctrans">
    <use-retval/>
    <pure/>
    <returnValue type="wctype_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- wctype_t wctype(const char* property); -->
  <function name="wctype,std::wctype">
    <use-retval/>
    <pure/>
    <returnValue type="wctype_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- double complex cabs(double complex z); -->
  <!-- float complex cabsf(float complex z); -->
  <!-- long double complex cabsl(long double complex z); -->
  <function name="cabs,cabsf,cabsl">
    <use-retval/>
    <pure/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double complex cacos(double complex z); -->
  <!-- float complex cacosf(float complex z); -->
  <!-- long double complex cacosl(long double complex z); -->
  <function name="cacos,cacosf,cacosl">
    <use-retval/>
    <pure/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double complex cacosh(double complex z); -->
  <!-- float complex cacoshf(float complex z); -->
  <!-- long double complex cacoshl(long double complex z); -->
  <function name="cacosh,cacoshf,cacoshl">
    <use-retval/>
    <pure/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long int labs(long int x); -->
  <function name="labs,std::labs">
    <use-retval/>
    <pure/>
    <returnValue type="long int">arg1&gt;0?arg1:-arg1</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long long int llabs(long long int x);-->
  <function name="llabs,std::llabs">
    <use-retval/>
    <pure/>
    <returnValue type="long long int">arg1&gt;0?arg1:-arg1</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double ldexp(double x, int exp); -->
  <function name="ldexp,std::ldexp">
    <use-retval/>
    <pure/>
    <returnValue type="double">ldexp(arg1,arg2)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float ldexpf(float x, int exp); -->
  <function name="ldexpf,std::ldexpf">
    <use-retval/>
    <pure/>
    <returnValue type="float">ldexp(arg1,arg2)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double ldexpl(long double x, int exp); -->
  <function name="ldexpl,std::ldexpl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">ldexp(arg1,arg2)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- https://cplusplus.com/reference/cmath/lgamma/ -->
  <!-- double lgamma(double x); -->
  <function name="lgamma,std::lgamma">
    <use-retval/>
    <pure/>
    <returnValue type="double">lgamma(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <!-- If x is zero or a negative integer for which the function is asymptotic, it may cause a pole error (depending on implementation).-->
      <valid>!0.0:</valid>
    </arg>
  </function>
  <!-- https://cplusplus.com/reference/cmath/lgamma/ -->
  <!-- float lgammaf(float x); -->
  <function name="lgammaf,std::lgammaf">
    <use-retval/>
    <pure/>
    <returnValue type="float">lgamma(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <!-- If x is zero or a negative integer for which the function is asymptotic, it may cause a pole error (depending on implementation).-->
      <valid>!0.0:</valid>
    </arg>
  </function>
  <!-- https://cplusplus.com/reference/cmath/lgamma/ -->
  <!-- long double lgammal(long double x); -->
  <function name="lgammal,std::lgammal">
    <use-retval/>
    <pure/>
    <returnValue type="long double">lgamma(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <!-- If x is zero or a negative integer for which the function is asymptotic, it may cause a pole error (depending on implementation).-->
      <valid>!0.0:</valid>
    </arg>
  </function>
  <!-- double rint(double x); -->
  <function name="rint,std::rint">
    <use-retval/>
    <pure/>
    <returnValue type="double"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float rintf(float x); -->
  <function name="rintf,std::rintf">
    <use-retval/>
    <pure/>
    <returnValue type="float"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double rintl(long double x); -->
  <function name="rintl,std::rintl">
    <use-retval/>
    <pure/>
    <returnValue type="long double"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long int lrint(double x); -->
  <!-- long int lrintf(float x); -->
  <!-- long int lrintl(long double x); -->
  <function name="lrint,std::lrint,lrintf,std::lrintf,lrintl,std::lrintl">
    <use-retval/>
    <pure/>
    <returnValue type="long int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long long int llrint(double x); -->
  <!-- long long int llrintf(float x); -->
  <!-- long long int llrintl(long double x); -->
  <function name="llrint,std::llrint,llrintf,std::llrintf,llrintl,std::llrintl">
    <use-retval/>
    <pure/>
    <returnValue type="long long int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long long int llround(double x); -->
  <!-- long long int llroundf(float x); -->
  <!-- long long int llroundl(long double x); -->
  <function name="llround,std::llround,llroundf,std::llroundf,llroundl,std::llroundl">
    <use-retval/>
    <pure/>
    <returnValue type="long long int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long int lround(double x); -->
  <!-- long int lroundf(float x); -->
  <!-- long int lroundl(long double x); -->
  <function name="lround,std::lround,lroundf,std::lroundf,lroundl,std::lroundl">
    <use-retval/>
    <pure/>
    <returnValue type="long int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int rand(void); -->
  <function name="rand,std::rand">
    <use-retval/>
    <returnValue type="int" unknownValues="all"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
  </function>
  <!-- void srand(unsigned int seed); -->
  <function name="srand,std::srand">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- ldiv_t ldiv(long int num, long int denom); -->
  <function name="ldiv,std::ldiv">
    <pure/>
    <returnValue type="ldiv_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>:-1,1:</valid>
    </arg>
  </function>
  <!-- lldiv_t lldiv(long long int num, long long int denom); -->
  <function name="lldiv,std::lldiv">
    <pure/>
    <returnValue type="lldiv_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>:-1,1:</valid>
    </arg>
  </function>
  <!-- struct tm * localtime(const time_t *tp); -->
  <function name="localtime,std::localtime">
    <use-retval/>
    <returnValue type="struct tm *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- struct tm *localtime_s(const time_t *restrict time, struct tm *restrict result) -->
  <function name="localtime_s,std::localtime_s">
    <returnValue type="struct tm *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
    </arg>
  </function>
  <!-- double log(double x); -->
  <function name="log,std::log">
    <use-retval/>
    <returnValue type="double">log(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>4.94066e-324:</valid>
    </arg>
  </function>
  <!-- float logf(float x); -->
  <function name="logf,std::logf">
    <use-retval/>
    <returnValue type="float">log(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>1.4013e-45:</valid>
    </arg>
  </function>
  <!-- long double logl(long double x);-->
  <function name="logl,std::logl">
    <use-retval/>
    <returnValue type="long double">log(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>4.94066e-324:</valid>
    </arg>
  </function>
  <!-- double complex clog(double complex x); -->
  <!-- float complex clogf(float complex x); -->
  <!-- long double complex clogl(long double complex x);-->
  <function name="clog,clogf,clogl">
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double complex conj(double complex x);-->
  <!-- float complex conjf(float complex x);-->
  <!-- long double complex conjl(long double complex x);-->
  <function name="conj,conjf,conjl">
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int fpclassify(double x); -->
  <function name="fpclassify,std::fpclassify">
    <use-retval/>
    <pure/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int isfinite(double x); -->
  <!-- bool isfinite(float x); // since C++11 -->
  <!-- bool isfinite(double x); // since C++11 -->
  <!-- bool isfinite(long double x); // since C++11 -->
  <!-- bool isfinite(Integral x); // since C++11 -->
  <function name="isfinite,std::isfinite">
    <use-retval/>
    <pure/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int isgreater(double x, double y); -->
  <function name="isgreater,std::isgreater">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&gt;arg2?1:0</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int isgreaterequal(double x, double y); -->
  <function name="isgreaterequal,std::isgreaterequal">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1 &gt;= arg2?1:0</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int isinf(double x); -->
  <!-- bool isinf(float x); // since C++11 -->
  <!-- bool isinf(double x); // since C++11 -->
  <!-- bool isinf(long double x); // since C++11 -->
  <!-- bool isinf(Integral x); // since C++11 -->
  <function name="isinf,std::isinf">
    <use-retval/>
    <pure/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- double logb(double x); -->
  <function name="logb,std::logb">
    <use-retval/>
    <pure/>
    <returnValue type="double">logb(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float logbf(float x); -->
  <function name="logbf,std::logbf">
    <use-retval/>
    <pure/>
    <returnValue type="float">logb(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double logbl(long double x); -->
  <function name="logbl,std::logbl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">logb(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int isless(double x, double y); -->
  <function name="isless,std::isless">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1&lt;arg2?1:0</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int islessequal(double x, double y); -->
  <function name="islessequal,std::islessequal">
    <use-retval/>
    <pure/>
    <returnValue type="int">arg1 &lt;= arg2?1:0</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int islessgreater(double x, double y); -->
  <function name="islessgreater,std::islessgreater">
    <use-retval/>
    <pure/>
    <returnValue type="int">(arg1&lt;arg2 || arg1&gt;arg2)?1:0</returnValue>
    <!-- true if x < y || x > y, false otherwise -->
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double nan(const char *tagp); -->
  <function name="nan,std::nan">
    <use-retval/>
    <pure/>
    <returnValue type="double"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- float nanf(const char *tagp); -->
  <function name="nanf,std::nanf">
    <use-retval/>
    <pure/>
    <returnValue type="float"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- long double nanl(const char *tagp); -->
  <function name="nanl,std::nanl">
    <use-retval/>
    <pure/>
    <returnValue type="long double"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- int isnan(double x); -->
  <!-- bool isnan(float x); // since C++11 -->
  <!-- bool isnan(double x); // since C++11 -->
  <!-- bool isnan(long double x); // since C++11 -->
  <!-- bool isnan(Integral x); // since C++11 -->
  <function name="isnan,std::isnan">
    <use-retval/>
    <pure/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int isnormal(double x); -->
  <!--bool isnormal(float x); // since C++11 -->
  <!--bool isnormal(double x); // since C++11 -->
  <!--bool isnormal(long double x); // since C++11 -->
  <!--bool isnormal(Integral x); // since C++11 -->
  <function name="isnormal,std::isnormal">
    <use-retval/>
    <pure/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int isunordered(double x, double y);-->
  <!-- bool isunordered(float x, float y); // since C++11 -->
  <!-- bool isunordered(double x, double y); // since C++11 -->
  <!-- bool isunordered(long double x, long double y); // since C++11 -->
  <!-- bool isunordered(Arithmetic x, Arithmetic y); // since C++11 -->
  <function name="isunordered,std::isunordered">
    <use-retval/>
    <pure/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int ilogb(double x); -->
  <function name="ilogb,std::ilogb">
    <use-retval/>
    <pure/>
    <returnValue type="int">ilogb(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int ilogbf(float x); -->
  <function name="ilogbf,std::ilogbf">
    <use-retval/>
    <pure/>
    <returnValue type="int">ilogb(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int ilogbl(long double x); -->
  <function name="ilogbl,std::ilogbl">
    <use-retval/>
    <pure/>
    <returnValue type="int">ilogb(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double log10(double x); -->
  <function name="log10,std::log10">
    <use-retval/>
    <pure/>
    <returnValue type="double">log10(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>4.94066e-324:</valid>
    </arg>
  </function>
  <!-- float log10f(float x);-->
  <function name="log10f,std::log10f">
    <use-retval/>
    <pure/>
    <returnValue type="float">log10(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>1.4013e-45:</valid>
    </arg>
  </function>
  <!-- long double log10l(long double x); -->
  <function name="log10l,std::log10l">
    <use-retval/>
    <pure/>
    <returnValue type="long double">log10(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>4.94066e-324:</valid>
    </arg>
  </function>
  <!-- double log1p(double x); -->
  <function name="log1p,std::log1p">
    <use-retval/>
    <pure/>
    <returnValue type="double">log1p(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float log1pf(float x); -->
  <function name="log1pf,std::log1pf">
    <use-retval/>
    <pure/>
    <returnValue type="float">log1p(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double log1pl(long double x); -->
  <function name="log1pl,std::log1pl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">log1p(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double log2(double x); -->
  <function name="log2,std::log2">
    <use-retval/>
    <pure/>
    <returnValue type="double">log2(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>4.94066e-324:</valid>
    </arg>
  </function>
  <!-- float log2f(float x); -->
  <function name="log2f,std::log2f">
    <use-retval/>
    <pure/>
    <returnValue type="float">log2(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>1.4013e-45:</valid>
    </arg>
  </function>
  <!-- long double log2l(long double x); -->
  <function name="log2l,std::log2l">
    <use-retval/>
    <pure/>
    <returnValue type="long double">log2(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>4.94066e-324:</valid>
    </arg>
  </function>
  <!-- double nearbyint(double x); -->
  <function name="nearbyint,std::nearbyint">
    <use-retval/>
    <pure/>
    <returnValue type="double">nearbyint(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float nearbyintf(float x); -->
  <function name="nearbyintf,std::nearbyintf">
    <use-retval/>
    <pure/>
    <returnValue type="float">nearbyint(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double nearbyintl(long double x); -->
  <function name="nearbyintl,std::nearbyintl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">nearbyint(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double nextafter(double x, double y); -->
  <function name="nextafter,std::nextafter">
    <use-retval/>
    <pure/>
    <returnValue type="double">nextafter(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float nextafterf(float x, float y); -->
  <function name="nextafterf,std::nextafterf">
    <use-retval/>
    <pure/>
    <returnValue type="float">nextafter(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double nextafterl(long double x, long double y);-->
  <function name="nextafterl,std::nextafterl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">nextafter(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double nexttoward(double x, long double y); -->
  <function name="nexttoward,std::nexttoward">
    <use-retval/>
    <pure/>
    <returnValue type="double">nexttoward(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float nexttowardf(float x, long double y); -->
  <function name="nexttowardf,std::nexttowardf">
    <use-retval/>
    <pure/>
    <returnValue type="float">nexttoward(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double nexttowardl(long double x, long double y); -->
  <function name="nexttowardl,std::nexttowardl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">nexttoward(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void longjmp(jmp_buf env, int val); -->
  <function name="longjmp,std::longjmp">
    <noreturn>true</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void * malloc(size_t size); -->
  <function name="malloc,std::malloc">
    <use-retval/>
    <returnValue type="void *"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- void *alloca(size_t size); -->
  <function name="alloca">
    <use-retval/>
    <returnValue type="void *"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- void *aligned_alloc(size_t alignment, size_t size); -->
  <function name="aligned_alloc,std::aligned_alloc">
    <use-retval/>
    <returnValue type="void *"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- const void * memchr ( const void * ptr, int value, size_t num ); -->
  <!--       void * memchr (       void * ptr, int value, size_t num ); -->
  <function name="memchr,std::memchr">
    <use-retval/>
    <pure/>
    <returnValue type="void *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- wchar_t *wmemchr(const wchar_t *cs, wchar_t c, size_t n);-->
  <function name="wmemchr,std::wmemchr">
    <use-retval/>
    <pure/>
    <returnValue type="wchar_t *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- int memcmp(const void *s1, const void *s2, size_t n);-->
  <function name="memcmp,std::memcmp">
    <use-retval/>
    <pure/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- int wmemcmp (const wchar_t* ptr1, const wchar_t* ptr2, size_t num);-->
  <function name="wmemcmp,std::wmemcmp">
    <use-retval/>
    <pure/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- void * memcpy(void *ct, const void *cs, size_t n);-->
  <function name="memcpy,std::memcpy">
    <returnValue type="void *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <not-overlapping-data ptr1-arg="1" ptr2-arg="2" size-arg="3"/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- wchar_t * wmemcpy(wchar_t *ct, const wchar_t *cs, size_t n);-->
  <function name="wmemcpy,std::wmemcpy">
    <returnValue type="wchar_t *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <not-overlapping-data ptr1-arg="1" ptr2-arg="2" size-arg="3"/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- errno_t  memcpy_s( void *restrict dest, rsize_t destsz, const void *restrict src, rsize_t count );-->
  <!-- errno_t wmemcpy_s( wchar_t *restrict dest, rsize_t destsz, const wchar_t *restrict src, rsize_t count );-->
  <function name="memcpy_s,wmemcpy_s">
    <returnValue type="errno_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- void * memmove(void *ct, const void *cs, size_t n); -->
  <function name="memmove,std::memmove">
    <noreturn>false</noreturn>
    <returnValue type="void *"/>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="2">
      <!-- TODO: direction? memory could be overwritten -->
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- wchar_t * wmemmove(wchar_t *ct, const wchar_t *cs, size_t n); -->
  <function name="wmemmove,std::wmemmove">
    <noreturn>false</noreturn>
    <returnValue type="wchar_t *"/>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="2">
      <!-- TODO: direction? memory could be overwritten -->
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- errno_t memset_s( void *dest, rsize_t destsz, int ch, rsize_t count ) -->
  <function name="memset_s">
    <returnValue type="errno_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- void *memset(void *s, int c, size_t n); -->
  <function name="memset,std::memset">
    <returnValue type="void *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- wchar_t *wmemset(wchar_t *s, wchar_t c, size_t n); -->
  <function name="wmemset,std::wmemset">
    <returnValue type="wchar_t *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- time_t mktime(struct tm *tp); -->
  <!-- time_t mkxtime(struct tmx *tp); -->
  <function name="mktime,std::mktime,mkxtime">
    <returnValue type="time_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- double modf(double x, double *ip); -->
  <function name="modf,std::modf">
    <returnValue type="double"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
    </arg>
  </function>
  <!-- float modff(float x, float *ip); -->
  <function name="modff,std::modff">
    <returnValue type="float"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
    </arg>
  </function>
  <!-- long double modfl(long double x, long double *ip); -->
  <function name="modfl,std::modfl">
    <returnValue type="long double"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
    </arg>
  </function>
  <!-- void perror(const char *string); -->
  <function name="perror,std::perror">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- double pow(double x, double y); -->
  <function name="pow,std::pow">
    <use-retval/>
    <pure/>
    <returnValue type="double">pow(arg1, arg2)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float powf(float x, float y);-->
  <function name="powf,std::powf">
    <use-retval/>
    <pure/>
    <returnValue type="float">pow(arg1, arg2)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double powl(long double x, long double y); -->
  <function name="powl,std::powl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">pow(arg1, arg2)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double complex cpow(double complex x, double complex y); -->
  <!-- float complex cpowf(float complex x, float complex y);-->
  <!-- long double complex cpowl(long double complex x, long double complex y); -->
  <function name="cpow,cpowf,cpowl">
    <use-retval/>
    <pure/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float  remquo (float x , float y , int *quo); -->
  <!-- double remquo (double x, double y, int *quo); -->
  <!-- long double remquo (long double x, long double y, int *quo); -->
  <function name="remquo,std::remquo">
    <use-retval/>
    <pure/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>!0.0</valid>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
    </arg>
  </function>
  <!-- float  remquof (float x , float y , int *quo); -->
  <function name="remquof,std::remquof">
    <use-retval/>
    <pure/>
    <returnValue type="float"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>!0.0</valid>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
    </arg>
  </function>
  <!-- long double  remquol (long double x , long double y , int *quo); -->
  <function name="remquol,std::remquol">
    <use-retval/>
    <pure/>
    <returnValue type="long double"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>!0.0</valid>
    </arg>
    <arg nr="3" direction="out">
      <not-null/>
    </arg>
  </function>
  <!-- double remainder(double x, double y); -->
  <function name="remainder,std::remainder">
    <use-retval/>
    <pure/>
    <returnValue type="double">remainder(arg1,arg2)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>!0.0</valid>
    </arg>
  </function>
  <!-- float remainderf(float x, float y); -->
  <function name="remainderf,std::remainderf">
    <use-retval/>
    <pure/>
    <returnValue type="float">remainder(arg1,arg2)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>!0.0</valid>
    </arg>
  </function>
  <!-- long double remainderl(long double x, long double y); -->
  <function name="remainderl,std::remainderl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">remainder(arg1,arg2)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>!0.0</valid>
    </arg>
  </function>
  <!-- int printf(const char *format, ...); -->
  <function name="printf,std::printf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <formatstr/>
    <arg nr="1" direction="in">
      <formatstr/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- int vprintf(const char *format, va_list arg); -->
  <function name="vprintf,std::vprintf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <formatstr/>
      <strz/>
    </arg>
    <arg nr="2"/>
  </function>
  <!-- int vwprintf(const wchar_t *format, va_list arg); -->
  <function name="vwprintf,std::vwprintf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2"/>
  </function>
  <!-- void* bsearch_s( const void *key, const void *ptr , rsize_t count, rsize_t size, int (*comp)(const void *, const void *, void *), void *context ); since C11-->
  <function name="bsearch_s">
    <use-retval/>
    <pure/>
    <returnValue type="void *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="5" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="6" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- void* bsearch  ( const void* key, const void* base   , size_t num,  size_t size, int(*compar)(const void*,const void*));-->
  <function name="bsearch,std::bsearch">
    <use-retval/>
    <pure/>
    <returnValue type="void *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="5" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- void qsort(void *base, size_t n, size_t size, int (*cmp)(const void *, const void *)); -->
  <function name="qsort,std::qsort">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="4">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int putc(int c, FILE *stream); -->
  <function name="putc,std::putc">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- wint_t putwc(wchar_t wc, FILE* stream); -->
  <function name="putwc,std::putwc">
    <returnValue type="wint_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- int puthchar(int c); -->
  <function name="putchar,std::putchar">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- std::ostream& std::ostream::put(char c);-->
  <function name="std::ostream::put">
    <returnValue type="std::ostream &amp;"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- std::stringstream& std::stringstream::put(char c);-->
  <function name="std::stringstream::put">
    <returnValue type="std::stringstream &amp;"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- wint_t putwchar(wchar_t wc); -->
  <function name="putwchar,std::putwchar">
    <returnValue type="wint_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int puts(const char *string); -->
  <function name="puts,std::puts">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- void *realloc(void *block, size_t newsize); -->
  <function name="realloc,std::realloc">
    <returnValue type="void *"/>
    <use-retval/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- int remove(const char *filename); -->
  <function name="remove,std::remove">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- int rename(const char *oldname, const char *newname); -->
  <function name="rename,std::rename">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- void rewind(FILE *stream); -->
  <function name="rewind,std::rewind">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- double round(double x); -->
  <function name="round,std::round">
    <use-retval/>
    <pure/>
    <returnValue type="double">round(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- float roundf(float x); -->
  <function name="roundf,std::roundf">
    <use-retval/>
    <pure/>
    <returnValue type="float">round(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double roundl(long double x); -->
  <function name="roundl,std::roundl">
    <use-retval/>
    <pure/>
    <returnValue type="long double">round(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- long long int scalbn(double x, int n); -->
  <!-- long long int scalbnf(float x, int n); -->
  <!-- long long int scalbnl(long double x, int n); -->
  <!-- long long int scalbln(double x, long int n); -->
  <!-- long long int scalblnf(float x, long int n); -->
  <!-- long long int scalblnl(long double x, long int n); -->
  <function name="scalbn,std::scalbn,scalbnf,std::scalbnf,scalbnl,std::scalbnl,scalbln,std::scalbln,scalblnf,std::scalblnf,scalblnl,std::scalblnl">
    <use-retval/>
    <pure/>
    <returnValue type="long long int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int signbit(double x); -->
  <function name="signbit,std::signbit">
    <use-retval/>
    <pure/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void (*signal(int sig, void (*func)(int)))(int); -->
  <function name="signal,std::signal">
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2"/>
  </function>
  <!-- int raise(int sig); -->
  <function name="raise,std::raise">
    <returnValue type="int"/>
    <noreturn>maybe</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int scanf(const char *format, ...); -->
  <function name="scanf,std::scanf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <formatstr scan="true"/>
    <arg nr="1" direction="in">
      <formatstr/>
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- int vsscanf(const char *s, const char *format, va_list arg); -->
  <function name="vsscanf,std::vsscanf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3"/>
  </function>
  <!-- int vswscanf(const wchar_t *s, const wchar_t *format, va_list arg); -->
  <function name="vswscanf,std::vswscanf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3"/>
  </function>
  <!-- int vscanf(const char *format, va_list arg); -->
  <function name="vscanf,std::vscanf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2"/>
  </function>
  <!-- int vscanf(const wchar_t *format, va_list arg); -->
  <function name="vwscanf,std::vwscanf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <strz/>
    </arg>
    <arg nr="2"/>
  </function>
  <!-- void setbuf(FILE *stream, char *buf); -->
  <function name="setbuf,std::setbuf">
    <returnValue type="void"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-bool/>
    </arg>
  </function>
  <!-- int setvbuf(FILE* stream, char *buf, int mode, size_t size); -->
  <function name="setvbuf,std::setvbuf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <minsize type="argvalue" arg="4"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <!-- Note: _IOFBF=0, _IOLBF=1 and _IONBF=2  has been set to in this configuration-->
      <valid>0:2</valid>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- int setjmp(jmp_buf env); -->
  <function name="setjmp">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out"/>
  </function>
  <!-- char * strcat(char *deststr, const char *srcstr); -->
  <function name="strcat,std::strcat">
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <not-overlapping-data ptr1-arg="1" ptr2-arg="2" strlen-arg="2"/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- wchar_t wcscat(wchar_t *deststr, const wchar_t *srcstr); -->
  <function name="wcscat,std::wcscat">
    <returnValue type="wchar_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <not-overlapping-data ptr1-arg="1" ptr2-arg="2" strlen-arg="2"/>
    <arg nr="1" direction="inout">
      <not-null/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- size_t wcrtomb(char *s, wchar_t wc, mbstate_t *ps); -->
  <function name="wcrtomb,std::wcrtomb">
    <returnValue type="size_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- char * strchr(const char *cs, int c); -->
  <function name="strchr,std::strchr">
    <use-retval/>
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:255</valid>
    </arg>
  </function>
  <!-- wchar_t* wcschr(const wchar_t *cs, wchar_t c); -->
  <function name="wcschr,std::wcschr">
    <use-retval/>
    <returnValue type="wchar_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int strcmp(const char *str1, const char *str2); -->
  <!-- int wcscmp(const wchar_t *str1, const wchar_t *str2) -->
  <function name="strcmp,std::strcmp,wcscmp,std::wcscmp">
    <use-retval/>
    <pure/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- char *strcpy(char *desstr, const char *srcstr); -->
  <function name="strcpy,std::strcpy">
    <returnValue type="char *">arg1</returnValue>
    <noreturn>false</noreturn>
    <not-overlapping-data ptr1-arg="1" ptr2-arg="2" strlen-arg="2"/>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="strlen" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- errno_t strcpy_s(char *restrict dest, rsize_t destsz, const char *restrict src); // since C11 -->
  <function name="strcpy_s">
    <noreturn>false</noreturn>
    <returnValue type="errno_t"/>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>1:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- wchar_t *wcscpy(wchar_t *deststr, const wchar_t *srcstr); -->
  <function name="wcscpy,std::wcscpy">
    <returnValue type="wchar_t *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <not-overlapping-data ptr1-arg="1" ptr2-arg="2" strlen-arg="2"/>
    <arg nr="1" direction="out">
      <not-null/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- size_t strftime(char *s, size_t max, const char *fmt, const struct tm *p); -->
  <!-- size_t strfxtime(char *s, size_t max, const char *fmt, const struct tmx *p); -->
  <function name="strftime,std::strftime,strfxtime">
    <returnValue type="size_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-null/>
      <not-uninit/>
      <formatstr/>
      <strz/>
    </arg>
    <arg nr="4" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- size_t strlen(const char *string); -->
  <!-- size_t wcslen(const wchar_t *string); -->
  <function name="strlen,std::strlen,wcslen,std::wcslen">
    <use-retval/>
    <pure/>
    <returnValue type="size_t">strlen(arg1)</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- char *strncpy(char *s, const char *ct, size_t n); -->
  <function name="strncpy,std::strncpy">
    <returnValue type="char *">arg1</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <not-overlapping-data ptr1-arg="1" ptr2-arg="2" size-arg="3"/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- errno_t strncpy_s(char *restrict dest, rsize_t destsz, const char *restrict src, rsize_t count); // since C11 -->
  <!-- errno_t wcsncpy_s(wchar_t *restrict dest, rsize_t destsz, const wchar_t *restrict src, rsize_t n); // since C11 -->
  <function name="strncpy_s,wcsncpy_s">
    <noreturn>false</noreturn>
    <returnValue type="errno_t"/>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="4"/>
      <strz/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- const char *strpbrk(const char *str1, const char *str2); -->
  <!--       char *strpbrk(      char *str1, const char *str2); -->
  <function name="strpbrk,std::strpbrk">
    <use-retval/>
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- char * strncat(char *ct, const char *s, size_t n); -->
  <function name="strncat,std::strncat">
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <not-overlapping-data ptr1-arg="1" ptr2-arg="2" size-arg="3"/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
      <strz/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- errno_t strcat_s(char *restrict dest, rsize_t destsz, const char *restrict src); // since C11-->
  <!-- errno_t wcscat_s(wchar_t *restrict dest, rsize_t destsz, const wchar_t *restrict src); // since C11-->
  <function name="strcat_s,wcscat_s">
    <returnValue type="errno_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
      <strz/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- errno_t strncat_s(char *restrict dest, rsize_t destsz, const char *restrict src, rsize_t count); // since C11 -->
  <function name="strncat_s">
    <noreturn>false</noreturn>
    <returnValue type="errno_t"/>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="4"/>
      <strz/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- wchar_t *wcsncat(wchar_t *ct, const wchar_t *cs, size_t n); -->
  <function name="wcsncat,std::wcsncat">
    <noreturn>false</noreturn>
    <returnValue type="wchar_t *"/>
    <leak-ignore/>
    <not-overlapping-data ptr1-arg="1" ptr2-arg="2" size-arg="3"/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
      <strz/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- int strncmp(const char *s1, const char *s2, size_t n); -->
  <!-- int wcsncmp(const wchar_t* wcs1, const wchar_t* wcs2, size_t num); -->
  <function name="strncmp,std::strncmp,wcsncmp,std::wcsncmp">
    <use-retval/>
    <pure/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- char* strstr(const char *s1, const char *s2); -->
  <function name="strstr,std::strstr">
    <use-retval/>
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- wchar_t *wcsstr(const wchar_t *s1, const wchar_t *s2); -->
  <function name="wcsstr,std::wcsstr">
    <use-retval/>
    <pure/>
    <returnValue type="wchar_t *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- size_t strspn(const char *cs, const char *ct); -->
  <function name="strspn,std::strspn">
    <use-retval/>
    <returnValue type="size_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- size_t strxfrm(char *ds, const char *ss, size_t n); -->
  <function name="strxfrm,std::strxfrm">
    <returnValue type="size_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <!-- In case the 3rd argument is 0, the 1st argument is permitted to be a null pointer. (#6306) -->
    <arg nr="1" direction="out">
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- size_t wcsxfrm(wchar_t *s1, const wchar_t *s2, size_t n); -->
  <function name="wcsxfrm,std::wcsxfrm">
    <returnValue type="size_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <not-overlapping-data ptr1-arg="1" ptr2-arg="2" size-arg="3"/>
    <!-- In case the 3rd argument is 0, the 1st argument is permitted to be a null pointer. (#6306) -->
    <arg nr="1" direction="out"/>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- size_t wcsspn(const wchar_t *s1, const wchar_t *s2); -->
  <function name="wcsspn,std::wcsspn">
    <use-retval/>
    <pure/>
    <returnValue type="size_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- struct lconv* localeconv(void); -->
  <function name="localeconv,std::localeconv">
    <use-retval/>
    <returnValue type="struct lconv*"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
  </function>
  <!-- static std::locale std::locale::global( const std::locale& loc ); -->
  <function name="std::locale::global">
    <returnValue type="struct std::locale"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- static std::locale& std::locale::classic(); -->
  <function name="std::locale::classic">
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
  </function>
  <!-- char* setlocale(int category, const char* locale); -->
  <function name="setlocale,std::setlocale">
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- char * strerror(int errornum); -->
  <function name="strerror,std::strerror">
    <use-retval/>
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- size_t strcspn(const char *cs, const char *ct); -->
  <function name="strcspn,std::strcspn">
    <use-retval/>
    <pure/>
    <returnValue type="size_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- size_t wcscspn(const wchar_t *cs, const wchar_t *ct); -->
  <function name="wcscspn,std::wcscspn">
    <use-retval/>
    <pure/>
    <returnValue type="size_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- wchar_t * wcspbrk(const wchar_t *ct, wchar_t *c); -->
  <function name="wcspbrk,std::wcspbrk">
    <use-retval/>
    <returnValue type="wchar_t *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- wchar_t * wcsncpy(wchar_t *s, const wchar_t *cs, size_t n); -->
  <function name="wcsncpy,std::wcsncpy">
    <returnValue type="wchar_t *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <not-overlapping-data ptr1-arg="1" ptr2-arg="2" size-arg="3"/>
    <arg nr="1">
      <not-null/>
      <minsize type="argvalue" arg="3" baseType="wchar_t"/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- int strcoll(const char *cs, const char *c); -->
  <function name="strcoll,std::strcoll">
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- int wcscoll(const wchar_t *s1, const wchar_t *s2); -->
  <function name="wcscoll,std::wcscoll">
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- const char * strrchr(const char * str, int character);-->
  <!-- char * strrchr(char * str, int character); -->
  <function name="strrchr,std::strrchr">
    <use-retval/>
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:255</valid>
    </arg>
  </function>
  <!-- const wchar_t* wcsrchr(const wchar_t* ws, wchar_t wc);-->
  <!--       wchar_t* wcsrchr(wchar_t* ws, wchar_t wc); -->
  <function name="wcsrchr,std::wcsrchr">
    <use-retval/>
    <returnValue type="wchar_t *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- size_t wcsrtombs(char *dst, const wchar_t **src, size_t len, mbstate_t *ps); -->
  <function name="wcsrtombs,std::wcsrtombs">
    <returnValue type="size_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="inout">
      <not-null/>
    </arg>
  </function>
  <!-- char *strtok(char *s, const char *ct); -->
  <function name="strtok,std::strtok">
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- float strtof(const char *s, char **endp); -->
  <function name="strtof,std::strtof">
    <returnValue type="float"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="out"/>
  </function>
  <!-- double strtod(const char *s, char **endp); -->
  <function name="strtod,std::strtod">
    <returnValue type="double"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="out"/>
  </function>
  <!-- long double strtold(const char *s, char **endp); -->
  <function name="strtold,std::strtold">
    <!-- TODO #8387: use-retval is only valid when arg2==NULL -->
    <returnValue type="long double"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="out"/>
  </function>
  <!-- long strtol(const char *s, char **endp, int base); -->
  <function name="strtol,std::strtol">
    <!-- TODO #8387: use-retval is only valid when arg2==NULL -->
    <returnValue type="long"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="out"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0,2:36</valid>
    </arg>
  </function>
  <!-- unsigned long strtoul(const char *s, char **endp, int base); -->
  <function name="strtoul,std::strtoul">
    <!-- TODO #8387: use-retval is only valid when arg2==NULL -->
    <returnValue type="unsigned long"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="out"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0,2:36</valid>
    </arg>
  </function>
  <!-- long long strtoll(const char *s, char **endp, int base); -->
  <function name="strtoll,std::strtoll">
    <!-- TODO #8387: use-retval is only valid when arg2==NULL -->
    <returnValue type="long long"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="out"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0,2:36</valid>
    </arg>
  </function>
  <!-- unsigned long long strtoull(const char *s, char **endp, int base); -->
  <function name="strtoull,std::strtoull">
    <!-- TODO #8387: use-retval is only valid when arg2==NULL -->
    <returnValue type="unsigned long long"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="out"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0,2:36</valid>
    </arg>
  </function>
  <!-- intmax_t strtoimax (const char* str, char** endptr, int base); -->
  <function name="strtoimax,std::strtoimax">
    <!-- TODO #8387: use-retval is only valid when arg2==NULL -->
    <returnValue type="intmax_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="out"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0,2:36</valid>
    </arg>
  </function>
  <!-- uintmax_t strtoumax (const char* str, char** endptr, int base); -->
  <function name="strtoumax,std::strtoumax">
    <!-- TODO #8387: use-retval is only valid when arg2==NULL -->
    <returnValue type="uintmax_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="out"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0,2:36</valid>
    </arg>
  </function>
  <!-- time_t time(time_t *tp); -->
  <function name="time,std::time">
    <returnValue type="time_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-uninit/>
    </arg>
  </function>
  <!-- char *tmpnam(char *s); -->
  <function name="tmpnam,std::tmpnam">
    <returnValue type="char *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-uninit/>
    </arg>
  </function>
  <!-- errno_t tmpnam_s(char *filename_s, rsize_t maxsize); -->
  <function name="tmpnam_s">
    <returnValue type="errno_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-uninit/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- FILE *tmpfile(void); -->
  <function name="tmpfile">
    <use-retval/>
    <returnValue type="FILE *"/>
    <noreturn>false</noreturn>
  </function>
  <!-- int tolower(int c); -->
  <function name="tolower,std::tolower">
    <use-retval/>
    <returnValue type="int">arg1 &lt; 'A' || arg1 &gt; 'Z' ? arg1 : arg1 + 32</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:255</valid>
    </arg>
  </function>
  <!-- int toupper(int c); -->
  <function name="toupper,std::toupper">
    <use-retval/>
    <returnValue type="int">arg1 &lt; 'a' || arg1 &gt; 'z' ? arg1 : arg1 - 32</returnValue>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:255</valid>
    </arg>
  </function>
  <!-- typeid operator -->
  <function name="typeid">
    <use-retval/>
    <noreturn>false</noreturn>
    <arg nr="1"/>
  </function>
  <!-- type va_arg(va_list ap, type); -->
  <function name="va_arg">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="2"/>
  </function>
  <!-- void va_copy(va_list dest, va_list src); -->
  <function name="va_copy">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="2"/>
  </function>
  <!-- void va_end(va_list ap); -->
  <function name="va_end">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1"/>
  </function>
  <!-- void va_start(va_list ap, paramN); -->
  <function name="va_start">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="2"/>
  </function>
  <!-- float wcstof(const wchar_t *s, wchar ** endp); -->
  <function name="wcstof,std::wcstof">
    <use-retval/>
    <returnValue type="float"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="out"/>
  </function>
  <!-- double wcstod(const wchar_t *s, wchar ** endp); -->
  <function name="wcstod,std::wcstod">
    <use-retval/>
    <returnValue type="double"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="out"/>
  </function>
  <!-- long double wcstold(const wchar_t *s, wchar ** endp); -->
  <function name="wcstold,std::wcstold">
    <use-retval/>
    <returnValue type="long double"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="out"/>
  </function>
  <!-- float stof (const string&  str, size_t* idx = 0); -->
  <!-- float stof (const wstring& str, size_t* idx = 0); -->
  <function name="stof,std::stof">
    <use-retval/>
    <returnValue type="float"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0" direction="out">
      <not-uninit/>
    </arg>
  </function>
  <!-- double stod (const string&  str, size_t* idx = 0); -->
  <!-- double stod (const wstring& str, size_t* idx = 0); -->
  <function name="stod,std::stod">
    <use-retval/>
    <returnValue type="double"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0" direction="out">
      <not-uninit/>
    </arg>
  </function>
  <!-- long double stold (const string&  str, size_t* idx = 0); -->
  <!-- long double stold (const wstring& str, size_t* idx = 0); -->
  <function name="stold,std::stold">
    <use-retval/>
    <returnValue type="long double"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0" direction="out">
      <not-uninit/>
    </arg>
  </function>
  <!-- int stoi (const string&  str, size_t* idx = 0, int base = 10);-->
  <!-- int stoi (const wstring& str, size_t* idx = 0, int base = 10); -->
  <function name="stoi,std::stoi">
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0" direction="out">
      <not-uninit/>
    </arg>
    <arg nr="3" default="10" direction="in">
      <not-uninit/>
      <valid>0,2:36</valid>
    </arg>
  </function>
  <!-- long stol (const string&  str, size_t* idx = 0, int base = 10);-->
  <!-- long stol (const wstring& str, size_t* idx = 0, int base = 10); -->
  <function name="stol,std::stol">
    <use-retval/>
    <returnValue type="long"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0" direction="out">
      <not-uninit/>
    </arg>
    <arg nr="3" default="10" direction="in">
      <not-uninit/>
      <valid>0,2:36</valid>
    </arg>
  </function>
  <!-- long long stoll (const string&  str, size_t* idx = 0, int base = 10);-->
  <!-- long long stoll (const wstring& str, size_t* idx = 0, int base = 10); -->
  <function name="stoll,std::stoll">
    <use-retval/>
    <returnValue type="long long"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0" direction="out">
      <not-uninit/>
    </arg>
    <arg nr="3" default="10" direction="in">
      <not-uninit/>
      <valid>0,2:36</valid>
    </arg>
  </function>
  <!-- unsigned long stoul (const string&  str, size_t* idx = 0, int base = 10);-->
  <!-- unsigned long stoul (const wstring& str, size_t* idx = 0, int base = 10); -->
  <function name="stoul,std::stoul">
    <use-retval/>
    <returnValue type="unsigned long"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0" direction="out">
      <not-uninit/>
    </arg>
    <arg nr="3" default="10" direction="in">
      <not-uninit/>
      <valid>0,2:36</valid>
    </arg>
  </function>
  <!-- unsigned long long stoull (const string&  str, size_t* idx = 0, int base = 10);-->
  <!-- unsigned long long stoull (const wstring& str, size_t* idx = 0, int base = 10); -->
  <function name="stoull,std::stoull">
    <use-retval/>
    <returnValue type="unsigned long long"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0" direction="out">
      <not-uninit/>
    </arg>
    <arg nr="3" default="10" direction="in">
      <not-uninit/>
      <valid>0,2:36</valid>
    </arg>
  </function>
  <!-- string to_string (int val); -->
  <!-- string to_string (long val); -->
  <!-- string to_string (long long val); -->
  <!-- string to_string (unsigned val); -->
  <!-- string to_string (unsigned long val); -->
  <!-- string to_string (unsigned long long val); -->
  <!-- string to_string (float val); -->
  <!-- string to_string (double val); -->
  <!-- string to_string (long double val);-->
  <function name="std::to_string">
    <use-retval/>
    <returnValue type="std::string"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void std::string::resize (size_t n);-->
  <!-- void std::string::resize (size_t n, char c);-->
  <!-- void std::wstring::resize (size_t n);-->
  <!-- void std::wstring::resize (size_t n, wchar_t c);-->
  <function name="std::string::resize,std::wstring::resize">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" default=" " direction="in">
      <not-uninit/>
      <valid>0:255</valid>
    </arg>
  </function>
  <!-- wstring to_wstring (int val); -->
  <!-- wstring to_wstring (long val); -->
  <!-- wstring to_wstring (long long val); -->
  <!-- wstring to_wstring (unsigned val); -->
  <!-- wstring to_wstring (unsigned long val); -->
  <!-- wstring to_wstring (unsigned long long val); -->
  <!-- wstring to_wstring (float val); -->
  <!-- wstring to_wstring (double val); -->
  <!-- wstring to_wstring (long double val);-->
  <function name="std::to_wstring">
    <use-retval/>
    <returnValue type="std::wstring"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- size_t mbrtowc(wchar_t* pwc, const char* pmb, size_t max, mbstate_t* ps); -->
  <function name="mbrtowc,std::mbrtowc">
    <returnValue type="size_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="inout">
      <not-uninit/>
    </arg>
  </function>
  <!-- wchar_t* wcstok(wchar_t *s, const wchar_t *ct, wchar_t **ptr); -->
  <function name="wcstok,std::wcstok">
    <returnValue type="wchar_t *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3" direction="inout">
      <not-null/>
    </arg>
  </function>
  <!-- intmax_t wcstoimax(const wchar_t *s, wchar_t ** endp, int base); -->
  <function name="wcstoimax,std::wcstoimax">
    <use-retval/>
    <returnValue type="intmax_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="out"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0,2:36</valid>
    </arg>
  </function>
  <!-- uintmax_t wcstoumax(const wchar_t *s, wchar_t ** endp, int base); -->
  <function name="wcstoumax,std::wcstoumax">
    <use-retval/>
    <returnValue type="uintmax_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="out"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0,2:36</valid>
    </arg>
  </function>
  <!-- long wcstol(const wchar_t *s, wchar ** endp, int base); -->
  <function name="wcstol,std::wcstol">
    <!-- TODO #8387: use-retval is only valid when arg2==NULL -->
    <returnValue type="long"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="out"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0,2:36</valid>
    </arg>
  </function>
  <!-- long long wcstoll(const wchar_t *s, wchar ** endp, int base); -->
  <function name="wcstoll,std::wcstoll">
    <!-- TODO #8387: use-retval is only valid when arg2==NULL -->
    <returnValue type="long long"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="out"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0,2:36</valid>
    </arg>
  </function>
  <!-- unsigned long wcstoul(const wchar_t *s, wchar ** endp, int base); -->
  <function name="wcstoul,std::wcstoul">
    <!-- TODO #8387: use-retval is only valid when arg2==NULL -->
    <returnValue type="unsigned long"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="out"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0,2:36</valid>
    </arg>
  </function>
  <!-- unsigned long long wcstoull(const wchar_t *s, wchar ** endp, int base); -->
  <function name="wcstoull,std::wcstoull">
    <!-- TODO #8387: use-retval is only valid when arg2==NULL -->
    <returnValue type="unsigned long long"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="out"/>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0,2:36</valid>
    </arg>
  </function>
  <!-- int wprintf(const wchar_t *format, ...); -->
  <function name="wprintf,std::wprintf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <formatstr/>
    <arg nr="1" direction="in">
      <formatstr/>
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- int sprintf(char *s, const char *format, ...); -->
  <function name="sprintf,std::sprintf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-uninit/>
      <minsize type="strlen" arg="2"/>
    </arg>
    <formatstr/>
    <arg nr="2" direction="in">
      <formatstr/>
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- int sprintf_s(char *restrict buffer, rsize_t bufsz, const char *restrict format, ...); // since C11 // TODO: conflicts with the Windows functions where it is overloaded for C++ also -->
  <!-- int snprintf_s(char *restrict buffer, rsize_t bufsz, const char *restrict format, ...); // since C11 -->
  <function name="snprintf_s">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-uninit/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <formatstr/>
    <arg nr="3" direction="in">
      <formatstr/>
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- int swprintf(wchar_t *s, size_t n, const wchar_t *format, ...); -->
  <function name="swprintf,std::swprintf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-uninit/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
      <strz/>
    </arg>
    <arg nr="3" direction="in">
      <!-- Due to different swprintf definitions "<formatstr/>" can not be used here. See https://trac.cppcheck.net/ticket/4790 -->
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- int vsprintf(char *s, const char *format, va_list arg); -->
  <function name="vsprintf,std::vsprintf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <formatstr/>
      <strz/>
    </arg>
    <arg nr="3"/>
  </function>
  <!-- int vswprintf(wchar_t *s, size_t n, const wchar_t *format, va_list arg); -->
  <function name="vswprintf,std::vswprintf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-null/>
      <not-uninit/>
      <formatstr/>
      <strz/>
    </arg>
    <arg nr="4"/>
  </function>
  <!-- int fwprintf(FILE* stream, const wchar_t* format, ...); -->
  <function name="fwprintf,std::fwprintf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
    </arg>
    <formatstr/>
    <arg nr="2" direction="in">
      <formatstr/>
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- int snprintf(char *s, size_t n, const char *format, ...); -->
  <function name="snprintf,std::snprintf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-uninit/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <formatstr/>
    <arg nr="3" direction="in">
      <formatstr/>
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- int vsnprintf(char *s, size_t n, const char *format, va_list arg); -->
  <function name="vsnprintf,std::vsnprintf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-uninit/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-null/>
      <not-uninit/>
      <formatstr/>
      <strz/>
    </arg>
    <arg nr="4"/>
  </function>
  <!-- int wscanf(const wchar_t *format, ...); -->
  <function name="wscanf,std::wscanf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <formatstr scan="true"/>
    <arg nr="1" direction="in">
      <formatstr/>
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- int sscanf(const char *string, const char * format, ...); -->
  <function name="sscanf,std::sscanf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <strz/>
    </arg>
    <formatstr scan="true"/>
    <arg nr="2" direction="in">
      <formatstr/>
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- int fwscanf(FILE* stream, const wchar_t* format, ...); -->
  <function name="fwscanf,std::fwscanf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <formatstr scan="true"/>
    <arg nr="2" direction="in">
      <formatstr/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- int swscanf(const wchar_t *string, const wchar_t *format, ...); -->
  <function name="swscanf,std::swscanf">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <strz/>
    </arg>
    <formatstr scan="true"/>
    <arg nr="2" direction="in">
      <formatstr/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- int system(const char *command); -->
  <function name="system,std::system">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <!-- If a null pointer is given, command processor is checked for existence -->
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- /*unspecified*/ setw(int n); -->
  <function name="setw,std::setw">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- template <class T> std::numeric_limits<T>::epsilon(void); -->
  <function name="std::numeric_limits::epsilon">
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <pure/>
  </function>
  <!-- static std::chrono::steady_clock::time_point std::chrono::high_resolution_clock::now() noexcept;-->
  <function name="std::chrono::high_resolution_clock::now">
    <use-retval/>
    <returnValue type="std::chrono::steady_clock::time_point"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
  </function>
  <!-- static std::chrono::system_clock::time_point std::chrono::system_clock::now() noexcept;-->
  <function name="std::chrono::system_clock::now">
    <use-retval/>
    <returnValue type="std::chrono::system_clock::time_point"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
  </function>
  <!-- static std::time_t std::chrono::system_clock::to_time_t( const time_point& t ) noexcept;-->
  <function name="std::chrono::system_clock::to_time_t">
    <use-retval/>
    <returnValue type="std::time_t"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in"/>
    <leak-ignore/>
  </function>
  <!-- template <class T> const T& min(const T& a, const T& b); -->
  <function name="min,std::min">
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <pure/>
    <returnValue>arg1&lt;arg2?arg1:arg2</returnValue>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- template <class T> const T& max(const T& a, const T& b); -->
  <function name="max,std::max">
    <use-retval/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <pure/>
    <returnValue>arg1&gt;arg2?arg1:arg2</returnValue>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- /*unspecified*/ setiosflags(ios_base::fmtflags mask); -->
  <function name="setiosflags,std::setiosflags">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- /*unspecified*/ resetiosflags(ios_base::fmtflags mask); -->
  <function name="resetiosflags,std::resetiosflags">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- /*unspecified*/ setfill(char_type c); -->
  <function name="setfill,std::setfill">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- char std::ios::fill() const; -->
  <!-- char std::ios::fill(char_type c); -->
  <!-- char std::ostringstream::fill() const; -->
  <!-- char std::ostringstream::fill(char_type c); -->
  <function name="std::ios::fill,std::ostringstream::fill">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <use-retval/>
    <arg nr="1" direction="in" default="">
      <not-uninit/>
    </arg>
  </function>
 <!-- std::ios_base::fmtflags std::ios_base::flags() const;                          -->
 <!-- std::ios_base::fmtflags std::ios_base::flags( std::ios_base::fmtflags flags ); -->
  <function name="std::ios_base::flags">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::ios_base::fmtflags"/>
    <arg nr="1" direction="in" default="">
      <not-uninit/>
    </arg>
  </function>
 <!-- std::ostream::fmtflags std::ostream::flags() const;                          -->
 <!-- std::ostream::fmtflags std::ostream::flags( std::ostream::fmtflags flags );  -->
  <function name="std::ostream::flags">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::ostream::fmtflags"/>
    <arg nr="1" direction="in" default="">
      <not-uninit/>
    </arg>
  </function>
  <!-- std::ios_base::fmtflags std::ios_base::setf( std::ios_base::fmtflags flags ); -->
  <!-- std::ios_base::fmtflags std::ios_base::setf( std::ios_base::fmtflags flags, std::ios_base::fmtflags mask ); -->
  <!-- std::ios_base::fmtflags std::ostringstream::setf( std::ios_base::fmtflags flags ); -->
  <!-- std::ios_base::fmtflags std::ostringstream::setf( std::ios_base::fmtflags flags, std::ios_base::fmtflags mask ); -->
  <function name="std::ios_base::setf,std::ostringstream::setf">
    <noreturn>false</noreturn>
    <returnValue type="std::ios_base::fmtflags"/>
    <leak-ignore/>
    <arg nr="any" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- /*unspecified*/ setprecision(int n); -->
  <function name="setprecision,std::setprecision">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!--std::streamsize std::ios_base::precision(streamsize prec)-->
  <!--std::streamsize std::ostringstream::precision(streamsize prec)-->
  <!--std::streamsize std::ostream::precision(streamsize prec)-->
  <function name="std::ios_base::precision,std::ostringstream::precision,std::ostream::precision,std::ofstream::precision">
    <noreturn>false</noreturn>
    <returnValue type="std::streamsize"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!--void std::ios_base::unsetf (fmtflags mask);-->
  <!--void std::ifstream::unsetf (fmtflags mask);-->
  <function name="std::ios_base::unsetf,std::ifstream::unsetf">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- single character (1)           int std::istream::get ();
                            std::istream& std::istream::get (char& c);
       c-string (2)         std::istream& std::istream::get (char* s, streamsize n);
                            std::istream& std::istream::get (char* s, streamsize n, char delim);
       stream buffer (3)    std::istream& std::istream::get (streambuf& sb);
                            std::istream& std::istream::get (streambuf& sb, char delim);-->
  <!-- There is currently no way to define this properly because Cppcheck lacks support for
       overloaded functions.-->
  <function name="std::istream::get,std::istringstream::get,std::ifstream::get">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
      <strz/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" default="'\n'" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- /*unspecified*/ setbase(int base); -->
  <function name="setbase,std::setbase">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- struct tmx *zonetime(const time_t *tp, int zone); -->
  <function name="zonetime">
    <use-retval/>
    <returnValue type="struct tmx *"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- size_t c16rtomb ( char * pmb, char16_t c16, mbstate_t * ps ); -->
  <!-- size_t c32rtomb ( char * pmb, char32_t c32, mbstate_t * ps ); -->
  <function name="c16rtomb,c32rtomb">
    <returnValue type="size_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- size_t mbrtoc16 ( char16_t * pc16, const char * pmb, size_t max, mbstate_t * ps); -->
  <!-- size_t mbrtoc32 ( char32_t * pc32, const char * pmb, size_t max, mbstate_t * ps); -->
  <function name="mbrtoc16,mbrtoc32">
    <returnValue type="size_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- (1) (since C++17) bool std::filesystem::exists( std::filesystem::file_status s ) noexcept; -->
  <!-- (2) (since C++17) bool std::filesystem::exists( const std::filesystem::path& p ); -->
  <!-- (3) (since C++17) bool std::filesystem::exists( const std::filesystem::path& p, std::error_code& ec ) noexcept; -->
  <!-- (1) (since C++17) bool std::filesystem::is_block_file( std::filesystem::file_status s ) noexcept; -->
  <!-- (2) (since C++17) bool std::filesystem::is_block_file( const std::filesystem::path& p ); -->
  <!-- (3) (since C++17) bool std::filesystem::is_block_file( const std::filesystem::path& p, std::error_code& ec ) noexcept; -->
  <!-- (1) (since C++17) bool std::filesystem::is_character_file( std::filesystem::file_status s ) noexcept; -->
  <!-- (2) (since C++17) bool std::filesystem::is_character_file( const std::filesystem::path& p ); -->
  <!-- (3) (since C++17) bool std::filesystem::is_character_file( const std::filesystem::path& p, std::error_code& ec ) noexcept; -->
  <!-- (1) (since C++17) bool std::filesystem::is_directory( std::filesystem::file_status s ) noexcept; -->
  <!-- (2) (since C++17) bool std::filesystem::is_directory( const std::filesystem::path& p ); -->
  <!-- (3) (since C++17) bool std::filesystem::is_directory( const std::filesystem::path& p, std::error_code& ec ) noexcept; -->
  <!-- (1) (since C++17) bool std::filesystem::is_empty const std::filesystem::path& p ); -->
  <!-- (2) (since C++17) bool std::filesystem::is_empty( const std::filesystem::path& p, std::error_code& ec ) noexcept; -->
  <!-- (1) (since C++17) bool std::filesystem::is_fifo( std::filesystem::file_status s ) noexcept; -->
  <!-- (2) (since C++17) bool std::filesystem::is_fifo const std::filesystem::path& p ); -->
  <!-- (3) (since C++17) bool std::filesystem::is_fifo( const std::filesystem::path& p, std::error_code& ec ) noexcept; -->
  <!-- (1) (since C++17) bool std::filesystem::is_other( std::filesystem::file_status s ) noexcept; -->
  <!-- (2) (since C++17) bool std::filesystem::is_other const std::filesystem::path& p ); -->
  <!-- (3) (since C++17) bool std::filesystem::is_other( const std::filesystem::path& p, std::error_code& ec ) noexcept; -->
  <!-- (1) (since C++17) bool std::filesystem::is_regular_file( std::filesystem::file_status s ) noexcept; -->
  <!-- (2) (since C++17) bool std::filesystem::is_regular_file( const std::filesystem::path& p ); -->
  <!-- (3) (since C++17) bool std::filesystem::is_regular_file( const std::filesystem::path& p, std::error_code& ec ) noexcept; -->
  <!-- (1) (since C++17) bool std::filesystem::is_socket( std::filesystem::file_status s ) noexcept; -->
  <!-- (2) (since C++17) bool std::filesystem::is_socket( const std::filesystem::path& p ); -->
  <!-- (3) (since C++17) bool std::filesystem::is_socket( const std::filesystem::path& p, std::error_code& ec ) noexcept; -->
  <!-- (1) (since C++17) bool std::filesystem::is_symlink( std::filesystem::file_status s ) noexcept; -->
  <!-- (2) (since C++17) bool std::filesystem::is_symlink( const std::filesystem::path& p ); -->
  <!-- (3) (since C++17) bool std::filesystem::is_symlink( const std::filesystem::path& p, std::error_code& ec ) noexcept; -->
  <function name="std::filesystem::exists,std::filesystem::is_block_file,std::filesystem::is_character_file,std::filesystem::is_directory,std::filesystem::is_empty,std::filesystem::is_fifo,std::filesystem::is_other,std::filesystem::is_regular_file,std::filesystem::is_socket,std::filesystem::is_symlink">
    <returnValue type="bool"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <use-retval/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="out" default=""/>
  </function>
  <!-- std::string std::ostringstream::str() const; -->
  <!-- std::string std::stringstream::str() const; -->
  <!-- std::string std::basic_stringstream::str() const; -->
  <!-- @todo Implement the second version when function overloading is possible: -->
  <!--       void std::ostringstream::str(const string & s); -->
  <function name="std::istringstream::str,std::ostringstream::str,std::stringstream::str,std::basic_stringstream::str">
    <use-retval/>
    <const/>
    <returnValue type="std::string"/>
    <noreturn>false</noreturn>
  </function>
  <!-- std::ios::pos_type std::istream::tellg( void ); -->
  <!-- std::ios::pos_type std::ifstream::tellg( void ); -->
  <function name="std::istream::tellg,std::basic_istream::tellg,std::ifstream::tellg,std::basic_ifstream::tellg">
    <use-retval/>
    <const/>
    <returnValue type="std::ios::pos_type"/>
    <noreturn>false</noreturn>
  </function>
  <!-- bool good() const; -->
  <function name="std::ios::good,std::wifstream::good,std::ios_base::good,std::ostream::good,std::ofstream::good,std::ostringstream::good,std::ifstream::good,std::istream::good,std::istringstream::good,std::wistringstream::good,std::basic_istringstream::good">
    <use-retval/>
    <const/>
    <returnValue type="bool"/>
    <noreturn>false</noreturn>
  </function>
  <!-- bool eof() const; -->
  <function name="std::ios::eof,std::ios_base::eof,std::ostream::eof,std::ofstream::eof,std::ostringstream::eof,std::istringstream::eof,std::wistringstream::eof,std::basic_istringstream::eof,std::istream::eof,std::ifstream::eof">
    <use-retval/>
    <const/>
    <returnValue type="bool"/>
    <noreturn>false</noreturn>
  </function>
  <!-- bool fail() const; -->
  <function name="std::ios::fail,std::ios_base::fail,std::ostream::fail,std::ofstream::fail,std::ostringstream::fail,std::ifstream::fail,std::istringstream::fail,std::wistringstream::fail,std::basic_istringstream::fail,std::istream::fail">
    <use-retval/>
    <const/>
    <returnValue type="bool"/>
    <noreturn>false</noreturn>
  </function>
  <!-- bool bad() const; -->
  <function name="std::ios::bad,std::wifstream::bad,std::ios_base::bad,std::ostream::bad,std::ofstream::bad,std::ostringstream::bad,std::ifstream::bad,std::istringstream::bad,std::wistringstream::bad,std::basic_istringstream::bad,std::istream::bad">
    <use-retval/>
    <const/>
    <returnValue type="bool"/>
    <noreturn>false</noreturn>
  </function>
  <!-- bool is_open() const; -->
  <function name="std::basic_ifstream::is_open,std::ifstream::is_open,std::basic_ofstream::is_open,std::ofstream::is_open,std::wifstream::is_open">
    <use-retval/>
    <const/>
    <returnValue type="bool"/>
    <noreturn>false</noreturn>
  </function>
  <!--std::ofstream& std::ofstream::write (const char* s, streamsize n);-->
  <function name="std::ofstream::write">
    <returnValue type="std::ofstream &amp;"/>
    <leak-ignore/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!--std::ostringstream& std::ostringstream::write (const char* s, streamsize n);-->
  <function name="std::ostringstream::write">
    <returnValue type="std::ostringstream &amp;"/>
    <leak-ignore/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!--https://en.cppreference.com/w/cpp/io/manip/quoted -->
  <!--template< class CharT >
      /*unspecified*/ quoted( const CharT* s,
                              CharT delim = CharT('"'), CharT escape = CharT('\\') ); (1)(since C++14)
      template< class CharT, class Traits, class Allocator >
      /*unspecified*/ quoted( const std::basic_string<CharT, Traits, Allocator>& s,
                              CharT delim = CharT('"'), CharT escape = CharT('\\') ); (2)(since C++14)
      template< class CharT, class Traits>
      /*unspecified*/ quoted( std::basic_string_view<CharT, Traits> s,
                              CharT delim = CharT('"'), CharT escape = CharT('\\') ); (3)(since C++17)
      template< class CharT, class Traits, class Allocator >
      /*unspecified*/ quoted( std::basic_string<CharT, Traits, Allocator>& s,
                              CharT delim=CharT('"'), CharT escape=CharT('\\') ); (4)(since C++14) -->
  <function name="std::quoted">
    <!-- This function returns an object of unspecified type -->
    <leak-ignore/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in" default="&apos;&quot;&apos;">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in" default="&apos;\\&apos;">
      <not-uninit/>
    </arg>
  </function>
  <!--std::ostream& std::ostream::write (const char* s, streamsize n);-->
  <function name="std::ostream::write">
    <returnValue type="std::ostream &amp;"/>
    <leak-ignore/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!--std::fstream& std::fstream::write (const char* s, streamsize n);-->
  <function name="std::fstream::write">
    <returnValue type="std::fstream &amp;"/>
    <leak-ignore/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- void std::vector::assign( size_type count, const T& value ); -->
  <!-- template< class InputIt > void std::vector::assign( InputIt first, InputIt last ); -->
  <!-- void std::vector::assign( std::initializer_list<T> ilist ); // since C++11 -->
  <function name="std::vector::assign">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0">
      <not-uninit/>
    </arg>
  </function>
  <!-- void std::deque::push_back( const T& value ); -->
  <!-- void std::deque::push_back( T&& value ); // since C++11 -->
  <!-- void std::deque::push_front( const T& value ); -->
  <!-- void std::deque::push_front( T&& value ); // since C++11 -->
  <!-- void std::list::push_back( const T& value ); -->
  <!-- void std::list::push_back( T&& value ); // since C++11 -->
  <!-- void std::list::push_front( const T& value ); -->
  <!-- void std::list::push_front( T&& value ); // since C++11 -->
  <!-- void std::forward_list::push_front( const T& value ); // since C++11 -->
  <!-- void std::forward_list::push_front( T&& value ); // since C++11 -->
  <!-- void std::queue::push( const value_type& value ); -->
  <!-- void std::queue::push( value_type&& value ); // since C++11 -->
  <!-- void std::stack::push( const value_type& value ); -->
  <!-- void std::stack::push( value_type&& value ); // since C++11 -->
  <!-- void std::vector::push_back( const T& value ); -->
  <!-- void std::vector::push_back( T&& value ); // since C++11 -->
  <function name="std::deque::push_back,std::deque::push_front,std::list::push_back,std::list::push_front,std::forward_list::push_front,std::queue::push,std::priority_queue::push,std::stack::push,std::vector::push_back">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- iterator std::list::insert( iterator pos, const T& value ); // until C++11 -->
  <!-- iterator std::list::insert( const_iterator pos, const T& value ); // since C++11 -->
  <!-- iterator std::list::insert( const_iterator pos, T&& value ); // since C++11 -->
  <!-- void std::list::insert( iterator pos, size_type count, const T& value ); // until C++11 -->
  <!-- iterator std::list::insert( const_iterator pos, size_type count, const T& value ); // since C++11 -->
  <!-- template<class InputIt> void std::list::insert( iterator pos, InputIt first, InputIt last); until C++11 -->
  <!-- template<class InputIt> iterator std::list::insert( const_iterator pos, InputIt first, InputIt last ); since C++11 -->
  <!-- iterator std::list::insert( const_iterator pos, std::initializer_list<T> ilist ); since C++11 -->
  <!-- For std::multimap::insert see http://en.cppreference.com/w/cpp/container/multimap/insert -->
  <!-- For std::map::insert see http://en.cppreference.com/w/cpp/container/map/insert -->
  <!-- For std::multiset::insert see http://en.cppreference.com/w/cpp/container/multiset/insert -->
  <!-- For std::set::insert see http://en.cppreference.com/w/cpp/container/set/insert -->
  <!-- For std::string::insert see http://en.cppreference.com/w/cpp/string/basic_string/insert -->
  <!-- For std::vector::insert see http://en.cppreference.com/w/cpp/container/vector/insert -->
  <!-- For std::unordered_map::insert see https://en.cppreference.com/w/cpp/container/unordered_map/insert -->
  <!-- For std::unordered_set::insert see https://en.cppreference.com/w/cpp/container/unordered_set/insert -->
  <!-- Return value type is "iterator" or "void" depending on the overloaded function. -->
  <function name="std::list::insert,std::multimap::insert,std::map::insert,std::set::insert,std::multiset::insert,std::string::insert,std::vector::insert,std::unordered_map::insert,std::unordered_set::insert">
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0">
      <not-uninit/>
    </arg>
    <arg nr="3" default="0">
      <not-uninit/>
    </arg>
  </function>
  <function name="std::deque::emplace_back,std::deque::emplace_front,std::list::emplace_back,std::list::emplace_front,std::forward_list::emplace_front,std::queue::emplace,std::priority_queue::emplace,std::stack::emplace,std::vector::emplace_back,std::vector::emplace_front,std::unordered_set::emplace">
    <noreturn>false</noreturn>
    <arg nr="variadic">
      <not-uninit/>
    </arg>
  </function>
  <function name="std::map::find,std::set::find,std::unordered_map::find,std::unordered_multimap::find,std::unordered_set::find,std::unordered_multiset::find,std::multiset::find,std::multimap::find">
    <use-retval/>
    <returnValue type="iterator"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <function name="std::deque::max_size,std::list::max_size,std::map::max_size,std::unordered_map::max_size,std::set::max_size,std::unordered_set::max_size,std::vector::capacity,std::vector::max_size,std::span::size_bytes">
    <use-retval/>
    <const/>
    <returnValue type="std::size_t"/>
    <noreturn>false</noreturn>
  </function>
  <!--                     size_type std::map::count( const Key& key ) const; -->
  <!-- template< class K > size_type std::map::count( const K& x ) const; // since C++14 -->
  <!--                     size_type std::set::count( const value_type& val) const; -->
  <!--                     size_type std::unordered_set::count( const Key& key ) const; -->
  <!--                     size_type std::unordered_map::count( const Key& key ) const; -->
  <!--                     size_type std::multimap::count( const Key& key ) const; -->
  <function name="std::map::count,std::set::count,std::unordered_set::count,std::unordered_map::count,std::multimap::count">
    <noreturn>false</noreturn>
    <returnValue type="std::size_t"/>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <function name="std::string::size,std::string::length,std::string_view::size,std::string_view::length,std::wstring_view::size,std::wstring_view::length,std::wstring::size,std::wstring::length">
    <noreturn>false</noreturn>
    <returnValue type="std::size_t"/>
    <use-retval/>
    <const/>
    <leak-ignore/>
  </function>
  <function name="std::vector::at,std::deque::at,std::array::at,std::string_view::at,std::wstring_view::at">
    <use-retval/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- void std::vector::reserve(size_type new_capacity); -->
  <function name="std::vector::reserve">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- void std::list::remove (const value_type& val); -->
  <function name="std::list::remove">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void std::vector::resize(size_type count, T value = T()); // until C++11 -->
  <!-- void std::vector::resize(size_type count); // since C++11 -->
  <!-- void std::vector::resize(size_type count, const value_type& value); // since C++11 -->
  <function name="std::vector::resize">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" default="0">
      <not-uninit/>
    </arg>
  </function>
  <!-- https://en.cppreference.com/w/cpp/container/deque/swap -->
  <!-- void std::deque::swap( deque& other ); -->
  <!-- https://en.cppreference.com/w/cpp/container/list/swap -->
  <!-- void std::list::swap( list& other ); -->
  <!-- https://en.cppreference.com/w/cpp/container/map/swap -->
  <!-- void std::map::swap( map& other ); -->
  <!-- https://en.cppreference.com/w/cpp/container/set/swap -->
  <!-- void std::set::swap( set& other ); -->
  <!-- https://en.cppreference.com/w/cpp/container/vector/swap -->
  <!-- void std::vector::swap( vector& other ); -->
  <function name="std::deque::swap,std::list::swap,std::map::swap,std::set::swap,std::vector::swap">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-bool/>
    </arg>
  </function>
  <!-- https://en.cppreference.com/w/cpp/algorithm/swap -->
  <!-- template< class T > void swap( T& a, T& b ); -->
  <!-- template< class T > constexpr void swap( T& a, T& b ) -->
  <!-- template< class T2, std::size_t N > void swap( T2 (&a)[N], T2 (&b)[N]) -->
  <!-- template< class T2, std::size_t N > constexpr void swap( T2 (&a)[N], T2 (&b)[N]) -->
  <function name="std::swap">
    <leak-ignore/>
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
  </function>
  <!-- https://en.cppreference.com/w/cpp/utility/exchange -->
  <!-- template< class T, class U = T > constexpr T exchange( T& obj, U&& new_value ) -->
  <function name="std::exchange">
    <leak-ignore/>
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue>arg1</returnValue>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
  </function>
  <!-- https://en.cppreference.com/w/cpp/memory/addressof -->
  <!-- template< class T > constexpr T* addressof( T& arg ) noexcept; -->
  <function name="std::addressof">
    <leak-ignore/>
    <use-retval/>
    <noreturn>false</noreturn>
    <returnValue type="void*">&amp; arg1</returnValue>
    <arg nr="1">
    </arg>
  </function>
  <function name="std::stack::pop,std::queue::pop,std::priority_queue::pop">
    <noreturn>false</noreturn>
  </function>
  <function name="std::stack::top,std::priority_queue::top">
    <use-retval/>
    <noreturn>false</noreturn>
  </function>
  <!--       char& at(size_t pos); -->
  <!-- const char& at(size_t pos) const; -->
  <function name="std::string::at">
    <use-retval/>
    <returnValue type="char &amp;"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!--       wchar_t& std::wstring::at(size_t pos); -->
  <!-- const wchar_t& std::wstring::at(size_t pos) const; -->
  <function name="std::wstring::at">
    <use-retval/>
    <returnValue type="wchar_t &amp;"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!--       char16_t& std::u16string::at(size_t pos); -->
  <!-- const char16_t& std::u16string::at(size_t pos) const; -->
  <function name="std::u16string::at">
    <use-retval/>
    <returnValue type="char16_t &amp;"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!--       char32_t& std::u32string::at(size_t pos); -->
  <!-- const char32_t& std::u32string::at(size_t pos) const; -->
  <function name="std::u32string::at">
    <use-retval/>
    <returnValue type="char32_t &amp;"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- void push_back (char c); -->
  <function name="std::string::push_back">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- char& std::string::back(); -->
  <!-- const char& std::string::back() const; -->
  <function name="std::string::back">
    <use-retval/>
    <returnValue type="char &amp;"/>
    <noreturn>false</noreturn>
  </function>
  <!-- int compare( const basic_string& str ) const; -->
  <!-- int compare( size_type pos1, size_type count1, const basic_string& str ) const; -->
  <!-- int compare( size_type pos1, size_type count1, const basic_string& str, size_type pos2, size_type count2 ) const; // until C++14 -->
  <!-- int compare( size_type pos1, size_type count1, const basic_string& str, size_type pos2, size_type count2 = npos ) const; // since C++14 -->
  <!-- int compare( const CharT* s ) const; -->
  <!-- int compare( size_type pos1, size_type count1, const CharT* s ) const; -->
  <!-- int compare( size_type pos1, size_type count1, const CharT* s, size_type count2 ) const; -->
  <!-- template < class T > int compare( const T& t ) const; // since C++17 -->
  <!-- template < class T > int compare( size_type pos1, size_type count1, const T& t ) const; // since C++17 -->
  <!-- template < class T > int compare( size_type pos1, size_type count1, const T& t, size_type pos2, size_type count2 = npos) const; // since C++17 -->
  <function name="std::string::compare,std::string_view::compare,std::wstring_view::compare,std::wstring::compare">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" default="0" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" default="0" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="5" default="0" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- constexpr bool std::string::starts_with( std::basic_string_view<CharT,Traits> sv ) const noexcept; // since C++20 -->
  <!-- constexpr bool starts_with( CharT ch ) const noexcept; // since C++20 -->
  <!-- constexpr bool starts_with( const CharT* s ) const; // since C++20 -->
  <function name="std::string::starts_with,std::wstring::starts_with,std::string::ends_with,std::wstring::ends_with">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="bool"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- size_type std::string::copy( CharT* dest, size_type count, size_type pos = 0) const; -->
  <!-- size_type std::wstring::copy( CharT* dest, size_type count, size_type pos = 0) const; -->
  <function name="std::string::copy,std::wstring::copy">
    <noreturn>false</noreturn>
    <returnValue type="std::size_t"/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="out">
      <not-null/>
      <!-- TODO: #8335 arg2 is the number of characters, not bytes. -->
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in" default="0">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- void swap (string& str); -->
  <function name="std::string::swap">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1"/>
  </function>
  <!-- const char* c_str() const; // until C++11 -->
  <!-- const char* c_str() const noexcept; // since C++11 -->
  <!-- const char* data() const; // until C++11 -->
  <!-- const char* data() const noexcept; // since C++11 -->
  <function name="std::string::c_str,std::string::data">
    <use-retval/>
    <const/>
    <returnValue type="const char *"/>
    <noreturn>false</noreturn>
  </function>
  <!-- char& front(); -->
  <!-- const char& front() const; -->
  <function name="std::string::front">
    <use-retval/>
    <returnValue type="char &amp;"/>
    <noreturn>false</noreturn>
  </function>
  <!-- size_t find (const string& str, size_t pos = 0) const; -->
  <!-- size_t find (const char* s, size_t pos, size_t n) const; -->
  <!-- size_t find (char c, size_t pos = 0) const;-->
  <!-- size_t find (const char* s, size_t pos = 0) const; -->
  <!-- size_t rfind (const string& str, size_t pos = npos) const noexcept; -->
  <!-- size_t rfind (const char* s, size_t pos = npos) const; -->
  <!-- size_t rfind (const char* s, size_t pos, size_t n) const; -->
  <!-- size_t rfind (char c, size_t pos = npos) const noexcept; -->
  <function name="std::basic_string::find_last_not_of,std::string::find_last_not_of,std::wstring::find_last_not_of,std::basic_string::find_first_not_of,std::string::find_first_not_of,std::wstring::find_first_not_of,std::string::find,std::string::rfind,std::wstring::find,std::wstring::rfind,std::basic_string::find,std::basic_string::rfind,std::string::find_first_of,std::wstring::find_first_of,std::basic_string::find_first_of,std::string::find_last_of,std::wstring::find_last_of,std::basic_string::find_last_of">
    <use-retval/>
    <const/>
    <returnValue type="size_t"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" default="">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <function name="std::string::substr">
    <use-retval/>
    <const/>
    <returnValue type="std::string"/>
    <noreturn>false</noreturn>
    <arg nr="1" default="0" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <function name="std::wstring::substr">
    <use-retval/>
    <const/>
    <returnValue type="std::wstring"/>
    <noreturn>false</noreturn>
    <arg nr="1" default="0" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <function name="std::basic_string::substr">
    <use-retval/>
    <const/>
    <returnValue type="std::basic_string"/>
    <noreturn>false</noreturn>
    <arg nr="1" default="0" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- int std::istream::peak();-->
  <function name="std::istream::peek">
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
  </function>
  <!-- void std::ios::clear (std::ios::iostate state = std::ios::goodbit);-->
  <function name="std::ios::clear,std::fstream::clear,std::istream::clear">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" default="std::ios::goodbit">
      <not-uninit/>
    </arg>
  </function>
  <!-- std::istream& std::istream::read (char* s, streamsize n); -->
  <function name="std::istream::read">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::istream&amp;"/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- std::ifstream& std::ifstream::read (char* s, streamsize n); -->
  <function name="std::ifstream::read">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::ifstream&amp;"/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- std::wifstream& std::wifstream::read (wchar_t* s, streamsize n); -->
  <function name="std::wifstream::read">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::wifstream&amp;"/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- streamsize readsome (char* s, streamsize n); -->
  <function name="std::istream::readsome,std::ifstream::readsome">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- istream& getline (char* s, streamsize n ); -->
  <!-- istream& getline (char* s, streamsize n, char delim ); -->
  <function name="std::istream::getline,std::ifstream::getline">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
      <strz/>
      <minsize type="argvalue" arg="2"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" default="'\n'" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- Not part of standard, but widely supported by runtime libraries. -->
  <!-- char * itoa (int value, char * str, int base); -->
  <function name="itoa">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="char *"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-null/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>2:36</valid>
    </arg>
  </function>
  <!-- iterator std::string::erase (const_iterator first, const_iterator last); -->
  <function name="std::string::erase,std::wstring::erase,std::basic_string::erase">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
  </function>
  <!--  std::istringstream& std::istringstream::seekg (streampos pos);
        std::istringstream& std::istringstream::seekg (streamoff off, ios_base::seekdir way); -->
  <function name="std::istringstream::seekg">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::istringstream &amp;"/>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <!--  std::stringstream& std::stringstream::seekg (streampos pos);
        std::stringstream& std::stringstream::seekg (streamoff off, ios_base::seekdir way); -->
  <function name="std::stringstream::seekg">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::stringstream &amp;"/>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <!--  std::ifstream& std::ifstream::seekg (streampos pos);
        std::ifstream& std::ifstream::seekg (streamoff off, ios_base::seekdir way); -->
  <function name="std::ifstream::seekg">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::ifstream &amp;"/>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <!--  std::istream& std::istream::seekg (streampos pos);
        std::istream& std::istream::seekg (streamoff off, ios_base::seekdir way); -->
  <function name="std::istream::seekg">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::istream &amp;"/>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <!--  std::wifstream& std::wifstream::seekg (streampos pos);
        std::wifstream& std::wifstream::seekg (streamoff off, ios_base::seekdir way); -->
  <function name="std::wifstream::seekg">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::wifstream &amp;"/>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <!-- https://en.cppreference.com/w/cpp/string/basic_string/assign -->
  <function name="std::basic_string::assign">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::basic_string &amp;"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <function name="std::string::assign">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::string &amp;"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <function name="std::wstring::assign">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::wstring &amp;"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <!-- std::string& std::string::append (const std::string& str); -->
  <function name="std::string::append">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::string &amp;"/>
    <arg nr="any"/>
  </function>
  <!-- std::wstring& std::wstring::append (const std::wstring& str); -->
  <function name="std::wstring::append">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::wstring &amp;"/>
    <arg nr="any"/>
  </function>
  <!-- std::basic_string& std::basic_string::append (const std::basic_string& str); -->
  <function name="std::basic_string::append">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::basic_string &amp;"/>
    <arg nr="any"/>
  </function>
  <!-- https://en.cppreference.com/w/cpp/string/basic_string/replace -->
  <function name="std::basic_string::replace">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::basic_string &amp;"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
    <arg nr="3">
      <not-uninit/>
    </arg>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <!-- string (1)    string& replace (size_t pos,        size_t len,        const string& str);
                     string& replace (const_iterator i1, const_iterator i2, const string& str);
    substring (2)    string& replace (size_t pos,        size_t len,        const string& str, size_t subpos, size_t sublen = npos);
     c-string (3)    string& replace (size_t pos,        size_t len,        const char* s);
                     string& replace (const_iterator i1, const_iterator i2, const char* s);
        buffer (4)   string& replace (size_t pos,        size_t len,        const char* s, size_t n);
                     string& replace (const_iterator i1, const_iterator i2, const char* s, size_t n);
         fill (5)    string& replace (size_t pos,        size_t len,        size_t n, char c);
                     string& replace (const_iterator i1, const_iterator i2, size_t n, char c);
         range (6)   template <class InputIterator>
                     string& replace (const_iterator i1, const_iterator i2, InputIterator first, InputIterator last);
initializer list (7) string& replace (const_iterator i1, const_iterator i2, initializer_list<char> il);-->
  <function name="std::string::replace">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::string &amp;"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
    <arg nr="3">
      <not-uninit/>
    </arg>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <function name="std::wstring::replace">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="std::wstring &amp;"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-uninit/>
    </arg>
    <arg nr="3">
      <not-uninit/>
    </arg>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <!-- void std::ofstream::open(const char* filename        , ios_base::openmode mode = ios_base::out); -->
  <!-- void std::ofstream::open(const std::string & filename, ios_base::openmode mode = ios_base::out); -->
  <function name="std::ofstream::open">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
       <not-null/>
       <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="std::ios_base::out">
      <not-uninit/>
    </arg>
  </function>
  <!-- void std::ifstream::open(const char* filename, ios_base::openmode mode = ios_base::in); -->
  <!-- void std::ifstream::open(const std::string & filename, ios_base::openmode mode = ios_base::in); -->
  <function name="std::ifstream::open">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
       <not-null/>
       <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="std::ios_base::in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void std::ifstream::close(); -->
  <!-- void std::ofstream::close(); -->
  <!-- void std::fstream::close(); -->
  <!-- void std::wifstream::close(); -->
  <function name="std::ifstream::close,std::ofstream::close,std::fstream::close,std::wifstream::close">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
  </function>
  <!-- void std::fstream::open(const char* filename, ios_base::openmode mode = ios_base::in | ios_base::out); -->
  <!-- void std::fstream::open(const std::string& filename, ios_base::openmode mode = ios_base::in | ios_base::out); -->
  <function name="std::fstream::open">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
       <not-null/>
       <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="std::ios_base::in | std::ios_base::out">
      <not-uninit/>
    </arg>
  </function>
  <!-- std::filebuf* std::filebuf::open (const char* filename,  ios_base::openmode mode); -->
  <!-- std::filebuf* std::filebuf::open (const string filename, ios_base::openmode mode);-->
  <function name="std::filebuf::open">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <use-retval/>
    <returnValue type="std::filebuf *"/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void reserve (size_t n = 0); -->
  <function name="std::string::reserve,std::wstring::reserve,std::basic_string::reserve">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" default="0" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- https://en.cppreference.com/w/cpp/string/basic_string_view/substr -->
  <!-- constexpr basic_string_view substr( size_type pos = 0, size_type count = npos ) const; -->
  <function name="std::string_view::substr">
    <use-retval/>
    <returnValue type="std::string_view"/>
    <noreturn>false</noreturn>
    <arg nr="1" default="0" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <function name="std::wstring_view::substr">
    <use-retval/>
    <returnValue type="std::wstring_view"/>
    <noreturn>false</noreturn>
    <arg nr="1" default="0" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- default (1) template <class ForwardIterator>                ForwardIterator std::min_element (ForwardIterator first, ForwardIterator last);
        custom (2) template <class ForwardIterator, class Compare> ForwardIterator std::min_element (ForwardIterator first, ForwardIterator last, Compare comp); -->
  <!-- default (1) template <class ForwardIterator>                ForwardIterator std::max_element (ForwardIterator first, ForwardIterator last);
        custom (2) template <class ForwardIterator, class Compare> ForwardIterator std::max_element (ForwardIterator first, ForwardIterator last, Compare comp); -->
  <function name="std::min_element,std::max_element">
    <use-retval/>
    <returnValue type="iterator" container="1"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
    <arg nr="3" direction="in" default="">
      <not-uninit/>
    </arg>
  </function>
  <!-- template< class ForwardIt > std::pair<ForwardIt,ForwardIt> std::minmax_element( ForwardIt first, ForwardIt last ) -->
  <!-- template< class ForwardIt > std::pair<ForwardIt,ForwardIt> std::minmax( ForwardIt first, ForwardIt last ) -->
  <function name="std::minmax_element,std::minmax">
    <use-retval/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
  </function>
  <!-- template<class InputIterator> typename iterator_traits<InputIterator>::difference_type std::distance (InputIterator first, InputIterator last); -->
  <function name="std::distance">
    <use-retval/>
    <returnValue type="std::ptrdiff_t"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
  </function>
  <!-- template <class ForwardIterator, class OutputIterator>  OutputIterator std::rotate_copy (ForwardIterator first, ForwardIterator middle, ForwardIterator last, OutputIterator result); -->
  <function name="std::rotate_copy">
    <leak-ignore/>
    <returnValue type="iterator" container="1"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <iterator container="1" type="middle"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
    <arg nr="4" direction="out"/>
  </function>
  <!-- template <class ForwardIterator>  ForwardIterator std::rotate (ForwardIterator first, ForwardIterator middle, ForwardIterator last); -->
  <function name="std::rotate">
    <returnValue type="iterator" container="1"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <iterator container="1" type="middle"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
  </function>
  <!-- InputIterator std::adjacent_find(InputIterator first, InputIterator last) -->
  <function name="std::adjacent_find">
    <use-retval/>
    <returnValue type="iterator" container="1"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
    <arg nr="3" default="">
      <not-uninit/>
    </arg>
  </function>
  <!-- template< class InputIterator, class T >              InputIterator std::find(InputIterator first, InputIterator last, T val) -->
  <!-- template< class InputIterator, class UnaryPredicate > InputIterator std::find_if(InputIterator first, InputIterator last, UnaryPredicate val) -->
  <!-- template< class InputIterator, class UnaryPredicate > InputIterator std::find_if_not(InputIterator first, InputIterator last, UnaryPredicate val) -->
  <function name="std::find,std::find_if,std::find_if_not">
    <use-retval/>
    <returnValue type="iterator" container="1"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
    <arg nr="3">
      <not-uninit/>
    </arg>
  </function>
  <!-- template< class InputIt, class OutputIt > OutputIt copy( InputIt first, InputIt last, OutputIt d_first ); -->
  <function name="std::copy">
    <returnValue type="iterator" container="2"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
    <arg nr="3">
      <not-uninit/>
      <iterator container="2" type="first"/>
    </arg>
  </function>
  <!-- template< class InputIt, class Size, class OutputIt > OutputIt copy_n( InputIt first, Size count, OutputIt result ); -->
  <function name="std::copy_n">
    <returnValue type="iterator" container="2"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <not-bool/>
      <valid>0:</valid>
    </arg>
    <arg nr="3">
      <not-uninit/>
      <iterator container="2" type="first"/>
    </arg>
  </function>
  <!--        template <class RandomAccessIterator>                            void std::sort (RandomAccessIterator first, RandomAccessIterator last);                (until C++20) -->
  <!--        template <class RandomAccessIterator>  constexpr                 void std::sort (RandomAccessIterator first, RandomAccessIterator last);                (since C++20) -->
  <!-- @todo: template< class ExecutionPolicy, class RandomIt >                void std::sort (ExecutionPolicy&& policy, RandomIt first, RandomIt last);              (since C++17) -->
  <!-- @todo: template< class RandomIt, class Compare >                        void std::sort (RandomIt first, RandomIt last, Compare comp);                          (until C++17) -->
  <!-- @todo: template< class RandomIt, class Compare > constexpr              void std::sort (RandomIt first, RandomIt last, Compare comp);                          (since C++20) -->
  <!-- @todo: template< class ExecutionPolicy, class RandomIt, class Compare > void std::sort (ExecutionPolicy&& policy, RandomIt first, RandomIt last, Compare comp);(since C++17) -->
  <function name="std::sort">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
  </function>
  <!--        template< class RandomIt>                                        void stable_sort( RandomIt first, RandomIt last );                                                            -->
  <!-- @todo: template< class ExecutionPolicy, class RandomIt >                void stable_sort( ExecutionPolicy&& policy, RandomIt first, RandomIt last );                    (since C++17) -->
  <!-- @todo: template< class RandomIt, class Compare >                        void stable_sort( RandomIt first, RandomIt last, Compare comp );                                              -->
  <!-- @todo: template< class ExecutionPolicy, class RandomIt, class Compare > void stable_std::sort( ExecutionPolicy&& policy, RandomIt first, RandomIt last, Compare comp ); (since C++17) -->
  <function name="std::stable_sort">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
  </function>
  <!-- template< class InputIt1, class InputIt2, class OutputIt > OutputIt merge( InputIt1 first1, InputIt1 last1, InputIt2 first2, InputIt2 last2, OutputIt d_first ); -->
  <function name="std::merge">
    <noreturn>false</noreturn>
    <returnValue type="iterator" container="3"/>
    <arg nr="1">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
    <arg nr="3">
      <not-uninit/>
      <iterator container="2" type="first"/>
    </arg>
    <arg nr="4">
      <not-uninit/>
      <iterator container="2" type="last"/>
    </arg>
    <arg nr="5">
      <not-uninit/>
      <iterator container="3" type="first"/>
    </arg>
  </function>
  <!-- template< class RandomIt > void push_heap( RandomIt first, RandomIt last ); -->
  <function name="std::push_heap">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
  </function>
  <!-- template< class RandomIt >                   void std::random_shuffle( RandomIt first, RandomIt last ); (deprecated in C++14) (removed in C++17) -->
  <!-- template< class RandomIt, class RandomFunc > void std::random_shuffle( RandomIt first, RandomIt last, RandomFunc& r ); (until C++11) -->
  <!-- template< class RandomIt, class RandomFunc > void std::random_shuffle( RandomIt first, RandomIt last, RandomFunc&& r ); (since C++11) (deprecated in C++14) (removed in C++17) -->
  <!-- template< class RandomIt, class URBG >       void std::shuffle( RandomIt first, RandomIt last, URBG&& g ); (since C++11) -->
  <function name="std::random_shuffle,std::shuffle">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
    <arg nr="3" default="0">
      <not-uninit/>
    </arg>
  </function>
  <!-- template< class ForwardIt, class T > void std::fill( ForwardIt first, ForwardIt last, const T& value ); -->
  <function name="std::fill">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- template< class ForwardIt, class Generator >                        void std::generate (ForwardIt first, ForwardIt last, Generator g ); (until C++20) -->
  <!-- template< class ForwardIt, class Generator > constexpr              void std::generate (ForwardIt first, ForwardIt last, Generator g ); (since C++20) -->
  <!-- template< class ExecutionPolicy, class ForwardIt, class Generator > void std::generate (ExecutionPolicy&& policy, ForwardIt first, ForwardIt last, Generator g ); (since C++17) -->
  <function name="std::generate">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="any">
      <not-uninit/>
    </arg>
  </function>
  <!-- template< class OutputIt, class Size, class T > void std::fill_n( OutputIt first, Size count, const T& value ); // until C++11 -->
  <!-- template< class OutputIt, class Size, class T > OutputIt std::fill_n( OutputIt first, Size count, const T& value ); // since C++11, until C++20 -->
  <!-- template< class OutputIt, class Size, class T > constexpr OutputIt std::fill_n( OutputIt first, Size count, const T& value ); // since C++20 -->
  <!-- template< class ExecutionPolicy, class ForwardIt, class Size, class T > ForwardIt std::fill_n( ExecutionPolicy&& policy, ForwardIt first, Size count, const T& value ); // since C++17 -->
  <function name="std::fill_n">
    <noreturn>false</noreturn>
    <arg nr="1"/>
    <arg nr="2"/>
    <arg nr="3" direction="in"/>
    <arg nr="4" direction="in" default=""/>
  </function>
  <!-- template< class ForwardIt, class T > void iota( ForwardIt first, ForwardIt last, T value ); -->
  <function name="std::iota">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <arg nr="1">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- @todo: iterator iterator erase (const_iterator position); -->
  <!-- iterator std::vector::erase( iterator first, iterator last ); -->
  <!-- iterator std::map::erase( iterator first, iterator last ); -->
  <!-- iterator std::multimap::erase( iterator first, iterator last ); -->
  <!-- iterator std::deque::erase( iterator first, iterator last ); -->
  <!-- iterator std::list::erase( iterator first, iterator last ); -->
  <!-- iterator std::set::erase( iterator first, iterator last ); -->
  <!-- iterator std::multiset::erase( iterator first, iterator last ); -->
  <!-- iterator std::unordered_set::erase( iterator first, iterator last ); -->
  <!-- iterator std::unordered_multiset::erase( iterator first, iterator last ); -->
  <!-- iterator std::unordered_map::erase( iterator first, iterator last ); -->
  <!-- iterator std::unordered_multimap::erase( iterator first, iterator last ); -->
  <function name="std::vector::erase,std::map::erase,std::multimap::erase,std::deque::erase,std::list::erase,std::set::erase,std::multiset::erase,std::unordered_set::erase,std::unordered_multimap::erase,std::unordered_map::erase,std::unordered_multiset::erase">
    <noreturn>false</noreturn>
    <returnValue type="iterator" container="1"/>
    <leak-ignore/>
    <arg nr="1">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
  </function>
  <!-- bool std::all_of(InputIterator first, InputIterator last, UnaryPredicate pred) -->
  <!-- bool std::any_of(InputIterator first, InputIterator last, UnaryPredicate pred) -->
  <!-- bool std::none_of(InputIterator first, InputIterator last, UnaryPredicate pred) -->
  <function name="std::all_of,std::any_of,std::none_of">
    <use-retval/>
    <returnValue type="bool"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
    <arg nr="3">
      <not-uninit/>
    </arg>
  </function>
  <!-- template< class ForwardIt > ForwardIt std::unique( ForwardIt first, ForwardIt last ); -->
  <function name="std::unique">
    <use-retval/>
    <returnValue type="iterator" container="1"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
  </function>
  <!-- difference_type std::count(InputIterator first, InputIterator last, T val) -->
  <!-- difference_type std::count_if(InputIterator first, InputIterator last, UnaryPredicate val) -->
  <function name="std::count,std::count_if">
    <use-retval/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
    <arg nr="3">
      <not-uninit/>
    </arg>
  </function>
  <!-- template< class ForwardIt, class UnaryPredicate > ForwardIt std::remove_if( ForwardIt first, ForwardIt last, UnaryPredicate p ); -->
  <function name="std::remove_if">
    <use-retval/>
    <returnValue type="iterator" container="1"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
    <arg nr="3">
      <not-uninit/>
    </arg>
  </function>
  <!-- template< class Container > std::back_insert_iterator<Container> std::back_inserter( Container& c );-->
  <!-- TODO: Add returnValue type when it is possible to configure it, see trac #9229 -->
  <function name="std::back_inserter">
    <use-retval/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- template< class BidirIt > void std::reverse( BidirIt first, BidirIt last ); -->
  <function name="std::reverse">
    <returnValue type="void"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
  </function>
  <!-- Function std::for_each(InputIterator first, InputIterator last, Function func) -->
  <function name="std::for_each">
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
    <arg nr="3">
      <not-uninit/>
    </arg>
  </function>
  <!-- InputIterator std::search_n(InputIterator first, InputIterator last, Size count, const T& val) -->
  <function name="std::search_n">
    <use-retval/>
    <returnValue type="iterator" container="1"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="5" default=""/>
  </function>
  <!-- InputIterator std::find_end(InputIterator first1, InputIterator last1, InputIterator first2, InputIterator last2) -->
  <!-- InputIterator std::find_first_of(InputIterator first1, InputIterator last1, InputIterator first2, InputIterator last2) -->
  <!-- InputIterator std::search(InputIterator first1, InputIterator last1, InputIterator first2, InputIterator last2) -->
  <function name="std::find_end,std::find_first_of,std::search">
    <use-retval/>
    <returnValue type="iterator" container="1"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
    <arg nr="3">
      <not-uninit/>
      <iterator container="2" type="first"/>
    </arg>
    <arg nr="4">
      <not-uninit/>
      <iterator container="2" type="last"/>
    </arg>
    <arg nr="5" default="">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- template< class ForwardIt, class T >                       void std::replace   ( ForwardIt first, ForwardIt last, const T& old_value, const T& new_value );-->
  <!-- template< class ForwardIt, class UnaryPredicate, class T > void std::replace_if( ForwardIt first, ForwardIt last, UnaryPredicate p, const T& new_value );-->
  <function name="std::replace,std::replace_if">
    <returnValue type="void"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
    <arg nr="3">
      <not-uninit/>
    </arg>
    <arg nr="4">
      <not-uninit/>
    </arg>
  </function>
  <!-- template<class BidirIt> void inplace_merge( BidirIt first, BidirIt middle, BidirIt last ); -->
  <!-- template<class ExecutionPolicy, class BidirIt> void inplace_merge( ExecutionPolicy&& policy, BidirIt first, BidirIt middle, BidirIt last ); // since C++17 -->
  <!-- template<class BidirIt, class Compare> void inplace_merge( BidirIt first, BidirIt middle, BidirIt last, Compare comp ); -->
  <!-- template<class ExecutionPolicy, class BidirIt, class Compare> void inplace_merge( ExecutionPolicy&& policy, BidirIt first, BidirIt middle, BidirIt last, Compare comp ); // since C++17 -->
  <!-- TODO: Function argument configuration is not valid for C++17 function overloads -->
  <function name="std::inplace_merge">
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <iterator container="1" type="middle"/>
    </arg>
    <arg nr="3">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
  </function>
  <!-- template< class CharT, class Traits, class Allocator > std::basic_istream<CharT,Traits>& std::getline( std::basic_istream<CharT,Traits>&  input, std::basic_string<CharT,Traits,Allocator>& str, CharT delim ); -->
  <!-- template< class CharT, class Traits, class Allocator > std::basic_istream<CharT,Traits>& std::getline( std::basic_istream<CharT,Traits>&& input, std::basic_string<CharT,Traits,Allocator>& str, CharT delim ); -->
  <!-- template< class CharT, class Traits, class Allocator > std::basic_istream<CharT,Traits>& std::getline( std::basic_istream<CharT,Traits>&  input, std::basic_string<CharT,Traits,Allocator>& str ); -->
  <!-- template< class CharT, class Traits, class Allocator > std::basic_istream<CharT,Traits>& std::getline( std::basic_istream<CharT,Traits>&& input, std::basic_string<CharT,Traits,Allocator>& str ); -->
  <function name="std::getline">
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="out"/>
    <arg nr="3" direction="in" default="0">
      <not-uninit/>
    </arg>
  </function>
  <!-- template< class InputIt, class Distance >           void std::advance( InputIt& it, Distance n ); (until C++17) -->
  <!-- template< class InputIt, class Distance > constexpr void std::advance( InputIt& it, Distance n );               -->
  <function name="std::advance">
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in"/>
  </function>
  <!-- template< class ForwardIt, class T >           ForwardIt lower_bound( ForwardIt first, ForwardIt last, const T& value ); (until C++20) -->
  <!-- template< class ForwardIt, class T > constexpr ForwardIt lower_bound( ForwardIt first, ForwardIt last, const T& value ); (since C++20) -->
  <!-- TODO: template< class ForwardIt, class T, class Compare >           ForwardIt lower_bound( ForwardIt first, ForwardIt last, const T& value, Compare comp ); (until C++20) -->
  <!-- TODO: template< class ForwardIt, class T, class Compare > constexpr ForwardIt lower_bound( ForwardIt first, ForwardIt last, const T& value, Compare comp ); (since C++20) -->
  <!-- template< class ForwardIt, class T >           ForwardIt upper_bound( ForwardIt first, ForwardIt last, const T& value ); (until C++20) -->
  <!-- template< class ForwardIt, class T > constexpr ForwardIt upper_bound( ForwardIt first, ForwardIt last, const T& value ); (since C++20) -->
  <!-- TODO: template< class ForwardIt, class T, class Compare >           ForwardIt upper_bound( ForwardIt first, ForwardIt last, const T& value, Compare comp ); (until C++20) -->
  <!-- TODO: template< class ForwardIt, class T, class Compare > constexpr ForwardIt upper_bound( ForwardIt first, ForwardIt last, const T& value, Compare comp ); (since C++20) -->
  <function name="std::lower_bound,std::upper_bound">
    <use-retval/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
    <arg nr="3">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool std::equal(InputIterator first1, InputIterator last1, InputIterator first2) -->
  <!-- bool std::is_permutation(InputIterator first1, InputIterator last1, InputIterator first2) -->
  <function name="std::equal,std::is_permutation">
    <use-retval/>
    <returnValue type="bool"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
    <arg nr="3">
      <not-uninit/>
      <iterator container="2" type="first"/>
    </arg>
  </function>
  <!--       template <class InputIterator, class OutputIterator, class UnaryOperation>                         OutputIterator std::transform (InputIterator first1, InputIterator last1, OutputIterator result, UnaryOperation op); -->
  <!-- @todo template <class InputIterator1, class InputIterator2, class OutputIterator, class BinaryOperation> OutputIterator std::transform (InputIterator1 first1, InputIterator1 last1, InputIterator2 first2, OutputIterator result, BinaryOperation binary_op); -->
  <function name="std::transform">
    <returnValue type="iterator" container="2"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
    <arg nr="3">
      <not-uninit/>
    </arg>
    <arg nr="4"/>
  </function>
  <!-- pair<InputIterator1, InputIterator2> std::mismatch(InputIterator first1, InputIterator last1, InputIterator first2) -->
  <function name="std::mismatch">
    <use-retval/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2">
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
    <arg nr="3">
      <not-uninit/>
      <iterator container="2" type="first"/>
    </arg>
  </function>
  <!-- template< class T1, class T2 > std::pair<T1,T2> make_pair( T1 t, T2 u ); // until C++11 -->
  <!-- template< class T1, class T2 > std::pair<V1,V2> make_pair( T1&& t, T2&& u ); // since C++11 until C++14 -->
  <!-- template< class T1, class T2 > constexpr std::pair<V1,V2> make_pair( T1&& t, T2&& u ); // since C++14 -->
  <function name="std::make_pair">
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1"/>
    <arg nr="2"/>
  </function>
  <!-- template< class T >           typename std::remove_reference<T>::type&& move( T&& t ) noexcept; // (since C++11) -->
  <!-- template< class T > constexpr typename std::remove_reference<T>::type&& move( T&& t ) noexcept; // (until C++14) -->
  <function name="std::move">
    <pure/>
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- template< class ForwardIt, class T >                bool binary_search( ForwardIt first, ForwardIt last, const T& value ); (until C++20) -->
  <!-- template< class ForwardIt, class T >      constexpr bool binary_search( ForwardIt first, ForwardIt last, const T& value ); (since C++20) -->
  <!-- template< class ForwardIt, class T, class Compare >           bool binary_search( ForwardIt first, ForwardIt last, const T& value, Compare comp ); (until C++20) -->
  <!-- template< class ForwardIt, class T, class Compare > constexpr bool binary_search( ForwardIt first, ForwardIt last, const T& value, Compare comp ); (since C++20 -->
  <function name="std::binary_search">
    <pure/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <use-retval/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <iterator container="1" type="first"/>
    </arg>
    <arg nr="2" direction="in" >
      <not-uninit/>
      <iterator container="1" type="last"/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in" default="0">
      <not-uninit/>
    </arg>
  </function>
  <!--#define offsetof(type, member) /*implementation-defined*/-->
  <function name="offsetof">
    <noreturn>false</noreturn>
    <returnValue type="std::size_t"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1"/>
    <arg nr="2"/>
  </function>
  <!-- template< class M, class T > /*unspecified*/ mem_fn(M T::* pm); // since C++11 until C++17 -->
  <!-- template< class M, class T > /*unspecified*/ mem_fn(M T::* pm) noexcept; // since C++17 -->
  <function name="std::mem_fn">
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- template< class ForwardIt > ForwardIt std::next(ForwardIt it, typename std::iterator_traits<ForwardIt>::difference_type n = 1 ); // since C++11 until C++17 -->
  <!-- template< class InputIt > constexpr InputIt std::next(InputIt it, typename std::iterator_traits<InputIt>::difference_type n = 1 ); // since C++17 -->
  <!-- TODO: Specify return value type and iterator type for first argument when this is supported for such types -->
  <function name="std::next">
    <noreturn>false</noreturn>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- template< class BidirIt > BidirIt std::prev(BidirIt it, typename std::iterator_traits<BidirIt>::difference_type n = 1 ); // since C++11 until C++17 -->
  <!-- template< class BidirIt > constexpr BidirIt std::prev(BidirIt it, typename std::iterator_traits<BidirIt>::difference_type n = 1 ); // since C++17 -->
  <!-- TODO: Specify return value type and iterator type for first argument when this is supported for such types -->
  <function name="std::prev">
    <noreturn>false</noreturn>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in" default="1">
      <not-uninit/>
    </arg>
  </function>
  <function name="std::size">
    <noreturn>false</noreturn>
    <use-retval/>
    <leak-ignore/>
    <container yields="size"/>
    <returnValue type="size_t"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <function name="std::ssize">
    <noreturn>false</noreturn>
    <use-retval/>
    <leak-ignore/>
    <container yields="size"/>
    <returnValue type="long"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <function name="std::empty">
    <noreturn>false</noreturn>
    <use-retval/>
    <leak-ignore/>
    <container yields="empty"/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <function name="std::data">
    <noreturn>false</noreturn>
    <use-retval/>
    <leak-ignore/>
    <container yields="buffer"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- template< class F, class... Args > /*unspecified*/ bind( F&& f, Args&&... args ); // since C++11 -->
  <!-- template< class R, class F, class... Args > /*unspecified*/ bind( F&& f, Args&&... args ); // since C++11 -->
  <function name="std::bind">
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="1">
      <not-uninit/>
    </arg>
    <arg nr="any"/>
  </function>
  <!--       T& std::map::at( const Key& key );       // since C++11 -->
  <!-- const T& std::map::at( const Key& key ) const; // since C++11 -->
  <!--       T& std::unordered_map::at( const Key& key );       // since C++11 -->
  <!-- const T& std::unordered_map::at( const Key& key ) const; // since C++11 -->
  <function name="std::map::at,std::unordered_map::at">
    <noreturn>false</noreturn>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- (1) std::basic_streambuf<CharT, Traits>* std::ostringstream::rdbuf() const; -->
  <!-- (2) std::basic_streambuf<CharT, Traits>* std::ostringstream::rdbuf( std::basic_streambuf<CharT, Traits>* sb ); -->
  <function name="std::ostringstream::rdbuf">
    <noreturn>false</noreturn>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in" default="nullptr">
      <not-uninit/>
    </arg>
  </function>
  <!-- C Threads https://en.cppreference.com/w/c/thread -->
  <!-- int thrd_create( thrd_t *thr, thrd_start_t func, void *arg ); // since C11 -->
  <function name="thrd_create">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
    </arg>
    <arg nr="2"/>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- thrd_t thrd_current(void); // since C11 -->
  <function name="thrd_current">
    <noreturn>false</noreturn>
    <returnValue type="thrd_t"/>
    <use-retval/>
  </function>
  <!-- int thrd_detach( thrd_t thr ); // since C11 -->
  <function name="thrd_detach">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int thrd_equal( thrd_t lhs, thrd_t rhs ); // since C11 -->
  <function name="thrd_equal">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- _Noreturn void thrd_exit( int res ); // since C11 -->
  <function name="thrd_exit">
    <noreturn>true</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int thrd_join( thrd_t thr, int *res ); // since C11 -->
  <function name="thrd_join">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- int thrd_sleep( const struct timespec* duration, struct timespec* remaining ); // since C11 -->
  <function name="thrd_sleep">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- void thrd_yield(void); // since C11 -->
  <function name="thrd_yield">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
  </function>
  <!-- Mutual exclusion https://en.cppreference.com/w/c/thread -->
  <!-- void mtx_destroy( mtx_t *mutex ); // since C11 -->
  <function name="mtx_destroy">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
    <arg nr="1">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- char *strdup(const char *s); -->
  <function name="strdup">
    <noreturn>false</noreturn>
    <returnValue type="char *"/>
    <use-retval/>
    <arg nr="1" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
      <strz/>
    </arg>
  </function>
  <!-- int mtx_init( mtx_t* mutex, int type ); // since C11 -->
  <function name="mtx_init">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="out">
      <not-null/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int mtx_lock( mtx_t* mutex ); // since C11 -->
  <function name="mtx_lock">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int mtx_timedlock( mtx_t *restrict mutex, const struct timespec *restrict time_point ); // since C11 -->
  <function name="mtx_timedlock">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int mtx_trylock( mtx_t *mutex ); // since C11 -->
  <function name="mtx_trylock">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
  <!-- int mtx_unlock( mtx_t *mutex ); // since C11 -->
  <function name="mtx_unlock">
    <noreturn>false</noreturn>
    <returnValue type="int"/>
    <leak-ignore/>
    <arg nr="1" direction="inout">
      <not-null/>
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>
    <!-- void std::string::shrink_to_fit(void) ; (since C++11) -->
    <!-- void std::wstring::shrink_to_fit(void) ; (since C++11) -->
  <function name="std::string::shrink_to_fit,std::wstring::shrink_to_fit,std::vector::shrink_to_fit">
    <noreturn>false</noreturn>
    <returnValue type="void"/>
    <leak-ignore/>
  </function>
  <!-- bool std::bitset::all() const noexcept; (since C++11) -->
  <!-- bool std::bitset::any() const; (until C++11) -->
  <!-- bool std::bitset::any() const noexcept; (since C++11) -->
  <!-- bool std::bitset::none() const; (until C++11) -->
  <!-- bool std::bitset::none() const noexcept; (since C++11)-->
  <function name="std::bitset::all,std::bitset::any,std::bitset::none">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="bool"/>
    <leak-ignore/>
    <const/>
  </function>
  <!-- std::size_t std::bitset::count() const; (until C++11) -->
  <!-- std::size_t  std::bitset::count() const noexcept; (since C++11)-->
  <!-- std::size_t  std::bitset::size() const; (until C++11) -->
  <!-- constexpr std::size_t std::bitset::size() const noexcept; (since C++11)-->
  <function name="std::bitset::count,std::bitset::size">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="std::size_t"/>
    <leak-ignore/>
    <const/>
  </function>
  <!-- unsigned long long std::bitset::to_ullong() const ; (since C++11) -->
  <function name="std::bitset::to_ullong">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="unsigned long long"/>
    <leak-ignore/>
    <const/>
  </function>
  <!-- unsigned long std::bitset::to_ulong() const ; -->
  <function name="std::bitset::to_ulong">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="unsigned long"/>
    <leak-ignore/>
    <const/>
  </function>
  <!-- std::string std::bitset::to_string() const ; -->
  <function name="std::bitset::to_string">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="std::string"/>
    <leak-ignore/>
    <const/>
  </function>
  <!-- bool std::bitset< N >::test( std::size_t pos ) const -->
  <function name="std::bitset::test">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="bool"/>
    <leak-ignore/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- std::bitset& std::bitset< N >::set( ) (until C++11) -->
  <!-- std::bitset& std::bitset< N >::set( ) noexcept (since C++11) -->
  <!-- std::bitset& std::bitset< N >::set( std::size_t pos, bool value = true ) noexcept (since C++11) -->
  <function name="std::bitset::set">
    <noreturn>false</noreturn>
    <returnValue type="std::bitset &amp;"/>
    <leak-ignore/>
    <arg nr="1" default="0" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="true" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- std::bitset& std::bitset< N >::reset( ) (until C++11) -->
  <!-- std::bitset& std::bitset< N >::reset( ) noexcept (since C++11) -->
  <!-- std::bitset& std::bitset< N >::reset( std::size_t pos ) noexcept (since C++11) -->
  <!-- std::bitset& std::bitset< N >::flip( ) (until C++11) -->
  <!-- std::bitset& std::bitset< N >::flip( ) noexcept (since C++11) -->
  <!-- std::bitset& std::bitset< N >::flip( std::size_t pos ) noexcept (since C++11) -->
  <function name="std::bitset::reset,std::bitset::flip">
    <noreturn>false</noreturn>
    <returnValue type="std::bitset &amp;"/>
    <leak-ignore/>
    <arg nr="1" default="0" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- std::pair<iterator,iterator> std::map::equal_range( const Key& key ); -->
  <!-- std::pair<const_iterator,const_iterator> std::map::equal_range( const Key& key ) const; -->
  <!-- template< class K > std::pair<iterator,iterator> std::map::equal_range( const K& x ); // since C++14 -->
  <!-- template< class K > std::pair<const_iterator,const_iterator> std::map::equal_range( const K& x ) const; // since C++14 -->
  <function name="std::map::equal_range">
    <noreturn>false</noreturn>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- iterator std::map::lower_bound( const Key& key ); -->
  <!-- const_iterator lower_bound( const Key& key ) const; -->
  <!-- template< class K > iterator std::map::lower_bound(const K& x); // since C++14 -->
  <!-- template< class K > const_iterator std::map::lower_bound(const K& x) const; // since C++14 -->
  <function name="std::map::lower_bound">
    <noreturn>false</noreturn>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- iterator std::map::upper_bound( const Key& key ); -->
  <!-- const_iterator std::map::upper_bound( const Key& key ) const; -->
  <!-- template< class K > iterator std::map::upper_bound( const K& x ); // since C++14 -->
  <!-- template< class K > const_iterator std::map::upper_bound( const K& x ) const; // since C++14 -->
  <function name="std::map::upper_bound">
    <noreturn>false</noreturn>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool std::map::contains( const Key& key ) const; // since C++20 -->
  <!-- template< class K > bool std::map::contains( const K& x ) const; // since C++20 -->
  <function name="std::map::contains">
    <noreturn>false</noreturn>
    <returnValue type="bool"/>
    <use-retval/>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <function name="std::span::first">
    <use-retval/>
    <returnValue type="std::span"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <function name="std::span::last">
    <use-retval/>
    <returnValue type="std::span"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <function name="std::span::subspan">
    <use-retval/>
    <returnValue type="std::span"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <function name="std::begin,std::cbegin,std::rbegin,std::crbegin">
    <use-retval/>
    <leak-ignore/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in"/>
    <container yields="start-iterator"/>
    <returnValue type="iterator" container="1"/>
  </function>
  <function name="std::end,std::cend,std::rend,std::crend">
    <use-retval/>
    <leak-ignore/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in"/>
    <container yields="end-iterator"/>
    <returnValue type="iterator" container="1"/>
  </function>
  <function name="std::auto_ptr::get,std::shared_ptr::get,std::unique_ptr::get,std::weak_ptr::get">
    <use-retval/>
    <leak-ignore/>
    <noreturn>false</noreturn>
    <returnValue type="void *"/>
  </function>
  <function name="std::auto_ptr::reset,std::shared_ptr::reset,std::unique_ptr::reset,std::weak_ptr::reset">
    <noreturn>false</noreturn>
    <arg nr="1" direction="in" default="0">
      <not-uninit/>
    </arg>
    <returnValue type="void *"/>
  </function>
  <function name="std::auto_ptr::release,std::unique_ptr::release">
    <use-retval/>
    <noreturn>false</noreturn>
    <returnValue type="void *"/>
  </function>
  <function name="std::tie">
    <noreturn>false</noreturn>
    <use-retval/>
    <arg nr="variadic">
    </arg>
  </function>
  <function name="std::exception::what,std::logic_error::what,std::invalid_argument::what,std::domain_error::what,std::length_error::what,std::out_of_range::what,std::future_error::what,std::runtime_error::what,std::range_error::what,std::overflow_error::what,std::underflow_error::what,std::regex_error::what,std::system_error::what,std::ios_base::failure::what,std::filesystem::filesystem_error::what,std::nonexistent_local_time::what,std::ambiguous_local_time::what,std::format_error::what,std::bad_typeid::what,std::bad_cast::what,std::bad_any_cast::what,std::bad_optional_access::what,std::bad_expected_access::what,std::bad_weak_ptr::what,std::bad_function_call::what,std::bad_alloc::what,std::bad_array_new_length::what,std::bad_exception::what,std::bad_variant_access::what">
    <noreturn>false</noreturn>
    <use-retval/>
    <returnValue type="const char *"/>
  </function>
  <memory>
    <alloc init="false" buffer-size="malloc">malloc,std::malloc</alloc>
    <alloc init="true" buffer-size="calloc">calloc,std::calloc</alloc>
    <alloc init="false" buffer-size="malloc:2">aligned_alloc,std::aligned_alloc</alloc>
    <realloc init="false" buffer-size="malloc:2">realloc,std::realloc</realloc>
    <dealloc>free,std::free</dealloc>
  </memory>
  <memory>
    <alloc init="true" buffer-size="strdup">strdup,std::strdup</alloc>
    <dealloc>free,std::free</dealloc>
  </memory>
  <resource>
    <alloc init="true">fopen,std::fopen</alloc>
    <alloc init="true">tmpfile,std::tmpfile</alloc>
    <realloc init="true" realloc-arg="3">freopen,std::freopen</realloc>
    <dealloc>fclose,std::fclose</dealloc>
  </resource>
  <container id="stdContainer" endPattern="&gt; !!::" opLessAllowed="false" itEndPattern="&gt; :: iterator|const_iterator|reverse_iterator|const_reverse_iterator" hasInitializerListConstructor="true">
    <type templateParameter="0"/>
    <size>
      <function name="resize" action="resize"/>
      <function name="clear" action="clear"/>
      <function name="size" yields="size" returnType="std::size_t"/>
      <function name="empty" yields="empty" returnType="bool"/>
      <function name="erase" action="erase"/>
      <function name="insert" action="insert" yields="iterator"/>
      <function name="emplace" action="push" yields="iterator"/>
      <function name="swap" action="change"/>
      <function name="assign" action="change"/>
    </size>
    <access>
      <function name="begin" yields="start-iterator"/>
      <function name="cbegin" yields="start-iterator"/>
      <function name="rbegin" yields="start-iterator"/>
      <function name="crbegin" yields="start-iterator"/>
      <function name="end" yields="end-iterator"/>
      <function name="cend" yields="end-iterator"/>
      <function name="rend" yields="end-iterator"/>
      <function name="crend" yields="end-iterator"/>
    </access>
  </container>
  <container id="stdVectorDeque" inherits="stdContainer" opLessAllowed="true">
    <size>
      <function name="push_back" action="push"/>
      <function name="emplace_back" action="push"/>
      <function name="pop_back" action="pop"/>
      <function name="push_front" action="push"/>
      <function name="emplace_front" action="push"/>
    </size>
    <access indexOperator="array-like">
      <function name="at" yields="at_index"/>
      <function name="front" yields="item"/>
      <function name="back" yields="item"/>
      <function name="data" yields="buffer"/>
      <function name="shrink_to_fit" action="change-internal"/>
      <function name="reserve" action="change-internal"/>
    </access>
  </container>
  <container id="stdVector" startPattern="std :: vector &lt;" inherits="stdVectorDeque">
    <type unstable="erase insert"/>
    <access>
      <function name="emplace_back" action="push" yields="item"/>
      <function name="emplace_front" action="push" yields="item"/>
    </access>
  </container>
  <container id="stdDeque" startPattern="std :: deque &lt;" inherits="stdVectorDeque">
    <type unstable="erase insert"/>
    <size>
      <function name="pop_front" action="pop"/>
    </size>
  </container>
  <container id="stdArray" startPattern="std :: array &lt;" inherits="stdContainer" opLessAllowed="true">
    <size templateParameter="1">
      <function name="max_size" yields="size" returnType="std::size_t"/>
    </size>
    <access indexOperator="array-like">
      <function name="at" yields="at_index"/>
      <function name="front" yields="item"/>
      <function name="back" yields="item"/>
      <function name="data" yields="buffer"/>
      <function name="fill" action="change-content"/>
    </access>
  </container>
  <container id="stdBitset" startPattern="std :: bitset &lt;" inherits="stdContainer">
    <size templateParameter="0"/>
    <access indexOperator="array-like"/>
  </container>
  <container id="stdQueue" startPattern="std :: queue &lt;" inherits="stdContainer">
    <access>
      <function name="emplace" action="push" yields="item"/>
      <function name="push" action="push"/>
      <function name="pop" action="pop"/>
      <function name="front" yields="item"/>
      <function name="back" yields="item"/>
    </access>
  </container>
  <container id="stdStack" startPattern="std :: stack|priority_queue &lt;" inherits="stdContainer">
    <access>
      <function name="emplace" action="push" yields="item"/>
      <function name="push" action="push"/>
      <function name="pop" action="pop"/>
      <function name="top" yields="item"/>
    </access>
  </container>
  <container id="stdMultiSet" startPattern="std :: multiset|unordered_multiset &lt;" inherits="stdContainer">
    <type associative="std-like"/>
    <access>
      <function name="find" action="find" yields="iterator"/>
      <function name="count" action="find"/>
      <function name="emplace_hint" action="push" yields="iterator"/>
      <function name="rehash" action="change-internal"/>
      <function name="lower_bound" yields="iterator"/>
      <function name="upper_bound" yields="iterator"/>
    </access>
  </container>
  <container id="stdMultiMap" startPattern="std :: multimap|unordered_multimap &lt;" inherits="stdContainer">
    <type templateParameter="1" associative="std-like"/>
    <rangeItemRecordType>
      <member name="first" templateParameter="0"/>
      <member name="second" templateParameter="1"/>
    </rangeItemRecordType>
    <access>
      <function name="count" action="find"/>
      <function name="find" action="find" yields="iterator"/>
      <function name="emplace_hint" action="push" yields="iterator"/>
      <function name="rehash" action="change-internal"/>
      <function name="lower_bound" yields="iterator"/>
      <function name="upper_bound" yields="iterator"/>
    </access>
  </container>
  <container id="stdSet" startPattern="std :: set|unordered_set &lt;" inherits="stdMultiSet">
    <access>
      <function name="insert" action="push"/>
      <function name="emplace" action="push"/>
      <function name="try_emplace" action="push"/>
      <function name="insert_or_assign" action="push"/>
    </access>
  </container>
  <container id="stdMap" startPattern="std :: map|unordered_map &lt;" inherits="stdMultiMap">
    <access>
      <function name="insert" action="push"/>
      <function name="emplace" action="push"/>
      <function name="try_emplace" action="push"/>
      <function name="insert_or_assign" action="push"/>
    </access>
  </container>
  <container id="stdList" startPattern="std :: list|forward_list &lt;" inherits="stdContainer">
    <size>
      <function name="push_back" action="push"/>
      <function name="emplace_back" action="push" yields="item"/>
      <function name="emplace_after" action="push"/>
      <function name="pop_back" action="pop"/>
      <function name="push_front" action="push"/>
      <function name="emplace_front" action="push" yields="item"/>
      <function name="pop_front" action="pop"/>
      <function name="erase_after" action="erase"/>
      <function name="insert_after" action="insert"/>
      <function name="remove" action="change"/>
      <function name="remove_if" action="change"/>
      <function name="unique" action="change"/>
      <function name="merge" action="change"/>
      <function name="splice" action="change"/>
      <function name="splice_after" action="change"/>
    </size>
    <access>
      <function name="front" yields="item"/>
      <function name="back" yields="item"/>
      <function name="before_begin" yields="iterator"/>
      <function name="cbefore_begin" yields="iterator"/>
      <function name="reverse" action="change-content"/>
      <function name="sort" action="change-content"/>
    </access>
  </container>
  <container id="stdAllString" inherits="stdContainer" opLessAllowed="true" hasInitializerListConstructor="false">
    <type string="std-like" unstable="erase insert"/>
    <size>
      <function name="push_back" action="push"/>
      <function name="pop_back" action="pop"/>
      <function name="append" action="change"/>
      <function name="replace" action="change"/>
      <function name="reserve" action="change-internal"/>
      <function name="shrink_to_fit" action="change-internal"/>
      <function name="length" yields="size" returnType="std::size_t"/>
    </size>
    <access indexOperator="array-like">
      <function name="at" yields="at_index"/>
      <function name="front" yields="item"/>
      <function name="back" yields="item"/>
      <function name="data" yields="buffer"/>
      <function name="c_str" yields="buffer-nt"/>
      <function name="find" action="find-const"/>
      <function name="rfind" action="find-const"/>
      <function name="find_last_of" action="find-const"/>
      <function name="find_last_not_of" action="find-const"/>
      <function name="find_first_of" action="find-const"/>
      <function name="find_first_not_of" action="find-const"/>
    </access>
  </container>
  <container id="stdBasicString" startPattern="std :: basic_string &lt;" inherits="stdAllString">
    <type templateParameter="0"/>
  </container>
  <container id="stdString" startPattern="std :: string|wstring|u16string|u32string" endPattern="" inherits="stdAllString"/>
  <container id="stdAllStringView" inherits="stdAllString" view="true">
    <size>
      <function name="remove_prefix" action="change"/>
      <function name="remove_suffix" action="change"/>
    </size>
  </container>
  <container id="stdBasicStringView" startPattern="std :: basic_string_view &lt;" inherits="stdAllStringView">
    <type templateParameter="0"/>
  </container>
  <container id="stdStringView" startPattern="std :: string_view|wstring_view|u16string_view|u32string_view" endPattern="" inherits="stdAllStringView"/>
  <container id="stdExperimentalStringView" startPattern="std :: experimental :: string_view|wstring_view|u16string_view|u32string_view" endPattern="" inherits="stdAllStringView"/>
  <container id="stdExperimentalBasicStringView" startPattern="std :: experimental :: basic_string_view &lt;" inherits="stdBasicStringView" />
  <container id="stdSpan" startPattern="std :: span" endPattern="" inherits="stdContainer" view="true">
    <access indexOperator="array-like">
      <function name="front" yields="item"/>
      <function name="back" yields="item"/>
      <function name="data" yields="buffer"/>
    </access>
  </container>
  <smart-pointer class-name="std::auto_ptr">
    <unique/>
  </smart-pointer>
  <smart-pointer class-name="std::shared_ptr"/>
  <smart-pointer class-name="std::unique_ptr">
    <unique/>
  </smart-pointer>
  <smart-pointer class-name="std::weak_ptr"/>
  <type-checks>
    <unusedvar>
      <suppress>std::fstream</suppress>
      <suppress>std::wfstream</suppress>
      <suppress>std::ofstream</suppress>
      <suppress>std::wofstream</suppress>
      <suppress>std::basic_fstream</suppress>
      <suppress>std::basic_ofstream</suppress>
      <checkFiniteLifetime>std::insert_iterator</checkFiniteLifetime>
      <checkFiniteLifetime>std::lock_guard</checkFiniteLifetime>
      <checkFiniteLifetime>std::scoped_lock</checkFiniteLifetime>
      <checkFiniteLifetime>std::unique_lock</checkFiniteLifetime>
      <checkFiniteLifetime>std::shared_lock</checkFiniteLifetime>
      <check>std::pair</check>
      <check>std::exception</check>
      <check>std::logic_error</check>
      <check>std::domain_error</check>
      <check>std::invalid_argument</check>
      <check>std::length_error</check>
      <check>std::out_of_range</check>
      <check>std::future_error</check>
      <check>std::runtime_error</check>
      <check>std::range_error</check>
      <check>std::overflow_error</check>
      <check>std::underflow_error</check>
      <check>std::regex_error</check>
      <check>std::system_error</check>
      <check>std::bad_typeid</check>
      <check>std::bad_cast</check>
      <check>std::bad_optional_access</check>
      <check>std::bad_expected_access</check>
      <check>std::bad_weak_ptr</check>
      <check>std::bad_function_call</check>
      <check>std::bad_alloc</check>
      <check>std::bad_array_new_length</check>
      <check>std::bad_exception</check>
      <check>std::ios_base::failure</check>
      <check>std::filesystem::filesystem_error</check>
      <check>std::bad_variant_access</check>
      <check>std::span</check>
    </unusedvar>
    <operatorEqVarError>
      <suppress>std::mutex</suppress>
      <suppress>std::recursive_mutex</suppress>
    </operatorEqVarError>
  </type-checks>
  <podtype name="char8_t,std::char8_t" sign="u" size="1"/>
  <podtype name="char16_t,std::char16_t" sign="u" size="2"/>
  <podtype name="char32_t,std::char32_t" sign="u" size="4"/>
  <podtype name="int8_t,std::int8_t" sign="s" size="1"/>
  <podtype name="int16_t,std::int16_t" sign="s" size="2"/>
  <podtype name="int32_t,std::int32_t" sign="s" size="4"/>
  <podtype name="int64_t,std::int64_t" sign="s" size="8"/>
  <podtype name="uint8_t,std::uint8_t" sign="u" size="1"/>
  <podtype name="uint16_t,std::uint16_t" sign="u" size="2"/>
  <podtype name="uint32_t,std::uint32_t" sign="u" size="4"/>
  <podtype name="uint64_t,std::uint64_t" sign="u" size="8"/>
  <podtype name="int_fast8_t,std::int_fast8_t" sign="s"/>
  <podtype name="int_fast16_t,std::int_fast16_t" sign="s"/>
  <podtype name="int_fast32_t,std::int_fast32_t" sign="s"/>
  <podtype name="int_fast64_t,std::int_fast64_t" sign="s"/>
  <podtype name="int_least8_t,std::int_least8_t" sign="s"/>
  <podtype name="int_least16_t,std::int_least16_t" sign="s"/>
  <podtype name="int_least32_t,std::int_least32_t" sign="s"/>
  <podtype name="int_least64_t,std::int_least64_t" sign="s"/>
  <podtype name="uint_fast8_t,std::uint_fast8_t" sign="u"/>
  <podtype name="uint_fast16_t,std::uint_fast16_t" sign="u"/>
  <podtype name="uint_fast32_t,std::uint_fast32_t" sign="u"/>
  <podtype name="uint_fast64_t,std::uint_fast64_t" sign="u"/>
  <podtype name="uint_least8_t,std::uint_least8_t" sign="u"/>
  <podtype name="uint_least16_t,std::uint_least16_t" sign="u"/>
  <podtype name="uint_least32_t,std::uint_least32_t" sign="u"/>
  <podtype name="uint_least64_t,std::uint_least64_t" sign="u"/>
  <podtype name="intptr_t,std::intptr_t" sign="s"/>
  <podtype name="uintptr_t,std::uintptr_t" sign="u"/>
  <podtype name="intmax_t,std::intmax_t" sign="s"/>
  <podtype name="uintmax_t,std::uintmax_t" sign="u"/>
  <podtype name="std::string::size_type" sign="u"/>
  <podtype name="std::wstring::size_type" sign="u"/>
  <podtype name="std::u16string::size_type" sign="u"/>
  <podtype name="std::u32string::size_type" sign="u"/>
  <!-- rsize_t is a typedef for size_t -->
  <podtype name="rsize_t" sign="u"/>
  <podtype name="double_t"/>
  <podtype name="float_t"/>
  <podtype name="time_t,std::time_t"/>
  <podtype name="clock_t,std::clock_t"/>
  <podtype name="tm"/>
  <podtype name="fenv_t"/>
  <podtype name="fexcept_t"/>
  <podtype name="lconv"/>
  <podtype name="sig_atomic_t"/>
  <podtype name="va_list"/>
  <!-- http://en.cppreference.com/w/c/types/ptrdiff_t -->
  <!-- http://en.cppreference.com/w/cpp/types/ptrdiff_t-->
  <podtype name="ptrdiff_t,std::ptrdiff_t" sign="s"/>
  <podtype name="max_align_t"/>
  <podtype name="nullptr_t"/>
  <podtype name="fpos_t"/>
  <podtype name="FILE"/>
  <podtype name="div_t"/>
  <podtype name="ldiv_t"/>
  <podtype name="lldiv_t"/>
  <podtype name="imaxdiv_t"/>
  <podtype name="mbstate_t"/>
  <podtype name="wint_t"/>
  <podtype name="jmp_buf"/>
  <podtype name="std::streamsize,streamsize" sign="s"/>
  <podtype name="std::streamoff,streamoff" sign="s"/>
  <podtype name="std::atomic_bool" stdtype="bool"/>
  <podtype name="std::atomic_char" stdtype="char"/>
  <podtype name="std::atomic_schar" stdtype="char" sign="s"/>
  <podtype name="std::atomic_uchar" stdtype="char" sign="u"/>
  <podtype name="std::atomic_short" stdtype="short" sign="s"/>
  <podtype name="std::atomic_ushort" stdtype="short" sign="u"/>
  <podtype name="std::atomic_int" stdtype="int" sign="s"/>
  <podtype name="std::atomic_uint" stdtype="int" sign="u"/>
  <podtype name="std::atomic_long" stdtype="long" sign="s"/>
  <podtype name="std::atomic_ulong" stdtype="long" sign="u"/>
  <podtype name="std::atomic_llong" stdtype="long long" sign="s"/>
  <podtype name="std::atomic_ullong" stdtype="long long" sign="u"/>
  <podtype name="std::atomic_char8_t" size="1"/>
  <podtype name="std::atomic_char16_t" size="2"/>
  <podtype name="std::atomic_char32_t" size="4"/>
  <podtype name="std::atomic_wchar_t" size="2"/>
  <podtype name="std::atomic_int_least8_t"/>
  <podtype name="std::atomic_uint_least8_t"/>
  <podtype name="std::atomic_int_least16_t"/>
  <podtype name="std::atomic_uint_least16_t"/>
  <podtype name="std::atomic_int_least32_t"/>
  <podtype name="std::atomic_uint_least32_t"/>
  <podtype name="std::atomic_int_least64_t"/>
  <podtype name="std::atomic_uint_least64_t"/>
  <podtype name="std::atomic_int_fast8_t"/>
  <podtype name="std::atomic_uint_fast8_t"/>
  <podtype name="std::atomic_int_fast16_t"/>
  <podtype name="std::atomic_uint_fast16_t"/>
  <podtype name="std::atomic_int_fast32_t"/>
  <podtype name="std::atomic_uint_fast32_t"/>
  <podtype name="std::atomic_int_fast64_t"/>
  <podtype name="std::atomic_uint_fast64_t"/>
  <podtype name="std::atomic_intptr_t"/>
  <podtype name="std::atomic_uintptr_t"/>
  <podtype name="std::atomic_size_t"/>
  <podtype name="std::atomic_ptrdiff_t"/>
  <podtype name="std::atomic_intmax_t"/>
  <podtype name="std::atomic_uintmax_t"/>
  <!-- https://en.cppreference.com/w/c/program/SIG_types
        Values are in alignment with posix.cfg -->
  <define name="SIGTERM" value="15"/>
  <define name="SIGSEGV" value="11"/>
  <define name="SIGINT" value="2"/>
  <define name="SIGKILL" value="9"/>
  <define name="SIGABRT" value="6"/>
  <define name="SIGFPE" value="8"/>
  <!-- http://www.cplusplus.com/reference/cstdio/fseek/ -->
  <define name="SEEK_CUR" value="1"/>
  <define name="SEEK_END" value="2"/>
  <define name="SEEK_SET" value="0"/>
  <!-- https://en.cppreference.com/w/c/numeric/fenv/FE_exceptions -->
  <define name="FE_INEXACT" value="1"/>
  <define name="FE_UNDERFLOW" value="2"/>
  <define name="FE_OVERFLOW" value="4"/>
  <define name="FE_DIVBYZERO" value="8"/>
  <define name="FE_INVALID" value="16"/>
  <define name="FE_ALL_EXCEPT" value="31"/>
</def>
