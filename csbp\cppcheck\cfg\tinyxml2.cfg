<?xml version="1.0"?>
<def format="2">
  <!-- tinyxml2::XMLError tinyxml2::XMLDocument::LoadFile( const char* filename ) -->
  <!-- tinyxml2::XMLError tinyxml2::XMLDocument::LoadFile( FILE* ) -->
  <function name="tinyxml2::XMLDocument::LoadFile">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="tinyxml2::XMLError"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- const tinyxml2::XMLElement* tinyxml2::XMLNode::FirstChildElement( const char* name ) const -->
  <!-- const tinyxml2::XMLElement* tinyxml2::XMLNode::LastChildElement( const char* name ) const -->
  <function name="tinyxml2::XMLNode::FirstChildElement,tinyxml2::XMLNode::LastChildElement">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const tinyxml2::XMLElement *"/>
    <arg nr="1">
      <not-uninit/>
    </arg>
  </function>
  <!-- const char* tinyxml2::XMLElement::Attribute( const char* name, const char* value=0 ) const-->
  <function name="tinyxml2::XMLElement::Attribute">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const char *"/>
    <use-retval/>
    <arg nr="1">
      <not-uninit/>
      <not-null/>
    </arg>
    <arg nr="2" default="0">
      <not-uninit/>
    </arg>
  </function>
</def>
