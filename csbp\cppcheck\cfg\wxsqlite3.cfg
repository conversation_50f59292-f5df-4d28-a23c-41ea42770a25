<?xml version="1.0"?>
<def format="2">
  <!-- wxString wxSQLite3Authorizer::AuthorizationCodeToString( wxSQLite3Authorizer::wxAuthorizationCode type ) -->
  <function name="wxSQLite3Authorizer::AuthorizationCodeToString">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxString"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void wxSQLite3Blob::CheckBlob( void ) const -->
  <function name="wxSQLite3Blob::CheckBlob">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
  </function>
  <!-- int wxSQLite3Blob::GetSize( void ) const -->
  <function name="wxSQLite3Blob::GetSize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool wxSQLite3Blob::IsOk( void ) const -->
  <function name="wxSQLite3Blob::IsOk">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool wxSQLite3Blob::IsReadOnly( void ) const -->
  <function name="wxSQLite3Blob::IsReadOnly">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- wxMemoryBuffer & wxSQLite3Blob::Read( wxMemoryBuffer & blobValue, int length, int offset ) const -->
  <function name="wxSQLite3Blob::Read">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxMemoryBuffer &amp;"/>
    <const/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void wxSQLite3Blob::Rebind( wxLongLong rowid ) -->
  <function name="wxSQLite3Blob::Rebind">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void wxSQLite3Blob::Write( const wxMemoryBuffer & blobValue, int offset ) -->
  <function name="wxSQLite3Blob::Write">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- wxSQLite3CipherType wxSQLite3Cipher::GetCipher( wxSQLite3Database & db ) -->
  <function name="wxSQLite3Cipher::GetCipher">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxSQLite3CipherType"/>
    <use-retval/>
    <pure/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- wxSQLite3CipherType wxSQLite3Cipher::GetCipherDefault( wxSQLite3Database & db ) -->
  <function name="wxSQLite3Cipher::GetCipherDefault">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxSQLite3CipherType"/>
    <use-retval/>
    <pure/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- const wxString wxSQLite3Cipher::GetCipherName( wxSQLite3CipherType cipherType ) -->
  <function name="wxSQLite3Cipher::GetCipherName">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const wxString"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- int wxSQLite3Cipher::GetCipherParameterMax( const wxString & cipherName, const wxString & paramName ) -->
  <function name="wxSQLite3Cipher::GetCipherParameterMax">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- int wxSQLite3Cipher::GetCipherParameterMin( const wxString & cipherName, const wxString & paramName ) -->
  <function name="wxSQLite3Cipher::GetCipherParameterMin">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <pure/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void * wxSQLite3Cipher::GetDatabaseHandle( wxSQLite3Database & db ) -->
  <function name="wxSQLite3Cipher::GetDatabaseHandle">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void *"/>
    <pure/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- wxSQLite3CipherType wxSQLite3Cipher::GetGlobalCipherDefault( void ) -->
  <function name="wxSQLite3Cipher::GetGlobalCipherDefault">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxSQLite3CipherType"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- int wxSQLite3Cipher::GetLegacyPageSize( void ) const -->
  <function name="wxSQLite3Cipher::GetLegacyPageSize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool wxSQLite3Cipher::InitializeFromCurrent( wxSQLite3Database & db ) -->
  <function name="wxSQLite3Cipher::InitializeFromCurrent">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- bool wxSQLite3Cipher::InitializeFromCurrentDefault( wxSQLite3Database & db ) -->
  <function name="wxSQLite3Cipher::InitializeFromCurrentDefault">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- bool wxSQLite3Cipher::InitializeFromGlobalDefault( void ) -->
  <function name="wxSQLite3Cipher::InitializeFromGlobalDefault">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- bool wxSQLite3Cipher::IsOk( void ) const -->
  <function name="wxSQLite3Cipher::IsOk">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool wxSQLite3Cipher::SetCipher( wxSQLite3Database & db, wxSQLite3CipherType cipherType ) -->
  <function name="wxSQLite3Cipher::SetCipher">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- bool wxSQLite3Cipher::SetCipherDefault( wxSQLite3Database & db, wxSQLite3CipherType cipherType ) -->
  <function name="wxSQLite3Cipher::SetCipherDefault">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void wxSQLite3Cipher::SetCipherType( wxSQLite3CipherType cipherType ) -->
  <function name="wxSQLite3Cipher::SetCipherType">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void wxSQLite3Cipher::SetInitialized( bool initialized ) -->
  <function name="wxSQLite3Cipher::SetInitialized">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void wxSQLite3Cipher::SetLegacyPageSize( int pageSize ) -->
  <function name="wxSQLite3Cipher::SetLegacyPageSize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool wxSQLite3CipherAes128::GetLegacy( void ) -->
  <function name="wxSQLite3CipherAes128::GetLegacy">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- bool wxSQLite3CipherAes128::InitializeFromCurrent( wxSQLite3Database & db ) -->
  <function name="wxSQLite3CipherAes128::InitializeFromCurrent">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- bool wxSQLite3CipherAes128::InitializeFromCurrentDefault( wxSQLite3Database & db ) -->
  <function name="wxSQLite3CipherAes128::InitializeFromCurrentDefault">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- bool wxSQLite3CipherAes128::InitializeFromGlobalDefault( void ) -->
  <function name="wxSQLite3CipherAes128::InitializeFromGlobalDefault">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- void wxSQLite3CipherAes128::SetLegacy( bool legacy ) -->
  <function name="wxSQLite3CipherAes128::SetLegacy">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int wxSQLite3CipherAes256::GetKdfIter( void ) -->
  <function name="wxSQLite3CipherAes256::GetKdfIter">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
  </function>
  <!-- bool wxSQLite3CipherAes256::GetLegacy( void ) -->
  <function name="wxSQLite3CipherAes256::GetLegacy">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- bool wxSQLite3CipherAes256::InitializeFromCurrent( wxSQLite3Database & db ) -->
  <function name="wxSQLite3CipherAes256::InitializeFromCurrent">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- bool wxSQLite3CipherAes256::InitializeFromCurrentDefault( wxSQLite3Database & db ) -->
  <function name="wxSQLite3CipherAes256::InitializeFromCurrentDefault">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- bool wxSQLite3CipherAes256::InitializeFromGlobalDefault( void ) -->
  <function name="wxSQLite3CipherAes256::InitializeFromGlobalDefault">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- void wxSQLite3CipherAes256::SetKdfIter( int kdfIter ) -->
  <function name="wxSQLite3CipherAes256::SetKdfIter">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void wxSQLite3CipherAes256::SetLegacy( bool legacy ) -->
  <function name="wxSQLite3CipherAes256::SetLegacy">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int wxSQLite3CipherChaCha20::GetKdfIter( void ) -->
  <function name="wxSQLite3CipherChaCha20::GetKdfIter">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
  </function>
  <!-- bool wxSQLite3CipherChaCha20::GetLegacy( void ) -->
  <function name="wxSQLite3CipherChaCha20::GetLegacy">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- bool wxSQLite3CipherChaCha20::InitializeFromCurrent( wxSQLite3Database & db ) -->
  <function name="wxSQLite3CipherChaCha20::InitializeFromCurrent">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- bool wxSQLite3CipherChaCha20::InitializeFromCurrentDefault( wxSQLite3Database & db ) -->
  <function name="wxSQLite3CipherChaCha20::InitializeFromCurrentDefault">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- bool wxSQLite3CipherChaCha20::InitializeFromGlobalDefault( void ) -->
  <function name="wxSQLite3CipherChaCha20::InitializeFromGlobalDefault">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- void wxSQLite3CipherChaCha20::SetKdfIter( int kdfIter ) -->
  <function name="wxSQLite3CipherChaCha20::SetKdfIter">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void wxSQLite3CipherChaCha20::SetLegacy( bool legacy ) -->
  <function name="wxSQLite3CipherChaCha20::SetLegacy">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool wxSQLite3CipherRC4::GetLegacy( void ) -->
  <function name="wxSQLite3CipherRC4::GetLegacy">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- bool wxSQLite3CipherRC4::InitializeFromCurrent( wxSQLite3Database & db ) -->
  <function name="wxSQLite3CipherRC4::InitializeFromCurrent">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- bool wxSQLite3CipherRC4::InitializeFromCurrentDefault( wxSQLite3Database & db ) -->
  <function name="wxSQLite3CipherRC4::InitializeFromCurrentDefault">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- bool wxSQLite3CipherRC4::InitializeFromGlobalDefault( void ) -->
  <function name="wxSQLite3CipherRC4::InitializeFromGlobalDefault">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- int wxSQLite3CipherSQLCipher::GetFastKdfIter( void ) -->
  <function name="wxSQLite3CipherSQLCipher::GetFastKdfIter">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
  </function>
  <!-- Algorithm wxSQLite3CipherSQLCipher::GetHmacAlgorithm( void ) -->
  <function name="wxSQLite3CipherSQLCipher::GetHmacAlgorithm">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="Algorithm"/>
    <use-retval/>
  </function>
  <!-- int wxSQLite3CipherSQLCipher::GetHmacPgNo( void ) -->
  <function name="wxSQLite3CipherSQLCipher::GetHmacPgNo">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
  </function>
  <!-- int wxSQLite3CipherSQLCipher::GetHmacSaltMask( void ) -->
  <function name="wxSQLite3CipherSQLCipher::GetHmacSaltMask">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
  </function>
  <!-- bool wxSQLite3CipherSQLCipher::GetHmacUse( void ) -->
  <function name="wxSQLite3CipherSQLCipher::GetHmacUse">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- Algorithm wxSQLite3CipherSQLCipher::GetKdfAlgorithm( void ) -->
  <function name="wxSQLite3CipherSQLCipher::GetKdfAlgorithm">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="Algorithm"/>
    <use-retval/>
  </function>
  <!-- int wxSQLite3CipherSQLCipher::GetKdfIter( void ) -->
  <function name="wxSQLite3CipherSQLCipher::GetKdfIter">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
  </function>
  <!-- bool wxSQLite3CipherSQLCipher::GetLegacy( void ) -->
  <function name="wxSQLite3CipherSQLCipher::GetLegacy">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- int wxSQLite3CipherSQLCipher::GetLegacyVersion( void ) -->
  <function name="wxSQLite3CipherSQLCipher::GetLegacyVersion">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
  </function>
  <!-- bool wxSQLite3CipherSQLCipher::InitializeFromCurrent( wxSQLite3Database & db ) -->
  <function name="wxSQLite3CipherSQLCipher::InitializeFromCurrent">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- bool wxSQLite3CipherSQLCipher::InitializeFromCurrentDefault( wxSQLite3Database & db ) -->
  <function name="wxSQLite3CipherSQLCipher::InitializeFromCurrentDefault">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- bool wxSQLite3CipherSQLCipher::InitializeFromGlobalDefault( void ) -->
  <function name="wxSQLite3CipherSQLCipher::InitializeFromGlobalDefault">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- void wxSQLite3CipherSQLCipher::InitializeVersionDefault( int version ) -->
  <function name="wxSQLite3CipherSQLCipher::InitializeVersionDefault">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void wxSQLite3CipherSQLCipher::SetFastKdfIter( int fastKdfIter ) -->
  <function name="wxSQLite3CipherSQLCipher::SetFastKdfIter">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void wxSQLite3CipherSQLCipher::SetHmacAlgorithm( Algorithm algorithm ) -->
  <function name="wxSQLite3CipherSQLCipher::SetHmacAlgorithm">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void wxSQLite3CipherSQLCipher::SetHmacPgNo( int hmacPgNo ) -->
  <function name="wxSQLite3CipherSQLCipher::SetHmacPgNo">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void wxSQLite3CipherSQLCipher::SetHmacSaltMask( int hmacSaltMask ) -->
  <function name="wxSQLite3CipherSQLCipher::SetHmacSaltMask">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void wxSQLite3CipherSQLCipher::SetHmacUse( bool hmacUse ) -->
  <function name="wxSQLite3CipherSQLCipher::SetHmacUse">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void wxSQLite3CipherSQLCipher::SetKdfAlgorithm( Algorithm algorithm ) -->
  <function name="wxSQLite3CipherSQLCipher::SetKdfAlgorithm">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void wxSQLite3CipherSQLCipher::SetKdfIter( int kdfIter ) -->
  <function name="wxSQLite3CipherSQLCipher::SetKdfIter">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void wxSQLite3CipherSQLCipher::SetLegacy( bool legacy ) -->
  <function name="wxSQLite3CipherSQLCipher::SetLegacy">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int wxSQLite3Collation::Compare( const wxString & text1, const wxString & text2 ) -->
  <function name="wxSQLite3Collation::Compare">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void wxSQLite3Database::AutoWriteAheadLogCheckpoint( int frameCount ) -->
  <function name="wxSQLite3Database::AutoWriteAheadLogCheckpoint">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void wxSQLite3Database::Begin( wxSQLite3TransactionType transactionType = WXSQLITE_TRANSACTION_DEFAULT ) -->
  <function name="wxSQLite3Database::Begin">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" default="WXSQLITE_TRANSACTION_DEFAULT" direction="in"/>
  </function>
  <!-- void wxSQLite3Database::CheckDatabase( void ) const -->
  <function name="wxSQLite3Database::CheckDatabase">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
  </function>
  <!-- void wxSQLite3Database::Commit( void ) -->
  <function name="wxSQLite3Database::Commit">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- bool wxSQLite3Database::CompileOptionUsed( const wxString & optionName ) -->
  <function name="wxSQLite3Database::CompileOptionUsed">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- wxSQLite3IntegerCollection wxSQLite3Database::CreateIntegerCollection( const wxString & collectionName ) -->
  <function name="wxSQLite3Database::CreateIntegerCollection">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxSQLite3IntegerCollection"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- wxSQLite3StringCollection wxSQLite3Database::CreateStringCollection( const wxString & collectionName ) -->
  <function name="wxSQLite3Database::CreateStringCollection">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxSQLite3StringCollection"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void wxSQLite3Database::DetachDatabase( const wxString & schemaName ) -->
  <function name="wxSQLite3Database::DetachDatabase">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool wxSQLite3Database::EnableForeignKeySupport( bool enable ) -->
  <function name="wxSQLite3Database::EnableForeignKeySupport">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void wxSQLite3Database::EnableLoadExtension( bool enable ) -->
  <function name="wxSQLite3Database::EnableLoadExtension">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void wxSQLite3Database::ExecCollationNeeded( void * db, void * internalDb, int eTextRep, const char * name ) -->
  <function name="wxSQLite3Database::ExecCollationNeeded">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="inout"/>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="inout">
      <not-uninit/>
    </arg>
  </function>
  <!-- int wxSQLite3Database::ExecComparisonWithCollation( void * collation, int len1, const void * txt1, int len2, const void * txt2 ) -->
  <function name="wxSQLite3Database::ExecComparisonWithCollation">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="inout">
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="5" direction="inout">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool wxSQLite3Database::GetAutoCommit( void ) const -->
  <function name="wxSQLite3Database::GetAutoCommit">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- wxString wxSQLite3Database::GetCompileOptionName( int optionIndex ) -->
  <function name="wxSQLite3Database::GetCompileOptionName">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxString"/>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- wxString wxSQLite3Database::GetDatabaseFilename( const wxString & databaseName ) -->
  <function name="wxSQLite3Database::GetDatabaseFilename">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxString"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void * wxSQLite3Database::GetDatabaseHandle( void ) const -->
  <function name="wxSQLite3Database::GetDatabaseHandle">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void *"/>
    <use-retval/>
    <const/>
  </function>
  <!-- wxSQLite3JournalMode wxSQLite3Database::GetJournalMode( const wxString & database = wxEmptyString ) -->
  <function name="wxSQLite3Database::GetJournalMode">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxSQLite3JournalMode"/>
    <use-retval/>
    <arg nr="1" default="wxEmptyString" direction="in"/>
  </function>
  <!-- wxString wxSQLite3Database::GetKeySalt( const wxString & schemaName = wxEmptyString ) const -->
  <function name="wxSQLite3Database::GetKeySalt">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxString"/>
    <const/>
    <arg nr="1" default="wxEmptyString" direction="in"/>
  </function>
  <!-- wxLongLong wxSQLite3Database::GetLastRowId( void ) const -->
  <function name="wxSQLite3Database::GetLastRowId">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxLongLong"/>
    <use-retval/>
    <const/>
  </function>
  <!-- int wxSQLite3Database::GetLimit( wxSQLite3LimitType id ) const -->
  <function name="wxSQLite3Database::GetLimit">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- wxString wxSQLite3Database::GetSourceId( void ) -->
  <function name="wxSQLite3Database::GetSourceId">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxString"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- int wxSQLite3Database::GetSystemErrorCode( void ) const -->
  <function name="wxSQLite3Database::GetSystemErrorCode">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!-- void wxSQLite3Database::GetUserList( wxArrayString & userList ) -->
  <function name="wxSQLite3Database::GetUserList">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- wxString wxSQLite3Database::GetVersion( void ) -->
  <function name="wxSQLite3Database::GetVersion">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxString"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- wxString wxSQLite3Database::GetWrapperVersion( void ) -->
  <function name="wxSQLite3Database::GetWrapperVersion">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxString"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- bool wxSQLite3Database::HasBackupSupport( void ) -->
  <function name="wxSQLite3Database::HasBackupSupport">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- bool wxSQLite3Database::HasEncryptionSupport( void ) -->
  <function name="wxSQLite3Database::HasEncryptionSupport">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- bool wxSQLite3Database::HasIncrementalBlobSupport( void ) -->
  <function name="wxSQLite3Database::HasIncrementalBlobSupport">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- bool wxSQLite3Database::HasLoadExtSupport( void ) -->
  <function name="wxSQLite3Database::HasLoadExtSupport">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- bool wxSQLite3Database::HasMetaDataSupport( void ) -->
  <function name="wxSQLite3Database::HasMetaDataSupport">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- bool wxSQLite3Database::HasNamedCollectionSupport( void ) -->
  <function name="wxSQLite3Database::HasNamedCollectionSupport">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- bool wxSQLite3Database::HasPointerParamsSupport( void ) -->
  <function name="wxSQLite3Database::HasPointerParamsSupport">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- bool wxSQLite3Database::HasSavepointSupport( void ) -->
  <function name="wxSQLite3Database::HasSavepointSupport">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- bool wxSQLite3Database::HasUserAuthenticationSupport( void ) -->
  <function name="wxSQLite3Database::HasUserAuthenticationSupport">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- bool wxSQLite3Database::HasWriteAheadLogSupport( void ) -->
  <function name="wxSQLite3Database::HasWriteAheadLogSupport">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- void wxSQLite3Database::Interrupt( void ) -->
  <function name="wxSQLite3Database::Interrupt">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- bool wxSQLite3Database::IsEncrypted( void ) -->
  <function name="wxSQLite3Database::IsEncrypted">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- bool wxSQLite3Database::IsForeignKeySupportEnabled( void ) -->
  <function name="wxSQLite3Database::IsForeignKeySupportEnabled">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- bool wxSQLite3Database::IsOpen( void ) const -->
  <function name="wxSQLite3Database::IsOpen">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool wxSQLite3Database::IsSharedCacheEnabled( void ) -->
  <function name="wxSQLite3Database::IsSharedCacheEnabled">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <pure/>
  </function>
  <!-- wxString wxSQLite3Database::LimitTypeToString( wxSQLite3LimitType type ) -->
  <function name="wxSQLite3Database::LimitTypeToString">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxString"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void * wxSQLite3Database::Prepare( const char * sql ) -->
  <function name="wxSQLite3Database::Prepare">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void *"/>
    <arg nr="1" direction="inout">
      <not-uninit/>
    </arg>
  </function>
  <!-- void * wxSQLite3Database::PreparePersistent( const char * sql ) -->
  <function name="wxSQLite3Database::PreparePersistent">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void *"/>
    <arg nr="1" direction="inout">
      <not-uninit/>
    </arg>
  </function>
  <!-- int wxSQLite3Database::QueryRollbackState( void ) const -->
  <function name="wxSQLite3Database::QueryRollbackState">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!-- wxSQLite3TransactionState wxSQLite3Database::QueryTransactionState( const wxString & schemaName = wxEmptyString ) const -->
  <function name="wxSQLite3Database::QueryTransactionState">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxSQLite3TransactionState"/>
    <use-retval/>
    <const/>
    <arg nr="1" default="wxEmptyString" direction="in"/>
  </function>
  <!-- bool wxSQLite3Database::Randomness( int n, wxMemoryBuffer & random ) -->
  <function name="wxSQLite3Database::Randomness">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- void wxSQLite3Database::ReleaseMemory( void ) -->
  <function name="wxSQLite3Database::ReleaseMemory">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void wxSQLite3Database::ReleaseSavepoint( const wxString & savepointName ) -->
  <function name="wxSQLite3Database::ReleaseSavepoint">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool wxSQLite3Database::RemoveAuthorizer( void ) -->
  <function name="wxSQLite3Database::RemoveAuthorizer">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- void wxSQLite3Database::Rollback( const wxString & savepointName = wxEmptyString ) -->
  <function name="wxSQLite3Database::Rollback">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" default="wxEmptyString" direction="in"/>
  </function>
  <!-- void wxSQLite3Database::Savepoint( const wxString & savepointName ) -->
  <function name="wxSQLite3Database::Savepoint">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool wxSQLite3Database::SetAuthorizer( wxSQLite3Authorizer & authorizer ) -->
  <function name="wxSQLite3Database::SetAuthorizer">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void wxSQLite3Database::SetBackupRestorePageCount( int pageCount ) -->
  <function name="wxSQLite3Database::SetBackupRestorePageCount">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void wxSQLite3Database::SetBusyTimeout( int milliSeconds ) -->
  <function name="wxSQLite3Database::SetBusyTimeout">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void wxSQLite3Database::SetCollation( const wxString & name, wxSQLite3Collation * collation ) -->
  <function name="wxSQLite3Database::SetCollation">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="inout"/>
  </function>
  <!-- void wxSQLite3Database::SetCollationNeededCallback( void ) -->
  <function name="wxSQLite3Database::SetCollationNeededCallback">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void wxSQLite3Database::SetCommitHook( wxSQLite3Hook * commitHook ) -->
  <function name="wxSQLite3Database::SetCommitHook">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- wxSQLite3JournalMode wxSQLite3Database::SetJournalMode( wxSQLite3JournalMode mode, const wxString & database = wxEmptyString ) -->
  <function name="wxSQLite3Database::SetJournalMode">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxSQLite3JournalMode"/>
    <use-retval/>
    <arg nr="1" direction="in"/>
    <arg nr="2" default="wxEmptyString" direction="in"/>
  </function>
  <!-- int wxSQLite3Database::SetLimit( wxSQLite3LimitType id, int newValue ) -->
  <function name="wxSQLite3Database::SetLimit">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void wxSQLite3Database::SetRollbackHook( wxSQLite3Hook * rollbackHook ) -->
  <function name="wxSQLite3Database::SetRollbackHook">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void wxSQLite3Database::SetSharedCache( bool enable ) -->
  <function name="wxSQLite3Database::SetSharedCache">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool wxSQLite3Database::SetTemporaryDirectory( const wxString & tempDirectory ) -->
  <function name="wxSQLite3Database::SetTemporaryDirectory">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <pure/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void wxSQLite3Database::SetUpdateHook( wxSQLite3Hook * updateHook ) -->
  <function name="wxSQLite3Database::SetUpdateHook">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void wxSQLite3Database::SetWriteAheadLogHook( wxSQLite3Hook * walHook ) -->
  <function name="wxSQLite3Database::SetWriteAheadLogHook">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void wxSQLite3Database::ShutdownSQLite( void ) -->
  <function name="wxSQLite3Database::ShutdownSQLite">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
  </function>
  <!-- bool wxSQLite3Database::UserAdd( const wxString & username, const wxString & password, bool isAdmin = false ) -->
  <function name="wxSQLite3Database::UserAdd">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" default="false" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool wxSQLite3Database::UserChange( const wxString & username, const wxString & password, bool isAdmin ) -->
  <function name="wxSQLite3Database::UserChange">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
    <arg nr="3" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool wxSQLite3Database::UserDelete( const wxString & username ) -->
  <function name="wxSQLite3Database::UserDelete">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool wxSQLite3Database::UserIsPrivileged( const wxString & username ) -->
  <function name="wxSQLite3Database::UserIsPrivileged">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- bool wxSQLite3Database::UserLogin( const wxString & username, const wxString & password ) -->
  <function name="wxSQLite3Database::UserLogin">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <arg nr="1" direction="in"/>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void wxSQLite3Database::Vacuum( void ) -->
  <function name="wxSQLite3Database::Vacuum">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- const wxString wxSQLite3Exception::ErrorCodeAsString( int errorCode ) -->
  <function name="wxSQLite3Exception::ErrorCodeAsString">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const wxString"/>
    <pure/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int wxSQLite3Exception::GetErrorCode( void ) -->
  <function name="wxSQLite3Exception::GetErrorCode">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
  </function>
  <!-- int wxSQLite3Exception::GetExtendedErrorCode( void ) -->
  <function name="wxSQLite3Exception::GetExtendedErrorCode">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
  </function>
  <!-- const wxString wxSQLite3Exception::GetMessage( void ) -->
  <function name="wxSQLite3Exception::GetMessage">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const wxString"/>
    <use-retval/>
  </function>
  <!-- void wxSQLite3FunctionContext::ExecAggregateFinalize( void * ctx ) -->
  <function name="wxSQLite3FunctionContext::ExecAggregateFinalize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void wxSQLite3FunctionContext::ExecAggregateStep( void * ctx, int argc, void * argv ) -->
  <function name="wxSQLite3FunctionContext::ExecAggregateStep">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- int wxSQLite3FunctionContext::ExecCommitHook( void * hook ) -->
  <function name="wxSQLite3FunctionContext::ExecCommitHook">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <pure/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void wxSQLite3FunctionContext::ExecRollbackHook( void * hook ) -->
  <function name="wxSQLite3FunctionContext::ExecRollbackHook">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void wxSQLite3FunctionContext::ExecScalarFunction( void * ctx, int argc, void * argv ) -->
  <function name="wxSQLite3FunctionContext::ExecScalarFunction">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void wxSQLite3FunctionContext::ExecWindowFinalize( void * ctx ) -->
  <function name="wxSQLite3FunctionContext::ExecWindowFinalize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void wxSQLite3FunctionContext::ExecWindowInverse( void * ctx, int argc, void * argv ) -->
  <function name="wxSQLite3FunctionContext::ExecWindowInverse">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void wxSQLite3FunctionContext::ExecWindowStep( void * ctx, int argc, void * argv ) -->
  <function name="wxSQLite3FunctionContext::ExecWindowStep">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="inout"/>
  </function>
  <!-- void wxSQLite3FunctionContext::ExecWindowValue( void * ctx ) -->
  <function name="wxSQLite3FunctionContext::ExecWindowValue">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- int wxSQLite3FunctionContext::GetAggregateCount( void ) const -->
  <function name="wxSQLite3FunctionContext::GetAggregateCount">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!-- void * wxSQLite3FunctionContext::GetAggregateStruct( int len ) const -->
  <function name="wxSQLite3FunctionContext::GetAggregateStruct">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void *"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int wxSQLite3FunctionContext::GetArgCount( void ) const -->
  <function name="wxSQLite3FunctionContext::GetArgCount">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!-- int wxSQLite3FunctionContext::GetArgType( int argIndex ) const -->
  <function name="wxSQLite3FunctionContext::GetArgType">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- double wxSQLite3FunctionContext::GetDouble( int argIndex, double nullValue = 0 ) const -->
  <function name="wxSQLite3FunctionContext::GetDouble">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="double"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
      <valid>-1.79769e+308:1.79769e+308</valid>
    </arg>
  </function>
  <!-- int wxSQLite3FunctionContext::GetInt( int argIndex, int nullValue = 0 ) const -->
  <function name="wxSQLite3FunctionContext::GetInt">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- wxLongLong wxSQLite3FunctionContext::GetInt64( int argIndex, wxLongLong nullValue = 0 ) const -->
  <function name="wxSQLite3FunctionContext::GetInt64">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxLongLong"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="0" direction="in"/>
  </function>
  <!-- void * wxSQLite3FunctionContext::GetPointer( int argIndex, const wxString & pointerType ) const -->
  <function name="wxSQLite3FunctionContext::GetPointer">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void *"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in"/>
  </function>
  <!-- wxString wxSQLite3FunctionContext::GetString( int argIndex, const wxString & nullValue = wxEmptyString ) const -->
  <function name="wxSQLite3FunctionContext::GetString">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxString"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" default="wxEmptyString" direction="in"/>
  </function>
  <!-- bool wxSQLite3FunctionContext::IsNull( int argIndex ) const -->
  <function name="wxSQLite3FunctionContext::IsNull">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- const char * wxSQLite3FunctionContext::MakePointerTypeCopy( const wxString & pointerType ) -->
  <function name="wxSQLite3FunctionContext::MakePointerTypeCopy">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const char *"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void wxSQLite3FunctionContext::SetResultArg( int argIndex ) -->
  <function name="wxSQLite3FunctionContext::SetResultArg">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void wxSQLite3FunctionContext::SetResultError( const wxString & errmsg ) -->
  <function name="wxSQLite3FunctionContext::SetResultError">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void wxSQLite3FunctionContext::SetResultNull( void ) -->
  <function name="wxSQLite3FunctionContext::SetResultNull">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void wxSQLite3FunctionContext::SetResultZeroBlob( int blobSize ) -->
  <function name="wxSQLite3FunctionContext::SetResultZeroBlob">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool wxSQLite3Hook::CommitCallback( void ) -->
  <function name="wxSQLite3Hook::CommitCallback">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- wxSQLite3Database * wxSQLite3Hook::GetDatabase( void ) -->
  <function name="wxSQLite3Hook::GetDatabase">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxSQLite3Database *"/>
    <use-retval/>
  </function>
  <!-- void wxSQLite3Hook::RollbackCallback( void ) -->
  <function name="wxSQLite3Hook::RollbackCallback">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void wxSQLite3Hook::SetDatabase( wxSQLite3Database * db ) -->
  <function name="wxSQLite3Hook::SetDatabase">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void wxSQLite3Logger::Activate( bool active = true ) -->
  <function name="wxSQLite3Logger::Activate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" default="true" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void wxSQLite3Logger::Deactivate( void ) -->
  <function name="wxSQLite3Logger::Deactivate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- void wxSQLite3Logger::ExecLoggerHook( void * logger, int errorCode, const char * errorMsg ) -->
  <function name="wxSQLite3Logger::ExecLoggerHook">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <pure/>
    <arg nr="1" direction="inout"/>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="3" direction="inout">
      <not-uninit/>
    </arg>
  </function>
  <!-- void wxSQLite3Logger::HandleLogMessage( int errorCode, const wxString & errorMessage ) -->
  <function name="wxSQLite3Logger::HandleLogMessage">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in"/>
  </function>
  <!-- bool wxSQLite3Logger::IsActive( void ) -->
  <function name="wxSQLite3Logger::IsActive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- const wxString & wxSQLite3NamedCollection::GetName( void ) -->
  <function name="wxSQLite3NamedCollection::GetName">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const wxString &amp;"/>
    <use-retval/>
  </function>
  <!-- bool wxSQLite3NamedCollection::IsOk( void ) -->
  <function name="wxSQLite3NamedCollection::IsOk">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- void wxSQLite3RegExpOperator::Execute( wxSQLite3FunctionContext & ctx ) -->
  <function name="wxSQLite3RegExpOperator::Execute">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="inout"/>
  </function>
  <!-- void wxSQLite3ResultSet::CheckStmt( void ) const -->
  <function name="wxSQLite3ResultSet::CheckStmt">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
  </function>
  <!-- bool wxSQLite3ResultSet::CursorMoved( void ) const -->
  <function name="wxSQLite3ResultSet::CursorMoved">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool wxSQLite3ResultSet::Eof( void ) const -->
  <function name="wxSQLite3ResultSet::Eof">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- int wxSQLite3ResultSet::FindColumnIndex( const wxString & columnName ) const -->
  <function name="wxSQLite3ResultSet::FindColumnIndex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- int wxSQLite3ResultSet::GetColumnCount( void ) const -->
  <function name="wxSQLite3ResultSet::GetColumnCount">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!-- wxString wxSQLite3ResultSet::GetColumnName( int columnIndex ) const -->
  <function name="wxSQLite3ResultSet::GetColumnName">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxString"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int wxSQLite3ResultSet::GetColumnType( int columnIndex ) const -->
  <function name="wxSQLite3ResultSet::GetColumnType">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- wxString wxSQLite3ResultSet::GetDatabaseName( int columnIndex ) const -->
  <function name="wxSQLite3ResultSet::GetDatabaseName">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxString"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- wxString wxSQLite3ResultSet::GetDeclaredColumnType( int columnIndex ) const -->
  <function name="wxSQLite3ResultSet::GetDeclaredColumnType">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxString"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- wxString wxSQLite3ResultSet::GetExpandedSQL( void ) const -->
  <function name="wxSQLite3ResultSet::GetExpandedSQL">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxString"/>
    <use-retval/>
    <const/>
  </function>
  <!-- wxString wxSQLite3ResultSet::GetOriginName( int columnIndex ) const -->
  <function name="wxSQLite3ResultSet::GetOriginName">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxString"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- wxString wxSQLite3ResultSet::GetSQL( void ) const -->
  <function name="wxSQLite3ResultSet::GetSQL">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxString"/>
    <use-retval/>
    <const/>
  </function>
  <!-- wxString wxSQLite3ResultSet::GetTableName( int columnIndex ) const -->
  <function name="wxSQLite3ResultSet::GetTableName">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxString"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- bool wxSQLite3ResultSet::IsOk( void ) const -->
  <function name="wxSQLite3ResultSet::IsOk">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool wxSQLite3ResultSet::NextRow( void ) -->
  <function name="wxSQLite3ResultSet::NextRow">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- void wxSQLite3Statement::BindBool( int paramIndex, bool value ) -->
  <function name="wxSQLite3Statement::BindBool">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void wxSQLite3Statement::BindDate( int paramIndex, const wxDateTime & date ) -->
  <function name="wxSQLite3Statement::BindDate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void wxSQLite3Statement::BindDateTime( int paramIndex, const wxDateTime & datetime ) -->
  <function name="wxSQLite3Statement::BindDateTime">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void wxSQLite3Statement::BindJulianDayNumber( int paramIndex, const wxDateTime & datetime ) -->
  <function name="wxSQLite3Statement::BindJulianDayNumber">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void wxSQLite3Statement::BindNull( int paramIndex ) -->
  <function name="wxSQLite3Statement::BindNull">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void wxSQLite3Statement::BindNumericDateTime( int paramIndex, const wxDateTime & datetime ) -->
  <function name="wxSQLite3Statement::BindNumericDateTime">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void wxSQLite3Statement::BindTime( int paramIndex, const wxDateTime & time ) -->
  <function name="wxSQLite3Statement::BindTime">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void wxSQLite3Statement::BindTimestamp( int paramIndex, const wxDateTime & timestamp ) -->
  <function name="wxSQLite3Statement::BindTimestamp">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void wxSQLite3Statement::BindUnixDateTime( int paramIndex, const wxDateTime & datetime ) -->
  <function name="wxSQLite3Statement::BindUnixDateTime">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void wxSQLite3Statement::BindZeroBlob( int paramIndex, int blobSize ) -->
  <function name="wxSQLite3Statement::BindZeroBlob">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void wxSQLite3Statement::CheckDatabase( void ) const -->
  <function name="wxSQLite3Statement::CheckDatabase">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
  </function>
  <!-- void wxSQLite3Statement::CheckStmt( void ) const -->
  <function name="wxSQLite3Statement::CheckStmt">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
  </function>
  <!-- void wxSQLite3Statement::ClearBindings( void ) -->
  <function name="wxSQLite3Statement::ClearBindings">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- wxSQLite3ResultSet wxSQLite3Statement::ExecuteQuery( void ) -->
  <function name="wxSQLite3Statement::ExecuteQuery">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxSQLite3ResultSet"/>
    <use-retval/>
  </function>
  <!-- int wxSQLite3Statement::ExecuteScalar( void ) -->
  <function name="wxSQLite3Statement::ExecuteScalar">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
  </function>
  <!-- int wxSQLite3Statement::ExecuteUpdate( void ) -->
  <function name="wxSQLite3Statement::ExecuteUpdate">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
  </function>
  <!-- wxString wxSQLite3Statement::GetExpandedSQL( void ) const -->
  <function name="wxSQLite3Statement::GetExpandedSQL">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxString"/>
    <use-retval/>
    <const/>
  </function>
  <!-- int wxSQLite3Statement::GetParamCount( void ) const -->
  <function name="wxSQLite3Statement::GetParamCount">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!-- int wxSQLite3Statement::GetParamIndex( const wxString & paramName ) const -->
  <function name="wxSQLite3Statement::GetParamIndex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- wxString wxSQLite3Statement::GetParamName( int paramIndex ) const -->
  <function name="wxSQLite3Statement::GetParamName">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxString"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- wxString wxSQLite3Statement::GetSQL( void ) const -->
  <function name="wxSQLite3Statement::GetSQL">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxString"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool wxSQLite3Statement::IsBusy( void ) const -->
  <function name="wxSQLite3Statement::IsBusy">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool wxSQLite3Statement::IsOk( void ) const -->
  <function name="wxSQLite3Statement::IsOk">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool wxSQLite3Statement::IsReadOnly( void ) const -->
  <function name="wxSQLite3Statement::IsReadOnly">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- void wxSQLite3Statement::Reset( void ) -->
  <function name="wxSQLite3Statement::Reset">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- int wxSQLite3Statement::Status( wxSQLite3StatementStatus opCode, bool resetFlag = false ) const -->
  <function name="wxSQLite3Statement::Status">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <const/>
    <arg nr="1" direction="in"/>
    <arg nr="2" default="false" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void wxSQLite3StatementBuffer::Clear( void ) -->
  <function name="wxSQLite3StatementBuffer::Clear">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- const char * wxSQLite3StatementBuffer::FormatV( const char * format, va_list va ) -->
  <function name="wxSQLite3StatementBuffer::FormatV">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="const char *"/>
    <arg nr="1" direction="inout">
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in"/>
  </function>
  <!-- void wxSQLite3StringCollection::Bind( const wxArrayString & stringCollection ) -->
  <function name="wxSQLite3StringCollection::Bind">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- void wxSQLite3Table::CheckResults( void ) const -->
  <function name="wxSQLite3Table::CheckResults">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <const/>
  </function>
  <!-- void wxSQLite3Table::Finalize( void ) -->
  <function name="wxSQLite3Table::Finalize">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- int wxSQLite3Table::FindColumnIndex( const wxString & columnName ) const -->
  <function name="wxSQLite3Table::FindColumnIndex">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <const/>
    <arg nr="1" direction="in"/>
  </function>
  <!-- int wxSQLite3Table::GetColumnCount( void ) const -->
  <function name="wxSQLite3Table::GetColumnCount">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!-- wxString wxSQLite3Table::GetColumnName( int columnIndex ) const -->
  <function name="wxSQLite3Table::GetColumnName">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="wxString"/>
    <const/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- int wxSQLite3Table::GetRowCount( void ) const -->
  <function name="wxSQLite3Table::GetRowCount">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="int"/>
    <use-retval/>
    <const/>
  </function>
  <!-- bool wxSQLite3Table::IsOk( void ) const -->
  <function name="wxSQLite3Table::IsOk">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
    <const/>
  </function>
  <!-- void wxSQLite3Table::SetRow( int row ) -->
  <function name="wxSQLite3Table::SetRow">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
    <arg nr="1" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- void wxSQLite3Transaction::Commit( void ) -->
  <function name="wxSQLite3Transaction::Commit">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
  <!-- bool wxSQLite3Transaction::IsActive( void ) -->
  <function name="wxSQLite3Transaction::IsActive">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="bool"/>
    <use-retval/>
  </function>
  <!-- void wxSQLite3Transaction::Rollback( void ) -->
  <function name="wxSQLite3Transaction::Rollback">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <returnValue type="void"/>
  </function>
</def>
