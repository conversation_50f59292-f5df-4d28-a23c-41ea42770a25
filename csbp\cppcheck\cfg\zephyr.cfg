<?xml version="1.0"?>
<def format="2">

  <function name="device_is_ready">
    <returnValue type="bool"/>
    <use-retval/>
    <pure/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
      <not-bool/>
    </arg>
  </function>

  <function name="gpio_init_callback">
    <returnValue type="void"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-null/>
      <not-bool/>
    </arg>
    <arg nr="3" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>

  <function name="gpio_add_callback">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-null/>
      <not-bool/>
    </arg>
  </function>

  <function name="gpio_pin_configure_dt,gpio_pin_interrupt_configure_dt">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
      <not-bool/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
      <not-bool/>
    </arg>
  </function>

  <function name="gpio_pin_toggle_dt">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <leak-ignore/>
    <arg nr="1" direction="in">
      <not-uninit/>
      <not-null/>
      <not-bool/>
    </arg>
  </function>

  <function name="k_msleep">
    <returnValue type="int32_t"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-uninit/>
      <valid>0:2147483647</valid>
	</arg>
  </function>

  <function name="printk">
    <noreturn>false</noreturn>
    <leak-ignore/>
    <formatstr/>
    <arg nr="1" direction="in">
      <formatstr/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>

  <define name="KERN_EMERG" value="&quot;0&quot;"/>
  <define name="KERN_ALERT" value="&quot;1&quot;"/>
  <define name="KERN_CRIT" value="&quot;2&quot;"/>
  <define name="KERN_ERR" value="&quot;3&quot;"/>
  <define name="KERN_WARNING" value="&quot;4&quot;"/>
  <define name="KERN_NOTICE" value="&quot;5&quot;"/>
  <define name="KERN_INFO" value="&quot;6&quot;"/>
  <define name="KERN_DEBUG" value="&quot;7&quot;"/>
  <define name="KERN_DEFAULT" value="&quot;&quot;"/>
  <define name="KERN_CONT" value="&quot;c&quot;"/>

  <define name="ARRAY_SIZE(array)" value="(sizeof(array) / sizeof((array)[0]))"/>
  <define name="BIT(x)" value="(1UL&lt;&lt;x)"/>
  <define name="IS_ENABLED(flag)" value="flag"/>
</def>
