<?xml version="1.0"?>
<def format="2">
  <!-- Reference to zlib documentation https://www.zlib.net/manual.html -->
  <!-- Data structures and types -->
  <podtype name="alloc_func"/>
  <podtype name="Byte" sign="u" size="1"/>
  <podtype name="Bytef" sign="u" size="1"/>
  <podtype name="charf" sign="s" size="1"/>
  <podtype name="free_func"/>
  <podtype name="gzFile"/>
  <podtype name="gzFile_s"/>
  <podtype name="gz_header"/>
  <podtype name="gz_headerp"/>
  <podtype name="gz_header_s"/>
  <podtype name="intf" sign="s"/>
  <podtype name="uInt" sign="u"/>
  <podtype name="uIntf"/>
  <podtype name="uLong" sign="u"/>
  <podtype name="uLongf" sign="u"/>
  <podtype name="voidp"/>
  <podtype name="voidpc"/>
  <podtype name="voidpf"/>
  <podtype name="z_crc_t" sign="u"/>
  <podtype name="z_off_t" sign="s"/>
  <podtype name="z_off64_t" sign="s" size="8"/>
  <podtype name="z_stream"/>
  <podtype name="z_streamp"/>
  <podtype name="z_stream_s"/>
  <!-- Defines -->
  <define name="z_const" value="const"/>
  <define name="z_longlong" value="long long"/>
  <define name="z_size_t" value="size_t"/>
  <define name="Z_NO_FLUSH" value="0"/>
  <define name="Z_PARTIAL_FLUSH" value="1"/>
  <define name="Z_SYNC_FLUSH" value="2"/>
  <define name="Z_FULL_FLUSH" value="3"/>
  <define name="Z_FINISH" value="4"/>
  <define name="Z_BLOCK" value="5"/>
  <define name="Z_TREES" value="6"/>
  <define name="Z_OK" value="0"/>
  <define name="Z_STREAM_END" value="1"/>
  <define name="Z_NEED_DICT" value="2"/>
  <define name="Z_ERRNO" value="(-1)"/>
  <define name="Z_STREAM_ERROR" value="(-2)"/>
  <define name="Z_DATA_ERROR" value="(-3)"/>
  <define name="Z_MEM_ERROR" value="(-4)"/>
  <define name="Z_BUF_ERROR" value="(-5)"/>
  <define name="Z_VERSION_ERROR" value="(-6)"/>
  <define name="Z_NO_COMPRESSION" value="0"/>
  <define name="Z_BEST_SPEED" value="1"/>
  <define name="Z_BEST_COMPRESSION" value="9"/>
  <define name="Z_DEFAULT_COMPRESSION" value="(-1)"/>
  <define name="Z_FILTERED" value="1"/>
  <define name="Z_HUFFMAN_ONLY" value="2"/>
  <define name="Z_RLE" value="3"/>
  <define name="Z_FIXED" value="4"/>
  <define name="Z_DEFAULT_STRATEGY" value="0"/>
  <define name="Z_BINARY" value="0"/>
  <define name="Z_TEXT" value="1"/>
  <define name="Z_ASCII" value="Z_TEXT"/>
  <define name="Z_UNKNOWN" value="2"/>
  <define name="Z_DEFLATED" value="8"/>
  <define name="Z_NULL" value="0"/>
  <define name="zlib_version" value="zlibVersion()"/>
  <!-- Functions -->
  <!-- ZEXTERN const char * ZEXPORT zlibVersion OF((void)); -->
  <function name="zlibVersion">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="const char*"/>
    <noreturn>false</noreturn>
  </function>
  <!-- ZEXTERN int ZEXPORT deflateInit OF((z_streamp strm, int level)); -->
  <function name="deflateInit">
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout">
      <not-bool/>
      <not-null/>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT deflate OF((z_streamp strm, int flush)); -->
  <function name="deflate">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT deflateEnd OF((z_streamp strm)); -->
  <function name="deflateEnd">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout">
      <not-bool/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT inflateInit OF((z_streamp strm)); -->
  <function name="inflateInit">
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout">
      <not-bool/>
      <not-null/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT inflate OF((z_streamp strm, int flush)); -->
  <function name="inflate">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT inflateEnd OF((z_streamp strm)); -->
  <function name="inflateEnd">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout">
      <not-bool/>
      <not-uninit/>
    </arg>
  </function>
  <!-- Advanced functions -->
  <!-- ZEXTERN int ZEXPORT deflateInit2 OF((z_streamp strm,
                                            int  level,
                                            int  method,
                                            int  windowBits,
                                            int  memLevel,
                                            int  strategy)); -->
  <function name="deflateInit2">
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout">
      <not-bool/>
      <not-null/>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
    <arg nr="5" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
    <arg nr="6" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT deflateSetDictionary OF((z_streamp strm,
                                                    const Bytef *dictionary,
                                                    uInt  dictLength)); -->
  <function name="deflateSetDictionary">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT deflateGetDictionary OF((z_streamp strm,
                                                    Bytef *dictionary,
                                                    uInt  *dictLength)); -->
  <function name="deflateGetDictionary">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-bool/>
    </arg>
    <arg nr="3" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT deflateCopy OF((z_streamp dest,
                                           z_streamp source)); -->
  <function name="deflateCopy">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="out">
      <not-bool/>
      <not-null/>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT deflateReset OF((z_streamp strm)); -->
  <function name="deflateReset">
    <leak-ignore/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT deflateParams OF((z_streamp strm,
                                             int level,
                                             int strategy)); -->
  <function name="deflateParams">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT deflateTune OF((z_streamp strm,
                                           int good_length,
                                           int max_lazy,
                                           int nice_length,
                                           int max_chain)); -->
  <function name="deflateTune">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
    <arg nr="4" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
    <arg nr="5" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN uLong ZEXPORT deflateBound OF((z_streamp strm,
                                              uLong sourceLen)); -->
  <function name="deflateBound">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="uLong"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT deflatePending OF((z_streamp strm,
                                              unsigned *pending,
                                              int *bits)); -->
  <function name="deflatePending">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-bool/>
    </arg>
    <arg nr="3" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT deflatePrime OF((z_streamp strm,
                                            int bits,
                                            int value)); -->
  <function name="deflatePrime">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>:16</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT deflateSetHeader OF((z_streamp strm,
                                                gz_headerp head)); -->
  <function name="deflateSetHeader">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT inflateInit2 OF((z_streamp strm,
                                            int  windowBits)); -->
  <function name="inflateInit2">
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout">
      <not-bool/>
      <not-null/>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT inflateSetDictionary OF((z_streamp strm,
                                                    const Bytef *dictionary,
                                                    uInt  dictLength)); -->
  <function name="inflateSetDictionary">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT inflateGetDictionary OF((z_streamp strm,
                                                    Bytef *dictionary,
                                                    uInt  *dictLength)); -->
  <function name="inflateGetDictionary">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-bool/>
    </arg>
    <arg nr="3" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT inflateSync OF((z_streamp strm)); -->
  <function name="inflateSync">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT inflateCopy OF((z_streamp dest,
                                           z_streamp source)); -->
  <function name="inflateCopy">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="out">
      <not-bool/>
      <not-null/>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT inflateReset OF((z_streamp strm)); -->
  <function name="inflateReset">
    <leak-ignore/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT inflateReset2 OF((z_streamp strm,
                                             int windowBits)); -->
  <function name="inflateReset2">
    <leak-ignore/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT inflatePrime OF((z_streamp strm,
                                            int bits,
                                            int value)); -->
  <function name="inflatePrime">
    <leak-ignore/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>:16</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN long ZEXPORT inflateMark OF((z_streamp strm)); -->
  <function name="inflateMark">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="long"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT inflateGetHeader OF((z_streamp strm,
                                                gz_headerp head)); -->
  <function name="inflateGetHeader">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-bool/>
      <not-null/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT inflateBackInit OF((z_streamp strm,
                                               int windowBits,
                                               unsigned char FAR *window)); -->
  <function name="inflateBackInit">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
    <arg nr="3">
      <not-bool/>
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="2"/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT inflateBack OF((z_streamp strm,
                                           in_func in, void FAR *in_desc,
                                           out_func out, void FAR *out_desc)); -->
  <function name="inflateBack">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <arg nr="1" direction="inout">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="3">
      <not-bool/>
    </arg>
    <arg nr="4">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="5">
      <not-bool/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT inflateBackEnd OF((z_streamp strm)); -->
  <function name="inflateBackEnd">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout">
      <not-bool/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN uLong ZEXPORT zlibCompileFlags OF((void)); -->
  <function name="zlibCompileFlags">
    <use-retval/>
    <returnValue type="uLong"/>
    <noreturn>false</noreturn>
  </function>
  <!-- Utility functions -->
  <!-- ZEXTERN int ZEXPORT compress OF((Bytef *dest, uLongf *destLen,
                                        const Bytef *source, uLong sourceLen)); -->
  <function name="compress">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="out">
      <not-bool/>
      <not-null/>
    </arg>
    <arg nr="2" direction="inout">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="4"/>
    </arg>
    <arg nr="4" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT compress2 OF((Bytef *dest, uLongf *destLen,
                                         const Bytef *source, uLong sourceLen,
                                         int level)); -->
  <function name="compress2">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="out">
      <not-bool/>
      <not-null/>
    </arg>
    <arg nr="2" direction="inout">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="4"/>
    </arg>
    <arg nr="4" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="5" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN uLong ZEXPORT compressBound OF((uLong sourceLen)); -->
  <function name="compressBound">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="uLong"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT uncompress OF((Bytef *dest, uLongf *destLen,
                                          const Bytef *source, uLong sourceLen)); -->
  <function name="uncompress">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="out">
      <not-bool/>
      <not-null/>
    </arg>
    <arg nr="2" direction="inout">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="4"/>
    </arg>
    <arg nr="4" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT uncompress2 OF((Bytef *dest,   uLongf *destLen,
                                           const Bytef *source, uLong *sourceLen)); -->
  <function name="uncompress2">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="out">
      <not-bool/>
      <not-null/>
    </arg>
    <arg nr="2" direction="inout">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="4"/>
    </arg>
    <arg nr="4" direction="inout">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- gzip file access function -->
  <memory>
    <alloc init="true">gzopen</alloc>
    <alloc init="true">gzopen64</alloc>
    <alloc init="true">gzdopen</alloc>
    <alloc init="true">gzopen_w</alloc>
    <dealloc>gzclose</dealloc>
    <dealloc>gzclose_r</dealloc>
    <dealloc>gzclose_w</dealloc>
  </memory>
  <!-- ZEXTERN gzFile ZEXPORT gzopen OF((const char *path, const char *mode)); -->
  <function name="gzopen,gzopen64">
    <use-retval/>
    <returnValue type="gzFile"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-bool/>
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- ZEXTERN gzFile ZEXPORT gzdopen OF((int fd, const char *mode)); -->
  <function name="gzdopen">
    <use-retval/>
    <returnValue type="gzFile"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT gzbuffer OF((gzFile file, unsigned size)); -->
  <function name="gzbuffer">
    <leak-ignore/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT gzsetparams OF((gzFile file, int level, int strategy)); -->
  <function name="gzsetparams">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="inout">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT gzread OF((gzFile file, voidp buf, unsigned len)); -->
  <function name="gzread">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-bool/>
      <not-null/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- ZEXTERN z_size_t ZEXPORT gzfread OF((voidp buf, z_size_t size, z_size_t nitems,
                                            gzFile file)); -->
  <function name="gzfread">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="z_size_t"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="out">
      <not-bool/>
      <not-null/>
      <minsize type="mul" arg="2" arg2="3"/>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="4">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT gzwrite OF((gzFile file,
                                       voidpc buf, unsigned len)); -->
  <function name="gzwrite">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-null/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- ZEXTERN z_size_t ZEXPORT gzfwrite OF((voidpc buf, z_size_t size,
                                             z_size_t nitems, gzFile file)); -->
  <function name="gzfwrite">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="z_size_t"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-bool/>
      <not-null/>
      <not-uninit/>
      <minsize type="mul" arg="2" arg2="3"/>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="4">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORTVA gzprintf Z_ARG((gzFile file, const char *format, ...)); -->
  <function name="gzprintf">
    <formatstr/>
    <leak-ignore/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <formatstr/>
      <not-bool/>
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT gzputs OF((gzFile file, const char *s)); -->
  <function name="gzputs">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
  </function>
  <!-- ZEXTERN char * ZEXPORT gzgets OF((gzFile file, char *buf, int len)); -->
  <function name="gzgets">
    <leak-ignore/>
    <returnValue type="char*"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-bool/>
      <not-null/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT gzputc OF((gzFile file, int c)); -->
  <function name="gzputc">
    <leak-ignore/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:255</valid>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT gzgetc OF((gzFile file)); -->
  <function name="gzgetc">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT gzungetc OF((int c, gzFile file)); -->
  <function name="gzungetc">
    <leak-ignore/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:255</valid>
    </arg>
    <arg nr="2">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT gzflush OF((gzFile file, int flush)); -->
  <function name="gzflush">
    <leak-ignore/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN z_off_t ZEXPORT gzseek OF((gzFile file,
                                          z_off_t offset, int whence)); -->
  <function name="gzseek">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="z_off_t"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
  </function>
  <function name="gzseek64">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="z_off64_t"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2">
      <not-bool/>
      <not-uninit/>
    </arg>
    <arg nr="3">
      <not-bool/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT gzrewind OF((gzFile file)); -->
  <function name="gzrewind">
    <leak-ignore/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN z_off_t ZEXPORT gztell OF((gzFile file)); -->
  <function name="gztell">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="z_off_t"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <function name="gztell64">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="z_off64_t"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN z_off_t ZEXPORT gzoffset OF((gzFile file)); -->
  <function name="gzoffset">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="z_off_t"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <function name="gzoffset64">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="z_off64_t"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT gzeof OF((gzFile file)); -->
  <function name="gzeof">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT gzdirect OF((gzFile file)); -->
  <function name="gzdirect">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN int ZEXPORT gzclose OF((gzFile file)); -->
  <!-- ZEXTERN int ZEXPORT gzclose_r OF((gzFile file)); -->
  <!-- ZEXTERN int ZEXPORT gzclose_w OF((gzFile file)); -->
  <function name="gzclose,gzclose_r,gzclose_w">
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN const char * ZEXPORT gzerror OF((gzFile file, int *errnum)); -->
  <function name="gzerror">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="const char*"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="out">
      <not-bool/>
    </arg>
  </function>
  <!-- ZEXTERN void ZEXPORT gzclearerr OF((gzFile file)); -->
  <function name="gzclearerr">
    <leak-ignore/>
    <returnValue type="void"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- Checksum functions -->
  <!-- ZEXTERN uLong ZEXPORT adler32 OF((uLong adler, const Bytef *buf, uInt len)); -->
  <function name="adler32">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="uLong"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- ZEXTERN uLong ZEXPORT adler32_z OF((uLong adler, const Bytef *buf,
                                           z_size_t len)); -->
  <function name="adler32_z">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="uLong"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- ZEXTERN uLong ZEXPORT adler32_combine OF((uLong adler1, uLong adler2,
                                                 z_off_t len2)); -->
  <function name="adler32_combine,adler32_combine64">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="uLong"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN uLong ZEXPORT crc32 OF((uLong crc, const Bytef *buf, uInt len)); -->
  <function name="crc32">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="uLong"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- ZEXTERN uLong ZEXPORT crc32_z OF((uLong adler, const Bytef *buf,
                                         z_size_t len)); -->
  <function name="crc32_z">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="uLong"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-uninit/>
      <minsize type="argvalue" arg="3"/>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
  </function>
  <!-- ZEXTERN uLong ZEXPORT crc32_combine OF((uLong crc1, uLong crc2, z_off_t len2)); -->
  <function name="crc32_combine,crc32_combine64">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="uLong"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-uninit/>
      <valid>0:</valid>
    </arg>
    <arg nr="3" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
  </function>
  <!-- Undocumented functions -->
  <!-- ZEXTERN const char   * ZEXPORT zError           OF((int)); -->
  <function name="zError">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="const char*"/>
    <noreturn>false</noreturn>
    <arg nr="1" direction="in">
      <not-bool/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN int            ZEXPORT inflateSyncPoint OF((z_streamp z)); -->
  <function name="inflateSyncPoint">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN const z_crc_t FAR * ZEXPORT get_crc_table    OF((void)); -->
  <function name="get_crc_table">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="z_crc_t"/>
    <noreturn>false</noreturn>
  </function>
  <!-- ZEXTERN int            ZEXPORT inflateUndermine OF((z_streamp, int)); -->
  <function name="inflateUndermine">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN int            ZEXPORT inflateValidate OF((z_streamp, int)); -->
  <function name="inflateValidate">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN unsigned long  ZEXPORT inflateCodesUsed OF ((z_streamp)); -->
  <function name="inflateCodesUsed">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="unsigned long"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN int            ZEXPORT deflateResetKeep OF((z_streamp)); -->
  <!-- ZEXTERN int            ZEXPORT inflateResetKeep OF((z_streamp)); -->
  <function name="deflateResetKeep,inflateResetKeep">
    <leak-ignore/>
    <use-retval/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
  </function>
  <!-- ZEXTERN int            ZEXPORTVA gzvprintf Z_ARG((gzFile file,
                                                         const char *format,
                                                         va_list va)); -->
  <function name="gzvprintf">
    <leak-ignore/>
    <returnValue type="int"/>
    <noreturn>false</noreturn>
    <arg nr="1">
      <not-bool/>
      <not-null/>
      <not-uninit/>
    </arg>
    <arg nr="2" direction="in">
      <not-bool/>
      <not-null/>
      <not-uninit/>
      <strz/>
    </arg>
    <arg nr="3">
      <not-bool/>
    </arg>
  </function>
</def>
