#!/bin/bash


AppName=${1:-default}
compile_commands=${2}

# cppcheck 命令行选项
#OPTIONS="--enable=style \
#         --enable=performance \
#         --enable=portability \
#         --check-level=exhaustive"

# 根据你的配置，添加相应的检查项
EXTRA_OPTIONS=""

# 根据你的配置，添加相应的抑制选项
SUPPRESS="--suppress=information \
          --suppress=missingIncludeSystem \
          --suppress=unmatchedSuppression \
          --suppress=passedByValue \
          --suppress=unusedFunction \
          --suppress=uninitMemberVar \
          --suppress=uninitvar \
          --suppress=nullPointer \
          --suppress=shadowVariable \
          --suppress=variableScope \
          --suppress=duplicateExpression \
          --suppress=useInitializationList"

#pip3 install Pygments==2.5.2 -i https://mirrors.aliyun.com/pypi/simple/

# 如果需要设置最大函数复杂度，可以取消下面这行的注释
# OPTIONS="$OPTIONS --max-complexity=10"

# 运行 cppcheck
script_dir=$(dirname $(readlink -f $0))
base_dir=$script_dir/../../csbase
out_dir=$script_dir/output/$AppName
src_dir=$script_dir/../../$AppName
if [ ! -d $out_dir ];then
    mkdir -p $out_dir
fi

$script_dir/cppcheck -j 4 --project=$compile_commands --suppressions-list=$script_dir/ignor-cppcheck.txt $OPTIONS $EXTRA_OPTIONS $SUPPRESS --xml 2> $out_dir/err.xml
$script_dir/cppcheck-htmlreport --file=$out_dir/err.xml --report-dir=$out_dir --source-dir=$src_dir --source-encoding="iso8859-1"
#echo $script_dir/cppcheck-htmlreport --file=$out_dir/err.xml --report-dir=$out_dir --source-dir=$src_dir --source-encoding="iso8859-1"


