    
#!/bin/bash
exit 0
key="27082dff-2057-4875-8119-526d4e8b7ff6"


# 文件路径
file_path=$1  # 从命令行参数接收文件路径

# 检查是否提供了文件路径
if [ -z "$file_path" ]; then
  echo "请提供文件路径作为参数。"
  exit 1
fi


# API URL
url="https://qyapi.weixin.qq.com/cgi-bin/webhook/upload_media?key=${key}&type=file"

# 获取文件名和文件长度
filename=$(basename "$file_path")
filelength=$(stat -c%s "$file_path")
content_type=$(file --mime-type -b "$file_path")

# 上传文件并获取响应
response=$(curl -F "media=@${file_path};filename=${filename}" "$url")

# 打印响应
echo "Response: $response"

# 提取 media_id
media_id=$(echo $response | jq -r '.media_id')

# 打印 media_id
echo "Media ID: $media_id"
    

# API URL
url="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=${key}"

# JSON 数据
json_data=$(cat <<EOF
{
    "msgtype": "file",
    "file": {
         "media_id": "$media_id"
    }
}
EOF
)

# 发送 JSON 数据并获取响应
response=$(curl -X POST -H "Content-Type: application/json" -d "$json_data" "$url")

# 打印响应
echo "Response: $response"



