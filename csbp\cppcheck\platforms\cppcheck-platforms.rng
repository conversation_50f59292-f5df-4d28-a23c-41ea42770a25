<?xml version="1.0" encoding="UTF-8"?>
<grammar ns="" xmlns="http://relaxng.org/ns/structure/1.0" datatypeLibrary="http://www.w3.org/2001/XMLSchema-datatypes">
	<start>
		<element name="platform">
			<element name="char_bit">
				<data type="integer"/>
			</element>
			<element name="default-sign">
				<data type="NCName"/>
			</element>
			<element name="sizeof">
				<element name="bool">
					<data type="integer"/>
				</element>
				<element name="short">
					<data type="integer"/>
				</element>
				<element name="int">
					<data type="integer"/>
				</element>
				<element name="long">
					<data type="integer"/>
				</element>
				<element name="long-long">
					<data type="integer"/>
				</element>
				<element name="float">
					<data type="integer"/>
				</element>
				<element name="double">
					<data type="integer"/>
				</element>
				<element name="long-double">
					<data type="integer"/>
				</element>
				<element name="pointer">
					<data type="integer"/>
				</element>
				<element name="size_t">
					<data type="integer"/>
				</element>
				<element name="wchar_t">
					<data type="integer"/>
				</element>
			</element>
		</element>
	</start>
</grammar>
