#!/bin/bash

script_dir=$(dirname $(readlink -f $0))
AKCS_SRC_ROOT=${script_dir}/../../



MAKE_ROOT_DIR=("csadapt" "csconfig" "csconfig-office" "csmain" 
"csstorage" "csgate" "csroute" "cssession")

MAKE_BUILD_DIR=("csvrtsp" "csoffice" "csresid")


run_cppcheck() {
    local CMakeDir=$1
    local AppName=$2

    local CompileCommands=${AKCS_SRC_ROOT}/$CMakeDir/compile_commands.json

    cd "${AKCS_SRC_ROOT}/$CMakeDir" || return
    cmake .
    cd "$script_dir"
    bash -x cppcheck.sh "$AppName" "$CompileCommands"

    ERROR_COUNT=$(grep 'severity="error"' "$script_dir/output/$AppName/err.xml" | wc -l)
    if [ "$ERROR_COUNT" -gt 0 ]; then
        datatime=$(date +"%Y-%m-%d-%H:%M:%S")
        filepath=$script_dir/output/$AppName-$datatime.tar.gz
        tar -czf $filepath -C "$script_dir/output" $AppName
        bash notify.sh  $filepath
        rm $filepath
    fi
}

ONLY_APP=
# 检查是否提供了第一个参数
if [ $# -gt 0 ]; then
    ONLY_APP=$1
    for AppName in "${MAKE_ROOT_DIR[@]}"; do
        if [[ "$AppName" == "$ONLY_APP" ]]; then
            run_cppcheck "$AppName" "$AppName"
            exit 0
        fi
    done
    for AppName in "${MAKE_BUILD_DIR[@]}"; do
        if [[ "$AppName" == "$ONLY_APP" ]]; then
            run_cppcheck "$AppName/build" "$AppName"
            exit 0
        fi
    done         
fi

# 处理 MAKE_ROOT_DIR
for AppName in "${MAKE_ROOT_DIR[@]}"; do
    run_cppcheck "$AppName" "$AppName"
done

# 处理 MAKE_BUILD_DIR
for AppName in "${MAKE_BUILD_DIR[@]}"; do
    run_cppcheck "$AppName/build" "$AppName"
done