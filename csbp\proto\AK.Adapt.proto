syntax = "proto3";
package AK.Adapt; 

message ConfigCommonMessage {
    uint32 msg_id = 1;                   
    bytes  msg_body = 2;
    string msg_key = 3;  
    uint32 project_type = 4;
}

message PersonnalUpdateUser{
	//cmd id:   MSG_P2A_PERSONNAL_UPDATE_USER
	string user = 1;	//主账号
	int32  type = 2;	//增删动作
}

message PersonnalUpdateNode{
	//cmd id:   MSG_P2A_PERSONNAL_UPDATE_NODE_DEV
	string node = 1;	//个人终端联动单元
	int32 update_type = 2;
}

message PersonalAlarmDeal{
	//cmd id:   MSG_P2A_PERSONNAL_ALARM_DEAL
	string area_node = 1;
	string user = 2;
	string alarm_id = 3;
	string result = 4;	
	string mac = 5;
	string alarm_time = 6;
}

message PerAddDev{
	//cmd id:   MSG_P2A_PERSONNAL_ADD_DEV
	string mac = 1;
	string mac_old = 2;	
}

message PerModDev{
	//cmd id:   MSG_P2A_PERSONNAL_MODIFY_DEV
	int32 mac_id = 1;
	int32 is_per = 2;	
}

message PerDelDev{
	//cmd id:   MSG_P2A_PERSONNAL_DEL_DEV
	string mac = 1;	
}

message PerDelUid{
	//cmd id:   MSG_P2A_PERSONNAL_DEL_UID
	string uid = 1;	
}

message PerDelPic{
	//cmd id:   MSG_P2A_PERSONNAL_DEL_PIC
	string pic_url = 1;	
}

message PerCreateUser{
	//cmd id:   MSG_P2A_PERSONNAL_CREATE_UID
	string user = 1;	
	string pwd = 2;
	string email = 3;
	string qrcode_body = 4;
	string qrcode_url = 5;
}

message PerChangePwd{
	//cmd id:   MSG_P2A_PERSONNAL_CHANGE_PWD
	string user = 1;	
	string pwd = 2;
	string email = 3;
	string qrcode_body = 4;
	string qrcode_url = 5;
}

message PerNewMessage{
	//cmd id:   MSG_P2A_PERSONNAL_NEW_TEXT_MESSAGE
	string data = 1;	//实际无参数，php传null
}

message PerSendCheckCode{
	//cmd id:   MSG_P2A_PERSONNAL_SEND_CKECK_CODE_TO_EMAIL
	string check_code = 1;
	string email = 2;
	string language = 3;
}

message PerCleanDevCode{
	//cmd id:   MSG_P2A_NOTIFY_DEV_CLEAN_DEV_CODE
	string macs = 1;
}

message NotityDevExpire{
	//cmd id:   MSG_P2A_NOTIFY_DEV_NOT_EXPIRE
	string uids = 1;
	string macs = 2;
	string node = 3;
	int32  type = 4;
}

message CommunityUpdateDevNotify{
	//cmd id:   MSG_P2A_COMMUNITY_UPDATE_NODE_DEV
	string node = 1;
	string mac = 2;
	int32 mng_account_id= 3;
	int32 unit_id = 4;
	int32 update_dev_type = 5;
	int32 update_type = 6;
}

message CommunityUpdateUserOrUnit{
	//cmd id:   MSG_P2A_COMMUNITY_UPDATE_USER
	string node = 1;
	int32 mng_account_id = 2;
	int32 unit_id = 3;
	int32 type = 4;
}
/*
message CommunityAlarmDeal{
	//cmd id:   MSG_P2A_COMMUNITY_ALARM_DEAL
	string area_node = 1;
	string user = 2;
	string alarm_id = 3;
	string result = 4;
}

message CommunityPerAddDev{
	//cmd id:   MSG_P2A_COMMUNITY_ADD_DEV
	string mac = 1;
	string mac_old = 2;
}

message CommunityPerModDev{
	//cmd id:   MSG_P2A_COMMUNITY_MODIFY_DEV
	int32 mac_id = 1;
	int32 is_per = 2;
}

message CommunityPerDelDev{
	//cmd id:   MSG_P2A_COMMUNITY_DEL_DEV
	string mac = 1;
}

message CommunityPerDelUid{
	//cmd id:   MSG_P2A_COMMUNITY_DEL_UID
	string uid = 1;
}

message CommunityPerDelPic{
	//cmd id:   MSG_P2A_COMMUNITY_DEL_PIC
	string pic_url = 1;
}

message CommunityPerCreateUser{
	//cmd id:   MSG_P2A_COMMUNITY_CREATE_UID
	string user = 1;
	string pwd = 2;
	string email = 3;
	string qrcode_body = 4;
	string qrcode_url = 5;
}

message CommunityPerChangePwd{
	//cmd id:   MSG_P2A_COMMUNITY_CHANGE_PWD
	string user = 1;
	string pwd = 2;
	string email = 3;
	string qrcode_body = 4;
}

message CommunityPerNewMessage{
	//cmd id:   MSG_P2A_COMMUNITY_NEW_TEXT_MESSAGE
	string data = 1;	//实际无参数，php传null
}
*/
message CommunitModifyMasterUser{
	//cmd id:   MSG_P2A_COMMUNITY_MODIFY_MASTER_USER
	string data = 1;	//实际无参数，php传null
}

message NotityDelPerPublicDevVirtuaAccount{
	//cmd id:   MSG_P2A_NOTIFY_DEL_PER_PUBLIC_DEV_VIRTUAL_ACCOUNT
	string account = 1;
}

message NotityAddVideoSched{
	//cmd id:   MSG_P2A_NOTIFY_ADD_VIDEO_SCHED
	uint32 id = 1;
	uint32 sched_type = 2;
	uint32 date_flag = 3;
	string mac = 4;
	string begin_time = 5;
	string end_time = 6;
}

message NotityDelVideoSched{
	//cmd id:   MSG_P2A_NOTIFY_DEL_VIDEO_SCHED
	uint32 id = 1;
	uint32 sched_type = 2;
	string mac = 3;
}

message NotityDelVideo{
	//cmd id:   MSG_P2A_NOTIFY_DEL_VIDEO_STORAGE
	uint32 video_id = 1;
}

message CommunityUpdatePubKeyNotify{
	//cmd id:   MSG_P2A_NOTIFY_COMMUNITY_PUBLIC_KEY_CHANGE
	string mac = 1;
}

message AccountActiveNotify{
	//cmd id:   MSG_P2A_NOTIFY_ACOUNT_ACTIVE
	int32 active = 1;
	string user_name = 2;
	string email = 3;
	int32 subscription = 4;
}

message ShareTempKeyNotify{
	//cmd id:   MSG_P2A_NOTIFY_SHARE_TEMPKEY
	string tmp_key = 1;
	string email = 2;
	string msg = 3;
	string count_every = 4;
	string start_time = 5;
	string stop_time = 6;
	string qrcode_body = 7;
	string language = 8;
	int32  mng_id = 9;
}

message CreatePropertyWorkNotify{
	//cmd id:   MSG_P2A_NOTIFY_CREATE_PROPERTY_WORK
	string email = 1;
	string user_name = 2;
	string password = 3;
}

message UpdateCommnuityAllPubDevNotify{
	//cmd id:   MSG_P2A_NOTIFY_UPDATE_ALL_PUB_DEV
	int32 mng_id = 1;
}

message DevRebootNotify{
	//cmd id:   MSG_P2A_REBOOT_TO_DEVICE
	string mac = 1;
}

message CommunityAptPinChangeNotify{
	//cmd id:   MSG_P2A_NOTIFY_COMMUNITY_APT_PIN_CHANGE
	int32 mng_id = 1;
}

message DevConfigUpdateNotify{
	//cmd id:   MSG_P2A_NOTIFY_DEV_CONFIG_UPDATE
	string mac = 1;
	string config = 2;
}

message ResetPasswd{
	//cmd id:   MSG_P2A_RESET_PASSWD
	string web_ip = 1;
	string user = 2;
	string email = 3;
	string token = 4;
	string role_type = 5;
}

message OpenDoorNotify{
	//cmd id:   MSG_P2A_NOTIFY_REMOTE_OPENDOOR
	string mac = 1;
	string uid = 2;
	int32 relay = 3;
	int32 repost = 4;
	string msg_traceid = 5;
}

message RenewServerNotify{
	//cmd id:   MSG_P2A_NOTIFY_ENDUSER_RENEWSERVER
	string uid = 1;
	int32 type = 2;
}

message AlexaLoginNotify{
	//cmd id:   MSG_P2A_NOTIFY_ALEXA_LOGIN
	string node = 1;
}

message AlexaSetArmingNotify{
	//cmd id:   MSG_P2A_NOTIFY_ALEXA_SET_ARMING
    string mac = 1;
    int32  mode = 2;
}

message UpdateCommMonthlyFee{
	//cmd id:   MSG_P2A_NOTIFY_UPDATE_COMM_MONTHLY_FEE
    string account = 1;
}

message CreateRemoteDevContorl{
	//cmd id:   MSG_P2A_NOTIFY_CREATE_REMOTE_DEV_CONTORL
    string user = 1;
	string password = 2;
	int32 port = 3;
	string mac = 4;
	string ssh_proxy_domain = 5;
}

message FaceServerPicDownloadNotify{
	//cmd id:   MSG_P2A_NOTIFY_FACESERVER_PIC_DOWNLOAD
	string mac = 1;
	string url = 2;
	string picmd5 = 3;
	string name = 4;
	string doornum = 5;
	string week = 6;
	string time_start = 7;
	string time_end = 8;
	string id = 9;
}

message FaceServerPicModifyNotify{
	//cmd id:  MSG_P2A_NOTIFY_FACESERVER_PIC_MODIFY
	string mac = 1;
	string url = 2;
	string picmd5 = 3;
	string name = 4;
	string doornum = 5;
	string week = 6;
	string time_start = 7;
	string time_end = 8;
	string id = 9;
}

message FaceServerPicDeleteNotify{
	//cmd id:   MSG_P2A_NOTIFY_FACESERVER_PIC_DELETE
	string name = 1;
	string id = 2;
	string mac = 3;
}

message FaceServerPicDownloadBatchNotify{
	repeated FaceServerPicDownloadNotify pic_download_list = 1;
}

message FaceServerPicModifyBatchNotify{
	repeated FaceServerPicModifyNotify pic_modify_list = 1;
}

message FaceServerPicDeleteBatchNotify{
	repeated FaceServerPicDeleteNotify pic_delete_list = 1;
}

message WebPersonalModifyNotify
{
    repeated string mac_list = 1;
    uint32 change_type = 2;
    string node = 3;
    uint32 installer_id = 4;
}

message WebCommunityModifyNotify
{
    repeated string mac_list = 1;
    uint32 change_type = 2;
    string node = 3;
    uint32 community_id = 4;
    uint32 unit_id = 5;
    uint32 already_check = 6;
    uint64 trace_id = 7;
}

message SendSmsCode{
	//cmd id:   MSG_P2A_SEND_SMS_CODE_MESSAGE
    uint32 type = 1;
	string code = 2;
	string area_code = 3;
	string phone = 4;
	uint32 user_type = 5; // 0=end user, 1=admin
}

message PmExportLog{
	//cmd id:   MSG_P2A_PM_EXPORT_LOG
	string trace_id = 1;
	uint32 communit_id = 2;
	string datas = 3;
}

message FeedbackNotify{
	//cmd id:   MSG_P2A_APP_FREEBACK
	string datas = 1;
}

message AccessGroupModifyNotify{
	//cmd id:   MSG_P2A_NOTIFY_ACCESS_GROUP_MODIFY
	uint32 access_group_id = 1;
	repeated string mac_list = 2;
	uint32 community_id = 3;
	repeated string del_userlist = 4;
}

message AccessGroupPersonalModifyNotify{
	//cmd id:   MSG_P2A_NOTIFY_ACCESS_GROUP_PER_MODIFY
	uint32 access_group_id = 1;
	uint32 community_id = 2;
}

message CommunityPersonalModifyNotify{
	//cmd id:   MSG_P2A_NOTIFY_COMMUNITY_PERSONAL_MODIFY
	repeated uint32 staff_id_list = 1;
	repeated uint32 delivery_id_list = 2;
	uint32 community_id = 3;
	repeated uint32 access_group_id_list = 4;
}

message CommunityAccountModifyNotify{
	//cmd id:   MSG_P2A_NOTIFY_COMMUNITY_ACCOUNT_MODIFY
	repeated string account_list = 1;
	repeated uint32 access_group_id_list = 2;
	uint32 community_id = 3;
	repeated string node_list = 4;
}

message CommunityImportAccountDataNotify{
	//cmd id:   MSG_P2A_NOTIFY_COMMUNITY_IMPORT_ACCOUNT_DATAS
	uint32 community_id = 1;
	repeated string mac_list = 2;
}

message DataAnalysisNotify{
	//cmd id:   MSG_P2A_NOTIFY_DATA_ANALYSIS_NOTIFY
	string datas = 1;
	uint32 project_id = 2;
}

message RegularAutopNotify{
    //cmd id : MSG_P2A_REGULAR_AUTOP
    repeated string mac_list = 1;
	uint32 project_id = 2;
}

message OnceAutopNotify{
    //cmd id : MSG_P2A_ONCE_AUTOP
    repeated string mac_list = 1;
    string config = 2;	
	uint32 project_id = 3;
}

message DelAppAccountNotify{
    //cmd id : MSG_P2A_DELETE_APP_ACCOUNT
    string account = 1;
}

message OpenSecurityRelayNotify{
	//cmd id:   MSG_P2A_NOTIFY_REMOTE_OPEN_SECURITY_RELAY
	string mac = 1;
	string uid = 2;
	int32 security_relay = 3;
	string msg_traceid = 4;
	int32 repost = 5;
	string repost_mac = 6;
}

message PmEmergencyDoorControlNotify{
	//cmd id:	MSG_P2A_NOTIFY_PM_EMERGENCY_DOOR_CONTROL
	string uuid = 1;
	int32 type = 2;
}

message PmAccountActiveEmailNotify{
	//cmd id:	MSG_P2A_NOTIFY_PM_ACOUNT_ACTIVE
	int32 active = 1;
	string account = 2;
	int32 subscription = 3;
}

message ThirdPartyLockNotify{
	//cmd id:	MSG_P2A_NOTIFY_OPERATE_THIRD_PARTYA_LOCK
    int32 message_type = 1;
    int32 capture_type = 2;
    int32 lock_type = 3;
    string uuid = 4;
    string personal_account_uuid = 5;
    string initiator = 6;
    string lock_name = 7;
}

message WebCommAccessModifyNotify
{
    repeated string mac_list = 1;
    uint32 change_type = 2;
    string node = 3;
    uint32 community_id = 4;
    uint32 ag_id = 5;
    uint32 already_check = 6;
    uint64 trace_id = 7;
}

message PerAddNewSite{
	string userinfo_uuid = 1;
	string project_name = 2;
	string apt_num = 3;
	string email = 4;
	string send_type = 5;
}

message PmLinkNewSites{
	string account_uuid = 1;
	string comm_name_list = 2;
	string office_name_list = 3;
	string email = 4;
}

message SendSMSVerficationCode{
	string mobile_number = 1;
	string phone_code = 2;
	string language = 3;
	string code = 4;
	string type = 5;
	string account_uuid = 6;
}

message SendEmailVerficationCode{
	string email = 1;
	string name = 2;
	string language = 3;
	string code = 4;
	string type = 5;
	string account_uuid = 6;
}

message WebPcapCaptureNotify
{
    uint32 type = 1; //0关闭  1开启
    string uuid = 2;//本次开启抓包监控的uuid
    string mac = 3;
}

message WebSipPcapCaptureNotify
{
    uint32 type = 1; //0关闭  1开启
    string uuid = 2;//本次开启抓包监控的uuid
    string caller_sip = 3;//只能是根据主叫来抓，因为如果是群呼，被叫是群组号这样过滤就会出现问题
}

message SendEmailNotifyMsg
{
	// 发送邮件统一模板
    string key = 1; // 邮箱
    string payload = 2; // json格式
}

message KitAccountLogOff
{
	//Kit方案下的账号注销
    string node = 1;
}

message SendMessageNotifyMsg
{
	//发送Message统一模板
	string key = 1; // 用户Account
	string payload = 2; // 统一json格式
}

message OpenApiSocketHealthCheck
{
    string message = 1;
}

message GeneralMessageProtocol
{
    // 通用消息协议
    string msg_type  = 1;   // 消息类型, 区分大小写
    string trace_id  = 2;   // 消息ID,  区分大小写
    uint64 timestamp = 3;   // 时间戳,  单位：微秒
    string data      = 4;   // json格式
}

message OpenDoorNotifyMsg{
	// 开门通知消息
    string uid = 1;             // 用户uid
	string mac = 2;             // 设备mac
	int32 relay = 3;            // 
	int32 relay_type = 4;       // 0=普通relay, 1=安全relay
	int32 project_type = 5;     // 0=住宅,1=办公,2=单住户,3=新办公
	string repost_mac  = 6;     // 转发的mac
    string msg_traceid = 7;     // 消息ID
}

message RequestDeviceCaptureNotifyMsg{
	//cmd id:   MSG_P2A_REQUEST_DEVICE_CAPTURE
	string mac = 1;
	string site = 2;
	string uuid = 3;
	string camera = 4;
	int32 project_type = 5; // 0社区 1个人
}

message SmartLockUpdateNotify
{
	//cmd id: MSG_P2A_NOTIFY_SMARTLOCK_UPDATE
	string lock_uuid = 1;
	int32 lock_type = 2;
}