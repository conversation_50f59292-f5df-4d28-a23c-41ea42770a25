syntax = "proto3";
package AK.AdaptOffice; 

import "AK.Server.proto";

message WebOfficeModifyNotify
{
	//MSG_P2A_NOTIFY_OFFICE_INFO_UPDATE
    repeated string mac_list = 1;
    uint32 change_type = 2;
    string node = 3;
    uint32 office_id = 4;
	uint32 department_id = 5;
}

message PMOfficeAccountRenew
{
    //cmd id: MSG_P2A_NOTIFY_OFFICE_ACCOUNT_RENEW
    string community = 1;
    string email = 2;
    string pm_name = 3;
    int32 account_num = 4;
	string list = 5;
}

message OfficeUpdateFileConfig
{
	repeated string dev_uuid_list  = 1;
    string mac = 2; //在设备被删除时候需要传递
}

message OfficeUpdateBaseMessage {
    uint32 change_type = 1;
    string office_uuid = 2;
    string trace_id = 3;
    string msg_uuid = 4;    
    oneof message_type {
       AK.AdaptOffice.OfficeUpdateFileConfig file_update = 5;
       AK.Server.P2PMainRequestWriteUserinfo user_info = 6;
    }
}
