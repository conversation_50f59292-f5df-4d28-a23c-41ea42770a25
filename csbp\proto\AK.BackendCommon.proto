syntax = "proto3";

package AK.BackendCommon;

import "AK.Server.proto";

message BackendP2PBaseMessage {
    uint32 type = 1;            //透传给app or 设备 1dev-Mac 2app-UID 3-dev-UUID 4app-UUID
    string uid = 2;             //设备是mac/uuid 用户是uid（主站点的uuid/uid）
    uint32 project_type = 3;    //消息原本的业务类型，用于转发到指定的后端业务服务
    uint32 conn_type = 4;       //主站点的业务连接类型，用于转发到指定的用户
    uint32 msgid = 5;    
    oneof message_type {
       AK.Server.P2PSendVoiceMsg P2PSendVoiceMsg2 = 6;
       AK.Server.P2PSendDeliveryMsg P2PSendDeliveryMsg2 = 7;
       AK.Server.GroupMainReportRelayStatus GroupIndoorRelayStatusMsg2 = 8;
       AK.Server.P2PSendEmergencyNotifyMsg P2PSendEmergencyNotifyMsg2 = 9;
       AK.Server.P2PPmEmergencyDoorControlMsg P2PPmEmergencyDoorControlMsg2 = 10;
       AK.Server.P2PSendMotionNotifyMsg P2PSendMotionNotifyMsg2 = 11;
       AK.Server.P2PMainSendTmpkeyUsed P2PMainSendTmpkeyUsed2 = 12;
       AK.Server.P2PSendAlarmNotifyMsg P2PSendAlarmNotifyMsg2 = 13;
       AK.Server.P2PCommonTxtMsgNotifyMsg P2PCommonTxtMsgNotifyMsg2 = 14;
       AK.Server.P2PMainResponseOpenDoorMsg P2PMainResponseOpenDoorMsg2 = 15;
       AK.Server.P2POpenDoorNotifyMsg       P2POpenDoorNotifyMsg2       = 16;
       AK.Server.P2PAlarmDealNotifyMsg      P2PAlarmDealNotifyMsg2 = 17;
       AK.Server.P2PLockDownDoorControlMsg  P2PLockDownDoorControlMsg2 = 18;
       AK.Server.P2PRequestDeviceCaptureNotifyMsg P2PRequestDeviceCaptureNotifyMsg2 = 19;
       AK.Server.P2PSendSL20LockEventNotify P2PSendSL20LockEventNotify2 = 20;
    }
}