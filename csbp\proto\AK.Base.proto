syntax = "proto3";

package AK.Base;

enum LogicClientType{
    LOGIC_CLIENT_TYPE_INVALID  = 0x00;
	LOGIC_CLIENT_TYPE_MAIN     = 0x01;
    LOGIC_CLIENT_TYPE_VRTSP    = 0x02;
    LOGIC_CLIENT_TYPE_ADAPT    = 0x03;
    LOGIC_CLIENT_TYPE_PBX      = 0x04;
    LOGIC_CLIENT_TYPE_CONFIG   = 0x05;
    LOGIC_CLIENT_TYPE_RESID   = 0x06;
    LOGIC_CLIENT_TYPE_OFFICE   = 0x07;
    LOGIC_CLIENT_TYPE_SIPHUB   = 0x08;//包括siphub和freeswtich sipcapture
    LOGIC_CLIENT_TYPE_CONFIG_OFFICE   = 0x09;
    LOGIC_CLIENT_TYPE_SMARTLOCK = 0x0a;
}
