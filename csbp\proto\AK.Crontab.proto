syntax = "proto3";
package AK.Crontab; 

message AppExpire{
	//cmd id:   MSG_P2A_APP_EXPIRE
	string uid = 1;
	string user_name = 2;
	string email = 3;
	string community = 4;
}

message AppWillExpire{
	//cmd id:   MSG_P2A_APP_WILLBE_EXPIRE
	string uid = 1;
	string user_name = 2;
	string email = 3;
	string community = 4;
}

message PMAppWillBeExpire{
	//cmd id:   MSG_P2A_NOTIFY_PM_ACCOUNT_WILL_EXPIRE
	string community = 1;
	string email = 2;
	string name = 3;
	int32  account_num = 4;
	string list = 5;
	int32  before = 6;
}

message PhoneExpire{
	//cmd id:   MSG_P2A_PHONE_EXPIRE
	string uid = 1;
	string name = 2;
	string email = 3;
	int32  mode = 4;
}

message PhoneWillExpire{
	//cmd id:   MSG_P2A_PHONE_WILL_EXPIRE
	string uid = 1;
	string name = 2;
	string email = 3;
	int32  before = 4;
}

message InstallerAppWillExpire{
	//cmd id:   MSG_P2A_NOTIFY_INSTALLER_APP_WILL_EXPIRE
	string name = 1;
	string email = 2;
	string location = 3;
	int32  count = 4;
	string list = 5;
	int32  before = 6;
}

message InstallerPhoneWillExpire{
	//cmd id:   MSG_P2A_NOTIFY_INSTALLER_PHONE_WILL_EXPIRE
	string name = 1;
	string email = 2;
	int32  count = 3;
	string list = 4;
	int32  before = 5;
}

message InstallerAppExpire{
	//cmd id:   MSG_P2A_NOTIFY_INSTALLER_APP_WILL_EXPIRE
	string name = 1;
	string email = 2;
	string location = 3;
	int32  count = 4;
	string list = 5;
}

message InstallerPhoneExpire{
	//cmd id:   MSG_P2A_NOTIFY_INSTALLER_PHONE_WILL_EXPIRE
	string name = 1;
	string email = 2;
	int32  count = 3;
	string list = 4;
}

message PmFeatureWillExpire{
	//cmd id:   MSG_P2A_NOTIFY_PM_FEATURE_WILL_EXPIRE
	string name = 1;
	string email = 2;
	string location = 3;
	int32  before = 4;
}

message InstallerFeatureWillExpire{
	//cmd id:   MSG_P2A_NOTIFY_INSTALLER_FEATURE_WILL_EXPIRE
	string name = 1;
	string email = 2;
	string location = 3;
	int32  before = 4;
}

message WebPersonalModifyNotify
{
    repeated string mac_list = 1;
    uint32 change_type = 2;
    string node = 3;
    uint32 installer_id = 4;
}

message WebCommunityModifyNotify
{
    repeated string mac_list = 1;
    uint32 change_type = 2;
    string node = 3;
    uint32 community_id = 4;
	uint32 unit_id = 5;
}

message PMAppAccountWillBeExpire{
	//cmd id:   MSG_P2A_NOTIFY_PM_APP_ACCOUNT_WILL_EXPIRE
	string community = 1;
	string email = 2;
	string name = 3;
	int32  account_num = 4;
	string list = 5;
	int32  before = 6;
}

message PMAppAccountExpire{
	//cmd id:   MSG_P2A_NOTIFY_PM_APP_ACCOUNT_EXPIRE
	string community = 1;
	string email = 2;
	string name = 3;
	int32  account_num = 4;
	string list = 5;
}

message SendEmailNotifyMsg
{
	// 发送邮件统一模板
    string key = 1; // 邮箱
    string payload = 2; // 统一json格式
}

message SendMessageNotifyMsg
{
	//发送Message统一模板
	string key = 1; // 用户Account
	string payload = 2; // 统一json格式
}

message CronUserAccessGroupNotifyMsg
{
	// 定时发送用户权限组更新通知
	string account = 1; //用户account
	uint32 type = 2; // 更新类型 0=新增 1=删除
	uint32 community_id = 3; //社区id
	uint32 ag_id = 4; //权限组id
}
