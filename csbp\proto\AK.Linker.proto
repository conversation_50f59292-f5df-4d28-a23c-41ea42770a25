syntax = "proto3";

package AK.Linker; 

// csroute->cslinker
message P2PRouteLinker
{
    int32 message_type = 1; //消息类型 同之前的：LINKER_MSG_TYPE_KIT
    int32 project_type = 2; //项目类型 RESIDENCE = 0,OFFICE = 1,PERSONAL =2
    string key = 3;//唯一标识 用于后期分片等
    string msg_json = 4;// json格式的消息
}

// cslinker->csroute->csmain->dev
message LinkerWeatherNotify
{
    string mac = 1;
    string country = 2;
    string states = 3;
    string city = 4;
    string aqi = 5;
    string humidity = 6;
    string temperature = 7;
    string weather = 8;
    string wind_speed = 9;		
}

// cslinker->csroute->csresid
message LinkerPacportCheckNotify
{
	uint32 result = 1; //0=失败 1=成功
	string mac = 2;
	string traceid = 3;
	string room_num = 4;
}

// cslinker->csroute->csresid->csmain->dev
message LinkerDevCommonAck
{
    string mac = 1;
    uint32 msg_id = 2;
    string msg_seq = 3;
}