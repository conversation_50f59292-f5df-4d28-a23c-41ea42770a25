syntax = "proto3";

package AK.PBX;

// The adding service definition.
service PbxRpcSrv {

  rpc QueryUidStatusHandle (QueryUidStatusRequest) returns (QueryUidStatusReply) {}
  rpc WakeupAppHandle (WakeupAppRequest) returns (WakeupAppReply) {}
  rpc QueryLandlineStatusHandle (QueryLandlineStatusRequest) returns (QueryLandlineStatusReply) {}
  rpc WriteCallHistoryHandle (WriteCallHistoryRequest) returns (WriteCallHistoryReply) {}
  rpc QueryLandlineNumberHandle (QueryLandlineNumberRequest) returns (QueryLandlineNumberReply) {}
  rpc QueryMainSiteSipHandle (QueryMainSiteSipRequest) returns (QueryMainSiteSipReply) {}
  rpc HangupAppHandle (HangupAppRequest) returns (HangupAppReply) {}
  rpc QuerySipInfoHandle (QuerySipInfoRequest) returns (QuerySipInfoReply) {}
}

message QueryUidStatusRequest {
    string uid = 1;
    uint64 msg_traceid = 2;
    string caller = 3;
    string original_callee = 4;
    uint32 app_type = 5;
}

message QueryUidStatusReply {
     uint32 ret = 1; //1:online 0:offline
}

message WakeupAppRequest {
    string callee_sip = 1;
    string caller_sip = 2;
    string nick_name_location = 3;
    uint64 msg_traceid = 4;
    uint32 app_type = 5;
    string x_caller = 6;  // 转流时候门口机的sip
    string timestamp = 7; // 呼叫开始的时间戳
    string x_name = 8;    // 转流门口机的name
}

message WakeupAppReply {
	 uint32 ret = 1; //0:successful 1:faile
}

message QueryLandlineStatusRequest {
    string caller_sip = 1;
    string phone_number = 2;  
    uint64 msg_traceid = 3;
}

message QueryLandlineStatusReply {
	 uint32 ret = 1; //1:active 0:expire
}

//TODO:可以优化为流式的提高效率
message WriteCallHistoryRequest {
    string caller = 1;
    string callee = 2;
    string called = 3;
    string start_time = 4;
    string answer_time = 5;
    int32 bill_second = 6; 
    uint64 msg_traceid = 7;
    string freeswitch_node = 8;
    string caller_ops_node = 9;
    string callee_ops_node = 10;
    string caller_name = 11;
    string group_call_list = 12;
    string call_trace_id = 13;
}

message WriteCallHistoryReply {
	 uint32 ret = 1; //0:successful others:faile
}

message QueryLandlineNumberRequest {
    string sip = 1;
	int32  type = 2;
    uint64 msg_traceid = 3;
}

message QueryLandlineNumberReply {
	 string ret = 1; 
	 string phone_code = 2;
}

message QueryMainSiteSipRequest {
    string sip = 1;
    uint64 msg_traceid = 2;
}

message QueryMainSiteSipReply {
	 string main_sip = 1; 
}

message HangupAppRequest {
    string callee_sip = 1;
    string caller_sip = 2;
    string nick_name_location = 3;
    uint64 msg_traceid = 4;
    uint32 app_type = 5;
    string x_caller = 6; //转流时候门口机的sip
    string x_name = 7;   // 转流门口机的name
}

message HangupAppReply {
	 uint32 ret = 1; //0:successful others:faile
}

message QuerySipInfoRequest {
    string sip = 1;
    uint64 msg_traceid = 2;
}

message QuerySipInfoReply {
    uint32 ret = 1; //0:successful 1:faile
    string name = 2;
}
