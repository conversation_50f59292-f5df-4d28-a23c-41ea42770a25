syntax = "proto3";
package domainServer_rbac;

message Context {
  string trace_id = 1;      // 雪花算法生成
  string originName = 2;    // 固定串: "app_backend"
  string originRequest = 3; // 请求url
  string from = 4;          // 组件
  int64 time = 5;           // 时间戳, 单位：秒
  string type = 6;          // normal
  int64 level = 7;          // 0
  bool isSync = 8;          // false
}

message ErrorDetail {
  string code = 1;
  string message = 2;
  int64 type = 3;
}

message RoleSet {
  string super = 1;
  string dis = 2;
  string ins = 3;
  string project = 4;
  string company = 5;
  string mainUser = 6;
  string subUser = 7;
}

message UserData {
  string user = 1;
  string account = 2;
  int64 role = 3;
  string project = 4;
  string project_account = 5;
  int64 project_role = 6;
  RoleSet roleSet = 7;
}

message CheckAuthData {
  bool pass = 1;
  UserData userData = 2;
}

message CheckAuthRequest {
  Context context = 1;
  string token = 2;
  string url = 3;
  repeated string dataUUIDs = 4;
  string project = 5;
}

message CheckAuthResponse {
  Context context = 1;
  CheckAuthData data = 2;
}

message MenuData {
  string name = 1;
  string menu = 2;
}

message GetMenusData {
  UserData userData = 1;
  repeated MenuData menus = 2;
}

message GetMenusRequest {
  Context context = 1;
  string token = 2;
  string project = 3;
}

message GetMenusResponse {
  Context context = 1;
  GetMenusData data = 2;
}

message UserDataGroup {
  string dataGroup = 1;
  string RBACDataGroupUUID = 2;     // uuid
}

message GetEndUserDataGroupRequest {
  Context context = 1;
  string user = 2;                  // personal_account uuid
}

message GetEndUserDataGroupResponse {
  Context context = 1;
  UserDataGroup data = 2;
}

message GetAdminDataGroupRequest {
  Context context = 1;
  string user = 2;
}

message GetAdminDataGroupResponse {
  Context context = 1;
  UserDataGroup data = 2;
}

message GetCompanyDataGroupRequest {
  Context context = 1;
  string company = 2;
}

message GetCompanyDataGroupResponse {
  Context context = 1;
  UserDataGroup data = 2;
}

message CreateDataGroupData {
  string dataGroup = 1;
}

message CreateEndUserDataGroupRequest {
  Context context = 1;
  string user = 2;
}

message CreateEndUserDataGroupResponse {
  Context context = 1;
  CreateDataGroupData data = 2;
}

message CreateAdminDataGroupRequest {
  Context context = 1;
  string user = 2;
}

message CreateAdminDataGroupResponse {
  Context context = 1;
  CreateDataGroupData data = 2;
}

service RBAC {
  rpc CheckAuth(CheckAuthRequest) returns(CheckAuthResponse);
  rpc GetMenus(GetMenusRequest) returns(GetMenusResponse);
  rpc GetEndUserDataGroup(GetEndUserDataGroupRequest) returns(GetEndUserDataGroupResponse);
  rpc GetAdminDataGroup(GetAdminDataGroupRequest) returns(GetAdminDataGroupResponse);
  rpc GetCompanyDataGroup(GetCompanyDataGroupRequest) returns(GetCompanyDataGroupResponse);
  rpc CreateEndUserDataGroup(CreateEndUserDataGroupRequest) returns(CreateEndUserDataGroupResponse);
  rpc CreateAdminDataGroup(CreateAdminDataGroupRequest) returns(CreateAdminDataGroupResponse);
}
