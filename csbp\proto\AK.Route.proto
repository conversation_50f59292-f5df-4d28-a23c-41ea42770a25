syntax = "proto3";

package AK.Route; 

//以下为csroute->csmain的,基本是由于逻辑在csroute处理，导致消息发往csmain的时候与先来的消息不一致了
message P2PRouteOneDevLogOutMsg
{
    //cmd id: AKCS_R2M_DEL_DEV_MSG_REQ
    string mac = 1;
}

message P2PRouteOneUidLogOutMsg
{
    //cmd id: AKCS_R2M_DEL_UID_MSG_REQ
    string uid = 1;
}

message P2PAdaptOneDevCleanDeviceCodeMsg
{
    //cmd id: AKCS_R2M_CLEAN_DEV_CODE_MSG_REQ
    string mac = 1;
}

message P2PUpgradeDevMsg
{
    //cmd id: AKCS_R2M_UPGRADE_DEV_REQ
    repeated string mac_list = 1;
    string firmware_url = 2;
    string firmware_ver = 3;
	int32 is_need_reset = 4;
}

//vrtspd通过mq发送监控请求给csroute,csroute通过tcp连接转发给csmain,信令内容相同,信令id不同
message StartRtspReq{
	//cmd id:   AKCS_V2R_START_RTSP_MSG_REQ / AKCS_R2M_START_RTSP_REQ 共用
    //RTSP_DATA
    string mac = 1; // app请求监控设备的mac (有三方摄像头时为摄像头的uuid)
    string ip = 2;
    int32 port = 3;
    string vrtspd_logic_id = 4;
    string ssrc = 5;//十六进制的
    string ipv6 = 6;//流媒体的ipv6
    int32 is_third = 7;//三方摄像头监控转发标识
    string dev_mac = 8;//三方摄像头绑定的设备mac
    string transfer_door_uuid = 9;   // 西班牙转流为门口机的sip, hager转流为为门口机的mac
    string transfer_indoor_mac = 10; // 转流室内机的mac
    int32 video_pt = 11;
    string video_type = 12;
    string video_fmtp = 13;
    string srtp_key = 14; //rtp的加密类型和key
    int32 rtp_confuse = 15;
    string flow_uuid = 16; //流的唯一标识
    string camera = 17; //监控的摄像头，Main=主摄 Auxiliary=辅摄
    int32 stream_id = 18; //监控流的id
}

message StopRtspReq{
	//cmd id:   AKCS_V2R_STOP_RTSP_MSG_REQ / AKCS_R2M_STOP_RTSP_REQ
    //RTSP_DATA
    string mac = 1; // app请求监控设备的mac  (有三方摄像头时为摄像头的uuid)
    string ip = 2;
    int32 port = 3;
    string vrtspd_logic_id = 4;
    int32 is_third = 5;//三方摄像头监控转发标识
    string dev_mac = 6;//设备mac (有三方摄像头时为绑定的设备mac)
    string transfer_door_uuid = 7;  //西班牙转流为门口机的sip, hager转流为为门口机的mac
    string transfer_indoor_mac = 8; //转流室内机的mac
	string srtp_key = 9; //rtp的加密类型和key
    string flow_uuid = 10; //流的唯一标识
    string camera = 11; //监控的摄像头，Main=主摄 Auxiliary=辅摄
    int32 stream_id = 12; //监控流的id
}

message RtspKeepAliveReq{
	//cmd id:   AKCS_V2R_KEEPALIVE_RTSP_REQ / AKCS_R2M_KEEPALIVE_RTSP_REQ
    string mac = 1;  // app请求监控设备的mac (有三方摄像头时为摄像头的uuid)
    string vrtspd_logic_id = 2;
    int32 is_third = 3;//三方摄像头监控转发标识
    string dev_mac = 4;//三方摄像头绑定的设备mac
    string transfer_door_uuid = 5;  //西班牙转流为门口机的sip, hager转流为为门口机的mac
    string transfer_indoor_mac = 6; //转流室内机的mac
	string srtp_key = 7; //rtp的加密类型和key
    string flow_uuid = 8; //流的唯一标识
    string camera = 9; //监控的摄像头，Main=主摄 Auxiliary=辅摄
    int32 stream_id = 10; //监控码流的id
}

//route转发csmain对设备监控视频进行截图,流程:csmain->csroute->csvrtspd->(unix socket)csvrecord
message RtspCaptureReq{
	//cmd id:   AKCS_M2R_P2P_RTSP_CAPTURE_REQ /AKCS_R2V_P2P_RTSP_CAPTURE_REQ
    string mac = 1;
    string pic_name = 2;
    string node = 3;
    string user_name = 4;
    string site = 5;
    int32 record_video = 6;
    string flow_uuid = 7; //截图的视频流，规则MAC+"ch"+通道+"stm"+码流
}

message P2PRouteStopVideoRecordReq
{
    string site = 1;
    string mac = 2;
}

//route转发pbx关于设备
message P2PRouteQueryUidStatusReq
{
    //cmd id: AKCS_R2M_QUERY_UID_STATUS_REQ
    string uid = 1;
    int64 query_seq = 2;//route对pbx查询app状态的编码序列号
}
message P2PRouteQueryUidStatusResp
{
    //cmd id: AKCS_R2M_QUERY_UID_STATUS_RESP
    string uid = 1;
    int32 status = 2;
    int64 query_seq = 3;
}
message P2PRoutWakeUpAppReq
{
    ////cmd id: AKCS_P2R_WAKEUP_APP_REQ
    string calling_sip = 1;
    string called_uid = 2;
    string nick_name_location = 3;
}

//route->给所有的逻辑服务器ping-pong消息
message P2PRoutePingPong
{
    ////cmd id: AKCS_MSG_R2L_PING_REQ 跟 AKCS_MSG_R2L_PING_RESP
    string logic_srv_id = 1; //逻辑服务器的id
}

//SL50 直接路由所有消息 不进行任何处理。可以通过client_id做负载均衡
message P2PRoutLockMessage
{
    string client_id = 1;
    string akcs_command = 2; //我们自己的命令标识,用于反序列化。家居command下还有domain、type 没办法唯一区分
    string trace_id = 3;
    string json_message = 4;
}

//SL50 直接路由所有消息 不进行任何处理。可以通过client_id做负载均衡
message P2PRoutLockAckMessage
{
    string client_id = 1;
    string akcs_command = 2; //我们自己的命令标识,用于反序列化。家居command下还有domain、type 没办法唯一区分
    string trace_id = 3;
    string json_message = 4;
}