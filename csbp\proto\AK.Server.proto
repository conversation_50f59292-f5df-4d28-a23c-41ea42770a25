syntax = "proto3";

package AK.Server; 
import "AK.Base.proto";

//tcp交互
message LogicSrvReg{
    //cmd id:   0x00100001
    string logic_srv_uid = 1;
    AK.Base.LogicClientType srv_type = 2;
}
//以下是通过nsq交互
//社区告警消息群发请求
message GroupCommAlarmMsg{
    //cmd id:   0x00400001 AKCS_M2R_GROUP_COMM_ALARM_REQ
    //SOCKET_MSG_ALARM_SEND
    string alarm_type = 1;//告警内容
    string community = 2;
    string address = 3;//也就是node
    string time = 4;
    string msg_seq = 5;
    string from_local = 6;//设备的location
    string mac = 7;
    uint32 grade = 8;//公共设备不需要广播消息
    uint32 mng_account_id = 9;
    uint32 unit_id = 10;
    uint32 extension = 11;
    uint32 device_type = 12;
    uint32 id = 13;
    uint32 alarm_code= 14;//区分于第一个参数,用于程序判断的类型
    uint32 alarm_zone= 15;//哪个防区
    uint32 alarm_location= 16;//哪个位置
    uint32 alarm_customize= 17;//是否是设备自定义的alarm消息
    string alarm_uuid = 18;
    uint64 trace_id = 19;//用于推送解除告警消息
}

//社区用户告警处理消息群发请求
message GroupCommAlarmDealMsg{
    //cmd id:   AKCS_M2R_GROUP_COMM_ALARM_DEAL_MSG_REQ
    //SOCKET_MSG_ALARM_DEAL
    string node = 1;//联动系统
    string alarm_id = 2;
    string deal_user = 3;//也就是node
    string deal_result = 4;
    string deal_type = 5;
    string deal_time = 6;//设备的location
    string mac = 7;
    string dev_location = 8;
    string alarm_type = 9;//原始告警内容
    uint32 mng_account_id = 10;
    string trace_id = 11;//用于推送解除告警消息
}


//个人用户告警消息群发请求
message GroupPerAlarmMsg{
    //cmd id: AKCS_M2R_GROUP_PER_ALARM_MSG_REQ
    string alarm_type = 1;//告警内容
    string address = 2;//也就是node
    string time = 3;
    string msg_seq = 4;
    string from_local = 5;//设备的location
    string mac = 6;
    uint32 extension = 7;
    uint32 device_type = 8;
    uint32 id = 9;
    string community = 10;
    uint32 alarm_code= 11;//区分于第一个参数,用于程序判断的类型
    uint32 alarm_zone= 12;//哪个防区
    uint32 alarm_location= 13;//哪个位置
    uint32 alarm_customize= 14;//是否是设备自定义的alarm消息
    uint64 trace_id = 15;//用于推送解除告警消息
}

//个人用户告警处理消息群发请求
message GroupPerAlarmDealMsg{
    //cmd id: AKCS_M2R_GROUP_PER_ALARM_DEAL_MSG_REQ
    string node = 1;//联动系统
    string alarm_id = 2;
    string deal_user = 3;//也就是node
    string deal_result = 4;
    string deal_type = 5;
    string deal_time = 6;
    string mac = 7;
    string dev_location = 8;//设备的location
    string alarm_type = 9;//原始告警内容
    string community = 10;
}

//motion处理消息群发请求
message GroupPerMotionMsg{
    //cmd id: AKCS_M2R_GROUP_PER_MOTION_MSG_REQ
    string mac = 1;//联动系统
    string node = 2;
    string dev_location = 3;//也就是node
    string motion_time = 4;
    string sip_account = 5;
    uint32 id = 6;
}

//app获取布防 app查找和返回用了通过消息体，消息ID不同
message P2PMainAppHandleArmingMsg{
    //cmd id:AKCS_M2R_P2P_APP_GET_ARMING_MSG_REQ AKCS_M2R_P2P_APP_GET_ARMING_MSG_RESP
    string mac = 1;
    string uid = 2;
    string action = 3;//app的set/get
    uint32 mode = 4;
    uint32 oem = 5;//跟进oem版本判断是否转发给对应的设备。罗伯特版本
    uint32 resp_action = 6;//设备报arming状态时，指明是那种情况 1=app get  2=app set 3=self set
    string node = 7;
    string main_site = 8; //多套房主站点
    uint32 home_sync = 9; //室内机自己配置云转发,0=关 1=开 2=同步关配置给其他室内机和门口机  3=同步开配置给其他室内机和门口机
}

/////以下是rtsp的消息交互,弃用, rtsp相关改到route文件里面实现 ///////
//message P2PRtspActionMsg
//message P2PRtspKeepAliveMsg

/////以下是pbx的消息交互,pbx没有使用protobuf来传输,跳过///////


/////以下为csadapt->csroute，由原先的ipc通道消息而来
message P2PAdaptRebootDevMsg
{
    //cmd id: MSG_C2S_REBOOT_DEVICE
    string mac = 1;
}

message P2PAdaptResetDevMsg
{
    string mac = 1;
}

message GroupAdaptPerUpdateNodeMsg
{
    //cmd id: MSG_C2S_NOTIFY_UPDATE_NODE
    string node = 1;
}

message GroupAdaptCommUpdateNodeMsg
{
    //cmd id: MSG_C2S_NOTIFY_UPDATE_COMMUNITY_NODE
    string node = 1;
    string mac = 2;
    uint32 mng_account_id = 3;
    uint32 unit_id = 4;
    uint32 update_dev_type = 5;
    uint32 change_type = 6;
}
message GroupAdaptPerAlarmDealMsg{
    //cmd id: MSG_C2S_NOTIFY_PERSONAL_ALARM_DEAL
    string node = 1;//联动系统
    string alarm_id = 2;
    string deal_user = 3;//也就是node
    string deal_result = 4;
}

message GroupAdaptCommAlarmDealMsg{
    //cmd id: MSG_C2S_NOTIFY_COMMUNITY_ALARM_DEAL
    string node = 1;//联动系统
    string alarm_id = 2;
    string deal_user = 3;//也就是node
    string deal_result = 4;
}

message P2PAdaptReportStatusMsg
{
    //cmd id: MSG_C2S_PER_SEND_REPORT_STATUS
    string mac = 1;
}

message P2PAdaptDevLogOutMsg
{
    //cmd id: MSG_C2S_PER_SEND_DEL_DEV
    string macs = 1;//多个mac用<,>隔开,在csroute处理成单个再发往csmain,具体见:AKCS_R2M_DEL_DEV_MSG_REQ
}

message P2PAdaptUidLogOutMsg
{
    //cmd id: MSG_C2S_PER_SEND_DEL_UID
    string uids = 1;//多个uid用<,>隔开,处理同上
}

message GroupAdaptTextMsg
{
    //cmd id: MSG_C2S_PER_SEND_TEXT_MSG
    repeated string node_list = 1;
    int32 client_type = 2;
    string type = 3;
    string title = 4;
    string content = 5;
    string time = 6;
    string from = 7;
    string to = 8;
    uint32 id = 9;
}

message P2PAdaptCreateUidMailMsg
{
    //cmd id: MSG_C2S_PER_SEND_CREATE_UID_MAIL
    string user = 1;
    string pwd = 2;
    string email = 3;
    string qrcode_body = 4;
    string qrcode_url = 5;
    string srv_web_url = 6;
    uint32 to_master = 7;
    string user_uid = 8;
    uint32 is_fake = 9;
}

message P2PAdaptResetPwdMailMsg
{
    //cmd id: MSG_C2S_PER_SEND_RESET_PWD_MAIL
    string web_ip = 1;
    string user = 2;
    string email = 3;
    string token = 4;
    uint32 to_master = 5;
    string role_type = 6;
}
message P2PAdaptPerChangePwdMailMsg
{
    //cmd id: MSG_C2S_PER_SEND_CHANGE_PWD_MAIL
    string user = 1;
    string pwd = 2;
    string email = 3;
    string qrcode_body = 4;
    string qrcode_url = 5;
    uint32 to_master = 6;
    string user_uid = 7;
}

message P2PAdaptPerCheckCodeMailMsg
{
    //cmd id: MSG_C2S_PER_SEND_CHECK_CODE_MAIL
    string check_code = 1;
    string email = 2;
    string language = 3;
}

message GroupAdaptConfFileChangeMsg
{
    //cmd id: MSG_C2S_NOTIFY_CONFIG_FILE_CHANGE
    string mac = 1;//有值时,表示只有单台设备的配置文件变更
    string node = 2;//表示整个联动系统的配置文件变更
    int32 type = 3;
    int32 notify_type = 4;
    uint32 mng_id = 5;
    uint32 unit_id = 6;
}

//modified by chenyc,2019-07-01,v4.5版本设备不设置过期时间
message GroupAdaptDevAppExpireMsg
{
    //cmd id: MSG_C2S_DEV_APP_EXPIRE
    message GroupAdaptAppExpireInnerMsg
    {
        string uid = 1;
        string user_name = 2;
        string email = 3;
        string community = 4;
    }
    repeated GroupAdaptAppExpireInnerMsg expire_uid_list = 1;
}

message P2PAdaptDevAppWillBeExpireMsg
{
    //cmd id: MSG_C2S_DEV_APP_WILL_BE_EXPIRE
    string user_name = 1;//主账号
    string email = 2;
    string community = 3;
}

message P2PAdaptFreeTrialWillBeExpireMsg
{
    //cmd id: MSG_C2S_DEV_APP_WILL_BE_EXPIRE
    string srv_web_url = 1;
    string node_name = 2;
    string email = 3;
    int32 type = 4; //个人 or 社区
}

message GroupAdaptDevNotExpireMsg
{
    //cmd id: MSG_C2S_DEV_NOT_EXPIRE
    string uids = 1;//多个
    string macs = 2;//多个
    string node = 3;
    int32 type = 4; //个人 or 社区
}
message P2PAdaptDevCleanDeviceCodeMsg
{
    //cmd id: MSG_C2S_DEV_CLEAN_DEV_CODE
    string macs = 1;//多个用<;>隔开,在csroute处理成单个再发往csmain,具体见:AKCS_R2M_CLEAN_DEV_CODE_MSG_REQ
}
message P2PAdaptDevChangeMsg
{
    //cmd id: MSG_C2S_DEV_CHANGE
    int32 mac_id = 1;
    int32 is_per = 2;
    string mac = 3;
}
message GroupAdaptAddVsSchedMsg
{
    //cmd id: MSG_C2S_ADD_VIDEO_STORAGE_SCHED
    uint32 id = 1;
    uint32 sched_type = 2;
    uint32 date_flag = 3;
    string mac = 4;
    string begin_time = 5;
    string end_time = 6;
}

message GroupAdaptDelVsSchedMsg
{
    //cmd id: MSG_C2S_DEL_VIDEO_STORAGE_SCHED
    uint32 id = 1;
    uint32 sched_type = 2;
    string mac = 3;
}

message P2PAdaptDelVsMsg
{
    //cmd id: MSG_C2S_DEL_VIDEO_STORAGE
    uint32 video_id = 1;
}

message P2PAdaptAccountActInfoMsg
{
    //cmd id: MSG_C2S_ACCOUNT_ACTIVE
    uint32 active = 1;
    string user_name = 2;
    string email = 3;
    string web_url = 4;
    string time = 5;//格式:'2299-12-31 23:59:59'
    uint32 subscription = 6;
}

message P2PAdaptPmAccountActInfoMsg
{
    //cmd id: MSG_C2S_PM_ACCOUNT_ACTIVE
    string account = 1;
    string user_name = 2;
    string email = 3;
    string web_url = 4;
    string time = 5;//格式:'2299-12-31 23:59:59'
    uint32 active = 6;
    uint32 subscription = 7;
}

message P2PAdaptTmpKeyInfoMsg
{
    //cmd id: MSG_C2S_SHARE_TMPKEY
    string tmp_key = 1;
    string email = 2;
    string msg = 3;
    string count_every = 4;
    string start_time = 5;
    string stop_time = 6;
    string qrcode_body = 7;
    string web_url = 8;
    string language = 9;
    int32  mng_id = 10;
}
message P2PAdaptRemoteOpenDoorMsg
{
    //cmd id: MSG_C2S_REMOTE_OPENDOOR
    string mac = 1;
    string uid = 2;
    uint32 relay = 3;
    string msg_traceid = 4;
    string repost_mac = 5; //用于转发的mac
}

message P2PAdaptCreatePropertyWorkMsg
{
    //cmd id: MSG_C2S_CREATE_PROPERTY_WORK
    string email = 1;
    string user_name = 2;
    string pwd = 3;
    string srv_web_url = 4;
}

//续费邮件接口
message P2PAdaptRenewSrvMsg
{
    //cmd id: MSG_C2S_RENEW_SERVER
    message P2PAdaptRenewSrvInnerMsg
    {
        string uid = 1;
        string user_name = 2;//昵称
        string email = 3;
        string time = 4;//格式:'2299-12-31 23:59:59'
        int32  type = 5;
    }
    repeated P2PAdaptRenewSrvInnerMsg renew_srv_uid_list = 1;
}

//pm续费邮件接口
message P2PAdaptPmRenewSrvMsg
{
    //cmd id: MSG_C2S_PM_RENEW_SERVER
    string uid = 1;
    string user_name = 2;//昵称
    string email = 3;
    string time = 4;//格式:'2299-12-31 23:59:59'
    int32  type = 5;
}

//邮件通知pm,社区下游账号即将过期
message P2PAdaptPMAccountWillExpireMsg
{
    //cmd id: MSG_C2S_PM_WILL_EXPIRE
    string community = 1;
    string email = 2;
    string pm_name = 3;
    int32 account_num = 4;
    string list = 5;
    int32 before = 6;
}


//以下为原先csmain发送给csadapt的信令
message P2PMainDevConfigRewriteMsg
{
    //cmd id: MSG_S2C_DEV_CONFIG_REWRITE
    string mac = 1;
    int32 type = 2;
    string ip = 3;
    //经过重复判断的消息,不需要进行再进行判断
    int32 already_check = 4;
    int32 project_id = 5;
}

//csmain用户通知更新设备配置的信令 
message P2PMainAccountConfigRewriteMsg
{
    //cmd id: MSG_S2C_ACCOUNT_CONFIG_REWRITE
    string node = 1;
    string account = 2;
    string main_site = 3;
    int32 type = 4;
    int32 account_role = 5;
    int32 manager_id = 6;
    int32 unit_id = 7;
    string project_uuid = 8; //只有office有赋值
}

message P2PMainDevReportVisitorMsg
{
    //cmd id: MSG_S2C_DEV_REPORT_VISITOR
    int32 id = 1;

}

message P2PMainHandleVisitorAuth
{
    //cmd id: AKCS_M2R_P2P_VISITOR_AUTHORIZE_REQ
    int32  count = 1;
    string mac = 2;
}

message P2PMainHandleForwardFaceData
{
    //cmd id: AKCS_M2R_P2P_FACE_DATA_FORWORD_REQ
    string model_url = 1;
    string mac_list = 2;
}

message P2PMainNotifyDevOffline
{
    //cmd id: AKCS_M2R_P2P_NOTIFY_DEV_OFFLINE_REQ
    string mac_list = 1;
    string location_list = 2;
    string community = 3;
    string name = 4;
    string email = 5;
    string time =6;
    int32  quantity = 7;
}

message GroupMngTextMsg
{
    //cmd id:   0x00400007 AKCS_M2R_GROUP_MNG_TEXT_MSG_REQ
    repeated string node_list = 1;
    int32 client_type = 2;
    string type = 3;
    string title = 4;
    string content = 5;
    string time = 6;
    string from = 7;
    string to = 8;
    uint32 id = 9;
}



message GroupAdaptAlexaLoginMsg
{
    //cmd id: MSG_C2S_ALEXA_LOGIN_MSG
    string node = 1;
}


message P2PAdaptAlexaSetArmingMsg
{
    //cmd id: MSG_C2S_ALEXA_SET_ARMING_MSG
    string mac = 1;
    int32  mode = 2;
}

message P2PAdaptPhoneExpireMsg
{
    //cmd id: MSG_C2S_PHONE_EXPIRE
    string user_name = 1;//主账号
    string email = 2;
}

message P2PAdaptPhoneWillExpireMsg
{
    //cmd id: MSG_C2S_PHONE_WILL_EXPIRE
    string user_name = 1;//主账号
    string email = 2;
    int32  before = 3;
}

message P2PAdaptInstallerPhoneWillExpireMsg
{
    //cmd id: MSG_C2S_INSTALLER_PHONE_WILL_EXPIRE
    string user_name = 1;
    string email = 2;
    int32  count = 3;
    string list = 4;
    int32  before = 5; 
}

message P2PAdaptInstallerAppWillExpireMsg
{
    //cmd id: MSG_C2S_INSTALLER_APP_WILL_EXPIRE
    string user_name = 1;
    string email = 2;
    string community = 3;
    int32  count = 4;
    string list = 5;
    int32  before = 6;
}

message P2PAdaptPmFeatureWillExpireMsg
{
    //cmd id: MSG_C2S_PM_FEATURE_WILL_EXPIRE
    string user_name = 1;
    string email = 2;
    string location = 3;
    int32  before = 4; 
}

message P2PAdaptInstallerFeatureWillExpireMsg
{
    //cmd id: MSG_C2S_INSTALLER_FEATURE_WILL_EXPIRE
    string user_name = 1;
    string email = 2;
    string location = 3;
    int32  before = 4; 
}

message P2PAdaptCreateRemoteDevContorlMsg
{
    //cmd id: MSG_S2C_DEV_CONFIGFILE_REWRITE
    string user = 1;
    string password = 2;
    int32 port = 3;
    string mac = 4;
    string ssh_proxy_domain = 5;
}

message P2PMainRequestOpenDoor
{
    //cmd id: AKCS_M2R_P2P_OPEN_DOOR_REQ
    string uid = 1;
    string mac = 2; 
    int32  relay = 3;
    string open_door_type = 4;
    string msg_traceid = 5;
}

message P2PMainSendDelivery
{
    //cmd id: AKCS_M2R_P2P_SEND_DELIVERY_REQ
    string account = 1;
    int32  amount = 2;
    int32  type = 3;
    string content = 4; 
    string main_site = 5;
}

message GroupAdaptNotifyRefreshConnCache
{
    //cmd id: MSG_C2S_REFRESH_CONN_CACHE
    int32 type = 1;
    string mac = 2;
    string node = 3;    
}

message P2PMainSendTmpkeyUsed
{
    //cmd id: AKCS_M2R_P2P_SEND_TMPKEY_USED_REQ
    string account = 1;
    string  name = 2;
    int32  type =3;
    string main_site = 4;
}

message SendSmsRemindFlowOutofLimit{
    //cmd id: MSG_C2S_SEND_REMIND_FLOW_OUT_OF_LIMIT
    uint32 type = 1;
    string area_code = 2;
    string phone = 3;
    string location = 4;
    string unit_name = 5;
    int32 grade = 6;
    string flow = 7;
    string language = 8;
    int32 out_of_flow = 9;
    string community_name = 10;
}

message P2PMainChangeRelay
{
    //cmd id: AKCS_M2R_P2P_CHANGE_RELAY_REQ
    string mac = 1;
    int32  relay_id = 2;
    int32  relay_switch =3;
    int32 relay_type = 4; //0=local relay 1=extern relay
}

message GroupMainReportRelayStatus
{
    //cmd id: AKCS_M2R_GROUP_REPORT_RELAY_REQ
    string mac = 1;
    uint64 relay_status = 2;
    string account = 3;
    int32  relay_type = 4; //0=LocalRelay, 1=ExternRelay
}

message P2PMainRequestWriteUserinfo
{
    //cmd id: MSG_S2C_DEV_REQ_USER_INFO
    string mac = 1;
    string uuids = 2;//uuid列表分号隔开
    uint64 msg_traceid = 3;
    string accounts_key = 4;//请求用户列表的标识
    uint64 timestamp = 5;
    //经过重复判断的消息,不需要进行再进行判断
    int32 already_check = 6;
    int32 dclient_ver = 7;
    string project_uuid = 8;
}

message P2PAdaptNotifyFileChangeMsg
{
    //cmd id: MSG_C2S_NOTIFY_FILE_CHANGE
    string mac = 1;
    int32 type = 2;//更新配置文件类型
    uint64 msg_traceid = 3;
    string file_path = 4;//文件路径 不包括地址
    string file_md5 = 5;//
}

message SendSmsCodeDelAppAccount {
    //cmd id: MSG_C2S_SEND_SMS_DELETE_APP_ACCOUNT
    uint32 type = 1;
    string area_code = 2;
    string phone = 3;
    string language = 4;
    string code = 5;
}

message P2PAdaptDelAppAccountMailMsg
{
    //cmd id: MSG_C2S_SEND_MAIL_DELETE_APP_ACCOUNT
    string email = 1;
    string language = 2;
    string name = 3;
    string code = 4;
    uint32 to_master = 5;
}

message SendSmsCreateUid {
    //cmd id: MSG_C2S_SEND_SMS_CREATE_UID
    uint32 type = 1;
    string user = 2;
    string pwd = 3;
    string phone = 4;
    string language = 5;
    string area_code = 6;
}

message P2PAdaptPmAppCreateUidMailMsg
{
    //cmd id: MSG_C2S_PM_APP_SEND_CREATE_UID_MAIL
    string user = 1;
    string pwd = 2;
    string email = 3;
    string qrcode_body = 4;
    string qrcode_url = 5;
    string srv_web_url = 6;
}

message P2PAdaptPMAppAccountWillExpireMsg
{
    //cmd id: MSG_C2S_PM_APP_ACCOUNT_WILL_EXPIRE
    string community = 1;
    string email = 2;
    string pm_name = 3;
    int32 account_num = 4;
    string list = 5;
    int32 before = 6;
}

message P2PAdaptPMAppAccountExpireMsg
{
    //cmd id: MSG_C2S_PM_APP_ACCOUNT_EXPIRE
    string community = 1;
    string email = 2;
    string pm_name = 3;
    int32 account_num = 4;
    string list = 5;
}

message P2PStorageHandleOfflineAckMsg
{
    //cmd id: AKCS_S2R_P2P_OFFLINE_MSG_ACK_REQ
    string mac = 1;
    string tar_filename = 2;
    int32  status = 3;
}

message P2PStorageHandleVoiceAckMsg
{
    //cmd id: AKCS_S2R_P2P_VOICE_MSG_ACK_REQ
    string mac = 1;
    string filename = 2;
    int32  result = 3;
    int32  project_type = 4;
}

message P2PSendVoiceMsg
{
    //cmd id: AKCS_M2R_P2P_SEND_VOICE_MSG
    int32 count = 1;
    string location = 2;
    string receiver_uuid = 3;
    int32  receiver_type = 4;
    int32 msg_id = 5;
    int32 project_type = 6;
    string main_site = 7;
}

message P2PPmEmergencyDoorControlMsg
{
    //cmd id: MSG_C2S_PM_EMERGENCY_DOOR_CONTROL
    string msg_uuid = 1;       //pmlog的uuid
    string device_uuid = 2;    //设备的uuid
    string initiator = 3;      //操作人PM名称
    int32 auto_manual = 4;     //自动0/手动1
    int32 operation_type = 5;  //关门0/开门1
    string relay = 6;
    string security_relay = 7;
    string mac = 8;
}

message P2PMainResponseOpenDoorMsg
{
    //cmd id: MSG_TO_DEVICE_OPENDOOR_ACK
    string msg_traceid = 1;   //traceid
    int32 result = 2;          //开门结果
    string info = 3;          //请求失败的原因
    string mac_or_uid = 4;           //回给那台设备的mac或回给App的uid
    int32 response_type = 5;    //回复类型 0=App 1=设备
}

message P2PSendUserAddNewSite
{
    string name = 1;
    string email = 2;
    string project_name = 3;
    string apt_num = 4;
    string send_type = 5;
    int32 role = 6;
}

message P2PSendPmWebLinkNewSites
{
    string name = 1;
    string email = 2;
    string comm_name_list = 3;
    string office_name_list = 4;
}

message P2PSendCodeToEmail
{
    string name = 1;
    string email = 2;
    string language = 3;
    string code = 4;
    string type = 5;
}

message P2PSendCodeToMobile
{
    string area_code = 1;
    string phone = 2;
    string language = 3;
    string code = 4;
    string type = 5;
}

//多套房主站点变更
message GroupAdaptNotifyChangeMainSite{
    //cmd id: MSG_C2S_CHANGE_MAIN_SITE
    string before_main_site = 1;//变更前主站点account
    string after_main_site = 2;//变更后主站点account
}

message P2PSendRequestDevDelLog
{
    string mac = 1;
}

message P2PAdaptNotifyAppRefreshConfigMsg
{
    string account = 1;
    int32 project_type = 2;
}

message P2PSendDeliveryMsg
{
    //cmd id: AKCS_M2R_P2P_SEND_DELIVERY_MSG 
    string title = 1;
    string content = 2;
    int32 receiver_type = 3;
    string receiver_uuid = 4;
    int32 project_type = 5;
    int32 message_id = 6;
}

message P2PSendEmergencyNotifyMsg
{
    //cmd id:AKCS_M2R_P2P_SEND_DELIVERY_MSG
    int32 control_type = 1;
    string receiver_uid = 2; //实际接收者uid
    string timenow = 3; 
}

// csroute->web
message P2PRouteToWebMsg
{
    int32 message_type = 1; //消息类型
    string msg_json = 2;// json格式的消息
}

message P2PSendAlarmNotifyMsg
{
    string alarm_type = 1;//告警内容
    string community = 2;
    string address = 3;  //也就是node
    string time = 4;
    string msg_seq = 5;
    string from_local = 6;//设备的location
    string mac = 7;
    uint32 grade = 8;//公共设备不需要广播消息
    uint32 mng_account_id = 9;
    uint32 unit_id = 10;
    uint32 extension = 11;
    uint32 device_type = 12;
    uint32 id = 13;
    uint32 alarm_code= 14;//区分于第一个参数,用于程序判断的类型
    uint32 alarm_zone= 15;//哪个防区
    uint32 alarm_location= 16;//哪个位置
    uint32 alarm_customize= 17;//是否是设备自定义的alarm消息
    string alarm_uuid = 18;
    uint64 trace_id = 19; // 用于推送解除告警消息
    string receive_endpoint = 20; // 通知的对象
}

//motion P2P消息发送
message P2PSendMotionNotifyMsg
{
    //cmd id: AKCS_M2R_P2P_SEND_MOTION_NOTIFY_MSG
    string mac = 1;//上报motion的设备mac
    string node = 2;
    string dev_location = 3;
    string motion_time = 4;
    string sip_account = 5;
    uint32 id = 6;
    string receiver_account = 7;
    uint32 detection_type = 8;  //0:移动侦测1:包裹检测2声音检测
    uint32 detection_info = 9; // 侦测类型为1时，这里的0包裹放入，1包裹放出；侦测类型为2时，这里的0枪声 1狗叫声 2孩子哭声 3玻璃破碎 4警笛
}

message P2PCommonTxtMsgNotifyMsg
{
    int32 msg_type = 1;         // 消息类型 0=message 1=快递msg 2=TmpKey使用msg 3=JTS快递箱 4=yale锁电量剩余2周提示 5=yale锁电量剩余1周提示 6=yale锁电量过低提示 
                                            // 7=语音留言 8=booking预约成功通知 9=dormakaba锁电量过低通知 10=家居智能锁电量通知 11=连续试错密码通知
    string title = 2;           // 消息头
    string content = 3;         // 消息内容
    int32  client_type = 4;     // client_type: 1:发给设备; 2:发给app;    
    string uuid = 5;            // 目标uuid, client_type=1表示设备uuid，client_type=2表示per_uuid     
    string time = 6;            // 发送时间
    string from = 7;            // 发送人
    string to = 8;              // 接受人
    uint32 recv_msg_id = 9;     // 消息id（MessageAccountList/OfficeMessageReceiver表的消息id）
    string extension_field = 10; //消息内容拓展字段，用于消息组装
}

message P2PAdaptNotifyDeviceIsAttendanceMsg
{
    string device_uuid = 1;
}

message P2POpenDoorNotifyMsg
{
    string uid = 1;             // 用户uid
    string mac = 2;             // 设备mac
    int32 relay = 3;            // 二进制位表示开哪个门
    int32 relay_type = 4;       // 0=开的是relay,1=开的是安全relay
    int32 project_type = 5;     // 0=住宅,1=办公,2=单住户,3=新办公
    string repost_mac  = 6;     // 转发的mac
    string msg_traceid = 7;     // 消息ID
}


message P2PAlarmDealNotifyMsg
{
    //cmd id: AKCS_M2R_GROUP_ALARM_DEAL_REPLY_MSG
    //cmd id: AKCS_M2R_GROUP_OFFICE_ALARM_DEAL_NOTIFY_MSG
    //cmd id: AKCS_M2R_GROUP_NEWOFFICE_ALARM_DEAL_NOTIFY_MSG
    
    string area_node = 1;   // 告警所在节点
    string user = 2;        // 处理告警的用户
    string alarm_id = 3;    // Alarms表的告警ID
    string result = 4;      // 处理结果，目前写死Deal
    string alarm_time = 5;  // 告警时间
    uint32 target_type = 6; // 目标类型: 1=管理机, 2=室内机, 2=门口机, 4=用户APP, 5=PM APP  (详见AlarmNotifyTargetType)
    string target = 7;      // 目标节点: 管理机mac / 室内机mac / 门口机 / 用户账号 / PM账号
}

message P2PLockDownDoorControlMsg
{
    //cmd id: AKCS_M2R_P2P_LOCKDOWN_DOOR_CONTROL
    string msg_uuid = 1;	   // lockdown表的uuid
    int32 control_switch = 2;  // lockdown类型,0:off, 1:on
    string device_uuid = 3;    // 设备的uuid
    string relay = 4;          // 设备的relay
    string security_relay = 5; // 设备的security_relay
    string mac = 6;            // 设备的mac
}
message P2PRequestDeviceCaptureNotifyMsg{
	//cmd id:   AKCS_MSG_P2A_REQUEST_DEVICE_CAPTURE
	string mac = 1;
	string site = 2;
	string uuid = 3;
	string camera = 4;
	int32 project_type = 5; // 0社区 1个人
	string msg_traceid = 6;
}

message SmartLockUpdateConfigurationNotifyMsg
{
    //cmd id: AKCS_R2S_UPDATE_SMARTLOCK_CONFIGURATION_NOTIFY_REQ
    string lock_uuid = 1;
    int32 lock_type = 2;
}

message P2PSendSL20LockEventNotify
{
    //cmd id: AKCS_M2R_P2P_SEND_SL20_LOCK_EVENT_NOTIFY
    string lock_uuid = 1;
    uint32 event_type = 2;
    string site = 3;
}

