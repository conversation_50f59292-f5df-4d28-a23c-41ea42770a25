syntax = "proto3";

package AK.ServerOffice; 

//办公续费邮件接口
message P2PAdaptPMOfficeRenewMsg
{
    //cmd id: MSG_C2S_OFFICE_SEND_ACCOUNT_RENEW_MAIL
    string community = 1;
    string email = 2;
    string pm_name = 3;
    int32 account_num = 4;
	string list = 5;
}

message PMAppExpire{
	//cmd id:   MSG_C2S_NOTIFY_OFFICE_PM_ACCOUNT_EXPIRE
	string community = 1;
	string email = 2;
	string name = 3;
	int32  account_num = 4;
	string list = 5;
}
