syntax = "proto3";

package AK.VideoRecord;

service VideoRecordRpcSrv {
    rpc StartVideoRecordHandle (StartVideoRecordRequest) returns (StartVideoRecordReply) {}
    rpc StopVideoRecordHandle (StopVideoRecordRequest) returns (StopVideoRecordReply) {}
}

message StartVideoRecordRequest {
    string site = 1;        // 当前录制的站点
    string mac = 2;         // 录制的mac
}

message StartVideoRecordReply {
     uint32 ret = 1;        // 0:successful 1:fail
}

message StopVideoRecordRequest {
    string site = 1;        // 当前录制的站点
    string mac = 2;         // 录制的mac
}

message StopVideoRecordReply {
     uint32 ret = 1;        // 0:successful 1:fail
}