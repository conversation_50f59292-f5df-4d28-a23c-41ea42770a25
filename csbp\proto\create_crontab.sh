#!/bin/sh

crontab=../../../appbackend-php-service/cscron/system_scripts/protobuf
phpname=proto_crontab.php

protoc --php_out=$crontab AK.Crontab.proto
protoc --cpp_out=../../csbase/protobuf/ AK.Crontab.proto

echo "<?php" > $phpname
echo "require_once (dirname(__FILE__).'/GPBMetadata/AKCrontab.php');" >> $phpname

for i in `ls $crontab/AK/Crontab`
do
   txt="require_once (dirname(__FILE__) . '/AK/Crontab/$i');"
   echo "$txt" >> $phpname
done

echo "?>" >> $phpname

cp $phpname $crontab 
