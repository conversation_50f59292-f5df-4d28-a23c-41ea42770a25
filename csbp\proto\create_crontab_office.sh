#!/bin/sh

crontab=../../../appbackend-php-service/cscron/system_scripts/protobuf
phpname=proto_crontab_office.php

protoc --php_out=$crontab AK.CrontabOffice.proto
protoc --cpp_out=../../csbase/protobuf/ AK.CrontabOffice.proto

echo "<?php" > $phpname
echo "require_once (dirname(__FILE__).'/GPBMetadata/AKCrontabOffice.php');" >> $phpname

for i in `ls $crontab/AK/CrontabOffice`
do
   txt="require_once (dirname(__FILE__) . '/AK/CrontabOffice/$i');"
   echo "$txt" >> $phpname
done


echo "?>" >> $phpname

cp $phpname $crontab 
