#!/bin/sh

web=../../../web_backend/apache-v3.0/notify
slim=../../../slim-base/slim-cloud-app/framework/notify/proto

rm $web/proto.php
rm $web/proto_office.php
rm -r $web/AK
rm -r $web/GPBMetadata

rm $slim/proto.php
rm $slim/proto_office.php
rm -r $slim/AK
rm -r $slim/GPBMetadata

protoc --php_out=$web AK.Adapt.proto
protoc --php_out=$web AK.AdaptOffice.proto
protoc --php_out=$slim AK.Adapt.proto
protoc --php_out=$slim AK.AdaptOffice.proto

echo "<?php" > proto.php
echo "require_once (dirname(__FILE__).'/GPBMetadata/AKAdapt.php');" >> proto.php
for i in `ls $web/AK/Adapt`
do
   txt="require_once (dirname(__FILE__) . '/AK/Adapt/$i');"
   echo "$txt" >> proto.php
done

echo "?>" >> proto.php


echo "<?php" > proto_office.php
echo "require_once (dirname(__FILE__).'/GPBMetadata/AKAdaptOffice.php');" >> proto_office.php
for i in `ls $web/AK/AdaptOffice`
do
   txt="require_once (dirname(__FILE__) . '/AK/AdaptOffice/$i');"
   echo "$txt" >> proto_office.php
done

echo "?>" >> proto_office.php

cp proto.php $web 
cp proto.php $slim 

cp proto_office.php $web 
cp proto_office.php $slim 