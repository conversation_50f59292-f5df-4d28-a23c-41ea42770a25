#!/bin/bash

WORK_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
AKCS_SRC_ROOT=${WORK_DIR}/../..
AKCS_SRC_CSBASE=${AKCS_SRC_ROOT}/csbase
AKCS_SRC_CSBASE_PROTOBUF=${AKCS_SRC_CSBASE}/protobuf

if [ -f /usr/local/protobuf/bin/protoc ];then
    ln -sf /usr/local/protobuf/bin/protoc /bin/protoc
fi

if [ -f /usr/local/bin/grpc_cpp_plugin ];then
    ln -sf /usr/local/bin/grpc_cpp_plugin /bin/grpc_cpp_plugin
fi

if [ -f /usr/local/grpc/grpc_cpp_plugin ];then
    ln -sf /usr/local/grpc/grpc_cpp_plugin /bin/grpc_cpp_plugin
fi

if [ -f /usr/local/grpc/bin/grpc_cpp_plugin ];then
    ln -sf /usr/local/grpc/bin/grpc_cpp_plugin /bin/grpc_cpp_plugin
fi

build_proto() {
    if [ ! -d "$AKCS_SRC_CSBASE_PROTOBUF" ];then 
        mkdir -p $AKCS_SRC_CSBASE_PROTOBUF
    fi
    rm -rf $AKCS_SRC_CSBASE_PROTOBUF/*
    echo "path : $AKCS_SRC_CSBASE_PROTOBUF"
    protoc --cpp_out=$AKCS_SRC_CSBASE_PROTOBUF AK.Server.proto
    protoc --cpp_out=$AKCS_SRC_CSBASE_PROTOBUF AK.ServerOffice.proto
    protoc --cpp_out=$AKCS_SRC_CSBASE_PROTOBUF AK.Adapt.proto
    protoc --cpp_out=$AKCS_SRC_CSBASE_PROTOBUF AK.AdaptOffice.proto
    protoc --cpp_out=$AKCS_SRC_CSBASE_PROTOBUF AK.Base.proto
    protoc --cpp_out=$AKCS_SRC_CSBASE_PROTOBUF AK.Route.proto
    protoc --cpp_out=$AKCS_SRC_CSBASE_PROTOBUF AK.Crontab.proto
    protoc --cpp_out=$AKCS_SRC_CSBASE_PROTOBUF AK.CrontabOffice.proto
    protoc --cpp_out=$AKCS_SRC_CSBASE_PROTOBUF AK.Resid.proto
    protoc --cpp_out=$AKCS_SRC_CSBASE_PROTOBUF AK.Linker.proto
    protoc --cpp_out=$AKCS_SRC_CSBASE_PROTOBUF AK.BackendCommon.proto
    protoc --cpp_out=$AKCS_SRC_CSBASE/grpc/csmain AK.Main.proto
    protoc --grpc_out=$AKCS_SRC_CSBASE/grpc/csmain --plugin=protoc-gen-grpc=/bin/grpc_cpp_plugin AK.Main.proto
    protoc --cpp_out=$AKCS_SRC_CSBASE/grpc/cssession AK.Session.proto
    protoc --grpc_out=$AKCS_SRC_CSBASE/grpc/cssession --plugin=protoc-gen-grpc=/bin/grpc_cpp_plugin AK.Session.proto
    protoc --cpp_out=$AKCS_SRC_CSBASE/grpc/cspbxrpc AK.PBX.proto
    protoc --grpc_out=$AKCS_SRC_CSBASE/grpc/cspbxrpc --plugin=protoc-gen-grpc=/bin/grpc_cpp_plugin AK.PBX.proto
    protoc --cpp_out=$AKCS_SRC_CSBASE/grpc/rbac AK.RBAC.proto
    protoc --grpc_out=$AKCS_SRC_CSBASE/grpc/rbac --plugin=protoc-gen-grpc=/bin/grpc_cpp_plugin AK.RBAC.proto
    protoc --cpp_out=$AKCS_SRC_CSBASE/grpc/csvideorecord AK.VideoRecord.proto
    protoc --grpc_out=$AKCS_SRC_CSBASE/grpc/csvideorecord --plugin=protoc-gen-grpc=/bin/grpc_cpp_plugin AK.VideoRecord.proto
}

build_proto
