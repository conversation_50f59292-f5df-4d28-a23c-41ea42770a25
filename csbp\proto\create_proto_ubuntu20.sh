#!/bin/bash

WORK_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
AKCS_SRC_ROOT=${WORK_DIR}/../../
AKCS_SRC_CSBASE=${AKCS_SRC_ROOT}/csbase
AKCS_SRC_CSBASE_PROTOBUF=${AKCS_SRC_CSBASE}/protobuf


build_proto() {
    if [ ! -d "$AKCS_SRC_CSBASE_PROTOBUF" ];then 
        mkdir -p $AKCS_SRC_CSBASE_PROTOBUF
    fi
    rm -rf $AKCS_SRC_CSBASE_PROTOBUF/*
    echo "path : $AKCS_SRC_CSBASE_PROTOBUF"
    /usr/local/protobuf/bin/protoc --cpp_out=$AKCS_SRC_CSBASE_PROTOBUF AK.Server.proto
    /usr/local/protobuf/bin/protoc --cpp_out=$AKCS_SRC_CSBASE_PROTOBUF AK.ServerOffice.proto
    /usr/local/protobuf/bin/protoc --cpp_out=$AKCS_SRC_CSBASE_PROTOBUF AK.Adapt.proto
    /usr/local/protobuf/bin/protoc --cpp_out=$AKCS_SRC_CSBASE_PROTOBUF AK.AdaptOffice.proto
    /usr/local/protobuf/bin/protoc --cpp_out=$AKCS_SRC_CSBASE_PROTOBUF AK.Base.proto
    /usr/local/protobuf/bin/protoc --cpp_out=$AKCS_SRC_CSBASE_PROTOBUF AK.Route.proto
    /usr/local/protobuf/bin/protoc --cpp_out=$AKCS_SRC_CSBASE_PROTOBUF AK.Crontab.proto
    /usr/local/protobuf/bin/protoc --cpp_out=$AKCS_SRC_CSBASE_PROTOBUF AK.CrontabOffice.proto
    /usr/local/protobuf/bin/protoc --cpp_out=$AKCS_SRC_CSBASE_PROTOBUF AK.Resid.proto
    /usr/local/protobuf/bin/protoc --cpp_out=$AKCS_SRC_CSBASE_PROTOBUF AK.Linker.proto
    /usr/local/protobuf/bin/protoc --cpp_out=$AKCS_SRC_CSBASE/grpc/csmain AK.Main.proto
    /usr/local/protobuf/bin/protoc --grpc_out=$AKCS_SRC_CSBASE/grpc/csmain --plugin=protoc-gen-grpc=/usr/local/grpc/bin/grpc_cpp_plugin AK.Main.proto
    /usr/local/protobuf/bin/protoc --cpp_out=$AKCS_SRC_CSBASE/grpc/cssession AK.Session.proto
    /usr/local/protobuf/bin/protoc --grpc_out=$AKCS_SRC_CSBASE/grpc/cssession --plugin=protoc-gen-grpc=/usr/local/grpc/bin/grpc_cpp_plugin AK.Session.proto
}

build_proto
