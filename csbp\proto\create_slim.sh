#!/bin/sh

slim=../../../slim-base/slim-cloud-app/framework/notify/proto

rm $slim/proto.php
rm -r $slim/AK
rm -r $slim/GPBMetadata

protoc --php_out=$slim AK.Adapt.proto

echo "<?php" > proto.php
echo "require_once (dirname(__FILE__).'/GPBMetadata/AKAdapt.php');" >> proto.php
for i in `ls $slim/AK/Adapt`
do
   txt="require_once (dirname(__FILE__) . '/AK/Adapt/$i');"
   echo "$txt" >> proto.php
done

echo "?>" >> proto.php

cp proto.php $slim 