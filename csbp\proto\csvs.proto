syntax = "proto3";

option java_package = "ex.grpc";

package VideoStorage;

// The adding service definition.
service VideoStorageMsg {
  // Sends a video storage action
  rpc VideoStorageHandle (VsRequest) returns (VsReply) {}
  rpc DelVideoStorageHandle (VsDelRequest) returns (VsDelReply) {}
}


enum VideoStorageAction 
{
    NULL_VIDEO_STORAGE = 0;
    START_VIDEO_STORAGE = 1;
    STOP_VIDEO_STORAGE  = 2;
}

// The request message containing the device rtsp video infos.
message VsRequest {
  	 string storage_uid = 1;
	 string dev_rtsp_pwd = 2;
     string rtsp_srv_ip = 3;
     string storage_node = 4;
     VideoStorageAction act = 5;
     
}

// The response message containing the uri of hls
message VsReply {
	 string hls_uri = 1;
     uint32 global_video_id = 2;
     string resp_storage_mac = 3;
     string resp_storage_node = 4;
}

message VsDelRequest {
  	 uint32 global_video_id = 1;     
}

message VsDelReply {
  	 string del_reply = 1;    
}
