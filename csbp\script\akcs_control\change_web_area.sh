#!/bin/bash
echo "Enter your area 0-overseas 1-china: "
read SYSTEM_AREA;


echo "you want update web info<0-no 1-yes>: "
read updateweb;

if [ $updateweb -eq 1 ];then
	if [ $SYSTEM_AREA -eq 1 ];then
		sed -i "s/^.*var SERVER_LOCATION.*/var SERVER_LOCATION = \"cn\";/g" /var/www/html/dist/static/js/config.env.js
		sed -i "s/^.*SERVER_LOCATION.*/var SERVER_LOCATION = \"cn\";/g" /var/www/html/manage/static/js/config.env.js
		sed -i "s/^.*SERVER_LOCATION.*/var SERVER_LOCATION = \"cn\";/g" /var/www/html/manage-new/js/config.env.js
		sed -i "s/^.*SERVER_LOCATION.*/SERVER_LOCATION: 'cn',/g" /var/www/html/smartplus/js/setEnv.js
		sed -i "s/^.*isDisplayPhone =.*/var isDisplayPhone = 0;/g" /var/www/html/VBell/js/bower.js
		sed -i "s/^.*CANGETTOOLBOX.*/const CANGETTOOLBOX = 0;/g" /var/www/html/apache-v3.0/config/dynamic_config.php
		sed -i "s/^.*CANGETTOOLBOX.*/const CANGETTOOLBOX = 0;/g" /var/www/html/web-server/config/dynamic_config.php
		sed -i "s/^.*SHOWPHONE.*/var SHOWPHONE = 1;/g" /var/www/html/VBell/js/bower.js
		sed -i "s/^.*SERVER_LOCATION.*/const SERVER_LOCATION = \"cn\";/g" /var/www/html/apache-v3.0/config/dynamic_config.php
		sed -i "s/^.*SERVER_LOCATION.*/const SERVER_LOCATION = \"cn\";/g" /var/www/html/web-server/config/dynamic_config.php
	else
		sed -i "s/^.*var SERVER_LOCATION.*/var SERVER_LOCATION = \"na\";/g" /var/www/html/dist/static/js/config.env.js
		sed -i "s/^.*SERVER_LOCATION.*/var SERVER_LOCATION = \"na\";/g" /var/www/html/manage/static/js/config.env.js
		sed -i "s/^.*SERVER_LOCATION.*/var SERVER_LOCATION = \"na\";/g" /var/www/html/manage-new/js/config.env.js
		sed -i "s/^.*SERVER_LOCATION.*/SERVER_LOCATION: 'na',/g" /var/www/html/smartplus/js/setEnv.js
		sed -i "s/^.*isDisplayPhone =.*/var isDisplayPhone = 1;/g" /var/www/html/VBell/js/bower.js
		sed -i "s/^.*CANGETTOOLBOX.*/const CANGETTOOLBOX = 0;/g" /var/www/html/apache-v3.0/config/dynamic_config.php
		sed -i "s/^.*CANGETTOOLBOX.*/const CANGETTOOLBOX = 0;/g" /var/www/html/web-server/config/dynamic_config.php
		sed -i "s/^.*SHOWPHONE.*/var SHOWPHONE = 0;/g" /var/www/html/VBell/js/bower.js
		sed -i "s/^.*SERVER_LOCATION.*/const SERVER_LOCATION = \"na\";/g" /var/www/html/apache-v3.0/config/dynamic_config.php
		sed -i "s/^.*SERVER_LOCATION.*/const SERVER_LOCATION = \"na\";/g" /var/www/html/web-server/config/dynamic_config.php

	fi
fi

echo "you want update freeswitch info<0-no 1-yes>: "
read updatepbx;
if [ $updatepbx -eq 1 ];then
	if [ $SYSTEM_AREA -eq 1 ];then
		sed -i "s/system_area = .*/system_area = 1/g" /usr/local/freeswitch/scripts/akcs_cluster_call.lua
		sed -i "s/system_area = .*/system_area = 1/g" /usr/local/freeswitch/scripts/akcs_cluster_groupcall.lua
	else
		sed -i "s/system_area = .*/system_area = 5/g" /usr/local/freeswitch/scripts/akcs_cluster_call.lua
		sed -i "s/system_area = .*/system_area = 5/g" /usr/local/freeswitch/scripts/akcs_cluster_groupcall.lua	
	fi
	kill -9 `ps -ef |grep "freeswitch -nonat" | grep -v grep | awk '{print $2}'`
fi

echo "you want update csadapt info<0-no 1-yes>: "
read updateadapt;
if [ $updateadapt -eq 1 ];then
	if [ $SYSTEM_AREA -eq 1 ];then
		sed -i "s/^.*system_area_type.*/system_area_type=1/g" /usr/local/akcs/csadapt/conf/csadapt.conf 
	else
		sed -i "s/^.*system_area_type.*/system_area_type=5/g" /usr/local/akcs/csadapt/conf/csadapt.conf 
	fi
	kill -9  `cat /var/run/csadapt.pid`
fi

echo "you want update csconfig info<0-no 1-yes>: "
read updateconfig;
if [ $updateconfig -eq 1 ];then
	if [ $SYSTEM_AREA -eq 1 ];then
		sed -i "s/^.*system_area_type.*/system_area_type=1/g" /usr/local/akcs/csconfig/conf/csconfig.conf 
	else
		sed -i "s/^.*system_area_type.*/system_area_type=5/g" /usr/local/akcs/csconfig/conf/csconfig.conf 
	fi
	kill -9  `cat /var/run/csconfig.pid`
fi








