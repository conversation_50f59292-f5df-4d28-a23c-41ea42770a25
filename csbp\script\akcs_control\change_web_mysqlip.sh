#!/bin/bash
echo "Enter your old mysql master ip: "
read OLD_DB_IP;


echo "Enter your new mysql master ip: "
read NEW_DB_IP;

sed -i "s/host=${OLD_DB_IP}/host=${NEW_DB_IP}/g" /var/www/html-api/openapi/common/comm.php
sed -i "s/const DATABASEIP = \"${OLD_DB_IP}\"/const DATABASEIP = \"${NEW_DB_IP}\"/g" /var/www/html-api/openapi/PHPFramework/config/base.php
sed -i "s/const DATABASEIP=\"${OLD_DB_IP}\"/const DATABASEIP=\"${NEW_DB_IP}\"/g" /var/www/html/apache-v3.0/config/dynamic_config.php
sed -i "s/const DATABASEIP=\"${OLD_DB_IP}\"/const DATABASEIP=\"${NEW_DB_IP}\"/g" /var/www/html/WXSmartPlus/dynamic_config.php
sed -i "s/const DATABASEIP=\"${OLD_DB_IP}\"/const DATABASEIP=\"${NEW_DB_IP}\"/g" /var/www/html/slim/common/dynamic_config.php
sed -i "s/const DATABASEIP=\"${OLD_DB_IP}\"/const DATABASEIP=\"${NEW_DB_IP}\"/g" /var/www/html/web-server/config/dynamic_config.php
sed -i "s/^.*var databaseHost.*/var databaseHost=\"${NEW_DB_IP}\";/g" /usr/local/web/async-task/define.js
sed -i "s/^.*db_ip=.*/db_ip=${NEW_DB_IP}/g" /usr/local/akcs/csadapt/conf/csadapt.conf
sed -i "s/^.*MYSQL_INNER_IP=.*/MYSQL_INNER_IP=${NEW_DB_IP}/g" /etc/*.conf

killall csadapt
kill -9 `ps -ef|grep async-task/index|grep -v grep|awk '{print $2}'`





