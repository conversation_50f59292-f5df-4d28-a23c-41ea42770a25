#!/bin/bash
CONF_FILE=/usr/local/akcs/scripts/notify_email.conf
MAIL_LIST=`cat $CONF_FILE | grep APP_STOP_EMAIL_LIST | awk -F'=' '{ print $2 }'`

SERVERIP=`cat /etc/ip`

#检测证书过期
SSL_FILES="
/usr/local/nginx/conf/cert/akcs_web_cert.pem"

detect_cert_expire()
{
    for i in $SSL_FILES
    do
       date_str=`openssl x509 -in $i -noout -text | grep "Not After" | awk -F 'Not After :'  '{print $2}'`
       timestamp=`date -d "$date_str" +%s`
       now_timestamp=`date +%s`
       let "timestamp-=864000"
       if [ $timestamp -lt $now_timestamp ];then
            echo "IP: $SERVERIP. $i expire time: $date_str" | mutt -s "Cert will Expire"  ${MAIL_LIST}
       fi
    done
}


detect_cert_expire
