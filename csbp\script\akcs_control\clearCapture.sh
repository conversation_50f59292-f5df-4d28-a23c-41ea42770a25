#!/bin/bash
HOSTNAME="localhost"    #数据库信息
PORT="3306"
USERNAME="root"
PASSWORD="Ak@56@<EMAIL>"
DBNAME="AKCS"         #数据库名称
CMD="select ID,PicUrl from PersonalCapture where TO_DAYS(NOW()) - TO_DAYS(CaptureTime) >30 limit 0,1000;"
tmpfile=/tmp/redis.tmp
testfile=/tmp/test.tmp
echo "" >$tmpfile

execquit=1 #这个变量在子shell没有办法回传
while [ $execquit -eq 1 ]
do
	rm $testfile
	execquit=0
	MYSQL_CMD="mysql -N -h${HOSTNAME}  -P${PORT}  -u${USERNAME} -p${PASSWORD} ${DBNAME} -S /tmp/mysql.sock"
	(echo $CMD | ${MYSQL_CMD} 2>/dev/null)|while read id url #如果要查找多个字段，只需要在这里添加个参数
	do
		touch $testfile
		echo "delete from PersonalCapture where id=$id" | $MYSQL_CMD 2>/dev/null
                if [ -n "$url" ];then
                   curl -d "$url" 'http://localhost:4151/pub?topic=delpic' >/dev/null
                   #echo  "$url" >>$tmpfile
                fi

	done
	if [ -f $testfile ];then
		execquit=1
	fi
done

