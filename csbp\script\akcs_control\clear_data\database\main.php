<?php
include_once __DIR__."/base.php";
include_once __DIR__."/query.php";
include_once __DIR__."/update.php";
include_once __DIR__."/util.php";
class CDatabase
{
    use DataBaseBasic;
    use QueryDatabase;
    use UpdateDatabase;
    use Util;
    public function __construct($host, $dbName, $user, $password)
    {
        $this->db = $this->connect($host, $dbName, $user, $password);
    }

    public function lastInsertId()
    {
        return  $this->db->lastInsertId();
    }

    public function begin()
    {
        $this->db->beginTransaction();
    }

    public function commit()
    {
        $this->db->commit();
    }
}
