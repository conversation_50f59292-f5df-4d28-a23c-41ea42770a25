<?php
include_once __DIR__."/util.php";
trait UpdateDatabase
{
    public function insert2List($tableName, $bindArray)
    {
        //插入数据
        $keys = [];
        $filed = [];
        foreach ($bindArray as $key => $value) {
            array_push($keys, $key);
            array_push($filed, substr($key, 1));
        }
        $keys = implode(",", $keys);
        $filed = implode(",", $filed);
        
        $this->sql = "insert into $tableName ( $filed ) value( $keys )";
        $simt = $this->db->prepare($this->sql);
        foreach ($bindArray as $key => $value) {
            $simt->bindValue($key, $value);
        }
        $simt->execute();
        return $this->db->lastInsertId();
    }
    
    public function update2ListWID($tableName, $bindArray)
    {
        //根据ID更新数据
        return $this->update2ListWKey($tableName, $bindArray, "ID");
    }
    
    public function update2ListWKey($tableName, $bindArray, $keyWord)
    {
        $filed = [];
        foreach ($bindArray as $key => $value) {
            $keys = substr($key, 1)." = ".$key;
            array_push($filed, $keys);
        }
        
        $filed = implode(",", $filed);
        
        $this->sql = "update $tableName set $filed where $keyWord = :$keyWord";
        $simt = $this->db->prepare($this->sql);
        foreach ($bindArray as $key => $value) {
            $simt->bindValue($key, $value);
        }
        $simt->execute();
        
        return $simt->rowCount();
    }
    
    
    public function delete2ListWID($tableName, $id)
    {
        //根据ID删除数据
        $this->sql = "delete from $tableName where ID = :ID";
        $simt = $this->db->prepare($this->sql);
        $simt->bindValue(":ID", $id);
        $simt->execute();
    }
    
    public function delete2ListWKey($tableName, $keyName, $keyValue)
    {
        $this->sql = "delete from $tableName where $keyName = :$keyName";
        $simt = $this->db->prepare($this->sql);
        $simt->bindValue(":$keyName", $keyValue);
        $simt->execute();
    }

    public function execSql($sql)
    {
        $this->sql = $sql;
        $simt = $this->db->prepare($this->sql);
        $simt->execute();
    }
}
