<?php
    trait Util {
		function getWhere ($bindArray)
		{
			$where = "";
			if(count($bindArray) === 0)
				return $where;
			else
			{
				$equation = "";
				if(array_key_exists("equation", $bindArray))
				{
					//相等的条件查询
					$equation = $this->bindArr2StrEqu($bindArray["equation"],"=");
					//单独一个项时
					$where = $equation;
				}
				
				$unEquation = "";
				if(array_key_exists("unEquation", $bindArray))
				{
					//不等的条件查询
					$unEquation = $this->bindArr2StrEqu($bindArray["unEquation"],"!=");
					//单独一个项时
					$where = $unEquation;
				}
				
				$like = "";
				if(array_key_exists("like", $bindArray))
				{
					//相似的条件查询
					$like = $this->bindArr2StrEqu($bindArray["like"],"like");
					//单独一个项时
					$where = $like;
				}
				
				if(array_key_exists("e2Un", $bindArray) && array_key_exists("Un2Li", $bindArray))
				{
					//如果全存在时
					$e2Un = $bindArray["e2Un"];
					$Un2Li = $bindArray["Un2Li"];
					$where = "$equation $e2Un $unEquation $Un2Li $like";
				}else
				{
					//如果只存在其中两个时
					if (array_key_exists("e2Un", $bindArray))
					{
						$e2Un = $bindArray["e2Un"];
						$where = "$equation $e2Un $unEquation";
					}
					if (array_key_exists("e2Li", $bindArray))
					{
						$e2Li = $bindArray["e2Li"];
						$where = "$equation $e2Li $like";
					}
					if (array_key_exists("Un2Li", $bindArray))
					{
						$Un2Li = $bindArray["Un2Li"];
						$where = "$unEquation $Un2Li $like";
					}
				}
			}
			return "where $where";
		}
		
		function bindArr2StrEqu($array,$type)
		{
			//将PDO [":A"=>a,":B"=>b] 转换为 (A = :A AND B = :B)
			if(count($array) == 0)
				return "";
			$tmp = [];
			foreach($array as $key => $value)
			{
				$keys = substr($key, 1)." $type ".$key;
				array_push($tmp,$keys);
			}
			$str = implode(" AND ",$tmp);
			return "( $str )";
		}
    }