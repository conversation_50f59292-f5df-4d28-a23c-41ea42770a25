<?php
/**
 * 无法处理PIN唯一性，sip和sip群组重复，sip分配问题，无效的unitid，单住户过期的问题
 */
require_once "./config.php";
require_once "./database/main.php";
$options = "t:";
$params = getopt($options);
$t = $params['t'];
if ($t != 'all' && !in_array($t, TABLES)) {
    echo "please input param -t all or\r\n".implode("\r\n", TABLES);
    die;
}

$db =  new CDatabase(AK_DB_IP, AK_DB_NAME, AK_DB_USER, AK_DB_PW);
$now = date('Y_m_d_H_i_s');
$brkName = "AKCS_$now";
$db->execSql("create database $brkName");

function clear($sql, $table)
{
   if ( $table == "PersonalCapture" || $table == "PersonalMotion")
   {
       global $db, $brkName;
       $db->execSql($sql);
       return;
   }
	global $db, $brkName;
	echo "备份数据库: $brkName\n";
	echo "备份数据库start：".date('Y_m_d_H_i_s')."\n";
	// 备份
	$db->execSql("use $brkName");
	$db->execSql("create table $table select * from ".AK_DB_NAME.".$table");
	echo "备份数据库end:".date('Y_m_d_H_i_s')."\n";
    // 清理
    $db->execSql("use ".AK_DB_NAME);
    $db->execSql($sql);
}

function checkTable($table)
{
    global $t;
    if ($t == 'all') {
        return true;
    }
    if (is_string($table)) {
        return $t == $table;
    }
    if (is_array($table)) {
        return in_array($t, $table);
    }
    return false;
}

echo "begin\r\n";
$sql = [];
$db->begin();
if (checkTable("PubDevMngList")) {
    array_push($sql, "delete from PubDevMngList where DevicesID not in (select ID from Devices where Grade = 1)");
} elseif (checkTable([
    'AccessGroupDevice',
    'UserAccessGroupDevice',
    'PersonalPrivateKeyList',
    'PersonalRfcardKeyList',
    'PubAppTmpKeyList',
    'PubPrivateKeyList',
    'PubRfcardKeyList',
    'Temperature',
    'PersonalCapture',
    'PersonalMotion'
])) {

    $macs = $db->querySList('select MAC from Devices union select MAC from PersonalDevices', []);
    foreach ($macs as &$mac) {
        $mac = "'".$mac['MAC']."'";
    }
    unset($mac);
    $macs = implode(',', $macs);

    array_push($sql, "delete from $t where MAC not in ($macs)");
} elseif (checkTable(['PersonalAlarms', 'Alarms'])) {

    $macs = $db->querySList('select MAC from Devices union select MAC from PersonalDevices', []);
    foreach ($macs as &$mac) {
        $mac = "'".$mac['MAC']."'";
    }
    unset($mac);
    $macs = implode(',', $macs);

    array_push($sql, "delete from $t where DevicesMAC not in ($macs)");
} elseif (checkTable(["PersonalAccountCnf", "PersonalBillingInfo"])) {
    array_push($sql, "delete from $t where Account not in (select Account from PersonalAccount where Role in (10,20))");
} elseif (checkTable(["AccountAccess", "CommPerPrivateKey"])) {
    array_push($sql, "delete from $t where Account not in (select Account from PersonalAccount)");
} elseif (checkTable(["PersonalPrivateKey"])) {
    array_push($sql, "delete from $t where Node not in (select Account from PersonalAccount)");
} elseif (checkTable(["PersonalAppTmpKey"])) {
    array_push($sql, "delete from $t where Node not in (select Account from PersonalAccount where Role in (10,20))");
} elseif (checkTable(["SipGroup2"])) {
    array_push($sql, "delete from SipGroup2 where Account not in (select Account from PersonalAccount where Role in (10,20))");
}

foreach ($sql as $val) {
    clear($val, $t);
}
$db->commit();
