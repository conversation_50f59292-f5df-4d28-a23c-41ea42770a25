#!/bin/bash
COMMON_FIRST_RUN=1
key_csmain_stop=alarm.app.stop.csmain
key_csroute_stop=alarm.app.stop.csroute
key_csvrtspd_stop=alarm.app.stop.csvrtspd
key_app_stop=alarm.app.stop

send_akcs_alarm() {
    #这个key先再里面判断处理
	key=$1
	node=$2
	alarm_msg=$3

	if [ $node = "csmain" ];then
		key=${key_csmain_stop}
	elif [ $node = "csroute" ];then
		key=${key_csroute_stop}
	elif [ $node = "csvrtspd" ];then
		key=${key_csvrtspd_stop}
	else
		key=${key_app_stop}
	fi

	CONF_FILE=/etc/ip
	hostname=`cat $CONF_FILE | grep AKCS_HOSTNAME= | awk -F'=' '{ print $2 }'`
	ip=`cat $CONF_FILE | grep SERVERIP= | awk -F'=' '{ print $2 }'`
	nsqd_addr=`cat $CONF_FILE | grep SERVER_INNER_IP= | awk -F'=' '{ print $2 }'`
	time=`date "+%Y-%m-%d %H:%M:%S"`

	data="
	{
		\"description\":\"${alarm_msg}\",
		\"hostname\":\"${hostname}\",
		\"ip\":\"${ip}\",
		\"key\":\"${key}\",
		\"node\":\"${node}\",
		\"time\":\"${time}\"
	}"

	curl -s -d "$data" http://$nsqd_addr:8513/pub?topic=akcs_alarm
}

app_stop_email()
{
    COMMON_PROCESS_NAME=$1
    COMMON_LOG_FILE=$2
    DEATH_DETECT_TIME_FILE=/tmp/.$COMMON_PROCESS_NAME

    email=0
    if [ -f ${DEATH_DETECT_TIME_FILE}* ];then
        time=`ls ${DEATH_DETECT_TIME_FILE}* | awk -F '_' '{print $NF}'`
        unix=`date +%s`
        let time=$time+60
        if [ $time -lt $unix ];then
            #报警  重新计算时间
            rm  /tmp/.$1*
            touch ${DEATH_DETECT_TIME_FILE}_`date +%s`
            email=1
        fi
    else
        touch ${DEATH_DETECT_TIME_FILE}_`date +%s`
        email=1
    fi
    if [ $email -eq 1 ];then
		send_akcs_alarm "$key" "$COMMON_PROCESS_NAME" "${COMMON_PROCESS_NAME} is stopped"
        echo "sending email...." >> $COMMON_LOG_FILE
    fi
}

#COMMON_PROCESS_NAME 这个变量，在函数调用会重写调用方
common_run_pid_detect() {
    COMMON_PROCESS_NAME=$1
    COMMON_PROCESS_PID_FILE=$2
    COMMON_PROCESS_START_CMD=$3
    COMMON_LOG_FILE=$4
    if [ -f $COMMON_PROCESS_PID_FILE ];then
        pid=`cat $COMMON_PROCESS_PID_FILE`
        if [ $pid"x" = "x" ];then
           #pid里面的文件是空的
           pid="xxxxxxxxxx"
        fi
    else
        #重启之后没有这个pid文件
        pid="xxxxxxxxxx"
    fi

	count=`ls /proc/$pid | wc -l`
	if [ $count -eq 0 ]
	then
		date >> $COMMON_LOG_FILE
        echo "warning !, $1 is stopped..." >> $COMMON_LOG_FILE
		$COMMON_PROCESS_START_CMD
		if [ $COMMON_FIRST_RUN -ne 1 ];then
			echo "${COMMON_PROCESS_NAME} stop"  >> $COMMON_LOG_FILE
			app_stop_email $COMMON_PROCESS_NAME $COMMON_LOG_FILE
		fi
		sleep 2
	fi
}


common_run_netstat_detect() {
    COMMON_PROCESS_NAME=$1
    COMMON_PROCESS_START_CMD=$2
    COMMON_LOG_FILE=$3
    count=`ss -ntlp | grep $COMMON_PROCESS_NAME | grep -v grep | wc -l`
    if [ $count -eq 0 ]
    then
        date >> $COMMON_LOG_FILE
        echo "warning !, $COMMON_PROCESS_NAME is stopped..." >> $COMMON_LOG_FILE
        `$COMMON_PROCESS_START_CMD`
        if [ $COMMON_FIRST_RUN -ne 1 ];then
            echo "$COMMON_PROCESS_NAME stop"  >> $COMMON_LOG_FILE
            app_stop_email $COMMON_PROCESS_NAME $COMMON_LOG_FILE
        fi
        sleep 2
    fi
}
