#!/bin/bash

INSTALL_CONF=$1
APP_MYSQL_CONF=$2
APP_MYSQL_CONF2=$3
#read 删除不能用问题
stty erase ^H
font_off(){
	echo -en "\033[0m"
}

yellow(){
    echo -e "\033[33m$1\033[0m"
	font_off
}

red(){
    echo -e "\033[31m$1\033[0m"
	font_off
}

EnterBasicSrvIPAddr()
{

    #输入dbproxy
    yellow "Enable dbproxy  1=enable 0=disable: \c"
    read ENABLE_DBPROXY;

    if [ $ENABLE_DBPROXY -eq 1 ];then
        yellow "Enter dbproxy inner IP: \c"
        read DBPROXY_INNER_IP;
    fi
    #写mysql dbproxy 配置
    sed -i '/ENABLE_DBPROXY/d' $INSTALL_CONF
    sed -i '/DBPROXY_INNER_IP/d' $INSTALL_CONF
    echo "ENABLE_DBPROXY=$ENABLE_DBPROXY" >>$INSTALL_CONF
    echo "DBPROXY_INNER_IP=$DBPROXY_INNER_IP" >>$INSTALL_CONF
}


EchoBasicSrvIPAddr()
{
    str="ENABLE_DBPROXY="
    value=`cat $INSTALL_CONF | grep -w ENABLE_DBPROXY | awk -F'=' '{ print $2 }'`
    echo $str$value

    str="DBPROXY_INNER_IP="
    value=`cat $INSTALL_CONF | grep -w DBPROXY_INNER_IP | awk -F'=' '{ print $2 }'`
    echo $str$value

}
#再确定redis、mysql、etcd、nsqlookupd等组件的内网ip信息
if [ -f $INSTALL_CONF ];then
    EchoBasicSrvIPAddr
    ENABLE_DBPROXY=`cat $INSTALL_CONF | grep -w ENABLE_DBPROXY | awk -F'=' '{ print $2 }'`
    DBPROXY_INNER_IP=`cat $INSTALL_CONF | grep -w DBPROXY_INNER_IP | awk -F'=' '{ print $2 }'`
    yellow "please comfirm the config information is ok? Enter 1/0(1 means ok, 0 means no):"
    read yes;
    if [ $yes -eq 0 ];then
        EnterBasicSrvIPAddr
    fi
else
    red "Can not found install config $INSTALL_CONF";
    exit
fi

#空值兼容处理
if [ $APP_MYSQL_CONF"x" == "x" ];then
    exit
fi
if [ ! -f $APP_MYSQL_CONF ];then
    red "Can not found app mysql config $APP_MYSQL_CONF";
    exit
fi

if [ $ENABLE_DBPROXY -eq 1 ];then
    sed -i "s/^.*db_port=.*/db_port=3308/g" $APP_MYSQL_CONF
    sed -i "s/^.*db_ip=.*/db_ip=${DBPROXY_INNER_IP}/g" $APP_MYSQL_CONF
else
    MYSQL_INNER_IP=`cat $INSTALL_CONF | grep -w MYSQL_INNER_IP | awk -F'=' '{ print $2 }'`
    sed -i "s/^.*db_port=.*/db_port=3306/g" $APP_MYSQL_CONF
    sed -i "s/^.*db_ip=.*/db_ip=${MYSQL_INNER_IP}/g" $APP_MYSQL_CONF
fi

#空值兼容处理
if [ $APP_MYSQL_CONF2"x" == "x" ];then
    exit
fi
if [ ! -f $APP_MYSQL_CONF2 ];then
    red "Can not found app mysql config $APP_MYSQL_CONF";
    exit
fi

if [ $ENABLE_DBPROXY -eq 1 ];then
    sed -i "s/^.*db_port=.*/db_port=3308/g" $APP_MYSQL_CONF2
    sed -i "s/^.*db_ip=.*/db_ip=${DBPROXY_INNER_IP}/g" $APP_MYSQL_CONF2
else
    MYSQL_INNER_IP=`cat $INSTALL_CONF | grep -w MYSQL_INNER_IP | awk -F'=' '{ print $2 }'`
    sed -i "s/^.*db_port=.*/db_port=3306/g" $APP_MYSQL_CONF2
    sed -i "s/^.*db_ip=.*/db_ip=${MYSQL_INNER_IP}/g" $APP_MYSQL_CONF2
fi
