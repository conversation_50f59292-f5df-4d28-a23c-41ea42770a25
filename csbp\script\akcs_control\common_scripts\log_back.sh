#!/bin/bash

csmain_log_back(){
	DAY3=`date +"%Y%m%d"`
	cd $1 || exit 1
	mkdir backup 2>/dev/null
	now_ln_file=`ls -l | grep "\->" | awk '{print $9"\n"$11}'`
	now_all_file=`ls`
	for file in $now_all_file
	do
		if [[ "${now_ln_file[*]}" =~ ${file} ]];then
			echo "$file"
		else
			if [ $file != "backup" ];then
				#解决错误日志备份时候的文件名不是当前时间
				tar -czf ${file}-${DAY3}.tar.gz $file
				mv ${file}-${DAY3}.tar.gz backup
				rm $file

			fi
		fi
	done
}

common_log_back(){
	DAY3=`date +"%Y%m%d"`
    cd $1 || exit 1
	mkdir backup 2>/dev/null
	now_ln_file=`ls -l | grep "\->" | awk '{print $9"\n"$11}'`
	now_all_file=`ls`
	for file in $now_all_file
	do
		if [[ "${now_ln_file[*]}" =~ ${file} ]];then
			echo "$file"
		else
			if [ $file != "backup" ];then
				#解决错误日志备份时候的文件名不是当前时间
				tar -czf ${file}-${DAY3}.tar.gz $file
				mv ${file}-${DAY3}.tar.gz backup
				rm $file
			fi
		fi
	done

    #每种日志类型保留不同个数,INFO数量较多
    cd backup || exit 1
    if [ `ls | grep INFO | grep gz | wc -l |  tr -cd "[0-9]"` -gt 10 ]; then
       ls -lt | grep INFO | grep gz | tail -$((`ls | grep INFO | grep gz | wc -l | tr -cd "[0-9]"` - 10)) | awk '{print $9}' | xargs rm -rf {}
    fi
	if [ `ls | grep ERROR | grep gz | wc -l |  tr -cd "[0-9]"` -gt 5 ]; then
       ls -lt | grep ERROR | grep gz | tail -$((`ls | grep ERROR | grep gz | wc -l | tr -cd "[0-9]"` - 5)) | awk '{print $9}' | xargs rm -rf {}
    fi
	if [ `ls | grep WARN | grep gz | wc -l |  tr -cd "[0-9]"` -gt 5 ]; then
       ls -lt | grep WARN | grep gz | tail -$((`ls | grep WARN | grep gz | wc -l | tr -cd "[0-9]"` - 5)) | awk '{print $9}' | xargs rm -rf {}
    fi
}
