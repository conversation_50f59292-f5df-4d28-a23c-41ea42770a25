#!/bin/bash

INSTALL_CONF=$1
APP_REDIS_CONF=$2
#read 删除不能用问题
stty erase ^H
font_off(){
	echo -en "\033[0m"
}

yellow(){
    echo -e "\033[33m$1\033[0m"
	font_off
}

red(){
    echo -e "\033[31m$1\033[0m"
	font_off
}

EnterBasicSrvIPAddr()
{
    #输入redis sentinel
    yellow "Enable redis sentinel 1=enable 0=disable: \c"
    read ENABLE_REDIS_SENTINEL;

    if [ $ENABLE_REDIS_SENTINEL -eq 1 ];then
        yellow "Enter your sentinel addr,eg 192.168.14:8506, *************:8599"
        yellow "Enter: \c"
        read SENTINEL_HOSTS;
    fi

    #写redis sentinel配置
    sed -i '/ENABLE_REDIS_SENTINEL/d' $INSTALL_CONF
    sed -i '/SENTINEL_HOSTS/d' $INSTALL_CONF
    echo "ENABLE_REDIS_SENTINEL=$ENABLE_REDIS_SENTINEL" >>$INSTALL_CONF
    echo "SENTINEL_HOSTS=$SENTINEL_HOSTS" >>$INSTALL_CONF
}


EchoBasicSrvIPAddr()
{
    str="ENABLE_REDIS_SENTINEL="
    value=`cat $INSTALL_CONF | grep -w ENABLE_REDIS_SENTINEL | awk -F'=' '{ print $2 }'`
    echo $str$value

    str="SENTINEL_HOSTS="
    value=`cat $INSTALL_CONF | grep -w SENTINEL_HOSTS | awk -F'=' '{ print $2 }'`
    echo $str$value

}
#再确定redis、mysql、etcd、nsqlookupd等组件的内网ip信息
if [ -f $INSTALL_CONF ];then
    EchoBasicSrvIPAddr
    ENABLE_REDIS_SENTINEL=`cat $INSTALL_CONF | grep -w ENABLE_REDIS_SENTINEL | awk -F'=' '{ print $2 }'`
    SENTINEL_HOSTS=`cat $INSTALL_CONF | grep -w SENTINEL_HOSTS | awk -F'=' '{ print $2 }'`
    yellow "please comfirm the config information is ok? Enter 1/0(1 means ok, 0 means no):"
    read yes;
    if [ $yes -eq 0 ];then
        EnterBasicSrvIPAddr
    fi
else
    red "Can not found install config $INSTALL_CONF";
    exit
fi

if [ ! -f $APP_REDIS_CONF ];then
    red "Can not found app redis config $APP_REDIS_CONF";
    exit
fi



if [ $ENABLE_REDIS_SENTINEL -eq 1 ];then
    sed -i "s/^.*sentinels=.*/sentinels=${SENTINEL_HOSTS}/g" $APP_REDIS_CONF
else
    sed -i "s/^.*sentinels=.*/sentinels=/g" $APP_REDIS_CONF
fi
