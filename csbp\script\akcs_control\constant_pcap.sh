#!/bin/bash
ACMD="$1"

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop"
     exit
fi

if [ ! -d /home/<USER>
    mkdir -p /home/<USER>
fi

run_sip_pcap_pid()
{
    pid=`ps -ef |grep tcpdump | grep 5070 | awk '{print $2}' | wc -l`
    echo $pid
}

start_pcap()
{
    pid=$(run_sip_pcap_pid)
    if [ ! $pid -gt 0 ];then
        nohup tcpdump -i any -s0 port 5070  -G 600  -w /home/<USER>/%Y%m%d_%H%M_%S.pcap >/dev/null &
    else
        echo "is already running"
    fi

}
stop_pcap()
{
    pid=`ps -ef |grep tcpdump | grep 5070 | awk '{print $2}'`
    kill -9 $pid
}


case $ACMD in
  start)
        start_pcap
    ;;
  stop)
        stop_pcap
    ;;
  restart)
        stop_pcap
        start_pcap
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop"
    ;;
esac
exit

