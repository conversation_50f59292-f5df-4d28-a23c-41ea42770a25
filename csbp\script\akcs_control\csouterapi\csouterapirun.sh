#!/bin/bash
PROCESS_NAME=csouterapi
PROCESS_START_CMD="/usr/local/akcs/csouterapi/scripts/csouterapictl.sh start"
PROCESS_PID_FILE=/var/run/csouterapi.pid
LOG_FILE=/var/log/csouterapi_run_daemon.log
PROCESS_COMMON_SCRIPTS="/usr/local/akcs/csouterapi/scripts/common.sh"
LOG_BACK_SCRIPTS="/usr/local/akcs/csouterapi/scripts/log_back.sh"
csouterapilog_path="/var/log/csouterapilog"

#一定要是绝对路径，不然就变成要指定目录执行这个run
source $PROCESS_COMMON_SCRIPTS
source $LOG_BACK_SCRIPTS

times=1
while [ 1 ]
do
    common_run_pid_detect $PROCESS_NAME $PROCESS_PID_FILE "$PROCESS_START_CMD" $LOG_FILE
    COMMON_FIRST_RUN=0
	sleep 5
	let times+=1
    if [ $times -gt 120 ];then
       times=1
       common_log_back $csouterapilog_path
    fi
done
