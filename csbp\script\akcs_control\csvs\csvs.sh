#!/bin/bash
ACMD="$1"
CSVS_BIN='/usr/local/akcs/csvs/bin/csvs'

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_csvs()
{
    nohup $CSVS_BIN >/dev/null 2>&1 &
    echo "Start csvs successful"
}
stop_csvs()
{
    echo "Begin to stop csvs"
    kill -9 `pidof csvs`
    sleep 2
    echo "Stop csvs successful"
}

case $ACMD in
  start)
    cnt=`ss -alnp | grep 9001 | grep csvs | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        start_csvs
    else
        echo "csvs is already running"
    fi
    ;;
  stop)
    cnt=`ss -alnp | grep 9001 | grep csvs | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "csvs is already stopping"
    else
        stop_csvs
    fi
    ;;
  restart)
    stop_csvs
    sleep 1
    start_csvs
    ;;
  status)
    cnt=`ss -alnp | grep 9001 | grep csvs | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m csvs is stop!!!\033[0m"
    else
        echo "\033[0;32m csvs is running \033[0m"
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

