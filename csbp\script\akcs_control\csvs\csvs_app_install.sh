#!/bin/bash

AKCS_INSTALL_PATH=/usr/local/
AKCS_RUN_SCRIPT_NAME=csvsrun.sh
AKCS_RUN_SCRIPT=${AKCS_INSTALL_PATH}akcs/scripts/${AKCS_RUN_SCRIPT_NAME}

INSTALL_CONF=/etc/install.conf

WORK_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
PAKCAGES_ROOT=${WORK_DIR}/../../
chmod 777 -R ${PAKCAGES_ROOT}/*
upgrade=0


#read 删除不能用问题
stty erase ^H
font_off(){
	echo -en "\033[0m"
}
blue(){
    echo -e "\033[34m$1\033[0m"
	font_off
}
green(){
    echo -e  "\033[32m$1\033[0m"
	font_off
}
red(){
    echo -e "\033[31m$1\033[0m"
	font_off
}
yellow(){
    echo -e "\033[33m$1\033[0m"
	font_off
}


if [ -f $INSTALL_CONF ];then
    echo -e "\033[34m$1\033[0m"
    cat $INSTALL_CONF
    SERVERIP=`cat $INSTALL_CONF | grep -w SERVERIP | awk -F'=' '{ print $2 }'`
    SERVERIPV6=`cat $INSTALL_CONF | grep -w SERVERIPV6 | awk -F'=' '{ print $2 }'`

    if [ ${SERVERIPV6}"x" = "x" ];then
        IPV6_enable=0
    else
        IPV6_enable=1
    fi


    yellow "please comfirm the message is ok!"
    yellow "Do you want to upgrade your system 1/0: \c"
    read yes;
    if [ $yes -eq 0 ];then
        exit;
    fi
    upgrade=1
else
    blue "Can not found system config, will be reinstall system!"

    #输入IP
    yellow "Enter your server IPV4: \c"
    read SERVERIP;

    echo "$SERVERIP" > /etc/ip

    #输入IP6
    yellow "Enter your server IPV6: \c"
    read SERVERIPV6;

    if [ ${SERVERIPV6}"x" = "x" ];then
        IPV6_enable=0
    else
        IPV6_enable=1
    fi

    #写入文件
    echo "" >$INSTALL_CONF
    echo "SERVERIP=$SERVERIP" >>$INSTALL_CONF
    echo "SERVERIPV6=$SERVERIPV6" >>$INSTALL_CONF
fi

serveriplen=`echo ${#SERVERIP}`

#ip validation, only check the length currently
if [ ${serveriplen} -lt 7 -o ${serveriplen} -gt 15 ]; then
    echo "invalid server ip!!!"
    exit
fi

CheckIPAddr()
{
    echo $1|grep "^[0-9]\{1,3\}\.\([0-9]\{1,3\}\.\)\{2\}[0-9]\{1,3\}$" > /dev/null;
    #IP地址必须为全数字
    if [ $? -ne 0 ]
    then
        return 1
    fi
    ipaddr=$1
    a=`echo $ipaddr|awk -F . '{print $1}'`  #以"."分隔，取出每个列的值
    b=`echo $ipaddr|awk -F . '{print $2}'`
    c=`echo $ipaddr|awk -F . '{print $3}'`
    d=`echo $ipaddr|awk -F . '{print $4}'`
    for num in $a $b $c $d
    do
        if [ $num -gt 255 ] || [ $num -lt 0 ]    #每个数值必须在0-255之间
        then
            return 1
        fi
    done
    return 0
}

scriptpid=`ps -fe|grep "${AKCS_RUN_SCRIPT_NAME}" |grep -v grep |awk '{print $2}'`
if [ -n "${scriptpid}" ];then
	echo "${AKCS_RUN_SCRIPT_NAME} is running at ${scriptpid}, will kill it first."
	kill -kill ${scriptpid}
	sleep 2
fi

echo "stopping services..."
${PAKCAGES_ROOT}akcs/scripts/csvsctl.sh stop

sleep 3

echo "making logs directories..."
mkdir -p /var/log/csvslog

if [ -d /usr/local/akcs/ ]
then
    rm -rf /usr/local/akcs/
fi


echo "copying csvs files..."
mkdir -p /usr/local/akcs/
cp -rf ${PAKCAGES_ROOT}akcs/libs/* /usr/local/lib/
cp -rf ${PAKCAGES_ROOT}akcs/* /usr/local/akcs/
ln -s /usr/local/lib/libglog.so /usr/local/lib/libglog.so.0
ln -s /usr/local/lib/libmysqlclient.so /usr/local/lib/libmysqlclient.so.18

if [ ! -d /home/<USER>/ ]
then
    mkdir -p /home/<USER>/
    chmod -R 777 /home/<USER>/
fi
mysql -h localhost -u root -pAk@56@<EMAIL> < ${PAKCAGES_ROOT}akcs/sql/csvs.sql

#add run script to rc.local
if [ -z "`grep "${AKCS_RUN_SCRIPT}" /etc/init.d/rc.local`" ];then
	echo "bash ${AKCS_RUN_SCRIPT} &" >> /etc/init.d/rc.local
fi

chmod 777 -R /usr/local/akcs/*

#执行性能监控cron添加脚本
bash /usr/local/akcs/scripts/csvs_performance_cron.sh  >/dev/null 2>&1

echo "starting services..."
chmod 777 /usr/local/akcs/scripts/csvsctl.sh
/usr/local/akcs/scripts/csvsctl.sh start

sleep 1
chmod 777 ${AKCS_RUN_SCRIPT}
if [ -z "`ps -fe|grep "${AKCS_RUN_SCRIPT_NAME}" |grep -v grep`" ];then
	nohup ${AKCS_RUN_SCRIPT} >/dev/null 2>&1 &
fi

sleep 2
echo "csvs install completed ..."

#printf status
/usr/local/akcs/scripts/csvsctl.sh status
