#!/bin/bash
csvslog_path="/var/log/csvslog"
nginxlog_path="/var/log/nginx"

DAY=`date +"%Y-%m-%d %H:%M:%S"`
H24=`date "+%H"`
S30=`date "+%S"`

y1=`date "+%Y"`
m1=`date "+%m"`
d1=`date "+%d"`
H1=`date "+%H"`
M1=`date "+%M"`
S1=`date "+%S"`


csvs_log_back(){
    cd $csvslog_path || exit 1
    if [ `ls | grep INFO | wc -l |  tr -cd "[0-9]"` -gt 3 ]; then
    #保存最后3个
       ls -lt | grep INFO | tail -$((`ls | grep INFO | wc -l |  tr -cd "[0-9]"` - 3)) | awk -F " " '{print $9}' | xargs rm -rf {}
    fi

    if [ `ls | grep ERROR | wc -l |  tr -cd "[0-9]"` -gt 5 ]; then
    #保存最后5个
       ls -lt | grep ERROR | tail -$((`ls | grep ERROR | wc -l |  tr -cd "[0-9]"` - 5)) | awk -F " " '{print $9}' | xargs rm -rf {}
    fi

    if [ `ls | grep WARN | wc -l |  tr -cd "[0-9]"` -gt 5 ]; then
    #保存最后5个
       ls -lt | grep WARN | tail -$((`ls | grep WARN | wc -l |  tr -cd "[0-9]"` - 5)) | awk -F " " '{print $9}' | xargs rm -rf {}
    fi
}

nginx_log_back(){
    cd $nginxlog_path || exit 1
    if [ `du -sk logs | awk '{print $1}'` -gt 10240 ]; then
        DESFILE="logs-$y1-$m1-$d1_$H1-$M1-$S1.tar.gz"
        tar zvcf $DESFILE logs
        echo > logs/web_access.log
        echo > logs/web_error.log
    fi

    #删除过期的压缩日志文件
    num=`ls -t *tar.gz | wc -l |  tr -cd "[0-9]"`
    if [ $num -gt 5 ]; then
    #保存最后5个
       num=$(($num - 5))
       ls -t *tar.gz | tail -n$num | xargs rm -rf {}
    fi
}

csvs_log_back
nginx_log_back
