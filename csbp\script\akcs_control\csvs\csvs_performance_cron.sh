#!/bin/bash


#将日志清理脚本、数据库备份脚本加入到定时器中,定时执行一次动作
if [ -z "`grep "/usr/local/akcs/scripts/akcs_log_backup.sh" /var/spool/cron/crontabs/root`" ];then
	echo "*/10 * * * * /bin/bash  /usr/local/akcs/scripts/akcs_log_backup.sh >/dev/null 2>&1" >> /var/spool/cron/crontabs/root
fi
if [ -z "`grep "/usr/local/akcs/scripts/akcs_mysql_data_backup.sh" /var/spool/cron/crontabs/root`" ];then
	echo "00 23 * * * /bin/bash  /usr/local/akcs/scripts/akcs_mysql_data_backup.sh >/dev/null 2>&1" >> /var/spool/cron/crontabs/root
fi

if [ -z "`grep "/usr/local/akcs/scripts/performance_record.sh" /var/spool/cron/crontabs/root`" ];then
	echo "*/30 * * * * /bin/bash  /usr/local/akcs/scripts/performance_record.sh >/dev/null 2>&1" >> /var/spool/cron/crontabs/root
fi

if [ -z "`grep "/usr/local/akcs/scripts/performance_warning.sh" /var/spool/cron/crontabs/root`" ];then
	echo "*/10 * * * * /bin/bash  /usr/local/akcs/scripts/performance_warning.sh >/dev/null 2>&1 " >> /var/spool/cron/crontabs/root
fi

if [ -z "`grep "/usr/local/akcs/scripts/performance_mail.sh" /var/spool/cron/crontabs/root`" ];then
	echo "0 8 * * * /bin/bash  /usr/local/akcs/scripts/performance_mail.sh >/dev/null 2>&1 " >> /var/spool/cron/crontabs/root
fi

if [ -z "`grep "/usr/local/akcs/scripts/performance_record_cleanup.sh" /var/spool/cron/crontabs/root`" ];then
	echo "0 0 * * 0 /bin/bash  /usr/local/akcs/scripts/performance_record_cleanup.sh >/dev/null 2>&1 " >> /var/spool/cron/crontabs/root
fi

if [ -z "`grep "/usr/local/akcs/scripts/akcs_account_statistics.php" /var/spool/cron/crontabs/root`" ];then
	echo "58 7 * * * /usr/local/bin/php /usr/local/akcs/scripts/akcs_account_statistics.php >/dev/null 2>&1 " >> /var/spool/cron/crontabs/root
fi

#凌晨5分 试用期剩三天提醒
if [ -z "`grep "/usr/local/akcs/scripts/check_account_freetrial.php" /var/spool/cron/crontabs/root`" ];then
	echo "5 0 * * * /usr/local/bin/php /usr/local/akcs/scripts/check_account_freetrial.php >/dev/null 2>&1 " >> /var/spool/cron/crontabs/root
fi

#凌晨10分 设备app剩三天提醒
if [ -z "`grep "/usr/local/akcs/scripts/check_dev_willbe_expire.php" /var/spool/cron/crontabs/root`" ];then
	echo "10 0 * * * /usr/local/bin/php /usr/local/akcs/scripts/check_dev_willbe_expire.php >/dev/null 2>&1 " >> /var/spool/cron/crontabs/root
fi

#凌晨15分 设备过期检测
if [ -z "`grep "/usr/local/akcs/scripts/check_dev_expire.php" /var/spool/cron/crontabs/root`" ];then
	echo "15 0 * * * /usr/local/bin/php /usr/local/akcs/scripts/check_dev_expire.php >/dev/null 2>&1 " >> /var/spool/cron/crontabs/root
fi

if [ -z "`grep "/usr/local/akcs/scripts/clearCapture.sh" /var/spool/cron/crontabs/root`" ];then
	echo "20 0 * * * /bin/bash /usr/local/akcs/scripts/clearCapture.sh >/dev/null 2>&1 " >> /var/spool/cron/crontabs/root
fi

#强制改成7.50因为8点发邮件可能来不及
sed -i '/akcs_account_statistics.php/d' var/spool/cron/crontabs/root
if [ -z "`grep "/usr/local/akcs/scripts/akcs_account_statistics.php" /var/spool/cron/crontabs/root`" ];then
	echo "50 7 * * * /usr/local/bin/php /usr/local/akcs/scripts/akcs_account_statistics.php >/dev/null 2>&1 " >> /var/spool/cron/crontabs/root
fi

sleep 1
chmod 0600 /var/spool/cron/crontabs/root
service cron restart


