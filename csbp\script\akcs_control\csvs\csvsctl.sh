#!/bin/bash
ACMD="$1"
WORK_DIR="/usr/local/akcs/csvs/scripts"

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_csvs()
{

    sh ${WORK_DIR}/csvs.sh start
}
stop_csvs()
{
    sh ${WORK_DIR}/csvs.sh stop
}

status_csvs()
{
    sh ${WORK_DIR}/csvs.sh status
}

case $ACMD in
  start)
        start_csvs
    ;;
  stop)
        stop_csvs
    ;;
  restart)
    stop_csvs
    sleep 3
    start_csvs
    ;;
  status)
    status_csvs
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

