#!/bin/bash

PROCESS_CSVS_NAME=csvs
PROCESS_CSVS_PATH=/usr/local/akcs/csvs/csvs.sh

LOG_FILE=/var/log/csvs_run_daemon.log

FIRST_RUN=1 #第一次启动这个程序 不需要通知运维。

. /etc/profile #开启core文件
SERVERIP=`cat /etc/ip`

CONF_FILE=/usr/local/akcs/scripts/notify_email.conf
MAIL_LIST=`cat $CONF_FILE | grep APP_STOP_EMAIL_LIST | awk -F'=' '{ print $2 }'`


EMAIL_TIMES=300

app_stop_email()
{
    email=0
    if [ -f $DETECTFILE_ROOT/.$1* ];then
        time=`ls /tmp/.$1* | awk -F '_' '{print $2}'`
        unix=`date +%s`
        let time=$time+$EMAIL_TIMES
        if [ $time -lt $unix ];then
            #报警  重新计算时间
            rm  $DETECTFILE_ROOT/.$1*
            touch $DETECTFILE_ROOT/.$1_`date +%s`
            email=1
        fi
    else
        touch $DETECTFILE_ROOT/.$1_`date +%s`
        email=1
    fi
    if [ $email -eq 1 ];then
        processname=$1
        echo "${processname} is stopped，请及时排查原因。\nIP:${SERVERIP}" | mutt -s "应用程序停止警告"  ${MAIL_LIST}
    fi
}

run() {
	processname=$1
    processpath=$2
	ps -fe|grep ${processname} |grep -v grep
	if [ $? -ne 0 ]
	then
		date >> $LOG_FILE
        echo "warning !, $1 is stopped..." >> $LOG_FILE
		$processpath start
		if [ $FIRST_RUN -ne 1 ];then
			echo "${processname} stop报警"  >> $LOG_FILE
			app_stop_email $processname
		fi
		sleep 2
	fi

}

while [ 1 ]
do
    run $PROCESS_CSVS_NAME $PROCESS_CSVS_PATH
	FIRST_RUN=0
	sleep 5
done
