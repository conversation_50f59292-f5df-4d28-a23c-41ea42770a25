#!/bin/bash  
MAIL_LIST="-b <EMAIL> -c <EMAIL> -c <EMAIL> -c <EMAIL> -c <EMAIL>"
TXT_PATH="/home"
IP=`cat /etc/ip  | grep 'SERVERIP=' | awk -F '=' '{print $2}'`
#提取当前日期时间  
today=`date -d "0 day" +%Y年%m月%d日`  

if [ -f ${TXT_PATH}/expire-email.txt ];then
	echo "$IP服务器账号过期邮件发送报告，请下载附件进行查看。" | mutt -s "账号过期检测报告$IP" -a ${TXT_PATH}/expire-email.txt ${MAIL_LIST}
	sleep 1
	rm ${TXT_PATH}/expire-email.txt
fi

