#!/bin/bash
# fastdfs docker安装脚本
# author yicong.chen

#处理docker
if [ ! -x /usr/local/bin/docker ]
then
    sudo apt-get update
    sudo apt-get install docker.io -y
    ln -s /usr/bin/docker.io /usr/local/bin/docker
    service docker restart
fi

#还原docker容器,并解压对应宿主机上的配置文件
fastdfs_extract()
{
    if [ -d /home/<USER>
    then
        echo "fdfs is exist,begin to load docker of fdfs_storage and fdfs_tracker"
    else
        mkdir -p /home/<USER>
        chmod 755 -R /home/<USER>

        mkdir -p /home/<USER>
        chmod 755 -R /home/<USER>
    fi

    cd /usr/local/fastdfs/ || exit 1
    #moidified by chenyc 2018-06-11, change v32 to v33 for fastdfs-nginx ipv6
    docker load < ./fdfs_storage_v33_image.tar
    docker load < ./fdfs_tracker_v33_image.tar
    docker tag e02ad5db1ba0 tracker:v3.3
    docker tag 451d86f53b65 storage:v3.3
    if [ -d /etc/fdfs ]
    then
        rm -rf /etc/fdfs
    fi
    mkdir -p /etc/fdfs
    cp -rf /usr/local/fastdfs/fdfs/*  /etc/fdfs
}

run_fdfs_trackerd()
{
    #先确定是否有fdfs_trackerd容器
    cnt=`docker ps -a| grep tracker | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        #moidified by chenyc 2018-06-11, change v32 to v33 for fastdfs-nginx ipv6
        docker run -itd --restart=always --name tracker --net=host -v /etc/fdfs:/etc/fdfs -v /etc/fdfs/nginx_conf/tracker:/usr/local/nginx/conf -v /home/<USER>/home/<USER>
    else
        echo "fastdfs tracker docker container is exist"
    fi
    docker restart tracker
    sleep 1
    docker exec tracker fdfs_trackerd /etc/fdfs/tracker.conf
    docker exec tracker /usr/local/nginx/sbin/nginx
}
run_fdfs_storaged()
{
    #先确定是否有fdfs_trackerd容器
    cnt=`docker ps -a| grep storage | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        #moidified by chenyc 2018-06-11, change v32 to v33 for fastdfs-nginx ipv6
        docker run -itd --restart=always --name storage --net=host -v /etc/fdfs:/etc/fdfs -v /etc/fdfs/nginx_conf/storage:/usr/local/nginx/conf -v /home/<USER>/home/<USER>
    else
        echo "fastdfs storage docker container is exist"
    fi
    docker restart storage
    sleep 1
    docker exec storage fdfs_storaged /etc/fdfs/storage.conf
    docker exec storage /usr/local/nginx/sbin/nginx
    sleep 2
    ln -s /home/<USER>/data  /home/<USER>/data/M00
    #added by chenyc,2018-03-05 解决nginx浏览图片提示无法访问的问题
    if [ -d /home/<USER>/mod_fdfs_logs ]
    then
        echo '/home/<USER>/mod_fdfs_logs already exit'
    else
        mkdir /home/<USER>/mod_fdfs_logs
    fi
    chmod -R 777 /home/<USER>/mod_fdfs_logs
    docker exec storage /usr/local/nginx/sbin/nginx -s reload
}

#fastdfs存储服务
#先确定是否有fastdfs容器
cnt=`docker ps -a| grep storage | wc -l`
if [ "$cnt" -eq "0" ]
then
    fastdfs_extract
else
    echo "fastdfs docker container is exist"
fi

#再确定是否有fastdfs容器是否运行
cnt=`ss -alnp | grep 22122 | grep LISTEN | wc -l`
if [ "$cnt" -eq "0" ]
then
    run_fdfs_trackerd
else
    echo "fdfs_trackerd is running"
fi

cnt=`ss -alnp | grep 23000 | grep LISTEN | wc -l`
if [ "$cnt" -eq "0" ]
then
    run_fdfs_storaged
else
    echo "fdfs_storaged is running"
fi

#处理外部配置文件相关
