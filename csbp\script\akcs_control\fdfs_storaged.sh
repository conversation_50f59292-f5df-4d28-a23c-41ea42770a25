#!/bin/bash
ACMD="$1"
STORAGED_BIN='docker exec storage fdfs_storaged /etc/fdfs/storage.conf'
STORAGED_NGINX='docker exec storage /usr/local/nginx/sbin/nginx'

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_storaged()
{
    nohup $STORAGED_BIN >/dev/null 2>&1 &
    echo "Start fdfs_storaged successful"
}
stop_storaged()
{
    echo "Begin to stop fdfs_storaged"
    kill -9 `pidof fdfs_storaged`
    sleep 2
    echo "Stop fdfs_storaged successful"
}

case $ACMD in
  start)
    cnt=`ss -alnp | grep fdfs_storaged | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        start_storaged
    else
        echo "fdfs_storaged is already running"
    fi
    ;;
  stop)
    cnt=`ss -alnp | grep fdfs_storaged | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "fdfs_storaged is already stopping"
    else
        stop_storaged
    fi
    ;;
  restart)
    stop_storaged
    sleep 1
    start_storaged
    ;;
  status)
    cnt=`ss -alnp | grep fdfs_storaged | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m fdfs_storaged is stop!!!\033[0m"
    else
        echo "\033[0;32m fdfs_storaged is running \033[0m"
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

