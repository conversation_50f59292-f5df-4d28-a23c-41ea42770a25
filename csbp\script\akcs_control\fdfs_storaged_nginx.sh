#!/bin/bash
ACMD="$1"
STORAGED_NGINX='docker exec storage /usr/local/nginx/sbin/nginx'

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_storaged_nginx()
{
    nohup $STORAGED_NGINX >/dev/null 2>&1 &
    echo "Start fdfs_storaged_nginx successful"
}
stop_storaged_nginx()
{
    echo "Begin to stop fdfs_storaged_nginx"
    docker exec storage /usr/local/nginx/sbin/nginx -s stop
    sleep 2
    echo "Stop fdfs_storaged_nginx successful"
}

case $ACMD in
  start)
    cnt=`ss -alnp | grep 8888 | grep LISTEN | grep nginx | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        start_storaged_nginx
    else
        echo "fdfs_storaged_nginx is already running"
    fi
    ;;
  stop)
    cnt=`ss -alnp | grep 8888 | grep LISTEN | grep nginx | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "fdfs_storaged_nginx is already stopping"
    else
        stop_storaged_nginx
    fi
    ;;
  restart)
    stop_storaged_nginx
    sleep 1
    start_storaged_nginx
    ;;
  status)
    cnt=`ss -alnp | grep 8888 | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m fdfs_storaged_nginx is stop!!!\033[0m"
    else
        echo "\033[0;32m fdfs_storaged_nginx is running \033[0m"
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

