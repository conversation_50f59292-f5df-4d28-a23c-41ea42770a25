#!/bin/bash
ACMD="$1"
TRACKER_BIN='docker exec tracker fdfs_trackerd /etc/fdfs/tracker.conf'
TRACKER_NGINX='docker exec tracker /usr/local/nginx/sbin/nginx'

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_trackerd()
{
    nohup $TRACKER_BIN >/dev/null 2>&1 &
    echo "Start fdfs_trackerd successful"
}
stop_trackerd()
{
    echo "Begin to stop fdfs_trackerd"
    kill -9 `pidof fdfs_trackerd`
    sleep 2
    echo "Stop fdfs_trackerd successful"
}

case $ACMD in
  start)
    cnt=`ss -alnp | grep fdfs_trackerd | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        start_trackerd
    else
        echo "fdfs_trackerd is already running"
    fi
    ;;
  stop)
    cnt=`ss -alnp | grep fdfs_trackerd | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "fdfs_trackerd is already stopping"
    else
        stop_trackerd
    fi
    ;;
  restart)
    stop_trackerd
    sleep 1
    start_trackerd
    ;;
  status)
    cnt=`ss -alnp | grep fdfs_trackerd | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m fdfs_trackerd is stop!!!\033[0m"
    else
        echo "\033[0;32m fdfs_trackerd is running \033[0m"
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

