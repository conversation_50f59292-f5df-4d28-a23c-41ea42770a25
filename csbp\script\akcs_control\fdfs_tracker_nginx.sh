#!/bin/bash
ACMD="$1"
TRACKER_NGINX='docker exec tracker /usr/local/nginx/sbin/nginx'

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_tracker_nginx()
{
    nohup docker restart tracker >/dev/null 2>&1 &
    nohup $TRACKER_NGINX >/dev/null 2>&1 &
    echo "Start fdfs_trackered_nginx successful"
}
stop_tracker_nginx()
{
    echo "Begin to stop fdfs_trackered_nginx"
    docker exec tracker /usr/local/nginx/sbin/nginx -s stop
    sleep 2
    echo "Stop fdfs_trackered_nginx successful"
}

case $ACMD in
  start)
    cnt=`ss -alnp | grep 8090 | grep LISTEN | grep nginx | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        start_tracker_nginx
    else
        echo "fdfs_trackered_nginx is already running"
    fi
    ;;
  stop)
    cnt=`ss -alnp | grep 8090 | grep LISTEN | grep nginx | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "fdfs_trackered_nginx is already stopping"
    else
        stop_tracker_nginx
    fi
    ;;
  restart)
    stop_tracker_nginx
    sleep 1
    start_tracker_nginx
    ;;
  status)
    cnt=`ss -alnp | grep 8090 | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m fdfs_trackered_nginx is stop!!!\033[0m"
    else
        echo "\033[0;32m fdfs_trackered_nginx is running \033[0m"
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

