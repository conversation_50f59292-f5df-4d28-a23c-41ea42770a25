import os
import time
from concurrent.futures import Thread<PERSON>oolExecutor

def upload_file(base_filename, filename_add):
    cmd = [
        "curl",
        "-u", "akuvox:pu6HYKvTkyGstq",
        "-T", base_filename,
        "ftp://39.108.105.163/{}".format(filename_add)
    ]
    
    result = os.system(" ".join(cmd))
    return result

def upload_in_parallel(base_filename, num_clients, num_files):
    with ThreadPoolExecutor(max_workers=num_clients) as executor:
        futures = []
        for i in range(num_files):
            filename_add = "{}_{}.jpg".format(base_filename, i)
            filename = "{}.jpg".format(base_filename)
            future = executor.submit(upload_file, filename, filename_add)
            futures.append(future)
        
        # 等待所有任务完成
        for future in futures:
            future.result()

if __name__ == "__main__":
    base_filename = "CCCC00000048-1690177750_0_Dev_2e74e60fb6dc0c2364d67d406e9039c8"
    num_clients = 50
    num_files = 50  # 假设要上传50个不同的文件

    while True:
        upload_in_parallel(base_filename, num_clients, num_files)
        time.sleep(1)  # 暂停2秒后再次执行
