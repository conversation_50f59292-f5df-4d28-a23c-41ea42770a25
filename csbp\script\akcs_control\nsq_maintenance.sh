#!/bin/bash

# Set timezone
export TZ=Asia/Shanghai

HOST_IP=/etc/ip
SERVER_INNER_IP=`cat $HOST_IP | grep -w SERVER_INNER_IP | awk -F'=' '{ print $2 }'`

# Define functions
cmd_usage() {
  echo "usage: $0 stats <host_ip>"
  echo "       $0 clean <host_ip> <topic> [channel]"
  exit 0
}

# Check argument count
if [[ $# -lt 1 ]]; then
  cmd_usage
fi

# Check command
if [[ "$1" == "stats" ]]; then
  sever_ip=$2
  echo "curl $sever_ip:8513/stats"
  curl $sever_ip:8513/stats
elif [[ "$1" == "clean" ]]; then
  server_ip=$2
  topic=$3
  if [[ $# -eq 4 ]]; then
    channel=$4
    echo "curl -X POST $server_ip:8513/channel/empty?topic=$topic&channel=$channel"
    curl -X POST $server_ip:8513/channel/empty?topic=$topic&channel=$channel
  else
    echo "curl -X POST $server_ip:8513/topic/empty?topic=$topic"
    curl -X POST $server_ip:8513/topic/empty?topic=$topic
  fi
else
  cmd_usage
fi
