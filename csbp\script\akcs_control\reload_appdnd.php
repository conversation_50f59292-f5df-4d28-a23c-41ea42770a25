<?php
//将mysql中的appDND信息刷到redis中

const db_ip = "127.0.0.1";//master db ip
const redis_ip = "127.0.0.1";//master redis ip

const redis_master_port = 8504;//redis 端口
const redis_slave_port = 8505;//redis 端口
const redis_passwd = "Akcs#xm2610*";//redis 密码

if (strstr(db_ip, "127.0.0.1") || strstr(redis_ip, "127.0.0.1"))
{
    echo "please change script db_ip and redis_ip\n";
    exit(1);
}

function getDB()
{
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbip = db_ip;
    $dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbip;port=$dbport;dbname=AKCS";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

$db = getDB();
$sth = $db->prepare("select Status, StartTime, EndTime, Account from AppCallDND");
$sth->execute();
$data = $sth->fetchAll(PDO::FETCH_ASSOC);

$redis = new Redis();
$ret = $redis->connect(redis_ip, redis_master_port);
if ($ret)
{
     $redis->auth(redis_passwd);
}
$redis->select(3);

foreach($data as $key => $value){
    $status = $value['Status'];
    $startMinutes = $value['StartTime'];
    $endMinutes = $value['EndTime'];
    $account = $value['Account'];
    $dndData = $status . "-" . $startMinutes . "-" . $endMinutes;
    $redis->hset($account, "dndinfo", $dndData);
}
















