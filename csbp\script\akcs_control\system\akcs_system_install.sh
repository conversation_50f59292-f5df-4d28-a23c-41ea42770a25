#!/bin/bash

#ssytem安装脚本,主要是sql文件操作，以及mysql nginx php等基础组件配置文件的更新
AKCS_INSTALL_PATH=/usr/local/akcs
AKCS_RUN_SCRIPT_NAME=systemrun.sh
AKCS_RUN_SCRIPT=${AKCS_INSTALL_PATH}/scripts/${AKCS_RUN_SCRIPT_NAME}

WORK_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
PAKCAGES_ROOT=${WORK_DIR}/../..
chmod 777 -R ${PAKCAGES_ROOT}/*
INSTALL_CONF=/etc/system_install.conf
HOST_IP=/etc/ip

#read 删除不能用问题
stty erase ^H
font_off(){
	echo -en "\033[0m"
}
blue(){
    echo -e "\033[34m$1\033[0m"
	font_off
}
green(){
    echo -e  "\033[32m$1\033[0m"
	font_off
}
red(){
    echo -e "\033[31m$1\033[0m"
	font_off
}
yellow(){
    echo -e "\033[33m$1\033[0m"
	font_off
}
CheckIPAddr()
{
    echo $1|grep "^[0-9]\{1,3\}\.\([0-9]\{1,3\}\.\)\{2\}[0-9]\{1,3\}$" > /dev/null; 
    #IP地址必须为全数字 
    if [ $? -ne 0 ] 
    then 
        return 1 
    fi 
    ipaddr=$1 
    a=`echo $ipaddr|awk -F . '{print $1}'`  #以"."分隔，取出每个列的值 
    b=`echo $ipaddr|awk -F . '{print $2}'` 
    c=`echo $ipaddr|awk -F . '{print $3}'` 
    d=`echo $ipaddr|awk -F . '{print $4}'` 
    for num in $a $b $c $d 
    do 
        if [ $num -gt 255 ] || [ $num -lt 0 ]    #每个数值必须在0-255之间 
        then 
            return 1 
        fi 
    done 
    return 0 
} 
EchoHostIPAddr()
{
    echo -e "\033[34m$1\033[0m"
    inner_ip_str="SERVER_INNER_IP="
    inner_ip_cat=`cat $HOST_IP | grep -w SERVER_INNER_IP | awk -F'=' '{ print $2 }'`
    inner_ip=$inner_ip_str$inner_ip_cat
    echo $inner_ip
    
    outer_ipv4_str="SERVERIP="
    outer_ipv4_cat=`cat $HOST_IP | grep -w SERVERIP | awk -F'=' '{ print $2 }'`
    outer_ipv4=$outer_ipv4_str$outer_ipv4_cat
    echo $outer_ipv4
        
    outer_ipv6_str="SERVERIPV6="
    outer_ipv6_cat=`cat $HOST_IP | grep -w SERVERIPV6 | awk -F'=' '{ print $2 }'`
    outer_ipv6=$outer_ipv6_str$outer_ipv6_cat
    echo $outer_ipv6
}
EnterHostIPAddr()
{
   #输入内网IP
    yellow "Enter your host server inner IPV4: \c"
    #不能写成这样:read $SERVER_INNER_IP;
    read SERVER_INNER_IP;

    #输入外网IP
    yellow "Enter your host server outer IPV4: \c"
    read SERVERIP;

    #输入IP6
    yellow "Enter your host server IPV6: \c"
    read SERVERIPV6; 
        
    for ip in $SERVER_INNER_IP $SERVERIP ; do
      CheckIPAddr $ip
      if [ $? -eq 0 ]; then
        echo "$ip is valid"
      else
        echo "$ip is invalid, exit"
        exit 1;
      fi
    done
    #写入主机的IP文件
    echo "" >$HOST_IP
    echo "SERVER_INNER_IP=$SERVER_INNER_IP" >>$HOST_IP
    echo "SERVERIP=$SERVERIP" >>$HOST_IP
    echo "SERVERIPV6=$SERVERIPV6" >>$HOST_IP
}

EnterBasicSrvIPAddr()
{
    yellow "Enter your fdfs server inner IPV4: \c"
    read FDFS_INNER_IP;      
    
    #输入mysql内网IP
    yellow "Enter your mysql server inner IPV4: \c"
    read MYSQL_INNER_IP;

    #输入redis内网IP
    yellow "Enter your redis server inner IPV4: \c"
    read REDIS_INNER_IP;    
    
	
    #输入全局的macpool
    yellow "Enter your Global Mac Pool outer IPV4(IP:Port): \c"
    read GLOBAL_MAC_POOL_IP;    
    for ip in $FDFS_INNER_IP ; do
      CheckIPAddr $ip
      if [ $? -eq 0 ]; then
        echo "$ip is valid"
      else
        echo "$ip is invalid, exit"
        exit 1;
      fi
    done
    #写入基础服务的IP文件
    echo "" >$INSTALL_CONF
    echo "FDFS_INNER_IP=$FDFS_INNER_IP" >>$INSTALL_CONF
    echo "MYSQL_INNER_IP=$MYSQL_INNER_IP" >>$INSTALL_CONF
    echo "REDIS_INNER_IP=$REDIS_INNER_IP" >>$INSTALL_CONF
	echo "GLOBAL_MAC_POOL_IP=$GLOBAL_MAC_POOL_IP" >>$INSTALL_CONF

}
function Md5sumCheck()
{
	newfile=$1
	oldfile=$2
	newmd5=`md5sum $newfile|awk '{print $1}'`
	oldmd5=`md5sum $oldfile|awk '{print $1}'`
	if [ $oldmd5 != $newmd5 ];then
	echo "md5sum check error!"
	echo "$oldfile install failed!"
	exit 0
	
	fi
}

if [ -f $HOST_IP ];then
    EchoHostIPAddr
    SERVER_INNER_IP=`cat $HOST_IP | grep -w SERVER_INNER_IP | awk -F'=' '{ print $2 }'`
    SERVERIP=`cat $HOST_IP | grep -w SERVERIP | awk -F'=' '{ print $2 }'`
    SERVERIPV6=`cat $HOST_IP | grep -w SERVERIPV6 | awk -F'=' '{ print $2 }'`
    
    yellow "please comfirm the host ip information is ok(host ip must contain inner ip and outer ipv4, outer ipv6 is an option.)? Enter 1/0(1 means ok, 0 means no):"
    read yes;
    if [ $yes -eq 0 ];then
        EnterHostIPAddr
    fi
else
    blue "Can not found host ip file </etc/ip>, please enter all information below:"
    EnterHostIPAddr
fi
EchoBasicSrvIPAddr()
{
    echo -e "\033[34m$1\033[0m"
    
    fdfs_inner_ip_str="FDFS_INNER_IP="
    fdfs_inner_ip_cat=`cat $INSTALL_CONF | grep -w FDFS_INNER_IP | awk -F'=' '{ print $2 }'`
    fdfs_inner_ip=$fdfs_inner_ip_str$fdfs_inner_ip_cat
    echo $fdfs_inner_ip

    mysql_inner_ip_str="MYSQL_INNER_IP="
    mysql_inner_ip_cat=`cat $INSTALL_CONF | grep -w MYSQL_INNER_IP | awk -F'=' '{ print $2 }'`
    mysql_inner_ip=$mysql_inner_ip_str$mysql_inner_ip_cat
    echo $mysql_inner_ip

    redis_inner_ip_str="REDIS_INNER_IP="
    redis_inner_ip_cat=`cat $INSTALL_CONF | grep -w REDIS_INNER_IP | awk -F'=' '{ print $2 }'`
    redis_inner_ip=$redis_inner_ip_str$redis_inner_ip_cat
    echo $redis_inner_ip    

    global_ip_str="GLOBAL_MAC_POOL_IP="
    global_ip_cat=`cat $INSTALL_CONF | grep -w GLOBAL_MAC_POOL_IP | awk -F'=' '{ print $2 }'`
    global_ip=$global_ip_str$global_ip_cat
    echo $global_ip    	
}
if [ -f $INSTALL_CONF ];then
    echo -e "\033[34m$1\033[0m"
    EchoBasicSrvIPAddr
    FDFS_INNER_IP=`cat $INSTALL_CONF | grep -w FDFS_INNER_IP | awk -F'=' '{ print $2 }'`
    MYSQL_INNER_IP=`cat $INSTALL_CONF | grep -w MYSQL_INNER_IP | awk -F'=' '{ print $2 }'`
    REDIS_INNER_IP=`cat $INSTALL_CONF | grep -w REDIS_INNER_IP | awk -F'=' '{ print $2 }'`
	GLOBAL_MAC_POOL_IP=`cat $INSTALL_CONF | grep -w GLOBAL_MAC_POOL_IP | awk -F'=' '{ print $2 }'`
    yellow "please comfirm the basic server inner ip information is ok? Enter 1/0(1 means ok, 0 means no):"
    read yes;
    if [ $yes -eq 0 ];then
        EnterBasicSrvIPAddr
    fi
else
    blue "Can not found system config </etc/sytem_install.conf>, please enter all information below:" 
    EnterBasicSrvIPAddr
fi

#杀死守护脚本
kill -9 `ps -ef | grep ${AKCS_RUN_SCRIPT_NAME} | grep -v grep | awk '{print $2}'`

#处理fdfs的配置文件信息
sed -i "s/^bind_addr=.*/bind_addr=${SERVER_INNER_IP}/g" ${PAKCAGES_ROOT}/system/fdfs/tracker.conf
sed -i "s/^bind_addr=.*/bind_addr=${SERVER_INNER_IP}/g" ${PAKCAGES_ROOT}/system/fdfs/storage.conf
sed -i "s/^tracker_server=.*/tracker_server=${SERVER_INNER_IP}:22122/g" ${PAKCAGES_ROOT}/system/fdfs/storage.conf

#scripts mysql相关
bash $WORK_DIR/dbproxy-install.sh $INSTALL_CONF 
ENABLE_DBPROXY=`cat $INSTALL_CONF | grep -w ENABLE_DBPROXY | awk -F'=' '{ print $2 }'`
DBPROXY_INNER_IP=`cat $INSTALL_CONF | grep -w DBPROXY_INNER_IP | awk -F'=' '{ print $2 }'`
if [ $ENABLE_DBPROXY -eq 1 ];then  
    DBPROXY_LINE="\$dbport = 3308;"
    sed -i "s/^.*dbport =.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/system_scripts/check_expire_common_v4500.php
    sed -i "s/^.*dbport =.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/system_scripts/clearCapture2.php
    sed -i "s/^.*dbport =.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/system_scripts/akcs_account_statistics.php
    sed -i "s/^.*dbport =.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/system_scripts/using_data_statistics.php  
    sed -i "s/^.*dbport =.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/system_scripts/clearModel.php
	sed -i "s/^.*dbport =.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/system_scripts/data_check.php

    DBPROXY_LINE="\$dbhost = \"${DBPROXY_INNER_IP}\";"
    sed -i "s/^.*dbhost =.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/system_scripts/check_expire_common_v4500.php
    sed -i "s/^.*dbhost =.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/system_scripts/clearCapture2.php
    sed -i "s/^.*dbhost =.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/system_scripts/akcs_account_statistics.php
    sed -i "s/^.*dbhost =.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/system_scripts/using_data_statistics.php
	sed -i "s/^.*dbhost =.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/system_scripts/clearModel.php
	sed -i "s/^.*dbhost =.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/system_scripts/data_check.php
	
    #sed -i "s/^.*dbhost =.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/system_scripts/sql_update.php     
    
else   
    DBPROXY_LINE="\$dbport = 3306;"
    sed -i "s/^.*dbport =.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/system_scripts/check_expire_common_v4500.php
    sed -i "s/^.*dbport =.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/system_scripts/clearCapture2.php
    sed -i "s/^.*dbport =.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/system_scripts/akcs_account_statistics.php
    sed -i "s/^.*dbport =.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/system_scripts/using_data_statistics.php
	sed -i "s/^.*dbport =.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/system_scripts/clearModel.php
	sed -i "s/^.*dbport =.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/system_scripts/data_check.php
    
    DBPROXY_LINE="\$dbhost = \"$MYSQL_INNER_IP\";"
    sed -i "s/^.*dbhost =.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/system_scripts/check_expire_common_v4500.php
    sed -i "s/^.*dbhost =.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/system_scripts/clearCapture2.php
    sed -i "s/^.*dbhost =.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/system_scripts/akcs_account_statistics.php
    sed -i "s/^.*dbhost =.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/system_scripts/using_data_statistics.php
	
    sed -i "s/^.*dbhost =.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/system_scripts/clearModel.php
	sed -i "s/^.*dbhost =.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/system_scripts/data_check.php
    #sed -i "s/^.*dbhost =.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/system_scripts/sql_update.php           
fi
DBPROXY_LINE="\$dbhost = \"$MYSQL_INNER_IP\";"
sed -i "s/^.*dbhost =.*/${DBPROXY_LINE}/g" ${PAKCAGES_ROOT}/system_scripts/landline_notify.php

#将所有脚本全部拷贝过去
if [ ! -d /usr/local/akcs/scripts/ ];then
    mkdir -p /usr/local/akcs/scripts
fi
cp -rf ${PAKCAGES_ROOT}/system_scripts/* /usr/local/akcs/scripts/
chmod 777 -R /usr/local/akcs/scripts/


#执行添加cron脚本
bash /usr/local/akcs/scripts/akcs_performance_cron.sh  >/dev/null 2>&1

#先更新配置文件
cd ${PAKCAGES_ROOT}/system
sh upgrade.sh

if [ ! -d /var/log/redis/ ]
then
    mkdir -p /var/log/redis
    chmod 777 -R /var/log/redis
fi

#copy systemrun
cp -rf ${PAKCAGES_ROOT}/system_scripts/system/systemrun.sh /usr/local/akcs/scripts/
cp -rf ${PAKCAGES_ROOT}/system_scripts/system/common.sh /usr/local/akcs/scripts/

#add run script to rc.local
if [ -z "`grep "${AKCS_RUN_SCRIPT}" /etc/init.d/rc.local`" ];then
	echo "bash ${AKCS_RUN_SCRIPT} &" >> /etc/init.d/rc.local
fi

#启动守护脚本
chmod 777 ${AKCS_RUN_SCRIPT}
if [ -z "`ps -fe|grep "${AKCS_RUN_SCRIPT_NAME}" |grep -v grep`" ];then
	nohup bash ${AKCS_RUN_SCRIPT} >/dev/null 2>&1 &
fi

#拉取mac信息
GLOBAL_OUTER_IP_LINE="const GLOBAL_MACPOOL_IP = \"${GLOBAL_MAC_POOL_IP}\";"
sed -i "s/^.*const GLOBAL_MACPOOL_IP.*/${GLOBAL_OUTER_IP_LINE}/g" /usr/local/akcs/scripts/get_acks_macpool.php
DB_LINE="\$dbhost = \"$MYSQL_INNER_IP\";"
sed -i "s/^.*dbhost=.*/${DB_LINE}/g" /usr/local/akcs/scripts/get_acks_macpool.php
if [ -z "`grep "/usr/local/akcs/scripts/get_acks_macpool.php" /var/spool/cron/crontabs/root`" ];then
		echo "15 9 * * * /usr/local/bin/php  /usr/local/akcs/scripts/get_acks_macpool.php >/dev/null 2>&1 " >> /var/spool/cron/crontabs/root
fi
    
if [ ! -d /var/www/download/personal ];then
	mkdir -p /var/www/download/personal
	chown nobody:nogroup /var/www/download/personal
fi

cp -rf ${PAKCAGES_ROOT}/conf/000000000001.cfg /var/www/download/personal/
cp -rf ${PAKCAGES_ROOT}/conf/000000000100.xml /var/www/download/personal/
cp -rf ${PAKCAGES_ROOT}/conf/000000000010.xml /var/www/download/personal/
cp -rf $PAKCAGES_ROOT/conf/000000000011.json /var/www/download/personal/
cp -rf $PAKCAGES_ROOT/conf/000000000111.json /var/www/download/personal/

if [ ! -d /var/www/download/community ];then
	mkdir -p /var/www/download/community
	chown nobody:nogroup /var/www/download/community
fi

if [ ! -d /var/www/download/tmp_key_qrcode ];then
	mkdir -p /var/www/download/tmp_key_qrcode
	chown nobody:nogroup /var/www/download/tmp_key_qrcode
fi

if [ ! -d /var/www/download/per_qrcode ];then
	 mkdir -p /var/www/download/per_qrcode
	 chown nobody:nogroup /var/www/download/per_qrcode
fi

if [ ! -d /var/www/download/versionfile ];then
	mkdir -p /var/www/download/versionfile
	chown nobody:nogroup /var/www/download/versionfile
fi



