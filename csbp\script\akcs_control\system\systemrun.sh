#!/bin/bash

PROCESS_NGINX=nginx
PROCESS_NGINX_START_CMD="/etc/init.d/nginx start"
PROCESS_NGINX_PID=/usr/local/nginx/logs/nginx.pid

PROCESS_PHP=php-fpm
PROCESS_PHP_PID=/usr/local/php/var/run/php-fpm.pid
PROCESS_PHP_START_CMD="/etc/init.d/php-fpm start"

LOG_FILE=/var/log/system_run_daemon.log


. /etc/profile #开启core文件


PROCESS_COMMON_SCRIPTS="/usr/local/akcs/scripts/common.sh"

#一定要是绝对路径，不然就变成要指定目录执行这个run
source $PROCESS_COMMON_SCRIPTS
while [ 1 ]
do
    common_run_pid_detect $PROCESS_NGINX $PROCESS_NGINX_PID "$PROCESS_NGINX_START_CMD" $LOG_FILE
    common_run_pid_detect $PROCESS_PHP $PROCESS_PHP_PID "$PROCESS_PHP_START_CMD" $LOG_FILE
    COMMON_FIRST_RUN=0
    
	sleep 5
done