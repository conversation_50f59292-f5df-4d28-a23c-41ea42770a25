#!/bin/bash

WORK_DIR=`pwd`
BASE_PATH=${WORK_DIR}/../../../
CSBASE_PATH=csbase
CSMAIN_PATH=csmain
CSADAPT_PATH=csadapt
CSVRTSP_PATH=csvrtsp
CSGATE_PATH=csgate
CSSTORAGE_PATH=csstorage
CSVRECORD_PATH=csvrecord
AKCS_PACKAGE_ROOT=${BASE_PATH}/
AKCS_PACKAGE=${AKCS_PACKAGE_ROOT}akcs-packages/
AKCS_PACKAGE_CSMAIN=${AKCS_PACKAGE}akcs/csmain
AKCS_PACKAGE_CSGATE=${AKCS_PACKAGE}akcs/csgate
AKCS_PACKAGE_CSADAPT=${AKCS_PACKAGE}akcs/csadapt
AKCS_PACKAGE_CSVRTSP=${AKCS_PACKAGE}akcs/csvrtsp
AKCS_PACKAGE_CSSTORAGE=${AKCS_PACKAGE}akcs/csstorage
AKCS_PACKAGE_CSVRECORD=${AKCS_PACKAGE}akcs/csvrecord
AKCS_PACKAGE_FASTDFS=${AKCS_PACKAGE}akcs/fastdfs
AKCS_PACKAGE_CSPUSH=${AKCS_PACKAGE}akcs/cspush
AKCS_PACKAGE_SCRIPTS=${AKCS_PACKAGE}akcs/scripts
AKCS_PACKAGE_LIBS=${AKCS_PACKAGE}akcs/libs
AKCS_PACKAGE_WEBROOT=${AKCS_PACKAGE}webroot
AKCS_PACKAGE_SLIM=${AKCS_PACKAGE}akcs/slim
AKCS_PACKAGE_SQL=${AKCS_PACKAGE}akcs/sql


build() {
    rm -rf $AKCS_PACKAGE
    if [ -d $AKCS_PACKAGE_CSMAIN ]
    then
        rm -rf $AKCS_PACKAGE_CSMAIN
    fi
    mkdir -p $AKCS_PACKAGE_CSMAIN/bin
    mkdir -p $AKCS_PACKAGE_CSMAIN/conf
    mkdir -p $AKCS_PACKAGE_CSMAIN/lib
    chmod -R 755 $AKCS_PACKAGE_CSMAIN/*


    if [ -d $AKCS_PACKAGE_CSGATE ]
    then
        rm -rf $AKCS_PACKAGE_CSGATE
    fi
    mkdir -p $AKCS_PACKAGE_CSGATE/bin
    mkdir -p $AKCS_PACKAGE_CSGATE/conf
    mkdir -p $AKCS_PACKAGE_CSGATE/lib
    chmod -R 755 $AKCS_PACKAGE_CSGATE/*

    if [ -d $AKCS_PACKAGE_CSADAPT ]
    then
        rm -rf $AKCS_PACKAGE_CSADAPT
    fi
    mkdir -p $AKCS_PACKAGE_CSADAPT/bin
    mkdir -p $AKCS_PACKAGE_CSADAPT/conf
    mkdir -p $AKCS_PACKAGE_CSADAPT/lib
    chmod -R 755 $AKCS_PACKAGE_CSADAPT/*

    if [ -d $AKCS_PACKAGE_CSVRTSP ]
    then
        rm -rf $AKCS_PACKAGE_CSVRTSP
    fi
    mkdir -p $AKCS_PACKAGE_CSVRTSP/bin
    mkdir -p $AKCS_PACKAGE_CSVRTSP/conf
    mkdir -p $AKCS_PACKAGE_CSVRTSP/lib
    chmod -R 755 $AKCS_PACKAGE_CSVRTSP/*

    #csstorage
    if [ -d $AKCS_PACKAGE_CSSTORAGE ]
    then
        rm -rf $AKCS_PACKAGE_CSSTORAGE
    fi
    mkdir -p $AKCS_PACKAGE_CSSTORAGE/bin
    mkdir -p $AKCS_PACKAGE_CSSTORAGE/conf
    mkdir -p $AKCS_PACKAGE_CSSTORAGE/lib
    mkdir -p $AKCS_PACKAGE_CSSTORAGE/data
    chmod -R 755 $AKCS_PACKAGE_CSSTORAGE/*

    #csvrecord
    if [ ! -d $AKCS_PACKAGE_CSVRECORD ]
    then
        rm -rf $AKCS_PACKAGE_CSVRECORD
    fi
    mkdir -p $AKCS_PACKAGE_CSVRECORD/bin
    mkdir -p $AKCS_PACKAGE_CSVRECORD/conf
    mkdir -p $AKCS_PACKAGE_CSVRECORD/lib
    chmod -R 755 $AKCS_PACKAGE_CSVRECORD/*

    #cspush
    if [ -d $AKCS_PACKAGE_CSPUSH ]
    then
        rm -rf $AKCS_PACKAGE_CSVRECORD
    fi
    mkdir -p $AKCS_PACKAGE_CSPUSH
    chmod 755 -R $AKCS_PACKAGE_CSPUSH

    #fdfs
    if [ -d $AKCS_PACKAGE_FASTDFS ]
    then
        rm -rf $AKCS_PACKAGE_FASTDFS
    fi
    mkdir -p $AKCS_PACKAGE_FASTDFS
    chmod -R 755 $AKCS_PACKAGE_FASTDFS/

    #script
    if [ ! -d $AKCS_PACKAGE_SCRIPTS ]
    then
        rm -rf $AKCS_PACKAGE_SCRIPTS
        mkdir -p $AKCS_PACKAGE_SCRIPTS
        chmod -R 755 $AKCS_PACKAGE_SCRIPTS/
    fi

    #slim打包位置
    if [ -d $AKCS_PACKAGE_SLIM ]
    then
        rm -rf $AKCS_PACKAGE_SLIM
    fi
    mkdir -p $AKCS_PACKAGE_SLIM
    chmod -R 755 $AKCS_PACKAGE_SLIM/

    #sql打包位置
    if [ -d $AKCS_PACKAGE_SQL ]
    then
        rm -rf $AKCS_PACKAGE_SQL
    fi
    mkdir -p $AKCS_PACKAGE_SQL
    chmod -R 755 $AKCS_PACKAGE_SQL/

    if [ ! -d $AKCS_PACKAGE_LIBS ]
    then
        rm -rf $AKCS_PACKAGE_LIBS
        mkdir -p $AKCS_PACKAGE_LIBS
    fi

    if [ ! -d $AKCS_PACKAGE_WEBROOT ]
    then
        rm -rf $AKCS_PACKAGE_WEBROOT
        mkdir -p $AKCS_PACKAGE_WEBROOT
    fi

    #build csbase
    CSBASE=$BASE_PATH/$CSBASE_PATH
	cd $CSBASE || exit 1
    cmake ./
    make
    if [ $? -eq 0 ]; then
        echo "make csbase successed";
    else
        echo "make csbase failed";
        exit;
    fi

    #build csmain
    #而不是CSMAIN=$BASE_PATH/$1/$CSMAIN_PATH/build
    CSMAIN=$BASE_PATH/$CSMAIN_PATH/build
	cd $CSMAIN || exit 1
    make
    if [ $? -eq 0 ]; then
        echo "make csmain successed";
    else
        echo "make csmain failed";
        exit;
    fi
    cp -f ../bin/*  $AKCS_PACKAGE_CSMAIN/bin
    ln -sf $AKCS_PACKAGE_CSMAIN/bin/csmain_cli /bin/
    cp -f $BASE_PATH/conf/csmain.conf  $AKCS_PACKAGE_CSMAIN/conf
    cp -f $BASE_PATH/conf/akcs_redis.conf  $AKCS_PACKAGE_CSMAIN/conf

    #build csgate
    CSGATE=$BASE_PATH/$CSGATE_PATH/build
	cd $CSGATE || exit 1
    make
    if [ $? -eq 0 ]; then
        echo "make csgate successed";
    else
        echo "make csgate failed";
        exit;
    fi
    cp -f ../bin/*  $AKCS_PACKAGE_CSGATE/bin
    cp -f $BASE_PATH/conf/csgate.conf  $AKCS_PACKAGE_CSGATE/conf

    #build csvrtsp
    CSVRTSP=$BASE_PATH/$CSVRTSP_PATH/build
	cd $CSVRTSP || exit 1
    make
    if [ $? -eq 0 ]; then  #即使有告警,也不算是错误
        echo "make csvrtsp successed";
    else
        echo "make csvrtsp failed";
        exit;
    fi
    cp -f ../bin/*  $AKCS_PACKAGE_CSVRTSP/bin
    cp -f ../lib/*  $AKCS_PACKAGE_CSVRTSP/lib
    cp -f $BASE_PATH/conf/csvrtsp.conf  $AKCS_PACKAGE_CSVRTSP/conf

    #build csadapt
    CSADAPT=$BASE_PATH/$CSADAPT_PATH/build
	cd $CSADAPT || exit 1
    make
    if [ $? -eq 0 ]; then
        echo "make csadapt successed";
    else
        echo "make csadapt failed";
        exit;
    fi
    cp -f ../bin/*  $AKCS_PACKAGE_CSADAPT/bin
    cp -f $BASE_PATH/conf/csadapt.conf  $AKCS_PACKAGE_CSADAPT/conf
    cp -f $BASE_PATH/conf/000000000001.cfg  $AKCS_PACKAGE_CSADAPT/conf
    cp -f $BASE_PATH/conf/000000000100.xml  $AKCS_PACKAGE_CSADAPT/conf

    #build csstorage
    CSSTORAGE=$BASE_PATH/$CSSTORAGE_PATH
	cd $CSSTORAGE || exit 1
    cmake ./
    make
    if [ $? -eq 0 ]; then
        echo "make csstorage successed";
    else
        echo "make csstorage failed";
        exit;
    fi
    cp -f ./*  $AKCS_PACKAGE_CSSTORAGE/bin
    cp -f $BASE_PATH/conf/csstorage.conf   $AKCS_PACKAGE_CSSTORAGE/conf
    cp -f $BASE_PATH/conf/csstorage_fdfs.conf   $AKCS_PACKAGE_CSSTORAGE/conf

    #build csvrecord
    CSVRECORD=$BASE_PATH/$CSVRECORD_PATH/build
	cd $CSVRECORD || exit 1
    make
    if [ $? -eq 0 ]; then
        echo "make csvrecord successed";
    else
        echo "make csvrecord failed";
        exit;
    fi
    cp -f ../bin/*  $AKCS_PACKAGE_CSVRECORD/bin
    cp -f $BASE_PATH/conf/csvrecord.conf  $AKCS_PACKAGE_CSVRECORD/conf

    #copy cspush
    #echo '---begin to copy cspush----'
    #CSPUSH=$BASE_PATH/cspush
	#cd $CSPUSH
    #cp -fr ./*  $AKCS_PACKAGE_CSPUSH

    #fastdfs,直接拷贝docker容器的tar包,以及宿主机上的配置文件, -r递归拷贝
    #4.0之后把这部分放到单独的包
    #echo '---begin to copy fastdfs----'
    #cd ${WORK_DIR}
    #cp -fr $BASE_PATH/fastdfs/*  $AKCS_PACKAGE_FASTDFS/

    #slim
    echo "coping slim..."
    cd ${WORK_DIR} || exit 1
    cp -rf $BASE_PATH/slim/*  $AKCS_PACKAGE_SLIM/

    #make web
    cd $BASE_PATH/web/AKCS3.0 || exit 1
    npm run build
    if [ $? -eq 0 ]; then
        echo "npm build AKCS3.0 successed";
    else
        echo "npm build AKCS3.0 failed";
        exit;
    fi

    cd $BASE_PATH/web/AKCS3.0-Person || exit 1
    npm run build
    if [ $? -eq 0 ]; then
        echo "npm build AKCS3.0-Person successed";
    else
        echo "npm build AKCS3.0-Person failed";
        exit;
    fi

    #copy scripts
    echo "coping sql..."
    cp -rf $BASE_PATH/csbp/script/akcs_control/* $AKCS_PACKAGE_SCRIPTS/

    #copy sql
    echo "coping sql..."
    cp -rf $BASE_PATH/csbp/install/* $AKCS_PACKAGE_SQL/

    #copy libs
    echo "coping libs..."
    cp -f $BASE_PATH/csbase/thirdlib/* $AKCS_PACKAGE_LIBS/
    cp -f $BASE_PATH/csbase/evpp/lib/* $AKCS_PACKAGE_LIBS/

    #copy web files
    echo "coping web files..."
    mkdir -p $AKCS_PACKAGE_WEBROOT/VBell
    cp -rf $BASE_PATH/web/VBell/VBell/* $AKCS_PACKAGE_WEBROOT/VBell
    cp -rf $BASE_PATH/web/webroot/* $AKCS_PACKAGE_WEBROOT/
    cp -rf $BASE_PATH/slim $AKCS_PACKAGE_WEBROOT/
    cp -rf $BASE_PATH/web/AKCS3.0-Person/dist $AKCS_PACKAGE_WEBROOT/
    cp -rf $BASE_PATH/web/AKCS3.0/dist $AKCS_PACKAGE_WEBROOT/manage
    find $AKCS_PACKAGE_WEBROOT -name .svn |xargs rm -rf

	#copy system config
	cp -R $BASE_PATH/system ${AKCS_PACKAGE}

	#copy version 在jenkins中生成
	cp -R $BASE_PATH/version ${AKCS_PACKAGE}

    #个组件均编译成功
    echo "-----------------------all build successful-------------------------"

    #打成tar包
    cd ${AKCS_PACKAGE_ROOT} || exit 1
    rm -rf akcs-packages.tar.gz
    tar zcf akcs-packages.tar.gz akcs-packages

    echo "${AKCS_PACKAGE_ROOT}/akcs-packages.tar.gz be created successful."
}

clean() {
	cd $BASE_PATH/$CSMAIN_PATH/build || exit 1
	make clean
	cd $BASE_PATH/$CSGATE_PATH/build || exit 1
	make clean
	cd $BASE_PATH/$CSVRTSP_PATH/build || exit 1
	make clean
	cd $BASE_PATH/$CSADAPT_PATH/build || exit 1
	make clean
    cd $BASE_PATH/$CSVRECORD_PATH/build || exit 1
	make clean
    cd $BASE_PATH/$CSSTORAGE_PATH || exit 1
	rm -rf CMakeCache.txt CMakeFiles cmake_install.cmake
}

print_help() {
	echo "Usage: "
	echo "  $0 clean --- clean version xx, eg : $0 clean "
    echo "  $0 build ---  build version xx, eg : $0 build "
}

case $1 in
	clean)
    	if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi
		echo "clean all build..."
		clean
		;;
	build)
		if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi

		echo "build..."
		build
		;;
	*)
		print_help
		;;
esac
