#!/bin/bash

PWD=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
AKCS_SRC_ROOT=${PWD}/../../..
AKCS_SRC_CSCONFIG=${AKCS_SRC_ROOT}/csconfig
AKCS_SRC_CSBASE=${AKCS_SRC_ROOT}/csbase
AKCS_SRC_CSBP=${AKCS_SRC_ROOT}/csbp

#在源码包的同一路径下生成安装包
AKCS_PACKAGE_ROOT=${AKCS_SRC_ROOT}/akcs_csconfig_packeg
AKCS_PACKAGE_ROOT_CSCONFIG=${AKCS_PACKAGE_ROOT}/csconfig
AKCS_PACKAGE_ROOT_SCRIPTS=${AKCS_PACKAGE_ROOT}/csconfig_scripts

build() {
    #先清理上一次的安装包
    if [ -d $AKCS_PACKAGE_ROOT ]
    then
        rm -rf $AKCS_PACKAGE_ROOT
    fi
    mkdir -p $AKCS_PACKAGE_ROOT_CSCONFIG/bin
    mkdir -p $AKCS_PACKAGE_ROOT_CSCONFIG/conf
	mkdir -p $AKCS_PACKAGE_ROOT_CSCONFIG/lib

    mkdir -p $AKCS_PACKAGE_ROOT_SCRIPTS

    
    chmod -R 777 $AKCS_PACKAGE_ROOT/*
    #build csbase
	cd $AKCS_SRC_CSBASE
    cmake ./
    make
    if [ $? -eq 0 ]; then
        echo "make csbase successed";
    else
        echo "make csbase failed";
        exit;
    fi

    #build csconfig
	cd $AKCS_SRC_CSCONFIG/build
    make
    if [ $? -eq 0 ]; then  #即使有告警,也不算是错误
        echo "make csconfig successed";
    else
        echo "make csconfig failed";
        exit;
    fi    
    cp -f ../bin/*  $AKCS_PACKAGE_ROOT_CSCONFIG/bin
    cp -f $AKCS_SRC_ROOT/conf/csconfig.conf  $AKCS_PACKAGE_ROOT_CSCONFIG/conf
	cp -f $AKCS_SRC_ROOT/conf/csconfig_fdfs.conf  $AKCS_PACKAGE_ROOT_CSCONFIG/conf
	    cp -f $AKCS_SRC_ROOT/conf/csconfig_redis.conf  $AKCS_PACKAGE_ROOT_CSCONFIG/conf
    cp -f $AKCS_SRC_ROOT/conf/000000000001.cfg  $AKCS_PACKAGE_ROOT_CSCONFIG/conf
    cp -f $AKCS_SRC_ROOT/conf/000000000100.xml  $AKCS_PACKAGE_ROOT_CSCONFIG/conf
	cp -f $AKCS_SRC_CSBASE/thirdlib/libetcd-cpp-api.so  $AKCS_PACKAGE_ROOT_CSCONFIG/lib
    #copy control scripts
    echo "coping control scripts..."
    cp -rf $AKCS_SRC_ROOT/csbp/script/akcs_control/csconfig/* $AKCS_PACKAGE_ROOT_SCRIPTS/
    cp -rf $AKCS_SRC_ROOT/csbp/script/akcs_control/common_scripts/* $AKCS_PACKAGE_ROOT_SCRIPTS/

	#copy version 在jenkins中生成
	cp -R $AKCS_SRC_ROOT/csconfig_version ${AKCS_PACKAGE_ROOT}	
	
    #个组件均编译成功
    echo "-----------------------all build successful-------------------------"

    #打成tar包
    cd ${AKCS_PACKAGE_ROOT}/../
    rm -rf akcs_csconfig_packeg.tar.gz
    tar zcvf akcs_csconfig_packeg.tar.gz akcs_csconfig_packeg
    echo "${AKCS_PACKAGE_ROOT}/akcs-packages.tar.gz is created successful."
}

clean() {
	cd $AKCS_SRC_CSCONFIG/build
	make clean
}

print_help() {
	echo "Usage: "
	echo "  $0 clean --- clean csconfig application, eg : $0 clean "
    echo "  $0 build ---  build csconfig application, eg : $0 build "
}

case $1 in
	clean)
    	if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi
		echo "clean all build..."
		clean
		;;
	build)
		if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi

		echo "build..."
		build 
		;;
	*)
		print_help
		;;
esac
