#!/bin/bash

WORK_DIR=`pwd`
AKCS_SRC_ROOT=${WORK_DIR}/../../..
AKCS_SRC_CSFTP=${AKCS_SRC_ROOT}/csftp
AKCS_SRC_CSBASE=${AKCS_SRC_ROOT}/csbase
AKCS_SRC_CSBP=${AKCS_SRC_ROOT}/csbp

#在源码包的同一路径下生成安装包
AKCS_PACKAGE_ROOT=${AKCS_SRC_ROOT}/akcs_csftp_packeg
AKCS_PACKAGE_ROOT_CSFTP=${AKCS_PACKAGE_ROOT}/csftp
AKCS_PACKAGE_ROOT_SCRIPTS=${AKCS_PACKAGE_ROOT}/csftp_scripts

build() {
    #先清理上一次的安装包
    if [ -d $AKCS_PACKAGE_ROOT ]
    then
        rm -rf $AKCS_PACKAGE_ROOT
    fi
    mkdir -p $AKCS_PACKAGE_ROOT_CSFTP/bin
    mkdir -p $AKCS_PACKAGE_ROOT_CSFTP/conf
    mkdir -p $AKCS_PACKAGE_ROOT_SCRIPTS
    chmod -R 777 $AKCS_PACKAGE_ROOT/*

	#build csbase
	cd $AKCS_SRC_CSBASE || exit 1
    cmake ./
    make
    if [ $? -eq 0 ]; then
        echo "make csbase successed";
    else
        echo "make csbase failed";
        exit;
    fi

    #build csftp
	str_pam=`dpkg -l|grep libpam0g-dev`
	if [ ! "$str_pam" ];then
		apt-get install libpam0g-dev
	fi
	cd $AKCS_SRC_CSFTP/ || exit 1
    make
    if [ $? -eq 0 ]; then  #即使有告警,也不算是错误
        echo "make csftp successed";
    else
        echo "make csftp failed";
        exit;
    fi
    cp -f vsftpd $AKCS_PACKAGE_ROOT_CSFTP/bin
	cp -rf system/ $AKCS_PACKAGE_ROOT/
    cp -f $AKCS_SRC_ROOT/conf/csftp.conf  $AKCS_PACKAGE_ROOT_CSFTP/conf
	cp -f $AKCS_SRC_ROOT/conf/csftp_redis.conf  $AKCS_PACKAGE_ROOT_CSFTP/conf

    #copy control scripts
    echo "coping control scripts..."
    cp -rf $AKCS_SRC_ROOT/csbp/script/akcs_control/csftp/* $AKCS_PACKAGE_ROOT_SCRIPTS/
    cp -rf $AKCS_SRC_ROOT/csbp/script/akcs_control/common_scripts/* $AKCS_PACKAGE_ROOT_SCRIPTS/


	#copy version 在jenkins中生成
	cp -R $AKCS_SRC_ROOT/csftp_version ${AKCS_PACKAGE_ROOT}

    #个组件均编译成功
    echo "-----------------------all build successful-------------------------"

    #打成tar包
    cd ${AKCS_PACKAGE_ROOT}/../ || exit 1
    rm -rf akcs_csftp_packeg.tar.gz
    tar zcvf akcs_csftp_packeg.tar.gz akcs_csftp_packeg
    echo "${AKCS_PACKAGE_ROOT}/akcs-packages.tar.gz is created successful."
}

clean() {
	cd $AKCS_SRC_CSFTP || exit 1
	make clean
}

print_help() {
	echo "Usage: "
	echo "  $0 clean --- clean csftp application, eg : $0 clean "
    echo "  $0 build ---  build csftp application, eg : $0 build "
}

case $1 in
	clean)
    	if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi
		echo "clean all build..."
		clean
		;;
	build)
		if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi

		echo "build..."
		build
		;;
	*)
		print_help
		;;
esac
