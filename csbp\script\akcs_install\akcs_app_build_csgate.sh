#!/bin/bash

WORK_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
AKCS_SRC_ROOT=${WORK_DIR}/../../..
AKCS_SRC_CSGATE=${AKCS_SRC_ROOT}/csgate
AKCS_SRC_CSBASE=${AKCS_SRC_ROOT}/csbase
AKCS_SRC_CSBP=${AKCS_SRC_ROOT}/csbp


#在源码包的同一路径下生成安装包
AKCS_PACKAGE_ROOT=${AKCS_SRC_ROOT}/akcs_csgate_packeg
AKCS_PACKAGE_ROOT_CSGATE=${AKCS_PACKAGE_ROOT}/csgate
AKCS_PACKAGE_ROOT_SCRIPTS=${AKCS_PACKAGE_ROOT}/csgate_scripts

build() {
    #先清理上一次的安装包
    if [ -d $AKCS_PACKAGE_ROOT ]
    then
        rm -rf $AKCS_PACKAGE_ROOT
    fi
    mkdir -p $AKCS_PACKAGE_ROOT_CSGATE/bin
	mkdir -p $AKCS_PACKAGE_ROOT_CSGATE/lib
    mkdir -p $AKCS_PACKAGE_ROOT_CSGATE/conf
    mkdir -p $AKCS_PACKAGE_ROOT_SCRIPTS
    chmod -R 777 $AKCS_PACKAGE_ROOT/*
    #build csbase
	cd $AKCS_SRC_CSBASE || exit 1
    cmake ./
    make
    if [ $? -eq 0 ]; then
        echo "make csbase successed";
    else
        echo "make csbase failed";
        exit;
    fi

    #build csgate
	cd $AKCS_SRC_CSGATE/build || exit 1
    make
    if [ $? -eq 0 ]; then  #即使有告警,也不算是错误
        echo "make csgate successed";
    else
        echo "make csgate failed";
        exit;
    fi
    cp -f ../bin/*  $AKCS_PACKAGE_ROOT_CSGATE/bin
    cp -f $AKCS_SRC_ROOT/conf/csgate.conf  $AKCS_PACKAGE_ROOT_CSGATE/conf
	cp -f $AKCS_SRC_ROOT/conf/csgate_AWS.conf  $AKCS_PACKAGE_ROOT_CSGATE/conf
	cp -f $AKCS_SRC_ROOT/conf/csgate_redis.conf  $AKCS_PACKAGE_ROOT_CSGATE/conf
	cp -f $AKCS_SRC_CSBASE/thirdlib/libevpp.so  $AKCS_PACKAGE_ROOT_CSGATE/lib
    cp -f $AKCS_SRC_CSBASE/thirdlib/libetcd-cpp-api.so  $AKCS_PACKAGE_ROOT_CSGATE/lib
    
    #copy control scripts
    echo "coping control scripts..."
    cp -rf $AKCS_SRC_ROOT/csbp/script/akcs_control/csgate/* $AKCS_PACKAGE_ROOT_SCRIPTS/
    cp -rf $AKCS_SRC_ROOT/csbp/script/akcs_control/common_scripts/* $AKCS_PACKAGE_ROOT_SCRIPTS/

	#copy version 在jenkins中生成
	cp -R $AKCS_SRC_ROOT/csgate_version ${AKCS_PACKAGE_ROOT}

	#svn版本获取
	cd $AKCS_SRC_CSGATE || exit 1
	svn upgrade
	REV=`svn info | grep 'Last Changed Rev' | awk '{print $4}'`
	sed -i "s/^.*svn_version=.*/svn_version=${REV}/g" $AKCS_PACKAGE_ROOT_CSGATE/conf/csgate.conf

    #个组件均编译成功
    echo "-----------------------all build successful-------------------------"

    #打成tar包
    cd ${AKCS_PACKAGE_ROOT}/../ || exit 1
    rm -rf akcs_csgate_packeg.tar.gz
    tar zcvf akcs_csgate_packeg.tar.gz akcs_csgate_packeg
    echo "${AKCS_PACKAGE_ROOT}/akcs-packages.tar.gz is created successful."
}

clean() {
	cd $AKCS_SRC_CSGATE/build || exit 1
	make clean
}

print_help() {
	echo "Usage: "
	echo "  $0 clean --- clean csgate application, eg : $0 clean "
    echo "  $0 build ---  build csgate application, eg : $0 build "
}

case $1 in
	clean)
    	if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi
		echo "clean all build..."
		clean
		;;
	build)
		if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi

		echo "build..."
		build
		;;
	*)
		print_help
		;;
esac
