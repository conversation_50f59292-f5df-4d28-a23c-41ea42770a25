#!/bin/bash

WORK_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
AKCS_SRC_ROOT=${WORK_DIR}/../../..
AKCS_SRC_CSLINKER=${AKCS_SRC_ROOT}/cslinker
AKCS_SRC_CSBP=${AKCS_SRC_ROOT}/csbp


#在源码包的同一路径下生成安装包
AKCS_PACKAGE_ROOT=${AKCS_SRC_ROOT}/akcs_cslinker_packeg
AKCS_PACKAGE_ROOT_CSLINKER=${AKCS_PACKAGE_ROOT}/cslinker
AKCS_PACKAGE_ROOT_SCRIPTS=${AKCS_PACKAGE_ROOT}/cslinker_scripts

build() {
    #先清理上一次的安装包
    if [ -d $AKCS_PACKAGE_ROOT ]
    then
        rm -rf $AKCS_PACKAGE_ROOT
    fi
    mkdir -p $AKCS_PACKAGE_ROOT_CSLINKER
    mkdir -p $AKCS_PACKAGE_ROOT_SCRIPTS

    chmod -R 777 $AKCS_PACKAGE_ROOT/*

	#copy file
	cp -rR $AKCS_SRC_CSLINKER/* $AKCS_PACKAGE_ROOT_CSLINKER

    #copy control scripts
    echo "coping control scripts..."
    cp -rf $AKCS_SRC_ROOT/csbp/script/akcs_control/cslinker/* $AKCS_PACKAGE_ROOT_SCRIPTS/
    cp -rf $AKCS_SRC_ROOT/csbp/script/akcs_control/common_scripts/common.sh $AKCS_PACKAGE_ROOT_SCRIPTS/


	#copy version 在jenkins中生成
	cp -R $AKCS_SRC_ROOT/cslinker_version ${AKCS_PACKAGE_ROOT}

    #个组件均编译成功
    echo "-----------------------all build successful-------------------------"

    #打成tar包
    cd ${AKCS_PACKAGE_ROOT}/../ || exit 1
    rm -rf akcs_cslinker_packeg.tar.gz
    tar zcvf akcs_cslinker_packeg.tar.gz akcs_cslinker_packeg
    echo "${AKCS_PACKAGE_ROOT}/akcs-packages.tar.gz is created successful."
}

clean() {
	echo "clean";
}

print_help() {
	echo "Usage: "
	echo "  $0 clean --- clean cslinker application, eg : $0 clean "
    echo "  $0 build ---  build cslinker application, eg : $0 build "
}

case $1 in
	clean)
    	if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi
		echo "clean all build..."
		clean
		;;
	build)
		if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi

		echo "build..."
		build
		;;
	*)
		print_help
		;;
esac
