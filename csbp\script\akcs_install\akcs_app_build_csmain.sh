#!/bin/bash

WORK_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
AKCS_SRC_ROOT=${WORK_DIR}/../../..
AKCS_SRC_CSMAIN=${AKCS_SRC_ROOT}/csmain
AKCS_SRC_CSBASE=${AKCS_SRC_ROOT}/csbase
AKCS_SRC_CSBP=${AKCS_SRC_ROOT}/csbp
AKCS_SRC_ETCD=${AKCS_SRC_ROOT}/system/etcd

#在源码包的同一路径下生成安装包
AKCS_PACKAGE_ROOT=${AKCS_SRC_ROOT}/akcs_csmain_packeg
AKCS_PACKAGE_ROOT_CSMAIN=${AKCS_PACKAGE_ROOT}/csmain
AKCS_PACKAGE_ROOT_ETCDCTL=${AKCS_PACKAGE_ROOT}/etcdctl
AKCS_PACKAGE_ROOT_SCRIPTS=${AKCS_PACKAGE_ROOT}/csmain_scripts

build() {
    #先清理上一次的安装包
    if [ -d $AKCS_PACKAGE_ROOT ]
    then
        rm -rf $AKCS_PACKAGE_ROOT
    fi
    mkdir -p $AKCS_PACKAGE_ROOT_CSMAIN/bin
    mkdir -p $AKCS_PACKAGE_ROOT_CSMAIN/conf
    mkdir -p $AKCS_PACKAGE_ROOT_CSMAIN/lib
    mkdir -p $AKCS_PACKAGE_ROOT_ETCDCTL

    mkdir -p $AKCS_PACKAGE_ROOT_SCRIPTS


    chmod -R 777 $AKCS_PACKAGE_ROOT/*
    #build csbase
	cd $AKCS_SRC_CSBASE || exit 1
    cmake ./
    make
    if [ $? -eq 0 ]; then
        echo "make csbase successed";
    else
        echo "make csbase failed";
        exit;
    fi

    #build csmain
	cd $AKCS_SRC_CSMAIN/build || exit 1
    make
    if [ $? -eq 0 ]; then  #即使有告警,也不算是错误
        echo "make csmain successed";
    else
        echo "make csmain failed";
        exit;
    fi
    cp -f ../bin/*  $AKCS_PACKAGE_ROOT_CSMAIN/bin
    cp -f $AKCS_SRC_ROOT/conf/csmain.conf  $AKCS_PACKAGE_ROOT_CSMAIN/conf
    cp -f $AKCS_SRC_ROOT/conf/csmain_redis.conf  $AKCS_PACKAGE_ROOT_CSMAIN/conf
    cp -f $AKCS_SRC_ROOT/conf/TimeZone.xml  $AKCS_PACKAGE_ROOT_CSMAIN/conf
    cp -f $AKCS_SRC_CSBASE/thirdlib/libevpp.so  $AKCS_PACKAGE_ROOT_CSMAIN/lib	
    cp -f $AKCS_SRC_CSBASE/thirdlib/libetcd-cpp-api.so  $AKCS_PACKAGE_ROOT_CSMAIN/lib

    #copy etcdctl
    cp -f $AKCS_SRC_ETCD/bin/etcdctl  $AKCS_PACKAGE_ROOT_ETCDCTL

    #copy control scripts
    echo "coping control scripts..."
    cp -rf $AKCS_SRC_ROOT/csbp/script/akcs_control/csmain/* $AKCS_PACKAGE_ROOT_SCRIPTS/
    cp -rf $AKCS_SRC_ROOT/csbp/script/akcs_control/common_scripts/* $AKCS_PACKAGE_ROOT_SCRIPTS/


	#copy version 在jenkins中生成
	cp -R $AKCS_SRC_ROOT/csmain_version ${AKCS_PACKAGE_ROOT}

	#svn版本获取
	cd $AKCS_SRC_CSMAIN || exit 1
	svn upgrade
	REV=`svn info | grep 'Last Changed Rev' | awk '{print $4}'`
	sed -i "s/^.*svn_version=.*/svn_version=${REV}/g" $AKCS_PACKAGE_ROOT_CSMAIN/conf/csmain.conf

    #个组件均编译成功
    echo "-----------------------all build successful-------------------------"

    #打成tar包
    cd ${AKCS_PACKAGE_ROOT}/../ || exit 1
    rm -rf akcs_csmain_packeg.tar.gz
    tar zcvf akcs_csmain_packeg.tar.gz akcs_csmain_packeg
    echo "${AKCS_PACKAGE_ROOT}/akcs-packages.tar.gz is created successful."
}

clean() {
	cd $AKCS_SRC_CSMAIN/build || exit 1
	make clean
}

print_help() {
	echo "Usage: "
	echo "  $0 clean --- clean csmain application, eg : $0 clean "
    echo "  $0 build ---  build csmain application, eg : $0 build "
}

case $1 in
	clean)
    	if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi
		echo "clean all build..."
		clean
		;;
	build)
		if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi

		echo "build..."
		build
		;;
	*)
		print_help
		;;
esac
