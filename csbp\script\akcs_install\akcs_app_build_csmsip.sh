#!/bin/bash

WORK_DIR=`pwd`
AKCS_SRC_ROOT=${WORK_DIR}/../../..
AKCS_SRC_CSMSIP=${AKCS_SRC_ROOT}/csmsip
AKCS_SRC_CSBASE=${AKCS_SRC_ROOT}/csbase
AKCS_SRC_CSBP=${AKCS_SRC_ROOT}/csbp


#在源码包的同一路径下生成安装包
AKCS_PACKAGE_ROOT=${AKCS_SRC_ROOT}/akcs_csmsip_packeg
AKCS_PACKAGE_ROOT_CSMSIP=${AKCS_PACKAGE_ROOT}/csmsip
AKCS_PACKAGE_ROOT_SCRIPTS=${AKCS_PACKAGE_ROOT}/csmsip_scripts
AKCS_PACKAGE_NAME=akcs_csmsip_packeg

build() {
    #先清理上一次的安装包
    if [ -d $AKCS_PACKAGE_ROOT ]
    then
        rm -rf $AKCS_PACKAGE_ROOT
    fi
    mkdir -p $AKCS_PACKAGE_ROOT_CSMSIP/bin
    mkdir -p $AKCS_PACKAGE_ROOT_CSMSIP/conf

    mkdir -p $AKCS_PACKAGE_ROOT_SCRIPTS


    chmod -R 777 $AKCS_PACKAGE_ROOT/*
    #build csbase
	cd $AKCS_SRC_CSBASE || exit 1
    cmake ./
    make
    if [ $? -eq 0 ]; then
        echo "make csbase successed";
    else
        echo "make csbase failed";
        exit;
    fi

    #build csmsip
	cd $AKCS_SRC_CSMSIP || exit 1
    cmake ./
    make
    if [ $? -eq 0 ]; then  #即使有告警,也不算是错误
        echo "make csmsip successed";
    else
        echo "make csmsip failed";
        exit;
    fi
    cp -f ./bin/* $AKCS_PACKAGE_ROOT_CSMSIP/bin
    cp -f $AKCS_SRC_ROOT/conf/csmsip.conf  $AKCS_PACKAGE_ROOT_CSMSIP/conf
	cp -f $AKCS_SRC_ROOT/conf/csmsip_redis.conf  $AKCS_PACKAGE_ROOT_CSMSIP/conf


    #copy control scripts
    echo "coping control scripts..."
    cp -rf $AKCS_SRC_ROOT/csbp/script/akcs_control/csmsip/* $AKCS_PACKAGE_ROOT_SCRIPTS/
    cp -rf $AKCS_SRC_ROOT/csbp/script/akcs_control/common_scripts/* $AKCS_PACKAGE_ROOT_SCRIPTS/

    #copy email client
    cp -rf $AKCS_EMAIL_CLIENT $AKCS_PACKAGE_ROOT
	#copy version 在jenkins中生成
	cp -R $AKCS_SRC_ROOT/csmsip_version ${AKCS_PACKAGE_ROOT}

    #个组件均编译成功
    echo "-----------------------all build successful-------------------------"

    #打成tar包
    cd ${AKCS_PACKAGE_ROOT}/../ || exit 1
    rm -rf ${AKCS_PACKAGE_NAME}.tar.gz
    tar zcvf ${AKCS_PACKAGE_NAME}.tar.gz ${AKCS_PACKAGE_NAME}
    echo "${AKCS_PACKAGE_ROOT}/${AKCS_PACKAGE_NAME}.tar.gz is created successful."
}

clean() {
	cd $AKCS_SRC_CSMSIP || exit 1
	rm -rf CMakeCache.txt CMakeFiles cmake_install.cmake
}

print_help() {
	echo "Usage: "
	echo "  $0 clean --- clean csmsip application, eg : $0 clean "
    echo "  $0 build ---  build csmsip application, eg : $0 build "
}

case $1 in
	clean)
    	if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi
		echo "clean all build..."
		clean
		;;
	build)
		if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi

		echo "build..."
		build
		;;
	*)
		print_help
		;;
esac
