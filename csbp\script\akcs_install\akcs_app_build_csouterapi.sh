#!/bin/bash

WORK_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
AKCS_SRC_ROOT=${WORK_DIR}/../../..
AKCS_SRC_CSOUTERAPI=${AKCS_SRC_ROOT}/csouterapi
AKCS_SRC_CSBASE=${AKCS_SRC_ROOT}/csbase
AKCS_SRC_CSBP=${AKCS_SRC_ROOT}/csbp


#在源码包的同一路径下生成安装包
AKCS_PACKAGE_ROOT=${AKCS_SRC_ROOT}/akcs_csouterapi_packeg
AKCS_PACKAGE_ROOT_CSOUTERAPI=${AKCS_PACKAGE_ROOT}/csouterapi
AKCS_PACKAGE_ROOT_SCRIPTS=${AKCS_PACKAGE_ROOT}/csouterapi_scripts

build() {
    #先清理上一次的安装包
    if [ -d $AKCS_PACKAGE_ROOT ]
    then
        rm -rf $AKCS_PACKAGE_ROOT
    fi
    mkdir -p $AKCS_PACKAGE_ROOT_CSOUTERAPI/bin
    mkdir -p $AKCS_PACKAGE_ROOT_CSOUTERAPI/conf
    mkdir -p $AKCS_PACKAGE_ROOT_SCRIPTS
    chmod -R 777 $AKCS_PACKAGE_ROOT/*
    #build csbase
	cd $AKCS_SRC_CSBASE || exit 1
    cmake ./
    make
    if [ $? -eq 0 ]; then
        echo "make csbase successed";
    else
        echo "make csbase failed";
        exit;
    fi

    #build csouterapi
	cd $AKCS_SRC_CSOUTERAPI || exit 1
	cmake ./
    make
    if [ $? -eq 0 ]; then  #即使有告警,也不算是错误
        echo "make csouterapi successed";
    else
        echo "make csouterapi failed";
        exit;
    fi
    cp -f csouterapi  $AKCS_PACKAGE_ROOT_CSOUTERAPI/bin
    cp -f $AKCS_SRC_ROOT/conf/csouterapi.conf  $AKCS_PACKAGE_ROOT_CSOUTERAPI/conf

    #copy control scripts
    echo "coping control scripts..."
    cp -rf $AKCS_SRC_ROOT/csbp/script/akcs_control/csouterapi/* $AKCS_PACKAGE_ROOT_SCRIPTS/
    cp -rf $AKCS_SRC_ROOT/csbp/script/akcs_control/common_scripts/* $AKCS_PACKAGE_ROOT_SCRIPTS/

	#copy version 在jenkins中生成
	cp -R $AKCS_SRC_ROOT/csouterapi_version ${AKCS_PACKAGE_ROOT}

	#svn版本获取
	cd $AKCS_SRC_CSOUTERAPI || exit 1
	svn upgrade
	REV=`svn info | grep 'Last Changed Rev' | awk '{print $4}'`
	sed -i "s/^.*svn_version=.*/svn_version=${REV}/g" $AKCS_PACKAGE_ROOT_CSOUTERAPI/conf/csouterapi.conf

    #个组件均编译成功
    echo "-----------------------all build successful-------------------------"

    #打成tar包
    cd ${AKCS_PACKAGE_ROOT}/../ || exit 1
    rm -rf akcs_csouterapi_packeg.tar.gz
    tar zcvf akcs_csouterapi_packeg.tar.gz akcs_csouterapi_packeg
    echo "${AKCS_PACKAGE_ROOT}/akcs-packages.tar.gz is created successful."
}

clean() {
	cd $AKCS_SRC_CSOUTERAPI || exit 1
	make clean
}

print_help() {
	echo "Usage: "
	echo "  $0 clean --- clean csouterapi application, eg : $0 clean "
    echo "  $0 build ---  build csouterapi application, eg : $0 build "
}

case $1 in
	clean)
    	if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi
		echo "clean all build..."
		clean
		;;
	build)
		if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi

		echo "build..."
		build
		;;
	*)
		print_help
		;;
esac
