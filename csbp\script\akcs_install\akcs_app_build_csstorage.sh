#!/bin/bash

PWD=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
AKCS_SRC_ROOT=${PWD}/../../..
AKCS_SRC_CSSTORAGE=${AKCS_SRC_ROOT}/csstorage
AKCS_SRC_CSBASE=${AKCS_SRC_ROOT}/csbase
AKCS_SRC_CSBP=${AKCS_SRC_ROOT}/csbp

#在源码包的同一路径下生成安装包
AKCS_PACKAGE_ROOT=${AKCS_SRC_ROOT}/akcs_csstorage_packeg
AKCS_PACKAGE_ROOT_CSSTORAGE=${AKCS_PACKAGE_ROOT}/csstorage
AKCS_PACKAGE_ROOT_SCRIPTS=${AKCS_PACKAGE_ROOT}/csstorage_scripts

build() {
    #先清理上一次的安装包
    if [ -d $AKCS_PACKAGE_ROOT ]
    then
        rm -rf $AKCS_PACKAGE_ROOT
    fi
 
    mkdir -p $AKCS_PACKAGE_ROOT_CSSTORAGE/bin
    mkdir -p $AKCS_PACKAGE_ROOT_CSSTORAGE/conf
    mkdir -p $AKCS_PACKAGE_ROOT_CSSTORAGE/lib
    mkdir -p $AKCS_PACKAGE_ROOT_CSSTORAGE/data
    mkdir -p $AKCS_PACKAGE_ROOT_CSSTORAGE/scripts
	mkdir -p $AKCS_PACKAGE_ROOT_CSSTORAGE/offline
	mkdir -p $AKCS_PACKAGE_ROOT_CSSTORAGE/bak_offline
    chmod -R 755 $AKCS_PACKAGE_ROOT_CSSTORAGE/*
	
    #script
    if [ ! -d $AKCS_PACKAGE_ROOT_SCRIPTS ]
    then
        mkdir -p $AKCS_PACKAGE_ROOT_SCRIPTS
        chmod -R 755 $AKCS_PACKAGE_ROOT_SCRIPTS/
    fi 
    chmod -R 777 $AKCS_PACKAGE_ROOT
    #build csbase
	cd $AKCS_SRC_CSBASE
    cmake ./
    make
    if [ $? -eq 0 ]; then
        echo "make csbase successed";
    else
        echo "make csbase failed";
        exit;
    fi

   
    #build csstorage
	cd $AKCS_SRC_CSSTORAGE
    cmake ./
    make
    if [ $? -eq 0 ]; then
        echo "make csstorage successed";
    else
        echo "make csstorage failed";
        exit;
    fi
    cp -f ./release/bin/csstorage  $AKCS_PACKAGE_ROOT_CSSTORAGE/bin
    cp -f $AKCS_SRC_ROOT/conf/csstorage.conf   $AKCS_PACKAGE_ROOT_CSSTORAGE/conf
    cp -f $AKCS_SRC_ROOT/conf/csstorage_fdfs.conf   $AKCS_PACKAGE_ROOT_CSSTORAGE/conf
    

    #copy control scripts
    echo "coping control scripts..."
    cp -rf $AKCS_SRC_CSBP/script/akcs_control/csstorage/* $AKCS_PACKAGE_ROOT_SCRIPTS
    cp -rf $AKCS_SRC_CSBP/script/akcs_control/common_scripts/* $AKCS_PACKAGE_ROOT_SCRIPTS

	cp -f $AKCS_SRC_CSBASE/thirdlib/libevpp.so  $AKCS_PACKAGE_ROOT_CSSTORAGE/lib
    cp -f $AKCS_SRC_CSBASE/thirdlib/libetcd-cpp-api.so  $AKCS_PACKAGE_ROOT_CSSTORAGE/lib

	#copy version 在jenkins中生成
	cp -R $AKCS_SRC_ROOT/csstorage_version ${AKCS_PACKAGE_ROOT}
	
	#svn版本获取
	cd $AKCS_SRC_CSSTORAGE
	svn upgrade
	REV=`svn info | grep 'Last Changed Rev' | awk '{print $4}'`
	sed -i "s/^.*svn_version=.*/svn_version=${REV}/g" $AKCS_PACKAGE_ROOT_CSVRTSP/conf/csstorage.conf

    #个组件均编译成功
    echo "-----------------------all build successful-------------------------"

    #打成tar包
    cd ${AKCS_PACKAGE_ROOT}/../
    rm -rf akcs_csstorage_packeg.tar.gz
    tar zcvf akcs_csstorage_packeg.tar.gz akcs_csstorage_packeg
    echo "${AKCS_PACKAGE_ROOT}/akcs-packages.tar.gz is created successful."
}

clean() {
    cd $AKCS_SRC_CSSTORAGE
	rm -rf CMakeCache.txt CMakeFiles cmake_install.cmake
}

print_help() {
	echo "Usage: "
	echo "  $0 clean --- clean csstorage application, eg : $0 clean "
    echo "  $0 build ---  build csstorage application, eg : $0 build "
}

case $1 in
	clean)
    	if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi
		echo "clean all build..."
		clean
		;;
	build)
		if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi

		echo "build..."
		build 
		;;
	*)
		print_help
		;;
esac
