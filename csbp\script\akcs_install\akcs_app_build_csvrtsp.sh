#!/bin/bash

WORK_DIR=`pwd`
AKCS_SRC_ROOT=${WORK_DIR}/../../..
AKCS_SRC_CSVRTSP=${AKCS_SRC_ROOT}/csvrtsp
AKCS_SRC_CSVRECORD=${AKCS_SRC_ROOT}/csvrecord
AKCS_SRC_CSBASE=${AKCS_SRC_ROOT}/csbase
AKCS_SRC_CSBP=${AKCS_SRC_ROOT}/csbp
AKCS_SRC_SCRIPTS=${AKCS_SRC_ROOT}/scripts
AKCS_SRC_CSMEDIAGATE=${AKCS_SRC_ROOT}/csmediagate

#在源码包的同一路径下生成安装包
AKCS_PACKAGE_ROOT=${AKCS_SRC_ROOT}/akcs_csvrtsp_packeg
AKCS_PACKAGE_ROOT_CSVRTSP=${AKCS_PACKAGE_ROOT}/csvrtsp
AKCS_PACKAGE_ROOT_CSVRECORD=${AKCS_PACKAGE_ROOT}/csvrecord
AKCS_PACKAGE_ROOT_SCRIPTS=${AKCS_PACKAGE_ROOT}/csvrtsp_scripts
AKCS_PACKAGE_ROOT_CSMEDIAGATE=${AKCS_PACKAGE_ROOT}/csmediagate

build() {
    #先清理上一次的安装包
    if [ -d $AKCS_PACKAGE_ROOT ]
    then
        rm -rf $AKCS_PACKAGE_ROOT
    fi
    mkdir -p $AKCS_PACKAGE_ROOT_CSVRTSP/bin
    mkdir -p $AKCS_PACKAGE_ROOT_CSVRTSP/conf
    mkdir -p $AKCS_PACKAGE_ROOT_CSVRTSP/lib
    chmod -R 755 $AKCS_PACKAGE_ROOT_CSVRTSP/*

    mkdir -p $AKCS_PACKAGE_ROOT_CSVRECORD/bin
    mkdir -p $AKCS_PACKAGE_ROOT_CSVRECORD/conf
    mkdir -p $AKCS_PACKAGE_ROOT_CSVRECORD/lib
    chmod -R 755 $AKCS_PACKAGE_ROOT_CSVRECORD/*

    mkdir -p $AKCS_PACKAGE_ROOT_CSMEDIAGATE/bin
    mkdir -p $AKCS_PACKAGE_ROOT_CSMEDIAGATE/conf
    mkdir -p $AKCS_PACKAGE_ROOT_CSMEDIAGATE/lib
	mkdir -p $AKCS_PACKAGE_ROOT_CSMEDIAGATE/scripts
    chmod -R 755 $AKCS_PACKAGE_ROOT_CSMEDIAGATE/*

    #script
    if [ ! -d $AKCS_PACKAGE_ROOT_SCRIPTS ]
    then
        mkdir -p $AKCS_PACKAGE_ROOT_SCRIPTS
        chmod -R 755 $AKCS_PACKAGE_ROOT_SCRIPTS/
    fi
    chmod -R 777 $AKCS_PACKAGE_ROOT
    #build csbase
	cd $AKCS_SRC_CSBASE || exit 1
    cmake ./
    make
    if [ $? -eq 0 ]; then
        echo "make csbase successed";
    else
        echo "make csbase failed";
        exit;
    fi

    #build csvrtsp
	cd $AKCS_SRC_CSVRTSP/build || exit 1
    make
    if [ $? -eq 0 ]; then  #即使有告警,也不算是错误
        echo "make csvrtsp successed";
    else
        echo "make csvrtsp failed";
        exit;
    fi
    cp -f ../bin/*  $AKCS_PACKAGE_ROOT_CSVRTSP/bin
    cp -f ../lib/*  $AKCS_PACKAGE_ROOT_CSVRTSP/lib
    cp -f $AKCS_SRC_ROOT/conf/csvrtsp.conf  $AKCS_PACKAGE_ROOT_CSVRTSP/conf
    cp -f $AKCS_SRC_ROOT/conf/csvrtsp_redis.conf  $AKCS_PACKAGE_ROOT_CSVRTSP/conf

    #build csvrecord
	cd $AKCS_SRC_CSVRECORD/build || exit 1
    make
    if [ $? -eq 0 ]; then
        echo "make csvrecord successed";
    else
        echo "make csvrecord failed";
        exit;
    fi
    cp -f ../bin/csvrecord  $AKCS_PACKAGE_ROOT_CSVRECORD/bin
    cp -f $AKCS_SRC_ROOT/conf/csvrecord.conf  $AKCS_PACKAGE_ROOT_CSVRECORD/conf
	cp -f $AKCS_SRC_ROOT/conf/csvrecord.conf  $AKCS_PACKAGE_ROOT_CSVRECORD/conf

	#build mediagate
	cd $AKCS_SRC_CSMEDIAGATE/build || exit 1
    make
    if [ $? -eq 0 ]; then
        echo "make csmediagate successed";
    else
        echo "make csmediagate failed";
        exit;
    fi
    cp -f ../bin/csmediagate  $AKCS_PACKAGE_ROOT_CSMEDIAGATE/bin
    cp -f ../conf/csmediagate.conf  $AKCS_PACKAGE_ROOT_CSMEDIAGATE/conf
	cp -f ../scripts/*  $AKCS_PACKAGE_ROOT_CSMEDIAGATE/scripts
    cp -rf $AKCS_SRC_ROOT/csbp/script/akcs_control/common_scripts/common.sh $AKCS_PACKAGE_ROOT_CSMEDIAGATE/scripts/

    #copy control scripts
    echo "coping control scripts..."
    cp -rf $AKCS_SRC_ROOT/csbp/script/akcs_control/csvrtsp/* $AKCS_PACKAGE_ROOT_SCRIPTS/
    cp -rf $AKCS_SRC_ROOT/csbp/script/akcs_control/common_scripts/* $AKCS_PACKAGE_ROOT_SCRIPTS/

	#copy version 在jenkins中生成
	cp -R $AKCS_SRC_ROOT/csvrtsp_version ${AKCS_PACKAGE_ROOT}

	#svn版本获取
	cd $AKCS_SRC_CSVRTSP || exit 1
	svn upgrade
	REV=`svn info | grep 'Last Changed Rev' | awk '{print $4}'`
	sed -i "s/^.*svn_version=.*/svn_version=${REV}/g" $AKCS_PACKAGE_ROOT_CSVRTSP/conf/csvrtsp.conf

    #个组件均编译成功
    echo "-----------------------all build successful-------------------------"

    #打成tar包
    cd ${AKCS_PACKAGE_ROOT}/../ || exit 1
    rm -rf akcs_csvrtsp_packeg.tar.gz
    tar zcvf akcs_csvrtsp_packeg.tar.gz akcs_csvrtsp_packeg
    echo "${AKCS_PACKAGE_ROOT}/akcs-packages.tar.gz is created successful."
}

clean() {
	cd $AKCS_SRC_CSVRTSP/build || exit 1
	make clean
    cd $AKCS_SRC_CSVRECORD/build || exit 1
	make clean
}

print_help() {
	echo "Usage: "
	echo "  $0 clean --- clean csvrtsp application, eg : $0 clean "
    echo "  $0 build ---  build csvrtsp application, eg : $0 build "
}

case $1 in
	clean)
    	if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi
		echo "clean all build..."
		clean
		;;
	build)
		if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi

		echo "build..."
		build
		;;
	*)
		print_help
		;;
esac
