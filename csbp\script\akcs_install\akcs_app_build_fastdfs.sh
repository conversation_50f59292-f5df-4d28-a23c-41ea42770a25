#!/bin/bash

WORK_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
BASE_PATH=${WORK_DIR}/../../../
AKCS_PACKAGE_ROOT=${BASE_PATH}/
AKCS_PACKAGE=${AKCS_PACKAGE_ROOT}akcs-fastdfs/
AKCS_PACKAGE_FASTDFS=${AKCS_PACKAGE}/fastdfs


AKCS_BUILD_NO_WITH_FSFD=0

build() {
    rm -rf $AKCS_PACKAGE

    #script

    #fastdfs,直接拷贝docker容器的tar包,以及宿主机上的配置文件, -r递归拷贝
    cd ${WORK_DIR} || exit 1
    mkdir -p $AKCS_PACKAGE_FASTDFS
    cp -fr $BASE_PATH/fastdfs/*  $AKCS_PACKAGE_FASTDFS/
    #cp -rf $BASE_PATH/csbp/script/akcs_control/fastdfs_install.sh $AKCS_PACKAGE_SCRIPTS/

    #个组件均编译成功
    echo "-----------------------all build successful-------------------------"

    #打成tar包
    cd ${AKCS_PACKAGE_ROOT} || exit 1
    rm -rf akcs-fastdfs.tar.gz
    tar zcf akcs-fastdfs.tar.gz akcs-fastdfs

    echo "${AKCS_PACKAGE_ROOT}/akcs-fastdfs.tar.gz be created successful."
}



print_help() {
	echo "Usage: "
    echo "  $0 build ---  build version xx, eg : $0 build "
}

case $1 in
	build)
		if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi

		echo "build..."
		build
		;;
	*)
		print_help
		;;
esac
