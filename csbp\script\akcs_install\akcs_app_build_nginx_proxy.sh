#!/bin/bash

WORK_DIR=`pwd`
AKCS_SRC_ROOT=${WORK_DIR}/../../..
AKCS_SRC_NGINX=${AKCS_SRC_ROOT}/nginx

#在源码包的同一路径下生成安装包
AKCS_PACKAGE_ROOT=${AKCS_SRC_ROOT}/akcs_nginx_proxy_packeg
AKCS_PACKAGE_ROOT_NGINX=${AKCS_PACKAGE_ROOT}/nginx_proxy

build() {
    #先清理上一次的安装包
    if [ -d $AKCS_PACKAGE_ROOT ]
    then
        rm -rf $AKCS_PACKAGE_ROOT
    fi
    mkdir -p $AKCS_PACKAGE_ROOT
    mkdir -p $AKCS_PACKAGE_ROOT_NGINX


    #copy system
    echo "coping  files..."
    cp -rf $AKCS_SRC_NGINX/* $AKCS_PACKAGE_ROOT_NGINX/

	#copy version 在jen<PERSON>中生成
	cp -R $AKCS_SRC_ROOT/nginx_version ${AKCS_PACKAGE_ROOT_NGINX}

    #个组件均编译成功
    echo "-----------------------all build successful-------------------------"

    #打成tar包
    cd ${AKCS_PACKAGE_ROOT}/../ || exit 1
    rm -rf akcs_nginx_proxy_packeg.tar.gz
    tar zcf akcs_nginx_proxy_packeg.tar.gz akcs_nginx_proxy_packeg
    echo "${AKCS_PACKAGE_ROOT}/akcs_nginx_proxy_packeg.tar.gz is created successful."
}

clean() {
    cd $AKCS_SRC_CSSTORAGE || exit 1
	rm -rf CMakeCache.txt CMakeFiles cmake_install.cmake
}

print_help() {
	echo "Usage: "
	echo "  $0 clean --- clean system application, eg : $0 clean "
    echo "  $0 build ---  build system application, eg : $0 build "
}

case $1 in
	clean)
    	if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi
		echo "clean all build..."
		clean
		;;
	build)
		if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi

		echo "build..."
		build
		;;
	*)
		print_help
		;;
esac
