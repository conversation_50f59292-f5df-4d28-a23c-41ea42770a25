#!/bin/bash

WORK_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
AKCS_SRC_ROOT=${WORK_DIR}/../../..
AKCS_SRC_OPENAPI=${AKCS_SRC_ROOT}/openapi
#在源码包的同一路径下生成安装包
AKCS_PACKAGE_ROOT=${AKCS_SRC_ROOT}/akcs_openapi_packeg
AKCS_PACKAGE_ROOT_OPENAPI=${AKCS_PACKAGE_ROOT}/openapi
AKCS_PACKAGE_ROOT_SCRIPTS=${AKCS_PACKAGE_ROOT}/openapi_scripts

build() {
    #先清理上一次的安装包
    if [ -d $AKCS_PACKAGE_ROOT ]
    then
        rm -rf $AKCS_PACKAGE_ROOT
    fi
    mkdir -p $AKCS_PACKAGE_ROOT_OPENAPI
	mkdir -p $AKCS_PACKAGE_ROOT_SCRIPTS
    chmod -R 777 $AKCS_PACKAGE_ROOT/*

	#copy scripts
    echo "coping scripts..."
    cp -rf $AKCS_SRC_ROOT/csbp/script/akcs_control/openapi/* $AKCS_PACKAGE_ROOT_SCRIPTS/

    #copy files
    echo "coping web files..."
    cp -rf $AKCS_SRC_OPENAPI/* $AKCS_PACKAGE_ROOT_OPENAPI/

    find $AKCS_PACKAGE_ROOT_OPENAPI -name .svn |xargs -i rm -rf {}

    #个组件均编译成功
    echo "-----------------------all build successful-------------------------"

    #打成tar包
    cd ${AKCS_PACKAGE_ROOT}/../ || exit 1
    rm -rf akcs_openapi_packeg.tar.gz
    tar zcvf akcs_openapi_packeg.tar.gz akcs_openapi_packeg
    echo "akcs_openapi_packeg.tar.gz is created successful."
}

clean() {
echo "clean successful."
}

print_help() {
	echo "Usage: "
	echo "  $0 clean --- clean system application, eg : $0 clean "
    echo "  $0 build ---  build system application, eg : $0 build "
}

case $1 in
	clean)
    	if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi
		echo "clean all build..."
		clean
		;;
	build)
		if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi

		echo "build..."
		build
		;;
	*)
		print_help
		;;
esac
