#!/bin/bash

WORK_DIR=`pwd`
BASE_PATH=${WORK_DIR}/../../../
AKCS_PACKAGE_CSPUSH=${AKCS_PACKAGE}akcs/cspush
AKCS_PACKAGE_ROOT=${BASE_PATH}/pack_push


build() {
	rm -rf $AKCS_PACKAGE_ROOT
	mkdir $AKCS_PACKAGE_ROOT/akcs -p
	cp ${BASE_PATH}/cspush $AKCS_PACKAGE_ROOT/akcs -R
	#添加启动脚本
	mkdir $AKCS_PACKAGE_ROOT/akcs/scripts
	cp ${BASE_PATH}/csbp/script/akcs_control/cspush.sh $AKCS_PACKAGE_ROOT/akcs/scripts
	cp ${BASE_PATH}/csbp/script/akcs_control/akcs_app_install_push.sh $AKCS_PACKAGE_ROOT
    #打成tar包
    cd ${AKCS_PACKAGE_ROOT} || exit 1
    rm -rf akcs-push.tar.gz
    tar zcf akcs-push.tar.gz akcs akcs_app_install_push.sh
	mv akcs-push.tar.gz ../
    echo "${AKCS_PACKAGE_ROOT}/akcs-push.tar.gz be created successful."
}


print_help() {
	echo "Usage: "
    echo "  $0 build"
}

case $1 in
	build)
		if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi

		echo "build..."
		build
		;;
	*)
		print_help
		;;
esac
