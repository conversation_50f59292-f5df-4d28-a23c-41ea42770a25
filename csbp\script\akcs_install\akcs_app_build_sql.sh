#!/bin/bash

PWD=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
AKCS_SRC_ROOT=${PWD}/../../..
AKCS_SRC_CSBP=${AKCS_SRC_ROOT}/csbp

#在源码包的同一路径下生成安装包
AKCS_PACKAGE_ROOT=${AKCS_SRC_ROOT}/akcs_sql_packeg
AKCS_PACKAGE_ROOT_SCRIPTS=${AKCS_PACKAGE_ROOT}/sql_scripts
AKCS_PACKAGE_ROOT_SQL=${AKCS_PACKAGE_ROOT}/sql

build() {
    #先清理上一次的安装包
    if [ -d $AKCS_PACKAGE_ROOT ]
    then
        rm -rf $AKCS_PACKAGE_ROOT
    fi
    mkdir -p $AKCS_PACKAGE_ROOT_SCRIPTS
    mkdir -p $AKCS_PACKAGE_ROOT_SQL
    chmod -R 777 $AKCS_PACKAGE_ROOT/* 

    echo "coping sql..."
    cp -rf $AKCS_SRC_CSBP/install/* $AKCS_PACKAGE_ROOT_SQL/
	cp -rf $AKCS_SRC_CSBP/script/akcs_control/sql_scripts/* $AKCS_PACKAGE_ROOT_SCRIPTS/
	
    #个组件均编译成功
    echo "-----------------------all build successful-------------------------"

    #打成tar包
    cd ${AKCS_PACKAGE_ROOT}/../
    rm -rf akcs_sql_packeg.tar.gz
    tar zcvf akcs_sql_packeg.tar.gz akcs_sql_packeg
    echo "${AKCS_PACKAGE_ROOT}/akcs-packages.tar.gz is created successful."
}

clean() {
echo "clean successful."
}

print_help() {
	echo "Usage: "
	echo "  $0 clean --- clean sql application, eg : $0 clean "
    echo "  $0 build ---  build sql application, eg : $0 build "
}

case $1 in
	clean)
    	if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi
		echo "clean all build..."
		clean
		;;
	build)
		if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi

		echo "build..."
		build 
		;;
	*)
		print_help
		;;
esac
