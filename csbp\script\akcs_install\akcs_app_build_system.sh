#!/bin/bash

WORK_DIR=`pwd`
AKCS_SRC_ROOT=${WORK_DIR}/../../..
AKCS_SRC_SYSTEM=${AKCS_SRC_ROOT}/system
AKCS_SRC_CSBP=${AKCS_SRC_ROOT}/csbp
AKCS_SRC_CONF=${AKCS_SRC_ROOT}/conf
#在源码包的同一路径下生成安装包
AKCS_PACKAGE_ROOT=${AKCS_SRC_ROOT}/akcs_system_packeg
AKCS_PACKAGE_ROOT_SYSTEM=${AKCS_PACKAGE_ROOT}/system
AKCS_PACKAGE_ROOT_SCRIPTS=${AKCS_PACKAGE_ROOT}/system_scripts
AKCS_PACKAGE_ROOT_SQL=${AKCS_PACKAGE_ROOT}/sql
AKCS_PACKAGE_ROOT_CONF=${AKCS_PACKAGE_ROOT}/conf

build() {
    #先清理上一次的安装包
    if [ -d $AKCS_PACKAGE_ROOT ]
    then
        rm -rf $AKCS_PACKAGE_ROOT
    fi
    mkdir -p $AKCS_PACKAGE_ROOT_SYSTEM
    mkdir -p $AKCS_PACKAGE_ROOT_SCRIPTS
    mkdir -p $AKCS_PACKAGE_ROOT_SQL
    mkdir -p $AKCS_PACKAGE_ROOT_CONF
    chmod -R 777 $AKCS_PACKAGE_ROOT/*

    #copy system
    echo "coping system files..."
    cp -rf $AKCS_SRC_SYSTEM/* $AKCS_PACKAGE_ROOT_SYSTEM/

    echo "coping sql..."
    cp -rf $AKCS_SRC_CSBP/install/* $AKCS_PACKAGE_ROOT_SQL/

    #copy control scripts
    echo "coping control scripts..."
    cp -rf $AKCS_SRC_ROOT/csbp/script/akcs_control/* $AKCS_PACKAGE_ROOT_SCRIPTS/
    cp -rf $AKCS_SRC_ROOT/csbp/script/akcs_control/common_scripts/* $AKCS_PACKAGE_ROOT_SCRIPTS/system

    echo "coping conf..."
	#TODO这些应该是在web里面的
    cp -rf $AKCS_SRC_ROOT/conf/000000000001.cfg $AKCS_PACKAGE_ROOT_CONF/
    cp -rf $AKCS_SRC_ROOT/conf/000000000100.xml $AKCS_PACKAGE_ROOT_CONF/
	cp -rf $AKCS_SRC_ROOT/conf/000000000010.xml $AKCS_PACKAGE_ROOT_CONF/
    cp -rf $AKCS_SRC_ROOT/conf/000000000011.json $AKCS_PACKAGE_ROOT_CONF/
	cp -rf $AKCS_SRC_ROOT/conf/000000000111.json $AKCS_PACKAGE_ROOT_CONF/

	#copy version 在jenkins中生成
	cp -R $AKCS_SRC_ROOT/system_version ${AKCS_PACKAGE_ROOT}

    #个组件均编译成功
    echo "-----------------------all build successful-------------------------"

    #打成tar包
    cd ${AKCS_PACKAGE_ROOT}/../ || exit 1
    rm -rf akcs_system_packeg.tar.gz
    tar zcvf akcs_system_packeg.tar.gz akcs_system_packeg
    echo "${AKCS_PACKAGE_ROOT}/akcs-packages.tar.gz is created successful."
}

clean() {
echo "clean successful."
}

print_help() {
	echo "Usage: "
	echo "  $0 clean --- clean system application, eg : $0 clean "
    echo "  $0 build ---  build system application, eg : $0 build "
}

case $1 in
	clean)
    	if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi
		echo "clean all build..."
		clean
		;;
	build)
		if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi

		echo "build..."
		build
		;;
	*)
		print_help
		;;
esac
