#!/bin/bash

WORK_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
AKCS_SRC_ROOT=${WORK_DIR}/../../..
AKCS_SRC_SLIM=${AKCS_SRC_ROOT}/slim
AKCS_SRC_WEB=${AKCS_SRC_ROOT}/web/web

AKCS_SRC_WEB_MANAGE=${AKCS_SRC_ROOT}/web/AKCS3.0/manage
AKCS_SRC_WEB_MANAGE_NEW=${AKCS_SRC_ROOT}/web/cloud-vue-3.0/manage-new
AKCS_SRC_WEB_DIST=${AKCS_SRC_ROOT}/web/AKCS3.0-Person/dist

AKCS_SRC_WEB_MANAGE_DIR=${AKCS_SRC_ROOT}/web/AKCS3.0
AKCS_SRC_WEB_MANAGE_NEW_DIR=${AKCS_SRC_ROOT}/web/cloud-vue-3.0
AKCS_SRC_WEB_DIST_DIR=${AKCS_SRC_ROOT}/web/AKCS3.0-Person

AKCS_SRC_WEB_VBELL=${AKCS_SRC_ROOT}/web/VBell/VBell
AKCS_SRC_WEB_SMARTPLUS=${AKCS_SRC_ROOT}/web/SmartPlus2/smartplus
AKCS_SRC_WEB_SMARTPLUS_BUILD=${AKCS_SRC_ROOT}/web/SmartPlus2/src
AKCS_SRC_WEB_WECHAT=${AKCS_SRC_ROOT}/web/wechat/WXSmartPlus


AKCS_SRC_WEB_SMARTPLUS_HOME=${AKCS_SRC_ROOT}/web/SmartPlus-home/smartplus-home
AKCS_SRC_WEB_SMARTPLUS_HOME_BUILD=${AKCS_SRC_ROOT}/web/SmartPlus-home/src

#task
AKCS_SRC_WEB_TASK_DIR=${AKCS_SRC_WEB}/../async-task
AKCS_SRC_WEB_TASK_DIST_DIR=${AKCS_SRC_WEB}/../async-task/async-task
AKCS_SRC_WEB_TASK_SCRIPT_DIR=${AKCS_SRC_WEB}/../async-task/script

#在源码包的同一路径下生成安装包
AKCS_PACKAGE_ROOT=${AKCS_SRC_ROOT}/akcs_web_packeg
AKCS_PACKAGE_ROOT_WEBROOT=${AKCS_PACKAGE_ROOT}/webroot
AKCS_PACKAGE_ROOT_DOWNLOAD=${AKCS_PACKAGE_ROOT}/download
AKCS_PACKAGE_ROOT_SCRIPTS=${AKCS_PACKAGE_ROOT}/web_scripts
AKCS_PACKAGE_ROOT_TASK=${AKCS_PACKAGE_ROOT}/async-task
build() {
    #先清理上一次的安装包
    if [ -d $AKCS_PACKAGE_ROOT ]
    then
        rm -rf $AKCS_PACKAGE_ROOT
    fi
    mkdir -p $AKCS_PACKAGE_ROOT_WEBROOT
	mkdir -p $AKCS_PACKAGE_ROOT_DOWNLOAD
	mkdir -p $AKCS_PACKAGE_ROOT_SCRIPTS
	mkdir -p $AKCS_PACKAGE_ROOT_TASK
    chmod -R 777 $AKCS_PACKAGE_ROOT/*
	
	#检测词条
	php_lang="$AKCS_SRC_WEB/webroot/web-server/share/lang $AKCS_SRC_WEB/webroot/apache-v3.0/lang"
	for dir in $php_lang
	do
		echo "$dir"
		php detect_language.php  $dir
		if [ $? -ne 0 ];then
			echo "detect_language.php $dir check fail."
			exit -1;
		fi		
	done
	
	js_lang="$AKCS_SRC_WEB_SMARTPLUS_BUILD/../public/js/lang
	$AKCS_SRC_WEB_MANAGE_NEW_DIR/public/js/lang"
	for dir in $js_lang
	do
		echo "$dir"
		node detect_language.js  $dir
		if [ $? -ne 0 ];then
			echo "detect_language.js $dir check fail."
			exit -1;
		fi		
	done	

	cd $AKCS_SRC_WEB_MANAGE_DIR || exit 1
	npm run build
	if [ $? -ne 0 ];then
		exit 1
	fi

	cd $AKCS_SRC_WEB_MANAGE_NEW_DIR || exit 1
	npm i
	npm run build
	if [ $? -ne 0 ];then
		exit 1
	fi

	cd $AKCS_SRC_WEB_DIST_DIR || exit 1
	npm run build
	if [ $? -ne 0 ];then
		exit 1
	fi

	cd $AKCS_SRC_WEB_SMARTPLUS_BUILD || exit 1
	npm i
	npm run build
	if [ $? -ne 0 ];then
		exit 1
	fi

	cd $AKCS_SRC_WEB_SMARTPLUS_HOME_BUILD || exit 1
	npm run build
	if [ $? -ne 0 ];then
		exit 1
	fi

	cd $AKCS_SRC_WEB_TASK_DIR || exit 1
	npm run build
	if [ $? -ne 0 ];then
		exit 1
	fi

	#copy control scripts
    echo "coping control scripts..."
    cp -rf $AKCS_SRC_ROOT/csbp/script/akcs_control/web/* $AKCS_PACKAGE_ROOT_SCRIPTS/
    cp -rf $AKCS_SRC_ROOT/csbp/script/akcs_control/common_scripts/* $AKCS_PACKAGE_ROOT_SCRIPTS/

    #copy web slim files
    echo "coping web files..."
    mkdir -p $AKCS_PACKAGE_ROOT_WEBROOT/VBell
    cp -rf $AKCS_SRC_WEB_VBELL/* $AKCS_PACKAGE_ROOT_WEBROOT/VBell
	mkdir -p $AKCS_PACKAGE_ROOT_WEBROOT/smartplus
    cp -rf $AKCS_SRC_WEB_SMARTPLUS/* $AKCS_PACKAGE_ROOT_WEBROOT/smartplus
	
	mkdir -p $AKCS_PACKAGE_ROOT_WEBROOT/smartplus-home
    cp -rf $AKCS_SRC_WEB_SMARTPLUS_HOME/* $AKCS_PACKAGE_ROOT_WEBROOT/smartplus-home
	
    cp -rf $AKCS_SRC_WEB/webroot/* $AKCS_PACKAGE_ROOT_WEBROOT/
	rm -R $AKCS_PACKAGE_ROOT_WEBROOT/web-server/test
	rm -R $AKCS_PACKAGE_ROOT_WEBROOT/apache-v3.0/test
	#拷贝func, 后台一份代码，但是前端有多个地方引用
	cp -rf $AKCS_PACKAGE_ROOT_WEBROOT/apache-v3.0/notify/* $AKCS_PACKAGE_ROOT_WEBROOT/web-server/share/notify
	cp -rf $AKCS_SRC_WEB/download/* $AKCS_PACKAGE_ROOT_DOWNLOAD/
    cp -rf $AKCS_SRC_SLIM $AKCS_PACKAGE_ROOT_WEBROOT/
    cp -rf $AKCS_SRC_WEB_DIST $AKCS_PACKAGE_ROOT_WEBROOT/
    cp -rf $AKCS_SRC_WEB_MANAGE $AKCS_PACKAGE_ROOT_WEBROOT/
    cp -rf $AKCS_SRC_WEB_MANAGE_NEW $AKCS_PACKAGE_ROOT_WEBROOT/
	
	#task
	cp -rf $AKCS_SRC_WEB_TASK_DIST_DIR/* $AKCS_PACKAGE_ROOT_TASK
	cp -rf $AKCS_SRC_WEB_TASK_SCRIPT_DIR $AKCS_PACKAGE_ROOT_TASK
	
	#mysmart 隐私政策放着云上管理,app固定访问scloud
    mkdir -p $AKCS_PACKAGE_ROOT_WEBROOT/MySmartPrivacyPolicy
    cp -rf $AKCS_SRC_WEB/MySmartPrivacyPolicy/* $AKCS_PACKAGE_ROOT_WEBROOT/MySmartPrivacyPolicy

	#
	mkdir -p $AKCS_PACKAGE_ROOT_WEBROOT/WXSmartPlus
	cp -rf $AKCS_SRC_WEB_WECHAT/* $AKCS_PACKAGE_ROOT_WEBROOT/WXSmartPlus

    find $AKCS_PACKAGE_ROOT_WEBROOT -name .svn |xargs -i rm -rf {}
	find $AKCS_PACKAGE_ROOT_TASK -name .svn |xargs -i rm -rf {}

	#copy version 在jenkins中生成
	cp -R $AKCS_SRC_ROOT/system_version ${AKCS_PACKAGE_ROOT}

    #个组件均编译成功
    echo "-----------------------all build successful-------------------------"

    #打成tar包
    cd ${AKCS_PACKAGE_ROOT}/../ || exit 1
    rm -rf akcs_web_packeg.tar.gz
    tar zcvf akcs_web_packeg.tar.gz akcs_web_packeg
    echo "${AKCS_PACKAGE_ROOT}/akcs-packages.tar.gz is created successful."
}

clean() {
echo "clean successful."
}

print_help() {
	echo "Usage: "
	echo "  $0 clean --- clean system application, eg : $0 clean "
    echo "  $0 build ---  build system application, eg : $0 build "
}

case $1 in
	clean)
    	if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi
		echo "clean all build..."
		clean
		;;
	build)
		if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi

		echo "build..."
		build
		;;
	*)
		print_help
		;;
esac
