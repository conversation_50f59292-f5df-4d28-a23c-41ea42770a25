#!/bin/bash

WORK_DIR=$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)
BASE_PATH=${WORK_DIR}/../../../
CSBASE_PATH=csbase
CSVS_PATH=csvs
AKCS_PACKAGE_ROOT=${BASE_PATH}/
AKCS_PACKAGE=${AKCS_PACKAGE_ROOT}csvs-packages/
AKCS_PACKAGE_CSVS=${AKCS_PACKAGE}akcs/csvs
AKCS_PACKAGE_SCRIPTS=${AKCS_PACKAGE}akcs/scripts
AKCS_PACKAGE_LIBS=${AKCS_PACKAGE}akcs/libs
AKCS_PACKAGE_FFMPEG_BIN=${AKCS_PACKAGE_CSVS}/ffmpeg/bin
AKCS_PACKAGE_SQL=${AKCS_PACKAGE}akcs/sql


build() {
    rm -rf $AKCS_PACKAGE
    if [ -d $AKCS_PACKAGE_CSVS ]
    then
        rm -rf $AKCS_PACKAGE_CSVS
    fi
    mkdir -p $AKCS_PACKAGE_CSVS/bin
    mkdir -p $AKCS_PACKAGE_CSVS/conf
    mkdir -p $AKCS_PACKAGE_CSVS/lib
    mkdir -p $AKCS_PACKAGE_CSVS/ffmpeg/bin
    chmod -R 755 $AKCS_PACKAGE_CSVS/*


    #script
    if [ ! -d $AKCS_PACKAGE_SCRIPTS ]
    then
        rm -rf $AKCS_PACKAGE_SCRIPTS
        mkdir -p $AKCS_PACKAGE_SCRIPTS
        chmod -R 755 $AKCS_PACKAGE_SCRIPTS/
    fi

    #sql打包位置
    if [ -d $AKCS_PACKAGE_SQL ]
    then
        rm -rf $AKCS_PACKAGE_SQL
    fi
    mkdir -p $AKCS_PACKAGE_SQL
    chmod -R 755 $AKCS_PACKAGE_SQL/

    if [ ! -d $AKCS_PACKAGE_LIBS ]
    then
        rm -rf $AKCS_PACKAGE_LIBS
        mkdir -p $AKCS_PACKAGE_LIBS
    fi

    if [ ! -d $AKCS_PACKAGE_WEBROOT ]
    then
        rm -rf $AKCS_PACKAGE_WEBROOT
        mkdir -p $AKCS_PACKAGE_WEBROOT
    fi

    #build csbase
    CSBASE=$BASE_PATH/$CSBASE_PATH
	cd $CSBASE || exit 1
    cmake ./
    make
    if [ $? -eq 0 ]; then
        echo "make csbase successed";
    else
        echo "make csbase failed";
        exit;
    fi

    #build csvs
    CSVS=$BASE_PATH/$CSVS_PATH
	cd $CSVS || exit 1
    cmake ./
    make
    if [ $? -eq 0 ]; then
        echo "make csvs successed";
    else
        echo "make csvs failed";
        exit;
    fi
    cp -f ./csvs  $AKCS_PACKAGE_CSVS/bin
    cp -f $BASE_PATH/conf/csvs.conf   $AKCS_PACKAGE_CSVS/conf

    #copy scripts
    echo "coping sql..."
    cp -rf $BASE_PATH/csbp/script/akcs_control/csvs/* $AKCS_PACKAGE_SCRIPTS/

    #copy sql
    echo "coping sql..."
    cp -rf $BASE_PATH/csbp/install/csvs.sql $AKCS_PACKAGE_SQL/

    #copy libs
    echo "coping libs..."
    cp -f $BASE_PATH/csbase/thirdlib/libevent.so $AKCS_PACKAGE_LIBS/
    cp -f $BASE_PATH/csbase/thirdlib/libglog.so $AKCS_PACKAGE_LIBS/
    cp -f $BASE_PATH/csbase/thirdlib/libmysqlclient.so $AKCS_PACKAGE_LIBS/
    cp -f $BASE_PATH/csbase/thirdlib/libgpr.so.6 $AKCS_PACKAGE_LIBS/
    cp -f $BASE_PATH/csbase/thirdlib/libgrpc.so.6 $AKCS_PACKAGE_LIBS/
    cp -f $BASE_PATH/csbase/thirdlib/libgrpc++.so.1 $AKCS_PACKAGE_LIBS/
    cp -f $BASE_PATH/csbase/thirdlib/libprotobuf.so.15 $AKCS_PACKAGE_LIBS/
    cp -f $BASE_PATH/csbase/evpp/lib/* $AKCS_PACKAGE_LIBS/

    #copy ffmpeg
    echo "coping ffmpeg..."
    cp -f $BASE_PATH/csbase/thirdlib/ffmpeg/bin/ffmpeg $AKCS_PACKAGE_CSVS/ffmpeg/bin/

    #个组件均编译成功
    echo "-----------------------csvs build successful-------------------------"

    #打成tar包
    cd ${AKCS_PACKAGE_ROOT} || exit 1
    rm -rf csvs-packages.tar.gz
    tar zcf csvs-packages.tar.gz csvs-packages

    echo "${AKCS_PACKAGE_ROOT}/csvs-packages.tar.gz be created successful."
}

clean() {
	cd $BASE_PATH/$CSVS_PATH/build || exit 1
	rm -rf CMakeCache.txt CMakeFiles cmake_install.cmake
}

print_help() {
	echo "Usage: "
	echo "  $0 clean "
    echo "  $0 build  "
}

case $1 in
	clean)
    	if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi
		echo "clean all build..."
		clean
		;;
	build)
		if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi

		echo "build..."
		build
		;;
	*)
		print_help
		;;
esac
