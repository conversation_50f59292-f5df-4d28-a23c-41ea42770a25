var arguments = process.argv.splice(2)[0];
// sync version
function walkSync(currentDirPath, callback) {
    var fs = require('fs'),
        path = require('path');
    fs.readdirSync(currentDirPath).forEach(function (name) {
        var filePath = path.join(currentDirPath, name);
        var stat = fs.statSync(filePath);
        if (stat.isFile()) {
            callback(filePath, stat);
        } else if (stat.isDirectory()) {
            walkSync(filePath, callback);
        }
    });
}
walkSync(arguments, function(filePath, stat) {
    // do something with "filePath"...a
	if (filePath.endsWith('.js') && !filePath.endsWith('Text.js') && !filePath.endsWith('store.js'))
	{
		require(filePath);
		//console.log(filePath)
	}
});
