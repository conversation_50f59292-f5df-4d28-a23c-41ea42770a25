<?php
/*检测目录下的php文件是否合法*/

function endsWith($haystack, $needle)
{
    $length = strlen($needle);
    if ($length == 0) {
        return true;
    }

    return (substr($haystack, -$length) === $needle);
}


if ($argc != 2)
{
	return false;
}
$dir = $argv[1];


if(!is_dir($dir)) return false;

$handle = opendir($dir);

if($handle){
	while(($fl = readdir($handle)) !== false){
		$temp = $dir.DIRECTORY_SEPARATOR.$fl;
		//如果不加  $fl!='.' && $fl != '..'  则会造成把$dir的父级目录也读取出来
		if(is_dir($temp) && $fl!='.' && $fl != '..'){
			echo '目录：'.$temp.'/n';
			
		}else{
			if($fl!='.' && $fl != '..' && $fl != 'check_entry.php' && endsWith($fl, ".php")){
				@require_once ($temp);
			}
		}
	}
}
