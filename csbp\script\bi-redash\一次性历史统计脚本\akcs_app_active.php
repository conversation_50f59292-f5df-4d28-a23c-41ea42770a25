<?php
date_default_timezone_set('PRC');
require('./akcs_dw_config.php');
//区域
$REGION = null;
if ($argc == 2) {
    $REGION = $argv[1];
} else {
    echo 'use: akcs_dw_month.php  usa';
    exit;
}

//每月新增激活家庭数
function ActiveAppNum($REGION)
{
    //从业务数据库里面查询数据
    $ods_db = getODSDB();
    $dw_db = getDWDB();

    $year_months = array("2019-09-01 00:00:00","2019-10-01 00:00:00","2019-11-01 00:00:00","2019-12-01 00:00:00","2020-01-01 00:00:00","2020-02-01 00:00:00","2020-03-01 00:00:00","2020-04-01 00:00:00","2020-05-01 00:00:00","2020-06-01 00:00:00"); 
    foreach ($year_months as $year_month)
    {        
        $timestart = $year_month;
        $timestart_t = strtotime($timestart);//转成时间戳
        $timeend_t = strtotime("first day of +1 month",$timestart_t);//不能用 + 1 months的形式
        $timeend = date("Y-m-d 00:00:00", $timeend_t);
        $sth_act_family = $ods_db->prepare("select count(1) as count from PersonalAccount where ((ActiveTime between '".$timestart."' and '".$timeend."') or (ActiveTime is NULL and CreateTime between '".$timestart."' and '".$timeend."')) and Active = 1;");

        $sth_act_family->execute();
        $resultRole = $sth_act_family->fetch(PDO::FETCH_ASSOC);
        $family_active_num = $resultRole['count'];
        
        //从 2019-09-01 00:00:00 改成 2019-09
        $year_month = substr($year_month,0,7);
        //UNIQUE KEY `region_data` (`Region`,`DateTime`)  对应的表格已经通过唯一键来保证只会插入一次，后续的都是更新
        $sth = $dw_db->prepare("INSERT INTO  GlobalActiveApp(`Region`,`DateTime`,`Num`) VALUES (:region, :time, :family_active_num) ON DUPLICATE KEY UPDATE Num = :family_active_num");
        $sth->bindParam(':family_active_num', $family_active_num, PDO::PARAM_INT);
        $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
        $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
        $sth->execute(); 
    }
}
ActiveAppNum($REGION);

?>
