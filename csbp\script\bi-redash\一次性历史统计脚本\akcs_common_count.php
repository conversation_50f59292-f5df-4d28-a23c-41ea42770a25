<?php
date_default_timezone_set('PRC');
require('/home/<USER>/akcs_dw_config.php');
//区域
$REGION = null;
if ($argc == 2) {
    $REGION = $argv[1];
} else {
    echo 'use: akcs_common_count.php USA';
    exit;
}

//查询不需要统计的dis
$dis_remove_top_list = [];
$ods_db = null;
$dw_server_db = null;
if($REGION == 'USA')
{
    $dw_server_db = getUSADWDB();
}
else if($REGION == 'EUR')
{
    $dw_server_db = getEURDWDB();
}
else if($REGION == 'ASIA')
{
    $dw_server_db = getASIADWDB();
}
else if($REGION == 'JPN')
{
    $dw_server_db = getJPNDWDB();
}
else if($REGION == 'LOCAL')
{
    $dw_server_db = getLOCALDWDB();
}
if (null !== $dw_server_db) {
    $ods_db = getODSDB();
    $sth_dis = $dw_server_db->prepare("select Dis from DisListRemove;");
    $sth_dis->execute();
    $dis_list = $sth_dis->fetchALL(PDO::FETCH_ASSOC);
    foreach ($dis_list as $row => $dis)
    {
        $dis_acc = $dis['Dis'];
        $sth = $ods_db->prepare("select ID from Account where Account = :dis and Grade = 11");
        $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
        $sth->execute();
        $dis_id = $sth->fetch(PDO::FETCH_ASSOC)['ID'];//fetch 不是 fetchALL
        if (empty($dis_id)) {
            continue;
        }
        $dis_remove_top_list[$dis_acc] = $dis_id;
    }
}

//全球每月业务数据量统计
function CommonCountNum($REGION, $year_month)
{
    //从业务数据库里面查询数据
    $ods_db = getODSDB();

    global $dis_remove_top_list;
    $disRemoveIds = join(',', $dis_remove_top_list);

    $timeend = date("Y-m-t 23:59:59", strtotime($year_month));//本月最后一天

    //单住户总数量
    $sth = $ods_db->prepare("select count(1) as num from PersonalAccount where Role = 10 and Active = 1 and Special = 0 and ActiveTime <= '$timeend';");
    $sth->execute();
    $num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    if (!empty($disRemoveIds)) {
        $sth = $ods_db->prepare("select count(1) as num from PersonalAccount P left join Account A on A.ID = P.ParentID left join Account B on B.ID = A. ParentID where (B.ID in ({$disRemoveIds})) and P.Role = 10 and P.Active = 1 and P.Special = 0 and P.ActiveTime <= '$timeend';");
        $sth->execute();
        $remove_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        $num = $num - $remove_num;
    }
    AddCommonCountData($REGION, $year_month, 1, $num);

    //单住户门口机当月总数量
    $sth = $ods_db->prepare("select count(1) as num From PersonalDevices where (Type = 0 or Type = 1) and CreateTime <= '$timeend'");
    $sth->execute();
    $num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    if (!empty($disRemoveIds)) {
        $sth = $ods_db->prepare("select count(1) as num From PersonalDevices D left join Account A on A.Account = D.Community left join Account B on B.ID = A.ParentID where B.ID in ({$disRemoveIds}) and (D.Type = 0 or D.Type = 1) and D.CreateTime <= '$timeend'");
        $sth->execute();
        $remove_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        $num = $num - $remove_num;
    }
    AddCommonCountData($REGION, $year_month, 2, $num);

    //单住户室内机当月总数量
    $sth = $ods_db->prepare("select count(1) as num From PersonalDevices where Type = 2 and CreateTime <= '$timeend'");
    $sth->execute();
    $num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    if (!empty($disRemoveIds)) {
        $sth = $ods_db->prepare("select count(1) as num From PersonalDevices D left join Account A on A.Account = D.Community left join Account B on B.ID = A.ParentID where B.ID in ({$disRemoveIds}) and D.Type = 2 and D.CreateTime <= '$timeend'");
        $sth->execute();
        $remove_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        $num = $num - $remove_num;
    }
    AddCommonCountData($REGION, $year_month, 3, $num);

    //多住户总数量
    $sth = $ods_db->prepare("select count(1) as num from PersonalAccount where Role = 20 and Active = 1 and Special = 0 and ActiveTime <= '$timeend';");
    $sth->execute();
    $num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    if (!empty($disRemoveIds)) {
        $sth = $ods_db->prepare("select count(1) as num from PersonalAccount P left join Account A on A.ID = P.ParentID left join Account B on B.ID = A. ParentID where (B.ID in ({$disRemoveIds})) and P.Role = 20 and P.Active = 1 and P.Special = 0 and P.ActiveTime <= '$timeend';");
        $sth->execute();
        $remove_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        $num = $num - $remove_num;
    }
    AddCommonCountData($REGION, $year_month, 4, $num);

    //多住户门口机当月总数量
    $sth = $ods_db->prepare("select count(1) as num From Devices where (Type = 0 or Type = 1) and CreateTime <= '$timeend'");
    $sth->execute();
    $num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    if (!empty($disRemoveIds)) {
        $sth = $ods_db->prepare("select count(1) as num From Devices D left join Account A on A.ID = D.MngAccountID left join Account B on B.ID = A.ParentID where B.ID in ({$disRemoveIds}) and (D.Type = 0 or D.Type = 1) and D.CreateTime <= '$timeend'");
        $sth->execute();
        $remove_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        $num = $num - $remove_num;
    }
    AddCommonCountData($REGION, $year_month, 5, $num);

    //多住户室内机当月总数量
    $sth = $ods_db->prepare("select count(1) as num From Devices where Type = 2 and CreateTime <= '$timeend'");
    $sth->execute();
    $num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    if (!empty($disRemoveIds)) {
        $sth = $ods_db->prepare("select count(1) as num From Devices D left join Account A on A.ID = D.MngAccountID left join Account B on B.ID = A.ParentID where B.ID in ({$disRemoveIds}) and D.Type = 2 and D.CreateTime <= '$timeend'");
        $sth->execute();
        $remove_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        $num = $num - $remove_num;
    }
    AddCommonCountData($REGION, $year_month, 6, $num);
}

//通用插入方法
function AddCommonCountData($region, $datetime, $type, $num)
{
    $dw_db = getDWDB();
    $sth = $dw_db->prepare("INSERT INTO  GlobalCommonCount(`Region`,`DateTime`,`DataType`,`Num`) VALUES (:region, :datetime, :type, :num) ON DUPLICATE KEY UPDATE Num = :num");
    $sth->bindParam(':region', $region, PDO::PARAM_STR);
    $sth->bindParam(':datetime', $datetime, PDO::PARAM_STR);
    $sth->bindParam(':num', $num, PDO::PARAM_INT);
    $sth->bindParam(':type', $type, PDO::PARAM_INT);
    $sth->execute();
}

$months = ['2022-01', '2022-02', '2022-03', '2022-04', '2022-05', '2022-06', '2022-07', '2022-08', '2022-09'];
foreach ($months as $month) {
    CommonCountNum($REGION, $month);
}
echo "end\n";
?>
