<?php
date_default_timezone_set('PRC');
require('/home/<USER>/akcs_dw_config.php');
//区域
$REGION = null;
if ($argc == 2) {
    $REGION = $argv[1];
} else {
    echo 'use: akcs_dw_month.php  usa/eur/asia';
    exit;
}
 $dis_top_list = null;
 $dw_db = null;
 $ods_db = null;
if($REGION == 'USA')
{
    $dw_db = getUSADWDB();
}
else if($REGION == 'EUR')
{
    $dw_db = getEURDWDB();
}
else if($REGION == 'ASIA')
{
    $dw_db = getASIADWDB();
}
else if($REGION == 'LOCAL')
{
    $dw_db = getLOCALDWDB();    
}
$ods_db = getODSDB();

//查询最大项目数量的dis
$sth_dis = $dw_db->prepare("select Dis,sum(Num) as pro_count from DisProjectSize group by Dis order by pro_count desc limit 20;");
$sth_dis->execute();
$dis_list = $sth_dis->fetchALL(PDO::FETCH_ASSOC);
foreach ($dis_list as $row => $dis)
{
    $dis_acc = $dis['Dis'];
    $sth = $ods_db->prepare("select ID from Account where Account = :dis and Grade = 11");
    $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
    $sth->execute();
    $dis_id = $sth->fetch(PDO::FETCH_ASSOC)['ID'];//fetch 不是 fetchALL
    $dis_top_list[$dis_acc] = $dis_id;
}

function OpenDoorNum()
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;
    $table_name = 'PersonalCapture';    
    $year_months = array("201909","201910","201911","201912","202001","202002","202003","202004","202005","202006"); 
    foreach ($year_months as $year_month)
    {
        $ym_table = $table_name."_".$year_month;
        //从 YYYYMM 改成 YYYY-MM
        $year = substr($year_month,0,4);
        $month = substr($year_month,4);
        $year_month = $year.'-'.$month;
        foreach ($dis_top_list as $dis_acc => $dis_id)
        {      
            $sth = $ods_db->prepare("select count(*) as num from " .$ym_table. " C left join Account A on A.ID = C.MngAccountID left join Account B on B.ID = A.ParentID where B.ID = :id and C.CaptureType < 102");
            $sth->bindParam(':id', $dis_id, PDO::PARAM_INT);
            $sth->execute();
            $opendoor_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
            
            $sth = $dw_db->prepare("INSERT INTO  DisOpenDoor(`Dis`,`DateTime`,`Num`) VALUES (:dis, :time, :opendoor_num) ON DUPLICATE KEY UPDATE Num = :opendoor_num");
            $sth->bindParam(':opendoor_num', $opendoor_num, PDO::PARAM_INT);
            $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
            $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
            $sth->execute(); 
        }
    }
}

OpenDoorNum();
?>
