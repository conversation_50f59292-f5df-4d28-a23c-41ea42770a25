<?php
date_default_timezone_set('PRC');
require('./akcs_dw_config.php');
//区域
$REGION = null;
if ($argc == 2) {
    $REGION = $argv[1];
} else {
    echo 'use: akcs_dw_month.php  usa';
    exit;
}

//每月新增开门数
function DoorNum($REGION)
{
    //从业务数据库里面查询数据
    $ods_db = getODSDB();
    $dw_db = getDWDB();
    $table_name = 'PersonalCapture';
    //$year_months = array("201909","201910","201911","201912","202001","202002","202003","202004","202005"); 
    
    $year_months = array("201909","201910","201911","201912","202001","202002","202003","202004","202005"); 
    foreach ($year_months as $year_month)
    {
        $ym_table = $table_name."_".$year_month;
        
        
                $ym_table = $table_name."_".$year_month;
        if($ym_table == 'CallHistory_201909')
        {
            //剔除掉工具的测试次数:
            //美国：select count(*) FROM CallHistory_201909 where Node != '300100000' and StartTime > '2019-09-01 00:00:00';
            //欧洲：select count(*) FROM CallHistory_201909 where Node != '100100000' and StartTime > '2019-09-01 00:00:00';
            //亚洲：select count(*) FROM CallHistory_201909 where Node != '800100000' and StartTime > '2019-09-01 00:00:00';
            //所以 后面干脆把201909这个月份去掉了，这样就不用再考虑这个东西了
            $sth = $ods_db->prepare("select count(*) as num from " .$ym_table. " where StartTime > '2019-09-01 00:00:00'");
        }
        else
        {
            $sth = $ods_db->prepare("select count(*) as num from " .$ym_table);
        }
        
        
        $sth = $ods_db->prepare("select count(1) as num From " . $ym_table." where (CaptureType = 0 or CaptureType = 1 or CaptureType = 2 or CaptureType = 3 or CaptureType = 4 or CaptureType = 100 or CaptureType = 101);");
        //$sth->bindParam(':table', $ym_table, PDO::PARAM_STR);
        $sth->execute();
        $door_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

        //$year_month = date("Ym");
        //从 YYYYMM 改成 YYYY-MM
        $year = substr($year_month,0,4);
        $month = substr($year_month,4);
        $year_month = $year.'-'.$month;
        
        $sth = $dw_db->prepare("INSERT INTO  GlobalOpenDoor(`Region`,`DateTime`,`Num`) VALUES (:region, :time, :door_num) ON DUPLICATE KEY UPDATE Num = :door_num");
        $sth->bindParam(':door_num', $door_num, PDO::PARAM_INT);
        $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
        $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
        $sth->execute(); 
    }
}
DoorNum($REGION);

?>
