<?php
date_default_timezone_set('PRC');
require('/home/<USER>/akcs_dw_config.php');
//区域
$REGION = null;
if ($argc == 2) {
    $REGION = $argv[1];
} else {
    echo 'use: akcs_dw_month.php  USA';
    exit;
}

function RegisterDeviceNum($REGION)
{
    //从业务数据库里面查询数据
    $ods_db = getODSDB();
    $dw_db = getDWDB();
    $year_months = array("2019-09-01 00:00:00","2019-10-01 00:00:00","2019-11-01 00:00:00","2019-12-01 00:00:00","2020-01-01 00:00:00","2020-02-01 00:00:00","2020-03-01 00:00:00","2020-04-01 00:00:00","2020-05-01 00:00:00","2020-06-01 00:00:00"); 
    foreach ($year_months as $year_month)
    { 
        $timestart = $year_month;
        $timestart_t = strtotime($timestart);//转成时间戳
        $timeend_t = strtotime("first day of +1 month",$timestart_t);//不能用 + 1 months的形式
        $timeend = date("Y-m-d 00:00:00", $timeend_t);
              
        $sth = $ods_db->prepare("select count(1) as num From Devices where CreateTime between :time_start and :time_end;");
        $sth->bindParam(':time_start', $timestart, PDO::PARAM_INT);
        $sth->bindParam(':time_end', $timeend, PDO::PARAM_STR);
        $sth->execute();
        $register_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        
        $sth = $ods_db->prepare("select count(1) as num From PersonalDevices where CreateTime between :time_start and :time_end;");
        $sth->bindParam(':time_start', $timestart, PDO::PARAM_INT);
        $sth->bindParam(':time_end', $timeend, PDO::PARAM_STR);
        $sth->execute();
        $register_per_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        
        $register_num = $register_devices_num + $register_per_devices_num;
        
        $year_month_t = substr($year_month,0,7);
        $sth = $dw_db->prepare("INSERT INTO  GlobalRegisterDevice(`Region`,`DateTime`,`Num`) VALUES (:region, :time, :register_num) ON DUPLICATE KEY UPDATE Num = :register_num");
        $sth->bindParam(':register_num', $register_num, PDO::PARAM_INT);
        $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
        $sth->bindParam(':time', $year_month_t, PDO::PARAM_INT);
        $sth->execute();        
    }
}

RegisterDeviceNum($REGION);
?>
