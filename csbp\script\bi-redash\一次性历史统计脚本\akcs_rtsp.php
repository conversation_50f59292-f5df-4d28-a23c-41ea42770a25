<?php
date_default_timezone_set('PRC');
require('/home/<USER>/akcs_dw_config.php');
//区域
$REGION = null;
if ($argc == 2) {
    $REGION = $argv[1];
} else {
    echo 'use: akcs_dw_month.php  USA';
    exit;
}

function RtspNum($REGION)
{
    //从业务数据库里面查询数据
    $ods_db = getODSDB();
    $dw_db = getDWDB();
    $table_name = 'AppManualRtsp';    
    $year_months = array("2019-09-01 00:00:00","2019-10-01 00:00:00","2019-11-01 00:00:00","2019-12-01 00:00:00","2020-01-01 00:00:00","2020-02-01 00:00:00","2020-03-01 00:00:00","2020-04-01 00:00:00","2020-05-01 00:00:00","2020-06-01 00:00:00"); 
    foreach ($year_months as $year_month)
    { 
        $timestart = $year_month;
        $timestart_t = strtotime($timestart);//转成时间戳
        $timeend_t = strtotime("first day of +1 month",$timestart_t);//不能用 + 1 months的形式
        $timeend = date("Y-m-d 00:00:00", $timeend_t);
        
        $sth = $ods_db->prepare("select count(1) as num From AppManualRtsp where CreateTime between :time_start and :time_end;");
        $sth->bindParam(':time_start', $timestart, PDO::PARAM_STR);
        $sth->bindParam(':time_end', $timeend, PDO::PARAM_STR);
        $sth->execute();
        $rtsp_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

        //从 YYYYMM 改成 YYYY-MM
        $year_month_t = substr($year_month,0,7);
        
        $sth = $dw_db->prepare("INSERT INTO  GlobalRtsp(`Region`,`DateTime`,`Num`) VALUES (:region, :time, :rtsp_num) ON DUPLICATE KEY UPDATE Num = :rtsp_num");
        $sth->bindParam(':rtsp_num', $rtsp_num, PDO::PARAM_INT);
        $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
        $sth->bindParam(':time', $year_month_t, PDO::PARAM_STR);
        $sth->execute(); 
    }
}

RtspNum($REGION);
?>
