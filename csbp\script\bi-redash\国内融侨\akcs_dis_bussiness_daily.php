<?php
date_default_timezone_set('PRC');
require('/home/<USER>/akcs_dw_config.php');
//区域
$REGION = null;
if ($argc == 2) {
    $REGION = $argv[1];
} else {
    echo 'use: akcs_dw_month.php  usa/eur/asia';
    exit;
}
 $dis_top_list = null;
 $dw_db = null;
 $ods_db = null;
if($REGION == 'USA')
{
    $dw_db = getUSADWDB();
}
else if($REGION == 'EUR')
{
    $dw_db = getEURDWDB();
}
else if($REGION == 'ASIA')
{
    $dw_db = getASIADWDB();
}
else if($REGION == 'CHN')
{
    $dw_db = getCHNDWDB();
}
else if($REGION == 'LOCAL')
{
    $dw_db = getLOCALDWDB();    
}
$ods_db = getODSDB();

$dis_top_list;
$dis_list = array("rongqiao","shenyang<PERSON><PERSON>","XiamenProject");
foreach ($dis_list as $dis)
{
    $sth = $ods_db->prepare("select ID from Account where Account = :dis and Grade = 11");
    $sth->bindParam(':dis', $dis, PDO::PARAM_STR);
    $sth->execute();
    $dis_id = $sth->fetch(PDO::FETCH_ASSOC)['ID'];//fetch 不是 fetchALL
    $dis_top_list[$dis] = $dis_id;
}

function DisOpenDoorNum()
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;
    $table_name = 'PersonalCapture';
    $year_month = date("Y-m");
    foreach ($dis_top_list as $dis_acc => $dis_id)
    {      
        $sth = $ods_db->prepare("select count(*) as num from PersonalCapture C left join Account A on A.ID = C.MngAccountID left join Account B on B.ID = A.ParentID where B.ID = :id and C.CaptureType < 102");
        $sth->bindParam(':id', $dis_id, PDO::PARAM_INT);
        $sth->execute();
        $opendoor_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        
        $sth = $dw_db->prepare("INSERT INTO  DisOpenDoor(`Dis`,`DateTime`,`Num`) VALUES (:dis, :time, :opendoor_num) ON DUPLICATE KEY UPDATE Num = :opendoor_num");
        $sth->bindParam(':opendoor_num', $opendoor_num, PDO::PARAM_INT);
        $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
        $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
        $sth->execute(); 
    }
}

function DisCallNum()
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;
    $year_month = date("Y-m");

    foreach ($dis_top_list as $dis_acc => $dis_id)
    {
        $sth = $ods_db->prepare("select count(*) as num from CallHistory C left join Account A on A.ID = C.MngAccountID left join Account B on B.ID = A.ParentID where B.ID = :id");
        $sth->bindParam(':id', $dis_id, PDO::PARAM_INT);
        $sth->execute();
        $call_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        
        $sth = $dw_db->prepare("INSERT INTO  DisCall(`Dis`,`DateTime`,`Num`) VALUES (:dis, :time, :call_num) ON DUPLICATE KEY UPDATE Num = :call_num");
        $sth->bindParam(':call_num', $call_num, PDO::PARAM_INT);
        $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
        $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
        $sth->execute(); 
    }

}
//每月新增激活家庭数
function DisActiveFamilyNum($REGION)
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;

    $timeend = date("Y-m-d H:i:s");
    $timestart_t= date("Y-m");
    $timestart = $timestart_t .'-01 00:00:00';
    $year_month = date("Y-m");
    
    
    foreach ($dis_top_list as $dis_acc => $dis_id)
    {  
        $sth_act_family = $ods_db->prepare("select count(1) as count from PersonalAccount P left join Account A on A.ID = P.ParentID left join Account B on B.ID = A. ParentID where (B.ID = :id) and (P.ActiveTime between '".$timestart."' and '".$timeend."') and (P.Role = 10 or P.Role = 20) and P.Active = 1;");
        $sth_act_family->bindParam(':id', $dis_id, PDO::PARAM_INT);
        $sth_act_family->execute();
        $resultRole = $sth_act_family->fetch(PDO::FETCH_ASSOC);
        $family_active_num = $resultRole['count'];
        
        //UNIQUE KEY `region_data` (`Region`,`DateTime`)  对应的表格已经通过唯一键来保证只会插入一次，后续的都是更新
        $sth = $dw_db->prepare("INSERT INTO  DisActiveFamily(`Dis`,`DateTime`,`Num`) VALUES (:dis, :time, :family_active_num) ON DUPLICATE KEY UPDATE Num = :family_active_num");
        $sth->bindParam(':family_active_num', $family_active_num, PDO::PARAM_INT);
        $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
        $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
        $sth->execute();         
    }
}

DisActiveFamilyNum($REGION);
DisCallNum();
DisOpenDoorNum();
//ActiveFamilyNumWeek();
?>
