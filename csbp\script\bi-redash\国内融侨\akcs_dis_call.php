<?php
date_default_timezone_set('PRC');
require('/home/<USER>/akcs_dw_config.php');
//区域
$REGION = null;
if ($argc == 2) {
    $REGION = $argv[1];
} else {
    echo 'use: akcs_dw_month.php  usa/eur/asia';
    exit;
}
 $dis_top_list = null;
 $dw_db = null;
 $ods_db = null;
if($REGION == 'USA')
{
    $dw_db = getUSADWDB();
}
else if($REGION == 'EUR')
{
    $dw_db = getEURDWDB();
}
else if($REGION == 'ASIA')
{
    $dw_db = getASIADWDB();
}
else if($REGION == 'CHN')
{
    $dw_db = getCHNDWDB();
}
else if($REGION == 'LOCAL')
{
    $dw_db = getLOCALDWDB();    
}
$ods_db = getODSDB();

$dis_top_list;
$dis_list = array("rong<PERSON>ao","shenyang<PERSON><PERSON>","XiamenProject");
foreach ($dis_list as $dis)
{
    $sth = $ods_db->prepare("select ID from Account where Account = :dis and Grade = 11");
    $sth->bindParam(':dis', $dis, PDO::PARAM_STR);
    $sth->execute();
    $dis_id = $sth->fetch(PDO::FETCH_ASSOC)['ID'];//fetch 不是 fetchALL
    $dis_top_list[$dis] = $dis_id;
}

function CallNum()
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;
    $table_name = 'CallHistory';    
    $year_months = array("202002","202003","202004","202005","202006","202007","202008","202009","202010","202011","202012","202101");
    foreach ($year_months as $year_month)
    {
        $ym_table = $table_name."_".$year_month;
        //从 YYYYMM 改成 YYYY-MM
        $year = substr($year_month,0,4);
        $month = substr($year_month,4);
        $year_month = $year.'-'.$month;
        $year_month_day = $year_month.'-01 00:00:00';
        foreach ($dis_top_list as $dis_acc => $dis_id)
        {
            if($ym_table == 'CallHistory_201909')
            {
                $sth = $ods_db->prepare("select count(*) as num from " .$ym_table. " C left join Account A on A.ID = C.MngAccountID left join Account B on B.ID = A.ParentID where B.ID = :id and C.StartTime > '2019-09-01 00:00:00'");
            }
            else
            {
                $sth = $ods_db->prepare("select count(*) as num from " .$ym_table. " C left join Account A on A.ID = C.MngAccountID left join Account B on B.ID = A.ParentID where B.ID = :id");
            }
            $sth->bindParam(':id', $dis_id, PDO::PARAM_INT);
            $sth->execute();
            $call_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
            
            $sth = $dw_db->prepare("INSERT INTO  DisCall(`Dis`,`DateTime`,`Num`) VALUES (:dis, :time, :call_num) ON DUPLICATE KEY UPDATE Num = :call_num");
            $sth->bindParam(':call_num', $call_num, PDO::PARAM_INT);
            $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
            $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
            $sth->execute(); 
        }
    }
}

CallNum();
?>
