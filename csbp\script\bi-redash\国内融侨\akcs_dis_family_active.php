<?php
date_default_timezone_set('PRC');
require('/home/<USER>/akcs_dw_config.php');
//区域
$REGION = null;
if ($argc == 2) {
    $REGION = $argv[1];
} else {
    echo 'use: akcs_dw_month.php  usa/eur/asia';
    exit;
}
 $dis_top_list = null;
 $dw_db = null;
 $ods_db = null;
if($REGION == 'USA')
{
    $dw_db = getUSADWDB();
}
else if($REGION == 'EUR')
{
    $dw_db = getEURDWDB();
}
else if($REGION == 'ASIA')
{
    $dw_db = getASIADWDB();
}
else if($REGION == 'CHN')
{
    $dw_db = getCHNDWDB();
}
else if($REGION == 'LOCAL')
{
    $dw_db = getLOCALDWDB();    
}
$ods_db = getODSDB();

/*
//查询最大项目数量的dis
$sth_dis = $dw_db->prepare("select Dis,sum(Num) as pro_count from DisProjectSize group by Dis order by pro_count desc limit 20;");
$sth_dis->execute();
$dis_list = $sth_dis->fetchALL(PDO::FETCH_ASSOC);
foreach ($dis_list as $row => $dis)
{
    $dis_acc = $dis['Dis'];
    $sth = $ods_db->prepare("select ID from Account where Account = :dis and Grade = 11");
    $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
    $sth->execute();
    $dis_id = $sth->fetch(PDO::FETCH_ASSOC)['ID'];//fetch 不是 fetchALL
    $dis_top_list[$dis_acc] = $dis_id;
}
*/
$dis_top_list;
$dis_list = array("rongqiao","shenyangjiali","XiamenProject");
foreach ($dis_list as $dis)
{
    $sth = $ods_db->prepare("select ID from Account where Account = :dis and Grade = 11");
    $sth->bindParam(':dis', $dis, PDO::PARAM_STR);
    $sth->execute();
    $dis_id = $sth->fetch(PDO::FETCH_ASSOC)['ID'];//fetch 不是 fetchALL
    $dis_top_list[$dis] = $dis_id;
}

//每月新增激活家庭数
function DisActiveFamilyNum($REGION)
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;

    $year_months = array("2020-02-01 00:00:00","2020-03-01 00:00:00","2020-04-01 00:00:00","2020-05-01 00:00:00","2020-06-01 00:00:00","2020-07-01 00:00:00","2020-08-01 00:00:00","2020-09-01 00:00:00","2020-10-01 00:00:00","2020-11-01 00:00:00","2020-12-01 00:00:00","2021-01-01 00:00:00"); 
    foreach ($year_months as $year_month)
    {        
        $timestart = $year_month;
        $timestart_t = strtotime($timestart);//转成时间戳
        $timeend_t = strtotime("first day of +1 month",$timestart_t);//不能用 + 1 months的形式
        $timeend = date("Y-m-d 00:00:00", $timeend_t);
        
        foreach ($dis_top_list as $dis_acc => $dis_id)
        {  
            $sth_act_family = $ods_db->prepare("select count(1) as count from PersonalAccount P left join Account A on A.ID = P.ParentID left join Account B on B.ID = A. ParentID where (B.ID = :id) and (P.ActiveTime between '".$timestart."' and '".$timeend."') and (P.Role = 10 or P.Role = 20) and P.Active = 1 and P.special = 0;");
            $sth_act_family->bindParam(':id', $dis_id, PDO::PARAM_INT);
            $sth_act_family->execute();
            $resultRole = $sth_act_family->fetch(PDO::FETCH_ASSOC);
            $family_active_num = $resultRole['count'];
            
            //从 2019-09-01 00:00:00 改成 2019-09
            $year_month = substr($year_month,0,7);
            //UNIQUE KEY `region_data` (`Region`,`DateTime`)  对应的表格已经通过唯一键来保证只会插入一次，后续的都是更新
            $sth = $dw_db->prepare("INSERT INTO  DisActiveFamily(`Dis`,`DateTime`,`Num`) VALUES (:dis, :time, :family_active_num) ON DUPLICATE KEY UPDATE Num = :family_active_num");
            $sth->bindParam(':family_active_num', $family_active_num, PDO::PARAM_INT);
            $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
            $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
            $sth->execute();         
        }
    }
}
DisActiveFamilyNum($REGION);

?>
