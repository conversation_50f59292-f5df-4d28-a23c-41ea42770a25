module.exports = {
    root: true,
    env: {
        node: true
    },
    extends: [
        'plugin:vue/essential',
        '@vue/airbnb',
        '@vue/typescript/recommended'
    ],

    plugins: [
        'vue',
        'typescript'
    ],

    parserOptions: {
        ecmaVersion: 2020
    },
    /*
        全局变量
        key:true
    */
    globals: {},
    /*
        0 或’off’：  关闭规则。
        1 或’warn’： 打开规则，并且作为一个警告，字体颜色为黄色（并不会导致检查不通过）。
        2 或’error’：打开规则，并且作为一个错误 ，色体颜色为红色(退出码为1，检查不通过)。
    */
    rules: {
        'global-require': 0,
        'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
        'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
        'linebreak-style': [0, 'error', 'windows'],
        indent: ['error', 4], // 4个空格缩进
        'no-cond-assign': 2, // 条件语句的条件中不允许出现赋值运算符
        'no-extra-semi': 2, // 不允许出现不必要的分号
        'no-eval': 2, // 不允许使用eval()
        semi: [2, 'always'], // 强制语句分号结尾
        'comma-dangle': [2, 'never'], // 数组和对象键值对最后一个逗号
        'no-dupe-keys': 2, // 在创建对象字面量时不允许键重复 {a:1,a:1}
        'space-before-blocks': [2, 'always'], // 块前的空格
        'quote-props': [2, 'as-needed'], // 对象字面量中的属性名不使用双引号，除非必须
        'no-var': 2, // 使用let和const代替var
        'no-func-assign': 2, // 禁止重复的函数声明
        'no-inline-comments': 2, // 禁止行内备注
        'no-lonely-if': 2, // 禁止else语句内只有if语句
        'no-lone-blocks': 2, // 禁止不必要的嵌套块
        'no-floating-decimal': 2, // 禁止省略浮点数中的0 .5 3.
        'max-params': 0,
        'max-len': [2, { 'code': 150 }],
        '@typescript-eslint/no-explicit-any': 0,
        eqeqeq: 2, // 必须使用全等
        'no-empty': 2, // 块语句中的内容不能为空
        'eol-last': 0,
        'class-methods-use-this': 0,
        'vue/no-v-for-template-key': 0,
        'import/no-cycle': 0,
        '@typescript-eslint/consistent-type-definitions': [
                'error',
                'interface'
            ] // 优先使用interface而不是type
    },
    overrides: [{
        files: [
            '**/__tests__/*.{j,t}s?(x)',
            '**/tests/unit/**/*.spec.{j,t}s?(x)'
        ],
        env: {
            mocha: true
        }
    }]
};