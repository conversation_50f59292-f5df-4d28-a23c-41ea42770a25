{"name": "analysis", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build  --max_old_space_size=10000", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e", "lint": "vue-cli-service lint"}, "dependencies": {"@element-plus/icons-vue": "^1.1.4", "@soerenmartius/vue3-clipboard": "^0.1.2", "@types/echarts": "^4.9.13", "@types/jquery": "^3.5.13", "axios": "^0.26.0", "core-js": "^3.6.5", "echarts": "^5.3.0", "element-plus": "^2.1.11", "highcharts": "^9.3.3", "register-service-worker": "^1.7.1", "vue": "^3.0.0", "vue-class-component": "^8.0.0-0", "vue-router": "^4.0.0-0", "vuex": "^4.0.0-0"}, "devDependencies": {"@types/chai": "^4.2.11", "@types/mocha": "^5.2.4", "@typescript-eslint/eslint-plugin": "^4.18.0", "@typescript-eslint/parser": "^4.18.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-e2e-nightwatch": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-pwa": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-plugin-unit-mocha": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.0", "@vue/eslint-config-typescript": "^7.0.0", "@vue/test-utils": "^2.0.0-0", "@vue/eslint-config-airbnb": "^5.0.2", "chai": "^4.1.2", "chromedriver": "98", "eslint": "^6.7.2", "eslint-config-airbnb-typescript": "^12.3.1", "eslint-config-airbnb": "^19.0.4", "eslint-plugin-import": "^2.25.4", "eslint-plugin-vue": "^7.0.0", "eslint-plugin-typescript": "^0.14.0", "less": "^3.0.4", "less-loader": "^5.0.0", "typescript": "~4.1.5"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "@vue/airbnb", "@vue/typescript/recommended"], "parserOptions": {"ecmaVersion": 2020}, "rules": {}, "overrides": [{"files": ["**/__tests__/*.{j,t}s?(x)", "**/tests/unit/**/*.spec.{j,t}s?(x)"], "env": {"mocha": true}}]}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}