// background
@base: 100/1080;
@BackgroundColor: linear-gradient(180deg, #090A20 0%, #0C0B29 51%, #040710 100%);
@OuterPadding: @base * 33vh 45px;

each(range(0, 1000, 1), {
    .height@{value}vh {
        height: (@base * @value * 1vh);
    }
});

each(range(0, 1000, 1), {
    .width@{value}px {
        width: (@value * 1px);
    }
});

each(range(0, 500, 1), {
    .right@{value}px {
        right: (@value * 1px);
    }
});

each(range(0, 500, 1), {
    .margin-left@{value}px {
        margin-left: (@value * 1px);
    }
});

each(range(0, 500, 1), {
    .margin-top@{value}vh {
        margin-top: (@base * @value * 1vh);
    }
});

each(range(0, 500, 1), {
    .margin-bottom@{value}vh {
        margin-bottom: (@base * @value * 1vh);
    }
});

each(range(0, 500, 1), {
    .padding-bottom@{value}vh {
        padding-bottom: (@base * @value * 1vh);
    }
});

@displayList: inline, inline-block, block, none, flex;
each(@displayList, {
    .display-@{value} {
        display: @value;
    }
});

each(range(0, 500, 1), {
    .flex@{value} {
        flex: @value;
    }
});

.position-rel {
    position: relative;
}
.position-abs {
    position: absolute;
}

.full-container {
    width: 100%;
    min-height: @base * 100vh;
}
.body-container {
    padding: @OuterPadding;
    box-sizing: border-box;
    .light-background {
        box-shadow: inset 0px 1px 60px 0px rgba(13,45,67,0.42);
        border: 1px solid #093350;
        min-height: @base * 100vh;
        padding: @base * 22vh 45px @base * 33vh;
        box-sizing: border-box;
        // opacity: 0.42;
        z-index: -1;
    }
}

.align-items-center {
    align-items: center;
}
.justify-content-center {
    justify-content: center;
}
.flex-direction-column {
    flex-direction: column;
}
.blue-border {
    box-shadow: inset 0px 1px 60px 0px #1F5E89;
    border: 1px solid #1B5378;
}

.base-background {
    background-image: url('../../assets/image/background.png');
    background-size: 100% 100%;
}

.cursor-pointer {
    cursor: pointer;
}