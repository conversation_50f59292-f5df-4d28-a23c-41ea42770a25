<template>
    <div
        class="card-container display-flex flex-direction-column blue-border"
        :style="{height: height ? height :'', 'min-width': width ? width : ''}"
    >
        <div :class="['border', showBorder ? 'left-top' : '']"></div>
        <div :class="['border', showBorder ? 'right-top': '']"></div>
        <div :class="['border', showBorder ? 'left-bottom' : '']"></div>
        <div :class="['border', showBorder ? 'right-bottom' : '']"></div>
        <div class="card-title" v-if="showTitle">
            <div class="title-container position-rel display-flex align-items-center">
                <div>
                    <img src="@/assets/image/mark.png">
                    <label class="margin-left10px">{{ data.title || title }}</label>
                </div>
                <label class="position-abs right0px" v-if="data.rightTitle!=''">{{ data.rightTitle }}</label>
                <slot name="header"></slot>
            </div>
        </div>
        <div class="card-content flex1 display-flex" :style="{paddingLeft: type=='customize'?'0':''}">
            <template v-if="type==='customize'">
                <slot></slot>
            </template>
            <template v-else>
                <div class="text-content display-flex align-items-center">
                    <img :src="data.icon">
                    <label
                        class="margin-left48px"
                        :style="{color: data.textColor ? data.textColor : '', fontWeight: data.textColor ? 'bold' : ''}"
                    >{{ data.text }}</label>
                </div>
            </template>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';

interface CardData {
    title: string;
    icon: string;
    text: string;
    textColor?: string;
    rightTitle?: string;
}
export default defineComponent({
    props: {
        data: {
            type: Object as PropType<CardData>,
            default() {
                return {
                    title: '',
                    icon: '',
                    text: ''
                };
            }
        },
        height: {
            type: String
        },
        width: {
            type: String
        },
        showTitle: {
            type: Boolean,
            default: true
        },
        type: {
            type: String as PropType<'' | 'customize'>,
            default: ''
        },
        title: {
            type: String,
            default: ''
        },
        showBorder: {
            type: Boolean,
            default: true
        }
    }
});
</script>

<style lang="less" scoped>
@import url('../../../assets/less/common.less');
@Background: rgba(12, 15, 44, 0.66);
@Border: 1px solid #1B5378;
@Shadow: inset 0px 1px 60px 0px #1F5E89;
@Width: 288px;

@TitleColor: #02A3FF;
@TitleBackground: rgba(22, 55, 115, 0.4);
.card-container {
    background: @Background;
    height: @base * 140vh;
    min-width: @Width;
    position: relative;
    box-shadow: @Shadow;
    opacity: 0.86;
    .card-title {
        background-image: url('../../../assets/image/bar-bg.png');
        background-repeat: repeat;
        padding: @base * 10vh 13px;
        font-size: 16px;
        color: white;
    }
    .title-container {
        justify-content: space-between;
    }
    .card-content {
        font-size: 26px;
        color: white;
        padding-left: 50px;
        .text-content img {
            height: @base * 52vh;
            width: auto;
        }
    }
    .border {
        width: 24px;
        height: @base * 24vh;
        position: absolute;
    }
    .left-top {
        border-left: 2px solid #02A3FF;
        border-top: @base * 2vh solid #02A3FF;
        left: -1px;
        top: -1px;
    }
    .right-top {
        border-top: @base * 2vh solid #02A3FF;
        border-right: 2px solid #02A3FF;
        right: -1px;
        top: -1px;
    }
    .left-bottom {
        border-left: 2px solid #02A3FF;
        border-bottom:  @base * 2vh solid #02A3FF;
        left: -1px;
        bottom: -1px;
    }
    .right-bottom {
        border-bottom:  @base * 2vh solid #02A3FF;
        border-right: 2px solid #02A3FF;
        right: -1px;
        bottom: -1px;
    }
}
</style>