<template>
    <div
        :class="['my-button',
            disabled ? 'my-button-disabled' : 'my-button-abled',
            classType[type]]"
        :style="{
            width: width ? width : '',
            height: height ? height : '',
            lineHeight: height ? height : ''
        }"
        @click="clickHandler"
    >
        <slot></slot>
    </div>
</template>
<script lang="ts">
import { defineComponent, ref, PropType } from 'vue';

export default defineComponent({
    name: 'cus-button',
    emits: ['click'],
    props: {
        type: {
            type: String as PropType<'default' | 'blue' | 'iconread' | 'iconblue'>,
            default: 'default'
        },
        disabled: {
            type: Boolean,
            default: false
        },
        width: {
            type: String,
            default: null
        },
        height: {
            type: String,
            default: '28px'
        }
    },
    setup(props, { emit }) {
        const classType = ref({
            default: 'default',
            blue: 'blue',
            iconread: 'icon red',
            iconblue: 'icon blue-empty'
        });

        const clickHandler = () => {
            if (!props.disabled) emit('click');
        };

        return {
            classType,
            clickHandler
        };
    }
});
</script>

<style lang='less' scoped>
.my-button{
    display: inline-block;
    height: 28px;
    line-height: 28px;
    min-width: 60px;
    text-align: center;
    padding: 0 10px;
    box-sizing: border-box;
    border-radius: 5px;
    text-align: center;
    font-size: 13px;
    cursor: pointer;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    vertical-align:middle;
}

.my-button-disabled {
    cursor: not-allowed;
}
.default{
    border: 1px solid rgb(204,204,204);
    background-color: rgb(255, 255, 255);
}
.my-button-disabled.default{
    background-color: #c7c7c7;
    color: #fdfdfd
}
.my-button-disabled.blue{
    background-color: #c7c7c7;
    color: #fdfdfd;
    border: 1px solid #c7c7c7;
}
.blue{
    border: 1px solid rgb(24,163,253);
    background-color: rgb(24,163,253);
    color:#ffffff;
}

.blue-empty {
    border: 1px solid rgb(24,163,253);
    color: rgb(24,163,253);
    &:hover {
        background-color: rgb(24,163,253);
        color:#ffffff;
    }
}

.my-button:hover{
    opacity: 0.8;
}
.icon{
    min-width: 10px;
    height: 26px;
    line-height: 26px;
}
.red{
    border: 1px solid #ff210a;
    color:#fb602a;
    i{
        font-size: 14px;
    }
}
.my-button-abled.red:hover{
    background-color: #ff210a;
    border: 1px solid #ffffff;
    color:#ffffff;
}
.my-button-abled.default:hover{
    border: 1px solid rgb(24,163,253);
    color:rgb(24,163,253);
}
</style>