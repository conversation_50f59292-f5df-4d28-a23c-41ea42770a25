<template>
    <div class="base">
        <div class="toast-box">
            <div class="header">Requesting Failed</div>
            <div class="content display-flex flex-direction-column align-items-center">
                <div class="display-flex align-items-center">
                    <label>{{ text }}</label>
                </div>
                <div
                    :class="['btn', 'cursor-pointer', isClick ? 'click' : '']"
                    @mousedown="isClick=true"
                    @mouseup="isClick=false"
                    @mouseleave="isClick=false"
                    @click="clickFun"
                >OK</div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, PropType, ref } from 'vue';

export default defineComponent({
    emits: ['confirm'],
    props: {
        text: {
            type: String,
            required: true
        },
        clickFun: {
            type: Function as PropType<() => void>,
            required: true
        }
    },
    setup() {
        const isShowToast = ref(false);
        const isClick = ref(false);
        const test = () => {
            alert(1);
        };
        return {
            isClick,
            isShowToast,
            test
        };
    }
});
</script>

<style lang="less" scoped>
@import url("../../../assets/less/common.less");
.base {
    background: rgba(2, 5, 19, 0.54);
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    justify-content: center;
    z-index: 9999;
}
.toast-box {
    width: 550px;
    height: 358vh * @base;
    margin-top: 330vh * @base;
    background-image: url('../../../assets/image/reset-background.png');
    background-size: 100% 100%;
    .header {
        height: 67vh * @base;
        line-height: 67vh * @base;
        color: #FFFFFF;
        font-size: 24vh * @base;
        text-align: center;
    }
    .content {
        box-sizing: border-box;
        height: calc(100% - 67vh * @base);
        padding: 45vh * @base 20px;
        color: white;
        justify-content: space-between;
        position: relative;
        .btn {
            width: 330px;
            height: 60vh * @base;
            line-height: 60vh * @base;
            background: #02A3FF;
            border-radius: 8px;
            font-size: 24vh * @base;
            text-align: center;
        }
        .click {
            background: #0172B3;
        }
    }
}
</style>