<template>
    <el-dialog
        :title="title"
        :close-on-click-modal="false"
        :width="width"
        :top="top"
        v-model="dialogVisible"
        @close="$emit('close')"
    >
        <slot></slot>
        <template #footer>
            <span class="dialog-footer" v-if="footerType === 'default'">
                <el-button @click="$emit('close')">
                    Cancel
                </el-button>
                <el-button type="primary" @click="$emit('submit')">
                    Submit
                </el-button>
            </span>
            <template v-else>
                <slot name="footer"></slot>
            </template>
        </template>
    </el-dialog>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue';

export default defineComponent({
    emits: ['submit', 'success', 'close'],
    props: {
        title: {
            type: String,
            required: true
        },
        width: {
            type: String,
            default: '600px'
        },
        footerType: {
            type: String as PropType<'default' | 'customize'>,
            default: 'default'
        },
        top: {
            type: String,
            default: '15vh'
        }
    },
    setup() {
        return {
            dialogVisible: true
        };
    }
});
</script>