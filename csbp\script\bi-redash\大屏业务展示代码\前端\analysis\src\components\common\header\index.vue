<template>
    <div class="page-header">
        <slot></slot>
        <div class="display-flex align-items-center position-rel" v-if="showShare || showManage">
            <div class="icon justify-content-center icon-hover" @click="$emit('share')" v-if="!showManage">
                <img src="@/assets/image/share.png" class="normal">
            </div>
            <div class="icon margin-left37px cursor-pointer" @click="select">
                <img
                    :src="type==='blue'?require('@/assets/image/user-manage-blue.png'):require('@/assets/image/user-manage.png')"
                    class="normal cursor-pointer">
                <div class="width8px cursor-pointer"></div>
                <img
                    :class="['select-img', expand ? 'expand' : '', 'cursor-pointer']"
                    :src="type==='blue'?require('@/assets/image/select-btn-blue.png'):require('@/assets/image/select-btn.png')"
                >
                <div class="select-list" v-if="expand">
                    <div v-for="(item, index) in editList" :key="item.value" @click="clickItem(item)">
                        <span class="select-item">{{ item.label }}</span>
                        <div class="line" v-if="index !== editList.length - 1"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import { useRouter } from 'vue-router';

interface OptionsType {
    label: string;
    value: string;
}
export default defineComponent({
    emits: ['share'],
    props: {
        showSelect: {
            type: Boolean,
            default: true
        },
        showShare: {
            type: Boolean,
            default: true
        },
        showManage: {
            type: Boolean,
            default: false
        },
        type: {
            type: String,
            default: ''
        }
    },
    setup() {
        const expand = ref(false);

        const editList = [{
            label: 'Edit Profile',
            value: 'Profiles'
        }, {
            label: 'Users',
            value: 'Users'
        }, {
            label: 'Log Out',
            value: 'LogOut'
        }];
        const router = useRouter();

        const select = () => {
            expand.value = !expand.value;
        };
        const clickItem = (item: OptionsType) => {
            if (item.value === 'LogOut') {
                localStorage.removeItem('token');
                router.replace('/');
            } else {
                router.push(`/user?type=${item.value}`);
            }
        };

        return {
            expand,
            editList,
            select,
            clickItem
        };
    }
});
</script>

<style lang="less" scoped>
@import url("../../../assets/less/common.less");
.page-header {
    display: flex;
    justify-content: space-between;
    height: 100%;
    .icon {
        width: 40vh * @base;
        height: 30vh * @base;
        display: flex;
        align-items: center;
        .normal {
            height: 18vh * @base;
            width: auto;
        }
        .select {
            width: auto;
            height: 8vh * @base;
            transition: transform .5s;
            .expand {
                transform: rotate(180deg);
            }
        }
        .select-list {
            justify-content: end;
            width: 120px;
            right: 0;
        }
    }
    .icon-hover:hover {
        background: #02A3FF;
        border-radius: 5px;
        cursor: pointer;
    }
}
.select-img {
    width: auto;
    height: 8vh * @base;
    transition: transform .5s;
}
.expand {
    transform: rotate(180deg);
}
.select-list {
    position: absolute;
    top: calc(30vh * @base + 5px);
    z-index: 80;
    display: flex;
    flex-direction: column;
    background: #0C0F2C;
    box-shadow: 0px 4px 18px 0px rgba(6, 108, 248, 0.5), inset 0px 1px 17px 0px #1F5E89;
    opacity: 0.96;
    order: 1px solid #1B5378;
    width: 120px;
    .select-item {
        display: inline-block;
        height: 40vh * @base;
        width: 100%;
        line-height: 40vh * @base;
        color: #FFFFFF;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        &:hover {
            color: #02A1FC;
            cursor: pointer;
        }
    }
    .line {
        height: 1px;
        background:linear-gradient(244deg,rgba(255,255,255,0) 0%,#03B8FF 50%,rgba(255,255,255,0) 100%);
        opacity: 0.41;
        filter: blur(0px);
    }
}
</style>
