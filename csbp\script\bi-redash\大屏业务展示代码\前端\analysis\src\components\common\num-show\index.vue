<template>
    <div class="display-flex">
        <div v-for="item in numForArray" :key="item.id" class="display-flex">
            <span v-if="item.separate" class="separate">,</span>
            <span class="num-back">
                {{item.text}}
            </span>
        </div>
    </div>
</template>

<script lang="ts">
import { computed, defineComponent } from 'vue';

export default defineComponent({
    props: {
        number: {
            type: Number,
            required: true
        }
    },
    setup(props) {
        const numForArray = computed(() => {
            const numString = props.number.toString();
            const tempArray: {
                id: number;
                separate: boolean;
                text: string;
            }[] = [];
            let separateCount = 3;
            for (let i = numString.length - 1; i >= 0; i -= 1) {
                separateCount -= 1;
                tempArray.push({
                    id: i,
                    separate: separateCount === 0 && i !== 0,
                    text: numString[i]
                });
            }
            return tempArray.reverse();
        });

        return {
            numForArray
        };
    }
});
</script>

<style lang="less" scoped>
@import url("../../../assets/less/common.less");

.separate {
    margin: 5px 10px 0 4px;
    font-size: 36px;
}
.num-back {
    display: inline-block;
    background: linear-gradient(to bottom, #052FFC 0%, #052FFC 50%, #006BFF, 51%, #006BFF 100%);
    width: 38vh * @base;
    height: 66vh * @base;
    line-height: 66vh * @base;
    text-align: center;
    margin-right: 6px;
}
</style>
