<template>
    <dialog-shell title="Change Password" footerType="customize">
        <el-form
            ref="formRef"
            label-position="top"
            :rules="rules"
            :model="formData">
            <el-form-item label="Current Password" prop="CurrentPassword">
                <el-input type="password" v-model="formData.CurrentPassword" @blur="checkCurrentPassword"></el-input>
            </el-form-item>
            <el-form-item label="New Password" prop="NewPassword">
                <password-input v-model="formData.NewPassword">
                </password-input>
            </el-form-item>
            <el-form-item label="Confirm Password" prop="Confirm">
                <el-input type="password" v-model="formData.Confirm"></el-input>
            </el-form-item>
        </el-form>

        <template #footer>
            <span class="dialog-footer">
                <el-button @click="$emit('close')">Cancel</el-button>
                <el-button type="primary" @click="confirmChange">Ok</el-button>
            </span>
        </template>
    </dialog-shell>
</template>

<script lang="ts">
import {
    defineComponent, computed,
    reactive, toRef, ref
} from 'vue';
import PasswordInput from '@/components/common/password-input';
import DialogShell from '@/components/common/dialog-shell/index.vue';
import { user } from '@/methods/rule';
import HttpRequest from '@/util/axios.config';
import { ElMessageBox } from 'element-plus';

export default defineComponent({
    emits: ['close', 'success'],
    components: {
        PasswordInput,
        DialogShell
    },
    setup(props, { emit }) {
        const formRef = ref();
        const formData = reactive({
            CurrentPassword: '',
            NewPassword: '',
            Confirm: ''
        });
        const isCurrentPasswordDifferent = ref(false);
        const rules = {
            CurrentPassword: [{
                required: true,
                message: 'Please enter the password.',
                trigger: 'blur'
            }, {
                validator: user.checkCurrentPassword(isCurrentPasswordDifferent),
                trigger: 'blur'
            }],
            NewPassword: [{
                required: true,
                message: 'Please enter the password.',
                trigger: 'blur'
            }, {
                validator: user.checkPassword,
                trigger: 'blur'
            }],
            Confirm: [{
                required: true,
                message: 'Please re-enter the password.',
                trigger: 'blur'
            }, {
                validator: user.checkConfirmPassword(toRef(formData, 'NewPassword')),
                trigger: 'blur'
            }]
        };

        const checkCurrentPassword = () => {
            isCurrentPasswordDifferent.value = false;
            if (formData.CurrentPassword !== '') {
                HttpRequest.post('updatePwdCheck', [{
                    Password: formData.CurrentPassword
                }, false], () => false, [() => {
                    isCurrentPasswordDifferent.value = true;
                    formRef.value.validateField('CurrentPassword');
                }, false]);
            }
        };

        const confirmChange = () => {
            formRef.value.validate((valid: boolean) => {
                if (valid) {
                    HttpRequest.post('updatePwd', {
                        Password: formData.CurrentPassword,
                        NewPassword: formData.NewPassword
                    }, () => {
                        ElMessageBox.confirm('Password has been changed successfully.\nPlease login again.', 'Change Password', {
                            confirmButtonText: 'OK',
                            center: true,
                            showCancelButton: false
                        }).then(() => {
                            emit('close');
                            emit('success');
                        });
                    });
                }
            });
        };

        return {
            formRef,
            rules,
            formData,
            checkCurrentPassword,
            confirmChange
        };
    }
});
</script>

<style lang="less">
.el-button {
    background-color: var(--el-button-bg-color);
}
</style>