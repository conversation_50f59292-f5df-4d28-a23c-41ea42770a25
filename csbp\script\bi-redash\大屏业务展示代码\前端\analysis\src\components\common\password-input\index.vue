<template>
    <div class="password-input-container">
        <el-input v-model="value" type="password" @focus="isShowRule = true" @blur="closeRule"></el-input>
        <ul class="limit" v-if="isShowRule">
            <li style="left: -10px;">Your password must meet three of the four terms below:</li>
            <li
                v-for='item in rules'
                :class='item.valid ? "success":  "default"'
                :key="item.name"
            >
                <span></span> {{ item.content }}
            </li>
        </ul>
    </div>
</template>

<script lang="ts">
import {
    computed, defineComponent, reactive, ref,
    watch
} from 'vue';
import { user } from '@/methods/rule';
import './password.less';

export default defineComponent({
    name: 'password-input',
    props: {
        modelValue: {
            type: String,
            required: true
        }
    },
    setup(props, { emit }) {
        const rules = reactive([{
            valid: false,
            content: 'at least one lowercase letter',
            name: 'lLetter'
        }, {
            valid: false,
            content: 'at least one uppercase letter',
            name: 'uLetter'
        }, {
            valid: false,
            content: 'at least one number',
            name: 'dLetter'
        }, {
            valid: false,
            content: 'at least one special character',
            name: 'tLetter'
        }]);

        const value = ref('');
        watch(computed(() => props.modelValue), () => {
            value.value = props.modelValue;
        }, {
            immediate: true
        });
        watch(value, () => {
            emit('update:modelValue', value.value);
            const {
                ruleLow, ruleUp, ruleNumber, ruleSpecial
            } = user.checkPasswordComplexity(value.value);
            rules[0].valid = ruleLow;
            rules[1].valid = ruleUp;
            rules[2].valid = ruleNumber;
            rules[3].valid = ruleSpecial;
        });

        const isShowRule = ref(false);
        const closeRule = () => {
            isShowRule.value = false;
        };
        return {
            value,
            rules,
            isShowRule,
            closeRule
        };
    }
});
</script>