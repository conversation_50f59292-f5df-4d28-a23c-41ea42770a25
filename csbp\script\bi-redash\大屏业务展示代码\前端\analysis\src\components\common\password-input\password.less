.password-input-container {
    position: relative;
    width: 100%;
    ul.limit{
        position: absolute;
        z-index: 10000;
        width: 100%;
        height: auto;
        padding: 5px 15px;
        box-sizing: border-box;
        margin-top: 3px;
        border: 1px solid gainsboro;
        background-color: white;
        li{
            line-height: 30px;
            list-style: none;
            position: relative;
            text-indent: 5px;
            padding-left: 15px;
        }
        li.success{
            color: green;
            span{
                width: 5px;
                height: 5px;
            }
            span:before{
                display: inline-block;
                position: absolute;
                width: 3px;
                height: 8px;
                border-bottom:solid 2px green;
                border-right:solid 2px green;
                content: '';
                left: 10px;
                top:10px;
                transform: rotate(30deg);
            }
        }
        li.default{
            color: red;
            span{
                width: 5px;
                height: 5px;
            }
            span:before{
                display: inline-block;
                position: absolute;
                width: 4px;
                height: 4px;
                border-bottom:solid 2px red;
                border-right:solid 2px red;
                content: '';
                left: 10px;
                top:10px;
                transform: rotate(45deg);
            }
            span:after{
                content: '';
                display: inline-block;
                position: absolute;
                width: 4px;
                height: 4px;
                border-bottom:solid 2px red;
                border-right:solid 2px red;
                left: 10px;
                top:16px;
                transform: rotate(-135deg);
            }
        }
    }
}
