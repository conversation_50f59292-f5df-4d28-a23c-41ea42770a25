<template>
    <div :style="{width: width === '' ? '165px': width}" class="position-rel">
        <div class="display-flex align-items-center cursor-pointer" ref="labelRef" style="height: 100%;" @click="select">
            <label class="page-name flex1 cursor-pointer" ref="pageNameRef" :style="{color: type==='blue'?'#006CFF':''}">{{ title }}</label>
            <div
                :class="['select', 'width20px', 'display-flex', 'justify-content-center', 'align-items-center',
                title==='' ? '' : 'margin-left10px', 'cursor-pointer']"
                ref="selectRef"
            >
                <img
                    v-if="list.length !== 0"
                    :class="['select-img', expand ? 'expand' : '', 'cursor-pointer']"
                    :src="type==='blue'?require('@/assets/image/select-btn-blue.png'):require('@/assets/image/select-btn.png')"
                    ref="selectImgRef"
                >
            </div>
        </div>
        <div v-show="expand" class="select-list" ref="listRef">
            <el-scrollbar class="scroll">
                <div v-for="(item, index) in list" :key="item.value" @click="clickItem(item)">
                    <span :class="['select-item', active === item.value ? 'active' : '']">{{ item.label }}</span>
                    <div class="line" v-if="index !== list.length - 1"></div>
                </div>
            </el-scrollbar>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, PropType, ref } from 'vue';
import { ElScrollbar } from 'element-plus';

interface OptionsType {
    label: string;
    value: string;
}
export default defineComponent({
    emits: ['click'],
    props: {
        title: {
            type: String,
            default: ''
        },
        list: {
            type: Array as PropType<OptionsType[]>,
            default: () => []
        },
        active: {
            type: String,
            default: ''
        },
        width: {
            type: String,
            default: ''
        },
        type: {
            type: String,
            default: ''
        }
    },
    components: {
        ElScrollbar
    },
    setup(props, { emit }) {
        const expand = ref(false);
        const labelRef = ref<HTMLElement>();
        const listRef = ref<HTMLElement>();
        const pageNameRef = ref<HTMLElement>();
        const selectRef = ref<HTMLElement>();
        const selectImgRef = ref<HTMLElement>();

        const select = () => {
            if (!expand.value && labelRef.value && listRef.value) {
                listRef.value.style.top = `${labelRef.value.clientHeight + 5}px`;
            }
            expand.value = !expand.value;
        };

        const clickItem = (item: OptionsType) => {
            emit('click', item.value);
        };

        document.body.addEventListener('click', (event: Event) => {
            if (event.target !== selectRef.value
                && event.target !== selectImgRef.value && event.target !== pageNameRef.value) {
                expand.value = false;
            }
        });

        return {
            expand,
            labelRef,
            listRef,
            pageNameRef,
            selectRef,
            selectImgRef,
            select,
            clickItem
        };
    }
});
</script>

<style lang="less" scoped>
@import url("../../../assets/less/common.less");
.page-name {
    color: #FFFFFF;
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.select {
    height: 100%;
    img {
        width: auto;
        height: 8vh * @base;
        transition: transform .5s;
    }
    .expand {
        transform: rotate(180deg);
    }
}
.select-list {
    position: absolute;
    top: 0;
    z-index: 80;
    display: flex;
    flex-direction: column;
    background: #0C0F2C;
    box-shadow: 0px 4px 18px 0px rgba(6, 108, 248, 0.5), inset 0px 1px 17px 0px #1F5E89;
    opacity: 0.96;
    order: 1px solid #1B5378;
    width: 100%;
    .select-item {
        display: inline-block;
        height: 40vh * @base;
        width: 100%;
        line-height: 40vh * @base;
        color: #FFFFFF;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        &:hover {
            color: #02A1FC;
            cursor: pointer;
        }
    }
    .active {
        color: #02A1FC;
    }
    .line {
        height: 1px;
        background:linear-gradient(244deg,rgba(255,255,255,0) 0%,#03B8FF 50%,rgba(255,255,255,0) 100%);
        opacity: 0.41;
        filter: blur(0px);
    }
    .scroll {
        max-height: 400vh;
    }
}
</style>
