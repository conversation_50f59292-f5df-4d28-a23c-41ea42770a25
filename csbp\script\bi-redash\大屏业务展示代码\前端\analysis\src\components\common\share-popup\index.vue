<template>
    <div class="pop-background">
        <div class="share-board">
            <div class="header display-flex align-items-center">
                <label>Share Page</label>
                <img
                    :src="close ? require('@/assets/image/share-close-touched.png') : require('@/assets/image/share-close.png')"
                    class="cursor-pointer"
                    @mousedown="close=true"
                    @mouseup="close=false"
                    @mouseleave="close=false"
                    @click="$emit('close')"
                >
            </div>
            <div class="content display-flex flex-direction-column align-items-center">
                <div class="display-flex align-items-center">
                    <label>Page Name: </label>
                    <div class="page-name margin-left15px">
                        <input v-model="config.Title">
                    </div>
                </div>
                <div
                    :class="['link-btn', 'cursor-pointer', isClickLink ? 'click' : '']"
                    @mousedown="isClickLink=true"
                    @mouseup="isClickLink=false"
                    @mouseleave="isClickLink=false"
                    @click="getLink"
                >Get Link For Sharing</div>
                <div class="copied" v-show="isShowCopied">
                    <div class="text">Link Copied</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import {
    computed, defineComponent, ref, reactive,
    PropType
} from 'vue';
import HttpRequest from '@/util/axios.config';
import { toClipboard } from '@soerenmartius/vue3-clipboard';

interface Config {
    Uri: string;
    Region: string;
    Dis: string;
}
export default defineComponent({
    emits: ['close'],
    props: {
        baseUrl: {
            type: String,
            required: true
        },
        otherConfig: {
            type: Object as PropType<Config>,
            required: true
        }
    },
    setup(props) {
        const close = ref(false);
        const config = reactive({
            Title: ''
        });

        const isClickLink = ref(false);
        const isShowCopied = ref(false);
        const token = ref('');
        const url = computed(() => `${props.baseUrl}${token.value}`);
        const getLink = () => {
            HttpRequest.post('createToken', [{
                Config: JSON.stringify({ ...config, ...props.otherConfig })
            }, false], (res: {
                data: {
                    token: string;
                }
            }) => {
                token.value = res.data.token;
                toClipboard(url.value);
                isShowCopied.value = true;
                setTimeout(() => {
                    isShowCopied.value = false;
                }, 2000);
            });
        };

        return {
            close,
            config,
            isClickLink,
            isShowCopied,
            token,
            url,
            getLink
        };
    }
});
</script>

<style lang="less" scoped>
@import url("../../../assets/less/common.less");
.pop-background {
    background: rgba(2, 5, 19, 0.54);
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    justify-content: center;
}
.share-board {
    width: 670px;
    height: 358vh * @base;
    margin-top: 330vh * @base;
    background-image: url('../../../assets/image/reset-background.png');
    background-size: 100% 100%;
    .header {
        box-sizing: border-box;
        height: 67vh * @base;
        padding: 0 20px;
        color: #FFFFFF;
        font-size: 14vh * @base;
        justify-content: space-between;
        font-size: 24vh * @base;
        img {
            height: 20vh * @base;
            width: auto;
        }
    }
    .content {
        box-sizing: border-box;
        height: calc(100% - 67vh * @base);
        padding: 45vh * @base 20px;
        color: white;
        justify-content: space-between;
        position: relative;
        .page-name {
            border-radius: 8px;
            border: 1px solid #02A3FF;
            padding: 0 10px;
            box-sizing: border-box;
            height: 40vh * @base;
            width: 420px;
            input {
                height: 100%;
            }
        }
        .link-btn {
            width: 330px;
            height: 60vh * @base;
            line-height: 60vh * @base;
            background: #02A3FF;
            border-radius: 8px;
            font-size: 24vh * @base;
            text-align: center;
        }
        .copied {
            position: absolute;
            bottom: 105vh * @base;
            height: 57vh * @base;
            width: 144px;
            background-image: url('../../../assets/image/copied.png');
            background-size: 100% 100%;
            text-align: center;
            .text {
                margin-top: 12vh * @base;
                font-size: 16vh * @base;
            }
        }
        .click {
            background: #0172B3;
        }
    }
}
</style>
