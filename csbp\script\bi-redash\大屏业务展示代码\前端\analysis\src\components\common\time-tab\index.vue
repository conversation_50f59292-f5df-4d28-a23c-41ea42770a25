<template>
    <div class="width137px height27vh display-flex outer cursor-pointer">
        <div :class="['flex1', type==='yearly'?'active':'']" @click="$emit('click', 'yearly')">Yearly</div>
        <div :class="['flex1', type==='monthly'?'active':'']" @click="$emit('click', 'monthly')">Monthly</div>
    </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
    emits: ['click'],
    props: {
        type: {
            type: String,
            default: 'monthly'
        }
    },
    setup() {
        //
    }
});
</script>

<style lang="less" scoped>
@import url('../../../assets/less/common.less');
.outer {
    background: rgba(3, 5, 23, 0.1);
    box-shadow: inset 0px 1px 17px 0px rgba(31, 94, 137, 0.25);
    border: 1px solid rgba(2, 163, 255, 0.6);
    color: #FFFFFF;
    text-align: center;
    font-size: 14vh * @base;
    line-height: 27vh * @base;
}
.active {
    background: rgba(2, 163, 255, 0.6);
}
</style>
