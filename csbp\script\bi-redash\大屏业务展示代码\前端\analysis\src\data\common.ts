import { ref, reactive } from 'vue';

const identity = ref<'Admin' | 'User'>('Admin');
const userData = reactive({
    userName: '',
    Email: ''
});

const server: {
    [key in string]: string;
} = {
    CHN: 'China Mainland',
    ASIA: 'Asia',
    USA: 'America',
    EUR: 'EMEA',
    JPN: 'Japan',
    INDIA: 'India',
    RU: 'Russia',
    INC: 'The Middle East'
};

export default null;
export {
    identity,
    userData,
    server
};
