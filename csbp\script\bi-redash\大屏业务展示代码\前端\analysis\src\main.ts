import { createApp } from 'vue';
import ElementPlus from 'element-plus';
import { VueClipboard } from '@soerenmartius/vue3-clipboard';
import App from './App.vue';
import 'element-plus/dist/index.css';
import './assets/less/common.less';
import './registerServiceWorker';
import router from './router';
import store from './store';

// eslint-disable-next-line no-extend-native
String.prototype.format = function (...restOfParams: string[]) {
    if (restOfParams.length === 0) return this.toString();
    const param = restOfParams[0];
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    let s = this;
    if (typeof (param) === 'object') {
        Object.keys(param).forEach((key) => {
            s = s.replace(new RegExp(`\\{${key}\\}`, 'g'), param[key]);
        });
        return s.toString();
    }
    for (let i = 0; i < arguments.length; i += 1) s = s.replace(new RegExp(`\\{${i}\\}`, 'g'), restOfParams[i]);
    return s.toString();
};

createApp(App)
    .use(store)
    .use(router)
    .use(ElementPlus)
    .use(VueClipboard)
    .mount('#app');
