import CusToast from '@/components/common/customize-toast/index.vue';
import { h, render } from 'vue';

const createToast: (text: string, callback: () => void) => void = (text: string, callback: () => void) => {
    const d = document.createElement('div');
    const vnode = h(CusToast, {
        text,
        clickFun: () => {
            callback();
            document.body.removeChild(document.body.lastChild!);
        }
    });
    render(vnode, d);
    document.body.appendChild(d.firstElementChild!);
};

const formatNumber = (num: number | string) => Number(num).toLocaleString();

export default null;
export {
    createToast,
    formatNumber
};