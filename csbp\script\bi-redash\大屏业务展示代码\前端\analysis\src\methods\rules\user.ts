import { Ref } from 'vue';

type RuleMethod = (rule: any, value: string | number, callback: (error?: Error) => any) => any;

/**
 * @name 密码四种规则的满足情况
 */
const checkPasswordComplexity = (value: string) => {
    const regLow = /[a-z]/;
    const regUp = /[A-Z]/;
    const regNumber = /[0-9]/;
    const regSpecial = /[`~!@#$^&*()=|{}':;',[\\\].<>/?]/;
    return {
        ruleLow: regLow.test(value),
        ruleUp: regUp.test(value),
        ruleNumber: regNumber.test(value),
        ruleSpecial: regSpecial.test(value)
    };
};

const checkPassword: RuleMethod = (rule, value, callback) => {
    const password = value as string;
    const {
        ruleLow, ruleUp, ruleNumber, ruleSpecial
    } = checkPasswordComplexity(password);
    if (password.length < 8) {
        return callback(new Error('The Password must be more than 8 characters.'));
    } if (password.length > 20) {
        return callback(new Error('The Password must be less than 20 characters.'));
    } if (/\s/.test(password)) {
        return callback(new Error('Password cannot contain spaces.'));
    }

    const complexityGrade = (ruleLow as unknown as number)
        + (ruleUp as unknown as number)
        + (ruleNumber as unknown as number)
        + (ruleSpecial as unknown as number);
    if (complexityGrade < 3) {
        return callback(new Error('Password is too weak.'));
    }

    return callback();
};

const checkConfirmPassword = (password: Ref<string>): RuleMethod => (rule, value, callback) => {
    const confirm = value as string;
    if (confirm !== password.value) {
        return callback(new Error('Confirm password and new password do not match!'));
    }
    return callback();
};

const checkCurrentPassword = (error: Ref<boolean>): RuleMethod => (rule, value, callback) => {
    if (error.value) {
        return callback(new Error('Incorrect current password!'));
    }
    return callback();
};

const checkEmail: RuleMethod = (rule, value, callback) => {
    if (value === '') return callback();
    const reg = /^[.a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
    if (!reg.test(value as string)) {
        return callback(new Error('Please enter a valid email address.'));
    }
    return callback();
};
const checkExistEmail = (error: Ref<boolean>, msg: Ref<string>): RuleMethod => (rule, value, callback) => {
    if (error.value) {
        return callback(new Error(msg.value));
    }
    return callback();
};

export default null;
export {
    checkPasswordComplexity,
    checkPassword,
    checkConfirmPassword,
    checkCurrentPassword,
    checkEmail,
    checkExistEmail
};
