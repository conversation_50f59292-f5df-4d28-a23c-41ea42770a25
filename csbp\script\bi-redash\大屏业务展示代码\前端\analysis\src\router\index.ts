import { createRouter, createWeb<PERSON>ash<PERSON><PERSON><PERSON>, RouteRecordRaw } from 'vue-router';

// const index = () => import('@/views/base/index.vue');
const login = () => import('@/views/login/index.vue');
const akcsGlobal = () => import('@/views/global/index.vue');
const server = () => import('@/views/server/index.vue');
const dis = () => import('@/views/dis/index.vue');
const user = () => import('@/views/user/index.vue');
const resetPassword = () => import('@/views/reset-password/index.vue');
const resetExpired = () => import('@/views/reset-password/expired.vue');

const routes: Array<RouteRecordRaw> = [{
    path: '/',
    redirect: '/login'
}, {
    path: '/login',
    name: 'login',
    component: login
}, {
    path: '/global',
    name: 'global',
    component: akcsGlobal,
    props: (route) => ({
        token: route.query.token
    })
}, {
    path: '/server',
    name: 'server',
    component: server,
    props: (route) => ({
        type: route.query.type,
        token: route.query.token
    })
}, {
    path: '/dis',
    name: 'dis',
    component: dis,
    props: (route) => ({
        type: route.query.type,
        server: route.query.server,
        token: route.query.token
    })
}, {
    path: '/user',
    name: 'user',
    component: user,
    props: (route) => ({
        type: route.query.type
    })
}, {
    path: '/resetPassword',
    name: 'resetPassword',
    component: resetPassword,
    props: (route) => ({
        Token: route.query.Token
    })
}, {
    path: '/resetExpired',
    name: 'resetExpired',
    component: resetExpired
}];

const router = createRouter({
    history: createWebHashHistory(),
    routes
});

router.beforeEach((to, from, next) => {
    const { matched } = to;
    if (matched.length !== 0) {
        next();
    }
});

export default router;