import axios from 'axios';
import { mockList, mockUrl } from '@/util/mock.config';
import { createToast } from '@/methods/base';
import goBackLogin from '../router/back-login';
import { showLoading, closeLoading } from './loading';

axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded';

let showNotice = true;

/* eslint-disable @typescript-eslint/ban-types */
function createRejection(callback: Function) {
    return (error: any) => {
        if (error.code) {
            if (showNotice) {
                showNotice = false;
                if (error.code === 1001) {
                    createToast(error.msg, () => {
                        goBackLogin();
                    });
                } else {
                    createToast(error.msg, () => {
                        callback(error);
                    });
                }
            } else {
                callback(error);
            }
        } else {
            callback(error);
        }
    };
}
let host = '';
const version = '6.3';

if (process.env.NODE_ENV === 'development') {
    host = '/api/';
    // host = 'http://bigScreen.com/';
} else {
    host = '/';
}

// 添加请求拦截器
axios.interceptors.request.use((config) => {
    const config2 = { ...config };
    (config2.headers as any).common['x-auth-token'] = localStorage.getItem('token');
    return config2;
}, (error) => Promise.reject(error));

// 添加响应拦截器
axios.interceptors.response.use((response) => {
    const { data } = response;
    if (data.code !== 0) return Promise.reject(data);
    return data;
}, (error) => Promise.reject(error));

const defaultError = (error?: any): void => { console.error(error); };

interface Param {
    [key: string]: any;
}

type PostParam = Param | FormData;
type getParm = Param;

class HttpRequest {
    http: any;

    host: string;

    version: string;

    defaultUrl: string;

    constructor() {
        this.http = axios;
        this.host = host;
        this.version = version;
        this.defaultUrl = `${host}bigScreen/`;
    }

    private dealParam(params: Param, searchParams: URLSearchParams, keyPrefix = '{0}') {
        Object.keys(params).forEach((key) => {
            if (typeof params[key] === 'string' || typeof params[key] === 'number') {
                searchParams.append(keyPrefix.format(key), params[key]);
            } else if (typeof params[key] === 'object' && params[key] !== null) {
                this.dealParam(params[key], searchParams, `${keyPrefix.format(key)}[{0}]`);
            }
        });
    }

    private getUrl(url: string) {
        // v3开头全部访问新接口
        if (process.env.NODE_ENV === 'development' && mockList.includes(url)) {
            return `${mockUrl}bigScreen/${url}`;
        }
        return `${host}bigScreen/${url}`;
    }

    get(url: string, params: getParm, successCall: Function, errorCall = defaultError) {
        const requestUrl = this.getUrl(url);
        this.http.get(requestUrl, {
            params
        }).then((data: object) => {
            successCall(data);
        }).catch((error: any) => {
            createRejection(errorCall)(error);
        });
    }

    post(url: string, params: PostParam | [PostParam, Boolean], successCall: Function, errorCall: [Function, Boolean] | Function = defaultError) {
        const requestUrl = this.getUrl(url);
        const newParams = new URLSearchParams();
        let loading = true;
        let finalParams = params;
        if (Array.isArray(params)) {
            [finalParams, loading] = params as [Function, boolean];
        }
        if (loading) {
            showLoading();
        }
        if (!(finalParams instanceof FormData)) {
            this.dealParam(finalParams, newParams);
        }
        this.http.post(requestUrl, finalParams instanceof FormData ? finalParams : newParams).then((data: any) => {
            if (loading) {
                closeLoading();
            }
            successCall(data);
        }).catch((error: any) => {
            if (loading) {
                closeLoading();
            }
            if (Array.isArray(errorCall)) {
                errorCall[0](error);
            } else {
                createRejection(errorCall)(error);
            }
        });
    }
}
export default new HttpRequest();
