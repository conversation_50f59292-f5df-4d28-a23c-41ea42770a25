<template>
    <div class="body-container full-container base-background">
        <div class="light-background">
            <div class="height30vh margin-bottom15vh">
                <cus-header :show-share="token===''" @share="showShareDialog=true">
                    <div class="display-flex">
                        <select-list
                            :title="serverForCode[server]"
                            :list="serverList"
                            :active="server"
                            v-if="token===''"
                            @click="goToSeverPage"
                        ></select-list>
                        <select-list
                            :list="disList"
                            :title="pageName"
                            :active="type"
                            @click="goToDisPage"
                            :class="token===''?'margin-left40px':''"
                        ></select-list>
                    </div>
                </cus-header>
            </div>
            <div>
                <div class="content1 display-flex height464vh">
                    <div class="display-flex flex1 flex-direction-column">
                        <card-icon
                            :data="cardInfo.projects"
                            class="flex1"
                            height="100%"
                            width="auto"
                        ></card-icon>
                        <card-icon
                            :data="cardInfo.users"
                            class="margin-top22vh flex1"
                            height="100%"
                            width="auto"
                        ></card-icon>
                        <card-icon
                            :data="cardInfo.subscribers"
                            class="margin-top22vh flex1"
                            height="100%"
                            width="auto"
                        ></card-icon>
                    </div>
                    <div class="margin-left30px display-flex flex4">
                        <card-icon
                            type="customize"
                            height="calc(100% - 2px)"
                            width="100%"
                            title="Installer Community Project Distribution Graph"
                        >
                            <div id="graph" style="width: 100%"></div>
                        </card-icon>
                    </div>
                </div>
                <div class="content2 margin-top22vh display-flex height950vh">
                    <div class="display-flex flex-direction-column flex1">
                        <card-icon
                            :data="cardInfo.doorLogs"
                            class="flex1"
                            height="100%"
                            width="auto"
                        ></card-icon>
                        <card-icon
                            :data="cardInfo.todayDoorLogs"
                            class="margin-top22vh flex1"
                            height="100%"
                            width="auto"
                        ></card-icon>
                        <card-icon
                            :data="cardInfo.callLogs"
                            class="margin-top22vh flex1"
                            height="100%"
                            width="auto"
                        ></card-icon>
                        <card-icon
                            :data="cardInfo.todayCallLogs"
                            class="margin-top22vh flex1"
                            height="100%"
                            width="auto"
                        ></card-icon>
                        <card-icon
                            :data="cardInfo.registeredDevice"
                            class="margin-top22vh flex1"
                            height="100%"
                            width="auto"
                        ></card-icon>
                        <card-icon
                            :data="cardInfo.onlineDevice"
                            class="margin-top22vh flex1"
                            height="100%"
                            width="auto"
                        ></card-icon>
                    </div>
                    <div class="margin-left30px display-flex flex4 flex-direction-column">
                        <div class="display-flex">
                            <div class="flex1 height302vh">
                                <card-icon
                                    type="customize"
                                    height="calc(100% - 2px)"
                                    width="100%"
                                    title="Family / Office users trend"
                                >
                                    <template v-slot:header>
                                        <time-tab :type="timeType.users" @click="setTimeType($event, 'users')"></time-tab>
                                    </template>
                                    <div id="family-office-trend"></div>
                                </card-icon>
                            </div>
                            <div class="margin-left30px flex1 height302vh">
                                <card-icon
                                    type="customize"
                                    height="calc(100% - 2px)"
                                    width="100%"
                                    title="Subscribers trend"
                                >
                                    <template v-slot:header>
                                        <time-tab :type="timeType.users" @click="setTimeType($event, 'users')"></time-tab>
                                    </template>
                                    <div id="subscribers-trend"></div>
                                </card-icon>
                            </div>
                        </div>
                        <div class="display-flex margin-top22vh">
                            <div class="flex1 height302vh">
                                <card-icon
                                    type="customize"
                                    height="calc(100% - 2px)"
                                    width="100%"
                                    title="Door logs trend"
                                >
                                    <template v-slot:header>
                                        <time-tab :type="timeType.doorLogs" @click="setTimeType($event, 'doorLogs')"></time-tab>
                                    </template>
                                    <div id="door-logs-trend"></div>
                                </card-icon>
                            </div>
                            <div class="margin-left30px flex1 height302vh">
                                <card-icon
                                    type="customize"
                                    height="calc(100% - 2px)"
                                    width="100%"
                                    title="Proportion of each door opening method"
                                >
                                    <div id="door-type"></div>
                                </card-icon>
                            </div>
                        </div>
                        <div class="display-flex margin-top22vh">
                            <div class="flex1 height302vh">
                                <card-icon
                                    type="customize"
                                    height="calc(100% - 2px)"
                                    width="100%"
                                    title="Call logs trend"
                                >
                                    <template v-slot:header>
                                        <time-tab :type="timeType.callLogs" @click="setTimeType($event, 'callLogs')"></time-tab>
                                    </template>
                                    <div id="call-logs-trend"></div>
                                </card-icon>
                            </div>
                            <div class="margin-left30px flex1 height302vh">
                                <card-icon
                                    type="customize"
                                    height="calc(100% - 2px)"
                                    width="100%"
                                    title="Devices trend"
                                >
                                    <template v-slot:header>
                                        <time-tab :type="timeType.devices" @click="setTimeType($event, 'devices')"></time-tab>
                                    </template>
                                    <div id="device"></div>
                                </card-icon>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="content3 margin-top22vh"></div>
                <div class="content4 margin-top22vh"></div>
            </div>
        </div>
        <share-popup
            v-if="showShareDialog"
            @close="showShareDialog=false"
            :base-url="shareUrl"
            :other-config="{
                Uri: 'getAkcsRegionDisData',
                Region: server,
                Dis: type
            }"
        ></share-popup>
    </div>
</template>

<script lang="ts">
import { defineComponent, Ref, ref } from 'vue';
import CardIcon from '@/components/common/card-icon/index.vue';
import CusHeader from '@/components/common/header/index.vue';
import SelectList, { OptionsType } from '@/components/common/select-list';
import SharePopup from '@/components/common/share-popup/index.vue';
import { server as serverForCode } from '@/data/common';
import { useRouter } from 'vue-router';
import HttpRequest from '@/util/axios.config';
import TimeTab from '@/components/common/time-tab/index.vue';
import getData, { controlTimeType } from './util';

export default defineComponent({
    components: {
        CardIcon,
        CusHeader,
        SelectList,
        SharePopup,
        TimeTab
    },
    props: {
        type: {
            type: String,
            required: true
        },
        server: {
            type: String,
            required: true
        },
        token: {
            type: String,
            default: ''
        }
    },
    setup(props) {
        const {
            disList,
            cardInfo
        } = getData(props.server, props.type, props.token);

        const pageName = ref('');
        const serverList: Ref<Array<OptionsType>> = ref([]);
        const getServerList = () => {
            HttpRequest.get('getConfig', props.token === '' ? {} : {
                Token: props.token
            }, (res: {
                data: {
                    admin: {
                        pageName: string;
                    },
                    serverList: Array<{
                        name: string;
                        code: string;
                    }>,
                    token: {
                        Title: string;
                    }
                }
            }) => {
                if (props.token !== '') {
                    pageName.value = res.data.token.Title === '' ? props.type : res.data.token.Title;
                } else {
                    pageName.value = res.data.admin.pageName ? res.data.admin.pageName : props.type;
                }
                serverList.value = [{
                    label: 'Global',
                    value: 'global'
                }];
                if (props.token === '') {
                    res.data.serverList.forEach((item) => {
                        serverList.value.push({
                            label: item.name,
                            value: item.code
                        });
                    });
                }
            });
        };
        getServerList();

        const router = useRouter();
        // 跳转服务器页面
        const goToSeverPage = (server: string) => {
            if (server === 'global') {
                router.push('/global');
            } else {
                router.push(`/server?type=${server}`);
            }
        };
        // 跳转代理商页面
        const goToDisPage = (dis: string) => {
            router.push(`/dis?type=${dis}&server=${props.server}`);
            getData(props.server, dis, props.token);
            getServerList();
        };

        const showShareDialog = ref(false);
        const shareUrl = `http://**************:1717/manage-new/#/dis?type=${props.type}&server=${props.server}&token=`;

        const {
            timeType,
            setTimeType
        } = controlTimeType();

        return {
            disList,
            cardInfo,
            serverList,
            goToSeverPage,
            goToDisPage,
            showShareDialog,
            shareUrl,
            pageName,
            timeType,
            setTimeType,
            serverForCode
        };
    }
});
</script>

<style lang="less" scoped>
@import url("../../assets/less/common.less");

</style>