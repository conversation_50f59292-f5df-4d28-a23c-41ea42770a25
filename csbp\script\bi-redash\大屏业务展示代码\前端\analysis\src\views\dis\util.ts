import { ref, nextTick } from 'vue';
import HttpRequest from '@/util/axios.config';
import { OptionsType } from '@/components/common/select-list';
import { formatNumber } from '@/methods/base';

declare const Highcharts: any;

const getAreaChart = (
    container: string, data: Array<any>,
    colors: Array<string>,
    style: 'area' | null = null
) => {
    let maxY = 0;
    data.forEach((item, index) => {
        let tempColors: string[] | string = colors[index].split(',');
        tempColors.splice(tempColors.length - 1, 1, '0)');
        tempColors = tempColors.join(',');
        /* eslint-disable no-param-reassign */
        item.fillColor = {
            color: 'white',
            linearGradient: {
                x1: 0, y1: 0, x2: 0, y2: 1
            },
            stops: [
                [0, style ? colors[index] : 'rgba(0,0,0,0)'],
                [1, style ? tempColors : 'rgba(0, 0, 0, 0)']
            ]
        };
        item.fillOpacity = 0.1;
        item.marker = {
            fillColor: '#D8D8D8',
            lineColor: colors[index],
            lineWidth: 2,
            radius: 4,
            symbol: 'circle'
        };
        item.data.forEach((innerItem: Array<any>) => {
            if (Number(innerItem[1]) > maxY) {
                // eslint-disable-next-line prefer-destructuring
                maxY = innerItem[1];
            }
        });
    });
    Highcharts.chart(container, {
        chart: {
            type: 'area',
            backgroundColor: 'transparent'
        },
        title: {
            text: ''
        },
        colors,
        xAxis: {
            type: 'category',
            labels: {
                step: Math.floor((data[0].data.length - 3) / 2),
                style: {
                    color: '#ffffff'
                }
            },
            lineColor: '#02A3FF'
        },
        yAxis: {
            min: 0,
            max: maxY,
            tickAmount: 4,
            title: {
                text: ''
            },
            gridLineColor: 'rgba(255, 255, 255, 0.1)',
            lineColor: '#02A3FF',
            lineWidth: 1
        },
        legend: {
            align: 'right',
            verticalAlign: 'top',
            symbolRadius: 0,
            itemStyle: {
                color: '#fff'
            },
            itemHoverStyle: {
                color: '#02A1FC'
            }
        },
        credits: {
            enabled: false
        },
        exporting: {
            enabled: false
        },
        series: data
    });
};

const getColumnCountChart = (
    container: string, data: Array<any>,
    colors: Array<string>,
    categories?: Array<string>
):void => {
    Highcharts.chart(container, {
        chart: {
            type: 'column',
            backgroundColor: 'transparent'
        },
        title: {
            text: ''
        },
        colors,
        xAxis: Array.isArray(categories) ? {
            categories,
            labels: {
                style: {
                    color: '#ffffff'
                }
            },
            lineColor: '#02A3FF'
        } : {
            type: 'category',
            labels: {
                step: Math.floor((data[0].data.length - 3) / 2),
                style: {
                    color: '#ffffff'
                }
            },
            lineColor: '#02A3FF'
        },
        yAxis: {
            min: 0,
            title: {
                text: ''
            },
            gridLineColor: 'rgba(255, 255, 255, 0.1)',
            lineColor: '#02A3FF',
            lineWidth: 1
        },
        legend: Array.isArray(categories) ? {
            align: 'right',
            verticalAlign: 'top',
            layout: 'vertical',
            backgroundColor: 'transparent',
            borderColor: 'transparent',
            borderWidth: 0,
            symbolRadius: 0,
            symbolHeight: 8,
            shadow: 'inset 0px 1px 15px 0px #1F5E89',
            itemStyle: {
                color: '#fff'
            },
            itemMarginBottom: 20,
            y: 10,
            itemHoverStyle: {
                color: '#02A1FC'
            }
        } : {
            align: 'right',
            verticalAlign: 'top',
            symbolRadius: 0,
            itemStyle: {
                color: '#fff'
            },
            itemHoverStyle: {
                color: '#02A1FC'
            }
        },
        plotOptions: {
            series: {
                borderColor: 'transparent'
            },
            column: Array.isArray(categories) ? {
                stacking: 'normal'
            } : {}
        },
        credits: {
            enabled: false
        },
        exporting: {
            enabled: false
        },
        series: data
    });
};

const getPieChart = (data: Array<any>): void => {
    Highcharts.chart('door-type', {
        chart: {
            type: 'pie',
            backgroundColor: 'transparent'
        },
        colors: ['#006CFF', '#0094FF', '#04C8DC',
            '#00F6BB', '#00CD0B', '#803FFF', '#522BFF'
        ],
        legend: {
            align: 'right',
            verticalAlign: 'top',
            layout: 'vertical',
            backgroundColor: 'transparent',
            borderColor: 'transparent',
            borderWidth: 0,
            symbolRadius: 0,
            symbolHeight: 8,
            shadow: 'inset 0px 1px 15px 0px #1F5E89',
            itemStyle: {
                color: '#fff',
                x: 50
            },
            itemMarginBottom: 10,
            y: 10,
            itemHoverStyle: {
                color: '#02A1FC'
            }
        },
        title: {
            text: '',
            style: {
                color: 'white'
            }
        },
        credits: {
            enabled: false
        },
        plotOptions: {
            pie: {
                dataLabels: {
                    enabled: true,
                    style: {
                        fontWeight: 'bold',
                        color: 'white',
                        textShadow: '0px 1px 2px black'
                    },
                    format: '{point.percentage:.1f} %'
                },
                showInLegend: true,
                borderColor: 'transparent'
            }
        },
        series: [{
            type: 'pie',
            // name: '',
            innerSize: '50%',
            colorByPoint: true,
            data
        }],
        exporting: {
            enabled: false
        }
    });
};

const cardInfo = ref({
    projects: {
        title: 'Projects',
        icon: require('@/assets/image/dis/project-counts.png'),
        text: '',
        textColor: '#00CD0B'
    },
    users: {
        title: 'Users',
        icon: require('@/assets/image/dis/user-counts.png'),
        text: '',
        textColor: '#00CD0B'
    },
    subscribers: {
        title: 'Subscribers',
        icon: require('@/assets/image/month-user-num.png'),
        text: ''
    },
    doorLogs: {
        title: 'Door Logs',
        icon: require('@/assets/image/all-open-door-count.png'),
        text: '',
        textColor: '#00F6BB'
    },
    todayDoorLogs: {
        title: 'Today - Door Logs',
        icon: require('@/assets/image/today-open-door-count.png'),
        text: ''
    },
    callLogs: {
        title: 'Call Logs',
        icon: require('@/assets/image/all-call-count.png'),
        text: '',
        textColor: '#00F6BB'
    },
    todayCallLogs: {
        title: 'Today -  Call Logs',
        icon: require('@/assets/image/today-call-count.png'),
        text: ''
    },
    registeredDevice: {
        title: 'Devices on the cloud',
        icon: require('@/assets/image/registered-device-count.png'),
        text: ''
    },
    onlineDevice: {
        title: 'Devices online',
        icon: require('@/assets/image/online-device-count.png'),
        text: ''
    }
});

type KeyIsString = 'TotalProjects' | 'UserNum' | 'MonthFeeUserNum' | 'TotalOpenDoorNum' | 'TodayOpenDoorNum'
    | 'TotalCallNum' | 'TodayCallNum' | 'RegisteredDeviceNum' | 'DeviceOnline';
type KeyIsArrayObject = 'FamilyAndOFficeNumMonth' | 'FamilyAndOFficeNumYear' | 'DeviceListMonth' | 'DeviceListYear'
    | 'CallNumListMonth' | 'CallNumListYear'
    | 'OpenDoorNumListMonth' | 'OpenDoorNumListYear' | 'LiveView' | 'OpenDoorSuccessType' | 'MonthFeeUserMonthNumMonth' | 'MonthFeeUserMonthNumYear';
/* eslint-disable @typescript-eslint/ban-types */
type AnalysisData = {
    [key in KeyIsString]: string;
} & {
    [key in KeyIsArrayObject]: Array<any>;
} & {
    disList: Array<string>
} & {
    InstallerProjects: Array<{
        [key in string]: string;
    }>
}

let insSizeType: {
    [key in string]: number[]
} = {
    '1-10 households': [],
    '11-25 households': [],
    '26-50 households': [],
    '51-100 households': [],
    '101-200 households': [],
    '201-500 households': [],
    'more than 500 households': []
};

const timeData = ref<{
    [key in string] : {
        [type in string]: any[]
    }
}>({
    monthly: {
        users: [],
        subscribers: [],
        devices: [],
        callLogs: [],
        doorLogs: []
    },
    yearly: {
        users: [],
        subscribers: [],
        devices: [],
        callLogs: [],
        doorLogs: []
    }
});

const disList = ref<OptionsType[]>([]);
const getData = (server: string, dis: string, token: string) => {
    HttpRequest.get('getAkcsRegionDisData', token === '' ? {
        Region: server,
        Dis: dis
    } : {
        Region: server,
        Dis: dis,
        Token: token
    }, (res: {
        data: AnalysisData
    }) => {
        const { data } = res;

        cardInfo.value.projects.text = formatNumber(data.TotalProjects || '0');
        cardInfo.value.users.text = formatNumber(data.UserNum || '0');
        cardInfo.value.subscribers.text = formatNumber(data.MonthFeeUserNum || '0');
        cardInfo.value.doorLogs.text = formatNumber(data.TotalOpenDoorNum || '0');
        cardInfo.value.todayDoorLogs.text = formatNumber(data.TodayOpenDoorNum || '0');
        cardInfo.value.callLogs.text = formatNumber(data.TotalCallNum || '0');
        cardInfo.value.todayCallLogs.text = formatNumber(data.TodayCallNum || '0');
        cardInfo.value.registeredDevice.text = formatNumber(data.RegisteredDeviceNum || '0');
        cardInfo.value.onlineDevice.text = formatNumber(data.DeviceOnline || '0');

        // 保存当前服务器下的代理商
        disList.value = [];
        if (token === '') {
            res.data.disList.forEach((item) => {
                disList.value.push({
                    label: item,
                    value: item
                });
            });
        }

        // 统计installer下数量
        const insCategories: string[] = [];
        data.InstallerProjects.forEach((item) => {
            if (!insCategories.includes(item.Ins)) {
                insCategories.push(item.Ins);
            }
        });
        insSizeType = {
            '1-10 households': [],
            '11-25 households': [],
            '26-50 households': [],
            '51-100 households': [],
            '101-200 households': [],
            '201-500 households': [],
            'more than 500 households': []
        };
        insCategories.forEach((item) => {
            data.InstallerProjects.forEach((projectsItem) => {
                if (item === projectsItem.Ins) {
                    insSizeType[projectsItem.SIZE].push(Number(projectsItem.project_num));
                }
            });
        });
        const seriesData = [{
            name: 'more than 500 households',
            data: insSizeType['more than 500 households']
        }, {
            name: '201-500 households',
            data: insSizeType['201-500 households']
        }, {
            name: '101-200 households',
            data: insSizeType['101-200 households']
        }, {
            name: '51-100 households',
            data: insSizeType['51-100 households']
        }, {
            name: '26-50 households',
            data: insSizeType['26-50 households']
        }, {
            name: '11-25 households',
            data: insSizeType['11-25 households']
        }, {
            name: '1-10 households',
            data: insSizeType['1-10 households']
        }];

        // users 年月数据保存
        const activeNum = <any>[{
            name: 'Family users',
            data: []
        }, {
            name: 'Office users',
            data: []
        }];
        data.FamilyAndOFficeNumMonth.forEach((item) => {
            activeNum[0].data.push([item.DateTime, Number(item.FamilyNum)]);
            activeNum[1].data.push([item.DateTime, Number(item.OfficeNum)]);
        });
        timeData.value.monthly.users = activeNum;
        const activeNumYear = <any>[{
            name: 'Family users',
            data: []
        }, {
            name: 'Office users',
            data: []
        }];
        data.FamilyAndOFficeNumYear.forEach((item) => {
            activeNumYear[0].data.push([item.DateTime, Number(item.FamilyNum)]);
            activeNumYear[1].data.push([item.DateTime, Number(item.OfficeNum)]);
        });
        timeData.value.yearly.users = activeNumYear;

        // subscribers 年月数据保存
        const monthFeeUserMonthNum = <any>[{
            name: 'Subscribers',
            data: []
        }];
        data.MonthFeeUserMonthNumMonth.forEach((item) => {
            monthFeeUserMonthNum[0].data.push([item.DateTime, Number(item.FeeFamilyNum)]);
        });
        timeData.value.monthly.subscribers = monthFeeUserMonthNum;
        const monthFeeUserMonthNumYear = <any>[{
            name: 'Subscribers',
            data: []
        }];
        data.MonthFeeUserMonthNumYear.forEach((item) => {
            monthFeeUserMonthNumYear[0].data.push([item.DateTime, Number(item.FeeFamilyNum)]);
        });
        timeData.value.yearly.subscribers = monthFeeUserMonthNumYear;

        // doorLogs 年月数据保存
        const doorNum = <any>[{
            name: 'Door logs',
            data: []
        }];
        data.OpenDoorNumListMonth.forEach((item) => {
            doorNum[0].data.push([item.DateTime, Number(item.Num)]);
        });
        timeData.value.monthly.doorLogs = doorNum;
        const doorNumYear = <any>[{
            name: 'Door logs',
            data: []
        }];
        data.OpenDoorNumListYear.forEach((item) => {
            doorNumYear[0].data.push([item.DateTime, Number(item.Num)]);
        });
        timeData.value.yearly.doorLogs = doorNumYear;

        // 开门成功类型
        const doorType: Array<{
            name: string;
            y: number;
        }> = [];
        data.OpenDoorSuccessType.forEach((item) => {
            doorType.push({
                name: item.OpenDoorType,
                y: Number(item.OpenDoorNum)
            });
        });

        // callLogs 年月数据保存
        const callNum = <any>[{
            name: 'Call logs',
            data: []
        }];
        data.CallNumListMonth.forEach((item) => {
            callNum[0].data.push([item.DateTime, Number(item.Num)]);
        });
        timeData.value.monthly.callLogs = callNum;
        const callNumYear = <any>[{
            name: 'Call logs',
            data: []
        }];
        data.CallNumListYear.forEach((item) => {
            callNumYear[0].data.push([item.DateTime, Number(item.Num)]);
        });
        timeData.value.yearly.callLogs = callNumYear;

        // device 年月数据保存
        const deviceNum = <any>[{
            name: 'Online devices',
            data: []
        }, {
            name: 'Registered devices',
            data: []
        }];
        data.DeviceListMonth.forEach((item) => {
            deviceNum[0].data.push([item.DateTime, Number(item.online_num)]);
            deviceNum[1].data.push([item.DateTime, Number(item.register_num)]);
        });
        timeData.value.monthly.devices = deviceNum;
        const deviceNumYear = <any>[{
            name: 'Online devices',
            data: []
        }, {
            name: 'Registered devices',
            data: []
        }];
        data.DeviceListYear.forEach((item) => {
            deviceNumYear[0].data.push([item.DateTime, Number(item.online_num)]);
            deviceNumYear[1].data.push([item.DateTime, Number(item.register_num)]);
        });
        timeData.value.yearly.devices = deviceNumYear;

        nextTick(() => {
            getColumnCountChart('graph', seriesData, ['#006CFF', '#0094FF', '#04C8DC', '#00F6BB', '#00CD0B', '#803FFF', '#522BFF'], insCategories);

            getAreaChart('family-office-trend', timeData.value.monthly.users, ['rgba(128, 63, 255, 1)', 'rgba(0, 108, 255, 1)']);
            getAreaChart('subscribers-trend', timeData.value.monthly.subscribers, ['rgba(22, 211, 166, 1)'], 'area');

            getColumnCountChart('door-logs-trend', timeData.value.monthly.doorLogs, ['#006CFF']);
            getPieChart(doorType);

            getColumnCountChart('call-logs-trend', timeData.value.monthly.callLogs, ['#16D3A6']);
            getAreaChart('device', timeData.value.monthly.devices, ['#23D26A', '#006CFF']);
        });
    });

    return {
        disList,
        cardInfo
    };
};

const controlTimeType = () => {
    const timeType = ref<{
        [key in string]: string;
    }>({
        users: 'monthly',
        subscribers: 'monthly',
        devices: 'monthly',
        callLogs: 'monthly',
        doorLogs: 'monthly'
    });
    const setTimeType = (time: string, type: string) => {
        timeType.value[type] = time;
        if (type === 'users') {
            getAreaChart('family-office-trend', timeData.value[time].users, ['rgba(128, 63, 255, 1)', 'rgba(0, 108, 255, 1)']);
        } else if (type === 'subscribers') {
            getAreaChart('subscribers-trend', timeData.value[time].subscribers, ['rgba(22, 211, 166, 1)'], 'area');
        } else if (type === 'devices') {
            getAreaChart('device', timeData.value[time].devices, ['#23D26A', '#006CFF']);
        } else if (type === 'callLogs') {
            getColumnCountChart('call-logs-trend', timeData.value[time].callLogs, ['#16D3A6']);
        } else if (type === 'doorLogs') {
            getColumnCountChart('door-logs-trend', timeData.value[time].doorLogs, ['#006CFF']);
        }
    };
    return {
        timeType,
        setTimeType
    };
};

export default getData;
export {
    controlTimeType
};