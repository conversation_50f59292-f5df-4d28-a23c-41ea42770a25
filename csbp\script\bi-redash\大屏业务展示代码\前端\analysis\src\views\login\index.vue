<template>
    <div class="login-container">
        <div class="main-area display-flex flex-direction-column align-items-center">
            <img src="@/assets/image/login-icon.png" class="header-icon">
            <div class="input-container normal-container" v-show="!isReset">
                <div class="login-text">Login</div>
                <div class="input-area display-flex flex-direction-column">
                    <div>
                        <div class="input">
                            <img src="@/assets/image/input-account.png" class="icon">
                            <div class="line"></div>
                            <input v-model="userData.Account" @blur="checkValid('name')">
                        </div>
                        <div class="height35vh">
                            <label v-if="showNameTips" class="error-label">Please enter the username</label>
                        </div>
                        <div class="input">
                            <img src="@/assets/image/input-lock.png" class="icon">
                            <div class="line"></div>
                            <input
                                v-model="userData.Password"
                                :type="encrypt?'password':'text'"
                            >
                            <div class="display-flex align-items-center">
                                <img
                                    :src="encrypt?require('@/assets/image/encrypt.png'):require('@/assets/image/not-encrypt.png')"
                                    :class="[encrypt?'encrypt':'not-encrypt', 'normal']"
                                    @click="encrypt=!encrypt"
                                >
                            </div>
                        </div>
                        <div class="height35vh forget-parent">
                            <label v-if="showPasswdTips" class="error-label">Invalid user name or password</label>
                            <label class="forget-label" @click="goToResetPage">Forgot Password</label>
                        </div>
                    </div>
                    <div
                        :class="['normal-btn', userData.Account === '' || userData.Password === '' ? 'unavailable' : loginBtnType]"
                        @mousedown="loginBtnType='touched'"
                        @mouseup="loginBtnType='normal'"
                        @click="login"
                    >
                        Login
                    </div>
                </div>
            </div>
            <div class="reset-container normal-container" v-show="isReset">
                <div class="header display-flex align-items-center">
                    <img src="../../assets/image/reset-back.png" class="cursor-pointer" @click="isReset=false">
                    <label>Forget Password</label>
                </div>
                <div class="reset-area display-flex flex-direction-column">
                    <template v-if="beforeReset">
                        <div class="reset-input">
                            <input v-model="userData.Email" @blur="checkValid('email')">
                            <div class="height35vh">
                                <label class="error-label">{{emailTips}}</label>
                            </div>
                        </div>
                        <div
                            :class="['normal-btn', userData.Email === '' ? 'unavailable' : loginBtnType]"
                            @mousedown="loginBtnType='touched'"
                            @mouseout="loginBtnType='normal'"
                            @click="reset"
                        >
                            Submit
                        </div>
                    </template>
                    <template v-if="!beforeReset">
                        <div style="color: white">We've sent an email to {{userData.Email}} with further instructions.</div>
                        <div
                            :class="['normal-btn', loginBtnType]"
                            @mousedown="loginBtnType='touched'"
                            @mouseout="loginBtnType='normal'"
                            @click="isReset=false"
                        >
                            OK
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, reactive, ref } from 'vue';
import HttpRequest from '@/util/axios.config';
import { useRouter } from 'vue-router';

export default defineComponent({
    setup() {
        const router = useRouter();

        const userData = reactive({
            Account: '',
            Password: '',
            Email: ''
        });

        const encrypt = ref(true);

        const showNameTips = ref(false);
        const showPasswdTips = ref(false);
        const emailTips = ref('');
        const checkValid = (type: 'name' | 'email') => {
            if (type === 'name') {
                showNameTips.value = userData.Account === '';
                return showNameTips.value;
            }
            if (type === 'email') {
                const reg = /^[.a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
                if (userData.Email === '') {
                    emailTips.value = 'Please enter the email.';
                } else if (!reg.test(userData.Email)) {
                    emailTips.value = 'Please enter a valid email address.';
                } else {
                    emailTips.value = '';
                }
                return emailTips.value !== '';
            }
            return true;
        };

        const loginBtnType = ref<'normal' | 'touched' | 'unavailable'>('normal');
        const groups = ['Admin', 'User'];
        const login = () => {
            if (loginBtnType.value === 'unavailable') {
                return;
            }
            loginBtnType.value = 'normal';
            if (checkValid('name')) {
                return;
            }
            HttpRequest.post('login', {
                Account: userData.Account,
                Password: userData.Password
            }, (res: {
                data: {
                    authToken: string;
                    Group: string;
                    Email: string;
                    Nickname: string;
                }
            }) => {
                localStorage.setItem('token', res.data.authToken);
                localStorage.setItem('userInfo', JSON.stringify({
                    userName: res.data.Nickname,
                    email: res.data.Email,
                    identity: groups[Number(res.data.Group) - 1]
                }));
                router.push('/global');
            }, () => {
                showPasswdTips.value = true;
            });
        };

        const isReset = ref(false);
        const beforeReset = ref(true);
        const goToResetPage = () => {
            isReset.value = true;
            beforeReset.value = true;
            userData.Email = '';
            emailTips.value = '';
        };
        const reset = () => {
            if (loginBtnType.value === 'unavailable') {
                return;
            }
            loginBtnType.value = 'normal';
            if (checkValid('email')) {
                return;
            }
            HttpRequest.post('resetPwdEmail', {
                Email: userData.Email
            }, () => {
                beforeReset.value = false;
            }, () => {
                emailTips.value = 'This Email doesn\'t exist.';
            });
        };

        return {
            userData,
            encrypt,
            showNameTips,
            showPasswdTips,
            emailTips,
            checkValid,
            loginBtnType,
            login,
            isReset,
            beforeReset,
            goToResetPage,
            reset
        };
    }
});
</script>

<style lang="less">
@import url("../../assets/less/common.less");
input {
    outline: none;
    background: transparent;
    border: none;
    color: white;
    font-size: 18px;
    width: 100%;
}
input:-internal-autofill-previewed,
input:-internal-autofill-selected {
    -webkit-text-fill-color: white;
    transition: background-color 5000s ease-out 0.5s;
}
.login-container {
    height: 100%;
    width: 100%;
    background-image: url('../../assets/image/login-background.png');
    background-size: 100% 100%;
    overflow: hidden;
    .header-icon {
        width: 18.5%;
        height: auto;
        margin-top: 212vh * @base;
    }
    .normal-container {
        width: 27%;
        margin-top: 45vh * @base;
        background-size: 100% 100%;
    }
    .input-container {
        height: 445vh * @base;
        background-image: url('../../assets/image/input-background.png');
        .login-text {
            line-height: 80vh * @base;
            height: 80vh * @base;
            font-size: 28px;
            color: white;
            text-align: center;
        }
        .input-area {
            padding: 30vh * @base 6% 47vh * @base 6%;
            height: 365vh * @base;
            box-sizing: border-box;
            justify-content: space-between;
            .input {
                border: 1px solid #02A3FF;
                border-radius: 8px;
                padding: 11.5vh * @base 4.4% 11.5vh * @base 2.6%;
                display: flex;
            }
            .input .icon {
                height: 32vh * @base;
            }
            .input .line {
                border: 1px solid #C0C4CC;
                opacity: 0.19;
                margin:0 2.6%;
            }
            .input .normal {
                margin-right: 2.6%;
                &:hover {
                    cursor: pointer;
                }
            }
            .input .encrypt {
                height: 23vh * @base;
            }
            .input .not-encrypt {
                height: 19vh * @base;
            }
            .forget-parent {
                position: relative;
            }
            .forget-label {
                color: white;
                font-size: 18px;
                position: absolute;
                top: 15vh * @base;
                right: 0;
                &:hover {
                    cursor: pointer;
                    color: #02A1FC;
                }
            }
        }
    }
    .reset-container {
        height: 359vh * @base;
        background-image: url('../../assets/image/reset-background.png');
        .header {
            height: 67vh * @base;
            padding: 0 3.9%;
        }
        .header img {
            height: 26vh * @base;
            width: auto;
        }
        .header label {
            margin-left: 3%;
            font-size: 24px;
            color: white;
        }
        .reset-area {
            box-sizing: border-box;
            height: 292vh *@base;
            padding: 67vh * @base 5.8% 40vh * @base;
            justify-content: space-between;
            .reset-input {
                width: 100%;
                height: 55vh * @base;
                border: 1px solid #02A3FF;
                border-radius: 8px;
                padding: 0 10px;
                box-sizing: border-box;
                input {
                    height: 100%;
                }
            }
        }
    }
}
.normal-btn {
    width: 100%;
    height: 60vh * @base;
    line-height: 60vh * @base;
    position: relative;
    background-size: 100% 100%;
    color: white;
    text-align: center;
    font-size: 24px;
}
.normal-btn.unavailable {
    background-image: url('../../assets/image/login-btn-unavailable.png');
}
.normal-btn.normal {
    background-image: url('../../assets/image/login-btn-normal.png');
    &:hover {
        cursor: pointer;
    }
}
.normal-btn.touched {
    background-image: url('../../assets/image/login-btn-touched.png');
}

.error-label {
    color: #FD5555;
    font-size: 14px;
    margin: 5vh * @base 0 0 14.7%;
}
</style>