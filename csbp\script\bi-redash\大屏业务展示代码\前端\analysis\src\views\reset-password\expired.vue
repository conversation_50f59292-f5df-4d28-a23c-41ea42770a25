<template>
    <div class="send-box display-flex justify-content-center align-items-center">
        <div class="send-board">
            <b>The reset password link has expired.</b>
            <b>To reset your password, Please enter your email address.</b>
            <el-form ref="formRef" :model="formData" :rules="rules">
                <el-form-item prop="Email">
                    <el-input class="height50vh margin-top40vh" v-model="formData.Email" placeholder="Email"></el-input>
                </el-form-item>
            </el-form>
            <cus-button height="40px" width="100%" type="blue" class="margin-top100vh" @click="submit">Submit</cus-button>
        </div>
    </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive } from 'vue';
import CusButton from '@/components/common/customize-button/index.vue';
import { user } from '@/methods/rule';
import HttpRequest from '@/util/axios.config';
import { ElMessage } from 'element-plus';

export default defineComponent({
    components: {
        CusButton
    },
    setup() {
        const formRef = ref();
        const formData = reactive({
            Email: ''
        });

        const rules = {
            Email: [{
                required: true,
                message: 'Please enter the email address.',
                trigger: 'blur'
            }, {
                validator: user.checkEmail,
                trigger: 'blur'
            }]
        };

        const submit = () => {
            formRef.value.validate((valid: boolean) => {
                if (valid) {
                    HttpRequest.post('resetPwdEmail', formData, (res: {
                        msg: string;
                    }) => {
                        ElMessage({
                            message: res.msg,
                            type: 'success'
                        });
                    }, (res: {
                        msg:string;
                    }) => {
                        ElMessage({
                            message: res.msg,
                            type: 'error'
                        });
                    });
                }
            });
        };

        return {
            formRef,
            formData,
            rules,
            submit
        };
    }
});
</script>

<style lang="less" scoped>
.send-box {
    height: 100%;
}
.send-board {
    width: 500px;
    height: auto;
    border: 1px solid grey;
    border-radius: 5px;
    padding: 40px;
    box-sizing: border-box;
}
</style>
