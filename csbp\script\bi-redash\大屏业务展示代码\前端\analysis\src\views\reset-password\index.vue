<template>
    <div class="pw-content">
        <div class="pw-title">
            <div class="pw-name display-flex align-items-center">
                <img src="@/assets/image/reset-icon.png" />
                <label class="margin-left30px">{{ nickName }}</label>
            </div>
            <p class="margin-top30vh">The new password must be between 8 and 20 characters, and cannot contains spaces.</p>
        </div>
        <div class="pw-input margin-top50vh">
            <el-form ref="formRef" :model="formData" :rules="rules">
                <el-form-item prop="NewPassword">
                    <el-input v-model="formData.NewPassword" placeholder="New Password" type="password"></el-input>
                </el-form-item>
                <el-form-item prop="Confirm">
                    <el-input v-model="formData.Confirm" placeholder="Confirm Password" type="password"></el-input>
                </el-form-item>
            </el-form>
            <ul class="limit">
                <li style="left: -10px;">Your password must meet three of the four terms below:</li>
                <li
                    v-for='item in rulesTips'
                    :class='item.valid ? "success":  "default"'
                    :key="item.name"
                >
                    <span></span> {{ item.content }}
                </li>
            </ul>
        </div>
        <cus-button type="blue" height="35px" width="150px" class="my-btn" @click="submit">Submit</cus-button>
    </div>
</template>

<script lang="ts">
import {
    defineComponent,
    reactive,
    ref,
    toRef,
    watch
} from 'vue';
import CusButton from '@/components/common/customize-button/index.vue';
import { user } from '@/methods/rule';
import HttpRequest from '@/util/axios.config';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';

export default defineComponent({
    components: {
        CusButton
    },
    props: {
        Token: {
            type: String,
            default: ''
        }
    },
    setup(props) {
        const formRef = ref();
        const formData = reactive({
            NewPassword: '',
            Confirm: ''
        });
        const rulesTips = reactive([{
            valid: false,
            content: 'at least one lowercase letter',
            name: 'lLetter'
        }, {
            valid: false,
            content: 'at least one uppercase letter',
            name: 'uLetter'
        }, {
            valid: false,
            content: 'at least one number',
            name: 'dLetter'
        }, {
            valid: false,
            content: 'at least one special character',
            name: 'tLetter'
        }]);
        watch(() => formData.NewPassword, () => {
            const {
                ruleLow, ruleUp, ruleNumber, ruleSpecial
            } = user.checkPasswordComplexity(formData.NewPassword);
            rulesTips[0].valid = ruleLow;
            rulesTips[1].valid = ruleUp;
            rulesTips[2].valid = ruleNumber;
            rulesTips[3].valid = ruleSpecial;
        });

        const rules = {
            NewPassword: [{
                required: true,
                message: 'Please enter the password.',
                trigger: 'blur'
            }, {
                validator: user.checkPassword,
                trigger: 'blur'
            }],
            Confirm: [{
                required: true,
                message: 'Please re-enter the password.',
                trigger: 'blur'
            }, {
                validator: user.checkConfirmPassword(toRef(formData, 'NewPassword')),
                trigger: 'blur'
            }]
        };

        const router = useRouter();
        const nickName = ref('');
        const checkTokenValid = () => {
            HttpRequest.post('emailTokenCheck', {
                Token: props.Token
            }, (res: {
                data: {
                    Nickname: string;
                };
            }) => {
                nickName.value = res.data.Nickname;
            }, [(res: {
                msg: string
            }) => {
                ElMessage({
                    message: res.msg,
                    type: 'error',
                    onClose: () => {
                        router.push('/resetExpired');
                    }
                });
            }, false]);
        };
        checkTokenValid();

        const submit = () => {
            formRef.value.validate((valid: boolean) => {
                if (valid) {
                    HttpRequest.post('resetPwd', {
                        NewPassword: formData.NewPassword,
                        Token: props.Token
                    }, () => {
                        ElMessage({
                            message: 'Modify Password Success',
                            type: 'success',
                            onClose: () => {
                                router.replace('/');
                            }
                        });
                    });
                }
            });
        };

        return {
            rulesTips,
            formData,
            rules,
            formRef,
            submit,
            nickName
        };
    }
});
</script>

<style lang="less" scoped>
.pw-content {
    padding: 100px 0 0;
    width: 700px;
    position: relative;
    left: 50%;
    transform: translateX(-60%);
    .pw-title {
        width: 80%;
        margin-left: 10%;
        img {
            width: 70px;
            height: auto;
        }
        label {
            font-size: 18px;
        }
    }
    .pw-input {
        width: 74%;
        margin-left: 10%;
        position: relative;
        input {
            color: black;
            border-bottom: 1px #c9c9c9 solid;
        }
    }
    .my-btn {
        font-size: 17px;
        margin: 60px 10%;
    }
}
ul.limit{
    position: absolute;
    top: -30px;
    width: 380px;
    height: auto;
    padding: 5px 15px;
    box-sizing: border-box;
    margin-top: 3px;
    margin-left: calc(100% + 50px);
    border: 1px solid gainsboro;
    background-color: white;
    li{
        line-height: 30px;
        list-style: none;
        position: relative;
        text-indent: 5px;
        padding-left: 15px;
    }
    li.success{
        color: green;
        span{
            width: 5px;
            height: 5px;
        }
        span:before{
            display: inline-block;
            position: absolute;
            width: 3px;
            height: 8px;
            border-bottom:solid 2px green;
            border-right:solid 2px green;
            content: '';
            left: 10px;
            top:10px;
            transform: rotate(30deg);
        }
    }
    li.default{
        color: red;
        span{
            width: 5px;
            height: 5px;
        }
        span:before{
            display: inline-block;
            position: absolute;
            width: 4px;
            height: 4px;
            border-bottom:solid 2px red;
            border-right:solid 2px red;
            content: '';
            left: 10px;
            top:10px;
            transform: rotate(45deg);
        }
        span:after{
            content: '';
            display: inline-block;
            position: absolute;
            width: 4px;
            height: 4px;
            border-bottom:solid 2px red;
            border-right:solid 2px red;
            left: 10px;
            top:16px;
            transform: rotate(-135deg);
        }
    }
}
</style>
