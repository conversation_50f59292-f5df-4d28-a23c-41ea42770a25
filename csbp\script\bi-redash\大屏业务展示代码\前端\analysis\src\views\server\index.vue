<template>
    <div class="body-container full-container dashboard">
        <div class="light-background">
            <div class="height30vh margin-bottom15vh">
                <cus-header :show-share="token===''" @share="showShareDialog=true">
                    <div class="display-flex">
                        <select-list
                            :list="serverList"
                            :title="pageName"
                            :active="type"
                            @click="goToServerPage"
                        ></select-list>
                        <select-list
                            :list="disList"
                            title="All"
                            @click="goToDisPage"
                            class="margin-left40px"
                            v-if="token===''"
                        ></select-list>
                    </div>
                </cus-header>
            </div>
            <div>
                <div class="first-row display-flex height788vh">
                    <div class="left flex1 display-flex flex-direction-column">
                        <card-icon
                            :data="cardInfo.families"
                            class="flex1"
                            height="100%"
                            width="auto"
                        ></card-icon>
                        <card-icon
                            :data="cardInfo.officeUsers"
                            class="margin-top22vh flex1"
                            height="100%"
                            width="auto"
                        ></card-icon>
                        <card-icon
                            :data="cardInfo.subscribers"
                            class="margin-top22vh flex1"
                            height="100%"
                            width="auto"
                        ></card-icon>
                        <card-icon
                            :data="cardInfo.devices"
                            class="margin-top22vh flex1"
                            width="auto"
                        ></card-icon>
                        <card-icon
                            :data="cardInfo.devicesOnline"
                            class="margin-top22vh flex1"
                            width="auto"
                        ></card-icon>
                    </div>
                    <div class="middle flex3 margin-left30px" ref="globalRef">
                        <card-icon type="customize" :showTitle="false" height="calc(100% - 2px)" width="auto">
                            <div style="width: 100%;height: 100%;">
                                <div ref="headerRef">
                                    <div class="card-title">
                                        Business Distribution
                                    </div>
                                    <div class="margin-top39vh display-flex align-items-center justify-content-center">
                                        <div>Total users: </div>
                                        <div class="total-num"><b>{{ formatNumber(totalUserNum) }}</b></div>
                                        <!-- <num-show :number="totalUserNum" class="margin-left25px"></num-show> -->
                                    </div>
                                    <div class="height39vh"></div>
                                </div>
                                <div id="global-container" style="width: 100%;" ref="mapRef"></div>
                            </div>
                        </card-icon>
                    </div>
                    <div class="right display-flex flex1 margin-left30px flex-direction-column">
                        <card-icon
                            :data="cardInfo.doorLogs"
                            class="flex1"
                            width="auto"
                        ></card-icon>
                        <card-icon
                            :data="cardInfo.todayDoorLogs"
                            class="margin-top22vh flex1"
                            width="auto"
                        ></card-icon>
                        <card-icon
                            :data="cardInfo.callLogs"
                            width="auto"
                            class="margin-top22vh flex1"
                        ></card-icon>
                        <card-icon
                            :data="cardInfo.todayCallLogs"
                            class="margin-top22vh flex1"
                            width="auto"
                        ></card-icon>
                        <card-icon
                            :data="cardInfo.liveView"
                            class="margin-top22vh flex1"
                            width="auto"
                        ></card-icon>
                    </div>
                </div>
                <div class="second-row display-flex margin-top22vh height302vh">
                    <div class="left flex1">
                        <card-icon type="customize" height="100%" width="auto" title="Family / Office users trend">
                            <template v-slot:header>
                                <time-tab :type="timeType.users" @click="setTimeType($event, 'users')"></time-tab>
                            </template>
                            <div id="active-container" style="width: 100%"></div>
                        </card-icon>
                    </div>
                    <div class="middle flex1 margin-left30px">
                        <card-icon type="customize" height="100%" width="auto" title="Subscribers trend">
                            <template v-slot:header>
                                <time-tab :type="timeType.subscribers" @click="setTimeType($event, 'subscribers')"></time-tab>
                            </template>
                            <div id="family-container" style="width: 100%"></div>
                        </card-icon>
                    </div>
                    <div class="right flex1 margin-left30px">
                        <card-icon type="customize" height="100%" width="auto" title="Devices trend">
                            <template v-slot:header>
                                <time-tab :type="timeType.devices" @click="setTimeType($event, 'devices')"></time-tab>
                            </template>
                            <div id="device-container" style="width: 100%"></div>
                        </card-icon>
                    </div>
                </div>
                <div class="third-row display-flex margin-top22vh height302vh">
                    <div class="flex1">
                        <card-icon type="customize" height="100%" width="auto" title="Call logs trend">
                            <template v-slot:header>
                                <time-tab :type="timeType.callLogs" @click="setTimeType($event, 'callLogs')"></time-tab>
                            </template>
                            <div id="call-container" style="width: 100%"></div>
                        </card-icon>
                    </div>
                    <div class="flex1 margin-left30px">
                        <card-icon type="customize" height="100%" width="auto" title="Door logs trend">
                            <template v-slot:header>
                                <time-tab :type="timeType.doorLogs" @click="setTimeType($event, 'doorLogs')"></time-tab>
                            </template>
                            <div id="door-container" style="width: 100%"></div>
                        </card-icon>
                    </div>
                    <div
                        class="flex1 margin-left30px"
                    >
                        <card-icon
                            type="customize"
                            width="auto"
                            height="100%"
                            title="Proportion of each door opening method"
                        >
                            <div id="pie-container" style="width: 100%"></div>
                        </card-icon>
                    </div>
                </div>
            </div>
        </div>
        <share-popup
            v-if="showShareDialog"
            @close="showShareDialog=false"
            :base-url="shareUrl"
            :other-config="{
                Uri: 'getAkcsRegionData',
                Region: type,
                Dis: ''
            }"
        ></share-popup>
    </div>
</template>
<script lang="ts">
import {
    defineComponent, onMounted, ref,
    Ref, PropType
} from 'vue';
import '@/assets/less/common.less';
import CardIcon from '@/components/common/card-icon/index.vue';
import CusHeader from '@/components/common/header/index.vue';
import NumShow from '@/components/common/num-show/index.vue';
import HttpRequest from '@/util/axios.config';
import SelectList, { OptionsType } from '@/components/common/select-list';
import SharePopup from '@/components/common/share-popup/index.vue';
import TimeTab from '@/components/common/time-tab/index.vue';
import { server } from '@/data/common';
import { useRouter } from 'vue-router';
import { formatNumber } from '@/methods/base';
import {
    getData, controlTimeType
} from './util';

export default defineComponent({
    components: {
        CardIcon,
        CusHeader,
        // NumShow,
        SelectList,
        SharePopup,
        TimeTab
    },
    props: {
        type: {
            type: String as PropType<'CHN' | 'ASIA' | 'EUR' | 'USA'>,
            required: true
        },
        token: {
            type: String,
            default: ''
        }
    },
    setup(props) {
        const globalRef = ref<HTMLElement | null>(null);
        const headerRef = ref<HTMLElement | null>(null);
        const mapRef = ref<HTMLElement | null>(null);

        // 手动计算地图的高度
        onMounted(() => {
            if (globalRef.value !== null && headerRef.value !== null && mapRef.value !== null) {
                mapRef.value.style.height = `${globalRef.value.clientHeight - headerRef.value.clientHeight - 10}px`;
            }
        });

        const {
            disList,
            cardInfo,
            totalUserNum
        } = getData(props.type, props.token);

        // 获取头部相关信息
        const pageName = ref('');
        const serverList: Ref<Array<OptionsType>> = ref([]);
        const getHeaderData = () => {
            HttpRequest.get('getConfig', props.token === '' ? {} : {
                Token: props.token
            }, (res: {
                data: {
                    admin: {
                        pageName: string;
                    },
                    serverList: Array<{
                        name: string;
                        code: string;
                    }>,
                    token: {
                        Title: string;
                    }
                }
            }) => {
                if (props.token !== '') {
                    pageName.value = res.data.token.Title === '' ? server[props.type] : res.data.token.Title;
                } else {
                    pageName.value = res.data.admin.pageName ? res.data.admin.pageName : server[props.type];
                }
                serverList.value = [{
                    label: 'Global',
                    value: 'global'
                }];
                if (props.token === '') {
                    res.data.serverList.forEach((item) => {
                        serverList.value.push({
                            label: item.name,
                            value: item.code
                        });
                    });
                }
            });
        };
        getHeaderData();

        // 跳转服务器页面
        const router = useRouter();
        const goToServerPage = (code: 'CHN' | 'ASIA' | 'EUR' | 'USA' | 'global' | 'JPN' | 'INDIA' | 'RU' | 'INC') => {
            if (code === 'global') {
                router.push('/global');
            } else {
                router.replace(`/server?type=${code}`);
                getData(code, props.token);
                getHeaderData();
            }
        };
        // 跳转代理商页面
        const goToDisPage = (dis: string) => {
            router.push(`/dis?type=${dis}&server=${props.type}`);
        };

        const showShareDialog = ref(false);
        const shareUrl = `http://120.78.142.208:1717/manage-new/#/server?type=${props.type}&token=`;

        const {
            timeType,
            setTimeType
        } = controlTimeType();

        return {
            disList,
            cardInfo,
            globalRef,
            headerRef,
            totalUserNum,
            mapRef,
            pageName,
            serverList,
            goToServerPage,
            goToDisPage,
            showShareDialog,
            shareUrl,
            timeType,
            setTimeType,
            formatNumber
        };
    }
});
</script>
<style lang="less">
@import url("../../assets/less/common.less");
.dashboard {
    // background: @BackgroundColor;
    background-image: url('../../assets/image/background.png');
    background-size: 100% 100%;
    .first-row .middle {
        .card-content {
            padding-left: 0;
        }
        .card-title {
            background-image: url('../../assets/image/global-title.png');
            background-repeat: round;
            height: 60vh * @base;
            text-align: center;
            line-height: 50vh * @base;
            font-size: 20px;
            position: relative;
            top: -1px;
        }
    }
    .total-num {
        display: flex;
        align-items: center;
        color: white;
        padding: 8vh * @base 30vh * @base;
        background: #052FFC;
        margin-left: 10vh * @base;
        letter-spacing: 5vh * @base;
        font-size: 36vh * @base;
    }
}
</style>
