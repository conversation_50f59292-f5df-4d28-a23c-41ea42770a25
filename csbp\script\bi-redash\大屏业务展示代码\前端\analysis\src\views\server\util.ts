import { ref, nextTick } from 'vue';
import HttpRequest from '@/util/axios.config';
import { OptionsType } from '@/components/common/select-list';
import { formatNumber } from '@/methods/base';

declare const Highcharts: any;

const server = {
    CHN: ['CHN'],
    JPN: ['JPN'],
    INDIA: ['IND'],
    RU: ['RUS'],
    INC: ['ARE', 'BHR', 'CYP', 'IRN', 'IRQ', 'ISR', 'JOR', 'KWT',
        'LBN', 'OMN', 'PSE', 'QAT', 'SAU', 'SYR', 'TUR', 'AZE', 'GEO'],
    ASIA: [
        'PHL', 'THA', 'IND', 'KOR',
        'BGD', 'SPI', 'MMR', 'IDN', 'SGP', 'RUS', 'MYS', 'AZE',
        'ARM', 'VNM', 'TJK', 'UZB', 'TLS', 'KHM', 'BTN', 'GEO', 'KAZ',
        'JOR', 'TKM', 'MNG', 'LAO',
        'PAK', 'BRN', 'AFG', 'PRK', 'NPL',
        'LKA', 'KGZ'
    ],
    EUR: [
        'FIN', 'SWE', 'NOR', 'ISL', 'DNK', 'EST', 'LVA', 'LTU',
        'BLR', 'UKR', 'MDA', 'POL', 'CZE', 'SVK', 'HUN',
        'DEU', 'AUT', 'CHE', 'LIE', 'GBR', 'IRL', 'NLD', 'BEL',
        'LUX', 'FRA', 'MCO', 'ROU', 'BGR', 'SRB', 'MKD', 'ALB',
        'GRC', 'SVN', 'HRV', 'MNE', 'MLT', 'BIH', 'ITA', 'VAT',
        'SMR', 'ESP', 'PRT', 'AND', 'FRO',
        'SAU', 'IRN', 'IRQ', 'KWT', 'ARE', 'OMN', 'QAT', 'BHR',
        'TUR', 'ISR', 'SYR', 'LBN', 'JOR', 'CYP', 'YEM', 'EGY',
        'LBY', 'TUN', 'DZA', 'MAR'
    ],
    USA: [
        'CAN', 'USA', 'MEX', 'GRL', 'GTM', 'BLZ', 'SLV', 'HND', 'NIC',
        'CRI', 'PAN', 'BHS', 'CUB', 'JAM', 'HTI', 'DOM', 'ATG', 'KNA',
        'DMA', 'LCA', 'VCT', 'GRD', 'BRB', 'TTO', 'PRI', 'VIR', 'COL',
        'VEN', 'GUY', 'SUR', 'ECU', 'PER', 'BOL', 'BRA', 'CHL', 'ARG',
        'URY', 'PRY', 'UMI'
    ]
};

const getGlobalMap = (globalData: Array<any>, type: 'CHN' | 'ASIA' | 'USA' | 'EUR' | 'JPN' | 'INDIA' | 'RU' | 'INC'): void => {
    const serverData: Array<{
        Code: string;
        Num: string;
    }> = [];
    globalData.forEach((item) => {
        if (server[type].includes(item.Code)) {
            serverData.push(item);
        }
    });
    const columns = [
        ['Country code', 'AFG', 'ALB', 'DZA', 'ASM', 'AND', 'AGO', 'ATG', 'ARG', 'ARM', 'AUS', 'AUT', 'AZE', 'BHS',
            'BHR', 'BGD', 'BRB', 'BLR', 'BEL', 'BLZ', 'BEN', 'BTN', 'BOL', 'BIH', 'BWA', 'BRA', 'BRN', 'BGR', 'BFA', 'BDI',
            'KHM', 'CMR', 'CAN', 'CPV', 'CAF', 'TCD', 'CHL', 'CHN', 'COL', 'COM', 'COG', 'COD', 'CRI', 'CIV', 'HRV', 'CUB',
            'CYP', 'CZE', 'DNK', 'DJI', 'DMA', 'DOM', 'ECU', 'EGY', 'SLV', 'GNQ', 'ERI', 'EST', 'ETH', 'FRO', 'FJI', 'FIN',
            'FRA', 'GAB', 'GMB', 'GEO', 'DEU', 'GHA', 'GRC', 'GRL', 'GRD', 'GUM', 'GTM', 'GIN', 'GNB', 'GUY', 'HTI', 'HND',
            'HUN', 'ISL', 'IND', 'IDN', 'IRN', 'IRQ', 'IRL', 'ISR', 'ITA', 'JAM', 'JPN', 'JOR', 'KAZ', 'KEN', 'KIR',
            'PRK', 'KOR', '-99', 'KWT', 'KGZ', 'LAO', 'LVA', 'LBN', 'LSO', 'LBR', 'LBY', 'LIE', 'LTU', 'LUX', 'MRT', 'MKD',
            'MDG', 'MWI', 'MYS', 'MDV', 'MLI', 'MLT', 'MHL', 'MRT', 'MUS', 'MEX', 'FSM', 'MDA', 'MCO', 'MNG', 'MNE', 'MAR', 'MOZ',
            'MMR', 'NAM', 'NPL', 'NLD', '-99', 'NZL', 'NIC', 'NER', 'NGA', 'MNP', 'NOR', 'OMN', 'PAK', 'PLW', 'PAN', 'PNG', 'PRY',
            'PER', 'PHL', 'POL', 'PRT', 'PRI', 'QAT', 'ROU', 'RUS', 'RWA', 'ASM', 'SMR', 'STP', 'SAU', 'SEN', 'SRB', 'SYC', 'SLE',
            'SGP', 'SVK', 'SVN', 'SLB', 'SOM', 'ZAF', 'SSD', 'ESP', 'LKA', 'KNA', 'LCA', 'VCT', 'SDN', 'SUR', 'SWZ', 'SWE',
            'CHE', 'SYR', 'TJK', 'TZA', 'THA', 'TLS', 'TGO', 'TON', 'TTO', 'TUN', 'TUR', 'TKM', 'TUV', 'UGA', 'UKR', 'ARE',
            'GBR', 'USA', 'URY', 'UZB', 'VUT', 'VEN', 'VNM', 'VIR', 'ESH', 'YEM', 'ZMB', 'ZWE'],
        ['Country name', 'Afghanistan', 'Albania', 'Algeria', 'American Samoa', 'Andorra', 'Angola', 'Antigua and Barbuda',
            'Argentina', 'Armenia', 'Australia', 'Austria', 'Azerbaijan', 'Bahamas', 'Bahrain', 'Bangladesh',
            'Barbados', 'Belarus', 'Belgium', 'Belize', 'Benin', 'Bhutan', 'Bolivia', 'Bosnia and Herzegovina',
            'Botswana', 'Brazil', 'Brunei', 'Bulgaria', 'Burkina Faso', 'Burundi', 'Cambodia', 'Cameroon', 'Canada',
            'Cape Verde', 'Central African Republic', 'Chad', 'Chile', 'China', 'Colombia', 'Comoros',
            'Republic of Congo', 'Democratic Republic of the Congo', 'Costa Rica', 'Ivory Coast', 'Croatia', 'Cuba', 'Cyprus',
            'Czech Republic', 'Denmark', 'Djibouti', 'Dominica', 'Dominican Republic', 'Ecuador', 'Egypt',
            'El Salvador', 'Equatorial Guinea', 'Eritrea', 'Estonia', 'Ethiopia', 'Faroe Islands', 'Fiji', 'Finland',
            'France', 'Gabon', 'Gambia', 'Georgia', 'Germany', 'Ghana', 'Greece', 'Greenland',
            'Grenada', 'Guam', 'Guatemala', 'Guinea', 'Guinea Bissau', 'Guyana', 'Haiti', 'Honduras',
            'Hungary', 'Iceland', 'India', 'Indonesia', 'Iran', 'Iraq', 'Ireland', 'Israel', 'Italy',
            'Jamaica', 'Japan', 'Jordan', 'Kazakhstan', 'Kenya', 'Kiribati', 'North Korea', 'South Korea', 'Kosovo', 'Kuwait',
            'Kyrgyzstan', 'Laos', 'Latvia', 'Lebanon', 'Lesotho', 'Liberia', 'Libya', 'Liechtenstein', 'Lithuania', 'Luxembourg',
            'Macedonia', 'Madagascar', 'Malawi', 'Malaysia', 'Maldives', 'Mali', 'Malta', 'Marshall Islands',
            'Mauritania', 'Mauritius', 'Mexico', 'Federated States of Micronesia', 'Moldova', 'Monaco', 'Mongolia', 'Montenegro',
            'Morocco', 'Mozambique', 'Myanmar', 'Namibia', 'Nepal', 'Netherlands', 'Northern Cyprus', 'New Zealand', 'Nicaragua', 'Niger',
            'Nigeria', 'Northern Mariana Islands', 'Norway', 'Oman', 'Pakistan', 'Palau', 'Panama', 'Papua New Guinea', 'Paraguay', 'Peru',
            'Philippines', 'Poland', 'Portugal', 'Puerto Rico', 'Qatar', 'Romania', 'Russia', 'Rwanda', 'Samoa', 'San Marino',
            'Sao Tome and Principe', 'Saudi Arabia', 'Senegal', 'Serbia', 'Seychelles', 'Sierra Leone', 'Singapore', 'Slovakia',
            'Slovenia', 'Solomon Islands', 'Somalia', 'South Africa', 'South Sudan', 'Spain', 'Sri Lanka', 'Saint Kitts and Nevis',
            'Saint Lucia', 'Saint Vincent and the Grenadines', 'Sudan', 'Suriname', 'Swaziland', 'Sweden',
            'Switzerland', 'Syria', 'Tajikistan', 'Tanzania', 'Thailand', 'East Timor', 'Togo', 'Tonga', 'Trinidad and Tobago',
            'Tunisia', 'Turkey', 'Turkmenistan', 'Tuvalu', 'Uganda', 'Ukraine', 'United Arab Emirates',
            'United Kingdom', 'United States of America', 'Uruguay', 'Uzbekistan', 'Vanuatu', 'Venezuela', 'Vietnam', 'Virgin Islands',
            'Western Sahara', 'Yemen', 'Zambia', 'Zimbabwe'],
        ['Global Business', null]
    ];
    columns[0].forEach((item, index) => {
        if (index > 0) {
            columns[2][index] = null;
        }
    });
    let maxNum = 0;
    let minNum = Number(serverData[0].Num);
    serverData.forEach((item) => {
        if (columns[0].indexOf(item.Code.toUpperCase()) !== -1) {
            columns[2][columns[0].indexOf(item.Code.toUpperCase())] = item.Num;
        }
        if (Number(item.Num) > maxNum) {
            maxNum = Number(item.Num);
        }
        if (Number(item.Num) < minNum) {
            minNum = Number(item.Num);
        }
    });
    const averageNum = (maxNum - minNum) / 4;
    const data: Array<Array<any>> = [[], [], [], [], [], []];
    columns[2].forEach((item, index) => {
        if (index > 0 && item) {
            switch (true) {
            case Number(item) <= minNum:
                data[0].push({
                    code: columns[0][index],
                    value: Number(item)
                });
                break;
            case Number(item) <= (averageNum + minNum):
                data[1].push({
                    code: columns[0][index],
                    value: Number(item)
                });
                break;
            case Number(item) <= (2 * averageNum + minNum):
                data[2].push({
                    code: columns[0][index],
                    value: Number(item)
                });
                break;
            case Number(item) <= (3 * averageNum + minNum):
                data[3].push({
                    code: columns[0][index],
                    value: Number(item)
                });
                break;
            case Number(item) <= (4 * averageNum + minNum):
                data[4].push({
                    code: columns[0][index],
                    value: Number(item)
                });
                break;
            default:
                break;
            }
        } else {
            data[5].push(columns[0][index]);
        }
    });
    console.log('----', data);
    Highcharts.mapChart('global-container', {
        chart: {
            borderWidth: 0,
            backgroundColor: 'transparent',
            type: 'map',
            marginRight: 100,
            marginBottom: (120 * document.body.clientHeight) / 1080
        },
        colors: ['transparent', '#803FFF', '#23D26A',
            '#16D3A6', '#04C8DC', '#006CFF'
        ],
        title: {
            text: ''
        },
        credits: {
            enabled: false
        },
        exporting: {
            enabled: false
        },
        legend: {
            enabled: false,
            align: 'right',
            verticalAlign: 'bottom',
            floating: true,
            layout: 'vertical',
            valueDecimals: 0,
            backgroundColor: 'rgba(255, 255, 255, 0.12)',
            borderColor: 'rgba(255, 255, 255, 0.15)',
            borderWidth: 1,
            symbolRadius: 0,
            symbolHeight: 8,
            shadow: 'inset 0px 1px 15px 0px #1F5E89',
            itemStyle: {
                color: '#fff',
                x: 50
            },
            x: -12,
            reversed: true,
            itemMarginBottom: 5,
            width: 100,
            itemHoverStyle: {
                color: '#02A1FC'
            }
        },

        plotOptions: {
            map: {
                allAreas: false,
                joinBy: ['iso-a3', 'code'],
                mapData: Highcharts.maps['custom/world'],
                borderColor: '#006cff',
                tooltip: {
                    headerFormat: '',
                    pointFormat: '{point.name}'
                }
            }
        },
        series: [{
            name: '',
            data: Highcharts.map(data[5], (code: string) => ({
                code
            })),
            showInLegend: false
        }, {
            name: minNum.toFixed(2),
            data: data[0]
        }, {
            name: (averageNum + minNum).toFixed(2),
            data: data[1],
            showInLegend: type !== 'CHN'
        }, {
            name: (averageNum * 2 + minNum).toFixed(2),
            data: data[2],
            showInLegend: type !== 'CHN'
        }, {
            name: (averageNum * 3 + minNum).toFixed(2),
            data: data[3],
            showInLegend: type !== 'CHN'
        }, {
            name: (averageNum * 4 + minNum).toFixed(2),
            data: data[4],
            showInLegend: type !== 'CHN'
        }]
    });
};

const getPieChart = (data: Array<any>): void => {
    Highcharts.chart('pie-container', {
        chart: {
            type: 'pie',
            backgroundColor: 'transparent'
        },
        colors: ['#006CFF', '#0094FF', '#04C8DC',
            '#00F6BB', '#00CD0B', '#803FFF', '#522BFF'
        ],
        legend: {
            align: 'right',
            verticalAlign: 'top',
            layout: 'vertical',
            backgroundColor: 'transparent',
            borderColor: 'transparent',
            borderWidth: 0,
            symbolRadius: 0,
            symbolHeight: 8,
            shadow: 'inset 0px 1px 15px 0px #1F5E89',
            itemStyle: {
                color: '#fff',
                x: 50
            },
            itemMarginBottom: 10,
            y: 10,
            itemHoverStyle: {
                color: '#02A1FC'
            }
        },
        title: {
            text: '',
            style: {
                color: 'white'
            }
        },
        credits: {
            enabled: false
        },
        plotOptions: {
            pie: {
                dataLabels: {
                    enabled: true,
                    style: {
                        fontWeight: 'bold',
                        color: 'white',
                        textShadow: '0px 1px 2px black'
                    },
                    format: '{point.percentage:.1f} %'
                },
                showInLegend: true,
                borderColor: 'transparent'
            }
        },
        series: [{
            type: 'pie',
            // name: '',
            innerSize: '50%',
            colorByPoint: true,
            data
        }],
        exporting: {
            enabled: false
        }
    });
};

const getColumnCountChart = (
    container: string, data: Array<any>,
    colors: Array<string>
):void => {
    Highcharts.chart(container, {
        chart: {
            type: 'column',
            backgroundColor: 'transparent'
        },
        title: {
            text: ''
        },
        colors,
        xAxis: {
            type: 'category',
            labels: {
                step: Math.floor((data[0].data.length - 3) / 2),
                style: {
                    color: '#ffffff'
                }
            },
            lineColor: '#02A3FF'
        },
        yAxis: {
            min: 0,
            title: {
                text: ''
            },
            gridLineColor: 'rgba(255, 255, 255, 0.1)',
            lineColor: '#02A3FF',
            lineWidth: 1
        },
        legend: {
            align: 'right',
            verticalAlign: 'top',
            symbolRadius: 0,
            itemStyle: {
                color: '#fff'
            },
            itemHoverStyle: {
                color: '#02A1FC'
            }
        },
        plotOptions: {
            series: {
                borderColor: 'transparent'
            }
        },
        credits: {
            enabled: false
        },
        exporting: {
            enabled: false
        },
        series: data
    });
};

const getAreaChart = (
    container: string, data: Array<any>,
    colors: Array<string>,
    style: 'area' | null = null
) => {
    let maxY = 0;
    data.forEach((item, index) => {
        let tempColors: string[] | string = colors[index].split(',');
        tempColors.splice(tempColors.length - 1, 1, '0)');
        tempColors = tempColors.join(',');
        /* eslint-disable no-param-reassign */
        item.fillColor = {
            color: 'white',
            linearGradient: {
                x1: 0, y1: 0, x2: 0, y2: 1
            },
            stops: [
                [0, style ? colors[index] : 'rgba(0,0,0,0)'],
                [1, style ? tempColors : 'rgba(0, 0, 0, 0)']
            ]
        };
        item.fillOpacity = 0.1;
        item.marker = {
            fillColor: '#D8D8D8',
            lineColor: colors[index],
            lineWidth: 2,
            radius: 4,
            symbol: 'circle'
        };
        item.data.forEach((innerItem: Array<any>) => {
            if (Number(innerItem[1]) > maxY) {
                // eslint-disable-next-line prefer-destructuring
                maxY = innerItem[1];
            }
        });
    });
    console.log('max: ', maxY);
    Highcharts.chart(container, {
        chart: {
            type: 'area',
            backgroundColor: 'transparent'
        },
        title: {
            text: ''
        },
        colors,
        xAxis: {
            type: 'category',
            labels: {
                step: Math.floor((data[0].data.length - 3) / 2),
                style: {
                    color: '#ffffff'
                }
            },
            lineColor: '#02A3FF'
        },
        yAxis: {
            min: 0,
            max: maxY,
            tickAmount: 4,
            title: {
                text: ''
            },
            gridLineColor: 'rgba(255, 255, 255, 0.1)',
            lineColor: '#02A3FF',
            lineWidth: 1
        },
        legend: {
            align: 'right',
            verticalAlign: 'top',
            symbolRadius: 0,
            itemStyle: {
                color: '#fff'
            },
            itemHoverStyle: {
                color: '#02A1FC'
            }
        },
        credits: {
            enabled: false
        },
        exporting: {
            enabled: false
        },
        series: data
    });
};

type KeyIsString = 'ActiveFamilyNum' | 'RegisteredDeviceNum' | 'TodayCallNum'
    | 'TodayOpenDoorNum' | 'TotalCallNum' | 'TotalLiveView' | 'TotalOpenDoorNum' | 'OfficeNum' | 'MonthFeeUserNum';
type KeyIsArrayObject = 'FamilyAndOFficeNumMonth' | 'FamilyAndOFficeNumYear' | 'DeviceMonth' | 'DeviceYear' | 'GlobalBusiness'
    | 'GlobalMonthCallNumMonth' | 'GlobalMonthCallNumYear' | 'LiveView' | 'OpenDoorSuccessType'
    | 'MonthFeeUserMonthNumMonth' | 'MonthFeeUserMonthNumYear' | 'GlobalOpenDoorNumMonth' | 'GlobalOpenDoorNumYear';
/* eslint-disable @typescript-eslint/ban-types */
type AnalysisData = {
    [key in KeyIsString]: string;
} & {
    [key in KeyIsArrayObject]: Array<any>;
} & {
    DeviceOnline: {
        online: string;
        rate: string;
    };
} & {
    disList: Array<string>
}

const cardInfo = ref({
    doorLogs: {
        title: 'Door Logs',
        icon: require('@/assets/image/all-open-door-count.png'),
        text: ''
    },
    todayDoorLogs: {
        title: 'Today - Door Logs',
        icon: require('@/assets/image/today-open-door-count.png'),
        text: ''
    },
    callLogs: {
        title: 'Call Logs',
        icon: require('@/assets/image/all-call-count.png'),
        text: ''
    },
    todayCallLogs: {
        title: 'Today -  Call Logs',
        icon: require('@/assets/image/today-call-count.png'),
        text: ''
    },
    officeUsers: {
        title: 'Office users',
        icon: require('@/assets/image/active-app-count.png'),
        text: ''
    },
    devices: {
        title: 'Devices on the cloud',
        icon: require('@/assets/image/registered-device-count.png'),
        text: ''
    },
    devicesOnline: {
        title: 'Devices online',
        icon: require('@/assets/image/online-device-count.png'),
        text: '',
        rightTitle: ''
    },
    liveView: {
        title: 'Live View',
        icon: require('@/assets/image/all-live-view-count.png'),
        text: ''
    },
    families: {
        title: 'Families',
        icon: require('@/assets/image/active-family-count.png'),
        text: '',
        textColor: '#00CD0B'
    },
    subscribers: {
        title: 'Subscribers',
        icon: require('@/assets/image/month-user-num.png'),
        text: '',
        textColor: '#13CEA1'
    }
});
const totalUserNum = ref(0);

const timeData = ref<{
    [key in string] : {
        [type in string]: any[]
    }
}>({
    monthly: {
        users: [],
        subscribers: [],
        devices: [],
        callLogs: [],
        doorLogs: []
    },
    yearly: {
        users: [],
        subscribers: [],
        devices: [],
        callLogs: [],
        doorLogs: []
    }
});

const disList = ref<OptionsType[]>([]);
const getData = (type: 'CHN' | 'ASIA' | 'USA' | 'EUR' | 'JPN' | 'INDIA' | 'RU' | 'INC', token: string) => {
    HttpRequest.get('getAkcsRegionData', token === '' ? {
        Region: type
    } : {
        Region: type,
        Token: token
    }, (res: {
        data: AnalysisData
    }) => {
        const { data } = res;

        cardInfo.value.todayDoorLogs.text = formatNumber(data.TodayOpenDoorNum || '0');
        cardInfo.value.doorLogs.text = formatNumber(data.TotalOpenDoorNum || '0');
        cardInfo.value.todayCallLogs.text = formatNumber(data.TodayCallNum || '0');
        cardInfo.value.callLogs.text = formatNumber(data.TotalCallNum || '0');
        cardInfo.value.devicesOnline.text = formatNumber(data.DeviceOnline.online || '0');
        cardInfo.value.devicesOnline.rightTitle = `${data.DeviceOnline.rate ? '' : `(${data.DeviceOnline.rate})`}`;
        cardInfo.value.devices.text = formatNumber(data.RegisteredDeviceNum || '0');
        cardInfo.value.liveView.text = formatNumber(data.TotalLiveView || '0');
        cardInfo.value.families.text = formatNumber(data.ActiveFamilyNum || '0');
        cardInfo.value.officeUsers.text = formatNumber(data.OfficeNum || '0');
        cardInfo.value.subscribers.text = formatNumber(data.MonthFeeUserNum || '0');

        // 保存当前服务器下的代理商
        disList.value = [];
        const tempDisList: OptionsType[] = [];

        if (token === '') {
            res.data.disList.forEach((item) => {
                tempDisList.push({
                    label: item,
                    value: item
                });
            });
            disList.value = tempDisList;
        }

        const liveViewNum = <any>[];
        const doorType = <any>[];

        // callLogs 年月数据保存
        const callNum = <any>[{
            name: 'Call logs',
            data: []
        }];
        data.GlobalMonthCallNumMonth.forEach((item) => {
            callNum[0].data.push([item.DateTime, Number(item.CallNum)]);
        });
        timeData.value.monthly.callLogs = callNum;
        const callNumYear = <any>[{
            name: 'Call logs',
            data: []
        }];
        data.GlobalMonthCallNumYear.forEach((item) => {
            callNumYear[0].data.push([item.DateTime, Number(item.CallNum)]);
        });
        timeData.value.yearly.callLogs = callNumYear;

        // doorLogs 年月数据保存
        const doorNum = <any>[{
            name: 'Door logs',
            data: []
        }];
        data.GlobalOpenDoorNumMonth.forEach((item) => {
            doorNum[0].data.push([item.DateTime, Number(item.DoorNum)]);
        });
        timeData.value.monthly.doorLogs = doorNum;
        const doorNumYear = <any>[{
            name: 'Door logs',
            data: []
        }];
        data.GlobalOpenDoorNumYear.forEach((item) => {
            doorNumYear[0].data.push([item.DateTime, Number(item.DoorNum)]);
        });
        timeData.value.yearly.doorLogs = doorNumYear;

        data.LiveView.forEach((item) => {
            liveViewNum.push([item.DateTime, Number(item.LiveViewNum)]);
        });
        data.OpenDoorSuccessType.forEach((item) => {
            doorType.push({
                name: item.OpenDoorType,
                y: Number(item.OpenDoorNum)
            });
        });

        // device 年月数据保存
        const deviceNum = <any>[{
            name: 'Online devices',
            data: []
        }, {
            name: 'Registered devices',
            data: []
        }];
        data.DeviceMonth.forEach((item) => {
            deviceNum[0].data.push([item.DateTime, Number(item.online_num)]);
            deviceNum[1].data.push([item.DateTime, Number(item.register_num)]);
        });
        timeData.value.monthly.devices = deviceNum;
        const deviceNumYear = <any>[{
            name: 'Online devices',
            data: []
        }, {
            name: 'Registered devices',
            data: []
        }];
        data.DeviceYear.forEach((item) => {
            deviceNumYear[0].data.push([item.DateTime, Number(item.online_num)]);
            deviceNumYear[1].data.push([item.DateTime, Number(item.register_num)]);
        });
        timeData.value.yearly.devices = deviceNumYear;

        // users 年月数据保存
        const activeNum = <any>[{
            name: 'Family users',
            data: []
        }, {
            name: 'Office users',
            data: []
        }];
        data.FamilyAndOFficeNumMonth.forEach((item) => {
            activeNum[0].data.push([item.DateTime, Number(item.FamilyNum)]);
            activeNum[1].data.push([item.DateTime, Number(item.OfficeNum)]);
        });
        timeData.value.monthly.users = activeNum;
        const activeNumYear = <any>[{
            name: 'Family users',
            data: []
        }, {
            name: 'Office users',
            data: []
        }];
        data.FamilyAndOFficeNumYear.forEach((item) => {
            activeNumYear[0].data.push([item.DateTime, Number(item.FamilyNum)]);
            activeNumYear[1].data.push([item.DateTime, Number(item.OfficeNum)]);
        });
        timeData.value.yearly.users = activeNumYear;

        // subscribers 年月数据保存
        const monthFeeUserMonthNum = <any>[{
            name: 'Subscribers',
            data: []
        }];
        data.MonthFeeUserMonthNumMonth.forEach((item) => {
            monthFeeUserMonthNum[0].data.push([item.DateTime, Number(item.FeeFamilyNum)]);
        });
        timeData.value.monthly.subscribers = monthFeeUserMonthNum;
        const monthFeeUserMonthNumYear = <any>[{
            name: 'Subscribers',
            data: []
        }];
        data.MonthFeeUserMonthNumYear.forEach((item) => {
            monthFeeUserMonthNumYear[0].data.push([item.DateTime, Number(item.FeeFamilyNum)]);
        });
        timeData.value.yearly.subscribers = monthFeeUserMonthNumYear;

        // 计算所有用户数量
        totalUserNum.value = Number(data.ActiveFamilyNum) + Number(data.OfficeNum);

        nextTick(() => {
            getColumnCountChart('call-container', timeData.value.monthly.callLogs, ['#16D3A6']);
            getColumnCountChart('door-container', timeData.value.monthly.doorLogs, ['#006CFF']);
            getAreaChart('device-container', timeData.value.monthly.devices, ['#23D26A', '#006CFF']);
            getAreaChart('family-container', timeData.value.monthly.subscribers, ['rgba(22, 211, 166, 1)'], 'area');
            getAreaChart('active-container', timeData.value.monthly.users, ['rgba(128, 63, 255, 1)', 'rgba(0, 108, 255, 1)']);
            getPieChart(doorType);
            getGlobalMap(data.GlobalBusiness, type);
        });
    });

    return {
        disList,
        cardInfo,
        totalUserNum
    };
};

const controlTimeType = () => {
    const timeType = ref<{
        [key in string]: string;
    }>({
        users: 'monthly',
        subscribers: 'monthly',
        devices: 'monthly',
        callLogs: 'monthly',
        doorLogs: 'monthly'
    });
    const setTimeType = (time: string, type: string) => {
        timeType.value[type] = time;
        if (type === 'users') {
            getAreaChart('active-container', timeData.value[time].users, ['rgba(128, 63, 255, 1)', 'rgba(0, 108, 255, 1)']);
        } else if (type === 'subscribers') {
            getAreaChart('family-container', timeData.value[time].subscribers, ['rgba(22, 211, 166, 1)'], 'area');
        } else if (type === 'devices') {
            getAreaChart('device-container', timeData.value[time].devices, ['#23D26A', '#006CFF']);
        } else if (type === 'callLogs') {
            getColumnCountChart('call-container', timeData.value[time].callLogs, ['#16D3A6']);
        } else if (type === 'doorLogs') {
            getColumnCountChart('door-container', timeData.value[time].doorLogs, ['#006CFF']);
        }
    };
    return {
        timeType,
        setTimeType
    };
};

export default null;
export {
    getGlobalMap,
    getPieChart,
    getColumnCountChart,
    getData,
    controlTimeType
};
