<template>
    <dialog-shell title="Create User" footerType="customize">
        <el-form ref="formRef" label-position="top" :model="formData" :rules="rules">
            <el-form-item label="Name" prop="Username">
                <el-input v-model="formData.Username"></el-input>
            </el-form-item>
            <el-form-item label="Email" prop="Email">
                <el-input v-model="formData.Email" @blur="checkExistEmail"></el-input>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="$emit('close')">Cancel</el-button>
                <el-button type="primary" @click="clickOperate">Ok</el-button>
            </span>
        </template>
    </dialog-shell>
</template>

<script lang="ts">
import {
    defineComponent,
    ref,
    reactive,
    watch
} from 'vue';
import DialogShell from '@/components/common/dialog-shell/index.vue';
import { user } from '@/methods/rule';
import HttpRequest from '@/util/axios.config';
import { ElMessage } from 'element-plus/lib/components';

const group = ['Admin', 'User'];
export default defineComponent({
    emits: ['close'],
    components: {
        DialogShell
    },
    setup(props, { emit }) {
        const formRef = ref();
        const formData = reactive({
            Username: '',
            Email: ''
        });

        const { identity } = JSON.parse(localStorage.getItem('userInfo')!);
        const isBlur = ref(false);
        const isClick = ref(false);
        const confirmCreate = () => {
            formRef.value.validate((valid: boolean) => {
                if (valid) {
                    HttpRequest.post('addUser', {
                        ...formData,
                        Group: group[Number(identity) - 1]
                    }, () => {
                        ElMessage({
                            message: 'User Created!',
                            type: 'success'
                        });
                        emit('close');
                    });
                }
            });
            isClick.value = false;
        };

        const isErrorEmail = ref(false);
        const errorMsg = ref('');
        const rules = {
            Email: [{
                required: true,
                message: 'Please enter the email address.',
                trigger: 'blur'
            }, {
                validator: user.checkEmail,
                trigger: 'blur'
            }, {
                validator: user.checkExistEmail(isErrorEmail, errorMsg),
                trigger: 'blur'
            }],
            Username: [{
                required: true,
                message: 'Please enter the name.',
                trigger: 'blur'
            }]
        };
        const checkExistEmail = () => {
            isErrorEmail.value = false;
            isBlur.value = true;
            formRef.value.validateField('Email', (valid: boolean) => {
                if (valid) {
                    HttpRequest.post('emailCheck', [{
                        Email: formData.Email
                    }, false], () => {
                        isBlur.value = false;
                    }, [(res: {
                        msg: string;
                    }) => {
                        isErrorEmail.value = true;
                        errorMsg.value = res.msg;
                        formRef.value.validateField('Email');
                        isBlur.value = false;
                    }, false]);
                }
            });
        };

        watch(() => isBlur.value, () => {
            if (!isBlur.value && isClick.value) {
                confirmCreate();
            }
        });
        const clickOperate = () => {
            isClick.value = true;
            if (!isBlur.value) {
                confirmCreate();
            }
        };

        return {
            formRef,
            formData,
            confirmCreate,
            rules,
            checkExistEmail,
            clickOperate
        };
    }
});
</script>
