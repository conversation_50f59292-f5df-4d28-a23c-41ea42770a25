<template>
    <div class="all-container">
        <div class="height30vh margin-bottom15vh">
            <cus-header :show-share="false" :show-manage="true" type="blue">
                <select-list
                    :list="serverList"
                    title="Server"
                    @click="goToServerPage"
                    width="135px"
                    type="blue"
                ></select-list>
            </cus-header>
        </div>

        <div style="height: 2px;background-color: var(--el-border-color-light);margin-bottom: 50px;"></div>

        <el-tabs v-model="activeNames" @tab-click="changeTab">
            <el-tab-pane label="Profiles" name="Profiles">
                <el-form label-position="top" :rules="rules" ref="formRef" :model="formData">
                    <el-form-item label="Name" prop="Name">
                        <el-input class="width400px" v-model="formData.Name"></el-input>
                    </el-form-item>
                    <el-form-item label="Email" prop="Email">
                        <el-input class="width400px" v-model="formData.Email" :disabled="true"></el-input>
                    </el-form-item>
                    <el-form-item label="Group">
                        <div class="height36vh identity-icon">
                            {{ userData.identity }}
                        </div>
                    </el-form-item>
                </el-form>
                <cus-button type="blue" width="400px" height="30px" @click="editProfile">Save</cus-button>
                <div class="margin-top30vh">
                    <div>Password</div>
                    <cus-button width="400px" height="30px" class="margin-top20vh" @click="isShowPasswordDialog=true">Change Password</cus-button>
                </div>
            </el-tab-pane>
            <el-tab-pane label="Users" name="Users">
                <div class="margin-top20vh display-flex search-header" v-if="userData.identity==='Admin'">
                    <cus-button type="blue" @click="isShowCreateUserDialog=true">New User</cus-button>
                    <el-input v-model="searchValue" class="width300px" placeholder="Search"></el-input>
                </div>
                <el-scrollbar class="margin-top20vh height600vh">
                    <el-table :data="tableData" style="width: 100%" @sort-change="sortChange">
                        <el-table-column label="Name" sortable="custom">
                            <template #default="scope">
                                <div style="display: flex; align-items: center">
                                    <span style="margin-left: 10px">{{ scope.row.Nickname }}</span>
                                </div>
                            </template>
                        </el-table-column>
                        <el-table-column label="Groups">
                            <template #default="scope">
                                <el-tag
                                    :type="scope.row.Group === '2' ? 'info': ''"
                                >
                                    {{ ['Admin', 'User'][Number(scope.row.Group) - 1] }}
                                </el-tag>
                            </template>
                        </el-table-column>
                        <el-table-column label="Operations" v-if="userData.identity==='Admin'">
                            <template #default="scope">
                                <el-icon
                                    @click="deleteUser(scope.$index, scope.row)"
                                    class="margin-left30px delete-icon cursor-pointer"
                                >
                                    <Delete />
                                </el-icon>
                            </template>
                        </el-table-column>
                    </el-table>
                </el-scrollbar>
            </el-tab-pane>
        </el-tabs>
        <password-dialog v-if="isShowPasswordDialog" @close="isShowPasswordDialog=false" @success="updatePw"></password-dialog>
        <create-user-dialog v-if="isShowCreateUserDialog" @close="refreshPage"></create-user-dialog>
    </div>
</template>

<script lang="ts">
import {
    computed,
    defineComponent,
    ref,
    watch
} from 'vue';
import CusButton from '@/components/common/customize-button/index.vue';
import passwordDialog from '@/components/common/password-change';
import { ElMessage, ElMessageBox } from 'element-plus';
import type { TabsPaneContext } from 'element-plus';
import { Delete } from '@element-plus/icons-vue';
import HttpRequest from '@/util/axios.config';
import { useRouter } from 'vue-router';
import CusHeader from '@/components/common/header/index.vue';
import SelectList, { OptionsType } from '@/components/common/select-list';
import CreateUserDialog from './create-user.vue';
import goBackLogin from '../../router/back-login';

type RuleMethod = (rule: any, value: string | number, callback: (error?: Error) => any) => any;
const checkEmail: RuleMethod = (rule, value, callback) => {
    if (value === '') return callback();
    const reg = /^[.a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
    if (!reg.test(value as string)) {
        return callback(new Error('Please enter a valid email address.'));
    }
    return callback();
};
interface UserType {
    ID: string;
    Email: string;
    Nickname: string;
    Group: string;
}
export default defineComponent({
    components: {
        CusButton,
        passwordDialog,
        CreateUserDialog,
        Delete,
        CusHeader,
        SelectList
    },
    props: {
        type: {
            type: String,
            default: 'Profiles'
        }
    },
    setup(props) {
        const activeNames = computed(() => props.type);
        // user显示用户列表
        const tableData = ref<Array<UserType>>([]);
        const getUserList = (searchValue: string, sort: string | null) => {
            const sortParams: {
                [key in string]: number
            } = {
                descending: 2,
                ascending: 1
            };
            HttpRequest.get('getUserList', {
                row: 100,
                Search: searchValue,
                Sort: sort ? sortParams[sort] : ''
            }, (res: {
                data: {
                    row: Array<UserType>;
                }
            }) => {
                tableData.value = res.data.row;
            });
        };
        const searchValue = ref('');
        const sort = ref<string | null>(null);
        watch(searchValue, () => {
            getUserList(searchValue.value, sort.value);
        });

        // 第一次就是user页面主动请求数据
        if (activeNames.value === 'Users') {
            getUserList(searchValue.value, sort.value);
        }
        const router = useRouter();
        const changeTab = (tab: TabsPaneContext) => {
            router.push(`/user?type=${tab.paneName}`);
            if (tab.paneName === 'Users') {
                getUserList(searchValue.value, sort.value);
            }
        };

        const formRef = ref();
        const rules = {
            Name: [{
                required: true,
                trigger: 'blur',
                message: 'Please enter the name.'
            }]
            // Email: [{
            //     required: true,
            //     trigger: 'blur',
            //     message: 'Please enter the email.'
            // }, {
            //     validator: checkEmail,
            //     trigger: 'blur'
            // }]
        };
        const userData = JSON.parse(localStorage.getItem('userInfo')!);
        const formData = ref({
            Name: userData.userName,
            Email: userData.email
        });

        // 修改信息
        const editProfile = () => {
            formRef.value.validate((valid: boolean) => {
                if (valid) {
                    HttpRequest.post('updateInfo', {
                        Username: formData.value.Name
                    }, () => {
                        userData.userName = formData.value.Name;
                        localStorage.setItem('userInfo', JSON.stringify(userData));
                        ElMessage({
                            message: 'Edit Success',
                            type: 'success'
                        });
                    });
                }
            });
        };
        // 修改密码
        const isShowPasswordDialog = ref(false);

        const deleteUser = (index: string, row: UserType) => {
            ElMessageBox.confirm(
                'Are you sure you want to delete the user?',
                'Delete User',
                {
                    confirmButtonText: 'Delete',
                    cancelButtonText: 'Cancel',
                    center: true
                }
            ).then(() => {
                HttpRequest.post('delUser', {
                    ID: row.ID
                }, () => {
                    getUserList(searchValue.value, sort.value);
                    ElMessage({
                        message: 'User Deleted!',
                        type: 'success'
                    });
                });
            }).catch(() => false);
        };
        const isShowCreateUserDialog = ref(false);
        const refreshPage = () => {
            isShowCreateUserDialog.value = false;
            getUserList(searchValue.value, sort.value);
        };
        const updatePw = () => {
            goBackLogin();
        };
        const sortChange = (current: {
            order: string
        }) => {
            sort.value = current.order;
            getUserList(searchValue.value, current.order);
        };

        // 获取服务器列表
        const serverList = ref<Array<OptionsType>>([]);
        const pageName = ref('');
        const getHeaderData = () => {
            HttpRequest.get('getConfig', {}, (res: {
                data: {
                    admin: {
                        pageName: string;
                    },
                    serverList: Array<{
                        name: string;
                        code: string;
                    }>,
                    token: {
                        Title: string;
                    }
                }
            }) => {
                pageName.value = res.data.token.Title === '' ? 'Global' : res.data.token.Title;
                serverList.value = [{
                    label: 'Global',
                    value: 'global'
                }];
                res.data.serverList.forEach((item) => {
                    serverList.value.push({
                        label: item.name,
                        value: item.code
                    });
                });
            });
        };
        getHeaderData();
        const goToServerPage = (code: string) => {
            if (code !== 'global') {
                router.push(`/server?type=${code}`);
            } else {
                router.push('/global');
            }
        };

        return {
            activeNames,
            changeTab,
            userData,
            formRef,
            rules,
            formData,
            editProfile,
            isShowPasswordDialog,
            tableData,
            deleteUser,
            isShowCreateUserDialog,
            refreshPage,
            updatePw,
            searchValue,
            sortChange,
            serverList,
            pageName,
            goToServerPage
        };
    }
});
</script>

<style lang="less" scoped>
@import url("../../assets/less/common.less");
.all-container {
    padding: 100px;
    height: 100%;
    box-sizing: border-box;
}
.identity-icon {
    border: 1px solid #cccccc;
    padding: 0 10px;
    border-radius: 8px;
    text-align: center;
    color: gray;
}
.delete-icon {
    color: #ff210a;
}
.search-header {
    justify-content: space-between;
}
</style>
