<?php
/**
 * @description 数据库连接类
 * <AUTHOR>
 * @date 2022/5/10 14:26
 * @version V6.4
 * @lastEditor csc
 * @lastEditTime 2022/5/10 14:26
 * @lastVersion V6.4
 */

class DataBase
{
    private static $instance;

    private function __construct($config)
    {
        $host = $config['host'];
        $username = $config['username'];
        $password = $config['password'];
        $dbName = $config['dbname'];
        $this->db = new PDO("mysql:host={$host};dbname={$dbName}", $username, $password);
    }

    public static function getInstance($config)
    {
        $instanceKey = md5(serialize($config));
        if (!self::$instance[$instanceKey]) {
            self::$instance[$instanceKey] = new self($config);
        }
        return self::$instance[$instanceKey];
    }

    public function querySList($sql, $bindArray = [])
    {
        $simt = $this->db->prepare($sql);
        foreach ($bindArray as $key => $value) {
            $simt->bindValue($key, $value);
        }
        $simt->execute();
        $data = $simt->fetchAll(PDO::FETCH_ASSOC);
        return $data;
    }

    public function insert2List($tableName, $bindArray = [], $tryIndex = 0)
    {
        //插入数据
        $keys = [];
        $filed = [];
        foreach ($bindArray as $key => $value) {
            array_push($keys, $key);
            array_push($filed, substr($key, 1));
        }
        $keys = implode(",", $keys);
        $filed = implode(",", $filed);

        $this->sql = "insert into $tableName ( $filed ) value( $keys )";
        $simt = $this->db->prepare($this->sql);
        foreach ($bindArray as $key => $value) {
            $simt->bindValue($key, $value);
        }
        $simt->execute();
        return $this->db->lastInsertId();
    }

    public function update2ListWID($tableName, $bindArray)
    {
        //根据ID更新数据
        return $this->update2ListWKey($tableName, $bindArray, "ID");
    }

    public function update2ListWKey($tableName, $bindArray, $keyWord, $tryIndex = 0)
    {
        $filed = [];
        foreach ($bindArray as $key => $value) {
            $keys = substr($key, 1)." = ".$key;
            array_push($filed, $keys);
        }

        $filed = implode(",", $filed);

        $this->sql = "update $tableName set $filed where $keyWord = :$keyWord";
        $simt = $this->db->prepare($this->sql);
        foreach ($bindArray as $key => $value) {
            $simt->bindValue($key, $value);
        }
        $simt->execute();

        return $simt->rowCount();
    }


    public function delete2ListWID($tableName, $id, $tryIndex = 0)
    {
        //根据ID删除数据
        $this->sql = "delete from $tableName where ID = :ID";
        $simt = $this->db->prepare($this->sql);
        $simt->bindValue(":ID", $id);
        $simt->execute();
    }

    public function delete2ListWKey($tableName, $keyName, $keyValue, $tryIndex = 0)
    {
        $this->sql = "delete from $tableName where $keyName = :$keyName";
        $simt = $this->db->prepare($this->sql);
        $simt->bindValue(":$keyName", $keyValue);
        $simt->execute();
    }

    public function execSql($sql, $bindArray = [])
    {
        $simt = $this->db->prepare($sql);
        foreach ($bindArray as $key => $value) {
            $simt->bindValue($key, $value);
        }
        return $simt->execute();
    }
}
