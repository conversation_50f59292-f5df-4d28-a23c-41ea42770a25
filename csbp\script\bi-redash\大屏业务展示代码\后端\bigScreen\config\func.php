<?php
/**
 * @description 功能方法
 * <AUTHOR>
 * @date 2022/5/10 15:25
 * @version V6.4
 * @lastEditor csc
 * @lastEditTime 2022/5/10 15:25
 * @lastVersion V6.4
 */

//获取配置文件中的配置项
if (!function_exists('config')) {
    function config($key)
    {
        global $gConfig;

        return isset($gConfig[$key]) ? $gConfig[$key] : null;
    }
}

//返回json结果并输出
function returnJson($code = 0, $msg = '', $data = [])
{
    $res = [
        'code' => $code,
        'msg' => $msg,
        'data' => $data,
    ];
    printf(json_encode($res));
    exit;
}

//获取参数
function getParams($param, $default = '')
{
    $method = $_SERVER['REQUEST_METHOD'];
    if ('POST' == strtoupper($method)) {
        return isset($_POST[$param]) ? $_POST[$param] : $default;
    } elseif ('GET' == strtoupper($method)) {
        return isset($_GET[$param]) ? $_GET[$param] : $default;
    } else {
        returnJson(1, 'Request parameter type not allowed');
    }
}

//获取加密密码
function getEncryptPasswd($account, $password)
{
    return md5(config('md5Salt') . $account . $password);
}

//生成随机字符串
function randString($length = 16)
{
    $str = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $len = strlen($str) - 1;
    $randstr = '';
    for ($i = 0; $i < $length; $i++) {
        $num = mt_rand(0, $len);
        $randstr .= $str[$num];
    }
    return $randstr;
}

//强制必须为post方法
function checkPost()
{
    $method = $_SERVER['REQUEST_METHOD'];
    if ('POST' != strtoupper($method)) {
        returnJson(1, 'Request type not allowed');
    }
}

//发送创建用户邮件
function sendAddUserEmail($user, $pwd, $sendto_email)
{
    require_once dirname((dirname(__FILE__))) . '/vendor/PHPMail/PHPMailerAutoload.php';
    $url = config('loginUrl');
    $mail = new PHPMailer;
    $mail->Charset = 'UTF-8';
    $mail->SMTPOptions = array(
        'ssl' => array(
            'verify_peer' => false,
            'verify_peer_name' => false,
            'allow_self_signed' => true
        )
    );
    //$mail->SMTPDebug = 3;                               // Enable verbose debug output

    $mail->isSMTP();                                      // Set mailer to use SMTP
    $mail->Host = 'hwsmtp.exmail.qq.com;smtp.exmail.qq.com';  // Specify main and backup SMTP servers
    $mail->SMTPAuth = true;                               // Enable SMTP authentication
    $mail->Username = '<EMAIL>';                 // SMTP username
    $mail->Password = 'Aa.789789.1';                        // SMTP password
    $mail->SMTPSecure = 'ssl';                            // Enable TLS encryption, `ssl` also accepted
    $mail->Port = 465;                                    // TCP port to connect to

    $mail->setFrom('<EMAIL>', 'Akuvox');
    $mail->addAddress($sendto_email, $user);       // Name is optional

    $mail->isHTML(true);                                  // Set email format to HTML

    $subject = '[Akuvox] Welcome to Akuvox Business Intelligence Center';
    $mail->Subject = "=?utf-8?B?" . base64_encode($subject) . "?=";
    $tmpBody = '
    			 <html>
                     <head>
				        <meta http-equiv=\"Content-Language\" content=\"en-US\">   
				        <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">   
			        </head>   
			          <body style="width:80%;margin:0 aotu;padding:10px;">   
                        Dear ' . $user . ',
                         <br/>
                        Your account has been created.
                        <table>
                        <tr>
                        <td>
                        Username: <label style=color:red;>' . $sendto_email . '</label>
                         <br/>
                        Password: <label style=color:red;>' . $pwd . '</label>
                        </td>
                        <td style="width:100px;">
                        </td>
                        <td>
                        </td>
                        </tr>
                        </table>
                        <br/>
                        <br/>
                        <a href="' . $url . '" target="_blank" >Click here to visit Akuvox Business Intelligence Center</a>
                        <br/>
                        <br/>
                        -------------------------
                        <br/>
                        Best regards,
                        <br/>
                        <br/>
                        The Akuvox Team
			          </body>   
			    </html>
    ';

    $mail->Body = $tmpBody;
    $mail->AltBody = 'This is the body in plain text for non-HTML mail clients';
    if (!$mail->send()) {
        $now = date('Y-m-d H:i:s');
        @error_log("TIME:$now \r\nMSG:$mail->ErrorInfo \r\n\r\n", 3, '../log/email_error.log');
    }
}

//重置密码邮件
function sendResetPwdEMail($url, $sendto_email, $user_name)
{
    require_once dirname((dirname(__FILE__))) . '/vendor/PHPMail/PHPMailerAutoload.php';
    $now = date("Y-m-d H:i:s");
    @error_log("TIME:$now \r\nMSG:ChangePwdMail \r\n\r\n", 3, '../log/email_error.log');
    $mail = new PHPMailer;
    $mail->Charset = 'UTF-8';
    $mail->SMTPOptions = array(
        'ssl' => array(
            'verify_peer' => false,
            'verify_peer_name' => false,
            'allow_self_signed' => true
        )
    );

    $mail->isSMTP();                                      // Set mailer to use SMTP
    $mail->Host = 'hwsmtp.exmail.qq.com;smtp.exmail.qq.com';  // Specify main and backup SMTP servers
    $mail->SMTPAuth = true;                               // Enable SMTP authentication
    $mail->Username = '<EMAIL>';                 // SMTP username
    $mail->Password = 'Aa.789789.1';                        // SMTP password
    $mail->SMTPSecure = 'ssl';                            // Enable TLS encryption, `ssl` also accepted
    $mail->Port = 465;                                    // TCP port to connect to
    $mail->setFrom('<EMAIL>', 'Akuvox');
    $mail->addAddress($sendto_email, $user_name);       // Name is optional

    $mail->isHTML(true);                                  // Set email format to HTML

    $subject = '[Akuvox] Business Intelligence Center Password Reset';
    $mail->Subject = "=?utf-8?B?" . base64_encode($subject) . "?=";
    $tmpBody = '<html>
                     <head>
				        <meta http-equiv=\"Content-Language\" content=\"en-US\">   
				        <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">   
			        </head>   
			          <body>   
                         	Dear ' . $user_name . '
                         <br/>
                         <br/>
                         We heard you need a password reset. Click the link below and you\'ll be redirected to a secure site from which you can set a new password.
                         <br/>
                         <br/>
                         <a href="' . $url . '" target="_blank" >The link will expire in 3 hours.</a>
                    
                         <br/>
                         <br/>
                         -----------------------
                         <br/>
                         Best regards,
                         <br/>
                         <br/>
                         The Akuvox Team
			          </body>   
			    </html>';
    $mail->Subject = '[Akuvox] Business Intelligence Center Password Reset ';
    $mail->Body = $tmpBody;
    $mail->AltBody = 'This is the body in plain text for non-HTML mail clients';

    if (!$mail->send()) {
        error_log("TIME:$now \r\nMSG:$mail->ErrorInfo \r\n\r\n", 3, '../log/email_error.log');
        return -1;
    } else {
        //echo 'Message has been sent';
        return 0;
    }
}


function dd()
{
    $args = func_get_args();
    echo '<pre>';
    foreach ($args as $arg) {
        print_r($arg);
        echo '<br>';
    }
    echo '</pre>';
    exit;
}

function pr()
{
    $args = func_get_args();
    echo '<pre>';
    foreach ($args as $arg) {
        print_r($arg);
        echo '<br>';
    }
    echo '</pre>';
}