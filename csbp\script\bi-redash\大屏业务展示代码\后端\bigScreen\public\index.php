<?php
/**
 * @description 简单的入口文件
 * <AUTHOR>
 * @date 2022/5/11 18:31
 * @version V6.4
 * @lastEditor csc
 * @lastEditTime 2022/5/11 18:31
 * @lastVersion V6.4
 */
header('Access-Control-Allow-Methods:POST,GET,OPTIONS,DELETE');
header('Access-Control-Allow-Headers:x-auth-token');
header('Access-Control-Expose-Headers:x-auth-token');
error_reporting(E_ERROR | E_PARSE);

$uri = $_SERVER['REQUEST_URI'];
$pro = strpos($uri, '?');
if ($pro !== false) {
    $uri = substr($uri, 0, $pro);
}

$file = explode('/', rawurldecode($uri));
$file = array_pop($file);
$path = "../src/$file.php";
if (!file_exists($path)) {
    header("HTTP/1.1 404 Not Found");
    exit;
}

global $gFile;
$gFile = $file;

try {
    include_once $path;
}
catch (Exception $e) {
    $now = date('Y-m-d H:i:s');
    @error_log("TIME:$now \r\nMSG:$e \r\n\r\n", 3, '../log/php_error.log');
    header("HTTP/1.1 500 Server error");
    echo 'There seems to be some problems at present, please contact the administrator';
    exit;
}
catch (Throwable $e) {
    $now = date('Y-m-d H:i:s');
    @error_log("TIME:$now \r\nMSG:$e \r\n\r\n", 3, '../log/php_error.log');
    header("HTTP/1.1 500 Server error");
    echo 'There seems to be some problems at present, please contact the administrator';
    exit;
}
