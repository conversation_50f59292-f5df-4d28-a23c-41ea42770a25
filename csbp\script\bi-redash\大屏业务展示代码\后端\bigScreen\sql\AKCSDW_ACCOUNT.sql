-- MySQL dump 10.13  Distrib 5.7.34, for Linux (x86_64)
--
-- Host: localhost    Database: AKCSDW_ACCOUNT
-- ------------------------------------------------------
-- Server version	5.7.34-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `Admin`
--

DROP TABLE IF EXISTS `Admin`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `Admin` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Account` varchar(64) NOT NULL DEFAULT '' COMMENT '帐号',
  `Password` varchar(32) NOT NULL DEFAULT '' COMMENT '密码',
  `Email` varchar(64) NOT NULL DEFAULT '' COMMENT '邮箱',
  `Nickname` varchar(32) NOT NULL DEFAULT '' COMMENT '昵称',
  `Status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态：0-正常 1-禁止登陆',
  `Level` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0-超级管理员 1-普通管理员 2-用户',
  PRIMARY KEY (`ID`),
  KEY `Admin_Account_index` (`Account`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `Admin`
--

LOCK TABLES `Admin` WRITE;
/*!40000 ALTER TABLE `Admin` DISABLE KEYS */;
INSERT INTO `Admin` VALUES (1,'supermanage','f45adc3142c541fde0e92f8e6e811b3e','','超级管理员',0,1);
/*!40000 ALTER TABLE `Admin` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `AdminConfig`
--

DROP TABLE IF EXISTS `AdminConfig`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `AdminConfig` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `AdminID` int(11) NOT NULL DEFAULT '0' COMMENT '管理员ID',
  `ConfigItem` varchar(32) NOT NULL DEFAULT '' COMMENT '配置项名称',
  `ConfigVal` varchar(512) NOT NULL DEFAULT '' COMMENT '配置值',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `idx_config` (`AdminID`,`ConfigItem`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='管理员配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `AdminConfig`
--

LOCK TABLES `AdminConfig` WRITE;
/*!40000 ALTER TABLE `AdminConfig` DISABLE KEYS */;
/*!40000 ALTER TABLE `AdminConfig` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `AdminToken`
--

DROP TABLE IF EXISTS `AdminToken`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `AdminToken` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `AdminID` int(11) NOT NULL DEFAULT '0' COMMENT 'admin的id',
  `Token` varchar(32) NOT NULL DEFAULT '' COMMENT 'token值',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `TokenEt` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `IDX_AdminID` (`AdminID`),
  KEY `IDX_Token` (`Token`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='admin的token表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `AdminToken`
--

LOCK TABLES `AdminToken` WRITE;
/*!40000 ALTER TABLE `AdminToken` DISABLE KEYS */;
/*!40000 ALTER TABLE `AdminToken` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `EmailToken`
--

DROP TABLE IF EXISTS `EmailToken`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `EmailToken` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `AdminID` int(11) NOT NULL DEFAULT '0',
  `Token` varchar(32) NOT NULL DEFAULT '' COMMENT 'token\n',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `TokenEt` int(11) NOT NULL DEFAULT '0' COMMENT 'token过期时间',
  `IsUsed` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已使用，0-否，1-是',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `IDX_Token` (`Token`),
  KEY `IDX_AdminID` (`AdminID`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='邮箱的token';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `EmailToken`
--

LOCK TABLES `EmailToken` WRITE;
/*!40000 ALTER TABLE `EmailToken` DISABLE KEYS */;
/*!40000 ALTER TABLE `EmailToken` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `Token`
--

DROP TABLE IF EXISTS `Token`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `Token` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `AdminID` int(11) NOT NULL DEFAULT '0' COMMENT 'admin的id',
  `Token` varchar(32) NOT NULL DEFAULT '',
  `Config` varchar(2048) NOT NULL DEFAULT '' COMMENT '前端配置的参数，对应token获取时需要返回',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `IDX_Token` (`Token`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='分享链接的token表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `Token`
--

LOCK TABLES `Token` WRITE;
/*!40000 ALTER TABLE `Token` DISABLE KEYS */;
/*!40000 ALTER TABLE `Token` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2022-05-20 15:35:22
