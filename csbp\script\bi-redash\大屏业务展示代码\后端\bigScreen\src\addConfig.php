<?php
/**
 * @description 增加配置项
 * <AUTHOR>
 * @date 2022/5/11 11:09
 * @version V6.4
 * @lastEditor csc
 * @lastEditTime 2022/5/11 11:09
 * @lastVersion V6.4
 */

include_once "../src/global.php";

checkPost(); //必须为post请求
$configItem = trim(getParams('ConfigItem'));
$configVal = trim(getParams('ConfigVal'));
if (empty($configItem) or empty($configVal)) {
    returnJson(1, 'Configuration cannot be empty');
}

global $gApp;
$db = \DataBase::getInstance(config('databaseAccount'));

$adminID = $gApp['admin']['ID'];
$config = $db->querySList("select * from AdminConfig where AdminID = :AdminID and ConfigItem = :ConfigItem",
    [':AdminID' => $adminID, ':ConfigItem' => $configItem]);

if (!empty($config)) {
    $now = date('Y-m-d H:i:s');
    $db->querySList("update AdminConfig set ConfigVal = :ConfigVal, UpdateTime = :UpdateTime where AdminID = :AdminID and ConfigItem = :ConfigItem",
        [':AdminID' => $adminID, ':ConfigItem' => $configItem, ':ConfigVal' => $configVal, ':UpdateTime' => $now]);
} else {
    $db->insert2List('AdminConfig', [
        ':AdminID' => $adminID,
        ':ConfigItem' => $configItem,
        ':ConfigVal' => $configVal,
    ]);
}

returnJson(0, 'Successfully added');