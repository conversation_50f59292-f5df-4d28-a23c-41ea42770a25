<?php
/**
 * @description 创建管理员或者用户
 * <AUTHOR>
 * @date 2022/5/10 17:06
 * @version V6.4
 * @lastEditor csc
 * @lastEditTime 2022/5/10 17:06
 * @lastVersion V6.4
 */

include_once "../src/global.php";

global $gApp;

checkPost(); //必须为post请求

$group = getParams('Group', 2); //group 1-普通管理员 2-用户
if (!in_array($group, [1, 2]) or !in_array($gApp['admin']['Level'], [0, 1])) {
    returnJson(1, 'Illegal operation, you do not have permission to perform this operation');
}

if ($group == 1 && $gApp['admin']['Level'] != 0) {
    returnJson(1, 'Only super administrator can create administrator');
}

$username = trim(getParams('Username'));
$email = trim(getParams('Email'));
if (empty($username) or empty($email)) {
    returnJson(1, 'Username and email cannot be empty');
}

if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    returnJson(1, 'Please fill in the correct email');
}

$db = \DataBase::getInstance(config('databaseAccount'));

$count = $db->querySList('select count(*) as count from Admin where Email = :Email',
    [':Email' => $email])[0]['count'];
if (!empty($count)) {
    returnJson(1, 'Email already exists');
}
$password = randString(8);
$ecPassword = getEncryptPasswd($email, $password);
$id = $db->insert2List('Admin', [
    ':Account' => $email,
    ':Password' => $ecPassword,
    ':Nickname' => $username,
    ':Email' => $email,
    ':Level' => $group,
]);
if ($id) {
    if (function_exists('fastcgi_finish_request')) {
        $res = [
            'code' => 0,
            'msg' => 'Created successfully',
            'data' => [],
        ];
        printf(json_encode($res));
        fastcgi_finish_request();
        sendAddUserEmail($username, $password, $email);
        exit(0);
    } else {
        sendAddUserEmail($username, $password, $email);
        returnJson(0, 'Created successfully');
    }
}

returnJson(1, 'Created failed');