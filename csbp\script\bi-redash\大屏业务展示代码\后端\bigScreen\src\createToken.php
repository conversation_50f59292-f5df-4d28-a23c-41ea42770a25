<?php
/**
 * @description 生成token
 * <AUTHOR>
 * @date 2022/5/10 17:06
 * @version V6.4
 * @lastEditor csc
 * @lastEditTime 2022/5/10 17:06
 * @lastVersion V6.4
 */

include_once "../src/global.php";

global $gApp;

checkPost(); //必须为post请求
$token = randString().$gApp['admin']['ID'];
$config = getParams('Config');
$config = json_decode($config, true);
if (!is_array($config)) {
    returnJson(1002, 'Failed to create Link. The parameter is illegal');
}

if (empty($config['Uri'])) {
    returnJson(1003, 'Failed to create Link. The parameter is illegal(uri dont exist)');
}

$config = [
    'Title' => isset($config['Title']) ? $config['Title'] : '',
    'Uri' => isset($config['Uri']) ? $config['Uri'] : '',
    'Region' => isset($config['Region']) ? $config['Region'] : '',
    'Dis' => isset($config['Dis']) ? $config['Dis'] : '',
];
$config = json_encode($config, JSON_UNESCAPED_UNICODE);

$db = \DataBase::getInstance(config('databaseAccount'));
$db->insert2List('Token', [
    ':AdminID' => $gApp['admin']['ID'],
    ':Token' => $token,
    ':Config' => $config,
]);

returnJson(0, 'Successfully', ['token' => $token]);