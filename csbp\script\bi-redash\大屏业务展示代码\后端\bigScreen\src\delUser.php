<?php
/**
 * @description 删除用户
 * <AUTHOR>
 * @date 2022/5/10 17:06
 * @version V6.4
 * @lastEditor csc
 * @lastEditTime 2022/5/10 17:06
 * @lastVersion V6.4
 */

include_once "../src/global.php";

global $gApp;

checkPost(); //必须为post请求

$id = trim(getParams('ID'));

if ($id == 1) {
    returnJson(1, 'This account cannot be deleted');
}

if (!in_array($gApp['admin']['Level'], [0, 1])) {
    returnJson(1, 'You do not have permission to delete users');
}

$db = \DataBase::getInstance(config('databaseAccount'));

$db->delete2ListWID('Admin', $id);

returnJson(0, 'Deleted successfully');