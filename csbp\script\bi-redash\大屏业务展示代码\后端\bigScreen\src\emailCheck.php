<?php
/**
 * @description 邮箱检测
 * <AUTHOR>
 * @date 2022/5/10 17:06
 * @version V6.4
 * @lastEditor csc
 * @lastEditTime 2022/5/10 17:06
 * @lastVersion V6.4
 */

include_once "../src/global.php";

global $gApp;

checkPost(); //必须为post请求

$email = trim(getParams('Email'));
if (empty($email)) {
    returnJson(1, 'Email cannot be empty');
}

if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
    returnJson(1, 'Please fill in the correct email');
}

$db = \DataBase::getInstance(config('databaseAccount'));

$count = $db->querySList('select count(*) as count from Admin where Email = :Email',
    [':Email' => $email])[0]['count'];
if (empty($count)) {
    returnJson(0, 'Successfully');
}
returnJson(1, 'Email already exists');