<?php
/**
 * @description 重置密码Token检测
 * <AUTHOR>
 * @date 2022/5/10 16:13
 * @version V6.4
 * @lastEditor csc
 * @lastEditTime 2022/5/10 16:13
 * @lastVersion V6.4
 */

include_once "../config/base.php";
include_once "../config/database.php";
include_once "../config/func.php";

checkPost(); //必须为post请求
$token = getParams('Token');

$db = \DataBase::getInstance(config('databaseAccount'));
$admin = $db->querySList("select A.* from EmailToken ET left join Admin A on A.ID = ET.AdminID where ET.Token = :Token and ET.IsUsed = 0 and ET.TokenEt > :TokenEt",
    [':Token' => $token, ':TokenEt' => time()]);
if (empty($admin)) {
    returnJson(1, 'Token has expired');
}

returnJson(0, 'Verification successful', ['Group' => $admin[0]['Level'], 'Nickname' => $admin[0]['Nickname'], 'Email' => $admin[0]['Email']]);
