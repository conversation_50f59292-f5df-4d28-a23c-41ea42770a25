<?php
/**
 * @description 获取地区 Business Analysis数据
 * <AUTHOR>
 * @date 2022/5/11 11:09
 * @version V6.4
 * @lastEditor csc
 * @lastEditTime 2022/5/11 11:09
 * @lastVersion V6.4
 */

include_once "../src/global.php";

$db = \DataBase::getInstance(config('databaseCommon'));

$region = getParams('Region');
$serverList = config('serverList');
$allRegion = array_column($serverList, 'code');
if (!in_array($region, $allRegion)) {
    returnJson(1, 'Servers in this region are temporarily unavailable');
}

//印度云和俄罗斯、中东云的统计不在总库中
if (in_array($region, ['RU', 'INDIA', 'INC'])) {
    $db = \DataBase::getInstance(config('database'. $region));
}

//最近12个月的数组
$lastMonths = [];
$lastMonths[] = date('Y-m');
for($i = 1; $i < 12; $i++) {
    $lastMonths[] = date('Y-m', strtotime("- $i month"));
}
$lastMonths = array_reverse($lastMonths);

//全球业务分布图
if (in_array($region, ['RU', 'INDIA', 'INC'])) {
    $globalBussiness = \DataBase::getInstance(config('databaseCommon'))->querySList("select * from GlobalBussiness", []);
} else {
    $globalBussiness = $db->querySList("select * from GlobalBussiness", []);
}

//总开门次数
$totalOpenDoor = $db->querySList("select (t1.num1 + t2.num2) as open_door_num from 
(select sum(Num) as num1 from GlobalOpenDoor where Region = :Region) t1,
(select Num as num2 from GlobalRealTimeData  where DataType = 1 and Region = :Region) t2", [':Region' => $region])[0]['open_door_num'];

//今日开门次数
$todayOpenDoor = $db->querySList("select sum(Num) as today_door_open_num from GlobalRealTimeData where DataType = 1 and Region = :Region",
    [':Region' => $region])[0]['today_door_open_num'];

//全球月开门次数趋势图
$globalOpenDoor = $db->querySList("select sum(Num) as DoorNum,DateTime from GlobalOpenDoor where Region = :Region group by DateTime",
    [':Region' => $region]);

//全球月开门次数趋势图 按年/最近12个月
$globalOpenDoorYear = $globalOpenDoorMonth = [];
if (!empty($globalOpenDoor)) {
    foreach ($globalOpenDoor as $val) {
        $year = date('Y', strtotime($val['DateTime']));
        if (isset($globalOpenDoorYear[$year])) {
            $globalOpenDoorYear[$year]['DoorNum'] += $val['DoorNum'];
        } else {
            $globalOpenDoorYear[$year] = [
                'DateTime' => $year,
                'DoorNum' => $val['DoorNum']
            ];
        }
        if (in_array($val['DateTime'], $lastMonths)) {
            $globalOpenDoorMonth[] = $val;
        }
    }
    $globalOpenDoorYear = array_values($globalOpenDoorYear);
}

//开门成功类型统计图
$openDoorType = $db->querySList("SELECT (CASE TYPE
            WHEN 0 THEN 'CALL'
            WHEN 1 THEN 'TempKey'
            WHEN 2 THEN 'PrivateKey'
            WHEN 3 THEN 'RFCard'
            WHEN 4 THEN 'FaceRecognition'
            WHEN 100 THEN 'NFC'
            ELSE 'BLE'
        END) as OpenDoorType,sum(Num) as OpenDoorNum 
FROM GlobalOpenDoorType where Region = :Region group by OpenDoorType order by OpenDoorNum desc", [':Region' => $region]);

//$activeAppNum = $db->querySList("select sum(Num) as ActiveAppNum from GlobalRealTimeData where DataType = 11 and Region = :Region", [':Region' => $region])[0]['ActiveAppNum'];

//激活家庭数
$activeFamilyNum = $db->querySList("select sum(Num) as ActiveFamilyNum from GlobalRealTimeData where DataType = 10 and Region = :Region",
    [':Region' => $region])[0]['ActiveFamilyNum'];

//办公用户数
$officeNum = $db->querySList("select sum(Num) as OfficeNum from GlobalRealTimeData where DataType = 13 and Region = :Region",
    [':Region' => $region])[0]['OfficeNum'];

//家庭/办公用户数月趋势图
$familyAndOfficeNum = $db->querySList("SELECT F.DateTime,
       sum(F.Num) as FamilyNum,
       sum(A.Num) as OfficeNum
FROM GlobalActiveFamily F
LEFT JOIN GlobalOffice A ON (A.DateTime = F.DateTime
                                     AND A.Region = F.Region) where F.Region = :Region group BY F.DateTime", [':Region' => $region]);

//家庭/办公用户数月趋势图 按年/最近12个月
$familyAndOfficeNumYear = $familyAndOfficeNumMonth = [];
if (!empty($familyAndOfficeNum)) {
    foreach ($familyAndOfficeNum as $val) {
        $year = date('Y', strtotime($val['DateTime']));
        if (isset($familyAndOfficeNumYear[$year])) {
            $familyAndOfficeNumYear[$year]['FamilyNum'] += $val['FamilyNum'];
            $familyAndOfficeNumYear[$year]['OfficeNum'] += $val['OfficeNum'];
        } else {
            $familyAndOfficeNumYear[$year] = [
                'DateTime' => $year,
                'FamilyNum' => $val['FamilyNum'],
                'OfficeNum' => $val['OfficeNum'],
            ];
        }
        if (in_array($val['DateTime'], $lastMonths)) {
            $familyAndOfficeNumMonth[] = $val;
        }
    }
    $familyAndOfficeNumYear = array_values($familyAndOfficeNumYear);
}

//月租用户数
$monthFeeUserNum = $db->querySList("select sum(Num) as MonthFeeUserNum from GlobalRealTimeData where DataType = 12 and Region = :Region",
    [':Region' => $region])[0]['MonthFeeUserNum'];

//月租用户数月趋势图
$monthFeeUserMonthNum = $db->querysList("select DateTime, sum(Num) as FeeFamilyNum from GlobalFeeFamily where Region = :Region group by DateTime",
    [':Region' => $region]);
//月租用户数月趋势图 按年/最近12个月
$monthFeeUserMonthNumYear = $monthFeeUserMonthNumMonth = [];
if (!empty($monthFeeUserMonthNum)) {
    foreach ($monthFeeUserMonthNum as $val) {
        $year = date('Y', strtotime($val['DateTime']));
        if (isset($monthFeeUserMonthNumYear[$year])) {
            $monthFeeUserMonthNumYear[$year]['FeeFamilyNum'] += $val['FeeFamilyNum'];
        } else {
            $monthFeeUserMonthNumYear[$year] = [
                'DateTime' => $year,
                'FeeFamilyNum' => $val['FeeFamilyNum']
            ];
        }
        if (in_array($val['DateTime'], $lastMonths)) {
            $monthFeeUserMonthNumMonth[] = $val;
        }
    }
    $monthFeeUserMonthNumYear = array_values($monthFeeUserMonthNumYear);
}

//注册设备数
$registerDeviceNum = $db->querySList("select sum(Num) as RegisterDeviceNum from GlobalRealTimeData where DataType = 4 and Region = :Region",
    [':Region' => $region])[0]['RegisterDeviceNum'];

//在线设备数
$deviceOnline = $db->querySList("select t1.Num as online,t2.Num as register,convert(t1.Num/t2.Num,decimal(4,2)) as rate from 
  (select sum(Num) as Num from GlobalRealTimeData where DataType = 3 and Region = :Region) t1,
  (select sum(Num) as Num from GlobalRealTimeData where DataType = 4 and Region = :Region) t2", [':Region' => $region])[0];

//设备趋势图
$globalDevice = $db->querySList("SELECT O.DateTime,
       sum(O.Num) as online_num,
       sum(R.Num) as register_num
FROM GlobalOnlineDevice O
LEFT JOIN GlobalRegisterDevice R ON (R.DateTime = O.DateTime
                                     AND R.Region = O.Region) where R.Region = :Region group BY O.DateTime", [':Region' => $region]);
//设备趋势图 按年/最近12个月
$globalDeviceYear = $globalDeviceMonth = [];
if (!empty($globalDevice)) {
    foreach ($globalDevice as $val) {
        $year = date('Y', strtotime($val['DateTime']));
        if (isset($globalDeviceYear[$year])) {
            $globalDeviceYear[$year]['online_num'] += $val['online_num'];
            $globalDeviceYear[$year]['register_num'] += $val['register_num'];
        } else {
            $globalDeviceYear[$year] = [
                'DateTime' => $year,
                'online_num' => $val['online_num'],
                'register_num' => $val['register_num'],
            ];
        }
        if (in_array($val['DateTime'], $lastMonths)) {
            $globalDeviceMonth[] = $val;
        }
    }
    $globalDeviceYear = array_values($globalDeviceYear);
}

//总live view次数
$totalLiveViewNum = $db->querySList("select sum(Num) as LiveViewNum from GlobalRealTimeData where DataType = 7 and Region = :Region",
    [':Region' => $region])[0]['LiveViewNum'];

//live view趋势图
$globalLiveView = $db->querySList("select DateTime, sum(Num) as LiveViewNum from GlobalRtsp where Region = :Region group by DateTime", [':Region' => $region]);

//总通话次数
$totalCallNum = $db->querySList("select (t1.num1 + t2.num2) as call_num from 
(select sum(Num) as num1 from GlobalCall where Region = :Region) t1,
(select Num as num2 from GlobalRealTimeData where DataType = 2 and Region = :Region) t2", [':Region' => $region])[0]['call_num'];

//今日通话次数
$todayCallNum = $db->querySList("select sum(Num) as today_call_num from GlobalRealTimeData where DataType = 2 and Region = :Region",
    [':Region' => $region])[0]['today_call_num'];

//全球月呼叫次数趋势图
$globalCallNum = $db->querySList("select DateTime,sum(Num) as CallNum from GlobalCall where DateTime >= '2019-09' and Region = :Region group by DateTime",
    [':Region' => $region]);
//全球月呼叫次数趋势图 按年/最近12个月
$globalCallNumYear = $globalCallNumMonth = [];
if (!empty($globalCallNum)) {
    foreach ($globalCallNum as $val) {
        $year = date('Y', strtotime($val['DateTime']));
        if (isset($globalCallNumYear[$year])) {
            $globalCallNumYear[$year]['CallNum'] += $val['CallNum'];
        } else {
            $globalCallNumYear[$year] = [
                'DateTime' => $year,
                'CallNum' => $val['CallNum']
            ];
        }
        if (in_array($val['DateTime'], $lastMonths)) {
            $globalCallNumMonth[] = $val;
        }
    }
    $globalCallNumYear = array_values($globalCallNumYear);
}

//当前服务器下所有有统计的代理
$dbRegion = \DataBase::getInstance(config('database'. $region));
$disInfo = $dbRegion->querySList("select Dis,sum(Num) as pro_count from DisProjectSize where Dis not in (select Dis from DisListRemove) group by Dis order by pro_count desc limit 20;");

$disList = array_column($disInfo, 'Dis');
$extraDisInfo = $dbRegion->querySList('select Dis from DisList');
$extraDisList = array_column($extraDisInfo, 'Dis');
$disList = array_values(array_unique(array_merge($disList, $extraDisList)));

$data = [
    "GlobalBusiness" => $globalBussiness, //全球业务分布图
    "TotalOpenDoorNum" => $totalOpenDoor, //总开门次数
    "TodayOpenDoorNum" => $todayOpenDoor, //今日开门次数
    "GlobalOpenDoorNum" => $globalOpenDoor, //全球月开门次数趋势图
    "GlobalOpenDoorNumYear" => $globalOpenDoorYear, //全球月开门次数趋势图 按年
    "GlobalOpenDoorNumMonth" => $globalOpenDoorMonth, //全球月开门次数趋势图 最近12个月
    "OpenDoorSuccessType" => $openDoorType, //开门成功类型统计图
    "ActiveFamilyNum" => $activeFamilyNum, //激活家庭数
//	"ActiveAppNum" => $activeAppNum,
    "FamilyAndOFficeNum" => $familyAndOfficeNum, //家庭/办公用户数月趋势图
    "FamilyAndOFficeNumYear" => $familyAndOfficeNumYear, //家庭/办公用户数月趋势图 按年
    "FamilyAndOFficeNumMonth" => $familyAndOfficeNumMonth, //家庭/办公用户数月趋势图 最近12个月
    "RegisteredDeviceNum" => $registerDeviceNum, //注册设备数
    "DeviceOnline" => $deviceOnline, //在线设备数
    "Device" => $globalDevice, //设备趋势图
    "DeviceYear" => $globalDeviceYear, //设备趋势图 按年
    "DeviceMonth" => $globalDeviceMonth, //设备趋势图 最近12个月
    "TotalLiveView" => $totalLiveViewNum, //总live view次数
    "LiveView" => $globalLiveView, //live view趋势图
    "TotalCallNum" => $totalCallNum, //总通话次数
    "TodayCallNum" => $todayCallNum, //今日通话次数
    "GlobalMonthCallNum" => $globalCallNum, //全球月呼叫次数趋势图
    "GlobalMonthCallNumYear" => $globalCallNumYear, //全球月呼叫次数趋势图 按年
    "GlobalMonthCallNumMonth" => $globalCallNumMonth, //全球月呼叫次数趋势图 最近12个月
    "OfficeNum" => $officeNum, //办公用户数
    "MonthFeeUserNum" => $monthFeeUserNum, //月租用户数
    "MonthFeeUserMonthNum" => $monthFeeUserMonthNum, //月租用户数月趋势图
    "MonthFeeUserMonthNumYear" => $monthFeeUserMonthNumYear, //月租用户数月趋势图 按年
    "MonthFeeUserMonthNumMonth" => $monthFeeUserMonthNumMonth, //月租用户数月趋势图 最近12个月
    "disList" => $disList, //当前服务器下所有代理
];

if (!empty($token)) {
    unset($data['disList']);
}
returnJson(0, 'Successful', $data);
