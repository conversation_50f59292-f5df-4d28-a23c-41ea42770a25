<?php
/**
 * @description 获取地区下代理的 Business Analysis数据
 * <AUTHOR>
 * @date 2022/5/11 11:09
 * @version V6.4
 * @lastEditor csc
 * @lastEditTime 2022/5/11 11:09
 * @lastVersion V6.4
 */

include_once "../src/global.php";

$region = getParams('Region');
$dis = getParams('Dis');
$serverList = config('serverList');
$allRegion = array_column($serverList, 'code');
if (!in_array($region, $allRegion)) {
    returnJson(1, 'Servers in this region are temporarily unavailable');
}

if (empty($dis)) {
    returnJson(1, 'Dis information cannot be empty');
}

$db = \DataBase::getInstance(config('database'. $region));

//最近12个月的数组
$lastMonths = [];
$lastMonths[] = date('Y-m');
for($i = 1; $i < 12; $i++) {
    $lastMonths[] = date('Y-m', strtotime("- $i month"));
}
$lastMonths = array_reverse($lastMonths);

//项目数
$totalProjects = $db->querySList("select Num from DisRealTimeData where DataType = 7 and Dis = :Dis",
    [':Dis' => $dis])[0]['Num'];

//用户数
$userNum = $db->querySList("select sum(Num) as num from DisRealTimeData where Dis = :Dis and DataType in (4, 6)",
    [':Dis' => $dis])[0]['num'];

//月租用户数
$monthFeeUserNum = $db->querySList("select sum(Num) as num from DisRealTimeData where Dis = :Dis and DataType = 8",
    [':Dis' => $dis])[0]['num'];

//Installer项目分部图
$installerProjects = $db->querySList("SELECT Ins,
       (CASE
            WHEN ProType=1 THEN '1-10 households'
            WHEN ProType=2 THEN '11-25 households'
            WHEN ProType=3 THEN '26-50 households'
            WHEN ProType=4 THEN '51-100 households'
            WHEN ProType=5 THEN '101-200 households'
            WHEN ProType=6 THEN '201-500 households'
            WHEN ProType=7 THEN 'more than 500 households'
            ELSE '>500户'
        END) AS SIZE,
       Num AS project_num
FROM InsProjectSize
WHERE Dis = :Dis
  AND Ins IN
    (SELECT t1.Ins
     FROM
       (SELECT Ins
        FROM InsProjectSize where Dis = :Dis
        GROUP BY Ins
        ORDER BY sum(Num) DESC
        LIMIT 10) AS t1)", [':Dis' => $dis]);

//家庭/办公用户数月趋势图
$familyAndOfficeNum = $db->querySList("SELECT F.DateTime,
       sum(F.Num) as FamilyNum,
       sum(A.Num) as OfficeNum
FROM DisActiveFamily F
LEFT JOIN DisActiveOffice A ON (A.DateTime = F.DateTime
                                     AND A.Dis = F.Dis) where F.Dis = :Dis group BY F.DateTime", [':Dis' => $dis]);

//家庭/办公用户数月趋势图 按年/最近12个月
$familyAndOfficeNumYear = $familyAndOfficeNumMonth = [];
if (!empty($familyAndOfficeNum)) {
    foreach ($familyAndOfficeNum as $val) {
        $year = date('Y', strtotime($val['DateTime']));
        if (isset($familyAndOfficeNumYear[$year])) {
            $familyAndOfficeNumYear[$year]['FamilyNum'] += $val['FamilyNum'];
            $familyAndOfficeNumYear[$year]['OfficeNum'] += $val['OfficeNum'];
        } else {
            $familyAndOfficeNumYear[$year] = [
                'DateTime' => $year,
                'FamilyNum' => $val['FamilyNum'],
                'OfficeNum' => $val['OfficeNum'],
            ];
        }
        if (in_array($val['DateTime'], $lastMonths)) {
            $familyAndOfficeNumMonth[] = $val;
        }
    }
    $familyAndOfficeNumYear = array_values($familyAndOfficeNumYear);
}

//月租用户数月趋势图
$monthFeeUserMonthNum = $db->querysList("select DateTime, sum(Num) as FeeFamilyNum from DisFeeFamily where Dis = :Dis group by DateTime",
    [':Dis' => $dis]);

//月租用户数月趋势图 按年/最近12个月
$monthFeeUserMonthNumYear = $monthFeeUserMonthNumMonth = [];
if (!empty($monthFeeUserMonthNum)) {
    foreach ($monthFeeUserMonthNum as $val) {
        $year = date('Y', strtotime($val['DateTime']));
        if (isset($monthFeeUserMonthNumYear[$year])) {
            $monthFeeUserMonthNumYear[$year]['FeeFamilyNum'] += $val['FeeFamilyNum'];
        } else {
            $monthFeeUserMonthNumYear[$year] = [
                'DateTime' => $year,
                'FeeFamilyNum' => $val['FeeFamilyNum']
            ];
        }
        if (in_array($val['DateTime'], $lastMonths)) {
            $monthFeeUserMonthNumMonth[] = $val;
        }
    }
    $monthFeeUserMonthNumYear = array_values($monthFeeUserMonthNumYear);
}

//开门总数
$totalOpenDoor = $db->querySList("select (t1.num1 + t2.num2) as open_door_num from 
(select sum(Num) as num1 from DisOpenDoor where Dis = :Dis) t1,
(select Num as num2 from DisRealTimeData  where DataType = 1 and Dis = :Dis) t2",
    [':Dis' => $dis])[0]['open_door_num'];

//当天开门统计
$todayOpenDoor = $db->querySList("select Num from DisRealTimeData where DataType =1 and Dis = :Dis",
    [':Dis' => $dis])[0]['Num'];

//开门次数月趋势图
$openDoorList = $db->querySList("select Dis,DateTime,Num from DisOpenDoor where Dis = :Dis",
    [':Dis' => $dis]);

//开门次数月趋势图 按年/最近12个月
$openDoorListYear = $openDoorListMonth = [];
if (!empty($openDoorList)) {
    foreach ($openDoorList as $val) {
        $year = date('Y', strtotime($val['DateTime']));
        if (isset($openDoorListYear[$year])) {
            $openDoorListYear[$year]['Num'] += $val['Num'];
        } else {
            $openDoorListYear[$year] = [
                'Dis' => $val['Dis'],
                'DateTime' => $year,
                'Num' => $val['Num']
            ];
        }
        if (in_array($val['DateTime'], $lastMonths)) {
            $openDoorListMonth[] = $val;
        }
    }
    $openDoorListYear = array_values($openDoorListYear);
}

//开门成功类型统计图
$openDoorType = $db->querySList("SELECT (CASE TYPE
            WHEN 0 THEN 'CALL'
            WHEN 1 THEN 'TempKey'
            WHEN 2 THEN 'PrivateKey'
            WHEN 3 THEN 'RFCard'
            WHEN 4 THEN 'FaceRecognition'
            WHEN 100 THEN 'NFC'
            ELSE 'BLE'
        END) as OpenDoorType,sum(Num) as OpenDoorNum 
FROM DisOpenDoorType where Dis = :Dis group by OpenDoorType order by OpenDoorNum desc", [':Dis' => $dis]);

//通话总数
$totalCallNum = $db->querySList("select (t1.num1 + t2.num2) as call_num from 
(select sum(Num) as num1 from DisCall where Dis = :Dis) t1,
(select Num as num2 from DisRealTimeData where DataType = 2  and Dis = :Dis) t2",
    [':Dis' => $dis])[0]['call_num'];

//当天通话统计
$todayCallNum = $db->querySList("select Num from DisRealTimeData where DataType =2 and Dis = :Dis",
    [':Dis' => $dis])[0]['Num'];

//通话次数月趋势图
$callNumList = $db->querySList("select Dis,DateTime,Num from DisCall where Dis = :Dis",
    [':Dis' => $dis]);

//通话次数月趋势图 按年/最近12个月
$callNumListYear = $callNumListMonth = [];
if (!empty($callNumList)) {
    foreach ($callNumList as $val) {
        $year = date('Y', strtotime($val['DateTime']));
        if (isset($callNumListYear[$year])) {
            $callNumListYear[$year]['Num'] += $val['Num'];
        } else {
            $callNumListYear[$year] = [
                'Dis' => $val['Dis'],
                'DateTime' => $year,
                'Num' => $val['Num']
            ];
        }
        if (in_array($val['DateTime'], $lastMonths)) {
            $callNumListMonth[] = $val;
        }
    }
    $callNumListYear = array_values($callNumListYear);
}

//注册设备数
$registerDeviceNum = $db->querySList("select sum(Num) as RegisterDeviceNum from DisRealTimeData where DataType = 9 and Dis = :Dis",
    [':Dis' => $dis])[0]['RegisterDeviceNum'];

//在线设备数
$deviceOnline = $db->querySList("select Num from DisRealTimeData where DataType = 3 and Dis = :Dis",
    [':Dis' => $dis])[0]['Num'];

//设备趋势图
$deviceList = $db->querySList("SELECT O.DateTime,
       sum(O.Num) as online_num,
       sum(R.Num) as register_num
FROM DisOnlineDevice O
LEFT JOIN DisRegisterDevice R ON (R.DateTime = O.DateTime
                                     AND R.Dis = O.Dis) where R.Dis = :Dis group BY O.DateTime", [':Dis' => $dis]);

//设备趋势图 按年/最近12个月
$deviceListYear = $deviceListMonth = [];
if (!empty($deviceList)) {
    foreach ($deviceList as $val) {
        $year = date('Y', strtotime($val['DateTime']));
        if (isset($deviceListYear[$year])) {
            $deviceListYear[$year]['online_num'] += $val['online_num'];
            $deviceListYear[$year]['register_num'] += $val['register_num'];
        } else {
            $deviceListYear[$year] = [
                'DateTime' => $year,
                'online_num' => $val['online_num'],
                'register_num' => $val['register_num'],
            ];
        }
        if (in_array($val['DateTime'], $lastMonths)) {
            $deviceListMonth[] = $val;
        }
    }
    $deviceListYear = array_values($deviceListYear);
}

//当前服务器下所有有统计的代理
$dbRegion = \DataBase::getInstance(config('database'. $region));
$disInfo = $dbRegion->querySList("select Dis,sum(Num) as pro_count from DisProjectSize where Dis not in (select Dis from DisListRemove) group by Dis order by pro_count desc limit 20;");
$disList = array_column($disInfo, 'Dis');
$extraDisInfo = $dbRegion->querySList('select Dis from DisList');
$extraDisList = array_column($extraDisInfo, 'Dis');
$disList = array_values(array_unique(array_merge($disList, $extraDisList)));


$data = [
    'TotalProjects' => $totalProjects, //项目数
    'UserNum' => $userNum, //用户数
    "MonthFeeUserNum" => $monthFeeUserNum, //月租用户数
    'InstallerProjects' => $installerProjects, //Installer项目分部图
    "FamilyAndOFficeNum" => $familyAndOfficeNum, //家庭/办公用户数月趋势图
    "FamilyAndOFficeNumYear" => $familyAndOfficeNumYear, //家庭/办公用户数月趋势图 按年
    "FamilyAndOFficeNumMonth" => $familyAndOfficeNumMonth, //家庭/办公用户数月趋势图 最近12个月
    "MonthFeeUserMonthNum" => $monthFeeUserMonthNum, //月租用户数月趋势图
    "MonthFeeUserMonthNumYear" => $monthFeeUserMonthNumYear, //月租用户数月趋势图 按年
    "MonthFeeUserMonthNumMonth" => $monthFeeUserMonthNumMonth, //月租用户数月趋势图 最近12个月
    "TotalOpenDoorNum" => $totalOpenDoor, //总开门次数
    "TodayOpenDoorNum" => $todayOpenDoor, //今日开门次数
    "OpenDoorNumList" => $openDoorList, //开门次数月趋势图
    "OpenDoorNumListYear" => $openDoorListYear, //开门次数月趋势图 按年
    "OpenDoorNumListMonth" => $openDoorListMonth, //开门次数月趋势图 最近12个月
    "OpenDoorSuccessType" => $openDoorType, //开门成功类型统计图
    "TotalCallNum" => $totalCallNum, //总通话次数
    "TodayCallNum" => $todayCallNum, //今日通话次数
    "CallNumList" => $callNumList, //通话次数月趋势图
    "CallNumListYear" => $callNumListYear, //通话次数月趋势图 按年
    "CallNumListMonth" => $callNumListMonth, //通话次数月趋势图 最近12个月
    "RegisteredDeviceNum" => $registerDeviceNum, //注册设备数
    "DeviceOnline" => $deviceOnline, //在线设备数
    "DeviceList" => $deviceList, //设备趋势图
    "DeviceListYear" => $deviceListYear, //设备趋势图 按年
    "DeviceListMonth" => $deviceListMonth, //设备趋势图 最近12个月
    "disList" => $disList, //当前服务器下所有代理
];

if (!empty($token)) {
    unset($data['disList']);
}
returnJson(0, 'Successful', $data);
