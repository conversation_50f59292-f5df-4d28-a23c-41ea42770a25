<?php
/**
 * @description 获取通用配置
 * <AUTHOR>
 * @date 2022/5/11 10:30
 * @version V6.4
 * @lastEditor csc
 * @lastEditTime 2022/5/11 10:30
 * @lastVersion V6.4
 */

include_once "../src/global.php";

global $tokenInfo, $gApp;

$config = ['token' => '', 'admin' => ''];
$config['token'] = isset($tokenInfo['Config']) ? $tokenInfo['Config'] : ["Title" => ""];
if (!empty($gApp['admin'])) {
    $db = \DataBase::getInstance(config('databaseAccount'));
    $configData = $db->querySList("select ConfigItem, ConfigVal from AdminConfig where AdminID = :AdminID",
        [':AdminID' => $gApp['admin']['ID']]);
    foreach ($configData as $val) {
        $config['admin'][$val['ConfigItem']] = $val['ConfigVal'];
    }
}

$config['serverList'] = config('serverList');

returnJson(0, '', $config);
