<?php
/**
 * @description 获取用户列表
 * <AUTHOR>
 * @date 2022/5/10 17:06
 * @version V6.4
 * @lastEditor csc
 * @lastEditTime 2022/5/10 17:06
 * @lastVersion V6.4
 */

include_once "../src/global.php";

global $gApp;

$search = trim(getParams('Search'));
$sort = getParams('Sort');
$page = getParams('page', 1);
$row = getParams('row', 10);
$offset = ($page - 1) * $row;

$order = 'ID DESC';
if ($sort == 1) {
    $order = 'Nickname ASC';
} elseif ($sort == 2) {
    $order = 'Nickname DESC';
}

$db = \DataBase::getInstance(config('databaseAccount'));

$userInfo = $db->querySList("select ID,Email,Nickname,Level as `Group` from Admin where Email like :Search or Nickname like :Search order by $order limit $offset, $row",
    [':Search' => "%$search%"]);
$total = $db->querySList("select count(ID) as total from Admin where Email like :Search or Nickname like :Search",
    [':Search' => "%$search%"])[0]['total'];
returnJson(0, 'Successful', ['total' => $total, 'row' => $userInfo]);