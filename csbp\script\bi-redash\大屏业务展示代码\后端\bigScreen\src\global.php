<?php
/**
 * @description 通用类，除了login、找回密码等无登录态之外的接口都需引用
 * <AUTHOR>
 * @date 2022/5/10 15:30
 * @version V6.4
 * @lastEditor csc
 * @lastEditTime 2022/5/10 15:30
 * @lastVersion V6.4
 */

include_once "../config/base.php";
include_once "../config/database.php";
include_once "../config/func.php";

global $gApp;
$authToken = isset($_SERVER['HTTP_X_AUTH_TOKEN']) ? strval($_SERVER['HTTP_X_AUTH_TOKEN']) : '';

if (!empty($authToken)) {
    $db = \DataBase::getInstance(config('databaseAccount'));
    $adminInfo = $db->querySList("select A.* from AdminToken AT left join Admin A on A.ID = AT.AdminID where AT.Token = :Token and AT.TokenEt > :TokenEt",
        [':Token' => $authToken, ':TokenEt' => time()]);
    if (!empty($adminInfo)) {
        $gApp['admin'] = $adminInfo[0];
    }
}

//校验登录状态
global $tokenInfo, $gFile;
$tokenInfo = [];
$token = getParams('Token'); //只有查询的界面支持token查询，如果是post请求相关接口，需要自己加上验证并写死$_GET['token']来获取
if (!empty($token)) {
    $db = \DataBase::getInstance(config('databaseAccount'));
    $tokenInfo = $db->querySList("select * from Token where Token = :Token",
        [':Token' => $token]);
    if (empty($tokenInfo)) {
        returnJson(1001, 'The token is invalid. Please make sure the link is correct');
    }
    $tokenInfo = $tokenInfo[0];
    //分享链接只能查看允许的页面
    if (empty($gApp['admin']) && !in_array($gFile, ['getAkcsData','getAkcsRegionData','getAkcsRegionDisData','getConfig'])) {
        returnJson(1001, 'You do not have permission to access this page');
    }

    $config = json_decode($tokenInfo['Config'], true);
    if (empty($config) or !is_array($config)) {
        returnJson(1001, 'The link has expired, please contact the administrator');
    }

    //校验路由和参数
    if ($gFile != 'getConfig' && strtolower($gFile) != strtolower($config['Uri'])) {
        returnJson(1001, 'The token is invalid. Please make sure the link is correct');
    }

    $tokenRegion = getParams('Region', '');
    $tokenDis = getParams('Dis', '');

    if (!empty($tokenRegion) && trim($tokenRegion) != trim($config['Region'])) {
        returnJson(1001, 'The token is invalid. Please make sure the link is correct');
    }

    if (!empty($tokenDis) && trim($tokenDis) != trim($config['Dis'])) {
        returnJson(1001, 'The token is invalid. Please make sure the link is correct');
    }
    $tokenInfo['Config'] = [
        'Title' => $config['Title']
    ];
}

if (empty($gApp['admin']) && empty($tokenInfo)) {
    if (!empty($authToken)) {
        returnJson(1001, 'Login status has expired, please login again');
    }

    returnJson(1001, 'Please log in first');
}
