<?php
/**
 * @description 登陆类
 * <AUTHOR>
 * @date 2022/5/10 16:13
 * @version V6.4
 * @lastEditor csc
 * @lastEditTime 2022/5/10 16:13
 * @lastVersion V6.4
 */

include_once "../config/base.php";
include_once "../config/database.php";
include_once "../config/func.php";

checkPost(); //必须为post请求
$account = getParams('Account');
$password = getParams('Password');
$password = getEncryptPasswd($account, $password);

$db = \DataBase::getInstance(config('databaseAccount'));
$admin = $db->querySList("select * from Admin where Account = :Account and Password = :Password",
    [':Account' => $account, ':Password' => $password]);
if (empty($admin)) {
    returnJson(1, 'Incorrect account or password');
}
$admin = $admin[0];
if ($admin['Status'] == 1) {
    returnJson(1, 'This account is not allowed to log in');
}

//保存token
$token = randString(18).$admin['ID'];
$tokenEt = time() + 24 * 3600;
$db->execSql("INSERT INTO  AdminToken(`AdminID`,`Token`,`TokenEt`) VALUES (:AdminID, :Token, :TokenEt) ON DUPLICATE KEY UPDATE Token = :Token, TokenEt = :TokenEt",
    [':AdminID' => $admin['ID'], ':Token' => $token, ':TokenEt' => $tokenEt]);

returnJson(0, 'Login successful', ['authToken' => $token, 'Group' => $admin['Level'], 'Nickname' => $admin['Nickname'], 'Email' => $admin['Email']]);