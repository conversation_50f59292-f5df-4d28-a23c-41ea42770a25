<?php
/**
 * @description 重置密码
 * <AUTHOR>
 * @date 2022/5/10 16:13
 * @version V6.4
 * @lastEditor csc
 * @lastEditTime 2022/5/10 16:13
 * @lastVersion V6.4
 */

include_once "../config/base.php";
include_once "../config/database.php";
include_once "../config/func.php";

checkPost(); //必须为post请求
$token = getParams('Token');
$newPassword = getParams('NewPassword');

$db = \DataBase::getInstance(config('databaseAccount'));
$admin = $db->querySList("select A.* from EmailToken ET left join Admin A on A.ID = ET.AdminID where ET.Token = :Token and ET.IsUsed = 0 and ET.TokenEt > :TokenEt",
    [':Token' => $token, ':TokenEt' => time()]);
if (empty($admin)) {
    returnJson(1, 'Token has expired');
}

if (empty($newPassword)) {
    returnJson(1, 'New password cannot be empty');
}

$admin = $admin[0];
$newPassword = getEncryptPasswd($admin['Account'], $newPassword);

if ($admin['Password'] == $newPassword) {
    returnJson(1, 'The new password cannot be the same as the original password');
}

$count = $db->update2ListWID('Admin', [':ID' => $admin['ID'], ':Password' => $newPassword]);
if ($count) {
    $db->update2ListWKey('EmailToken', [':Token' => $token, ':IsUsed' => 1], 'Token');
    returnJson(0, 'Modified successfully');
}


returnJson(1, 'Modified failed');
