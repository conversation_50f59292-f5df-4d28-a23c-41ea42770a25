<?php
/**
 * @description 重置密码邮件
 * <AUTHOR>
 * @date 2022/5/10 16:13
 * @version V6.4
 * @lastEditor csc
 * @lastEditTime 2022/5/10 16:13
 * @lastVersion V6.4
 */

include_once "../config/base.php";
include_once "../config/database.php";
include_once "../config/func.php";

checkPost(); //必须为post请求
$email = getParams('Email');

$db = \DataBase::getInstance(config('databaseAccount'));
$admin = $db->querySList("select * from Admin where Account = :Account",
    [':Account' => $email]);
if (empty($admin)) {
    returnJson(1, 'Email does not exist');
}

$admin = $admin[0];

//创建一个有效期3h的token
$token = randString(20).$admin['ID'];
$tokenEt = time() + 3 * 3600;

$url = config('resetPwdUrl') . '?Token=' . $token;
//每次重置相同账号，把之前生成的该账号的Token设为不可用
$db->update2ListWKey('EmailToken', [':AdminID' => $admin['ID'], ':IsUsed' => 1], 'AdminID');
$id = $db->insert2List('EmailToken', [':AdminID' => $admin['ID'], ':Token' => $token, ':TokenEt' => $tokenEt]);

if ($id) {
    if (function_exists('fastcgi_finish_request')) {
        $res = [
            'code' => 0,
            'msg' => 'Sent successfully',
            'data' => [],
        ];
        printf(json_encode($res));
        fastcgi_finish_request();
        sendResetPwdEMail($url, $admin['Email'], $admin['Nickname']);
        exit(0);
    } else {
        sendResetPwdEMail($url, $admin['Email'], $admin['Nickname']);
        returnJson(0, 'Sent successfully');
    }
}

returnJson(1, 'fail in send');
