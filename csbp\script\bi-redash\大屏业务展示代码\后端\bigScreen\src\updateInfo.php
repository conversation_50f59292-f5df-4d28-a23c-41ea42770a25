<?php
/**
 * @description 修改个人信息
 * <AUTHOR>
 * @date 2022/5/10 17:06
 * @version V6.4
 * @lastEditor csc
 * @lastEditTime 2022/5/10 17:06
 * @lastVersion V6.4
 */

include_once "../src/global.php";

global $gApp;

checkPost(); //必须为post请求

$username = trim(getParams('Username'));

if (empty($username)) {
    returnJson(1, 'User name cannot be empty');
}

if ($username == $gApp['admin']['Nickname']) {
    returnJson(1, 'User name not modified');
}

$db = \DataBase::getInstance(config('databaseAccount'));
$count = $db->update2ListWID('Admin', [':ID' => $gApp['admin']['ID'], ':Nickname' => $username]);
if ($count) {
    returnJson(0, 'Modified successfully');
}

returnJson(1, 'Modification failed');
