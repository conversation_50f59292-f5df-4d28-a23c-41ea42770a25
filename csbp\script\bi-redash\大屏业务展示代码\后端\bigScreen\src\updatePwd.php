<?php
/**
 * @description 修改密码
 * <AUTHOR>
 * @date 2022/5/10 17:06
 * @version V6.4
 * @lastEditor csc
 * @lastEditTime 2022/5/10 17:06
 * @lastVersion V6.4
 */

include_once "../src/global.php";

global $gApp;

checkPost(); //必须为post请求

$password = getParams('Password');
$newPassword = getParams('NewPassword');

$ecPassword = getEncryptPasswd($gApp['admin']['Account'], $password);
if ($ecPassword != $gApp['admin']['Password']) {
    returnJson(1, 'Old password incorrect');
}

if (empty($newPassword)) {
    returnJson(1, 'New password cannot be empty');
}

if ($password == $newPassword) {
    returnJson(1, 'Old and new passwords cannot be the same');
}

$newPassword = getEncryptPasswd($gApp['admin']['Account'], $newPassword);

$db = \DataBase::getInstance(config('databaseAccount'));
$count = $db->update2ListWID('Admin', [':ID' => $gApp['admin']['ID'], ':Password' => $newPassword]);
if ($count) {
    returnJson(0, 'Modified successfully');
}

returnJson(1, 'Modification failed');
