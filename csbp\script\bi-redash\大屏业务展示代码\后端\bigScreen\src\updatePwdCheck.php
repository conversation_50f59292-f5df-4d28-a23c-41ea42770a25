<?php
/**
 * @description 修改密码前置校验当前密码
 * <AUTHOR>
 * @date 2022/5/10 17:06
 * @version V6.4
 * @lastEditor csc
 * @lastEditTime 2022/5/10 17:06
 * @lastVersion V6.4
 */

include_once "../src/global.php";

global $gApp;

checkPost(); //必须为post请求

$password = getParams('Password');

$ecPassword = getEncryptPasswd($gApp['admin']['Account'], $password);
if ($ecPassword != $gApp['admin']['Password']) {
    returnJson(1, 'Incorrect password');
}

returnJson(0, 'Verification successful');