<?php
date_default_timezone_set('PRC');

require('/home/<USER>/akcs_dw_config.php');
//区域
$REGION = null;
if ($argc == 2) {
    $REGION = $argv[1];
} else {
    echo 'use: akcs_dw_month.php  usa';
    exit;
}

//查询不需要统计的dis
$dis_remove_top_list = [];
$ods_db = null;
$dw_server_db = null;
if($REGION == 'USA')
{
    $dw_server_db = getUSADWDB();
}
else if($REGION == 'EUR')
{
    $dw_server_db = getEURDWDB();
}
else if($REGION == 'ASIA')
{
    $dw_server_db = getASIADWDB();
}
else if($REGION == 'JPN')
{
    $dw_server_db = getJPNDWDB();
}
else if($REGION == 'LOCAL')
{
    $dw_server_db = getLOCALDWDB();
}
if (null !== $dw_server_db) {
    $ods_db = getODSDB();
    $sth_dis = $dw_server_db->prepare("select Dis from DisListRemove;");
    $sth_dis->execute();
    $dis_list = $sth_dis->fetchALL(PDO::FETCH_ASSOC);
    foreach ($dis_list as $row => $dis)
    {
        $dis_acc = $dis['Dis'];
        $sth = $ods_db->prepare("select ID from Account where Account = :dis and Grade = 11");
        $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
        $sth->execute();
        $dis_id = $sth->fetch(PDO::FETCH_ASSOC)['ID'];//fetch 不是 fetchALL
        if (empty($dis_id)) {
            continue;
        }
        $dis_remove_top_list[$dis_acc] = $dis_id;
    }
}

//当月新增呼叫数
function CallNum($REGION)
{
    //从业务数据库里面查询数据
    $ods_db = getODSDB();
    $dw_db = getDWDB();

    global $year_month;

    $yearmonth = date("Ym", strtotime($year_month));
    $ym_table = "CallHistory_".$yearmonth;


    $sth = $ods_db->prepare("show tables like '{$ym_table}'");
    $sth->execute();
    $tableRes = $sth->fetch(PDO::FETCH_ASSOC);
    if (empty($tableRes)) {
        return;
    }

    $sth = $ods_db->prepare("select count(1) as num From " .$ym_table);
    $sth->execute();
    $call_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

    $sth = $dw_db->prepare("INSERT INTO  GlobalCall(`Region`,`DateTime`,`Num`) VALUES (:region, :time, :call_num) ON DUPLICATE KEY UPDATE Num = :call_num");
    $sth->bindParam(':call_num', $call_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
    $sth->execute();
}
//当月新增开门数，每日统计一次
function DoorNum($REGION)
{
    //从业务数据库里面查询数据
    $ods_db = getODSDB();
    $dw_db = getDWDB();

    global $year_month;

    $yearmonth = date("Ym", strtotime($year_month));
    $ym_table = "PersonalCapture_".$yearmonth;

    $sth = $ods_db->prepare("show tables like '{$ym_table}'");
    $sth->execute();
    $tableRes = $sth->fetch(PDO::FETCH_ASSOC);
    if (empty($tableRes)) {
        return;
    }

    $sth = $ods_db->prepare("select count(1) as num From " . $ym_table." where  (CaptureType = 0 or CaptureType = 1 or CaptureType = 2 or CaptureType = 3 or CaptureType = 4 or CaptureType = 100 or CaptureType = 101);");
    $sth->execute();
    $door_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

    $sth = $dw_db->prepare("INSERT INTO  GlobalOpenDoor(`Region`,`DateTime`,`Num`) VALUES (:region, :time, :door_num) ON DUPLICATE KEY UPDATE Num = :door_num");
    $sth->bindParam(':door_num', $door_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
    $sth->execute();
}

//每月新增激活家庭数
function ActiveFamilyNum($REGION)
{
    //从业务数据库里面查询数据
    $ods_db = getODSDB();
    $dw_db = getDWDB();

    global $year_month;
    $timestart = date("Y-m-01 00:00:00", strtotime($year_month));//本月第一天
    $timeend = date("Y-m-t 23:59:59", strtotime($year_month));//本月最后一天

    $sth = $ods_db->prepare("select count(1) as num from PersonalAccount where (ActiveTime between '".$timestart."' and '".$timeend."') and (Role = 10 or Role = 20) and Active = 1 and Special = 0;");
    $sth->execute();
    $active_family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

    $sth = $dw_db->prepare("INSERT INTO  GlobalActiveFamily(`Region`,`DateTime`,`Num`) VALUES (:region, :time, :active_family_num) ON DUPLICATE KEY UPDATE Num = :active_family_num");
    $sth->bindParam(':active_family_num', $active_family_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
    $sth->execute();
}

//每月新增收费
function ServiceChargeNum($REGION)
{
    //从业务数据库里面查询数据
    $ods_db = getODSDB();
    $dw_db = getDWDB();

    global $year_month;
    $timestart = date("Y-m-01 00:00:00", strtotime($year_month));//本月第一天
    $timeend = date("Y-m-t 23:59:59", strtotime($year_month));//本月最后一天

    $sth = $ods_db->prepare("select sum(TotalPrice*Discount/10000) as num from OrderList where (CreateTime between '".$timestart."' and '".$timeend."') and Status = 1 and (TotalPrice > 0 and Discount != 0)");
    $sth->execute();
    $service_charge_num = $sth->fetch(PDO::FETCH_ASSOC)['num'] ? 0 : $sth->fetch(PDO::FETCH_ASSOC)['num'];
    $sth = $dw_db->prepare("INSERT INTO  GlobalServiceCharge(`Region`,`DateTime`,`Num`) VALUES (:region, :time, :service_charge_num) ON DUPLICATE KEY UPDATE Num = :service_charge_num");
    $sth->bindParam(':service_charge_num', $service_charge_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
    $sth->execute();
}

//每月新增激活app
function ActiveAppNum($REGION)
{
    //从业务数据库里面查询数据
    $ods_db = getODSDB();
    $dw_db = getDWDB();

    global $year_month;
    $timestart = date("Y-m-01 00:00:00", strtotime($year_month));//本月第一天
    $timeend = date("Y-m-t 23:59:59", strtotime($year_month));//本月最后一天

    $sth_act_family = $ods_db->prepare("select count(1) as count from PersonalAccount where (ActiveTime between '".$timestart."' and '".$timeend."') and Active = 1;");

    $sth_act_family->execute();
    $resultRole = $sth_act_family->fetch(PDO::FETCH_ASSOC);
    $family_active_num = $resultRole['count'];

    //UNIQUE KEY `region_data` (`Region`,`DateTime`)  对应的表格已经通过唯一键来保证只会插入一次，后续的都是更新
    $sth = $dw_db->prepare("INSERT INTO  GlobalActiveApp(`Region`,`DateTime`,`Num`) VALUES (:region, :time, :family_active_num) ON DUPLICATE KEY UPDATE Num = :family_active_num");
    $sth->bindParam(':family_active_num', $family_active_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
    $sth->execute();
}

function OnlineDeviceNum($REGION)
{
    //从业务数据库里面查询数据
    $ods_db = getODSDB();
    $dw_db = getDWDB();

    global $year_month;
    $timestart = date("Y-m-01 00:00:00", strtotime($year_month));//本月第一天
    $timeend = date("Y-m-t 23:59:59", strtotime($year_month));//本月最后一天

    $sth = $ods_db->prepare("select count(1) as num From Devices where CreateTime between :time_start and :time_end and Status = 1;");
    $sth->bindParam(':time_start', $timestart, PDO::PARAM_INT);
    $sth->bindParam(':time_end', $timeend, PDO::PARAM_STR);
    $sth->execute();
    $register_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

    $sth = $ods_db->prepare("select count(1) as num From PersonalDevices where CreateTime between :time_start and :time_end and Status = 1;");
    $sth->bindParam(':time_start', $timestart, PDO::PARAM_INT);
    $sth->bindParam(':time_end', $timeend, PDO::PARAM_STR);
    $sth->execute();
    $register_per_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

    $register_num = $register_devices_num + $register_per_devices_num;

    $sth = $dw_db->prepare("INSERT INTO  GlobalOnlineDevice(`Region`,`DateTime`,`Num`) VALUES (:region, :time, :register_num) ON DUPLICATE KEY UPDATE Num = :register_num");
    $sth->bindParam(':register_num', $register_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':time', $year_month, PDO::PARAM_INT);
    $sth->execute();
}

function RegisterDeviceNum($REGION)
{
    //从业务数据库里面查询数据
    $ods_db = getODSDB();
    $dw_db = getDWDB();

    global $year_month;
    $timestart = date("Y-m-01 00:00:00", strtotime($year_month));//本月第一天
    $timeend = date("Y-m-t 23:59:59", strtotime($year_month));//本月最后一天

    $sth = $ods_db->prepare("select count(1) as num From Devices where CreateTime between :time_start and :time_end;");
    $sth->bindParam(':time_start', $timestart, PDO::PARAM_INT);
    $sth->bindParam(':time_end', $timeend, PDO::PARAM_STR);
    $sth->execute();
    $register_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

    $sth = $ods_db->prepare("select count(1) as num From PersonalDevices where CreateTime between :time_start and :time_end;");
    $sth->bindParam(':time_start', $timestart, PDO::PARAM_INT);
    $sth->bindParam(':time_end', $timeend, PDO::PARAM_STR);
    $sth->execute();
    $register_per_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

    $register_num = $register_devices_num + $register_per_devices_num;

    $sth = $dw_db->prepare("INSERT INTO  GlobalRegisterDevice(`Region`,`DateTime`,`Num`) VALUES (:region, :time, :register_num) ON DUPLICATE KEY UPDATE Num = :register_num");
    $sth->bindParam(':register_num', $register_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':time', $year_month, PDO::PARAM_INT);
    $sth->execute();
}
//每月新增激活家庭数
function RtspNum($REGION)
{
    //从业务数据库里面查询数据
    $ods_db = getODSDB();
    $dw_db = getDWDB();

    global $year_month;
    $timestart = date("Y-m-01 00:00:00", strtotime($year_month));//本月第一天
    $timeend = date("Y-m-t 23:59:59", strtotime($year_month));//本月最后一天

    $sth = $ods_db->prepare("select count(1) as num From AppManualRtsp where (CreateTime between :time_start and :time_end);");
    $sth->bindParam(':time_start', $timestart, PDO::PARAM_STR);
    $sth->bindParam(':time_end', $timeend, PDO::PARAM_STR);
    $sth->execute();
    $rtsp_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

    $sth = $dw_db->prepare("INSERT INTO  GlobalRtsp(`Region`,`DateTime`,`Num`) VALUES (:region, :time, :rtsp_num) ON DUPLICATE KEY UPDATE Num = :rtsp_num");
    $sth->bindParam(':rtsp_num', $rtsp_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
    $sth->execute();
}

//每月新增月租家庭数
function FeeFamilyNum($REGION)
{
    //从业务数据库里面查询数据
    $ods_db = getODSDB();
    $dw_db = getDWDB();

    global $year_month;
    $timestart = date("Y-m-01 00:00:00", strtotime($year_month));//本月第一天
    $timeend = date("Y-m-t 23:59:59", strtotime($year_month));//本月最后一天

    $sth = $ods_db->prepare("select count(1) as num from PersonalAccount where (ActiveTime between '".$timestart."' and '".$timeend."') and (Role = 10 or Role = 20) and Active = 1 and Special = 0 and ExpireTime < '2029-01-01 00:00:00';");
    $sth->execute();
    $active_family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];


    $sth = $dw_db->prepare("INSERT INTO GlobalFeeFamily(`Region`,`DateTime`,`Num`) VALUES (:region, :time, :active_family_num) ON DUPLICATE KEY UPDATE Num = :active_family_num");
    $sth->bindParam(':active_family_num', $active_family_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
    $sth->execute();
}

//每月新增办公用户数
function OfficerNum($REGION)
{
    //从业务数据库里面查询数据
    $ods_db = getODSDB();
    $dw_db = getDWDB();

    global $year_month;
    $timestart = date("Y-m-01 00:00:00", strtotime($year_month));//本月第一天
    $timeend = date("Y-m-t 23:59:59", strtotime($year_month));//本月最后一天

    $sth = $ods_db->prepare("select count(1) as num from PersonalAccount where (ActiveTime between '".$timestart."' and '".$timeend."') and (Role = 30 or Role = 31) and Active = 1;");
    $sth->execute();
    $active_family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];


    $sth = $dw_db->prepare("INSERT INTO GlobalOffice(`Region`,`DateTime`,`Num`) VALUES (:region, :time, :active_family_num) ON DUPLICATE KEY UPDATE Num = :active_family_num");
    $sth->bindParam(':active_family_num', $active_family_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
    $sth->execute();
}

$startMonth = '2019-01';
$endMonth = '2022-06';
$months = [];
while (strtotime($startMonth) <= strtotime($endMonth)) {
    $months[] = $startMonth;
    $startMonth = date('Y-m', (strtotime('+1 month',strtotime($startMonth))));
}

foreach ($months as $year_month) {
    CallNum($REGION);
    DoorNum($REGION);
    ActiveFamilyNum($REGION);
    ActiveAppNum($REGION);
    OnlineDeviceNum($REGION);
    RegisterDeviceNum($REGION);
    RtspNum($REGION);
    FeeFamilyNum($REGION);
    OfficerNum($REGION);
}

?>
