<?php
/**
 * 新增自定义dis或者已有dis重新获取数据
 */

date_default_timezone_set('PRC');
require('/home/<USER>/akcs_dw_config.php');
//区域
$REGION = null;
if ($argc == 2) {
    $REGION = $argv[1];
} else {
    echo 'use: akcs_dw_month.php  usa/eur/asia';
    exit;
}
 $dis_top_list = null;
 $dw_db = null;
 $ods_db = null;
if($REGION == 'USA')
{
    $dw_db = getUSADWDB();
}
else if($REGION == 'EUR')
{
    $dw_db = getEURDWDB();
}
else if($REGION == 'ASIA')
{
    $dw_db = getASIADWDB();
}
else if($REGION == 'JPN')
{
    $dw_db = getJPNDWDB();
}
else if($REGION == 'LOCAL')
{
    $dw_db = getLOCALDWDB();    
}
else if($REGION == 'INDIA')
{
    $dw_db = getINDIADWDB();
}
else if($REGION == 'RU')
{
    $dw_db = getRUDWDB();
}
else if($REGION == 'INC')
{
    $dw_db = getINCDWDB();
}

$ods_db = getODSDB();

//补数据时需要修改开始结束时间
$year_start = '2023-02';
$year_end = '2023-07';
$year_months = MonthDataPad($year_start, $year_end);

//查询最大项目数量的dis以及自定义的dis
$sth_dis = $dw_db->prepare("select Dis,sum(Num) as pro_count from DisProjectSize where Dis not in (select Dis from DisListRemove) group by Dis order by pro_count desc limit 20;");
$sth_dis->execute();
$dis_list = $sth_dis->fetchALL(PDO::FETCH_ASSOC);
$sth_dis = $dw_db->prepare("select Dis from DisList;");
$sth_dis->execute();
$extra_dis_list = $sth_dis->fetchALL(PDO::FETCH_ASSOC);

$dis_list = array_merge($dis_list, $extra_dis_list);

//指定只更新某些新增加的dis
//$dis_list = $dw_db->prepare("select Dis from DisList where CreateTime >= '2022-07-01 00:00:00';");
//$sth_dis->execute();
//$dis_list = $sth_dis->fetchALL(PDO::FETCH_ASSOC);

foreach ($dis_list as $row => $dis)
{
    $dis_acc = $dis['Dis'];
    if (isset($dis_top_list[$dis_acc])) {
        continue;
    }
    $sth = $ods_db->prepare("select ID from Account where Account = :dis and Grade = 11");
    $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
    $sth->execute();
    $dis_id = $sth->fetch(PDO::FETCH_ASSOC)['ID'];//fetch 不是 fetchALL
    $dis_top_list[$dis_acc] = $dis_id;
}

//开门次数
function DisOpenDoorNum()
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;
    global $year_months;
    $table_name = 'PersonalCapture';

    foreach ($year_months as $year_month)
    {
        $ym_table = $table_name."_".date('Ym', strtotime($year_month));
        $sth = $ods_db->prepare("show tables like '{$ym_table}'");
        $sth->execute();
        $tableRes = $sth->fetch(PDO::FETCH_ASSOC);
        if (empty($tableRes)) {
            continue;
        }

        foreach ($dis_top_list as $dis_acc => $dis_id)
        {
            $sth = $ods_db->prepare("select count(*) as num from " .$ym_table. " C left join Account A on A.ID = C.MngAccountID left join Account B on B.ID = A.ParentID where B.ID = :id and C.CaptureType < 102");
            $sth->bindParam(':id', $dis_id, PDO::PARAM_INT);
            $sth->execute();
            $opendoor_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

            $sth = $dw_db->prepare("INSERT INTO  DisOpenDoor(`Dis`,`DateTime`,`Num`) VALUES (:dis, :time, :opendoor_num) ON DUPLICATE KEY UPDATE Num = :opendoor_num");
            $sth->bindParam(':opendoor_num', $opendoor_num, PDO::PARAM_INT);
            $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
            $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
            $sth->execute();
        }
    }
}

//通话次数
function DisCallNum()
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;
    global $year_months;
    $table_name = 'CallHistory';

    foreach ($year_months as $year_month) {
        $ym_table = $table_name."_".date('Ym', strtotime($year_month));
        $sth = $ods_db->prepare("show tables like '{$ym_table}'");
        $sth->execute();
        $tableRes = $sth->fetch(PDO::FETCH_ASSOC);
        if (empty($tableRes)) {
            continue;
        }

        foreach ($dis_top_list as $dis_acc => $dis_id)
        {
            if($ym_table == 'CallHistory_201909')
            {
                $sth = $ods_db->prepare("select count(*) as num from " .$ym_table. " C left join Account A on A.ID = C.MngAccountID left join Account B on B.ID = A.ParentID where B.ID = :id and C.StartTime > '2019-09-01 00:00:00'");
            }
            else
            {
                $sth = $ods_db->prepare("select count(*) as num from " .$ym_table. " C left join Account A on A.ID = C.MngAccountID left join Account B on B.ID = A.ParentID where B.ID = :id");
            }
            $sth->bindParam(':id', $dis_id, PDO::PARAM_INT);
            $sth->execute();
            $call_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

            $sth = $dw_db->prepare("INSERT INTO  DisCall(`Dis`,`DateTime`,`Num`) VALUES (:dis, :time, :call_num) ON DUPLICATE KEY UPDATE Num = :call_num");
            $sth->bindParam(':call_num', $call_num, PDO::PARAM_INT);
            $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
            $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
            $sth->execute();
        }
    }
}

//每月新增激活家庭数
function DisActiveFamilyNum()
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;
    global $year_months;

    foreach ($year_months as $year_month)
    {
        $timestart = $year_month .'-01 00:00:00';
        $timestart_t = strtotime($timestart);//转成时间戳
        $timeend_t = strtotime("first day of +1 month",$timestart_t);//不能用 + 1 months的形式
        $timeend = date("Y-m-d 00:00:00", $timeend_t);

        foreach ($dis_top_list as $dis_acc => $dis_id)
        {
            $sth_act_family = $ods_db->prepare("select count(1) as count from PersonalAccount P left join Account A on A.ID = P.ParentID left join Account B on B.ID = A. ParentID where (B.ID = :id) and (P.ActiveTime between '".$timestart."' and '".$timeend."') and (P.Role = 10 or P.Role = 20) and P.Active = 1;");
            $sth_act_family->bindParam(':id', $dis_id, PDO::PARAM_INT);
            $sth_act_family->execute();
            $resultRole = $sth_act_family->fetch(PDO::FETCH_ASSOC);
            $family_active_num = $resultRole['count'];

            //added by chenyc,2021.12.17,统计这些激活的家庭中，需要收取月租的账号有多少,以过期时间来判断是否需要收费
            $sth_expire_family = $ods_db->prepare("select count(1) as count from PersonalAccount P left join Account A on A.ID = P.ParentID left join Account B on B.ID = A. ParentID where (B.ID = :id) and (P.ActiveTime between '".$timestart."' and '".$timeend."') and (P.ExpireTime < '2029-01-01 00:00:00') and (P.Role = 10 or P.Role = 20) and P.Active = 1;");
            $sth_expire_family->bindParam(':id', $dis_id, PDO::PARAM_INT);
            $sth_expire_family->execute();
            $resultRole = $sth_expire_family->fetch(PDO::FETCH_ASSOC);
            $family_expire_num = $resultRole['count'];

            $sth = $dw_db->prepare("INSERT INTO  DisActiveFamily(`Dis`,`DateTime`,`Num`, `ExpireNum`) VALUES (:dis, :time, :family_active_num, :family_expire_num) ON DUPLICATE KEY UPDATE Num = :family_active_num, ExpireNum = :family_expire_num");
            $sth->bindParam(':family_active_num', $family_active_num, PDO::PARAM_INT);
            $sth->bindParam(':family_expire_num', $family_expire_num, PDO::PARAM_INT);
            $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
            $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
            $sth->execute();
        }
    }

}

//每月新增月租家庭数
function DisFeeFamilyNum()
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;
    global $year_months;

    foreach ($year_months as $year_month)
    {
        $timestart = $year_month .'-01 00:00:00';
        $timestart_t = strtotime($timestart);//转成时间戳
        $timeend_t = strtotime("first day of +1 month",$timestart_t);//不能用 + 1 months的形式
        $timeend = date("Y-m-d 00:00:00", $timeend_t);

        foreach ($dis_top_list as $dis_acc => $dis_id)
        {
            $sth = $ods_db->prepare("select count(1) as num from PersonalAccount P left join Account A on A.ID = P.ParentID left join Account B on B.ID = A. ParentID where (B.ID = :id) and (P.ActiveTime between '".$timestart."' and '".$timeend."') and (P.Role = 10 or P.Role = 20) and P.Active = 1 and P.Special = 0 and P.ExpireTime < '2029-01-01 00:00:00';");
            $sth->bindParam(':id', $dis_id, PDO::PARAM_INT);
            $sth->execute();
            $active_family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
            $sth = $dw_db->prepare("INSERT INTO DisFeeFamily(`Dis`,`DateTime`,`Num`) VALUES (:dis, :time, :active_family_num) ON DUPLICATE KEY UPDATE Num = :active_family_num");
            $sth->bindParam(':active_family_num', $active_family_num, PDO::PARAM_INT);
            $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
            $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
            $sth->execute();
        }
    }
}

//每月新增办公用户数
function DisOfficerNum()
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;
    global $year_months;

    foreach ($year_months as $year_month)
    {
        $timestart = $year_month . '-01 00:00:00';
        $timestart_t = strtotime($timestart);//转成时间戳
        $timeend_t = strtotime("first day of +1 month", $timestart_t);//不能用 + 1 months的形式
        $timeend = date("Y-m-d 00:00:00", $timeend_t);

        foreach ($dis_top_list as $dis_acc => $dis_id)
        {
            $sth = $ods_db->prepare("select count(1) as num from PersonalAccount P left join Account A on A.ID = P.ParentID left join Account B on B.ID = A. ParentID where (B.ID = :id) and (P.ActiveTime between '".$timestart."' and '".$timeend."') and (P.Role = 30 or P.Role = 31) and P.Active = 1;");
            $sth->bindParam(':id', $dis_id, PDO::PARAM_INT);
            $sth->execute();
            $active_family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

            $sth = $dw_db->prepare("INSERT INTO DisActiveOffice(`Dis`,`DateTime`,`Num`) VALUES (:dis, :time, :active_family_num) ON DUPLICATE KEY UPDATE Num = :active_family_num");
            $sth->bindParam(':active_family_num', $active_family_num, PDO::PARAM_INT);
            $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
            $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
            $sth->execute();
        }
    }
}

//当月新增开门类型统计
function DisDoorOpenType()
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;
    global $year_months;

    $capture_types = array(0,1,2,3,4,100,101);
    $table_name = 'PersonalCapture';

    foreach ($year_months as $year_month)
    {
        $ym_table = $table_name."_".date('Ym', strtotime($year_month));
        $sth = $ods_db->prepare("show tables like '{$ym_table}'");
        $sth->execute();
        $tableRes = $sth->fetch(PDO::FETCH_ASSOC);
        if (empty($tableRes)) {
            continue;
        }
        foreach ($dis_top_list as $dis_acc => $dis_id)
        {
            foreach ($capture_types as $capture_type)
            {
                $sth = $ods_db->prepare("select count(1) as num from {$ym_table} C left join Account A on A.ID = C.MngAccountID left join Account B on B.ID = A. ParentID where (B.ID = :id) and C.CaptureType = :type and C.Response = 0");
                $sth->bindParam(':id', $dis_id, PDO::PARAM_INT);
                $sth->bindParam(':type', $capture_type, PDO::PARAM_INT);
                $sth->execute();
                $capture_type_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

                $sth = $dw_db->prepare("INSERT INTO  DisOpenDoorType(`Dis`,`DateTime`,`Type`,`Num`) VALUES (:dis, :time, :type, :capture_type_num) ON DUPLICATE KEY UPDATE Num = :capture_type_num");
                $sth->bindParam(':capture_type_num', $capture_type_num, PDO::PARAM_INT);
                $sth->bindParam(':type', $capture_type, PDO::PARAM_INT);
                $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
                $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
                $sth->execute();
            }
        }
    }

}

//在线设备数量
function DisOnlineDeviceNum()
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;
    global $year_months;

    foreach ($year_months as $year_month)
    {
        $timestart = $year_month . '-01 00:00:00';
        $timestart_t = strtotime($timestart);//转成时间戳
        $timeend_t = strtotime("first day of +1 month", $timestart_t);//不能用 + 1 months的形式
        $timeend = date("Y-m-d 00:00:00", $timeend_t);

        foreach ($dis_top_list as $dis_acc => $dis_id)
        {
            $sth = $ods_db->prepare("select count(1) as num From Devices D left join Account A on A.ID = D.MngAccountID left join Account B on B.ID = A.ParentID where B.ID = :id and D.CreateTime between :time_start and :time_end and D.Status = 1;");
            $sth->bindParam(':time_start', $timestart, PDO::PARAM_INT);
            $sth->bindParam(':time_end', $timeend, PDO::PARAM_STR);
            $sth->bindParam(':id', $dis_id, PDO::PARAM_INT);
            $sth->execute();
            $devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

            $sth = $ods_db->prepare("select count(1) as num From PersonalDevices D left join Account A on A.Account = D.Community left join Account B on B.ID = A.ParentID where B.ID = :id and D.CreateTime between :time_start and :time_end and D.Status = 1;");
            $sth->bindParam(':time_start', $timestart, PDO::PARAM_INT);
            $sth->bindParam(':time_end', $timeend, PDO::PARAM_STR);
            $sth->bindParam(':id', $dis_id, PDO::PARAM_INT);
            $sth->execute();
            $per_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

            $num = $devices_num + $per_devices_num;

            $sth = $dw_db->prepare("INSERT INTO  DisOnlineDevice(`Dis`,`DateTime`,`Num`) VALUES (:dis, :time, :num) ON DUPLICATE KEY UPDATE Num = :num");
            $sth->bindParam(':num', $num, PDO::PARAM_INT);
            $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
            $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
            $sth->execute();
        }
    }
}

//注册设备数量
function DisRegisterDeviceNum()
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;
    global $year_months;

    foreach ($year_months as $year_month)
    {
        $timestart = $year_month . '-01 00:00:00';
        $timestart_t = strtotime($timestart);//转成时间戳
        $timeend_t = strtotime("first day of +1 month", $timestart_t);//不能用 + 1 months的形式
        $timeend = date("Y-m-d 00:00:00", $timeend_t);

        foreach ($dis_top_list as $dis_acc => $dis_id)
        {
            $sth = $ods_db->prepare("select count(1) as num From Devices D left join Account A on A.ID = D.MngAccountID left join Account B on B.ID = A.ParentID where B.ID = :id and D.CreateTime between :time_start and :time_end;");
            $sth->bindParam(':time_start', $timestart, PDO::PARAM_INT);
            $sth->bindParam(':time_end', $timeend, PDO::PARAM_STR);
            $sth->bindParam(':id', $dis_id, PDO::PARAM_INT);
            $sth->execute();
            $devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

            $sth = $ods_db->prepare("select count(1) as num From PersonalDevices D left join Account A on A.Account = D.Community left join Account B on B.ID = A.ParentID where B.ID = :id and D.CreateTime between :time_start and :time_end;");
            $sth->bindParam(':time_start', $timestart, PDO::PARAM_INT);
            $sth->bindParam(':time_end', $timeend, PDO::PARAM_STR);
            $sth->bindParam(':id', $dis_id, PDO::PARAM_INT);
            $sth->execute();
            $per_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

            $num = $devices_num + $per_devices_num;

            $sth = $dw_db->prepare("INSERT INTO  DisRegisterDevice(`Dis`,`DateTime`,`Num`) VALUES (:dis, :time, :num) ON DUPLICATE KEY UPDATE Num = :num");
            $sth->bindParam(':num', $num, PDO::PARAM_INT);
            $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
            $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
            $sth->execute();
        }
    }
}

//自动填充开始和结束中间月份数据
function MonthDataPad($start, $end)
{
    $year_months = [];
    $month = $start;
    while (strtotime($month) <= strtotime($end)) {
        $year_months[] = $month;
        $month = date('Y-m', strtotime($month . '+1 month'));
    }
    return $year_months;
}


DisActiveFamilyNum();
DisCallNum();
DisOpenDoorNum();
DisFeeFamilyNum();
DisOfficerNum();
DisDoorOpenType();
DisOnlineDeviceNum();
DisRegisterDeviceNum();
//ActiveFamilyNumWeek();
?>
