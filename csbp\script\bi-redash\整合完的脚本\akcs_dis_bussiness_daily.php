<?php
date_default_timezone_set('PRC');
require('/home/<USER>/akcs_dw_config.php');
//区域
$REGION = null;
if ($argc == 2) {
    $REGION = $argv[1];
} else {
    echo 'use: akcs_dw_month.php  usa/eur/asia';
    exit;
}
 $dis_top_list = null;
 $dw_db = null;
 $ods_db = null;
if($REGION == 'USA')
{
    $dw_db = getUSADWDB();
}
else if($REGION == 'EUR')
{
    $dw_db = getEURDWDB();
}
else if($REGION == 'ASIA')
{
    $dw_db = getASIADWDB();
}
else if($REGION == 'JPN')
{
    $dw_db = getJPNDWDB();
}
else if($REGION == 'INDIA')
{
    $dw_db = getINDIADWDB();
}
else if($REGION == 'RU')
{
    $dw_db = getRUDWDB();
}
else if($REGION == 'LOCAL')
{
    $dw_db = getLOCALDWDB();    
}
else if($REGION == 'INC')
{
    $dw_db = getINCDWDB();
}
$ods_db = getODSDB();

//查询最大项目数量的dis以及自定义的dis
$sth_dis = $dw_db->prepare("select Dis,sum(Num) as pro_count from DisProjectSize where Dis not in (select Dis from DisListRemove) group by Dis order by pro_count desc limit 20;");
$sth_dis->execute();
$dis_list = $sth_dis->fetchALL(PDO::FETCH_ASSOC);
$sth_dis = $dw_db->prepare("select Dis from DisList;");
$sth_dis->execute();
$extra_dis_list = $sth_dis->fetchALL(PDO::FETCH_ASSOC);
$dis_list = array_merge($dis_list, $extra_dis_list);
foreach ($dis_list as $row => $dis)
{
    $dis_acc = $dis['Dis'];
    if (isset($dis_top_list[$dis_acc])) {
        continue;
    }
    $sth = $ods_db->prepare("select ID from Account where Account = :dis and Grade = 11");
    $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
    $sth->execute();
    $dis_id = $sth->fetch(PDO::FETCH_ASSOC)['ID'];//fetch 不是 fetchALL
    $dis_top_list[$dis_acc] = $dis_id;
}

function DisOpenDoorNum()
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;
    $year_month = date("Y-m");
    //判断日志库功能是否已经上线
    $sth = $ods_db->prepare("show databases like 'LOG'");
    $sth->execute();
    $res = $sth->fetch(PDO::FETCH_ASSOC);
    $logConfig = [];
    if (!empty($res)) {
        $sth = $ods_db->prepare("select * from LOG.LogSlice where LogTableName = 'PersonalCapture'");
        $sth->execute();
        $logConfig = $sth->fetch(PDO::FETCH_ASSOC);
    }
    foreach ($dis_top_list as $dis_acc => $dis_id)
    {
        $opendoor_num = 0;
        //日志库功能未上线，查询AKCS库,日志库功能上线当月，查询AKCS库和LOG库,非日志库上线当月，只查LOG库即可
        if (empty($logConfig) or date('Y-m') == date('Y-m', strtotime($logConfig['DeliveryTime']))) {
            $sth = $ods_db->prepare("select count(*) as num from PersonalCapture C left join Account A on A.ID = C.MngAccountID left join Account B on B.ID = A.ParentID where B.ID = :id and C.CaptureType < 102");
            $sth->bindParam(':id', $dis_id, PDO::PARAM_INT);
            $sth->execute();
            $opendoor_num += $sth->fetch(PDO::FETCH_ASSOC)['num'];
        }

        if (!empty($logConfig)) {
            $sth = $ods_db->prepare("select ID from Account where ParentID = :id");
            $sth->bindParam(':id', $dis_id, PDO::PARAM_INT);
            $sth->execute();
            $mngAccountIDs = array_filter(array_column($sth->fetchAll(PDO::FETCH_ASSOC), 'ID'));
            if (!empty($mngAccountIDs)) {
                $mngAccountIDs = join(',', $mngAccountIDs);
                for($i = 0; $i < $logConfig['Delivery']; $i++) {
                    $table = 'PersonalCapture_'.$i;
                    $sth = $ods_db->prepare("select count(*) as num from LOG.{$table} where MngAccountID in ($mngAccountIDs) and CaptureType < 102");
                    $sth->execute();
                    $num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
                    $opendoor_num += $num;
                }
            }
        }

        $sth = $dw_db->prepare("INSERT INTO  DisOpenDoor(`Dis`,`DateTime`,`Num`) VALUES (:dis, :time, :opendoor_num) ON DUPLICATE KEY UPDATE Num = :opendoor_num");
        $sth->bindParam(':opendoor_num', $opendoor_num, PDO::PARAM_INT);
        $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
        $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
        $sth->execute(); 
    }
}

function DisCallNum()
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;
    $year_month = date("Y-m");
    //判断日志库功能是否已经上线
    $sth = $ods_db->prepare("show databases like 'LOG'");
    $sth->execute();
    $res = $sth->fetch(PDO::FETCH_ASSOC);
    $logConfig = [];
    if (!empty($res)) {
        $sth = $ods_db->prepare("select * from LOG.LogSlice where LogTableName = 'CallHistory'");
        $sth->execute();
        $logConfig = $sth->fetch(PDO::FETCH_ASSOC);
    }

    foreach ($dis_top_list as $dis_acc => $dis_id)
    {
        $call_num = 0;
        //日志库功能未上线，查询AKCS库,日志库功能上线当月，查询AKCS库和LOG库,非日志库上线当月，只查LOG库即可
        if (empty($logConfig) or date('Y-m') == date('Y-m', strtotime($logConfig['DeliveryTime']))) {
            $sth = $ods_db->prepare("select count(*) as num from CallHistory C left join Account A on A.ID = C.MngAccountID left join Account B on B.ID = A.ParentID where B.ID = :id");
            $sth->bindParam(':id', $dis_id, PDO::PARAM_INT);
            $sth->execute();
            $call_num += $sth->fetch(PDO::FETCH_ASSOC)['num'];
        }

        if (!empty($logConfig)) {
            $sth = $ods_db->prepare("select ID from Account where ParentID = :id");
            $sth->bindParam(':id', $dis_id, PDO::PARAM_INT);
            $sth->execute();
            $mngAccountIDs = array_filter(array_column($sth->fetchAll(PDO::FETCH_ASSOC), 'ID'));
            if (!empty($mngAccountIDs)) {
                $mngAccountIDs = join(',', $mngAccountIDs);
                for($i = 0; $i < $logConfig['Delivery']; $i++) {
                    $table = 'CallHistory_'.$i;
                    $sth = $ods_db->prepare("select count(*) as num from LOG.$table where MngAccountID in ($mngAccountIDs)");
                    $sth->execute();
                    $num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
                    $call_num += $num;
                }
            }
        }

        $sth = $dw_db->prepare("INSERT INTO  DisCall(`Dis`,`DateTime`,`Num`) VALUES (:dis, :time, :call_num) ON DUPLICATE KEY UPDATE Num = :call_num");
        $sth->bindParam(':call_num', $call_num, PDO::PARAM_INT);
        $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
        $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
        $sth->execute(); 
    }

}
//每月新增激活家庭数
function DisActiveFamilyNum($REGION)
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;

    //如果是1日，则对上个月的数据进行补齐
    if(date("Y-m-d") == date('Y-m-01'))
    {
        $timeend = date("Y-m-d 00:00:00");//本月第一天
        $timestart = date('Y-m-d 00:00:00', strtotime(date('Y-m-01') . '-1 month')); // 计算出上月第一天
        $year_month = date("Y-m",strtotime($timestart));
    }
    else
    {
        //统计的是今天0点的数据
        $timeend = date("Y-m-d 00:00:00");
        $timestart_t= date("Y-m");
        $timestart = $timestart_t .'-01 00:00:00';
        $year_month = date("Y-m");
    }

    foreach ($dis_top_list as $dis_acc => $dis_id)
    {  
        $sth_act_family = $ods_db->prepare("select count(1) as count from PersonalAccount P left join Account A on A.ID = P.ParentID left join Account B on B.ID = A.ParentID where (B.ID = :id) and (P.ActiveTime between '".$timestart."' and '".$timeend."') and (P.Role = 10 or P.Role = 20) and P.Active = 1;");
        $sth_act_family->bindParam(':id', $dis_id, PDO::PARAM_INT);
        $sth_act_family->execute();
        $resultRole = $sth_act_family->fetch(PDO::FETCH_ASSOC);
        $family_active_num = $resultRole['count'];
        
        //added by chenyc,2021.12.17,统计这些激活的家庭中，需要收取月租的账号有多少,以过期时间来判断是否需要收费
        $sth_expire_family = $ods_db->prepare("select count(1) as count from PersonalAccount P left join Account A on A.ID = P.ParentID left join Account B on B.ID = A.ParentID where (B.ID = :id) and (P.ActiveTime between '".$timestart."' and '".$timeend."') and (P.ExpireTime < '2029-01-01 00:00:00') and (P.Role = 10 or P.Role = 20) and P.Active = 1;");
        $sth_expire_family->bindParam(':id', $dis_id, PDO::PARAM_INT);
        $sth_expire_family->execute();
        $resultRole = $sth_expire_family->fetch(PDO::FETCH_ASSOC);
        $family_expire_num = $resultRole['count'];
        
        $sth = $dw_db->prepare("INSERT INTO  DisActiveFamily(`Dis`,`DateTime`,`Num`, `ExpireNum`) VALUES (:dis, :time, :family_active_num, :family_expire_num) ON DUPLICATE KEY UPDATE Num = :family_active_num, ExpireNum = :family_expire_num");
        $sth->bindParam(':family_active_num', $family_active_num, PDO::PARAM_INT);
        $sth->bindParam(':family_expire_num', $family_expire_num, PDO::PARAM_INT);
        $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
        $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
        $sth->execute();         
    }
}


//每月新增办公用户数
function DisActiveOfficerNum()
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;

    //如果是1日，则对上个月的数据进行补齐
    if(date("Y-m-d") == date('Y-m-01'))
    {
        $timeend = date("Y-m-d 00:00:00");//本月第一天
        $timestart = date('Y-m-d 00:00:00', strtotime(date('Y-m-01') . '-1 month')); // 计算出上月第一天
        $year_month = date("Y-m",strtotime($timestart));
    }
    else
    {
        //统计的是今天0点的数据
        $timeend = date("Y-m-d 00:00:00");
        $timestart_t= date("Y-m");
        $timestart = $timestart_t .'-01 00:00:00';
        $year_month = date("Y-m");
    }
    
    foreach ($dis_top_list as $dis_acc => $dis_id)
    {
        $sth_act_family = $ods_db->prepare("select count(1) as count from PersonalAccount P left join Account A on A.ID = P.ParentID left join Account B on B.ID = A.ParentID where (B.ID = :id) and (P.ActiveTime between '".$timestart."' and '".$timeend."') and (P.Role = 30 or P.Role = 31) and P.Active = 1;");
        $sth_act_family->bindParam(':id', $dis_id, PDO::PARAM_INT);
        $sth_act_family->execute();
        $resultRole = $sth_act_family->fetch(PDO::FETCH_ASSOC);
        $office_active_num = $resultRole['count'];

        $sth = $dw_db->prepare("INSERT INTO  DisActiveOffice(`Dis`,`DateTime`,`Num`) VALUES (:dis, :time, :office_active_num) ON DUPLICATE KEY UPDATE Num = :office_active_num");
        $sth->bindParam(':office_active_num', $office_active_num, PDO::PARAM_INT);
        $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
        $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
        $sth->execute(); 
    }    
}

//每月新增月租家庭数
function DisFeeFamilyNum()
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;

    //如果是1日，则对上个月的数据进行补齐
    if(date("Y-m-d") == date('Y-m-01'))
    {
        $timeend = date("Y-m-d 00:00:00");//本月第一天
        $timestart = date('Y-m-d 00:00:00', strtotime(date('Y-m-01') . '-1 month')); // 计算出上月第一天
        $year_month = date("Y-m",strtotime($timestart));
    }
    else
    {
        //统计的是今天0点的数据
        $timeend = date("Y-m-d 00:00:00");
        $timestart_t= date("Y-m");
        $timestart = $timestart_t .'-01 00:00:00';
        $year_month = date("Y-m");
    }

    foreach ($dis_top_list as $dis_acc => $dis_id)
    {
        $sth = $ods_db->prepare("select count(1) as num from PersonalAccount P left join Account A on A.ID = P.ParentID left join Account B on B.ID = A. ParentID where (B.ID = :id) and (P.ActiveTime between '".$timestart."' and '".$timeend."') and (P.Role = 10 or P.Role = 20) and P.Active = 1 and P.Special = 0 and P.ExpireTime < '2029-01-01 00:00:00';");
        $sth->bindParam(':id', $dis_id, PDO::PARAM_INT);
        $sth->execute();
        $active_family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        $sth = $dw_db->prepare("INSERT INTO DisFeeFamily(`Dis`,`DateTime`,`Num`) VALUES (:dis, :time, :active_family_num) ON DUPLICATE KEY UPDATE Num = :active_family_num");
        $sth->bindParam(':active_family_num', $active_family_num, PDO::PARAM_INT);
        $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
        $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
        $sth->execute();
    }
}

//每月新增办公用户数
function DisOfficerNum()
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;

    //如果是1日，则对上个月的数据进行补齐
    if(date("Y-m-d") == date('Y-m-01'))
    {
        $timeend = date("Y-m-d 00:00:00");//本月第一天
        $timestart = date('Y-m-d 00:00:00', strtotime(date('Y-m-01') . '-1 month')); // 计算出上月第一天
        $year_month = date("Y-m",strtotime($timestart));
    }
    else
    {
        //统计的是今天0点的数据
        $timeend = date("Y-m-d 00:00:00");
        $timestart_t= date("Y-m");
        $timestart = $timestart_t .'-01 00:00:00';
        $year_month = date("Y-m");
    }

    foreach ($dis_top_list as $dis_acc => $dis_id)
    {
        $sth = $ods_db->prepare("select count(1) as num from PersonalAccount P left join Account A on A.ID = P.ParentID left join Account B on B.ID = A. ParentID where (B.ID = :id) and (P.ActiveTime between '".$timestart."' and '".$timeend."') and (P.Role = 30 or P.Role = 31) and P.Active = 1;");
        $sth->bindParam(':id', $dis_id, PDO::PARAM_INT);
        $sth->execute();
        $active_family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

        $sth = $dw_db->prepare("INSERT INTO DisActiveOffice(`Dis`,`DateTime`,`Num`) VALUES (:dis, :time, :active_family_num) ON DUPLICATE KEY UPDATE Num = :active_family_num");
        $sth->bindParam(':active_family_num', $active_family_num, PDO::PARAM_INT);
        $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
        $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
        $sth->execute();
    }
}

//当月新增开门类型统计
function DisDoorOpenType()
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;

    $capture_types = array(0,1,2,3,4,100,101);
    $year_month = date("Y-m");

    //判断日志库功能是否已经上线
    $sth = $ods_db->prepare("show databases like 'LOG'");
    $sth->execute();
    $res = $sth->fetch(PDO::FETCH_ASSOC);
    $logConfig = [];
    if (!empty($res)) {
        $sth = $ods_db->prepare("select * from LOG.LogSlice where LogTableName = 'PersonalCapture'");
        $sth->execute();
        $logConfig = $sth->fetch(PDO::FETCH_ASSOC);
    }

    foreach ($dis_top_list as $dis_acc => $dis_id)
    {
        foreach ($capture_types as $capture_type)
        {
            $capture_type_num = 0;
            //日志库功能未上线，查询AKCS库,日志库功能上线当月，查询AKCS库和LOG库,非日志库上线当月，只查LOG库即可
            if (empty($logConfig) or date('Y-m') == date('Y-m', strtotime($logConfig['DeliveryTime']))) {
                $sth = $ods_db->prepare("select count(1) as num from PersonalCapture C left join Account A on A.ID = C.MngAccountID left join Account B on B.ID = A. ParentID where (B.ID = :id) and C.CaptureType = :type and C.Response = 0");
                $sth->bindParam(':id', $dis_id, PDO::PARAM_INT);
                $sth->bindParam(':type', $capture_type, PDO::PARAM_INT);
                $sth->execute();
                $capture_type_num += $sth->fetch(PDO::FETCH_ASSOC)['num'];
            }

            if (!empty($logConfig)) {
                $sth = $ods_db->prepare("select ID from Account where ParentID = :id");
                $sth->bindParam(':id', $dis_id, PDO::PARAM_INT);
                $sth->execute();
                $mngAccountIDs = array_filter(array_column($sth->fetchAll(PDO::FETCH_ASSOC), 'ID'));
                if (!empty($mngAccountIDs)) {
                    $mngAccountIDs = join(',', $mngAccountIDs);
                    for($i = 0; $i < $logConfig['Delivery']; $i++) {
                        $table = 'PersonalCapture_'.$i;
                        $sth = $ods_db->prepare("select count(*) as num from LOG.{$table} where MngAccountID in ($mngAccountIDs) and CaptureType = :type and Response = 0");
                        $sth->bindParam(':type', $capture_type, PDO::PARAM_INT);
                        $sth->execute();
                        $num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
                        $capture_type_num += $num;
                    }
                }
            }

            $sth = $dw_db->prepare("INSERT INTO  DisOpenDoorType(`Dis`,`DateTime`,`Type`,`Num`) VALUES (:dis, :time, :type, :capture_type_num) ON DUPLICATE KEY UPDATE Num = :capture_type_num");
            $sth->bindParam(':capture_type_num', $capture_type_num, PDO::PARAM_INT);
            $sth->bindParam(':type', $capture_type, PDO::PARAM_INT);
            $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
            $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
            $sth->execute();
        }
    }
}

//在线设备数量
function DisOnlineDeviceNum()
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;

    if(date("Y-m-d") == date('Y-m-01'))
    {
        $timeend = date("Y-m-d 00:00:00");//本月第一天
        $timestart = date('Y-m-d 00:00:00', strtotime(date('Y-m-01') . '-1 month')); // 计算出上月第一天
        $year_month = date("Y-m",strtotime($timestart));
    }
    else
    {
        //统计的是今天0点的数据
        $timeend = date("Y-m-d 00:00:00");
        $timestart_t= date("Y-m");
        $timestart = $timestart_t .'-01 00:00:00';
        $year_month = date("Y-m");
    }

    foreach ($dis_top_list as $dis_acc => $dis_id)
    {
        $sth = $ods_db->prepare("select count(1) as num From Devices D left join Account A on A.ID = D.MngAccountID left join Account B on B.ID = A.ParentID where B.ID = :id and D.CreateTime between :time_start and :time_end and D.Status = 1;");
        $sth->bindParam(':time_start', $timestart, PDO::PARAM_INT);
        $sth->bindParam(':time_end', $timeend, PDO::PARAM_STR);
        $sth->bindParam(':id', $dis_id, PDO::PARAM_INT);
        $sth->execute();
        $devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

        $sth = $ods_db->prepare("select count(1) as num From PersonalDevices D left join Account A on A.Account = D.Community left join Account B on B.ID = A.ParentID where B.ID = :id and D.CreateTime between :time_start and :time_end and D.Status = 1;");
        $sth->bindParam(':time_start', $timestart, PDO::PARAM_INT);
        $sth->bindParam(':time_end', $timeend, PDO::PARAM_STR);
        $sth->bindParam(':id', $dis_id, PDO::PARAM_INT);
        $sth->execute();
        $per_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

        $num = $devices_num + $per_devices_num;

        $sth = $dw_db->prepare("INSERT INTO  DisOnlineDevice(`Dis`,`DateTime`,`Num`) VALUES (:dis, :time, :num) ON DUPLICATE KEY UPDATE Num = :num");
        $sth->bindParam(':num', $num, PDO::PARAM_INT);
        $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
        $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
        $sth->execute();
    }
}

//注册设备数量
function DisRegisterDeviceNum()
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;

    if(date("Y-m-d") == date('Y-m-01'))
    {
        $timeend = date("Y-m-d 00:00:00");//本月第一天
        $timestart = date('Y-m-d 00:00:00', strtotime(date('Y-m-01') . '-1 month')); // 计算出上月第一天
        $year_month = date("Y-m",strtotime($timestart));
    }
    else
    {
        //统计的是今天0点的数据
        $timeend = date("Y-m-d 00:00:00");
        $timestart_t= date("Y-m");
        $timestart = $timestart_t .'-01 00:00:00';
        $year_month = date("Y-m");
    }

    foreach ($dis_top_list as $dis_acc => $dis_id)
    {
        $sth = $ods_db->prepare("select count(1) as num From Devices D left join Account A on A.ID = D.MngAccountID left join Account B on B.ID = A.ParentID where B.ID = :id and D.CreateTime between :time_start and :time_end;");
        $sth->bindParam(':time_start', $timestart, PDO::PARAM_INT);
        $sth->bindParam(':time_end', $timeend, PDO::PARAM_STR);
        $sth->bindParam(':id', $dis_id, PDO::PARAM_INT);
        $sth->execute();
        $devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

        $sth = $ods_db->prepare("select count(1) as num From PersonalDevices D left join Account A on A.Account = D.Community left join Account B on B.ID = A.ParentID where B.ID = :id and D.CreateTime between :time_start and :time_end;");
        $sth->bindParam(':time_start', $timestart, PDO::PARAM_INT);
        $sth->bindParam(':time_end', $timeend, PDO::PARAM_STR);
        $sth->bindParam(':id', $dis_id, PDO::PARAM_INT);
        $sth->execute();
        $per_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

        $num = $devices_num + $per_devices_num;

        $sth = $dw_db->prepare("INSERT INTO  DisRegisterDevice(`Dis`,`DateTime`,`Num`) VALUES (:dis, :time, :num) ON DUPLICATE KEY UPDATE Num = :num");
        $sth->bindParam(':num', $num, PDO::PARAM_INT);
        $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
        $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
        $sth->execute();
    }
}


DisActiveFamilyNum($REGION);
DisCallNum();
DisOpenDoorNum();
DisFeeFamilyNum();
DisOfficerNum();
DisDoorOpenType();
DisOnlineDeviceNum();
DisRegisterDeviceNum();
//ActiveFamilyNumWeek();
DisActiveOfficerNum();
?>
