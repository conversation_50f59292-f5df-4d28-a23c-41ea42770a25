<?php
date_default_timezone_set('PRC');
require('/home/<USER>/akcs_dw_config.php');
//区域
$REGION = null;
if ($argc == 2) {
    $REGION = $argv[1];
} else {
    echo 'use: akcs_dw_month.php  usa/eur/asis';
    exit;
}

//各个dis项目占比全量更新
function DisProject($REGION)
{
    $ods_db = getODSDB();
    if($REGION == 'USA')
    {
        $dw_db = getUSADWDB();
    }
    else if($REGION == 'EUR')
    {
        $dw_db = getEURDWDB();
    }
    else if($REGION == 'ASIA')
    {
        $dw_db = getASIADWDB();
    }
    else if($REGION == 'LOCAL')
    {
        $dw_db = getLOCALDWDB();
    }
    else if($REGION == 'CHN')
    {
        $dw_db = getCHNDWDB();
    }
    else if($REGION == 'JPN')
    {
        $dw_db = getJPNDWDB();
    }
    else if($REGION == 'INDIA')
    {
        $dw_db = getINDIADWDB();
    }
    else if($REGION == 'RU')
    {
        $dw_db = getRUDWDB();
    }
    else if($REGION == 'INC')
    {
        $dw_db = getINCDWDB();
    }
    //只统计主dis， ManageGroup = 0。
    $sth_dis = $ods_db->prepare("select ID,Account from Account where Grade = 11 and ManageGroup = 0");
    $sth_dis->execute();
    $dis_list = $sth_dis->fetchALL(PDO::FETCH_ASSOC);

    $sth_remove_dis = $dw_db->prepare("select Dis from DisListRemove");
    $sth_remove_dis->execute();
    $remove_dis_list = $sth_remove_dis->fetchALL(PDO::FETCH_ASSOC);
    $remove_dis_list = array_column($remove_dis_list, 'Dis');
    foreach ($dis_list as $row => $dis) //统计区域下面的个人终端管理员
    {
        //如果有不需要统计的dis，则进行过滤
        if (in_array($dis['Account'], $remove_dis_list))
        {
            continue;
        }

        //1:0-10户; 2:10-25户; 3:25-50户; 4:50-100户; 5:100-200户; 6:200-500户;7:500以上'
        $dis_project_size = array("1"=>0,"2"=>0,"3"=>0,"4"=>0,"5"=>0,"6"=>0,"7"=>0);
        $dis_id=$dis['ID'];
        $dis_name=$dis['Account'];
        //下面的所有社区
        $sth = $ods_db->prepare("select A1.ID,A1.Location,A2.Account from Account A1 left join Account A2 on A2.ID=A1.ManageGroup where A1.ParentID = :pid and A1.Grade = 21;");
        $sth->bindParam(':pid', $dis_id, PDO::PARAM_INT);
        $sth->execute();
        $mng_list = $sth->fetchALL(PDO::FETCH_ASSOC);
        $online_str = null;
        foreach ($mng_list as $row => $mng) //统计个人终端管理员下面的主账号
        {	
            echo '1';
            $mng_id=$mng['ID'];
            $mng_location=$mng['Location'];
            //再查该社区下面的所有主账号
            $sth = $ods_db->prepare(" select count(*) as num from PersonalAccount where ParentID = :pid and Role = 20;");
            $sth->bindParam(':pid', $mng_id, PDO::PARAM_INT);
            $sth->execute();
            $family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
            if($family_num > 1 && $family_num <= 10)
            {
                $dis_project_size['1']++;
            }
            else if($family_num > 11 && $family_num <= 25)
            {
               $dis_project_size['2']++; 
            }
            else if($family_num > 26 && $family_num <= 50)
            {
               $dis_project_size['3']++; 
            }
            else if($family_num > 51 && $family_num <= 100)
            {
               $dis_project_size['4']++; 
            }
            else if($family_num > 101 && $family_num <= 200)
            {
               $dis_project_size['5']++; 
            }
            else if($family_num > 201 && $family_num <= 500)
            {
               $dis_project_size['6']++; 
            }
            else if($family_num > 500)
            {
               $dis_project_size['7']++; 
            }
        }
        //将各个dis的项目分部插入数仓中
        foreach($dis_project_size as $x=>$x_value)
        {
            $type = number_format($x);
            $sth = $dw_db->prepare("INSERT INTO  DisProjectSize(`Dis`,`ProType`,`Num`) VALUES (:dis, :type, :num) ON DUPLICATE KEY UPDATE Num = :num");
            $sth->bindParam(':num', $x_value, PDO::PARAM_INT);
            $sth->bindParam(':type', $type, PDO::PARAM_INT);
            $sth->bindParam(':dis', $dis_name, PDO::PARAM_STR);
            $sth->execute();
        }
    }
}

DisProject($REGION);
?>