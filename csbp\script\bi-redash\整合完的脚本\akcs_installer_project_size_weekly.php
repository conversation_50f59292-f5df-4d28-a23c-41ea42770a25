<?php
date_default_timezone_set('PRC');
require('/home/<USER>/akcs_dw_config.php');
//区域
$REGION = null;
if ($argc == 2) {
    $REGION = $argv[1];
} else {
    echo 'use: akcs_dw_month.php  usa/eur/asia';
    exit;
}
 $dis_top_list = null;
 $dw_db = null;
 $ods_db = null;
if($REGION == 'USA')
{
    $dw_db = getUSADWDB();
}
else if($REGION == 'EUR')
{
    $dw_db = getEURDWDB();
}
else if($REGION == 'ASIA')
{
    $dw_db = getASIADWDB();
}
else if($REGION == 'LOCAL')
{
    $dw_db = getLOCALDWDB();    
}
else if($REGION == 'JPN')
{
    $dw_db = getJPNDWDB();
}
else if($REGION == 'INDIA')
{
    $dw_db = getINDIADWDB();
}
else if($REGION == 'RU')
{
    $dw_db = getRUDWDB();
}
else if($REGION == 'INC')
{
    $dw_db = getINCDWDB();
}

$ods_db = getODSDB();

//查询最大项目数量的dis top20即可以及自定义的dis
$sth_dis = $dw_db->prepare("select Dis,sum(Num) as pro_count from DisProjectSize where Dis not in (select Dis from DisListRemove) group by Dis order by pro_count desc limit 20;");
$sth_dis->execute();
$dis_list = $sth_dis->fetchALL(PDO::FETCH_ASSOC);
$sth_dis = $dw_db->prepare("select Dis from DisList;");
$sth_dis->execute();
$extra_dis_list = $sth_dis->fetchALL(PDO::FETCH_ASSOC);
$dis_list = array_merge($dis_list, $extra_dis_list);
foreach ($dis_list as $row => $dis)
{
    $dis_acc = $dis['Dis'];
    if (isset($dis_top_list[$dis_acc])) {
        continue;
    }
    $sth = $ods_db->prepare("select ID from Account where Account = :dis and Grade = 11");
    $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
    $sth->execute();
    $dis_id = $sth->fetch(PDO::FETCH_ASSOC)['ID'];//fetch 不是 fetchALL
    $dis_top_list[$dis_acc] = $dis_id;
}

$count = 0;
//各个dis项目占比全量更新
function DisInsProject()
{
    global $dis_top_list;
    global $ods_db;
    global $dw_db;
    foreach ($dis_top_list as $dis_acc => $dis_id)//统计区域下面的个人终端管理员
    {
        global $count;
        $count = $count + 1;
        echo $count;
        echo $dis_id;
        //下面的所有installer
        $sth = $ods_db->prepare("select ID,Account from Account where ManageGroup = ID and ParentID = :pid and Grade in (21, 22)");
        $sth->bindParam(':pid', $dis_id, PDO::PARAM_INT);
        $sth->execute();
        $ins_list = $sth->fetchALL(PDO::FETCH_ASSOC);     
        foreach ($ins_list as $row => $ins)
        {	
            echo 'a---';
            //1:0-10户; 2:10-25户; 3:25-50户; 4:50-100户; 5:100-200户; 6:200-500户;7:500以上'
            $ins_project_size = array("1"=>0,"2"=>0,"3"=>0,"4"=>0,"5"=>0,"6"=>0,"7"=>0);
            $ins_id=$ins['ID'];
            $ins_acc=$ins['Account'];
            echo $ins_acc;
            //installer下面的所有社区
            $sth = $ods_db->prepare("select ID from Account where ManageGroup = :pid and Grade = 21;");
            $sth->bindParam(':pid', $ins_id, PDO::PARAM_INT);
            $sth->execute();
            $comm_list = $sth->fetchALL(PDO::FETCH_ASSOC);
            foreach ($comm_list as $row => $comm)
            {
                echo 'b----';
                $comm_id = $comm['ID'];
                //再查该社区下面的所有主账号
                $sth = $ods_db->prepare(" select count(*) as num from PersonalAccount where ParentID = :pid and Role = 20;");
                $sth->bindParam(':pid', $comm_id, PDO::PARAM_INT);
                $sth->execute();
                $family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
                if($family_num > 1 && $family_num <= 10)
                {
                    $ins_project_size['1']++;
                }
                else if($family_num > 10 && $family_num <= 25)
                {
                   $ins_project_size['2']++; 
                }
                else if($family_num > 25 && $family_num <= 50)
                {
                   $ins_project_size['3']++; 
                }
                else if($family_num > 50 && $family_num <= 100)
                {
                   $ins_project_size['4']++; 
                }
                else if($family_num > 100 && $family_num <= 200)
                {
                   $ins_project_size['5']++; 
                }
                else if($family_num > 200 && $family_num <= 500)
                {
                   $ins_project_size['6']++; 
                }
                else if($family_num > 500)
                {
                   $ins_project_size['7']++; 
                }
            }
            //将各个ins的项目分部插入数仓中
            foreach($ins_project_size as $x=>$x_value)
            {
                $type = number_format($x);
                $sth = $dw_db->prepare("INSERT INTO InsProjectSize(`Dis`,`Ins`,`ProType`,`Num`) VALUES (:dis, :ins, :type, :num) ON DUPLICATE KEY UPDATE Num = :num");
                $sth->bindParam(':num', $x_value, PDO::PARAM_INT);
                $sth->bindParam(':type', $type, PDO::PARAM_INT);
                $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
                $sth->bindParam(':ins', $ins_acc, PDO::PARAM_STR);
                $sth->execute();
            }
        }
    }
}

DisInsProject($REGION);
?>