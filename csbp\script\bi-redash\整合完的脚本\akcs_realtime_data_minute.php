<?php
date_default_timezone_set('PRC');
require('/home/<USER>/akcs_dw_config.php');
//区域
$REGION = null;
if ($argc == 2) {
    $REGION = $argv[1];
} else {
    echo 'use: akcs_dw_month.php  usa/eur/asia';
    exit;
}
# '0:无意义; 1:今日开门次数; 2:今日呼叫次数;3:在线设备数;4:设备数;5:今日新增设备数;6:今日新增rtsp次数;7:总rtsp次数;8:新增激活家庭;9:新增激活App;10:总激活家庭数;11:总激活APP数',

//查询不需要统计的dis
$dis_remove_top_list = [];
$ods_db = null;
$dw_server_db = null;
if($REGION == 'USA')
{
    $dw_server_db = getUSADWDB();
}
else if($REGION == 'EUR')
{
    $dw_server_db = getEURDWDB();
}
else if($REGION == 'ASIA')
{
    $dw_server_db = getASIADWDB();
}
else if($REGION == 'JPN')
{
    $dw_server_db = getJPNDWDB();
}
else if($REGION == 'LOCAL')
{
    $dw_server_db = getLOCALDWDB();
}
if (null !== $dw_server_db) {
    $ods_db = getODSDB();
    $sth_dis = $dw_server_db->prepare("select Dis from DisListRemove;");
    $sth_dis->execute();
    $dis_list = $sth_dis->fetchALL(PDO::FETCH_ASSOC);
    foreach ($dis_list as $row => $dis)
    {
        $dis_acc = $dis['Dis'];
        $sth = $ods_db->prepare("select ID from Account where Account = :dis and Grade = 11");
        $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
        $sth->execute();
        $dis_id = $sth->fetch(PDO::FETCH_ASSOC)['ID'];//fetch 不是 fetchALL
        if (empty($dis_id)) {
            continue;
        }
        $dis_remove_top_list[$dis_acc] = $dis_id;
    }
}

//今日开门次数实时同步
function TodayDoorNum($REGION)
{
    //从业务数据库里面查询数据
    $ods_db = getODSDB();
    $dw_db = getDWDB();
    $today = date("Y-m-d 00:00:00");
    global $dis_remove_top_list;
    $disRemoveIds = join(',', $dis_remove_top_list);

    //判断日志库功能是否已经上线
    $sth = $ods_db->prepare("show databases like 'LOG'");
    $sth->execute();
    $res = $sth->fetch(PDO::FETCH_ASSOC);
    $logConfig = [];
    if (!empty($res)) {
        $sth = $ods_db->prepare("select * from LOG.LogSlice where LogTableName = 'PersonalCapture'");
        $sth->execute();
        $logConfig = $sth->fetch(PDO::FETCH_ASSOC);
    }

    $open_door_num = 0;

    if (empty($logConfig)) {
        $sth = $ods_db->prepare("select count(1) as num From PersonalCapture where (CaptureType = 0 or CaptureType = 1 or CaptureType = 2 or CaptureType = 3 or CaptureType = 4 or CaptureType = 100 or CaptureType = 101) and CaptureTime > :today;");
        $sth->bindParam(':today', $today, PDO::PARAM_STR);
        $sth->execute();
        $open_door_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        if (!empty($disRemoveIds)) {
            $sth = $ods_db->prepare("select count(1) as num From PersonalCapture C left join Account A on A.ID = C.MngAccountID 
        left join Account B on B.ID = A.ParentID where B.ID in ({$disRemoveIds}) and 
        (C.CaptureType = 0 or C.CaptureType = 1 or C.CaptureType = 2 or C.CaptureType = 3 or C.CaptureType = 4 or C.CaptureType = 100 or C.CaptureType = 101) and C.CaptureTime > :today;");
            $sth->bindParam(':today', $today, PDO::PARAM_STR);
            $sth->execute();
            $remove_open_door_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
            $open_door_num = $open_door_num - $remove_open_door_num;
        }
    } else {
        for($i = 0; $i < $logConfig['Delivery']; $i++) {
            $table = 'PersonalCapture_'.$i;
            $sth = $ods_db->prepare("select count(1) as num from LOG.{$table} where (CaptureType = 0 or CaptureType = 1 or CaptureType = 2 or CaptureType = 3 or CaptureType = 4 or CaptureType = 100 or CaptureType = 101) and CaptureTime > :today");
            $sth->bindParam(':today', $today, PDO::PARAM_STR);
            $sth->execute();
            $num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
            $open_door_num += $num;
        }
        if (!empty($disRemoveIds)) {
            $sth = $ods_db->prepare("select ID from Account where ParentID in ({$disRemoveIds})");
            $sth->execute();
            $mngAccountIDs = array_filter(array_column($sth->fetchAll(PDO::FETCH_ASSOC), 'ID'));
            if (!empty($mngAccountIDs)) {
                $mngAccountIDs = join(',', $mngAccountIDs);
                $remove_open_door_num = 0;
                for($i = 0; $i < $logConfig['Delivery']; $i++) {
                    $table = 'PersonalCapture_'.$i;
                    $sth = $ods_db->prepare("select count(*) as num from LOG.{$table} where MngAccountID in ($mngAccountIDs) and (CaptureType = 0 or CaptureType = 1 or CaptureType = 2 or CaptureType = 3 or CaptureType = 4 or CaptureType = 100 or CaptureType = 101) and CaptureTime > :today");
                    $sth->execute();
                    $num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
                    $remove_open_door_num += $num;
                }
                $open_door_num = $open_door_num - $remove_open_door_num;
            }
        }
    }

    $data_type = 1;
    $sth = $dw_db->prepare("INSERT INTO  GlobalRealTimeData(`Region`,`DataType`,`Num`) VALUES (:region, :type, :open_door_num) ON DUPLICATE KEY UPDATE Num = :open_door_num");
    $sth->bindParam(':open_door_num', $open_door_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':type', $data_type, PDO::PARAM_INT);
    $sth->execute();
}
//今日通话次数实时同步
function TodayCallNum($REGION)
{
    //从业务数据库里面查询数据
    $ods_db = getODSDB();
    $dw_db = getDWDB();
    $today = date("Y-m-d 00:00:00");
    global $dis_remove_top_list;
    $disRemoveIds = join(',', $dis_remove_top_list);

    //判断日志库功能是否已经上线
    $sth = $ods_db->prepare("show databases like 'LOG'");
    $sth->execute();
    $res = $sth->fetch(PDO::FETCH_ASSOC);
    $logConfig = [];
    if (!empty($res)) {
        $sth = $ods_db->prepare("select * from LOG.LogSlice where LogTableName = 'CallHistory'");
        $sth->execute();
        $logConfig = $sth->fetch(PDO::FETCH_ASSOC);
    }

    $call_num = 0;
    if (empty($logConfig)) {
        $sth = $ods_db->prepare("select count(1) as num From CallHistory where StartTime > :today;");
        $sth->bindParam(':today', $today, PDO::PARAM_STR);
        $sth->execute();
        $call_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        if (!empty($disRemoveIds)) {
            $sth = $ods_db->prepare("select count(1) as num From CallHistory C left join Account A on A.ID = C.MngAccountID left join Account B on B.ID = A.ParentID where B.ID in ({$disRemoveIds}) and C.StartTime > :today;");
            $sth->bindParam(':today', $today, PDO::PARAM_STR);
            $sth->execute();
            $remove_call_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
            $call_num = $call_num - $remove_call_num;
        }
    } else {
        for($i = 0; $i < $logConfig['Delivery']; $i++) {
            $table = 'CallHistory_'.$i;
            $sth = $ods_db->prepare("select count(*) as num from LOG.$table where StartTime > :today");
            $sth->bindParam(':today', $today, PDO::PARAM_STR);
            $sth->execute();
            $num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
            $call_num += $num;
        }
        if (!empty($disRemoveIds)) {

        }
    }

    $data_type = 2;
    $sth = $dw_db->prepare("INSERT INTO  GlobalRealTimeData(`Region`,`DataType`,`Num`) VALUES (:region, :type, :call_num) ON DUPLICATE KEY UPDATE Num = :call_num");
    $sth->bindParam(':call_num', $call_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':type', $data_type, PDO::PARAM_INT);
    $sth->execute();
}

//实时在线设备数
function TotalOnlineDevices($REGION)
{
    //从业务数据库里面查询数据
    $ods_db = getODSDB();
    $dw_db = getDWDB();
    global $dis_remove_top_list;
    $disRemoveIds = join(',', $dis_remove_top_list);

    $sth = $ods_db->prepare("select count(1) as num From Devices where Status = 1;");
    $sth->execute();
    $online_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    if (!empty($disRemoveIds)) {
        $sth = $ods_db->prepare("select count(1) as num From Devices D left join Account A on A.ID = D.MngAccountID left join Account B on B.ID = A.ParentID where B.ID in ({$disRemoveIds}) and D.Status = 1;");
        $sth->execute();
        $remove_online_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        $online_devices_num = $online_devices_num - $remove_online_devices_num;
    }

    $sth = $ods_db->prepare("select count(1) as num From PersonalDevices where Status = 1;");
    $sth->execute();
    $online_per_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    if (!empty($disRemoveIds)) {
        $sth = $ods_db->prepare("select count(1) as num From PersonalDevices D left join Account A on A.Account = D.Community left join Account B on B.ID = A.ParentID where B.ID in ({$disRemoveIds}) and D.Status = 1;");
        $sth->execute();
        $remove_online_per_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        $online_per_devices_num = $online_per_devices_num - $remove_online_per_devices_num;
    }

    $online_num = $online_devices_num + $online_per_devices_num;

    $data_type = 3;
    $sth = $dw_db->prepare("INSERT INTO  GlobalRealTimeData(`Region`,`DataType`,`Num`) VALUES (:region, :type, :online_num) ON DUPLICATE KEY UPDATE Num = :online_num");
    $sth->bindParam(':online_num', $online_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':type', $data_type, PDO::PARAM_INT);
    $sth->execute();
}

//实时注册设备数
function TotalRegisterDevices($REGION)
{
    //从业务数据库里面查询数据
    $ods_db = getODSDB();
    $dw_db = getDWDB();
    global $dis_remove_top_list;
    $disRemoveIds = join(',', $dis_remove_top_list);

    $sth = $ods_db->prepare("select count(1) as num From Devices;");
    $sth->execute();
    $online_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    if (!empty($disRemoveIds)) {
        $sth = $ods_db->prepare("select count(1) as num From Devices D left join Account A on A.ID = D.MngAccountID left join Account B on B.ID = A.ParentID where B.ID in ({$disRemoveIds});");
        $sth->execute();
        $remove_online_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        $online_devices_num = $online_devices_num - $remove_online_devices_num;
    }

    $sth = $ods_db->prepare("select count(1) as num From PersonalDevices;");
    $sth->execute();
    $online_per_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    if (!empty($disRemoveIds)) {
        $sth = $ods_db->prepare("select count(1) as num From PersonalDevices D left join Account A on A.Account = D.Community left join Account B on B.ID = A.ParentID where B.ID in ({$disRemoveIds});");
        $sth->execute();
        $remove_online_per_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        $online_per_devices_num = $online_per_devices_num - $remove_online_per_devices_num;
    }

    $online_num = $online_devices_num + $online_per_devices_num;

    $data_type = 4;
    $sth = $dw_db->prepare("INSERT INTO  GlobalRealTimeData(`Region`,`DataType`,`Num`) VALUES (:region, :type, :online_num) ON DUPLICATE KEY UPDATE Num = :online_num");
    $sth->bindParam(':online_num', $online_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':type', $data_type, PDO::PARAM_INT);
    $sth->execute();
}

//今日新增注册设备数
function TodayRegisterDevices($REGION)
{
    //从业务数据库里面查询数据
    $ods_db = getODSDB();
    $dw_db = getDWDB();
    $today = date("Y-m-d 00:00:00");
    global $dis_remove_top_list;
    $disRemoveIds = join(',', $dis_remove_top_list);

    $sth = $ods_db->prepare("select count(1) as num From Devices where CreateTime > :today;");
    $sth->bindParam(':today', $today, PDO::PARAM_STR);
    $sth->execute();
    $online_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    if (!empty($disRemoveIds)) {
        $sth = $ods_db->prepare("select count(1) as num From Devices D left join Account A on A.ID = D.MngAccountID left join Account B on B.ID = A.ParentID where B.ID in ({$disRemoveIds}) and D.CreateTime > :today;");
        $sth->bindParam(':today', $today, PDO::PARAM_STR);
        $sth->execute();
        $remove_online_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        $online_devices_num = $online_devices_num - $remove_online_devices_num;
    }

    $sth = $ods_db->prepare("select count(1) as num From PersonalDevices where CreateTime > :today;");
    $sth->bindParam(':today', $today, PDO::PARAM_STR);
    $sth->execute();
    $online_per_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    if (!empty($disRemoveIds)) {
        $sth = $ods_db->prepare("select count(1) as num From PersonalDevices D left join Account A on A.Account = D.Community left join Account B on B.ID = A.ParentID where B.ID in ({$disRemoveIds}) and D.CreateTime > :today;");
        $sth->bindParam(':today', $today, PDO::PARAM_STR);
        $sth->execute();
        $remove_online_per_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        $online_per_devices_num = $online_per_devices_num - $remove_online_per_devices_num;
    }

    $online_num = $online_devices_num + $online_per_devices_num;

    $data_type = 5;
    $sth = $dw_db->prepare("INSERT INTO  GlobalRealTimeData(`Region`,`DataType`,`Num`) VALUES (:region, :type, :online_num) ON DUPLICATE KEY UPDATE Num = :online_num");
    $sth->bindParam(':online_num', $online_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':type', $data_type, PDO::PARAM_INT);
    $sth->execute();
}

function TotalActiveFamily($REGION)
{
    //从业务数据库里面查询数据
    $ods_db = getODSDB();
    $dw_db = getDWDB();
    $now = date("Y-m-d H:i:s");
    global $dis_remove_top_list;
    $disRemoveIds = join(',', $dis_remove_top_list);

    $sth = $ods_db->prepare("select count(1) as num from PersonalAccount where ((ActiveTime is NULL and CreateTime < '2019-09-01 00:00:00' and (Role = 10 or Role = 20) and Active = 1) or (ActiveTime < :now and (Role = 10 or Role = 20) and Active = 1  and Special = 0));");
    $sth->bindParam(':now', $now, PDO::PARAM_STR);
    $sth->execute();
    $total_active_family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    if (!empty($disRemoveIds)) {
        $sth = $ods_db->prepare("select count(1) as num from PersonalAccount P left join Account A on A.ID = P.ParentID left join Account B on B.ID = A. ParentID where (B.ID in ({$disRemoveIds})) and ((P.ActiveTime is NULL and P.CreateTime < '2019-09-01 00:00:00' and (P.Role = 10 or P.Role = 20) and P.Active = 1) or (P.ActiveTime < :now and (P.Role = 10 or P.Role = 20) and P.Active = 1  and P.Special = 0));");
        $sth->bindParam(':now', $now, PDO::PARAM_STR);
        $sth->execute();
        $remove_total_active_family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        $total_active_family_num = $total_active_family_num - $remove_total_active_family_num;
    }

    $data_type = 10;
    $sth = $dw_db->prepare("INSERT INTO  GlobalRealTimeData(`Region`,`DataType`,`Num`) VALUES (:region, :type, :total_active_family_num) ON DUPLICATE KEY UPDATE Num = :total_active_family_num");
    $sth->bindParam(':total_active_family_num', $total_active_family_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':type', $data_type, PDO::PARAM_INT);
    $sth->execute();
}
function TodayActiveFamily($REGION)
{
    //从业务数据库里面查询数据
    $ods_db = getODSDB();
    $dw_db = getDWDB();
    $today = date("Y-m-d 00:00:00");
    global $dis_remove_top_list;
    $disRemoveIds = join(',', $dis_remove_top_list);

    $sth = $ods_db->prepare("select count(1) as num from PersonalAccount where ActiveTime > :today and (Role = 10 or Role = 20) and Active = 1 and Special = 0;");
    $sth->bindParam(':today', $today, PDO::PARAM_STR);
    $sth->execute();
    $today_active_family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    if (!empty($disRemoveIds)) {
        $sth = $ods_db->prepare("select count(1) as num from PersonalAccount P left join Account A on A.ID = P.ParentID left join Account B on B.ID = A. ParentID where (B.ID in ({$disRemoveIds})) and P.ActiveTime > :today and (P.Role = 10 or P.Role = 20) and P.Active = 1 and P.Special = 0;");
        $sth->bindParam(':today', $today, PDO::PARAM_STR);
        $sth->execute();
        $remove_today_active_family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        $today_active_family_num = $today_active_family_num - $remove_today_active_family_num;
    }

    $data_type = 8;
    $sth = $dw_db->prepare("INSERT INTO  GlobalRealTimeData(`Region`,`DataType`,`Num`) VALUES (:region, :type, :today_active_family_num) ON DUPLICATE KEY UPDATE Num = :today_active_family_num");
    $sth->bindParam(':today_active_family_num', $today_active_family_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':type', $data_type, PDO::PARAM_INT);
    $sth->execute();
}

function TotalActiveApp($REGION)
{
    //从业务数据库里面查询数据
    $ods_db = getODSDB();
    $dw_db = getDWDB();
    global $dis_remove_top_list;
    $disRemoveIds = join(',', $dis_remove_top_list);

    $sth = $ods_db->prepare("select count(1) as num from PersonalAccount where Active = 1;");
    $sth->execute();
    $total_active_app_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    if (!empty($disRemoveIds)) {
        $sth = $ods_db->prepare("select count(1) as num from PersonalAccount P left join Account A on A.ID = P.ParentID left join Account B on B.ID = A. ParentID where (B.ID in ({$disRemoveIds})) and P.Active = 1;");
        $sth->execute();
        $remove_total_active_app_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        $total_active_app_num = $total_active_app_num - $remove_total_active_app_num;
    }

    $data_type = 11;
    $sth = $dw_db->prepare("INSERT INTO  GlobalRealTimeData(`Region`,`DataType`,`Num`) VALUES (:region, :type, :total_active_app_num) ON DUPLICATE KEY UPDATE Num = :total_active_app_num");
    $sth->bindParam(':total_active_app_num', $total_active_app_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':type', $data_type, PDO::PARAM_INT);
    $sth->execute();
}
function TodayActiveApp($REGION)
{
    //从业务数据库里面查询数据
    $ods_db = getODSDB();
    $dw_db = getDWDB();
    $today = date("Y-m-d 00:00:00");
    global $dis_remove_top_list;
    $disRemoveIds = join(',', $dis_remove_top_list);

    $sth = $ods_db->prepare("select count(1) as num from PersonalAccount where ActiveTime > :today and Active = 1;");
    $sth->bindParam(':today', $today, PDO::PARAM_STR);
    $sth->execute();
    $today_active_app_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

    if (!empty($disRemoveIds)) {
        $sth = $ods_db->prepare("select count(1) as num from PersonalAccount P left join Account A on A.ID = P.ParentID left join Account B on B.ID = A. ParentID where (B.ID in ({$disRemoveIds})) and P.ActiveTime > :today and P.Active = 1;");
        $sth->bindParam(':today', $today, PDO::PARAM_STR);
        $sth->execute();
        $remove_today_active_app_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        $today_active_app_num = $today_active_app_num - $remove_today_active_app_num;
    }

    $data_type = 9;
    $sth = $dw_db->prepare("INSERT INTO  GlobalRealTimeData(`Region`,`DataType`,`Num`) VALUES (:region, :type, :today_active_app_num) ON DUPLICATE KEY UPDATE Num = :today_active_app_num");
    $sth->bindParam(':today_active_app_num', $today_active_app_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':type', $data_type, PDO::PARAM_INT);
    $sth->execute();
}
function TotalRtsp($REGION)
{
    $ods_db = getODSDB();
    $dw_db = getDWDB();

    $sth = $ods_db->prepare("select count(1) as num From AppManualRtsp;");
    $sth->execute();
    $total_rtsp_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

    $data_type = 7;
    $sth = $dw_db->prepare("INSERT INTO  GlobalRealTimeData(`Region`,`DataType`,`Num`) VALUES (:region, :type, :total_rtsp_num) ON DUPLICATE KEY UPDATE Num = :total_rtsp_num");
    $sth->bindParam(':total_rtsp_num', $total_rtsp_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':type', $data_type, PDO::PARAM_INT);
    $sth->execute();
}

function TodayRtsp($REGION)
{
    $ods_db = getODSDB();
    $dw_db = getDWDB();
    $today = date("Y-m-d 00:00:00");

    $sth = $ods_db->prepare("select count(1) as num From AppManualRtsp where CreateTime > :today;");
    $sth->bindParam(':today', $today, PDO::PARAM_STR);
    $sth->execute();
    $today_rtsp_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

    $data_type = 6;
    $sth = $dw_db->prepare("INSERT INTO  GlobalRealTimeData(`Region`,`DataType`,`Num`) VALUES (:region, :type, :today_rtsp_num) ON DUPLICATE KEY UPDATE Num = :today_rtsp_num");
    $sth->bindParam(':today_rtsp_num', $today_rtsp_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':type', $data_type, PDO::PARAM_INT);
    $sth->execute();

}
//有月租的家庭数
function TotalFeeFamilys($REGION)
{
    //从业务数据库里面查询数据
    $ods_db = getODSDB();
    $dw_db = getDWDB();
    $now = date("Y-m-d H:i:s");
    global $dis_remove_top_list;
    $disRemoveIds = join(',', $dis_remove_top_list);

    $sth = $ods_db->prepare("select count(1) as num from PersonalAccount where ((ActiveTime is NULL and CreateTime < '2019-09-01 00:00:00' and (Role = 10 or Role = 20) and Active = 1) or (ActiveTime < :now and (Role = 10 or Role = 20) and Active = 1  and Special = 0)) and ExpireTime < '2029-01-01 00:00:00';");
    $sth->bindParam(':now', $now, PDO::PARAM_STR);
    $sth->execute();
    $total_active_family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    if (!empty($disRemoveIds)) {
        $sth = $ods_db->prepare("select count(1) as num from PersonalAccount P left join Account A on A.ID = P.ParentID left join Account B on B.ID = A. ParentID where (B.ID in ({$disRemoveIds})) and ((P.ActiveTime is NULL and P.CreateTime < '2019-09-01 00:00:00' and (P.Role = 10 or P.Role = 20) and P.Active = 1) or (P.ActiveTime < :now and (P.Role = 10 or P.Role = 20) and P.Active = 1  and P.Special = 0)) and P.ExpireTime < '2029-01-01 00:00:00';");
        $sth->bindParam(':now', $now, PDO::PARAM_STR);
        $sth->execute();
        $remove_total_active_family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        $total_active_family_num = $total_active_family_num - $remove_total_active_family_num;
    }

    $data_type = 12;
    $sth = $dw_db->prepare("INSERT INTO  GlobalRealTimeData(`Region`,`DataType`,`Num`) VALUES (:region, :type, :total_active_family_num) ON DUPLICATE KEY UPDATE Num = :total_active_family_num");
    $sth->bindParam(':total_active_family_num', $total_active_family_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':type', $data_type, PDO::PARAM_INT);
    $sth->execute();
}

function TotalActiveOfficer($REGION)
{
    //从业务数据库里面查询数据
    $ods_db = getODSDB();
    $dw_db = getDWDB();
    $now = date("Y-m-d H:i:s");
    global $dis_remove_top_list;
    $disRemoveIds = join(',', $dis_remove_top_list);

    $sth = $ods_db->prepare("select count(1) as num from PersonalAccount where (ActiveTime < :now and (Role = 30 or Role = 31) and Active = 1);");
    $sth->bindParam(':now', $now, PDO::PARAM_STR);
    $sth->execute();
    $total_active_family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    if (!empty($disRemoveIds)) {
        $sth = $ods_db->prepare("select count(1) as num from PersonalAccount P left join Account A on A.ID = P.ParentID left join Account B on B.ID = A. ParentID where (B.ID in ({$disRemoveIds})) and (P.ActiveTime < :now and (P.Role = 30 or P.Role = 31) and P.Active = 1);");
        $sth->bindParam(':now', $now, PDO::PARAM_STR);
        $sth->execute();
        $remove_total_active_family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        $total_active_family_num = $total_active_family_num - $remove_total_active_family_num;
    }


    $data_type = 13;
    $sth = $dw_db->prepare("INSERT INTO  GlobalRealTimeData(`Region`,`DataType`,`Num`) VALUES (:region, :type, :total_active_family_num) ON DUPLICATE KEY UPDATE Num = :total_active_family_num");
    $sth->bindParam(':total_active_family_num', $total_active_family_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':type', $data_type, PDO::PARAM_INT);
    $sth->execute();


}

TodayDoorNum($REGION);
echo '1';
TodayCallNum($REGION);
echo '2';
TotalOnlineDevices($REGION);
echo '3';
TotalRegisterDevices($REGION);
echo '4';
TodayRegisterDevices($REGION);
TotalActiveFamily($REGION);
echo '5';
TodayActiveFamily($REGION);
echo '6';
TotalActiveApp($REGION);
TodayActiveApp($REGION);
echo '7';
TotalRtsp($REGION);
echo '8';
TodayRtsp($REGION);
echo '9';
TotalFeeFamilys($REGION);
echo '10';
TotalActiveOfficer($REGION);
echo '11';
?>
