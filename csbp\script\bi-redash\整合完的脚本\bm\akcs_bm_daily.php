<?php
date_default_timezone_set('PRC');

require('/home/<USER>/akcs_dw_config.php');

//实时云服务收费金额
function TotalBmChargeNum()
{
    //从业务数据库里面查询数据
    $ods_db = getODSDB();
    $dw_db = getDWDB();

    $arr = array('na','eu','as','jp');
    foreach ($arr as $client) {
        $REGION = null;
        $sth = $ods_db->prepare("select sum((FinalPrice - CouponCount)/100) as fee_count from Orders where Client = :cli and (Status = 1 or Status = 4) and (FinalPrice - CouponCount) > 0 and PayPlatOrder != '';");
        $sth->bindParam(':cli', $client, PDO::PARAM_STR);
        $sth->execute();
        $fee_count = $sth->fetch(PDO::FETCH_ASSOC)['fee_count'];
        if($client == 'na'){
            $REGION = 'USA';
        }elseif($client == 'eu'){
            $REGION = 'EUR';
        }elseif($client == 'as'){
            $REGION = 'ASIA';
        }else{
            $REGION = 'JPN';
        }
        $sth = $dw_db->prepare("INSERT INTO GlobalRealTimeData(`Region`,`DataType`,`Num`) VALUES (:region, 14, :fee_count) ON DUPLICATE KEY UPDATE Num = :fee_count");
        $sth->bindParam(':fee_count', $fee_count, PDO::PARAM_INT);
        $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
        $sth->execute();
    }
}

//每月新增云服务收费金额
function MonthlyBmChargeNum()
{
    //从业务数据库里面查询数据
    $ods_db = getODSDB();
    $dw_db = getDWDB();
    
    //如果是1日，则对上个月的数据进行补齐
    if(date("Y-m-d") == date('Y-m-01'))
    {
        $timeend = date("Y-m-d 00:00:00");//本月第一天
        $timestart = date('Y-m-d 00:00:00', strtotime(date('Y-m-01') . '-1 month')); // 计算出上月第一天
        $year_month = date("Y-m",strtotime($timestart));
    }
    else
    {
        //统计的是今天0点的数据
        $timeend = date("Y-m-d 00:00:00");
        $timestart_t= date("Y-m");
        $timestart = $timestart_t .'-01 00:00:00';
        $year_month = date("Y-m");
    }
    $arr = array('na','eu','as','jp');
    foreach ($arr as $client) {

        $sth = $ods_db->prepare("select sum((FinalPrice - CouponCount)/100) as fee_count from Orders where (CreateTime between '".$timestart."' and '".$timeend."') and Client = :cli and (Status = 1 or Status = 4) and (FinalPrice - CouponCount) > 0 and PayPlatOrder != '';");
        $sth->bindParam(':cli', $client, PDO::PARAM_STR);
        $sth->execute();
        $fee_count = $sth->fetch(PDO::FETCH_ASSOC)['fee_count'];
        if($fee_count == null){
            $fee_count = 0; 
        }
        if($client == 'na'){
            $REGION = 'USA';
        }elseif($client == 'eu'){
            $REGION = 'EUR';
        }elseif($client == 'as'){
            $REGION = 'ASIA';
        }else{
            $REGION = 'JPN';
        }
        

        $sth = $dw_db->prepare("INSERT INTO GlobalServiceCharge(`Region`,`DateTime`,`Num`) VALUES (:region, :time, :fee_count) ON DUPLICATE KEY UPDATE Num = :fee_count");
        $sth->bindParam(':fee_count', $fee_count, PDO::PARAM_INT);
        $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
        $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
        $sth->execute();
    }
}
TotalBmChargeNum();
MonthlyBmChargeNum();
?>
