<?php
date_default_timezone_set('PRC');
const STATIS_FILE = "/home/<USER>/akcs_bm_subers_statistics.csv";//产品要的每周最新云服务线上收费数据与收费家庭数据
shell_exec("touch ". STATIS_FILE);
chmod(STATIS_FILE, 0777);
if (file_exists(STATIS_FILE)) {
    //unlink(STATIS_FILE);
    shell_exec("echo > ". STATIS_FILE);
} 

function STATIS_TRACE($content)
{
	file_put_contents(STATIS_FILE, $content, FILE_APPEND);
	file_put_contents(STATIS_FILE, "\n", FILE_APPEND);
}
function getDB()
{
    $dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCSDW";
    $dbport = 3306;
    
    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}
$db = getDB(); 
$static_str = 'subscribers' . ',' .'本月已新增月费家庭'. ',' . '总月费家庭'. ',' . '本月线上已收费金额'. ',' .'总线上收费金额' . ',';
STATIS_TRACE($static_str);

$regions=array("USA"=>"美国云","EUR"=>"欧洲云","ASIA"=>"新加坡云","JPN"=>"日本云");

foreach($regions as $key=>$value) {
    $REGION = $key;

    //如果是1日,则对上个月的数据进行补齐
    if(date("Y-m-d") == date('Y-m-01'))
    {
        $timeend = date("Y-m-d 00:00:00");//本月第一天
        $timestart = date('Y-m-d 00:00:00', strtotime(date('Y-m-01') . '-1 month')); // 计算出上月第一天
        $year_month = date("Y-m",strtotime($timestart));
    }
    else
    {
        //统计的是今天0点的数据
        $timeend = date("Y-m-d 00:00:00");
        $timestart_t= date("Y-m");
        $timestart = $timestart_t .'-01 00:00:00';
        $year_month = date("Y-m");
    }
    //step1: 获取本月实时云收费数据
    $sth = $db->prepare("select Num from GlobalServiceCharge where Region = :region and DateTime = :year_month;)");
    $sth->bindParam(':year_month', $year_month, PDO::PARAM_STR);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->execute();
    $monthly_service_charge_num = $sth->fetch(PDO::FETCH_ASSOC)['Num'] ;

    //step2: 获取当前实时云收费总金额
    $sth = $db->prepare("select Num from GlobalRealTimeData where Region = :region and DataType = 14;)");
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->execute();
    $total_service_charge_num = $sth->fetch(PDO::FETCH_ASSOC)['Num'];

    //step4: 获取本月实时云收费数据
    $sth = $db->prepare("select Num from GlobalFeeFamily where Region = :region and DateTime = :year_month;)");
    $sth->bindParam(':year_month', $year_month, PDO::PARAM_STR);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->execute();
    $monthly_fee_family_num = $sth->fetch(PDO::FETCH_ASSOC)['Num'];

    //step3: 获取当前实时云月租家庭数
    $sth = $db->prepare("select Num from GlobalRealTimeData where Region = :region and DataType = 12;)");
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->execute();
    $total_fee_family_num = $sth->fetch(PDO::FETCH_ASSOC)['Num'];


    $static_str = $value . ',' .$monthly_fee_family_num. ',' . $total_fee_family_num. ',' . $monthly_service_charge_num. ',' .$total_service_charge_num . ',';
    STATIS_TRACE($static_str);
}

?>
