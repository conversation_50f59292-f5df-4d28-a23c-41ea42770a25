<?php
/**
 * @description: 统计每个云的门禁设备数量，格式： 社区总门禁数 单住户总门禁数 办公总门禁数
 * @author: csc 2023/1/16 10:53
 * @lastEditors: csc 2023/1/16 10:53
 */

date_default_timezone_set('PRC');

require('/home/<USER>/akcs_dw_config.php');
//区域
$REGION = null;
if ($argc == 2) {
    $REGION = $argv[1];
} else {
    echo 'use: akcs_business_access_count_once.php  usa/eur/asia';
    exit;
}
$dis_top_list = null;
$dw_db = null;
$ods_db = null;
if($REGION == 'USA')
{
    $dw_db = getUSADWDB();
}
else if($REGION == 'EUR')
{
    $dw_db = getEURDWDB();
}
else if($REGION == 'ASIA')
{
    $dw_db = getASIADWDB();
}
else if($REGION == 'JPN')
{
    $dw_db = getJPNDWDB();
}
else if($REGION == 'LOCAL')
{
    $dw_db = getLOCALDWDB();
}

//查询不需要统计的dis
$dis_remove_top_list = [];
if (null !== $dw_db) {
    $ods_db = getODSDB();
    $sth_dis = $dw_db->prepare("select Dis from DisListRemove;");
    $sth_dis->execute();
    $dis_list = $sth_dis->fetchALL(PDO::FETCH_ASSOC);
    foreach ($dis_list as $row => $dis)
    {
        $dis_acc = $dis['Dis'];
        $sth = $ods_db->prepare("select ID from Account where Account = :dis and Grade = 11");
        $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
        $sth->execute();
        $dis_id = $sth->fetch(PDO::FETCH_ASSOC)['ID'];//fetch 不是 fetchALL
        if (empty($dis_id)) {
            continue;
        }
        $dis_remove_top_list[$dis_acc] = $dis_id;
    }
}

$ods_db = getODSDB();

$disRemoveIds = join(',', $dis_remove_top_list);


//社区门禁数量
$sth = $ods_db->prepare("select count(1) as num from Devices where ProjectType = 0 and Type = 50");
$sth->execute();
$comm_num = intval($sth->fetch(PDO::FETCH_ASSOC)['num']);
if (!empty($disRemoveIds)) {
    $sth = $ods_db->prepare("select count(1) as num from Devices D left join Account A on A.ID = D.MngAccountID left join Account B on B.ID = A.ParentID where B.ID in ({$disRemoveIds}) and D.ProjectType = 0 and D.Type = 50");
    $sth->execute();
    $remove_comm_num = intval($sth->fetch(PDO::FETCH_ASSOC)['num']);
    $comm_num = $comm_num - $remove_comm_num;
}

//办公门禁数量
$sth = $ods_db->prepare("select count(1) as num from Devices where ProjectType = 1 and Type = 50");
$sth->execute();
$office_num = intval($sth->fetch(PDO::FETCH_ASSOC)['num']);
if (!empty($disRemoveIds)) {
    $sth = $ods_db->prepare("select count(1) as num from Devices D left join Account A on A.ID = D.MngAccountID left join Account B on B.ID = A.ParentID where B.ID in ({$disRemoveIds}) and D.ProjectType = 1 and D.Type = 50");
    $sth->execute();
    $remove_office_num = intval($sth->fetch(PDO::FETCH_ASSOC)['num']);
    $office_num = $office_num - $remove_office_num;
}

//单住户门禁数量
$sth = $ods_db->prepare("select count(1) as num from PersonalDevices where Type = 50");
$sth->execute();
$single_num = intval($sth->fetch(PDO::FETCH_ASSOC)['num']);
if (!empty($disRemoveIds)) {
    $sth = $ods_db->prepare("select count(1) as num from PersonalDevices D left join Account A on A.Account = D.Community left join Account B on B.ID = A.ParentID where B.ID in ({$disRemoveIds}) and D.Type = 50");
    $sth->execute();
    $remove_single_num = intval($sth->fetch(PDO::FETCH_ASSOC)['num']);
    $single_num = $single_num - $remove_single_num;
}

$fileName = '/tmp/akcs_'.$REGION.'_access_count.csv';
file_put_contents($fileName, "Single Access Num,Community Access Num,Office Access Num");
file_put_contents($fileName,"\n{$comm_num},{$office_num},{$single_num}", FILE_APPEND);
echo '执行成功，生成的文件路径为:'. $fileName . "\n";