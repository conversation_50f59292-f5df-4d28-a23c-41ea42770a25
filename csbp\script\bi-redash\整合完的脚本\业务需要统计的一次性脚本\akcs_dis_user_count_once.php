<?php
//一次性脚本-统计指定时间内新建的社区，每个社区总共创建了多少个帐号，激活了多少帐号

date_default_timezone_set('PRC');

require('/home/<USER>/akcs_dw_config.php');
//区域
$REGION = null;
if ($argc == 2) {
    $REGION = $argv[1];
} else {
    echo 'use: akcs_dis_user_count_once.php  usa/eur/asia';
    exit;
}
$dis_top_list = null;
$dw_db = null;
$ods_db = null;
if($REGION == 'USA')
{
    $dw_db = getUSADWDB();
}
else if($REGION == 'EUR')
{
    $dw_db = getEURDWDB();
}
else if($REGION == 'ASIA')
{
    $dw_db = getASIADWDB();
}
else if($REGION == 'JPN')
{
    $dw_db = getJPNDWDB();
}
else if($REGION == 'LOCAL')
{
    $dw_db = getLOCALDWDB();
}

$ods_db = getODSDB();

//统计时间段内新建的社区
$startTime = '2022-11-01 00:00:00';
$endTime = '2022-12-31 23:59:59';
$sth = $ods_db->prepare("select ID,ParentID,ManageGroup,Location,CreateTime from Account where Grade = 21 and (CreateTime between '{$startTime}' and '{$endTime}') order by ParentID asc, ManageGroup asc, ID asc;");
$sth->execute();
$commInfo = $sth->fetchALL(PDO::FETCH_ASSOC);
if (empty($commInfo)) {
    echo '没有符合条件的新建社区';
    exit;
}

//查询所有dis的信息
$disIds = array_unique(array_column($commInfo, 'ParentID'));
$disIds = join(',', $disIds);
$sth = $ods_db->prepare("select ID,Account from Account where ID in ($disIds)");
$sth->execute();
$disInfo = $sth->fetchALL(PDO::FETCH_ASSOC);
$disInfo = array_column($disInfo, null, 'ID');

//查询所有ins信息
$insIds = array_unique(array_column($commInfo, 'ManageGroup'));
$insIds = join(',', $insIds);
$sth = $ods_db->prepare("select ID,Account from Account where ID in ($insIds)");
$sth->execute();
$insInfo = $sth->fetchALL(PDO::FETCH_ASSOC);
$insInfo = array_column($insInfo, null, 'ID');

$fileName = '/tmp/akcs_'.$REGION.'_dis_register_user.csv';
file_put_contents($fileName, "Dis,Ins,CommunityName,CreateTime,Total Family Num,Actived Family Num,Percent");
//查询每个小区至今的家庭数和激活家庭数
foreach ($commInfo as $info) {
    $dis = isset($disInfo[$info['ParentID']]) ? $disInfo[$info['ParentID']]['Account'] : '';
    $ins = isset($insInfo[$info['ManageGroup']]) ? $insInfo[$info['ManageGroup']]['Account'] : '';
    $communityName = utf8_encode($info['Location']);
    $createTime = $info['CreateTime'];
    //总家庭
    $sth = $ods_db->prepare("select count(1) as num from PersonalAccount where Role = 20 and ParentID = :commID;");
    $sth->bindParam(':commID', $info['ID'], PDO::PARAM_INT);
    $sth->execute();
    $totalNum = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    //已激活家庭
    $sth = $ods_db->prepare("select count(1) as num from PersonalAccount where Role = 20 and ParentID = :commID and Active = 1 and Special = 0;");
    $sth->bindParam(':commID', $info['ID'], PDO::PARAM_INT);
    $sth->execute();
    $activeNum = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    $percent = $totalNum > 0 ? round($activeNum/$totalNum, 3) : 0;
    file_put_contents($fileName,"\n{$dis},{$ins},{$communityName},{$createTime},{$totalNum},{$activeNum},{$percent}", FILE_APPEND);
}
echo '执行成功，生成的文件路径为:'. $fileName . "\n";