<?php
date_default_timezone_set('PRC');

require('/home/<USER>/akcs_dw_config.php');
//区域
$REGION = null;
if ($argc == 2) {
    $REGION = $argv[1];
} else {
    echo 'use: akcs_dw_month.php  usa';
    exit;
}
 $dw_db = null;
 $ods_db = null;
if($REGION == 'USA')
{
    $dw_db = getUSADWDB();
}
else if($REGION == 'EUR')
{
    $dw_db = getEURDWDB();
}
else if($REGION == 'ASIA')
{
    $dw_db = getASIADWDB();
}
else if($REGION == 'LOCAL')
{
    $dw_db = getLOCALDWDB();    
}
else if($REGION == 'CHN')
{
    $dw_db = getCHNDWDB();
}
else if($REGION == 'INDIA')
{
    $dw_db = getINDIADWDB();
}
else if($REGION == 'RU')
{
    $dw_db = getRUDWDB();
}
else if($REGION == 'INC')
{
    $dw_db = getINCDWDB();
}
$ods_db = getODSDB();

//日志表相关已经进行了分库分表，需要写兼容写法查询
function countLogNum($table) {
    try {
        //从业务数据库里面查询数据
        $ods_db = getODSDB();
        //判断日志库功能是否已经上线
        $sth = $ods_db->prepare("show databases like 'LOG'");
        $sth->execute();
        $res = $sth->fetch(PDO::FETCH_ASSOC);

        if (empty($res)) {
            //只需要查询AKCS库的
            return selectLogFromAKCS($table);
        } else {
            $sth = $ods_db->prepare("select * from LOG.LogSlice where LogTableName = :LogTableName");
            $sth->bindParam(':LogTableName', $table, PDO::PARAM_STR);
            $sth->execute();
            $logConfig = $sth->fetch(PDO::FETCH_ASSOC);
            //LOG库上线当月和下个月的1号，需要查询LOG库和AKCS库
            if (date('Y-m') == date('Y-m', strtotime($logConfig['DeliveryTime']))
                or date('Y-m', strtotime(date('Y-m-01') . '-1 month')) == date('Y-m', strtotime($logConfig['DeliveryTime']))) {
                $akcsNum = selectLogFromAKCS($table, true);
                $logNum = selectLogFromLOG($table, $logConfig);
                return $akcsNum + $logNum;
            }
            //LOG库上线2个月后，都只需要查LOG库
            return selectLogFromLOG($table, $logConfig);
        }
    } catch (\Exception $e) {
        echo "统计日志表出错，错误为:\n";
        print_r($e);
    }
}

/**
 * @description: 查询AKCS中LOG的数量
 * @param $table 要查询的表
 * @param $withLOG LOG库功能是否已上线，如果已上线后，AKCS库的日志不会再分表，例如CallHistory表跨月了，也还是这个表名
 * @author: csc 2023/6/28 14:34
 * @lastEditors: csc 2023/6/28 14:34
 */
function selectLogFromAKCS($table, $withLOG = false) {
    $ods_db = getODSDB();
    $where = '';
    if ($table == 'PersonalCapture') {
        $where = ' where (CaptureType = 0 or CaptureType = 1 or CaptureType = 2 or CaptureType = 3 or CaptureType = 4 or CaptureType = 100 or CaptureType = 101)';
    }
    //如果是1日，则对上个月的数据进行补齐
    if(date("Y-m-d") == date('Y-m-01') && !$withLOG)
    {
        $yearmonth = date("Ym",strtotime(date('Y-m-01') . '-1 month'));
        $ym_table = $table . "_".$yearmonth;
        $sth = $ods_db->prepare("select count(1) as num From " .$ym_table . $where);
        $sth->execute();
        $num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    }
    else
    {
        $sth = $ods_db->prepare("select count(1) as num From $table $where;");
        $sth->execute();
        $num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    }
    return intval($num);
}

function selectLogFromLOG($table, $logConfig) {
    $ods_db = getODSDB();
    $sum = 0;
    $where = '';
    if ($table == 'PersonalCapture') {
        $where = ' where (CaptureType = 0 or CaptureType = 1 or CaptureType = 2 or CaptureType = 3 or CaptureType = 4 or CaptureType = 100 or CaptureType = 101)';
    }
    for($i = 0; $i < $logConfig['Delivery']; $i++) {
        //如果是1日，则对上个月的数据进行补齐
        if(date("Y-m-d") == date('Y-m-01'))
        {
            $yearmonth = date("Ym",strtotime(date('Y-m-01') . '-1 month'));
            $ym_table = $table . '_' . $i . '_' . $yearmonth;
            $sth = $ods_db->prepare("select count(1) as num From LOG." .$ym_table . $where);
            $sth->execute();
            $num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        } else {
            $ym_table = $table . '_' . $i;
            $sth = $ods_db->prepare("select count(1) as num From LOG." . $ym_table . $where);
            $sth->execute();
            $num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        }
        $sum += intval($num);
    }

    return $sum;
}

//当月新增呼叫数
function CallNum($REGION)
{
    //从业务数据库里面查询数据
    global $dw_db;

    //如果是1日，则对上个月的数据进行补齐
    if(date("Y-m-d") == date('Y-m-01'))
    {
        $timestart = date('Y-m-d 00:00:00', strtotime(date('Y-m-01') . '-1 month')); // 计算出上月第一天
        $year_month = date("Y-m",strtotime($timestart));
    }
    else
    {
        $year_month = date("Y-m");
    }
    $call_num = countLogNum('CallHistory');

    $sth = $dw_db->prepare("INSERT INTO  GlobalCall(`Region`,`DateTime`,`Num`) VALUES (:region, :time, :call_num) ON DUPLICATE KEY UPDATE Num = :call_num");
    $sth->bindParam(':call_num', $call_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
    $sth->execute();
}
//当月新增开门数，每日统计一次
function DoorNum($REGION)
{
    //从业务数据库里面查询数据
    global $ods_db;
    global $dw_db;

     //如果是1日，则对上个月的数据进行补齐
    if(date("Y-m-d") == date('Y-m-01'))
    {
        $timestart = date('Y-m-d 00:00:00', strtotime(date('Y-m-01') . '-1 month')); // 计算出上月第一天
        $year_month = date("Y-m",strtotime($timestart));
    }
    else
    {
        $year_month = date("Y-m");
    }
    $door_num = countLogNum('PersonalCapture');

    $sth = $dw_db->prepare("INSERT INTO  GlobalOpenDoor(`Region`,`DateTime`,`Num`) VALUES (:region, :time, :door_num) ON DUPLICATE KEY UPDATE Num = :door_num");
    $sth->bindParam(':door_num', $door_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
    $sth->execute();
}
//当月新增开门类型统计
function DoorOpenType($REGION)
{
    global $ods_db;
    global $dw_db;

    //判断日志库功能是否已经上线
    $sth = $ods_db->prepare("show databases like 'LOG'");
    $sth->execute();
    $res = $sth->fetch(PDO::FETCH_ASSOC);
    $logConfig = [];
    if (!empty($res)) {
        $sth = $ods_db->prepare("select * from LOG.LogSlice where LogTableName = 'PersonalCapture'");
        $sth->execute();
        $logConfig = $sth->fetch(PDO::FETCH_ASSOC);
    }

    $capture_types = array(0,1,2,3,4,100,101); 
    foreach ($capture_types as $capture_type)
    {
        $capture_type_num = 0;
        if (empty($logConfig)) {
            $sth = $ods_db->prepare("select count(1) as num From PersonalCapture where CaptureType = :type and Response = 0;");
            $sth->bindParam(':type', $capture_type, PDO::PARAM_INT);
            $sth->execute();
            $capture_type_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        } else {
            for($i = 0; $i < $logConfig['Delivery']; $i++) {
                $table = 'PersonalCapture_'.$i;
                $sth = $ods_db->prepare("select count(1) as num from LOG.{$table} where CaptureType = :type and Response = 0");
                $sth->bindParam(':type', $capture_type, PDO::PARAM_INT);
                $sth->execute();
                $num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
                $capture_type_num += $num;
            }
        }

        $year_month = date("Y-m");
        $sth = $dw_db->prepare("INSERT INTO  GlobalOpenDoorType(`Region`,`DateTime`,`Type`,`Num`) VALUES (:region, :time, :type, :capture_type_num) ON DUPLICATE KEY UPDATE Num = :capture_type_num");
        $sth->bindParam(':capture_type_num', $capture_type_num, PDO::PARAM_INT);
        $sth->bindParam(':type', $capture_type, PDO::PARAM_INT);
        $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
        $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
        $sth->execute();
    }
}
//每月新增激活家庭数
function ActiveFamilyNum($REGION)
{
    //从业务数据库里面查询数据
    global $ods_db;
    global $dw_db;
    
    //如果是1日，则对上个月的数据进行补齐
    if(date("Y-m-d") == date('Y-m-01'))
    {
        $timeend = date("Y-m-d 00:00:00");//本月第一天
        $timestart = date('Y-m-d 00:00:00', strtotime(date('Y-m-01') . '-1 month')); // 计算出上月第一天
        $year_month = date("Y-m",strtotime($timestart));
    }
    else
    {
        //统计的是今天0点的数据
        $timeend = date("Y-m-d 00:00:00");
        $timestart_t= date("Y-m");
        $timestart = $timestart_t .'-01 00:00:00';
        $year_month = date("Y-m");
    }
    $sth = $ods_db->prepare("select count(1) as num from PersonalAccount where (ActiveTime between '".$timestart."' and '".$timeend."') and (Role = 10 or Role = 20) and Active = 1 and Special = 0;");
    $sth->execute();
    $active_family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    

    $sth = $dw_db->prepare("INSERT INTO  GlobalActiveFamily(`Region`,`DateTime`,`Num`) VALUES (:region, :time, :active_family_num) ON DUPLICATE KEY UPDATE Num = :active_family_num");
    $sth->bindParam(':active_family_num', $active_family_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
    $sth->execute();
}

//当月新增开门类型统计
function CallType($REGION)
{
    global $ods_db;
    global $dw_db;

    //判断日志库功能是否已经上线
    $sth = $ods_db->prepare("show databases like 'LOG'");
    $sth->execute();
    $res = $sth->fetch(PDO::FETCH_ASSOC);
    $logConfig = [];
    if (!empty($res)) {
        $sth = $ods_db->prepare("select * from LOG.LogSlice where LogTableName = 'CallHistory'");
        $sth->execute();
        $logConfig = $sth->fetch(PDO::FETCH_ASSOC);
    }

    $call_types = array(1,2,3,4); 
    foreach ($call_types as $call_type)
    {
        $call_type_num = 0;
        if (empty($logConfig)) {
            $sth = $ods_db->prepare("select count(1) as num From CallHistory where CallType = :type;");
            $sth->bindParam(':type', $call_type, PDO::PARAM_INT);
            $sth->execute();
            $call_type_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        } else {
            for($i = 0; $i < $logConfig['Delivery']; $i++) {
                $table = 'CallHistory_'.$i;
                $sth = $ods_db->prepare("select count(1) as num from LOG.$table where CallType = :type");
                $sth->bindParam(':type', $call_type, PDO::PARAM_INT);
                $sth->execute();
                $num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
                $call_type_num += $num;
            }
        }

        $year_month = date("Y-m");
        $sth = $dw_db->prepare("INSERT INTO  GlobalCallType(`Region`,`DateTime`,`Type`,`Num`) VALUES (:region, :time, :type, :call_type_num) ON DUPLICATE KEY UPDATE Num = :call_type_num");
        $sth->bindParam(':call_type_num', $call_type_num, PDO::PARAM_INT);
        $sth->bindParam(':type', $call_type, PDO::PARAM_INT);
        $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
        $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
        $sth->execute();
    }
}
//每月新增激活app
function ActiveAppNum($REGION)
{
    //从业务数据库里面查询数据
    global $ods_db;
    global $dw_db;

    if(date("Y-m-d") == date('Y-m-01'))
    {
        $timeend = date("Y-m-d 00:00:00");//本月第一天
        $timestart = date('Y-m-d 00:00:00', strtotime(date('Y-m-01') . '-1 month')); // 计算出上月第一天
        $year_month = date("Y-m",strtotime($timestart));
    }
    else
    {
        //统计的是今天0点的数据
        $timeend = date("Y-m-d 00:00:00");
        $timestart_t= date("Y-m");
        $timestart = $timestart_t .'-01 00:00:00';
        $year_month = date("Y-m");
    }
    
    $sth_act_family = $ods_db->prepare("select count(1) as count from PersonalAccount where (ActiveTime between '".$timestart."' and '".$timeend."') and Active = 1;");

    $sth_act_family->execute();
    $resultRole = $sth_act_family->fetch(PDO::FETCH_ASSOC);
    $family_active_num = $resultRole['count'];

    //UNIQUE KEY `region_data` (`Region`,`DateTime`)  对应的表格已经通过唯一键来保证只会插入一次，后续的都是更新
    $sth = $dw_db->prepare("INSERT INTO  GlobalActiveApp(`Region`,`DateTime`,`Num`) VALUES (:region, :time, :family_active_num) ON DUPLICATE KEY UPDATE Num = :family_active_num");
    $sth->bindParam(':family_active_num', $family_active_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
    $sth->execute(); 
}

function OnlineDeviceNum($REGION)
{
    //从业务数据库里面查询数据
    global $ods_db;
    global $dw_db;

    if(date("Y-m-d") == date('Y-m-01'))
    {
        $timeend = date("Y-m-d 00:00:00");//本月第一天
        $timestart = date('Y-m-d 00:00:00', strtotime(date('Y-m-01') . '-1 month')); // 计算出上月第一天
        $year_month = date("Y-m",strtotime($timestart));
    }
    else
    {
        //统计的是今天0点的数据
        $timeend = date("Y-m-d 00:00:00");
        $timestart_t= date("Y-m");
        $timestart = $timestart_t .'-01 00:00:00';
        $year_month = date("Y-m");
    }
    
    $sth = $ods_db->prepare("select count(1) as num From Devices where CreateTime between :time_start and :time_end and Status = 1;");
    $sth->bindParam(':time_start', $timestart, PDO::PARAM_INT);
    $sth->bindParam(':time_end', $timeend, PDO::PARAM_STR);
    $sth->execute();
    $register_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    
    $sth = $ods_db->prepare("select count(1) as num From PersonalDevices where CreateTime between :time_start and :time_end and Status = 1;");
    $sth->bindParam(':time_start', $timestart, PDO::PARAM_INT);
    $sth->bindParam(':time_end', $timeend, PDO::PARAM_STR);
    $sth->execute();
    $register_per_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    
    $register_num = $register_devices_num + $register_per_devices_num;
    
    $sth = $dw_db->prepare("INSERT INTO  GlobalOnlineDevice(`Region`,`DateTime`,`Num`) VALUES (:region, :time, :register_num) ON DUPLICATE KEY UPDATE Num = :register_num");
    $sth->bindParam(':register_num', $register_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':time', $year_month, PDO::PARAM_INT);
    $sth->execute();   
}

function RegisterDeviceNum($REGION)
{
    //从业务数据库里面查询数据
    global $ods_db;
    global $dw_db;

    if(date("Y-m-d") == date('Y-m-01'))
    {
        $timeend = date("Y-m-d 00:00:00");//本月第一天
        $timestart = date('Y-m-d 00:00:00', strtotime(date('Y-m-01') . '-1 month')); // 计算出上月第一天
        $year_month = date("Y-m",strtotime($timestart));
    }
    else
    {
        //统计的是今天0点的数据
        $timeend = date("Y-m-d 00:00:00");
        $timestart_t= date("Y-m");
        $timestart = $timestart_t .'-01 00:00:00';
        $year_month = date("Y-m");
    }
    
    $sth = $ods_db->prepare("select count(1) as num From Devices where CreateTime between :time_start and :time_end;");
    $sth->bindParam(':time_start', $timestart, PDO::PARAM_INT);
    $sth->bindParam(':time_end', $timeend, PDO::PARAM_STR);
    $sth->execute();
    $register_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    
    $sth = $ods_db->prepare("select count(1) as num From PersonalDevices where CreateTime between :time_start and :time_end;");
    $sth->bindParam(':time_start', $timestart, PDO::PARAM_INT);
    $sth->bindParam(':time_end', $timeend, PDO::PARAM_STR);
    $sth->execute();
    $register_per_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    
    $register_num = $register_devices_num + $register_per_devices_num;
    
    $sth = $dw_db->prepare("INSERT INTO  GlobalRegisterDevice(`Region`,`DateTime`,`Num`) VALUES (:region, :time, :register_num) ON DUPLICATE KEY UPDATE Num = :register_num");
    $sth->bindParam(':register_num', $register_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':time', $year_month, PDO::PARAM_INT);
    $sth->execute();   
}
//每月新增激活家庭数
function RtspNum($REGION)
{
    //从业务数据库里面查询数据
    global $ods_db;
    global $dw_db;

    if(date("Y-m-d") == date('Y-m-01'))
    {
        $timeend = date("Y-m-d 00:00:00");//本月第一天
        $timestart = date('Y-m-d 00:00:00', strtotime(date('Y-m-01') . '-1 month')); // 计算出上月第一天
        $year_month = date("Y-m",strtotime($timestart));
    }
    else
    {
        //统计的是今天0点的数据
        $timeend = date("Y-m-d 00:00:00");
        $timestart_t= date("Y-m");
        $timestart = $timestart_t .'-01 00:00:00';
        $year_month = date("Y-m");
    }
    
    $sth = $ods_db->prepare("select count(1) as num From AppManualRtsp where MAC between :time_start and :time_end;");
    $sth->bindParam(':time_start', $timestart, PDO::PARAM_STR);
    $sth->bindParam(':time_end', $timeend, PDO::PARAM_STR);
    $sth->execute();
    $rtsp_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    
    $sth = $dw_db->prepare("INSERT INTO  GlobalRtsp(`Region`,`DateTime`,`Num`) VALUES (:region, :time, :rtsp_num) ON DUPLICATE KEY UPDATE Num = :rtsp_num");
    $sth->bindParam(':rtsp_num', $rtsp_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
    $sth->execute();
}

//每月新增月租家庭数
function FeeFamilyNum($REGION)
{
    //从业务数据库里面查询数据
    global $ods_db;
    global $dw_db;

    //如果是1日，则对上个月的数据进行补齐
    if(date("Y-m-d") == date('Y-m-01'))
    {
        $timeend = date("Y-m-d 00:00:00");//本月第一天
        $timestart = date('Y-m-d 00:00:00', strtotime(date('Y-m-01') . '-1 month')); // 计算出上月第一天
        $year_month = date("Y-m",strtotime($timestart));
    }
    else
    {
        //统计的是今天0点的数据
        $timeend = date("Y-m-d 00:00:00");
        $timestart_t= date("Y-m");
        $timestart = $timestart_t .'-01 00:00:00';
        $year_month = date("Y-m");
    }
    $sth = $ods_db->prepare("select count(1) as num from PersonalAccount where (ActiveTime between '".$timestart."' and '".$timeend."') and (Role = 10 or Role = 20) and Active = 1 and Special = 0 and ExpireTime < '2029-01-01 00:00:00';");
    $sth->execute();
    $active_family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];


    $sth = $dw_db->prepare("INSERT INTO GlobalFeeFamily(`Region`,`DateTime`,`Num`) VALUES (:region, :time, :active_family_num) ON DUPLICATE KEY UPDATE Num = :active_family_num");
    $sth->bindParam(':active_family_num', $active_family_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
    $sth->execute();
}

//每月新增办公用户数
function OfficerNum($REGION)
{
    //从业务数据库里面查询数据
    global $ods_db;
    global $dw_db;

    //如果是1日，则对上个月的数据进行补齐
    if(date("Y-m-d") == date('Y-m-01'))
    {
        $timeend = date("Y-m-d 00:00:00");//本月第一天
        $timestart = date('Y-m-d 00:00:00', strtotime(date('Y-m-01') . '-1 month')); // 计算出上月第一天
        $year_month = date("Y-m",strtotime($timestart));
    }
    else
    {
        //统计的是今天0点的数据
        $timeend = date("Y-m-d 00:00:00");
        $timestart_t= date("Y-m");
        $timestart = $timestart_t .'-01 00:00:00';
        $year_month = date("Y-m");
    }
    $sth = $ods_db->prepare("select count(1) as num from PersonalAccount where (ActiveTime between '".$timestart."' and '".$timeend."') and (Role = 30 or Role = 31) and Active = 1;");
    $sth->execute();
    $active_family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];


    $sth = $dw_db->prepare("INSERT INTO GlobalOffice(`Region`,`DateTime`,`Num`) VALUES (:region, :time, :active_family_num) ON DUPLICATE KEY UPDATE Num = :active_family_num");
    $sth->bindParam(':active_family_num', $active_family_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
    $sth->execute();
}

//全球每月业务数据量统计
function CommonCountNum($REGION)
{
    //从业务数据库里面查询数据
    global $ods_db;

    //如果是1日，则对上个月的数据进行补齐
    if(date("Y-m-d") == date('Y-m-01'))
    {
        $timeend = date("Y-m-d 00:00:00");//本月第一天
        $timestart = date('Y-m-d 00:00:00', strtotime(date('Y-m-01') . '-1 month')); // 计算出上月第一天
        $year_month = date("Y-m",strtotime($timestart));
    }
    else
    {
        //统计的是今天0点的数据
        $timeend = date("Y-m-d 00:00:00");
        $timestart_t= date("Y-m");
        $timestart = $timestart_t .'-01 00:00:00';
        $year_month = date("Y-m");
    }
    //单住户总数量
    $sth = $ods_db->prepare("select count(1) as num from PersonalAccount where Role = 10 and Active = 1 and Special = 0;");
    $sth->execute();
    $num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    AddCommonCountData($REGION, $year_month, 1, $num);

    //单住户门口机当月总数量
    $sth = $ods_db->prepare("select count(1) as num From PersonalDevices where (Type = 0 or Type = 1)");
    $sth->execute();
    $num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    AddCommonCountData($REGION, $year_month, 2, $num);

    //单住户室内机当月总数量
    $sth = $ods_db->prepare("select count(1) as num From PersonalDevices where Type = 2");
    $sth->execute();
    $num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    AddCommonCountData($REGION, $year_month, 3, $num);

    //多住户总数量
    $sth = $ods_db->prepare("select count(1) as num from PersonalAccount where Role = 20 and Active = 1 and Special = 0;");
    $sth->execute();
    $num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    AddCommonCountData($REGION, $year_month, 4, $num);

    //多住户门口机当月总数量
    $sth = $ods_db->prepare("select count(1) as num From Devices where (Type = 0 or Type = 1)");
    $sth->execute();
    $num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    AddCommonCountData($REGION, $year_month, 5, $num);

    //多住户室内机当月总数量
    $sth = $ods_db->prepare("select count(1) as num From Devices where Type = 2");
    $sth->execute();
    $num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    AddCommonCountData($REGION, $year_month, 6, $num);
}

//通用插入方法
function AddCommonCountData($region, $datetime, $type, $num)
{
    global $dw_db;
    $sth = $dw_db->prepare("INSERT INTO  GlobalCommonCount(`Region`,`DateTime`,`DataType`,`Num`) VALUES (:region, :datetime, :type, :num) ON DUPLICATE KEY UPDATE Num = :num");
    $sth->bindParam(':region', $region, PDO::PARAM_STR);
    $sth->bindParam(':datetime', $datetime, PDO::PARAM_STR);
    $sth->bindParam(':num', $num, PDO::PARAM_INT);
    $sth->bindParam(':type', $type, PDO::PARAM_INT);
    $sth->execute();
}

CallNum($REGION);
DoorNum($REGION);
DoorOpenType($REGION);
ActiveFamilyNum($REGION);
ActiveAppNum($REGION);
CallType($REGION);
OnlineDeviceNum($REGION);
RegisterDeviceNum($REGION);
RtspNum($REGION);
FeeFamilyNum($REGION);
OfficerNum($REGION);
CommonCountNum($REGION);
?>
