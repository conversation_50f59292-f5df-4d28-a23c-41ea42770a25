<?php
//俄罗斯云一次性脚本

date_default_timezone_set('PRC');

require('/home/<USER>/akcs_dw_config.php');
//区域
$REGION = null;
if ($argc == 2) {
    $REGION = $argv[1];
} else {
    echo 'use: akcs_dw_month.php  usa';
    exit;
}

//查询不需要统计的dis
$dis_remove_top_list = [];
$dw_db = null;
$ods_db = null;
if($REGION == 'USA')
{
    $dw_db = getUSADWDB();
}
else if($REGION == 'EUR')
{
    $dw_db = getEURDWDB();
}
else if($REGION == 'ASIA')
{
    $dw_db = getASIADWDB();
}
else if($REGION == 'LOCAL')
{
    $dw_db = getLOCALDWDB();
}
else if($REGION == 'CHN')
{
    $dw_db = getCHNDWDB();
}
else if($REGION == 'INDIA')
{
    $dw_db = getINDIADWDB();
}
else if($REGION == 'RU')
{
    $dw_db = getRUDWDB();
}
else if($REGION == 'INC')
{
    $dw_db = getINCDWDB();
}

$ods_db = getODSDB();
if (null !== $dw_db) {
    $ods_db = getODSDB();
    $sth_dis = $dw_db->prepare("select Dis from DisListRemove;");
    $sth_dis->execute();
    $dis_list = $sth_dis->fetchALL(PDO::FETCH_ASSOC);
    foreach ($dis_list as $row => $dis)
    {
        $dis_acc = $dis['Dis'];
        $sth = $ods_db->prepare("select ID from Account where Account = :dis and Grade = 11");
        $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
        $sth->execute();
        $dis_id = $sth->fetch(PDO::FETCH_ASSOC)['ID'];//fetch 不是 fetchALL
        if (empty($dis_id)) {
            continue;
        }
        $dis_remove_top_list[$dis_acc] = $dis_id;
    }
}

//每月新增激活家庭数
function ActiveFamilyNum($REGION)
{
    //从业务数据库里面查询数据
    global $ods_db;
    global $dw_db;

    global $dis_remove_top_list;
    $disRemoveIds = join(',', $dis_remove_top_list);

    global $year_month;
    $timestart = date("Y-m-01 00:00:00", strtotime($year_month));//本月第一天
    $timeend = date("Y-m-t 23:59:59", strtotime($year_month));//本月最后一天

    $sth = $ods_db->prepare("select count(1) as num from PersonalAccount where (ActiveTime between '".$timestart."' and '".$timeend."') and (Role = 10 or Role = 20) and Active = 1 and Special = 0;");
    $sth->execute();
    $active_family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    if (!empty($disRemoveIds)) {
        $sth = $ods_db->prepare("select count(1) as num from PersonalAccount P left join Account A on A.ID = P.ParentID left join Account B on B.ID = A. ParentID where (B.ID in ({$disRemoveIds})) and (P.ActiveTime between '".$timestart."' and '".$timeend."') and (P.Role = 10 or P.Role = 20) and P.Active = 1 and P.Special = 0;");
        $sth->execute();
        $remove_active_family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        $active_family_num = $active_family_num - $remove_active_family_num;
    }

    $sth = $dw_db->prepare("INSERT INTO  GlobalActiveFamily(`Region`,`DateTime`,`Num`) VALUES (:region, :time, :active_family_num) ON DUPLICATE KEY UPDATE Num = :active_family_num");
    $sth->bindParam(':active_family_num', $active_family_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
    $sth->execute();
}

//每月新增激活app
function ActiveAppNum($REGION)
{
    //从业务数据库里面查询数据
    global $ods_db;
    global $dw_db;

    global $dis_remove_top_list;
    $disRemoveIds = join(',', $dis_remove_top_list);

    global $year_month;
    $timestart = date("Y-m-01 00:00:00", strtotime($year_month));//本月第一天
    $timeend = date("Y-m-t 23:59:59", strtotime($year_month));//本月最后一天

    $sth_act_family = $ods_db->prepare("select count(1) as count from PersonalAccount where (ActiveTime between '".$timestart."' and '".$timeend."') and Active = 1;");

    $sth_act_family->execute();
    $resultRole = $sth_act_family->fetch(PDO::FETCH_ASSOC);
    $family_active_num = $resultRole['count'];
    if (!empty($disRemoveIds)) {
        $sth_act_family = $ods_db->prepare("select count(1) as count from PersonalAccount P left join Account A on A.ID = P.ParentID left join Account B on B.ID = A. ParentID where (B.ID in ({$disRemoveIds})) and (P.ActiveTime between '".$timestart."' and '".$timeend."') and P.Active = 1;");

        $sth_act_family->execute();
        $resultRole = $sth_act_family->fetch(PDO::FETCH_ASSOC);
        $remove_family_active_num = $resultRole['count'];
        $family_active_num = $family_active_num - $remove_family_active_num;
    }

    //UNIQUE KEY `region_data` (`Region`,`DateTime`)  对应的表格已经通过唯一键来保证只会插入一次，后续的都是更新
    $sth = $dw_db->prepare("INSERT INTO  GlobalActiveApp(`Region`,`DateTime`,`Num`) VALUES (:region, :time, :family_active_num) ON DUPLICATE KEY UPDATE Num = :family_active_num");
    $sth->bindParam(':family_active_num', $family_active_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
    $sth->execute();
}

function RegisterDeviceNum($REGION)
{
    //从业务数据库里面查询数据
    global $ods_db;
    global $dw_db;

    global $dis_remove_top_list;
    $disRemoveIds = join(',', $dis_remove_top_list);

    global $year_month;
    $timestart = date("Y-m-01 00:00:00", strtotime($year_month));//本月第一天
    $timeend = date("Y-m-t 23:59:59", strtotime($year_month));//本月最后一天

    $sth = $ods_db->prepare("select count(1) as num From Devices where CreateTime between :time_start and :time_end;");
    $sth->bindParam(':time_start', $timestart, PDO::PARAM_INT);
    $sth->bindParam(':time_end', $timeend, PDO::PARAM_STR);
    $sth->execute();
    $register_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

    if (!empty($disRemoveIds)) {
        $sth = $ods_db->prepare("select count(1) as num From Devices D left join Account A on A.ID = D.MngAccountID left join Account B on B.ID = A.ParentID where B.ID in ({$disRemoveIds}) and (D.CreateTime between :time_start and :time_end);");
        $sth->bindParam(':time_start', $timestart, PDO::PARAM_INT);
        $sth->bindParam(':time_end', $timeend, PDO::PARAM_STR);
        $sth->execute();
        $remove_register_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        $register_devices_num = $register_devices_num - $remove_register_devices_num;
    }

    $sth = $ods_db->prepare("select count(1) as num From PersonalDevices where CreateTime between :time_start and :time_end;");
    $sth->bindParam(':time_start', $timestart, PDO::PARAM_INT);
    $sth->bindParam(':time_end', $timeend, PDO::PARAM_STR);
    $sth->execute();
    $register_per_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

    if (!empty($disRemoveIds)) {
        $sth = $ods_db->prepare("select count(1) as num From PersonalDevices D left join Account A on A.Account = D.Community left join Account B on B.ID = A.ParentID where B.ID in ({$disRemoveIds}) and (D.CreateTime between :time_start and :time_end);");
        $sth->bindParam(':time_start', $timestart, PDO::PARAM_INT);
        $sth->bindParam(':time_end', $timeend, PDO::PARAM_STR);
        $sth->execute();
        $remove_register_per_devices_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        $register_per_devices_num = $register_per_devices_num - $remove_register_per_devices_num;
    }

    $register_num = $register_devices_num + $register_per_devices_num;

    $sth = $dw_db->prepare("INSERT INTO  GlobalRegisterDevice(`Region`,`DateTime`,`Num`) VALUES (:region, :time, :register_num) ON DUPLICATE KEY UPDATE Num = :register_num");
    $sth->bindParam(':register_num', $register_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':time', $year_month, PDO::PARAM_INT);
    $sth->execute();
}

//每月新增月租家庭数
function FeeFamilyNum($REGION)
{
    //从业务数据库里面查询数据
    global $ods_db;
    global $dw_db;

    global $dis_remove_top_list;
    $disRemoveIds = join(',', $dis_remove_top_list);

    global $year_month;
    $timestart = date("Y-m-01 00:00:00", strtotime($year_month));//本月第一天
    $timeend = date("Y-m-t 23:59:59", strtotime($year_month));//本月最后一天

    $sth = $ods_db->prepare("select count(1) as num from PersonalAccount where (ActiveTime between '".$timestart."' and '".$timeend."') and (Role = 10 or Role = 20) and Active = 1 and Special = 0 and ExpireTime < '2029-01-01 00:00:00';");
    $sth->execute();
    $active_family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

    if (!empty($disRemoveIds)) {
        $sth = $ods_db->prepare("select count(1) as num from PersonalAccount P left join Account A on A.ID = P.ParentID left join Account B on B.ID = A. ParentID where (B.ID in ({$disRemoveIds})) and (P.ActiveTime between '".$timestart."' and '".$timeend."') and (P.Role = 10 or P.Role = 20) and P.Active = 1 and P.Special = 0 and P.ExpireTime < '2029-01-01 00:00:00';");
        $sth->execute();
        $remove_active_family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        $active_family_num = $active_family_num - $remove_active_family_num;
    }


    $sth = $dw_db->prepare("INSERT INTO GlobalFeeFamily(`Region`,`DateTime`,`Num`) VALUES (:region, :time, :active_family_num) ON DUPLICATE KEY UPDATE Num = :active_family_num");
    $sth->bindParam(':active_family_num', $active_family_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
    $sth->execute();
}

//每月新增办公用户数
function OfficerNum($REGION)
{
    //从业务数据库里面查询数据
    global $ods_db;
    global $dw_db;

    global $dis_remove_top_list;
    $disRemoveIds = join(',', $dis_remove_top_list);

    global $year_month;
    $timestart = date("Y-m-01 00:00:00", strtotime($year_month));//本月第一天
    $timeend = date("Y-m-t 23:59:59", strtotime($year_month));//本月最后一天

    $sth = $ods_db->prepare("select count(1) as num from PersonalAccount where (ActiveTime between '".$timestart."' and '".$timeend."') and (Role = 30 or Role = 31) and Active = 1;");
    $sth->execute();
    $active_family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

    if (!empty($disRemoveIds)) {
        $sth = $ods_db->prepare("select count(1) as num from PersonalAccount P left join Account A on A.ID = P.ParentID left join Account B on B.ID = A. ParentID where (B.ID in ({$disRemoveIds})) and (P.ActiveTime between '".$timestart."' and '".$timeend."') and (P.Role = 30 or P.Role = 31) and P.Active = 1;");
        $sth->execute();
        $remove_active_family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        $active_family_num = $active_family_num - $remove_active_family_num;
    }


    $sth = $dw_db->prepare("INSERT INTO GlobalOffice(`Region`,`DateTime`,`Num`) VALUES (:region, :time, :active_family_num) ON DUPLICATE KEY UPDATE Num = :active_family_num");
    $sth->bindParam(':active_family_num', $active_family_num, PDO::PARAM_INT);
    $sth->bindParam(':region', $REGION, PDO::PARAM_STR);
    $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
    $sth->execute();
}

$startMonth = '2023-02';
$endMonth = '2023-07';
$months = [];
while (strtotime($startMonth) <= strtotime($endMonth)) {
    $months[] = $startMonth;
    $startMonth = date('Y-m', (strtotime('+1 month',strtotime($startMonth))));
}

foreach ($months as $year_month) {
    ActiveFamilyNum($REGION);
    ActiveAppNum($REGION);
    RegisterDeviceNum($REGION);
    FeeFamilyNum($REGION);
    OfficerNum($REGION);
}

?>
