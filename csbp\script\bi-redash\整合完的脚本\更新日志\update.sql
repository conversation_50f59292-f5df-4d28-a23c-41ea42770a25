###############2022.05.16更新 START####################
#区域数据库更新
#没有DisActiveOffice的数据库
DROP TABLE if exists DisActiveOffice;
CREATE TABLE `DisActiveOffice` (
    `ID` int(11) NOT NULL AUTO_INCREMENT,
    `Dis` char(64) DEFAULT '' COMMENT '该套生产环境中的各个Dis账号',
    `DateTime` char(8) DEFAULT '' COMMENT 'YYYY-MM的格式，每日从各Dis中会刷新本月最新数据',
    `Num` int(11) NOT NULL DEFAULT 0 COMMENT '当月新增的增量数据',
    `UpdateTime` TIMESTAMP default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP COMMENT '最新一次记录变更的时间',
    PRIMARY KEY (`ID`),
    UNIQUE KEY `dis_datetime` (`Dis`,`DateTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 comment='Dis办公用户每月增量表趋势图';

#有DisActiveOffice的数据库只需要修改Dis的字段长度即可 例如EMEA所在的
alter table DisActiveOffice modify Dis char(64) default '' null comment '该套生产环境中的各个Dis账号';

DROP TABLE if exists DisFeeFamily;
CREATE TABLE `DisFeeFamily` (
    `ID` int(11) NOT NULL AUTO_INCREMENT,
    `Dis` char(64) DEFAULT '' COMMENT '该套生产环境中的各个Dis账号',
    `DateTime` char(8) DEFAULT '' COMMENT 'YYYY-MM的格式，每日从各套环境中会刷新本月最新数据',
    `Num` int(11) NOT NULL DEFAULT 0 COMMENT '当月新增的增量数据',
    `UpdateTime` TIMESTAMP default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP COMMENT '最新一次记录变更的时间',
    PRIMARY KEY (`ID`),
    UNIQUE KEY `dis_datetime` (`Dis`,`DateTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 comment='Dis月租家庭每月增量表趋势图';

DROP TABLE if exists DisOpenDoorType;
CREATE TABLE `DisOpenDoorType` (
    `ID` int(11) NOT NULL AUTO_INCREMENT,
    `Dis` char(64) DEFAULT '' COMMENT '该套生产环境中的各个Dis账号',
    `DateTime` char(8) DEFAULT '' COMMENT 'YYYY-MM的格式，每日从各套环境中会刷新本月最新数据',
    `Type` int(11) NOT NULL DEFAULT -1 COMMENT '开门类型，具体见业务表的截图类型，与之一一对应',
    `Num` int(11) NOT NULL DEFAULT 0 COMMENT '当月新增的增量数据，每日更新一次',
    `UpdateTime` TIMESTAMP default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP COMMENT '最新一次记录变更的时间',
    PRIMARY KEY (`ID`),
    UNIQUE KEY `dis_datetime` (`Dis`,`DateTime`,Type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 comment='Dis开门类型统计表';

DROP TABLE if exists DisOnlineDevice;
CREATE TABLE `DisOnlineDevice` (
    `ID` int(11) NOT NULL AUTO_INCREMENT,
    `Dis` char(64) DEFAULT '' COMMENT '该套生产环境中的各个Dis账号',
    `DateTime` char(8) DEFAULT '' COMMENT 'YYYY-MM的格式，每日从各套环境中会刷新本月最新数据',
    `Num` int(11) NOT NULL DEFAULT 0 COMMENT '当月新增的增量数据',
    `UpdateTime` TIMESTAMP default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP COMMENT '最新一次记录变更的时间',
    PRIMARY KEY (`ID`),
    UNIQUE KEY `dis_datetime` (`Dis`,`DateTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 comment='Dis在线设备每月增量表';

DROP TABLE if exists DisRegisterDevice;
CREATE TABLE `DisRegisterDevice` (
    `ID` int(11) NOT NULL AUTO_INCREMENT,
    `Dis` char(64) DEFAULT '' COMMENT '该套生产环境中的各个Dis账号',
    `DateTime` char(8) DEFAULT '' COMMENT 'YYYY-MM的格式，每日从各套环境中会刷新本月最新数据',
    `Num` int(11) NOT NULL DEFAULT 0 COMMENT '当月新增的增量数据',
    `UpdateTime` TIMESTAMP default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP COMMENT '最新一次记录变更的时间',
    PRIMARY KEY (`ID`),
    UNIQUE KEY `dis_datetime` (`Dis`,`DateTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 comment='Dis注册设备每月增量表';

DROP TABLE if exists DisList;
CREATE TABLE `DisList` (
    `ID` int(11) NOT NULL AUTO_INCREMENT,
    `Dis` varchar(32) NOT NULL DEFAULT '' COMMENT '需要统计的Dis名称',
    `CreateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`ID`),
    UNIQUE KEY `dis` (`Dis`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='需要统计的Dis';

###############2022.05.16更新 END####################


###############2022.07.20更新 END####################
DROP TABLE if exists DisListRemove;
CREATE TABLE `DisListRemove` (
    `ID` int(11) NOT NULL AUTO_INCREMENT,
    `Dis` varchar(32) NOT NULL DEFAULT '' COMMENT '需要排除统计的Dis名称',
    `CreateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`ID`),
    UNIQUE KEY `dis` (`Dis`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='需要排除统计的Dis';
##以下只需在新加坡云执行
insert into DisListRemove (Dis) values ('wavedge'), ('Link'), ('Jnets'), ('Glamo'), ('IIJ'), ('VALTEC'), ('ODI'), ('Moncable'), ('DigitalPower'), ('Daminn'), ('JTS'), ('DOORCOM'), ('cool_jp'), ('testfee_jp');
###############2022.07.20更新 END####################