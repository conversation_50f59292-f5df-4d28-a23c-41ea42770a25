<?php
date_default_timezone_set('PRC');
require('/home/<USER>/akcs_dw_config.php');
require('/home/<USER>/akcs_emea_dis.php');
//区域
$REGION = null;
if ($argc == 2) {
    $REGION = $argv[1];
} else {
    echo 'use: akcs_dw_month.php  usa/eur/asia';
    exit;
}
 $dis_top_list = null;
 $dw_db = null;
 $ods_db = null;
if($REGION == 'USA')
{
    $dw_db = getUSADWDB();
}
else if($REGION == 'EUR')
{
    $dw_db = getEURDWDB();
}
else if($REGION == 'ASIA')
{
    $dw_db = getASIADWDB();
}
else if($REGION == 'LOCAL')
{
    $dw_db = getLOCALDWDB();    
}
$ods_db = getODSDB();

//EMEA的dis列表
$dis_list = getEMEADisList();
foreach ($dis_list as $row => $dis)
{
    $dis_acc = $dis;
    $sth = $ods_db->prepare("select ID from Account where Account = :dis and Grade = 11");
    $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
    $sth->execute();
    $dis_id = $sth->fetch(PDO::FETCH_ASSOC)['ID'];//fetch 不是 fetchALL
    if($dis_id == null)
    {
        continue;
    }
    $dis_top_list = $dis_top_list . 'B.ID = ' . $dis_id . ' or ';
}
//去掉最后面的 ' or '
$dis_top_list = substr($dis_top_list,0,-4);

function CallNum()
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;
    $table_name = 'CallHistory';    
    $year_months = array("202001","202002","202003","202004","202005","202006","202007","202008","202009","202010","202011"); 
    foreach ($year_months as $year_month)
    {
        $ym_table = $table_name."_".$year_month;
        //从 YYYYMM 改成 YYYY-MM
        $year = substr($year_month,0,4);
        $month = substr($year_month,4);
        $year_month = $year.'-'.$month;
        $year_month_day = $year_month.'-01 00:00:00';

        $sth = $ods_db->prepare("select count(*) as num from " .$ym_table. " C left join Account A on A.ID = C.MngAccountID left join Account B on B.ID = A.ParentID where ( " . $dis_top_list ." ) ");
        $sth->execute();
        $call_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        
        $sth = $dw_db->prepare("INSERT INTO  DisCall(`Dis`,`DateTime`,`Num`) VALUES ('EMEA', :time, :call_num) ON DUPLICATE KEY UPDATE Num = :call_num");
        $sth->bindParam(':call_num', $call_num, PDO::PARAM_INT);
        $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
        $sth->execute(); 
    }
}
function OpenDoorNum()
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;
    $table_name = 'PersonalCapture';    
    $year_months = array("202001","202002","202003","202004","202005","202006","202007","202008","202009","202010","202011"); 
    foreach ($year_months as $year_month)
    {
        $ym_table = $table_name."_".$year_month;
        //从 YYYYMM 改成 YYYY-MM
        $year = substr($year_month,0,4);
        $month = substr($year_month,4);
        $year_month = $year.'-'.$month;
    
        $sth = $ods_db->prepare("select count(*) as num from " .$ym_table. " C left join Account A on A.ID = C.MngAccountID left join Account B on B.ID = A.ParentID where ( " . $dis_top_list ." ) and C.CaptureType < 102");
        $sth->execute();
        $opendoor_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        
        $sth = $dw_db->prepare("INSERT INTO  DisOpenDoor(`Dis`,`DateTime`,`Num`) VALUES ('EMEA', :time, :opendoor_num) ON DUPLICATE KEY UPDATE Num = :opendoor_num");
        $sth->bindParam(':opendoor_num', $opendoor_num, PDO::PARAM_INT);
        $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
        $sth->execute(); 

    }
}
//每月新增激活家庭数
function ActiveFamilyNum($REGION)
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;

    $year_months = array("2020-01-01 00:00:00","2020-02-01 00:00:00","2020-03-01 00:00:00","2020-04-01 00:00:00","2020-05-01 00:00:00","2020-06-01 00:00:00","2020-07-01 00:00:00","2020-08-01 00:00:00","2020-09-01 00:00:00","2020-10-01 00:00:00","2020-11-01 00:00:00"); 
    foreach ($year_months as $year_month)
    {        
        $timestart = $year_month;
        $timestart_t = strtotime($timestart);//转成时间戳
        $timeend_t = strtotime("first day of +1 month",$timestart_t);//不能用 + 1 months的形式
        $timeend = date("Y-m-d 00:00:00", $timeend_t);
        
        $sth_act_family = $ods_db->prepare("select count(1) as count from PersonalAccount P left join Account A on A.ID = P.ParentID left join Account B on B.ID = A. ParentID where ( " . $dis_top_list ." ) and ((P.ActiveTime between '".$timestart."' and '".$timeend."') or (P.ActiveTime is NULL and P.CreateTime between '".$timestart."' and '".$timeend."')) and (P.Role = 10 or P.Role = 20) and P.Active = 1;");
        $sth_act_family->execute();
        $resultRole = $sth_act_family->fetch(PDO::FETCH_ASSOC);
        $family_active_num = $resultRole['count'];
        
        //从 2019-09-01 00:00:00 改成 2019-09
        $year_month = substr($year_month,0,7);
        //UNIQUE KEY `region_data` (`Region`,`DateTime`)  对应的表格已经通过唯一键来保证只会插入一次，后续的都是更新
        $sth = $dw_db->prepare("INSERT INTO  DisActiveFamily(`Dis`,`DateTime`,`Num`) VALUES ('EMEA', :time, :family_active_num) ON DUPLICATE KEY UPDATE Num = :family_active_num");
        $sth->bindParam(':family_active_num', $family_active_num, PDO::PARAM_INT);
        $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
        $sth->execute();         
    }
}
//历史每月新增家伙办公用户数
function ActiveOfficeNum()
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;

    $year_months = array("2022-03-01 00:00:00","2022-04-01 00:00:00","2020-05-01 00:00:00"); 
    foreach ($year_months as $year_month)
    {        
        $timestart = $year_month;
        $timestart_t = strtotime($timestart);//转成时间戳
        $timeend_t = strtotime("first day of +1 month",$timestart_t);//不能用 + 1 months的形式
        $timeend = date("Y-m-d 00:00:00", $timeend_t);
        
        $sth_act_family = $ods_db->prepare("select count(1) as count from PersonalAccount P left join Account A on A.ID = P.ParentID left join Account B on B.ID = A. ParentID where ( " . $dis_top_list ." ) and (P.ActiveTime between '".$timestart."' and '".$timeend."') and (P.Role = 30 or P.Role = 31) and P.Active = 1;");
        $sth_act_family->execute();
        $resultRole = $sth_act_family->fetch(PDO::FETCH_ASSOC);
        $family_active_num = $resultRole['count'];
        
        //从 2019-09-01 00:00:00 改成 2019-09
        $year_month = substr($year_month,0,7);
        $sth = $dw_db->prepare("INSERT INTO  DisActiveOffice(`Dis`,`DateTime`,`Num`) VALUES ('EMEA', :time, :family_active_num) ON DUPLICATE KEY UPDATE Num = :family_active_num");
        $sth->bindParam(':family_active_num', $family_active_num, PDO::PARAM_INT);
        $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
        $sth->execute();         
    }
}

//CallNum();
//OpenDoorNum();
//ActiveFamilyNum($REGION);
ActiveOfficeNum();
?>
