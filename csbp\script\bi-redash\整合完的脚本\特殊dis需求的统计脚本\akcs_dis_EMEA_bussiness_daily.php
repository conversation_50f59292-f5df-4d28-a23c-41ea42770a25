<?php
date_default_timezone_set('PRC');
require('/home/<USER>/akcs_dw_config.php');
require('/home/<USER>/akcs_emea_dis.php');
//区域
$REGION = null;
if ($argc == 2) {
    $REGION = $argv[1];
} else {
    echo 'use: akcs_dw_month.php  usa/eur/asia';
    exit;
}
 $dis_top_list = null;
 $dw_db = null;
 $ods_db = null;
if($REGION == 'USA')
{
    $dw_db = getUSADWDB();
}
else if($REGION == 'EUR')
{
    $dw_db = getEURDWDB();
}
else if($REGION == 'ASIA')
{
    $dw_db = getASIADWDB();
}
else if($REGION == 'LOCAL')
{
    $dw_db = getLOCALDWDB();    
}
$ods_db = getODSDB();

//EMEA的dis列表
$dis_list = getEMEADisList();
foreach ($dis_list as $row => $dis)
{
    $dis_acc = $dis;
    $sth = $ods_db->prepare("select ID from Account where Account = :dis and Grade = 11");
    $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
    $sth->execute();
    $dis_id = $sth->fetch(PDO::FETCH_ASSOC)['ID'];//fetch 不是 fetchALL
    if($dis_id == null)
    {
        continue;
    }
    $dis_top_list = $dis_top_list . 'B.ID = ' . $dis_id . ' or ';
}
//去掉最后面的 ' or '
 $dis_top_list = substr($dis_top_list,0,-4);

function DisOpenDoorNum()
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;
    $table_name = 'PersonalCapture';
    $year_month = date("Y-m");
     
    $sth = $ods_db->prepare("select count(*) as num from PersonalCapture C left join Account A on A.ID = C.MngAccountID left join Account B on B.ID = A.ParentID where ( " .  $dis_top_list ." ) and C.CaptureType < 102");
    $sth->execute();
    $opendoor_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    echo $opendoor_num;

    $sth = $dw_db->prepare("INSERT INTO  DisOpenDoor(`Dis`,`DateTime`,`Num`) VALUES ('EMEA', :time, :opendoor_num) ON DUPLICATE KEY UPDATE Num = :opendoor_num");
    $sth->bindParam(':opendoor_num', $opendoor_num, PDO::PARAM_INT);
    $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
    $sth->execute(); 
}

function DisCallNum()
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;
    $year_month = date("Y-m");

    $sth = $ods_db->prepare("select count(*) as num from CallHistory C left join Account A on A.ID = C.MngAccountID left join Account B on B.ID = A.ParentID where ". $dis_top_list );
    $sth->execute();
    $call_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    
    $sth = $dw_db->prepare("INSERT INTO  DisCall(`Dis`,`DateTime`,`Num`) VALUES ('EMEA', :time, :call_num) ON DUPLICATE KEY UPDATE Num = :call_num");
    $sth->bindParam(':call_num', $call_num, PDO::PARAM_INT);
    $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
    $sth->execute(); 

}
//每月新增激活家庭数
function DisActiveFamilyNum()
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;

    $timeend = date("Y-m-d H:i:s");
    $timestart_t= date("Y-m");
    $timestart = $timestart_t .'-01 00:00:00';
    $year_month = date("Y-m");

    $sth_act_family = $ods_db->prepare("select count(1) as count from PersonalAccount P left join Account A on A.ID = P.ParentID left join Account B on B.ID = A. ParentID where ( " . $dis_top_list ." ) and (P.ActiveTime between '".$timestart."' and '".$timeend."') and (P.Role = 10 or P.Role = 20) and P.Active = 1;");
    $sth_act_family->execute();
    $resultRole = $sth_act_family->fetch(PDO::FETCH_ASSOC);
    $family_active_num = $resultRole['count'];
    
    //UNIQUE KEY `region_data` (`Region`,`DateTime`)  对应的表格已经通过唯一键来保证只会插入一次，后续的都是更新
    $sth = $dw_db->prepare("INSERT INTO  DisActiveFamily(`Dis`,`DateTime`,`Num`) VALUES ('EMEA', :time, :family_active_num) ON DUPLICATE KEY UPDATE Num = :family_active_num");
    $sth->bindParam(':family_active_num', $family_active_num, PDO::PARAM_INT);
    $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
    $sth->execute();         
}

//每月新增办公用户数
function DisActiveOfficerNum()
{
    //从业务数据库里面查询数据
    global $dis_top_list;
    global $ods_db;
    global $dw_db;

    $timeend = date("Y-m-d H:i:s");
    $timestart_t= date("Y-m");
    $timestart = $timestart_t .'-01 00:00:00';
    $year_month = date("Y-m");

    $sth_act_family = $ods_db->prepare("select count(1) as count from PersonalAccount P left join Account A on A.ID = P.ParentID left join Account B on B.ID = A. ParentID where ( " . $dis_top_list ." ) and (P.ActiveTime between '".$timestart."' and '".$timeend."') and (P.Role = 30 or P.Role = 31) and P.Active = 1;");
    $sth_act_family->execute();
    $resultRole = $sth_act_family->fetch(PDO::FETCH_ASSOC);
    $family_active_num = $resultRole['count'];
    
    //UNIQUE KEY `region_data` (`Region`,`DateTime`)  对应的表格已经通过唯一键来保证只会插入一次，后续的都是更新
    $sth = $dw_db->prepare("INSERT INTO  DisActiveOffice(`Dis`,`DateTime`,`Num`) VALUES ('EMEA', :time, :family_active_num) ON DUPLICATE KEY UPDATE Num = :family_active_num");
    $sth->bindParam(':family_active_num', $family_active_num, PDO::PARAM_INT);
    $sth->bindParam(':time', $year_month, PDO::PARAM_STR);
    $sth->execute();         
}

//EMEA的dis列表, 家庭每周激活数的sql语句不一样，所以重新组装
$dis_emea_list = null;
//$dis_list = getEMEADisList();
foreach ($dis_list as $row => $dis)
{
    $dis_acc = $dis;
    $sth = $ods_db->prepare("select ID from Account where Account = :dis and Grade = 11");
    $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
    $sth->execute();
    $dis_id = $sth->fetch(PDO::FETCH_ASSOC)['ID'];//fetch 不是 fetchALL
    if($dis_id == null)
    {
        continue;
    }
    $dis_emea_list = $dis_emea_list . 'A.ParentID = ' . $dis_id . ' or ';
}
//去掉最后面的 ' or '
 $dis_emea_list = substr($dis_emea_list,0,-4);
 
//每周新增激活家庭数
function ActiveFamilyNumWeek()
{
    global $ods_db;
    global $dw_db;
    global $dis_emea_list;
    $timeend = null;
    $timestart = null;
    $date = date('Y-m-d'); 
    $first=1; //$first =1 表示每周星期一为开始日期 0表示每周日为开始日期
    $w=date('w',strtotime($date));  //获取当前周的第几天 周日是 0 周一到周六是 1 - 6
    
    $now_start = date('Y-m-d',strtotime("$date -".($w ? $w - $first : 6).' days')); //获取本周开始日期，如果$w是0，则表示周日，减去 6 天
    //如果是周一,则计算上一周的激活家庭数
    if(date("w") == 1)
    {        
        $timestart = date('Y-m-d 00:00:00',strtotime("$now_start - 7 days"));  //上周一
        $timeend = date('Y-m-d 00:00:00');  //本周一
    }
    else
    {
        $timestart = date('Y-m-d 00:00:00',strtotime("$date -".($w ? $w - $first : 6).' days'));//本周一
        //统计的是今天0点的数据
        $timeend = date("Y-m-d 00:00:00");
    }

    $sth = $ods_db->prepare("select count(1) as num from PersonalAccount P left join Account A on A.ID=P.ParentID where ( " . $dis_emea_list ." ) and (P.ActiveTime between '".$timestart."' and '".$timeend."') and (P.Role = 10 or P.Role = 20) and P.Active = 1 and P.Special = 0;");
    $sth->execute();
    $create_family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
    
    $sth = $dw_db->prepare("INSERT INTO  DisActiveFamilyWeek(`Dis`,`WeekTime`,`CreateNum`) VALUES ('EMEA', :time, :create_family_num) ON DUPLICATE KEY UPDATE CreateNum = :create_family_num");
    $sth->bindParam(':time', $timestart, PDO::PARAM_STR);
    $sth->bindParam(':create_family_num', $create_family_num, PDO::PARAM_INT);
    $sth->execute(); 
    
}

DisOpenDoorNum();
DisCallNum();
DisActiveFamilyNum();
DisActiveOfficerNum();
ActiveFamilyNumWeek();
?>
