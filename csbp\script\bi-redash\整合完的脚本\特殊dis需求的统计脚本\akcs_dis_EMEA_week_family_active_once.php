<?php
date_default_timezone_set('PRC');
require('/home/<USER>/akcs_dw_config.php');
//区域
$REGION = null;
if ($argc == 2) {
    $REGION = $argv[1];
} else {
    echo 'use: akcs_dw_month.php  usa/eur/asia';
    exit;
}
 $dis_top_list = null;
 $dw_db = null;
 $ods_db = null;
if($REGION == 'USA')
{
    $dw_db = getUSADWDB();
}
else if($REGION == 'EUR')
{
    $dw_db = getEURDWDB();
}
else if($REGION == 'ASIA')
{
    $dw_db = getASIADWDB();
}
else if($REGION == 'LOCAL')
{
    $dw_db = getLOCALDWDB();    
}
$ods_db = getODSDB();

//EMEA的dis列表
$dis_list = array("PalwintecS", "Pardik", "Techring", "Arabia02","DVCOM", "DVCOM-Kuwait", "DVCOM-Oman", "Ignite","Qatar", "Bus Shelter", "Techtrade", "IT service","Norway01", "JNT", "realtimetec", "Oman01","Slovenija", "businesscomdis", "CIEPaybydis", "LydisDis","CIE", "YLI-test", "Sweden01", "GDXNOR","FreephoneD", "Elfon", "IPP", "Egypt","Sweden", "TOA", "Poland", "Italy0","allnet ita", "Allnet Ger", "Automationplus", "arabia","Vanderbilt", "Allnet");
foreach ($dis_list as $row => $dis)
{
    $dis_acc = $dis;
    $sth = $ods_db->prepare("select ID from Account where Account = :dis and Grade = 11");
    $sth->bindParam(':dis', $dis_acc, PDO::PARAM_STR);
    $sth->execute();
    $dis_id = $sth->fetch(PDO::FETCH_ASSOC)['ID'];//fetch 不是 fetchALL
    if($dis_id == null)
    {
        continue;
    }
    $dis_top_list = $dis_top_list . 'A.ParentID = ' . $dis_id . ' or ';
}

//去掉最后面的 ' or '
$dis_top_list = substr($dis_top_list,0,-4);
//每月新增激活家庭数
function DisActiveFamilyNum()
{
    global $ods_db;
    global $dw_db;
    global $dis_top_list;
    $date = date('Y-m-d'); 
    $first=1; //$first =1 表示每周星期一为开始日期 0表示每周日为开始日期
    $w=date('w',strtotime($date));  //获取当前周的第几天 周日是 0 周一到周六是 1 - 6
    
    $time_begin = '2020-07-06 00:00:00';
    $time_this_mon = date('Y-m-d 00:00:00',strtotime("$date -".($w ? $w - $first : 6).' days'));//本周一
    while($time_begin < $time_this_mon)
    {
        $timestart = $time_begin;
        //echo $timestart;
        $timeend = date('Y-m-d 00:00:00',strtotime("$time_begin + 7 days")); 
        //select * from Account where Account = 'PalwintecS';  查询palwintec
        $sth = $ods_db->prepare("select count(1) as num from PersonalAccount P left join Account A on A.ID=P.ParentID  where ( " . $dis_top_list ." ) and (P.ActiveTime between '".$timestart."' and '".$timeend."') and (P.Role = 10 or P.Role = 20) and P.Active = 1 and P.Special = 0;");
        $sth->execute();
        $create_family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

        $sth = $dw_db->prepare("INSERT INTO  DisActiveFamilyWeek(`Dis`,`WeekTime`,`CreateNum`) VALUES ('EMEA', :time, :create_family_num) ON DUPLICATE KEY UPDATE CreateNum = :create_family_num");
        $sth->bindParam(':time', $timestart, PDO::PARAM_STR);
        $sth->bindParam(':create_family_num', $create_family_num, PDO::PARAM_INT);
        $sth->execute(); 
        $time_begin = $timeend;
    }
}

DisActiveFamilyNum();
?>
