<?php
date_default_timezone_set('PRC');
require('/home/<USER>/akcs_dw_config.php');
//区域
$REGION = null;
if ($argc == 2) {
    $REGION = $argv[1];
} else {
    echo 'use: akcs_dw_month.php  usa/eur/asia';
    exit;
}
 $dis_top_list = null;
 $dw_db = null;
 $ods_db = null;
if($REGION == 'USA')
{
    $dw_db = getUSADWDB();
}
else if($REGION == 'EUR')
{
    $dw_db = getEURDWDB();
}
else if($REGION == 'ASIA')
{
    $dw_db = getASIADWDB();
}
else if($REGION == 'LOCAL')
{
    $dw_db = getLOCALDWDB();    
}
$ods_db = getODSDB();


//每月新增激活家庭数
function DisActiveFamilyNum()
{
    global $ods_db;
    global $dw_db;

    $date = date('Y-m-d'); 
    $first=1; //$first =1 表示每周星期一为开始日期 0表示每周日为开始日期
    $w=date('w',strtotime($date));  //获取当前周的第几天 周日是 0 周一到周六是 1 - 6
    
    $time_begin = '2020-01-06 00:00:00';
    $time_this_mon = date('Y-m-d 00:00:00',strtotime("$date -".($w ? $w - $first : 6).' days'));//本周一
    echo $time_this_mon;
    while($time_begin < $time_this_mon)
    {
        $timestart = $time_begin;
        echo $timestart;
        $timeend = date('Y-m-d 00:00:00',strtotime("$time_begin + 7 days")); 
        //select * from Account where Account = 'PalwintecS';  查询palwintec
        $sth = $ods_db->prepare("select count(1) as num from PersonalAccount P left join Account A on A.ID=P.ParentID  where A.ParentID = 46 and (P.ActiveTime between '".$timestart."' and '".$timeend."') and (P.Role = 10 or P.Role = 20) and P.Active = 1 and P.Special = 0;");
        $sth->execute();
        $create_family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];
        
        $sth = $dw_db->prepare("INSERT INTO  DisActiveFamilyWeek(`Dis`,`WeekTime`,`CreateNum`) VALUES ('PalwintecS', :time, :create_family_num) ON DUPLICATE KEY UPDATE CreateNum = :create_family_num");
        $sth->bindParam(':time', $timestart, PDO::PARAM_STR);
        $sth->bindParam(':create_family_num', $create_family_num, PDO::PARAM_INT);
        $sth->execute(); 
        $time_begin = $timeend;
    }
}

//每周新增激活家庭数
function ActiveFamilyNumWeek()
{
    global $ods_db;
    global $dw_db;
    $timeend = null;
    $timestart = null;
    $date = date('Y-m-d'); 
    $first=1; //$first =1 表示每周星期一为开始日期 0表示每周日为开始日期
    $w=date('w',strtotime($date));  //获取当前周的第几天 周日是 0 周一到周六是 1 - 6
    
    $now_start = date('Y-m-d',strtotime("$date -".($w ? $w - $first : 6).' days')); //获取本周开始日期，如果$w是0，则表示周日，减去 6 天
    //如果是周一,则计算上一周的激活家庭数
    if(date("w") == 1)
    {        
        $timestart = date('Y-m-d 00:00:00',strtotime("$now_start - 7 days"));  //上周一
        $timeend = date('Y-m-d 00:00:00');  //本周一
    }
    else
    {
        $timestart = date('Y-m-d 00:00:00',strtotime("$date -".($w ? $w - $first : 6).' days'));//本周一
        //统计的是今天0点的数据
        $timeend = date("Y-m-d 00:00:00");
    }
    $sth = $ods_db->prepare("select count(1) as num from PersonalAccount where (ActiveTime between '".$timestart."' and '".$timeend."') and (Role = 10 or Role = 20) and Active = 1 and Special = 0;");
    $sth->execute();
    $active_family_num = $sth->fetch(PDO::FETCH_ASSOC)['num'];

    $sth = $dw_db->prepare("INSERT INTO  RegionActiveFamilyWeek(`ActiveTime`,`Num`) VALUES (:time, :active_family_num) ON DUPLICATE KEY UPDATE Num = :active_family_num");
    $sth->bindParam(':active_family_num', $active_family_num, PDO::PARAM_INT);
    $sth->bindParam(':time', $timestart, PDO::PARAM_STR);
    $sth->execute();
}
DisActiveFamilyNum();
//ActiveFamilyNumWeek();
?>
