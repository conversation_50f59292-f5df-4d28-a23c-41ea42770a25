#!/usr/bin/env python
# coding=utf8

from socket import *

#host = '**************'
host = '127.0.0.1'
port = 8501
bufsiz = 2048
errnu = 0
#for num in range(1,1000):
while True:
	tcpCliSock = socket(AF_INET, SOCK_STREAM)    # 开启套接字
	tcpCliSock.connect((host, port))             # 连接到服务器

	#while True:
#	data = 'echo'     # 等待输入
	#	if not data:
	#		break
#	tcpCliSock.send(data)       # 发送信息
	response = tcpCliSock.recv(bufsiz)       # 接受返回信息
	if not response:
		errnu = errnu + 1
	print response

	#tcpCliSock.close()
