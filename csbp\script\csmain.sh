#!/bin/bash
ACMD="$1"
CSMAIND_BIN='/usr/local/akcs/csmain/bin/csmain'

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_csmain()
{
    nohup $CSMAIND_BIN >/dev/null 2>&1 &
    echo "Start csmain successful"
}
stop_csmain()
{
    echo "Begin to stop csmain"
    kill -9 `pidof csmain`
    sleep 2
    echo "Stop csmain successful"
}

case $ACMD in
  start)
    cnt=`netstat -alnp | grep 8501 | grep csmain | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        start_csmain
    else
        echo "csmain is already running"
    fi
    ;;
  stop)
    cnt=`netstat -alnp | grep 8501 | grep csmain | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "csmain is already stopping"
    else
        stop_csmain
    fi
    ;;
  restart)
    stop_csmain
    sleep 1
    start_csmain
    ;;
  status)
    cnt=`netstat -alnp | grep 8501 | grep csmain | grep -v grep | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "csmain is stopping"
    else
        echo "csmain is running"
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

