#!/bin/sh
#编译csbase
cd $1/csbase
cmake ./
make

#编译cslocalgs
cd $1/cslocalgs/build
make

cd $1/cslocalgs
svn upgrade
REV=`svn info | grep 'Last Changed Rev' | awk '{print $4}'`

#获取project的版本号
version=5.4
packeg_name="akcs_cslocalgs_packeg-${version}-r$REV.tar.gz"
packeg_ftp_path="CloudServer/AKCS-cslocalgs/cslocalgs_1.0"
version_name=cslocalgs-${version}-r$REV 
echo "$version_name" > /$1/cslocalgs/version

cd $1/cslocalgs
cp ./bin/* ./install/cslocalgs/bin
cp ./version ./install/cslocalgs
mkdir -p akcs_cslocalgs_packeg
cp -rf install/ akcs_cslocalgs_packeg
tar zcvf ${packeg_name} akcs_cslocalgs_packeg --exclude .svn
