#!/bin/bash
csmainlog_path="/var/log/csmainlog"
csadaptlog_path="/var/log/csadaptlog"
DAY=`date +"%Y-%m-%d %H:%M"`
H24=`date "+%H"`

y1=`date "+%Y"`
m1=`date "+%m"`
d1=`date "+%d"`

function csmain_log_back(){
 cd $csmainlog_path || exit 1
 #每天0点就讲csmain01.log打包
 if [ $H24 -eq 0 ];then
   DESFILE="csmainlog-$y1-$m1-$d1.tar.gz"
   tar zvcf $DESFILE csmain01.log
   echo > csmain01.log
 fi
 #csmain00.log达到1M的时候就将内容echo该csmain01.log并清空
 if [ `ls -l --block-size=k csmain00.log | awk '{print $5}' |  tr -cd "[0-9]"` -gt 1024 ]; then
   echo "---------------------------$DAY--------------------------" >> csmain01.log
   cat csmain00.log >> csmain01.log
   echo > csmain00.log
 fi
}

function csadapt_log_back(){
 cd $csadaptlog_path || exit 1
 #每天0点就讲csadapt01.log打包
 if [ $H24 -eq 0 ];then
   DESFILE="csadaptlog-$y1-$m1-$d1.tar.gz"
   tar zvcf $DESFILE csadapt01.log
   echo > csadapt01.log
 fi
 #csadapt00.log达到1M的时候就将内容echo该csadapt01.log并清空
 if [ `ls -l --block-size=k csadapt00.log | awk '{print $5}' |  tr -cd "[0-9]"` -gt 1024 ]; then
   echo "---------------------------$DAY--------------------------" >> csadapt01.log
   cat csadapt00.log >> csadapt01.log
   echo > csadapt00.log
 fi
}

csmain_log_back
csadapt_log_back
