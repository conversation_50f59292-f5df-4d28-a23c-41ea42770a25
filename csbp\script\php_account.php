<?php
ini_set('date.timezone','Asia/Shanghai');
echo "Begin to create user's download folders."."\r\n";

$DownloadPath = '/home/<USER>/apache/download';
$DbPasswd = GetDbPasswd();
@ $db = new mysqli('localhost', 'root', $DbPasswd,'AKCS');
if (mysqli_connect_errno()) 
{
    echo "Error: Could not connect to database.  Please make sure the passwd that you inputed is right or check internet."."\r\n";
    exit;
}

//遍历用户表创建各个用户的下载目录 
CreateChildFolder();

echo "\r\n Finish to create user's download folders."."\r\n"."\r\n";

function GetDbPasswd()
{
    $fs = true;
    do{
        if($fs)
        {
            fwrite(STDOUT,"Please input the passwd of DB:");
            $fs = false;
        }
        else
        {
            fwrite(STDOUT,'Sorry, you entered a null value,please input again. ');
        }          
        $name = trim(fgets(STDIN));
    }while(!$name);

    echo 'The passwd that your input is:'.$name."\r\n";
    return $name;
} 

//遍历用户表创建各个用户的下载目录 
function CreateChildFolder()
{
    //首先将$db声明为全局变量,否则不能使用
    global $db;
    global $DownloadPath;
    $UsersList = array();
    $sql = "SELECT Account FROM Account";
    $result = $db->query($sql);
    if ($result && ($db->affected_rows > 0))
    {
        $i=0;
        //mysqli_fetch_array返回的也是数组,这样下面的foreach要嵌套两次才能取出子节点名称,所以改成
        //while($row=mysqli_fetch_array($result,MYSQLI_ASSOC))
        while ($row = $result->fetch_row())
        { 
            $UsersList[$i]=$row[0]; 
            $i++; 
        }
    }
    else
    {
        echo "There is not account in db. \r\n";
        return;
    }
    //循环创建子目录
    foreach($UsersList as $User)
    { 
        $fileDir = $DownloadPath.'/'.$User;
        echo $fileDir;
        
        if (!is_dir("$fileDir"))
        {
             echo "Info: begin to create folder " . $fileDir. "\r\n";
            if (!mkdir($fileDir, 0777, true)) 
            {
                echo "Error: Failed to create $fileDir folders,exit..."."\r\n";
                exit;
            } 

            //用户目录创建成功之后
            else
            {
                chmod($fileDir, 0777);
                $addressPath = $fileDir.'/'.'Address';
                if (!mkdir($addressPath, 0777, true)) 
                {
                    echo "Error: Failed to create $addressPath folders,exit..."."\r\n";
                    exit;
                } 
                chmod($addressPath, 0777);
                
                $ADPath = $fileDir.'/'.'AD';
                if (!mkdir($ADPath, 0777, true)) 
                {
                    echo "Error: Failed to create $ADPath folders,exit..."."\r\n";
                    exit;
                } 
                chmod($ADPath, 0777);
                
                $AppConfigPath = $fileDir.'/'.'AppConfig';
                if (!mkdir($AppConfigPath, 0777, true)) 
                {
                    echo "Error: Failed to create $AddressPath folders,exit..."."\r\n";
                    exit;
                } 
                chmod($AppConfigPath, 0777);
                
                $ConfigPath = $fileDir.'/'.'Config';
                if (!mkdir($ConfigPath, 0777, true)) 
                {
                    echo "Error: Failed to create $ConfigPath folders,exit..."."\r\n";
                    exit;
                } 
                chmod($ConfigPath, 0777);
                
                $FirmwarePath = $fileDir.'/'.'Firmware';
                if (!mkdir($FirmwarePath, 0777, true)) 
                {
                    echo "Error: Failed to create $FirmwarePath folders,exit..."."\r\n";
                    exit;
                } 
                chmod($FirmwarePath, 0777);
                
                $PrivatekeyPath = $fileDir.'/'.'Privatekey';
                if (!mkdir($PrivatekeyPath, 0777, true)) 
                {
                    echo "Error: Failed to create $PrivatekeyPath folders,exit..."."\r\n";
                    exit;
                } 
                chmod($PrivatekeyPath, 0777);
                
                $RfidPath = $fileDir.'/'.'Rfid';
                if (!mkdir($RfidPath, 0777, true)) 
                {
                    echo "Error: Failed to create $RfidPath folders,exit..."."\r\n";
                    exit;
                } 
                chmod($RfidPath, 0777);
            }
        }
        else
        {
            echo "   Info: folder " . $fileDir. " already exit,continue..."."\r\n";
            continue;
        }
    } 
    return ;    
}
?>