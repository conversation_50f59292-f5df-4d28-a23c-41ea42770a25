<?php 

function getDB()
{
    $dbhost = "localhost";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";

    $mysql_conn_string = "mysql:host=$dbhost;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

$file = fopen('accountInfo.csv','r'); 
while ($data = fgetcsv($file))
{ 
    //每次读取CSV里面的一行内容
    $goods_list[] = $data;
}
$db = getDB();
$total = count($goods_list);
for ($i = 1; $i < $total; $i++)
{
   //echo $goods_list[$i][0];
   //echo "\n";
    $sth = $db->prepare("update PersonalSip set SipPwd = :Pwd where SipAccount = :Account");
    $sth->bindParam(':Pwd',$goods_list[$i][1], PDO::PARAM_STR);
    $sth->bindParam(':Account',$goods_list[$i][0], PDO::PARAM_STR);
    $sth->execute();
    if ($i % 100 == 0)
    {
        $num = $total - $i;
        echo "we have update [ " . $i . " ]now, there are still [ " . $num . " ] to be update \n";
    }

}
 fclose($file);
?> 
