<?php
date_default_timezone_set('PRC');



if($argc != 2)
{
    echo ("usage:php ".$argv[0]." <xcloud>");
    exit(1);
}

$cloud = $argv[1];

$STATIS_TOTAL_FILE = "/tmp/" . $cloud ."_door_info.csv";
shell_exec("touch ". $STATIS_TOTAL_FILE);

chmod($STATIS_TOTAL_FILE, 0777);
if (file_exists($STATIS_TOTAL_FILE)) {
    shell_exec("echo > ". $STATIS_TOTAL_FILE);
}

function STATIS_WRITE($content)
{
	global $STATIS_TOTAL_FILE;
	file_put_contents($STATIS_TOTAL_FILE, $content, FILE_APPEND);
	file_put_contents($STATIS_TOTAL_FILE, "\n", FILE_APPEND);
}

function getDB()
{
    $dbhost = "127.0.0.1";
    $dbuser = "dbread";
    $dbpass = "ohfae@thie0c+e3I";
    $dbname = "AKCS";

    $mysql_conn_string = "mysql:host=$dbhost;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

function getDevices($db, $firmware, $time)
{
	$sth = $db->prepare("select MAC,Firmware,MngAccountID,UnitID from Devices where Firmware like :firmware and Grade in (1,2) and CreateTime > :time group by UnitID order by MngAccountID,UnitID;");
	$sth->bindParam(':firmware', $firmware, PDO::PARAM_STR);
	$sth->bindParam(':time', $time, PDO::PARAM_STR);
	$sth->execute();
	$ret = $sth->fetchAll(PDO::FETCH_ASSOC);
	return $ret;
}

function getPersonalDevices($db, $firmware, $time)
{
	$sth = $db->prepare("select MAC,Firmware,Node from PersonalDevices where Firmware like :firmware and CreateTime > :time group by Node;");
	$sth->bindParam(':firmware', $firmware, PDO::PARAM_STR);
	$sth->bindParam(':time', $time, PDO::PARAM_STR);
	$sth->execute();
	$ret = $sth->fetchAll(PDO::FETCH_ASSOC);
	return $ret;
}

function getDevicesCount($db, $firmware, $time)
{
	$sth = $db->prepare("select count(*) as count from Devices where Firmware like :firmware and Grade in (1,2) and CreateTime > :time;");
	$sth->bindParam(':firmware', $firmware, PDO::PARAM_STR);
	$sth->bindParam(':time', $time, PDO::PARAM_STR);
	$sth->execute();
	$ret = $sth->fetch(PDO::FETCH_ASSOC)["count"];
	return $ret;
}

function getPersonalDevicesCount($db, $firmware, $time)
{
	$sth = $db->prepare("select count(*) as count from PersonalDevices where Firmware like :firmware and CreateTime > :time;");
	$sth->bindParam(':firmware', $firmware, PDO::PARAM_STR);
	$sth->bindParam(':time', $time, PDO::PARAM_STR);
	$sth->execute();
	$ret = $sth->fetch(PDO::FETCH_ASSOC)["count"];
	return $ret;
}

function getRelateDeviveIndoor($db, $deviceList)
{
	$indoor_count = 0;
	$mng_id = 0;
	$mng_has_pub = 0;
    foreach ($deviceList as $row => $dev) {
		$dev_m = $dev['MngAccountID'];
		$dev_u = $dev['UnitID'];
		if ($dev_m != $mng_id)//不等代表新的一个社区
		{
			$mng_has_pub = 0;
			if ($dev_u == 0)//sql 已经排序了 
			{
				$mng_has_pub = 1;
				$sth = $db->prepare("select count(*) from Devices where MngAccountID = :MngAccountID and type = 2");
				$sth->bindParam(':MngAccountID', $dev_m, PDO::PARAM_STR);
				$sth->execute();
				$ret = $sth->fetchAll(PDO::FETCH_ASSOC);
				$indoor_count += count($ret);				
			}
			else 
			{
				$sth = $db->prepare("select count(*) from Devices where UnitID = :unitid and type = 2");
				$sth->bindParam(':unitid', $dev['UnitID'], PDO::PARAM_STR);
				$sth->execute();
				$ret = $sth->fetchAll(PDO::FETCH_ASSOC);
				
				$indoor_count += count($ret);				
			}
		}
		else
		{
			if (($mng_has_pub == 1))
			{
				continue;
			}
			$sth = $db->prepare("select count(*) from Devices where UnitID = :unitid and type = 2");
			$sth->bindParam(':unitid', $dev['UnitID'], PDO::PARAM_STR);
			$sth->execute();
			$ret = $sth->fetchAll(PDO::FETCH_ASSOC);
			
			$indoor_count += count($ret);
		}
    }
	return $indoor_count;
}

function getRelatePersonalDeviveIndoor($db, $deviceList)
{
	$indoor_count = 0;
    foreach ($deviceList as $row => $dev) {
        if ($dev['Node']) {
            $sth = $db->prepare("select MAC, Firmware from PersonalDevices where Node = :node and type = 2");
            $sth->bindParam(':node', $dev['Node'], PDO::PARAM_STR);
            $sth->execute();
            $ret = $sth->fetchAll(PDO::FETCH_ASSOC);

			$indoor_count += count($ret);
        }
    }
	return $indoor_count;
}

function writeCSV($lineList)
{
    foreach ($lineList as $line) {
		echo $line . "\n";
		STATIS_WRITE($line);
	}
}

function getDevicesByFirmware($firmwareList, $time)
{
	$db = getDB();
	foreach ($firmwareList as $firmware) {
		$type = str_replace(".%", "", $firmware);
        $devices = getDevices($db, $firmware, $time);
		$indoor_count = getRelateDeviveIndoor($db, $devices);
		$dcount = getDevicesCount($db, $firmware, $time);

		
        $personalDevices = getPersonalDevices($db, $firmware, $time);
		$indoor_count += getRelatePersonalDeviveIndoor($db, $personalDevices);
		$dcount += getPersonalDevicesCount($db, $firmware, $time);
		
		writeCSV(["dev type: $type count door: $dcount, indoor: $indoor_count"]);          


		
/*		
		writeCSV(["dev type: $firmware"]);
		writeCSV(["community:"]);
        $devices = getDevices($db, $firmware, $time);
		$indoor_count = getRelateDeviveIndoor($db, $devices);
		
		$dcount = getDevicesCount($db, $firmware);
		writeCSV(["count door: $dcount, indoor: $indoor_count"]);

		
		writeCSV(["signal:"]);
		$indoor_count = 0;
        $personalDevices = getPersonalDevices($db, $firmware, $time);
		$indoor_count = getRelatePersonalDeviveIndoor($db, $personalDevices);
				
		$dcount = getPersonalDevicesCount($db, $firmware);
		writeCSV(["count door: $dcount, indoor: $indoor_count"]);   */         
    }
}

$firmwareList = ['916.%', '915.%', '2915.%', '539.%', '29.%', '18.%', '216.%', '116.%', '227.%', '28.%'];
writeCSV(["All"]);
getDevicesByFirmware($firmwareList, "2000-02-12 17:14:30");

writeCSV([""]);
writeCSV([""]);
writeCSV(["2023"]);
getDevicesByFirmware($firmwareList, "2023-01-01 00:00:00");


