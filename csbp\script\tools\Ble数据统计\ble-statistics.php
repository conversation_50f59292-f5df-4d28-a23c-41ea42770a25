<?php
error_reporting(E_ALL || ~E_NOTICE);

if($argc != 2)
{
    echo ("usage:php ".$argv[0]." <xcloud>");
    exit(1);
}
$cloud = $argv[1];

$STATIS_FILE = "/tmp/$cloud.csv";
shell_exec("rm ". $STATIS_FILE);
shell_exec("touch ". $STATIS_FILE);
chmod(STATIS_FILE, 0777);

function TRACE($content)
{
    global $STATIS_FILE;
	@file_put_contents($STATIS_FILE, $content, FILE_APPEND);
	@file_put_contents($STATIS_FILE, "\n", FILE_APPEND);
}

function getDB(){
	$dbhost = "127.0.0.1";	//需在mysql主机执行
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
	$dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

$bleappcount = 0;

function BLE($forble_where){
    global $appcount;
    $arr_comm = array();
    $arr_comm_unitid = array();
    
    $arr_comm_dev = array();
    $arr_comm_unitid_dev = array();    
   
    $arr_signal_dev = array();
    $arr_signal = array();
    
    $x912count = 0; //有ble app 或者 有室内机的912
    $x912appcount = 0; //有ble app 的912
    $allx912count = 0;
    $indoor = array();
    $x912info = array();
   
    $db = getDB();

    $sth = $db->prepare("select A2.Account as Dis,A1.Account as Ins,D.MngAccountID,D.UnitID, D.Mac, D.CreateTime  from Devices D join Account A on D.MngAccountID = A.ID join Account A1 on A.ManageGroup = A1.ID join Account A2 on A1.ParentID=A2.ID  where  D.Firmware like '912%' order by Dis,Ins,MngAccountID,UnitID;");
    $sth->execute();
    $list = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach($list as $key => $value){
        $allx912count++;
        $mngid = $value['MngAccountID'];
        $unitid = $value['UnitID'];
        
        $x912info[$value["Mac"]]["CreateTime"] = $value["CreateTime"];
        $x912info[$value["Mac"]]["Dis"] = $value["Dis"];
        $x912info[$value["Mac"]]["Ins"] = $value["Ins"];
        
        if ($unitid == 0)
        {
            $cnt_account = 0;
            $cnt_dev = 0;
            //查整改社区用户
            $sth = $db->prepare("select count(*) as cnt from PersonalAccount where ParentID=:mngid and Role=20 and $forble_where;");
            $sth->bindParam(':mngid', $mngid, PDO::PARAM_STR);
            $sth->execute();            
            $cnt_account = $sth->fetch(PDO::FETCH_ASSOC)["cnt"];
    
            if($forble_where == "1=1")
            {
                $sth = $db->prepare(" select count(*) as cnt from PersonalAccount P join PersonalAccount PP on PP.ID = P.ParentID where PP.ParentID=:mngid and P.Role=21 and $forble_where;");                
            }
            else
            {
                $sth = $db->prepare(" select count(*) as cnt from PersonalAccount P join PersonalAccount PP on PP.ID = P.ParentID where PP.ParentID=:mngid and P.Role=21 and P.BLEOpenDoorType = 1;");
            }
            $sth->bindParam(':mngid', $mngid, PDO::PARAM_STR);
            $sth->execute();
            $cnt_account += $sth->fetch(PDO::FETCH_ASSOC)["cnt"];
            
            //查设备
            $sth = $db->prepare(" select SUBSTRING_INDEX(D.Firmware, '.', 1) as fw,D.CreateTime,D.Mac from Devices  D join VersionModel v on v.VersionNumber = SUBSTRING_INDEX(D.Firmware, '.', 1) where MngAccountID=:mngid and Grade =3 and v.type = 2 order by D.CreateTime desc");
            $sth->bindParam(':mngid', $mngid, PDO::PARAM_STR);
            $sth->execute();
            $list = $sth->fetchALL(PDO::FETCH_ASSOC);
            $cnt_dev = 0;
            if (!array_key_exists($value['MngAccountID'], $indoor["MngID"] ))
            {
                foreach($list as $key1 => $value1){
                    $cnt_dev++;
                    $indoor["MngID"][$value['MngAccountID']][$value1["fw"]] += 1;
                } 
            }
            else
            {
                $x912count++;
                continue;
            }
                    
            if ($cnt_account != 0)
            {
                $x912appcount++;
                $arr_comm[$value['Dis']][$value['Ins']][$value['MngAccountID']] = $cnt_account;
            }
            if ($cnt_dev != 0)
            {
                $arr_comm_dev[$value['Dis']][$value['Ins']][$value['MngAccountID']] = $cnt_dev;
            }
            if ($cnt_dev != 0 || $cnt_account != 0)
            {
                $x912count++;
            }
        }
        else
        {
            //查楼栋用户
            $sth = $db->prepare("select count(*) as cnt from PersonalAccount where UnitID=:unitdid and  $forble_where;");
            $sth->bindParam(':unitdid', $unitid, PDO::PARAM_STR);
            $sth->execute();
            $cnt_account = $sth->fetch(PDO::FETCH_ASSOC)["cnt"];
            
            //查设备
            $sth = $db->prepare(" select SUBSTRING_INDEX(D.Firmware, '.', 1) as fw,D.CreateTime,D.Mac from Devices D join VersionModel v on v.VersionNumber = SUBSTRING_INDEX(D.Firmware, '.', 1) where UnitID=:unitdid and Grade =3 and v.type = 2 order by D.CreateTime desc");
            $sth->bindParam(':unitdid', $unitid, PDO::PARAM_STR);
            $sth->execute();
            $list = $sth->fetchALL(PDO::FETCH_ASSOC);
            $cnt_dev = 0;          
             
            if (!array_key_exists($value['UnitID'], $indoor[$value['MngAccountID']] ))
            {
                foreach($list as $key1 => $value1){
                    $cnt_dev++;
                    $indoor[$value['MngAccountID']][$value['UnitID']][$value1["fw"]] += 1;
                }
            } 
            else
            {
                $x912count++;
                continue;
            }            
            if ($cnt_account != 0)
            {
                $x912appcount++;
                $arr_comm_unitid[$value['Dis']][$value['Ins']][$value['MngAccountID']][$value['UnitID']] = $cnt_account;
            }
            if ($cnt_dev != 0)
            {
                $arr_comm_unitid_dev[$value['Dis']][$value['Ins']][$value['MngAccountID']][$value['UnitID']] = $cnt_dev;
            }
            if ($cnt_dev != 0 || $cnt_account != 0)
            {
                $x912count++;
            }
        }
    }
    
    $sth = $db->prepare("select A2.Account as Dis,A1.Account as Ins,A.ID as MngAccountID, D.Node, D.Mac, D.CreateTime  from PersonalDevices D join Account A on D.Community = A.Account join Account A1 on A.ManageGroup = A1.ID join Account A2 on A1.ParentID=A2.ID  where  D.Firmware like '912%' order by Dis,Ins,Node;");
    $sth->execute();
    $list = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach($list as $key => $value){
        $allx912count++;
        
        $mngid = $value['MngAccountID'];
        $node = $value['Node'];
        
        $x912info[$value["Mac"]]["CreateTime"] = $value["CreateTime"];
        $x912info[$value["Mac"]]["Dis"] = $value["Dis"];
        $x912info[$value["Mac"]]["Ins"] = $value["Ins"];
        
        $cnt_account = 0;
        $cnt_dev = 0;

        //查用户
        $sth = $db->prepare("select count(*) as cnt from PersonalAccount where Account=:node and Role=10 and $forble_where;");
        $sth->bindParam(':node', $node, PDO::PARAM_STR);
        $sth->execute();            
        $cnt_account = $sth->fetch(PDO::FETCH_ASSOC)["cnt"];

        if($forble_where == "1=1")
        {
            $sth = $db->prepare(" select count(*) as cnt from PersonalAccount P join PersonalAccount PP on PP.ID = P.ParentID where PP.Account=:node and P.Role=11 and $forble_where;");
        }
        else
        {
            $sth = $db->prepare(" select count(*) as cnt from PersonalAccount P join PersonalAccount PP on PP.ID = P.ParentID where PP.Account=:node and P.Role=11 and P.BLEOpenDoorType = 1;");
        }

        $sth->bindParam(':node', $node, PDO::PARAM_STR);
        $sth->execute();
        $cnt_account += $sth->fetch(PDO::FETCH_ASSOC)["cnt"];
        
        //查设备
        $sth = $db->prepare(" select  SUBSTRING_INDEX(D.Firmware, '.', 1) as fw,D.CreateTime,D.Mac from PersonalDevices  D join VersionModel v on v.VersionNumber = SUBSTRING_INDEX(D.Firmware, '.', 1) where D.Node=:node and v.type = 2 order by D.CreateTime desc");
        $sth->bindParam(':node', $node, PDO::PARAM_STR);
        $sth->execute();
        $list = $sth->fetchALL(PDO::FETCH_ASSOC);
        
        $cnt_dev = 0;
        if (!array_key_exists($value['Node'], $indoor['Node'] ))
        {
            foreach($list as $key1 => $value1){
                $cnt_dev++;
                $indoor["Node"][$value['Node']][$value1["fw"]] += 1;
            } 
        } 
        else
        {
            $x912count++;
            continue;
        }         
        
        if ($cnt_account != 0)
        {
            $x912appcount++;
            $arr_signal[$value['Dis']][$value['Ins']][$value['Node']] = $cnt_account;
        }
        if ($cnt_dev != 0)
        {     
            $arr_signal_dev[$value['Dis']][$value['Ins']][$value['Node']] = $cnt_dev;
        }            
        if ($cnt_dev != 0 || $cnt_account != 0)
        {
            $x912count++;
        }
    }    
    
    $arrcount = array();
    foreach($arr_comm as $key1 => $value1){
        foreach($value1 as $key2 => $value2){
            $count = 0;
            foreach($value2 as $key3 => $value3){
                $count += $value3;
            }
            $arrcount[$key1][$key2]["app"] += $count;
        }
    }

    foreach($arr_comm_dev as $key1 => $value1){
        foreach($value1 as $key2 => $value2){
            $count = 0;
            foreach($value2 as $key3 => $value3){
                $count += $value3;
            }
            $arrcount[$key1][$key2]["dev"] += $count;
        }
    }

    foreach($arr_comm_unitid as $key1 => $value1){
        foreach($value1 as $key2 => $value2){
            foreach($value2 as $key3 => $value3){
                //如果最外围已经有x912 里面就不进行统计
                if (!array_key_exists($key3,$arr_comm_dev[$key1][$key2]) 
                    && !array_key_exists($key3,$arr_comm[$key1][$key2]))
                {
                    $count = 0;
                    foreach($value3 as $key4 => $value4){
                        $count += $value4;
                    }
                    $arrcount[$key1][$key2]["app"] += $count;
                }
            }
        }
    }

    foreach($arr_comm_unitid_dev as $key1 => $value1){
        foreach($value1 as $key2 => $value2){
            foreach($value2 as $key3 => $value3){
                //如果最外围已经有x912 里面就不进行统计
                if (!array_key_exists($key3,$arr_comm_dev[$key1][$key2]) 
                    && !array_key_exists($key3,$arr_comm[$key1][$key2]))
                {
                    $count = 0;
                    foreach($value3 as $key4 => $value4){
                        $count += $value4;
                    }
                    $arrcount[$key1][$key2]["dev"] += $count;
                }
            }
        }
    }   
    
    
    foreach($arr_signal as $key1 => $value1){
        foreach($value1 as $key2 => $value2){
            $count = 0;
            foreach($value2 as $key3 => $value3){
                $count += $value3;
            }
            $arrcount[$key1][$key2]["app"] += $count;
        }
    }
    foreach($arr_signal_dev as $key1 => $value1){
        foreach($value1 as $key2 => $value2){
            $count = 0;
            foreach($value2 as $key3 => $value3){
                $count += $value3;
            }
            $arrcount[$key1][$key2]["dev"] += $count;
        }
    }    
    
    $app = 0;
    $dev = 0;
    foreach($arrcount as $key1 => $value1){
        foreach($value1 as $key2 => $value2){
            $app += (int)$value2["app"];
            $dev += (int)$value2["dev"];
            TRACE($key1.','.$key2.','.(int)$value2["app"].','.(int)$value2["dev"]);
        }
    }
    if($forble_where == "1=1")
    {
        $appcount = $app;
    }
    

    TRACE("All,x912:$x912count x912_for_ble_app:$x912appcount,app:$app,indoor:$dev");
    TRACE("");
    TRACE("indoor info,fw_number, number");
    
    $indoorcount = array();
    foreach($indoor as $key1 => $value1){
        if ($key1 == "Node")
        {
            foreach($value1 as $key2 => $value2)
            {
                foreach($value2 as $key3 => $value3)
                {
                    $indoorcount[$key3] += $value3;
                } 
            }
        }
        else if ($key1 == "MngID")
        {
            foreach($value1 as $key2 => $value2)
            {
                foreach($value2 as $key3 => $value3)
                {
                    $indoorcount[$key3] += $value3;
                } 
            }            
        }
        else
        {
            if (!array_key_exists($key1,$indoor["MngID"]))
            {
                foreach($value1 as $key2 => $value2){
                    foreach($value2 as $key3 => $value3)
                    {
                        $indoorcount[$key3] += $value3;
                    } 
                }    
            } 
        }
    }       
    #var_dump($indoorcount);    
    
    foreach($indoorcount as $key1 => $value1){
        TRACE(",".$key1.','.$value1);
    }
    
    arsort($x912info);
    TRACE("");
    TRACE("X912 Info, Dis,Ins,Mac, CreateTime");
    foreach($x912info as $key1 => $value1){
        $dis = $value1["Dis"];
        $ins = $value1["Ins"];
        $ct = $value1["CreateTime"];
        TRACE(", $dis, $ins, $key1, $ct");
    }
    if (!($forble_where == "1=1"))
    {
        TRACE("");
        TRACE("X912 All, All App");
        TRACE("$allx912count,$appcount");        
    }

    /*
    echo("comm account\n");
    var_dump( $arr_comm);
    echo("comm dev\n");
    var_dump($arr_comm_dev);
    echo("comm unit account\n");
    var_dump($arr_comm_unitid);
    echo("comm unit dev\n");
    var_dump($arr_comm_unitid_dev);
    echo("sigal dev\n");
    var_dump($arr_signal_dev);
    echo("sigal account\n");
    var_dump($arr_signal);*/
}


//获取所有912下的app数据
TRACE("Dis,Ins,BleApp,Indoor");
BLE("1=1");
shell_exec("rm ". $STATIS_FILE);
shell_exec("touch ". $STATIS_FILE);
TRACE("Dis,Ins,BleApp,Indoor");
//获取912下有ble app或有室内机的数据
BLE("BLEOpenDoorType = 1");


$cmd = "echo \"$cloud x912 BLE数据统计 请看附件\" | mutt -s \"$cloud x912 BLE数据统计\" -a $STATIS_FILE  -b <EMAIL> -c <EMAIL>";
shell_exec($cmd);














