<?php
error_reporting(E_ALL || ~E_NOTICE);

if($argc != 2)
{
    echo ("usage:php ".$argv[0]." <xcloud>");
    exit(1);
}
$cloud = $argv[1];

$STATIS_FILE = "/tmp/$cloud.csv";
shell_exec("rm ". $STATIS_FILE);
shell_exec("touch ". $STATIS_FILE);
chmod(STATIS_FILE, 0777);

function TRACE($content)
{
    global $STATIS_FILE;
	@file_put_contents($STATIS_FILE, $content, FILE_APPEND);
	@file_put_contents($STATIS_FILE, "\n", FILE_APPEND);
}

function getDB(){
	$dbhost = "127.0.0.1";	//需在mysql主机执行
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
	$dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

$bleappcount = 0;

function countDev(){
   
    $db = getDB();

    $sth = $db->prepare("select MngAccountID,count(*) as count  From Devices where Firmware like \"28.%\" group by MngAccountID;");
    $sth->execute();
    $list = $sth->fetchALL(PDO::FETCH_ASSOC);
    TRACE("Community");
    TRACE("CommunityID,R28Count,FamilyCount,IndoorCount");
    $comm_count = 0;
    foreach($list as $key => $value){
        $mngid = $value['MngAccountID'];
        $count = $value['count'];
        
        $sth = $db->prepare("select count(*) as count From Devices where MngAccountID=:MngAccountID and Type=2 and grade=3;");
        $sth->bindParam(':MngAccountID', $mngid, PDO::PARAM_STR);
        $sth->execute();
        $indoor_count = $sth->fetch(PDO::FETCH_ASSOC)["count"];
        
        $sth = $db->prepare("select count(*) as count From PersonalAccount where Role=20 and ParentID=:MngAccountID and Special=0;");
        $sth->bindParam(':MngAccountID', $mngid, PDO::PARAM_STR);
        $sth->execute();
        $family_count = $sth->fetch(PDO::FETCH_ASSOC)["count"];
        if ($family_count == 0)//过滤掉社区
        {
            continue;
        }
        $comm_count += $count;
        TRACE("$mngid, $count, $family_count, $indoor_count");
        
    }
    TRACE("DevCount $comm_count");
    
    
    TRACE("\n\nOffice");
    TRACE("OfficeID,R28Count,IndoorCount");
    $comm_count = 0;
    foreach($list as $key => $value){
        $mngid = $value['MngAccountID'];
        $count = $value['count'];
        
        $sth = $db->prepare("select count(*) as count From Devices where MngAccountID=:MngAccountID and Type=2 and grade=3;");
        $sth->bindParam(':MngAccountID', $mngid, PDO::PARAM_STR);
        $sth->execute();
        $indoor_count = $sth->fetch(PDO::FETCH_ASSOC)["count"];
        
        $sth = $db->prepare("select count(*) as count From PersonalAccount where (Role=30 or Role=31) and ParentID=:MngAccountID and Special=0;");
        $sth->bindParam(':MngAccountID', $mngid, PDO::PARAM_STR);
        $sth->execute();
        $family_count = $sth->fetch(PDO::FETCH_ASSOC)["count"];
        if ($family_count == 0)
        {
            continue;
        }
        
        $comm_count += $count;
        TRACE("$mngid, $count, $indoor_count");
        
    }    
    TRACE("Office $comm_count");
    
    
    
    $sth = $db->prepare("select Node,count(*) as count  From PersonalDevices where Firmware like \"28.%\" group by Node;");
    $sth->execute();
    $list = $sth->fetchALL(PDO::FETCH_ASSOC);
    
    TRACE("\n\nSingle");
    TRACE("Node,R28Count,IndoorCount");
    $comm_count = 0;
    foreach($list as $key => $value){
        $node = $value['Node'];

        
        $sth = $db->prepare("select count(*) as count From PersonalDevices where Node=:Node and Type=2;");
        $sth->bindParam(':Node', $node, PDO::PARAM_STR);
        $sth->execute();
        $indoor_count = $sth->fetch(PDO::FETCH_ASSOC)["count"];

        
        $comm_count += $count;
        TRACE("$node, $count, $indoor_count");
        
    }    
    TRACE("Single $comm_count");    
    
}



countDev();





