<?php

function getDB()
{
    $dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}


function getInactiveAccountNumWithImportPrivateKey()
{
    $db = getDB();
    $sth = $db->prepare("SELECT COUNT(DISTINCT A.Account) AS count FROM PersonalAccount A LEFT JOIN CommPerPrivateKey B ON A.Account = B.Account WHERE A.Active = 0 AND B.Special = 0;");
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    echo "Inactive accounts with pin created by pm count:".$result['count']."\n";
}

function getInactiveAccountNumWithImportRfcard()
{
    $db = getDB();
    $sth = $db->prepare("SELECT COUNT(DISTINCT A.Account) AS count FROM PersonalAccount A LEFT JOIN CommPerRfKey B ON A.Account = B.Account WHERE A.Active = 0 AND B.IsCreateByPm = 1;");
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    echo "Inactive accounts with rf created by pm count:".$result['count']."\n";
}

function getInactiveAccountNumWithImportFace()
{
    $db = getDB();
    $sth = $db->prepare("SELECT COUNT(DISTINCT A.Account) AS count FROM PersonalAccount A LEFT JOIN FaceMng B ON A.ID = B.PersonalAccountID WHERE A.Active = 0 AND B.CreatorType = 0;");
    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    echo "Inactive accounts with face created by pm count:".$result['count']."\n";;
}

getInactiveAccountNumWithImportPrivateKey();
getInactiveAccountNumWithImportRfcard();
getInactiveAccountNumWithImportFace();
