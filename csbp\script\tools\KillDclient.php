<?php
//需要修改下面sql,来指定重启哪些设备

date_default_timezone_set('PRC');

$cloud = 'ucloud';
    
const STATIS_FILE = "/tmp/mac_list_offline.csv";
shell_exec("touch ". STATIS_FILE);

chmod(STATIS_FILE, 0777);
if (file_exists(STATIS_FILE)) {
    shell_exec("echo > ". STATIS_FILE);
} 
function STATIS_WRITE($content)
{
	file_put_contents(STATIS_FILE, $content, FILE_APPEND);
	file_put_contents(STATIS_FILE, "\n", FILE_APPEND);
}
function get_db_obj(){
    $PARAM_host='127.0.0.1';
    $PARAM_port='3306';
    $PARAM_db_name='AKCS';
    $PARAM_user='root';
    $PARAM_db_pass='Ak@56@<EMAIL>';

    $dbh = new PDO('mysql:host='.$PARAM_host.';port='.$PARAM_port.';dbname='.$PARAM_db_name,$PARAM_user,$PARAM_db_pass,null);
    return $dbh;
}


function send_post_login($url, $post_data) {
 
    $postdata = http_build_query($post_data);
    $options = array(
        'http' => array(
            'method' => 'POST',
            'header' => 'Content-type:application/x-www-form-urlencoded',
            'content' => $postdata,
            'timeout' => 15 * 60 
        ),
        'ssl' => array(
            "verify_peer"=>false,
            "verify_peer_name"=>false,
        )
    );
    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
 
    return $result;
}

function send_post_api($url, $post_data, $token) {
 
    $postdata = http_build_query($post_data);
    $options = array(
        'http' => array(
            'method' => 'POST',
                'header'=>"Content-type: application/x-www-form-urlencoded\r\n" .
                          "Authorization: JWT " . $token,
            'content' => $postdata,
            'timeout' => 15 * 60 
        ),
        'ssl' => array(
            "verify_peer"=>false,
            "verify_peer_name"=>false,
        )
    );
    $context = stream_context_create($options);
    $result = file_get_contents($url, false, $context);
 
    return $result;
}
function get_login_token() {
    $post_data_login = array(
        'username' => 'dev-monitor',
        'password' => 'aep#eeYo8ohV'
    );
    $url_login = 'https://maintenance.akuvox.com/api/login/';

    $result_login_json = send_post_login($url_login, $post_data_login);
    $result_login_arr = json_decode($result_login_json, true);
    $token = $result_login_arr['data']['token'];
    return $token;
}


function send_kill_dclient() {
    global $cloud;
    
    $token = get_login_token();

    $dbh = get_db_obj();
    
    //需要修改下列sql,来指定重启哪些设备
    $sth = $dbh->prepare("select MAC,Firmware from Devices where Status=0 limit 1;");
    $sth->execute();
    $mac_list = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($mac_list as $row => $mac_info)
    {
        $mac = $mac_info['MAC']; 
        $fw = $mac_info['Firmware'];
        $mac_fw = $mac . '-' . $fw;
        STATIS_WRITE($mac_fw);
     
        $post_data_login = array(
            'cloud' => $cloud,
            'mac' => $mac,
            'cmd' => 'console',
            'param' => 'killself'
        );
        $url_kill_dclient = 'https://maintenance.akuvox.com/api/maintenance/';

        $result_api_json = send_post_api($url_kill_dclient, $post_data_login, $token);
        //echo $result_api_json;
        $result_api_arr = json_decode($result_api_json, true);
        $code = $result_api_arr['code'];
        if ($code != 200) {
            echo 'kill dclient error, mac is ' . $mac . '\r\n';
        }
        usleep(50000);
    }
    
}

send_kill_dclient();
?>
