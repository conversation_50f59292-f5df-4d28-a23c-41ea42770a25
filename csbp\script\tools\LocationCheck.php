<?php

global $STATIS_FILE; 
$STATIS_FILE = "/tmp/reboot_dev_".date("Y-m-d").".csv";
shell_exec("touch ". $STATIS_FILE);
chmod($STATIS_FILE, 0777);
if (file_exists($STATIS_FILE)) {
	shell_exec("echo > ". $STATIS_FILE);
} 

function TRACE($content)
{  
	global $STATIS_FILE; 
	@file_put_contents($STATIS_FILE, $content, FILE_APPEND);
	@file_put_contents($STATIS_FILE, "\n", FILE_APPEND);
}

function getDB()
{
	$dbhost = "127.0.0.1";	//需在mysql主机执行
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
	$dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

function getCommunityInfo()
{
    $db = getDB();

    $sth = $db->prepare("select ID,Country,States,City from CommunityInfo");
    $db->exec("SET NAMES utf8mb4");
    $sth->execute();

    $communityInfoList = $sth->fetchALL(PDO::FETCH_ASSOC);

   // foreach ($communityInfoList as $communityInfo) {
     //   echo "ID = {$communityInfo["ID"]}, Country = {$communityInfo["Country"]}, States = {$communityInfo["States"]}, City = {$communityInfo["City"]} \n";
    //}

    return $communityInfoList;
}

function normalizeString($str) {
    // 先处理可能的编码问题
    if (!mb_check_encoding($str, 'UTF-8')) {
        $str = utf8_encode($str);
    }
    
    // 处理特殊字符
    $str = trim($str);
    
    $replacements = array(
        'é' => 'e',
        'è' => 'e',
        'ê' => 'e',
        'ë' => 'e',
        'É' => 'E',
        'È' => 'E',
        'Ê' => 'E',
        'Ë' => 'E',
        'ó' => 'o',
        'ò' => 'o',
        'ô' => 'o',
        'ō' => 'o',
        'ñ' => 'n',
        'á' => 'a',
        'à' => 'a',
        'â' => 'a',
        'í' => 'i',
        'ì' => 'i',
        'î' => 'i',
        'ú' => 'u',
        'ù' => 'u',
        'û' => 'u',
        'Á' => 'A',
        'À' => 'A',
        'Â' => 'A',
        'Í' => 'I',
        'Ì' => 'I',
        'Î' => 'I',
        'Ó' => 'O',
        'Ò' => 'O',
        'Ô' => 'O',
        'Ú' => 'U',
        'Ù' => 'U',
        'Û' => 'U',
        'Ñ' => 'N',
        '�' => 'e'  // 特别处理显示为�的字符
    );
    
    // 转换为小写并替换特殊字符
    $str = strtolower($str);
    $str = str_replace(array_keys($replacements), array_values($replacements), $str);
    
    // 移除所有其他非ASCII字符
    $str = preg_replace('/[^\x20-\x7E]/u', '', $str);
    
    return $str;
}

function getAddressData()
{
    $content = file_get_contents("/home/<USER>/address.js");

    // 提取数组内容 (假设格式为 var addressOption=[{...}])
    if (preg_match('/var\s+addressOption\s*=\s*(\[.*\])/s', $content, $matches)) {
        // 将 JS 数组转换为 PHP 数组
        $jsonStr = $matches[1];
        $addressData = json_decode($jsonStr, true);
        return $addressData;
    }
    
    return null;
}

function validateAddress($addressData, $country, $state, $city) {
    // 在地址数据中查找国家
    $countryData = null;
    foreach ($addressData as $c) {
        if (normalizeString($c['value']) == $country) {
            $countryData = $c;
            break;
        }
    }
    
    if (!$countryData) {
        return false;
    }
    
    // 查找省份/州
    $stateData = null;
    foreach ($countryData['province'] as $p) {
        if (normalizeString($p['value']) == $state) {
            $stateData = $p;
            break;
        }
    }
    
    if (!$stateData) {
        return false;
    }
    
    // 查找城市
    foreach ($stateData['city'] as $c) {
        if (normalizeString($c['value']) == $city) {
            return true;
        }
    }
    
    return false;
}

function checkLocation()
{
    $invalidCount = 0; // 计数器
    $totalCount = 0;   // 总记录数
    $addressData = getAddressData();
    $communityInfoList = getCommunityInfo();

    foreach ($communityInfoList as $communityInfo) {
        $totalCount++;

        // 在这里对数据库查出的数据进行标准化
        $normalizedCountry = normalizeString($communityInfo["Country"]);
        $normalizedStates = normalizeString($communityInfo["States"]);
        $normalizedCity = normalizeString($communityInfo["City"]);

        $isValid = validateAddress(
            $addressData,
            $normalizedCountry, 
            $normalizedStates, 
            $normalizedCity
        );
        
        if (!$isValid) {
            $invalidCount++;
               
            if ($communityInfo["Country"] == "United States") {
                echo "ID = {$communityInfo["ID"]}, " . 
                "Country = {$communityInfo["Country"]}, " .
                "States = {$communityInfo["States"]}, " .
                "City = {$communityInfo["City"]}, " .
                "Valid = " . ($isValid ? "Yes" : "No") . "\n";
            }
        }
    }

    // 输出统计结果
    echo "\n统计结果：\n";
    echo "总记录数：{$totalCount}\n";
    echo "无效地址数：{$invalidCount}\n";
    echo "有效地址数：" . ($totalCount - $invalidCount) . "\n";
    echo "错误率：" . round(($invalidCount / $totalCount) * 100, 2) . "%\n";

}

checkLocation();