#!/bin/sh

now=`date "+%Y-%m-%d"`" 00:00:00"
lastweek=`date +%F -d "-7day"`" 00:00:01"
nowdate=`date "+%Y-%m-%d"`

allfile=/home/<USER>
lastweekfile=/home/<USER>
summarystaticfile=/home/<USER>
app_allfile=/home/<USER>
app_lastweekfile=/home/<USER>
master_lastweekfile=/home/<USER>
#added by chenyc,增加社区更多维度的统计报表
commtotalfile=/home/<USER>
commlistfile=/home/<USER>

/usr/local/bin/php /home/<USER>"$lastweek" "$now"
cp /home/<USER>

/usr/local/bin/php /home/<USER>"$now"
cp /home/<USER>
#提取汇总表
cp /home/<USER>

/usr/local/bin/php /home/<USER>
cp /home/<USER>
cp /home/<USER>

#added by chenyc,2019-06-13,增加用户列表统计
#modified by chenyc,2019-07-30,增加每周新增master统计
/usr/local/bin/php /home/<USER>"$lastweek" "$now"
cp /home/<USER>
cp /home/<USER>
/usr/local/bin/php /home/<USER>
cp /home/<USER>

echo "美西服务器统计报告，请下载附件进行查看。" | mutt -s "美西统计报告" -a $allfile $lastweekfile $summarystaticfile $app_allfile $app_lastweekfile $master_lastweekfile $commtotalfile $commlistfile -b <EMAIL> -c <EMAIL>  -c <EMAIL> -c <EMAIL> -c <EMAIL> -c <EMAIL> -c <EMAIL> -c <EMAIL> -c <EMAIL> -c <EMAIL>



rm $lastweekfile $allfile $summarystaticfile $app_allfile $app_lastweekfile $master_lastweekfile $commlistfile $commtotalfile
