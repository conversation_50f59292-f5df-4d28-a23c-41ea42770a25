<?php

/*
检验pbx库的sip账号和akcs库里面sip是否一致。删除pbx多余的sip

*/
date_default_timezone_set("PRC");
ini_set("memory_limit", "250M");
function getDB()
{
    //这个升级的先不要弄主从的，直接在主库执行，不然sql语句就要考虑主从的问题
    $dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

function getDBPBX()
{
    //这个升级的先不要弄主从的，直接在主库执行，不然sql语句就要考虑主从的问题
    $dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "freeswitch";
    $dbport = 3305;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}


function CheckSip()
{
    $db = getDB();
    $sips = array();
    $sips_per = array();
    
    //社区主/pm/办公用户
    $sth = $db->prepare("select SipAccount as SipAccount, ParentID as MngAccountID From PersonalAccount where Role in (20,30,31,40);");
    $sth->execute();
    $siplist = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($siplist as $row => $username)
    {
        $sips[$username['SipAccount']] = $username['MngAccountID'];
    }

    //社区从账号
    $sth = $db->prepare("select P.SipAccount,PP.ParentID as MngAccountID  From PersonalAccount P left join PersonalAccount PP on P.ParentID=PP.ID where P.Role=21;");
    $sth->execute();
    $siplist = $sth->fetchALL(PDO::FETCH_ASSOC);

    foreach ($siplist as $row => $username)
    {
        $sips[$username['SipAccount']] = $username['MngAccountID'];
    }

    //社区办公设备
    $sth = $db->prepare("select SipAccount,MngAccountID From Devices;");
    $sth->execute();
    $siplist = $sth->fetchALL(PDO::FETCH_ASSOC);

    foreach ($siplist as $row => $username)
    {
        $sips[$username['SipAccount']] = $username['MngAccountID'];
    }

    //单住户主账号
    $sth = $db->prepare("select SipAccount as SipAccount, ParentID as MngAccountID From PersonalAccount where Role in (10,12);");
    $sth->execute();
    $siplist = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($siplist as $row => $username)
    {
        $sips_per[$username['SipAccount']] = $username['MngAccountID'];
    }
    
    //单住户从账号
    $sth = $db->prepare("select P.SipAccount,PP.ParentID as MngAccountID  From PersonalAccount P left join PersonalAccount PP on P.ParentID=PP.ID where P.Role=11;");
    $sth->execute();
    $siplist = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($siplist as $row => $username)
    {
        $sips_per[$username['SipAccount']] = $username['MngAccountID'];
    }    
    
    //单住户设备
    $sth = $db->prepare("select P.SipAccount,A.ID as MngAccountID From PersonalDevices P left join Account A on A.Account=P.Community;");
    $sth->execute();
    $siplist = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($siplist as $row => $username)
    {
        $sips_per[$username['SipAccount']] = $username['MngAccountID'];
    }  

    //pbx信息 
    $dbpbx = getDBPBX();
    $pbx_sips = array();
    $sth = $dbpbx->prepare("select username,communityType,communityid From freeswitch.userinfo;");
    $sth->execute();
    $siplist = $sth->fetchALL(PDO::FETCH_ASSOC);

    $per_error = array();
    $comm_error = array();
    
    $per_id_error = array();
    $comm_id_error = array();    
    foreach ($siplist as $row => $username)
    {
        if (isset($sips[$username["username"]]))
        {
            if ($username["communityType"] != 0)
            {
                echo "comm sip ".$username["username"]."\n";
                array_push($comm_error, $username["username"]);
            }
            if($username["communityid"] != $sips[$username["username"]])
            {
                echo "comm sip community id : ".$username["username"]."\n";
                array_push($comm_id_error, $username["username"]);
            }
        }
        
        if (isset($sips_per[$username["username"]]))
        {
            if ($username["communityType"] != 1)
            {
                echo "per sip ".$username["username"]."\n";
                array_push($per_error, $username["username"]);
            }
            if($username["communityid"] != $sips_per[$username["username"]])
            {
                echo "per sip per id : ".$username["username"]."\n";
                array_push($per_id_error, $username["username"]);
            }            
        }        
    }      
    $per = implode(",", $per_error);
    $comm = implode(",", $comm_error);
    $per_id = implode(",", $per_id_error);
    $comm_id = implode(",", $comm_id_error);    
    echo "per: $per\n";
    echo "comm:$comm\n";
    
    echo "per_id:$per_id\n";
    echo "comm_id:$comm_id\n";
    //communityType=0社区  communityType=1单住户
    #$sth = $dbpbx->prepare("update freeswitch.userinfo set communityType=1 where username in ($per);");
    #$sth->execute();
    
    #$sth = $dbpbx->prepare("update freeswitch.userinfo set communityType=0 where username in ($comm);");
    #$sth->execute();    
    return 1;
}



CheckSip();

