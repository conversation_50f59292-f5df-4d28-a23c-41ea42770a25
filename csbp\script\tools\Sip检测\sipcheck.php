<?php

/*
检验pbx库的sip账号和akcs库里面sip是否一致。删除pbx多余的sip

*/

exit(1);

date_default_timezone_set("PRC");
function getDB()
{
    //这个升级的先不要弄主从的，直接在主库执行，不然sql语句就要考虑主从的问题
    $dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

const STATIS_FILE1 = "/root/DeleteFreeSwitchSip.log";
shell_exec("touch ". STATIS_FILE1);
chmod(STATIS_FILE1, 0777);

function TRACE1($content)
{
    $tmpNow = time();
    #$Now = date('Y-m-d H:i:s', $tmpNow);    
	@file_put_contents(STATIS_FILE1, $content, FILE_APPEND);
	@file_put_contents(STATIS_FILE1, "\n", FILE_APPEND);
}


function CheckSip()
{
    $db = getDB();
    $sips = array();
    $sth = $db->prepare("select Account as SipAccount From PersonalAccount;");
    $sth->execute();
    $siplist = $sth->fetchALL(PDO::FETCH_ASSOC);
    TRACE1("per count=".count($siplist));
    foreach ($siplist as $row => $username)
    {
        array_push($sips, $username['SipAccount']);
    }

    $sth = $db->prepare("select SipAccount From Devices;");
    $sth->execute();
    $siplist = $sth->fetchALL(PDO::FETCH_ASSOC);
    TRACE1("Devices count=".count($siplist));
    foreach ($siplist as $row => $username)
    {
        array_push($sips, $username['SipAccount']);
    }
    
    $sth = $db->prepare("select SipAccount From PersonalDevices;");
    $sth->execute();
    $siplist = $sth->fetchALL(PDO::FETCH_ASSOC);
    TRACE1("PersonalDevices count=".count($siplist));
    foreach ($siplist as $row => $username)
    {
        array_push($sips, $username['SipAccount']);
    }  
    TRACE1("all count=".count($sips));
        
    
    $pbx_sips = array();
    $sth = $db->prepare("select username From freeswitch.userinfo;");
    $sth->execute();
    $siplist = $sth->fetchALL(PDO::FETCH_ASSOC);
    TRACE1("PersonalDevices count=".count($siplist));
    foreach ($siplist as $row => $username)
    {
        array_push($pbx_sips, $username['username']);
    }      
    
   TRACE1("pbx count=".count($pbx_sips));
   
   
    foreach ($sips as $row => $username)
    {

        
        if(in_array($username,$pbx_sips)){  
            //echo "匹配成功";  
        }else {
            TRACE1("$username not exist pbx");  
        }  
    }
    return 1;        
}

CheckSip();

?>
