<?php
date_default_timezone_set("PRC");
const STATIS_FILE = "./sip_enable.log";

const LOG_FILE = "/tmp/sipenableSearch.log";
shell_exec("rm ". LOG_FILE);
shell_exec("touch ". LOG_FILE);
chmod(LOG_FILE, 0777);
function TRACE($content)
{   
	@file_put_contents(LOG_FILE, $content, FILE_APPEND);
	@file_put_contents(LOG_FILE, "\n", FILE_APPEND);
}

function getDB()
{
    $dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";

    $mysql_conn_string = "mysql:host=$dbhost;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}
function getPbxDB()
{
    $dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "freeswitch";

    $mysql_conn_string = "mysql:host=$dbhost;port=3305;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

$db = getDB();
$db_pbx = getPbxDB();

$logFile = file(STATIS_FILE);
foreach($logFile as $key => $line)
{	
    $allPos = strpos($line, "all callee");
    if($allPos !== false)
    {
        continue;
    }

    $pos = strpos($line, "sipEnable");
    if($pos===false)
    {
        continue;
    }

    $groupPos = strpos($line, "groupcall");
    if($groupPos !== false) //群呼
    {
        $account = substr($line, $pos-10, 9);
        $callType = substr($line, $pos-17, 6);
        $sth = $db->prepare("SELECT Active, ExpireTime,ActiveTime FROM PersonalAccount WHERE Account = :account");
        $sth->bindParam(':account', $account, PDO::PARAM_STR);
        $sth->execute();
        $ret = $sth->fetch(PDO::FETCH_ASSOC);
        if($ret)
        {
            if($ret['Active'] == 1)
            {
                TRACE($callType." is "."APP"." ".$account." ActiveTime:".$ret['ActiveTime']." ExpireTime:".$ret['ExpireTime']);
                TRACE($line);
            }
        }
        else
        {
            $sth = $db->prepare("SELECT P.Account, P.Active, P.ExpireTime, P.ActiveTime  FROM Devices D JOIN PersonalAccount P ON D.Node = P.Account WHERE D.SipAccount = :account");
            $sth->bindParam(':account', $account, PDO::PARAM_STR);
            $sth->execute();
            $ret1 = $sth->fetch(PDO::FETCH_ASSOC);
            if($ret1)
            {
                if($ret1['Active'] == 1)
                {
                    TRACE($callType." is "."CommDev."."Owner is ".$ret1['Account']." ActiveTime:".$ret1['ActiveTime']." ExpireTime:".$ret1['ExpireTime']);
                    TRACE($line);
                }
            }
            else
            {
                $sth = $db->prepare("SELECT P.Account, P.Active, P.ExpireTime, P.ActiveTime  FROM PersonalDevices D JOIN PersonalAccount P ON D.Node = P.Account WHERE D.SipAccount = :account");
                $sth->bindParam(':account', $account, PDO::PARAM_STR);
                $sth->execute();
                $ret2 = $sth->fetch(PDO::FETCH_ASSOC);
                if($ret2)
                {
                    if($ret2['Active'] == 1)
                    {
                        TRACE($callType." is "."PerDev."."Owner is ".$ret2['Account']." ActiveTime:".$ret2['ActiveTime']." ExpireTime:".$ret2['ExpireTime']);
                        TRACE($line);
                    }
                }
                else
                {
                    $sth = $db_pbx->prepare("SELECT type FROM userinfo WHERE username = :account");
                    $sth->bindParam(':account', $account, PDO::PARAM_STR);
                    $sth->execute();
                    $pbxRet = $sth->fetch(PDO::FETCH_ASSOC);
                    if($pbxRet)
                    {
                        TRACE("error");
                    }
                    else
                    {
                        TRACE($account." Sip account has deleted");
                    }
                    TRACE($line);
                }
            }
            
        }
    }
    else    //单呼
    {
        $callType = substr($line, $pos-7, 6);
        $callPos = strpos($line, $callType);
        $callType1 = "";
        $callPos1 = "";
        if($callType == "caller")
        {
            $callType1 = "callee";
            $callPos1 = strpos($line, $callType1);
        }
        else
        {
            $callType1 = "caller";
            $callPos1 = strpos($line, $callType1);
        }
        if($callPos !== false)
        {
            $account = substr($line, $callPos+7, 9);
            $account1 = substr($line, $callPos1+7, 9);
            $sth = $db_pbx->prepare("SELECT type FROM userinfo WHERE username = :account");
            $sth->bindParam(':account', $account1, PDO::PARAM_STR);
            $sth->execute();
            $ret = $sth->fetch(PDO::FETCH_ASSOC);
            $userType = 111;
            if($ret)
            {
                $userType = $ret['type'];               
            }

            $sth = $db->prepare("SELECT Active, ExpireTime, ActiveTime FROM PersonalAccount WHERE Account = :account");
            $sth->bindParam(':account', $account, PDO::PARAM_STR);
            $sth->execute();
            $ret = $sth->fetch(PDO::FETCH_ASSOC);
            if($ret)
            {
                if($ret['Active'] == 1)
                {
                    TRACE($callType1." is ".$userType);
                    TRACE($callType." is "."APP"." ".$account." ActiveTime:".$ret['ActiveTime']." ExpireTime:".$ret['ExpireTime']);
                    TRACE($line);
                }
            }
            else
            {
                $sth = $db->prepare("SELECT P.Account, P.Active, P.ExpireTime, P.ActiveTime  FROM Devices D JOIN PersonalAccount P ON D.Node = P.Account WHERE D.SipAccount = :account");
                $sth->bindParam(':account', $account, PDO::PARAM_STR);
                $sth->execute();
                $ret1 = $sth->fetch(PDO::FETCH_ASSOC);
                if($ret1)
                {
                    if($ret1['Active'] == 1)
                    {
                        TRACE($callType." is "."CommDev."."Owner is ".$ret1['Account']." ActiveTime:".$ret1['ActiveTime']." ExpireTime:".$ret1['ExpireTime']);
                        TRACE($line);
                    }
                }
                else
                {
                    $sth = $db->prepare("SELECT P.Account, P.Active, P.ExpireTime, P.ActiveTime  FROM PersonalDevices D JOIN PersonalAccount P ON D.Node = P.Account WHERE D.SipAccount = :account");
                    $sth->bindParam(':account', $account, PDO::PARAM_STR);
                    $sth->execute();
                    $ret2 = $sth->fetch(PDO::FETCH_ASSOC);
                    if($ret2)
                    {
                        if($ret2['Active'] == 1)
                        {
                            TRACE($callType." is "."PerDev."."Owner is ".$ret2['Account']." ActiveTime:".$ret2['ActiveTime']." ExpireTime:".$ret2['ExpireTime']);
                            TRACE($line);
                        }
                    }
                    else
                    {
                        $sth = $db_pbx->prepare("SELECT type FROM userinfo WHERE username = :account");
                        $sth->bindParam(':account', $account, PDO::PARAM_STR);
                        $sth->execute();
                        $pbxRet = $sth->fetch(PDO::FETCH_ASSOC);
                        if($pbxRet)
                        {
                            TRACE("error");
                        }
                        else
                        {
                            TRACE($account." Sip account has deleted");
                        }
                        TRACE($line);
                    }
                }
                
            }
        }
    }
    usleep(5000);
}
