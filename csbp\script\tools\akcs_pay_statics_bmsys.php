<?php

//6.0之后，由于引入计费系统
//该脚本直接在生产环境的计费系统主站点执行即可
date_default_timezone_set('PRC');
const PAYPAL_STATIS_FILE = "/home/<USER>/cloud_pay_statastic.csv";
shell_exec("touch ". PAYPAL_STATIS_FILE);

if ($argc != 3)
{
    echo 'please input akcs_pay_statics.php timestart timeend';
    exit;
}
$timestart=$argv[1];
$timeend= $argv[2];
chmod(PAYPAL_STATIS_FILE, 0777);
if (file_exists(PAYPAL_STATIS_FILE)) {
    shell_exec("echo > ". PAYPAL_STATIS_FILE);
} 
function PAYPAL_STATIS_WRITE($content)
{
	file_put_contents(PAYPAL_STATIS_FILE, $content, FILE_APPEND);
	file_put_contents(PAYPAL_STATIS_FILE, "\n", FILE_APPEND);
}

function getDB()
{
    $dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "bmsys";

    $mysql_conn_string = "mysql:host=$dbhost;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

function getPaypalID($url){
        $headerArray =array("Authorization: Basic QVp6alZQVUI3c3plcTM5M1JBUjVUanNnVV9WMkxjajFQcFlybmw5V3lMVFR1R2RmdktaTENHazhBT1p2YWpMVU9EM0dYZjlIWlpLVVhhWmM6RUNwdjdzdE1XcWlKVVdCZ1N5bWQwdEpIcElVcHY3NUlMblJpNVZPaGQ5MmQzYzJmNThZb3JZTkhGLUFfVUVJOERaRWJ0NGkxQVJaZUFCZ0U=");
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE); 
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE); 
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch,CURLOPT_HTTPHEADER,$headerArray);
        $output = curl_exec($ch);
        curl_close($ch);
        $output = json_decode($output,true);
        return $output['purchase_units'][0]['payments']['captures'][0]['id'];
}

$db = getDB();
$paypal_url = 'https://api.paypal.com/v2/checkout/orders/';

$static_str = 'PayPlatformOrderID' . ',' .'AkOrderID'. ',' . 'Payer'. ',' . 'PayerEmail'. ',' .'Amount of Money'.',' . 'Pay Time'. ',' .'Pay Event Type'.',' .'PayPlatform' . ',';
PAYPAL_STATIS_WRITE($static_str);
$count = 0;
$sth = $db->prepare("select Number,(case Type when 1 then 'activation' when 2 then 'subscription' else 'additional app' end) as PayType,Payer, PayPlatEmail,PayPlatform,((FinalPrice - CouponCount)/100) as account,CreateTime, PayPlatOrder from Orders where (CreateTime between '".$timestart."' and '".$timeend."') and (Status = 1 or Status = 4) and (FinalPrice - CouponCount) > 0");
$sth->execute();
$paypal_list = $sth->fetchALL(PDO::FETCH_ASSOC);
foreach ($paypal_list as $row => $paypal)
{
    $pay_Platform = '';
    $paypal_PaypalOrder = $paypal['PayPlatOrder'];
    $paypal_OrderNumber = $paypal['Number'];
    $paypal_Payer = $paypal['Payer'];
    $paypal_account = $paypal['account'];
    $paypal_CreateTime = $paypal['CreateTime'];
    $paypal_Type = $paypal['PayType'];
    $paypal_Email = $paypal['PayPlatEmail'];
    $paypal_Platform = $paypal['PayPlatform'];
    if($paypal_Platform  == 0)
    {
        $pay_Platform = 'paypal';
        //远程调用id
        $url = $paypal_url . $paypal_PaypalOrder ;
        $paypal_PaypalOrder = getPaypalID($url);
    }
    else
    {
        $pay_Platform = 'stripe';
        //pi_1IvzuBDcItrxa574OjLOryWf_secret_AfF6W7g3XjUtVug6hqKWjTEFP 转成 pi_1IvzuBDcItrxa574OjLOryWf
        $paypal_PaypalOrder = substr($paypal_PaypalOrder, 0, 27);
    }

    $static_str = null;
    $static_str = $paypal_PaypalOrder.','. $paypal_OrderNumber .','. $paypal_Payer .','. $paypal_Email .','. $paypal_account .','. $paypal_CreateTime .','.$paypal_Type .','. $pay_Platform .',';

    PAYPAL_STATIS_WRITE($static_str);
    $count++;
    echo $count;
}
 
?>
