<?php
date_default_timezone_set('PRC');
const STATIS_FILE = "/home/<USER>";
shell_exec("touch ". STATIS_FILE);

if ($argc != 3)
{
    echo 'please input akcs_paypal_invoice_statastic.php timestart timeend';
    exit;
}
$timestart=$argv[1];
$timeend= $argv[2];
chmod(STATIS_FILE, 0777);
if (file_exists(STATIS_FILE)) {
    shell_exec("echo > ". STATIS_FILE);
} 
function STATIS_WRITE($content)
{
	file_put_contents(STATIS_FILE, $content, FILE_APPEND);
	file_put_contents(STATIS_FILE, "\n", FILE_APPEND);
}

function getDB()
{
    $dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";

    $mysql_conn_string = "mysql:host=$dbhost;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

$db = getDB();

//分成个人用户、社区管理员和installer账号三种类型去刷新
$paypal_total=array();
//先installer
$sth = $db->prepare("select R.PaypalOrder,R.OrderNumber ,R.Payer,(R.TotalPrice*R.Discount/10000) as account,R.CreateTime,I.BillingTitle,I.Contactor,I.Street,I.City,I.Postcode,I.Country,I.TelePhone,I.Fax from OrderList R left join Account A on R.AccountID=A.ID left join InstallerBillingInfo I on A.Account = I.Account where R.PayerType = 2 and R.Status = 1 and (R.CreateTime > '$timestart' and R.CreateTime< '$timeend') and (R.TotalPrice > 0 and R.Discount != 0)");
$sth->execute();
$paypal_list = $sth->fetchALL(PDO::FETCH_ASSOC);
foreach ($paypal_list as $row => $paypal)
{
    $paypal_PaypalOrder = $paypal['PaypalOrder'];
    $paypal_OrderNumber = $paypal['OrderNumber'];
    $paypal_Payer = $paypal['Payer'];
    $paypal_account = $paypal['account'];
    $paypal_CreateTime = $paypal['CreateTime'];
    $paypal_BillingTitle = $paypal['BillingTitle'];
    $paypal_Contactor = $paypal['Contactor'];
    //$paypal_Street = $paypal['Street'];
    $paypal_Street=mb_convert_encoding($paypal['Street'], 'UTF-8', 'UTF-8');
    $paypal_City = $paypal['City'];
    $paypal_Postcode = $paypal['Postcode'];
    $paypal_Country = $paypal['Country'];
    $paypal_TelePhone = $paypal['TelePhone'];
    $paypal_Fax = $paypal['Fax'];
    $paypal_array = array('PaypalOrder'=>$paypal_PaypalOrder,
                    'OrderNumber'=>$paypal_OrderNumber,
                    'Payer'=>$paypal_Payer,
                    'account'=>$paypal_account,
                    'CreateTime'=>$paypal_CreateTime,
                    'BillingTitle'=>$paypal_BillingTitle,
                    'Contactor'=>$paypal_Contactor,
                    'Street'=>$paypal_Street,
                    'City'=>$paypal_City,
                    'Postcode'=>$paypal_Postcode,
                    'Country'=>$paypal_Country,
                    'TelePhone'=>$paypal_TelePhone);
    array_push($paypal_total,$paypal_array);
}
//先property
$paypal_total_property=array();
$sth = $db->prepare("select R.PaypalOrder,R.OrderNumber ,R.Payer,(R.TotalPrice*R.Discount/10000) as account,R.CreateTime,I.BillingTitle,I.Contactor,I.Street,I.City,I.Postcode,I.Country,I.TelePhone,I.Fax,P.FirstName,P.LastName from OrderList R left join Account A on R.AccountID=A.ID left join PropertyInfo P on P.AccountID = R.AccountID left join PropertyBillingInfo I on A.Account = I.Account where R.PayerType = 1 and R.Status = 1 and (R.CreateTime > '$timestart' and R.CreateTime< '$timeend') and (R.TotalPrice > 0 and R.Discount != 0)");
$sth->execute();
$paypal_list = $sth->fetchALL(PDO::FETCH_ASSOC);
foreach ($paypal_list as $row => $paypal)
{
    $paypal_PaypalOrder = $paypal['PaypalOrder'];
    $paypal_OrderNumber = $paypal['OrderNumber'];
    $paypal_Payer = $paypal['Payer'];
    $paypal_account = $paypal['account'];
    $paypal_CreateTime = $paypal['CreateTime'];
    $paypal_BillingTitle = $paypal['BillingTitle'];
    $paypal_Contactor = $paypal['Contactor'];
    $paypal_Street = $paypal['Street'];
    $paypal_City = $paypal['City'];
    $paypal_Postcode = $paypal['Postcode'];
    $paypal_Country = $paypal['Country'];
    $paypal_TelePhone = $paypal['TelePhone'];
    $paypal_Fax = $paypal['Fax'];
    $paypal_FirstName = $paypal['FirstName'];
    //$paypal_LastName = $paypal['LastName'];
    $paypal_array = array('PaypalOrder'=>$paypal_PaypalOrder,
                    'OrderNumber'=>$paypal_OrderNumber,
                    'Payer'=>$paypal_Payer,
                    'account'=>$paypal_account,
                    'CreateTime'=>$paypal_CreateTime,
                    'BillingTitle'=>$paypal_BillingTitle,
                    'Contactor'=>$paypal_Contactor,
                    'Street'=>$paypal_Street,
                    'City'=>$paypal_City,
                    'Postcode'=>$paypal_Postcode,
                    'Country'=>$paypal_Country,
                    'TelePhone'=>$paypal_TelePhone);
    array_push($paypal_total,$paypal_array);
}
//最后是enduser
$paypal_total_enduser=array();
$sth = $db->prepare("select R.PaypalOrder,R.OrderNumber ,R.Payer,(R.TotalPrice*R.Discount/10000) as account,R.CreateTime,I.BillingTitle,I.Contactor,I.Street,I.City,I.Postcode,I.Country,I.TelePhone,I.Fax,P.FirstName,P.LastName from OrderList R left join Account A on R.AccountID=A.ID left join PersonalAccount P on P.ID = R.AccountID left join PersonalBillingInfo I on A.Account = I.Account where R.PayerType = 0 and R.Status = 1 and (R.CreateTime > '$timestart' and R.CreateTime< '$timeend') and (R.TotalPrice > 0 and R.Discount != 0)");
$sth->execute();
$paypal_list = $sth->fetchALL(PDO::FETCH_ASSOC);
foreach ($paypal_list as $row => $paypal)
{
    $paypal_PaypalOrder = $paypal['PaypalOrder'];
    $paypal_OrderNumber = $paypal['OrderNumber'];
    $paypal_Payer = $paypal['Payer'];
    $paypal_account = $paypal['account'];
    $paypal_CreateTime = $paypal['CreateTime'];
    $paypal_BillingTitle = $paypal['BillingTitle'];
    $paypal_Contactor = $paypal['Contactor'];
    $paypal_Street = $paypal['Street'];
    $paypal_City = $paypal['City'];
    $paypal_Postcode = $paypal['Postcode'];
    $paypal_Country = $paypal['Country'];
    $paypal_TelePhone = $paypal['TelePhone'];
    $paypal_Fax = $paypal['Fax'];
    $paypal_FirstName = $paypal['FirstName'];
    $paypal_LastName = $paypal['LastName'];
    $paypal_array = array('PaypalOrder'=>$paypal_PaypalOrder,
                    'OrderNumber'=>$paypal_OrderNumber,
                    'Payer'=>$paypal_Payer,
                    'account'=>$paypal_account,
                    'CreateTime'=>$paypal_CreateTime,
                    'BillingTitle'=>$paypal_BillingTitle,
                    'Contactor'=>$paypal_Contactor,
                    'Street'=>$paypal_Street,
                    'City'=>$paypal_City,
                    'Postcode'=>$paypal_Postcode,
                    'Country'=>$paypal_Country,
                    'TelePhone'=>$paypal_TelePhone);
    array_push($paypal_total,$paypal_array);
}

echo 'paypal count is '. count($paypal_total);
 $json_invoice = json_encode($paypal_total);
 STATIS_WRITE($json_invoice);
?>
