<?php
date_default_timezone_set('PRC');
const PAYPAL_STATIS_FILE = "/home/<USER>";
shell_exec("touch ". PAYPAL_STATIS_FILE);

if ($argc != 3)
{
    echo 'please input akcs_paypal_statics.php timestart timeend';
    exit;
}
$timestart=$argv[1];
$timeend= $argv[2];
chmod(PAYPAL_STATIS_FILE, 0777);
if (file_exists(PAYPAL_STATIS_FILE)) {
    shell_exec("echo > ". PAYPAL_STATIS_FILE);
} 
function PAYPAL_STATIS_WRITE($content)
{
	file_put_contents(PAYPAL_STATIS_FILE, $content, FILE_APPEND);
	file_put_contents(PAYPAL_STATIS_FILE, "\n", FILE_APPEND);
}

function getDB()
{
    $dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";

    $mysql_conn_string = "mysql:host=$dbhost;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

$db = getDB();

$static_str = 'PayPalOrderID' . ',' .'AkOrderID'. ',' . 'Payer'. ',' . 'Amount of Money'.',' . 'Pay Time'. ',' .'Pay Event Type'.',' .'PayPlatform' . ',';
PAYPAL_STATIS_WRITE($static_str);

$sth = $db->prepare("select PaypalOrder,OrderNumber ,Payer,(TotalPrice*Discount/10000) as account,CreateTime,(case Type when 1 then 'activation' when 2 then 'subscription' else 'additional app' end) as PayType, PayPlatform from OrderList where (CreateTime between '".$timestart."' and '".$timeend."') and Status = 1 and (TotalPrice > 0 and Discount != 0)");
$sth->execute();
$paypal_list = $sth->fetchALL(PDO::FETCH_ASSOC);
foreach ($paypal_list as $row => $paypal)
{
    $pay_Platform = '';
    $paypal_PaypalOrder = $paypal['PaypalOrder'];
    $paypal_OrderNumber = $paypal['OrderNumber'];
    $paypal_Payer = $paypal['Payer'];
    $paypal_account = $paypal['account'];
    $paypal_CreateTime = $paypal['CreateTime'];
    $paypal_Type = $paypal['PayType'];
    $paypal_Platform = $paypal['PayPlatform'];
    if($paypal_Platform  == 0)
    {
        $pay_Platform = 'paypal';
    }
    else
    {
        $pay_Platform = 'stripe';
    }
    $static_str = null;
    $static_str = $paypal_PaypalOrder.','. $paypal_OrderNumber .','. $paypal_Payer .','. $paypal_account .','. $paypal_CreateTime .','.$paypal_Type .','. $pay_Platform .',';

    PAYPAL_STATIS_WRITE($static_str);
}
 
?>
