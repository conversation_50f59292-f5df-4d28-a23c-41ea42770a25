1、拷贝call_detect_pcap ak_els.conf call_detect_run.sh到需要的目录
2、修改ak_els.conf得tcpdump_path指定存放pcap/log目录
   detect_caller_list 为监控得sip账号(主叫/被叫都可以，只要一通呼叫包含对应的账号就会抓包)
3、抓包的文件格式和日志说明：
   3.1 抓包格式
   主叫：$sip-时间-$ip-$uuid-$callerid
   被叫：$sip-时间-$ip-$uuid-callee
   例如：
	   6504100005_1124_192800_110.87.81.36_1acb0d72-5a32-4f48-a4e5-2fad40132f9a_1777385560.pcap                                      nohup.out
	   6504100005_1124_192800_110.87.81.36_96393da7-894d-421f-b901-036ec67f44a5_callee.pcap
	   
	3.2 日志文件为$tcpdump_path/detect.log
4、启动程序
   nohup bash call_detect_run.sh >/dev/null &
   
   


代码编译说明：
   1、开启esl模块和编译 生成esl静态库。
      cd src/freeswitch-1.6.17
	  vi modules.conf
	     开启applications/mod_esl
   2、编译call_detect_pcap
      2.1 到目录src\freeswitch-1.6.17\libs\esl
      2.2 vi read_ak	  