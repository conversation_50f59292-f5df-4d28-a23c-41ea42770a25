<?php

//检验社区设备实际文件的md5值和数据库是否一致
exit;

date_default_timezone_set('PRC');
const STATIS_FILE = "/tmp/check_file_md5.txt";
shell_exec("touch ". STATIS_FILE);
chmod(STATIS_FILE, 0777);
if (file_exists(STATIS_FILE)) {
    //unlink(STATIS_FILE);
    shell_exec("echo > ". STATIS_FILE);
} 

function STATIS_TRACE($content)
{
	file_put_contents(STATIS_FILE, $content, FILE_APPEND);
	file_put_contents(STATIS_FILE, "\n", FILE_APPEND);
}

function getDB()
{
    $dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $dbport = 3306;
    
    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}
$db = getDB();

$ROOT="/var/www/download/community";
$sth = $db->prepare("select ID,MngAccountID,UnitID,Node, MAC,Type,Grade,PrivatekeyMD5,RfidMD5,ConfigMD5,ContactMD5 from Devices");
$sth->execute();
$dev_all = $sth->fetchALL(PDO::FETCH_ASSOC);
foreach ($dev_all as $row => $dev)
{	
	$mng = $dev['MngAccountID'];
	$unit = $dev['UnitID'];
	$node = $dev['Node'];
	$mac = $dev['MAC'];
	$id = $dev['ID'];
	
	$type = $dev['Type'];
	$grade = $dev['Grade'];
	
	$pri_md5 = $dev['PrivatekeyMD5'];
	$rf_md5 = $dev['RfidMD5'];
	$config_md5 = $dev['ConfigMD5'];
	$contact_md5 = $dev['ContactMD5'];
	
	
	STATIS_TRACE("DB===pri=$pri_md5 rf=$rf_md5 cf=$config_md5 ct=$contact_md5");
	$is_change = 0;
    //COMMUNITY_DEVICE_TYPE_NONE = 0,//代表个人
    //COMMUNITY_DEVICE_TYPE_PUBLIC = 1,
    //COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT = 2,
    //COMMUNITY_DEVICE_TYPE_PERSONAL =3,
	
	if ($grade == 3)//个人
	{
		if ($type == 2 || $type == 3 )//室内机 管理机
		{
			$config=$ROOT."/".$mng."/".$unit."/".$node."/Config/".$mac.".cfg";
			$contact=$ROOT."/".$mng."/".$unit."/".$node."/ContactList/".$mac.".xml";
			
			$config_file_md5=md5_file($config);
			$contact_file_md5=md5_file($contact);
		}
		else
		{
			$config=$ROOT."/".$mng."/".$unit."/".$node."/Config/".$mac.".cfg";
			$pri=$ROOT."/".$mng."/".$unit."/".$node."/Privatekey/".$mac.".xml";
			$rfid=$ROOT."/".$mng."/".$unit."/".$node."/Rfid/".$mac.".xml";
			$contact=$ROOT."/".$mng."/".$unit."/".$node."/ContactList/".$mac.".xml";

			$config_file_md5=md5_file($config);
			$contact_file_md5=md5_file($contact);
			$rfid_file_md5=md5_file($rfid);
			$pri_file_md5=md5_file($pri);			
		}		
	}
	else if ($grade == 1)//公共 
	{
		if ($type != 2 && $type != 3)//不是室内机
		{
			$config=$ROOT."/".$mng."/Public_".$mng."/Config/".$mac.".cfg";
			$pri=$ROOT."/".$mng."/Public_".$mng."/Privatekey/".$mac.".xml";
			$rfid=$ROOT."/".$mng."/Public_".$mng."/Rfid/".$mac.".xml";
			$contact=$ROOT."/".$mng."/Public_".$mng."/ContactList/".$mac.".xml";

			$config_file_md5=md5_file($config);
			$contact_file_md5=md5_file($contact);
			$rfid_file_md5=md5_file($rfid);
			$pri_file_md5=md5_file($pri);				
		}		
	}
	else if ($grade == 2)//单元
	{
		if ($type != 2 && $type != 3 )//不是室内机
		{
			$config=$ROOT."/".$mng."/".$unit."/Public_".$unit."/Config/".$mac.".cfg";
			$pri=$ROOT."/".$mng."/".$unit."/Public_".$unit."/Privatekey/".$mac.".xml";
			$rfid=$ROOT."/".$mng."/".$unit."/Public_".$unit."/Rfid/".$mac.".xml";
			$contact=$ROOT."/".$mng."/".$unit."/Public_".$unit."/ContactList/".$mac.".xml";	

			$config_file_md5=md5_file($config);
			$contact_file_md5=md5_file($contact);
			$rfid_file_md5=md5_file($rfid);
			$pri_file_md5=md5_file($pri);	
		}					
	}
	
	if ($config_md5 !="" && $config_file_md5 != $config_md5)
	{
		STATIS_TRACE("MAC: $mac change:$config");
		$config_md5 = $config_file_md5;
		$is_change = 1;
	}
	
	if ($contact_md5 !="" && $contact_file_md5 != $contact_md5)
	{
		STATIS_TRACE("MAC: $mac change:$contact");
		$contact_md5 = $contact_file_md5;
		$is_change = 1;
	}
	
	if ($type != 2 && $type != 3)
	{
		if ($rf_md5 !="" && $rfid_file_md5 != $rf_md5)
		{
			STATIS_TRACE("MAC: $mac change:$rfid");
			$rf_md5 = $rfid_file_md5;
			$is_change = 1;
		}
		
		if ($pri_md5 !="" && $pri_file_md5 != $pri_md5)
		{
			STATIS_TRACE("MAC: $mac change:$pri");
			$pri_md5 = $pri_file_md5;
			$is_change = 1;
		}
	}
	if ($is_change == 1)
	{
		$sth = $db->prepare("update Devices set PrivatekeyMD5=:PrivatekeyMD5,RfidMD5=:RfidMD5,ConfigMD5=:ConfigMD5,ContactMD5=:ContactMD5 where id=$id");
		$sth->bindParam(':PrivatekeyMD5', $pri_md5, PDO::PARAM_STR);
		$sth->bindParam(':RfidMD5', $rf_md5, PDO::PARAM_STR);
		$sth->bindParam(':ConfigMD5', $config_md5, PDO::PARAM_STR);
		$sth->bindParam(':ContactMD5', $contact_md5, PDO::PARAM_STR);
		$sth->execute();
		STATIS_TRACE("FILE===pri=$pri_file_md5 rf=$rfid_file_md5 cf=$config_file_md5 ct=$contact_file_md5");
	}	
}

?>
