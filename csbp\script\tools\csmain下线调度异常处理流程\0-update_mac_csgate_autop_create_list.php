<?php

const AKCSInnerIP="db.akcs.ucloud.akcs.inner";

if ($argc < 3 || empty($argv[1]) || empty($argv[2])) {
    echo "Usage: php script.php <csmain_ip> <out_mac_list_file_name> \n";
    exit(1);
}


function GetAkcsDb()
{
    $dbhost = AKCSInnerIP;
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

$AccSrvID = $argv[1];
$out_file_name = $argv[2];

if (file_exists($out_file_name)) {
    echo "The file $filename exists. check if config already handle, delete it to continue";
    exit(1);
}


$pdo = GetAkcsDb();

$update_mac = array();

$sql = "select AccSrvID,Mac,Status,Config ,'Devices' AS Source From Devices where AccSrvID=:AccSrvID and Status=1  union all select AccSrvID,Mac,Status,Config, 'PersonalDevices' AS Source From PersonalDevices where AccSrvID=:AccSrvID  and Status=1 ";
$stmt = $pdo->prepare($sql);
$stmt->bindParam(':AccSrvID', $AccSrvID);
$stmt->execute();
$result = $stmt->fetchALL(PDO::FETCH_ASSOC);
foreach ($result as $row => $values)
{
    $status = $values["Status"];
    $mac = $values["Mac"];;
    $srv = $values["AccSrvID"];
    $Config = $values["Config"];
    $Source = $values["Source"];

    $var = array();
    $var["Mac"] = $mac;
    $var["Config"] = $Config;
    $update_mac[] = $var;  
} 

#$DateAndTime = date('m-d-Y-h-i-s', time()); 
$json_string = json_encode($update_mac);
file_put_contents($out_file_name, $json_string);



















