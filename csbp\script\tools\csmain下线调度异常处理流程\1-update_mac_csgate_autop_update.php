<?php
const AKCSInnerIP="db.akcs.ucloud.akcs.inner";

function GetAkcsDb()
{
    $dbhost = AKCSInnerIP;
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

$AccSrvID ="*************";  #预下线的csmain
$reconnect_gate="**********"; #网关ip
$reconnect_csmain="**********"; #其他csmain的任意一个节点

if ($argc < 2 || empty($argv[1])) {
    echo "Usage: php script.php <mac_json> \n";
    exit(1);
}

$mac_file = $argv[1];


$jsonData = file_get_contents($mac_file);
$dataArray = json_decode($jsonData, true);
$pdo = GetAkcsDb();

foreach ($dataArray as $value)
{
    $mac = $value["Mac"];
    $connect_config="Config.DoorSetting.CLOUDSERVER.Server = $reconnect_csmain\nConfig.DoorSetting.CLOUDSERVER.Port = 8501\nConfig.DoorSetting.CLOUDSERVER.GateServer = $reconnect_gate\nConfig.DoorSetting.CLOUDSERVER.GatePort = 9999\n";


    $sql = "update Devices set Config=:Config where Mac=:Mac limit 1";
    $sql2 = "update PersonalDevices set Config=:Config where Mac=:Mac limit 1";

    
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':Config', $connect_config);
    $stmt->bindParam(':Mac', $mac);
    $stmt->execute();

    $stmt = $pdo->prepare($sql2);
    $stmt->bindParam(':Config', $connect_config);
    $stmt->bindParam(':Mac', $mac);
    $stmt->execute();    

    $cmd = "curl http://$AccSrvID:9998/UpdateConfig -d '{\"mac\":\"$mac\"}'";
    echo "$cmd\n";
    shell_exec($cmd);
}