<?php


const AKCSInnerIP="db.akcs.ucloud.akcs.inner";


// 检查是否提供了命令行参数
if ($argc < 2 || empty($argv[1])) {
    // 如果没有提供参数或第一个参数为空，则退出并显示消息
    echo "Usage: php script.php <mac_json> \n";
    exit(1);
}

$mac_file = $argv[1];

function GetAkcsDb()
{
    $dbhost = AKCSInnerIP;
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

$jsonData = file_get_contents($mac_file);
$dataArray = json_decode($jsonData, true);
$pdo = GetAkcsDb();


foreach ($dataArray as $value)
{
    $mac = $value["Mac"];

    $sql = "select AccSrvID,Mac,Status,Config ,'Devices' AS Source From Devices where Mac=:Mac and Status=1  union all select AccSrvID,Mac,Status,Config, 'PersonalDevices' AS Source From PersonalDevices where Mac=:Mac  and Status=1 ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':Mac', $mac);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($result)
    {
        $status = $result["Status"];
        $mac = $result["Mac"];;
        $srv = $result["AccSrvID"];
        if($status == 0)
        {
            echo("$mac status = 0\n"); 
            continue;
        }
    
    } 

    $cmd = "curl http://$srv:9998/rebootDev -d '{\"mac\":\"$mac\"}'";
    echo "$cmd\n";
    shell_exec($cmd);
}
