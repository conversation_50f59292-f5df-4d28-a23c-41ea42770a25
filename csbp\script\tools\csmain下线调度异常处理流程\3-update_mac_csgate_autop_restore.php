<?php


const AKCSInnerIP="db.akcs.ucloud.akcs.inner";


// 检查是否提供了命令行参数
if ($argc < 3 || empty($argv[1]) || empty($argv[2])) {
    // 如果没有提供参数或第一个参数为空，则退出并显示消息
    echo "Usage: php script.php <mac_json> <one_csmain_ip> \n";
    exit(1);
}
$mac_file = $argv[1];
$csmain_srv = $argv[2];

function GetAkcsDb()
{
    $dbhost = AKCSInnerIP;
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

$jsonData = file_get_contents($mac_file);
$dataArray = json_decode($jsonData, true);
$pdo = GetAkcsDb();


foreach ($dataArray as $value)
{
    $mac = $value["Mac"];
    $config = $value["Config"];

    $sql = "update Devices set Config=:Config where Mac=:Mac limit 1";
    $sql2 = "update PersonalDevices set Config=:Config where Mac=:Mac limit 1";
    
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':Mac', $mac);
    $stmt->bindParam(':Config', $config);
    $stmt->execute();

    $stmt = $pdo->prepare($sql2);
    $stmt->bindParam(':Mac', $mac);
    $stmt->bindParam(':Config', $config);
    $stmt->execute();    


    $cmd = "curl http://$csmain_srv:9998/UpdateConfig -d '{\"mac\":\"$mac\"}'";
    echo "$cmd\n";
    shell_exec($cmd);  

}
