
问题背景：
csmain预下线后我们在网关不会分配这个csmain，但是如果这时候设备域名解析出现异常，设备没有办法重新走rps和网关，导致csmain地址都不会更新。

处理流程：
1、首先通过csmain运维通道更新设备的网关。执行脚步 php reconnect-gate.php
2、还没有调度成功的就是因为域名问题，只能通过配置autop的方式
2.1、根据srvid生成需要处理的mac列表： php 0-update_mac_csgate_autop_create_list.php
2.1 更新autop指定网关和csmain地址  
    更新1-update_mac_csgate_autop_update.php里面的配置：
        $AccSrvID ="*************";  #预下线的csmain
        $reconnect_gate="**********"; #网关ip
        $reconnect_csmain="**********"; #其他csmain的任意一个节点
    php 1-update_mac_csgate_autop_update.php
2.2 等待个两三分钟 让设备重新下载配置
    重启这些设备(有可能更新autop后没有直接生效，也可以通过4-update_mac_csgate_autop_check.php校验)
    php 2-update_mac_csgate_autop_reboot.php
2.3 校验是否有全部更新 php 4-update_mac_csgate_autop_check.php
2.4 恢复客户自己配置的autop 3-update_mac_csgate_autop_restore.php 
