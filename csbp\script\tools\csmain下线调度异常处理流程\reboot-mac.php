<?php


const AKCSInnerIP="db.akcs.ucloud.akcs.inner";

function GetAkcsDb()
{
    $dbhost = AKCSInnerIP;
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

const STATIS_FILE = "./mac-reboot.log";
function STATIS_TRACE($content)
{
	file_put_contents(STATIS_FILE, $content, FILE_APPEND);
	file_put_contents(STATIS_FILE, "\n", FILE_APPEND);
}


$jsonData = file_get_contents('mac.json');
$dataArray = json_decode($jsonData, true);
$pdo = GetAkcsDb();


foreach ($dataArray as $mac)
{
    $sql = "select AccSrvID,Mac,Status From Devices where Mac=:Mac  union all select AccSrvID,Mac,Status From PersonalDevices where Mac=:Mac";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':Mac', $mac);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    if ($result)
    {
        $status = $result["Status"];
        $mac = $result["Mac"];;
        $srv = $result["AccSrvID"];
        STATIS_TRACE("$mac $status");
        if ($status== 0)
        {
            continue;
        }
        $srv = $result["AccSrvID"];
        if (strlen($srv) > 0)
        {
            $cmd = "curl http://$srv:9998/rebootDev -d '{\"mac\":\"$mac\"}'";
            echo "$cmd\n";
            shell_exec($cmd);   
        }
    } 

}
