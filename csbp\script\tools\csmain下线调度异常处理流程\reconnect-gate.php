<?php
const AKCSInnerIP="db.akcs.ucloud.akcs.inner";

function GetAkcsDb()
{
    $dbhost = AKCSInnerIP;
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

$AccSrvID ="*************";
$reconnect_gate="**********:9999";

const STATIS_FILE = "./reconnect-gate.log";
function STATIS_TRACE($content)
{
	file_put_contents(STATIS_FILE, $content, FILE_APPEND);
	file_put_contents(STATIS_FILE, "\n", FILE_APPEND);
}

STATIS_TRACE("Start");


$pdo = GetAkcsDb();

$sql = "select AccSrvID,Mac,Status From Devices where AccSrvID=:AccSrvID  union all select AccSrvID,Mac,Status From PersonalDevices where AccSrvID=:AccSrvID";
$stmt = $pdo->prepare($sql);
$stmt->bindParam(':AccSrvID', $AccSrvID);
$stmt->execute();
$result = $stmt->fetchALL(PDO::FETCH_ASSOC);
foreach ($result as $row => $values)
{
    $status = $values["Status"];
    $mac = $values["Mac"];;
    $srv = $values["AccSrvID"];
    STATIS_TRACE("$mac $status");
    if ($status== 0)
    {
        continue;
    }
    if (strlen($srv) > 0)
    {
        $cmd = "curl -H \"Content-Type:application/json\" -X POST http://$srv:9998/reconnectGateway -d '{\"mac\":\"$mac\",\"serveraddr\":\"$reconnect_gate\"}'";
        echo "$cmd\n";
        shell_exec($cmd);   
    }
} 
