<?php 
#需修改WEB_URL $headers
require_once (dirname(__FILE__).'/curl.php');

const WEB_URL = "https://ecloud.akuvox.com/apache-v3.0/";
const STATIS_FILE = "/tmp/rfcard_import.log";
shell_exec("rm ". STATIS_FILE);
shell_exec("touch ". STATIS_FILE);
chmod(STATIS_FILE, 0777);
function TRACE($content)
{
    $tmpNow = time();
    $Now = date('Y-m-d H:i:s', $tmpNow);    
	@file_put_contents(STATIS_FILE, $Now." ".$content, FILE_APPEND);
	@file_put_contents(STATIS_FILE, "\n", FILE_APPEND);
}

			/*直接用小龙的excel解析*/
            $fileName = "RFCard_Template.xlsx";
            require_once './PHPExcel/PHPExcel/IOFactory.php';

            $objPHPExcel = PHPExcel_IOFactory::load($fileName);

            //获取表格行数
            $rowCount = $objPHPExcel->getActiveSheet()->getHighestRow();
            //获取表格列数
			$columnCount = $objPHPExcel->getActiveSheet()->getHighestColumn();


			$header2Key = [
				"RF Card"=>"card",
				"User"=>"user",
				"Door"=>"device",
				"RepeatDay"=>"repeat",
				"StartDay"=>"startDay",
				"StartTime"=>"startTime",
				"EndDay"=>"endDay",
				"EndTime"=>"endTime"
			];

			$dataArr = [];
            $keyColumn = [];

			$startRow = 1;
            for ($row = $startRow; $row <= $rowCount; $row++) {
				if($row != $startRow) $data = ["row"=>$row];

				// 列循环
				for ($column = 'A'; $column <= $columnCount; $column++) {
					// 获取单元格值
					$value = $objPHPExcel->getActiveSheet()->getCell($column.$row)->getValue();
					$value = $value === null ? "" : $value;
					// 获取第一行值
					if($row == $startRow) {
						$key = $header2Key[$value];
						$keyColumn[$column] = $key;
					}else {
						$cellText = $value;
                        $key = $keyColumn[$column];
						if(!$key) continue;
						elseif(($key == "card" && $value == "") || ($key == "user" && $value == "")) continue 2;
						elseif($key == "startDay" || $key == "endDay")  $value = date("Y-m-d",\PHPExcel_Shared_Date::ExcelToPHP($value));
						elseif($key == "startTime" || $key == "endTime")  $value = gmdate("H:i:s",\PHPExcel_Shared_Date::ExcelToPHP($value));
						$data[$key] = $value;
					}
				}
				if($row != $startRow) array_push($dataArr,$data);
			}
			

			
foreach ($dataArr as $val) {
	
    $request_data['Key'] = $val["card"];
    $request_data['Name'] = $val["user"];
    $request_data['SchedulerType'] = 1;	//每日计划
    $request_data['StartTime'] = "00:00:00";
	$request_data['StopTime'] = "23:59:59";
	$request_data['Type'] = 1;
	
	$deviceList = explode(";",$val["device"]);
	$macArray = [];
	foreach ($deviceList as $deviceItem) 
	{
		if($deviceItem == "") continue;
		$device = explode(":",$deviceItem);
		$macValue = $device[0];
		$mac['MAC'] = $macValue;
		$mac['Relay'] = 0;	//relay0
		array_push($macArray,$mac);
	}
	
	$request_data['MAC'] = $macArray;
            
    $headers = array(
        'x-cloud-version: 5.2',
        'x-auth-token: 15W95K3s1p9219P8',
        'x-community-id: 1763'
    );

    $output = httpRequest('post', WEB_URL.'addpdrfcard', $headers, $request_data);
	TRACE($val["card"].$output);
	sleep(10);
}

?>
