<?php

const TMPLOG = "/tmp/devcfg.csv";
const CONFIG_ARRAY = ["Config.DoorSetting.GENERAL.Theme",
"Config.DoorSetting.LANGUAGE.BuildingLanVisible",
"Config.DoorSetting.DISPLAY.Key1Type",
"Config.DoorSetting.DISPLAY.Key2Type",
"Config.DoorSetting.DISPLAY.Key3Type",
"Config.DoorSetting.DISPLAY.Key4Type",
"Config.DoorSetting.DISPLAY.Key5Type",
"Config.DoorSetting.DISPLAY.Key6Type",
"Config.DoorSetting.GENERAL.DisplayType",
"Config.DoorSetting.GENERAL.DisplayVisible",];

//R29 X915 X916
const CONFIG_THEME = "Config.DoorSetting.GENERAL.Theme";
const CONFIG_ARRAY1 = ["Config.DoorSetting.GENERAL.DisplayType",
"Config.DoorSetting.GENERAL.DisplayVisible",];
const CONFIG_ARRAY2 = ["Config.DoorSetting.LANGUAGE.BuildingLanVisible",
"Config.DoorSetting.DISPLAY.Key1Type",
"Config.DoorSetting.DISPLAY.Key2Type",
"Config.DoorSetting.DISPLAY.Key3Type",
"Config.DoorSetting.DISPLAY.Key4Type",
"Config.DoorSetting.DISPLAY.Key5Type",
"Config.DoorSetting.DISPLAY.Key6Type",];


const SEARCH_FIRMWARE = "29.%";

function logWrite($content)
{
	file_put_contents(TMPLOG, $content, FILE_APPEND);
	file_put_contents(TMPLOG, "\n", FILE_APPEND);
}

function getDB()
{
    $dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";

    $mysql_conn_string = "mysql:host=$dbhost;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

function getCfg($mac, $cfg){
    $resp = shell_exec("/usr/local/akcs/csmain/bin/csmain_cli -x '$mac;getcfg $cfg'");
    $cfgValue = explode("$cfg=", $resp);	
    if(count($cfgValue)>1)	//获取正常
    {
        $value = trim($cfgValue[1]);	//去掉换行符
        return $value;
    }
    return "";
}

//生成csv文件
shell_exec("touch ". TMPLOG);
chmod(TMPLOG, 0777);
if (file_exists(TMPLOG)) {
    shell_exec("echo > ". TMPLOG);
} 

$title = 'project'.','.'mac'.',';
foreach(CONFIG_ARRAY as $config){
    $title = $title."$config,";
}
logWrite($title);

$db = getDB();	
/*
//单住户
$sth = $db->prepare("SELECT MAC FROM PersonalDevices WHERE Status = 1 AND DclientVer > 4400 AND Firmware like '" . SEARCH_FIRMWARE . "'");
$sth->execute();
$list = $sth->fetchAll(PDO::FETCH_ASSOC);	
foreach ($list as $row => $value){
        $mac = $value['MAC'];
        $result = "single,"."$mac,";
        foreach(CONFIG_ARRAY as $config){
            $csgResult = getCfg($mac, $config);	
            $result = $result."$csgResult,";          
        }
        logWrite($result);
        usleep(100000);
}
*/
$sth = $db->prepare("SELECT P.MAC FROM PersonalDevices P join Account A on P.Community = A.Account join  Account A1 on A.ParentID = A1.ID WHERE A1.Account in ('Blvs','idealtech') AND P.Status = 1 AND P.DclientVer > 4400 AND P.Firmware like '" . SEARCH_FIRMWARE . "'");
$sth->execute();
$list = $sth->fetchAll(PDO::FETCH_ASSOC);	
foreach ($list as $row => $value){
        $mac = $value['MAC'];
        $result = "single,"."$mac,";
        
        $csgResult = getCfg($mac, CONFIG_THEME);	
        $result = $result."$csgResult,";   
        $theme = $csgResult;

        if($theme == 1){
            $result = $result.",,,,,,,";
            foreach(CONFIG_ARRAY1 as $config){               
                $csgResult = getCfg($mac, $config);	
                $result = $result."$csgResult,";          
            }
        }else if($theme == 2){
            foreach(CONFIG_ARRAY2 as $config){               
                $csgResult = getCfg($mac, $config);	
                $result = $result."$csgResult,";          
            }  
        }
        logWrite($result);
        usleep(300000);
}

//社区
$sth = $db->prepare("select A.ID from Account A join Account B on A.ParentID=B.ID  where A.Grade = 21 and B.Account in ('Blvs','idealtech')");
$sth->execute();
$list = $sth->fetchAll(PDO::FETCH_ASSOC);	
$resident_1_10 = []; //10户以下项目
$resident_10_50 = []; //50户以下
$resident_50_500 = []; //500户以下
$resident_500 = []; //500户以上
foreach ($list as $row => $value){
    $commID = $value['ID'];
    $sth = $db->prepare("select count(ID) as cnt from PersonalAccount where ParentID = :id and Role = 20");
    $sth->bindParam(':id', $commID, PDO::PARAM_INT);
    $sth->execute();
    $ret = $sth->fetch(PDO::FETCH_ASSOC);	
    if(!$ret){
        continue;
    }
    $cnt = $ret['cnt'];
    if($cnt < 10){
        array_push($resident_1_10, $commID);
    }else if($cnt < 50){
        array_push($resident_10_50, $commID);
    }else if($cnt < 500){
        array_push($resident_50_500, $commID);
    }else{
        array_push($resident_500, $commID);
    }
}

cfgOutput2($db, $resident_1_10, "1~10");
cfgOutput2($db, $resident_10_50, "10~50");
cfgOutput2($db, $resident_50_500, "50~500");
cfgOutput2($db, $resident_500, "500+");



function cfgOutput($db, $community, $tag)
{
    foreach ($community as $id){
        $sth = $db->prepare("SELECT MAC FROM Devices WHERE MngAccountID = :id AND Status = 1 AND DclientVer > 4400 AND Firmware like '" . SEARCH_FIRMWARE . "'");
        $sth->bindParam(':id', $id, PDO::PARAM_INT);
        $sth->execute();
        $list = $sth->fetchAll(PDO::FETCH_ASSOC);
        if($list){	 
            echo "$tag:$id\n"; 
        }
        foreach ($list as $row => $value){
            $mac = $value['MAC'];
            $result = $tag.",$mac,";
            foreach(CONFIG_ARRAY as $config){
                $csgResult = getCfg($mac, $config);	
                $result = $result."$csgResult,";          
            }
            logWrite($result);
            usleep(300000);
        } 
    }
}

function cfgOutput2($db, $community, $tag)
{
    foreach ($community as $id){
        $sth = $db->prepare("SELECT MAC FROM Devices WHERE MngAccountID = :id AND Status = 1 AND DclientVer > 4400 AND Firmware like '" . SEARCH_FIRMWARE . "'");
        $sth->bindParam(':id', $id, PDO::PARAM_INT);
        $sth->execute();
        $list = $sth->fetchAll(PDO::FETCH_ASSOC);	
        if($list){	 
            echo "$tag:$id\n"; 
        }   
        foreach ($list as $row => $value){
            $mac = $value['MAC'];
            $result = $tag.",$mac,";

            $csgResult = getCfg($mac, CONFIG_THEME);	
            $result = $result."$csgResult,";   
            $theme = $csgResult;

            if($theme == 1){
                $result = $result.",,,,,,,";
                foreach(CONFIG_ARRAY1 as $config){               
                    $csgResult = getCfg($mac, $config);	
                    $result = $result."$csgResult,";          
                }
            }else if($theme == 2){
                foreach(CONFIG_ARRAY2 as $config){               
                    $csgResult = getCfg($mac, $config);	
                    $result = $result."$csgResult,";          
                }  
            }

            logWrite($result);
            usleep(300000);
        } 
    }
}

