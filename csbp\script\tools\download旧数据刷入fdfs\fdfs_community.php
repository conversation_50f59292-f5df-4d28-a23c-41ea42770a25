<?php
//刷社区/office项目的设备影子

//需要在web1节点执行
function getDB(){
	$dbhost = "127.0.0.1";	//修改db地址
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
	$dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

function GetDevPath($value){
    if($value['Grade'] == 1){
        $tmp_path = '/var/www/download/community/'.$value['MngAccountID'].'/Public_'.$value['MngAccountID'];
        $download_path['PrivatekeyPath'] = $tmp_path.'/Privatekey/'.$value['MAC'].'.xml';
        $download_path['RfidPath'] = $tmp_path.'/Rfid/'.$value['MAC'].'.xml';
        $download_path['ConfigPath'] = $tmp_path.'/Config/'.$value['MAC'].'.cfg';
        $download_path['ContactPath'] = $tmp_path.'/ContactList/'.$value['MAC'].'.xml';
        $download_path['FacePath'] = $tmp_path.'/Face/'.$value['MAC'].'.xml';
        $download_path['UserMetaPath'] = $tmp_path.'/UserMeta/'.$value['MAC'].'.json';
        $download_path['SchedulePath'] = $tmp_path.'/Schedule/'.$value['MAC'].'.json';
    }
    else if($value['Grade'] == 2){
        $tmp_path = '/var/www/download/community/'.$value['MngAccountID'].'/'.$value['UnitID'].'/Public_'.$value['UnitID'];
        $download_path['PrivatekeyPath'] = $tmp_path.'/Privatekey/'.$value['MAC'].'.xml';
        $download_path['RfidPath'] = $tmp_path.'/Rfid/'.$value['MAC'].'.xml';
        $download_path['ConfigPath'] = $tmp_path.'/Config/'.$value['MAC'].'.cfg';
        $download_path['ContactPath'] = $tmp_path.'/ContactList/'.$value['MAC'].'.xml';
        $download_path['FacePath'] = $tmp_path.'/Face/'.$value['MAC'].'.xml';
        $download_path['UserMetaPath'] = $tmp_path.'/UserMeta/'.$value['MAC'].'.json';
        $download_path['SchedulePath'] = $tmp_path.'/Schedule/'.$value['MAC'].'.json';
    }
    else{
        $tmp_path = '/var/www/download/community/'.$value['MngAccountID'].'/'.$value['UnitID'].'/'.$value['Node'];
        $download_path['PrivatekeyPath'] = $tmp_path.'/Privatekey/'.$value['MAC'].'.xml';
        $download_path['RfidPath'] = $tmp_path.'/Rfid/'.$value['MAC'].'.xml';
        $download_path['ConfigPath'] = $tmp_path.'/Config/'.$value['MAC'].'.cfg';
        $download_path['ContactPath'] = $tmp_path.'/ContactList/'.$value['MAC'].'.xml';
        $download_path['FacePath'] = $tmp_path.'/Face/'.$value['MAC'].'.xml';
        $download_path['UserMetaPath'] = $tmp_path.'/UserMeta/'.$value['MAC'].'.json';
        $download_path['SchedulePath'] = $tmp_path.'/Schedule/'.$value['MAC'].'.json';
    } 
    return $download_path;
}

function storage_file_to_fdfs($filePath){
    $ret = false;
    if(!file_exists($filePath)){
        echo "no exist\n";
        return $ret;
    }
    $tracker = fastdfs_tracker_get_connection();
    if (!$tracker){
        return $ret;
    }
    $storage = fastdfs_tracker_query_storage_store("group2");
    if (!$storage){
        return $ret;
    }
    $server = fastdfs_connect_server($storage['ip_addr'], $storage['port']);
    if (!$server){
        return $ret;
    }
    $storage['sock'] = $server['sock'];

    
    $file_info = fastdfs_storage_upload_by_filename($filePath, null, array(), null, $tracker, $storage);
    if ($file_info)
    {
       $group_name = $file_info['group_name'];
       $remote_filename = $file_info['filename'];
       $ret = '/'.$file_info['group_name'].'/'.$file_info['filename'];
    }

    fastdfs_disconnect_server($storage);
    return $ret;
}

function UpdateDevPath($column, $path, $mac, $db)
{
    $sql = "update DevicesShadow set $column = :path where MAC = :mac";
    $sth = $db->prepare($sql);
    $sth->bindParam(':path', $path, PDO::PARAM_STR);
    $sth->bindParam(':mac', $mac, PDO::PARAM_STR);
    $sth->execute();
}

function logWrite($content)
{
	file_put_contents(TMPLOG, $content, FILE_APPEND);
	file_put_contents(TMPLOG, "\n", FILE_APPEND);
}

const TMPLOG = "/tmp/fdfs_community.csv";
shell_exec("touch ". TMPLOG);
chmod(TMPLOG, 0777);
logWrite('column,mac,path_before,path_after,');

    $db = getDB();
    $sth = $db->prepare("select MAC,Grade,MngAccountID,UnitID,Node,PrivatekeyMD5,RfidMD5,ConfigMD5,ContactMD5,FaceMD5,UserMetaMD5,ScheduleMD5 from Devices limit 1000");
    $sth->execute();
    $list = $sth->fetchAll(PDO::FETCH_ASSOC);
    foreach($list as $value){
        $download_path = GetDevPath($value);
        $sth = $db->prepare("select PrivatekeyPath,RfidPath,ConfigPath,ContactPath,FacePath,UserMetaPath,SchedulePath from DevicesShadow where MAC = :mac");
        $sth->bindParam(':mac', $value['MAC'], PDO::PARAM_STR);
        $sth->execute();
        $path = $sth->fetch(PDO::FETCH_ASSOC);      
        if(!$path){
            $sth = $db->prepare("insert into DevicesShadow(MAC) value(:mac)");
            $sth->bindParam(':mac', $value['MAC'], PDO::PARAM_STR);
            $sth->execute();
        }
        if(strlen($value['PrivatekeyMD5']) > 0){
            if(!$path || !strstr($path['PrivatekeyPath'], 'group2')){                   
                //echo $download_path['PrivatekeyPath']."\n";
                $fdfs_path = storage_file_to_fdfs($download_path['PrivatekeyPath']);
                if($fdfs_path){
                    UpdateDevPath('PrivatekeyPath', $fdfs_path, $value['MAC'], $db);
                    logWrite('PrivatekeyPath,'.$value['MAC'].','.$download_path['PrivatekeyPath'].','.$fdfs_path.',');
                }
            }
        }
        if(strlen($value['RfidMD5']) > 0){
            if(!$path || !strstr($path['RfidPath'], 'group2')){
                //echo $download_path['RfidPath']."\n";
                $fdfs_path = storage_file_to_fdfs($download_path['RfidPath']);
                if($fdfs_path){
                    UpdateDevPath('RfidPath', $fdfs_path, $value['MAC'], $db);
                    logWrite('RfidPath,'.$value['MAC'].','.$download_path['RfidPath'].','.$fdfs_path.',');
                }
            }
        }
        if(strlen($value['ConfigMD5']) > 0){
            if(!$path || !strstr($path['ConfigPath'], 'group2')){
                //echo $download_path['ConfigPath']."\n";
                $fdfs_path = storage_file_to_fdfs($download_path['ConfigPath']);
                if($fdfs_path){
                    UpdateDevPath('ConfigPath', $fdfs_path, $value['MAC'], $db);
                    logWrite('ConfigPath,'.$value['MAC'].','.$download_path['ConfigPath'].','.$fdfs_path.',');
                }
            }
        }
        if(strlen($value['ContactMD5']) > 0){
            if(!$path || !strstr($path['ContactPath'], 'group2')){
                //echo $download_path['ContactPath']."\n";
                $fdfs_path = storage_file_to_fdfs($download_path['ContactPath']);
                if($fdfs_path){
                    UpdateDevPath('ContactPath', $fdfs_path, $value['MAC'], $db);
                    logWrite('ContactPath,'.$value['MAC'].','.$download_path['ContactPath'].','.$fdfs_path.',');
                }
            }
        }
        if(strlen($value['FaceMD5']) > 0){
            if(!$path || !strstr($path['FacePath'], 'group2')){
                //echo $download_path['FacePath']."\n";
                $fdfs_path = storage_file_to_fdfs($download_path['FacePath']);
                if($fdfs_path){
                    UpdateDevPath('FacePath', $fdfs_path, $value['MAC'], $db);
                    logWrite('FacePath,'.$value['MAC'].','.$download_path['FacePath'].','.$fdfs_path.',');
                }
            }
        }
        if(strlen($value['UserMetaMD5']) > 0){
            if(!$path || !strstr($path['UserMetaPath'], 'group2')){
                //echo $download_path['UserMetaPath']."\n";
                $fdfs_path = storage_file_to_fdfs($download_path['UserMetaPath']);
                if($fdfs_path){
                    UpdateDevPath('UserMetaPath', $fdfs_path, $value['MAC'], $db);
                    logWrite('UserMetaPath,'.$value['MAC'].','.$download_path['UserMetaPath'].','.$fdfs_path.',');
                }
            }
        }
        if(strlen($value['ScheduleMD5']) > 0){
            if(!$path || !strstr($path['SchedulePath'], 'group2')){
                //echo $download_path['SchedulePath']."\n";
                $fdfs_path = storage_file_to_fdfs($download_path['SchedulePath']);
                if($fdfs_path){
                    UpdateDevPath('SchedulePath', $fdfs_path, $value['MAC'], $db);
                    logWrite('SchedulePath,'.$value['MAC'].','.$download_path['SchedulePath'].','.$fdfs_path.',');
                }
            }
        }

    }





    
