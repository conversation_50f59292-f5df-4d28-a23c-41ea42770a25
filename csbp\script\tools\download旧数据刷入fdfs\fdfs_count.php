<?php
//统计数量用，看下服务器上还有大概多少旧数据量
//需要在web1节点执行

function getDB(){
	$dbhost = "127.0.0.1";	//修改db地址
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
	$dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

function GetDevPath($value){
    if($value['Grade'] == 1){
        $tmp_path = '/var/www/download/community/'.$value['MngAccountID'].'/Public_'.$value['MngAccountID'];
        $download_path['PrivatekeyPath'] = $tmp_path.'/Privatekey/'.$value['MAC'].'.xml';
        $download_path['RfidPath'] = $tmp_path.'/Rfid/'.$value['MAC'].'.xml';
        $download_path['ConfigPath'] = $tmp_path.'/Config/'.$value['MAC'].'.cfg';
        $download_path['ContactPath'] = $tmp_path.'/ContactList/'.$value['MAC'].'.xml';
        $download_path['FacePath'] = $tmp_path.'/Face/'.$value['MAC'].'.xml';
        $download_path['UserMetaPath'] = $tmp_path.'/UserMeta/'.$value['MAC'].'.json';
        $download_path['SchedulePath'] = $tmp_path.'/Schedule/'.$value['MAC'].'.json';
    }
    else if($value['Grade'] == 2){
        $tmp_path = '/var/www/download/community/'.$value['MngAccountID'].'/'.$value['UnitID'].'/Public_'.$value['UnitID'];
        $download_path['PrivatekeyPath'] = $tmp_path.'/Privatekey/'.$value['MAC'].'.xml';
        $download_path['RfidPath'] = $tmp_path.'/Rfid/'.$value['MAC'].'.xml';
        $download_path['ConfigPath'] = $tmp_path.'/Config/'.$value['MAC'].'.cfg';
        $download_path['ContactPath'] = $tmp_path.'/ContactList/'.$value['MAC'].'.xml';
        $download_path['FacePath'] = $tmp_path.'/Face/'.$value['MAC'].'.xml';
        $download_path['UserMetaPath'] = $tmp_path.'/UserMeta/'.$value['MAC'].'.json';
        $download_path['SchedulePath'] = $tmp_path.'/Schedule/'.$value['MAC'].'.json';
    }
    else{
        $tmp_path = '/var/www/download/community/'.$value['MngAccountID'].'/'.$value['UnitID'].'/'.$value['Node'];
        $download_path['PrivatekeyPath'] = $tmp_path.'/Privatekey/'.$value['MAC'].'.xml';
        $download_path['RfidPath'] = $tmp_path.'/Rfid/'.$value['MAC'].'.xml';
        $download_path['ConfigPath'] = $tmp_path.'/Config/'.$value['MAC'].'.cfg';
        $download_path['ContactPath'] = $tmp_path.'/ContactList/'.$value['MAC'].'.xml';
        $download_path['FacePath'] = $tmp_path.'/Face/'.$value['MAC'].'.xml';
        $download_path['UserMetaPath'] = $tmp_path.'/UserMeta/'.$value['MAC'].'.json';
        $download_path['SchedulePath'] = $tmp_path.'/Schedule/'.$value['MAC'].'.json';
    } 
    return $download_path;
}

    $db = getDB();
    $sth = $db->prepare("select count(*) as cnt from FaceMng where FaceUrl not like '/group2%'");
    $sth->execute();
    $ret = $sth->fetch(PDO::FETCH_ASSOC);
    echo "FaceMng: ".$ret['cnt']."\n";

    $sth = $db->prepare("select count(*) as cnt from PersonalAppTmpKey where  (SchedulerType != 0 or EndTime > now()) and QrCodeUrl like '/download%'");
    $sth->execute();
    $ret = $sth->fetch(PDO::FETCH_ASSOC);
    echo "PersonalAppTmpKey: ".$ret['cnt']."\n";

    $sth = $db->prepare("select count(*) as cnt from PubAppTmpKey where  (SchedulerType != 0 or EndTime > now()) and QrCodeUrl like '/download%'");
    $sth->execute();
    $ret = $sth->fetch(PDO::FETCH_ASSOC);
    echo "PubAppTmpKey: ".$ret['cnt']."\n";

//limit可进行调整
    $sth = $db->prepare("select MAC,Grade,MngAccountID,UnitID,Node,PrivatekeyMD5,RfidMD5,ConfigMD5,ContactMD5,FaceMD5,UserMetaMD5,ScheduleMD5 from Devices limit 1000");
    $sth->execute();
    $list = $sth->fetchAll(PDO::FETCH_ASSOC);
    foreach($list as $value){
        $download_path = GetDevPath($value);
        $sth = $db->prepare("select PrivatekeyPath,RfidPath,ConfigPath,ContactPath,FacePath,UserMetaPath,SchedulePath from DevicesShadow where MAC = :mac");
        $sth->bindParam(':mac', $value['MAC'], PDO::PARAM_STR);
        $sth->execute();
        $path = $sth->fetch(PDO::FETCH_ASSOC);      

        if(strlen($value['PrivatekeyMD5']) > 0){
            if(!$path || !strstr($path['PrivatekeyPath'], 'group2')){                   
                if(file_exists($download_path['PrivatekeyPath'])){
                    echo $download_path['PrivatekeyPath']."\n";
                }
            }
        }
        if(strlen($value['RfidMD5']) > 0){
            if(!$path || !strstr($path['RfidPath'], 'group2')){
                if(file_exists($download_path['RfidPath'])){
                    echo $download_path['RfidPath']."\n";
                }
            }
        }
        if(strlen($value['ConfigMD5']) > 0){
            if(!$path || !strstr($path['ConfigPath'], 'group2')){
                if(file_exists($download_path['ConfigPath'])){
                    echo $download_path['ConfigPath']."\n";
                }
            }
        }
        if(strlen($value['ContactMD5']) > 0){
            if(!$path || !strstr($path['ContactPath'], 'group2')){
                if(file_exists($download_path['ContactPath'])){
                    echo $download_path['ContactPath']."\n";
                }
            }
        }
        if(strlen($value['FaceMD5']) > 0){
            if(!$path || !strstr($path['FacePath'], 'group2')){
                if(file_exists($download_path['FacePath'])){
                    echo $download_path['FacePath']."\n";
                }
            }
        }
        if(strlen($value['UserMetaMD5']) > 0){
            if(!$path || !strstr($path['UserMetaPath'], 'group2')){
                if(file_exists($download_path['UserMetaPath'])){
                    echo $download_path['UserMetaPath']."\n";
                }
            }
        }
        if(strlen($value['ScheduleMD5']) > 0){
            if(!$path || !strstr($path['SchedulePath'], 'group2')){
                if(file_exists($download_path['SchedulePath'])){
                    echo $download_path['SchedulePath']."\n";
                }
            }
        }

    }





   
