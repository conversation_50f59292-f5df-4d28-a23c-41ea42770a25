<?php
//刷人脸FaceMng到fdfs

//需要在web1节点执行
function getDB(){
	$dbhost = "*************";	//修改db地址
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
	$dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

function storage_file_to_fdfs($filePath){
    $ret = false;
    if(!file_exists($filePath)){
        return $ret;
    }
    $tracker = fastdfs_tracker_get_connection();
    if (!$tracker){
        return $ret;
    }
    $storage = fastdfs_tracker_query_storage_store("group2");
    if (!$storage){
        return $ret;
    }
    $server = fastdfs_connect_server($storage['ip_addr'], $storage['port']);
    if (!$server){
        return $ret;
    }
    $storage['sock'] = $server['sock'];

    
    $file_info = fastdfs_storage_upload_by_filename($filePath, null, array(), null, $tracker, $storage);
    if ($file_info)
    {
       $group_name = $file_info['group_name'];
       $remote_filename = $file_info['filename'];
       $ret = '/'.$file_info['group_name'].'/'.$file_info['filename'];
    }

    fastdfs_disconnect_server($storage);
    return $ret;
}

function logWrite($content)
{
	file_put_contents(TMPLOG, $content, FILE_APPEND);
	file_put_contents(TMPLOG, "\n", FILE_APPEND);
}

const TMPLOG = "/tmp/fdfs_face.csv";
shell_exec("touch ". TMPLOG);
chmod(TMPLOG, 0777);
logWrite('id,path_before,path_after,');

    $db = getDB();
    $sth = $db->prepare("select ID,FaceUrl from FaceMng where FaceUrl not like '/group2%' limit 1");
    $sth->execute();
    $ret = $sth->fetchAll(PDO::FETCH_ASSOC);
    foreach($ret as $value){
        $download_path = '/var/www/download/face'.$value['FaceUrl'];
        $face_id = $value['ID'];
        $fdfs_path = storage_file_to_fdfs($download_path);
        if($fdfs_path){
            $sth = $db->prepare("update FaceMng set FaceUrl = :url where ID = :id");
            $sth->bindParam(':id', $face_id, PDO::PARAM_INT);
            $sth->bindParam(':url', $fdfs_path, PDO::PARAM_STR);
            $sth->execute();
            logWrite($face_id.','.$download_path.','.$fdfs_path.',');
        }
    }






   
