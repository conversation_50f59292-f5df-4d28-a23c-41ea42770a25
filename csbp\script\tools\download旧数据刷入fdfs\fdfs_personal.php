<?php
//刷单住户项目的设备影子，和社区主要区别在于download路径的查找方式不同

//需要在web1节点执行
function getDB(){
	$dbhost = "127.0.0.1";	//修改db地址
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
	$dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

function GetDevPath($value){
    $tmp_path = '/var/www/download/personal/node_*/'.$value['Node'];  //使用glob方法就可以精准找到路径
    $download_path['PrivatekeyPath'] = $tmp_path.'/Privatekey/'.$value['MAC'].'.xml';
    $download_path['RfidPath'] = $tmp_path.'/Rfid/'.$value['MAC'].'.xml';
    $download_path['ConfigPath'] = $tmp_path.'/Config/'.$value['MAC'].'.cfg';
    $download_path['ContactPath'] = $tmp_path.'/ContactList/'.$value['MAC'].'.xml';
    $download_path['FacePath'] = $tmp_path.'/Face/'.$value['MAC'].'.xml';
    return $download_path;
}

function storage_file_to_fdfs($filePath){
    $ret = false;
    if(!file_exists($filePath)){
        echo "no exist\n";
        return $ret;
    }
    $tracker = fastdfs_tracker_get_connection();
    if (!$tracker){
        return $ret;
    }
    $storage = fastdfs_tracker_query_storage_store("group2");
    if (!$storage){
        return $ret;
    }
    $server = fastdfs_connect_server($storage['ip_addr'], $storage['port']);
    if (!$server){
        return $ret;
    }
    $storage['sock'] = $server['sock'];

    
    $file_info = fastdfs_storage_upload_by_filename($filePath, null, array(), null, $tracker, $storage);
    if ($file_info)
    {
       $group_name = $file_info['group_name'];
       $remote_filename = $file_info['filename'];
       $ret = '/'.$file_info['group_name'].'/'.$file_info['filename'];
    }

    fastdfs_disconnect_server($storage);
    return $ret;
}
function logWrite($content)
{
	file_put_contents(TMPLOG, $content, FILE_APPEND);
	file_put_contents(TMPLOG, "\n", FILE_APPEND);
}

const TMPLOG = "/tmp/fdfs_personal.csv";
shell_exec("touch ". TMPLOG);
chmod(TMPLOG, 0777);
logWrite('column,mac,path_before,path_after,');

function UpdateDevPath($column, $path, $mac, $db)
{
    $sql = "update DevicesShadow set $column = :path where MAC = :mac";
    $sth = $db->prepare($sql);
    $sth->bindParam(':path', $path, PDO::PARAM_STR);
    $sth->bindParam(':mac', $mac, PDO::PARAM_STR);
    $sth->execute();
}

    $db = getDB();

    $sth = $db->prepare("select MAC,Node,PrivatekeyMD5,RfidMD5,ConfigMD5,ContactMD5,FaceMD5 from PersonalDevices limit 1000");
    $sth->execute();
    $list = $sth->fetchAll(PDO::FETCH_ASSOC);
    foreach($list as $value){
        $download_path = GetDevPath($value);
        $sth = $db->prepare("select PrivatekeyPath,RfidPath,ConfigPath,ContactPath,FacePath from DevicesShadow where MAC = :mac");
        $sth->bindParam(':mac', $value['MAC'], PDO::PARAM_STR);
        $sth->execute();
        $path = $sth->fetch(PDO::FETCH_ASSOC);      
        if(!$path){
            $sth = $db->prepare("insert into DevicesShadow(MAC) value(:mac)");
            $sth->bindParam(':mac', $value['MAC'], PDO::PARAM_STR);
            $sth->execute();
        }
        if(strlen($value['PrivatekeyMD5']) > 0){
            if(!$path || !strstr($path['PrivatekeyPath'], 'group2')){  
                $tmp_download = glob($download_path['PrivatekeyPath']);                 
                if(count($tmp_download) == 1){
                    //echo $tmp_download[0]."\n";
                    $fdfs_path = storage_file_to_fdfs($tmp_download[0]);
                    if($fdfs_path){
                        UpdateDevPath('PrivatekeyPath', $fdfs_path, $value['MAC'], $db);
                        logWrite('PrivatekeyPath,'.$value['MAC'].','.$tmp_download[0].','.$fdfs_path.',');
                    }
                }
            }
        }
        if(strlen($value['RfidMD5']) > 0){
            if(!$path || !strstr($path['RfidPath'], 'group2')){
                $tmp_download = glob($download_path['RfidPath']);  
                if(count($tmp_download) == 1){
                    //echo $tmp_download[0]."\n";
                    $fdfs_path = storage_file_to_fdfs($tmp_download[0]);
                    if($fdfs_path){
                        UpdateDevPath('RfidPath', $fdfs_path, $value['MAC'], $db);
                        logWrite('RfidPath,'.$value['MAC'].','.$tmp_download[0].','.$fdfs_path.',');
                    }
                }
            }
        }
        if(strlen($value['ConfigMD5']) > 0){
            if(!$path || !strstr($path['ConfigPath'], 'group2')){
                $tmp_download = glob($download_path['ConfigPath']);                 
                if(count($tmp_download) == 1){
                    //echo $tmp_download[0]."\n";
                    $fdfs_path = storage_file_to_fdfs($tmp_download[0]);
                    if($fdfs_path){
                        UpdateDevPath('ConfigPath', $fdfs_path, $value['MAC'], $db);
                        logWrite('ConfigPath,'.$value['MAC'].','.$tmp_download[0].','.$fdfs_path.',');
                    }
                }
            }
        }
        if(strlen($value['ContactMD5']) > 0){
            if(!$path || !strstr($path['ContactPath'], 'group2')){
                $tmp_download = glob($download_path['ContactPath']);                 
                if(count($tmp_download) == 1){
                    //echo $tmp_download[0]."\n";
                    $fdfs_path = storage_file_to_fdfs($tmp_download[0]);
                    if($fdfs_path){
                        UpdateDevPath('ContactPath', $fdfs_path, $value['MAC'], $db);
                        logWrite('ContactPath,'.$value['MAC'].','.$tmp_download[0].','.$fdfs_path.',');
                    }
                }
            }
        }
        if(strlen($value['FaceMD5']) > 0){
            if(!$path || !strstr($path['FacePath'], 'group2')){
                $tmp_download = glob($download_path['FacePath']);                 
                if(count($tmp_download) == 1){
                    //echo $tmp_download[0]."\n";
                    $fdfs_path = storage_file_to_fdfs($tmp_download[0]);
                    if($fdfs_path){
                        UpdateDevPath('FacePath', $fdfs_path, $value['MAC'], $db);
                        logWrite('FacePath,'.$value['MAC'].','.$tmp_download[0].','.$fdfs_path.',');
                    }
                }
            }
        }

    }





   
