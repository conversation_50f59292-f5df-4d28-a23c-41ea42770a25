<?php

function getLogDB()
{
    $dbhost = "*************";
	$dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "LOG";
	$dbport = 3308;
	
    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

const DOORLOG_STORE_PATH = "/pic_data/fdfs_storage_csstorage/data/";

function getDoorLogPic($mac, $captureType, $response)
{	
	$db = getLogDB();
	$sth = $db->prepare("select PicUrl from PersonalCapture_5 where MAC =:mac and CaptureType =:captureAction and Response =:response");
	$sth->bindParam(':mac', $mac, PDO::PARAM_STR);
    $sth->bindParam(':captureAction', $captureType, PDO::PARAM_STR);
	$sth->bindParam(':response', $response, PDO::PARAM_INT);
    $sth->execute();
	$data = $sth->fetchAll(PDO::FETCH_ASSOC);
	
	$realFaceDoorLogUrl = array();
	foreach ($data as $key => $picUrl) {
		if ($picUrl['PicUrl']) {
			$newFaceUrl = preg_replace('/\/group\d\/\w+\//', DOORLOG_STORE_PATH, $picUrl['PicUrl']);
			echo $newFaceUrl;
			echo "\n";
			array_push($realFaceDoorLogUrl, $newFaceUrl);
		}
    }
	
	// 创建一个存放图片的文件夹
	$dir = "";
	if ($response == 0) {
		$dir = "faceDoorlog_success";
	} else {
		$dir = "faceDoorlog_fail";
	}

    if (!file_exists($dir)) {
        mkdir($dir);
        echo "Directory created successfully!";
    } else {
        echo "Directory already exists!";
    }
	
	//切换目录
    chdir($dir);
	foreach ($realFaceDoorLogUrl as $faceDoorLogUrl) {
		//复制图片到当前路径
		$source = $faceDoorLogUrl;
        $dest = basename($faceDoorLogUrl);
        copy($source, $dest);
	}

	//切换目录
    chdir('../');

    $command = "tar -czvf " . $dir . ".tar.gz " . $dir;
    exec($command);
}


//使用说明,先查一下log在那个表 ： php erport_device_face_doorlog.php $mac $captureType
$mac = $argv[1];
$captureType = $argv[2];
getDoorLogPic($mac, $captureType, 0); // 成功
getDoorLogPic($mac, $captureType, 1); // 失败