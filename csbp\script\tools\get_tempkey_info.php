<?php
/*
20201015
查找夏令时会影响的tempkey的客户信息。
*/
function getDB()
{
	$dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
	$dbport = 3306;
    
    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}


const STATIS_FILE1 = "/root/tmpkey_info.csv";
shell_exec("rm ". STATIS_FILE1);
shell_exec("touch ". STATIS_FILE1);
chmod(STATIS_FILE1, 0777);

function TRACE1($content)
{
    $tmpNow = time();
    #$Now = date('Y-m-d H:i:s', $tmpNow);    
	@file_put_contents(STATIS_FILE1, $content, FILE_APPEND);
	@file_put_contents(STATIS_FILE1, "\n", FILE_APPEND);
	echo "$content\n";
}

$db = getDB();

$sth = $db->prepare("select TmpKey,A.Location,A.ManageGroup,MngAccountID,UnitID,Node,SchedulerType,BeginTime,EndTime,StartTime,StopTime,P.CreateTime 
From PersonalAppTmpKey P  left join Account A on A.ID=P.MngAccountID 
where SchedulerType in(1,2) and UnitID !=0 order by MngAccountID ");
$sth->execute();
$tmpkeylist = $sth->fetchALL(PDO::FETCH_ASSOC);

TRACE1("Communit For Personal");
TRACE1("Dis,Ins,Community,Account,Start,End");
foreach ($tmpkeylist as $row => $val)
{	
	$communitid=$val['MngAccountID'];
	$ins_id = $val['ManageGroup'];
	$communit_name = $val['Location'];
	$start = $val['StartTime'];
	$end = $val['StopTime'];
	$key = $val['TmpKey'];
	$node = $val['Node'];

	$sth = $db->prepare("select AA.Account as dis ,A.Account as ins From Account A left join Account AA on A.ParentID=AA.ID where A.ID=:ID;");
	$sth->bindParam(':ID', $ins_id, PDO::PARAM_STR);
	$sth->execute();
	$mng_info = $sth->fetch(PDO::FETCH_ASSOC);
	TRACE1($mng_info['dis'].",".$mng_info['ins'].",$communit_name,$node,$start,$end");
}

$sth = $db->prepare("select A.Location,A.ManageGroup,MngAccountID,SchedulerType,BeginTime,EndTime,StartTime,
StopTime,P.CreateTime  From PubAppTmpKey P  left join Account A on A.ID=P.MngAccountID  
 where SchedulerType in(1,2) order by MngAccountID");
$sth->execute();
$tmpkeylist = $sth->fetchALL(PDO::FETCH_ASSOC);

TRACE1("");
TRACE1("");
TRACE1("Communit For Public Devices");
TRACE1("Dis,Ins,Community,Start,End");
foreach ($tmpkeylist as $row => $val)
{	
	$communitid=$val['MngAccountID'];
	$ins_id = $val['ManageGroup'];
	$communit_name = $val['Location'];
	$start = $val['StartTime'];
	$end = $val['StopTime'];

	$sth = $db->prepare("select AA.Account as dis ,A.Account as ins From Account A left join Account AA on A.ParentID=AA.ID where A.ID=:ID;");
	$sth->bindParam(':ID', $ins_id, PDO::PARAM_STR);
	$sth->execute();
	$mng_info = $sth->fetch(PDO::FETCH_ASSOC);
	TRACE1($mng_info['dis'].",".$mng_info['ins'].",$communit_name,$start,$end");
}

$sth = $db->prepare("select A.Email,A.Name,MngAccountID,Node,SchedulerType,BeginTime,EndTime,StartTime,
StopTime,P.CreateTime  From PersonalAppTmpKey P  left join PersonalAccount A on A.Account=P.Node   
where SchedulerType in(1,2) and P.UnitID=0 order by MngAccountID limit 100;");
$sth->execute();
$tmpkeylist = $sth->fetchALL(PDO::FETCH_ASSOC);

TRACE1("");
TRACE1("");
TRACE1("Single Personal");
TRACE1("Dis,Ins,Per_Name,Per_Email,Start,End");
foreach ($tmpkeylist as $row => $val)
{	
	$ins_id=$val['MngAccountID'];
	$start = $val['StartTime'];
	$end = $val['StopTime'];
	$email = $val['Email'];
	$name = $val['Name'];

	$sth = $db->prepare("select AA.Account as dis ,A.Account as ins From Account A left join Account AA on A.ParentID=AA.ID where A.ID=:ID;");
	$sth->bindParam(':ID', $ins_id, PDO::PARAM_STR);
	$sth->execute();
	$mng_info = $sth->fetch(PDO::FETCH_ASSOC);
	TRACE1($mng_info['dis'].",".$mng_info['ins'].",$name,$email,$start,$end");
}






?>