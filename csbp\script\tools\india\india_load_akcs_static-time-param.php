<?php
date_default_timezone_set('PRC');
const STATIS_FILE = "/home/<USER>";
shell_exec("touch ". STATIS_FILE);
const STATIS_TOTAL_FILE = "/home/<USER>";
shell_exec("touch ". STATIS_TOTAL_FILE);
$time_one_week = TRUE;
if ($argc == 3) {
    $timestart=$argv[1];
    $timeend= $argv[2];
} else {
    $timestart="2015-01-01 00:00:00";
    $timeend= "2025-01-01 00:00:00";
    $now = $argv[1];//统计当天的00:00:00，而不是统计执行的时间点
    $time_one_week = FALSE;
}

chmod(STATIS_FILE, 0777);
if (file_exists(STATIS_FILE)) {
    shell_exec("echo > ". STATIS_FILE);
} 
function STATIS_WRITE($content)
{
	file_put_contents(STATIS_FILE, $content, FILE_APPEND);
	file_put_contents(STATIS_FILE, "\n", FILE_APPEND);
}

function getDB()
{
    $dbhost = "localhost";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";

    $mysql_conn_string = "mysql:host=$dbhost;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

$db = getDB();


#统计各个个人终端管理员下的数据
//先统计下区域管理员
$static_str = 'SingleTenant' . ',' .'Distributor'. ',' . 'Installer'. ',' . 'Public Device'.',' . 'Public Bind Time'. ',' .'Family Master'.',' . 'Family Members'.',' . 'Device'.',' . 'version'.',' . 'Device Bind Time'.',' . 'dev online status' . ',' . 'last connect time' . ',';
STATIS_WRITE($static_str);

$dis_name_tmp = null;
$mng_name_tmp = null;
$node_tmp = null;
$sth_dis = $db->prepare("select ID,Account,Email from Account where Grade = 11");
$sth_dis->execute();
$dis_list = $sth_dis->fetchALL(PDO::FETCH_ASSOC);
foreach ($dis_list as $row => $dis) //统计区域下面的个人终端管理员
{
    $dis_id=$dis['ID'];
	$dis_name=$dis['Account'].'/'.$dis['Email'];//加上邮箱chenzhx
    $dis_email=$dis['Email'];
    
    //个人终端管理员
    $sth = $db->prepare("select ID,Account from Account where ParentID = :pid and Grade = 22");
    $sth->bindParam(':pid', $dis_id, PDO::PARAM_INT);
    $sth->execute();
    $mng_list = $sth->fetchALL(PDO::FETCH_ASSOC);
    $online_str = null;
    foreach ($mng_list as $row => $mng) //统计个人终端管理员下面的主账号
    {	
        $mng_id=$mng['ID'];
        $mng_name=$mng['Account'];
        
        #查询公共设备
        $sth_p_dev = $db->prepare("select P.MAC,P.CreateTime,P.Firmware,P.LastConnection,P.Status from PersonalDevices P right join PersonalAccount A on P.Node=A.Account and A.ParentID=:MngID where P.flag=1  and (P.CreateTime between '".$timestart."' and '".$timeend."');");
        $sth_p_dev->bindParam(':MngID', $mng_id, PDO::PARAM_INT);
        $sth_p_dev->execute();
        $p_dev_list = $sth_p_dev->fetchALL(PDO::FETCH_ASSOC);
        foreach ($p_dev_list as $row => $pub_dev)
        {
            $pub_dev_mac = $pub_dev['MAC'];
            $pub_dev_time = $pub_dev['CreateTime'];
            $pub_dev_ver = $pub_dev['Firmware'];
            $pub_dev_lastconn = $pub_dev['LastConnection'];
            $pub_dev_status = $pub_dev['Status'];
            $static_str = null;
            //var_dump($mng_name_tmp);
            //var_dump($mng_name);
            if($mng_name_tmp == $mng_name) //只要个人终端管理员一样，那么证明区域管理员也是相同的，所以$mng_name在这一行就不需要显示
            {
                $static_str = ',' . ''. ',' . '' . ',' . $pub_dev_mac . ',' . $pub_dev_time . ',' . ','. ',' . ','. $pub_dev_ver . ',' . ','. ',' .$pub_dev_status .','.$pub_dev_lastconn . ',';
            }
            else if (($mng_name_tmp != $mng_name) && ($dis_name_tmp == $dis_name))
            {
                $static_str = ',' . ''. ',' . $mng_name . ',' . $pub_dev_mac . ',' . $pub_dev_time . ',' . ','. ',' . ','. $pub_dev_ver . ',' . ','. ',' .$pub_dev_status .','.$pub_dev_lastconn . ',';
            }
            else
            {
                $static_str = ',' . $dis_name. ',' . $mng_name . ',' . $pub_dev_mac . ',' . $pub_dev_time . ',' . ','. ',' . ','. $pub_dev_ver . ',' . ','. ',' .$pub_dev_status .','.$pub_dev_lastconn . ',';
            }
            STATIS_WRITE($static_str);
            $mng_name_tmp = $mng_name; //缓存上一次的个人终端管理员
            $dis_name_tmp = $dis_name; //缓存上一次的区域管理员
        }
        
        //查询主账号
        $sth_master = $db->prepare("select ID,Account,Name from PersonalAccount where ParentID=:ParentID and Role = 10");
        $sth_master->bindParam(':ParentID', $mng_id, PDO::PARAM_INT);
        $sth_master->execute();
        $master_list = $sth_master->fetchALL(PDO::FETCH_ASSOC);
        foreach ($master_list as $row => $master) //统计主账号
        {	
            $node_id=$master['ID'];
            $node=$master['Account'];
            $node_name=$master['Name'];
            
            //查询联动系统的用户数
            $sth_fellow = $db->prepare("select count(1) as fellow_num from PersonalAccount where ParentID=:ParentID and role=11");
            $sth_fellow->bindParam(':ParentID', $node_id, PDO::PARAM_INT);
            $sth_fellow->execute();
            $node_fellow_num = $sth_fellow->fetch(PDO::FETCH_ASSOC);
            $fellow_num = $node_fellow_num['fellow_num'] + 1;
            
            //查询联动系统的设备列表
            $sth_mac = $db->prepare("select MAC,Firmware,CreateTime,Status,LastConnection from PersonalDevices where Node=:Node and (CreateTime between '".$timestart."' and '".$timeend."');");
            $sth_mac->bindParam(':Node', $node, PDO::PARAM_STR);
            $sth_mac->execute();
            $node_mac_list = $sth_mac->fetchALL(PDO::FETCH_ASSOC);
            foreach ($node_mac_list as $row => $dev) //统计联动系统下的设备列表
            {
                $dev_mac = $dev['MAC'];
                $dev_ver = $dev['Firmware'];
                $dev_time = $dev['CreateTime'];
                $dev_status = $dev['Status'];
                $dev_last_conn_time = $dev['LastConnection'];
                if($node_tmp == $node) 
                {
                    $static_str = ',' . ''. ',' . '' . ',' .',' . ',' . ''. ',' . '' . ',' . $dev_mac . ',' . $dev_ver . ',' . $dev_time . ',' . $dev_status. ',' . $dev_last_conn_time . ',';
                }
                else if(($node_tmp != $node) && ($mng_name_tmp == $mng_name))
                {
                    $static_str = ',' . ''. ',' . '' . ',' .',' . ',' . $node. ',' . $fellow_num . ',' . $dev_mac . ',' . $dev_ver . ',' . $dev_time . ',' . $dev_status. ',' . $dev_last_conn_time . ',';
                }
                else if(($node_tmp != $node) && ($mng_name_tmp != $mng_name) &&($dis_name_tmp == $dis_name))
                {
                    $static_str = ',' . ''. ',' . $mng_name . ',' .',' . ',' . $node. ',' . $fellow_num . ',' . $dev_mac . ',' . $dev_ver . ',' . $dev_time . ',' . $dev_status. ',' . $dev_last_conn_time . ',';
                }
                else
                {
                    $static_str = ',' . $dis_name. ',' . $mng_name . ',' .',' . ',' . $node. ',' . $fellow_num . ',' . $dev_mac . ',' . $dev_ver . ',' . $dev_time .',' . $dev_status. ',' . $dev_last_conn_time . ',';
                }
                
                STATIS_WRITE($static_str);
                $node_tmp = $node;
                $mng_name_tmp = $mng_name; //缓存上一次的个人终端管理员
                $dis_name_tmp = $dis_name; //缓存上一次的区域管理员
            }
        }
        //$mng_name_tmp = $mng_name; //缓存上一次的个人终端管理员
    } 
    //$dis_name_tmp = $dis_name; //缓存上一次的区域管理员
}

?>
