<?php
date_default_timezone_set('PRC');
const STATIS_FILE = "/home/<USER>";
shell_exec("touch ". STATIS_FILE);

const STATIS_FILE_TOTAL = "/home/<USER>";
shell_exec("touch ". STATIS_FILE_TOTAL);

chmod(STATIS_FILE, 0777);
if (file_exists(STATIS_FILE)) {
    shell_exec("echo > ". STATIS_FILE);
} 
function STATIS_WRITE($content)
{
	file_put_contents(STATIS_FILE, $content, FILE_APPEND);
	file_put_contents(STATIS_FILE, "\n", FILE_APPEND);
}

chmod(STATIS_FILE_TOTAL, 0777);
if (file_exists(STATIS_FILE_TOTAL)) {
    shell_exec("echo > ". STATIS_FILE_TOTAL);
} 
function STATIS_WRITE_TOTAL($content)
{
	file_put_contents(STATIS_FILE_TOTAL, $content, FILE_APPEND);
	file_put_contents(STATIS_FILE_TOTAL, "\n", FILE_APPEND);
}

function getDB()
{
    $dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";

    $mysql_conn_string = "mysql:host=$dbhost;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

$db = getDB();

$static_str = 'Distributor'.','.'Installer'.','.'Installer Email'.','.'Community Name'.','.'Reisident Number'.','.'Indoor Monitor Number'.','.'Land Line'.',' .'charge' .',';
STATIS_WRITE($static_str);

$sth_dis = $db->prepare("select ID,Account from Account where Grade = 11");
$sth_dis->execute();
$dis_list = $sth_dis->fetchALL(PDO::FETCH_ASSOC);
foreach ($dis_list as $row => $dis)
{
    $dis_id=$dis['ID'];
	$dis_name=$dis['Account'];
    //下面开始统计社区管理员 
    $sth_comm_mng = $db->prepare("select A1.ID,A1.Location, A2.Account, A2.Email from Account A1 left join Account A2 on A2.ID=A1.ManageGroup where A1.ParentID = :pid and A1.Grade = 21");
    $sth_comm_mng->bindParam(':pid', $dis_id, PDO::PARAM_INT);
    $sth_comm_mng->execute();
    $comm_mng_list = $sth_comm_mng->fetchAll(PDO::FETCH_ASSOC);
    foreach ($comm_mng_list as $row => $comm_mng) //遍历社区管理员
    {
        $indoor_num = 0;
        $reisident_num = 0;
        $landline = 0;
        $charge = 0;
        $comm_mng_id=$comm_mng['ID'];//社区管理员的id
        $comm_mng_name=$comm_mng['Account'];//installer
        $comm_mng_email=$comm_mng['Email'];//installer的email
        $comm_location=$comm_mng['Location'];//社区的location
        if(($comm_mng_name != NUll) && ($comm_location != NULL))//之前的数据有bug,有些社区是没有对应的installer的,统计的时候，过滤掉即可。
        {
			$sth_comm_master = $db->prepare("select count(1) as master_num from PersonalAccount where ParentID=:ParentID and Role = 20");
            $sth_comm_master->bindParam(':ParentID', $comm_mng_id, PDO::PARAM_INT);
            $sth_comm_master->execute();
            $comm_master_list = $sth_comm_master->fetchALL(PDO::FETCH_ASSOC);
			$num_result = $sth_comm_master->fetch(PDO::FETCH_ASSOC);
			$comm_node_num = $num_result['master_num'];//社区家庭的数量
            $reisident_num = $comm_node_num;//会造成同个数据多次赋值，当时这样比较方便
			
            //遍历社区的主账号,确定室内机的数量
            $sth_comm_master = $db->prepare("select ID,Account,Name,PhoneStatus from PersonalAccount where ParentID=:ParentID and Role = 20");
            $sth_comm_master->bindParam(':ParentID', $comm_mng_id, PDO::PARAM_INT);
            $sth_comm_master->execute();
            $comm_master_list = $sth_comm_master->fetchALL(PDO::FETCH_ASSOC);
            foreach ($comm_master_list as $row => $master) //统计主账号
            {
                $comm_node=$master['Account'];
                $comm_node_phone_status=$master['PhoneStatus'];
                //落地只要判断其中有一个开启，就算整个社区开启
                if($comm_node_phone_status == 1)
                {
                    $landline = 1;
                    $charge = 1;
                }
                //查询室内机的数量
                $sth_comm_dev = $db->prepare("select count(1) as indoor_num from Devices where Node=:Node and Type = 2");
                $sth_comm_dev->bindParam(':Node', $comm_node, PDO::PARAM_STR);
                $sth_comm_dev->execute();
                $node_mac_list = $sth_comm_dev->fetchALL(PDO::FETCH_ASSOC);
                foreach ($node_mac_list as $row => $dev) //统计联动系统下的室内机的数量
                {
                    $node_indoor_num = $dev['indoor_num'];
                    $indoor_num += $node_indoor_num;
                }
            }
            //收月租有两种情况:一种是 community开启落地的（只要有一户开了就算有） 另一种是0户以上且没有室内机的 
            if(($reisident_num >=20) && ($indoor_num ==0))
            {
                $charge = 1;
            }
             //开始打印
            $static_str = null;
            $static_str = $dis_name.','. $comm_mng_name .','.$comm_mng_email .','. $comm_location.','. $reisident_num.','. $indoor_num.','.$landline.','.$charge.',';
            STATIS_WRITE($static_str);
        }
    }      
}

//统计各种类型的社区
$static_str = null;
$static_str = 'More than 20'.','.'Less than 20'.','.'With Indoor Monitor'.','.'Without Indoor Monitor'.','.'Total number of Community'.','.'Number of No Reisident Community'.',';
STATIS_WRITE_TOTAL($static_str);
//各项统计数据
$morethan20_num = 0;
$lessthan20_num = 0;
$with_indoor_num = 0;
$without_indoor_num = 0;
$total_comm_num = 0;
$no_resident_comm_num = 0;
     
$sth_dis = $db->prepare("select ID,Account from Account where Grade = 11");
$sth_dis->execute();
$dis_list = $sth_dis->fetchALL(PDO::FETCH_ASSOC);
foreach ($dis_list as $row => $dis)
{
    $dis_id=$dis['ID'];
	$dis_name=$dis['Account'];
    //下面开始dis统计社区管理员 
    $sth_comm_mng = $db->prepare("select A1.ID,A1.Location, A2.Account from Account A1 left join Account A2 on A2.ID=A1.ManageGroup where A1.ParentID = :pid and A1.Grade = 21");
    $sth_comm_mng->bindParam(':pid', $dis_id, PDO::PARAM_INT);
    $sth_comm_mng->execute();
    $comm_mng_list = $sth_comm_mng->fetchAll(PDO::FETCH_ASSOC);
    foreach ($comm_mng_list as $row => $comm_mng) //遍历社区管理员
    {
        $without_indoor_flag = 0;//该社区是有有室内机的标志\
        $resident_num_flag = 0;//0=0;1<20;2>=20户;
        $comm_mng_id=$comm_mng['ID'];//社区管理员的id
        $comm_mng_name=$comm_mng['Account'];//installer
        $comm_location=$comm_mng['Location'];//社区的location
        if(($comm_mng_name != NULL) && ($comm_location != NULL))
        {
           $total_comm_num++;
        }
        //遍历社区的主账号
        $sth_comm_master = $db->prepare("select Account,count(1) as master_num from PersonalAccount where ParentID=:ParentID and Role = 20");
        $sth_comm_master->bindParam(':ParentID', $comm_mng_id, PDO::PARAM_INT);
        $sth_comm_master->execute();
        $comm_master_list = $sth_comm_master->fetchALL(PDO::FETCH_ASSOC);
        foreach ($comm_master_list as $row => $master) //统计主账号
        {
            $comm_node=$master['Account'];
            $comm_node_num=$master['master_num'];//社区家庭的数量
            if($comm_node_num >= 20)
            {
                $resident_num_flag = 2;
            }
            else if(($comm_node_num < 20) && ($comm_node_num > 0))
            {
                $resident_num_flag = 1;
            }
            else
            {
                $resident_num_flag = 0;//空房间也会到这里
                //echo 'null room';
            }
            //查询室内机的数量
            $sth_comm_dev = $db->prepare("select count(1) as indoor_num from Devices where Node=:Node and Type = 2");
            
            $sth_comm_dev->bindParam(':Node', $comm_node, PDO::PARAM_STR);
            $sth_comm_dev->execute();
            $node_indoor = $sth_comm_dev->fetch(PDO::FETCH_ASSOC);
            $node_indoor_num = $node_indoor['indoor_num'];
            if($node_indoor_num == 0)//只要有一户没有室内机，就认为没有室内机
            {
                $without_indoor_flag = 1;
            }
        }
        if($resident_num_flag == 2)
        {
            $morethan20_num++;
        }
        else if($resident_num_flag == 1)
        {
            $lessthan20_num++;
        }
        
        if($without_indoor_flag == 1)
        {
           $without_indoor_num++; 
        }
        else
        {
            $with_indoor_num++;//with的数量是精准的
        }
    }
}
$no_resident_comm_num = $total_comm_num - $morethan20_num - $lessthan20_num;
$without_indoor_num = $morethan20_num + $lessthan20_num - $with_indoor_num;
 //开始打印
$static_str = null;
$static_str = $morethan20_num.','. $lessthan20_num .','. $with_indoor_num .','. $without_indoor_num.','. $total_comm_num.','. $no_resident_comm_num.',';
        
STATIS_WRITE_TOTAL($static_str);
?>
