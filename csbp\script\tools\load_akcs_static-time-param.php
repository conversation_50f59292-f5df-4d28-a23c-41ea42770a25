<?php
date_default_timezone_set('PRC');
const STATIS_FILE = "/home/<USER>";
shell_exec("touch ". STATIS_FILE);
const STATIS_TOTAL_FILE = "/home/<USER>";
shell_exec("touch ". STATIS_TOTAL_FILE);
$time_one_week = TRUE;
if ($argc == 3) {
    $timestart=$argv[1];
    $timeend= $argv[2];
} else {
    $timestart="2015-01-01 00:00:00";
    $timeend= "2025-01-01 00:00:00";
    $now = $argv[1];//统计当天的00:00:00，而不是统计执行的时间点
    $time_one_week = FALSE;
}

chmod(STATIS_FILE, 0777);
if (file_exists(STATIS_FILE)) {
    shell_exec("echo > ". STATIS_FILE);
} 
function STATIS_WRITE($content)
{
	file_put_contents(STATIS_FILE, $content, FILE_APPEND);
	file_put_contents(STATIS_FILE, "\n", FILE_APPEND);
}

chmod(STATIS_TOTAL_FILE, 0777);
if (file_exists(STATIS_TOTAL_FILE) && ($time_one_week == TRUE)) {
    shell_exec("echo > ". STATIS_TOTAL_FILE);
} 
function STATIS_TOTAL_WRITE($content)
{
	file_put_contents(STATIS_TOTAL_FILE, $content, FILE_APPEND);
	file_put_contents(STATIS_TOTAL_FILE, "\n", FILE_APPEND);
}

function getDB()
{
    $dbhost = "localhost";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";

    $mysql_conn_string = "mysql:host=$dbhost;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

$db = getDB();


#统计各个个人终端管理员下的数据
//先统计下区域管理员
$static_str = 'SingleTenant' . ',' .'Distributor'. ',' . 'Installer'. ',' . 'Public Device'.',' . 'Public Bind Time'. ',' .'Family Master'.',' . 'Family Members'.',' . 'Device'.',' . 'version'.',' . 'Device Bind Time'.',' .  'Land Line' . ',' . 'dev online status' . ',' . 'last connect time' . ',';
STATIS_WRITE($static_str);

$dis_name_tmp = null;
$mng_name_tmp = null;
$node_tmp = null;
$sth_dis = $db->prepare("select ID,Account,Email from Account where Grade = 11");
$sth_dis->execute();
$dis_list = $sth_dis->fetchALL(PDO::FETCH_ASSOC);
foreach ($dis_list as $row => $dis) //统计区域下面的个人终端管理员
{
    $dis_id=$dis['ID'];
	$dis_name=$dis['Account'].'/'.$dis['Email'];//加上邮箱chenzhx
    $dis_email=$dis['Email'];
    
    //个人终端管理员
    $sth = $db->prepare("select ID,Account from Account where ParentID = :pid and Grade = 22");
    $sth->bindParam(':pid', $dis_id, PDO::PARAM_INT);
    $sth->execute();
    $mng_list = $sth->fetchALL(PDO::FETCH_ASSOC);
    $online_str = null;
    foreach ($mng_list as $row => $mng) //统计个人终端管理员下面的主账号
    {	
        $mng_id=$mng['ID'];
        $mng_name=$mng['Account'];
        
        #查询公共设备
        $sth_p_dev = $db->prepare("select P.MAC,P.CreateTime,P.Firmware,P.LastConnection,P.Status from PersonalDevices P right join PersonalAccount A on P.Node=A.Account and A.ParentID=:MngID where P.flag=1  and (P.CreateTime between '".$timestart."' and '".$timeend."');");
        $sth_p_dev->bindParam(':MngID', $mng_id, PDO::PARAM_INT);
        $sth_p_dev->execute();
        $p_dev_list = $sth_p_dev->fetchALL(PDO::FETCH_ASSOC);
        foreach ($p_dev_list as $row => $pub_dev)
        {
            $pub_dev_mac = $pub_dev['MAC'];
            $pub_dev_time = $pub_dev['CreateTime'];
            $pub_dev_ver = $pub_dev['Firmware'];
            $pub_dev_lastconn = $pub_dev['LastConnection'];
            $pub_dev_status = $pub_dev['Status'];
            $static_str = null;
            //var_dump($mng_name_tmp);
            //var_dump($mng_name);
            if($mng_name_tmp == $mng_name) //只要个人终端管理员一样，那么证明区域管理员也是相同的，所以$mng_name在这一行就不需要显示
            {
                $static_str = ',' . ''. ',' . '' . ',' . $pub_dev_mac . ',' . $pub_dev_time . ',' . ','. ',' . ','. $pub_dev_ver . ',' . ','. ',' .$pub_dev_status .','.$pub_dev_lastconn . ',';
            }
            else if (($mng_name_tmp != $mng_name) && ($dis_name_tmp == $dis_name))
            {
                $static_str = ',' . ''. ',' . $mng_name . ',' . $pub_dev_mac . ',' . $pub_dev_time . ',' . ','. ',' . ','. $pub_dev_ver . ',' . ','. ',' .$pub_dev_status .','.$pub_dev_lastconn . ',';
            }
            else
            {
                $static_str = ',' . $dis_name. ',' . $mng_name . ',' . $pub_dev_mac . ',' . $pub_dev_time . ',' . ','. ',' . ','. $pub_dev_ver . ',' . ','. ',' .$pub_dev_status .','.$pub_dev_lastconn . ',';
            }
            STATIS_WRITE($static_str);
            $mng_name_tmp = $mng_name; //缓存上一次的个人终端管理员
            $dis_name_tmp = $dis_name; //缓存上一次的区域管理员
        }
        
        //查询主账号
        $sth_master = $db->prepare("select ID,Account,Name,PhoneStatus from PersonalAccount where ParentID=:ParentID and Role = 10");
        $sth_master->bindParam(':ParentID', $mng_id, PDO::PARAM_INT);
        $sth_master->execute();
        $master_list = $sth_master->fetchALL(PDO::FETCH_ASSOC);
        foreach ($master_list as $row => $master) //统计主账号
        {	
            $node_id=$master['ID'];
            $node=$master['Account'];
            $node_name=$master['Name'];
            $node_phone_status=$master['PhoneStatus'];
            
            //查询联动系统的用户数
            $sth_fellow = $db->prepare("select count(1) as fellow_num from PersonalAccount where ParentID=:ParentID and role=11");
            $sth_fellow->bindParam(':ParentID', $node_id, PDO::PARAM_INT);
            $sth_fellow->execute();
            $node_fellow_num = $sth_fellow->fetch(PDO::FETCH_ASSOC);
            $fellow_num = $node_fellow_num['fellow_num'] + 1;
            
            //查询联动系统的设备列表
            $sth_mac = $db->prepare("select MAC,Firmware,CreateTime,Status,LastConnection from PersonalDevices where Node=:Node and (CreateTime between '".$timestart."' and '".$timeend."');");
            $sth_mac->bindParam(':Node', $node, PDO::PARAM_STR);
            $sth_mac->execute();
            $node_mac_list = $sth_mac->fetchALL(PDO::FETCH_ASSOC);
            foreach ($node_mac_list as $row => $dev) //统计联动系统下的设备列表
            {
                $dev_mac = $dev['MAC'];
                $dev_ver = $dev['Firmware'];
                $dev_time = $dev['CreateTime'];
                $dev_status = $dev['Status'];
                $dev_last_conn_time = $dev['LastConnection'];
                if($node_tmp == $node) 
                {
                    $static_str = ',' . ''. ',' . '' . ',' .',' . ',' . ''. ',' . '' . ',' . $dev_mac . ',' . $dev_ver . ',' . $dev_time . ',' . $node_phone_status . ',' . $dev_status. ',' . $dev_last_conn_time . ',';
                }
                else if(($node_tmp != $node) && ($mng_name_tmp == $mng_name))
                {
                    $static_str = ',' . ''. ',' . '' . ',' .',' . ',' . $node. ',' . $fellow_num . ',' . $dev_mac . ',' . $dev_ver . ',' . $dev_time . ',' . $node_phone_status . ',' . $dev_status. ',' . $dev_last_conn_time . ',';
                }
                else if(($node_tmp != $node) && ($mng_name_tmp != $mng_name) &&($dis_name_tmp == $dis_name))
                {
                    $static_str = ',' . ''. ',' . $mng_name . ',' .',' . ',' . $node. ',' . $fellow_num . ',' . $dev_mac . ',' . $dev_ver . ',' . $dev_time . ',' . $node_phone_status . ',' . $dev_status. ',' . $dev_last_conn_time . ',';
                }
                else
                {
                    $static_str = ',' . $dis_name. ',' . $mng_name . ',' .',' . ',' . $node. ',' . $fellow_num . ',' . $dev_mac . ',' . $dev_ver . ',' . $dev_time . ',' . $node_phone_status . ',' . $dev_status. ',' . $dev_last_conn_time . ',';
                }
                
                STATIS_WRITE($static_str);
                $node_tmp = $node;
                $mng_name_tmp = $mng_name; //缓存上一次的个人终端管理员
                $dis_name_tmp = $dis_name; //缓存上一次的区域管理员
            }
        }
        //$mng_name_tmp = $mng_name; //缓存上一次的个人终端管理员
    } 
    //$dis_name_tmp = $dis_name; //缓存上一次的区域管理员
}

$static_str = 'Community' . ',' .'Distributor'. ',' . 'Installer'. ',' . 'Community Name'. ',' . 'Community Unit'.',' . 'Public Device'. ',' .'Public Bind Time'.','.'Residents'.','.'Family Members'.',' . 'Device'.',' . 'version'.',' . 'Device Bind Time'.',' .  'Land Line'. ','. 'dev online status' . ',' . 'last connect time' . ',';
STATIS_WRITE($static_str);

$dis_name_tmp = null;
$comm_mng_name_tmp = null; //installer的临时缓存
$comm_location_tmp = null; //社区location的临时缓存
$unit_name_tmp = null;
$node_tmp = null;
$sth_dis = $db->prepare("select ID,Account from Account where Grade = 11");
$sth_dis->execute();
$dis_list = $sth_dis->fetchALL(PDO::FETCH_ASSOC);
foreach ($dis_list as $row => $dis)
{
    $dis_id=$dis['ID'];
	$dis_name=$dis['Account'];
    //下面开始统计社区管理员 
    $sth_comm_mng = $db->prepare("select A1.ID,A1.Location, A2.Account from Account A1 left join Account A2 on A2.ID=A1.ManageGroup where A1.ParentID = :pid and A1.Grade = 21");
    $sth_comm_mng->bindParam(':pid', $dis_id, PDO::PARAM_INT);
    $sth_comm_mng->execute();
    $comm_mng_list = $sth_comm_mng->fetchAll(PDO::FETCH_ASSOC);
    foreach ($comm_mng_list as $row => $comm_mng) //遍历社区管理员
    {
        $comm_mng_id=$comm_mng['ID'];
        $comm_mng_name=$comm_mng['Account'];//installer
        $comm_location=$comm_mng['Location'];//社区的location
        //社区公共设备
        $sth_comm_dev = $db->prepare("select MAC,CreateTime,Firmware,Status,LastConnection from Devices where MngAccountID=:MngID and Grade = 1  and (CreateTime between '".$timestart."' and '".$timeend."');");
        $sth_comm_dev->bindParam(':MngID', $comm_mng_id, PDO::PARAM_INT);
        $sth_comm_dev->execute();
        $comm_dev_list = $sth_comm_dev->fetchAll(PDO::FETCH_ASSOC);
        foreach ($comm_dev_list as $row => $comm_pub_dev) //社区公共设备
        {
            $pub_dev_mac = $comm_pub_dev['MAC'];
            $pub_dev_time = $comm_pub_dev['CreateTime'];
            $pub_dev_ver = $comm_pub_dev['Firmware'];
            $pub_dev_lastconn = $comm_pub_dev['LastConnection'];
            $pub_dev_status = $comm_pub_dev['Status'];
            $static_str = null;
            
            if(($comm_location_tmp == $comm_location) && ($comm_mng_name_tmp == $comm_mng_name))
            {
                $static_str = ',' . ''. ',' . ',' . ',' . 'PublicArea'. ',' .$pub_dev_mac . ',' . $pub_dev_time . ',' . ','. ',' . ','. $pub_dev_ver . ',' . ','. ',' .$pub_dev_status .','.$pub_dev_lastconn . ',';
            }
            else if(($comm_location_tmp != $comm_location) && ($comm_mng_name_tmp == $comm_mng_name))
            {
                $static_str = ',' . ''. ',' . '' . ',' . $comm_location.',' .'PublicArea'. ',' .$pub_dev_mac . ',' . $pub_dev_time . ',' . ','. ',' . ','. $pub_dev_ver . ',' . ','. ',' .$pub_dev_status .','.$pub_dev_lastconn . ',';
            }
            else if (($comm_mng_name_tmp != $comm_mng_name) && ($dis_name_tmp == $dis_name))
            {
                $static_str = ',' . ''. ',' . $comm_mng_name . ','. $comm_location.','. 'PublicArea'. ',' .$pub_dev_mac . ',' . $pub_dev_time . ',' . ','. ',' . ','. $pub_dev_ver . ',' . ','. ',' .$pub_dev_status .','.$pub_dev_lastconn . ',';
            }
            else
            {
                $static_str = ',' . $dis_name. ',' . $comm_mng_name . ',' . $comm_location.',' . 'PublicArea'. ',' .$pub_dev_mac . ',' . $pub_dev_time . ',' . ','. ',' . ','. $pub_dev_ver . ',' . ','. ',' .$pub_dev_status .','.$pub_dev_lastconn . ',';
            }
            STATIS_WRITE($static_str);
            $comm_location_tmp = $comm_location; //缓存上一次的社区账号
            $comm_mng_name_tmp = $comm_mng_name; //缓存上一次的个人终端管理员
            $dis_name_tmp = $dis_name; //缓存上一次的区域管理员
        }
        
        //社区单元公共设备
        $sth_comm_uint_dev = $db->prepare("select D.MAC,D.CreateTime,U.UnitName,D.Firmware,D.LastConnection,D.Status from Devices D left join CommunityUnit U on D.UnitID = U.ID where D.MngAccountID=:MngID and D.Grade = 2  and (D.CreateTime between '".$timestart."' and '".$timeend."') order by D.UnitID");
        $sth_comm_uint_dev->bindParam(':MngID', $comm_mng_id, PDO::PARAM_INT);
        $sth_comm_uint_dev->execute();
        $comm_unit_dev_list = $sth_comm_uint_dev->fetchAll(PDO::FETCH_ASSOC);
        foreach ($comm_unit_dev_list as $row => $comm_unit_dev) //社区公共设备
        {
            $unit_name = $comm_unit_dev['UnitName'];
            $pub_dev_mac = $comm_unit_dev['MAC'];
            $pub_dev_time = $comm_unit_dev['CreateTime'];
            $pub_dev_ver = $comm_unit_dev['Firmware'];
            $pub_dev_lastconn = $comm_unit_dev['LastConnection'];
            $pub_dev_status = $comm_unit_dev['Status'];
            $static_str = null;

            if(($unit_name_tmp == $unit_name) && ($comm_location_tmp == $comm_location))
            {
                $static_str = ',' . ''. ',' . '' . ',' . ','. ','. $pub_dev_mac . ',' . $pub_dev_time . ',' . ','. ',' . ','. $pub_dev_ver . ',' . ','. ',' .$pub_dev_status .','.$pub_dev_lastconn . ',';
            }            
            else if(($unit_name_tmp != $unit_name) && ($comm_location_tmp == $comm_location) && ($comm_mng_name_tmp == $comm_mng_name))
            {
                $static_str = ',' . ''. ',' . '' . ',' . ','. $unit_name . ',' . $pub_dev_mac . ',' . $pub_dev_time . ',' . ','. ',' . ','. $pub_dev_ver . ',' . ','. ',' .$pub_dev_status .','.$pub_dev_lastconn . ',';
            }
            else if(($comm_location_tmp != $comm_location) && ($comm_mng_name_tmp == $comm_mng_name)) //证明区域管理员也是相同的
            {
                $static_str = ',' . ''. ',' . '' . ',' . $comm_location . ','. $unit_name . ',' . $pub_dev_mac . ',' . $pub_dev_time . ',' . ','. ',' . ','. $pub_dev_ver . ',' . ','. ',' .$pub_dev_status .','.$pub_dev_lastconn . ',';
            }
            else if (($comm_mng_name_tmp != $comm_mng_name) && ($dis_name_tmp == $dis_name))
            {
                $static_str = ',' . ''. ',' . $comm_mng_name . ','. $comm_location . ',' . $unit_name . ',' . $pub_dev_mac . ',' . $pub_dev_time . ',' . ','. ',' . ','. $pub_dev_ver . ',' . ','. ',' .$pub_dev_status .','.$pub_dev_lastconn . ',';
            }
            else
            {
                $static_str = ',' . $dis_name. ',' . $comm_mng_name . ','. $comm_location . ',' . $unit_name . ',' . $pub_dev_mac . ',' . $pub_dev_time . ',' . ','. ',' . ','. $pub_dev_ver . ',' . ','. ',' .$pub_dev_status .','.$pub_dev_lastconn . ',';
            }
            
            STATIS_WRITE($static_str);
            $unit_name_tmp = $unit_name; //缓存上一次的社区
            $comm_location_tmp = $comm_location; //缓存上一次的社区
            $comm_mng_name_tmp = $comm_mng_name; //缓存上一次的个人终端管理员
            $dis_name_tmp = $dis_name; //缓存上一次的区域管理员
        }
        
        //3、社区联动系统的列表
        //查询主账号
        $sth_comm_master = $db->prepare("select ID,Account,Name,PhoneStatus from PersonalAccount where ParentID=:ParentID and Role = 20 order by UnitID");
        $sth_comm_master->bindParam(':ParentID', $comm_mng_id, PDO::PARAM_INT);
        $sth_comm_master->execute();
        $comm_master_list = $sth_comm_master->fetchALL(PDO::FETCH_ASSOC);
        foreach ($comm_master_list as $row => $master) //统计主账号
        {
            $comm_node_id=$master['ID'];
            $comm_node=$master['Account'];
            $comm_node_name=$master['Name'];
            $comm_node_phone_status=$master['PhoneStatus'];
            //查询联动系统的用户数
            $sth_fellow = $db->prepare("select count(1) as fellow_num from PersonalAccount where ParentID=:ParentID and role=21");
            $sth_fellow->bindParam(':ParentID', $comm_node_id, PDO::PARAM_INT);
            $sth_fellow->execute();
            $node_fellow_num = $sth_fellow->fetch(PDO::FETCH_ASSOC);
            $fellow_num = $node_fellow_num['fellow_num'] + 1;
            
            //查询设备列表
            $sth_comm_dev = $db->prepare("select MAC,Firmware,CreateTime,Status,LastConnection from Devices where Node=:Node  and (CreateTime between '".$timestart."' and '".$timeend."');");
            $sth_comm_dev->bindParam(':Node', $comm_node, PDO::PARAM_STR);
            $sth_comm_dev->execute();
            $node_mac_list = $sth_comm_dev->fetchALL(PDO::FETCH_ASSOC);
            foreach ($node_mac_list as $row => $dev) //统计联动系统下的设备列表
            {
                $dev_mac = $dev['MAC'];
                $dev_ver = $dev['Firmware'];
                $dev_time = $dev['CreateTime'];
                $dev_status = $dev['Status'];
                $dev_last_conn_time = $dev['LastConnection'];
                //开始打印
                $static_str = null;
                if($node_tmp == $comm_node) 
                {
                    $static_str = ',' . ''. ',' . '' . ',' . ','. ','. ','. ''. ',' . '' . ',' . ','. $dev_mac . ',' . $dev_ver . ',' . $dev_time . ',' . $comm_node_phone_status . ',' . $dev_status . ',' . $dev_last_conn_time . ',';
                }
                else if(($node_tmp != $comm_node) && ($comm_location_tmp == $comm_location) &&($comm_mng_name_tmp == $comm_mng_name))
                {
                    $static_str = ',' . ''. ',' . '' . ',' .''. ','. ','. ','. ','.$comm_node. ',' . $fellow_num . ',' . $dev_mac . ',' . $dev_ver . ',' . $dev_time . ',' . $comm_node_phone_status . ',' . $dev_status . ',' . $dev_last_conn_time . ',';
                }
                else if(($comm_location_tmp != $comm_location) && ($comm_mng_name_tmp == $comm_mng_name))
                {
                    $static_str = ',' . ''. ',' . '' . ',' .',' . $comm_location .''. ','. ','. ','.$comm_node. ',' . $fellow_num . ',' . $dev_mac . ',' . $dev_ver . ',' . $dev_time . ',' . $comm_node_phone_status . ',' . $dev_status . ',' . $dev_last_conn_time . ',';
                }
                else if(($node_tmp != $comm_node) && ($comm_mng_name_tmp != $comm_mng_name) &&($dis_name_tmp == $dis_name))
                {
                    $static_str = ',' . ''. ',' . $comm_mng_name . ',' . $comm_location .',' .',' . ','.',' . $comm_node. ',' . $fellow_num . ',' . $dev_mac . ',' . $dev_ver . ',' . $dev_time . ',' . $node_phone_status . ',' . $dev_status . ',' . $dev_last_conn_time . ',';
                }
                else
                {
                    $static_str = ',' . $dis_name. ',' . $comm_mng_name .',' . $comm_location . ',' .',' . ','.',' . $comm_node. ',' . $fellow_num . ',' . $dev_mac . ',' . $dev_ver . ',' . $dev_time . ',' . $node_phone_status . ',' . $dev_status . ',' . $dev_last_conn_time . ',';
                }
                STATIS_WRITE($static_str);
                $node_tmp = $comm_node;
                $comm_mng_name_tmp = $comm_mng_name; //缓存上一次的个人终端管理员
                $dis_name_tmp = $dis_name; //缓存上一次的区域管理员
            }
        }
    }      
}
#added by chenyc,2019-05-21，增加一些业务量的统计
#added by chenyc,2020-08-07,当一周的时间跨越月度的时候，一周的数据会有问题，少了上月的数据了。
function getCallHistoryStatistics($startTime, $endTime)
{
    $db = getDB();
    $sth = $db->prepare("select count(1) as count,
    sum(Duration2)as totaltime,
    sum(case CallType when 1 then 1 else 0 end) as a2a,
    sum(case CallType when 2 then 1 else 0 end) as d2a,
    sum(case CallType when 3 then 1 else 0 end) as a2d,
    sum(case CallType when 4 then 1 else 0 end) as d2d 
    From CallHistory where StartTime between \"$startTime\" and \"$endTime\";");
    $sth->execute();
    $statistics = $sth->fetch(PDO::FETCH_ASSOC);

    return $statistics;
}
//total=count Duration=totaltime
function getManualAppRtspStatistics($startTime, $endTime)
{
    $db = getDB();
    $sth = $db->prepare("select count(1) as count,
    sum(Duration)as totaltime
    From AppManualRtsp where CreateTime between \"$startTime\" and \"$endTime\";");
    $sth->execute();
    $statistics = $sth->fetch(PDO::FETCH_ASSOC);

    return $statistics;
}

//total=count 0=call2,1=tmpkey,2=privatekey,3=rfkey,4=face,100=nfc,101=ble,102=app手动截图
function getReleaseDoorsStatistics($startTime, $endTime)
{
    $db = getDB();
    $sth = $db->prepare("select 
    sum(case when CaptureType != 102 then 1 else 0 end) as count,
    sum(case CaptureType when 0 then 1 else 0 end) as call2,
    sum(case CaptureType when 1 then 1 else 0 end) as tmpkey,
    sum(case CaptureType when 2 then 1 else 0 end) as privatekey,
    sum(case CaptureType when 3 then 1 else 0 end) as rfkey,
    sum(case CaptureType when 4 then 1 else 0 end) as face,
    sum(case CaptureType when 5 then 1 else 0 end) as remote_app,
    sum(case CaptureType when 6 then 1 else 0 end) as call_app,
    sum(case CaptureType when 7 then 1 else 0 end) as call_indoor,
    sum(case CaptureType when 100 then 1 else 0 end) as nfc,
    sum(case CaptureType when 101 then 1 else 0 end) as ble,
    sum(case CaptureType when 102 then 1 else 0 end) as appcapture    
    From PersonalCapture where CaptureTime between \"$startTime\" and \"$endTime\";");
    $sth->execute();
    $statistics = $sth->fetch(PDO::FETCH_ASSOC);
    
    return $statistics;
}

$callAll = getCallHistoryStatistics($timestart, $timeend);
$call_total = $callAll['count'];

#登记几台设备
$sth = $db->prepare("select count(1) as register from Devices where CreateTime between '".$timestart."' and '".$timeend."';");
$sth->execute();
$resultRole = $sth->fetch(PDO::FETCH_ASSOC);
$comm_register_count = $resultRole['register'];

$per_sth = $db->prepare("select count(1) as register from PersonalDevices where CreateTime between '".$timestart."' and '".$timeend."';");
$per_sth->execute();
$per_resultRole = $per_sth->fetch(PDO::FETCH_ASSOC);
$per_register_count = $per_resultRole['register'];

#几台设备在线
$sth = $db->prepare("select count(1) as online from Devices where Status = 1 and CreateTime between '".$timestart."' and '".$timeend."';");
$sth->execute();
$resultRole = $sth->fetch(PDO::FETCH_ASSOC);
$comm_online_count = $resultRole['online'];

$sth = $db->prepare("select count(1) as online from PersonalDevices where Status = 1 and CreateTime between '".$timestart."' and '".$timeend."';");
$sth->execute();
$resultRole = $sth->fetch(PDO::FETCH_ASSOC);
$per_online_count = $resultRole['online'];

$total_dev_count = $comm_register_count + $per_register_count;
$total_dev_online = $comm_online_count + $per_online_count;
$total_online_rate = $total_dev_online * 100 / $total_dev_count;

#app总数
$sth = $db->prepare("select count(1) as count from PersonalAccount where (CreateTime between '".$timestart."' and '".$timeend."') and (Role = 10 or Role = 11 or Role = 20 or Role = 21);");
$sth->execute();
$resultRole = $sth->fetch(PDO::FETCH_ASSOC);
$total_app_count = $resultRole['count'];
#已经激活的app总数
$sth = $db->prepare("select count(1) as count from PersonalAccount where (CreateTime between '".$timestart."' and '".$timeend."') and (Role = 10 or Role = 11 or Role = 20 or Role = 21) and Active = 1;");
$sth->execute();
$resultRole = $sth->fetch(PDO::FETCH_ASSOC);
$total_app_count_actived = $resultRole['count'];

#家庭总数
$sth = $db->prepare("select count(1) as count from PersonalAccount where (CreateTime between '".$timestart."' and '".$timeend."') and (Role = 10 or Role = 20)");
$sth->execute();
$resultRole = $sth->fetch(PDO::FETCH_ASSOC);
$total_master_count = $resultRole['count'];

#已经激活的家庭总数
$sth_act_family = null;
if($time_one_week == TRUE)
{
    $sth_act_family = $db->prepare("select count(1) as count from PersonalAccount where (ActiveTime between '".$timestart."' and '".$timeend."') and (Role = 10 or Role = 20) and Active = 1 and special = 0;");//Special的字段1就是空房间,代码写得不好，默认激活
}
else
{
    $sth_act_family = $db->prepare("select count(1) as count from PersonalAccount where ((ActiveTime is NULL and CreateTime < '2019-09-01 00:00:00' and (Role = 10 or Role = 20) and Active = 1) or (ActiveTime < '" . $now . "' and (Role = 10 or Role = 20) and Active = 1  and special = 0));");//  ActiveTime is NULL主要是处理收费版本之前,激活时间默认都是NULL;
}
$sth_act_family->execute();
$resultRole = $sth_act_family->fetch(PDO::FETCH_ASSOC);
$total_master_count_active = $resultRole['count'];

#added by chenyc, 2020-04-03,增加单独的社区激活统计
if($time_one_week != TRUE)
{
    $sth_act_comm_family = $db->prepare("select count(1) as count from PersonalAccount where ((ActiveTime is NULL and CreateTime < '2019-09-01 00:00:00' and (Role = 20) and Active = 1) or (ActiveTime < '" . $now . "' and (Role = 20) and Active = 1  and special = 0));");
    $sth_act_comm_family->execute();
    $resultRole = $sth_act_comm_family->fetch(PDO::FETCH_ASSOC);
    $total_comm_master_count_active = $resultRole['count'];
}    
#通话
$callAll = getCallHistoryStatistics($timestart, $timeend);
$call_total = $callAll['count'];
$call_a2a = $callAll['a2a'];
$call_a2d = $callAll['a2d'];
$call_d2d = $callAll['d2d'];
$call_d2a = $callAll['d2a'];
#开门
$doorAll = getReleaseDoorsStatistics($timestart, $timeend);
$door_total = $doorAll['count'];
#liveview
$liveviewAll = getManualAppRtspStatistics($timestart, $timeend);
$liveview_total = $liveviewAll['count'];
#app capture
$sth = $db->prepare("select count(1) as count from PersonalCapture where CaptureType = 102 and CaptureTime between '".$timestart."' and '".$timeend."';");
$sth->execute();
$resultRole = $sth->fetch(PDO::FETCH_ASSOC);
$total_app_capture_count = $resultRole['count'];
if($time_one_week == TRUE)
{
    $static_total_str = 'Server' . ',' .'New Familys'. ',' . 'New Actived Familys '.','.'New Devs'. ','. 'New Apps'. ',' . 'New Call Times'.',' . 'New app2app Times'.','. 'New app2dev Times'.','. 'New dev2dev Times'.','. 'New dev2app Times'.','. 'New Door-Open Times'. ',' .'New Live_view Times'.',' . 'New App-Capture Times'.',' . 'New Dev Online Rate'.',' . 'New abnormity' . ',';
    
    STATIS_TOTAL_WRITE($static_total_str); 
    $static_str_weekly = ',' . $total_master_count. ',' . $total_master_count_active. ',' . $total_dev_count. ',' . $total_app_count . ',' .$call_total . ',' .$call_a2a . ',' .$call_a2d . ',' .$call_d2d . ',' .$call_d2a . ','. $door_total .',' . $liveview_total. ',' . $total_app_capture_count . ',' . $total_online_rate . ',' ;
    STATIS_TOTAL_WRITE($static_str_weekly);
}
/*
else
{
    $static_total_str = 'Server' . ',' .'Total Familys'. ',' .'Total Actived Familys'. ',' .'Community Actived Familys'. ',' . 'Total Devs'. ',' . 'Total Apps'. ',' . 'Total Call Times'.',' . 'Total app2app Times'.','. 'Total app2dev Times'.','. 'Total dev2dev Times'.','. 'Total dev2app Times'.',' . 'Total Door-Open Times'. ',' .'Total Live_view Times'.',' . 'Total App-Capture Times'.',' . 'Total Dev Online Rate'.',' . 'Total abnormity' . ',';
    
    STATIS_TOTAL_WRITE($static_total_str); 
    $static_str_weekly = ',' . $total_master_count. ',' . $total_master_count_active. ','. $total_comm_master_count_active. ',' . $total_dev_count. ',' . $total_app_count . ',' .$call_total . ',' .$call_a2a . ',' .$call_a2d . ',' .$call_d2d . ',' .$call_d2a . ','. $door_total .',' . $liveview_total. ',' . $total_app_capture_count . ',' . $total_online_rate . ',' ;
    STATIS_TOTAL_WRITE($static_str_weekly);
}
*/
?>
