let fs = require('fs');

class modFileVersion {
    constructor(path, options) {
        this.path = path;
        this.$options = options || {};
        let that = this;
        fs.readdir(this.path, function(err, val) {
            if (val) {
                that.files = [];
                let reg = /.html$/
                for (let i in val) {
                    if (reg.test(val[i]))
                        that.files.push(val[i])
                }
                reg = /.js$/
                for (let i in val) {
                    if (reg.test(val[i]))
                        that.files.push(val[i])
                }
            }
            that.readFileContent()
        })
    }

    readFileContent() {
        let that = this;
        for (let i in this.files) {
            let path = this.path + this.files[i];
            fs.readFile(path, function(err, data) {
                if (err) console.log('read' + that.files[i] + ' error', err)
                if (data) {
                    data = data.toString();
                    let newFileContent = data;
                    for (let i in that.$options) {
                        newFileContent = that.modVersion(newFileContent, i);
                    }
                    fs.writeFile(path, newFileContent, function(err) {
                        if (err) {
                            console.log(err);
                            return
                        }
                        console.log('write file success for ' + path);
                    })
                }
            })
        }
    }

    modVersion(data, i) {
        let fileName, newFileName, index, indexD, indexQ, indexq, version, newVersion, versionArr;
        let opeare = this.$options[i];
        i += "?v";
        let endStart = 0;
        let start;
        do {
            start = endStart;
            index = data.toLowerCase().indexOf(i, start);
            if (index > -1) {
                indexD = data.indexOf('=', index);
                indexQ = data.indexOf('\"', indexD);
                indexq = data.indexOf("\'", indexD);
                if (indexQ > -1 && indexq > -1)
                    indexQ = indexQ > indexq ? indexq : indexQ
                else if (indexq > -1)
                    indexQ = indexq
                endStart = indexQ;
                newFileName = fileName = data.substring(index, indexQ);
                version = data.substring(indexD + 1, indexQ);
                // versionArr = version.split('.')
                // switch (opeare){
                // 	case 'adds':
                // 		versionArr[versionArr.length-1]++;
                // 		newVersion = versionArr.join('.');
                // 		break;
                // 	case 'addm':
                // 		versionArr[versionArr.length-2]++;
                // 		newVersion = versionArr.join('.');
                // 		break;
                // 	case 'addb':
                // 		versionArr[versionArr.length-3]++;
                // 		newVersion = versionArr.join('.');
                // 		break;
                // 	default:
                // 		newVersion = opeare
                // 		break;
                // }
                newVersion = opeare
                newFileName = newFileName.replace(version, newVersion);


                data = data.replace(fileName, newFileName);
            }
        } while (start != endStart)

        return data;
    }
}
const argv = process.argv
var version = argv[2];
if (argv.length < 5) {
    console.log("至少需要两个路径");
    throw paramError
}
for (let i = 3; i < argv.length; i++) {
    var path = argv[i];
    new modFileVersion(path, {
        '.js': version,
        '.css': version
    })
}