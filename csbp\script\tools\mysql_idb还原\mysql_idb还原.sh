#!/bin/bash
#by chenzhx ********
#把脚本和.frm .ibd放在同个目录下
TABLE_NAME=Account
DATABASE=recover_test5
chown mysql:mysql ${TABLE_NAME}*
MYSQL_CDM_HEAD="mysql -h 127.0.0.1 -u root -pAk@56@<EMAIL> -e"
MYSQL_DATA_PATH=/var/lib/mysql/
TABLE_SCM=`mysqlfrm --diagnostic ${TABLE_NAME}.frm`
if [ ! -f /usr/bin/mysqlfrm ];then
   apt-get -f install mysql-utilities
fi
`$MYSQL_CDM_HEAD "DROP DATABASE IF EXISTS $DATABASE;"`
`$MYSQL_CDM_HEAD "create database $DATABASE DEFAULT CHARSET utf8;"`
`$MYSQL_CDM_HEAD "use $DATABASE;$TABLE_SCM"`
`$MYSQL_CDM_HEAD "use $DATABASE;alter table ${TABLE_NAME} discard tablespace;"`
cp ${TABLE_NAME}.ibd $MYSQL_DATA_PATH/$DATABASE/
chown mysql:mysql $MYSQL_DATA_PATH/$DATABASE/$TABLE_NAME.ibd
`$MYSQL_CDM_HEAD "use $DATABASE;alter table ${TABLE_NAME} import tablespace;"`
