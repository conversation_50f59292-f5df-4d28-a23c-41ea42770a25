<?php
function getDB()
{
	$dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
	$dbport = 3306;
    
    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

function getFSDB()
{
	$dbhost = "*************";
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "freeswitch";
	$dbport = 3305;
    
    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

const STATIS_FILE1 = "/root/check_grouping.log";
shell_exec("touch ". STATIS_FILE1);
chmod(STATIS_FILE1, 0777);

function TRACE1($content)
{
    $tmpNow = time();
    #$Now = date('Y-m-d H:i:s', $tmpNow);    
	@file_put_contents(STATIS_FILE1, $content, FILE_APPEND);
	@file_put_contents(STATIS_FILE1, "\n", FILE_APPEND);
	echo "$content\n";
}
TRACE1("===================================");

$db = getDB();
$dbfs = getFSDB();

$db->beginTransaction();
$dbfs->beginTransaction();

$sth = $db->prepare("select A.appLoginStatus,A.Active,A.Account,A.PhoneStatus,C.Calltype  From PersonalAccountCnf  C left join PersonalAccount A on A.Account=C.Account where A.Role=20;");
$sth->execute();
$accountlist = $sth->fetchALL(PDO::FETCH_ASSOC);

foreach ($accountlist as $row => $val)
{	
	$phonestatus = $val['PhoneStatus'];
	$account = $val['Account'];
	$calltype = $val['Calltype'];
	$active = $val['Active'];
	$appLoginStatus = $val['appLoginStatus'];
	if ($calltype == "1" && $phonestatus == "0")
	{
		TRACE1("AKCS1 active=$active appLoginStatus=$appLoginStatus account=$account calltype=$calltype phonestatus=$phonestatus update_phonestatus=1");
		$sth = $db->prepare("update PersonalAccount set PhoneStatus=:PhoneStatus where Account=:Account;");
		$tmp = 1;
		$sth->bindParam(':PhoneStatus', $tmp, PDO::PARAM_STR);
		$sth->bindParam(':Account', $account, PDO::PARAM_STR);
		$sth->execute();

	}
	if ($calltype != "1" && $phonestatus == "1")
	{
		TRACE1("AKCS2 active=$active appLoginStatus=$appLoginStatus account=$account calltype=$calltype phonestatus=$phonestatus  update_phonestatus=0");
		$sth = $db->prepare("update PersonalAccount set PhoneStatus=:PhoneStatus where Account=:Account;");
		$tmp = 0;
		$sth->bindParam(':PhoneStatus', $tmp, PDO::PARAM_STR);
		$sth->bindParam(':Account', $account, PDO::PARAM_STR);
		$sth->execute();		
	}
	
	$sth = $dbfs->prepare("select groupring,username from userinfo where username=:username;");
	$sth->bindParam(':username', $account, PDO::PARAM_STR);
	$sth->execute();
	$groupring2 = $sth->fetch(PDO::FETCH_ASSOC);
	$groupring = $groupring2['groupring'];
	
	if ($groupring == "0")
	{
		if ($calltype != "1")
		{
			TRACE1("PBX3 active=$active appLoginStatus=$appLoginStatus account=$account calltype=$calltype groupring=$groupring update_groupring=1");			
			$sth = $dbfs->prepare("update userinfo set groupring=:groupring where username=:username;");
			$sth->bindParam(':username', $account, PDO::PARAM_STR);
			$tmp = 1;
			$sth->bindParam(':groupring', $tmp, PDO::PARAM_STR);
			$sth->execute();				
		}
	}
	if ($groupring == "1")
	{
		if ($calltype == "1")
		{
			TRACE1("PBX4 active=$active appLoginStatus=$appLoginStatus account=$account calltype=$calltype groupring=$groupring update_groupring=0");
			$sth = $dbfs->prepare("update userinfo set groupring=:groupring where username=:username;");
			$sth->bindParam(':username', $account, PDO::PARAM_STR);
			$tmp = 0;
			$sth->bindParam(':groupring', $tmp, PDO::PARAM_STR);
			$sth->execute();			
		}
	}	
	
}

#$db->commit();
#$dbfs->commit();


?>