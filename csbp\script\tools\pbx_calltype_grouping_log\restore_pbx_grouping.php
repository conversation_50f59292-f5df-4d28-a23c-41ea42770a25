<?php
function getDB()
{
	$dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "freeswitch";
	$dbport = 3305;
    
    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

const STATIS_FILE1 = "/root/restore_pbx_grouping.log";
shell_exec("touch ". STATIS_FILE1);
chmod(STATIS_FILE1, 0777);

function TRACE1($content)
{
    $tmpNow = time();
    #$Now = date('Y-m-d H:i:s', $tmpNow);    
	@file_put_contents(STATIS_FILE1, $content, FILE_APPEND);
	@file_put_contents(STATIS_FILE1, "\n", FILE_APPEND);
	echo "$content\n";
}
#TRACE1("===================================");

$db = getDB();
$db->beginTransaction();

$file = popen("cat ucloud-check_grouping.log   | grep PBX | grep 'active=1'","r");

while(! feof($file))
{
	$line = fgets($file);
	$account=shell_exec("echo  \"$line\" | awk -F 'account=' '{print $2}' | awk '{print $1}' | tr -d '\n' ");
	$groupring=shell_exec("echo  \"$line\" | awk -F 'groupring=' '{print $2}' | awk '{print $1}' | tr -d '\n' ");

	if (strlen($account) > 0)
	{
		TRACE1("update userinfo set groupring=$groupring where username=$account;");
		$sth = $db->prepare("update userinfo set groupring=:groupring where username=:username;");
		$sth->bindParam(':username', $account, PDO::PARAM_STR);
		$sth->bindParam(':groupring', $groupring, PDO::PARAM_STR);
		#$sth->execute();
	}	
}

pclose($file);
#$db->commit();

/*

foreach ($accountlist as $row => $val)
{	
	$sth = $dbfs->prepare("update userinfo set groupring=:groupring where username=:username;");
	$sth->bindParam(':username', $account, PDO::PARAM_STR);
	$tmp = 0;
	$sth->bindParam(':groupring', $tmp, PDO::PARAM_STR);
	$sth->execute();
	
}

#$db->commit();
#$dbfs->commit();

*/
?>