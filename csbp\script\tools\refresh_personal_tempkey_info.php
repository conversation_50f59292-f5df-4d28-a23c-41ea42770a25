<?php

/*
  20220314
  刷新APP创建的temp key
 * 增加TEMP KEY对应的设备列表，写入PersonalAppTmpKeyList
 */

function getDB() {
    $dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

$db = getDB();

function refreshPersonalAppTmpKey() {
    global $db;

    $sth = $db->prepare("select A.Email,
       A.Name,
       MngAccountID,
       Node,
       A.UnitID,
       SchedulerType,
       BeginTime,
       EndTime,
       StartTime,
       StopTime,
       P.CreateTime,
       P.ID
  From PersonalAppTmpKey P
  left join PersonalAccount A
    on A.Account = P.Node
 where 1 = 1
   and ((SchedulerType = 0 and sysdate() >= BeginTime and
       sysdate() <= EndTime) or SchedulerType = 1 or SchedulerType = 2)");

    $sth->execute();
    $keyList = $sth->fetchAll(PDO::FETCH_ASSOC);
    foreach ($keyList as $row => $key) {
        $keyID = $key['ID'];
        $devList = getDevices($key['Node'], $key['MngAccountID'], $key['UnitID']);
        foreach ($devList as $row => $dev) {
            $doorNum = getDoorNum($dev['Relay']);
            insertPersonalAppTmpKeyList($dev['MAC'], $keyID, $doorNum);
        }
    }
}

function getDoorNum($relay) {
    $relays = explode(";", $relay);
    $doorNum = 0;
    foreach ($relays as $key => $value) {
        if (explode(",", $value)[4] == 1) {
            $doorNum += getRelayValue($key);
        }        
    }
    return $doorNum;
}

function getRelayValue($val) {
    return [1, 2, 4, 8][$val];
}

function insertPersonalAppTmpKeyList($mac, $keyID, $relay) {
    global $db;
    $delSth = $db->prepare("delete from PersonalAppTmpKeyList where MAC = :MAC and KeyID = :KeyID");
    $delSth->bindParam(':MAC', $mac, PDO::PARAM_STR);
    $delSth->bindParam(':KeyID', $keyID, PDO::PARAM_INT);    
    $delSth->execute();

    $sth = $db->prepare("insert into PersonalAppTmpKeyList (MAC, KeyID, Relay) values(:MAC, :KeyID, :Relay)");
    $sth->bindParam(':MAC', $mac, PDO::PARAM_STR);
    $sth->bindParam(':KeyID', $keyID, PDO::PARAM_INT);
    $sth->bindParam(':Relay', $relay, PDO::PARAM_INT);
    $sth->execute();
}

function getDevices($node, $mngAccountID, $unitID) {
    global $db;
    $sth = $db->prepare("select MAC, Relay
                          from PersonalDevices
                         where node = :Node
                           and type in (0, 1, 50)
                        union all
                        select MAC, Relay
                          from Devices
                         where grade = 1
                           and MngAccountID = :MngAccountID
                           and type in (0, 1, 50)
                        union all
                        select MAC, Relay
                          from Devices
                         where grade = 2
                           and UnitID = :UnitID
                           and type in (0, 1, 50)
                        union all
                        select MAC, Relay
                          from Devices
                         where grade = 3
                           and node = :Node
                           and type in (0, 1, 50)");

    $sth->bindParam(':Node', $node, PDO::PARAM_STR);
    $sth->bindParam(':MngAccountID', $mngAccountID, PDO::PARAM_INT);
    $sth->bindParam(':UnitID', $unitID, PDO::PARAM_INT);
    $sth->execute();
    $result = $sth->fetchAll(PDO::FETCH_ASSOC);
    return $result;
}

refreshPersonalAppTmpKey();
print_r("refreshPersonalAppTmpKey Success\n");
?>