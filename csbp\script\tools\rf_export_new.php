<?php

const TMPLOG = "/tmp/rf_export.csv";
function logWrite($content)
{
	file_put_contents(TMPLOG, $content, FILE_APPEND);
	file_put_contents(TMPLOG, "\n", FILE_APPEND);
}

function getDB()
{
    $dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";

    $mysql_conn_string = "mysql:host=$dbhost;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
	$dbConnection->query('set names utf8;');
    return $dbConnection;
}
	shell_exec("touch ". TMPLOG);
	chmod(TMPLOG, 0777);
	if (file_exists(TMPLOG)) {
		shell_exec("echo > ". TMPLOG);
	} 

resident();	

function staff(){
	logWrite('Name'.','.'Rf'.','.'AccessGroup'.',');
	$communityid = 4651;
	$db = getDB();	
	$sth = $db->prepare("select * from Staff where CommunityID = :CommunityID order by ID desc;");
	$sth->bindParam(':CommunityID', $communityid, PDO::PARAM_INT);
	$sth->execute();
	$list = $sth->fetchAll(PDO::FETCH_ASSOC);	
	foreach ($list as $row => $value){
		$id = $value['ID'];
		$name = $value['Name'];
		$code = $value['CardCode'];	

				$sth1 = $db->prepare("select AG.Name from AccessGroup AG join StaffAccess T on AG.ID = T.AccessGroupID where T.StaffID = :ID");
				$sth1->bindParam(':ID', $id, PDO::PARAM_INT);
				$sth1->execute();
				$ret = $sth1->fetchAll(PDO::FETCH_ASSOC);
				$accesslist = "";
				foreach ($ret as $row => $access){
					$accesslist = $accesslist.$access['Name'].';';
				}
				logWrite($name.','.$code.','.$accesslist.',');						
	}
}

function resident(){
	logWrite('UserName'.','.'Email'.','.'Rf'.',');
	$communityid = 4651;
	$db = getDB();	
	$sth = $db->prepare("select P.Name,P.Email,R.Code from CommPerRfKey R join PersonalAccount P on R.Account = P.Account where CommunityID = :CommunityID;");
	$sth->bindParam(':CommunityID', $communityid, PDO::PARAM_INT);
	$sth->execute();
	$list = $sth->fetchAll(PDO::FETCH_ASSOC);	
	foreach ($list as $row => $value){
				logWrite($value['Name'].','.$value['Email'].','.$value['Code'].',');						
	}
}


		

