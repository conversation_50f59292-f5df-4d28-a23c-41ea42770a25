<?php

const TMPLOG = "/tmp/rf_export.csv";
function logWrite($content)
{
	file_put_contents(TMPLOG, $content, FILE_APPEND);
	file_put_contents(TMPLOG, "\n", FILE_APPEND);
}

function getDB()
{
    $dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";

    $mysql_conn_string = "mysql:host=$dbhost;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
	$dbConnection->query('set names utf8;');
    return $dbConnection;
}
	shell_exec("touch ". TMPLOG);
	chmod(TMPLOG, 0777);
	if (file_exists(TMPLOG)) {
		shell_exec("echo > ". TMPLOG);
	} 
	
	logWrite('Name'.','.'Rf'.','.'Mac'.',');
	$communityid = 1763;
	$db = getDB();	
	$sth = $db->prepare("select ID,Name,Code from PubRfcardKey where MngAccountID = :CommunityID AND OwnerType = 0;");
	$sth->bindParam(':CommunityID', $communityid, PDO::PARAM_INT);
	$sth->execute();
	$list = $sth->fetchAll(PDO::FETCH_ASSOC);	
	foreach ($list as $row => $value){
		$id = $value['ID'];
		$name = $value['Name'];
		$code = $value['Code'];	//1-个人 0-社区

				$sth1 = $db->prepare("select MAC from PubRfcardKeyList where KeyID = :id");
				$sth1->bindParam(':id', $id, PDO::PARAM_INT);
				$sth1->execute();
				$ret = $sth1->fetchAll(PDO::FETCH_ASSOC);
				$maclist = "";
				foreach ($ret as $row => $mac){
					$maclist = $maclist.$mac['MAC'].';';
				}
				logWrite($name.','.$code.','.$maclist.',');						
	}
	

		

