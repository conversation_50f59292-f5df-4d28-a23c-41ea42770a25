<?php

/*
统计app日活
*/



date_default_timezone_set("PRC");

$csmainRedisConf = parse_ini_file("/usr/local/akcs/csmain/conf/csmain_redis.conf");
$host = $csmainRedisConf["appstat_host"];
$port = $csmainRedisConf["appstat_port"];
$pwd = "Akcs#xm2610*";
$db = $csmainRedisConf["appstat_db"];;
$redis = new Redis();

if ($redis->connect($host, $port) == false) {
  die($redis->getLastError());
}

if ($redis->auth($pwd) == false) {
  die($redis->getLastError());
}

$redis->select($db);

function showHelp() {
    print_r("Usage:  php stat_app_daily_active.php [date]\n");
    print_r("\t[date],if date string's length is 8, this means specify a day,like 20210825;else if date string's length is 6,this means specify a month, like 202108\n");
    exit(-1);
}

function getAllMonthData($redis, $date) {
	for ($i = 1; $i <= 31; $i++) {
		$day = $date . str_pad($i, 2 ,"0", STR_PAD_LEFT); 
		$count = $redis->scard($day);
		print_r("Day[$day] active user count is [$count]\n");
	}	
}

if ($argc == 2) {
	$date = $argv[1];
	if (strlen($date) == 6) {
		getAllMonthData($redis, $date);
	} else if (strlen($date) == 8) {
		$count = $redis->scard($date);
		print_r("Day[$date] active user count is [$count]\n");
	}
	$redis->close();
} else {
	showHelp();
}

?>
