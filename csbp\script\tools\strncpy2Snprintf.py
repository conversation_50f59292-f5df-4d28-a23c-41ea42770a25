import os
import re

# strncpy(dst, src, count)
# Snprintf(dst, count, src)

# 替换前字符串正则
origin_pattern_1 = r'strncpy\(([^,]+),([^,]+),\s*(\S+)\s*-\s*1\)'  # 带-1&第二个参数为变量
origin_pattern_2 = r'strncpy\(([^,]+),([^,]+),\s*(\S+)\)'  # 不带-1&第二个参数为变量
origin_pattern_3 = r'strncpy\(([^,]+),\s*"([^"]+)",\s*(\S+)\s*-\s*1\)'  # 带-1且第二个参数为常量
origin_pattern_4 = r'strncpy\(([^,]+),\s*"([^"]+)",\s*(\S+)\)'  # 不带-1且第二个参数为常量
# 替换后字符串正则
replace_pattern_1 = r'Snprintf(\1, \3, \2)'
replace_pattern_2 = r'Snprintf(\1, \3, "\2")'

# 遍历的目录
directory_path = r'E:\linux_shared\code\dev_6.7.0\app_backend\csvrecord\src'

# 遍历指定目录下的所有文件
for root, dirs, files in os.walk(directory_path):
    for file in files:
        if file.endswith(".cpp"):
            file_path = os.path.join(root, file)
            print("file:" + file_path)

            # 打开文件并读取内容
            try:
                with open(file_path, 'r', encoding='UTF-8') as f:
                    content = f.read()
            except UnicodeDecodeError:
                print("File contains non-UTF-8 characters. Specify the correct encoding.")

            # 使用正则表达式进行替换
            updated_content = re.sub(origin_pattern_1, replace_pattern_1, content)
            updated_content = re.sub(origin_pattern_2, replace_pattern_1, updated_content)
            updated_content = re.sub(origin_pattern_3, replace_pattern_2, updated_content)
            updated_content = re.sub(origin_pattern_4, replace_pattern_2, updated_content)

            # 将替换后的内容写回文件
            with open(file_path, 'w', encoding='UTF-8') as f:
                f.write(updated_content)

print("替换完成")
