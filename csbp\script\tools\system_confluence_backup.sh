#!/bin/bash

#confluence的备份
cd /workspaces/confluence/confluence-data/backups/
filename=`ls -lt /workspaces/confluence/confluence-data/backups/ |grep backup-| head -n 1 |awk '{print $9}'`

filename_pre="confluence_data_"
date=`date '+%Y%m%d'`
filename_date=`date -d  ${date} +%w`
filename_post=".zip"
filename_upload="${filename_pre}${filename_date}${filename_post}"

cp ${filename} ${filename_upload}
sshpass -p B+uxUk6o rsync -e 'ssh -p 60022' -av ${filename_upload}  sys.confluence@**************::sys.confluence
rm -rf ${filename_upload}

#jira的备份
cd /workspaces/confluence/jira-data/export/
filename=`ls -lt /workspaces/confluence/jira-data/export/ | head -n 2 | tail -n 1 |awk '{print $9}'`

filename_pre="jira_data_"
date=`date '+%Y%m%d'`
filename_date=`date -d  ${date} +%w`
filename_post=".zip"
filename_upload="${filename_pre}${filename_date}${filename_post}"

cp ${filename} ${filename_upload}
sshpass -p B+uxUk6o rsync -e 'ssh -p 60022' -av ${filename_upload}  sys.confluence@**************::sys.confluence
rm -rf ${filename_upload}

#jira的附件备份
cd /workspaces/confluence/jira-data/data/attachments
date=`date '+%Y%m%d'`
filename_date=`date -d  ${date} +%w`
tar_file=${filename_date}_jira_attachment.tar.gz
tar zcvf ${tar_file} /workspaces/confluence/jira-data/data/attachments
sshpass -p B+uxUk6o rsync -e 'ssh -p 60022' -av ${tar_file}  sys.confluence@**************::sys.confluence
rm ${tar_file}
#设置pgsql的密码环境变量
export PGPASSWORD="Aksystem"
