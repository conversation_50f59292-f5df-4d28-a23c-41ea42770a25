<?php

// tiandy用户关联AK设备型号数量统计
date_default_timezone_set("PRC");

const STATIS_FILE = "tiandy_market_statistics.csv";

shell_exec("touch ". STATIS_FILE);
chmod(STATIS_FILE, 0777);

function getDB()
{
    $dbhost = "*************";
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $dbport = 3308;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

function statisWrite($content)
{
    file_put_contents(STATIS_FILE, $content, FILE_APPEND);
    file_put_contents(STATIS_FILE, "\n", FILE_APPEND);
}

function appendContent($versionModelInfo)
{
    foreach ($versionModelInfo as $key => $value) {
        echo "VersionName = {$value["VersionName"]}, count = {$value["Count"]}  \n";
        statisWrite("{$value["VersionName"]},{$value["Count"]}");
    }
}

function getTiandyPerStatistics($db)
{
    $sth = $db->prepare("select VersionModel.VersionName, COUNT(*) AS Count from PersonalDevices join VersionModel on SUBSTRING_INDEX(PersonalDevices.Firmware, '.', 1) = VersionModel.VersionNumber 
                        where PersonalDevices.Node IN (select PersonalAccount.Account from PersonalAccount where PersonalAccount.UUID in ( select TiandyAccount.NodeUUID from TiandyAccount) and PersonalAccount.role = 10) group by VersionModel.VersionName");
    $sth->execute();
    $perDevice = $sth->fetchALL(PDO::FETCH_ASSOC);
    statisWrite("personnel device");
    statisWrite("Model, Count");
    appendContent($perDevice);
}	

function getTiandyCommunityStatistics($db)
{
    // 查询社区公共设备
    $sth = $db->prepare("select VM.VersionName, count(*) as Count from Devices D join VersionModel VM on SUBSTRING_INDEX(D.Firmware, '.', 1) = VM.VersionNumber
                        where D.MngAccountID IN (select ParentID FROM PersonalAccount where UUID IN (select NodeUUID FROM TiandyAccount) and role = 20) and D.Grade in (1, 2) group by VM.VersionName");
    $sth->execute();
    $communityPubDevice = $sth->fetchALL(PDO::FETCH_ASSOC);
    statisWrite("community public device");
    statisWrite("Model, Count");
    appendContent($communityPubDevice);
    file_put_contents(STATIS_FILE, "\n", FILE_APPEND);

    // 查询社区apt内设备
    $sth = $db->prepare("select VM.VersionName, count(*) as Count from Devices D join VersionModel VM on SUBSTRING_INDEX(D.Firmware, '.', 1) = VM.VersionNumber 
                        where D.Node in (select Account from PersonalAccount where UUID in (select NodeUUID From TiandyAccount)) group by VM.VersionName");
    $sth->execute();
    $communityAptDevice = $sth->fetchALL(PDO::FETCH_ASSOC);
    statisWrite("community apt device");
    statisWrite("Model, Count");
    appendContent($communityAptDevice);
    file_put_contents(STATIS_FILE, "\n", FILE_APPEND);
}

function getTiandyMarketStatistics()
{
    $db = getDB();
    getTiandyPerStatistics($db);
    getTiandyCommunityStatistics($db);
}

getTiandyMarketStatistics();