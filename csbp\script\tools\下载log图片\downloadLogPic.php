<?php

require_once __DIR__ . '/oss/aliyun.phar';
require_once __DIR__ . '/oss/aws.phar';

use Aws\S3\S3Client;
use Aws\Exception\AwsException;
use OSS\OssClient;
use OSS\Core\OssException;

class S3Handle
{
    private $cfg;
    public function __construct()
    {
        $this->cfg = $this->InitS3Cfg();
    }
    private function InitS3Cfg()
    {
        $cfg = parse_ini_file("/etc/oss_install.conf");
        if ($cfg['IS_AWS']) {
            try {
                $credentials = new \Aws\Credentials\Credentials($cfg['User'], $cfg['Password']);
                $s3 = new \Aws\S3\S3Client([
                    'version'     => 'latest',
                    'region'      => $cfg['RegionID'],
                    'credentials' => $credentials
                ]);
                $cfg["s3"] = $s3;
            } catch (\Exception $exception) {
                echo "Init S3 Client Error:" . $exception->getMessage();
                throw $exception;
            }
        } else {
            try {
                $oss = new OssClient($cfg['User'], $cfg['Password'], $cfg['Endpoint']);
                $cfg["oss"] = $oss;
            } catch (OssException $exception) {
                echo "Init OSS Client Error:" . $exception->getMessage();
                throw $exception;
            }
        }
        return $cfg;
    }
    public function DownloadFile($filepath, $localfile)
    {
        $cfg = $this->cfg;
        if ($cfg['IS_AWS']) {
            try {
                $file = $cfg['s3']->getObject([
                    'Bucket' => $cfg['BucketForPic'],
                    'Key' => $filepath
                ]);
                $body = $file->get('Body');
                file_put_contents($localfile, $body->getContents());
            } catch (\Exception $exception) {
                echo "Failed to download $filepath  with error: " . $exception->getMessage();
                throw $exception;
            }
        } else {
            $options = array(
                OssClient::OSS_FILE_DOWNLOAD => $localfile
            );
            try {
                $cfg['oss']->getObject($cfg['BucketForPic'], $filepath, $options);
            } catch (OssException $exception) {
                echo "Failed to download $filepath  with error: " . $exception->getMessage();
                throw $exception;
            }
        }
    }
    public function UploadFile($remote_path, $localfile, $inner2Outer = true)
    {
        $cfg = $this->cfg;
        if ($cfg['IS_AWS']) {
            try {
                $cfg['s3']->putObject([
                    'Bucket' => $cfg['BucketForLog'],
                    'Key' => $remote_path,
                    'SourceFile' => $localfile,
                ]);
                $cmd = $cfg['s3']->getCommand('GetObject', [
                    'Bucket' => $cfg['BucketForLog'],
                    'Key' => $remote_path
                ]);
                $request = $cfg['s3']->createPresignedRequest($cmd, '+604800 second');
                $presignedUrl = (string)$request->getUri();
                if ($inner2Outer) {
                    $presignedUrl = $this->innerUrl2OuterUrl($presignedUrl);
                }
                return $presignedUrl;
            } catch (\Exception $exception) {
                echo "Failed to Upload S3 File $localfile  with error: " . $exception->getMessage();
                throw $exception;
            }
        } else {
            try {
                $cfg['oss']->uploadFile($cfg['BucketForLog'], $remote_path, $localfile);
                $signedUrl = $cfg['oss']->signUrl($cfg['BucketForLog'], $remote_path, 604800, "GET", "");
                if ($inner2Outer) {
                    $signedUrl = $this->innerUrl2OuterUrl($signedUrl);
                }
                return $signedUrl;
            } catch (OssException $exception) {
                echo "Failed to Upload OSS File $localfile  with error: " . $exception->getMessage();
                throw $exception;
            }

        }
    }

    /**
     * @description: 内网地址转为外网地址
     * @param {string} $url
     * @return array|string|string[]
     * @author: csc 2023/10/17 9:30 V6.7.0
     * @lastEditors: csc 2023/10/17 9:30 V6.7.0
     */
    public function innerUrl2OuterUrl($url)
    {
        $url = str_replace("http:", "https:", $url);
        $url = str_replace("%2F", "/", $url);
        #替换为外网地址
        $url = str_replace("oss-eu-central-1-internal.aliyuncs.com", "oss-eu-central-1.aliyuncs.com", $url);
        $url = str_replace("oss-ap-southeast-1-internal.aliyuncs.com", "oss-ap-southeast-1.aliyuncs.com", $url);
        $url = str_replace("oss-us-west-1-internal.aliyuncs.com", "oss-us-west-1.aliyuncs.com", $url);
        $url = str_replace("oss-cn-shenzhen-internal.aliyuncs.com", "oss-cn-shenzhen.aliyuncs.com", $url);
        return $url;
    }
}
if ($argc != 2) {
    echo("downloadLogPic: php downloadLogPic.php <picurl> \n");
    exit(1);
}


$picurl = $argv[1];
$fileName = basename($file);

echo "picurl = $picurl, filename = $filename";

$S3Handle = new S3Handle();
$S3Handle->DownloadFile($picurl, $fileName);