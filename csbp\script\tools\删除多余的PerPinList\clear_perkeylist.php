<?php
function getDB()
{
    $dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $dbport = 3306;
    
    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

$STATIS_FILE="Delete_PersonalPrivateKeyList.txt";
function TRACE($content)
{  
	global $STATIS_FILE; 
	@file_put_contents($STATIS_FILE, $content, FILE_APPEND);
	@file_put_contents($STATIS_FILE, "\n", FILE_APPEND);
}

TRACE("Begin");
$db = getDB();

$ids = array();

//Office
$sth = $db->prepare("select P.*,D.MngAccountID from PersonalPrivateKeyList P left join Devices D on D.Mac=P.Mac left join Account A on A.ID=D.MngAccountID where A.Grade=23;");
$sth->execute();
$list = $sth->fetchALL(PDO::FETCH_ASSOC);
foreach($list as $key => $value){
    $mac = $value["MAC"];
    $ID = $value["ID"];
    $relay = $value["Relay"];
    $keyid = $value["KeyID"];
    $SecurityRelay = $value["SecurityRelay"];
    array_push($ids, $ID);
    TRACE("$ID,$mac,$relay,$keyid,$SecurityRelay");
}

//Community
$sth = $db->prepare("select P.*,D.MngAccountID from PersonalPrivateKeyList P left join Devices D on D.Mac=P.Mac left join CommunityInfo C on C.AccountID=D.MngAccountID where C.IsNew=1;");
$sth->execute();
$list = $sth->fetchALL(PDO::FETCH_ASSOC);
foreach($list as $key => $value){
    $mac = $value["MAC"];
    $ID = $value["ID"];
    $relay = $value["Relay"];
    $keyid = $value["KeyID"];
    $SecurityRelay = $value["SecurityRelay"];
    array_push($ids, $ID);
    TRACE("$ID,$mac,$relay,$keyid,$SecurityRelay");
       
}

$id_list = array_chunk($ids, 200);
foreach($id_list as $value){
    #echo(implode("','", $value));
    $sth = $db->prepare("delete From PersonalPrivateKeyList where ID in ('".implode("','", $value)."')");
    $sth->execute();   
}












