<?php

function getDB2()
{
    $dbhost = "*************";
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $dbport = 3306;
    
    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

function shua_mac()
{
    $db = getDB2();

    $sth = $db->prepare("select MngAccountID,count(*) as count  from Devices where binary MAC regexp '[a-z]' group by MngAccountID;");
    $sth->execute();

    $comm = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($comm as $key => $val) {
        $mngid=$val["MngAccountID"];
        $count=$val["count"];
        $sth = $db->prepare("select count(*) as count  from Devices where MngAccountID=:MngAccountID;");
        $sth->bindParam(':MngAccountID', $mngid, PDO::PARAM_STR);
        $sth->execute();
        $count2 = $sth->fetch(PDO::FETCH_ASSOC)["count"];

        $sth = $db->prepare("select count(*) as count  from PersonalAccount where ParentID=:MngAccountID and Role in (20, 30,31,40) and Active=1;");
        $sth->bindParam(':MngAccountID', $mngid, PDO::PARAM_STR);
        $sth->execute();
        $count3 = $sth->fetch(PDO::FETCH_ASSOC)["count"];

        echo  "communityId: $mngid low_mac_count:$count all_community_count:$count2 Per:$count3.\n";
    }
}

shua_mac();
