<?php

function getDB2()
{
    $dbhost = "*************";
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $dbport = 3306;
    
    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

function shua_mac()
{
    $db = getDB2();

    $sth = $db->prepare("select Node  from PersonalDevices where binary MAC regexp '[a-z]';");
    $sth->execute();

    $comm = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($comm as $key => $val) {
        $Node=$val["Node"];

        echo"Node: $Node\n";
    }
}

shua_mac();
