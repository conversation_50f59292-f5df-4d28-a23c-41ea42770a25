<?php
require_once(dirname(__FILE__) . '/adapt_define.php');
require_once(dirname(__FILE__) . '/socket.php');

if ($argc != 2) {
    echo "usage xxx communityid\n";
    exit(1);
}
$community_id = $argv[1];
echo "update communityid: $community_id \n";

function getDB2()
{
    $dbhost = "*************";
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $dbport = 3306;
    
    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}


function ImportComm($community_id)
{
    $data = array();
    $data[0] = 5004;
    $data[1] = "";
    $data[2] = "";
    $data[3] = $community_id;
    $data[4] = 0;

    $Socket = new CWebCommunityModifyNotifySocket();
    $Socket->setMsgID(MSG_P2A_NOTIFY_COMMUNITY_MESSAGE);
    $Socket->copy($data);
}

$db = getDB2();

$db->beginTransaction();

$sth = $db->prepare("update DevicesSpecial set MAC=UPPER(MAC) where MAC  in (select Mac from Devices where binary MAC regexp '[a-z]' and MngAccountID=:MngAccountID);");
$sth->bindParam(':MngAccountID', $community_id, PDO::PARAM_INT);
$sth->execute();

$sth = $db->prepare("update PendingRegUser set MAC=UPPER(MAC) where MAC in  (select Mac from Devices where binary MAC regexp '[a-z]' and MngAccountID=:MngAccountID); ");
$sth->bindParam(':MngAccountID', $community_id, PDO::PARAM_INT);
$sth->execute();

$sth = $db->prepare("update DevicesShadow  set MAC=UPPER(MAC) where MAC in  (select Mac from Devices where binary MAC regexp '[a-z]' and MngAccountID=:MngAccountID);");
$sth->bindParam(':MngAccountID', $community_id, PDO::PARAM_INT);
$sth->execute();

$sth = $db->prepare("update AccessGroupDevice  set MAC=UPPER(MAC) where MAC in  (select Mac from Devices where binary MAC regexp '[a-z]' and MngAccountID=:MngAccountID);");
$sth->bindParam(':MngAccountID', $community_id, PDO::PARAM_INT);
$sth->execute();

$sth = $db->prepare("update FaceModel  set MAC=UPPER(MAC) where MAC in  (select Mac from Devices where binary MAC regexp '[a-z]' and MngAccountID=:MngAccountID); ");
$sth->bindParam(':MngAccountID', $community_id, PDO::PARAM_INT);
$sth->execute();

$sth = $db->prepare("update PersonalAppTmpKeyList set MAC=UPPER(MAC)  where MAC in  (select Mac from Devices where binary MAC regexp '[a-z]' and MngAccountID=:MngAccountID); ");
$sth->bindParam(':MngAccountID', $community_id, PDO::PARAM_INT);
$sth->execute();

$sth = $db->prepare("update PersonalPrivateKeyList set MAC=UPPER(MAC)  where MAC in  (select Mac from Devices where binary MAC regexp '[a-z]' and MngAccountID=:MngAccountID);");
$sth->bindParam(':MngAccountID', $community_id, PDO::PARAM_INT);
$sth->execute();

$sth = $db->prepare("update PersonalRfcardKeyList set MAC=UPPER(MAC)  where MAC in  (select Mac from Devices where binary MAC regexp '[a-z]' and MngAccountID=:MngAccountID); ");
$sth->bindParam(':MngAccountID', $community_id, PDO::PARAM_INT);
$sth->execute();

$sth = $db->prepare("update PubAppTmpKeyList set MAC=UPPER(MAC)  where MAC in  (select Mac from Devices where binary MAC regexp '[a-z]' and MngAccountID=:MngAccountID); ");
$sth->bindParam(':MngAccountID', $community_id, PDO::PARAM_INT);
$sth->execute();

$sth = $db->prepare("update PubPrivateKeyList set MAC=UPPER(MAC)  where MAC in  (select Mac from Devices where binary MAC regexp '[a-z]' and MngAccountID=:MngAccountID); ");
$sth->bindParam(':MngAccountID', $community_id, PDO::PARAM_INT);
$sth->execute();

$sth = $db->prepare("update PubRfcardKeyList set MAC=UPPER(MAC)  where MAC in  (select Mac from Devices where binary MAC regexp '[a-z]' and MngAccountID=:MngAccountID); ");
$sth->bindParam(':MngAccountID', $community_id, PDO::PARAM_INT);
$sth->execute();

$sth = $db->prepare("update UserAccessGroupDevice set MAC=UPPER(MAC)  where MAC in  (select Mac from Devices where binary MAC regexp '[a-z]' and MngAccountID=:MngAccountID); ");
$sth->bindParam(':MngAccountID', $community_id, PDO::PARAM_INT);
$sth->execute();

$sth = $db->prepare("update Devices set MAC=UPPER(MAC)  where  MngAccountID=:MngAccountID");
$sth->bindParam(':MngAccountID', $community_id, PDO::PARAM_INT);
$sth->execute();

$db->commit();

#ImportComm($community_id);
