<?php
require_once(dirname(__FILE__) . '/adapt_define.php');
require_once(dirname(__FILE__) . '/socket.php');

if ($argc != 2) {
    echo "usage xxx node\n";
    exit(1);
}
$node = $argv[1];
echo "update node: $node \n";

function getDB2()
{
    $dbhost = "127.0.0.1";
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $dbport = 3306;
    
    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

function PersonalModifyNotify($node)
{
    $data[0] = 1001;
    $data[1] = $node;
    $data[2] = "";
    $data[3] = 0;

    $Socket = new CWebPersonalModifyNotifySocket();
    $Socket->setMsgID(MSG_P2A_NOTIFY_PERSONAL_MESSAGE);
    $Socket->copy($data);
}


$db = getDB2();

$db->beginTransaction();

$sth = $db->prepare("update DevicesSpecial set MAC=UPPER(MAC) where MAC  in (select Mac from PersonalDevices where binary MAC regexp '[a-z]' and Node=:Node);");
$sth->bindParam(':Node', $node, PDO::PARAM_STR);
$sth->execute();

$sth = $db->prepare("update PendingRegUser set MAC=UPPER(MAC) where MAC in  (select Mac from PersonalDevices where binary MAC regexp '[a-z]' and Node=:Node); ");
$sth->bindParam(':Node', $node, PDO::PARAM_STR);
$sth->execute();

$sth = $db->prepare("update DevicesShadow  set MAC=UPPER(MAC) where MAC in  (select Mac from PersonalDevices where binary MAC regexp '[a-z]' and Node=:Node);");
$sth->bindParam(':Node', $node, PDO::PARAM_STR);
$sth->execute();

$sth = $db->prepare("update FaceModel  set MAC=UPPER(MAC) where MAC in  (select Mac from PersonalDevices where binary MAC regexp '[a-z]' and Node=:Node); ");
$sth->bindParam(':Node', $node, PDO::PARAM_STR);
$sth->execute();

$sth = $db->prepare("update PersonalAppTmpKeyList set MAC=UPPER(MAC)  where MAC in  (select Mac from PersonalDevices where binary MAC regexp '[a-z]' and Node=:Node); ");
$sth->bindParam(':Node', $node, PDO::PARAM_STR);
$sth->execute();

$sth = $db->prepare("update PersonalPrivateKeyList set MAC=UPPER(MAC)  where MAC in  (select Mac from PersonalDevices where binary MAC regexp '[a-z]' and Node=:Node);");
$sth->bindParam(':Node', $node, PDO::PARAM_STR);
$sth->execute();

$sth = $db->prepare("update PersonalRfcardKeyList set MAC=UPPER(MAC)  where MAC in  (select Mac from PersonalDevices where binary MAC regexp '[a-z]' and Node=:Node); ");
$sth->bindParam(':Node', $node, PDO::PARAM_STR);
$sth->execute();


$sth = $db->prepare("update PersonalDevices set MAC=UPPER(MAC)  where  Node=:Node");
$sth->bindParam(':Node', $node, PDO::PARAM_STR);
$sth->execute();

$db->commit();

PersonalModifyNotify($node);
