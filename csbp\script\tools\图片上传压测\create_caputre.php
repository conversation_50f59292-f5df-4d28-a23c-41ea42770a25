<?php

date_default_timezone_set("PRC");
function getLogDB()
{
    $dbhost = "logdb.dev.akcs.inner";
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "LOG";
    $dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

function getDB()
{
    $dbhost = "app1.dev-cloud.akcs.inner";
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

function getHashUUID($mac)
{
    $db = getDB();

    $sth = $db->prepare("select AccountUUID from Devices where MAC = :mac");
    $sth->bindValue(':mac', $mac, PDO::PARAM_STR);

    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);

    if ($result)
    {
        return $result['AccountUUID'];
    }
    
    $sth = $db->prepare("select A.UUID from PersonalAccount A left join PersonalDevices D on D.Node = A.Account where D.MAC = :mac");
    $sth->bindValue(':mac', $mac, PDO::PARAM_STR);

    $sth->execute();
    $result = $sth->fetch(PDO::FETCH_ASSOC);
    if ($result)
    {
        return $result['UUID'];
    }

    return "";
}

//获取分表索引
function getLogDbTableIndex($hash_uuid, $delivery)
{
    $hash = sprintf('%u', crc32($hash_uuid));
    return $hash%$delivery;
}

function cmd_usage($cmd)
{
    echo("usage: php ". $cmd . " <mac> <pic_count> <time> <round>\n");
    echo("mac: 设备MAC,设备需要先加到云上  \n");
    echo("pic_count: 每一轮上传的图片文件数量  \n");
    echo("time: 每一轮之间的间隔时长,单位秒\n");
    echo("round: 轮数\n");
    exit(0);
}

if ($argc != 5)
{
    cmd_usage($argv[0]);
}

$mac = $argv[1];
$pic_count = $argv[2];
$time = $argv[3];
$round = $argv[4];

$hash_uuid = getHashUUID($mac);
if (!$hash_uuid)
{
    echo ("mac not bind.\n");
    exit(0);
}
$table_index = getLogDbTableIndex($hash_uuid, 8);

$i=0;
$log_db = getLogDB();
for ($i; $i < $round; $i++)
{
    $timestamp = time();
    $md5values=md5("ak_ftp:". $mac . ":" . $timestamp);
    
    for ($j=0; $j < $pic_count; $j++)
    {
        $pic_m= $mac . "-" . $timestamp . "_" . $j . "_MotionDev_" . $md5values . ".jpg-IP-::ffff:************";
        $pic= $mac . "-". $timestamp . "_" . $j . "_DoorDev_" . $md5values . ".jpg-IP-::ffff:************";

        $log_db = getLogDB();
        $sth = $log_db->prepare("insert into PersonalCapture_" . $table_index . " (MAC,PicName,CaptureTime) values(:mac,:pic,'2022-06-28 13:46:02')");
        $sth->bindParam(":mac", $mac, PDO::PARAM_STR);
        $sth->bindParam(":pic", $pic, PDO::PARAM_STR);
        $sth->execute();

        $db = getDB();
        $sth = $log_db->prepare("insert into PersonalMotion_" . $table_index . "(MAC,PicName,CaptureTime) values(:mac, :pic, '2022-06-28 13:46:02')");
        $sth->bindParam(":mac", $mac, PDO::PARAM_STR);
        $sth->bindParam(":pic", $pic_m, PDO::PARAM_STR);
        $sth->execute();

        $cmd="cp test.jpg /usr/local/akcs/csstorage/ftp/data/".$pic_m;
        exec($cmd);

        $cmd="cp test.jpg /usr/local/akcs/csstorage/ftp/data/".$pic;
        exec($cmd); 
    }
    sleep($time);
}


?>
