<?php

class MYAES128CBC
{
    const METHOD = 'AES-128-CBC';
    //added by cheny<PERSON>,新建用户,二维码发送加密秘钥
    const QRCODE_SECRET_KEY = 'Akuvox55069013Akuvox';
    const FACE_MNG_KEY = 'ak202008';
    const AES_IV = '0000000000000000';
    /**
     * Define the number of blocks that should be read from the source file for each chunk.
     * For 'AES-128-CBC' each block consist of 16 bytes.
     * So if we read 10,000 blocks we load 160kb into memory. You may adjust this value
     * to read/write shorter or longer chunks.
     */
    const FILE_ENCRYPTION_BLOCKS = 10000;

    public function encrypt($text)
    {
        $iv = self::AES_IV;
        return openssl_encrypt($text, self::METHOD, self::QRCODE_SECRET_KEY . $text, 0, $iv);
    }

    public function decrypt($text)
    {
        $iv = self::AES_IV;
        $opensslDecrypt = openssl_decrypt($text, self::METHOD, self::QRCODE_SECRET_KEY . $text, 0, $iv);
        return $opensslDecrypt;
    }

    public function getKey($account)
    {
        return substr($account . self::FACE_MNG_KEY, 0, 16);
    }
    /**
     * Encrypt the passed file and saves the result in a new file with ".enc" as suffix.
     *
     * @param string $source Path to file that should be encrypted
     * @param string $key    The key used for the encryption
     * @param string $dest   File name where the encryped file should be written to.
     * @return string|false  Returns the file name that has been created or FALSE if an error occured
     */
    public function encryptFile($source, $key, $dest)
    {
        $key = substr(sha1($key, false), 0, 16);
        $iv = self::AES_IV;

        $error = false;
        if ($fpOut = fopen($dest, 'w')) {
            if ($fpIn = fopen($source, 'rb')) {
                $plaintext = fread($fpIn, ceil(filesize($source) / 128));
                $ciphertext = openssl_encrypt($plaintext, self::METHOD, $key, OPENSSL_RAW_DATA, $iv);
                fwrite($fpOut, $ciphertext);
                fclose($fpIn);
            } else {
                $error = true;
            }
            fclose($fpOut);
        } else {
            $error = true;
        }

        return $error ? false : $dest;
    }

    /**
     * Dencrypt the passed file and saves the result in a new file, removing the
     * last 4 characters from file name.
     *
     * @param string $source Path to file that should be decrypted
     * @param string $key    The key used for the decryption (must be the same as for encryption)
     * @param string $dest   File name where the decryped file should be written to.
     * @return string|false  Returns the file name that has been created or FALSE if an error occured
     */
    public function decryptFile($source, $key, $dest)
    {
        $key = substr(sha1($key, false), 0, 16);
        $iv = self::AES_IV;

        $error = false;
        if ($fpOut = fopen($dest, 'w')) {
            if ($fpIn = fopen($source, 'rb')) {
                $ciphertext = fread($fpIn, filesize($source));
                $plaintext = openssl_decrypt($ciphertext, self::METHOD, $key, OPENSSL_RAW_DATA, $iv);
                fwrite($fpOut, $plaintext);
                fclose($fpIn);
            } else {
                $error = true;
            }
            fclose($fpOut);
        } else {
            $error = true;
        }

        return $error ? false : $dest;
    }
}

function getDB()
{
	$dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
	$dbport = 3306;
    
    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

function getUserPicMap($mng_account_id)
{
    $db = getDB();
    $sth = $db->prepare("SELECT P.Account,F.FaceUrl FROM FaceMng F left join PersonalAccount P on P.ID = F.PersonalAccountID where F.MngAccountID = :mng_account_id");
    $sth->bindParam(':mng_account_id', $mng_account_id, PDO::PARAM_STR);
    
    $ret = $sth->execute();
    $userPicMap = array();  // 创建一个空的关联数组

    // 通过 fetch 函数遍历查询结果，将每一行的 Node 和 FaceUrl 添加到关联数组中
    while ($resultToken = $sth->fetch(PDO::FETCH_ASSOC)) {
        $userPicMap[$resultToken['Account']] = $resultToken['FaceUrl'];
    }

    return $userPicMap;  // 返回关联数组
}

function movePic($userPicMap, &$userPicMap2)
{
    //遍历userPicMap，拼接实际存储的Url
    foreach ($userPicMap as $node => $faceUrl) {
        $newFaceUrl =  preg_replace('/\/group\d\/\w+\//', '/home/<USER>/data/', $faceUrl);
        $userPicMap[$node] = $newFaceUrl;
    }

    //创建一个存放图片的文件夹
    $dir1 = 'face_pic';
    if (!file_exists($dir1)) {
        mkdir($dir1);
        echo "Directory created successfully!";
    } else {
        echo "Directory already exists!";
    }

    //创建一个存放解密后图片的文件夹
    $dir2 = 'decrypt_face_pic';
    if (!file_exists($dir2)) {
        mkdir($dir2);
        echo "Directory created successfully!";
    } else {
        echo "Directory already exists!";
    }

    //切换目录
    chdir($dir1);

    //复制图片到指定文件夹
    foreach ($userPicMap as $node => $faceUrl) {
        $source = $faceUrl;
        $dest = basename($faceUrl);
        $userPicMap2[$node] = $dest;
        copy($source, $dest);
    }
}

function decryptPic($userPicMap)
{
    $aes = new MYAES128CBC;
    foreach ($userPicMap as $node => $faceUrl) {
        $key = $aes->getKey($node);
        $decrypt_pic = "decrypt-". $faceUrl;
        $ret = $aes->decryptFile($faceUrl, $key, $decrypt_pic);
        $targetFile = '../decrypt_face_pic/' . $decrypt_pic;
        rename($decrypt_pic, $targetFile);
    }
}

function getUserPicTar($mng_account_id)
{
    //获取Node和FaceUrl的映射关系
    $userPicMap = getUserPicMap($mng_account_id);

    //拷贝图片到face_pic
    $userPicMap2 = array();
    movePic($userPicMap, $userPicMap2);

    //对图片进行解密
    decryptPic($userPicMap2);

    //切换目录
    chdir('../');

    $command = 'tar -czvf decrypt_face_pic.tar.gz decrypt_face_pic';
    exec($command);
}

//使用说明：① 网页批量导入人脸 ② php decrypt_face_pic.php $mng_account_id ③解密后的图片在decrypt_face_pic.tar.gz
$mng_account_id = $argv[1];
getUserPicTar($mng_account_id);