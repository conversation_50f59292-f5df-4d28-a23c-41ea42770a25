<?php
$xml = file_get_contents('TimeZone.xml');
$dom = new DOMDocument();
$dom->loadXML($xml);

$dsts = $dom->getElementsByTagName('DST');

$res = '';
foreach ($dsts as $dst) {
  if ($dst->hasAttribute('Type')) {
    $type = $dst->getAttribute('Type');
    $time = $dst->getAttribute('TimeZone');
    $zone = $dst->getAttribute('Zone');
    $timeZone = "$time ".explode('/', $zone)[1];
    $start = $dst->getAttribute('Start');
    $end = $dst->getAttribute('End');
    $offset = $dst->getAttribute('Offset');
    // 获取其他属性值...
    $res .= "\"$timeZone\"=>[\"Type\"=>\"$type\", \"Start\"=>\"$start\", \"End\"=>\"$end\", \"Offset\"=>\"$offset\"],\n";
  }
}
echo $res;
