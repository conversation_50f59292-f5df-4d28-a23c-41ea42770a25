<?php
ini_set('memory_limit', '256M');
date_default_timezone_set('PRC');

function getDB()
{
    $dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";

    $mysql_conn_string = "mysql:host=$dbhost;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

function getLogDB()
{
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "LOG";
    $dbhost = "*************";
        $dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

function getMngAccountIDList($db, $limit, $offset)
{
    $sth = $db->prepare("SELECT MngAccountID FROM CallHistory_0_202408 LIMIT :limit OFFSET :offset");
    $sth->bindParam(':limit', $limit, PDO::PARAM_INT);
    $sth->bindParam(':offset', $offset, PDO::PARAM_INT);
    $sth->execute();
    return $sth->fetchAll(PDO::FETCH_ASSOC);
}

function getCommunityCallStatics()
{
    $db = getDB();
    $logDb = getLogDB();

    $count = 0;
    $limit = 20000; // 每次查询的记录数
    $offset = 0;    // 查询的起始位置

    do {
        // 获取一批数据
        $mngAccountList = getMngAccountIDList($logDb, $limit, $offset);

        // 处理获取到的数据
        foreach ($mngAccountList as $mngAccount) {
            $sth = $db->prepare("SELECT Grade FROM Account WHERE ID=:mngAccount");
            $sth->bindParam(':mngAccount', $mngAccount['MngAccountID'], PDO::PARAM_STR);
            $sth->execute();
            $ret = $sth->fetch(PDO::FETCH_ASSOC);
            if ($ret['Grade'] == 21) {
                $count++;
                echo "count = $count \n";
            }
        }

        // 增加 offset 以获取下一批数据
        $offset += $limit;

    } while (count($mngAccountList) > 0); // 如果获取到的数据条数为 0，终止循环

    echo "final count = $count ";
}

getCommunityCallStatics();
                        