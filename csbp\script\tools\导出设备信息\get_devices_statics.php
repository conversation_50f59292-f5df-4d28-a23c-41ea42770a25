<?php
date_default_timezone_set('PRC');

const STATIS_TOTAL_FILE = "/tmp/device_statics.csv";
shell_exec("touch ". STATIS_TOTAL_FILE);

chmod(STATIS_TOTAL_FILE, 0777);
if (file_exists(STATIS_TOTAL_FILE)) {
    shell_exec("echo > ". STATIS_TOTAL_FILE);
} 

function STATIS_WRITE($content)
{
	file_put_contents(STATIS_TOTAL_FILE, $content, FILE_APPEND);
	file_put_contents(STATIS_TOTAL_FILE, "\n", FILE_APPEND);
}

function getDB()
{
    $dbhost = "127.0.0.1";
    $dbuser = "dbread";
    $dbpass = "ohfae@thie0c+e3I";
    $dbname = "AKCS";

    $mysql_conn_string = "mysql:host=$dbhost;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

function getDevices($db, $firmware)
{
	$sth = $db->prepare("select C.MAC,C.Firmware as Firmware, A.Account as Distributor,C.Community as Installer,'' as Community from Account A left join Account B on B.parentID = A.ID left join PersonalDevices C on B.Account = C.Community where C.Firmware like :firmware 

union all 

select D.MAC, D.Firmware as Firmware , A.Account as Distributor, B.Account as Installer ,C.Location as Community from Account A left join Account B on B.ParentID = A.ID left join Account C on B.ManageGroup = C.ManageGroup left join Devices D on D.MngAccountID = C.ID where D.Firmware like :firmware and B.Grade=22 and B.Role = 4 order by Firmware");
	$sth->bindParam(':firmware', $firmware, PDO::PARAM_STR);
	$sth->execute();
	$ret = $sth->fetchAll(PDO::FETCH_ASSOC);
	return $ret;
}

function writeCSV($devicesList)
{
    foreach ($devicesList as $row => $dev) {
		$dis = $dev['Distributor'];
		$ins = $dev['Installer'];
		$comm = $dev['Community'];
		$fr = $dev['Firmware'];
		$mac = $dev['MAC'];
		$static_str = "$mac, $fr, $dis, $ins, $comm";
		STATIS_WRITE($static_str);
	}
}

function getDevicesByFirmware($firmwareList)
{
	$db = getDB();
	foreach ($firmwareList as $firmware) {
        $devices = getDevices($db, $firmware);
        writeCSV($devices);
    }
}

$firmwareList = ["20.%","220.%","320.%","12.%","312.%","29.%","915.%","2915.%","916.%","539.%","933.%","113.%","213.%","212.%","115.%","117.%","119.%","83.%","88.%","567.%","101.%","101.%","103.%","105.%","116.%","216.%","18.%","94.%","912.%"];

$static_str ='MAC, Firmware,Distributor,Installer,Community';

STATIS_WRITE($static_str);
getDevicesByFirmware($firmwareList);
