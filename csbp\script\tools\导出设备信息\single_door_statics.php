<?php
date_default_timezone_set('PRC');

const STATIS_TOTAL_FILE = "/tmp/device_statics.csv";
shell_exec("touch ". STATIS_TOTAL_FILE);

chmod(STATIS_TOTAL_FILE, 0777);
if (file_exists(STATIS_TOTAL_FILE)) {
    shell_exec("echo > ". STATIS_TOTAL_FILE);
} 

function STATIS_WRITE($content)
{
	file_put_contents(STATIS_TOTAL_FILE, $content, FILE_APPEND);
	file_put_contents(STATIS_TOTAL_FILE, "\n", FILE_APPEND);
}

function getDB()
{
    $dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";

    $mysql_conn_string = "mysql:host=$dbhost;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

function getNodeInfo($db)
{
	$sth = $db->prepare("select Account from PersonalAccount where Role=10");
	$sth->execute();
	return $sth->fetchAll(PDO::FETCH_ASSOC);
}

function getPersonalDevicesStatics()
{
	$db = getDB();
	$nodeInfo = getNodeInfo($db);

	// key count, value 个数
	$result = array();
	$result[1] = 0;
	$result[2] = 0;
	$result[3] = 0;
	$result[4] = 0;
	foreach ($nodeInfo as $node) {
		$sth = $db->prepare("select count(*) as count from PersonalDevices where Node=:account and Type in (0,1,50)");
		$sth->bindParam(':account', $node['Account'], PDO::PARAM_STR);
		$sth->execute();
		$count = $sth->fetch(PDO::FETCH_ASSOC);
		if ($count['count'] > 0 && $count['count'] <= 3) {
			$result[$count['count']]++;
		} else  {
			$result[4]++;
		}
	}

	foreach ($result as $count => $count1) {
		echo "count = $count ,  count1 = $count1\n";
	}
}


getPersonalDevicesStatics();