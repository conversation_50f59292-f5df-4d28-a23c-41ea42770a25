<?php

class DataConfusion
{
    const METHOD = 'AES-256-CBC';
    const SECRET_KEY = 'Akuvox1956131*69czeahaaew216023*';
    const IV = "0123456789000000";

    public static $instance = null;

    private function __clone()
    {
    }

    public static function getInstance()
    {
        if (!self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    public function encrypt($text)
    {
        if ($text === '' or is_null($text)) {
            return $text;
        }

        $res = openssl_encrypt($text, self::METHOD, self::SECRET_KEY, 0, self::IV);

        return $res;
    }

    public function decrypt($text)
    {
        if ($text === '' or is_null($text)) {
            return $text;
        }

        $res = openssl_decrypt($text, self::METHOD, self::SECRET_KEY, 0, self::IV);

        return $res;
    }
}

function cmd_usage($cmd)
{
    echo("usage: php ". $cmd . " <operation> <data> \n");
    echo("operation: en 加密, de 解密  \n");
    exit(0);
}

//使用说明：① php db_confusion.php $operation $data
if ($argc != 3) {
    cmd_usage($argv[0]);
}

$operation = $argv[1];
$data = $argv[2];

$dataConfusion = DataConfusion::getInstance();

if ($operation === "en") {
    $enData = $dataConfusion->encrypt($data);
    echo "$enData\n";
} elseif ($operation === "de") {
    $deData =$dataConfusion->decrypt($data);
    echo "$deData\n";
} else {
    echo "error operation\n";
}