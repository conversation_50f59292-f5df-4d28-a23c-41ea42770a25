<?php

const LOG_ENCRYPT_SECRET = "mx6pC6U7RFQ8sp38DZMydQJ037yAhVmh";

const IV = "vVhuHEBY4NiccDrj";

// 加密函数
function encryptData($data, $key = LOG_ENCRYPT_SECRET)
{
    return openssl_encrypt($data, 'aes-256-cbc', $key, 0, IV);
}

// 解密函数
function decryptData($encryptedData, $key = LOG_ENCRYPT_SECRET)
{
    return openssl_decrypt($encryptedData, 'aes-256-cbc', $key, 0, IV);
}

function cmd_usage($cmd)
{
    echo("usage: php ". $cmd . " <operation> <data> \n");
    echo("operation: en 加密, de 解密  \n");
    exit(0);
}

//使用说明：① php log_confusion.php $operation $data
if ($argc != 3) {
    cmd_usage($argv[0]);
}

$operation = $argv[1];
$data = $argv[2];

if ($operation === "en") {
    $enData = encryptData($data);
    echo "$enData\n";
} elseif ($operation === "de") {
    $deData = decryptData($data);
    echo "$deData\n";
} else {
    echo "error operation\n";
}