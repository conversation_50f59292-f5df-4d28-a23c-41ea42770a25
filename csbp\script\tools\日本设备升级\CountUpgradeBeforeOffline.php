<?php

date_default_timezone_set('PRC');
const STATIS_FILE = "./UpgradeList.csv";

function get_db_obj()
{
    $PARAM_host='127.0.0.1';
    $PARAM_port='3306';
    $PARAM_db_name='AKCS';
    $PARAM_user='root';
    $PARAM_db_pass='Ak@56@<EMAIL>';

    $dbh = new PDO('mysql:host='.$PARAM_host.';port='.$PARAM_port.';dbname='.$PARAM_db_name, $PARAM_user, $PARAM_db_pass, null);
    $dbh->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbh->query('set names utf8;');
    return $dbh;
}

$file = fopen(STATIS_FILE,"rb");
$macs = array();
$mac_firm = array();
while (!feof($file)) {
    $content = fgets($file);

    $splitStrings = explode(" ", $content);
    if (isset($splitStrings[2])) {
        $mac = $splitStrings[2];
        $mac = trim($mac);
        array_push($macs, $mac);
        if (isset($splitStrings[3])) {
            $fir = $splitStrings[3];
            $fir = trim($fir);
            $mac_firm[$mac] = $fir;
        }
    }

}


$WRITE_FILE = "./upgrade_result.csv";
shell_exec("touch ". $WRITE_FILE);

chmod($WRITE_FILE, 0777);
if (file_exists($WRITE_FILE)) {
    shell_exec("echo > ". $WRITE_FILE);
}
function STATIS_WRITE($content)
{
    global $WRITE_FILE;
    file_put_contents($WRITE_FILE, $content, FILE_APPEND);
    file_put_contents($WRITE_FILE, "\n", FILE_APPEND);
}
$static_str = 'Community,RoomName,Mac,UpgradeFirmware,NowFirmware,Upgrade Success,Online';
STATIS_WRITE($static_str);

$macstr = '"' . implode('","', $macs) . '"';
$db = get_db_obj();
//社区
$sth = $db->prepare("select A.Location,Mac,Status,Firmware,Name From Devices D left join PersonalAccount P on P.Account=D.Node left join Account A on A.ID=P.ParentID  where Mac in ($macstr) order by A.Location;");
$sth->execute();
$mac_list = $sth->fetchALL(PDO::FETCH_ASSOC);

foreach ($mac_list as $row => $mac_info) {
    $mac = $mac_info['Mac'];
    $status = $mac_info['Status'];
    $fw = $mac_info['Firmware'];
    $comm = $mac_info['Location'];
    $name = $mac_info['Name'];
    if ($status == 0)
    {
        echo "offline $mac\n";
    }
    //echo "$comm, $name, $mac, $fw, $status\n";
    $succ = 0;
    $upgrade_fw = $mac_firm[$mac];
    if($upgrade_fw == $fw)
    {
        $succ = 1;
    }
    $static_str =  "$comm,$name,$mac,$upgrade_fw,$fw,$succ,$status";
    STATIS_WRITE($static_str);
}


