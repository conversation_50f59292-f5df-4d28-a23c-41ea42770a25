<?php

date_default_timezone_set('PRC');


$community = ['Oriental Mansion','<PERSON>EP_laurel','<PERSON>EP_soujiji','BEP_sunhillsk','<PERSON>EP_bellemaison','BEP_psmile','<PERSON>EP_heimshino','寺田町ハイツⅠ','寺田町ハイツⅡ','<PERSON>EP_prime','<PERSON>','<PERSON> Imazu','Angle Plaza','BEP_capcon','Excelstage','Triumphal','刈谷第一東海ビル','第一メリーハウス','Community1','E-story黒川','Veldmealp<PERSON>','It<PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','<PERSON><PERSON><PERSON><PERSON>','MLA <PERSON><PERSON><PERSON><PERSON>','Sakusesu21','Amie Grand Court','Mansion tomoro','Verdesakaihigashi','Carudamomo Yagoto Tower','Saintray Hikibune'];

$Firmware=[

    "113.30.4.98" => "113.30.8.106",
    "113.30.4.103" => "113.30.8.106",
    "113.30.4.156" => "113.30.8.106",
    "113.30.4.215" => "113.30.8.106",
    "113.30.6.131" => "113.30.8.106",
    "113.30.6.139" => "113.30.8.106",
    "113.30.7.100" => "113.30.8.106",
    "113.55.4.108" => "113.30.8.106",
    "113.55.4.109" => "113.30.8.106",
    "113.30.4.199" => "113.30.8.106",
    "113.30.4.213" => "113.30.8.106",
    "113.30.8.30" => "113.30.8.106",
    "113.30.8.59" => "113.30.8.106",
    "113.30.8.68" => "113.30.8.106",
    
    "212.30.6.74" => "212.30.8.28",
    "212.30.6.75" => "212.30.8.28",
    "212.30.8.11" => "212.30.8.28",
    "212.30.8.23" => "212.30.8.28",
    "212.30.8.21" => "212.30.8.28",
    "212.87.8.17" => "212.30.8.28",

    "213.30.1.130" => "***********",
    "213.30.8.69" => "***********",
    "213.30.8.30" => "***********",
    "213.30.8.35" => "***********",
    "213.30.8.38" => "***********",
    "213.30.8.44" => "***********",
    "213.30.8.53" => "***********",
    "***********" => "***********",

];

$communitycount = count($community);
$communitylist = '"' . implode('","', $community) . '"';

$WRITE_FILE = "./community_info.csv";
shell_exec("touch ". $WRITE_FILE);

chmod($WRITE_FILE, 0777);
if (file_exists($WRITE_FILE)) {
    shell_exec("echo > ". $WRITE_FILE);
}
function STATIS_WRITE($content)
{
    global $WRITE_FILE;
    file_put_contents($WRITE_FILE, $content, FILE_APPEND);
    file_put_contents($WRITE_FILE, "\n", FILE_APPEND);
}
function getDB()
{
    $dbhost = "127.0.0.1";
    $dbuser = "root";

    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $opts_values = array(PDO::ATTR_PERSISTENT=>true,PDO::ATTR_ERRMODE=>2,PDO::MYSQL_ATTR_INIT_COMMAND=>'SET NAMES utf8');
    $mysql_conn_string = "mysql:host=$dbhost;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass, $opts_values);
    //$dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION, PDO::MYSQL_ATTR_INIT_COMMAND=>'SET NAMES utf8');
    return $dbConnection;
}

$db = getDB();

$static_str = 'Community,MngAccountID,RoomName,Mac,Firmware,UpgradeFirmware,Need Upgrade,Online';
STATIS_WRITE($static_str);


//社区
$sth = $db->prepare("select A.Location,P.Name,D.Mac,D.Firmware,D.Status,D.MngAccountID From Devices D left join PersonalAccount P on P.Account=D.Node left join Account A on P.ParentID=A.ID  left join Account AA on AA.ID=A.ParentID where (AA.Account='DOORCOM' or AA.Account='JTS' ) and (D.Firmware like '113.%' or D.Firmware like '213%' or D.Firmware like '212%') and A.Location in ($communitylist) order by Location,Status;");


$sth->execute();
$mac_list = $sth->fetchALL(PDO::FETCH_ASSOC);

$commids=[];

$real_comm=[];

foreach ($mac_list as $row => $mac_info) {
    $location = $mac_info['Location'];
    
    $mac = $mac_info['Mac'];
    $fw = $mac_info['Firmware'];
    $status = $mac_info['Status'];
    $MngAccountID = $mac_info['MngAccountID'];
    $Name = $mac_info['Name'];
    
    $upgrade_fw = $fw;
    if (array_key_exists($fw, $Firmware)) {
        $upgrade_fw = $Firmware[$fw];
    }    
    $upgrade = 1;
    if ($fw == $upgrade_fw)
    {
        $upgrade = 0;
	continue;
    }
    $commids[] = $MngAccountID;
    $real_comm[] = $location;
    
    $static_str =  "$location,$MngAccountID,$Name,$mac,$fw,$upgrade_fw,$upgrade,$status";
    STATIS_WRITE($static_str);
}

$diff = [];
$real_comm = array_unique($real_comm);
foreach ($community as $value) {
    if (!in_array($value, $real_comm)) {
        $diff[] = $value;
    }
}
print_r($diff);

$comm = array_unique($commids);
$communitycount2 = count($comm);

$CommIDStr = implode(',', $comm);
STATIS_WRITE("\"Community ID list: $CommIDStr\"");

if ($communitycount != $communitycount2)
{
    echo "error community number no mach $communitycount != $communitycount2";
}



