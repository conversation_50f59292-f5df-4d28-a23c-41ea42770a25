<?php


/*
以社区id升级设备，把对应的版本升级到指定版本
注意：服务器要先在super登记升级的固件
生成的：UpgradeList.csv用于后续CountUpgradeBeforeOffline.php统计
*/

date_default_timezone_set('PRC');
const STATIS_FILE = "./UpgradeList.csv";

$CommIDs = [1928,1500,1279,1525,1325,1222,1478,1291,1226,1253,1969,2384,1631,1699,4557,1604,1938,2166,2118,1124,1944,2412,2141,1698,1790,2324,4551,1606,1389,1390,1633];

$Firmware=[

    "113.30.4.98" => "113.30.8.106",
    "113.30.4.103" => "113.30.8.106",
    "113.30.4.156" => "113.30.8.106",
    "113.30.4.215" => "113.30.8.106",
    "113.30.6.131" => "113.30.8.106",
    "113.30.6.139" => "113.30.8.106",
    "113.30.7.100" => "113.30.8.106",
    "113.55.4.108" => "113.30.8.106",
    "113.55.4.109" => "113.30.8.106",
    "113.30.4.199" => "113.30.8.106",
    "113.30.4.213" => "113.30.8.106",
    "113.30.8.30" => "113.30.8.106",
    "113.30.8.59" => "113.30.8.106",
    "113.30.8.68" => "113.30.8.106",
    
    "212.30.6.74" => "***********",
    "212.30.6.75" => "***********",
    "***********" => "***********",
    "***********" => "***********",
    "***********" => "***********",
    "***********" => "***********",

    "************" => "***********",
    "***********" => "***********",
    "***********" => "***********",
    "***********" => "***********",
    "***********" => "***********",
    "***********" => "***********",
    "***********" => "***********",
    "***********" => "***********",

];

$CommIDStr = implode(',', $CommIDs);
function get_db_obj()
{
    $PARAM_host='127.0.0.1';
    $PARAM_port='3306';
    $PARAM_db_name='AKCS';
    $PARAM_user='root';
    $PARAM_db_pass='Ak@56@<EMAIL>';

    $dbh = new PDO('mysql:host='.$PARAM_host.';port='.$PARAM_port.';dbname='.$PARAM_db_name, $PARAM_user, $PARAM_db_pass, null);
    return $dbh;
}
$db = get_db_obj();
//社区
$sth = $db->prepare("select Location,Mac,Firmware,Status From Devices where MngAccountID in ($CommIDStr)");
$sth->execute();
$mac_list = $sth->fetchALL(PDO::FETCH_ASSOC);

$upgradelist = array();
foreach ($mac_list as $row => $mac_info) {
    $location = $mac_info['Location'];
    $mac = $mac_info['Mac'];
    $fw = $mac_info['Firmware'];
    $status = $mac_info['Status'];

    if (array_key_exists($fw, $Firmware)) {
        $upgrade_fw = $Firmware[$fw];
        $upgradelist[$mac] =  $upgrade_fw;
    }
}


shell_exec("touch ". STATIS_FILE);

chmod(STATIS_FILE, 0777);
if (file_exists(STATIS_FILE)) {
    shell_exec("echo > ". STATIS_FILE);
}
function STATIS_WRITE($content)
{
    file_put_contents(STATIS_FILE, $content, FILE_APPEND);
    file_put_contents(STATIS_FILE, "\n", FILE_APPEND);
}

$currentDate = date('Y-m-d');
$verlist = array();
$i = 0;
foreach ($upgradelist as $mac => $fw) {
    if ($i % 50 == 0) {
        sleep(120);
        foreach ($Firmware as $old => $new) {
            $dbh = get_db_obj();
            //先插入要升级的固件版本
            $sth = $dbh->prepare("insert into UpgradeRomVersion(Version,Status,CreateTime,UpdateTime,OwnerAccount) values (:ver,0,now(), now() + interval 1 minute, 'superManage');");
            $sth->bindParam(':ver', $new, PDO::PARAM_STR);
            $sth->execute();

            $sth = $dbh->prepare("SELECT LAST_INSERT_ID() AS id");
            $sth->execute();
            $ver_id = $sth->fetch(PDO::FETCH_ASSOC)['id'];

            $verlist[$new] =  $ver_id;
        }
    }
    $i++;
    $ver_id = $verlist[$fw];
    if ($ver_id <= 0) {
        continue;
    }
    
    $sth = $dbh->prepare("insert into UpgradeRomDevices(UpgradeRomVerID,MAC,Status) values (:ver_id,:mac, 0);");
    $sth->bindParam(':ver_id', $ver_id, PDO::PARAM_INT);
    $sth->bindParam(':mac', $mac, PDO::PARAM_STR);
    $sth->execute();
    STATIS_WRITE("$currentDate UpgradeRomDevices $mac $fw");
}
