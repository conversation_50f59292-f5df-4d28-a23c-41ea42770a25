<?php

const TMPLOG = "/tmp/devfirm.csv";
function logWrite($content)
{
	file_put_contents(TMPLOG, $content, FILE_APPEND);
	file_put_contents(TMPLOG, "\n", FILE_APPEND);
}

function getDB()
{
    $dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";

    $mysql_conn_string = "mysql:host=$dbhost;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}
	shell_exec("touch ". TMPLOG);
	chmod(TMPLOG, 0777);
	if (file_exists(TMPLOG)) {
		shell_exec("echo > ". TMPLOG);
	} 
	$firmware = ['88.','119.','933.','115.','117.','82.','83.','49.','567.'];
	logWrite('Dis'.','.'Ins'.','.'Community'.','.'MAC'.','.'firmware'.',');
	$db = getDB();	
    foreach($firmware as $firm)
    {
        $sql = "SELECT D.MAC,D.Firmware,A.Location,A1.Account as Ins,A2.Account as Dis FROM Devices D join Account A on 
        D.MngAccountID=A.ID join Account A1 on A1.ID=A.ManageGroup join Account A2 on A2.ID=A.ParentID WHERE D.Firmware like '" . $firm . "%' order by Firmware";
        $sth = $db->prepare($sql);
        $sth->execute();
        $list = $sth->fetchAll(PDO::FETCH_ASSOC);	
        foreach ($list as $row => $value){
            logWrite($value['Dis'].','.$value['Ins'].','.$value['Location'].','.$value['MAC'].','.$value['Firmware'].',');
        }
    }   

		

