<?php
/*处理大版本升级时候，需要根据低版本在插入数据的操作*/
/*V4.0到4.1需要插入PersonalAccountCnf配置*/
date_default_timezone_set("PRC");
function getDB()
{
    //这个升级的先不要弄主从的，直接在主库执行，不然sql语句就要考虑主从的问题
    $dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}
/*
+------+-------------------+----------------------------------+-------+------+----------+----------------------+-------+------+-----------+---------+-------+-----------------+------------+------------------------+--------------------+---------------+-------------+------------+---------+
| ID   | Account           | Passwd                           | Grade | Role | ParentID | Location             | Email | Info | SipPrefix | Special | Phone | TimeZone        | HouseCount | EnableValidTimeSetting | EnableCountSetting | CustomizeForm | ManageGroup | ChargeMode | SipType |
+------+-------------------+----------------------------------+-------+------+----------+----------------------+-------+------+-----------+---------+-------+-----------------+------------+------------------------+--------------------+---------------+-------------+------------+---------+
| 1062 | 1AF574NQ9XX671665 | 69352d9b36e9e3976731d6560a0ef7f8 |    21 |    2 |       46 | Ben Gurion 3 Rehovot |       |      |         0 |       1 |       | +2:00 Jerusalem |        100 |                      0 |                  0 |             7 |         162 |          1 |       3 |
+------+-------------------+----------------------------------+-------+------+----------+----------------------+-------+------+-----------+---------+-------+-----------------+------------+------------------------+--------------------+---------------+-------------+------------+---------+
1 row in set (0.02 sec)
update PersonalAccount set ExpireTime='2299-12-31 23:59:59' where Account in(*********,*********,*********);
update PersonalAccount set ExpireTime='2299-12-31 23:59:59' where ParentID=3546 and Role=21;
update PersonalAccount set ExpireTime='2299-12-31 23:59:59' where ParentID=3547 and Role=21;
update PersonalAccount set ExpireTime='2299-12-31 23:59:59' where ParentID=3552 and Role=21;

*/



$db = getDB();

function UpdateAllAcountFree()
{
    $db = getDB();
    $db->beginTransaction(); // 开启一个事务
    $sth = $db->prepare("select ID,ParentID from PersonalAccount where ParentID=1062 and Role=20;");
    $sth->execute();
    $account_list = $sth->fetchALL(PDO::FETCH_ASSOC);
    if (empty($account_list))
    {
        return 0;
    }
    foreach ($account_list as $row => $account)
    {
        $sth = $db->prepare("select ID  from PersonalAccount where ParentID=:ID and Role=21;");
        $sth->bindParam(':ID', $account['ID'], PDO::PARAM_STR);
        $sth->execute();
        $cong_list = $sth->fetchALL(PDO::FETCH_ASSOC);
        foreach ($cong_list as $row => $cong)
        {
            $sth = $db->prepare("update PersonalAccount set Active=1,ExpireTime='2299-12-31 23:59:59' where ID=:ID;");
            $sth->bindParam(':ID', $cong['ID'], PDO::PARAM_STR);
            $sth->execute();            
        }        
        $sth = $db->prepare("update PersonalAccount set Active=1,ExpireTime='2299-12-31 23:59:59' where ID=:ID;");
        $sth->bindParam(':ID', $account['ID'], PDO::PARAM_STR);
        $sth->execute();          

    }
    $db->commit();
    return 1;        
}
UpdateAllAcountFree();

?>
