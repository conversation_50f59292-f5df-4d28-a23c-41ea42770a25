<?php
/*处理大版本升级时候，需要根据低版本在插入数据的操作*/
/*V4.0到4.1需要插入PersonalAccountCnf配置*/
date_default_timezone_set("PRC");
function getDB()
{
    //这个升级的先不要弄主从的，直接在主库执行，不然sql语句就要考虑主从的问题
    $dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}
/*
把区域管理员PalwintecS/<EMAIL> 下的所有用户设置过期时间为永久，不包括激活
*/


function UpdateAllAcountFree($installID)
{
    $db = getDB();
    $db->beginTransaction(); // 开启一个事务
    $sth = $db->prepare("select ID,ParentID from PersonalAccount where ParentID=:InstallID and Role=20;");
    $sth->bindParam(':InstallID', $InstallID, PDO::PARAM_INT);
    $sth->execute();
    $account_list = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($account_list as $row => $account)
    {
        $sth = $db->prepare("select ID  from PersonalAccount where ParentID=:ID and Role=21;");
        $sth->bindParam(':ID', $account['ID'], PDO::PARAM_STR);
        $sth->execute();
        $cong_list = $sth->fetchALL(PDO::FETCH_ASSOC);
        foreach ($cong_list as $row => $cong)
        {
            $sth = $db->prepare("update PersonalAccount set ExpireTime='2299-12-31 23:59:59' where ID=:ID;");
            $sth->bindParam(':ID', $cong['ID'], PDO::PARAM_STR);
            $sth->execute();            
        }        
        $sth = $db->prepare("update PersonalAccount set ExpireTime='2299-12-31 23:59:59' where ID=:ID;");
        $sth->bindParam(':ID', $account['ID'], PDO::PARAM_STR);
        $sth->execute();          

    }
    
    
    $sth = $db->prepare("select ID,ParentID from PersonalAccount where ParentID=:InstallID and Role=10;");
    $sth->bindParam(':InstallID', $InstallID, PDO::PARAM_INT);
    $sth->execute();
    $account_list = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach ($account_list as $row => $account)
    {
        $sth = $db->prepare("select ID  from PersonalAccount where ParentID=:ID and Role=11;");
        $sth->bindParam(':ID', $account['ID'], PDO::PARAM_STR);
        $sth->execute();
        $cong_list = $sth->fetchALL(PDO::FETCH_ASSOC);
        foreach ($cong_list as $row => $cong)
        {
            $sth = $db->prepare("update PersonalAccount set ExpireTime='2299-12-31 23:59:59' where ID=:ID;");
            $sth->bindParam(':ID', $cong['ID'], PDO::PARAM_STR);
            $sth->execute();            
        }        
        $sth = $db->prepare("update PersonalAccount set ExpireTime='2299-12-31 23:59:59' where ID=:ID;");
        $sth->bindParam(':ID', $account['ID'], PDO::PARAM_STR);
        $sth->execute();          

    }    
   
    $db->commit();
    return 1;        
}
/*46为这个区域管理员的ID*/
$db = getDB();
$sth = $db->prepare("select ID From  Account where ParentID=46;");
$sth->execute();
$account_list = $sth->fetchALL(PDO::FETCH_ASSOC);
foreach ($account_list as $row => $account)
{
     $installID= $account['ID'];         
     UpdateAllAcountFree($installID);
} 



?>
