1、登录
   https://letsencrypt.osfipin.com/user-0408/order?id=5vlpxg
   重新申请证书和下载证书

2、登录************
  2.1 重启cert_manage(py代码没写好，执行一段时间会卡住)
  2.2 cd /usr/local/cert_manage/cert  上传证书目录

3、主机1：
   上传完证书后执行：
   kill `ps -ef | grep update_cert_from_remote_server.sh | grep -v grep | awk '{print $2}'`
   浏览器看：api.xcloud.akuvox.com看过期时间是否更新
   
   从机：
   上传完证书后执行：
   kill `ps -ef | grep update_cert_from_remote_server.sh | grep -v grep | awk '{print $2}'`  
   
   在执行下面命令看证书名称和时间是否正确
   openssl x509 -in /usr/local/nginx/conf/cert/wildcard/fullchain.crt -noout -text | grep -E "Subject|After|Before"
