<?php
date_default_timezone_set('PRC');

function get_db_obj(){
    $PARAM_host='127.0.0.1';
    $PARAM_port='3306';
    $PARAM_db_name='AKCS';
    $PARAM_user='root';
    $PARAM_db_pass='Ak@56@<EMAIL>';

    $dbh = new PDO('mysql:host='.$PARAM_host.';port='.$PARAM_port.';dbname='.$PARAM_db_name,$PARAM_user,$PARAM_db_pass,null);
    return $dbh;
}

function check_dev($mac) {

    $dbh = get_db_obj();
    
    #注意类型
    $sth = $dbh->prepare("select Status from Devices where mac=\"$mac\";");
    $sth->execute();
    $value = $sth->fetch(PDO::FETCH_ASSOC);
    if ($value)
	{
		$status = date('Y-m-d H:i:s'). "   ".$value["Status"];
        $STATIS_FILE="echo \"$status\" >> /root/monitor/$mac.log";
        shell_exec($STATIS_FILE);
        $h = date('H');
        $m = date('i');
		#if ($h == 9 && $m == 30 )
		#{
        #                $STATIS_FILE = "echo '$mac status' | mutt -s '$mac mac status' -a /root/czx/$mac.log  -b <EMAIL> -c <EMAIL>";
	    #	shell_exec($STATIS_FILE);
		#}
	}
}

check_dev("0C1105134F5D");
check_dev("0C1105134FE0");
check_dev("0C1105135718");
check_dev("0C11051357D2");

