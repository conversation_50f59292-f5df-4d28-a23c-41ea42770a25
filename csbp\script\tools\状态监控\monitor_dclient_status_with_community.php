<?php
date_default_timezone_set('PRC');

function get_db_obj(){
    $PARAM_host='127.0.0.1';
    $PARAM_port='3306';
    $PARAM_db_name='AKCS';
    $PARAM_user='root';
    $PARAM_db_pass='Ak@56@<EMAIL>';

    $dbh = new PDO('mysql:host='.$PARAM_host.';port='.$PARAM_port.';dbname='.$PARAM_db_name,$PARAM_user,$PARAM_db_pass,null);
    return $dbh;
}

function check_dev() {

    $dbh = get_db_obj();
    
    #注意类型
    $sth = $dbh->prepare("select Mac,Firmware,LastConnection,Status  From Devices where MngAccountID in (11641,11644) AND TIMESTAMPDIFF(MINUTE, LastConnection, NOW()) <= 5;");
    $sth->execute();
    $mac_list = $sth->fetchALL(PDO::FETCH_ASSOC);

    $file = date('Y-m-d-H-i-s');
    foreach ($mac_list as $row => $mac_info) 
    {
        $Mac = $mac_info["Mac"];
        $LastConnection = $mac_info["LastConnection"];
        $Status = $mac_info["Status"];

        $STATIS_FILE="echo \"$Mac, $LastConnection, $Status\" >> /root/monitor/$file.log";
        shell_exec($STATIS_FILE);
        
	}
    if ($mac_list)
    {
        $STATIS_FILE = "echo 'SS 120 status' | mutt -s 'SS 120 status' -a /root/monitor/$file.log  -b <EMAIL> ";
        shell_exec($STATIS_FILE);
    }        
  
}

#每5分钟执行一次，如果连接状态是最近5分钟  发送邮件通知
check_dev();


