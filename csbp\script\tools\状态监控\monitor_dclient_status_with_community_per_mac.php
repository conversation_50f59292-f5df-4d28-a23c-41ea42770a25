<?php
date_default_timezone_set('PRC');
function get_db_obj(){
    $PARAM_host='127.0.0.1';
    $PARAM_port='3306';
    $PARAM_db_name='AKCS';
    $PARAM_user='root';
    $PARAM_db_pass='Ak@56@<EMAIL>';

    $dbh = new PDO('mysql:host='.$PARAM_host.';port='.$PARAM_port.';dbname='.$PARAM_db_name,$PARAM_user,$PARAM_db_pass,null);
    return $dbh;
}

function check_dev() {

    $dbh = get_db_obj();
    
    #注意类型
    $sth = $dbh->prepare("select Mac,IPAddress,Firmware,LastConnection,LastDisConn,Status From Devices where MngAccountID in  (17939) and Firmware like '212.%';");
    $sth->execute();
    $mac_list = $sth->fetchALL(PDO::FETCH_ASSOC);

    $date = new DateTime();
    $times = $date->format('Y-m-d H:i:s');
    $file = date('Y-m-d-H-i-s');
    foreach ($mac_list as $row => $mac_info) 
    {
        $Mac = $mac_info["Mac"];
        $LastConnection = $mac_info["LastConnection"];
        $LastDisConn = $mac_info["LastDisConn"];
        $Status = $mac_info["Status"];
        $Firmware = $mac_info["Firmware"];
        $IPAddress = $mac_info["IPAddress"];
        #在通过monitor_tag.php 定时打包通知
        $STATIS_FILE="echo \"$Mac, $times, $Firmware, $LastConnection, $LastDisConn, $IPAddress, $Status\" >> /home/<USER>/dev/$Mac.log";
        shell_exec($STATIS_FILE);
        
    }
  
}

check_dev();


