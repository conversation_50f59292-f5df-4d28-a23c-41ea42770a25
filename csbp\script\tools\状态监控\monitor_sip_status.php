<?php
date_default_timezone_set('PRC');


function check_dev($mac, $sip) {
    $STATIS_FILE = "/usr/local/freeswitch/bin/fs_cli -x \"sofia status profile internal reg $sip\" | grep Ping-Status | tr -d '\n'";
    $ret = shell_exec($STATIS_FILE);
    $status = date('Y-m-d H:i:s'). "   ".$ret;
    $STATIS_FILE="echo \"$status\" >> /root/monitor/$mac-$sip.log";
    shell_exec($STATIS_FILE);
}


check_dev("0C1105134F5D", "931100164");
check_dev("0C1105134FE0", "931100152");
check_dev("0C1105135718", "931100145");
check_dev("0C11051357D2", "931100148");
