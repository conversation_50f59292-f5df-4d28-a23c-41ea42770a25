<?php
require_once('PHPExcel/PHPExcel.php');
require_once('PHPExcel/PHPExcel/Writer/Excel2007.php');
$objPHPExcel = new PHPExcel();
 
//Set properties 设置文件属性
$objPHPExcel->getProperties()->setCreator("Maarten Balliauw");
$objPHPExcel->getProperties()->setLastModifiedBy("Maarten Balliauw");
$objPHPExcel->getProperties()->setTitle("Office 2007 XLSX Test Document");
$objPHPExcel->getProperties()->setSubject("Office 2007 XLSX Test Document");
$objPHPExcel->getProperties()->setDescription("Test document for Office 2007 XLSX, generated using PHP classes.");
$objPHPExcel->getProperties()->setKeywords("office 2007 openxml php");
$objPHPExcel->getProperties()->setCategory("Test result file");
 
//Add some data 添加数据
$objPHPExcel->setActiveSheetIndex(0);
$objPHPExcel->getActiveSheet()->setCellValue('A1', 'Building');//可以指定位置
$objPHPExcel->getActiveSheet()->setCellValue('B1', "Apt");
$objPHPExcel->getActiveSheet()->setCellValue('C1', "AptName");
$objPHPExcel->getActiveSheet()->setCellValue('D1', "Device");
$objPHPExcel->getActiveSheet()->setCellValue('E1', "FirstName");
$objPHPExcel->getActiveSheet()->setCellValue('F1', "LastName");
$objPHPExcel->getActiveSheet()->setCellValue('G1', "MobileNumber");
$objPHPExcel->getActiveSheet()->setCellValue('H1', "Email");
$objPHPExcel->getActiveSheet()->setCellValue('I1', "TelephoneCallingCode");
$objPHPExcel->getActiveSheet()->setCellValue('J1', "1stPhone");
$objPHPExcel->getActiveSheet()->setCellValue('K1', "2ndPhone");
$objPHPExcel->getActiveSheet()->setCellValue('L1', "3rdPhone"); 
$objPHPExcel->getActiveSheet()->setCellValue('M1', "CallType");
 
//循环
#$buildings = 50;
#$accounts = 100;
//building 个数和每个building下的用户数据
$buildings = 10;
$accounts = 100;
$index = 2;
for($b = 1; $b <= $buildings; $b++) {

	for($a = 1; $a <= $accounts; $a++) {
		$b_name = 'B'.$b;
		$apt_name = 'A'.$a;
		$blank = "";
		$email = "$b_name"."_$<EMAIL>";

		$objPHPExcel->getActiveSheet()->setCellValue('A' . $index, $b_name);
		$objPHPExcel->getActiveSheet()->setCellValue('B' . $index, $a);
		$objPHPExcel->getActiveSheet()->setCellValue('C' . $index, $apt_name);
		$objPHPExcel->getActiveSheet()->setCellValue('D' . $index, $blank);
		$objPHPExcel->getActiveSheet()->setCellValue('E' . $index, $a);
		$objPHPExcel->getActiveSheet()->setCellValue('F' . $index, $a);
		$objPHPExcel->getActiveSheet()->setCellValue('G' . $index, $blank);
		$objPHPExcel->getActiveSheet()->setCellValue('H' . $index, $email);
		$objPHPExcel->getActiveSheet()->setCellValue('I' . $index, $blank);
		$objPHPExcel->getActiveSheet()->setCellValue('J' . $index, $blank);
		$objPHPExcel->getActiveSheet()->setCellValue('K' . $index, $blank);
		$objPHPExcel->getActiveSheet()->setCellValue('L' . $index, $blank);
		$objPHPExcel->getActiveSheet()->setCellValue('M' . $index, 0);
		
		$index = $index + 1;
	}
}


$objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);
$objWriter->save('community.xlsx');



