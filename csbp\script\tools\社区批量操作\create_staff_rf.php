<?php
require_once('PHPExcel/PHPExcel.php');
require_once('PHPExcel/PHPExcel/Writer/Excel2007.php');
$objPHPExcel = new PHPExcel();
 
//Set properties 设置文件属性
$objPHPExcel->getProperties()->setCreator("Maarten Balliauw");
$objPHPExcel->getProperties()->setLastModifiedBy("Maarten Balliauw");
$objPHPExcel->getProperties()->setTitle("Office 2007 XLSX Test Document");
$objPHPExcel->getProperties()->setSubject("Office 2007 XLSX Test Document");
$objPHPExcel->getProperties()->setDescription("Test document for Office 2007 XLSX, generated using PHP classes.");
$objPHPExcel->getProperties()->setKeywords("office 2007 openxml php");
$objPHPExcel->getProperties()->setCategory("Test result file");
 
//Add some data 添加数据
$objPHPExcel->setActiveSheetIndex(0);
$objPHPExcel->getActiveSheet()->setCellValue('A1', 'Staff Name');//可以指定位置
$objPHPExcel->getActiveSheet()->setCellValue('B1', "RF Card");
$objPHPExcel->getActiveSheet()->setCellValue('C1', "Access Group ID");
#$objPHPExcel->getActiveSheet()->setCellValue('B2', 'world!');
#$objPHPExcel->getActiveSheet()->setCellValue('B3', 2);
#$objPHPExcel->getActiveSheet()->setCellValue('C1', 'Hello');
#$objPHPExcel->getActiveSheet()->setCellValue('D2', 'world!');
 
//循环
$times = 4000;
$start = 28001;
$ag = "63";
	
for($i = 2;$i<$times;$i++) {
	$offset = $i + $start;
	$name = "Staff_$offset";
	$rf = "ABCDEF$offset";
	$objPHPExcel->getActiveSheet()->setCellValue('A' . $i, $name);
	$objPHPExcel->getActiveSheet()->setCellValue('B' . $i, $rf);
	$objPHPExcel->getActiveSheet()->setCellValue('C' . $i, $ag);
}



$objWriter = new PHPExcel_Writer_Excel2007($objPHPExcel);
$objWriter->save("staff_rf$start.xlsx");



