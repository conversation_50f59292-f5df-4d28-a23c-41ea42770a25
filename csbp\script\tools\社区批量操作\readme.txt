1、生成社区导入的文件
   修改脚本create_community.php里面的变量，生成指定的个数用户
   $buildings = 10;
   $accounts = 100;
   
2、生成物业的卡文件
   修改脚本create_staff_rf.php里面的变量，生成导入卡的数据
	$times = 4000;//个数
	$start = 28001;//卡的起始下标
	$ag = "63";//权限组ID
   
3、重命名人脸。会扫描face下面的人脸图片，给每张人脸按build+主账号的方式命名。
  (提前准备人脸图片，人脸图片可以重复，设备不区分不同家庭的用户人脸是否重复)
   修改脚本rename_face.php里面的变量
   $buildings = 10;//与导入社区时候的一致
   $accounts = 100;//与导入社区时候的一致
   
   


