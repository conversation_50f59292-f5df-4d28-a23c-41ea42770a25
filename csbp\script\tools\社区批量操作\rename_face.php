<?php
require_once('PHPExcel/PHPExcel.php');
require_once('PHPExcel/PHPExcel/Writer/Excel2007.php');


 $face_dir = "face";

function myScanDir($dir)
{
    $file_arr = scandir($dir);
    $new_arr = [];
    foreach($file_arr as $item)
	{
        if($item!=".." && $item !=".")
		{
            if(is_dir($dir."/".$item))
			{
                $new_arr[$item] = myScanDir($dir."/".$item);
            }
			else
			{
                $new_arr[] = $item;
            }
        }
    }
    return $new_arr;
    
}

$files = myScanDir($face_dir);

$buildings = 10;
$accounts = 100;
$index = 0;
for($b = 1; $b <= $buildings; $b++) {
	for($a = 1; $a <= $accounts; $a++) {
		$new_name = $face_dir."/B$b+$a+$a $a.jpg";
		if (count($files) > $index)
		{
			rename("$face_dir/".$files[$index], $new_name);
		}
		else
		{
			return;
		}
		
		$index = $index + 1;
	}
}







