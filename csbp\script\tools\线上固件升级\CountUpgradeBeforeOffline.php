<?php

date_default_timezone_set('PRC');
const STATIS_FILE = "./UpgradeList.csv";

function get_db_obj()
{
    $PARAM_host='127.0.0.1';
    $PARAM_port='3306';
    $PARAM_db_name='AKCS';
    $PARAM_user='root';
    $PARAM_db_pass='Ak@56@<EMAIL>';

    $dbh = new PDO('mysql:host='.$PARAM_host.';port='.$PARAM_port.';dbname='.$PARAM_db_name, $PARAM_user, $PARAM_db_pass, null);
    $dbh->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbh->query('set names utf8;');
    return $dbh;
}

$file = fopen(STATIS_FILE,"rb");
$macs = array();
while (!feof($file)) {
    $content = fgets($file);

    $splitStrings = explode(" ", $content);
    if (isset($splitStrings[2])) {
        $mac = $splitStrings[2];
        $mac = trim($mac);
        array_push($macs, $mac);
    }
}

$macstr = '"' . implode('","', $macs) . '"';
$db = get_db_obj();
//社区
$sth = $db->prepare("select Mac,Status,Firmware,P.Email,Name,A.Location From Devices D left join PersonalAccount P on P.Account=D.Node left join Account A on A.ID=P.ParentID  where Mac in ($macstr) order by Location;");
$sth->execute();
$mac_list = $sth->fetchALL(PDO::FETCH_ASSOC);

foreach ($mac_list as $row => $mac_info) {
    $mac = $mac_info['Mac'];
    $status = $mac_info['Status'];
    $fw = $mac_info['Firmware'];
    $comm = $mac_info['Location'];
    $name = $mac_info['Name'];
    $line = "";
    if ($status == 0)
    {
        $line = "offline";
    }
    echo "$mac,$fw,$comm,$name,$line\n";
}

