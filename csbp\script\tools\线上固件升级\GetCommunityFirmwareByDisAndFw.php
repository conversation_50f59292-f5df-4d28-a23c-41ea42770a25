<?php

date_default_timezone_set('PRC');


/*
获取dis下这些版本的列表
格式：Community,RoomName,Mac,Firmware,Status,MngAccountID
*/

$DIS = "DOORCOM";
$firmware=["113.30.4.213","113.30.8.30","113.30.8.59","113.30.8.68","212.30.8.11","212.30.8.21",
"213.30.8.30","***********","***********","***********","***********","***********","************"];


$firmwareString = '"' . implode('","', $firmware) . '"';

$WRITE_FILE = "./$DIS.csv";
shell_exec("touch ". $WRITE_FILE);

chmod($WRITE_FILE, 0777);
if (file_exists($WRITE_FILE)) {
    shell_exec("echo > ". $WRITE_FILE);
}
function STATIS_WRITE($content)
{
    global $WRITE_FILE;
    file_put_contents($WRITE_FILE, $content, FILE_APPEND);
    file_put_contents($WRITE_FILE, "\n", FILE_APPEND);
}
function getDB()
{
    $dbhost = "127.0.0.1";
    $dbuser = "root";

    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $opts_values = array(PDO::ATTR_PERSISTENT=>true,PDO::ATTR_ERRMODE=>2,PDO::MYSQL_ATTR_INIT_COMMAND=>'SET NAMES utf8');
    $mysql_conn_string = "mysql:host=$dbhost;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass, $opts_values);
    //$dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION, PDO::MYSQL_ATTR_INIT_COMMAND=>'SET NAMES utf8');
    return $dbConnection;
}

$db = getDB();

$static_str = 'Community,RoomName,Mac,Firmware,Status,MngAccountID';
STATIS_WRITE($static_str);

//社区
$sth = $db->prepare("select A.Location,Mac,Firmware,D.Status,D.MngAccountID,R.RoomName,PA.Name From Devices D left join PersonalAccount PA on PA.Account=D.Node join Account A on A.ID=PA.ParentID 
 join Account AA on AA.ID=A.ParentID join CommunityRoom R on R.ID = PA.RoomID where AA.Account=:DIS and PA.Role=20 and Firmware in ($firmwareString) order by A.Location;");
 $sth->bindParam(':DIS', $DIS, PDO::PARAM_STR);

$sth->execute();
$mac_list = $sth->fetchALL(PDO::FETCH_ASSOC);


foreach ($mac_list as $row => $mac_info) {
    $location = $mac_info['Location'];
    $mac = $mac_info['Mac'];
    $fw = $mac_info['Firmware'];
    $status = $mac_info['Status'];
    $MngAccountID = $mac_info['MngAccountID'];
    $RoomName = $mac_info['RoomName'];
    $Name = $mac_info['Name'];
    $static_str = null;
    $static_str =  "$location,$RoomName,$Name,$mac,$fw,$status,$MngAccountID";
    STATIS_WRITE($static_str);
}