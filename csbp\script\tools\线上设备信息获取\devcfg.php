<?php


$config="Config.Account1.GENERAL.UserName";
$mac = ['0C1105147DAE','0C110516B6A9','0C11051732E5','0C110514B3DD','0C110516098E','0C1105156338','0C110515A3EE'];

foreach ($mac as $mac){

    $resp = shell_exec("/usr/local/akcs/csmain/bin/csmain_cli -x '".$mac.";getcfg ".$config."'");
    $cfg = explode("Config.Settings.LOGLEVEL.Level=", $resp);	
    if(count($cfg)>1)	//获取正常
    {
        $type = trim($cfg[1]);	//去掉换行符
        echo "$mac  $type";
    }
    sleep(1);
}

    

