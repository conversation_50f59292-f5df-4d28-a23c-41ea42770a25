<?php

const TMPLOG = "/tmp/mac_system.csv";
const MACS = ["0C1105091EF6", "0C110512F895"];
function logWrite($content)
{

	file_put_contents(TMPLOG, $content, FILE_APPEND);
	file_put_contents(TMPLOG, "\n", FILE_APPEND);
}

function getfree($mac){
    $resp = shell_exec("/usr/local/akcs/csmain/bin/csmain_cli -x '$mac;free -k'");
    $value = trim($resp);
    return $value;
}
function getuptime($mac){
    $resp = shell_exec("/usr/local/akcs/csmain/bin/csmain_cli -x '$mac;uptime'");
    $value = trim($resp);
    return $value;
}

function getLoad($mac){
    $resp = shell_exec("/usr/local/akcs/csmain/bin/csmain_cli -x '$mac;cat /proc/loadavg'");
    $value = trim($resp);
    return $value;
}

shell_exec("touch ". TMPLOG);
chmod(TMPLOG, 0777);

foreach (MACS as $mac){

    echo "req:$mac\n";
    $result = getfree($mac);
    $result=shell_exec("echo \"$result\" | grep -E 'Mem|total' ");
    echo "$result";
    
    $result = getuptime($mac);
    $result=shell_exec("echo \"$result\" | grep -vE 'Content|Seq' ");
    echo "$result\n";
    
    $result = getLoad($mac);
    $result=shell_exec("echo \"$result\" | grep -vE 'Content|Seq' ");
    echo "$result\n\n\n";    
}

