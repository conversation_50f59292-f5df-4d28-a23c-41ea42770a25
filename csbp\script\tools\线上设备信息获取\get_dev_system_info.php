<?php

const TMPLOG = "/tmp/mac_system.csv";
const MACS = ["0C1105097F16","0C11051050C2","0C1105102194","0C11051699F6","0C1105118EFB","0C11050AA9B9","0C11050DF805"];
const R29R20MACS = ["0C11050A12C5","0C110507A582"];
function logWrite($content)
{

	file_put_contents(TMPLOG, $content, FILE_APPEND);
	file_put_contents(TMPLOG, "\n", FILE_APPEND);
}

function getfree($mac){
    $resp = shell_exec("/usr/local/akcs/csmain/bin/csmain_cli -x '$mac;free -k'");
    $value = trim($resp);
    return $value;
}
function getuptime($mac){
    $resp = shell_exec("/usr/local/akcs/csmain/bin/csmain_cli -x '$mac;uptime'");
    $value = trim($resp);
    return $value;
}

function getLoad($mac){
    $resp = shell_exec("/usr/local/akcs/csmain/bin/csmain_cli -x '$mac;cat /proc/loadavg'");
    $value = trim($resp);
    return $value;
}

function getBusyboxfree($mac){
    $resp = shell_exec("/usr/local/akcs/csmain/bin/csmain_cli -x '$mac;busybox free'");
    $value = trim($resp);
    return $value;
}

shell_exec("touch ". TMPLOG);
chmod(TMPLOG, 0777);

foreach (MACS as $mac){

    echo "req:$mac\n";
    $result = getfree($mac);
    $result=shell_exec("echo \"$result\" | grep -iE 'Mem' | awk -F ' ' '{print $2, $3}' ");
    $result = explode(" ", $result);
    echo $result[1]/$result[0]."\n";
    
    $result = getuptime($mac);
    $result=shell_exec("echo \"$result\" | grep -vE 'Content|Seq' ");
    echo "$result\n\n";
    
    $result = getLoad($mac);
    $result=shell_exec("echo \"$result\" | grep -vE 'Content|Seq' ");
    echo "$result\n\n";    
}

foreach (R29R20MACS as $mac){

    echo "req:$mac\n";
    $result = getBusyboxfree($mac);
    $result=shell_exec("echo \"$result\" | grep -iE 'Mem' | awk -F ' ' '{print $2, $3}' ");
    $result = explode(" ", $result);
    echo $result[1]/$result[0]."\n";
    
    $result = getuptime($mac);
    $result=shell_exec("echo \"$result\" | grep -vE 'Content|Seq' ");
    echo "$result\n\n";
    
    $result = getLoad($mac);
    $result=shell_exec("echo \"$result\" | grep -vE 'Content|Seq' ");
    echo "$result\n\n";    
}


