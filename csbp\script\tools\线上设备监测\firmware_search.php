<?php
if($argc<2)
{
    echo "no param\n";
    return;
}
$firm = $argv[1];

function getDB(){
	$dbhost = "127.0.0.1";	//需在mysql主机执行
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
	$dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}


    $db = getDB();
    $sth = $db->prepare("select MAC from Devices where Firmware = :firm and Status=1 union all select MAC from PersonalDevices where Firmware = :firm and Status=1 limit 1;");
    $sth->bindParam(':firm', $firm, PDO::PARAM_STR);
    $sth->execute();
    $ret = $sth->fetch(PDO::FETCH_ASSOC);
    echo $ret['MAC']."\n"; 
