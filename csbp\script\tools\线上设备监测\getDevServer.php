<?php
const DEV_MONITOR_LIST = array(
    //第一批
    "0C11050FACD0"=>"ucloud",
    "0C11051501B7"=>"ucloud",
"0C110519FE94"=>"ucloud",
"0C11051769D9"=>"ucloud",
"0C110517A19E"=>"ucloud",
"0C110517A3A4"=>"ucloud",
"0C11050F1D6F"=>"ecloud",
"0C110510F5A6"=>"ucloud",
"0C110518B784"=>"ucloud",
"0C110515607D"=>"jcloud",
"0C11050F302B"=>"ucloud",
"0C110508AEA9"=>"ucloud",
"0C1105092814"=>"ucloud",
"0C11050FD13B"=>"ucloud",
"0C11051B6BE1"=>"ucloud",
"0C11051882E7"=>"ecloud",
"0C110517AB36"=>"ecloud",
"0C110519FE56"=>"ecloud",
//第二批
"0C1105185162"=>"ecloud",
"0C1105187B7A"=>"ecloud",
"0C110515E317"=>"ecloud",
"0C11050B694C"=>"ucloud",
"0C110513867A"=>"ucloud",
"0C11051BF1B2"=>"ucloud",
"0C110512E13B"=>"ecloud",
"0C11050AA9C1"=>"ecloud",
"0C110518A130"=>"ecloud",
//第三批
"0C1105145811"=>"ucloud",
"0C110511FC48"=>"ucloud",
"0C1105163EE5"=>"ucloud",
"0C11050F3C13"=>"ucloud",
"0C1105100322"=>"ucloud",
"0C110513509D"=>"ucloud",
"0C1105091ABD"=>"ucloud",
"0C11050B0E20"=>"ucloud",
"0C11051A1903"=>"ucloud",
"0C11050FE975"=>"ucloud",
"0C1105118EFB"=>"ucloud",
"0C1105183203"=>"ucloud",
"0C1105078180"=>"ucloud",
"0C110507A582"=>"ucloud",
"0C110507819F"=>"ucloud",
"0C1105135EB6"=>"ucloud",
"0C11050A5936"=>"ucloud",
"0C110507067B"=>"ucloud",
"0C1105104B7C"=>"ucloud",
"0C11051B579C"=>"ucloud",
"0C1105092814"=>"ucloud",
"0C11050F79BD"=>"ucloud",
"0C11050DF7FD"=>"ucloud",
"0C11050AA9B9"=>"ucloud",
"0C11050862F1"=>"ucloud",
"0C110511CF23"=>"ucloud",
"0C11051775D7"=>"ucloud",
"0C110514917B"=>"ucloud",
"0C1105198E9B"=>"ucloud",
"0C1105166D61"=>"ucloud",
"0C1105133672"=>"ucloud",
"0C11050A6CFB"=>"ucloud",
"0C110505C453"=>"ucloud",
"0C110507A44D"=>"ucloud",
"0C110507A7BD"=>"ucloud",
"0C1105133DCA"=>"ucloud",
"0C110511003C"=>"ucloud",

);

$mac = $argv[1];
if(!array_key_exists($mac,DEV_MONITOR_LIST)){
    exit;
}

function httpRequest($method, $url, $headers, $data = '', $is_json = 0)
{
    $curl = curl_init();
    curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($curl, CURLOPT_HEADER, false);
    curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 3);
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
    if ($method == 'post') {
        curl_setopt($curl, CURLOPT_POST, true);

        if ($is_json) {
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        } else {
            curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
        }
    } elseif ($method == 'put') {
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "PUT");
        if ($is_json) {
            curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
        } else {
            curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($data));
        }
    }
    else if($method == 'delete'){
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, "DELETE");
    }
        
    
    $output = curl_exec($curl);
    if ($output == false) {
        printf("Error: ".curl_error($curl));
    }
    curl_close($curl);
    return $output;
}

$data['username'] = 'dev-monitor';
$data['password'] = 'aep#eeYo8ohV';
$output = httpRequest('post','https://maintenance.akuvox.com/api/login/',[],$data,1);
if($output){
    $userData = json_decode($output,true);    
    $token = $userData['data']['token']; 
    $header = ["Authorization: JWT ".$token];
    $output1 = httpRequest('get','https://maintenance.akuvox.com/api/devices/?mac='.$mac.'&cloud='.DEV_MONITOR_LIST[$mac],$header);
    $devData = json_decode($output1,true); 
    $ip = $devData['data']['server_ip'];
    if($ip){
        print($ip);
    }
}


