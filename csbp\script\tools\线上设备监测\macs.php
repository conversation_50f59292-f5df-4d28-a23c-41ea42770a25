<?php

$MACS = array();

//终端软件部

//116.30.4.21
$MACS['0C11050FACD0']['free_type'] = 0;
$MACS['0C11050FACD0']['cpu_type'] = 0;
$MACS['0C11050FACD0']['pid_type'] = 0;
$MACS['0C11050FACD0']['uptime_type'] = 0;
$MACS['0C11050FACD0']['pidmem_type'] = 0;
$MACS['0C11050FACD0']['process'][1] = '/app/bin/phone';
$MACS['0C11050FACD0']['process'][2] = '/app/bin/lighttpd';
$MACS['0C11050FACD0']['process'][3] = '/app/bin/dclient';

//216.30.0.67
$MACS['0C1105187B7A']['free_type'] = 0;
$MACS['0C1105187B7A']['cpu_type'] = 0;
$MACS['0C1105187B7A']['pid_type'] = 0;
$MACS['0C1105187B7A']['uptime_type'] = 0;
$MACS['0C1105187B7A']['pidmem_type'] = 0;
$MACS['0C1105187B7A']['process'][1] = '/app/bin/phone';
$MACS['0C1105187B7A']['process'][2] = '/app/bin/lighttpd';
$MACS['0C1105187B7A']['process'][3] = '/app/bin/dclient';

//116.30.8.5
$MACS['0C110511003C']['free_type'] = 0;
$MACS['0C110511003C']['cpu_type'] = 0;
$MACS['0C110511003C']['pid_type'] = 0;
$MACS['0C110511003C']['uptime_type'] = 0;
$MACS['0C110511003C']['pidmem_type'] = 0;
$MACS['0C110511003C']['process'][1] = '/app/bin/phone';
$MACS['0C110511003C']['process'][2] = '/app/bin/lighttpd';
$MACS['0C110511003C']['process'][3] = '/app/bin/dclient';


//18.30.4.38
$MACS['0C11051501B7']['free_type'] = 0;
$MACS['0C11051501B7']['cpu_type'] = 0;
$MACS['0C11051501B7']['pid_type'] = 0;
$MACS['0C11051501B7']['uptime_type'] = 0;
$MACS['0C11051501B7']['pidmem_type'] = 0;
$MACS['0C11051501B7']['process'][1] = '/app/bin/phone';
$MACS['0C11051501B7']['process'][2] = '/app/bin/lighttpd';
$MACS['0C11051501B7']['process'][3] = '/app/bin/dclient';

//216.30.0.48
$MACS['0C110519FE94']['free_type'] = 0;
$MACS['0C110519FE94']['cpu_type'] = 0;
$MACS['0C110519FE94']['pid_type'] = 0;
$MACS['0C110519FE94']['uptime_type'] = 0;
$MACS['0C110519FE94']['pidmem_type'] = 0;
$MACS['0C110519FE94']['process'][1] = '/app/bin/phone';
$MACS['0C110519FE94']['process'][2] = '/app/bin/lighttpd';
$MACS['0C110519FE94']['process'][3] = '/app/bin/dclient';

//567.30.1.225
$MACS['0C11051B6BE1']['free_type'] = 0;
$MACS['0C11051B6BE1']['cpu_type'] = 0;
$MACS['0C11051B6BE1']['pid_type'] = 0;
$MACS['0C11051B6BE1']['uptime_type'] = 0;
$MACS['0C11051B6BE1']['pidmem_type'] = 0;
$MACS['0C11051B6BE1']['process'][1] = 'com.akuvox.phone';
$MACS['0C11051B6BE1']['process'][2] = 'com.akuvox.upgradeui';
$MACS['0C11051B6BE1']['process'][3] = '/app/bin/sip';
$MACS['0C11051B6BE1']['process'][4] = '/app/bin/dclient';
$MACS['0C11051B6BE1']['process'][5] = '/app/bin/autop';
$MACS['0C11051B6BE1']['process'][6] = '/app/bin/lighttpd';

//933.30.207.138
$MACS['0C110519FE56']['free_type'] = 0;
$MACS['0C110519FE56']['cpu_type'] = 0;
$MACS['0C110519FE56']['pid_type'] = 0;
$MACS['0C110519FE56']['uptime_type'] = 0;
$MACS['0C110519FE56']['pidmem_type'] = 0;
$MACS['0C110519FE56']['process'][1] = 'com.akubela.panel';
$MACS['0C110519FE56']['process'][2] = 'com.akuvox.upgradeui';
$MACS['0C110519FE56']['process'][3] = '/app/bin/sip';
$MACS['0C110519FE56']['process'][4] = '/app/bin/netcast';

//***********
$MACS['0C1105145811']['free_type'] = 0;
$MACS['0C1105145811']['cpu_type'] = 0;
$MACS['0C1105145811']['pid_type'] = 0;
$MACS['0C1105145811']['uptime_type'] = 0;
$MACS['0C1105145811']['pidmem_type'] = 0;
$MACS['0C1105145811']['process'][1] = '/app/bin/phone';
$MACS['0C1105145811']['process'][2] = '/app/bin/lighttpd';
$MACS['0C1105145811']['process'][3] = '/app/bin/dclient';

//**********
$MACS['0C110511FC48']['free_type'] = 0;
$MACS['0C110511FC48']['cpu_type'] = 0;
$MACS['0C110511FC48']['pid_type'] = 0;
$MACS['0C110511FC48']['uptime_type'] = 0;
$MACS['0C110511FC48']['pidmem_type'] = 0;
$MACS['0C110511FC48']['process'][1] = '/app/bin/phone';
$MACS['0C110511FC48']['process'][2] = '/app/bin/lighttpd';
$MACS['0C110511FC48']['process'][3] = '/app/bin/dclient';

//**********
$MACS['0C1105163EE5']['free_type'] = 0;
$MACS['0C1105163EE5']['cpu_type'] = 0;
$MACS['0C1105163EE5']['pid_type'] = 0;
$MACS['0C1105163EE5']['uptime_type'] = 0;
$MACS['0C1105163EE5']['pidmem_type'] = 0;
$MACS['0C1105163EE5']['process'][1] = '/app/bin/phone';
$MACS['0C1105163EE5']['process'][2] = '/app/bin/lighttpd';
$MACS['0C1105163EE5']['process'][3] = '/app/bin/dclient';

//105.30.4.8
$MACS['0C11050F3C13']['free_type'] = 0;
$MACS['0C11050F3C13']['cpu_type'] = 0;
$MACS['0C11050F3C13']['pid_type'] = 0;
$MACS['0C11050F3C13']['uptime_type'] = 0;
$MACS['0C11050F3C13']['pidmem_type'] = 0;
$MACS['0C11050F3C13']['process'][1] = '/app/bin/phone';
$MACS['0C11050F3C13']['process'][2] = '/app/bin/lighttpd';
$MACS['0C11050F3C13']['process'][3] = '/app/bin/dclient';

//************
$MACS['0C11050862F1']['free_type'] = 0;
$MACS['0C11050862F1']['cpu_type'] = 0;
$MACS['0C11050862F1']['pid_type'] = 0;
$MACS['0C11050862F1']['uptime_type'] = 0;
$MACS['0C11050862F1']['pidmem_type'] = 0;
$MACS['0C11050862F1']['process'][1] = 'com.akuvox.phone';
$MACS['0C11050862F1']['process'][2] = 'com.akuvox.upgradeui';
$MACS['0C11050862F1']['process'][3] = '/app/bin/sip';
$MACS['0C11050862F1']['process'][4] = '/app/bin/dclient';
$MACS['0C11050862F1']['process'][5] = '/app/bin/autop';
$MACS['0C11050862F1']['process'][6] = '/app/bin/lighttpd';

//933.30.1.324
$MACS['0C110511CF23']['free_type'] = 0;
$MACS['0C110511CF23']['cpu_type'] = 0;
$MACS['0C110511CF23']['pid_type'] = 0;
$MACS['0C110511CF23']['uptime_type'] = 0;
$MACS['0C110511CF23']['pidmem_type'] = 0;
$MACS['0C110511CF23']['process'][1] = 'com.akuvox.phone';
$MACS['0C110511CF23']['process'][2] = 'com.akuvox.upgradeui';
$MACS['0C110511CF23']['process'][3] = '/app/bin/sip';
$MACS['0C110511CF23']['process'][4] = '/app/bin/dclient';
$MACS['0C110511CF23']['process'][5] = '/app/bin/autop';
$MACS['0C110511CF23']['process'][6] = '/app/bin/lighttpd';


//***********
$MACS['0C11051775D7']['free_type'] = 0;
$MACS['0C11051775D7']['cpu_type'] = 0;
$MACS['0C11051775D7']['pid_type'] = 0;
$MACS['0C11051775D7']['uptime_type'] = 0;
$MACS['0C11051775D7']['pidmem_type'] = 0;
$MACS['0C11051775D7']['process'][1] = 'com.akuvox.phone';
$MACS['0C11051775D7']['process'][2] = 'com.akuvox.upgradeui';
$MACS['0C11051775D7']['process'][3] = '/app/bin/sip';
$MACS['0C11051775D7']['process'][4] = '/app/bin/dclient';
$MACS['0C11051775D7']['process'][5] = '/app/bin/autop';
$MACS['0C11051775D7']['process'][6] = '/app/bin/lighttpd';

//************
$MACS['0C110514917B']['free_type'] = 0;
$MACS['0C110514917B']['cpu_type'] = 0;
$MACS['0C110514917B']['pid_type'] = 0;
$MACS['0C110514917B']['uptime_type'] = 0;
$MACS['0C110514917B']['pidmem_type'] = 0;
$MACS['0C110514917B']['process'][1] = 'com.akuvox.phone';
$MACS['0C110514917B']['process'][2] = 'com.akuvox.upgradeui';
$MACS['0C110514917B']['process'][3] = '/app/bin/sip';
$MACS['0C110514917B']['process'][4] = '/app/bin/dclient';
$MACS['0C110514917B']['process'][5] = '/app/bin/autop';
$MACS['0C110514917B']['process'][6] = '/app/bin/lighttpd';

//117.30.2.928
$MACS['0C11050A6CFB']['free_type'] = 0;
$MACS['0C11050A6CFB']['cpu_type'] = 0;
$MACS['0C11050A6CFB']['pid_type'] = 0;
$MACS['0C11050A6CFB']['uptime_type'] = 0;
$MACS['0C11050A6CFB']['pidmem_type'] = 0;
$MACS['0C11050A6CFB']['process'][1] = 'com.akuvox.phone';
$MACS['0C11050A6CFB']['process'][2] = 'com.akuvox.upgradeui';
$MACS['0C11050A6CFB']['process'][3] = '/app/bin/sip';
$MACS['0C11050A6CFB']['process'][4] = '/app/bin/dclient';
$MACS['0C11050A6CFB']['process'][5] = '/app/bin/autop';
$MACS['0C11050A6CFB']['process'][6] = '/app/bin/lighttpd';





//***********
$MACS['0C1105198E9B']['free_type'] = 0;
$MACS['0C1105198E9B']['cpu_type'] = 0;
$MACS['0C1105198E9B']['pid_type'] = 0;
$MACS['0C1105198E9B']['uptime_type'] = 0;
$MACS['0C1105198E9B']['pidmem_type'] = 0;
$MACS['0C1105198E9B']['process'][1] = 'com.akuvox.phone';
$MACS['0C1105198E9B']['process'][2] = '/app/bin/vaMain';
$MACS['0C1105198E9B']['process'][3] = '/app/bin/sip';
$MACS['0C1105198E9B']['process'][4] = '/app/bin/dclient';
$MACS['0C1105198E9B']['process'][5] = '/app/bin/netconfig';

//***********
$MACS['0C1105166D61']['free_type'] = 0;
$MACS['0C1105166D61']['cpu_type'] = 0;
$MACS['0C1105166D61']['pid_type'] = 0;
$MACS['0C1105166D61']['uptime_type'] = 0;
$MACS['0C1105166D61']['pidmem_type'] = 0;
$MACS['0C1105166D61']['process'][1] = 'com.akuvox.phone';
$MACS['0C1105166D61']['process'][2] = '/app/bin/vaMain';
$MACS['0C1105166D61']['process'][3] = '/app/bin/sip';
$MACS['0C1105166D61']['process'][4] = '/app/bin/dclient';
$MACS['0C1105166D61']['process'][5] = '/app/bin/netconfig';

//***********
$MACS['0C1105133672']['free_type'] = 0;
$MACS['0C1105133672']['cpu_type'] = 0;
$MACS['0C1105133672']['pid_type'] = 0;
$MACS['0C1105133672']['uptime_type'] = 0;
$MACS['0C1105133672']['pidmem_type'] = 0;
$MACS['0C1105133672']['process'][1] = 'com.akuvox.phone';
$MACS['0C1105133672']['process'][2] = '/app/bin/vaMain';
$MACS['0C1105133672']['process'][3] = '/app/bin/sip';
$MACS['0C1105133672']['process'][4] = '/app/bin/dclient';
$MACS['0C1105133672']['process'][5] = '/app/bin/netconfig';

//117.30.2.928
$MACS['0C11050A6CFB']['free_type'] = 0;
$MACS['0C11050A6CFB']['cpu_type'] = 0;
$MACS['0C11050A6CFB']['pid_type'] = 0;
$MACS['0C11050A6CFB']['uptime_type'] = 0;
$MACS['0C11050A6CFB']['pidmem_type'] = 0;
$MACS['0C11050A6CFB']['process'][1] = 'com.akuvox.phone';
$MACS['0C11050A6CFB']['process'][2] = 'com.akuvox.upgradeui';
$MACS['0C11050A6CFB']['process'][3] = '/app/bin/sip';
$MACS['0C11050A6CFB']['process'][4] = '/app/bin/dclient';
$MACS['0C11050A6CFB']['process'][5] = '/app/bin/autop';
$MACS['0C11050A6CFB']['process'][6] = '/app/bin/lighttpd';

//***********
$MACS['0C110505C453']['free_type'] = 0;
$MACS['0C110505C453']['cpu_type'] = 0;
$MACS['0C110505C453']['pid_type'] = 0;
$MACS['0C110505C453']['uptime_type'] = 0;
$MACS['0C110505C453']['pidmem_type'] = 0;
$MACS['0C110505C453']['process'][1] = 'com.akuvox.phone';
$MACS['0C110505C453']['process'][2] = 'com.akuvox.upgradeui';
$MACS['0C110505C453']['process'][3] = '/app/bin/sip';
$MACS['0C110505C453']['process'][4] = '/app/bin/dclient';
$MACS['0C110505C453']['process'][5] = '/app/bin/autop';
$MACS['0C110505C453']['process'][6] = '/app/bin/lighttpd';

//***********
$MACS['0C110507A44D']['free_type'] = 0;
$MACS['0C110507A44D']['cpu_type'] = 0;
$MACS['0C110507A44D']['pid_type'] = 0;
$MACS['0C110507A44D']['uptime_type'] = 0;
$MACS['0C110507A44D']['pidmem_type'] = 0;
$MACS['0C110507A44D']['process'][1] = 'com.akuvox.phone';
$MACS['0C110507A44D']['process'][2] = 'com.akuvox.upgradeui';
$MACS['0C110507A44D']['process'][3] = '/app/bin/sip';
$MACS['0C110507A44D']['process'][4] = '/app/bin/dclient';
$MACS['0C110507A44D']['process'][5] = '/app/bin/autop';
$MACS['0C110507A44D']['process'][6] = '/app/bin/lighttpd';

//48.30.2.530
$MACS['0C110507A7BD']['free_type'] = 0;
$MACS['0C110507A7BD']['cpu_type'] = 0;
$MACS['0C110507A7BD']['pid_type'] = 0;
$MACS['0C110507A7BD']['uptime_type'] = 0;
$MACS['0C110507A7BD']['pidmem_type'] = 0;
$MACS['0C110507A7BD']['process'][1] = 'com.akuvox.phone';
$MACS['0C110507A7BD']['process'][2] = 'com.akuvox.upgradeui';
$MACS['0C110507A7BD']['process'][3] = '/app/bin/sip';
$MACS['0C110507A7BD']['process'][4] = '/app/bin/dclient';
$MACS['0C110507A7BD']['process'][5] = '/app/bin/autop';
$MACS['0C110507A7BD']['process'][6] = '/app/bin/lighttpd';

//49.30.6.510
$MACS['0C1105133DCA']['free_type'] = 0;
$MACS['0C1105133DCA']['cpu_type'] = 0;
$MACS['0C1105133DCA']['pid_type'] = 0;
$MACS['0C1105133DCA']['uptime_type'] = 0;
$MACS['0C1105133DCA']['pidmem_type'] = 0;
$MACS['0C1105133DCA']['process'][1] = 'com.akuvox.phone';
$MACS['0C1105133DCA']['process'][2] = 'com.akuvox.upgradeui';
$MACS['0C1105133DCA']['process'][3] = '/app/bin/sip';
$MACS['0C1105133DCA']['process'][4] = '/app/bin/dclient';
$MACS['0C1105133DCA']['process'][5] = '/app/bin/autop';
$MACS['0C1105133DCA']['process'][6] = '/app/bin/lighttpd';




//福州软件部

//**********
$MACS['0C110515E317']['free_type'] = 1;
$MACS['0C110515E317']['cpu_type'] = 1;
$MACS['0C110515E317']['pid_type'] = 1;
$MACS['0C110515E317']['uptime_type'] = 0;
$MACS['0C110515E317']['pidmem_type'] = 0;
$MACS['0C110515E317']['process'][1] = 'vaMain';
$MACS['0C110515E317']['process'][2] = 'phone';

//***********
$MACS['0C11050B694C']['free_type'] = 1;
$MACS['0C11050B694C']['cpu_type'] = 1;
$MACS['0C11050B694C']['pid_type'] = 1;
$MACS['0C11050B694C']['uptime_type'] = 0;
$MACS['0C11050B694C']['pidmem_type'] = 0;
$MACS['0C11050B694C']['process'][1] = 'vaMain';
$MACS['0C11050B694C']['process'][2] = 'phone';

//***********
$MACS['0C110513867A']['free_type'] = 1;
$MACS['0C110513867A']['cpu_type'] = 1;
$MACS['0C110513867A']['pid_type'] = 1;
$MACS['0C110513867A']['uptime_type'] = 0;
$MACS['0C110513867A']['pidmem_type'] = 0;
$MACS['0C110513867A']['process'][1] = 'vaMain';
$MACS['0C110513867A']['process'][2] = 'phone';

//312.30.2.109
$MACS['0C11051BF1B2']['free_type'] = 1;
$MACS['0C11051BF1B2']['cpu_type'] = 1;
$MACS['0C11051BF1B2']['pid_type'] = 1;
$MACS['0C11051BF1B2']['uptime_type'] = 0;
$MACS['0C11051BF1B2']['pidmem_type'] = 0;
$MACS['0C11051BF1B2']['process'][1] = 'vaMain';
$MACS['0C11051BF1B2']['process'][2] = 'phone';

//912.30.1.84
$MACS['0C11051769D9']['free_type'] = 1;
$MACS['0C11051769D9']['cpu_type'] = 1;
$MACS['0C11051769D9']['pid_type'] = 1;
$MACS['0C11051769D9']['uptime_type'] = 0;
$MACS['0C11051769D9']['pidmem_type'] = 0;
$MACS['0C11051769D9']['process'][1] = 'vaMain';
$MACS['0C11051769D9']['process'][2] = 'phone';

//320.30.10.4
$MACS['0C110517A19E']['free_type'] = 1;
$MACS['0C110517A19E']['cpu_type'] = 1;
$MACS['0C110517A19E']['pid_type'] = 1;
$MACS['0C110517A19E']['uptime_type'] = 0;
$MACS['0C110517A19E']['pidmem_type'] = 0;
$MACS['0C110517A19E']['process'][1] = 'vaMain';
$MACS['0C110517A19E']['process'][2] = 'phone';

//320.30.4.112
$MACS['0C110517A3A4']['free_type'] = 1;
$MACS['0C110517A3A4']['cpu_type'] = 1;
$MACS['0C110517A3A4']['pid_type'] = 1;
$MACS['0C110517A3A4']['uptime_type'] = 0;
$MACS['0C110517A3A4']['pidmem_type'] = 0;
$MACS['0C110517A3A4']['process'][1] = 'vaMain';
$MACS['0C110517A3A4']['process'][2] = 'phone';

//111.30.10.7
$MACS['0C11050F1D6F']['free_type'] = 1;
$MACS['0C11050F1D6F']['cpu_type'] = 1;
$MACS['0C11050F1D6F']['pid_type'] = 0;
$MACS['0C11050F1D6F']['uptime_type'] = 0;
$MACS['0C11050F1D6F']['pidmem_type'] = 0;
$MACS['0C11050F1D6F']['process'][1] = '/app/bin/vaMain';
$MACS['0C11050F1D6F']['process'][2] = '/app/bin/phone';


//92.30.1.213
$MACS['0C110510F5A6']['free_type'] = 1;
$MACS['0C110510F5A6']['cpu_type'] = 1;
$MACS['0C110510F5A6']['pid_type'] = 0;
$MACS['0C110510F5A6']['uptime_type'] = 0;
$MACS['0C110510F5A6']['pidmem_type'] = 0;
$MACS['0C110510F5A6']['process'][1] = '/app/bin/phone';

//539.30.1.105
$MACS['0C110518B784']['free_type'] = 1;
$MACS['0C110518B784']['cpu_type'] = 1;
$MACS['0C110518B784']['pid_type'] = 1;
$MACS['0C110518B784']['uptime_type'] = 0;
$MACS['0C110518B784']['pidmem_type'] = 0;
$MACS['0C110518B784']['process'][1] = 'com.akuvox.phone';
$MACS['0C110518B784']['process'][2] = 'dclient';
$MACS['0C110518B784']['process'][3] = 'lighttpd';
$MACS['0C110518B784']['process'][4] = 'fcgiserver.fcgi';
$MACS['0C110518B784']['process'][5] = 'api.fcgi';

//915.30.1.534
$MACS['0C110515607D']['free_type'] = 1;
$MACS['0C110515607D']['cpu_type'] = 1;
$MACS['0C110515607D']['pid_type'] = 1;
$MACS['0C110515607D']['uptime_type'] = 0;
$MACS['0C110515607D']['pidmem_type'] = 0;
$MACS['0C110515607D']['process'][1] = 'com.akuvox.phone';
$MACS['0C110515607D']['process'][2] = 'dclient';
$MACS['0C110515607D']['process'][3] = 'lighttpd';
$MACS['0C110515607D']['process'][4] = 'fcgiserver.fcgi';
$MACS['0C110515607D']['process'][5] = 'api.fcgi';

//915.30.1.414
$MACS['0C11050F302B']['free_type'] = 1;
$MACS['0C11050F302B']['cpu_type'] = 1;
$MACS['0C11050F302B']['pid_type'] = 1;
$MACS['0C11050F302B']['uptime_type'] = 0;
$MACS['0C11050F302B']['pidmem_type'] = 0;
$MACS['0C11050F302B']['process'][1] = 'com.akuvox.phone';
$MACS['0C11050F302B']['process'][2] = 'dclient';
$MACS['0C11050F302B']['process'][3] = 'lighttpd';
$MACS['0C11050F302B']['process'][4] = 'fcgiserver.fcgi';
$MACS['0C11050F302B']['process'][5] = 'api.fcgi';

//29.30.2.920
$MACS['0C110508AEA9']['free_type'] = 1;
$MACS['0C110508AEA9']['cpu_type'] = 1;
$MACS['0C110508AEA9']['pid_type'] = 1;
$MACS['0C110508AEA9']['uptime_type'] = 0;
$MACS['0C110508AEA9']['pidmem_type'] = 0;
$MACS['0C110508AEA9']['process'][1] = 'com.akuvox.phone';
$MACS['0C110508AEA9']['process'][2] = 'dclient';
$MACS['0C110508AEA9']['process'][3] = 'lighttpd';
$MACS['0C110508AEA9']['process'][4] = 'fcgiserver.fcgi';
$MACS['0C110508AEA9']['process'][5] = 'api.fcgi';

//29.30.3.225
$MACS['0C1105092814']['free_type'] = 1;
$MACS['0C1105092814']['cpu_type'] = 1;
$MACS['0C1105092814']['pid_type'] = 1;
$MACS['0C1105092814']['uptime_type'] = 0;
$MACS['0C1105092814']['pidmem_type'] = 0;
$MACS['0C1105092814']['process'][1] = 'com.akuvox.phone';
$MACS['0C1105092814']['process'][2] = 'dclient';
$MACS['0C1105092814']['process'][3] = 'lighttpd';
$MACS['0C1105092814']['process'][4] = 'fcgiserver.fcgi';
$MACS['0C1105092814']['process'][5] = 'api.fcgi';

//916.30.1.709
$MACS['0C11050FD13B']['free_type'] = 1;
$MACS['0C11050FD13B']['cpu_type'] = 1;
$MACS['0C11050FD13B']['pid_type'] = 1;
$MACS['0C11050FD13B']['uptime_type'] = 0;
$MACS['0C11050FD13B']['pidmem_type'] = 0;
$MACS['0C11050FD13B']['process'][1] = 'com.akuvox.phone';
$MACS['0C11050FD13B']['process'][2] = 'dclient';
$MACS['0C11050FD13B']['process'][3] = 'lighttpd';
$MACS['0C11050FD13B']['process'][4] = 'fcgiserver.fcgi';
$MACS['0C11050FD13B']['process'][5] = 'api.fcgi';

//915.30.10.6
$MACS['0C11050FD13B']['free_type'] = 1;
$MACS['0C11050FD13B']['cpu_type'] = 1;
$MACS['0C11050FD13B']['pid_type'] = 1;
$MACS['0C11050FD13B']['uptime_type'] = 0;
$MACS['0C11050FD13B']['pidmem_type'] = 0;
$MACS['0C11050FD13B']['process'][1] = 'com.akuvox.phone';
$MACS['0C11050FD13B']['process'][2] = 'dclient';
$MACS['0C11050FD13B']['process'][3] = 'lighttpd';
$MACS['0C11050FD13B']['process'][4] = 'fcgiserver.fcgi';
$MACS['0C11050FD13B']['process'][5] = 'api.fcgi';


//916.30.1.618
$MACS['0C11050AA9C1']['free_type'] = 1;
$MACS['0C11050AA9C1']['cpu_type'] = 1;
$MACS['0C11050AA9C1']['pid_type'] = 1;
$MACS['0C11050AA9C1']['uptime_type'] = 0;
$MACS['0C11050AA9C1']['pidmem_type'] = 0;
$MACS['0C11050AA9C1']['process'][1] = 'com.akuvox.phone';
$MACS['0C11050AA9C1']['process'][2] = 'dclient';
$MACS['0C11050AA9C1']['process'][3] = 'lighttpd';
$MACS['0C11050AA9C1']['process'][4] = 'fcgiserver.fcgi';
$MACS['0C11050AA9C1']['process'][5] = 'api.fcgi';


//28.30.3.108
$MACS['0C1105100322']['free_type'] = 1;
$MACS['0C1105100322']['cpu_type'] = 1;
$MACS['0C1105100322']['pid_type'] = 1;
$MACS['0C1105100322']['uptime_type'] = 0;
$MACS['0C1105100322']['pidmem_type'] = 0;
$MACS['0C1105100322']['process'][1] = 'vaMain';
$MACS['0C1105100322']['process'][2] = 'phone';
$MACS['0C1105100322']['process'][2] = 'dclient';

//221.30.1.152
$MACS['0C110513509D']['free_type'] = 1;
$MACS['0C110513509D']['cpu_type'] = 1;
$MACS['0C110513509D']['pid_type'] = 1;
$MACS['0C110513509D']['uptime_type'] = 0;
$MACS['0C110513509D']['pidmem_type'] = 0;
$MACS['0C110513509D']['process'][1] = 'vaMain';
$MACS['0C110513509D']['process'][2] = 'phone';
$MACS['0C110513509D']['process'][2] = 'dclient';

//26.31.5.15
$MACS['0C1105091ABD']['free_type'] = 1;
$MACS['0C1105091ABD']['cpu_type'] = 1;
$MACS['0C1105091ABD']['pid_type'] = 1;
$MACS['0C1105091ABD']['uptime_type'] = 0;
$MACS['0C1105091ABD']['pidmem_type'] = 0;
$MACS['0C1105091ABD']['process'][1] = 'vaMain';
$MACS['0C1105091ABD']['process'][2] = 'phone';
$MACS['0C1105091ABD']['process'][2] = 'dclient';

//226.30.2.109
$MACS['0C11050B0E20']['free_type'] = 1;
$MACS['0C11050B0E20']['cpu_type'] = 1;
$MACS['0C11050B0E20']['pid_type'] = 1;
$MACS['0C11050B0E20']['uptime_type'] = 0;
$MACS['0C11050B0E20']['pidmem_type'] = 0;
$MACS['0C11050B0E20']['process'][1] = 'vaMain';
$MACS['0C11050B0E20']['process'][2] = 'phone';
$MACS['0C11050B0E20']['process'][2] = 'dclient';

//539.30.1.11
$MACS['0C11051A1903']['free_type'] = 1;
$MACS['0C11051A1903']['cpu_type'] = 1;
$MACS['0C11051A1903']['pid_type'] = 1;
$MACS['0C11051A1903']['uptime_type'] = 0;
$MACS['0C11051A1903']['pidmem_type'] = 0;
$MACS['0C11051A1903']['process'][1] = 'com.akuvox.phone';
$MACS['0C11051A1903']['process'][2] = 'dclient';
$MACS['0C11051A1903']['process'][3] = 'lighttpd';
$MACS['0C11051A1903']['process'][4] = 'fcgiserver.fcgi';
$MACS['0C11051A1903']['process'][5] = 'api.fcgi';

//915.30.1.305
$MACS['0C11050FE975']['free_type'] = 1;
$MACS['0C11050FE975']['cpu_type'] = 1;
$MACS['0C11050FE975']['pid_type'] = 1;
$MACS['0C11050FE975']['uptime_type'] = 0;
$MACS['0C11050FE975']['pidmem_type'] = 0;
$MACS['0C11050FE975']['process'][1] = 'com.akuvox.phone';
$MACS['0C11050FE975']['process'][2] = 'dclient';
$MACS['0C11050FE975']['process'][3] = 'lighttpd';
$MACS['0C11050FE975']['process'][4] = 'fcgiserver.fcgi';
$MACS['0C11050FE975']['process'][5] = 'api.fcgi';

//915.30.1.408
$MACS['0C1105118EFB']['free_type'] = 1;
$MACS['0C1105118EFB']['cpu_type'] = 1;
$MACS['0C1105118EFB']['pid_type'] = 1;
$MACS['0C1105118EFB']['uptime_type'] = 0;
$MACS['0C1105118EFB']['pidmem_type'] = 0;
$MACS['0C1105118EFB']['process'][1] = 'com.akuvox.phone';
$MACS['0C1105118EFB']['process'][2] = 'dclient';
$MACS['0C1105118EFB']['process'][3] = 'lighttpd';
$MACS['0C1105118EFB']['process'][4] = 'fcgiserver.fcgi';
$MACS['0C1105118EFB']['process'][5] = 'api.fcgi';

//29.30.3.108
$MACS['0C1105183203']['free_type'] = 1;
$MACS['0C1105183203']['cpu_type'] = 1;
$MACS['0C1105183203']['pid_type'] = 1;
$MACS['0C1105183203']['uptime_type'] = 0;
$MACS['0C1105183203']['pidmem_type'] = 0;
$MACS['0C1105183203']['process'][1] = 'com.akuvox.phone';
$MACS['0C1105183203']['process'][2] = 'dclient';
$MACS['0C1105183203']['process'][3] = 'lighttpd';
$MACS['0C1105183203']['process'][4] = 'fcgiserver.fcgi';
$MACS['0C1105183203']['process'][5] = 'api.fcgi';

//29.30.3.108
$MACS['0C1105078180']['free_type'] = 1;
$MACS['0C1105078180']['cpu_type'] = 1;
$MACS['0C1105078180']['pid_type'] = 1;
$MACS['0C1105078180']['uptime_type'] = 0;
$MACS['0C1105078180']['pidmem_type'] = 0;
$MACS['0C1105078180']['process'][1] = 'com.akuvox.phone';
$MACS['0C1105078180']['process'][2] = 'dclient';
$MACS['0C1105078180']['process'][3] = 'lighttpd';
$MACS['0C1105078180']['process'][4] = 'fcgiserver.fcgi';
$MACS['0C1105078180']['process'][5] = 'api.fcgi';

//29.30.2.907
$MACS['0C110507A582']['free_type'] = 1;
$MACS['0C110507A582']['cpu_type'] = 1;
$MACS['0C110507A582']['pid_type'] = 1;
$MACS['0C110507A582']['uptime_type'] = 0;
$MACS['0C110507A582']['pidmem_type'] = 0;
$MACS['0C110507A582']['process'][1] = 'com.akuvox.phone';
$MACS['0C110507A582']['process'][2] = 'dclient';
$MACS['0C110507A582']['process'][3] = 'lighttpd';
$MACS['0C110507A582']['process'][4] = 'fcgiserver.fcgi';
$MACS['0C110507A582']['process'][5] = 'api.fcgi';


//29.30.2.458
$MACS['0C110507819F']['free_type'] = 1;
$MACS['0C110507819F']['cpu_type'] = 1;
$MACS['0C110507819F']['pid_type'] = 1;
$MACS['0C110507819F']['uptime_type'] = 0;
$MACS['0C110507819F']['pidmem_type'] = 0;
$MACS['0C110507819F']['process'][1] = 'com.akuvox.phone';
$MACS['0C110507819F']['process'][2] = 'dclient';
$MACS['0C110507819F']['process'][3] = 'lighttpd';
$MACS['0C110507819F']['process'][4] = 'fcgiserver.fcgi';
$MACS['0C110507819F']['process'][5] = 'api.fcgi';

//29.30.2.454
$MACS['0C1105135EB6']['free_type'] = 1;
$MACS['0C1105135EB6']['cpu_type'] = 1;
$MACS['0C1105135EB6']['pid_type'] = 1;
$MACS['0C1105135EB6']['uptime_type'] = 0;
$MACS['0C1105135EB6']['pidmem_type'] = 0;
$MACS['0C1105135EB6']['process'][1] = 'com.akuvox.phone';
$MACS['0C1105135EB6']['process'][2] = 'dclient';
$MACS['0C1105135EB6']['process'][3] = 'lighttpd';
$MACS['0C1105135EB6']['process'][4] = 'fcgiserver.fcgi';
$MACS['0C1105135EB6']['process'][5] = 'api.fcgi';

//29.30.2.465
$MACS['0C11050A5936']['free_type'] = 1;
$MACS['0C11050A5936']['cpu_type'] = 1;
$MACS['0C11050A5936']['pid_type'] = 1;
$MACS['0C11050A5936']['uptime_type'] = 0;
$MACS['0C11050A5936']['pidmem_type'] = 0;
$MACS['0C11050A5936']['process'][1] = 'com.akuvox.phone';
$MACS['0C11050A5936']['process'][2] = 'dclient';
$MACS['0C11050A5936']['process'][3] = 'lighttpd';
$MACS['0C11050A5936']['process'][4] = 'fcgiserver.fcgi';
$MACS['0C11050A5936']['process'][5] = 'api.fcgi';

//29.30.2.22
$MACS['0C110507067B']['free_type'] = 1;
$MACS['0C110507067B']['cpu_type'] = 1;
$MACS['0C110507067B']['pid_type'] = 1;
$MACS['0C110507067B']['uptime_type'] = 0;
$MACS['0C110507067B']['pidmem_type'] = 0;
$MACS['0C110507067B']['process'][1] = 'com.akuvox.phone';
$MACS['0C110507067B']['process'][2] = 'dclient';
$MACS['0C110507067B']['process'][3] = 'lighttpd';
$MACS['0C110507067B']['process'][4] = 'fcgiserver.fcgi';
$MACS['0C110507067B']['process'][5] = 'api.fcgi';

//29.30.2.444
$MACS['0C1105104B7C']['free_type'] = 1;
$MACS['0C1105104B7C']['cpu_type'] = 1;
$MACS['0C1105104B7C']['pid_type'] = 1;
$MACS['0C1105104B7C']['uptime_type'] = 0;
$MACS['0C1105104B7C']['pidmem_type'] = 0;
$MACS['0C1105104B7C']['process'][1] = 'com.akuvox.phone';
$MACS['0C1105104B7C']['process'][2] = 'dclient';
$MACS['0C1105104B7C']['process'][3] = 'lighttpd';
$MACS['0C1105104B7C']['process'][4] = 'fcgiserver.fcgi';
$MACS['0C1105104B7C']['process'][5] = 'api.fcgi';

//29.30.3.111
$MACS['0C11051B579C']['free_type'] = 1;
$MACS['0C11051B579C']['cpu_type'] = 1;
$MACS['0C11051B579C']['pid_type'] = 1;
$MACS['0C11051B579C']['uptime_type'] = 0;
$MACS['0C11051B579C']['pidmem_type'] = 0;
$MACS['0C11051B579C']['process'][1] = 'com.akuvox.phone';
$MACS['0C11051B579C']['process'][2] = 'dclient';
$MACS['0C11051B579C']['process'][3] = 'lighttpd';
$MACS['0C11051B579C']['process'][4] = 'fcgiserver.fcgi';
$MACS['0C11051B579C']['process'][5] = 'api.fcgi';

//29.30.3.225
$MACS['0C1105092814']['free_type'] = 1;
$MACS['0C1105092814']['cpu_type'] = 1;
$MACS['0C1105092814']['pid_type'] = 1;
$MACS['0C1105092814']['uptime_type'] = 0;
$MACS['0C1105092814']['pidmem_type'] = 0;
$MACS['0C1105092814']['process'][1] = 'com.akuvox.phone';
$MACS['0C1105092814']['process'][2] = 'dclient';
$MACS['0C1105092814']['process'][3] = 'lighttpd';
$MACS['0C1105092814']['process'][4] = 'fcgiserver.fcgi';
$MACS['0C1105092814']['process'][5] = 'api.fcgi';

//29.30.2.313
$MACS['0C11050F79BD']['free_type'] = 1;
$MACS['0C11050F79BD']['cpu_type'] = 1;
$MACS['0C11050F79BD']['pid_type'] = 1;
$MACS['0C11050F79BD']['uptime_type'] = 0;
$MACS['0C11050F79BD']['pidmem_type'] = 0;
$MACS['0C11050F79BD']['process'][1] = 'com.akuvox.phone';
$MACS['0C11050F79BD']['process'][2] = 'dclient';
$MACS['0C11050F79BD']['process'][3] = 'lighttpd';
$MACS['0C11050F79BD']['process'][4] = 'fcgiserver.fcgi';
$MACS['0C11050F79BD']['process'][5] = 'api.fcgi';

//916.30.1.511
$MACS['0C11050DF7FD']['free_type'] = 1;
$MACS['0C11050DF7FD']['cpu_type'] = 1;
$MACS['0C11050DF7FD']['pid_type'] = 1;
$MACS['0C11050DF7FD']['uptime_type'] = 0;
$MACS['0C11050DF7FD']['pidmem_type'] = 0;
$MACS['0C11050DF7FD']['process'][1] = 'com.akuvox.phone';
$MACS['0C11050DF7FD']['process'][2] = 'dclient';
$MACS['0C11050DF7FD']['process'][3] = 'lighttpd';
$MACS['0C11050DF7FD']['process'][4] = 'fcgiserver.fcgi';
$MACS['0C11050DF7FD']['process'][5] = 'api.fcgi';

//916.30.1.503
$MACS['0C11050AA9B9']['free_type'] = 1;
$MACS['0C11050AA9B9']['cpu_type'] = 1;
$MACS['0C11050AA9B9']['pid_type'] = 1;
$MACS['0C11050AA9B9']['uptime_type'] = 0;
$MACS['0C11050AA9B9']['pidmem_type'] = 0;
$MACS['0C11050AA9B9']['process'][1] = 'com.akuvox.phone';
$MACS['0C11050AA9B9']['process'][2] = 'dclient';
$MACS['0C11050AA9B9']['process'][3] = 'lighttpd';
$MACS['0C11050AA9B9']['process'][4] = 'fcgiserver.fcgi';
$MACS['0C11050AA9B9']['process'][5] = 'api.fcgi';













//家居

//**********
$MACS['0C11051882E7']['free_type'] = 2;
$MACS['0C11051882E7']['cpu_type'] = 2;
$MACS['0C11051882E7']['pid_type'] = 1;
$MACS['0C11051882E7']['uptime_type'] = 0;
$MACS['0C11051882E7']['pidmem_type'] = 0;
$MACS['0C11051882E7']['process'][1] = 'com.akubela.panel';
$MACS['0C11051882E7']['process'][2] = 'com.akuvox.upgradeui';
$MACS['0C11051882E7']['process'][3] = 'sip';
$MACS['0C11051882E7']['process'][4] = 'netcast';


//**********
$MACS['0C110517AB36']['free_type'] = 2;
$MACS['0C110517AB36']['cpu_type'] = 2;
$MACS['0C110517AB36']['pid_type'] = 1;
$MACS['0C110517AB36']['uptime_type'] = 0;
$MACS['0C110517AB36']['pidmem_type'] = 0;
$MACS['0C110517AB36']['process'][1] = 'com.akubela.panel';
$MACS['0C110517AB36']['process'][2] = 'com.akuvox.upgradeui';
$MACS['0C110517AB36']['process'][3] = 'sip';
$MACS['0C110517AB36']['process'][4] = 'netcast';

//*********
$MACS['0C110518A130']['free_type'] = 2;
$MACS['0C110518A130']['cpu_type'] = 2;
$MACS['0C110518A130']['pid_type'] = 1;
$MACS['0C110518A130']['uptime_type'] = 0;
$MACS['0C110518A130']['pidmem_type'] = 0;
$MACS['0C110518A130']['process'][1] = 'phone';
$MACS['0C110518A130']['process'][2] = 'vaMain';
$MACS['0C110518A130']['process'][3] = 'sip';
$MACS['0C110518A130']['process'][4] = 'netconfig';



