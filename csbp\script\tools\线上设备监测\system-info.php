<?php
require_once(dirname(__FILE__) . '/macs.php');

function getfree($mac, $type){
    if($type == 0)
    {
        $resp = shell_exec("/usr/local/akcs/csmain/bin/csmain_cli -x \"$mac;top -n1 | grep 'Mem:' | grep -v grep |awk '{if(\\$2 ~ /total/) {print \\$3} else {print \\$2}}'\"");
    }
    else if($type == 1)
    {
        $resp = shell_exec("/usr/local/akcs/csmain/bin/csmain_cli -x \"$mac;busybox free | grep 'Mem:' | busybox awk '{print \\$3}'\"");
    }
    else
    {
        $resp = shell_exec("/usr/local/akcs/csmain/bin/csmain_cli -x \"$mac;cat /proc/meminfo | grep 'MemAvailable:' | awk '{print \\$2}'\"");
    }
    $value = trim($resp);
    $result=shell_exec("echo \"$value\" | grep -vE 'Content|Seq' ");
    $result = trim($result);
    $result = number_format(intval($result)/1024,2);
    return $result;
}

function getcpu($mac, $type){
    if($type == 0)
    {
        $resp = shell_exec("/usr/local/akcs/csmain/bin/csmain_cli -x \"$mac;top -n1 |grep 'CPU:' | grep -v grep | awk '{print 100%-\\$8}'\"");
    }
    else if($type == 1)
    {
        $resp = shell_exec("/usr/local/akcs/csmain/bin/csmain_cli -x \"$mac;busybox top -n1 |grep 'CPU:' | grep -v grep | busybox awk '{print 100%-\\$8}'\"");
    }
    else
    {
        $resp = shell_exec("/usr/local/akcs/csmain/bin/csmain_cli -x \"$mac;top -n1 | grep 'cpu' | grep -v grep | awk -F '[% ]+' '{print \\$9}'\"");      
    }
    $value = trim($resp);
    $result=shell_exec("echo \"$value\" | grep -vE 'Content|Seq' ");
    $result = trim($result);
    if($type == 2)  //家居 4核cpu
    {
        $result = ($result)/4;
    }
    return $result;
}

function getuptime($mac, $type){
    if($type == 0)
    {
        $resp = shell_exec("/usr/local/akcs/csmain/bin/csmain_cli -x \"$mac;uptime|busybox awk -F 'up |, ' '{print \\$2}'\"");
    }
    $value = trim($resp);
    $result=shell_exec("echo \"$value\" | grep -vE 'Content|Seq' ");
    $result = trim($result);
    if(strstr($result, 'day'))
    {
        $result = preg_replace('/[^0-9]/', '', $result); //提取数字
    }
    else
    {
        $result = 1;    //不足一天按一天
    }
    return $result;
}

function getpid($mac, $process, $type){
    if($type == 0)
    {
        $resp = shell_exec("/usr/local/akcs/csmain/bin/csmain_cli -x \"$mac;ps| grep $process |grep -v grep| awk '{print \\$1}'\"");
    }
    else
    {
        $resp = shell_exec("/usr/local/akcs/csmain/bin/csmain_cli -x \"$mac;pidof $process\"");
    }

    $value = trim($resp);
    $result=shell_exec("echo \"$value\" | grep -vE 'Content|Seq' ");
    $result = trim($result);
    return $result;
}

function getpidmem($mac, $pid, $type){
    if($type == 0)
    {
        $resp = shell_exec("/usr/local/akcs/csmain/bin/csmain_cli -x \"$mac;cat /proc/$pid/status|grep 'VmRSS'|busybox awk '{print \\$2}'\"");
    }
    $value = trim($resp);
    $result=shell_exec("echo \"$value\" | grep -vE 'Content|Seq' ");
    $result = trim($result);
    $result = number_format($result/1024,2);
    return $result;
}


function curlRequest($url, $data)
{
    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_HEADER, 0);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($curl, CURLOPT_POST, 1);

    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
    //超时，只需要设置一个秒的数量就可以
    curl_setopt($curl, CURLOPT_TIMEOUT, 10);
    curl_setopt($curl, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4);
    curl_setopt($curl, CURLOPT_POSTFIELDS, $data);

    $output = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    echo $output."\n";
}


foreach ($MACS as $mac => $value){
    $data = [];
    $data['shijian'] = date('YmdHis', time());
    $data['Other_name'] = $mac;
    $data['Cpu'] = getcpu($mac, $value['cpu_type']);
    $data['Uptime'] = getuptime($mac, $value['uptime_type']);
    $data['Mem'] = getfree($mac, $value['free_type']);
    $pid_type = $value['pid_type'];
    $pidmem_type = $value['pidmem_type'];
    $processs_list = $value['process'];
    foreach($processs_list as $key => $process)
    {
        $data['Pid'.$key.'_name'] = $process;
        $pid = getpid($mac, $process, $pid_type);
        $data['Pid'.$key.'_num'] = $pid;
        $data['Pid'.$key.'_mem'] = getpidmem($mac, $pid, $pidmem_type);     
    }
    $json_data = json_encode($data);
    echo json_encode($data)."\n";
    curlRequest("http://117.25.162.76:8083/customer-devinfo",$data);
}


