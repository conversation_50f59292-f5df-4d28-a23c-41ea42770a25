<?php
//pay by installer指定所有邮件都发送给自己
date_default_timezone_set("PRC");
function getDB()
{
    $dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $dbport = 3306;
    
    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
	$dbConnection->query('set names utf8;');
    return $dbConnection;
}

$db = getDB();
$data = array();
$diss = array();

const STATIS_FILE = "active.csv";
$begin_times = "2021-01-01";
$end_times = "2021-06-30";
$sth = $db->prepare("select ParentID,Role  From PersonalAccount where ActiveTime > \"$begin_times\" and  ActiveTime < \"$end_times\" and (Role =10 or Role=20)");
$sth->execute();
$active_list = $sth->fetchALL(PDO::FETCH_ASSOC);
foreach ($active_list as $row => $account)
{
	$sth1 = $db->prepare("select AA.Account From Account A left join Account AA on A.ParentID=AA.ID where A.ID=:ID;");
	$sth1->bindParam(':ID', $account['ParentID'], PDO::PARAM_INT);
	$sth1->execute();
	$exist = $sth1->fetch(PDO::FETCH_ASSOC);
	if($exist)
	{
		$t = array();
		$t["dis"] = $exist['Account'];
		$t["role"] = $account['Role'];


		$d = array();
		$d["name"] = $exist['Account'];
		$d["10"] = array();
		$d["20"] = array();
		$diss[$exist['Account']] = $d;

		array_push($data, $t);
	}
}

foreach ($diss as $dis => $value) {
	foreach ($data as $key) {
		if ($dis == $key["dis"])
		{
			if ($key["role"] == 10)
			{
				array_push($diss[$dis]["10"], 1);
			}
			if ($key["role"] == 20)
			{
				array_push($diss[$dis]["20"], 1);
			}			
		}
	}
}

if (file_exists(STATIS_FILE)) {
    shell_exec("echo > ". STATIS_FILE);
} 
function STATIS_WRITE($content)
{
	file_put_contents(STATIS_FILE, $content, FILE_APPEND);
	file_put_contents(STATIS_FILE, "\n", FILE_APPEND);
}

$all_active = 0;
$all_s = 0;
$all_c = 0;
STATIS_WRITE("Dis, All, single, community");
foreach ($diss as $dis => $value) {
	$name = $value["name"];
	$s = count($value["10"]);
	$c = count($value["20"]);
	$all = $s + $c;

	$all_active += $all;
	$all_s += $s;
	$all_c += $c;

	$txt = "$name,$all,$s,$c";
	STATIS_WRITE($txt);


}

STATIS_WRITE("");
STATIS_WRITE("All,$all_active,$all_s,$all_c");









