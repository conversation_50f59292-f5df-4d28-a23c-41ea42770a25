<?php

$sql = 
"select CU.UnitName, CR.<PERSON>ame, NM.<PERSON>, MainAccount.Active, MainAccount.appLoginStatus, CPK.Code As Pin,  CPRK.Code As RFKey,
CASE WHEN F.FaceUrl IS NULL THEN FALSE ELSE TRUE END AS FaceReg
from `AKCS`.`PersonalAccount` MainAccount 
left join  `AKCS`.`CommunityRoom` CR on MainAccount.RoomID = CR.ID
left join `AKCS`.`CommunityUnit` CU on MainAccount.UnitID = CU.ID 
left join `AKCS`.`CommPerPrivateKey` CPK on MainAccount.Account = CPK.Account
left join `AKCS`.`CommPerRfKey` CPRK on MainAccount.Account = CPRK.Account
left join `AKCS`.`FaceMng` F on MainAccount.ID = F.PersonalAccountID
left join `AKCSMapping`.`NameMapping` NM on md5(MainAccount.Name) = NM.EnColumnMd5
where MainAccount.Role = 20 and MainAccount.ParentUUID = 'cn-82c59704182311efbaeb00163e12a735' and MainAccount.Special = 0 
union all  
select CU.UnitName, CR.RoomName, NM.Decolumn, SubAccount.Active, SubAccount.appLoginStatus, CPK.Code As Pin,  CPRK.Code As RFKey,
CASE WHEN F.FaceUrl IS NULL THEN FALSE ELSE TRUE END AS FaceReg 
from `AKCS`.`PersonalAccount` MainAccount 
left join `AKCS`.`CommunityRoom` CR on MainAccount.RoomID = CR.ID 
left join `AKCS`.`CommunityUnit` CU on MainAccount.UnitID = CU.ID 
left join `AKCS`.`PersonalAccount` SubAccount on MainAccount.UUID = SubAccount.ParentUUID 
left join `AKCS`.`CommPerPrivateKey` CPK on SubAccount.Account = CPK.Account
left join `AKCS`.`CommPerRfKey` CPRK on SubAccount.Account = CPRK.Account
left join `AKCS`.`FaceMng` F on SubAccount.ID = F.PersonalAccountID
left join `AKCSMapping`.`NameMapping` NM on md5(SubAccount.Name) = NM.EnColumnMd5 
where SubAccount.Role = 21  and MainAccount.ParentUUID = 'cn-82c59704182311efbaeb00163e12a735' and MainAccount.Special = 0 order by UnitName,RoomName;";

function getDB()
{
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbip = "db.akcs.ccloud.akcs.inner";
    $dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbip;port=$dbport;dbname=AKCS";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

try {
    $db = getDB();
    $sth = $db->prepare($sql);
    $ret = $sth->execute();

    if ($ret) {
        $results = $sth->fetchAll(PDO::FETCH_ASSOC);
        $filePath = '/root/czx/output.csv';
        $file = fopen($filePath, 'w');

        if ($file) {
            // Write the header row
            $header = ['楼栋', '房间', '用户', 'App是否激活', 'App是否登录过', 'Pin', 'RfCard', '人脸'];
            if (!empty($results)) {
                fputcsv($file, $header);
            }

            // Write the data rows
            foreach ($results as $row) {
                fputcsv($file, $row);
            }

            fclose($file);
            echo "Data successfully written to $filePath";
            $STATIS_FILE = "echo '金碧文华项目情况' | mutt -s '金碧文华项目情况' -a /root/czx/output.csv  -b <EMAIL> -c <EMAIL>";
            shell_exec($STATIS_FILE);
        } else {
            echo "Failed to open file for writing.";
        }
    } else {
        echo "Failed to execute query.";
    }
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage();
}



