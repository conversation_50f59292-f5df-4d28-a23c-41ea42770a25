<?php


function curlRequest($url, $data)
{
    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_HEADER, 0);
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($curl, CURLOPT_POST, 1);

    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
    //超时，只需要设置一个秒的数量就可以
    curl_setopt($curl, CURLOPT_TIMEOUT, 10);
    curl_setopt($curl, CURLOPT_IPRESOLVE, CURL_IPRESOLVE_V4);
    curl_setopt($curl, CURLOPT_POSTFIELDS, $data);

    $output = curl_exec($curl);
    $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
    curl_close($curl);
    //echo $output."\n";
}

function getDB()
{
    $dbhost = "127.0.0.1";
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";

    $mysql_conn_string = "mysql:host=$dbhost;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->exec("set names utf8mb4");
    return $dbConnection;
}

$db = getDB();

$firm_arr = [101,103,205,108,18,216];
$MACs = file('mac_list.txt', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
foreach($MACs as $mac){
$officeSql = "select * from Devices where MAC = '$mac'";
$sth = $db->prepare($officeSql);
$sth->execute();
$officeData = $sth->fetch(PDO::FETCH_ASSOC);
if($officeData){
    $status = $officeData['Status'];
    if($status == 0)
    {
        echo "$mac is offline\n";
        continue;
    }
    echo "$mac is success\n";
    $firm = $officeData['Firmware'];
    $firm_parts = explode('.', $firm);
    if(in_array($firm_parts[0], $firm_arr)){
        $location = '/config/Door/Log/EventLog.db';
    }
    else{
        $location = '/config/Door/Key/EventLog.db';
    }
    $accsrv = $officeData['AccSrvID'];
    $data = [];
    $data['file_type'] = 3;
    $data['user'] = 'akuvox';
    $data['passwd'] = 'pu6HYKvTkyGstq';
    $data['ip'] = '*************';
    $data['port'] = 21;
    $data['mac'] = $mac;
    $data['file_name'] = "maintenance-ecloud-$mac.tar.gz";
    $data['location'] = $location;
    $json_data = json_encode($data, JSON_UNESCAPED_SLASHES);
    //echo $json_data."\n";
    curlRequest("http://$accsrv:9998/getDevFile",$json_data);
    sleep(1);
}

}



