<?php
date_default_timezone_set('PRC');
const STATIS_FILE = "/tmp/update_dev.txt";
shell_exec("touch ". STATIS_FILE);
chmod(STATIS_FILE, 0777);
if (file_exists(STATIS_FILE)) {
    //unlink(STATIS_FILE);
    shell_exec("echo > ". STATIS_FILE);
} 

function STATIS_TRACE($content)
{
	file_put_contents(STATIS_FILE, $content, FILE_APPEND);
	file_put_contents(STATIS_FILE, "\n", FILE_APPEND);
}

function getDB()
{
    $dbhost = "*************";
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $dbport = 3308;
    
    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}
$db = getDB();

#0C110510209E
#0C11050A732A
#=0C1105182383

$macs = ["0C1105182383","0C11050A726C","0C11050F3027","215200101FFF","215200201FFF","0C11050A727D","0C11050AB35D","0C11050716A2","0C11050AB317","0C11050AB32E","0C11050D8831","0C11050AEC8E","0C11050AEC7F","0C11050AECA5","0C11050AECB5","0C11050AEC77","0C11050AECD2","0C11050A639F","0C11050927E3","0C11050B1FD6","0C11050A7265","0C11050A7324","0C1105105CEE","0C11050AE0C5","0C11050AE1E6","0C11050AE1D1","0C11050AE0BC","0C11050AE0C6","0C11050B296D","0C11050AE122","0C11050AE0BB","0C11050AE1EB","0C11050A0C04","0C11050CD566","0C11050B6B17","0C110512EF0D","0C110512BD27","0C110512BD26","0C11050A63C2","0C11050A63A8","0C11050A638D","0C11050C3660","0C11050B6AD5","0C11050BDD0B","0C11050B121C","0C11050B11C4","0C11050BDCDC","0C11050AB2C4","0C11050BDD23","0C11050AB2DB","0C11050BDCE3","0C11050BDD04","0C11050BDCB0","0C11050DD641","0C11050DD635","0C11050DD5F0","0C11050DD589","0C11050D4F00","0C11050B29F8","0C11050BDDA7","0C110510202D","0C1105127A27","0C11051277AE","0C110510E4A4","0C11051145FB","0C11050A639C","0C11050D8819","0C11050DE1D2","0C11050ACE99","0C110508EA3F","0C110515D3AD","0C110515D3A6","0C11050D4F04","0C110511012C","0C1105110128","0C11050F300F","0C11050F26FC","0C110514D80A","0C11050EA804","0C11050F86B5","0C11050F88D3","0C11050EAD0D","0C1105105728","0C11050B6AF3","0C11050BC198","0C11051B2088","0C11050CD5B4","0C11050C3042","0C11050FCB04","0C11050FA10A","0C11051020E2","0C11050F743D","0C11051059FF","0C11050C3053","0C11050FE96D","0C110510212A","0C11050BC19B","0C11050EAD0B","0C11050EAF0E","0C11051059A3","0C110510588C","0C11050D3380","0C11050FE969"];




foreach ($macs as $row => $mac)
{	
    echo "start: $mac\n";
    $sth12 = $db->prepare("select Mac,Firmware,Status From Devices where Mac=:Mac");
    $sth12->bindParam(':Mac', $mac, PDO::PARAM_STR);
    $sth12->execute();
    $info = $sth12->fetch(PDO::FETCH_ASSOC);
    $fm = $info["Firmware"];
    $status = $info["Status"];
    if ($status == 0)
    {
        echo "mac offline\n";
        continue;
    }
    
    $parts = explode(".", $fm);
    $face_list = ["29","105","915","116"];
    if (isset($parts[0])) {
        $fm_n = $parts[0];
        if (in_array($fm_n, $face_list)) {

            $shxxx = "python3.4 /bin/configcat reboot $mac";
            shell_exec($shxxx);
            STATIS_TRACE($mac);
            echo "end: $mac\n";
         
        }
    }     
    

}



