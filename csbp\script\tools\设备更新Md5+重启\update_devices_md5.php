<?php
date_default_timezone_set('PRC');
const STATIS_FILE = "/tmp/update_dev.txt";
shell_exec("touch ". STATIS_FILE);
chmod(STATIS_FILE, 0777);
if (file_exists(STATIS_FILE)) {
    //unlink(STATIS_FILE);
    shell_exec("echo > ". STATIS_FILE);
} 

function STATIS_TRACE($content)
{
	file_put_contents(STATIS_FILE, $content, FILE_APPEND);
	file_put_contents(STATIS_FILE, "\n", FILE_APPEND);
}

function getDB()
{
    $dbhost = "*************";
    $dbuser = "dbuser01";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
    $dbport = 3308;
    
    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}
$db = getDB();


$macs = ["0C11050ACE94","0C11050AE0BB","0C11050AE0BC","0C11050AE0C5","0C11050AE0C6","0C11050AE122","0C11050AE1D1","0C11050AE1E6","0C11050AE1EB","0C11050AEC77","0C11050AEC7F","0C11050AEC8E","0C11050AECA5","0C11050AECB5","0C11050AECD2","0C11050B0DDC","0C11050B1FD6","0C11050B2001","0C11050B296D","0C11050BDE4E","0C11050C3540","0C11050C3542","0C11050C3544","0C11050C3553","0C11050C355B","0C11050C355F","0C11050C3560","0C11050C3562","0C11050C3567","0C11050C3568","0C11050C3569","0C11050C357C","0C11050C3589","0C11050C3593","0C11050C359D","0C11050C35A3","0C11050C35A9","0C11050C35B2","0C11050C35B9","0C11050C35C7","0C11050C35D6","0C11050C35E1","0C11050C35E3","0C11050C35F1","0C11050C35F5","0C11050CDA09","0C11050CDA0A","0C11050CDA0B","0C11050CDA0C","0C11050CDA18","0C11050CDA19","0C11050CDA1B","0C11050CDA1F","0C11050CDA22","0C11050CDA28","0C11050CDA3E","0C11050CDA40","0C11050CDA41","0C11050CDA47","0C11050CDA4D","0C11050CDA4E","0C11050CDA51","0C11050CDA55","0C11050CDA5A","0C11050CDA60","0C11050CDF0F","0C11050D8EFC","0C11050D8F03","0C11050DDB9D","0C11050DDBAC","0C11050DDBED","0C11050EAC0B","0C11050EAD0B","0C11050F743D","0C11050FE969","0C1105102032","0C1105102038","0C110510205F","0C110510209E","0C11051020BB","0C11051020C0","0C11051020DC","0C1105102103","0C1105102125","0C1105102170","0C1105103265","0C1105103293","0C11051032B0","0C11051032BB","0C11051032BD","0C11051032C4","0C11051032C5","0C11051032D6","0C11051032E0","0C11051032E5","0C1105103CAF","0C110510E4A4","0C11051100BF","0C110512BCAC","0C110512BD26","0C110512BD27"];



foreach ($macs as $mac)
{	
    echo "start: $mac\n";
    $sth12 = $db->prepare("select Mac,Firmware,Status From Devices where Mac=:Mac");
    $sth12->bindParam(':Mac', $mac, PDO::PARAM_STR);
    $sth12->execute();
    $info = $sth12->fetch(PDO::FETCH_ASSOC);
    $fm = $info["Firmware"];
    $status = $info["Status"];
    if ($status == 0)
    {
        echo "mac offline\n";
        continue;
    }
    
    
    $parts = explode(".", $fm);
    $face_list = ["29","105","915","116"];
    if (isset($parts[0])) {
        $fm_n = $parts[0];
        if (in_array($fm_n, $face_list)) {
            echo "$fm \n";
            $shxxx = "/usr/local/akcs/csmain/bin/csmain_cli -x \"$mac;/app/bin/inifile_wr w /config/Door/Setting.conf UPDATE FACEIDMD5 ''\"";
            shell_exec($shxxx);            
        }
    }     

    $shxxx = "/usr/local/akcs/csmain/bin/csmain_cli -x \"$mac;/app/bin/inifile_wr r /config/Door/Setting.conf UPDATE RfidMD5 ''\"";
    echo shell_exec($shxxx); 
   
    $shxxx = "/usr/local/akcs/csmain/bin/csmain_cli -x \"$mac;/app/bin/inifile_wr w /config/Door/Setting.conf UPDATE RfidMD5 ''\"";
    shell_exec($shxxx);   
   
    $shxxx = "/usr/local/akcs/csmain/bin/csmain_cli -x \"$mac;/app/bin/inifile_wr r /config/Door/Setting.conf UPDATE ConfigMD5 ''\"";
    echo shell_exec($shxxx); 
   
    $shxxx = "/usr/local/akcs/csmain/bin/csmain_cli -x \"$mac;/app/bin/inifile_wr w /config/Door/Setting.conf UPDATE ConfigMD5 ''\"";
    shell_exec($shxxx);  
    
    $shxxx = "/usr/local/akcs/csmain/bin/csmain_cli -x \"$mac;/app/bin/inifile_wr w /config/Door/Setting.conf UPDATE PrivatekeyMD5 ''\"";
    shell_exec($shxxx);
   
    $shxxx = "/usr/local/akcs/csmain/bin/csmain_cli -x \"$mac;/app/bin/inifile_wr w /config/Door/Setting.conf UPDATE PrivatekeyMD5 ''\"";
    shell_exec($shxxx);

    $shxxx = "python3.4 /bin/configcat updatemac $mac";
    shell_exec($shxxx);
    STATIS_TRACE($mac);
    echo "end: $mac\n";
    sleep(3);
}



