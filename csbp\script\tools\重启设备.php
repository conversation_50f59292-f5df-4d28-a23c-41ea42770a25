<?php
global $STATIS_FILE; 
$STATIS_FILE = "/tmp/reboot_dev_".date("Y-m-d").".csv";
shell_exec("touch ". $STATIS_FILE);
chmod($STATIS_FILE, 0777);
if (file_exists($STATIS_FILE)) {
	shell_exec("echo > ". $STATIS_FILE);
} 

function TRACE($content)
{  
	global $STATIS_FILE; 
	@file_put_contents($STATIS_FILE, $content, FILE_APPEND);
	@file_put_contents($STATIS_FILE, "\n", FILE_APPEND);
}

function getDB(){
	$dbhost = "127.0.0.1";	//需在mysql主机执行
    $dbuser = "root";
    $dbpass = "Ak@56@<EMAIL>";
    $dbname = "AKCS";
	$dbport = 3306;

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    return $dbConnection;
}

function rebootdev(){
    try{
        $db = getDB();
        $sth = $db->prepare("select Mac,CreateTime,status,AccSrvID From PersonalDevices where IPAddress='' and Gateway!='' and status=1;");
  
        $sth->execute();
        $list = $sth->fetchALL(PDO::FETCH_ASSOC);
        foreach($list as $key => $value){
            $accsrv = $value['AccSrvID'];
            $mac = $value['Mac'];
            echo($mac."\n");	
            $body = '{\"mac\":\"'.$mac.'\"}';
            $cmd="curl -s -d \"$body\" \"http://$accsrv:9998/rebootDev\" >/dev/null &";
            shell_exec($cmd);
            usleep(300000);	
        }
    }catch(PDOException $e) {
        echo 'error: '.$e->getMessage();
    }
}
rebootdev();

?>
