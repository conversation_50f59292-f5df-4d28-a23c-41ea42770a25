#!/bin/sh

old_gate_code=62
new_gate_code=63

old_inner_ip=*************
new_inner_ip=*************

old_ex_ip=************
new_ex_ip=************

old_domain=test84.akuvox.com
new_domain=aijitest.akuvox.com

old_gate_domain=gate.test84.akuvox.com
new_gate_domain=gate.aijitest.akuvox.com


#read 删除不能用问题
stty erase ^H
font_off(){
	echo -en "\033[0m"
}
yellow(){
    echo -e "\033[33m$1\033[0m"
	font_off
}

echo ""
yellow "install server with images step:" 
yellow "step1:change this shell script old/new  vars infomation." 
yellow "step2:upload akcs.sql." 
yellow "step3:when shell exec success,reboot server." 
yellow "step4:reinstall cert for domain." 

yellow "Please input 1/0 to install server: \c" 
read INSTALL
if [ ! $INSTALL -eq 1 ];then
    yellow "uninstall server." 
	exit
fi

if [ ! -f akcs.sql ];then
    yellow "not found akcs.sql" 
	exit
fi


mysqldump -h 127.0.0.1 -u root -pAk@56@<EMAIL> -d freeswitch> freeswitch-schema.sql
mysqldump -h 127.0.0.1 -u root -pAk@56@<EMAIL> -d AKCS> AKCS-schema.sql

mysql -h 127.0.0.1 -u root -pAk@56@<EMAIL> -e "drop database freeswitch;"
mysql -h 127.0.0.1 -u root -pAk@56@<EMAIL> -e "create  database freeswitch;"
mysql -h 127.0.0.1 -f -u root -pAk@56@<EMAIL> freeswitch < freeswitch-schema.sql

mysql -h 127.0.0.1 -u root -pAk@56@<EMAIL> -e "drop database AKCS;"
mysql -h 127.0.0.1 -u root -pAk@56@<EMAIL> -e "create  database AKCS;"
mysql -h 127.0.0.1 -u root -pAk@56@<EMAIL> AKCS < akcs.sql

/bin/dboperation create_rw dbuser01 Ak@56@<EMAIL>  $new_inner_ip
/bin/dboperation-freeswitch create_rw dbuser01 Ak@56@<EMAIL> $new_inner_ip


cd /usr/local/etcd/
rm -rf data  wal
sed -i "s/$old_inner_ip/$new_inner_ip/g" conf/*conf

cd /usr/local/nsqd
sed -i "s/$old_inner_ip/$new_inner_ip/g" */*.sh

cd /usr/local/nsqlookupd
sed -i "s/$old_inner_ip/$new_inner_ip/g" */*.sh

cd /usr/local/akcs/
sed -i "s/gateway_code=$old_gate_code/gateway_code=$new_gate_code/g" csroute/conf/csroute.conf
sed -i "s/gateway_code=$old_gate_code/gateway_code=$new_gate_code/g" csmain/conf/csmain.conf

cd /var/www/html/apache-v3.0/config
GATEWAY_NUMBER_REPLACE_LINE="const SERVERNUMBER = \"${new_gate_code}\";"
sed -i "s/^.*SERVERNUMBER.*/${GATEWAY_NUMBER_REPLACE_LINE}/g" base.php

cd /usr/local/zookeeper
sed -i "s/$old_inner_ip/$new_inner_ip/g" conf/zoo.cfg

cd /usr/local/kafka
sed -i "s/$old_inner_ip/$new_inner_ip/g" */*

cd /usr/local/akcs/
sed -i "s/$old_inner_ip/$new_inner_ip/g" */*/*conf
sed -i "s/$old_inner_ip/$new_inner_ip/g" */*.php
sed -i "s/$old_ex_ip/$new_ex_ip/g" */*/*conf
sed -i "s/$old_domain/$new_domain/g" */*/*conf

cd /etc
sed  -i "s/$old_inner_ip/$new_inner_ip/g" ip
sed  -i "s/$old_ex_ip/$new_ex_ip/g" ip

sed  -i "s/$old_ex_ip/$new_ex_ip/g" *.conf
sed  -i "s/$old_ex_ip/$new_ex_ip/g" */*.conf
sed  -i "s/$old_inner_ip/$new_inner_ip/g" *.conf

sed  -i "s/$old_inner_ip/$new_inner_ip/g" */*.conf
sed  -i "s/$old_inner_ip/$new_inner_ip/g" */*/*.conf
sed  -i "s/$old_inner_ip/$new_inner_ip/g" */*/*/*.conf
sed  -i "s/$old_inner_ip/$new_inner_ip/g" *.ini

sed  -i "s/$old_gate_domain/$new_gate_domain/g" *.conf
sed  -i "s/$old_domain/$new_domain/g" *.conf

cd  /var/www/html
sed  -i "s/$old_gate_domain/$new_gate_domain/g" */*/*.php
sed  -i "s/$old_domain/$new_domain/g" */*/*.php
sed -i "s/$old_ex_ip/$new_ex_ip/g" */*/*.php
sed -i "s/$old_ex_ip/$new_ex_ip/g" */*/*.js
sed -i "s/$old_ex_ip/$new_ex_ip/g" */*/*/*.php
sed -i "s/$old_inner_ip/$new_inner_ip/g" */*/*.php
sed -i "s/$old_inner_ip/$new_inner_ip/g" */*/*/*.php

cd /usr/local/cspush
sed  -i "s/$old_inner_ip/$new_inner_ip/g" */*.conf
sed  -i "s/$old_domain/$new_domain/g" */*/*/*.php

cd /usr/local/freeswitch
sed -i "s/$old_inner_ip/$new_inner_ip/g" *.conf
sed -i "s/$old_inner_ip/$new_inner_ip/g" */*/*.xml



