#!/bin/bash
# ****************************************************************************
# Author        :   buhuai.su
# Last modified :   2022-08-05
# Filename      :   build.sh
# Version       :
# Description   :   web 的构建脚本
# Modifier      :
# ****************************************************************************

set -euo pipefail

PROJECT_PATH=$1        #git clone时项目路径
SRC_PATH=$2            #编译后代码存储路径

DOCKER_TAG=$3          #docker镜像tag号
CONTAINER_NAME=$4      #启动容器名称
PROJECT_NAME=$5
JOB_NAME=$6

[[ -z "$PROJECT_PATH" ]] && { echo "【PROJECT_PATH】变量值不能为空"; exit 1; }
[[ -z "$SRC_PATH" ]] && { echo "【SRC_PATH】变量值不能为空"; exit 1; }

WORKSPACE_PATH=/opt/jenkins/workspace

DOCKER_ROOT=/home/<USER>
DOCKER_PROJECT_ROOT=$DOCKER_ROOT/$JOB_NAME/$PROJECT_NAME

#在源码包的同一路径下生成安装包
AKCS_PACKAGE_NAME=objects
AKCS_PACKAGE_ROOT=$WORKSPACE_PATH/$JOB_NAME/$PROJECT_NAME/$AKCS_PACKAGE_NAME

echo "【开始编译项目】"
ts=$(date +%s)
mkdir -p "$AKCS_PACKAGE_ROOT"
docker run --rm --name "ubuntu20-build-csadapt-$ts" -v $WORKSPACE_PATH:$DOCKER_ROOT app_backend_build_cicd_ubuntu20:1.0 bash -c "cd $DOCKER_PROJECT_ROOT/build && bash build.sh clean && bash build.sh build"
