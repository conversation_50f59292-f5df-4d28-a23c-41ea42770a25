<?php
date_default_timezone_set("PRC");


const CONF_PATH="/usr/local/akcs/csconfig/conf/csconfig.conf";

function getDB()
{
    @$conf=parse_ini_file(CONF_PATH);
    $dbhost = $conf["db_ip"];
    $dbuser = $conf["db_username"];
    $dbpass = $conf["db_passwd"];
    $dbname = $conf["db_database"];
    $dbport = $conf["db_port"];

    $mysql_conn_string = "mysql:host=$dbhost;port=$dbport;dbname=$dbname";
    $dbConnection = new PDO($mysql_conn_string, $dbuser, $dbpass);
    $dbConnection->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $dbConnection->query('set names utf8;');
    return $dbConnection;
}

function storage_file_to_fdfs($filePath){
    $ret = false;
    $tracker = fastdfs_tracker_get_connection();
    if (!$tracker){
        return $ret;
    }
    $storage = fastdfs_tracker_query_storage_store("group2");
    if (!$storage){
        return $ret;
    }
    $server = fastdfs_connect_server($storage['ip_addr'], $storage['port']);
    if (!$server){
        return $ret;
    }
    $storage['sock'] = $server['sock'];

    
    $file_info = fastdfs_storage_upload_by_filename($filePath, null, array(), null, $tracker, $storage);
    if ($file_info)
    {
       $group_name = $file_info['group_name'];
       $remote_filename = $file_info['filename'];
       $ret = '/'.$file_info['group_name'].'/'.$file_info['filename'];
    }

    fastdfs_disconnect_server($storage);
    return $ret;
}


const SHADOW_TYPE = [0=>'ConfigPath',1=>'PrivatekeyPath',2=>'RfidPath',3=>'ContactPath',4=>'FacePath',5=>'SchedulePath',6=>'UserMetaPath'];

$ip_conf = parse_ini_file('/etc/ip');
$inner_ip = $ip_conf['SERVER_INNER_IP'];
try {
    $db = getDB();
    $sth = $db->prepare("select MAC,ShadowType,ID from DevicesShadowError where IsDeal = 0 and ServerIP = :ip");
    //处理各自csconfig服务器的异常（可能是集群）
    $sth->bindParam(':ip', $inner_ip, PDO::PARAM_STR);
    $sth->execute();
    $list = $sth->fetchALL(PDO::FETCH_ASSOC);
    foreach($list as $value) {
        $id = $value['ID'];
        $mac = $value['MAC'];
        $type = $value['ShadowType'];
        $shadow_column = SHADOW_TYPE[$type];
        $sth = $db->prepare("select $shadow_column from DevicesShadow where MAC = :mac");
        $sth->bindParam(':mac', $mac, PDO::PARAM_STR);
        $sth->execute();
        $ret = $sth->fetch(PDO::FETCH_ASSOC);
        if($ret) {
            $db->beginTransaction();
            $path = $ret[$shadow_column];
            if(strstr($path, 'download')) {
                $deal_ret = storage_file_to_fdfs('/var/www'.$path);
                if($deal_ret) {                   
                    $sth = $db->prepare("update DevicesShadow set $shadow_column = :path where MAC = :mac");
                    $sth->bindParam(':mac', $mac, PDO::PARAM_STR);
                    $sth->bindParam(':path', $deal_ret, PDO::PARAM_STR);
                    $sth->execute();

                    $sth = $db->prepare("update DevicesShadowError set IsDeal = 1 where ID = :id");
                    $sth->bindParam(':id', $id, PDO::PARAM_INT);
                    $sth->execute();
                }
            }
            else //现在已经存入fdfs了
            {
                $sth = $db->prepare("update DevicesShadowError set IsDeal = 1 where ID = :id");
                $sth->bindParam(':id', $id, PDO::PARAM_INT);
                $sth->execute();
            }
            $db->commit();
        }
    }
} catch (PDOException $e) {
    echo $e->getMessage();
}
