#!/bin/bash

# ****************************************************************************
# Author        :   jian<PERSON>.li
# Last modified :   2022-04-22
# Filename      :   install.sh
# Version       :
# Description   :   csconfig 的安装脚本
# Input         :
# ****************************************************************************

set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径

# Input:
#   $1: item
#   $2: conf_file
# Output:
#   value: the value of item
grep_conf() {
    grep -w "$1" "$2" | cut -d'=' -f 2 | awk '{gsub(/^[ \t]+|[ \t]+$/, "");print}'
}


check_md5()
{
    newfile=$1
    oldfile=$2
    newmd5=$(md5sum "$newfile" | awk '{print $1}')
    oldmd5=$(md5sum "$oldfile" | awk '{print $1}')
    if [ "$oldmd5" != "$newmd5" ]; then
        return 1
    else
        return 0
    fi
}


# ============================================================================
# ==== main
# ============================================================================
cd "$(dirname "$0")"
PKG_ROOT=$(cd .. && pwd)

IP_FILE=/etc/ip
INSTALL_CONF=$RSYNC_PATH/app_backend_install.conf
APP_NAME=csconfig    # 安装包中 bin 目录下应用程序的文件名
APP_HOME=/usr/local/akcs/csconfig-office
LOG_PATH=/var/log/csconfig-officelog
CTRL_SCRIPT=csconfig-office-ctl.sh
RUN_SCRIPT=csconfig-office-run.sh
RUN_SCRIPT_PATH=$APP_HOME/scripts/$RUN_SCRIPT
SIGNAL=${SIGNAL:-TERM}

# ----------------------------------------------------------------------------
# 开始安装
# ----------------------------------------------------------------------------
echo "Begin to install $APP_NAME"

echo '读取配置'

if [ ! -f $IP_FILE ]; then
    echo "文件不存在：$IP_FILE"
    exit 1
fi

if [ ! -f $INSTALL_CONF ]; then
    echo "文件不存在：$INSTALL_CONF"
    exit 1
fi

SERVER_INNER_IP=$(grep_conf 'SERVER_INNER_IP' $IP_FILE)
ETCD_INNER_IP=$(grep_conf 'ETCD_INNER_IP' $INSTALL_CONF)
MYSQL_INNER_IP=$(grep_conf 'MYSQL_INNER_IP' $INSTALL_CONF)
PBX_OUTER_IP=$(grep_conf 'PBX_OUTER_IPV4' $INSTALL_CONF)
BEANSTALKD_IP=$(grep_conf 'BEANSTALKD_IP' $INSTALL_CONF)
BEANSTALKD_BACKUP_IP=$(grep_conf 'BEANSTALKD_BACKUP_IP' $INSTALL_CONF || echo '')
REMOTE_CONIFG_PRIMARY_DOMAIN=$(grep_conf 'REMOTE_CONIFG_PRIMARY_DOMAIN' $INSTALL_CONF)
IS_AWS=$(grep_conf 'IS_AWS' $INSTALL_CONF)
WEB_IP=$(grep_conf 'WEB_IP' $INSTALL_CONF)
SYSTEM_AREA=$(grep_conf 'GATEWAY_NUM' $INSTALL_CONF)
CSFTP_SERVER_DOMAIN=$(grep_conf 'CSFTP_SERVER_DOMAIN' $INSTALL_CONF)
REDIS_INNER_IP=$(grep_conf 'REDIS_INNER_IP' $INSTALL_CONF)
ENABLE_REDIS_SENTINEL=$(grep_conf 'ENABLE_REDIS_SENTINEL' $INSTALL_CONF)
SENTINEL_HOSTS=$(grep_conf 'SENTINEL_HOSTS' $INSTALL_CONF)
FDFS_INNER_IP=$(grep_conf 'FDFS_INNER_IP' $INSTALL_CONF)
FDFS_BACKUP_INNER_IP=$(grep_conf 'FDFS_BACKUP_INNER_IP' $INSTALL_CONF)
STORE_FDFS=$(grep_conf 'STORE_FDFS' $INSTALL_CONF || echo '1')
VRTSP_SERVER_DOMAIN=$(grep_conf 'VRTSP_SERVER_DOMAIN' $INSTALL_CONF)
FDFS_CONFIG_ADDR=$(grep_conf 'FDFS_CONFIG_ADDR' $INSTALL_CONF)
KAFKA_INNER_IP=$(grep_conf 'KAFKA_INNER_IP' $INSTALL_CONF)
CONFIG_SERVER_DOMAIN=$(grep_conf 'CONFIG_SERVER_DOMAIN' $INSTALL_CONF)
# 停止守护脚本和服务
echo "停止守护脚本 $RUN_SCRIPT"
run_pids=$(ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep | awk '{print $2}' || true)
if [ -n "$run_pids" ]; then
    kill -9 $run_pids
    sleep 2
fi

echo "停止服务 $APP_NAME"
if [ -f $APP_HOME/scripts/$CTRL_SCRIPT ]; then
    bash "$APP_HOME"/scripts/$CTRL_SCRIPT stop
    sleep 2
fi


# 替换配置文件
echo '替换配置文件的配置'

sed -i "
    s/^.*etcd_srv_net=.*/etcd_srv_net=${ETCD_INNER_IP}/g
    s/^.*db_ip=.*/db_ip=${MYSQL_INNER_IP}/g
    s/^.*cspbx_ip=.*/cspbx_ip=${PBX_OUTER_IP}/g
    s/^.*beanstalkd_ip=.*/beanstalkd_ip=${BEANSTALKD_IP}/g
    s/^.*beanstalkd_backup_ip=.*/beanstalkd_backup_ip=${BEANSTALKD_BACKUP_IP}/g  
    s/^.*remote_config_domain=.*/remote_config_domain=${REMOTE_CONIFG_PRIMARY_DOMAIN}/g
    s/^.*ftp_ip=.*/ftp_ip=${CSFTP_SERVER_DOMAIN}/g
    s/^.*system_area_type=.*/system_area_type=${SYSTEM_AREA}/g
    s/^.*is_aws=.*/is_aws=${IS_AWS}/g
    s/^.*is_store_fdfs=.*/is_store_fdfs=${STORE_FDFS}/g
    s/^.*web_ip=.*/web_ip=${WEB_IP}/g
    s/^.*fdfs_config_addr=.*/fdfs_config_addr=${FDFS_CONFIG_ADDR}/g
    s/^.*vrtsp_server_domain=.*/vrtsp_server_domain=${VRTSP_SERVER_DOMAIN}/g
    s/^.*kafka_broker_ip=.*/kafka_broker_ip=${KAFKA_INNER_IP}:8520/g
    s/^.*config_server_domain=.*/config_server_domain=${CONFIG_SERVER_DOMAIN}/g
    " "$PKG_ROOT"/conf/csconfig.conf


# redis 配置
sed -i "
    s/^.*userdetail_host=.*/userdetail_host=${REDIS_INNER_IP}/g
    s/^.*proc_record_host=.*/proc_record_host=${REDIS_INNER_IP}/g
    s/^.*appcode_host=.*/appcode_host=${REDIS_INNER_IP}/g
    " "$PKG_ROOT"/conf/csconfig_redis.conf

# redis sentinel 配置
if [ "$ENABLE_REDIS_SENTINEL" = "1" ]; then
    sed -i "s/^.*sentinels=.*/sentinels=${SENTINEL_HOSTS}/g" "$PKG_ROOT"/conf/csconfig_redis.conf
else
    sed -i "s/^.*sentinels=.*/sentinels=/g" "$PKG_ROOT"/conf/csconfig_redis.conf
fi

# FastDFS 配置
sed -i "s/^tracker_server.*/tracker_server=${FDFS_INNER_IP}:22122/g" "$PKG_ROOT"/conf/csconfig_fdfs.conf
if [ -n "${FDFS_BACKUP_INNER_IP}" ];then
    sed -i "s/^backup_tracker_server.*/tracker_server=${FDFS_BACKUP_INNER_IP}:22122/g" "$PKG_ROOT"/conf/csconfig_fdfs.conf
fi

echo "创建存放日志的文件夹 $LOG_PATH"
if [ ! -d $LOG_PATH ]; then
    mkdir -p $LOG_PATH
fi

echo "删除旧的安装文件 $APP_HOME"
if [ -d $APP_HOME ]; then
    rm -rf $APP_HOME
fi

if [ -d /usr/local/akcs/csconfig_scripts ]; then
    rm -rf /usr/local/akcs/csconfig_scripts
fi

echo '复制安装包的文件'
if [ ! -d $APP_HOME ]; then
    mkdir -p $APP_HOME
fi

cp -rf "$PKG_ROOT"/conf $APP_HOME
cp -rf "$PKG_ROOT"/bin $APP_HOME
cp -rf "$PKG_ROOT"/lib $APP_HOME
cp -rf "$PKG_ROOT"/scripts $APP_HOME
cp -f "$PKG_ROOT"/version $APP_HOME


cd $APP_HOME/lib
if [ -f $APP_HOME/lib/libevpp.so ]; then
    ln -sf libevpp.so libevpp.so.0.7
fi

ln -sf libcppkafka.so libcppkafka.so.0.4.0
ln -sf librdkafka.so librdkafka.so.1
cd "$PKG_ROOT"

# md5 校验，避免拷贝不完全
if ! check_md5 "$PKG_ROOT"/bin/$APP_NAME $APP_HOME/bin/$APP_NAME; then
    echo "copy error!"
    echo "$PKG_ROOT/bin/$APP_NAME    copy failed."
    exit 1
fi

chmod 755 $APP_HOME
chmod -R 755 $APP_HOME/bin
chmod -R 755 $APP_HOME/scripts


echo '添加到开机启动'
if ! grep -q "$RUN_SCRIPT_PATH" /etc/init.d/rc.local; then
    echo "bash $RUN_SCRIPT_PATH &" >> /etc/init.d/rc.local
fi


# ----------------------------------------------------------------------------
# 启动服务
# ----------------------------------------------------------------------------
# core文件生效
if [ ! -d /var/core ]; then
    mkdir -p /var/core
    chmod -R 777 /var/core
fi

if ! grep -q 'kernel.core_pattern' /etc/sysctl.conf; then
    echo 'kernel.core_pattern = /var/core/core_%e_%p_%t' >> /etc/sysctl.conf
    sysctl -p
fi

if ! grep -q 'ulimit -c unlimited' /etc/profile; then
    echo 'ulimit -c unlimited' >> /etc/profile
fi

ulimit -c unlimited
DIRS=(
    "/var/www/download/personal"
    "/var/www/download/community"
    "/var/www/download/face"
    "/var/www/download/UserDetail"
    "/var/www/download/UserMeta"
    "/var/www/download/UserAll"
    "/var/www/download/Schedule"
)

for dir in "${DIRS[@]}"
do
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        chown nobody:nogroup -R "$dir"
    fi
done


echo '启动服务'
$APP_HOME/scripts/$CTRL_SCRIPT start
sleep 2

echo '检查服务的运行状态'
$APP_HOME/scripts/$CTRL_SCRIPT status

echo '启动守护脚本'
if ! ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep; then
    nohup bash $RUN_SCRIPT_PATH >/dev/null 2>&1 &
    sleep 2
fi

echo '检查守护脚本的运行状态'
if ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep; then
    echo '守护脚本运行中'
else
    echo '守护脚本运行失败'
    exit 1
fi

if [ -z "`grep "/usr/local/akcs/csconfig/bin/deal_shadow_error.php" /var/spool/cron/crontabs/root`" ];then
    #每月5号2点时执行
	echo "0 2 5 * * /usr/local/bin/php /usr/local/akcs/csconfig/bin/deal_shadow_error.php >/dev/null 2>&1 " >> /var/spool/cron/crontabs/root
    service cron reload
fi

echo "$APP_NAME install complete."

