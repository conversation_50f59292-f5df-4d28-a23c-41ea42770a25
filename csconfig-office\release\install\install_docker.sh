#!/bin/bash

set -euo pipefail

RSYNC_PATH=$1              #代码同步到的目录
PROJECT_RUN_PATH=$2        #项目运行路径
DOCKER_IMG=$3
CONTAINER_NAME=csconfig-office

# Input:
#   $1: item
#   $2: conf_file
# Output:
#   value: the value of item
grep_conf() {
    grep -w "$1" "$2" | cut -d'=' -f 2 | awk '{gsub(/^[ \t]+|[ \t]+$/, "");print}'
}

cd "$(dirname "$0")"

IP_FILE=/etc/ip
APP_NAME=csconfig-office   # 安装包中 bin 目录下应用程序的文件名
APP_HOME=/usr/local/akcs/csconfig-office
INSTALL_CONF=$RSYNC_PATH/app_backend_install.conf
LOG_PATH=/var/log/csconfig-officelog
RUN_SCRIPT=csconfig-office-run.sh
SIGNAL=${SIGNAL:-TERM}

cd "$(dirname "$0")"
PKG_ROOT=$(cd .. && pwd)
# ----------------------------------------------------------------------------
# 开始安装
# ----------------------------------------------------------------------------
echo "Begin to install $APP_NAME"

echo '读取配置'

if [ ! -f $IP_FILE ]; then
    echo "文件不存在：$IP_FILE"
    exit 1
fi

if [ ! -f $INSTALL_CONF ]; then
    echo "文件不存在：$INSTALL_CONF"
    exit 1
fi

echo "删除旧的安装文件 $APP_HOME"
if [ -d $APP_HOME ]; then
    rm -rf $APP_HOME
fi

echo "拷贝运维脚本"
mkdir -p $APP_HOME/scripts
cp -rf "$PKG_ROOT"/scripts/* $APP_HOME/scripts

ENV_CONF_PARAM="
-e SERVER_INNER_IP=$(grep_conf 'SERVER_INNER_IP' $IP_FILE)
-e ETCD_INNER_IP=$(grep_conf 'ETCD_INNER_IP' $INSTALL_CONF)
-e MYSQL_INNER_IP=$(grep_conf 'MYSQL_INNER_IP' $INSTALL_CONF)
-e PBX_OUTER_IP=$(grep_conf 'PBX_OUTER_IPV4' $INSTALL_CONF)
-e BEANSTALKD_IP=$(grep_conf 'BEANSTALKD_IP' $INSTALL_CONF)
-e BEANSTALKD_BACKUP_IP=$(grep_conf 'BEANSTALKD_BACKUP_IP' $INSTALL_CONF || echo '')
-e REMOTE_CONIFG_PRIMARY_DOMAIN=$(grep_conf 'REMOTE_CONIFG_PRIMARY_DOMAIN' $INSTALL_CONF)
-e IS_AWS=$(grep_conf 'IS_AWS' $INSTALL_CONF)
-e WEB_IP=$(grep_conf 'WEB_IP' $INSTALL_CONF)
-e SYSTEM_AREA=$(grep_conf 'GATEWAY_NUM' $INSTALL_CONF)
-e CSFTP_SERVER_DOMAIN=$(grep_conf 'CSFTP_SERVER_DOMAIN' $INSTALL_CONF)
-e REDIS_INNER_IP=$(grep_conf 'REDIS_INNER_IP' $INSTALL_CONF)
-e ENABLE_REDIS_SENTINEL=$(grep_conf 'ENABLE_REDIS_SENTINEL' $INSTALL_CONF)
-e SENTINEL_HOSTS=$(grep_conf 'SENTINEL_HOSTS' $INSTALL_CONF)
-e FDFS_INNER_IP=$(grep_conf 'FDFS_INNER_IP' $INSTALL_CONF)
-e FDFS_BACKUP_INNER_IP=$(grep_conf 'FDFS_BACKUP_INNER_IP' $INSTALL_CONF)
-e STORE_FDFS=$(grep_conf 'STORE_FDFS' $INSTALL_CONF || echo '1')
-e VRTSP_SERVER_DOMAIN=$(grep_conf 'VRTSP_SERVER_DOMAIN' $INSTALL_CONF)
-e FDFS_CONFIG_ADDR=$(grep_conf 'FDFS_CONFIG_ADDR' $INSTALL_CONF)
-e KAFKA_INNER_IP=$(grep_conf 'KAFKA_INNER_IP' $INSTALL_CONF)
-e CONFIG_SERVER_DOMAIN=$(grep_conf 'CONFIG_SERVER_DOMAIN' $INSTALL_CONF)
"

ENV_LOAD_PARAM="
-v /usr/share/zoneinfo:/usr/share/zoneinfo
-v /var/log/csconfig-officelog:/var/log/csconfig-officelog
-v /var/core:/var/core
-v /etc/ip:/etc/ip
-v /etc/kdc.conf:/etc/kdc.conf
-v /bin/crypto:/bin/crypto
-v /var/www/download:/var/www/download
"

echo "创建存放日志的文件夹 $LOG_PATH"
if [ ! -d $LOG_PATH ]; then
    mkdir -p $LOG_PATH
fi

# ----------------------------------------------------------------------------
# 启动服务
# ----------------------------------------------------------------------------

if [ `docker ps -a | grep -w $CONTAINER_NAME | wc -l` -gt 0 ];then
    old_image_id=$(docker inspect --format='{{.Image}}' $CONTAINER_NAME)
    docker stop $CONTAINER_NAME;
    docker rm -f $CONTAINER_NAME;
    docker rmi -f $old_image_id || true

else
    # 停止旧的守护脚本和服务
    echo "停止守护脚本 $RUN_SCRIPT"
    run_pids=$(ps -ef | grep -w "$RUN_SCRIPT" | grep -v grep | awk '{print $2}' || true)
    if [ -n "$run_pids" ]; then
        kill -9 $run_pids
        sleep 2
    fi
    echo "停止服务 $APP_NAME"
    app_pids=$(pidof csconfig-office || true)
    if [ -n "$app_pids" ]; then
        kill -s $SIGNAL $app_pids
        sleep 2
    fi
    sed -i '/csconfig-office-run.sh/d' /etc/init.d/rc.local
fi
docker run -d -e TZ=Asia/Shanghai ${ENV_CONF_PARAM} --restart=always --net=host ${ENV_LOAD_PARAM} --name ${CONTAINER_NAME} ${DOCKER_IMG} /bin/bash /usr/local/akcs/csconfig-office/scripts/csconfig-office-run.sh


#守护进程中会进行环境变量替换配置文件中的内容
#具体看csadapt/scripts/sedconf.sh