#!/bin/bash
ACMD="$1"
csconfig_BIN='/usr/local/akcs/csconfig-office/bin/csconfig-office'

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_csconfig()
{
	nohup $csconfig_BIN >/dev/null 2>&1 &
    echo "Start csconfig-office successful"
    if [ -z "`ps -fe|grep "csconfig-office-run.sh" |grep -v grep`" ];then
        nohup bash /usr/local/akcs/csconfig-office/scripts/csconfig-office-run.sh >/dev/null 2>&1 &
    fi
}
stop_csconfig()
{
    echo "Begin to stop csconfig-office-run.sh"
    csconfigrunid=`ps aux | grep -w csconfig-office-run.sh | grep -v grep | awk '{ print $(2) }'`
    if [ -n "${csconfigrunid}" ];then
	    echo "csconfig-office-run.sh is running at ${csconfigrunid}, will kill it first."
	    kill -9 ${csconfigrunid}
    fi
    echo "Begin to stop csconfig"
    kill -9 `pidof csconfig-office`
    sleep 2
    echo "Stop csconfig-office successful"
}

case $ACMD in
  start)
     start_csconfig
    ;;
  stop)
     stop_csconfig
    ;;
  restart)
    stop_csconfig
    sleep 1
    start_csconfig
    ;;
  status)
    if [ -f /var/run/csconfig-office.pid ];then
        pid=`cat /var/run/csconfig-office.pid`
        if [ $pid"x" = "x" ];then
           #pid里面的文件是空的
           pid="xxxxxxxxxx"
        fi
    else
        #重启之后没有这个pid文件
        pid="xxxxxxxxxx"
    fi
    cnt=`ls /proc/$pid | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m csconfig-office is stop!!!\033[0m"
        exit 1
    else
        echo "\033[0;32m csconfig-office is running \033[0m"
        exit 0
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

