#!/bin/bash
# 替换配置文件
echo '替换配置文件的配置'

sed -i "
    s/^.*etcd_srv_net=.*/etcd_srv_net=${ETCD_INNER_IP}/g
    s/^.*db_ip=.*/db_ip=${MYSQL_INNER_IP}/g
    s/^.*cspbx_ip=.*/cspbx_ip=${PBX_OUTER_IP}/g
    s/^.*beanstalkd_ip=.*/beanstalkd_ip=${BEANSTALKD_IP}/g
    s/^.*beanstalkd_backup_ip=.*/beanstalkd_backup_ip=${BEANSTALKD_BACKUP_IP}/g  
    s/^.*remote_config_domain=.*/remote_config_domain=${REMOTE_CONIFG_PRIMARY_DOMAIN}/g
    s/^.*ftp_ip=.*/ftp_ip=${CSFTP_SERVER_DOMAIN}/g
    s/^.*system_area_type=.*/system_area_type=${SYSTEM_AREA}/g
    s/^.*is_aws=.*/is_aws=${IS_AWS}/g
    s/^.*is_store_fdfs=.*/is_store_fdfs=${STORE_FDFS}/g
    s/^.*web_ip=.*/web_ip=${WEB_IP}/g
    s/^.*fdfs_config_addr=.*/fdfs_config_addr=${FDFS_CONFIG_ADDR}/g
    s/^.*vrtsp_server_domain=.*/vrtsp_server_domain=${VRTSP_SERVER_DOMAIN}/g
    s/^.*kafka_broker_ip=.*/kafka_broker_ip=${KAFKA_INNER_IP}:8520/g
    s/^.*config_server_domain=.*/config_server_domain=${CONFIG_SERVER_DOMAIN}/g
    " /usr/local/akcs/csconfig-office/conf/csconfig.conf


# redis 配置
sed -i "
    s/^.*userdetail_host=.*/userdetail_host=${REDIS_INNER_IP}/g
    s/^.*proc_record_host=.*/proc_record_host=${REDIS_INNER_IP}/g
    s/^.*appcode_host=.*/appcode_host=${REDIS_INNER_IP}/g
    " /usr/local/akcs/csconfig-office/conf/csconfig_redis.conf

# redis sentinel 配置
if [ "$ENABLE_REDIS_SENTINEL" = "1" ]; then
    sed -i "s/^.*sentinels=.*/sentinels=${SENTINEL_HOSTS}/g" /usr/local/akcs/csconfig-office/conf/csconfig_redis.conf
else
    sed -i "s/^.*sentinels=.*/sentinels=/g" /usr/local/akcs/csconfig-office/conf/csconfig_redis.conf
fi

# FastDFS 配置
sed -i "s/^tracker_server.*/tracker_server=${FDFS_INNER_IP}:22122/g" /usr/local/akcs/csconfig-office/conf/csconfig_fdfs.conf
if [ -n "${FDFS_BACKUP_INNER_IP}" ];then
    sed -i "s/^backup_tracker_server.*/tracker_server=${FDFS_BACKUP_INNER_IP}:22122/g" /usr/local/akcs/csconfig-office/conf/csconfig_fdfs.conf
fi