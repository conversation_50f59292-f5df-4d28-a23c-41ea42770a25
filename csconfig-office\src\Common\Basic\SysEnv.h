#ifndef _SYS_ENV_H_
#define _SYS_ENV_H_

#ifdef __cplusplus
extern "C" {
#endif

//VOID GetCurTime(INOUT CHAR *pszDate, INOUT uint32_t size);
int code_convert(const char* from_charset, char* inbuf, size_t inlen, const char* to_charset, char* outbuf, size_t outlen) ;
int CSADAPT_U2G(char* inbuf, size_t inlen, char* outbuf, size_t outlen);
int CSADAPT_G2U(char* inbuf, size_t inlen, char* outbuf, size_t outlen);

int GetLastError();

#ifdef __cplusplus
}
#endif
#endif //_SYS_ENV_H_
