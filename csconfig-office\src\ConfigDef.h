#ifndef __ADAPT_DEFINE_H__
#define __ADAPT_DEFINE_H__

#define IP_SIZE                         16
#define DEVICE_ID_SIZE                  24

#define MD5_SIZE                        36
#define NAME_SIZE                       32
#define CONFIG_MSG_SIZE                 1024

#define MAX_FILE_PATH                   128
#define DEV_NODE_SIZE                   64
#define COMMON_CONF_SIZE                64
#define KEY_VAL_LEN                     32
#define DATETIME_SIZE                   24

#define UDPMTU_SIZE                     1024

#define PERSONNAL_SIP_UA_TIMEOUT        "1800"
#define PERSONNAL_SIP_GROUP_DOOR_PHONE  "Config.Programable.SOFTKEY01.Param1="

#define  CONFIG_ENABLE        "Config.Account1.GENERAL.Enable="
#define  CONFIG_LABLE         "Config.Account1.GENERAL.Label="
#define  CONFIG_DISPLAYNAME   "Config.Account1.GENERAL.DisplayName="
#define  CONFIG_USERNAME      "Config.Account1.GENERAL.UserName="
#define  CONFIG_AUTHNAME      "Config.Account1.GENERAL.AuthName="
#define  CONFIG_PWD           "Config.Account1.GENERAL.Pwd="
#define  CONFIG_SERVER        "Config.Account1.SIP.Server="
#define  CONFIG_PORT          "Config.Account1.SIP.Port="
#define  CONFIG_TIMEOUT       "Config.Account1.REG.Timeout="
#define  CONFIG_SERVER2       "Config.Account1.SIP.Server2="
#define  CONFIG_PORT2         "Config.Account1.SIP.Port2="
#define  CONFIG_TIMEOUT2      "Config.Account1.REG.Timeout2="
#define  CONFIG_INDOOR        "Config.DoorSetting.SPEEDDIAL.Indoor="
#define  CONFIG_OUTDOOR       "Config.DoorSetting.SPEEDDIAL.Outdoor="
#define  CONFIG_DTMF_ENABLE   "Config.DoorSetting.DTMF.Enable="
#define  CONFIG_DTMF_CODE1    "Config.DoorSetting.DTMF.Code1="
#define  CONFIG_SIP_GROUP_ACCOUNT          "Config.Programable.SOFTKEY01.Param1="
#define  CONFIG_SIP_NAT_RPORT              "Config.Account1.NAT.Rport="
#define  CONFIG_RTSP_ENABLE                "Config.DoorSetting.RTSP.Enable="
#define  CONFIG_RTSP_AUDIO                 "Config.DoorSetting.RTSP.Audio="
#define  CONFIG_RTSP_VIDEO                 "Config.DoorSetting.RTSP.Video="
#define  CONFIG_RTSP_CODEC                 "Config.DoorSetting.RTSP.Codec="
#define  CONFIG_RTSP_H264_RESOLUTION       "Config.DoorSetting.RTSP.H264Resolution="
#define  CONFIG_RTSP_H264_FRAMERATE        "Config.DoorSetting.RTSP.H264FrameRate="
#define  CONFIG_RTSP_H264_BITRATE          "Config.DoorSetting.RTSP.H264BitRate="
#define  CONFIG_NAT_UDP_ENABLE             "Config.Account1.NAT.UdpKeepEnable="
#define  CONFIG_NAT_UDP_INTERVAL           "Config.Account1.NAT.UdpKeepInterval="

#define  CONFIG_CLOUDSERVER_FTP_URL        "Config.DoorSetting.CLOUDSERVER.UploadUrl="
#define  CONFIG_CLOUDSERVER_FTP_USER       "Config.DoorSetting.CLOUDSERVER.UploadUser="
#define  CONFIG_CLOUDSERVER_FTP_PWD        "Config.DoorSetting.CLOUDSERVER.UploadPassword="

//社区R27 29界面呈现 只有公共设备才会设置这个值为1
#define  CONFIG_CLOUDSERVER_RXX_COMMUNITY  "Config.DoorSetting.CLOUDSERVER.DeviceCommunity="
//是否过期
#define  CONFIG_CLOUDSERVER_DEV_EXPIRE  "Config.DoorSetting.CLOUDSERVER.DeviceExpire="
#define  CONFIG_CLOUDSERVER_TYPE  "Config.DoorSetting.CLOUDSERVER.Type="
//1代表新小区 0代表旧小区，默认是0
#define  CONFIG_CLOUDSERVER_COMMUNITY_TYPE   "Config.DoorSetting.CLOUDSERVER.NewComm="
#define  CLOUD_SERVER_TYPE_PERSONAL            "0"
#define  CLOUD_SERVER_TYPE_PERSONAL_COMMUNITY  "1"
#define  CLOUD_SERVER_TYPE_COMMUNITY           "2"
#define  CLOUD_SERVER_TYPE_COMMUNITY_UNIT      "3" //梯口
#define  CLOUD_SERVER_TYPE_COMMUNITY_PUBLIC    "4" //公共设备

//V4.1
#define CONFIG_FEATURES_CALLROBIN_NUM "Config.Features.CALLROBIN.Num="
#define CONFIG_FEATURES_CALLROBIN_ENABLE "Config.Features.CALLROBIN.Enable="
#define CONFIG_FEATURES_CALLROBIN_TIME "Config.Features.CALLROBIN.Time="
#define CONFIG_DOORSETTING_MOTION_DETECT_ENABLE "Config.DoorSetting.MOTION_DETECT.Enable="
#define CONFIG_DOORSETTING_MOTION_DETECT_TIME "Config.DoorSetting.MOTION_DETECT.Interval="

//V4.2
//dtmf 是否根据联系人判断  1根据联系人           0全部允许
#define CONFIG_CLOUDSERVER_DOOR_OPEN_LIMIT "Config.DoorSetting.CLOUDSERVER.OpenDoorLimit="
//梯口机设备显示控制
#define CONFIG_CONTACT_SHOW_TEYP "Config.DoorSetting.GENERAL.ContactViewShowChild="

//V4.3 社区添加街道和地址
#define CONFIG_COMMUNITY_NAME "Config.DoorSetting.DISPLAY.CommunityName="
#define CONFIG_COMMUNITY_STREET "Config.DoorSetting.DISPLAY.Street="

#define CONFIG_IDCARD_ENABLE "Config.DoorSetting.GENERAL.IDCardVerificationEnable="


//V4.6 平台时区配置
#define CONFIG_CLOUDSERVER_TOMEZONE "Config.Settings.SNTP.TimeZone="
#define CONFIG_CLOUDSERVER_CITYNAME "Config.Settings.SNTP.Name="
#define CONFIG_CLOUDSERVER_DATEFORMAT "Config.Settings.DATETIME.DateFormat="
#define CONFIG_CLOUDSERVER_TIMEFORMAT "Config.Settings.DATETIME.TimeFormat="

#define CONFIG_NEW_TIMEZONE_NUUK    "Nuuk"
#define CONFIG_NEW_TIMEZONE_KOLKATA "Kolkata"

//relay
#define CONFIG_RELAY_DMTF_OPTION "Config.DoorSetting.DTMF.Option="//dtmf 几个键 0=1个按键
#define CONFIG_RELAY_DMTF_CODE1 "Config.DoorSetting.DTMF.Code1=" //11 //11=#  10=*
#define CONFIG_RELAY_DMTF_CODE2 "Config.DoorSetting.DTMF.Code2="
#define CONFIG_RELAY_DMTF_CODE3 "Config.DoorSetting.DTMF.Code3="
#define CONFIG_RELAY_DMTF_CODE4 "Config.DoorSetting.DTMF.Code4="

#define CONFIG_RELAY_DMTF_NAME1 "Config.DoorSetting.RELAY.NameA="
#define CONFIG_RELAY_DMTF_NAME2 "Config.DoorSetting.RELAY.NameB="
#define CONFIG_RELAY_DMTF_NAME3 "Config.DoorSetting.RELAY.NameC="
#define CONFIG_RELAY_DMTF_NAME4 "Config.DoorSetting.RELAY.NameD="

//6.7新增relay开门方式设置
#define CONFIG_RELAY_RELAYUNLOCK1 "Config.DoorSetting.RELAYUNLOCK.RelayA="
#define CONFIG_RELAY_RELAYUNLOCK2 "Config.DoorSetting.RELAYUNLOCK.RelayB="
#define CONFIG_RELAY_RELAYUNLOCK3 "Config.DoorSetting.RELAYUNLOCK.RelayC="
#define CONFIG_RELAY_RELAYUNLOCK4 "Config.DoorSetting.RELAYUNLOCK.RelayD="

//6.7新增relay schedule下发
#define CONFIG_RELAY_RELAYSCHEDULE_ENABLE1 "Config.DoorSetting.RELAYSCHEDULE.CloudRelayAEnable="
#define CONFIG_RELAY_RELAYSCHEDULE_ENABLE2 "Config.DoorSetting.RELAYSCHEDULE.CloudRelayBEnable="
#define CONFIG_RELAY_RELAYSCHEDULE_ENABLE3 "Config.DoorSetting.RELAYSCHEDULE.CloudRelayCEnable="
#define CONFIG_RELAY_RELAYSCHEDULE_ENABLE4 "Config.DoorSetting.RELAYSCHEDULE.CloudRelayDEnable="

#define CONFIG_RELAY_RELAYSCHEDULE1 "Config.DoorSetting.RELAYSCHEDULE.CloudRelayASchedule="
#define CONFIG_RELAY_RELAYSCHEDULE2 "Config.DoorSetting.RELAYSCHEDULE.CloudRelayBSchedule="
#define CONFIG_RELAY_RELAYSCHEDULE3 "Config.DoorSetting.RELAYSCHEDULE.CloudRelayCSchedule="
#define CONFIG_RELAY_RELAYSCHEDULE4 "Config.DoorSetting.RELAYSCHEDULE.CloudRelayDSchedule="


//security relay
#define CONFIG_SECURITY_RELAY_DMTF_CODE1 "Config.DoorSetting.DTMF.CodeSA=" //11 //11=#  10=*
#define CONFIG_SECURITY_RELAY_DMTF_CODE2 "Config.DoorSetting.DTMF.CodeSB="
#define CONFIG_SECURITY_RELAY_DMTF_CODE3 "Config.DoorSetting.DTMF.CodeSC="
#define CONFIG_SECURITY_RELAY_DMTF_CODE4 "Config.DoorSetting.DTMF.CodeSD="

#define CONFIG_SECURITY_RELAY_DMTF_NAME1 "Config.DoorSetting.RELAY.NameSA="
#define CONFIG_SECURITY_RELAY_DMTF_NAME2 "Config.DoorSetting.RELAY.NameSB="
#define CONFIG_SECURITY_RELAY_DMTF_NAME3 "Config.DoorSetting.RELAY.NameSC="
#define CONFIG_SECURITY_RELAY_DMTF_NAME4 "Config.DoorSetting.RELAY.NameSD="

#define CONFIG_SECURITY_RELAY_ENABLED1  "Config.DoorSetting.RELAY.SecurityRelayAEnabled="
#define CONFIG_SECURITY_RELAY_ENABLED2  "Config.DoorSetting.RELAY.SecurityRelayBEnabled=" 
#define CONFIG_SECURITY_RELAY_ENABLED3  "Config.DoorSetting.RELAY.SecurityRelayCEnabled=" 
#define CONFIG_SECURITY_RELAY_ENABLED4  "Config.DoorSetting.RELAY.SecurityRelayDEnabled=" 

//6.7新增security relay开门方式设置
#define CONFIG_SECURITY_RELAY_RELAYUNLOCK1 "Config.DoorSetting.RELAYUNLOCK.SeRelayA="
#define CONFIG_SECURITY_RELAY_RELAYUNLOCK2 "Config.DoorSetting.RELAYUNLOCK.SeRelayB="
#define CONFIG_SECURITY_RELAY_RELAYUNLOCK3 "Config.DoorSetting.RELAYUNLOCK.SeRelayC="
#define CONFIG_SECURITY_RELAY_RELAYUNLOCK4 "Config.DoorSetting.RELAYUNLOCK.SeRelayD="

// Relay检测Input常开 设备端autop没统一 INPUT 和 INPUTA 都要下发
#define CONFIG_INPUT_DOOROPENEDA_ENABLED "Config.DoorSetting.INPUT.DoorOpenedAEnabled="
#define CONFIG_INPUT_DOOROPENEDB_ENABLED "Config.DoorSetting.INPUT.DoorOpenedBEnabled="
#define CONFIG_INPUT_DOOROPENEDC_ENABLED "Config.DoorSetting.INPUT.DoorOpenedCEnabled="
#define CONFIG_INPUT_DOOROPENEDD_ENABLED "Config.DoorSetting.INPUT.DoorOpenedDEnabled="

#define CONFIG_INPUT_DOOROPENEDA_TIMEOUT "Config.DoorSetting.INPUT.DoorOpenedATimeout="
#define CONFIG_INPUT_DOOROPENEDB_TIMEOUT "Config.DoorSetting.INPUT.DoorOpenedBTimeout="
#define CONFIG_INPUT_DOOROPENEDC_TIMEOUT "Config.DoorSetting.INPUT.DoorOpenedCTimeout="
#define CONFIG_INPUT_DOOROPENEDD_TIMEOUT "Config.DoorSetting.INPUT.DoorOpenedDTimeout="

#define CONFIG_INPUTA_DOOROPENED_ENABLED "Config.DoorSetting.INPUTA.DoorOpenedEnabled="
#define CONFIG_INPUTB_DOOROPENED_ENABLED "Config.DoorSetting.INPUTB.DoorOpenedEnabled="
#define CONFIG_INPUTC_DOOROPENED_ENABLED "Config.DoorSetting.INPUTC.DoorOpenedEnabled="
#define CONFIG_INPUTD_DOOROPENED_ENABLED "Config.DoorSetting.INPUTD.DoorOpenedEnabled="

#define CONFIG_INPUTA_DOOROPENED_TIMEOUT "Config.DoorSetting.INPUTA.DoorOpenedTimeout="
#define CONFIG_INPUTB_DOOROPENED_TIMEOUT "Config.DoorSetting.INPUTB.DoorOpenedTimeout="
#define CONFIG_INPUTC_DOOROPENED_TIMEOUT "Config.DoorSetting.INPUTC.DoorOpenedTimeout="
#define CONFIG_INPUTD_DOOROPENED_TIMEOUT "Config.DoorSetting.INPUTD.DoorOpenedTimeout="

#define CONFIG_MAGNETICA_DOOROPENED_ENABLED "Config.DoorSetting.DoorMagnetic.MagneticADoorOpenedEnable="
#define CONFIG_MAGNETICB_DOOROPENED_ENABLED "Config.DoorSetting.DoorMagnetic.MagneticBDoorOpenedEnable="
#define CONFIG_MAGNETICC_DOOROPENED_ENABLED "Config.DoorSetting.DoorMagnetic.MagneticCDoorOpenedEnable="
#define CONFIG_MAGNETICD_DOOROPENED_ENABLED "Config.DoorSetting.DoorMagnetic.MagneticDDoorOpenedEnable="

#define CONFIG_MAGNETICA_DOOROPENED_TIMEOUT "Config.DoorSetting.DoorMagnetic.MagneticATimer="
#define CONFIG_MAGNETICB_DOOROPENED_TIMEOUT "Config.DoorSetting.DoorMagnetic.MagneticBTimer="
#define CONFIG_MAGNETICC_DOOROPENED_TIMEOUT "Config.DoorSetting.DoorMagnetic.MagneticCTimer="
#define CONFIG_MAGNETICD_DOOROPENED_TIMEOUT "Config.DoorSetting.DoorMagnetic.MagneticDTimer="

// A094 DoorStatus Input Enable配置: 每个Input独立配置项，只要任何door的Doorstatus选择到了这个input，值就是1
#define CONFIG_MAGNETIC_A_ENABLE "Config.DoorSetting.DoorMagnetic.MagneticAEnable="
#define CONFIG_MAGNETIC_B_ENABLE "Config.DoorSetting.DoorMagnetic.MagneticBEnable="
#define CONFIG_MAGNETIC_C_ENABLE "Config.DoorSetting.DoorMagnetic.MagneticCEnable="
#define CONFIG_MAGNETIC_D_ENABLE "Config.DoorSetting.DoorMagnetic.MagneticDEnable="

#define CONFIG_INPUT_INPUTA_ENABLE  "Config.DoorSetting.INPUT.InputEnable="
#define CONFIG_INPUT_INPUTB_ENABLE  "Config.DoorSetting.INPUT.InputBEnable="
#define CONFIG_INPUT_INPUTC_ENABLE  "Config.DoorSetting.INPUT.InputCEnable="
#define CONFIG_INPUT_INPUTD_ENABLE  "Config.DoorSetting.INPUT.InputDEnable="

// 反潜回
#define CONFIG_RELAY_ANTIPASSBACK1 "Config.DoorSetting.ANTIPASSBACK.RelayA="
#define CONFIG_RELAY_ANTIPASSBACK2 "Config.DoorSetting.ANTIPASSBACK.RelayB="
#define CONFIG_RELAY_ANTIPASSBACK3 "Config.DoorSetting.ANTIPASSBACK.RelayC="
#define CONFIG_RELAY_ANTIPASSBACK4 "Config.DoorSetting.ANTIPASSBACK.RelayD="
#define CONFIG_SECURITY_RELAY_ANTIPASSBACK1 "Config.DoorSetting.ANTIPASSBACK.SeRelayA="
#define CONFIG_SECURITY_RELAY_ANTIPASSBACK2 "Config.DoorSetting.ANTIPASSBACK.SeRelayB="

// InputEnable配置,只要Input在exit_button_input或者door_status_input中存在，则enable=1
#define CONFIG_INPUTA_ENABLE "Config.DoorSetting.INPUTA.Enable="
#define CONFIG_INPUTB_ENABLE "Config.DoorSetting.INPUTB.Enable="
#define CONFIG_INPUTC_ENABLE "Config.DoorSetting.INPUTC.Enable="
#define CONFIG_INPUTD_ENABLE "Config.DoorSetting.INPUTD.Enable="

#define CONFIG_INPUT_ENABLE_A "Config.DoorSetting.INPUT.EnableA="
#define CONFIG_INPUT_ENABLE_B "Config.DoorSetting.INPUT.EnableB="
#define CONFIG_INPUT_ENABLE_C "Config.DoorSetting.INPUT.EnableC="
#define CONFIG_INPUT_ENABLE_D "Config.DoorSetting.INPUT.EnableD="

// 强闯配置(A01/A02/A03/A08): 只要任何door的Door Status选择到了这个input，且强闯告警启用，值就是1
#define CONFIG_INPUT_A_BREAKIN_INTRUSION   "Config.DoorSetting.INPUTA.BreakInIntrusion="
#define CONFIG_INPUT_B_BREAKIN_INTRUSION   "Config.DoorSetting.INPUTB.BreakInIntrusion="
#define CONFIG_INPUT_C_BREAKIN_INTRUSION   "Config.DoorSetting.INPUTC.BreakInIntrusion="
#define CONFIG_INPUT_D_BREAKIN_INTRUSION   "Config.DoorSetting.INPUTD.BreakInIntrusion="

// 强闯配置(安卓门口机 R29/X912/X915): 每个Input独立配置项，且和Relay有关系。
// 强闯告警启用时，把值改成relay对应的值，否则值就是0 (0-NONE, 1-RelayA, 2-RelayB, 3-RelayC, 4-RelayD)
#define CONFIG_INPUT_BREAKIN_INTRUSION_A  "Config.DoorSetting.INPUT.Break-inIntrusionA="
#define CONFIG_INPUT_BREAKIN_INTRUSION_B  "Config.DoorSetting.INPUT.Break-inIntrusionB="
#define CONFIG_INPUT_BREAKIN_INTRUSION_C  "Config.DoorSetting.INPUT.Break-inIntrusionC="
#define CONFIG_INPUT_BREAKIN_INTRUSION_D  "Config.DoorSetting.INPUT.Break-inIntrusionD="

// 强闯配置(A094): 每个Input独立配置项，且和Relay有关系
// FGHI分别代表Magnetic ABCD，强闯告警启用时，把值改成relay对应的值，否则值就是0 (0-NONE, 1-RelayA, 2-RelayB, 3-RelayC, 4-RelayD)
#define CONFIG_INPUT_BREAKIN_INTRUSION_F  "Config.DoorSetting.INPUT.Break-inIntrusionF="
#define CONFIG_INPUT_BREAKIN_INTRUSION_G  "Config.DoorSetting.INPUT.Break-inIntrusionG="
#define CONFIG_INPUT_BREAKIN_INTRUSION_H  "Config.DoorSetting.INPUT.Break-inIntrusionH="
#define CONFIG_INPUT_BREAKIN_INTRUSION_I  "Config.DoorSetting.INPUT.Break-inIntrusionI="

// 内开门配置
#define CONFIG_EXIT_BUTTON_INPUT_A_RELAYID  "Config.DoorSetting.INPUTA.RelayId="
#define CONFIG_EXIT_BUTTON_INPUT_B_RELAYID  "Config.DoorSetting.INPUTB.RelayId="
#define CONFIG_EXIT_BUTTON_INPUT_C_RELAYID  "Config.DoorSetting.INPUTC.RelayId="
#define CONFIG_EXIT_BUTTON_INPUT_D_RELAYID  "Config.DoorSetting.INPUTD.RelayId="

#define CONFIG_INPUT_INPUTA_RELAY  "Config.DoorSetting.INPUT.InputRelay="
#define CONFIG_INPUT_INPUTB_RELAY  "Config.DoorSetting.INPUT.InputBRelay="
#define CONFIG_INPUT_INPUTC_RELAY  "Config.DoorSetting.INPUT.InputCRelay="
#define CONFIG_INPUT_INPUTD_RELAY  "Config.DoorSetting.INPUT.InputDRelay="

// Relay Reader 配置
#define CONFIG_RELAY_ENTRY_READER_A  "Config.DoorSetting.CLOUDSERVER.RelayAEntryReader="
#define CONFIG_RELAY_ENTRY_READER_B  "Config.DoorSetting.CLOUDSERVER.RelayBEntryReader="
#define CONFIG_RELAY_ENTRY_READER_C  "Config.DoorSetting.CLOUDSERVER.RelayCEntryReader="
#define CONFIG_RELAY_ENTRY_READER_D  "Config.DoorSetting.CLOUDSERVER.RelayDEntryReader="
#define CONFIG_RELAY_EXIT_READER_A   "Config.DoorSetting.CLOUDSERVER.RelayAExitReader="
#define CONFIG_RELAY_EXIT_READER_B   "Config.DoorSetting.CLOUDSERVER.RelayBExitReader="
#define CONFIG_RELAY_EXIT_READER_C   "Config.DoorSetting.CLOUDSERVER.RelayCExitReader="
#define CONFIG_RELAY_EXIT_READER_D   "Config.DoorSetting.CLOUDSERVER.RelayDExitReader="

// Security Relay Reader 配置
#define CONFIG_SECURITY_RELAY_ENTRY_READER_A  "Config.DoorSetting.CLOUDSERVER.RelaySAEntryReader="
#define CONFIG_SECURITY_RELAY_ENTRY_READER_B  "Config.DoorSetting.CLOUDSERVER.RelaySBEntryReader="
#define CONFIG_SECURITY_RELAY_EXIT_READER_A   "Config.DoorSetting.CLOUDSERVER.RelaySAExitReader="
#define CONFIG_SECURITY_RELAY_EXIT_READER_B   "Config.DoorSetting.CLOUDSERVER.RelaySBExitReader="

#define CONFIG_DOOR_WHITE_LIST_RELAY_A     "Config.DoorSetting.CLOUDSERVER.WhiteListForRelayA"
#define CONFIG_DOOR_WHITE_LIST_RELAY_B     "Config.DoorSetting.CLOUDSERVER.WhiteListForRelayB"
#define CONFIG_DOOR_WHITE_LIST_RELAY_C     "Config.DoorSetting.CLOUDSERVER.WhiteListForRelayC"
#define CONFIG_DOOR_WHITE_LIST_RELAY_D     "Config.DoorSetting.CLOUDSERVER.WhiteListForRelayD"

#define CONFIG_DOOR_WHITE_LIST_RELAY_SA    "Config.DoorSetting.CLOUDSERVER.WhiteListForRelaySA"
#define CONFIG_DOOR_WHITE_LIST_RELAY_SB    "Config.DoorSetting.CLOUDSERVER.WhiteListForRelaySB"

//双摄像头设备的辅摄像头配置
#define CONFIG_RTSP_AUX_CAMERA_H264_RESOLUTION            "Config.DoorSetting.RTSP.AUXH264Resolution1="
#define CONFIG_RTSP_AUX_CAMERA_H264_FRAMERATE             "Config.DoorSetting.RTSP.AUXH264FrameRate1="
#define CONFIG_RTSP_AUX_CAMERA_H264_BITRATE               "Config.DoorSetting.RTSP.AUXH264BitRate1="


#define CONFIG_CLOUDSERVER_NEW_OFFICE    "Config.DoorSetting.CLOUDSERVER.NewOffice"

#define CONFIG_NETWORK_DATAUSAGE_DATATYPE "Config.Network.DATAUSAGE.DataType="

#define CONFIG_RTSPS_ENABLE               "Config.DoorSetting.RTSP.RTSPSEnable="

#define HTTPROOT            "/var/www/" //chang by chenzhx V4.0

#define CONFFILEPATH        "/usr/local/akcs/csconfig/conf/csconfig.conf"

#define HTTP_DOWNLOAD       "/var/www/download"


#define PERSONNAL_DOWNLOAD  "/download/personal/" //chang by chenzhx V4.0 去掉_download
#define COMMUNITY_DOWNLOAD  "/download/community/" //chang by chenzhx V4.0 去掉_download

#define USER_DETAIL_DOWNLOAD "/download/UserDetail/"



//SOCKET相关
#define SOCKET_MULTICAST_PORT       8500
#define SOCKET_TCP_LISTEN_PORT      8501


#define CSCONFIG_CONF_COMMON_LEN 64

typedef struct CSCONFIG_CONF_T
{
    /* 本机IP配置信息 */
    char server_outer_ip[CSCONFIG_CONF_COMMON_LEN];
    char server_inner_ip[CSCONFIG_CONF_COMMON_LEN];
    char server_hostname[CSCONFIG_CONF_COMMON_LEN];

    /* 本机配置信息 */
    char csconfig_outer_ip[CSCONFIG_CONF_COMMON_LEN];
    int log_level; //日志打印级别 LOG_LEVEL_E
    char log_file[CSCONFIG_CONF_COMMON_LEN];

    /* cspbx本机配置信息 */
    char cspbx_outer_ip[CSCONFIG_CONF_COMMON_LEN];
    char cspbx_outer_port[CSCONFIG_CONF_COMMON_LEN];

    /* DB配置项 */
    char db_ip[CSCONFIG_CONF_COMMON_LEN];
    char db_username[CSCONFIG_CONF_COMMON_LEN];
    char db_password[CSCONFIG_CONF_COMMON_LEN];
    char db_database[CSCONFIG_CONF_COMMON_LEN];
    char db_socket_file[CSCONFIG_CONF_COMMON_LEN]; //如果这个文件不为空 代表用unix连接
    int  db_port;
    /*OEM 配置*/
    char oem_config[1024];

    /*远程服务器ssh代理地址*/
    char ssh_proxy_domain[128];

    int no_encrypt;
    char nsq_topic_for_del_pic[32];
    char nsq_route_topic[CSCONFIG_CONF_COMMON_LEN];//跟route的通信topic
    char etcd_server_addr[CSCONFIG_CONF_COMMON_LEN];
    char beanstalk_addr[CSCONFIG_CONF_COMMON_LEN];
    char beanstalk_tube[CSCONFIG_CONF_COMMON_LEN];
    char web_ip[CSCONFIG_CONF_COMMON_LEN];    
    char ftp_ip[CSCONFIG_CONF_COMMON_LEN];
    char vrtsp_server_domain[CSCONFIG_CONF_COMMON_LEN];
    char fdfs_config_addr[CSCONFIG_CONF_COMMON_LEN];    
    
    int config_server_domain_gray_percentage;
    char config_server_domain[CSCONFIG_CONF_COMMON_LEN];

    //Area list:1)ccloud 2)scloud 3)ecloud 4)ucloud 5)other 6)rcloud    
    int server_type;
    char community_ids[128];
    //是否亚马逊云
    int is_aws; 
    char aws_db_ip[CSCONFIG_CONF_COMMON_LEN];
    //重复刷userinfo超时时间 30s
    int repeated_userdetail_timeout;
    //重复刷ipchange超时时间 120s
    int repeated_ipchange_timeout;
    //web重复刷配置超时时间60秒
    int repeated_web_timeout;        
    int is_store_fdfs;
    
    char ip_change_filter[256];
    char user_info_filter[256];
    
    char project_uuid_filter[256];

    int check_big_project_handle_time;

    int write_thread_number;
    int write_file_number;

    char kafka_broker_ip[CSCONFIG_CONF_COMMON_LEN];
    char write_config_topic[CSCONFIG_CONF_COMMON_LEN];
    char write_config_group[CSCONFIG_CONF_COMMON_LEN]; 
    int write_config_thread_num;
    
    int write_user_detail_thread_num;
    
    int write_config_batch_read_num;
    int write_config_heap_up_num;
    //new office
    char notify_appbackend_analysis_topic[CSCONFIG_CONF_COMMON_LEN];
    char notify_appbackend_analysis_group[CSCONFIG_CONF_COMMON_LEN];    
    int notify_appbackend_analysis_thread_num;

    char beanstalk_delay_user_tube[CSCONFIG_CONF_COMMON_LEN];
} CSCONFIG_CONF;

#endif// __ADAPT_DEFINE_H__

