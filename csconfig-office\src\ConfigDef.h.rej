diff a/csconfig-office/src/ConfigDef.h b/csconfig-office/src/ConfigDef.h	(rejected hunks)
@@ -177,51 +177,51 @@
 #define CONFIG_MAGNETICD_DOOROPENED_TIMEOUT "Config.DoorSetting.DoorMagnetic.MagneticDTimer="
 
 // 反潜回
 #define CONFIG_RELAY_ANTIPASSBACK1 "Config.DoorSetting.ANTIPASSBACK.RelayA="
 #define CONFIG_RELAY_ANTIPASSBACK2 "Config.DoorSetting.ANTIPASSBACK.RelayB="
 #define CONFIG_RELAY_ANTIPASSBACK3 "Config.DoorSetting.ANTIPASSBACK.RelayC="
 #define CONFIG_RELAY_ANTIPASSBACK4 "Config.DoorSetting.ANTIPASSBACK.RelayD="
 #define CONFIG_SECURITY_RELAY_ANTIPASSBACK1 "Config.DoorSetting.ANTIPASSBACK.SeRelayA="
 #define CONFIG_SECURITY_RELAY_ANTIPASSBACK2 "Config.DoorSetting.ANTIPASSBACK.SeRelayB="
 
 // 强闯配置
 #define CONFIG_INPUT_BREAKIN_INTRUSION_A  "Config.DoorSetting.INPUT.Break-inIntrusionA="
 #define CONFIG_INPUT_BREAKIN_INTRUSION_B  "Config.DoorSetting.INPUT.Break-inIntrusionB="
 #define CONFIG_INPUT_BREAKIN_INTRUSION_C  "Config.DoorSetting.INPUT.Break-inIntrusionC="
 #define CONFIG_INPUT_BREAKIN_INTRUSION_D  "Config.DoorSetting.INPUT.Break-inIntrusionD="
 
 // 内开门配置
 #define CONFIG_EXIT_BUTTON_INPUT_A_RELAYID  "Config.DoorSetting.INPUTA.RelayId="
 #define CONFIG_EXIT_BUTTON_INPUT_B_RELAYID  "Config.DoorSetting.INPUTB.RelayId="
 #define CONFIG_EXIT_BUTTON_INPUT_C_RELAYID  "Config.DoorSetting.INPUTC.RelayId="
 #define CONFIG_EXIT_BUTTON_INPUT_D_RELAYID  "Config.DoorSetting.INPUTD.RelayId="
 
 #define CONFIG_EXIT_BUTTON_INPUT_RELAYA_ID  "Config.DoorSetting.INPUT.RelayId="
 #define CONFIG_EXIT_BUTTON_INPUT_RELAYB_ID  "Config.DoorSetting.INPUT.RelayBId="
 #define CONFIG_EXIT_BUTTON_INPUT_RELAYC_ID  "Config.DoorSetting.INPUT.RelayCId="
-#define CONFIG_EXIT_BUTTON_INPUT_RELAYD_ID  "Config.DoorSetting.INPUT.RelayCId="
+#define CONFIG_EXIT_BUTTON_INPUT_RELAYD_ID  "Config.DoorSetting.INPUT.RelayDId="
 
 // Relay Reader 配置
 #define CONFIG_RELAY_ENTRY_READER_A  "Config.DoorSetting.CLOUDSERVER.RelayAEntryReader="
 #define CONFIG_RELAY_ENTRY_READER_B  "Config.DoorSetting.CLOUDSERVER.RelayBEntryReader="
 #define CONFIG_RELAY_ENTRY_READER_C  "Config.DoorSetting.CLOUDSERVER.RelayCEntryReader="
 #define CONFIG_RELAY_ENTRY_READER_D  "Config.DoorSetting.CLOUDSERVER.RelayDEntryReader="
 #define CONFIG_RELAY_EXIT_READER_A   "Config.DoorSetting.CLOUDSERVER.RelayAExitReader="
 #define CONFIG_RELAY_EXIT_READER_B   "Config.DoorSetting.CLOUDSERVER.RelayBExitReader="
 #define CONFIG_RELAY_EXIT_READER_C   "Config.DoorSetting.CLOUDSERVER.RelayCExitReader="
 #define CONFIG_RELAY_EXIT_READER_D   "Config.DoorSetting.CLOUDSERVER.RelayDExitReader="
 
 // Security Relay Reader 配置
 #define CONFIG_SECURITY_RELAY_ENTRY_READER_A  "Config.DoorSetting.CLOUDSERVER.RelaySAEntryReader="
 #define CONFIG_SECURITY_RELAY_ENTRY_READER_B  "Config.DoorSetting.CLOUDSERVER.RelaySBEntryReader="
 #define CONFIG_SECURITY_RELAY_EXIT_READER_A   "Config.DoorSetting.CLOUDSERVER.RelaySAExitReader="
 #define CONFIG_SECURITY_RELAY_EXIT_READER_B   "Config.DoorSetting.CLOUDSERVER.RelaySBExitReader="
 
 #define CONFIG_DOOR_WHITE_LIST_RELAY_A     "Config.DoorSetting.CLOUDSERVER.WhiteListForRelayA"
 #define CONFIG_DOOR_WHITE_LIST_RELAY_B     "Config.DoorSetting.CLOUDSERVER.WhiteListForRelayB"
 #define CONFIG_DOOR_WHITE_LIST_RELAY_C     "Config.DoorSetting.CLOUDSERVER.WhiteListForRelayC"
 #define CONFIG_DOOR_WHITE_LIST_RELAY_D     "Config.DoorSetting.CLOUDSERVER.WhiteListForRelayD"
 
 #define CONFIG_DOOR_WHITE_LIST_RELAY_SA    "Config.DoorSetting.CLOUDSERVER.WhiteListForRelaySA"
 #define CONFIG_DOOR_WHITE_LIST_RELAY_SB    "Config.DoorSetting.CLOUDSERVER.WhiteListForRelaySB"
 
