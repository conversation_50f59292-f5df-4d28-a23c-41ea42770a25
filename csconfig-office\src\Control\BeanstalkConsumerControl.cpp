#include "BeanstalkConsumerControl.h"
#include <stdio.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <sys/types.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <errno.h>
#include <pthread.h>
#include <thread>
#include <assert.h>
#include <string.h>
#include <ctype.h>
#include <string>
#include <list>
#include <boost/crc.hpp>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "CharChans.h"
#include "ConfigDef.h"
#include "AKCSMsg.h"
#include "AkLogging.h"
#include "SysEnv.h"
#include "AkcsWebMsgSt.h"
#include "beanstalk.hpp"
#include "AK.Adapt.pb.h"
#include "AkcsMsgDef.h"
#include "util.h"
#include "json/json.h"
#include "AkcsCommonDef.h"
#include "AK.AdaptOffice.pb.h"
#include "GroupMsgMng.h"
#include "SnowFlakeGid.h"


extern CSCONFIG_CONF gstCSCONFIGConf;
Beanstalk::Client* g_bs_client_ptr = nullptr;
Beanstalk::Client* g_bs_backup_client_ptr = nullptr;
const std::string AKCS_ATTACK_IP_TUBE = "akcs_attack_ips"; //beanstalkd的tube,专用于akcs业务判断攻击者的来源ip用
std::mutex g_bs_mutex;
std::mutex g_bs_backup_mutex;

#define BEANSTALK_SERVER_PORT  (8519)


BeanstalkConsumerControl* BeanstalkConsumerControl::instance = NULL;
BeanstalkConsumerControl::BeanstalkConsumerControl()
{

}

BeanstalkConsumerControl::~BeanstalkConsumerControl()
{

}

BeanstalkConsumerControl* GetBeanstalkConsumerControlInstance()
{
    return BeanstalkConsumerControl::GetInstance();
}

BeanstalkConsumerControl* BeanstalkConsumerControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new BeanstalkConsumerControl();
    }

    return instance;
}

void BeanstalkConsumerControl::Init(const std::string& beanstalk_ip, const std::string &tube)
{    
    if(0 == beanstalk_ip.size())
    {
        return;
    }

    std::thread thread3(ProcessMsgForBeanstalk, tube, beanstalk_ip);
    thread3.detach();

    return;
}

//beanstalk因为目前是单节点 因此状态检查判断生产者状态即可
bool BeanstalkConsumerControl::CheckBeanstalkStatus()
{
    if(!g_bs_client_ptr)
    {
        return false;
    }
    return true;
}


void BeanstalkConsumerControl::InitPduBeanstalk()
{
    g_bs_client_ptr = new Beanstalk::Client(gstCSCONFIGConf.beanstalk_addr, BEANSTALK_SERVER_PORT);
}

void BeanstalkConsumerControl::AddMsgToBeanstalk(const char* msg, int len, const std::string &tube, int delay_interval)
{
    //默认1秒，aws延迟3秒，异常刷配置延迟180s
    uint32_t delay = delay_interval;
    
    AK_LOG_INFO << "Add msg to beanstalk tube:" << tube;
    int is_put_ok = 1;
    if (!g_bs_client_ptr)
    {
        std::lock_guard<std::mutex> lock(g_bs_mutex);
        g_bs_client_ptr = new Beanstalk::Client(gstCSCONFIGConf.beanstalk_addr, BEANSTALK_SERVER_PORT);
        g_bs_client_ptr->use(tube);
        if (!g_bs_client_ptr->put(msg, len, 0, delay, 1))
        {
            if (g_bs_client_ptr)
            {
                delete g_bs_client_ptr;
                g_bs_client_ptr = nullptr;
            }
            AK_LOG_WARN << "beanstalk put message error, put another";
            is_put_ok = 0;
        }
    }
    else
    {
        std::lock_guard<std::mutex> lock(g_bs_mutex);
        g_bs_client_ptr->use(tube);
        if (!g_bs_client_ptr->put(msg, len, 0, delay, 1))
        {
            if (g_bs_client_ptr)
            {
                delete g_bs_client_ptr;
                g_bs_client_ptr = nullptr;
            }
            AK_LOG_WARN << "beanstalk put message error, put another";
            is_put_ok = 0;
        }
    }  
}


void BeanstalkConsumerControl::ProcessMsgForBeanstalk(const std::string &tube, const std::string& beanstalk_ip)
{    
    AK_LOG_INFO << "Create ProcessThread:" << tube;
    
    while (1)
    {
        Beanstalk::Client client(beanstalk_ip.c_str(), BEANSTALK_SERVER_PORT);
        client.watch(tube);

        Beanstalk::Job job;
        while (client.reserve(job)) //reserve接口阻塞，正常情况不会跳出循环
        {
            client.del(job.id());
            AK_LOG_INFO << "process msg from tube:"<< tube;
            GetBeanstalkConsumerControlInstance()->DispatchMsg((void*)job.body().c_str(), job.body().length());
        }
        sleep(1);
    }

    return;
}


//msg_len:udp消息的整体长度,包括消息头
int BeanstalkConsumerControl::DispatchMsg(void* msg_buf, unsigned int msg_len)
{
    if ((NULL == msg_buf) || (msg_len < CS_COMMON_MSG_HEADER_SIZE))
    {
        return -1;
    }
    
    std::shared_ptr<CAkcsPdu> pdu = std::make_shared<CAkcsPdu>();
    pdu->Write((char*)msg_buf, msg_len);

    char tmp_buf[sizeof(PduHeader_t)] = {0};
    memcpy(tmp_buf, msg_buf, sizeof(tmp_buf));
    if (pdu->ReadPduHeader(tmp_buf, sizeof(PduHeader_t)) != 0) //赋值包长度的信息,并校验包头长度是否正确
    {
         AK_LOG_WARN << "Pdu packet header len is invalid";
         return -1;
    }

    uint32_t id = pdu->GetCommandId();
    uint32_t traceid =pdu->GetTraceId();
    
    if(traceid == 0)
    {
        traceid = GenRandUint32TraceId();
    }
    
    ThreadLocalSingleton::GetInstance().SetTraceID((uint64_t)traceid);    
    switch(id)
    {
        /*csmain 过来的消息+数据分析后(也是丢到csmian过来的队列)*/
        case MSG_S2C_DEV_CONFIG_REWRITE:
        {
            CGroupMsgMng::Instance()->HandleP2PDevConfigRewriteReq(pdu);
            break;
        }
        case MSG_S2C_ACCOUNT_CONFIG_REWRITE:
        {
            CGroupMsgMng::Instance()->HandleP2PDevConfigNodeRewriteReq(pdu);
            break;
        }
        case MSG_S2C_DEV_REQ_USER_INFO:
        {
            CGroupMsgMng::Instance()->HandleP2PDevWriteUserinfoReq(pdu);
            break;
        }
        default:
        {
            AK_LOG_WARN << "Failed to match msg id:" << id;
            return -1;
        }
    }

    AK_LOG_INFO << "BeanstalkConsumerControl::DispatchMsg end traceid:"<< traceid;
    return 0;
}


