#ifndef __UNIX_CONTROL_H__
#define __UNIX_CONTROL_H__

#include <map>

#define BEANSTALK_NORMAL_DELAY          1
#define BEANSTALK_AWS_DELAY             3
#define BEANSTALK_IPCHANGE_DELAY      180


class BeanstalkConsumerControl
{
public:
    BeanstalkConsumerControl();
    ~BeanstalkConsumerControl();
    static BeanstalkConsumerControl* GetInstance();
    void Init(const std::string& beanstalk_ip, const std::string &tube);
    
    //消费者线程
    static void ProcessMsgForBeanstalk(const std::string &tube, const std::string& beanstalk_ip);

    void AddMsgToBeanstalk(const char* msg, int len, const std::string &tube, int is_delay);
    int DispatchMsg(void* msg_buf, unsigned int msg_len);
    bool CheckBeanstalkStatus();
    void InitPduBeanstalk();    
private:
    BeanstalkConsumerControl(const BeanstalkConsumerControl&);
    BeanstalkConsumerControl& operator = (const BeanstalkConsumerControl&);


    static BeanstalkConsumerControl* instance;

    //消息消费者线程ID
    pthread_t thread_process;
};

BeanstalkConsumerControl* GetBeanstalkConsumerControlInstance();

#endif //__UNIX_CONTROL_H__
