#include <stdio.h>
#include <stdlib.h>
#include "BasicDefine.h"
#include "AkcsMsgDef.h"
#include "util.h"
#include "AkcsCommonDef.h"
#include "CachePool.h"
#include "CommonHandle.h"
#include "AkcsPduBase.h"
#include "AkcsWebPduBase.h"
#include "ConfigDef.h"
#include "AK.Adapt.pb.h"
#include "AkcsWebMsgSt.h"
#include "BeanstalkConsumerControl.h"
#include "SafeCacheConn.h"

extern CSCONFIG_CONF gstCSCONFIGConf;
extern int g_csmain_office_delay_tube;

extern const char*g_redis_db_proc_record;
extern const char*g_proc_record_bigprojec;

std::set<int> g_big_project_cache = {};

//旧R29有问题，6500的设备开始使用限流。
#define USER_INFO_LIMIT_DCLIENT_VERSION 6500

/*
过滤重复请求, $key=重复标识
*/   
int CommonHandle::FilterRequests(const std::string &key, const char *db, int repeate_check_time)
{
    int ret = FILTER_REQUEST_RET::REQ_FIRST;
    //目前都是用g_redis_db_userdetail库,key通过添加前缀防止重复
    SafeCacheConn cache_conn(db);
    if (cache_conn.isConnect())
    {
        if (!cache_conn.isExists(key))
        {
            //不存在执行生成操作+设置key
            cache_conn.set(key, std::to_string(1));
            cache_conn.expire(key, repeate_check_time);
            AK_LOG_INFO << "Requests REQ_FIRST Add Key:" << key;
        }
        else
        {
            std::string num = cache_conn.get(key);
            if (num == "1")
            {
                cache_conn.set(key, std::to_string(2));
                cache_conn.expire(key, repeate_check_time);
                ret = FILTER_REQUEST_RET::REQ_SECOND;
            }
            else if (num == "2") // timeout内执行3次 直接不处理
            {
                AK_LOG_WARN << "FilterRequests repeated excution counts 3 times in "
                      << repeate_check_time << " key:" << key;
                ret = FILTER_REQUEST_RET::REQ_MORE;
            }
        }
    }
    else
    {
         AK_LOG_WARN << "connect redis error. db:" << db;
    }
    return ret;
}

//只更新时间 不更新次数
int CommonHandle::FilterUpdateRequests(const std::string &key, const char *db, int repeate_check_time)
{
    SafeCacheConn cache_conn(db);
    if (cache_conn.isConnect())
    {
        if (cache_conn.isExists(key))
        {
            cache_conn.expire(key, repeate_check_time);
        }
        else
        {
            //不存在执行生成操作+设置key
            cache_conn.set(key, std::to_string(1));
            cache_conn.expire(key, repeate_check_time);
        }        
    }
    else
    {
         AK_LOG_WARN << "connect redis error. db:" << db;
    }

    return 0;
}


int CommonHandle::CheckIpchangeRequest(const AK::Server::P2PMainDevConfigRewriteMsg &msg, int project_type)
{
    if (msg.type() == CSMAIN_UPDATE_CONFIG_IP_CHANGE && !msg.already_check())
    {
        std::string key = msg.mac() + "-IPCHANGE";
        int ret = FilterRequests(key, g_redis_db_userdetail, gstCSCONFIGConf.repeated_ipchange_timeout);
        if (ret == FILTER_REQUEST_RET::REQ_SECOND)
        {
            //120秒内变化两次,第二次不执行，放入120延时的延时队列，后面再执行。
            //int tube = GetRandomNum(gstCSCONFIGConf.write_thread_number);
        
            AK::Server::P2PMainDevConfigRewriteMsg new_msg;
            new_msg.set_mac(msg.mac());
            new_msg.set_ip(msg.ip());
            new_msg.set_type(msg.type());
            new_msg.set_already_check(1);//防止这条消息从延时队列出来后 又进行判断
            
            CAkcsPdu pdu;
            pdu.SetMsgBody(&new_msg);
            pdu.SetHeadLen(sizeof(PduHeader_t));
            pdu.SetVersion(50);
            pdu.SetCommandId(MSG_S2C_DEV_CONFIG_REWRITE);
            pdu.SetSeqNum(0);

            GetBeanstalkConsumerControlInstance()->AddMsgToBeanstalk(pdu.GetBuffer(), pdu.GetLength(), gstCSCONFIGConf.beanstalk_delay_user_tube,
                 gstCSCONFIGConf.repeated_ipchange_timeout + 1); //防止延时队列中的执行完了，redis还没有到期，从而丢掉了最新的请求。
            AK_LOG_INFO << "Ip change key:" << key << " handle delay " << gstCSCONFIGConf.repeated_ipchange_timeout <<  "s";
            
            return 1;
        }
        else if (ret == FILTER_REQUEST_RET::REQ_MORE)
        {
            //TODO:重复更新时候如果没有更新IP,下次csmain还是会判断为变化，继续发送变化的请求.
            //去更新表的ip字段意义不大，这时候设备就是异常，会一直变化
            AK_LOG_WARN << "HandleP2PDevConfigRewriteReq: ignore. request repeated excution counts 3 times in "
                  << gstCSCONFIGConf.repeated_ipchange_timeout << " second,Device Mac : " << msg.mac() << " key:" << key;
            return 1;
        }
    }
    return 0;
}

int CommonHandle::CheckUserInfoRequest(const AK::Server::P2PMainRequestWriteUserinfo &msg, int project_type)
{
    if (!msg.already_check() && msg.dclient_ver() >= USER_INFO_LIMIT_DCLIENT_VERSION)
    {
        std::string key = "USER-" + msg.mac() + "-" + msg.accounts_key();
        
        int ret = FilterRequests(key, g_redis_db_userdetail, gstCSCONFIGConf.repeated_userdetail_timeout);
        if (ret == FILTER_REQUEST_RET::REQ_SECOND)
        {
            //30秒内变化两次,第二次不执行，放入30延时的延时队列，后面再执行。
            //int tube = GetRandomNum(gstCSCONFIGConf.write_thread_number);
            AK::Server::P2PMainRequestWriteUserinfo new_msg;
            new_msg.set_mac(msg.mac());
            new_msg.set_accounts_key(msg.accounts_key());
            new_msg.set_uuids(msg.uuids());
            new_msg.set_msg_traceid(msg.msg_traceid());
            new_msg.set_timestamp(msg.timestamp());
            new_msg.set_dclient_ver(msg.dclient_ver());
            new_msg.set_project_uuid(msg.project_uuid());
            new_msg.set_already_check(1);//防止这条消息从延时队列出来后 又进行判断
            
            CAkcsPdu pdu;
            pdu.SetMsgBody(&new_msg);
            pdu.SetHeadLen(sizeof(PduHeader_t));
            pdu.SetVersion(50);
            pdu.SetCommandId(MSG_S2C_DEV_REQ_USER_INFO);
            pdu.SetSeqNum(0);
            
            GetBeanstalkConsumerControlInstance()->AddMsgToBeanstalk(pdu.GetBuffer(), pdu.GetLength(), gstCSCONFIGConf.beanstalk_delay_user_tube,
                gstCSCONFIGConf.repeated_userdetail_timeout + 1); //防止延时队列中的执行完了，redis还没有到期，从而丢掉了最新的请求。
            AK_LOG_INFO << "Request user mac:"<< msg.mac() << ", key:" << key << " handle delay " << gstCSCONFIGConf.repeated_userdetail_timeout <<  "s";
            
            return 1;
        }
        else if (ret == FILTER_REQUEST_RET::REQ_MORE)
        {
            //TODO:不会走到这个流程，因为被防止csconfig消息处理不过来设备，然后一直重复请求的给拦截了
            AK_LOG_WARN << "HandleP2PDevWriteUserinfoReq: ignore. request user repeated excution counts 3 times in "
                  << gstCSCONFIGConf.repeated_userdetail_timeout << " second,Device Mac : " << msg.mac() << " key:" << key;
            return 1;
        }
    }
    return 0;
}

int CommonHandle::CheckBigProject(std::time_t start, std::time_t end, int project_id)
{
    if (project_id > 0 && end - start > gstCSCONFIGConf.check_big_project_handle_time)
    {
        if (g_big_project_cache.count(project_id))
        {
            return 0;
        }
        std::string key = g_proc_record_bigprojec + std::to_string(project_id);

        SafeCacheConn cache_conn(g_redis_db_proc_record);
        if (cache_conn.isConnect())
        {
            if (!cache_conn.isExists(key))
            {
                cache_conn.set(key, std::to_string(project_id));
                g_big_project_cache.insert(project_id);
                AK_LOG_INFO << "Check Big Project, Add Project:" << project_id << " redis key:" << key;
            }
        }
     
    }
    return 0;
}

int CommonHandle::IsBigProject(int project_id)
{
    int ret = 0;
    std::string key = g_proc_record_bigprojec + std::to_string(project_id);
    SafeCacheConn cache_conn(g_redis_db_proc_record);
    if (cache_conn.isConnect())
    {
        if (cache_conn.isExists(key))
        {
            ret = 1;
        }
    }
    return ret;
}


