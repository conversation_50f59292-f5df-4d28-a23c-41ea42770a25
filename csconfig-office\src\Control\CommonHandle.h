#ifndef __COMMON_HANDLE_CONTROL_H__
#define __COMMON_HANDLE_CONTROL_H__

#include <map>
#include "AK.Server.pb.h"
#include <set>
#include <algorithm>
#include <chrono>


enum FILTER_REQUEST_RET{
   REQ_FIRST,
   REQ_SECOND, 
   REQ_MORE, 
};

class CommonHandle
{
public:
    CommonHandle(){}
    ~CommonHandle(){}

    static int CheckIpchangeRequest(const AK::Server::P2PMainDevConfigRewriteMsg &msg, int project_type);
    static int CheckUserInfoRequest(const AK::Server::P2PMainRequestWriteUserinfo &msg, int project_type);

    static int CheckBigProject(std::time_t start, std::time_t end, int project_id);
    static int IsBigProject(int project_id);
        
    static int CheckAccessGroupUpdateRequest(int changetype, uint32_t mng_id, const std::string &node, 
        std::set<std::string> &macs, uint32_t ag_id, int already_check);
    
private:
    static int FilterRequests(const std::string &key, const char *db, int repeate_check_time);
    static int FilterUpdateRequests(const std::string &key, const char *db, int repeate_check_time);
    
};


#endif //__UNIX_CONTROL_H__
