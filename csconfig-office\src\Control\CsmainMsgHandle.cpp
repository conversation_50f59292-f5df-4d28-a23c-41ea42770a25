#include "stdlib.h"
#include <functional>
#include "AkcsPduBase.h"
#include "AkcsWebPduBase.h"
#include "util.h"
#include<evpp/evnsq/message.h>
#include "CsmainMsgHandle.h"
#include "evpp/event_loop.h"
#include "AkcsMonitor.h"
#include "AkcsIpcMsgCodec.h"
#include "AkcsMsgDef.h"
#include "GroupMsgMng.h"
#include "AkcsCommonDef.h"
#include <arpa/inet.h>
#include "ConfigDef.h"

CsmainMsgHandle* CsmainMsgHandle::instance_ = nullptr;
extern CSCONFIG_CONF gstCSCONFIGConf;
CsmainMsgHandle* GetCsmainMsgHandleInstance()
{
    return CsmainMsgHandle::GetInstance();
}


CsmainMsgHandle::CsmainMsgHandle()
{
    tread_number_ = 1;//modify by chenzhx 20220531 写配置已经统一移到和web处理线程
    //modified by chenyc,2021.11.26,调整线程数量，加快速度.从2->4,因为该类消费者线程基本执行的任务都是阻塞的
    //经过实测，单线程跑满的时候 占用的cpu也就20-25%左右，因此对于该消费者的能力提升可以先简单提升线程数来处理
    //但是需要注意,同一个节点上不可能所有模块都通过增加线程来提升效率，线程越多导致cpu切换过多，整体负载下降
    //tread_number_ = 2;//20220214 4->2 mysql 磁盘扛不住
}

CsmainMsgHandle::~CsmainMsgHandle()
{

}

CsmainMsgHandle* CsmainMsgHandle::GetInstance()
{
    if (instance_ == nullptr)
    {
        instance_ = new CsmainMsgHandle();
    }

    return instance_;
}


int CsmainMsgHandle::InitConsumerThread()
{
    for (int i = 0; i < tread_number_; ++i)
    {
        int *p = new int(i);//不回收了
        pthread_create(&consumer_threads_, nullptr, CsmainMsgThread, (void*)p);
    }

}

void* CsmainMsgHandle::CsmainMsgThread(void* id)
{
    int thread = *(int *)id;
    GetCsmainMsgHandleInstance()->ProcessMsg(thread);
    return 0;
}

void CsmainMsgHandle::AddMsg(const std::unique_ptr<CAkcsPdu>& pdu)
{
    {
        std::unique_lock<std::mutex> lock(mute_);
        std::shared_ptr<CAkcsPdu> msg = std::move(const_cast<std::unique_ptr<CAkcsPdu>& >(pdu));
        msg_list_.push_front(msg);
        cv_.notify_all();
    }    
}

int CsmainMsgHandle::GetMsgListSize()
{
    std::unique_lock<std::mutex> lock(mute_);
    return msg_list_.size();
}

int CsmainMsgHandle::ProcessMsg(int thread)
{
    while (1)
    {
        AkcsPduPrt msgptr;
        {
            std::unique_lock<std::mutex> lock(mute_);
            while (msg_list_.size() == 0)
            {
                cv_.wait(lock);
            }
            //检测队列长度
            //modified by chenyc,2021.11.26 当大项目社区的公共设备(含梯口机)数量较多的时候,此时pm的一些设置操作会导致所有公共设备一起过来刷新，频繁触发此告警
            if (msg_list_.size() > 200)
            {
                std::string worker_node = "csconfig-office";
                AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm(worker_node, "handle dev request user detail data msg queue overflow", AKCS_MONITOR_ALARM_QUEUE_OVERFLOW_CSMAIN_NOTIFY);
                AK_LOG_WARN << "csmain->csconfig msg queue overflow, the length now is " << msg_list_.size();
            }
            msgptr = msg_list_.back();
            msg_list_.pop_back();
        }
        OnMessage(msgptr);
    }
    return 0;
}

void CsmainMsgHandle::OnMessage(const AkcsPduPrt &pdu)
{
    uint32_t traceid = GenRandUint32TraceId();
    ThreadLocalSingleton::GetInstance().SetTraceID((uint64_t)traceid);

    int id = pdu->GetCommandId();
    switch(id)
    {
        case MSG_S2C_DEV_CONFIG_REWRITE:
        {
            CGroupMsgMng::Instance()->HandleP2PDevConfigRewriteReq(pdu);
            break;
        }
        case MSG_S2C_ACCOUNT_CONFIG_REWRITE:
        {
            CGroupMsgMng::Instance()->HandleP2PDevConfigNodeRewriteReq(pdu);
            break;
        }
        case MSG_S2C_DEV_REQ_USER_INFO:
        {
            CGroupMsgMng::Instance()->HandleP2PDevWriteUserinfoReq(pdu);
            break;         
        }       
    }

}
