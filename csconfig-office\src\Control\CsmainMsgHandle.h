#ifndef __CSMAIN_MSG_HANDLE_H__
#define __CSMAIN_MSG_HANDLE_H__

#include <memory>
#include <functional>
#include <evpp/buffer.h>
#include <evpp/any.h>
#include <string>
#include <condition_variable>
#include "AkcsPduBase.h"

class CsmainMsgHandle
{
public:
    CsmainMsgHandle();
    ~CsmainMsgHandle();

    int InitConsumerThread();
    static void* CsmainMsgThread(void* id);
    void AddMsg(const std::unique_ptr<CAkcsPdu>& pdu);
    int ProcessMsg(int thread);
    int GetMsgListSize();
    static CsmainMsgHandle* GetInstance();
    void OnMessage(const AkcsPduPrt &pdu);

   static CsmainMsgHandle* instance_;
private:
   int tread_number_;    
   pthread_t consumer_threads_;
   AkcsPduList msg_list_;
   std::mutex mute_;
   std::condition_variable cv_;
};

CsmainMsgHandle* GetCsmainMsgHandleInstance();


#endif //__CSMAIN_MSG_HANDLE_H__

