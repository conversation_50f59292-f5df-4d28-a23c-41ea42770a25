#include "catch.hpp"
#include <etcd/Client.hpp>
#include "EtcdCliMng.h"
#include <vector>
#include "ConfigDef.h"
#include "util.h"
#include "RouteClient.h"
#include "RouteClientMng.h"
#include "loop/RouteLoopManager.h"
#include "evpp/event_watcher.h"

extern CSCONFIG_CONF gstCSCONFIGConf;
CAkEtcdCliManager* g_etcd_cli_mng = nullptr;
std::string g_logic_srv;
static evpp::SignalEventWatcher* ev = nullptr;
extern const char *g_ak_srv_route;

std::shared_ptr<evpp::EventLoop> g_etcd_loop = std::shared_ptr<evpp::EventLoop>(new evpp::EventLoop);

//与所有的csroute建立tcp连接
void RouteSrvConnInit(const std::set<std::string>& csroute_addrs, evpp::EventLoop* loop,
                      const std::string& logic_srv_id)
{
    for (const auto& csroute : csroute_addrs) //ip:port的形式
    {
        RouteClientPtr route_cli_ptr(new CRouteClient(loop, csroute, "csconfig client", logic_srv_id));
        route_cli_ptr->Start();
        CRouteClientMng::Instance()->AddRouteSrv(csroute, route_cli_ptr);
    }
}

static int64_t RegSrv2Etcd(const std::string& key, const std::string& value, const int ttl, int type, evpp::EventLoop* loop)
{
    return g_etcd_cli_mng->RegKeepAliveSrv(key, value, ttl, type, loop);
}

//监控的回调函数,不能阻塞
void UpdateRouteSrvList()
{
    std::set<std::string> csroute_addrs;
    if (g_etcd_cli_mng->GetAllRouteSrvs(csroute_addrs) == 0)
    {
        //更新route的连接列表
        CRouteClientMng::Instance()->UpdateRouteSrv(csroute_addrs, GetRouteLoopManagerInstance()->GetRouteLoop(), g_logic_srv);
    }
}


void EtcdSrvInit()
{
    g_logic_srv = "csconfig";
    g_logic_srv += GetEth0IPAddr();
    g_etcd_cli_mng = CAkEtcdCliManager::GetInstance(gstCSCONFIGConf.etcd_server_addr);//"ip:port;ip:port;..."
    std::set<std::string> csroute_addrs;
    if (g_etcd_cli_mng->GetAllRouteSrvs(csroute_addrs) != 0)
    {
        AK_LOG_FATAL << "connetc to etcd srv fialed";
    }
    RouteSrvConnInit(csroute_addrs, GetRouteLoopManagerInstance()->GetRouteLoop(), g_logic_srv);

    GetRouteLoopManagerInstance()->StartLoop();

    g_etcd_cli_mng->AddAsyncWatchKey(g_ak_srv_route, UpdateRouteSrvList);
    g_etcd_cli_mng->CheckEtcdHealth(g_etcd_loop.get()); 

    g_etcd_loop->Run();//etcd_loop 目前只有route的连接在用

}

