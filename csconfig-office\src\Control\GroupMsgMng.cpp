#include "AK.Server.pb.h"
#include "AkcsMsgDef.h"
#include "GroupMsgMng.h"
#include <ctime>
#include "ConfigDef.h"
#include "json/json.h"
#include "AkcsMonitor.h"
#include "AkcsCommonDef.h"
#include "AkcsWebPduBase.h"
#include "AkcsWebMsgSt.h"
#include "util.h"
#include "CachePool.h"
#include "CommonHandle.h"
#include "OfficePduConfigMsg.h"
#include "OfficeNew/ConfigFile/OfficeNewDevUser.h"
#include "dbinterface/office/OfficeDevices.h"
#include "IPCControl.h"
#include "OfficeNew/ConfigFile/OfficeNewConfigHandle.h"
#include "SafeCacheConn.h"
#include "OfficeUserDetailControl.h"

#include "dbinterface/ProjectUserManage.h"

extern CSCONFIG_CONF gstCSCONFIGConf;
extern const char* g_redis_db_userdetail;
CGroupMsgMng* CGroupMsgMng::s_group_msg_mng_instance_ = nullptr;

CGroupMsgMng* CGroupMsgMng::Instance()
{
    if (!s_group_msg_mng_instance_)
    {
        s_group_msg_mng_instance_ = new CGroupMsgMng();
    }
    return s_group_msg_mng_instance_;
}

void CGroupMsgMng::HandleP2PDevConfigRewriteReq(const AkcsPduPrt &pdu)
{
    AK::Server::P2PMainDevConfigRewriteMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));

    if(msg.type() == CSMAIN_UPDATE_CONFIG_IP_CHANGE  && IpChangeMacFilter(msg.mac()))
    {
        AK_LOG_INFO << "IpChangeMacFilter mac:"<<msg.mac();
        return;
    }
    if (CommonHandle::CheckIpchangeRequest(msg, project::OFFICE) != 0 )
    {
        return;
    }
    OfficeDevPtr dev;
    if (dbinterface::OfficeDevices::GetMacDev(msg.mac(), dev) == 0)
    {
        OfficeFileUpdateInfo update_info(dev->project_uuid, OfficeUpdateType::OFFICE_DEV_INFO_CHANGE);
        update_info.AddDevUUIDToList(msg.mac());
        ProduceConfigUpdateMsg(update_info.GetUUID(), update_info.GetInfo());
    }
}



void CGroupMsgMng::HandleP2PDevConfigNodeRewriteReq(const AkcsPduPrt &pdu)
{
    AK::Server::P2PMainAccountConfigRewriteMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));

    AK_LOG_INFO << "receive ConfigNodeRewrite msg:" << msg.DebugString();
    dbinterface::ProjectUserManage::UpdataDataVersionByAccount(msg.account());
    
    
    OfficeAccount account;
    if (dbinterface::OfficePersonalAccount::GetUidAccount(msg.account(), account) == 0)
    {
        //当前这个node更新只有app 被挤掉后nfc和ble需要更新
        OfficeFileUpdateInfo update_info(account.parent_uuid, OfficeUpdateType::OFFICE_USER_ACCESS_CHANGE);
        dbinterface::OfficePersonalAccount::UpdateVersionByUUID(account.uuid);        
        ProduceConfigUpdateMsg(update_info.GetUUID(), update_info.GetInfo());
    }
    GetIPCControlInstance()->NotifyAppRefreshConfig(msg.account(), project::OFFICE);

}


void CGroupMsgMng::HandleP2PDevWriteUserinfoReq(const AkcsPduPrt &pdu)
{
    AK::Server::P2PMainRequestWriteUserinfo msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray(pdu->GetBodyData(), pdu->GetBodyLength()));

    uint64_t traceid = msg.msg_traceid();
    ThreadLocalSingleton::GetInstance().SetTraceID(traceid);    
    
    AK_LOG_INFO << "receive user info msg:" << msg.DebugString();
    if (CommonHandle::CheckUserInfoRequest(msg, project::OFFICE) != 0)
    {
        AK_LOG_INFO << "request is already in queue.";
        return;
    }
	
	if(UserInfoMacFilter(msg.mac()))
    {
        AK_LOG_INFO << "UserInfoMacFilter mac:"<<msg.mac();
        return;
    }
    
    SafeCacheConn cache_conn(g_redis_db_userdetail);
    if (cache_conn.isConnect())
    {
        //csmain插入，防止处理不过来，设备一直重复请求
        cache_conn.del(msg.accounts_key());
    }
    uint64_t msg_time = msg.timestamp();
    std::time_t t = std::time(0);
    if (t - msg_time > 60)
    {
        AK_LOG_INFO << "Devices request user handle time more than 60s.";
    }

    OfficeDevPtr dev;
    if (dbinterface::OfficeDevices::GetMacDev(msg.mac(), dev) == 0)
    {
        UserDetailPtr ptr = std::make_shared<AK::Server::P2PMainRequestWriteUserinfo>(msg);
        AKCS::Singleton<OfficeUserDetailControl>::instance().AddUserDetailMsg(dev->project_uuid, ptr);       
    }   
}

bool CGroupMsgMng::IpChangeMacFilter(const std::string mac)
{
    if(strlen(gstCSCONFIGConf.ip_change_filter) == 0)
    {
        return false;
    }
    if(strstr(gstCSCONFIGConf.ip_change_filter, mac.c_str()) != nullptr)
    {
        return true;
    }
    return false;
}
bool CGroupMsgMng::UserInfoMacFilter(const std::string mac)
{
    if(strlen(gstCSCONFIGConf.user_info_filter) == 0)
    {
        return false;
    }
    if(strstr(gstCSCONFIGConf.user_info_filter, mac.c_str()) != nullptr)
    {
        return true;
    }
    return false;
}

