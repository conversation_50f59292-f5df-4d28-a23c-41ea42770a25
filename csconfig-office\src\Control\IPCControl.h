#ifndef __IPC_CONTROL_H__
#define __IPC_CONTROL_H__

#include "stdint.h"

typedef struct CSP2A_REBOOT_DEVICE_T  CSP2A_REBOOT_DEVICE;

//modified by chenyc,2019-03-05, 由原先的跟csmain的直接通信改成经过csroute,ipc的名称暂时不变.
class CIPCControl
{
public:
    CIPCControl();
    virtual ~CIPCControl();
    static CIPCControl* GetInstance();

    //个人终端用户,发送请求设备状态的UDP消息给csmain进程
    int SendPersonalReportStatus(std::string mac);
    int SendPerDevLogOutSip(const std::string& mac);
    int SendPerUidLogOutSip(const std::string& uid);

    //清空设备码
    int SendDevCleanDeviceCode(const CSP2A_DEV_CLEAN_DEVICE_CODE* pstExpire);

    int NotifyRefreshConnCache(CSP2A_REFRESH_CACHE& info);
    int SendDevFileChange(CSP2A_DEV_FILE_CHANGE* dev_change);

    int SendConfigFileChange(CSP2A_CONFIG_FILE_CHANGE* file_change);
    void NotifyChangeMainSite(const std::string& after_main_site, const std::string& before_main_site);

    void NotifyAppRefreshConfig(const std::string &account, int project_type);
    void NotifyDevFileChange(const std::string &mac, int type, uint64_t traceid, const std::string &file, const std::string &file_md5 );

    void NotifyMacChange(const std::string& mac); 
    void NotifyDeviceIsAttendance(const std::string& device_uuid);
private:
    static CIPCControl* instance;
};

CIPCControl* GetIPCControlInstance();
#endif //__IPC_CONTROL_H__

