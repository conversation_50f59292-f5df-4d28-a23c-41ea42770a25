#ifndef __CSCONFIG_OFFICE_INNER_DB_H__
#define __CSCONFIG_OFFICE_INNER_DB_H__


#include "dbinterface/Account.h"
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/new-office/OfficeAccessGroupDevice.h"
#include "dbinterface/new-office/OfficeGroupAccessGroup.h"
#include "dbinterface/new-office/OfficePersonnelGroup.h"
#include "dbinterface/new-office/OfficeGroup.h"
#include "dbinterface/new-office/OfficeAccessGroup.h"
#include "dbinterface/new-office/OfficeCompany.h"
#include "dbinterface/new-office/OfficeDeviceAssign.h"
#include "dbinterface/new-office/OfficePersonnel.h"
#include "dbinterface/new-office/OfficeAdmin.h"
#include "dbinterface/new-office/OfficeAdminGroup.h"
#include "dbinterface/new-office/OfficeGroupSequenceCall.h"
#include "dbinterface/new-office/OfficeHoliday.h"
#include "dbinterface/new-office/OfficeDelivery.h"
#include "dbinterface/new-office/OfficeDeliveryAccessGroup.h"
#include "dbinterface/new-office/OfficeGroupAccessFloor.h"
#include "dbinterface/new-office/OfficeDeliveryAccessFloor.h"
#include "dbinterface/Sip.h"
#include "dbinterface/VersionModel.h"
#include "dbinterface/new-office/OfficePinCode.h"
#include "dbinterface/new-office/OfficeRfCard.h"
#include "dbinterface/new-office/OfficeFace.h"
#include "dbinterface/new-office/OfficeDeliveryAccessFloor.h"
#include "dbinterface/new-office/OfficeGroupAccessFloor.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/new-office/OfficeCompany.h"
#include "dbinterface/VersionModel.h"
#include "dbinterface/new-office/OfficeCompanyAccessFloor.h"
#include "dbinterface/new-office/OfficeLicensePlate.h"
#endif // __CSCONFIG_OFFICE_INNER_DB_H__