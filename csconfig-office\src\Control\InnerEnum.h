#ifndef __CSCONFIG_OFFICE_INNER_ENUM_H__
#define __CSCONFIG_OFFICE_INNER_ENUM_H__
#include "json/json.h"


enum OfficeUUIDType{
   PER_UUID,
   GROUP_UUID,
   COMPANY_UUID
};

class OfficeUserDetailReq
{
public:
    std::string file_md5;
    std::string write_file_path;
    std::string download_file_path;
    uint64_t trarceid;
    
};

using OfficePerIDSet = std::set<std::string>;
using OfficeUserJson = std::vector<Json::Value>; 
using GetCompanyNameCb = std::function<std::string(const std::string&, OfficeUUIDType)>;
using GetGroupNameCb = std::function<std::string(const std::string&, OfficeUUIDType)>;

#endif // __CSCONFIG_OFFICE_INNER_ENUM_H__