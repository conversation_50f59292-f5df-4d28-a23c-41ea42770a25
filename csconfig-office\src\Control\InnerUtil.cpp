#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <pthread.h>
#include <string.h>
#include <ctype.h>
#include <string>
#include <sstream>
#include <unistd.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <iomanip>
#include <sys/ioctl.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <net/if.h>
#include <assert.h>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "CharChans.h"
#include "ConfigDef.h"
#include "AkcsCommonDef.h"
#include <json/json.h>
#include "AkLogging.h"
#include "InnerUtil.h"
#include <atomic>
#include "util_virtual_door.h"


//创建一个路径
void CreateDir(const std::string& strDirectory)
{
    std::stringstream strTmpDir;
    strTmpDir << "mkdir -p "
              << strDirectory;
    //调用shell脚本删除
    if (system(strTmpDir.str().c_str()) < 0)
    {
        AK_LOG_WARN << "Failed to CreateDir on " << strDirectory.c_str() << ", errno:" << errno;
    }
}


//获取HTTP根目录
std::string GetHttpRootPath()
{
    std::string path = HTTPROOT;
    return std::move(path);
}

void GetContactDoorRelayInfo(const DevicesDoorInfoList& door_list, std::stringstream& relay_contact_info, std::stringstream& security_relay_contact_info)
{
    for (const auto& door : door_list)
    {
        if (door.relay_type == DoorRelayType::RELAY)
        {
            if (KRelay2ValueMap.find(door.controlled_relay) != KRelay2ValueMap.end())
            {
                relay_contact_info << KRelay2ValueMap.at(door.controlled_relay) << "," << door.name << "," << door.dtmf << ";";
            }
        }
        else if (door.relay_type == DoorRelayType::SECURITY_RELAY)
        {
            if (KRelay2ValueMap.find(door.controlled_relay) != KRelay2ValueMap.end())
            {
                security_relay_contact_info << KRelay2ValueMap.at(door.controlled_relay) << "," << door.name << "," << door.dtmf << ";";
            }
        }
    }
    return;
}

std::string GetRelayContactStr(const std::vector<RELAY_INFO>& relay_infos)
{
    int i = 0;
    std::stringstream relay_str;
    for (auto& relay : relay_infos)
    {
        if (!relay.enable)
        {
            i++;
            continue;
        }

        relay_str << i << "," << relay.name << ",";
        if (10 == relay.dtmf)
        {
            relay_str << "*";
        }
        else if (11 == relay.dtmf)
        {
            relay_str << "#";
        }
        else
        {
            relay_str << relay.dtmf;
        }
        i++;

        if (i < relay_infos.size())
        {
            relay_str << ";";
        }
    }

    return relay_str.str();
}

std::string GetSecurityRelayConfig(const std::vector<RELAY_INFO>& relay_infos)
{
    int i = 1;
    std::stringstream config_str;
    for (auto &relay : relay_infos)
    {
        switch (i)
        {
            case 1:
                config_str << CONFIG_SECURITY_RELAY_DMTF_CODE1 << relay.dtmf << "\n";
                config_str << CONFIG_SECURITY_RELAY_DMTF_NAME1 << relay.name << "\n";
                config_str << CONFIG_SECURITY_RELAY_ENABLED1 << relay.enable << "\n";
                break;

            case 2:
                config_str << CONFIG_SECURITY_RELAY_DMTF_CODE2 << relay.dtmf << "\n";
                config_str << CONFIG_SECURITY_RELAY_DMTF_NAME2 << relay.name << "\n";
                config_str << CONFIG_SECURITY_RELAY_ENABLED2 << relay.enable << "\n";
                break;

            case 3:
                config_str << CONFIG_SECURITY_RELAY_DMTF_CODE3 << relay.dtmf << "\n";
                config_str << CONFIG_SECURITY_RELAY_DMTF_NAME3 << relay.name << "\n";
                config_str << CONFIG_SECURITY_RELAY_ENABLED3 << relay.enable << "\n";
                break;

            case 4:
                config_str << CONFIG_SECURITY_RELAY_DMTF_CODE4 << relay.dtmf << "\n";
                config_str << CONFIG_SECURITY_RELAY_DMTF_NAME4 << relay.name << "\n";
                config_str << CONFIG_SECURITY_RELAY_ENABLED4 << relay.enable << "\n";
                break;

            default:
                //只支持4个
                break;
        }
        i++;
    }

    return config_str.str();
}

std::string GetNewOfficePath(const std::string &uuid)
{
    std::size_t mac_hash = std::hash<std::string> {}(uuid);
    std::size_t hash = mac_hash % 64;

    std::stringstream path;
    path << GetHttpRootPath() << COMMUNITY_DOWNLOAD
            << "NewOffice/" << hash << "/" << uuid << "/";
            
    CreateDir(path.str());
    return path.str();
}

std::string GetUserDetailDownloadPath(const std::string mac, std::string &web_download_path)
{
    std::size_t mac_hash = std::hash<std::string> {}(mac);
    std::size_t hash = mac_hash % 64;
    std::stringstream path;
    std::stringstream download_path;

    std::string date = GetNowDate();
    //日期/64个节点/mac_traceid.json"
    path << GetHttpRootPath() << USER_DETAIL_DOWNLOAD
            << date << "/"
            << hash << "/";
    CreateDir(path.str());


    download_path << USER_DETAIL_DOWNLOAD
            << date << "/"
            << hash << "/";
    web_download_path = download_path.str();
    
    return path.str();
}


//更新有这个设备权限的用户version
void UpdateUserVersionByDevices(const std::string &dev_uuid)
{
    AkcsStringSet ag_list;
    dbinterface::OfficeAccessGroup::GetGroupAccessGroupByDeviceUUID(dev_uuid, ag_list);

    AkcsStringSet group_set;
    for (auto &it : ag_list)
    {
        dbinterface::OfficeGroupAccessGroup::GetGroupUuidsByAgUUID(it, group_set);
    }
    dbinterface::OfficePersonalAccount::UpdateVersionByGroupUUID(group_set);

    dbinterface::OfficeDelivery::UpdateVersionByAgGroupUUID(ag_list);
}

//更新权限组相关连的用户版本
void UpdateUserVersionByAgUUID(const std::string &ag_uuid)
{
    AkcsStringSet ag_list;
    ag_list.insert(ag_uuid);

    AkcsStringSet group_set;
    dbinterface::OfficeGroupAccessGroup::GetGroupUuidsByAgUUID(ag_uuid, group_set);
    dbinterface::OfficePersonalAccount::UpdateVersionByGroupUUID(group_set);
    
    dbinterface::OfficeDelivery::UpdateVersionByAgGroupUUID(ag_list);
}

//更新group对应的用户版本
void UpdateUserVersionByGroupUUID(const std::string &gruop_uuid)
{
    AkcsStringSet group_list;
    group_list.insert(gruop_uuid);
    dbinterface::OfficePersonalAccount::UpdateVersionByGroupUUID(group_list);
}

//删除权限组 更新公司下所有用户的版本号
void UpdateUserVersionByCompanyUUID(const std::string &company_uuid)
{
    AkcsStringSet group_list;
    dbinterface::OfficeGroup::GetUuidsByCompanyUUID(company_uuid, group_list);
    dbinterface::OfficePersonalAccount::UpdateVersionByGroupUUID(group_list);
}