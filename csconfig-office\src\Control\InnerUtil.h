#ifndef __INNER_UTILITY_H__
#define __INNER_UTILITY_H__
#include <vector>
#include <string>
#include "util.h"
#include "AKCSMsg.h"
#include "util_relay.h"
#include "util_virtual_door.h"
#include "dbinterface/new-office/DevicesDoorList.h"
#include "dbinterface/new-office/OfficeAccessGroup.h"
#include "dbinterface/new-office/OfficeDelivery.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/new-office/OfficeGroup.h"
#include "dbinterface/new-office/OfficeGroupAccessGroup.h"

//创建一个路径
void CreateDir(const std::string& strDirectory);
//获取HTTP根目录
std::string GetHttpRootPath();
std::string GetRelayContactStr(const std::vector<RELAY_INFO>& relay_infos);
std::string GetSecurityRelayConfig(const std::vector<RELAY_INFO>& relay_infos);
std::string GetNewOfficePath(const std::string &uuid);
std::string GetUserDetailDownloadPath(const std::string mac, std::string &web_download_path);

void UpdateUserVersionByDevices(const std::string &dev_uuid);
void UpdateUserVersionByAgUUID(const std::string &ag_uuid);
void UpdateUserVersionByGroupUUID(const std::string &gruop);
void UpdateUserVersionByCompanyUUID(const std::string &company_uuid);
void GetContactDoorRelayInfo(const DevicesDoorInfoList& door_list, std::stringstream& relay_contact_info, std::stringstream& security_relay_contact_info);


#endif //__ADAPT_UTILITY_H__

