#include "Metric.h"
#include "CachePool.h"
#include "AkLogging.h"
#include "EtcdCliMng.h"
#include "MQProduce.h"
#include "ShadowMng.h"
#include "RouteClientMng.h"
#include "ConnectionPool.h"
#include "CsmainMsgHandle.h"
#include "ConfigFileReader.h"
#include "BeanstalkConsumerControl.h"
extern RouteMQProduce* g_nsq_producer;
extern CAkEtcdCliManager* g_etcd_cli_mng;
#define VERSION_CONF_FILE "/usr/local/akcs/csconfig-office/conf/version.conf"

void InitMetricInstance()
{
    MetricService* metric_service = MetricService::GetInstance();
    if (metric_service == nullptr)
    {
        AK_LOG_WARN << "metric service init failed.";
        return;
    }

    //版本信息
    CConfigFileReader tag_config_file(VERSION_CONF_FILE);
    std::string branch_or_tag_version = tag_config_file.GetConfigName("branch_or_tag");
    static long version_metric = (long)ATOI(branch_or_tag_version.c_str());

    // 添加 metric 指标
    metric_service->AddMetric(
        "db_get_conn_failed_count",
        "DB GetConnection failed count",
        "csconfig_office_db_get_conn_failed_count",
        nullptr
    );
    metric_service->AddMetric(
        "logdb_get_conn_failed_count",
        "LOGDB GetConnection failed count",
        "csconfig_office_logdb_get_conn_failed_count",
        nullptr
    );
    metric_service->AddMetric(
        "handle_csmain_queue_length",
        "The length of csconfig handle csmain msg queue",
        "csconfig_office_handle_csmain_queue_length",
        []() -> long { return (long)(CsmainMsgHandle::GetInstance()->GetMsgListSize()); }
    );

    metric_service->AddMetric(
        "nsq_check",
        "nsq producer status",
        "csconfig_office_nsq_check_error",
        []() -> long { return (long)(g_nsq_producer->Status() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "beanstalk_check",
        "beanstalk server status",
        "csconfig_office_beanstalk_check_error",
        []() -> long { return (long)(GetBeanstalkConsumerControlInstance()->CheckBeanstalkStatus() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "redis_check",
        "redis server status",
        "csconfig_office_redis_check_error",
        []() -> long { return (long)(CacheManager::getInstance()->CheckRedisNormal() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "db_conn_check",
        "db conn status",
        "csconfig_office_db_conn_check_error",
        []() -> long { return (long)(GetDBConnPollInstance()->CheckDBConnNormal() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "csroute_check",
        "route server status",
        "csconfig_office_csroute_check_error",
        []() -> long { return (long)(CRouteClientMng::Instance()->CheckRouteNormal() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "fdfs_check",
        "fdfs server status",
        "csconfig_office_fdfs_check_error",
        []() -> long { return (long)(AKCS::Singleton<CShadowMng>::instance().CheckFdfsNormal() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "etcd_check",
        "etcd server status",
        "csconfig_office_etcd_check_error",
        []() -> long { return (long)(g_etcd_cli_mng->CheckEtcdCliStatus() ? 0 : 1); }
    );
    metric_service->AddMetric(
        "version_metric",
        "version description",
        "version_metric{team=\"app_backend\"}",
        []() -> long { return version_metric; }
    );
}
