#include "OfficePduConfigMsg.h"
#include <assert.h>
#include <memory.h>
#include "AkLogging.h"
#include "AkcsCommonDef.h"
#include "CommonHandle.h"
#include "AkcsMsgDef.h"


OfficePduConfigMsg g_office_pdu_config_update_msg;



void OfficePduConfigMsg::ProduceMsg(const std::string &key, const google::protobuf::MessageLite *msg)
{    
    std::string str;
    if (!msg->SerializeToString(&str))
    {
        AK_LOG_WARN << "pb msg miss required fields.";
        return;
    }
    kafka_producer_->ProduceMsgWithLock(key, str);
}

void OfficePduConfigMsg::InitKafkaProducer(const std::string &ip, const std::string &topic)
{
    kafka_producer_ = std::make_shared<AkcsKafkaProducer>(topic, ip);
}


void ProduceConfigUpdateMsg(const std::string &key, const google::protobuf::MessageLite *msg)
{
    g_office_pdu_config_update_msg.ProduceMsg(key, msg);
}

