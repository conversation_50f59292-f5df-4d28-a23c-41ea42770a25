#ifndef __CSADAPT_UPDATECONFIG_NEWOFFICE_FILEUPDATE_H__
#define __CSADAPT_UPDATECONFIG_NEWOFFICE_FILEUPDATE_H__

#include <vector>
#include <string>
#include <map>
#include <list>
#include <memory>
#include "UpdateConfigDef.h"
#include "BasicDefine.h"
#include "AK.AdaptOffice.pb.h"
#include "kafka/AkcsKafkaProducer.h"
#include <mutex>
#include "AkcsWebMsgSt.h"
#include "encrypt/Md5.h"

class OfficeNewFileUpdateInfoBase
{
public:
    OfficeNewFileUpdateInfoBase(const std::string   &office_uuid, OfficeUpdateType change_type)
    :change_type_(change_type),office_uuid_(office_uuid)
    {
        
    }

    ~OfficeNewFileUpdateInfoBase(){};
    virtual google::protobuf::MessageLite *  GetInfo()   = 0; 
    virtual std::string GetUniqKey() = 0;
    std::string GetUUID() {return office_uuid_;} 
    int GetChangeType() {return (int)change_type_;} 
   
private:
    OfficeUpdateType change_type_;
    std::string office_uuid_;
};


class OfficeFileUpdateInfo:public OfficeNewFileUpdateInfoBase
{
public:
    OfficeFileUpdateInfo(const std::string   &office_uuid, OfficeUpdateType change_type)
    :OfficeNewFileUpdateInfoBase(office_uuid, change_type)
    {
        
    }

    ~OfficeFileUpdateInfo(){};

    int AddDevUUIDToList(const std::string   &dev_uuid) { dev_uuid_.insert(dev_uuid); return 0;};
    //只能在添加和删除设备时候使用
    int AddDevMac(const std::string   &dev_mac) { mac_ = dev_mac; return 0;};
    int SetTraceID(const std::string   &trace_id) { trace_id_ = trace_id; return 0;};
    //去掉 在当前没有用
    //int SetPersonalUUID(const std::string   &uuid) { personal_uuid_ = uuid; };
    
    google::protobuf::MessageLite * GetInfo() 
    {
       base_.set_office_uuid(GetUUID());
       base_.set_change_type(GetChangeType());
       base_.set_trace_id(trace_id_);
       AK::AdaptOffice::OfficeUpdateFileConfig info;
       for(auto &it : dev_uuid_)
       {
            info.add_dev_uuid_list(it);
       }
       info.set_mac(mac_);

       base_.set_msg_uuid(GetUniqKey());
       base_.mutable_file_update()->CopyFrom(info);

       return &base_;
    }  
    std::string GetUniqKey()
    {
       std::stringstream ss;
       ss << GetUUID() << " " << GetChangeType();
       
       for(auto &it : dev_uuid_)
       {
            ss << " " << it;
       }
       ss << " " << mac_;       
       return  akuvox_encrypt::MD5(ss.str()).toStr();;
    }       

private:
    AK::AdaptOffice::OfficeUpdateBaseMessage base_;
    std::set<std::string> dev_uuid_;
    std::string mac_;
    std::string trace_id_;
};


class OfficePduConfigMsg
{
public:
    OfficePduConfigMsg(){}
    ~OfficePduConfigMsg(){}

    void ProduceMsg(const std::string &key, const google::protobuf::MessageLite *msg);
    void InitKafkaProducer(const std::string &ip, const std::string &topic); 
private:
    std::shared_ptr<AkcsKafkaProducer> kafka_producer_;
};


void ProduceConfigUpdateMsg(const std::string &key, const google::protobuf::MessageLite *msg);


#endif //__CSADAPT_UPDATECONFIG_NEWOFFICE_FILEUPDATE_H__