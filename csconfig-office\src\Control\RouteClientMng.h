#ifndef __CSADAPT_ROUTE_CLIENT_MNG_H__
#define __CSADAPT_ROUTE_CLIENT_MNG_H__
#include <list>
#include <string>
#include <map>
#include <set>
#include <mutex>
#include <boost/noncopyable.hpp>
#include "RouteClient.h"

class CRouteClientMng : public boost::noncopyable
{
public:
    CRouteClientMng()
    {}
    ~CRouteClientMng()
    {}
    static CRouteClientMng* Instance();
    void AddRouteSrv(const std::string& route_addr, const RouteClientPtr& route_cli);
    void UpdateRouteSrv(const std::set<std::string>& route_addrs, evpp::EventLoop* etcd_loop, const std::string& logic_srv_id);
    bool CheckRouteNormal();

private:
    void RemoveDisconnectCli();
    std::mutex route_clis_mutex_;
    std::map<std::string/*csroute ip:port*/, RouteClientPtr> route_clis_;
    std::mutex route_clis_remove_mutex_;
    std::vector<RouteClientPtr> route_remove_clis_;
    static CRouteClientMng* instance_;
};

#endif //__CSADAPT_ROUTE_CLIENT_MNG_H__


