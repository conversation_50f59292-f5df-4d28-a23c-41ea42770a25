#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <assert.h>
#include <string.h>
#include <ctype.h>
#include <string>
#include <list>
#include <sstream>
#include "WriteFileControl.h"
#include <thread>
#include "util.h"
#include "AkLogging.h"
#include "util_cstring.h"
#include "AkcsWebMsgSt.h"
#include "AES256.h"
#include "AkcsMonitor.h"
#include "encrypt/Md5.h"
#include "AkcsCommonDef.h"
#include "ShadowMng.h"
#include "ConfigDef.h" 
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/Shadow.h"
#include "IPCControl.h"

extern CSCONFIG_CONF gstCSCONFIGConf;


std::unique_ptr<WriteFileControl> WriteFileControl::instance_ = nullptr;
std::once_flag WriteFileControl::init_instance_flag_;

WriteFileControl* GetWriteFileControlInstance()
{
    return WriteFileControl::GetInstance();
}

WriteFileControl* WriteFileControl::GetInstance()
{
    std::call_once(init_instance_flag_, []() 
    {
        instance_.reset(new WriteFileControl());
    });
    return instance_.get();
}

WriteFileControl::WriteFileControl()
{
#if 0

    write_thread_number_ = gstCSCONFIGConf.write_file_number;
    eque_.resize(write_thread_number_);
    for (int i = 0; i < write_thread_number_; ++i)
    {
        std::thread t = std::thread(WriteFileThread, i);
        t.detach();
    }
#endif    
}

WriteFileControl::~WriteFileControl()
{

}

int WriteFileControl::WriteFileThread(int id)
{
#if 0

    AK_LOG_INFO << "Create Write file thread:" << std::this_thread::get_id() << " id:" << id; 
    std::unique_ptr<CShadowMng> shadow_mng(new CShadowMng());
    while (true)
    {
        GetWriteFileControlInstance()->ThreadHandle(id, shadow_mng);
        sleep(5);
    }
#endif
}


/*单个mac, 通知mac*/
/*

static void NotifyMacChange(const std::string& mac)
{
    CSP2A_CONFIG_FILE_CHANGE file_change;
    memset(&file_change, 0, sizeof(file_change));
    file_change.nNotifyType = CONFIG_FILE_CHANGE_NOTIFY_TYPE_MAC;
    file_change.type |= CONFIG_FILE_CHANGE_TYPE_CONTACT;
    file_change.type |= CONFIG_FILE_CHANGE_TYPE_CONFIG;
    snprintf(file_change.mac, sizeof(file_change.mac), "%s", mac.c_str());
    if (GetIPCControlInstance()->SendConfigFileChange(&file_change) != 0)
    {
        AK_LOG_WARN << "Send contact change failed";
    }
}
*/

void WriteFileControl::ThreadHandle(int id, std::unique_ptr<CShadowMng>& shadow_mng)
{
#if 0

    std::deque<DevFileInfoPtr> tmp_deque;
    {
        std::lock_guard<std::mutex> lock_(mutex_);
        if (eque_[id].size() == 0)
        {
           return;
        }        
        eque_[id].swap(tmp_deque);
    }

    if (tmp_deque.size() > 500)
    {
        AK_LOG_INFO << "Write file queue size:" << tmp_deque.size();    
        std::stringstream err;
        err << "Write file queue size: " << tmp_deque.size() <<  ", Temporary use of alarm ID: AKCS_MONITOR_ALARM_CONFIG_WRITE_FAILED";
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csconfig", err.str(), AKCS_MONITOR_ALARM_CONFIG_WRITE_FAILED);
    }
    
    std::set<std::string> notify_mac_list;
    DevFileInfoPtr fileinfo;
    while (tmp_deque.size() > 0)
    {
        fileinfo = tmp_deque.front();
        tmp_deque.pop_front();

        //取值
        std::string filepath = fileinfo->filepath_;
        std::string content= fileinfo->content_;
        std::string mac= fileinfo->mac_;
        SHADOW_TYPE file_type = fileinfo->file_type_;
        int project_type = fileinfo->project_type_;
        uint32_t id = fileinfo->table_id_;
        ThreadLocalSingleton::GetInstance().SetTraceID(STOULL(fileinfo->trace_id_));
        //文件操作
        FILE* pfile = fopen(filepath.c_str(), "w+");
        if (pfile == NULL)
        {
            AK_LOG_WARN << "fopen  failed " << filepath;
            AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csconfig-office", filepath, AKCS_MONITOR_ALARM_CONFIG_WRITE_FAILED);
            continue;
        }
        
        fwrite(content.c_str(), sizeof(char), strlen(content.c_str()), pfile);
        fclose(pfile);
 
        //加密
        if (!gstCSCONFIGConf.no_encrypt)
        {
            FileAESEncrypt(filepath.c_str(), AES_ENCRYPT_KEY_V1, filepath.c_str());
        }

        shadow_mng->StoreDevShadow(filepath.c_str(), mac, file_type);
        std::string md5 = akuvox_encrypt::MD5::GetFileMD5(filepath);    
        
        AK_LOG_INFO << "The file path is " << filepath << " md5:" << md5;


        dbinterface::OfficeDevices::UpdateMd5ByID(id, file_type, md5);
        notify_mac_list.insert(mac);
      
    }

    //TODO：需不需要通知可以判断md5前后是否一致
    for (auto &mac : notify_mac_list)
    {
        GetIPCControlInstance()->NotifyMacChange(mac);
        AK_LOG_INFO << "Notify mac: " << mac;
    } 

#endif
}

#if 0
void WriteFileControl::AddFileInfo(const std::string &mac, const DevFileInfoPtr &info)
{
    if(ThreadLocalSingleton::GetInstance().GetDbStatus())
    {
        AK_LOG_INFO << "AddFileInfo mac:" << mac << " file: " << info->filepath_; 
        int id = StrHash(mac, write_thread_number_);
        std::lock_guard<std::mutex> lock_(mutex_);
        eque_[id].push_back(info);
    }
    else
    {
        AK_LOG_ERROR << "Alarm Monitoring: Db error. Pause AddFileInfo mac:" << mac << " file: " << info->filepath_; 
    }
}
#else

void WriteFileControl::AddFileInfo(const std::string &mac2, const DevFileInfoPtr &fileinfo)
{
    if(!ThreadLocalSingleton::GetInstance().GetDbStatus())
    {
        return;
    }

    auto& pool = ShadowMngPool::instance();
    auto shadow_mng = pool.acquire();
    
    //取值
    std::string filepath = fileinfo->filepath_;
    std::string content= fileinfo->content_;
    std::string mac= fileinfo->mac_;
    SHADOW_TYPE file_type = fileinfo->file_type_;
    int project_type = fileinfo->project_type_;
    uint32_t id = fileinfo->table_id_;
    ThreadLocalSingleton::GetInstance().SetTraceID(STOULL(fileinfo->trace_id_));
    //文件操作
    FILE* pfile = fopen(filepath.c_str(), "w+");
    if (pfile == NULL)
    {
        AK_LOG_ERROR << "fopen  failed " << filepath;
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csconfig-office", filepath, AKCS_MONITOR_ALARM_CONFIG_WRITE_FAILED);
        return;
    }
    
    fwrite(content.c_str(), sizeof(char), strlen(content.c_str()), pfile);
    fclose(pfile);
    
    //加密
    if (!gstCSCONFIGConf.no_encrypt)
    {
        FileAESEncrypt(filepath.c_str(), AES_ENCRYPT_KEY_V1, filepath.c_str());
    }
    
    shadow_mng->StoreDevShadow(filepath.c_str(), mac, file_type);
    std::string md5 = akuvox_encrypt::MD5::GetFileMD5(filepath);    
    
    AK_LOG_INFO << "The file path is " << filepath << " md5:" << md5;
    
    dbinterface::OfficeDevices::UpdateMd5ByID(id, file_type, md5);
    GetIPCControlInstance()->NotifyMacChange(mac);
    AK_LOG_INFO << "Notify mac: " << mac;

    return;
}


#endif



