#ifndef __WIRETE_FILE_CONTROL_H__
#define __WIRETE_FILE_CONTROL_H__

#include "BasicDefine.h"
#include "AKCSMsg.h"
#include <mutex>
#include <vector>
#include <deque>
#include "ShadowMng.h"
#include "ThreadLocalSingleton.h"

class DevFileInfo
{
public:
    DevFileInfo(const std::string &mac, const std::string &filepath, const std::string &content,
        SHADOW_TYPE file_type, int project_type, int id)
        :mac_(mac),content_(content),filepath_(filepath),
        file_type_(file_type),project_type_(project_type),
        table_id_(id)
    {
       trace_id_ = std::to_string(ThreadLocalSingleton::GetInstance().GetTraceID()); 
    }   
    ~DevFileInfo(){};
public:
    std::string mac_;
    std::string filepath_;
    std::string content_;
    SHADOW_TYPE file_type_;
    int project_type_;
    uint32_t table_id_;    
    std::string trace_id_;
};

typedef std::shared_ptr<DevFileInfo> DevFileInfoPtr;


class WriteFileControl
{
public:
    WriteFileControl();
    ~WriteFileControl();
    static WriteFileControl* GetInstance();
    
    void AddFileInfo(const std::string &mac, const DevFileInfoPtr &info);
    
    static void WriteFileThread(int id);
private:
    void ThreadHandle(int, std::unique_ptr<CShadowMng>& shadow_mng);
    static std::unique_ptr<WriteFileControl> instance_;
    std::mutex mutex_;
    std::vector<std::deque<DevFileInfoPtr>> eque_;
    int write_thread_number_ = 2;
    static std::once_flag init_instance_flag_; 

};

WriteFileControl* GetWriteFileControlInstance();


#endif

