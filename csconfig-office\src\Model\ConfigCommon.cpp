#include <sstream>
#include "ConfigCommon.h"
#include <string.h>
#include "util_string.h"
#include "util.h"
#include "AkcsCommonDef.h"
#include "ConfigDef.h" 
#include "AkcsWebMsgSt.h"
#include "dbinterface/VersionModel.h"

extern CSCONFIG_CONF gstCSCONFIGConf;


void UpdateUcloudVideoBitRate(const std::string &firmware, std::stringstream &config)
{
    if(gstCSCONFIGConf.server_type != ServerArea::ucloud)
    {
        return;
    }

    int model = 0;
    int big_ver = 0;
    int small_ver = 0;
    if (false == GetFirmwareInfo(firmware, model, big_ver, small_ver))
    {
        return;
    }
    
    //如果版本>10 或者版本=10且小版本大于指定值 代表需要更新
    if (
            (model == SOFTWARE_R20V3 && ((big_ver == 10 && small_ver >= 9) || big_ver > 10))
            || (model == SOFTWARE_X910)
       // || (model == SOFTWARE_X915 && ((big_ver == 10  && small_ver >= 14) || big_ver > 10))
       // || (model == SOFTWARE_X915_V2 && ((big_ver == 10 && small_ver >= 9) || big_ver > 10))
       // || (model == SOFTWARE_E12 && ((big_ver == 10 && small_ver >= 9) || big_ver > 10))
       )
    {
        config << "Config.DoorSetting.RTSP.H264Resolution2 = 5\n";
        config << "Config.DoorSetting.RTSP.H264BitRate2 = 1024\n";
        config << "Config.Account1.Video00.ProfileLevel = 4\n";
        config << "Config.Account1.Video00.MaxBR = 1024\n";
    }
    return;
}


void UpdateSipSrtpConfig(int sip_type, uint64_t fun_bit, std::stringstream &config)
{
    if (DevMngSipType_TLS == sip_type && SwitchHandle(fun_bit, FUNC_DEV_SUPPORT_SRTP))
    {
        config << "Config.Account1.ENCRYPTION.SRTPEncryption = 2\n";
    }
    else
    {
        config << "Config.Account1.ENCRYPTION.SRTPEncryption = 0\n";
    }

    return;
}

void UpdateAuxCameraConfig(uint64_t fun_bit, std::stringstream &config)
{
    if (SwitchHandle(fun_bit, FUNC_DEV_SUPPORT_MULTI_MONITOR))
    {
        config << CONFIG_RTSP_AUX_CAMERA_H264_RESOLUTION << "5\n";
        config << CONFIG_RTSP_AUX_CAMERA_H264_FRAMERATE  << "30\n";
        config << CONFIG_RTSP_AUX_CAMERA_H264_BITRATE    << "1024\n";
    }

    return;
}

void UpdateHighResolutionVideoResolution(short dev_firmware, std::stringstream &config)
{
    FirmwareList high_resolution_list;
    dbinterface::VersionModel::GetHighResolutionList(high_resolution_list);
    if (high_resolution_list.count(dev_firmware))
    {
        // 云上二路流：2K
        config << "Config.DoorSetting.RTSP.H264Resolution = 10\n";

        // 默认流：VGA
        config << "Config.DoorSetting.RTSP.H264Resolution2 = 3\n";
    }
}