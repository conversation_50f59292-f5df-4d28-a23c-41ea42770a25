#ifndef CONFIG_COMMON_H_
#define CONFIG_COMMON_H_
#include <sstream>
#include <string>
#include <vector>
#include "AkcsCommonSt.h"

void UpdateUcloudVideoBitRate(const std::string &firmware, std::stringstream &config);
void UpdateSipSrtpConfig(int sip_type, uint64_t fun_bit, std::stringstream &config);
void UpdateAuxCameraConfig(uint64_t fun_bit, std::stringstream &config);
void UpdateHighResolutionVideoResolution(short dev_firmware, std::stringstream &config);

#endif
