#ifndef CONTACT_COMMON_H_
#define CONTACT_COMMON_H_
#include <sstream>
#include "ContactCommon.h"
#include <string>
#include <vector>
#include "AkcsCommonSt.h"
#include "dbinterface/new-office/DevicesDoorList.h"

enum CONTACT_ATTR
{
    TYPE,
    NODE,
    UID,
    NAME,
    SIP,
    IP,
    MAC,
    RTSPPWD,
    PUB,
    RELAY,
    SEC_RELAY,
    SEQ,//key value一起
    SEQ2,//key value一起
    SEQ_KEY,//只有key 没有value
    SIP0,
    LAND,
    GROUP_CALL,
    MASTER,
    MATCH_DTMF1,
    MATCH_DTMF2,
    MATCH_DTMF3,
    MATCH_DTMF4,
    APP,
    UNIT,
    ROOM,
    ROOM_N,
    UNIT_APT,
    IP_DIRECT,
    CALL_LOOP,
    UUID,
    URL,
    BONDMAC,
    RTSPUSER,
    MONITOR_TYPE,
    NOT_MONITOR,
    REPOST,
    IS_DISPLAY,
    COMPAN<PERSON>,
    IS_Group,
    IS_UNIT_TILED_DISPLAY,
    CAMERA_NUM,
};

typedef std::vector<std::pair<int, std::string> > ContactKvList;
using MultipleContactKvList = std::vector<ContactKvList>;

void GetContactCommonStr(std::stringstream &str, const ContactKvList &kv);
void GetContactStr(std::stringstream &str, const ContactKvList &kv);
void GetGroupStr(std::stringstream &str, const ContactKvList &kv);
void GroupEndStr(std::stringstream &str);
void GetContactEndStr(std::stringstream &str);
void GetContactHeadStr(std::stringstream &str);
void GetDepartmentStr(std::stringstream &str, const ContactKvList &kv);
void DepartmentEndStr(std::stringstream &str);
void WriteDoorRelayContact(const uint32_t dev_type, const DevicesDoorInfoList& door_list, ContactKvList &kv);

#endif
