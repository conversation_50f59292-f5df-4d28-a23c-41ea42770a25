#include "DataAnalysis.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisTableParse.h"
#include "AkLogging.h"
#include "AkcsCommonDef.h"


extern DataAnalysisDBHandlerMap g_db_handler_map;

using namespace Akcs;

static const std::set<std::string> g_skip_table = {
    "NameMapping",
    "PersonalAccountDataGroup",
    "RBACDataGroup",
    "SipGroup2",
    "RBACRoleMap"
};

bool IsInSkipTable(const std::string& str) {
    return g_skip_table.find(str) != g_skip_table.end();
}

DataAnalysis::DataAnalysis(const std::string &data)
{
    original_data = data;
    is_special_ = 0;
    Init();
}

DataAnalysis::~DataAnalysis()
{

}
/*
{
        "msg_type": "data_analysis",
        "trace_id": "xxxx",
        "timestamp": 111111,
        "data": {
            "update": {
                "CommPerPrivateKey": [
                    [{
                        "ID": 3,
                        "Account": "**********"
                    }, {
                        "ID": 3,
                        "Account": "**********"
                    }],
                    [{
                        "ID": 4,
                        "Account": "**********"
                    }, {
                        "ID": 4,
                        "Account": "**********"
                    }]
                ]
            },
            "insert": {
                "CommPerPrivateKey": [
                    [{
                        "ID": 3,
                        "Account": "**********"
                    }, {
                        "ID": 3,
                        "Account": "**********"
                    }],
                    [{
                        "ID": 4,
                        "Account": "**********"
                    }, {
                        "ID": 4,
                        "Account": "**********"
                    }]
                ]
            }
        }
    }
}

*/

int DataAnalysis::Init()
{
    Json::Reader reader;
    Json::Value root;
    Json::Value insert;
    Json::Value del;
    Json::Value update;
    Json::Value special;
    
    DataAnalysisTableInfoMap data_info;
    if (!reader.parse(original_data, root))
    {
        AK_LOG_WARN <<  "Parse json error.data=" << original_data << " error msg=" << reader.getFormatedErrorMessages();
        return -1;
    }
    if (!root.isMember("data"))
    {
        AK_LOG_WARN <<  "Parse json error. no have section type. data=" << original_data;
        return -1;
    }
    
    if (!root.isMember("trace_id"))
    {
        AK_LOG_WARN <<  "Parse json error. no have section type. data=" << original_data;
        return -1;
    }
    trace_id_ = root["trace_id"].asString();
    
    AK_LOG_INFO <<  "DEBUG>>>>DataAnalysis data=" << original_data;
    Json::Value data = root["data"];
   
    insert = data[da_insert];
    del = data[da_delete];
    update = data[da_update];


    InsertData(insert);
    DeleteData(del);
    UpdateData(update);

    return 0;
}


void DataAnalysis::InsertData(Json::Value &insert)
{
    if (insert.empty())
    {
        return;
    }
    
    Json::Value::Members array_member = insert.getMemberNames(); 
    for(Json::Value::Members::iterator iter = array_member.begin(); iter != array_member.end(); ++iter)
    {
        std::string sheet= *iter;
        if(IsInSkipTable(sheet))
        {
            continue;
        }
        Json::Value sql_arr = insert[sheet];
        DataAnalysisRowList kvlist;
        for (unsigned int i = 0; i < sql_arr.size(); i++)
        {
            DataAnalysisSqlKV kv;
            for (auto const& key : sql_arr[i].getMemberNames()) {
                kv.insert(std::map<std::string, std::string>::value_type(key, sql_arr[i][key].asString()));
            }
            kvlist.push_back(kv);
        }
        AK_LOG_INFO <<  "DataAnalysis insert " << sheet << " data.";
        db_insert_info_.insert(std::map<std::string, DataAnalysisRowList>::value_type(sheet, kvlist));
    }

}

void DataAnalysis::DeleteData(Json::Value &del)
{
    if (del.empty())
    {
        return;
    }

    Json::Value::Members array_member = del.getMemberNames(); 
    for(Json::Value::Members::iterator iter = array_member.begin(); iter != array_member.end(); ++iter)
    {
        std::string sheet= *iter;
        if(IsInSkipTable(sheet))
        {
            continue;
        }        
        Json::Value sql_arr = del[sheet];
        DataAnalysisRowList kvlist;
        for (unsigned int i = 0; i < sql_arr.size(); i++)
        {
            DataAnalysisSqlKV kv;
            for (auto const& key : sql_arr[i].getMemberNames()) {
                kv.insert(std::map<std::string, std::string>::value_type(key, sql_arr[i][key].asString()));
            }
            kvlist.push_back(kv);
        }
        AK_LOG_INFO <<  "DataAnalysis delete " << sheet << " data.";
        db_delete_info_.insert(std::map<std::string, DataAnalysisRowList>::value_type(sheet, kvlist));
    }  

}


void DataAnalysis::UpdateData(Json::Value &update)
{
    if (update.empty())
    {
        return;
    }
    
    Json::Value::Members array_member = update.getMemberNames(); 
    for(Json::Value::Members::iterator iter = array_member.begin(); iter != array_member.end(); ++iter)
    {
        std::string sheet= *iter;
        if(IsInSkipTable(sheet))
        {
            continue;
        }
        Json::Value sql_arr = update[sheet];
        DataAnalysisRowList kvlist;
        for (unsigned int i = 0; i < sql_arr.size(); i++)
        {
            Json::Value data = sql_arr[i];
            if (data.size() != 2)
            {
                AK_LOG_WARN << "Data analysis update, data size error!";
                continue;
            }
            
            int before = 0;//直接放入常量会报错
            int after = 1;
            DataAnalysisSqlKV kv1;
            DataAnalysisSqlKV kv2;
            for (auto const& key : data[before].getMemberNames())
            {
                if (!(data[before][key].isArray() ||
                    data[before][key].isObject()))
                {
                    kv1.insert(std::map<std::string, std::string>::value_type(key, data[before][key].asString()));
                }
            }
            kvlist.push_back(kv1);
            
            for (auto const& key : data[after].getMemberNames())
            {
                if (!(data[before][key].isArray() ||
                    data[before][key].isObject()))
                {
                    kv2.insert(std::map<std::string, std::string>::value_type(key, data[after][key].asString()));
                }
            }
            kvlist.push_back(kv2);
            
        }
        AK_LOG_INFO <<  "DataAnalysis update " << sheet << " data.";
        db_update_info_.insert(std::map<std::string, DataAnalysisRowList>::value_type(sheet, kvlist));
    }    
}


int DataAnalysis::Analysis()
{    
    context_.SetTraceID(trace_id_);
    DataAnalysisTableInfoMap::iterator tab_it;
    for (tab_it = db_delete_info_.begin(); tab_it != db_delete_info_.end(); ++tab_it)
    {
        DataAnalysisDBHandlerMap::iterator handler_it;
        handler_it = g_db_handler_map.find(tab_it->first);
        if (handler_it == g_db_handler_map.end())
        {
            AK_LOG_WARN << "Mysql delete operation. table:" << tab_it->first << " is no need do anything.";
            continue;
        }
        DataAnalysisDBHandlerPtr ptr = handler_it->second;
        
        for (const auto &data : tab_it->second)
        {
            const DataAnalysisColumnList detect_key = ptr->get_detect_func_();
            DataAnalysisTableParse parse(detect_key, data);
            parse.SetOperation(DataAnalysisTableParse::DBHandleType::DA_OPERATION_DELETE);
            parse.SetProjectType(project_type_);
            DataAnalysisTableHandler(parse, ptr, context_);
        }

    }

    for (tab_it = db_insert_info_.begin(); tab_it != db_insert_info_.end(); ++tab_it)
    {
        DataAnalysisDBHandlerMap::iterator handler_it;
        handler_it = g_db_handler_map.find(tab_it->first);
        if (handler_it == g_db_handler_map.end())
        {
            AK_LOG_WARN << "Mysql insert operation. table:" << tab_it->first << " is no need do anything.";
            continue;
        }
        DataAnalysisDBHandlerPtr ptr = handler_it->second;
        for (const auto &data : tab_it->second)
        {
            const DataAnalysisColumnList detect_key = ptr->get_detect_func_();
            DataAnalysisTableParse parse(detect_key, data);
            parse.SetOperation(DataAnalysisTableParse::DBHandleType::DA_OPERATION_INSERT);
            parse.SetProjectType(project_type_);
            DataAnalysisTableHandler(parse, ptr, context_);
        }
    }
    
    for (tab_it = db_update_info_.begin(); tab_it != db_update_info_.end(); ++tab_it)
    {
        DataAnalysisDBHandlerMap::iterator handler_it;
        handler_it = g_db_handler_map.find(tab_it->first);
        if (handler_it == g_db_handler_map.end())
        {
            AK_LOG_WARN << "Mysql update operation. table:" << tab_it->first << " is no need do anything.";
            continue;
        }
        
        DataAnalysisDBHandlerPtr ptr = handler_it->second;
        DataAnalysisRowList data = tab_it->second;
        int i = 0;
        for (i = 0; i < data.size(); i++,i++)
        {
            const DataAnalysisColumnList detect_key = ptr->get_detect_func_();
            DataAnalysisTableParse parse(detect_key, data[i], data[i + 1]);
            parse.SetOperation(DataAnalysisTableParse::DBHandleType::DA_OPERATION_UPDATE);
            parse.SetProjectType(project_type_);
            DataAnalysisTableHandler(parse, ptr, context_);      
        }
    }  

    context_.DispatchUpdateConfigInfo();  

    return 0;  
}


