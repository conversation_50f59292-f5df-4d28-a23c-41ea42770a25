#include "DataAnalysisContorl.h"
#include "DataAnalysis.h"
#include "DataAnalysisContext.h"
#include "json/json.h"
#include "OfficePduConfigMsg.h"



DataAnalysisContext::DataAnalysisContext()
{

}

DataAnalysisContext::~DataAnalysisContext()
{


}

void DataAnalysisContext::AddUpdateConfigInfo(OfficeFileUpdateInfo &info)
{
    //通过map的key 对同一次的数据分析进行写配置的去重
    info.SetTraceID(trace_id_);
    std::string msg_key = info.GetUniqKey();
    update_office_config_list_.insert(std::map<std::string, OfficeFileUpdateInfo>::value_type(msg_key, info));        
}

void DataAnalysisContext::DispatchUpdateConfigInfo()
{
    for (auto &config : update_office_config_list_)
    {
        ProduceConfigUpdateMsg(config.second.GetUUID(), config.second.GetInfo());                
    }
    
}





