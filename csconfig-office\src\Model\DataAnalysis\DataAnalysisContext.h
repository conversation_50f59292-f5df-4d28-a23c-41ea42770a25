#ifndef __CSADAPT_DATA_ANALYSIS_CONTEXT_H__
#define __CSADAPT_DATA_ANALYSIS_CONTEXT_H__

#include <vector>
#include <string>
#include <map>
#include <list>
#include <memory>
#include <memory>
#include "DataAnalysisDef.h"
#include "DataAnalysisUpdateConfig.h"
#include "InnerUtil.h"
#include "OfficePduConfigMsg.h"

using OfficeFileUpdateInfoMap = std::map<std::string/*uniq key*/, OfficeFileUpdateInfo>;

class DataAnalysisContext
{
public:
    DataAnalysisContext();
    ~DataAnalysisContext();
    void AddUpdateConfigInfo(OfficeFileUpdateInfo &info);
    void DispatchUpdateConfigInfo();
    void SetTraceID(const std::string &trace_id){ trace_id_ = trace_id;}
    
private:
    OfficeFileUpdateInfoMap update_office_config_list_;
    std::string trace_id_;

};



#endif //__CSADAPT_DATA_ANALYSIS_CONTEXT_H__
