#ifndef __CSADAPT_DATA_ANALYSIS_CONTORL_H__
#define __CSADAPT_DATA_ANALYSIS_CONTORL_H__

#include <vector>
#include <string>
#include <map>
#include <list>
#include <memory>
#include <memory>
#include "DataAnalysisDef.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisContext.h"

void RegDataAnalysisDBHandler(const std::string &name, DataAnalysisDBHandlerPtr &handler);
void RegDaSort(DataAnalysisColumnList &detect_key, DataAnalysisChangeHandle change_handle[], int len);
void DataAnalysisTableHandler(DataAnalysisTableParse &da, DataAnalysisDBHandlerPtr &handler, DataAnalysisContext &context);
int DataAnalysisChangeRoleToProjectType(int role);
void RegDataAanlysisDBAllHandler();


#endif //__CSADAPT_DATA_ANALYSIS_CONTORL_H__
