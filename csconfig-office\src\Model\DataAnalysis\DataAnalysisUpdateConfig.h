#ifndef __CSADAPT_DATA_ANALYSIS_UPDATECONFIG_H__
#define __CSADAPT_DATA_ANALYSIS_UPDATECONFIG_H__

#include <vector>
#include <string>
#include <map>
#include <list>
#include <memory>
#include<algorithm>
#include "DataAnalysisDef.h"
//#include "AKCSMsg.h"

typedef int (*UpdateConfigHandlerFunc) (std::shared_ptr<void>);
typedef std::string (*UpdateConfigHandlerIdentify)(std::shared_ptr<void>);

class UpdateConfigTool
{
public:
    UpdateConfigTool(UpdateConfigHandlerFunc handler_func, UpdateConfigHandlerIdentify identify_func){
        hander_func_ = handler_func;
        identify_func_ = identify_func;
    }
    UpdateConfigHandlerFunc hander_func_;
    UpdateConfigHandlerIdentify identify_func_;
};

typedef std::shared_ptr<UpdateConfigTool> UpdateConfigToolPtr;
typedef std::map<int, UpdateConfigToolPtr> UCHandlerToolMap;
void RegUpdateConfigTool(int type,  UpdateConfigHandlerFunc hander,  UpdateConfigHandlerIdentify identify);


typedef std::shared_ptr<void> UpdateConfigDataPtr;
typedef std::vector<UpdateConfigDataPtr> UpdateConfigDataList;
typedef std::map<int, UpdateConfigDataList> UpdateConfigInfoMap;


void UpdateConfigDispatch(UpdateConfigInfoMap &datas);
void UpdateConfigInit();


#endif //__CSADAPT_DATA_ANALYSIS_UPDATECONFIG_H__