#ifndef __DATA_ANALYSIS_DB_HANDLE_H__
#define __DATA_ANALYSIS_DB_HANDLE_H__
#include <string>
#include <memory>
#include <vector>
#include "AkcsCommonDef.h"

typedef struct UserInfo_T
{
    int role;
    int mng_id;
    int unit_id;
    char mac[20];
    char node[32];
    char account[32];
}UserInfo;

namespace dbhandle
{

class DAInfo
{
public:
    DAInfo();
    ~DAInfo();
    static int GetUserInfoByUid(const std::string& struser, UserInfo &user_info);
    static int GetUserInfoByUUID(const std::string& uuid, UserInfo &user_info);
    static int GetUserInfoByRoomID(int room_id, UserInfo &user_info);
private:
};



}
#endif
