#include <stdio.h>
#include <stdlib.h>
#include <fstream>
#include <cstdlib>
#include <evpp/libevent.h>
#include <evpp/timestamp.h>
#include <evpp/event_loop_thread.h>
#include <evpp/httpc/request.h>
#include <evpp/httpc/conn.h>
#include <evpp/httpc/response.h>
#include "evpp/http/service.h"
#include "evpp/http/context.h"
#include "evpp/http/http_server.h"
#include "HttpServer.h"
#include "util.h"
#include "util_string.h"
#include "http/HttpMsgControl.h"
#include "Md5.h"
#include "AES256.h"
#include "ConfigDef.h"
#include "AkcsPasswdConfuse.h"
#include "dbinterface/Shadow.h"
#include "dbinterface/Account.h"
#include "json/json.h"
#include "CsmainMsgHandle.h"
#include "AkLogging.h"
#include "EtcdCliMng.h"
#include "MQProduce.h"
#include "kafka/AkcsKafkaConsumer.h"
#include "redis/CachePool.h"
#include "RouteClientMng.h"
#include "ShadowMng.h"
#include "BeanstalkConsumerControl.h"
#include "ConnectionPool.h"
#include "OfficePduConfigMsg.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "dbinterface/office/OfficeDevices.h"
#include "OfficeNew/ConfigFile/OfficeNewConfigHandle.h"
#include "Model/ShadowUserDetailMng.h"
#include "MetricService.h"

//全局变量
extern CSCONFIG_CONF gstCSCONFIGConf;
static const char ak_download_key[] = "ak_download";
extern RouteMQProduce* g_nsq_producer;
extern CAkEtcdCliManager* g_etcd_cli_mng ;


//添加getDevicesShadow所需函数
static std::string GetNginxAntiHotlink(const std::string& key, const std::string& uri)
{
    time_t timestamp = time(nullptr) + 3600;
    std::string form_token = key + ":" + uri + ":" + std::to_string(timestamp);
    char* token_pointer = akuvox_encrypt::MD5(form_token).GetBase64Md5();

    if (!token_pointer)
    {
        return "";
    }

    std::string token = token_pointer;
    free(token_pointer);
    
    StringReplace(token, "+", "-");
    StringReplace(token, "/", "_");  //nginx防盗链要求token中+/转换为-_

    std::string ret_url = "?token=" + token + "&e=" + std::to_string(timestamp);
    return ret_url;
}


//当请求无效时的处理函数
void DefaultHandler(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    AK_LOG_WARN << "http req route is not define";
    cb("http req route is not define");
}

void HttpReqGetMngFilterCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";

    HttpRespKV kv;
    kv.insert(std::map<std::string, std::string>::value_type("project_uuid_filter", gstCSCONFIGConf.project_uuid_filter));  
    cb(buildCommHttpMsg(HTTP_CODE_SUC, kv));
    return;
}

void HttpReqSetMngFilterCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";

    std::string filter = ctx->GetQuery("filter_uuid");
    snprintf(gstCSCONFIGConf.project_uuid_filter, sizeof(gstCSCONFIGConf.project_uuid_filter), "%s", filter.c_str());

    HttpRespKV kv;
    cb(buildCommHttpMsg(HTTP_CODE_SUC, kv));
    return;
}


void HttpRefreshProjectDataCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{

    int delay_interval = 1;
    std::string project_uuid = ctx->GetQuery("project_uuid");
    
    AK_LOG_INFO << "HttpImportCommunityCallback project_uuid = "<< project_uuid;

    dbinterface::AccountInfo account;
    if (0 != dbinterface::Account::GetAccountByUUID(project_uuid, account))
    {
        AK_LOG_INFO << "HttpRefreshProjectCallback GetAccountByUUID failed";
        cb("HttpRefreshProjectCallback GetAccountByUUID failed");
        return;
    }

    //更新数据版本
    dbinterface::OfficePersonalAccount::UpdateVersionByProjectUUID(project_uuid);  
    OfficeFileUpdateInfo update_info(project_uuid, OfficeUpdateType::OFFICE_IMPORT_PROJECT);     
    ProduceConfigUpdateMsg(update_info.GetUUID(), update_info.GetInfo());

    std::stringstream resp;
    resp << "RefreshProject project_uuid = " << project_uuid << " success\n";
    cb(resp.str());
    
    return;
}

//add by czw ********: 构建csconfig exporter指标
void HttpReqMetricsCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    MetricService* metric_service = MetricService::GetInstance();
    if (metric_service == nullptr)
    {
        AK_LOG_WARN << "metric service init failed.";
        cb("# metric service init failed.");
        return;
    }

    metric_service->UpdateMetrics();
    std::string response = metric_service->ToFormateString();
    cb(response);
    return;
}

void HttpReqGetDevicesShadowCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    std::string mac = ctx->GetQuery("mac");
    std::string type = ctx->GetQuery("type");
    std::string token = ctx->GetQuery("token");

    AK_LOG_INFO << "maintance request device shadow, mac:" << mac << ",type:" << type << ",token:" << token;

    // 计算token 
    std::string token_string = mac + ":" + type + ":" + "Akuvox2023";
    std::string token_string_md5 = akuvox_encrypt::MD5(token_string).toStr();
    if (strcmp(token.c_str(), token_string_md5.c_str()) != 0)
    {
        AK_LOG_WARN << "token error, post_token = " << token << ", cal_token_string = " << token_string << ", cal_token_string_md5 = " << token_string_md5;
        cb(buildErrorHttpMsg(-1, "token is error, mac = " +  mac));
        return;
    }
    
    std::stringstream content;
    std::string remote_filepath;
    
    int filetype = ATOI(type.c_str());
    
    // 获取文件链接
    DevShadow shadow = {0};
    if(dbinterface::Shadow::GetAllShadowByMac(mac, shadow) != 0)
    {
        AK_LOG_WARN << "device shadow is not exist, mac = " << mac;
        cb(buildErrorHttpMsg(-1, "get device shadow failed, mac = " +  mac));
        return;
    }

    switch(filetype)
    {
        case SHADOW_TYPE::SHADOW_CONFIG:
        {
            remote_filepath = shadow.config_storage_path;
            break;
        }
        case SHADOW_TYPE::SHADOW_PRIKEY:
        {
            remote_filepath = shadow.prikey_storage_path;
            break;
        }
        case SHADOW_TYPE::SHADOW_RFID:
        {
            remote_filepath = shadow.rfkey_storage_path;
            break;
        }
        case SHADOW_TYPE::SHADOW_CONTACT:
        {
            remote_filepath = shadow.contac_storage_path;
            break;
        }
        case SHADOW_TYPE::SHADOW_FACECONF:
        {
            remote_filepath = shadow.face_storage_path;
            break;
        }
        case SHADOW_TYPE::SHADOW_SCHE:
        {
            remote_filepath = shadow.schedule_storage_path;
            break;
        }
        case SHADOW_TYPE::SHADOW_USERMETA:
        {
            remote_filepath = shadow.usermeta_storage_path;
            break;
        }
        case SHADOW_TYPE::SHADOW_USERALL:
        {
            // 获取设备的userdetail信息
            OfficeDevPtr dev;
            if (dbinterface::OfficeDevices::GetMacDev(mac, dev) != 0)
            {
                AK_LOG_WARN << "get device info failed, mac = " << mac;
                cb(buildErrorHttpMsg(-1, "get device info failed, mac = " +  mac));
                return;
            }
            
            // 创建空的PerIDSet，获取所有用户信息
            OfficePerIDSet account_list;
            OfficeUserDetailReq req;
            req.trarceid = 10000; // 我们的traceid算法不会出现这个值
            
            NewOfficeConfigHandle handle(dev->project_uuid);
            account_list = handle.GetOfficeDevPermissionAccountList(mac, dev->uuid);
            handle.CreateUserInfo(dev, account_list, req);
            
            if(req.file_md5.size() > 0)
            {
                auto& pool = ConfigUserDetailFdfsUploaderPool::GetInstance();
                {
                    ConfigUserDetailFdfsUploaderPool::UploaderHandle handle(pool);
                    std::string path_after;
                    if (handle.UploadFile(req.write_file_path, path_after) == 0)
                    {
                        remote_filepath = path_after;
                        AK_LOG_INFO << "Mac:" << mac << " shadow_userall, upload file success, path = " << remote_filepath;
                    }
                    else
                    {
                        AK_LOG_WARN << "Mac:" << mac << " shadow_userall, upload file failed";
                        cb(buildErrorHttpMsg(-1, "upload file failed, mac = " +  mac));
                        return;
                    }
                }
            }
            else
            {
                AK_LOG_WARN << "Mac:" << mac << " shadow_userall, create user detail file failed";
                cb(buildErrorHttpMsg(-1, "create user detail file failed, mac = " +  mac));
                return;
            }
            break;
        }
        default:
        {
            AK_LOG_WARN << "invalid shadow type: " << filetype;
            cb(buildErrorHttpMsg(-1, "invalid shadow type: " + type));
            return;
        }
    }

    std::string antihot_link = GetNginxAntiHotlink(ak_download_key, remote_filepath);
    char download_url[256];
    snprintf(download_url, sizeof(download_url), "https://%s%s%s", gstCSCONFIGConf.fdfs_config_addr, remote_filepath.c_str(), antihot_link.c_str());

    std::string local_filepath =  "/tmp/office_devshadow";
    remove(local_filepath.c_str());

    char wget_cmd[256];
    snprintf(wget_cmd, sizeof(wget_cmd), "wget -c '%s' --no-check-certificate -O %s", download_url, local_filepath.c_str()); 
    system(wget_cmd);

    char enc_key_v1[64] = {0};
    Snprintf(enc_key_v1, sizeof(enc_key_v1), AES_ENCRYPT_KEY_V1);

    FileAESDecrypt(local_filepath.c_str(), enc_key_v1, local_filepath.c_str());  

    std::ifstream input_file(local_filepath.c_str()); 
    if (input_file.is_open()) 
    {
        std::string line;
        while (std::getline(input_file, line)) 
        {   
            DataMasking(line);            
            content << line << "\r\n";
        }
    }
    input_file.close(); // 关闭文件
    
    cb(content.str());
    return;
}

void startHttpServer()
{
    const int port = 9510;
    const int thread_num = 0;
    std::string addr = GetEth0IPAddr(); //监听在内网

    evpp::http::Server server(addr, thread_num, false);//evpp::http::Server 不需要器线程池,网络线程跟业务处理线程同一个即可.
    server.RegisterDefaultHandler(&DefaultHandler);
    server.RegisterHandler("/metrics", HttpReqMetricsCallback);
    
    server.RegisterHandler("/getMngUUIDFilter", HttpReqGetMngFilterCallback);
    server.RegisterHandler("/setMngUUIDFilter", HttpReqSetMngFilterCallback);    
    server.RegisterHandler("/refreshProjectData", HttpRefreshProjectDataCallback);
    server.RegisterHandler("/getDevicesShadow", HttpReqGetDevicesShadowCallback);
    
    server.Init(port);
    server.Start();
    
    return;
}


