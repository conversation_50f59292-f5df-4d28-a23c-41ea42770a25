#ifndef __SHADOW_MNG_H__
#define __SHADOW_MNG_H__

#include <string>
#include <mutex>
#include <boost/noncopyable.hpp>
#include "dbinterface/Shadow.h"
#include "config_fdfs_uploader.h"
#include <memory>
#include <mutex>
#include <queue>
#include <condition_variable>

class CShadowMng
{
public:
    CShadowMng();
    ~CShadowMng();    
    int StoreDevShadow(const char* local_filepath, const std::string& mac, SHADOW_TYPE shadow_type);
    int DeleteDevShadow(const std::string &mac);
    bool CheckFdfsNormal();
private:
    std::unique_ptr<ConfigFdfsUploader> uploader_;
};




class ShadowMngPool {
public:
    class PooledObject {
    public:
        PooledObject(ShadowMngPool& pool, std::unique_ptr<CShadowMng> obj)
            : pool_(pool), obj_(std::move(obj)) {}

        ~PooledObject() {
            if (obj_) {
                pool_.release(std::move(obj_));
            }
        }

        PooledObject(const PooledObject&) = delete;
        PooledObject& operator=(const PooledObject&) = delete;

        PooledObject(PooledObject&&) = default;
        PooledObject& operator=(PooledObject&&) = default;

        CShadowMng* operator->() const noexcept { return obj_.get(); }
        CShadowMng& operator*() const noexcept { return *obj_; }

    private:
        ShadowMngPool& pool_;
        std::unique_ptr<CShadowMng> obj_;
    };

    explicit ShadowMngPool(size_t initial_size = 10) {
        std::lock_guard<std::mutex> lock(mutex_);
        for (size_t i = 0; i < initial_size; ++i) {
            pool_.push(std::unique_ptr<CShadowMng>(new CShadowMng()));
        }
    }

    // 获取全局单例（线程安全）
    static ShadowMngPool& instance() {
        static ShadowMngPool global_instance;
        return global_instance;
    }


    PooledObject acquire() {
        std::unique_lock<std::mutex> lock(mutex_);
        cond_.wait(lock, [this] { return !pool_.empty(); });

        auto obj = std::move(pool_.front());
        pool_.pop();
        return PooledObject(*this, std::move(obj)); // 隐式构造
    }

    std::unique_ptr<PooledObject> try_acquire() {
        std::lock_guard<std::mutex> lock(mutex_);
        if (pool_.empty()) return std::unique_ptr<PooledObject>(); // 空指针

        auto obj = std::move(pool_.front());
        pool_.pop();
        return std::unique_ptr<PooledObject>(
            new PooledObject(*this, std::move(obj))
        );
    }

private:
    friend class PooledObject;

    void release(std::unique_ptr<CShadowMng> obj) {
        {
            std::lock_guard<std::mutex> lock(mutex_);
            pool_.push(std::move(obj));
        }
        cond_.notify_one();
    }

    std::mutex mutex_;
    std::condition_variable cond_;
    std::queue<std::unique_ptr<CShadowMng>> pool_;
};




#endif //__SHADOW_MNG_H__

