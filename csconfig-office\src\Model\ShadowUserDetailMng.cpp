#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <string.h>
#include <errno.h>
#include <sys/types.h>
#include <sys/stat.h>
#include "base64.h"
#include "sockopt.h"
#include <assert.h>
#include "AkLogging.h"
#include "ShadowUserDetailMng.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "ConfigDef.h"
#include "AES256.h"
#include "AkcsMonitor.h"
#include "util.h"



static const char kConfigFdfsGroup[] = "group4";
static const char kConfigOfficeFdfsFilePath[] = "/usr/local/akcs/csconfig-office/conf/csconfig_fdfs.conf";

ConfigUserDetailFdfsUploaderPool::ConfigUserDetailFdfsUploaderPool(size_t pool_size) : max_size_(pool_size)
{
    for (size_t i = 0; i < max_size_; ++i) {
        auto uploader = std::make_shared<ConfigFdfsUploader>();
        uploader->Init(kConfigOfficeFdfsFilePath);
        uploader->SetUploadGroupName(kConfigFdfsGroup);
        pool_.emplace_back(uploader);
    }
}


std::shared_ptr<ConfigFdfsUploader> ConfigUserDetailFdfsUploaderPool::AcquireUploader() {
    std::unique_lock<std::mutex> lock(mutex_);
    cond_.wait(lock, [this]() { return !pool_.empty(); });

    auto uploader = pool_.back();
    pool_.pop_back();
    return uploader;
}

void ConfigUserDetailFdfsUploaderPool::ReleaseUploader(std::shared_ptr<ConfigFdfsUploader> uploader) {
    std::lock_guard<std::mutex> lock(mutex_);
    pool_.push_back(uploader);
    cond_.notify_one();
}




int ConfigUserDetailFdfsUploaderPool::UploaderHandle::UploadFile(const std::string& local_filepath, std::string& path_after) {
    int storage_ret = 0;
    if (local_filepath.size() == 0) {
        AK_LOG_WARN << "StoreDevShadow param error, return";
        return -1;
    }

    if (uploader_->UploadFile(local_filepath, path_after, 1) != 0) {
        if (uploader_->UploadFile(local_filepath, path_after, 1) != 0) {
            AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csconfig", local_filepath, AKCS_MONITOR_ALARM_CONFIG_WRITE_FAILED);
            AK_LOG_WARN << "Upload User detail File failed, direct update source file to database. path:" <<local_filepath;
            
            storage_ret = -1;
        }
    }

    return storage_ret;
}

