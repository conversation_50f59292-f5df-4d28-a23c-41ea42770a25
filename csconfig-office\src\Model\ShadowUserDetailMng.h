#ifndef __SHADOW_USERDEAIL_MNG_H__
#define __SHADOW_USERDEAIL_MNG_H__

#include <string>
#include <mutex>
#include <iostream>
#include <vector>
#include <mutex>
#include <condition_variable>
#include <memory>
#include <boost/noncopyable.hpp>
#include "config_fdfs_uploader.h"
#include "AkcsMonitor.h"



class ConfigUserDetailFdfsUploaderPool {
public:
    static ConfigUserDetailFdfsUploaderPool& GetInstance(size_t pool_size = 3) {
        static ConfigUserDetailFdfsUploaderPool instance(pool_size);
        return instance;
    }

    std::shared_ptr<ConfigFdfsUploader> AcquireUploader();
    void ReleaseUploader(std::shared_ptr<ConfigFdfsUploader> uploader);

    class UploaderHandle {
    public:
        UploaderHandle(ConfigUserDetailFdfsUploaderPool& pool) 
            : pool_(pool), uploader_(pool.AcquireUploader()) {}
    
        ~UploaderHandle() {
            pool_.ReleaseUploader(uploader_);
        }
    
        int UploadFile(const std::string& local_filepath, std::string& path_after);
    
    private:
        ConfigUserDetailFdfsUploaderPool& pool_;
        std::shared_ptr<ConfigFdfsUploader> uploader_;
    };


private:
    ConfigUserDetailFdfsUploaderPool(size_t pool_size);

    // Disable copy and assignment
    ConfigUserDetailFdfsUploaderPool(const ConfigUserDetailFdfsUploaderPool&) = delete;
    ConfigUserDetailFdfsUploaderPool& operator=(const ConfigUserDetailFdfsUploaderPool&) = delete;

    size_t max_size_;
    std::vector<std::shared_ptr<ConfigFdfsUploader>> pool_;
    std::mutex mutex_;
    std::condition_variable cond_;
};





#endif //__SHADOW_MNG_H__

