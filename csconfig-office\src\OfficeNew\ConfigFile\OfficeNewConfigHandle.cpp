#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <assert.h>
#include <string>
#include "AKCSMsg.h"
#include "AkLogging.h"
#include "ConfigDef.h"
#include "InnerUtil.h"
#include "util_judge.h"
#include "BasicDefine.h"
#include "AkcsCommonDef.h"
#include "util_virtual_door.h"
#include "dbinterface/AntiPassbackDoor.h"
#include "OfficeNew/ConfigFile/OfficeNewConfigHandle.h"
#include "OfficeNew/ConfigFile/OfficeNewDevContact.h"
#include "OfficeNew/ConfigFile/OfficeNewDevSchedule.h"
#include "dbinterface/new-office/OfficeMusterReportSettingReaderList.h"
#include "InnerUtil.h"
#include "util_judge.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "util_time.h"

extern CSCONFIG_CONF gstCSCONFIGConf;


NewOfficeConfigHandle::NewOfficeConfigHandle(const std::string &office_uuid)
{
    FunctionCostTicker cost_ticker(__func__, office_uuid);
    ThreadLocalSingleton::GetInstance().SetDbStatus(true);

    office_uuid_ = office_uuid;
    file_path_ = GetNewOfficePath(office_uuid);
    dbinterface::Account::GetMngTransType(office_uuid, mng_setting_);
    g_office_info_ = std::make_shared<OfficeInfo>(office_uuid);

    //初始化所有设备列表
    InitDevicesList();
    //初始化设备DoorList
    InitDevicesDoorList();
    //初始化返潜回信息
    InitAntiPassbackInfo();
    //初始化公司信息
    dbinterface::OfficeCompany::GetOfficeCompanyByProjectUUID(office_uuid, company_list_);
    //初始化公司和group信息
    dbinterface::OfficeGroup::GetOfficeGroupByProjectUUID(office_uuid, group_company_group_map_, group_company_uuid_map_, group_company_company_map_);
}

NewOfficeConfigHandle::~NewOfficeConfigHandle()
{
    
}


void NewOfficeConfigHandle::InitUserInfo()
{
    FunctionCostTicker cost_ticker(__func__, office_uuid_);
    //获取权限组列表
    if (!dm_ag_dev_map_.IsInitialized())
    {
        dbinterface::OfficeAccessGroupDevice::GetAccessGroupDevicesByProjectUUID(office_uuid_, dm_ag_dev_map_.GetAndInit(), dm_ag_dev_uuid_map_.GetAndInit());
    }

    //获取group和权限组关系
    if (!dm_ag_group_map_.IsInitialized())
    {    
        dbinterface::OfficeGroupAccessGroup::GetOfficeGroupAGByProjectUUID(office_uuid_, dm_group_ag_map_.GetAndInit(), dm_ag_group_map_.GetAndInit());
    }
    
    //获取delivery和权限组关系
    if (!dm_ag_devlivery_map_.IsInitialized())
    {    
        dbinterface::OfficeDeliveryAccessGroup::GetOfficeDeliveryAccessGroupByProjectUUID(office_uuid_, dm_devlivery_ag_map_.GetAndInit(), dm_ag_devlivery_map_.GetAndInit());
    }
    
    //获取用户和group的关系 
    if (!dm_group_of_per_group_map_.IsInitialized())
    {    
        dbinterface::OfficePersonnelGroup::GetOfficePersonnelGroupByProjectUUID(office_uuid_, dm_group_of_per_group_map_.GetAndInit(), dm_group_of_per_per_map_.GetAndInit());
    }

    //获取admin和group的关系
    if (!dm_group_of_admin_group_map_.IsInitialized())
    {
        dbinterface::OfficeAdminGroup::GetofficeAdminGroupByProjectUUID(office_uuid_, dm_group_of_admin_per_map_.GetAndInit(), dm_group_of_admin_group_map_.GetAndInit());
    }
    
    //获取所有的用户列表
    if (!dm_account_map_.IsInitialized())
    {    
        dbinterface::OfficePersonalAccount::GetAllAccountList(office_uuid_, dm_account_map_.GetAndInit(), dm_account_mate_map_.GetAndInit());
    }     
    if (!dm_personnel_map_.IsInitialized())
    {    
        dbinterface::OfficePersonnel::GetOfficePersonnelByProjectUUID(office_uuid_, dm_personnel_map_.GetAndInit());
    }
    if (!dm_admin_map_.IsInitialized())
    {
        dbinterface::OfficeAdmin::GetOfficeAdminPerMapByProjectUUID(office_uuid_, dm_admin_map_.GetAndInit(), dm_company_admin_map_.GetAndInit());
    }
     
}

void NewOfficeConfigHandle::InitScheduleInfo()
{
    FunctionCostTicker cost_ticker(__func__, office_uuid_);
    //获取权限组列表
    if (!dm_ag_dev_map_.IsInitialized())
    {
        dbinterface::OfficeAccessGroupDevice::GetAccessGroupDevicesByProjectUUID(office_uuid_, dm_ag_dev_map_.GetAndInit(), dm_ag_dev_uuid_map_.GetAndInit());
    }

    //权限组详细信息
    if (!dm_ag_info_map_.IsInitialized())
    {      
        dbinterface::OfficeAccessGroup::GetOfficeAccessGroupByProjectUUID(office_uuid_, dm_ag_info_map_.GetAndInit());
    }
    //holiday相关
    if (!dm_project_holiday_map_.IsInitialized())
    {      
        dbinterface::OfficeHoliday::GetOfficeHolidayByProjectUUID(office_uuid_, dm_project_holiday_map_.GetAndInit(), dm_company_holiday_map_.GetAndInit());
    }
}

void NewOfficeConfigHandle::InitUserDetailInfo()
{
    FunctionCostTicker cost_ticker(__func__, office_uuid_);
    //获取权限组列表
    if (!dm_ag_dev_map_.IsInitialized())
    {
        dbinterface::OfficeAccessGroupDevice::GetAccessGroupDevicesByProjectUUID(office_uuid_, dm_ag_dev_map_.GetAndInit(), dm_ag_dev_uuid_map_.GetAndInit());
    }
    
    //权限组详细信息
    if (!dm_ag_info_map_.IsInitialized())
    {      
        dbinterface::OfficeAccessGroup::GetOfficeAccessGroupByProjectUUID(office_uuid_, dm_ag_info_map_.GetAndInit());
    }

    //快递人员
    if (!dm_delivery_map_.IsInitialized())
    {      
        dbinterface::OfficeDelivery::GetOfficeDeliveryByProjectUUID(office_uuid_, dm_delivery_map_.GetAndInit(), dm_delivery_mate_map_.GetAndInit());
    }
}

void NewOfficeConfigHandle::InitDevicesList()
{
    FunctionCostTicker cost_ticker(__func__, office_uuid_);
    dbinterface::OfficeDevices::GetAllOfficeDevList(office_uuid_, all_dev_list_);
    for(const auto &dev : all_dev_list_)
    {
        if (akjudge::IsCommunityPublicDev(dev.second->grade) && akjudge::DevDoorType(dev.second->dev_type))
        {
            public_list_.insert(dev);
        }
        else
        {
            indoor_or_manage_dev_list_.insert(dev);
        }
    }    

    //获取设备绑定的用户或组的列表
    if (!dm_account_dev_dev_map_.IsInitialized())
    {    
        dbinterface::OfficeDeviceAssign::GetOfficeDeviceAssignByProjectUUID(office_uuid_, dm_account_dev_dev_map_.GetAndInit(),
        dm_account_dev_per_map_.GetAndInit(), dm_account_dev_group_map_.GetAndInit(), dm_account_dev_company_map_.GetAndInit());
    }

    GetNoBelongsToDevList(no_belongs_to_dev_uuid_list_, no_belongs_to_dev_list_);
    return;
}

void NewOfficeConfigHandle::InitDevicesDoorValidity(const DevicesDoorInfoList& devices_door_info_list, OfficeDevPtr& dev)
{
    FunctionCostTicker cost_ticker(__func__, office_uuid_);
    for (const auto& devices_door : devices_door_info_list)
    {
        if (IsDoorInactiveOrExpired(devices_door.enable, devices_door.active, devices_door.expire))
        {
            dev->exist_invalid_door = true;
            return;
        }
    }
    return;
}

void NewOfficeConfigHandle::InitDevicesDoorList()
{
    FunctionCostTicker cost_ticker(__func__, office_uuid_);
    // 获取项目的设备 Door列表
    dbinterface::DevicesDoorList::GetDevicesDoorMapByProjectUUID(office_uuid_, dev_door_info_map_, pub_door_info_map_);

    for (auto& dev : public_list_)
    {
        OfficeDevPtr dev_info = dev.second;
        if (dev_door_info_map_.find(dev_info->uuid) != dev_door_info_map_.end())
        {
            // 初始化设备door付费状态
            InitDevicesDoorValidity(dev_door_info_map_[dev_info->uuid], dev_info);
        }
    }
    return;
}

void NewOfficeConfigHandle::InitAntiPassbackInfo()
{
    FunctionCostTicker cost_ticker(__func__, office_uuid_);
    // 获取项目返潜回设备列表
    AntiPassBackDoorInfoList antipassback_door_list;
    dbinterface::AntiPassBackDoor::GetAntiPassBackDoorListByProjectUUID(office_uuid_, antipassback_door_list);

    // 清空map以确保没有旧数据
    antipassback_door_info_map_.clear();

    //转换成map便于查找
    for (const auto& antipassback_door : antipassback_door_list) 
    {
        // 检查设备UUID是否已存在
        bool found = false;
        auto range = antipassback_door_info_map_.equal_range(antipassback_door.dev_uuid);
        
        for (auto it = range.first; it != range.second; ++it) 
        {
            auto& relay_type_map = it->second;
            auto relay_type_it = relay_type_map.find((int)antipassback_door.relay_type);
            
            if (relay_type_it != relay_type_map.end()) 
            {
                // relay_type存在，添加或更新relay_num
                relay_type_it->second[antipassback_door.relay_num] = antipassback_door;
                found = true;
                break;
            }
        }
        
        if (!found) {
            // 没有找到匹配的设备UUID和relay_type，插入新条目
            std::unordered_map<int, std::unordered_map<int, AntiPassBackDoorInfo>> relay_type_map;
            relay_type_map[(int)antipassback_door.relay_type] = {{antipassback_door.relay_num, antipassback_door}};
            antipassback_door_info_map_.insert({antipassback_door.dev_uuid, relay_type_map});
        }
    }
}

std::string NewOfficeConfigHandle::GetCompanyName(const std::string &uuid, OfficeUUIDType type)
{
    std::string company_name;
    if (type == OfficeUUIDType::PER_UUID)
    {
        //会有多条，但是一定是属于同个公司的
        auto it_per = dm_group_of_per_per_map_.Get().find(uuid);
        if (it_per != dm_group_of_per_per_map_.Get().end()) {
            const OfficePersonnelGroupInfo& value = it_per->second;
            //这里一定会有company？
            std::string company_uuid = group_company_uuid_map_[value.office_group_uuid];
            auto it_per_company = company_list_.find(company_uuid);
            if (it_per_company != company_list_.end())
            {
                return it_per_company->second.name;
            }
        }

        //找不到就找admin的
        auto it_admin = dm_group_of_admin_per_map_.Get().find(uuid);
        if (it_admin != dm_group_of_admin_per_map_.Get().end()) {
            const auto& value = it_admin->second;
            std::string company_uuid = group_company_uuid_map_[value.office_group_uuid];
            auto it_admin_company = company_list_.find(company_uuid);
            if (it_admin_company != company_list_.end())
            {
                return it_admin_company->second.name;
            }
        }
    }
    else if (type == OfficeUUIDType::GROUP_UUID)
    {
        std::string company_uuid = group_company_uuid_map_[uuid];
        auto it_group_company = company_list_.find(company_uuid);
        if (it_group_company != company_list_.end())
        {
            company_name = it_group_company->second.name;
        }
    }
    else if (type == OfficeUUIDType::COMPANY_UUID)
    {
        auto it_company = company_list_.find(uuid);
        if (it_company != company_list_.end())
        {
            company_name = it_company->second.name;
        }
    }

    return std::move(company_name);    
}

std::string NewOfficeConfigHandle::GetGroupName(const std::string &uuid, OfficeUUIDType type)
{
    std::string group_name;
    if (type == OfficeUUIDType::PER_UUID)
    {
        auto per_group_list = dm_group_of_per_per_map_.Get().equal_range(uuid);
        for (auto it = per_group_list.first; it != per_group_list.second; ++it) {
            const OfficePersonnelGroupInfo& personnel_group = it->second;
            auto group = group_company_group_map_.find(personnel_group.office_group_uuid);
            if (group != group_company_group_map_.end())
            {
                return group->second.name;
            } 
        }  

        auto admin_group_list = dm_group_of_admin_per_map_.Get().equal_range(uuid);
        for (auto it = admin_group_list.first; it != admin_group_list.second; ++it) {
            const OfficeAdminGroupInfo& admin_group = it->second;
            auto group = group_company_company_map_.find(admin_group.office_group_uuid);
            if (group != group_company_company_map_.end())
            {
                return group->second.name;
            }
        }   
    }
    else if (type == OfficeUUIDType::GROUP_UUID)
    {
        auto group = group_company_group_map_.find(uuid);
        if (group != group_company_group_map_.end())
        {
            return group->second.name;
        }        
    }
    return std::move(group_name);    
}

void NewOfficeConfigHandle::GetGroupCallSeq(const std::string &group_uuid, OfficeGroupSeqCallMap &call_seq_map)
{
    auto it = group_call_seq_map_.find(group_uuid);
    if (it == group_call_seq_map_.end()) {
        //group_call_seq_map_需要时候再查询 并且不用重复查询
        dbinterface::OfficeGroupSequenceCall::GetOfficeGroupSequenceCallByOfficeGroupUUID(group_uuid, group_call_seq_map_);
    }

    auto range = group_call_seq_map_.equal_range(group_uuid);
    for (auto it = range.first; it != range.second; ++it) {
        call_seq_map.insert(*it);
    } 
}

std::string NewOfficeConfigHandle::GetCompanyUUID(const std::string &uuid, OfficeUUIDType type)
{
    if (type == OfficeUUIDType::PER_UUID)
    {
        auto it = account_company_uuid_map_.find(uuid);
        if (it == account_company_uuid_map_.end()) 
        {
            std::string group_uuid, company_uuid;
            auto group = dm_group_of_per_per_map_.Get().find(uuid);
            if (group != dm_group_of_per_per_map_.Get().end())
            {
                group_uuid = group->second.office_group_uuid;
            }
            auto it = group_company_group_map_.find(group_uuid);
            if (it != group_company_group_map_.end())
            {
                company_uuid = it->second.office_company_uuid;
            }   
            account_company_uuid_map_.insert(std::make_pair(uuid, company_uuid));
            return std::move(company_uuid);
        }
        return it->second;    
    }
    else if (type == OfficeUUIDType::GROUP_UUID)
    {
        auto group = group_company_uuid_map_.find(uuid);
        if (group != group_company_uuid_map_.end())
        {
            return group->second;
        }        
    }
    return "";
}

OfficeUUIDSet NewOfficeConfigHandle::GetGroupUUIDList(const std::string &account_uuid, int role)
{
    OfficeUUIDSet group_uuid_set;
    if (role == ACCOUNT_ROLE_OFFICE_NEW_PER)
    {
        auto range = dm_group_of_per_per_map_.Get().equal_range(account_uuid);
        for (auto it = range.first; it != range.second; ++it) {
            group_uuid_set.insert(it->second.office_group_uuid);
        }
    }
    else if (role == ACCOUNT_ROLE_OFFICE_NEW_ADMIN)
    {
        auto range = dm_group_of_admin_per_map_.Get().equal_range(account_uuid);
        for (auto it = range.first; it != range.second; ++it) {
            group_uuid_set.insert(it->second.office_group_uuid);
        }
    }
    return std::move(group_uuid_set);
}

//获取用户的权限组uuid列表
OfficeUUIDSet NewOfficeConfigHandle::GetOfficePerAgUUIDListCb(const std::string &account_uuid, int role)
{
    OfficeUUIDSet group_uuid_set = GetGroupUUIDList(account_uuid, role);;

    OfficeUUIDSet ag_uuid_list;
    for (const auto &uuid : group_uuid_set) {
        auto ag = dm_ag_group_map_.Get().equal_range(uuid);
        
        for (auto it = ag.first; it != ag.second; ++it) {
            ag_uuid_list.insert(it->second);
        }         
    }

    return std::move(ag_uuid_list);
}

//获取设备权限组uuid列表
OfficeUUIDSet NewOfficeConfigHandle::GetOfficeDevAgUUIDListCb(const std::string &dev_uuid)
{
    OfficeUUIDSet ag_uuid_list;
    auto range = dm_ag_dev_map_.Get().equal_range(dev_uuid);
    for (auto it = range.first; it != range.second; ++it) {
        ag_uuid_list.insert(it->second.office_access_group_uuid);
    }
    return std::move(ag_uuid_list);
}

//获取delivery权限组uuid列表
OfficeUUIDSet NewOfficeConfigHandle::GetOfficeDeliveryAgUUIDListCb(const std::string &delivery_uuid)
{
    OfficeUUIDSet ag_uuid_list;
    auto range = dm_devlivery_ag_map_.Get().equal_range(delivery_uuid);
    for (auto it = range.first; it != range.second; ++it) {
        ag_uuid_list.insert(it->second);
    }
    return std::move(ag_uuid_list);
}

OfficeUUIDSet NewOfficeConfigHandle::GetOfficePerGroupUUIDListCb(const std::string& account_uuid, int role)
{
    return GetGroupUUIDList(account_uuid, role);
}

void NewOfficeConfigHandle::GetPerOrGroupPermissionDevUUIDSet(const OfficeDeviceAssignInfo &dev_info, OfficeUUIDSet &permission_dev_list)
{
    if(dev_info.type == DeviceAssignType::DeviceAssignTypPer)//关联用户
    {         
        permission_dev_list = GetOfficePubDevUUIDListByPerUUID(dev_info.personal_account_uuid, dm_ag_dev_uuid_map_.Get(), dm_ag_group_map_.Get(), dm_group_of_per_per_map_.Get());
    }
    else if (dev_info.type == DeviceAssignType::DeviceAssignTypGroup)//关联组
    {
        permission_dev_list = GetOfficeDevListByGroup(dev_info.office_group_uuid, dm_ag_dev_uuid_map_.Get(),dm_ag_group_map_.Get());                
    }

}


//获取公司室内机和管理机列表
OfficeUUIDSet NewOfficeConfigHandle::GetCompanyIndoorMngDevUUIDList(const std::string &company_uuid)
{
    OfficeUUIDSet dev_uuid_list;
    auto ag = dm_account_dev_company_map_.Get().equal_range(company_uuid);
    for (auto it = ag.first; it != ag.second; ++it) {
        dev_uuid_list.insert(it->second.devices_uuid);
    }        
    return std::move(dev_uuid_list);
}

//获取未配置belongs to的设备列表
void NewOfficeConfigHandle::GetNoBelongsToDevList(OfficeUUIDSet& dev_uuid_list, OfficeDevMap& dev_list)
{
    for(const auto& indoor_or_manage_dev : indoor_or_manage_dev_list_)
    {    
        bool have_belongs_to = false;
        //根据dev_uuid 获取对应的assign_info,看是否有AssignPer的类型
        auto range = dm_account_dev_dev_map_.Get().equal_range(indoor_or_manage_dev.first);
        for (auto it = range.first; it != range.second; ++it) 
        {
            //判断belongs to
            if (it->second.type == DeviceAssignType::DeviceAssignTypPer)
            {
                have_belongs_to = true;
                break;
            }
        }
        if(!have_belongs_to)
        {
            dev_uuid_list.insert(indoor_or_manage_dev.first);
            dev_list.insert(indoor_or_manage_dev);
        }
    }
}

OfficeUUIDSet NewOfficeConfigHandle::GetPublicDoorList()
{
    OfficeUUIDSet dev_uuid_list;
    for(const auto& pub_door : pub_door_info_map_)
    {    
        dev_uuid_list.insert(pub_door.first);
    }
    
    return dev_uuid_list;
}

OfficeUUIDSet NewOfficeConfigHandle::GetOfficeDevPermissionUserUUIDList(const std::string& dev_uuid, bool is_muster_report_dev)
{
    OfficeUUIDSet group_set;
    OfficeUUIDSet permission_account_list;
    //点名设备,写入公司所有Personnel+有权限的admin
    if (is_muster_report_dev)
    {
        // 写入公司所有Personnel
        for(const auto& pair : dm_personnel_map_.Get())
        {
            permission_account_list.insert(pair.first);
        }   
        // 写入有权限的admin
        auto admin_list = GetOfficeAdminUUIDListByAgDev(dev_uuid, dm_ag_dev_map_.Get(), dm_group_ag_map_.Get(), dm_group_of_admin_group_map_.Get(), group_set);
        permission_account_list.insert(admin_list.begin(), admin_list.end());
    }
    else
    {
        //写入设备有权限的personnel+admin列表
        permission_account_list = GetOfficeUserUUIDListByAgDev(dev_uuid, dm_ag_dev_map_.Get(), dm_group_ag_map_.Get(), dm_group_of_per_group_map_.Get(), 
                                    dm_group_of_admin_group_map_.Get(), group_set);
    }

    return std::move(permission_account_list);
}

OfficeUUIDSet NewOfficeConfigHandle::GetOfficeDevPermissionDeliveryUUIDList(const std::string& dev_uuid, bool is_muster_report_dev)
{
    OfficeUUIDSet permission_delivery_list;
    //点名设备,下发公司所有Delivery
    if (is_muster_report_dev)
    {
        for(const auto& pair : dm_delivery_map_.Get())
        {
            permission_delivery_list.insert(pair.first);
        }    
    }
    else
    {
        //获取设备有权限的快递人员列表
        permission_delivery_list = GetOfficeDeliveryUUIDListByAgDev(dev_uuid, dm_ag_dev_map_.Get(), dm_ag_devlivery_map_.Get());
    }

    return std::move(permission_delivery_list);
}

void NewOfficeConfigHandle::UpdateDevConfig(const std::string &dev_uuid)
{
    FunctionCostTicker cost_ticker(__func__, office_uuid_);
    InitScheduleInfo();
    NewOfficeDevConfig devconfig(file_path_, mng_setting_);
    devconfig.SetOfficeInfo(g_office_info_);
    devconfig.SetAgInfo(dm_ag_dev_map_.Get(), dm_ag_dev_uuid_map_.Get(), dm_ag_info_map_.Get());
    devconfig.SetDevDoorListInfo(dev_door_info_map_);    
    devconfig.SetPubDoorListInfo(pub_door_info_map_);
    devconfig.SetNoBelongsToDevUUIDList(no_belongs_to_dev_list_);
    devconfig.SetAntiPassbackInfo(antipassback_door_info_map_);
    for(const auto &dev : public_list_)
    {
        // 判断设备的door存在非法的door, 存在非法的door就不刷配置
        if (dev.second->exist_invalid_door)
        {
            AK_LOG_INFO << "UpdateDevConfig Device all doors not avtive or doors already expired, mac = " << dev.second->mac;
            continue;
        }

        if (dev_uuid.size() == 0 || strcmp(dev.second->uuid, dev_uuid.c_str()) == 0)
        {
            AK_LOG_INFO << ">>start write public config mac:" << dev.second->mac;
            devconfig.WriteFiles(dev.second);
        }
    }

    for(const auto &dev : indoor_or_manage_dev_list_)
    {
        if (dev_uuid.size() == 0 || strcmp(dev.second->uuid, dev_uuid.c_str()) == 0 )
        {
            AK_LOG_INFO << ">>start write indoor/mng config mac:" << dev.second->mac;
            devconfig.WriteFiles(dev.second);
        }
    }     
}


void NewOfficeConfigHandle::UpdateDevContact(const std::string &dev_uuid)
{
    FunctionCostTicker cost_ticker(__func__, office_uuid_);
    InitUserInfo();
    ProjectSipGroupMap sip_group_map;
    dbinterface::Sip::GetSipGroupListByProject(office_uuid_, sip_group_map);
    
    NewOfficeDevContact contact(file_path_, g_office_info_);
    contact.InitUserInfo(dm_account_map_.Get(), dm_personnel_map_.Get(), all_dev_list_, dm_group_of_per_per_map_.Get(), dm_group_of_per_group_map_.Get(),
        dm_account_dev_per_map_.Get(), group_company_group_map_, dm_admin_map_.Get(), dm_group_of_admin_per_map_.Get(), dm_group_of_admin_group_map_.Get(), dm_company_admin_map_.Get());
    contact.SetNameInfoCb(std::bind(&NewOfficeConfigHandle::GetCompanyName, this, std::placeholders::_1, std::placeholders::_2),
        std::bind(&NewOfficeConfigHandle::GetGroupName, this, std::placeholders::_1, std::placeholders::_2));
    contact.SetCompanyUUIDCb(std::bind(&NewOfficeConfigHandle::GetCompanyUUID, this, std::placeholders::_1, std::placeholders::_2));
    contact.SetGetGroupSeqCallCb(std::bind(&NewOfficeConfigHandle::GetGroupCallSeq, this, std::placeholders::_1, std::placeholders::_2));
    contact.SetSipGroupMap(sip_group_map);
    contact.SetDevDoorInfoMap(dev_door_info_map_);
    contact.SetDevPublicDoorInfoMap(pub_door_info_map_);    
    OfficeUUIDSet pub_door_uuid_set = GetPublicDoorList();                        

    for(const auto &dev_map : public_list_)
    {
        OfficeDevPtr dev = dev_map.second;
        // 判断设备的door存在非法的door, 存在非法的door就不刷配置
        if (dev->exist_invalid_door)
        {
            AK_LOG_INFO << "UpdateDevContact exist invalid door, no need to write contact file. mac = " << dev->mac;
            continue;
        }
        
        if (dev_uuid.size() == 0 || strcmp(dev->uuid, dev_uuid.c_str()) == 0)
        {
            //设备关联的group列表
            OfficeUUIDSet group_set;
            //获取设备有权限的用户列表
            OfficeUUIDSet permission_account_list = GetOfficeUserUUIDListByAgDev(dev->uuid, dm_ag_dev_map_.Get(), 
                                                                                dm_group_ag_map_.Get(), dm_group_of_per_group_map_.Get(), 
                                                                                dm_group_of_admin_group_map_.Get(), group_set);
            if (permission_account_list.size() == 0)
            {
                AK_LOG_INFO << "UpdateDevContact Mac have not account permission list. mac:" << dev->mac;
            }
            AK_LOG_INFO << ">>start write public contact mac:" << dev->mac;
            contact.WritePublicDevContactFile(dev, permission_account_list, group_set, no_belongs_to_dev_uuid_list_);
        }
    }
    
    for(const auto &dev_tmp : indoor_or_manage_dev_list_)
    {
        if (dev_uuid.size() == 0 || strcmp(dev_tmp.second->uuid, dev_uuid.c_str()) == 0)
        {
            auto dev = dev_tmp.second;
            AK_LOG_INFO << ">>start write indoor/mng contact mac:" << dev->mac;

            //根据dev_uuid获取assign_info,一对多的关系
            auto range = dm_account_dev_dev_map_.Get().equal_range(dev->uuid);
            if (range.first != range.second)
            {
                OfficeUUIDSet device_company_uuids;
                OfficeUUIDSet permission_dev_list;
                bool have_belongs_to = false;
                OfficeDeviceAssignInfo device_assign_info;
                for (auto it = range.first; it != range.second; ++it)
                {
                    int dev_assign_type = it->second.type;
                    //若有设置belongs to,需要获取对应用户有权限的设备,即permission_dev_list
                    //管理机是用可能assign到公司，但没有belongs to人的
                    if(dev_assign_type == DeviceAssignType::DeviceAssignTypPer)
                    {
                        device_assign_info = it->second;
                        GetPerOrGroupPermissionDevUUIDSet(device_assign_info, permission_dev_list);
                        have_belongs_to = true;
                    }
                    device_company_uuids.insert(it->second.office_company_uuid);
                }
                
            
                if (dev->dev_type == DEVICE_TYPE_MANAGEMENT)
                {
                    //管理机的PubInfo需要加上项目中所有没选belongs to的设备（即公共管理机/室内机）
                    permission_dev_list.insert(no_belongs_to_dev_uuid_list_.begin(), no_belongs_to_dev_uuid_list_.end());
                    if(!have_belongs_to)
                    {
                        //管理机没有belongs to,才需要加上项目中所有的pubic door
                        permission_dev_list.insert(pub_door_uuid_set.begin(), pub_door_uuid_set.end());
                    }
                    contact.WriteMngDevContactFile(have_belongs_to, dev, device_company_uuids, permission_dev_list);
                }
                else if (dev->dev_type == DEVICE_TYPE_INDOOR)
                {
                    if(device_assign_info.type == DeviceAssignType::DeviceAssignTypPer)//关联用户
                    {
                        OfficeUUIDSet company_dev_uuid_set = GetCompanyIndoorMngDevUUIDList(device_assign_info.office_company_uuid);
                        //获取用户关联的其他设备
                        contact.WriteAccountIndoorDevContactFile(dev, device_assign_info.personal_account_uuid, permission_dev_list, company_dev_uuid_set);
                    }
                    else if (device_assign_info.type == DeviceAssignType::DeviceAssignTypGroup)//关联组
                    {           
                        contact.WriteGroupIndoorDevContactFile(dev, device_assign_info.office_group_uuid, permission_dev_list);
                    }
            
                }            
            }
            //没有assign_info(belongs to 和 company)的也要写一些公共的联系人
            else
            {                
                if (dev->dev_type == DEVICE_TYPE_INDOOR)
                {     
                    OfficeUUIDSet permission_dev_list;
                    permission_dev_list.insert(no_belongs_to_dev_uuid_list_.begin(), no_belongs_to_dev_uuid_list_.end());
                    permission_dev_list.insert(pub_door_uuid_set.begin(), pub_door_uuid_set.end());
                    contact.WriteNoBelongsToIndoorDevContactFile(dev, permission_dev_list);            
                } 
                /*
                else if (dev->dev_type == DEVICE_TYPE_MANAGEMENT)
                {
                    //管理机当前一定至少会有一个company
                }
                */
            }

        }
    }     
}

void NewOfficeConfigHandle::UpdateDevUserMeta(const std::string &dev_uuid)
{
    FunctionCostTicker cost_ticker(__func__, office_uuid_);
    InitUserInfo();
    InitUserDetailInfo();
    NewOfficeDevUser user(file_path_, g_office_info_);
    user.InitUserMateInfo(dm_account_map_.Get(), dm_delivery_map_.Get(), dm_personnel_map_.Get(), dm_admin_map_.Get(),
         dm_account_mate_map_.Get(), dm_delivery_mate_map_.Get(), dm_group_of_per_per_map_.Get(), dev_door_info_map_);
    
    for(const auto &dev_map : public_list_)
    {
        OfficeDevPtr dev = dev_map.second;        
        if (dev_uuid.size() == 0 || strcmp(dev->uuid, dev_uuid.c_str()) == 0)
        {
            // 判断设备的door存在非法的door, 存在非法的door就不刷配置
            if (dev->exist_invalid_door)
            {
                AK_LOG_INFO << "UpdateDevUserMeta Device all doors not avtive or doors already expired, mac = " << dev->mac;
                continue;
            }

            AK_LOG_INFO << ">> start write usermate mac:" << dev->mac;

            //获取设备有权限的用户列表
            OfficeUUIDSet permission_account_list;
            OfficeUUIDSet permission_delivery_list;

            GetOfficeDevPermissionAllUUIDListForUserFileType(dev->mac, dev->uuid, permission_account_list, permission_delivery_list);

            //写入
            user.WritePublicDevUserMateFile(dev, permission_account_list, permission_delivery_list);
        }
    }

}

void NewOfficeConfigHandle::UpdateDevSchedule(const std::string &dev_uuid)
{
    FunctionCostTicker cost_ticker(__func__, office_uuid_);
    InitScheduleInfo();
    NewOfficeDevSchedule schedule(file_path_);
    schedule.InitInfo(dm_ag_dev_map_.Get(), dm_ag_info_map_.Get(), dm_project_holiday_map_.Get(), dm_company_holiday_map_.Get());
    
    for(const auto &dev : public_list_)
    {
        // 判断设备的door存在非法的door, 存在非法的door就不刷配置
        if (dev.second->exist_invalid_door)
        {
            AK_LOG_INFO << "UpdateDevSchedule Device all doors not avtive or doors already expired, mac = " << dev.second->mac;
            continue;
        }
    
        if (dev_uuid.size() == 0 || strcmp(dev.second->uuid, dev_uuid.c_str()) == 0)
        {
            AK_LOG_INFO << ">>start write schedule mac:" << dev.second->mac;
            schedule.WirtePubSchedule(dev.second);
        }
    }
    
}

void NewOfficeConfigHandle::CreateUserInfo(const OfficeDevPtr& dev, OfficePerIDSet &per_set, OfficeUserDetailReq &req)
{
    FunctionCostTicker cost_ticker(__func__, office_uuid_);
    InitUserInfo();
    InitUserDetailInfo();

    NewOfficeDevUser user(file_path_, g_office_info_);
    user_access_.Init(office_uuid_);
    user.InitUserAccess(user_access_, dm_ag_dev_map_.Get(), dm_ag_info_map_.Get());
    user.InitUserMateInfo(dm_account_map_.Get(), dm_delivery_map_.Get(), dm_personnel_map_.Get(), dm_admin_map_.Get(), 
         dm_account_mate_map_.Get(), dm_delivery_mate_map_.Get(), dm_group_of_per_per_map_.Get(), dev_door_info_map_);

    user.SetGetAccountAgUUIDListCb(std::bind(&NewOfficeConfigHandle::GetOfficePerAgUUIDListCb, this, std::placeholders::_1, std::placeholders::_2));
    user.SetGetDevAgUUIDListCb(std::bind(&NewOfficeConfigHandle::GetOfficeDevAgUUIDListCb, this, std::placeholders::_1));
    user.SetGetDeliveryAgUUIDListCb(std::bind(&NewOfficeConfigHandle::GetOfficeDeliveryAgUUIDListCb, this, std::placeholders::_1));    
    user.SetGetAccountGroupUUIDListCb(std::bind(&NewOfficeConfigHandle::GetOfficePerGroupUUIDListCb, this, std::placeholders::_1, std::placeholders::_2));    

    user.GetUserDetailFile(dev, per_set, req);

    AK_LOG_INFO << "write user detail mac:" << dev->mac;
}

void NewOfficeConfigHandle::GetOfficeDevPermissionAllUUIDListForUserFileType(const std::string& mac, const std::string& dev_uuid, OfficeUUIDSet &permission_per_uuid_list, OfficeUUIDSet &permission_delivery_uuid_list)
{
    bool is_muster_report_dev = dbinterface::OfficeMusterReportSettingReaderList::IsMusterReportDevice(dev_uuid);

    //获取设备有权限的员工+admin列表
    permission_per_uuid_list = GetOfficeDevPermissionUserUUIDList(dev_uuid, is_muster_report_dev);       
    if (permission_per_uuid_list.size() == 0)
    {
        AK_LOG_INFO << "UpdateDevUserMeta Mac have not account permission list. mac:" << mac;
    }

    // 获取设备有权限的快递人员列表
    permission_delivery_uuid_list = GetOfficeDevPermissionDeliveryUUIDList(dev_uuid, is_muster_report_dev);
    if (permission_delivery_uuid_list.size() == 0)
    {
        AK_LOG_INFO << "UpdateDevUserMeta Mac have not delivery permission list. mac:" << mac;
    }

    return;
}

OfficePerIDSet NewOfficeConfigHandle::GetOfficeDevPermissionAccountList(const std::string& mac, const std::string& dev_uuid)
{
    InitUserInfo();
    InitUserDetailInfo();

    OfficeUUIDSet permission_per_uuid_list;
    OfficeUUIDSet permission_delivery_uuid_list;

    GetOfficeDevPermissionAllUUIDListForUserFileType(mac, dev_uuid, permission_per_uuid_list, permission_delivery_uuid_list);

    OfficePerIDSet permission_account_list;

    for (const auto& per_uuid : permission_per_uuid_list)
    {
        auto it = dm_account_map_.Get().find(per_uuid);
        if (it != dm_account_map_.Get().end())
        {
            permission_account_list.insert(it->second.account);
        }
    }

    for (const auto& delivery_uuid : permission_delivery_uuid_list)
    {
        auto it = dm_delivery_map_.Get().find(delivery_uuid);
        if (it != dm_delivery_map_.Get().end())
        {
            char delivery_per_id[64] = "";
            snprintf(delivery_per_id, sizeof(delivery_per_id), "D%09d", it->second.id);
            permission_account_list.insert(delivery_per_id);
        }
    }

    return std::move(permission_account_list);
}

