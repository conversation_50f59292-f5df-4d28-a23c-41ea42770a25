#ifndef __OFFICE_CONFIG_NEW_CONTROL_HANDLE__
#define __OFFICE_CONFIG_NEW_CONTROL_HANDLE__

#include "BasicDefine.h"
#include "AKCSMsg.h"
#include "AkLogging.h"
#include "OfficeUserAccessInfo.h"
#include "InnerEnum.h"
#include "InnerDbDef.h"
#include "dbinterface/new-office/DevicesDoorList.h"
#include "dbinterface/AntiPassbackDoor.h"
#include "dbinterface/AntiPassbackArea.h"
#include "OfficeNew/ConfigFile/OfficeNewDevConfig.h"
#include "OfficeNew/ConfigFile/OfficeNewDevCommon.h"
#include "OfficeNew/ConfigFile/OfficeNewDevUser.h"
#include "OfficeNew/ConfigFile/OfficeNewConfigHandleTool.h"

template <typename Type>
class DataManager {
private:
    bool initialized = false;
    Type data_;

public:
    bool IsInitialized() {
        return initialized;
    }

    //获取后进行设置
    Type& GetAndInit() {
        if (initialized)
        {
            AK_LOG_FATAL << "data repeated initialization";
        }    
        initialized = true;
        return data_;
    }

    //获取
    Type& Get() {
        if (!initialized)
        {
            AK_LOG_FATAL << "data uninitialized!";
        }
        return data_;
    }

};


class NewOfficeConfigHandle
{
public:
    NewOfficeConfigHandle(const std::string &office_uuid);
    ~NewOfficeConfigHandle();

    //如果有指定mac则只更新对应的mac的数据
    void UpdateDevConfig(const std::string &dev_uuid = "");
    void UpdateDevContact(const std::string &dev_uuid = "");
    void UpdateDevUserMeta(const std::string &dev_uuid = "");
    void UpdateDevSchedule(const std::string &dev_uuid = "");

    void CreateUserInfo(const OfficeDevPtr& dev, OfficePerIDSet &other_set, OfficeUserDetailReq &req);

    OfficePerIDSet GetOfficeDevPermissionAccountList(const std::string& mac, const std::string& dev_uuid);

    std::string office_uuid_;
private:
    void InitUserInfo();
    void InitScheduleInfo();  
    void InitUserDetailInfo();
    void InitDevicesList();
    void InitDevicesDoorList();
    void InitDevicesDoorValidity(const DevicesDoorInfoList& devices_door_info_list, OfficeDevPtr& dev);
    void InitAntiPassbackInfo();
    std::string GetCompanyName(const std::string &uuid, OfficeUUIDType type);
    std::string GetGroupName(const std::string &uuid, OfficeUUIDType type);
    void GetGroupCallSeq(const std::string &group_uuid, OfficeGroupSeqCallMap &call_seq_map);
    void GetPerOrGroupPermissionDevUUIDSet(const OfficeDeviceAssignInfo &dev_info, OfficeUUIDSet &permission_dev_list);
    std::string GetCompanyUUID(const std::string &uuid, OfficeUUIDType type);
    // 获取设备有权限的员工列表和快递人员列表, 只有获取user相关的需要
    void GetOfficeDevPermissionAllUUIDListForUserFileType(const std::string& mac, const std::string& dev_uuid, OfficeUUIDSet &permission_per_uuid_list, OfficeUUIDSet &permission_delivery_uuid_list);
    OfficeUUIDSet GetOfficeDevPermissionUserUUIDList(const std::string& dev_uuid, bool is_muster_report_dev);
    OfficeUUIDSet GetOfficeDevPermissionDeliveryUUIDList(const std::string& dev_uuid, bool is_muster_report_dev);
    OfficeUUIDSet GetGroupUUIDList(const std::string &account_uuid, int role);
    //获取用户的权限组uuid列表
    OfficeUUIDSet GetOfficePerAgUUIDListCb(const std::string &account_uuid, int role);
    //获取用户的group列表
    OfficeUUIDSet GetOfficePerGroupUUIDListCb(const std::string &account_uuid, int role);
    //获取设备权限组uuid列表
    OfficeUUIDSet GetOfficeDevAgUUIDListCb(const std::string &dev_uuid);
    OfficeUUIDSet GetOfficeDeliveryAgUUIDListCb(const std::string &delivery_uuid);
    OfficeUUIDSet GetCompanyIndoorMngDevUUIDList(const std::string &company_uuid);   
    void GetNoBelongsToDevList(OfficeUUIDSet& dev_uuid_list, OfficeDevMap& dev_list);    
    OfficeUUIDSet GetPublicDoorList();
    std::string file_path_;
    dbinterface::MngSetting mng_setting_;
    OfficeInfoPtr g_office_info_;

    OfficeDevMap all_dev_list_;
    OfficeDevMap public_list_; 
    OfficeDevMap indoor_or_manage_dev_list_; //管理机或者室内机
    OfficeUUIDSet no_belongs_to_dev_uuid_list_;
    OfficeDevMap no_belongs_to_dev_list_;
    ProjectCompanyMap company_list_;
    GroupOfCompanyUUIDMap group_company_uuid_map_;
    GroupOfCompanyCompanyMap group_company_company_map_;
    GroupOfCompanyGroupMap group_company_group_map_;
    OfficeGroupSeqCallMap group_call_seq_map_; //在回调时候判断是否初始化过，没有初始化过再初始化
    OfficeAccountCompanyUUIDMap account_company_uuid_map_; //在回调时候判断是否初始化过，没有初始化过再初始化

    //获取权限组列表
    DataManager<AgDevInfoDevMap> dm_ag_dev_map_;
    DataManager<AgDevInfoUUIDMap> dm_ag_dev_uuid_map_;
    //获取group和权限组关系
    DataManager<GroupOfAgUUIDMap> dm_ag_group_map_;
    DataManager<GroupOfAgAgMap> dm_group_ag_map_;
    //获取用户和group的关系
    DataManager<GroupOfPerGroupMap> dm_group_of_per_group_map_;
    DataManager<GroupOfPerPerMap> dm_group_of_per_per_map_;    
    //获取admin和group的关系
    DataManager<GroupOfAdminPerMap> dm_group_of_admin_per_map_;
    DataManager<GroupOfAdminGroupMap> dm_group_of_admin_group_map_;
    //获取所有的用户列表
    DataManager<OfficeAccountMap> dm_account_map_;
    DataManager<OfficeAccountMateMap> dm_account_mate_map_;
    DataManager<OfficePersonnelMap> dm_personnel_map_;
    DataManager<OfficeAdminMap> dm_admin_map_;    
    DataManager<OfficeCompanyAdminMap> dm_company_admin_map_;
    //获取用户绑定的设备列表
    DataManager<OfficeDeviceAssignDevMap> dm_account_dev_dev_map_;
    DataManager<OfficeDeviceAssignPerMap> dm_account_dev_per_map_;
    DataManager<OfficeDeviceAssignGroupMap> dm_account_dev_group_map_;
    DataManager<OfficeDeviceAssignCompanyMap> dm_account_dev_company_map_;
    //权限组详细信息
    DataManager<AccessGroupUUIDMap> dm_ag_info_map_;

    //holiday相关
    DataManager<ProjectHolidayMap> dm_project_holiday_map_;
    DataManager<CompanyHolidayMap> dm_company_holiday_map_;    

    //delivery
    DataManager<OfficeDeliveryMap> dm_delivery_map_;
    DataManager<OfficeDeliveryUserMateMap> dm_delivery_mate_map_;
    //获取group和权限组关系
    DataManager<DeliveryOfAgAgMap> dm_ag_devlivery_map_;
    DataManager<DeliveryOfAgUUIDMap> dm_devlivery_ag_map_;       

    //用户开门方式
    UserAccessInfo user_access_;
    
    //设备的DoorList
    DevicesDoorInfoMap dev_door_info_map_;
    DevicesPublicDoorInfoMap pub_door_info_map_;

    //返潜回设备信息
    AntiPassbackDoorMap antipassback_door_info_map_;
};


#endif


