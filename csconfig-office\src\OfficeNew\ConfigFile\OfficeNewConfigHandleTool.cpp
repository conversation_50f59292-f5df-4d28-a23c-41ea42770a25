#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <assert.h>
#include <string>
#include "OfficeNew/ConfigFile/OfficeNewConfigHandle.h"
#include "BasicDefine.h"
#include "AKCSMsg.h"
#include "AkLogging.h"
#include "AkcsCommonDef.h"
#include "ConfigDef.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "OfficeNew/ConfigFile/OfficeNewConfigHandle.h"
#include "OfficeNew/ConfigFile/OfficeNewDevContact.h"
#include "OfficeNew/ConfigFile/OfficeNewDevSchedule.h"
#include "InnerUtil.h"
#include "util_judge.h"
#include "util_virtual_door.h"
#include "dbinterface/new-office/OfficeMusterReportSettingReaderList.h"


//查找设备包含多少ag
inline void GetOfficeDevAgList(const std::string &dev_uuid, const AgDevInfoDevMap &ag_dev_map, AgInfoList& ag_list)
{
    auto range = ag_dev_map.equal_range(dev_uuid);
    for (auto it = range.first; it != range.second; ++it) {
        ag_list.push_back(it->second);
    } 
}

//查权限组被多少group绑定
inline void GetOfficeAgGroupList(AgInfoList& ag_list, const GroupOfAgAgMap &ag_group_map, OfficeUUIDSet& group_set)
{
    for (const auto &it : ag_list) {
        auto ag = ag_group_map.equal_range(it.office_access_group_uuid);
        if (ag.first == ag.second)
        {
            AK_LOG_INFO << "GroupOfAgAgMap not found access group, ag_uuid:" << it.office_access_group_uuid;
        }
        for (auto it2 = ag.first; it2 != ag.second; ++it2) {
            group_set.insert(it2->second);
        }         
    }    
}

//获取设备有权限的personnel+admin列表
OfficeUUIDSet GetOfficeUserUUIDListByAgDev(const std::string &dev_uuid, const AgDevInfoDevMap &ag_dev_map, const GroupOfAgAgMap &ag_group_map, 
   const GroupOfPerGroupMap &group_of_per_group_map, const GroupOfAdminGroupMap &group_of_admin_group_map, OfficeUUIDSet &group_set)
{
    OfficeUUIDSet user_set;

    AgInfoList ag_list;
    GetOfficeDevAgList(dev_uuid, ag_dev_map, ag_list);
    if (ag_list.size() == 0)
    {
        AK_LOG_INFO << "devices access group is null : devuuid:" << dev_uuid;
        return user_set;
    }

    GetOfficeAgGroupList(ag_list, ag_group_map, group_set);
    if (group_set.size() == 0)
    {
        AK_LOG_INFO << "access group no bind group: devuuid:" << dev_uuid;
        return user_set;
    }

    for (const auto &group_uuid : group_set) {
        auto per = group_of_per_group_map.equal_range(group_uuid);
        
        for (auto it = per.first; it != per.second; ++it) {
            const std::string& per_uuid = it->second.personal_account_uuid;
            user_set.insert(per_uuid);
        }         

        auto admin = group_of_admin_group_map.equal_range(group_uuid);

        for (auto it = admin.first; it != admin.second; ++it) {
            const std::string& per_uuid = it->second.personal_account_uuid;
            user_set.insert(per_uuid);
        }
    }
    return std::move(user_set);
}

//获取设备有权限的admin列表
OfficeUUIDSet GetOfficeAdminUUIDListByAgDev(const std::string &dev_uuid, const AgDevInfoDevMap &ag_dev_map, const GroupOfAgAgMap &ag_group_map, 
   const GroupOfAdminGroupMap &group_of_admin_group_map, OfficeUUIDSet &group_set)
{
    OfficeUUIDSet admin_set;
    
    AgInfoList ag_list;
    GetOfficeDevAgList(dev_uuid, ag_dev_map, ag_list);
    if (ag_list.size() == 0)
    {
        AK_LOG_INFO << "devices access group is null : devuuid:" << dev_uuid;
        return admin_set;
    }

    GetOfficeAgGroupList(ag_list, ag_group_map, group_set);
    if (group_set.size() == 0)
    {
        AK_LOG_INFO << "access group no bind group: devuuid:" << dev_uuid;  
        return admin_set;
    }

    for (const auto &group_uuid : group_set) 
    {

        auto admin = group_of_admin_group_map.equal_range(group_uuid);
        
        for (auto it = admin.first; it != admin.second; ++it)
        {
            const std::string& per_uuid = it->second.personal_account_uuid;
            admin_set.insert(per_uuid);
        }
        
    }
    return std::move(admin_set);
}


//获取设备有权限的用户列表
OfficeUUIDSet GetOfficeDeliveryUUIDListByAgDev(const std::string &dev_uuid, const AgDevInfoDevMap &ag_dev_map, const DeliveryOfAgAgMap &ag_ag_map)
{
   //查权限组被多少delivery绑定
   OfficeUUIDSet delivery_set;

    AgInfoList ag_list;
    GetOfficeDevAgList(dev_uuid, ag_dev_map, ag_list);
    if (ag_list.size() == 0)
    {
        AK_LOG_INFO << "devices access group is null : devuuid:" << dev_uuid;
        return delivery_set;
    }

    for (const auto &it : ag_list) {
        auto ag = ag_ag_map.equal_range(it.office_access_group_uuid);
        
        for (auto it2 = ag.first; it2 != ag.second; ++it2) {
            delivery_set.insert(it2->second);
        }         
    } 

    return std::move(delivery_set);
}


//获取组关联的有权限的设备uuid列表
OfficeUUIDSet GetOfficeDevListByGroup(const std::string &group_uuid, const AgDevInfoUUIDMap &ag_dev_uuid_map, const GroupOfAgUUIDMap &group_ag_map)
{
   OfficeUUIDSet dev_set;

   OfficeUUIDSet ag_uuid_list;
   auto group_ag = group_ag_map.equal_range(group_uuid);
   for (auto it = group_ag.first; it != group_ag.second; ++it)
   {
       const std::string& ag_uuid = it->second;
       ag_uuid_list.insert(ag_uuid);
   } 

   for (const auto & uuid: ag_uuid_list)
   {
       auto ag_dev = ag_dev_uuid_map.equal_range(uuid);
       for (auto it = ag_dev.first; it != ag_dev.second; ++it)
       {
           const std::string& dev_uuid = it->second.devices_uuid;
           dev_set.insert(dev_uuid);
       }         
   } 
   return std::move(dev_set);
}

   
//查用户绑定了多少个group
inline void GetOfficePerGroupList(const std::string &account_uuid, const GroupOfPerPerMap& group_per_map, OfficeUUIDSet &group_uuid_list)
{
   auto range = group_per_map.equal_range(account_uuid);
   for (auto it = range.first; it != range.second; ++it) {
       AK_LOG_INFO << "GetOfficePerGroupList : account_uuid:" << account_uuid << " office group uuid:" << it->second.office_group_uuid;
       group_uuid_list.insert(it->second.office_group_uuid);
   }
}

//查group绑定了多少个权限组
inline void GetOfficeAgListByGroupList(OfficeUUIDSet group_uuid_set, const GroupOfAgUUIDMap & ag_group_ag_map, OfficeUUIDSet &ag_uuid_list)
{
    for (const auto &uuid : group_uuid_set) {
        auto ag = ag_group_ag_map.equal_range(uuid);
        AK_LOG_INFO << "GetOfficeAgListByGroupList : group uuid:" << uuid;
        for (auto it = ag.first; it != ag.second; ++it) {
            AK_LOG_INFO << "GetOfficeAgListByGroupList : group uuid:" << uuid << " access group uuid:" << it->second;
            ag_uuid_list.insert(it->second);
        }         
    } 
}


//获取用户有权限的公设备列表
OfficeUUIDSet GetOfficePubDevUUIDListByPerUUID(const std::string &account_uuid, const AgDevInfoUUIDMap &ag_uuid_map, const GroupOfAgUUIDMap &ag_group_ag_map, 
  const GroupOfPerPerMap &group_per_map)
{
    AK_LOG_INFO << "GetOfficePubDevUUIDListByPerUUID : account_uuid:" << account_uuid;
    OfficeUUIDSet dev_uuid_set;
    
    OfficeUUIDSet group_uuid_set;
    GetOfficePerGroupList(account_uuid, group_per_map, group_uuid_set);

    OfficeUUIDSet ag_uuid_list;
    GetOfficeAgListByGroupList(group_uuid_set, ag_group_ag_map, ag_uuid_list);


   for (const auto &ag_uuid : ag_uuid_list) {
       auto ag = ag_uuid_map.equal_range(ag_uuid);
       AK_LOG_INFO << "Get access group devices. ag uuid:" << ag_uuid;
       for (auto it = ag.first; it != ag.second; ++it)
       {
            AK_LOG_INFO << "Get access group devices. ag uuid:" << ag_uuid << " dev uuid:" << it->second.devices_uuid;    
           const std::string& dev_uuid = it->second.devices_uuid;
           dev_uuid_set.insert(dev_uuid);
       }         
   }
   return std::move(dev_uuid_set);
}

// 获取Device的RelayValue, CreateUserSchedule 这个函数有用到
// 这个函数CreateUserSchedule是不需要判断门是否过期的，原因因为如果有过期就不更新user了，这样新添加的用户不能用，
// 但是旧的user，在重置设备后userdetail 里面的relay是不判断是否过期的
void GetDevicesRelayValue(const std::string &dev_uuid, const DevicesDoorInfoMap &dev_door_info_map, int &relay_value, int &security_relay_value)
{
    auto it = dev_door_info_map.find(dev_uuid);
    if (it == dev_door_info_map.end())
    {
        return;
    }

    for (const auto &door_info : it->second)
    {
        // 如果door是开启的, 则添加relay value
        if (door_info.enable)
        {
            int relay_value_to_add = GetRelayValueByControlledRelay(door_info.controlled_relay);
            if (door_info.relay_type == DoorRelayType::RELAY)
            {   
                relay_value += relay_value_to_add;
            }
            else if (door_info.relay_type == DoorRelayType::SECURITY_RELAY)
            {
                security_relay_value += relay_value_to_add;
            }
        }
    }
    
    return;
}   

bool CheckDevUnitPermission(const OfficeDevPtr& own_dev, const OfficeDevPtr& other_dev)
{
    if (own_dev == nullptr || other_dev == nullptr)
    {
        AK_LOG_WARN << "CheckDevUnitPermission own_dev or other_dev is null";
        return false;
    }
    //公共区域设备拥有所有位置的权限
    if (strlen(own_dev->unit_uuid) == 0 || strlen(other_dev->unit_uuid) == 0)
    {
        return true;
    }
    //楼栋相同的设备可以访问
    if (strncmp(own_dev->unit_uuid, other_dev->unit_uuid, strlen(own_dev->unit_uuid)) == 0)
    {
        return true;
    }
    return false;
}