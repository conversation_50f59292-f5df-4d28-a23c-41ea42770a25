#ifndef __OFFICE_CONFIG_NEW_CONTROL_HANDLE_TOOL__
#define __OFFICE_CONFIG_NEW_CONTROL_HANDLE_TOOL__

#include "BasicDefine.h"
#include "AKCSMsg.h"
#include "AkLogging.h"
#include "OfficeUserAccessInfo.h"
#include "InnerEnum.h"
#include "InnerDbDef.h"
#include "OfficeNew/ConfigFile/OfficeNewDevConfig.h"
#include "OfficeNew/ConfigFile/OfficeNewDevCommon.h"
#include "OfficeNew/ConfigFile/OfficeNewDevUser.h"
#include "dbinterface/new-office/DevicesDoorList.h"

//获取设备有权限的personnel+admin列表
OfficeUUIDSet GetOfficeUserUUIDListByAgDev(const std::string &dev_uuid, const AgDevInfoDevMap &ag_dev_map, const GroupOfAgAgMap &ag_group_map, 
   const GroupOfPerGroupMap &group_of_per_group_map, const GroupOfAdminGroupMap &group_of_admin_group_map, OfficeUUIDSet &group_set);

//获取设备有权限的admin列表
OfficeUUIDSet GetOfficeAdminUUIDListByAgDev(const std::string &dev_uuid, const AgDevInfoDevMap &ag_dev_map, const GroupOfAgAgMap &ag_group_map, 
   const GroupOfAdminGroupMap &group_of_admin_group_map, OfficeUUIDSet &group_set);

//获取用户有权限的公设备列表
OfficeUUIDSet GetOfficePubDevUUIDListByPerUUID(const std::string &account_uuid, const AgDevInfoUUIDMap &ag_uuid_map, const GroupOfAgUUIDMap &ag_group_ag_map, 
  const GroupOfPerPerMap &group_per_map);

//获取组关联的设备uuid列表
OfficeUUIDSet GetOfficeDevListByGroup(const std::string &group_uuid, const AgDevInfoUUIDMap &ag_dev_uuid_map, const GroupOfAgUUIDMap &group_ag_map);

OfficeUUIDSet GetOfficeDeliveryUUIDListByAgDev(const std::string &dev_uuid, const AgDevInfoDevMap &ag_dev_map, const DeliveryOfAgAgMap &ag_ag_map);


//获取用户的权限组uuid列表
OfficeUUIDSet GetOfficePerAgUUIDList(const std::string &account_uuid, const GroupOfAgUUIDMap &ag_group_ag_map, const GroupOfPerPerMap &group_per_map);
//获取设备权限组uuid列表
OfficeUUIDSet GetOfficeDevAgUUIDList(const std::string &dev_uuid, const AgDevInfoDevMap &ag_dev_map);

// 获取Device的RelayValue
void GetDevicesRelayValue(const std::string &dev_uuid, const DevicesDoorInfoMap &dev_door_info_map, int &relay_value, int &security_relay_value);

// 校验设备之间的楼栋权限，用于联系人下发控制
bool CheckDevUnitPermission(const OfficeDevPtr& own_dev, const OfficeDevPtr& other_dev);    
#endif


