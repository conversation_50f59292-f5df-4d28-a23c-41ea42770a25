#include <sstream>
#include "OfficeNew/ConfigFile/OfficeNewContactInfo.h"
#include <string.h>

void AccountContactInfo::SetContactDisplayOptions(ContactDevType contact_dev_type, OfficeGroupDisplayType group_display_type)
{
    if (account_.role == ACCOUNT_ROLE_OFFICE_NEW_ADMIN)
    {
        SetAdminContactDisplayOptions(contact_dev_type);
    }
    else if (account_.role == ACCOUNT_ROLE_OFFICE_NEW_PER)
    {
        SetPersonnelContactDisplayOptions(contact_dev_type);
    }

    // 根据group选项设置联系人展示开关
    SetContactDisplayOptionsGroupRelated(group_display_type);

    return;
}


void AccountContactInfo::SetAdminContactDisplayOptions(ContactDevType contact_dev_type)
{
    if (admin_.app_status == (int)AdminAppStatus::DISABLE)
    {
        SetNoDisplayAccountContact();   
        SetNoIntercome();
    }

    return;
}

void AccountContactInfo::SetPersonnelContactDisplayOptions(ContactDevType contact_dev_type)
{
    if (!personnel_.is_smart_plus_intercom)
    {
        SetNoDisplayAccountContact();
        SetNoIntercome();
    }

    if (!personnel_.is_display_in_directory)
    {
        if (contact_dev_type == ContactDevType::DOOR)
        {
            SetNoDisplayDevContact();
            SetNoDisplayAccountContact();
        }
    }

    return;
}

void AccountContactInfo::SetContactDisplayOptionsGroupRelated(OfficeGroupDisplayType group_display_type)
{
    if (group_display_type == OfficeGroupDisplayType::NOT_DISPLAY || group_display_type == OfficeGroupDisplayType::SHOW_GROUP)
    {
        AK_LOG_INFO << "personal display is no show in list. account_uuid:" << account_.uuid << " GroupDisplayType not display type<1:not 2:group 3:per> type=" << group_display_type << ". personnel display in directory:" << DisplayDevContact();
        SetNoDisplayAccountContact();
        SetNoDisplayDevContact();
    }
    else if (group_display_type == OfficeGroupDisplayType::SHOW_PERSONNEL)
    {
        SetIsUnitTiledDisplay();
    }
}

int AccountContactInfo::GetCallType()
{
    if (account_.role == ACCOUNT_ROLE_OFFICE_NEW_ADMIN)
    {
        return admin_.call_type;
    }
    else if (account_.role == ACCOUNT_ROLE_OFFICE_NEW_PER)
    {
        return personnel_.call_type;
    }

    return 0;
}