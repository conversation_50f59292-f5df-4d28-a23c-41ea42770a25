#ifndef __New_OFFICE_CONTACT_INFO_H__
#define __New_OFFICE_CONTACT_INFO_H__
#include <string>
#include <map>
#include "AKCSMsg.h"
#include "OfficeNew/ConfigFile/OfficeNewConfigHandle.h"
#include "ContactCommon.h"
#include "OfficeNewDevContactCallSeq.h"
#include "AkcsCommonDef.h"

// 枚举类型表示子类的类型
enum class ContactTypeInfo
{
    GROUP,
    ACCOUNT,
    PUBINFO,
    PUB_MANAGE_DEV //公共的管理设备(GuardPhone/Indoor未选择belongs to的情况)
};

class OfficeNewUserInfo {
public:

    OfficeNewUserInfo(const OfficePersonnelInfo& personnel_info):personnel_(personnel_info) {}
    OfficeNewUserInfo(const OfficeAdminInfo& admin_info):admin_(admin_info) {} 


    int GetUserRole() const {
        return role_;
    }

private:
    OfficePersonnelInfo personnel_;
    OfficeAdminInfo admin_;
    int role_;
};

class ContactInfo {
public:
    ContactInfo(ContactTypeInfo type) : type_(type) {}
    virtual ~ContactInfo() {}


    ContactTypeInfo GetType() const {
        return type_;
    }

private:
    ContactTypeInfo type_;
};

class AccountContactInfo;
using AccountContactInfoPtr = std::shared_ptr<AccountContactInfo>;
class AccountContactInfo: public ContactInfo {
public:
    AccountContactInfo() : ContactInfo(ContactTypeInfo::ACCOUNT) {}

	void SetAccountInfo(const OfficeAccount& account, const OfficePersonnelInfo& personnel)
    {
        account_ = account;
        personnel_ = personnel;
    }
    
    void SetAccountInfo(const OfficeAccount& account, const OfficeAdminInfo& admin)
    {
        account_ = account;
        admin_ = admin;
    }

    void AddDev(const OfficeDevPtr &dev)
    {
        dev_map_.insert(std::make_pair(dev->uuid, dev));
    }
    
    void SetIsUnitTiledDisplay()
    {
        is_unit_tiled_display_ = true;
    }
    
    bool UnitTiledDisplay()
    {
        return is_unit_tiled_display_;
    }
    
    bool DisplayAccountContact()
    {
        return is_display_account;
    } 

    void SetNoDisplayAccountContact()
    {
        is_display_account = false;
    } 

    void SetNoIntercome()
    {
        is_account_have_intercome = false;
    }
    
    bool EnableIntercome()
    {
        return is_account_have_intercome;
    } 

    bool DisplayDevContact()
    {
        return is_display_dev;
    } 

    void SetNoDisplayDevContact()
    {
        is_display_dev = false;
    } 

    void SetGroupUUID(const std::string &group_uuid)
    {
        group_uuid_ = group_uuid;
    }    

    std::string GetPersonnelCompanyUUID()
    {
        return personnel_.office_company_uuid;
    }

    void PutIndoorAdminContact(const AccountContactInfoPtr& indoor_admin_contact)
    {
        indoor_admin_contacts_.push_back(indoor_admin_contact);
    }


    void SetContactDisplayOptions(ContactDevType contact_dev_type, OfficeGroupDisplayType group_display_type);
    void SetContactDisplayOptionsGroupRelated(OfficeGroupDisplayType group_display_type);

    int GetCallType();

	~AccountContactInfo(){}

    OfficeAccount account_; // PersonnalAccount
    std::map<std::string, OfficeDevPtr> dev_map_;
    std::string group_uuid_; //这个人员所属的groupuuid，一个人会有多个group 会有多条记录
    std::vector<AccountContactInfoPtr> indoor_admin_contacts_; //室内机的联系人Group中要有admin contact
private:

    void SetPersonnelContactDisplayOptions(ContactDevType contact_dev_type);
    void SetAdminContactDisplayOptions(ContactDevType contact_dev_type);

    OfficePersonnelInfo personnel_; // OfficePersonnel
    OfficeAdminInfo admin_; // OfficeAdmin

    bool is_unit_tiled_display_ = false;
    bool is_display_account = true;
    bool is_account_have_intercome = true;
    bool is_display_dev = true;
};


class GroupContactInfo : public ContactInfo {
public:

        
    GroupContactInfo() : ContactInfo(ContactTypeInfo::GROUP) {}
    
    void AddAccountAppInfo(const AccountContactInfoPtr& account_info, CallSeqType type, int call_order)
    {
        account_app_list_.insert(std::make_pair(account_info->account_.uuid, account_info));
        account_callseq_list_.insert(std::make_pair(account_info->account_.uuid, GroupCallSeq(type, call_order)));
    }


    void AddDev(const OfficeDevPtr &dev, int call_order)
    {
        dev_callseq_list_.push_back(std::make_pair(dev, call_order));
    }
    
    void AddDev(const OfficeDevPtr &dev)
    {
        dev_callseq_list_.push_back(std::make_pair(dev, dev_seq_disable_));
    }

    bool IsNeedDevSeq(int order)
    {
        return order != dev_seq_disable_;
    }
    
    void SetUUID(const std::string &uuid)
    {
        group_uuid_ = uuid;
    }    
    
	~GroupContactInfo(){}

    int dev_seq_disable_ = 9999;
    std::string group_uuid_;
    
    std::vector<std::pair<OfficeDevPtr, int>> dev_callseq_list_;
    //TODO：这里放Ptr 主要是和AccountContactInfoList里面的ptr使用统一起来。
    std::map<std::string, AccountContactInfoPtr> account_app_list_;
    GroupSeqCallSeqMap account_callseq_list_;
};

class PubinfoContactInfo: public ContactInfo {
public:
    PubinfoContactInfo() : ContactInfo(ContactTypeInfo::PUBINFO) {}
    
    void AddDev(const OfficeDevPtr &dev)
    {
        dev_map_.insert(std::make_pair(dev->uuid, dev));
    }
    
	~PubinfoContactInfo(){}

    std::map<std::string, OfficeDevPtr> dev_map_;
};

class PubManageDevContactInfo: public ContactInfo {
public:
    PubManageDevContactInfo() : ContactInfo(ContactTypeInfo::PUB_MANAGE_DEV) {}
    
    void AddDev(const OfficeDevPtr &dev)
    {
        dev_info_ = dev;
    }

    void SetCompanyUUID(const std::string &uuid)
    {
        company_uuid_ = uuid;
    } 
    
	~PubManageDevContactInfo(){}

    std::string company_uuid_;    
    OfficeDevPtr dev_info_;
};


using ContactInfoList = std::vector<std::shared_ptr<ContactInfo>>;
using PubinfoContactInfoPtr = std::shared_ptr<PubinfoContactInfo>;
using GroupContactInfoPtr = std::shared_ptr<GroupContactInfo>;
using PubManageDevContactInfoPtr = std::shared_ptr<PubManageDevContactInfo>;



#endif


