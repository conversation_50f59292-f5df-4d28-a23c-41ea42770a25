#ifndef __NEW_OFFICE_DEV_COMMON_CONFIG_H__
#define __NEW_OFFICE_DEV_COMMON_CONFIG_H__
#include <string>
#include "AKCSMsg.h"


class DevCommonSetting
{
public:
    DevCommonSetting()
    {
        mng_rtsp_type_ = 0;
        mng_sip_type_ = 0;
        rtp_confuse_ = 0;
    }
	DevCommonSetting(  int mng_sip_type, int rtp_confuse, int mng_rtsp_type)
    {
        mng_sip_type_ = mng_sip_type;
        rtp_confuse_ = rtp_confuse;
        mng_rtsp_type_ = mng_rtsp_type;
    }

	~DevCommonSetting()
    {
        
    }  

    int MngRtspType()
    {
        return mng_rtsp_type_; 
    }   
    
    int MngSipType()
    {
        return mng_rtsp_type_;
    }
    
    int MngRtpConfuse()
    {
        return rtp_confuse_;
    }
    
private:
    int mng_rtsp_type_;
    int mng_sip_type_;
    int rtp_confuse_;   
};

#endif 
