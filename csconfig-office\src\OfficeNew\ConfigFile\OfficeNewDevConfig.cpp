#include <sstream>
#include "OfficeNew/ConfigFile/OfficeNewDevConfig.h"
#include <string.h>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "ConfigDef.h" 
#include "json/json.h"
#include "util.h"
#include "util_relay.h"
#include "util_cstring.h"
#include "util_judge.h"
#include "util_string.h"
#include "util_virtual_door.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AES256.h"
#include "AkcsMonitor.h"
#include "encrypt/Md5.h"
#include "AkcsCommonDef.h"
#include "ShadowMng.h"
#include "WriteFileControl.h"
#include "ConfigCommon.h"
#include "dbinterface/PubDevMngList.h"
#include "dbinterface/AccessGroupDB.h"
#include "dbinterface/new-office/OfficeMusterReportSettingReaderList.h"
#include "OfficeNewConfigHandleTool.h"

extern CSCONFIG_CONF gstCSCONFIGConf;

using AntiPassBackRelayConfig = std::map<std::string, std::string>;
using SensorAlarmInputConfig = std::map<std::string, std::string>;
using ReaderTypeToReaderNameMap = std::map<std::string, std::string>;
using RelayReaderTypeMap = std::map<DoorReaderType, std::string>;
using RelayReaderConfigMap = std::map<std::string, std::string>;
using ExitButtonInputConfigMap = std::map<std::string, std::string>;
using Rs485ConnectTypeMap = std::map<DoorReaderRs485ConnectType, std::string>;
using Rs485ConnectAddressMap = std::map<std::string, std::string>;
using RelayRs485ConnectTypeMap = std::map<std::string, std::string>;
using InputEnableConfigMap = std::map<std::string, std::string>;
using BreakInAlarmInputConfigMap = std::map<std::string, std::string>;

static const AntiPassBackRelayConfig KAntipassbackRelayConfig = {
    {"A", CONFIG_RELAY_ANTIPASSBACK1}, 
    {"B", CONFIG_RELAY_ANTIPASSBACK2}, 
    {"C", CONFIG_RELAY_ANTIPASSBACK3}, 
    {"D", CONFIG_RELAY_ANTIPASSBACK4}
};

static const AntiPassBackRelayConfig KAntipassbackSecurityRelayConfig = {
    {"A", CONFIG_SECURITY_RELAY_ANTIPASSBACK1}, 
    {"B", CONFIG_SECURITY_RELAY_ANTIPASSBACK2}
};

static const SensorAlarmInputConfig KDoorHoldOpenAlarmInputEnableConfig = {
    {"A", CONFIG_INPUT_DOOROPENEDA_ENABLED},
    {"B", CONFIG_INPUT_DOOROPENEDB_ENABLED},
    {"C", CONFIG_INPUT_DOOROPENEDC_ENABLED},
    {"D", CONFIG_INPUT_DOOROPENEDD_ENABLED}
};

static const SensorAlarmInputConfig KDoorHoldOpenAlarmInputTimeoutConfig = {
    {"A", CONFIG_INPUT_DOOROPENEDA_TIMEOUT},
    {"B", CONFIG_INPUT_DOOROPENEDB_TIMEOUT},
    {"C", CONFIG_INPUT_DOOROPENEDC_TIMEOUT},
    {"D", CONFIG_INPUT_DOOROPENEDD_TIMEOUT}
};

static const SensorAlarmInputConfig KDoorHoldOpenAlarmInputEnableConfig2 = {
    {"A", CONFIG_INPUTA_DOOROPENED_ENABLED},
    {"B", CONFIG_INPUTB_DOOROPENED_ENABLED},
    {"C", CONFIG_INPUTC_DOOROPENED_ENABLED},
    {"D", CONFIG_INPUTD_DOOROPENED_ENABLED}
};

static const SensorAlarmInputConfig KDoorHoldOpenAlarmInputTimeoutConfig2 = {
    {"A", CONFIG_INPUTA_DOOROPENED_TIMEOUT},
    {"B", CONFIG_INPUTB_DOOROPENED_TIMEOUT},
    {"C", CONFIG_INPUTC_DOOROPENED_TIMEOUT},
    {"D", CONFIG_INPUTD_DOOROPENED_TIMEOUT}
};

static const SensorAlarmInputConfig KA094DoorHoldOpenAlarmInputEnableConfig = {
    {"magneticA", CONFIG_MAGNETICA_DOOROPENED_ENABLED},
    {"magneticB", CONFIG_MAGNETICB_DOOROPENED_ENABLED},
    {"magneticC", CONFIG_MAGNETICC_DOOROPENED_ENABLED},
    {"magneticD", CONFIG_MAGNETICD_DOOROPENED_ENABLED}
};

static const SensorAlarmInputConfig KA094DoorHoldOpenAlarmInputTimeoutConfig = {
    {"magneticA", CONFIG_MAGNETICA_DOOROPENED_TIMEOUT},
    {"magneticB", CONFIG_MAGNETICB_DOOROPENED_TIMEOUT},
    {"magneticC", CONFIG_MAGNETICC_DOOROPENED_TIMEOUT},
    {"magneticD", CONFIG_MAGNETICD_DOOROPENED_TIMEOUT}
};

static const RelayReaderTypeMap KRelayReaderTypeMap = {
    {DoorReaderType::INTERNAL, "Internal"},
    {DoorReaderType::WIEGAND, "Wiegand"},
    {DoorReaderType::RS485, "RS485"}
};

static const RelayReaderConfigMap KRelayEntryReaderConfigMap = {
    {"A", CONFIG_RELAY_ENTRY_READER_A},
    {"B", CONFIG_RELAY_ENTRY_READER_B},
    {"C", CONFIG_RELAY_ENTRY_READER_C},
    {"D", CONFIG_RELAY_ENTRY_READER_D}
};

static const RelayReaderConfigMap KRelayExitReaderConfigMap = {
    {"A", CONFIG_RELAY_EXIT_READER_A},
    {"B", CONFIG_RELAY_EXIT_READER_B},
    {"C", CONFIG_RELAY_EXIT_READER_C},
    {"D", CONFIG_RELAY_EXIT_READER_D}
};

static const RelayReaderConfigMap KSecurityRelayEntryReaderConfigMap = {
    {"A", CONFIG_SECURITY_RELAY_ENTRY_READER_A},
    {"B", CONFIG_SECURITY_RELAY_ENTRY_READER_B}
};

static const RelayReaderConfigMap KSecurityRelayExitReaderConfigMap = {
    {"A", CONFIG_SECURITY_RELAY_EXIT_READER_A},
    {"B", CONFIG_SECURITY_RELAY_EXIT_READER_B}
};

static const ExitButtonInputConfigMap KExitButtonInputNRelayIDConfigMap = {
    {"A", CONFIG_EXIT_BUTTON_INPUT_A_RELAYID},
    {"B", CONFIG_EXIT_BUTTON_INPUT_B_RELAYID},
    {"C", CONFIG_EXIT_BUTTON_INPUT_C_RELAYID},
    {"D", CONFIG_EXIT_BUTTON_INPUT_D_RELAYID}
};

static const ExitButtonInputConfigMap KA094ExitButtonInputRelayConfigMap = {
    {"A", CONFIG_INPUT_INPUTA_RELAY},
    {"B", CONFIG_INPUT_INPUTB_RELAY},
    {"C", CONFIG_INPUT_INPUTC_RELAY},
    {"D", CONFIG_INPUT_INPUTD_RELAY}
};

static const ExitButtonInputConfigMap KA094InputEnableConfigMap = {
    {"A", CONFIG_INPUT_INPUTA_ENABLE},
    {"B", CONFIG_INPUT_INPUTB_ENABLE},
    {"C", CONFIG_INPUT_INPUTC_ENABLE},
    {"D", CONFIG_INPUT_INPUTD_ENABLE}
};
static const InputEnableConfigMap KInputEnableConfigMap = {
    {"A", CONFIG_INPUTA_ENABLE},
    {"B", CONFIG_INPUTB_ENABLE},
    {"C", CONFIG_INPUTC_ENABLE},
    {"D", CONFIG_INPUTD_ENABLE}
};

static const InputEnableConfigMap KInputEnable2ConfigMap = {
    {"A", CONFIG_INPUT_ENABLE_A},
    {"B", CONFIG_INPUT_ENABLE_B},
    {"C", CONFIG_INPUT_ENABLE_C},
    {"D", CONFIG_INPUT_ENABLE_D}
};

static const BreakInAlarmInputConfigMap KBreakInAlarmInputEnableConfigMap = {
    {"A", CONFIG_INPUT_A_BREAKIN_INTRUSION},
    {"B", CONFIG_INPUT_B_BREAKIN_INTRUSION},
    {"C", CONFIG_INPUT_C_BREAKIN_INTRUSION},
    {"D", CONFIG_INPUT_D_BREAKIN_INTRUSION}
};

static const BreakInAlarmInputConfigMap KBreakInAlarmInputAndroidConfigMap = {
    {"A", CONFIG_INPUT_BREAKIN_INTRUSION_A},
    {"B", CONFIG_INPUT_BREAKIN_INTRUSION_B},
    {"C", CONFIG_INPUT_BREAKIN_INTRUSION_C},
    {"D", CONFIG_INPUT_BREAKIN_INTRUSION_D}
};

static const BreakInAlarmInputConfigMap KBreakInAlarmInputA094ConfigMap = {
    {"magneticA", CONFIG_INPUT_BREAKIN_INTRUSION_F},
    {"magneticB", CONFIG_INPUT_BREAKIN_INTRUSION_G},
    {"magneticC", CONFIG_INPUT_BREAKIN_INTRUSION_H},
    {"magneticD", CONFIG_INPUT_BREAKIN_INTRUSION_I}
};

static const InputEnableConfigMap KA094DoorStatusInputEnableConfigMap = {
    {"magneticA", CONFIG_MAGNETIC_A_ENABLE},
    {"magneticB", CONFIG_MAGNETIC_B_ENABLE},
    {"magneticC", CONFIG_MAGNETIC_C_ENABLE},
    {"magneticD", CONFIG_MAGNETIC_D_ENABLE}
}; 

std::string NewOfficeDevConfig::GetAlwaysRelaySchedule(const std::string &dev_uuid, const std::string& relay_ag_uuids, unsigned int now_relay_index)
{
    std::stringstream valid_schedule;
    if(now_relay_index == 0)
    {
        AK_LOG_INFO << "pass relay_index wrong, val is 0";
        return valid_schedule.str();
    }

    std::set<std::string> ag_uuids;
    SplitString(relay_ag_uuids, ",", ag_uuids);
    for(const auto &ag_uuid : ag_uuids) 
    {
        //先判断权限组是否 包含这个设备
        auto range = ag_dev_map_.equal_range(dev_uuid);
        bool ag_valid = false;
        int relay = 0;
        for (auto it = range.first; it != range.second; ++it) {
            if (strcmp(ag_uuid.c_str(), it->second.office_access_group_uuid) == 0)
            {
                ag_valid = true;
                relay = it->second.relay;
            }
        }
        if (!ag_valid)
        {
            continue;
        }

        //判断当前relay是否在权限组中
        if ((relay >> (now_relay_index - 1)) & 1)
        {
            //获取权限组时间的ID
            auto range = ag_info_map_.equal_range(ag_uuid);
            for (auto it = range.first; it != range.second; ++it) {
                valid_schedule << it->second.id;
                valid_schedule << "/";

            }            
        }          
    }
    return valid_schedule.str();
}

void NewOfficeDevConfig::ResetDoorCommonConfig(std::stringstream &config)
{
    config << CONFIG_RELAY_DMTF_OPTION << 0 << "\n";    
    
    // InputEnable配置
    // config << CONFIG_INPUTA_ENABLE << 0 << "\n";
    // config << CONFIG_INPUTB_ENABLE << 0 << "\n";
    // config << CONFIG_INPUTC_ENABLE << 0 << "\n";
    // config << CONFIG_INPUTD_ENABLE << 0 << "\n";

    // config << CONFIG_INPUT_ENABLE_A << 0 << "\n";
    // config << CONFIG_INPUT_ENABLE_B << 0 << "\n";
    // config << CONFIG_INPUT_ENABLE_C << 0 << "\n";
    // config << CONFIG_INPUT_ENABLE_D << 0 << "\n";

    // A094 DoorStatus Input Enable配置
    config << CONFIG_MAGNETIC_A_ENABLE << 0 << "\n";
    config << CONFIG_MAGNETIC_B_ENABLE << 0 << "\n";
    config << CONFIG_MAGNETIC_C_ENABLE << 0 << "\n";
    config << CONFIG_MAGNETIC_D_ENABLE << 0 << "\n";    

    // A094 ExitButton Input Enable配置
    // config << CONFIG_INPUT_INPUTA_ENABLE << 0 << "\n";
    // config << CONFIG_INPUT_INPUTB_ENABLE << 0 << "\n";
    // config << CONFIG_INPUT_INPUTC_ENABLE << 0 << "\n";
    // config << CONFIG_INPUT_INPUTD_ENABLE << 0 << "\n";

    // A094 ExitButton Input Relay配置
    // config << CONFIG_INPUT_INPUTA_RELAY << 0 << "\n";
    // config << CONFIG_INPUT_INPUTB_RELAY << 0 << "\n";
    // config << CONFIG_INPUT_INPUTC_RELAY << 0 << "\n";
    // config << CONFIG_INPUT_INPUTD_RELAY << 0 << "\n";

    // 强闯配置
    
    config << CONFIG_INPUT_BREAKIN_INTRUSION_F << 0 << "\n";
    config << CONFIG_INPUT_BREAKIN_INTRUSION_G << 0 << "\n";
    config << CONFIG_INPUT_BREAKIN_INTRUSION_H << 0 << "\n";
    config << CONFIG_INPUT_BREAKIN_INTRUSION_I << 0 << "\n";

    config << CONFIG_INPUT_BREAKIN_INTRUSION_A << 0 << "\n";
    config << CONFIG_INPUT_BREAKIN_INTRUSION_B << 0 << "\n";
    config << CONFIG_INPUT_BREAKIN_INTRUSION_C << 0 << "\n";
    config << CONFIG_INPUT_BREAKIN_INTRUSION_D << 0 << "\n";

    config << CONFIG_INPUT_A_BREAKIN_INTRUSION << 0 << "\n";
    config << CONFIG_INPUT_B_BREAKIN_INTRUSION << 0 << "\n";
    config << CONFIG_INPUT_C_BREAKIN_INTRUSION << 0 << "\n";
    config << CONFIG_INPUT_D_BREAKIN_INTRUSION << 0 << "\n";

    // 内开门配置
    // config << CONFIG_EXIT_BUTTON_INPUT_A_RELAYID << 0 << "\n";
    // config << CONFIG_EXIT_BUTTON_INPUT_B_RELAYID << 0 << "\n";
    // config << CONFIG_EXIT_BUTTON_INPUT_C_RELAYID << 0 << "\n";
    // config << CONFIG_EXIT_BUTTON_INPUT_D_RELAYID << 0 << "\n";

    // Relay Reader配置
    config << CONFIG_RELAY_ENTRY_READER_A << "\n";
    config << CONFIG_RELAY_ENTRY_READER_B << "\n";
    config << CONFIG_RELAY_ENTRY_READER_C << "\n";
    config << CONFIG_RELAY_ENTRY_READER_D << "\n";
    config << CONFIG_RELAY_EXIT_READER_A << "\n";
    config << CONFIG_RELAY_EXIT_READER_B << "\n";
    config << CONFIG_RELAY_EXIT_READER_C << "\n";
    config << CONFIG_RELAY_EXIT_READER_D << "\n";

    // Security Relay Reader配置
    config << CONFIG_SECURITY_RELAY_ENTRY_READER_A << "\n";
    config << CONFIG_SECURITY_RELAY_ENTRY_READER_B << "\n";
    config << CONFIG_SECURITY_RELAY_EXIT_READER_A << "\n";
    config << CONFIG_SECURITY_RELAY_EXIT_READER_B << "\n";

    // 门常开告警
    kv_.push_back(KVPair(CONFIG_INPUT_DOOROPENEDA_ENABLED, 0));
    kv_.push_back(KVPair(CONFIG_INPUT_DOOROPENEDB_ENABLED, 0));
    kv_.push_back(KVPair(CONFIG_INPUT_DOOROPENEDC_ENABLED, 0));
    kv_.push_back(KVPair(CONFIG_INPUT_DOOROPENEDD_ENABLED, 0));

    kv_.push_back(KVPair(CONFIG_INPUTA_DOOROPENED_ENABLED, 0));
    kv_.push_back(KVPair(CONFIG_INPUTB_DOOROPENED_ENABLED, 0));
    kv_.push_back(KVPair(CONFIG_INPUTC_DOOROPENED_ENABLED, 0));
    kv_.push_back(KVPair(CONFIG_INPUTD_DOOROPENED_ENABLED, 0));
    
    kv_.push_back(KVPair(CONFIG_MAGNETICA_DOOROPENED_ENABLED, 0));
    kv_.push_back(KVPair(CONFIG_MAGNETICB_DOOROPENED_ENABLED, 0));
    kv_.push_back(KVPair(CONFIG_MAGNETICC_DOOROPENED_ENABLED, 0));
    kv_.push_back(KVPair(CONFIG_MAGNETICD_DOOROPENED_ENABLED, 0));

    // 返潜回配置
    kv_.push_back(KVPair(CONFIG_RELAY_ANTIPASSBACK1, (int)AntiPassbackDoorType::NORMAL));
    kv_.push_back(KVPair(CONFIG_RELAY_ANTIPASSBACK2, (int)AntiPassbackDoorType::NORMAL));
    kv_.push_back(KVPair(CONFIG_RELAY_ANTIPASSBACK3, (int)AntiPassbackDoorType::NORMAL));
    kv_.push_back(KVPair(CONFIG_RELAY_ANTIPASSBACK4, (int)AntiPassbackDoorType::NORMAL));
    kv_.push_back(KVPair(CONFIG_SECURITY_RELAY_ANTIPASSBACK1, (int)AntiPassbackDoorType::NORMAL));
    kv_.push_back(KVPair(CONFIG_SECURITY_RELAY_ANTIPASSBACK2, (int)AntiPassbackDoorType::NORMAL));
    return;
}

void NewOfficeDevConfig::ResetDoorInputEnableConfig(std::stringstream &config)
{
    // A094 ExitButton Input Enable配置
    config << CONFIG_INPUT_INPUTA_ENABLE << 0 << "\n";
    config << CONFIG_INPUT_INPUTB_ENABLE << 0 << "\n";
    config << CONFIG_INPUT_INPUTC_ENABLE << 0 << "\n";
    config << CONFIG_INPUT_INPUTD_ENABLE << 0 << "\n";

    // A094 ExitButton Input Relay配置
    config << CONFIG_INPUT_INPUTA_RELAY << 0 << "\n";
    config << CONFIG_INPUT_INPUTB_RELAY << 0 << "\n";
    config << CONFIG_INPUT_INPUTC_RELAY << 0 << "\n";
    config << CONFIG_INPUT_INPUTD_RELAY << 0 << "\n";

    // 内开门配置
    config << CONFIG_EXIT_BUTTON_INPUT_A_RELAYID << 0 << "\n";
    config << CONFIG_EXIT_BUTTON_INPUT_B_RELAYID << 0 << "\n";
    config << CONFIG_EXIT_BUTTON_INPUT_C_RELAYID << 0 << "\n";
    config << CONFIG_EXIT_BUTTON_INPUT_D_RELAYID << 0 << "\n";

    config << CONFIG_INPUTA_ENABLE << 0 << "\n";
    config << CONFIG_INPUTB_ENABLE << 0 << "\n";
    config << CONFIG_INPUTC_ENABLE << 0 << "\n";
    config << CONFIG_INPUTD_ENABLE << 0 << "\n";

    config << CONFIG_INPUT_ENABLE_A << 0 << "\n";
    config << CONFIG_INPUT_ENABLE_B << 0 << "\n";
    config << CONFIG_INPUT_ENABLE_C << 0 << "\n";
    config << CONFIG_INPUT_ENABLE_D << 0 << "\n";

}

void NewOfficeDevConfig::WriteDoorRelayReaderConfig(const DevicesDoorInfo& door_info)
{
    DoorReaderInfoList door_reader_list = door_info.door_reader_list;

    // reader config key
    std::string relay_entry_reader_config_key;
    std::string relay_exit_reader_config_key;
    std::string security_relay_entry_reader_config_key;
    std::string security_relay_exit_reader_config_key;
    if (KRelayEntryReaderConfigMap.find(door_info.controlled_relay) != KRelayEntryReaderConfigMap.end())
    {
        relay_entry_reader_config_key = KRelayEntryReaderConfigMap.at(door_info.controlled_relay);
    }
    if (KRelayExitReaderConfigMap.find(door_info.controlled_relay) != KRelayExitReaderConfigMap.end())
    {
        relay_exit_reader_config_key = KRelayExitReaderConfigMap.at(door_info.controlled_relay);
    }
    if (KSecurityRelayEntryReaderConfigMap.find(door_info.controlled_relay) != KSecurityRelayEntryReaderConfigMap.end())
    {
        security_relay_entry_reader_config_key = KSecurityRelayEntryReaderConfigMap.at(door_info.controlled_relay);
    }
    if (KSecurityRelayExitReaderConfigMap.find(door_info.controlled_relay) != KSecurityRelayExitReaderConfigMap.end())
    {
        security_relay_exit_reader_config_key = KSecurityRelayExitReaderConfigMap.at(door_info.controlled_relay);
    }
    
    // reader config value
    std::stringstream relay_entry_reader_config_value;
    std::stringstream relay_exit_reader_config_value;
    std::stringstream security_relay_entry_reader_config_value;
    std::stringstream security_relay_exit_reader_config_value;
    for (const auto& door_reader : door_info.door_reader_list)
    {
        std::stringstream reader_config_value;
        if (KRelayReaderTypeMap.find(door_reader.type) != KRelayReaderTypeMap.end())
        {
            std::string reader_type = KRelayReaderTypeMap.at(door_reader.type);
            reader_config_value << GetReaderConfigValue(reader_type, door_reader.type_value, door_reader.rs485_address);
            if (reader_config_value.str().empty())
            {
                continue;
            }

            if (door_info.relay_type == DoorRelayType::RELAY)
            {
                if (door_reader.mode == DoorReaderMode::ENTRY && KRelayEntryReaderConfigMap.find(door_info.controlled_relay) != KRelayEntryReaderConfigMap.end())
                {
                    relay_entry_reader_config_value << reader_config_value.str() << ";";
                }
                else if (door_reader.mode == DoorReaderMode::EXIT && KRelayExitReaderConfigMap.find(door_info.controlled_relay) != KRelayExitReaderConfigMap.end())
                {
                    relay_exit_reader_config_value << reader_config_value.str() << ";";
                }
            }
            else if (door_info.relay_type == DoorRelayType::SECURITY_RELAY)
            {
                if (door_reader.mode == DoorReaderMode::ENTRY && KSecurityRelayEntryReaderConfigMap.find(door_info.controlled_relay) != KSecurityRelayEntryReaderConfigMap.end())
                {
                    security_relay_entry_reader_config_value << reader_config_value.str() << ";";
                }
                else if (door_reader.mode == DoorReaderMode::EXIT && KSecurityRelayExitReaderConfigMap.find(door_info.controlled_relay) != KSecurityRelayExitReaderConfigMap.end())
                {
                    security_relay_exit_reader_config_value << reader_config_value.str() << ";";
                }
            }
        }
    }
    
    // 写入配置
    if (!relay_entry_reader_config_key.empty() && !relay_entry_reader_config_value.str().empty())
    {
        kv_.push_back(KVPair(relay_entry_reader_config_key, relay_entry_reader_config_value.str()));
    }
    if (!relay_exit_reader_config_key.empty() && !relay_exit_reader_config_value.str().empty())
    {
        kv_.push_back(KVPair(relay_exit_reader_config_key, relay_exit_reader_config_value.str()));
    }
    if (!security_relay_entry_reader_config_key.empty() && !security_relay_entry_reader_config_value.str().empty())
    {
        kv_.push_back(KVPair(security_relay_entry_reader_config_key, security_relay_entry_reader_config_value.str()));
    }
    if (!security_relay_exit_reader_config_key.empty() && !security_relay_exit_reader_config_value.str().empty())
    {
        kv_.push_back(KVPair(security_relay_exit_reader_config_key, security_relay_exit_reader_config_value.str()));
    }
    return;
}

bool NewOfficeDevConfig::GetRelayAntipassBackDoor(const OfficeDevPtr& dev, const DevicesDoorInfo &door_info, AntiPassBackDoorInfo& antipassback_door)
{
    // 使用equal_range获取所有匹配的元素
    auto range = antipassback_door_info_map_.equal_range(dev->uuid);
    if (range.first == range.second)
    {
        AK_LOG_INFO << "Device " << dev->mac << " not found in antipassback map, UUID: " << dev->uuid;
        return false;
    }

    // 计算反潜回door_type
    int antipassback_door_type = (int)door_info.relay_type + 1;

    // 计算relay_num
    int relay_num = GetRelayIndexByControlledRelay(door_info.controlled_relay);

    // 遍历设备的所有的反潜回door
    for (auto it = range.first; it != range.second; ++it) 
    {
        const auto& relay_type_map = it->second;
        
        // 查找对应的relay_type
        auto relay_type_iter = relay_type_map.find(antipassback_door_type);
        if (relay_type_iter != relay_type_map.end()) 
        {
            const auto& relay_num_map = relay_type_iter->second;
            
            // 查找对应的relay_num
            auto relay_num_iter = relay_num_map.find(relay_num);
            if (relay_num_iter != relay_num_map.end()) 
            {
                // 找到匹配的门信息
                antipassback_door = relay_num_iter->second;
                AK_LOG_INFO << "Found matching antipassback door for device " << dev->mac << ", relay_type: " << antipassback_door_type << ", relay_num: " << relay_num;
                return true;
            }
        }
    }
    
    AK_LOG_INFO << "Device " << dev->mac << " does not have antipassback door with relay_type: " 
                << antipassback_door_type << " and relay_num: " << relay_num;
    return false;
}

void NewOfficeDevConfig::WriteAntipassBackConfig(const OfficeDevPtr& dev, const DevicesDoorInfo &door_info)
{
    // 设备不支持返潜回
    if (!SwitchHandle(dev->fun_bit, FUNC_DEV_SUPPORT_ANTIPASSBACK))
    {
        AK_LOG_INFO << "device " << dev->mac << " not support antipassback, function bit = " << dev->fun_bit;
        return;
    }

    // 判断door_info的relay是否在返潜回Door中
    AntiPassBackDoorInfo antipassback_door;
    if (!GetRelayAntipassBackDoor(dev, door_info, antipassback_door))
    {
        AK_LOG_INFO << "device " << dev->mac << " not get antipassback door, door_info = " << door_info.name;
        return;
    }

    // 返潜回区域开关关闭
    if (!antipassback_door.area_enable)
    {
        AK_LOG_INFO << "device " << dev->mac << " antipassback area is disabled, door_info = " << door_info.name << ", area_enable = " << antipassback_door.area_enable;
        return;
    }

    if (door_info.relay_type == DoorRelayType::RELAY)
    {
        kv_.push_back(KVPair(KAntipassbackRelayConfig.at(door_info.controlled_relay), (int)antipassback_door.door_type));
    }
    else if (door_info.relay_type == DoorRelayType::SECURITY_RELAY)
    {
        kv_.push_back(KVPair(KAntipassbackSecurityRelayConfig.at(door_info.controlled_relay), (int)antipassback_door.door_type));
    }
    return;
}

// 写内开门Input配置
void NewOfficeDevConfig::WriteExitButtonInputConfig(const DevicesDoorInfo &door_info)
{
    // 内开门Input配置: (0-None;1-RelayA;2-RelayB;3-RelayC;4-RelayD;)
    if (door_info.is_null_exit_button == 0 && strlen(door_info.exit_button_input) > 0 && KExitButtonInputNRelayIDConfigMap.find(door_info.exit_button_input) != KExitButtonInputNRelayIDConfigMap.end())
    {
        std::string exit_button_input_n_relay_id_config_key = KExitButtonInputNRelayIDConfigMap.at(door_info.exit_button_input);
        std::string A094_exit_button_input_relay_config_key = KA094ExitButtonInputRelayConfigMap.at(door_info.exit_button_input);

        if (strcmp(door_info.controlled_relay, "A") == 0)
        {
            kv_.push_back(KVPair(A094_exit_button_input_relay_config_key, 1));
            kv_.push_back(KVPair(exit_button_input_n_relay_id_config_key, 1));
        }
        else if (strcmp(door_info.controlled_relay, "B") == 0)
        {
            kv_.push_back(KVPair(A094_exit_button_input_relay_config_key, 2));
            kv_.push_back(KVPair(exit_button_input_n_relay_id_config_key, 2));
        }
        else if (strcmp(door_info.controlled_relay, "C") == 0)
        {
            kv_.push_back(KVPair(A094_exit_button_input_relay_config_key, 3));
            kv_.push_back(KVPair(exit_button_input_n_relay_id_config_key, 3));
        }
        else if (strcmp(door_info.controlled_relay, "D") == 0)
        {
            kv_.push_back(KVPair(A094_exit_button_input_relay_config_key, 4));
            kv_.push_back(KVPair(exit_button_input_n_relay_id_config_key, 4));
        }
    }
    return;
}

bool NewOfficeDevConfig::IsPublicDoor(const DevicesDoorInfo &door_info)
{ 
    //查找设备
    auto range = pub_door_info_map_.equal_range(door_info.devices_uuid);
    for (auto it = range.first; it != range.second; ++it)
    {
        //确认是同一个relay
        if(strncmp(it->second.uuid, door_info.uuid, sizeof(door_info.uuid)) == 0)
        {
            return true;
        }
    }
        
    return false;
}

void NewOfficeDevConfig::WriteDoorRelayConfig(const OfficeDevPtr &dev, const DevicesDoorInfo &door_info)
{
    std::string white_list;
    //判断此relay是否为public door,若是public door则需要配置no belongs to的设备联系人到白名单
    if(IsPublicDoor(door_info))
    {
        white_list = GetWhiteList(dev);
    }
    
    if (strcmp(door_info.controlled_relay, "A") == 0)
    {
        kv_.push_back(KVPair(CONFIG_RELAY_DMTF_CODE1, GetDtmfValue(door_info.dtmf)));
        kv_.push_back(KVPair(CONFIG_RELAY_DMTF_NAME1, door_info.name));
        kv_.push_back(KVPair(CONFIG_RELAY_RELAYUNLOCK1, door_info.access_control_value));
        kv_.push_back(KVPair(CONFIG_RELAY_RELAYSCHEDULE_ENABLE1, door_info.schedule_enable));
        //权限组校验，防止relay不在权限组中还下发了relay schedule给设备
        kv_.push_back(KVPair(CONFIG_RELAY_RELAYSCHEDULE1, GetAlwaysRelaySchedule(dev->uuid, door_info.schedule_access, KRelay2IndexMap.at(door_info.controlled_relay))));
        kv_.push_back(KVPair(CONFIG_DOOR_WHITE_LIST_RELAY_A, white_list));
    }
    else if (strcmp(door_info.controlled_relay, "B") == 0)
    {
        kv_.push_back(KVPair(CONFIG_RELAY_DMTF_CODE2, GetDtmfValue(door_info.dtmf)));
        kv_.push_back(KVPair(CONFIG_RELAY_DMTF_NAME2, door_info.name));
        kv_.push_back(KVPair(CONFIG_RELAY_RELAYUNLOCK2, door_info.access_control_value));
        kv_.push_back(KVPair(CONFIG_RELAY_RELAYSCHEDULE_ENABLE2, door_info.schedule_enable));
        //权限组校验，防止relay不在权限组中还下发了relay schedule给设备
        kv_.push_back(KVPair(CONFIG_RELAY_RELAYSCHEDULE2, GetAlwaysRelaySchedule(dev->uuid, door_info.schedule_access, KRelay2IndexMap.at(door_info.controlled_relay))));
        kv_.push_back(KVPair(CONFIG_DOOR_WHITE_LIST_RELAY_B, white_list));
    }
    else if (strcmp(door_info.controlled_relay, "C") == 0)
    {
        kv_.push_back(KVPair(CONFIG_RELAY_DMTF_CODE3, GetDtmfValue(door_info.dtmf)));
        kv_.push_back(KVPair(CONFIG_RELAY_DMTF_NAME3, door_info.name));
        kv_.push_back(KVPair(CONFIG_RELAY_RELAYUNLOCK3, door_info.access_control_value));
        kv_.push_back(KVPair(CONFIG_RELAY_RELAYSCHEDULE_ENABLE3, door_info.schedule_enable));
        //权限组校验，防止relay不在权限组中还下发了relay schedule给设备
        kv_.push_back(KVPair(CONFIG_RELAY_RELAYSCHEDULE3, GetAlwaysRelaySchedule(dev->uuid, door_info.schedule_access, KRelay2IndexMap.at(door_info.controlled_relay))));
        kv_.push_back(KVPair(CONFIG_DOOR_WHITE_LIST_RELAY_C, white_list));
    }
    else if (strcmp(door_info.controlled_relay, "D") == 0)
    {
        kv_.push_back(KVPair(CONFIG_RELAY_DMTF_CODE4, GetDtmfValue(door_info.dtmf)));
        kv_.push_back(KVPair(CONFIG_RELAY_DMTF_NAME4, door_info.name));
        kv_.push_back(KVPair(CONFIG_RELAY_RELAYUNLOCK4, door_info.access_control_value));
        kv_.push_back(KVPair(CONFIG_RELAY_RELAYSCHEDULE_ENABLE4, door_info.schedule_enable));
        //权限组校验，防止relay不在权限组中还下发了relay schedule给设备
        kv_.push_back(KVPair(CONFIG_RELAY_RELAYSCHEDULE4, GetAlwaysRelaySchedule(dev->uuid, door_info.schedule_access, KRelay2IndexMap.at(door_info.controlled_relay))));
        kv_.push_back(KVPair(CONFIG_DOOR_WHITE_LIST_RELAY_D, white_list));
    }

    // 门常开配置
    WriteDoorHoldOpenAlarmConfig(door_info);

    // 内开门配置
    WriteExitButtonInputConfig(door_info);

    return;
}

void NewOfficeDevConfig::WriteDoorSecutiryRelayConfig(const OfficeDevPtr &dev, const DevicesDoorInfo &door_info)
{
    std::string white_list;
    //判断此relay是否为public door,若是public door则需要配置no belongs to的设备联系人到白名单
    if(IsPublicDoor(door_info))
    {
        white_list = GetWhiteList(dev);
    }

    if (strcmp(door_info.controlled_relay, "A") == 0)
    {
        kv_.push_back(KVPair(CONFIG_SECURITY_RELAY_DMTF_CODE1, GetDtmfValue(door_info.dtmf)));
        kv_.push_back(KVPair(CONFIG_SECURITY_RELAY_DMTF_NAME1, door_info.name));
        kv_.push_back(KVPair(CONFIG_SECURITY_RELAY_ENABLED1, door_info.enable));
        kv_.push_back(KVPair(CONFIG_SECURITY_RELAY_RELAYUNLOCK1, door_info.access_control_value));
        kv_.push_back(KVPair(CONFIG_DOOR_WHITE_LIST_RELAY_SA, white_list));
    }
    else if (strcmp(door_info.controlled_relay, "B") == 0)
    {
        kv_.push_back(KVPair(CONFIG_SECURITY_RELAY_DMTF_CODE2, GetDtmfValue(door_info.dtmf)));
        kv_.push_back(KVPair(CONFIG_SECURITY_RELAY_DMTF_NAME2, door_info.name));
        kv_.push_back(KVPair(CONFIG_SECURITY_RELAY_ENABLED2, door_info.enable));
        kv_.push_back(KVPair(CONFIG_SECURITY_RELAY_RELAYUNLOCK2, door_info.access_control_value));
        kv_.push_back(KVPair(CONFIG_DOOR_WHITE_LIST_RELAY_SB, white_list));
    }

    // 门常开配置
    WriteDoorHoldOpenAlarmConfig(door_info);

    return;
}

void NewOfficeDevConfig::WriteInputEnableConfig(const DevicesDoorInfoList &door_info_list)
{
    // A01/A02/A03/A08/Android/A094 Input Enable配置, 只要input在exit button或door status出现过，它的值就是1
    std::map<std::string, bool> input_enable_config_map = { {"A", false}, {"B", false}, {"C", false}, {"D", false} };

    // Check all doors for exit buttons or door status inputs
    for (const auto& door_info : door_info_list)
    {
        // 门关闭或者没有配置内开门或门状态输入，则不配置input enable
        if (door_info.enable == 0 || (strlen(door_info.exit_button_input) == 0 && strlen(door_info.door_status_input) == 0))
        {
            continue;
        }
        
        // Process exit button inputs
        if (strlen(door_info.exit_button_input) > 0)
        {
            for (const auto& pair : input_enable_config_map)
            {
                const std::string& input_id = pair.first;
                if (strstr(door_info.exit_button_input, input_id.c_str()) != nullptr)
                {
                    input_enable_config_map[input_id] = true;
                }
            }
        }
        
        // Process door status inputs
        if (strlen(door_info.door_status_input) > 0)
        {
            for (const auto& pair : input_enable_config_map)
            {
                const std::string& input_id = pair.first;
                if (strstr(door_info.door_status_input, input_id.c_str()) != nullptr)
                {
                    input_enable_config_map[input_id] = true;
                }
            }
        }
    }

    // A01/A02/A03/A08/Android/A094 Input Enable配置, 只要input在exit button或door status出现过，它的值就是1
    for (const auto& pair : input_enable_config_map)
    {
        const std::string& input_id = pair.first;
        bool enable = pair.second;
        if (enable)
        {
            kv_.push_back(KVPair(KA094InputEnableConfigMap.at(input_id), 1));
            kv_.push_back(KVPair(KInputEnableConfigMap.at(input_id), 1));
            kv_.push_back(KVPair(KInputEnable2ConfigMap.at(input_id), 1));
        }
    }

    return;
}
void NewOfficeDevConfig::WriteDoorConfig(const OfficeDevPtr &dev, std::stringstream &config)
{
    // 先重置配置, 再按实际的配置下发
    ResetDoorCommonConfig(config);

    if (dev_door_info_map_.find(dev->uuid) != dev_door_info_map_.end())
    {
        DevicesDoorInfoList door_info_list = dev_door_info_map_.at(dev->uuid);
        int has_reset_exit_button = 0;
        for (const auto& door_info : door_info_list)
        {
            // 网页设置过才需要下发默认内开门配置。兼容升级上来 本地配置exitbutton的情况
            if (door_info.is_null_exit_button == 0 && has_reset_exit_button == 0) {
                ResetDoorInputEnableConfig(config);
                has_reset_exit_button = 1;
            }
            // 开关开启刷配置
            if (!door_info.enable)
            {
                continue;
            }

            if (door_info.relay_type == DoorRelayType::RELAY)
            {
                WriteDoorRelayConfig(dev, door_info);
            }
            else if (door_info.relay_type == DoorRelayType::SECURITY_RELAY)
            {
                WriteDoorSecutiryRelayConfig(dev, door_info);
            }

            // 写relay关联的reader配置
            WriteDoorRelayReaderConfig(door_info);

            // 写强闯告警检测DoorStatusInput配置
            WriteBreakInAlarmConfig(door_info);

            // 写反潜回配置
            WriteAntipassBackConfig(dev, door_info);
        }

        // 写input enable配置
        WriteInputEnableConfig(door_info_list);
    }
}

// 强闯告警检测DoorStatusInput配置
void NewOfficeDevConfig::WriteBreakInAlarmConfig(const DevicesDoorInfo &door_info)
{
    if (strlen(door_info.door_status_input) > 0 && KBreakInAlarmInputEnableConfigMap.find(door_info.door_status_input) != KBreakInAlarmInputEnableConfigMap.end())
    {
        if (door_info.breakin_alarm_enable)
        {
            // 强闯input连接到door的那个relay
            kv_.push_back(KVPair(KBreakInAlarmInputAndroidConfigMap.at(door_info.door_status_input), GetRelayIndexByControlledRelay(door_info.controlled_relay)));

            // 下发给A01/A02/A03/A08,input enable
            kv_.push_back(KVPair(KBreakInAlarmInputEnableConfigMap.at(door_info.door_status_input), 1));
        }
    }
    if (strlen(door_info.door_status_input) > 0 && KBreakInAlarmInputA094ConfigMap.find(door_info.door_status_input) != KBreakInAlarmInputA094ConfigMap.end())
    {
        if (door_info.breakin_alarm_enable)
        {
            // 强闯input连接到door的那个relay,下发给A094,input enable
            kv_.push_back(KVPair(KBreakInAlarmInputA094ConfigMap.at(door_info.door_status_input), GetRelayIndexByControlledRelay(door_info.controlled_relay)));
        }
    }
    return;
}

void NewOfficeDevConfig::WriteDoorHoldOpenAlarmConfig(const DevicesDoorInfo &door_info)
{
    if (door_info.holdopen_alarm_enable && KDoorHoldOpenAlarmInputEnableConfig.find(door_info.door_status_input) != KDoorHoldOpenAlarmInputEnableConfig.end())
    {
        kv_.push_back(KVPair(KDoorHoldOpenAlarmInputEnableConfig.at(door_info.door_status_input), door_info.holdopen_alarm_enable));
        kv_.push_back(KVPair(KDoorHoldOpenAlarmInputTimeoutConfig.at(door_info.door_status_input), door_info.holdopen_alarm_timeout));
        kv_.push_back(KVPair(KDoorHoldOpenAlarmInputEnableConfig2.at(door_info.door_status_input), door_info.holdopen_alarm_enable));
        kv_.push_back(KVPair(KDoorHoldOpenAlarmInputTimeoutConfig2.at(door_info.door_status_input), door_info.holdopen_alarm_timeout));
    }

    // A094 Input启用开关，只要door_status_input配置了，就启用, 不管holdopen_alarm_enable开关
    if (strlen(door_info.door_status_input) > 0 && KA094DoorHoldOpenAlarmInputEnableConfig.find(door_info.door_status_input) != KA094DoorHoldOpenAlarmInputEnableConfig.end())
    {
        kv_.push_back(KVPair(KA094DoorStatusInputEnableConfigMap.at(door_info.door_status_input), 1));
        kv_.push_back(KVPair(KA094DoorHoldOpenAlarmInputEnableConfig.at(door_info.door_status_input), 1));
    }

    // A094 门超时未关 只有timeout参数，判断doorstatus选了input 并且 door help open alarm开关开启才下发时长
    if (strlen(door_info.door_status_input) > 0 && door_info.holdopen_alarm_enable
         && KA094DoorHoldOpenAlarmInputEnableConfig.find(door_info.door_status_input) != KA094DoorHoldOpenAlarmInputEnableConfig.end())
    {
        kv_.push_back(KVPair(KA094DoorHoldOpenAlarmInputTimeoutConfig.at(door_info.door_status_input), door_info.holdopen_alarm_timeout));
    }
    return;
}

void NewOfficeDevConfig::WriteTimeZoneConfig(const OfficeDevPtr &dev)
{
    std::string ntp_time = "GMT";
    std::string city_name;
    std::string timezone = office_info_->TimeZone();
    int time_format = office_info_->TimeFormat();

    std::size_t found = timezone.find(' ');
    if (found != std::string::npos)
    {
        ntp_time += timezone.substr(0, found);

        city_name = timezone.substr(found + 1);
    }
    //旧版本设备用了新时区的不配
    if (dev->dclient_ver >= D_CLIENT_VERSION_5400 
        || (city_name != CONFIG_NEW_TIMEZONE_NUUK && city_name != CONFIG_NEW_TIMEZONE_KOLKATA))  
    {
        kv_.push_back(KVPair(CONFIG_CLOUDSERVER_TOMEZONE, ntp_time));
        kv_.push_back(KVPair(CONFIG_CLOUDSERVER_CITYNAME, city_name));
    }
    
    int hour_format = CustomizeDateFormatToDeviceConfigValue(time_format, 1);
    kv_.push_back(KVPair(CONFIG_CLOUDSERVER_TIMEFORMAT, hour_format));

    
    int date_format = CustomizeDateFormatToDeviceConfigValue(time_format, 2);
    kv_.push_back(KVPair(CONFIG_CLOUDSERVER_DATEFORMAT, date_format));
}

void NewOfficeDevConfig::WriteAudioVedioInfo(const OfficeDevPtr &dev)
{

    if (DevMngSipType_NONE == mng_setting_.sip_type)
    {
        kv_.push_back(KVPair( "Config.Account1.SIP.TransType", dev->sip_type));
    }
    else
    {
        kv_.push_back(KVPair( "Config.Account1.SIP.TransType", mng_setting_.sip_type));
    }
    //rtp confuse 6.0
    if (dev->dclient_ver >= D_CLIENT_VERSION_6000)
    {
        kv_.push_back(KVPair( "Config.Account1.CALL.AudioVideoConfuse", mng_setting_.rtp_confuse));
    }
    // rtsps 开关
    kv_.push_back(KVPair(CONFIG_RTSPS_ENABLE, mng_setting_.rtsp_type));
}


void NewOfficeDevConfig::WriteDoorDtmfType(const OfficeDevPtr &dev)
{
    int type = 0;
     //******** add by chenzhx        
    if (gstCSCONFIGConf.server_type == ServerArea::ccloud  && dev->dclient_ver < D_CLIENT_VERSION_6400)
    {
        type = 4;
    }
    else if (dev->dclient_ver >= D_CLIENT_VERSION_6400)
    {
        //1:Inband;2:RFC2833;3:Info;4:Info+Inband;5:Info+RFC2833;6:Info+Inband+RFC2833
        //add by czw, dtmf国内外方式不同处理
        if(gstCSCONFIGConf.server_type == ServerArea::ccloud)
        {
            type = 6;
        }
        else
        {
            type = 2;
        }      
    }
    if (type != 0)
    {
        kv_.push_back(KVPair("Config.Account1.DTMF.Type", type));
    }
}

// 7.1.1暂时只支持配置Internal Reader作为点名Reader，先写死
void NewOfficeDevConfig::WriteMusterReaderConfig(const OfficeDevPtr &dev)
{
    if (!dbinterface::OfficeMusterReportSettingReaderList::IsMusterReportDevice(dev->uuid))
    {
        kv_.push_back(KVPair("Config.DoorSetting.Muster.Reader", ""));
        return;
    }
    
    // OfficeMusterReportSettingReaderListInfo dev_muster_report_setting_info;
    // if (0 != dbinterface::OfficeMusterReportSettingReaderList::GetOfficeMusterReportSettingReaderInfoByDeviceUUID(dev->uuid, dev_muster_report_setting_info))
    // {
    //     return;
    // }

    kv_.push_back(KVPair("Config.DoorSetting.Muster.Reader", "Internal"));
}

int GetMotionDetection(uint32_t dclient_version, int enable_motion)
{
    if (dclient_version < 6000)
    {
        return (enable_motion == (int)MotionDectionOption::MOTION_DECTION_VIDEO) ? (int)MotionDectionOption::MOTION_DECTION_IR : enable_motion;
    }
    else
    {
        return enable_motion;
    }
}

std::string NewOfficeDevConfig::GetWhiteList(const OfficeDevPtr &own_dev)
{
    std::stringstream white_list;
    for(const auto& no_belongs_to_dev : no_belongs_to_dev_list_)
    {
        OfficeDevPtr dev = no_belongs_to_dev.second;
        if (!CheckDevUnitPermission(own_dev, dev))
        {
            continue;
        }
        if (own_dev->net_group_number == dev->net_group_number)
        {   
            if(strlen(dev->ipaddr) > 0)
            {
                white_list << dev->ipaddr << ";";
            }
        }
        else
        {
            white_list << dev->sip << ";";
        }
    }

    std::string white_content = white_list.str();
    if(white_content.size() > 0)
    {
        //去掉最后一个;
        white_content.pop_back();
    }
    return white_content;
}

void NewOfficeDevConfig::WriteDoorInfo(const OfficeDevPtr &dev, std::stringstream& config)
{
    kv_.push_back(KVPair(CONFIG_RTSP_ENABLE, 1)); 
    kv_.push_back(KVPair(CONFIG_RTSP_VIDEO , 1));
    kv_.push_back(KVPair(CONFIG_RTSP_CODEC, 0));
    kv_.push_back(KVPair(CONFIG_RTSP_H264_FRAMERATE, 30));
    kv_.push_back(KVPair("Config.DoorSetting.RTSP.H264Resolution", 4));
    kv_.push_back(KVPair("Config.DoorSetting.RTSP.H264BitRate", 2048));

    int enable_motion = GetMotionDetection(dev->dclient_ver, office_info_->isEnableMotion());
    kv_.push_back(KVPair(CONFIG_DOORSETTING_MOTION_DETECT_ENABLE, enable_motion));
    kv_.push_back(KVPair(CONFIG_DOORSETTING_MOTION_DETECT_TIME, office_info_->MotionTime()));
    kv_.push_back(KVPair(CONFIG_IDCARD_ENABLE, office_info_->IDCard()));
    
    WriteDoorConfig(dev, config);
    return;
}

void NewOfficeDevConfig::WriteIndoorDoorInfo(const OfficeDevPtr &dev)
{

}

void NewOfficeDevConfig::WriteCommonInfo(const OfficeDevPtr &dev)
{
    int sip_port = HashtabHashString(dev->sippwd) % 55534 + 10000;
    kv_.push_back(KVPair(CONFIG_ENABLE , 1));
    kv_.push_back(KVPair(CONFIG_LABLE ,  dev->sip));
    kv_.push_back(KVPair(CONFIG_DISPLAYNAME, dev->location));
    kv_.push_back(KVPair(CONFIG_USERNAME, dev->sip));
    kv_.push_back(KVPair("Config.Account1.OUTPROXY.Enable", 0));
    kv_.push_back(KVPair(CONFIG_AUTHNAME,dev->sip));
    kv_.push_back(KVPair(CONFIG_PWD, dev->sippwd));
    kv_.push_back(KVPair(CONFIG_TIMEOUT, PERSONNAL_SIP_UA_TIMEOUT));
    kv_.push_back(KVPair(CONFIG_SIP_NAT_RPORT, 1)); 
    kv_.push_back(KVPair(CONFIG_NAT_UDP_ENABLE, 1)); 
    kv_.push_back(KVPair(CONFIG_NAT_UDP_INTERVAL, 30)); 
    kv_.push_back(KVPair(CONFIG_CLOUDSERVER_FTP_USER, "akuvox" ));
    kv_.push_back(KVPair(CONFIG_CLOUDSERVER_FTP_PWD, "pu6HYKvTkyGstq")); 
    kv_.push_back(KVPair(CONFIG_CLOUDSERVER_DEV_EXPIRE, 0));
    kv_.push_back(KVPair("Config.Account1.Video00.ProfileLevel", 2));
    kv_.push_back(KVPair("Config.Account1.Video00.MaxBR", 512));
    kv_.push_back(KVPair("Config.Features.VIDEO_CODEC_PARAM.ProfileLevel", "720P"));
    kv_.push_back(KVPair("Config.Account1.SIP.ListenPortMin", sip_port));
    kv_.push_back(KVPair("Config.Account1.SIP.ListenPortMax", sip_port + 10));
    kv_.push_back(KVPair("Config.Account1.Audio0.Enable", 0));
    kv_.push_back(KVPair("Config.Account1.Audio1.Enable", 1));
    kv_.push_back(KVPair("Config.Account1.Audio4.Enable", 0));
    kv_.push_back(KVPair("Config.Account1.Audio5.Enable", 0));
    kv_.push_back(KVPair("Config.Account1.Audio0.Enable", 1));//******** 涂鸦强制用pcmu

    kv_.push_back(KVPair("Config.Doorsetting.PASSWORD.PrivateKeyType", 1)); //apt+pin 0apt+key, 1key
    kv_.push_back(KVPair(CONFIG_CLOUDSERVER_DOOR_OPEN_LIMIT, 1));
    kv_.push_back(KVPair("Config.Account1.CALL.PreventSIPHacking", 1));//写siptype siphacking
    kv_.push_back(KVPair("Config.DoorSetting.DEVICENODE.Location",  dev->location));//v5.0加入设备名称配置项
    kv_.push_back(KVPair("Config.DoorSetting.DEVICENODE.SSHPassSrv",  gstCSCONFIGConf.ssh_proxy_domain));
    kv_.push_back(KVPair("Config.DoorSetting.DISPLAY.Key4Type", 7));//（隐藏默认配置顺序的拨号入口）
    kv_.push_back(KVPair("Config.DoorSetting.CLOUDSERVER.NewComm", 1));//新办公NewComm默认值为1
    kv_.push_back(KVPair(CONFIG_CLOUDSERVER_TYPE, CLOUD_SERVER_TYPE_COMMUNITY_PUBLIC));//默认都是放在最外围 支持以部门进行过滤


    kv_.push_back(KVPair(CONFIG_COMMUNITY_NAME,office_info_->Name()));
    kv_.push_back(KVPair(CONFIG_COMMUNITY_STREET,office_info_->Street()));
    
    kv_.push_back(KVPair(CONFIG_CLOUDSERVER_NEW_OFFICE, 1));
}

/*
void NewOfficeDevConfig::WritePackgeRomm(const OfficeDevPtr &dev)
{
    if (dev->dclient_ver >= D_CLIENT_VERSION_6200 && akjudge::IsCommunityPublicDev(dev->grade) && akjudge::DevDoorType(dev->dev_type))
    {
        if (!office_info_->IsExpire() && office_info_->CheckFeature(OfficeInfo::FeaturePlan::TAB_DEVICES_CHECK_INDEX_DELIVERY))
        {
            kv_.push_back(KVPair("Config.DoorSetting.PACKAGEROOM.RemindAvailable", 1)); 
        }
        else
        {
            kv_.push_back(KVPair("Config.DoorSetting.PACKAGEROOM.RemindAvailable", 0));
        }
    }
}
*/

int NewOfficeDevConfig::WriteFiles(const OfficeDevPtr &dev)
{
    kv_.clear();
    std::stringstream config;

    WriteCommonInfo(dev);
    WriteTimeZoneConfig(dev);
    WriteAudioVedioInfo(dev);

    //门口机、梯口机增加sip群组账号
    if (akjudge::DevDoorType(dev->dev_type))
    {
        WriteDoorInfo(dev, config);
        
        if (akjudge::DeviceSupportLimitFlow(dev->firmwares))
        {
            kv_.push_back(KVPair(CONFIG_NETWORK_DATAUSAGE_DATATYPE, office_info_->LimitFlowDataType()));
        }
        WriteDoorDtmfType(dev);

        // 点名reader配置
        WriteMusterReaderConfig(dev);
    }   

    if (dev->dev_type == DEVICE_TYPE_INDOOR)
    {
        WriteIndoorDoorInfo(dev);
    }

    //新办公不支持快递间
    //WritePackgeRomm(dev);

    WriteKvInfo(config);

    UpdateUcloudVideoBitRate(dev->sw_ver, config);
    UpdateSipSrtpConfig(mng_setting_.sip_type, dev->fun_bit, config);
    UpdateAuxCameraConfig(dev->fun_bit, config);
    UpdateHighResolutionVideoResolution(dev->firmwares, config);

    config << dev->autop_config << "\n";
    //写入文件
    std::string config_path = config_root_path_ + dev->mac + "-config.cfg";
    DevFileInfoPtr ptr = std::make_shared<DevFileInfo>(dev->mac, config_path, config.str(), SHADOW_TYPE::SHADOW_CONFIG, project::OFFICE, dev->id);
    GetWriteFileControlInstance()->AddFileInfo(dev->mac, ptr);
    return 0;
}

void NewOfficeDevConfig::WriteKvInfo(std::stringstream &config)
{

    for (auto& pair : kv_)
    {
        std::string key = pair.key;
        TrimString(key);
        if (key.empty()) {
            continue;
        }
        
        if (key.back() == '=')
        {
            config << key << pair.value << std::endl;
        }
        else
        {
            config << key << "=" << pair.value << std::endl;
        }
    }

}
