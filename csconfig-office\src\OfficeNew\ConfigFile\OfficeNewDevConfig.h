#ifndef __NEW_OFFICE_DEV_CONFIG_H__
#define __NEW_OFFICE_DEV_CONFIG_H__

#include <string>
#include <iostream>
#include <sstream>
#include "AKCSMsg.h"
#include "InnerDbDef.h"
#include "dbinterface/Account.h"
#include "dbinterface/AntiPassbackDoor.h"
#include "dbinterface/AntiPassbackArea.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/new-office/DevicesDoorList.h"
#include "dbinterface/office/OfficePersonalAccount.h"

//std::vector<std::string> gNewTimeZoneVec{"Pacific-New","Center","New_Salem","Beulah","Nuuk","Kolkata"};

using AntiPassbackDoorMap = std::multimap<std::string/*DevicesUUID*/, std::unordered_map<int /*RelayType*/, std::unordered_map<int /*RelayNum*/, AntiPassBackDoorInfo>>>;

// 定义一个支持不同类型的键值对类
class KVPair {
public:
    std::string key;
    std::string value;

    // 默认构造函数
    KVPair() {}

    // 带参数的构造函数，将传入的键和值转换为字符串类型
    template <typename KeyType, typename ValueType>
    KVPair(const KeyType& k, const ValueType& v) {
        std::stringstream key_stream;
        key_stream << k;
        key = key_stream.str();

        std::stringstream value_stream;
        value_stream << v;
        value = value_stream.str();
    } 
    // 特化版本的构造函数，处理std::stringstream对象
    KVPair(const std::stringstream& k, const std::stringstream& v) {
        key = k.str();
        value = v.str();
    }    
};

class NewOfficeDevConfig
{
public:
	NewOfficeDevConfig(  const std::string& config_root_path, const    dbinterface::MngSetting &setting)
    {
        config_root_path_ = config_root_path;
        mng_setting_ = setting;
    }

	~NewOfficeDevConfig()
    {
        
    }
    int WriteFiles(const OfficeDevPtr &dev);    
    void SetOfficeInfo(   OfficeInfoPtr &office_info)
    {
        office_info_ = office_info;
    }

    void SetAgInfo(const AgDevInfoDevMap &ag_dev_map, const   AgDevInfoUUIDMap &dev_uuid_map, const   AccessGroupUUIDMap &info_map)
    {
        ag_dev_map_ = ag_dev_map;
        ag_dev_uuid_map_ = dev_uuid_map;
        ag_info_map_ = info_map;
    }

    void SetDevDoorListInfo(const DevicesDoorInfoMap& dev_door_info_map)
    {
        dev_door_info_map_ = dev_door_info_map;
    }

    
    void SetPubDoorListInfo(const DevicesPublicDoorInfoMap& pub_door_info_map)
    {
        pub_door_info_map_ = pub_door_info_map;
    }

    void SetAntiPassbackInfo(const AntiPassbackDoorMap& antipassback_door_info_map)
    {
        antipassback_door_info_map_ = antipassback_door_info_map;
    }

    void SetNoBelongsToDevUUIDList(const OfficeDevMap& no_belongs_to_dev_list)
    {
        no_belongs_to_dev_list_ = no_belongs_to_dev_list;
    }

    bool IsPublicDoor(const DevicesDoorInfo &door_info);
    
private:
    void WriteDoorConfig(const OfficeDevPtr &dev, std::stringstream &config);
    void WriteTimeZoneConfig(const OfficeDevPtr &dev);
    void WriteAudioVedioInfo(const OfficeDevPtr &dev);
    void WriteDoorDtmfType(const OfficeDevPtr &dev);
    void WriteDoorInfo(const OfficeDevPtr &dev, std::stringstream& config);
    void WriteInputEnableConfig(const DevicesDoorInfoList &door_info_list);
    void WriteIndoorDoorInfo(const OfficeDevPtr &dev);
    void WriteCommonInfo(const OfficeDevPtr &dev);
    void WritePackgeRomm(const OfficeDevPtr &dev);
    void WriteKvInfo(std::stringstream &config);
    void WriteMusterReaderConfig(const OfficeDevPtr &dev);
    void ResetDoorCommonConfig(std::stringstream &config);
    void ResetDoorInputEnableConfig(std::stringstream &config);
    void WriteDoorHoldOpenAlarmConfig(const DevicesDoorInfo &door_info);
    void WriteAntipassBackConfig(const OfficeDevPtr& dev, const DevicesDoorInfo &door_info);
    void WriteDoorRelayReaderConfig(const DevicesDoorInfo& door_info);
    void WriteDoorRelayConfig(const OfficeDevPtr &dev, const DevicesDoorInfo &door_info);
    void WriteDoorSecutiryRelayConfig(const OfficeDevPtr &dev, const DevicesDoorInfo &door_info);
    void WriteBreakInAlarmConfig(const DevicesDoorInfo &door_info);
    bool GetRelayAntipassBackDoor(const OfficeDevPtr& dev, const DevicesDoorInfo &door_info, AntiPassBackDoorInfo& antipassback_door);
    void WriteExitButtonInputConfig(const DevicesDoorInfo &door_info);
    std::string GetAlwaysRelaySchedule(const std::string &dev_uuid, const std::string& relay_ag_uuids, uint32_t now_relay_index);
    std::string GetWhiteList(const OfficeDevPtr &own_dev);


    std::string config_root_path_;
    OfficeInfoPtr office_info_;
    dbinterface::MngSetting mng_setting_;
    std::list<KVPair> kv_;

    AgDevInfoDevMap ag_dev_map_;
    AgDevInfoUUIDMap ag_dev_uuid_map_;
    AccessGroupUUIDMap ag_info_map_;
    DevicesDoorInfoMap dev_door_info_map_;
    DevicesPublicDoorInfoMap pub_door_info_map_;
    AntiPassbackDoorMap antipassback_door_info_map_;
    OfficeDevMap no_belongs_to_dev_list_;
};

#endif 
