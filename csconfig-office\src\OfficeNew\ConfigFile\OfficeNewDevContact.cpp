#include <sstream>
#include "OfficeNew/ConfigFile/OfficeNewDevContact.h"
#include "OfficeNew/ConfigFile/OfficeNewDevContactCallSeq.h"
#include <string.h>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "ConfigDef.h" 
#include "json/json.h"
#include "util.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AES256.h"
#include "AkcsMonitor.h"
#include "encrypt/Md5.h"
#include "AkcsCommonDef.h"
#include "ShadowMng.h"
#include "ContactCommon.h"
#include "WriteFileControl.h"
#include "dbinterface/VersionModel.h"
#include "util_judge.h"

extern CSCONFIG_CONF gstCSCONFIGConf;


std::string NewOfficeDevContact::GetAccountSipGroup(const std::string &per_uuid)
{
    std::string sip_group;
    auto it = account_sip_group_map_.find(per_uuid);
    if (it != account_sip_group_map_.end())
    {
        sip_group = it->second;
    }
    return std::move(sip_group);
}

std::string NewOfficeDevContact::GetAccountLandline(const OfficeAccount &account)
{
    std::string phone_head;
    if (strlen(account.phone) > 0)
    {
        phone_head = PHONE_CALL_OUT_SUBFIX;
        phone_head += account.phone_code;
        phone_head += account.phone;
    }
    return std::move(phone_head);
}

OfficeGroupInfo NewOfficeDevContact::GetGroupInfo(const std::string &group_uuid)
{
    OfficeGroupInfo info;
    auto it = all_group_list_.find(group_uuid);
    if (it != all_group_list_.end())
    {
        info = it->second;
    }
    return std::move(info);
}

std::string NewOfficeDevContact::GetContactNameByOrder(const OfficeAccount &account)
{
    std::string name;
    if (!office_info_)
    {
        return name;
    }
    
    int contact_name_order = office_info_->ContactDisplayOrder();
    if (contact_name_order == (int)NameOrder::FIRST_NAME_FIRST)
    {
        name += account.firstname;
        name += " ";
        name += account.lastname;
    }
    else if (contact_name_order == (int)NameOrder::LAST_NAME_FIRST)
    {
        name += account.lastname;
        name += " ";
        name += account.firstname;
    }
    return name;
}

void NewOfficeDevContact::TransformGroupSeqCall(const OfficeGroupSeqCallMap &seq_call, GroupContactInfoPtr &contact)
{
    //app和landline只能下发一条数据 需要进行整合
    for (const auto &it : seq_call)
    {
        OfficeGroupSequenceCallInfo info = it.second;
        if (info.call_type == CallSeqType::CALL_SEQ_TYPE_APP || info.call_type == CallSeqType::CALL_SEQ_TYPE_LANDLINE)
        {
            auto account = all_account_map_.find(info.personal_account_uuid);
            if (account == all_account_map_.end())
            {
                AK_LOG_WARN << "GroupSeqCall account not found. account_uuid" << info.personal_account_uuid;
                continue;
            }

            AccountContactInfoPtr group_account = std::make_shared<AccountContactInfo>();
            if (account->second.role == ACCOUNT_ROLE_OFFICE_NEW_PER)
            {
                auto personnel = all_personnel_map_.find(info.personal_account_uuid);
                if (personnel == all_personnel_map_.end())
                {
                    AK_LOG_WARN << "GroupSeqCall personnel not found. account_uuid" << info.personal_account_uuid;
                    continue;
                }
                group_account->SetAccountInfo(account->second, personnel->second);
            }
            else if (account->second.role == ACCOUNT_ROLE_OFFICE_NEW_ADMIN)
            {
                auto admin = all_admin_map_.find(info.personal_account_uuid);
                if (admin == all_admin_map_.end())
                {
                    AK_LOG_WARN << "GroupSeqCall admin not found. account_uuid=" << info.personal_account_uuid;
                    continue;
                }
                group_account->SetAccountInfo(account->second, admin->second);
            }

            contact->AddAccountAppInfo(group_account, info.call_type, info.call_order);
        }
        else if (info.call_type == CallSeqType::CALL_SEQ_TYPE_DEV)
        {
            auto dev = all_dev_map_.find(info.device_uuid);
            if (dev == all_dev_map_.end())
            {
                AK_LOG_INFO << "GroupSeqCall dev not found. dev_uuid" << info.device_uuid;
                continue;
            }            
            contact->AddDev(dev->second, info.call_order);
        }
    }    
}

void NewOfficeDevContact::GenerateAccountGroupContactList(const AccountContactInfoPtr &info, ContactKvList &group_kv)
{
    group_kv.push_back(std::make_pair(CONTACT_ATTR::NAME,  GetContactNameByOrder(info->account_)));
    group_kv.push_back(std::make_pair(CONTACT_ATTR::NODE, info->account_.sip_account));  
    group_kv.push_back(std::make_pair(CONTACT_ATTR::SIP, GetAccountSipGroup(info->account_.uuid))); 
    group_kv.push_back(std::make_pair(CONTACT_ATTR::ROOM, GetContactNameByOrder(info->account_)));     
    group_kv.push_back(std::make_pair(CONTACT_ATTR::ROOM_N, std::string("")));
    group_kv.push_back(std::make_pair(CONTACT_ATTR::UNIT_APT, std::string("")));
    group_kv.push_back(std::make_pair(CONTACT_ATTR::UNIT, group_name_cb_(info->group_uuid_, OfficeUUIDType::GROUP_UUID))); 
    group_kv.push_back(std::make_pair(CONTACT_ATTR::COMPANY, company_name_cb_(info->account_.uuid, OfficeUUIDType::PER_UUID)));
    group_kv.push_back(std::make_pair(CONTACT_ATTR::IP_DIRECT, std::string("1")));/*新办公     默认值*/
    group_kv.push_back(std::make_pair(CONTACT_ATTR::CALL_LOOP,std::string("2")));/*新办公 默认值*/
}    

//写group的xml group内容
void NewOfficeDevContact::GenerateDepartmentHeadList(const OfficeGroupInfo &info, ContactKvList &group_kv)
{   
    group_kv.push_back(std::make_pair(CONTACT_ATTR::UNIT, info.name)); 
    group_kv.push_back(std::make_pair(CONTACT_ATTR::COMPANY, company_name_cb_(info.uuid, OfficeUUIDType::GROUP_UUID))); 
    group_kv.push_back(std::make_pair(CONTACT_ATTR::CALL_LOOP,std::string("2")));/*新办公 默认值*/
    group_kv.push_back(std::make_pair(CONTACT_ATTR::UUID, info.uuid)); 
}    

void NewOfficeDevContact::GeneratePubManageDevHeadList(const string &company_uuid, const OfficeDevPtr& dev_info, ContactKvList &group_kv)
{   
    group_kv.push_back(std::make_pair(CONTACT_ATTR::UNIT, dev_info->location)); 
    group_kv.push_back(std::make_pair(CONTACT_ATTR::COMPANY, company_name_cb_(company_uuid,  OfficeUUIDType::COMPANY_UUID))); 
    group_kv.push_back(std::make_pair(CONTACT_ATTR::CALL_LOOP,std::string("2")));/*新办公 默认值*/
    std::string uuid = std::string("device_") + dev_info->uuid;
    group_kv.push_back(std::make_pair(CONTACT_ATTR::UUID, uuid)); 
}    



void NewOfficeDevContact::GenerateAccountContactList(const AccountContactInfoPtr &info, ContactKvList &contact_kv, bool display)
{
    contact_kv.push_back(std::make_pair(CONTACT_ATTR::NAME, GetContactNameByOrder(info->account_)));
    contact_kv.push_back(std::make_pair(CONTACT_ATTR::UID, info->account_.sip_account));
    contact_kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(DEVICE_TYPE_APP)));
    contact_kv.push_back(std::make_pair(CONTACT_ATTR::GROUP_CALL, std::string("1")));
    contact_kv.push_back(std::make_pair(CONTACT_ATTR::MASTER, std::string("1")));
    contact_kv.push_back(std::make_pair(CONTACT_ATTR::MATCH_DTMF1, GetSubstrFromBehind(info->account_.phone, PHONE_SUBSTR_DETECT_NUMBER)));
    contact_kv.push_back(std::make_pair(CONTACT_ATTR::MATCH_DTMF2, info->account_.sip_account));            
    contact_kv.push_back(std::make_pair(CONTACT_ATTR::SIP, info->account_.sip_account));
    contact_kv.push_back(std::make_pair(CONTACT_ATTR::LAND, GetAccountLandline(info->account_)));
    if (display == false)
    {
        contact_kv.push_back(std::make_pair(CONTACT_ATTR::IS_DISPLAY, std::string("0")));
    }    
}

void NewOfficeDevContact::GenerateDevContactList(ContactKvList &contact_kv, const OfficeDevPtr &own_dev, const OfficeDevPtr &dev, bool display)
{
    contact_kv.push_back(std::make_pair(CONTACT_ATTR::NAME, dev->location));
    contact_kv.push_back(std::make_pair(CONTACT_ATTR::UID, dev->sip));  
    contact_kv.push_back(std::make_pair(CONTACT_ATTR::MAC, dev->mac));
    contact_kv.push_back(std::make_pair(CONTACT_ATTR::RTSPPWD, dev->rtsppwd));
    contact_kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(dev->dev_type)));
    
    if (own_dev->net_group_number == dev->net_group_number)
    {                    
        contact_kv.push_back(std::make_pair(CONTACT_ATTR::SIP, std::string("")));
        contact_kv.push_back(std::make_pair(CONTACT_ATTR::IP, dev->ipaddr));      
    }
    else
    {                   
        contact_kv.push_back(std::make_pair(CONTACT_ATTR::SIP, dev->sip));
        contact_kv.push_back(std::make_pair(CONTACT_ATTR::IP, std::string("")));
    }
    if (display == false)
    {
        contact_kv.push_back(std::make_pair(CONTACT_ATTR::IS_DISPLAY, std::string("0")));
    }
    
    if (akjudge::DevDoorType(dev->dev_type) && IsNoMonitor(dev->firmwares))
    {
        contact_kv.push_back(std::make_pair(CONTACT_ATTR::NOT_MONITOR, std::string("1")));
    }
    else 
    {
        contact_kv.push_back(std::make_pair(CONTACT_ATTR::CAMERA_NUM, std::to_string(dev->camera_num)));
    }
    
    if (dev_door_info_map_.find(dev->uuid) != dev_door_info_map_.end())
    {
        WriteDoorRelayContact(dev->dev_type, dev_door_info_map_[dev->uuid], contact_kv);
    }
}


void NewOfficeDevContact::WriteXmlAccountContact(std::stringstream &config_body, ContactKvList &group_kv, ContactKvList &account_kv, MultipleContactKvList &multiple_dev_kv, MultipleContactKvList &admin_kv_list)
{
    GetGroupStr(config_body, group_kv);
    if (account_kv.size() > 0)
    {
        GetContactStr(config_body, account_kv);
    }

    for (auto &dev : multiple_dev_kv)
    {
        GetContactStr(config_body, dev);
    }
   
    for (auto &admin : admin_kv_list)        
    {
        GetContactStr(config_body, admin);
    }

    GroupEndStr(config_body);
}

void NewOfficeDevContact::WriteXmlDepartmentContact(std::stringstream &config_body, ContactKvList &group_kv, ContactKvList &account_kv, MultipleContactKvList &multiple_dev_kv)
{
    GetDepartmentStr(config_body, group_kv);
    if (account_kv.size() > 0)
    {
        GetContactStr(config_body, account_kv);      
    }

    for (auto &dev : multiple_dev_kv)
    {
        GetContactStr(config_body, dev);
    }

    DepartmentEndStr(config_body);
}


void NewOfficeDevContact::WriteXmlPubInfoDevContact(std::stringstream &config_body, MultipleContactKvList &multiple_dev_kv)
{
    config_body << "<PubInfo> \n";
    for (auto &dev : multiple_dev_kv)
    {
        GetContactStr(config_body, dev);
    }
    config_body << "</PubInfo>\n";
}


void NewOfficeDevContact::GenerateContact(const OfficeDevPtr &own_dev, const  ContactInfoList &show_list, std::stringstream &config_body)
{
    for (const auto& contact : show_list) 
    {
        ContactTypeInfo type = contact->GetType();
        if (type == ContactTypeInfo::ACCOUNT)
        {
            AccountContactInfoPtr account_ptr = std::dynamic_pointer_cast<AccountContactInfo>(contact);
            GenerateAccountContact(own_dev, account_ptr, config_body);
        }
        else if (type  == ContactTypeInfo::PUBINFO)
        {
            PubinfoContactInfoPtr pubinfo_ptr = std::dynamic_pointer_cast<PubinfoContactInfo>(contact);
            GeneratePubInfoContact(own_dev, pubinfo_ptr, config_body);          
        }
        else if(type == ContactTypeInfo::GROUP)
        {
            GroupContactInfoPtr group_ptr = std::dynamic_pointer_cast<GroupContactInfo>(contact);
            GenerateDepartmentContact(own_dev, group_ptr, config_body);                    
        }      
        else if(type == ContactTypeInfo::PUB_MANAGE_DEV)
        {
            PubManageDevContactInfoPtr manage_ptr = std::dynamic_pointer_cast<PubManageDevContactInfo>(contact);
            GeneratePubManageDevContact(own_dev, manage_ptr, config_body);                    
        } 
    }
 
}

void NewOfficeDevContact::GenerateAccountContact(const OfficeDevPtr &own_dev, const AccountContactInfoPtr &account_ptr, std::stringstream &config_body)
{

/*生成时候已经判断了
    if(!account_ptr->EnableIntercome())
    {
        AK_LOG_INFO << "account no display in contact. intercome off and no indoor. account uuid:" << account_ptr->account_.uuid << ". account name:" << account_ptr->account_.name;
        return;
    }
*/
    ContactKvList group_kv;
    ContactKvList account_kv;    
    MultipleContactKvList account_admin_kv_list;
    MultipleContactKvList dev_list_kv;
    GenerateAccountGroupContactList(account_ptr, group_kv);

    if (account_ptr->EnableIntercome())
    {
        //不显示在联系 seq也需要发，不然旧设备控制不了联系人展示，没有seq呼叫直接not found
        account_kv.push_back(std::make_pair(CONTACT_ATTR::SEQ_KEY, OfficeDevContactCallSeq::GetAccountCallSeq(account_ptr->GetCallType(), CallSeqType::CALL_SEQ_TYPE_APP)));             
        if (account_ptr->DisplayAccountContact())
        {
            GenerateAccountContactList(account_ptr, account_kv, true);
        }
        else
        {
            GenerateAccountContactList(account_ptr, account_kv, false);
            AK_LOG_INFO << "account no display in contact. account uuid:" << account_ptr->account_.uuid << ". account name:" << account_ptr->account_.name;
        }
    }
    
    if (account_ptr->UnitTiledDisplay())
    {
        group_kv.push_back(std::make_pair(CONTACT_ATTR::IS_UNIT_TILED_DISPLAY, std::string("1")));
    }
    
    for (auto &dev_it: account_ptr->dev_map_)
    {
        if(strcmp(own_dev->uuid, dev_it.second->uuid) == 0)
        {
            continue;
        }
        ContactKvList dev_kv;
        //设备是否展示在联系人跟随用户。
        GenerateDevContactList(dev_kv, own_dev, dev_it.second, account_ptr->DisplayDevContact());
        dev_kv.push_back(std::make_pair(CONTACT_ATTR::SEQ_KEY, OfficeDevContactCallSeq::GetAccountCallSeq(account_ptr->GetCallType(), CallSeqType::CALL_SEQ_TYPE_DEV)));
        dev_list_kv.push_back(dev_kv);
        AK_LOG_INFO << "account contact write mac:" << dev_it.second->mac;
    }


    for(const auto &indoor_admin_contact: account_ptr->indoor_admin_contacts_)
    {
        ContactKvList admin_kv;            
        admin_kv.push_back(std::make_pair(CONTACT_ATTR::SEQ_KEY, OfficeDevContactCallSeq::GetAccountCallSeq(indoor_admin_contact->GetCallType(), CallSeqType::CALL_SEQ_TYPE_APP)));             
        GenerateAccountContactList(indoor_admin_contact, admin_kv, true);
        account_admin_kv_list.push_back(admin_kv);
    }

    WriteXmlAccountContact(config_body, group_kv, account_kv, dev_list_kv, account_admin_kv_list);            
}


void NewOfficeDevContact::GeneratePubInfoContact(const OfficeDevPtr &own_dev, const PubinfoContactInfoPtr &pubinfo_ptr, std::stringstream &config_body)
{
    MultipleContactKvList dev_list_kv;
    for (auto &dev_it: pubinfo_ptr->dev_map_)
    {
        if(strcmp(own_dev->uuid, dev_it.second->uuid) == 0)
        {
            continue;
        }            
        ContactKvList dev_kv;
        //只有Group信息会根据是否开启对讲+是否展示在联系人进行display判断。
        GenerateDevContactList(dev_kv, own_dev, dev_it.second, true);
        dev_list_kv.push_back(dev_kv);
        AK_LOG_INFO << "pub info contact write mac:" << dev_it.second->mac;
    }
    WriteXmlPubInfoDevContact(config_body, dev_list_kv);
}

void NewOfficeDevContact::GenerateDepartmentContact(const OfficeDevPtr &own_dev, const  GroupContactInfoPtr &group_ptr, std::stringstream &config_body)
{
    ContactKvList group_kv;
    ContactKvList account_kv;
    MultipleContactKvList dev_list_kv;
    
    GenerateDepartmentHeadList(GetGroupInfo(group_ptr->group_uuid_), group_kv);
    
    for (auto &dev_it: group_ptr->dev_callseq_list_)
    {
        if(strcmp(own_dev->uuid, dev_it.first->uuid) == 0)
        {
            continue;
        }            
        ContactKvList dev_kv;
        //只有Group信息会根据是否开启对讲+是否展示在联系人进行display判断。
        GenerateDevContactList(dev_kv, own_dev, dev_it.first, true);
        if (group_ptr->IsNeedDevSeq(dev_it.second))
        {
            dev_kv.push_back(std::make_pair(CONTACT_ATTR::SEQ_KEY, OfficeDevContactCallSeq::GetGroupSeqCallSeq(dev_it.second, CallSeqType::CALL_SEQ_TYPE_DEV)));
        }
        dev_list_kv.push_back(dev_kv);
        AK_LOG_INFO << "group contact write mac:" << dev_it.first->mac;
    }

    for (auto &app_it: group_ptr->account_app_list_)
    {
        ContactKvList app_kv;
        //只有Group信息会根据是否开启对讲+是否展示在联系人进行display判断。
        GenerateAccountContactList(app_it.second, app_kv, true);
        
        std::string per_uuid = app_it.second->account_.uuid;
        app_kv.push_back(std::make_pair(CONTACT_ATTR::SEQ_KEY, OfficeDevContactCallSeq::GetGroupSeqAccountSeq(per_uuid, group_ptr->account_callseq_list_)));
        dev_list_kv.push_back(app_kv);
    }
    WriteXmlDepartmentContact(config_body, group_kv, account_kv, dev_list_kv);            
}

void NewOfficeDevContact::GeneratePubManageDevContact(const OfficeDevPtr &own_dev, const  PubManageDevContactInfoPtr &manage_ptr, std::stringstream &config_body)
{
    ContactKvList group_kv;
    ContactKvList account_kv;
    MultipleContactKvList dev_list_kv;
    
    GeneratePubManageDevHeadList(manage_ptr->company_uuid_, manage_ptr->dev_info_, group_kv);    

    if(strcmp(own_dev->uuid, manage_ptr->dev_info_->uuid) == 0)
    {
        return;
    }            
    ContactKvList dev_kv;
    GenerateDevContactList(dev_kv, own_dev, manage_ptr->dev_info_, true);
    //必须带seq，否则设备无法呼叫，都是只包含单个设备，写死即可
    dev_kv.push_back(std::make_pair(CONTACT_ATTR::SEQ_KEY, "1-2"));
    dev_list_kv.push_back(dev_kv);
    AK_LOG_INFO << "public manage contact write mac:" << manage_ptr->dev_info_->mac;

    WriteXmlDepartmentContact(config_body, group_kv, account_kv, dev_list_kv);            
}


void NewOfficeDevContact::FinalWriteContact(const OfficeDevPtr &dev, const  ContactInfoList &show_list)
{
    std::stringstream config_body;
    
    GetContactHeadStr(config_body);

    GenerateContact(dev, show_list, config_body);

    GetContactEndStr(config_body);
    
    //写入文件
    std::string config_path = config_root_path_ + dev->mac + "-Contact.xml";
    DevFileInfoPtr ptr = std::make_shared<DevFileInfo>(dev->mac, config_path, config_body.str(), SHADOW_TYPE::SHADOW_CONTACT,
                                                        project::OFFICE, dev->id);
    GetWriteFileControlInstance()->AddFileInfo(dev->mac, ptr);    
}


OfficeGroupDisplayType NewOfficeDevContact::GetGroupDispalyType(const std::string &group_uuid,  const GroupOfCompanyGroupMap &all_group_list)
{
    auto it2 = all_group_list.find(group_uuid);
    if (it2 != all_group_list.end())
    {
        return it2->second.display_type;
    }
    return OfficeGroupDisplayType::NOT_DISPLAY;
}


OfficeUUIDSet NewOfficeDevContact::GetPersonalGroupUUIDSet(const std::string &per_uuid)
{
    OfficeUUIDSet group_set;
    auto group_per_range = group_per_map_.equal_range(per_uuid);
    for (auto it = group_per_range.first; it != group_per_range.second; ++it) {
        const OfficePersonnelGroupInfo& value = it->second;
        group_set.insert(value.office_group_uuid);
    } 

    auto group_admin_range = group_admin_per_map_.equal_range(per_uuid);
    for (auto it = group_admin_range.first; it != group_admin_range.second; ++it) {
        const OfficeAdminGroupInfo& value = it->second;
        group_set.insert(value.office_group_uuid);
    }

    return std::move(group_set);
}

bool NewOfficeDevContact::GenerateIndoorAdminContact(AccountContactInfoPtr &account_contact_info)
{
    bool indoor_show_admin = false;
    //获取所属用户的公司
    std::string company_uuid = account_contact_info->GetPersonnelCompanyUUID();
    //获取公司所有的admin
    auto range = all_company_admin_map_.equal_range(company_uuid);
    for (auto company_admin_it = range.first; company_admin_it != range.second; ++company_admin_it)
    {
        auto admin_account_it = all_account_map_.find(company_admin_it->second.personal_account_uuid);
        if (admin_account_it == all_account_map_.end())
        {
            AK_LOG_WARN << "admin no in all_account_map_: per_account_uuid:" << company_admin_it->second.personal_account_uuid;    
            continue;    
        } 
        if(company_admin_it->second.app_status == (int)AdminAppStatus::DISABLE)
        {
            AK_LOG_WARN << "admin is closed, per_account_uuid:" << company_admin_it->second.personal_account_uuid;    
            continue;    
        }
        //构建admin的  ContactInfo,并存储到用户的对象中      
        AccountContactInfoPtr admin_contact_info = std::make_shared<AccountContactInfo>();
        admin_contact_info->SetAccountInfo(admin_account_it->second, company_admin_it->second);
        account_contact_info->PutIndoorAdminContact(admin_contact_info);
        indoor_show_admin = true;
    }

    return indoor_show_admin;
}


bool NewOfficeDevContact::GenerateAccountAndDevContact(const std::string &account_uuid, AccountContactInfoPtr &account_contact_info, ContactDevType type, OfficeGroupDisplayType group_display_type)
{
    //account
    auto account_it = all_account_map_.find(account_uuid);
    if (account_it == all_account_map_.end())
    {
        AK_LOG_WARN << "account no in all_account_map_: account_uuid:" << account_uuid;    
        return false;    
    }  

    bool have_dev = false;
    bool indoor_show_admin = false;
    if (account_it->second.role == ACCOUNT_ROLE_OFFICE_NEW_PER)
    {
        //personnel
        auto personnel_it = all_personnel_map_.find(account_uuid);
        if (personnel_it == all_personnel_map_.end())
        {
            AK_LOG_WARN << "account no in all_personnel_map: account_uuid:" << account_uuid;
            return false;
        }
        account_contact_info->SetAccountInfo(account_it->second, personnel_it->second);

        //用户的其他设备,当前只有给personnel分配设备的场景
        auto range = account_dev_per_map_.equal_range(account_uuid);
        for (auto it = range.first; it != range.second; ++it)
        {
            auto dev_it = all_dev_map_.find(it->second.devices_uuid);
            if (dev_it != all_dev_map_.end())
            {
                account_contact_info->AddDev(dev_it->second);
                have_dev = true;
            } 

        }
    }
    else if (account_it->second.role == ACCOUNT_ROLE_OFFICE_NEW_ADMIN)
    {
        //admin
        auto admin_it = all_admin_map_.find(account_uuid);
        if (admin_it == all_admin_map_.end())
        {
            AK_LOG_WARN << "account no in all_admin_map: account_uuid:" << account_uuid;
            return false;
        }
        account_contact_info->SetAccountInfo(account_it->second, admin_it->second);
    }
    
    account_contact_info->SetContactDisplayOptions(type, group_display_type);


    //室内机要展示admin联系人
    if(type == ContactDevType::INDOOR)
    {
        indoor_show_admin = GenerateIndoorAdminContact(account_contact_info);
    }
    
    //如果没有设备+没有开对讲+室内机没有admin 直接不显示这个用户了
    if (!have_dev && !indoor_show_admin && !account_contact_info->EnableIntercome())
    {
        return false;
    }
    return true;
}


//own_dev 要写入的设备
//permission_account_list 有权限的用户UUID列表
//permission_group_set 有权限的组UUID列表
//manage_dev_uuid_list 公共管理设备(即没有设置belongs to的管理机/室内机)
int NewOfficeDevContact::WritePublicDevContactFile(const OfficeDevPtr &own_dev, 
    const OfficeUUIDSet &permission_account_list, const OfficeUUIDSet &permission_group_set, const OfficeUUIDSet& manage_dev_uuid_list)
{
    ContactInfoList show_list;
    OfficeUUIDSet permission_company_uuid_list;

    //遍历组 看是否有按组显示的
    for(const auto &it : all_group_list_)
    {
        const OfficeGroupInfo group = it.second;
        if (permission_group_set.count(group.uuid) ==  0)
        {
            continue;    
        }
        permission_company_uuid_list.insert(group.office_company_uuid);
        
        if (group.display_type == OfficeGroupDisplayType::SHOW_GROUP)
        {
            AK_LOG_INFO << "group display type is group. group name:" << group_name_cb_(group.uuid, OfficeUUIDType::GROUP_UUID);
            //查找组呼叫列表
            OfficeGroupSeqCallMap seq_group_call;
            get_group_call_seq_cb_(group.uuid, seq_group_call);
            if (seq_group_call.size() == 0)
            {
                AK_LOG_INFO << "group display type is group. call seq is null.";
                continue;
            }
            
            GroupContactInfoPtr group_contact = std::make_shared<GroupContactInfo>();
            group_contact->SetUUID(group.uuid);
            TransformGroupSeqCall(seq_group_call, group_contact);            
            show_list.push_back(group_contact);
        }

    }
    
    //遍历有权限的用户列表
    for(const auto &account_uuid : permission_account_list)
    {
        //有权限的用户关联的group集合
        OfficeUUIDSet group_set = GetPersonalGroupUUIDSet(account_uuid);
        for (auto &group_uuid : group_set)
        {
            AccountContactInfoPtr account_contact_info = std::make_shared<AccountContactInfo>();
            account_contact_info->SetGroupUUID(group_uuid);
                    
            //查看用户组的显示类型
            OfficeGroupDisplayType group_display_type = GetGroupDispalyType(group_uuid, all_group_list_);

            if(!GenerateAccountAndDevContact(account_uuid, account_contact_info, ContactDevType::DOOR, group_display_type))
            {
                continue;
            }

            show_list.push_back(account_contact_info);         
        }
    } 

    //所有有权限的公司下都要写所有公共管理设备的联系人
    for(const auto &permission_company_uuid : permission_company_uuid_list)
    {
        //public door才写no belongs to的联系人
        if (pub_door_info_map_.find(own_dev->uuid) == pub_door_info_map_.end())
        {
            continue;
        }
        
        for (const auto &manage_dev_uuid : manage_dev_uuid_list)
        {            
            const auto &manage_dev = all_dev_map_.find(manage_dev_uuid);
            if (manage_dev != all_dev_map_.end())
            {            
                if(!CheckDevUnitPermission(own_dev, manage_dev->second)) 
                {
                    continue;
                }
                PubManageDevContactInfoPtr manage_contact = std::make_shared<PubManageDevContactInfo>();
                manage_contact->SetCompanyUUID(permission_company_uuid);
                manage_contact->AddDev(manage_dev->second);                
                show_list.push_back(manage_contact);
            }            
        }
    }
    
    FinalWriteContact(own_dev, show_list);   
    return 0;
}



int NewOfficeDevContact::WriteAccountIndoorDevContactFile(const OfficeDevPtr &own_dev, const std::string &account_uuid, 
    const OfficeUUIDSet &permission_pub_dev_list, const OfficeUUIDSet &company_other_dev_list)
{
    ContactInfoList show_list;
    
    AccountContactInfoPtr account_contact_info = std::make_shared<AccountContactInfo>();
    if(account_uuid.size() != 0)
    {
        if(!GenerateAccountAndDevContact(account_uuid, account_contact_info, ContactDevType::INDOOR, OfficeGroupDisplayType::NO_NEED))
        {
            AK_LOG_WARN << "GenerateAccountAndDevContact error no need write contact file. account_uuid" << account_uuid;
            return -1;
        }
        
        OfficeUUIDSet group_set = GetPersonalGroupUUIDSet(account_uuid);
        for (auto &uuid : group_set)
        {
            account_contact_info->SetGroupUUID(uuid); //一个人员属于多个组，对于室内机随机写一个即可
            break;
        }    
   }


    auto add_dev_func = [&](const OfficeUUIDSet& dev_list, bool only_add_manage_type) {
        for (const auto& dev_uuid : dev_list) {
            auto dev = all_dev_map_.find(dev_uuid);
            if (dev != all_dev_map_.end()) {
                if (only_add_manage_type && dev->second->dev_type != DEVICE_TYPE_MANAGEMENT )
                {
                    continue;
                }
                AK_LOG_INFO << "write dev. mac:" << dev->second->mac;
                account_contact_info->AddDev(dev->second);
            } else {
                AK_LOG_WARN << "write dev. dev_uuid not found. dev uuid:" << dev_uuid;
            }
        }
    };   

    add_dev_func(permission_pub_dev_list, false);
    add_dev_func(company_other_dev_list, true);//公司的只要写管理机

    show_list.push_back(account_contact_info);

    FinalWriteContact(own_dev, show_list);    
    return 0;    
}

    
int NewOfficeDevContact::WriteNoBelongsToIndoorDevContactFile(const OfficeDevPtr &own_dev, const OfficeUUIDSet &permission_pub_dev_list)
{
    ContactInfoList show_list;
    
    AccountContactInfoPtr account_contact_info = std::make_shared<AccountContactInfo>();
    //存在不属于任何用户(公共)，但是室内机也要显示联系人的情况，此时account为空
    account_contact_info->SetNoIntercome();


    auto add_dev_func = [&](const OfficeUUIDSet& dev_list) {
        for (const auto& dev_uuid : dev_list) {
            auto dev = all_dev_map_.find(dev_uuid);
            if (dev != all_dev_map_.end()) {
                if(!CheckDevUnitPermission(own_dev, dev->second))
                {
                    continue;
                }
                AK_LOG_INFO << "write dev. mac:" << dev->second->mac;
                account_contact_info->AddDev(dev->second);
            } else {
                AK_LOG_WARN << "write dev. dev_uuid not found. dev uuid:" << dev_uuid;
            }
        }
    };   

    add_dev_func(permission_pub_dev_list);

    show_list.push_back(account_contact_info);

    FinalWriteContact(own_dev, show_list);    
    return 0;    
}

//permission_pub_dev_list group下有权限的设备
//group_other_dev_list  其他设备关联这个组的设备列表
/*
1.若Belongs to的Group，则联系人列表显示同Group的所有人及他们的室内机和
管理机，+ group有权限的公共设备, + group的其他设备。点击后单呼 app或设备
2.若Belongs to人员，则联系人列表显示人员及他的室内机和管理机+ group有权限的公共设备，点击后单
呼 app或设备
*/
int NewOfficeDevContact::WriteGroupIndoorDevContactFile(const OfficeDevPtr &own_dev, const std::string &group_uuid,
  const OfficeUUIDSet &permission_pub_dev_list)
{
/* 当前没有分配给部门的设备 先注释掉
      ContactInfoList show_list;
      ContactInfoList no_show_list;

      //组下的用户  
       auto range = group_per_group_map_.equal_range(group_uuid);
       for (auto it = range.first; it != range.second; ++it)
       {
           auto account_uuid  = it->second.personal_account_uuid;
                       
            //查看用户组的显示类型
            //if (GetPersonalGroupDispalyType(account_uuid, all_group_list_) == OfficeGroupDisplayType::NOT_DISPLAY)
            //{
            //    continue;
            //}
            AccountContactInfoPtr account_contact_info = std::make_shared<AccountContactInfo>();
            if(!GenerateAccountAndDevContact(account_uuid, account_contact_info))
            {
                continue;
            }
            
            show_list.push_back(account_contact_info);      
       }

       PubinfoContactInfoPtr pub_contact_info = std::make_shared<PubinfoContactInfo>();
       for(const auto &dev_uuid : permission_pub_dev_list)
       {
           auto dev = all_dev_map_.find(dev_uuid);
           if (dev != all_dev_map_.end())
           {
               AK_LOG_INFO << "WriteGroupIndoorDevContactFile permission pub dev. mac:" << dev->second->mac;
               pub_contact_info->AddDev(dev->second);
           }
       } 
       for(const auto &dev_uuid : group_other_dev_list)
       {      
           auto dev = all_dev_map_.find(dev_uuid);
           if (dev != all_dev_map_.end())
           {
               AK_LOG_INFO << "WriteGroupIndoorDevContactFile group other. mac:" << dev->second->mac;            
               pub_contact_info->AddDev(dev->second);
           }
       }  
       FinalWriteContact(own_dev, show_list, no_show_list);
*/       
    return 0;
}

int NewOfficeDevContact::WriteMngDevContactFile(bool have_belongs_to, const OfficeDevPtr &own_dev, const OfficeUUIDSet &company_uuids, 
          const OfficeUUIDSet &permission_pub_dev_list)
{
    ContactInfoList show_list;

    if(!company_uuids.empty())
    {
        for(const auto &account : all_account_map_)
        {
            std::string company_uuid = get_company_uuid_cb_(account.second.uuid, OfficeUUIDType::PER_UUID);
            if(company_uuids.find(company_uuid) == company_uuids.end())
            {
                continue;
            }

            OfficeUUIDSet group_set = GetPersonalGroupUUIDSet(account.second.uuid);
            for (auto &group_uuid : group_set)
            {
                AccountContactInfoPtr account_contact_info = std::make_shared<AccountContactInfo>();
                account_contact_info->SetGroupUUID(group_uuid);
                if(!GenerateAccountAndDevContact(account.second.uuid, account_contact_info, ContactDevType::MANAGER, OfficeGroupDisplayType::NO_NEED))
                {
                    continue;
                }
                
                show_list.push_back(account_contact_info);  
            }      
        }   
    }

   PubinfoContactInfoPtr pub_contact_info = std::make_shared<PubinfoContactInfo>();
   for(const auto &dev_uuid : permission_pub_dev_list)
   {
       auto dev = all_dev_map_.find(dev_uuid);
       if (dev != all_dev_map_.end())
       {
            if(!have_belongs_to && !CheckDevUnitPermission(own_dev, dev->second))
            {
                continue;
            }
           AK_LOG_INFO << "WriteMngDevContactFile permission pub dev. mac:" << dev->second->mac;
           pub_contact_info->AddDev(dev->second);
       }
   }
   show_list.push_back(pub_contact_info);

   FinalWriteContact(own_dev, show_list);
   return 0;
}


int NewOfficeDevContact::CleanContact(const OfficeDevPtr &own_dev)
{
    ContactInfoList show_list;
    FinalWriteContact(own_dev, show_list);
    return 0;
}

