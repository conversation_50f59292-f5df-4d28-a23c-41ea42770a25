#ifndef __New_OFFICE_DEV_CONTACT_H__
#define __New_OFFICE_DEV_CONTACT_H__
#include <string>
#include <map>
#include "AKCSMsg.h"
#include "InnerDbDef.h"
#include "ContactCommon.h"
#include "dbinterface/new-office/DevicesDoorList.h"
#include "OfficeNew/ConfigFile/OfficeNewConfigHandle.h"
#include "OfficeNew/ConfigFile/OfficeNewContactInfo.h"


using GetCompanyUUIDCb = std::function<std::string(const std::string&, OfficeUUIDType type)>;
using GetGroupCallSeqCb = std::function<void(const std::string &group_uuid, OfficeGroupSeqCallMap &call_seq_map)>;

class NewOfficeDevContact
{
public:
    
	NewOfficeDevContact(const std::string& config_root_path, const OfficeInfoPtr &office_info)
    {
        config_root_path_ = config_root_path;
        office_info_ = office_info;
    }


	~NewOfficeDevContact()
    {
        
    }
    
    void InitUserInfo(const OfficeAccountMap &all_account_list,
        const OfficePersonnelMap &all_personnel_list,
        const OfficeDevMap &all_dev_map,
        const GroupOfPerPerMap &group_per_map,
        const GroupOfPerGroupMap &group_per_group_map,
        const OfficeDeviceAssignPerMap &account_dev_per_map,
        const GroupOfCompanyGroupMap &all_group_list,
        const OfficeAdminMap &all_admin_list,
        const GroupOfAdminPerMap &group_admin_per_map,
        const GroupOfAdminGroupMap &group_admin_group_map,
        const OfficeCompanyAdminMap &all_company_admin_map)
    {
        all_account_map_ = all_account_list;
        all_personnel_map_ = all_personnel_list;
        all_dev_map_ = all_dev_map;
        group_per_map_ = group_per_map;
        group_per_group_map_ = group_per_group_map;
        account_dev_per_map_ = account_dev_per_map;
        all_group_list_ = all_group_list;
        all_admin_map_ = all_admin_list;
        all_company_admin_map_ = all_company_admin_map;
        group_admin_per_map_ = group_admin_per_map;
        group_admin_group_map_ = group_admin_group_map;
    }
    void SetSipGroupMap(const ProjectSipGroupMap &sip_group_map)
    {
        account_sip_group_map_ = sip_group_map;
    }

    void SetDevDoorInfoMap(const DevicesDoorInfoMap& dev_door_info_map)
    {
        dev_door_info_map_ = dev_door_info_map;
    }
    
    void SetDevPublicDoorInfoMap(const DevicesPublicDoorInfoMap& pub_door_info_map)
    {
        pub_door_info_map_ = pub_door_info_map;
    }
    
    void SetNameInfoCb(const GetCompanyNameCb &fun, const GetGroupNameCb &group_fun)
    {
        company_name_cb_ = fun;
        group_name_cb_ = group_fun;
    }   

    void SetCompanyUUIDCb(const GetCompanyUUIDCb &fun)
    {
        get_company_uuid_cb_ = fun;
    } 

    void SetGetGroupSeqCallCb(const GetGroupCallSeqCb &fun)
    {
        get_group_call_seq_cb_ = fun;
    }     

    int WritePublicDevContactFile(const OfficeDevPtr &dev, const OfficeUUIDSet &permission_account_list, const OfficeUUIDSet &permission_group_set, const OfficeUUIDSet& manage_dev_uuid_list);
    int WriteAccountIndoorDevContactFile(const OfficeDevPtr &your_dev, const std::string &account_uuid, 
          const OfficeUUIDSet &permission_pub_dev_list, const OfficeUUIDSet &company_othre_dev_list);
   
    int WriteNoBelongsToIndoorDevContactFile(const OfficeDevPtr &own_dev, const OfficeUUIDSet &permission_pub_dev_list);
    int WriteGroupIndoorDevContactFile(const OfficeDevPtr &your_dev, const std::string &group_uuid,
        const OfficeUUIDSet &permission_pub_dev_list);

    int WriteMngDevContactFile(bool have_belongs_to, const OfficeDevPtr &your_dev, const OfficeUUIDSet &company_uuids, 
          const OfficeUUIDSet &permission_pub_dev_list);
    int CleanContact(const OfficeDevPtr &own_dev);                          
private:
    inline void GenerateAccountGroupContactList(const AccountContactInfoPtr &info, ContactKvList &group_kv);
    inline void GenerateAccountContactList(const AccountContactInfoPtr &info, ContactKvList &contact_kv, bool display);
    inline void GenerateDevContactList( ContactKvList &contact_kv, const OfficeDevPtr &dev, const OfficeDevPtr &cur_dev, bool display);
    inline void GenerateDepartmentHeadList(const OfficeGroupInfo &info, ContactKvList &group_kv);
    inline void GeneratePubManageDevHeadList(const string &company, const OfficeDevPtr& dev_info, ContactKvList &group_kv);


    void FinalWriteContact(const OfficeDevPtr &dev, const  ContactInfoList &show_list);
    void WriteXmlAccountContact(std::stringstream &config_body, ContactKvList &group_kv, ContactKvList &account_kv, MultipleContactKvList &multiple_dev_kv, MultipleContactKvList &admin_kv_list);
    void WriteXmlDepartmentContact(std::stringstream &config_body, ContactKvList &group_kv, ContactKvList &account_kv, MultipleContactKvList &multiple_dev_kv);
    void WriteXmlPubInfoDevContact(std::stringstream &config_body, MultipleContactKvList &multiple_dev_kv);
    
    void GenerateContact(const OfficeDevPtr &dev, const  ContactInfoList &show_list, std::stringstream &config_body);
    inline void GenerateAccountContact(const OfficeDevPtr &own_dev, const AccountContactInfoPtr &account_ptr, std::stringstream &config_body);
    inline void GeneratePubInfoContact(const OfficeDevPtr &own_dev, const PubinfoContactInfoPtr &pubinfo_ptr, std::stringstream &config_body);
    inline void GenerateDepartmentContact(const OfficeDevPtr &own_dev, const  GroupContactInfoPtr &group_ptr, std::stringstream &config_body);
    inline void GeneratePubManageDevContact(const OfficeDevPtr &own_dev, const  PubManageDevContactInfoPtr &manage_ptr, std::stringstream &config_body);
    inline bool GenerateIndoorAdminContact(AccountContactInfoPtr &account_contact_info);

    bool GenerateAccountAndDevContact(const std::string &account_uuid, AccountContactInfoPtr& account_contact_info, ContactDevType type, OfficeGroupDisplayType group_display_type); 
    std::string GetContactNameByOrder(const OfficeAccount &account);

    OfficeGroupDisplayType GetGroupDispalyType(const std::string &group_uuid,  const GroupOfCompanyGroupMap &all_group_list); 
    OfficeUUIDSet GetPersonalGroupUUIDSet(const std::string &per_uuid);
    
    OfficeGroupInfo  GetGroupInfo(const std::string &group_uuid);
    inline std::string GetAccountLandline(const OfficeAccount &account);
    
    void TransformGroupSeqCall(const OfficeGroupSeqCallMap &seq_call, GroupContactInfoPtr &contact);
    inline std::string GetAccountSipGroup(const std::string &per_uuid);

    bool IsNoMonitor(int firmware)
    {
        if(no_motion_list_.size() == 0)
        {
            dbinterface::VersionModel::GetNoMonitorList(no_motion_list_);
        }
        return no_motion_list_.count(firmware);
    }

    ProjectSipGroupMap account_sip_group_map_;
    std::string config_root_path_;
    OfficeInfoPtr office_info_;
    FirmwareList no_motion_list_;

    OfficeAccountMap all_account_map_; //项目所有的用户列表
    OfficePersonnelMap all_personnel_map_;//项目所有的personnel列表
    OfficeAdminMap all_admin_map_; //项目所有的admin列表
    OfficeCompanyAdminMap all_company_admin_map_;//项目所有company下的admin列表
    OfficeDevMap all_dev_map_;//项目所有的设备 通过用户和设备的映射关系来这里找设备详情
    GroupOfPerPerMap group_per_map_;
    GroupOfPerGroupMap group_per_group_map_;
    GroupOfAdminPerMap group_admin_per_map_;
    GroupOfAdminGroupMap group_admin_group_map_;
    OfficeDeviceAssignPerMap account_dev_per_map_; 
    GroupOfCompanyGroupMap all_group_list_;  
    
    GetCompanyNameCb company_name_cb_;
    GetGroupNameCb group_name_cb_;
    GetCompanyUUIDCb get_company_uuid_cb_;
    GetGroupCallSeqCb get_group_call_seq_cb_;
    DevicesDoorInfoMap dev_door_info_map_;
    DevicesPublicDoorInfoMap pub_door_info_map_;
};

#endif


