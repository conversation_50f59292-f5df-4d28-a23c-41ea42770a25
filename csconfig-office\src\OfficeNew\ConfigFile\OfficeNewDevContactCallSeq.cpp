#include <sstream>
#include "OfficeNew/ConfigFile/OfficeNewDevContactCallSeq.h"
#include <string.h>
#include "AkcsCommonDef.h"



/*确定呼叫顺序
比如：2-2;3-1 代表第二组呼叫，取SIP或IP呼叫。加代表第三组呼叫，取SIP0
    因为app有落地要呼叫，所以一条联系人可能会存在于多个呼叫组，用分号分隔。
SIP0(uid)  SIP(phone)
SIP0为下标1，SIP/IP为下标2，land下标为3
seq2是因为从账号加了落地号码，也需要按照calltype的顺序，因为群呼时候的落地又不包括从账号的落地，所以需要新增字段
开启的calltype选项只有呼叫phone，没有呼叫app，如果从账号没有配置phone，会呼叫app。平台会把land的选项配置为sip账号

*/

std::string OfficeDevContactCallSeq::GetAccountCallSeq(int call_type, CallSeqType type)
{
    std::string seq;
    if (type == CallSeqType::CALL_SEQ_TYPE_DEV)
    {
        seq = "1-2";//根据calltype所有的设备呼叫都是1-2;
        return std::move(seq);
    }

    if (call_type == NODE_CALL_TYPE_APP_INDOOR)
    {
        seq = "1-2";
    }
    else if (call_type == NODE_CALL_TYPE_INDOOR_PHONE)
    {
        seq = "1-3";
    }
    else if (call_type == NODE_CALL_TYPE_APP_INDOOR_BACK_PHONE)
    {
        seq = "1-2;2-3";
    }    
    else if (call_type == NODE_CALL_TYPE_INDOOR_BACK_APP)
    {
        seq = "2-2";       
    }
    else if (call_type == NODE_CALL_TYPE_INDOOR_BACK_PHONE)
    {
        seq = "2-3";   
    }
    else if (call_type == NODE_CALL_TYPE_INDOOR_BACK_APP_BACK_PHONE)
    {
        seq = "2-2;3-3";        
    }
    return std::move(seq);
}

std::string OfficeDevContactCallSeq::GetGroupSeqCallSeq(int call_order, CallSeqType type)
{
    std::string seq = std::to_string(call_order);
    if (type == CallSeqType::CALL_SEQ_TYPE_DEV)
    {
        seq += "-2";
    }
    else if (type == CallSeqType::CALL_SEQ_TYPE_LANDLINE)
    {
        seq += "-3";
    }
    else if (type == CallSeqType::CALL_SEQ_TYPE_APP)
    {
        seq += "-2";
    }          
    return std::move(seq);
}

//group seq 返回用户的呼叫顺序
std::string OfficeDevContactCallSeq::GetGroupSeqAccountSeq(const std::string &per_uuid, const GroupSeqCallSeqMap &seq_map)
{
    std::string call_seq;
    auto range = seq_map.equal_range(per_uuid);
    for (auto it = range.first; it != range.second; ++it)
    {
        const GroupCallSeq &info = it->second;
        if (info.type_ == CallSeqType::CALL_SEQ_TYPE_APP)
        {
            std::string order = std::to_string(info.order_);
            order += "-2;";
            call_seq = call_seq + order;
        }
        else if (info.type_ == CallSeqType::CALL_SEQ_TYPE_LANDLINE)
        {
            std::string order = std::to_string(info.order_);
            order += "-3;";
            call_seq = call_seq + order;
        }
    }
    return std::move(call_seq);
}


