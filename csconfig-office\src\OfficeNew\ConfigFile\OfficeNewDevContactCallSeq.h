#ifndef __New_OFFICE_DEV_CONTACT_CALL_SEQ_H__
#define __New_OFFICE_DEV_CONTACT_CALL_SEQ_H__
#include <string>
#include <map>
#include "AkcsCommonDef.h"


class GroupCallSeq
{
public:
    GroupCallSeq(CallSeqType type, int order):type_(type),order_(order)
    {

    }
    CallSeqType type_;
    int order_;
};


using  GroupSeqCallSeqMap = std::multimap<std::string/*per_uuid*/, GroupCallSeq>;


class OfficeDevContactCallSeq
{
public:
    
    static std::string GetAccountCallSeq(int call_type, CallSeqType type);
    static std::string GetGroupSeqCallSeq(int call_seq, CallSeqType type);  
    static std::string GetGroupSeqAccountSeq(const std::string &per_uuid, const GroupSeqCallSeqMap &seq_map);
    
private:

};




#endif


