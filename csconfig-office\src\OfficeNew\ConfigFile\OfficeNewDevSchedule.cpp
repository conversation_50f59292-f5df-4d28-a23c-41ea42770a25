#include <sstream>
#include "OfficeNewDevSchedule.h"
#include <string.h>
#include "BasicDefine.h"
#include "json/json.h"
#include "util.h"
#include "AkLogging.h"
#include "ShadowMng.h"
#include "WriteFileControl.h"

int NewOfficeDevSchedule::WirtePubSchedule(const OfficeDevPtr &dev)
{
    //获取设备包含的所有权限组列表
    OfficeUUIDSet ag_uuid_set;
    auto range = ag_dev_map_.equal_range(dev->uuid);
    if (range.first == range.second)
    {
        AK_LOG_INFO << "write schedule devices have not access group. mac:" << dev->mac;
        return -1;
    }
    for (auto it = range.first; it != range.second; ++it)
    {
        std::string ag_uuid = it->second.office_access_group_uuid;
        ag_uuid_set.insert(ag_uuid);
    } 

    //权限组详情。一个权限组包含多个时间段
    OfficeAccessGroupInfoList ag_list;
    for (auto &ag_uuid : ag_uuid_set)
    {
        auto range = ag_info_map_.equal_range(ag_uuid);
        for (auto it = range.first; it != range.second; ++it)
        {
            ag_list.push_back(it->second);
        }         
    }
    if (ag_list.size() == 0)
    {
        AK_LOG_INFO << "write schedule devices have not schedule. mac:" << dev->mac;
        return -1;
    }    
    WirteScheduleInfo(dev, ag_list);
    return 0;    
}



void NewOfficeDevSchedule::WirteScheduleInfo(const OfficeDevPtr &dev, const OfficeAccessGroupInfoList &ag_list)
{
    Json::Value item;
    Json::FastWriter w;
    item["UserType"] = 0;//0表示云，1表示ACMS;
    Json::Value sche;

    for (auto &ag : ag_list)
    {
        Json::Value info;
        info["Name"] = ag.name;

        char daily[128] = "";
        snprintf(daily, sizeof(daily), "%s-%s", ag.start_time, ag.stop_time);        
        if (ag.scheduler_type == SchedType::ONCE_SCHED)
        {
            info["ID"] = ag.id;
            info["Type"] = static_cast<int>(DevSchedType::DEV_SCHE_ONCE_SCHED);
            char dayinfo[128] = "";
            snprintf(dayinfo, sizeof(dayinfo), "%s-%s", ag.begin_date_time, ag.end_date_time);             
            info["Date"] = dayinfo;
            info["Weekly"] = WeekBinaryToString(ag.date_flag);
            info["Daily"] = daily;
        }
        else if (ag.scheduler_type == SchedType::WEEKLY_SCHED)
        {
            info["ID"] = ag.id;
            info["Type"] = static_cast<int>(DevSchedType::DEV_SCHE_WEEKLY_SCHED);
            info["Date"] = "";
            info["Weekly"] = WeekBinaryToString(ag.date_flag);
            info["Daily"] = daily;
        }
        else if (ag.scheduler_type == SchedType::DAILY_SCHED)
        {
            info["ID"] = ag.id;
            info["Type"] = static_cast<int>(DevSchedType::DEV_SCHE_DAILY_SCHED);
            info["Date"] = "";
            info["Weekly"] = "";
            info["Daily"] = daily;
        }

        item["Schedule"].append(info);
    }     
    for(auto &it : holiday_json_)
    {
        item["Holiday"].append(it);
    }
    
    std::string msg_json = w.write(item);

    //写入文件
    std::string config_path = config_root_path_;
    config_path += dev->mac;
    config_path += "-schedule.json";    
    DevFileInfoPtr ptr = std::make_shared<DevFileInfo>(dev->mac, config_path, msg_json, SHADOW_TYPE::SHADOW_SCHE,
                                                        project::OFFICE, dev->id);
    GetWriteFileControlInstance()->AddFileInfo(dev->mac, ptr);    
}

void NewOfficeDevSchedule::CreateHolidayInfo()
{
    for (auto &it : project_holiday_map_)
    {
        const OfficeHolidayInfo &info = it.second;  
        CreateHolidayInfoDetail(info);
    }    

    for (auto &it : company_holiday_map_)
    {
        const OfficeHolidayInfo &info = it.second;  
        CreateHolidayInfoDetail(info);
    }        
}

void NewOfficeDevSchedule::CreateHolidayInfoDetail(const OfficeHolidayInfo &info)
{
    Json::Value holiday;
    holiday["Name"] = info.name;
    holiday["RepeatByYear"] = info.is_year_repeat;
    if(info.is_all_company)
    {
        holiday["CompanyUUID"] = "";
    }
    else
    {
        holiday["CompanyUUID"] = info.company_uuid;
    }
    
    if (info.is_working_hours)
    {
        char worktime[128] = "";
        snprintf(worktime, sizeof(worktime), "%s-%s", info.start_time, info.stop_time);                  
        holiday["WorkTime"] = worktime;
    }
    else
    {
        holiday["WorkTime"] = "";
    }

    Json::Value date_slot(Json::arrayValue);
    Json::Value root;
    Json::Reader reader;
    bool suc = reader.parse(info.dates, root);
    
    if (suc && root.isArray()) {
        for (int i = 0; i < root.size(); ++i)
        {
            Json::Value date_range;
            char date[128] = "";
            snprintf(date, sizeof(date), "%s-%s", info.year, root[i].asString().c_str());
            date_range["Begin"] = date;
            date_range["End"] = date;
            
            date_slot.append(date_range);
            
        }
    }
    holiday["DateSlot"] = date_slot;
    
    holiday_json_.push_back(holiday);
       
}


