#ifndef __NEW_OFFICE_DEV_SCHEDULE_H__
#define __NEW_OFFICE_DEV_SCHEDULE_H__
#include <string>
#include "AKCSMsg.h"
#include "OfficeNew/ConfigFile/OfficeNewConfigHandle.h"
#include "json/json.h"



//传给设备的类型(和平台数据库不一致)
enum class DevSchedType
{
    DEV_SCHE_ONCE_SCHED = 0,
    DEV_SCHE_WEEKLY_SCHED,
    DEV_SCHE_DAILY_SCHED,
};


class NewOfficeDevSchedule
{

public:
	NewOfficeDevSchedule(const std::string& config_root_path)
    {
        config_root_path_ = config_root_path;
    }
    void InitInfo(const AgDevInfoDevMap &ag_dev_map,
        const AccessGroupUUIDMap &ag_info_map,
        const ProjectHolidayMap project_holiday_map,
        const CompanyHolidayMap company_holiday_map)
    {
        ag_dev_map_ = ag_dev_map;
        ag_info_map_ = ag_info_map;
        project_holiday_map_ = project_holiday_map;
        company_holiday_map_ = company_holiday_map;
        CreateHolidayInfo();
    }

	~NewOfficeDevSchedule(){}

    int WirtePubSchedule(const OfficeDevPtr &dev);

    
    
private:
    //不区分设备是否有哪个公司的权限，直接全部下发
    void CreateHolidayInfo();
    void CreateHolidayInfoDetail(const OfficeHolidayInfo &info);
    void WirteScheduleInfo(const OfficeDevPtr &dev, const OfficeAccessGroupInfoList &ag_list);
    std::string config_root_path_;

    std::vector<Json::Value> holiday_json_;
    
    AgDevInfoDevMap ag_dev_map_;
    AccessGroupUUIDMap ag_info_map_;
    ProjectHolidayMap project_holiday_map_;
    CompanyHolidayMap company_holiday_map_;
          
};


#endif 



