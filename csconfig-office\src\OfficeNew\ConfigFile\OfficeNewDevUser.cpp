#include <sstream>
#include "WriteFileControl.h"
#include "OfficeNewDevUser.h"
#include <string.h>
#include "BasicDefine.h"
#include "json/json.h"
#include "util.h"
#include "AkLogging.h"
#include "AkcsCommonDef.h"
#include "ConfigDef.h" 
#include "Md5.h"
#include "OfficeNewConfigHandleTool.h"


extern CSCONFIG_CONF gstCSCONFIGConf;

// 超时时间 60 秒 * 30 分钟
constexpr int FACE_URL_TIMEOUT = 60 * 30;

enum UuidMode
{
    MODE_DELIVERY = 'D',
    MODE_STAFF = 'S'
};
    

/*重新组装url，加入token和过期时间，Add by czw*/
static int ModifyUrlAddToken(std::string& url)
{
    std::string token;
    std::string uri;
    size_t pos = url.find("/group");
    if (pos != std::string::npos)
    {
        uri = url.substr(pos);  //截取出uri
    }
    else
    {
        pos = url.find("/download");
        if (pos != std::string::npos)
        {
            uri = url.substr(pos);
        }
        else
        {
            AK_LOG_WARN << "ModifyUrlAddToken failed,url=" << url;
            return false;
        }
    }

    time_t timer = time(nullptr) + FACE_URL_TIMEOUT; 
    char time_sec[16] = {0};
    ::snprintf(time_sec, sizeof(time_sec), "%ld", timer);

    /* token生成方式：base64Encode(Md5(ak_download:path:time_overdue)) */
    std::string form_token = "ak_download:";
    form_token += uri;
    form_token += ":";
    form_token += time_sec;

    char* token_pointer = akuvox_encrypt::MD5(form_token).GetBase64Md5();
    if (!token_pointer)
    {
        AK_LOG_WARN << "GetBase64Md5 failed.";
        return false;
    }
    else
    {
        token = token_pointer;
        free(token_pointer);
    }

    /* 最终的url：https://192.168.11.112:443/download/personal/node_60/10000100004/Config/0C110507C7FF.cfg?token=oV_3cnoyCQ6VxbFRLFGd1Q==&e=1577811600 */
    StringReplace(token, "+", "-");
    StringReplace(token, "/", "_");  //nginx防盗链要求token中+/转换为-_
    url += "?token=";
    url += token;
    url += "&e=";
    url += time_sec;

    return true;
}


static std::string GetPicFilePath(const std::string &mac)
{
    char pic_file_path[128];
    if (strlen(gstCSCONFIGConf.config_server_domain) > 0
        && static_cast<int>(std::hash<std::string>{}(mac) % 100) < gstCSCONFIGConf.config_server_domain_gray_percentage)
    {
        snprintf(pic_file_path, sizeof(pic_file_path), "https://%s:8091/download/face", gstCSCONFIGConf.config_server_domain);
    }
    else
    {
        snprintf(pic_file_path, sizeof(pic_file_path), "https://%s/download/face", gstCSCONFIGConf.fdfs_config_addr);
    }
    return pic_file_path;
}

static  std::string GetPicFileDownloadPath()
{
    char pic_file_path[128];
    //设备的url只有128个字节 加入防盗链要注意长度(有download/fac会超过) add by zhenxing.chen 20240812
    snprintf(pic_file_path, sizeof(pic_file_path), "https://%s/download/face", gstCSCONFIGConf.fdfs_config_addr);
    return pic_file_path;
}

int NewOfficeDevUser::GetUserDetailFilePath(const OfficeDevPtr &dev, uint64_t traceid, std::string &write_path, std::string &download_path)
{
    std::string filename;
    write_path = GetUserDetailDownloadPath(dev->mac, download_path);
    
    filename += dev->mac;
    filename += "_";
    filename += GetNowTime();
    filename += "_";
    filename += std::to_string(traceid);
    filename += ".json";

    write_path += filename;
    download_path += filename;

    return 0;
}


std::string NewOfficeDevUser::CreateSpecialUUID(USER_TYPE      type,uint32_t id)
{
    char uuid[64] = "";
    if (USER_TYPE::DELIVERY == type)
    {
        snprintf(uuid, sizeof(uuid), "D%09d", id);
    }
    else if (USER_TYPE::STAFF == type)
    {
        snprintf(uuid, sizeof(uuid), "S%09d", id);
    }
    return std::move(uuid);
}

std::string NewOfficeDevUser::GetAccountPinList(const std::string &uuid, int user_can_create_pin)
{
    std::stringstream pinlist;
    auto per = user_access_.account_pin_map_.equal_range(uuid);
    for (auto it = per.first; it != per.second; ++it)
    {
        const PinCodeInfo& pin = it->second;
        if ((pin.creator_type == AccessCreatorType::END_USER && pin.type == PinCodeType::Account)
        || (pin.creator_type == AccessCreatorType::ADMIN && pin.type == PinCodeType::Admin))
        {
            if (user_can_create_pin)
            {
                pinlist <<  pin.code  << ";";
            }
        }
        else //其他管理员的可以直接拼接
        {
            pinlist <<  pin.code  << ";";
        }
    } 
    return std::move(pinlist.str());
}

std::string NewOfficeDevUser::GetAccountRfList(const std::string &uuid, const OfficeAccount &info)
{
    std::stringstream rflist;
    auto per = user_access_.account_rf_map_.equal_range(uuid);
    for (auto it = per.first; it != per.second; ++it)
    {
        const RfCardInfo& rf = it->second;
        rflist << rf.code << ";";
    }
    if (strlen(info.ble_code) > 0)
    {
        rflist << info.ble_code << ";";
    }
    if (strlen(info.nfc_code) > 0)
    {
        rflist << info.nfc_code << ";";
    }

    return std::move(rflist.str());
}


std::string NewOfficeDevUser::GetDeliveryRfList(const std::string &uuid)
{
    std::stringstream rflist;
    auto per = user_access_.delivery_rf_map_.equal_range(uuid);
    if(per.first == per.second)
    {
        AK_LOG_INFO << "Delivery rf is null. delivery uuid:" << uuid;
        return ""; 
    }    
    for (auto it = per.first; it != per.second; ++it)
    {
        const RfCardInfo& rf = it->second;
        rflist << rf.code << ";";
    }
    return std::move(rflist.str());
}

std::string NewOfficeDevUser::GetDeliveryPinList(const std::string &uuid)
{
    std::stringstream pinlist;
    auto per = user_access_.delivery_pin_map_.equal_range(uuid);
    if(per.first == per.second)
    {
        AK_LOG_INFO << "Delivery Pin is null. delivery uuid:" << uuid;
        return ""; 
    }
    for (auto it = per.first; it != per.second; ++it)
    {
        const PinCodeInfo& pin = it->second;
        pinlist <<  pin.code  << ";";
    }
    return std::move(pinlist.str());
}

std::string NewOfficeDevUser::GetDeliveryFloorList(const OfficeDevPtr &dev, const std::string &uuid)
{
    std::string floor;
    if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
    {
        AK_LOG_INFO << "public dev not support LiftFloor:" << dev->mac;
        return std::move(floor);
    }    
    auto per = user_access_.delivery_access_floor_.equal_range(uuid);
    if (per.first == per.second)
    {
        AK_LOG_INFO << "delivery access not band access floor. delivery uuid" << uuid;
        return std::move(floor);
    }    
    for (auto it = per.first; it != per.second; ++it)
    {
        const OfficeDeliveryAccessFloorInfo& delivery_floor = it->second;
        if (strcmp(dev->unit_uuid, delivery_floor.community_unit_uuid) == 0)//和设备同一个楼栋的
        {
            floor = GetAccessibleFloor(floor, delivery_floor.floors);
            break;//只会有一条数据
        }
    } 
    return std::move(floor);
}

std::string NewOfficeDevUser::GetAccountFloorList(const OfficeDevPtr &dev, const std::string &per_uuid, int role)
{
    std::string floor;    
    if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
    {
        AK_LOG_INFO << "public dev not support LiftFloor:" << dev->mac;
        return std::move(floor);
    }
    
    OfficeUUIDSet group_set = get_account_group_uuid_cb_(per_uuid, role);
    
    if (group_set.size() == 0)
    {
        AK_LOG_INFO << "per uuid not found group, so floor is null. per_uuid:" << per_uuid;
        return std::move(floor);
    }
        
    for(auto &group_uuid : group_set)
    {
        auto per = user_access_.group_access_floor_.equal_range(group_uuid);
        if (per.first == per.second)
        {   
            AK_LOG_INFO << "group not band access floor, it will default to the company's accessible floor settings. group uuid" << group_uuid;
            //group没有设置，默认跟随company的access floor
            auto company_it = user_access_.group_company_uuid_map_.find(group_uuid);
            if (company_it == user_access_.group_company_uuid_map_.end()) 
            {
                AK_LOG_INFO << "GroupUUID not found in group_company_uuid_map: " << group_uuid;
                continue;
            }
        
            // 找到了对应的 company uuid
            const std::string& company_uuid = company_it->second;
        
            auto company_floor_it = user_access_.company_access_floor_.equal_range(company_uuid);
            if (company_floor_it.first == company_floor_it.second)
            {
                AK_LOG_INFO << "CompanyUUID not found in company_access_floor: " << company_uuid;
                continue;
            }

            // 找到了对应的 company access floor
            for (auto it = company_floor_it.first; it != company_floor_it.second; ++it)
            {   
                const OfficeCompanyAccessFloorInfo& company_floor = it->second;
                if (strcmp(dev->unit_uuid, company_floor.community_unit_uuid) == 0)
                {
                    floor = GetAccessibleFloor(floor, company_floor.access_floors);
                }
            } 
        }
        for (auto it = per.first; it != per.second; ++it)
        {
            const OfficeGroupAccessFloorInfo& group_floor = it->second;
            if (strcmp(dev->unit_uuid, group_floor.community_unit_uuid) == 0)
            {
                //会有多条数据 每个group的对同个一个building的楼层会不一样
                floor = GetAccessibleFloor(floor, group_floor.floors);
            }
        }         
    }

    return std::move(floor);
}


//获取某台设备在指定权限组uuid下的  权限组信息
OfficeUserScheduleInfoList NewOfficeDevUser::GetOfficeDevOneAgInfoList(const std::string &dev_uuid, const std::string &ag_uuid)
{
    OfficeUserScheduleInfoList list;
    auto range = ag_dev_info_map_.equal_range(dev_uuid);
    if (range.first == range.second)
    {
        AK_LOG_INFO << "dev not found access group. dev uuid" << dev_uuid;
        return std::move(list);
    }   
    
    for (auto it = range.first; it != range.second; ++it)
    {
        //一个设备会被多个权限组关联，只要找指定的权限组
        if (ag_uuid == it->second.office_access_group_uuid)
        {
            OfficeUserScheduleInfo info;
            info.relay = it->second.relay;
            info.srelay = it->second.security_relay;
            
            auto info_range = ag_info_map_.equal_range(ag_uuid);
            for (auto it2 = info_range.first; it2 != info_range.second; ++it2)
            {
                info.ag_id = it2->second.id;
                list.push_back(info);
            } 
        }
    }
    
    if (list.size() == 0)
    {
       AK_LOG_WARN << "dev not found associated access group. dev uuid:" << dev_uuid << " ag_uuid:" << ag_uuid; 
    }
    return std::move(list);
}


Json::Value NewOfficeDevUser::CreateUserSchedule(const OfficeDevPtr &dev, const OfficeUUIDSet &ag_uuid_list)
{
    Json::Value schedule(Json::arrayValue);
    if (ag_uuid_list.size() == 0)
    {
        AK_LOG_INFO << "dev ag list is null. dev mac:" << dev->mac;
        return std::move(schedule);
    }
    
    //写权限组对应的relay和ScheduleID
    for (auto& ag_uuid : ag_uuid_list)
    {
        OfficeUserScheduleInfoList list = GetOfficeDevOneAgInfoList(dev->uuid, ag_uuid);
        for (auto& scheduleInfo : list)
        {
            Json::Value tmp;
            int enabled_relay_value = 0;  
            int enabled_security_relay_value = 0;

            // 获取设备的relay和security_relay value
            GetDevicesRelayValue(dev->uuid, dev_door_info_map_, enabled_relay_value, enabled_security_relay_value);

            std::string door_num = RelayToString(scheduleInfo.relay & enabled_relay_value); 
            std::string security_door = RelayToString(scheduleInfo.srelay & enabled_security_relay_value);
            
            tmp["ScheduleID"] = std::to_string(scheduleInfo.ag_id);
            tmp["Relay"] = door_num;
            if (!security_door.empty())
            {
                tmp["SecurityRelay"] = security_door;
            }   
            schedule.append(tmp);
        }
    }    
    return std::move(schedule);
}


int NewOfficeDevUser::CreateUserDetail(const OfficeDevPtr &dev, const OfficePerIDSet &account_list, OfficeUserJson &user_list)
{
    for (auto &per_id : account_list)
    {
        auto it = all_account_mate_map_.find(per_id);
        if (it == all_account_mate_map_.end())
        {
            AK_LOG_INFO << "CreateUserDetail PerID no found in all_account_mate_map. PerID:" << per_id;
            continue;
        }

        const OfficeAccount& account = it->second;
        std::string per_uuid = account.uuid;
        int role = account.role;
        
        Json::Value user;
        user["PerID"] = per_id;
        user["Name"] = account.name;

        user["Card"] = GetAccountRfList(per_uuid, account);
        user["PerType"] = USER_TYPE::ACCOUNT;
        
        if (office_info_->CheckFeature(OfficeInfo::FeaturePlan::TAB_DEVICES_CHECK_INDEX_PIN) && !office_info_->IsExpire())
        {
            user["PriKey"] = GetAccountPinList(per_uuid, office_info_->IsAllowCreatePin()); 
        }
        else 
        {
             AK_LOG_INFO << "Office Account UUID:" << per_uuid << " Switch(pin) feature check result is 0. allow app create pin";
            //如果办公项目过期则默认允许app创建pin
            user["PriKey"] = GetAccountPinList(per_uuid, OfficeInfo::AllowCreatePin::ALLOW); 
        }
        user["LiftFloorNum"] = GetAccountFloorList(dev, per_uuid, role);
        user["CompanyUUID"] = GetAccountCompanyUUID(per_uuid, role);

        auto personnel = all_personnel_map_.find(per_uuid);
        if (personnel != all_personnel_map_.end())
        {
            if (personnel->second.is_set_valid_time)
            {
                user["ValidTimeStart"] = personnel->second.valid_start_time;
                user["ValidTimeEnd"] = personnel->second.valid_end_time;
            }
        }
        
        auto face_it = user_access_.user_face_map_.find(per_uuid);
        if (face_it != user_access_.user_face_map_.end())
        {
            const FaceInfo &info = face_it->second;
            std::string face_url = GetPicFilePath(dev->mac) + info.face_url;
            ModifyUrlAddToken(face_url); //设备的url只有128个字节 加入防盗链要主要长度
            if (face_url.size() < 128)
            {
                user["FaceUrl"] =  face_url;
            }
            else
            {
                user["FaceUrl"] =  GetPicFilePath(dev->mac) + info.face_url;
            }
            user["FaceMD5"] = info.face_md5;
        }

        OfficeUUIDSet user_ag_list = get_account_ag_uuid_cb_(per_uuid, role);
        OfficeUUIDSet dev_ag_list = get_dev_ag_uuid_cb_(dev->uuid);
        OfficeUUIDSet intersection_uuid;
        //取交集 用户对这台设备的权限组列表
        std::set_intersection(user_ag_list.begin(), user_ag_list.end(),
                              dev_ag_list.begin(), dev_ag_list.end(),
                              std::inserter(intersection_uuid, intersection_uuid.begin()));
        Json::Value schedule = CreateUserSchedule(dev, intersection_uuid);
        if (!schedule.empty())
        {
            user["ScheduleRelay"] = schedule;
        }
        user["Cars"] = CreateUserLicensePlate(per_uuid); 

        user_list.push_back(user);
    }
    return 0;
}

int NewOfficeDevUser::CreateDeliveryDetail(const OfficeDevPtr &dev, const OfficePerIDSet &delivery_list, OfficeUserJson &user_list)
{
    for (auto &per_id : delivery_list)
    {
        auto it = delivery_mate_map_.find(per_id);
        if (it == delivery_mate_map_.end())
        {
            AK_LOG_INFO << "CreateDeliveryDetail PerID no found in delivery_mate_map. PerID:" << per_id;
            continue;
        }
        const OfficeDeliveryInfo& info = it->second;
        const std::string &uuid = info.uuid;
        
        Json::Value user;
        user["PerID"] = per_id;
        user["Name"] = info.name;
        
        user["Card"] =GetDeliveryRfList(uuid);
        user["PriKey"] = GetDeliveryPinList(uuid); 
        user["PerType"] = USER_TYPE::DELIVERY;
        
        user["LiftFloorNum"] = GetDeliveryFloorList(dev, uuid);
        user["CompanyUUID"] = info.office_company_uuid;


        OfficeUUIDSet user_ag_list = get_delivery_ag_uuid_cb_(uuid);
        OfficeUUIDSet dev_ag_list = get_dev_ag_uuid_cb_(dev->uuid);
        OfficeUUIDSet intersection_uuid;
        //取交集 用户对这台设备的权限组列表
        std::set_intersection(user_ag_list.begin(), user_ag_list.end(),
                              dev_ag_list.begin(), dev_ag_list.end(),
                              std::inserter(intersection_uuid, intersection_uuid.begin()));      
        
        Json::Value schedule = CreateUserSchedule(dev, intersection_uuid);        
        user["ScheduleRelay"] = schedule;    
        user_list.push_back(user);
    }    
    return 0;
}


int NewOfficeDevUser::WritePublicDevUserMateFile(const OfficeDevPtr &dev, const OfficeUUIDSet &permission_account_list, const OfficeUUIDSet &permission_delivery_list)
{
    Json::Value item;
    Json::FastWriter w;
    item["UserType"] = 0;

    for (auto account_uuid : permission_account_list)
    {
        auto it = all_account_map_.find(account_uuid);
        if (it != all_account_map_.end())
        {
            const OfficeAccount& info = it->second;
            Json::Value user;
            user["PerID"] = info.account;
            user["Meta"] = std::to_string(info.version);
            item["User"].append(user);            
        }
    }   

    for (auto delivery_uuid : permission_delivery_list)
    {
        auto it = delivery_map_.find(delivery_uuid);
        if (it != delivery_map_.end())
        {
            const OfficeDeliveryInfo& info = it->second;
            Json::Value user;
            user["PerID"] = CreateSpecialUUID(USER_TYPE::DELIVERY, info.id);
            user["Meta"] = std::to_string(info.version);
            item["User"].append(user);            
        }
    } 


    std::string msg_json = w.write(item);

    //写入文件
    std::string config_path = config_root_path_ + dev->mac + "-mate.json";
    DevFileInfoPtr ptr = std::make_shared<DevFileInfo>(dev->mac, config_path, msg_json, SHADOW_TYPE::SHADOW_USERMETA,
                                                        project::OFFICE, dev->id);
    GetWriteFileControlInstance()->AddFileInfo(dev->mac, ptr);     

    return 0;
}

int NewOfficeDevUser::GetUserDetailFile(const OfficeDevPtr &dev, const OfficePerIDSet &account_list, OfficeUserDetailReq &req)
{
    OfficePerIDSet account_per_id_list;
    OfficePerIDSet delivery_per_id_list;
    for(auto &it : account_list)
    {
        if (it[0] == MODE_DELIVERY)
        {
            delivery_per_id_list.insert(it);
        }
        else
        {
            account_per_id_list.insert(it);
        }
    }
    
    OfficeUserJson delivery_user_list;
    OfficeUserJson account_user_list;
    CreateUserDetail(dev, account_per_id_list, account_user_list);
    CreateDeliveryDetail(dev, delivery_per_id_list, delivery_user_list);
    Json::Value item;
    Json::FastWriter w;
    
    item["UserType"] = 0;
    for (auto &user : account_user_list)
    {
        item["User"].append(user);
    } 
    for (auto &user : delivery_user_list)
    {
        item["User"].append(user);
    }        

    GetUserDetailFilePath(dev, req.trarceid, req.write_file_path, req.download_file_path);

    std::string msg_json = w.write(item);
    if (WirteFile(req.write_file_path, msg_json))
    {
        return -1;
    }    
    req.file_md5 = akuvox_encrypt::MD5::GetFileMD5(req.write_file_path);
    return 0;  
}

int NewOfficeDevUser::WirteFile(const std::string &filename, const std::string &content)
{
    //user detail
    if(!ThreadLocalSingleton::GetInstance().GetDbStatus())
    {
        AK_LOG_ERROR << "Alarm Monitoring: Db error. Pause write user detail file:" << filename;
        return -1;    
    }
    FILE* file = fopen(filename.c_str(), "w+");
    if (file == NULL)
    {
        AK_LOG_WARN << "fopen failed " << filename;       
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csconfig", filename, AKCS_MONITOR_ALARM_CONFIG_WRITE_FAILED);
        return -1;
    }
    //将配置信息内容写入文件中
    fwrite(content.c_str(), sizeof(char), strlen(content.c_str()) + 1, file);
    fclose(file);
    AK_LOG_INFO << "The user file path is " << filename;

    //加密
    if (!gstCSCONFIGConf.no_encrypt)
    {
        FileAESEncrypt(filename.c_str(), AES_ENCRYPT_KEY_V1, filename.c_str());
    }
    return 0;

}

std::string NewOfficeDevUser::GetAccountCompanyUUID(const std::string& per_uuid, int role)
{
    std::string office_company_uuid;
    if (role == ACCOUNT_ROLE_OFFICE_NEW_PER)
    {
        auto personnel = all_personnel_map_.find(per_uuid);
        if (personnel != all_personnel_map_.end())
        {
            office_company_uuid = personnel->second.office_company_uuid;
        }
    }
    else if (role == ACCOUNT_ROLE_OFFICE_NEW_ADMIN)
    {
        auto admin = all_admin_map_.find(per_uuid);
        if (admin != all_admin_map_.end())
        {
            office_company_uuid = admin->second.office_company_uuid;
        }
    }
    return std::move(office_company_uuid);
}


Json::Value NewOfficeDevUser::CreateUserLicensePlate(const std::string &uuid)
{
    Json::Value license_plate_json(Json::arrayValue);
    auto per = user_access_.account_license_plate_map_.equal_range(uuid);
    for (auto it = per.first; it != per.second; ++it)
    {
        const LicensePlateInfo& license_plate = it->second;
        Json::Value tmp;
        tmp["LicensePlate"] = std::string(license_plate.plate);
        tmp["UFHCard"] = std::string(license_plate.ufh);
        tmp["BeginTime"] = std::string(license_plate.begin_time);
        tmp["EndTime"] = std::string(license_plate.end_time);
        if (license_plate.time_control == TimeControl::Always) {
            tmp["BeginTime"] = "";
            tmp["EndTime"] = "";
        }
        license_plate_json.append(tmp);
    } 
    return std::move(license_plate_json);
}
