#ifndef __NEW_OFFICE_DEV_USER_H__
#define __NEW_OFFICE_DEV_USER_H__

#include <string>
#include "json/json.h"
#include "AES256.h"
#include "AkcsMonitor.h"
#include "encrypt/Md5.h"
#include "InnerUtil.h"
#include "InnerEnum.h"
#include "OfficeUserAccessInfo.h"
#include "dbinterface/office/OfficeInfo.h"
#include "dbinterface/new-office/DevicesDoorList.h"
#include "OfficeNew/ConfigFile/OfficeNewConfigHandle.h"

using GetAccountAgUUIDListCb = std::function<OfficeUUIDSet(const std::string &account_uuid, int role)>;
using GetAccountGroupUUIDListCb = std::function<OfficeUUIDSet(const std::string &account_uuid, int role)>;
using GetDeliveryAgUUIDListCb = std::function<OfficeUUIDSet(const std::string &account_uuid)>;
using GetDevAgUUIDListCb = std::function<OfficeUUIDSet(const std::string &dev_uuid)>;

class OfficeUserScheduleInfo
{
public:
    int relay;
    int srelay;
    uint64_t ag_id; 
};

using OfficeUserScheduleInfoList = std::vector<OfficeUserScheduleInfo>;

class NewOfficeDevUser
{
    
public:
	NewOfficeDevUser(const std::string& config_root_path, const OfficeInfoPtr &office_info)
    {
        config_root_path_ = config_root_path;
        office_info_ = office_info;
    }
    

	~NewOfficeDevUser(){};

    void InitUserMateInfo(const OfficeAccountMap &all_account_list, const OfficeDeliveryMap &delivery_map, const OfficePersonnelMap& personnel_map, const OfficeAdminMap& admin_map,
         const OfficeAccountMateMap &all_account_mate_list, const OfficeDeliveryUserMateMap &delivery_mate_map, const GroupOfPerPerMap &group_per_map, const DevicesDoorInfoMap &dev_door_info_map)
    {
        all_account_map_ = all_account_list;
        delivery_map_ = delivery_map;
        all_personnel_map_ = personnel_map;
        all_admin_map_ = admin_map;
        all_account_mate_map_ = all_account_mate_list;
        delivery_mate_map_ = delivery_mate_map;
        group_per_map_ = group_per_map;
        dev_door_info_map_ = dev_door_info_map;
    }    
    
    void InitUserAccess(const UserAccessInfo &user_access, const AgDevInfoDevMap& ag_dev_info_map, const AccessGroupUUIDMap& ag_map)
    {
        user_access_ = user_access;
        ag_dev_info_map_ = ag_dev_info_map;
        ag_info_map_ = ag_map;
    }
    
    void SetGetAccountAgUUIDListCb(const GetAccountAgUUIDListCb &cb)
    {
        get_account_ag_uuid_cb_ = cb;
    }
    void SetGetDevAgUUIDListCb(const GetDevAgUUIDListCb &cb)
    {
        get_dev_ag_uuid_cb_ = cb;
    }
    void SetGetDeliveryAgUUIDListCb(const GetDeliveryAgUUIDListCb &cb)
    {
        get_delivery_ag_uuid_cb_ = cb;
    }
    void SetGetAccountGroupUUIDListCb(const GetAccountGroupUUIDListCb &cb)
    {
        get_account_group_uuid_cb_ = cb;
    }

    int WritePublicDevUserMateFile(const OfficeDevPtr &dev, const OfficeUUIDSet &permission_account_list, const OfficeUUIDSet &permission_delivery_list);
    int GetUserDetailFile(const OfficeDevPtr &dev, const OfficePerIDSet &account_list, OfficeUserDetailReq &req);
    
private:
    std::string CreateSpecialUUID(USER_TYPE      type,uint32_t id);
    int GetUserDetailFilePath(const OfficeDevPtr &dev, uint64_t traceid, std::string &write_path, std::string &download_path);
    OfficeUserScheduleInfoList GetOfficeDevOneAgInfoList(const std::string &dev_uuid, const std::string &ag_uuid);
    Json::Value CreateUserSchedule(const OfficeDevPtr &dev, const OfficeUUIDSet &ag_uuid_set);
    int CreateUserDetail(const OfficeDevPtr &dev, const OfficePerIDSet &account_list, OfficeUserJson &user_list);
    int CreateDeliveryDetail(const OfficeDevPtr &dev, const OfficePerIDSet &account_list, OfficeUserJson &user_list);
    int WirteFile(const std::string &filename, const std::string &content);

    std::string GetAccountPinList(const std::string &uuid, int user_can_create_pin);
    std::string GetAccountRfList(const std::string &uuid, const OfficeAccount &info);
    std::string GetDeliveryRfList(const std::string &uuid);
    std::string GetDeliveryPinList(const std::string &uuid);
    std::string GetDeliveryFloorList(const OfficeDevPtr &dev, const std::string &uuid);
    std::string GetAccountFloorList(const OfficeDevPtr &dev, const std::string &uuid, int role);
    std::string GetAccountCompanyUUID(const std::string& per_uuid, int role);
    Json::Value CreateUserLicensePlate(const std::string &uuid);
private:
    std::string config_root_path_;
    OfficeInfoPtr office_info_;
    
    GroupOfPerPerMap group_per_map_;
    OfficeAccountMap all_account_map_; //项目所有的用户列表
    OfficePersonnelMap all_personnel_map_;//项目所有的personnel列表   
    OfficeAdminMap all_admin_map_; //项目所有的admin列表
    OfficeAccountMateMap all_account_mate_map_; //项目所有的用户列表
    OfficeDeliveryMap delivery_map_;
    OfficeDeliveryUserMateMap delivery_mate_map_;

    UserAccessInfo user_access_;
    GetAccountAgUUIDListCb get_account_ag_uuid_cb_;
    GetDeliveryAgUUIDListCb get_delivery_ag_uuid_cb_;
    GetAccountGroupUUIDListCb get_account_group_uuid_cb_;
    GetDevAgUUIDListCb get_dev_ag_uuid_cb_;

    AgDevInfoDevMap ag_dev_info_map_;
    AccessGroupUUIDMap ag_info_map_;
    DevicesDoorInfoMap dev_door_info_map_;
};



#endif 

