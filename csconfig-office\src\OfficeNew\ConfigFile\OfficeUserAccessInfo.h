#ifndef __NEW_OFFICE_USER_ACCESS_H__
#define __NEW_OFFICE_USER_ACCESS_H__
#include <string>
#include "dbinterface/office/OfficeInfo.h"
#include "InnerUtil.h"
#include "InnerDbDef.h"


class UserAccessInfo
{
    
public:
	UserAccessInfo()
    {    
    }
    void Init(const std::string &project_uuid)
    {
        dbinterface::UserFace::GetFaceByProjectUUID(project_uuid, user_face_map_);
        dbinterface::UserPinCode::GetPinCodeByProjectUUID(project_uuid, account_pin_map_, delivery_pin_map_);
        dbinterface::UserRfCard::GetUserRfCardByProjectUUID(project_uuid, account_rf_map_, delivery_rf_map_);
        dbinterface::UserLicensePlate::GetUserLicensePlateByProjectUUID(project_uuid, account_license_plate_map_);

        dbinterface::OfficeDeliveryAccessFloor::GetOfficeDeliveryAccessFloorByProjectUUID(project_uuid, delivery_access_floor_);
        dbinterface::OfficeGroupAccessFloor::GetOfficeGroupAccessFloorByProjectUUID(project_uuid, group_access_floor_);
        dbinterface::OfficeCompanyAccessFloor::GetOfficeCompanyAccessFloorByProjectUUID(project_uuid, company_access_floor_);
        dbinterface::OfficeGroup::GetOfficeGroupOfCompanyByProjectUUID(project_uuid, group_company_uuid_map_);
    }

    UserFaceMap user_face_map_;
    UserRfCardMap account_rf_map_; 
    UserRfCardMap delivery_rf_map_; 
    UserPinCodeMap delivery_pin_map_; 
    UserPinCodeMap account_pin_map_;
    UserLicensePlateMap account_license_plate_map_; 

    OfficeDeliveryAccessFloorMap delivery_access_floor_;
    OfficeGroupAccessFloorMap group_access_floor_;
    OfficeCompanyAccessFloorMap company_access_floor_;
    GroupOfCompanyUUIDMap group_company_uuid_map_;
};



#endif 

