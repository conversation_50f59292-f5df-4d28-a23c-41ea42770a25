#include "OfficeNew/DataAnalysis/DataAnalysisAccount.h"
#include "DataAnalysisContorl.h"
#include "OfficePduConfigMsg.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include "dbinterface/ProjectUserManage.h"
#include <string.h>
#include <memory>
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"





static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);


enum DAAccountIndex{
    DA_INDEX_ACCOUNT_ID,
    DA_INDEX_ACCOUNT_GRADE,
    DA_INDEX_ACCOUNT_LOCATION,
    DA_INDEX_ACCOUNT_TIMEZONE,
    DA_INDEX_ACCOUNT_CUSTOMIZEFORM,
    DA_INDEX_ACCOUNT_SIPTYPE,
    DA_INDEX_ACCOUNT_UUID,
};


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "Account";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
    //当前只对办公和社区的account数据做分析
    {DA_INDEX_ACCOUNT_ID, "ID", ItemChangeHandle},
    {DA_INDEX_ACCOUNT_GRADE, "Grade", ItemChangeHandle},
    {DA_INDEX_ACCOUNT_LOCATION, "Location", ItemChangeHandle},
    {DA_INDEX_ACCOUNT_TIMEZONE, "TimeZone", ItemChangeHandle},
    {DA_INDEX_ACCOUNT_CUSTOMIZEFORM, "CustomizeForm", ItemChangeHandle},
    {DA_INDEX_ACCOUNT_SIPTYPE, "SipType", ItemChangeHandle},
    {DA_INDEX_ACCOUNT_UUID, "UUID", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //创建社区或office时没有设备，所以无需刷新配置
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //当前没有删除社区，办公的删除在special处理
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string project_uuid = data.GetIndex(DA_INDEX_ACCOUNT_UUID);

    uint32_t grade = data.GetIndexAsInt(DA_INDEX_ACCOUNT_GRADE);
    if ((grade == AccountGrade::COMMUNITY_MANEGER_GRADE 
        || grade == AccountGrade::OFFICE_MANEGER_GRADE)
        && (data.IsIndexChange(DA_INDEX_ACCOUNT_LOCATION) 
        || data.IsIndexChange(DA_INDEX_ACCOUNT_TIMEZONE)
        || data.IsIndexChange(DA_INDEX_ACCOUNT_CUSTOMIZEFORM)))
    {
        OfficeFileUpdateInfo update_info(project_uuid, OfficeUpdateType::OFFICE_PROJECT_CHANGE); 
        context.AddUpdateConfigInfo(update_info);      
    }
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaAccountHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);
}






