#include "OfficeNew/DataAnalysis/DataAnalysisAntiPassbackArea.h"

#include "DataAnalysisTableParse.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include "dbinterface/ProjectUserManage.h"
#include "OfficePduConfigMsg.h"
#include "dbinterface/AntiPassbackArea.h"

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "AntiPassbackArea";
/*复制到DataAnalysisDef.h*/ 
enum DAAntiPassbackAreaIndex{
    DA_INDEX_ANTI_PASSBACK_AREA_ID,
    DA_INDEX_ANTI_PASSBACK_AREA_VERSION,
    DA_INDEX_ANTI_PASSBACK_AREA_ACCOUNTUUID,
    DA_INDEX_ANTI_PASSBACK_AREA_NAME,
    DA_INDEX_ANTI_PASSBACK_AREA_SCHEDULETYPE,
    DA_INDEX_ANTI_PASSBACK_AREA_STARTTIME,
    DA_INDEX_ANTI_PASSBACK_AREA_STOPTIME,
    DA_INDEX_ANTI_PASSBACK_AREA_RESTRICTTYPE,
    DA_INDEX_ANTI_PASSBACK_AREA_RESTRICTTIME,
    DA_INDEX_ANTI_PASSBACK_AREA_ENABLE,
    DA_INDEX_ANTI_PASSBACK_AREA_RBACDATAGROUPUUID,
    DA_INDEX_ANTI_PASSBACK_AREA_UUID,
    DA_INDEX_ANTI_PASSBACK_AREA_UPDATETIME,
    DA_INDEX_ANTI_PASSBACK_AREA_CREATETIME,
};
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/ 
   {DA_INDEX_ANTI_PASSBACK_AREA_ID, "ID", ItemChangeHandle},
   {DA_INDEX_ANTI_PASSBACK_AREA_VERSION, "Version", ItemChangeHandle},
   {DA_INDEX_ANTI_PASSBACK_AREA_ACCOUNTUUID, "AccountUUID", ItemChangeHandle},
   {DA_INDEX_ANTI_PASSBACK_AREA_NAME, "Name", ItemChangeHandle},
   {DA_INDEX_ANTI_PASSBACK_AREA_SCHEDULETYPE, "ScheduleType", ItemChangeHandle},
   {DA_INDEX_ANTI_PASSBACK_AREA_STARTTIME, "StartTime", ItemChangeHandle},
   {DA_INDEX_ANTI_PASSBACK_AREA_STOPTIME, "StopTime", ItemChangeHandle},
   {DA_INDEX_ANTI_PASSBACK_AREA_RESTRICTTYPE, "RestrictType", ItemChangeHandle},
   {DA_INDEX_ANTI_PASSBACK_AREA_RESTRICTTIME, "RestrictTime", ItemChangeHandle},
   {DA_INDEX_ANTI_PASSBACK_AREA_ENABLE, "Enable", ItemChangeHandle},
   {DA_INDEX_ANTI_PASSBACK_AREA_RBACDATAGROUPUUID, "RBACDataGroupUUID", ItemChangeHandle},
   {DA_INDEX_ANTI_PASSBACK_AREA_UUID, "UUID", ItemChangeHandle},
   {DA_INDEX_INSERT, "", InsertHandle},
   {DA_INDEX_DELETE, "", DeleteHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

/*只需要处理update, 新增和删除最终都会走到DataAnalysisAntiPassbackDoor*/
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string project_uuid = data.GetIndex(DA_INDEX_ANTI_PASSBACK_AREA_ACCOUNTUUID);
    std::string uuid = data.GetIndex(DA_INDEX_ANTI_PASSBACK_AREA_UUID);

    // 区域开关变化 才需要刷新设备配置
    if (data.IsIndexChange(DA_INDEX_ANTI_PASSBACK_AREA_ENABLE))
    {
        AkcsStringSet dev_uuid_set;
        dbinterface::AntiPassbackArea::GetAntiPassbackAreaDevListByUUID(uuid, dev_uuid_set);
        AK_LOG_INFO << "AntipassBackArea Switch Change, AreaUUID = " << uuid;
        
        OfficeFileUpdateInfo update_info(project_uuid, OfficeUpdateType::OFFICE_DEV_CONFIG_CHANGE_WITH_MAC);
        for(const auto& dev_uuid : dev_uuid_set)
        {
            update_info.AddDevUUIDToList(dev_uuid);  
        }
        
        context.AddUpdateConfigInfo(update_info);
    }

    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaAntiPassbackAreaHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}


