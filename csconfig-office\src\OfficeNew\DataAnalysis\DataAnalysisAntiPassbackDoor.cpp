#include "OfficeNew/DataAnalysis/DataAnalysisAntiPassbackDoor.h"

#include "DataAnalysisTableParse.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include "dbinterface/ProjectUserManage.h"
#include "OfficePduConfigMsg.h"
#include "dbinterface/AntiPassbackArea.h"
#include "dbinterface/office/OfficeDevices.h"

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "AntiPassbackDoor";
/*复制到DataAnalysisDef.h*/ 
enum DAAntiPassbackDoorIndex{
    DA_INDEX_ANTI_PASSBACK_DOOR_ID,
    DA_INDEX_ANTI_PASSBACK_DOOR_ANTIPASSBACKAREAUUID,
    DA_INDEX_ANTI_PASSBACK_DOOR_DEVICESUUID,
    DA_INDEX_ANTI_PASSBACK_DOOR_RELAYTYPE,
    DA_INDEX_ANTI_PASSBACK_DOOR_RELAYNUM,
    DA_INDEX_ANTI_PASSBACK_DOOR_TYPE,
    DA_INDEX_ANTI_PASSBACK_DOOR_UUID,
    DA_INDEX_ANTI_PASSBACK_DOOR_CREATETIME,
    DA_INDEX_ANTI_PASSBACK_DOOR_UPDATETIME,
};
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/ 
   {DA_INDEX_ANTI_PASSBACK_DOOR_ID, "ID", ItemChangeHandle},
   {DA_INDEX_ANTI_PASSBACK_DOOR_ANTIPASSBACKAREAUUID, "AntiPassbackAreaUUID", ItemChangeHandle},
   {DA_INDEX_ANTI_PASSBACK_DOOR_DEVICESUUID, "DevicesUUID", ItemChangeHandle},
   {DA_INDEX_ANTI_PASSBACK_DOOR_RELAYTYPE, "RelayType", ItemChangeHandle},
   {DA_INDEX_ANTI_PASSBACK_DOOR_RELAYNUM, "RelayNum", ItemChangeHandle},
   {DA_INDEX_ANTI_PASSBACK_DOOR_TYPE, "Type", ItemChangeHandle},
   {DA_INDEX_ANTI_PASSBACK_DOOR_UUID, "UUID", ItemChangeHandle},
   {DA_INDEX_INSERT, "", UpdateHandle},
   {DA_INDEX_DELETE, "", DeleteHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}
/*
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}
*/

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string dev_uuid = data.GetIndex(DA_INDEX_ANTI_PASSBACK_DOOR_DEVICESUUID);
    
    OfficeDevPtr dev;
    if (0 != dbinterface::OfficeDevices::GetDevByUUID(dev_uuid, dev))
    {
        AK_LOG_INFO << "DeleteHandle GetUUIDDev failed, dev_uuid = " << dev_uuid;
        return 0;
    }

    if (dev != nullptr)
    {
        OfficeFileUpdateInfo update_info(dev->project_uuid, OfficeUpdateType::OFFICE_DEV_CONFIG_CHANGE_WITH_MAC);
        update_info.AddDevUUIDToList(dev_uuid);
        context.AddUpdateConfigInfo(update_info);
    }

    return 0;
}

//都走更新 最后都能获取到设备的UUID进行更新
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string anti_uuid = data.GetIndex(DA_INDEX_ANTI_PASSBACK_DOOR_ANTIPASSBACKAREAUUID);
    std::string dev_uuid = data.GetIndex(DA_INDEX_ANTI_PASSBACK_DOOR_DEVICESUUID);

    AntiPassbackAreaInfo info;
    dbinterface::AntiPassbackArea::GetAntiPassbackAreaByUUID(anti_uuid, info);

    OfficeFileUpdateInfo update_info(info.account_uuid, OfficeUpdateType::OFFICE_DEV_CONFIG_CHANGE_WITH_MAC);
    update_info.AddDevUUIDToList(dev_uuid);
    context.AddUpdateConfigInfo(update_info);
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaAntiPassbackDoorHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}


