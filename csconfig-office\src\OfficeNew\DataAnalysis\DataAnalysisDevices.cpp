#include "OfficeNew/DataAnalysis/DataAnalysisDevices.h"
#include "DataAnalysisContorl.h"
#include "OfficePduConfigMsg.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include "dbinterface/ProjectUserManage.h"
#include <string.h>
#include <memory>
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "InnerUtil.h"


static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int GetDevicesChangeType();


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "Devices";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
	{DA_INDEX_DEVICES_NAME, "Location", ItemChangeHandle},
	{DA_INDEX_DEVICES_MAC, "MAC", ItemChangeHandle},
	{DA_INDEX_DEVICES_MNG_ID, "MngAccountID", ItemChangeHandle},
	{DA_INDEX_DEVICES_UNIT_ID, "UnitID", ItemChangeHandle},
	{DA_INDEX_DEVICES_NODE, "Node", ItemChangeHandle},
	{DA_INDEX_DEVICES_TYPE, "Type", ItemChangeHandle},
	{DA_INDEX_DEVICES_GRADE, "Grade", ItemChangeHandle},
	{DA_INDEX_DEVICES_PROJECT_TYPE, "ProjectType", ItemChangeHandle},
	{DA_INDEX_DEVICES_NET_GROUP, "NetGroupNumber", ItemChangeHandle},
	{DA_INDEX_DEVICES_CONFIG, "Config", ItemChangeHandle},
	{DA_INDEX_DEVICES_ARMING_FUNCTION, "ArmingFunction", ItemChangeHandle},
	{DA_INDEX_DEVICES_STAIRSHOW, "StairShow", ItemChangeHandle},
	{DA_INDEX_DEVICES_RELAY, "Relay", ItemChangeHandle},
	{DA_INDEX_DEVICES_SECURITYRELAY, "SecurityRelay", ItemChangeHandle},
	{DA_INDEX_DEVICES_FLAGS, "Flags", ItemChangeHandle},
	{DA_INDEX_DEVICES_ACCOUNT_UUID, "AccountUUID", ItemChangeHandle},
	{DA_INDEX_DEVICES_UUID, "UUID", ItemChangeHandle},
	
	{DA_INDEX_INSERT, "", InsertHandle},
	{DA_INDEX_DELETE, "", DeleteHandle},
	{DA_INDEX_UPDATE, "", UpdateHandle}
};

static void UpdateDevicesConfig(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string project_uuid = data.GetIndex(DA_INDEX_DEVICES_ACCOUNT_UUID);
    std::string dev_uuid = data.GetIndex((DA_INDEX_DEVICES_UUID));

    OfficeFileUpdateInfo update_info(project_uuid, OfficeUpdateType::OFFICE_DEV_INFO_CHANGE);
    update_info.AddDevUUIDToList(dev_uuid);
    context.AddUpdateConfigInfo(update_info); 
}

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string project_uuid = data.GetIndex(DA_INDEX_DEVICES_ACCOUNT_UUID);
    std::string dev_uuid = data.GetIndex(DA_INDEX_DEVICES_UUID);
    std::string mac = data.GetIndex(DA_INDEX_DEVICES_MAC);
    
    OfficeFileUpdateInfo update_info(project_uuid, OfficeUpdateType::OFFICE_DEV_ADD);
    update_info.AddDevUUIDToList(dev_uuid);
    update_info.AddDevMac(mac);
    context.AddUpdateConfigInfo(update_info);

    UpdateDevicesConfig(data, context);

}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string project_uuid = data.GetIndex(DA_INDEX_DEVICES_ACCOUNT_UUID);
    std::string dev_uuid = data.GetIndex(DA_INDEX_DEVICES_UUID);
    std::string mac = data.GetIndex(DA_INDEX_DEVICES_MAC);

    OfficeFileUpdateInfo update_info(project_uuid, OfficeUpdateType::OFFICE_DEV_DELETE);
    update_info.AddDevUUIDToList(dev_uuid);
    update_info.AddDevMac(mac);
    context.AddUpdateConfigInfo(update_info); 

    UpdateDevicesConfig(data, context);//变化是为了更新别的设备联系人
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //user里面有relay的信息
    if (data.IsIndexChange(DA_INDEX_DEVICES_RELAY) || data.IsIndexChange(DA_INDEX_DEVICES_SECURITYRELAY))
    {
        //更新数据版本
        std::string dev_uuid = data.GetIndex(DA_INDEX_DEVICES_UUID);
        UpdateUserVersionByDevices(dev_uuid);
    }
    UpdateDevicesConfig(data, context);
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaDevicesHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);

}






