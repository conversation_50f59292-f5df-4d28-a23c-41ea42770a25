#include "InnerUtil.h"
#include "DataAnalysis.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysisTableParse.h"
#include "OfficeNew/DataAnalysis/DataAnalysisFileUpdate.h"
#include "OfficeNew/DataAnalysis/DataAnalysisDevicesDoorList.h"

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

enum DADevicesDoorListIndex{
    DA_INDEX_DEVICES_DOOR_LIST_ACCOUNTUUID,
    DA_INDEX_DEVICES_DOOR_LIST_DEVICESUUID,
    DA_INDEX_DEVICES_DOOR_LIST_ENABLE,
    DA_INDEX_DEVICES_DOOR_LIST_INDEX,
    DA_INDEX_DEVICES_DOOR_LIST_NAME,
    DA_INDEX_DEVICES_DOOR_LIST_RELAYTYPE,
    DA_INDEX_DEVICES_DOOR_LIST_DTMF,
    DA_INDEX_DEVICES_DOOR_LIST_CONTROLLEDRELAY,
    DA_INDEX_DEVICES_DOOR_LIST_SCHEDULEENABLE,
    DA_INDEX_DEVICES_DOOR_LIST_SCHEDULEACCESS,
    DA_INDEX_DEVICES_DOOR_LIST_SHOWHOME,
    DA_INDEX_DEVICES_DOOR_LIST_SHOWTALKING,
    DA_INDEX_DEVICES_DOOR_LIST_PINENABLE,
    DA_INDEX_DEVICES_DOOR_LIST_FACEENABLE,
    DA_INDEX_DEVICES_DOOR_LIST_RFCARDENABLE,
    DA_INDEX_DEVICES_DOOR_LIST_BLEENABLE,
    DA_INDEX_DEVICES_DOOR_LIST_NFCENABLE,
    DA_INDEX_DEVICES_DOOR_LIST_PLATEENABLE,
    DA_INDEX_DEVICES_DOOR_LIST_ISEMERGENCYDOOR,
    DA_INDEX_DEVICES_DOOR_LIST_ISLOCKED,
    DA_INDEX_DEVICES_DOOR_LIST_EXITBUTTON,
    DA_INDEX_DEVICES_DOOR_LIST_DOORSTATUS,
    DA_INDEX_DEVICES_DOOR_LIST_HOLDOPENALARMENABLE,
    DA_INDEX_DEVICES_DOOR_LIST_HOLDOPENALARMTIMEOUT,
    DA_INDEX_DEVICES_DOOR_LIST_BREAKINALARMENABLE,
    DA_INDEX_DEVICES_DOOR_LIST_OFFICECOMPANYUUID,
    DA_INDEX_DEVICES_DOOR_LIST_ACTIVE,
    DA_INDEX_DEVICES_DOOR_LIST_EXPIRETIME,
};

static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "DevicesDoorList";

static DataAnalysisChangeHandle da_change_handle[] = {
   {DA_INDEX_DEVICES_DOOR_LIST_ACCOUNTUUID, "AccountUUID", ItemChangeHandle},
   {DA_INDEX_DEVICES_DOOR_LIST_DEVICESUUID, "DevicesUUID", ItemChangeHandle},
   {DA_INDEX_DEVICES_DOOR_LIST_ENABLE, "Enable", ItemChangeHandle},
   {DA_INDEX_DEVICES_DOOR_LIST_INDEX, "Index", ItemChangeHandle},
   {DA_INDEX_DEVICES_DOOR_LIST_NAME, "Name", ItemChangeHandle},
   {DA_INDEX_DEVICES_DOOR_LIST_RELAYTYPE, "RelayType", ItemChangeHandle},
   {DA_INDEX_DEVICES_DOOR_LIST_DTMF, "Dtmf", ItemChangeHandle},
   {DA_INDEX_DEVICES_DOOR_LIST_CONTROLLEDRELAY, "ControlledRelay", ItemChangeHandle},
   {DA_INDEX_DEVICES_DOOR_LIST_SCHEDULEENABLE, "ScheduleEnable", ItemChangeHandle},
   {DA_INDEX_DEVICES_DOOR_LIST_SCHEDULEACCESS, "ScheduleAccess", ItemChangeHandle},
   {DA_INDEX_DEVICES_DOOR_LIST_SHOWHOME, "ShowHome", ItemChangeHandle},
   {DA_INDEX_DEVICES_DOOR_LIST_SHOWTALKING, "ShowTalking", ItemChangeHandle},
   {DA_INDEX_DEVICES_DOOR_LIST_PINENABLE, "PinEnable", ItemChangeHandle},
   {DA_INDEX_DEVICES_DOOR_LIST_FACEENABLE, "FaceEnable", ItemChangeHandle},
   {DA_INDEX_DEVICES_DOOR_LIST_RFCARDENABLE, "RfCardEnable", ItemChangeHandle},
   {DA_INDEX_DEVICES_DOOR_LIST_BLEENABLE, "BleEnable", ItemChangeHandle},
   {DA_INDEX_DEVICES_DOOR_LIST_NFCENABLE, "NfcEnable", ItemChangeHandle},
   {DA_INDEX_DEVICES_DOOR_LIST_PLATEENABLE, "PlateEnable", ItemChangeHandle},
   {DA_INDEX_DEVICES_DOOR_LIST_ISEMERGENCYDOOR, "IsEmergencyDoor", ItemChangeHandle},
   {DA_INDEX_DEVICES_DOOR_LIST_ISLOCKED, "IsLocked", ItemChangeHandle},
   {DA_INDEX_DEVICES_DOOR_LIST_EXITBUTTON, "ExitButton", ItemChangeHandle},
   {DA_INDEX_DEVICES_DOOR_LIST_DOORSTATUS, "DoorStatus", ItemChangeHandle},
   {DA_INDEX_DEVICES_DOOR_LIST_HOLDOPENALARMENABLE, "HoldOpenAlarmEnable", ItemChangeHandle},
   {DA_INDEX_DEVICES_DOOR_LIST_HOLDOPENALARMTIMEOUT, "HoldOpenAlarmTimeOut", ItemChangeHandle},
   {DA_INDEX_DEVICES_DOOR_LIST_BREAKINALARMENABLE, "BreakInAlarmEnable", ItemChangeHandle},
   {DA_INDEX_DEVICES_DOOR_LIST_OFFICECOMPANYUUID, "OfficeCompanyUUID", ItemChangeHandle},
   {DA_INDEX_DEVICES_DOOR_LIST_ACTIVE, "Active", ItemChangeHandle},
   {DA_INDEX_DEVICES_DOOR_LIST_EXPIRETIME, "ExpireTime", ItemChangeHandle},
   {DA_INDEX_DEVICES_DOOR_LIST_DEVICESUUID, "DevicesUUID", ItemChangeHandle},
   {DA_INDEX_INSERT, "", UpdateHandle},
   {DA_INDEX_DELETE, "", UpdateHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string devices_uuid = data.GetIndex(DA_INDEX_DEVICES_DOOR_LIST_DEVICESUUID);
    std::string project_uuid =  data.GetIndex(DA_INDEX_DEVICES_DOOR_LIST_ACCOUNTUUID);
    AK_LOG_INFO << "DataAnalysisDevicesDoorList UpdateHandle, devices_uuid = " << devices_uuid << ", project_uuid = " << project_uuid;

    // 更新数据版本
    UpdateUserVersionByDevices(devices_uuid);   

    OfficeFileUpdateInfo update_info(project_uuid, OfficeUpdateType::OFFICE_DEV_INFO_CHANGE);
    update_info.AddDevUUIDToList(devices_uuid);
    context.AddUpdateConfigInfo(update_info);
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaDevicesDoorListHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}

