#include "DataAnalysis.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysisTableParse.h"
#include "dbinterface/new-office/DevicesDoorList.h"
#include "OfficeNew/DataAnalysis/DataAnalysisFileUpdate.h"
#include "OfficeNew/DataAnalysis/DataAnalysisDoorReaderList.h"

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

enum DADoorReaderListIndex{
    DA_INDEX_DOOR_READER_LIST_MODE,
    DA_INDEX_DOOR_READER_LIST_TYPE,
    DA_INDEX_DOOR_READER_LIST_CONNECTTYPE,
    DA_INDEX_DOOR_READER_LIST_RS485ADDRESS,
    DA_INDEX_DOOR_READER_LIST_DEVICESDOORLISTUUID
};

static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "DoorReaderList";

static DataAnalysisChangeHandle da_change_handle[] = {
    {DA_INDEX_DOOR_READER_LIST_MODE, "Mode", ItemChangeHandle},
    {DA_INDEX_DOOR_READER_LIST_TYPE, "Type", ItemChangeHandle},
    {DA_INDEX_DOOR_READER_LIST_CONNECTTYPE, "ConnectType", ItemChangeHandle},
    {DA_INDEX_DOOR_READER_LIST_RS485ADDRESS, "RS485Address", ItemChangeHandle},
    {DA_INDEX_DOOR_READER_LIST_DEVICESDOORLISTUUID, "DevicesDoorListUUID", ItemChangeHandle},
    {DA_INDEX_INSERT, "", UpdateHandle},
    {DA_INDEX_DELETE, "", UpdateHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string devices_door_uuid = data.GetIndex(DA_INDEX_DOOR_READER_LIST_DEVICESDOORLISTUUID);
    
    DevicesDoorInfo devices_door_info;
    if (0 != dbinterface::DevicesDoorList::GetDevicesDoorInfoByUUID(devices_door_uuid, devices_door_info))
    {
        AK_LOG_INFO << "DataAnalysisDoorReaderList GetDevicesDoorInfoByUUID failed, devices_door_uuid = " << devices_door_uuid;
        return 0;
    }
    
    OfficeFileUpdateInfo update_info(devices_door_info.account_uuid, OfficeUpdateType::OFFICE_DEV_CONFIG_CHANGE_WITH_MAC);
    update_info.AddDevUUIDToList(devices_door_info.uuid);
    context.AddUpdateConfigInfo(update_info);
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaDoorReaderListHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}

