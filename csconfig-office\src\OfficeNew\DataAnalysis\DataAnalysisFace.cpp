#include "OfficeNew/DataAnalysis/DataAnalysisFace.h"

#include "DataAnalysisTableParse.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include "dbinterface/ProjectUserManage.h"
#include "OfficePduConfigMsg.h"
#include "dbinterface/new-office/OfficeFace.h"
#include "dbinterface/office/OfficePersonalAccount.h"

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "Face";
/*复制到DataAnalysisDef.h*/ 
enum  DAFaceIndex{
    DA_INDEX_FACE_ID,
    DA_INDEX_FACE_UUID,
    DA_INDEX_FACE_ACCOUNTUUID,
    DA_INDEX_FACE_OFFICECOMPANYUUID,
    DA_INDEX_FACE_PERSONALACCOUNTUUID,
    DA_INDEX_FACE_FACEMD5,
    DA_INDEX_FACE_FACEURL,
    DA_INDEX_FACE_CREATORTYPE,
    DA_INDEX_FACE_CREATORACCOUNTUUID,
    DA_INDEX_FACE_CREATORPERSONALACCOUNTUUID,
    DA_INDEX_FACE_RBACDATAGROUPUUID,
    DA_INDEX_FACE_VERSION,
    DA_INDEX_FACE_CREATETIME,
    DA_INDEX_FACE_UPDATETIME,
};
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/ 
   {DA_INDEX_FACE_ID, "ID", ItemChangeHandle},
   {DA_INDEX_FACE_UUID, "UUID", ItemChangeHandle},
   {DA_INDEX_FACE_ACCOUNTUUID, "AccountUUID", ItemChangeHandle},
   {DA_INDEX_FACE_OFFICECOMPANYUUID, "OfficeCompanyUUID", ItemChangeHandle},
   {DA_INDEX_FACE_PERSONALACCOUNTUUID, "PersonalAccountUUID", ItemChangeHandle},
   {DA_INDEX_FACE_FACEMD5, "FaceMD5", ItemChangeHandle},
   {DA_INDEX_FACE_FACEURL, "FaceUrl", ItemChangeHandle},
   {DA_INDEX_FACE_CREATORTYPE, "CreatorType", ItemChangeHandle},
   {DA_INDEX_FACE_CREATORACCOUNTUUID, "CreatorAccountUUID", ItemChangeHandle},
   {DA_INDEX_FACE_CREATORPERSONALACCOUNTUUID, "CreatorPersonalAccountUUID", ItemChangeHandle},
   {DA_INDEX_INSERT, "", UpdateHandle},
   {DA_INDEX_DELETE, "", UpdateHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

/*
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}
*/
//都处理为更新 因为最终的数据都能获取到
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string account_uuid = data.GetIndex(DA_INDEX_FACE_PERSONALACCOUNTUUID);
    dbinterface::OfficePersonalAccount::UpdateVersionByUUID(account_uuid);

    std::string office_uuid = data.GetIndex(DA_INDEX_FACE_ACCOUNTUUID);

    OfficeFileUpdateInfo update_info(office_uuid, OfficeUpdateType::OFFICE_USER_ACCESS_CHANGE);
    context.AddUpdateConfigInfo(update_info);
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaFaceHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}


