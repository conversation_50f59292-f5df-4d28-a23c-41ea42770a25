#ifndef __CSADAPT_UPDATECONFIG_NEWOFFICE_FILEUPDATE_H__
#define __CSADAPT_UPDATECONFIG_NEWOFFICE_FILEUPDATE_H__

#include <vector>
#include <string>
#include <map>
#include <list>
#include <memory>
#include "UpdateConfigDef.h"
#include "BasicDefine.h"
#include "AK.AdaptOffice.pb.h"
#include "kafka/AkcsKafkaProducer.h"
#include <mutex>

class OfficeUpdateInfoBase
{
public:
    OfficeUpdateInfoBase(const std::string   &office_uuid, uint32_t change_type)
    :change_type_(change_type),office_uuid_(office_uuid)
    {
        
    }

    ~OfficeUpdateInfoBase(){};
    virtual google::protobuf::MessageLite* GetInfo() = 0; 
    std::string GetUUID() {return office_uuid_;} 
   
private:
    uint32_t change_type_;
    std::string office_uuid_;
};


class OfficeNewFileUpdateInfo:public OfficeUpdateInfoBase
{
public:
    OfficeNewFileUpdateInfo(const std::string   &office_uuid, uint32_t change_type)
    :OfficeUpdateInfoBase(office_uuid, change_type)
    {
        
    }

    ~OfficeNewFileUpdateInfo(){};

    int SetMac(const std::string   &mac) { mac_ = mac; };
    int SetUid(const std::string   &uid) { uid_ = uid; };
    
    google::protobuf::MessageLite* GetInfo()
    {
       AK::AdaptOffice::OfficeUpdateBaseMessage base;
       
       AK::AdaptOffice::OfficeUpdateFileConfig info;
       info.set_mac(mac_);
       info.set_uid(uid_);

       
       base.mutable_file_update()->CopyFrom(info);
       return std::move(&base);    
    }

   
private:
    std::string uid_;
    std::string mac_;
};


class OfficeNewFileUpdateContorl
{
public:
    OfficeNewFileUpdateContorl(){}
    ~OfficeNewFileUpdateContorl(){}

    void ProduceMsg(const std::string &key, const google::protobuf::MessageLite* msg);
    void InitKafkaProducer(const std::string &ip, const std::string &topic);
    static OfficeNewFileUpdateContorl* Instance();
    static OfficeNewFileUpdateContorl* contorl_;   
private:
    std::shared_ptr<AkcsKafkaProducer> kafka_producer_;
    //有个多个消费者会调用数据分析，而丢这里没有按线程区分，只能加锁
    std::mutex kafka_producer_mutex_;
};




#endif //__CSADAPT_UPDATECONFIG_NEWOFFICE_FILEUPDATE_H__