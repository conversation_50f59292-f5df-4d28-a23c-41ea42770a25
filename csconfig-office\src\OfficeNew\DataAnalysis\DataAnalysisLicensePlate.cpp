#include "OfficeNew/DataAnalysis/DataAnalysisLicensePlate.h"

#include "DataAnalysisTableParse.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include "dbinterface/ProjectUserManage.h"
#include "OfficePduConfigMsg.h"

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "LicensePlate";
/*复制到DataAnalysisDef.h*/ 
enum DALicensePlateIndex{
    DA_INDEX_LICENSE_PLATE_PLATE,
    DA_INDEX_LICENSE_PLATE_UHF,
    DA_INDEX_LICENSE_PLATE_MANAGEUUID,
    DA_INDEX_LICENSE_PLATE_PERSONALACCOUNTUUID,
    DA_INDEX_LICENSE_PLATE_TIMECONTROL,
    DA_INDEX_LICENSE_PLATE_BEGINTIME,
    DA_INDEX_LICENSE_PLATE_ENDTIME,
};
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/ 
   {DA_INDEX_LICENSE_PLATE_PLATE, "Plate", ItemChangeHandle},
   {DA_INDEX_LICENSE_PLATE_UHF, "UHF", ItemChangeHandle},
   {DA_INDEX_LICENSE_PLATE_MANAGEUUID, "ManageUUID", ItemChangeHandle},
   {DA_INDEX_LICENSE_PLATE_PERSONALACCOUNTUUID, "PersonalAccountUUID", ItemChangeHandle},
   {DA_INDEX_LICENSE_PLATE_TIMECONTROL, "TimeControl", ItemChangeHandle},
   {DA_INDEX_LICENSE_PLATE_BEGINTIME, "BeginTime", ItemChangeHandle},
   {DA_INDEX_LICENSE_PLATE_ENDTIME, "EndTime", ItemChangeHandle},
   {DA_INDEX_INSERT, "", InsertHandle},
   {DA_INDEX_DELETE, "", DeleteHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    UpdateHandle(data, context);
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    UpdateHandle(data, context);
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string office_uuid = data.GetIndex(DA_INDEX_LICENSE_PLATE_MANAGEUUID);
    std::string per_account_uuid = data.GetIndex(DA_INDEX_LICENSE_PLATE_PERSONALACCOUNTUUID);
    dbinterface::OfficePersonalAccount::UpdateVersionByUUID(per_account_uuid);
    OfficeFileUpdateInfo update_info(office_uuid, OfficeUpdateType::OFFICE_USER_ACCESS_CHANGE);
    context.AddUpdateConfigInfo(update_info);
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaLicensePlateHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}



//拷贝到DataAnalysisContorl.cpp
// RegDaLicensePlateHandler();
// #include "OfficeNew/DataAnalysis/DataAnalysisLicensePlate.h"
