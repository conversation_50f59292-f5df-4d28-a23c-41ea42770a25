#include "OfficeNew/DataAnalysis/DataAnalysisManageFeature.h"

#include "DataAnalysisTableParse.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include "dbinterface/ProjectUserManage.h"
#include "OfficePduConfigMsg.h"
#include "dbinterface/Account.h"

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "ManageFeature";
/*复制到DataAnalysisDef.h*/ 
enum DAManageFeatureIndex{
    DA_INDEX_MANAGE_FEATURE_ID,
    DA_INDEX_MANAGE_FEATURE_ACCOUNTID,
    DA_INDEX_MANAGE_FEATURE_FEATUREID,
    DA_INDEX_MANAGE_FEATURE_FEEUUID,
};
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/ 
   {DA_INDEX_MANAGE_FEATURE_ID, "ID", ItemChangeHandle},
   {DA_INDEX_MANAGE_FEATURE_ACCOUNTID, "AccountID", ItemChangeHandle},
   {DA_INDEX_MANAGE_FEATURE_FEATUREID, "FeatureID", ItemChangeHandle},
   {DA_INDEX_MANAGE_FEATURE_FEEUUID, "FeeUUID", ItemChangeHandle},
   {DA_INDEX_INSERT, "", InsertHandle},
   {DA_INDEX_DELETE, "", DeleteHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    // 使用举例
    uint32_t account_id = data.GetIndexAsInt(DA_INDEX_MANAGE_FEATURE_ACCOUNTID);

    dbinterface::AccountInfo account;
    if (dbinterface::Account::GetAccountById(account_id, account) == 0)
    {
        OfficeFileUpdateInfo update_info(account.uuid, OfficeUpdateType::OFFICE_IMPORT_PROJECT);
        context.AddUpdateConfigInfo(update_info);        
    }
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaManageFeatureHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}



//拷贝到DataAnalysisContorl.cpp
// RegDaManageFeatureHandler();
// #include "OfficeNew/DataAnalysis/DataAnalysisManageFeature.h"
