#include "OfficeNew/DataAnalysis/DataAnalysisOfficeAccessGroup.h"

#include "DataAnalysisTableParse.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include "dbinterface/ProjectUserManage.h"
#include "OfficePduConfigMsg.h"
#include "InnerUtil.h"
#include "dbinterface/new-office/OfficeAccessGroup.h"

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "OfficeAccessGroup";
/*复制到DataAnalysisDef.h*/ 
enum DAOfficeAccessGroupIndex{
    DA_INDEX_OFFICE_ACCESS_GROUP_ID,
    DA_INDEX_OFFICE_ACCESS_GROUP_UUID,
    DA_INDEX_OFFICE_ACCESS_GROUP_NAME,
    DA_INDEX_OFFICE_ACCESS_GROUP_ACCOUNTUUID,
    DA_INDEX_OFFICE_ACCESS_GROUP_OFFICECOMPANYUUID,
    DA_INDEX_OFFICE_ACCESS_GROUP_SCHEDULERTYPE,
    DA_INDEX_OFFICE_ACCESS_GROUP_BEGINDATETIME,
    DA_INDEX_OFFICE_ACCESS_GROUP_ENDDATETIME,
    DA_INDEX_OFFICE_ACCESS_GROUP_ISDEFAULT,
    DA_INDEX_OFFICE_ACCESS_GROUP_RBACDATAGROUPUUID,
    DA_INDEX_OFFICE_ACCESS_GROUP_VERSION,
    DA_INDEX_OFFICE_ACCESS_GROUP_CREATETIME,
    DA_INDEX_OFFICE_ACCESS_GROUP_UPDATETIME,
};
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/ 
   {DA_INDEX_OFFICE_ACCESS_GROUP_ID, "ID", ItemChangeHandle},
   {DA_INDEX_OFFICE_ACCESS_GROUP_UUID, "UUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_ACCESS_GROUP_NAME, "Name", ItemChangeHandle},
   {DA_INDEX_OFFICE_ACCESS_GROUP_ACCOUNTUUID, "AccountUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_ACCESS_GROUP_OFFICECOMPANYUUID, "OfficeCompanyUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_ACCESS_GROUP_SCHEDULERTYPE, "SchedulerType", ItemChangeHandle},
   {DA_INDEX_OFFICE_ACCESS_GROUP_BEGINDATETIME, "BeginDateTime", ItemChangeHandle},
   {DA_INDEX_OFFICE_ACCESS_GROUP_ENDDATETIME, "EndDateTime", ItemChangeHandle},
   {DA_INDEX_OFFICE_ACCESS_GROUP_ISDEFAULT, "IsDefault", ItemChangeHandle},
   {DA_INDEX_INSERT, "", InsertHandle},
   {DA_INDEX_DELETE, "", DeleteHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

//新增和删除最终都会反馈到OfficeAccessGroupDevice, 这里不需要处理
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string project_uuid = data.GetIndex(DA_INDEX_OFFICE_ACCESS_GROUP_ACCOUNTUUID);
    std::string company_uuid = data.GetIndex(DA_INDEX_OFFICE_ACCESS_GROUP_OFFICECOMPANYUUID);

    UpdateUserVersionByCompanyUUID(company_uuid);   
    OfficeFileUpdateInfo update_info(project_uuid, OfficeUpdateType::OFFICE_ACCESS_GROUP_CHANGE);
    context.AddUpdateConfigInfo(update_info);    
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string project_uuid = data.GetIndex(DA_INDEX_OFFICE_ACCESS_GROUP_ACCOUNTUUID);
    std::string ag_uuid = data.GetIndex(DA_INDEX_OFFICE_ACCESS_GROUP_UUID);

    UpdateUserVersionByAgUUID(ag_uuid);
/*    
    AkcsStringSet dev_uuid_list;
    dbinterface::OfficeAccessGroup::GetDevListByAccessGroupUUID(ag_uuid, dev_uuid_list);
    
    OfficeFileUpdateInfo update_info(project_uuid, OFFICE_ACCESS_GROUP_CHANGE);
    for(auto &it : dev_uuid_list)
    {
        update_info.AddDevUUIDToList(it);  
    }
*/    
    OfficeFileUpdateInfo update_info(project_uuid, OfficeUpdateType::OFFICE_ACCESS_GROUP_CHANGE);
    context.AddUpdateConfigInfo(update_info);
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaOfficeAccessGroupHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}

