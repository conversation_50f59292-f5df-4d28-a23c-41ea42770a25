#include "OfficeNew/DataAnalysis/DataAnalysisOfficeAccessGroupDevice.h"

#include "DataAnalysisTableParse.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include "dbinterface/ProjectUserManage.h"
#include "OfficePduConfigMsg.h"
#include "InnerUtil.h"
#include "dbinterface/new-office/OfficeAccessGroup.h"

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "OfficeAccessGroupDevice";
/*复制到DataAnalysisDef.h*/ 
enum DAOfficeAccessGroupDeviceIndex{
    DA_INDEX_OFFICE_ACCESS_GROUP_DEVICE_ID,
    DA_INDEX_OFFICE_ACCESS_GROUP_DEVICE_UUID,
    DA_INDEX_OFFICE_ACCESS_GROUP_DEVICE_OFFICEACCESSGROUPUUID,
    DA_INDEX_OFFICE_ACCESS_GROUP_DEVICE_DEVICESUUID,
    DA_INDEX_OFFICE_ACCESS_GROUP_DEVICE_RELAY,
    DA_INDEX_OFFICE_ACCESS_GROUP_DEVICE_SECURITYRELAY,
    DA_INDEX_OFFICE_ACCESS_GROUP_DEVICE_VERSION,
    DA_INDEX_OFFICE_ACCESS_GROUP_DEVICE_CREATETIME,
    DA_INDEX_OFFICE_ACCESS_GROUP_DEVICE_UPDATETIME,
};
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/ 
   {DA_INDEX_OFFICE_ACCESS_GROUP_DEVICE_ID, "ID", ItemChangeHandle},
   {DA_INDEX_OFFICE_ACCESS_GROUP_DEVICE_UUID, "UUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_ACCESS_GROUP_DEVICE_OFFICEACCESSGROUPUUID, "OfficeAccessGroupUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_ACCESS_GROUP_DEVICE_DEVICESUUID, "DevicesUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_ACCESS_GROUP_DEVICE_RELAY, "Relay", ItemChangeHandle},
   {DA_INDEX_OFFICE_ACCESS_GROUP_DEVICE_SECURITYRELAY, "SecurityRelay", ItemChangeHandle},
   {DA_INDEX_INSERT, "", UpdateHandle},
   {DA_INDEX_DELETE, "", DeleteHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}


static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string ag_uuid = data.GetIndex(DA_INDEX_OFFICE_ACCESS_GROUP_DEVICE_OFFICEACCESSGROUPUUID);
    std::string dev_uuid = data.GetIndex(DA_INDEX_OFFICE_ACCESS_GROUP_DEVICE_DEVICESUUID);
    //权限组的relay更新了，需要更新对应的所有用户。
    UpdateUserVersionByAgUUID(ag_uuid);
    AkcsStringSet dev_uuid_list;
    std::string project_uuid = dbinterface::OfficeAccessGroup::GetProjectUUIDByAgUUID(ag_uuid);
    
    OfficeFileUpdateInfo update_info(project_uuid, OfficeUpdateType::OFFICE_ACCESS_GROUP_CHANGE);
    update_info.AddDevUUIDToList(dev_uuid);  
    context.AddUpdateConfigInfo(update_info);
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaOfficeAccessGroupDeviceHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}
