#include "OfficeNew/DataAnalysis/DataAnalysisOfficeAccessGroupScheduler.h"

#include "DataAnalysisTableParse.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include "dbinterface/ProjectUserManage.h"
#include "OfficePduConfigMsg.h"
#include "InnerUtil.h"
#include "dbinterface/new-office/OfficeAccessGroup.h"

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "OfficeAccessGroupScheduler";
/*复制到DataAnalysisDef.h*/ 
enum  DAOfficeAccessGroupSchedulerIndex{
    DA_INDEX_OFFICE_ACCESS_GROUP_SCHEDULER_ID,
    DA_INDEX_OFFICE_ACCESS_GROUP_SCHEDULER_UUID,
    DA_INDEX_OFFICE_ACCESS_GROUP_SCHEDULER_PART,
    DA_INDEX_OFFICE_ACCESS_GROUP_SCHEDULER_INDEX,
    DA_INDEX_OFFICE_ACCESS_GROUP_SCHEDULER_OFFICEACCESSGROUPUUID,
    DA_INDEX_OFFICE_ACCESS_GROUP_SCHEDULER_DATEFLAG,
    DA_INDEX_OFFICE_ACCESS_GROUP_SCHEDULER_STARTTIME,
    DA_INDEX_OFFICE_ACCESS_GROUP_SCHEDULER_STOPTIME,
    DA_INDEX_OFFICE_ACCESS_GROUP_SCHEDULER_CREATETIME,
    DA_INDEX_OFFICE_ACCESS_GROUP_SCHEDULER_UPDATETIME,
};
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/ 
   {DA_INDEX_OFFICE_ACCESS_GROUP_SCHEDULER_ID, "ID", ItemChangeHandle},
   {DA_INDEX_OFFICE_ACCESS_GROUP_SCHEDULER_UUID, "UUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_ACCESS_GROUP_SCHEDULER_PART, "Part", ItemChangeHandle},
   {DA_INDEX_OFFICE_ACCESS_GROUP_SCHEDULER_INDEX, "Index", ItemChangeHandle},
   {DA_INDEX_OFFICE_ACCESS_GROUP_SCHEDULER_OFFICEACCESSGROUPUUID, "OfficeAccessGroupUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_ACCESS_GROUP_SCHEDULER_DATEFLAG, "DateFlag", ItemChangeHandle},
   {DA_INDEX_OFFICE_ACCESS_GROUP_SCHEDULER_STARTTIME, "StartTime", ItemChangeHandle},
   {DA_INDEX_OFFICE_ACCESS_GROUP_SCHEDULER_STOPTIME, "StopTime", ItemChangeHandle},
   {DA_INDEX_INSERT, "", UpdateHandle},
   {DA_INDEX_DELETE, "", DeleteHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string ag_uuid = data.GetIndex(DA_INDEX_OFFICE_ACCESS_GROUP_SCHEDULER_OFFICEACCESSGROUPUUID);
    std::string project_uuid = dbinterface::OfficeAccessGroup::GetProjectUUIDByAgUUID(ag_uuid);

    UpdateUserVersionByAgUUID(ag_uuid);
    
    OfficeFileUpdateInfo update_info(project_uuid, OfficeUpdateType::OFFICE_ACCESS_GROUP_CHANGE);
    context.AddUpdateConfigInfo(update_info);
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaOfficeAccessGroupSchedulerHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}

