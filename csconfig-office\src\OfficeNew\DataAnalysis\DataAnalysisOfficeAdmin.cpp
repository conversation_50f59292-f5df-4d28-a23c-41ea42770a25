#include "OfficeNew/DataAnalysis/DataAnalysisOfficeAdmin.h"

#include "DataAnalysisTableParse.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include "dbinterface/ProjectUserManage.h"
#include "OfficePduConfigMsg.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "DataAnalysisDef.h"
#include "AkcsCommonDef.h"

static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "OfficeAdmin";

enum DAOfficeAdminIndex{
    DA_INDEX_OFFICE_ADMIN_ID,
    DA_INDEX_OFFICE_ADMIN_UUID,
    DA_INDEX_OFFICE_ADMIN_OFFICEUUID,
    DA_INDEX_OFFICE_ADMIN_OFFICECOMPANYUUID,
    DA_INDEX_OFFICE_ADMIN_ACCOUNTUUID,
    DA_INDEX_OFFICE_ADMIN_NAME,
    DA_INDEX_OFFICE_ADMIN_FIRSTNAME,
    DA_INDEX_OFFICE_ADMIN_LASTNAME,
    DA_INDEX_OFFICE_ADMIN_PHONECODE,
    DA_INDEX_OFFICE_ADMIN_ROLE,
    DA_INDEX_OFFICE_ADMIN_CREATETIME,
    DA_INDEX_OFFICE_ADMIN_UPDATETIME,
    DA_INDEX_OFFICE_ADMIN_PERSONALACCOUNTUUID,
    DA_INDEX_OFFICE_ADMIN_CALLTYPE,
    DA_INDEX_OFFICE_ADMIN_APPSTATUS,
};

static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/ 
   {DA_INDEX_OFFICE_ADMIN_ID, "ID", ItemChangeHandle},
   {DA_INDEX_OFFICE_ADMIN_UUID, "UUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_ADMIN_OFFICEUUID, "OfficeUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_ADMIN_OFFICECOMPANYUUID, "OfficeCompanyUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_ADMIN_ACCOUNTUUID, "AccountUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_ADMIN_NAME, "Name", ItemChangeHandle},
   {DA_INDEX_OFFICE_ADMIN_FIRSTNAME, "FirstName", ItemChangeHandle},
   {DA_INDEX_OFFICE_ADMIN_LASTNAME, "LastName", ItemChangeHandle},
   {DA_INDEX_OFFICE_ADMIN_PHONECODE, "PhoneCode", ItemChangeHandle},
   {DA_INDEX_OFFICE_ADMIN_ROLE, "Role", ItemChangeHandle},
   {DA_INDEX_OFFICE_ADMIN_CREATETIME, "CreateTime", ItemChangeHandle},
   {DA_INDEX_OFFICE_ADMIN_UPDATETIME, "UpdateTime", ItemChangeHandle},
   {DA_INDEX_OFFICE_ADMIN_PERSONALACCOUNTUUID, "PersonalAccountUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_ADMIN_CALLTYPE, "CallType", ItemChangeHandle},
   {DA_INDEX_OFFICE_ADMIN_APPSTATUS, "AppStatus", ItemChangeHandle},
   {DA_INDEX_INSERT, "", UpdateHandle},
   {DA_INDEX_DELETE, "", UpdateHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{   
    bool is_update_operation = (data.GetOperation() == DataAnalysisTableParse::DBHandleType::DA_OPERATION_UPDATE);

    // 只有App开关变化或App状态开启的情况下更新，才执行刷配置的动作
    // 插入和删除动作在PersonalAccount表处理了
    if (is_update_operation)
    {
        bool is_app_status_change = data.IsIndexChange(DA_INDEX_OFFICE_ADMIN_APPSTATUS);
        bool is_app_status_enable = data.GetIndexAsInt(DA_INDEX_OFFICE_ADMIN_APPSTATUS) == (int)AdminAppStatus::ENABLE;

        if (is_app_status_change || is_app_status_enable)
        {
            std::string uuid = data.GetIndex(DA_INDEX_OFFICE_ADMIN_PERSONALACCOUNTUUID);
            std::string office_uuid = data.GetIndex(DA_INDEX_OFFICE_ADMIN_OFFICEUUID);

            //更新数据版本
            dbinterface::OfficePersonalAccount::UpdateVersionByUUID(uuid);

            OfficeFileUpdateInfo update_info(office_uuid, OfficeUpdateType::OFFICE_USER_INFO_CHANGE);

            context.AddUpdateConfigInfo(update_info);
        }
    }
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaOfficeAdminHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}

