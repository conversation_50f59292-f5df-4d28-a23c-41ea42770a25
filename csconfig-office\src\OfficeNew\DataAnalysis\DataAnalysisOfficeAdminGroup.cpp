#include "OfficeNew/DataAnalysis/DataAnalysisOfficeAdminGroup.h"

#include "DataAnalysisTableParse.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include "dbinterface/ProjectUserManage.h"
#include "OfficePduConfigMsg.h"
#include "dbinterface/new-office/OfficeGroup.h"
#include "dbinterface/new-office/OfficeAdmin.h"
#include "dbinterface/office/OfficePersonalAccount.h"


static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "OfficeAdminGroup";

enum DAOfficeAdminGroupIndex{
    DA_INDEX_OFFICE_ADMIN_GROUP_UUID,
    DA_INDEX_OFFICE_ADMIN_GROUP_ACCOUNTUUID,
    DA_INDEX_OFFICE_ADMIN_GROUP_PERSONALACCOUNTUUID,
    DA_INDEX_OFFICE_ADMIN_GROUP_OFFICEGROUPUUID,
    DA_INDEX_OFFICE_ADMIN_GROUP_CREATETIME,
    DA_INDEX_OFFICE_ADMIN_GROUP_UPDATETIME,
};

static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/ 
   {DA_INDEX_OFFICE_ADMIN_GROUP_UUID, "UUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_ADMIN_GROUP_ACCOUNTUUID, "AccountUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_ADMIN_GROUP_PERSONALACCOUNTUUID, "PersonalAccountUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_ADMIN_GROUP_OFFICEGROUPUUID, "OfficeGroupUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_ADMIN_GROUP_CREATETIME, "CreateTime", ItemChangeHandle},
   {DA_INDEX_OFFICE_ADMIN_GROUP_UPDATETIME, "UpdateTime", ItemChangeHandle},
   {DA_INDEX_INSERT, "", UpdateHandle},
   {DA_INDEX_DELETE, "", UpdateHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}


static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    AK_LOG_INFO << "OfficeAdminGroup UpdateHandle";
    std::string per_uuid = data.GetIndex(DA_INDEX_OFFICE_ADMIN_GROUP_PERSONALACCOUNTUUID);
    if (per_uuid.empty()) {
        AK_LOG_WARN << "OfficeAdminGroup UpdateHandle: PersonalAccountUUID is empty";
        return 0;
    }

    OfficeAdminInfo admin_info;
    if (0 != dbinterface::OfficeAdmin::GetOfficeAdminByPersonalAccountUUID(per_uuid, admin_info)) {
        AK_LOG_WARN << "OfficeAdminGroup UpdateHandle: GetOfficeAdminByPersonalAccountUUID failed";
        return 0;
    }

    if (admin_info.app_status == (int)AdminAppStatus::DISABLE) {
        AK_LOG_WARN << "OfficeAdminGroup UpdateHandle: Admin is not enabled";
        return 0;
    }

    dbinterface::OfficePersonalAccount::UpdateVersionByUUID(per_uuid);

    std::string group_uuid = data.GetIndex(DA_INDEX_OFFICE_ADMIN_GROUP_OFFICEGROUPUUID);
    OfficeGroupInfo info;
    dbinterface::OfficeGroup::GetOfficeGroupByUUID(group_uuid, info);

    OfficeFileUpdateInfo update_info(info.project_uuid, OfficeUpdateType::OFFICE_GROUP_CHANGE);
    context.AddUpdateConfigInfo(update_info);

    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaOfficeAdminGroupHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}

