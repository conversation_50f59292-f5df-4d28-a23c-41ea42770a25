#include "OfficeNew/DataAnalysis/DataAnalysisOfficeAttendanceDevice.h"

#include "DataAnalysisTableParse.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include "dbinterface/ProjectUserManage.h"
#include "OfficePduConfigMsg.h"
#include "IPCControl.h"

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "OfficeAttendanceDevice";

enum DAOfficeAttendanceDeviceIndex{
    DA_INDEX_OFFICE_ATTENDANCE_DEVICE_OFFICECOMPANYUUID,
    DA_INDEX_OFFICE_ATTENDANCE_DEVICE_DEVICESUUID,
};

static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
   {DA_INDEX_OFFICE_ATTENDANCE_DEVICE_OFFICECOMPANYUUID, "OfficeCompanyUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_ATTENDANCE_DEVICE_DEVICESUUID, "DevicesUUID", ItemChangeHandle},
   {DA_INDEX_INSERT, "", InsertHandle},
   {DA_INDEX_DELETE, "", DeleteHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string devices_uuid = data.GetIndex(DA_INDEX_OFFICE_ATTENDANCE_DEVICE_DEVICESUUID);
    GetIPCControlInstance()->NotifyDeviceIsAttendance(devices_uuid);
    AK_LOG_INFO << "NotifyDeviceIsAttendance device uuid=" << devices_uuid;
    return 0;
}

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    CommonChangeHandle(data, context);
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    CommonChangeHandle(data, context);
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    CommonChangeHandle(data, context);
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    if (data.IsIndexChange(DA_INDEX_OFFICE_ATTENDANCE_DEVICE_OFFICECOMPANYUUID) ||
    data.IsIndexChange(DA_INDEX_OFFICE_ATTENDANCE_DEVICE_DEVICESUUID)
    )
    {
        std::string before_devices_uuid = data.GetBeforeIndex(DA_INDEX_OFFICE_ATTENDANCE_DEVICE_DEVICESUUID);
        std::string after_devices_uuid = data.GetIndex(DA_INDEX_OFFICE_ATTENDANCE_DEVICE_DEVICESUUID);
        if (before_devices_uuid != after_devices_uuid) {
            GetIPCControlInstance()->NotifyDeviceIsAttendance(before_devices_uuid);
        }
        CommonChangeHandle(data, context);
    }
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaOfficeAttendanceDeviceHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}