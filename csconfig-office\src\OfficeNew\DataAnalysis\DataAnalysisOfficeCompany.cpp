#include "OfficeNew/DataAnalysis/DataAnalysisOfficeCompany.h"

#include "DataAnalysisTableParse.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include "dbinterface/ProjectUserManage.h"
#include "OfficePduConfigMsg.h"

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "OfficeCompany";
/*复制到DataAnalysisDef.h*/ 
enum DAOfficeCompanyIndex{
    DA_INDEX_OFFICE_COMPANY_ID,
    DA_INDEX_OFFICE_COMPANY_UUID,
    DA_INDEX_OFFICE_COMPANY_ACCOUNTUUID,
    DA_INDEX_OFFICE_COMPANY_NAME,
};
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/ 
   {DA_INDEX_OFFICE_COMPANY_ID, "ID", ItemChangeHandle},
   {DA_INDEX_OFFICE_COMPANY_UUID, "UUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_COMPANY_ACCOUNTUUID, "AccountUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_COMPANY_NAME, "Name", ItemChangeHandle},
   {DA_INDEX_INSERT, "", InsertHandle},
   {DA_INDEX_DELETE, "", DeleteHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    // 使用举例
    std::string office_uuid = data.GetIndex(DA_INDEX_OFFICE_COMPANY_ACCOUNTUUID);

    OfficeFileUpdateInfo update_info(office_uuid, OfficeUpdateType::OFFICE_PROJECT_CHANGE);
    context.AddUpdateConfigInfo(update_info);
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaOfficeCompanyHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}



//拷贝到DataAnalysisContorl.cpp
// RegDaOfficeCompanyHandler();
// #include "OfficeNew/DataAnalysis/DataAnalysisOfficeCompany.h"
