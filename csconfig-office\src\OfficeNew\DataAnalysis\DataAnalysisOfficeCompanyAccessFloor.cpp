#include "OfficeNew/DataAnalysis/DataAnalysisOfficeCompanyAccessFloor.h"

#include "DataAnalysisTableParse.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include "dbinterface/ProjectUserManage.h"
#include "OfficePduConfigMsg.h"
#include "dbinterface/new-office/OfficeCompany.h"


static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "OfficeCompanyAccessFloor";


enum DAOfficeCompanyAccessFloorIndex{
    DA_INDEX_OFFICE_COMPANY_ACCESS_FLOOR_ID,
    DA_INDEX_OFFICE_COMPANY_ACCESS_FLOOR_UUID,
    DA_INDEX_OFFICE_COMPANY_ACCESS_FLOOR_OFFICECOMPANYUUID,
    DA_INDEX_OFFICE_COMPANY_ACCESS_FLOOR_COMMUNITYUNITUUID,
    DA_INDEX_OFFICE_COMPANY_ACCESS_FLOOR_ACCESSFLOORS,
    DA_INDEX_OFFICE_COMPANY_ACCESS_FLOOR_CREATETIME,
    DA_INDEX_OFFICE_COMPANY_ACCESS_FLOOR_UPDATETIME,
};


static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/ 
   {DA_INDEX_OFFICE_COMPANY_ACCESS_FLOOR_ID, "ID", ItemChangeHandle},
   {DA_INDEX_OFFICE_COMPANY_ACCESS_FLOOR_UUID, "UUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_COMPANY_ACCESS_FLOOR_OFFICECOMPANYUUID, "OfficeCompanyUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_COMPANY_ACCESS_FLOOR_COMMUNITYUNITUUID, "CommunityUnitUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_COMPANY_ACCESS_FLOOR_ACCESSFLOORS, "AccessFloors", ItemChangeHandle},
   {DA_INDEX_OFFICE_COMPANY_ACCESS_FLOOR_CREATETIME, "CreateTime", ItemChangeHandle},
   {DA_INDEX_OFFICE_COMPANY_ACCESS_FLOOR_UPDATETIME, "UpdateTime", ItemChangeHandle},
   {DA_INDEX_INSERT, "", InsertHandle},
   {DA_INDEX_DELETE, "", DeleteHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string companny_uuid = data.GetIndex(DA_INDEX_OFFICE_COMPANY_ACCESS_FLOOR_OFFICECOMPANYUUID);
    
    OfficeCompanyInfo info;
    dbinterface::OfficeCompany::GetOfficeCompanyByUUID(companny_uuid, info);
    
    UpdateUserVersionByCompanyUUID(companny_uuid);

    OfficeFileUpdateInfo update_info(info.project_uuid, OfficeUpdateType::OFFICE_COMPANY_INFO_CHANGE); 
    context.AddUpdateConfigInfo(update_info);
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string companny_uuid = data.GetIndex(DA_INDEX_OFFICE_COMPANY_ACCESS_FLOOR_OFFICECOMPANYUUID);
    
    OfficeCompanyInfo info;
    dbinterface::OfficeCompany::GetOfficeCompanyByUUID(companny_uuid, info);
    
    UpdateUserVersionByCompanyUUID(companny_uuid);

    OfficeFileUpdateInfo update_info(info.project_uuid, OfficeUpdateType::OFFICE_COMPANY_INFO_CHANGE); 
    context.AddUpdateConfigInfo(update_info);
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string companny_uuid = data.GetIndex(DA_INDEX_OFFICE_COMPANY_ACCESS_FLOOR_OFFICECOMPANYUUID);
    
    OfficeCompanyInfo info;
    dbinterface::OfficeCompany::GetOfficeCompanyByUUID(companny_uuid, info);
    
    UpdateUserVersionByCompanyUUID(companny_uuid);

    OfficeFileUpdateInfo update_info(info.project_uuid, OfficeUpdateType::OFFICE_COMPANY_INFO_CHANGE); 
    context.AddUpdateConfigInfo(update_info);
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaOfficeCompanyAccessFloorHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}



//拷贝到DataAnalysisContorl.cpp
// RegDaOfficeCompanyAccessFloorHandler();
// #include "OfficeNew/DataAnalysis/DataAnalysisOfficeCompanyAccessFloor.h"
