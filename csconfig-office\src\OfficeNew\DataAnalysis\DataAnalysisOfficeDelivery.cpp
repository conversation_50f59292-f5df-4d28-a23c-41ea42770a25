#include "OfficeNew/DataAnalysis/DataAnalysisOfficeDelivery.h"

#include "DataAnalysisTableParse.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include "dbinterface/ProjectUserManage.h"
#include "OfficePduConfigMsg.h"
#include "dbinterface/new-office/OfficeDelivery.h"

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "OfficeDelivery";
/*复制到DataAnalysisDef.h*/ 
enum DAOfficeDeliveryIndex{
    DA_INDEX_OFFICE_DELIVERY_ID,
    DA_INDEX_OFFICE_DELIVERY_UUID,
    DA_INDEX_OFFICE_DELIVERY_ACCOUNTUUID,
    DA_INDEX_OFFICE_DELIVERY_OFFICECOMPANYUUID,
    DA_INDEX_OFFICE_DELIVERY_NAME,
    DA_INDEX_OFFICE_DELIVERY_RBACDATAGROUPUUID,
    DA_INDEX_OFFICE_DELIVERY_VERSION,
    DA_INDEX_OFFICE_DELIVERY_USERVERSION,
    DA_INDEX_OFFICE_DELIVERY_CREATETIME,
    DA_INDEX_OFFICE_DELIVERY_UPDATETIME,
};
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/ 
   {DA_INDEX_OFFICE_DELIVERY_ID, "ID", ItemChangeHandle},
   {DA_INDEX_OFFICE_DELIVERY_UUID, "UUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_DELIVERY_ACCOUNTUUID, "AccountUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_DELIVERY_OFFICECOMPANYUUID, "OfficeCompanyUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_DELIVERY_NAME, "Name", ItemChangeHandle},
   {DA_INDEX_INSERT, "", UpdateHandle},
   {DA_INDEX_DELETE, "", UpdateHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

/*
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}
*/
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string project_uuid = data.GetIndex(DA_INDEX_OFFICE_DELIVERY_ACCOUNTUUID);
    std::string delivery_uuid = data.GetIndex(DA_INDEX_OFFICE_DELIVERY_UUID);
    
    dbinterface::OfficeDelivery::UpdateDeliveryVersion(delivery_uuid);


    OfficeFileUpdateInfo update_info(project_uuid, OfficeUpdateType::OFFICE_PUB_USER_INFO_CHANGE); 
    context.AddUpdateConfigInfo(update_info);
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaOfficeDeliveryHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}

