#include "OfficeNew/DataAnalysis/DataAnalysisOfficeDeliveryAccessGroup.h"

#include "DataAnalysisTableParse.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include "dbinterface/ProjectUserManage.h"
#include "OfficePduConfigMsg.h"
#include "dbinterface/new-office/OfficeDelivery.h"
#include "InnerUtil.h"
static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "OfficeDeliveryAccessGroup";
/*复制到DataAnalysisDef.h*/ 
enum DAOfficeDeliveryAccessGroupIndex{
    DA_INDEX_OFFICE_DELIVERY_ACCESS_GROUP_ID,
    DA_INDEX_OFFICE_DELIVERY_ACCESS_GROUP_UUID,
    DA_INDEX_OFFICE_DELIVERY_ACCESS_GROUP_OFFICEDELIVERYUUID,
    DA_INDEX_OFFICE_DELIVERY_ACCESS_GROUP_OFFICEACCESSGROUPUUID,
    DA_INDEX_OFFICE_DELIVERY_ACCESS_GROUP_CREATETIME,
    DA_INDEX_OFFICE_DELIVERY_ACCESS_GROUP_UPDATETIME,
};
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/ 
   {DA_INDEX_OFFICE_DELIVERY_ACCESS_GROUP_ID, "ID", ItemChangeHandle},
   {DA_INDEX_OFFICE_DELIVERY_ACCESS_GROUP_UUID, "UUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_DELIVERY_ACCESS_GROUP_OFFICEDELIVERYUUID, "OfficeDeliveryUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_DELIVERY_ACCESS_GROUP_OFFICEACCESSGROUPUUID, "OfficeAccessGroupUUID", ItemChangeHandle},
   {DA_INDEX_INSERT, "", UpdateHandle},
   {DA_INDEX_DELETE, "", UpdateHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

/*
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}
*/
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string delivery_uuid = data.GetIndex(DA_INDEX_OFFICE_DELIVERY_ACCESS_GROUP_OFFICEDELIVERYUUID);
    
    dbinterface::OfficeDelivery::UpdateDeliveryVersion(delivery_uuid);

    OfficeDeliveryInfo info;
    dbinterface::OfficeDelivery::GetOfficeDeliveryByUUID(delivery_uuid, info);
    std::string project_uuid = info.project_uuid;
    
    OfficeFileUpdateInfo update_info(project_uuid, OfficeUpdateType::OFFICE_PUB_USER_INFO_CHANGE); 
    context.AddUpdateConfigInfo(update_info);
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaOfficeDeliveryAccessGroupHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}


