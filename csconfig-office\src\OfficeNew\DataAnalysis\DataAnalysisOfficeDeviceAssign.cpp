#include "OfficeNew/DataAnalysis/DataAnalysisOfficeDeviceAssign.h"

#include "DataAnalysisTableParse.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include "dbinterface/ProjectUserManage.h"
#include "OfficePduConfigMsg.h"
#include "dbinterface/new-office/OfficeDeviceAssign.h"



static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "OfficeDeviceAssign";
/*复制到DataAnalysisDef.h*/ 
enum  DAOfficeDeviceAssignIndex{
    DA_INDEX_OFFICE_DEVICE_ASSIGN_ID,
    DA_INDEX_OFFICE_DEVICE_ASSIGN_UUID,
    DA_INDEX_OFFICE_DEVICE_ASSIGN_ACCOUNTUUID,
    DA_INDEX_OFFICE_DEVICE_ASSIGN_PERSONALACCOUNTUUID,
    DA_INDEX_OFFICE_DEVICE_ASSIGN_OFFICEGROUPUUID,
    DA_INDEX_OFFICE_DEVICE_ASSIGN_DEVICESUUID,
    DA_INDEX_OFFICE_DEVICE_ASSIGN_TYPE,
    DA_INDEX_OFFICE_DEVICE_ASSIGN_CREATETIME,
    DA_INDEX_OFFICE_DEVICE_ASSIGN_UPDATETIME,
};
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/ 
   {DA_INDEX_OFFICE_DEVICE_ASSIGN_ID, "ID", ItemChangeHandle},
   {DA_INDEX_OFFICE_DEVICE_ASSIGN_UUID, "UUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_DEVICE_ASSIGN_ACCOUNTUUID, "AccountUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_DEVICE_ASSIGN_PERSONALACCOUNTUUID, "PersonalAccountUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_DEVICE_ASSIGN_OFFICEGROUPUUID, "OfficeGroupUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_DEVICE_ASSIGN_DEVICESUUID, "DevicesUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_DEVICE_ASSIGN_TYPE, "Type", ItemChangeHandle},
   {DA_INDEX_INSERT, "", UpdateHandle},
   {DA_INDEX_DELETE, "", UpdateHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

/*
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}
*/
//分配室内机给用户，需要更新用户有权限的设备联系人+管理机
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string dev_uuid = data.GetIndex(DA_INDEX_OFFICE_DEVICE_ASSIGN_DEVICESUUID);
    std::string project_uuid = data.GetIndex(DA_INDEX_OFFICE_DEVICE_ASSIGN_ACCOUNTUUID);
    DeviceAssignType type = (DeviceAssignType)data.GetIndexAsInt(DA_INDEX_OFFICE_DEVICE_ASSIGN_TYPE);
    
    if (type == DeviceAssignType::DeviceAssignTypGroup)
    {
        std::string per_uuid = data.GetIndex(DA_INDEX_OFFICE_DEVICE_ASSIGN_OFFICEGROUPUUID);    
    }
    else if (type == DeviceAssignType::DeviceAssignTypPer)
    {
        std::string per_uuid = data.GetIndex(DA_INDEX_OFFICE_DEVICE_ASSIGN_PERSONALACCOUNTUUID);      
    }
    
    OfficeFileUpdateInfo update_info(project_uuid, OfficeUpdateType::OFFICE_DEV_INFO_CHANGE); //TODO
    update_info.AddDevUUIDToList(dev_uuid);
    context.AddUpdateConfigInfo(update_info);
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaOfficeDeviceAssignHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}

