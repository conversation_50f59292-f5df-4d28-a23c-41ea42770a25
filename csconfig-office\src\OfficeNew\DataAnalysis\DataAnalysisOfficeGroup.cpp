#include "OfficeNew/DataAnalysis/DataAnalysisOfficeGroup.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include "dbinterface/ProjectUserManage.h"
#include "OfficePduConfigMsg.h"
#include "dbinterface/new-office/OfficeGroup.h"

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "OfficeGroup";
/*复制到DataAnalysisDef.h*/ 
enum DAOfficeGroupIndex{
    DA_INDEX_OFFICE_GROUP_ID,
    DA_INDEX_OFFICE_GROUP_UUID,
    DA_INDEX_OFFICE_GROUP_OFFICECOMPANYUUID,
    DA_INDEX_OFFICE_GROUP_ACCOUNTUUID,
    DA_INDEX_OFFICE_GROUP_NAME,
    DA_INDEX_OFFICE_GROUP_DISPLAYTYPE,
    DA_INDEX_OFFICE_GROUP_ISIMMUNEANTIPASSBACK,
    DA_INDEX_OFFICE_GROUP_RBACDATAGROUPUUID,
    DA_INDEX_OFFICE_GROUP_VERSION,
    DA_INDEX_OFFICE_GROUP_CREATETIME,
    DA_INDEX_OFFICE_GROUP_UPDATETIME,
};
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/ 
   {DA_INDEX_OFFICE_GROUP_ID, "ID", ItemChangeHandle},
   {DA_INDEX_OFFICE_GROUP_UUID, "UUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_GROUP_OFFICECOMPANYUUID, "OfficeCompanyUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_GROUP_ACCOUNTUUID, "AccountUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_GROUP_NAME, "Name", ItemChangeHandle},
   {DA_INDEX_OFFICE_GROUP_DISPLAYTYPE, "DisplayType", ItemChangeHandle},
   {DA_INDEX_OFFICE_GROUP_ISIMMUNEANTIPASSBACK, "IsImmuneAntipassback", ItemChangeHandle},
   {DA_INDEX_OFFICE_GROUP_RBACDATAGROUPUUID, "RBACDataGroupUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_GROUP_VERSION, "Version", ItemChangeHandle},
   {DA_INDEX_OFFICE_GROUP_CREATETIME, "CreateTime", ItemChangeHandle},
   {DA_INDEX_OFFICE_GROUP_UPDATETIME, "UpdateTime", ItemChangeHandle},
   {DA_INDEX_INSERT, "", InsertHandle},
   {DA_INDEX_DELETE, "", DeleteHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

//组变化 有权限的设备 全部更新
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string group_uuid = data.GetIndex(DA_INDEX_OFFICE_GROUP_UUID);
    
    OfficeGroupInfo info;
    dbinterface::OfficeGroup::GetOfficeGroupByUUID(group_uuid, info);
    UpdateUserVersionByGroupUUID(group_uuid);

    OfficeFileUpdateInfo update_info(info.project_uuid, OfficeUpdateType::OFFICE_GROUP_CHANGE);    
    context.AddUpdateConfigInfo(update_info);

    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaOfficeGroupHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}
