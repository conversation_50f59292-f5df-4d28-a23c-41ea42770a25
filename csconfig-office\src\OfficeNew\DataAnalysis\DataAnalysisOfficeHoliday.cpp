#include "OfficeNew/DataAnalysis/DataAnalysisOfficeHoliday.h"

#include "DataAnalysisTableParse.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include "dbinterface/ProjectUserManage.h"
#include "OfficePduConfigMsg.h"

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "OfficeHoliday";
/*复制到DataAnalysisDef.h*/ 
enum DAOfficeHolidayIndex{
    DA_INDEX_OFFICE_HOLIDAY_ID,
    DA_INDEX_OFFICE_HOLIDAY_UUID,
    DA_INDEX_OFFICE_HOLIDAY_ACCOUNTUUID,
    DA_INDEX_OFFICE_HOLIDAY_NAME,
    DA_INDEX_OFFICE_HOLIDAY_ISCREATEDBYADMIN,
    DA_INDEX_OFFICE_HOLIDAY_ISALLCOMPANY,
    DA_INDEX_OFFICE_HOLIDAY_ISWORKINGHOURS,
    DA_INDEX_OFFICE_HOLIDAY_STARTTIME,
    DA_INDEX_OFFICE_HOLIDAY_STOPTIME,
    DA_INDEX_OFFICE_HOLIDAY_ISYEARREPEAT,
    DA_INDEX_OFFICE_HOLIDAY_YEAR,
    DA_INDEX_OFFICE_HOLIDAY_DATES,
};
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/ 
   {DA_INDEX_OFFICE_HOLIDAY_ID, "ID", ItemChangeHandle},
   {DA_INDEX_OFFICE_HOLIDAY_UUID, "UUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_HOLIDAY_ACCOUNTUUID, "AccountUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_HOLIDAY_NAME, "Name", ItemChangeHandle},
   {DA_INDEX_OFFICE_HOLIDAY_ISCREATEDBYADMIN, "IsCreatedByAdmin", ItemChangeHandle},
   {DA_INDEX_OFFICE_HOLIDAY_ISALLCOMPANY, "IsAllCompany", ItemChangeHandle},
   {DA_INDEX_OFFICE_HOLIDAY_ISWORKINGHOURS, "IsWorkingHours", ItemChangeHandle},
   {DA_INDEX_OFFICE_HOLIDAY_STARTTIME, "StartTime", ItemChangeHandle},
   {DA_INDEX_OFFICE_HOLIDAY_STOPTIME, "StopTime", ItemChangeHandle},
   {DA_INDEX_OFFICE_HOLIDAY_ISYEARREPEAT, "IsYearRepeat", ItemChangeHandle},
   {DA_INDEX_OFFICE_HOLIDAY_YEAR, "Year", ItemChangeHandle},
   {DA_INDEX_OFFICE_HOLIDAY_DATES, "Dates", ItemChangeHandle},
   {DA_INDEX_INSERT, "", UpdateHandle},
   {DA_INDEX_DELETE, "", UpdateHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

/*
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}
*/
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string project_uuid = data.GetIndex(DA_INDEX_OFFICE_HOLIDAY_ACCOUNTUUID);

    OfficeFileUpdateInfo update_info(project_uuid, OfficeUpdateType::OFFICE_HOLIDAY_CHANGE); 
    context.AddUpdateConfigInfo(update_info);

    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaOfficeHolidayHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}

