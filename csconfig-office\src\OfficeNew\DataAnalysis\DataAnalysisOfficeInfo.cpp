#include "OfficeNew/DataAnalysis/DataAnalysisOfficeInfo.h"
#include "DataAnalysisContorl.h"
#include "OfficePduConfigMsg.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/Account.h"
#include <string.h>
#include <memory>
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "dbinterface/office/OfficeInfo.h"





static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "OfficeInfo";

enum DAOfficeInfoIndex{
    DA_INDEX_OFFICEINFO_ID,
    DA_INDEX_OFFICEINFO_ACCOUNTUUID,
    DA_INDEX_OFFICEINFO_STREET,
    DA_INDEX_OFFICEINFO_CITY,
    DA_INDEX_OFFICEINFO_POSTALCODE,
    DA_INDEX_OFFICEINFO_COUNTRY,
    DA_INDEX_OFFICEINFO_STATES,
    DA_INDEX_OFFICEINFO_ENABLEMOTION,
    DA_INDEX_OFFICEINFO_MOTIONTIME,
    DA_INDEX_OFFICEINFO_SWITCH,
    DA_INDEX_OFFICEINFO_FEATUREEXPIRETIME,
    DA_INDEX_OFFICEINFO_NAMEDISPLAY,
};


static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
    {DA_INDEX_OFFICEINFO_ID, "ID", ItemChangeHandle},
    {DA_INDEX_OFFICEINFO_ACCOUNTUUID, "AccountUUID", ItemChangeHandle},
    {DA_INDEX_OFFICEINFO_STREET, "Street", ItemChangeHandle},
    {DA_INDEX_OFFICEINFO_CITY, "City", ItemChangeHandle},
    {DA_INDEX_OFFICEINFO_POSTALCODE, "PostalCode", ItemChangeHandle},
    {DA_INDEX_OFFICEINFO_COUNTRY, "Country", ItemChangeHandle},
    {DA_INDEX_OFFICEINFO_STATES, "States", ItemChangeHandle},
    {DA_INDEX_OFFICEINFO_ENABLEMOTION, "EnableMotion", ItemChangeHandle},
    {DA_INDEX_OFFICEINFO_MOTIONTIME, "MotionTime", ItemChangeHandle},
    {DA_INDEX_OFFICEINFO_SWITCH, "Switch", ItemChangeHandle},
    {DA_INDEX_OFFICEINFO_FEATUREEXPIRETIME, "FeatureExpireTime", ItemChangeHandle},
    {DA_INDEX_OFFICEINFO_NAMEDISPLAY, "NameDisplay", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //创建office时没有设备，所以无需刷新配置
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //办公的删除在special处理
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string project_uuid = data.GetIndex(DA_INDEX_OFFICEINFO_ACCOUNTUUID);
    
    if (data.IsIndexChange(DA_INDEX_OFFICEINFO_STREET) 
        || data.IsIndexChange(DA_INDEX_OFFICEINFO_CITY)
        || data.IsIndexChange(DA_INDEX_OFFICEINFO_POSTALCODE)
        || data.IsIndexChange(DA_INDEX_OFFICEINFO_COUNTRY)
        || data.IsIndexChange(DA_INDEX_OFFICEINFO_STATES)
        || data.IsIndexChange(DA_INDEX_OFFICEINFO_ENABLEMOTION)
        || data.IsIndexChange(DA_INDEX_OFFICEINFO_MOTIONTIME)
        || data.IsIndexChange(DA_INDEX_OFFICEINFO_SWITCH)
        || data.IsIndexChange(DA_INDEX_OFFICEINFO_FEATUREEXPIRETIME)
        || data.IsIndexChange(DA_INDEX_OFFICEINFO_NAMEDISPLAY))
    {

        
        int pin_change = 0;
        if (data.IsIndexChange(DA_INDEX_OFFICEINFO_SWITCH))
        {
            int switch_after = data.GetIndexAsInt(DA_INDEX_OFFICEINFO_SWITCH);
            int switch_befor = data.GetBeforeIndexAsInt(DA_INDEX_OFFICEINFO_SWITCH);
            int change_bit = GetFirstDiffBit(switch_befor, switch_after);

            switch(change_bit)
            {
                case OfficeInfo::SwitchType::AllowPin:
                {
                    //是否允许用户使用PIN,刷user
                    pin_change = 1;
                    break;
                }
                case OfficeInfo::SwitchType::Landline:
                case OfficeInfo::SwitchType::OfflineNotify:
                case OfficeInfo::SwitchType::SIMNotify:
                {
                    break;
                }
                default:
                {
                    AK_LOG_INFO << "no need to update, change_bit= " << change_bit;            
                }
            }
        }
        
        if (data.IsIndexChange(DA_INDEX_OFFICEINFO_FEATUREEXPIRETIME) || pin_change )
        {
            //更新数据版本
            dbinterface::OfficePersonalAccount::UpdateVersionByProjectUUID(project_uuid);
            OfficeFileUpdateInfo update_info(project_uuid, OfficeUpdateType::OFFICE_PROJECT_CHANGE_FOR_USER); 
            context.AddUpdateConfigInfo(update_info);
        }
        else 
        {
            OfficeFileUpdateInfo update_info(project_uuid, OfficeUpdateType::OFFICE_PROJECT_CHANGE); 
            context.AddUpdateConfigInfo(update_info);
        }
        
    }
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaOfficeInfoHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);

}






