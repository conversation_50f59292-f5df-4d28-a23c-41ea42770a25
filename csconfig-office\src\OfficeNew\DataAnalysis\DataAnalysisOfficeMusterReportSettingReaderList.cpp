#include "DataAnalysisOfficeMusterReportSettingReaderList.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "DataAnalysisContorl.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "DataAnalysisdbHandle.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/office/OfficeDevices.h"
#include "OfficePduConfigMsg.h"


static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "OfficeMusterReportSettingReaderList";

enum DAOfficeMusterReportSettingReaderListIndex{
    DA_INDEX_OFFICE_MUSTER_REPORT_SETTING_READER_LIST_UUID,
    DA_INDEX_OFFICE_MUSTER_REPORT_SETTING_READER_LIST_OFFICEMUSTERREPORTSETTINGUUID,
    DA_INDEX_OFFICE_MUSTER_REPORT_SETTING_READER_LIST_DEVICESUUID,
    DA_INDEX_OFFICE_MUSTER_REPORT_SETTING_READER_LIST_READERLIST,
    DA_INDEX_OFFICE_MUSTER_REPORT_SETTING_READER_LIST_CREATETIME,
    DA_INDEX_OFFICE_MUSTER_REPORT_SETTING_READER_LIST_UPDATETIME,
};

static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/ 
   {DA_INDEX_OFFICE_MUSTER_REPORT_SETTING_READER_LIST_UUID, "UUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_MUSTER_REPORT_SETTING_READER_LIST_OFFICEMUSTERREPORTSETTINGUUID, "OfficeMusterReportSettingUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_MUSTER_REPORT_SETTING_READER_LIST_DEVICESUUID, "DevicesUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_MUSTER_REPORT_SETTING_READER_LIST_READERLIST, "ReaderList", ItemChangeHandle},
   {DA_INDEX_OFFICE_MUSTER_REPORT_SETTING_READER_LIST_CREATETIME, "CreateTime", ItemChangeHandle},
   {DA_INDEX_OFFICE_MUSTER_REPORT_SETTING_READER_LIST_UPDATETIME, "UpdateTime", ItemChangeHandle},
   {DA_INDEX_INSERT, "", UpdateHandle},
   {DA_INDEX_DELETE, "", UpdateHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

/*
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    // 具体使用
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    // 具体使用
    return 0;
}
*/
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string dev_uuid = data.GetIndex(DA_INDEX_OFFICE_MUSTER_REPORT_SETTING_READER_LIST_DEVICESUUID);
    OfficeDevPtr dev;
    if (0 != dbinterface::OfficeDevices::GetDevByUUID(dev_uuid, dev))
    {
        //找不到说明是删除设备动作触发的，删除设备的数据分析那边有处理了
        return 0;
    }
    std::string office_uuid = dev->project_uuid;

    OfficeFileUpdateInfo update_info(office_uuid, OfficeUpdateType::OFFICE_DEV_INFO_CHANGE);
    update_info.AddDevUUIDToList(dev_uuid);
    context.AddUpdateConfigInfo(update_info); 

    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaOfficeMusterReportSettingReaderListHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}

