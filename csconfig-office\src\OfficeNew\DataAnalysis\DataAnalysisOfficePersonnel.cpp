#include "OfficeNew/DataAnalysis/DataAnalysisOfficePersonnel.h"

#include "DataAnalysisTableParse.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include "dbinterface/ProjectUserManage.h"
#include "OfficePduConfigMsg.h"
#include "dbinterface/office/OfficePersonalAccount.h"

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "OfficePersonnel";
/*复制到DataAnalysisDef.h*/ 
enum DAOfficePersonnelIndex{
    DA_INDEX_OFFICE_PERSONNEL_ID,
    DA_INDEX_OFFICE_PERSONNEL_UUID,
    DA_INDEX_OFFICE_PERSONNEL_ACCOUNTUUID,
    DA_INDEX_OFFICE_PERSONNEL_OFFICECOMPANYUUID,
    DA_INDEX_OFFICE_PERSONNEL_PERSONALACCOUNTUUID,
    DA_INDEX_OFFICE_PERSONNEL_IDNO,
    DA_INDEX_OFFICE_PERSONNEL_ISDISPLAYINDIRECTORY,
    DA_INDEX_OFFICE_PERSONNEL_ISSMARTPLUSINTERCOM,
    DA_INDEX_OFFICE_PERSONNEL_CALLTYPE,
    DA_INDEX_OFFICE_PERSONNEL_ISSETVALIDTIME,
    DA_INDEX_OFFICE_PERSONNEL_VALIDSTARTTIME,
    DA_INDEX_OFFICE_PERSONNEL_VALIDENDTIME,
    DA_INDEX_OFFICE_PERSONNEL_CREATETIME,
    DA_INDEX_OFFICE_PERSONNEL_UPDATETIME,
};
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/ 
   {DA_INDEX_OFFICE_PERSONNEL_ID, "ID", ItemChangeHandle},
   {DA_INDEX_OFFICE_PERSONNEL_UUID, "UUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_PERSONNEL_ACCOUNTUUID, "AccountUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_PERSONNEL_OFFICECOMPANYUUID, "OfficeCompanyUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_PERSONNEL_PERSONALACCOUNTUUID, "PersonalAccountUUID", ItemChangeHandle},
   {DA_INDEX_OFFICE_PERSONNEL_IDNO, "IdNo", ItemChangeHandle},
   {DA_INDEX_OFFICE_PERSONNEL_ISDISPLAYINDIRECTORY, "IsDisplayInDirectory", ItemChangeHandle},
   {DA_INDEX_OFFICE_PERSONNEL_ISSMARTPLUSINTERCOM, "IsSmartPlusIntercom", ItemChangeHandle},
   {DA_INDEX_OFFICE_PERSONNEL_CALLTYPE, "CallType", ItemChangeHandle},
   {DA_INDEX_OFFICE_PERSONNEL_ISSETVALIDTIME, "IsSetValidTime", ItemChangeHandle},
   {DA_INDEX_OFFICE_PERSONNEL_VALIDSTARTTIME, "ValidStartTime", ItemChangeHandle},
   {DA_INDEX_OFFICE_PERSONNEL_VALIDENDTIME, "ValidEndTime", ItemChangeHandle},
   {DA_INDEX_INSERT, "", UpdateHandle},
   {DA_INDEX_DELETE, "", UpdateHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

/*
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}
*/
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string per_uuid = data.GetIndex(DA_INDEX_OFFICE_PERSONNEL_PERSONALACCOUNTUUID);
    dbinterface::OfficePersonalAccount::UpdateVersionByUUID(per_uuid);

    std::string project_uuid = data.GetIndex(DA_INDEX_OFFICE_PERSONNEL_ACCOUNTUUID);

    OfficeFileUpdateInfo update_info(project_uuid, OfficeUpdateType::OFFICE_USER_INFO_CHANGE);
    context.AddUpdateConfigInfo(update_info);
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaOfficePersonnelHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}

