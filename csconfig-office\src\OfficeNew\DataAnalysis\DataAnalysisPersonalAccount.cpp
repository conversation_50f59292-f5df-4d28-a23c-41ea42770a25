#include "OfficeNew/DataAnalysis/DataAnalysisPersonalAccount.h"
#include "DataAnalysisContorl.h"
#include "OfficePduConfigMsg.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include "dbinterface/ProjectUserManage.h"
#include <string.h>
#include <memory>
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "dbinterface/office/OfficePersonalAccount.h"

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

enum DAPersonalAccountIndex{
    DA_INDEX_PERSONAL_ACCOUNT_NAME,
    DA_INDEX_PERSONAL_ACCOUNT_ROLE,
    DA_INDEX_PERSONAL_ACCOUNT_ACCOUNT,
    DA_INDEX_PERSONAL_ACCOUNT_UNITID,
    DA_INDEX_PERSONAL_ACCOUNT_PHONE,
    DA_INDEX_PERSONAL_ACCOUNT_PHONE2,
    DA_INDEX_PERSONAL_ACCOUNT_PHONE3,
    DA_INDEX_PERSONAL_ACCOUNT_PHONECODE,
    DA_INDEX_PERSONAL_ACCOUNT_PHONESTATUS,
    DA_INDEX_PERSONAL_ACCOUNT_NFCCODE,
    DA_INDEX_PERSONAL_ACCOUNT_BLECODE,
    DA_INDEX_PERSONAL_ACCOUNT_PARENTID,
    DA_INDEX_PERSONAL_ACCOUNT_ROOMNAME,
    DA_INDEX_PERSONAL_ACCOUNT_TIMEZONE,
    DA_INDEX_PERSONAL_ACCOUNT_SPECIAL,
    DA_INDEX_PERSONAL_ACCOUNT_PARENTUUID,
    DA_INDEX_PERSONAL_ACCOUNT_UUID,
    DA_INDEX_PERSONAL_ACCOUNT_EXPIRETIME,
};


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "PersonalAccount";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
    {DA_INDEX_PERSONAL_ACCOUNT_NAME, "Name", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_ROLE, "Role", ItemChangeHandle},    
    {DA_INDEX_PERSONAL_ACCOUNT_ACCOUNT, "Account", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_UNITID, "UnitID", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_PHONE, "Phone", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_PHONE2, "Phone2", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_PHONE3, "Phone3", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_PHONECODE, "PhoneCode", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_PHONESTATUS, "PhoneStatus", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_NFCCODE, "NFCCode", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_BLECODE, "BLECode", ItemChangeHandle},    
    {DA_INDEX_PERSONAL_ACCOUNT_PARENTID, "ParentID", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_ROOMNAME, "RoomNumber", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_TIMEZONE, "TimeZone", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_SPECIAL, "Special", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_PARENTUUID, "ParentUUID", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_UUID, "UUID", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_EXPIRETIME, "ExpireTime", ItemChangeHandle},
    {DA_INDEX_INSERT, "", UpdateHandle},
    {DA_INDEX_DELETE, "", UpdateHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};



static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

/*
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{

}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
}
*/
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string uuid = data.GetIndex(DA_INDEX_PERSONAL_ACCOUNT_UUID);
    std::string office_uuid = data.GetIndex(DA_INDEX_PERSONAL_ACCOUNT_PARENTUUID);

    //更新数据版本
    dbinterface::OfficePersonalAccount::UpdateVersionByUUID(uuid);


    OfficeFileUpdateInfo update_info(office_uuid, OfficeUpdateType::OFFICE_USER_INFO_CHANGE);

    context.AddUpdateConfigInfo(update_info);

    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaPersonalAccountHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);    
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);
}




