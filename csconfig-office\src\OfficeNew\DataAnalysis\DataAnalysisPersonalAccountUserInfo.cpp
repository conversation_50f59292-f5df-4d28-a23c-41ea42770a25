#include "OfficeNew/DataAnalysis/DataAnalysisPersonalAccountUserInfo.h"
#include "DataAnalysisContorl.h"
#include "OfficePduConfigMsg.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include "dbinterface/ProjectUserManage.h"
#include <string.h>
#include <memory>
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/office/OfficePersonalAccount.h"



static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "PersonalAccountUserInfo";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
    {DA_INDEX_PERSONAL_ACCOUNT_USER_EMAIL, "Email", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_USER_MOBILENUMBER, "MobileNumber", ItemChangeHandle},    
    {DA_INDEX_PERSONAL_ACCOUNT_USER_UUID, "UUID", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_USER_MAINACCOUNT, "AppMainUserAccount", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};


static bool NeedUpdateContact(const std::string& before_email, const std::string& before_mobile_number, const std::string& email, const std::string& mobile_number)
{
    // Email && MobileNumber, 从无到有 || 从有到无 的情况下需要刷新node的联系人文件
    if ((before_email.empty() && before_mobile_number.empty() && (!email.empty() || !mobile_number.empty()))
    || ((!before_email.empty() || !before_mobile_number.empty()) && (email.empty() && mobile_number.empty())))
    {
        return true;
    }
    
    return false;
}

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //PersonalAccount处理
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //PersonalAccount处理
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    if (data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_USER_EMAIL) || data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_USER_MOBILENUMBER))
    {
        std::string email = data.GetIndex(DA_INDEX_PERSONAL_ACCOUNT_USER_EMAIL);
        std::string mobile_number = data.GetIndex(DA_INDEX_PERSONAL_ACCOUNT_USER_MOBILENUMBER);
        std::string before_email = data.GetBeforeIndex(DA_INDEX_PERSONAL_ACCOUNT_USER_EMAIL);
        std::string before_mobile_number = data.GetBeforeIndex(DA_INDEX_PERSONAL_ACCOUNT_USER_MOBILENUMBER);
        
        if (NeedUpdateContact(before_email, before_mobile_number, email, mobile_number))
        {
            std::string userinfo_uuid = data.GetIndex(DA_INDEX_PERSONAL_ACCOUNT_USER_UUID);

            //获取用户列表 newoffice只会有一个
            ResidentPerAccountList account_list;
            if (0 != dbinterface::ResidentPersonalAccount::GetAccountListByUserInfoUUID(userinfo_uuid, account_list))
            {
                AK_LOG_INFO << "GetAccountListByUserInfoUUID failed, userinfo_uuid:" << userinfo_uuid;
                return 0;
            }
            for (auto &account : account_list)
            {
                dbinterface::OfficePersonalAccount::UpdateVersionByUUID(account.uuid);
                std::string office_uuid =  account.parent_uuid;
                
                OfficeFileUpdateInfo update_info(office_uuid, OfficeUpdateType::OFFICE_USER_INFO_CHANGE);
                context.AddUpdateConfigInfo(update_info);
            }                       
        }
    }
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaPersonalAccountUserInfoHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);

}






