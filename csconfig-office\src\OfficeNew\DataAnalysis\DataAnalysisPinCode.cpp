#include "OfficeNew/DataAnalysis/DataAnalysisPinCode.h"

#include "DataAnalysisTableParse.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include "dbinterface/ProjectUserManage.h"
#include "OfficePduConfigMsg.h"
#include "dbinterface/new-office/OfficePinCode.h"
#include "dbinterface/new-office/OfficeDelivery.h"
#include "dbinterface/office/OfficePersonalAccount.h"

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "PinCode";
/*复制到DataAnalysisDef.h*/ 
enum DAPinCodeIndex{
    DA_INDEX_PIN_CODE_ID,
    DA_INDEX_PIN_CODE_UUID,
    DA_INDEX_PIN_CODE_ACCOUNTUUID,
    DA_INDEX_PIN_CODE_OFFICECOMPANYUUID,
    DA_INDEX_PIN_CODE_PERSONALACCOUNTUUID,
    DA_INDEX_PIN_CODE_OFFICEDELIVERYUUID,
    DA_INDEX_PIN_CODE_TYPE,
    DA_INDEX_PIN_CODE_CODE,
    DA_INDEX_PIN_CODE_CREATORTYPE,
    DA_INDEX_PIN_CODE_CREATORACCOUNTUUID,
    DA_INDEX_PIN_CODE_CREATORPERSONALACCOUNTUUID,
    DA_INDEX_PIN_CODE_RBACDATAGROUPUUID,
    DA_INDEX_PIN_CODE_VERSION,
    DA_INDEX_PIN_CODE_CREATETIME,
    DA_INDEX_PIN_CODE_UPDATETIME,
};
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/ 
   {DA_INDEX_PIN_CODE_ID, "ID", ItemChangeHandle},
   {DA_INDEX_PIN_CODE_UUID, "UUID", ItemChangeHandle},
   {DA_INDEX_PIN_CODE_ACCOUNTUUID, "AccountUUID", ItemChangeHandle},
   {DA_INDEX_PIN_CODE_OFFICECOMPANYUUID, "OfficeCompanyUUID", ItemChangeHandle},
   {DA_INDEX_PIN_CODE_PERSONALACCOUNTUUID, "PersonalAccountUUID", ItemChangeHandle},
   {DA_INDEX_PIN_CODE_OFFICEDELIVERYUUID, "OfficeDeliveryUUID", ItemChangeHandle},
   {DA_INDEX_PIN_CODE_TYPE, "Type", ItemChangeHandle},
   {DA_INDEX_PIN_CODE_CODE, "Code", ItemChangeHandle},
   {DA_INDEX_PIN_CODE_CREATORTYPE, "CreatorType", ItemChangeHandle},
   {DA_INDEX_PIN_CODE_CREATORACCOUNTUUID, "CreatorAccountUUID", ItemChangeHandle},
   {DA_INDEX_PIN_CODE_CREATORPERSONALACCOUNTUUID, "CreatorPersonalAccountUUID", ItemChangeHandle},
   {DA_INDEX_INSERT, "", UpdateHandle},
   {DA_INDEX_DELETE, "", UpdateHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

/*
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}
*/
//都处理为更新 因为最终的数据都能获取到
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string office_uuid = data.GetIndex(DA_INDEX_PIN_CODE_ACCOUNTUUID);
    PinCodeType type = (PinCodeType)data.GetIndexAsInt(DA_INDEX_PIN_CODE_TYPE);
    if(type == PinCodeType::Account || type == PinCodeType::Admin)
    {
        std::string account_uuid = data.GetIndex(DA_INDEX_PIN_CODE_PERSONALACCOUNTUUID);
        dbinterface::OfficePersonalAccount::UpdateVersionByUUID(account_uuid);
        OfficeFileUpdateInfo update_info(office_uuid, OfficeUpdateType::OFFICE_USER_ACCESS_CHANGE);
        context.AddUpdateConfigInfo(update_info);
    }
    else if (type == PinCodeType::Delivery)
    {
        std::string delivery_uuid = data.GetIndex(DA_INDEX_PIN_CODE_OFFICEDELIVERYUUID);
        dbinterface::OfficeDelivery::UpdateDeliveryVersion(delivery_uuid);

        OfficeFileUpdateInfo update_info(office_uuid, OfficeUpdateType::OFFICE_PUB_USER_INFO_CHANGE);
        context.AddUpdateConfigInfo(update_info);
    }
    

    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaPinCodeHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}

