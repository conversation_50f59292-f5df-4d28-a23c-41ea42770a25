#include "OfficeNew/DataAnalysis/DataAnalysisRfCard.h"

#include "DataAnalysisTableParse.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include "dbinterface/ProjectUserManage.h"
#include "OfficePduConfigMsg.h"
#include "dbinterface/new-office/OfficeRfCard.h"
#include "dbinterface/new-office/OfficeDelivery.h"
#include "dbinterface/office/OfficePersonalAccount.h"

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "RfCard";
/*复制到DataAnalysisDef.h*/ 
enum DARfCardIndex{
    DA_INDEX_RF_CARD_ID,
    DA_INDEX_RF_CARD_UUID,
    DA_INDEX_RF_CARD_ACCOUNTUUID,
    DA_INDEX_RF_CARD_OFFICECOMPANYUUID,
    DA_INDEX_RF_CARD_PERSONALACCOUNTUUID,
    DA_INDEX_RF_CARD_OFFICEDELIVERYUUID,
    DA_INDEX_RF_CARD_TYPE,
    DA_INDEX_RF_CARD_CODE,
    DA_INDEX_RF_CARD_RBACDATAGROUPUUID,
    DA_INDEX_RF_CARD_VERSION,
    DA_INDEX_RF_CARD_CREATETIME,
    DA_INDEX_RF_CARD_UPDATETIME,
};
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/ 
   {DA_INDEX_RF_CARD_ID, "ID", ItemChangeHandle},
   {DA_INDEX_RF_CARD_UUID, "UUID", ItemChangeHandle},
   {DA_INDEX_RF_CARD_ACCOUNTUUID, "AccountUUID", ItemChangeHandle},
   {DA_INDEX_RF_CARD_OFFICECOMPANYUUID, "OfficeCompanyUUID", ItemChangeHandle},
   {DA_INDEX_RF_CARD_PERSONALACCOUNTUUID, "PersonalAccountUUID", ItemChangeHandle},
   {DA_INDEX_RF_CARD_OFFICEDELIVERYUUID, "OfficeDeliveryUUID", ItemChangeHandle},
   {DA_INDEX_RF_CARD_TYPE, "Type", ItemChangeHandle},
   {DA_INDEX_RF_CARD_CODE, "Code", ItemChangeHandle},
   {DA_INDEX_INSERT, "", UpdateHandle},
   {DA_INDEX_DELETE, "", UpdateHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}
/*
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}
*/
//都处理为更新 因为最终的数据都能获取到
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string office_uuid = data.GetIndex(DA_INDEX_RF_CARD_ACCOUNTUUID);
    RfCardType type = (RfCardType)data.GetIndexAsInt(DA_INDEX_RF_CARD_TYPE);
    if(type == RfCardType::Account)
    {
        std::string account_uuid = data.GetIndex(DA_INDEX_RF_CARD_PERSONALACCOUNTUUID);
        dbinterface::OfficePersonalAccount::UpdateVersionByUUID(account_uuid);
        
        OfficeFileUpdateInfo update_info(office_uuid, OfficeUpdateType::OFFICE_USER_ACCESS_CHANGE);
        context.AddUpdateConfigInfo(update_info);
    }
    else if (type == RfCardType::Delivery)
    {
        std::string delivery_uuid = data.GetIndex(DA_INDEX_RF_CARD_OFFICEDELIVERYUUID);
        dbinterface::OfficeDelivery::UpdateDeliveryVersion(delivery_uuid);

        OfficeFileUpdateInfo update_info(office_uuid, OfficeUpdateType::OFFICE_PUB_USER_INFO_CHANGE);
        context.AddUpdateConfigInfo(update_info);
    }
    
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaRfCardHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}

