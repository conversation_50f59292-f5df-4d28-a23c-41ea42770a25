<?php

// 生成时替换建表语句
$tableDefine= "

CREATE TABLE `OfficeCompanyAccessFloor` (
    `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `UUID` char(36) NOT NULL,
    `OfficeCompanyUUID` char(36) NOT NULL DEFAULT '',
    `CommunityUnitUUID` char(36) NOT NULL COMMENT '楼栋UUID',
    `AccessFloors` varchar(512) NOT NULL DEFAULT '' COMMENT '分配的楼层，楼层与楼层之间用 ; 分隔',
    `CreateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `UpdateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`ID`),
    UNIQUE KEY `UUID` (`UUID`),
    KEY `OfficeCompanyUUID` (`OfficeCompanyUUID`)
  ) ENGINE=InnoDB AUTO_INCREMENT=10013 DEFAULT CHARSET=utf8 COMMENT='公司楼栋楼层关联表' ;
";




$pattern = '/^\s*(PRIMARY|UNIQUE|KEY)\s+.*$/m';
$tableDefine = preg_replace($pattern, '', $tableDefine);
function getTableName($tableDefine)
{
    preg_match("/CREATE TABLE `([^`]+)`/", $tableDefine, $matches);
    $tableName = $matches[1];
    return $tableName;
}

function getTableHeaderDefine($input)
{
    // 使用正则表达式将单词分隔
    $output = preg_replace_callback('/([A-Z][a-z]*)/', function($matches) {
        return '_' . strtoupper($matches[1]);
    }, $input);

    // 如果开头有下划线，则去除
    if (substr($output, 0, 1) === '_') {
        $output = substr($output, 1);
    }

    return "__CSADAPT_DATAANALYSIS_" . $output . "_H__";
}

function getDataAnalysisChangeHandle($tableName, $tableDefine)
{
    $dataChangeHandle = "    /*单个变化的检测, 主要用于缓存变化/数据清理*/ \n";

    $tableUpper = preg_replace_callback('/([A-Z][a-z]*)/', function($matches) {
        return '_' . strtoupper($matches[1]);
    }, $tableName);

    // 如果开头有下划线，则去除
    if (substr($tableUpper, 0, 1) === '_') {
        $tableUpper = substr($tableUpper, 1);
    }

    preg_match_all("/`(\w+)` ([^ ]+)/", $tableDefine, $matches, PREG_SET_ORDER);

    foreach ($matches as $row => $match) {
        if($row == 0) continue;//调过表名
        $fieldName = $match[1];
        $filedUpper = strtoupper($fieldName);
        $dataChangeHandle .= "   {DA_INDEX_{$tableUpper}_{$filedUpper}, \"{$fieldName}\", ItemChangeHandle},\n";
    }

	$dataChangeHandle .= "   {DA_INDEX_INSERT, \"\", InsertHandle},\n";
	$dataChangeHandle .= "   {DA_INDEX_DELETE, \"\", DeleteHandle},\n";
	$dataChangeHandle .= "   {DA_INDEX_UPDATE, \"\", UpdateHandle},\n";   


    return rtrim($dataChangeHandle, ",\n");
}

function getDataAnalysisEnumHandle($tableName, $tableDefine)
{
    $EnumHandle = " \n";
    
    $tableUpper = preg_replace_callback('/([A-Z][a-z]*)/', function($matches) {
        return '_' . strtoupper($matches[1]);
    }, $tableName);

    $EnumHandle .= "enum DA${tableName}Index{\n";
    
    // 如果开头有下划线，则去除
    if (substr($tableUpper, 0, 1) === '_') {
        $tableUpper = substr($tableUpper, 1);
    }

    preg_match_all("/`(\w+)` ([^ ]+)/", $tableDefine, $matches, PREG_SET_ORDER);

    foreach ($matches as $row => $match) {
        if($row == 0) continue;//调过表名
        $fieldName = $match[1];
        $filedUpper = strtoupper($fieldName);
        $EnumHandle .= "    DA_INDEX_{$tableUpper}_{$filedUpper},\n";
    }
    $EnumHandle .= "};\n";
    return rtrim($EnumHandle, ",\n");
}

// 表名
$tableName = getTableName($tableDefine);

// 头文件定义
$hppDefine = getTableHeaderDefine($tableName);

$headerDefine = "
#include <map>
#include <list>
#include <vector>
#include <string>
#include <memory>
#include \"AkLogging.h\"
#include \"AkcsWebMsgSt.h\"
#include \"AkcsCommonDef.h\"
";

// 构造hpp文件
$hppContent = "";
$hppContent .= "#ifndef $hppDefine\n";
$hppContent .= "#define $hppDefine\n";
$hppContent .= $headerDefine . "\n";
$hppContent .= "void RegDa" . $tableName . "Handler();" ."\n\n";
$hppContent .= "#endif" ."\n";

$fileName = "DataAnalysis" . $tableName;
file_put_contents("$fileName.h", $hppContent);


// 构造cpp文件
$cppHeaderDefine = "#include \"OfficeNew/DataAnalysis/DataAnalysis" . $tableName . ".h\"\n";
$cppHeaderDefine .= '
#include "DataAnalysisTableParse.h"
#include "DataAnalysisContorl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include "dbinterface/ProjectUserManage.h"
#include "OfficePduConfigMsg.h"';

$handleFunction = "
static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
";

$dataEnum = getDataAnalysisEnumHandle($tableName, $tableDefine);
$dataChangeHandle = getDataAnalysisChangeHandle($tableName, $tableDefine);


$itemChangeHandler = "
static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    // 使用举例
    std::string uid = data.GetIndex(DA_INDEX_PERSONAL_ACCOUNT_ACCOUNT);
    std::string office_uuid = data.GetIndex(DA_INDEX_PERSONAL_ACCOUNT_ACCOUNT);

    //更新数据版本
    dbinterface::ProjectUserManage::UpdateAccountDataVersionByUid(uid);


    OfficeFileUpdateInfo update_info(office_uuid, OfficeUpdateType::OFFICE_USER_INFO_CHANGE);
    update_info.SetPersonalUUID(uid);
    context.AddUpdateConfigInfo(update_info);
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}
";

$regCppFunction = "void RegDa" . $tableName . "Handler()\n";
$regCppFunction .= "{\n";
$regCppFunction .= "    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);\n";
$regCppFunction .= "    RegDaSort(local_detect_key, da_change_handle, len);\n";
$regCppFunction .= "    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);\n";
$regCppFunction .= "    RegDataAnalysisDBHandler(local_table_name, ptr);\n";
$regCppFunction .= "}\n";


$cppContent = "";
$cppContent .= $cppHeaderDefine . "\n";
$cppContent .= $handleFunction . "\n";
$cppContent .= "static  DataAnalysisColumnList local_detect_key;\n";
$cppContent .= "static const std::string local_table_name = \"" . $tableName . "\";" . "\n";
$cppContent .= $dataEnum . "\n";
$cppContent .= "static DataAnalysisChangeHandle da_change_handle[] = {\n";
$cppContent .= $dataChangeHandle . "\n};\n";
$cppContent .= $itemChangeHandler . "\n";
$cppContent .= $regCppFunction . "\n";

$cppContent .= "\n\n//拷贝到DataAnalysisContorl.cpp\n";  
$cppContent .= "// RegDa" . $tableName . "Handler();\n";
$cppContent .= "// #include \"OfficeNew/DataAnalysis/DataAnalysis" . $tableName . ".h\"\n";    

$fileName = "DataAnalysis" . $tableName;
file_put_contents("$fileName.cpp", $cppContent);
?>

