#!/bin/bash


File="./DataAnalysisGenerate.cpp"
echo '#include "DataAnalysisGenerate.h"' > $File
files=$(grep RegDa *.h | awk -F ":" '{print $1}')

for file in $files; do
    echo "#include \"OfficeNew/DataAnalysis/$file\"" >> $File
done


echo "void RegDataAanlysisDBAllHandlerGenerate()" >>$File
echo "{" >> $File

grep  RegDa *.h | grep -v "RegDataAanlysisDBAllHandlerGenerate"| awk -F ".h:" '{print $2}' | awk  '{print $2}' >> $File

echo "}" >> $File
