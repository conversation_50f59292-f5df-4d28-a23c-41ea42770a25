#include "stdafx.h"
#include <functional>
#include "KafkaConsumerAnalysisTopicHandle.h"
#include "ConfigDef.h"
#include "AkLogging.h"
#include "AkcsCommonDef.h"
#include "AkcsMsgDef.h"
#include "KafkaParseWebMsg.h"
#include "DataAnalysis.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "OfficePduConfigMsg.h"
#include "AkcsWebMsgSt.h"
#include "SnowFlakeGid.h"

extern CSCONFIG_CONF gstCSCONFIGConf;

const char *g_analysis_msg_type = "data_analysis";


HandleKafkaAnalysisTopicMsg::HandleKafkaAnalysisTopicMsg()
{

}

void HandleKafkaAnalysisTopicMsg::RegNewOfficeHandle(const std::string& msg_type, HandleWebNotifyFunc func)
{
    functions_.insert(std::map<std::string, HandleWebNotifyFunc>::value_type(msg_type, func));
}


void HandleKafkaAnalysisTopicMsg::StartKafkaConsumer()
{
    RegNewOfficeHandle("delete_project", HandleKafkaAnalysisTopicMsg::OnDeleteProject);
    RegNewOfficeHandle("import_project", HandleKafkaAnalysisTopicMsg::OnImportProject);
    RegNewOfficeHandle("account_modify", HandleKafkaAnalysisTopicMsg::OnAccountModify); //usermeta
    RegNewOfficeHandle("feature_plan_renew", HandleKafkaAnalysisTopicMsg::onFearturePlanRenew);



    kafak_.SetParma(gstCSCONFIGConf.kafka_broker_ip, gstCSCONFIGConf.notify_appbackend_analysis_topic,
        gstCSCONFIGConf.notify_appbackend_analysis_group, gstCSCONFIGConf.notify_appbackend_analysis_thread_num);
    kafak_.SetConsumerCb(std::bind(&HandleKafkaAnalysisTopicMsg::HandleKafkaMessage, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4) );
    kafak_.Start();
    
}


bool HandleKafkaAnalysisTopicMsg::HandleKafkaMessage(uint64_t partition, uint64_t offset, const std::string& key, const std::string& org_msg)
{
    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    ThreadLocalSingleton::GetInstance().SetTraceID(traceid);
    KafkaWebMsgParse msg(org_msg);
    if(!msg.ParseOk())
    {
        AK_LOG_WARN << "Parse msg error:" << "partition:" << partition << " offset:" << offset << " data;" << org_msg;
        return true;
    }
    
    //csconfig只处理数据分析的消息
    if (msg.msg_type_ == g_analysis_msg_type)
    {
        DataAnalysis data_analysis(org_msg);
        data_analysis.Analysis();
        return true;
    }


    auto it = functions_.find(msg.msg_type_);
    if (it == functions_.end())
    {
        AK_LOG_WARN << "Not found msg_type=" << msg.msg_type_ << ", trace_id=" << msg.trace_id_;
        return true;
    }

    it->second(org_msg, msg.msg_type_, msg.kv_);
    return true;
}


void HandleKafkaAnalysisTopicMsg::OnDeleteProject(const std::string& msg, const std::string& msg_type, KakfaMsgKV& kv)
{
    std::string project_uuid = kv["project_uuid"];
    if (project_uuid.size() == 0)
    {
        AK_LOG_INFO << "import office :" << project_uuid << " parma error";
        return;
    }
    
    std::string macs = kv["macs"];
    AK_LOG_INFO << "delete office :" << project_uuid;
    
    std::set<std::string> macs_set;
    SplitString(macs, ",", macs_set);
    
    for (const auto& mac : macs_set)
    {
        OfficeFileUpdateInfo update_info(project_uuid, OfficeUpdateType::OFFICE_DEV_DELETE);
        update_info.AddDevMac(mac);
        ProduceConfigUpdateMsg(update_info.GetUUID(), update_info.GetInfo());
    }

    return;
}

void HandleKafkaAnalysisTopicMsg::OnImportProject(const std::string& msg, const std::string& msg_type, KakfaMsgKV& kv)
{
    std::string project_uuid = kv["project_uuid"];
    if (project_uuid.size() == 0)
    {
        AK_LOG_INFO << "import office :" << project_uuid << " parma error";
        return;
    }
    
    std::string import_type = kv["import_type"];
    AK_LOG_INFO << "import office :" << project_uuid << " type:" << import_type;

    //更新数据版本
    dbinterface::OfficePersonalAccount::UpdateVersionByProjectUUID(project_uuid);    
    OfficeFileUpdateInfo update_info(project_uuid, OfficeUpdateType::OFFICE_IMPORT_PROJECT);
    ProduceConfigUpdateMsg(update_info.GetUUID(), update_info.GetInfo());
    return;
}


void HandleKafkaAnalysisTopicMsg::OnAccountModify(const std::string& msg, const std::string& msg_type, KakfaMsgKV& kv)
{
    std::string office_uuid = kv["office_uuid"];
    std::string account_uuid = kv["account_uuid"];
    
    //更新数据版本
    dbinterface::OfficePersonalAccount::UpdateVersionByUUID(account_uuid);
    
    OfficeFileUpdateInfo update_info(office_uuid, OfficeUpdateType::OFFICE_USER_INFO_CHANGE);
    ProduceConfigUpdateMsg(update_info.GetUUID(), update_info.GetInfo());

}

void HandleKafkaAnalysisTopicMsg::onFearturePlanRenew(const std::string& msg, const std::string& msg_type, KakfaMsgKV& kv)
{
    std::string office_uuid = kv["project_uuid"];
    
    //更新数据版本
    dbinterface::OfficePersonalAccount::UpdateVersionByProjectUUID(office_uuid);
    
    OfficeFileUpdateInfo update_info(office_uuid, OfficeUpdateType::OFFICE_IMPORT_PROJECT);
    ProduceConfigUpdateMsg(update_info.GetUUID(), update_info.GetInfo());

}


