#ifndef KAFKA_CONSUMER_HANDLE_ANALYSIS_TOPIC_H_
#define KAFKA_CONSUMER_HANDLE_ANALYSIS_TOPIC_H_

#include <vector>
#include <map>
#include <set>
#include <unordered_set>
#include <memory>
#include <functional>
#include "AkcsKafkaConsumer.h"
#include "Singleton.h"
#include "KafkaParseWebMsg.h"

typedef void (*HandleWebNotifyFunc)(const std::string& org_msg, const std::string& msg_type_, KakfaMsgKV& kv);

class HandleKafkaAnalysisTopicMsg
{
public:
    HandleKafkaAnalysisTopicMsg();
    // 实现单例
    friend class AKCS::Singleton<HandleKafkaAnalysisTopicMsg>;

    void StartKafkaConsumer();
    bool HandleKafkaMessage(uint64_t partition, uint64_t offset, const std::string& key, const std::string& msg);
    void RegNewOfficeHandle(const std::string& msg_type, HandleWebNotifyFunc func);



    static void OnDeleteProject(const std::string& msg, const std::string& msg_type, KakfaMsgKV& kv);
    static void OnAccountModify(const std::string& msg, const std::string& msg_type, KakfaMsgKV& kv);
    static void OnImportProject(const std::string& msg, const std::string& msg_type, KakfaMsgKV& kv);
    static void onFearturePlanRenew(const std::string& msg, const std::string& msg_type, KakfaMsgKV& kv);

    
private:
    AkcsKafkaConsumer kafak_;
    std::map<std::string, HandleWebNotifyFunc> functions_;
};


#endif //__OFFICE_MEESSAGE_HANDLE_H__
