#include "stdafx.h"
#include <functional>
#include "KafkaConsumerWriteConfigTopicHandle.h"
#include "ConfigDef.h"
#include "AkLogging.h"
#include "AkcsCommonDef.h"
#include "AkcsMsgDef.h"
#include "AK.AdaptOffice.pb.h"
#include "AkcsWebMsgSt.h"
#include "IPCControl.h"
#include "ShadowMng.h"
#include "SnowFlakeGid.h"
#include "dbinterface/office/OfficeDevices.h"

extern CSCONFIG_CONF gstCSCONFIGConf;

using KafkaMessageMap = std::multimap<std::string/*officeUUID*/, const cppkafka::Message &>;

HandleKafkaWriteConfigTopicMsg::HandleKafkaWriteConfigTopicMsg()
{

}

void HandleKafkaWriteConfigTopicMsg::StartKafkaConsumer()
{
    kafak_.SetParma(gstCSCONFIGConf.kafka_broker_ip, gstCSCONFIGConf.write_config_topic,
        gstCSCONFIGConf.write_config_group, gstCSCONFIGConf.write_config_thread_num);
    kafak_.SetBatchConsumerCb(
         std::bind(&HandleKafkaWriteConfigTopicMsg::BatchKafkaMessage, this, 
                   std::placeholders::_1, std::placeholders::_2)
    );
    kafak_.StartConsumerBatch();
    
}

bool HandleKafkaWriteConfigTopicMsg::BatchKafkaMessage(const std::vector<cppkafka::Message> &messagelist, uint64_t unread)
{
    AK_LOG_INFO << "message size:" << messagelist.size();
    if (unread > gstCSCONFIGConf.write_config_heap_up_num)
    {
        kafak_.setBatchReadeNumber(gstCSCONFIGConf.write_config_batch_read_num);
        AK_LOG_INFO << "Heap up" << unread << " Start Batch consumer! batch number:" << gstCSCONFIGConf.write_config_batch_read_num;
    }
    else
    {
        AK_LOG_INFO << "normal consumer! read numb 1.";
        kafak_.setBatchReadeNumber(1);
    }

    KafkaMessageMap msg_map;
    std::set<std::string> filter;
    for (const auto &msg : messagelist)
    {

        AK::AdaptOffice::OfficeUpdateBaseMessage base;
        if(base.ParseFromString(msg.get_payload()) == false)
        {
            AK_LOG_WARN << "parse pb msg failed. msg:" << msg.get_payload();
            continue;
        }
        std::string uuid = base.msg_uuid();
        if (filter.count(uuid) == 0)
        {
            filter.insert(base.msg_uuid());
            msg_map.insert({msg.get_key(), msg});
            //AK_LOG_INFO << "insert msg uuid:" << uuid;
        }
        else
        {
            //AK_LOG_INFO << "filter msg uuid:" << uuid;
        }
    }

    uint32_t max_handle_time = 0;
    for (auto it = msg_map.begin(); it != msg_map.end(); )
    {
        std::string office_uuid = it->first;
        NewOfficeConfigHandle handle(office_uuid);
        
        auto range = msg_map.equal_range(office_uuid);
        for (auto inner_it = range.first; inner_it != range.second; ++inner_it)
        {
            //msg.get_partition(), msg.get_offset(), msg.get_key(),
            uint32_t handle_time = 0;
            HandleKafkaMessage(handle, inner_it->second.get_payload(), handle_time);
            if (handle_time > max_handle_time)
            {
                max_handle_time = handle_time;
            }
        }
        it = range.second;
    }    
    if (max_handle_time > 120) //120s
    {
        kafak_.setBatchReadeNumber(gstCSCONFIGConf.write_config_batch_read_num);
        AK_LOG_INFO << "Handle time > 120s. Heap up" << unread << " Start Batch consumer! batch number:" << gstCSCONFIGConf.write_config_batch_read_num;
    }

    return true;
}


bool HandleKafkaWriteConfigTopicMsg::HandleKafkaMessage(NewOfficeConfigHandle& handle, const std::string& msg, uint32_t &handle_time)
{
    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    ThreadLocalSingleton::GetInstance().SetTraceID(traceid);
    
    std::time_t tstart = std::time(0);

    AK::AdaptOffice::OfficeUpdateBaseMessage base;
    if(base.ParseFromString(msg) == false)
    {
        AK_LOG_WARN << "parse pb msg failed. msg:" << msg;
        return true;
    }
    AK_LOG_INFO << "kafka message. office uuid: " << base.office_uuid() << " change_type: " << base.change_type() << " trace_id: " << base.trace_id(); 

    AK::AdaptOffice::OfficeUpdateFileConfig update_info = base.file_update();
    if (base.office_uuid().size() == 0)
    {
        AK_LOG_WARN << "parse pb msg failed. office_uuid is null. It is possible that the pile up has already been deleted. msg:" << msg;
        return true;
    }

    if (base.office_uuid() != handle.office_uuid_)
    {
        //这里是不可能出现的，但如果出现需要进行拦截，因为实际的项目uuid和初始化的handle的uuid不匹配
        AK_LOG_FATAL << "base.office_uuid() != handle.office_uuid_. " << msg;
    }

    //判断是否被过滤了
    if(CheckStrInFilter(gstCSCONFIGConf.project_uuid_filter, base.office_uuid()))
    {
        AK_LOG_INFO << "FileUpdate return " <<  base.office_uuid();    
        return true;
    }


    OfficeUpdateType type = (OfficeUpdateType)base.change_type();
    switch (type)
    {
        case OfficeUpdateType::OFFICE_ACCESS_GROUP_CHANGE:// 更新权限组信息 更新所有设备的联系人和user、Schedule
        {
            handle.UpdateDevUserMeta();
            handle.UpdateDevContact();
            handle.UpdateDevSchedule();
            break;
        }
        case OfficeUpdateType::OFFICE_PUB_USER_INFO_CHANGE:
        {
            handle.UpdateDevUserMeta();
            handle.UpdateDevSchedule();
            break;
        }
        case OfficeUpdateType::OFFICE_GROUP_CHANGE: 
        {
            handle.UpdateDevContact();
            handle.UpdateDevUserMeta();
            handle.UpdateDevSchedule();            
            break;
        }
        case OfficeUpdateType::OFFICE_DEV_CONFIG_CHANGE_WITH_MAC: // 单个设备的config. 携带设备
        {
            for (int i = 0; i < update_info.dev_uuid_list_size(); ++i)
            {
                std::string mac = update_info.dev_uuid_list(i);
                AK_LOG_INFO << "only chagne mac config :" << update_info.dev_uuid_list(i);
                handle.UpdateDevConfig(mac);
            }
            break;
        }
        case OfficeUpdateType::OFFICE_USER_INFO_CHANGE: //contact、usermate、shecdule
        {
            handle.UpdateDevUserMeta();
            handle.UpdateDevContact();
            handle.UpdateDevSchedule();
            break;
        }   
        case OfficeUpdateType::OFFICE_USER_ACCESS_CHANGE: //usermate
        {
            handle.UpdateDevUserMeta();
            break;
        }
        case OfficeUpdateType::OFFICE_DEV_INFO_CHANGE: // 单个设备的所有配置 + 所有设备的contact、config
        {
            for (int i = 0; i < update_info.dev_uuid_list_size(); ++i)
            {
                AK_LOG_INFO << "mac info change :" << update_info.dev_uuid_list(i);
                std::string mac = update_info.dev_uuid_list(i);
                handle.UpdateDevSchedule(mac); 
                handle.UpdateDevUserMeta(mac);
            }

            //白名单在autop中，因此所有设备的DevConfig都要更新
            handle.UpdateDevConfig();
            handle.UpdateDevContact();
            break;
        }   
        case OfficeUpdateType::OFFICE_HOLIDAY_CHANGE: //all shecdule
        {
            handle.UpdateDevSchedule();
            break;
        }

        case OfficeUpdateType::OFFICE_PROJECT_CHANGE: //项目信息，motion项目名称等 config/contact
        {
            handle.UpdateDevContact();
            handle.UpdateDevConfig();
            break;
        }
        case OfficeUpdateType::OFFICE_PROJECT_CHANGE_FOR_USER: //项目信息的高级功能过期+pin改变 需要更新所有user
        {
            handle.UpdateDevUserMeta();
            handle.UpdateDevSchedule();          
            break;
        }
        case OfficeUpdateType::OFFICE_DEV_ADD:
        {
            AK_LOG_INFO << "add mac notify csmain :" << update_info.mac();
            GetIPCControlInstance()->SendPersonalReportStatus(update_info.mac());
            break;
        }
        case OfficeUpdateType::OFFICE_DEV_DELETE:
        {
            AK_LOG_INFO << "delete mac delete shadow file and notify :" << update_info.mac();
            AKCS::Singleton<CShadowMng>::instance().DeleteDevShadow(update_info.mac());
            GetIPCControlInstance()->SendPerDevLogOutSip(update_info.mac());
            break;
        }
        case OfficeUpdateType::OFFICE_IMPORT_PROJECT:
        {
            AK_LOG_INFO << "import project:" << base.office_uuid();
            handle.UpdateDevUserMeta();
            handle.UpdateDevSchedule(); 
            handle.UpdateDevContact();
            handle.UpdateDevConfig();

            OfficeDevMap all_dev_list_;
            dbinterface::OfficeDevices::GetAllOfficeDevList(base.office_uuid(), all_dev_list_);
            for(const auto &dev : all_dev_list_)
            {
                AK_LOG_INFO << "notify csmain :" << dev.second->mac;
                GetIPCControlInstance()->SendPersonalReportStatus(dev.second->mac);                
            }   
            
            break;
        }   
        case OfficeUpdateType::OFFICE_COMPANY_INFO_CHANGE: //contact、usermate、shecdule
        {
            handle.UpdateDevUserMeta();
            handle.UpdateDevContact();
            handle.UpdateDevSchedule();
            break;
        }  
        default:
        {
            AK_LOG_WARN << "unkonw msg id :" << base.change_type();
        }
               
    }

    std::time_t tend = std::time(0);
    handle_time =  tend - tstart;
    AK_LOG_INFO << "project uuid [" << base.office_uuid() << "] update file time: " << handle_time << "s traceid=" << traceid;    

    return true;
}


