#ifndef __OFFICE_WRITE_CONFIG_HANDLE_H__
#define __OFFICE_WRITE_CONFIG_HANDLE_H__

#include <vector>
#include <map>
#include <set>
#include <unordered_set>
#include <memory>
#include <functional>
#include "AkcsKafkaConsumer.h"
#include "Singleton.h"
#include "InnerEnum.h"

#include "OfficeNew/ConfigFile/OfficeNewConfigHandle.h"


class HandleKafkaWriteConfigTopicMsg
{
public:
    HandleKafkaWriteConfigTopicMsg();

    // 实现单例
    friend class AKCS::Singleton<HandleKafkaWriteConfigTopicMsg>;

    void StartKafkaConsumer();
    bool BatchKafkaMessage(const std::vector<cppkafka::Message> &message, uint64_t unread);
    bool HandleKafkaMessage(NewOfficeConfigHandle& handle, const std::string& msg, uint32_t &handle_time);
    
private:
    <PERSON><PERSON><PERSON><PERSON><PERSON>kaConsumer kafak_;
};


#endif //__OFFICE_MEESSAGE_HANDLE_H__
