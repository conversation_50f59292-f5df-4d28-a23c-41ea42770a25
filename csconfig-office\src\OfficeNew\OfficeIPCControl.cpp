#include <stdio.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <errno.h>
#include <assert.h>
#include <string.h>
#include <ctype.h>
#include <string>
#include "util_cstring.h"
#include "ConfigDef.h"
#include "AKCSMsg.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "OfficeIPCControl.h"
#include "MQProduce.h"
#include "AK.Server.pb.h"
#include "AK.ServerOffice.pb.h"
#include "AkcsMsgDef.h"
#include "util.h"
#include "AkcsCommonDef.h"

#define IPC_RECONNECT_INTERVAL  1000
#define IPC_SELECT_TIMEOUT      2000

extern CSCONFIG_CONF gstCSCONFIGConf;
extern RouteMQProduce* g_nsq_producer;
static OfficeIPCControl* instance = nullptr;

OfficeIPCControl* GetOfficeIPCControlInstance()
{
    return OfficeIPCControl::GetInstance();
}

OfficeIPCControl::OfficeIPCControl()
{

}
OfficeIPCControl::~OfficeIPCControl()
{

}
OfficeIPCControl* OfficeIPCControl::instance = NULL;

OfficeIPCControl* OfficeIPCControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new OfficeIPCControl();
    }

    return instance;
}



//csadapt通知csmain,更新联系人
int OfficeIPCControl::SendConfigFileChange(CSP2A_CONFIG_FILE_CHANGE* filechange)
{
    AK::Server::GroupAdaptConfFileChangeMsg msg;
    msg.set_mac(filechange->mac);
    msg.set_node(filechange->node);
    msg.set_type(filechange->type);
    msg.set_mng_id(filechange->mng_id);
    msg.set_unit_id(filechange->unit_id);
    msg.set_notify_type(filechange->nNotifyType);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetProjectType(project::OFFICE);
    pdu.SetCommandId(MSG_C2S_OFFICE_NOTIFY_CONFIG_FILE_CHANGE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSCONFIGConf.nsq_route_topic);
    return 0;
}





