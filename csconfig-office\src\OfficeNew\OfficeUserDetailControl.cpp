#include "stdafx.h"
#include <functional>
#include "OfficeUserDetailControl.h"
#include "ConfigDef.h"
#include "AkLogging.h"
#include "AkcsCommonDef.h"
#include "AkcsMsgDef.h"
#include "KafkaParseWebMsg.h"
#include "DataAnalysis.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "OfficePduConfigMsg.h"
#include "AkcsWebMsgSt.h"
#include "AK.Server.pb.h"
#include "util.h"
#include "dbinterface/office/OfficeDevices.h"
#include "IPCControl.h"
#include "OfficeNew/ConfigFile/OfficeNewConfigHandle.h"
#include "ShadowUserDetailMng.h"


void OfficeUserDetailControl::AddUserDetailMsg(const std::string &office_uuid, UserDetailPtr &msg)
{
    int queue_index = queue_consistent_hash_.GetQueueNumBy<PERSON>ey(office_uuid);
    std::lock_guard<std::mutex> lock(queues_[queue_index]->mutex_);
    queues_[queue_index]->data_queue_.push_back(msg);
    queues_[queue_index]->cv_.notify_one();
}

void OfficeUserDetailControl::HandleUserDetail(size_t queue_index)
{
    while (!stop_)
    {
        std::deque<UserDetailPtr> tmp_deque;
        {
            std::unique_lock<std::mutex> lock(queues_[queue_index]->mutex_);
            while (queues_[queue_index]->data_queue_.empty())
            {
                queues_[queue_index]->cv_.wait(lock);
            }
            queues_[queue_index]->data_queue_.swap(tmp_deque);
        }
        ProcessUserDetail(tmp_deque);
    }
}

void OfficeUserDetailControl::Start(int thread_num)
{
    queue_consistent_hash_.InitQueueNumList(thread_num);

    thread_num_ = thread_num;
    queues_.resize(thread_num);
    for (auto& queue : queues_)
    {
        queue.reset(new MessageQueue());
    }        
    for (size_t i = 0; i < queues_.size(); ++i)
    {
        threads_.emplace_back(&OfficeUserDetailControl::HandleUserDetail, this, i);
    }
}

void OfficeUserDetailControl::Stop()
{
    stop_ = true;
    for (auto& queue : queues_)
    {
        queue->cv_.notify_all();
    }
    for (auto& thread : threads_)
    {
        if (thread.joinable())
        {
            thread.join();
        }
    }
}


void OfficeUserDetailControl::ProcessUserDetail(std::deque<UserDetailPtr> &msg_queue)
{
    while(msg_queue.size() > 0)
    {
        UserDetailPtr msg = msg_queue.front();
        msg_queue.pop_front();

        OfficePerIDSet account_list;
        SplitString(msg->uuids(), ";", account_list);
        AK_LOG_INFO<< "Mac:" << msg->mac() << " request user info, userinfo:" << msg->uuids() << " traceid:" << msg->msg_traceid();
        
        OfficeUserDetailReq req;
        req.trarceid = msg->msg_traceid();
        OfficeDevPtr dev;
        if (dbinterface::OfficeDevices::GetMacDev(msg->mac(), dev) == 0)
        {
            NewOfficeConfigHandle handle(dev->project_uuid);
            handle.CreateUserInfo(dev, account_list, req);
            if(req.file_md5.size() > 0)
            {
                auto& pool = ConfigUserDetailFdfsUploaderPool::GetInstance();
                {
                    ConfigUserDetailFdfsUploaderPool::UploaderHandle handle(pool);
                    std::string path_after;
                    if (handle.UploadFile(req.write_file_path, path_after) == 0)
                    {
                        req.download_file_path = path_after;
                    }
                }
            
                GetIPCControlInstance()->NotifyDevFileChange(msg->mac(), DEV_FILE_CHANGE_NOTIFY_USER_INFO, req.trarceid, req.download_file_path, req.file_md5); 
                AK_LOG_INFO<< "Mac:" << msg->mac() << " request user info, " << msg->msg_traceid() << " file path " << req.download_file_path;
            }
        }
    }
}


