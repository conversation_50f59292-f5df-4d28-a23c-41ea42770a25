#ifndef __OFFICE_USER_DETAIL_MSG_CONTROL_H__
#define __OFFICE_USER_DETAIL_MSG_CONTROL_H__

#include <vector>
#include <map>
#include <set>
#include <unordered_set>
#include <memory>
#include <functional>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>
#include "AK.Server.pb.h"
#include "Singleton.h"
#include "consistent-hash/QueueConsistentHash.h"

using UserDetailPtr = std::shared_ptr<AK::Server::P2PMainRequestWriteUserinfo>;

struct MessageQueue {
    std::deque<UserDetailPtr> data_queue_;
    std::mutex mutex_;
    std::condition_variable cv_;
};

class OfficeUserDetailControl {
public:

    // 实现单例
    friend class AKCS::Singleton<OfficeUserDetailControl>;
    
    OfficeUserDetailControl() : stop_(false) {}

    void AddUserDetailMsg(const std::string &office_uuid, UserDetailPtr &msg);
    void HandleUserDetail(size_t queue_index);
    void Start(int thread_num);
    void Stop();

private:
    void ProcessUserDetail(std::deque<UserDetailPtr> &msg_queue);
    std::vector<std::unique_ptr<MessageQueue>> queues_;
    std::vector<std::thread> threads_;
    bool stop_;
    int thread_num_;
    akcs_consistent_hash::QueueConsistentHash queue_consistent_hash_;
};

#endif //__OFFICE_USER_DETAIL_MSG_HANDLE_H__
