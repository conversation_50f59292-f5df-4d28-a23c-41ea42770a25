#config pack_rom makefile

MOD_BIN_NAME = AdaptUnitTest
PWD := $(shell pwd)
export MOD_DIR := $(PWD)/../../../
export CSBASE_DIR := $(PWD)/../../../../csbase

PJ_DIR ?= $(MOD_DIR)

include $(PJ_DIR)/src/unit_test/build/PROJECT.mak
include $(PJ_DIR)/build/TOOL.mak
include $(PJ_DIR)/src/unit_test/build/MOD.mak

export CPPFLAGS := -I$(PJ_INC_DIR)/mysql
export CPPFLAGS += -I$(MOD_SRC_DIR)/Common/Utility
export CPPFLAGS += -I$(MOD_SRC_DIR)/Common/Basic
export CPPFLAGS += -I$(MOD_SRC_DIR)/Common/Cstring
export CPPFLAGS += -I$(MOD_SRC_DIR)/Common/Lock
export CPPFLAGS += -I$(MOD_SRC_DIR)/Common/Tinyxml
#export CPPFLAGS += -I$(MOD_SRC_DIR)/Common/Socket
export CPPFLAGS += -I$(MOD_SRC_DIR)/Control
export CPPFLAGS += -I$(MOD_SRC_DIR)/Model
export CPPFLAGS += -I$(MOD_SRC_DIR)/Model/DataAnalysis
export CPPFLAGS += -I$(MOD_SRC_DIR)
export CPPFLAGS += -I$(MOD_SRC_DIR)/Office
export CPPFLAGS += -I$(MOD_EXTERN_INC_DIR)
export CPPFLAGS += -I$(MOD_SRC_INC_DIR)
export CPPFLAGS += -I$(CSBASE_DIR)
export CPPFLAGS += -I$(CSBASE_DIR)/mysql
export CPPFLAGS += -I$(CSBASE_DIR)/evpp
export CPPFLAGS += -I$(CSBASE_DIR)/etcd
export CPPFLAGS += -I$(CSBASE_DIR)/Rldb
export CPPFLAGS += -I$(CSBASE_DIR)/protobuf
export CPPFLAGS += -I$(CSBASE_DIR)/beanstalk-client
export CPPFLAGS += -I$(CSBASE_DIR)/encrypt
export CPPFLAGS += -I$(CSBASE_DIR)/redis
export CPPFLAGS += -I$(CSBASE_DIR)/jsoncpp0.5/include
export CPPFLAGS += -I$(CSBASE_DIR)/fdfs_client/fdfsclient
export CPPFLAGS += -I$(CSBASE_DIR)/fdfs_client/libfdfscomm
export CPPFLAGS += -I$(CSBASE_DIR)/grpc
export CPPFLAGS += -I$(CSBASE_DIR)/grpc/gens

export CPPFLAGS += -I$(MOD_SRC_DIR)/unit_test/model/inih-master
export CPPFLAGS += -I$(MOD_SRC_DIR)/unit_test/model/common

export LDFLAGS := -L$(MOD_LIB_DIR) -L$(CSBASE_DIR) -L$(CSBASE_DIR)/thirdlib -L$(CSBASE_DIR)/redis/hiredis  -L$(CSBASE_DIR)/evpp/lib -lpthread -lmysqlclient -liconv -levent -lglog  -lssl -lcrypto -levnsq -levpp  -lboost_system -lcpprest -letcd-cpp-api  -lprotobuf -Bstatic -lhiredis -lcsbase   -lfdfsclient -lfastcommon -lgpr -lgrpc -lgrpc++

export CXX = g++
CXX += -std=c++11 -DCARES_STATICLIB -DGFLAGS_IS_A_DLL=0 -DPB_FIELD_16BIT -D_TURN_OFF_PLATFORM_STRING -std=c++11 -fPIE

export STRIP = strip
RM = rm -rf
CP = cp -rf
OFLAG =

MOD_COMM_SRC_DIRS := $(MOD_SRC_DIR)/Common/Basic  $(MOD_SRC_DIR)/Common/Cstring $(MOD_SRC_DIR)/Common/Lock $(CSBASE_DIR)/Rldb \
	 $(MOD_SRC_DIR)/Common/Tinyxml $(MOD_SRC_DIR)/Common/Utility  $(MOD_SRC_DIR)/Control $(MOD_SRC_DIR)/Model $(MOD_SRC_DIR)/Model/DataAnalysis \
    $(CSBASE_DIR)/redis $(CSBASE_DIR)/jsoncpp0.5/src/json $(CSBASE_DIR)/etcd  $(CSBASE_DIR)/protobuf $(CSBASE_DIR)/beanstalk-client $(CSBASE_DIR)/encrypt \
    $(CSBASE_DIR)/dbinterface $(CSBASE_DIR)/dbinterface/office $(MOD_SRC_DIR)/Model/Maintenance $(MOD_SRC_DIR)/Office 

MOD_UNIT_SRC_DIRS := $(MOD_COMM_SRC_DIRS) $(MOD_SRC_DIR)/unit_test $(MOD_SRC_DIR)/unit_test/model/unit_test $(MOD_SRC_DIR)/unit_test/model/devfile_test \
    $(MOD_SRC_DIR)/unit_test/model/common 
	
    
.PHONY: all $(MOD_UNIT_SRC_DIRS) clean

all: check $(MOD_UNIT_SRC_DIRS) $(MOD_BIN_NAME)

check:
ifeq ($(PJ_DIR),)
	@echo "Build failed: PJ_DIR is NULL"
	@exit 1
endif


AdaptUnitTestObjs := $(MOD_OBJ_DIR)*.o
$(MOD_BIN_NAME): $(MOD_UNIT_SRC_DIRS)
	@echo "\n"
	$(CXX) -g -Wall -gstabs+ -o $(MOD_BIN_DIR)$(MOD_BIN_NAME) $(AdaptUnitTestObjs)  $(LDFLAGS)
	cp ../bin/AdaptUnitTest ../release/bin/

$(MOD_UNIT_SRC_DIRS):
	@echo "\n"
	@$(MAKE) --directory=$@

clean: check
	for d in $(MOD_UNIT_SRC_DIRS); \
	do \
		cd $${d}; \
		$(MAKE) clean; \
		cd ..;	\
	done \

	-rm $(MOD_BIN_DIR)$(MOD_BIN_NAME)

