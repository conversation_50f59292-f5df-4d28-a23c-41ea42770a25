﻿#include <string>
#include <map>
#include "unistd.h"
#include <catch2/catch.hpp>
#include "AkLogging.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "UnitTestUtil.h"


int exec_sql(const std::string &sql)
{
    if (sql.size() == 0)
    {
        return 0;
    }
    RldbPtr conn = GetDBConnPollInstance()->GetConnection();
    CRldb* rldb_conn = conn.get();
    if (NULL == rldb_conn)
    {
        AK_LOG_WARN << "Get DB conn failed.";
        return -1;
    }
    int ret = rldb_conn->Execute(sql) >= 0 ? 0 : -1;      
    ReleaseDBConn(conn);
    return ret;
}



