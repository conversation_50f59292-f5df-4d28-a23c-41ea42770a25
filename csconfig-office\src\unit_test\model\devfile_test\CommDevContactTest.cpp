﻿#include <string>
#include <map>
#include "unistd.h"
#include <catch2/catch.hpp>
#include "AkLogging.h"
#include "ConfigDef.h"
#include "AKCSMsg.h"
#include "ConnectionPool.h"
#include "ConfigFileReader.h"
#include "CommConfigHandle.h"
#include "util_cstring.h"
#include "FileUpdateControl.h"
#include "json/json.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "INIReader.h"
#include "UnitTestUtil.h"


static const std::string CaseFile = "../conf/CommDevContactTest.ini";

TEST_CASE("CommunityContact", "[community][contact]")
{
    INIReader reader(CaseFile);
    if (reader.ParseError() < 0) {
        AK_LOG_WARN << "Can't load ini file";
        REQUIRE(1 == 0);
        return;
    }

    std::string init_db = reader.Get("common", "init_sql_cmd", "UNKNOWN");
    std::string detect_bin_common = reader.Get("common", "detect_bin", "UNKNOWN");
    std::vector<std::string> cases_list = reader.Sections();

    SECTION("CommonContact")
    {
        //初始化全局db
        if (init_db.size() > 0 )
        {
            system(init_db.c_str());
            REQUIRE(errno == 0);
        }
        for (auto &it : cases_list)
        {
            std::string section = it;
            std::string case_name = section;
            if (case_name == "common")
            {
                continue;
            }
            AK_LOG_INFO <<  "Case: " << case_name << " init";
            std::string sql = reader.Get(section, "sql_cmd", "");
            std::string detect_bin = reader.Get(section, "detect_bin", "");
            if (detect_bin.size() == 0)
            {
                detect_bin = detect_bin_common;
            }

            Json::Value param,param2;
            Json::FastWriter w;
            param["check_file_path"] =reader.Get(section, "check_file_path", "");
            param["check_by_json"] = reader.Get(section, "check_by_json", "");
            param["check_group"] = reader.Get(section, "check_group", "");
            param["check_uid"] = reader.Get(section, "check_uid", "");
            param["check_case"] = case_name;
            std::string argvs = w.write(param); 
            
            std::vector<std::string> sqls;
            SplitString(sql, "\n", sqls);
            for (auto &sql : sqls)
            {
                int ret = exec_sql(sql);
                REQUIRE(ret == 0);                
            }

            //执行
            //B1 101房间
            uint32_t mng_id = 5;
            uint32_t unit_id = 1;
            std::string node = "6300100003";
    		std::vector<std::string> macs = {"CE0000000009"};            
            AK_LOG_INFO <<  "Case: " << case_name << " handling";
            if (case_name == "UpdateUnitAllNodeDevContactList")
            {
                CommConfigHandle handle(mng_id, unit_id, node, macs);
                handle.UpdateUnitAllNodeDevContactList();
            }
            else if (case_name == "UpdateCommunityAllNodeDevContactList")
            {
                CommConfigHandle handle(mng_id, unit_id, node, macs);
                handle.UpdateCommunityAllNodeDevContactList();
            }
            else if (case_name == "UpdateCommunityAllUnitDevContactList")
            {
                CommConfigHandle handle(mng_id, unit_id, node, macs);
                handle.UpdateCommunityAllUnitDevContactList();
            }
            else
            {
                CommConfigHandle handle(mng_id, unit_id, node, macs);
                handle.UpdateNodeContactEvent();
            }            

            //检测
            char detect[4096] = "";
            snprintf(detect, sizeof(detect), "%s %s '%s'", detect_bin.c_str(), case_name.c_str(), argvs.c_str());
            AK_LOG_INFO << "check cmd: "<< detect;
            
            FILE *pp = popen(detect, "r");
            if(!pp) {
                AK_LOG_INFO << "error, cannot popen cmd: "<< detect;
                return;
            }
            char tmp[512];
            while(fgets(tmp, sizeof(tmp), pp) != NULL) {
                AK_LOG_INFO <<"check log:" << tmp;
            }
            int rv = pclose(pp);
            REQUIRE(WEXITSTATUS(rv) == 0);
            AK_LOG_INFO <<  "Case: " << case_name << " check ok. statu = "<< WEXITSTATUS(rv) ;
            
        }
    } 
}

