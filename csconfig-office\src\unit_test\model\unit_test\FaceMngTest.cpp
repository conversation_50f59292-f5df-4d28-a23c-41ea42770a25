﻿#include <string>
#include <map>
#include "FaceMng.h"
#include "unistd.h"
#include <catch2/catch.hpp>
#include "AkLogging.h"
#include "ConfigDef.h"
#include "ConnectionPool.h"
#include "ConfigFileReader.h"
#include "util_cstring.h"
#include "PerConfigHandle.h"
#include "CommConfigHandle.h"

using namespace std;

extern int DaoInit();
extern void ConfInit();

void PrintFaceMngInfos(const vector<FaceMngInfo>& face_mng_infos)
{
    AK_LOG_INFO << "print begin,size=" << face_mng_infos.size();
    for (auto it = face_mng_infos.begin(); it != face_mng_infos.end(); it++)
    {
        AK_LOG_INFO << "face_mng_id=" << it->face_mng_id << ";mng_account_id=" << it->mng_account_id
                    << ";unit_id=" << it->unit_id << ";personal_account_id=" << it->personal_account_id
                    << ";file_name=" << it->file_name << ";face_url=" << it->face_url
                    << ";name=" << it->name << ";account=" << it->account;
    }
    AK_LOG_INFO << "print end";
}

TEST_CASE("FaceMngTest", "[DaoGetFaceMng]")
{
    ConfInit();
    DaoInit();

    //section里面必须写东西
    SECTION("All")
    {
        vector<FaceMngInfo> face_mng_infos;
        int ret = CFaceMng::GetInstance().DaoGetFaceMng(face_mng_infos, 0, 0, 0);
        AK_LOG_INFO << "ret=" << ret;
        REQUIRE(ret == 0);
        PrintFaceMngInfos(face_mng_infos);
    }

    SECTION("MngAccountID")
    {
        vector<FaceMngInfo> face_mng_infos;
        int ret = CFaceMng::GetInstance().DaoGetFaceMng(face_mng_infos, 21, 0, 0);
        AK_LOG_INFO << "ret=" << ret;
        REQUIRE(ret == 0);
        PrintFaceMngInfos(face_mng_infos);
    }

    SECTION("UnitID")
    {
        vector<FaceMngInfo> face_mng_infos;
        int ret = CFaceMng::GetInstance().DaoGetFaceMng(face_mng_infos, 0, 1, 0);
        AK_LOG_INFO << "ret=" << ret;
        REQUIRE(ret == 0);
        PrintFaceMngInfos(face_mng_infos);
    }

    SECTION("PersonalAccountID")
    {
        vector<FaceMngInfo> face_mng_infos;
        int ret = CFaceMng::GetInstance().DaoGetFaceMng(face_mng_infos, 0, 0, 1);
        AK_LOG_INFO << "ret=" << ret;
        REQUIRE(ret == 0);
        PrintFaceMngInfos(face_mng_infos);
    }

	SECTION("DelFaceMngByMngId")
	{
		uint32_t mng_account_id = 22;
		int ret = CFaceMng::GetInstance().DelFaceMngByMngId(mng_account_id);
		REQUIRE(ret == 0);
	}
}

TEST_CASE("FaceXmlHelperTest", "[ToXml]")
{
    ConfInit();
    DaoInit();

    SECTION("XmlNormal")
    {
        vector<FaceMngInfo> face_mng_infos;
        int ret = CFaceMng::GetInstance().DaoGetFaceMng(face_mng_infos, 0, 0, 0);
        AK_LOG_INFO << "ret=" << ret;
        REQUIRE(ret == 0);
        PrintFaceMngInfos(face_mng_infos);

        DEVICE_SETTING device_setting;
        snprintf(device_setting.relay, sizeof(device_setting.relay), "%s", "1,门1,1,1,1;2,门2,1,1,0;3,门3,1,1,0;4,门4,1,1,1");

        string xml = CFaceXmlHelper::GetInstance().ToXml("*********************************************/pic", face_mng_infos, &device_setting);
        AK_LOG_INFO << xml;
    }

    SECTION("XmlNoData")
    {
        vector<FaceMngInfo> face_mng_infos;
        int ret = CFaceMng::GetInstance().DaoGetFaceMng(face_mng_infos, 999, 999, 999);
        AK_LOG_INFO << "ret=" << ret;
        REQUIRE(ret == 0);
        PrintFaceMngInfos(face_mng_infos);

        DEVICE_SETTING device_setting;
        snprintf(device_setting.relay, sizeof(device_setting.relay), "%s", "1,门1,1,1,1;2,门2,1,1,0;3,门3,1,1,0;4,门4,1,1,1");

        string xml = CFaceXmlHelper::GetInstance().ToXml("*********************************************/pic", face_mng_infos, &device_setting);
        AK_LOG_INFO << xml;
    }
}

TEST_CASE("CFaceXmlWriter", "[WriteXml]")
{
    ConfInit();
    DaoInit();

    SECTION("Personal")
    {
        DEVICE_SETTING device_setting;
        strcpy(device_setting.mac, "255200001FFF");

        std::vector<DEVICE_CONTACTLIST> personal_account_ids;
        DEVICE_CONTACTLIST contact_list;
        contact_list.id = 31;
        personal_account_ids.push_back(contact_list);
        contact_list.id = 33;
        personal_account_ids.push_back(contact_list);

        std::vector<FaceMngInfo> face_mng_infos;
        int ret = CFaceMng::GetInstance().DaoGetFaceMngByPersonalAccountIds(face_mng_infos, personal_account_ids);
        if (ret != 0)
        {
            AK_LOG_WARN << "DaoGetFaceMngByPersonalAccountIds Failed";
            return;
        }
        PrintFaceMngInfos(face_mng_infos);

        std::string face_root_path = GetPersonnalDownloadFacePath("**********");
        ret = CFaceXmlWriter::GetInstance().WriteXml(&device_setting, face_mng_infos, face_root_path);
        REQUIRE(ret == 0);
    }

}

TEST_CASE("PerConfigHandle", "OnPersonalFileUpdate")
{
    ConfInit();
    DaoInit();

    SECTION("MasterAccount")
    {
        std::string node = "**********";
        PerConfigHandle handle(node);
        handle.UpdateNodeFace();
    }
}

TEST_CASE("CommConfigHandle", "OnCommunityFileUpdate")
{
    ConfInit();
    DaoInit();

    SECTION("WEB_COMM_IMPORT_FACE_PIC")
    {
        uint32_t mng_id = 22;
		std::vector<std::string> macs;
        CommConfigHandle handle(mng_id, 0, "", macs);
        handle.UpdateCommunityPubFace();
        handle.UpdateCommunityAllUnitFace();
        handle.UpdateCommunityAllNodeFace();
    }

    SECTION("WEB_COMM_UPLOAD_FACE_PIC")
    {
        uint32_t mng_id = 22;
        uint32_t unit_id = 1;
        std::string node = "**********";
		std::vector<std::string> macs;
        CommConfigHandle handle(mng_id, unit_id, node, macs);
        handle.UpdateCommunityPubFace();
        handle.UpdateCommunityOneUnitFace();
        handle.UpdateCommunityOneNodeFace();
    }

    SECTION("WEB_COMM_DELETE_FACE_PIC")
    {
        uint32_t mng_id = 22;
        uint32_t unit_id = 1;
        std::string node = "**********";
		std::vector<std::string> macs;
        CommConfigHandle handle(mng_id, unit_id, node, macs);
        handle.UpdateCommunityPubFace();
        handle.UpdateCommunityOneUnitFace();
        handle.UpdateCommunityOneNodeFace();
    }
}

