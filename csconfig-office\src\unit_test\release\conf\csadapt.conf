#csadapt conf
csadapt_outerip=**************
csadapt_outerdomain=
csadapt_port=8503

#cspbx conf
cspbx_ip=*************
cspbx_port=5070

#db conf
db_ip=127.0.0.1
db_username=root
db_passwd=Ak@56@<EMAIL>
db_database=AKCS_UnitTest
db_port=3306

#nsq
nsq_delpic_topic=delpic

#etcd conf,etcd是集群,通过配置文件指定
etcd_srv_net=http://***********:8507;http://***********:18507;

#nsq route conf
nsq_route_topic=ak_route

#beanstalkd
beanstalkd_ip=127.0.0.1

#remote sshd domain
remote_config_domain=remoteconfig.akuvox.com

#web ip
web_ip=*************

#1=ccloud 2=scloud 3=ecloud 4=ucloud 5=other 6=rcloud
system_area_type=5
#空房间不写主账号联系人，小区列表id(id1,id2) 
#目前只有美国有特殊处理,注意前后的逗号要有
community_ids=,5037,5041,5532,

#AWS
is_aws=0
aws_mysql_ip=************

noencrypt=1