/*
Navicat MySQL Data Transfer

Source Server         : localhost_3306
Source Server Version : 50622
Source Host           : localhost:3306
Source Database       : sdmc

Target Server Type    : MYSQL
Target Server Version : 50622
File Encoding         : 65001

Date: 2016-12-23 14:17:15
Ver: akcs-32
*/
DROP database if exists AKCS;
CREATE database AKCS;
use AKCS;

SET FOREIGN_KEY_CHECKS=0;

DROP TABLE IF EXISTS Account;
CREATE TABLE `Account` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `Account` char(64) NOT NULL,
  `Passwd` char(64) NOT NULL,
  `Grade` tinyint(1) DEFAULT 0 COMMENT '等级:1超;11区域;21小区;22个人终端;31物业',
  `Role` tinyint(1) DEFAULT 0 COMMENT '角色:0超管;1管理员|区管;2小区物业;3小区安装者;4个人终端管理员',
  `ParentID` int(10) DEFAULT NULL,
  `Location` char(32) NOT NULL COMMENT '对于小区管理员:小区名，对于个人终端管理员:公司',
  `Email` char(32) NOT NULL,
  `Info` char(64) DEFAULT '',
  `SipPrefix` int(10) NOT NULL DEFAULT '0' COMMENT '对于每一级管理员，记录到本级sip规则的前缀',
  `Special` tinyint(1) DEFAULT '0',
  `Phone` char(20) DEFAULT '',
  `TimeZone` char(64) DEFAULT '+0:00 Abidjan' COMMENT '时区',
  `HouseCount` int(11) DEFAULT '100' COMMENT '个人终端主账号创建个数',
  `EnableValidTimeSetting` tinyint(1) NOT NULL DEFAULT '0' COMMENT '个人终端设置主账号时间',
  `EnableCountSetting` tinyint(1) NOT NULL DEFAULT '0' COMMENT '个人终端设置主账号app/dev个数',
  `ManageGroup` int(11) NOT NULL DEFAULT 0 comment '社区管理员/个人终端管理员组标识',
  `CustomizeForm` tinyint(1) DEFAULT '3' COMMENT 'time1:（1:12小时制，2:24小时制）；time2:(1:y-m-d,3:m-d-y,5:d-m-y),此处值为time1+time2',
  `ChargeMode` tinyint(1) NOT NULL DEFAULT '0' COMMENT '针对distributor和installer 0：下级缴费；1：本级缴费',
  `SipType` tinyint DEFAULT 3 comment '0=udp  1=tcp 2=tls 3=none, 总开关 如果是none按设备表SipType写配置',
  `Initialization` tinyint(1) DEFAULT '0'  COMMENT '是否初始化过，用于installer首次登录修改密码',
  `Language` char(8) NOT NULL  Default 'en',
  `SendExpireEmailType` tinyint(1) NOT NULL DEFAULT '1' COMMENT '1:发送给自己，2:发送给PM/User',
  `Flags` int DEFAULT 0 COMMENT '标识位: 第1位rtp混淆开关,默认关',
  `CreateTime` datetime DEFAULT NULL,
  `SendRenew` tinyint(1) DEFAULT 1 COMMENT '是否发送邮箱通知更新 0:不通知 1:通知',
  `UUID` char(36) NOT NULL COMMENT '唯一标识',
  PRIMARY KEY (`ID`),
  KEY `ManageGroup` (`ManageGroup`),
  KEY `Grade_SipPreFix` (`Grade`,`SipPrefix`),
  UNIQUE KEY `Account` (`Account`),
  UNIQUE INDEX UUID (`UUID`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8;
/*superManage supAk56#10!pt预先创建*/
INSERT INTO Account(ID,Account,Passwd,Grade,Role,ParentID) VALUES ('1', 'superManage', '0f1dbde6161d8ce78c2bd550129d045d', '1', '0', '0');
/*区域管理员，用于散户 areaAk56#10!pt预先创建*/
/*INSERT INTO `Account` VALUES ('2', 'areaManage', '64a09f5eab8e97646f14af3fbf807f67', '11', '0', '1', '', '', '', 0);*/
/*个人终端管理员，用于散户 perAk56#10!pt预先创建*/
/*INSERT INTO `Account` VALUES ('3', 'personalManage', 'b7cbb05610267073a6ea6b6c750a8602', '22', '0', '2', 'DispersiveUser', '', '', 0);*/


-- ----------------------------
-- Table structure for `Alarms`
-- ----------------------------
DROP TABLE IF EXISTS Alarms;
CREATE TABLE `Alarms` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `AlarmType` char(64) DEFAULT '',
  `MngAccountID` int(10) NOT NULL DEFAULT 0 COMMENT '社区管理员ID，参见Account表',
  `UnitID` int(10) NOT NULL DEFAULT 0 COMMENT '全局的单元unit ID',
  `DevicesMAC` char(16) DEFAULT NULL COMMENT '告警设备的mac地址',
  `Node` char(16) DEFAULT '',
  `AlarmTime` datetime DEFAULT NULL,
  `Status`  int(4) DEFAULT NULL,
  `DealTime` datetime DEFAULT NULL,
  `DealUser` char(64) DEFAULT '',
  `DealType` int(11) DEFAULT NULL,
  `DealResult` varchar(1024) DEFAULT '',
  `AlarmCode` tinyint DEFAULT 0 comment '0 old data/1-Door Unlock/2-Infrared/3-Drmagent/4-Smoke/5-Gas/6-Urgency/7-SOS/8-Tamper',
  `AlarmCustomize` tinyint(1) NOT NULL DEFAULT 1 COMMENT '自定义',
  `AlarmLocation` tinyint(1) NOT NULL DEFAULT 0 COMMENT '位置',
  `AlarmZone` tinyint(1) NOT NULL DEFAULT 0 COMMENT '防区',
  PRIMARY KEY (`ID`),
  INDEX `MAC` (`DevicesMAC`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;

-- ----------------------------
-- Table structure for `CommunityUnit`,全局的社区单元表
-- ----------------------------
DROP TABLE IF EXISTS CommunityUnit;
CREATE TABLE `CommunityUnit` (
  `ID` int(10) NOT NULL AUTO_INCREMENT,
  `MngAccountID` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '所属那个社区ID,见Account表',
  `UnitName` varchar(128) DEFAULT '' COMMENT 'unit名称,最长128个字节',
  PRIMARY KEY (`ID`),
  INDEX `MngAccountID_key` (`MngAccountID`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS CommunityInfo;
CREATE TABLE `CommunityInfo` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `AccountID` int(10) unsigned NOT NULL,
  `Street` varchar(128) NOT NULL DEFAULT '',
  `City` varchar(128) NOT NULL DEFAULT '',
  `PostalCode` varchar(32) NOT NULL DEFAULT '',
  `Country` varchar(128) NOT NULL DEFAULT '',
  `States` varchar(128) NOT NULL DEFAULT '',
  `EnableMotion` tinyint(1) NOT NULL DEFAULT '0' COMMENT '门口机是否开启motion',
  `MotionTime` tinyint(1) NOT NULL DEFAULT '10' COMMENT '门口机监测多久没有动作上报截图时间',
  `AptPinType` tinyint(1) DEFAULT '0' COMMENT '0为PIN 1为APT+PIN',
  `FaceEnrollment` tinyint(1) NOT NULL DEFAULT '1', 
  `IDCardVerification` tinyint(1) NOT NULL DEFAULT '1',
  `NumberOfApt` int(11) unsigned NOT NULL DEFAULT '20',
  `LastDevOfflineNotifyTime` datetime DEFAULT '1970-01-01 08:00:00' COMMENT '上次通知时间',
  `Switch` tinyint(1) DEFAULT 5 COMMENT '按位开关标示符:1=落地开关,2=掉线通知开发,3=是否允许用户使用PIN,4=SIM卡超流量开关,5=是否启用智能家居',
  `IsNew` tinyint default 0 COMMENT 'V6.1新旧社区标识 0旧社区',
  `MobileNumber` char(24) NOT NULL  DEFAULT '' COMMENT '手机号',
  `PhoneCode` char(8) NOT NULL DEFAULT '' COMMENT '区号',
  `FeatureExpireTime` datetime DEFAULT '2299-12-31 23:59:59' COMMENT '高级功能过期时间',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `AccountID` (`AccountID`),
  CONSTRAINT `CommunityInfo_ibfk_1` FOREIGN KEY (`AccountID`) REFERENCES `Account` (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=674 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS CommunityRoom;
CREATE TABLE `CommunityRoom` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `UnitID` int(11) DEFAULT NULL,
  `RoomName` varchar(128) DEFAULT '' COMMENT '房间名称,最长128个字节',
  PRIMARY KEY (`ID`),
  KEY `UnitID_key` (`UnitID`),
  CONSTRAINT `CommunityRoom_ibfk_1` FOREIGN KEY (`UnitID`) REFERENCES `CommunityUnit` (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=1692 DEFAULT CHARSET=utf8;
-- ----------------------------
-- Table structure for `Devices`
-- ----------------------------
DROP TABLE IF EXISTS Devices;
CREATE TABLE `Devices` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `MngAccountID` int(10) NOT NULL DEFAULT 0 COMMENT '社区管理员ID，参见Account表',
  `UnitID` int(10) NOT NULL DEFAULT 0 COMMENT '全局的单元unit ID',
  `Node` char(16) DEFAULT '' COMMENT '家庭主账号uid',
  `MAC` char(32) DEFAULT '',
  `Type` int(11) NOT NULL COMMENT '设备的机型,室内机、室外机...',
  `Grade` tinyint(1) NOT NULL DEFAULT 0 COMMENT '设备的归属等级,1=社区共享,2=单元共享,3=家庭独占...',
  `IPAddress` char(40) DEFAULT '',
  `Gateway` char(40) DEFAULT '',
  `SubnetMask` char(40) DEFAULT '',
  `PrimaryDNS` char(40) DEFAULT '',
  `SecondaryDNS` char(40) DEFAULT '',
  `Firmware` char(32) DEFAULT '',
  `Hardware` char(32) DEFAULT '',
  `Status` tinyint(1) DEFAULT 0,
  `outerIP` varchar(40) DEFAULT '',
  `Port` int(10) unsigned zerofill DEFAULT 0,
  `LastConnection` datetime DEFAULT NULL,
  `PrivatekeyMD5` char(32) DEFAULT '',
  `RfidMD5` char(32) DEFAULT '',
  `ConfigSettings` varchar(2048) DEFAULT '',
  `ConfigMD5` char(32) DEFAULT '',
  `ContactMD5` char(33) NOT NULL DEFAULT '',
  `SipAccount` char(64) DEFAULT '',
  `SipPwd` char(16) NOT NULL DEFAULT '',
  `RtspPwd` char(24) DEFAULT '' COMMENT '室外机rtsp监控密码',
  `Location` char(64) DEFAULT '',
  `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `ExpireTime` datetime DEFAULT '2299-12-31 23:59:59' COMMENT '公共设备默认时间',
  `DclientVer` int(10) NOT NULL DEFAULT 0 COMMENT 'Dclient版本号，用来区分客户端版本',
  `NetGroupNumber` tinyint(1) NOT NULL DEFAULT 0 COMMENT '设备的网络组号',
  `StairShow` tinyint(1) DEFAULT '0' COMMENT '公共设备显示格式: 0:default 1:roomnum  2:app/dev显示控制',
  `AuthCode` char(20) default '' Comment '设备校验码',
  `Relay` varchar(256) NOT NULL DEFAULT '#,Relay1,1,1,1' COMMENT '格式: 开门按键,doorname,是否显示在home,是否显示在talking,是否启用;多个relay以逗号分隔',
  `Config` varchar(2048) NOT NULL DEFAULT '' COMMENT '手动新增的配置',
  `Arming` tinyint(1) DEFAULT "0" COMMENT '室内机arming状态',
  `SipType` tinyint(1) DEFAULT "1" COMMENT '0=udp  1=tcp 2=tls',
  `AccSrvID` char(24) NOT NULL DEFAULT '' COMMENT '接入服务器id的标示',
  `Flags` int(11) DEFAULT 8 COMMENT '按位标识 1=home;2=away;3=sleep;4=管理机是否开启全选,默认开启;5-8位代表设备relay的开关情况 0关1开;9=室内机上线标识;10=室内机所属的家庭是否为Kit方案',
  `ArmingFunction` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'smartplus显示arming的入口',
  `LastDisConn` datetime DEFAULT NULL COMMENT '上次设备断线时间',
  `FaceMD5` char(32) DEFAULT '',
  `UserMetaMD5` char(32) DEFAULT '',
  `ScheduleMD5` char(32) DEFAULT '',
  PRIMARY KEY (`ID`),
  KEY `MAC` (`MAC`),
  KEY `MngAccountID` (`MngAccountID`),
  KEY `UnitID` (`UnitID`),
  KEY `SipAccount` (`SipAccount`),
  KEY `Node` (Node)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for `DeviceUpgrade`
-- ----------------------------
DROP TABLE IF EXISTS DeviceUpgrade;
CREATE TABLE `DeviceUpgrade` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `Model` char(64) NOT NULL,
  `ModelID` char(32) NOT NULL,
  `FirmwareVersion` char(64) DEFAULT '',
  `FirmwareFile` char(64) DEFAULT '',
  `UpdateTime` datetime DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;

-- ----------------------------
-- Table structure for `OwnerManagement`
-- ----------------------------
DROP TABLE IF EXISTS OwnerManagement;
CREATE TABLE `OwnerManagement` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Name` char(48) DEFAULT '',
  `Sex` int(11) DEFAULT NULL,
  `Community` varchar(32) DEFAULT '',
  `Address` char(128) DEFAULT '',
  `IDCard` char(64) DEFAULT '',
  `Phone` char(64) DEFAULT '',
  `UpdateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateUser` char(24) DEFAULT 'Administrator',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;

-- ----------------------------
-- Table structure for `Token`
-- ----------------------------
DROP TABLE IF EXISTS Token;
CREATE TABLE `Token` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Account` char(32) NOT NULL,
  `WebToken` varchar(64) NOT NULL,
  `AppToken` varchar(64) DEFAULT NULL,
  `AlexaToken` varchar(64) NOT NULL DEFAULT '',
  `AlexaAccessToken` varchar(512) DEFAULT NULL,
  `AlexaReflashToken` varchar(512) DEFAULT NULL,  
  `AppTokenEt` int unsigned NOT NULL DEFAULT ********** comment 'token时效期',
  `AuthCode` char(32) UNIQUE KEY DEFAULT NULL,
  `AppRefreshToken` char(32) UNIQUE KEY DEFAULT NULL,
  `RefreshTokenEt` int(10) unsigned NOT NULL,
  `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'token 创建的时间',
  `AuthToken` varchar(64) DEFAULT NULL COMMENT '短信登陆方式中用于token续时的身份标识',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `AppToken` (`AppToken`),
  UNIQUE KEY `Account` (`Account`),
  UNIQUE KEY `AuthToken` (`AuthToken`),
  KEY `AlexaToken` (`AlexaToken`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- Table structure for `PersonalAccount`
-- ----------------------------
DROP TABLE IF EXISTS PersonalAccount;
CREATE TABLE `PersonalAccount` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `Account` char(32) NOT NULL,
  `Passwd` char(64) NOT NULL,
  `Role` tinyint(1) DEFAULT 0 COMMENT '10=个人用户主账号;11=个人从账号;20=社区用户主账号;21=社区从账号',
  `ParentID` int(10) unsigned DEFAULT 0 COMMENT '对于社区用户主账号，就是社区管理员的ID',
  `UnitID` int(10) unsigned DEFAULT 0 COMMENT '对于社区用户就是unit单元的ID',
  `Email` char(64) DEFAULT NULL,
  `Info` char(64) DEFAULT '',
  `Name` varchar(128) DEFAULT '',
  `SipAccount` char(64) DEFAULT '',
  `SipPwd` char(16) NOT NULL DEFAULT '',
  `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `Address` char(64) DEFAULT '',
  `Phone` char(24) DEFAULT '' COMMENT '落地时使用',
  `PhoneStatus` tinyint(1) DEFAULT 0 COMMENT '该号码是否开启群响铃的开关：0:不开启，1:开启',
  `RoomNumber` char(64) DEFAULT '',
  `TimeZone` char(64) DEFAULT '+0:00 Abidjan' COMMENT '时区',
  `ReadMsgID` int(10) unsigned NOT NULL DEFAULT 0 COMMENT '账号读取到通知消息的id',
  `ExpireTime` datetime DEFAULT '2299-12-31 23:59:59' COMMENT '过期时间',
  `EnableIpDirect` tinyint(1) default 1 COMMENT '是否启动ip直播 1启动 0关闭 ',
  `Special`  tinyint(1) DEFAULT 0 COMMENT '特殊账号标识,标识个人终端管理员里面的公共设备的虚拟主账号',
  `CustomizeForm` tinyint(1) DEFAULT '3' COMMENT 'time1:（1:12小时制，2:24小时制）；time2:(1:y-m-d,3:m-d-y,5:d-m-y),此处值为time1+time2',
  `FreeDays` int unsigned NOT NULL DEFAULT 0 COMMENT '主账号试用天数',
  `NFCCode`  varchar(32) NOT NULL DEFAULT '' comment '个人账号的NFC 有值代表开，没有代表关',
  `BLECode`  varchar(32) NOT NULL DEFAULT '' comment '个人账号的BLE 有值代表开，没有代表关',
  `Active`  tinyint NOT NULL default 1 comment '是否激活 1激活 0未激活',
  `Initialization`  tinyint NOT NULL default 0 comment '是否初始化过',
  `appLoginStatus`  tinyint NOT NULL default 0 comment '是否在app登陆过 1登陆过 0未登陆过',
  `ActiveTime` timestamp NULL DEFAULT NULL,
  `FirstName` varchar(64) DEFAULT '',
  `LastName` varchar(64) DEFAULT '',
  `RoomID`  int unsigned NOT NULL comment '个人房间标识',
  `SipType` tinyint(1) DEFAULT 0 comment '0udp/1tcp/2tls',
  `Codec`  varchar(32) NOT NULL default "0,8,18" comment '0=PCMU, 8=PCMA, 18=G.729 用逗号隔开代表优先级 18,0,8。如果值空代表默认或客户端自行定义',
  `TempKeyPermission` tinyint(1) DEFAULT 1 COMMENT 'TempKey生成权限 0:无 1:有',
  `Phone2` char(24) DEFAULT '',
  `Phone3` char(24) DEFAULT '', 
  `PhoneCode` char(8) DEFAULT '' comment '区号',
  `PhoneExpireTime` datetime DEFAULT '2020-01-01' comment '不能是当前时间,不然不管有没有开落地,隔天就会邮件提醒',
  `Language` char(8) NOT NULL Default 'en',
  `MobileNumber` char(24) DEFAULT NULL COMMENT '手机号帐号',
  `Version` int(10) unsigned DEFAULT 123456 COMMENT '用户数据版本号',
  `BLEOpenDoorType` tinyint(1) DEFAULT 0 COMMENT '蓝牙开门的方式 0=摇一摇, 1=无感',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Email` (`Email`),
  KEY `ParentID` (`ParentID`),
  KEY `UnitID` (`UnitID`),
  KEY `NFC_BLE` (`NFCCode`, `BLECode`),
  KEY `Name` (`Name`),
  UNIQUE KEY `Account` (`Account`),
  UNIQUE KEY `MobileNumber` (`MobileNumber`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- ----------------------------
-- Table structure for `PersonalAlarms`
-- ----------------------------
DROP TABLE IF EXISTS PersonalAlarms;
CREATE TABLE `PersonalAlarms` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `AlarmType` char(64) DEFAULT '',
  `Address` char(64) DEFAULT '',
  `Community` varchar(32) DEFAULT NULL comment '个人终端管理员',
  `Node` char(32) DEFAULT '',
  `Extension` int(11) DEFAULT NULL,
  `AlarmTime` datetime DEFAULT NULL,
  `Status` int(4) DEFAULT NULL,
  `DealTime` datetime DEFAULT NULL,
  `DealUser` char(64) DEFAULT '',
  `DealType` int(11) DEFAULT NULL,
  `DealResult` varchar(1024) DEFAULT '',
  `DevicesMAC` char(16) DEFAULT '' comment '告警设备的mac地址',
  `AlarmCode` tinyint DEFAULT 0 comment '0 old data/1-Door Unlock/2-Infrared/3-Drmagent/4-Smoke/5-Gas/6-Urgency/7-SOS/8-Tamper',
  `AlarmCustomize` tinyint(1) NOT NULL DEFAULT 1 COMMENT '自定义',
  `AlarmLocation` tinyint(1) NOT NULL DEFAULT 0 COMMENT '位置',
  `AlarmZone` tinyint(1) NOT NULL DEFAULT 0 COMMENT '防区',
  PRIMARY KEY (`ID`),
  INDEX `Community_Node` (Community,Node),
  INDEX `MAC` (`DevicesMAC`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for `PersonalAppTmpKey`
-- ----------------------------
DROP TABLE IF EXISTS PersonalAppTmpKey;
CREATE TABLE `PersonalAppTmpKey` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MngAccountID` int(10) NOT NULL DEFAULT 0 COMMENT '社区管理员ID，参见Account表',
  `UnitID` int(10) NOT NULL DEFAULT 0 COMMENT '全局的单元unit ID',
  `Type` tinyint(1) DEFAULT '0' comment '0:社区终端用户的tmp key;1:个人终端用户的tmp key',
  `TmpKey` int(11) NOT NULL,
  `Node` char(32) DEFAULT '',
  `BeginTime` timestamp NULL DEFAULT NULL,
  `EndTime` timestamp NULL DEFAULT NULL,
  `AccessTimes` int(11) DEFAULT '0',
  `AllowedTimes` int(11) DEFAULT NULL,
  `QrCodeUrl` varchar(128) DEFAULT '' comment '临时秘钥二维码图片的完整url',
  `DeliveryTXT` varchar(32) DEFAULT '' COMMENT '分享方式 email',
  `Description` varchar(64) DEFAULT '' COMMENT '描述',
  `IDNumber` char(20) NOT NULL DEFAULT '', 
  `Creator` char(32) DEFAULT '',
  `SchedulerType` tinyint(1) NOT NULL DEFAULT '0' COMMENT '计划的类型,0:单次计划; 1:每日计划；2:每周计划',
  `DateFlag` int(11) NOT NULL DEFAULT '0' COMMENT '只有每周计划使用,可能的选择如下: {0x01,0x02,0x04,0x08,0x10,0x20,0x40};从周日开始，到周六结束',
  `StartTime` time DEFAULT NULL COMMENT '存储计划的开始时间点,格式:HH:MM:SS',
  `StopTime` time DEFAULT NULL COMMENT '存储计划的结束时间点,格式:同上', 
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `Node` (`Node`),
  KEY `TmpKey` (`TmpKey`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for `PersonalDevices`
-- ----------------------------
DROP TABLE IF EXISTS PersonalDevices;
CREATE TABLE `PersonalDevices` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `Type` int(11) NOT NULL,
  `Community` char(32) DEFAULT NULL COMMENT '个人终端管理员',
  `Node` char(64) DEFAULT '',
  `Extension` int(11) DEFAULT NULL,
  `IPAddress` char(40) DEFAULT '',
  `Gateway` char(40) DEFAULT '',
  `SubnetMask` char(40) DEFAULT '',
  `PrimaryDNS` char(40) DEFAULT '',
  `SecondaryDNS` char(40) DEFAULT '',
  `MAC` char(32) DEFAULT NULL,
  `Firmware` char(32) DEFAULT '',
  `Hardware` char(32) DEFAULT '',
  `Status` tinyint(1) DEFAULT 0,
  `outerIP` varchar(40) DEFAULT '',
  `Port` int(10) unsigned zerofill DEFAULT NULL,
  `LastConnection` datetime DEFAULT NULL,
  `PrivatekeyMD5` char(32) DEFAULT '',
  `RfidMD5` char(32) DEFAULT '',
  `ConfigMD5` char(32) DEFAULT '',
  `ContactMD5` char(33) NOT NULL DEFAULT '',
  `SipAccount` char(64) DEFAULT '',
  `SipPwd` char(16) NOT NULL DEFAULT '',
  `RtspPwd` char(24) DEFAULT '' COMMENT '室外机rtsp监控密码',
  `Location` char(64) DEFAULT '',
  `CreateTime` timestamp NULL DEFAULT NULL,
  `ExpireTime` datetime DEFAULT '2299-12-31 23:59:59' COMMENT '公共设备默认时间',
  `DclientVer` int(10) NOT NULL DEFAULT 0 COMMENT 'Dclient版本号，用来区分客户端版本',
  `NetGroupNumber` tinyint(1) NOT NULL DEFAULT 1 COMMENT '设备的网络组号',
  `Flag` int(11) NOT NULL DEFAULT 0 COMMENT '个人类型设备的标示符，位计算。第1位标示设备是否为个人终端管理员的，第二位之后暂时保留',
  `StairShow` tinyint(1) DEFAULT '0' COMMENT '公共设备显示格式: 0:default 1:roomnum  2:roomnum/app/dev 3:app/dev',
  `AuthCode` char(20) default '' Comment '设备校验码',
  `Relay` varchar(256) NOT NULL DEFAULT '#,Relay1,1,1,1' COMMENT '格式: 开门按键,doorname,是否显示在home,是否显示在talking,是否启用;多个relay以逗号分隔',
  `Config` varchar(2048) NOT NULL DEFAULT '' COMMENT '手动新增的配置',
  `AccSrvID` char(24) NOT NULL DEFAULT '' COMMENT '接入服务器id的标示',
  `Arming` tinyint(1) DEFAULT "0" COMMENT '室内机arming状态',
  `SipType` tinyint(1) DEFAULT "1" COMMENT '0=udp  1=tcp 2=tls',
  `Flags` int(11) DEFAULT 8 COMMENT '按位标识 1=home;2=away;3=sleep;4=管理机是否开启全选,默认开启;5-8位代表设备relay的开关情况 0关1开;9=室内机上线标识;10=室内机所属的家庭是否为Kit方案',
  `LastDisConn` datetime DEFAULT NULL COMMENT '上次设备断线时间',
  `FaceMD5` char(32) DEFAULT '',
  `UserMetaMD5` char(32) DEFAULT '',
  `ScheduleMD5` char(32) DEFAULT '',
  PRIMARY KEY (`ID`),
  KEY `MAC` (`MAC`),
  KEY `SipAccount` (`SipAccount`),
  KEY `Community_Node` (`Community`,`Node`),
  KEY `Node` (`Node`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS CallHistory;
CREATE TABLE `CallHistory` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Node` char(16) DEFAULT NULL COMMENT '被叫所在的联动系统，因为个人没办法呼叫公共设备',
  `CallerID` char(16) NOT NULL DEFAULT '' COMMENT '主叫方的sip账号',
  `CallerName` char(64) NOT NULL DEFAULT '' COMMENT '主叫方的昵称,若主叫方为设备，则为设备Location;若主叫方为App,则为App用户的账号昵称',
  `CalleeID` char(16) NOT NULL DEFAULT '' COMMENT '被叫方的sip账号',
  `CalleeName` char(64) NOT NULL DEFAULT '' COMMENT '被叫方的昵称,若被叫方为设备，则为设备Location;若被叫方为App,则为App用户的账号昵称',
  `IsAnswer` tinyint(1) NOT NULL DEFAULT '0' COMMENT '呼叫是否成功标示,0:呼叫成功; 1:呼叫不成功',
  `StartTime` datetime DEFAULT CURRENT_TIMESTAMP,
  `Duration` char(16) NOT NULL DEFAULT '' COMMENT '呼叫时长 format 00:00:00',
  `Duration2` int(11) NOT NULL DEFAULT 0,
  `CallType` tinyint(1) NOT NULL DEFAULT '0' COMMENT '呼叫类型 1=app->app 2=dev->app 3=app->dev 4=dev->dev',
  `Status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已读的标志,0:未读(app需要); 1:已读',
  `SipGroup` varchar(20) NOT NULL DEFAULT '' COMMENT '是否是群组呼叫, 有值代表是群组呼叫',
  `MngAccountID` int(10) default 0 COMMENT '管理员id',
  PRIMARY KEY (ID,StartTime),
  KEY `CallerID` (`CallerID`),
  KEY `CalleeID` (`CalleeID`),
  KEY `MngAccountID` (`MngAccountID`),
  KEY `Node` (`Node`)
) ENGINE=InnoDB AUTO_INCREMENT=203 DEFAULT CHARSET=utf8
PARTITION BY RANGE(MONTH(StartTime))(
PARTITION P1 VALUES LESS THAN (2),
PARTITION P2 VALUES LESS THAN (3),
PARTITION P3 VALUES LESS THAN (4),
PARTITION P4 VALUES LESS THAN (5),
PARTITION P5 VALUES LESS THAN (6),
PARTITION P6 VALUES LESS THAN (7),
PARTITION P7 VALUES LESS THAN (8),
PARTITION P8 VALUES LESS THAN (9),
PARTITION P9 VALUES LESS THAN (10),
PARTITION P10 VALUES LESS THAN (11),
PARTITION P11 VALUES LESS THAN (12),
PARTITION P12 VALUES LESS THAN (13)
);

-- ----------------------------
-- Table structure for `PersonalLogs`
-- ----------------------------
DROP TABLE IF EXISTS PersonalLogs;
CREATE TABLE `PersonalLogs` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `DevicesID` int(10) DEFAULT NULL,
  `AccountID` int(10) DEFAULT NULL,
  `Type` tinyint(1) DEFAULT NULL,
  `Action` char(32) DEFAULT '',
  `Operator` char(32) DEFAULT '',
  `Time` timestamp NULL DEFAULT NULL,
  `Result` char(32) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
-- ----------------------------
-- Table structure for `PersonalPrivateKey`
-- ----------------------------
DROP TABLE IF EXISTS PersonalPrivateKey;
CREATE TABLE `PersonalPrivateKey` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `MngAccountID` int(10) NOT NULL DEFAULT 0 COMMENT '社区管理员ID，参见Account表',
  `UnitID` int(10) NOT NULL DEFAULT 0 COMMENT '全局的单元unit ID',
  `Grade` tinyint(1) NOT NULL DEFAULT 0 COMMENT '设备的归属等级,1=社区共享,2=单元共享,3=家庭独占...',
  `Type` tinyint(1) DEFAULT '0' COMMENT '0:社区终端用户的card;1:个人终端用户的card',
  `Code` char(20) DEFAULT NULL,
  `Status` tinyint(1) NOT NULL DEFAULT '0',
  `Node` char(32) DEFAULT NULL,
  `CreateTime` timestamp DEFAULT CURRENT_TIMESTAMP,
  `ExpireTime` datetime DEFAULT NULL,
  `Access` varchar(4096) DEFAULT '',
  `AccountID` int(11) unsigned DEFAULT NULL COMMENT 'personal account id',
  `Special` tinyint(1) DEFAULT '0' COMMENT '是否是创建账号时候创建的key',
  `SchedulerType` tinyint(1) NOT NULL DEFAULT '1' COMMENT '计划的类型,0:单次计划; 1:每日计划；2:周计划',
  `DateFlag` int(11) NOT NULL DEFAULT '0' COMMENT '只有周计划使用,可能的选择如下: {0x01,0x02,0x04,0x08,0x10,0x20,0x40};从周日开始，到周六结束',
  `BeginTime` timestamp DEFAULT NULL COMMENT '单次计划的开始时间点,格式:YYYY-MM-DD HH:MM:SS',
  `EndTime` timestamp DEFAULT NULL COMMENT '单次计划的结束时间点,格式:同上',
  `StartTime` time DEFAULT '00:00:00' COMMENT '日、周计划的开始时间点,格式:HH:MM:SS',
  `StopTime` time DEFAULT '23:59:59' COMMENT '日、周计划的结束时间点,格式:同上',
  PRIMARY KEY (`ID`),
  KEY `Node` (`Node`),
  INDEX `MngAccountID_Code` (`MngAccountID`, `Code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for `PersonalRfcardKey`
-- ----------------------------
DROP TABLE IF EXISTS PersonalRfcardKey;
CREATE TABLE `PersonalRfcardKey` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MngAccountID` int(10) NOT NULL DEFAULT 0 COMMENT '社区管理员ID，参见Account表',
  `UnitID` int(10) NOT NULL DEFAULT 0 COMMENT '全局的单元unit ID',
  `Grade` tinyint(1) NOT NULL DEFAULT 0 COMMENT '设备的归属等级,1=社区共享,2=单元共享,3=家庭独占...',
  `Type` tinyint(1) DEFAULT '0' comment '0:社区终端用户的card;1:个人终端用户的card',
  `Code` char(20) DEFAULT NULL,
  `Status` tinyint(1) NOT NULL DEFAULT '0',
  `Node` char(32) DEFAULT '',
  `CreateTime` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `ExpireTime` datetime DEFAULT NULL,
  `Access` varchar(4096) DEFAULT '',
  `AccountID` int(11) unsigned DEFAULT NULL comment 'account id',
  `Special` tinyint(1) DEFAULT '0' COMMENT '是否是创建账号时候创建的key',
  `SchedulerType` tinyint(1) NOT NULL DEFAULT '1' COMMENT '计划的类型,0:单次计划; 1:每日计划；2:周计划',
  `DateFlag` int(11) NOT NULL DEFAULT '0' COMMENT '只有周计划使用,可能的选择如下: {0x01,0x02,0x04,0x08,0x10,0x20,0x40};从周日开始，到周六结束',
  `BeginTime` timestamp DEFAULT NULL COMMENT '单次计划的开始时间点,格式:YYYY-MM-DD HH:MM:SS',
  `EndTime` timestamp DEFAULT NULL COMMENT '单次计划的结束时间点,格式:同上',
  `StartTime` time DEFAULT '00:00:00' COMMENT '日、周计划的开始时间点,格式:HH:MM:SS',
  `StopTime` time DEFAULT '23:59:59' COMMENT '日、周计划的结束时间点,格式:同上',
  PRIMARY KEY (`ID`),
  KEY `Node` (`Node`),
  INDEX `MngAccountID_Code` (`MngAccountID`, `Code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for `RomVersion`
-- ----------------------------
DROP TABLE IF EXISTS RomVersion;
CREATE TABLE `RomVersion` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `Version` varchar(32) NOT NULL,
  `Model` varchar(32) NOT NULL,
  `Log` varchar(1024) NOT NULL,
  `CreateTime` timestamp NULL DEFAULT NULL,
  `FileSrc` varchar(64) DEFAULT '',
  `Url` varchar(1024) DEFAULT '' COMMENT '固件下载路径',
  `AllManage` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否默认所有管理员;0关闭，1开启',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Version` (`Version`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8; 

DROP TABLE IF EXISTS ReleaseRomVersion;
CREATE TABLE `ReleaseRomVersion` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `VersionID` int(11) NOT NULL,
  `MngID` int(11) NOT NULL COMMENT '区域管理员ID',
  PRIMARY KEY (`ID`),
  KEY `MngID` (`MngID`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for `UpgradeRomVersion`
-- ----------------------------
DROP TABLE IF EXISTS UpgradeRomVersion;
CREATE TABLE `UpgradeRomVersion` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `Version` char(32) NOT NULL,
  `Status` tinyint(1) NOT NULL comment '0:未开始, 1:升级中, 2:升级完成',
  `CreateTime` timestamp NULL DEFAULT NULL,
  `UpdateTime` timestamp NULL DEFAULT NULL,
  `OwnerAccount` char(32) NOT NULL,
  PRIMARY KEY (`ID`),
  KEY `Version` (`Version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for `UpgradeRomDevices`
-- ----------------------------
DROP TABLE IF EXISTS UpgradeRomDevices;
CREATE TABLE `UpgradeRomDevices` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `UpgradeRomVerID` int(10) NOT NULL,
  `MAC` char(20) DEFAULT '',
  `Status` tinyint(1) NOT NULL comment '0:未升级, 1:升级完成',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for `TmpToken`
-- ----------------------------
DROP TABLE IF EXISTS TmpToken;
CREATE TABLE `TmpToken` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Account` char(32) NOT NULL,
  `WebToken` varchar(64) NOT NULL,
  `AppToken` varchar(128) NOT NULL,
  `EndTime` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Account` (`Account`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for `PersonalCapture`
-- ----------------------------
DROP TABLE IF EXISTS PersonalCapture;
CREATE TABLE `PersonalCapture` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MAC` varchar(16) NOT NULL DEFAULT '',
  `MngAccountID` int  DEFAULT 0,
  `DevType` int  DEFAULT 0 comment "0 不是公共设备 1是公共设备",
  `MngType` int  DEFAULT 0 comment "1是个人 0社区, 为了下次拆分个人社区",
  `SipAccount` char(16) DEFAULT '',
  `Location` varchar(64)  DEFAULT '',
  `PicName` char(80) NOT NULL COMMENT '图片的名字,由设备端决定',
  `PicUrl` char(64) DEFAULT '' COMMENT '图片的完整url',
  `SPicUrl` char(64) DEFAULT '' COMMENT '小图的完整url',
  `CaptureAction` varchar(64) DEFAULT '',
  `Initiator` varchar(128) DEFAULT '',
  `CaptureTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `Response` tinyint(1) DEFAULT NULL comment '0:success; 1:Fail',
  `Status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已读的标志,0:未读; 1:已读',
  `Node` char(32) DEFAULT '' comment '标识由那个联动截图,用于公共设备',
  `CaptureType` tinyint(1) NOT NULL DEFAULT 99 COMMENT '截图类型 0=CALL Unlock,1=TMPKEY,2=LOCALKEY,3=RFCARD,4=FACE,100=NFC,101=BLE,102=app手动截图,103=call不开门截图,99=之前版本数据属于开门记录',
  `KeyNum` varchar(32) NOT NULL DEFAULT '' COMMENT '开门所用的key',
  `RoomNum` varchar(20) NOT NULL DEFAULT '' COMMENT 'key所属的房间号',
  PRIMARY KEY (ID,CaptureTime),
  INDEX `Node` (`Node`),
  INDEX `MAC_PicName` (`MAC`,`PicName`),
  INDEX `CaptureTime` (`CaptureTime`),
  KEY `MngAccountID` (`MngAccountID`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8
PARTITION BY RANGE(MONTH(CaptureTime))(
PARTITION P1 VALUES LESS THAN (2),
PARTITION P2 VALUES LESS THAN (3),
PARTITION P3 VALUES LESS THAN (4),
PARTITION P4 VALUES LESS THAN (5),
PARTITION P5 VALUES LESS THAN (6),
PARTITION P6 VALUES LESS THAN (7),
PARTITION P7 VALUES LESS THAN (8),
PARTITION P8 VALUES LESS THAN (9),
PARTITION P9 VALUES LESS THAN (10),
PARTITION P10 VALUES LESS THAN (11),
PARTITION P11 VALUES LESS THAN (12),
PARTITION P12 VALUES LESS THAN (13)
);

-- ----------------------------
-- Table structure for `VersionModel`
-- ----------------------------
DROP TABLE IF EXISTS VersionModel;
CREATE TABLE `VersionModel` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `VersionNumber` char(32) DEFAULT '',
  `VersionName` char(32) NOT NULL,
  `Type` tinyint(2) unsigned NOT NULL DEFAULT '0' COMMENT '标识0梯口机 1门口机 2室内机 3管理机等等',
  `CreateTime` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '110', 'E10S', '1' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '110');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '20', 'R20', '1' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '20');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '26', 'R26', '1' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '26');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '27', 'R27', '0' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '27');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '29', 'R29', '0' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '29');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '80', 'IT80', '2' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '80');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '81', 'IT81', '2' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '81');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '82', 'IT82', '2' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '82');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '83', 'IT83', '2' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '83');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '113', 'C313', '2' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '113');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '115', 'C315', '2' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '115');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '47', 'R47', '3' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '47');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '48', 'R48', '3' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '48');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '117', 'C317', '2' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '117');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '21', 'E21', '1' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '21');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '28', 'R28', '0' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '28');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '47', 'R47', '3' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '47');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '48', 'R48', '3' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '48');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '227', 'R27-V2', '0' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '227');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '226', 'R26-V2', '1' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '226');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '221', 'E21-V2', '1' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '221');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '111', 'E11', '1' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '111');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '916', 'X916', '0' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '916');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '101', 'A01', '50' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '101');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '105', 'A05', '50' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '105');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '106', 'A06', '50' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '106');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '92', 'A092', '50' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '92');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '116', 'E16', '1' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '116');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '12', 'E12', '1' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '12');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '933', 'X933', '2' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '933');
INSERT INTO VersionModel(CreateTime,VersionNumber,VersionName,Type) SELECT now(), '915', 'X915', '0' FROM DUAL WHERE NOT EXISTS(SELECT VersionNumber FROM VersionModel WHERE VersionNumber = '915');


-- ----------------------------
-- Table structure for `SystemSetting`
-- ----------------------------
DROP TABLE IF EXISTS SystemSetting;
CREATE TABLE `SystemSetting` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `SchemeVer` int(11) NOT NULL DEFAULT 0 comment '数据库scheme版本',
  `MacPoolLastID` int(11) unsigned DEFAULT 0 COMMENT '最新的Mac Pool ID',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB  DEFAULT CHARSET=UTF8;


-- ----------------------------
-- Table structure for `EmailCheckCode`
-- ----------------------------
DROP TABLE IF EXISTS EmailCheckCode;
CREATE TABLE `EmailCheckCode` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Email` char(64) NOT NULL DEFAULT '',
  `IP` char(64) NOT NULL DEFAULT '' COMMENT '哪个IP地址要求发送验证码',
  `CheckCode` char(16) NOT NULL DEFAULT '',
  `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `Email` (`Email`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;

-- ----------------------------
-- Table structure for `DeviceCode`
-- ----------------------------
DROP TABLE IF EXISTS DeviceCode;
CREATE TABLE `DeviceCode` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MAC` char(16) NOT NULL DEFAULT '' COMMENT '设备Mac',
  `HwCode` char(8) NOT NULL DEFAULT '' COMMENT '固件码',
  `Code` char(16) NOT NULL DEFAULT '' COMMENT '随机10位的数字，全表唯一',
  `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `MAC` (`MAC`),
  unique KEY `Code` (`Code`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;

DROP TABLE IF EXISTS CustomerService;
CREATE TABLE `CustomerService` (
  `MngAccount` char(64) NOT NULL COMMENT '区域管理员账号',
  `Phone` char(16) DEFAULT '',
  `Email` char(32) DEFAULT '',
  PRIMARY KEY (`MngAccount`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
insert into CustomerService(MngAccount) VALUES('superManage');

DROP TABLE IF EXISTS SipReuse;
CREATE TABLE `SipReuse` (
  `ID` int(10) NOT NULL AUTO_INCREMENT,
  `SipAccount` char(64) DEFAULT '',
  `MngAccount` char(64) DEFAULT '0' COMMENT '对应区域管理员的账号',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS SipLastAccount;
CREATE TABLE `SipLastAccount` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `SipAccount` char(16) NOT NULL COMMENT '上次添加的最新sip',
  `MngAccount` char(64) NOT NULL COMMENT '对应区域管理员账号',
  `SipGroup` char(16) NOT NULL DEFAULT '' COMMENT '上次添加的最新sip群组',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS SipGroup2;
CREATE TABLE `SipGroup2` (
  `ID` int(10) NOT NULL AUTO_INCREMENT,
  `Account` char(32) NOT NULL,
  `SipGroup` char(16) NOT NULL,
  PRIMARY KEY (`ID`),
  UNIQUE INDEX SipGroup (`SipGroup`),
  UNIQUE KEY `Account` (`Account`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS AppLoginLog;
CREATE TABLE `AppLoginLog` (
  `ID` int(10) NOT NULL AUTO_INCREMENT,
  `Uid` char(32) NOT NULL,
  `Node` char(32) NOT NULL,
  `LoginTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY `Node` (`Node`),
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS DevLoginLog;
CREATE TABLE `DevLoginLog` (
  `ID` int(10) NOT NULL AUTO_INCREMENT,
  `Mac` char(32) NOT NULL,
  `Node` char(32) NOT NULL,
  `LoginTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY `Node` (`Node`),
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS DevRtspLog;
CREATE TABLE `DevRtspLog` (
  `ID` int(10) NOT NULL AUTO_INCREMENT,
  `Mac` char(32) NOT NULL,
  `Node` char(32) NOT NULL,
  `RtspTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY `Node` (`Node`),
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


-- Table structure for `PerNodeDevices`
-- ----------------------------
DROP TABLE IF EXISTS PerNodeDevices;
CREATE TABLE `PerNodeDevices` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `NodeID` int(10) unsigned NOT NULL COMMENT '主账号ID',
  `PerDevID` int(10) unsigned NOT NULL COMMENT '个人终端管理员公共设备ID',
  PRIMARY KEY (`ID`),
  KEY `NodeID_PerDevID` (`NodeID`,`PerDevID`),
  KEY `PerDevID` (`PerDevID`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS DeviceForRegister;
CREATE TABLE `DeviceForRegister` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MngID` int(11) NOT NULL COMMENT '区域管理员ID',
  `MAC` char(12) NOT NULL,
  `Owner` char(20) DEFAULT '',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `PerMngID` int(11) NOT NULL DEFAULT '0' COMMENT '个人终端管理员id',
  PRIMARY KEY (`ID`),
  KEY `MngID` (`MngID`),
  UNIQUE KEY `MAC` (`MAC`),
  KEY `PerMngID` (`PerMngID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS AppPushToken;
CREATE TABLE `AppPushToken` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Account` char(20) NOT NULL DEFAULT '',
  `AppType` tinyint(1) DEFAULT '0' COMMENT '0ios 4fcm',
  `FcmPushToken` varchar(256) DEFAULT '' COMMENT '推送的token',
  `IOSPushToken` varchar(256) DEFAULT '' COMMENT '推送的token',
  `VoipToken` varchar(256) DEFAULT '' COMMENT '推送的voip token',
  `Node` char(20) not null default '' comment '用户的联动标识',
  `Login` tinyint(1) DEFAULT '1' COMMENT '登陆状态 1登陆 0退出',
  `Version` int(10) DEFAULT '0' COMMENT '通用版本号ios/android一样,即通过dclient设置',
  `AppVersion` varchar(32) DEFAULT '' COMMENT 'app上架版本号',
  `Language` char(16) DEFAULT "en" COMMENT 'app 客户端语言',
  `Oem` varchar(32) NOT NULL COMMENT '第三方APP',
  `LandlineNotifyStatus` tinyint(1) NOT NULL DEFAULT '1' COMMENT '0未发送落地号码更新通知 1已发送',
  `OnlineTime` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'APP最近上线时间',
  `AppOem` tinyint(1) unsigned DEFAULT 0 comment 'APP类型:0:Common App;1:MySmart App;2:Fasttel App;',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Account` (`Account`),
  KEY `Node` (`Node`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS PersonalAccountCnf;
CREATE TABLE `PersonalAccountCnf` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Account` char(20) NOT NULL DEFAULT '' COMMENT '主账号联动',
  `EnableMotion` tinyint(1) NOT NULL DEFAULT 0 COMMENT '门口机是否开启motion',
  `MotionTime` tinyint(1) NOT NULL DEFAULT 10 COMMENT '门口机监测多久没有动作上报截图时间',
  `EnableRobinCall` tinyint(1) NOT NULL DEFAULT 0 COMMENT '开启循环呼叫',
  `RobinCallTime` tinyint(1) NOT NULL DEFAULT 20 COMMENT '呼叫超时时间',
  `RobinCallVal` varchar(512) DEFAULT '' COMMENT '循环呼叫的值json格式',
  `AppCount` tinyint(1) NOT NULL DEFAULT 7 COMMENT 'app个数',
  `DevCount` SMALLINT(11) NOT NULL DEFAULT 100 COMMENT 'dev个数',
  `ValidTime` datetime NOT NULL DEFAULT '2299-12-31 23:59:59' COMMENT '过期时间',  
  `CallType` tinyint  DEFAULT 0 COMMENT '呼叫顺序和类型 0=app 1=phone 2=app先 未接听后phone',
  `AlreadySendEmail` tinyint(1) DEFAULT '0' COMMENT '创建后是否发过邮件',
  `FreeAppCount` int(4) NOT NULL DEFAULT '0' COMMENT '主账号免费从账号数量，会随着主账号购买app数量而增加',  
  `Name` varchar(128) DEFAULT '',
  `AllowCreateSlaveCnt` int  COMMENT 'app允许创建的家庭成员的个数',
  `Flags` tinyint(1) DEFAULT 0 COMMENT '按位开关,从低位到高位分别是:1=控制家庭成员',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Account` (`Account`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS VideoSchedule;
CREATE TABLE `VideoSchedule` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Node` char(16) DEFAULT '',
  `MAC` char(16) DEFAULT '',
  `DateFlag` int(11) NOT NULL DEFAULT 0 COMMENT '只有每周计划使用,可能的选择如下: {0x01,0x02,0x04,0x08,0x10,0x20,0x40};从周日开始，到周六结束',  
  `StartDay` date  COMMENT '只有单次计划使用,存储计划的开始日期点,格式:YYYY-MM-DD',
  `StopDay` date  COMMENT '存储计划的结束日期点,格式:同上', 
  `StartTime` time COMMENT '存储计划的开始时间点,格式:HH:MM:SS',
  `StopTime` time COMMENT '存储计划的结束时间点,格式:同上',
  `SchedulerType` tinyint(1) NOT NULL DEFAULT '0' COMMENT '视频定时录制计划的类型,0:单次计划; 1:每日计划；2:每周计划',
  `Status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否已失效的标志,0:未失效; 1:已失效',
  `UID` int(11) NOT NULL,
  PRIMARY KEY (`ID`),
  KEY `Node_key` (`Node`),
  KEY `MAC_key` (`MAC`),
  KEY `UID_key` (`UID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='视频存储计划';


DROP TABLE IF EXISTS VideoList;
CREATE TABLE `VideoList` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Node` char(16) DEFAULT '',
  `MAC` char(16) DEFAULT '',
  `VideoLength` int(10) DEFAULT 0 COMMENT '录制时长,单位:s', 
  `VideoTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '录制时间点，格式:YYYY-MM-DD HH:MM:SS',
  `VideoUri` char(128) DEFAULT '',
  `VideoUid` int(10) DEFAULT 0 COMMENT '视频存储服务器中的全局ID',
  PRIMARY KEY (`ID`),
  KEY `Node_key` (`Node`),
  KEY `MAC_key` (`MAC`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='视频存储列表';

DROP TABLE IF EXISTS VideoLength;
CREATE TABLE `VideoLength` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Node` char(16) DEFAULT '',
  `VideoLength` int(10) DEFAULT 0 COMMENT '当前录制时长,单位:s', 
  `VideoCap` int(10) DEFAULT 18000 COMMENT '联动系统允许的录制容量,单位:s', 
  `VideoStorageTime` int(10) DEFAULT 604800 COMMENT '联动系统最大的视频存储时间，超过该时间，则平台需要自动删除掉视频,单位:s，默认一周', 
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Node_key` (`Node`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='视频存储空间占用率';

DROP TABLE IF EXISTS PubRfcardKey;
CREATE TABLE `PubRfcardKey` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MngAccountID` int(11) NOT NULL DEFAULT 0 COMMENT '社区管理员ID，参见Account表',
  `WorkID` int(11) NOT NULL DEFAULT 0 COMMENT '物业id',
  `Code` char(20) NOT NULL,
  `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `OwnerType` tinyint default 0 COMMENT '所属类型 0物业 1快递',
  `Name` varchar(64) DEFAULT '' COMMENT '卡名称',  
  `SchedulerType` tinyint(1) NOT NULL DEFAULT '1' COMMENT '计划的类型,0:单次计划; 1:每日计划；2:周计划',
  `DateFlag` int(11) NOT NULL DEFAULT '0' COMMENT '只有周计划使用,可能的选择如下: {0x01,0x02,0x04,0x08,0x10,0x20,0x40};从周日开始，到周六结束',
  `BeginTime` timestamp DEFAULT NULL COMMENT '单次计划的开始时间点,格式:YYYY-MM-DD HH:MM:SS',
  `EndTime` timestamp DEFAULT NULL COMMENT '单次计划的结束时间点,格式:同上',
  `StartTime` time DEFAULT '00:00:00' COMMENT '日、周计划的开始时间点,格式:HH:MM:SS',
  `StopTime` time DEFAULT '23:59:59' COMMENT '日、周计划的结束时间点,格式:同上',
  PRIMARY KEY (`ID`),
  KEY `WorkID` (`WorkID`),
  KEY `MngAccountID` (`MngAccountID`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8; 

DROP TABLE IF EXISTS PubRfcardKeyList;
CREATE TABLE `PubRfcardKeyList` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MAC` char(20) DEFAULT '' COMMENT '公共设备的mac',
  `KeyID` int(10) NOT NULL DEFAULT 0 COMMENT '公共添加列表id',
  `Relay` tinyint(1) NOT NULL DEFAULT '15' COMMENT '默认全开，即二进制1111',
  PRIMARY KEY (`ID`),
  KEY `KeyID` (`KeyID`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8; 

DROP TABLE IF EXISTS PubPrivateKey;
CREATE TABLE `PubPrivateKey` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MngAccountID` int(10) NOT NULL DEFAULT 0 COMMENT '社区管理员ID，参见Account表',
  `WorkID` int(10) NOT NULL DEFAULT 0 COMMENT '物业id',
  `Code` char(20) NOT NULL,
  `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `OwnerType` tinyint default 0 COMMENT '所属类型 0物业 1快递',
  `Name` varchar(64) DEFAULT '' COMMENT '卡名称',
   `SchedulerType` tinyint(1) NOT NULL DEFAULT '1' COMMENT '计划的类型,0:单次计划; 1:每日计划;2:周计划',
  `DateFlag` int(11) NOT NULL DEFAULT '0' COMMENT '只有周计划使用,可能的选择如下: {0x01,0x02,0x04,0x08,0x10,0x20,0x40};从周日开始，到周六结束',
  `BeginTime` timestamp DEFAULT NULL COMMENT '单次计划的开始时间点,格式:YYYY-MM-DD HH:MM:SS',
  `EndTime` timestamp DEFAULT NULL COMMENT '单次计划的结束时间点,格式:同上',
  `StartTime` time DEFAULT '00:00:00' COMMENT '日、周计划的开始时间点,格式:HH:MM:SS',
  `StopTime` time DEFAULT '23:59:59' COMMENT '日、周计划的结束时间点,格式:同上',
  PRIMARY KEY (`ID`),
  KEY `WorkID` (`WorkID`),
  KEY `MngAccountID` (`MngAccountID`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS PubPrivateKeyList;
CREATE TABLE `PubPrivateKeyList` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MAC` char(20) DEFAULT '' COMMENT '公共设备的mac',
  `KeyID` int(10) NOT NULL DEFAULT 0 COMMENT '公共添加列表id',
  `Relay` tinyint(1) NOT NULL DEFAULT '15' COMMENT '默认全开，即二进制1111',
  PRIMARY KEY (`ID`),
  KEY `KeyID` (`KeyID`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS PubAppTmpKey;
CREATE TABLE `PubAppTmpKey` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MngAccountID` int(10) NOT NULL DEFAULT 0 COMMENT '社区管理员ID，参见Account表',
  `WorkID` int(10) NOT NULL DEFAULT 0 COMMENT '物业id',
  `Code` int(11) NOT NULL,
  `BeginTime` timestamp NULL DEFAULT NULL,
  `EndTime` timestamp NULL DEFAULT NULL,
  `AccessTimes` int(11) DEFAULT 0,
  `AllowedTimes` int(11) DEFAULT NULL,
  `QrCodeUrl` varchar(128) DEFAULT '' comment '临时秘钥二维码图片的完整url',
  `Description` varchar(64) DEFAULT '' COMMENT '描述',
  `SchedulerType` tinyint(1) NOT NULL DEFAULT '0' COMMENT '计划的类型,0:单次计划; 1:每日计划；2:每周计划',
  `DateFlag` int(11) NOT NULL DEFAULT '0' COMMENT '只有每周计划使用,可能的选择如下: {0x01,0x02,0x04,0x08,0x10,0x20,0x40};从周日开始，到周六结束',
  `StartTime` time DEFAULT NULL COMMENT '存储计划的开始时间点,格式:HH:MM:SS',
  `StopTime` time DEFAULT NULL COMMENT '存储计划的结束时间点,格式:同上',  
  `Account` char(64) NOT NULL DEFAULT '' COMMENT '创建者账号',
  `IDNumber` varchar(128) NOT NULL DEFAULT '',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `PersonalAccountID` int(11)unsigned DEFAULT 0 COMMENT '属于哪个用户',
  PRIMARY KEY (`ID`),
  KEY `WorkID` (`WorkID`),
  KEY `MngAccountID` (`MngAccountID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS PubAppTmpKeyList;
CREATE TABLE `PubAppTmpKeyList` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MAC` char(20) DEFAULT '' COMMENT '公共设备的mac',
  `KeyID` int(10) NOT NULL DEFAULT 0 COMMENT '公共添加列表id',
  `Relay` tinyint(1) NOT NULL DEFAULT '15' COMMENT '默认全开，即二进制1111',
  PRIMARY KEY (`ID`),
  KEY `KeyID` (`KeyID`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS ErrorConnect;
CREATE TABLE `ErrorConnect` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `IP` varchar(64) NOT NULL,
  `ConnectTime` datetime default CURRENT_TIMESTAMP,
  `ErrorType` tinyint(1) DEFAULT '0' comment '错误类型 1=未按时上报状态, 2=错误MAC校验码, 3=未登记的设备',
  `MAC` char(20) DEFAULT '',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS AppManualRtsp;
CREATE TABLE `AppManualRtsp` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `CreateTime` datetime default CURRENT_TIMESTAMP,
  `Duration` int(11) DEFAULT 0 comment '监控时长',
  `UserAccount` char(20) DEFAULT '' comment '监控操作人',
  `Account` char(20) DEFAULT '' comment '监控人所在的联动',
  `MAC` char(20) DEFAULT '',
  KEY `Account` (`Account`),
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS PropertyInfo;
CREATE TABLE `PropertyInfo` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `AccountID` int(10) unsigned NOT NULL,
  `FirstName` varchar(64) NOT NULL DEFAULT '',
  `LastName` varchar(64) NOT NULL DEFAULT '',
  UNIQUE KEY `AccountID` (`AccountID`),
  CONSTRAINT `PropertyInfo_ibfk_1` FOREIGN KEY (`AccountID`) REFERENCES `Account` (`ID`),
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS PropertyMngList;
CREATE TABLE `PropertyMngList` (
  `ID` int(10) NOT NULL AUTO_INCREMENT,
  `PropertyID` int(10) unsigned DEFAULT NULL,
  `CommunityID` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`ID`),
  KEY `PropertyID` (`PropertyID`),
  KEY `CommunityID` (`CommunityID`),
  CONSTRAINT `PropertyMngList_ibfk_1` FOREIGN KEY (`PropertyID`) REFERENCES `Account` (`ID`),
  CONSTRAINT `PropertyMngList_ibfk_2` FOREIGN KEY (`CommunityID`) REFERENCES `Account` (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS SystemExtremum;
CREATE TABLE `SystemExtremum` (
`ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
`MaxEntryFee` int(11) NOT NULL DEFAULT '2000',
`MaxMonthlyFee` int(11) NOT NULL DEFAULT '2000',
`MaxFeeApps` int(11) NOT NULL DEFAULT '2000',
`MaxApps` int(11) NOT NULL DEFAULT '20',
PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='收费系统的最大、最小值';
insert into SystemExtremum (`ID`) VALUES(1);

DROP TABLE IF EXISTS ChargePlan;
CREATE TABLE `ChargePlan` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `ManageID` int(11) NOT NULL DEFAULT '0' COMMENT '各级管理员的ID',
  `EntrFee` int(11) NOT NULL DEFAULT '0' COMMENT '激活费用',
  `MonthlyFee` int(11) NOT NULL DEFAULT '0' COMMENT '收费计划',
  `AppsNumber` int(11) NOT NULL DEFAULT '0',
  `AddAppsFee` int(11) NOT NULL DEFAULT '0' COMMENT '额外app每个的费用',  
  PRIMARY KEY (`ID`),
  KEY `MngAccount` (`ManageID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='收费计划';
insert into ChargePlan(ID,ManageID,EntrFee,MonthlyFee,AppsNumber,AddAppsFee) VALUES(1,1,800,400,4,400);

DROP TABLE IF EXISTS ChargePerPlan;
CREATE TABLE `ChargePerPlan` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `EntrFee` int(11) NOT NULL DEFAULT '0',
  `MonthlyFee` int(11) NOT NULL DEFAULT '0',
  `AppsNumber` int(11) NOT NULL DEFAULT '0',
  `AddAppsFee` int(11) NOT NULL DEFAULT '0',
  `LandlineFee` int(11) NOT NULL DEFAULT '0' COMMENT '落地收费',
  `AccountID` int(11)unsigned NOT NULL DEFAULT 1 COMMENT 'AccountID=1为统一的收费方案,其余的是区域管理员特定的收费方案',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
insert into ChargePerPlan(ID,EntrFee,MonthlyFee,AppsNumber,AddAppsFee,LandlineFee,AccountID) VALUES(1,800,0,4,400,200,1);

DROP TABLE IF EXISTS OrderList;
CREATE TABLE `OrderList` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `OrderNumber` char(32) NOT NULL DEFAULT '' COMMENT '订单号',
  `AccountID` int(11) NOT NULL COMMENT '主账号或者物业ID',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `Status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0=支付中 1=支付成功 2=支付失败 3=支付超时 4=异常订单(支付成功，但是部分app无法成功续费，存在前后app被删除的情况) 5=取消 6=系统处理中',
  `TotalPrice` int(11) NOT NULL DEFAULT '0' COMMENT '单位 美元/100 ,此处金额扩大100倍;实际支付金额',
  `Type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '支付类型：1=激活;2=家庭续费;3=额外app购买费用,4=个人落地费用;5=高级功能一次性付费;6=高级功能月费;7=>高级功能差价',
  `Payer` varchar(64) NOT NULL COMMENT '支付人。终端用户和物业的邮箱',
  `PayerType` tinyint(4) NOT NULL DEFAULT '0' COMMENT '支付人类型；0=>终端用户,1=>物业',
  `InstallID` int(10) unsigned NOT NULL COMMENT '社区管理员或者install的ID',
  `AreaManageID` int(10) unsigned NOT NULL COMMENT '区域管理员ID',
  `Months` tinyint(4) NOT NULL DEFAULT '0' COMMENT '购买月数',
  `Days` tinyint(4) NOT NULL DEFAULT '0' COMMENT '购买天数',
  `AppNumbers` tinyint(3) unsigned DEFAULT NULL COMMENT '用于续费时，套餐内app个数',
  `PaypalOrder` varchar(64) NOT NULL DEFAULT '',
  `PaypalEmail` varchar(64) NOT NULL DEFAULT '',
  `Discount` int(11) NOT NULL DEFAULT '100' COMMENT '优惠金额百分比',
  `IsDelete` tinyint(4) NOT NULL DEFAULT '0' COMMENT '用户删除',
  `PayPlatform` tinyint DEFAULT 0 COMMENT 'paypal付款0，stripe付款1',
  `BmOrderNumber` char(20) NOT NULL DEFAULT '' COMMENT '计费系统的订单号',
  `BeforeOncePrice` int(11) NOT NULL DEFAULT '0' COMMENT '单位 美元/100 ,同步计费系统修改订单价格之前的价格',
  `CouponNumber` char(16) DEFAULT NULL COMMENT '同步计费系统代金券',
  `CouponCount` int(11) unsigned DEFAULT '0' COMMENT '单位 美元/100 ,此处金额扩大100倍,同步计费系统代金券金额',
  `FinalPrice` int(11) NOT NULL DEFAULT 0 COMMENT '新版订单最终价格,将旧订单的FinalPrice更新成TotalPrice，后续统一使用FinalPrice',
  `WebHookToken` varchar(64) NOT NULL COMMENT '计费系统刷新当前订单状态的token',
  `PayCode` varchar(10) NOT NULL COMMENT '用户跳转计费系统支付时验证码',  
  PRIMARY KEY (`ID`),
  UNIQUE KEY `OrderNumber` (`OrderNumber`) USING HASH,
  KEY `Account` (`AccountID`),
  KEY `Status` (`Status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


DROP TABLE IF EXISTS OrderEndUserList;
CREATE TABLE `OrderEndUserList` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `OrderID` int(10) unsigned NOT NULL,
  `Type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '1=>激活; 2=>家庭续费(套餐内资费); 3=>额外app购买费用; 4=>家庭续费额外app费用; 5=>落地费',
  `Amount` int(11) NOT NULL COMMENT '商品金额。扩大100倍.续费时家庭套餐内价格，或者额外app价格，均为每个月单价',
  `AppID` int(11) unsigned NOT NULL COMMENT '额外app时，使用appID，家庭套餐使用主账号ID，6.2后可为高级收费的方案FeatureID',
  `Object` varchar(64) NOT NULL DEFAULT '' COMMENT '房间号，用于物业购买显示;或者额外App记录App邮箱',
  `ParentID` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '购买额外app或者续费额外app时主账号id',
  `Discount` tinyint(4) DEFAULT 100,
  `DiscountInfo` varchar(512) DEFAULT '',
  `ActivityUUID` varchar(32) DEFAULT NULL,
  `ChargeData` varchar(1024) DEFAULT NULL,  
  PRIMARY KEY (`ID`),
  KEY `OrderID` (`OrderID`) USING BTREE,
  KEY `AppID` (`AppID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS PersonalBillingInfo;
CREATE TABLE `PersonalBillingInfo` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Account` char(20) NOT NULL DEFAULT '' COMMENT '主账号',
  `BillingTitle` char(128) NOT NULL COMMENT '公司名字或家庭名字',
  `Contactor` char(128) NOT NULL DEFAULT '',
  `Street` char(128) NOT NULL DEFAULT '' COMMENT '街道',
  `City` char(64) NOT NULL DEFAULT '' COMMENT '城市',
  `Postcode` char(32) DEFAULT '' COMMENT '邮编',
  `Country` varchar(8) DEFAULT 'USA',
  `TelePhone` char(32) DEFAULT '',
  `Fax` char(32) DEFAULT '' COMMENT '传真',
  PRIMARY KEY (`ID`),
  KEY `AccountID` (`Account`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS PropertyBillingInfo;
CREATE TABLE `PropertyBillingInfo` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Account` char(32) NOT NULL DEFAULT '' COMMENT '物业管理员账号名称',
  `BillingTitle` char(128) NOT NULL COMMENT '公司名字或家庭名字',
  `Contactor` char(128) NOT NULL DEFAULT '',
  `Street` char(128) NOT NULL DEFAULT '' COMMENT '街道',
  `City` char(64) NOT NULL DEFAULT '' COMMENT '城市',
  `Postcode` char(32) DEFAULT '' COMMENT '邮编',
  `Country` varchar(8) DEFAULT 'USA',
  `TelePhone` char(32) DEFAULT '',
  `Fax` char(32) DEFAULT '' COMMENT '传真',
  PRIMARY KEY (`ID`),
  KEY `AccountID` (`Account`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS PersonalMotion;
CREATE TABLE `PersonalMotion` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MAC` varchar(16) NOT NULL DEFAULT '',
  `SipAccount` char(16) DEFAULT '',
  `Location` varchar(64)  DEFAULT '',
  `MngAccountID` int  DEFAULT 0,
  `MngType` int  DEFAULT 0 comment "1是个人 0社区, 为了下次拆分个人社区",
  `DevType` int  DEFAULT 0 comment "0 不是公共设备 1是公共设备",
  `PicName` char(80) NOT NULL COMMENT '图片的名字,由设备端决定',
  `PicUrl` char(64) DEFAULT '' COMMENT '图片的完整url',
  `SPicUrl` char(64) DEFAULT '' COMMENT '小图的完整url',
  `CaptureTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `Status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已读的标志,0:未读; 1:已读',
  `Node` char(32) DEFAULT '' comment '标识由那个联动截图,用于公共设备',
  PRIMARY KEY (ID,CaptureTime),
  KEY `Node` (`Node`),
  KEY `MngAccountID` (`MngAccountID`),
  KEY `CaptureTime` (`CaptureTime`),
  INDEX `MAC_PicName` (`MAC`,`PicName`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8
PARTITION BY RANGE(MONTH(CaptureTime))(
PARTITION P1 VALUES LESS THAN (2),
PARTITION P2 VALUES LESS THAN (3),
PARTITION P3 VALUES LESS THAN (4),
PARTITION P4 VALUES LESS THAN (5),
PARTITION P5 VALUES LESS THAN (6),
PARTITION P6 VALUES LESS THAN (7),
PARTITION P7 VALUES LESS THAN (8),
PARTITION P8 VALUES LESS THAN (9),
PARTITION P9 VALUES LESS THAN (10),
PARTITION P10 VALUES LESS THAN (11),
PARTITION P11 VALUES LESS THAN (12),
PARTITION P12 VALUES LESS THAN (13)
);


DROP TABLE IF EXISTS MonthSliceTableName;
CREATE TABLE `MonthSliceTableName` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `Name` varchar(64) NOT NULL DEFAULT '' COMMENT '表名',
  `YearMonth` int  DEFAULT 0 COMMENT '年月比如201908,可用于排序',
  `StartID` int UNSIGNED DEFAULT 0 COMMENT '表数据开始ID',
  `EndID` int UNSIGNED  DEFAULT 0 COMMENT '表数据结束ID',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS MonthCaptureCounted;
CREATE TABLE `MonthCaptureCounted` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `MonthSliceTableNameID` int(10) unsigned DEFAULT 0 COMMENT '对应月份的表名ID',
  `MngAccountID` int(10) unsigned DEFAULT 0,
  `DoorCount` int(10) unsigned DEFAULT 0 COMMENT '所有开门的记录值',
  `CallDoorCapture` int(10) unsigned DEFAULT 0 COMMENT '通话开门截图0',
  `Tmpkey` int(10) unsigned DEFAULT 0,
  `Privkey` int(10) unsigned DEFAULT 0,
  `Rfkey` int(10) unsigned DEFAULT 0,
  `Face` int(10) unsigned DEFAULT 0,
  `RemoteApp` int(10) unsigned DEFAULT 0,
  `CallApp` int(10) unsigned DEFAULT 0,
  `CallIndoor` int(10) unsigned DEFAULT 0,
  `NFC` int(10) unsigned DEFAULT 0,
  `BLE` int(10) unsigned DEFAULT 0,
  `CallCapture` int(10) unsigned DEFAULT 0 COMMENT '通话不开门的截图103',
  `AppCapture` int(10) unsigned DEFAULT 0 COMMENT 'app手动截图102',
  PRIMARY KEY (`ID`),
  INDEX `MonthSliceTableNameID_MngID` (`MngAccountID`,`MonthSliceTableNameID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS InstallerBillingInfo;
CREATE TABLE `InstallerBillingInfo` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Account` char(64) NOT NULL DEFAULT '' COMMENT '管理员账号名称',
  `BillingTitle` char(128) NOT NULL COMMENT '公司名字或家庭名字',
  `Contactor` char(128) NOT NULL DEFAULT '',
  `Street` char(128) NOT NULL DEFAULT '' COMMENT '街道',
  `City` char(64) NOT NULL DEFAULT '' COMMENT '城市',
  `Postcode` char(32) DEFAULT '' COMMENT '邮编',
  `Country` varchar(8) DEFAULT 'USA',
  `TelePhone` char(32) DEFAULT '',
  `Fax` char(32) DEFAULT '' COMMENT '传真',
  `Email` varchar(64) NOT NULL,
  PRIMARY KEY (`ID`),
  UNIQUE `Account` (`Account`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS FaceModel;
CREATE TABLE `FaceModel` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MAC` varchar(16) NOT NULL DEFAULT '',
  `ModelName` varchar(256) NOT NULL comment '模型的名字,由设备端决定',
  `ModelUrl` varchar(128) DEFAULT '' comment '模型的完整url',
  `UploadTime` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`ID`),
  INDEX `MAC_ModelName` (`MAC`,`ModelName`),
  INDEX `UploadTime` (`UploadTime`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;


DROP TABLE IF EXISTS LocalSipTransaction;
CREATE TABLE `LocalSipTransaction` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Sip` char(16) DEFAULT '' comment '操作哪个sip，用于后面消费并发处理',
  `CreateTime` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `MessageStatus` tinyint DEFAULT 0 comment '0未处理 1成功处理 2错误消息需人工介入',
  `Message` varchar(1024) DEFAULT '',
  `HandleTime` TIMESTAMP DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=UTF8;

DROP TABLE IF EXISTS PersonalRfcardKeyList;
CREATE TABLE `PersonalRfcardKeyList` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MAC` char(20) DEFAULT '' COMMENT '设备的mac',
  `KeyID` int(10) NOT NULL DEFAULT '0' COMMENT 'PersonalRfcardKey表id',
  `Relay` tinyint(1) NOT NULL DEFAULT '15' COMMENT '默认全开，即二进制1111',
  PRIMARY KEY (`ID`),
  KEY `KeyID` (`KeyID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8; 

DROP TABLE IF EXISTS PersonalPrivateKeyList;
CREATE TABLE `PersonalPrivateKeyList` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `MAC` char(20) DEFAULT '' COMMENT '设备的mac',
  `KeyID` int(10) NOT NULL DEFAULT '0' COMMENT 'PersonalPrivateKey表id',
  `Relay` tinyint(1) NOT NULL DEFAULT '15' COMMENT '默认全开，即二进制1111',
  PRIMARY KEY (`ID`),
  KEY `KeyID` (`KeyID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8; 	 
	 
	 
DROP TABLE IF EXISTS DevOfflineLog;
CREATE TABLE `DevOfflineLog` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `MngAccountID` int(10) NOT NULL DEFAULT '0' COMMENT '社区ID',
  `Quantity` int(10) NOT NULL DEFAULT 0 COMMENT '掉线设备数量',
  `MacList` varchar(256) NOT NULL DEFAULT '' COMMENT '掉线设备列表',
  `OfflineTime` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `MngAccountID` (`MngAccountID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS ApiClient;
CREATE TABLE `ApiClient` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `ClientId` char(32) NOT NULL,
  `ClientSecret` char(64) NOT NULL,
  `ClientName` varchar(32) NOT NULL DEFAULT 'Akuvox' COMMENT '第三方APP代号',
  `Trust` tinyint(1) NOT NULL DEFAULT '0' COMMENT '信任等级 1-直接返回密码',
PRIMARY KEY (`ID`),
UNIQUE KEY `ClientId` (`ClientId`)
)ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS ThirdParty;
CREATE TABLE `ThirdParty` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `AccountID` int(10) unsigned NOT NULL COMMENT 'PM账户ID',
  `CommunityID` int(10) unsigned NOT NULL COMMENT 'PM下的社区ID',
  `AccessName` char(32) NOT NULL COMMENT '接入名称:GsFace',
  `GsfaceLoginApi` char(64) NOT NULL COMMENT 'GsFace给设备调用的地址',
  PRIMARY KEY (`ID`)
) DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS PubDevMngList;
CREATE TABLE `PubDevMngList` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `DevicesID` int(10) unsigned NOT NULL COMMENT 'Devices表ID',
  `UnitID` int(10) unsigned NOT NULL COMMENT '单元表ID',
  PRIMARY KEY (`ID`),
  KEY `DevicesID` (`DevicesID`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS FaceMng;
CREATE TABLE `FaceMng` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `MngAccountID` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '对于社区用户是社区管理员ID或者对于个人用户是installerID',
  `UnitID` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '全局的单元unit ID, 单住户为空',
  `Node` char(16) DEFAULT '' COMMENT '家庭主账号uid',
  `PersonalAccountID` int(10) unsigned NOT NULL COMMENT 'PersonalAccount表ID',
  `FileName` varchar(128) DEFAULT '' COMMENT '原始文件名',
  `FaceUrl` char(64) DEFAULT '' COMMENT '文件路径后缀,文件名为加密后的md5值',
  `CreateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`ID`),
  KEY `MngAccountID` (`MngAccountID`),
  KEY `UnitID` (`UnitID`),
  KEY `Node` (Node),
  KEY `PersonalAccountID` (`PersonalAccountID`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS Temperature;
CREATE TABLE `Temperature` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `MAC` char(12) NOT NULL DEFAULT '',
  `MngAccountID` int(11) unsigned DEFAULT '0',
  `PicName` char(80) NOT NULL COMMENT '图片的名字,由设备端决定',
  `PicUrl` char(64) DEFAULT '' COMMENT '图片的完整url',
  `SPicUrl` char(64) DEFAULT '' COMMENT '小图的完整url',
  `Fahrenheit` char(8) DEFAULT '' COMMENT '体温',
  `CaptureTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `Status` tinyint(1) NOT NULL COMMENT '0:Normal; 1:Abnormal，2：Low',
  PRIMARY KEY (`ID`),
  KEY `CaptureTime` (`CaptureTime`),
  KEY `MngAccountID` (`MngAccountID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS MessageTemplate;
CREATE TABLE `MessageTemplate` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Name` varchar(64) DEFAULT '' COMMENT 'message 名称',
  `Title` varchar(256) DEFAULT '' COMMENT 'message 标题',
  `Message` varchar(512) NOT NULL COMMENT 'message 内容',
  `CreateTime` timestamp NULL DEFAULT NULL,
  `AccountID` int(11) unsigned  NOT NULL COMMENT 'message创建者的ID',
  PRIMARY KEY (`ID`),
  KEY `AccountID_key` (`AccountID`)
) ENGINE=InnoDB AUTO_INCREMENT=660 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS VerificationCode;
CREATE TABLE `VerificationCode` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `Account` char(32) NOT NULL COMMENT '用户账号',
  `Code` char(6) NOT NULL DEFAULT '' COMMENT '验证码',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '生成时间',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Account` (`Account`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS DtPbxServer;
CREATE TABLE `DtPbxServer`(
   `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
   `Distributor` char(64) NOT NULL DEFAULT '' COMMENT '区域管理员账号',
   `PbxIp` char(20) NOT NULL DEFAULT '',
   `PbxIpv6` char(40) NOT NULL DEFAULT '',
    PRIMARY KEY (`ID`),
	KEY `Distributor_key` (`Distributor`)
) ENGINE=InnoDB AUTO_INCREMENT=660 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS AppCallDND;
CREATE TABLE `AppCallDND` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `Account` char(32) DEFAULT "" COMMENT 'PersonalAccount',
  `Status` tinyint(1) DEFAULT 0 COMMENT '是否开启',
  `StartTime` int unsigned DEFAULT 0 COMMENT '开始时间,从0点到开始时间的分钟数,最大值:24*60',
  `EndTime` int unsigned DEFAULT 0 COMMENT '结束时间,从0点到结束时间的分钟数,最大值:24*60',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Account` (`Account`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


DROP TABLE IF EXISTS PmExportLog;
CREATE TABLE `PmExportLog` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `PmID` int(10) unsigned  DEFAULT 0 COMMENT 'PM的Account ID',
  `CommunityID` int(10) unsigned  DEFAULT 0 COMMENT '小区ID',
  `TraceID` VARCHAR (128) NOT NULL COMMENT '导出日志的唯一标识',
  `LogType` tinyint(1) DEFAULT 0 COMMENT '1=DoorLog 2=Capture 3=TemperatureCapture',
  `ExportType` tinyint(1) DEFAULT 0 COMMENT '0=all 1=only_excel',
  `LastTime` timestamp DEFAULT NULL COMMENT '导出的截至时间',
  `CreateTime` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '导出的时间,更新也要变化',
  `DownloadUrl` VARCHAR (512) DEFAULT '' COMMENT '下载的url',
  PRIMARY KEY (`ID`),
  KEY `TraceID` (`TraceID`),
  KEY `PmID_CommunityID_Log_Export_time` (`PmID`,`CommunityID`,`LogType`,`ExportType`,`LastTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS AppEnduserFeedback;
CREATE TABLE `AppEnduserFeedback` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `Account` int(10) unsigned  DEFAULT 0 COMMENT '用户账号',
  `ContactEmail` VARCHAR (64) DEFAULT '' COMMENT '联系的email',
  `Content` VARCHAR (2048) DEFAULT '' COMMENT '内容',
  `FileList` VARCHAR (512) DEFAULT '' COMMENT '文件列表;分号分割(文件存储默认路径/var/www/upload/freeback)',
  `CreateTime` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS DevDeliveryMsg;
CREATE TABLE `DevDeliveryMsg` (
  `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `Account` char(32) NOT NULL COMMENT '用户账号',
  `DeliveryNumber` int(10) unsigned NOT NULL COMMENT '快递的个数',
  `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `Status` tinyint(2) unsigned NOT NULL DEFAULT '0' COMMENT '0未读 1已读',
  PRIMARY KEY (`ID`),
  KEY `Account` (`Account`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS UserActivityUsed;
CREATE TABLE `UserActivityUsed` (
	`ID` INT (11) unsigned NOT NULL AUTO_INCREMENT,
	`PersonalAccountID` INT (11) unsigned NOT NULL,
	`Activity` varchar(32) NOT NULL,
	PRIMARY KEY (`ID`),
	KEY `PersonalAccountID` (`PersonalAccountID`),
	KEY `Activity` (`Activity`)
) ENGINE = INNODB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8;

DROP TABLE IF EXISTS AuditLog;
CREATE TABLE `AuditLog` (
	`ID` INT (11) UNSIGNED NOT NULL AUTO_INCREMENT,
	`IP` VARCHAR (64) NOT NULL,
	`Operator` VARCHAR (64) NOT NULL COMMENT '操作者',
	`CreateTime` DATETIME NOT NULL COMMENT '创建时间',
	`Type` TINYINT (4) NOT NULL COMMENT '日志类型',
	`KeyInfo` VARCHAR (1024) NOT NULL COMMENT '日志替换Key，使用json数组',
  `OperaType` ENUM('SuperManager','Distributor','Installer','PM','SingleMaster','SingleMember','CommunityMaster','CommunityMember') NOT NULL,
	PRIMARY KEY (`ID`),
    KEY `Operator` (`Operator`),
	KEY `Type_CreateTime` (`Type`, `CreateTime`)
) ENGINE = INNODB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8;

DROP TABLE IF EXISTS AuditLogInfo;
CREATE TABLE `AuditLogInfo` (
	`ID` INT (11) UNSIGNED NOT NULL AUTO_INCREMENT,
	`AuditLogID` INT (11) UNSIGNED NOT NULL,
	`Type` smallint(5) NOT NULL COMMENT '日志类型',
	`KeyInfo` VARCHAR (1024) NOT NULL COMMENT '日志替换Key，使用json数组',
	PRIMARY KEY (`ID`)
) ENGINE = INNODB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8;

DROP TABLE IF EXISTS PbxRedirectInfo;
CREATE TABLE `PbxRedirectInfo`(
   `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
   `CommunitID` int(10) unsigned  NOT NULL DEFAULT 0 COMMENT '社区:小区ID. 社区用这个自动匹配',
   `Node` char(32)   NOT NULL DEFAULT "" COMMENT '个人：Node 单住户用这个自动匹配',
   `PbxID` int(10) unsigned NOT NULL,
    PRIMARY KEY (`ID`),
	KEY `CommunitID_key` (`CommunitID`,`PbxID`),
	KEY `Node_key` (`Node`,`PbxID`)
) ENGINE=InnoDB AUTO_INCREMENT=660 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS PbxServerList;
CREATE TABLE `PbxServerList`(
   `ID` int(10) unsigned NOT NULL AUTO_INCREMENT,
   `PbxIp` char(20) NOT NULL DEFAULT '',
   `PbxIpv6` char(40) NOT NULL DEFAULT '',
   `PbxDomain` char(64) NOT NULL  DEFAULT '' COMMENT 'pbx域名',
    PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS DistributorInfo;
CREATE TABLE `DistributorInfo` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Account` char(64) NOT NULL,
  `IsEncryptPin` tinyint(4) NOT NULL default 0 COMMENT 'pin 是否加密,0:不加密, 1:加密',
  `IsVillaMonitor` tinyint(4) NOT NULL DEFAULT '0' COMMENT 'Ins单住户是否显示绑定Monitor模块 0：off, 1:on',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Account` (`Account`) 
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS AccountAccess;
CREATE TABLE `AccountAccess` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Account` char(32) NOT NULL,
  `AccessGroupID` int(10) unsigned NOT NULL,
  PRIMARY KEY (`ID`),
  KEY `AccessGroupID` (`AccessGroupID`),
  KEY `Account` (`Account`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS UserAccessGroup;
CREATE TABLE `UserAccessGroup` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Account` char(32) NOT NULL COMMENT '用户账号',
  `SchedulerType` tinyint(4) unsigned NOT NULL COMMENT '计划的类型,0:单次计划; 1:每日计划；2:周计划',
  `DateFlag` int(11) NOT NULL DEFAULT '0' COMMENT '只有周计划使用,可能的选择如下: {0x01,0x02,0x04,0x08,0x10,0x20,0x40};从周日开始，到周六结束',
  `BeginTime` datetime NOT NULL DEFAULT '2021-01-01 00:00:00' COMMENT '单次计划的开始时间点,格式:YYYY-MM-DD HH:MM:SS',
  `EndTime` datetime NOT NULL DEFAULT '2299-01-01 00:00:00' COMMENT '单次计划的结束时间点,格式:同上',
  `StartTime` time DEFAULT '00:00:00' COMMENT '日、周计划的开始时间点,格式:HH:MM:SS',
  `StopTime` time DEFAULT '23:59:59' COMMENT '日、周计划的结束时间点,格式:同上',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Account` (`Account`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;


DROP TABLE IF EXISTS UserAccessGroupDevice;
CREATE TABLE `UserAccessGroupDevice` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `UserAccessGroupID` int(10) unsigned NOT NULL,
  `MAC` char(12) NOT NULL COMMENT '设备的mac',
  `Relay` tinyint(1) NOT NULL DEFAULT '15' COMMENT '默认全开，即二进制1111',
  PRIMARY KEY (`ID`),
  KEY `UserAccessGroup` (`UserAccessGroupID`),
  KEY `MAC` (`MAC`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS Delivery;
CREATE TABLE `Delivery` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Name` char(64) NOT NULL,
  `CommunityID` int(10) unsigned NOT NULL,
  `PinCode` char(20) DEFAULT NULL,
  `CardCode` char(20) DEFAULT NULL,
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `Version` int(10) unsigned DEFAULT 123456 COMMENT '用户数据版本号',
  PRIMARY KEY (`ID`),
  KEY `Comm_Pin` (`CommunityID`, `PinCode`),
  KEY `Comm_Rf` (`CommunityID`, `CardCode`),  
  KEY `Name` (`Name`) 
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS DeliveryAccess;
CREATE TABLE `DeliveryAccess` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `DeliveryID` int(10) unsigned NOT NULL,
  `AccessGroupID` int(10) unsigned NOT NULL,
  PRIMARY KEY (`ID`),
  KEY `DeliveryID` (`DeliveryID`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS Staff;
CREATE TABLE `Staff` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Name` char(64) NOT NULL,
  `CommunityID` int(10) unsigned NOT NULL,
  `CardCode` char(20) DEFAULT NULL,
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `Version` int(10) unsigned DEFAULT 123456 COMMENT '用户数据版本号',
  PRIMARY KEY (`ID`),
  KEY `Comm_Rf` (`CommunityID`, `CardCode`),
  KEY `Name` (`Name`) 
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS StaffAccess;
CREATE TABLE `StaffAccess` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `StaffID` int(10) unsigned NOT NULL,
  `AccessGroupID` int(10) unsigned NOT NULL,
  PRIMARY KEY (`ID`),
  KEY `StaffID` (`StaffID`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS AccessGroup;
CREATE TABLE `AccessGroup` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Name` varchar(64) NOT NULL,
  `CreateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `CommunityID` int(10) unsigned NOT NULL DEFAULT 0,
  `UnitID` int(10) unsigned NOT NULL DEFAULT 0 COMMENT '表示某个楼栋的系统权限组，对应CommunityUnit ID,0为普通权限组',
  `SchedulerType` tinyint(4) unsigned NOT NULL COMMENT '计划的类型,0:单次计划; 1:每日计划；2:周计划',
  `DateFlag` int(11) NOT NULL DEFAULT '0' COMMENT '只有周计划使用,可能的选择如下: {0x01,0x02,0x04,0x08,0x10,0x20,0x40};从周日开始，到周六结束',
  `BeginTime` datetime NOT NULL DEFAULT '2021-01-01 00:00:00' COMMENT '单次计划的开始时间点,格式:YYYY-MM-DD HH:MM:SS',
  `EndTime` datetime NOT NULL DEFAULT '2299-01-01 00:00:00' COMMENT '单次计划的结束时间点,格式:同上',
  `StartTime` time DEFAULT '00:00:00' COMMENT '日、周计划的开始时间点,格式:HH:MM:SS',
  `StopTime` time DEFAULT '23:59:59' COMMENT '日、周计划的结束时间点,格式:同上',
  PRIMARY KEY (`ID`),
  KEY `CommunityID_Name` (`CommunityID`,`Name`),
  KEY `UnitID` (`UnitID`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;
 
DROP TABLE IF EXISTS AccessGroupDevice;
CREATE TABLE `AccessGroupDevice` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `AccessGroupID` int(10) unsigned NOT NULL,
  `MAC` char(12) NOT NULL COMMENT '设备的mac',
  `Relay` tinyint(1) NOT NULL DEFAULT '15' COMMENT '默认全开，即二进制1111',
  PRIMARY KEY (`ID`),
  KEY `AccessGroupID` (`AccessGroupID`),
  KEY `MAC` (`MAC`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS AuditLog;
CREATE TABLE `AuditLog` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `IP` varchar(64) NOT NULL,
  `Operator` varchar(64) NOT NULL COMMENT '操作者',
  `Distributor` varchar(64) DEFAULT '',
  `Installer` varchar(64) DEFAULT '',
  `Community` varchar(64) DEFAULT '',
  `CreateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `Type` tinyint(4) NOT NULL COMMENT '日志类型',
  `KeyInfo` varchar(1024) NOT NULL COMMENT '日志替换Key，使用json数组',
  `OperaType` enum('SuperManager','Distributor','Installer','PM','SingleMaster','SingleMember','CommunityMaster','CommunityMember') NOT NULL,
  PRIMARY KEY (`ID`),
  KEY `Operator` (`Operator`),
  KEY `Type_CreateTime` (`Type`,`CreateTime`)

) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS AuditLogInfo;
CREATE TABLE `AuditLogInfo` (
  `ID` int (11) unsigned NOT NULL AUTO_INCREMENT,
  `AuditLogID` int (11) unsigned NOT NULL,
  `Type` smallint(5) NOT NULL COMMENT '日志类型; 有数十种类型,详见6.1研发设计文档',
  `KeyInfo` VARCHAR (1024) NOT NULL COMMENT '日志替换Key，使用json数组',
  PRIMARY KEY (`ID`)
) ENGINE = INNODB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8;


DROP TABLE IF EXISTS Message;
CREATE TABLE `Message` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Title` varchar(256) NOT NULL comment 'message 标题',
  `Content` varchar(512) NOT NULL comment '当Type=0时:message的具体内容 当Type=1时:快递消息中的number 当Type=2时:TmpKey消息中的使用者',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `AccountID` int(11) NOT NULL DEFAULT 0 comment 'message创建者(管理员)的ID',
  `Type` tinyint(2) unsigned NOT NULL DEFAULT 0 COMMENT '消息类型 0=message 1=快递msg 2=TmpKey使用msg',
  `Status` tinyint(2) unsigned NOT NULL DEFAULT 0 COMMENT 'Type=0时有效message创建者发送状态 0 :还未发送 1:发送完成',
  `NickNames` text NOT NULL DEFAULT '' COMMENT '用于管理员显示;不同联动系统主账号昵称之间以分号分割:akuvox;xm',
  `ReceiverType` tinyint(2) unsigned NOT NULL DEFAULT 0 COMMENT '用于管理员显示;0:室内机+APP; 1:室内机; 2:app',
  PRIMARY KEY (`ID`),
  KEY `AccountID_key` (`AccountID`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;


DROP TABLE IF EXISTS MessageAccountList;
CREATE TABLE `MessageAccountList` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `ClientType` tinyint(2) unsigned NOT NULL DEFAULT 2 COMMENT ' 1:发送给当前account的设备; 2:发送给当前account的app;',
  `Account` char(32) NOT NULL comment '发送给用户帐号',
  `Status` tinyint(2) unsigned NOT NULL DEFAULT 0 COMMENT '0 :未读 1:已读',
  `MessageID` int(11) NOT NULL,
  PRIMARY KEY (`ID`),
  KEY `MessageID_key` (`MessageID`),
  KEY `Account_key` (`Account`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8;


DROP TABLE IF EXISTS CommPerRfKey;
CREATE TABLE `CommPerRfKey` (
  `ID` int(11) NOT NULL AUTO_INCREMENT, 
  `Account` char(32) NOT NULL COMMENT '用户账号,包括主从',  
  `Code` char(20) DEFAULT '',
  `CommunityID` int(10) unsigned NOT NULL DEFAULT 0,
  `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `CommunityID_Code` (`CommunityID`, `Code`),
  KEY `Account` (`Account`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


DROP TABLE IF EXISTS CommPerPrivateKey;
CREATE TABLE `CommPerPrivateKey` (
  `ID` int(11) NOT NULL AUTO_INCREMENT, 
  `Account` char(32) NOT NULL COMMENT '用户账号,包括主从',  
  `Code` char(20) DEFAULT '',
  `CommunityID` int(10) unsigned NOT NULL DEFAULT 0,
  `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `Special` tinyint(1) unsigned DEFAULT 0 COMMENT '是否是创建账号时候创建的key',
  PRIMARY KEY (`ID`),
  KEY `CommunityID_Code` (`CommunityID`, `Code`),
  KEY `Account` (`Account`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS DevUpdateUserLog;
CREATE TABLE `DevUpdateUserLog` (
  `ID` int(11) NOT NULL AUTO_INCREMENT, 
  `MAC` char(12) NOT NULL COMMENT '设备的mac',
  `Accounts` varchar(4096) NOT NULL comment '下载用户列表 D开头是快递人员的数据库id, S开头是物业',
  `TraceID` bigint(10) unsigned NOT NULL DEFAULT 0,
  `FilePath` varchar(256) DEFAULT '' COMMENT '文件存储路径', 
  `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `MAC_TRACE` (`MAC`, `TraceID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS MacPool;
CREATE TABLE `MacPool`( 
	`ID` int(11) unsigned NOT NULL AUTO_INCREMENT, 
	`Mac` char(12) NOT NULL, 
	`AuthCode` char(32) NOT NULL, 
	`CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`ID`),
    UNIQUE KEY `Mac` (`Mac`)
 )ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT charSET=utf8; 

DROP TABLE IF EXISTS ToBeDealOrder;
CREATE TABLE `ToBeDealOrder` (
    `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `OrderNumber` char(32) NOT NULL,
    `Status` tinyint(1) NOT NULL default 0 COMMENT '[0=进行中，2=完成，3=异常]',
    `CreateTime` datetime,
    PRIMARY KEY (`ID`),
    UNIQUE KEY `OrderNumber` (`OrderNumber`)
) ENGINE = INNODB AUTO_INCREMENT = 1 DEFAULT charSET = utf8;


DROP TABLE IF EXISTS ToBeDealSmartHome;
CREATE TABLE `ToBeDealSmartHome`(
    `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `DataKey` varchar(33) NOT NULL COMMENT '关键字',
    `Type` tinyint(1) NOT NULL COMMENT '类型[添加dis,添加installer，添加小区，删除dis,删除installer，删除小区，添加家庭主，添加家庭从，添加设备，删除家庭主，删除家庭从，删除设备，重置施工信息]',
    `Info` varchar(2048) default '' COMMENT '额外信息',
    `Status` tinyint(1) NOT NULL default 0 COMMENT '[进行中，完成，家居云失败, 对讲云数据不存在, 家居云成功，但操作数据库异常]',
    `CreateTime` datetime,
    `HandleTime` datetime default null,
    `NextHandleTime` datetime default null,
    `HandleTimes` tinyint(1) default 0  COMMENT '处理次数',
    PRIMARY KEY (`ID`),
    KEY Status_NextHandleTime_HandleTimes (`Status`, `NextHandleTime`,`HandleTimes`)
)ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS SmartHomeManageMap;
CREATE TABLE `SmartHomeManageMap`(
    `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `Account` varchar(32) NOT NULL COMMENT '管理员账号',
    `SmartHomeUUID` char(33) NOT NULL,
    PRIMARY KEY (`ID`),
    UNIQUE KEY Account (`Account`)
)ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS SmartHomeUserMap;
CREATE TABLE `SmartHomeUserMap`(
    `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `Account` varchar(32) NOT NULL COMMENT '终端用户账号',
    `SmartHomeUUID` char(33) NOT NULL,
    `HomeID` char(33) NOT NULL,
    PRIMARY KEY (`ID`),
    UNIQUE KEY Account (`Account`)
)ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS SmartHomeUserCache;
CREATE TABLE `SmartHomeUserCache`(
    `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `Account` varchar(32) NOT NULL COMMENT '终端用户账号',
    `LayoutName` varchar(128) NOT NULL,
    `CreateTime` datetime,
    PRIMARY KEY (`ID`),
    UNIQUE KEY Account (`Account`)
)ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS SmartHomeDeviceMap;
CREATE TABLE `SmartHomeDeviceMap`(
    `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `MAC` char(12) NOT NULL,
    `SmartHomeUUID` char(33) NOT NULL,
    PRIMARY KEY (`ID`),
    UNIQUE KEY MAC (`MAC`)
)ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;


DROP TABLE IF EXISTS FeaturePlan;
CREATE TABLE `FeaturePlan`(
    `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `Name` varchar(256) NOT NULL,
    `Item` smallint(8) COMMENT '绑定的高级功能,按位标识:1=快递件,2=pin是否可用,3=QrCode是否可用,4=限制家庭成员,5=室内机方案',
    `Type` tinyint(1) NOT NULL COMMENT 'Feature类型 0：社区 1：办公',
    PRIMARY KEY (`ID`) ,
    KEY `Name` (`Name`)
)ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT charSET=utf8;
insert into FeaturePlan(ID, Name, Item, Type) values(1,'Basic',16, 0);
insert into FeaturePlan(ID, Name, Item, Type) values(2,'Premium',15, 0);

DROP TABLE IF EXISTS ManageFeature;
CREATE TABLE `ManageFeature`(
    `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `AccountID` int(11) unsigned NOT NULL COMMENT 'dis、小区账号ID',
    `FeatureID` int(11) NOT NULL COMMENT '0表示6.2前特殊方案，非0表示正常的feature',
    `FeeUUID` char(32) COMMENT '计费系统中高级功能计划的UUID，小区绑定为null',
    PRIMARY KEY (`ID`) ,
    KEY `AccountID` (`AccountID`),
    KEY `FeatureID` (`FeatureID`)
)ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT charSET=utf8;

DROP TABLE IF EXISTS DevicesSpecial;
CREATE TABLE `DevicesSpecial`(
    `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `Account` varchar(32) NOT NULL COMMENT '社区和个人主账号',
    `MAC` char(12) NOT NULL COMMENT 'Mac地址',
    PRIMARY KEY (`ID`),
    KEY `Account` (`Account`),
	KEY `MAC` (`MAC`)
)ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT charSET=utf8;

DROP TABLE IF EXISTS LockOrder;
CREATE TABLE `LockOrder` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `OrderNumber` char(32) NOT NULL,
  `DataKey` char(32) NOT NULL comment '用户或小区账号',
  `Type` tinyint(1) unsigned NOT NULL comment '1=激活用户，2=续费，3=购买App，4=落地，5=高级功能一次性，6=高级功能月费，7=高级功能差价',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `DataKey_Type` (`DataKey`,`Type`),
  KEY `OrderNumber` (`OrderNumber`)
) ENGINE=InnoDB DEFAULT charSET=utf8; 
 
DROP TABLE IF EXISTS APPSpecial; 
CREATE TABLE `APPSpecial`(
    `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `Node` char(32) NOT NULL COMMENT '家庭标识',
    `Account` char(32) NOT NULL COMMENT '从账号标识',
    `CreateTime` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间', 
    PRIMARY KEY (`ID`),
    KEY `Account` (`Account`),
 KEY `Node` (`Node`)
)ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8; 

DROP TABLE IF EXISTS AwsRedirect; 
CREATE TABLE `AwsRedirect`( 
	`ID` int(16) unsigned NOT NULL AUTO_INCREMENT, 
	`Account` char(64) NOT NULL COMMENT '调度粒度为单住户时为InstallerAccount, 调度粒度为社区时为CommunityAccount', 
	`AccountID` int(10) unsigned NOT NULL COMMENT '调度粒度为单住户时为InstallerID, 调度粒度为社区时为CommunityID', 
PRIMARY KEY (`ID`),
UNIQUE KEY `Account` (`Account`),
UNIQUE KEY `AccountID` (`AccountID`)
 )ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;  

DROP TABLE IF EXISTS ImportTask; 
CREATE TABLE `ImportTask` (
    `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `UUID` char(36) NOT NULL COMMENT '两位服务器号 + -uuid()去除横杠,例如na-45efccef469111ec8fe900163e047e78',
    `CreateTime` datetime NOT NULL,
    `UpdateTime` datetime NOT NULL,
    `UpdateUser` char(8) NOT NULL DEFAULT '',
    `Account` char(64) NOT NULL COMMENT 'Account表Account',
    `Status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '0=进行中;1=已完成;2=巡检任务修改的超时任务',
    `Type` tinyint(4) NOT NULL DEFAULT '0' COMMENT '类型:0=导入小区',
PRIMARY KEY (`ID`),
KEY `Account_Type` (`Account`, `Type`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;  

DROP TABLE IF EXISTS PendingRegUser;
CREATE TABLE `PendingRegUser` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Account` char(64) NOT NULL,
  `MAC` char(32) NOT NULL  COMMENT '设备MAC地址',
  `Token` varchar(64) DEFAULT '',
  `Status` tinyint(1) DEFAULT 0 COMMENT '注册状态,0:未注册;1:已注册',  
  PRIMARY KEY (`ID`),
  UNIQUE KEY `Account` (`Account`),
  KEY `MAC` (`MAC`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS SipPrefix;
CREATE TABLE `SipPrefix` (
    `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `UUID` char(36) NOT NULL COMMENT '唯一标识',
    `CreateTime` datetime NOT NULL,
    `UpdateTime` datetime NOT NULL,
    `UpdateUser` varchar(32) DEFAULT '',
    `AccountUUID` char(36) NOT NULL COMMENT 'Account表uuid',
    `SipPrefix` int(11) NOT NULL DEFAULT '0' COMMENT '对于每一级管理员，记录到本级sip规则的前缀',
    `Flag` tinyint(4) NOT NULL DEFAULT '0' COMMENT '按位标识:1=sip是否使用，2=sip群组是否使用',
PRIMARY KEY (`ID`),
UNIQUE KEY `SipPrefix` (`SipPrefix`),
KEY `AccountUUID` (`AccountUUID`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

DROP TABLE IF EXISTS SipPrefixUnique;
CREATE TABLE `SipPrefixUnique` (
    `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `SipPrefix` int(11) NOT NULL DEFAULT '0',
PRIMARY KEY (`ID`),
UNIQUE KEY `SipPrefix` (`SipPrefix`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;


/* 删除之前遗留的问题, 远程访问数据库*/
use mysql;
delete from user where user='jabberd2';
delete from user where user='bm_user';
delete from user where user='root' and host='%';
update user set Reload_priv='Y' where host='localhost' and user='root';


use AKCS;
INSERT INTO SystemSetting(ID, SchemeVer) VALUES ('1', 6212);









use AKCS;

DROP TABLE IF EXISTS DevicesShadow;
CREATE TABLE `DevicesShadow` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `MAC` char(20) NOT NULL DEFAULT '' COMMENT '设备的mac',
  `PrivatekeyPath` varchar(128)  NOT NULL DEFAULT '',
  `RfidPath` varchar(128)  NOT NULL DEFAULT '',
  `ConfigPath` varchar(128)  NOT NULL DEFAULT '',
  `ContactPath` varchar(128)  NOT NULL DEFAULT '',
  `FacePath` varchar(128)  NOT NULL DEFAULT '',
  `SchedulePath` varchar(128)  NOT NULL DEFAULT '',
  `UserMetaPath` varchar(128)  NOT NULL DEFAULT '',
  PRIMARY KEY (`ID`),
  UNIQUE KEY `MAC` (`MAC`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

alter table PersonalAccount add UNIQUE KEY `UUID` (`UUID`);
alter table PersonalAccount add KEY `RoomID` (`RoomID`);
alter table PersonalAccount alter RoomID set default 0;
alter table PersonalAccount add ParentUUID char(36) NOT NULL;
alter table Account add ParentUUID char(36) NOT NULL;

alter table Devices add `SecurityRelay` varchar(256) NOT NULL DEFAULT '' COMMENT '格式: 开门按键,doorname,是否显示在home,是否显示在talking,是否启用;多个relay以分号分隔';
alter table PersonalDevices add `SecurityRelay` varchar(256) NOT NULL DEFAULT '' COMMENT '格式: 开门按键,doorname,是否显示在home,是否显示在talking,是否启用;多个relay以分号分隔';
alter table PubRfcardKeyList add `SecurityRelay` tinyint(1) NOT NULL DEFAULT '0' COMMENT '默认不开';
alter table PersonalRfcardKeyList add `SecurityRelay` tinyint(1) NOT NULL DEFAULT '0' COMMENT '默认不开';
alter table PubAppTmpKeyList add `SecurityRelay` tinyint(1) NOT NULL DEFAULT '0' COMMENT '默认不开';
alter table PersonalAppTmpKeyList add `SecurityRelay` tinyint(1) NOT NULL DEFAULT '0' COMMENT '默认不开';
alter table PubPrivateKeyList add `SecurityRelay` tinyint(1) NOT NULL DEFAULT '0' COMMENT '默认不开';
alter table PersonalPrivateKeyList add `SecurityRelay` tinyint(1) NOT NULL DEFAULT '0' COMMENT '默认不开';
alter table UserAccessGroupDevice add `SecurityRelay` tinyint(1) NOT NULL DEFAULT '0' COMMENT '默认不开';
alter table AccessGroupDevice add `SecurityRelay` tinyint(1) NOT NULL DEFAULT '0' COMMENT '默认不开';

alter table PersonalCapture add KEY `MAC_PicName` (`MAC`,`CaptureType`);
alter table PersonalCapture add KEY `MngAccountID_PicName` (`MngAccountID`,`CaptureType`);

alter table PersonalAccount modify `Role` tinyint(1) DEFAULT 0 COMMENT '10=个人用户主账号;11=个人从账号;20=社区用户主账号;21=社区从账号;;30=office主;31=office admin;40=PM';
DROP TABLE IF EXISTS PmAccountMap;
CREATE TABLE `PmAccountMap` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `AccountUUID` char(36) NOT NULL DEFAULT '' COMMENT 'Account表中PM帐号的UUID',
  `PersonalAccountUUID` char(36) NOT NULL DEFAULT '' COMMENT 'PersonalAccount表中对应子账号的UUID',
  `PersonalAccount` varchar(64) NOT NULL DEFAULT '' COMMENT '对应子账号',
  `ProjectUUID` char(36) NOT NULL DEFAULT '' COMMENT '子帐号所属社区UUID',
  `AppStatus` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'PM APP开启状态，0-关闭 1-开启',
  `CreateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `AccountUUID` (`AccountUUID`),
  KEY `ProjectUUID` (`ProjectUUID`),
  KEY `PersonalAccountUUID` (`PersonalAccountUUID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='pm app帐号映射表';

alter table PersonalAccountCnf add `WebRelay` tinyint(1) NOT NULL DEFAULT 0 COMMENT '支持0-50';

alter table OrderList add `IsBatch` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否为批量支付。0=否；1=是';
alter table OrderEndUserList modify column `Type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1=>激活; 2=>家庭续费(套餐内资费); 3=>额外app购买费用; 4=>家庭续费额外app费用; 5=>落地费;8=按日期续费;9=pm app激活;10=pm app续费';
alter table OrderEndUserList add column `ProjectUUID` char(36) NOT NULL COMMENT '小区UUID';
alter table OrderEndUserList add column `ProjectName` char(36) NOT NULL COMMENT '小区或者办公的location';

alter table FaceMng add column `FaceMD5` char(32) NOT NULL DEFAULT '' COMMENT '文件md5值';

alter table PersonalAppTmpKey drop KEY `TmpKey`;
alter table PersonalAppTmpKey add KEY `Key_Mng_Unit` (`MngAccountID`,`TmpKey`,`UnitID`);

DROP TABLE IF EXISTS OfflineResendLog;
CREATE TABLE `OfflineResendLog` (
  `ID` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `Mac` char(16) NOT NULL DEFAULT '' COMMENT '设备mac',
  `FileName` varchar(256) NOT NULL DEFAULT '' COMMENT '离线文件明',
  `MessageSeq` char(16) NOT NULL DEFAULT '' COMMENT '设备发送的seq',
  `CreateTime` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `UpdateTime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`ID`),
  KEY `Mac_FILE` (`Mac`, FileName )
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='离线重传的日志记录';

update SystemSetting set SchemeVer=6401;































