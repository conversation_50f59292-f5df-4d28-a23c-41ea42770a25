CMAKE_MINIMUM_REQUIRED(VERSION 2.8)

project (config C CXX)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

SET(DBINTERFACE_QUERY_DIR "${CMAKE_CURRENT_SOURCE_DIR}")
SET(CSBASE_SOURCE_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../csbase)
SET(DBINTERFACE_FILES_OUTPUT_DIR ${CMAKE_CURRENT_BINARY_DIR})
include(${CMAKE_CURRENT_SOURCE_DIR}/../csbase/common_scripts/dbinterface_files_list.cmake)

SET(INC_DIR ${CMAKE_CURRENT_SOURCE_DIR}/include)
SET(SRC_DIR ${CMAKE_CURRENT_SOURCE_DIR}/src)
LINK_DIRECTORIES(${CSBASE_SOURCE_DIR} ${CSBASE_SOURCE_DIR}/thirdlib ${CSBASE_SOURCE_DIR}/redis/hiredis ${CSBASE_SOURCE_DIR}/evpp/lib)

AUX_SOURCE_DIRECTORY(${SRC_DIR} SRC_LIST_CONFIG)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Common/Basic SRC_LIST_CONFIG_BASIC)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Common/Cstring SRC_LIST_CONFIG_CSTRING)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Common/Utility SRC_LIST_CONFIG_Utility)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Control SRC_LIST_CONFIG_CONTROL)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Model SRC_LIST_CONFIG_MODEL)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Model/DataAnalysis SRC_LIST_CONFIG_DATAANALYSIS)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Model/Maintenance SRC_LIST_CONFIG_MAINTENANCE)
AUX_SOURCE_DIRECTORY(${SRC_DIR}/Office SRC_LIST_CONFIG_OFFICE)

AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/Rldb SRC_LIST_BASE_RLDB)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/redis SRC_LIST_BASE_REDIS)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/jsoncpp0.5/src/json SRC_LIST_BASE_JSON)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/etcd SRC_LIST_BASE_ETCD)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/protobuf SRC_LIST_BASE_PROTOBUF)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/beanstalk-client SRC_LIST_BASE_BEANSTALK)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/encrypt SRC_LIST_BASE_ENCRYPT)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/metrics SRC_LIST_BASE_METRICS)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/kafka SRC_LIST_BASE_KAFKA)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/http SRC_LIST_BASE_HTTP)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/Tinyxml SRC_LIST_BASE_TINYXML)
AUX_SOURCE_DIRECTORY(${CSBASE_SOURCE_DIR}/loop SRC_LIST_BASE_LOOP)


SET(BASE_LIST_INC ${CSBASE_SOURCE_DIR} ${CSBASE_SOURCE_DIR}/mysql ${CSBASE_SOURCE_DIR}/evpp ${CSBASE_SOURCE_DIR}/etcd ${CSBASE_SOURCE_DIR}/Rldb
                  ${CSBASE_SOURCE_DIR}/encrypt ${CSBASE_SOURCE_DIR}/protobuf ${CSBASE_SOURCE_DIR}/beanstalk-client 
                  ${CSBASE_SOURCE_DIR}/jsoncpp0.5/include ${CSBASE_SOURCE_DIR}/Tinyxml
                  ${CSBASE_SOURCE_DIR}/redis ${CSBASE_SOURCE_DIR}/redis/hiredis 
                  ${CSBASE_SOURCE_DIR}/fdfs_client/fdfsclient ${CSBASE_SOURCE_DIR}/fdfs_client/libfdfscomm
                  ${CSBASE_SOURCE_DIR}/grpc ${CSBASE_SOURCE_DIR}/grpc/gens ${CSBASE_SOURCE_DIR}/gid ${CSBASE_SOURCE_DIR}/uploader ${CSBASE_SOURCE_DIR}/kafka)

ADD_DEFINITIONS(-std=gnu++11 -g2 -Werror -Wno-unused-parameter -Wno-deprecated)

include_directories(${BASE_LIST_INC} ${INC_DIR}/mysql ${SRC_DIR}/Common/Utility ${SRC_DIR}/Common/Basic ${SRC_DIR}/Common/Cstring
                    ${SRC_DIR}/Common/Tinyxml ${SRC_DIR}/Common/Socket ${SRC_DIR}/Control
                    ${SRC_DIR}/Model ${SRC_DIR}/Model/DataAnalysis ${SRC_DIR}/Model/Maintenance ${SRC_DIR}/Office
                    ${SRC_DIR} ${SRC_DIR}/include /usr/local/boost/include /usr/local/protobuf/include /usr/local/grpc/include 
                    ${SRC_DIR}/uploader/fdfs_uploader.h
                    ${CSBASE_SOURCE_DIR}/metrics)

add_executable(csconfig ${SRC_LIST_CONFIG} ${SRC_LIST_CONFIG_BASIC} ${SRC_LIST_CONFIG_CSTRING} 
                 ${SRC_LIST_CONFIG_Utility}   ${SRC_LIST_BASE_TINYXML} 
                ${SRC_LIST_CONFIG_CONTROL} ${SRC_LIST_CONFIG_MODEL} ${SRC_LIST_CONFIG_DATAANALYSIS} 
                ${SRC_LIST_CONFIG_MAINTENANCE} ${SRC_LIST_CONFIG_OFFICE} ${SRC_LIST_BASE_RLDB} 
                ${SRC_LIST_BASE_REDIS} ${SRC_LIST_BASE_JSON} ${SRC_LIST_BASE_ETCD} 
                ${SRC_LIST_BASE_PROTOBUF} ${SRC_LIST_BASE_BEANSTALK} ${SRC_LIST_BASE_ENCRYPT} 
                ${SRC_LIST_BASE_METRICS} ${prefixed_file_list} ${SRC_LIST_BASE_HTTP} ${SRC_LIST_BASE_KAFKA} ${SRC_LIST_BASE_LOOP}
                ${CSBASE_SOURCE_DIR}/uploader/fdfs_uploader.cpp
                ${CSBASE_SOURCE_DIR}/consistent-hash/QueueConsistentHash.cpp)


SET(EXECUTABLE_OUTPUT_PATH ${CMAKE_CURRENT_SOURCE_DIR}/release/bin)

set_target_properties(csconfig PROPERTIES LINK_FLAGS  "-Wl,--rpath=/usr/local/akcs/csconfig/lib")

target_link_libraries(csconfig pthread mysqlclient iconv event glog ssl crypto evpp boost_system cpprest etcd-cpp-api
                      protobuf -Bstatic hiredis csbase fdfsclient fastcommon gpr grpc grpc++ cppkafka rdkafka rdkafka++)
