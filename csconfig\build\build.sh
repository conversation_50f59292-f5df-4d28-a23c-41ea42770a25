#!/bin/bash

WORK_DIR=`pwd`
AKCS_SRC_ROOT=${WORK_DIR}/../../
AKCS_SRC_BIN_DIR=${AKCS_SRC_ROOT}/csconfig
AKCS_SRC_CSBASE=${AKCS_SRC_ROOT}/csbase
set -euo pipefail

#在源码包的同一路径下生成安装包
AKCS_PACKAGE_NAME=akcs_csconfig_packeg
AKCS_PACKAGE_ROOT=${AKCS_SRC_ROOT}/${AKCS_PACKAGE_NAME}

build() {
    #先清理上一次的安装包
    if [ -d $AKCS_PACKAGE_ROOT ]
    then
        rm -rf $AKCS_PACKAGE_ROOT
    fi
	mkdir -p $AKCS_PACKAGE_ROOT
	
    #create protobuf
    cd $AKCS_SRC_ROOT/csbp/proto || exit 1
    bash create_proto.sh 
	
    #build csbase
	cd $AKCS_SRC_CSBASE || exit 1
    cmake ./
    make -j2
    if [ $? -eq 0 ]; then
        echo "make csbase successed";
    else
        echo "make csbase failed";
        exit 1;
    fi
	
    cd $AKCS_SRC_BIN_DIR/src/Model/DataAnalysis/ || exit 1
    bash generate_func.sh

    cd $AKCS_SRC_BIN_DIR || exit 1
    cmake ./
    make -j$(nproc)
    if [ $? -eq 0 ]; then  #即使有告警,也不算是错误
        echo "make successed";
    else
        echo "make failed";
        exit 1;
    fi
	
	cp -rf $AKCS_SRC_BIN_DIR/release/* $AKCS_PACKAGE_ROOT
	cp -rf $AKCS_SRC_CSBASE/common_scripts/* $AKCS_PACKAGE_ROOT/scripts
    cp -rf $AKCS_SRC_CSBASE/common_conf/* $AKCS_PACKAGE_ROOT/conf    
	cp -rf $AKCS_SRC_CSBASE/thirdlib/libetcd-cpp-api.so  $AKCS_PACKAGE_ROOT/lib
    cp -rf $AKCS_SRC_CSBASE/thirdlib/libevpp.so  $AKCS_PACKAGE_ROOT/lib
	cp -rf $AKCS_SRC_CSBASE/thirdlib/libcppkafka.so  $AKCS_PACKAGE_ROOT/lib
    cp -rf $AKCS_SRC_CSBASE/thirdlib/librdkafka.so  $AKCS_PACKAGE_ROOT/lib
    cp -rf $AKCS_SRC_CSBASE/thirdlib/librdkafka++.so  $AKCS_PACKAGE_ROOT/lib  

    #个组件均编译成功
    echo "-----------------------all build successful-------------------------"

    #打成tar包
    cd ${AKCS_SRC_ROOT} || exit 1
    rm -rf ${AKCS_PACKAGE_NAME}.tar.gz
    tar zcvf ${AKCS_PACKAGE_NAME}.tar.gz ${AKCS_PACKAGE_NAME}
    echo "${AKCS_PACKAGE_ROOT}.tar.gz is created successful."
}

clean() {
	cd $AKCS_SRC_CSBASE || exit 1
	cmake ./
    make clean

	cd $AKCS_SRC_BIN_DIR || exit 1
    cmake ./
	make clean
}

print_help() {
	echo "Usage: "
	echo "  $0 clean --- clean application, eg : $0 clean "
    echo "  $0 build ---  build application, eg : $0 build "
}

case $1 in
	clean)
    	if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi
		echo "clean all build..."
		clean
		;;
	build)
		if [ $# != 1 ]; then  #命令行传参不是1个
			echo $#
			print_help
			exit
		fi

		echo "build..."
		build
		;;
	*)
		print_help
		;;
esac
