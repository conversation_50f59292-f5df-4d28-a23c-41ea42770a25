#cspbx conf
cspbx_ip=*************
cspbx_port=5070

#db conf
db_ip=127.0.0.1
db_username=dbuser01
db_database=AKCS
db_port=3306

#nsq
nsq_delpic_topic=delpic

#etcd conf,etcd是集群,通过配置文件指定
etcd_srv_net=http://***********:8507;http://***********:18507;

#nsq route conf
nsq_route_topic=ak_route

#beanstalkd
beanstalkd_ip=127.0.0.1
beanstalkd_backup_ip=
#根据消费不同csadapt队列来做集群,csadapt有几个队列一定要都有分配到，避免消息丢失
#弃用
beanstalkd_tube=web_to_adapt0;web_to_adapt1

#remote sshd domain
remote_config_domain=remoteconfig.akuvox.com

#rtsp_domain
vrtsp_server_domain=

#ftp ip
ftp_ip=*************
#web ip
web_ip=*************


#9=ccloud 8=scloud 1=ecloud 3=ucloud
system_area_type=5
#空房间不写主账号联系人，小区列表id(id1,id2) 
#目前只有美国有特殊处理,注意前后的逗号要有
community_ids=,5037,5041,5532,

#AWS
is_aws=0
aws_mysql_ip=*************

#30秒执行内只能执行2次
repeated_userdetail_timeout=30
#120秒执行内只能执行2次
repeated_ipchange_timeout=120

#是否将设备影子存储到fdfs服务器 0时存在本地/var/www/download
is_store_fdfs=1

#ipchange 过滤mac,示例:0C1100123456,0C1100123457
ip_change_filter=

#userinfo 过滤mac,示例:0C1100123456,0C1100123457
user_info_filter=

#过滤某社区的配置刷新,社区id间用用逗号隔开（举例：,1, 或 ,1,2,3, ）,刷太慢时用于临时过滤(需重启生效),待不再过滤后,需手动刷新社区配置下发
#手动刷新社区配置方法 cd /var/www/html/apache-v3.0/notify/tools && php GenerateConfigFile.php
mng_id_filter=
#只对国内生效
ip_change_mng_id_filter=,2793,

#检测大社区刷配置时间的阈值
check_big_project_handle_time=60

#web过来的重复消息过滤延时时间
repeated_web_timeout=30

#生成配置的线程个数  不包括转发(转发硬编码两个) csadapt.conf也要同步修改
write_thread_number=4

#写文件线程数(写文件有专门的队列处理)
write_file_number=3

config_server_port=
fdfs_config_addr=
config_server_domain=
config_server_domain_gray_percentage=20

log_encrypt=0
log_trace=1

enable_db_confusion_cache=1

#生成设备配置的工作线程数(总的社区是串行处理，生成具体设备配置时候并行)
write_dev_work_thread_num=4

#只对国内的金碧文华小区生效，用4g网络卡不稳定 ip会一直变
ip_change_mng_id_filter=,2793,

kafka_broker_ip=
notify_csconfig_topic=notify-csconfig
notify_csconfig_topic_group=notify-csconfig_group
notify_csconfig_topic_thread=3

notify_csconfig_user_detail_topic=notify-csconfig-user-detail
notify_csconfig_user_detail_topic_group=notify-csconfig-user-detail_group
notify_csconfig_user_detail_topic_thread=3

#写配置消息堆积多少条之后 开启批量读取
write_config_heap_up_num=100
write_config_batch_read_num=100
write_config_community_overflow_threshold=20
