#!/usr/bin/env python3
"""beanstalkc - A beanstalkd Client Library for Python"""

import logging
import socket
import sys
from time import sleep


__license__ = '''
Copyright (C) 2008-2015 <PERSON>

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
'''

__version__ = '0.4.1'


DEFAULT_HOST = 'localhost'
DEFAULT_PORT = 11300
DEFAULT_PRIORITY = 2 ** 31
DEFAULT_TTR = 120


PY3 = sys.version_info[0] > 2
if PY3:
    b = lambda x: isinstance(x, bytes) and x or bytes(x, 'us-ascii')
    s = lambda x: x.decode('us-ascii')
else:
    b = lambda x: x
    s = lambda x: x


class BeanstalkcException(Exception): pass
class UnexpectedResponse(BeanstalkcException): pass
class CommandFailed(BeanstalkcException): pass
class DeadlineSoon(BeanstalkcException): pass

class SocketError(BeanstalkcException):
    @staticmethod
    def wrap(wrapped_function, *args, **kwargs):
        try:
            return wrapped_function(*args, **kwargs)
        except socket.error:
            err = sys.exc_info()[1]
            raise SocketError(err)


class Connection(object):
    def __init__(self, host=DEFAULT_HOST, port=DEFAULT_PORT, parse_yaml=True,
                 connect_timeout=socket.getdefaulttimeout(), encoding=sys.getdefaultencoding()):
        if parse_yaml is True:
            try:
                parse_yaml = __import__('yaml').safe_load
            except ImportError:
                logging.error('Failed to load PyYAML, will not parse YAML')
                parse_yaml = False
        self._connect_timeout = connect_timeout
        self._parse_yaml = parse_yaml or (lambda x: x)
        self._encoding = encoding
        self.host = host
        self.port = port
        self.connect()

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        self.close()

    def connect(self):
        """Connect to beanstalkd server."""
        self._socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self._socket.settimeout(self._connect_timeout)
        SocketError.wrap(self._socket.connect, (self.host, self.port))
        self._socket.settimeout(None)
        self._socket_file = self._socket.makefile('rb')

    def close(self):
        """Close connection to server."""
        try:
            self._socket.sendall(b('quit\r\n'))
        except socket.error:
            pass
        try:
            self._socket.close()
        except socket.error:
            pass

    def reconnect(self):
        """Re-connect to server."""
        self.close()
        self.connect()

    def _interact(self, command, expected_ok, expected_err=[]):
        SocketError.wrap(self._socket.sendall, b(command))
        status, results = self._read_response()
        if status in expected_ok:
            return results
        elif status in expected_err:
            raise CommandFailed(command.split()[0], status, results)
        else:
            raise UnexpectedResponse(command.split()[0], status, results)

    def _read_response(self):
        line = SocketError.wrap(self._socket_file.readline)
        if not line:
            raise SocketError()
        response = s(line).split()
        return response[0], response[1:]

    def _read_body(self, size):
        body = SocketError.wrap(self._socket_file.read, size)
        SocketError.wrap(self._socket_file.read, 2)  # trailing crlf
        if size > 0 and not body:
            raise SocketError()
        if PY3 and self._encoding:
            body = body.decode(self._encoding)
        return body

    def _interact_value(self, command, expected_ok, expected_err=[]):
        return self._interact(command, expected_ok, expected_err)[0]

    def _interact_job(self, command, expected_ok, expected_err, reserved=True):
        jid, size = self._interact(command, expected_ok, expected_err)
        body = self._read_body(int(size))
        return Job(self, int(jid), body, reserved)

    def _interact_yaml(self, command, expected_ok, expected_err=[]):
        size, = self._interact(command, expected_ok, expected_err)
        body = self._read_body(int(size))
        return self._parse_yaml(body)

    def _interact_peek(self, command):
        try:
            return self._interact_job(command, ['FOUND'], ['NOT_FOUND'], False)
        except CommandFailed:
            return None

    # -- public interface --

    def put(self, body, priority=DEFAULT_PRIORITY, delay=0, ttr=DEFAULT_TTR):
        """Put a job into the current tube. Returns job id."""
        if not isinstance(body, str) and not isinstance(body, bytes):
            raise ValueError('Job body must be a str or bytes instance')
        if PY3 and isinstance(body, str):
            if not self._encoding:
                raise ValueError('Job body must be a bytes instance when no encoding is specified')
            body = bytes(body, self._encoding)
        jid = self._interact_value(b('put %d %d %d %d\r\n' % (priority, delay, ttr, len(body))) +
                                   body + b('\r\n'),
                                   ['INSERTED'],
                                   ['JOB_TOO_BIG', 'BURIED', 'DRAINING'])
        return int(jid)

    def reserve(self, timeout=None):
        """Reserve a job from one of the watched tubes, with optional timeout
        in seconds. Returns a Job object, or None if the request times out."""
        if timeout is not None:
            command = 'reserve-with-timeout %d\r\n' % timeout
        else:
            command = 'reserve\r\n'
        try:
            return self._interact_job(command,
                                      ['RESERVED'],
                                      ['DEADLINE_SOON', 'TIMED_OUT'])
        except CommandFailed:
            exc = sys.exc_info()[1]
            _, status, results = exc.args
            if status == 'TIMED_OUT':
                return None
            elif status == 'DEADLINE_SOON':
                raise DeadlineSoon(results)

    def kick(self, bound=1):
        """Kick at most bound jobs into the ready queue."""
        return int(self._interact_value('kick %d\r\n' % bound, ['KICKED']))

    def kick_job(self, jid):
        """Kick a specific job into the ready queue."""
        self._interact('kick-job %d\r\n' % jid, ['KICKED'], ['NOT_FOUND'])

    def peek(self, jid):
        """Peek at a job. Returns a Job, or None."""
        return self._interact_peek('peek %d\r\n' % jid)

    def peek_ready(self):
        """Peek at next ready job. Returns a Job, or None."""
        return self._interact_peek('peek-ready\r\n')

    def peek_delayed(self):
        """Peek at next delayed job. Returns a Job, or None."""
        return self._interact_peek('peek-delayed\r\n')

    def peek_buried(self):
        """Peek at next buried job. Returns a Job, or None."""
        return self._interact_peek('peek-buried\r\n')

    def tubes(self):
        """Return a list of all existing tubes."""
        return self._interact_yaml('list-tubes\r\n', ['OK'])

    def using(self):
        """Return the tube currently being used."""
        return self._interact_value('list-tube-used\r\n', ['USING'])

    def use(self, name):
        """Use a given tube."""
        return self._interact_value('use %s\r\n' % name, ['USING'])

    def watching(self):
        """Return a list of all tubes being watched."""
        return self._interact_yaml('list-tubes-watched\r\n', ['OK'])

    def watch(self, name):
        """Watch a given tube."""
        return int(self._interact_value('watch %s\r\n' % name, ['WATCHING']))

    def ignore(self, name):
        """Stop watching a given tube."""
        try:
            return int(self._interact_value('ignore %s\r\n' % name,
                                            ['WATCHING'],
                                            ['NOT_IGNORED']))
        except CommandFailed:
            return 1

    def stats(self):
        """Return a dict of beanstalkd statistics."""
        return self._interact_yaml('stats\r\n', ['OK'])

    def stats_tube(self, name):
        """Return a dict of stats about a given tube."""
        return self._interact_yaml('stats-tube %s\r\n' % name,
                                   ['OK'],
                                   ['NOT_FOUND'])

    def pause_tube(self, name, delay):
        """Pause a tube for a given delay time, in seconds."""
        self._interact('pause-tube %s %d\r\n' % (name, delay),
                       ['PAUSED'],
                       ['NOT_FOUND'])

    # -- job interactors --

    def delete(self, jid):
        """Delete a job, by job id."""
        self._interact('delete %d\r\n' % jid, ['DELETED'], ['NOT_FOUND'])

    def release(self, jid, priority=DEFAULT_PRIORITY, delay=0):
        """Release a reserved job back into the ready queue."""
        self._interact('release %d %d %d\r\n' % (jid, priority, delay),
                       ['RELEASED', 'BURIED'],
                       ['NOT_FOUND'])

    def bury(self, jid, priority=DEFAULT_PRIORITY):
        """Bury a job, by job id."""
        self._interact('bury %d %d\r\n' % (jid, priority),
                       ['BURIED'],
                       ['NOT_FOUND'])

    def touch(self, jid):
        """Touch a job, by job id, requesting more time to work on a reserved
        job before it expires."""
        self._interact('touch %d\r\n' % jid, ['TOUCHED'], ['NOT_FOUND'])

    def stats_job(self, jid):
        """Return a dict of stats about a job, by job id."""
        return self._interact_yaml('stats-job %d\r\n' % jid,
                                   ['OK'],
                                   ['NOT_FOUND'])


class Job(object):
    def __init__(self, conn, jid, body, reserved=True):
        self.conn = conn
        self.jid = jid
        self.body = body
        self.reserved = reserved

    def _priority(self):
        stats = self.stats()
        if isinstance(stats, dict):
            return stats['pri']
        return DEFAULT_PRIORITY

    # -- public interface --

    def delete(self):
        """Delete this job."""
        self.conn.delete(self.jid)
        self.reserved = False

    def release(self, priority=None, delay=0):
        """Release this job back into the ready queue."""
        if self.reserved:
            self.conn.release(self.jid, priority or self._priority(), delay)
            self.reserved = False

    def bury(self, priority=None):
        """Bury this job."""
        if self.reserved:
            self.conn.bury(self.jid, priority or self._priority())
            self.reserved = False

    def kick(self):
        """Kick this job alive."""
        self.conn.kick_job(self.jid)

    def touch(self):
        """Touch this reserved job, requesting more time to work on it before
        it expires."""
        if self.reserved:
            self.conn.touch(self.jid)

    def stats(self):
        """Return a dict of stats about this job."""
        return self.conn.stats_job(self.jid)


class BeanstalkClient(object):
    def __init__(self, host=DEFAULT_HOST, port=DEFAULT_PORT, parse_yaml=False):
        self.conn = Connection(host=host, port=port, parse_yaml=parse_yaml)

    def use(self, tube):
        self.conn.use(tube)
        return self.conn
    
    def watch(self, tube):
        self.conn.watch(tube)
        return self.conn

    def api(self, cmd, *params):
        if 'stats' == cmd:
            print(self.conn.stats())
        elif 'list-tubes' == cmd:
            print(self.conn.tubes())
        elif 'list-tube-used' == cmd:
            print(self.conn.using())
        elif 'list-tubes-watched' == cmd:
            print(self.conn.watching())
        elif 'stats-tube' == cmd:
            try:
                tube = params[0]
            except Exception:
                print('请指定一个 tube')
                sys.exit(1)

            print(self.conn.stats_tube(params[0]))
        elif 'put' == cmd:
            try:
                tube = params[0]
            except Exception:
                print('请指定一个 tube')
                sys.exit(1)

            try:
                body = params[1]
            except Exception:
                print('请输入消息的内容（必须是字符串）')
                sys.exit(1)

            self.use(tube).put(body)
        elif 'reserve' == cmd:
            try:
                tube = params[0]
            except Exception:
                print('请指定一个 tube')
                sys.exit(1)

            try:
                timeout = params[1]
            except Exception:
                timeout = 0
            for count in range(100):
                job = self.watch(tube).reserve(timeout)
                print(job.body)
                job.delete()
        else:
            print('不支持的 API 操作')


if __name__ == '__main__':
    args_len = len(sys.argv)
    if args_len < 4:
        sys.exit()

    host_ip = sys.argv[1]
    host_port = int(sys.argv[2])
    cmd = sys.argv[3]
    cli = BeanstalkClient(host=host_ip, port=host_port)
    cli.api(cmd, *sys.argv[4:])


# if __name__ == '__main__':
#     args_len = len(sys.argv)
#     if args_len < 4:
#         sys.exit()

#     server_inner_ip = sys.argv[1]
#     beanstalk_port = int(sys.argv[2])
#     api_name = sys.argv[3]

#     # 一些操作指令（比如 stats）的结果默认是 YAML格式，如果不把 YAML 解析为 json，则不需要额外安装 PyYAML 模块
#     beanstalkc = Connection(host=server_inner_ip, port=beanstalk_port, parse_yaml=False)

#     if 'stats' == api_name:
#         print(beanstalkc.stats())
#     elif 'list-tubes' == api_name:
#         print(beanstalkc.tubes())
#     elif 'list-tube-used' == api_name:
#         print(beanstalkc.using())
#     elif 'list-tubes-watched' == api_name:
#         print(beanstalkc.watching())
#     elif 'stats-tube' == api_name:
#         try:
#             tube_name = sys.argv[4]
#         except Exception:
#             tube_name = 'default'

#         print(beanstalkc.stats_tube(tube_name))
#     elif 'put' == api_name:
#         try:
#             tube_name = sys.argv[4]
#         except Exception:
#             print('请输入要添加任务的 tube 和 消息内容')
#             sys.exit(1)

#         print('使用的 tube 是：')
#         print(beanstalkc.use(tube_name))
#         print(beanstalkc.watching())
#         # try:
#         #     body = sys.argv[5]
#         # except Exception:
#         #     print('在 tube: %s 中添加任务失败: 请输入消息内容' % tube_name)
#         #     sys.exit(1)

#         # jid = beanstalkc.use(tube_name)
#         # print('在 tube: %s 中添加任务成功，job_id: %d' % (tube_name, jid))
#     elif 'reserve' == api_name:
#         try:
#             tube_name = sys.argv[4]
#         except Exception:
#             print('请输入取出任务的 tube')
#             sys.exit(1)

#         print('默认使用的 tube 是：')
#         print(beanstalkc.watching())
        
#         job = beanstalkc.watch(tube_name)
#         print(beanstalkc.watching())
#         print(type(job))
        

#     else:
#         print('不支持的 API 操作')

#     beanstalkc.close()



