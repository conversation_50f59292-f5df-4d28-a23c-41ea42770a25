#!/bin/bash
ACMD="$1"
csconfig_BIN='/usr/local/akcs/csconfig/bin/csconfig'

if [ "$#" -ne 1 ];then
     echo "Usage: sh `basename $0` start|stop|restart|status"
     exit
fi

start_csconfig()
{
	nohup $csconfig_BIN >/dev/null 2>&1 &
    echo "Start csconfig successful"
    if [ -z "`ps -fe|grep "csconfigrun.sh" |grep -v grep`" ];then
        nohup bash /usr/local/akcs/csconfig/scripts/csconfigrun.sh >/dev/null 2>&1 &
    fi
}
stop_csconfig()
{
    echo "Begin to stop csconfigrun.sh"
    csconfigrunid=`ps aux | grep -w csconfigrun.sh | grep -v grep | awk '{ print $(2) }'`
    if [ -n "${csconfigrunid}" ];then
	    echo "csconfigrun.sh is running at ${csconfigrunid}, will kill it first."
	    kill -9 ${csconfigrunid}
    fi
    echo "Begin to stop csconfig"
    kill -9 `pidof csconfig`
    sleep 2
    echo "Stop csconfig successful"
}

case $ACMD in
  start)
     start_csconfig
    ;;
  stop)
     stop_csconfig
    ;;
  restart)
    stop_csconfig
    sleep 1
    start_csconfig
    ;;
  status)
    if [ -f /var/run/csconfig.pid ];then
        pid=`cat /var/run/csconfig.pid`
        if [ $pid"x" = "x" ];then
           #pid里面的文件是空的
           pid="xxxxxxxxxx"
        fi
    else
        #重启之后没有这个pid文件
        pid="xxxxxxxxxx"
    fi
    cnt=`ls /proc/$pid | wc -l`
    if [ "$cnt" -eq "0" ]
    then
        echo "\033[1;31;05m csconfig is stop!!!\033[0m"
        exit 1
    else
        echo "\033[0;32m csconfig is running \033[0m"
        exit 0
    fi
    ;;
  *)
    echo "Usage: sh `basename $0` start|stop|restart|status"
    ;;
esac
exit

