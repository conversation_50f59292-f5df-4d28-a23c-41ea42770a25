#!/bin/bash

# Set timezone
export TZ=Asia/Shanghai

HOST_IP=/etc/ip
SERVER_INNER_IP=`cat $HOST_IP | grep -w SERVER_INNER_IP | awk -F'=' '{ print $2 }'`
BACKEND_CONF=/etc/app_backend_install.conf
BEANSTALKD_IP=`cat $BACKEND_CONF | grep -w BEANSTALKD_IP | awk -F'=' '{ print $2 }'`
BEANSTALKD_PORT=8519
KAFKA_CONSUMER_CONF="/usr/local/akcs/csconfig/conf/csconfig.conf"
KAFKA_IP=$(grep -w kafka_broker_ip $KAFKA_CONSUMER_CONF | awk -F'=' '{ print $2 }')

# Define functions
cmd_usage() {
  echo "usage: $0 list-tubes"
  echo "       $0 stats-tube [tube]"
  echo "       $0 echo"
  echo "       $0 getMacUser <mac> <dstfile>"
  echo "       $0 getDeviceConfig <mac> <Config/Privatekey/Rfid/Contact/Face/Schedule/UserMeta/UserAll>"
  echo "       $0 getDeviceRtspUrl <mac>"
  echo "       $0 getMngFilter"
  echo "       $0 setMngFilter <filter>"
  echo "       $0 getDataConfusionCacheCount"
  echo "       $0 refreshProjectData <mng_id>"
  echo "       $0 metrics"
  echo "  Kafka Commands:";
  echo "       $0 kafka-get <partition> <offset>   # 获取指定分区和偏移量的消息";  
  echo "       $0 kafka-list                       # 列出消费者组的描述信息";
  exit 0
}

getToken() {
    mac="$1"
    configtype="$2"
    key='Akuvox2023'
    raw_str="$mac:$configtype:$key"
    md5=$(echo -n "$raw_str" | md5sum | awk '{print $1}')
    echo "$md5"
}

beanstalk_consumer() {
  py_script=/usr/local/akcs/csconfig/scripts/beanstalk_consumer.py
  host_ip=$BEANSTALKD_IP
  host_port=$BEANSTALKD_PORT
  cmd=$1
  tube=$2
  echo "python3.4 /usr/local/akcs/csconfig/scripts/beanstalk_consumer.py $host_ip $host_port $cmd $tube"
  python3.4 /usr/local/akcs/csconfig/scripts/beanstalk_consumer.py $host_ip $host_port $cmd $tube
}

# Check argument count
if [[ $# -lt 1 ]]; then
  cmd_usage
fi

# Check command
if [[ "$1" == "list-tubes" ]]; then
  beanstalk_consumer "list-tubes" ""
elif [[ "$1" == "stats-tube" ]]; then
  beanstalk_consumer stats-tube $2
elif [[ "$1" == "echo" ]]; then
  echo "curl $SERVER_INNER_IP:9996/echo"
  curl $SERVER_INNER_IP:9996/echo
elif [[ "$1" == "getMacUser" ]]; then
  mac=$2
  dstfile=$3
  echo "curl $SERVER_INNER_IP:9996/getMacUser?mac=$mac&dstfile=$dstfile"
  curl $SERVER_INNER_IP:9996/getMacUser?mac=$mac&dstfile=$dstfile
elif [[ "$1" == "getDeviceConfig" ]]; then
  mac=$2; type=$3
  if [[ "$type" == "Config" ]]; then
    type=0
  elif [[ "$type" == "Privatekey" ]]; then
    type=1
  elif [[ "$type" == "Rfid" ]]; then
    type=2
  elif [[ "$type" == "Contact" ]]; then
    type=3
  elif [[ "$type" == "Face" ]]; then
    type=4
  elif [[ "$type" == "Schedule" ]]; then
    type=5
  elif [[ "$type" == "UserMeta" ]]; then
    type=6
  elif [[ "$type" == "UserAll" ]]; then
    type=7
  fi
  token=$(getToken "$mac" "$type")
  curl_cmd="curl \"$SERVER_INNER_IP:9996/getDevicesShadow?mac=$mac&type=$type&token=$token\""
  echo "Executing: $curl_cmd"
  curl "$SERVER_INNER_IP:9996/getDevicesShadow?mac=$mac&type=$type&token=$token"
elif [[ "$1" == "getDeviceRtspUrl" ]]; then
  mac=$2
  echo "curl $SERVER_INNER_IP:9996/getDeviceRtspUrl?mac=$mac"
  curl $SERVER_INNER_IP:9996/getDeviceRtspUrl?mac=$mac
elif [[ "$1" == "getMngFilter" ]]; then
  echo "curl $SERVER_INNER_IP:9996/getMngFilter"
  curl $SERVER_INNER_IP:9996/getMngFilter
elif [[ "$1" == "setMngFilter" ]]; then
  filter=$2
  echo "curl -X POST $SERVER_INNER_IP:9996/setMngFilter?filter=$filter"
  curl -X POST $SERVER_INNER_IP:9996/setMngFilter?filter=$filter
elif [[ "$1" == "metrics" ]]; then
  echo "curl $SERVER_INNER_IP:9996/metrics"
  curl $SERVER_INNER_IP:9996/metrics
elif [[ "$1" == "getDataConfusionCacheCount" ]]; then
  echo "curl $SERVER_INNER_IP:9996/getDataConfusionCacheCount"
  curl $SERVER_INNER_IP:9996/getDataConfusionCacheCount
elif [[ "$1" == "refreshProjectData" ]]; then
  mng_id=$2
  echo "curl $SERVER_INNER_IP:9996/refreshProjectData?mng_id=$mng_id"
  curl $SERVER_INNER_IP:9996/refreshProjectData?mng_id=$mng_id
elif [[ "$1" == "kafka-get" ]]; then
  partition=$2
  offset=$3
  echo "Execute the following command on running Kafka server machine:"
  echo "/usr/local/kafka/bin/kafka-console-consumer.sh --bootstrap-server $KAFKA_IP --topic notify-csconfig --offset $offset --partition $partition --max-messages 1"
  echo "/usr/local/kafka/bin/kafka-console-consumer.sh --bootstrap-server $KAFKA_IP --topic notify-csconfig-user-detail --offset $offset --partition $partition --max-messages 1"
elif [[ "$1" == "kafka-list" ]]; then
  echo "Execute the following command on running Kafka server machine:"
  echo "/usr/local/kafka/bin/kafka-consumer-groups.sh --bootstrap-server $KAFKA_IP --group notify-csconfig_group --describe"
  echo "/usr/local/kafka/bin/kafka-consumer-groups.sh --bootstrap-server $KAFKA_IP --group notify-csconfig-user-detail_group --describe"
else
  cmd_usage
fi

