diff a/csconfig/release/scripts/maintenance.sh b/csconfig/release/scripts/maintenance.sh	(rejected hunks)
@@ -65,24 +65,6 @@
 elif [[ "$1" == "getDeviceConfig" ]]; then
   mac=$2
   type=$3
-  if  [[ "$type" == "Config" ]]; then
-    configtype=0
-  elif [[ "$type" == "Privatekey" ]]; then
-    configtype=1
-  elif [[ "$type" == "Rfid" ]]; then
-    configtype=2
-  elif [[ "$type" == "Contact" ]]; then
-    configtype=3
-  elif [[ "$type" == "Face" ]]; then
-    configtype=4
-  elif [[ "$type" == "Schedule" ]]; then
-    configtype=5
-  elif [[ "$type" == "UserMeta" ]]; then
-    configtype=6
-  elif [[ "$type" == "UserAll" ]]; then
-    configtype=7
-  fi
-  token=$(getToken "$mac" "$configtype")
   token=$(getToken "$mac" "$type")
   echo "curl $SERVER_INNER_IP:9996/getDevicesShadow?mac=$mac&type=$type&token=$token"
   curl "$SERVER_INNER_IP:9996/getDevicesShadow?mac=$mac&type=$type&token=$token"
