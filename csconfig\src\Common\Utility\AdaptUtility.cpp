#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <pthread.h>
#include <string.h>
#include <ctype.h>
#include <string>
#include <sstream>
#include <unistd.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <iomanip>
#include <sys/ioctl.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <net/if.h>
#include <assert.h>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "CharChans.h"
#include "ConfigDef.h"
#include "AkcsCommonDef.h"
#include <json/json.h>
#include "AkLogging.h"
#include "AdaptUtility.h"
#include <atomic>

//全局的序列号,主要用于获取设备配置信息时,异步回调时使用
//boost::atomics::atomic_int nConfigureSeq(0);
std::atomic<int> nConfigureSeq(0);

void GetCurTime(char* pszDate, int size)
{
    time_t timep;
    struct tm* p;
    time(&timep);
    p = localtime(&timep);
    snprintf(pszDate, size, "%d-%02d-%02d %02d:%02d:%02d", (1900 + p->tm_year), (1 + p->tm_mon), p->tm_mday,
             p->tm_hour, p->tm_min, p->tm_sec);
    return;
}

//获取当前系统时间
CString GetCurTime()
{
    time_t timep;
    struct tm* p;
    time(&timep);
    p = localtime(&timep);

    CString strCurTime;
    //strCurTime.Format(_T(FORMATE_GET_DATE_TIME_FROM_INT), curTime.wYear, curTime.wMonth, curTime.wDay, curTime.wHour, curTime.wMinute, curTime.wSecond);
    strCurTime.Format(_T(FORMATE_GET_DATE_TIME_FROM_INT), (1900 + p->tm_year), p->tm_mon, p->tm_mday, p->tm_hour, p->tm_min, p->tm_sec);
    return strCurTime;
}


//个人终端用户根据设备mac地址获取私钥文件的完全路径
std::string GetPersonnalPrivatekeyFullPath(const std::string& user, const std::string& mac)
{
    std::string strPrivatekeyPath = GetPersonnalDownloadPrivatekeyPath(user) + mac;
    strPrivatekeyPath += ".xml";
    return strPrivatekeyPath;
}


//判断设备是否在列表中
BOOL IsDeviceSettingInList(DEVICE_SETTING* device_setting_list, CString device_node, uint32_t extension)
{
    if (device_setting_list == NULL)
    {
        return FALSE;
    }

    DEVICE_SETTING* cur_node = device_setting_list;
    while (cur_node != NULL)
    {
        if ((device_node == cur_node->device_node) && (extension == cur_node->extension))
        {
            return TRUE;
        }
        cur_node = cur_node->next;
    }

    return FALSE;
}


//个人终端用户,判断设备是否在列表中
BOOL IsPersonnalDeviceSettingInList(DEVICE_SETTING* device_setting_list, const std::string& mac)
{
    if (device_setting_list == NULL)
    {
        return FALSE;
    }

    DEVICE_SETTING* cur_node = device_setting_list;
    while (cur_node != NULL)
    {
        if (mac == cur_node->mac)
        {
            return TRUE;
        }
        cur_node = cur_node->next;
    }

    return FALSE;
}

//解析通过字符解析字符串Key = Value
int ParseKeyData(PARSE_LINE_DATA* pLineData, PARSE_KEY_DATA* pKeyData)
{
    int ret = PARSE_STR_LINE_SIZE;
    if (pLineData == NULL || pKeyData == NULL)
    {
        return -1;
    }

    for (int i = 0; i < PARSE_STR_LINE_SIZE; i++)
    {
        if (strlen(pLineData->line[i]) == 0)
        {
            ret = i;
            break;
        }

        PARSE_LINE_DATA* pTempLine = new PARSE_LINE_DATA;
        memset(pTempLine, 0, sizeof(PARSE_LINE_DATA));
        ParseStringByChar(pLineData->line[i], pTempLine, '=');
        Snprintf(pKeyData->key[i], sizeof(pKeyData->key[i]) / sizeof(TCHAR), pTempLine->line[0]);
        //chenyc,解析到每一行时,接下来就是获取value了
        std::string strTmpValue(pTempLine->line[1]);
        Snprintf(pKeyData->value[i], sizeof(pKeyData->value[i]) / sizeof(TCHAR), strTmpValue.c_str());

        delete pTempLine;
        pTempLine = NULL;
    }

    return ret;
}


//删除目录下的所有文件但不包括文件夹
void DeleteDirFiles(std::string strDirectory)
{
    std::stringstream strTmpDir;
    strTmpDir << "rm -rf ";
    strTmpDir << strDirectory;
    strTmpDir << "/*";
    //调用shell脚本删除
    if (system(strTmpDir.str().c_str()) < 0)
    {
        AK_LOG_WARN << "Failed to DeleteDirFiles on " << strDirectory.c_str();
    }

}

//删除目录下的所有文件但不包括文件夹
void DeleteDirFilesWithSuffix(std::string strDirectory, std::string strSuffix)
{
    std::stringstream strTmpDir;
    strTmpDir << "rm -rf ";
    strTmpDir << strDirectory;
    strTmpDir << "/*" << strSuffix;
    //调用shell脚本删除
    if (system(strTmpDir.str().c_str()) < 0)
    {
        AK_LOG_WARN << "Failed to DeleteDirFiles on " << strDirectory.c_str();
    }

}


//删除文件
int DeleteFile(CString strFile)
{
    if (strcmp(strFile.GetBuffer(), "/") == 0)
    {
        return -1;
    }

    std::stringstream strTmpFile;
    strTmpFile << "rm -rf ";
    strTmpFile << strFile.GetBuffer();
    //调用shell脚本删除
    if (system(strTmpFile.str().c_str()) < 0)
    {
        AK_LOG_WARN << "Failed to DeleteFile " << strFile.GetBuffer() << ", errno:" << errno;
        return -1;
    }
    return 0;
}

//创建一个路径
void CreateDir(const std::string& strDirectory)
{
    std::stringstream strTmpDir;
    strTmpDir << "mkdir -p "
              << strDirectory;
    //调用shell脚本删除
    if (system(strTmpDir.str().c_str()) < 0)
    {
        AK_LOG_WARN << "Failed to CreateDir on " << strDirectory.c_str() << ", errno:" << errno;
    }
}


//删除一个路径
void DeleteDir(const std::string& strDirectory)
{
    if (strcmp(strDirectory.c_str(), "/") == 0)
    {
        return;
    }
    if (!IsDir(strDirectory))
    {
        return ;
    }
    std::stringstream strTmpDir;
    strTmpDir << "rm -rf "
              << strDirectory;
    //调用shell脚本删除
    if (system(strTmpDir.str().c_str()) < 0)
    {
        AK_LOG_WARN << "Failed to DeleteDir on " << strDirectory.c_str();
    }
}


//从符号转义
CString ReplaceFromSymbol(CString strText)
{
    strText.Replace(_T("&"), _T("&amp"));
    strText.Replace(_T("'"), _T("&#039"));
    strText.Replace(_T("<"), _T("&lt"));
    strText.Replace(_T(">"), _T("&gt"));
    strText.Replace(_T("\""), _T("&quot"));

    return strText;
}

//转义到符号
CString ReplaceToSymbol(CString strText)
{
    strText.Replace(_T("&amp"), _T("&"));
    strText.Replace(_T("&#039"), _T("'"));
    strText.Replace(_T("&lt"), _T("<"));
    strText.Replace(_T("&gt"), _T(">"));
    strText.Replace(_T("&quot"), _T("\""));

    return strText;
}

/* 删除左边的空格 */
char* cscomm_l_trim(char* szOutput, const char* szInput)
{
    assert(szInput != NULL);
    assert(szOutput != NULL);
    assert(szOutput != szInput);
    for (NULL; *szInput != '\0' && isspace(*szInput); ++szInput)
    {
    }
    return strcpy(szOutput, szInput);
}

/* 删除右边的空格 */
char* cscomm_r_trim(char* szOutput, const char* szInput)
{
    char* p = NULL;
    assert(szInput != NULL);
    assert(szOutput != NULL);
    assert(szOutput != szInput);
    strcpy(szOutput, szInput);
    for (p = szOutput + strlen(szOutput) - 1; p >= szOutput && isspace(*p); --p)
    {
    }
    *(++p) = '\0';
    return szOutput;
}

/* 删除两边的空格 */
char* cscomm_rl_trim(char* szOutput, const char* szInput)
{
    char* p = NULL;
    assert(szInput != NULL);
    assert(szOutput != NULL);
    cscomm_l_trim(szOutput, szInput);
    for (p = szOutput + strlen(szOutput) - 1; p >= szOutput && isspace(*p); --p)
    {
    }
    *(++p) = '\0';
    return szOutput;
}

int cscomm_get_conf(const char* section, const char* key_name, char* key_val, int val_limit_len)
{
    char sectionname[NAME_SIZE], keyname[NAME_SIZE];
    char* buf, *c;
    char buf_in[KEY_VAL_LEN], buf_out[KEY_VAL_LEN];
    FILE* fp;
    int found = 0; /* 1 AppName 2 KeyName */
    if ((fp = fopen(CONFFILEPATH, "r")) == NULL)
    {
        printf("open file [%s] error [%s]\n", CONFFILEPATH, strerror(errno));
        return -1;
    }
    fseek(fp, 0, SEEK_SET);
    memset(sectionname, 0, sizeof(sectionname));
    snprintf(sectionname, NAME_SIZE - 1, "[%s]", section);

    while (!feof(fp) && fgets(buf_in, KEY_VAL_LEN, fp) != NULL)
    {
        cscomm_l_trim(buf_out, buf_in);
        if (strlen(buf_out) <= 0)
        {
            continue;
        }
        buf = NULL;
        buf = buf_out;

        if (found == 0)
        {
            if (buf[0] != '[')
            {
                continue;
            }
            else if (strncmp(buf, sectionname, strlen(sectionname)) == 0)
            {
                found = 1;
                continue;
            }
        }
        else if (found == 1)
        {
            if (buf[0] == '#')
            {
                continue;
            }
            else if (buf[0] == '[')
            {
                break;
            }
            else
            {
                if ((c = (char*)strchr(buf, '=')) == NULL)
                {
                    continue;
                }
                memset(keyname, 0, sizeof(keyname));
                sscanf(buf, "%[^=|^ |^\t]", keyname);
                if (strcmp(keyname, key_name) == 0)
                {
                    sscanf(++c, "%[^\n]", key_val);
                    char* tmp_KeyVal = (char*)malloc(strlen(key_val) + 1);
                    if (tmp_KeyVal != NULL)
                    {
                        memset(tmp_KeyVal, 0, strlen(key_val) + 1);
                        //清除val两边的空格
                        cscomm_rl_trim(tmp_KeyVal, key_val);
                        if (tmp_KeyVal && strlen(tmp_KeyVal) > 0)
                        {
                            Snprintf(key_val, val_limit_len, tmp_KeyVal);
                        }
                        free(tmp_KeyVal);
                        tmp_KeyVal = NULL;
                    }
                    found = 2;
                    break;
                }
                else
                {
                    continue;
                }
            }
        }
    }
    fclose(fp);
    if (found == 2)
    {
        return (0);
    }
    else
    {
        return (-1);
    }
}

//获取HTTP根目录
CString GetHttpRootPath()
{
    CString path = HTTPROOT;
    return path;
}

std::string GetHttpRootPathStr()
{
    std::string path = HTTPROOT;
    return path;
}
//获取个人终端用户所在单元的路径,eg:/home/<USER>/apache/download/personal/node_XX/$user/
std::string GetPersonnalDownloadPath(const std::string user)
{
    std::size_t nUserHash = std::hash<std::string> {}(user);
    std::size_t nHash = nUserHash % 100;
    std::stringstream path;

    path << GetHttpRootPathStr() << PERSONNAL_DOWNLOAD
            << "node_" << nHash << "/"
            << user << "/";
    CreateDir(path.str());
    return path.str();
}


//获取社区公共设备目录
std::string GetCommunityPublicDownloadPath(const int mng_account_id)
{
    std::stringstream path;
    path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
            << mng_account_id << "/"
            << "Public_" << mng_account_id
            << mng_account_id << "/";
    CreateDir(path.str());
    return path.str();

}
//获取社区单元公共设备目录
std::string GetCommunityUnitPublicDownloadPath(const int mng_account_id, const int unit_id)
{
    std::stringstream path;
    path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
            << mng_account_id << "/"
            << unit_id << "/"
            << "Public_" << unit_id
            << mng_account_id << "/";
    CreateDir(path.str());
    return path.str();

}
//获取社区个人公共设备目录
std::string GetCommunityPersonalDownloadPath(const int mng_account_id, const int unit_id, const char* pszNode)
{
    std::stringstream path;
    path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
            << mng_account_id << "/"
            << unit_id << "/"
            << pszNode << "/";
    CreateDir(path.str());
    return path.str();

}

std::string GetCommunityPublicDownloadConfigPath(const int mng_account_id)
{
    std::stringstream path;
    path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
            << mng_account_id << "/"
            << "Public_" << mng_account_id << "/"
            << "Config/";
    CreateDir(path.str());
    return path.str();
}

std::string GetCommunityPublicDownloadContactPath(const int mng_account_id)
{
    std::stringstream path;
    path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
            << mng_account_id << "/"
            << "Public_" << mng_account_id << "/"
            << "ContactList/";
    CreateDir(path.str());
    return path.str();
}

std::string GetCommunityUnitPublicDownloadConfigPath(const int mng_account_id, const int unit_id)
{
    std::stringstream path;
    path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
            << mng_account_id << "/"
            << unit_id << "/"
            << "Public_" << unit_id << "/"
            << "Config/";
    CreateDir(path.str());
    return path.str();
}

std::string GetCommunityUnitPublicDownloadContactPath(const int mng_account_id, const int unit_id)
{
    std::stringstream path;
    path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
            << mng_account_id << "/"
            << unit_id << "/"
            << "Public_" << unit_id << "/"
            << "ContactList/";
    CreateDir(path.str());
    return path.str();
}

std::string GetCommunityPersonalDownloadConfigPath(const int mng_account_id, const int unit_id, const char* pszNode)
{
    std::stringstream path;
    path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
            << mng_account_id << "/"
            << unit_id << "/"
            << pszNode << "/"
            << "Config/";
    CreateDir(path.str());
    return path.str();
}



std::string GetPersonnalDownloadFacePath(const std::string& user)
{
    std::string strpath = GetPersonnalDownloadPath(user) + "Face/";
    CreateDir(strpath);
    return strpath;
}

std::string GetPersonnalDownloadConfPath(const std::string& user)
{
    std::string strpath = GetPersonnalDownloadPath(user) + "Config/";
    CreateDir(strpath);
    return strpath;
}

std::string GetPersonnalDownloadContactListPath(const std::string& user)
{
    std::string strpath = GetPersonnalDownloadPath(user) + "ContactList/";
    CreateDir(strpath);
    return strpath;
}


std::string GetCommunityPublicDownloadPrivatekeyPath(const int mng_account_id)
{
    std::stringstream path;
    path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
            << mng_account_id << "/"
            << "Public_" << mng_account_id << "/"
            << "Privatekey/";
    CreateDir(path.str());
    return path.str();
}
std::string GetCommunityUnitPublicDownloadPrivatekeyPath(const int mng_account_id, const int unit_id)
{
    std::stringstream path;
    path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
            << mng_account_id << "/"
            << unit_id << "/"
            << "Public_" << unit_id << "/"
            << "Privatekey/";
    CreateDir(path.str());
    return path.str();
}
std::string GetCommunityPersonalDownloadPrivatekeyPath(const int mng_account_id, const int unit_id, const char* pszNode)
{
    std::stringstream path;
    path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
            << mng_account_id << "/"
            << unit_id << "/"
            << pszNode << "/"
            << "Privatekey/";
    CreateDir(path.str());
    return path.str();
}

std::string GetCommunityPublicDownloadUserPath(const int mng_account_id)
{
    std::stringstream path;
    path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
            << mng_account_id << "/"
            << "Public_" << mng_account_id << "/"
            << "User/";
    CreateDir(path.str());
    return path.str();
}
std::string GetCommunityUnitPublicDownloadUserPath(const int mng_account_id, const int unit_id)
{
    std::stringstream path;
    path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
            << mng_account_id << "/"
            << unit_id << "/"
            << "Public_" << unit_id << "/"
            << "User/";
    CreateDir(path.str());
    return path.str();
}
std::string GetCommunityPersonalDownloadUserPath(const int mng_account_id, const int unit_id, const char* pszNode)
{
    std::stringstream path;
    path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
            << mng_account_id << "/"
            << unit_id << "/"
            << pszNode << "/"
            << "User/";
    CreateDir(path.str());
    return path.str();
}

std::string GetCommunitySaveContactListDir(uint32_t mng_account_id,
        uint32_t nUintID, const char* pszAccountID, int grade)
{
    std::stringstream path;
    if (grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
    {
        path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
                << mng_account_id << "/"
                << nUintID << "/"
                << pszAccountID << "/"
                << "ContactList/";
    }
    else if (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
    {
        path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
                << mng_account_id << "/"
                << "Public_" << mng_account_id
                << "/ContactList/";

    }
    else
    {
        path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
                << mng_account_id << "/"
                << nUintID << "/"
                << "Public_" << nUintID << "/"
                << "ContactList/";

    }
    CreateDir(path.str());
    return path.str();
}


std::string GetCommunityScheduleRootDir(uint32_t mng_account_id,
        uint32_t nUintID, const char* pszAccountID, int grade)
{
    std::stringstream path;
    if (grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
    {
        path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
                << mng_account_id << "/"
                << nUintID << "/"
                << pszAccountID << "/"
                << "Schedule/";
    }
    else if (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
    {
        path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
                << mng_account_id << "/"
                << "Public_" << mng_account_id
                << "/Schedule/";

    }
    else
    {
        path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
                << mng_account_id << "/"
                << nUintID << "/"
                << "Public_" << nUintID << "/"
                << "Schedule/";

    }
    CreateDir(path.str());
    return path.str();
}

std::string GetCommunityUserRootDir(uint32_t mng_account_id,
        uint32_t nUintID, const char* pszAccountID, int grade)
{
    std::stringstream path;
    if (grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
    {
        path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
                << mng_account_id << "/"
                << nUintID << "/"
                << pszAccountID << "/"
                << "UserMeta/";
    }
    else if (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
    {
        path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
                << mng_account_id << "/"
                << "Public_" << mng_account_id
                << "/UserMeta/";

    }
    else
    {
        path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
                << mng_account_id << "/"
                << nUintID << "/"
                << "Public_" << nUintID << "/"
                << "UserMeta/";

    }
    CreateDir(path.str());
    return path.str();
}


std::string GetCommunitySaveContactListPath(uint32_t mng_account_id,
        uint32_t nUintID, const char* pszAccountID, int grade)
{
    std::stringstream path;
    if (grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
    {
        path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
                << mng_account_id << "/"
                << nUintID << "/"
                << pszAccountID << "/"
                << "ContactList/";
    }
    else if (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
    {
        path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
                << mng_account_id << "/"
                << "Public_" << mng_account_id << "/"
                << "ContactList/";

    }
    else
    {
        path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
                << mng_account_id << "/"
                << nUintID << "/"
                << "Public_" << nUintID << "/"
                << "ContactList/";

    }
    CreateDir(path.str());
    return path.str();
}




std::string GetPersonnalDownloadPrivatekeyPath(const std::string& user)
{
    std::string strpath = GetPersonnalDownloadPath(user) + "Privatekey/";
    CreateDir(strpath);
    return strpath;
}

std::string GetCommunityPublicDownloadRfidPath(const int mng_account_id)
{
    std::stringstream path;
    path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
            << mng_account_id << "/"
            << "Public_" << mng_account_id << "/"
            << "Rfid/";
    CreateDir(path.str());
    return path.str();
}
std::string GetCommunityUnitPublicDownloadRfidPath(const int mng_account_id, const int unit_id)
{
    std::stringstream path;
    path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
            << mng_account_id << "/"
            << unit_id << "/"
            << "Public_" << unit_id << "/"
            << "Rfid/";
    CreateDir(path.str());
    return path.str();
}
std::string GetCommunityPersonalDownloadRfidPath(const int mng_account_id, const int unit_id, const char* pszNode)
{
    std::stringstream path;
    path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
            << mng_account_id << "/"
            << unit_id << "/"
            << pszNode << "/"
            << "Rfid/";
    CreateDir(path.str());
    return path.str();
}


std::string GetPersonnalDownloadRfidPath(const std::string& user)
{
    std::string strpath = GetPersonnalDownloadPath(user) + "Rfid/";
    CreateDir(strpath);
    return strpath;
}

int StrCatTchar(TCHAR* pszDst, int size, const TCHAR* pszSrc)
{
    if (pszDst == NULL || pszSrc == NULL)
    {
        return -1;
    }

    //return _tcscat_s(pszDst, size, pszSrc);
    strncat(pszDst, pszSrc, size);
    return 0;
}



//获取本地IP地址
int GetLocalIPAddr(TCHAR* pszLocalIPAddr, int size)
{
    if (pszLocalIPAddr == NULL)
    {
        return -1;
    }
    //added by chenyc,2017-04-21,对aliyun的网卡以及本地的测试虚拟机做兼容,如果存在网卡1,就用网卡1的数据,没有的话就用网卡0的IP
    int inet_sock;
    struct ifreq ifr;
    inet_sock = socket(AF_INET, SOCK_DGRAM, 0);
    Snprintf(ifr.ifr_name, sizeof(ifr.ifr_name), "eth1");
    ioctl(inet_sock, SIOCGIFADDR, &ifr);
    Snprintf(pszLocalIPAddr, size,  inet_ntoa(((struct sockaddr_in*)&ifr.ifr_addr)->sin_addr));
    if (pszLocalIPAddr[0] == '0')
    {
        strcpy(ifr.ifr_name, "eth0");
        ioctl(inet_sock, SIOCGIFADDR, &ifr);
        Snprintf(pszLocalIPAddr, size,  inet_ntoa(((struct sockaddr_in*)&ifr.ifr_addr)->sin_addr));
    }
    return 0;
}

//解析通过字符解析字符串
//chenyc,分两次解析,第一次根据','解析整体,第二次根据'='解析每一行,所以当配置信息(value)为空时,需要做保护.
int ParseStringByChar(const WCHAR* pszOri, PARSE_LINE_DATA* pLineData, WCHAR c)
{
    if (pLineData == NULL || pszOri == NULL)
    {
        return -1;
    }
    CString strOri = pszOri;
    int nFindIndex = FindCharOutOfQuote(strOri, c);
    int nIndex = 0;
    while (nFindIndex >= 0)
    {
        if (nIndex >= PARSE_STR_LINE_SIZE)
        {
            break;
        }
        CString strTemp = strOri.Left(nFindIndex);
        strTemp.TrimLeft();
        strTemp.TrimRight();
        Snprintf(pLineData->line[nIndex], sizeof(pLineData->line[nIndex]) / sizeof(TCHAR), strTemp.GetBuffer());
        //chenyc,开始解析value部分,需要对value是空字符串的情况做保护
        strOri = strOri.Right(strOri.GetLength() - nFindIndex - 1);
        strTemp.TrimLeft();
        strTemp.TrimRight();
        nIndex++;

        //added by chenyc,做保护,如果strOri是空串了,证明value是空,需要做保护
        if (strOri.IsEmpty() && c == '=')
        {
            //AK_LOG_WARN << "The cstring is empty";
            memset(pLineData->line[nIndex], 0, sizeof(pLineData->line[nIndex]));
            break;
        }
        nFindIndex = FindCharOutOfQuote(strOri, c);
    }
    if (!strOri.IsEmpty() && (nIndex < PARSE_STR_LINE_SIZE))
    {
        std::string strTmpValue(strOri.GetBuffer());
        strTrim(strTmpValue);
        Snprintf(pLineData->line[nIndex], sizeof(pLineData->line[nIndex]) / sizeof(TCHAR), strTmpValue.c_str());
    }

    return nIndex;
}

void strTrim(std::string& str)
{
    if (str.empty())
    {
        return;
    }
    int s = str.find_first_not_of(' ');
    int e = str.find_last_not_of(' ');
    str = str.substr(s, e - s + 1);
}


//查找不在引号以内的第一个字符出现的位置(应用场景是引号内出现分隔符，如Key1="123,123",Key2="234,234")
static int FindCharOutOfQuote(CString strOri, WCHAR c)
{
    //找出字符串中所有引号所在的位置
    int nPosList[4096] = {-1};
    for (int i = 0; i < 4096; i++)
    {
        nPosList[i] = -1;
    }
    int nPosIndex = 0;
    int nQuoteIndex = strOri.Find('\"');
    while (nQuoteIndex >= 0)
    {
        nPosList[nPosIndex] = nQuoteIndex;
        nQuoteIndex = strOri.Find('\"', nQuoteIndex + 1);
        nPosIndex++;
    }

    int nCharIndex = -1;
    while (1)
    {
        //查找c所在位置
        nCharIndex = strOri.Find(c, nCharIndex + 1);
        if (nCharIndex < 0)
        {
            return -1;
        }

        int nQuoteCount = 0;

        for (int i = 0; i < sizeof(nPosList) / sizeof(uint32_t); i++)
        {
            if (nPosList[i] == -1)
            {
                break;
            }

            if (nPosList[i] < nCharIndex)
            {
                nQuoteCount++;
            }
            else
            {
                break;
            }
        }
        if (nQuoteCount % 2 == 0)
        {
            return nCharIndex;
        }
    }

    return -1;
}

//获取设备配置信息时的全局seq
uint32_t GetConfigureSeq()
{
    nConfigureSeq++;
    return nConfigureSeq;
}

/* Added by chenyc,2017-05-03,增加一些常用的工具函数 */

// 是否为目录
bool IsDir(const std::string& file)
{
    struct stat statbuf;
    if (stat(file.c_str(), &statbuf) == 0)
    {
        if (S_ISDIR(statbuf.st_mode) != 0)
        {
            return true;
        }
    }
    return false;
}

//s:待分割的源字符串,C:分隔符,V:分割后的结果集,vector<>
#if 0
void SplitString(const std::string& s, const std::string& c, std::vector<std::string>& oVec)
{
    std::string::size_type pos1 = 0;
    auto pos2 = s.find(c);
    while (std::string::npos != pos2)
    {
        //容错，防止加入一个空串,形如:12,,23,42 或者:12,23, 或者:12,23,,
        if (pos2 > pos1)
        {
            oVec.push_back(s.substr(pos1, pos2 - pos1));
        }
        pos1 = pos2 + c.size();
        pos2 = s.find(c, pos1);
    }
    //将最后一个字符串压入容器中
    if (pos1 != s.length())
    {
        oVec.push_back(s.substr(pos1));
    }
}
#endif

std::string GetRelayContactStr(const std::vector<RELAY_INFO>& relay_infos)
{
    int i = 0;
    std::stringstream relay_str;
    for (auto& relay : relay_infos)
    {
        if (!relay.enable)
        {
            i++;
            continue;
        }

        relay_str << i << "," << relay.name << ",";
        if (10 == relay.dtmf)
        {
            relay_str << "*";
        }
        else if (11 == relay.dtmf)
        {
            relay_str << "#";
        }
        else
        {
            relay_str << relay.dtmf;
        }
        i++;

        if (i < relay_infos.size())
        {
            relay_str << ";";
        }
    }

    return relay_str.str();
}

std::string GetSecurityRelayConfig(const std::vector<RELAY_INFO>& relay_infos)
{
    int i = 1;
    std::stringstream config_str;
    for (auto &relay : relay_infos)
    {
        switch (i)
        {
            case 1:
                config_str << CONFIG_SECURITY_RELAY_DMTF_CODE1 << relay.dtmf << "\n";
                config_str << CONFIG_SECURITY_RELAY_DMTF_NAME1 << relay.name << "\n";
                config_str << CONFIG_SECURITY_RELAY_ENABLED1 << relay.enable << "\n";
                break;

            case 2:
                config_str << CONFIG_SECURITY_RELAY_DMTF_CODE2 << relay.dtmf << "\n";
                config_str << CONFIG_SECURITY_RELAY_DMTF_NAME2 << relay.name << "\n";
                config_str << CONFIG_SECURITY_RELAY_ENABLED2 << relay.enable << "\n";
                break;

            case 3:
                config_str << CONFIG_SECURITY_RELAY_DMTF_CODE3 << relay.dtmf << "\n";
                config_str << CONFIG_SECURITY_RELAY_DMTF_NAME3 << relay.name << "\n";
                config_str << CONFIG_SECURITY_RELAY_ENABLED3 << relay.enable << "\n";
                break;

            case 4:
                config_str << CONFIG_SECURITY_RELAY_DMTF_CODE4 << relay.dtmf << "\n";
                config_str << CONFIG_SECURITY_RELAY_DMTF_NAME4 << relay.name << "\n";
                config_str << CONFIG_SECURITY_RELAY_ENABLED4 << relay.enable << "\n";
                break;

            default:
                //只支持4个
                break;
        }
        i++;
    }

    return config_str.str();
}

std::string GetCommunityUnitPublicDownloadFacePath(const int mng_account_id, const int unit_id)
{
    std::stringstream path;
    path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
            << mng_account_id << "/"
            << unit_id << "/"
            << "Public_" << unit_id << "/"
            << "Face/";
    CreateDir(path.str());
    return path.str();
}

std::string GetCommunityPublicDownloadFacePath(const int mng_account_id)
{
    std::stringstream path;
    path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
            << mng_account_id << "/"
            << "Public_" << mng_account_id << "/"
            << "Face/";
    CreateDir(path.str());
    return path.str();
}

std::string GetCommunityNodeDownloadFacePath(const int mng_account_id, const int unit_id, const char* pszNode)
{
    std::stringstream path;
    path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
            << mng_account_id << "/"
            << unit_id << "/"
            << pszNode << "/"
            << "Face/";
    CreateDir(path.str());
    return path.str();
}

std::string GetUserDetailDownloadPath(const std::string mac, std::string &web_download_path)
{
    std::size_t mac_hash = std::hash<std::string> {}(mac);
    std::size_t hash = mac_hash % 64;
    std::stringstream path;
    std::stringstream download_path;

    std::string date = GetNowDate();
    //日期/64个节点/mac_traceid.json"
    path << GetHttpRootPath() << USER_DETAIL_DOWNLOAD
            << date << "/"
            << hash << "/";
    CreateDir(path.str());


    download_path << USER_DETAIL_DOWNLOAD
            << date << "/"
            << hash << "/";
    web_download_path = download_path.str();
    
    return path.str();
}

std::string GetCommunityUserAllDetailDir(uint32_t mng_account_id,
        uint32_t nUintID, const char* pszAccountID, int grade)
{
    std::stringstream path;
    if (grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
    {
        path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
                << mng_account_id << "/"
                << nUintID << "/"
                << pszAccountID << "/"
                << "UserAll/";
    }
    else if (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
    {
        path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
                << mng_account_id << "/"
                << "Public_" << mng_account_id
                << "/UserAll/";

    }
    else
    {
        path << GetHttpRootPathStr() << COMMUNITY_DOWNLOAD
                << mng_account_id << "/"
                << nUintID << "/"
                << "Public_" << nUintID << "/"
                << "UserAll/";

    }
    CreateDir(path.str());
    return path.str();
}


