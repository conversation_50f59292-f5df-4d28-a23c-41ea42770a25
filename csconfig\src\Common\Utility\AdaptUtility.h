#ifndef __ADAPT_UTILITY_H__
#define __ADAPT_UTILITY_H__
#include <vector>
#include <string>
#include "util.h"
#include "AKCSMsg.h"

//以下为前置声明
//class CString;
typedef struct DEVICE_SETTING_T DEVICE_SETTING;
typedef struct CONFIG_MODULE_T CONFIG_MODULE;

#define HTONS   htons
#define NTOHS   ntohs
#define HTONL   htonl
#define NTOHL   ntohl

#define PARSE_STR_LINE_SIZE     16
#define PARSE_STR_TEXT_SIZE     256
typedef struct PARSE_LINE_DATA_T
{
    char line[PARSE_STR_LINE_SIZE][PARSE_STR_TEXT_SIZE];
} PARSE_LINE_DATA;
typedef struct PARSE_KEY_DATA_T
{
    char key[PARSE_STR_LINE_SIZE][PARSE_STR_TEXT_SIZE];
    char value[PARSE_STR_LINE_SIZE][PARSE_STR_TEXT_SIZE];
} PARSE_KEY_DATA;


//获取当前系统时间
void GetCurTime(TCHAR* pszDate, int size);


//获取当前系统时间
CString GetCurTime();

//解析通过字符解析字符串Key = Value
int ParseKeyData(PARSE_LINE_DATA* pLineData, PARSE_KEY_DATA* pKeyData);

//解析通过字符解析字符串
int ParseStringByChar(const WCHAR* pszOri, PARSE_LINE_DATA* pLineData, WCHAR c);

//判断设备是否在列表中
BOOL IsDeviceSettingInList(DEVICE_SETTING* device_setting_list, CString device_node, uint32_t extension);

//删除目录下的所有文件不包括文件夹
void DeleteDirFiles(std::string strDirectory);

//删除目录下的所有文件不包括文件夹 定义后缀
void DeleteDirFilesWithSuffix(std::string strDirectory, std::string strSuffix);

/* 删除左边的空格 */
char* cscomm_l_trim(char* szOutput, const char* szInput);

/* 删除右边的空格 */
char* cscomm_r_trim(char* szOutput, const char* szInput);

/* 删除两边的空格 */
char* cscomm_rl_trim(char* szOutput, const char* szInput);

int cscomm_get_conf(const char* section, const char* key_name, char* key_val, int val_limit_len);

//获取HTTP根目录
CString GetHttpRootPath();
std::string GetHttpRootPathStr();
//CString GetDownloadConfigPath(); add by chenzhx 20180403
//std::string GetDownloadConfigPath(const std::string user);add by chenzhx 20180403

int StrCatTchar(TCHAR* pszDst, int size, const TCHAR* pszSrc);

//删除目录下的所有文件不包括文件夹
int DeleteFile(CString strFile);
//获取本地IP地址
int GetLocalIPAddr(TCHAR* pszLocalIPAddr, int size);
int ParseStringByChar(const WCHAR* pszOri, PARSE_LINE_DATA* pLineData, WCHAR c);
static int FindCharOutOfQuote(CString strOri, WCHAR c);


//c++标准的去掉左右两边空格
void strTrim(std::string& str);

uint32_t GetConfigureSeq();

// 是否为目录
bool IsDir(const std::string& file);

//个人终端用户,判断设备是否在列表中
BOOL IsPersonnalDeviceSettingInList(DEVICE_SETTING* device_setting_list, const std::string& mac);
std::string GetPersonnalDownloadPrivatekeyPath(const std::string& user);
std::string GetPersonnalDownloadFacePath(const std::string& user);
std::string GetPersonnalDownloadConfPath(const std::string& user);
std::string GetPersonnalDownloadRfidPath(const std::string& user);
//获取个人终端用户所在单元的路径,eg:/home/<USER>/apache/personnal_download/node_XX/user/
std::string GetPersonnalDownloadPath(const std::string user);


//个人终端用户根据设备mac地址获取私钥文件的完全路径
std::string GetPersonnalPrivatekeyFullPath(const std::string& user, const std::string& mac);

//创建一个路径
void CreateDir(const std::string& strDirectory);
//删除一个路径
void DeleteDir(const std::string& strDirectory);
//s:待分割的源字符串,C:分隔符,V:分割后的结果集,vector<>
//void SplitString(const std::string& s, const std::string& c, std::vector<std::string>& oVec);


//***************v4.0******************
std::string GetCommunityPublicDownloadRfidPath(const int mng_account_id);
std::string GetCommunityUnitPublicDownloadRfidPath(const int mng_account_id, const int unit_id);
std::string GetCommunityPersonalDownloadRfidPath(const int mng_account_id, const int unit_id, const char* pszNode);
std::string GetCommunityPublicDownloadPrivatekeyPath(const int mng_account_id);
std::string GetCommunityUnitPublicDownloadPrivatekeyPath(const int mng_account_id, const int unit_id);
std::string GetCommunityPersonalDownloadPrivatekeyPath(const int mng_account_id, const int unit_id, const char* pszNode);
std::string GetCommunityPublicDownloadConfigPath(const int mng_account_id);
std::string GetCommunityUnitPublicDownloadConfigPath(const int mng_account_id, const int unit_id);
std::string GetCommunityPersonalDownloadConfigPath(const int mng_account_id, const int unit_id, const char* pszNode);
//获取社区公共设备目录
std::string GetCommunityPublicDownloadPath(const int mng_account_id);
//获取社区单元公共设备目录
std::string GetCommunityUnitPublicDownloadPath(const int mng_account_id, const int unit_id);
//获取社区个人公共设备目录
std::string GetCommunityPersonalDownloadPath(const int mng_account_id, const int unit_id, const char* pszNode);

//个人终端联系人路径
std::string GetPersonnalDownloadContactListPath(const std::string& user);
std::string GetCommunitySaveContactListDir(uint32_t mng_account_id,
        uint32_t nUintID, const char* pszAccountID, int grade);

std::string GetCommunityUnitPublicDownloadContactPath(const int mng_account_id, const int unit_id);
std::string GetCommunityPublicDownloadContactPath(const int mng_account_id);

std::string GetRelayContactStr(const std::vector<RELAY_INFO>& relay_infos);

std::string GetCommunityUnitPublicDownloadFacePath(const int mng_account_id, const int unit_id);
std::string GetCommunityPublicDownloadFacePath(const int mng_account_id);
std::string GetCommunityNodeDownloadFacePath(const int mng_account_id, const int unit_id, const char* pszNode);

std::string GetCommunityPublicDownloadUserPath(const int mng_account_id);
std::string GetCommunityUnitPublicDownloadUserPath(const int mng_account_id, const int unit_id);
std::string GetCommunityPersonalDownloadUserPath(const int mng_account_id, const int unit_id, const char* pszNode);

std::string GetUserDetailDownloadPath(const std::string mac, std::string &web_download_path);

std::string GetCommunityUserAllDetailDir(uint32_t mng_account_id,uint32_t nUintID, const char* pszAccountID, int grade);


std::string GetCommunityScheduleRootDir(uint32_t mng_account_id,
        uint32_t nUintID, const char* pszAccountID, int grade);
std::string GetCommunityUserRootDir(uint32_t mng_account_id,
        uint32_t nUintID, const char* pszAccountID, int grade);



#endif //__ADAPT_UTILITY_H__

