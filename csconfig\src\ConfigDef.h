#ifndef __ADAPT_DEFINE_H__
#define __ADAPT_DEFINE_H__

#define IP_SIZE                         16
#define DEVICE_ID_SIZE                  24

#define MD5_SIZE                        36
#define NAME_SIZE                       32
#define CONFIG_MSG_SIZE                 1024

#define MAX_FILE_PATH                   128
#define DEV_NODE_SIZE                   64
#define COMMON_CONF_SIZE                64
#define KEY_VAL_LEN                     32
#define DATETIME_SIZE                   24

#define UDPMTU_SIZE                     1024

#define PERSONNAL_SIP_UA_TIMEOUT        "1800"
#define PERSONNAL_SIP_GROUP_DOOR_PHONE  "Config.Programable.SOFTKEY01.Param1="

#define  CONFIG_ENABLE        "Config.Account1.GENERAL.Enable="
#define  CONFIG_LABLE         "Config.Account1.GENERAL.Label="
#define  CONFIG_DISPLAYNAME   "Config.Account1.GENERAL.DisplayName="
#define  CONFIG_USERNAME      "Config.Account1.GENERAL.UserName="
#define  CONFIG_AUTHNAME      "Config.Account1.GENERAL.AuthName="
#define  CONFIG_PWD           "Config.Account1.GENERAL.Pwd="
#define  CONFIG_SERVER        "Config.Account1.SIP.Server="
#define  CONFIG_PORT          "Config.Account1.SIP.Port="
#define  CONFIG_TIMEOUT       "Config.Account1.REG.Timeout="
#define  CONFIG_SERVER2       "Config.Account1.SIP.Server2="
#define  CONFIG_PORT2         "Config.Account1.SIP.Port2="
#define  CONFIG_TIMEOUT2      "Config.Account1.REG.Timeout2="
#define  CONFIG_INDOOR        "Config.DoorSetting.SPEEDDIAL.Indoor="
#define  CONFIG_OUTDOOR       "Config.DoorSetting.SPEEDDIAL.Outdoor="
#define  CONFIG_DTMF_ENABLE   "Config.DoorSetting.DTMF.Enable="
#define  CONFIG_DTMF_CODE1    "Config.DoorSetting.DTMF.Code1="
#define  CONFIG_SIP_GROUP_ACCOUNT          "Config.Programable.SOFTKEY01.Param1="
#define  CONFIG_SIP_NAT_RPORT              "Config.Account1.NAT.Rport="
#define  CONFIG_RTSP_ENABLE                "Config.DoorSetting.RTSP.Enable="
#define  CONFIG_RTSP_AUDIO                 "Config.DoorSetting.RTSP.Audio="
#define  CONFIG_RTSP_VIDEO                 "Config.DoorSetting.RTSP.Video="
#define  CONFIG_RTSP_CODEC                 "Config.DoorSetting.RTSP.Codec="
#define  CONFIG_RTSP_H264_RESOLUTION       "Config.DoorSetting.RTSP.H264Resolution="
#define  CONFIG_RTSP_H264_FRAMERATE        "Config.DoorSetting.RTSP.H264FrameRate="
#define  CONFIG_RTSP_H264_BITRATE          "Config.DoorSetting.RTSP.H264BitRate="
#define  CONFIG_NAT_UDP_ENABLE             "Config.Account1.NAT.UdpKeepEnable="
#define  CONFIG_NAT_UDP_INTERVAL           "Config.Account1.NAT.UdpKeepInterval="

#define  CONFIG_CLOUDSERVER_FTP_URL        "Config.DoorSetting.CLOUDSERVER.UploadUrl="
#define  CONFIG_CLOUDSERVER_FTP_USER       "Config.DoorSetting.CLOUDSERVER.UploadUser="
#define  CONFIG_CLOUDSERVER_FTP_PWD        "Config.DoorSetting.CLOUDSERVER.UploadPassword="

//社区R27 29界面呈现 只有公共设备才会设置这个值为1
#define  CONFIG_CLOUDSERVER_RXX_COMMUNITY  "Config.DoorSetting.CLOUDSERVER.DeviceCommunity="
//是否过期
#define  CONFIG_CLOUDSERVER_DEV_EXPIRE  "Config.DoorSetting.CLOUDSERVER.DeviceExpire="
#define  CONFIG_CLOUDSERVER_TYPE  "Config.DoorSetting.CLOUDSERVER.Type="
//1代表新小区 0代表旧小区，默认是0
#define  CONFIG_CLOUDSERVER_COMMUNITY_TYPE   "Config.DoorSetting.CLOUDSERVER.NewComm="
#define  CLOUD_SERVER_TYPE_PERSONAL            "0"
#define  CLOUD_SERVER_TYPE_PERSONAL_COMMUNITY  "1"
#define  CLOUD_SERVER_TYPE_COMMUNITY           "2" //社区个人
#define  CLOUD_SERVER_TYPE_COMMUNITY_UNIT      "3" //梯口
#define  CLOUD_SERVER_TYPE_COMMUNITY_PUBLIC    "4" //公共设备

//V4.1
#define CONFIG_FEATURES_CALLROBIN_NUM "Config.Features.CALLROBIN.Num="
#define CONFIG_FEATURES_CALLROBIN_ENABLE "Config.Features.CALLROBIN.Enable="
#define CONFIG_FEATURES_CALLROBIN_TIME "Config.Features.CALLROBIN.Time="
#define CONFIG_DOORSETTING_MOTION_DETECT_ENABLE "Config.DoorSetting.MOTION_DETECT.Enable="
#define CONFIG_DOORSETTING_MOTION_DETECT_TIME "Config.DoorSetting.MOTION_DETECT.Interval="

//V4.2
//dtmf 是否根据联系人判断  1根据联系人           0全部允许
#define CONFIG_CLOUDSERVER_DOOR_OPEN_LIMIT "Config.DoorSetting.CLOUDSERVER.OpenDoorLimit="
//梯口机设备显示控制
#define CONFIG_CONTACT_SHOW_TEYP "Config.DoorSetting.GENERAL.ContactViewShowChild="

//V4.3 社区添加街道和地址
#define CONFIG_COMMUNITY_NAME "Config.DoorSetting.DISPLAY.CommunityName="
#define CONFIG_COMMUNITY_STREET "Config.DoorSetting.DISPLAY.Street="

#define CONFIG_IDCARD_ENABLE "Config.DoorSetting.GENERAL.IDCardVerificationEnable="


//V4.6 平台时区配置
#define CONFIG_CLOUDSERVER_TOMEZONE "Config.Settings.SNTP.TimeZone="
#define CONFIG_CLOUDSERVER_CITYNAME "Config.Settings.SNTP.Name="
#define CONFIG_CLOUDSERVER_DATEFORMAT "Config.Settings.DATETIME.DateFormat="
#define CONFIG_CLOUDSERVER_TIMEFORMAT "Config.Settings.DATETIME.TimeFormat="

#define CONFIG_NEW_TIMEZONE_NUUK    "Nuuk"
#define CONFIG_NEW_TIMEZONE_KOLKATA "Kolkata"

//relay
#define CONFIG_RELAY_DMTF_OPTION "Config.DoorSetting.DTMF.Option="//dtmf 几个键 0=1个按键
#define CONFIG_RELAY_DMTF_CODE1 "Config.DoorSetting.DTMF.Code1=" //11 //11=#  10=*
#define CONFIG_RELAY_DMTF_CODE2 "Config.DoorSetting.DTMF.Code2="
#define CONFIG_RELAY_DMTF_CODE3 "Config.DoorSetting.DTMF.Code3="
#define CONFIG_RELAY_DMTF_CODE4 "Config.DoorSetting.DTMF.Code4="

#define CONFIG_RELAY_DMTF_NAME1 "Config.DoorSetting.RELAY.NameA="
#define CONFIG_RELAY_DMTF_NAME2 "Config.DoorSetting.RELAY.NameB="
#define CONFIG_RELAY_DMTF_NAME3 "Config.DoorSetting.RELAY.NameC="
#define CONFIG_RELAY_DMTF_NAME4 "Config.DoorSetting.RELAY.NameD="

//6.7新增relay开门方式设置
#define CONFIG_RELAY_RELAYUNLOCK1 "Config.DoorSetting.RELAYUNLOCK.RelayA="
#define CONFIG_RELAY_RELAYUNLOCK2 "Config.DoorSetting.RELAYUNLOCK.RelayB="
#define CONFIG_RELAY_RELAYUNLOCK3 "Config.DoorSetting.RELAYUNLOCK.RelayC="
#define CONFIG_RELAY_RELAYUNLOCK4 "Config.DoorSetting.RELAYUNLOCK.RelayD="

//6.7新增relay schedule下发
#define CONFIG_RELAY_RELAYSCHEDULE_ENABLE1 "Config.DoorSetting.RELAYSCHEDULE.CloudRelayAEnable="
#define CONFIG_RELAY_RELAYSCHEDULE_ENABLE2 "Config.DoorSetting.RELAYSCHEDULE.CloudRelayBEnable="
#define CONFIG_RELAY_RELAYSCHEDULE_ENABLE3 "Config.DoorSetting.RELAYSCHEDULE.CloudRelayCEnable="
#define CONFIG_RELAY_RELAYSCHEDULE_ENABLE4 "Config.DoorSetting.RELAYSCHEDULE.CloudRelayDEnable="

#define CONFIG_RELAY_RELAYSCHEDULE1 "Config.DoorSetting.RELAYSCHEDULE.CloudRelayASchedule="
#define CONFIG_RELAY_RELAYSCHEDULE2 "Config.DoorSetting.RELAYSCHEDULE.CloudRelayBSchedule="
#define CONFIG_RELAY_RELAYSCHEDULE3 "Config.DoorSetting.RELAYSCHEDULE.CloudRelayCSchedule="
#define CONFIG_RELAY_RELAYSCHEDULE4 "Config.DoorSetting.RELAYSCHEDULE.CloudRelayDSchedule="


//security relay
#define CONFIG_SECURITY_RELAY_DMTF_CODE1 "Config.DoorSetting.DTMF.CodeSA=" //11 //11=#  10=*
#define CONFIG_SECURITY_RELAY_DMTF_CODE2 "Config.DoorSetting.DTMF.CodeSB="
#define CONFIG_SECURITY_RELAY_DMTF_CODE3 "Config.DoorSetting.DTMF.CodeSC="
#define CONFIG_SECURITY_RELAY_DMTF_CODE4 "Config.DoorSetting.DTMF.CodeSD="

#define CONFIG_SECURITY_RELAY_DMTF_NAME1 "Config.DoorSetting.RELAY.NameSA="
#define CONFIG_SECURITY_RELAY_DMTF_NAME2 "Config.DoorSetting.RELAY.NameSB="
#define CONFIG_SECURITY_RELAY_DMTF_NAME3 "Config.DoorSetting.RELAY.NameSC="
#define CONFIG_SECURITY_RELAY_DMTF_NAME4 "Config.DoorSetting.RELAY.NameSD="

#define CONFIG_SECURITY_RELAY_ENABLED1  "Config.DoorSetting.RELAY.SecurityRelayAEnabled="
#define CONFIG_SECURITY_RELAY_ENABLED2  "Config.DoorSetting.RELAY.SecurityRelayBEnabled=" 
#define CONFIG_SECURITY_RELAY_ENABLED3  "Config.DoorSetting.RELAY.SecurityRelayCEnabled=" 
#define CONFIG_SECURITY_RELAY_ENABLED4  "Config.DoorSetting.RELAY.SecurityRelayDEnabled=" 

//6.7新增security relay开门方式设置
#define CONFIG_SECURITY_RELAY_RELAYUNLOCK1 "Config.DoorSetting.RELAYUNLOCK.SeRelayA="
#define CONFIG_SECURITY_RELAY_RELAYUNLOCK2 "Config.DoorSetting.RELAYUNLOCK.SeRelayB="
#define CONFIG_SECURITY_RELAY_RELAYUNLOCK3 "Config.DoorSetting.RELAYUNLOCK.SeRelayC="
#define CONFIG_SECURITY_RELAY_RELAYUNLOCK4 "Config.DoorSetting.RELAYUNLOCK.SeRelayD="

//视频存储
#define CONFIG_VIDEO_RECORD_ENABLE              "Config.DoorSetting.VIDEORECORD.Enabled="
#define CONFIG_VIDEO_RECORD_CLOUD               "Config.DoorSetting.VIDEORECORD.Cloud="
#define CONFIG_VIDEO_RECORD_LENGTH              "Config.DoorSetting.VIDEORECORD.Length="
#define CONFIG_VIDEO_RECORD_ACCESS_GRANTED      "Config.DoorSetting.VIDEORECORD.AccessGranted="
#define CONFIG_VIDEO_RECORD_ACCESS_DENIED       "Config.DoorSetting.VIDEORECORD.AccessDenied="
#define CONFIG_VIDEO_RECORD_MOTION              "Config.DoorSetting.VIDEORECORD.Motion="
#define CONFIG_VIDEO_RECORD_TAMPER              "Config.DoorSetting.VIDEORECORD.Tamper="
#define CONFIG_VIDEO_RECORD_OPENDOOR_ALARM      "Config.DoorSetting.VIDEORECORD.OpenDoorAlarm="
#define CONFIG_VIDEO_RECORD_CALL_IN             "Config.DoorSetting.VIDEORECORD.CallIn="
#define CONFIG_VIDEO_RECORD_CALL_OUT            "Config.DoorSetting.VIDEORECORD.CallOut="
#define CONFIG_VIDEO_RECORD_PACKAGE             "Config.DoorSetting.VIDEORECORD.Package="
#define CONFIG_VIDEO_RECORD_BREAK_IN            "Config.DoorSetting.VIDEORECORD.BreakIn="
#define CONFIG_VIDEO_RECORD_SOUND               "Config.DoorSetting.VIDEORECORD.Sound="
#define CONFIG_VIDEO_RECORD_INCLUDE_AUDIO       "Config.DoorSetting.VIDEORECORD.IncludeAudio="

//负楼层下发
#define CONFIG_RELAY_GROUND_FLOOR               "Config.DoorSetting.RELAY.GroundFloor="
#define CONFIG_RELAY_FLOOR_STARTS_FROM          "Config.DoorSetting.RELAY.FloorStartsFrom="
#define CONFIG_LIFTCONTROL_GROUND_FLOOR         "Config.DoorSetting.LIFTCONTROL.GroundFloor="
#define CONFIG_LIFTCONTROL_FLOOR_STARTS_FROM    "Config.DoorSetting.LIFTCONTROL.FloorStartsFrom=" 
#define CONFIG_GROUND_FLOOR_NONE                "0"
#define CONFIG_GROUND_FLOOR_G0                  "1"
#define CONFIG_GROUND_FLOOR_G0_G1               "2"
#define CONFIG_GROUND_FLOOR_G0_G1_G2            "3"

//室内机外籍relay
// Device1 配置定义
#define CONFIG_INDOOR_EXTRELAY_DEVICE_ENABLE          "Config.Indoor.EXTRELAY.DeviceEnable"
#define CONFIG_INDOOR_EXTRELAY_DEVICE_ADDRESS         "Config.Indoor.EXTRELAY.DeviceAddress"
#define CONFIG_INDOOR_EXTRELAY_STATUS                 "Config.Indoor.EXTRELAY.Status"
#define CONFIG_INDOOR_EXTRELAY_DISPLAY_NAME           "Config.Indoor.EXTRELAY.DisplayName"
#define CONFIG_INDOOR_EXTRELAY_FUNCTION               "Config.Indoor.EXTRELAY.Function"
#define CONFIG_INDOOR_EXTRELAY_HOLD_DELAY             "Config.Indoor.EXTRELAY.HoldDelay"
#define CONFIG_INDOOR_EXTRELAY_INTERVAL               "Config.Indoor.EXTRELAY.Interval"

#define CONFIG_INDOOR_DIGITAL_OUTPUT_STATUS           "Config.Indoor.DIGITALOUTPUT.Status"
#define CONFIG_INDOOR_DIGITAL_OUTPUT_DISPLAY_NAME     "Config.Indoor.DIGITALOUTPUT.DisplayName"
#define CONFIG_INDOOR_DIGITAL_OUTPUT_FUNCTION         "Config.Indoor.DIGITALOUTPUT.Function"
#define CONFIG_INDOOR_DIGITAL_OUTPUT_HOLD_DELAY       "Config.Indoor.DIGITALOUTPUT.HoldDelay"
#define CONFIG_INDOOR_DIGITAL_OUTPUT_INTERVAL         "Config.Indoor.DIGITALOUTPUT.Interval"

#define CONFIG_INDOOR_DIGITALINPUT_LINK_RELAY_OR_OUTPUT "Config.Indoor.DIGITALINPUT.LinkRelayorOutput"
#define CONFIG_INDOOR_DIGITALINPUT_STATUS               "Config.Indoor.DIGITALINPUT.Status"
#define CONFIG_INDOOR_DIGITALINPUT_SHOW_POPUP           "Config.Indoor.DIGITALINPUT.ShowPopup"
#define CONFIG_INDOOR_DIGITALINPUT_TRIGGER_MODE         "Config.Indoor.DIGITALINPUT.TriggerMode"
#define CONFIG_INDOOR_DIGITALINPUT_DISPLAY_NAME         "Config.Indoor.DIGITALINPUT.DisplayName"

// Device2 配置定义
#define CONFIG_INDOOR_EXTRELAY2_DEVICE_ENABLE         "Config.Indoor.EXTRELAY2.DeviceEnable"
#define CONFIG_INDOOR_EXTRELAY2_DEVICE_ADDRESS        "Config.Indoor.EXTRELAY2.DeviceAddress"
#define CONFIG_INDOOR_EXTRELAY2_STATUS                "Config.Indoor.EXTRELAY2.Status"
#define CONFIG_INDOOR_EXTRELAY2_DISPLAY_NAME          "Config.Indoor.EXTRELAY2.DisplayName"
#define CONFIG_INDOOR_EXTRELAY2_FUNCTION              "Config.Indoor.EXTRELAY2.Function"
#define CONFIG_INDOOR_EXTRELAY2_HOLD_DELAY            "Config.Indoor.EXTRELAY2.HoldDelay"
#define CONFIG_INDOOR_EXTRELAY2_INTERVAL              "Config.Indoor.EXTRELAY2.Interval"

#define CONFIG_INDOOR_DIGITAL_OUTPUT2_STATUS          "Config.Indoor.DIGITALOUTPUT2.Status" 
#define CONFIG_INDOOR_DIGITAL_OUTPUT2_DISPLAY_NAME    "Config.Indoor.DIGITALOUTPUT2.DisplayName" 
#define CONFIG_INDOOR_DIGITAL_OUTPUT2_FUNCTION        "Config.Indoor.DIGITALOUTPUT2.Function" 
#define CONFIG_INDOOR_DIGITAL_OUTPUT2_HOLD_DELAY      "Config.Indoor.DIGITALOUTPUT2.HoldDelay"
#define CONFIG_INDOOR_DIGITAL_OUTPUT2_INTERVAL         "Config.Indoor.DIGITALOUTPUT2.Interval"

#define CONFIG_INDOOR_DIGITALINPUT2_LINK_RELAY_OR_OUTPUT "Config.Indoor.DIGITALINPUT2.LinkRelayorOutput"
#define CONFIG_INDOOR_DIGITALINPUT2_STATUS               "Config.Indoor.DIGITALINPUT2.Status"
#define CONFIG_INDOOR_DIGITALINPUT2_SHOW_POPUP           "Config.Indoor.DIGITALINPUT2.ShowPopup"
#define CONFIG_INDOOR_DIGITALINPUT2_TRIGGER_MODE         "Config.Indoor.DIGITALINPUT2.TriggerMode"
#define CONFIG_INDOOR_DIGITALINPUT2_DISPLAY_NAME         "Config.Indoor.DIGITALINPUT2.DisplayName"

// Device3 配置定义
#define CONFIG_INDOOR_EXTRELAY3_DEVICE_ENABLE         "Config.Indoor.EXTRELAY3.DeviceEnable"
#define CONFIG_INDOOR_EXTRELAY3_DEVICE_ADDRESS        "Config.Indoor.EXTRELAY3.DeviceAddress"
#define CONFIG_INDOOR_EXTRELAY3_STATUS                "Config.Indoor.EXTRELAY3.Status"
#define CONFIG_INDOOR_EXTRELAY3_DISPLAY_NAME          "Config.Indoor.EXTRELAY3.DisplayName"
#define CONFIG_INDOOR_EXTRELAY3_FUNCTION              "Config.Indoor.EXTRELAY3.Function"
#define CONFIG_INDOOR_EXTRELAY3_HOLD_DELAY            "Config.Indoor.EXTRELAY3.HoldDelay"
#define CONFIG_INDOOR_EXTRELAY3_INTERVAL              "Config.Indoor.EXTRELAY3.Interval"

#define CONFIG_INDOOR_DIGITAL_OUTPUT3_STATUS          "Config.Indoor.DIGITALOUTPUT3.Status" 
#define CONFIG_INDOOR_DIGITAL_OUTPUT3_DISPLAY_NAME    "Config.Indoor.DIGITALOUTPUT3.DisplayName" 
#define CONFIG_INDOOR_DIGITAL_OUTPUT3_FUNCTION        "Config.Indoor.DIGITALOUTPUT3.Function" 
#define CONFIG_INDOOR_DIGITAL_OUTPUT3_HOLD_DELAY      "Config.Indoor.DIGITALOUTPUT3.HoldDelay"
#define CONFIG_INDOOR_DIGITAL_OUTPUT3_INTERVAL         "Config.Indoor.DIGITALOUTPUT3.Interval"

#define CONFIG_INDOOR_DIGITALINPUT3_LINK_RELAY_OR_OUTPUT "Config.Indoor.DIGITALINPUT3.LinkRelayorOutput"
#define CONFIG_INDOOR_DIGITALINPUT3_STATUS               "Config.Indoor.DIGITALINPUT3.Status"
#define CONFIG_INDOOR_DIGITALINPUT3_SHOW_POPUP           "Config.Indoor.DIGITALINPUT3.ShowPopup"
#define CONFIG_INDOOR_DIGITALINPUT3_TRIGGER_MODE         "Config.Indoor.DIGITALINPUT3.TriggerMode"
#define CONFIG_INDOOR_DIGITALINPUT3_DISPLAY_NAME         "Config.Indoor.DIGITALINPUT3.DisplayName"



//双摄像头设备的辅摄像头配置
#define CONFIG_RTSP_AUX_CAMERA_H264_RESOLUTION            "Config.DoorSetting.RTSP.AUXH264Resolution1="
#define CONFIG_RTSP_AUX_CAMERA_H264_FRAMERATE             "Config.DoorSetting.RTSP.AUXH264FrameRate1="
#define CONFIG_RTSP_AUX_CAMERA_H264_BITRATE               "Config.DoorSetting.RTSP.AUXH264BitRate1="

#define CONFIG_NETWORK_DATAUSAGE_DATATYPE "Config.Network.DATAUSAGE.DataType="

#define CONFIG_ANALOG_TYPE               "Config.DoorSetting.ANALOG.Type="

#define CONFIG_RTSPS_ENABLE               "Config.DoorSetting.RTSP.RTSPSEnable="

#define HTTPROOT            "/var/www/" //chang by chenzhx V4.0

#define CONFFILEPATH        "/usr/local/akcs/csconfig/conf/csconfig.conf"

#define HTTP_DOWNLOAD       "/var/www/download"


#define PERSONNAL_DOWNLOAD  "/download/personal/" //chang by chenzhx V4.0 去掉_download
#define COMMUNITY_DOWNLOAD  "/download/community/" //chang by chenzhx V4.0 去掉_download

#define USER_DETAIL_DOWNLOAD "/download/UserDetail/"



//SOCKET相关
#define SOCKET_MULTICAST_PORT       8500
#define SOCKET_TCP_LISTEN_PORT      8501


#define CSCONFIG_CONF_COMMON_LEN 64

typedef struct CSCONFIG_CONF_T
{
    /* 本机IP配置信息 */
    char server_outer_ip[CSCONFIG_CONF_COMMON_LEN];
    char server_inner_ip[CSCONFIG_CONF_COMMON_LEN];
    char server_hostname[CSCONFIG_CONF_COMMON_LEN];

    /* 本机配置信息 */
    char csconfig_outer_ip[CSCONFIG_CONF_COMMON_LEN];
    int log_level; //日志打印级别 LOG_LEVEL_E
    char log_file[CSCONFIG_CONF_COMMON_LEN];

    /* cspbx本机配置信息 */
    char cspbx_outer_ip[CSCONFIG_CONF_COMMON_LEN];
    char cspbx_outer_port[CSCONFIG_CONF_COMMON_LEN];

    /* DB配置项 */
    char db_ip[CSCONFIG_CONF_COMMON_LEN];
    char db_username[CSCONFIG_CONF_COMMON_LEN];
    char db_password[CSCONFIG_CONF_COMMON_LEN];
    char db_database[CSCONFIG_CONF_COMMON_LEN];
    char db_socket_file[CSCONFIG_CONF_COMMON_LEN]; //如果这个文件不为空 代表用unix连接
    int  db_port;
    /*OEM 配置*/
    char oem_config[1024];

    /*远程服务器ssh代理地址*/
    char ssh_proxy_domain[128];

    int no_encrypt;
    char nsq_topic_for_del_pic[32];
    char nsq_route_topic[CSCONFIG_CONF_COMMON_LEN];//跟route的通信topic
    char etcd_server_addr[CSCONFIG_CONF_COMMON_LEN];
    char beanstalk_addr[CSCONFIG_CONF_COMMON_LEN];
    char beanstalk_tube[CSCONFIG_CONF_COMMON_LEN];
    char beanstalk_backup_ip[CSCONFIG_CONF_COMMON_LEN];
    char web_ip[CSCONFIG_CONF_COMMON_LEN];    
    char ftp_ip[CSCONFIG_CONF_COMMON_LEN];
    char vrtsp_server_domain[CSCONFIG_CONF_COMMON_LEN];
    char fdfs_config_addr[CSCONFIG_CONF_COMMON_LEN];    

    int config_server_domain_gray_percentage;
    char config_server_domain[CSCONFIG_CONF_COMMON_LEN];

    //Area list:1)ccloud 2)scloud 3)ecloud 4)ucloud 5)other 6)rcloud    
    int server_type;
    char community_ids[128];
    //是否亚马逊云
    int is_aws; 
    char aws_db_ip[CSCONFIG_CONF_COMMON_LEN];
    //重复刷userinfo超时时间 30s
    int repeated_userdetail_timeout;
    //重复刷ipchange超时时间 120s
    int repeated_ipchange_timeout;
    //web重复刷配置超时时间60秒
    int repeated_web_timeout;        
    int is_store_fdfs;
    
    char ip_change_filter[256];
    char user_info_filter[256];
    char mng_id_filter[256];
    char ip_change_mng_id_filter[256];

    int check_big_project_handle_time;

    int write_thread_number;
    int write_file_number;

    int enable_db_confusion_cache;

    int write_dev_work_thread_num;

    char kafka_broker_ip[64];
    char notify_csconfig_topic[64];
    char notify_csconfig_topic_group[64];
    int  notify_csconfig_topic_thread;
    char notify_csconfig_user_detail_topic[64];
    char notify_csconfig_user_detail_topic_group[64];
    int  notify_csconfig_user_detail_topic_thread;
    int write_config_heap_up_num;
    int write_config_batch_read_num;
    int write_config_community_overflow_threshold;
} CSCONFIG_CONF;


#endif// __ADAPT_DEFINE_H__

