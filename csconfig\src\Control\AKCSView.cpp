#include <stdio.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <errno.h>
#include <pthread.h>
#include <assert.h>
#include <string.h>
#include <ctype.h>
#include <vector>
#include <string>
#include <sstream>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "ConfigDef.h"
#include "AKCSMsg.h"
#include "AdaptUtility.h"
#include "AkcsWebMsgSt.h"
#include "PrivateKeyControl.h"
#include "RfKeyControl.h"
#include "DeviceControl.h"
#include "IPCControl.h"
#include "AKCSView.h"
#include "AkLogging.h"
#include "redis/PubSubManager.h"
#include "PersonalAccount.h"
#include "util.h"
#include "PersonnalDeviceSetting.h"
#include "CommunityMng.h"
#include <evpp/evnsq/producer.h>
#include "DeviceSetting.h"
#include "dbinterface/CommunityInfo.h"
#include "AK.Adapt.pb.h"
#include "AK.Crontab.pb.h"
#include "AkcsMsgDef.h"
#include "AK.Server.pb.h"
#include "AkcsPduBase.h"
#include "MQProduce.h"
#include "FileUpdateControl.h"
#include "PerFileUpdateControl.h"
#include "ConfigFileReader.h"
#include "json/json.h"
#include "DevUser.h"
#include "DevSchedule.h"
#include <boost/algorithm/string.hpp>
#include "DataAnalysis.h"
#include "AkcsCommonDef.h"
#include "AkcsMonitor.h"
#include "dbinterface/office/OfficePersonalAccount.h"
#include "CachePool.h"
#include "dbinterface/Delivery.h"
#include "dbinterface/Staff.h"
#include "CommConfigHandle.h"
#include "CommunityDevContact.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "CommonHandle.h"
#include "dbinterface/ProjectUserManage.h"
#include "SnowFlakeGid.h"
#include "AkcsWebPduBase.h"
#include "SpecialTubeHandle.h"
#include "dbinterface/AccessGroupDB.h"
#include "dbinterface/resident/ResidentDevices.h"


extern PubSubManager* g_pub_sub_mng_ptr;
extern CSCONFIG_CONF gstCSCONFIGConf;
extern evnsq::Producer* g_nsq_pub_mng_ptr;
extern RouteMQProduce* g_nsq_producer;
extern const char* g_redis_db_userdetail;
static const char channel_pic_del[] = "ak_redis_pic_del";

#define PHP_MAIL_TO_RESET_PASSWD  "php /usr/local/akcs/csadapt/scripts/adapt.mail.php "
#define PHP_MAIL_TO_CREATE_UID  "php /usr/local/akcs/csadapt/scripts/create_uid.mail.php "
#define PHP_MAIL_TO_CHANGE_PWD  "php /usr/local/akcs/csadapt/scripts/change_pwd.mail.php "

CAKCSView::CAKCSView()
{

}

CAKCSView::~CAKCSView()
{

}

CAKCSView* CAKCSView::instance = NULL;

CAKCSView* CAKCSView::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CAKCSView();
    }

    return instance;
}
CAKCSView* GetAKCSViewInstance()
{
    return CAKCSView::GetInstance();
}

/*单个mac, 通知mac*/
void CAKCSView::NotifyMacChange(const std::string& mac)
{
    CSP2A_CONFIG_FILE_CHANGE file_change;
    memset(&file_change, 0, sizeof(file_change));
    file_change.nNotifyType = CONFIG_FILE_CHANGE_NOTIFY_TYPE_MAC;
    file_change.type |= CONFIG_FILE_CHANGE_TYPE_CONTACT;
    file_change.type |= CONFIG_FILE_CHANGE_TYPE_CONFIG;
    snprintf(file_change.mac, sizeof(file_change.mac), "%s", mac.c_str());
    if (GetIPCControlInstance()->SendConfigFileChange(&file_change) != 0)
    {
        AK_LOG_WARN << "Send contact change failed";
    }
}

/*整个社区更新, 通知社区所有设备*/
void CAKCSView::NotifyCommunityChange(uint32_t mng_id)
{
    CSP2A_CONFIG_FILE_CHANGE file_change;
    memset(&file_change, 0, sizeof(file_change));
    file_change.nNotifyType = CONFIG_FILE_CHANGE_NOTIFY_TYPE_COMMUNITY;
    file_change.mng_id = mng_id;
    if (GetIPCControlInstance()->SendConfigFileChange(&file_change) != 0)
    {
        AK_LOG_WARN << "Send contact change failed";
    }
}

void CAKCSView::NotifyDelCommunityPics(uint32_t mng_id)
{
    AK::Server::GroupAdaptConfFileChangeMsg msg;
    msg.set_mng_id(mng_id);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_P2A_NOTIFY_COMMUNITY_MESSAGE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSCONFIGConf.nsq_topic_for_del_pic);
}

void CAKCSView::NotifyDelPersonalPics(const std::string& pic_url)
{
    AK::Adapt::PerDelPic msg;
    msg.set_pic_url(pic_url);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_P2A_PERSONNAL_DEL_PIC);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSCONFIGConf.nsq_topic_for_del_pic);
}

void CAKCSView::NotifyPerDelDevPics(const std::string& mac)
{
    AK::Adapt::PerDelDev msg;
    msg.set_mac(mac); 

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_P2A_PERSONNAL_DEL_DEV);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSCONFIGConf.nsq_topic_for_del_pic);
}


/*单元公共设备更新, 通知单元公共设备(不包括租户)*/
void CAKCSView::NotifyDevFileChange(const std::string &mac, int type, DULONG traceid, const std::string &file, const std::string 
&file_md5 )
{
    CSP2A_DEV_FILE_CHANGE file_change;
    memset(&file_change, 0, sizeof(file_change));
    file_change.type = type;
    file_change.traceid = traceid;
    Snprintf(file_change.mac, sizeof(file_change.mac), mac.c_str());
    Snprintf(file_change.file_path, sizeof(file_change.file_path), file.c_str());
    Snprintf(file_change.file_md5, sizeof(file_change.file_md5), file_md5.c_str());
    if (GetIPCControlInstance()->SendDevFileChange(&file_change) != 0)
    {
        AK_LOG_WARN << "NotifyDevFileChange failed, mac=" << mac;
    }
}

void CAKCSView::NotifyAppRefreshConfig(const std::string &account, int project_type)
{
    AK::Server::P2PAdaptNotifyAppRefreshConfigMsg msg;
    msg.set_account(account);
    msg.set_project_type(project_type);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_REFRESH_APP_CONF);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSCONFIGConf.nsq_route_topic);
}

void CAKCSView::UpdateMacConfigByCsmain(int changetype, const char* pMac, const std::string& ip)
{  //TODO:后期在区分
    DEVICE_SETTING* dev = GetDeviceControlInstance()->GetDeviceSettingByMac(pMac);
    if (!dev)
    {
        AK_LOG_WARN << "Devices is not exist, mac=" <<  pMac;
        return;
    }
    AK_LOG_INFO << "UpdateMacConfigByCsmain mac=" <<  dev->mac << " changetype=" << changetype << " ip: " << ip;
    std::string strmac = dev->mac;
    std::vector<std::string> macs = {strmac};
    
    int new_change_type = 0;
    AK::Adapt::WebCommunityModifyNotify new_msg;
    if (dev->is_personal == 1)
    {
        switch (changetype)
        {
            case CSMAIN_UPDATE_CONFIG_IP_CHANGE://ip变化
            {
                new_change_type = CSMAIN_PER_DEV_IP_CHANGE;
                break;
            }
            case CSMAIN_UPDATE_CONFIG_MAINTANCE://运维的接口
            {
                new_change_type = CSMAIN_PER_DEV_MAINTANCE;
                break;
            }
            case CSMAIN_UPDATE_CONFIG_UPGRADE://版本升级
            {
                new_change_type = CSMAIN_PER_DEV_UPGRADE;
                break;
            }
        } 
        std::vector<std::string> macs = {strmac};
        GetPerFileUpdateContorlInstance()->PersonalFileHandle(new_change_type, dev->device_node, macs);
        return;
    }
    else
    {
        switch (changetype)
        {
            case CSMAIN_UPDATE_CONFIG_IP_CHANGE://ip变化
            {
                if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
                {
                    new_change_type = CSMAIN_COMM_PUB_DEV_IP_CHANGE;
                }
                else if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
                {
                    new_change_type = CSMAIN_COMM_UNIT_DEV_IP_CHANGE;
                }
                else if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
                {
                    new_change_type = CSMAIN_COMM_DEV_IP_CHANGE;
                }
                break;
            }
            case CSMAIN_UPDATE_CONFIG_MAINTANCE://运维的接口
            {
                if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
                {
                    new_change_type = CSMAIN_COMM_PUB_DEV_MAINTANCE;
                }
                else if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
                {
                    new_change_type = CSMAIN_COMM_UNIT_DEV_MAINTANCE;
                }
                else if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
                {
                    new_change_type = CSMAIN_COMM_DEV_MAINTANCE;
                }

                break;
            }
            case CSMAIN_UPDATE_CONFIG_UPGRADE://版本升级
            {
                if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
                {
                    new_change_type = CSMAIN_COMM_PUB_DEV_UPGRADE;
                }
                else if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
                {
                    new_change_type = CSMAIN_COMM_UNIT_DEV_UPGRADE;
                }
                else if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
                {
                    new_change_type = CSMAIN_COMM_DEV_UPGRADE;
                }

                break;
            }    
        }    
    }
    
    
    GetFileUpdateContorlInstance()->CommunityFileUpdateFormateWeb(new_change_type, dev->manager_account_id, dev->unit_id, 
        dev->device_node, macs);

    GetDeviceControlInstance()->DestoryDeviceSettingList(dev);
}

void CAKCSView::UpdateDevAccountConfigByCsmain(int changetype, const std::string &node, const std::string& account, 
    int account_role, int manager_id, int unit_id)
{
    int new_change_type = 0;
    int project_type = project::NONE;
    AK_LOG_INFO << "UpdateAccountConfig account:" << account << "account role: " << account_role << "node: " << node; 
    if((account_role == ACCOUNT_ROLE_PERSONNAL_MAIN) || (account_role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT))
    {
        switch (changetype)
        {
            case CSMAIN_UPDATE_CONFIG_RF_CHANGE:
            {
                new_change_type = WEB_PER_UPDATE_RF;
                break;
            }
        }
        std::vector<std::string> macs = {};
        AK_LOG_INFO << "UpdateAccountConfig Personal Account: Account=" <<  account;
        GetPerFileUpdateContorlInstance()->PersonalFileHandle(new_change_type, node, macs);
        project_type = project::PERSONAL;
    }
    else if((account_role == ACCOUNT_ROLE_COMMUNITY_MAIN) || (account_role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT) || (account_role == ACCOUNT_ROLE_COMMUNITY_PM))
    {
        switch (changetype)
        {
            case CSMAIN_UPDATE_CONFIG_RF_CHANGE:
            {
                new_change_type = CSMAIN_COMM_ACCOUNT_NFC_UPDATE;
                break;
            }
        }
        std::vector<std::string> macs = {};
        //更新用户数据版本
        AK_LOG_INFO << "UpdateAccountConfig Community Account: Account=" <<  account;
        dbinterface::ProjectUserManage::UpdataDataVersionByAccount(account);
        GetFileUpdateContorlInstance()->CommunityFileUpdateFormateWeb(new_change_type, manager_id, unit_id, node, macs);
        project_type = project::RESIDENCE;
    }

    NotifyAppRefreshConfig(account, project_type);
}

void CAKCSView::WriteMacUserInfoByCsmain(const std::string &mac, const UserUUIDList &list, DULONG traceid)
{
    DEVICE_SETTING* dev = GetDeviceControlInstance()->GetDeviceSettingByMac(mac);
    if (!dev)
    {
        AK_LOG_WARN << "WriteMacUserInfoByCsmain: Devices is not exist, mac=" <<  mac;
        return;
    }
    if (list.size() == 0)
    {
        AK_LOG_WARN << "WriteMacUserInfoByCsmain: list is null, mac=" <<  mac;
        return;
    }

    CommunityInfoPtr comm_info = std::make_shared<CommunityInfo>(dev->manager_account_id);
    std::string file_path;
    std::string file_md5;
    DevUser user(comm_info);
    user.GetDetailDataForRequest(dev, list, traceid, file_path, file_md5);
    GetDeviceControlInstance()->DestoryDeviceSettingList(dev);
    if(file_md5.size() > 0)
    {
        GetAKCSViewInstance()->NotifyDevFileChange(mac, DEV_FILE_CHANGE_NOTIFY_USER_INFO, traceid, file_path, file_md5);
    }
}

//因为权限组会删除，此时后台没有办法通过权限组查找设备，所以直接让前端传对应的mac
void CAKCSView::OnAccessGroupModify(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnAccessGroupModify The param is NULL";
        return;
    }
    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    AK::Adapt::AccessGroupModifyNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    AK_LOG_INFO << "OnAccessGroupModify msg=" << msg.DebugString() << " traceid=" << traceid;

    int mng_id = msg.community_id();
    if (SpecialTubeHandle::GetInstance().CheckIsFilter(mng_id))
    {
        AK_LOG_INFO << "OnAccessGroupModify return " << mng_id;    
        return;
    }

    CommunityInfoPtr comm_info = std::make_shared<CommunityInfo>(msg.community_id());
    if (!comm_info->GetIsNew())
    {
        AK_LOG_INFO << "communitid [" << msg.community_id() << "] not new commynity, not required update user file";
        return;
    }

    std::time_t tstart = std::time(0);
    
    //更新schedule
    std::set<std::string> macs;
    int size = msg.mac_list_size();
    if (size == 0)
    {
        //可能是默认权限组
        AK_LOG_INFO << "OnAccessGroupModify mac is null, maybe default accessgroup.";
        dbinterface::AccessGroup::GetMacListByAccessGroupID(msg.access_group_id(), macs);
    }
    
    for (int i = 0; i < size; i++)
    {
        const std::string mac = msg.mac_list(i);
        macs.insert(mac);
    }
    
    DEVICE_SETTING* dev_list = GetDeviceSettingInstance()->GetDeviceSettingByMacList(macs);    
    DevSchedule schedule(comm_info);
    schedule.UpdateScheduleData(dev_list);    

    //更新user
    DevUser user(comm_info);
    user.UpdateMetaData(dev_list);

    //update contact list
    //更新被从权限组删除的node下的所有设备的联系人列表
    //******** del_userlist这个参数更新为：新增和删除用户列表都用这个标识。
    std::vector<std::string> del_users;
    ResidentPerAccountList account_list;
    std::set<std::string> user_mac_set;
    int user_size = msg.del_userlist_size();
    if (user_size)
    {
        for (int i = 0; i < user_size; i++)
        {
            const std::string del_node = msg.del_userlist(i);
            //更新用户数据版本
            dbinterface::ProjectUserManage::UpdateDataVersionByNode(del_node);

            ResidentPerAccount account;
            memset(&account, 0, sizeof(account));
            if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(del_node, account))
            {
                account_list.push_back(account);
            }
            del_users.push_back(del_node);
        }
        dbinterface::ResidentDevices::GetNodesDevList(del_users, user_mac_set);
    }

    UpdateCommunityAccessGroupContactListByAccount(msg.community_id(), account_list);

    GetDeviceControlInstance()->DestoryDeviceSettingList(dev_list);        

    std::time_t tend = std::time(0);
    CommonHandle::CheckBigProject(tstart, tend, mng_id);
    AK_LOG_INFO << "communitid [" << mng_id << "] update file time: " << tend - tstart << "s" << " traceid=" << traceid;
    return;
}

/*社区人员更新*/
void CAKCSView::OnCommunityPersonalModify(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnCommunityPersonalModify The param is NULL";
        return;
    }
    
    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    AK::Adapt::CommunityPersonalModifyNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    AK_LOG_INFO << "OnCommunityPersonalModify msg=" << msg.DebugString() << " traceid=" << traceid;

    int mng_id_tmp = msg.community_id();
    if (SpecialTubeHandle::GetInstance().CheckIsFilter(mng_id_tmp))
    {
        AK_LOG_INFO << "OnCommunityPersonalModify return " << mng_id_tmp;    
        return;
    }

    CommunityInfoPtr comm_info = std::make_shared<CommunityInfo>(msg.community_id());
    if (!comm_info->GetIsNew())
    {
        AK_LOG_INFO << "communitid [" << msg.community_id() << "] not new commynity, not required update user file";
        return;
    }
    int mng_id = msg.community_id();
    std::time_t tstart = std::time(0);

    DevUser user(comm_info);

    std::set<std::string> pub_mac_set;
    std::vector<uint32_t> ag_ids;
    int size = msg.access_group_id_list_size();
    if (size > 0)
    {
        
        for (int i = 0; i < size; i++)
        {
            uint32_t id = msg.access_group_id_list(i);
            if ( id > 0 )
            {
                ag_ids.push_back(id);
            }
        }
    }
    
    if (ag_ids.size() > 0)
    {
       user.UpdatePubDevMetaByAccessGroupID(ag_ids, pub_mac_set);
    }    
    else
    {
        std::vector<uint32_t> staff_ids;
        std::vector<uint32_t> delivery_ids;
        int size = msg.staff_id_list_size();  
        for (int i = 0; i < size; i++)
        {
            staff_ids.push_back(static_cast<uint32_t>(msg.staff_id_list(i)));
        }
        
        size = msg.delivery_id_list_size();
        for (int i = 0; i < size; i++)
        {
            delivery_ids.push_back(static_cast<uint32_t>(msg.delivery_id_list(i)));
        }
        user.UpdatePubDevMetaByPubUser(staff_ids, delivery_ids, pub_mac_set);        
    }
    
    std::time_t tend = std::time(0);
    CommonHandle::CheckBigProject(tstart, tend, mng_id);
    AK_LOG_INFO << "communitid [" << mng_id << "] update file time: " << tend - tstart << "s" << " traceid=" << traceid;
    return;
}

void CAKCSView::OnCommunityAccountModify(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnCommunityAccountModify The param is NULL";
        return;
    }

    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    AK::Adapt::CommunityAccountModifyNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    AK_LOG_INFO << "OnCommunityAccountModify msg=" << msg.DebugString() << " traceid=" << traceid;

    int mng_id_tmp = msg.community_id();
    if (SpecialTubeHandle::GetInstance().CheckIsFilter(mng_id_tmp))
    {
        AK_LOG_INFO << "OnCommunityAccountModify return " << mng_id_tmp;    
        return;
    }

    CommunityInfoPtr comm_info = std::make_shared<CommunityInfo>(msg.community_id());
    if (!comm_info->GetIsNew())
    {
        AK_LOG_INFO << "communitid [" << msg.community_id() << "] not new commynity, not required update user file";
        return;
    }
    std::time_t tstart = std::time(0);
    int mng_id = msg.community_id();
    
    DevUser user(comm_info);

    std::set<std::string> pub_mac_set;
    std::set<std::string> user_mac_set;

    std::vector<std::string> accounts;
    std::vector<std::string> nodes;
    int size = msg.account_list_size();
    for (int i = 0; i < size; i++)
    {
        accounts.push_back(msg.account_list(i));
    }

    size = msg.node_list_size();
    for (int i = 0; i < size; i++)
    {
        nodes.push_back(msg.node_list(i));
    }

    //如果权限组有变化就不需要根据Accounts找对应权限组更新设备
    std::vector<uint32_t> ag_ids;
    size = msg.access_group_id_list_size();
    if (size > 0)
    {
        for (int i = 0; i < size; i++)
        {
            uint32_t id = msg.access_group_id_list(i);
            if ( id > 0 )
            {
                ag_ids.push_back(id);
            }        
        }
    }
    
    if (ag_ids.size() > 0)
    {
        //更新权限组关联的设备user
        user.UpdatePubDevMetaByAccessGroupID(ag_ids, pub_mac_set);
    }
    else
    {
        //更新用户关联的权限组的设备user
        user.UpdatePubDevMetaByAccount(accounts, pub_mac_set);
    }

    //更新用户关联的家庭设备user
    user.UpdateUserDevMetaByNodes(accounts, user_mac_set);

    if(nodes.size() > 0)
    {
        //删除用户：更新家庭设备user
        user.UpdateUserDevMetaByNodes(nodes, user_mac_set);
    }

    //TODO:目前网页没有更新用户权限组（用户设备的权限组）的接口。所以需要更新一遍schedule
    DEVICE_SETTING* devlist = GetDeviceSettingInstance()->GetDeviceSettingByMacList(user_mac_set);
    DevSchedule schedule(comm_info);
    schedule.UpdateScheduleData(devlist);
    GetDeviceControlInstance()->DestoryDeviceSettingList(devlist);      

    std::time_t tend = std::time(0);
    CommonHandle::CheckBigProject(tstart, tend, mng_id);
    AK_LOG_INFO << "APP OnCommunityAccountModify communitid [" << mng_id << "] update file time: " << tend - tstart << "s" << " traceid=" << traceid;
    return;
}

void CAKCSView::OnDataAnalysisNotify(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnDataAnalysisNotify The param is NULL";
        return;
    }

    AK::Adapt::DataAnalysisNotify msg;
    if ((msg_len - CS_COMMON_MSG_HEADER_SIZE) > CS_COMMON_MSG_DATA_SIZE)
    {
        AK_LOG_WARN << "OnDataAnalysisNotify Data length is longer than 1024000";
        std::string error = "DataAnalysisNotify message, Data length is longer than 1024000, data_length = ";
        error += msg_len - CS_COMMON_MSG_HEADER_SIZE;
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csconfig", error, AKCS_MONITOR_ALARM_DATA_ERROR);
        return;
    }
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    AK_LOG_INFO << "DataAnalysisNotify message, Data length is " << msg_len - CS_COMMON_MSG_HEADER_SIZE;

    DataAnalysis da(msg.datas());
    int ret = da.Analysis();
    return;
}

void CAKCSView::OnCommunityImportAccountData(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnCommunityImportAccountData The param is NULL";
        return;
    }

    AK::Adapt::CommunityImportAccountDataNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    AK_LOG_INFO << "OnCommunityImportAccountData msg=" << msg.DebugString();

    CommunityInfoPtr comm_info = std::make_shared<CommunityInfo>(msg.community_id());
    if (!comm_info->GetIsNew())
    {
        AK_LOG_INFO << "communitid [" << msg.community_id() << "] not new commynity, not required update user file";
        return;
    }

    DevUser user(comm_info);

    std::set<std::string> mac_set;
    int size = msg.mac_list_size();
    for (int i = 0; i < size; i++)
    {
        mac_set.insert(msg.mac_list(i));
    }

    DEVICE_SETTING* devlist = GetDeviceSettingInstance()->GetDeviceSettingByMacList(mac_set);
    user.UpdateMetaData(devlist);
    GetDeviceControlInstance()->DestoryDeviceSettingList(devlist);      
  
    return;
}

void CAKCSView::RegularyAutopOneDevice(const std::string &mac)
{
    DEVICE_SETTING dev;
    memset(&dev, 0, sizeof(dev));
    int ret = GetDeviceSettingInstance()->GetDeviceSettingByMac(mac, &dev);
    if (ret != 0)
    {
        AK_LOG_WARN << "GetDeviceSettingByMac Failed,MAC=" << mac;
        return;
    }

    std::vector<std::string> macs;
    macs.push_back(mac);
    if (csmain::PERSONNAL_DEV == dev.device_type)
    {
        GetPerFileUpdateContorlInstance()->PersonalFileHandle(WEB_PER_UPDATE_MAC_CONFIG, dev.device_node, macs);
        GetFileUpdateContorlInstance()->OnDevUpdateCommonHandle(WEB_PER_UPDATE_MAC_CONFIG, macs);
    }
    else
    {
        GetFileUpdateContorlInstance()->CommunityFileUpdateFormateWeb(WEB_COMM_UPDATE_MAC_CONFIG, dev.manager_account_id, dev.unit_id,dev.device_node, macs);
        GetFileUpdateContorlInstance()->OnDevUpdateCommonHandle(WEB_COMM_UPDATE_MAC_CONFIG, macs);
    }
}

void CAKCSView::OnRegularyAutopNotify(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnRegularyAutopNotify The param is NULL";
        return;
    }

    AK::Adapt::RegularAutopNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    AK_LOG_INFO << "OnRegularyAutopNotify msg=" << msg.DebugString();

    int size = msg.mac_list_size();
    for (int i = 0; i < size; i++)
    {
        RegularyAutopOneDevice(msg.mac_list(i));
    }
}

void CAKCSView::OnOnceAutopNotify(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnRegularyAutopNotify The param is NULL";
        return;
    }

    AK::Adapt::OnceAutopNotify once_autop_msg;

    CHECK_PB_PARSE_MSG(once_autop_msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    AK_LOG_INFO << "OnOnceAutopNotify msg=" << once_autop_msg.DebugString();

    int size = once_autop_msg.mac_list_size();
    for (int i = 0; i < size; i++)
    {
        AK::Adapt::DevConfigUpdateNotify msg;
        msg.set_config(once_autop_msg.config());
        msg.set_mac(once_autop_msg.mac_list(i));
        AK_LOG_INFO << "OnUpdateMacConfig msg=" << msg.DebugString();

        CAkcsPdu pdu;
        pdu.SetMsgBody(&msg);
        pdu.SetHeadLen(sizeof(PduHeader_t));
        pdu.SetVersion(50);
        pdu.SetCommandId(MSG_C2S_UPDATE_TO_DEVICE);
        pdu.SetSeqNum(0);
        g_nsq_producer->OnPublish(pdu, gstCSCONFIGConf.nsq_route_topic);
    }
}

void CAKCSView::NotifySmartLockConfigChange(const std::string &lock_uuid, NotifySmartLockType notify_smartlock_type)
{
    AK::Server::SmartLockUpdateConfigurationNotifyMsg msg;
    msg.set_lock_uuid(lock_uuid);
    msg.set_lock_type((int)notify_smartlock_type);

    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(AKCS_R2S_UPDATE_SMARTLOCK_CONFIGURATION_NOTIFY_REQ);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSCONFIGConf.nsq_route_topic);
}
