#ifndef __AKCS_VIEW_H__
#define __AKCS_VIEW_H__

#include <string>
#include "AkcsWebMsgSt.h"
#include "AKCSMsg.h"
#include "DevUser.h"

//前置声明
typedef struct PRIVATE_KEY_T PRIVATE_KEY;
typedef PRIVATE_KEY RF_KEY;

enum DoorControlType
{
    CLOSE = 0,
    OPEN = 1
};

class CAKCSView
{
public:
    CAKCSView();
    ~CAKCSView();

    static CAKCSView* GetInstance();

    void NotifyCommunityChange(uint32_t mng_id);
    void NotifyDelCommunityPics(uint32_t mng_id);
    void NotifyDelPersonalPics(const std::string& pic_url);
    void NotifyPerDelDevPics(const std::string& mac);
    void UpdateMacConfigByCsmain(int changetype, const char* pMac, const std::string& ip="");
    void UpdateDevAccountConfigByCsmain(int changetype, const std::string& node, const std::string& account,
        int account_role, int manager_id, int unit_id);
    void NotifyMacChange(const std::string& mac);
    void WriteMacUserInfoByCsmain(const std::string &mac, const UserUUIDList &list, DULONG traceid);
    void NotifyDevFileChange(const std::string &mac, int type, DULONG traceid, const std::string &file, 
        const std::string &file_md5);
    void OnAccessGroupModify(void* msg_buf, unsigned int msg_len);
    void OnAccessGroupPerModify(void* msg_buf, unsigned int msg_len);
    void OnCommunityPersonalModify(void* msg_buf, unsigned int msg_len);
    void OnCommunityAccountModify(void* msg_buf, unsigned int msg_len);
    void OnDataAnalysisNotify(void* msg_buf, unsigned int msg_len); 
    void OnCommunityImportAccountData(void* msg_buf, unsigned int msg_len);
    void NotifyAppRefreshConfig(const std::string &account, int project_type);
    void NotifySmartLockConfigChange(const std::string &lock_uuid, NotifySmartLockType notify_smartlock_type);

    /**
     *  设备批量Regular Autop下发
     * @param msg_buf 
     * @param msg_len 
     */
    void OnRegularyAutopNotify(void* msg_buf, unsigned int msg_len);

    /**
     *  设备批量Once Autop下发
     * @param msg_buf 
     * @param msg_len 
     */
    void OnOnceAutopNotify(void* msg_buf, unsigned int msg_len);

    /**
     * 删除APP账号通知
     * 
     * @param msg_buf 
     * @param msg_len 
     */
private:
    void RegularyAutopOneDevice(const std::string &mac);
private:
    CAKCSView(const CAKCSView&);
    CAKCSView& operator = (const CAKCSView&);

private:

    static CAKCSView* instance;
};

CAKCSView* GetAKCSViewInstance();

#endif //__AKCS_VIEW_H__
