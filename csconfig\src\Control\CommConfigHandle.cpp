#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <assert.h>
#include <string>
#include "CommConfigHandle.h"
#include "DeviceControl.h"
#include "PersonalAccount.h"
#include "RfKeyControl.h"
#include "PrivateKeyControl.h"
#include "dbinterface/CommunityInfo.h"
#include "BasicDefine.h"
#include "util_cstring.h"
#include "CharChans.h"
#include "AKCSMsg.h"
#include "AkLogging.h"
#include "AdaptUtility.h"
#include "DeviceSetting.h"
#include "CommunityMng.h"
#include "FaceMng.h"
#include "DevKey.h"
#include "DevUser.h"
#include "DevSchedule.h"
#include "AkcsCommonDef.h"
#include "util.h"
#include "dbinterface/CommunityRoom.h"
#include "dbinterface/Account.h"
#include "CommunityDevContact.h"
#include "dbinterface/AccessGroupDB.h"
#include "dbinterface/FaceMngDB.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/AccountAccess.h"
#include "dbinterface/UserAccessGroup.h"
#include "WriteDevWorkPool.h"
#include <ratio>
#include <chrono>


#define CHECK_NODE_IS_NULL(node) \
    do { \
        if (node.length() <=0 ) {AK_LOG_WARN << "CHECK_NODE_IS_NULL is true";return;} \
       } while(0)

#define CHECK_UNIT_IS_NULL(unit_id) \
    do { \
        if (unit_id <=0 ){AK_LOG_WARN << "CHECK_UNIT_IS_NULL is true";return;}\
       }while(0)



CommConfigHandle::CommConfigHandle(uint32_t mng_id, uint32_t unit_id, const std::string &node, const std::vector<std::string> mac_list)
                    :mng_id_(mng_id),unit_id_(unit_id),node_(node) 
{
    node_dev_list_ = nullptr;
    unit_dev_list_ = nullptr;
    pub_dev_list_ = nullptr;
    app_list_init_ = 0;
    
    dbinterface::CommunityUnit::GetCommunityUnitsByMngID(mng_id_, g_unit_list_);
    g_communit_info_ = std::make_shared<CommunityInfo>(mng_id_);

    g_mng_sip_type_ = 0;
    g_rtp_confuse_ = 0;
    g_rtsp_type_ = 0;
    dbinterface::Account::GetMngTransType(mng_id_, g_mng_sip_type_, g_rtp_confuse_, g_rtsp_type_);

    g_user_access_group_devices_map_ptr_ = std::make_shared<MapUserAGDeviceList>();

    
    node_cnf_map_ptr = std::make_shared<PersonalAccountCnfInfoMap>();
    dbinterface::PersonalAccountCnf::GetPeronalAccountCnfByCommunityProjectID(mng_id_, node_cnf_map_ptr);

    sip_contorl_ptr_ = std::make_shared<SipContorl>();
    sip_contorl_ptr_->Init(mng_id);

    
    devices_contorl_.Init(mng_id, node_cnf_map_ptr, sip_contorl_ptr_);
    
    config_context_ = std::make_shared<ConfigContext>();
    
    config_context_->SetDevContorl(&devices_contorl_, node_cnf_map_ptr, sip_contorl_ptr_);
    config_context_->Init(mng_id, g_communit_info_->UUID()); 

    InitAllAccessGroup();//这要先于InitAllAppList
    InitAllUnitDev();
    InitAllAppList();
    
    config_context_->SetUserDevicesMap(g_user_access_group_devices_map_ptr_);//这一步要晚于InitAllAppList

}

CommConfigHandle::~CommConfigHandle()
{
    devices_contorl_.ReleaseDeviceSetting(node_dev_list_);
    devices_contorl_.ReleaseDeviceSetting(pub_dev_list_);
    devices_contorl_.ReleaseDeviceSetting(unit_dev_list_);
}


void CommConfigHandle::UpdateNode(const std::string& node)
{
    node_ = node;
    devices_contorl_.ReleaseDeviceSetting(node_dev_list_);
    node_dev_list_ = nullptr;
}

void CommConfigHandle::SetUnitID(uint32_t unit_id)
{
    unit_id_ = unit_id;
    devices_contorl_.ReleaseDeviceSetting(unit_dev_list_);
    unit_dev_list_ = nullptr;
}


void CommConfigHandle::InitNodeDev()
{
    CHECK_NODE_IS_NULL(node_);
    if (!node_dev_list_)
    {
        node_dev_list_ = devices_contorl_.GetNodeDeviceSettingList(node_);
    }
}

void CommConfigHandle::InitUnitDev()
{
    CHECK_UNIT_IS_NULL(unit_id_);
    if (!unit_dev_list_)
    {
        std::set<int> unit_set;
        GetPermissiveUnitListByNode(node_, unit_id_, unit_set);
        unit_dev_list_ = devices_contorl_.GetUnitDeviceInGlobal(unit_set);

        //旧流程，避免异常未获取到
        if(!unit_dev_list_)
        {
            unit_dev_list_ =  devices_contorl_.GetUnitDeviceInGlobal(unit_id_);
        }

    }

}

void CommConfigHandle::InitAllUnitDev()
{
    for (const auto& unit : g_unit_list_)
    {
        ResidentDeviceList dev_list;
        if (0 == dbinterface::ResidentDevices::GetDepartmentDevList(unit.unit_id, dev_list))
        {
            g_all_unit_dev_list_.insert(std::make_pair(unit.unit_id, dev_list));
            for (const auto& dev : dev_list)
            {
                g_all_unit_mac_unitid_map_.insert(std::make_pair(dev.mac, unit.unit_id));
            }
        }
    }

}

void CommConfigHandle::InitAllAccessGroup()
{
    std::set<int> ag_id_list;
    dbinterface::AccessGroup::GetAgIDListByCommunityID(mng_id_, ag_id_list);
    for(const auto& ag_id : ag_id_list)
    {
        std::set<std::string> mac_set;
        std::set<std::string> accounts;
        
        //获取权限组关联的设备
        config_context_->GetAgIdMacList(ag_id, mac_set);
        //获取权限组关联的用户
        config_context_->GetAgIdAccountList(ag_id, accounts);

        //构建用户和有权限的设备之间的关系
        for(const auto& account : accounts)
        {
            g_all_account_pub_mac_map_[account].insert(mac_set.begin(), mac_set.end());
        }
    }
    
}


void CommConfigHandle::InitPubDev()
{
    if (!pub_dev_list_)
    {
        pub_dev_list_ = devices_contorl_.GetPubDeviceInGlobal();
    }

}

void CommConfigHandle::InitAllAppList()
{
    if (app_list_init_)
    {
        return;
    }

    for (auto& unit : g_unit_list_)
    {   
        CommunitAccountInfoList nodes;
        config_context_->GetUnitNodes(unit.unit_id, nodes);
        g_unit_node_map_.insert(std::make_pair(unit.unit_id, nodes));  
        for (auto& node : nodes)
        {
            NodeAppList apps;
            config_context_->GetCommunityApplistByNode(node.account, apps);
            g_all_node_app_map_.insert(std::make_pair(node.account, apps));

            InitAllUserPermissions(node.account, apps);
        }        
    }    
    app_list_init_ = 1;
}


void CommConfigHandle::InitAllUserPermissions(const std::string& node, const NodeAppList& apps)
{
    std::set<std::string> valid_mac_set;
    std::set<std::string> node_default_valid_mac_set;
    for (const auto& app : apps)
    {
        valid_mac_set.clear();
        //新小区的非空房间获取有权限设备,其他相当于默认权限
        if (g_communit_info_->GetIsNew() && !app.only_apt)
        {
            const auto &iter = g_all_account_pub_mac_map_.find(app.sip_account);
            if(iter != g_all_account_pub_mac_map_.end())
            {
               valid_mac_set = iter->second; 
            }
            config_context_->GetUserAccessAccountMacList(app.sip_account, node, valid_mac_set);
            //dbinterface::UserAccessGroup::GetValidPerAccessGroupDevices(app.sip_account, valid_mac_set);
        }
        else
        {
            //旧小区流程
            if(node_default_valid_mac_set.size() > 0)
            {
                valid_mac_set = node_default_valid_mac_set;
            }
            else
            {
                dbinterface::ResidentDevices::GetAllContactListDevices(node, app.unit_id, app.mng_account_id, valid_mac_set);
                //减少重复查询，查过就不要再查了
                node_default_valid_mac_set = valid_mac_set;
            }
        }
        g_user_access_group_devices_map_ptr_->insert(std::make_pair(app.sip_account, valid_mac_set)); 

        //构造g_node_access_group_unit_map_和g_unitdev_access_group_unit_map_
        for(const auto& valid_mac : valid_mac_set)
        {
            const auto &iter = g_all_unit_mac_unitid_map_.find(valid_mac);
            //楼栋设备
            if(iter != g_all_unit_mac_unitid_map_.end())
            {
                //房间插入有权限的设备的unit_id
                g_node_access_group_unit_map_[node].insert(iter->second);
                
                //设备插入有权限的房间的unit_id
                g_unitdev_access_group_unit_map_[valid_mac].insert(app.unit_id);
            }
        }
    }
}

//获取有某个楼栋门口设备权限的楼栋下的用户列表
void CommConfigHandle::GetPermissiveUnitAccountListByMac(const std::string& unit_mac, int unit_id, CommunitAccountInfoList& accounts)
{
    //先获取有权限的楼栋
    std::set<int> unit_set;
    unit_set.insert(unit_id);
    const auto &it = g_unitdev_access_group_unit_map_.find(unit_mac);
    if(it != g_unitdev_access_group_unit_map_.end())
    {
        unit_set.insert(it->second.begin(), it->second.end());
    }
    //再通过楼栋找用户
    for(const auto& unit : unit_set)
    {
        auto iter = g_unit_node_map_.find(unit);
        if(iter == g_unit_node_map_.end())
        {
            AK_LOG_WARN << "Wirte unit contact, but unit id=" << unit << " can not found nodes" ;
            return;
        }
        accounts.insert(accounts.end(), iter->second.begin(), iter->second.end());
        
    }
}


//获取某个房间有权限的楼栋的门口设备列表
void CommConfigHandle::GetPermissiveUnitDevListByNode(const std::string& node, int unit_id, ResidentDeviceList& unit_dev_list)
{
    //先获取有权限的楼栋
    std::set<int> unit_set;
    unit_set.insert(unit_id);
    if(node.size() > 0)
    {
        const auto &iter = g_node_access_group_unit_map_.find(node);
        if(iter != g_node_access_group_unit_map_.end())
        {
            unit_set.insert(iter->second.begin(), iter->second.end());
        }
    }
    
    //再通过楼栋找到设备
    for(const auto& unit : unit_set) 
    {
        const auto &iter = g_all_unit_dev_list_.find(unit);
        if(iter != g_all_unit_dev_list_.end())
        {
            unit_dev_list.insert(unit_dev_list.end(), iter->second.begin(), iter->second.end());
        }
    }
}


//获取某个房间有权限的楼栋列表
void CommConfigHandle::GetPermissiveUnitListByNode(const std::string& node, int unit_id, std::set<int> &unit_set)
{
    //先获取有权限的楼栋
    unit_set.insert(unit_id);
    if(node.size() > 0)
    {
        const auto &iter = g_node_access_group_unit_map_.find(node);
        if(iter != g_node_access_group_unit_map_.end())
        {
            unit_set.insert(iter->second.begin(), iter->second.end());
        }
    }
}

DEVICE_SETTING* CommConfigHandle::GetUnitDeviceByNode(const std::string &node, uint32_t unit_id)
{
    std::set<int> unit_set;
    GetPermissiveUnitListByNode(node, unit_id, unit_set);
    return devices_contorl_.GetUnitDeviceInGlobal(unit_set);
}

void CommConfigHandle::UpdateNodeDevConfig()
{
    CHECK_NODE_IS_NULL(node_);
    InitNodeDev();
    if (!node_dev_list_)
    {
        AK_LOG_INFO << "Devices List is null, no need update config file!";
        return;
    }
    std::string config_root_path = GetCommunityPersonalDownloadConfigPath(mng_id_, unit_id_, node_.c_str());
    
    DevConfig config(config_root_path, g_mng_sip_type_,  g_rtp_confuse_, g_rtsp_type_);
    config.SetCommunityInfo(g_communit_info_);
    config.SetContext(config_context_);
    
    if ( config.WriteDevListFiles(node_dev_list_) < 0)
    {
        AK_LOG_WARN << "Update community config files failed." << config_root_path;
    }
}

void CommConfigHandle::UpdateNodeDevConfig(uint32_t mng_id, uint32_t unit_id, const std::string &node, DEVICE_SETTING *node_dev_list)
{
    if (!node_dev_list)
    {
        //AK_LOG_INFO << "Devices List is null, no need update config file!";
        return;
    }
    std::string config_root_path = GetCommunityPersonalDownloadConfigPath(mng_id, unit_id, node.c_str());
    DevConfig config(config_root_path, g_mng_sip_type_,  g_rtp_confuse_, g_rtsp_type_);
    config.SetCommunityInfo(g_communit_info_);
    config.SetContext(config_context_);
    
    if (config.WriteDevListFiles(node_dev_list) < 0)
    {
        AK_LOG_WARN << "Update community config files failed." << config_root_path;
    }
}


void CommConfigHandle::UpdateUnitDevConfig()
{
    CHECK_UNIT_IS_NULL(unit_id_);
    InitUnitDev();
    if (!unit_dev_list_)
    {
        //AK_LOG_INFO << "Devices List is null, no need update config file!";
        return;
    }    
    std::string config_root_path = GetCommunityUnitPublicDownloadConfigPath(mng_id_, unit_id_);
    DevConfig config(config_root_path, g_mng_sip_type_,  g_rtp_confuse_, g_rtsp_type_);
    config.SetCommunityInfo(g_communit_info_);
    config.SetContext(config_context_);
    
    if (config.WriteDevListFiles(unit_dev_list_) < 0)
    {
        AK_LOG_WARN << "Update community config files failed." << config_root_path;
    }  
}

void CommConfigHandle::UpdateUnitDevConfig(uint32_t mng_id, uint32_t unit_id, DEVICE_SETTING *unit_dev_list)
{
    if (!unit_dev_list)
    {
        //AK_LOG_INFO << "Devices List is null, no need update config file!";
        return;
    } 

    std::string config_root_path = GetCommunityUnitPublicDownloadConfigPath(mng_id, unit_id);
    DevConfig config(config_root_path, g_mng_sip_type_,  g_rtp_confuse_, g_rtsp_type_);
    config.SetCommunityInfo(g_communit_info_);    
    config.SetContext(config_context_);
    
    if (config.WriteDevListFiles(unit_dev_list) < 0)
    {
        AK_LOG_WARN << "Update community config files failed." << config_root_path;
    }   
}


void CommConfigHandle::UpdatePubDevConfig()
{
    InitPubDev();
    if (!pub_dev_list_)
    {
        //AK_LOG_INFO << "Devices List is null, no need update config file!";
        return;
    }
    std::string config_root_path = GetCommunityPublicDownloadConfigPath(mng_id_);      
    DevConfig config(config_root_path, g_mng_sip_type_,  g_rtp_confuse_, g_rtsp_type_);
    config.SetCommunityInfo(g_communit_info_);   
    config.SetContext(config_context_);
    
    if (config.WriteDevListFiles(pub_dev_list_) < 0)
    {
        AK_LOG_WARN << "Update community config files failed." << config_root_path;
    }
}

void CommConfigHandle::UpdateNodeDevRf(uint32_t mng_id, uint32_t unit_id, const std::string &node, DEVICE_SETTING* node_dev_list)
{
    if (!node_dev_list)
    {
        AK_LOG_INFO << "Node Devices List is null, no need update config file!";
        return;
    }
    std::string rf_root_path = GetCommunityPersonalDownloadRfidPath(mng_id, unit_id, node.c_str());
    RF_KEY* rf_key_list = GetRfKeyControlInstance()->GetPersonnalRootBothRfKeyList(node);
    GetRfKeyControlInstance()->GetNodeNFCKeyList(node, &rf_key_list);

    DevKey rf(rf_root_path, csmain::COMMUNITY_DEVICE_TYPE_PERSONAL);
    rf.SetCommunityInfo(g_communit_info_);
    int ret = rf.UpdateRfKeyFiles(node_dev_list, rf_key_list);
	if (ret < 0 && (rf_key_list != nullptr))
    {
        AK_LOG_WARN << "Update community rf key files failed.";
    }

    if (rf_key_list != nullptr)
    {
        GetRfKeyControlInstance()->DestoryRfKeyList(rf_key_list);
    }
    
}


void CommConfigHandle::UpdateNodeDevRf()
{
    CHECK_NODE_IS_NULL(node_);
    InitNodeDev();
    UpdateNodeDevRf(mng_id_, unit_id_, node_, node_dev_list_);
}


void CommConfigHandle::UpdateUnitDevRf(uint32_t mng_id, uint32_t unit_id, DEVICE_SETTING* unit_dev_list)
{
    if (!unit_dev_list)
    {
        AK_LOG_INFO << "Unit Devices List is null, no need update config file!";
        return;
    }

    CommunityInfo community_info(mng_id_);
    std::string rf_root_path = GetCommunityUnitPublicDownloadRfidPath(mng_id, unit_id);
    RF_KEY* rf_key_list = nullptr;
    
    GetRfKeyControlInstance()->GetCommunityUnitNfcKeyList(unit_id, &rf_key_list);
    DevKey rf(rf_root_path, csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT);
    rf.SetCommunityInfo(g_communit_info_);
    int ret = rf.UpdateRfKeyFiles(unit_dev_list, rf_key_list);
	if (ret < 0 && (rf_key_list != nullptr))
    {
        AK_LOG_WARN << "Update community rf key files failed.";
    }

    if (rf_key_list != nullptr)
    {
        GetRfKeyControlInstance()->DestoryRfKeyList(rf_key_list);
    }

}


void CommConfigHandle::UpdateUnitDevRf()
{
    CHECK_UNIT_IS_NULL(unit_id_);
    InitUnitDev();
    UpdateUnitDevRf(mng_id_, unit_id_, unit_dev_list_);
}

void CommConfigHandle::UpdatePubDevRf()
{
    InitPubDev();
    if (!pub_dev_list_)
    {
        AK_LOG_INFO << "Public Devices List is null, no need update config file!";
        return;
    }
    
    std::string rf_root_path = GetCommunityPublicDownloadRfidPath(mng_id_);
    RF_KEY* rf_key_list = nullptr;
    
    GetRfKeyControlInstance()->GetCommunityPublicNfcKeyList(mng_id_, &rf_key_list);
    DevKey rf(rf_root_path, csmain::COMMUNITY_DEVICE_TYPE_PUBLIC);
    rf.SetCommunityInfo(g_communit_info_);
    int ret = rf.UpdateRfKeyFiles(pub_dev_list_, rf_key_list);
    if (ret < 0 && (rf_key_list != nullptr))
    {
        AK_LOG_WARN << "Update community rf key files failed.";
    }

    if (rf_key_list != nullptr)
    {
        GetRfKeyControlInstance()->DestoryRfKeyList(rf_key_list);
    }      
}


void CommConfigHandle::UpdatePrivateKey2AptPin(PRIVATE_KEY* private_key_list)
{
    //遍历PRIVATEKEY，为该设备生成一个新的PRIVATEKEY列表
    PRIVATE_KEY* cur_private_key = private_key_list;
    while (cur_private_key != NULL)
    {
        if (strlen(cur_private_key->code) == 0)//V4.3删除社区用户时候会把code=‘’,不会删除数据
        {
            cur_private_key = cur_private_key->next;
            continue;
        }
        CommunityRoomInfo room; 
        dbinterface::CommunityRoom::GetCommunityRoomByID(cur_private_key->room_id, room);
        std::string tmpcode = cur_private_key->code;
        ::snprintf(cur_private_key->code, sizeof(cur_private_key->code), "%s+%s", room.room_number, tmpcode.c_str());
        cur_private_key = cur_private_key->next;
    }
}


void CommConfigHandle::UpdateNodeDevPrivatekey()
{
    CHECK_NODE_IS_NULL(node_);
    InitNodeDev();
    UpdateNodeDevPrivatekey(mng_id_, unit_id_, node_, node_dev_list_);
}


void CommConfigHandle::UpdateNodeDevPrivatekey(uint32_t mng_id, uint32_t unit_id, const std::string &node, DEVICE_SETTING* node_dev_list)
{
    if (!node_dev_list)
    {
        //AK_LOG_INFO << "Devices List is null, no need update config file!";
        return;
    }
    std::string pri_root_path = GetCommunityPersonalDownloadPrivatekeyPath(mng_id, unit_id, node.c_str());
    PRIVATE_KEY* private_key_list = GetPrivateKeyControlInstance()->GetPersonnalRootBothPrivateKeyList(node);

     if (g_communit_info_->AptPinType() == CommunityInfo::AptPinTypeEnum::APT_PIN)
     {
         UpdatePrivateKey2AptPin(private_key_list);
     }
     
    DevKey privatekey(pri_root_path, csmain::COMMUNITY_DEVICE_TYPE_PERSONAL);
    privatekey.SetCommunityInfo(g_communit_info_); 
    int ret = privatekey.UpdatePrivateKeyFiles(node_dev_list, private_key_list);
	if (ret < 0 && (private_key_list != NULL))
    {
        AK_LOG_WARN << "Update community private key files failed.";
    }

    if (private_key_list != nullptr)
    {
        GetPrivateKeyControlInstance()->DestoryPrivateKeyList(private_key_list);
    }    
}



void CommConfigHandle::UpdateUnitDevPrivatekey()
{
    CHECK_UNIT_IS_NULL(unit_id_);
    InitUnitDev();
    if (!unit_dev_list_)
    {
        //AK_LOG_INFO << "Devices List is null, no need update config file!";
        return;
    }    
    std::string pri_root_path = GetCommunityUnitPublicDownloadPrivatekeyPath(mng_id_, unit_id_);
    //获取NFC、BLE的RFKEY列表
    PRIVATE_KEY* private_key_list = NULL;

    DevKey privatekey(pri_root_path, csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT);
    privatekey.SetCommunityInfo(g_communit_info_); 
    int ret = privatekey.UpdatePrivateKeyFiles(unit_dev_list_, private_key_list);    
	if (ret < 0 && (private_key_list != NULL))
    {
        AK_LOG_WARN << "Update community private key files failed.";
    }

}

void CommConfigHandle::UpdatePubDevPrivatekey()
{
    InitPubDev();
    if (!pub_dev_list_)
    {
        //AK_LOG_INFO << "Devices List is null, no need update config file!";
        return;
    }     
    std::string pri_root_path = GetCommunityPublicDownloadPrivatekeyPath(mng_id_);
    //获取NFC、BLE的RFKEY列表
    PRIVATE_KEY* private_key_list = NULL;
    DevKey privatekey(pri_root_path, csmain::COMMUNITY_DEVICE_TYPE_PUBLIC);
    privatekey.SetCommunityInfo(g_communit_info_); 
    int ret = privatekey.UpdatePrivateKeyFiles(pub_dev_list_, private_key_list);     
     if (ret < 0 && (private_key_list != NULL))
    {
        AK_LOG_WARN << "Update community private key files failed.";
    }   
}

void CommConfigHandle::UpdateNodeDevContactList()
{
    CHECK_NODE_IS_NULL(node_);
    InitPubDev();
    InitNodeDev();
    InitUnitDev();

    UpdateNodeDevContactList(mng_id_, unit_id_, node_, node_dev_list_, unit_dev_list_, pub_dev_list_);
}


void CommConfigHandle::UpdateNodeDevContactList(uint32_t mng_id, uint32_t unit_id, const std::string &node,
   DEVICE_SETTING* node_dev_list, DEVICE_SETTING* unit_dev_list, DEVICE_SETTING* pub_dev_list)
{
    if (!node_dev_list)
    {
        return;
    }
    
    std::string contact_root_path_ = GetCommunitySaveContactListDir(mng_id, unit_id, node.c_str(), csmain::COMMUNITY_DEVICE_TYPE_PERSONAL);
    std::vector<DEVICE_CONTACTLIST> app_list;

    config_context_->GetCommunityApplistByNode(node, app_list);

    const auto &it = node_cnf_map_ptr->find(node);
    if (it == node_cnf_map_ptr->end())
    {
        return;
    }
    const PersonalAccountCnfInfo &node_config = it->second;

    if (app_list.size() == 0)
    {
        return;
    }
    
    DEVICE_SETTING* cur_dev = node_dev_list;
    while (cur_dev != NULL)
    {
        //设备V4.4 版本NODE_CALL_TYPE_INDOOR_PHONE 修改为新的呼叫方式
        if (node_config.call_type == NODE_CALL_TYPE_INDOOR_BACK_PHONE
                || node_config.call_type == NODE_CALL_TYPE_INDOOR_BACK_APP_BACK_PHONE
                || node_config.call_type == NODE_CALL_TYPE_INDOOR_BACK_APP
                || node_config.call_type == NODE_CALL_TYPE_INDOOR_PHONE)
        {
            app_list[0].call_loop = CALL_LOOP_TYPE_GROUP_CALL;
        }
        else if (node_config.call_type == NODE_CALL_TYPE_APP_INDOOR_BACK_PHONE)
        {
            app_list[0].call_loop = CALL_LOOP_TYPE_APP_INDOOR_BACK_PHONE;
        }
        else
        {
            app_list[0].call_loop = CALL_LOOP_TYPE_NORMAL;
        }
        if (node_config.call_type == NODE_CALL_TYPE_APP_INDOOR_BACK_PHONE
                || node_config.call_type == NODE_CALL_TYPE_INDOOR_PHONE
                || node_config.call_type == NODE_CALL_TYPE_INDOOR_BACK_PHONE
                || node_config.call_type == NODE_CALL_TYPE_INDOOR_BACK_APP_BACK_PHONE)
        {
            app_list[0].phone_status = 1;//落地V4.3改为CallType控制
        }
        else
        {
            app_list[0].phone_status = 0;
        }

        app_list[0].call_type = node_config.call_type;
        //如果是第二种，那么旧版本走CallLoop=0 新版本才走Group Call
        if (cur_dev->dclient_version < D_CLIENT_VERSION_4400 && node_config.call_type == NODE_CALL_TYPE_INDOOR_PHONE)
        {
            app_list[0].call_type = NODE_CALL_TYPE_INDOOR_PHONE_OLD;
            app_list[0].call_loop = CALL_LOOP_TYPE_NORMAL;
        }

        CommunityDevContact contact(contact_root_path_);
        contact.SetCommunityInfo(g_communit_info_);
        contact.SetContext(config_context_);
        contact.UpdateCommContactFile(cur_dev, node_dev_list, app_list, pub_dev_list, unit_dev_list);
        cur_dev = cur_dev->next;
    }
    
}

void CommConfigHandle::UpdateUnitDevContactList()
{
    CHECK_UNIT_IS_NULL(unit_id_);
    InitUnitDev();
    if (!unit_dev_list_)
    {
        return;
    }     

    //获取文件路径
    std::string contact_root_path = GetCommunityUnitPublicDownloadContactPath(mng_id_, unit_id_);
    DEVICE_SETTING* cur_dev = unit_dev_list_;
    while (cur_dev != nullptr)
    {    
        CommunitAccountInfoList accounts;
        GetPermissiveUnitAccountListByMac(cur_dev->mac, cur_dev->unit_id, accounts);
        CommunityDevContact contact(contact_root_path);
        contact.SetCommunityInfo(g_communit_info_);
        contact.SetContext(config_context_);
        contact.UpdateCommunityPublicContactFile(cur_dev, accounts, g_all_node_app_map_, csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT);
             
        cur_dev = cur_dev->next;
    }
    return;
}



void CommConfigHandle::UpdatePubDevContactList()
{
    InitPubDev();
    if (!pub_dev_list_)
    {
        //AK_LOG_INFO << "Devices List is null, no need update config file!";
        return;
    }       
    //获取文件路径
    std::string contact_root_path = GetCommunityPublicDownloadContactPath(mng_id_);

    CommunitAccountInfoList all_node_list_for_pub;
    for (const auto& unit_node_pair : g_unit_node_map_)
    {
        all_node_list_for_pub.insert(all_node_list_for_pub.end(), unit_node_pair.second.begin(), unit_node_pair.second.end());
    }

    DEVICE_SETTING* cur_dev = pub_dev_list_;
    WorkTask task;
    bool db_status = ThreadLocalSingleton::GetInstance().GetDbStatus();
    while (cur_dev != nullptr)
    {
        task.AddWork([this, cur_dev, all_node_list_for_pub, contact_root_path, db_status]{
            //将上一级线程的db状态初始化到各个工作线程，用于判断后续是否能执行写文件
            ThreadLocalSingleton::GetInstance().SetDbStatus(db_status);
            CommunityDevContact contact(contact_root_path);
            contact.SetContext(config_context_);
            contact.SetCommunityInfo(g_communit_info_);
            contact.UpdateCommunityPublicContactFile(cur_dev, all_node_list_for_pub, g_all_node_app_map_, csmain::COMMUNITY_DEVICE_TYPE_PUBLIC); 
            return std::string(cur_dev->mac);
        });        
        cur_dev = cur_dev->next;
    }
    task.WaitWork();
}

void CommConfigHandle::UpdateUnitAllNodeDevContactList()
{
    InitPubDev();   
    CommunitAccountInfoList node_list;
    const auto &iter = g_unit_node_map_.find(unit_id_);
    if(iter == g_unit_node_map_.end())
    {
        AK_LOG_WARN << " unit id=" << unit_id_ << " can not found nodes" ;
        return;
    }
    node_list = iter->second;

    WorkTask task;
    bool db_status = ThreadLocalSingleton::GetInstance().GetDbStatus();
    for (const auto& node : node_list)
    {
        task.AddWork([this, node, db_status]{
            //将上一级线程的db状态初始化到各个工作线程，用于判断后续是否能执行写文件
            ThreadLocalSingleton::GetInstance().SetDbStatus(db_status);
            DEVICE_SETTING* dev_list =  devices_contorl_.GetNodeDeviceSettingList(node.account);  
            DEVICE_SETTING* unit_dev_setting_list = GetUnitDeviceByNode(node.account, node.unit_id);

            UpdateNodeDevContactList(mng_id_, unit_id_, node.account, dev_list, unit_dev_setting_list, pub_dev_list_);
            devices_contorl_.ReleaseDeviceSetting(dev_list);
            devices_contorl_.ReleaseDeviceSetting(unit_dev_setting_list);
            return std::string(node.account);
        });
        
    }
    task.WaitWork();
    return;    
}

//更新楼栋设备有权限的楼栋的房间联系人
void CommConfigHandle::UpdateNodeDevContactListByUnitMac(const std::string& mac)
{
    if(mac.size() == 0)
    {   
        AK_LOG_WARN << "UpdateNodeDevContactListByUnitMac mac is empty!";
        return;
    }
    
    InitPubDev();         
    CommunitAccountInfoList node_list;
    GetPermissiveUnitAccountListByMac(mac, unit_id_, node_list);

    WorkTask task;
    bool db_status = ThreadLocalSingleton::GetInstance().GetDbStatus();
    for (const auto& node : node_list)
    {
        task.AddWork([this, node, db_status]{  
                //将上一级线程的db状态初始化到各个工作线程，用于判断后续是否能执行写文件
                ThreadLocalSingleton::GetInstance().SetDbStatus(db_status);
                DEVICE_SETTING* dev_list = devices_contorl_.GetNodeDeviceSettingList(node.account);  
                DEVICE_SETTING* unit_dev_setting_list = GetUnitDeviceByNode(node.account, node.unit_id);

                UpdateNodeDevContactList(mng_id_, unit_id_, node.account, dev_list, unit_dev_setting_list, pub_dev_list_);  
                devices_contorl_.ReleaseDeviceSetting(unit_dev_setting_list);
                devices_contorl_.ReleaseDeviceSetting(dev_list);
                return std::string(node.account);
        });
    }
    task.WaitWork();
    return;    
}

void CommConfigHandle::UpdateSomeNodeDevContactList(const std::set<std::string>& nodes)
{
    if(nodes.size() == 0)
    {
        return;
    }
    
    InitPubDev();       
    WorkTask task;
    bool db_status = ThreadLocalSingleton::GetInstance().GetDbStatus();
    for (const auto& node : nodes)
    {
        task.AddWork([this, node, db_status]{
            //将上一级线程的db状态初始化到各个工作线程，用于判断后续是否能执行写文件
            ThreadLocalSingleton::GetInstance().SetDbStatus(db_status);
            DEVICE_SETTING* dev_list = devices_contorl_.GetNodeDeviceSettingList(node);  
            if(dev_list != nullptr)
            {
                int node_unit_id = dev_list->unit_id;
                DEVICE_SETTING* unit_dev_setting_list = GetUnitDeviceByNode(node, node_unit_id);
                UpdateNodeDevContactList(mng_id_, unit_id_, node, dev_list, unit_dev_setting_list, pub_dev_list_);
                devices_contorl_.ReleaseDeviceSetting(unit_dev_setting_list);
                devices_contorl_.ReleaseDeviceSetting(dev_list); 
            }

            return std::string(node);
        });
    }
    task.WaitWork();
    return;    
}

void CommConfigHandle::UpdateCommunityAllNodeDevContactList()
{
    InitPubDev();
    for (auto& unit : g_unit_list_)
    {   
        const auto &iter = g_unit_node_map_.find(unit.unit_id);
        if(iter == g_unit_node_map_.end())
        {
            AK_LOG_WARN << " unit id=" << unit.unit_id << " can not found nodes" ;
            continue;
        }
        const CommunitAccountInfoList &accounts = iter->second;
        
        WorkTask task;
        bool db_status = ThreadLocalSingleton::GetInstance().GetDbStatus();
        for (auto& node : accounts)
        {
            task.AddWork([this, unit, node, db_status]{
                //将上一级线程的db状态初始化到各个工作线程，用于判断后续是否能执行写文件
                ThreadLocalSingleton::GetInstance().SetDbStatus(db_status);
                DEVICE_SETTING* unit_dev_setting_list = GetUnitDeviceByNode(node.account, node.unit_id);
                
                DEVICE_SETTING* dev_list = devices_contorl_.GetNodeDeviceSettingList(node.account);  
                UpdateNodeDevContactList(mng_id_, unit.unit_id, node.account, dev_list, unit_dev_setting_list, pub_dev_list_);
                devices_contorl_.ReleaseDeviceSetting(unit_dev_setting_list);
                devices_contorl_.ReleaseDeviceSetting(dev_list);
                return std::string(node.account);
            });            
        }
        task.WaitWork();
    }      
}


void CommConfigHandle::UpdateCommunityAllNodeDevPrivatekey()
{
    for (auto& unit : g_unit_list_)
    {   
        const auto &iter = g_unit_node_map_.find(unit.unit_id);
        if(iter == g_unit_node_map_.end())
        {
            AK_LOG_WARN << " unit id=" << unit.unit_id << " can not found nodes" ;
            continue;
        }
        const CommunitAccountInfoList &accounts = iter->second;

        for (auto& node : accounts)
        {
            DEVICE_SETTING* node_dev_list = devices_contorl_.GetNodeDeviceSettingList(node.account);  
            UpdateNodeDevPrivatekey(mng_id_, unit.unit_id, node.account, node_dev_list);
            devices_contorl_.ReleaseDeviceSetting(node_dev_list);
        }        
    }      
}


void CommConfigHandle::UpdateCommunityAllUnitDevContactList()
{
    InitPubDev();
    for (auto& unit : g_unit_list_)
    {   
        DEVICE_SETTING* unit_dev_list =  devices_contorl_.GetUnitDeviceInGlobal(unit.unit_id);
        std::string contact_root_path = GetCommunityUnitPublicDownloadContactPath(mng_id_, unit.unit_id);

        DEVICE_SETTING* cur_dev = unit_dev_list;
        WorkTask task;
        bool db_status = ThreadLocalSingleton::GetInstance().GetDbStatus();
        while (cur_dev != nullptr)
        {   
            task.AddWork([this, cur_dev, contact_root_path, db_status]{
                //将上一级线程的db状态初始化到各个工作线程，用于判断后续是否能执行写文件
                ThreadLocalSingleton::GetInstance().SetDbStatus(db_status);
                CommunitAccountInfoList accounts;
                GetPermissiveUnitAccountListByMac(cur_dev->mac, cur_dev->unit_id, accounts);
                CommunityDevContact contact(contact_root_path);
                contact.SetContext(config_context_);
                contact.SetCommunityInfo(g_communit_info_);
                contact.UpdateCommunityPublicContactFile(cur_dev, accounts, g_all_node_app_map_, csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT);
                return std::string(cur_dev->mac);
            });            
            cur_dev = cur_dev->next;
        }
        task.WaitWork();
        devices_contorl_.ReleaseDeviceSetting(unit_dev_list);
    }      
}

void CommConfigHandle::UpdateCommunityAllUnitDevConfig()
{
    InitPubDev();

    for (auto& unit : g_unit_list_)
    {   
        DEVICE_SETTING* unit_dev_list =  devices_contorl_.GetUnitDeviceInGlobal(unit.unit_id);
        std::string contact_root_path = GetCommunityUnitPublicDownloadContactPath(mng_id_, unit.unit_id);

        UpdateUnitDevConfig(mng_id_, unit.unit_id, unit_dev_list);
        devices_contorl_.ReleaseDeviceSetting(unit_dev_list);
    }      
}

void CommConfigHandle::UpdateUnitAllNodeDevConfig()
{
    InitUnitDev();
    InitPubDev();
    CommunitAccountInfoList accounts;
    const auto &iter = g_unit_node_map_.find(unit_id_);
    if(iter == g_unit_node_map_.end())
    {
        AK_LOG_WARN << " unit id=" << unit_id_ << " can not found nodes" ;
        return;
    }
    accounts = iter->second;

    for (auto& node : accounts)
    {
        DEVICE_SETTING* dev_list = devices_contorl_.GetNodeDeviceSettingList(node.account);  
        UpdateNodeDevConfig(mng_id_, unit_id_, node.account, dev_list);
        devices_contorl_.ReleaseDeviceSetting(dev_list);
    }
    return;    
}

void CommConfigHandle::UpdateCommunityAllNodeDevConfig()
{
    for (auto& unit : g_unit_list_)
    {   
        CommunitAccountInfoList accounts;
        const auto &iter = g_unit_node_map_.find(unit.unit_id);
        if(iter == g_unit_node_map_.end())
        {
            AK_LOG_WARN << " unit id=" << unit.unit_id << " can not found nodes" ;
            continue;
        }
        accounts = iter->second;

        for (auto& node : accounts)
        {
            DEVICE_SETTING* dev_list = devices_contorl_.GetNodeDeviceSettingList(node.account);  
            UpdateNodeDevConfig(mng_id_, unit.unit_id, node.account, dev_list);
            devices_contorl_.ReleaseDeviceSetting(dev_list);
        }

    }
    return;    
}


void CommConfigHandle::UpdateCommunityAllUnitDevPrivatekey()
{
    for (auto& unit : g_unit_list_)
    {   
        DEVICE_SETTING* unit_dev_list =  devices_contorl_.GetUnitDeviceInGlobal(unit.unit_id);
        std::string pri_root_path = GetCommunityUnitPublicDownloadPrivatekeyPath(mng_id_, unit.unit_id);
        PRIVATE_KEY* private_key_list = NULL;

        DevKey privatekey(pri_root_path, csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT);
        privatekey.SetCommunityInfo(g_communit_info_); 
        int ret = privatekey.UpdatePrivateKeyFiles(unit_dev_list, private_key_list);   
		if (ret < 0 && (private_key_list != NULL))
        {
            AK_LOG_WARN << "Update community private key files failed.";
        }
        devices_contorl_.ReleaseDeviceSetting(unit_dev_list);
    }
   
}

void CommConfigHandle::UpdateCommunityAllUnitDevRf()
{
    for (auto& unit : g_unit_list_)
    {   
        DEVICE_SETTING* unit_dev_list =  devices_contorl_.GetUnitDeviceInGlobal(unit.unit_id);
        UpdateUnitDevRf(mng_id_, unit.unit_id, unit_dev_list);
        devices_contorl_.ReleaseDeviceSetting(unit_dev_list);
    }    
}

void CommConfigHandle::UpdateCommunityAllNodeDevRf()
{
    for (auto& unit : g_unit_list_)
    {   
        CommunitAccountInfoList accounts;
        const auto &iter = g_unit_node_map_.find(unit.unit_id);
        if(iter == g_unit_node_map_.end())
        {
            AK_LOG_WARN << " unit id=" << unit.unit_id << " can not found nodes" ;
            continue;
        }
        accounts = iter->second;

        for (auto& node : accounts)
        {
            DEVICE_SETTING* node_dev_list = devices_contorl_.GetNodeDeviceSettingList(node.account);  
            UpdateNodeDevRf(mng_id_, unit.unit_id, node.account, node_dev_list);
            devices_contorl_.ReleaseDeviceSetting(node_dev_list);
        }        
    } 

}

void CommConfigHandle::UpdateCommunityOneUnitFace()
{
    if (unit_id_ <= 0)
    {
        return;
    }
    InitUnitDev();
    UpdateUnitDevFace(mng_id_, unit_id_, unit_dev_list_);
}

void CommConfigHandle::UpdateCommunityAllUnitFace()
{
    for (auto& unit : g_unit_list_)
    {   
        DEVICE_SETTING* unit_dev_list =  devices_contorl_.GetUnitDeviceInGlobal(unit.unit_id);
        UpdateUnitDevFace(mng_id_, unit.unit_id, unit_dev_list);
        devices_contorl_.ReleaseDeviceSetting(unit_dev_list);
    }      
}

void CommConfigHandle::UpdateUnitDevFace(uint32_t mng_id, uint32_t unit_id, DEVICE_SETTING *unit_dev_list)
{
    if (!unit_dev_list)
    {
        //AK_LOG_INFO << "Devices List is null, no need update face file!";
        return;
    } 

    std::vector<FaceMngInfo> face_mng_infos;
    int ret = dbinterface::FaceMng::GetFaceMng(face_mng_infos, mng_id, unit_id, 0);
    if (ret != 0)
    {
        AK_LOG_WARN << "DaoGetFaceMng Failed.";
        return;
    }

    std::string face_root_path = GetCommunityUnitPublicDownloadFacePath(mng_id, unit_id);
    DEVICE_SETTING *cur_dev = unit_dev_list;
    while (cur_dev)
    {
		if (GetDeviceSettingInstance()->DeviceSupportFaceMng(cur_dev->type))
        {
            CFaceXmlWriter::GetInstance().WriteXml(cur_dev, face_mng_infos, face_root_path, project::RESIDENCE);
        }
        cur_dev = cur_dev->next;
    }

    return;
}

void CommConfigHandle::UpdateCommunityOneNodeFace()
{
    if (node_.size() == 0 || unit_id_ <= 0)
    {
        return;
    }
    InitNodeDev();
    if (g_all_node_app_map_.count(node_) == 0)
    {
        AK_LOG_INFO << "g_all_node_app_map_ has no account:" << node_ << " 's info";
        return;
    }

    NodeAppList node_app_list = g_all_node_app_map_[node_];
    UpdateNodeDevFace(mng_id_, unit_id_, node_.c_str(), node_app_list, node_dev_list_);
}

void CommConfigHandle::UpdateCommunityAllNodeFace()
{
    InitPubDev();
    for (auto& unit : g_unit_list_)
    {   
        CommunitAccountInfoList accounts;
        const auto &iter = g_unit_node_map_.find(unit.unit_id);
        if(iter == g_unit_node_map_.end())
        {
            AK_LOG_WARN << " unit id=" << unit_id_ << " can not found nodes" ;
            continue;
        }
        accounts = iter->second;

        for (auto& node : accounts)
        {
            if (g_all_node_app_map_.count(node.account) == 0)
            {
                continue;
            }
            NodeAppList node_app_list = g_all_node_app_map_[node.account];
            DEVICE_SETTING* dev_list = devices_contorl_.GetNodeDeviceSettingList(node.account);  
            UpdateNodeDevFace(mng_id_, unit.unit_id, node.account, node_app_list, dev_list);
            devices_contorl_.ReleaseDeviceSetting(dev_list);
        }        
    }      
}

void CommConfigHandle::UpdateCommunityPubFace()
{
    InitPubDev();
    if (!pub_dev_list_)
    {
        //AK_LOG_INFO << "Devices List is null, no need update face file!";
        return;
    }       

    std::vector<FaceMngInfo> face_mng_infos;
    int ret = dbinterface::FaceMng::GetFaceMng(face_mng_infos, mng_id_, 0, 0);
    if (ret != 0)
    {
        AK_LOG_WARN << "DaoGetFaceMng Failed.";
        return;
    }

    //获取文件路径
    std::string face_root_path = GetCommunityPublicDownloadFacePath(mng_id_);

    DEVICE_SETTING* cur_dev = pub_dev_list_;
    while (cur_dev != nullptr)
    {
		if (GetDeviceSettingInstance()->DeviceSupportFaceMng(cur_dev->type))
        {
			std::vector<uint32_t> unit_list;
            int manage_all_flag = config_context_->DevMngUnitListOrMngAll(cur_dev, unit_list);
			if (0 == manage_all_flag)
			{
				std::vector<FaceMngInfo> dev_face_mng_infos(face_mng_infos);
				CFaceMng::GetInstance().FilterByUnit(unit_list, dev_face_mng_infos);
				CFaceXmlWriter::GetInstance().WriteXml(cur_dev, dev_face_mng_infos, face_root_path, project::RESIDENCE);
			}
			else
			{
				CFaceXmlWriter::GetInstance().WriteXml(cur_dev, face_mng_infos, face_root_path, project::RESIDENCE);
			}
        }
        cur_dev = cur_dev->next;
    }
}

void CommConfigHandle::UpdateNodeDevFace(uint32_t mng_id, uint32_t unit_id, const char *account, NodeAppList node_app_list, DEVICE_SETTING *node_dev_list)
{
    if (node_dev_list == nullptr)
    {
        return;
    }

    std::vector<FaceMngInfo> face_mng_infos;
    int ret = dbinterface::FaceMng::GetFaceMngByPersonalAccountIds(face_mng_infos, node_app_list);
    if (ret != 0)
    {
        AK_LOG_WARN << "DaoGetFaceMng Failed.";
        return;
    }

    std::string face_root_path = GetCommunityNodeDownloadFacePath(mng_id, unit_id, account);

    DEVICE_SETTING *cur_dev = node_dev_list;
    while (cur_dev != nullptr)
    {
		if (GetDeviceSettingInstance()->DeviceSupportFaceMng(cur_dev->type))
        {
            CFaceXmlWriter::GetInstance().WriteXml(cur_dev, face_mng_infos, face_root_path, project::RESIDENCE);
        }
        cur_dev = cur_dev->next;
    }

}

void CommConfigHandle::UpdateMacDevConfig(const std::string& mac)
{  
    std::string config_root_path;
    if(node_.size() > 0)
    {
        //TODO:个人的设备里面有pushbutton、motion等配置,是统一在GetNodeDeviceSettingList里面获取
        UpdateNodeDevConfig();
        return;
    }
    
    DEVICE_SETTING* dev_setting = devices_contorl_.GetMacDeviceInGlobal(mac);
    if (!dev_setting)
    {
        AK_LOG_WARN << "Devices is not exist, mac=" <<  mac;
        return;
    }      
    if(unit_id_ > 0)
    {
        config_root_path = GetCommunityUnitPublicDownloadConfigPath(mng_id_, unit_id_);
    }
    else
    {
        config_root_path = GetCommunityPublicDownloadConfigPath(mng_id_);
    }
  
    DevConfig config(config_root_path, g_mng_sip_type_,  g_rtp_confuse_, g_rtsp_type_);
    config.SetCommunityInfo(g_communit_info_);   
    config.SetContext(config_context_);
    if (config.WriteDevListFiles(dev_setting) < 0)
    {
        AK_LOG_WARN << "Update community config files failed." << config_root_path;
    }
    devices_contorl_.ReleaseDeviceSetting(dev_setting);
}


void CommConfigHandle::UpdateMacUser(const std::string &mac)
{
    DEVICE_SETTING *dev = devices_contorl_.GetMacDeviceInGlobal(mac);
    DevUser user(g_communit_info_);
    user.UpdateMetaData(dev);
    devices_contorl_.ReleaseDeviceSetting(dev);
}

void CommConfigHandle::UpdateCommunityAllDevUser()
{
    DEVICE_SETTING *dev_list =  devices_contorl_.GetAllDeviceInGlobal();
    DevUser user(g_communit_info_);
    user.UpdateMetaData(dev_list);
    devices_contorl_.ReleaseDeviceSetting(dev_list);
}

//以设备为粒度
void CommConfigHandle::UpdateDevSchedule(const std::string &mac)
{
    DEVICE_SETTING* dev = devices_contorl_.GetMacDeviceInGlobal(mac);
    
    DevSchedule schedule(g_communit_info_);
    schedule.UpdateScheduleData(dev);
    devices_contorl_.ReleaseDeviceSetting(dev);
}

void CommConfigHandle::UpdateNodeUser()
{
    if (!g_communit_info_->GetIsNew())
    {
        AK_LOG_INFO << "communitid [" << mng_id_ << "] not new commynity, not required update user file";
        return;
    }
    
    if (node_.size() == 0)
    {
        AK_LOG_INFO << "UpdateNodeUser node is null";
        return;
    }

    std::set<std::string> pub_mac_set;
    std::set<std::string> user_mac_set;
    std::vector<std::string> accounts;
    //根据node获取家庭账号列表
    if (0 != dbinterface::ResidentPersonalAccount::GetNodeUidListByNode(node_, accounts))
    {
        AK_LOG_WARN << "get node uid list failed. node=" << node_;
        return;
    }
    
    DevUser user(g_communit_info_);

    //更新用户关联的权限组的设备user
    user.UpdatePubDevMetaByAccount(accounts, pub_mac_set);
    //更新用户关联的家庭设备user
    user.UpdateUserDevMetaByAccount(accounts, user_mac_set);
}

void CommConfigHandle::UpdateNodeUserWhenDelete()
{
    if (!g_communit_info_->GetIsNew())
    {
        AK_LOG_INFO << "communitid [" << mng_id_ << "] not new commynity, not required update user file";
        return;
    }

    if (node_.size() == 0)
    {
        AK_LOG_INFO << "UpdateNodeUserWhenDelete node is null";
        return;
    }

    std::set<std::string> user_mac_set;
    std::vector<std::string> accounts;
    accounts.push_back(node_);
    DevUser user(g_communit_info_);

    //更新家庭设备user
    user.UpdateUserDevMetaByNodes(accounts, user_mac_set);
}

//添加用户就会给住户设备添加一个权限组
void CommConfigHandle::UpdateNodeDevSchedule()
{
    CHECK_NODE_IS_NULL(node_);
    InitNodeDev();
    if (!node_dev_list_)
    {
        AK_LOG_INFO << "Devices List is null, no need update schedule file!";
        return;
    }

    DevSchedule schedule(g_communit_info_);
    schedule.UpdateScheduleData(node_dev_list_);
}

//更新楼栋/部门下的设备权限组
void CommConfigHandle::UpdateUnitDevSchedule()
{
    CHECK_UNIT_IS_NULL(unit_id_);
    InitUnitDev();
    if (!unit_dev_list_)
    {
        AK_LOG_INFO << "Node Devices List is null, no need update schedule file!";
        return;
    }

    DevSchedule schedule(g_communit_info_);
    schedule.UpdateScheduleData(unit_dev_list_);
}

//更新最外围公共设备权限组
void CommConfigHandle::UpdatePubDevSchedule()
{
    InitPubDev();
    if (!pub_dev_list_)
    {
        AK_LOG_INFO << "pub_dev_list_ is null, no need update schedule file!";
        return;
    }

    DevSchedule schedule(g_communit_info_);
    schedule.UpdateScheduleData(pub_dev_list_);
}

void CommConfigHandle::UpdateCommunityAllDevSchedule()
{
    DEVICE_SETTING *dev_list = devices_contorl_.GetAllDeviceInGlobal();
    DevSchedule schedule(g_communit_info_);
    schedule.UpdateScheduleData(dev_list); 
    devices_contorl_.ReleaseDeviceSetting(dev_list);
}

void CommConfigHandle::UpdateCommunityUpdatePubMacContact(const std::string& mac)
{
    DEVICE_SETTING* dev_setting = GetDeviceControlInstance()->GetDeviceSettingByMac(mac);
    if (!dev_setting)
    {
        AK_LOG_WARN << "Devices is not exist, mac=" <<  mac;
        return;
    }      
    if(dev_setting->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
    {
        std::string contact_root_path = GetCommunityPublicDownloadContactPath(dev_setting->manager_account_id);
        CommunitAccountInfoList all_node_list_for_pub;
        for (const auto& unit_node_pair : g_unit_node_map_)
        {
            all_node_list_for_pub.insert(all_node_list_for_pub.end(), unit_node_pair.second.begin(), unit_node_pair.second.end());
        }
        
        CommunityDevContact contact(contact_root_path);
        contact.SetContext(config_context_);
        contact.SetCommunityInfo(g_communit_info_);
        contact.UpdateCommunityPublicContactFile(dev_setting, all_node_list_for_pub, g_all_node_app_map_, csmain::COMMUNITY_DEVICE_TYPE_PUBLIC);
    }
    else if(dev_setting->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
        std::string contact_root_path = GetCommunityUnitPublicDownloadContactPath(dev_setting->manager_account_id, dev_setting->unit_id);
        CommunitAccountInfoList accounts;
        GetPermissiveUnitAccountListByMac(dev_setting->mac, dev_setting->unit_id, accounts);
        CommunityDevContact contact(contact_root_path);
        contact.SetContext(config_context_);
        contact.SetCommunityInfo(g_communit_info_);
        contact.UpdateCommunityPublicContactFile(dev_setting, accounts, g_all_node_app_map_, csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT);
    }
    GetDeviceControlInstance()->DestoryDeviceSettingList(dev_setting); 

}

void CommConfigHandle::UpdateMacDevContact(const std::string& mac)
{   
    DEVICE_SETTING* dev_setting = config_context_->GetMacDeviceInGlobal(mac);
    if (!dev_setting)
    {
        AK_LOG_WARN << "Devices is not exist, mac=" <<  mac;
        return;
    }      
    if(dev_setting->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC || dev_setting->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
        UpdateCommunityUpdatePubMacContact(mac);
    }

    else if(dev_setting->grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
    {
        UpdateNodeDevContactList();
    }
    config_context_->ReleaseDeviceSetting(dev_setting);
}

std::string CommConfigHandle::GetUnitName(uint32_t unit_id)
{
    const auto &it = units_map_.find(unit_id);
    if (it != units_map_.end())
    {
        return it->second.unit_name;
    }
    return "";
}


