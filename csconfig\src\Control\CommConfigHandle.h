#ifndef __COMM_CONFIG_HANDLE__
#define __COMM_CONFIG_HANDLE__

#include "BasicDefine.h"
#include "AKCSMsg.h"
#include "CommunityMng.h"
#include "dbinterface/CommunityInfo.h"
#include "DevConfig.h"
#include "DevContact.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "DeviceControl.h"
#include "CommConfigHandleDevices.h"
#include "dbinterface/CommunityUnit.h"
#include "UpdateConfigContext.h"
#include "dbinterface/CommunityRoom.h"
#include "DeviceExternPushButton.h"

typedef struct DEVICE_SETTING_T DEVICE_SETTING;


class CommConfigHandle
{
public:
    CommConfigHandle(uint32_t mng_id, uint32_t unit_id, const std::string &node, const std::vector<std::string> macs);
    ~CommConfigHandle();

    void UpdateNodeDevConfig();
    void UpdateUnitDevConfig();
    void UpdatePubDevConfig();
    void UpdateNodeDevRf();
    void UpdateUnitDevRf();
    void UpdatePubDevRf();
    void UpdateNodeDevPrivatekey();
    void UpdateUnitDevPrivatekey();
    void UpdatePubDevPrivatekey();
    void UpdateNodeDevContactList();
    void UpdateUnitDevContactList();
    void UpdatePubDevContactList();


    //更新楼栋下所有用户设备的联系人
    void UpdateUnitAllNodeDevContactList();
    //更新部分用户设备的联系人
    void UpdateSomeNodeDevContactList(const std::set<std::string>& nodes);
    //更新社区下所有用户设备得联系人
    void UpdateCommunityAllNodeDevContactList();
    //更新单元下所有用户设备得配置文件
    void UpdateUnitAllNodeDevConfig();
    //更新社区下所有单元公共设备得联系人
    void UpdateCommunityAllUnitDevContactList();
    //更新社区下所有单元公共设备privatekey
    void UpdateCommunityAllUnitDevPrivatekey();
    //更新社区下所有用户设备的配置文件
    void UpdateCommunityAllNodeDevConfig();
    //更新社区下所有单元公共设备RF
    void UpdateCommunityAllUnitDevRf();
    //更新社区下所有用户设备RF
    void UpdateCommunityAllNodeDevRf();
    //更新社区下所有用户设备的PrivateKey
    void UpdateCommunityAllNodeDevPrivatekey();
    //更新社区下所有单元公共设备的配置
    void UpdateCommunityAllUnitDevConfig();

	//更新社区下一个单元设备的人脸数据
	void UpdateCommunityOneUnitFace();
	//更新社区下所有单元设备的人脸数据
	void UpdateCommunityAllUnitFace();
	//更新社区下一个用户设备的人脸数据
	void UpdateCommunityOneNodeFace();
	//更新社区下所有用户设备的人脸数据
	void UpdateCommunityAllNodeFace();
	//更新社区公共设备的人脸数据
	void UpdateCommunityPubFace();
    //更新单个设备配置
    void UpdateMacDevConfig(const std::string& mac);

    //更新user数据
    void UpdateMacUser(const std::string &mac);
    void UpdateCommunityAllDevUser();
    void UpdateNodeUser();
    void UpdateNodeUserWhenDelete();
    
    //schedule
    //以设备为粒度
    void UpdateDevSchedule(const std::string &mac);
    void UpdateNodeDevSchedule();
    void UpdateUnitDevSchedule();
    void UpdatePubDevSchedule();
    void UpdateCommunityAllDevSchedule();
    void UpdateNodeDevContactListByUnitMac(const std::string& mac);

    /*联动更新，更新相关联的设备*/
    void UpdateNodeConfigEvent()
    {
        UpdateNodeDevConfig();
        UpdateUnitDevConfig();
        UpdatePubDevConfig();    
    }
    void UpdateNodeContactEvent()
    {
        UpdateNodeDevContactList();
        UpdateUnitDevContactList();
        UpdatePubDevContactList();    
    }
    void UpdateNodeRfEvent()
    {
        UpdateNodeDevRf();
        UpdateUnitDevRf();
        UpdatePubDevRf();   
    }
    void UpdateNodePrivatekeyEvent()
    {
        UpdateNodeDevPrivatekey();
        UpdateUnitDevPrivatekey();
        UpdatePubDevPrivatekey();
    } 
    //刷社区下所有设备联系人
    void UpdateCommunityContactEvent()
    {
        UpdatePubDevContactList();
        UpdateCommunityAllUnitDevContactList();
        UpdateCommunityAllNodeDevContactList();
    }

    void UpdateNode(const std::string& node);
    void SetUnitID(uint32_t unit_id);

    void UpdateCommunityUpdatePubMacContact(const std::string& mac);

    void UpdateMacDevContact(const std::string& mac);

    int InitSuccess()
    {
        return g_communit_info_->InitSuccess();
    }

private:
    void InitNodeDev();
    void InitUnitDev();
    void InitPubDev();
    void InitAllAppList();/*初始化所有app信息*/
    void InitAllAccessGroup();
    void InitAllUnitDev();
    void UpdatePrivateKey2AptPin(PRIVATE_KEY* private_key_list);
    void UpdateNodeDevContactList(uint32_t mng_id, uint32_t unit_id, const std::string &node,
       DEVICE_SETTING*node_dev_list, DEVICE_SETTING* unit_dev_list, DEVICE_SETTING* pub_dev_list);
    void UpdateNodeDevConfig(uint32_t mng_id, uint32_t unit_id, const std::string &node, DEVICE_SETTING *node_dev_list);
    void UpdateUnitDevConfig(uint32_t mng_id, uint32_t unit_id, DEVICE_SETTING *unit_dev_list);
    void UpdateNodeDevPrivatekey(uint32_t mng_id, uint32_t unit_id, const std::string &node, DEVICE_SETTING* node_dev_list);
    void UpdateUnitDevRf(uint32_t mng_id, uint32_t unit_id, DEVICE_SETTING* unit_dev_list);
    void UpdateNodeDevRf(uint32_t mng_id, uint32_t unit_id, const std::string &node, DEVICE_SETTING* node_dev_list);
	void UpdateUnitDevFace(uint32_t mng_id, uint32_t unit_id, DEVICE_SETTING *unit_dev_list);
	void UpdateNodeDevFace(uint32_t mng_id, uint32_t unit_id, const char *account, NodeAppList node_app_list, DEVICE_SETTING *node_dev_list);  
    void GetPermissiveUnitAccountListByMac(const std::string& unit_mac, int unit_id, CommunitAccountInfoList& accounts);
    void GetPermissiveUnitDevListByNode(const std::string& node, int unit_id, ResidentDeviceList& unit_dev_list);
    void InitAllUserPermissions(const std::string& node, const NodeAppList& apps);
    void GetPermissiveUnitListByNode(const std::string& node, int unit_id, std::set<int> &unit_set);    
    DEVICE_SETTING* GetUnitDeviceByNode(const std::string &node, uint32_t unit_id);


    std::string GetUnitName(uint32_t unit_id);
    std::string node_;
    uint32_t mng_id_;
    uint32_t unit_id_;
    int app_list_init_;
    int g_rtp_confuse_;/*g_开头为全局初始化的变量*/
    int g_mng_sip_type_;/*g_开头为全局初始化的变量*/
    int g_rtsp_type_;
    CommunityInfoPtr g_communit_info_;/*g_开头为全局初始化的变量*/
    //每个函数需要用到下面的dev,就要自己调用,构造函数没有初始化对应值
    DEVICE_SETTING* node_dev_list_;/*当前操作的node的所有设备*/
    DEVICE_SETTING* unit_dev_list_;/*当前操作单元的所有公共设备*/
    DEVICE_SETTING* pub_dev_list_;/*当前操作所有最外层公共设备*/
    
    MapNodeAppList g_all_node_app_map_;
    MapUnitCommunitAccountList g_unit_node_map_;
    MapUserAGDeviceListPtr g_user_access_group_devices_map_ptr_;

    std::vector<COMMUNIT_UNIT_INFO> g_unit_list_;/*g_开头为全局初始化的变量*/

    std::map<int, ResidentDeviceList> g_all_unit_dev_list_;//各楼栋下的devlist
    std::map<std::string, int> g_all_unit_mac_unitid_map_;//楼栋设备mac与所在楼栋unitid
    
    std::map<std::string, std::set<int>> g_node_access_group_unit_map_;//房间node与有对应权限的楼栋unit列表(房间成员有跨楼栋权限组的话，就对多个楼栋有权限)
    std::map<std::string, std::set<int>> g_unitdev_access_group_unit_map_; //楼栋设备mac与有对应权限的楼栋unit列表
    std::map<std::string, std::set<std::string>> g_all_account_pub_mac_map_;//用户account与有权限的公共设备mac

    //全部的社区设备数据
    CommConfigHandleDevices devices_contorl_;
    PersonalAccountCnfInfoMapPtr node_cnf_map_ptr;
    SipContorlPtr sip_contorl_ptr_;
    CommunityRoomContorl room_contorl_;
    CommunityUnitMap units_map_;
    NodeAppList pm_app_list_;
    DeviceExternPushButtonPtr push_button_control_;

    ConfigContextPtr config_context_;
};


#endif


