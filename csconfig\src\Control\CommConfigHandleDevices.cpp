#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <assert.h>
#include <string>
#include "CommConfigHandleDevices.h"
#include "PersonnalDeviceSetting.h"
#include <boost/algorithm/string.hpp>


#define CopyDeviceSetting(dev_header, dev_cur, in) \
    do { \
        DEVICE_SETTING* dev_pointer  = new DEVICE_SETTING; \
        memcpy(dev_pointer, in, sizeof(DEVICE_SETTING));    \
        InsertIntoDevicesList(dev_header, dev_cur, dev_pointer);    \
       } while(0)



CommConfigHandleDevices::CommConfigHandleDevices()
{
}

void CommConfigHandleDevices::Init(uint32_t mng_id, PersonalAccountCnfInfoMapPtr &cnf_map, SipContorlPtr &sip_contorl)
{
    GetDeviceSettingInstance()->GetCommunityAllDev(mng_id, global_all_dev_list_, global_unit_dev_map_, global_unit_uuid_dev_map_, 
        global_pub_dev_list_, global_node_dev_map_, global_mac_dev_map_, global_uuid_dev_map_);
    node_cnf_map_ptr_ = cnf_map;
    sip_contorl_ = sip_contorl;

    pub_unit_dev_list_ = nullptr;
    mng_dev_list_ = nullptr;
}


void CommConfigHandleDevices::ReleaseDeviceSetting(DEVICE_SETTING *devlist)
{
    GetDeviceControlInstance()->DestoryDeviceSettingList(devlist); 
}

/*最外围公共设备*/
DEVICE_SETTING* CommConfigHandleDevices::GetPubDeviceInGlobal()
{
    DEVICE_SETTING* dev_cur = nullptr;
    DEVICE_SETTING* dev_header = nullptr;

    for (const auto &dev : global_pub_dev_list_) {       
        CopyDeviceSetting(&dev_header, &dev_cur, dev);
    }         
    return dev_header;    
}

/*单元公共设备*/ 
DEVICE_SETTING* CommConfigHandleDevices::GetUnitDeviceInGlobal(uint32_t unit_id)
{
    DEVICE_SETTING* dev_cur = nullptr;
    DEVICE_SETTING* dev_header = nullptr;

    const auto &unit_dev_it = global_unit_dev_map_.equal_range(unit_id);
    for (auto it = unit_dev_it.first; it != unit_dev_it.second; ++it) {
        CopyDeviceSetting(&dev_header, &dev_cur, it->second);
    }         
    return dev_header;    
}

DEVICE_SETTING* CommConfigHandleDevices::GetUnitDeviceInGlobal(std::set<int> unit_set)
{
    DEVICE_SETTING* dev_cur = nullptr;
    DEVICE_SETTING* dev_header = nullptr;

    for (auto unit_id : unit_set)
    {
        const auto &unit_dev_it = global_unit_dev_map_.equal_range(unit_id);
        for (auto it = unit_dev_it.first; it != unit_dev_it.second; ++it) {
            CopyDeviceSetting(&dev_header, &dev_cur, it->second);
        }  
    }
       
    return dev_header;    
}

DEVICE_SETTING* CommConfigHandleDevices::GetAllDeviceInGlobal()
{
    DEVICE_SETTING* dev_cur = nullptr;
    DEVICE_SETTING* dev_header = nullptr;

    for (auto &dev : global_all_dev_list_) {
        CopyDeviceSetting(&dev_header, &dev_cur, dev);
    }         
    return dev_header;    
}

DEVICE_SETTING* CommConfigHandleDevices::GetAllPubUnitDeviceInGlobal()
{
    DEVICE_SETTING* dev_cur = nullptr;
    DEVICE_SETTING* dev_header = nullptr;

    for (auto &dev : global_all_dev_list_) {
        if(dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC || dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
        {
            CopyDeviceSetting(&dev_header, &dev_cur, dev);
        }
    }
    return dev_header;    
}

DEVICE_SETTING* CommConfigHandleDevices::GetAllMngDeviceInGlobal()
{
    DEVICE_SETTING* dev_cur = nullptr;
    DEVICE_SETTING* dev_header = nullptr;

    for (auto &dev : global_all_dev_list_) {
        if(dev->type == DEVICE_TYPE_MANAGEMENT)
        {
            CopyDeviceSetting(&dev_header, &dev_cur, dev);
        }
    }
    return dev_header;    
}


DEVICE_SETTING* CommConfigHandleDevices::GetMacDeviceInGlobal(const std::string &mac)
{
    const auto &it = global_mac_dev_map_.find(mac);
    if (it == global_mac_dev_map_.end())
    {
        return nullptr;
    }
    DEVICE_SETTING* dev_cur = nullptr;
    DEVICE_SETTING* dev_header = nullptr;    
    CopyDeviceSetting(&dev_header, &dev_cur, it->second);    
    return dev_header;
}

DEVICE_SETTING* CommConfigHandleDevices::GetDeviceInGlobalByUUID(const std::string &uuid)
{
    const auto &it = global_uuid_dev_map_.find(uuid);
    if (it == global_uuid_dev_map_.end())
    {
        return nullptr;
    }
    DEVICE_SETTING* dev_cur = nullptr;
    DEVICE_SETTING* dev_header = nullptr;    
    CopyDeviceSetting(&dev_header, &dev_cur, it->second);    
    return dev_header;
}


int CommConfigHandleDevices::GetPhoneStatusByCallType(const int call_type)
{
    //V4.3修改去掉落地开关，当是calltype是如下两个选项时候开启落地
    if (call_type == NODE_CALL_TYPE_INDOOR_PHONE || call_type == NODE_CALL_TYPE_APP_INDOOR_BACK_PHONE
     || call_type == NODE_CALL_TYPE_INDOOR_BACK_PHONE || call_type == NODE_CALL_TYPE_INDOOR_BACK_APP_BACK_PHONE)
    {
        return 1;
    }
    else
    {
        return 0;
    }
}

// 这里获取没有主账号的参数配置
DEVICE_SETTING* CommConfigHandleDevices::GetNodeIndoorOrMngDeviceSettingList(const std::string& node)
{
    DEVICE_SETTING* dev_cur = nullptr;
    DEVICE_SETTING* dev_header = nullptr;

    const auto &dev_list = global_node_dev_map_.equal_range(node);
    for (auto it = dev_list.first; it != dev_list.second; ++it)
    {
        if(it->second->type == DEVICE_TYPE_INDOOR || it->second->type == DEVICE_TYPE_MANAGEMENT)
        {
            CopyDeviceSetting(&dev_header, &dev_cur, it->second); 
        }     
    }
    return dev_header;
}


// 获取社区apt内所有设备联系人配置 : apt
DEVICE_SETTING* CommConfigHandleDevices::GetNodeDeviceSettingList(const std::string& node)
{
    const auto &it = node_cnf_map_ptr_->find(node);
    if (it == node_cnf_map_ptr_->end())
    {
        AK_LOG_WARN << "Node Cnf not found. node:" << node;
        return nullptr;
    }
    const PersonalAccountCnfInfo &node_config = it->second;
    
    int phone_status = GetPhoneStatusByCallType(node_config.call_type);
    std::string sip_group = sip_contorl_->GetNodeSipGroup(node);
    
    DEVICE_SETTING* dev_cur = nullptr;
    DEVICE_SETTING* dev_header = nullptr;
    std::list<DevNetInfo> push_ip_list;
    
    const auto &dev_list = global_node_dev_map_.equal_range(node);
    for (auto it = dev_list.first; it != dev_list.second; ++it)
    {
        CopyDeviceSetting(&dev_header, &dev_cur, it->second);
        
        // 写主账号配置信息
        dev_cur->motion_time = node_config.motion_time;
        dev_cur->enable_motion = node_config.enable_motion;
        dev_cur->enable_package_detection = node_config.enable_package_detection;
        // dev_cur->enable_sound_detection = node_config.enable_sound_detection;
        // dev_cur->sound_type = node_config.sound_type;
        dev_cur->robin_call_time = node_config.robin_call_time;
        dev_cur->enable_robin_call = node_config.enable_robin_call;

        // 启用ip直播才能添加室内机IP到pushbutton 并且要把室内机移出群组 //管理中心机也可能放在室内 ********
        if ((dev_cur->type == DEVICE_TYPE_INDOOR || dev_cur->type == DEVICE_TYPE_MANAGEMENT) && node_config.ip_direct)
        {
            DevNetInfo infos = {0};
            infos.netgroup_num = dev_cur->netgroup_num;
            snprintf(infos.ip, sizeof(infos.ip), "%s", dev_cur->ip_addr);
            snprintf(infos.sip, sizeof(infos.sip), "%s", dev_cur->sip_account);
            push_ip_list.push_front(infos);
        }        
    }   

    DEVICE_SETTING* cur_dev = dev_header;
    while (cur_dev != nullptr)
    {
        if (cur_dev->type == DEVICE_TYPE_DOOR || cur_dev->type == DEVICE_TYPE_STAIR || cur_dev->type == DEVICE_TYPE_WALL || cur_dev->type == DEVICE_TYPE_ACCESS)
        {
            std::list<DevNetInfo>::iterator it_net = push_ip_list.begin();

            std::stringstream push_button;
            // 写ip/sip
            while (it_net != push_ip_list.end())
            {
               if (it_net->netgroup_num == cur_dev->netgroup_num && node_config.ip_direct)
               {
                   push_button << it_net->ip << ";";
               }
               else if (it_net->netgroup_num != cur_dev->netgroup_num || !node_config.ip_direct)
               {
                   push_button << it_net->sip <<";";
               }
               it_net++;
            }
            
            // 写sip_group
             push_button << sip_group <<";";
    
            // 写落地
            if (phone_status)
            {
               if (node_config.phone.size() > 0)
               {
                   push_button << PHONE_CALL_OUT_SUBFIX << node_config.phone_code << node_config.phone << ";";
               }
               if (node_config.phone2.size() > 0)
               {
                   push_button << PHONE_CALL_OUT_SUBFIX << node_config.phone_code << node_config.phone2 << ";";
               }
               if (node_config.phone3.size() > 0)
               {
                   push_button << PHONE_CALL_OUT_SUBFIX << node_config.phone_code << node_config.phone3 << ";";
               }
            }
           
            std::string push = push_button.str();
            while (strstr(push.c_str(), ";;"))
            {
               boost::replace_all(push, ";;", ";");
            }
            snprintf(cur_dev->push_button, sizeof(cur_dev->push_button), "%s", push.c_str());
        }             
        cur_dev = cur_dev->next;
    }

    return dev_header;  
}

/*单元公共设备*/ 
DEVICE_SETTING* CommConfigHandleDevices::GetUnitUUIDDeviceInGlobal(const std::string& unit_uuid)
{
    DEVICE_SETTING* dev_cur = nullptr;
    DEVICE_SETTING* dev_header = nullptr;

    const auto &unit_dev_it = global_unit_uuid_dev_map_.equal_range(unit_uuid);
    for (auto it = unit_dev_it.first; it != unit_dev_it.second; ++it) {
        CopyDeviceSetting(&dev_header, &dev_cur, it->second);
    }         
    return dev_header;    
}

/*
// 获取社区apt内所有设备联系人配置 : apt
DEVICE_SETTING* CDeviceSetting::GetNodeDeviceSettingList(const std::string& node)
{
    DEVICE_SETTING* dev_cur = NULL;
    DEVICE_SETTING* dev_header = NULL;

    ResidentDeviceList dev_list;
    dbinterface::ResidentDevices::GetNodeDevList(node, dev_list);
    if (dev_list.size() == 0)
    {
        return dev_header;
    }

    ResidentPerAccount node_info;
    memset(&node_info, 0, sizeof(node_info));
    if (0 == dbinterface::ResidentPersonalAccount::GetUserInfoByAccount(node, node_info))
    {
        PersonalAccountCnfInfo node_config;
        dbinterface::PersonalAccountCnf::GetPeronalAccountCnfByNode(node, node_config);
        
        node_info.phone_status = GetPhoneStatusByCallType(node_config.call_type);
        std::string sip_group = dbinterface::Sip::GetSipGroupByNode(node);
      
        std::list<DevNetInfo> push_ip_list;
        // 给node下的所有设备写主账号配置信息;记录室内机管理机的ip/sip,用于写pushbutton
        for (auto& dev : dev_list)
        {
            // 启用ip直播才能添加室内机IP到pushbutton 并且要把室内机移出群组 //管理中心机也可能放在室内 ********
            if ((dev.dev_type == DEVICE_TYPE_INDOOR || dev.dev_type == DEVICE_TYPE_MANAGEMENT) && node_info.ip_direct)
            {
                DevNetInfo infos = {0};
                infos.netgroup_num = dev.netgroup_num;
                snprintf(infos.ip, sizeof(infos.ip), "%s", dev.ipaddr);
                snprintf(infos.sip, sizeof(infos.sip), "%s", dev.sip);
                push_ip_list.push_front(infos);
        
            }
            // 写主账号配置信息
            dev.motion_time = node_config.motion_time;
            dev.enable_motion = node_config.enable_motion;
            dev.robin_call_time = node_config.robin_call_time;
            dev.enable_robin_call = node_config.enable_robin_call;
        }
        
        // 给node下的所有门口机写pushbutton/robin_call_val
        for (auto& dev : dev_list)
        {
            if (dev.dev_type == DEVICE_TYPE_DOOR || dev.dev_type == DEVICE_TYPE_STAIR || dev.dev_type == DEVICE_TYPE_WALL || dev.dev_type == DEVICE_TYPE_ACCESS)
            {
                std::string robincall;
                std::list<DevNetInfo>::iterator it = push_ip_list.begin();
        
                // 写ip/sip
                while (it != push_ip_list.end())
                {
                   if (it->netgroup_num == dev.netgroup_num && node_info.ip_direct)
                   {
                       snprintf(dev.pushbutton + strlen(dev.pushbutton), sizeof(dev.pushbutton) - strlen(dev.pushbutton), "%s;", it->ip);
                   }
                   else if (it->netgroup_num != dev.netgroup_num || !node_info.ip_direct)
                   {
                       snprintf(dev.pushbutton + strlen(dev.pushbutton), sizeof(dev.pushbutton) - strlen(dev.pushbutton), "%s;", it->sip);
                   }
                   it++;
                }
                
                // 写sip_group
                snprintf(dev.pushbutton + strlen(dev.pushbutton), sizeof(dev.pushbutton) - strlen(dev.pushbutton), "%s;", sip_group.c_str());
        
                // 写落地
                if (node_info.phone_status)
                {
                   if (strlen(node_info.phone) > 0)
                   {
                       snprintf(dev.pushbutton + strlen(dev.pushbutton), sizeof(dev.pushbutton) - strlen(dev.pushbutton), "%s%s%s;", PHONE_CALL_OUT_SUBFIX, node_info.phone_code, node_info.phone);
                   }
                   if (strlen(node_info.phone2) > 0)
                   {
                       snprintf(dev.pushbutton + strlen(dev.pushbutton), sizeof(dev.pushbutton) - strlen(dev.pushbutton), "%s%s%s;", PHONE_CALL_OUT_SUBFIX, node_info.phone_code, node_info.phone2);
                   }
                   if (strlen(node_info.phone3) > 0)
                   {
                       snprintf(dev.pushbutton + strlen(dev.pushbutton), sizeof(dev.pushbutton) - strlen(dev.pushbutton), "%s%s%s;", PHONE_CALL_OUT_SUBFIX, node_info.phone_code, node_info.phone3);
                   }
                }
               
                while (strstr(robincall.c_str(), ";;"))
                {
                   boost::replace_all(robincall, ";;", ";");
                }
                snprintf(dev.robin_call_val, sizeof(dev.robin_call_val), "%s", robincall.c_str());
        
                std::string push = dev.pushbutton;
                while (strstr(push.c_str(), ";;"))
                {
                   boost::replace_all(push, ";;", ";");
                }
                snprintf(dev.pushbutton, sizeof(dev.pushbutton), "%s", push.c_str());
            }
        }
        
        for (auto &dev : dev_list)
        {
            DEVICE_SETTING* dev_pointer  = new DEVICE_SETTING;
            TransferDevToPointer(dev, dev_pointer);
            dev_pointer->motion_time = dev.motion_time;
            dev_pointer->enable_motion = dev.enable_motion;
            dev_pointer->robin_call_time = dev.robin_call_time;
            dev_pointer->enable_robin_call = dev.enable_robin_call;
            Snprintf(dev_pointer->push_button, sizeof(dev_pointer->push_button), dev.pushbutton);
            Snprintf(dev_pointer->robin_call_val, sizeof(dev_pointer->robin_call_val), dev.robin_call_val);
            InsertIntoDevicesList(&dev_header, &dev_cur, dev_pointer);
        }

    }
    return dev_header;
}

*/


