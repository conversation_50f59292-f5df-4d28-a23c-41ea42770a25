#ifndef __COMM_CONFIG_DEVICES_HANDLE__
#define __COMM_CONFIG_DEVICES_HANDLE__

#include "BasicDefine.h"
#include "AKCSMsg.h"
#include "CommunityMng.h"
#include "dbinterface/CommunityInfo.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "DeviceControl.h"
#include "DeviceSetting.h"
#include "dbinterface/PersonalAccountCnf.h"
#include "dbinterface/Sip.h"



class CommConfigHandleDevices
{
public:
    CommConfigHandleDevices();
    ~CommConfigHandleDevices()
    {
        for (auto dev : global_all_dev_list_)
        {
            delete dev;
        }
    }

    void Init(uint32_t mng_id, PersonalAccountCnfInfoMapPtr &cnf_map, SipContorlPtr &sip);
    DEVICE_SETTING* GetPubDeviceInGlobal();
    DEVICE_SETTING* GetUnitDeviceInGlobal(uint32_t unit_id);
    DEVICE_SETTING* GetUnitDeviceInGlobal(std::set<int> unit_set);
    DEVICE_SETTING* GetUnitUUIDDeviceInGlobal(const std::string& unit_uuid);
    DEVICE_SETTING* GetAllDeviceInGlobal();
    DEVICE_SETTING* GetMacDeviceInGlobal(const std::string &mac);
    DEVICE_SETTING* GetDeviceInGlobalByUUID(const std::string &uuid);
    DEVICE_SETTING* GetAllPubUnitDeviceInGlobal();
    DEVICE_SETTING* GetAllMngDeviceInGlobal();
    DEVICE_SETTING* GetNodeIndoorOrMngDeviceSettingList(const std::string& node);
    void ReleaseDeviceSetting(DEVICE_SETTING *devlist);
    std::string GetNodeSipGroup(const std::string &node);
    int GetPhoneStatusByCallType(const int call_type);
    DEVICE_SETTING* GetNodeDeviceSettingList(const std::string& node);
    void GetPermissiveUnitListByNode(const std::string& node, int unit_id, std::set<int> &unit_set);  

private:
    //全部的社区设备数据
    DeviceSettingList global_all_dev_list_;
    DeviceSettingIntMap global_unit_dev_map_;
    DeviceSettingStrMap global_unit_uuid_dev_map_;
    DeviceSettingList global_pub_dev_list_;
    DeviceSettingStrMap global_node_dev_map_;
    DeviceSettingStrMap global_mac_dev_map_;
    DeviceSettingStrMap global_uuid_dev_map_;
    
    PersonalAccountCnfInfoMapPtr node_cnf_map_ptr_;
    SipContorlPtr sip_contorl_;  

    DEVICE_SETTING* pub_unit_dev_list_;
    DEVICE_SETTING* mng_dev_list_;//所有的管理机
};

 
#endif


