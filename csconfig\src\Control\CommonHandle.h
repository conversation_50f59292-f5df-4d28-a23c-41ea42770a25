#ifndef __COMMON_HANDLE_CONTROL_H__
#define __COMMON_HANDLE_CONTROL_H__

#include <map>
#include "AK.Server.pb.h"
#include <set>
#include <algorithm>
#include <chrono>

enum FILTER_REQUEST_RET{    // 过滤重复请求标识
   REQ_FIRST,               // 标记第一次请求
   REQ_SECOND,              // 标记第二次请求
   REQ_MORE,                // 标记两次以上的请求
};

class CommonHandle
{
public:
    CommonHandle(){}
    ~CommonHandle(){}

    static int CheckIpchangeRequest(const AK::Server::P2PMainDevConfigRewriteMsg &msg, int project_type);
    static int CheckUserInfoRequest(const AK::Server::P2PMainRequestWriteUserinfo &msg, int project_type);

    static int CheckBigProject(std::time_t start, std::time_t end, int project_id);
    static int IsBigProject(int project_id);
    
    static int UpdateWebCommunityFileUpdateRequestTime(int changetype, uint32_t mng_id, 
        uint32_t unit_id, const std::string &node, std::vector<std::string> &macs);

        
    static int UpdateAccessGroupUpdateRequestTime(int changetype, uint32_t mng_id, const std::string &node, 
         std::set<std::string> &macs, uint32_t ag_id);

    
private:
    static int FilterRequests(const std::string &key, const char *db, int repeate_check_time);
    static int FilterUpdateRequests(const std::string &key, const char *db, int repeate_check_time);
    
    static std::string GetCommunityFileUpdateKey(int changetype, uint32_t mng_id, 
        uint32_t unit_id, const std::string &node, std::vector<std::string> &macs);
    static std::string GetAccessFileUpdateKey(int changetype, uint32_t mng_id, const std::string &node, 
        std::set<std::string> &macs, uint32_t ag_id);
};


#endif //__UNIX_CONTROL_H__
