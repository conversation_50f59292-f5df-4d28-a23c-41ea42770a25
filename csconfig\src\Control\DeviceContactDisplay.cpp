#include "AkLogging.h"
#include "DeviceContactDisplay.h"
#include "dbinterface/resident/CommunityDeviceContact.h"



DeviceContactDisplay::DeviceContactDisplay(const std::string& dev_uuid, int is_new_community)
{
    is_new_ = is_new_community ? true : false;
    CommunityDeviceContactInfoList dev_contact_list;
    dbinterface::CommunityDeviceContact::GetCommunityDeviceContactListByDeviceUUID(dev_uuid, dev_contact_list);
    for(const auto& dev_contact : dev_contact_list)
    {
        switch(dev_contact.type)
        {
            case dbinterface::CommunityDeviceContact::CONTACT_TYPE::BUILDING :
                display_unit_uuid_list_.insert(dev_contact.community_unit_uuid);
                break;    
            case dbinterface::CommunityDeviceContact::CONTACT_TYPE::APT :
                display_apt_uuid_list_.insert(dev_contact.apt_uuid);
                break;  
            case dbinterface::CommunityDeviceContact::CONTACT_TYPE::PERSONAL :
                display_personal_uuid_list_.insert(dev_contact.personal_account_uuid);
                break;  
            case dbinterface::CommunityDeviceContact::CONTACT_TYPE::INDOOR :
                display_indoor_uuid_list_.insert(dev_contact.indoor_device_uuid);
                break;  
        }
    }
}

DeviceContactDisplay::~DeviceContactDisplay()
{

}

int DeviceContactDisplay::PersonalIsDisplay(const std::string& personal_uuid)
{
    if(!is_new_)
    {
        //旧社区全展示
        //TODO 新旧UpdateCommunityPublicContactFile拆开后，旧社区就不要调这个功能了
        return 1;     
    }
    //逐级查询是否设置展示
    auto iter = display_unit_uuid_list_.find(contact_unit_uuid_);
    if(iter != display_unit_uuid_list_.end())
    {
        return 1;
    }
    iter = display_apt_uuid_list_.find(contact_apt_uuid_);    
    if(iter != display_apt_uuid_list_.end())
    {        
        return 1;
    }
    
    iter = display_personal_uuid_list_.find(personal_uuid);    
    if(iter != display_personal_uuid_list_.end())
    {
        return 1;
    }
    
    return 0;
}
int DeviceContactDisplay::IndoorIsDisplay(const std::string& indoor_uuid)
{
    if(!is_new_)
    {
        //旧社区全展示        
        //TODO 新旧UpdateCommunityPublicContactFile拆开后，旧社区就不要调这个功能了
        return 1;     
    }

    //逐级查询是否设置展示
    auto iter = display_unit_uuid_list_.find(contact_unit_uuid_);
    if(iter != display_unit_uuid_list_.end())
    {
        return 1;
    }
    iter = display_apt_uuid_list_.find(contact_apt_uuid_);    
    if(iter != display_apt_uuid_list_.end())
    {
        return 1;
    }
    
    iter = display_indoor_uuid_list_.find(indoor_uuid);    
    if(iter != display_indoor_uuid_list_.end())
    {
        return 1;
    }

    return 0;

}



