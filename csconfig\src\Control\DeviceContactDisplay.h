#ifndef __DEVICE_CONTACT_DISPLAY_H__
#define __DEVICE_CONTACT_DISPLAY_H__

#include <set>
#include <string>

class DeviceContactDisplay
{
public:
    DeviceContactDisplay(const std::string& dev_uuid, int is_new_community);
    ~DeviceContactDisplay();
    void SetUnitUUID(const std::string& unit_uuid)  
    {
        contact_unit_uuid_ = unit_uuid;
    }
    void SetAptUUID(const std::string& apt_uuid) 
    {
        contact_apt_uuid_ = apt_uuid;
    }
    int PersonalIsDisplay(const std::string& personal_uuid);
    int IndoorIsDisplay(const std::string& indoor_uuid);
    
private:
    std::set<std::string> display_unit_uuid_list_;
    std::set<std::string> display_apt_uuid_list_;
    std::set<std::string> display_personal_uuid_list_;
    std::set<std::string> display_indoor_uuid_list_;
    std::string contact_unit_uuid_;
    std::string contact_apt_uuid_;
    bool is_new_;
};

#endif
