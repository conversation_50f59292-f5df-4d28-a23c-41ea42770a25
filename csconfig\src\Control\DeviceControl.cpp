#include <stdio.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <errno.h>
#include <pthread.h>
#include <assert.h>
#include <string.h>
#include <ctype.h>
#include <string>
#include <list>
#include <sstream>
#include <iomanip>
#include <net/if.h>
#include <sys/ioctl.h>
#include "AkcsWebMsgSt.h"
#include "AES256.h"
#include "util_cstring.h"
#include "BasicDefine.h"
#include "CharChans.h"
#include "ConfigDef.h"
#include "AKCSMsg.h"
#include "AkLogging.h"
#include "AdaptUtility.h"
#include "Rldb.h"
#include "DeviceSetting.h"
#include "Md5.h"
#include "PrivateKeyControl.h"
#include "DeviceControl.h"
#include "PersonnalDeviceSetting.h"
#include "util.h"
#include "PersonalAccount.h"
#include "CommunityMng.h"
#include "RfKeyControl.h"
#include "dbinterface/CommunityInfo.h"
#include "AkcsMonitor.h"
#include "AkcsCommonDef.h"

extern CSCONFIG_CONF gstCSCONFIGConf;

#define SDMC_SOCKET_UDP_PORT        8502

CDeviceControl* GetDeviceControlInstance()
{
    return CDeviceControl::GetInstance();
}

CDeviceControl::CDeviceControl()
{

}

CDeviceControl::~CDeviceControl()
{

}

CDeviceControl* CDeviceControl::instance = NULL;

CDeviceControl* CDeviceControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CDeviceControl();
    }

    return instance;
}

//销毁设备列表
void CDeviceControl::DestoryDeviceSettingList(DEVICE_SETTING* device_header)
{
    DEVICE_SETTING* cur_device = NULL;
    while (NULL != device_header)
    {
        cur_device = device_header;
        device_header = device_header->next;
        delete cur_device;
    }
}



DEVICE_SETTING* CDeviceControl::GetDeviceSettingByMac(const std::string& mac)
{
    DEVICE_SETTING* dev = GetPersonnalDevSettingInstance()->GetDeviceSettingByMac(mac);
    if (!dev)
    {
        return GetDeviceSettingInstance()->GetDeviceSettingByMac(mac);
    }
    return dev;
}

//根据传入的mac字符串获取设备列表，字符串格式如:12345678901,0231654879
DEVICE_SETTING* CDeviceControl::GetDeviceSettingListFromMac(std::string device_list)
{
    if (device_list.length() == 0)
    {
        return NULL;
    }
    DEVICE_SETTING* device_setting_header = NULL;
    DEVICE_SETTING* cur_device_setting = NULL;
    std::vector<std::string> oVec;
    SplitString(device_list, ";", oVec);

    //循环解析strDevieList
    for (auto& strmac : oVec)
    {
        DEVICE_SETTING* new_node = new DEVICE_SETTING;
        memset(new_node, 0, sizeof(DEVICE_SETTING));
        Snprintf(new_node->mac, sizeof(new_node->mac), strmac.c_str());
        if (device_setting_header == NULL)
        {
            device_setting_header = new_node;
        }
        else
        {
            cur_device_setting->next = new_node;
        }

        cur_device_setting = new_node;
    }

    return device_setting_header;
}

bool CDeviceControl::DeviceIsBelongBuilding(const uint32_t type, const uint32_t unit_id, const std::vector<int>& unit_list)
{
	//如果不是需要分栋管理的设备类型，那么也是归属于所有社区用户
	if (!DeviceIsManageBuilding(type))
	{
		return true;
	}

	if (unit_id <= 0)
	{
		return true;
	}

	//判断用户的所在单元是否在设备的管理单元
	auto iter = find(unit_list.begin(), unit_list.end(), unit_id);  
	if (iter != unit_list.end())
	{
		return true;
	}

	return false;
}

bool CDeviceControl::DeviceIsManageBuilding(uint32_t type)
{

	if(DEVICE_TYPE_MANAGEMENT == type ||
		DEVICE_TYPE_DOOR == type ||
		DEVICE_TYPE_STAIR == type ||
		DEVICE_TYPE_ACCESS == type) 
	{
		return true;
	}

	return false;
}

