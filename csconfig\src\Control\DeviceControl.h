#ifndef __DEVICE_CONTROL_H__
#define __DEVICE_CONTROL_H__
#include <vector>
#include <map>

#include "BasicDefine.h"
#include "AKCSMsg.h"
//#include "CommConfigHandle.h"
#include "dbinterface/CommunityInfo.h"



typedef struct DEVICE_SETTING_T DEVICE_SETTING;
typedef struct CONFIG_MODULE_T CONFIG_MODULE;
typedef struct PRIVATE_KEY_T PRIVATE_KEY;
typedef struct DEVICE_UPGRADE_T DEVICE_UPGRADE;


using DeviceSettingList = std::vector<DEVICE_SETTING*>;
using DeviceSettingIntMap = std::multimap<uint32_t, DEVICE_SETTING*>;
using DeviceSettingStrMap = std::multimap<std::string, DEVICE_SETTING*>;



class CDeviceControl
{
public:
    CDeviceControl();
    ~CDeviceControl();
    static CDeviceControl* GetInstance();
    void DestoryDeviceSettingList(DEVICE_SETTING* device_header);
    
    DEVICE_SETTING* GetDeviceSettingByMac(const std::string& mac);
    DEVICE_SETTING* GetDeviceSettingListFromMac(std::string device_list);
    
	bool DeviceIsBelongBuilding(const uint32_t type, const uint32_t unit_id, const std::vector<int>& unit_list);
	bool DeviceIsManageBuilding(uint32_t type);
private:
    static CDeviceControl* instance;
    //检查连接情况
    int CheckConnect();

};

CDeviceControl* GetDeviceControlInstance();

#endif

