#include "DeviceExternPushButton.h"
#include <map>
#include <string>
#include <vector>
#include "dbinterface/Account.h"
#include <mutex>

DeviceExternPushButton::DeviceExternPushButton()
{
    is_push_button_initialized_ = false;

}

void DeviceExternPushButton::InitPushButton(const std::string& community_uuid)
{
    //task.AddWork 由于这个用到了多线程,所以要加锁,防止进行了多次初始化
    std::lock_guard<std::mutex> lock(mtx_);
    if(is_push_button_initialized_ == false)
    {
        //初始化device_push_button
        if (0 != dbinterface::ExternPushButton::GetDevicePushButtonMapByProjectUUID(community_uuid, device_extern_push_button_map_))
        {
            return;
        }
        //初始化device_push_button_list
        if (0 != dbinterface::DevicePushButtonList::GetDevicePushButtonListMapByProjectUUID(community_uuid, device_extern_push_button_list_map_))
        {
            return;
        }
        is_push_button_initialized_ = true;
    }
    return;
}

int DeviceExternPushButton::GetDevicePushButtonByDeviceUUID(const std::string& device_uuid, DevicePushButton& dev_push_button) 
{
    auto it = device_extern_push_button_map_.find(device_uuid);
    if (it != device_extern_push_button_map_.end()) {
        dev_push_button = it->second; // 找到对应的 DevicePushButton
        return 0;
    }
    return -1; 
}

void DeviceExternPushButton::GetDevicePushButtonListByDeviceUUIDAndModule(const std::string& device_uuid, int module, std::vector<DevicePushButtonListInfo>& module_sequence)
{
    std::string device_extern_push_button_list_key = device_uuid + "_" + std::to_string(module);
    // 查找 map 中的键
    auto range = device_extern_push_button_list_map_.equal_range(device_extern_push_button_list_key);
    for (auto it = range.first; it != range.second; ++it) 
    {
        module_sequence.push_back(it->second);
    }
    return;
}


