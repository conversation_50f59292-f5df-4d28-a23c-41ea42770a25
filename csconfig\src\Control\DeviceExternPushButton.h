#ifndef _DEVICE_EXTERN_PUSH_BUTTON_H_
#define _DEVICE_EXTERN_PUSH_BUTTON_H_
#include <string>
#include <map>
#include <vector>
#include <mutex>

#include "dbinterface/ExternPushButton.h"
#include "dbinterface/DevicePushButtonList.h"



class DeviceExternPushButton
{
public:
    DeviceExternPushButton();
    ~DeviceExternPushButton(){};
    void InitPushButton(const     std::string& community_uuid);
    int GetDevicePushButtonByDeviceUUID(const std::string& device_uuid, DevicePushButton& dev_push_button);
    void GetDevicePushButtonListByDeviceUUIDAndModule(const std::string& device_uuid, int module, std::vector<DevicePushButtonListInfo>& module_sequence);
private:
    std::mutex mtx_;
    bool is_push_button_initialized_;
    DevicePushButtonMap device_extern_push_button_map_ ;
    DevicePushButtonListInfoMap device_extern_push_button_list_map_;
};
using DeviceExternPushButtonPtr = std::shared_ptr<DeviceExternPushButton>;
#endif

