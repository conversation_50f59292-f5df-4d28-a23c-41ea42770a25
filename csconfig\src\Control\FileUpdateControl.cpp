#include <stdio.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <errno.h>
#include <pthread.h>
#include <assert.h>
#include <string.h>
#include <ctype.h>
#include <vector>
#include <string>
#include <sstream>
#include <evpp/evnsq/producer.h>
#include "AK.Adapt.pb.h"
#include "AK.Crontab.pb.h"
#include "AkcsMsgDef.h"
#include "AK.Server.pb.h"
#include "AkcsWebMsgSt.h"
#include "FileUpdateControl.h"
#include "CommConfigHandle.h"
#include "PerConfigHandle.h"
#include "AKCSView.h"
#include "IPCControl.h"
#include "PersonnalDeviceSetting.h"
#include "DeviceSetting.h"
#include "FaceMng.h"
#include "DeviceControl.h"
#include "PersonalAccount.h"
#include "DevUser.h"
#include "DevSchedule.h"
#include "ShadowMng.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/ContactFavorite.h"
#include "dbinterface/ContactBlock.h"
#include "dbinterface/ProjectUserManage.h"
#include "CommonHandle.h"
#include "SnowFlakeGid.h"
#include "CommunityDevContact.h"
#include "AkcsWebPduBase.h"
#include "SpecialTubeHandle.h"
#include "AK.Crontab.pb.h"
#include "UnixSocketControl.h"
#include "AkcsCommonDef.h"
#include "MsgIdToMsgName.h"

extern CSCONFIG_CONF gstCSCONFIGConf;
extern int g_special_tube;

CFileUpdateContorl::CFileUpdateContorl()
{

}

CFileUpdateContorl::~CFileUpdateContorl()
{

}

CFileUpdateContorl* CFileUpdateContorl::instance = NULL;

CFileUpdateContorl* CFileUpdateContorl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CFileUpdateContorl();
    }

    return instance;
}
CFileUpdateContorl* GetFileUpdateContorlInstance()
{
    return CFileUpdateContorl::GetInstance();
}

//为了去重把OnCommunityFileUpdate 转换为web过来的格式
void CFileUpdateContorl::CommunityFileUpdateFormateWeb(int changetype, uint32_t mng_id, uint32_t unit_id, 
   const std::string &node, std::vector<std::string> &macs)
{
    //更新设备配置
    AK::Adapt::WebCommunityModifyNotify new_msg;
    new_msg.set_change_type(changetype);
    new_msg.set_community_id(mng_id);
    new_msg.set_unit_id(unit_id);
    new_msg.set_node(node); 
    new_msg.set_already_check(0);
    for (auto &mac : macs)
    {
        new_msg.add_mac_list(mac);
    }

    CAkcsWebPdu web_pdu;
    web_pdu.SetMsgBody(&new_msg);
    web_pdu.SetMsgID(MSG_P2A_NOTIFY_COMMUNITY_MESSAGE);//这个id没用到 因为下面按社区变化处理了
    web_pdu.SetProjectType(project::RESIDENCE);
    GetFileUpdateContorlInstance()->OnCommunityConfigFileUpdate(web_pdu.GetBuffer(), web_pdu.GetLength()); 
}

void CFileUpdateContorl::OnCommunityConfigFileUpdate(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnCommunityConfigFileUpdate The param is NULL";
        return;
    }

    AK::Adapt::WebCommunityModifyNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));

    std::vector<std::string> macs;
    uint32_t mac_cnt = msg.mac_list_size();
    for (uint32_t i = 0; i < mac_cnt; ++i)
    {
        std::string mac = msg.mac_list(i);
        macs.push_back(mac);
    }
    uint32_t mng_id = msg.community_id();
    uint32_t unit_id = msg.unit_id();
    std::string node = msg.node();
    int change_type = msg.change_type();
    int already_check = msg.already_check();
    uint64_t traceid = msg.trace_id();

    AK_LOG_INFO << "OnCommunityConfigFileUpdate message is from delay tube? already_check=" << already_check;
    ConfigFileHandle(change_type, mng_id, unit_id,node, macs, traceid);
    OnDevUpdateCommonHandle(change_type, macs);
}

//这个函数会进行重复的过滤
void CFileUpdateContorl::OnCommunityAccessGroupFileUpdate(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnCommunityAccessGroupFileUpdate The param is NULL";
        return;
    }

    AK::Adapt::WebCommAccessModifyNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));

    std::set<std::string> macs;
    uint32_t mac_cnt = msg.mac_list_size();
    for (uint32_t i = 0; i < mac_cnt; ++i)
    {
        std::string mac = msg.mac_list(i);
        macs.insert(mac);
    }
    uint32_t mng_id = msg.community_id();
    uint32_t ag_id = msg.ag_id();
    std::string node = msg.node();
    int change_type = msg.change_type();
    int already_check = msg.already_check();
    uint64_t traceid = msg.trace_id();
        
    AccessGroupFileHandle(change_type, mng_id, node, macs, ag_id, traceid);
}
//阿塞拜疆 用户权限组更改通知
void CFileUpdateContorl::OnAzerAccountAccessModify(void* msg_buf, unsigned int msg_len)
{
    AK::Crontab::CronUserAccessGroupNotifyMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));

    std::string account = msg.account();
    uint32_t type = msg.type(); //type=0用户加入权限组 type=1 用户移出权限组
    uint32_t mng_id = msg.community_id();
    uint32_t ag_id = msg.ag_id();
    uint32_t change_type = 0;
    if(type == AzAccountAccessUpdateType::TYPE_ADD)
    {
        change_type = WEB_COMM_ADD_ACCOUNT_ACCESS;
    } else if(type == AzAccountAccessUpdateType::TYPE_DEL)
    {
        change_type = WEB_COMM_DEL_ACCOUNT_ACCESS;
    }

     AK_LOG_INFO << "Azerbaijan modify user access group, update user account=" << account
                                << "community_id=" << mng_id << "change_type=" << change_type;

    //更新数据版本
    dbinterface::ProjectUserManage::UpdateAccountDataVersionByUid(account);
    AK::Adapt::WebCommAccessModifyNotify new_msg;
    new_msg.set_community_id(mng_id);
    new_msg.set_ag_id(ag_id);
    new_msg.set_change_type(change_type);
    new_msg.set_node(account);
    new_msg.set_already_check(0);
    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    new_msg.set_trace_id(traceid);

    CAkcsWebPdu web_pdu;
    web_pdu.SetMsgBody(&new_msg);
    web_pdu.SetMsgID(MSG_S2C_DATAANALYSIS_COMM_ACCESS_FILE_UPDATE);
    web_pdu.SetProjectType(project::RESIDENCE);    
    
    GetFileUpdateContorlInstance()->OnCommunityAccessGroupFileUpdate(web_pdu.GetBuffer(), web_pdu.GetLength());
}

bool CFileUpdateContorl::IsFilterIpChange(uint32_t mng_id, int changetype)
{
    if (changetype == CSMAIN_COMM_DEV_IP_CHANGE 
        || changetype == CSMAIN_COMM_UNIT_DEV_IP_CHANGE 
        || changetype == CSMAIN_COMM_PUB_DEV_IP_CHANGE )
    {
        if(gstCSCONFIGConf.server_type == ServerArea::ccloud &&  CheckStrInFilter(gstCSCONFIGConf.ip_change_mng_id_filter, to_string(mng_id)))
        {
            return true;
        }
    } 
    return false;
}


//配置文件更新
void CFileUpdateContorl::ConfigFileHandle(int changetype, uint32_t mng_id, uint32_t unit_id, 
   const std::string &node, std::vector<std::string> &macs, uint64_t traceid) noexcept
{
    std::string mac_0;
    if (macs.size() > 0)
    {
        mac_0 = macs[0];
    }
    std::string macs_string;
    for (auto &mac : macs)
    {
        macs_string += mac;
        macs_string += ";";
    }
    if(0 == traceid)
    {
        traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    }
    AK_LOG_INFO << "ConfigFileHandle change type= " << changetype << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(changetype)<< " node= " << node
                << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << macs_string 
                << " traceid=" << traceid;

    if (SpecialTubeHandle::GetInstance().CheckIsFilter(mng_id, changetype))
    {
        AK_LOG_INFO << "ConfigFileHandle return " << mng_id;    
        return;
    }

    if (IsFilterIpChange(mng_id, changetype))
    {
        AK_LOG_INFO << "ConfigFileHandle Ipchange return " << mng_id;    
        return;
    }

    if (changetype == WEB_COMM_DELETE_COMMUNITY)
    {
        AK_LOG_INFO << "Request delete community mngid:" << mng_id;        
        CAKCSView::GetInstance()->NotifyDelCommunityPics(mng_id);
        CFaceMng::GetInstance().DelFaceMngByMngId(mng_id);

        std::string community_dir = "/var/www/download/community/";
        if (mng_id > 0)
        {
            std::string community_dir = "/var/www/download/community/";
            community_dir += std::to_string(mng_id);
            DeleteDir(community_dir);
        }
        GetAKCSViewInstance()->NotifyCommunityChange(mng_id);
        return;
    }
    
    std::time_t tstart = std::time(0);
    CommConfigHandle handle(mng_id, unit_id, node, macs);
    if (!handle.InitSuccess())
    {
        AK_LOG_INFO << "CommConfigHandle init error, project type is correct." << mng_id;    
        return;
    }
    switch(changetype)
    {
        //csmain
        case CSMAIN_COMM_DEV_IP_CHANGE:
        {
            handle.UpdateNodeDevConfig();
            handle.UpdateNodeContactEvent();
            handle.UpdateNodeDevRf();
            handle.UpdateNodeDevPrivatekey();
            //刚添加设备时候需要更新
            handle.UpdateMacUser(mac_0);
            handle.UpdateDevSchedule(mac_0);
            
            break;
        }
        case CSMAIN_COMM_DEV_UPGRADE://升级时候全部更新
        case CSMAIN_COMM_DEV_MAINTANCE:
        {
            handle.UpdateNodeDevConfig();
            handle.UpdateNodeDevContactList();
            handle.UpdateNodeDevRf();
            handle.UpdateNodeDevPrivatekey();
            
            handle.UpdateMacUser(mac_0);
            handle.UpdateDevSchedule(mac_0);
           
            break;
        }
        case CSMAIN_COMM_UNIT_DEV_IP_CHANGE://刚添加设备时候走ip变化，不走升级流程
        {            
            handle.UpdateUnitDevConfig();
            handle.UpdateNodeDevContactListByUnitMac(mac_0);
            handle.UpdatePubDevContactList();
            handle.UpdateUnitDevRf();
            handle.UpdateUnitDevPrivatekey();
            handle.UpdateUnitDevContactList();//刷新管理机的联系人

            handle.UpdateMacUser(mac_0);
            handle.UpdateDevSchedule(mac_0);
           
            break;
        }          
        case CSMAIN_COMM_UNIT_DEV_UPGRADE:
        case CSMAIN_COMM_UNIT_DEV_MAINTANCE:           
        {
            handle.UpdateUnitDevConfig();
            handle.UpdateUnitDevRf();
            handle.UpdateUnitDevPrivatekey();
            handle.UpdateUnitDevContactList();
            
            handle.UpdateMacUser(mac_0);
            handle.UpdateDevSchedule(mac_0);              
                    
            break;
        }
        
        case CSMAIN_COMM_PUB_DEV_IP_CHANGE:
        {
            handle.UpdatePubDevConfig();
            handle.UpdatePubDevContactList();
            handle.UpdateCommunityAllNodeDevConfig();
            handle.UpdateCommunityAllUnitDevConfig();
            handle.UpdateCommunityAllNodeDevContactList();
            handle.UpdateCommunityAllUnitDevContactList();
            handle.UpdatePubDevRf();
            handle.UpdatePubDevPrivatekey();

            handle.UpdateMacUser(mac_0);
            handle.UpdateDevSchedule(mac_0);
            
            break;
        }
        case CSMAIN_COMM_PUB_DEV_UPGRADE:
        case CSMAIN_COMM_PUB_DEV_MAINTANCE:
        {
            handle.UpdatePubDevConfig();
            handle.UpdatePubDevContactList();
            handle.UpdatePubDevRf();//TODO:这时候只需要更新自己
            handle.UpdatePubDevPrivatekey();//TODO:这时候只需要更新自己

            handle.UpdateMacUser(mac_0);
            handle.UpdateDevSchedule(mac_0);  
            
            break;
        }
        case CSMAIN_COMM_ACCOUNT_NFC_UPDATE:
        {
            handle.UpdateNodeRfEvent();
            handle.UpdateNodeUser();

            break;
        }
        //个人
        case WEB_COMM_NODE_UPDATE:/*(只会更新config/contact不会更新卡)IP直播切换、motion开关切换、群组呼叫和顺序呼叫更新*/
        {
            handle.UpdateNodeConfigEvent();
            handle.UpdateNodeContactEvent();

            break;
        }
        //卡要更新，因为nfc/ble是和用户绑定的
        case WEB_COMM_ADD_USER:
        case WEB_COMM_ADD_SLAVE_USER:
        {
            handle.UpdateNodeContactEvent();
            //新社区更新User
            handle.UpdateNodeUser();
            handle.UpdateNodeDevSchedule();
            break;
        }
        case WEB_COMM_DEL_USER:
        case WEB_COMM_MODIFY_USER:
        case WEB_COMM_DEL_SLAVE_USER:
        case WEB_COMM_MODIFY_SLAVE_USER:
        {
            handle.UpdateNodeDevConfig();//pubutton/robin call
            handle.UpdateNodeContactEvent(); 
            handle.UpdateNodeRfEvent();//NFC/BLE
            handle.UpdateNodePrivatekeyEvent();//名称

            //modify by chenzhx V6.0 人脸信息用户信息要更新
            //if (changetype == WEB_COMM_DEL_SLAVE_USER || changetype == WEB_COMM_DEL_USER) 
            //{
    			handle.UpdateCommunityPubFace();
    			handle.UpdateCommunityOneUnitFace();
    			handle.UpdateCommunityOneNodeFace();
            //}

            //新社区更新User
            if (changetype == WEB_COMM_DEL_SLAVE_USER || changetype == WEB_COMM_DEL_USER)
            {
                //删除用户时根据node获取家庭设备列表，刷新user
                //用户关联的权限组设备由权限组接口处理
                handle.UpdateNodeUserWhenDelete();
            }
            else
            {
                handle.UpdateNodeUser();
            }
            handle.UpdateNodeDevSchedule();
            
            if (changetype == WEB_COMM_DEL_USER || changetype == WEB_COMM_DEL_SLAVE_USER) 
            {
                CSP2A_REFRESH_CACHE refresh_cache = {0};
                refresh_cache.type = REFRESH_CACHE_TYPE::DELETE_USER;
                snprintf(refresh_cache.node, sizeof(refresh_cache.node), "%s", node.c_str());   
                if (GetIPCControlInstance()->NotifyRefreshConnCache(refresh_cache) != 0)
                {
                    AK_LOG_WARN << "NotifyRefreshConnCache failed";
                }
            }

            break;
        }
        case WEB_COMM_ADD_DEV:
        case WEB_COMM_DEL_DEV:
        case WEB_COMM_MODIFY_DEV:
        case WEB_COMM_DEL_THIRD_CAMERA:
        {
            handle.UpdateNodeConfigEvent();
            handle.UpdateNodeContactEvent();
            handle.UpdateNodeRfEvent();
            handle.UpdateNodePrivatekeyEvent();
			if (changetype == WEB_COMM_ADD_DEV || WEB_COMM_MODIFY_DEV)
            {
    			handle.UpdateCommunityOneNodeFace();
            }             
            //修改了Location之类的，要刷新csmain 设备conn 缓存
            if(changetype == WEB_COMM_MODIFY_DEV)
            {
                CSP2A_REFRESH_CACHE refresh_cache = {0};
                refresh_cache.type = REFRESH_CACHE_TYPE::MODIFY_DEV;
                snprintf(refresh_cache.mac, sizeof(refresh_cache.mac), "%s", mac_0.c_str());   
                if (GetIPCControlInstance()->NotifyRefreshConnCache(refresh_cache) != 0)
                {
                    AK_LOG_WARN << "NotifyRefreshConnCache failed";
                }
            }
            
            for (auto &mac : macs)
            {
               handle.UpdateMacUser(mac);
               handle.UpdateDevSchedule(mac);
            }
            break;
        }    
        case WEB_COMM_UPDATE_RF:
        {
            handle.UpdateNodeRfEvent();
            break;
        }
        case WEB_COMM_UPDATE_PIN:
        {
            handle.UpdateNodePrivatekeyEvent();
            break;
        }
        //单元
        case WEB_COMM_UNIT_ADD_DEV:
        case WEB_COMM_UNIT_DEL_DEV:
        case WEB_COMM_UNIT_MODIFY_DEV:
        case WEB_COMM_UNIT_DEL_THIRD_CAMERA:
        {
            handle.UpdateUnitDevConfig();
            handle.UpdateUnitDevContactList();
            handle.UpdateUnitDevRf();
            handle.UpdateUnitDevPrivatekey();
            handle.UpdatePubDevContactList();

            handle.UpdateNodeDevContactListByUnitMac(mac_0);
            if (mac_0.empty()) 
            {
                //楼栋下的未link的三方摄像头数据变化时，需要去刷下楼栋下的所有node
                handle.UpdateUnitAllNodeDevContactList();
            }
            if (changetype == WEB_COMM_UNIT_ADD_DEV || changetype == WEB_COMM_UNIT_MODIFY_DEV)
            {
                handle.UpdateCommunityOneUnitFace();
            }    
            //修改了Location之类的，要刷新csmain 设备conn 缓存
            if(changetype == WEB_COMM_UNIT_MODIFY_DEV)
            {
                CSP2A_REFRESH_CACHE refresh_cache = {0};
                refresh_cache.type = REFRESH_CACHE_TYPE::MODIFY_DEV;
                snprintf(refresh_cache.mac, sizeof(refresh_cache.mac), "%s", mac_0.c_str());   
                if (GetIPCControlInstance()->NotifyRefreshConnCache(refresh_cache) != 0)
                {
                    AK_LOG_WARN << "NotifyRefreshConnCache failed";
                }
            }
            
            for (auto &mac : macs)
            {
               handle.UpdateMacUser(mac);
               handle.UpdateDevSchedule(mac);
            }
            break;
        }       
        case WEB_COMM_UNIT_UPDATE_RF:
        {
            handle.UpdateUnitDevRf();
            break;
        }
        case WEB_COMM_UNIT_UPDATE_PIN:
        {
            handle.UpdateUnitDevPrivatekey();
            break;
        }
        //public
        case WEB_COMM_PUB_ADD_DEV:
        case WEB_COMM_PUB_DEL_DEV:
        case WEB_COMM_PUB_MODIFY_DEV:
        case WEB_COMM_PUB_DEL_THIRD_CAMERA:
        {
            handle.UpdatePubDevConfig();
            handle.UpdatePubDevRf();
            handle.UpdatePubDevPrivatekey();
            handle.UpdatePubDevContactList();

            handle.UpdateCommunityAllNodeDevContactList();
            handle.UpdateCommunityAllUnitDevContactList();
            handle.UpdateCommunityAllUnitDevConfig();   //更新管理机按键
			if (changetype == WEB_COMM_PUB_ADD_DEV || changetype == WEB_COMM_PUB_MODIFY_DEV)
            {
    			handle.UpdateCommunityPubFace();//TODO:所有的添加设备其实只需要更新自己的face/rf/private
            }   
            //修改了Location之类的，要刷新csmain 设备conn 缓存
            if(changetype == WEB_COMM_PUB_MODIFY_DEV)
            {
                CSP2A_REFRESH_CACHE refresh_cache = {0};
                refresh_cache.type = REFRESH_CACHE_TYPE::MODIFY_DEV;
                snprintf(refresh_cache.mac, sizeof(refresh_cache.mac), "%s", mac_0.c_str());   
                if (GetIPCControlInstance()->NotifyRefreshConnCache(refresh_cache) != 0)
                {
                    AK_LOG_WARN << "NotifyRefreshConnCache failed";
                }
            }

            for (auto &mac : macs)
            {
               handle.UpdateMacUser(mac);
               handle.UpdateDevSchedule(mac);
            }
            break;
        }       
        case WEB_COMM_PUB_UPDATE_RF:
        {
            handle.UpdatePubDevRf();
            break;
        }
        case WEB_COMM_PUB_UPDATE_PIN:
        {
            handle.UpdatePubDevPrivatekey();
            break;
        }

        //community info
        case WEB_COMM_INFO://社区信息
        {
            handle.UpdatePubDevConfig();
            handle.UpdateCommunityAllUnitDevConfig();
            handle.UpdateCommunityAllNodeDevConfig();
            break;
        }
        case WEB_COMM_APT_PIN:
        {
            handle.UpdatePubDevConfig();
            handle.UpdateCommunityAllUnitDevConfig();
            handle.UpdateCommunityAllNodeDevConfig();
            handle.UpdatePubDevPrivatekey();
            handle.UpdateCommunityAllUnitDevPrivatekey();
            handle.UpdateCommunityAllNodeDevPrivatekey();
            
            handle.UpdateCommunityAllDevUser();
            break;
        }
        case WEB_COMM_MOTION:
        {
            handle.UpdatePubDevConfig();
            handle.UpdateCommunityAllUnitDevConfig();
            break;
        }   
        case WEB_COMM_IMPORT_COMMUNITY:
        {
            handle.UpdatePubDevContactList();
            handle.UpdateCommunityAllNodeDevContactList();
            handle.UpdateCommunityAllUnitDevContactList();

            handle.UpdatePubDevPrivatekey();
            handle.UpdateCommunityAllUnitDevPrivatekey();
            handle.UpdateCommunityAllNodeDevPrivatekey();

            handle.UpdatePubDevConfig();
            handle.UpdateCommunityAllUnitDevConfig();
            handle.UpdateCommunityAllNodeDevConfig();
            
            handle.UpdatePubDevRf();
            handle.UpdateCommunityAllUnitDevRf();
            handle.UpdateCommunityAllNodeDevRf();

            handle.UpdateCommunityAllDevUser();
            handle.UpdateCommunityAllDevSchedule();

			handle.UpdateCommunityPubFace();
			handle.UpdateCommunityAllUnitFace();
			handle.UpdateCommunityAllNodeFace();
            
            break;
        }   
        case WEB_COMM_IMPORT_FACE_PIC:
        case WEB_COMM_DELETE_ALL_FACE_PIC://只对6.1版本之前的社区生效
        {
			handle.UpdateCommunityPubFace();
			handle.UpdateCommunityAllUnitFace();
			handle.UpdateCommunityAllNodeFace();
            break;
        }
        case WEB_COMM_UPLOAD_FACE_PIC:
        case WEB_COMM_DELETE_FACE_PIC:
		{
			handle.UpdateCommunityPubFace();
			handle.UpdateCommunityOneUnitFace();
			handle.UpdateCommunityOneNodeFace();
			break;
		}
        case WEB_COMM_ADD_BUILDING:
        {
            break;
        }
        case WEB_COMM_DEL_BUILDING:
        {
            break;
        }
        case WEB_COMM_MODIFY_BUILDING:
        {
            handle.UpdateUnitDevConfig();
            handle.UpdateUnitDevContactList();
            handle.UpdatePubDevContactList();
            handle.UpdateUnitAllNodeDevContactList();
            handle.UpdateUnitDevSchedule();
            break;
        }
        case WEB_COMM_MODIFY_TIMEINFO:
        {
            handle.UpdatePubDevConfig();
            handle.UpdateCommunityAllUnitDevConfig();
            handle.UpdateCommunityAllNodeDevConfig();
            break;
        }  
        case WEB_COMM_UPDATE_MAC_CONFIG:
        {
            handle.UpdateMacDevConfig(mac_0);
            break;
        }
        case WEB_COMM_NOTIFY_FLOW_OUT_OF_LIMIT:
        {
            handle.UpdatePubDevConfig();
            handle.UpdateCommunityAllUnitDevConfig();
            break;
        }
        case WEB_COMM_DELETE_ALL_RF_CARD://只对6.1版本之前的社区生效
        {
            handle.UpdatePubDevRf();
            handle.UpdateCommunityAllUnitDevRf();
            handle.UpdateCommunityAllNodeDevRf();
            break;
        }
        case WEB_COMM_ALLOW_CREATE_PIN:
        {
            handle.UpdateCommunityAllDevUser();

            handle.UpdatePubDevPrivatekey();
            handle.UpdateCommunityAllUnitDevPrivatekey();
            handle.UpdateCommunityAllNodeDevPrivatekey();
          
            break;
        }
        case WEB_COMM_FEATURE_PLAN_RENEW:
        {
            //目前高级功能只有涉及到快递间的
            //还有app pin
            dbinterface::ProjectUserManage::UpdateCommunityAllAccountDataVersion(mng_id);
            handle.UpdatePubDevConfig();
            handle.UpdateCommunityAllUnitDevConfig();
            handle.UpdateCommunityAllNodeDevConfig();

            handle.UpdateCommunityAllDevUser();   
            break;
        }
        case WEB_COMM_MODIFY_PM_APP_ACCOUNT:
        {
            //只有新小区有PM APP
            handle.UpdatePubDevConfig();
            handle.UpdateCommunityAllUnitDevConfig();

            handle.UpdatePubDevContactList();
            handle.UpdateCommunityAllUnitDevContactList();

            handle.UpdateCommunityAllDevUser();
            handle.UpdateCommunityAllDevSchedule();
            break;
        }
        case WEB_COMM_DEL_PM_APP_ACCOUNT:
        case WEB_COMM_ADD_PM_APP_ACCOUNT:
        case WEB_COMM_MODIFY_PM_APP_STATUS:
        {
            //只有新小区有PM APP
            handle.UpdatePubDevConfig();
            handle.UpdateCommunityAllUnitDevConfig();

            handle.UpdatePubDevContactList();
            handle.UpdateCommunityAllUnitDevContactList();
            //室内机群呼pm app,pm app开关状态发生变化,需要刷社区内所有室内机
            handle.UpdateCommunityAllNodeDevContactList();

            handle.UpdateCommunityAllDevUser();
            handle.UpdateCommunityAllDevSchedule();
            
            break;
        }
        case WEB_COMM_UPDATE_COMMUNITY_CALLS:
        {
            std::string node_uuid;
            dbinterface::ResidentPersonalAccount::GetUUIDByAccount(node, node_uuid);
            std::set<std::string> uuids;
            //查询修改的用户 在户户通中 被哪几个用户关联
            dbinterface::ContactFavorite::GetPerUUIDListByFavorite(node_uuid, uuids);
            dbinterface::ContactBlock::GetPerUUIDListByBlack(node_uuid, uuids);
            for (auto& uuid : uuids)
            {          
                ResidentPerAccount account_info;
                memset(&account_info, 0, sizeof(account_info));
                if (0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(uuid, account_info))
                {
                   //macs实际为空
                    CommConfigHandle handle_tmp(mng_id, account_info.unit_id, account_info.account, macs);
                    if (handle_tmp.InitSuccess())
                    {
                        handle_tmp.UpdateNodeContactEvent();
                    }                   
                    
                }
            }
            break;
        }
        case WEB_COMM_UPDATE_COMMUNITY_PUB_UNIT_CONTACT:
        {
            //在OnAccessGroupModify通知了设备
            handle.UpdateUnitDevContactList();
            handle.UpdatePubDevContactList();
            break;
        }
        case WEB_COMM_MODIFY_PRIVATE_ACCESS:
        {
            handle.UpdateCommunityAllDevUser();
            break;
        }
        case WEB_COMM_UPDATE_NODE_USER:
        {
            handle.UpdateNodeUser();
            break;
        }
        case WEB_COMM_UPDATE_LANDLINE_STATUS:
        {
            handle.UpdatePubDevContactList();
            handle.UpdateCommunityAllUnitDevContactList();
            handle.UpdateCommunityAllNodeDevContactList();
          
            handle.UpdatePubDevConfig();
            handle.UpdateCommunityAllUnitDevConfig();
            handle.UpdateCommunityAllNodeDevConfig();
            break;
        }
        case WEB_COMM_MODIFY_BUILDING_NAME:
        {
            //刷unit+pub设备的contact和schedule
            handle.UpdateUnitDevSchedule();
            handle.UpdatePubDevSchedule();
            handle.UpdatePubDevContactList();
            handle.UpdateUnitDevContactList();
            //刷unit设备的config,首页展示building字段
            handle.UpdateUnitDevConfig();
            break;
        }
        case WEB_COMM_MODIFY_NODE_MOTION_CONFIG:
        {
            handle.UpdateNodeConfigEvent();
            break;
        }
        case WEB_COMM_MODIFY_FEATURE_PLAN:
        {
            handle.UpdatePubDevConfig();
            handle.UpdateCommunityAllUnitDevConfig();
            break;
        }
        case WEB_COMM_UPDATE_COMMUNITY_ALL:
        {
            handle.UpdatePubDevContactList();
            handle.UpdateCommunityAllNodeDevContactList();
            handle.UpdateCommunityAllUnitDevContactList();

            handle.UpdatePubDevPrivatekey();
            handle.UpdateCommunityAllUnitDevPrivatekey();
            handle.UpdateCommunityAllNodeDevPrivatekey();

            handle.UpdatePubDevConfig();
            handle.UpdateCommunityAllUnitDevConfig();
            handle.UpdateCommunityAllNodeDevConfig();
            
            handle.UpdatePubDevRf();
            handle.UpdateCommunityAllUnitDevRf();
            handle.UpdateCommunityAllNodeDevRf();

            handle.UpdateCommunityAllDevUser();
            handle.UpdateCommunityAllDevSchedule();

            handle.UpdateCommunityPubFace();
            handle.UpdateCommunityAllUnitFace();
            handle.UpdateCommunityAllNodeFace();
            
            break;
        }
        case WEB_COMM_MODIFY_CONTACT_DISPLAY_ORDER:
        {
            handle.UpdateCommunityContactEvent();
            break;
        }
        case WEB_COMM_UPDATE_PUB_MAC_CONTACT:
        {
            handle.UpdateCommunityUpdatePubMacContact(mac_0);
            break;
        }
        case WEB_COMM_UPDATE_APT_CALLRULE:
        {
            handle.UpdateNodeContactEvent();
            break;
        }
        case WEB_COMM_MODIFY_MAC_CONTACT:            
        {
            handle.UpdateMacDevContact(mac_0);
            break;
        }
        case WEB_COMM_UPDATE_CONFIG_AND_CONTACT:
        {
            handle.UpdatePubDevConfig();
            handle.UpdateCommunityAllUnitDevConfig();
            handle.UpdateCommunityAllNodeDevConfig();
            
            handle.UpdatePubDevContactList();
            handle.UpdateCommunityAllUnitDevContactList();
            handle.UpdateCommunityAllNodeDevContactList();
            break;
        }
        case WEB_COMM_MODIFY_BUILDING_FLOOR_SETTING:
        {
            handle.UpdateUnitDevConfig();
            break;
        }
        case WEB_COMM_MODIFY_NODE_DEV_DETECTION:
        {
            handle.UpdateNode(node);
            handle.UpdateNodeDevConfig();
            break;
        }
        case WEB_COMM_MODIFY_NODE_CONFIG:
        {
            handle.UpdateNodeDevConfig();
            break;
        }
        default:
        {
            AK_LOG_WARN << "not define this change type= " << changetype << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(changetype);            
        }
    }

    std::time_t tend = std::time(0);
    CommonHandle::CheckBigProject(tstart, tend, mng_id);
    AK_LOG_INFO << "communitid [" << mng_id << "] update file time: " << tend - tstart << "s traceid=" << traceid << " change type= " << changetype << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(changetype);
}

//权限组文件更新配置
void CFileUpdateContorl::AccessGroupFileHandle(int changetype, uint32_t mng_id, const std::string &node, std::set<std::string> &macs, uint32_t ag_id, uint64_t traceid)
{
    if(0 == traceid)
    {
        traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    }
    AK_LOG_INFO << "OnAccessGroupHandle change type= " << changetype << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(changetype) << " node= " << node
                << " community_id= " << mng_id << " ag_id= " << ag_id << " traceid=" << traceid;

                
    if (SpecialTubeHandle::GetInstance().CheckIsFilter(mng_id, changetype))
    {
        AK_LOG_INFO << "OnAccessGroupHandle return " << mng_id;    
        return;
    }
    
    CommunityInfoPtr comm_info = std::make_shared<CommunityInfo>(mng_id);
    if (!comm_info->GetIsNew())
    {
        AK_LOG_INFO << "communitid [" << mng_id << "] not new commynity, not required update user file";
        return;
    }
    DevUser user(comm_info);
    std::time_t tstart = std::time(0);
    switch (changetype)
    {
        case WEB_COMM_ADD_ACCOUNT_ACCESS:
        case WEB_COMM_MODIFY_ACCOUNT_ACCESS:
        {
            std::vector<std::string> accounts;
            std::set<std::string> pub_mac_set;
            std::set<std::string> user_mac_set;
            accounts.push_back(node);
            //更新用户关联的权限组的设备user
            user.UpdatePubDevMetaByAccount(accounts, pub_mac_set);

            //更新Contact
            ResidentPerAccountList account_list;
            ResidentPerAccount node_account;
            memset(&node_account, 0, sizeof(node_account));
            if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(node, node_account))
            {
                account_list.push_back(node_account);
            }
            UpdateCommunityAccessGroupContactListByAccount(mng_id, account_list);

            //更新scedule
            DEVICE_SETTING* devlist = GetDeviceSettingInstance()->GetDeviceSettingByMacList(pub_mac_set);
            DevSchedule schedule(comm_info);
            schedule.UpdateScheduleData(devlist);
            GetDeviceControlInstance()->DestoryDeviceSettingList(devlist);
            break;
        }
        case WEB_COMM_ADD_USER_ACCESSGROUP:
        case WEB_COMM_MODIFY_USER_ACCESSGROUP:
        case WEB_COMM_MODIFY_USER_ACCESSGROUP_DEVICE:
        {
            //个人权限组设备添加删除已在Devices表处理
            std::vector<std::string> accounts;
            std::set<std::string> user_mac_set;
            accounts.push_back(node);
            //更新用户关联的家庭设备user
            user.UpdateUserDevMetaByNodes(accounts, user_mac_set);

            //更新Contact
            ResidentPerAccount node_account;
            memset(&node_account, 0, sizeof(node_account));
            if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(node, node_account))
            {
                std::vector<std::string> mac_param;
                CommConfigHandle handle(mng_id, node_account.unit_id, node_account.account, mac_param);
                if (handle.InitSuccess())
                {
                    handle.UpdateNodeDevContactList();
                }                
                
            }
            
            //更新scedule
            DEVICE_SETTING* devlist = GetDeviceSettingInstance()->GetDeviceSettingByMacList(user_mac_set);
            DevSchedule schedule(comm_info);
            schedule.UpdateScheduleData(devlist);
            GetDeviceControlInstance()->DestoryDeviceSettingList(devlist);
            break;
        }
        //根据用户刷新权限组及个人设备，当前只有新社区和办公修改pin调用
        case WEB_COMM_MODIFY_USER_ALL_ACCESS:
        {
            std::vector<std::string> accounts;
            std::set<std::string> pub_mac_set;
            std::set<std::string> user_mac_set;
            accounts.push_back(node);
            //更新用户关联的权限组的设备user
            user.UpdatePubDevMetaByAccount(accounts, pub_mac_set);
             //更新用户关联的家庭设备user
            user.UpdateUserDevMetaByAccount(accounts, user_mac_set);
            //更新用户关联权限组设备的scedule
            DEVICE_SETTING* pub_devlist = GetDeviceSettingInstance()->GetDeviceSettingByMacList(pub_mac_set);
            DevSchedule schedule(comm_info);
            schedule.UpdateScheduleData(pub_devlist);
            GetDeviceControlInstance()->DestoryDeviceSettingList(pub_devlist);

            //更新用户关联个人设备的scedule
            DEVICE_SETTING* node_devlist = GetDeviceSettingInstance()->GetDeviceSettingByMacList(user_mac_set);
            schedule.UpdateScheduleData(node_devlist);
            GetDeviceControlInstance()->DestoryDeviceSettingList(node_devlist);
            break;
        }
        case WEB_COMM_DEL_ACCOUNT_ACCESS:
        {
            //删除用户时/删除权限组时/用户移除权限组时删除AccountAccess表
            std::set<std::string> pub_mac_set;
            std::vector<uint32_t> ag_ids;
            ag_ids.push_back(ag_id);
            
            //更新用户关联的权限组的设备user
            user.UpdatePubDevMetaByAccessGroupID(ag_ids, pub_mac_set);

            //更新Contact
            ResidentPerAccount node_account;
            memset(&node_account, 0, sizeof(node_account));
            dbinterface::ResidentPersonalAccount::GetUidAccount(node, node_account);

            //更新scedule

            DEVICE_SETTING* devlist = GetDeviceSettingInstance()->GetDeviceSettingByMacList(pub_mac_set);
            DevSchedule schedule(comm_info);
            schedule.UpdateScheduleData(devlist);
            
            UpdateCommunityAccessGroupContactListByAccountAndDevice(mng_id, node_account, devlist);
            
            GetDeviceControlInstance()->DestoryDeviceSettingList(devlist);
            break;
        }
        case WEB_COMM_MODIFY_ACCESS_GROUP:
        {
            std::set<std::string> mac_set;
            if (0 == macs.size())
            {
                //AccessGroup表数据分析，根据权限组ID查mac更新配置
                dbinterface::AccessGroup::GetMacListByAccessGroupID(ag_id, mac_set);
            }
            else
            {
                //AccessGroupDevice表数据分析，根据web数据收集给出的mac更新配置
                mac_set.insert(macs.begin(),macs.end());
            }
            
            //更新schedule
            DEVICE_SETTING* dev_list = GetDeviceSettingInstance()->GetDeviceSettingByMacList(mac_set);    
            DevSchedule schedule(comm_info);
            schedule.UpdateScheduleData(dev_list);

            //更新user
            user.UpdateMetaData(dev_list);

            //更新Contact
            UpdateCommunityAccessGroupContactListByAgid(ag_id, mng_id);
            GetDeviceControlInstance()->DestoryDeviceSettingList(dev_list);
            
            break;
        }
        case WEB_COMM_MODIFY_STAFF:
        case WEB_COMM_MODIFY_DELIVERY:
        {
            std::set<std::string> pub_mac_set;
            std::vector<uint32_t> ag_ids;

            if (ag_id > 0)
            {
                ag_ids.push_back(ag_id);
                user.UpdatePubDevMetaByAccessGroupID(ag_ids, pub_mac_set);
            }
            break;
        }
        case WEB_COMM_MODIFY_HOLD_DOOR:
        {
            std::set<std::string> mac_set;
            mac_set.insert(macs.begin(),macs.end());
            //更新schedule
            DEVICE_SETTING* dev_list = GetDeviceSettingInstance()->GetDeviceSettingByMacList(mac_set);    
            DevSchedule schedule(comm_info);
            schedule.UpdateScheduleData(dev_list);
            GetDeviceControlInstance()->DestoryDeviceSettingList(dev_list);
            break;
        }
        //只刷新设备用户信息
        case WEB_COMM_MODIFY_USER_META:
        {
            std::vector<std::string> accounts;
            std::set<std::string> pub_mac_set;
            std::set<std::string> user_mac_set;
            accounts.push_back(node);
            //更新用户关联的权限组的设备user
            user.UpdatePubDevMetaByAccount(accounts, pub_mac_set);
            //更新用户关联的家庭设备user
            user.UpdateUserDevMetaByAccount(accounts, user_mac_set);
            break;
        }
        default:
        {
            AK_LOG_WARN << "[OnAccessGroupHandle] not define this change type= " << changetype << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(changetype);            
        }
    }

    std::time_t tend = std::time(0);
    CommonHandle::CheckBigProject(tstart, tend, mng_id);
    AK_LOG_INFO << "communitid [" << mng_id << "] update file time: " << tend - tstart << "s traceid=" << traceid << " change type= " << changetype << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(changetype);
}


/*处理和写配置无关的信息*/
void CFileUpdateContorl::OnDevUpdateCommonHandle(int changetype, std::vector<std::string> &macs)
{
    if (macs.size() <= 0)
    {
        //AK_LOG_WARN << "OnDevUpdateCommonHandle: mac is null! changetype=" << changetype;
        return;
    }
    std::string mac = macs[0];

    switch (changetype)
    {
        case WEB_PER_ADD_DEV:
        case WEB_COMM_ADD_DEV:
        case WEB_COMM_UNIT_ADD_DEV:
        case WEB_COMM_PUB_ADD_DEV:   
        {
           AK_LOG_INFO << "Request add dev mac:" << mac;
           GetIPCControlInstance()->SendPersonalReportStatus(mac);
           CSP2A_DEV_CLEAN_DEVICE_CODE clean_dev_code;
           ::snprintf(clean_dev_code.szMacs, sizeof(clean_dev_code.szMacs), "%s", mac.c_str());
           if (GetIPCControlInstance()->SendDevCleanDeviceCode(&clean_dev_code) != 0)
           {
               AK_LOG_WARN << "SendDevCleanDeviceCode failed";
               return;
           }
           break;
        }
        case WEB_PER_DEL_DEV:
        case WEB_COMM_DEL_DEV:
        case WEB_COMM_UNIT_DEL_DEV:
        case WEB_COMM_PUB_DEL_DEV: 
        {

            AK_LOG_INFO << "Request delete dev mac:" << mac;
            //通知csmain去下发注销sip的指令
            if (GetIPCControlInstance()->SendPerDevLogOutSip(mac) != 0)
            {
                AK_LOG_WARN << "Send personal dev logout sip msg failed";
            }

            //删除掉设备的配置文件
            //删除这个设备的截图和motion
            // CAKCSView::GetInstance()->NotifyPerDelDevPics(mac);

            //删除mac对应的配置文件
            if (mac.length() > 8)
            {
                AKCS::Singleton<CShadowMng>::instance().DeleteDevShadow(mac);
            }          
            break;
        }
        case WEB_PER_MODIFY_DEV:
        case WEB_COMM_MODIFY_DEV:
        case WEB_COMM_UNIT_MODIFY_DEV:
        case WEB_COMM_PUB_MODIFY_DEV: 
        {
            break;
        }
        case WEB_COMM_DELETE_COMMUNITY:
        {
            for(const auto& strmac : macs)
            {
                //通知csmain去下发注销sip的指令
                AK_LOG_INFO << "Request delete office, mac:" << mac;
                if (GetIPCControlInstance()->SendPerDevLogOutSip(strmac) != 0)
                {
                    AK_LOG_WARN << "Send personal dev logout sip msg failed";
                }

                //删除掉设备的配置文件
                //删除这个设备的截图和motion
                // CAKCSView::GetInstance()->NotifyPerDelDevPics(strmac);

                //删除mac对应的配置文件
                if (strmac.length() > 8)
                {
                    AKCS::Singleton<CShadowMng>::instance().DeleteDevShadow(mac);
                }
            }
            break;
        }
        case WEB_COMM_ADD_INDOOR_PLAN_DEV:
        {
            AK_LOG_INFO << "Request add indoor plan dev mac:" << mac;
            //通知设备上报状态
            GetIPCControlInstance()->SendPersonalReportStatus(mac);
            break;
        }
    }  
}

