#ifndef __FILE_UPDATE_CONTROL_H__
#define __FILE_UPDATE_CONTROL_H__

#include "AkcsWebMsgSt.h"

#include <string>
#include <set>

//前置声明
typedef struct DEVICE_SETTING_T DEVICE_SETTING;
typedef struct PRIVATE_KEY_T PRIVATE_KEY;
typedef PRIVATE_KEY RF_KEY;

class CFileUpdateContorl
{
public:
    CFileUpdateContorl();
    ~CFileUpdateContorl();

    static CFileUpdateContorl* GetInstance();
    
    //社区更新配置(config/key等)
    void OnCommunityConfigFileUpdate(void* msg_buf, unsigned int msg_len);
    //社区更新权限组配置
    void OnCommunityAccessGroupFileUpdate(void* msg_buf, unsigned int msg_len);
    //转换为web过来的统一格式
    void CommunityFileUpdateFormateWeb(int changetype, uint32_t mng_id, uint32_t unit_id, 
       const std::string &node, std::vector<std::string> &macs);  
    //处理和写配置无关的信息
    void OnDevUpdateCommonHandle(int changetype, std::vector<std::string> &macs);
    //阿塞拜疆社区更新用户权限组
    void OnAzerAccountAccessModify(void* msg_buf, unsigned int msg_len);
    
private:
    CFileUpdateContorl(const CFileUpdateContorl&);
    CFileUpdateContorl& operator = (const CFileUpdateContorl&);

    void AccessGroupFileHandle(int changetype, uint32_t mng_id, const std::string &node, std::set<std::string> &macs, uint32_t ag_id, uint64_t traceid);
    void ConfigFileHandle(int changetype, uint32_t mng_id, uint32_t unit_id, const std::string &node, std::vector<std::string> &macs, uint64_t traceid) noexcept;
    bool IsFilterIpChange(uint32_t mng_id, int changetype);
private:

    static CFileUpdateContorl* instance;
};

CFileUpdateContorl* GetFileUpdateContorlInstance();

#endif //__FILE_UPDATE_CONTROL_H__
