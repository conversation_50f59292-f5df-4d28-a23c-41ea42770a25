#include "AKCSView.h"
#include "AK.Server.pb.h"
#include "AkcsMsgDef.h"
#include "GroupMsgMng.h"
#include <ctime>
#include "ConfigDef.h"
#include "json/json.h"
#include "UnixSocketControl.h"
#include "AkcsMonitor.h"
#include "AkcsCommonDef.h"
#include "AkcsWebPduBase.h"
#include "util.h"
#include "CachePool.h"
#include "CommonHandle.h"

extern CSCONFIG_CONF gstCSCONFIGConf;
extern const char* g_redis_db_userdetail;
CGroupMsgMng* CGroupMsgMng::s_group_msg_mng_instance_ = nullptr;

CGroupMsgMng* CGroupMsgMng::Instance()
{
    if (!s_group_msg_mng_instance_)
    {
        s_group_msg_mng_instance_ = new CGroupMsgMng();
    }
    return s_group_msg_mng_instance_;
}

void CGroupMsgMng::HandleP2PDevConfigRewriteReq(void* msg_buf, unsigned int len)
{
    if (nullptr == msg_buf)
    {
        AK_LOG_WARN << "HandleP2PDevConfigRewriteReq The param is NULL";
        return;
    }
    AK::Server::P2PMainDevConfigRewriteMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));

    if(msg.type() == CSMAIN_UPDATE_CONFIG_IP_CHANGE  && IpChangeMacFilter(msg.mac()))
    {
        AK_LOG_INFO << "IpChangeMacFilter mac:"<<msg.mac();
        return;
    }
    if (CommonHandle::CheckIpchangeRequest(msg, project::RESIDENCE) != 0 )
    {
        return;
    }
    GetAKCSViewInstance()->UpdateMacConfigByCsmain(msg.type(), msg.mac().c_str(), msg.ip());
}


void CGroupMsgMng::HandleP2PDevConfigNodeRewriteReq(void* msg_buf, unsigned int len)
{
    if (nullptr == msg_buf)
    {
        AK_LOG_WARN << "HandleP2PDevConfigNodeRewriteReq The param is NULL";
        return;
    }
    AK::Server::P2PMainAccountConfigRewriteMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));

    GetAKCSViewInstance()->UpdateDevAccountConfigByCsmain(msg.type(),msg.node(),msg.account(),msg.account_role(),msg.manager_id(),msg.unit_id());
}

void CGroupMsgMng::HandleP2PDevReportVisitorReq(void* msg_buf, unsigned int len)
{
    if (nullptr == msg_buf)
    {
        AK_LOG_WARN << "HandleP2PDevConfigRewriteReq The param is NULL";
        return;
    }
    AK::Server::P2PMainDevReportVisitorMsg msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));

    char data[64];
    snprintf(data, sizeof(data), "sudo -u nobody php Visitor.php %d &", msg.id());
    chdir("/var/www/html/apache-v3.0/notify");
    if (system(data) < 0)
    {
        AK_LOG_WARN << "Run visitor php error";
    }
}

void CGroupMsgMng::HandleP2PDevWriteUserinfoReq(void* msg_buf, unsigned int len)
{
    AK::Server::P2PMainRequestWriteUserinfo msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE));

    if (CommonHandle::CheckUserInfoRequest(msg, project::RESIDENCE) != 0)
    {
        return;
    }
	
	if(UserInfoMacFilter(msg.mac()))
    {
        AK_LOG_INFO << "UserInfoMacFilter mac:"<<msg.mac();
        return;
    }

    CacheManager* cache_mng = CacheManager::getInstance();
    CacheConn* cache_conn = cache_mng->GetCacheConn(g_redis_db_userdetail);
    if (cache_conn)
    {
        //csmain插入，防止处理不过来，设备一直重复请求
        cache_conn->del(msg.accounts_key());
        cache_mng->RelCacheConn(cache_conn);
    }
    uint64_t msg_time = msg.timestamp();
    std::time_t t = std::time(0);
    if (t - msg_time > 60)
    {
        AK_LOG_INFO << "Devices request user handle time more than 60s.";
    }

    UserUUIDList list;
    SplitString(msg.uuids(), ";", list);
    AK_LOG_INFO << "Mac:" << msg.mac() << " request user info, user:" << msg.uuids() << " traceid:" << msg.msg_traceid();

    GetAKCSViewInstance()->WriteMacUserInfoByCsmain(msg.mac(), list, msg.msg_traceid());
    
}

bool CGroupMsgMng::IpChangeMacFilter(const std::string mac)
{
    if(strlen(gstCSCONFIGConf.ip_change_filter) == 0)
    {
        return false;
    }
    if(strstr(gstCSCONFIGConf.ip_change_filter, mac.c_str()) != nullptr)
    {
        return true;
    }
    return false;
}
bool CGroupMsgMng::UserInfoMacFilter(const std::string mac)
{
    if(strlen(gstCSCONFIGConf.user_info_filter) == 0)
    {
        return false;
    }
    if(strstr(gstCSCONFIGConf.user_info_filter, mac.c_str()) != nullptr)
    {
        return true;
    }
    return false;
}

