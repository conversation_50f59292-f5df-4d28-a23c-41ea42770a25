#ifndef __CSADAPT_GROUP_MSG_MNG_H__
#define __CSADAPT_GROUP_MSG_MNG_H__

#include <boost/noncopyable.hpp>
#include "AkcsPduBase.h"
#include <mutex>

class CGroupMsgMng : public boost::noncopyable
{
public:
    CGroupMsgMng() {}
    virtual ~CGroupMsgMng() {}
    static CGroupMsgMng* Instance();
    void HandleP2PDevConfigRewriteReq(void* msg_buf, unsigned int len);
    void HandleP2PDevConfigNodeRewriteReq(void* msg_buf, unsigned int len);
    void HandleP2PDevReportVisitorReq(void* msg_buf, unsigned int len);
    void HandleP2PDevWriteUserinfoReq(void* msg_buf, unsigned int len);
    bool IpChangeMacFilter(const std::string mac);
    bool UserInfoMacFilter(const std::string mac);
private:

    std::mutex beanstalk_mute_;
    static CGroupMsgMng* s_group_msg_mng_instance_;

};

#endif /* __CSADAPT_GROUP_MSG_MNG_H__ */

