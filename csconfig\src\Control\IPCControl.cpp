#include <stdio.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <errno.h>
#include <assert.h>
#include <string.h>
#include <ctype.h>
#include <string>
#include "util_cstring.h"
#include "ConfigDef.h"
#include "AKCSMsg.h"
#include "AkLogging.h"
#include "AdaptUtility.h"
#include "AkcsWebMsgSt.h"
#include "IPCControl.h"
#include "UnixSocketControl.h"
#include "AKCSView.h"
#include "MQProduce.h"
#include "AK.Server.pb.h"
#include "AkcsMsgDef.h"
#include "util.h"
#include "PersonalAccount.h"

#define IPC_RECONNECT_INTERVAL  1000
#define IPC_SELECT_TIMEOUT      2000

extern CSCONFIG_CONF gstCSCONFIGConf;
extern RouteMQProduce* g_nsq_producer;

CIPCControl* GetIPCControlInstance()
{
    return CIPCControl::GetInstance();
}

CIPCControl::CIPCControl()
{

}
CIPCControl::~CIPCControl()
{

}

CIPCControl* CIPCControl::instance = NULL;

CIPCControl* CIPCControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CIPCControl();
    }

    return instance;
}

//个人终端用户,发送请求设备状态的UDP消息给csmain进程
int CIPCControl::SendPersonalReportStatus(std::string mac)
{
    AK::Server::P2PAdaptReportStatusMsg msg;
    msg.set_mac(mac);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PER_SEND_REPORT_STATUS);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSCONFIGConf.nsq_route_topic);
    return 0;
}

//个人终端用户,发送请求设备注销sip的tcp消息给csmain进程
int CIPCControl::SendPerDevLogOutSip(const std::string& mac)
{
    AK::Server::P2PAdaptDevLogOutMsg msg;
    msg.set_macs(mac);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PER_SEND_DEL_DEV);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSCONFIGConf.nsq_route_topic);
    return 0;
}

//个人终端用户,发送请求设备注销sip的tcp消息给csmain进程
int CIPCControl::SendPerUidLogOutSip(const std::string& uid)
{
    AK::Server::P2PAdaptUidLogOutMsg msg;
    msg.set_uids(uid);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_PER_SEND_DEL_UID);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSCONFIGConf.nsq_route_topic);
    return 0;
}


//csadapt通知csmain,更新联系人
int CIPCControl::SendConfigFileChange(CSP2A_CONFIG_FILE_CHANGE* file_change)
{
    AK::Server::GroupAdaptConfFileChangeMsg msg;
    msg.set_mac(file_change->mac);
    msg.set_node(file_change->node);
    msg.set_type(file_change->type);
    msg.set_mng_id(file_change->mng_id);
    msg.set_unit_id(file_change->unit_id);
    msg.set_notify_type(file_change->nNotifyType);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_NOTIFY_CONFIG_FILE_CHANGE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSCONFIGConf.nsq_route_topic);
    return 0;
}


//清空设备码
int CIPCControl::SendDevCleanDeviceCode(const CSP2A_DEV_CLEAN_DEVICE_CODE* expire)
{
    AK::Server::P2PAdaptDevCleanDeviceCodeMsg msg;
    msg.set_macs(expire->szMacs);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_DEV_CLEAN_DEV_CODE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSCONFIGConf.nsq_route_topic);
    return 0;
}


//通知csmain刷新conn缓存
int CIPCControl::NotifyRefreshConnCache(CSP2A_REFRESH_CACHE& info)
{
    AK::Server::GroupAdaptNotifyRefreshConnCache msg;
    msg.set_mac(info.mac);
    msg.set_node(info.node);
    msg.set_type(info.type);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_REFRESH_CONN_CACHE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSCONFIGConf.nsq_route_topic);
    return 0;
}

void CIPCControl::NotifyChangeMainSite(const std::string& after_main_site, const std::string& before_main_site)
{
    AK::Server::GroupAdaptNotifyChangeMainSite msg;
    msg.set_after_main_site(after_main_site);
    msg.set_before_main_site(before_main_site);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_CHANGE_MAIN_SITE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSCONFIGConf.nsq_route_topic);
}

//csadapt通知csmain,发送keysend
int CIPCControl::SendDevFileChange(CSP2A_DEV_FILE_CHANGE* dev_change)
{
    AK::Server::P2PAdaptNotifyFileChangeMsg msg;
    msg.set_mac(dev_change->mac);
    msg.set_type(dev_change->type);
    msg.set_msg_traceid(dev_change->traceid);
    msg.set_file_path(dev_change->file_path);
    msg.set_file_md5(dev_change->file_md5);
    CAkcsPdu pdu;
    pdu.SetMsgBody(&msg);
    pdu.SetHeadLen(sizeof(PduHeader_t));
    pdu.SetVersion(50);
    pdu.SetCommandId(MSG_C2S_NOTIFY_FILE_CHANGE);
    pdu.SetSeqNum(0);
    g_nsq_producer->OnPublish(pdu, gstCSCONFIGConf.nsq_route_topic);
    return 0;
}


