#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <pthread.h>
#include <string.h>
#include <ctype.h>
#include <string>
#include <sstream>
#include <unistd.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <netdb.h>
#include <sys/types.h>
#include <sys/stat.h>
#include <iomanip>
#include <sys/ioctl.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <net/if.h>
#include <assert.h>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "CharChans.h"
#include "ConfigDef.h"
#include "AkcsCommonDef.h"
#include <json/json.h>
#include "AkLogging.h"
#include "InnerUtil.h"
#include <atomic>


