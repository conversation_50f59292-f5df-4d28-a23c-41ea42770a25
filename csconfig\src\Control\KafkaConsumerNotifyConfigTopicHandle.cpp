#include "KafkaConsumerNotifyConfigTopicHandle.h"
#include "UnixSocketControl.h"
#include "AkLogging.h"
#include "util.h"
#include "AkcsCommonDef.h"
#include "AkcsWebPduBase.h"
#include "SnowFlakeGid.h"
#include "AkcsWebMsgSt.h"
#include "AK.Adapt.pb.h"
#include "ConfigDef.h"
#include "dbinterface/Account.h"
#include "dbinterface/ProjectUserManage.h"
#include "util_judge.h"


extern CSCONFIG_CONF gstCSCONFIGConf;

//kafka_mng_id_filter_的形式为     社区id间用用逗号隔开（举例：,1, 或 ,1,2,3, ）
//初始化开头字符,
thread_local std::string HandleKafkaNotifyConfigTopicMsg::kafka_mng_id_filter_ = ",";

void HandleKafkaNotifyConfigTopicMsg::CheckCommunityOverflowThreshold(const std::vector<cppkafka::Message> &messagelist)
{
    std::unordered_map<std::string, int> count_map;
    for(const auto &msg : messagelist)
    {
        //计数统计
        count_map[msg.get_key()]++;
    }
    //判断是否超过阈值,超过则加入过滤
    for(const auto& pair : count_map)
    {
        if (pair.second > gstCSCONFIGConf.write_config_community_overflow_threshold) {
            kafka_mng_id_filter_ += pair.first;
            kafka_mng_id_filter_ += ",";
        }
    }
}

void HandleKafkaNotifyConfigTopicMsg::FilterRecover()
{
    std::set<std::string> filter_set;
    SplitString(kafka_mng_id_filter_, ",", filter_set);
    for(const auto& filter : filter_set)   
    {
        int filter_id = ATOI(filter.c_str());
        int grade = dbinterface::Account::GetAccountGradeById(filter_id);

        
        //刷整个项目,把之前过滤的刷配置操作都补回来
        int change_type = 0;
        int msg_id = 0;
        int project_type = 0;
        if (akjudge::IsCommunity(grade))
        {
            change_type = WEB_COMM_IMPORT_COMMUNITY;
            msg_id = MSG_P2A_NOTIFY_COMMUNITY_MESSAGE;
            project_type = project::PROJECT_TYPE::RESIDENCE;
            dbinterface::ProjectUserManage::UpdateCommunityAllAccountDataVersion(filter_id);
        }
        else if (akjudge::IsOffice(grade))
        {
            change_type = WEB_OFFICE_IMPORT_OFFICE;
            msg_id = MSG_P2A_NOTIFY_OFFICE_INFO_UPDATE;
            project_type = project::PROJECT_TYPE::OFFICE;
            dbinterface::ProjectUserManage::UpdateOfficeAllAccountDataVersion(filter_id);
        }
        else
        {        
            AK_LOG_WARN << "FilterRecover error project type, filter_id:" << filter_id << " grade:" << grade;
            continue;
        }
        
        AK::Adapt::WebCommunityModifyNotify msg;
        msg.set_community_id(filter_id);
        msg.set_change_type(change_type);
        msg.set_trace_id(AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId());                  
        CAkcsWebPdu web_pdu;
        web_pdu.SetMsgBody(&msg);
        web_pdu.SetProjectType(project_type);        
        web_pdu.SetMsgID(msg_id);
        GetUnixSocketControlInstance()->DispatchMsg(web_pdu.GetBuffer(), web_pdu.GetLength());
    }
    
    //恢复初始值
    kafka_mng_id_filter_ = ",";
}

bool HandleKafkaNotifyConfigTopicMsg::BatchKafkaMessage(const std::vector<cppkafka::Message> &messagelist, uint64_t unread)
{
    AK_LOG_INFO << "message size:" << messagelist.size();
    //未读数量超过一定限度
    if (unread > gstCSCONFIGConf.write_config_heap_up_num)
    {
        //改为批量读取
        kafak_.setBatchReadeNumber(gstCSCONFIGConf.write_config_batch_read_num);
        AK_LOG_INFO << "Heap up" << unread << " Start Batch consumer! batch number:" << gstCSCONFIGConf.write_config_batch_read_num;
    }
    else
    {
        AK_LOG_INFO << "normal consumer! set read numb 1.";
        //恢复单个读取
        kafak_.setBatchReadeNumber(1);
        //把过滤的社区恢复正常
        FilterRecover();
    }

    //检查当前刷配置的社区,是否有超过阈值的社区
    CheckCommunityOverflowThreshold(messagelist);

    for (const auto &msg : messagelist)
    {
        std::string msg_key = msg.get_key();
        //判断是否需要过滤
        if(CheckStrInFilter(kafka_mng_id_filter_, msg_key))
        {
            int project_id = ATOI(msg_key.c_str());
            int grade = dbinterface::Account::GetAccountGradeById(project_id);
            if(akjudge::IsCommunity(grade) || akjudge::IsOffice(grade))
            {
                AK_LOG_INFO << "kafka msg skip, kafka_mng_id_filter_: " << kafka_mng_id_filter_ << " project_id: " << project_id;
                continue;
            }
        }
        
        std::string msg_buf = msg.get_payload();
        GetUnixSocketControlInstance()->DispatchMsg((char*)msg_buf.c_str(), msg_buf.length());
    }

}


