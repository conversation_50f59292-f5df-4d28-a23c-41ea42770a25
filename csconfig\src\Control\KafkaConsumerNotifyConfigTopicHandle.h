#ifndef __NOTIFY_CONFIG_HANDLE_H__
#define __NOTIFY_CONFIG_HANDLE_H__

#include "kafka/KafkaConsumerTopicHandleBase.h"

class HandleKafkaNotifyConfigTopicMsg : public HandleKafkaTopicMsg
{
public:
    HandleKafkaNotifyConfigTopicMsg(){}

    bool BatchKafkaMessage(const std::vector<cppkafka::Message> &message, uint64_t unread);

    static thread_local std::string kafka_mng_id_filter_;

private:    
    void CheckCommunityOverflowThreshold(const std::vector<cppkafka::Message> &messagelist);
    
    void FilterRecover();
};


#endif 
