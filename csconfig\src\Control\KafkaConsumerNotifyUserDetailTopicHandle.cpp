#include "KafkaConsumerNotifyUserDetailTopicHandle.h"
#include "UnixSocketControl.h"
#include "AkLogging.h"


bool HandleKafkaNotifyUserDetailTopicMsg::BatchKafkaMessage(const std::vector<cppkafka::Message> &messagelist, uint64_t unread)
{
    for (const auto &msg : messagelist)
    {
        std::string msg_buf = msg.get_payload();
        GetUnixSocketControlInstance()->DispatchMsg((char*)msg_buf.c_str(), msg_buf.length());
    }

}


