#ifndef __CSADAPT_MQ_PRODUCE_H__
#define __CSADAPT_MQ_PRODUCE_H__

#include <memory>
#include <functional>
#include <evpp/buffer.h>
#include <evpp/any.h>
#include "AkcsPduBase.h"
#include <string>
#include <evnsq/message.h>
#include <evnsq/producer.h>

void MQProduceInit();
void OnNSQReady();
int OnRouteMQMessage(const evnsq::Message* msg);
class RouteMQProduce
{
public:
    RouteMQProduce(evnsq::Producer* producer)
        : client_(producer)
    {}
    ~RouteMQProduce() {}

public:
    bool OnPublish(CAkcsPdu& pdu, const std::string& topic);
    void OnNSQReady();
    void OnConnectError(const std::string& addr);
    bool Status(){return nsq_status_;};
private:
    bool nsq_status_ = false;
    evnsq::Producer* client_;
};


#endif //__CSADAPT_MQ_PRODUCE_H__

