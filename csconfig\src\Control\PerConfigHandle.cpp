#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <assert.h>
#include <string>
#include "PerConfigHandle.h"
#include "DeviceControl.h"
#include "PersonnalDeviceSetting.h"
#include "PersonalAccount.h"
#include "RfKeyControl.h"
#include "PrivateKeyControl.h"
#include "FaceMng.h"
#include "DeviceSetting.h"
#include "PerDevConfig.h"
#include "PerDevContact.h"
#include "PerDevKey.h"
#include "AkcsCommonDef.h"
#include "dbinterface/Account.h"
#include "dbinterface/PerNodeDevices.h"
#include "dbinterface/FaceMngDB.h"


PerConfigHandle::PerConfigHandle(const std::string &node)
{
    node_ = node;
    dev_list_ = nullptr;
    dev_pub_list_ = nullptr;
    config_root_path_ = GetPersonnalDownloadConfPath(node_);
	face_root_path_ = GetPersonnalDownloadFacePath(node_);
    private_root_path_ = GetPersonnalDownloadPrivatekeyPath(node_);
    rf_root_path_ = GetPersonnalDownloadRfidPath(node_);
    contact_root_path_ = GetPersonnalDownloadContactListPath(node_);

    mng_rtsp_type_ = 0;
    mng_sip_type_ = 0;
    mng_rtp_confuse_ = 0;
    dbinterface::Account::GetPerMngTransType(node_, mng_sip_type_, mng_rtp_confuse_, mng_rtsp_type_);

    CreateDir(config_root_path_);
	CreateDir(face_root_path_);
    CreateDir(private_root_path_);
    CreateDir(rf_root_path_);
    CreateDir(contact_root_path_);

    dev_list_ = GetPersonnalDevSettingInstance()->GetNodeDeviceSettingList(node_);
    
    //获取个人公共设备    
    ResidentPerAccount node_info;
    memset(&node_info, 0, sizeof(node_info));
    if (0 ==  dbinterface::ResidentPersonalAccount::InitAccountByUid(node, node_info))
    {
        node_role_ = node_info.role;
        if (node_role_ == ACCOUNT_ROLE_PERSONNAL_V_PUB)//个人公共设备
        {
            if (dev_list_)
            {
                //如果是公共设备，那么下面只会挂一个设备
                dbinterface::PerNodeDevices::GetNodesByPublicDevID(dev_list_->id, pub_per_nodes_);
            }
        }
        else
        {
            std::vector<int> pub_dev_ids;
            dbinterface::PerNodeDevices::GetPublicDevIDsByNodeID(node_info.id, pub_dev_ids);
            if (pub_dev_ids.capacity() > 0)
            {
                std::string ids_str = ListToSeparatedFormatString(pub_dev_ids);
                dev_pub_list_ = GetPersonnalDevSettingInstance()->GetPerPublicDeviceSettingListByIDs(ids_str);
                dbinterface::PerNodeDevices::GetNodesByPublicDevID(dev_pub_list_->id, pub_per_nodes_);
            }   
        } 
    }
}

PerConfigHandle::~PerConfigHandle()
{
    GetPersonnalDevSettingInstance()->DestoryDeviceSettingList(dev_list_);
    GetPersonnalDevSettingInstance()->DestoryDeviceSettingList(dev_pub_list_);
}

int PerConfigHandle::InitAppList()
{
    if (app_list_.size() <= 0 )
    {
        GetPersonalAccountInstance()->DaoGetApplistByNode(node_, app_list_);
    }
    return 0;
}


void PerConfigHandle::UpdateNodeConfig()
{
    if (!dev_list_)
    {
        AK_LOG_WARN << "none devices in this node=" << node_;
        return;
    }
   
    PerDevConfig config(config_root_path_, mng_sip_type_,  mng_rtp_confuse_, mng_rtsp_type_);   
    if (config.WriteDevListFiles(dev_list_) < 0)
    {
        AK_LOG_WARN << "Update personnal config files failed.";
    }
    //更新公共设备
    UpdatePubConfig();
}

void PerConfigHandle::UpdatePubConfig()
{
    if (node_role_ != ACCOUNT_ROLE_PERSONNAL_V_PUB)
    {
        DEVICE_SETTING* dev = dev_pub_list_;
        while (dev != NULL)
        {
            //不能遍历写配置
            DEVICE_SETTING* dev_next =dev->next;
            dev->next = nullptr;
            std::string config_root_path = GetPersonnalDownloadConfPath(dev->device_node);
            //遍历设备列表生成config.xml

            PerDevConfig config(config_root_path, mng_sip_type_,  mng_rtp_confuse_, mng_rtsp_type_);   
            if (config.WriteDevListFiles(dev) < 0)
            {
                AK_LOG_WARN << "Update personnal config files failed.";
            }
            dev->next = dev_next;
            dev = dev->next;
        }
    }
    else
    {
        //网页已经不允许修改这部分内容
    }
}

void PerConfigHandle::UpdateNodeContactList()
{
    InitAppList();
    if (node_role_ != ACCOUNT_ROLE_PERSONNAL_V_PUB && app_list_.size() > 0)
    {
        DEVICE_SETTING* cur_dev = dev_list_;
        while (cur_dev != nullptr)
        {
            PerDevContact contact(contact_root_path_);
            contact.UpdatePerContactFile(cur_dev, dev_list_, app_list_, dev_pub_list_);
            cur_dev = cur_dev->next;
        }
        //TODO:只需要掉一次
        UpdatePubContactListByNode();
    }
}

//住户更新引起公共设备更新的接口
void PerConfigHandle::UpdatePubContactListByNode()
{
    DEVICE_SETTING* cur_dev = dev_pub_list_;
    while (cur_dev != NULL)
    {
        std::string contac_root_path;
        contac_root_path = GetPersonnalDownloadContactListPath(cur_dev->device_node);
        PerDevContact contact(contac_root_path);
        contact.UpdatePerPublicContactFile(cur_dev);
        cur_dev = cur_dev->next;
    }
}

void PerConfigHandle::UpdateNodeRfKey()
{
    RF_KEY* rf_key_list = GetRfKeyControlInstance()->GetPersonnalRootBothRfKeyList(node_);
    //获取nfc/ble 添加到rf末尾 V4.2
    GetRfKeyControlInstance()->GetNodeNFCKeyList(node_, &rf_key_list);
    //获取license plate添加到rf末尾 V7.1.1
    GetRfKeyControlInstance()->GetNodeLicensePlateList(node_, &rf_key_list);

    if (dev_list_ && !(dev_list_->flag & DEVICE_SETTING_FLAG_PER_PUBLIC))//不是公共设备才写
    {
        PerDevKey handle(rf_root_path_);
        int ret = handle.UpdateRfKeyFiles(dev_list_, rf_key_list);
		if (ret < 0 && (rf_key_list != NULL))
        {
            AK_LOG_WARN << "Update personnal rf key files failed.";
        }
    }

    if (rf_key_list != nullptr)
    {
        GetRfKeyControlInstance()->DestoryRfKeyList(rf_key_list);
    }
    
    //更新公共设备
    UpdatePubRfKey();  
}

void PerConfigHandle::UpdatePubRfKey()
{
     if (node_role_ == ACCOUNT_ROLE_PERSONNAL_V_PUB)
     {
        //网页已经不允许修改这部分内容
        return;
     }
     
    RF_KEY* rf_key_list = NULL;
    for (auto& node : pub_per_nodes_)
    {
        RF_KEY* rf_key_list_tmp = GetRfKeyControlInstance()->GetPersonnalRootBothRfKeyList(node.node);
        //获取nfc 添加到rf末尾 V4.2
        GetRfKeyControlInstance()->GetNodeNFCKeyList(node.node, &rf_key_list_tmp);

        if (!rf_key_list)
        {
            rf_key_list = rf_key_list_tmp;
        }
        else
        {
            PRIVATE_KEY* cur_rf_key = rf_key_list;
            while (cur_rf_key != NULL)
            {
                if (!cur_rf_key->next)
                {
                    cur_rf_key->next = rf_key_list_tmp;
                    break;
                }
                else
                {
                    cur_rf_key = cur_rf_key->next;
                }
            }
        }
    } 

    DEVICE_SETTING* dev = dev_pub_list_;
    while (dev != NULL)
    {
        //不能遍历写配置
        DEVICE_SETTING* dev_next =dev->next;
        dev->next = nullptr;
        std::string rf_root_path = GetPersonnalDownloadRfidPath(dev->device_node);
        
        PerDevKey handle(rf_root_path);
        int ret = handle.UpdateRfKeyFiles(dev, rf_key_list);
        if (ret < 0 && (rf_key_list != NULL))
        {
            AK_LOG_WARN << "Update personnal rf key files failed.";
        }
        
        dev->next = dev_next;
        dev = dev->next;
    }
    
    if (rf_key_list != NULL)
    {
        GetRfKeyControlInstance()->DestoryRfKeyList(rf_key_list);
    } 

}

void PerConfigHandle::UpdateNodePrivateKey()
{
    PRIVATE_KEY* private_key_list = GetPrivateKeyControlInstance()->GetPersonnalRootBothPrivateKeyList(node_);
    //遍历PRIVATE_KEY,将KEY放在相应设备的pPrivateKeyHeader后面
    
    //遍历设备生成Privatekey文件
    if (dev_list_ && !(dev_list_->flag & DEVICE_SETTING_FLAG_PER_PUBLIC))//不是公共设备才写
    {
        PerDevKey handle(private_root_path_);
        int ret = handle.UpdatePrivateKeyFiles(dev_list_, private_key_list);
		if (ret < 0 && (private_key_list != nullptr))
        {
            AK_LOG_WARN << "Update personnal private key files failed.";
        }       
    }
    if (private_key_list != nullptr)
    {
        GetPrivateKeyControlInstance()->DestoryPrivateKeyList(private_key_list);
    }
    //更新公共设备
    UpdatePubPrivateKey(); 

}

void PerConfigHandle::UpdatePubPrivateKey()
{
    if (node_role_ == ACCOUNT_ROLE_PERSONNAL_V_PUB)
    {
       //网页已经不允许修改这部分内容
       return;
    }
    PRIVATE_KEY* private_key_list = NULL;
    for (auto& node : pub_per_nodes_)
    {
        PRIVATE_KEY* pri_key_list_tmp = GetPrivateKeyControlInstance()->GetPersonnalRootBothPrivateKeyList(node.node);
        if (!private_key_list)
        {
            private_key_list = pri_key_list_tmp;
        }
        else
        {
            PRIVATE_KEY* cur_private_key = private_key_list;
            while (cur_private_key != NULL)
            {
                if (!cur_private_key->next)
                {
                    cur_private_key->next = pri_key_list_tmp;
                    break;
                }
                else
                {
                    cur_private_key = cur_private_key->next;
                }
            }
        }
    
    } 

    DEVICE_SETTING* dev = dev_pub_list_;
    while (dev != NULL)
    {
        //不能遍历写配置
        DEVICE_SETTING* dev_next =dev->next;
        dev->next = nullptr;    
        std::string root_path = GetPersonnalDownloadPrivatekeyPath(dev->device_node);

        PerDevKey handle(root_path);
        int ret = handle.UpdatePrivateKeyFiles(dev, private_key_list);
        if (ret < 0 && (private_key_list != NULL))
        {
            AK_LOG_WARN << "Update personnal private key files failed.";
        }
        dev->next = dev_next;
        dev = dev->next;
    }
    
    if (private_key_list != NULL)
    {
        GetPrivateKeyControlInstance()->DestoryPrivateKeyList(private_key_list);
    } 
}

void PerConfigHandle::UpdateNodeFace()
{
    InitAppList();
	std::vector<FaceMngInfo> face_mng_infos;
	int ret = dbinterface::FaceMng::GetFaceMngByPersonalAccountIds(face_mng_infos, app_list_);
	if (ret != 0)
	{
		AK_LOG_WARN << "DaoGetFaceMngByPersonalAccountIds Failed";
		return;
	}


	DEVICE_SETTING* cur_dev = dev_list_;
	while (cur_dev != nullptr)
	{
		//只有梯口机和门口机需要维护人脸数据
		if (GetDeviceSettingInstance()->DeviceSupportFaceMng(cur_dev->type))
		{
			CFaceXmlWriter::GetInstance().WriteXml(cur_dev, face_mng_infos, face_root_path_, project::PERSONAL);
		}

		cur_dev = cur_dev->next;
	}

	return;
}
