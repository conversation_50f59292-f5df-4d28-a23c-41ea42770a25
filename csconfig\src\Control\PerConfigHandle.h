#ifndef __PER_CONFIG_HANDLE__
#define __PER_CONFIG_HANDLE__

#include "BasicDefine.h"
#include "AKCSMsg.h"
#include "AkcsCommonSt.h"

typedef struct DEVICE_SETTING_T DEVICE_SETTING;


class PerConfigHandle
{
public:
    PerConfigHandle(const std::string &node);
    ~PerConfigHandle();

    void UpdateNodeContactList();
    void UpdateNodeRfKey();
    void UpdateNodePrivateKey();
    void UpdateNodeConfig();
	void UpdateNodeFace();
    //void UpdateMacDevConfig(const std::string& mac);

    //获取整个家庭和对应公共设备进行通知
    DEVICE_SETTING* GetAllPubDev()
    {
        return dev_pub_list_;
    }

private:
    void UpdatePubPrivateKey();
    void UpdatePubRfKey();
    void UpdatePubContactListByNode();
    void UpdatePubConfig();
    int InitAppList();
    std::string node_;
    DEVICE_SETTING* dev_list_;
    DEVICE_SETTING* dev_pub_list_;
    std::string config_root_path_;
    std::string face_root_path_;
    std::string private_root_path_;
    std::string rf_root_path_;
    std::string contact_root_path_;
    std::vector<DEVICE_CONTACTLIST> app_list_;
    std::vector<PER_NODE_DEVICES> pub_per_nodes_;//公共设备下的住户
    int node_role_;
    int mng_sip_type_;
    int mng_rtp_confuse_;
    int mng_rtsp_type_; // 0:tcp,1:tls
};


#endif

