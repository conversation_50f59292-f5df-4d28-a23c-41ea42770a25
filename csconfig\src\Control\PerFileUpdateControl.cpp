#include <stdio.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <sys/types.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <errno.h>
#include <pthread.h>
#include <assert.h>
#include <string.h>
#include <ctype.h>
#include <vector>
#include <string>
#include <sstream>
#include <evpp/evnsq/producer.h>
#include "AK.Adapt.pb.h"
#include "AK.Crontab.pb.h"
#include "AkcsMsgDef.h"
#include "AK.Server.pb.h"
#include "AkcsWebMsgSt.h"
#include "PerFileUpdateControl.h"
#include "FileUpdateControl.h"
#include "CommConfigHandle.h"
#include "PerConfigHandle.h"
#include "AKCSView.h"
#include "IPCControl.h"
#include "PersonnalDeviceSetting.h"
#include "DeviceSetting.h"
#include "FaceMng.h"
#include "DeviceControl.h"
#include "PersonalAccount.h"
#include "DevUser.h"
#include "DevSchedule.h"
#include "ShadowMng.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/ContactFavorite.h"
#include "dbinterface/ContactBlock.h"
#include "dbinterface/ProjectUserManage.h"
#include "CommonHandle.h"
#include "SnowFlakeGid.h"
#include "AkcsWebPduBase.h"
#include "MsgIdToMsgName.h"


extern CSCONFIG_CONF gstCSCONFIGConf;


CPerFileUpdateContorl::CPerFileUpdateContorl()
{

}

CPerFileUpdateContorl::~CPerFileUpdateContorl()
{

}

CPerFileUpdateContorl* CPerFileUpdateContorl::instance = NULL;

CPerFileUpdateContorl* CPerFileUpdateContorl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CPerFileUpdateContorl();
    }

    return instance;
}
CPerFileUpdateContorl* GetPerFileUpdateContorlInstance()
{
    return CPerFileUpdateContorl::GetInstance();
}

void CPerFileUpdateContorl::PersonalFileHandle(int changetype, const std::string &node, std::vector<std::string> &macs)
{
    std::string macs_string;
    for (auto &mac : macs)
    {
        macs_string += mac;
        macs_string += ";";
    }
    
    AK_LOG_INFO << "PersonalFileHandle change type= " << changetype << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(changetype) << " node= " << node << " mac= " << macs_string;
    PerConfigHandle handle(node);
    switch(changetype)
    {
        case CSMAIN_PER_DEV_IP_CHANGE:
        {
            handle.UpdateNodeConfig();
            handle.UpdateNodeContactList();
            handle.UpdateNodeRfKey();
            handle.UpdateNodePrivateKey();
            break;
        } 
        case CSMAIN_PER_DEV_UPGRADE:
        case CSMAIN_PER_DEV_MAINTANCE:
        {
            handle.UpdateNodeConfig();
            handle.UpdateNodeContactList();
            handle.UpdateNodeRfKey();
            handle.UpdateNodePrivateKey();
            break;
        } 
        case WEB_PER_NODE_UPDATE:/*(只会更新config/contact不会更新卡)IP直播切换、motion开关切换、群组呼叫和顺序呼叫更新*/
        {
            handle.UpdateNodeConfig();
            handle.UpdateNodeContactList();
            break;
        }
        case WEB_PER_DEL_USER:
        case WEB_PER_DEL_SLAVE_USER:        
        case WEB_PER_ADD_USER:
        case WEB_PER_MODIFY_USER:
        case WEB_PER_ADD_SLAVE_USER:
        case WEB_PER_MODIFY_SLAVE_USER:
        {
            handle.UpdateNodeConfig();
            handle.UpdateNodeContactList();
            handle.UpdateNodeRfKey();//BLE/NFC   
            handle.UpdateNodePrivateKey();//卡里面有用户名，TODO:后期也可以分开

            //modify by chenzhx V6.0 人脸信息用户信息要更新
            //if (changetype == WEB_PER_DEL_USER || changetype == WEB_PER_DEL_SLAVE_USER) 
            //{
                handle.UpdateNodeFace();
            //}
            if (changetype == WEB_PER_DEL_USER || changetype == WEB_PER_DEL_SLAVE_USER) 
            {
                CSP2A_REFRESH_CACHE refresh_cache = {0};
                refresh_cache.type = REFRESH_CACHE_TYPE::DELETE_USER;
                snprintf(refresh_cache.node, sizeof(refresh_cache.node), "%s", node.c_str());   
                if (GetIPCControlInstance()->NotifyRefreshConnCache(refresh_cache) != 0)
                {
                    AK_LOG_WARN << "NotifyRefreshConnCache failed";
                }
            }
            break;
        }
        case WEB_PER_ADD_DEV:
        case WEB_PER_DEL_DEV:
        case WEB_PER_MODIFY_DEV:
        case WEB_PER_DEL_THIRD_CAMERA:
        {
            handle.UpdateNodeConfig();
            handle.UpdateNodeContactList();
            handle.UpdateNodeRfKey();
            handle.UpdateNodePrivateKey();
			if (changetype == WEB_PER_ADD_DEV || changetype == WEB_PER_MODIFY_DEV)
            {
                handle.UpdateNodeFace();
            }
            //修改了Location之类的，要刷新csmain 设备conn 缓存
            if(changetype == WEB_PER_MODIFY_DEV)
            {
                CSP2A_REFRESH_CACHE refresh_cache = {0};
                refresh_cache.type = REFRESH_CACHE_TYPE::MODIFY_DEV;
                snprintf(refresh_cache.mac, sizeof(refresh_cache.mac), "%s", macs[0].c_str());   
                if (GetIPCControlInstance()->NotifyRefreshConnCache(refresh_cache) != 0)
                {
                    AK_LOG_WARN << "NotifyRefreshConnCache failed";
                }
            }
            break;
        }        
        case WEB_PER_UPDATE_RF:
        {
            handle.UpdateNodeRfKey();
            break;
        }
        case WEB_PER_UPDATE_PIN:
        {
            handle.UpdateNodePrivateKey();
            break;
        }
        case WEB_PER_PHONE_PAY_SUCC:
        {
            handle.UpdateNodeConfig();
            handle.UpdateNodeContactList();
            break;
        }          
		case WEB_PER_UPLOAD_FACE_PIC:
		case WEB_PER_DELETE_FACE_PIC:
		{
            handle.UpdateNodeFace();
			break;
		}
        case WEB_PER_UPDATE_TIMEZOME:
        case WEB_PER_UPDATE_MAC_CONFIG:
        case WEB_PER_MODIFY_DETECTION_CONFIG:
        {
            handle.UpdateNodeConfig();
            break;            
        }
        case WEB_PER_UPDATE_NODE_CONTACT:
        {   
            handle.UpdateNodeContactList();
            break;
        }
        case WEB_PER_UPDATE_NODE_CONFIG:
        {
            handle.UpdateNodeConfig();
            break;
        }
        default:
        {
            AK_LOG_WARN << "not define this change type= " << changetype << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(changetype);            
        }
            
    }

    DEVICE_SETTING* dev = handle.GetAllPubDev();
    while (dev != NULL)
    {
        dev = dev->next;
    }     
}

void CPerFileUpdateContorl::OnPersonalFileUpdate(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnPersonalFileUpdate The param is NULL";
        return;
    }

    AK::Adapt::WebPersonalModifyNotify msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));

    std::vector<std::string> macs;
    uint32_t mac_cnt = msg.mac_list_size();
    for (uint32_t i = 0; i < mac_cnt; ++i)
    {
        std::string mac = msg.mac_list(i);
        macs.push_back(mac);
    }

    PersonalFileHandle(msg.change_type(), msg.node(), macs);
    GetFileUpdateContorlInstance()->OnDevUpdateCommonHandle(msg.change_type(), macs);
}

void CPerFileUpdateContorl::OnPhoneExpireFileUpdate(void* msg_buf, unsigned int msg_len)
{
    if (NULL == msg_buf)
    {
        AK_LOG_WARN << "OnPhoneExpireFileUpdate The param is NULL";
        return;
    }
    
    CSP2A_DEV_APP_EXPIRE phone_expire;
    CSP2A_DEV_APP_EXPIRE* phone_expire_ptr = &phone_expire;
    memset(phone_expire_ptr, 0, sizeof(CSP2A_DEV_APP_EXPIRE));
    AK::Crontab::PhoneExpire msg;
    CHECK_PB_PARSE_MSG(msg.ParseFromArray((char*)msg_buf + CS_COMMON_MSG_HEADER_SIZE, msg_len - CS_COMMON_MSG_HEADER_SIZE));
    Snprintf(phone_expire_ptr->szEmail, sizeof(phone_expire_ptr->szEmail), msg.email().c_str());
    Snprintf(phone_expire_ptr->szUid, sizeof(phone_expire_ptr->szUid), msg.uid().c_str());
    Snprintf(phone_expire_ptr->szUserName, sizeof(phone_expire_ptr->szUserName), msg.name().c_str());

    AK_LOG_INFO << "Request Phone expire, Account: " << phone_expire_ptr->szUid;
    if (strlen(phone_expire_ptr->szUid) == 0)
    {
        AK_LOG_WARN << "parameter error";
        return;
    }
    std::vector<std::string> macs;
    PersonalFileHandle(WEB_PER_NODE_UPDATE, phone_expire_ptr->szUid, macs); 
}

