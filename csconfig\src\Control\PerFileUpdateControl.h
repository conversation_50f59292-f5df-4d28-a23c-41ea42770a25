#ifndef __PER_FILE_UPDATE_CONTROL_H__
#define __PER_FILE_UPDATE_CONTROL_H__

#include "AkcsWebMsgSt.h"

#include <string>
#include <set>

class CPerFileUpdateContorl
{
public:
    CPerFileUpdateContorl();
    ~CPerFileUpdateContorl();

    static CPerFileUpdateContorl* GetInstance();

    //个人更新配置(config/key等)
    void OnPersonalFileUpdate(void* msg_buf, unsigned int msg_len);
    void OnPhoneExpireFileUpdate(void* msg_buf, unsigned int msg_len);
    void PersonalFileHandle(int changetype, const std::string &node, std::vector<std::string> &macs);
    
private:

private:

    static CPerFileUpdateContorl* instance;
};

CPerFileUpdateContorl* GetPerFileUpdateContorlInstance();

#endif 
