#include <list>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "CharChans.h"
#include "ConfigDef.h"
#include "AKCSMsg.h"
#include "AkLogging.h"
#include "AdaptUtility.h"
#include "SysEnv.h"
#include "Md5.h"

#include "PrivateKeyXml.h"
#include "DeviceSetting.h"
#include "DeviceControl.h"
#include "PrivateKeyControl.h"
#include "AkcsCommonDef.h"
#include "dbinterface/PersonalPrivateKey.h"


CPrivateKeyControl* GetPrivateKeyControlInstance()
{
    return CPrivateKeyControl::GetInstance();
}

CPrivateKeyControl::CPrivateKeyControl()
{

}

CPrivateKeyControl::~CPrivateKeyControl()
{

}

CPrivateKeyControl* CPrivateKeyControl::instance = NULL;

CPrivateKeyControl* CPrivateKeyControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CPrivateKeyControl();
    }

    return instance;
}


//个人终端用户
PRIVATE_KEY* CPrivateKeyControl::GetPersonnalRootBothPrivateKeyList(const std::string& user)
{
    return dbinterface::PersonalPrivateKey::GetRootBothPrivateKeyList(user);
}

void CPrivateKeyControl::DestoryPrivateKeyList(PRIVATE_KEY* private_key_header)
{
    //先遍历一遍将所有的DEVICE_SETTING销毁，放在逻辑层因为MODEL层不能调用DeviceControl的接口
    PRIVATE_KEY* cur_privatekey = private_key_header;
    while (cur_privatekey != NULL)
    {
        GetDeviceControlInstance()->DestoryDeviceSettingList(cur_privatekey->access_device_list);
        cur_privatekey = cur_privatekey->next;
    }
    while (NULL != private_key_header)
    {
        cur_privatekey = private_key_header;
        private_key_header = private_key_header->next;
        delete cur_privatekey;
    }

}
