#ifndef __PRIVATE_KEY_CONTROL_H__
#define __PRIVATE_KEY_CONTROL_H__

typedef struct PRIVATE_KEY_T PRIVATE_KEY;
class CPrivateKeyControl
{
public:
    CPrivateKeyControl();
    ~CPrivateKeyControl();

    static CPrivateKeyControl* GetInstance();


    void DestoryPrivateKeyList(PRIVATE_KEY* private_key_header);

    PRIVATE_KEY* GetPersonnalRootBothPrivateKeyList(const std::string& user);

private:
    static CPrivateKeyControl* instance;

};

CPrivateKeyControl* GetPrivateKeyControlInstance();

#endif
