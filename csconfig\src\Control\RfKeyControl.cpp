#include <list>

#include "BasicDefine.h"
#include "util_cstring.h"
#include "CharChans.h"
#include "ConfigDef.h"
#include "AKCSMsg.h"
#include "AdaptUtility.h"
#include "SysEnv.h"
#include "Md5.h"

#include "DeviceSetting.h"
#include "DeviceControl.h"
#include "RfKeyControl.h"
#include "AkcsCommonDef.h"
#include "dbinterface/PersonalRfcardKey.h"
#include "dbinterface/LicensePlate.h"

CRfKeyControl* GetRfKeyControlInstance()
{
    return CRfKeyControl::GetInstance();
}

CRfKeyControl::CRfKeyControl()
{

}

CRfKeyControl::~CRfKeyControl()
{

}

CRfKeyControl* CRfKeyControl::instance = NULL;

CRfKeyControl* CRfKeyControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CRfKeyControl();
    }

    return instance;
}

void CRfKeyControl::DestoryRfKeyList(RF_KEY* rf_key_header)
{
    //先遍历一遍将所有的DEVICE_SETTING销毁，放在逻辑层因为MODEL层不能调用DeviceControl的接口
    RF_KEY* cur_rf_key = rf_key_header;
    while (cur_rf_key != NULL)
    {
        GetDeviceControlInstance()->DestoryDeviceSettingList(cur_rf_key->access_device_list);
        cur_rf_key = cur_rf_key->next;
    }

    while (NULL != rf_key_header)
    {
        cur_rf_key = rf_key_header;
        rf_key_header = rf_key_header->next;
        delete cur_rf_key;
    }
}


//个人NFC/BLE V4.2 NFC  V4.3 BLE
void CRfKeyControl::GetNodeNFCKeyList(const std::string& user, RF_KEY** keylist)
{
    return dbinterface::PersonalRfcardKey::GetNodeNfcKeyList(user, keylist);
}


//个人终端用户
RF_KEY* CRfKeyControl::GetPersonnalRootBothRfKeyList(const std::string& user)
{
    return dbinterface::PersonalRfcardKey::GetRootBothRfKeyList(user);
}

//社区单元公共设备
RF_KEY* CRfKeyControl::GetCommunityUnitRootBothRfKeyList(const int unit_id)
{
    return dbinterface::PersonalRfcardKey::GetCommunityRootBothRfKeyList(unit_id, csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT);
}

//社区公共设备
RF_KEY* CRfKeyControl::GetCommunityPublicRootBothRfKeyList(const int mng_account_id)
{
    return dbinterface::PersonalRfcardKey::GetCommunityRootBothRfKeyList(mng_account_id, csmain::COMMUNITY_DEVICE_TYPE_PUBLIC);
}


//社区公共设备 获取对应关系的NFC
void CRfKeyControl::GetCommunityPublicNfcKeyList(const int mng_account_id, RF_KEY** keylist)
{
    return dbinterface::PersonalRfcardKey::GetCommunityNfcList(mng_account_id, csmain::COMMUNITY_DEVICE_TYPE_PUBLIC, keylist);
}

void CRfKeyControl::GetCommunityUnitNfcKeyList(const int unit_id, RF_KEY** keylist)
{
    return dbinterface::PersonalRfcardKey::GetCommunityNfcList(unit_id, csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT, keylist);
}

//社区公共设备 获取管理员对公共设备设置的Rf
void CRfKeyControl::GetCommunityPubMacRfKeyList(DEVICE_SETTING* dev_setting, RF_KEY** keylist)
{
    return dbinterface::PersonalRfcardKey::GetCommunityMacRfList(dev_setting, keylist);
}

//个人车牌，ufh V7.1.1
void CRfKeyControl::GetNodeLicensePlateList(const std::string& node, RF_KEY** keylist)
{
    return dbinterface::LicensePlate::GetNodeLicensePlateList(node, keylist);
}

