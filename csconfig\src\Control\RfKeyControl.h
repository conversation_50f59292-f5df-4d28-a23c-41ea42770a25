#ifndef __RF_KEY_CONTROL_H__
#define __RF_KEY_CONTROL_H__

class CRfKeyControl
{
public:
    CRfKeyControl();
    ~CRfKeyControl();

    static CRfKeyControl* GetInstance();

    void DestoryRfKeyList(RF_KEY* rf_key_header);
    //个人终端用户
    RF_KEY* GetPersonnalRootBothRfKeyList(const std::string& user);

    RF_KEY* GetCommunityUnitRootBothRfKeyList(const int unit_id);
    RF_KEY* GetCommunityPublicRootBothRfKeyList(const int account_id);

    void GetNodeNFCKeyList(const std::string& user, RF_KEY** keylist);
    void GetCommunityPublicNfcKeyList(const int mng_account_id, RF_KEY** keylist);
    void GetCommunityUnitNfcKeyList(const int unit_id, RF_KEY** keylist);
    void GetCommunityPubMacRfKeyList(DEVICE_SETTING* dev_setting, RF_KEY** keylist);
    void GetNodeLicensePlateList(const std::string& node, RF_KEY** keylist);

private:
    static CRfKeyControl* instance;

};

CRfKeyControl* GetRfKeyControlInstance();

#endif
