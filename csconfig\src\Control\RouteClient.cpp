#include <stdio.h>
#include <stdlib.h>
#include <string>
#include "AK.Server.pb.h"
#include "AkcsPduBase.h"
#include "AkcsMsgDef.h"
#include <evpp/tcp_conn.h>
#include "GroupMsgMng.h"
#include "RouteClient.h"
#include "util.h"
#include "AkcsMonitor.h"
#include "CsmainMsgHandle.h"

CRouteClient::CRouteClient(evpp::EventLoop* loop,
                           const std::string& server_addr/*ip:port*/,
                           const std::string& name,
                           const std::string& logic_srv_id)
    : client_(loop, server_addr, name)
    , addr_(server_addr)
    , route_codec_(std::bind(&CRouteClient::OnMessage, this, std::placeholders::_1, std::placeholders::_2))
    , logic_srv_id_(logic_srv_id)
    , connect_status_(false)
    , ping_status_(true)
{

    client_.SetConnectionCallback(
        std::bind(&CRouteClient::OnConnection, this, std::placeholders::_1));
    client_.SetMessageCallback(
        std::bind(&AkcsIpcMsgCodec::OnMessage, &route_codec_, std::placeholders::_1, std::placeholders::_2));
    client_.set_connecting_timeout(evpp::Duration(2.0));
    client_.set_auto_reconnect(true);
    loop->RunEvery(evpp::Duration(121.0), std::bind(&CRouteClient::onRoutePingCheckTimer, this));
}

void CRouteClient::Stop()
{
    if (connect_status_ == true)
    {
        client_.Disconnect();
        connect_status_ = false;
    }
    client_.set_auto_reconnect(false);
}

void CRouteClient::OnConnection(const evpp::TCPConnPtr& conn)
{
    if (conn->IsConnected())
    {
        connect_status_ = true;
        AK_LOG_INFO << "connect to route server " << addr_ << " successful.";
        //注册srv-id
        AK::Server::LogicSrvReg msg_logic_srv_reg;
        msg_logic_srv_reg.set_logic_srv_uid(logic_srv_id_);
        msg_logic_srv_reg.set_srv_type(AK::Base::LOGIC_CLIENT_TYPE_CONFIG);
        CAkcsPdu pdu;
        pdu.SetMsgBody(&msg_logic_srv_reg);
        pdu.SetHeadLen(sizeof(PduHeader_t));
        pdu.SetVersion(50); //ver=50
        pdu.SetCommandId(AKCS_MSG_L2R_REG_UID_REQ);
        pdu.SetSeqNum(0);
        conn->Send(pdu.GetBuffer(), pdu.GetLength());
    }
    else //参考: CRouteClientMng::UpdateRouteSrv 的逻辑处理
    {
        AK_LOG_WARN << "disconnect to route server " << addr_;        
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csconfig", "csconfig connect csroute error", AKCS_MONITOR_ALARM_MODULE_CONNECT_ERROR);
        if (connect_status_ == true)//对端先断开
        {
            connect_status_ = false;
            //client_.Disconnect();//当对端主动关闭的时候,本段立马执行关闭. chenyc,2019-05-10
        }
        else
        {
            //本端先断开的情况,暂时不需要业务处理. 此时connect_status_ = false由CRouteClient::Stop操作.
        }
    }
}

void CRouteClient::OnRoutePing()
{
    ping_status_ = true;
}
void CRouteClient::onRoutePingCheckTimer()
{
    if ((ping_status_ == false) && (connect_status_ == true))
    {
        AK_LOG_WARN << "in one ping check loop, i donnot have received any ping msg from csroute, reconnect to csroute ";
        client_.Disconnect();
        client_.Connect();
        client_.set_auto_reconnect(true);
    }
    ping_status_ = false;
}

bool CRouteClient::IsConnStatus()
{
    return connect_status_ == true;
}

//added by chenyc, 2019-03-05,分布式系统改造,csadapt与csroute的tcp长连接消息,都是csroute的广播消息发往csadapt
//参考原先 CIPCControl::OnIPCMsg(IPC_MSG *pMsg)  对接即可...
void CRouteClient::OnMessage(const evpp::TCPConnPtr& conn, const std::unique_ptr<CAkcsPdu>& pdu)
{
    uint32_t msg_id = pdu->GetCommandId();
    AK_LOG_INFO << "csroute ser msg, pdumsg id, 0x" << std::hex <<  msg_id;
    switch (msg_id)
    {

        case AKCS_MSG_R2L_PING_REQ:
        {
            OnRoutePing();
            break;
        }

        default:
        {
            GetCsmainMsgHandleInstance()->AddMsg(pdu);
        }
    }
}

std::string CRouteClient::GetAddr()
{
    return addr_;
}


