#include "SL20LockConfigHandle.h"
#include "dbinterface/SmartLock.h"
#include "AkLogging.h"
#include "AkcsCommonSt.h"
#include "util_time.h"
#include "AkcsCommonDef.h"
#include <exception>
#include "dbinterface/SmartLockShadow.h"
#include "AKCSView.h"
#include <set>
#include "dbinterface/CommPerPrivateKey.h"
#include "dbinterface/CommPerRfKey.h"
#include "dbinterface/CommunityInfo.h"
#include "dbinterface/CommPersonalAccount.h"
#include "dbinterface/Account.h"

SL20LockConfigHandle::SL20LockConfigHandle(const std::string& uuid, const UsersPinInfoMap& pin_list, const UsersRFInfoMap& rf_card_list)
                                        : lock_uuid_(uuid), credential_pwd_index_(0), already_get_lock_info_(false)
{
    GetLockRelatedInfo();
    pin_list_ = pin_list;
    rf_card_list_ = rf_card_list;
}

SL20LockConfigHandle::SL20LockConfigHandle(const std::string& uuid)
                                        : lock_uuid_(uuid), credential_pwd_index_(0), already_get_lock_info_(false)
{
    GetLockRelatedInfo();
}

void SL20LockConfigHandle::GetLockRelatedInfo()
{
    if (lock_uuid_.size() == 0)
    {
        AK_LOG_WARN << "load config failed. sl20lock uuid is empty.";
        return;
    }

    if (0 == dbinterface::SmartLock::GetSmartLockInfoByUUID(lock_uuid_, sl20_lock_info_))
    {
        already_get_lock_info_ = true;
    }

    return;
}

int SL20LockConfigHandle::WriteConfig()
{
    if (!already_get_lock_info_)
    {
        AK_LOG_WARN << "get sl20 lock info failed. lock uuid=" << lock_uuid_;
        return -1;
    }

    //根据锁信息生成影子信息
    if (0 != GenerateSL20LockShadowInfo())
    {
        AK_LOG_WARN << "generate sl20 lock shadow info failed.";
        return -1;
    }

    //配置加载成功，更新数据库
    if (0 != dbinterface::SmartLockShadow::UpdateSmartLockConfigurationShadow(sl20_lock_shadow_info_))
    {
        AK_LOG_WARN << "write config to db failed. lock uuid=" << lock_uuid_;
        return -1;
    }

    return 0;
}

int SL20LockConfigHandle::GenerateSL20LockShadowInfo()
{
    //获取configuration
    std::string configuration;
    if (0 != GenerateSL20LockConfiguration(configuration))
    {
        AK_LOG_WARN << "get sl20 lock configuration json string failed.";
        return -1;
    }

    //获取hash
    std::string hash = GetHashValStr(configuration);

    //赋值
    Snprintf(sl20_lock_shadow_info_.configuration_hash, sizeof(sl20_lock_shadow_info_.configuration_hash), hash.c_str());
    Snprintf(sl20_lock_shadow_info_.configuration, sizeof(sl20_lock_shadow_info_.configuration), configuration.c_str());
    Snprintf(sl20_lock_shadow_info_.smartlock_uuid, sizeof(sl20_lock_shadow_info_.smartlock_uuid), lock_uuid_.c_str());

    return 0;
}

int SL20LockConfigHandle::GenerateSL20LockConfiguration(std::string& configuration)
{
    Json::Value data;
    //更新时间
    data["configuration"]["updated_time"] = GetCurrentTimeStamp();

    //用户pin和card
    GenerateCredentialPwds(data);
    
    //锁基本配置
    data["configuration"]["basic"]["auto_lock"] = (sl20_lock_info_.auto_lock_enable == 1);
    data["configuration"]["basic"]["auto_lock_delay"] = sl20_lock_info_.auto_lock_delay;
    data["configuration"]["basic"]["keep_alive_switch"] = sl20_lock_info_.keep_alive;
    data["configuration"]["basic"]["secret_key"] = sl20_lock_info_.secret_key;

    //生成json字符串
    Json::FastWriter writer;
    
    try 
    {
        configuration = writer.write(data);
    } 
    catch (const std::exception& e) 
    {
        AK_LOG_WARN << "get json string failed.";
        return -1;
    }

    return 0;
}

void SL20LockConfigHandle::GenerateCredentialPwds(Json::Value& data)
{
    data["configuration"]["credential_pwds"] = Json::arrayValue; //初始化为空数组

    GenerateCredentialPwdPin(data);
    GenerateCredentialPwdRfCard(data);
}

void SL20LockConfigHandle::NotifySmartlockIfKeepAlive()
{
    // 只有当sl20锁开启保活时，才需要主动通知
    if (sl20_lock_info_.keep_alive == 0)
    {
        AK_LOG_INFO << "smart lock keepalive = 0. no need to notify. lock uuid=" << sl20_lock_info_.uuid;
        return;
    }

    AK_LOG_INFO << "notify smart lock config upgrade. lock uuid=" << sl20_lock_info_.uuid;
    GetAKCSViewInstance()->NotifySmartLockConfigChange(sl20_lock_info_.uuid, NotifySmartLockType::NOTIFY_SMARTLOCK_TYPE_SL20);
}

void SL20LockConfigHandle::ForceNotifySmartlock()
{
    // 可能有保活开关从开到关的情况，此时也需要通知
    AK_LOG_INFO << "force notify smart lock config upgrade. lock uuid=" << sl20_lock_info_.uuid;
    GetAKCSViewInstance()->NotifySmartLockConfigChange(sl20_lock_info_.uuid, NotifySmartLockType::NOTIFY_SMARTLOCK_TYPE_SL20);
}

void SL20LockConfigHandle::GenerateCredentialPwdPin(Json::Value& data)
{
    SL20LockCredentialType credential_type = SL20LockCredentialType::CREDENTIAL_TYPE_USER_PIN;
    int pin_count = 0;
    for (const auto& pins : pin_list_)
    {
        for (const auto& pin : pins.second)
        {
            data["configuration"]["credential_pwds"][credential_pwd_index_]["id"] = credential_pwd_index_ + 1;
            data["configuration"]["credential_pwds"][credential_pwd_index_]["note"] = "pin";
            data["configuration"]["credential_pwds"][credential_pwd_index_]["pwd"] = pin;
            data["configuration"]["credential_pwds"][credential_pwd_index_]["credential_type"] = int(credential_type);
            data["configuration"]["credential_pwds"][credential_pwd_index_]["account"] = pins.first;
            credential_pwd_index_++;
        }
    }
    return;
}

void SL20LockConfigHandle::GenerateCredentialPwdRfCard(Json::Value& data)
{
    SL20LockCredentialType credential_type = SL20LockCredentialType::CREDENTIAL_TYPE_USER_CARD;
    int rfcard_count = 0;
    for (const auto& rf_cards : rf_card_list_)
    {
        for (const auto& rf_card : rf_cards.second)
        {
            data["configuration"]["credential_pwds"][credential_pwd_index_]["id"] = credential_pwd_index_ + 1;
            data["configuration"]["credential_pwds"][credential_pwd_index_]["note"] = "rfcard";
            data["configuration"]["credential_pwds"][credential_pwd_index_]["pwd"] = rf_card;
            data["configuration"]["credential_pwds"][credential_pwd_index_]["credential_type"] = int(credential_type);
            data["configuration"]["credential_pwds"][credential_pwd_index_]["account"] = rf_cards.first;
            credential_pwd_index_++;
        }
    }
    return;
}
