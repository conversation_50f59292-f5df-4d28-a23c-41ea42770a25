#ifndef __SL20_LOCK_CONFIG_HANDLE_H__
#define __SL20_LOCK_CONFIG_HANDLE_H__

#include "SmartLockConfigHandle.h"
#include "dbinterface/SL20Lock.h"
#include "dbinterface/SL20Shadow.h"
#include "json/json.h"
#include "dbinterface/CommPerPrivateKey.h"
#include "dbinterface/CommPerRfKey.h"
#include "dbinterface/CommunityInfo.h"

class SL20LockConfigHandle : public SmartLockConfigHandle
{
public:
    SL20LockConfigHandle(const std::string& uuid, const UsersPinInfoMap& pin_list, const UsersRFInfoMap& rf_list);
    SL20LockConfigHandle(const std::string& uuid); // 用于不需要写配置的场景
    void GetLockRelatedInfo();
    int WriteConfig();
    void NotifySmartlockIfKeepAlive();
    void ForceNotifySmartlock();

private:

    SL20LockInfo sl20_lock_info_;
    SL20ShadowInfo sl20_lock_shadow_info_;
    UsersPinInfoMap pin_list_;
    UsersRFInfoMap rf_card_list_;

    std::string lock_uuid_;
    bool already_get_lock_info_;
    int credential_pwd_index_; // 密码索引，从0开始

    int GenerateSL20LockShadowInfo();
    int GenerateSL20LockConfiguration(std::string& configuration);
    void GenerateCredentialPwds(Json::Value& data);
    void GenerateCredentialPwdPin(Json::Value& data);
    void GenerateCredentialPwdRfCard(Json::Value& data);
};


#endif