#include "SL20LockUpdateManager.h"
#include "dbinterface/CommunityInfo.h"
#include <map>
#include <string>
#include "SL20LockConfigHandle.h"
#include "AkLogging.h"
#include "dbinterface/CommPerRfKey.h"
#include "dbinterface/CommPerPrivateKey.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/PersonalPrivateKey.h"
#include "dbinterface/PersonalRfcardKey.h"
#include "dbinterface/Account.h"
#include "dbinterface/CommPersonalAccount.h"

const int SL20_LOCK_PIN_LENGTH = 6;
const int SL20_LOCK_RF_CARD_LENGTH = 8;
const int SL20_LOCK_PIN_COUNT_LIMIT = 10;
const int SL20_LOCK_RF_CARD_COUNT_LIMIT = 9;

// 判断pin是否合法, sl20锁只支持6位pin
bool SL20LockUpdateManager::IsPinLegal(const std::string& pin)
{
    return pin.size() == SL20_LOCK_PIN_LENGTH;
}

// 判断rfcard是否合法, sl20锁只支持8位rfcard
bool SL20LockUpdateManager::IsRfCardLegal(const std::string& rf_card)
{
    return rf_card.size() == SL20_LOCK_RF_CARD_LENGTH;
}

bool SL20LockUpdateManager::IsExceedPinCountLimit(int pin_count)
{
    return pin_count >= SL20_LOCK_PIN_COUNT_LIMIT;
}

bool SL20LockUpdateManager::IsExceedRfCardCountLimit(int rf_card_count)
{
    return rf_card_count >= SL20_LOCK_RF_CARD_COUNT_LIMIT;
}

int SL20LockUpdateManager::UpdateSL20Lock(const std::string& lock_uuid, const std::string& node, int project_type, int mng_id, bool is_force_notify)
{
    UsersPinInfoMap pin_list;
    UsersRFInfoMap rf_card_list;

    if (0 != GetNodePinAndRfCardList(node, project_type, mng_id, pin_list, rf_card_list))
    {
        AK_LOG_WARN << "get node pin and rf card list failed.";
        return -1;
    }

    SL20LockConfigHandle sl20_lock_config_handle(lock_uuid, pin_list, rf_card_list);
    if (0 != sl20_lock_config_handle.WriteConfig())
    {
        AK_LOG_WARN << "write sl20 lock config failed. lock uuid=" << lock_uuid;
        return -1;
    }
    
    if (is_force_notify)
    {
        sl20_lock_config_handle.ForceNotifySmartlock();
    }
    else
    {
        sl20_lock_config_handle.NotifySmartlockIfKeepAlive();
    }
    
    return 0;
}

int SL20LockUpdateManager::UpdateNodeSL20Locks(const std::string& node, int project_type, int mng_id)
{
    // 获取房间下所有锁列表
    std::set<std::string> sl20_lock_uuid_list;
    dbinterface::SL20Lock::GetSL20LockUUIDListByNode(node, sl20_lock_uuid_list);
    if (sl20_lock_uuid_list.empty())
    {
        AK_LOG_INFO << "no sl20 lock found. node=" << node;
        return 0;
    }

    UsersPinInfoMap pin_list;
    UsersRFInfoMap rf_card_list;

    if (0 != GetNodePinAndRfCardList(node, project_type, mng_id, pin_list, rf_card_list))
    {
        AK_LOG_WARN << "get node pin and rf card list failed.";
        return -1;
    }

    for (const auto& lock_uuid : sl20_lock_uuid_list)
    {
        SL20LockConfigHandle sl20_lock_config_handle(lock_uuid, pin_list, rf_card_list);
        if (0 != sl20_lock_config_handle.WriteConfig())
        {
            AK_LOG_WARN << "write sl20 lock config failed. lock uuid=" << lock_uuid;
            continue;
        }
        
        sl20_lock_config_handle.NotifySmartlockIfKeepAlive();
    }
    
    return 0;
}

int SL20LockUpdateManager::UpdateCommunitySL20Locks(int mng_id)
{
    // 转换为account_uuid
    std::string account_uuid;
    if (0 != dbinterface::Account::GetUUIDByMngAccountId(mng_id, account_uuid))
    {
        AK_LOG_WARN << "get account uuid failed. mng_id=" << mng_id;
        return -1;
    }

    // 获取社区下所有锁列表
    SL20LockInfoList sl20_lock_list;
    dbinterface::SL20Lock::GetSL20LockInfoListByAccountUUID(account_uuid, sl20_lock_list);
    if (sl20_lock_list.empty())
    {
        AK_LOG_INFO << "no sl20 lock found. mng_id=" << mng_id;
        return 0;
    }

    // 获取对应的房间列表
    std::set<std::string> node_uuid_list;
    for (const auto& sl20_lock : sl20_lock_list)
    {
        node_uuid_list.insert(sl20_lock.personal_account_uuid);
    }

    for (const auto& node_uuid : node_uuid_list)
    {
        // 转换为node
        std::string node;
        if (0 != dbinterface::ResidentPersonalAccount::GetAccountByUUID(node_uuid, node))
        {
            AK_LOG_WARN << "get node failed. node_uuid=" << node_uuid;
            continue;
        }
        
        UpdateNodeSL20Locks(node, project::RESIDENCE, mng_id);
    }
}

void SL20LockUpdateManager::NotifySL20LockUpdate(const std::string& lock_uuid)
{
    // 通知锁更新配置，不需要写配置
    SL20LockConfigHandle sl20_lock_config_handle(lock_uuid);
    sl20_lock_config_handle.NotifySmartlockIfKeepAlive();

    return;
}

int SL20LockUpdateManager::GetNodePinAndRfCardList(const std::string& node, int project_type, int mng_id,
                                                    UsersPinInfoMap& pin_list, UsersRFInfoMap& rf_card_list)
{
    if (project_type == project::RESIDENCE)
    {
        CommunityInfo community_info(mng_id);

        std::string accounts_str;
        dbinterface::ResidentPersonalAccount::GetNodeAccountsStrByNode(node, accounts_str);

        GetCommuntiyNodePinList(accounts_str, community_info, pin_list);
        GetCommunityNodeRfCardList(accounts_str, community_info, rf_card_list);
    }
    else if (project_type == project::PERSONAL)
    {
        GetSingleUserNodePinList(node, pin_list);
        GetSingleUserNodeRfList(node, rf_card_list);
    }
    else
    {
        AK_LOG_WARN << "not support this project type. project type=" << project_type;
        return -1;
    }

    return 0;
}


void SL20LockUpdateManager::GetCommuntiyNodePinList(const std::string& accounts_str, const CommunityInfo& community_info, UsersPinInfoMap& pin_list)
{
    UserPinInfoList pin_info_list;
    dbinterface::CommPerPrivateKey::GetOrderedAccountPrivatekeyList(accounts_str, pin_info_list);
          
    bool is_user_allowed_create_pin = community_info.IsAllowCreatePin();
    bool is_pm_allowed_create_pin = community_info.EnablePrivateAccess();

    int pin_count = 0;

    for (const auto& pin_info : pin_info_list)
    {
        if (IsExceedPinCountLimit(pin_count))
        {
            break;
        }

        if (!IsPinLegal(pin_info.pin))
        {
            continue;
        }

        if (pin_info.is_create_by_pm && is_pm_allowed_create_pin)
        {
            pin_list[pin_info.account].push_back(pin_info.pin);
            pin_count++;
        }

        if (!pin_info.is_create_by_pm && is_user_allowed_create_pin)
        {
            pin_list[pin_info.account].push_back(pin_info.pin);
            pin_count++;
        }
    }
}

void SL20LockUpdateManager::GetCommunityNodeRfCardList(const std::string& accounts_str, const CommunityInfo& community_info, UsersRFInfoMap& rf_card_list)
{
    UserRFInfoList rf_info_list;
    // 获取用户自己的卡
    dbinterface::CommPerRfKey::GetOrderedAccountRfkeyList(accounts_str, rf_info_list);

    /* 用户创建的rfcard，都下发. pm的根据开关判断 */
    bool is_pm_allowed_create_rf = community_info.EnablePrivateAccess();

    int rf_count = 0;

    for (const auto& rf_info : rf_info_list)
    {
        if (IsExceedRfCardCountLimit(rf_count))
        {
            break;
        }

        if (!IsRfCardLegal(rf_info.rf_card))
        {
            continue;
        }

        if (rf_info.is_create_by_pm && is_pm_allowed_create_rf)
        {
            rf_card_list[rf_info.account].push_back(rf_info.rf_card);
            rf_count++;
        }

        if (!rf_info.is_create_by_pm)
        {
            rf_card_list[rf_info.account].push_back(rf_info.rf_card);
            rf_count++;
        }
    }

}


void SL20LockUpdateManager::GetSingleUserNodePinList(const std::string& node, UsersPinInfoMap& pin_list)
{
    UserPinInfoList pin_info_list;
    dbinterface::PersonalPrivateKey::GetOrderedSingleUserNodePrivateKey(node, pin_info_list);

    int pin_count = 0;

    for (const auto& pin_info : pin_info_list)
    {
        if (IsExceedPinCountLimit(pin_count))
        {
            break;
        }

        if (!IsPinLegal(pin_info.pin))
        {
            continue;
        }

        pin_list[pin_info.account].push_back(pin_info.pin);
        pin_count++;
    }
}

void SL20LockUpdateManager::GetSingleUserNodeRfList(const std::string& node, UsersRFInfoMap& rf_card_list)
{
    UserRFInfoList rf_info_list;
    dbinterface::PersonalRfcardKey::GetOrderedSingleUserNodeRfKeyList(node, rf_info_list);

    int rf_count = 0;

    for (const auto& rf_info : rf_info_list)
    {
        if (IsExceedRfCardCountLimit(rf_count))
        {
            break;
        }

        if (!IsRfCardLegal(rf_info.rf_card))
        {
            continue;
        }

        rf_card_list[rf_info.account].push_back(rf_info.rf_card);
        rf_count++;
    }
}