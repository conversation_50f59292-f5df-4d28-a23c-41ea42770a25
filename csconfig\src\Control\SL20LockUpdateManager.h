#ifndef SL20_LOCK_UPDATE_MANAGER_H
#define SL20_LOCK_UPDATE_MANAGER_H

#include <string>
#include <vector>
#include <map>
#include <set>
#include "dbinterface/CommunityInfo.h"
#include "dbinterface/CommPerPrivateKey.h"
#include "dbinterface/CommPerRfKey.h"

class SL20LockUpdateManager
{
public:
    // 更新单个锁配置
    static int UpdateSL20Lock(const std::string& lock_uuid, const std::string& node, int project_type, int mng_id, bool is_force_notify = false);
    
    // 更新节点下所有SL20锁配置
    static int UpdateNodeSL20Locks(const std::string& node, int project_type, int mng_id);
    
    // 更新社区下所有SL20锁配置
    static int UpdateCommunitySL20Locks(int mng_id);

    // 通知单个锁更新配置
    static void NotifySL20LockUpdate(const std::string& lock_uuid);
private:
    // 获取pin和rfcard列表
    static int GetNodePinAndRfCardList(const std::string& node, int project_type, int mng_id,
                                        UsersPinInfoMap& pin_list, UsersRFInfoMap& rf_card_list);
    // 获取社区节点PIN列表
    static void GetCommuntiyNodePinList(const std::string& accounts_str, const CommunityInfo& community_info, UsersPinInfoMap& pin_list);
    // 获取社区节点RF卡列表
    static void GetCommunityNodeRfCardList(const std::string& accounts_str, const CommunityInfo& community_info, UsersRFInfoMap& rf_card_list);
    // 获取单住户节点PIN列表
    static void GetSingleUserNodePinList(const std::string& node, UsersPinInfoMap& pin_list);
    // 获取单住户RF卡列表
    static void GetSingleUserNodeRfList(const std::string& node, UsersRFInfoMap& rf_card_list);

    static bool IsPinLegal(const std::string& pin);
    static bool IsRfCardLegal(const std::string& rf_card);
    static bool IsExceedPinCountLimit(int pin_count);
    static bool IsExceedRfCardCountLimit(int rf_card_count);
    
};

#endif // SL20_LOCK_UPDATE_SERVICE_H