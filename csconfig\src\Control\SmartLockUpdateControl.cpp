#include "SmartLockUpdateControl.h"
#include "SL20LockConfigHandle.h"
#include "SmartLockConfigHandle.h"
#include <unistd.h>
#include <memory>
#include "MsgIdToMsgName.h"
#include "SL20LockUpdateManager.h"

CSmartLockUpdateControl::CSmartLockUpdateControl()
{

}

CSmartLockUpdateControl::~CSmartLockUpdateControl()
{

}

CSmartLockUpdateControl* CSmartLockUpdateControl::instance = NULL;

CSmartLockUpdateControl* CSmartLockUpdateControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CSmartLockUpdateControl();
    }

    return instance;
}
CSmartLockUpdateControl* GetSmartLockUpdateContorlInstance()
{
    return CSmartLockUpdateControl::GetInstance();
}

//配置文件更新
void CSmartLockUpdateControl::SmartLockConfigUpdateHandle(int changetype, const std::string& lock_uuid, 
                                                        const std::string& node, int project_type, int mng_id)
{
    AK_LOG_INFO << "SmartLockConfigUpdateHandle change type= " << changetype << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(changetype) << " lock uuid=" << lock_uuid;

    std::unique_ptr<SmartLockConfigHandle> smartlock_handle;

    switch(changetype)
    {
        // 更新数据库影子文件+保活情况下通知锁
        case SMARTLOCK_SL20_CONFIG_UPDATE:
        {
            SL20LockUpdateManager::UpdateSL20Lock(lock_uuid, node, project_type, mng_id);
            break;
        }
        case SMARTLOCK_NODE_SL20_CONFIG_UPDATE:
        {
            SL20LockUpdateManager::UpdateNodeSL20Locks(node, project_type, mng_id);
            break;
        }
        case SMARTLOCK_PROJECT_SL20_CONFIG_UPDATE:
        {
            SL20LockUpdateManager::UpdateCommunitySL20Locks(mng_id);
            break;
        }
        case SMARTLOCK_SL20_LOCK_UPDATE_NOTIFY:
        {
            SL20LockUpdateManager::NotifySL20LockUpdate(lock_uuid);
            break;
        }
        case SMARTLOCK_SL20_LOCK_KEEP_ALIVE_SWITCH_CHANGE:
        {
            // 保活开关从开到关的情况，需要推一条消息给锁
            bool is_force_notify = true;
            SL20LockUpdateManager::UpdateSL20Lock(lock_uuid, node, project_type, mng_id, is_force_notify);
            break;
        }
        default:
        {
            AK_LOG_WARN << "not define this change type= " << changetype << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(changetype);            
        }
    }

    return;
}