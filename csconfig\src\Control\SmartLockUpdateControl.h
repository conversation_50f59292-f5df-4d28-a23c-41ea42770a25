#ifndef __SMARTLOCK_UPDATE_CONTROL_H__
#define __SMARTLOCK_UPDATE_CONTROL_H__

#include "AkcsWebMsgSt.h"
#include "dbinterface/CommunityInfo.h"

#include <string>
#include <set>

class CSmartLockUpdateControl
{
public:
    CSmartLockUpdateControl();
    ~CSmartLockUpdateControl();

    static CSmartLockUpdateControl* GetInstance();
    void SmartLockConfigUpdateHandle(int changetype, const std::string& lock_uuid, const std::string& node, int project_type, int mng_id);

private:
    CSmartLockUpdateControl(const CSmartLockUpdateControl&);
    CSmartLockUpdateControl& operator = (const CSmartLockUpdateControl&);

private:
    static CSmartLockUpdateControl* instance;
};

CSmartLockUpdateControl* GetSmartLockUpdateContorlInstance();

#endif //__FILE_UPDATE_CONTROL_H__
