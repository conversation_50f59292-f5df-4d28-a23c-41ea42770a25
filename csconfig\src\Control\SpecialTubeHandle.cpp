#include "SpecialTubeHandle.h"
#include "AkLogging.h"
#include "dbinterface/Account.h"
#include "AkcsCommonDef.h"
#include "AkcsWebPduBase.h"
#include "SnowFlakeGid.h"
#include "AkcsWebMsgSt.h"
#include "AK.Adapt.pb.h"
#include "UnixSocketControl.h"
#include "ConfigDef.h"
#include "util_string.h"


//最大允许堆积
const int special_tube_max_bank = 100;
//延迟处理的时间
const int after_handle_delay = 180;

extern int g_special_tube;
extern CSCONFIG_CONF gstCSCONFIGConf;

SpecialTubeHandle::SpecialTubeHandle()
{
    filter_mng_id_ = 0;
}

SpecialTubeHandle& SpecialTubeHandle::GetInstance()
{
    static SpecialTubeHandle instance;
    return instance;
}

//将投递到special tube的消息, 使用traceid记录到trace_map_缓存起来
void SpecialTubeHandle::AddTrace(uint64_t traceid, int mng_id)
{ 
    AK_LOG_INFO << "SpecialTube AddTrace traceid:" << traceid << " mng_id:" << mng_id;
    int size = 0;
    {
        std::lock_guard<std::mutex> lock(trace_map_mtx_);
        trace_map_.insert(std::make_pair(traceid, mng_id)); 
        size = trace_map_.size();
    }

    //超过最大堆积限制，则触发自动过滤处理流程
    if(size > special_tube_max_bank && filter_mng_id_ == 0)
    {
        //获取队列中出现最多次数的社区id，进行过滤和延迟处理
        int mng_id = GetMaxRepeate();
        dbinterface::AccountInfo project_account;
        //校验社区id合法性
        if (0 == dbinterface::Account::GetAccountById(mng_id, project_account))
        {
            if(project_account.grade == AccountGrade::COMMUNITY_MANEGER_GRADE)
            {
                filter_mng_id_ = mng_id;
                HandelFilterMngAfter(filter_mng_id_);
                return;
            }
        }
        
        AK_LOG_INFO << "SpecialTube max repeate mngid maybe abnormal, mng_id: " << mng_id;
    }
}

//每次消费tube中的消息时，会同步移除缓存中对应的traceeid
void SpecialTubeHandle::RemoveTrace(uint64_t traceid)
{
    std::lock_guard<std::mutex> lock(trace_map_mtx_);
    auto it = trace_map_.find(traceid);
    if(it != trace_map_.end())
    {
        AK_LOG_INFO << "SpecialTube RemoveTrace traceid:" << traceid;
        trace_map_.erase(traceid);    
    }
}

//检查社区是否被过滤
bool SpecialTubeHandle::CheckIsFilter(int mng_id, int change_type)
{
    //change_type为WEB_COMM_UPDATE_COMMUNITY_ALL时，代表是到了自动化处理的流程，需正常处理（并将过滤社区标识移除）
    if(change_type == WEB_COMM_UPDATE_COMMUNITY_ALL)
    {
        filter_mng_id_ = 0;
        return false;
    }

    if(mng_id && mng_id == filter_mng_id_) {
        return true;
    }

    //再判断是否设有全局过滤
    if(CheckStrInFilter(gstCSCONFIGConf.mng_id_filter, to_string(mng_id)))
    {
        return true;
    }

    return false;
}

void SpecialTubeHandle::GetSpecialTubeParam(int &filter_mng_id, int &trace_map_size)
{
    filter_mng_id = filter_mng_id_;
    trace_map_size = trace_map_.size();
}

//获取trace_map_中重复最多次数的社区
int SpecialTubeHandle::GetMaxRepeate()
{
    std::map<uint64_t, int> map_tmp;
    {        
        std::lock_guard<std::mutex> lock(trace_map_mtx_);
        map_tmp = trace_map_;
    }

    //将value出现的次数先记录在count_map
    std::map<int, int> count_map;
    for (const auto& kvpair : map_tmp) 
    {
        int value = kvpair.second; 
        if (count_map.find(value) != count_map.end()) 
        {
            count_map[value] = count_map[value] + 1;
        } 
        else 
        {
            count_map[value] = 1;
        }
    }
    //再通过遍历，比较出现次数的大小
    int max_count = 0;
    int max_value = 0;
    for (const auto& kvpair : count_map) 
    {
        if (kvpair.second > max_count) 
        {
            max_count = kvpair.second;
            max_value = kvpair.first;
        }
    }

    return max_value;
}

void SpecialTubeHandle::HandelFilterMngAfter(int mng_id)
{
    //使用延时自动触发的方式 来刷当前过滤的社区 -- 如果180s内还没全部过滤完，那就还会有一些堆积，但基本不会，且影响不大
    //不使用队列清空再自动触发的方式是因为 期间如果服务挂了，那就可能导致没去刷，而延时队列是有持久化的
    
    AK_LOG_INFO << "SpecialTube handle filter community after, mng_id: " << mng_id;
    /*改为kafka后不会再依赖这个做大社区延迟处理
    //现阶段只有社区在special_tube101队列处理            
    AK::Adapt::WebCommunityModifyNotify msg;
    msg.set_community_id(filter_mng_id_);
    msg.set_change_type(WEB_COMM_UPDATE_COMMUNITY_ALL);
    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    msg.set_trace_id(traceid);            
    CAkcsWebPdu web_pdu;
    web_pdu.SetMsgBody(&msg);
    web_pdu.SetMsgID(MSG_P2A_NOTIFY_COMMUNITY_MESSAGE);
    web_pdu.SetProjectType(project::RESIDENCE);
    GetUnixSocketControlInstance()->AddMsgToBeanstalk(web_pdu.GetBuffer(), web_pdu.GetLength(), g_special_tube, after_handle_delay);
    */
}

