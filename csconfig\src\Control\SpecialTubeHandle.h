#ifndef __SPECIAL_TUBE__
#define __SPECIAL_TUBE__
#include <mutex>
#include <map>
#include <string>
#include <boost/noncopyable.hpp>

class SpecialTubeHandle : private boost::noncopyable
{
public:
    SpecialTubeHandle();
    static SpecialTubeHandle& GetInstance();

    void AddTrace(uint64_t traceid, int mng_id);
    void RemoveTrace(uint64_t traceid);
    bool CheckIsFilter(int mng_id, int change_type = 0);
    void GetSpecialTubeParam(int &filter_mng_id, int &trace_map_size);


private:
    int GetMaxRepeate();
    void HandelFilterMngAfter(int mng_id);

private:
    std::mutex trace_map_mtx_;   
    std::map<uint64_t, int> trace_map_;  //<traceid, mngid>
    int filter_mng_id_;
};


#endif
