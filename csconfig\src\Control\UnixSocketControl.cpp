#include <stdio.h>
#include <stdlib.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <sys/types.h>
#include <unistd.h>
#include <arpa/inet.h>
#include <netinet/in.h>
#include <errno.h>
#include <pthread.h>
#include <thread>
#include <assert.h>
#include <string.h>
#include <ctype.h>
#include <string>
#include <list>
#include <boost/crc.hpp>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "CharChans.h"
#include "ConfigDef.h"
#include "AKCSMsg.h"
#include "AkLogging.h"
#include "AdaptUtility.h"
#include "SysEnv.h"
#include "AkcsWebMsgSt.h"
#include "PrivateKeyControl.h"
#include "RfKeyControl.h"
#include "DeviceControl.h"
#include "AKCSView.h"
#include "UnixSocketControl.h"
#include "beanstalk.hpp"
#include "FileUpdateControl.h"
#include "PerFileUpdateControl.h"
#include "AK.Adapt.pb.h"
#include "AkcsMsgDef.h"
#include "PersonalAccount.h"
#include "util.h"
#include "json/json.h"
#include "OfficeUnixSocketControl.h"
#include "AkcsCommonDef.h"
#include "AK.AdaptOffice.pb.h"
#include "GroupMsgMng.h"
#include "SnowFlakeGid.h"
#include "kafka/AkcsKafkaProducerNotifyConfig.h"
#include "kafka/AkcsKafkaProducerNotifyUserDetail.h"
#include "AkcsWebPduBase.h"


extern CSCONFIG_CONF gstCSCONFIGConf;
Beanstalk::Client* g_bs_client_ptr = nullptr;
Beanstalk::Client* g_bs_backup_client_ptr = nullptr;
const std::string AKCS_ATTACK_IP_TUBE = "akcs_attack_ips"; //beanstalkd的tube,专用于akcs业务判断攻击者的来源ip用
std::mutex g_bs_mutex;
std::mutex g_bs_backup_mutex;

int g_special_tube = 101;   //大社区队列
int g_csmain_userinfo_msg_tube = 102;//csmain过来队列
int g_csmain_userinfo_msg_tube2 = 105;//csmain过来队列
int g_csmain_userinfo_msg_tube3 = 106;//csmain过来队列

int g_csmain_delay_tube = 103; //csmain过来的user去重
int g_web_delay_tube = 104; //web过来去重

std::vector<int> g_user_tubes = {g_csmain_userinfo_msg_tube, g_csmain_userinfo_msg_tube2, g_csmain_userinfo_msg_tube3};

#define BEANSTALK_SERVER_PORT  (8519)


std::string GetWebTube(int id)
{
    char tube[128];
    if(gstCSCONFIGConf.is_aws)
    {
        snprintf(tube, sizeof(tube), "aws_web_to_adapt%d_%s", id, gstCSCONFIGConf.server_inner_ip);
    }
    else
    {
        snprintf(tube, sizeof(tube), "web_to_adapt%d_%s", id, gstCSCONFIGConf.server_inner_ip);
    }
    return tube;
}

CUnixSocketControl* CUnixSocketControl::instance = NULL;
CUnixSocketControl::CUnixSocketControl()
{

}

CUnixSocketControl::~CUnixSocketControl()
{

}

CUnixSocketControl* GetUnixSocketControlInstance()
{
    return CUnixSocketControl::GetInstance();
}
CUnixSocketControl* CUnixSocketControl::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CUnixSocketControl();
    }

    return instance;
}

void CUnixSocketControl::Init(const std::string& beanstalk_ip)
{    
    if(0 == beanstalk_ip.size())
    {
        return;
    }
    
    std::thread thread(ProcessMsgForBeanstalk, g_csmain_delay_tube, beanstalk_ip);
    thread.detach();
    return;
}

int CUnixSocketControl::GetWriteFileTube(const std::string &str, int write_num)
{
    boost::crc_32_type hash;
    hash.process_bytes(str.c_str(), str.size());
    return hash.checksum()%write_num;
}

bool CUnixSocketControl::CheckBeanstalkStatus()
{
    if(!g_bs_client_ptr)
    {
        return false;
    }
    return true;
}

void CUnixSocketControl::InitPduBeanstalk()
{
    g_bs_client_ptr = new Beanstalk::Client(gstCSCONFIGConf.beanstalk_addr, BEANSTALK_SERVER_PORT);
}


//beanstalk因为目前是单节点 因此状态检查判断生产者状态即可
bool CUnixSocketControl::CheckBeanstalkBackUpStatus()
{
    if(!g_bs_backup_client_ptr)
    {
        return false;
    }
    return true;
}

//beanstalk内部限流使用
void CUnixSocketControl::AddMsgToBeanstalk(const char* msg, int len, int tube_id, int delay_interval, const std::string& msg_key)
{
    if ((nullptr == msg) || (len < CS_COMMON_MSG_HEADER_SIZE))
    {
        return;
    }
    uint32_t msg_id = NTOHL(*((unsigned int*)((unsigned char*)msg + 4)));
    uint32_t project_type = NTOHL(*((unsigned int*)((unsigned char*)msg + 8)));
    std::string msg_body((char*)msg + CS_COMMON_MSG_HEADER_SIZE, len - CS_COMMON_MSG_HEADER_SIZE);
    
    //重新构建为统一的消息格式
    AK::Adapt::ConfigCommonMessage config_common_msg;
    config_common_msg.set_msg_id(msg_id);
    config_common_msg.set_project_type(project_type);
    config_common_msg.set_msg_body(msg_body);
    config_common_msg.set_msg_key(msg_key);
    std::string common_msg;
    config_common_msg.SerializeToString(&common_msg);

    std::string tube = GetWebTube(tube_id);
    //默认1秒，aws延迟3秒，异常刷配置延迟180s
    uint32_t delay = delay_interval;
    
    AK_LOG_INFO << "Add msg to beanstalk tube:" << tube;
    if (!g_bs_client_ptr)
    {
        std::lock_guard<std::mutex> lock(g_bs_mutex);
        g_bs_client_ptr = new Beanstalk::Client(gstCSCONFIGConf.beanstalk_addr, BEANSTALK_SERVER_PORT);
        g_bs_client_ptr->use(tube);
        if (!g_bs_client_ptr->put(common_msg.c_str(), common_msg.size(), 0, delay, 1))
        {
            if (g_bs_client_ptr)
            {
                delete g_bs_client_ptr;
                g_bs_client_ptr = nullptr;
            }
            AK_LOG_WARN << "beanstalk put message error, put another";
        }
    }
    else
    {
        std::lock_guard<std::mutex> lock(g_bs_mutex);
        g_bs_client_ptr->use(tube);
        if (!g_bs_client_ptr->put(common_msg.c_str(), common_msg.size(), 0, delay, 1))
        {
            if (g_bs_client_ptr)
            {
                delete g_bs_client_ptr;
                g_bs_client_ptr = nullptr;
            }
            AK_LOG_WARN << "beanstalk put message error, put another";
        }
    }
}

void CUnixSocketControl::ProcessMsgForBeanstalk(int tube_id, const std::string& beanstalk_ip)
{    
    std::string tube = GetWebTube(tube_id);
    AK_LOG_INFO << "Create ProcessThread:" << tube;
    
    while (1)
    {
        Beanstalk::Client client(beanstalk_ip.c_str(), BEANSTALK_SERVER_PORT);
        client.watch(tube);

        Beanstalk::Job job;
        while (client.reserve(job)) //reserve接口阻塞，正常情况不会跳出循环
        {
            
            Beanstalk::info_hash_t stats_map =  client.stats_tube(tube);
            AK_LOG_INFO << tube << " current-jobs-urgent :" << stats_map["current-jobs-urgent"];
            
            client.del(job.id());
            AK_LOG_INFO << "process msg from tube:"<< tube;

            
            AK::Adapt::ConfigCommonMessage config_common_msg;
            if(config_common_msg.ParseFromString(job.body().c_str()) == false)
            {
                AK_LOG_WARN << "parse pb msg failed. msg:" << job.body();
                continue;
            }
            
            std::string msg_key = config_common_msg.msg_key();            
            std::string msg_body = config_common_msg.msg_body();
            uint32_t msg_id = config_common_msg.msg_id();
            uint32_t project_type = config_common_msg.project_type();
            CAkcsWebPdu web_pdu;
            web_pdu.SetMsgBody((const void*)msg_body.c_str(), msg_body.size());
            web_pdu.SetMsgID(msg_id);
            web_pdu.SetProjectType(project_type);         
                        
            if(msg_id == MSG_S2C_DEV_REQ_USER_INFO)
            {
                GetUnixSocketControlInstance()->AddUserDetailMsgToKafka(web_pdu.GetBuffer(), web_pdu.GetLength(), msg_key);
            }
            else
            {
                GetUnixSocketControlInstance()->AddCommonMsgToKafka(web_pdu.GetBuffer(), web_pdu.GetLength(), msg_key);
            }
        }
        sleep(1);
    }

    return;
}

//msg_len:udp消息的整体长度,包括消息头
int CUnixSocketControl::DispatchMsg(void* msg_buf, unsigned int msg_len)
{
    if ((NULL == msg_buf) || (msg_len < CS_COMMON_MSG_HEADER_SIZE))
    {
        return -1;
    }
    BOOL is_need_resp = FALSE;
    uint32_t id = NTOHL(*((unsigned int*)((unsigned char*)msg_buf + 4)));
    uint32_t project_type = NTOHL(*((unsigned int*)((unsigned char*)msg_buf + 8)));
    uint32_t traceid = NTOHL(*((unsigned int*)((unsigned char*)msg_buf + 16)));
    if(traceid == 0)
    {
        traceid = GenRandUint32TraceId();
    }
    ThreadLocalSingleton::GetInstance().SetTraceID((uint64_t)traceid);
    ThreadLocalSingleton::GetInstance().SetDbStatus(true);

    //办公
    if (project_type ==project::OFFICE)
    {
        //办公处理过了直接退出
        OfficeUnixMsgControl::Instance()->OnSocketMsg(msg_buf, msg_len);
        return 1;
    }
    
    //uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    AK_LOG_INFO << "CUnixSocketControl::DispatchMsg start traceid:"<< traceid << " id:" << id;
    //csmain 过来的消息+数据分析后也是丢到csmian过来的队列
    switch(id)
    {
        /*csmain 过来的消息*/
        //有使用beanstalk做限流
        case MSG_S2C_DEV_CONFIG_REWRITE:
        {
            CGroupMsgMng::Instance()->HandleP2PDevConfigRewriteReq(msg_buf, msg_len);
            break;
        }
        //有使用beanstalk做限流
        case MSG_S2C_DEV_REQ_USER_INFO:
        {
            CGroupMsgMng::Instance()->HandleP2PDevWriteUserinfoReq(msg_buf, msg_len);
            break;         
        }
        //有使用beanstalk做限流
        case MSG_S2C_ACCOUNT_CONFIG_REWRITE:
        {
            CGroupMsgMng::Instance()->HandleP2PDevConfigNodeRewriteReq(msg_buf, msg_len);
            break;
        }
        /*csmain 过来的消息*/

        
        //以下是web/csadapt过来的消息
        case MSG_P2A_ACCOUNT_ACCESS_UPDATE_NOTIFY:
        {
            GetFileUpdateContorlInstance()->OnAzerAccountAccessModify(msg_buf, msg_len);
            break;
        }
    
        case MSG_P2A_REGULAR_AUTOP:
        {
            GetAKCSViewInstance()->OnRegularyAutopNotify(msg_buf, msg_len);
            break;
        }
        case MSG_P2A_NOTIFY_PERSONAL_MESSAGE:
        {
            GetPerFileUpdateContorlInstance()->OnPersonalFileUpdate(msg_buf, msg_len);
            break;
        }
        case MSG_P2A_NOTIFY_COMMUNITY_MESSAGE:
        {
            GetFileUpdateContorlInstance()->OnCommunityConfigFileUpdate(msg_buf, msg_len);
            break;
        }    
        case MSG_P2A_NOTIFY_DATA_ANALYSIS_NOTIFY:
        {
            GetAKCSViewInstance()->OnDataAnalysisNotify(msg_buf, msg_len);
            break;
        }


        case MSG_P2A_NOTIFY_ACCESS_GROUP_MODIFY:
        {
            GetAKCSViewInstance()->OnAccessGroupModify(msg_buf, msg_len);
            break;
        }
        case MSG_P2A_NOTIFY_COMMUNITY_PERSONAL_MODIFY:
        {
            GetAKCSViewInstance()->OnCommunityPersonalModify(msg_buf, msg_len);
            break;
        }
        case MSG_P2A_NOTIFY_COMMUNITY_ACCOUNT_MODIFY:
        {
            GetAKCSViewInstance()->OnCommunityAccountModify(msg_buf, msg_len);
            break;
        }
        case MSG_P2A_NOTIFY_COMMUNITY_IMPORT_ACCOUNT_DATAS:
        {    
            GetAKCSViewInstance()->OnCommunityImportAccountData(msg_buf, msg_len);
            break;
        }

        
        case MSG_P2A_PHONE_EXPIRE:
        {
            GetPerFileUpdateContorlInstance()->OnPhoneExpireFileUpdate(msg_buf, msg_len);
            break;
        }

        default:
        {
            //TODO,chenyc:响应消息类型不匹配(由于具体消息ID未知,故只能由通用消息头带回)
            AK_LOG_WARN << "Failed to match msg id:" << id;
            return -1;
        }
    }
    AK_LOG_INFO << "CUnixSocketControl::DispatchMsg end traceid:"<< traceid;
    return 0;
}

void CUnixSocketControl::AddCommonMsgToKafka(const char* msg, int len, const std::string& msg_key)
{
    if ((nullptr == msg) || (len < CS_COMMON_MSG_HEADER_SIZE))
    {
        AK_LOG_WARN << "AddCommonMsgToKafka msg is error";
        return;
    }    
    AK_LOG_INFO << "AddCommonMsgToKafka msg_key is " << msg_key;
    
    std::string kafka_msg(msg, len);    
    AKCS::Singleton<AkcsKafkaProducerNotifyConfig>::instance().ProduceMsgWithLock(msg_key, kafka_msg);
}


void CUnixSocketControl::AddUserDetailMsgToKafka(const char* msg, int len, const std::string& msg_key)
{
    if ((nullptr == msg) || (len < CS_COMMON_MSG_HEADER_SIZE))
    {
        AK_LOG_WARN << "AddUserDetailMsgToKafka msg is error";
        return;
    }    
    AK_LOG_INFO << "AddUserDetailMsgToKafka msg_key is " << msg_key;

    std::string kafka_msg(msg, len);
    AKCS::Singleton<AkcsKafkaProducerNotifyUserDetail>::instance().ProduceMsgWithLock(msg_key, kafka_msg);
}

