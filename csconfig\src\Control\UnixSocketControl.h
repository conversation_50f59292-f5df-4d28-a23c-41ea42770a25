#ifndef __UNIX_CONTROL_H__
#define __UNIX_CONTROL_H__

#include <map>

#define BEANSTALK_NORMAL_DELAY          1
#define BEANSTALK_AWS_DELAY             3
#define BEANSTALK_IPCHANGE_DELAY      180


class CUnixSocketControl
{
public:
    CUnixSocketControl();
    ~CUnixSocketControl();
    static CUnixSocketControl* GetInstance();
    void Init(const std::string& beanstalk_ip);

    int GetWriteFileTube(const std::string &str, int write_num);
    
    //消费者线程
    static void ProcessMsgForBeanstalk(int tube_id, const std::string& beanstalk_ip);

    void AddMsgToBeanstalk(const char* msg, int len, int tube_id, int is_delay, const std::string& msg_key);
    int DispatchMsg(void* msg_buf, unsigned int msg_len);
    bool CheckBeanstalkStatus();
    bool CheckBeanstalkBackUpStatus();
    void InitPduBeanstalk();
    void AddCommonMsgToKafka(const char* msg, int len, const std::string& msg_key);    
    void AddUserDetailMsgToKafka(const          char* msg, int len, const std::string& msg_key);
private:
    CUnixSocketControl(const CUnixSocketControl&);
    CUnixSocketControl& operator = (const CUnixSocketControl&);


    static CUnixSocketControl* instance;

    //消息消费者线程ID
    pthread_t thread_process;
};

CUnixSocketControl* GetUnixSocketControlInstance();

#endif //__UNIX_CONTROL_H__
