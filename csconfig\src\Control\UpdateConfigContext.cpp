#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <assert.h>
#include <string>
#include "UpdateConfigContext.h"
#include "dbinterface/Account.h"
#include "AkLogging.h"
#include "util_string.h"

ConfigContext::ConfigContext()
{
    push_button_control_ = std::make_shared<DeviceExternPushButton>();

}

void ConfigContext::Init(uint32_t mng_id, const std::string &project_uuid)
{
    mng_id_ = mng_id;
    dbinterface::CommunityUnit::GetCommunityUnitMap(mng_id_, units_map_);
    dbinterface::CommunityCallRule::GetCommunityCallRuleByCommunityUUID(project_uuid, call_rule_map_);
    dbinterface::VersionModel::GetNoMonitorList(no_monitor_list_);
    dbinterface::VersionModel::GetPackageDetectionList(package_detection_list_);
    // dbinterface::VersionModel::GetSoundDetectionList(sound_detection_list_); 

    InitCommunityContactSwitch();

    dbinterface::PubDevMngList::GetManagementBuildingListByProjectId(mng_id_, dev_mng_unit_id_);
    dbinterface::IndoorMonitorConfig::GetIndoorMonitorConfigByProjectID(mng_id_, indoor_config_map_);

    dbinterface::AccessGroup::GetAgInfoByCommunityID(mng_id_, ag_id_map_, ag_info_mac_map_, ag_id_mac_map_);
    dbinterface::AmenityDevice::GetRemoveDefaultAccessByCommunityUUID(project_uuid, remove_access_mac_set_);
    ChangeDefaultAgMac();
    dbinterface::AccessGroup::GetPubAgAccountByCommunityID(mng_id_, ag_id_account_map_);

    dbinterface::UserAccessGroup::GetAccountAccessPerDevMapByCommunityID(mng_id_, user_access_account_mac_map_);

    dbinterface::ResidentPersonalAccount::GetCommunityAccountByProjectID(mng_id_, community_main_account_list_, community_slave_account_list_, unit_nodes_map_, community_uuid_account_list_);

    room_contorl_.Init(mng_id);
    InitPmAppList(mng_id_);

    dbinterface::ThirdPartyCamrea::GetThirdPartyCameraByProjectUUID(project_uuid, all_pub_third_camera_, 
        pub_third_camera_, unit_third_camera_, per_third_camera_, mac_third_camera_);
    
    dbinterface::AnalogDevice::GetAnalogDeviceNodeUUIDMapByProjectUUID(project_uuid, node_uuid_analog_device_list_map_);

    InitIndoorRelay();

    video_storage_config_ = VideoStorageConfigHandle(project_uuid);
}

int ConfigContext::InitPmAppList(unsigned int mng_id)
{
    if(0 != GetPersonalAccountInstance()->DaoGetPmApplistByMngID(mng_id, pm_app_list_))
    {
        AK_LOG_INFO << "On Update DevContact, init pm_app_list failed";
        return 1;
    }
    return 0;
}

const NodeAppList& ConfigContext::GetPmAppList() const 
{
    return pm_app_list_;
}

std::string ConfigContext::GetUnitName(uint32_t unit_id)
{
    auto it = units_map_.find(unit_id);
    if (it != units_map_.end())
    {
        return it->second.unit_name;
    }
    return "";
}

int ConfigContext::GetCallRule(const std::string& per_uuid, CommunityCallRuleInfo& call_rule_info)
{
    const auto& it = call_rule_map_.find(per_uuid);
    if (it != call_rule_map_.end())
    {
        call_rule_info = it->second;
        return 0;
    }
    return -1;
}

CommunityUnitInfo ConfigContext::GetUnitInfo(uint32_t unit_id)
{
    CommunityUnitInfo unit_info;
    auto it = units_map_.find(unit_id);
    if (it != units_map_.end())
    {
        unit_info = it->second;
    }
    return unit_info;
}

int ConfigContext::IsNoMonitorDev(uint32_t hw)
{
    int not_monitor = 0;
    if (no_monitor_list_.count(hw))
    {
        not_monitor = 1;
    }
    return not_monitor;
}


const PersonalAccountCnfInfo* ConfigContext::GetPeronalAccountCnf(const std::string &node) const
{
    const auto &it = node_cnf_map_ptr_->find(node);
    if (it == node_cnf_map_ptr_->end())
    {
        return nullptr; // 返回空指针表示未找到
    }
    return &(it->second); // 返回找到的对象的引用
}



DEVICE_SETTING* ConfigContext::GetNodeDeviceSettingList(const std::string& node)
{
    return dev_contorl_->GetNodeDeviceSettingList(node);
}

DEVICE_SETTING* ConfigContext::AllPublicDeviceSetting()
{
    return dev_contorl_->GetAllPubUnitDeviceInGlobal();
}

DEVICE_SETTING* ConfigContext::AllMngDeviceSetting()
{
    return dev_contorl_->GetAllMngDeviceInGlobal();
}

DEVICE_SETTING* ConfigContext::PubDeviceSetting()
{
    return dev_contorl_->GetPubDeviceInGlobal();
}

DEVICE_SETTING* ConfigContext::UnitPubDeviceSetting(uint32_t unit_id)
{
    return dev_contorl_->GetUnitDeviceInGlobal(unit_id);
}

DEVICE_SETTING* ConfigContext::UnitPubDeviceSetting(const std::string& unit_uuid)
{
    return dev_contorl_->GetUnitUUIDDeviceInGlobal(unit_uuid);
}

DEVICE_SETTING* ConfigContext::GetNodeIndoorOrMngDeviceSettingList(const std::string& node)
{
    return dev_contorl_->GetNodeIndoorOrMngDeviceSettingList(node);
}

DEVICE_SETTING* ConfigContext::GetDeviceInGlobalByUUID(const std::string& uuid)
{
    return dev_contorl_->GetDeviceInGlobalByUUID(uuid);
}

DEVICE_SETTING* ConfigContext::GetMacDeviceInGlobal(const std::string& mac)
{
    return dev_contorl_->GetMacDeviceInGlobal(mac);
}

int ConfigContext::IsCommunityContactOn()
{
    return community_contact_on_;
}


int ConfigContext::InitCommunityContactSwitch()
{
    community_contact_on_ = dbinterface::Account::GetCommunityContactSwitch(mng_id_);
    return community_contact_on_;
}

int ConfigContext::DevMngUnitID(const DEVICE_SETTING *dev, uint32_t unit_id)
{
    if (dev && dbinterface::SwitchHandle(dev->flags, DeviceSwitch::DEV_MNG_ALL))
    {
        return 1;
    }

    const auto &dev_unit_list = dev_mng_unit_id_.equal_range(dev->id);
    for (auto it = dev_unit_list.first; it != dev_unit_list.second; ++it)
    {
        if (unit_id == it->second)
        {
            AK_LOG_INFO << "mac: "<< dev->mac << " mng this unitid. unit_id=" << unit_id;
            return 1;
        }
    }
    return 0;
}

//返回1 代表管理全部
int ConfigContext::DevMngUnitListOrMngAll(const DEVICE_SETTING *dev, std::vector<uint32_t> &unit_list)
{
    if (dev && dbinterface::SwitchHandle(dev->flags, DeviceSwitch::DEV_MNG_ALL))
    {
        return 1;
    }

    const auto &dev_list = dev_mng_unit_id_.equal_range(dev->id);
    for (auto it = dev_list.first; it != dev_list.second; ++it)
    {
        unit_list.push_back(it->second);
    }
    return 0;
}


const IndoorMonitorConfigInfo* ConfigContext::GetIndoorConfig(const std::string &dev_uuid) const
{
    const auto &it = indoor_config_map_.find(dev_uuid);
    if (it == indoor_config_map_.end())
    {
        return nullptr; 
    }
    return &(it->second);    
}


bool ConfigContext::IsDefaultAccessGroup(uint32_t ag_id)
{
    const auto &it = ag_id_map_.find(ag_id);
    if (it == ag_id_map_.end())
    {
        return false;
    }
    if (it->second.is_default_)
    {
        return true;
    }
    return false;    
}

int ConfigContext::DevSchduleIDRelay(const std::string &mac, uint32_t ag_id)
{
    const auto &dev_ag_list = ag_info_mac_map_.equal_range(mac);
    for (auto it = dev_ag_list.first; it != dev_ag_list.second; ++it)
    {
        if (ag_id == it->second.id_)
        {
            return it->second.relay_;
        }
    }

    return 0;    
}

void ConfigContext::ReleaseDeviceSetting(DEVICE_SETTING *devlist)
{
    dev_contorl_->ReleaseDeviceSetting(devlist); 
}

// 社区获取app_list
int ConfigContext::GetCommunityApplistByNode(const std::string& node, std::vector<DEVICE_CONTACTLIST>& app_list)
{
    const auto &it = community_main_account_list_.find(node);
    if (it == community_main_account_list_.end())
    {
        return 0;
    }
    const ResidentPerAccount node_info = it->second;

    DEVICE_CONTACTLIST node_app;
    memset(&node_app, 0, sizeof(node_app));
    
    TransferUserInfoToDeviceContactlist(node_info, node_app);
    
    std::string sip_group = sip_contorl_->GetNodeSipGroup(node);
    Snprintf(node_app.sip_group, sizeof(node_app.sip_group), sip_group.c_str());
    
    //社区PersonalAccount RoomNumber代表room name.CommunityRoom表RoomName代表room number
    CommunityRoomInfo room_info; 
    if (0 == room_contorl_.GetNodeRoom(node, room_info))
    {
        node_app.unit_id =  node_info.unit_id;
        Snprintf(node_app.floor, sizeof(node_app.floor), room_info.floor);
        Snprintf(node_app.room_num, sizeof(node_app.room_num), room_info.room_number);
        if (strlen(node_app.floor) == 0)
        {
            //若Floor字段为空,则unit_apt中apt值为RoomName
            snprintf(node_app.unit_apt, sizeof(node_app.unit_apt), "%u-%s", node_app.unit_id, ExtractFirstNumber(node_app.room_num).c_str());
        }            
        else
        {
            //若Floor字段不为空,则unit_apt中apt值为"Floor+00"
            snprintf(node_app.unit_apt, sizeof(node_app.unit_apt), "%u-%s%s", node_app.unit_id, node_app.floor, "00");
        }
        
        Snprintf(node_app.unit_uuid, sizeof(node_app.unit_uuid), room_info.unit_uuid);
    }        
    node_app.mng_account_id = node_info.parent_id;
    app_list.push_back(node_app);


    const auto &slave_list = community_slave_account_list_.equal_range(node_info.uuid);
    for (auto it = slave_list.first; it != slave_list.second; ++it)
    {
        DEVICE_CONTACTLIST attendant_app;
        memset(&attendant_app, 0, sizeof(attendant_app));

        const ResidentPerAccount slave_info = it->second;
        attendant_app.mng_account_id = node_info.parent_id;
        TransferUserInfoToDeviceContactlist(slave_info, attendant_app);
        app_list.push_back(attendant_app);        
    }

    return 0;
}


//通过uuid获取用户的ResidentPerAccount
int ConfigContext::GetCommunityPerAccountByUUID(const std::string& uuid, ResidentPerAccount& per_account)
{
    const auto &it = community_uuid_account_list_.find(uuid);
    if (it == community_uuid_account_list_.end())
    {
        return -1;
    }
    per_account = it->second;
    return 0;
}


const std::set<std::string>& ConfigContext::GetAccountMacList(const std::string &account)
{
    const auto &it = user_devices_map_->find(account);
    if (it == user_devices_map_->end())
    {
        static const std::set<std::string> empty_set;  // 静态空集合
        return empty_set;

    }
    return it->second;
}


bool ConfigContext::AccountHaveMacPermission(const std::string &account, const std::string &mac)
{
    const auto &it = user_devices_map_->find(account);
    if (it == user_devices_map_->end())
    {
        return false;

    }
    return it->second.count(mac) > 0 ? true : false;
}

void ConfigContext::GetAgIdAccountList(uint32_t ag_id, std::set<std::string> &account)
{
    const auto &account_list = ag_id_account_map_.equal_range(ag_id);
    for (auto it = account_list.first; it != account_list.second; ++it)
    {
        account.insert(it->second);        
    }
}


int ConfigContext::GetUnitNodes(uint32_t unit_id, CommunitAccountInfoList& node_list)
{
    const auto &it = unit_nodes_map_.find(unit_id);
    if (it == unit_nodes_map_.end())
    {
        return -1;
    }
    node_list = it->second;
    return 0;
}

const MapUnitCommunitAccountList& ConfigContext::GetUnitNodesMap() const
{
    return unit_nodes_map_;
}


void ConfigContext::ChangeDefaultAgMac()
{
    DEVICE_SETTING* pub_dev = PubDeviceSetting();
    
    auto add_mac_fn = [this](uint32_t ag_id, DEVICE_SETTING *dev_list){
        DEVICE_SETTING* cur_device = dev_list;
        while (nullptr != cur_device)
        {
            if(remove_access_mac_set_.count(cur_device->mac) == 0)
            {
                ag_id_mac_map_.insert(std::make_pair(ag_id, cur_device->mac));
            }
            cur_device = cur_device->next;
        }        
    };

    for (const auto &it : ag_id_map_)
    {
        if(it.second.is_default_)
        {
            DEVICE_SETTING* pub_unit_dev = UnitPubDeviceSetting(it.second.unit_id_);
            
            add_mac_fn(it.second.id_, pub_unit_dev);
            add_mac_fn(it.second.id_, pub_dev);
            ReleaseDeviceSetting(pub_unit_dev);
        }
    }
    ReleaseDeviceSetting(pub_dev);   
}

/*公共权限组的id和mac列表    */
void ConfigContext::GetAgIdMacList(uint32_t ag_id, std::set<std::string> &macs)
{
    const auto &mac_list = ag_id_mac_map_.equal_range(ag_id);
    for (auto it = mac_list.first; it != mac_list.second; ++it)
    {
        macs.insert(it->second);        
    }
}


/*公共权限组的id和mac列表    */
void ConfigContext::GetUserAccessAccountMacList(const std::string &account, const std::string &node,  std::set<std::string> &macs)
{
    const auto &mac_list = user_access_account_mac_map_.equal_range(account);
    for (auto it = mac_list.first; it != mac_list.second; ++it)
    {
        macs.insert(it->second);        
    }
    //获取室内机和管理机
    DEVICE_SETTING* node_indoor_dev_list = GetNodeIndoorOrMngDeviceSettingList(node);
    DEVICE_SETTING* cur_device = node_indoor_dev_list;
    while (nullptr != cur_device)
    {
        macs.insert(cur_device->mac);
        cur_device = cur_device->next;
    }   
    ReleaseDeviceSetting(node_indoor_dev_list); 
}

const ThirdPartyCamreaList& ConfigContext::GetPubThirdPartyCameraList() const
{
    return pub_third_camera_;
}

const ThirdPartyCamreaList& ConfigContext::GetAllPubThirdPartyCameraList() const
{
    return all_pub_third_camera_;
}

void ConfigContext::GetUnitThirdPartyCameraList(uint32_t unit_id, ThirdPartyCamreaList &list)
{
    const auto &third_list = unit_third_camera_.equal_range(unit_id);
    for (auto it = third_list.first; it != third_list.second; ++it)
    {
        list.push_back(it->second);        
    }
}

void ConfigContext::GetNodeThirdPartyCameraList(const std::string& node, ThirdPartyCamreaList &list)
{
    const auto &third_list = per_third_camera_.equal_range(node);
    for (auto it = third_list.first; it != third_list.second; ++it)
    {
        list.push_back(it->second);
    }
}

const ThirdPartyCamreaInfo* ConfigContext::GetMacThirdPartyCamera(const std::string &mac) const
{
    const auto &it = mac_third_camera_.find(mac);
    if (it == mac_third_camera_.end())
    {
        return nullptr; // 返回空指针表示未找到
    }
    return &(it->second); // 返回找到的对象的引用    
}

void ConfigContext::GetNodeAnalogDeviceList(const std::string& node_uuid, AnalogDeviceList& analog_device_list)
{
    const auto &analog_list = node_uuid_analog_device_list_map_.equal_range(node_uuid);
    for (auto it = analog_list.first; it != analog_list.second; ++it)
    {
        analog_device_list.push_back(it->second);
    }
}

int ConfigContext::GetDevicePushButtonByDeviceUUID(const std::string& device_uuid, DevicePushButton& dev_push_button)
{
    if (0 != push_button_control_->GetDevicePushButtonByDeviceUUID(device_uuid, dev_push_button))
    {
        return -1;
    }
    return 0;
}

void ConfigContext::GetDevicePushButtonListByDeviceUUIDAndModule(const std::string& device_uuid, int module, std::vector<DevicePushButtonListInfo>& module_sequence)
{
    push_button_control_->GetDevicePushButtonListByDeviceUUIDAndModule(device_uuid, module, module_sequence);
    return;
}

void ConfigContext::InitPushButton(const std::string& community_uuid)
{
    push_button_control_->InitPushButton(community_uuid);
    return;
}


int ConfigContext::IsPackageDetectionDev(uint32_t hw)
{
    int is_package_detection = 0;
    if (package_detection_list_.count(hw))
    {
        is_package_detection = 1;
    }
    return is_package_detection;
}

// int ConfigContext::IsSoundDetectionDev(uint32_t hw)
// {
//     int is_sound_detection = 0;
//     if (sound_detection_list_.count(hw))
//     {
//         is_sound_detection = 1;
//     }
//     return is_sound_detection;
// }

void ConfigContext::InitIndoorRelay()
{
    for (const auto& indoor_config_pair : indoor_config_map_)
    {
        const IndoorMonitorConfigInfo& indoor_config = indoor_config_pair.second;
        
        // 跳过未开启外接继电器的设备
        if (indoor_config.ex_relay_switch != 1) 
        {
            continue;
        }
        
        GetIndoorRelayInfo(indoor_config);
    }
    
    AK_LOG_INFO << "Initialized relay configurations for " << extra_device_relay_config_map_.size() << " devices";
}

void ConfigContext::GetIndoorRelayInfo(const IndoorMonitorConfigInfo& indoor_config)
{
    ExtraDeviceInfoList extra_devices;
    if (0 != dbinterface::ExtraDevice::GetExtraDevicesByIndoorConfigUUID(indoor_config.uuid, extra_devices))
    {
        AK_LOG_WARN << "Failed to get extra devices for indoor config: " << indoor_config.uuid;
        return;
    }
    
    for (const auto& device : extra_devices) 
    {
        InitExtraDeviceRelay(indoor_config, device);
    }
}

void ConfigContext::InitExtraDeviceRelay(const IndoorMonitorConfigInfo& indoor_config, const ExtraDeviceInfo& device)
{
    ExtraDeviceRelay device_config;
    device_config.extra_device_ = device;
    
    // 如果设备未启用，仍然保存设备信息但跳过relay配置加载
    if (device.enable_switch != 1)  
    {
        extra_device_relay_config_map_.insert({indoor_config.uuid, device_config});
        return;
    }
    
    // 加载设备的relay配置
    std::vector<std::string> all_relay_list_uuids;
    if (0 != dbinterface::ExtraDeviceRelayList::GetRelayListByExtraDevice(device, all_relay_list_uuids, device_config.relay_list_))
    {
        AK_LOG_WARN << "Failed to get relay list for device " << device.uuid;
        return;
    }
    
    if (0 != dbinterface::ExtraDeviceRelayAction::GetRelayActionsByRelayList(all_relay_list_uuids, device_config.relay_uuid_to_actions_map_))
    {
        AK_LOG_WARN << "Failed to get relay actions for device " << device.uuid;
        return;
    }
    
    // 将extra_device配置添加到map中
    extra_device_relay_config_map_.insert({indoor_config.uuid, device_config});
    
    AK_LOG_INFO << "Loaded relay config for device index " << device.device_index
               << " in indoor config: " << indoor_config.uuid
               << ", relays=" << device_config.relay_list_.size()
               << ", actions=" << device_config.relay_uuid_to_actions_map_.size();
}

void ConfigContext::GetAllExtraDeviceRelayConfigs(const std::string& indoor_monitor_config_uuid, ExtraDeviceRelayList& extra_device_infos) const
{
    auto range = extra_device_relay_config_map_.equal_range(indoor_monitor_config_uuid);
    for (auto it = range.first; it != range.second; ++it) 
    {
        extra_device_infos.push_back(it->second);
    }
}


