#ifndef __COMM_CONFIG_UPDATE_CONTEXT__
#define __COMM_CONFIG_UPDATE_CONTEXT__

#include "AKCSMsg.h"
#include "BasicDefine.h"
#include "CommunityMng.h"
#include "DeviceControl.h"
#include "PersonalAccount.h"
#include "VideoStorageConfig.h"
#include "DeviceExternPushButton.h"
#include "DeviceExternPushButton.h"
#include "CommConfigHandleDevices.h"
#include "dbinterface/VersionModel.h"
#include "dbinterface/AnalogDevice.h"
#include "dbinterface/CommunityUnit.h"
#include "dbinterface/PubDevMngList.h"
#include "dbinterface/AccessGroupDB.h"
#include "dbinterface/CommunityRoom.h"
#include "dbinterface/UserAccessGroup.h"
#include "dbinterface/ThirdPartyCamera.h"
#include "dbinterface/ExternPushButton.h"
#include "dbinterface/PersonalAccountCnf.h"
#include "dbinterface/resident/AmenityDevice.h"
#include "dbinterface/resident/IndoorMonitorConfig.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/CommunityCallRule.h"
#include "dbinterface/resident/ExtraDevice.h"
#include "dbinterface/resident/ExtraDeviceRelayList.h"
#include "dbinterface/resident/ExtraDeviceRelayAction.h"
#include "Model/ExtraDeviceRelayConfig.h"


class CommConfigHandleDevices;
class DevConfig;

class ConfigContext
{
public:
    ConfigContext();
    ~ConfigContext(){}
    void Init(uint32_t mng_id, const std::string &project_uuid); 
    void SetDevContorl(CommConfigHandleDevices *dev_contorl,     PersonalAccountCnfInfoMapPtr &cnf, SipContorlPtr &sip_contorl)
    {
        dev_contorl_ = dev_contorl;
        node_cnf_map_ptr_ = cnf;
        sip_contorl_ = sip_contorl;
    }

    // 声明友元类，使其能够访问private成员
    friend class DevConfig;

    /*设备相关*/
    int IsNoMonitorDev(uint32_t hw);
    int IsPackageDetectionDev(uint32_t hw);
    // int IsSoundDetectionDev(uint32_t hw);
    const IndoorMonitorConfigInfo* GetIndoorConfig(const std::string &dev_uuid) const ;
    DEVICE_SETTING* GetNodeDeviceSettingList(const std :: string & node);
    DEVICE_SETTING* GetNodeIndoorOrMngDeviceSettingList(const std::string& node);    
    DEVICE_SETTING* AllPublicDeviceSetting();
    DEVICE_SETTING* AllMngDeviceSetting();
    DEVICE_SETTING* PubDeviceSetting();
    DEVICE_SETTING* UnitPubDeviceSetting(uint32_t unit_id);
    DEVICE_SETTING* UnitPubDeviceSetting(const std::string& unit_uuid);
    int DevMngUnitID(const DEVICE_SETTING *dev, uint32_t unit_id);
    int DevMngUnitListOrMngAll(const DEVICE_SETTING *dev, std::vector<uint32_t> &unit_list);  
    void ReleaseDeviceSetting(DEVICE_SETTING *devlist);
    int DevSchduleIDRelay(const std::string &mac, uint32_t ag_id);
    const std::set<std::string>& GetAccountMacList(const std::string &account);
    bool AccountHaveMacPermission(const std::string &account, const std::string &mac);

    void GetUserAccessAccountMacList(const std::string &account, const std::string &node,  std::set<std::string> &macs);

     /*项目相关*/
    int IsCommunityContactOn();
    std::string GetUnitName(uint32_t unit_id);    
    int GetCallRule(const std::string& per_uuid, CommunityCallRuleInfo& call_rule_info);
    CommunityUnitInfo GetUnitInfo(uint32_t unit_id);
    
    /*用户相关*/    
    const PersonalAccountCnfInfo* GetPeronalAccountCnf(const std::string &node) const;
    
    bool IsDefaultAccessGroup(uint32_t ag_id);
    int GetCommunityApplistByNode(const std::string& node, std::vector<DEVICE_CONTACTLIST>& app_list);
    int GetUnitNodes(uint32_t unit_id, CommunitAccountInfoList& node_list);
    const MapUnitCommunitAccountList& GetUnitNodesMap() const;
    void GetAgIdAccountList(uint32_t ag_id, std::set<std::string> &account);

    void GetAgIdMacList(uint32_t ag_id, std::set<std::string> &macs);
    const NodeAppList& GetPmAppList() const ;
    int SetUserDevicesMap(MapUserAGDeviceListPtr user_devices_map) {
        user_devices_map_ = user_devices_map;
        return 0;
    }

    //三方设备
    const ThirdPartyCamreaList& GetPubThirdPartyCameraList() const;
    const ThirdPartyCamreaList& GetAllPubThirdPartyCameraList() const;
    void GetUnitThirdPartyCameraList(uint32_t unit_id, ThirdPartyCamreaList &list);
    void GetNodeThirdPartyCameraList(const std::string& node, ThirdPartyCamreaList &list);
    const ThirdPartyCamreaInfo* GetMacThirdPartyCamera(const std::string &mac) const;

    //通过uuid获取用户、设备的缓存信息
    int GetCommunityPerAccountByUUID(const std::string& uuid, ResidentPerAccount& per_account);
    DEVICE_SETTING* GetDeviceInGlobalByUUID(const std::string& uuid);
    DEVICE_SETTING* GetMacDeviceInGlobal(const std::string& mac);

    //外接板按键
    int GetDevicePushButtonByDeviceUUID(const std::string& device_uuid, DevicePushButton& dev_push_button);
    void GetDevicePushButtonListByDeviceUUIDAndModule(const std::string& device_uuid, int module, std::vector<DevicePushButtonListInfo>& module_sequence);
    void InitPushButton(const std::string& community_id);
    //模拟手柄相关
    void GetNodeAnalogDeviceList(const std::string& node_uuid, AnalogDeviceList& analog_device_list);

    // ExtraRelay 相关
    void GetAllExtraDeviceRelayConfigs(const std::string& indoor_monitor_config_uuid, ExtraDeviceRelayList& extra_device_infos) const;

    VideoStorageConfigHandle& GetVideoStorageConfig() { return video_storage_config_; }
private:
    void ChangeDefaultAgMac();
    int InitPmAppList(unsigned int mng_id);
    int InitCommunityContactSwitch();
    void InitIndoorRelay();
    void GetIndoorRelayInfo(const IndoorMonitorConfigInfo& indoor_config);
    void InitExtraDeviceRelay(const IndoorMonitorConfigInfo& indoor_config, const ExtraDeviceInfo& device);

    CommConfigHandleDevices* dev_contorl_;

    SipContorlPtr sip_contorl_;
    CommunityRoomContorl room_contorl_;
    DeviceExternPushButtonPtr push_button_control_;
    
    
    uint32_t mng_id_;
    CommunityUnitMap units_map_;
    CommunityCallRuleInfoMap call_rule_map_;
        
    NodeAppList pm_app_list_;
    FirmwareList no_monitor_list_;
    FirmwareList package_detection_list_;
    // FirmwareList sound_detection_list_;
    PersonalAccountCnfInfoMapPtr node_cnf_map_ptr_;
    
    ManagementBuildingMap dev_mng_unit_id_;
    int community_contact_on_;
    IndoorConfigMap indoor_config_map_;
    AccessGroupIDMap ag_id_map_;
    AccessGroupMacMap ag_info_mac_map_; //这个包含默认权限组
    AccessGroupIDAccountMap ag_id_account_map_;
    AccessGroupIDMacMap ag_id_mac_map_;

    UserAccessAccountMacMap user_access_account_mac_map_;

    ResidentPerAccountMap community_main_account_list_;
    ResidentPerAccountSlaveMap community_slave_account_list_;
    MapUnitCommunitAccountList unit_nodes_map_;
    ResidentUUIDPerAccountMap community_uuid_account_list_; // <uuid,per_account>

    MapUserAGDeviceListPtr user_devices_map_;
    AmenityDeviceRemoveAccessMacSet remove_access_mac_set_;

    ThirdPartyCamreaList all_pub_third_camera_;
    ThirdPartyCamreaList pub_third_camera_;
    ThirdPartyCamreaUnitMap unit_third_camera_; 
    ThirdPartyCamreaPerMap per_third_camera_;   
    ThirdPartyCamreaMacMap mac_third_camera_;

    AnalogDeviceNodeUuidMap node_uuid_analog_device_list_map_;
    
    ExtraDeviceRelayConfigMap extra_device_relay_config_map_;
    
    VideoStorageConfigHandle video_storage_config_;
};


typedef std::shared_ptr<ConfigContext> ConfigContextPtr;

#endif


