#include "VideoStorageConfig.h"

VideoStorageConfigHandle::VideoStorageConfigHandle(const std::string& project_uuid)
{
    dbinterface::VideoStorage::GetVideoStorageByAccountUUID(project_uuid, video_storage_info_);
    dbinterface::VideoStorageDevice::GetVideoStorageDevicesListByProjectUUID(project_uuid, video_storage_dev_list_);
}

bool VideoStorageConfigHandle::EnableVideoStorage()
{
    return video_storage_info_.is_enable == 1;
}

bool VideoStorageConfigHandle::EnableVideoStorageCallAudio()
{
    return video_storage_info_.is_enable_call_audio == 1;
}

bool VideoStorageConfigHandle::IsVideoStorageDevices(const std::string& dev_uuid)
{
    for (const auto& video_storage_dev : video_storage_dev_list_)
    {
        if (strncmp(video_storage_dev.devices_uuid, dev_uuid.c_str(), strlen(video_storage_dev.devices_uuid)) == 0)
        {
            return true;
        }
    }
    return false;
}