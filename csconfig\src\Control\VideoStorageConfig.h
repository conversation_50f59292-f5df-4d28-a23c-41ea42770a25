#ifndef __VIDEO_STORAGE_CONFIG_HANDLE_H__
#define __VIDEO_STORAGE_CONFIG_HANDLE_H__

#include "dbinterface/VideoStorage.h"
#include "dbinterface/VideoStorageDevice.h"

class VideoStorageConfigHandle
{
public:
    VideoStorageConfigHandle() {}
    ~VideoStorageConfigHandle() {}
    VideoStorageConfigHandle(const std::string& project_uuid);

    bool EnableVideoStorage();
    bool EnableVideoStorageCallAudio();
    bool IsVideoStorageDevices(const std::string& dev_uuid);
private:
    VideoStorageInfo video_storage_info_;
    VideoStorageDeviceInfoList video_storage_dev_list_;
};


#endif