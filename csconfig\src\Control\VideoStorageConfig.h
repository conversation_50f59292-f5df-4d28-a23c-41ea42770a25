#ifndef __VIDEO_STORAGE_CONFIG_HANDLE_H__
#define __VIDEO_STORAGE_CONFIG_HANDLE_H__

#include "AkcsCommonSt.h"
#include "dbinterface/VideoStorage.h"
#include "dbinterface/VideoStorageDevice.h"

class VideoStorageConfigHandle
{
public:
    VideoStorageConfigHandle() {}
    ~VideoStorageConfigHandle() {}
    VideoStorageConfigHandle(const std::string& project_uuid);

    void InitVideoStorageInfoMap();
    void InitVideoStorageDevicesList();
    bool IsVideoStorageDevices(const std::string& dev_uuid);
    bool GetVideoStorageInfo(const DEVICE_SETTING* dev, VideoStorageInfo& video_storage_info);

private:
    VideoStorageInfoList video_storage_info_list_;
    VideoStorageDeviceInfoList video_storage_dev_list_;
    std::set<std::string> video_storage_devices_uuid_list_;
    std::map<std::string, VideoStorageInfo> video_storage_info_map_;
};


#endif