#ifndef __WIRETE_DEV_WROK_POOL_H__
#define __WIRETE_DEV_WROK_POOL_H__

#include <memory>
#include "AkcsWorkPool.h"
#include "Singleton.h"
#include "AkLogging.h"
#include <mutex>



class WriteDevWorkPoolControl
{
public:
    friend class AKCS::Singleton<WriteDevWorkPoolControl>;            
    void Init(int thread_num)
    {
        pool_ = std::make_shared<AkcsWorkThreadPool>(thread_num);
    }
    
public:
    std::shared_ptr<AkcsWorkThreadPool> pool_;
    std::mutex mutex_;
};


class WorkTask
{
public:
    WorkTask(){}
    ~WorkTask(){}

    template <typename F>
    void AddWork(F&& func) {
    
        std::lock_guard<std::mutex> lock(AKCS::Singleton<WriteDevWorkPoolControl>::instance().mutex_);
        auto& pool = AKCS::Singleton<WriteDevWorkPoolControl>::instance().pool_;
        if (pool) {
            results_.emplace_back(pool->enqueue(std::forward<F>(func)));
        }
    }

    void WaitWork()
    {
        for(auto && result: results_)
        {
            AK_LOG_INFO << "work end. ret: " << result.get();
        };        
    }
private:
    std::vector< std::future<std::string> > results_;

};


#endif

