#include <stdio.h>
#include <stdlib.h>
#include <errno.h>
#include <assert.h>
#include <string.h>
#include <ctype.h>
#include <string>
#include <list>
#include <sstream>
#include "WriteFileControl.h"
#include <thread>
#include "util.h"
#include "AkLogging.h"
#include "util_cstring.h"
#include "DeviceSetting.h"
#include "AkcsWebMsgSt.h"
#include "AES256.h"
#include "AkcsMonitor.h"
#include "encrypt/Md5.h"
#include "AkcsCommonDef.h"
#include "ShadowMng.h"
#include "ConfigDef.h" 
#include "dbinterface/office/OfficeDevices.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/Shadow.h"
#include "AKCSView.h"
#include "OfficeUnixSocketControl.h"

extern CSCONFIG_CONF gstCSCONFIGConf;


WriteFileControl* WriteFileControl::instance = nullptr;
thread_local std::unique_ptr<CShadowMng> WriteFileControl::thread_shadow_mng = nullptr;

WriteFileControl* GetWriteFileControlInstance()
{
    return WriteFileControl::GetInstance();
}

WriteFileControl::WriteFileControl()
{
    #ifdef AKCS_ENABLE_WRITE_FILE_THREAD
    write_thread_number_ = gstCSCONFIGConf.write_file_number;
    eque_.resize(write_thread_number_);
    for (int i = 0; i < write_thread_number_; ++i)
    {
        std::thread t = std::thread(WriteFileThread, i);
        t.detach();
    }
    #endif

}

WriteFileControl::~WriteFileControl()
{

}

#ifdef AKCS_ENABLE_WRITE_FILE_THREAD
void WriteFileControl::ThreadHandle(int id, std::unique_ptr<CShadowMng>& shadow_mng)
{
    std::deque<DevFileInfoPtr> tmp_deque;
    {
        std::lock_guard<std::mutex> lock_(mutex_);
        if (eque_[id].size() == 0)
        {
           return;
        }        
        eque_[id].swap(tmp_deque);
    }

    if (tmp_deque.size() > 500)
    {
        AK_LOG_INFO << "Write file queue size:" << tmp_deque.size();    
        std::stringstream err;
        err << "Write file queue size: " << tmp_deque.size() <<  ", Temporary use of alarm ID: AKCS_MONITOR_ALARM_CONFIG_WRITE_FAILED";
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csconfig", err.str(), AKCS_MONITOR_ALARM_CONFIG_WRITE_FAILED);
    }
    
    std::set<std::string> notify_mac_list;
    std::set<std::string> office_notify_mac_list;
    DevFileInfoPtr fileinfo;
    while (tmp_deque.size() > 0)
    {
        fileinfo = tmp_deque.front();
        tmp_deque.pop_front();

        //取值
        std::string filepath = fileinfo->filepath_;
        std::string content= fileinfo->content_;
        std::string mac= fileinfo->mac_;
        SHADOW_TYPE file_type = fileinfo->file_type_;
        int project_type = fileinfo->project_type_;
        uint32_t id = fileinfo->table_id_;
        ThreadLocalSingleton::GetInstance().SetTraceID(STOULL(fileinfo->trace_id_));
        //文件操作
        FILE* pfile = fopen(filepath.c_str(), "w+");
        if (pfile == NULL)
        {
            AK_LOG_WARN << "fopen  failed " << filepath;
            AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csconfig", filepath, AKCS_MONITOR_ALARM_CONFIG_WRITE_FAILED);
            continue;
        }
        
        fwrite(content.c_str(), sizeof(char), strlen(content.c_str()), pfile);
        fclose(pfile);
 
        //加密
        if (!gstCSCONFIGConf.no_encrypt)
        {
            FileAESEncrypt(filepath.c_str(), AES_ENCRYPT_KEY_V1, filepath.c_str());
        }

        shadow_mng->StoreDevShadow(filepath.c_str(), mac, file_type);
        std::string md5 = akuvox_encrypt::MD5::GetFileMD5(filepath);    
        
        AK_LOG_INFO << "The file path is " << filepath << " md5:" << md5;

        //更新数据库值
        if (project_type == project::PERSONAL)
        {
            dbinterface::ResidentPerDevices::UpdateMd5ByID(id, file_type, md5);
            notify_mac_list.insert(mac);
        }
        else if (project_type == project::RESIDENCE)
        {
            dbinterface::ResidentDevices::UpdateMd5ByID(id, file_type, md5);
            notify_mac_list.insert(mac);
        }
        else if (project_type == project::OFFICE)
        {
            dbinterface::OfficeDevices::UpdateMd5ByID(id, file_type, md5);
            office_notify_mac_list.insert(mac);
        }        
    }

    //TODO：需不需要通知可以判断md5前后是否一致
    for (auto &mac : notify_mac_list)
    {
        GetAKCSViewInstance()->NotifyMacChange(mac);
        AK_LOG_INFO << "Notify mac: " << mac;
    } 
    for (auto &mac : office_notify_mac_list)
    {      
        OfficeUnixMsgControl::Instance()->NotifyMacChange(mac);
        AK_LOG_INFO << "Notify office mac: " << mac;
    }

}

int WriteFileControl::WriteFileThread(int id)
{
    AK_LOG_INFO << "Create Write file thread:" << std::this_thread::get_id() << " id:" << id; 
    std::unique_ptr<CShadowMng> shadow_mng(new CShadowMng());
    while (true)
    {
        GetWriteFileControlInstance()->ThreadHandle(id, shadow_mng);
        sleep(1);
    }
}
#else
void WriteFileControl::WriteFileHandle(const DevFileInfoPtr &fileinfo)
{
    std::set<std::string> notify_mac_list;
    std::set<std::string> office_notify_mac_list;

    //取值
    std::string filepath = fileinfo->filepath_;
    std::string content= fileinfo->content_;
    std::string mac= fileinfo->mac_;
    SHADOW_TYPE file_type = fileinfo->file_type_;
    int project_type = fileinfo->project_type_;
    uint32_t id = fileinfo->table_id_;
    ThreadLocalSingleton::GetInstance().SetTraceID(STOULL(fileinfo->trace_id_));
    //文件操作
    FILE* pfile = fopen(filepath.c_str(), "w+");
    if (pfile == NULL)
    {
        AK_LOG_WARN << "fopen  failed " << filepath;
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csconfig", filepath, AKCS_MONITOR_ALARM_CONFIG_WRITE_FAILED);
        return;
    }
    
    fwrite(content.c_str(), sizeof(char), strlen(content.c_str()), pfile);
    fclose(pfile);

    //加密
    if (!gstCSCONFIGConf.no_encrypt)
    {
        FileAESEncrypt(filepath.c_str(), AES_ENCRYPT_KEY_V1, filepath.c_str());
    }

    if(thread_shadow_mng == nullptr)
    {
        thread_shadow_mng.reset(new CShadowMng());
    }
    thread_shadow_mng->StoreDevShadow(filepath.c_str(), mac, file_type);
    std::string md5 = akuvox_encrypt::MD5::GetFileMD5(filepath);    
    
    AK_LOG_INFO << "The file path is " << filepath << " md5:" << md5;

    //更新数据库值
    if (project_type == project::PERSONAL)
    {
        dbinterface::ResidentPerDevices::UpdateMd5ByID(id, file_type, md5);
        notify_mac_list.insert(mac);
    }
    else if (project_type == project::RESIDENCE)
    {
        dbinterface::ResidentDevices::UpdateMd5ByID(id, file_type, md5);
        notify_mac_list.insert(mac);
    }
    else if (project_type == project::OFFICE)
    {
        dbinterface::OfficeDevices::UpdateMd5ByID(id, file_type, md5);
        office_notify_mac_list.insert(mac);
    }        

    //TODO：需不需要通知可以判断md5前后是否一致
    for (auto &mac : notify_mac_list)
    {
        GetAKCSViewInstance()->NotifyMacChange(mac);
        AK_LOG_INFO << "Notify mac: " << mac;
    } 
    for (auto &mac : office_notify_mac_list)
    {      
        OfficeUnixMsgControl::Instance()->NotifyMacChange(mac);
        AK_LOG_INFO << "Notify office mac: " << mac;
    }    
}
#endif

WriteFileControl* WriteFileControl::GetInstance()
{
    if (instance == nullptr)
    {
        instance = new WriteFileControl();
    }

    return instance;
}

void WriteFileControl::AddFileInfo(const std::string &mac, const DevFileInfoPtr &info)
{
    if(ThreadLocalSingleton::GetInstance().GetDbStatus())
    {
        AK_LOG_INFO << "AddFileInfo mac:" << mac << " file: " << info->filepath_; 
        #ifdef AKCS_ENABLE_WRITE_FILE_THREAD
            int id = StrHash(mac, write_thread_number_);
            std::lock_guard<std::mutex> lock_(mutex_);
            eque_[id].push_back(info);
        #else
            WriteFileHandle(info);
        #endif
    }
    //db异常状态下，不写不下发新的配置文件给设备，避免异常
    else
    {
        AK_LOG_ERROR << "Alarm Monitoring: Db error. Pause AddFileInfo mac:" << mac << " file: " << info->filepath_; 
    }
    
}



