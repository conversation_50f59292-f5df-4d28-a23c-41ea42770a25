#ifndef __WIRETE_FILE_CONTROL_H__
#define __WIRETE_FILE_CONTROL_H__

#include "BasicDefine.h"
#include "AKCSMsg.h"
#include <mutex>
#include <vector>
#include <deque>
#include "ShadowMng.h"
#include "ThreadLocalSingleton.h"

#ifndef AKCS_ENABLE_WRITE_FILE_THREAD
/*
name:czw
date:20250604
note:写文件是否投递到专门得线程处理；当前关闭写文件线程，写文件操作直接处理
v7.1.4暂时用宏定义保留代码，避免上线后异常方便回退
*/
//#define AKCS_ENABLE_WRITE_FILE_THREAD
#endif

class DevFileInfo
{
public:
    DevFileInfo(const std::string &mac, const std::string &filepath, const std::string &content,
        SHADOW_TYPE file_type, int project_type, int id)
        :mac_(mac),content_(content),filepath_(filepath),
        file_type_(file_type),project_type_(project_type),
        table_id_(id)
    {
       trace_id_ = std::to_string(ThreadLocalSingleton::GetInstance().GetTraceID()); 
    }   
    ~DevFileInfo(){};
public:
    std::string mac_;
    std::string filepath_;
    std::string content_;
    SHADOW_TYPE file_type_;
    int project_type_;
    uint32_t table_id_;    
    std::string trace_id_;
};

typedef std::shared_ptr<DevFileInfo> DevFileInfoPtr;


class WriteFileControl
{
public:
    WriteFileControl();
    ~WriteFileControl();
    static WriteFileControl* GetInstance();
    
    void AddFileInfo(const std::string &mac, const DevFileInfoPtr &info);
    void WriteFileHandle(const DevFileInfoPtr &fileinfo);
    
    static int WriteFileThread(int id);
private:
    void ThreadHandle(int, std::unique_ptr<CShadowMng>& shadow_mng);
    static WriteFileControl* instance;
    std::mutex mutex_;
    std::vector<std::deque<DevFileInfoPtr>> eque_;
    int write_thread_number_ = 2;
    static thread_local std::unique_ptr<CShadowMng> thread_shadow_mng;
};

WriteFileControl* GetWriteFileControlInstance();


#endif

