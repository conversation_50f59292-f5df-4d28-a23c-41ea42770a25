#include "AnalogDeviceHandler.h"
#include "dbinterface/AnalogDevice.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "ContactCommon.h"
#include "util_relay.h"
#include "util_time.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "AkLogging.h"
#include "dbinterface/CommunityUnit.h"
#include <climits>
#include "UpdateConfigContext.h"

void AnalogDeviceHandler::WriteHandleInfoCommonStr(std::stringstream &str, const HandleInfoKVList &kv)
{
    for (const auto& pair : kv)
    {
        char value[256] = "";
        ChangeSpecialXmlChar(value, 256, pair.second.c_str(), pair.second.size());

        switch(pair.first)
        {
            case (int)HANDLE_INFO_ATTR::UID :
                str << " UID=\""<< value << "\"";
                break;        
            case (int)HANDLE_INFO_ATTR::ANALOG_SYSTEM :
                str << " AnalogSystem=\""<< value << "\"";
                break;
            case (int)HANDLE_INFO_ATTR::ANALOG_NUMBER :
                str << " AnalogNumber=\""<< value << "\"";
                break;
            case (int)HANDLE_INFO_ATTR::ANALOG_MODE :
                str << " AnalogMode=\""<< value << "\"";
                break;
            case (int)HANDLE_INFO_ATTR::ANALOG_PROXY_ADDR :
                str << " AnalogProxyAddress=\""<< value << "\"";
                break;
            case (int)HANDLE_INFO_ATTR::ANALOG_DTMF_CODE :
                str << " DTMFCode=\""<< value << "\"";
                break;
        }
    }
}

void AnalogDeviceHandler::WriteHandleInfoStr(std::stringstream &str, const HandleInfoKVList &kv)
{
    str << "<HandleInfo ";
    WriteHandleInfoCommonStr(str, kv);
    str << "/>\n";
}

void AnalogDeviceHandler::WriteDTMFConfigCommonStr(std::stringstream &str, const DTMFConfigKVList &kv)
{
    for (const auto& pair : kv)
    {
        char value[256] = "";
        ChangeSpecialXmlChar(value, 256, pair.second.c_str(), pair.second.size());

        switch(pair.first)
        {
            case (int)DTMF_CONFIG_ATTR::MAC :
                str << " MAC=\""<< value << "\"";
                break;        
            case (int)DTMF_CONFIG_ATTR::IP :
                str << " IP=\""<< value << "\"";
                break;
            case (int)DTMF_CONFIG_ATTR::RELAY_DTMF :
                str << " RelayDTMF=\""<< value << "\"";
                break;
            case (int)DTMF_CONFIG_ATTR::SE_RELAY_DTMF:
                str << " SecurityRelayDTMF=\"" << value << "\"";
                break;
        }
    }
}

void AnalogDeviceHandler::WriteDTMFConfigStr(std::stringstream &str, const DTMFConfigKVList &kv)
{
    str << "<DTMFConfig ";
    WriteDTMFConfigCommonStr(str, kv);
    str << "/>\n";
}

void AnalogDeviceHandler::WriteNodeAnalogDeviceContactStrAndSave(std::stringstream& contact_str, const std::string& node_uuid, int call_type)
{
    if (!is_support_analog_device_)
    {
        return;
    }

    AnalogDeviceList analog_device_list;
    context_->GetNodeAnalogDeviceList(node_uuid, analog_device_list);

    for (auto& analog_device_info : analog_device_list)
    {
        //UID=HS+数据库ID，用于关联
        Snprintf(analog_device_info.uid, sizeof(analog_device_info.uid), GetAnalogDeviceUID(analog_device_info.id).c_str());

        ContactKvList kv;
        kv.push_back(std::make_pair((int)CONTACT_ATTR::NAME, analog_device_info.analog_device_name));
        kv.push_back(std::make_pair((int)CONTACT_ATTR::UID, analog_device_info.uid)); 
        kv.push_back(std::make_pair((int)CONTACT_ATTR::TYPE, std::to_string(DEVICE_TYPE_ANALOG_HANDLE)));
        kv.push_back(std::make_pair((int)CONTACT_ATTR::SEQ, GetCallSeqByCallType(call_type)));
        GetContactStr(contact_str, kv);

        //存当前手柄序列，后续统一下发手柄信息
        analog_device_list_.push_back(analog_device_info);
    }
}

std::string AnalogDeviceHandler::GetCallSeqByCallType(int call_type)
{
    //模拟手柄按照室内机处理，以下四种call_type都是先呼室内机 "2"跟设备端约定是找模拟手柄的UID
    if (call_type == NODE_CALL_TYPE_INDOOR_PHONE
        || call_type == NODE_CALL_TYPE_INDOOR_BACK_APP
        || call_type == NODE_CALL_TYPE_INDOOR_BACK_PHONE
        || call_type == NODE_CALL_TYPE_INDOOR_BACK_APP_BACK_PHONE)
    {
        return " seq=\"1-2\" ";
    }

    return "";
}

void AnalogDeviceHandler::WriteAnalogDeviceInfoStr(std::stringstream &config_body)
{
    if (!is_support_analog_device_)
    {
        return;
    }

    if (analog_device_list_.size() == 0)
    {
        return;
    }

    config_body << "<SimulationHandle>\n";

    int analog_mode = GetAnalogMode();

    for (const auto& analog_device_info : analog_device_list_)
    {
        std::string analog_proxy_address; //只有代理模式时需要设置
        if (analog_mode == (int)AnalogMode::ANALOG_MODE_PROXY)
        {
            analog_proxy_address = GetAnalogProxyIPAddressByCommunityUnitUUID(analog_device_info.community_unit_uuid);
        }

        HandleInfoKVList kv;
        kv.push_back(std::make_pair((int)HANDLE_INFO_ATTR::UID, analog_device_info.uid));
        kv.push_back(std::make_pair((int)HANDLE_INFO_ATTR::ANALOG_SYSTEM, "1")); 
        kv.push_back(std::make_pair((int)HANDLE_INFO_ATTR::ANALOG_NUMBER, analog_device_info.analog_device_number));
        kv.push_back(std::make_pair((int)HANDLE_INFO_ATTR::ANALOG_MODE, std::to_string(analog_mode)));
        kv.push_back(std::make_pair((int)HANDLE_INFO_ATTR::ANALOG_PROXY_ADDR, analog_proxy_address));
        kv.push_back(std::make_pair((int)HANDLE_INFO_ATTR::ANALOG_DTMF_CODE, analog_device_info.dtmf_code));

        WriteHandleInfoStr(config_body, kv);
    }

    config_body << "</SimulationHandle>\n";
}

int AnalogDeviceHandler::GetAnalogMode()
{
    if (dev_grade_ == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
    {
        return (int)AnalogMode::ANALOG_MODE_PROXY;
    }
    else if (dev_grade_ == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT ||
                dev_grade_ == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL) 
    {
        return (int)AnalogMode::ANALOG_MODE_DIRECT;
    }
    return 0;
}

std::string AnalogDeviceHandler::GetAnalogProxyIPAddressByCommunityUnitUUID(const std::string& unit_uuid)
{
    DEVICE_SETTING* unit_dev_list = context_->UnitPubDeviceSetting(unit_uuid);
    auto unit_dev = unit_dev_list;
    //获取该楼栋下最早创建的S532设备ip地址
    int first_timestamp = INT_MAX;
    std::string ip_addr;
    while(unit_dev)
    {
        if (!SwitchHandle(unit_dev->fun_bit, FUNC_DEV_SUPPORT_ANALOG_HANDLE))
        {
            unit_dev = unit_dev->next;
            continue;
        }
        int dev_create_time = StandardTimeToStamp(unit_dev->create_time);
        if (dev_create_time < first_timestamp)
        {
            ip_addr = unit_dev->ip_addr;
            first_timestamp = dev_create_time;
            AK_LOG_INFO << "dev create time:" << dev_create_time << "; ip address:" << ip_addr;
        }
        unit_dev = unit_dev->next;
    }

    context_->ReleaseDeviceSetting(unit_dev_list);

    return ip_addr;
}

void AnalogDeviceHandler::WriteAnalogDeviceDTMFConfigStr(std::stringstream &config_body)
{
    if (!is_support_analog_device_)
    {
        return;
    }

    //只给楼栋公共设备下发最外围设备的ip和DTMF
    if (dev_grade_ != csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
        return;
    }

    config_body << "<HSDTMF>\n";

    //获取最外围支持模拟手柄的设备列表
    auto pub_dev_list = context_->PubDeviceSetting();
    auto pub_dev = pub_dev_list;
    while (pub_dev)
    {
        if (!SwitchHandle(pub_dev->fun_bit, FUNC_DEV_SUPPORT_ANALOG_HANDLE))
        {
            pub_dev = pub_dev->next;
            continue;
        }
        std::string relay_dtmf = GetRelayDtmfPairStr(pub_dev->relay);
        std::string security_relay_dtmf = GetRelayDtmfPairStr(pub_dev->security_relay);

        DTMFConfigKVList kv;
        kv.push_back(std::make_pair((int)DTMF_CONFIG_ATTR::MAC, pub_dev->mac));
        kv.push_back(std::make_pair((int)DTMF_CONFIG_ATTR::IP, pub_dev->ip_addr));
        kv.push_back(std::make_pair((int)DTMF_CONFIG_ATTR::RELAY_DTMF, relay_dtmf));
        kv.push_back(std::make_pair((int)DTMF_CONFIG_ATTR::SE_RELAY_DTMF, security_relay_dtmf));
        WriteDTMFConfigStr(config_body, kv);

        pub_dev = pub_dev->next;
    }

    context_->ReleaseDeviceSetting(pub_dev_list);

    config_body << "</HSDTMF>\n";
}