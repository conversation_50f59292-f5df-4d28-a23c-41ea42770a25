#ifndef __ANALOG_DEVICE_HANDLER_H__
#define __ANALOG_DEVICE_HANDLER_H__

#include <list>
#include <string>
#include <vector>
#include <map>
#include "dbinterface/AnalogDevice.h"
#include "AkcsCommonSt.h"
#include "util.h"
#include "AkcsCommonDef.h"
#include "DeviceControl.h"
#include "UpdateConfigContext.h"

class AnalogDeviceHandler
{
public:
    using HandleInfoKVList = std::vector<std::pair<int, std::string>>;
    using DTMFConfigKVList = std::vector<std::pair<int, std::string>>;
    enum class HANDLE_INFO_ATTR
    {
        UID,
        ANALOG_SYSTEM,
        ANALOG_NUMBER,
        ANALOG_MODE,
        ANALOG_PROXY_ADDR,
        ANALOG_DTMF_CODE,
    };

    enum class DTMF_CONFIG_ATTR
    {
        MAC,
        IP,
        RELAY_DTMF,
        SE_RELAY_DTMF,
    };
public:
    AnalogDeviceHandler(DEVICE_SETTING* dev_info) : dev_grade_(dev_info->grade), project_uuid_(dev_info->project_uuid), is_support_analog_device_(false)
    {
        if (SwitchHandle(dev_info->fun_bit, FUNC_DEV_SUPPORT_ANALOG_HANDLE))
        {
            is_support_analog_device_ = true;
        }
    }
    void SetContext(ConfigContextPtr context)
    {
        context_ = context;
        return;
    }
    void WriteNodeAnalogDeviceContactStrAndSave(std::stringstream &contact_str, const std::string& node_uuid, int call_type);
    void WriteAnalogDeviceInfoStr(std::stringstream &config_body);
    void WriteAnalogDeviceDTMFConfigStr(std::stringstream &config_body);

private:
    std::string GetCallSeqByCallType(int call_type);
    int GetAnalogMode();
    std::string GetAnalogProxyIPAddressByCommunityUnitUUID(const std::string& unit_uuid);

    void WriteHandleInfoCommonStr(std::stringstream &str, const HandleInfoKVList &kv);
    void WriteHandleInfoStr(std::stringstream &str, const HandleInfoKVList &kv);
    void WriteDTMFConfigCommonStr(std::stringstream &str, const DTMFConfigKVList &kv);
    void WriteDTMFConfigStr(std::stringstream &str, const DTMFConfigKVList &kv);

private:
    int dev_grade_;
    std::string project_uuid_;
    bool is_support_analog_device_;
    AnalogDeviceList analog_device_list_;
    ConfigContextPtr context_;
    
};


#endif