#include <sstream>
#include <DevContact.h>
#include "CommunityDevContact.h"
#include <string.h>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "ConfigDef.h" 
#include "AdaptUtility.h"
#include "json/json.h"
#include "util.h"
#include "AkLogging.h"
#include "DeviceSetting.h"
#include "AkcsWebMsgSt.h"
#include "CommunityMng.h"
#include "DeviceControl.h"
#include "AES256.h"
#include "AkcsMonitor.h"
#include "encrypt/Md5.h"
#include "PersonalAccount.h"
#include "DeviceSetting.h"
#include "PersonnalDeviceSetting.h"
#include "AkcsCommonDef.h"
#include "ShadowMng.h"
#include "ContactCommon.h"
#include "dbinterface/PersonalAccountCnf.h"
#include "dbinterface/resident/ResidentDevices.h" 
#include "dbinterface/ContactBlock.h"
#include "dbinterface/ContactFavorite.h"
#include "dbinterface/Account.h"
#include "dbinterface/PmAccountMap.h"
#include "CommConfigHandle.h"
#include "FileUpdateControl.h"
#include "AKCSView.h"
#include "WriteFileControl.h"
#include "dbinterface/AccessGroupDB.h"
#include "dbinterface/AccountAccess.h"
#include "DeviceContactDisplay.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/CommunityCallRule.h"
#include "SequenceCallGenerator.h"
#include "util_judge.h"
#include "dbinterface/resident/CommunityDeviceContact.h"
#include "CommunityDevPushButton.h"
#include "AnalogDeviceHandler.h"
#include "dbinterface/VideoStorage.h"
#include "dbinterface/VideoStorageDevice.h"


extern CSCONFIG_CONF gstCSCONFIGConf;

int CommunityDevContact::CreateFavoriteOrBlock(const std::set<std::string>& uuid_list, std::stringstream& config_body)
{   
    for (auto& uuid : uuid_list)
    {        
        ResidentPerAccount account;
        memset(&account, 0, sizeof(account));

        if(0 != dbinterface::ResidentPersonalAccount::GetUUIDAccount(uuid, account))
        {
            continue;
        }

        std::string user = account.account;
        
        std::vector<DEVICE_CONTACTLIST> app_list;
        GetPersonalAccountInstance()->DaoGetCommunityApplistByNode(user, app_list);
        if (app_list.size() == 0)
        {
            AK_LOG_WARN << "get oAppList failed,user is: " << user;
            continue;
        }
        std::string unit_name = context_->GetUnitName(account.unit_id);
           
        int master = 1;
        //房间内的app联系人
        for (auto& app : app_list)
        {
            if (master)//主账号 在第一个
            {
                char apt[256] = "";
                snprintf(apt, sizeof(apt), "%s", app.room_name);
                if (strlen(apt) == 0)
                {
                    snprintf(apt, sizeof(apt), "%s", app.room_num);
                }
                ContactKvList kv;
                kv.push_back(std::make_pair(CONTACT_ATTR::NAME, app.name));
                kv.push_back(std::make_pair(CONTACT_ATTR::ROOM, apt));                    
                kv.push_back(std::make_pair(CONTACT_ATTR::NODE, app.sip_account)); 
                kv.push_back(std::make_pair(CONTACT_ATTR::UNIT, unit_name));
                kv.push_back(std::make_pair(CONTACT_ATTR::ROOM_N, app.room_num));
                GetGroupStr(config_body, kv);
                master = 0;
            }

            ContactKvList kv;
            kv.push_back(std::make_pair(CONTACT_ATTR::NAME, app.name));
            kv.push_back(std::make_pair(CONTACT_ATTR::SIP, app.sip_account));
            kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(DEVICE_TYPE_APP)));
            GetContactStr(config_body, kv);
        }

        ResidentDeviceList dev_list;
        dbinterface::ResidentDevices::GetNodeDevList(user, dev_list);
        //房间内的设备联系人
        for (auto& dev : dev_list)
        {
            if ((dev.dev_type == DEVICE_TYPE_INDOOR))
            {
                ContactKvList kv;
                kv.push_back(std::make_pair(CONTACT_ATTR::NAME, dev.location));
                kv.push_back(std::make_pair(CONTACT_ATTR::SIP, dev.sip));
                kv.push_back(std::make_pair(CONTACT_ATTR::MAC, dev.mac));
                kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(DEVICE_TYPE_INDOOR)));
                GetContactStr(config_body, kv);
            }
        }
        
        config_body << "</Group>\n";
    }
    
    return 0;
}


int CommunityDevContact::UpdateCommContactFile(DEVICE_SETTING* your_dev, DEVICE_SETTING* your_list/*房间下*/, std::vector<DEVICE_CONTACTLIST>& app_list,
                              const DEVICE_SETTING* pub_device_list/*最外层*/, 
                              const DEVICE_SETTING* unit_pub_device_list/*单元*/)
{
   UpdateContactFile(your_dev, your_list, app_list, pub_device_list, unit_pub_device_list); 
}


//更新三方摄像头联系人文件
void CommunityDevContact::UpdateThirdCameraContactFile(const DEVICE_CONTACTLIST& app, DEVICE_SETTING* your_dev, std::stringstream& config_body,
                              const DEVICE_SETTING* pub_device_list/*最外层*/, 
                              const DEVICE_SETTING* unit_pub_device_list/*单元*/,
                              const DEVICE_SETTING* your_list)
{
    const DEVICE_SETTING* cur_device_setting = nullptr;
    //社区室内机和管理机
    if (app.role == ACCOUNT_ROLE_COMMUNITY_MAIN && 
        ((your_dev->type == DEVICE_TYPE_INDOOR && your_dev->dclient_version >= D_CLIENT_VERSION_6533) 
        || (your_dev->type == DEVICE_TYPE_MANAGEMENT && your_dev->dclient_version >= D_CLIENT_VERSION_6542)))
    {
        ThirdPartyCamreaList unit_camera_list;
        ThirdPartyCamreaList node_camera_list;

        //最外围第三方摄像头
        const ThirdPartyCamreaList pub_camera_list = context_->GetPubThirdPartyCameraList();
        WriteCameraContactFile(pub_camera_list, pub_device_list, config_body, app.enable_ip_direct, your_dev);

        //单元楼栋第三方摄像头
        context_->GetUnitThirdPartyCameraList( app.unit_id, unit_camera_list);
        WriteCameraContactFile(unit_camera_list, unit_pub_device_list, config_body, app.enable_ip_direct, your_dev);

        //家庭第三方摄像头
        context_->GetNodeThirdPartyCameraList( app.uuid, node_camera_list);
        WriteCameraContactFile(node_camera_list, your_list, config_body, app.enable_ip_direct, your_dev);


    }
}

void CommunityDevContact::WriteCommunityContact(std::stringstream &config_body, DEVICE_SETTING* your_dev)
{
    if(your_dev->type == DEVICE_TYPE_INDOOR && your_dev->dclient_version >= D_CLIENT_VERSION_6500 
        && context_->IsCommunityContactOn())
    {
        std::string device_node_uuid;
        dbinterface::ResidentPersonalAccount::GetUUIDByAccount(your_dev->device_node, device_node_uuid);

        std::set<std::string> favorite_uuids;  
        dbinterface::ContactFavorite::GetFavoriteListByPerUUID(device_node_uuid, favorite_uuids);
        config_body << "<FavoriteData>\n";
        CreateFavoriteOrBlock(favorite_uuids, config_body);
        config_body << "</FavoriteData>\n";

        std::set<std::string> block_uuids;   
        dbinterface::ContactBlock::GetBlackListByPerUUID(device_node_uuid, block_uuids);
        config_body << "<BlockData>\n";
        CreateFavoriteOrBlock(block_uuids, config_body);
        config_body << "</BlockData>\n";
    }
}

void CommunityDevContact::CreateManagementDeviceContact(int enable_ip_direct, const DEVICE_SETTING* device_list, DEVICE_SETTING* your_dev, std::stringstream &config_body)
{
    const DEVICE_SETTING* cur_device_setting = device_list;
    while (cur_device_setting != nullptr)
    {
        if (cur_device_setting->type == DEVICE_TYPE_MANAGEMENT && IsManageBuilding(cur_device_setting, your_dev))
        {
            ContactKvList kv;
            kv.push_back(std::make_pair(CONTACT_ATTR::NAME, cur_device_setting->location));

            //开启了ip直播,并且室内机和管理机在同一网络组情况下,下发管理机的ip给室内机
            //未开启ip直播,或室内机和管理机不在同一网络组情况下,下发管理机的sip给室内机
            if (enable_ip_direct && your_dev->netgroup_num == cur_device_setting->netgroup_num)
            {
                kv.push_back(std::make_pair(CONTACT_ATTR::SIP, std::string("")));
                kv.push_back(std::make_pair(CONTACT_ATTR::IP, cur_device_setting->ip_addr));
            }
            else
            {               
                kv.push_back(std::make_pair(CONTACT_ATTR::SIP, cur_device_setting->sip_account));
                kv.push_back(std::make_pair(CONTACT_ATTR::IP, std::string("")));
            }
            kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(cur_device_setting->type)));
            kv.push_back(std::make_pair(CONTACT_ATTR::UID, cur_device_setting->sip_account));
            GetContactStr(config_body, kv);
        }
        cur_device_setting = cur_device_setting->next;
    }
}

void CommunityDevContact::WriteManagementContact(DEVICE_SETTING* your_dev, int enable_ip_direct,
                                      const DEVICE_SETTING* pub_device_list, const DEVICE_SETTING* unit_pub_device_list, std::stringstream &config_body)
{
    if(your_dev->type == DEVICE_TYPE_INDOOR && your_dev->dclient_version >= D_CLIENT_VERSION_6533)
    {        
        config_body << "<Management>\n";
        //管理机
        CreateManagementDeviceContact(enable_ip_direct, pub_device_list, your_dev, config_body);
        CreateManagementDeviceContact(enable_ip_direct, unit_pub_device_list, your_dev, config_body);
        //PM app
        const NodeAppList &pm_app_list = context_->GetPmAppList();
        for (const auto& app : pm_app_list)
        {
            ContactKvList kv;
            kv.push_back(std::make_pair(CONTACT_ATTR::NAME, app.name));                  
            kv.push_back(std::make_pair(CONTACT_ATTR::SIP, app.sip_account));
            kv.push_back(std::make_pair(CONTACT_ATTR::UID, app.sip_account));
            kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(DEVICE_TYPE_APP)));
            GetContactStr(config_body, kv);
        }
        config_body << "</Management>\n";
    }
}

/*把公共设备写到家庭设备*/
int CommunityDevContact::WritePubDevToFamilyDev(const DEVICE_CONTACTLIST& app, 
                              const DEVICE_SETTING* your_dev, 
                              std::stringstream& contact_file,
                              const DEVICE_SETTING* pub_device_list/*最外层*/, 
                              const DEVICE_SETTING* unit_pub_device_list/*单元*/,
                              const std::set<std::string> &node_valid_mac_list)
{
    int enable_ip_direct = app.enable_ip_direct;
    const DEVICE_SETTING* cur_device_setting = nullptr;
    //公共设备放在最前面
    int first_use_unit_dev = 0;
    if (pub_device_list)
    {
        cur_device_setting = pub_device_list;       
    }
    else
    {
        cur_device_setting = unit_pub_device_list;
        first_use_unit_dev = 1;
    }
    
    while (cur_device_setting != nullptr && (your_dev->type == DEVICE_TYPE_INDOOR || your_dev->type == DEVICE_TYPE_MANAGEMENT)) //梯口机门口机不用写公共设备联系人
    {
        if (!node_valid_mac_list.count(cur_device_setting->mac) && cur_device_setting->type != DEVICE_TYPE_MANAGEMENT)
        {
            cur_device_setting = cur_device_setting->next;
            if (cur_device_setting == nullptr && first_use_unit_dev == 0)
            {
                cur_device_setting = unit_pub_device_list;
                first_use_unit_dev = 1;
            }                    
            continue;
        }
        int manage_unit_flag = 1;

        if (csmain::COMMUNITY_DEVICE_TYPE_PUBLIC == cur_device_setting->grade && GetDeviceControlInstance()->DeviceIsManageBuilding(cur_device_setting->type))
        {
            manage_unit_flag = context_->DevMngUnitID(cur_device_setting, your_dev->unit_id);
        }

        if (0 == manage_unit_flag)
        {
            AK_LOG_INFO << "This Device mac=" << your_dev->mac << ",unit_id=" << your_dev->unit_id << " is not managed by Pub Device mac=" << cur_device_setting->mac;
            cur_device_setting = cur_device_setting->next;
            if (cur_device_setting == nullptr && first_use_unit_dev == 0)
            {
                cur_device_setting = unit_pub_device_list;
                first_use_unit_dev = 1;
            }
            continue;
        }

        /*室内机开门需求*/
        
        ContactKvList kv;
        kv.push_back(std::make_pair(CONTACT_ATTR::NAME, cur_device_setting->location));
        kv.push_back(std::make_pair(CONTACT_ATTR::UID, cur_device_setting->sip_account));                
        kv.push_back(std::make_pair(CONTACT_ATTR::MAC, cur_device_setting->mac));
        kv.push_back(std::make_pair(CONTACT_ATTR::PUB, std::string("1")));
        kv.push_back(std::make_pair(CONTACT_ATTR::RTSPPWD, cur_device_setting->rtsp_password));
        kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(cur_device_setting->type)));
        if (enable_ip_direct && your_dev->netgroup_num == cur_device_setting->netgroup_num)
        {
            kv.push_back(std::make_pair(CONTACT_ATTR::SIP, std::string("")));
            kv.push_back(std::make_pair(CONTACT_ATTR::IP, GetCurDevIPAddress(your_dev, cur_device_setting)));
            kv.push_back(std::make_pair(CONTACT_ATTR::OPTIONIP, GetOptionIP(your_dev, cur_device_setting)));
        }
        else
        {
            kv.push_back(std::make_pair(CONTACT_ATTR::SIP, cur_device_setting->sip_account));
            kv.push_back(std::make_pair(CONTACT_ATTR::IP, std::string("")));
                                
        }
        if (akjudge::DevDoorType(cur_device_setting->type))
        {
            int not_monitor = context_->IsNoMonitorDev(cur_device_setting->firmware);
            
            not_monitor = not_monitor || !(cur_device_setting->allow_end_user_monitor);
            kv.push_back(std::make_pair(CONTACT_ATTR::NOT_MONITOR, std::to_string(not_monitor)));
            if (communit_info_->GetIsNew() && !not_monitor)
            {
                kv.push_back(std::make_pair(CONTACT_ATTR::CAMERA_NUM, std::to_string(cur_device_setting->camera_num)));
            }
        }
        WriteRelayContact(cur_device_setting->type, cur_device_setting->relay, cur_device_setting->security_relay, kv);

        // 室内机代理门口机转流
        GetRepostContact(cur_device_setting, your_dev, kv);

        // 视频存储
        GetVideoRecordContact(cur_device_setting, your_dev, kv);
            
        GetContactStr(contact_file, kv);

        cur_device_setting = cur_device_setting->next;
        if (cur_device_setting == nullptr && first_use_unit_dev == 0)
        {
            cur_device_setting = unit_pub_device_list;
            first_use_unit_dev = 1;
        }
    }    
}

void CommunityDevContact::WriteMatchDtmfToKv(ContactKvList& kv, const std::string& phone, const std::string& phone2, const std::string& phone3, const std::string& app_sip_account)
{
    //sip交互时回的落地号码是没有国家号的，所以这边MATCH_DTMF要取后7位匹配
    //兼容：MatchDtmf1 MatchDtmf2一定是app和第一个落地号码，旧版本不识别MatchDtmf3 MatchDtmf4
    kv.push_back(std::make_pair(CONTACT_ATTR::MATCH_DTMF1, GetSubstrFromBehind(phone, PHONE_SUBSTR_DETECT_NUMBER)));
    kv.push_back(std::make_pair(CONTACT_ATTR::MATCH_DTMF2, app_sip_account));
    kv.push_back(std::make_pair(CONTACT_ATTR::MATCH_DTMF3, GetSubstrFromBehind(phone2, PHONE_SUBSTR_DETECT_NUMBER)));
    kv.push_back(std::make_pair(CONTACT_ATTR::MATCH_DTMF4, GetSubstrFromBehind(phone3, PHONE_SUBSTR_DETECT_NUMBER)));
}


int CommunityDevContact::UpdateContactFile(DEVICE_SETTING* your_dev, DEVICE_SETTING* your_list/*房间下*/, std::vector<DEVICE_CONTACTLIST>& app_list,
                                      const DEVICE_SETTING* pub_device_list/*最外层*/, 
                                      const DEVICE_SETTING* unit_pub_device_list/*单元*/)
{
    if (!your_dev)
    {
        AK_LOG_WARN << "UpdateContactFile failed .";
        return -1;
    }
    AK_LOG_INFO << "Start Write Dev Contact. Mac=" << your_dev->mac;
    
    //公共设备不需要更新联系人
    if (your_dev->flag & DEVICE_SETTING_FLAG_PER_PUBLIC)
    {
        //AK_LOG_WARN << "UpdateContactFile failed because dev is public dev.";
        return 0;
    }
    
    int contact_display_order = communit_info_->ContactDisplayOrder(); //联系人姓名展示顺序，0-FirstName+LastName,1-LastName+FirstName

    // 主账号有权限的设备列表
    const std::set<std::string> node_valid_mac_list = context_->GetAccountMacList(your_dev->device_node);
    //node_valid_mac_list = user_devices_map_[your_dev->device_node];

    //TODO:app_list[0]上一层已经判断
    CreateGroupCallSeq(app_list[0].call_type, app_list, your_list);

    const DEVICE_SETTING* cur_device_setting = your_list;
    std::stringstream group_file;
    std::stringstream contact_file;
    std::stringstream config_body;
    
    config_body << "\n<?xml version=\"1.0\" encoding=\"UTF-8\" ?>\n";
    config_body << "<ContactData>\n";

    int have_master = 0;
    int is_master = 1;
    int enable_ip_direct = 0;
    int phone_status = 0; //是否有落地
    int node_register = 0;
    int node_access_your_dev = node_valid_mac_list.count(your_dev->mac);  //主账号对your_dev的权限,用于判断主账号对apt door的权限
    std::string phone_head;
    for (auto& app : app_list)
    {
        char name[256] = {};
        WriteContactNameByOrder(contact_display_order, sizeof(name), app.firstname, app.lastname, name);
        std::string phone, phone2, phone3, phone_all, phone_last/*phone2 phone3*/;
        GetPhoneInfo(app, phone_head, phone, phone2, phone3, phone_all, phone_last);

        //开启的calltype选项只有呼叫phone，没有呼叫app，如果从账号没有配置phone，要呼叫app
        if (!is_master)
        {
            ChangeSlaveCallInfo(app, phone);
        }

        std::string call_seq = app.seq;
        std::string call_seq2 = app.seq2;
        if (your_dev->type == DEVICE_TYPE_INDOOR || your_dev->type == DEVICE_TYPE_MANAGEMENT)
        {
            call_seq = "";
            call_seq2 = "";
        }

        if (is_master)
        {
            is_master = 0;
            have_master = 1;
            enable_ip_direct = app.enable_ip_direct;

            GetMasterGroupInfo(group_file, app, your_dev->netgroup_num);

            //写公共设备
            WritePubDevToFamilyDev(app, your_dev, contact_file, pub_device_list, unit_pub_device_list, node_valid_mac_list);
            //三方摄像头
            UpdateThirdCameraContactFile(app, your_dev, contact_file, pub_device_list, unit_pub_device_list, your_list);
            //主账号
            //只有房间的不写入主账号信息
            //add by chenzhx02211028 R29空指针会崩溃
            if (gstCSCONFIGConf.server_type == ServerArea::ucloud
                && CheckUcloudCommunityID(your_dev->manager_account_id)
                && app.role == ACCOUNT_ROLE_COMMUNITY_MAIN 
                && app.only_apt == 1)
            {
                continue;
            }

            //v6.5版本以上的设备,空房间主账号和未注册账号不写入联系人列表,AKUVOX设备需要Dclient版本6500以上支持,其他品牌不需要判断Dclient版本
            if (CheckDeviceDclientVer6500(your_dev) && (CheckIsEmptyRoom(app) || !AccountIsRegister(app)))
            {
                continue;
            } 
            node_register = 1;

            //主账号判断apt door权限
            if (!node_access_your_dev)
            {
                continue;
            }
            
            //是否启用落地
            std::string sip;
            phone_status = app.phone_status;
            if (phone_status && your_dev->type != DEVICE_TYPE_INDOOR && your_dev->type != DEVICE_TYPE_MANAGEMENT) //v5.0室内机不配置落地信息
            {
                ContactKvList kv;
                kv.push_back(std::make_pair(CONTACT_ATTR::NAME, name));
                kv.push_back(std::make_pair(CONTACT_ATTR::SIP, phone));
                kv.push_back(std::make_pair(CONTACT_ATTR::UID, app.sip_account));
                kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(DEVICE_TYPE_APP)));
                kv.push_back(std::make_pair(CONTACT_ATTR::GROUP_CALL, std::string("1")));
                kv.push_back(std::make_pair(CONTACT_ATTR::MASTER, std::string("1")));
                WriteMatchDtmfToKv(kv, phone, phone2, phone3, app.sip_account);
                kv.push_back(std::make_pair(CONTACT_ATTR::SEQ, call_seq));                
                //区分于个人终端       目前个人 没有这个顺序。先呼叫app 在呼叫落地
                if (app.call_type == NODE_CALL_TYPE_INDOOR_PHONE
                        || app.call_type == NODE_CALL_TYPE_INDOOR_BACK_PHONE
                        || app.call_type == NODE_CALL_TYPE_INDOOR_BACK_APP_BACK_PHONE)
                {
                    kv.push_back(std::make_pair(CONTACT_ATTR::SIP0, app.sip_account));
                    kv.push_back(std::make_pair(CONTACT_ATTR::LAND, phone_last));
                }
                else if (app.call_type == NODE_CALL_TYPE_APP_INDOOR_BACK_PHONE)
                {
                    kv.push_back(std::make_pair(CONTACT_ATTR::SIP0, app.sip_account));
                    kv.push_back(std::make_pair(CONTACT_ATTR::LAND, phone_all));
                }
                else
                {              
                    kv.push_back(std::make_pair(CONTACT_ATTR::APP, app.sip_account));
                    kv.push_back(std::make_pair(CONTACT_ATTR::LAND, phone_all));
                }
                kv.push_back(std::make_pair(CONTACT_ATTR::SINGLECALL, GetContactSingleCallStr(app, phone_all)));
                GetContactStr(contact_file, kv);
            }
            else
            {
                ContactKvList kv;
                kv.push_back(std::make_pair(CONTACT_ATTR::NAME, name));
                kv.push_back(std::make_pair(CONTACT_ATTR::UID, app.sip_account));                    
                kv.push_back(std::make_pair(CONTACT_ATTR::SIP, app.sip_account));
                kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(DEVICE_TYPE_APP)));
                WriteMatchDtmfToKv(kv, phone, phone2, phone3, app.sip_account);
                kv.push_back(std::make_pair(CONTACT_ATTR::SEQ, call_seq));
                kv.push_back(std::make_pair(CONTACT_ATTR::SINGLECALL, GetContactSingleCallStr(app, phone_all)));
                GetContactStr(contact_file, kv);                             
            }
        }
        else
        {
            //家居适配
            if (!strcmp(app.name, "$$Delete$$ $$Delete$$"))
            {
                continue;
            }
            
            //主账号有注册,从账号不需要判断注册. 主账号未注册时,判断从账号有无注册
            //防止从账号未注册,但通过主账号邮箱收到的二维码登录app,产生呼叫相关的bug
            if (!node_register && !AccountIsRegister(app) && your_dev->dclient_version >= D_CLIENT_VERSION_6500)
            {
                continue;
            }

            //新小区个人门口机需要判断权限组
            if (IsNewCommunity() && akjudge::DevDoorType(your_dev->type))
            {
                // 从账号有权限的设备列表
                const std::set<std::string> slave_valid_mac_list = context_->GetAccountMacList(app.sip_account);
                if(!slave_valid_mac_list.count(your_dev->mac))
                {
                    continue;
                }
            }
            
            //从账号
            ContactKvList kv; 
            kv.push_back(std::make_pair(CONTACT_ATTR::NAME, name));
            kv.push_back(std::make_pair(CONTACT_ATTR::UID, app.sip_account));                    
            kv.push_back(std::make_pair(CONTACT_ATTR::SIP, app.sip_account));
            kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(DEVICE_TYPE_APP)));                  
            kv.push_back(std::make_pair(CONTACT_ATTR::MATCH_DTMF1, GetSubstrFromBehind(phone, PHONE_SUBSTR_DETECT_NUMBER)));              
            kv.push_back(std::make_pair(CONTACT_ATTR::SEQ, call_seq));
            kv.push_back(std::make_pair(CONTACT_ATTR::SEQ2, call_seq2));            
            if (phone_status && your_dev->type != DEVICE_TYPE_INDOOR && your_dev->type != DEVICE_TYPE_MANAGEMENT) //v5.0室内机不配置落地信息
            {
                kv.push_back(std::make_pair(CONTACT_ATTR::LAND, phone));        
            }
            kv.push_back(std::make_pair(CONTACT_ATTR::SINGLECALL, GetContactSingleCallStr(app, phone_all)));
            GetContactStr(contact_file, kv);
        }
    }
    
    if (!have_master)
    {
        group_file << "<Group  Name=\"\" Room=\"\" SIP=\"\" RoomN=\"\" IpDirect=\"\" />";
    }
 
    /*
    不启用IP直播，则联系都是SIP
    启用ip直播，则根据网络号，如果网络号一致放IP，反之放SIP
    */
    cur_device_setting = your_list;
    while (cur_device_setting != NULL)
    {
        if (cur_device_setting != your_dev &&
           ((your_dev->type != DEVICE_TYPE_INDOOR && CheckIsAptInDoorTypeDevice(cur_device_setting->type))  //门口机梯口机不写  只写室内机/管理中心
            || CheckIsAptInDoorTypeDevice(your_dev->type))  //室内机/管理中心机写全部联系人
            && !DeviceNotViewInContact(cur_device_setting->firmware, cur_device_setting->oem_id) //过滤不需要展示的设备
           )
        {
            //apt内设备权限跟主账号
            if (akjudge::DevDoorType(your_dev->type) && !node_access_your_dev)
            {
                cur_device_setting = cur_device_setting->next;
                continue;
            }
            
            int node_access_cur_dev = node_valid_mac_list.count(cur_device_setting->mac);
            if (CheckIsAptInDoorTypeDevice(your_dev->type) && akjudge::DevDoorType(cur_device_setting->type) && !node_access_cur_dev)
            {
                cur_device_setting = cur_device_setting->next;
                continue;
            }

            ContactKvList kv;
            kv.push_back(std::make_pair(CONTACT_ATTR::NAME, cur_device_setting->location));
            kv.push_back(std::make_pair(CONTACT_ATTR::UID, cur_device_setting->sip_account));
            kv.push_back(std::make_pair(CONTACT_ATTR::MAC, cur_device_setting->mac));              
            kv.push_back(std::make_pair(CONTACT_ATTR::RTSPPWD, cur_device_setting->rtsp_password));
            kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(cur_device_setting->type)));
            kv.push_back(std::make_pair(CONTACT_ATTR::SEQ, cur_device_setting->seq));
            if (akjudge::DevDoorType(cur_device_setting->type))
            {
                int not_monitor = context_->IsNoMonitorDev(cur_device_setting->firmware);
                not_monitor = not_monitor || !(cur_device_setting->allow_end_user_monitor);
                kv.push_back(std::make_pair(CONTACT_ATTR::NOT_MONITOR, std::to_string(not_monitor)));
                if (communit_info_->GetIsNew() && !not_monitor)
                {
                    kv.push_back(std::make_pair(CONTACT_ATTR::CAMERA_NUM, std::to_string(cur_device_setting->camera_num)));
                }
            }
            WriteRelayContact(cur_device_setting->type, cur_device_setting->relay, cur_device_setting->security_relay, kv);
            
            if (enable_ip_direct || your_dev->is_expire) //如果过期了默认回到ip直播
            {
                if (your_dev->netgroup_num == cur_device_setting->netgroup_num)
                {                  
                    kv.push_back(std::make_pair(CONTACT_ATTR::SIP, std::string("")));
                    kv.push_back(std::make_pair(CONTACT_ATTR::IP, GetCurDevIPAddress(your_dev, cur_device_setting)));   
                    kv.push_back(std::make_pair(CONTACT_ATTR::OPTIONIP, GetOptionIP(your_dev, cur_device_setting)));                                      
                }
                else
                {               
                    kv.push_back(std::make_pair(CONTACT_ATTR::SIP, cur_device_setting->sip_account));
                    kv.push_back(std::make_pair(CONTACT_ATTR::IP, std::string("")));    
                    kv.push_back(std::make_pair(CONTACT_ATTR::GROUP_CALL, std::string("1")));           
                }
            }
            else
            {                   
                kv.push_back(std::make_pair(CONTACT_ATTR::SIP, cur_device_setting->sip_account));
                kv.push_back(std::make_pair(CONTACT_ATTR::IP, std::string("")));    
            }
            // 室内机代理门口机转流
            GetRepostContact(cur_device_setting, your_dev, kv);
            
            // 视频存储
            GetVideoRecordContact(cur_device_setting, your_dev, kv);
            
            GetContactStr(contact_file, kv);
        }
        cur_device_setting = cur_device_setting->next;
    }

    AnalogDeviceHandler analog_device_handler(your_dev);

    //模拟手柄当作室内机处理，跟随主账号的权限
    if (node_access_your_dev)
    {
        analog_device_handler.SetContext(context_);
        analog_device_handler.WriteNodeAnalogDeviceContactStrAndSave(contact_file, app_list[0].uuid, app_list[0].call_type);
    }

    //contact为空时,不写入group
    if(contact_file.str().length())
    {
        config_body << group_file.str() << contact_file.str() << "</Group>\n";
    }

    //室内机判断是否开启户户通功能，需下发收藏和黑名单联系人列表
    WriteCommunityContact(config_body, your_dev);
    
    //室内机群呼管理机和pm app
    WriteManagementContact(your_dev, enable_ip_direct, pub_device_list, unit_pub_device_list, config_body);

    config_body << "</ContactData>\n";
    //给设备写pushbutton 的配置
    bool is_support_extern_push_button = dbinterface::SwitchHandle(your_dev->fun_bit, FUNC_DEV_SUPPORT_EXTERN_PUSH_BUTTON);
    if (is_support_extern_push_button)
    {
        CommunityDevPushButton push_button(context_);
        push_button.UpdateCommnityDevPushButtonContact(your_dev->mac, config_body);
    }

    analog_device_handler.WriteAnalogDeviceInfoStr(config_body);

    //写入文件
    std::string config_path = config_root_path_ + your_dev->mac + ".xml";
    DevFileInfoPtr ptr = std::make_shared<DevFileInfo>(your_dev->mac, config_path, config_body.str(), SHADOW_TYPE::SHADOW_CONTACT,
                                                        project::RESIDENCE, your_dev->id);
    GetWriteFileControlInstance()->AddFileInfo(your_dev->mac, ptr);

    return 0;
}

/*
<PubInfo > //公共设备信息，用于管理中心机，Unit代表对应building名称，Unit为空代表最外围公共设备
  <Contact Name="ddorrrd" Unit="building" SIP="" IP="************" MAC="************" RTSPPwd="1547152G2yX549l0" Type="0" seq="1-2"/>
  <Contact Name="ddorrrd" Unit="building2" SIP="" IP="************" MAC="************" RTSPPwd="1547152G2yX549l0" Type="0" seq="1-2"/>
  <Contact Name="ddorrrd" Unit="" SIP="" IP="************" MAC="************" RTSPPwd="1547152G2yX549l0" Type="0" seq="1-2"/>
</PubInfo>

*/
//写公共设备联系人 csmain::COMMUNITY_DEVICE_TYPE_PUBLIC
int CommunityDevContact::UpdateCommunityPublicContactFile(DEVICE_SETTING* dev, const CommunitAccountInfoList& accounts, const MapNodeAppList &map_node_list, int type)
{
    if (!dev)
    {
        return -1;
    }
    AK_LOG_INFO << "Start Write Dev Contact. Mac=" << dev->mac;
    DeviceContactDisplay contact_display(dev->uuid, IsNewCommunity());

    int is_pub = 0;
    int node_access = 0;
    int node_register = 0;
    int contact_display_order = communit_info_->ContactDisplayOrder(); //联系人姓名展示顺序，0-FirstName+LastName,1-LastName+FirstName
    std::stringstream config_body;
    std::stringstream group_file;
    std::stringstream contact_file;
    config_body << "\n<?xml version=\"1.0\" encoding=\"UTF-8\" ?>\n";
    config_body << "<ContactData>\n";
    
    if (csmain::COMMUNITY_DEVICE_TYPE_PUBLIC == type)
    {
        is_pub = 1;
    }

    AnalogDeviceHandler analog_device_handler(dev);
    analog_device_handler.SetContext(context_);

    //轮询房间
    for (auto& node : accounts)
    {
        node_access = 0;
        node_register = 0;
        group_file.str("");
        contact_file.str("");
        // 公共设备判断楼栋管理
        if (is_pub && GetDeviceControlInstance()->DeviceIsManageBuilding(dev->type))
        {
            if (!context_->DevMngUnitID(dev, node.unit_id))
            {
                // 未管理主账号所在的楼栋,跳过
                continue;
            }
        }   
        
        std::string user = node.account;
        std::string node_uuid = node.uuid;
        NodeAppList app_list;
        MapNodeAppListIter iter = map_node_list.find(user);
        if(iter == map_node_list.end())
        {
            continue; 
        }
        //TODO:通过指针可以减少内存拷贝
        app_list = iter->second;
        if (app_list.size() == 0)
        {
            AK_LOG_WARN << "get app_list failed,user is: " << user;
            continue;
        }

  
        const PersonalAccountCnfInfo* node_config = context_->GetPeronalAccountCnf(user);
        if (node_config == nullptr) {
            AK_LOG_WARN << "get GetPeronalAccountCnf failed,user is: " << user;
            continue;
        } 

        SetNodeCallInfo(app_list[0], node_config->call_type, dev->dclient_version);
        
        // 获取apt下的设备列表
        DEVICE_SETTING* node_device_setting_list = context_->GetNodeDeviceSettingList(user);

        CreateGroupCallSeq(app_list[0].call_type, app_list, node_device_setting_list);
        
        int is_master = 1;
        int ip_direct = 0;
        int node_phone_status = 0;
        std::string phone_head;

        //给公共设备写入app
        for (auto& app : app_list)
        {   
            char name[256] = {};
            WriteContactNameByOrder(contact_display_order, sizeof(name), app.firstname, app.lastname, name);
            std::string phone, phone2, phone3, phone_all, phone_last;
            GetPhoneInfo(app, phone_head, phone, phone2, phone3, phone_all, phone_last);
            
            if (is_master)//主账号 在第一个
            {
                contact_display.SetUnitUUID(app.unit_uuid);
                contact_display.SetAptUUID(app.uuid);
                
                is_master = 0;
                ip_direct = app.enable_ip_direct;
                node_phone_status = app.phone_status;

                ContactKvList kv;             
                GetMasterGroupInfo(group_file, app, dev->netgroup_num);

                //给公共设备写主账号,需要判断主账号对公共设备的主账号(公共区域管理机不需要判断权限组)
                //node_access必须先赋值，避免下面空房间continue掉了
                if (context_->AccountHaveMacPermission(app.sip_account, dev->mac) || dev->type == DEVICE_TYPE_MANAGEMENT)
                {
                    node_access = 1;
                }  

                //空房间的主账号信息不写
                //邮箱,手机,落地都没填的不写入联系人
                if ((!AccountIsRegister(app) ||  app_list[0].only_apt)
                      && dev->dclient_version >= D_CLIENT_VERSION_6500)
                {
                    continue;
                }
                else
                {
                    node_register= 1;
                }

                if(!node_access)
                {
                    continue;
                }

                
                //只有房间的不写入主账号信息
                //add by chenzhx02211028 R29空指针会崩溃
                if (gstCSCONFIGConf.server_type == ServerArea::ucloud
                    && CheckUcloudCommunityID(dev->manager_account_id)
                    && app.role == ACCOUNT_ROLE_COMMUNITY_MAIN 
                    && app.only_apt == 1)
                {
                    continue;
                }

                if (node_phone_status)
                {
                    ContactKvList kv;   
                    kv.push_back(std::make_pair(CONTACT_ATTR::IS_DISPLAY, std::to_string(contact_display.PersonalIsDisplay(app.uuid))));
                    kv.push_back(std::make_pair(CONTACT_ATTR::NAME, name));
                    kv.push_back(std::make_pair(CONTACT_ATTR::UID, app.sip_account));  
                    kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(DEVICE_TYPE_APP)));
                    kv.push_back(std::make_pair(CONTACT_ATTR::GROUP_CALL, std::string("1")));
                    kv.push_back(std::make_pair(CONTACT_ATTR::MASTER, std::string("1")));
                    WriteMatchDtmfToKv(kv, phone, phone2, phone3, app.sip_account);
                    kv.push_back(std::make_pair(CONTACT_ATTR::SEQ, app.seq));                        
                    //落地之后直接把号码赋值到IP,设备呼叫就会带上这个号码呼叫
                    //区分于个人终端       目前个人 没有这个顺序。先呼叫app 在呼叫落地
                    if (app.call_type == NODE_CALL_TYPE_INDOOR_PHONE
                            || app.call_type == NODE_CALL_TYPE_INDOOR_BACK_PHONE
                            || app.call_type == NODE_CALL_TYPE_INDOOR_BACK_APP_BACK_PHONE)
                    {                  
                        kv.push_back(std::make_pair(CONTACT_ATTR::SIP, phone));                    
                        kv.push_back(std::make_pair(CONTACT_ATTR::SIP0, app.sip_account));
                        kv.push_back(std::make_pair(CONTACT_ATTR::LAND, phone_last));
                    }
                    else if (app.call_type == NODE_CALL_TYPE_APP_INDOOR_BACK_PHONE)
                    {                  
                        kv.push_back(std::make_pair(CONTACT_ATTR::SIP, phone));                    
                        kv.push_back(std::make_pair(CONTACT_ATTR::SIP0, app.sip_account));
                        kv.push_back(std::make_pair(CONTACT_ATTR::LAND, phone_all));                                  
                    }
                    else
                    {                   
                        kv.push_back(std::make_pair(CONTACT_ATTR::SIP, phone));                    
                        kv.push_back(std::make_pair(CONTACT_ATTR::APP, app.sip_account));
                        kv.push_back(std::make_pair(CONTACT_ATTR::LAND, phone_all));
                    }
                    kv.push_back(std::make_pair(CONTACT_ATTR::SINGLECALL, GetContactSingleCallStr(app, phone_all)));
                    GetContactStr(contact_file, kv);                    
                }
                else
                {
                    ContactKvList kv;
                    kv.push_back(std::make_pair(CONTACT_ATTR::IS_DISPLAY,  std::to_string(contact_display.PersonalIsDisplay(app.uuid))));
                    kv.push_back(std::make_pair(CONTACT_ATTR::NAME, name));
                    kv.push_back(std::make_pair(CONTACT_ATTR::UID, app.sip_account));                    
                    kv.push_back(std::make_pair(CONTACT_ATTR::SIP, app.sip_account));  
                    kv.push_back(std::make_pair(CONTACT_ATTR::MASTER, std::string("1")));
                    kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(DEVICE_TYPE_APP)));
                    WriteMatchDtmfToKv(kv, phone, phone2, phone3, app.sip_account);
                    kv.push_back(std::make_pair(CONTACT_ATTR::SEQ, app.seq));
                    kv.push_back(std::make_pair(CONTACT_ATTR::SINGLECALL, GetContactSingleCallStr(app, phone_all)));
                    GetContactStr(contact_file, kv);                      
                }
            }
            else
            {
                //给公共设备写从账号,需要判断从账号对公共设备的权限(公共区域管理机不需要判断权限组)
                if (!context_->AccountHaveMacPermission(app.sip_account, dev->mac) && dev->type != DEVICE_TYPE_MANAGEMENT)
                {
                    continue;
                }            

                //家居适配
                if (!strcmp(app.name, "$$Delete$$ $$Delete$$"))
                {
                    continue;
                }            

                //主账号未注册时,判断从账号有无注册
                if (!node_register && !AccountIsRegister(app) && dev->dclient_version >= D_CLIENT_VERSION_6500)
                {
                    continue;
                }

                //开启的calltype选项只有呼叫phone，没有呼叫app，如果从账号没有配置phone，要呼叫app
                ChangeSlaveCallInfo(app, phone);
                //从账号
                ContactKvList kv;                
                kv.push_back(std::make_pair(CONTACT_ATTR::IS_DISPLAY,  std::to_string(contact_display.PersonalIsDisplay(app.uuid))));
                kv.push_back(std::make_pair(CONTACT_ATTR::NAME, name));
                kv.push_back(std::make_pair(CONTACT_ATTR::UID, app.sip_account));                    
                kv.push_back(std::make_pair(CONTACT_ATTR::SIP, app.sip_account));
                kv.push_back(std::make_pair(CONTACT_ATTR::MATCH_DTMF1, GetSubstrFromBehind(phone, PHONE_SUBSTR_DETECT_NUMBER)));                    
                kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(DEVICE_TYPE_APP)));
                kv.push_back(std::make_pair(CONTACT_ATTR::SEQ, app.seq));
                kv.push_back(std::make_pair(CONTACT_ATTR::SEQ2, app.seq2));                
                if (node_phone_status)
                {
                    kv.push_back(std::make_pair(CONTACT_ATTR::LAND, phone));                              
                }
                kv.push_back(std::make_pair(CONTACT_ATTR::SINGLECALL, GetContactSingleCallStr(app, phone_all)));
                GetContactStr(contact_file, kv);
            }
        }

        //给公共设备写入室内机,只需要判断主账号对公共设备的权限(室内机权限跟随主账号)
        if (node_access)
        {
            DEVICE_SETTING* cur_device_setting = node_device_setting_list;
            while (cur_device_setting != NULL)
            {
                // 过滤不需要展示的设备
                if(DeviceNotViewInContact(cur_device_setting->firmware, cur_device_setting->oem_id))
                {
                    cur_device_setting = cur_device_setting->next;
                    continue;
                }
                //1、公共设备联系人会有室内机，室内机会呼叫公共设备
                //2、公共设备是管理机时候，联系人会有所有室内机, 没有门口机和门禁。
                if(akjudge::DevDoorType(cur_device_setting->type) && dev->type == DEVICE_TYPE_MANAGEMENT && dev->dclient_version >= D_CLIENT_VERSION_6200)
                {
                    cur_device_setting = cur_device_setting->next;
                    continue;
                }
                //若当前公共设备是管理机，或要写入的设备是室内机或管理机时，判断公共设备是否管理了所在楼栋
                if ((cur_device_setting->type == DEVICE_TYPE_INDOOR || cur_device_setting->type == DEVICE_TYPE_MANAGEMENT) || dev->type == DEVICE_TYPE_MANAGEMENT)
                {
                    if(is_pub && GetDeviceControlInstance()->DeviceIsManageBuilding(dev->type))
                    {
                        if(!context_->DevMngUnitID(dev, cur_device_setting->unit_id))
                        {
                            cur_device_setting = cur_device_setting->next;
                            continue;
                        }
                    }  

                    ContactKvList kv;                   
                    kv.push_back(std::make_pair(CONTACT_ATTR::IS_DISPLAY,  std::to_string(contact_display.IndoorIsDisplay(cur_device_setting->uuid))));
                    kv.push_back(std::make_pair(CONTACT_ATTR::NAME, cur_device_setting->location));
                    kv.push_back(std::make_pair(CONTACT_ATTR::UID, cur_device_setting->sip_account));                       
                    kv.push_back(std::make_pair(CONTACT_ATTR::MAC, cur_device_setting->mac));                
                    kv.push_back(std::make_pair(CONTACT_ATTR::RTSPPWD, cur_device_setting->rtsp_password));  
                    kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(cur_device_setting->type)));
                    kv.push_back(std::make_pair(CONTACT_ATTR::SEQ, cur_device_setting->seq));                     
                    if (ip_direct && cur_device_setting->netgroup_num == dev->netgroup_num)
                    {                    
                        kv.push_back(std::make_pair(CONTACT_ATTR::SIP, std::string(""))); 
                        kv.push_back(std::make_pair(CONTACT_ATTR::IP, GetCurDevIPAddress(dev, cur_device_setting)));     
                        kv.push_back(std::make_pair(CONTACT_ATTR::OPTIONIP, GetOptionIP(dev, cur_device_setting)));                
                    }
                    else if (ip_direct)
                    {
                        //对于设备GroupCall只有在ip直播不同网络组才能配置
                        kv.push_back(std::make_pair(CONTACT_ATTR::SIP, cur_device_setting->sip_account)); 
                        kv.push_back(std::make_pair(CONTACT_ATTR::IP, std::string("")));
                        kv.push_back(std::make_pair(CONTACT_ATTR::GROUP_CALL, std::string("1")));
                    }
                    else
                    {                  
                        kv.push_back(std::make_pair(CONTACT_ATTR::SIP, cur_device_setting->sip_account)); 
                        kv.push_back(std::make_pair(CONTACT_ATTR::IP, std::string("")));
                    }
                    GetContactStr(contact_file, kv);
                }
                cur_device_setting = cur_device_setting->next;
            }

            //前面已经保证有对应房间的权限了 user即node
            analog_device_handler.WriteNodeAnalogDeviceContactStrAndSave(contact_file, node_uuid, app_list[0].call_type);
        }
       
        //contact不为空才写
        if(contact_file.str().length())
        {
            config_body << group_file.str() << contact_file.str() << "</Group>\n";
        }
        context_->ReleaseDeviceSetting(node_device_setting_list);

    }

    // 给设备写管理中心机的配置（包括所有的公共设备PubInfo）
    if (GetDeviceControlInstance()->DeviceIsManageBuilding(dev->type))
    {
        // 获取所有的pub/unit设备
        DEVICE_SETTING* pub_device_setting_list = context_->AllPublicDeviceSetting();
        
        //获取社区所有公共三方摄像头列表
        const ThirdPartyCamreaList all_pub_camera_list = context_->GetAllPubThirdPartyCameraList();
        
        DEVICE_SETTING* cur_device_setting = pub_device_setting_list;
        config_body << "<PubInfo>\n";
        std::string unit_name = "";

        //根据公共设备列表写管理机公共三方摄像头联系人文件
        if (dev->type == DEVICE_TYPE_MANAGEMENT && dev->dclient_version >= D_CLIENT_VERSION_6542)
        {
            //公共第三方摄像头
            if (all_pub_camera_list.size() > 0)
            {
                WriteCameraContactFileToPublicManagement(all_pub_camera_list, pub_device_setting_list, config_body);
            }
        }

        while (cur_device_setting != NULL)
        {
            bool can_write = false; //是否将当前设备信息写入
            if(dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
            {
                can_write = CanWriteCurDevToPublicDevPubInfo(dev, cur_device_setting);
            }
            else if(dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
            {
                can_write = CanWriteCurDevToUnitDevPubInfo(dev, cur_device_setting);
            }
            if(!can_write)
            {
                //不写入当前设备，直接跳过
                cur_device_setting = cur_device_setting->next;
                continue;
            }

            if (cur_device_setting->unit_id)
            {
                unit_name = context_->GetUnitName(cur_device_setting->unit_id);
            }
            else
            {
                unit_name = "";
            }

            // 写入其他公共设备的配置
            if (strcmp(dev->mac, cur_device_setting->mac))
            {
                ContactKvList kv;            
                kv.push_back(std::make_pair(CONTACT_ATTR::NAME, cur_device_setting->location));
                kv.push_back(std::make_pair(CONTACT_ATTR::UID, cur_device_setting->sip_account));                  
                kv.push_back(std::make_pair(CONTACT_ATTR::MAC, cur_device_setting->mac));                
                kv.push_back(std::make_pair(CONTACT_ATTR::RTSPPWD, cur_device_setting->rtsp_password));  
                kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(cur_device_setting->type)));
                kv.push_back(std::make_pair(CONTACT_ATTR::UNIT, unit_name));
                if (dev->type == DEVICE_TYPE_MANAGEMENT)
                {
                    WriteRelayContact(cur_device_setting->type, cur_device_setting->relay, cur_device_setting->security_relay, kv);
                }
            
                if (akjudge::DevDoorType(cur_device_setting->type))
                {
                    int not_monitor = context_->IsNoMonitorDev(cur_device_setting->firmware);
                    kv.push_back(std::make_pair(CONTACT_ATTR::NOT_MONITOR, std::to_string(not_monitor)));
                    if (communit_info_->GetIsNew() && !not_monitor)
                    {
                        kv.push_back(std::make_pair(CONTACT_ATTR::CAMERA_NUM, std::to_string(cur_device_setting->camera_num)));
                    }
                }

                if (cur_device_setting->netgroup_num == dev->netgroup_num)
                {         
                    kv.push_back(std::make_pair(CONTACT_ATTR::SIP, std::string(""))); 
                    kv.push_back(std::make_pair(CONTACT_ATTR::IP, GetCurDevIPAddress(dev, cur_device_setting)));
                    kv.push_back(std::make_pair(CONTACT_ATTR::OPTIONIP, GetOptionIP(dev, cur_device_setting)));
                }
                else
                {                           
                    kv.push_back(std::make_pair(CONTACT_ATTR::SIP, cur_device_setting->sip_account)); 
                    kv.push_back(std::make_pair(CONTACT_ATTR::IP, std::string("")));                   
                }
                GetContactStr(config_body, kv);
            }
            cur_device_setting = cur_device_setting->next;
        }
        config_body << "</PubInfo>\n";

        context_->ReleaseDeviceSetting(pub_device_setting_list);

    }
    config_body << "</ContactData>\n";
    //给设备写pushbutton 的配置
    bool is_support_extern_push_button = dbinterface::SwitchHandle(dev->fun_bit, FUNC_DEV_SUPPORT_EXTERN_PUSH_BUTTON);
    if (is_support_extern_push_button)
    {
        CommunityDevPushButton push_button(context_);
        push_button.UpdateCommnityDevPushButtonContact(dev->mac, config_body);
    }


    analog_device_handler.WriteAnalogDeviceInfoStr(config_body);
    //需求变更,不需要了.若是联调没问题,就删掉
    //相关的方法，也去掉下
    //analog_device_handler.WriteAnalogDeviceDTMFConfigStr(config_body);

    //写入文件
    std::string config_path = config_root_path_ + dev->mac + ".xml";
    DevFileInfoPtr ptr = std::make_shared<DevFileInfo>(dev->mac, config_path, config_body.str(), SHADOW_TYPE::SHADOW_CONTACT,
                                                        project::RESIDENCE, dev->id);
    GetWriteFileControlInstance()->AddFileInfo(dev->mac, ptr);
    
    return 0;
}


int CommunityDevContact::IsNewCommunity()
{
    if (!communit_info_)
    {
        return 0;
    }
    return communit_info_->GetIsNew();
}

//判断管理机是否管理室内机所在的楼栋
bool CommunityDevContact::IsManageBuilding(const DEVICE_SETTING* cur_dev, DEVICE_SETTING* your_dev)
{
    //楼栋位置的管理机,判断是否和室内机在同一个单元
    if (cur_dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT && cur_dev->unit_id == your_dev->unit_id)
    {
        return true;
    }
    //最外围的管理机,先判断是否开启全选,未开启再查找管理的楼栋
    if (cur_dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC && GetDeviceControlInstance()->DeviceIsManageBuilding(cur_dev->type))
    {
        if (context_->DevMngUnitID(cur_dev, your_dev->unit_id))
        {
            return true;
        }
    }
    return false;
}


bool CommunityDevContact::CanWriteCurDevToPublicDevPubInfo(const DEVICE_SETTING * your_dev, const DEVICE_SETTING* cur_dev)
{
    //数据合法性校验
    if(your_dev->grade != csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
    {
        return false;
    }
    //判断公共设备是否管理了楼栋设备所在楼栋
    if(cur_dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
        if(!context_->DevMngUnitID(your_dev, cur_dev->unit_id))
        {
            //未管理的楼栋设备不写入最外围公共设备
            return false;
        }
    }
    return true;
}

bool CommunityDevContact::CanWriteCurDevToUnitDevPubInfo(const DEVICE_SETTING* your_dev, const DEVICE_SETTING* cur_dev)
{
    //数据合法性校验
    if(your_dev->grade != csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
        return false;
    }
    //楼栋管理机需要写最外围公共设备信息
    if(your_dev->type == DEVICE_TYPE_MANAGEMENT && cur_dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
    {
        return true;
    }
    //其余情况，只写同一楼栋下公共设备
    if(your_dev->unit_id == cur_dev->unit_id)
    {
        return true;
    }
    return false;
}
void CommunityDevContact::WriteContactNameByOrder(const int contact_display_order, const size_t length, const char* firstname, const char* lastname, char *name)
{
    if (contact_display_order == 0)
    {
        ::snprintf(name, length, "%s %s", firstname, lastname);
    } 
    else if (contact_display_order == 1)
    {
        ::snprintf(name, length, "%s %s", lastname, firstname);
    }
}

void CommunityDevContact::GetMasterGroupInfo(std::stringstream &config_body, const DEVICE_CONTACTLIST &app, int dev_network_group)
{
    ContactKvList kv;
    std::string unit_info;
    if (app.role == ACCOUNT_ROLE_COMMUNITY_MAIN)
    {
        unit_info = context_->GetUnitName(app.unit_id);
    }    
    GetMasterGroupBaseInfo(app, kv, unit_info);
    
    //sequence call字段获取
    CommunityCallRuleInfo call_rule;
    if(context_->GetCallRule(app.uuid, call_rule) == 0)
    {
        SeqCallGenerator seq_call_gen(call_rule, app.enable_ip_direct, dev_network_group);
        seq_call_gen.GetSeqCallKv(kv);
    }

    GetGroupStr(config_body, kv);
}

//写三方摄像头联系人文件
void CommunityDevContact::WriteCameraContactFile(ThirdPartyCamreaList camera_list, const DEVICE_SETTING* device_list, std::stringstream& config_body, int enable_ip_direct, const DEVICE_SETTING* indoor_dev)
{
    const DEVICE_SETTING* cur_device_setting = nullptr;
    for (const auto& camera : camera_list)
    {
        int not_monitor = 0;
        //设备是否允许终端用户监控 0=不允许 1=允许
        if(!camera.allow_end_user_monitor)
        {
            not_monitor = 1;
        }
        //监控平台，1=SmartPlus + Indoor Monitor+apt下的management，2=Only SmartPlus，3=Only Indoor Monitor+apt下的management'
        if(camera.monitoring_platform == MONITOR_TYPE_ONLY_SMARTPLUS)
        {
            not_monitor = 1;
        }
        //有link ak设备,则要判断link的设备mac是否在device_list
        if (strlen(camera.mac) && device_list)
        {   
            cur_device_setting = device_list;
            while (cur_device_setting != nullptr)
            {
                if (0 == strcmp(cur_device_setting->mac, camera.mac))
                {
                    //如果摄像头BondMAC设备的ip地址与室内机的ip地址在同一个局域网（同一个网络组（network group））
                    //室内机直接和三方摄像头进行rtsp协商。否者经过门口机走云转发监控。
                    std::string mac(camera.mac);
                    DEVICE_SETTING* camera_bond_device = context_->GetMacDeviceInGlobal(mac);
                    //摄像头BondMAC设备和室内机在同一网络下，走本地取流
                    if(indoor_dev->netgroup_num == camera_bond_device->netgroup_num && enable_ip_direct)
                    {
                        InsertCameraList(camera, DEVICE_MONITOR_TYPE_THIRD_CAMERA, config_body, not_monitor);
                    }
                    else
                    {
                        InsertCameraList(camera, DEVICE_MONITOR_TYPE_CLOUD, config_body, not_monitor);
                    }
                    context_->ReleaseDeviceSetting(camera_bond_device);
                }
                cur_device_setting = cur_device_setting->next;
            }
        }
        else
        {
            //mac为空代表未绑定门口机
            InsertCameraList(camera, DEVICE_MONITOR_TYPE_THIRD_CAMERA, config_body, not_monitor);
        }
    }
}

// 给室内机下发门口机是否支持视频存储字段
void CommunityDevContact::GetVideoRecordContact(const DEVICE_SETTING* cur_dev, const DEVICE_SETTING* your_dev, ContactKvList &kv)
{    
    auto setVideoRecordDisabled = [&kv]() {
        kv.push_back(std::make_pair(CONTACT_ATTR::RECORD_VIDEO, std::string("0")));
    };

    auto setVideoRecordEnabled = [&kv]() {
        kv.push_back(std::make_pair(CONTACT_ATTR::RECORD_VIDEO, std::string("1")));
    };

    // your_dev 不是室内机或者 cur_dev 不是门口机
    if (your_dev->type != DEVICE_TYPE_INDOOR || !akjudge::DevDoorType(cur_dev->type))
    {
        setVideoRecordDisabled();
        return;
    }

    // 公共区域的室内机不支持视频存储
    if (akjudge::IsCommunityPublicDev(your_dev->grade)) 
    {
        setVideoRecordDisabled();
        return;
    }

    // 门口机不支持视频存储功能
    if (!SwitchHandle(cur_dev->fun_bit, FUNC_DEV_SUPPORT_VIDEO_RECORD) )
    {
        setVideoRecordDisabled();
        return;
    }
    
    VideoStorageInfo video_storage_info;
    if (0 != context_->GetVideoStorageConfig().GetVideoStorageInfo(cur_dev, video_storage_info))
    {
        setVideoRecordDisabled();
        return;
    }

    if (!context_->GetVideoStorageConfig().IsVideoStorageDevices(cur_dev->uuid))
    {
        setVideoRecordDisabled();
        return;
    }
    
    setVideoRecordEnabled();
    return;
}

void UpdateCommunityAccessGroupContactListByAccount(unsigned int community_id, const ResidentPerAccountList& account_list)
{
    AK_LOG_INFO << "On accessgroup modify, update related device contact list by account list.";
    unsigned int unit_id = 0;
    std::vector<std::string> mac_param;
    
    //在下方for循环去UpdateNode即可
    CommConfigHandle handle(community_id, unit_id, "", mac_param);
    if (!handle.InitSuccess())
    {
        AK_LOG_INFO << "CommConfigHandle init error, project type is correct." << community_id;    
        return;
    }    
    //更新权限组人员变化对应的node下的设备,假如房间都被删除了,这边就不用去刷了
    for(const auto& node : account_list)
    {
        if (node.unit_id != 0 && unit_id != node.unit_id)
        {
            unit_id = node.unit_id;
            //更新pub/unit设备联系人,不同楼栋才需要再刷
            GetFileUpdateContorlInstance()->CommunityFileUpdateFormateWeb(WEB_COMM_UPDATE_COMMUNITY_PUB_UNIT_CONTACT, community_id, unit_id, node.account, mac_param);
            handle.SetUnitID(unit_id);
        }
        handle.UpdateNode(node.account);
        handle.UpdateNodeDevContactList();
    }    

    AK_LOG_INFO << "On accessgroup only modify account.";
    return;
}


void UpdateCommunityAccessGroupContactListByAccountAndDevice(unsigned int community_id, const ResidentPerAccount& node, DEVICE_SETTING* pub_devlist)
{
    AK_LOG_INFO << "On accessgroup delete, update related device contact list by account list.";
    std::vector<std::string> mac_param;
    
    CommConfigHandle handle(community_id, node.unit_id, "", mac_param);
    if (!handle.InitSuccess())
    {
        AK_LOG_INFO << "CommConfigHandle init error, project type is correct." << community_id;    
        return;
    }      
    handle.UpdateNode(node.account);
    handle.UpdateNodeDevContactList();
   
    unsigned int unit_id = 0;
    //刷最外围的公共设备
    GetFileUpdateContorlInstance()->CommunityFileUpdateFormateWeb(WEB_COMM_UPDATE_COMMUNITY_PUB_UNIT_CONTACT, community_id, unit_id, "", mac_param);
    DEVICE_SETTING* cur_dev = pub_devlist;
    while(cur_dev != nullptr)
    {
        //刷楼栋公共设备，不同unit才需要二次更新
        if(cur_dev->unit_id != 0 && unit_id != cur_dev->unit_id)
        {
            unit_id = cur_dev->unit_id;
            //更新移除的权限组关联的pub/unit设备联系人
            GetFileUpdateContorlInstance()->CommunityFileUpdateFormateWeb(WEB_COMM_UPDATE_COMMUNITY_PUB_UNIT_CONTACT, community_id, unit_id, "", mac_param);
        }
        cur_dev = cur_dev->next;
    }
    return;
}

void UpdateCommunityAccessGroupContactListByAgid(unsigned int access_group_id, unsigned int community_id)
{

    AK_LOG_INFO << "On accessgroup modify, update related device contact list by Agid.";
    std::string node_param;
    unsigned int unit_id = 0;
    std::vector<std::string> mac_param;
    ResidentPerAccountList account_info_list;

    //以下是改权限设备或者时间的需要刷新所有相关家庭的室内机联系人
    //获取权限组关联的用户信息
    dbinterface::AccountAccess::GetAccountInfoByAccessGroup(access_group_id, account_info_list);    

    std::set<std::string> nodes;
    //更新用户对应node下的设备,只有主账号权限发生变化需要刷室内机联系人
    for (const auto& account_info : account_info_list)
    {
        if (account_info.role == ACCOUNT_ROLE_COMMUNITY_MAIN)
        {
            nodes.insert(account_info.account);
        }
        
        if (account_info.unit_id != 0 && unit_id != account_info.unit_id)
        {
            unit_id = account_info.unit_id;            
            //更新pub/unit设备联系人,不同楼栋才需要再刷
            GetFileUpdateContorlInstance()->CommunityFileUpdateFormateWeb(WEB_COMM_UPDATE_COMMUNITY_PUB_UNIT_CONTACT, community_id, unit_id, node_param, mac_param);
        }
    }    
    CommConfigHandle handle(community_id, unit_id, node_param, mac_param);
    if (!handle.InitSuccess())
    {
        AK_LOG_INFO << "CommConfigHandle init error, project type is correct." << community_id;    
        return;
    }      
    handle.UpdateSomeNodeDevContactList(nodes);
    
}


