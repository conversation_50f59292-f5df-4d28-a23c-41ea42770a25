#ifndef __COMMUNITY_DEV_CONTACT_H__
#define __COMMUNITY_DEV_CONTACT_H__
#include <string>
#include "dbinterface/CommunityInfo.h"
#include "AKCSMsg.h"
#include "dbinterface/PersonalThirdPartyCamera.h"
#include "dbinterface/ThirdPartyCamera.h"
#include "dbinterface/resident/ResidentPersonalAccount.h" 
#include <set>
#include <DevContact.h>
#include "UpdateConfigContext.h"

//社区设备Contact类
class CommConfigHandle; 

class CommunityDevContact : public DevContact
{
public:

    int SetCommunityInfo(   CommunityInfoPtr communit_info)
    {
      communit_info_ = communit_info;
      return 0;
    }
    
    int SetContext(ConfigContextPtr context)
    {
        context_ = context;
        return 0;
    }

    
    int UpdateCommContactFile(DEVICE_SETTING* your_dev, DEVICE_SETTING* your_list, std::vector<DEVICE_CONTACTLIST>& app_list,
                                  const DEVICE_SETTING* pub_device_list/*最外层*/, 
                                  const DEVICE_SETTING* unit_pub_device_list/*单元*/);
                                  
    int UpdateCommunityPublicContactFile(DEVICE_SETTING* pub_device_setting, const CommunitAccountInfoList& accounts, const MapNodeAppList &map_node_list, int type);

    int WritePubDevToFamilyDev(const DEVICE_CONTACTLIST& app, const DEVICE_SETTING* your_dev, std::stringstream& contact_file,
                 const DEVICE_SETTING* pub_device_list/*最外层*/, const DEVICE_SETTING* unit_pub_device_list/*单元*/, const std::set<std::string> &node_valid_mac_list);

    int UpdateContactFile(DEVICE_SETTING* your_dev, DEVICE_SETTING* your_list, std::vector<DEVICE_CONTACTLIST>& app_list,
                 const DEVICE_SETTING* pub_device_list/*最外层*/, const DEVICE_SETTING* unit_pub_device_list/*单元*/) override;

    void UpdateThirdCameraContactFile(const DEVICE_CONTACTLIST& app, DEVICE_SETTING* your_dev, std::stringstream& config_body,
                 const DEVICE_SETTING* pub_device_list/*最外层*/, const DEVICE_SETTING* unit_pub_device_list/*单元*/, const DEVICE_SETTING* your_list) override;

    void GetMasterGroupInfo(std::stringstream &config_body, const DEVICE_CONTACTLIST &app, int dev_network_group);
    void WriteCameraContactFile(ThirdPartyCamreaList camera_list, const DEVICE_SETTING* device_list, std::stringstream& config_body, int enable_ip_direct, const DEVICE_SETTING* indoor_dev);

private:      
        // 声明友元类
    friend class CommConfigHandle; 
    CommunityDevContact(const std::string& config_root_path  )
    {
        config_root_path_ = config_root_path;
    }


    int IsNewCommunity();
    
    bool IsManageBuilding(const DEVICE_SETTING* cur_dev, DEVICE_SETTING* your_dev);

    void WriteCommunityContact(std::stringstream &config_body, DEVICE_SETTING* your_dev);

    void WriteManagementContact(DEVICE_SETTING* your_dev, int enable_ip_direct,
                                      const DEVICE_SETTING* pub_device_list, const DEVICE_SETTING* unit_pub_device_list, std::stringstream &config_body);
   
    void CreateManagementDeviceContact(int enable_ip_direct, const DEVICE_SETTING* device_list, DEVICE_SETTING* your_dev, std::stringstream &config_body);

    bool CanWriteCurDevToPublicDevPubInfo(const DEVICE_SETTING * your_dev, const DEVICE_SETTING* cur_dev);

    bool CanWriteCurDevToUnitDevPubInfo(const DEVICE_SETTING* your_dev, const DEVICE_SETTING* cur_dev);
    void WriteContactNameByOrder(const int contact_display_order, const size_t length, const char* firstname, const char* lastname, char *name);

    int CreateFavoriteOrBlock(const std::set<std::string>& uuid_list, std::stringstream& config_body);

    void GetVideoRecordContact(const DEVICE_SETTING* cur_dev, const DEVICE_SETTING* your_dev, ContactKvList &kv);

    void WriteMatchDtmfToKv(ContactKvList& kv, const std::string& phone, const std::string& phone2, const std::string& phone3, const std::string& app_sip_account);

    MapUserAGDeviceList user_devices_map_; //k:user, v:macset
    std::string config_root_path_;
    CommunityInfoPtr communit_info_;    
    ConfigContextPtr context_;
};

void UpdateCommunityAccessGroupContactListByAccount(unsigned int community_id, const ResidentPerAccountList& account_list);
void UpdateCommunityAccessGroupContactListByAgid(unsigned int access_group_id, unsigned int community_id);
void UpdateCommunityAccessGroupContactListByAccountAndDevice(unsigned int community_id, const ResidentPerAccount& node, DEVICE_SETTING* pub_devlist);



#endif 
