#include "CommunityDevPushButton.h"
#include <sstream>
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/ExternPushButton.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "PersonalAccount.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "util_judge.h"
#include "DeviceSetting.h"
#include "dbinterface/PubDevMngList.h"
#include "AKCSMsg.h"
#include "AkLogging.h"
#include "DeviceControl.h"
#include "AkcsCommonSt.h"
#include "CommunityDevPushButton.h"
#include "DevConfig.h"


typedef std::vector<DEVICE_CONTACTLIST> NodeAppList;

CommunityDevPushButton::CommunityDevPushButton(const ConfigContextPtr& context)
{
    context_ = context;
    return ;
}

void CommunityDevPushButton::GenerateXMLForModule(const std::string& device_uuid, int module_id, std::stringstream& config_body, const std::string& mac) 
{
    std::vector<DevicePushButtonListInfo> module_sequence; 
    context_->GetDevicePushButtonListByDeviceUUIDAndModule(device_uuid, module_id, module_sequence);
    if (module_sequence.empty()) 
    {
        return;
    }
    
    for (const auto& item : module_sequence) {
        switch (item.type) {
            case WebCalleeType::WEB_NOT_SET: 
            {
                config_body << "<Key Index=\"" 
                            << item.sequence 
                            << "\" Type=\"" 
                            << DeviceCalleeType::DEV_NOT_SET 
                            << "\" Number=\"\"/>\n";
                break;
            }
            case WebCalleeType::WEB_SINGLE_CALL_PERSONAL: 
            {   
                ResidentPerAccount account;
                if (context_->GetCommunityPerAccountByUUID(item.callee_uuid, account) == 0) 
                {
                    config_body << "<Key Index=\"" 
                                << item.sequence 
                                << "\" Type=\"" 
                                << DeviceCalleeType::DEV_SINGLE_CALL 
                                << "\" Number=\"" 
                                << account.account 
                                << "\"/>\n";
                } 
                break;
            }
            case WebCalleeType::WEB_SINGLE_CALL_DEVICE: 
            {   
                DEVICE_SETTING* dev = context_->GetDeviceInGlobalByUUID(item.callee_uuid);
                if( dev == nullptr )
                {
                    continue;
                }
                config_body << "<Key Index=\"" 
                            << item.sequence 
                            << "\" Type=\"" 
                            << DeviceCalleeType::DEV_SINGLE_CALL 
                            << "\" Number=\"" 
                            << dev->sip_account 
                            << "\"/>\n";
                context_->ReleaseDeviceSetting(dev);
                break;
            }
            case WebCalleeType::WEB_GROUP_CALL_ROOM: 
            {   
                ResidentPerAccount account;
                if (context_->GetCommunityPerAccountByUUID(item.callee_uuid, account) == 0)
                {
                    config_body << "<Key Index=\"" 
                                << item.sequence 
                                << "\" Type=\"" 
                                << DeviceCalleeType::DEV_GROUP_CALL_ROOM 
                                << "\" Number=\"" 
                                << account.account << "\"/>\n";
                }
                break;
            }
            case WebCalleeType::WEB_ADMIN: 
            {   
                std::string mng_list;
                DEVICE_SETTING* dev_setting = context_->GetMacDeviceInGlobal(mac);
                if( dev_setting == nullptr )
                {
                    continue;
                }
                GetManageList(mng_list, context_, dev_setting);
                config_body << "<Key Index=\"" 
                            << item.sequence 
                            << "\" Type=\"" 
                            << DeviceCalleeType::DEV_ADMIN 
                            << "\" Number=\"" 
                            << mng_list << "\"/>\n";
                context_->ReleaseDeviceSetting(dev_setting);
                break;
            }
            default: 
            {
                AK_LOG_WARN << "Unknown CalleeType: " << item.type;
                break;
            }
        }
    }
}

void CommunityDevPushButton::GenerateXMLForDevicePushButton(DevicePushButton& push_button, std::stringstream& config_body, const string& mac) 
{
    // 生成模块 0 的 XML
    if (push_button.module_0_buttonNum > 0) {
        config_body << "<Module ID=\"0\">\n";
        GenerateXMLForModule(push_button.device_uuid, 0, config_body, mac);
        config_body << "</Module>\n";
    }

    // 生成模块 1 的 XML
    if (push_button.module_1_buttonNum > 0) {
        config_body << "<Module ID=\"1\">\n";
        GenerateXMLForModule(push_button.device_uuid, 1, config_body, mac);
        config_body << "</Module>\n";
    }

    // 生成模块 2 的 XML
    if (push_button.module_2_buttonNum > 0) {
        config_body << "<Module ID=\"2\">\n";
        GenerateXMLForModule(push_button.device_uuid, 2, config_body, mac);
        config_body << "</Module>\n";
    }

    // 生成模块 3 的 XML
    if (push_button.module_3_buttonNum > 0) {
        config_body << "<Module ID=\"3\">\n";
        GenerateXMLForModule(push_button.device_uuid, 3, config_body, mac);
        config_body << "</Module>\n";
    }

    // 生成模块 4 的 XML
    if (push_button.module_4_buttonNum > 0) {
        config_body << "<Module ID=\"4\">\n";
        GenerateXMLForModule(push_button.device_uuid, 4, config_body, mac);
        config_body << "</Module>\n";
    }

    // 生成模块 5 的 XML
    if (push_button.module_5_buttonNum > 0) {
        config_body << "<Module ID=\"5\">\n";
        GenerateXMLForModule(push_button.device_uuid, 5, config_body, mac);
        config_body << "</Module>\n";
    }
}


void CommunityDevPushButton::UpdateCommnityDevPushButtonContact(const string& mac, std::stringstream& config_body)
{
    config_body << "<MDKeyData>\n";

    DEVICE_SETTING* dev = context_->GetMacDeviceInGlobal(mac);
    if( dev == nullptr )
    {
        return;
    }
    context_->InitPushButton(dev->project_uuid);
    AK_LOG_INFO << "Update Commnity Dev Push Button Contact . dev->uuid: " << dev->uuid;
    //获取该dev下的所有按键信息
    DevicePushButton dev_push_button;
    if (0 != context_->GetDevicePushButtonByDeviceUUID(dev->uuid, dev_push_button))
    {
        config_body <<"</MDKeyData>\n";
        AK_LOG_WARN << "The DevicePushButton cannot be obtained, DeviceUUID: " << dev->uuid;
        context_->ReleaseDeviceSetting(dev);
        return;
    }
    GenerateXMLForDevicePushButton(dev_push_button, config_body, mac);
    config_body <<"</MDKeyData>\n";
    context_->ReleaseDeviceSetting(dev);
}

