#ifndef __COMMUNITY_DEVICE_PUSH_BUTTON_H__
#define __COMMUNITY_DEVICE_PUSH_BUTTON_H__

#include <vector>
#include <string>
#include "AkcsCommonSt.h"
#include "dbinterface/DevicePushButtonList.h"
#include "dbinterface/ExternPushButton.h"
#include "UpdateConfigContext.h"


class CommunityDevPushButton
{

public:
    enum WebCalleeType
    {
        WEB_NOT_SET = 0,    // 未设置
        WEB_SINGLE_CALL_PERSONAL = 1,    // 单呼人
        WEB_SINGLE_CALL_DEVICE = 2,    // 单呼设备
        WEB_GROUP_CALL_ROOM = 3,    // 群呼房间
        WEB_ADMIN = 4       // 管理员
    };
    //设备不需要区分单呼类型
    enum DeviceCalleeType
    {
        DEV_NOT_SET = 0,    // 未设置
        DEV_SINGLE_CALL = 1,    // 单呼
        DEV_GROUP_CALL_ROOM = 2,    // 群呼房间
        DEV_ADMIN = 3       // 管理员
    };
    
    CommunityDevPushButton(const ConfigContextPtr& context);
    ~CommunityDevPushButton(){};

public:
 
    void UpdateCommnityDevPushButtonContact(const std::string& mac, std::stringstream& config_body);
    void GenerateXMLForModule(const std::string& device_uuid, int module_id, std::stringstream& config_body, const std::string& mac); 
    void GenerateXMLForDevicePushButton(DevicePushButton& push_button, std::stringstream& config_body, const string& mac);
private:
    ConfigContextPtr context_;
};


#endif

