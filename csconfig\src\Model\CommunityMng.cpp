#include <sstream>
#include "AkLogging.h"
#include "CommunityMng.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "dbinterface/Account.h"
#include "AkcsWebMsgSt.h"
#include "dbinterface/CommunityUnit.h"

CCommunitMng* GetCommunitMngInstance()
{
    return CCommunitMng::GetInstance();
}

CCommunitMng* CCommunitMng::instance = NULL;

CCommunitMng* CCommunitMng::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CCommunitMng();
    }

    return instance;
}

std::string CCommunitMng::GetCommunityUnitName(int unit_id)
{
    return dbinterface::CommunityUnit::GetCommunityUnitName(unit_id);

}

