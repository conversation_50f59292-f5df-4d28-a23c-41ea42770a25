#ifndef __COMMUNIITY_MNG_H__
#define __COMMUNIITY_MNG_H__
#include <vector>

#include <boost/noncopyable.hpp>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "ConfigDef.h"
#include "AKCSMsg.h"
#include "AdaptUtility.h"
#include "AkcsCommonSt.h"



class CCommunitMng : public boost::noncopyable
{
public:
    CCommunitMng()
    {
    }
    ~CCommunitMng()
    {
    }
    std::string GetCommunityUnitName(int unit_id);
    static CCommunitMng* GetInstance();
private:

    static CCommunitMng* instance;

};

CCommunitMng* GetCommunitMngInstance();

#endif //__COMMUNIITY_MNG_H__
