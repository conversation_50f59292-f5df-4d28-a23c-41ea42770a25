#include <sstream>
#include "ConfigCommon.h"
#include <string.h>
#include "util_string.h"
#include "util.h"
#include "AkcsCommonDef.h"
#include "ConfigDef.h" 
#include "AkcsWebMsgSt.h"
#include "dbinterface/VersionModel.h"
#include "util_relay.h"

extern CSCONFIG_CONF gstCSCONFIGConf;


void UpdateUcloudVideoBitRate(const std::string &firmware, std::stringstream &config)
{
    if(gstCSCONFIGConf.server_type != ServerArea::ucloud)
    {
        return;
    }

    int model = 0;
    int big_ver = 0;
    int small_ver = 0;
    if (false == GetFirmwareInfo(firmware, model, big_ver, small_ver))
    {
        return;
    }

    //如果版本>10 或者版本=10且小版本大于指定值 代表需要更新

    //2024.10.8 新增下面三种机型视频参数下发
    //E12系列机型，Model为312，版本号大于等于312.30.10.9 
    //X915V1，Model为915，版本号大于等于915.30.10,14 
    //X915V2，Model为2915，版本号大于等于2915.30,10.9 
    if (
            (model == SOFTWARE_R20V3 && ((big_ver == 10 && small_ver >= 9) || big_ver > 10))
            || (model == SOFTWARE_X915 && ((big_ver == 10 && small_ver >= 14 ) || big_ver > 10))
            || (model == SOFTWARE_X915_V2 && ((big_ver == 10 && small_ver >= 9 ) || big_ver > 10))
            || (model == SOFTWARE_E12_SV823 && ((big_ver == 10 && small_ver >= 9 ) || big_ver > 10))
            || (model == SOFTWARE_X910)
       )
    {
        config << "Config.DoorSetting.RTSP.H264Resolution2 = 5\n";
        config << "Config.DoorSetting.RTSP.H264BitRate2 = 1024\n";
        config << "Config.Account1.Video00.ProfileLevel = 4\n";
        config << "Config.Account1.Video00.MaxBR = 1024\n";
    }
    return;
}


void UpdateSipSrtpConfig(int sip_type, uint64_t fun_bit, std::stringstream &config)
{
    if (DevMngSipType_TLS == sip_type && SwitchHandle(fun_bit, FUNC_DEV_SUPPORT_SRTP))
    {
        config << "Config.Account1.ENCRYPTION.SRTPEncryption = 2\n";
    }
    else
    {
        config << "Config.Account1.ENCRYPTION.SRTPEncryption = 0\n";
    }

    return;
}

void UpdateAuxCameraConfig(uint64_t fun_bit, std::stringstream &config)
{
    if (SwitchHandle(fun_bit, FUNC_DEV_SUPPORT_MULTI_MONITOR))
    {
        config << CONFIG_RTSP_AUX_CAMERA_H264_RESOLUTION << "5\n";
        config << CONFIG_RTSP_AUX_CAMERA_H264_FRAMERATE  << "30\n";
        config << CONFIG_RTSP_AUX_CAMERA_H264_BITRATE    << "1024\n";
    }

    return;
}

void UpdateHighResolutionVideoResolution(short dev_firmware, std::stringstream &config)
{
    FirmwareList high_resolution_list;
    dbinterface::VersionModel::GetHighResolutionList(high_resolution_list);
    if (high_resolution_list.count(dev_firmware))
    {
        // 云上二路流：2K
        config << "Config.DoorSetting.RTSP.H264Resolution = 10\n";

        // 默认流：VGA
        config << "Config.DoorSetting.RTSP.H264Resolution2 = 3\n";
    }
}

void WriteVoiceAssistantConfig(std::stringstream &config, DEVICE_SETTING *dev)
{
    //上云的室内机语音助手默认走云转发，当前尚未收费
    config << "Config.Settings.BELAASSISTANT.CloudClient=1\n";
    int model = 0;
    int big_ver = 0;
    int small_ver = 0;
    if (false == GetFirmwareInfo(dev->SWVer, model, big_ver, small_ver))
    {
        return;
    }
    //937设备不收费，过期时间要求写死覆盖
    if(model == SOFTWARE_937)
    {
        config << "Config.Settings.BELAASSISTANT.ExpireTime=2299-05-31 23:59:59\n";
    }
}

void WriteExtraDeviceEnableConfig(std::stringstream &config, int device_index, int enable, const std::string& device_address)
{
    if (device_index == 1)
    {
        config << CONFIG_INDOOR_EXTRELAY_DEVICE_ENABLE << "=" << enable << "\n";
        if (enable && !device_address.empty())
        {
            config << CONFIG_INDOOR_EXTRELAY_DEVICE_ADDRESS << "=" << device_address << "\n";
        }
    }
    else if (device_index == 2)
    {
        config << CONFIG_INDOOR_EXTRELAY2_DEVICE_ENABLE << "=" << enable << "\n";
        if (enable && !device_address.empty())
        {
            config << CONFIG_INDOOR_EXTRELAY2_DEVICE_ADDRESS << "=" << device_address << "\n";
        }
    }
    else if (device_index == 3)
    {
        config << CONFIG_INDOOR_EXTRELAY3_DEVICE_ENABLE << "=" << enable  << "\n";
        if (enable && !device_address.empty())
        {
            config << CONFIG_INDOOR_EXTRELAY3_DEVICE_ADDRESS << "=" << device_address << "\n";
        }
    }
}

void WriteExtRelayStatusConfig(std::stringstream &config, int device_index, int output_index, int status, int function_value, int hold_delay, const std::string& display_name)
{
    if (device_index == 1)
    {
        config << CONFIG_INDOOR_EXTRELAY_STATUS << output_index << "=" << status << "\n";
        config << CONFIG_INDOOR_EXTRELAY_FUNCTION << output_index << "=" << function_value << "\n";
        config << CONFIG_INDOOR_EXTRELAY_HOLD_DELAY << output_index << "=" << hold_delay << "\n";
        config << CONFIG_INDOOR_EXTRELAY_INTERVAL << output_index << "=" << hold_delay << "\n";
        config << CONFIG_INDOOR_EXTRELAY_DISPLAY_NAME << output_index << "=" << display_name << "\n";
    }
    else if (device_index == 2)
    {
        config << CONFIG_INDOOR_EXTRELAY2_STATUS << output_index << "=" << status << "\n";
        config << CONFIG_INDOOR_EXTRELAY2_FUNCTION << output_index << "=" << function_value << "\n";
        config << CONFIG_INDOOR_EXTRELAY2_HOLD_DELAY << output_index << "=" << hold_delay << "\n";
        config << CONFIG_INDOOR_EXTRELAY2_INTERVAL << output_index << "=" << hold_delay << "\n";
        config << CONFIG_INDOOR_EXTRELAY2_DISPLAY_NAME << output_index << "=" << display_name << "\n";
    }
    else if (device_index == 3)
    {
        config << CONFIG_INDOOR_EXTRELAY3_STATUS << output_index << "=" << status << "\n";
        config << CONFIG_INDOOR_EXTRELAY3_FUNCTION << output_index << "=" << function_value << "\n";
        config << CONFIG_INDOOR_EXTRELAY3_HOLD_DELAY << output_index << "=" << hold_delay << "\n";
        config << CONFIG_INDOOR_EXTRELAY3_INTERVAL << output_index << "=" << hold_delay << "\n";
        config << CONFIG_INDOOR_EXTRELAY3_DISPLAY_NAME << output_index << "=" << display_name << "\n";
    }
}

void WriteDigitalOutputConfig(std::stringstream &config, int device_index, int output_index, int status, int function_value, int hold_delay, const std::string& display_name)
{
    if (device_index == 1)
    {
        config << CONFIG_INDOOR_DIGITAL_OUTPUT_STATUS << output_index << "=" << status << "\n";
        config << CONFIG_INDOOR_DIGITAL_OUTPUT_FUNCTION << output_index << "=" << function_value << "\n";
        config << CONFIG_INDOOR_DIGITAL_OUTPUT_HOLD_DELAY << output_index << "=" << hold_delay << "\n";
        config << CONFIG_INDOOR_DIGITAL_OUTPUT_INTERVAL << output_index << "=" << hold_delay << "\n";
        config << CONFIG_INDOOR_DIGITAL_OUTPUT_DISPLAY_NAME << output_index << "=" << display_name << "\n";
    }
    else if (device_index == 2)
    {
        config << CONFIG_INDOOR_DIGITAL_OUTPUT2_STATUS << output_index << "=" << status << "\n";
        config << CONFIG_INDOOR_DIGITAL_OUTPUT2_FUNCTION << output_index << "=" << function_value << "\n";
        config << CONFIG_INDOOR_DIGITAL_OUTPUT2_HOLD_DELAY << output_index << "=" << hold_delay << "\n";
        config << CONFIG_INDOOR_DIGITAL_OUTPUT2_INTERVAL << output_index << "=" << hold_delay << "\n";
        config << CONFIG_INDOOR_DIGITAL_OUTPUT2_DISPLAY_NAME << output_index << "=" << display_name << "\n";
    }
    else if (device_index == 3)
    {
        config << CONFIG_INDOOR_DIGITAL_OUTPUT3_STATUS << output_index << "=" << status << "\n";
        config << CONFIG_INDOOR_DIGITAL_OUTPUT3_FUNCTION << output_index << "=" << function_value << "\n";
        config << CONFIG_INDOOR_DIGITAL_OUTPUT3_HOLD_DELAY << output_index << "=" << hold_delay << "\n";
        config << CONFIG_INDOOR_DIGITAL_OUTPUT3_INTERVAL << output_index << "=" << hold_delay << "\n";
        config << CONFIG_INDOOR_DIGITAL_OUTPUT3_DISPLAY_NAME << output_index << "=" << display_name << "\n";
    }
}

void WriteDigitalInputConfigs(std::stringstream &config, int device_index, int input_index, int output_value, int show_popup, int trigger_mode, const std::string& display_name, int enable_switch)
{
    if (device_index == 1)
    {
        config << CONFIG_INDOOR_DIGITALINPUT_LINK_RELAY_OR_OUTPUT << input_index << "=" << output_value << "\n";
        config << CONFIG_INDOOR_DIGITALINPUT_STATUS << input_index << "=" << enable_switch << "\n";
        config << CONFIG_INDOOR_DIGITALINPUT_SHOW_POPUP << input_index << "=" << show_popup << "\n";
        config << CONFIG_INDOOR_DIGITALINPUT_TRIGGER_MODE << input_index << "=" << trigger_mode << "\n";
        config << CONFIG_INDOOR_DIGITALINPUT_DISPLAY_NAME << input_index << "=" << display_name << "\n";
    }
    else if (device_index == 2)
    {
        config << CONFIG_INDOOR_DIGITALINPUT2_LINK_RELAY_OR_OUTPUT << input_index << "=" << output_value << "\n";
        config << CONFIG_INDOOR_DIGITALINPUT2_STATUS << input_index << "=" << enable_switch << "\n";
        config << CONFIG_INDOOR_DIGITALINPUT2_SHOW_POPUP << input_index << "=" << show_popup << "\n";
        config << CONFIG_INDOOR_DIGITALINPUT2_TRIGGER_MODE << input_index << "=" << trigger_mode << "\n";
        config << CONFIG_INDOOR_DIGITALINPUT2_DISPLAY_NAME << input_index << "=" << display_name << "\n";
    }
    else if (device_index == 3)
    {
        config << CONFIG_INDOOR_DIGITALINPUT3_LINK_RELAY_OR_OUTPUT << input_index << "=" << output_value << "\n";
        config << CONFIG_INDOOR_DIGITALINPUT3_STATUS << input_index << "=" << enable_switch << "\n";
        config << CONFIG_INDOOR_DIGITALINPUT3_SHOW_POPUP << input_index << "=" << show_popup << "\n";
        config << CONFIG_INDOOR_DIGITALINPUT3_TRIGGER_MODE << input_index << "=" << trigger_mode << "\n";
        config << CONFIG_INDOOR_DIGITALINPUT3_DISPLAY_NAME << input_index << "=" << display_name << "\n";
    }
}

void ResetExtraDeviceConfigs(std::stringstream &config) 
{
    // 重置所有三个外接设备开关为关闭
    std::set<std::string> config_keys;
    
    // 收集所有需要重置的配置项
    // Device 1
    config_keys.insert(CONFIG_INDOOR_EXTRELAY_DEVICE_ENABLE);
    for (int i = 1; i <= 8; i++) 
    {
        config_keys.insert(CONFIG_INDOOR_EXTRELAY_STATUS + std::to_string(i));
        config_keys.insert(CONFIG_INDOOR_DIGITAL_OUTPUT_STATUS + std::to_string(i));
        config_keys.insert(CONFIG_INDOOR_DIGITALINPUT_STATUS + std::to_string(i));
    }
    
    // Device 2
    config_keys.insert(CONFIG_INDOOR_EXTRELAY2_DEVICE_ENABLE);
    for (int i = 1; i <= 8; i++) 
    {
        config_keys.insert(CONFIG_INDOOR_EXTRELAY2_STATUS + std::to_string(i));
        config_keys.insert(CONFIG_INDOOR_DIGITAL_OUTPUT2_STATUS + std::to_string(i));
        config_keys.insert(CONFIG_INDOOR_DIGITALINPUT2_STATUS + std::to_string(i));
    }
    
    // Device 3
    config_keys.insert(CONFIG_INDOOR_EXTRELAY3_DEVICE_ENABLE);
    for (int i = 1; i <= 8; i++) 
    {
        config_keys.insert(CONFIG_INDOOR_EXTRELAY3_STATUS + std::to_string(i));
        config_keys.insert(CONFIG_INDOOR_DIGITAL_OUTPUT3_STATUS + std::to_string(i));
        config_keys.insert(CONFIG_INDOOR_DIGITALINPUT3_STATUS + std::to_string(i));
    }
    
    // 统一对所有配置项赋值为0
    for (const auto& key : config_keys) 
    {
        config << key << "=0\n";
    }
}
