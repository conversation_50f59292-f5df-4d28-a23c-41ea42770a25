#ifndef CONFIG_COMMON_H_
#define CONFIG_COMMON_H_
#include <sstream>
#include <string>
#include <vector>
#include "AkcsCommonSt.h"
#include "dbinterface/resident/ExtraDevice.h"
#include "dbinterface/resident/ExtraDeviceRelayList.h"
#include "dbinterface/resident/ExtraDeviceRelayAction.h"

void UpdateUcloudVideoBitRate(const std::string &firmware, std::stringstream &config);
void UpdateSipSrtpConfig(int sip_type, uint64_t fun_bit, std::stringstream &config);
void UpdateAuxCameraConfig(uint64_t fun_bit, std::stringstream &config);
void UpdateHighResolutionVideoResolution(short dev_firmware, std::stringstream &config);
void WriteVoiceAssistantConfig(std::stringstream &config, DEVICE_SETTING *dev);

// 外接设备配置写入公共函数
void WriteExtraDeviceEnableConfig(std::stringstream &config, int device_index, int enable, const std::string& device_address = "");
void WriteExtRelayStatusConfig(std::stringstream &config, int device_index, int output_index, int status, int function_value, int hold_delay, const std::string& display_name);
void WriteDigitalOutputConfig(std::stringstream &config, int device_index, int output_index, int status, int function_value, int hold_delay, const std::string& display_name);
void WriteDigitalInputConfigs(std::stringstream &config, int device_index, int input_index, int output_value, int show_popup, int trigger_mode, const std::string& display_name, int enable_switch);
void ResetExtraDeviceConfigs(std::stringstream &config);

#endif
