#include <sstream>
#include "ContactCommon.h"
#include <string.h>
#include <util.h>
#include "DevContact.h"
#include "util_relay.h"
#include "AdaptUtility.h"
#include "AkcsCommonDef.h"
#include "util_judge.h"

void GetContactCommonStr(std::stringstream &str, const ContactKvList &kv)
{
    for (const auto& pair : kv)
    {
        char value[256] = "";
        ChangeSpecialXmlChar(value, 256, pair.second.c_str(), pair.second.size());

        switch(pair.first)
        {

            case CONTACT_ATTR::CALL_LOOP :
                str << " CallLoop=\""<< value << "\"";
                break;        
            case CONTACT_ATTR::ROOM :
                str << " Room=\""<< value << "\"";
                break;
            case CONTACT_ATTR::ROOM_N :
                str << " RoomN=\""<< value << "\"";
                break;
            case CONTACT_ATTR::UNIT_APT :
                str << " UnitApt=\""<< value << "\"";
                break;            
            case CONTACT_ATTR::IP_DIRECT :
                str << " IpDirect=\""<< value << "\"";
                break;              
            case CONTACT_ATTR::TYPE :
                str << " Type=\""<< value << "\"";
                break;
            case CONTACT_ATTR::NODE :
                str << " Node=\""<< value << "\"";
                break;
            case CONTACT_ATTR::UID :
                str << " UID=\""<< value << "\"";
                break;
            case CONTACT_ATTR::NAME :
                str << " Name=\""<< value << "\"";
                break;
            case CONTACT_ATTR::SIP :
                str << " SIP=\""<< value << "\"";
                break;
            case CONTACT_ATTR::SIP0 :
                str << " SIP0=\""<< value << "\"";
                break;              
            case CONTACT_ATTR::IP :
                str << " IP=\""<< value << "\"";
                break;
            case CONTACT_ATTR::OPTIONIP :
                str << " OptionIP=\""<< value << "\"";
                break;
            case CONTACT_ATTR::LAND :
                str << " Land=\""<< value << "\"";
                break;            
            case CONTACT_ATTR::MAC :
                str << " MAC=\""<< value << "\"";
                break;
            case CONTACT_ATTR::RTSPPWD :
                str << " RTSPPwd=\""<< value << "\"";
                break;                
            case CONTACT_ATTR::PUB :
                str << " Pub=\""<< value << "\"";
                break;
            case CONTACT_ATTR::RELAY :
                str << " Relay=\""<< value << "\"";
                break;
            case CONTACT_ATTR::SEC_RELAY :
                str << " SecurityRelay=\""<< value << "\"";
                break;
            case CONTACT_ATTR::MATCH_DTMF1 :
                str << " MatchDtmf1=\""<< value << "\"";
                break;
            case CONTACT_ATTR::MATCH_DTMF2 :
                str << " MatchDtmf2=\""<< value << "\"";
                break;
            case CONTACT_ATTR::MATCH_DTMF3 :
                str << " MatchDtmf3=\""<< value << "\"";
                break;
            case CONTACT_ATTR::MATCH_DTMF4 :
                str << " MatchDtmf4=\""<< value << "\"";
                break;
            case CONTACT_ATTR::MASTER :
                str << " Master=\""<< value << "\"";
                break; 
            case CONTACT_ATTR::GROUP_CALL :
                str << " GroupCall=\""<< value << "\"";
                break;             
            case CONTACT_ATTR::APP :
                str << " APP=\""<< value << "\"";
                break;
            case CONTACT_ATTR::UNIT :          
                str << " Unit=\""<< value << "\""; 
                break;
            case CONTACT_ATTR::SEQ :
                // SEQ 和 SEQ2 是 key value一起 不能再转义
                str << pair.second;
                break;               
            case CONTACT_ATTR::SEQ2 :
                str << pair.second;
                break;             
            case CONTACT_ATTR::URL :
                str << " Url=\""<< value << "\"";
                break;
            case CONTACT_ATTR::BONDMAC :
                str << " BondMAC=\""<< value << "\"";
                break;
            case CONTACT_ATTR::RTSPUSER :
                str << " RTSPUser=\""<< value << "\"";
                break;    
            case CONTACT_ATTR::MONITOR_TYPE :
                str << " MonitorType =\""<< value << "\"";
                break;
            case CONTACT_ATTR::NOT_MONITOR :
                str << " NotMonitor =\""<< value << "\"";
                break;
            case CONTACT_ATTR::REPOST :
                str << " Repost =\""<< value << "\"";
                break;
            case CONTACT_ATTR::IS_DISPLAY :
                {
                    //若值为1则直接不写
                    if( ATOI(value) == 0 )
                    {
                        str << " IsDisplay =\"0\"";
                    }
                    break;
                }
            case CONTACT_ATTR::SINGLECALL :
                str << " SingleCall =\"" << value << "\"";
                break;
            case CONTACT_ATTR::SEQCALLENABLE :
                str << " SeqCallEnable =\"" << value << "\"";
                break;
            case CONTACT_ATTR::SEQCALLNUM :
                str << " SeqCallNum =\"" << value << "\"";
                break;
            case CONTACT_ATTR::RECORD_VIDEO :
                str << " RecordVideo =\"" << value << "\"";
                break;
            case CONTACT_ATTR::CAMERA_NUM :
                str << " CameraNum =\"" << value << "\"";
                break;
            case CONTACT_ATTR::BUILDING_ID :
                str << " BuildingId =\"" << value << "\"";
                break;
        }
    }
}



void GetContactStr(std::stringstream &str, const ContactKvList &kv)
{
    str << "<Contact ";
    GetContactCommonStr(str, kv);
    str << "/>\n";
}

void GetGroupStr(std::stringstream &str, const ContactKvList &kv)
{
    str << "<Group ";
    GetContactCommonStr(str, kv);
    str << ">\n";
}

void GetContactHeadStr(std::stringstream &str)
{
    str << "\n<?xml version=\"1.0\" encoding=\"UTF-8\" ?>\n";
    str << "<ContactData>\n";
}

void GetContactTailStr(std::stringstream &str)
{
    str << "</Group>\n";
    str << "</ContactData>\n";

}

void WriteRelayContact(const uint32_t dev_type, const char* relay, const char* se_relay, ContactKvList &kv)
{
    if(!akjudge::DevDoorType(dev_type))
    {
        return;
    }
    std::string relay_str, security_relay_str;
    std::vector<RELAY_INFO> relays;
    ParseRelay(relay, relays);
    relay_str = GetRelayContactStr(relays);

    std::vector<RELAY_INFO> security_relay_infos;
    ParseRelay(se_relay, security_relay_infos);
    security_relay_str = GetRelayContactStr(security_relay_infos);

    kv.push_back(std::make_pair(CONTACT_ATTR::RELAY, relay_str));
    if (!security_relay_str.empty())
    {
        kv.push_back(std::make_pair(CONTACT_ATTR::SEC_RELAY, security_relay_str));
    }
}

std::string GetAnalogDeviceUID(uint64_t id)
{
    char uid[64] = "";
    snprintf(uid, sizeof(uid), "H%09lu", id);
    return uid;
}