#ifndef CONTACT_COMMON_H_
#define CONTACT_COMMON_H_
#include <sstream>
#include "ContactCommon.h"
#include <string>
#include <vector>
#include "AkcsCommonSt.h"

enum CONTACT_ATTR
{
    TYPE,
    NODE,
    UID,
    NAME,
    SIP,
    IP,
    OPTIONIP,
    MAC,
    RTSPPWD,
    PUB,
    RELAY,
    SEC_RELAY,
    SEQ,
    SEQ2,
    SIP0,
    LAND,
    GROUP_CALL,
    MASTER,
    MATCH_DTMF1,
    MATCH_DTMF2,
    MATCH_DTMF3,
    MATCH_DTMF4,
    APP,
    UNIT,
    ROOM,
    ROOM_N,
    UNIT_APT,
    IP_DIRECT,
    CALL_LOOP,
    UUID,
    URL,
    BONDMAC,
    RTSPUSER,
    MONITOR_TYPE,
    NOT_MONITOR,
    REPOST,
    IS_DISPLAY,
    SINGLECALL,
    SEQCALL<PERSON>ABLE,
    SEQCALLNUM,
    RECORD_VIDEO,
    CAMERA_NUM,
};

typedef std::vector<std::pair<int, std::string> > ContactKvList;
void GetContactCommonStr(std::stringstream &str, const ContactKvList &kv);
void GetContactStr(std::stringstream &str, const ContactKvList &kv);
void GetGroupStr(std::stringstream &str, const ContactKvList &kv);
void WriteRelayContact(const uint32_t dev_type, const char* relay, const char* se_relay, ContactKvList &kv);
std::string GetAnalogDeviceUID(uint64_t id);

#endif
