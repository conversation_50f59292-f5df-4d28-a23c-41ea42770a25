#include "DataAnalysis.h"
#include "json/json.h"
#include "DataAnalysisSpecial.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisTableParse.h"
#include "AkLogging.h"

extern DataAnalysisDBHandlerMap g_db_handler_map;
using namespace Akcs;

DataAnalysis::DataAnalysis(const std::string data)
{
    original_data = data;
    is_special_ = 0;
    Init();
}

DataAnalysis::~DataAnalysis()
{

}
/*
{
    "type": "特殊类型 | common",
    "specialValue": {.....},
    "insert": {
        "表名1": [{
            "字段名1": "值1",
            "字段名2": "值2"
            ...
        }]
        ...
    },
    "delete": {
        "表名1": [{
            "字段名1": "值1",
            "字段名2": "值2"
            ...
        }]
        ...
    }
}
// 数组第一个下标是修改前的，第二个下标是修改后的
{
"type": "common",
"update": {
    "CommPerPrivateKey": [
     [{
        "ID": 3,
        "Account": "**********"
      }, {
        "ID": 3,
        "Account": "**********"
     }],
 
    [{
        "ID": 4,
        "Account": "**********"
    }, {
        "ID": 4,
        "Account": "**********"
    }]
   ]
  }
}
*/
int DataAnalysis::Init()
{
    Json::Reader reader;
    Json::Value root;
    Json::Value insert;
    Json::Value del;
    Json::Value update;
    Json::Value special;
    
    DataAnalysisTableInfoMap data_info;
    
    if (!reader.parse(original_data, root))
    {
        AK_LOG_WARN <<  "Parse json error.data=" << original_data << " error msg=" << reader.getFormatedErrorMessages();
        return -1;
    }
    if (!root.isMember(da_type))
    {
        AK_LOG_WARN <<  "Parse json error. no have section type. data=" << original_data;
        return -1;
    }
    type_ = root[da_type].asString();
    //和前端约定project_type, 0表示none，1表示社区，2表示办公，3表示单住户
    if (root[da_project_type].asInt() == 2)
    {
        project_type_ = 1;
    }
    else if (root[da_project_type].asInt() == 3)
    {
        project_type_ = 2;
    }
    else
    {
        project_type_ = 0;
    }
    special = root[da_special_value];
    insert = root[da_insert];
    del = root[da_delete];
    update = root[da_update];

    if (type_ == "common")
    {
        if (!insert.empty())
        {
            Json::Value::Members array_member = insert.getMemberNames(); 
            for(Json::Value::Members::iterator iter = array_member.begin(); iter != array_member.end(); ++iter)
            {
                std::string sheet= *iter;
                Json::Value sql_arr = insert[sheet];
                DataAnalysisRowList kvlist;
                for (unsigned int i = 0; i < sql_arr.size(); i++)
                {
                    DataAnalysisSqlKV kv;
                    for (auto const& key : sql_arr[i].getMemberNames()) {
                        kv.insert(std::map<std::string, std::string>::value_type(key, sql_arr[i][key].asString()));
                    }
                    kvlist.push_back(kv);
                }
                AK_LOG_INFO <<  "DataAnalysis insert " << sheet << " data.";
                db_insert_info_.insert(std::map<std::string, DataAnalysisRowList>::value_type(sheet, kvlist));
            }
        }

        if (!del.empty())
        {
            Json::Value::Members array_member = del.getMemberNames(); 
            for(Json::Value::Members::iterator iter = array_member.begin(); iter != array_member.end(); ++iter)
            {
                std::string sheet= *iter;
                Json::Value sql_arr = del[sheet];
                DataAnalysisRowList kvlist;
                for (unsigned int i = 0; i < sql_arr.size(); i++)
                {
                    DataAnalysisSqlKV kv;
                    for (auto const& key : sql_arr[i].getMemberNames()) {
                        kv.insert(std::map<std::string, std::string>::value_type(key, sql_arr[i][key].asString()));
                    }
                    kvlist.push_back(kv);
                }
                AK_LOG_INFO <<  "DataAnalysis delete " << sheet << " data.";
                db_delete_info_.insert(std::map<std::string, DataAnalysisRowList>::value_type(sheet, kvlist));
            }
        }        

        if (!update.empty())
        {
            Json::Value::Members array_member = update.getMemberNames(); 
            for(Json::Value::Members::iterator iter = array_member.begin(); iter != array_member.end(); ++iter)
            {
                std::string sheet= *iter;
                std::cout << sheet << std::endl;
                Json::Value sql_arr = update[sheet];
                DataAnalysisRowList kvlist;
                for (unsigned int i = 0; i < sql_arr.size(); i++)
                {
                    Json::Value data = sql_arr[i];
                    if (data.size() != 2)
                    {
                        AK_LOG_WARN << "Data analysis update, data size error!";
                        continue;
                    }
                    
                    int before = 0;//直接放入常量会报错
                    int after = 1;
                    DataAnalysisSqlKV kv1;
                    DataAnalysisSqlKV kv2;
                    for (auto const& key : data[before].getMemberNames())
                    {
                        if (!(data[before][key].isArray() ||
                            data[before][key].isObject()))
                        {
                            kv1.insert(std::map<std::string, std::string>::value_type(key, data[before][key].asString()));
                        }
                    }
                    kvlist.push_back(kv1);
                    
                    for (auto const& key : data[after].getMemberNames())
                    {
                        if (!(data[after][key].isArray() ||
                            data[after][key].isObject()))
                        {
                            kv2.insert(std::map<std::string, std::string>::value_type(key, data[after][key].asString()));
                        }
                    }
                    kvlist.push_back(kv2);
                    
                }
                AK_LOG_INFO <<  "DataAnalysis update " << sheet << " data.";
                db_update_info_.insert(std::map<std::string, DataAnalysisRowList>::value_type(sheet, kvlist));
            }
        }
        
    }
    else
    {
        is_special_ = 1;
        for (auto const& key : special.getMemberNames()) {
            std::string member_arr;
            if (special[key].isArray())
            {
                for (unsigned int i = 0; i < special[key].size(); i++)
                {
                    member_arr += special[key][i].asString();
                    member_arr += ",";
                }
                special_info_.insert(std::map<std::string, std::string>::value_type(key, member_arr));
            }
            else
            {
                special_info_.insert(std::map<std::string, std::string>::value_type(key, special[key].asString()));
            }
        }
    }    
    return 0;
}

int DataAnalysis::Analysis()
{
    if (is_special_ == 1)
    {
        DaSpecialHandler(type_, special_info_, context_);
        context_.DispatchUpdateConfigInfo();
        return 0;
    }
    
    DataAnalysisTableInfoMap::iterator tab_it;
    for (tab_it = db_delete_info_.begin(); tab_it != db_delete_info_.end(); ++tab_it)
    {
        DataAnalysisDBHandlerMap::iterator handler_it;
        handler_it = g_db_handler_map.find(tab_it->first);
        if (handler_it == g_db_handler_map.end())
        {
            AK_LOG_WARN << "Mysql delete operation. table:" << tab_it->first << " is no need do anything.";
            continue;
        }
        DataAnalysisDBHandlerPtr ptr = handler_it->second;
        
        for (const auto &data : tab_it->second)
        {
            const DataAnalysisColumnList detect_key = ptr->get_detect_func_();
            DataAnalysisTableParse parse(detect_key, data);
            parse.SetOperation(DataAnalysisTableParse::DBHandleType::DA_OPERATION_DELETE);
            parse.SetProjectType(project_type_);
            DataAnalysisTableHandler(parse, ptr, context_);
        }

    }

    for (tab_it = db_insert_info_.begin(); tab_it != db_insert_info_.end(); ++tab_it)
    {
        DataAnalysisDBHandlerMap::iterator handler_it;
        handler_it = g_db_handler_map.find(tab_it->first);
        if (handler_it == g_db_handler_map.end())
        {
            AK_LOG_WARN << "Mysql insert operation. table:" << tab_it->first << " is no need do anything.";
            continue;
        }
        DataAnalysisDBHandlerPtr ptr = handler_it->second;
        for (const auto &data : tab_it->second)
        {
            const DataAnalysisColumnList detect_key = ptr->get_detect_func_();
            DataAnalysisTableParse parse(detect_key, data);
            parse.SetOperation(DataAnalysisTableParse::DBHandleType::DA_OPERATION_INSERT);
            parse.SetProjectType(project_type_);
            DataAnalysisTableHandler(parse, ptr, context_);
        }
    }
    
    for (tab_it = db_update_info_.begin(); tab_it != db_update_info_.end(); ++tab_it)
    {
        DataAnalysisDBHandlerMap::iterator handler_it;
        handler_it = g_db_handler_map.find(tab_it->first);
        if (handler_it == g_db_handler_map.end())
        {
            AK_LOG_WARN << "Mysql update operation. table:" << tab_it->first << " is no need do anything.";
            continue;
        }
        
        DataAnalysisDBHandlerPtr ptr = handler_it->second;
        DataAnalysisRowList data = tab_it->second;
        int i = 0;
        for (i = 0; i < data.size(); i++,i++)
        {
            const DataAnalysisColumnList detect_key = ptr->get_detect_func_();
            DataAnalysisTableParse parse(detect_key, data[i], data[i + 1]);
            parse.SetOperation(DataAnalysisTableParse::DBHandleType::DA_OPERATION_UPDATE);
            parse.SetProjectType(project_type_);
            DataAnalysisTableHandler(parse, ptr, context_);      
        }
    }  

    context_.DispatchUpdateConfigInfo();    
}

