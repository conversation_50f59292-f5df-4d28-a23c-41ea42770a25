#ifndef __CSADAPT_DATA_ANALYSIS_H__
#define __CSADAPT_DATA_ANALYSIS_H__

#include <vector>
#include <string>
#include <map>
#include <list>
#include <memory>
#include "DataAnalysisDef.h"
#include "DataAnalysisContext.h"
//#include "AKCSMsg.h"


static const std::string da_type = "type";
static const std::string da_project_type = "project_type";
static const std::string da_special_value = "specialValue";
static const std::string da_insert = "insert";
static const std::string da_delete = "delete";
static const std::string da_update = "update";
static const std::string da_before = "before";
static const std::string da_after = "after";

class DataAnalysis
{
public:
    DataAnalysis(const std::string data);
    int Analysis();
    
    ~DataAnalysis();

private:
    int Init();
    std::string original_data;
    std::string type_;
    int project_type_;
    DataAnalysisTableInfoMap db_insert_info_;
    DataAnalysisTableInfoMap db_delete_info_;
    DataAnalysisTableInfoMap db_update_info_;
    DataAnalysisSqlKV special_info_;
    int is_special_;
    DataAnalysisContext context_;
};



#endif //__CSADAPT_DATA_ANALYSIS_H__
