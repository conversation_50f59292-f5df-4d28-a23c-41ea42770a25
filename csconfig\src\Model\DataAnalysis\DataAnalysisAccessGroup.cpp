#include "DataAnalysisAccessGroup.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigOfficeAccessUpdate.h"
#include "UpdateConfigCommAccessUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "PersonalAccount.h"
#include "dbinterface/ProjectUserManage.h"
#include "DataAnalysisdbHandle.h"



static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int GetDevicesChangeType();


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "AccessGroup";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
    {DA_INDEX_ACCESSGROUP_ID, "ID", ItemChangeHandle},
    {DA_INDEX_ACCESSGROUP_NAME, "Name", ItemChangeHandle},
    {DA_INDEX_ACCESSGROUP_COMMUNITYID, "CommunityID", ItemChangeHandle},
    {DA_INDEX_ACCESSGROUP_UNITID, "UnitID", ItemChangeHandle},
    {DA_INDEX_ACCESSGROUP_SCHEDULERTYPE, "SchedulerType", ItemChangeHandle},
    {DA_INDEX_ACCESSGROUP_DATEFLAG, "DateFlag", ItemChangeHandle},
    {DA_INDEX_ACCESSGROUP_BEGINTIME, "BeginTime", ItemChangeHandle},
    {DA_INDEX_ACCESSGROUP_ENDTIME, "EndTime", ItemChangeHandle},
    {DA_INDEX_ACCESSGROUP_STARTTIME, "StartTime", ItemChangeHandle},
    {DA_INDEX_ACCESSGROUP_STOPTIME, "StopTime", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    uint32_t ag_id = data.GetIndexAsInt(DA_INDEX_ACCESSGROUP_ID);
    std::string uid;
    std::string mac;
    uint32_t mng_id = data.GetIndexAsInt(DA_INDEX_ACCESSGROUP_COMMUNITYID);
    uint32_t project_type = data.GetProjectType();

    uint32_t change_type = WEB_COMM_MODIFY_ACCESS_GROUP;
    uint32_t office_change_type = WEB_OFFICE_MODIFY_ACCESS_GROUP;
    

    //更新数据版本
    dbinterface::ProjectUserManage::UpdateDataVersionByAccessGroupID(ag_id);

    if (project_type == project::OFFICE)
    {   
        //办公
        AK_LOG_INFO << local_table_name << " CommonHandle. office change type=" << office_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(office_change_type) 
        << " node= " << uid << " office_id= " << mng_id << " mac= " << mac << " ag_id= " << ag_id;
        UCOfficeAccessUpdatePtr ptr = std::make_shared<UCOfficeAccessUpdate>(office_change_type, mng_id, mac, uid, ag_id);
        context.AddUpdateConfigInfo(UPDATE_OFFICE_ACCESS_UPDATE, ptr);    
    }
    else 
    {
        //社区
        AK_LOG_INFO << local_table_name << " CommonHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type)
        << " node= " << uid << " community_id= " << mng_id << " mac= " << mac << " ag_id= " << ag_id;
        UCCommunityAccessUpdatePtr ptr = std::make_shared<UCCommunityAccessUpdate>(change_type, mng_id, mac, uid, ag_id);
        context.AddUpdateConfigInfo(UPDATE_COMM_ACCESS_UPDATE, ptr);
    }  
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    CommonChangeHandle(data, context);
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //权限组删除时，仅根据AccessGroup无法刷配置，需要在其他表做
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    if(data.IsIndexChange(DA_INDEX_ACCESSGROUP_ID) 
       || data.IsIndexChange(DA_INDEX_ACCESSGROUP_NAME)
       || data.IsIndexChange(DA_INDEX_ACCESSGROUP_COMMUNITYID)
       || data.IsIndexChange(DA_INDEX_ACCESSGROUP_UNITID)
       || data.IsIndexChange(DA_INDEX_ACCESSGROUP_SCHEDULERTYPE)
       || data.IsIndexChange(DA_INDEX_ACCESSGROUP_DATEFLAG)
       || data.IsIndexChange(DA_INDEX_ACCESSGROUP_BEGINTIME)
       || data.IsIndexChange(DA_INDEX_ACCESSGROUP_ENDTIME)
       || data.IsIndexChange(DA_INDEX_ACCESSGROUP_STARTTIME)
       || data.IsIndexChange(DA_INDEX_ACCESSGROUP_STOPTIME))
    {
        CommonChangeHandle(data, context);
    }
    else
    {
        AK_LOG_INFO << "without index change, AccessGroup don't need UpdateHandle";
    }
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaAccessGroupHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);
}






