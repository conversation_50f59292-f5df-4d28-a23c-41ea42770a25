#include "DataAnalysisAccount.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigOfficeFileUpdate.h"
#include "UpdateConfigCommFileUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "PersonalAccount.h"
#include "dbinterface/ProjectUserManage.h"



static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "Account";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
    //当前只对办公和社区的account数据做分析
    {DA_INDEX_ACCOUNT_ID, "ID", ItemChangeHandle},
    {DA_INDEX_ACCOUNT_GRADE, "Grade", ItemChangeHandle},
    {DA_INDEX_ACCOUNT_LOCATION, "Location", ItemChangeHandle},
    {DA_INDEX_ACCOUNT_TIMEZONE, "TimeZone", ItemChangeHandle},
    {DA_INDEX_ACCOUNT_CUSTOMIZEFORM, "CustomizeForm", ItemChangeHandle},
    {DA_INDEX_ACCOUNT_SIPTYPE, "SipType", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //创建社区或office时没有设备，所以无需刷新配置
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //当前没有删除社区，办公的删除在special处理
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    uint32_t grade = data.GetIndexAsInt(DA_INDEX_ACCOUNT_GRADE);
    //只对小区和办公的account做分析更新处理
    if ((grade == AccountGrade::COMMUNITY_MANEGER_GRADE 
        || grade == AccountGrade::OFFICE_MANEGER_GRADE)
        && (data.IsIndexChange(DA_INDEX_ACCOUNT_LOCATION) 
        || data.IsIndexChange(DA_INDEX_ACCOUNT_TIMEZONE)
        || data.IsIndexChange(DA_INDEX_ACCOUNT_CUSTOMIZEFORM)
        || data.IsIndexChange(DA_INDEX_ACCOUNT_SIPTYPE)))
    {
        std::string uid;
        std::string mac;
        uint32_t mng_id = data.GetIndexAsInt(DA_INDEX_ACCOUNT_ID);
        uint32_t unit_id = 0;
        uint32_t project_type = data.GetProjectType();

        uint32_t change_type = WEB_COMM_INFO;
        uint32_t office_change_type = WEB_OFFICE_INFO;

        if (project_type == project::OFFICE)
        {   
            //办公
            AK_LOG_INFO << local_table_name << " UpdateHandle. office change type=" << office_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(office_change_type) << " node= " << uid
                << " office_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
            UCOfficeFileUpdatePtr ptr = std::make_shared<UCOfficeFileUpdate>(office_change_type, mng_id, unit_id, mac, uid);
            context.AddUpdateConfigInfo(UPDATE_OFFICE_FILE_UPDATE, ptr);
        }
        else 
        {
            //社区
            AK_LOG_INFO << local_table_name << " UpdateHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
                << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
            UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, uid);
            context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
        }
    }
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaAccountHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);
}






