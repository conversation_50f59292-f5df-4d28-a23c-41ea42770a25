#include "DataAnalysisAccountAccess.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigOfficeAccessUpdate.h"
#include "UpdateConfigCommAccessUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "PersonalAccount.h"
#include "dbinterface/ProjectUserManage.h"
#include "DataAnalysisdbHandle.h"
#include "dbinterface/AccessGroupDB.h"



static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int GetDevicesChangeType();


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "AccountAccess";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
    {DA_INDEX_ACCOUNTACCESS_ACCOUNT, "Account", ItemChangeHandle},
    {DA_INDEX_ACCOUNTACCESS_ACCESSGROUPID, "AccessGroupID", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string uid = data.GetIndex(DA_INDEX_ACCOUNTACCESS_ACCOUNT);
    std::string mac = "";
    UserInfo user_info;
    memset(&user_info, 0, sizeof(user_info));
    uint32_t mng_id;
    if (0 == dbhandle::DAInfo::GetUserInfoByUid(uid, user_info))
    {
        mng_id = user_info.mng_id;
    }
    uint32_t project_type = data.GetProjectType();

    uint32_t change_type = WEB_COMM_ADD_ACCOUNT_ACCESS;
    uint32_t office_change_type = WEB_OFFICE_ADD_ACCOUNT_ACCESS;

    //更新数据版本
    dbinterface::ProjectUserManage::UpdateAccountDataVersionByUid(uid);

    if (project_type == project::OFFICE)
    {   
        //办公
        AK_LOG_INFO << local_table_name << " InsertHandle. office change type=" << office_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(office_change_type) << " node= " << uid
                << " office_id= " << mng_id << " mac= " << mac;
        UCOfficeAccessUpdatePtr ptr = std::make_shared<UCOfficeAccessUpdate>(office_change_type, mng_id, mac, uid);
        context.AddUpdateConfigInfo(UPDATE_OFFICE_ACCESS_UPDATE, ptr);    
    }
    else 
    {
        //社区
        AK_LOG_INFO << local_table_name << " InsertHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type)<< " node= " << uid
                << " community_id= " << mng_id << " mac= " << mac;
        UCCommunityAccessUpdatePtr ptr = std::make_shared<UCCommunityAccessUpdate>(change_type, mng_id, mac, uid);
        context.AddUpdateConfigInfo(UPDATE_COMM_ACCESS_UPDATE, ptr);
    }  
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //删除主从账户时node依然保留，所以可以根据account更新数据版本
    //删除用户或者删除权限组时调用
    std::string uid = data.GetIndex(DA_INDEX_ACCOUNTACCESS_ACCOUNT);
    uint32_t ag_id = data.GetIndexAsInt(DA_INDEX_ACCOUNTACCESS_ACCESSGROUPID);
    std::string mac = "";
    uint32_t mng_id;
    UserInfo user_info;
    memset(&user_info, 0, sizeof(user_info));
    //用户未删除，根据node查找mng_id
    if (dbhandle::DAInfo::GetUserInfoByUid(uid, user_info) == 0)
    {
        mng_id = user_info.mng_id;
    }
    else if (0 != dbinterface::AccessGroup::GetCommunityIDByAccessGroup(ag_id, mng_id))
    {
        //用户删除，根据ag_id查找mng_id
        //add by chenzhx: 这里一定要处理ag_id, 因为如果用户删除了在PersonalAccount是没办法根据user找到关联的权限组信息。
        AK_LOG_WARN << "on delete account access,but get mng_id by ag_id is null, ag_id="  << ag_id;
        return 0;
    }
    
    uint32_t project_type = data.GetProjectType();
    uint32_t change_type = WEB_COMM_DEL_ACCOUNT_ACCESS;
    uint32_t office_change_type = WEB_OFFICE_DEL_ACCOUNT_ACCESS;

    //更新数据版本
    dbinterface::ProjectUserManage::UpdateAccountDataVersionByUid(uid);

    if (project_type == project::OFFICE)
    {   
        //办公
        AK_LOG_INFO << local_table_name << " DeleteHandle. office change type=" << office_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(office_change_type) << " node= " << uid
                << " office_id= " << mng_id << " mac= " << mac;
        UCOfficeAccessUpdatePtr ptr = std::make_shared<UCOfficeAccessUpdate>(office_change_type, mng_id, mac, uid, ag_id);
        context.AddUpdateConfigInfo(UPDATE_OFFICE_ACCESS_UPDATE, ptr);    
    }
    else 
    {
        //社区
        AK_LOG_INFO << local_table_name << " DeleteHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
                << " community_id= " << mng_id << " mac= " << mac << " ag_id= " << ag_id;
        UCCommunityAccessUpdatePtr ptr = std::make_shared<UCCommunityAccessUpdate>(change_type, mng_id, mac, uid, ag_id);
        context.AddUpdateConfigInfo(UPDATE_COMM_ACCESS_UPDATE, ptr);
    }
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string uid = data.GetIndex(DA_INDEX_ACCOUNTACCESS_ACCOUNT);
    std::string mac = "";
    UserInfo user_info;
    memset(&user_info, 0, sizeof(user_info));
    uint32_t mng_id;
    if (0 == dbhandle::DAInfo::GetUserInfoByUid(uid, user_info))
    {
        mng_id = user_info.mng_id;
    }
    uint32_t project_type = data.GetProjectType();

    uint32_t change_type = WEB_COMM_MODIFY_ACCOUNT_ACCESS;
    uint32_t office_change_type = WEB_OFFICE_MODIFY_ACCOUNT_ACCESS;

    //更新数据版本
    dbinterface::ProjectUserManage::UpdateAccountDataVersionByUid(uid);

    if (project_type == project::OFFICE)
    {   
        AK_LOG_INFO << local_table_name << " UpdateHandle. office change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type);
        UCOfficeAccessUpdatePtr ptr = std::make_shared<UCOfficeAccessUpdate>(office_change_type, mng_id, mac, uid);
        context.AddUpdateConfigInfo(UPDATE_OFFICE_ACCESS_UPDATE, ptr);
    }
    else 
    {
        AK_LOG_INFO << local_table_name << " UpdateHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type);
        UCCommunityAccessUpdatePtr ptr = std::make_shared<UCCommunityAccessUpdate>(change_type, mng_id, mac, uid);
        context.AddUpdateConfigInfo(UPDATE_COMM_ACCESS_UPDATE, ptr);     
    }  
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaAccountAccessHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);
}






