#include "DataAnalysisAmenityDevice.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "DataAnalysisdbHandle.h"
#include "IPCControl.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/AccessGroupDB.h"
#include "dbinterface/ProjectUserManage.h"
#include "UpdateConfigCommAccessUpdate.h"
#include "DeviceSetting.h"
#include "dbinterface/resident/ResidentDevices.h"

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "AmenityDevice";
static DataAnalysisChangeHandle da_change_handle[] = {
    {DA_INDEX_AMENITY_DEVICE_DEV_UUID, "DeviceUUID", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string dev_uuid = data.GetIndex(DA_INDEX_AMENITY_DEVICE_DEV_UUID);
    
    ResidentDev dev;
    if (0 != dbinterface::ResidentDevices::GetUUIDDev(dev_uuid, dev))
    {
        AK_LOG_WARN << "cannot find device, uuid=" << dev_uuid;
        return -1;
    }

    std::string mac = dev.mac;
    uint32_t grade = dev.grade;
    uint32_t unit_id = dev.unit_id;
    uint32_t mng_id = dev.project_mng_id;
    std::string uid;

    if (dev.grade != csmain::COMMUNITY_DEVICE_TYPE_PUBLIC && dev.grade != csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
        AK_LOG_WARN << "device is not public, no support amenity device. grade=" << dev.grade;
        return -1;
    }

    if (dev.project_type != project::RESIDENCE)
    {
        AK_LOG_WARN << "booking no support office or personal. mac=" << mac;
        return -1;
    }

    uint32_t change_type = WEB_COMM_MODIFY_ACCESS_GROUP;

    //获取默认权限组列表
    std::vector<int> ag_list;
    dbinterface::AccessGroup::GetDevDefaultAccessGroupIDList(dev, ag_list);

    for (const auto& ag_id : ag_list)
    {
        dbinterface::ProjectUserManage::UpdateDataVersionByAccessGroupID(ag_id);
        AK_LOG_INFO << local_table_name << " CommonHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
                << " community_id= " << mng_id << " mac= " << mac << " ag_id= " << ag_id;
        UCCommunityAccessUpdatePtr ptr = std::make_shared<UCCommunityAccessUpdate>(change_type, mng_id, mac, uid, ag_id);
        context.AddUpdateConfigInfo(UPDATE_COMM_ACCESS_UPDATE, ptr);
    }
    return 0;

}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    CommonChangeHandle(data, context);
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    CommonChangeHandle(data, context);
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}


static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaAmenityDeviceHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);
}






