#include "DataAnalysisAnalogDevice.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigCommFileUpdate.h"
#include "UpdateConfigCommDevUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "DataAnalysisdbHandle.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"


static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "AnalogDevice";
/*复制到DataAnalysisDef.h*/ 

static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/ 
   {DA_INDEX_ANALOG_DEVICE_ID, "ID", ItemChangeHandle},
   {DA_INDEX_ANALOG_DEVICE_UUID, "UUID", ItemChangeHandle},
   {DA_INDEX_ANALOG_DEVICE_ACCOUNTUUID, "AccountUUID", ItemChangeHandle},
   {DA_INDEX_ANALOG_DEVICE_COMMUNITYUNITID, "CommunityUnitID", ItemChangeHandle},
   {DA_INDEX_ANALOG_DEVICE_PERSONALACCOUNTUUID, "PersonalAccountUUID", ItemChangeHandle},
   {DA_INDEX_ANALOG_DEVICE_ANALOGDEVICENAME, "AnalogDeviceName", ItemChangeHandle},
   {DA_INDEX_ANALOG_DEVICE_ANALOGDEVICENUMBER, "AnalogDeviceNumber", ItemChangeHandle},
   {DA_INDEX_ANALOG_DEVICE_DTMFCODE, "DTMFCode", ItemChangeHandle},
   {DA_INDEX_ANALOG_DEVICE_CREATETIME, "CreateTime", ItemChangeHandle},
   {DA_INDEX_ANALOG_DEVICE_UPDATETIME, "UpdateTime", ItemChangeHandle},
   {DA_INDEX_INSERT, "", InsertHandle},
   {DA_INDEX_DELETE, "", DeleteHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};


static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    uint32_t project_type = data.GetProjectType();
    if(project_type != project::RESIDENCE)
    {
        AK_LOG_WARN << local_table_name << " CommonChange. Project type not support, project_type=" << project_type;
        return -1;
    }

    //更新Apt相关的联系人
    uint32_t change_type = WEB_COMM_UPDATE_APT_CALLRULE;
    std::string node_uuid = data.GetIndex(DA_INDEX_ANALOG_DEVICE_PERSONALACCOUNTUUID);
    std::string node;

    UserInfo user_info;
    memset(&user_info, 0, sizeof(user_info));

    if (0 != dbhandle::DAInfo::GetUserInfoByUUID(node_uuid, user_info))
    {
        AK_LOG_WARN << local_table_name << " CommonChangeHandle. user is null, node uuid=" << node_uuid;
        return -1;
    }

    uint32_t mng_id = user_info.mng_id;
    uint32_t unit_id = user_info.unit_id;
    node = user_info.node; //确保是主账号的account
    std::string mac;

    //刷联系人        
    AK_LOG_INFO << local_table_name << " CommonChangeHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << node
        << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
    UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, node);
    context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);

    return 0;
}

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return CommonChangeHandle(data, context);
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return CommonChangeHandle(data, context);
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return CommonChangeHandle(data, context);
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaAnalogDeviceHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}
