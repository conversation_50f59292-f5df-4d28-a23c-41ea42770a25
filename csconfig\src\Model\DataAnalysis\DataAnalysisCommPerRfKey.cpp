#include "DataAnalysisCommPerRfKey.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigOfficeFileUpdate.h"
#include "UpdateConfigCommFileUpdate.h"
#include "UpdateConfigPersonalFileUpdate.h"
#include "UpdateConfigCommAccessUpdate.h"
#include "UpdateConfigOfficeAccessUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "PersonalAccount.h"
#include "dbinterface/ProjectUserManage.h"
#include "DataAnalysisdbHandle.h"
#include "UpdateSmartLockConfig.h"
#include "dbinterface/SmartLock.h"


static int CommonChangeHandle(DataAnalysisTableParse& data, DataAnalysisContext& context, bool if_account_change = false);
static int ItemChangeHandle(DataAnalysisTableParse& data, DataAnalysisContext& context);
static int InsertHandle(DataAnalysisTableParse& data, DataAnalysisContext& context);
static int DeleteHandle(DataAnalysisTableParse& data, DataAnalysisContext& context);
static int UpdateHandle(DataAnalysisTableParse& data, DataAnalysisContext& context);


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "CommPerRfKey";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
    {DA_INDEX_COMM_RF_ID, "ID", ItemChangeHandle},
    {DA_INDEX_COMM_RF_ACCOUNT, "Account", ItemChangeHandle},
    {DA_INDEX_COMM_RF_CODE, "Code", ItemChangeHandle},
    {DA_INDEX_COMM_RF_COMMUNITYID, "CommunityID", ItemChangeHandle},
    {DA_INDEX_COMM_RF_SPECIAL, "Special", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse& data, DataAnalysisContext& context)
{
    return 0;
}

static int CommonChangeHandle(DataAnalysisTableParse& data, DataAnalysisContext& context, bool if_account_change)
{
    std::string mac;
    std::string uid;
    uint32_t mng_id = data.GetIndexAsInt(DA_INDEX_COMM_RF_COMMUNITYID);

    if (if_account_change)
    {
        uid = data.GetBeforeIndex(DA_INDEX_COMM_RF_ACCOUNT);
    }
    else
    {
        uid = data.GetIndex(DA_INDEX_COMM_RF_ACCOUNT);
    }

    UserInfo user_info;
    memset(&user_info, 0, sizeof(user_info));
    if (uid.empty() || dbhandle::DAInfo::GetUserInfoByUid(uid, user_info) != 0)
    {
        //add by chenzhx: 说明用户中间被删除了，这样会触发PersonalAccount/AccountAccess, 这里不需要处理
        AK_LOG_INFO << local_table_name << " CommonHandle. User is null, uid=" << uid;
        return -1;
    }

    uint32_t change_type = 0;
    dbinterface::ProjectUserManage::UpdateAccountDataVersionByUid(uid);

    if (data.GetProjectType() == project::OFFICE)
    {
        //办公
        change_type = WEB_OFFICE_MODIFY_USER_ALL_ACCESS;
        UCOfficeAccessUpdatePtr ptr = std::make_shared<UCOfficeAccessUpdate>(change_type, mng_id, mac, uid);
        context.AddUpdateConfigInfo(UPDATE_OFFICE_ACCESS_UPDATE, ptr);
    }
    else
    {
        //新社区
        change_type = WEB_COMM_MODIFY_USER_ALL_ACCESS;
        UCCommunityAccessUpdatePtr ptr = std::make_shared<UCCommunityAccessUpdate>(change_type, mng_id, mac, uid);
        context.AddUpdateConfigInfo(UPDATE_COMM_ACCESS_UPDATE, ptr);

        dbinterface::SmartLock::UpdateNodeSmartLockStatusSynchronizing(user_info.node_uuid);

        uint32_t smartlock_change_type = SMARTLOCK_NODE_SL20_CONFIG_UPDATE;
        std::string lock_uuid;

        UCSmartLockConfigUpdatePtr smartlock_ptr = std::make_shared<UCSmartLockConfigUpdate>(smartlock_change_type, lock_uuid, user_info.node, project::RESIDENCE, mng_id);
        context.AddUpdateConfigInfo(UPDATE_SMARTLOCK_CONFIG, smartlock_ptr);

    }

    AK_LOG_INFO << "CommonHandle: table_name=" << local_table_name << ", change_type=" << change_type
        << ", mng_id= " << mng_id << ", node= " << uid << ", mac= " << mac;

    return 0;
}

static int InsertHandle(DataAnalysisTableParse& data, DataAnalysisContext& context)
{
    CommonChangeHandle(data, context);
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse& data, DataAnalysisContext& context)
{
    CommonChangeHandle(data, context);
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse& data, DataAnalysisContext& context)
{
    CommonChangeHandle(data, context);

    if (data.GetIndex(DA_INDEX_COMM_RF_ACCOUNT) != data.GetBeforeIndex(DA_INDEX_COMM_RF_ACCOUNT))
    {
        CommonChangeHandle(data, context, true);
    }
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaCommPerRfKeyHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);

}






