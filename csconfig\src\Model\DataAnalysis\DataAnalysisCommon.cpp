#include "DataAnalysisCommon.h"
#include "DataAnalysisControl.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include <memory>
#include <string.h>
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "UpdateConfigCommFileUpdate.h"
#include "UpdateConfigPersonalFileUpdate.h"
#include "UpdateConfigOfficeFileUpdate.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"



int DevicesModifyUpdateConfig(const std::string &dev_uuid, const std::string &local_table_name,  DataAnalysisContext &context)
{

    int proejct_type = 0;
    uint32_t change_type = 0;
    uint32_t mng_id = 0;
    uint32_t unit_id = 0;
    std::string uid;    
    ResidentDev dev;
    if (0 == dbinterface::ResidentDevices::GetUUIDDev(dev_uuid, dev))
    {
        mng_id = dev.project_mng_id;
        unit_id = dev.unit_id;
        uid = std::string(dev.node);    
        if(dev.project_type == project::OFFICE)
        {
            proejct_type = project::OFFICE;        
            if (dev.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
            {
                change_type = WEB_OFFICE_PUB_MODIFY_DEV;
            }
            else if (dev.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
            {
                change_type = WEB_OFFICE_UNIT_MODIFY_DEV;
            }
            else
            {
                change_type = WEB_OFFICE_MODIFY_DEV;
            }                    
        }
        else
        {
            proejct_type = project::RESIDENCE;
            if (dev.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
            {
                change_type = WEB_COMM_PUB_MODIFY_DEV;
            }
            else if (dev.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
            {
                change_type = WEB_COMM_UNIT_MODIFY_DEV;
            }
            else
            {
                change_type = WEB_COMM_MODIFY_DEV;
            }                    
        }
    }
    else if (0 == dbinterface::ResidentPerDevices::GetUUIDDev(dev_uuid, dev))
    {
        proejct_type = project::PERSONAL;
        change_type = WEB_PER_MODIFY_DEV;
        uid = std::string(dev.node);
    }
    else
    {
        AK_LOG_WARN << "device not found, dev uuid=" << dev_uuid;
        return 0;
    }    

    if (proejct_type == project::RESIDENCE)
    {
        AK_LOG_INFO << local_table_name << " Communnity UpdateHandle. change type=" << change_type  << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " mac= " << dev.mac;
        UCCommunityFileUpdatePtr fileptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, dev.mac, uid);
        context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, fileptr);
    }
    else if (proejct_type == project::PERSONAL)
    {
        AK_LOG_INFO << local_table_name << " Personal UpdateHandle. change type=" << change_type  << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " mac= " << dev.mac;
        UCPersonalFileUpdatePtr fileptr = std::make_shared<UCPersonalFileUpdate>(change_type, dev.mac, uid);
        context.AddUpdateConfigInfo(UPDATE_PERSONAL_FILE_UPDATE, fileptr);
    }
    else if (proejct_type == project::OFFICE)
    {
        AK_LOG_INFO << local_table_name << " Office UpdateHandle. change type=" << change_type  << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " mac= " << dev.mac;
        UCOfficeFileUpdatePtr fileptr = std::make_shared<UCOfficeFileUpdate>(change_type, mng_id, unit_id, dev.mac, uid);
        context.AddUpdateConfigInfo(UPDATE_OFFICE_FILE_UPDATE, fileptr);
    }
    return 1;
}






