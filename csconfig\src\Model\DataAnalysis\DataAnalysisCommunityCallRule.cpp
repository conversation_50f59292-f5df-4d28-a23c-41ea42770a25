#include "DataAnalysisCommunityCallRule.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigCommFileUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "PersonalAccount.h"
#include "dbinterface/ProjectUserManage.h"
#include "DataAnalysisdbHandle.h"
#include "dbinterface/Account.h"
#include "dbinterface/CommunityUnit.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"



static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "CommunityCallRule";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
    {DA_INDEX_COMMUNITY_CALLRULE_PERSONAL_ACCOUNT_UUID, "PersonalAccountUUID", ItemChangeHandle},
    {DA_INDEX_COMMUNITY_CALLRULE_DATA_VERSION, "Version", ItemChangeHandle},
    {DA_INDEX_COMMUNITY_CALLRULE_APT_CALL_TYPE, "AptCallType", ItemChangeHandle},    
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

//插入和删除在PersonalAccount添加处理
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    uint32_t project_type = data.GetProjectType();
    if(project_type != project::RESIDENCE)
    {
        AK_LOG_WARN << local_table_name << " UpdateHandle. Project type not support, project_type=" << project_type;
        return -1;
    }

    uint32_t change_type = WEB_COMM_UPDATE_APT_CALLRULE;

    if (data.IsIndexChange(DA_INDEX_COMMUNITY_CALLRULE_DATA_VERSION)
        || data.IsIndexChange(DA_INDEX_COMMUNITY_CALLRULE_APT_CALL_TYPE))
    {
        std::string node_uuid = data.GetIndex(DA_INDEX_COMMUNITY_CALLRULE_PERSONAL_ACCOUNT_UUID);
        
        UserInfo user_info;
        memset(&user_info, 0, sizeof(user_info));
        if (0 != dbhandle::DAInfo::GetUserInfoByUUID(node_uuid, user_info))
        {
            AK_LOG_WARN << local_table_name << " UpdateHandle. User is null, uuid=" << node_uuid;
            return -1;
        }
        
        uint32_t mng_id = user_info.mng_id;
        uint32_t unit_id = user_info.unit_id;
        std::string node = user_info.node;
        std::string mac;

        //刷联系人        
        AK_LOG_INFO << local_table_name << " UpdateHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << node
            << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
        UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, node);
        context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
    }
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaCommunityCallRuleHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);

}



