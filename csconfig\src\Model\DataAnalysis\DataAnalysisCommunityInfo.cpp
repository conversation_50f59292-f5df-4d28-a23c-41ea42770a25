#include "DataAnalysisCommunityInfo.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigOfficeFileUpdate.h"
#include "UpdateConfigCommFileUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "PersonalAccount.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/CommunityInfo.h"
#include "UpdateSmartLockConfig.h"
#include "dbinterface/Account.h"
#include "dbinterface/SmartLock.h"

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "CommunityInfo";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
    //当前只对办公和社区的account数据做分析
    //后续还要继续补充需要进行分析的字段，截止6.5版本只需要分析AptPinType这个字段
    {DA_INDEX_COMMUNITYINFO_ID, "ID", ItemChangeHandle},
    {DA_INDEX_COMMUNITYINFO_ACCOUNTID, "AccountID", ItemChangeHandle},
    {DA_INDEX_COMMUNITYINFO_STREET, "Street", ItemChangeHandle},
    {DA_INDEX_COMMUNITYINFO_CITY, "City", ItemChangeHandle},
    {DA_INDEX_COMMUNITYINFO_POSTALCODE, "PostalCode", ItemChangeHandle},
    {DA_INDEX_COMMUNITYINFO_COUNTRY, "Country", ItemChangeHandle},
    {DA_INDEX_COMMUNITYINFO_STATES, "States", ItemChangeHandle},
    {DA_INDEX_COMMUNITYINFO_APTPINTYPE, "AptPinType", ItemChangeHandle},
    {DA_INDEX_COMMUNITYINFO_NUMBEROFAPT, "NumberOfApt", ItemChangeHandle},
    {DA_INDEX_COMMUNITYINFO_SWITCH, "Switch", ItemChangeHandle},
    {DA_INDEX_COMMUNITYINFO_ENABLEMOTION, "EnableMotion", ItemChangeHandle},
    {DA_INDEX_COMMUNITYINFO_MOTIONTIME, "MotionTime", ItemChangeHandle},
    {DA_INDEX_COMMUNITYINFO_FACEENROLLMENT, "FaceEnrollment", ItemChangeHandle},
    {DA_INDEX_COMMUNITYINFO_IDCARDVERIFICATION, "IDCardVerification", ItemChangeHandle},
    {DA_INDEX_COMMUNITYINFO_NAMEDISPLAY, "NameDisplay", ItemChangeHandle},
    {DA_INDEX_COMMUNITYINFO_ENABLE_PACKAGE_DETECTION, "EnablePackageDetection", ItemChangeHandle},
    // {DA_INDEX_COMMUNITYINFO_ENABLE_SOUND_DETECTION, "EnableSoundDetection", ItemChangeHandle},
    // {DA_INDEX_COMMUNITYINFO_SOUND_TYPE, "SoundType", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;    
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //创建社区时没有设备，所以无需刷新配置
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //在special处理
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string uid;
    std::string mac;
    uint32_t unit_id = 0;
    uint32_t change_type = 0;
    uint32_t smartlock_change_type = 0;
    uint32_t mng_id = data.GetIndexAsInt(DA_INDEX_COMMUNITYINFO_ACCOUNTID);
    
    if (data.IsIndexChange(DA_INDEX_COMMUNITYINFO_STREET) || 
        data.IsIndexChange(DA_INDEX_COMMUNITYINFO_CITY) || 
        data.IsIndexChange(DA_INDEX_COMMUNITYINFO_POSTALCODE) || 
        data.IsIndexChange(DA_INDEX_COMMUNITYINFO_COUNTRY) || 
        data.IsIndexChange(DA_INDEX_COMMUNITYINFO_STATES) || 
        data.IsIndexChange(DA_INDEX_COMMUNITYINFO_NUMBEROFAPT)) 
    {
        change_type = WEB_COMM_INFO;
    }

    if (data.IsIndexChange(DA_INDEX_COMMUNITYINFO_APTPINTYPE))
    {
        change_type = WEB_COMM_APT_PIN;
        dbinterface::ProjectUserManage::UpdateCommunityAllAccountDataVersion(mng_id);
    }
    
    if (data.IsIndexChange(DA_INDEX_COMMUNITYINFO_ENABLEMOTION) || data.IsIndexChange(DA_INDEX_COMMUNITYINFO_MOTIONTIME) 
     || data.IsIndexChange(DA_INDEX_COMMUNITYINFO_FACEENROLLMENT) || data.IsIndexChange(DA_INDEX_COMMUNITYINFO_IDCARDVERIFICATION)
     || data.IsIndexChange(DA_INDEX_COMMUNITYINFO_ENABLE_PACKAGE_DETECTION)
    //  || data.IsIndexChange(DA_INDEX_COMMUNITYINFO_ENABLE_SOUND_DETECTION)
    //  || data.IsIndexChange(DA_INDEX_COMMUNITYINFO_SOUND_TYPE)
    )
    {
        change_type = WEB_COMM_MOTION;
    }

    if (data.IsIndexChange(DA_INDEX_COMMUNITYINFO_NAMEDISPLAY))
    {
        change_type = WEB_COMM_MODIFY_CONTACT_DISPLAY_ORDER;
    }

    if (data.IsIndexChange(DA_INDEX_COMMUNITYINFO_SWITCH))
    {
        int switch_after = data.GetIndexAsInt(DA_INDEX_COMMUNITYINFO_SWITCH);
        int switch_befor = data.GetBeforeIndexAsInt(DA_INDEX_COMMUNITYINFO_SWITCH);
        int change_bit = GetFirstDiffBit(switch_befor, switch_after);

        switch(change_bit)
        {
            case CommunityInfo::SwitchType::Landline:
            {
                //第1位 - 落地开关,刷config和contact
                change_type = WEB_COMM_UPDATE_LANDLINE_STATUS;
                break;
            }
            case CommunityInfo::SwitchType::AllowPin:
            {
                //第3位 - 是否允许用户使用PIN,刷user
                change_type = WEB_COMM_ALLOW_CREATE_PIN;
                dbinterface::ProjectUserManage::UpdateCommunityAllAccountDataVersion(mng_id);
                break;
            }
            case CommunityInfo::SwitchType::SIMNotify:
            case CommunityInfo::SwitchType::ENABLE_AUTO_EMERGENCY:
            {
                //第4位 - SIM卡超流量开关; 第6位 - 警报触发自动开门开关; 刷config
                change_type = WEB_COMM_INFO;
                break;
            }
            case CommunityInfo::SwitchType::ENABLE_PRIVATE_ACCESS:
            {
                //第7位 - pm有操作PublicArea+PrivateArea的权限,发生变化时刷社区内所有个人独占设备的user文件
                change_type = WEB_COMM_MODIFY_PRIVATE_ACCESS;
                smartlock_change_type = SMARTLOCK_PROJECT_SL20_CONFIG_UPDATE;
                dbinterface::AccountInfo account_info;
                dbinterface::Account::GetAccountById(mng_id, account_info);
                dbinterface::SmartLock::UpdateProjectSmartLockStatusSynchronizing(account_info.uuid);
                dbinterface::ProjectUserManage::UpdateCommunityAllAccountDataVersion(mng_id);
                break;
            }
            default:
            {
                AK_LOG_INFO << "no need to update, change_bit= " << change_bit;            
            }
        }
    }
    
    if (change_type)
    {
        AK_LOG_INFO << local_table_name << " UpdateHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
            << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
        UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, uid);
        context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
    }

    if (smartlock_change_type)
    {
        AK_LOG_INFO << local_table_name << " UpdateHandle. smartlock change type=" << smartlock_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(smartlock_change_type) << " node= " << uid
            << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
        std::string lock_uuid;
        UCSmartLockConfigUpdatePtr ptr = std::make_shared<UCSmartLockConfigUpdate>(smartlock_change_type, lock_uuid, uid, project::RESIDENCE, mng_id);
        context.AddUpdateConfigInfo(UPDATE_SMARTLOCK_CONFIG, ptr);
    }

    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaCommunityInfoHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);
}






