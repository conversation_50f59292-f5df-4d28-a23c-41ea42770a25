#include "DataAnalysisCommunityRoom.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigCommFileUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "PersonalAccount.h"
#include "dbinterface/ProjectUserManage.h"
#include "DataAnalysisdbHandle.h"
#include "CommunityMng.h"
#include "dbinterface/Account.h"


static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "CommunityRoom";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
    {DA_INDEX_COMM_ROOM_ID, "ID", ItemChangeHandle},
    {DA_INDEX_COMM_ROOM_UNIT_ID, "UnitID", ItemChangeHandle},    
    {DA_INDEX_COMM_ROOM_ROOMNAME, "RoomName", ItemChangeHandle},
    {DA_INDEX_COMM_ROOM_FLOOR, "Floor", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    if (data.IsIndexChange(DA_INDEX_COMM_ROOM_ROOMNAME) || data.IsIndexChange(DA_INDEX_COMM_ROOM_FLOOR))
    {
        uint32_t room_id = data.GetIndexAsInt(DA_INDEX_COMM_ROOM_ID);
        std::string mac;
        UserInfo user_info;
        memset(&user_info, 0, sizeof(user_info));
        if (0 != dbhandle::DAInfo::GetUserInfoByRoomID(room_id, user_info))
        {
            AK_LOG_INFO << local_table_name << " UpdateHandle. User is null, room_id=" << room_id;
            return -1;
        }
        std::string node = user_info.node;
        uint32_t mng_id = user_info.mng_id;
        uint32_t unit_id = data.GetIndexAsInt(DA_INDEX_COMM_ROOM_UNIT_ID);

        uint32_t change_type = WEB_COMM_MODIFY_USER;

        //更新数据版本
        dbinterface::ProjectUserManage::UpdateDataVersionByNode(node);

        //只有社区主账号有用到CommunityRoom这张表
        AK_LOG_INFO << local_table_name << " UpdateHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << node
            << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
        UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, node);
        context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
        
        //名称修改时 户户通联系人刷新    
        if (data.IsIndexChange(DA_INDEX_COMM_ROOM_ROOMNAME) && dbinterface::Account::GetCommunityContactSwitch(mng_id))
        {
            uint32_t change_type = WEB_COMM_UPDATE_COMMUNITY_CALLS;
            AK_LOG_INFO << local_table_name << " UpdateHandle WEB_COMM_UPDATE_COMMUNITY_CALLS. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << node
                << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
            UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, node);
            context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
        }
    }
    return 0;
    
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaCommunityRoomHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);

}






