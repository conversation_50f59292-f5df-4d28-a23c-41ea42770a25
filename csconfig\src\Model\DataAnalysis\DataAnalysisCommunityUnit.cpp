#include "DataAnalysis.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisCommunityUnit.h"
#include <memory>
#include <string.h>
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "UpdateConfigOfficeFileUpdate.h"
#include "UpdateConfigCommFileUpdate.h"


static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "CommunityUnit";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
    {DA_INDEX_COMMUNITY_UNIT_ID, "ID", ItemChangeHandle},
    {DA_INDEX_COMMUNITY_UNIT_UNITNAME, "UnitName", ItemChangeHandle},
    {DA_INDEX_COMMUNITY_UNIT_MNGACCOUNTID, "MngAccountID", ItemChangeHandle},
    {DA_INDEX_COMMUNITY_UNIT_GROUNDFLOOR, "GroundFloor", ItemChangeHandle},
    {DA_INDEX_COMMUNITY_UNIT_STARTFLOOR, "StartFloor", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};



static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;    
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
    
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //刷schedule
    if (data.IsIndexChange(DA_INDEX_COMMUNITY_UNIT_UNITNAME))
    {
        std::string mac;
        std::string node;
        uint32_t project_type = data.GetProjectType();
        uint32_t unit_id = data.GetIndexAsInt(DA_INDEX_COMMUNITY_UNIT_ID);
        uint32_t mng_id = data.GetIndexAsInt(DA_INDEX_COMMUNITY_UNIT_MNGACCOUNTID);
        
        AK_LOG_INFO << local_table_name << " UpdateHandle. UnitID=" << unit_id << ",mng_id=" << mng_id;
        
        if (project_type == project::OFFICE)
        {   
            //办公
            uint32_t change_type = WEB_OFFICE_MODIFY_DEPARTMENT_NAME;
            UCOfficeFileUpdatePtr ptr = std::make_shared<UCOfficeFileUpdate>(change_type, mng_id, unit_id, mac, node);
            context.AddUpdateConfigInfo(UPDATE_OFFICE_FILE_UPDATE, ptr);
        }
        else 
        {
            //社区
            uint32_t change_type = WEB_COMM_MODIFY_BUILDING_NAME;
            UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, node);
            context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
        }
    }
    else if (data.IsIndexChange(DA_INDEX_COMMUNITY_UNIT_GROUNDFLOOR) || data.IsIndexChange(DA_INDEX_COMMUNITY_UNIT_STARTFLOOR))
    {
        std::string mac;
        std::string node;
        uint32_t project_type = data.GetProjectType();
        uint32_t unit_id = data.GetIndexAsInt(DA_INDEX_COMMUNITY_UNIT_ID);
        uint32_t mng_id = data.GetIndexAsInt(DA_INDEX_COMMUNITY_UNIT_MNGACCOUNTID);
        
        AK_LOG_INFO << local_table_name << " UpdateHandle. Modify building floor setting. UnitID=" << unit_id << ",mng_id=" << mng_id;
        
        //社区
        uint32_t change_type = WEB_COMM_MODIFY_BUILDING_FLOOR_SETTING;
        UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, node);
        context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
    }
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaCommunityUnitHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);

}
