#include "DataAnalysisContactFavorite.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include "UpdateConfigCommFileUpdate.h"
#include "AkLogging.h"
#include "AkcsCommonDef.h"
#include "AkcsWebMsgSt.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"

static int InsertOrDeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "ContactFavoriteList";
static DataAnalysisChangeHandle da_change_handle[] = {    
    {DA_INDEX_CONTACT_FAVORITE_PER_UUID, "PersonalAccountUUID", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertOrDeleteHandle},
    {DA_INDEX_DELETE, "", InsertOrDeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;    
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}


static int InsertOrDeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string per_uuid = data.GetIndex(DA_INDEX_CONTACT_FAVORITE_PER_UUID); 
    if(per_uuid.size() == 0)
    {
        return 0;
    }
    ResidentPerAccount account_info;
    memset(&account_info, 0, sizeof(account_info));
    if(0 != dbinterface::ResidentPersonalAccount::GetUUIDAccount(per_uuid, account_info))
    {
        AK_LOG_WARN << "ContactFavoriteList InsertOrDeleteHandle, uuid get account is null, uuid=" << per_uuid;
        return 0;
    }
    std::string uid = account_info.account;
    std::string mac;
    uint32_t mng_id = account_info.parent_id;
    uint32_t unit_id = account_info.unit_id;
    uint32_t change_type = WEB_COMM_NODE_UPDATE;
    AK_LOG_INFO << local_table_name << " UpdateHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
        << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
    UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, uid);
    context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaContactFavoriteHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);
}






