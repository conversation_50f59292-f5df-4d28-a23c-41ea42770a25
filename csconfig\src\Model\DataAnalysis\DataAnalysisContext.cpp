#include "DataAnalysisControl.h"
#include "DataAnalysis.h"
#include "DataAnalysisContext.h"
#include "json/json.h"



DataAnalysisContext::DataAnalysisContext()
{

}

DataAnalysisContext::~DataAnalysisContext()
{


}

void DataAnalysisContext::AddUpdateConfigInfo(int type, std::shared_ptr<void> msg)
{
    UpdateConfigInfoMap::iterator handler_it;
    handler_it = update_config_list_.find(type);
    if (handler_it == update_config_list_.end())
    {
        UpdateConfigDataList list;
        list.push_back(msg);
        update_config_list_.insert(std::map<int, UpdateConfigDataList>::value_type(type, list));        
    }
    else
    {
        handler_it->second.push_back(msg);
    }
}

bool DataAnalysisContext::AddOperateType(int type)
{
    std::vector<int>::iterator result = find(operate_type_list_.begin(), operate_type_list_.end(), type);
    if (result == operate_type_list_.end())
    {
        //插入对应的操作类型
        operate_type_list_.push_back(type);
    }
    else
    {
        return false;
    }
    return true;
}


void DataAnalysisContext::DispatchUpdateConfigInfo()
{
    UpdateConfigDispatch(update_config_list_);
}




