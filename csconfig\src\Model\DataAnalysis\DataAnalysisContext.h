#ifndef __CSADAPT_DATA_ANALYSIS_CONTEXT_H__
#define __CSADAPT_DATA_ANALYSIS_CONTEXT_H__

#include <vector>
#include <string>
#include <map>
#include <list>
#include <memory>
#include <memory>
#include "DataAnalysisDef.h"
#include "DataAnalysisUpdateConfig.h"


class DataAnalysisContext
{
public:
    DataAnalysisContext();
    ~DataAnalysisContext();
    void AddUpdateConfigInfo(int type, std::shared_ptr<void> msg);
    void DispatchUpdateConfigInfo();
    bool AddOperateType(int type);
    
private:
    UpdateConfigInfoMap update_config_list_;
    std::vector<int> operate_type_list_;

};



#endif //__CSADAPT_DATA_ANALYSIS_CONTEXT_H__
