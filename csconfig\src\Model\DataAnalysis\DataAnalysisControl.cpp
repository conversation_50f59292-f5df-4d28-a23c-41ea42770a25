#include "DataAnalysisControl.h"
#include "DataAnalysis.h"
#include "json/json.h"
#include "AkLogging.h"
#include "AkcsCommonDef.h"
#include "util.h"
#include "DataAnalysisGenerate.h"

DataAnalysisDBHandlerMap g_db_handler_map;

void RegDataAnalysisDBHandler(const std::string &name, DataAnalysisDBHandlerPtr &handler)
{
    g_db_handler_map.insert(std::map<std::string, DataAnalysisDBHandlerPtr &>::value_type(name, handler));
}

void DataAnalysisTableHandler(DataAnalysisTableParse &da, DataAnalysisDBHandlerPtr &ptr, DataAnalysisContext &context)
{
    int i = 0;
    int type = da.GetOperation();
    int len = ptr->handler_size_;
    DataAnalysisChangeHandle *change_handler = ptr->handler_;
    for (i = 0; i < len; i++)
    {

        int column_name_index = change_handler[i].column_index;
        if (column_name_index < DA_INDEX_INSERT && type == DataAnalysisTableParse::DBHandleType::DA_OPERATION_UPDATE)
        {
            if (da.IsIndexChange(i) && change_handler[i].handler)
            {
                change_handler[i].handler(da, context);
            }
        }

        if ((column_name_index == DA_INDEX_INSERT && type == DataAnalysisTableParse::DBHandleType::DA_OPERATION_INSERT) || (column_name_index == DA_INDEX_DELETE && type == DataAnalysisTableParse::DBHandleType::DA_OPERATION_DELETE) || (column_name_index == DA_INDEX_UPDATE && type == DataAnalysisTableParse::DBHandleType::DA_OPERATION_UPDATE))
        {
            if (change_handler[i].handler)
            {
                change_handler[i].handler(da, context);
            }
            continue;
        }
    }
}

void RegDaSort(DataAnalysisColumnList &detect_key, DataAnalysisChangeHandle change_handle[], int len)
{
    int index;
    DataAnalysisChangeHandle temp_handle;

    // 根据column_index对da_change_handle进行排序
    for (int i = 1; i < len; i++)
    {
        for (int j = i - 1; j >= 0; j--)
        {
            if (change_handle[j + 1].column_index < change_handle[j].column_index)
            {
                temp_handle = change_handle[j];
                change_handle[j] = change_handle[j + 1];
                change_handle[j + 1] = temp_handle;
            }
            else
                break;
        }
    }

    // 取检测的最大枚举值，对小于它的每一个枚举值插入对应的列名，没有检测的插入空字符串
    for (index = 0; index <= change_handle[len - 4].column_index; index++)
    {
        if (strlen(change_handle[index].column_name) > 0)
        {
            detect_key.push_back(change_handle[index].column_name);
        }
        else
        {
            detect_key.push_back("");
        }
    }
}

void RegDataAnalysisDBAllHandler()
{
    RegDataAnalysisDBAllHandlerGenerate();
}

int DataAnalysisChangeRoleToProjectType(int role)
{
    if (role == ACCOUNT_ROLE_COMMUNITY_MAIN || role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT || role == ACCOUNT_ROLE_COMMUNITY_PM)
    {
        return project::RESIDENCE;
    }
    else if (IsOfficeRole(role))
    {
        return project::OFFICE;
    }
    else
    {
        return project::PERSONAL;
    }
}

/*
int main()
{
    RegDataAnalysisDBAllHandler();
    UpdateConfigInit();
    DataAnalysis
    test("{\"type\":\"common\",\"update\":{\"FeaturePlan\":[[{\"Item\":\"2\",\"Mac\":\"Mac1\"},{\"Item\":\"3\",\"Mac\":\"Mac2\"}]]}}");

    test.Analysis();
}
*/
