#ifndef __CSADAPT_DATA_ANALYSIS_DEF_H__
#define __CSADAPT_DATA_ANALYSIS_DEF_H__
#include<string>
#include<map>
#include<vector>
#include <iostream>
#include <memory>


typedef std::map<std::string/*key*/, std::string/*value*/> DataAnalysisSqlKV;
typedef std::vector<DataAnalysisSqlKV> DataAnalysisRowList;
typedef std::map<std::string/*sheet*/, DataAnalysisRowList> DataAnalysisTableInfoMap;

typedef std::vector<std::string> DataAnalysisColumnList;
typedef std::vector<bool> DataAnalysisColumnChange;


class DataAnalysisTableParse;
class DataAnalysisContext;
typedef int (*DataAnalysisChangeHandlerFunc) (DataAnalysisTableParse &, DataAnalysisContext &);
typedef struct DataAnalysisChangeHandle_t{
    int column_index;/*column index*/
    char column_name[32];/*column name*/
    DataAnalysisChangeHandlerFunc handler; 
}DataAnalysisChangeHandle;

typedef const DataAnalysisColumnList& (*DataAnalysisGetDetectColumnFunc) ();

class DataAnalysisDBHandler
{
public:
    DataAnalysisDBHandler(DataAnalysisChangeHandle *ptr, int size, DataAnalysisGetDetectColumnFunc detect_func){
        handler_ = ptr;
        handler_size_ = size;
        get_detect_func_ = detect_func;
    }
    DataAnalysisChangeHandle* handler_;
    int handler_size_;
    DataAnalysisGetDetectColumnFunc get_detect_func_;
};
typedef std::shared_ptr<DataAnalysisDBHandler> DataAnalysisDBHandlerPtr;

typedef std::map<std::string/*sheet*/, DataAnalysisDBHandlerPtr> DataAnalysisDBHandlerMap;


static const int DA_INDEX_INSERT = 1000;
static const int DA_INDEX_DELETE = 1001;
static const int DA_INDEX_UPDATE = 1002;


enum DADevicesIndex{
    DA_INDEX_DEVICES_NAME,
    DA_INDEX_DEVICES_MAC,
    DA_INDEX_DEVICES_MNG_ID,
    DA_INDEX_DEVICES_UNIT_ID,
    DA_INDEX_DEVICES_NODE,
    DA_INDEX_DEVICES_TYPE,
    DA_INDEX_DEVICES_GRADE,    
    DA_INDEX_DEVICES_PROJECT_TYPE,
    DA_INDEX_DEVICES_NET_GROUP,
    DA_INDEX_DEVICES_CONFIG,
    DA_INDEX_DEVICES_ARMING_FUNCTION,
    DA_INDEX_DEVICES_STAIRSHOW,
    DA_INDEX_DEVICES_RELAY,
    DA_INDEX_DEVICES_SECURITYRELAY,
    DA_INDEX_DEVICES_FLAGS,
};

enum DAPersonalDevicesIndex{
    DA_INDEX_PERSONAL_DEVICES_NAME,
    DA_INDEX_PERSONAL_DEVICES_MAC,
    DA_INDEX_PERSONAL_DEVICES_NODE,
    DA_INDEX_PERSONAL_DEVICES_TYPE,
    DA_INDEX_PERSONAL_DEVICES_NET_GROUP,
    DA_INDEX_PERSONAL_DEVICES_CONFIG,
    DA_INDEX_PERSONAL_DEVICES_STAIRSHOW,
    DA_INDEX_PERSONAL_DEVICES_SECURITYRELAY,
    DA_INDEX_PERSONAL_DEVICES_FLAGS,
};

enum DAFearturePlanIndex{
    DA_INDEX_FEARTUREPLAN_ID,
    DA_INDEX_FEARTUREPLAN_ITEM,
};

enum DAManageFeartureIndex{
    DA_INDEX_MANAGEFEATURE_ACCOUNTID,
    DA_INDEX_MANAGEFEATURE_FEATUREID    
};

enum DAAccountAccessIndex{
    DA_INDEX_ACCOUNTACCESS_ACCOUNT,
    DA_INDEX_ACCOUNTACCESS_ACCESSGROUPID,
};

enum DAUserAccessGroupIndex{
    DA_INDEX_USERACCESSGROUP_ACCOUNT,
    DA_INDEX_USERACCESSGROUP_SCHEDULERTYPE,
    DA_INDEX_USERACCESSGROUP_DATEFLAG,
    DA_INDEX_USERACCESSGROUP_BEGINTIME,
    DA_INDEX_USERACCESSGROUP_ENDTIME,
    DA_INDEX_USERACCESSGROUP_STARTTIME,
    DA_INDEX_USERACCESSGROUP_STOPTIME,
};

enum DAUserAccessGroupDeviceIndex{
    DA_INDEX_USERACCESSGROUPDEVICE_GROUPID,
    DA_INDEX_USERACCESSGROUPDEVICE_MAC,
    DA_INDEX_USERACCESSGROUPDEVICE_RELAY,
    DA_INDEX_USERACCESSGROUPDEVICE_SECURITYRELAY,
};

enum DAPersonalAccountIndex{
    DA_INDEX_PERSONAL_ACCOUNT_NAME,
    DA_INDEX_PERSONAL_ACCOUNT_ROLE,
    DA_INDEX_PERSONAL_ACCOUNT_ACCOUNT,
    DA_INDEX_PERSONAL_ACCOUNT_UNITID,
    DA_INDEX_PERSONAL_ACCOUNT_ENABLEIPDIRECT,
    DA_INDEX_PERSONAL_ACCOUNT_PHONE,
    DA_INDEX_PERSONAL_ACCOUNT_PHONE2,
    DA_INDEX_PERSONAL_ACCOUNT_PHONE3,
    DA_INDEX_PERSONAL_ACCOUNT_PHONECODE,
    DA_INDEX_PERSONAL_ACCOUNT_PHONESTATUS,
    DA_INDEX_PERSONAL_ACCOUNT_NFCCODE,
    DA_INDEX_PERSONAL_ACCOUNT_BLECODE,
    DA_INDEX_PERSONAL_ACCOUNT_PARENTID,
    DA_INDEX_PERSONAL_ACCOUNT_ROOMNAME,
    DA_INDEX_PERSONAL_ACCOUNT_TIMEZONE,
    DA_INDEX_PERSONAL_ACCOUNT_SPECIAL,
    DA_INDEX_PERSONAL_ACCOUNT_CUSTOMIZEFORM,
    DA_INDEX_PERSONAL_ACCOUNT_USERINFO_UUID,
};

enum DAPersonalAccountUserInfoIndex{
    DA_INDEX_PERSONAL_ACCOUNT_USER_EMAIL,
    DA_INDEX_PERSONAL_ACCOUNT_USER_MOBILENUMBER,
    DA_INDEX_PERSONAL_ACCOUNT_USER_UUID,
    DA_INDEX_PERSONAL_ACCOUNT_USER_MAINACCOUNT,
};

enum DAAccountUserInfoIndex{
    DA_INDEX_ACCOUNT_USER_MAINACCOUNT,
};


enum DAPersonalAccountCnfIndex{
    DA_INDEX_PERSONAL_ACCOUNT_CNF_ACCOUNT,
    DA_INDEX_PERSONAL_ACCOUNT_CNF_ENABLEMOTION,
    DA_INDEX_PERSONAL_ACCOUNT_CNF_MOTIONTIME,
    DA_INDEX_PERSONAL_ACCOUNT_CNF_ENABLEROBINCALL,
    DA_INDEX_PERSONAL_ACCOUNT_CNF_ROBINCALLTIME,
    DA_INDEX_PERSONAL_ACCOUNT_CNF_ROBINCALLVAL,
    DA_INDEX_PERSONAL_ACCOUNT_CNF_CALLTYPE,
    DA_INDEX_PERSONAL_ACCOUNT_CNF_WEBRELAY,
    DA_INDEX_PERSONAL_ACCOUNT_CNF_FLAGS,
};

enum DAUserPubDevMngListIndex{
    DA_INDEX_PUBDEVMNGLIST_DEVICESID,
    DA_INDEX_PUBDEVMNGLIST_UNITID,
};

enum DAAccountIndex{
    DA_INDEX_ACCOUNT_ID,
    DA_INDEX_ACCOUNT_GRADE,
    DA_INDEX_ACCOUNT_LOCATION,
    DA_INDEX_ACCOUNT_TIMEZONE,
    DA_INDEX_ACCOUNT_CUSTOMIZEFORM,
    DA_INDEX_ACCOUNT_SIPTYPE,
};

enum DAOfficeInfoIndex{
    DA_INDEX_OFFICEINFO_ID,
    DA_INDEX_OFFICEINFO_ACCOUNTUUID,
    DA_INDEX_OFFICEINFO_STREET,
    DA_INDEX_OFFICEINFO_CITY,
    DA_INDEX_OFFICEINFO_POSTALCODE,
    DA_INDEX_OFFICEINFO_COUNTRY,
    DA_INDEX_OFFICEINFO_STATES,
    DA_INDEX_OFFICEINFO_ENABLEMOTION,
    DA_INDEX_OFFICEINFO_MOTIONTIME,
    DA_INDEX_OFFICEINFO_SWITCH,
    DA_INDEX_OFFICEINFO_FEATUREEXPIRETIME,
    DA_INDEX_OFFICEINFO_NAMEDISPLAY,
};

enum DAPersonalPrivateKeyIndex{
    DA_INDEX_PER_PIN_ID,
    DA_INDEX_PER_PIN_MNGACCOUNTID,
    DA_INDEX_PER_PIN_UNITID,
    DA_INDEX_PER_PIN_GRADE,
    DA_INDEX_PER_PIN_TYPE,
    DA_INDEX_PER_PIN_CODE,
    DA_INDEX_PER_PIN_NODE,
    DA_INDEX_PER_PIN_SCHEDULERTYPE,
    DA_INDEX_PER_PIN_DATEFLAG,
    DA_INDEX_PER_PIN_BEGINTIME,
    DA_INDEX_PER_PIN_ENDTIME,
    DA_INDEX_PER_PIN_STARTTIME,
    DA_INDEX_PER_PIN_STOPTIME,
};

enum DACommPerPrivateKeyIndex{
    DA_INDEX_COMM_PIN_ID,
    DA_INDEX_COMM_PIN_ACCOUNT,
    DA_INDEX_COMM_PIN_CODE,
    DA_INDEX_COMM_PIN_COMMUNITYID,
    DA_INDEX_COMM_PIN_SPECIAL,
};

enum DACommunityRoomIndex{
    DA_INDEX_COMM_ROOM_ID,
    DA_INDEX_COMM_ROOM_UNIT_ID,
    DA_INDEX_COMM_ROOM_ROOMNAME,
    DA_INDEX_COMM_ROOM_FLOOR,
    DA_INDEX_COMM_ROOM_OLDDATAUNIQUE,
};

enum DAPubPrivateKeyIndex{
    DA_INDEX_PUB_PIN_ID,
    DA_INDEX_PUB_PIN_MNGACCOUNTID,
    DA_INDEX_PUB_PIN_WORKID,
    DA_INDEX_PUB_PIN_CODE,
    DA_INDEX_PUB_PIN_CREATETIME,
    DA_INDEX_PUB_PIN_OWNERTYPE,
    DA_INDEX_PUB_PIN_NAME,
    DA_INDEX_PUB_PIN_SCHEDULERTYPE,
    DA_INDEX_PUB_PIN_DATEFLAG,
    DA_INDEX_PUB_PIN_BEGINTIME,
    DA_INDEX_PUB_PIN_ENDTIME,
    DA_INDEX_PUB_PIN_STARTTIME,
    DA_INDEX_PUB_PIN_STOPTIME,
};

enum DAPubPrivateKeyListIndex{
    DA_INDEX_PUB_PIN_LIST_ID,
    DA_INDEX_PUB_PIN_LIST_MAC,
    DA_INDEX_PUB_PIN_LIST_KEYID,
    DA_INDEX_PUB_PIN_LIST_RELAY,
    DA_INDEX_PUB_PIN_LIST_SECURITYRELAY,
};

enum DAPubRfcardKeyIndex{
    DA_INDEX_PUB_RF_ID,
    DA_INDEX_PUB_RF_MNGACCOUNTID,
    DA_INDEX_PUB_RF_WORKID,
    DA_INDEX_PUB_RF_CODE,
    DA_INDEX_PUB_RF_CREATETIME,
    DA_INDEX_PUB_RF_OWNERTYPE,
    DA_INDEX_PUB_RF_NAME,
    DA_INDEX_PUB_RF_SCHEDULERTYPE,
    DA_INDEX_PUB_RF_DATEFLAG,
    DA_INDEX_PUB_RF_BEGINTIME,
    DA_INDEX_PUB_RF_ENDTIME,
    DA_INDEX_PUB_RF_STARTTIME,
    DA_INDEX_PUB_RF_STOPTIME,
};

enum DAPubRfcardKeyListIndex{
    DA_INDEX_PUB_RF_LIST_ID,
    DA_INDEX_PUB_RF_LIST_MAC,
    DA_INDEX_PUB_RF_LIST_KEYID,
    DA_INDEX_PUB_RF_LIST_RELAY,
    DA_INDEX_PUB_RF_LIST_SECURITYRELAY,
};

enum DAPersonalPrivateKeyListIndex{
    DA_INDEX_PERSONAL_PIN_LIST_ID,
    DA_INDEX_PERSONAL_PIN_LIST_MAC,
    DA_INDEX_PERSONAL_PIN_LIST_KEYID,
    DA_INDEX_PERSONAL_PIN_LIST_RELAY,
    DA_INDEX_PERSONAL_PIN_LIST_SECURITYRELAY,
};

enum DAPersonalRfcardKeyListIndex{
    DA_INDEX_PERSONAL_RF_LIST_ID,
    DA_INDEX_PERSONAL_RF_LIST_MAC,
    DA_INDEX_PERSONAL_RF_LIST_KEYID,
    DA_INDEX_PERSONAL_RF_LIST_RELAY,
    DA_INDEX_PERSONAL_RF_LIST_SECURITYRELAY,
};

enum DAThirdPartCameraIndex{
    DA_INDEX_THIRDPART_CAMERA_ID,
    DA_INDEX_THIRDPART_CAMERA_UUID,
    DA_INDEX_THIRDPART_CAMERA_PROJECTUUID,
    DA_INDEX_THIRDPART_CAMERA_UNITID,
    DA_INDEX_THIRDPART_CAMERA_PERSONALACCOUNTUUID,
    DA_INDEX_THIRDPART_CAMERA_GRADE,
    DA_INDEX_THIRDPART_CAMERA_LOCATION,    
    DA_INDEX_THIRDPART_CAMERA_RTSPADDRESS,
    DA_INDEX_THIRDPART_CAMERA_RTSPPORT,
    DA_INDEX_THIRDPART_CAMERA_RTSPUSERNAME,
    DA_INDEX_THIRDPART_CAMERA_RTSPPWD,
    DA_INDEX_THIRDPART_CAMERA_SWITCH,
    DA_INDEX_THIRDPART_CAMERA_MAC,
};

enum DAPerThirdPartCameraIndex{ 
    DA_INDEX_PER_THIRDPART_CAMERA_ID,
    DA_INDEX_PER_THIRDPART_CAMERA_UUID,
    DA_INDEX_PER_THIRDPART_CAMERA_PROJECTUUID,
    DA_INDEX_PER_THIRDPART_CAMERA_PERSONALACCOUNTUUID,
    DA_INDEX_PER_THIRDPART_CAMERA_LOCATION,    
    DA_INDEX_PER_THIRDPART_CAMERA_RTSPADDRESS,
    DA_INDEX_PER_THIRDPART_CAMERA_RTSPPORT,
    DA_INDEX_PER_THIRDPART_CAMERA_RTSPUSERNAME,
    DA_INDEX_PER_THIRDPART_CAMERA_RTSPPWD,
    DA_INDEX_PER_THIRDPART_CAMERA_SWITCH,
    DA_INDEX_PER_THIRDPART_CAMERA_MAC,

};

enum DACommunityInfoIndex{
    DA_INDEX_COMMUNITYINFO_ID,
    DA_INDEX_COMMUNITYINFO_ACCOUNTID,
    DA_INDEX_COMMUNITYINFO_STREET,
    DA_INDEX_COMMUNITYINFO_CITY,
    DA_INDEX_COMMUNITYINFO_POSTALCODE,
    DA_INDEX_COMMUNITYINFO_COUNTRY,
    DA_INDEX_COMMUNITYINFO_STATES,
    DA_INDEX_COMMUNITYINFO_APTPINTYPE,
    DA_INDEX_COMMUNITYINFO_NUMBEROFAPT,
    DA_INDEX_COMMUNITYINFO_SWITCH,
    DA_INDEX_COMMUNITYINFO_ENABLEMOTION,
    DA_INDEX_COMMUNITYINFO_MOTIONTIME,
    DA_INDEX_COMMUNITYINFO_FACEENROLLMENT,
    DA_INDEX_COMMUNITYINFO_IDCARDVERIFICATION,
    DA_INDEX_COMMUNITYINFO_NAMEDISPLAY,
    DA_INDEX_COMMUNITYINFO_ENABLE_PACKAGE_DETECTION,
    // DA_INDEX_COMMUNITYINFO_ENABLE_SOUND_DETECTION,
    // DA_INDEX_COMMUNITYINFO_SOUND_TYPE,
};

enum DAPmAccountMapIndex{
    DA_INDEX_PMACCOUNT_MAP_ID,
    DA_INDEX_PMACCOUNT_MAP_ACCOUNTUUID,
    DA_INDEX_PMACCOUNT_MAP_PERSONALACCOUNTUUID,
    DA_INDEX_PMACCOUNT_MAP_PERSONALACCOUNT,
    DA_INDEX_PMACCOUNT_MAP_PROJECTUUID,
    DA_INDEX_PMACCOUNT_MAP_APPSTATUS,
};
    
enum DAContactBlockIndex{
    DA_INDEX_CONTACT_BLOCK_PER_UUID,
};

enum DAContactFavoriteIndex{
    DA_INDEX_CONTACT_FAVORITE_PER_UUID,
};

enum DAPersonalAccountOfficeInfoIndex{
    DA_INDEX_PERSONAL_ACCOUNT_OFFICE_INFO_FLAGS,
    DA_INDEX_PERSONAL_ACCOUNT_OFFICE_INFO_FLOOR,
    DA_INDEX_PERSONAL_ACCOUNT_OFFICE_INFO_PERSONALACCOUNTUUID,
};
enum DAPersonalAccountCommunityInfoIndex{
    DA_INDEX_PERSONAL_ACCOUNT_COMMUNITY_INFO_PERSONALACCOUNTUUID,
    DA_INDEX_PERSONAL_ACCOUNT_COMMUNITY_INFO_ACCESSFLOOR,
};

enum DAAccessGroupIndex{
    DA_INDEX_ACCESSGROUP_ID,
    DA_INDEX_ACCESSGROUP_NAME,
    DA_INDEX_ACCESSGROUP_COMMUNITYID,
    DA_INDEX_ACCESSGROUP_UNITID,
    DA_INDEX_ACCESSGROUP_SCHEDULERTYPE,
    DA_INDEX_ACCESSGROUP_DATEFLAG,
    DA_INDEX_ACCESSGROUP_BEGINTIME,
    DA_INDEX_ACCESSGROUP_ENDTIME,
    DA_INDEX_ACCESSGROUP_STARTTIME,
    DA_INDEX_ACCESSGROUP_STOPTIME,
};

enum DAAccessGroupDeviceIndex{
    DA_INDEX_ACCESSGROUP_DEVICE_ID,
    DA_INDEX_ACCESSGROUP_DEVICE_ACCESSGROUPID,
    DA_INDEX_ACCESSGROUP_DEVICE_MAC,
    DA_INDEX_ACCESSGROUP_DEVICE_RELAY,
    DA_INDEX_ACCESSGROUP_DEVICE_SECURITYRELAY,
};

enum DADeliveryIndex{
    DA_INDEX_DELIVERY_ID,
    DA_INDEX_DELIVERY_NAME,
    DA_INDEX_DELIVERY_COMMUNITYID,
    DA_INDEX_DELIVERY_PINCODE,
    DA_INDEX_DELIVERY_CARDCODE,
};

enum DAStaffIndex{
    DA_INDEX_STAFF_ID,
    DA_INDEX_STAFF_NAME,
    DA_INDEX_STAFF_COMMUNITYID,
    DA_INDEX_STAFF_CARDCODE,
    DA_INDEX_STAFF_FILENAME,
    DA_INDEX_STAFF_FACEURL,
    DA_INDEX_STAFF_FACEMD5,
};

enum DADeliveryAccessIndex{
    DA_INDEX_DELIVERY_ACCESS_ID,
    DA_INDEX_DELIVERY_ACCESS_DELIVERYID,
    DA_INDEX_DELIVERY_ACCESS_ACCESSGROUPID,
};

enum DAStaffAccessIndex{
    DA_INDEX_STAFF_ACCESS_ID,
    DA_INDEX_STAFF_ACCESS_STAFFID,
    DA_INDEX_STAFF_ACCESS_ACCESSGROUPID,
};

enum DAPersonalRfcardKeyIndex{
    DA_INDEX_PER_RF_ID,
    DA_INDEX_PER_RF_MNGACCOUNTID,
    DA_INDEX_PER_RF_UNITID,
    DA_INDEX_PER_RF_GRADE,
    DA_INDEX_PER_RF_TYPE,
    DA_INDEX_PER_RF_CODE,
    DA_INDEX_PER_RF_NODE,
    DA_INDEX_PER_RF_SCHEDULERTYPE,
    DA_INDEX_PER_RF_DATEFLAG,
    DA_INDEX_PER_RF_BEGINTIME,
    DA_INDEX_PER_RF_ENDTIME,
    DA_INDEX_PER_RF_STARTTIME,
    DA_INDEX_PER_RF_STOPTIME,
    DA_INDEX_PER_RF_ACCOUNTID,
};

enum DACommPerRfKeyIndex{
    DA_INDEX_COMM_RF_ID,
    DA_INDEX_COMM_RF_ACCOUNT,
    DA_INDEX_COMM_RF_CODE,
    DA_INDEX_COMM_RF_COMMUNITYID,
    DA_INDEX_COMM_RF_SPECIAL,
};


enum DACommunityUnitIndex{
    DA_INDEX_COMMUNITY_UNIT_ID,
    DA_INDEX_COMMUNITY_UNIT_UNITNAME,
    DA_INDEX_COMMUNITY_UNIT_MNGACCOUNTID,
    DA_INDEX_COMMUNITY_UNIT_GROUNDFLOOR,
    DA_INDEX_COMMUNITY_UNIT_STARTFLOOR,
    DA_INDEX_COMMUNITY_UNIT_BUILDINGID,
};

enum DADevicesSpecialIndex{
    DA_INDEX_DEVICES_SPECIAL_ID,
    DA_INDEX_DEVICES_SPECIAL_USERACCOUNT,
    DA_INDEX_DEVICES_SPECIAL_DEVMAC,
};

enum DAExtraRelayListIndex{
    DA_INDEX_EXTRA_RELAY_LIST_NAME,
    DA_INDEX_EXTRA_RELAY_LIST_REALY_SW,
    DA_INDEX_EXTRA_RELAY_LIST_INDOOR_MONITOR_UUID,
};

enum DAPersonalIDAccessIndex{
    DA_INDEX_PERSONAL_ID_ACCESS_PER_UUID,
};

enum DAStaffIDAccessIndex{
    DA_INDEX_STAFF_ID_ACCESS_STAFF_UUID,
    DA_INDEX_STAFF_ID_ACCESS_MODE,
    DA_INDEX_STAFF_ID_ACCESS_RUN,
    DA_INDEX_STAFF_ID_ACCESS_SERIAL,
};

enum DADeliveryIDAccessIndex{
    DA_INDEX_DELIVERY_ID_ACCESS_DELIVERY_UUID,
    DA_INDEX_DELIVERY_ID_ACCESS_MODE,
    DA_INDEX_DELIVERY_ID_ACCESS_RUN,
    DA_INDEX_DELIVERY_ID_ACCESS_SERIAL,
};

enum DAAmenityDeviceIndex{
    DA_INDEX_AMENITY_DEVICE_DEV_UUID,
};

enum DACommunityCallRuleIndex{
    DA_INDEX_COMMUNITY_CALLRULE_PERSONAL_ACCOUNT_UUID,
    DA_INDEX_COMMUNITY_CALLRULE_APT_CALL_TYPE,
    DA_INDEX_COMMUNITY_CALLRULE_DATA_VERSION,
};

enum DAAnalogDeviceIndex{
    DA_INDEX_ANALOG_DEVICE_ID,
    DA_INDEX_ANALOG_DEVICE_UUID,
    DA_INDEX_ANALOG_DEVICE_ACCOUNTUUID,
    DA_INDEX_ANALOG_DEVICE_COMMUNITYUNITID,
    DA_INDEX_ANALOG_DEVICE_PERSONALACCOUNTUUID,
    DA_INDEX_ANALOG_DEVICE_ANALOGDEVICENAME,
    DA_INDEX_ANALOG_DEVICE_ANALOGDEVICENUMBER,
    DA_INDEX_ANALOG_DEVICE_DTMFCODE,
    DA_INDEX_ANALOG_DEVICE_CREATETIME,
    DA_INDEX_ANALOG_DEVICE_UPDATETIME,
};
enum DASL20LockIndex{
    DA_INDEX_SL20_LOCK_UUID,
    DA_INDEX_SL20_LOCK_NAME,
    DA_INDEX_SL20_LOCK_MAC,
    DA_INDEX_SL20_LOCK_WIFISTATUS,
    DA_INDEX_SL20_LOCK_LASTCONNECTEDTIME,
    DA_INDEX_SL20_LOCK_DEVICEUUID,
    DA_INDEX_SL20_LOCK_RELAY,
    DA_INDEX_SL20_LOCK_AUTOLOCK_ENABLE,
    DA_INDEX_SL20_LOCK_AUTOLOCKDELAY,
    DA_INDEX_SL20_LOCK_PINCODE,
    DA_INDEX_SL20_LOCK_IS_PINCODE_SYNCHRONIZING,
    DA_INDEX_SL20_LOCK_OFFLINECODE,
    DA_INDEX_SL20_LOCK_BATTERYLEVEL,
    DA_INDEX_SL20_LOCK_MODULEVERSION,
    DA_INDEX_SL20_LOCK_LOCKBODYVERSION,
    DA_INDEX_SL20_LOCK_VERSION,
    DA_INDEX_SL20_LOCK_RBACDATAGROUPUUID,
    DA_INDEX_SL20_LOCK_INSTALLERUUID,
    DA_INDEX_SL20_LOCK_PROJECTTYPE,
    DA_INDEX_SL20_LOCK_ACCOUNTUUID,
    DA_INDEX_SL20_LOCK_COMMUNITYUNITUUID,
    DA_INDEX_SL20_LOCK_PERSONALACCOUNTUUID,
    DA_INDEX_SL20_LOCK_UPDATETIME,
    DA_INDEX_SL20_LOCK_CREATETIME,
    DA_INDEX_SL20_LOCK_KEEP_ALIVE,
    DA_INDEX_SL20_LOCK_SECRET_KEY,
};

enum DAHoldDoorIndex{
    DA_INDEX_HOLD_DOOR_DEVICES_UUID,
    DA_INDEX_HOLD_DOOR_RELAY,
    DA_INDEX_HOLD_DOOR_START_TIME,
    DA_INDEX_HOLD_DOOR_END_TIME,
};

#endif //__CSADAPT_DATA_ANALYSIS_DEF_H__
