#include "DataAnalysisDeliveryAccess.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigOfficeAccessUpdate.h"
#include "UpdateConfigCommAccessUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "PersonalAccount.h"
#include "dbinterface/ProjectUserManage.h"
#include "DataAnalysisdbHandle.h"
#include "DeviceSetting.h"
#include "dbinterface/AccessGroupDB.h"





static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int GetDevicesChangeType();


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "DeliveryAccess";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
    {DA_INDEX_DELIVERY_ACCESS_ID, "ID", ItemChangeHandle},
    {DA_INDEX_DELIVERY_ACCESS_DELIVERYID, "DeliveryID", ItemChangeHandle},
    {DA_INDEX_DELIVERY_ACCESS_ACCESSGROUPID, "AccessGroupID", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string mac;
    uint32_t ag_id = data.GetIndexAsInt(DA_INDEX_DELIVERY_ACCESS_ACCESSGROUPID);
    uint32_t delivery_id = data.GetIndexAsInt(DA_INDEX_DELIVERY_ACCESS_DELIVERYID);
    uint32_t mng_id = dbinterface::AccessGroup::GetMngIDByAgID(ag_id);
    if (mng_id == 0)
    {
        AK_LOG_INFO << local_table_name << " CommonHandle. with ag_id get mngid, mngid is 0";
        return -1;
    }    
    std::string uid;
    uint32_t project_type = data.GetProjectType();

    uint32_t change_type = WEB_COMM_MODIFY_DELIVERY;
    uint32_t office_change_type = WEB_OFFICE_MODIFY_DELIVERY;
    

    //更新数据版本
    dbinterface::ProjectUserManage::UpdateDataVersionByDeliveryID(delivery_id);

    if (project_type == project::OFFICE)
    {   
        //办公
        AK_LOG_INFO << local_table_name << " CommonHandle. office change type=" << office_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(office_change_type) << " node= " << uid
                << " office_id= " << mng_id << " mac= " << mac << " ag_id= " << ag_id;
        UCOfficeAccessUpdatePtr ptr = std::make_shared<UCOfficeAccessUpdate>(office_change_type, mng_id, mac, uid, ag_id);
        context.AddUpdateConfigInfo(UPDATE_OFFICE_ACCESS_UPDATE, ptr);    
    }
    else 
    {
        //社区
        AK_LOG_INFO << local_table_name << " CommonHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
                << " community_id= " << mng_id << " mac= " << mac << " ag_id= " << ag_id;
        UCCommunityAccessUpdatePtr ptr = std::make_shared<UCCommunityAccessUpdate>(change_type, mng_id, mac, uid, ag_id);
        context.AddUpdateConfigInfo(UPDATE_COMM_ACCESS_UPDATE, ptr);
    }
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //新建快递员时insert
    CommonChangeHandle(data, context);
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //快递员删除或者快递员移出权限组,权限组删除的场景会在AccessGroupDevice处理
    CommonChangeHandle(data, context);
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //没有更新，不处理
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaDeliveryAccessHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);
}






