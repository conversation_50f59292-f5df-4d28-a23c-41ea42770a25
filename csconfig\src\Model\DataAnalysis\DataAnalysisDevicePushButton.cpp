#include "DataAnalysisDevicePushButton.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigCommFileUpdate.h"
#include "UpdateConfigCommAccessUpdate.h"
#include "UpdateConfigPersonalFileUpdate.h"
#include "UpdateConfigCommDevUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "DataAnalysisdbHandle.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "DataAnalysisControl.h"

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "DevicePushButton";

static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/ 
   {DA_INDEX_DEVICE_PUSH_BUTTON_DEVICEUUID, "DeviceUUID", ItemChangeHandle},
   {DA_INDEX_DEVICE_PUSH_BUTTON_UUID, "UUID", ItemChangeHandle},
   {DA_INDEX_DEVICE_PUSH_BUTTON_META, "Meta", ItemChangeHandle},
   {DA_INDEX_DEVICE_PUSH_BUTTON_CREATETIME, "CreateTime", ItemChangeHandle},
   {DA_INDEX_DEVICE_PUSH_BUTTON_MODULE0BUTTONNUM, "Module0ButtonNum", ItemChangeHandle},
   {DA_INDEX_DEVICE_PUSH_BUTTON_MODULE1BUTTONNUM, "Module1ButtonNum", ItemChangeHandle},
   {DA_INDEX_DEVICE_PUSH_BUTTON_MODULE2BUTTONNUM, "Module2ButtonNum", ItemChangeHandle},
   {DA_INDEX_DEVICE_PUSH_BUTTON_MODULE3BUTTONNUM, "Module3ButtonNum", ItemChangeHandle},
   {DA_INDEX_DEVICE_PUSH_BUTTON_MODULE4BUTTONNUM, "Module4ButtonNum", ItemChangeHandle},
   {DA_INDEX_DEVICE_PUSH_BUTTON_MODULE5BUTTONNUM, "Module5ButtonNum", ItemChangeHandle},
   {DA_INDEX_INSERT, "", InsertHandle},
   {DA_INDEX_DELETE, "", DeleteHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    if (data.IsIndexChange(DA_INDEX_DEVICE_PUSH_BUTTON_META))
    {
        std::string uuid = data.GetIndex(DA_INDEX_DEVICE_PUSH_BUTTON_UUID);
        std::string device_uuid = data.GetIndex(DA_INDEX_DEVICE_PUSH_BUTTON_DEVICEUUID);
        ResidentDev dev;
        if (0 == dbinterface::ResidentDevices::GetUUIDDev(device_uuid, dev))
        {
            uint32_t mng_id = dev.project_mng_id;
            uint32_t unit_id = dev.unit_id;
            std::string mac = dev.mac;
            std::string uid = dev.node;
            uint32_t change_type = WEB_COMM_MODIFY_MAC_CONTACT;
            
            AK_LOG_INFO << local_table_name << " UpdateHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
                    << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
            UCCommunityFileUpdatePtr fileptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, uid);
            context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, fileptr);
            return 0;
        }
        else if (0 == dbinterface::ResidentPerDevices::GetUUIDDev(device_uuid, dev))
        {   
            uint32_t change_type = WEB_PER_UPDATE_NODE_CONTACT;
            std::string uid = dev.node;
            std::string mac = dev.mac;
            AK_LOG_INFO << local_table_name << " UpdateHandle. personal change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
               << " mac= " << mac;
            UCPersonalFileUpdatePtr fileptr = std::make_shared<UCPersonalFileUpdate>(change_type, mac, uid);
            context.AddUpdateConfigInfo(UPDATE_PERSONAL_FILE_UPDATE, fileptr);
            return 0;
        }
        else
        {
            AK_LOG_WARN << local_table_name << " UpdateHandle. GetUUIDDev is null, device_uuid=" << device_uuid;
            return -1;
        }
        
    }
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}


static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaDevicePushButtonHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}

