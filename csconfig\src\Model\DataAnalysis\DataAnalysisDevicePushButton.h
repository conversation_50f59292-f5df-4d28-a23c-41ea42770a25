#ifndef __CSADAPT_DATAANALYSIS_DEVICE_PUSH_BUTTON_H__
#define __CSADAPT_DATAANALYSIS_DEVICE_PUSH_BUTTON_H__

#include <map>
#include <list>
#include <vector>
#include <string>

enum DADevicePushButtonIndex{
    DA_INDEX_DEVICE_PUSH_BUTTON_DEVICEUUID,
    DA_INDEX_DEVICE_PUSH_BUTTON_UUID,
    DA_INDEX_DEVICE_PUSH_BUTTON_META,
    DA_INDEX_DEVICE_PUSH_BUTTON_CREATETIME,
    DA_INDEX_DEVICE_PUSH_BUTTON_MODULE0BUTTONNUM,
    DA_INDEX_DEVICE_PUSH_BUTTON_MODULE1BUTTONNUM,
    DA_INDEX_DEVICE_PUSH_BUTTON_MODULE2BUTTONNUM,
    DA_INDEX_DEVICE_PUSH_BUTTON_MODULE3BUTTONNUM,
    DA_INDEX_DEVICE_PUSH_BUTTON_MODULE4BUTTONNUM,
    DA_INDEX_DEVICE_PUSH_BUTTON_MODULE5BUTTONNUM,
};

void RegDaDevicePushButtonHandler();

#endif
