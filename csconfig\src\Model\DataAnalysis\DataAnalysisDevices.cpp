#include "DataAnalysisDevices.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigOfficeFileUpdate.h"
#include "UpdateConfigCommFileUpdate.h"
#include "UpdateConfigOfficeDevUpdate.h"
#include "UpdateConfigCommDevUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/Account.h"
#include "util.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int GetDevicesChangeType();


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "Devices";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
	{DA_INDEX_DEVICES_NAME, "Location", ItemChangeHandle},
	{DA_INDEX_DEVICES_MAC, "MAC", ItemChangeHandle},
	{DA_INDEX_DEVICES_MNG_ID, "MngAccountID", ItemChangeHandle},
	{DA_INDEX_DEVICES_UNIT_ID, "UnitID", ItemChangeHandle},
	{DA_INDEX_DEVICES_NODE, "Node", ItemChangeHandle},
	{DA_INDEX_DEVICES_TYPE, "Type", ItemChangeHandle},
	{DA_INDEX_DEVICES_GRADE, "Grade", ItemChangeHandle},
	{DA_INDEX_DEVICES_PROJECT_TYPE, "ProjectType", ItemChangeHandle},
	{DA_INDEX_DEVICES_NET_GROUP, "NetGroupNumber", ItemChangeHandle},
	{DA_INDEX_DEVICES_CONFIG, "Config", ItemChangeHandle},
	{DA_INDEX_DEVICES_ARMING_FUNCTION, "ArmingFunction", ItemChangeHandle},
	{DA_INDEX_DEVICES_STAIRSHOW, "StairShow", ItemChangeHandle},
	{DA_INDEX_DEVICES_RELAY, "Relay", ItemChangeHandle},
	{DA_INDEX_DEVICES_SECURITYRELAY, "SecurityRelay", ItemChangeHandle},
	{DA_INDEX_DEVICES_FLAGS, "Flags", ItemChangeHandle},
	{DA_INDEX_INSERT, "", InsertHandle},
	{DA_INDEX_DELETE, "", DeleteHandle},
	{DA_INDEX_UPDATE, "", UpdateHandle}
};

static void UpdateCommunityCallContact(int mng_id, int unit_id, const std::string& mac, const std::string& node, DataAnalysisContext &context)
{
    uint32_t change_type = WEB_COMM_UPDATE_COMMUNITY_CALLS;

    if (dbinterface::Account::GetCommunityContactSwitch(mng_id))
    {
        AK_LOG_INFO << local_table_name << " UpdateHandle WEB_COMM_UPDATE_COMMUNITY_CALLS. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << node
            << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
        UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, node);
        context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
    }
}

static bool IsBuildingManageChange(DataAnalysisTableParse &data)
{
    int flags_after = data.GetIndexAsInt(DA_INDEX_DEVICES_FLAGS);
    int flags_before = data.GetBeforeIndexAsInt(DA_INDEX_DEVICES_FLAGS);

    //判断楼栋管理全选开关是否变化
    if (SwitchHandle(flags_after, DeviceSwitch::DEV_MNG_ALL) != SwitchHandle(flags_before, DeviceSwitch::DEV_MNG_ALL))
    {
        return true;
    }
    return false;
}

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    uint32_t mng_id = data.GetIndexAsInt(DA_INDEX_DEVICES_MNG_ID);
    uint32_t unit_id = data.GetIndexAsInt(DA_INDEX_DEVICES_UNIT_ID);
    uint32_t type = data.GetIndexAsInt(DA_INDEX_DEVICES_TYPE);
    uint32_t grade = data.GetIndexAsInt(DA_INDEX_DEVICES_GRADE);
    std::string uid = data.GetIndex(DA_INDEX_DEVICES_NODE);
    std::string mac = data.GetIndex(DA_INDEX_DEVICES_MAC);
    uint32_t project_type = data.GetIndexAsInt(DA_INDEX_DEVICES_PROJECT_TYPE);
    uint32_t change_type = 0;
    uint32_t offic_change_type = 0;
    int  is_per = 0;
    std::vector<std::string> macs;
    macs.push_back(mac);

    if (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
    {
        change_type = WEB_COMM_PUB_ADD_DEV;
        offic_change_type = WEB_OFFICE_PUB_ADD_DEV;
    }
    else if (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
        change_type = WEB_COMM_UNIT_ADD_DEV;
        offic_change_type = WEB_OFFICE_UNIT_ADD_DEV;
    }
    else
    {
        change_type = WEB_COMM_ADD_DEV;
        offic_change_type = WEB_OFFICE_ADD_DEV;
        is_per = 1;
    }
    
    if (project_type == project::OFFICE)
    {   
        AK_LOG_INFO << local_table_name << " UpdateHandle. office change type=" << offic_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(offic_change_type) << " node= " << uid
                << " office_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
        UCOfficeFileUpdatePtr fileptr = std::make_shared<UCOfficeFileUpdate>(offic_change_type, mng_id, unit_id, mac, uid);
        UCOfficeDevUpdatePtr devptr = std::make_shared<UCOfficeDevUpdate>(offic_change_type, macs);
        context.AddUpdateConfigInfo(UPDATE_OFFICE_FILE_UPDATE, fileptr);
        context.AddUpdateConfigInfo(UPDATE_OFFICE_DEV_UPDATE, devptr);
    }
    else 
    {
        AK_LOG_INFO << local_table_name << " UpdateHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
                << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
        UCCommunityFileUpdatePtr fileptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, uid);
        UCCommunityDevUpdatePtr devptr = std::make_shared<UCCommunityDevUpdate>(change_type, macs);
        context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, fileptr);
        context.AddUpdateConfigInfo(UPDATE_COMM_DEV_UPDATE, devptr);
    }

    //有关联的户户通联系人刷新    
    if (is_per && project_type == project::RESIDENCE)
    {
        UpdateCommunityCallContact(mng_id, unit_id, mac, uid, context);
    }
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    uint32_t mng_id = data.GetIndexAsInt(DA_INDEX_DEVICES_MNG_ID);
    uint32_t unit_id = data.GetIndexAsInt(DA_INDEX_DEVICES_UNIT_ID);
    uint32_t type = data.GetIndexAsInt(DA_INDEX_DEVICES_TYPE);
    uint32_t grade = data.GetIndexAsInt(DA_INDEX_DEVICES_GRADE);
    std::string uid = data.GetIndex(DA_INDEX_DEVICES_NODE);
    std::string mac = data.GetIndex(DA_INDEX_DEVICES_MAC);
    uint32_t project_type = data.GetIndexAsInt(DA_INDEX_DEVICES_PROJECT_TYPE);
    uint32_t change_type = 0;
    uint32_t offic_change_type = 0;
    int is_per = 0;
    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    // 设备还在,直接跳过后续操作
    if ((0 == dbinterface::ResidentDevices::GetMacDev(mac, dev)) || (0 == dbinterface::ResidentPerDevices::GetMacDev(mac, dev)))
    {
        AK_LOG_INFO << local_table_name << " DeleteHandle. Mac is not null, mac=" << mac;
        return 0;
    }
    std::vector<std::string> macs;
    macs.push_back(mac);

    if (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
    {
        change_type = WEB_COMM_PUB_DEL_DEV;
        offic_change_type = WEB_OFFICE_PUB_DEL_DEV;
    }
    else if (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
        change_type = WEB_COMM_UNIT_DEL_DEV;
        offic_change_type = WEB_OFFICE_UNIT_DEL_DEV;
    }
    else
    {
        is_per = 1;
        change_type = WEB_COMM_DEL_DEV;
        offic_change_type = WEB_OFFICE_DEL_DEV;
    }
    
    if (project_type == project::OFFICE)
    {   
        AK_LOG_INFO << local_table_name << " UpdateHandle. office change type=" << offic_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(offic_change_type) << " node= " << uid
                << " office_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
        UCOfficeFileUpdatePtr fileptr = std::make_shared<UCOfficeFileUpdate>(offic_change_type, mng_id, unit_id, mac, uid);
        UCOfficeDevUpdatePtr devptr = std::make_shared<UCOfficeDevUpdate>(offic_change_type, macs);
        context.AddUpdateConfigInfo(UPDATE_OFFICE_FILE_UPDATE, fileptr);
        context.AddUpdateConfigInfo(UPDATE_OFFICE_DEV_UPDATE, devptr);
    }
    else 
    {
        AK_LOG_INFO << local_table_name << " UpdateHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
                << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
        UCCommunityFileUpdatePtr fileptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, uid);
        UCCommunityDevUpdatePtr devptr = std::make_shared<UCCommunityDevUpdate>(change_type, macs);
        context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, fileptr);
        context.AddUpdateConfigInfo(UPDATE_COMM_DEV_UPDATE, devptr);
    }       

    //有关联的户户通联系人刷新    
    if (is_per && project_type == project::RESIDENCE)
    {
        UpdateCommunityCallContact(mng_id, unit_id, mac, uid, context);
    }
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    uint32_t mng_id = data.GetIndexAsInt(DA_INDEX_DEVICES_MNG_ID);
    uint32_t unit_id = data.GetIndexAsInt(DA_INDEX_DEVICES_UNIT_ID);
    uint32_t type = data.GetIndexAsInt(DA_INDEX_DEVICES_TYPE);
    uint32_t grade = data.GetIndexAsInt(DA_INDEX_DEVICES_GRADE);
    std::string uid = data.GetIndex(DA_INDEX_DEVICES_NODE);
    std::string mac = data.GetIndex(DA_INDEX_DEVICES_MAC);
    uint32_t project_type = data.GetIndexAsInt(DA_INDEX_DEVICES_PROJECT_TYPE);
    uint32_t change_type = 0;
    uint32_t offic_change_type = 0;    
    int  is_per = 0;
    std::vector<std::string> macs;
    macs.push_back(mac);

    if (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
    {
        change_type = WEB_COMM_PUB_MODIFY_DEV;
        offic_change_type = WEB_OFFICE_PUB_MODIFY_DEV;
        if (data.IsIndexChange(DA_INDEX_DEVICES_RELAY) || data.IsIndexChange(DA_INDEX_DEVICES_SECURITYRELAY) || IsBuildingManageChange(data))
        {
            //更新数据版本
            dbinterface::ProjectUserManage::UpdateDataVersionByPubMac(mng_id, mac);
        }
    }
    else if (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
        change_type = WEB_COMM_UNIT_MODIFY_DEV;
        offic_change_type = WEB_OFFICE_UNIT_MODIFY_DEV;
        if (data.IsIndexChange(DA_INDEX_DEVICES_RELAY) || data.IsIndexChange(DA_INDEX_DEVICES_SECURITYRELAY))
        {
            //更新数据版本
            dbinterface::ProjectUserManage::UpdateAccountDataVersionByUnitMac(unit_id, mac);
        }
    }
    else
    {
        is_per = 1;
        change_type = WEB_COMM_MODIFY_DEV;
        offic_change_type = WEB_OFFICE_MODIFY_DEV;
        if (data.IsIndexChange(DA_INDEX_DEVICES_RELAY) || data.IsIndexChange(DA_INDEX_DEVICES_SECURITYRELAY))
        {
            //更新数据版本
            dbinterface::ProjectUserManage::UpdateDataVersionByNode(uid);
        }
    }
    
    if (project_type == project::OFFICE)
    {   
        AK_LOG_INFO << local_table_name << " UpdateHandle. office change type=" << offic_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(offic_change_type) << " node= " << uid
                << " office_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
        UCOfficeFileUpdatePtr fileptr = std::make_shared<UCOfficeFileUpdate>(offic_change_type, mng_id, unit_id, mac, uid);
        UCOfficeDevUpdatePtr devptr = std::make_shared<UCOfficeDevUpdate>(offic_change_type, macs);
        context.AddUpdateConfigInfo(UPDATE_OFFICE_FILE_UPDATE, fileptr);
        context.AddUpdateConfigInfo(UPDATE_OFFICE_DEV_UPDATE, devptr);
    }
    else 
    {
        AK_LOG_INFO << local_table_name << " UpdateHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
                << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
        UCCommunityFileUpdatePtr fileptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, uid);
        UCCommunityDevUpdatePtr devptr = std::make_shared<UCCommunityDevUpdate>(change_type, macs);
        context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, fileptr);
        context.AddUpdateConfigInfo(UPDATE_COMM_DEV_UPDATE, devptr);
    }

    //有关联的户户通联系人刷新    
    if (is_per && project_type == project::RESIDENCE && data.IsIndexChange(DA_INDEX_DEVICES_NAME))
    {
        UpdateCommunityCallContact(mng_id, unit_id, mac, uid, context);
    }
    return 0;

}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaDevicesHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);

}






