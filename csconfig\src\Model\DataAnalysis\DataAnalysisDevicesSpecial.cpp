#include "DataAnalysis.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisDevicesSpecial.h"
#include <memory>
#include <string.h>
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "UpdateConfigCommDevUpdate.h"

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "DevicesSpecial";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
    {DA_INDEX_DEVICES_SPECIAL_ID, "ID", ItemChangeHandle},
    {DA_INDEX_DEVICES_SPECIAL_USERACCOUNT, "Account", ItemChangeHandle},
    {DA_INDEX_DEVICES_SPECIAL_DEVMAC, "MAC", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string mac = data.GetIndex(DA_INDEX_DEVICES_SPECIAL_DEVMAC);
    if(mac.size() == 0)
    {
        AK_LOG_WARN << "Insert into device special data wrong, mac is empty";
        return 0;
    }
    uint32_t change_type = WEB_COMM_ADD_INDOOR_PLAN_DEV;
    std::vector<std::string> macs;
    macs.push_back(mac);

    AK_LOG_INFO << local_table_name << " InsertHandle. change type =" << change_type << " mac= " << mac;
    UCCommunityDevUpdatePtr devptr = std::make_shared<UCCommunityDevUpdate>(change_type, macs);//只通知设备上报状态，不涉及配置的更新
    return 0;
    context.AddUpdateConfigInfo(UPDATE_COMM_DEV_UPDATE, devptr);
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaDevicesSpecialHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);

}