#include "DataAnalysisFeaturePlan.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigPayPlan.h"
#include "AkLogging.h"


static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "FeaturePlan";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
	{DA_INDEX_FEARTUREPLAN_ID, "ID", ItemChangeHandle},
	{DA_INDEX_FEARTUREPLAN_ITEM, "Item", ItemChangeHandle},
	{DA_INDEX_INSERT, "", InsertHandle},
	{DA_INDEX_DELETE, "", DeleteHandle},
	{DA_INDEX_UPDATE, "", UpdateHandle}
};



static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0; 
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //暂时没有用到该表的数据分析
    /*
    if (data.IsIndexChange(DA_INDEX_FEARTUREPLAN_ITEM))
    {
        int id = data.GetIndexAsInt(DA_INDEX_FEARTUREPLAN_ID);
        int before_item = 0;
        int after_item = 0;
        data.GetDetectChangeByIndex(DA_INDEX_FEARTUREPLAN_ITEM, before_item, after_item);
        
        AK_LOG_INFO << local_table_name << " UpdateHandle. ID=" << id << " item change:" << before_item << " to " << after_item ;
        UCPayPlanMsgPtr ptr = std::make_shared<UCPayPlanMsg>(UCPayPlanMsg::UPDATE_TYPE::UPDATE_TYPE_PLAN_ITME_CHANGE);
        ptr->SetPlanID(id );
        context.AddUpdateConfigInfo(UPDATE_CONFIG_PAY_PLAN, ptr);      
    }
    */
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaFeatureplanHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);

}






