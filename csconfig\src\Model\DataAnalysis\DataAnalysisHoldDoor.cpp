#include "DataAnalysisAccessGroupDevice.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigCommAccessUpdate.h"
#include "UpdateConfigCommFileUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "PersonalAccount.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "DataAnalysisdbHandle.h"




static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "HoldDoor";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
    {DA_INDEX_HOLD_DOOR_DEVICES_UUID, "DevicesUUID", ItemChangeHandle},
    {DA_INDEX_HOLD_DOOR_RELAY, "Relay", ItemChangeHandle},
    {DA_INDEX_HOLD_DOOR_START_TIME, "StartTime", ItemChangeHandle},
    {DA_INDEX_HOLD_DOOR_END_TIME, "EndTime", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string devicesUuid = data.GetIndex(DA_INDEX_HOLD_DOOR_DEVICES_UUID);
    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    if (0 != dbinterface::ResidentDevices::GetUUIDDev(devicesUuid, dev))
    {
        AK_LOG_INFO << local_table_name << " CommonHandle. Mac uuid is null, mac uuid =" << devicesUuid;
        return -1;
    }
    uint32_t mng_id = dev.project_mng_id;
    std::string mac = dev.mac;
    std::string uid = dev.node;
    uint32_t unit_id = dev.unit_id;
    uint32_t project_type = data.GetProjectType();

    uint32_t change_type = WEB_COMM_MODIFY_HOLD_DOOR;
    

    if (project_type == project::OFFICE)
    {   
        //办公
        AK_LOG_INFO << local_table_name << " CommonHandle. office change type not allow";
    }
    else 
    {
        //社区
        AK_LOG_INFO << local_table_name << " CommonHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
                << " community_id= " << mng_id << " mac= " << mac;
        UCCommunityAccessUpdatePtr accessptr = std::make_shared<UCCommunityAccessUpdate>(change_type, mng_id, mac, uid);
        context.AddUpdateConfigInfo(UPDATE_COMM_ACCESS_UPDATE, accessptr);

        UCCommunityFileUpdatePtr fileptr = std::make_shared<UCCommunityFileUpdate>(WEB_COMM_UPDATE_MAC_CONFIG, mng_id, unit_id, mac, uid);
        context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, fileptr);
    }
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    CommonChangeHandle(data, context);
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    CommonChangeHandle(data, context);
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    CommonChangeHandle(data, context);
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaHoldDoorHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);
}






