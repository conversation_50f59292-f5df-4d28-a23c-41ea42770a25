#include "DataAnalysis.h"
#include "DataAnalysisCommon.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisIndoorMonitorConfig.h"
#include <memory>
#include <string.h>
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"


enum DAIndoorMonitorConfigIndex{
    DA_INDEX_INDOOR_MONITOR_CONFIG_DEVICE_UUID,
    DA_INDEX_INDOOR_MONITOR_CONFIG_EXTRA_RELAY_SW,
    DA_INDEX_INDOOR_MONITOR_CONFIG_EXTRA_RELAY_TYPE,
    DA_INDEX_INDOOR_MONITOR_CONFIG_EXTRA_RELAY_MODE,
    DA_INDEX_INDOOR_MONITOR_CONFIG_RELAY_MODE_CONFIG,
    DA_INDEX_INDOOR_MONITOR_CONFIG_META,
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "IndoorMonitorConfig";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
    {DA_INDEX_INDOOR_MONITOR_CONFIG_DEVICE_UUID, "DeviceUUID", ItemChangeHandle},
    {DA_INDEX_INDOOR_MONITOR_CONFIG_EXTRA_RELAY_SW, "ExtraRelaySwitch", ItemChangeHandle},
    {DA_INDEX_INDOOR_MONITOR_CONFIG_EXTRA_RELAY_TYPE, "ExtraRelayType", ItemChangeHandle},
    {DA_INDEX_INDOOR_MONITOR_CONFIG_EXTRA_RELAY_MODE, "ExtraRelayMode", ItemChangeHandle},
    {DA_INDEX_INDOOR_MONITOR_CONFIG_RELAY_MODE_CONFIG, "RelayModeConfig", ItemChangeHandle},
    {DA_INDEX_INDOOR_MONITOR_CONFIG_META, "Meta", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string dev_uuid = data.GetIndex(DA_INDEX_INDOOR_MONITOR_CONFIG_DEVICE_UUID);
    if(dev_uuid.size() == 0)
    {
        AK_LOG_WARN << "update IndoorMonitorConfig data wrong, mac is empty";
        return 0;
    }
    DevicesModifyUpdateConfig(dev_uuid, local_table_name, context);
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaIndoorMonitorConfigHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);
}