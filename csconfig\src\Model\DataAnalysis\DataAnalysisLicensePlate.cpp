#include "DataAnalysisLicensePlate.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigCommFileUpdate.h"
#include "UpdateConfigCommAccessUpdate.h"
#include "UpdateConfigPersonalFileUpdate.h"
#include "UpdateConfigCommDevUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "DataAnalysisdbHandle.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/Account.h"


static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "LicensePlate";
/*复制到DataAnalysisDef.h*/ 
enum DALicensePlateIndex{
    DA_INDEX_LICENSE_PLATE_PLATE,
    DA_INDEX_LICENSE_PLATE_UHF,
    DA_INDEX_LICENSE_PLATE_MANAGEUUID,
    DA_INDEX_LICENSE_PLATE_PERSONALACCOUNTUUID,
    DA_INDEX_LICENSE_PLATE_TIMECONTROL,
    DA_INDEX_LICENSE_PLATE_BEGINTIME,
    DA_INDEX_LICENSE_PLATE_ENDTIME,
};
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
   {DA_INDEX_LICENSE_PLATE_PLATE, "Plate", ItemChangeHandle},
   {DA_INDEX_LICENSE_PLATE_UHF, "UHF", ItemChangeHandle},
   {DA_INDEX_LICENSE_PLATE_MANAGEUUID, "ManageUUID", ItemChangeHandle},
   {DA_INDEX_LICENSE_PLATE_PERSONALACCOUNTUUID, "PersonalAccountUUID", ItemChangeHandle},
   {DA_INDEX_LICENSE_PLATE_TIMECONTROL, "TimeControl", ItemChangeHandle},
   {DA_INDEX_LICENSE_PLATE_BEGINTIME, "BeginTime", ItemChangeHandle},
   {DA_INDEX_LICENSE_PLATE_ENDTIME, "EndTime", ItemChangeHandle},
   {DA_INDEX_INSERT, "", InsertHandle},
   {DA_INDEX_DELETE, "", DeleteHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};


static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string mac;
    uint32_t mng_id = 0;
    uint32_t change_type;
    std::string uid;

    uint32_t project_type = data.GetProjectType();
    if (project_type == project::PERSONAL)
    {
        //单住户
        std::string main_personal_uuid = data.GetIndex(DA_INDEX_LICENSE_PLATE_MANAGEUUID);
        std::string personal_uuid = data.GetIndex(DA_INDEX_LICENSE_PLATE_PERSONALACCOUNTUUID);
        ResidentPerAccount main_per_account;
        memset(&main_per_account, 0, sizeof(main_per_account));
        if (0 != dbinterface::ResidentPersonalAccount::GetUUIDAccount(main_personal_uuid, main_per_account))
        {
            AK_LOG_WARN << local_table_name << " CommonChangeHandle. GetUUID Per Account is null, personal_uuid=" << main_personal_uuid;    
            return -1;
        }

        uid = main_per_account.account;
        //更新用户数据版本
        dbinterface::ProjectUserManage::UpdateAccountDataVersionByUUID(personal_uuid);
        change_type = WEB_PER_UPDATE_RF;
        UCPersonalFileUpdatePtr fileptr = std::make_shared<UCPersonalFileUpdate>(change_type, mac, uid);
        context.AddUpdateConfigInfo(UPDATE_PERSONAL_FILE_UPDATE, fileptr);
    }
    else
    {
        // 新社区
        std::string mng_uuid = data.GetIndex(DA_INDEX_LICENSE_PLATE_MANAGEUUID);
        dbinterface::AccountInfo account;
        if (0 != dbinterface::Account::GetAccountByUUID(mng_uuid, account))
        {
            AK_LOG_WARN << local_table_name << " UpdateHandle. with mng_uuid get account is null, mng_uuid is:" << mng_uuid;
            return -1;
        }
        std::string personal_uuid = data.GetIndex(DA_INDEX_LICENSE_PLATE_PERSONALACCOUNTUUID);
        ResidentPerAccount per_account;
        memset(&per_account, 0, sizeof(per_account));
        if (0 != dbinterface::ResidentPersonalAccount::GetUUIDAccount(personal_uuid, per_account))
        {
            AK_LOG_WARN << local_table_name << " CommonChangeHandle. GetUUID User Account is null, personal_uuid=" << personal_uuid;    
            return -1;
        }
        if (per_account.role == ACCOUNT_ROLE_COMMUNITY_MAIN)
        {
            uid = per_account.account;
        }
        else
        {
            ResidentPerAccount main_per_account;
            memset(&main_per_account, 0, sizeof(main_per_account));
            if (0 != dbinterface::ResidentPersonalAccount::GetUUIDAccount(per_account.parent_uuid, main_per_account))
            {
                AK_LOG_WARN << local_table_name << " CommonChangeHandle. GetUUID Main Account is null, personal_uuid=" << per_account.parent_uuid;    
                return -1;
            }
            uid = main_per_account.account;
        }
        //更新用户数据版本
        dbinterface::ProjectUserManage::UpdateAccountDataVersionByUUID(personal_uuid);
        mng_id = account.id;
        change_type = WEB_COMM_MODIFY_USER_META;
        UCCommunityAccessUpdatePtr accessptr = std::make_shared<UCCommunityAccessUpdate>(change_type, mng_id, mac, uid);
        context.AddUpdateConfigInfo(UPDATE_COMM_ACCESS_UPDATE, accessptr);
    }
    AK_LOG_INFO << "CommonHandle: table_name=" << local_table_name << ", change_type=" << change_type
        << ", mng_id= " << mng_id << ", node= " << uid << ", mac= " << mac;
    return 0;
}

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    CommonChangeHandle(data, context);
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    CommonChangeHandle(data, context);
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    CommonChangeHandle(data, context);
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaLicensePlateHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}

