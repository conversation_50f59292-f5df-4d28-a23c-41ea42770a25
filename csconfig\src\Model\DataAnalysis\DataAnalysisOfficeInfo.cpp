#include "DataAnalysisOfficeInfo.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigOfficeFileUpdate.h"
#include "UpdateConfigCommFileUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "PersonalAccount.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/Account.h"
#include "dbinterface/office/OfficeInfo.h"




static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "OfficeInfo";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
    {DA_INDEX_OFFICEINFO_ID, "ID", ItemChangeHandle},
    {DA_INDEX_OFFICEINFO_ACCOUNTUUID, "AccountUUID", ItemChangeHandle},
    {DA_INDEX_OFFICEINFO_STREET, "Street", ItemChangeHandle},
    {DA_INDEX_OFFICEINFO_CITY, "City", ItemChangeHandle},
    {DA_INDEX_OFFICEINFO_POSTALCODE, "PostalCode", ItemChangeHandle},
    {DA_INDEX_OFFICEINFO_COUNTRY, "Country", ItemChangeHandle},
    {DA_INDEX_OFFICEINFO_STATES, "States", ItemChangeHandle},
    {DA_INDEX_OFFICEINFO_ENABLEMOTION, "EnableMotion", ItemChangeHandle},
    {DA_INDEX_OFFICEINFO_MOTIONTIME, "MotionTime", ItemChangeHandle},
    {DA_INDEX_OFFICEINFO_SWITCH, "Switch", ItemChangeHandle},
    {DA_INDEX_OFFICEINFO_FEATUREEXPIRETIME, "FeatureExpireTime", ItemChangeHandle},
    {DA_INDEX_OFFICEINFO_NAMEDISPLAY, "NameDisplay", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //创建office时没有设备，所以无需刷新配置
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //办公的删除在special处理
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //只对小区和办公的account做分析更新处理
    if (data.IsIndexChange(DA_INDEX_OFFICEINFO_STREET) 
        || data.IsIndexChange(DA_INDEX_OFFICEINFO_CITY)
        || data.IsIndexChange(DA_INDEX_OFFICEINFO_POSTALCODE)
        || data.IsIndexChange(DA_INDEX_OFFICEINFO_COUNTRY)
        || data.IsIndexChange(DA_INDEX_OFFICEINFO_STATES)
        || data.IsIndexChange(DA_INDEX_OFFICEINFO_ENABLEMOTION)
        || data.IsIndexChange(DA_INDEX_OFFICEINFO_MOTIONTIME)
        || data.IsIndexChange(DA_INDEX_OFFICEINFO_SWITCH)
        || data.IsIndexChange(DA_INDEX_OFFICEINFO_FEATUREEXPIRETIME)
        || data.IsIndexChange(DA_INDEX_OFFICEINFO_NAMEDISPLAY))
    {
        std::string uid;
        std::string mac;
        std::string mng_uuid = data.GetIndex(DA_INDEX_OFFICEINFO_ACCOUNTUUID);
        uint32_t mng_id = 0;
        dbinterface::AccountInfo account;
        if (0 != dbinterface::Account::GetAccountByUUID(mng_uuid, account))
        {
            AK_LOG_WARN << local_table_name << " UpdateHandle. with mng_uuid get account is null, mng_uuid is:" << mng_uuid;
            return -1;
        }
        
        mng_id = account.id;
        uint32_t unit_id = 0;
        uint32_t office_change_type= WEB_OFFICE_INFO;

        if (data.IsIndexChange(DA_INDEX_OFFICEINFO_SWITCH))
        {
            int switch_after = data.GetIndexAsInt(DA_INDEX_OFFICEINFO_SWITCH);
            int switch_befor = data.GetBeforeIndexAsInt(DA_INDEX_OFFICEINFO_SWITCH);
            int change_bit = GetFirstDiffBit(switch_befor, switch_after);

            switch(change_bit)
            {
                case OfficeInfo::SwitchType::AllowPin:
                {
                    //是否允许用户使用PIN,刷user
                    office_change_type = WEB_OFFICE_ALLOW_CREATE_PIN;
                    dbinterface::ProjectUserManage::UpdateOfficeAllAccountDataVersion(mng_id);
                    break;
                }
                case OfficeInfo::SwitchType::Landline:
                case OfficeInfo::SwitchType::OfflineNotify:
                case OfficeInfo::SwitchType::SIMNotify:
                {
                    break;
                }
                default:
                {
                    AK_LOG_INFO << "no need to update, change_bit= " << change_bit;            
                }
            }
        }
        
        if (data.IsIndexChange(DA_INDEX_OFFICEINFO_FEATUREEXPIRETIME))
        {
            //更新数据版本
            dbinterface::ProjectUserManage::UpdateOfficeAllAccountDataVersion(mng_id);
            office_change_type = WEB_OFFICE_FEATURE_PLAN_RENEW;
        }
        
        if (data.IsIndexChange(DA_INDEX_OFFICEINFO_NAMEDISPLAY))
        {
            office_change_type = WEB_OFFICE_MODIFY_CONTACT_DISPLAY_ORDER;
        }

        //办公
        AK_LOG_INFO << local_table_name << " UpdateHandle. office change type=" << office_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(office_change_type)<< " node= " << uid
                << " office_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
        UCOfficeFileUpdatePtr ptr = std::make_shared<UCOfficeFileUpdate>(office_change_type, mng_id, unit_id, mac, uid);
        context.AddUpdateConfigInfo(UPDATE_OFFICE_FILE_UPDATE, ptr);

    }
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaOfficeInfoHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);

}






