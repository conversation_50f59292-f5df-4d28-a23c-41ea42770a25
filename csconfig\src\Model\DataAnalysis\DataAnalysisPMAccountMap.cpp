#include "DataAnalysisPMAccountMap.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigOfficeFileUpdate.h"
#include "UpdateConfigCommFileUpdate.h"
#include "UpdateConfigPersonalFileUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "PersonalAccount.h"
#include "dbinterface/ProjectUserManage.h"
#include "DataAnalysisdbHandle.h"



static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "PmAccountMap";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
    {DA_INDEX_PMACCOUNT_MAP_ID, "ID", ItemChangeHandle},
    {DA_INDEX_PMACCOUNT_MAP_ACCOUNTUUID, "AccountUUID", ItemChangeHandle},
    {DA_INDEX_PMACCOUNT_MAP_PERSONALACCOUNTUUID, "PersonalAccountUUID", ItemChangeHandle},
    {DA_INDEX_PMACCOUNT_MAP_PERSONALACCOUNT, "PersonalAccount", ItemChangeHandle},
    {DA_INDEX_PMACCOUNT_MAP_PROJECTUUID, "ProjectUUID", ItemChangeHandle},
    {DA_INDEX_PMACCOUNT_MAP_APPSTATUS, "AppStatus", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //PersonalAccount表数据分析已作处理，这里不再处理
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //PersonalAccount表数据分析已作处理，这里不再处理
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    if (data.IsIndexChange(DA_INDEX_PMACCOUNT_MAP_APPSTATUS))
    {
        std::string uid = data.GetIndex(DA_INDEX_PMACCOUNT_MAP_PERSONALACCOUNT);
        std::string mac;
        UserInfo user_info;
        memset(&user_info, 0, sizeof(user_info));
        if (dbhandle::DAInfo::GetUserInfoByUid(uid, user_info) != 0)
        {
            AK_LOG_INFO << local_table_name << " UpdateHandle. User is null, uid=" << uid;
            return -1;
        }
        std::string node = user_info.node;
        uint32_t mng_id = user_info.mng_id;
        uint32_t unit_id = 0;

        //更新数据版本
        dbinterface::ProjectUserManage::UpdateAccountDataVersionByUid(uid);

        if (user_info.role == ACCOUNT_ROLE_COMMUNITY_PM)
        {
            uint32_t pm_change_type = WEB_COMM_MODIFY_PM_APP_STATUS;
            //pm账户
            AK_LOG_INFO << local_table_name << " UpdateHandle. pm community change type=" << pm_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(pm_change_type);
            UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(pm_change_type, mng_id, unit_id, mac, uid);
            context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
        }
    }
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaPMAccountMapHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);

}






