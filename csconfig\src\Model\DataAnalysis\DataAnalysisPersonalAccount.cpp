#include "DataAnalysisPersonalAccount.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigOfficeFileUpdate.h"
#include "UpdateConfigCommFileUpdate.h"
#include "UpdateConfigPersonalFileUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "PersonalAccount.h"
#include "dbinterface/ProjectUserManage.h"
#include "DataAnalysisdbHandle.h"
#include "dbinterface/Account.h"
#include "dbinterface/CommunityUnit.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"



static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "PersonalAccount";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
    {DA_INDEX_PERSONAL_ACCOUNT_NAME, "Name", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_ROLE, "Role", ItemChangeHandle},    
    {DA_INDEX_PERSONAL_ACCOUNT_ACCOUNT, "Account", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_UNITID, "UnitID", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_ENABLEIPDIRECT, "EnableIpDirect", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_PHONE, "Phone", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_PHONE2, "Phone2", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_PHONE3, "Phone3", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_PHONECODE, "PhoneCode", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_PHONESTATUS, "PhoneStatus", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_NFCCODE, "NFCCode", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_BLECODE, "BLECode", ItemChangeHandle},    
    {DA_INDEX_PERSONAL_ACCOUNT_PARENTID, "ParentID", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_ROOMNAME, "RoomNumber", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_TIMEZONE, "TimeZone", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_SPECIAL, "Special", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_CUSTOMIZEFORM, "CustomizeForm", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_USERINFO_UUID, "UserInfoUUID", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};

static void UpdateCommunityCallContact(int mng_id, int unit_id, const std::string& mac, const std::string& node, DataAnalysisContext &context)
{
    uint32_t change_type = WEB_COMM_UPDATE_COMMUNITY_CALLS;

    if (dbinterface::Account::GetCommunityContactSwitch(mng_id))
    {
        AK_LOG_INFO << local_table_name << " UpdateHandle WEB_COMM_UPDATE_COMMUNITY_CALLS. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << node
            << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
        UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, node);
        context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
    }
}

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string uid = data.GetIndex(DA_INDEX_PERSONAL_ACCOUNT_ACCOUNT);
    std::string mac;
    UserInfo user_info;
    memset(&user_info, 0, sizeof(user_info));
    if (dbhandle::DAInfo::GetUserInfoByUid(uid, user_info) != 0)
    {
        AK_LOG_INFO << local_table_name << " InsertHandle. User is null, uid=" << uid;
        return -1;
    }
    std::string node = user_info.node;
    uint32_t mng_id = user_info.mng_id;
    uint32_t unit_id = data.GetIndexAsInt(DA_INDEX_PERSONAL_ACCOUNT_UNITID);
    uint32_t project_type = data.GetProjectType();

    //更新数据版本
    dbinterface::ProjectUserManage::UpdateAccountDataVersionByUid(uid);

    //社区主从账号在FileUpdateControl处理逻辑一致,选取主账号的change_type即可
    uint32_t change_type = WEB_COMM_ADD_USER;
    uint32_t office_change_type = WEB_OFFICE_ADD_USER;
    uint32_t per_change_type = WEB_PER_ADD_USER;

    if (user_info.role == ACCOUNT_ROLE_COMMUNITY_PM)
    {
        uint32_t pm_change_type = WEB_COMM_ADD_PM_APP_ACCOUNT;
        //pm账户
        AK_LOG_INFO << local_table_name << " InsertHandle. pm community change type=" << pm_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(pm_change_type);
        UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(pm_change_type, mng_id, unit_id, mac, uid);
        context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
    }
    else
    {
        if (project_type == project::OFFICE)
        {   
            //办公
            AK_LOG_INFO << local_table_name << " InsertHandle. office change type=" << office_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(office_change_type) << " node= " << uid
                    << " office_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
            UCOfficeFileUpdatePtr ptr = std::make_shared<UCOfficeFileUpdate>(office_change_type, mng_id, unit_id, mac, uid);
            context.AddUpdateConfigInfo(UPDATE_OFFICE_FILE_UPDATE, ptr);
        }
        else if (project_type == project::PERSONAL)
        {
            //单住户
            AK_LOG_INFO << local_table_name << " InsertHandle. personal change type=" << per_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(per_change_type) << " node= " << node
            << " mac= " << mac;
            UCPersonalFileUpdatePtr ptr = std::make_shared<UCPersonalFileUpdate>(per_change_type, mac, node);
            context.AddUpdateConfigInfo(UPDATE_PERSONAL_FILE_UPDATE, ptr);
        }
        else 
        {
            //社区
            AK_LOG_INFO << local_table_name << " InsertHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << node
                    << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
            UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, node);
            context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
        }
    }

    //家庭成员变更,户户通联系人刷新  
    if (user_info.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT)
    {
        UpdateCommunityCallContact(mng_id, unit_id, mac, node, context);
    }
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //PersonalAccount删除没有办法处理到用户关联的权限组，因为关联都删除了
    //只能在AccountAccess中处理
    std::string uid = data.GetIndex(DA_INDEX_PERSONAL_ACCOUNT_ACCOUNT);
    std::string mac;
    std::string node;
    uint32_t mng_id = 0;
    uint32_t unit_id = data.GetIndexAsInt(DA_INDEX_PERSONAL_ACCOUNT_UNITID);
    uint32_t project_type = data.GetProjectType();

    //删除后是找不到node
    uint32_t parent_id = data.GetIndexAsInt(DA_INDEX_PERSONAL_ACCOUNT_PARENTID);
    uint32_t role = data.GetIndexAsInt(DA_INDEX_PERSONAL_ACCOUNT_ROLE);
    AK_LOG_INFO << " DeleteHandle. personal account role =" << role << " account=" << uid;

    uint32_t change_type = WEB_COMM_DEL_USER;
    uint32_t per_change_type = WEB_PER_DEL_USER;
    uint32_t office_change_type = WEB_OFFICE_DEL_USER;

    if (role == ACCOUNT_ROLE_COMMUNITY_MAIN || role == ACCOUNT_ROLE_PERSONNAL_MAIN
        || IsOfficeRole(role) || role == ACCOUNT_ROLE_COMMUNITY_PM)
    {
        mng_id = parent_id;
        node = uid;
    }
    else if (role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT)
    {
        //根据unit_id和CommunityUnit表找出mng_id
        CommunityUnitInfo unit_info;
        if (0 == dbinterface::CommunityUnit::GetCommunityUnitByID(unit_id, unit_info))
        {
            mng_id = unit_info.mng_id;
        }

        //根据parent_id查找node
        ResidentPerAccount account;
        if (0 == dbinterface::ResidentPersonalAccount::GetAccountByID(parent_id, account))
        {
            node = account.account;
        }

        change_type = WEB_COMM_DEL_SLAVE_USER;
    }
    else if (role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT)
    {
        //根据parent_id查找node
        ResidentPerAccount account;
        if (0 == dbinterface::ResidentPersonalAccount::GetAccountByID(parent_id, account))
        {
            node = account.account;
            mng_id = account.parent_id;
        }
    }
    
    if (node.size() == 0 || mng_id == 0)
    {
        AK_LOG_INFO << "DeleteHandle. personal account node = " << node << ", mng_id = " << mng_id;
        return -1;
    }

    if (role == ACCOUNT_ROLE_COMMUNITY_PM)
    {
        uint32_t pm_change_type = WEB_COMM_DEL_PM_APP_ACCOUNT;
        //pm账户
        AK_LOG_INFO << local_table_name << " DeleteHandle. pm community change type=" << pm_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(pm_change_type);
        UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(pm_change_type, mng_id, unit_id, mac, uid);
        context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
    }
    else
    {
        if (project_type == project::OFFICE)
        {   
            //办公
            AK_LOG_INFO << local_table_name << " DeleteHandle. office change type=" << office_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(office_change_type) << " node= " << uid
                    << " office_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
            UCOfficeFileUpdatePtr ptr = std::make_shared<UCOfficeFileUpdate>(office_change_type, mng_id, unit_id, mac, uid);
            context.AddUpdateConfigInfo(UPDATE_OFFICE_FILE_UPDATE, ptr);
        }
        else if (project_type == project::PERSONAL)
        {
            //单住户
            AK_LOG_INFO << local_table_name << " DeleteHandle. personal change type=" << per_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(per_change_type) << " node= " << node << " mac= " << mac;
            UCPersonalFileUpdatePtr ptr = std::make_shared<UCPersonalFileUpdate>(per_change_type, mac, node);
            context.AddUpdateConfigInfo(UPDATE_PERSONAL_FILE_UPDATE, ptr);
        }
        else 
        {
            //社区
            AK_LOG_INFO << local_table_name << " DeleteHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << node
                    << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
            UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, node);
            context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
        }
    }
    
    //家庭成员变更,户户通联系人刷新  
    if (role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT)
    {
        UpdateCommunityCallContact(mng_id, unit_id, mac, node, context);
    }
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    uint32_t role = data.GetIndexAsInt(DA_INDEX_PERSONAL_ACCOUNT_ROLE);
    uint32_t special = data.GetIndexAsInt(DA_INDEX_PERSONAL_ACCOUNT_SPECIAL);

    //timezone和customizeform字段更新只需对单住户刷新配置
    if (data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_NAME) 
        || data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_ENABLEIPDIRECT)
        || data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_PHONE)
        || data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_PHONE2)
        || data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_PHONE3)
        || data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_PHONESTATUS)
        || data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_PHONECODE)
        || data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_NFCCODE)
        || data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_BLECODE)
        || data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_UNITID)
        || data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_ROOMNAME)
        || data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_USERINFO_UUID)
        || ((data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_TIMEZONE) || data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_CUSTOMIZEFORM))
            && (role == ACCOUNT_ROLE_PERSONNAL_MAIN || role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT)))
    {
        std::string uid = data.GetIndex(DA_INDEX_PERSONAL_ACCOUNT_ACCOUNT);
        std::string mac;
        UserInfo user_info;
        memset(&user_info, 0, sizeof(user_info));
        if (dbhandle::DAInfo::GetUserInfoByUid(uid, user_info) != 0)
        {
            AK_LOG_INFO << local_table_name << " UpdateHandle. User is null, uid=" << uid;
            return -1;
        }
        std::string node = user_info.node;
        uint32_t mng_id = user_info.mng_id;
        uint32_t unit_id = data.GetIndexAsInt(DA_INDEX_PERSONAL_ACCOUNT_UNITID);
        uint32_t project_type = data.GetProjectType();

        //根据角色确定project_type，避免多套房场景下有跨项目的数据更新
        if(role)
        {
            project_type = DataAnalysisChangeRoleToProjectType(role);
        }

        //更新数据版本
        dbinterface::ProjectUserManage::UpdateAccountDataVersionByUid(uid);

        if (user_info.role == ACCOUNT_ROLE_COMMUNITY_PM)
        {
            uint32_t pm_change_type = WEB_COMM_MODIFY_PM_APP_ACCOUNT;
            //pm账户
            AK_LOG_INFO << local_table_name << " UpdateHandle. pm community change type=" << pm_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(pm_change_type);
            UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(pm_change_type, mng_id, unit_id, mac, uid);
            context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
        }
        else
        {
            uint32_t change_type = WEB_COMM_MODIFY_USER;
            uint32_t per_change_type = WEB_PER_MODIFY_USER;
            uint32_t office_change_type = WEB_OFFICE_MODIFY_USER;

            if (special)
            {
                //社区主账户和办公会在删除时更新PersoanlAccount表，special=1表示房间中没有用户
                change_type = WEB_COMM_DEL_USER;
                office_change_type = WEB_OFFICE_DEL_USER;
            }

            if (project_type == project::OFFICE)
            {   
                //办公
                AK_LOG_INFO << local_table_name << " UpdateHandle. office change type=" << office_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(office_change_type) << " node= " << uid
                    << " office_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
                UCOfficeFileUpdatePtr ptr = std::make_shared<UCOfficeFileUpdate>(office_change_type, mng_id, unit_id, mac, uid);
                context.AddUpdateConfigInfo(UPDATE_OFFICE_FILE_UPDATE, ptr);
            }
            else if (project_type == project::PERSONAL)
            {
                //单住户
                AK_LOG_INFO << local_table_name << " UpdateHandle. personal change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << node
                << " mac= " << mac;
                UCPersonalFileUpdatePtr ptr = std::make_shared<UCPersonalFileUpdate>(per_change_type, mac, node);
                context.AddUpdateConfigInfo(UPDATE_PERSONAL_FILE_UPDATE, ptr);
            }
            else 
            {
                //社区
                AK_LOG_INFO << local_table_name << " UpdateHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << node
                    << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
                UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, node);
                context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
            }
        }

        
        //名称修改时 户户通联系人刷新    
        if (project_type == project::RESIDENCE 
            && (data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_NAME) 
            || data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_ROOMNAME)))
        {
            UpdateCommunityCallContact(mng_id, unit_id, mac, node, context);
        }
    }
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaPersonalAccountHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    /*
    int index;
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    DataAnalysisChangeHandle temp_handle;

    //根据column_index对da_change_handle进行排序
    for (int i = 1; i < len; i++)
    {
        for (int j = i-1; j >= 0; j--)
        {
            if (da_change_handle[j+1].column_index < da_change_handle[j].column_index)
            {
                temp_handle = da_change_handle[j];
                da_change_handle[j] = da_change_handle[j+1];
                da_change_handle[j+1] = temp_handle;
            }
            else
                break;
        }
    }

    //取检测的最大枚举值，对小于它的每一个枚举值插入对应的列名，没有检测的插入空字符串
    for (index = 0; index <= da_change_handle[len-4].column_index; index++)
    {
        if (strlen(da_change_handle[index].column_name) > 0)
        {
            local_detect_key.push_back(da_change_handle[index].column_name);
        }
        else
        {
            local_detect_key.push_back("");
        }
    }
    */
    
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);

}






