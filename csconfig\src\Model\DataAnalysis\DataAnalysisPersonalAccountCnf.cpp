#include "DataAnalysisPersonalAccountCnf.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigOfficeFileUpdate.h"
#include "UpdateConfigCommFileUpdate.h"
#include "UpdateConfigPersonalFileUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "PersonalAccount.h"
#include "dbinterface/ProjectUserManage.h"
#include "DataAnalysisdbHandle.h"
#include "dbinterface/PersonalAccountCnf.h"


static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "PersonalAccountCnf";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
    {DA_INDEX_PERSONAL_ACCOUNT_CNF_ACCOUNT, "Account", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_CNF_ENABLEMOTION, "EnableMotion", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_CNF_MOTIONTIME, "MotionTime", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_CNF_ENABLEROBINCALL, "EnableRobinCall", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_CNF_ROBINCALLTIME, "RobinCallTime", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_CNF_ROBINCALLVAL, "RobinCallVal", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_CNF_CALLTYPE, "CallType", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_CNF_WEBRELAY, "WebRelay", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_CNF_FLAGS, "Flags", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};

static bool CheckIsFaceRegisterChange(DataAnalysisTableParse &data)
{
    int flags_after = data.GetIndexAsInt(DA_INDEX_PERSONAL_ACCOUNT_CNF_FLAGS);
    int flags_befor = data.GetBeforeIndexAsInt(DA_INDEX_PERSONAL_ACCOUNT_CNF_FLAGS);

    if (SwitchHandle(flags_after, PersonalAccountCnfInfo::FLAGS_TYPE::ENABLE_REGISTER_FACE) != 
        SwitchHandle(flags_befor, PersonalAccountCnfInfo::FLAGS_TYPE::ENABLE_REGISTER_FACE))
    {
        return true;
    }
    return false;

}

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string uid = data.GetIndex(DA_INDEX_PERSONAL_ACCOUNT_CNF_ACCOUNT);
    std::string mac;
    UserInfo user_info;
    memset(&user_info, 0, sizeof(user_info));
    if (dbhandle::DAInfo::GetUserInfoByUid(uid, user_info) != 0)
    {
        AK_LOG_INFO << local_table_name << " InsertHandle. User is null, uid=" << uid;
        return -1;
    }
    uint32_t mng_id = user_info.mng_id;
    uint32_t unit_id = user_info.unit_id;
    uint32_t project_type = data.GetProjectType();

    dbinterface::ProjectUserManage::UpdateAccountDataVersionByUid(uid);

    if (user_info.role == ACCOUNT_ROLE_COMMUNITY_PM)
    {
        uint32_t pm_change_type = WEB_COMM_ADD_PM_APP_ACCOUNT;
        //pm账户
        AK_LOG_INFO << local_table_name << " InsertHandle. pm community change type=" << pm_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(pm_change_type);
        UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(pm_change_type, mng_id, unit_id, mac, uid);
        context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
    }
    else
    {
        //需要判断角色吗？
        uint32_t change_type = WEB_COMM_ADD_USER;
        uint32_t office_change_type = WEB_OFFICE_ADD_USER;
        uint32_t per_change_type = WEB_PER_ADD_USER;

        if (project_type == project::OFFICE)
        {   
            //办公
            AK_LOG_INFO << local_table_name << " InsertHandle. office change type=" << office_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(office_change_type) << " node= " << uid
                    << " office_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
            UCOfficeFileUpdatePtr ptr = std::make_shared<UCOfficeFileUpdate>(office_change_type, mng_id, unit_id, mac, uid);
            context.AddUpdateConfigInfo(UPDATE_OFFICE_FILE_UPDATE, ptr);
        }
        else if (project_type == project::PERSONAL)
        {
            //单住户
            AK_LOG_INFO << local_table_name << " InsertHandle. personal change type=" << per_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(per_change_type)<< " node= " << uid
            << " mac= " << mac;
            UCPersonalFileUpdatePtr ptr = std::make_shared<UCPersonalFileUpdate>(per_change_type, mac, uid);
            context.AddUpdateConfigInfo(UPDATE_PERSONAL_FILE_UPDATE, ptr);
        }
        else 
        {
            //社区
            AK_LOG_INFO << local_table_name << " InsertHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
                    << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
            UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, uid);
            context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
        }
    }
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
/* 不需要处理  在PersonalAccount里面统一处理
    std::string node = data.GetIndex(DA_INDEX_PERSONAL_ACCOUNT_CNF_ACCOUNT);
    std::string mac = "";
    UserInfoPtr userptr;
    dbhandle::DAInfo::GetUserInfoByUid(node, userptr);
    uint32_t mng_id = userptr->mng_id;
    uint32_t unit_id = userptr->unit_id;
    uint32_t project_type = data.GetProjectType();

    //需要判断角色吗？
    uint32_t change_type = WEB_COMM_DEL_USER;
    uint32_t office_change_type = WEB_OFFICE_DEL_USER;

    if (project_type == project::OFFICE)
    {   
        //办公
        AK_LOG_INFO << local_table_name << " DeleteHandle. office change type=" << office_change_type << " node= " << node
                << " office_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
        UCOfficeFileUpdatePtr ptr = std::make_shared<UCOfficeFileUpdate>(office_change_type, mng_id, unit_id, mac, node);
        if (context.AddOperateType(office_change_type))
        {
            context.AddUpdateConfigInfo(UPDATE_OFFICE_FILE_UPDATE, ptr);
        }
    }
    else 
    {
        //社区
        AK_LOG_INFO << local_table_name << " DeleteHandle. community change type=" << change_type << " node= " << node
                << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
        UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, node);
        if (context.AddOperateType(change_type))
        {
            context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
        }
    }
*/
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    if (data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_CNF_ENABLEMOTION) 
        || data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_CNF_MOTIONTIME)
        || data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_CNF_ENABLEROBINCALL)
        || data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_CNF_ROBINCALLTIME)
        || data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_CNF_ROBINCALLVAL)
        || data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_CNF_CALLTYPE)
        || data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_CNF_WEBRELAY)
        || data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_CNF_FLAGS))
    {
        std::string uid = data.GetIndex(DA_INDEX_PERSONAL_ACCOUNT_CNF_ACCOUNT);
        std::string mac;
        UserInfo user_info;
        memset(&user_info, 0, sizeof(user_info));
        if (dbhandle::DAInfo::GetUserInfoByUid(uid, user_info) != 0)
        {
            AK_LOG_INFO << local_table_name << " UpdateHandle. User is null, uid=" << uid;
            return -1;
        }
        uint32_t mng_id = user_info.mng_id;
        uint32_t unit_id = user_info.unit_id;
        uint32_t project_type = data.GetProjectType();

        if (data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_CNF_WEBRELAY) || data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_CNF_FLAGS))
        {
            dbinterface::ProjectUserManage::UpdateDataVersionByNode(uid);
        }
        else
        {
            dbinterface::ProjectUserManage::UpdateAccountDataVersionByUid(uid);
        }

        if (user_info.role == ACCOUNT_ROLE_COMMUNITY_PM)
        {
            uint32_t pm_change_type = WEB_COMM_MODIFY_PM_APP_ACCOUNT;
            //pm账户
            AK_LOG_INFO << local_table_name << " UpdateHandle. pm community change type=" << pm_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(pm_change_type);
            UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(pm_change_type, mng_id, unit_id, mac, uid);
            context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
        }
        else
        {
            uint32_t change_type = WEB_COMM_MODIFY_USER;
            uint32_t office_change_type = WEB_OFFICE_MODIFY_USER;
            uint32_t per_change_type = WEB_PER_MODIFY_USER;

            if (project_type == project::OFFICE)
            {   
               //办公
               AK_LOG_INFO << local_table_name << " UpdateHandle. office change type=" << office_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(office_change_type) << " node= " << uid
                   << " office_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
               UCOfficeFileUpdatePtr ptr = std::make_shared<UCOfficeFileUpdate>(office_change_type, mng_id, unit_id, mac, uid);
               context.AddUpdateConfigInfo(UPDATE_OFFICE_FILE_UPDATE, ptr);
            }
            else if (project_type == project::PERSONAL)
            {
               //单住户
               AK_LOG_INFO << local_table_name << " UpdateHandle. personal change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid << " mac= " << mac;
               UCPersonalFileUpdatePtr ptr = std::make_shared<UCPersonalFileUpdate>(per_change_type, mac, uid);
               context.AddUpdateConfigInfo(UPDATE_PERSONAL_FILE_UPDATE, ptr);
            }
            else 
            {
                //社区 
                if (data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_CNF_FLAGS) && CheckIsFaceRegisterChange(data))
                {    
                    uint32_t change_type = WEB_COMM_UPDATE_NODE_USER;
                    AK_LOG_INFO << local_table_name << " UpdateHandle. pm change apt register face switch, change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " community_id= " << mng_id << " node=" << uid;

                    UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, uid);
                    context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
                }
                else
                {
                    AK_LOG_INFO << local_table_name << " UpdateHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
                       << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
                    UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, uid);
                    context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
                }
            }
        }
    }
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaPersonalAccountCnfHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);

}






