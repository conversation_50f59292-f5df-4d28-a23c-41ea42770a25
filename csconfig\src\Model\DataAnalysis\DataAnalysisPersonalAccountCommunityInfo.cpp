#include "DataAnalysisPersonalAccountCommunityInfo.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigOfficeFileUpdate.h"
#include "UpdateConfigCommFileUpdate.h"
#include "UpdateConfigPersonalFileUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "DataAnalysisdbHandle.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"


static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);


static DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "PersonalAccountCommunityInfo";
static DataAnalysisChangeHandle da_change_handle[] = {
    {DA_INDEX_PERSONAL_ACCOUNT_COMMUNITY_INFO_PERSONALACCOUNTUUID, "PersonalAccountUUID", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_COMMUNITY_INFO_ACCESSFLOOR, "AccessFloor", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};


static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;

}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    if (data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_COMMUNITY_INFO_ACCESSFLOOR))
    {
        std::string personal_account_uuid = data.GetIndex(DA_INDEX_PERSONAL_ACCOUNT_COMMUNITY_INFO_PERSONALACCOUNTUUID);
        UserInfo user_info;
        memset(&user_info, 0, sizeof(user_info));
        if(0 != dbhandle::DAInfo::GetUserInfoByUUID(personal_account_uuid, user_info))
        {
            return 0;
        }
        std::string mac;
        std::string uid = user_info.account;
        std::string node = user_info.node;
        uint32_t mng_id = user_info.mng_id;
        uint32_t unit_id = user_info.unit_id;
        uint32_t change_type = WEB_COMM_UPDATE_NODE_USER;

        //更新用户数据版本
        dbinterface::ProjectUserManage::UpdateAccountDataVersionByUid(uid);
        
        AK_LOG_INFO << local_table_name << " UpdateHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << node
            << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;

        UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, node);
        context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
    }
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaPersonalAccountCommunityInfoHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
   
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);
}
