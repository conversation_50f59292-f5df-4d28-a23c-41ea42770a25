#include "DataAnalysisPersonalAccountOfficeInfo.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigOfficeFileUpdate.h"
#include "UpdateConfigCommFileUpdate.h"
#include "UpdateConfigPersonalFileUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "DataAnalysisdbHandle.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"


static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);


static DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "PersonalAccountOfficeInfo";
static DataAnalysisChangeHandle da_change_handle[] = {
    {DA_INDEX_PERSONAL_ACCOUNT_OFFICE_INFO_FLOOR, "Floor", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_OFFICE_INFO_FLAGS, "Flags", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_OFFICE_INFO_PERSONALACCOUNTUUID, "PersonalAccountUUID", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};


static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //office 梯控
    if (data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_OFFICE_INFO_FLOOR))
    {
        std::string personal_account_uuid = data.GetIndex(DA_INDEX_PERSONAL_ACCOUNT_OFFICE_INFO_PERSONALACCOUNTUUID);
        ResidentPerAccount personal_account;
        memset(&personal_account, 0, sizeof(personal_account));
        if(0 != dbinterface::ResidentPersonalAccount::GetUUIDAccount(personal_account_uuid, personal_account))
        {
            AK_LOG_WARN << local_table_name << " UpdateHandle. GetUUIDAccount is null, personal_account_uuid=" << personal_account_uuid;
            return 0;
        }
        
        std::string mac;
        std::string uid = personal_account.account;
        uint32_t mng_id = personal_account.parent_id;
        uint32_t unit_id = personal_account.unit_id;
        uint32_t office_change_type = WEB_OFFICE_UPDATE_NODE_PUB_USER;

        //更新用户数据版本
        dbinterface::ProjectUserManage::UpdateAccountDataVersionByUid(uid);
        
        AK_LOG_INFO << local_table_name << " UpdateHandle. office change type=" << office_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(office_change_type)<< " node= " << uid
                << " office_id= " << mng_id << " unit id= " << unit_id;

        UCOfficeFileUpdatePtr ptr = std::make_shared<UCOfficeFileUpdate>(office_change_type, mng_id, unit_id, mac, uid);
        context.AddUpdateConfigInfo(UPDATE_OFFICE_FILE_UPDATE, ptr);
    }

    //是否开启smart intercom,刷联系人
    if (data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_OFFICE_INFO_FLAGS))
    {
        std::string personal_account_uuid = data.GetIndex(DA_INDEX_PERSONAL_ACCOUNT_OFFICE_INFO_PERSONALACCOUNTUUID);
        ResidentPerAccount personal_account;
        memset(&personal_account, 0, sizeof(personal_account));
        if(0 != dbinterface::ResidentPersonalAccount::GetUUIDAccount(personal_account_uuid, personal_account))
        {
            AK_LOG_WARN << local_table_name << " UpdateHandle. GetUUIDAccount is null, personal_account_uuid=" << personal_account_uuid;
            return 0;
        }
        std::string mac;
        std::string uid = personal_account.account;
        uint32_t mng_id = personal_account.parent_id;
        uint32_t unit_id = personal_account.unit_id;
        uint32_t office_change_type = WEB_OFFICE_UPDATE_NODE_CONTACT;
        
        AK_LOG_INFO << local_table_name << " UpdateHandle. office change type=" << office_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(office_change_type) << " node= " << uid
                << " office_id= " << mng_id << " unit id= " << unit_id;

        UCOfficeFileUpdatePtr ptr = std::make_shared<UCOfficeFileUpdate>(office_change_type, mng_id, unit_id, mac, uid);
        context.AddUpdateConfigInfo(UPDATE_OFFICE_FILE_UPDATE, ptr);
    }
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaPersonalAccountOfficeInfoHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
   
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);
}
