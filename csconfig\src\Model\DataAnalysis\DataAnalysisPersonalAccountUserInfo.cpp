#include "DataAnalysisPersonalAccountUserInfo.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigOfficeFileUpdate.h"
#include "UpdateConfigCommFileUpdate.h"
#include "UpdateConfigPersonalFileUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "PersonalAccount.h"
#include "dbinterface/ProjectUserManage.h"
#include "DataAnalysisdbHandle.h"
#include "dbinterface/Account.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "IPCControl.h"




static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "PersonalAccountUserInfo";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
    {DA_INDEX_PERSONAL_ACCOUNT_USER_EMAIL, "Email", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_USER_MOBILENUMBER, "MobileNumber", ItemChangeHandle},    
    {DA_INDEX_PERSONAL_ACCOUNT_USER_UUID, "UUID", ItemChangeHandle},
    {DA_INDEX_PERSONAL_ACCOUNT_USER_MAINACCOUNT, "AppMainUserAccount", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};

static void UpdateCommunityCallContact(int mng_id, int unit_id, const std::string& mac, const std::string& node, DataAnalysisContext &context)
{
    uint32_t change_type = WEB_COMM_UPDATE_COMMUNITY_CALLS;

    if (dbinterface::Account::GetCommunityContactSwitch(mng_id))
    {
        AK_LOG_INFO << local_table_name << " UpdateHandle WEB_COMM_UPDATE_COMMUNITY_CALLS. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << node
            << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
        UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, node);
        context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
    }
}

static bool NeedUpdateContact(const std::string& before_email, const std::string& before_mobile_number, const std::string& email, const std::string& mobile_number)
{
    // Email && MobileNumber, 从无到有 || 从有到无 的情况下需要刷新node的联系人文件
    if ((before_email.empty() && before_mobile_number.empty() && (!email.empty() || !mobile_number.empty()))
    || ((!before_email.empty() || !before_mobile_number.empty()) && (email.empty() && mobile_number.empty())))
    {
        return true;
    }
    
    return false;
}

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //PersonalAccount处理
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //PersonalAccount处理
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    if (data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_USER_EMAIL) || data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_USER_MOBILENUMBER))
    {
        std::string email = data.GetIndex(DA_INDEX_PERSONAL_ACCOUNT_USER_EMAIL);
        std::string mobile_number = data.GetIndex(DA_INDEX_PERSONAL_ACCOUNT_USER_MOBILENUMBER);
        std::string before_email = data.GetBeforeIndex(DA_INDEX_PERSONAL_ACCOUNT_USER_EMAIL);
        std::string before_mobile_number = data.GetBeforeIndex(DA_INDEX_PERSONAL_ACCOUNT_USER_MOBILENUMBER);
        
        if (NeedUpdateContact(before_email, before_mobile_number, email, mobile_number))
        {
            std::string userinfo_uuid = data.GetIndex(DA_INDEX_PERSONAL_ACCOUNT_USER_UUID);
            uint32_t project_type = data.GetProjectType();
            uint32_t change_type = WEB_COMM_MODIFY_USER;
            uint32_t per_change_type = WEB_PER_MODIFY_USER;
            uint32_t office_change_type = WEB_OFFICE_MODIFY_USER;

            //获取用户列表
            ResidentPerAccountList account_list;
            if (0 != dbinterface::ResidentPersonalAccount::GetAccountListByUserInfoUUID(userinfo_uuid, account_list))
            {
                AK_LOG_INFO << "GetAccountListByUserInfoUUID failed, userinfo_uuid:" << userinfo_uuid;
                return 0;
            }

            //如果PersonalAccount数据已被删除则在PersonalAccount处理
            for (const auto &account : account_list)
            {
                std::string mac;
                uint32_t role = account.role;
                std::string uid = account.account;
                uint32_t unit_id = account.unit_id;
                std::string node = account.account;
                uint32_t mng_id = account.parent_id;

                if (role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT || role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT)
                {
                    ResidentPerAccount master_info;
                    if (0 == dbinterface::ResidentPersonalAccount::GetAccountByID(account.parent_id, master_info))
                    {
                        node = master_info.account;
                        mng_id = master_info.parent_id;
                    }
                    else
                    {
                        continue;
                    }
                }

                //根据角色确定project_type，避免多套房场景下有跨项目的数据更新
                if(role)
                {
                    project_type = DataAnalysisChangeRoleToProjectType(role);
                }
                
                if (role == ACCOUNT_ROLE_COMMUNITY_PM)
                {
                    uint32_t pm_change_type = WEB_COMM_MODIFY_PM_APP_ACCOUNT;
                    AK_LOG_INFO << local_table_name << " UpdateHandle. pm community change type=" << pm_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(pm_change_type);
                    UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(pm_change_type, mng_id, unit_id, mac, uid);
                    context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
                }
                else
                {
                    if (project_type == project::OFFICE)
                    {   
                        //办公
                        AK_LOG_INFO << local_table_name << " UpdateHandle. office change type=" << office_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(office_change_type) << " node= " << uid
                                << " office_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
                        UCOfficeFileUpdatePtr ptr = std::make_shared<UCOfficeFileUpdate>(office_change_type, mng_id, unit_id, mac, uid);
                        context.AddUpdateConfigInfo(UPDATE_OFFICE_FILE_UPDATE, ptr);
                    }
                    else if (project_type == project::PERSONAL)
                    {
                        //单住户
                        AK_LOG_INFO << local_table_name << " UpdateHandle. personal change type=" << per_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(per_change_type) << " node= " << node << " mac= " << mac;
                        UCPersonalFileUpdatePtr ptr = std::make_shared<UCPersonalFileUpdate>(per_change_type, mac, node);
                        context.AddUpdateConfigInfo(UPDATE_PERSONAL_FILE_UPDATE, ptr);
                    }
                    else 
                    {
                        //社区
                        AK_LOG_INFO << local_table_name << " UpdateHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type)<< " node= " << node
                                << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
                        UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, node);
                        context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
                    }
                }
            }
        }
    }

    //主站点变更通知csmain更新缓存
    if (data.IsIndexChange(DA_INDEX_PERSONAL_ACCOUNT_USER_MAINACCOUNT))
    {
        std::string main_account = data.GetIndex(DA_INDEX_PERSONAL_ACCOUNT_USER_MAINACCOUNT);
        std::string before_main_account = data.GetBeforeIndex(DA_INDEX_PERSONAL_ACCOUNT_USER_MAINACCOUNT);
        if (!main_account.length() && !before_main_account.length())
        {
            GetIPCControlInstance()->NotifyChangeMainSite(main_account, before_main_account);
            AK_LOG_INFO << "NotifyChangeMainSite before_main_account:" << before_main_account << " main_account:" << main_account;
        }
    }
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaPersonalAccountUserInfoHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);

}






