#include "DataAnalysisDevices.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigOfficeFileUpdate.h"
#include "UpdateConfigCommFileUpdate.h"
#include "UpdateConfigPersonalFileUpdate.h"
#include "UpdateConfigOfficeDevUpdate.h"
#include "UpdateConfigCommDevUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"


static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int GetDevicesChangeType();


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "PersonalDevices";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
	{DA_INDEX_PERSONAL_DEVICES_NAME, "Location", ItemChangeHandle},
	{DA_INDEX_PERSONAL_DEVICES_MAC, "MAC", ItemChangeHandle},
	{DA_INDEX_PERSONAL_DEVICES_NODE, "Node", ItemChangeHandle},
	{DA_INDEX_PERSONAL_DEVICES_TYPE, "Type", ItemChangeHandle},
	{DA_INDEX_PERSONAL_DEVICES_NET_GROUP, "NetGroupNumber", ItemChangeHandle},
	{DA_INDEX_PERSONAL_DEVICES_CONFIG, "Config", ItemChangeHandle},
	{DA_INDEX_PERSONAL_DEVICES_STAIRSHOW, "StairShow", ItemChangeHandle},
	{DA_INDEX_PERSONAL_DEVICES_SECURITYRELAY, "SecurityRelay", ItemChangeHandle},
	{DA_INDEX_PERSONAL_DEVICES_FLAGS, "Flags", ItemChangeHandle},
	{DA_INDEX_INSERT, "", InsertHandle},
	{DA_INDEX_DELETE, "", DeleteHandle},
	{DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    uint32_t type = data.GetIndexAsInt(DA_INDEX_PERSONAL_DEVICES_TYPE);
    std::string uid = data.GetIndex(DA_INDEX_PERSONAL_DEVICES_NODE);
    std::string mac = data.GetIndex(DA_INDEX_PERSONAL_DEVICES_MAC);
    uint32_t change_type = WEB_PER_ADD_DEV;
    std::vector<std::string> macs;
    macs.push_back(mac);

    AK_LOG_INFO << local_table_name << " InsertHandle. personal change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
                << " mac= " << mac;
    UCPersonalFileUpdatePtr fileptr = std::make_shared<UCPersonalFileUpdate>(change_type, mac, uid);
    UCCommunityDevUpdatePtr devptr = std::make_shared<UCCommunityDevUpdate>(change_type, macs);
    context.AddUpdateConfigInfo(UPDATE_PERSONAL_FILE_UPDATE, fileptr);
    context.AddUpdateConfigInfo(UPDATE_COMM_DEV_UPDATE, devptr);
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    uint32_t type = data.GetIndexAsInt(DA_INDEX_PERSONAL_DEVICES_TYPE);
    std::string uid = data.GetIndex(DA_INDEX_PERSONAL_DEVICES_NODE);
    std::string mac = data.GetIndex(DA_INDEX_PERSONAL_DEVICES_MAC);
    uint32_t change_type = WEB_PER_DEL_DEV;
    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    // 设备还在,直接跳过后续操作
    if ((0 == dbinterface::ResidentDevices::GetMacDev(mac, dev)) || (0 == dbinterface::ResidentPerDevices::GetMacDev(mac, dev)))
    {
        AK_LOG_INFO << local_table_name << " DeleteHandle. Mac is not null, mac=" << mac;
        return 0;
    }
    std::vector<std::string> macs;
    macs.push_back(mac);

    AK_LOG_INFO << local_table_name << " DeleteHandle. personal change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
                   << " mac= " << mac;
   UCPersonalFileUpdatePtr fileptr = std::make_shared<UCPersonalFileUpdate>(change_type, mac, uid);
   UCCommunityDevUpdatePtr devptr = std::make_shared<UCCommunityDevUpdate>(change_type, macs);
   context.AddUpdateConfigInfo(UPDATE_PERSONAL_FILE_UPDATE, fileptr);
   context.AddUpdateConfigInfo(UPDATE_COMM_DEV_UPDATE, devptr);
   return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    uint32_t type = data.GetIndexAsInt(DA_INDEX_PERSONAL_DEVICES_TYPE);
    std::string uid = data.GetIndex(DA_INDEX_PERSONAL_DEVICES_NODE);
    std::string mac = data.GetIndex(DA_INDEX_PERSONAL_DEVICES_MAC);
    uint32_t change_type = WEB_PER_MODIFY_DEV;
    std::vector<std::string> macs;
    macs.push_back(mac);

    AK_LOG_INFO << local_table_name << " UpdateHandle. personal change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
                   << " mac= " << mac;
   UCPersonalFileUpdatePtr fileptr = std::make_shared<UCPersonalFileUpdate>(change_type, mac, uid);
   UCCommunityDevUpdatePtr devptr = std::make_shared<UCCommunityDevUpdate>(change_type, macs);
   context.AddUpdateConfigInfo(UPDATE_PERSONAL_FILE_UPDATE, fileptr);
   context.AddUpdateConfigInfo(UPDATE_COMM_DEV_UPDATE, devptr);
   return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaPerosnalDevicesHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);

}






