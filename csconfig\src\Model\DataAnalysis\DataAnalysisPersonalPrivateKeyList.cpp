#include "DataAnalysisPersonalPrivateKeyList.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigOfficeFileUpdate.h"
#include "UpdateConfigCommFileUpdate.h"
#include "UpdateConfigPersonalFileUpdate.h"
#include "UpdateConfigCommAccessUpdate.h"
#include "UpdateConfigCommDevUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "PersonalAccount.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/PersonalPrivateKey.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "DataAnalysisdbHandle.h"



static int ItemChangeHandle(DataAnalysisTableParse& data, DataAnalysisContext& context);
static int InsertHandle(DataAnalysisTableParse& data, DataAnalysisContext& context);
static int DeleteHandle(DataAnalysisTableParse& data, DataAnalysisContext& context);
static int UpdateHandle(DataAnalysisTableParse& data, DataAnalysisContext& context);


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "PersonalPrivateKeyList";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
    {DA_INDEX_PERSONAL_PIN_LIST_ID, "ID", ItemChangeHandle},
    {DA_INDEX_PERSONAL_PIN_LIST_MAC, "MAC", ItemChangeHandle},
    {DA_INDEX_PERSONAL_PIN_LIST_KEYID, "KeyID", ItemChangeHandle},
    {DA_INDEX_PERSONAL_PIN_LIST_RELAY, "Relay", ItemChangeHandle},
    {DA_INDEX_PERSONAL_PIN_LIST_SECURITYRELAY, "SecurityRelay", ItemChangeHandle},
    {DA_INDEX_INSERT, "", UpdateHandle},
    {DA_INDEX_DELETE, "", UpdateHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse& data, DataAnalysisContext& context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse& data, DataAnalysisContext& context)
{
    //插入在PersonalPrivateKey表的数据分析有处理过，所以无需处理
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse& data, DataAnalysisContext& context)
{
    //删除同理
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse& data, DataAnalysisContext& context)
{
    //只有relay字段发生变化时再刷新配置,只有旧社区的个人设备，单住户不用PersonalPrivateKeyList这张表
    std::string mac = data.GetIndex(DA_INDEX_PERSONAL_PIN_LIST_MAC);
    int key_id = data.GetIndexAsInt(DA_INDEX_PERSONAL_PIN_LIST_KEYID);
    PersonalPrivateKeyInfo key_info;
    if (0 == dbinterface::PersonalPrivateKey::GetPersonalPrivateKeyByID(key_id, key_info))
    {
        uint32_t project_type = data.GetProjectType();
        uint32_t com_change_type = WEB_COMM_UPDATE_PIN;
        uint32_t per_change_type = WEB_PER_UPDATE_PIN;
        uint32_t mng_id = key_info.mng_id;
        uint32_t unit_id = key_info.unit_id;
        std::string uid = key_info.node;
        std::vector<std::string> macs;
        macs.push_back(mac);

        if (project_type == project::PERSONAL)
        {
            //单住户
            AK_LOG_INFO << local_table_name << " UpdateHandle. personal change type=" << per_change_type 
                        << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(per_change_type) 
                        << " node= " << uid << " mac= " << mac;

            UCPersonalFileUpdatePtr fileptr = std::make_shared<UCPersonalFileUpdate>(per_change_type, mac, uid);
            context.AddUpdateConfigInfo(UPDATE_PERSONAL_FILE_UPDATE, fileptr);
        }
        else
        {
            AK_LOG_INFO << local_table_name << " UpdateHandle. community change type=" << com_change_type 
                        << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(com_change_type) 
                        << " node= " << uid << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
                        
            UCCommunityFileUpdatePtr fileptr = std::make_shared<UCCommunityFileUpdate>(com_change_type, mng_id, unit_id, mac, uid);
            context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, fileptr);
        }
    }
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaPersonalPrivateKeyListHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);

}






