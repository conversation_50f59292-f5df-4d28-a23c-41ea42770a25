#include "DataAnalysisPersonalRfcardKey.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigOfficeFileUpdate.h"
#include "UpdateConfigCommFileUpdate.h"
#include "UpdateConfigPersonalFileUpdate.h"
#include "UpdateConfigCommAccessUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "PersonalAccount.h"
#include "dbinterface/ProjectUserManage.h"
#include "DataAnalysisdbHandle.h"
#include "UpdateSmartLockConfig.h"
#include "dbinterface/SmartLock.h"


static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "PersonalRfcardKey";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
    {DA_INDEX_PER_RF_ID, "ID", ItemChangeHandle},
    {DA_INDEX_PER_RF_MNGACCOUNTID, "MngAccountID", ItemChangeHandle},
    {DA_INDEX_PER_RF_UNITID, "UnitID", ItemChangeHandle},    
    {DA_INDEX_PER_RF_GRADE, "Grade", ItemChangeHandle},
    {DA_INDEX_PER_RF_TYPE, "Type", ItemChangeHandle},
    {DA_INDEX_PER_RF_CODE, "Code", ItemChangeHandle},
    {DA_INDEX_PER_RF_NODE, "Node", ItemChangeHandle},
    {DA_INDEX_PER_RF_SCHEDULERTYPE, "SchedulerType", ItemChangeHandle},
    {DA_INDEX_PER_RF_DATEFLAG, "DateFlag", ItemChangeHandle},
    {DA_INDEX_PER_RF_BEGINTIME, "BeginTime", ItemChangeHandle},
    {DA_INDEX_PER_RF_ENDTIME, "EndTime", ItemChangeHandle},
    {DA_INDEX_PER_RF_STARTTIME, "StartTime", ItemChangeHandle},
    {DA_INDEX_PER_RF_STOPTIME, "StopTime", ItemChangeHandle},
    {DA_INDEX_PER_RF_ACCOUNTID, "AccountID", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string uid = data.GetIndex(DA_INDEX_PER_RF_NODE);
    std::string mac;
    uint32_t type = data.GetIndexAsInt(DA_INDEX_PER_RF_TYPE);
    uint32_t mng_id = data.GetIndexAsInt(DA_INDEX_PER_RF_MNGACCOUNTID);
    uint32_t unit_id = data.GetIndexAsInt(DA_INDEX_PER_RF_UNITID);
    uint32_t project_type = data.GetProjectType();

    uint32_t old_change_type = WEB_COMM_UPDATE_RF;
    uint32_t per_change_type = WEB_PER_UPDATE_RF;

    if (project_type == project::PERSONAL)
    {
        //单住户
        AK_LOG_INFO << local_table_name << " CommonHandle. personal change type=" << per_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(per_change_type) << " node= " << uid
        << " mac= " << mac;
        UCPersonalFileUpdatePtr ptr = std::make_shared<UCPersonalFileUpdate>(per_change_type, mac, uid);
        context.AddUpdateConfigInfo(UPDATE_PERSONAL_FILE_UPDATE, ptr);

        std::string node_uuid;
        dbinterface::ResidentPersonalAccount::GetUUIDByAccount(uid, node_uuid);

        dbinterface::SmartLock::UpdateNodeSmartLockStatusSynchronizing(node_uuid);

        uint32_t smartlock_change_type = SMARTLOCK_NODE_SL20_CONFIG_UPDATE;
        std::string lock_uuid;
        
        UCSmartLockConfigUpdatePtr smartlock_ptr = std::make_shared<UCSmartLockConfigUpdate>(smartlock_change_type, lock_uuid, uid, project::PERSONAL, mng_id);
        context.AddUpdateConfigInfo(UPDATE_SMARTLOCK_CONFIG, smartlock_ptr);
    }
    else 
    {
        //旧社区
        AK_LOG_INFO << local_table_name << " CommonHandle. community change type=" << old_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(old_change_type) << " node= " << uid
            << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
        UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(old_change_type, mng_id, unit_id, mac, uid);
        context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
    }
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    CommonChangeHandle(data, context);
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    CommonChangeHandle(data, context);
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    if (data.IsIndexChange(DA_INDEX_PER_RF_CODE) 
        || data.IsIndexChange(DA_INDEX_PER_RF_DATEFLAG)
        || data.IsIndexChange(DA_INDEX_PER_RF_BEGINTIME)
        || data.IsIndexChange(DA_INDEX_PER_RF_ENDTIME)
        || data.IsIndexChange(DA_INDEX_PER_RF_STARTTIME)
        || data.IsIndexChange(DA_INDEX_PER_RF_STOPTIME)
        || data.IsIndexChange(DA_INDEX_PER_RF_ACCOUNTID))
    {
        CommonChangeHandle(data, context);
    }
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaPersonalRfcardKeyHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);

}






