#include "DataAnalysisPersonalThirdPartCamera.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigOfficeFileUpdate.h"
#include "UpdateConfigCommFileUpdate.h"
#include "UpdateConfigPersonalFileUpdate.h"
#include "UpdateConfigOfficeDevUpdate.h"
#include "UpdateConfigCommDevUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "DataAnalysisdbHandle.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"


static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int GetDevicesChangeType();


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "PersonalThirdPartCamera";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
	{DA_INDEX_PER_THIRDPART_CAMERA_ID, "ID", ItemChangeHandle},
	{DA_INDEX_PER_THIRDPART_CAMERA_UUID, "UUID", ItemChangeHandle},
	{DA_INDEX_PER_THIRDPART_CAMERA_PROJECTUUID, "ProjectUUID", ItemChangeHandle},
	{DA_INDEX_PER_THIRDPART_CAMERA_PERSONALACCOUNTUUID, "PersonalAccountUUID", ItemChangeHandle},
	{DA_INDEX_PER_THIRDPART_CAMERA_LOCATION, "Location", ItemChangeHandle},
	{DA_INDEX_PER_THIRDPART_CAMERA_RTSPADDRESS, "RtspAddress", ItemChangeHandle},
	{DA_INDEX_PER_THIRDPART_CAMERA_RTSPPORT, "RtspPort", ItemChangeHandle},
	{DA_INDEX_PER_THIRDPART_CAMERA_RTSPUSERNAME, "RtspUserName", ItemChangeHandle},
	{DA_INDEX_PER_THIRDPART_CAMERA_RTSPPWD, "RtspPwd", ItemChangeHandle},
	{DA_INDEX_PER_THIRDPART_CAMERA_SWITCH, "Switch", ItemChangeHandle},
	{DA_INDEX_PER_THIRDPART_CAMERA_MAC, "MAC", ItemChangeHandle},
	{DA_INDEX_INSERT, "", InsertHandle},
	{DA_INDEX_DELETE, "", DeleteHandle},
	{DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string personal_uuid = data.GetIndex(DA_INDEX_PER_THIRDPART_CAMERA_PERSONALACCOUNTUUID);
    ResidentPerAccount per_account;
    memset(&per_account, 0, sizeof(per_account));
    if (0 != dbinterface::ResidentPersonalAccount::GetUUIDAccount(personal_uuid, per_account))
    {
        AK_LOG_WARN << local_table_name << " InsertHandle. GetUUIDAccount is null, personal_uuid=" << personal_uuid;    
        return -1;
    }
    std::string uid = per_account.account;
    std::string mac = data.GetIndex(DA_INDEX_PER_THIRDPART_CAMERA_MAC);
    uint32_t change_type = WEB_PER_ADD_DEV;

    AK_LOG_INFO << local_table_name << " InsertHandle. personal change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
            << " mac= " << mac;
    UCPersonalFileUpdatePtr fileptr = std::make_shared<UCPersonalFileUpdate>(change_type, mac, uid);
    context.AddUpdateConfigInfo(UPDATE_PERSONAL_FILE_UPDATE, fileptr);
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string personal_uuid = data.GetIndex(DA_INDEX_PER_THIRDPART_CAMERA_PERSONALACCOUNTUUID);
    ResidentPerAccount per_account;
    memset(&per_account, 0, sizeof(per_account));
    if (0 != dbinterface::ResidentPersonalAccount::GetUUIDAccount(personal_uuid, per_account))
    {
        AK_LOG_WARN << local_table_name << " DeleteHandle. GetUUIDAccount is null, personal_uuid=" << personal_uuid;    
        return -1;
    }
    std::string uid = per_account.account;
    std::string mac = data.GetIndex(DA_INDEX_PER_THIRDPART_CAMERA_MAC);
    uint32_t change_type = WEB_PER_DEL_THIRD_CAMERA;

    AK_LOG_INFO << local_table_name << " DeleteHandle. personal change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
               << " mac= " << mac;
    UCPersonalFileUpdatePtr fileptr = std::make_shared<UCPersonalFileUpdate>(change_type, mac, uid);
    context.AddUpdateConfigInfo(UPDATE_PERSONAL_FILE_UPDATE, fileptr);
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string personal_uuid = data.GetIndex(DA_INDEX_PER_THIRDPART_CAMERA_PERSONALACCOUNTUUID);
    ResidentPerAccount per_account;
    memset(&per_account, 0, sizeof(per_account));
    if (0 != dbinterface::ResidentPersonalAccount::GetUUIDAccount(personal_uuid, per_account))
    {
        AK_LOG_WARN << local_table_name << " UpdateHandle. GetUUIDAccount is null, personal_uuid=" << personal_uuid;
        return -1;
    }
    std::string uid = per_account.account;
    std::string mac = data.GetIndex(DA_INDEX_PER_THIRDPART_CAMERA_MAC);
    uint32_t change_type = WEB_PER_MODIFY_DEV;

    AK_LOG_INFO << local_table_name << " UpdateHandle. personal change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
               << " mac= " << mac;
    UCPersonalFileUpdatePtr fileptr = std::make_shared<UCPersonalFileUpdate>(change_type, mac, uid);
    context.AddUpdateConfigInfo(UPDATE_PERSONAL_FILE_UPDATE, fileptr);
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaPerosnalThirdPartCameraHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);

}






