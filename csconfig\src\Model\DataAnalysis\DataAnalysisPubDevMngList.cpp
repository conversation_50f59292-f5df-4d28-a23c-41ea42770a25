#include "DataAnalysisPubDevMngList.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigOfficeFileUpdate.h"
#include "UpdateConfigCommFileUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "CommConfigHandle.h"
#include "BasicDefine.h"
#include "util_cstring.h"
#include "ConfigDef.h" 
#include "util.h"
#include "DeviceSetting.h"
#include "dbinterface/ProjectUserManage.h"
#include "DataAnalysisdbHandle.h"




static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int GetDevicesChangeType();


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "PubDevMngList";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
	{DA_INDEX_PUBDEVMNGLIST_DEVICESID, "DevicesID", ItemChangeHandle},
	{DA_INDEX_PUBDEVMNGLIST_UNITID, "UnitID", ItemChangeHandle},
	{DA_INDEX_INSERT, "", UpdateHandle},
	{DA_INDEX_DELETE, "", UpdateHandle},
	{DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //最外围设备的属性之一，该表的crud代表最外围设备的更新
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //最外围设备的属性之一，该表的crud代表最外围设备的更新
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    uint32_t unit_id = data.GetIndexAsInt(DA_INDEX_PUBDEVMNGLIST_UNITID);
    uint32_t device_id = data.GetIndexAsInt(DA_INDEX_PUBDEVMNGLIST_DEVICESID);

    ResidentDev dev;
    if (dbinterface::ResidentDevices::GetDevByID(device_id, dev) != 0)
    {
        AK_LOG_WARN << local_table_name << " UpdateHandle. GetDevByID is null, device_id=" << device_id;
        return -1;
    }
    
    uint32_t mng_id = dev.project_mng_id;
    //最外围设备没有node
    std::string uid;
    uint32_t project_type = data.GetProjectType();
    uint32_t change_type = 0;
    uint32_t offic_change_type = 0;

    change_type = WEB_COMM_PUB_MODIFY_DEV;
    offic_change_type = WEB_OFFICE_PUB_MODIFY_DEV;

    if (project_type == project::OFFICE)
    {   
        AK_LOG_INFO << local_table_name << " UpdateHandle. office change type=" << offic_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(offic_change_type) << " node= " << uid
                << " office_id= " << mng_id << " unit id= " << unit_id << " mac= " << dev.mac;
        UCOfficeFileUpdatePtr ptr = std::make_shared<UCOfficeFileUpdate>(offic_change_type, mng_id, unit_id, dev.mac, uid);
        context.AddUpdateConfigInfo(UPDATE_OFFICE_FILE_UPDATE, ptr);
    }
    else 
    {
        AK_LOG_INFO << local_table_name << " UpdateHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
                << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << dev.mac;
        UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, dev.mac, uid);
        context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
    }
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaPubDevMngListHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);

}






