#include "DataAnalysisPubPrivateKey.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigOfficeFileUpdate.h"
#include "UpdateConfigCommFileUpdate.h"
#include "UpdateConfigPersonalFileUpdate.h"
#include "UpdateConfigCommAccessUpdate.h"
#include "UpdateConfigOfficeAccessUpdate.h"
#include "UpdateConfigCommDevUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "PersonalAccount.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/PubPrivateKeyList.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "DataAnalysisdbHandle.h"



static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "PubPrivateKey";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
    {DA_INDEX_PUB_PIN_ID, "ID", ItemChangeHandle},
    {DA_INDEX_PUB_PIN_MNGACCOUNTID, "MngAccountID", ItemChangeHandle},    
    {DA_INDEX_PUB_PIN_WORKID, "WorkID", ItemChangeHandle},
    {DA_INDEX_PUB_PIN_CODE, "Code", ItemChangeHandle},
    {DA_INDEX_PUB_PIN_CREATETIME, "CreateTime", ItemChangeHandle},
    {DA_INDEX_PUB_PIN_OWNERTYPE, "OwnerType", ItemChangeHandle},
    {DA_INDEX_PUB_PIN_NAME, "Name", ItemChangeHandle},
    {DA_INDEX_PUB_PIN_SCHEDULERTYPE, "SchedulerType", ItemChangeHandle},
    {DA_INDEX_PUB_PIN_DATEFLAG, "DateFlag", ItemChangeHandle},
    {DA_INDEX_PUB_PIN_BEGINTIME, "BeginTime", ItemChangeHandle},
    {DA_INDEX_PUB_PIN_ENDTIME, "EndTime", ItemChangeHandle},
    {DA_INDEX_PUB_PIN_STARTTIME, "StartTime", ItemChangeHandle},
    {DA_INDEX_PUB_PIN_STOPTIME, "StopTime", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    uint32_t mng_id = data.GetIndexAsInt(DA_INDEX_PUB_PIN_MNGACCOUNTID);
    uint32_t code_id = data.GetIndexAsInt(DA_INDEX_PUB_PIN_ID);
    std::vector<std::string> macs;
    dbinterface::PubPrivateKeyList::GetMacByCodeID(code_id, macs);
    for (const auto& mac : macs)
    {
        ResidentDev dev;
        memset(&dev, 0, sizeof(dev));
        uint32_t change_type = 0;
        if (0 == dbinterface::ResidentDevices::GetMacDev(mac, dev))
        {
            uint32_t unit_id = dev.unit_id;
            uint32_t grade = dev.grade;
            std::string uid = dev.node;

            if (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
            {
                change_type = WEB_COMM_PUB_UPDATE_PIN;
            }
            else if (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
            {
                change_type = WEB_COMM_UNIT_UPDATE_PIN;
            }

            AK_LOG_INFO << local_table_name << " CommonHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
                    << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
            UCCommunityFileUpdatePtr fileptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, uid);
            context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, fileptr);
        }
    }
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //旧社区创建公共人员pin，同时插入PubPrivateKey和PubPrivateKeyList,在PubPrivateKeyList做数据分析
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //旧社区创建公共人员pin，同时删除PubPrivateKey和PubPrivateKeyList,在PubPrivateKeyList做数据分析
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //relay字段发生变化时再刷新配置,只有旧社区
    if (data.IsIndexChange(DA_INDEX_PUB_PIN_CODE) || 
        data.IsIndexChange(DA_INDEX_PUB_PIN_NAME) || 
        data.IsIndexChange(DA_INDEX_PUB_PIN_SCHEDULERTYPE) || 
        data.IsIndexChange(DA_INDEX_PUB_PIN_DATEFLAG) || 
        data.IsIndexChange(DA_INDEX_PUB_PIN_BEGINTIME) || 
        data.IsIndexChange(DA_INDEX_PUB_PIN_ENDTIME) || 
        data.IsIndexChange(DA_INDEX_PUB_PIN_STARTTIME) || 
        data.IsIndexChange(DA_INDEX_PUB_PIN_STOPTIME))
    {
        CommonChangeHandle(data, context);
    }
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaPubPrivateKeyHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);

}






