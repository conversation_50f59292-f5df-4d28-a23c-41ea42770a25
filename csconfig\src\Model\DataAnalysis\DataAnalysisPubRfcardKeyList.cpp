#include "DataAnalysisPubRfcardKeyList.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigOfficeFileUpdate.h"
#include "UpdateConfigCommFileUpdate.h"
#include "UpdateConfigPersonalFileUpdate.h"
#include "UpdateConfigCommAccessUpdate.h"
#include "UpdateConfigOfficeAccessUpdate.h"
#include "UpdateConfigCommDevUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "PersonalAccount.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "DataAnalysisdbHandle.h"



static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "PubRfcardKeyList";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
    {DA_INDEX_PUB_RF_LIST_ID, "ID", ItemChangeHandle},
    {DA_INDEX_PUB_RF_LIST_MAC, "MAC", ItemChangeHandle},    
    {DA_INDEX_PUB_RF_LIST_KEYID, "KeyID", ItemChangeHandle},
    {DA_INDEX_PUB_RF_LIST_RELAY, "Relay", ItemChangeHandle},
    {DA_INDEX_PUB_RF_LIST_SECURITYRELAY, "SecurityRelay", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string mac = data.GetIndex(DA_INDEX_PUB_RF_LIST_MAC);
    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    if (0 == dbinterface::ResidentDevices::GetMacDev(mac, dev))
    {
        uint32_t change_type = 0;
        uint32_t mng_id = dev.project_mng_id;
        uint32_t unit_id = dev.unit_id;
        uint32_t grade = dev.grade;
        std::string uid = dev.node;

        if (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
        {
            change_type = WEB_COMM_PUB_UPDATE_RF;
        }
        else if (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
        {
            change_type = WEB_COMM_UNIT_UPDATE_RF;
        }

        AK_LOG_INFO << local_table_name << " UpdateHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
                << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
        UCCommunityFileUpdatePtr fileptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, uid);
        context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, fileptr);
    }
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    CommonChangeHandle(data, context);
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    CommonChangeHandle(data, context);
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //relay字段发生变化时再刷新配置,只有旧社区
    if (data.IsIndexChange(DA_INDEX_PUB_RF_LIST_RELAY))
    {
        CommonChangeHandle(data, context);
    }
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaPubRfcardKeyListHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);

}






