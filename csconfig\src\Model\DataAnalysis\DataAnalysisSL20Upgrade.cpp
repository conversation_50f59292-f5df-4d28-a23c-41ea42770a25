#include "DataAnalysisSL20Upgrade.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigCommFileUpdate.h"
#include "UpdateConfigCommAccessUpdate.h"
#include "UpdateConfigPersonalFileUpdate.h"
#include "UpdateConfigCommDevUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "DataAnalysisdbHandle.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/SL20Lock.h"
#include "UpdateSmartLockConfig.h"

static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "SL20Upgrade";
enum DASL20UpgradeIndex{
    DA_INDEX_SL20_UPGRADE_UUID,
    DA_INDEX_SL20_UPGRADE_UPGRADEVERSION,
    DA_INDEX_SL20_UPGRADE_UPGRADESTATUS,
    DA_INDEX_SL20_UPGRADE_SL20LOCKUUID,
    DA_INDEX_SL20_UPGRADE_FIRMWAREDOWNLOADURL,
};
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/ 
   {DA_INDEX_SL20_UPGRADE_UUID, "UUID", ItemChangeHandle},
   {DA_INDEX_SL20_UPGRADE_UPGRADEVERSION, "UpgradeVersion", ItemChangeHandle},
   {DA_INDEX_SL20_UPGRADE_UPGRADESTATUS, "UpgradeStatus", ItemChangeHandle},
   {DA_INDEX_SL20_UPGRADE_SL20LOCKUUID, "SL20LockUUID", ItemChangeHandle},
   {DA_INDEX_SL20_UPGRADE_FIRMWAREDOWNLOADURL, "FirmwareDownloadUrl", ItemChangeHandle},
   {DA_INDEX_INSERT, "", InsertHandle},
   {DA_INDEX_DELETE, "", DeleteHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};


static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{ 
    return 0;
}

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string lock_uuid = data.GetIndex(DA_INDEX_SL20_UPGRADE_SL20LOCKUUID);
    // 通知只需要锁的UUID，其他不需要
    UCSmartLockConfigUpdatePtr smartlockptr = std::make_shared<UCSmartLockConfigUpdate>(SMARTLOCK_SL20_LOCK_UPDATE_NOTIFY, lock_uuid, "", 0, 0);
    context.AddUpdateConfigInfo(UPDATE_SMARTLOCK_CONFIG, smartlockptr);
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    // 具体使用
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    // 升级状态从0变为1时，表示当前需要升级
    if (data.IsIndexChange(DA_INDEX_SL20_UPGRADE_UPGRADESTATUS)
        && data.GetBeforeIndexAsInt(DA_INDEX_SL20_UPGRADE_UPGRADESTATUS) == 0)
    {
        std::string lock_uuid = data.GetIndex(DA_INDEX_SL20_UPGRADE_SL20LOCKUUID);
        
        // 通知只需要锁的UUID，其他不需要
        UCSmartLockConfigUpdatePtr smartlockptr = std::make_shared<UCSmartLockConfigUpdate>(SMARTLOCK_SL20_LOCK_UPDATE_NOTIFY, lock_uuid, "", 0, 0);
        context.AddUpdateConfigInfo(UPDATE_SMARTLOCK_CONFIG, smartlockptr);
    }
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaSL20UpgradeHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}

