#include "DataAnalysisSmartLock.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "DataAnalysisdbHandle.h"
#include "UpdateSmartLockConfig.h"
#include "dbinterface/SmartLock.h"

#define SL20_LOCK_PROJECT_TYPE_PERSONAL 1
#define SL20_LOCK_PROJECT_TYPE_COMMUNITY 2

static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context, bool is_keep_alive_switch_change = false);
static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "SL20Lock";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/ 
   {DA_INDEX_SMART_LOCK_UUID, "UUID", ItemChangeHandle},
   {DA_INDEX_SMART_LOCK_NAME, "Name", ItemChangeHandle},
   {DA_INDEX_SMART_LOCK_MAC, "MAC", ItemChangeHandle},
   {DA_INDEX_SMART_LOCK_WIFISTATUS, "WifiStatus", ItemChangeHandle},
   {DA_INDEX_SMART_LOCK_LASTCONNECTEDTIME, "LastConnectedTime", ItemChangeHandle},
   {DA_INDEX_SMART_LOCK_DEVICEUUID, "DeviceUUID", ItemChangeHandle},
   {DA_INDEX_SMART_LOCK_RELAY, "Relay", ItemChangeHandle},
   {DA_INDEX_SMART_LOCK_AUTOLOCK_ENABLE, "AutoLockEnable", ItemChangeHandle},
   {DA_INDEX_SMART_LOCK_AUTOLOCKDELAY, "AutoLockDelay", ItemChangeHandle},
   {DA_INDEX_SMART_LOCK_PINCODE, "PinCode", ItemChangeHandle},
   {DA_INDEX_SMART_LOCK_IS_PINCODE_SYNCHRONIZING, "IsPinCodeSynchronizing", ItemChangeHandle},
   {DA_INDEX_SMART_LOCK_OFFLINECODE, "", ItemChangeHandle},
   {DA_INDEX_SMART_LOCK_BATTERYLEVEL, "BatteryLevel", ItemChangeHandle},
   {DA_INDEX_SMART_LOCK_MODULEVERSION, "ModuleVersion", ItemChangeHandle},
   {DA_INDEX_SMART_LOCK_LOCKBODYVERSION, "LockBodyVersion", ItemChangeHandle},
   {DA_INDEX_SMART_LOCK_VERSION, "Version", ItemChangeHandle},
   {DA_INDEX_SMART_LOCK_RBACDATAGROUPUUID, "RBACDataGroupUUID", ItemChangeHandle},
   {DA_INDEX_SMART_LOCK_INSTALLERUUID, "InstallerUUID", ItemChangeHandle},
   {DA_INDEX_SMART_LOCK_PROJECTTYPE, "ProjectType", ItemChangeHandle},
   {DA_INDEX_SMART_LOCK_ACCOUNTUUID, "AccountUUID", ItemChangeHandle},
   {DA_INDEX_SMART_LOCK_COMMUNITYUNITUUID, "CommunityUnitUUID", ItemChangeHandle},
   {DA_INDEX_SMART_LOCK_PERSONALACCOUNTUUID, "PersonalAccountUUID", ItemChangeHandle},
   {DA_INDEX_SMART_LOCK_UPDATETIME, "UpdateTime", ItemChangeHandle},
   {DA_INDEX_SMART_LOCK_CREATETIME, "CreateTime", ItemChangeHandle},
   {DA_INDEX_SMART_LOCK_KEEP_ALIVE, "KeepAlive", ItemChangeHandle},
   {DA_INDEX_SMART_LOCK_SECRET_KEY, "SecretKey", ItemChangeHandle},
   {DA_INDEX_INSERT, "", InsertHandle},
   {DA_INDEX_DELETE, "", DeleteHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};


static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context, bool is_keep_alive_switch_change)
{
    std::string lock_uuid = data.GetIndex(DA_INDEX_SMART_LOCK_UUID);
    uint32_t change_type = SMARTLOCK_SL20_CONFIG_UPDATE;
    if (is_keep_alive_switch_change)
    {
        change_type = SMARTLOCK_SL20_LOCK_KEEP_ALIVE_SWITCH_CHANGE;
    }
    uint32_t sl20_project_type = data.GetIndexAsInt(DA_INDEX_SMART_LOCK_PROJECTTYPE);
    std::string per_uuid = data.GetIndex(DA_INDEX_SMART_LOCK_PERSONALACCOUNTUUID);
    uint32_t project_type = 0;
    UserInfo user_info;
    memset(&user_info, 0, sizeof(user_info));
    if (dbhandle::DAInfo::GetUserInfoByUUID(per_uuid, user_info) != 0)
    {
        AK_LOG_INFO << local_table_name << " CommonHandle. User is null, per_uuid=" << per_uuid;
        return -1;
    }
    if (sl20_project_type == SL20_LOCK_PROJECT_TYPE_PERSONAL)
    {
        project_type = project::PERSONAL;
    }
    else if (sl20_project_type == SL20_LOCK_PROJECT_TYPE_COMMUNITY)
    {
        project_type = project::RESIDENCE;
    }
    else 
    {
        AK_LOG_WARN << "project type not support. project type=" << sl20_project_type;
        return -1;
    }

    UCSmartLockConfigUpdatePtr smartlockptr = std::make_shared<UCSmartLockConfigUpdate>(change_type, lock_uuid, user_info.node, project_type, user_info.mng_id);
    context.AddUpdateConfigInfo(UPDATE_SMARTLOCK_CONFIG, smartlockptr);
   
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return CommonChangeHandle(data, context);
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    if (data.IsIndexChange(DA_INDEX_SMART_LOCK_KEEP_ALIVE))
    {
        return CommonChangeHandle(data, context, true);
    }

    if (data.IsIndexChange(DA_INDEX_SMART_LOCK_AUTOLOCK_ENABLE) ||
        data.IsIndexChange(DA_INDEX_SMART_LOCK_AUTOLOCKDELAY) || 
        data.IsIndexChange(DA_INDEX_SMART_LOCK_KEEP_ALIVE))
    {
        return CommonChangeHandle(data, context);
    }
    return 0;
}

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaSL20LockHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}

