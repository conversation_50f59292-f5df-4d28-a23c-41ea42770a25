#include "DataAnalysisSpecial.h"
#include "DataAnalysis.h"
#include "json/json.h"
#include "AkLogging.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include <string.h>
#include <memory>
#include "UpdateConfigOfficeDevUpdate.h"
#include "UpdateConfigOfficeFileUpdate.h"
#include "UpdateConfigOfficeAccessUpdate.h"
#include "UpdateConfigCommFileUpdate.h"
#include "UpdateConfigCommDevUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "util.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"

static bool DaGetCommunityMngId(DataAnalysisSqlKV &kv, unsigned int &mng_id)
{
    auto iter = kv.find(DA_SPECIAL_COMMUNITY_ID);
    if(iter != kv.end())
    {
        mng_id = ATOI(iter->second.c_str());
        return true;
    }
    else
    {
        return false;
    }
}

static bool DaGetOfficeMngId(DataAnalysisSqlKV &kv, unsigned int &mng_id)
{
    auto iter = kv.find(DA_SPECIAL_OFFICE_ID);
    if(iter != kv.end())
    {
        mng_id = ATOI(iter->second.c_str());
        return true;
    }
    else
    {
        return false;
    }
}

static bool DaGetProjcetId(DataAnalysisSqlKV &kv, unsigned int &mng_id)
{
    auto iter = kv.find(DA_SPECIAL_PROJECT_ID);
    if(iter != kv.end())
    {
        mng_id = ATOI(iter->second.c_str());
        return true;
    }
    else
    {
        return false;
    }
}

static bool DaGetSpecialMacs(DataAnalysisSqlKV &kv, std::string &mac)
{
    auto iter = kv.find(DA_SPECIAL_MACS);
    if(iter != kv.end())
    {
        mac = iter->second;
        return true;
    }
    else
    {
        return false;
    }
}

static bool DaGetSpecialNodes(DataAnalysisSqlKV &kv, std::string &nodes)
{
    auto iter = kv.find(DA_SPECIAL_NODES);
    if(iter != kv.end())
    {
        nodes = iter->second;
        return true;
    }
    else
    {
        return false;
    }
}

static bool DaGetSpecialValueByKey(DataAnalysisSqlKV &kv, const std::string& key, std::string &value)
{
    auto iter = kv.find(key);
    if(iter != kv.end())
    {
        value = iter->second;
        return true;
    }
    else
    {
        return false;
    }
}

static bool DaGetSpecialAccounts(DataAnalysisSqlKV &kv, std::string &accounts)
{
    auto iter = kv.find(DA_SPECIAL_ACCOUNTS);
    if(iter != kv.end())
    {
        accounts = iter->second;
        return true;
    }
    else
    {
        return false;
    }
}

static void DaImportUserHandler(DataAnalysisSqlKV &kv, DataAnalysisContext &context)
{
    uint32_t change_type = 0;
    uint32_t mng_id = 0;
    uint32_t unit_id = 0;
    std::string mac;
    std::string nodes;
    std::set<std::string> node_list;
    change_type = WEB_COMM_ADD_USER;

    if (DaGetCommunityMngId(kv, mng_id) == false)
    {
        AK_LOG_WARN << "DataAnalysis special handle type: import user but mng_id is null";
        return;
    }

    if (DaGetSpecialNodes(kv, nodes) == false)
    {
        AK_LOG_WARN << "DataAnalysis special handle type: import user but nodes is null";
        return;
    }
    SplitString(nodes, ",", node_list);
    AK_LOG_INFO  << "DataAnalysis import user. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " community_id= " << mng_id << " nodes= " << nodes ;

    //added by czw
    //超过10个 其实就和导社区耗时差不多了,一个个刷反而容易堆积
    if(node_list.size() > 10)
    {
        AK_LOG_INFO  << "The number of imported users exceeds 10, refresh community config";
        for(const auto& node : node_list)
        {
            //更新数据版本,防止二次导入失效
            dbinterface::ProjectUserManage::UpdateDataVersionByNode(node);
        }
        change_type = WEB_COMM_IMPORT_COMMUNITY;
        UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, 0, "", "");
        context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);     
    }
    else
    {
        //导入较少的话，一个个刷比较省时
        for(const auto& node : node_list)
        {
            ResidentPerAccount account;
            memset(&account, 0, sizeof(account));
            if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(node, account))
            {
                //更新数据版本,防止二次导入失效
                dbinterface::ProjectUserManage::UpdateDataVersionByNode(node);
                UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, account.unit_id, mac, node);
                context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
            }
        }
    }
}

static void DaImportOfficeUserHandler(DataAnalysisSqlKV &kv, DataAnalysisContext &context)
{
    uint32_t change_type = WEB_OFFICE_MODIFY_USER_ALL_ACCESS;
    uint32_t mng_id;
    std::string mac;
    std::string accounts;
    std::set<std::string> account_list;

    if (DaGetOfficeMngId(kv, mng_id) == false)
    {
        AK_LOG_WARN << "DataAnalysis special handle type: import office user but mng_id is null";
        return;
    }

    if (DaGetSpecialAccounts(kv, accounts) == false)
    {
        AK_LOG_WARN << "DataAnalysis special handle type: import user but accounts is null";
        return;
    }
    SplitString(accounts, ",", account_list);
    AK_LOG_INFO  << "DataAnalysis import user. office_id= " << mng_id << " accounts= " << accounts;

    //超过10个 其实就和导项目耗时差不多了,一个个刷反而容易堆积
    if(account_list.size() > 10)
    {
        AK_LOG_INFO  << "The number of imported users exceeds 10, refresh office config";
        for(const auto& account : account_list)
        {
            //更新数据版本,防止二次导入失效
            dbinterface::ProjectUserManage::UpdateDataVersionByNode(account);
        }
        change_type = WEB_OFFICE_IMPORT_OFFICE;
        UCOfficeFileUpdatePtr ptr = std::make_shared<UCOfficeFileUpdate>(change_type, mng_id, 0, "", "");
        context.AddUpdateConfigInfo(UPDATE_OFFICE_DEV_UPDATE, ptr);     
    }
    else
    {
        //导入较少的话，一个个刷比较省时
        for(const auto& account : account_list)
        {
            ResidentPerAccount per_account;
            memset(&per_account, 0, sizeof(per_account));
            if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(account, per_account))
            {
                //更新数据版本,防止二次导入失效
                dbinterface::ProjectUserManage::UpdateDataVersionByNode(account);
                UCOfficeAccessUpdatePtr ptr = std::make_shared<UCOfficeAccessUpdate>(change_type, mng_id, mac, account);
                context.AddUpdateConfigInfo(UPDATE_OFFICE_ACCESS_UPDATE, ptr);
            }
        }
    }

}

static void DaIndoorPlanNotifyHandler(DataAnalysisSqlKV &kv, DataAnalysisContext &context)
{
    uint32_t change_type = WEB_COMM_ADD_INDOOR_PLAN_DEV;
    uint32_t mng_id;
    std::string macs;
    std::vector<std::string> mac_list;

    if (DaGetProjcetId(kv, mng_id) == false)
    {
        AK_LOG_WARN << "DataAnalysis special handle type: indoor plan notify but mng_id is null";
        return;
    }

    if (DaGetSpecialMacs(kv, macs) == false)
    {
        AK_LOG_WARN << "DataAnalysis special handle type: indoor plan notify but macs is null";
        return;
    }
    SplitString(macs, ",", mac_list);
    AK_LOG_INFO  << "DataAnalysis indoor plan notify. community id= " << mng_id << " macs= " << macs;

    UCCommunityDevUpdatePtr devptr = std::make_shared<UCCommunityDevUpdate>(change_type, mac_list);//只通知设备上报状态，不涉及配置的更新
    context.AddUpdateConfigInfo(UPDATE_COMM_DEV_UPDATE, devptr);
}

void DaSpecialHandler(const std::string &type, DataAnalysisSqlKV &kv, DataAnalysisContext &context)
{
    uint32_t change_type;
    uint32_t mng_id;
    uint32_t unit_id = 0;
    std::string mac;
    std::string uid;
    auto special_type_map = SpecialType.find(type);
    if(special_type_map != SpecialType.end())
    {
        int special_type = special_type_map->second;
        switch(special_type)
        {
            case DA_TYPE_IMPORT_COMMUNITY:
            {
                change_type = WEB_COMM_IMPORT_COMMUNITY;
                if (DaGetCommunityMngId(kv, mng_id) == false)
                {
                    AK_LOG_WARN << " DataAnalysis special handle type: import community but data is null";
                    return;
                }

                AK_LOG_INFO << " DataAnalysis special handle type: import community, comm_id =" << mng_id;
                //更新数据版本
                dbinterface::ProjectUserManage::UpdateCommunityAllAccountDataVersion(mng_id);
                UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, uid);
                context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
                break;
            }
            case DA_TYPE_IMPORT_OFFICE:
            {
                change_type = WEB_OFFICE_IMPORT_OFFICE;
                if (DaGetOfficeMngId(kv, mng_id));
                
                AK_LOG_INFO << " DataAnalysis special handle type: import office, office_id =" << mng_id;
                //更新数据版本
                dbinterface::ProjectUserManage::UpdateOfficeAllAccountDataVersion(mng_id);
                UCOfficeFileUpdatePtr ptr = std::make_shared<UCOfficeFileUpdate>(change_type, mng_id, unit_id, mac, uid);
                context.AddUpdateConfigInfo(UPDATE_OFFICE_FILE_UPDATE, ptr);
                break;
            }
            case DA_TYPE_DELETE_OFFICE:
            {
                change_type = WEB_OFFICE_DELETE_OFFICE;
                //获取mng_id
                if (DaGetOfficeMngId(kv, mng_id) == false)
                {
                    AK_LOG_WARN << " DataAnalysis special handle type: delete office but mng_id is null";
                    return;
                }
                
                //获取macs
                if (DaGetSpecialMacs(kv, mac) == false)
                {
                    AK_LOG_WARN << " DataAnalysis special handle type: delete office but mac is null";
                    return;
                }

                std::vector<std::string> omac;
                SplitString(mac, ",", omac);
                AK_LOG_INFO << " DataAnalysis special handle type: delete office, office_id =" << mng_id;
                //删除办公根据mac通知设备下载空的配置文件，不需要更新数据版本
                UCOfficeDevUpdatePtr ptr = std::make_shared<UCOfficeDevUpdate>(change_type, omac);
                context.AddUpdateConfigInfo(UPDATE_OFFICE_DEV_UPDATE, ptr);
                break;
            }
            case DA_TYPE_DELETE_COMMUNITY:
            {
                change_type = WEB_COMM_DELETE_COMMUNITY;
                //获取mng_id
                if (DaGetCommunityMngId(kv, mng_id) == false)
                {
                    AK_LOG_WARN << " DataAnalysis special handle type: delete community but mng_id is null";
                    return;
                }
                
                //获取macs
                if (DaGetSpecialMacs(kv, mac) == false)
                {
                    AK_LOG_WARN << " DataAnalysis special handle type: delete community but mac is null";
                    return;
                }

                std::vector<std::string> omac;
                SplitString(mac, ",", omac);
                AK_LOG_INFO << " DataAnalysis special handle type: delete community, comm_id =" << mng_id;

                UCCommunityFileUpdatePtr file_ptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, uid);
                UCCommunityDevUpdatePtr ptr = std::make_shared<UCCommunityDevUpdate>(change_type, omac);
                context.AddUpdateConfigInfo(UPDATE_COMM_DEV_UPDATE, ptr);
                break;
            }
            case DA_TYPE_IMPORT_USER:
            {
                DaImportUserHandler(kv, context);
                break;
            }
            case DA_TYPE_COMMUNITY_CUSTOM_CONTACT:
            {
                change_type = WEB_COMM_UPDATE_PUB_MAC_CONTACT;
                //获取mng_id
                if (DaGetCommunityMngId(kv, mng_id) == false)
                {
                    AK_LOG_WARN << " DataAnalysis special handle type: custom contact but mng_id is null";
                    return;
                }
                
                //获取mac
                if (DaGetSpecialValueByKey(kv, "mac", mac) == false)
                {
                    AK_LOG_WARN << " DataAnalysis special handle type: custom contact but mac is null";
                    return;
                }

                AK_LOG_INFO << " DataAnalysis special handle type: custom contact, comm_id=" << mng_id << " mac=" << mac;

                UCCommunityFileUpdatePtr ptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, uid);
                context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, ptr);
                
                break;
            }
            case DA_TYPE_IMPORT_OFFICE_USER:
            {
                DaImportOfficeUserHandler(kv, context);
                break;
            }
            //室内机方案特殊数据分析，为解决大社区更改为室内机方案后大量插入DevicesSpecial的问题
            case DA_TYPE_INDOOR_PLAN_NOTIFY:
            {
                DaIndoorPlanNotifyHandler(kv, context);
                break;
            }
        }
    }
    else
    {
        AK_LOG_WARN << " DaSpecialHandler. no such special type";
        return;
    }

}






