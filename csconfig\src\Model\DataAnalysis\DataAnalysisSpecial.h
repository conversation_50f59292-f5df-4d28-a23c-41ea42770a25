#ifndef __CSADAPT_DATAANALYSIS_SPECIAL_H__
#define __CSADAPT_DATAANALYSIS_SPECIAL_H__

#include <vector>
#include <string>
#include <map>
#include <list>
#include "DataAnalysisDef.h"

enum DASpecialType{
    DA_TYPE_IMPORT_COMMUNITY = 1,
    DA_TYPE_IMPORT_OFFICE,
    DA_TYPE_DELETE_OFFICE,
    DA_TYPE_DELETE_COMMUNITY,
    DA_TYPE_IMPORT_USER,
    DA_TYPE_IMPORT_OFFICE_USER,
    DA_TYPE_COMMUNITY_CUSTOM_CONTACT,
    DA_TYPE_INDOOR_PLAN_NOTIFY,
};

#define DA_SPECIAL_COMMUNITY_ID    "communityId"
#define DA_SPECIAL_OFFICE_ID    "officeId"
#define DA_SPECIAL_PROJECT_ID "projectId"
#define DA_SPECIAL_MACS    "macs"
#define DA_SPECIAL_NODES   "nodes"
#define DA_SPECIAL_ACCOUNTS "accounts"

const std::map<std::string, int> SpecialType = {
    {"importCommunity", DA_TYPE_IMPORT_COMMUNITY},
    {"importOffice", DA_TYPE_IMPORT_OFFICE},
    {"deleteOffice", DA_TYPE_DELETE_OFFICE},
    {"deleteCommunity", DA_TYPE_DELETE_COMMUNITY},
    {"importUser", DA_TYPE_IMPORT_USER},
    {"importOfficeUser", DA_TYPE_IMPORT_OFFICE_USER},
    {"communityCustomContact", DA_TYPE_COMMUNITY_CUSTOM_CONTACT},
    {"indoorPlanNotify", DA_TYPE_INDOOR_PLAN_NOTIFY},
};
void DaSpecialHandler(const std::string &type, DataAnalysisSqlKV &kv, DataAnalysisContext &context);


#endif //__CSADAPT_DATAANALYSIS_SPECIAL_H__
