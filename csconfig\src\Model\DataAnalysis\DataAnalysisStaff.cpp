#include "DataAnalysisStaff.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigOfficeAccessUpdate.h"
#include "UpdateConfigCommAccessUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "PersonalAccount.h"
#include "dbinterface/ProjectUserManage.h"
#include "DataAnalysisdbHandle.h"
#include "DeviceSetting.h"
#include "dbinterface/StaffAccess.h"





static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int GetDevicesChangeType();


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "Staff";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
    {DA_INDEX_STAFF_ID, "ID", ItemChangeHandle},
    {DA_INDEX_STAFF_NAME, "Name", ItemChangeHandle},
    {DA_INDEX_STAFF_COMMUNITYID, "CommunityID", ItemChangeHandle},
    {DA_INDEX_STAFF_CARDCODE, "CardCode", ItemChangeHandle},
    {DA_INDEX_STAFF_FILENAME, "FileName", ItemChangeHandle},
    {DA_INDEX_STAFF_FACEURL, "FaceUrl", ItemChangeHandle},
    {DA_INDEX_STAFF_FACEMD5, "FaceMD5", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string uid;
    std::string mac;
    
    uint32_t project_type = data.GetProjectType();
    uint32_t staff_id = data.GetIndexAsInt(DA_INDEX_STAFF_ID);
    uint32_t mng_id = data.GetIndexAsInt(DA_INDEX_STAFF_COMMUNITYID);

    uint32_t change_type = WEB_COMM_MODIFY_STAFF;
    uint32_t office_change_type = WEB_OFFICE_MODIFY_STAFF;

    //更新数据版本
    dbinterface::ProjectUserManage::UpdateDataVersionByStaffID(staff_id);
    
    //此时一定可以从StaffAccess查找到
    std::vector<unsigned int> ag_ids;
    if (0 == dbinterface::StaffAccess::GetAgIDsByStaffID(staff_id, ag_ids))
    {
        for (const auto& ag_id : ag_ids)
        {
             if (project_type == project::OFFICE)
            {   
                AK_LOG_INFO << local_table_name << " CommonHandle. office change type=" << office_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(office_change_type) << " node= " << uid
                        << " office_id= " << mng_id << " mac= " << mac << " ag_id= " << ag_id;
                        
                UCOfficeAccessUpdatePtr ptr = std::make_shared<UCOfficeAccessUpdate>(office_change_type, mng_id, mac, uid, ag_id);
                context.AddUpdateConfigInfo(UPDATE_OFFICE_ACCESS_UPDATE, ptr);    
            }
            else 
            {
                AK_LOG_INFO << local_table_name << " CommonHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
                        << " community_id= " << mng_id << " mac= " << mac << " ag_id= " << ag_id;
                        
                UCCommunityAccessUpdatePtr ptr = std::make_shared<UCCommunityAccessUpdate>(change_type, mng_id, mac, uid, ag_id);
                context.AddUpdateConfigInfo(UPDATE_COMM_ACCESS_UPDATE, ptr);
            }
        }
    }
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //新建staff时insert
    CommonChangeHandle(data, context);
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //删除快递员一定会删除StaffAccess,在StaffAccess处理
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //更新name,card,face时update
    CommonChangeHandle(data, context);
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaStaffHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);
}






