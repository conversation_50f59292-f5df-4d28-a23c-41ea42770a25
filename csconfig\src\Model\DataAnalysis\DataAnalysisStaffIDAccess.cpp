#include "DataAnalysisStaffIDAccess.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include <vector>
#include "UpdateConfigCommAccessUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "dbinterface/ProjectUserManage.h"
#include "DataAnalysisdbHandle.h"
#include "dbinterface/StaffAccess.h"
#include "dbinterface/Staff.h"

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "StaffIDAccess";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
    {DA_INDEX_STAFF_ID_ACCESS_STAFF_UUID, "StaffUUID", ItemChangeHandle},
    {DA_INDEX_STAFF_ID_ACCESS_MODE, "Mode", ItemChangeHandle},
    {DA_INDEX_STAFF_ID_ACCESS_RUN, "Run", ItemChangeHandle},
    {DA_INDEX_STAFF_ID_ACCESS_SERIAL, "Serial", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    uint32_t project_type = data.GetProjectType();
    uint32_t change_type = WEB_COMM_MODIFY_STAFF;
    std::string uid;
    std::string mac;

    if(project_type != project::RESIDENCE)
    {
        AK_LOG_WARN << "project type not support staff id access. project_type=" << project_type;
        return -1;
    }

    std::string staff_uuid = data.GetIndex(DA_INDEX_STAFF_ID_ACCESS_STAFF_UUID);
    dbinterface::StaffInfo staff_info;
    if (0 != dbinterface::Staff::InitStaffByUUID(staff_uuid, staff_info))
    {
        AK_LOG_WARN << "get staff info failed.";
        return -1;
    }
    uint32_t mng_id = staff_info.community_id;
    //更新数据版本
    dbinterface::ProjectUserManage::UpdateDataVersionByStaffUUID(staff_uuid);
    //获取staff对应的权限组
    std::vector<uint32_t> ag_ids;
    if (0 != dbinterface::StaffAccess::GetAgIDsByStaffUUID(staff_uuid, ag_ids))
    {
        AK_LOG_WARN << "get staff access group list failed. staff uuid=" << staff_uuid;
        return -1;
    }
    
    for(const auto& ag_id : ag_ids)
    {
        AK_LOG_INFO << local_table_name << " UpdateHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
                << " community_id= " << mng_id << " mac= " << mac << " ag_id= " << ag_id;

        UCCommunityAccessUpdatePtr ptr = std::make_shared<UCCommunityAccessUpdate>(change_type, mng_id, mac, uid, ag_id);
        context.AddUpdateConfigInfo(UPDATE_COMM_ACCESS_UPDATE, ptr);
    }

    return 0;
}

//staff表数据分析已经有处理
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    CommonChangeHandle(data, context);
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    CommonChangeHandle(data, context);
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    CommonChangeHandle(data, context);
    return 0;
}


static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaStaffIDAccessHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);
}

