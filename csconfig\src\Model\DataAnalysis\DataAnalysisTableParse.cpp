#include "DataAnalysisTableParse.h"
#include "DataAnalysis.h"
#include <assert.h>
#include "AkLogging.h"
#include "util.h"


DataAnalysisTableParse::DataAnalysisTableParse(const DataAnalysisColumnList &detect_key, const DataAnalysisSqlKV &before, const DataAnalysisSqlKV &after)
  :detect_key_(detect_key),before_(before),after_(after)
{
    key_length_ = detect_key.size();
    for (auto &key : detect_key)
    {
        if (before_[key] == after_[key])
        {
            detect_key_change_result_.push_back(false);
        }
        else
        {
            detect_key_change_result_.push_back(true);
        }
    }
    opt_typt_ = DA_OPERATION_NULL;
}

DataAnalysisTableParse::DataAnalysisTableParse(const DataAnalysisColumnList &detect_key, const DataAnalysisSqlKV &data)
:detect_key_(detect_key),after_(data)
{
    key_length_ = detect_key.size();
    opt_typt_ = DA_OPERATION_NULL;
}

DataAnalysisTableParse::~DataAnalysisTableParse()
{

}

void DataAnalysisTableParse::SetOperation(DBHandleType type)
{
    opt_typt_ = type;
}

int DataAnalysisTableParse::GetOperation() const
{
    return opt_typt_;
}

void DataAnalysisTableParse::SetProjectType(int project_type)
{
    project_type_ = project_type;
}

const int DataAnalysisTableParse::GetProjectType()
{
    return project_type_;
}

bool DataAnalysisTableParse::IsIndexChange(int index) const
{
    assert(index <= key_length_  &&  index >= 0);
    return detect_key_change_result_[index];
}

const int DataAnalysisTableParse::GetDetectChangeByIndex(int index, std::string &before, std::string &after)
{
   assert(index <= key_length_  &&  index >= 0);
   assert(opt_typt_ == DA_OPERATION_UPDATE);
   after = after_[detect_key_[index]];
   before = before_[detect_key_[index]];
   return 0;
}

const int DataAnalysisTableParse::GetDetectChangeByIndex(int index,        int &before, int &after)
{
   assert(index <= key_length_  &&  index >= 0);
   assert(opt_typt_ == DA_OPERATION_UPDATE);
   after = ATOI(after_[detect_key_[index]].c_str());
   before = ATOI(before_[detect_key_[index]].c_str());
   return 0;    
}


const std::string DataAnalysisTableParse::GetIndex(int index)
{
    assert(index <= key_length_  &&  index >= 0);
    return after_[detect_key_[index]];
}

const std::string DataAnalysisTableParse::GetBeforeIndex(int index)
{
    assert(index <= key_length_  &&  index >= 0);
    return before_[detect_key_[index]];    
}

int DataAnalysisTableParse::GetIndexAsInt(int index)
{
    assert(index <= key_length_  &&  index >= 0);
    return ATOI(after_[detect_key_[index]].c_str());    
}

int DataAnalysisTableParse::GetBeforeIndexAsInt(int index)
{
    assert(index <= key_length_  &&  index >= 0);
    return ATOI(before_[detect_key_[index]].c_str());    
}



