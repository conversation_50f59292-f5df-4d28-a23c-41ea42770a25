#ifndef __CSADAPT_DATA_ANALYSIS_TABLE_HANDLE_H__
#define __CSADAPT_DATA_ANALYSIS_TABLE_HANDLE_H__

#include <vector>
#include <string>
#include <map>
#include <list>
#include <memory>
#include "DataAnalysisDef.h"
//#include "AKCSMsg.h"
class DataAnalysisTableParse
{
public:
    enum DBHandleType{
        DA_OPERATION_NULL ,
        DA_OPERATION_UPDATE,
        DA_OPERATION_INSERT,
        DA_OPERATION_DELETE
    };
    /*for update*/
    DataAnalysisTableParse(const DataAnalysisColumnList &detect_key, const DataAnalysisSqlKV &before, const DataAnalysisSqlKV &after);
    /*for insert/delete*/
    DataAnalysisTableParse(const DataAnalysisColumnList &detect_key, const DataAnalysisSqlKV &data);
    ~DataAnalysisTableParse();

    bool IsIndexChange(int index) const;
    const int GetDetectChangeByIndex(int index, std::string &before, std::string &after);
    const int GetDetectChangeByIndex(int index,        int &before, int &after);
    const std::string GetIndex(int index);
    int GetIndexAsInt(int index);
    const std::string GetBeforeIndex(int index);
    int GetBeforeIndexAsInt(int index);

    void SetOperation(DBHandleType type);
    int GetOperation() const;
    void SetProjectType(int project_type);
    const int GetProjectType();
private:
    /*这样用下标直接查找提高效率*/
    DataAnalysisColumnList detect_key_;
    DataAnalysisColumnChange detect_key_change_result_;

    DataAnalysisSqlKV before_;
    DataAnalysisSqlKV after_;
    DataAnalysisSqlKV data_;
    int key_length_;
    DBHandleType opt_typt_;
    int project_type_;
};





#endif //__CSADAPT_DATA_ANALYSIS_TABLE_HANDLE_H__