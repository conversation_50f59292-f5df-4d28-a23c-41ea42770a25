#include "DataAnalysisThirdPartCamera.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigOfficeFileUpdate.h"
#include "UpdateConfigCommFileUpdate.h"
#include "UpdateConfigOfficeDevUpdate.h"
#include "UpdateConfigCommDevUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "dbinterface/ProjectUserManage.h"
#include "dbinterface/Account.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "DataAnalysisdbHandle.h"



static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int GetDevicesChangeType();


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "ThirdPartCamera";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
	{DA_INDEX_THIRDPART_CAMERA_ID, "ID", ItemChangeHandle},
	{DA_INDEX_THIRDPART_CAMERA_UUID, "UUID", ItemChangeHandle},
	{DA_INDEX_THIRDPART_CAMERA_PROJECTUUID, "ProjectUUID", ItemChangeHandle},
	{DA_INDEX_THIRDPART_CAMERA_UNITID, "UnitID", ItemChangeHandle},
	{DA_INDEX_THIRDPART_CAMERA_PERSONALACCOUNTUUID, "PersonalAccountUUID", ItemChangeHandle},
	{DA_INDEX_THIRDPART_CAMERA_GRADE, "Grade", ItemChangeHandle},
	{DA_INDEX_THIRDPART_CAMERA_LOCATION, "Location", ItemChangeHandle},
	{DA_INDEX_THIRDPART_CAMERA_RTSPADDRESS, "RtspAddress", ItemChangeHandle},
	{DA_INDEX_THIRDPART_CAMERA_RTSPPORT, "RtspPort", ItemChangeHandle},
	{DA_INDEX_THIRDPART_CAMERA_RTSPUSERNAME, "RtspUserName", ItemChangeHandle},
	{DA_INDEX_THIRDPART_CAMERA_RTSPPWD, "RtspPwd", ItemChangeHandle},
	{DA_INDEX_THIRDPART_CAMERA_SWITCH, "Switch", ItemChangeHandle},
	{DA_INDEX_THIRDPART_CAMERA_MAC, "MAC", ItemChangeHandle},
	{DA_INDEX_INSERT, "", InsertHandle},
	{DA_INDEX_DELETE, "", DeleteHandle},
	{DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
   return 0; 
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string mng_uuid = data.GetIndex(DA_INDEX_THIRDPART_CAMERA_PROJECTUUID);
    uint32_t unit_id = data.GetIndexAsInt(DA_INDEX_THIRDPART_CAMERA_UNITID);
    uint32_t grade = data.GetIndexAsInt(DA_INDEX_THIRDPART_CAMERA_GRADE);
    std::string personal_uuid = data.GetIndex(DA_INDEX_THIRDPART_CAMERA_PERSONALACCOUNTUUID);
    std::string mac = data.GetIndex(DA_INDEX_THIRDPART_CAMERA_MAC);
    std::string uid;
    uint32_t change_type = 0;
    uint32_t mng_id = 0;
    //根据uuid获取mng_id和node
    dbinterface::AccountInfo account;
    if (0 != dbinterface::Account::GetAccountByUUID(mng_uuid, account))
    {
        AK_LOG_WARN << local_table_name << " InsertHandle. GetAccountByUUID is null, mng_uuid=" << mng_uuid;
        return -1;
    }
    mng_id = account.id;
    ResidentPerAccount per_account;
    memset(&per_account, 0, sizeof(per_account));
    if (0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(personal_uuid, per_account))
    {
        uid = per_account.account;
    }

    if (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
    {
        change_type = WEB_COMM_PUB_ADD_DEV;
    }
    else if (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
        change_type = WEB_COMM_UNIT_ADD_DEV;
    }
    else
    {
        change_type = WEB_COMM_ADD_DEV;
    }
    AK_LOG_INFO << local_table_name << " UpdateHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
        << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
    UCCommunityFileUpdatePtr fileptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, uid);
    context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, fileptr);
    return 0;

}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string mng_uuid = data.GetIndex(DA_INDEX_THIRDPART_CAMERA_PROJECTUUID);
    uint32_t unit_id = data.GetIndexAsInt(DA_INDEX_THIRDPART_CAMERA_UNITID);
    uint32_t grade = data.GetIndexAsInt(DA_INDEX_THIRDPART_CAMERA_GRADE);
    std::string personal_uuid = data.GetIndex(DA_INDEX_THIRDPART_CAMERA_PERSONALACCOUNTUUID);
    std::string mac = data.GetIndex(DA_INDEX_THIRDPART_CAMERA_MAC);
    uint32_t change_type = 0;
    uint32_t mng_id = 0;
    std::string uid;

    //根据uuid获取mng_id和node
    dbinterface::AccountInfo account;
    if (0 != dbinterface::Account::GetAccountByUUID(mng_uuid, account))
    {
        AK_LOG_WARN << local_table_name << " DeleteHandle. GetAccountByUUID is null, mng_uuid=" << mng_uuid;
        return -1;
    }
    mng_id = account.id;
    ResidentPerAccount per_account;
    memset(&per_account, 0, sizeof(per_account));
    if (0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(personal_uuid, per_account))
    {
        uid = per_account.account;
    }

    if (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
    {
        change_type = WEB_COMM_PUB_DEL_THIRD_CAMERA;
    }
    else if (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
        change_type = WEB_COMM_UNIT_DEL_THIRD_CAMERA;
    }
    else
    {
        change_type = WEB_COMM_DEL_THIRD_CAMERA;
    }
    

    AK_LOG_INFO << local_table_name << " UpdateHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
            << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
    UCCommunityFileUpdatePtr fileptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, uid);
    context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, fileptr);
    return 0;

}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string mng_uuid = data.GetIndex(DA_INDEX_THIRDPART_CAMERA_PROJECTUUID);
    uint32_t unit_id = data.GetIndexAsInt(DA_INDEX_THIRDPART_CAMERA_UNITID);
    uint32_t grade = data.GetIndexAsInt(DA_INDEX_THIRDPART_CAMERA_GRADE);
    std::string personal_uuid = data.GetIndex(DA_INDEX_THIRDPART_CAMERA_PERSONALACCOUNTUUID);
    std::string mac = data.GetIndex(DA_INDEX_THIRDPART_CAMERA_MAC);
    uint32_t change_type = 0;
    uint32_t mng_id = 0;
    std::string uid;

    //根据uuid获取mng_id和node
    dbinterface::AccountInfo account;
    if (0 != dbinterface::Account::GetAccountByUUID(mng_uuid, account))
    {
        AK_LOG_WARN << local_table_name << " UpdateHandle. GetAccountByUUID is null, mng_uuid=" << mng_uuid;
        return -1;
    }
    mng_id = account.id;
    ResidentPerAccount per_account;
    memset(&per_account, 0, sizeof(per_account));
    if (0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(personal_uuid, per_account))
    {
        uid = per_account.account;
    }

    if (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
    {
        change_type = WEB_COMM_PUB_MODIFY_DEV;
    }
    else if (grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
        change_type = WEB_COMM_UNIT_MODIFY_DEV;
    }
    else
    {
        change_type = WEB_COMM_MODIFY_DEV;
    }
    
    AK_LOG_INFO << local_table_name << " UpdateHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
            << " community_id= " << mng_id << " unit id= " << unit_id << " mac= " << mac;
    UCCommunityFileUpdatePtr fileptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, uid);
    context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, fileptr);
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaThirdPartCameraHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);

}






