#include "DataAnalysisUpdateConfig.h"
#include "UpdateConfigPayPlan.h"
#include "AkLogging.h"
#include "UpdateConfigCommFileUpdate.h"
#include "UpdateConfigOfficeFileUpdate.h"
#include "UpdateConfigCommAccessUpdate.h"
#include "UpdateConfigOfficeAccessUpdate.h"
#include "UpdateConfigCommDevUpdate.h"
#include "UpdateConfigOfficeDevUpdate.h"
#include "UpdateConfigPersonalFileUpdate.h"
#include "UpdateSmartLockConfig.h"
#include <set>


UCHandlerToolMap g_update_config_tool_map;
void RegUpdateConfigTool(int type, UpdateConfigHandlerFunc hander, UpdateConfigHandlerIdentify identify_hander)
{
    UpdateConfigToolPtr ptr = std::make_shared<UpdateConfigTool>(hander, identify_hander); 
    g_update_config_tool_map.insert(std::map<int, UpdateConfigToolPtr>::value_type(type, ptr));

}


void UpdateConfigDispatch(UpdateConfigInfoMap &vec_datas)
{
    std::set<std::string> identify_list;
    for (auto &vec : vec_datas)
    {
        UCHandlerToolMap::iterator handler_it;
        handler_it = g_update_config_tool_map.find(vec.first);
        if (handler_it == g_update_config_tool_map.end())
        {
            AK_LOG_WARN << "UpdateConfigDispatch can not found type=" << vec.first;
            continue;
        }

        for (auto &data : vec.second)
        {
            UpdateConfigToolPtr toolptr = handler_it->second;
            std::string identify = toolptr->identify_func_(data);
            
            std::set<std::string>::iterator iter;
            if((iter = identify_list.find(identify)) == identify_list.end())
            {
                toolptr->hander_func_(data);
                identify_list.insert(identify);
            }            
        }
    }    
}

void UpdateConfigInit()
{
    RegPayPlanTool();
    RegCommunityFileUpdateTool();
    RegOfficeFileUpdateTool();
    RegCommunityAccessUpdateTool();
    RegOfficeAccessUpdateTool();
    RegCommunityDevUpdateTool();
    RegOfficeDevUpdateTool();
    RegPersonalFileUpdateTool();
    RegSmartLockConfigUpdateTool();
}



