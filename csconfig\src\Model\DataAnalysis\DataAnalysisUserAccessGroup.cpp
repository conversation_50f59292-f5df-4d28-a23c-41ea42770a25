#include "DataAnalysisUserAccessGroup.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigOfficeAccessUpdate.h"
#include "UpdateConfigCommAccessUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "PersonalAccount.h"
#include "dbinterface/ProjectUserManage.h"
#include "DataAnalysisdbHandle.h"



static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "UserAccessGroup";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
    {DA_INDEX_USERACCESSGROUP_ACCOUNT, "Account", ItemChangeHandle},
    {DA_INDEX_USERACCESSGROUP_SCHEDULERTYPE, "SchedulerType", ItemChangeHandle},
    {DA_INDEX_USERACCESSGROUP_DATEFLAG, "DateFlag", ItemChangeHandle},
    {DA_INDEX_USERACCESSGROUP_BEGINTIME, "BeginTime", ItemChangeHandle},
    {DA_INDEX_USERACCESSGROUP_ENDTIME, "EndTime", ItemChangeHandle},
    {DA_INDEX_USERACCESSGROUP_STARTTIME, "StartTime", ItemChangeHandle},
    {DA_INDEX_USERACCESSGROUP_STOPTIME, "StopTime", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string uid = data.GetIndex(DA_INDEX_USERACCESSGROUP_ACCOUNT);
    std::string mac = "";
    UserInfo user_info;
    memset(&user_info, 0, sizeof(user_info));
    if (dbhandle::DAInfo::GetUserInfoByUid(uid, user_info) != 0)
    {
        AK_LOG_INFO << local_table_name << " InsertHandle. User is null, uid=" << uid;
        return -1;
    }
    uint32_t mng_id = user_info.mng_id;
    uint32_t project_type = data.GetProjectType();

    uint32_t change_type = WEB_COMM_ADD_USER_ACCESSGROUP;
    uint32_t office_change_type = WEB_OFFICE_ADD_USER_ACCESSGROUP;

    if (project_type == project::OFFICE)
    {   
        //办公
        AK_LOG_INFO << local_table_name << " InsertHandle. office change type=" << office_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(office_change_type) << " node= " << uid
                << " office_id= " << mng_id << " mac= " << mac;
        UCOfficeAccessUpdatePtr ptr = std::make_shared<UCOfficeAccessUpdate>(office_change_type, mng_id, mac, uid);
        context.AddUpdateConfigInfo(UPDATE_OFFICE_ACCESS_UPDATE, ptr);
    }
    else 
    {
        //社区
        AK_LOG_INFO << local_table_name << " InsertHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
                << " community_id= " << mng_id << " mac= " << mac;
        UCCommunityAccessUpdatePtr ptr = std::make_shared<UCCommunityAccessUpdate>(change_type, mng_id, mac, uid);
        context.AddUpdateConfigInfo(UPDATE_COMM_ACCESS_UPDATE, ptr);
    }    
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //只有在删除用户时删除
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    if (data.IsIndexChange(DA_INDEX_USERACCESSGROUP_SCHEDULERTYPE)
        || data.IsIndexChange(DA_INDEX_USERACCESSGROUP_DATEFLAG)
        || data.IsIndexChange(DA_INDEX_USERACCESSGROUP_BEGINTIME)
        || data.IsIndexChange(DA_INDEX_USERACCESSGROUP_ENDTIME)
        || data.IsIndexChange(DA_INDEX_USERACCESSGROUP_STARTTIME)
        || data.IsIndexChange(DA_INDEX_USERACCESSGROUP_STOPTIME))
    {
        std::string uid = data.GetIndex(DA_INDEX_USERACCESSGROUP_ACCOUNT);
        std::string mac = "";
        UserInfo user_info;
        memset(&user_info, 0, sizeof(user_info));
        if (dbhandle::DAInfo::GetUserInfoByUid(uid, user_info) != 0)
        {
            AK_LOG_INFO << local_table_name << " InsertHandle. User is null, uid=" << uid;
            return -1;
        }
        uint32_t mng_id = user_info.mng_id;
        uint32_t project_type = data.GetProjectType();

        uint32_t change_type = WEB_COMM_MODIFY_USER_ACCESSGROUP;
        uint32_t office_change_type = WEB_OFFICE_MODIFY_USER_ACCESSGROUP;

        if (project_type == project::OFFICE)
        {   
            AK_LOG_INFO << local_table_name << " UpdateHandle. office change type=" << office_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(office_change_type) << " node= " << uid
                << " office_id= " << mng_id << " mac= " << mac;
            UCOfficeAccessUpdatePtr ptr = std::make_shared<UCOfficeAccessUpdate>(office_change_type, mng_id, mac, uid);
            context.AddUpdateConfigInfo(UPDATE_OFFICE_ACCESS_UPDATE, ptr);
        }
        else 
        {
            AK_LOG_INFO << local_table_name << " UpdateHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
                << " community_id= " << mng_id << " mac= " << mac;
            UCCommunityAccessUpdatePtr ptr = std::make_shared<UCCommunityAccessUpdate>(change_type, mng_id, mac, uid);
            context.AddUpdateConfigInfo(UPDATE_COMM_ACCESS_UPDATE, ptr);
        }
    }
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaUserAccessGroupHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);

}






