#include "DataAnalysisUserAccessGroupDevice.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigOfficeAccessUpdate.h"
#include "UpdateConfigCommAccessUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "PersonalAccount.h"
#include "DeviceSetting.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/ProjectUserManage.h"
#include "UpdateConfigCommFileUpdate.h"




static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);


static  DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "UserAccessGroupDevice";
static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/
    {DA_INDEX_USERACCESSGROUPDEVICE_GROUPID, "UserAccessGroupID", ItemChangeHandle},
    {DA_INDEX_USERACCESSGROUPDEVICE_MAC, "MAC", ItemChangeHandle},
    {DA_INDEX_USERACCESSGROUPDEVICE_RELAY, "Relay", ItemChangeHandle},
    {DA_INDEX_USERACCESSGROUPDEVICE_SECURITYRELAY, "SecurityRelay", ItemChangeHandle},
    {DA_INDEX_INSERT, "", InsertHandle},
    {DA_INDEX_DELETE, "", DeleteHandle},
    {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string mac = data.GetIndex(DA_INDEX_USERACCESSGROUPDEVICE_MAC);
    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    if (0 != dbinterface::ResidentDevices::GetMacDev(mac, dev))
    {
        AK_LOG_INFO << local_table_name << " UpdateHandle. Mac is null, mac=" << mac;
        return -1;
    }    
    uint32_t mng_id = dev.project_mng_id;
    std::string uid = dev.node;
    uint32_t project_type = data.GetProjectType();

    uint32_t change_type = WEB_COMM_MODIFY_USER_ACCESSGROUP_DEVICE;
    uint32_t office_change_type = WEB_OFFICE_MODIFY_USER_ACCESSGROUP_DEVICE;

    uint32_t ag_id = data.GetIndexAsInt(DA_INDEX_USERACCESSGROUPDEVICE_GROUPID);
    dbinterface::ProjectUserManage::UpdateDataVersionByUserAccessGroupID(ag_id);

    if (project_type == project::OFFICE)
    {   
        //办公
        AK_LOG_INFO << local_table_name << " CommonChangeHandle. office change type=" << office_change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(office_change_type) << " node= " << uid
                << " office_id= " << mng_id << " mac= " << mac;
        UCOfficeAccessUpdatePtr ptr = std::make_shared<UCOfficeAccessUpdate>(office_change_type, mng_id, mac, uid);
        context.AddUpdateConfigInfo(UPDATE_OFFICE_ACCESS_UPDATE, ptr);
    }
    else 
    {
        //社区
        AK_LOG_INFO << local_table_name << " CommonChangeHandle. community change type=" << change_type << ". change type name = " << MsgIdToMsgName::GetConfigChangeTypeName(change_type) << " node= " << uid
                << " community_id= " << mng_id << " mac= " << mac;
        UCCommunityAccessUpdatePtr ptr = std::make_shared<UCCommunityAccessUpdate>(change_type, mng_id, mac, uid);
        context.AddUpdateConfigInfo(UPDATE_COMM_ACCESS_UPDATE, ptr);

        UCCommunityFileUpdatePtr fileptr = std::make_shared<UCCommunityFileUpdate>(WEB_COMM_UPDATE_MAC_CONFIG, mng_id, dev.unit_id, mac, uid);
        context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, fileptr);
    }
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //只有在新增设备时插入
    CommonChangeHandle(data, context);
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    //只有在删除设备时删除
    CommonChangeHandle(data, context);
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    CommonChangeHandle(data, context);
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}


void RegDaUserAccessGroupDeviceHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);

    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey); 
    RegDataAnalysisDBHandler(local_table_name, ptr);

}






