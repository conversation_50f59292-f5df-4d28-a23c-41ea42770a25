#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "DataAnalysis.h"
#include "DataAnalysisdbHandle.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisVideoStorage.h"
#include "UpdateConfigCommDevUpdate.h"
#include "UpdateConfigCommFileUpdate.h"
#include "UpdateConfigCommAccessUpdate.h"
#include "UpdateConfigPersonalFileUpdate.h"
#include "dbinterface/Account.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"


static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "VideoStorage"; 

enum DAVideoStorageIndex{
    DA_INDEX_VIDEO_STORAGE_ID,
    DA_INDEX_VIDEO_STORAGE_UUID,
    DA_INDEX_VIDEO_STORAGE_INSTALLERUUID,
    DA_INDEX_VIDEO_STORAGE_ACCOUNTUUID,
    DA_INDEX_VIDEO_STORAGE_PERSONALACCOUNTUUID,
    DA_INDEX_VIDEO_STORAGE_PROJECTTYPE,
    DA_INDEX_VIDEO_STORAGE_ISALLDEVICE,
    DA_INDEX_VIDEO_STORAGE_ISTRIALED,
    DA_INDEX_VIDEO_STORAGE_ISENABLE,
    DA_INDEX_VIDEO_STORAGE_PLANTYPE,
    DA_INDEX_VIDEO_STORAGE_STORAGEDAYS,
    DA_INDEX_VIDEO_STORAGE_DEVICESLIMITNUM,
    DA_INDEX_VIDEO_STORAGE_ISENABLECALLAUDIO,
    DA_INDEX_VIDEO_STORAGE_RBACDATAGROUPUUID,
    DA_INDEX_VIDEO_STORAGE_VERSION,
    DA_INDEX_VIDEO_STORAGE_EXPIRETIME,
    DA_INDEX_VIDEO_STORAGE_CREATETIME,
    DA_INDEX_VIDEO_STORAGE_UPDATETIME,
};

static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/ 
   {DA_INDEX_VIDEO_STORAGE_ID, "ID", ItemChangeHandle},
   {DA_INDEX_VIDEO_STORAGE_UUID, "UUID", ItemChangeHandle},
   {DA_INDEX_VIDEO_STORAGE_INSTALLERUUID, "InstallerUUID", ItemChangeHandle},
   {DA_INDEX_VIDEO_STORAGE_ACCOUNTUUID, "AccountUUID", ItemChangeHandle},
   {DA_INDEX_VIDEO_STORAGE_PERSONALACCOUNTUUID, "PersonalAccountUUID", ItemChangeHandle},
   {DA_INDEX_VIDEO_STORAGE_PROJECTTYPE, "ProjectType", ItemChangeHandle},
   {DA_INDEX_VIDEO_STORAGE_ISALLDEVICE, "IsAllDevice", ItemChangeHandle},
   {DA_INDEX_VIDEO_STORAGE_ISTRIALED, "IsTrialed", ItemChangeHandle},
   {DA_INDEX_VIDEO_STORAGE_ISENABLE, "IsEnable", ItemChangeHandle},
   {DA_INDEX_VIDEO_STORAGE_PLANTYPE, "PlanType", ItemChangeHandle},
   {DA_INDEX_VIDEO_STORAGE_STORAGEDAYS, "StorageDays", ItemChangeHandle},
   {DA_INDEX_VIDEO_STORAGE_DEVICESLIMITNUM, "DevicesLimitNum", ItemChangeHandle},
   {DA_INDEX_VIDEO_STORAGE_ISENABLECALLAUDIO, "IsEnableCallAudio", ItemChangeHandle},
   {DA_INDEX_VIDEO_STORAGE_RBACDATAGROUPUUID, "RBACDataGroupUUID", ItemChangeHandle},
   {DA_INDEX_VIDEO_STORAGE_VERSION, "Version", ItemChangeHandle},
   {DA_INDEX_VIDEO_STORAGE_EXPIRETIME, "ExpireTime", ItemChangeHandle},
   {DA_INDEX_VIDEO_STORAGE_CREATETIME, "CreateTime", ItemChangeHandle},
   {DA_INDEX_VIDEO_STORAGE_UPDATETIME, "UpdateTime", ItemChangeHandle},
   {DA_INDEX_INSERT, "", InsertHandle},
   {DA_INDEX_DELETE, "", DeleteHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    if (data.IsIndexChange(DA_INDEX_VIDEO_STORAGE_ISENABLE)
     || data.IsIndexChange(DA_INDEX_VIDEO_STORAGE_ISENABLECALLAUDIO)
     || data.IsIndexChange(DA_INDEX_VIDEO_STORAGE_EXPIRETIME) // super修改过期时间 需要刷配置
     )
    {
        std::string mac;
        std::string node;
        uint32_t unit_id;
        uint32_t change_type;
        int project_type = data.GetIndexAsInt(DA_INDEX_VIDEO_STORAGE_PROJECTTYPE);
        
        if (project_type == (int)VideoStorageProjectType::PERSONAL)
        {
            std::string personal_acccount_uuid = data.GetIndex(DA_INDEX_VIDEO_STORAGE_PERSONALACCOUNTUUID);
            if (0 == dbinterface::ResidentPersonalAccount::GetAccountByUUID(personal_acccount_uuid, node)) 
            {
                change_type = WEB_PER_NODE_UPDATE;
                UCPersonalFileUpdatePtr fileptr = std::make_shared<UCPersonalFileUpdate>(WEB_PER_NODE_UPDATE, mac, node);
                context.AddUpdateConfigInfo(UPDATE_PERSONAL_FILE_UPDATE, fileptr);
                AK_LOG_INFO << local_table_name << " UpdateHandle. personal change type = " << change_type << ", node = " << node << ", mac= " << mac;
            }
        }
        else if (project_type == (int)VideoStorageProjectType::COMMUNITY)
        {
            std::string project_uuid = data.GetIndex(DA_INDEX_VIDEO_STORAGE_ACCOUNTUUID);
            dbinterface::AccountInfo account;
            if (0 == dbinterface::Account::GetAccountByUUID(project_uuid, account)) 
            {
                change_type = WEB_COMM_UPDATE_CONFIG_AND_CONTACT;
                UCCommunityFileUpdatePtr fileptr = std::make_shared<UCCommunityFileUpdate>(change_type, account.id, unit_id, mac);
                context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, fileptr);
                AK_LOG_INFO << local_table_name << " UpdateHandle. community change type = " << change_type << ", mng_id = " << account.id;
            }
        }
    }

    return 0;
}

static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaVideoStorageHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}

