#include "DataAnalysisVideoStorageDevice.h"
#include "DataAnalysisTableParse.h"
#include "DataAnalysisControl.h"
#include "DataAnalysisContext.h"
#include "DataAnalysis.h"
#include <string.h>
#include <memory>
#include "UpdateConfigCommFileUpdate.h"
#include "UpdateConfigCommAccessUpdate.h"
#include "UpdateConfigPersonalFileUpdate.h"
#include "UpdateConfigCommDevUpdate.h"
#include "AkLogging.h"
#include "AkcsWebMsgSt.h"
#include "AkcsCommonDef.h"
#include "DataAnalysisdbHandle.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"


static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);

static DataAnalysisColumnList local_detect_key;
static const std::string local_table_name = "VideoStorageDevice";

enum DAVideoStorageDeviceIndex{
    DA_INDEX_VIDEO_STORAGE_DEVICE_ID,
    DA_INDEX_VIDEO_STORAGE_DEVICE_UUID,
    DA_INDEX_VIDEO_STORAGE_DEVICE_INSTALLERUUID,
    DA_INDEX_VIDEO_STORAGE_DEVICE_ACCOUNTUUID,
    DA_INDEX_VIDEO_STORAGE_DEVICE_PERSONALACCOUNTUUID,
    DA_INDEX_VIDEO_STORAGE_DEVICE_VIDEOSTORAGEUUID,
    DA_INDEX_VIDEO_STORAGE_DEVICE_DEVICESUUID,
    DA_INDEX_VIDEO_STORAGE_DEVICE_CREATETIME,
    DA_INDEX_VIDEO_STORAGE_DEVICE_UPDATETIME,
};

static DataAnalysisChangeHandle da_change_handle[] = {
    /*单个变化的检测, 主要用于缓存变化/数据清理*/ 
   {DA_INDEX_VIDEO_STORAGE_DEVICE_ID, "ID", ItemChangeHandle},
   {DA_INDEX_VIDEO_STORAGE_DEVICE_UUID, "UUID", ItemChangeHandle},
   {DA_INDEX_VIDEO_STORAGE_DEVICE_INSTALLERUUID, "InstallerUUID", ItemChangeHandle},
   {DA_INDEX_VIDEO_STORAGE_DEVICE_ACCOUNTUUID, "AccountUUID", ItemChangeHandle},
   {DA_INDEX_VIDEO_STORAGE_DEVICE_PERSONALACCOUNTUUID, "PersonalAccountUUID", ItemChangeHandle},
   {DA_INDEX_VIDEO_STORAGE_DEVICE_VIDEOSTORAGEUUID, "VideoStorageUUID", ItemChangeHandle},
   {DA_INDEX_VIDEO_STORAGE_DEVICE_DEVICESUUID, "DevicesUUID", ItemChangeHandle},
   {DA_INDEX_VIDEO_STORAGE_DEVICE_CREATETIME, "CreateTime", ItemChangeHandle},
   {DA_INDEX_VIDEO_STORAGE_DEVICE_UPDATETIME, "UpdateTime", ItemChangeHandle},
   {DA_INDEX_INSERT, "", InsertHandle},
   {DA_INDEX_DELETE, "", DeleteHandle},
   {DA_INDEX_UPDATE, "", UpdateHandle}
};

static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string dev_uuid = data.GetIndex(DA_INDEX_VIDEO_STORAGE_DEVICE_DEVICESUUID);
    std::string project_uuid = data.GetIndex(DA_INDEX_VIDEO_STORAGE_DEVICE_ACCOUNTUUID);
    std::string personal_account_uuid = data.GetIndex(DA_INDEX_VIDEO_STORAGE_DEVICE_PERSONALACCOUNTUUID);

    ResidentDev dev;
    if (0 != dbinterface::ResidentDevices::GetUUIDDev(dev_uuid, dev) 
     && 0 != dbinterface::ResidentPerDevices::GetUUIDDev(dev_uuid, dev))
    {
        return -1;
    }
    
    uint32_t change_type;
    if (!personal_account_uuid.empty())
    {
        change_type = WEB_PER_NODE_UPDATE;
        UCPersonalFileUpdatePtr fileptr = std::make_shared<UCPersonalFileUpdate>(change_type, dev.mac, dev.node);
        context.AddUpdateConfigInfo(UPDATE_PERSONAL_FILE_UPDATE, fileptr);

        AK_LOG_INFO << local_table_name << " CommonChangeHandle. personal change type = " << change_type << ", node = " << dev.node << ", mac= " << dev.mac;
    }
    else if (!project_uuid.empty())
    {
        change_type = WEB_COMM_UPDATE_CONFIG_AND_CONTACT;
        UCCommunityFileUpdatePtr fileptr = std::make_shared<UCCommunityFileUpdate>(change_type, dev.project_mng_id, dev.unit_id, dev.mac);
        context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, fileptr);
        
        AK_LOG_INFO << local_table_name << " CommonChangeHandle. community change type = " << change_type << ", mac = " << dev.mac << ", mng_id = " << dev.project_mng_id;
    }
	
    return 0;
}

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    CommonChangeHandle(data, context);
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    CommonChangeHandle(data, context);
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    CommonChangeHandle(data, context);
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}

void RegDaVideoStorageDeviceHandler()
{
    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);
    RegDaSort(local_detect_key, da_change_handle, len);
    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);
    RegDataAnalysisDBHandler(local_table_name, ptr);
}

