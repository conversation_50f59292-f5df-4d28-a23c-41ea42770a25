#include <sstream>
#include "dbinterface/InterfaceComm.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "DataAnalysisdbHandle.h"
#include <string.h>
#include "AkLogging.h"
#include "AkcsCommonDef.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/CommunityUnit.h"
#include "util.h"

namespace dbhandle
{

int DAInfo::GetUserInfoByUid(const std::string& struser, UserInfo &user_info)
{
    int parent_id = 0;
    ResidentPerAccount account;
    if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(struser, account))
    {
        user_info.role = account.role;
        user_info.unit_id = account.unit_id;
        parent_id = account.parent_id;
    }
    else
    {
        return -1;
    }
    
    if (user_info.role == ACCOUNT_ROLE_PERSONNAL_MAIN || user_info.role == ACCOUNT_ROLE_COMMUNITY_MAIN
        || IsOfficeRole(user_info.role)
        || user_info.role == ACCOUNT_ROLE_COMMUNITY_PM)
    {
        user_info.mng_id = parent_id;
        Snprintf(user_info.node, sizeof(user_info.node), struser.c_str());
        Snprintf(user_info.node_uuid, sizeof(user_info.node_uuid), account.uuid);
    }
    else if (user_info.role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT || user_info.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT)
    {
        ResidentPerAccount main_account;
        if (0 == dbinterface::ResidentPersonalAccount::GetUUIDAccount(account.parent_uuid, main_account))
        {
            user_info.mng_id = main_account.parent_id;
            Snprintf(user_info.node, sizeof(user_info.node), main_account.account);
            Snprintf(user_info.node_uuid, sizeof(user_info.node_uuid), main_account.uuid);
        }
    }

    return 0;
}

int DAInfo::GetUserInfoByUUID(const std::string& uuid, UserInfo &user_info)
{
    ResidentPerAccount account_info;
    memset(&account_info, 0, sizeof(account_info));
    if (0 != dbinterface::ResidentPersonalAccount::GetUUIDAccount(uuid, account_info))
    {
        return -1;
    }
    user_info.role = account_info.role;
    user_info.unit_id = account_info.unit_id;
    Snprintf(user_info.account, sizeof(user_info.account), account_info.account);   

    if (user_info.role == ACCOUNT_ROLE_PERSONNAL_MAIN || user_info.role == ACCOUNT_ROLE_COMMUNITY_MAIN
        || IsOfficeRole(user_info.role)
        || user_info.role == ACCOUNT_ROLE_COMMUNITY_PM)
    {
        user_info.mng_id = account_info.parent_id;
        Snprintf(user_info.node, sizeof(user_info.node), user_info.account);
        Snprintf(user_info.node_uuid, sizeof(user_info.node_uuid), account_info.uuid);
    }
    else if (user_info.role == ACCOUNT_ROLE_PERSONNAL_ATTENDANT || user_info.role == ACCOUNT_ROLE_COMMUNITY_ATTENDANT)
    {
        //若是从账号，则通过主账号获取社区id
        ResidentPerAccount account_master;
        memset(&account_master, 0, sizeof(account_master));
        if (0 != dbinterface::ResidentPersonalAccount::GetUUIDAccount(account_info.parent_uuid, account_master))
        {
            return -1;
        }
        user_info.mng_id = account_master.parent_id;   
        Snprintf(user_info.node, sizeof(user_info.node), account_master.account); 
        Snprintf(user_info.node_uuid, sizeof(user_info.node_uuid), account_master.uuid);
    }

    return 0;
}

int DAInfo::GetUserInfoByRoomID(int room_id, UserInfo &user_info)
{
    ResidentPerAccount account;
    if (0 == dbinterface::ResidentPersonalAccount::GetRoomIDAccount(room_id, account))
    {
        user_info.mng_id = account.parent_id;
        Snprintf(user_info.node, sizeof(user_info.node), account.account);
        return 0;
    }
    return -1;
}


}

