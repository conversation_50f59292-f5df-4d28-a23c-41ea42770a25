#include "UpdateConfigCommAccessUpdate.h"
#include "DataAnalysisUpdateConfig.h"
#include <assert.h>
#include <memory.h>
#include "AkLogging.h"
#include "dbinterface/FeaturePlan.h"
#include "AKCSView.h"
#include "CommConfigHandle.h"
#include "FileUpdateControl.h"
#include "AkcsCommonDef.h"
#include "CommonHandle.h"
#include "AkcsWebPduBase.h"
#include "AkcsMsgDef.h"
#include "UnixSocketControl.h"
#include "AK.Adapt.pb.h"
#include "SnowFlakeGid.h"
#include "SpecialTubeHandle.h"

extern int g_special_tube;


UCCommunityAccessUpdate::UCCommunityAccessUpdate(uint32_t change_type, uint32_t comm_id, 
const std::string &mac, const std::string &uid, uint32_t ag_id)
:change_type_(change_type),comm_id_(comm_id),mac_(mac),uid_(uid),ag_id_(ag_id)
{
    
}

UCCommunityAccessUpdate::UCCommunityAccessUpdate(uint32_t change_type, uint32_t comm_id, 
const std::string &mac, const std::string &uid)
:change_type_(change_type),comm_id_(comm_id),mac_(mac),uid_(uid)
{
    
}

UCCommunityAccessUpdate::~UCCommunityAccessUpdate()
{

}

int UCCommunityAccessUpdate::SetMac(const std::string &mac)
{
    mac_ = mac;
    return 0;
}

int UCCommunityAccessUpdate::SetUid(const std::string &uid)
{
    uid_ = uid;
    return 0;
}

int UCCommunityAccessUpdate::SetAgid(uint32_t ag_id)
{
    ag_id_ = ag_id;
    return 0;
}


int UCCommunityAccessUpdate::Handler(UpdateConfigDataPtr msg)
{
    UCCommunityAccessUpdatePtr ptr =std::static_pointer_cast<UCCommunityAccessUpdate>(msg);

    AK::Adapt::WebCommAccessModifyNotify new_msg;
    new_msg.set_community_id(ptr->comm_id_);
    new_msg.set_ag_id(ptr->ag_id_);
    new_msg.set_change_type(ptr->change_type_);
    new_msg.set_node(ptr->uid_);
    new_msg.set_already_check(0);
    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    new_msg.set_trace_id(traceid);
    if (ptr->mac_.size() > 0)
    {
        new_msg.add_mac_list(ptr->mac_);
    }
    
    CAkcsWebPdu web_pdu;
    web_pdu.SetMsgBody(&new_msg);
    web_pdu.SetMsgID(MSG_S2C_DATAANALYSIS_COMM_ACCESS_FILE_UPDATE);
    web_pdu.SetProjectType(project::RESIDENCE);    
    
    GetFileUpdateContorlInstance()->OnCommunityAccessGroupFileUpdate(web_pdu.GetBuffer(), web_pdu.GetLength());
}

std::string UCCommunityAccessUpdate::Identify(UpdateConfigDataPtr msg)
{
    std::stringstream identify;
    UCCommunityAccessUpdatePtr ptr =std::static_pointer_cast<UCCommunityAccessUpdate>(msg);
    identify << "UCCommunityAccessUpdate " << ptr->change_type_ <<" "<< ptr->comm_id_ <<" "<< ptr->uid_ <<" "<< ptr->mac_ <<" "<< ptr->ag_id_;
    return identify.str();
}


void RegCommunityAccessUpdateTool()
{
    RegUpdateConfigTool(UPDATE_COMM_ACCESS_UPDATE, UCCommunityAccessUpdate::Handler, UCCommunityAccessUpdate::Identify);
}



