#include "UpdateConfigCommDevUpdate.h"
#include "DataAnalysisUpdateConfig.h"
#include <assert.h>
#include <memory.h>
#include "AkLogging.h"
#include "dbinterface/FeaturePlan.h"
#include "AKCSView.h"
#include "CommConfigHandle.h"
#include "OfficeFileUpdateControl.h"
#include "FileUpdateControl.h"


UCCommunityDevUpdate::UCCommunityDevUpdate(uint32_t change_type, const std::vector<std::string> &macs)
:change_type_(change_type),macs_(macs)
{
    
}

UCCommunityDevUpdate::~UCCommunityDevUpdate()
{

}

int UCCommunityDevUpdate::SetMacs(const std::vector<std::string> &macs)
{
    macs_ = macs;
    return 0;
}

int UCCommunityDevUpdate::Handler(UpdateConfigDataPtr msg)
{
    UCCommunityDevUpdatePtr ptr =std::static_pointer_cast<UCCommunityDevUpdate>(msg);
    GetFileUpdateContorlInstance()->OnDevUpdateCommonHandle(ptr->change_type_, ptr->macs_);
}

std::string UCCommunityDevUpdate::Identify(UpdateConfigDataPtr msg)
{
    std::stringstream identify;
    UCCommunityDevUpdatePtr ptr =std::static_pointer_cast<UCCommunityDevUpdate>(msg);
    identify << "UCCommunityDevUpdate " << ptr->change_type_ << " ";
    for (auto &mac : ptr->macs_)
    {
        identify << mac << " ";
    }

    return identify.str();
}

void RegCommunityDevUpdateTool()
{
    RegUpdateConfigTool(UPDATE_COMM_DEV_UPDATE, UCCommunityDevUpdate::Handler, UCCommunityDevUpdate::Identify);
}



