#ifndef __CSADAPT_UPDATECONFIG_COMM_DEVUPDATE_H__
#define __CSADAPT_UPDATECONFIG_COMM_DEVUPDATE_H__

#include <vector>
#include <string>
#include <map>
#include <list>
#include <memory>
#include "UpdateConfigDef.h"
#include "DataAnalysisUpdateConfig.h"
#include "BasicDefine.h"
class UCCommunityDevUpdate
{
public:
    UCCommunityDevUpdate(uint32_t change_type, const std::vector<std::string> &macs);
    ~UCCommunityDevUpdate();
    static int Handler(UpdateConfigDataPtr msg);
    static std::string Identify(UpdateConfigDataPtr msg);
    int SetMacs(const std::vector<std::string> &macs);
   
private:
    uint32_t change_type_;
    std::vector<std::string> macs_;
};

typedef std::shared_ptr<UCCommunityDevUpdate> UCCommunityDevUpdatePtr;
void RegCommunityDevUpdateTool();


#endif //__CSADAPT_UPDATECONFIG_COMM_DEVUPDATE_H__