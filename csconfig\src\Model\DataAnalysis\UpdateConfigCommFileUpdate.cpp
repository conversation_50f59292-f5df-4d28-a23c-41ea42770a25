#include "UpdateConfigCommFileUpdate.h"
#include "DataAnalysisUpdateConfig.h"
#include <assert.h>
#include <memory.h>
#include "AkLogging.h"
#include "dbinterface/FeaturePlan.h"
#include "AKCSView.h"
#include "CommConfigHandle.h"
#include "FileUpdateControl.h"
#include "AkcsCommonDef.h"
#include "CommonHandle.h"
#include "AkcsWebPduBase.h"
#include "AkcsMsgDef.h"
#include "UnixSocketControl.h"
#include "AK.Adapt.pb.h"
#include "SnowFlakeGid.h"
#include "SpecialTubeHandle.h"

extern int g_special_tube;

UCCommunityFileUpdate::UCCommunityFileUpdate(uint32_t change_type, uint32_t mng_id, uint32_t unit_id, 
const std::string   &mac, const std::string   &uid)
:change_type_(change_type),mng_id_(mng_id),unit_id_(unit_id),mac_(mac),uid_(uid)
{
    
}


UCCommunityFileUpdate::~UCCommunityFileUpdate()
{

}


int UCCommunityFileUpdate::SetMac(const std::string &mac)
{
    mac_ = mac;
    return 0;
}

int UCCommunityFileUpdate::SetUid(const std::string &uid)
{
    uid_ = uid;
    return 0;
}


int UCCommunityFileUpdate::Handler(UpdateConfigDataPtr msg)
{
    UCCommunityFileUpdatePtr ptr =std::static_pointer_cast<UCCommunityFileUpdate>(msg);

    AK::Adapt::WebCommunityModifyNotify new_msg;
    new_msg.set_community_id(ptr->mng_id_);
    new_msg.set_unit_id(ptr->unit_id_);
    new_msg.set_change_type(ptr->change_type_);
    new_msg.set_node(ptr->uid_);
    new_msg.set_already_check(0);   
    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    new_msg.set_trace_id(traceid);
    if (ptr->mac_.size() > 0)
    {
        new_msg.add_mac_list(ptr->mac_);
    }
    
    CAkcsWebPdu web_pdu;
    web_pdu.SetMsgBody(&new_msg);
    web_pdu.SetMsgID(MSG_P2A_NOTIFY_COMMUNITY_MESSAGE);//这个id没用到 因为下面按社区变化处理了
    web_pdu.SetProjectType(project::RESIDENCE);
    
    GetFileUpdateContorlInstance()->OnCommunityConfigFileUpdate(web_pdu.GetBuffer(), web_pdu.GetLength());
}

std::string UCCommunityFileUpdate::Identify(UpdateConfigDataPtr msg)
{
    std::stringstream identify;
    UCCommunityFileUpdatePtr ptr =std::static_pointer_cast<UCCommunityFileUpdate>(msg);
    identify << "UCCommunityFileUpdate " << ptr->change_type_ <<" "<< ptr->mng_id_ <<" "<< ptr->unit_id_ <<" "<< ptr->uid_ << " " <<  ptr->mac_;
    return identify.str();
}

void RegCommunityFileUpdateTool()
{
    RegUpdateConfigTool(UPDATE_COMM_FILE_UPDATE, UCCommunityFileUpdate::Handler, UCCommunityFileUpdate::Identify);
}



