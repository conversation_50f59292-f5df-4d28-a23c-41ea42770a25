#ifndef __CSADAPT_UPDATECONFIG_COMM_FILEUPDATE_H__
#define __CSADAPT_UPDATECONFIG_COMM_FILEUPDATE_H__

#include <vector>
#include <string>
#include <map>
#include <list>
#include <memory>
#include "UpdateConfigDef.h"
#include "DataAnalysisUpdateConfig.h"
#include "BasicDefine.h"
class UCCommunityFileUpdate
{
public:
    UCCommunityFileUpdate(uint32_t change_type, uint32_t mng_id, uint32_t unit_id, const std::string &mac, const std::string& uid = "");
    ~UCCommunityFileUpdate();
    static int Handler(UpdateConfigDataPtr msg);
    static std::string Identify(UpdateConfigDataPtr msg);

    int SetMac(const std::string   &mac);
    int SetUid(const std::string   &uid);
   
private:
    uint32_t change_type_;
    uint32_t mng_id_;
    uint32_t unit_id_;
    std::string uid_;
    std::string mac_;
   
};

typedef std::shared_ptr<UCCommunityFileUpdate> UCCommunityFileUpdatePtr;
void RegCommunityFileUpdateTool();


#endif //__CSADAPT_UPDATECONFIG_OFFICE_FILEUPDATE_H__