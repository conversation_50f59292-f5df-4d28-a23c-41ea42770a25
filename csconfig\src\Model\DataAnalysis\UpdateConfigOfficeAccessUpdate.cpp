#include "UpdateConfigOfficeAccessUpdate.h"
#include "DataAnalysisUpdateConfig.h"
#include <assert.h>
#include <memory.h>
#include "AkLogging.h"
#include "dbinterface/FeaturePlan.h"
#include "AKCSView.h"
#include "CommConfigHandle.h"
#include "OfficeFileUpdateControl.h"
#include "FileUpdateControl.h"


UCOfficeAccessUpdate::UCOfficeAccessUpdate(uint32_t change_type, uint32_t office_id, 
const std::string &mac, const std::string &uid, uint32_t ag_id = 0)
:change_type_(change_type),office_id_(office_id),mac_(mac),uid_(uid),ag_id_(ag_id)
{
    
}

UCOfficeAccessUpdate::UCOfficeAccessUpdate(uint32_t change_type, uint32_t office_id, 
const std::string &mac, const std::string &uid)
:change_type_(change_type),office_id_(office_id),mac_(mac),uid_(uid)
{
    
}

UCOfficeAccessUpdate::~UCOfficeAccessUpdate()
{

}

int UCOfficeAccessUpdate::SetOfficeID(uint32_t offce_id)
{
    office_id_ = offce_id;
    return 0;
}

int UCOfficeAccessUpdate::SetMac(const std::string &mac)
{
    mac_ = mac;
    return 0;
}

int UCOfficeAccessUpdate::SetUid(const std::string &uid)
{
    uid_ = uid;
    return 0;
}

int UCOfficeAccessUpdate::SetAgid(uint32_t ag_id)
{
    ag_id_ = ag_id;
    return 0;
}


int UCOfficeAccessUpdate::Handler(UpdateConfigDataPtr msg)
{
    UCOfficeAccessUpdatePtr ptr =std::static_pointer_cast<UCOfficeAccessUpdate>(msg);

    std::set<std::string> macs;
    if (!ptr->mac_.empty())
    {
        macs.insert(ptr->mac_);
    }
    OfficeFileUpdateControl::Instance()->OnOfficeAccessGroupHandle(ptr->change_type_, ptr->office_id_, ptr->uid_, macs, ptr->ag_id_);
    return 0;
}

std::string UCOfficeAccessUpdate::Identify(UpdateConfigDataPtr msg)
{
    std::stringstream identify;
    UCOfficeAccessUpdatePtr ptr =std::static_pointer_cast<UCOfficeAccessUpdate>(msg);
    identify << "UCOfficeAccessUpdate " << ptr->change_type_ <<" "<< ptr->office_id_ <<" "<< ptr->uid_ <<" "<< ptr->mac_ <<" "<< ptr->ag_id_;
    return identify.str();
}


void RegOfficeAccessUpdateTool()
{
    RegUpdateConfigTool(UPDATE_OFFICE_ACCESS_UPDATE, UCOfficeAccessUpdate::Handler, UCOfficeAccessUpdate::Identify);
}



