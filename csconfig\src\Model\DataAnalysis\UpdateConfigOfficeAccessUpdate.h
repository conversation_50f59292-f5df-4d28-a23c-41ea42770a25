#ifndef __CSADAPT_UPDATECONFIG_OFFICE_ACCESSUPDATE_H__
#define __CSADAPT_UPDATECONFIG_OFFICE_ACCESSUPDATE_H__

#include <vector>
#include <string>
#include <map>
#include <list>
#include <memory>
#include "UpdateConfigDef.h"
#include "DataAnalysisUpdateConfig.h"
#include "BasicDefine.h"
class UCOfficeAccessUpdate
{
public:
   UCOfficeAccessUpdate(uint32_t change_type, uint32_t office_id, const std::string &mac, const std::string &uid, uint32_t ag_id);
   UCOfficeAccessUpdate(uint32_t change_type, uint32_t office_id, const std::string &mac, const std::string &uid);
   ~UCOfficeAccessUpdate();
    static int Handler(UpdateConfigDataPtr msg);
    static std::string Identify(UpdateConfigDataPtr msg);
    int SetOfficeID(uint32_t office_id);
    int SetMac(const std::string   &mac);
    int SetUid(const std::string   &uid);
    int SetAgid(uint32_t ag_id);
   
private:
    uint32_t change_type_;
    uint32_t office_id_;
    uint32_t ag_id_ = 0;
    std::string uid_;
    std::string mac_;
   
};

typedef std::shared_ptr<UCOfficeAccessUpdate> UCOfficeAccessUpdatePtr;
void RegOfficeAccessUpdateTool();


#endif //__CSADAPT_UPDATECONFIG_OFFICE_ACCESSUPDATE_H__