#include "UpdateConfigOfficeDevUpdate.h"
#include "DataAnalysisUpdateConfig.h"
#include <assert.h>
#include <memory.h>
#include "AkLogging.h"
#include "dbinterface/FeaturePlan.h"
#include "AKCSView.h"
#include "CommConfigHandle.h"
#include "OfficeFileUpdateControl.h"
#include "FileUpdateControl.h"


UCOfficeDevUpdate::UCOfficeDevUpdate(uint32_t change_type, const std::vector<std::string> &macs)
:change_type_(change_type),macs_(macs)
{
    
}

UCOfficeDevUpdate::~UCOfficeDevUpdate()
{

}

int UCOfficeDevUpdate::SetMacs(const std::vector<std::string> &macs)
{
    macs_ = macs;
    return 0;
}

int UCOfficeDevUpdate::Handler(UpdateConfigDataPtr msg)
{
    UCOfficeDevUpdatePtr ptr =std::static_pointer_cast<UCOfficeDevUpdate>(msg);
    OfficeFileUpdateControl::Instance()->OnDevUpdateCommonHandle(ptr->change_type_, ptr->macs_);
    return 0;
}

std::string UCOfficeDevUpdate::Identify(UpdateConfigDataPtr msg)
{
    std::stringstream identify;
    UCOfficeDevUpdatePtr ptr =std::static_pointer_cast<UCOfficeDevUpdate>(msg);
    identify << "UCOfficeDevUpdate " << ptr->change_type_ << " ";
    for (auto &mac : ptr->macs_)
    {
        identify << mac << " ";
    }
    
    return identify.str();
}


void RegOfficeDevUpdateTool()
{
    RegUpdateConfigTool(UPDATE_OFFICE_DEV_UPDATE, UCOfficeDevUpdate::Handler, UCOfficeDevUpdate::Identify);
}



