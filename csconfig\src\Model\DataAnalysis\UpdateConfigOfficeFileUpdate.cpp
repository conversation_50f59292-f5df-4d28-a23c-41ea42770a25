#include "UpdateConfigOfficeFileUpdate.h"
#include "DataAnalysisUpdateConfig.h"
#include <assert.h>
#include <memory.h>
#include "AkLogging.h"
#include "dbinterface/FeaturePlan.h"
#include "AKCSView.h"
#include "CommConfigHandle.h"
#include "OfficeFileUpdateControl.h"


UCOfficeFileUpdate::UCOfficeFileUpdate(uint32_t change_type, uint32_t office_id, uint32_t department_id, 
const std::string   &mac, const std::string   &uid)
:change_type_(change_type),office_id_(office_id),department_id_(department_id),mac_(mac),uid_(uid)
{
    
}

UCOfficeFileUpdate::~UCOfficeFileUpdate()
{

}

int UCOfficeFileUpdate::SetOfficeID(uint32_t offce_id)
{
    office_id_ = offce_id;
    return 0;
}

int UCOfficeFileUpdate::SetDepartmentID(uint32_t department_id)
{
    department_id_ = department_id;
    return 0;
}

int UCOfficeFileUpdate::SetMac(const std::string &mac)
{
    mac_ = mac;
    return 0;
}

int UCOfficeFileUpdate::SetUid(const std::string &uid)
{
    uid_ = uid;
    return 0;
}


int UCOfficeFileUpdate::Handler(UpdateConfigDataPtr msg)
{
    UCOfficeFileUpdatePtr ptr =std::static_pointer_cast<UCOfficeFileUpdate>(msg);

    std::vector<std::string> macs;
    macs.push_back(ptr->mac_);
    OfficeFileUpdateControl::Instance()->OnOfficeFileUpdate(
        ptr->change_type_, ptr->office_id_, ptr->department_id_, ptr->uid_, macs);    
    return 0;
}

std::string UCOfficeFileUpdate::Identify(UpdateConfigDataPtr msg)
{
    std::stringstream identify;
    UCOfficeFileUpdatePtr ptr =std::static_pointer_cast<UCOfficeFileUpdate>(msg);
    identify << "UCOfficeFileUpdate " << ptr->change_type_ <<" "<< ptr->office_id_ <<" "<< ptr->department_id_ <<" "<< ptr->uid_ << " " <<  ptr->mac_;
    return identify.str();
}


void RegOfficeFileUpdateTool()
{
    RegUpdateConfigTool(UPDATE_OFFICE_FILE_UPDATE, UCOfficeFileUpdate::Handler, UCOfficeFileUpdate::Identify);
}



