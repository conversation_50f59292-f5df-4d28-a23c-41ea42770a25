#ifndef __CSADAPT_UPDATECONFIG_OFFICE_FILEUPDATE_H__
#define __CSADAPT_UPDATECONFIG_OFFICE_FILEUPDATE_H__

#include <vector>
#include <string>
#include <map>
#include <list>
#include <memory>
#include "UpdateConfigDef.h"
#include "DataAnalysisUpdateConfig.h"
#include "BasicDefine.h"
class UCOfficeFileUpdate
{
public:
   UCOfficeFileUpdate(uint32_t change_type, uint32_t office_id, uint32_t department_id, const std::string   &mac, const std::string   &uid);
   ~UCOfficeFileUpdate();
    static int Handler(UpdateConfigDataPtr msg);
    static std::string Identify(UpdateConfigDataPtr msg);
    int SetOfficeID(uint32_t offce_id);
    int SetDepartmentID(uint32_t department_id);
    int SetMac(const std::string   &mac);
    int SetUid(const std::string   &uid);
   
private:
    uint32_t change_type_;
    uint32_t office_id_;
    uint32_t department_id_;
    std::string uid_;
    std::string mac_;
   
};

typedef std::shared_ptr<UCOfficeFileUpdate> UCOfficeFileUpdatePtr;
void RegOfficeFileUpdateTool();


#endif //__CSADAPT_UPDATECONFIG_OFFICE_FILEUPDATE_H__