#include "UpdateConfigPayPlan.h"
#include "DataAnalysisUpdateConfig.h"
#include <assert.h>
#include <memory.h>
#include "AkLogging.h"
#include "dbinterface/FeaturePlan.h"
#include "AKCSView.h"
#include "CommConfigHandle.h"
#include "AK.Adapt.pb.h"
#include "CommonHandle.h"
#include "AkcsWebPduBase.h"
#include "AkcsMsgDef.h"
#include "UnixSocketControl.h"
#include "FileUpdateControl.h"
#include "AkcsCommonDef.h"
#include "SnowFlakeGid.h"
#include "SpecialTubeHandle.h"

extern int g_special_tube;

UCPayPlanMsg::UCPayPlanMsg(int type)
{
    update_type_ = type;
}

UCPayPlanMsg::~UCPayPlanMsg()
{

}

int UCPayPlanMsg::SetPlanID(uint32_t planid)
{
    plan_id_ = planid;
    return 0;
}

int UCPayPlanMsg::SetMngID(uint32_t mng_id)
{
    mng_id_ = mng_id;
    return 0;
}


int UCPayPlanMsg::Handler(UpdateConfigDataPtr msg)
{
    UCPayPlanMsgPtr ptr =std::static_pointer_cast<UCPayPlanMsg>(msg);
    if (ptr->update_type_ == UPDATE_TYPE::UPDATE_TYPE_PLAN_CHANGE)
    {
        AK_LOG_INFO << "community feature plan change.community id:" << ptr->mng_id_;
        CommonHandler(ptr->mng_id_, WEB_COMM_MODIFY_FEATURE_PLAN);
    }
    else if (ptr->update_type_ == UPDATE_TYPE::UPDATE_TYPE_PLAN_ITME_CHANGE)
    {
        AK_LOG_INFO << "feature plan itme change.plan id:" << ptr->plan_id_;
        dbinterface::FeaturePlan plan(ptr->plan_id_);

        std::vector<uint32_t> mng_list;
        plan.GetFeatureCommunityList(mng_list);
        for(auto mng : mng_list)
        {
            CommonHandler(mng, WEB_COMM_MODIFY_FEATURE_PLAN);
        }
    }
    else
    {
         
    }
	return 0;
}

int UCPayPlanMsg::CommonHandler(int mng_id, int change_type)
{
    AK::Adapt::WebCommunityModifyNotify new_msg;
    new_msg.set_community_id(mng_id);
    new_msg.set_unit_id(0);
    new_msg.set_change_type(change_type);
    new_msg.set_already_check(0);
    uint64_t traceid = AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId();
    new_msg.set_trace_id(traceid);
    
    CAkcsWebPdu web_pdu;
    web_pdu.SetMsgBody(&new_msg);
    web_pdu.SetMsgID(MSG_P2A_NOTIFY_COMMUNITY_MESSAGE);//这个id没用到 因为下面按社区变化处理了
    web_pdu.SetProjectType(project::RESIDENCE);
    
    GetFileUpdateContorlInstance()->OnCommunityConfigFileUpdate(web_pdu.GetBuffer(), web_pdu.GetLength());
}

std::string UCPayPlanMsg::Identify(UpdateConfigDataPtr msg)
{
    std::stringstream identify;
    UCPayPlanMsgPtr ptr =std::static_pointer_cast<UCPayPlanMsg>(msg);
    identify << "UCPayPlanMsg " << ptr->update_type_ <<" "<<  ptr->plan_id_ <<" "<< ptr->mng_id_ ;
    return identify.str();
}



void RegPayPlanTool()
{
    RegUpdateConfigTool(UPDATE_CONFIG_PAY_PLAN, UCPayPlanMsg::Handler, UCPayPlanMsg::Identify);
}



