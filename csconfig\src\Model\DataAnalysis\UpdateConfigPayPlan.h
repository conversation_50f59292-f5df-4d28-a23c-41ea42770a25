#ifndef __CSADAPT_UPDATECONFIG_FEATUREPLAN_H__
#define __CSADAPT_UPDATECONFIG_FEATUREPLAN_H__

#include <vector>
#include <string>
#include <map>
#include <list>
#include <memory>
#include "UpdateConfigDef.h"
#include "DataAnalysisUpdateConfig.h"
#include "BasicDefine.h"
class UCPayPlanMsg
{
public:
   enum UPDATE_TYPE{
        UPDATE_TYPE_PLAN_CHANGE = 1,//dis/小区切换高级功能方案
        UPDATE_TYPE_PLAN_ITME_CHANGE,//超级管理员 更新高级方案里的功能
   };
   UCPayPlanMsg(int update_type);
   ~UCPayPlanMsg();
   int SetPlanID(uint32_t planid);
   int SetMngID(uint32_t mng_id);
   static int Handler(UpdateConfigDataPtr msg);
   static int CommonHandler(int mng_id, int change_type);
   static std::string Identify(UpdateConfigDataPtr msg);
   
private:
   uint32_t plan_id_;
   uint32_t mng_id_;
   int update_type_;
};

typedef std::shared_ptr<UCPayPlanMsg> UCPayPlanMsgPtr;
void RegPayPlanTool();


#endif //__CSADAPT_UPDATECONFIG_FEATUREPLAN_H__