#include "UpdateConfigPersonalFileUpdate.h"
#include "DataAnalysisUpdateConfig.h"
#include <assert.h>
#include <memory.h>
#include "AkLogging.h"
#include "dbinterface/FeaturePlan.h"
#include "AKCSView.h"
#include "CommConfigHandle.h"
#include "OfficeFileUpdateControl.h"
#include "FileUpdateControl.h"
#include "PerFileUpdateControl.h"


UCPersonalFileUpdate::UCPersonalFileUpdate(uint32_t change_type, 
const std::string   &mac, const std::string   &uid)
:change_type_(change_type),mac_(mac),uid_(uid)
{
    
}


UCPersonalFileUpdate::~UCPersonalFileUpdate()
{

}

int UCPersonalFileUpdate::SetMac(const std::string &mac)
{
    mac_ = mac;
    return 0;
}

int UCPersonalFileUpdate::SetUid(const std::string &uid)
{
    uid_ = uid;
    return 0;
}


int UCPersonalFileUpdate::Handler(UpdateConfigDataPtr msg)
{
    UCPersonalFileUpdatePtr ptr =std::static_pointer_cast<UCPersonalFileUpdate>(msg);

    std::vector<std::string> macs;
    macs.push_back(ptr->mac_);
    GetPerFileUpdateContorlInstance()->PersonalFileHandle(ptr->change_type_, ptr->uid_, macs);
    return 0;
}

std::string UCPersonalFileUpdate::Identify(UpdateConfigDataPtr msg)
{
    std::stringstream identify;
    UCPersonalFileUpdatePtr ptr =std::static_pointer_cast<UCPersonalFileUpdate>(msg);
    identify << "UCPersonalFileUpdate " << ptr->change_type_ <<" "<< ptr->uid_ << " " <<  ptr->mac_;
    return identify.str();
}

void RegPersonalFileUpdateTool()
{
    RegUpdateConfigTool(UPDATE_PERSONAL_FILE_UPDATE, UCPersonalFileUpdate::Handler, UCPersonalFileUpdate::Identify);
}



