#ifndef __CSADAPT_UPDATECONFIG_PERSONAL_FILEUPDATE_H__
#define __CSADAPT_UPDATECONFIG_PERSONAL_FILEUPDATE_H__

#include <vector>
#include <string>
#include <map>
#include <list>
#include <memory>
#include "UpdateConfigDef.h"
#include "DataAnalysisUpdateConfig.h"
#include "BasicDefine.h"
class UCPersonalFileUpdate
{
public:
   UCPersonalFileUpdate(uint32_t change_type, const std::string &mac, const std::string &uid);
   ~UCPersonalFileUpdate();
    static int Handler(UpdateConfigDataPtr msg);
    static std::string Identify(UpdateConfigDataPtr msg);
    int SetMac(const std::string   &mac);
    int SetUid(const std::string   &uid);
   
private:
    uint32_t change_type_;
    std::string uid_;
    std::string mac_;
   
};

typedef std::shared_ptr<UCPersonalFileUpdate> UCPersonalFileUpdatePtr;
void RegPersonalFileUpdateTool();


#endif //__CSADAPT_UPDATECONFIG_OFFICE_FILEUPDATE_H__