#include "UpdateSmartLockConfig.h"
#include "DataAnalysisUpdateConfig.h"
#include <assert.h>
#include <memory.h>
#include "AkLogging.h"
#include "AKCSView.h"
#include "CommConfigHandle.h"
#include "FileUpdateControl.h"
#include "SmartLockUpdateControl.h"


UCSmartLockConfigUpdate::UCSmartLockConfigUpdate(uint32_t change_type, const std::string &uuid, const std::string &node, int project_type, int mng_id)
:change_type_(change_type), lock_uuid_(uuid), node_(node), project_type_(project_type), mng_id_(mng_id)
{
    
}


UCSmartLockConfigUpdate::~UCSmartLockConfigUpdate()
{

}


int UCSmartLockConfigUpdate::SetUUID(const std::string &uuid)
{
    lock_uuid_ = uuid;
    return 0;
}


int UCSmartLockConfigUpdate::Handler(UpdateConfigDataPtr msg)
{
    UCSmartLockConfigUpdatePtr ptr =std::static_pointer_cast<UCSmartLockConfigUpdate>(msg);
    GetSmartLockUpdateContorlInstance()->SmartLockConfigUpdateHandle(ptr->change_type_, ptr->lock_uuid_, ptr->node_, ptr->project_type_, ptr->mng_id_);

    return 0;
}

std::string UCSmartLockConfigUpdate::Identify(UpdateConfigDataPtr msg)
{
    std::stringstream identify;
    UCSmartLockConfigUpdatePtr ptr =std::static_pointer_cast<UCSmartLockConfigUpdate>(msg);
    identify << "UCSmartLockConfigUpdate change_type=" << ptr->change_type_ <<" lock_uuid=" << ptr->lock_uuid_ << " node=" << ptr->node_ << " project_type=" << ptr->project_type_ << " mng_id=" << ptr->mng_id_;
    return identify.str();
}

void RegSmartLockConfigUpdateTool()
{
    RegUpdateConfigTool(UPDATE_SMARTLOCK_CONFIG, UCSmartLockConfigUpdate::Handler, UCSmartLockConfigUpdate::Identify);
}



