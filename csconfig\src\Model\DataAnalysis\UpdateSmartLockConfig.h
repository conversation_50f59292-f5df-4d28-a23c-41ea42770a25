#ifndef __CSADAPT_UPDATE_SMARTLOCK_CONFIG_H__
#define __CSADAPT_UPDATE_SMARTLOCK_CONFIG_H__

#include <vector>
#include <string>
#include <map>
#include <list>
#include <memory>
#include "UpdateConfigDef.h"
#include "DataAnalysisUpdateConfig.h"
#include "BasicDefine.h"

class UCSmartLockConfigUpdate
{
public:
    UCSmartLockConfigUpdate(uint32_t change_type, const std::string &lock_uuid, const std::string &node, int project_type, int mng_id);
    ~UCSmartLockConfigUpdate();
    static int Handler(UpdateConfigDataPtr msg);
    static std::string Identify(UpdateConfigDataPtr msg);
    int SetUUID(const std::string &uuid);
   
private:
    uint32_t change_type_;
    std::string lock_uuid_;
    std::string node_;
    int project_type_;
    int mng_id_;
};

typedef std::shared_ptr<UCSmartLockConfigUpdate> UCSmartLockConfigUpdatePtr;
void RegSmartLockConfigUpdateTool();


#endif //__CSADAPT_UPDATE_SMARTLOCK_CONFIG_H__