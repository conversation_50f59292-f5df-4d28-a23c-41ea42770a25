<?php

// 生成时替换建表语句
$tableDefine = "
Create Table: CREATE TABLE `CommPerPrivateKeyTest` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Account` char(32) NOT NULL COMMENT '用户账号,包括主从',
  `Code` char(20) DEFAULT '',
  `CommunityID` int(10) unsigned NOT NULL DEFAULT '0',
  `CreateTime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `Special` tinyint(1) unsigned DEFAULT '0' COMMENT '是否是创建账号时候创建的key',
  PRIMARY KEY (`ID`),
  KEY `CommunityID_Code` (`CommunityID`,`Code`),
  KEY `Account` (`Account`)
) ENGINE=InnoDB AUTO_INCREMENT=1237 DEFAULT CHARSET=utf8
";

$pattern = '/^\s*(PRIMARY|UNIQUE|KEY)\s+.*$/m';
$tableDefine = preg_replace($pattern, '', $tableDefine);

function getTableName($tableDefine)
{
    preg_match("/CREATE TABLE `([^`]+)`/", $tableDefine, $matches);
    return $matches[1];
}

function getTableHeaderDefine($input)
{
    $output = preg_replace_callback('/([A-Z][a-z]*)/', function ($matches) {
        return '_' . strtoupper($matches[1]);
    }, $input);
    if (substr($output, 0, 1) === '_') {
        $output = substr($output, 1);
    }
    return "__CSADAPT_DATAANALYSIS_" . $output . "_H__";
}

function getDataAnalysisChangeHandle($tableName, $tableDefine)
{
    $dataChangeHandle = "    /*单个变化的检测, 主要用于缓存变化/数据清理*/ \n";
    $tableUpper = preg_replace_callback('/([A-Z][a-z]*)/', function ($matches) {
        return '_' . strtoupper($matches[1]);
    }, $tableName);
    if (substr($tableUpper, 0, 1) === '_') {
        $tableUpper = substr($tableUpper, 1);
    }
    preg_match_all("/`(\w+)` ([^ ]+)/", $tableDefine, $matches, PREG_SET_ORDER);
    foreach ($matches as $row => $match) {
        if ($row == 0) continue;
        $fieldName = $match[1];
        $fieldUpper = strtoupper($fieldName);
        $dataChangeHandle .= "   {DA_INDEX_{$tableUpper}_{$fieldUpper}, \"{$fieldName}\", ItemChangeHandle},\n";
    }
    $dataChangeHandle .= "   {DA_INDEX_INSERT, \"\", InsertHandle},\n";
    $dataChangeHandle .= "   {DA_INDEX_DELETE, \"\", DeleteHandle},\n";
    $dataChangeHandle .= "   {DA_INDEX_UPDATE, \"\", UpdateHandle},\n";
    return rtrim($dataChangeHandle, ",\n");
}

function getDataAnalysisEnumHandle($tableName, $tableDefine)
{
    $enumHandle = "/*复制到DataAnalysisDef.h*/ \n";
    $tableUpper = preg_replace_callback('/([A-Z][a-z]*)/', function ($matches) {
        return '_' . strtoupper($matches[1]);
    }, $tableName);
    $enumHandle .= "enum DA{$tableName}Index{\n";
    if (substr($tableUpper, 0, 1) === '_') {
        $tableUpper = substr($tableUpper, 1);
    }
    preg_match_all("/`(\w+)` ([^ ]+)/", $tableDefine, $matches, PREG_SET_ORDER);
    foreach ($matches as $row => $match) {
        if ($row == 0) continue;
        $fieldName = $match[1];
        $fieldUpper = strtoupper($fieldName);
        $enumHandle .= "    DA_INDEX_{$tableUpper}_{$fieldUpper},\n";
    }
    $enumHandle .= "};\n";
    return rtrim($enumHandle, ",\n");
}

// 表名
$tableName = getTableName($tableDefine);

// 头文件定义
$hppDefine = getTableHeaderDefine($tableName);

$headerDefine = "
#include <map>
#include <list>
#include <vector>
#include <string>
";

// 构造hpp文件
$hppContent = "";
$hppContent .= "#ifndef $hppDefine\n";
$hppContent .= "#define $hppDefine\n";
$hppContent .= $headerDefine . "\n";
$hppContent .= "void RegDa" . $tableName . "Handler();" . "\n\n";
$hppContent .= "#endif" . "\n";

$fileName = "DataAnalysis" . $tableName;
file_put_contents("$fileName.h", $hppContent);


// 构造cpp文件
$cppHeaderDefine = "";
$cppHeaderDefine .= "#include \"$fileName.h\"\n";
$cppHeaderDefine .= "#include \"DataAnalysisTableParse.h\"\n";
$cppHeaderDefine .= "#include \"DataAnalysisControl.h\"\n";
$cppHeaderDefine .= "#include \"DataAnalysisContext.h\"\n";
$cppHeaderDefine .= "#include \"DataAnalysis.h\"\n";
$cppHeaderDefine .= "#include <string.h>\n";
$cppHeaderDefine .= "#include <memory>\n";
$cppHeaderDefine .= "#include \"UpdateConfigCommFileUpdate.h\"\n";
$cppHeaderDefine .= "#include \"UpdateConfigCommAccessUpdate.h\"\n";
$cppHeaderDefine .= "#include \"UpdateConfigPersonalFileUpdate.h\"\n";
$cppHeaderDefine .= "#include \"UpdateConfigCommDevUpdate.h\"\n";
$cppHeaderDefine .= "#include \"AkLogging.h\"\n";
$cppHeaderDefine .= "#include \"AkcsWebMsgSt.h\"\n";
$cppHeaderDefine .= "#include \"AkcsCommonDef.h\"\n";
$cppHeaderDefine .= "#include \"DataAnalysisdbHandle.h\"\n";
$cppHeaderDefine .= "#include \"dbinterface/resident/ResidentPersonalAccount.h\"\n";

$handleFunction = "
static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context);
";

$dataEnum = getDataAnalysisEnumHandle($tableName, $tableDefine);
$dataChangeHandle = getDataAnalysisChangeHandle($tableName, $tableDefine);


$itemChangeHandler = "

static int CommonChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    std::string uid;
    std::string mac;
    std::vector<std::string> macs;
    uint32_t mng_id ;
    uint32_t unit_id;
    uint32_t change_type;
    UCCommunityFileUpdatePtr comfileptr = std::make_shared<UCCommunityFileUpdate>(change_type, mng_id, unit_id, mac, uid);
    context.AddUpdateConfigInfo(UPDATE_COMM_FILE_UPDATE, comfileptr);
	
    UCCommunityAccessUpdatePtr accessptr = std::make_shared<UCCommunityAccessUpdate>(change_type, mng_id, mac, uid);
    context.AddUpdateConfigInfo(UPDATE_COMM_ACCESS_UPDATE, accessptr);
	
    UCPersonalFileUpdatePtr fileptr = std::make_shared<UCPersonalFileUpdate>(change_type, mac, uid);
    context.AddUpdateConfigInfo(UPDATE_PERSONAL_FILE_UPDATE, fileptr);
	
    UCCommunityDevUpdatePtr devptr = std::make_shared<UCCommunityDevUpdate>(change_type, macs);
    context.AddUpdateConfigInfo(UPDATE_COMM_DEV_UPDATE, devptr);
   
    return 0;
}

static int ItemChangeHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    return 0;
}

static int InsertHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    // 具体使用
    return 0;
}

static int DeleteHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    // 具体使用
    return 0;
}

static int UpdateHandle(DataAnalysisTableParse &data, DataAnalysisContext &context)
{
    // 具体使用
    return 0;
}

static const DataAnalysisColumnList& GetDetectKey()
{
    return local_detect_key;
}
";

$regCppFunction = "void RegDa" . $tableName . "Handler()\n";
$regCppFunction .= "{\n";
$regCppFunction .= "    int len = sizeof(da_change_handle) / sizeof(DataAnalysisChangeHandle);\n";
$regCppFunction .= "    RegDaSort(local_detect_key, da_change_handle, len);\n";
$regCppFunction .= "    DataAnalysisDBHandlerPtr ptr = std::make_shared<DataAnalysisDBHandler>(da_change_handle, len, GetDetectKey);\n";
$regCppFunction .= "    RegDataAnalysisDBHandler(local_table_name, ptr);\n";
$regCppFunction .= "}\n";


$cppContent = "";
$cppContent .= $cppHeaderDefine . "\n";
$cppContent .= $handleFunction . "\n";
$cppContent .= "static DataAnalysisColumnList local_detect_key;\n";
$cppContent .= "static const std::string local_table_name = \"" . $tableName . "\";" . "\n";
$cppContent .= $dataEnum . "\n";
$cppContent .= "static DataAnalysisChangeHandle da_change_handle[] = {\n";
$cppContent .= $dataChangeHandle . "\n};\n";
$cppContent .= $itemChangeHandler . "\n";
$cppContent .= $regCppFunction . "\n";


$fileName = "DataAnalysis" . $tableName;
file_put_contents("$fileName.cpp", $cppContent);
?>