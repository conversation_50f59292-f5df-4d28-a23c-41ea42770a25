#!/bin/bash

File="./DataAnalysisGenerate.cpp"
echo '#include "DataAnalysisGenerate.h"' > $File

# Include all relevant header files
files=$(grep -l "RegDa" *.h)
for file in $files; do
    echo "#include \"Model/DataAnalysis/$file\"" >> $File
done


# Define the function
echo "void RegDataAnalysisDBAllHandlerGenerate()" >>$File
echo "{" >> $File

# Add all RegDa function calls
grep "RegDa" *.h | grep -Ev "RegDataAnalysisDBAllHandler|RegDaSort|RegDataAnalysisDBHandler|RegDataAanlysisDBAllHandlerGenerate" | awk -F ".h:" '{print $2}' | awk '{print $2}' >> $File

echo "}" >> $File




