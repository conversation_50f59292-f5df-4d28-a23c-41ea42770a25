#include <sstream>
#include "DevConfig.h"
#include <string.h>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "ConfigDef.h" 
#include "AdaptUtility.h"
#include "json/json.h"
#include "util.h"
#include "util_judge.h"
#include "AkLogging.h"
#include "DeviceSetting.h"
#include "AkcsWebMsgSt.h"
#include "CommunityMng.h"
#include "DeviceControl.h"
#include "AES256.h"
#include "AkcsMonitor.h"
#include "encrypt/Md5.h"
#include "AkcsCommonDef.h"
#include "ShadowMng.h"
#include "PersonalAccount.h"
#include "WriteFileControl.h"
#include "ConfigCommon.h"
#include "dbinterface/Account.h" 
#include "dbinterface/PmAccountMap.h" 
#include "dbinterface/PubDevMngList.h" 
#include "dbinterface/AccessGroupDB.h"
#include "dbinterface/VideoStorage.h"
#include "dbinterface/UserAccessGroup.h"
#include "dbinterface/VideoStorageDevice.h"
#include "dbinterface/resident/ResidentDevices.h" 
#include "dbinterface/resident/ResidentPersonalAccount.h" 
#include "dbinterface/resident/IndoorMonitorConfig.h"
#include "DevExternRelayConfig.h"
#include "util_relay.h"

extern CSCONFIG_CONF gstCSCONFIGConf;
extern std::map<string, AKCS_DST> g_time_zone_DST;

OldDeviceTimezone g_old_dev_timezone_list = {
    {"Dawson","GMT-8:00"},
    {"Whitehorse","GMT-8:00"},
    {"Boise","GMT-6:00"},
    {"Knox","GMT-5:00"},
    {"Matamoros","GMT-5:00"},
    {"Grand_Turk","GMT-4:00"},
    {"Havana","GMT-4:00"},
    {"Halifax","GMT-3:00"},
    {"Amsterdam","GMT+0:00"},
    {"Azores","GMT+0:00"},
    {"Casablanca","GMT+0:00"},
    {"Dublin","GMT+0:00"},
    {"El_Aaiun","GMT+0:00"},
    {"Troll"," GMT+1:00"}
};

DevConfig::DevConfig(const std::string& config_root_path, int mng_sip_type, int rtp_confuse, int mng_rtsp_type)
{
    config_root_path_ = config_root_path;
    mng_sip_type_ = mng_sip_type;
    rtp_confuse_ = rtp_confuse;
    mng_rtsp_type_ = mng_rtsp_type;
}

int DevConfig::WriteDevListFiles(DEVICE_SETTING* dev_list)
{
    DEVICE_SETTING *dev = dev_list;
    int ret = 0;
    while (dev != NULL)
    {
        ret = WriteFiles(dev);
        if (ret !=0 )
        {
            return ret;
        }
        dev = dev->next;
    }
    return ret;
}

int DevConfig::WriteDevFiles(DEVICE_SETTING* dev)
{
    return WriteFiles(dev);
}



//检查设备给定ID的权限组中是否包含某个relay,传入原有权限组ID列表，通过"/"分隔;传入relay字段代表设备的第几个relay
std::string DevConfig::GetValidRelaySchedule(const std::string &mac, const std::string& relay_schedule, unsigned int relay_index)
{
    std::string valid_schedule;
    if(relay_index == 0)
    {
        AK_LOG_INFO << "pass relay_index wrong, val is 0";
        return valid_schedule;
    }

    std::set<std::string> schedules;
    SplitString(relay_schedule, "/", schedules);

    for(const auto &schedule : schedules) 
    {
        int schedule_id = ATOI(schedule.c_str());
        int relay_val = -1;
        if (context_->IsDefaultAccessGroup(schedule_id))
        {
            valid_schedule += schedule;
            valid_schedule += "/";
            continue;
        }
        relay_val = context_->DevSchduleIDRelay(mac, schedule_id);
        if (relay_val <= 0) {
            continue;
        }
        //判断当前relay是否在权限组中
        if ((relay_val >> (relay_index - 1)) & 1) {
            valid_schedule += schedule;
            valid_schedule += "/";
        }
    }
    return valid_schedule;
}

std::map<int, std::string> DevConfig::GetHoldDoorRelayToString(DEVICE_SETTING* dev) {
    std::map<int, std::vector<int>> hold_door_relay_to_ids;
    std::map<int, std::string> relay_to_string;
    HoldDoorInfoPtrList hold_door_info_ptr_list;

    if (0 != dbinterface::UserAccessGroup::GetDeviceHoldDoorList(dev, hold_door_info_ptr_list)) {
        AK_LOG_INFO << "DevConfig GetDeviceHoldDoorList failed, dev_uuid = " << dev->uuid;
        return relay_to_string; // 返回空的 map
    }

    for (const auto& hold_door_info_ptr : hold_door_info_ptr_list) {
        for (int relay_index = 1; relay_index <= DEVICE_RELAY_NUM; ++relay_index) {
            unsigned int bitmask = 1 << (relay_index - 1);
            if ((hold_door_info_ptr->relay & bitmask) != 0) {
                hold_door_relay_to_ids[relay_index].push_back(hold_door_info_ptr->latest_user_access_group_id);
            }
        }
    }
    for (const auto& pair : hold_door_relay_to_ids) {
        int relay_index = pair.first;
        const std::vector<int>& ids = pair.second;
        std::string concatenated_ids;
        for (const auto& id : ids) {
            concatenated_ids += std::to_string(id) + "/";
        }
        relay_to_string[relay_index] = concatenated_ids;
    }

    return relay_to_string;
}

int DevConfig::GetEnableSchedule(const std::string &hold_door_relay_schedule, int schedule_enable)
{
    if (hold_door_relay_schedule.empty()) {
        return schedule_enable;
    }
    return 1;
}

void DevConfig::WriteNewCommunitRelayConfig(std::stringstream &config, DEVICE_SETTING* dev)
{
     //relay 配置
    config << CONFIG_RELAY_DMTF_OPTION << 0 << "\n";
    std::vector<RELAY_INFO> relays;
    ParseRelay(dev->relay, relays);

    //检查权限组中是否有该relay，没有则不下发
    uint32_t i = 1;
    std::vector<std::string> config_dtmf_code = {CONFIG_RELAY_DMTF_CODE1, CONFIG_RELAY_DMTF_CODE2, CONFIG_RELAY_DMTF_CODE3, CONFIG_RELAY_DMTF_CODE4};
    std::vector<std::string> config_dtmf_name = {CONFIG_RELAY_DMTF_NAME1, CONFIG_RELAY_DMTF_NAME2, CONFIG_RELAY_DMTF_NAME3, CONFIG_RELAY_DMTF_NAME4};
    std::vector<std::string> config_relay_unlock = {CONFIG_RELAY_RELAYUNLOCK1, CONFIG_RELAY_RELAYUNLOCK2, CONFIG_RELAY_RELAYUNLOCK3, CONFIG_RELAY_RELAYUNLOCK4};
    std::vector<std::string> config_relay_schedule_enable = {CONFIG_RELAY_RELAYSCHEDULE_ENABLE1, CONFIG_RELAY_RELAYSCHEDULE_ENABLE2, CONFIG_RELAY_RELAYSCHEDULE_ENABLE3, CONFIG_RELAY_RELAYSCHEDULE_ENABLE4};
    std::vector<std::string> config_relay_schedule = {CONFIG_RELAY_RELAYSCHEDULE1, CONFIG_RELAY_RELAYSCHEDULE2, CONFIG_RELAY_RELAYSCHEDULE3, CONFIG_RELAY_RELAYSCHEDULE4};
    std::map<int, std::string> hold_door_relay_to_ids;
    if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL) {
        hold_door_relay_to_ids = GetHoldDoorRelayToString(dev);
    }
    for (auto& Relay : relays)
    {
        if (!Relay.enable)
        {
            i++;
            continue;
        }
        std::string relay_schedule;
        std::string hold_door_schedule;
        int enable_schedule;
        //权限组校验，防止relay不在权限组中还下发了relay schedule给设备
        //防止web AccessGroupDevice的Relay字段变了，但Device表的Relay字段没去更新的问题，加一层保护
        relay_schedule = dbinterface::AccessGroup::GetValidRelaySchedule(dev->mac, Relay.schedule, i);
        if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL) {
            // 个人设备才配置门常开
            hold_door_schedule = hold_door_relay_to_ids[i];
            enable_schedule = GetEnableSchedule(hold_door_schedule, Relay.enable_schedule);
            relay_schedule += hold_door_schedule;
        } else {
            enable_schedule = Relay.enable_schedule;
        }
        if (i <= DEVICE_RELAY_NUM) // 确保不超过数组大小
        {
            config << config_dtmf_code[i-1] << Relay.dtmf << "\n";
            config << config_dtmf_name[i-1] << Relay.name << "\n";
            config << config_relay_unlock[i-1] << Relay.access_control << "\n";
            config << config_relay_schedule_enable[i-1] << enable_schedule << "\n";
            config << config_relay_schedule[i-1] << relay_schedule << "\n";
        }

        i++;
    }
    
    if (strlen(dev->security_relay) > 0)
    {
        int i = 1;
        std::vector<RELAY_INFO> security_relay_infos;
        ParseRelay(dev->security_relay, security_relay_infos);
        for (auto &relay : security_relay_infos)
        {
            switch (i)
            {
                if (!relay.enable)
                {
                    i++;
                    continue;
                }
                case 1:
                    config << CONFIG_SECURITY_RELAY_DMTF_CODE1 << relay.dtmf << "\n";
                    config << CONFIG_SECURITY_RELAY_DMTF_NAME1 << relay.name << "\n";
                    config << CONFIG_SECURITY_RELAY_ENABLED1 << relay.enable << "\n";
                    config << CONFIG_SECURITY_RELAY_RELAYUNLOCK1 << relay.access_control << "\n";                    
                    break;
                    
                case 2:
                    config << CONFIG_SECURITY_RELAY_DMTF_CODE2 << relay.dtmf << "\n";
                    config << CONFIG_SECURITY_RELAY_DMTF_NAME2 << relay.name << "\n";
                    config << CONFIG_SECURITY_RELAY_ENABLED2 << relay.enable << "\n";
                    config << CONFIG_SECURITY_RELAY_RELAYUNLOCK2 << relay.access_control << "\n";
                    break;
                    
                case 3:
                    config << CONFIG_SECURITY_RELAY_DMTF_CODE3 << relay.dtmf << "\n";
                    config << CONFIG_SECURITY_RELAY_DMTF_NAME3 << relay.name << "\n";
                    config << CONFIG_SECURITY_RELAY_ENABLED3 << relay.enable << "\n";
                    config << CONFIG_SECURITY_RELAY_RELAYUNLOCK3 << relay.access_control << "\n";                   
                    break;
                case 4:
                    config << CONFIG_SECURITY_RELAY_DMTF_CODE4 << relay.dtmf << "\n";
                    config << CONFIG_SECURITY_RELAY_DMTF_NAME4 << relay.name << "\n";
                    config << CONFIG_SECURITY_RELAY_ENABLED4 << relay.enable << "\n";
                    config << CONFIG_SECURITY_RELAY_RELAYUNLOCK4 << relay.access_control << "\n";
                    break;
                    
                default:
                    //只支持4个
                    break;
            }
            i++;
        }
    }
}

void DevConfig::WriteOldCommunitRelayConfig(std::stringstream &config, DEVICE_SETTING* dev)
{
    //relay 配置
    config << CONFIG_RELAY_DMTF_OPTION << 0 << "\n";
    std::vector<RELAY_INFO> relays;
    ParseRelay(dev->relay, relays);
    
    //检查权限组中是否有该relay，没有则不下发
    uint32_t i = 1;
    for (auto& Relay : relays)
    {
        if (!Relay.enable)
        {
            i++;
            continue;
        }
        std::string relay_schedule;
        switch (i)
        {
            case 1:
                config << CONFIG_RELAY_DMTF_CODE1 << Relay.dtmf << "\n";
                config << CONFIG_RELAY_DMTF_NAME1 << Relay.name << "\n";
                config << CONFIG_RELAY_RELAYUNLOCK1 << 31 << "\n";
                config << CONFIG_RELAY_RELAYSCHEDULE_ENABLE1 << 0 << "\n";
                config << CONFIG_RELAY_RELAYSCHEDULE1 << "" << "\n";
                
                break;
            case 2:
                config << CONFIG_RELAY_DMTF_CODE2 << Relay.dtmf << "\n";
                config << CONFIG_RELAY_DMTF_NAME2 << Relay.name << "\n";
                config << CONFIG_RELAY_RELAYUNLOCK2 << 31 << "\n";
                config << CONFIG_RELAY_RELAYSCHEDULE_ENABLE2 << 0 << "\n";
                config << CONFIG_RELAY_RELAYSCHEDULE2 << "" << "\n";
                
                break;
    
            case 3:
                config << CONFIG_RELAY_DMTF_CODE3 << Relay.dtmf << "\n";
                config << CONFIG_RELAY_DMTF_NAME3 << Relay.name << "\n";
                config << CONFIG_RELAY_RELAYUNLOCK3 << 31 << "\n";
                config << CONFIG_RELAY_RELAYSCHEDULE_ENABLE3 << 0 << "\n";
                config << CONFIG_RELAY_RELAYSCHEDULE3 << "" << "\n";
                
                break;
                
            case 4:
                config << CONFIG_RELAY_DMTF_CODE4 << Relay.dtmf << "\n";
                config << CONFIG_RELAY_DMTF_NAME4 << Relay.name << "\n";
                config << CONFIG_RELAY_RELAYUNLOCK4 << 31 << "\n";
                config << CONFIG_RELAY_RELAYSCHEDULE_ENABLE4 << 0 << "\n";
                config << CONFIG_RELAY_RELAYSCHEDULE4 << "" << "\n";
                break;
        }
        i++;
    }
    
    if (strlen(dev->security_relay) > 0)
    {
        int i = 1;
        std::vector<RELAY_INFO> security_relay_infos;
        ParseRelay(dev->security_relay, security_relay_infos);
        for (auto &relay : security_relay_infos)
        {
            switch (i)
            {
                case 1:
                    config << CONFIG_SECURITY_RELAY_DMTF_CODE1 << relay.dtmf << "\n";
                    config << CONFIG_SECURITY_RELAY_DMTF_NAME1 << relay.name << "\n";
                    config << CONFIG_SECURITY_RELAY_ENABLED1 << relay.enable << "\n";
                    config << CONFIG_SECURITY_RELAY_RELAYUNLOCK1 << 31 << "\n";                    
                    break;
                    
                case 2:
                    config << CONFIG_SECURITY_RELAY_DMTF_CODE2 << relay.dtmf << "\n";
                    config << CONFIG_SECURITY_RELAY_DMTF_NAME2 << relay.name << "\n";
                    config << CONFIG_SECURITY_RELAY_ENABLED2 << relay.enable << "\n";
                    config << CONFIG_SECURITY_RELAY_RELAYUNLOCK2 << 31 << "\n";
                    break;
                    
                case 3:
                    config << CONFIG_SECURITY_RELAY_DMTF_CODE3 << relay.dtmf << "\n";
                    config << CONFIG_SECURITY_RELAY_DMTF_NAME3 << relay.name << "\n";
                    config << CONFIG_SECURITY_RELAY_ENABLED3 << relay.enable << "\n";
                    config << CONFIG_SECURITY_RELAY_RELAYUNLOCK3 << 31 << "\n";
                    break;

                case 4:
                    config << CONFIG_SECURITY_RELAY_DMTF_CODE4 << relay.dtmf << "\n";
                    config << CONFIG_SECURITY_RELAY_DMTF_NAME4 << relay.name << "\n";
                    config << CONFIG_SECURITY_RELAY_ENABLED4 << relay.enable << "\n";
                    config << CONFIG_SECURITY_RELAY_RELAYUNLOCK4 << 31 << "\n";                    
                    break;

                default:
                    //只支持4个
                    break;
            }
            i++;
        }
    }


}

void DevConfig::WriteRelayConfig(std::stringstream &config, DEVICE_SETTING* dev)
{
    if(communit_info_->GetIsNew())
    {
        WriteNewCommunitRelayConfig(config,dev);
    }
    else
    {    
        WriteOldCommunitRelayConfig(config,dev);
    }
}

void DevConfig::WriteTimeZoneConfig(std::stringstream &config, DEVICE_SETTING* dev)
{
    /*4.6 下发平台时区配置,5.4 下发时间格式,add by czw*/
    if (dev->dclient_version >= D_CLIENT_VERSION_4600)    //新版本设备时区列表才一致，旧版本不做同步
    {
        std::string ntp_time = "GMT";
        std::string city_name;
        std::string timezone = communit_info_->TimeZone();
        int time_format = communit_info_->TimeFormate();
        std::size_t found = timezone.find(' ');
        if (found != std::string::npos)
        {
            ntp_time += timezone.substr(0, found);
        
            city_name = timezone.substr(found + 1);
        }
        //旧版本设备用了新时区的不配
        if (dev->dclient_version >= D_CLIENT_VERSION_5400 || \
            (city_name != CONFIG_NEW_TIMEZONE_NUUK && city_name != CONFIG_NEW_TIMEZONE_KOLKATA))  
        {
            config << CONFIG_CLOUDSERVER_TOMEZONE << ntp_time << "\n";
            config << CONFIG_CLOUDSERVER_CITYNAME << city_name << "\n";
        }
        
        if (dev->dclient_version >= D_CLIENT_VERSION_5400) 
        {
            int hour_format = CustomizeDateFormatToDeviceConfigValue(time_format, 1);
            config << CONFIG_CLOUDSERVER_TIMEFORMAT << hour_format << "\n";
            int date_format = CustomizeDateFormatToDeviceConfigValue(time_format, 2);
            config << CONFIG_CLOUDSERVER_DATEFORMAT << date_format << "\n";
        }
        if (dev->dclient_version < D_CLIENT_VERSION_5400)
        {
            std::string timezone = g_old_dev_timezone_list[city_name];
            config << CONFIG_CLOUDSERVER_TOMEZONE << timezone << "\n"; 
        }

    }

}

void GetManageList(std::string& mng_list, const ConfigContextPtr& context, DEVICE_SETTING* dev)
{
    DEVICE_SETTING* pMngDeviceList =  context->AllMngDeviceSetting();
    DEVICE_SETTING* pCurMngDevice = pMngDeviceList;
    while (pCurMngDevice != NULL)
    {
        if (dev->id == pCurMngDevice->id
                || pCurMngDevice->grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
        {
            pCurMngDevice = pCurMngDevice->next;
            continue;
        }
        if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT && pCurMngDevice->grade != csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT
         && !context->DevMngUnitID(pCurMngDevice, dev->unit_id))
        {
            pCurMngDevice = pCurMngDevice->next;
            continue;
        }
                
        std::string contact;
        //公共设备直接的是否走ip，只能根据netgroup
        if (pCurMngDevice->netgroup_num == dev->netgroup_num)
        {
            contact = pCurMngDevice->ip_addr;
        }
        else
        {
            contact = pCurMngDevice->sip_account;
        }

        if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
        {
            if ((dev->unit_id == pCurMngDevice->unit_id
                    &&  pCurMngDevice->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)//梯口
                    ||  pCurMngDevice->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC//公共
                    || (pCurMngDevice->grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL //个人
                        && strcmp(dev->device_node, pCurMngDevice->device_node) == 0)
               )
            {

                mng_list += contact;
                mng_list += ";";

            }
        }
        else if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
        {
            if ((dev->unit_id == pCurMngDevice->unit_id
                    &&  pCurMngDevice->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)//梯口
                    ||  pCurMngDevice->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC//公共
               )
            {
                mng_list += contact;
                mng_list += ";";

            }
        }
        else if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
        {
            if (pCurMngDevice->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)//公共
            {
                mng_list += contact;
                mng_list += ";";

            }
        }
        pCurMngDevice = pCurMngDevice->next;
    }
    
    context->ReleaseDeviceSetting(pMngDeviceList);
    //设备呼叫PM APP
    const NodeAppList& pm_app_list = context->GetPmAppList();
    for (const auto &pm_account : pm_app_list)
    {
        //TODO:目前pm app没有calltype
        //PM的calltype只能在app设置，值为012，当是2时候配置给设备的sip账号，呼叫时候再走兜底
        if (pm_account.call_type == NODE_CALL_TYPE_INDOOR_PHONE)
        {
            //目前PM端只会有一个呼叫号码
            if (strlen(pm_account.phone) > 0 && strlen(pm_account.phone_code) > 0)
            {
                std::string phone = PHONE_CALL_OUT_SUBFIX;
                phone += pm_account.phone_code;
                phone += pm_account.phone;
                mng_list += phone;
                mng_list += ";";
            }
        }
        else
        {
            mng_list += pm_account.sip_account;
            mng_list += ";";
        }
        
    }
}

void DevConfig::WriteManageKeyConfig(std::stringstream &config, DEVICE_SETTING* dev)
{
    if (akjudge::IsCommunityPublicDev(dev->grade))
    {
        std::string mng_list;
        GetManageList(mng_list, context_, dev);
        
        if (mng_list.length() > 2)
        {
            //R29
            config << "Config.DoorSetting.DISPLAY.Key6Typ =5\n";
            config << "Config.DoorSetting.DISPLAY.Key6Number=" << mng_list.c_str()  << "\n";
             //linux 设备
            if (dev->dclient_version < D_CLIENT_VERSION_4400)
            {

            }
            else if (akjudge::IsCommunityPublicDev(dev->grade)) //公共设备才配置这个选项
            {   
               
                config << CONFIG_SIP_GROUP_ACCOUNT << mng_list.c_str() << "\n";
            }
        }
        else
        {
            config << "Config.DoorSetting.DISPLAY.Key6Typ =\n";
            config << "Config.DoorSetting.DISPLAY.Key6Number=\n";
            if (dev->dclient_version < D_CLIENT_VERSION_4400)
            {
            
            }
            else if (akjudge::IsCommunityPublicDev(dev->grade)) //公共设备才配置这个选项
            {
                config << CONFIG_SIP_GROUP_ACCOUNT << "\n";
            }
        }

    }    
}

void DevConfig::WriteDoorConfig(std::stringstream &config, DEVICE_SETTING* dev)
{
    config << CONFIG_DTMF_ENABLE << "1" << "\n"
             << CONFIG_DTMF_CODE1 << "11" << "\n"
             << CONFIG_RTSP_ENABLE << "1" << "\n"
             << CONFIG_RTSP_VIDEO << "1" << "\n"
             << CONFIG_RTSP_CODEC << "0" << "\n"
             //<< CONFIG_RTSP_H264_RESOLUTION << "3" << "\n"
             << CONFIG_RTSP_H264_FRAMERATE << "30" << "\n";
    //<< CONFIG_RTSP_H264_BITRATE << "256" << "\n";
    //v4.0 这个群组号是根据联动找设备时候带出来的 //社区公共设备应该是空值
    //过期的只有室内机IP，已经在查数据这步处理了
    if (dev->dclient_version < D_CLIENT_VERSION_5000) //V5.0 单住户加入了落地，app和落地有呼叫顺序，要走callloop
    {
        config << CONFIG_SIP_GROUP_ACCOUNT << dev->push_button << "\n";
    }
    else
    {
        config << CONFIG_SIP_GROUP_ACCOUNT << "" << "\n";
    }

    config << "Config.DoorSetting.RTSP.H264Resolution = 4\n";
    config << "Config.DoorSetting.RTSP.H264BitRate = 2048\n";

   //社区全部配置空
    config << CONFIG_FEATURES_CALLROBIN_ENABLE << "0" << "\n"
           << CONFIG_FEATURES_CALLROBIN_NUM << "" << "\n"
           << CONFIG_FEATURES_CALLROBIN_TIME << "20" << "\n";

    if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
    {
        int enable_motion = CDeviceSetting::GetInstance()->GetMotionDetection(dev->dclient_version, dev->enable_motion);
        config << CONFIG_DOORSETTING_MOTION_DETECT_ENABLE << enable_motion << "\n"
                 << CONFIG_DOORSETTING_MOTION_DETECT_TIME << dev->motion_time << "\n";
    }
    else if (akjudge::IsCommunityPublicDev(dev->grade))
    {
        int enable_motion = CDeviceSetting::GetInstance()->GetMotionDetection(dev->dclient_version, communit_info_->isEnableMotion());
        config << CONFIG_DOORSETTING_MOTION_DETECT_ENABLE << enable_motion << "\n"
                 << CONFIG_DOORSETTING_MOTION_DETECT_TIME << communit_info_->MotionTime() << "\n";
    }

    //relay 配置
    WriteRelayConfig(config, dev);    
}

void DevConfig::WriteCameraConfig(std::stringstream &config, const ThirdPartyCamreaInfo* camera)
{
    if (camera == nullptr)
    {
        return;
    }
    config <<  "Config.DoorSetting.RTSP.ThirdPartyCameraName=" << camera->location << "\n";
    config <<  "Config.DoorSetting.RTSP.ThirdPartyCameraUUID=" << camera->camera_uuid << "\n";
    config <<  "Config.DoorSetting.RTSP.ThirdPartyCameraUrl=" << camera->rtsp_url << "\n";
    config <<  "Config.DoorSetting.RTSP.ThirdPartyCameraRTSPUser=" << camera->username << "\n";
    config <<  "Config.DoorSetting.RTSP.ThirdPartyCameraRTSPPwd=" << camera->passwd << "\n";
}

void DevConfig::WriteThirdPartyCameraConfig(std::stringstream &config, DEVICE_SETTING* dev)
{
    //三方摄像头给门口机/管理机/门禁下发配置
    if (dev->dclient_version >= D_CLIENT_VERSION_6500 && (dev->type == DEVICE_TYPE_STAIR 
        ||dev->type == DEVICE_TYPE_DOOR || dev->type == DEVICE_TYPE_MANAGEMENT || dev->type == DEVICE_TYPE_INDOOR || dev->type == DEVICE_TYPE_ACCESS))
    {
        const ThirdPartyCamreaInfo* camera = context_->GetMacThirdPartyCamera(dev->mac);
        WriteCameraConfig(config, camera);
    }

    //三方摄像头高级功能过期下发给室内机
    if (dev->dclient_version >= D_CLIENT_VERSION_6500 
        && (dev->type == DEVICE_TYPE_INDOOR || dev->type == DEVICE_TYPE_MANAGEMENT))
    {
        if(!communit_info_->IsExpire())
        {
            config <<  "Config.Settings.THIRDPARTYCAMERA.Expire=0\n";
        }
        else
        {
            config <<  "Config.Settings.THIRDPARTYCAMERA.Expire=1\n";
        }
    }
}

void DevConfig::WriteCommunityContactSwitch(std::stringstream &config, DEVICE_SETTING* dev)
{
    if (dev->type == DEVICE_TYPE_INDOOR && dev->dclient_version >= D_CLIENT_VERSION_6500)
    {
        if(context_->IsCommunityContactOn())
        {
            config <<  "Config.Settings.CONTACT.WithBlocklist=1" << "\n";
            config <<  "Config.Settings.CONTACT.WithCommunity=1" << "\n";
        }
        else
        {
            config <<  "Config.Settings.CONTACT.WithBlocklist=0" << "\n";
            config <<  "Config.Settings.CONTACT.WithCommunity=0" << "\n";
        }
    }
}

void DevConfig::WriteGroundFloorConfig(std::stringstream &config, DEVICE_SETTING* dev)
{
    if (!akjudge::DevDoorType(dev->type))
    {
        return;
    }
    CommunityUnitInfo unit_info = context_->GetUnitInfo(dev->unit_id);
    int ground_floor_type = unit_info.ground_floor;
    switch (ground_floor_type)
    {
        case GroundFloorType::GROUND_FLOOR_NONE:
        {
            config << CONFIG_RELAY_GROUND_FLOOR << CONFIG_GROUND_FLOOR_NONE << "\n";
            config << CONFIG_LIFTCONTROL_GROUND_FLOOR << CONFIG_GROUND_FLOOR_NONE << "\n";
            break;
        }
        case GroundFloorType::GROUND_FLOOR_G0:
        {
            config << CONFIG_RELAY_GROUND_FLOOR << CONFIG_GROUND_FLOOR_G0 << "\n";
            config << CONFIG_LIFTCONTROL_GROUND_FLOOR << CONFIG_GROUND_FLOOR_G0 << "\n";
            break;
        }
        case GroundFloorType::GROUND_FLOOR_G0_G1:
        {
            config << CONFIG_RELAY_GROUND_FLOOR << CONFIG_GROUND_FLOOR_G0_G1 << "\n";
            config << CONFIG_LIFTCONTROL_GROUND_FLOOR << CONFIG_GROUND_FLOOR_G0_G1 << "\n";
            break;
        }
        case GroundFloorType::GROUND_FLOOR_G0_G1_G2:
        {
            config << CONFIG_RELAY_GROUND_FLOOR << CONFIG_GROUND_FLOOR_G0_G1_G2 << "\n";
            config << CONFIG_LIFTCONTROL_GROUND_FLOOR << CONFIG_GROUND_FLOOR_G0_G1_G2 << "\n";
            break;
        }
        default:
        {
            break;
        }
    }
    // 为空表示设备本地配置，云不下发
    if (!std::string(unit_info.start_floor).empty())
    {
        config << CONFIG_RELAY_FLOOR_STARTS_FROM << unit_info.start_floor << "\n";
        config << CONFIG_LIFTCONTROL_FLOOR_STARTS_FROM << unit_info.start_floor << "\n";
    }
}

// 室内机视频存储配置
int DevConfig::WriteIndoorVideoStorageConfig(std::stringstream &config, DEVICE_SETTING* dev)
{
    // 公共区域的室内机不支持视频存储
    if (akjudge::IsCommunityPublicDev(dev->grade)) 
    {
        config << CONFIG_VIDEO_RECORD_ENABLE << "0" << "\n";
        return 0;
    }

    config << CONFIG_VIDEO_RECORD_ENABLE << "1" << "\n";
    return 0;
}

// 门口机视频存储配置
int DevConfig::WriteDoorVideoStorageConfig(std::stringstream &config, DEVICE_SETTING* dev)
{
    // 获取设备视频存储信息
    VideoStorageInfo video_storage_info;
    if (0 != context_->GetVideoStorageConfig().GetVideoStorageInfo(dev, video_storage_info))
    {
        AK_LOG_INFO << "mac = " << dev->mac << " get video storage info failed";
        return -1;
    }

    // 设备是否被勾选为视频存储设备
    if (!context_->GetVideoStorageConfig().IsVideoStorageDevices(dev->uuid)) 
    {
        AK_LOG_INFO << "mac = " << dev->mac << " not select for VideoStorageDevices";
        return -1;
    }
    
    config << CONFIG_VIDEO_RECORD_CLOUD << "1" << "\n";
    config << CONFIG_VIDEO_RECORD_ENABLE << "1" << "\n";
    config << CONFIG_VIDEO_RECORD_LENGTH << "10" << "\n";
    config << CONFIG_VIDEO_RECORD_MOTION << "1" << "\n";
    config << CONFIG_VIDEO_RECORD_CALL_IN << "1" << "\n";
    config << CONFIG_VIDEO_RECORD_CALL_OUT << "1" << "\n";
    config << CONFIG_VIDEO_RECORD_ACCESS_DENIED << "1" << "\n";
    config << CONFIG_VIDEO_RECORD_ACCESS_GRANTED << "1" << "\n";
    config << CONFIG_VIDEO_RECORD_OPENDOOR_ALARM << "1" << "\n";

    // 是否开启视频存储呼叫音频
    if (video_storage_info.is_enable_call_audio == 1) 
    {
        config << CONFIG_VIDEO_RECORD_INCLUDE_AUDIO << "1" << "\n";
    }
    else
    {
        config << CONFIG_VIDEO_RECORD_INCLUDE_AUDIO << "0" << "\n";
    }
    return 0;
}

int DevConfig::WriteVideoRecordConfig(std::stringstream &config, DEVICE_SETTING* dev)
{
    // 设备是否支持视频存储功能
    if (!SwitchHandle(dev->fun_bit, FUNC_DEV_SUPPORT_VIDEO_RECORD))
    {
        AK_LOG_INFO << "mac = " << dev->mac << " not support video record fucntion";
        return -1;
    }

    if (akjudge::DevIndoorType(dev->type))
    {
        WriteIndoorVideoStorageConfig(config, dev);
    }
    else if (akjudge::DevDoorType(dev->type))
    {
        WriteDoorVideoStorageConfig(config, dev);
    }
    return 0;
}

//新小区特有的配置统一写在这个函数
void DevConfig::WriteNewCommunityFuncConfig(std::stringstream &config, DEVICE_SETTING* dev)
{
    //模拟手柄功能
    if (SwitchHandle(dev->fun_bit, FUNC_DEV_SUPPORT_ANALOG_HANDLE))
    {
        config << CONFIG_ANALOG_TYPE << "9" << "\n"; //模拟品牌 9=Akuvox
    }
    //包裹/声音检测下发配置
    WriteDetectionConfig(config, dev);
    //辅助摄像头下发配置
    UpdateAuxCameraConfig(dev->fun_bit, config);
}

void DevConfig::WriteDetectionConfig(std::stringstream &config, DEVICE_SETTING *dev)
{
    bool is_personal_device = (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL);
    
    if (context_->IsPackageDetectionDev(dev->firmware)) {
        config << "Config.DoorSetting.PACKAGE_DETECT.Enable=" 
               << (is_personal_device ? dev->enable_package_detection : communit_info_->EnablePackageDetection()) << "\n";
    }

    // if (context_->IsSoundDetectionDev(dev->firmware)) {
    //     config << "Config.DoorSetting.SOUND_DETECT.Enabled=" << (is_personal_device ? dev->enable_sound_detection : communit_info_->EnableSoundDetection()) << "\n";

    //     auto soundType = is_personal_device ? dev->sound_type : communit_info_->SoundType();
    //     config << "Config.DoorSetting.SOUND_DETECT.Gun=" << SwitchHandle(soundType, static_cast<int>(SoundType::Gun)) << "\n";
    //     config << "Config.DoorSetting.SOUND_DETECT.Dog=" << SwitchHandle(soundType, static_cast<int>(SoundType::Dog)) << "\n";
    //     config << "Config.DoorSetting.SOUND_DETECT.Baby=" << SwitchHandle(soundType, static_cast<int>(SoundType::Baby)) << "\n";
    //     config << "Config.DoorSetting.SOUND_DETECT.Glass=" << SwitchHandle(soundType, static_cast<int>(SoundType::Glass)) << "\n";
    //     config << "Config.DoorSetting.SOUND_DETECT.Siren=" << SwitchHandle(soundType, static_cast<int>(SoundType::Siren)) << "\n";
    // }
}

// 嵌入式室内机最多只有1个local relay，安卓室内机最多有2个local relay
void DevConfig::WriteIndoorRelayDelayConfig(std::stringstream& config, const std::string& relay_json)
{
    // 从relay JSON中获取第一个和第二个relay的hold_delay值
    std::vector<RELAY_INFO> relays;
    ParseRelay(relay_json, relays);
    
    // 处理第一个relay的hold_delay
    if (relays.size() > 0)
    {
        int device_hold_delay;
        if (ConvertLocalRelayHoldDelayForDevice(relays[0].hold_delay, device_hold_delay))
        {
            //安卓室内机的Autop
            config << "Config.DoorSetting.RELAY.Delay1=" << device_hold_delay << "\n";
            // 嵌入式室内机
            config << "Config.Settings.RELAY.Interval=" << device_hold_delay << "\n";


        }
    }
    
    // 处理第二个relay的hold_delay
    if (relays.size() > 1)
    {
        int device_hold_delay;
        if (ConvertLocalRelayHoldDelayForDevice(relays[1].hold_delay, device_hold_delay))
        {
            //安卓室内机的Autop
            config << "Config.DoorSetting.RELAY.Delay2=" << device_hold_delay << "\n";
        }
    }
}

int DevConfig::WriteFiles(DEVICE_SETTING* dev)
{
    if (dev == NULL)
    {
        AK_LOG_WARN << "DevConfig::WriteFiles The param is null .";
        return -1;
    }

    // 确保extern_relay_config_已初始化
    if (extern_relay_config_ == nullptr && context_ != nullptr)
    {
        extern_relay_config_ = std::make_shared<DevExternRelayConfig>(context_);
    }

    //chenzhx 这个值不能一直变，相关设备/账户更新配置，会影响到这个配置重新下载
    int sip_port = HashtabHashString(dev->sip_password) % 55534 + 10000;

    std::stringstream config;
    config << CONFIG_ENABLE << "1" << "\n"
           << CONFIG_LABLE << dev->sip_account << "\n"
           << CONFIG_DISPLAYNAME << dev->location << "\n"
           << CONFIG_USERNAME << dev->sip_account << "\n"
           << "Config.Account1.OUTPROXY.Enable=0" << "\n"
           << CONFIG_AUTHNAME << dev->sip_account << "\n"
           << CONFIG_PWD << dev->sip_password << "\n"
           << CONFIG_TIMEOUT << PERSONNAL_SIP_UA_TIMEOUT << "\n"
           << CONFIG_SIP_NAT_RPORT << "1" << "\n"
           << CONFIG_NAT_UDP_ENABLE <<  "1" << "\n"
           << CONFIG_NAT_UDP_INTERVAL <<  "30" << "\n"
           << CONFIG_CLOUDSERVER_FTP_USER << "akuvox" << "\n"
           << CONFIG_CLOUDSERVER_FTP_PWD << "pu6HYKvTkyGstq" << "\n"
           << CONFIG_CLOUDSERVER_DEV_EXPIRE << "0" << "\n"
           << "Config.Account1.Video00.ProfileLevel=2" << "\n"
           << "Config.Account1.Video00.MaxBR=512" << "\n"
           << "Config.Features.VIDEO_CODEC_PARAM.ProfileLevel=720P" << "\n"
           << "Config.Account1.SIP.ListenPortMin=" << sip_port << "\n"
           << "Config.Account1.SIP.ListenPortMax=" << sip_port + 10 << "\n";

    if (dev->dclient_version < D_CLIENT_VERSION_4400)
    {
        config << CONFIG_SERVER<< gstCSCONFIGConf.cspbx_outer_ip << "\n"
               << CONFIG_PORT << gstCSCONFIGConf.cspbx_outer_port << "\n"
               << CONFIG_CLOUDSERVER_FTP_URL << "ftp://" << gstCSCONFIGConf.ftp_ip << ":21" << "\n";
    }

    if (dev->dclient_version >= D_CLIENT_VERSION_5200)
    {
        config << "Config.Account1.Audio0.Enable=0" << "\n" //PCMU
               << "Config.Account1.Audio1.Enable=1" << "\n" //PCMA
               << "Config.Account1.Audio4.Enable=0" << "\n" //G729
               << "Config.Account1.Audio5.Enable=0" << "\n"; //G722
    }
    if (dev->dclient_version >= D_CLIENT_VERSION_6100)
    {
        //******** 涂鸦强制用pcmu
        config << "Config.Account1.Audio0.Enable=1" << "\n"; //PCMU
    }

    //门口机、梯口机增加sip群组账号
    if (akjudge::DevDoorType(dev->type))
    {
       WriteDoorConfig(config, dev); 
    }

    //apt+pin 0apt+key, 1key
    if (communit_info_->AptPinType() == CommunityInfo::AptPinTypeEnum::APT_PIN)
    {
        config << "Config.Doorsetting.PASSWORD.PrivateKeyType=0\n";
    }
    else
    {
        config << "Config.Doorsetting.PASSWORD.PrivateKeyType=1\n";
    }

    //社区设备:标识新旧小区
    config << CONFIG_CLOUDSERVER_COMMUNITY_TYPE << communit_info_->GetIsNew() << "\n";

    //写入新小区相关功能配置
    if (communit_info_->GetIsNew())
    {
        WriteNewCommunityFuncConfig(config, dev);
    }


    if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)//公共
    {
        config <<  CONFIG_CLOUDSERVER_TYPE << CLOUD_SERVER_TYPE_COMMUNITY_PUBLIC << "\n";
        //******** modify by chenzhx 0->1 all app/indoor already in contact
        config <<  CONFIG_CLOUDSERVER_DOOR_OPEN_LIMIT << 1 << "\n";
        config <<  CONFIG_COMMUNITY_NAME << communit_info_->Name() << "\n";  //社区公共设备做这个设置
        config <<  CONFIG_COMMUNITY_STREET << communit_info_->Street() << "\n";
        config <<  CONFIG_IDCARD_ENABLE << communit_info_->IDCard() << "\n";
        if (akjudge::DeviceSupportLimitFlow(dev->firmware))
        {
            config << CONFIG_NETWORK_DATAUSAGE_DATATYPE << communit_info_->LimitFlowDataType() << "\n";
        }
    }
    else if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)//单元公共
    {
        std::string unit_name = context_->GetUnitName(dev->unit_id);
        config << CONFIG_CLOUDSERVER_TYPE << CLOUD_SERVER_TYPE_COMMUNITY_UNIT << "\n";
        config << CONFIG_CLOUDSERVER_DOOR_OPEN_LIMIT << 1 << "\n";
        config << CONFIG_COMMUNITY_NAME << communit_info_->Name() << " " << unit_name << "\n";
        config << CONFIG_COMMUNITY_STREET << communit_info_->Street() << "\n";
        config << CONFIG_IDCARD_ENABLE << communit_info_->IDCard() << "\n";
        if (akjudge::DeviceSupportLimitFlow(dev->firmware))
        {
            config << CONFIG_NETWORK_DATAUSAGE_DATATYPE << communit_info_->LimitFlowDataType() << "\n";
        }
    }
    else //社区个人
    {
        config << CONFIG_CLOUDSERVER_TYPE << CLOUD_SERVER_TYPE_COMMUNITY << "\n";
        config <<  CONFIG_CLOUDSERVER_DOOR_OPEN_LIMIT << 1 << "\n";
    }

    WriteTimeZoneConfig(config, dev);

    //20191218 加入DEVICE_TYPE_DOOR，设备已经默认是2了，就是防止设备移来移去配置没有更新
    if (akjudge::DevDoorType(dev->type))
    {
        if (dev->stair_show == 0)// 0是个人stair类型 用2显示app/devices
        {
            dev->stair_show = 2;
        }
        config <<  CONFIG_CONTACT_SHOW_TEYP << dev->stair_show << "\n";
    }
    
    if (dev->type == DEVICE_TYPE_INDOOR)
    {
        //all call  50000-65535
        int port = ATOI(dev->device_node) % 15534 + 50000;
        config << "Config.Multicast.SELECTEDGROUP.SelectedGroup=1\n"
                 << "Config.Multicast.GROUP1.IP=**********:" << port << "\n"
                 << "Config.Multicast.LISTEN1.IP=**********:" << port << "\n"
                 << "Config.Netcast.SELECTEDGROUP.SelectedGroup=1\n"
                 << "Config.Netcast.GROUP1.IP=**********:" << port << "\n"
                 << "Config.Netcast.LISTEN1.IP=**********:" << port << "\n";

        WriteVoiceAssistantConfig(config, dev);
    }
    //写siptype siphacking
    if (dev->dclient_version >= D_CLIENT_VERSION_4400)
    {
        config << "Config.Account1.CALL.PreventSIPHacking=1\n";

    }
    if (DevMngSipType_NONE == mng_sip_type_)
    {
        config << "Config.Account1.SIP.TransType=" << dev->sip_type << "\n";
    }
    else
    {
        config << "Config.Account1.SIP.TransType=" << mng_sip_type_ << "\n";
    }
    //rtp confuse 6.0
    if (dev->dclient_version >= D_CLIENT_VERSION_6000)
    {
        config << "Config.Account1.CALL.AudioVideoConfuse=" << rtp_confuse_ << "\n";
    }

    
    //v5.0加入设备名称配置项
    config << "Config.DoorSetting.DEVICENODE.Location=" << dev->location << "\n";

    //因为这个选项27 28 会冲突，所以目前去掉这个配置   add by chenzhx ********
    //if(dev->dclient_version >= D_CLIENT_VERSION_5000 && dev->type == DEVICE_TYPE_DOOR)
    //{
    //    config << "Config.DoorSetting.GENERAL.DisplayVersion=" << 0 << "\n";
    //这个选项27 28 会冲突，不能配置
    //config << "Config.DoorSetting.GENERAL.ShowLcdContents=" << 1 << "\n";
    //}

    //配置管理中心键
    WriteManageKeyConfig(config, dev);

    if (dev->dclient_version >= D_CLIENT_VERSION_5200 && !SwitchHandle(dev->fun_bit, FUNC_DEV_GET_REMOTECONFIG_ADDR_BY_DCLIENT))
    {
        config << "Config.DoorSetting.DEVICENODE.SSHPassSrv=" << gstCSCONFIGConf.ssh_proxy_domain << "\n";
    }
    
    // ******** add by chenzhx        
    if (gstCSCONFIGConf.server_type == ServerArea::ccloud && akjudge::DevDoorType(dev->type) && dev->dclient_version < D_CLIENT_VERSION_6400)
    {
        config <<  "Config.Account1.DTMF.Type=4" << "\n";
    }

    if (akjudge::IsCommunityPublicDev(dev->grade)  && dev->dclient_version >= D_CLIENT_VERSION_6200 && akjudge::DevDoorType(dev->type))
    {
        //社区高级功能快递提醒
        if (!communit_info_->IsExpire() && communit_info_->CheckFeature(CommunityInfo::FeaturePlan::TAB_DEVICES_CHECK_INDEX_DELIVERY))
        {
            config <<  "Config.DoorSetting.PACKAGEROOM.RemindAvailable=1\n";
        }
        else
        {
            config <<  "Config.DoorSetting.PACKAGEROOM.RemindAvailable=0\n";
        }
    }

    //三方摄像头下发配置
    WriteThirdPartyCameraConfig(config, dev);
    if (dev->dclient_version >= D_CLIENT_VERSION_6400 && akjudge::DevDoorType(dev->type))
    {
        //1:Inband;2:RFC2833;3:Info;4:Info+Inband;5:Info+RFC2833;6:Info+Inband+RFC2833
        //add by czw, dtmf国内外方式不同处理
        if(gstCSCONFIGConf.server_type == ServerArea::ccloud)
        {
            config <<  "Config.Account1.DTMF.Type=6" << "\n";
        }
        else
        {
            config <<  "Config.Account1.DTMF.Type=2" << "\n";
        }      
    }

    //户户通界面开关autop
    WriteCommunityContactSwitch(config, dev);

    //室内机是否开启转发autop配置, 0:Off,  1:On
    if (dev->type == DEVICE_TYPE_INDOOR)
    {
        config << "Config.Indoor.HidePage.TransferCall=" << dev->repost << "\n";
    }
    
    // rtsps 开关
    config << CONFIG_RTSPS_ENABLE << mng_rtsp_type_ << "\n";
    //外接Relay相关autop配置下发
    if (extern_relay_config_)
    {
        extern_relay_config_->WriteExternRelayConfig(config, dev->uuid);
    }

    // 视频存储配置
    if (0 != WriteVideoRecordConfig(config, dev))
    {
        config << CONFIG_VIDEO_RECORD_ENABLE << "0" << "\n";
    }
    // 负楼层配置下发
    //由于网页默认是跟随设备自身配置，所以这边不用考虑旧版本兼容
    WriteGroundFloorConfig(config, dev);
    
    UpdateUcloudVideoBitRate(dev->SWVer, config);
    UpdateSipSrtpConfig(mng_sip_type_, dev->fun_bit, config);

    if (communit_info_->GetIsNew())
    {
        UpdateHighResolutionVideoResolution(dev->firmware, config);
        // 室内机新增hold_delay配置
        if (dev->type == DEVICE_TYPE_INDOOR)
        {
            WriteIndoorRelayDelayConfig(config, dev->relay);
        }  
    }
    

    config << dev->config;
    std::string config_path = config_root_path_ + dev->mac + ".cfg";
    DevFileInfoPtr ptr = std::make_shared<DevFileInfo>(dev->mac, config_path, config.str(), SHADOW_TYPE::SHADOW_CONFIG,
                                                        project::RESIDENCE, dev->id);
    GetWriteFileControlInstance()->AddFileInfo(dev->mac, ptr);

    return 0;
}
