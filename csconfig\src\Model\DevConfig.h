#ifndef __DEV_CONFIG_H__
#define __DEV_CONFIG_H__
#include <string>
#include <functional>
#include <memory>
#include "dbinterface/CommunityInfo.h"
#include "AKCSMsg.h"
#include "dbinterface/PersonalThirdPartyCamera.h"
#include "dbinterface/ThirdPartyCamera.h"
#include "InnerUtil.h"
#include "UpdateConfigContext.h"
#include "dbinterface/resident/ExtraDevice.h"
#include "DevExternRelayConfig.h"

//std::vector<std::string> gNewTimeZoneVec{"Pacific-New","Center","New_Salem","Beulah","Nuuk","Kolkata"};
typedef map<std::string, std::string> OldDeviceTimezone;

class CommConfigHandle;

class ExtraDeviceRelay;

//社区设备Config类
class DevConfig
{
public:
    int WriteDevListFiles(DEVICE_SETTING* dev_list);
    int WriteDevFiles(DEVICE_SETTING* dev);
    int SetCommunityInfo(   CommunityInfoPtr communit_info)
    {
        communit_info_ = communit_info;
        return 0;
    }
    
    int SetContext(ConfigContextPtr context)
    {
        context_ = context;
        return 0;
    }
    

private:
        // 声明友元类
    friend class CommConfigHandle;  
    DevConfig(const std::string& config_root_path, int mng_sip_type, int rtp_confuse, int mng_rtsp_type);


    int WriteFiles(DEVICE_SETTING* dev);
    void WriteRelayConfig(std::stringstream &config, DEVICE_SETTING* dev);
    void WriteNewCommunitRelayConfig(std::stringstream &config, DEVICE_SETTING* dev);
    void WriteOldCommunitRelayConfig(std::stringstream &config, DEVICE_SETTING* dev);
    void WriteTimeZoneConfig(std::stringstream &config, DEVICE_SETTING* dev);
    void WriteManageKeyConfig(std::stringstream &config, DEVICE_SETTING* dev);
    void WriteDoorConfig(std::stringstream &config, DEVICE_SETTING* dev);
    void WriteThirdPartyCameraConfig(std::stringstream &config, DEVICE_SETTING* dev);
    void WriteCameraConfig(std::stringstream &config, const ThirdPartyCamreaInfo* camera);
    void WriteCommunityContactSwitch(std::stringstream &config, DEVICE_SETTING* dev);
    
    // 外接Relay相关函数已移至DevExternRelayConfig类
    
    int WriteVideoRecordConfig(std::stringstream &config, DEVICE_SETTING* dev);
    //新小区特有的配置统一写在这个函数
    void WriteNewCommunityFuncConfig(std::stringstream &config, DEVICE_SETTING* dev);
    std::map<int, std::string> GetHoldDoorRelayToString(DEVICE_SETTING* dev);
    int GetEnableSchedule(const std::string &hold_door_relay_schedule, int schedule_enable);
    std::string GetValidRelaySchedule(const std::string &mac, const std::string& relay_schedule, unsigned int relay_index);
    void WriteGroundFloorConfig(std::stringstream &config, DEVICE_SETTING* dev);
    void WriteDetectionConfig(std::stringstream &config, DEVICE_SETTING* dev);
    void WriteIndoorRelayDelayConfig(std::stringstream& config, const std::string& relay_json);

    int mng_rtsp_type_; // 0:tcp,1:tls
    int mng_sip_type_;
    int rtp_confuse_;
    std::string config_root_path_;
    CommunityInfoPtr communit_info_;

    ConfigContextPtr context_;
    std::shared_ptr<DevExternRelayConfig> extern_relay_config_; // 外接Relay配置对象
};
void GetManageList(std::string& mng_list, const ConfigContextPtr& context, DEVICE_SETTING*dev);

#endif 
