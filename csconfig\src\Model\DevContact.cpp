#include <sstream>
#include "DevContact.h"
#include <string.h>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "ConfigDef.h" 
#include "AdaptUtility.h"
#include "json/json.h"
#include "util.h"
#include "util_judge.h"
#include "AkLogging.h"
#include "DeviceSetting.h"
#include "AkcsWebMsgSt.h"
#include "CommunityMng.h"
#include "DeviceControl.h"
#include "AES256.h"
#include "AkcsMonitor.h"
#include "encrypt/Md5.h"
#include "PersonalAccount.h"
#include "DeviceSetting.h"
#include "PersonnalDeviceSetting.h"
#include "AkcsCommonDef.h"
#include "ShadowMng.h"
#include "ContactCommon.h"
#include "dbinterface/PersonalAccountCnf.h"
#include "dbinterface/resident/ResidentDevices.h" 
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/ContactBlock.h"
#include "dbinterface/ContactFavorite.h"
#include "dbinterface/Account.h"
#include "dbinterface/PmAccountMap.h"
#include "CommConfigHandle.h"
#include "FileUpdateControl.h"
#include "AKCSView.h"
#include "WriteFileControl.h"
#include "dbinterface/VersionModel.h"
#include "util_string.h"
#include <unordered_set>

#define MAX_CALL_SEQUENCE 3 //sequence call

extern CSCONFIG_CONF gstCSCONFIGConf;


int DevContact::WriteDevFile(const CString &file,  const std::stringstream& config_body)
{
    //写入文件 
    FILE* pfile = fopen(file.GetBuffer(), "w+");
    if (pfile == NULL)
    {
        AK_LOG_WARN << "fopen failed " << file.GetBuffer();       
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csconfig", file.GetBuffer(), AKCS_MONITOR_ALARM_CONFIG_WRITE_FAILED);
        return -1;
    }
    //将配置信息内容写入文件中
    fwrite(/*file_buf*/config_body.str().c_str(), sizeof(char), strlen(config_body.str().c_str()) + 1, pfile);
    fclose(pfile);
    AK_LOG_INFO << "The file path is " << file.GetBuffer();

    //加密
    if (!gstCSCONFIGConf.no_encrypt)
    {
        FileAESEncrypt(file.GetBuffer(), AES_ENCRYPT_KEY_V1, file.GetBuffer());
    }
    return 0;    
}

int DevContact::CheckUcloudCommunityID(unsigned int community_id)
{
    char tmp[64] = "";
    snprintf(tmp, sizeof(tmp), ",%u,", community_id);
    if (strstr(gstCSCONFIGConf.community_ids, tmp))
    {
        return 1;
    }
    return 0;
}

/*确定呼叫顺序
比如：2-2;3-1 代表第二组呼叫，取SIP或IP呼叫。加代表第三组呼叫，取SIP0
    因为app有落地要呼叫，所以一条联系人可能会存在于多个呼叫组，用分号分隔。
SIP0(uid)  SIP(phone)
SIP0为下标1，SIP/IP为下标2，land下标为3
seq2是因为从账号加了落地号码，也需要按照calltype的顺序，因为群呼时候的落地又不包括从账号的落地，所以需要新增字段
开启的calltype选项只有呼叫phone，没有呼叫app，如果从账号没有配置phone，会呼叫app。平台会把land的选项配置为sip账号

*/
int DevContact::CreateGroupCallSeq(int call_type, std::vector<DEVICE_CONTACTLIST>& app_list, DEVICE_SETTING* your_list)
{
    if (call_type == NODE_CALL_TYPE_INDOOR_PHONE)
    {
        DEVICE_SETTING* cur_device_setting = your_list;
        while (cur_device_setting != NULL)
        {
            if (cur_device_setting->type == DEVICE_TYPE_INDOOR || cur_device_setting->type == DEVICE_TYPE_MANAGEMENT)
            {
                ::snprintf(cur_device_setting->seq, sizeof(cur_device_setting->seq), " seq=\"1-2\" ");
            }
            cur_device_setting = cur_device_setting->next;
        }

        int is_master = 1;
        for (auto& app : app_list)
        {
            if (is_master)
            {
                is_master = 0;
                ::snprintf(app.seq, sizeof(app.seq), " seq=\"1-2;1-3\" ");
            }
            else
            {
                ::snprintf(app.seq2, sizeof(app.seq2), " seq2=\"1-3\" ");
            }
        }
    }
    else if (call_type == NODE_CALL_TYPE_INDOOR_BACK_APP)
    {
        DEVICE_SETTING* cur_device_setting = your_list;
        while (cur_device_setting != NULL)
        {
            if (cur_device_setting->type == DEVICE_TYPE_INDOOR || cur_device_setting->type == DEVICE_TYPE_MANAGEMENT)
            {
                ::snprintf(cur_device_setting->seq, sizeof(cur_device_setting->seq), " seq=\"1-2\" ");
            }
            cur_device_setting = cur_device_setting->next;
        }

        int is_master = 1;
        for (auto& app : app_list)
        {
            if (is_master)
            {
                is_master = 0;
                ::snprintf(app.seq, sizeof(app.seq), " seq=\"2-2\" ");

            }
            else
            {
                //从账号
                ::snprintf(app.seq, sizeof(app.seq), " seq=\"2-2\" ");
            }
        }
    }
    else if (call_type == NODE_CALL_TYPE_INDOOR_BACK_PHONE)
    {
        DEVICE_SETTING* cur_device_setting = your_list;
        while (cur_device_setting != NULL)
        {
            if (cur_device_setting->type == DEVICE_TYPE_INDOOR || cur_device_setting->type == DEVICE_TYPE_MANAGEMENT)
            {
                ::snprintf(cur_device_setting->seq, sizeof(cur_device_setting->seq), " seq=\"1-2\" ");
            }
            cur_device_setting = cur_device_setting->next;
        }
        int is_master = 1;
        for (auto& app : app_list)
        {
            if (is_master)
            {
                is_master = 0;
                ::snprintf(app.seq, sizeof(app.seq), " seq=\"2-2;2-3\" ");
            }
            else
            {
                ::snprintf(app.seq2, sizeof(app.seq2), " seq2=\"1-3;\" ");
            }
        }
    }
    else if (call_type == NODE_CALL_TYPE_INDOOR_BACK_APP_BACK_PHONE)
    {
        DEVICE_SETTING* cur_device_setting = your_list;
        while (cur_device_setting != NULL)
        {
            if (cur_device_setting->type == DEVICE_TYPE_INDOOR || cur_device_setting->type == DEVICE_TYPE_MANAGEMENT)
            {
                ::snprintf(cur_device_setting->seq, sizeof(cur_device_setting->seq), " seq=\"1-2\" ");
            }
            cur_device_setting = cur_device_setting->next;
        }

        int is_master = 1;
        for (auto& app : app_list)
        {
            if (is_master)
            {
                is_master = 0;
                ::snprintf(app.seq, sizeof(app.seq), " seq=\"2-1;3-2;3-3\" ");
            }
            else
            {
                //从账号
                ::snprintf(app.seq, sizeof(app.seq), " seq=\"2-2\" ");
                //从账号的第二个顺序，没有sip0
                ::snprintf(app.seq2, sizeof(app.seq2), " seq2=\"1-2;2-3;\" ");
            }
        }
    }
    return 0;
}

void DevContact::GetPhoneInfo(const DEVICE_CONTACTLIST &app, 
   std::string &phone_head,
   std::string &phone,
   std::string &phone2,
   std::string &phone3,
   std::string &phone_all,
   std::string &phone_last)
{
    phone_head = PHONE_CALL_OUT_SUBFIX;
    phone_head += app.phone_code;
    
    if (strlen(app.phone) > 0)
    {
        phone = phone_head;
        phone += app.phone;
        phone_all = phone;
    }
    if (strlen(app.phone2) > 0)
    {
        phone2 = phone_head;
        phone2 += app.phone2;
        phone_all += ";";
        phone_all += phone2;
        phone_last = phone2;
    }
    if (strlen(app.phone3) > 0)
    {
        phone3 = phone_head;
        phone3 += app.phone3;
        phone_all += ";";
        phone_all += phone3;
        phone_last += ";";
        phone_last += phone3;
    }
}

void DevContact::ChangeSlaveCallInfo(const DEVICE_CONTACTLIST &app, std::string &phone)
{
    //开启的calltype选项只有呼叫phone，没有呼叫app，如果从账号没有配置phone，要呼叫app
    if (app.call_type == NODE_CALL_TYPE_INDOOR_PHONE
                       || app.call_type == NODE_CALL_TYPE_APP_INDOOR_BACK_PHONE
                       || app.call_type == NODE_CALL_TYPE_INDOOR_BACK_PHONE)
    {
        if (strlen(app.phone) == 0)
        {
            phone = app.sip_account;
        }
    }
}

void DevContact::SetNodeCallInfo(DEVICE_CONTACTLIST &app, int calltype, int dclient_ver)
{

    if (calltype == NODE_CALL_TYPE_INDOOR_BACK_PHONE
            || calltype == NODE_CALL_TYPE_INDOOR_BACK_APP_BACK_PHONE
            || calltype == NODE_CALL_TYPE_INDOOR_BACK_APP
            || calltype == NODE_CALL_TYPE_INDOOR_PHONE)
    {
        app.call_loop = CALL_LOOP_TYPE_GROUP_CALL;
    }
    else if (calltype == NODE_CALL_TYPE_APP_INDOOR_BACK_PHONE)
    {
        app.call_loop = CALL_LOOP_TYPE_APP_INDOOR_BACK_PHONE;
    }
    else
    {
        app.call_loop = CALL_LOOP_TYPE_NORMAL;
    }
    
    if (calltype == NODE_CALL_TYPE_APP_INDOOR_BACK_PHONE
            || calltype == NODE_CALL_TYPE_INDOOR_PHONE
            || calltype == NODE_CALL_TYPE_INDOOR_BACK_PHONE
            || calltype == NODE_CALL_TYPE_INDOOR_BACK_APP_BACK_PHONE)
    {
        app.phone_status = 1;//落地V4.3改为CallType控制
    }
    else
    {
        app.phone_status = 0;
    }
    
    app.call_type = calltype;
    //设备V4.4 版本NODE_CALL_TYPE_INDOOR_PHONE 修改为新的呼叫方式
    if (dclient_ver < D_CLIENT_VERSION_4400 && calltype == NODE_CALL_TYPE_INDOOR_PHONE)
    {
        //node_config.call_type = NODE_CALL_TYPE_INDOOR_PHONE_OLD;
        app.call_loop = CALL_LOOP_TYPE_NORMAL;
    }
}

void DevContact::InsertCameraList(const ThirdPartyCamreaInfo &camera, int monitor_type, std::stringstream &config_body, int not_monitor)
{
    ContactKvList kv;
    kv.push_back(std::make_pair(CONTACT_ATTR::NAME, camera.location));
    kv.push_back(std::make_pair(CONTACT_ATTR::UID, camera.camera_uuid));
    kv.push_back(std::make_pair(CONTACT_ATTR::URL, camera.rtsp_url)); 
    kv.push_back(std::make_pair(CONTACT_ATTR::BONDMAC, camera.mac));
    kv.push_back(std::make_pair(CONTACT_ATTR::RTSPUSER, camera.username));
    kv.push_back(std::make_pair(CONTACT_ATTR::RTSPPWD, camera.passwd));
    kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(DEVICE_TYPE_THIRD_CAMERA)));
    kv.push_back(std::make_pair(CONTACT_ATTR::MONITOR_TYPE, std::to_string(monitor_type)));
    kv.push_back(std::make_pair(CONTACT_ATTR::NOT_MONITOR, std::to_string(not_monitor)));
    GetContactStr(config_body, kv);
    return;
}

//管理机写三方摄像头联系人文件
void DevContact::WriteCameraContactFileToPublicManagement(ThirdPartyCamreaList camera_list, const DEVICE_SETTING* device_list, std::stringstream& config_body)
{
    const DEVICE_SETTING* cur_device_setting = nullptr;
    for (const auto& camera : camera_list)
    {
        //mac不为空遍历device_list
        if (strlen(camera.mac) && device_list)
        {
            cur_device_setting = device_list;
            while (cur_device_setting != nullptr)
            {
                if (0 == strcmp(cur_device_setting->mac, camera.mac))
                {
                    InsertCameraList(camera, DEVICE_MONITOR_TYPE_CLOUD, config_body);
                }
                cur_device_setting = cur_device_setting->next;
            }
        }
        else
        {
            //mac为空代表未绑定门口机
            InsertCameraList(camera, DEVICE_MONITOR_TYPE_THIRD_CAMERA, config_body);
        }
    }
}

int DevContact::GetNoMonitorContactFile(const DEVICE_SETTING* dev_setting)
{
    int not_monitor = 0;
    std::string sw_ver = dev_setting->SWVer;
    auto pos = sw_ver.find(".");
    if (pos != std::string::npos)
    {
        FirmwareList no_monitor_list;
        dbinterface::VersionModel::GetNoMonitorList(no_monitor_list);
        int firmware = ATOI(sw_ver.substr(0,pos).c_str());
        if (no_monitor_list.count(firmware))
        {
            not_monitor = 1;
        }
    }
    return not_monitor;
}

void DevContact::GetMasterGroupInfo(std::stringstream &config_body, const DEVICE_CONTACTLIST &app)
{
    ContactKvList kv;
    GetMasterGroupBaseInfo(app, kv);
    GetGroupStr(config_body, kv);
}

void DevContact::GetMasterGroupBaseInfo(const DEVICE_CONTACTLIST &app, ContactKvList& kv, const std::string& unit_info)
{
    char apt[256] = "";
    snprintf(apt, sizeof(apt), "%s", app.room_name);
    if (strlen(apt) == 0)
    {
        snprintf(apt, sizeof(apt), "%s", app.room_num);
    }
    
    kv.push_back(std::make_pair(CONTACT_ATTR::NAME, app.name));
    kv.push_back(std::make_pair(CONTACT_ATTR::ROOM, apt));                    
    kv.push_back(std::make_pair(CONTACT_ATTR::NODE, app.sip_account)); 
    kv.push_back(std::make_pair(CONTACT_ATTR::UNIT, unit_info));
    kv.push_back(std::make_pair(CONTACT_ATTR::SIP, app.sip_group));
    kv.push_back(std::make_pair(CONTACT_ATTR::ROOM_N, app.room_num));
    kv.push_back(std::make_pair(CONTACT_ATTR::UNIT_APT, app.unit_apt));
    kv.push_back(std::make_pair(CONTACT_ATTR::IP_DIRECT, std::to_string(app.enable_ip_direct)));
    kv.push_back(std::make_pair(CONTACT_ATTR::CALL_LOOP, std::to_string(app.call_loop)));
}

//室内机代理门口机转流
void DevContact::GetRepostContact(const DEVICE_SETTING* cur_dev, const DEVICE_SETTING* your_dev, ContactKvList &kv)
{
    if (your_dev->type == DEVICE_TYPE_INDOOR && (cur_dev->type == DEVICE_TYPE_STAIR || cur_dev->type == DEVICE_TYPE_DOOR) && cur_dev->repost)
    {
        kv.push_back(std::make_pair(CONTACT_ATTR::REPOST, std::string("1")));
    }
    else
    {
        kv.push_back(std::make_pair(CONTACT_ATTR::REPOST, std::string("0")));
    }
}

int DevContact::AccountIsRegister(const DEVICE_CONTACTLIST &app)
{
    if(strlen(app.phone) || strlen(app.phone2) || strlen(app.phone3) || strlen(app.email)|| strlen(app.mobile_number))
    {
        return 1;
    }
    return 0;
}

bool DevContact::CheckIsAptInDoorTypeDevice(int device_type)
{
    if(device_type == DEVICE_TYPE_INDOOR || device_type == DEVICE_TYPE_MANAGEMENT)
    {
        return true;
    }
    return false;
}

bool DevContact::CheckDeviceDclientVer6500(const DEVICE_SETTING* dev)
{
    if((dev->brand == DEVICE_BRAND::AKUVOX && dev->dclient_version >= D_CLIENT_VERSION_6500) || dev->brand == DEVICE_BRAND::OTHERS)
    {
        return true;
    }
    return false;
}

bool DevContact::CheckIsEmptyRoom(const DEVICE_CONTACTLIST &app)
{
    if(app.role == ACCOUNT_ROLE_COMMUNITY_MAIN && app.only_apt == 1)
    {
        return true;
    }
    return false;
}

//规则：
//1. app登录过+有填落地号码=先呼app再呼落地
//2. app登录过+未填落地号码=仅呼app
//3. app未登录过+有填落地号码=仅呼落地
//4. app未登录过+未填落地号码=失败
std::string DevContact::GetContactSingleCallStr(const DEVICE_CONTACTLIST& app, const std::string& phone_all)
{
    std::string singlecall_val;
    bool has_login_app = (app.app_login_status == 1);
    bool has_landline_num = (phone_all.size() > 0);
    std::string phone_list_format_str = phone_all;
    //同一轮落地号码用","分隔
    StringReplace(phone_list_format_str, ";", ",");

    //不同轮呼叫用";"分隔
    if (has_login_app)
    {
        singlecall_val = std::string(app.sip_account);
    }
    if (has_landline_num)
    {
        //";"分隔每一轮呼叫
        singlecall_val += (has_login_app ? ";" : "") + phone_list_format_str;
    }

    return singlecall_val;
}

bool DevContact::DeviceNotViewInContact(int16_t firmware, short oem_id)
{
    static const std::unordered_set<int16_t> excludedFirmwares = {
            SOFTWARE_G31,
            SOFTWARE_SL50,
            SOFTWARE_SM_VIRTUAL,
            SOFTWARE_SL20,
            SOFTWARE_SL21,
            SOFTWARE_SL30,
            SOFTWARE_SL40
    };
    if (excludedFirmwares.count(firmware) > 0) {
        return true;
    }
    // 过滤亚萨60锁
    if (firmware == SOFTWARE_SL60) {
        return oem_id == SOFTWARE_SL60_OEM_YASA;
    }
    return false;
}

// 给your_dev下发contact_dev的IP地址
std::string DevContact::GetCurDevIPAddress(const DEVICE_SETTING* your_dev, const DEVICE_SETTING* contact_dev)
{
    if (your_dev == nullptr || contact_dev == nullptr)
    {
        return "";
    }

    // 联系人设备是单网卡，直接返回联系人设备的IP地址
    if (strlen(contact_dev->wired_ip_addr) == 0)
    {
        return contact_dev->ip_addr;
    }

    // your_dev也是双网卡
    if (strlen(your_dev->wired_ip_addr) > 0)
    {
        // 双网卡设备之间会下发两个IP来防止SipHacking，这里按照默认情况下发即可
        return contact_dev->ip_addr;
    }
    else
    {
        //联系人设备没上报有线子网掩码，则用your_dev的掩码去判断
        auto contact_dev_wired_subnet_mask = strlen(contact_dev->wired_subnet_mask) > 0 ? contact_dev->wired_subnet_mask : your_dev->subnet_mask;
        auto contact_dev_wired_subnet = GetSubnetValue(contact_dev->wired_ip_addr, contact_dev_wired_subnet_mask);
        auto contact_dev_wireless_subnet = GetSubnetValue(contact_dev->ip_addr, contact_dev->subnet_mask);
        auto your_dev_subnet = GetSubnetValue(your_dev->ip_addr, your_dev->subnet_mask);

        // your_dev是单网卡，检查其IP是否与contact_dev的有线IP在同一网段
        if (contact_dev_wired_subnet == your_dev_subnet)
        {
            return contact_dev->wired_ip_addr;
        }
    }
    
    // 默认返回无线IP
    return contact_dev->ip_addr;
}

std::string DevContact::GetOptionIP(const DEVICE_SETTING* your_dev, const DEVICE_SETTING* contact_dev)
{
    if (your_dev == nullptr || contact_dev == nullptr)
    {
        return "";
    }

    // 联系人设备是双网卡，返回联系人设备的有线IP
    if (strlen(contact_dev->wired_ip_addr) > 0)
    {
        return contact_dev->wired_ip_addr;
    }

    // 否则返回空
    return "";
}
