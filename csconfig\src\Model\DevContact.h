#ifndef __DEV_CONTACT_H__
#define __DEV_CONTACT_H__
#include <string>
#include "util_cstring.h"
#include <boost/noncopyable.hpp>
#include "dbinterface/CommunityInfo.h"
#include "AKCSMsg.h"
#include "ContactCommon.h"
#include "dbinterface/PersonalThirdPartyCamera.h"
#include "dbinterface/ThirdPartyCamera.h"
#include "dbinterface/resident/ResidentPersonalAccount.h" 
#include <set>

//设备Contact基类
class DevContact : private boost::noncopyable
{
public:
    DevContact() {}

	virtual ~DevContact() = default;

    int WriteDevFile(const CString &file,      const std::stringstream& config_body);
    
    int CheckUcloudCommunityID(unsigned int community_id);
    
    int CreateGroupCallSeq(int call_type, std::vector<DEVICE_CONTACTLIST>& app_list, DEVICE_SETTING* your_list);

    void ChangeSlaveCallInfo(const DEVICE_CONTACTLIST &app, std::string &phone);

    void GetMasterGroupBaseInfo(const DEVICE_CONTACTLIST &app, ContactKvList& kv, const std::string& unit_info = "");
    
    void GetMasterGroupInfo(std::stringstream &config_body, const DEVICE_CONTACTLIST &app);

    void GetPhoneInfo(const DEVICE_CONTACTLIST &app, std::string &phone_head, std::string &phone, std::string &phone2,
                std::string &phone3, std::string &phone_all, std::string &phone_last);

    void SetNodeCallInfo(DEVICE_CONTACTLIST &app, int calltype, int dclient_ver);
    
    void InsertCameraList(const ThirdPartyCamreaInfo &camera, int monitor_type, std::stringstream &config_body, int not_monitor = 0);
    
    int GetNoMonitorContactFile(const DEVICE_SETTING* dev_setting);

    int IsHaveIndoorDevices(const DEVICE_SETTING* device_setting_list);

    int AccountIsRegister(const DEVICE_CONTACTLIST& app);
    
    bool CheckIsAptInDoorTypeDevice(int device_type);

    bool CheckDeviceDclientVer6500(const DEVICE_SETTING* dev_setting);

    bool CheckIsEmptyRoom(const DEVICE_CONTACTLIST &app);

    void CreateManagementDeviceContact(int enable_ip_direct, const DEVICE_SETTING* device_list, DEVICE_SETTING* your_dev, std::stringstream &config_body);

    void GetRepostContact(const DEVICE_SETTING* cur_dev, const DEVICE_SETTING* your_dev, ContactKvList &kv);

    virtual int UpdateContactFile(DEVICE_SETTING* your_dev, DEVICE_SETTING* your_list, std::vector<DEVICE_CONTACTLIST>& app_list,
                               const DEVICE_SETTING* pub_device_list/*最外层*/, 
                               const DEVICE_SETTING* unit_pub_device_list/*单元*/) = 0;
     
    virtual void UpdateThirdCameraContactFile(const DEVICE_CONTACTLIST& app, DEVICE_SETTING* your_dev, std::stringstream& config_body,
                              const DEVICE_SETTING* pub_device_list/*最外层*/, 
                              const DEVICE_SETTING* unit_pub_device_list/*单元*/,
                              const DEVICE_SETTING* your_list) = 0;

    std::string GetContactSingleCallStr(const DEVICE_CONTACTLIST& app, const std::string& phone_all);

    bool DeviceNotViewInContact(int16_t firmware, short oem_id);

    void WriteCameraContactFileToPublicManagement(ThirdPartyCamreaList camera_list, const DEVICE_SETTING* device_list, std::stringstream& config_body);
    
    std::string GetCurDevIPAddress(const DEVICE_SETTING* your_dev, const DEVICE_SETTING* cur_dev);

    std::string GetOptionIP(const DEVICE_SETTING* your_dev, const DEVICE_SETTING* contact_dev);

};

#endif 

