#include <cstring>
#include "DevExternRelayConfig.h"
#include "ConfigDef.h"
#include "AkLogging.h"
#include "util_relay.h"
#include "ConfigCommon.h"

DevExternRelayConfig::DevExternRelayConfig(ConfigContextPtr context)
    : context_(context)
{
}

void DevExternRelayConfig::WriteExternRelayConfig(std::stringstream &config, const std::string &dev_uuid)
{
    const IndoorMonitorConfigInfo* indoor_monitor_config = context_->GetIndoorConfig(dev_uuid);
    
    if (indoor_monitor_config == nullptr)
    {
        return;
    }

    // IndoorMonitorConfig.Meta为0，表示云上面没有进行配置，不刷配置
    if (indoor_monitor_config->meta == 0)
    {
        AK_LOG_INFO << "Skip external relay config refresh, IndoorMonitorConfig.Meta is 0 (ExternalDevice module not modified), device uuid: " << dev_uuid;
        return;
    }

    // 先重置配置, 再按实际的配置下发
    ResetExtraDeviceConfigs(config);
    
    // 未开启ExternalDevice 不下发
    if (indoor_monitor_config->ex_relay_switch == 0)
    {
        config << "Config.Indoor.EXTRELAY.Type=" << ExtraRelayType::TYPE_NONE << "\n";
        return;
    }

    config << "Config.Indoor.EXTRELAY.Type=" << indoor_monitor_config_relay_type_map.at(indoor_monitor_config->ex_relay_type) << "\n";
    config << "Config.Indoor.EXTRELAY.Mode=" << indoor_monitor_config_relay_mode_map.at(indoor_monitor_config->ex_relay_mode) << "\n";

    if (indoor_monitor_config->ex_relay_mode == ExtraRelayMode::MODE_ETHERNET)
    {
        config << "Config.Indoor.EXTRELAY.RelayIpAddr=" << indoor_monitor_config->ex_relay_ip << "\n";
        config << "Config.Indoor.EXTRELAY.RelayPort=" << indoor_monitor_config->ex_relay_port << "\n";
    }

    WriteExRelayListConfig(config, indoor_monitor_config);
}


void DevExternRelayConfig::WriteExRelayListConfig(std::stringstream &config, const IndoorMonitorConfigInfo* indoor_monitor_config)
{
    if (indoor_monitor_config == nullptr)
    {
        AK_LOG_WARN << "Indoor monitor config not provided";
        return;
    }
    
    // 获取所有设备配置
    ExtraDeviceRelayList extra_device_infos;
    context_->GetAllExtraDeviceRelayConfigs(indoor_monitor_config->uuid, extra_device_infos);
    
    if (extra_device_infos.empty())
    {
        AK_LOG_WARN << "Relay config not found in context for uuid: " << indoor_monitor_config->uuid;
        return;
    }
    
    // 处理R8类型设备
    if (indoor_monitor_config->ex_relay_type == ExtraRelayType::TYPE_RSAC_C1_R8)
    {
        for (auto& extra_device_info : extra_device_infos) 
        {
            if (extra_device_info.extra_device_.enable_switch == 0) 
            {
                continue;
            }
            WriteR8RelayConfigForDevice(config, extra_device_info);
        }
        return;
    }
    else
    {
        // 处理非R8设备
        for (auto& extra_device_info : extra_device_infos) 
        {
            if (extra_device_info.extra_device_.enable_switch == 0) 
            {
                continue;
            }
            WriteLegacyRelayConfigForDevice(config, extra_device_info);
        }
    }
    
    
}

void DevExternRelayConfig::WriteDeviceConfigByIndex(std::stringstream &config, 
                               const ExtraDeviceRelayActionInfo& action,
                               const ExtraDeviceRelayListInfo& relay, 
                               int device_index, 
                               bool is_extern_relay,
                               ExtraDeviceRelay& extra_device_info,
                               int output_index,
                               int function_index)
{
    int function_value = extra_device_info.CalculateFunctionValue(relay, action.action_type, function_index);
    
    if (is_extern_relay)
    {
        WriteExtRelayStatusConfig(config, device_index, output_index, relay.enable_switch, function_value, ConvertExtraRelayHoldDelayForDevice(ATOI(action.hold_delay)), relay.name);
    }
    else
    {
        WriteDigitalOutputConfig(config, device_index, output_index, relay.enable_switch, function_value, ConvertExtraRelayHoldDelayForDevice(ATOI(action.hold_delay)), relay.name);
    }
    
    // 根据设备索引生成DIGITALINPUT配置
    if (strlen(action.input) > 0)
    {
        // 从input字段解析索引（IT1->1, IT2->2, ... IT8->8）
        int input_index = GetRelayOutputValue(action.input);
        if (input_index == 0) 
        {
            AK_LOG_WARN << "Invalid input format: " << action.input;
            return;
        }
        
        // 根据ConnectType决定ShowPopup值：1-Button(ShowPopup=0), 2-Sensor(ShowPopup=1)
        int show_popup = (action.connect_type == 2) ? 1 : 0;
        // 从output字段获取LinkRelayorOutput的值
        int output_value = GetRelayOutputValue(action.output);
        
        WriteDigitalInputConfigs(config, device_index, input_index, output_value, show_popup, action.trigger_model, relay.name, relay.enable_switch);
    }
}

void DevExternRelayConfig::WriteRelayActionConfig(std::stringstream &config, 
                                           const ExtraDeviceRelayActionInfo& action, 
                                           const ExtraDeviceRelayListInfo& relay, 
                                           int device_index,
                                           ExtraDeviceRelay& extra_device_info,
                                           int function_index)
{
    // 解析Output字段
    bool is_extern_relay;
    int output_index;
    if (GetRelayTypeAndIndex(action.output, is_extern_relay, output_index) != 0)
    {
        AK_LOG_WARN << "Unknown output type: " << action.output << " for action uuid: " << action.uuid;
        return;
    }
    
    // 根据继电器类型生成配置
    WriteDeviceConfigByIndex(config, action, relay, device_index, is_extern_relay, extra_device_info, output_index, function_index);
}

void DevExternRelayConfig::WriteR8RelayConfigForDevice(std::stringstream &config, ExtraDeviceRelay& extra_device_info)
{
    AK_LOG_INFO << "Writing RSAC_C1_R8 relay config for device index " << extra_device_info.extra_device_.device_index;
    
    WriteExtraDeviceEnableConfig(config, extra_device_info.extra_device_.device_index, extra_device_info.extra_device_.enable_switch, extra_device_info.extra_device_.device_address);
    
    // 如果设备开关关闭，跳过后续relay配置
    if (extra_device_info.extra_device_.enable_switch == 0)
    {
        return;
    }
    
    // 循环所有的relay，为每个relay下的action生成配置
    int shutter_index = 0;
    int shade_index = 0;
    for (const auto& relay : extra_device_info.relay_list_)
    {
        
        // 确保继电器属于当前设备
        if (strcmp(relay.extra_device_uuid, extra_device_info.extra_device_.uuid) != 0)
        {
            continue;
        }
        
        // 根据relay类型确定索引，对于同一种function可以配置up down的，需要用不同index的autop
        int function_index = 0;
        if (relay.function == dbinterface::RELAY_FUNCTION_SHUTTER)
        {
            function_index = shutter_index++;
        }
        else if (relay.function == dbinterface::RELAY_FUNCTION_SHADE)
        {
            function_index = shade_index++;
        }
        
        // 获取该relay下的所有actions
        auto actions_range = extra_device_info.relay_uuid_to_actions_map_.equal_range(relay.uuid);
        for (auto action_it = actions_range.first; action_it != actions_range.second; ++action_it)
        {
            const auto& action = action_it->second;
            
            AK_LOG_INFO << "Processing action: RelayID=" << relay.relay_id 
                       << ", Output=" << action.output
                       << ", ActionType=" << action.action_type
                       << ", RelayName=" << relay.name;
            
            // 写入relay配置
            WriteRelayActionConfig(config, action, relay, extra_device_info.extra_device_.device_index, extra_device_info, function_index);
        }
    }
}

void DevExternRelayConfig::WriteLegacyRelayConfigForDevice(std::stringstream &config, ExtraDeviceRelay& extra_device_info)
{
    // 为MK48和HF等旧类型设备生成配置，使用旧的配置格式
    AK_LOG_INFO << "Writing legacy relay config for device index " << extra_device_info.extra_device_.device_index;
    
    // 如果设备开关关闭，跳过后续relay配置
    if (extra_device_info.extra_device_.enable_switch == 0)
    {
        AK_LOG_INFO << "Device enable_switch is OFF, skipping relay config for device: " << extra_device_info.extra_device_.uuid;
        return;
    }
     
    // 循环所有的relay，为每个relay下的action生成配置
    for (const auto& relay : extra_device_info.relay_list_)
    {
        // 确保继电器属于当前设备
        if (strcmp(relay.extra_device_uuid, extra_device_info.extra_device_.uuid) != 0)
        {
            AK_LOG_WARN << "Skipping relay " << relay.relay_id << " - belongs to different device: " << relay.extra_device_uuid;
            continue;
        }
        
        // 获取该relay下的所有actions
        auto actions_range = extra_device_info.relay_uuid_to_actions_map_.equal_range(relay.uuid);
        for (auto action_it = actions_range.first; action_it != actions_range.second; ++action_it)
        {
            const auto& action = action_it->second;
            
            int output_index = GetRelayOutputValue(action.output);
            if (output_index == 0 || output_index > 8) 
            {
                AK_LOG_WARN << "Invalid output format for legacy device: " << action.output << " (parsed index: " << output_index << ")";
                continue;
            }
            
            // 计算功能值（Legacy设备不会有SHUTTER/SHADE类型，直接返回原始值）
            int function_value = extra_device_info.CalculateFunctionValue(relay, action.action_type, 0);
            
            config << CONFIG_INDOOR_EXTRELAY_STATUS << output_index << "=" << relay.enable_switch << "\n";
            config << CONFIG_INDOOR_EXTRELAY_FUNCTION << output_index << "=" << function_value << "\n";
            config << CONFIG_INDOOR_EXTRELAY_DISPLAY_NAME << output_index << "=" << relay.name << "\n";
            // 只有灯才下发HOLD_DELAY和INTERVAL配置。因为其它类型，云端没有配置入口。
            if (function_value == dbinterface::FunctionDeviceValues::FUNCTION_DEVICES_VALUE_LIGHT)
            {
                config << CONFIG_INDOOR_EXTRELAY_HOLD_DELAY << output_index << "=" << ConvertExtraRelayHoldDelayForDevice(ATOI(action.hold_delay)) << "\n";
                config << CONFIG_INDOOR_EXTRELAY_INTERVAL << output_index << "=" << ConvertExtraRelayHoldDelayForDevice(ATOI(action.hold_delay)) << "\n";
            }
        }
    }
}