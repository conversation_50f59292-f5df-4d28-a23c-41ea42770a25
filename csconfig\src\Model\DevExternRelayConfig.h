#ifndef __DEV_EXTERN_RELAY_CONFIG_H__
#define __DEV_EXTERN_RELAY_CONFIG_H__

#include <string>
#include <sstream>
#include <functional>
#include "UpdateConfigContext.h"
#include "dbinterface/resident/ExtraDevice.h"
#include "dbinterface/resident/ExtraDeviceRelayList.h"
#include "dbinterface/resident/ExtraDeviceRelayAction.h"
#include "ExtraDeviceRelayConfig.h"

// 外接Relay配置功能类
class DevExternRelayConfig
{
public:
    DevExternRelayConfig(ConfigContextPtr context);
    ~DevExternRelayConfig() = default;

    // 写入外接Relay配置
    void WriteExternRelayConfig(std::stringstream &config, const std::string &dev_uuid);

private:


    // 写入继电器列表配置
    void WriteExRelayListConfig(std::stringstream &config, const IndoorMonitorConfigInfo* indoor_monitor_config);



    // 根据设备索引和继电器类型生成配置
    void WriteDeviceConfigByIndex(std::stringstream &config, 
                                const ExtraDeviceRelayActionInfo& action,
                                const ExtraDeviceRelayListInfo& relay, 
                                int device_index, 
                                bool is_extern_relay,
                                ExtraDeviceRelay& extra_device_info,
                                int output_index,
                                int function_index);

    // 写入继电器动作配置
    void WriteRelayActionConfig(std::stringstream &config, 
                              const ExtraDeviceRelayActionInfo& action, 
                              const ExtraDeviceRelayListInfo& relay, 
                              int device_index,
                              ExtraDeviceRelay& extra_device_info,
                              int function_index);

    // R8类型设备配置
    void WriteR8RelayConfigForDevice(std::stringstream &config, 
                                    ExtraDeviceRelay& extra_device_info);
    
    // 传统类型设备配置
    void WriteLegacyRelayConfigForDevice(std::stringstream &config, 
                                        ExtraDeviceRelay& extra_device_info);

    ConfigContextPtr context_;  // 配置上下文指针
};

#endif // __DEV_EXTERN_RELAY_CONFIG_H__ 