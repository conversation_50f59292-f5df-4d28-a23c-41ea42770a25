#include <sstream>
#include "DevKey.h"
#include <string.h>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "ConfigDef.h" 
#include "AdaptUtility.h"
#include "json/json.h"
#include "util.h"
#include "AkLogging.h"
#include "DeviceSetting.h"
#include "AkcsWebMsgSt.h"
#include "CommunityMng.h"
#include "DeviceControl.h"
#include "AES256.h"
#include "AkcsMonitor.h"
#include "encrypt/Md5.h"
#include "CharChans.h"
#include "PrivateKeyControl.h"
#include "DevUser.h"
#include "AkcsCommonDef.h"
#include "ShadowMng.h"
#include "PrivateKeyXml.h"
#include "WriteFileControl.h"
#include "dbinterface/PersonalRfcardKey.h"
#include "dbinterface/PersonalPrivateKey.h"


extern CSCONFIG_CONF gstCSCONFIGConf;


int DevKey::UpdateRfKeyFiles(DEVICE_SETTING* device_setting_list, RF_KEY* privatekey_list)
{
    is_private_key_ = 0;
    UpdateKeyFiles(device_setting_list, (PRIVATE_KEY*)privatekey_list);
}

int DevKey::UpdatePrivateKeyFiles(DEVICE_SETTING* device_setting_list, PRIVATE_KEY* privatekey_list)
{
    is_private_key_ = 1;
    UpdateKeyFiles(device_setting_list, privatekey_list);
} 

int DevKey::UpdatePrivateKeyFilesForNewCommunityOldDev(DEVICE_SETTING* dev,  const DevCommKeyPtrList &list)
{
    is_private_key_ = 1;
    int ret = UpdateKeyFilesForNewCommunityOldDev(dev, list);
    return ret;
}
int DevKey::UpdateRfKeyFilesForNewCommunityOldDev(DEVICE_SETTING* dev,  const DevCommKeyPtrList &list)
{
    is_private_key_ = 0;
    int ret = UpdateKeyFilesForNewCommunityOldDev(dev, list);
    return ret;
}

int DevKey::SetCommunityInfo(   CommunityInfoPtr communit_info)
{
    communit_info_ = communit_info;
    return 0;
}


//更新个人终端用户的key配置表
int DevKey::UpdateKeyFiles(DEVICE_SETTING* device_setting_list, PRIVATE_KEY* privatekey_list)
{
    if (device_setting_list == NULL)
    {
        AK_LOG_WARN << "UpdateKeyFiles device is NULL.";
        return -1;
    }
    LOG_INFO << "Begin UpdateKeyFiles";
    DEVICE_SETTING* cur_device_setting = device_setting_list;
    //added by chenyc,先遍历每一个设备,然后在内循环中,比较该设备是否与key对应的所有管辖设备匹配
    while (cur_device_setting != NULL)
    {
        LOG_INFO << "UpdateKeyFiles mac: " << cur_device_setting->mac;
        if (communit_info_ && communit_info_->GetIsNew())//新社区:通过user,或者user里面数据转换
        {
            cur_device_setting = cur_device_setting->next;
            break;
        }
        //只有DOOR/STAIR/WALL才需要生成PRIVATEKEY和RFKEY
        if ((cur_device_setting->type != DEVICE_TYPE_WALL) && (cur_device_setting->type != DEVICE_TYPE_DOOR) \
            && (cur_device_setting->type != DEVICE_TYPE_STAIR) && (cur_device_setting->type != DEVICE_TYPE_ACCESS))
        {
            cur_device_setting = cur_device_setting->next;
            continue;
        }

        std::vector<int> unit_list;
        int manage_all_flag = 1;
        if (csmain::COMMUNITY_DEVICE_TYPE_PUBLIC == device_grade_ &&
            GetDeviceControlInstance()->DeviceIsManageBuilding(cur_device_setting->type))
        {
            manage_all_flag = GetDeviceSettingInstance()->GetManagementBuilding(cur_device_setting->id, unit_list);
        }

        PRIVATE_KEY* new_privatekey_list = NULL;
        PRIVATE_KEY* new_cur_privatekey = NULL;
        //遍历PRIVATEKEY，为该设备生成一个新的PRIVATEKEY列表
        PRIVATE_KEY* cur_privatekey = privatekey_list;
        while (cur_privatekey != NULL)
        {
            /**
             * V4.3删除社区用户时候会把code=‘’,不会删除数据;
             * 如果该用户所属单元不在设备的关联楼栋
             */
            if (strlen(cur_privatekey->code) == 0)
            {
                cur_privatekey = cur_privatekey->next;
                continue;
            }
            else
            {
                PRIVATE_KEY *new_node = new PRIVATE_KEY;
                memcpy(new_node, cur_privatekey, sizeof(PRIVATE_KEY));
                new_node->next = NULL;
                new_node->access_device_list = NULL;
                if (new_privatekey_list == NULL)
                {
                    new_privatekey_list = new_node;
                }
                else
                {
                    new_cur_privatekey->next = new_node;
                }

                new_cur_privatekey = new_node;
            }
            cur_privatekey = cur_privatekey->next;
        }

        //PM可为个人终端添加Rf、Pri Key
        if (is_private_key_)
        {
            dbinterface::PersonalPrivateKey::GetCommunityMacPrivList(cur_device_setting, &new_privatekey_list);
            dbinterface::PersonalPrivateKey::GetCommunityPerPrivateKey(communit_info_->IsAllowCreatePin(), cur_device_setting, &new_privatekey_list);
        }
        else
        {
            dbinterface::PersonalRfcardKey::GetCommunityMacRfList(cur_device_setting, (RF_KEY**)&new_privatekey_list);
            dbinterface::PersonalRfcardKey::GetCommunityPerRfKey(cur_device_setting, &new_privatekey_list);
        }

        PRIVATE_KEY* final_privatekey_list = NULL;
        if (1 == manage_all_flag)
        {
            final_privatekey_list = new_privatekey_list;
            new_privatekey_list = NULL;
        } else {
            new_cur_privatekey = NULL;
            cur_privatekey = new_privatekey_list;
            while (cur_privatekey != NULL)
            {
                if (!GetDeviceControlInstance()->DeviceIsBelongBuilding(cur_device_setting->type, cur_privatekey->building, unit_list))
                {
                    AK_LOG_INFO << "manage building remove code:device_id=" << cur_device_setting->id << ";type=" << cur_device_setting->type
                        << ";code=" << cur_privatekey->code << ";unit_id=" << cur_privatekey->building << ";name=" << cur_privatekey->user;
                    cur_privatekey = cur_privatekey->next;
                    continue;
                }
                PRIVATE_KEY *new_node = new PRIVATE_KEY;
                memcpy(new_node, cur_privatekey, sizeof(PRIVATE_KEY));
                new_node->next = NULL;
                new_node->access_device_list = NULL;

                if (final_privatekey_list == NULL)
                {
                    final_privatekey_list = new_node;
                }
                else
                {
                    new_cur_privatekey->next = new_node;
                }
                new_cur_privatekey = new_node;

                cur_privatekey = cur_privatekey->next;
            }
        }


        //获取文件名
        char file_path[MAX_FILE_PATH] = "";
        memset(file_path, 0, sizeof(file_path));
        CString path = _T("");
        if (is_private_key_)
        {
            path = config_root_path_ + cur_device_setting->mac + ".xml";
            AK_LOG_INFO << "The Privatekey file path is " << path;
        }
        else
        {
            path = config_root_path_ + cur_device_setting->mac + ".xml";
            AK_LOG_INFO << "The Rfid file path is " << path;
        }
        TransTcharToUtf8(path.GetBuffer(), file_path, sizeof(file_path));
        
        int default_relay = 0;  
        if(D_CLIENT_VERSION_5200 > cur_device_setting->dclient_version)
        {
            default_relay = 7;  //旧版本只支持doornum=123
        }
        else
        {
            GetValueByRelay(cur_device_setting->relay, default_relay);
        }
        int default_security_relay = 0;
        if (cur_device_setting->dclient_version >= D_CLIENT_VERSION_6400)
        {
            GetValueByRelay(cur_device_setting->security_relay, default_security_relay);
        }

        std::string file_content;
        community_create_private_key_xml(file_content, file_path, final_privatekey_list, default_relay, default_security_relay);

        SHADOW_TYPE ftype = SHADOW_TYPE::SHADOW_NONE; 
        int project = project::NONE;
        if (is_private_key_)
        {
            ftype = SHADOW_PRIKEY;
        }
        else
        {
            ftype = SHADOW_RFID;
        }
      
        DevFileInfoPtr ptr = std::make_shared<DevFileInfo>(cur_device_setting->mac, file_path, file_content, ftype,
                                                            project::RESIDENCE, cur_device_setting->id);
        GetWriteFileControlInstance()->AddFileInfo(cur_device_setting->mac, ptr);

        //销毁新生成的KEY列表
        GetPrivateKeyControlInstance()->DestoryPrivateKeyList(new_privatekey_list);
        new_privatekey_list = NULL;

        
        GetPrivateKeyControlInstance()->DestoryPrivateKeyList(final_privatekey_list);
        final_privatekey_list = NULL;

        cur_device_setting = cur_device_setting->next;
    }

    return 0;
}


int DevKey::UpdateKeyFilesForNewCommunityOldDev(DEVICE_SETTING* dev,  const DevCommKeyPtrList &list)
{
    if (dev == NULL)
    {
        AK_LOG_WARN << "UpdateKeyFilesForNewCommunityOldDev device is NULL.";
        return -1;
    }

    //只有DOOR/STAIR/WALL才需要生成PRIVATEKEY和RFKEY
    if ((dev->type != DEVICE_TYPE_WALL) && (dev->type != DEVICE_TYPE_DOOR) \
        && (dev->type != DEVICE_TYPE_STAIR) && (dev->type != DEVICE_TYPE_ACCESS))
    {
        return -1;
    }

    std::vector<int> unit_list;
    int manage_all_flag = 1;
    if (csmain::COMMUNITY_DEVICE_TYPE_PUBLIC == dev->grade &&
        GetDeviceControlInstance()->DeviceIsManageBuilding(dev->type))
    {
        manage_all_flag = GetDeviceSettingInstance()->GetManagementBuilding(dev->id, unit_list);
    }

    PRIVATE_KEY* private_list = NULL;
      
    for (auto key : list)
    {
        if ((1 != manage_all_flag) && !GetDeviceControlInstance()->DeviceIsBelongBuilding(dev->type, key->building, unit_list))
        {
            continue;
        }
        PRIVATE_KEY *new_node = new PRIVATE_KEY;
        memcpy(new_node, key.get(), sizeof(PRIVATE_KEY));
        new_node->next = NULL;
        new_node->access_device_list = NULL;

        if (private_list == NULL)
        {
            private_list = new_node;
        }
        else
        {
            if (private_list->next == NULL)
            {
                private_list->next = new_node;
            }
            else
            {
                PRIVATE_KEY * tmp = private_list->next;
                private_list->next = new_node;
                new_node->next = tmp;
            }
        }
    }


    //获取文件名
    char file_path[MAX_FILE_PATH] = "";
    memset(file_path, 0, sizeof(file_path));
    CString str_path = _T("");
    if (is_private_key_)
    {
        str_path = config_root_path_ + dev->mac + ".xml";
        AK_LOG_INFO << "The new Privatekey file path is " << str_path;
    }
    else
    {
        str_path = config_root_path_ + dev->mac + ".xml";
        AK_LOG_INFO << "The new Rfid file path is " << str_path;
    }

    TransTcharToUtf8(str_path.GetBuffer(), file_path, sizeof(file_path));
    
    int default_relay = 0;  
    if(D_CLIENT_VERSION_5200 > dev->dclient_version)
    {
        default_relay = 7;  //旧版本只支持doornum=123
    }
    else
    {
        GetValueByRelay(dev->relay, default_relay);
    }
    int default_security_relay = 0;
    if (dev->dclient_version >= D_CLIENT_VERSION_6400)
    {
        GetValueByRelay(dev->security_relay, default_security_relay);
    }

    std::string file_content;
    community_create_private_key_xml(file_content, file_path, private_list, default_relay, default_security_relay);
    
    SHADOW_TYPE ftype = SHADOW_TYPE::SHADOW_NONE; 
    int project = project::RESIDENCE;
    if (is_private_key_)
    {
        ftype = SHADOW_PRIKEY;
    }
    else
    {
        ftype = SHADOW_RFID;
    }
    
    DevFileInfoPtr ptr = std::make_shared<DevFileInfo>(dev->mac, file_path, file_content, ftype,
                                                        project, dev->id);
    GetWriteFileControlInstance()->AddFileInfo(dev->mac, ptr);
    GetPrivateKeyControlInstance()->DestoryPrivateKeyList(private_list);
    private_list = NULL;    

    return 0;
}


