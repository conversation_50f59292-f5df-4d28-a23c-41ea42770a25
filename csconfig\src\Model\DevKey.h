#ifndef __DEV_KEY_H__
#define __DEV_KEY_H__
#include <string>
#include "dbinterface/CommunityInfo.h"
#include "AKCSMsg.h"



class DevKey
{
public:
	DevKey(  const std::string& config_root_path, int community_device_type)
    {
        config_root_path_ = config_root_path;
        device_grade_ = community_device_type;
        communit_info_ = nullptr;
    }

	~DevKey()
    {
        
    }
    int UpdateRfKeyFiles(DEVICE_SETTING* device_setting_list, RF_KEY* privatekey_list);
    int UpdatePrivateKeyFiles(DEVICE_SETTING* device_setting_list, PRIVATE_KEY* privatekey_list);
    int UpdatePrivateKeyFilesForNewCommunityOldDev(DEVICE_SETTING* dev,  const DevCommKeyPtrList &list);
    int UpdateRfKeyFilesForNewCommunityOldDev(DEVICE_SETTING* dev,  const DevCommKeyPtrList &list);
    int SetCommunityInfo(   CommunityInfoPtr communit_info);
    
private:
    int UpdateKeyFilesForNewCommunityOldDev(DEVICE_SETTING* dev,  const DevCommKeyPtrList &list);
    int UpdateKeyFiles(DEVICE_SETTING* device_setting_list, PRIVATE_KEY* privatekey_list);
    int is_private_key_;
    int device_grade_;
    std::string config_root_path_;
    CommunityInfoPtr communit_info_;
};

#endif 
