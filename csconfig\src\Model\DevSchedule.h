#ifndef __DEV_SCHEDULE_H__
#define __DEV_SCHEDULE_H__
#include <string>
#include "dbinterface/CommunityInfo.h"
#include "AKCSMsg.h"
#include "dbinterface/AccessGroupDB.h"



class DevSchedule
{
public:
    enum SchedType
    {
        ONCE_SCHED = 0,
        DAILY_SCHED,
        WEEKLY_SCHED,
        ONCE_SCHED_WITH_TIME, // never模式，但支持time格式自定义, 目前只有app设置门常开会使用
    };

public:
	DevSchedule(   CommunityInfoPtr communit_info);
	~DevSchedule();

    int UpdateScheduleData(DEVICE_SETTING* dev_list);
    
    
private:
    int WirteFile(const std::string &filename, const std::string &content);
    int WirteScheduleToJson(DEVICE_SETTING* dev, AccessGroupInfoPtrList &ag_list);

    //传给设备的类型(和平台数据库不一致)
    enum DevSchedType
    {
        DEV_SCHE_ONCE_SCHED = 0,
        DEV_SCHE_WEEKLY_SCHED,
        DEV_SCHE_DAILY_SCHED,
    };

    CommunityInfoPtr communit_info_;
};

#endif 
