#include <sstream>
#include "DevUser.h"
#include <string.h>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "ConfigDef.h" 
#include "AdaptUtility.h"
#include "json/json.h"
#include "util.h"
#include "AkLogging.h"
#include "DeviceSetting.h"
#include "AkcsWebMsgSt.h"
#include "CommunityMng.h"
#include "DeviceControl.h"
#include "AES256.h"
#include "AkcsMonitor.h"
#include "encrypt/Md5.h"
#include "CharChans.h"
#include "PrivateKeyControl.h"
#include "AdaptUtility.h"
#include "DevKey.h"
#include "DevSchedule.h"
#include "AkcsMonitor.h"
#include "AkcsCommonDef.h"
#include "ShadowMng.h"
#include "dbinterface/resident/ResidentPersonalAccount.h" 
#include "dbinterface/PmAccountMap.h" 
#include "dbinterface/LadderControl.h" 
#include "dbinterface/PersonalAccountCnf.h"
#include "dbinterface/AccessGroupDB.h" 
#include "dbinterface/UserAccessGroup.h"
#include "WriteFileControl.h"
#include "dbinterface/resident/ResidentDevices.h" 
#include "dbinterface/PubDevMngList.h"
#include "ShadowUserDetailMng.h"
#include "dbinterface/PersonalAccountCommunityInfo.h"
static const DULONG all_user_info_traceid=*********;
extern CSCONFIG_CONF gstCSCONFIGConf;

DevUser::DevUser(CommunityInfoPtr communit_info)
{
    communit_info_ = communit_info;
}

DevUser::~DevUser()
{
    
}

int DevUser::DevSupportUser(int declient_ver)
{
    if (declient_ver >= D_CLIENT_VERSION_6100)
    {
        return 1;
    }
    return 0;
}

int DevUser::DevTypeSupportUser(int       dev_typ)
{
    if( DEVICE_TYPE_DOOR == dev_typ ||
        DEVICE_TYPE_STAIR == dev_typ ||
        DEVICE_TYPE_ACCESS == dev_typ) 
    {
        return true;
    }

    return false;

}

void DevUser::GetUserHoldDoorAccessGroupList(DEVICE_SETTING* dev, AccessGroupInfoPtrList &ag_list)
{
    HoldDoorInfoPtrList hold_door_info_ptr_list;
    if (0 != dbinterface::UserAccessGroup::GetDeviceHoldDoorList(dev, hold_door_info_ptr_list))
    {
        AK_LOG_INFO << "GetDevAccessGroupList GetDeviceHoldDoorList failed, dev_uuid = " << dev->uuid;
        return;
    }
    for (const auto& hold_door_info_ptr : hold_door_info_ptr_list)
    {
        AccessGroupInfoPtr custom_ag = std::make_shared<AccessGroupInfo>();
        custom_ag->id_ = hold_door_info_ptr->latest_user_access_group_id;
        Snprintf(custom_ag->day_start_, sizeof(custom_ag->day_start_), hold_door_info_ptr->day_start_);
        Snprintf(custom_ag->day_end_, sizeof(custom_ag->day_end_), hold_door_info_ptr->day_end_);
        custom_ag->relay_ = hold_door_info_ptr->relay;
        Snprintf(custom_ag->day_start_for_ymd_, sizeof(custom_ag->day_start_for_ymd_), hold_door_info_ptr->day_start_for_ymd_);
        Snprintf(custom_ag->day_end_for_ymd_, sizeof(custom_ag->day_end_for_ymd_), hold_door_info_ptr->day_end_for_ymd_);
        Snprintf(custom_ag->time_start_, sizeof(custom_ag->time_start_), hold_door_info_ptr->time_start_);
        Snprintf(custom_ag->time_end_, sizeof(custom_ag->time_end_), hold_door_info_ptr->time_end_);
        custom_ag->scheduler_type_ = DevSchedule::SchedType::ONCE_SCHED_WITH_TIME;
        custom_ag->date_flag_ = 0;
        Snprintf(custom_ag->name_, sizeof(custom_ag->name_), dev->device_node);
        custom_ag->security_relay_ = 0;
        ag_list.push_back(custom_ag);
    }
    return;
}

void DevUser::GetDevAccessGroupList(DEVICE_SETTING* dev, AccessGroupInfoPtrList &ag_list)
{
    if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC
        || dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
       dbinterface::AccessGroup::GetPubMacAccessGroupList(dev, ag_list);
       dbinterface::AccessGroup::GetDefaultAccessGroupList(dev, ag_list);
       if (ag_list.size() == 0)
       {
            AK_LOG_INFO << "Public/Unit MAC=" << dev->mac << " Access Group List is null";
       }
    }
    else if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
    {
       dbinterface::UserAccessGroup::GetUserMacAccessGroupList(dev, ag_list);
       if (ag_list.size() == 0)
       {
            AK_LOG_INFO << "Personal MAC=" << dev->mac << " Access Group List is null";
       }     
    }
    else
    {
        AK_LOG_WARN << "WriteMetaData device grade error. MAC=" << dev->mac << " Grade=" << dev->grade;
    }    
}

void DevUser::GetDevUserAccessMateInfoList(DEVICE_SETTING* dev, uint32_t ag_id, UserAccessInfoPtrMap &userlist)
{
    UserAccess ua;    
    if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC
        || dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
       ua.GetPubDevAccountListByAccessGroupID(ag_id, userlist);
       ua.GetPubDevPubAccountListByAccessGroupID(ag_id, userlist);
       if (userlist.size() == 0)
       {
            AK_LOG_INFO << "Public/Unit MAC=" << dev->mac << " access group id=" << ag_id << " user access List is null";
       }         

    }
    else if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
    { 
       ua.GetPerDevAccountListByAccessGroupID(ag_id, userlist);
       if (userlist.size() == 0)
       {
            AK_LOG_INFO << "Personal MAC=" << dev->mac << " access group id=" << ag_id << " user access List is null";
       }        
    }   
}

int DevUser::WirteFile(const std::string &filename, const std::string &content)
{
    //user detail
    if(!ThreadLocalSingleton::GetInstance().GetDbStatus())
    {
        AK_LOG_ERROR << "Alarm Monitoring: Db error. Pause write user detail file:" << filename;
        return -1;    
    }
    
    FILE* file = fopen(filename.c_str(), "w+");
    if (file == NULL)
    {
        AK_LOG_WARN << "fopen failed " << filename;       
        AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csconfig", filename, AKCS_MONITOR_ALARM_CONFIG_WRITE_FAILED);
        return -1;
    }
    //将配置信息内容写入文件中
    fwrite(content.c_str(), sizeof(char), strlen(content.c_str()) + 1, file);
    fclose(file);
    AK_LOG_INFO << "The user file path is " << filename;

    //加密
    if (!gstCSCONFIGConf.no_encrypt)
    {
        FileAESEncrypt(filename.c_str(), AES_ENCRYPT_KEY_V1, filename.c_str());
    }
    return 0;

}


std::string  DevUser::GetUserAccessInfoKey(uint32_t group_id, DEVICE_SETTING* dev)
{
    //group_id 用户自己的/公共的  
    //DevSupportUser(dev)  是不是包含用户全部信息
    std::stringstream key;
    key << "agid=" << group_id << " devtype=" << dev->grade << " support user:" << DevSupportUser(dev->dclient_version);
    return key.str();
}


int DevUser::UpdateMetaData(DEVICE_SETTING* dev_list)
{
    AK_LOG_INFO << "UpdateMetaData Start.";
    if (dev_list == NULL)
    {
        AK_LOG_WARN << "WriteMetaData device is NULL.";
        return -1;
    }
    if (!communit_info_->GetIsNew())
    {
        AK_LOG_INFO << "communitid [" << dev_list->manager_account_id << "] not new commynity, not required update user meta file";
        return 0;
    }

    AgKeyUaListCacheMap ag_key_ualist_cache_map;
    DEVICE_SETTING* cur_dev = dev_list;
    while (cur_dev != NULL)
    {    
        if(!DevTypeSupportUser(cur_dev->type))
        {
            AK_LOG_INFO << "devices mac=" << cur_dev->mac << " type=" << cur_dev->type << " not support user.";
            cur_dev = cur_dev->next;
            continue; 
        }
        AccessGroupInfoPtrList ag_list;
        GetDevAccessGroupList(cur_dev, ag_list);
        if (ag_list.size() <= 0)
        {
            AK_LOG_INFO << "devices mac=" << cur_dev->mac << " not bind access group. need clear data.";
            //cur_dev = cur_dev->next;
            //continue; //需要往下执行清空user
        }
        
        FilterAccessGroups(cur_dev, ag_list);
        
        AgUaListPairList ag_ua_list;
        UserAccessInfoPtrMap all_ua_list;
        
        for (auto ag : ag_list)
        {   
            //不需要一直查询,多个设备间会有重复的权限组信息
            UserAccessInfoPtrMap ua_list;
            std::string key = GetUserAccessInfoKey(ag->id_, cur_dev);

            std::map<std::string, UserAccessInfoPtrMap>::iterator it;            
            it = ag_key_ualist_cache_map.find(key);
            if(it != ag_key_ualist_cache_map.end())
            {
                ua_list = it->second;
                //AK_LOG_INFO << "already exist ua key:" << key;
            }
            else
            {
                GetDevUserAccessMateInfoList(cur_dev, ag->id_, ua_list);
                ag_key_ualist_cache_map.insert(std::make_pair(key, ua_list));
            }
            
            AgUaListPair pair(ag, ua_list);
            ag_ua_list.push_back(pair);
            all_ua_list.insert(ua_list.begin(), ua_list.end());
        }
        

        if (DevSupportUser(cur_dev->dclient_version))
        {
            WriteMetaDataToJson(cur_dev, all_ua_list);
        }
        else
        {
            //获取详细数据
            UserAccess ua;
            ua.GetPerUserDetailInfo(all_ua_list);        
            WriteOldCommunityInfo(cur_dev, ag_ua_list, all_ua_list, ag_list);
        }

        cur_dev = cur_dev->next;
    } 
    AK_LOG_INFO << "UpdateMetaData End.";
    return 0;
}

void DevUser::FilterAccessGroups(DEVICE_SETTING* cur_dev, AccessGroupInfoPtrList& ag_list)
{
    //是否对楼栋全管理
    int is_all_manage =dbinterface::SwitchHandle(cur_dev->flags, DeviceSwitch::DEV_MNG_ALL);
    //如果设备是公共设备，且非全管理楼栋，且公共设备没有某楼栋的管理权限，则应该过滤掉该楼栋的默认权限组
    if (cur_dev->grade != csmain::COMMUNITY_DEVICE_TYPE_PUBLIC || is_all_manage) {
        return;
    }
    //获取公共设备的管理楼栋
    std::vector<int> unit_id_list;
    dbinterface::PubDevMngList::GetManagementBuildingListById(cur_dev->id, unit_id_list);
    AccessGroupInfoPtrList filtered_list;
    for (const auto& ag : ag_list)
    {
        //设备楼栋权限组的 unit_id_ != 0
        if(ag->unit_id_ != 0)
        {   
            //判断是否在管理的权限组里面，不在的话就过滤掉。
            if (std::find(unit_id_list.begin(), unit_id_list.end(), ag->unit_id_) == unit_id_list.end())
            {
                continue;
            }
        }
        filtered_list.push_back(ag);
    }
    ag_list = std::move(filtered_list);
    
}

int DevUser::UpdatePubDevMetaByPubUser(const std::vector<uint32_t> &staff_ids, const std::vector<uint32_t> &delivery_ids, std::set<std::string> &ret_mac_set)
{
    AK_LOG_INFO << "UpdatePubDevMetaByPubUser Start.";
    dbinterface::AccessGroup::GetPubUserAccessGroupDevList(USER_TYPE::STAFF, staff_ids, ret_mac_set);
    dbinterface::AccessGroup::GetPubUserAccessGroupDevList(USER_TYPE::DELIVERY, delivery_ids, ret_mac_set);

    if (ret_mac_set.size() > 0)
    {
        DEVICE_SETTING* devlist = GetDeviceSettingInstance()->GetDeviceSettingByMacList(ret_mac_set);
        UpdateMetaData(devlist);
        
        GetDeviceControlInstance()->DestoryDeviceSettingList(devlist);
    }
    AK_LOG_INFO << "UpdatePubDevMetaByPubUser End.";
    return 0;
}

/*更新公共设备的user*/
int DevUser::UpdatePubDevMetaByAccount(const std::vector<std::string> &account_list, std::set<std::string> &ret_mac_set)
{
    AK_LOG_INFO << "UpdatePubDevMetaByAccount Start.";
    if (account_list.size() > 0 )
    {
        dbinterface::AccessGroup::GetAccessGroupDevListByUser(account_list, ret_mac_set);
        
        DEVICE_SETTING* devlist = GetDeviceSettingInstance()->GetDeviceSettingByMacList(ret_mac_set);
        UpdateMetaData(devlist);
        
        GetDeviceControlInstance()->DestoryDeviceSettingList(devlist);
    }
    AK_LOG_INFO << "UpdatePubDevMetaByAccount End.";
    return 0;
}

/*更新用户设备的user*/
int DevUser::UpdateUserDevMetaByAccount(const std::vector<std::string> &account_list, std::set<std::string> &ret_mac_set)
{
    AK_LOG_INFO << "UpdateUserDevMetaByAccount Start.";
    if (account_list.size() > 0 )
    {
        dbinterface::UserAccessGroup::GetUserAccessGroupDevList(account_list, ret_mac_set);
        
        DEVICE_SETTING* devlist = GetDeviceSettingInstance()->GetDeviceSettingByMacList(ret_mac_set);
        UpdateMetaData(devlist);
        
        GetDeviceControlInstance()->DestoryDeviceSettingList(devlist);
    }
    AK_LOG_INFO << "UpdateUserDevMetaByAccount End.";
    return 0;
}

/*更新家庭下设备的user*/
int DevUser::UpdateUserDevMetaByNodes(const std::vector<std::string> &node_list, std::set<std::string> &ret_mac_set)
{
    AK_LOG_INFO << "UpdateUserDevMetaByNodes Start.";
    dbinterface::ResidentDevices::GetNodesDevList(node_list, ret_mac_set);
    if (ret_mac_set.size() > 0 )
    {
        DEVICE_SETTING* devlist = GetDeviceSettingInstance()->GetDeviceSettingByMacList(ret_mac_set);
        UpdateMetaData(devlist);
        GetDeviceControlInstance()->DestoryDeviceSettingList(devlist);  
    }
    AK_LOG_INFO << "UpdateUserDevMetaByNodes End.";
    return 0;
}

int DevUser::UpdatePubDevMetaByAccessGroupID(const std::vector<uint32_t> &ag_ids, std::set<std::string> &ret_mac_set)
{
    AK_LOG_INFO << "UpdatePubDevMetaByAccessGroupID Start.";
    for (const auto& id : ag_ids)
    {
        if (id > 0)
        {
            dbinterface::AccessGroup::GetMacListByAccessGroupID(id, ret_mac_set);
        }
    } 
    if (ret_mac_set.size() > 0 )
    {
        DEVICE_SETTING* devlist = GetDeviceSettingInstance()->GetDeviceSettingByMacList(ret_mac_set);
        UpdateMetaData(devlist);
        GetDeviceControlInstance()->DestoryDeviceSettingList(devlist);
    }
    AK_LOG_INFO << "UpdatePubDevMetaByAccessGroupID End.";
}


int DevUser::CreateRequestUserListDetailData(DEVICE_SETTING* dev, const UserUUIDList &user_list, DULONG traceid, 
                                                      std::string &file_path,std::string &file_md5)
{
    int ret = -1;
    if (dev == NULL)
    {
        AK_LOG_WARN << "WriteMetaData device is NULL.";
        return ret;
    }
    if (!DevSupportUser(dev->dclient_version))
    {
        AK_LOG_WARN << "device not support user concept.";
        return ret;
    }

    AccessGroupInfoPtrList ag_list;
    GetDevAccessGroupList(dev, ag_list);
    if (ag_list.size() <= 0)
    {
        AK_LOG_WARN << "UpdateMetaData device " << dev->mac << " not found Access Group List!";
        return ret;
    }

    //权限组对应的人员列表
    AgUaListPairList ag_ua_list;
    UserAccessInfoPtrMap all_ua_list;
    for (auto ag : ag_list)
    {
        UserAccessInfoPtrMap ua_list;
        GetDevUserAccessMateInfoList(dev, ag->id_, ua_list);
        AgUaListPair pair(ag, ua_list);
        ag_ua_list.push_back(pair);

        all_ua_list.insert(ua_list.begin(), ua_list.end());
    }

    UserAccessInfoPtrMap tmp_dev_ua_list;
    for (const auto uuid : user_list)
    {
        UserAccessInfoPtrMapIter it;
        it = all_ua_list.find(uuid);
        if (it != all_ua_list.end())
        {
            tmp_dev_ua_list.insert(*it);
        }
        else
        {
            AK_LOG_INFO << "Devices request uid=" << uuid << " not found";
        }
    }

    
    if (traceid == USER_MAINTANCE_ALL_TRACEID)//维护过来的接口直接写全部
    {
        UserAccess ua; 
        ua.GetPerUserDetailInfo(all_ua_list);
        ret = WriteDetailDataForGiven(dev, ag_ua_list, all_ua_list, traceid, file_path);
    }
    else
    {
        UserAccess ua; 
        ua.GetPerUserDetailInfo(tmp_dev_ua_list);
        ret = WriteDetailDataForGiven(dev, ag_ua_list, tmp_dev_ua_list, traceid, file_path);
    }
    file_md5 = dev->user_info_md5; 
    return ret;
}


int DevUser::GetDetailDataForRequest(DEVICE_SETTING* dev, const UserUUIDList &user_list, DULONG traceid, std::string &file_path, std::string &file_md5)
{
    return CreateRequestUserListDetailData(dev, user_list, traceid, file_path, file_md5);
}

int DevUser::WriteMetaDataToJson(DEVICE_SETTING* dev, const UserAccessInfoPtrMap &user_list)
{
    Json::Value item;
    Json::FastWriter w;
    item["UserType"] = 0;

    std::vector<int> unit_list;
    int manage_all_flag = 1;
    if (csmain::COMMUNITY_DEVICE_TYPE_PUBLIC == dev->grade)
    {
        manage_all_flag = GetDeviceSettingInstance()->GetManagementBuilding(dev->id, unit_list);
    }

    for (auto ua_pair : user_list)
    {
        UserAccessInfoPtr ua = static_cast<UserAccessInfoPtr>(ua_pair.second);
        if (ua->GetUserType() == USER_TYPE::ACCOUNT 
             && (1 != manage_all_flag) 
             && !GetDeviceControlInstance()->DeviceIsBelongBuilding(dev->type, ua->GetUnitID(), unit_list))
        {
            //AK_LOG_INFO << "devices mac=" << dev->mac << " not belong the unit id:" << ua->GetUnitID();
            continue;
        }
        if (ua->GetRole() == ACCOUNT_ROLE_COMMUNITY_PM)
        {
            ResidentPerAccount account;
            memset(&account, 0, sizeof(account));
            std::string uid = ua->GetUUID();
            if (0 != dbinterface::ResidentPersonalAccount::GetUidAccount(uid, account))
            {
                continue;
            }

            //校验pm开关
            if (dbinterface::PmAccountMap::checkPMAppAccountStatus(account.uuid) <= 0)
            {
                continue;
            }
        }
        
        if (ua->GetUUID().size() == 0)
        {
            continue;
        }
        Json::Value user;
        user["PerID"] = ua->GetUUID();
        user["Meta"] = ua->GetMeta();
        item["User"].append(user);
    }   

    std::string msg_json = w.write(item);

    //写入文件
    std::string meta_path = GetCommunityUserRootDir(dev->manager_account_id, dev->unit_id, dev->device_node, dev->grade);
    meta_path += dev->mac;
    meta_path += ".json";

    std::string config_path = meta_path;
    DevFileInfoPtr ptr = std::make_shared<DevFileInfo>(dev->mac, config_path, msg_json, SHADOW_TYPE::SHADOW_USERMETA,
                                                        project::RESIDENCE, dev->id);
    GetWriteFileControlInstance()->AddFileInfo(dev->mac, ptr);   
    return 0;
}

void DevUser::CheckCodeUnique(const std::string &mac, std::set<std::string> &unique_list, const std::list<std::string> &codelist)
{
    std::string keystr;
    int old_count = unique_list.size();
    int count = 0;
    for(auto &code : codelist)
    {
        if (code.size() > 0)
        {
            
            unique_list.insert(code);
            keystr += code;
            keystr += ",";
            count ++;
        }
    }
    int new_count = unique_list.size();
    if (new_count != old_count + count)
    {
        
        std::string error = mac;
        error += " Pin/Rf some of them are not unique:";
        error += keystr;
        //AKCS::Singleton<SystemMonitor>::instance().TriggerMonitorAlarm("csconfig", error, AKCS_MONITOR_ALARM_DATA_ERROR);

    }
}

int DevUser::WriteDetailDataToJson(DEVICE_SETTING* dev, const AgUaListPairList &uag_map, 
     const UserAccessInfoPtrMap &user_list, const std::string &file_path)
{
    Json::Value item;
    Json::FastWriter w;
    
    item["UserType"] = 0;
    
    std::vector<int> unit_list;
    int manage_all_flag = 1;
    if (csmain::COMMUNITY_DEVICE_TYPE_PUBLIC == dev->grade)
    {
        manage_all_flag = GetDeviceSettingInstance()->GetManagementBuilding(dev->id, unit_list);
    }
    
    std::set<std::string> unique_pin_list;
    std::set<std::string> unique_rf_list;
    for (auto ua_pair : user_list)
    {
        UserAccessInfoPtr ua = static_cast<UserAccessInfoPtr>(ua_pair.second);
        if (ua->GetUserType() == USER_TYPE::ACCOUNT 
             && (1 != manage_all_flag) 
             && !GetDeviceControlInstance()->DeviceIsBelongBuilding(dev->type, ua->GetUnitID(), unit_list))
        {
            //AK_LOG_INFO << "devices mac=" << dev->mac << " not belong the unit id:" << ua->GetUnitID();
            continue;
        }

        if (ua->GetRole() == ACCOUNT_ROLE_COMMUNITY_PM)
        {
            ResidentPerAccount account;
            memset(&account, 0, sizeof(account));
            std::string uid = ua->GetUUID();
            if (0 != dbinterface::ResidentPersonalAccount::GetUidAccount(uid, account))
            {
                continue;
            }

            //校验pm开关
            if (dbinterface::PmAccountMap::checkPMAppAccountStatus(account.uuid) <= 0)
            {
                AK_LOG_INFO << "PmAccount app status is 0";
                continue;
            }
        }
        Json::Value schedule;
        Json::Value user;
        if (ua->GetUUID().size() == 0)
        {
            continue;
        }        

        user["PerID"] = ua->GetUUID();
        user["Name"] = ua->GetName();
        user["PerType"] = ua->GetUserType();
        if (ua->GetUserType() == USER_TYPE::ACCOUNT)
        {
            //判断是否为个人设备,以及社区private开关是否开启
            int enable_private_access = CheckEnablePrivateAccess(dev);
         
            user["FaceUrl"] = ua->GetFaceUrl(dev->mac);
            user["FaceMD5"] = ua->GetFaceMd5();

            //判断是否需要下发人脸
            if (!CheckUserEnableFace(enable_private_access, ua))
            {
                user["FaceUrl"] = Json::Value("");
                user["FaceMD5"] = Json::Value("");
            }
            
            //add by xuzr,检查pin高级功能.
            if (communit_info_->CheckFeature(CommunityInfo::FeaturePlan::TAB_DEVICES_CHECK_INDEX_PIN) && !communit_info_->IsExpire())
            {
                user["PriKey"] = ua->GetPinString(communit_info_->AptPinType(), communit_info_->IsAllowCreatePin(), enable_private_access);
            }
            else 
            {
                //如果社区过期则默认允许app创建pin
                user["PriKey"] = ua->GetPinString(communit_info_->AptPinType(), 1, enable_private_access);
            }                 

            //用户创建的ID Access需要校验后下发
            if (ua->isSpecialIDAccess())
            {
                if (CheckUserEnableIDAccess(ua))
                {
                    user["IDAccessMode"] = ua->GetSpecialIDAccessInfo().id_access_mode;
                    user["IDAccessRun"] = ua->GetSpecialIDAccessInfo().id_access_run;
                    user["IDAccessSerial"] = ua->GetSpecialIDAccessInfo().id_access_serial;
                }
            }
            else
            {
                //PM创建的ID Access不下发给私有设备
                if (enable_private_access)
                {
                    user["IDAccessMode"] = ua->GetIDAccessInfo().id_access_mode;
                    user["IDAccessRun"] = ua->GetIDAccessInfo().id_access_run;
                    user["IDAccessSerial"] = ua->GetIDAccessInfo().id_access_serial;
                }
            }

            user["WebRelay"] = ua->GetWebRelay();

            std::stringstream unit_apt;
            std::string floor = kDefaultFloor;
            std::string apt_floor = ua->GetFloor();
            std::string unit_uuid = ua->GetUnitUUID();
            floor = dbinterface::PersonalAccountCommunityInfo::GetAptBuildingAccessFloorInfoListByUUID(ua->GetUUID(), unit_uuid, apt_floor, *dev);
            // 楼栋设备才考虑LiftFloorNum
            if (csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT == dev->grade || csmain::COMMUNITY_DEVICE_TYPE_PUBLIC == dev->grade)
            {
                user["LiftFloorNum"] = floor;
            }

            if (apt_floor == kDefaultFloor)
            {
                //若Floor字段为空,则unit_apt中apt值为RoomName
                unit_apt << ua->GetUnitID() << "-" << ExtractFirstNumber(ua->GetRoomNumber());
            }
            else
            {
                //若Floor字段不为空,则unit_apt中apt值为"Floor+00"
                unit_apt << ua->GetUnitID() << "-" << apt_floor << "00";
            }
            user["UnitApt"] = unit_apt.str();
            
            //未开启private开关,不下发pm创的Card给个人设备,当前只有pm能创建card
            //enduser创建的NFC和Bluetooth需要下发给设备
            if (!enable_private_access)
            {
                user["Card"] = ua->GetEnduserRfString();
            }
            else
            {
                user["Card"] = ua->GetRfString();
            }
            user["Cars"] = ua->GetLicensePlateJson();
        }
        else
        {
            user["Card"] = ua->GetRfString();
            user["FaceMD5"] = ua->GetFaceMd5();   
            user["FaceUrl"] = ua->GetFaceUrl(dev->mac);
            user["PriKey"] = ua->GetPinString(communit_info_->AptPinType());
            user["LiftFloorNum"] = dbinterface::LadderControl::GetFloorByUUID(ua->GetDBUUID(), dev->unit_id,  ua->GetUserType());
            user["IDAccessMode"] = ua->GetIDAccessInfo().id_access_mode;
            user["IDAccessRun"] = ua->GetIDAccessInfo().id_access_run;
            user["IDAccessSerial"] = ua->GetIDAccessInfo().id_access_serial;
        }
        
        //check unique
        std::list<std::string> pin_list;
        std::list<std::string> rf_list;
        ua->GetRFList(rf_list);
        ua->GetPinListForMonitor(pin_list);
        CheckCodeUnique(dev->mac, unique_pin_list, pin_list);
        CheckCodeUnique(dev->mac, unique_rf_list, rf_list);
        
        //通过马上遍历的方式，比起全部组装好【用户-权限组列表】对应关系复杂度更低。
        //因为组装对应关系 需要遍历所有权限组下所有用户，但是其实我们设备发过来的更新列表只有上百个。
        AccessGroupInfoPtrList ag_list;
        for (auto pair : uag_map)
        {
            AccessGroupInfoPtr ag = pair.first;
            UserAccessInfoPtrMap ua_list = pair.second;
            
             UserAccessInfoPtrMapIter it;
             it = ua_list.find(ua->GetUUID());
             if (it != ua_list.end())
             {
                 ag_list.push_back(ag);
             }
        }
        for (auto ag : ag_list)
        {
            Json::Value tmp;
            int default_relay = 0;  
            GetValueByRelay(dev->relay, default_relay);
            std::string door_num = RelayToString(ag->relay_&default_relay);

            int default_security_relay = 0;
            GetValueByRelay(dev->security_relay, default_security_relay);
            std::string security_door = RelayToString(ag->security_relay_ & default_security_relay);

            tmp["ScheduleID"] = std::to_string(ag->id_);
            tmp["Relay"] = door_num;
            if (!security_door.empty())
            {
                tmp["SecurityRelay"] = security_door;
            }
            schedule.append(tmp);
        }

        user["ScheduleRelay"] = schedule;
        
        item["User"].append(user);
    }        

    std::string msg_json = w.write(item);

    //写详细数据有随机文件名不会存在交叉问题，不需要发到写文件队列。
    if (WirteFile(file_path, msg_json))
    {
        return -1;
    }    
    //计算MD5并保存到列表中以便后面一起更新到数据库中
    std::string md5 = akuvox_encrypt::MD5::GetFileMD5(file_path);
    Snprintf(dev->user_info_md5, sizeof(dev->user_info_md5), md5.c_str());
    return 0;
}



int DevUser::WriteDetailDataForGiven(DEVICE_SETTING* dev, const AgUaListPairList &uag_map, const UserAccessInfoPtrMap &user_list,
                                          DULONG traceid, std::string &path)
{
    //写入文件
    std::string download_path;
    std::string detail_path = GetUserDetailDownloadPath(dev->mac, download_path);
    detail_path += dev->mac;
    detail_path += "_";
    detail_path += GetNowTime();
    detail_path += "_";
    detail_path += std::to_string(traceid);
    detail_path += ".json";

    path = download_path;
    path += dev->mac;
    path += "_";
    path += GetNowTime();    
    path += "_";
    path += std::to_string(traceid);
    path += ".json";

    WriteDetailDataToJson(dev, uag_map, user_list, detail_path);  

    auto& pool = ConfigUserDetailFdfsUploaderPool::GetInstance();
    {
        ConfigUserDetailFdfsUploaderPool::UploaderHandle handle(pool);
        std::string path_after;
        if (handle.UploadFile(detail_path, path_after) == 0)
        {
            path = path_after;
        }
    }
    
    return 0;
}

int DevUser::WriteDetailDataForAll(DEVICE_SETTING* dev, const AgUaListPairList &uag_map, const UserAccessInfoPtrMap &user_list)
{
    //写入文件
    std::string all_path = GetCommunityUserAllDetailDir(dev->manager_account_id, dev->unit_id, dev->device_node, dev->grade);
    all_path += dev->mac;
    all_path += ".json";    
    WriteDetailDataToJson(dev, uag_map, user_list, all_path);    
    return 0;
}


void DevUser::HandleOldModelKeyInfo(DEVICE_SETTING* dev, const AgUaListPairList &uag_map, const UserAccessInfoPtrMap &ua_list, 
const AccessGroupInfoPtrList &ag_list, DevCommKeyPtrList &key_ptr_list, DEV_KEY_TYPE type)
{
    std::set<std::string> unique_pin_list;
    std::set<std::string> unique_rf_list;
    for (const auto ua_pair : ua_list)
    {
        UserAccessInfoPtr ua = static_cast<UserAccessInfoPtr>(ua_pair.second);
        std::list<std::string> key_list;
        if (type == DEV_KEY_TYPE::DEV_KEY_PIN)
        {
            if (ua->GetUserType() == USER_TYPE::ACCOUNT)
            {
                //add bu xuzr,检查pin高级功能
                if (communit_info_->CheckFeature(CommunityInfo::FeaturePlan::TAB_DEVICES_CHECK_INDEX_PIN) && 
                !communit_info_->IsExpire())
                {
                    ua->GetPinList(communit_info_->AptPinType(), key_list, communit_info_->IsAllowCreatePin());
                }
                else
                {
                    //如果社区过期或者默认允许app创建pin
                    ua->GetPinList(communit_info_->AptPinType(), key_list, 1);
                }
            }
            else
            {
                ua->GetPinList(CommunityInfo::AptPinTypeEnum::PIN , key_list, communit_info_->IsAllowCreatePin());
            }
            std::list<std::string> pin_list;
            ua->GetPinListForMonitor(pin_list);
            CheckCodeUnique(dev->mac, unique_pin_list, pin_list);
        }
        else if (type == DEV_KEY_TYPE::DEV_KEY_RF)
        {
            ua->GetRFList(key_list);
            CheckCodeUnique(dev->mac, unique_rf_list, key_list);
        }

        for (auto rf : key_list)
        {
            DevCommKeyPtr pk = std::make_shared<DEV_COMM_KEY>();
            Snprintf(pk->code, sizeof(pk->code), rf.c_str());
            Snprintf(pk->user, sizeof(pk->user), ua->GetName().c_str());
            if (ua->GetUserType() == USER_TYPE::DELIVERY)
            {
                pk->type = USER_TYPE::DELIVERY;
                pk->building = 0;//进行楼栋管理的判断，=0不判断楼栋管理
                pk->apt[0] = 0;
            }
            else if (ua->GetUserType() == USER_TYPE::STAFF)
            {
                pk->type = USER_TYPE::STAFF;
                pk->building = 0;
                pk->apt[0] = 0;
            }
            else 
            {
                Snprintf(pk->apt, sizeof(pk->apt), ua->GetRoomNumber().c_str());
                pk->building = ua->GetUnitID();//只有住户有 TODO:需要验证
            }

            AccessGroupInfoPtrList ag_list;
            for (auto pair : uag_map)
            {
                 AccessGroupInfoPtr ag = pair.first;
                 UserAccessInfoPtrMap ua_list = pair.second;
                
                 UserAccessInfoPtrMapIter it;
                 it = ua_list.find(ua->GetUUID());
                 if (it != ua_list.end())
                 {
                     ag_list.push_back(ag);
                 }
            }
            for (auto ag : ag_list)
            {
                if (D_CLIENT_VERSION_5200 > dev->dclient_version)
                {
                    pk->relay = 7;
                }
                else
                {
                    pk->relay = ag->relay_;
                }
                int sche_type = ag->scheduler_type_;
                if (DevSchedule::SchedType::ONCE_SCHED == sche_type) //单次计划
                {
                    if (D_CLIENT_VERSION_5200 > dev->dclient_version)   //旧设备无单次计划,故写死
                    {
                        Snprintf(pk->time_start, sizeof(pk->time_start), "00:00");
                        Snprintf(pk->time_end, sizeof(pk->time_end), "23:59");
                        pk->week_day = 127;    //即二进制1111111,每天
                    }
                    else
                    {
                        Snprintf(pk->day_start, sizeof(pk->day_start), ag->day_start_);
                        Snprintf(pk->day_end, sizeof(pk->day_end), ag->day_end_);
                    }
                }
                else if (DevSchedule::SchedType::DAILY_SCHED == sche_type) //每日计划
                {
                    Snprintf(pk->time_start, sizeof(pk->time_start), ag->time_start_);
                    Snprintf(pk->time_end, sizeof(pk->time_end), ag->time_end_);
                    pk->week_day = 127;    //每天
                }
                else //周计划
                {
                    Snprintf(pk->time_start, sizeof(pk->time_start), ag->time_start_);
                    Snprintf(pk->time_end, sizeof(pk->time_end), ag->time_end_);
                    pk->week_day = ag->date_flag_;
                }
            }

            key_ptr_list.push_back(pk);
        }
    } 
}

void DevUser::WriteOldCommunityInfo(DEVICE_SETTING* dev, const AgUaListPairList &uag_map, 
      const UserAccessInfoPtrMap &ua_list, const AccessGroupInfoPtrList &ag_list)
{
    std::string pri_root_path;
    std::string rf_root_path;
    if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
    {
        pri_root_path = GetCommunityPersonalDownloadPrivatekeyPath(dev->manager_account_id, dev->unit_id, dev->device_node);
        rf_root_path = GetCommunityPersonalDownloadRfidPath(dev->manager_account_id, dev->unit_id, dev->device_node);
    }
    else if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
    {
        pri_root_path = GetCommunityUnitPublicDownloadPrivatekeyPath(dev->manager_account_id, dev->unit_id);
        rf_root_path = GetCommunityUnitPublicDownloadRfidPath(dev->manager_account_id, dev->unit_id);

    }
    else if (dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
    {
        pri_root_path = GetCommunityPublicDownloadPrivatekeyPath(dev->manager_account_id);
        rf_root_path = GetCommunityPublicDownloadRfidPath(dev->manager_account_id);
    }

    DevCommKeyPtrList pin_list;
    HandleOldModelKeyInfo(dev, uag_map, ua_list,ag_list, pin_list, DEV_KEY_TYPE::DEV_KEY_PIN);
    DevKey key(pri_root_path, dev->grade);
    key.UpdatePrivateKeyFilesForNewCommunityOldDev(dev, pin_list);

    DevCommKeyPtrList rf_list;
    HandleOldModelKeyInfo(dev, uag_map, ua_list,ag_list, rf_list, DEV_KEY_TYPE::DEV_KEY_RF);
    DevKey key2(rf_root_path, dev->grade);
    key2.UpdateRfKeyFilesForNewCommunityOldDev(dev, rf_list);
}

//设备类型:支持user+个人独占;社区类型:新社区且开关为Only Public Area时,若PIN/Card/Face为pm创建,不写入对应请求的user文件中
int DevUser::CheckEnablePrivateAccess(DEVICE_SETTING* dev)
{
    if(dev->grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL && !communit_info_->EnablePrivateAccess())
    {
        return 0;
    }
    else
    {
        return 1;
    }
}

//判断注册人脸开关是否开启,pm端创建的人脸不受影响,只控制app创建的人脸
int DevUser::CheckEnableRegisterFace(UserAccessInfoPtr& user)
{
    if (user->GetRole() == ACCOUNT_ROLE_COMMUNITY_MAIN || user->GetRole() == ACCOUNT_ROLE_COMMUNITY_ATTENDANT)
    {
        PersonalAccountCnfInfo node_config;
        dbinterface::PersonalAccountCnf::GetPeronalAccountCnfByNode(user->GetNode(), node_config);
        int enable_register_face = dbinterface::PersonalAccountCnf::EnableFaceRegister(node_config);
        return enable_register_face;
    }
    else
    {
       return 1;
    }
}

//判断是否需要下发人脸给设备
bool DevUser::CheckUserEnableFace(int enable_private_access, UserAccessInfoPtr& user)
{
    //判断是否为app创建的人脸
    int special_face = user->GetSpecialFace();

    //super未开启private开关,pm创的Face不能开个人设备
    if (!enable_private_access && !special_face)
    {
        return false;
    }

    //判断Face高级功能
    if (communit_info_->CheckFeature(CommunityInfo::FeaturePlan::TAB_DEVICES_CHECK_INDEX_FACE_RECOGNITION) && !communit_info_->IsExpire())
    {
        //高级功能未过期: PM关闭Register Face ID开关,则app创建的Face不能开门
        int enable_register_face = CheckEnableRegisterFace(user);
        if (!enable_register_face && special_face)
        {
            return false;
        }
    }

    return true;
}

bool DevUser::CheckAllowEndUserIDAccess(UserAccessInfoPtr& user)
{
    if (user->GetRole() == ACCOUNT_ROLE_COMMUNITY_MAIN || user->GetRole() == ACCOUNT_ROLE_COMMUNITY_ATTENDANT)
    {
        PersonalAccountCnfInfo node_config;
        dbinterface::PersonalAccountCnf::GetPeronalAccountCnfByNode(user->GetNode(), node_config);
        return dbinterface::PersonalAccountCnf::EnableUserCreateIDAccess(node_config) == 1;
    }
    else
    {
       return false;
    }
}

//判断是否需要下发用户创建南美身份证相关信息 默认下发
bool DevUser::CheckUserEnableIDAccess(UserAccessInfoPtr& user)
{
    //判断高级功能
    if (communit_info_->CheckFeature(CommunityInfo::FeaturePlan::TAB_DEVICES_CHECK_INDEX_ID_ACCESS))
    {
        if (communit_info_->IsExpire())
        {
            return true;
        }
        //高级功能未过期 判断PM是否开启id access开关
        return CheckAllowEndUserIDAccess(user);
    }
    //默认允许
    return true;
}
