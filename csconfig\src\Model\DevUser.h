#ifndef __DEV_USER_H__
#define __DEV_USER_H__
#include <string>
#include "AKCSMsg.h"
#include "UserAccess.h"
#include "UserAccessInfo.h"
#include "dbinterface/CommunityInfo.h"
#include "dbinterface/AccessGroupDB.h"
#include <set>

#define USER_MAINTANCE_ALL_TRACEID 10000 /*我们的traceid算法不会出现这个值*/
#define USER_MAINTANCE_APPOINT_TRACEID 10001 /*我们的traceid算法不会出现这个值*/

typedef std::pair<AccessGroupInfoPtr, UserAccessInfoPtrMap> AgUaListPair;
typedef std::list<AgUaListPair> AgUaListPairList;
typedef std::map<std::string/*account*/, AccessGroupInfoPtrList> UserAccessGroupIDMap;
typedef std::set<std::string> UserUUIDList;
//权限组下的用户列表信息，列表信息包括：详细或者只有元数据
typedef std::map<std::string/*key*/, UserAccessInfoPtrMap> AgKeyUaListCacheMap;


class DevUser
{
    
public:
	DevUser(CommunityInfoPtr communit_info);
	~DevUser();

    int UpdateMetaData(DEVICE_SETTING* dev_list);
    
    int UpdatePubDevMetaByPubUser(const std::vector<uint32_t> &staff_ids, const std::vector<uint32_t> &delivery_ids,
        std::set<std::string> &ret_mac_set);
    int UpdatePubDevMetaByAccount(const std::vector<std::string> &account_list, std::set<std::string> &ret_mac_list); 
    int UpdatePubDevMetaByAccessGroupID(const std::vector<uint32_t> &ag_ids, std::set<std::string> &ret_mac_set);
    int UpdateUserDevMetaByNodes(const std::vector<std::string> &node_list, std::set<std::string> &ret_mac_set);
    int UpdateUserDevMetaByAccount(const std::vector<std::string> &account_list, std::set<std::string> &ret_mac_set);
    int GetDetailDataForRequest(DEVICE_SETTING* dev, const UserUUIDList &list, DULONG traceid, std::string &file_path, std::string &file_md5);
    
    static int DevSupportUser(int dclient_ver);
    static int DevTypeSupportUser(int dev_type);
    static void GetUserHoldDoorAccessGroupList(DEVICE_SETTING* dev, AccessGroupInfoPtrList &ag_list);
    static void GetDevAccessGroupList(DEVICE_SETTING* dev, AccessGroupInfoPtrList &ag_list);
private:
    int CreateRequestUserListDetailData(DEVICE_SETTING* dev, const UserUUIDList &list, DULONG traceid,  
                               std::string &file_path, std::string &file_md5);
    void GetDevUserAccessMateInfoList(DEVICE_SETTING* dev, uint32_t ag_id, UserAccessInfoPtrMap &usre_list);
    int WriteDetailDataForGiven(DEVICE_SETTING* dev, const AgUaListPairList &uag_map, const UserAccessInfoPtrMap &user_list,
                                              DULONG traceid, std::string &path);
    int WriteDetailDataForAll(DEVICE_SETTING* dev, const AgUaListPairList &uag_map, const UserAccessInfoPtrMap &user_list);
    int WriteMetaDataToJson(DEVICE_SETTING* dev, const UserAccessInfoPtrMap &user_list);
    int WriteDetailDataToJson(DEVICE_SETTING* dev, const  AgUaListPairList &uag_map, 
        const UserAccessInfoPtrMap &user_list,  const std::string &file_path);
    void StatisticsUserInformation(const AgUaListPairList &list, AgUaListPairList &uag_map, UserAccessInfoPtrMap &user_list);
    void WriteOldCommunityInfo(DEVICE_SETTING* dev, const AgUaListPairList &uag_map, 
        const UserAccessInfoPtrMap &ua_list, const AccessGroupInfoPtrList &ag_list);
    int WirteFile(const std::string &filename, const std::string &content);
    void HandleOldModelKeyInfo(DEVICE_SETTING* dev, const AgUaListPairList &uag_map, const UserAccessInfoPtrMap &ua_list, 
         const AccessGroupInfoPtrList &ag_list, DevCommKeyPtrList &key_list, DEV_KEY_TYPE type);
    std::string  GetUserAccessInfoKey(uint32_t group_id, DEVICE_SETTING* dev);
    
    void CheckCodeUnique(const std::string &mac, std::set<std::string> &unique_list, const std::list<std::string> &codelist);
    int CheckEnablePrivateAccess(DEVICE_SETTING* dev);
    int CheckEnableRegisterFace(UserAccessInfoPtr& user);
    bool CheckUserEnableFace(int enable_private_access, UserAccessInfoPtr& user);
    bool CheckUserEnableIDAccess(UserAccessInfoPtr& user);
    bool CheckAllowEndUserIDAccess(UserAccessInfoPtr& user);
    void FilterAccessGroups(DEVICE_SETTING* cur_dev, AccessGroupInfoPtrList& ag_list);
private:
    CommunityInfoPtr communit_info_;
    
};




#endif 
