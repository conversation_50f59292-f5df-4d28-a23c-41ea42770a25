#include <string.h>
#include <list>
#include <boost/algorithm/string.hpp>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "CharChans.h"
#include "AKCSMsg.h"
#include "AkLogging.h"
#include "AdaptUtility.h"
#include "AkcsCommonDef.h"
#include "DeviceSetting.h"
#include "PersonalAccount.h"
#include "PersonnalDeviceSetting.h"
#include "dbinterface/Sip.h"
#include "dbinterface/Shadow.h"
#include "dbinterface/PubDevMngList.h"
#include "dbinterface/InterfaceComm.h"
#include "dbinterface/PersonalAccountCnf.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"


CDeviceSetting* GetDeviceSettingInstance()
{
    return CDeviceSetting::GetInstance();
}

CDeviceSetting::CDeviceSetting()
{
}

CDeviceSetting::~CDeviceSetting()
{
}

CDeviceSetting* CDeviceSetting::instance = NULL;

CDeviceSetting* CDeviceSetting::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CDeviceSetting();
    }

    return instance;
}

// 和TransferPerDevToPointer的区别is_personal = 0.
static void TransferDevToPointer(const ResidentDev& dev, DEVICE_SETTING* dev_pointer)
{
    memset(dev_pointer, 0, sizeof(DEVICE_SETTING));
    dev_pointer->id = dev.id;
    dev_pointer->is_expire = 0;
    dev_pointer->is_personal = 0;    
    dev_pointer->port = dev.port;
    dev_pointer->grade = dev.grade;
    dev_pointer->brand = dev.brand;
    dev_pointer->flags = dev.flags;
    dev_pointer->repost = dev.repost;
    dev_pointer->type = dev.dev_type;
    dev_pointer->status = dev.status;
    dev_pointer->unit_id = dev.unit_id;
    dev_pointer->sip_type = dev.sip_type;
    dev_pointer->device_type = dev.conn_type;
    dev_pointer->stair_show = dev.stair_show; 
    dev_pointer->project_type = dev.project_type;
    dev_pointer->netgroup_num = dev.netgroup_num;
    dev_pointer->manager_account_id = dev.project_mng_id;
    dev_pointer->dclient_version = dev.dclient_ver;
    dev_pointer->fun_bit = dev.fun_bit;
    dev_pointer->firmware = dev.firmware;
    dev_pointer->allow_end_user_hold_door = dev.allow_end_user_hold_door;
    dev_pointer->oem_id = dev.oem_id;
    Snprintf(dev_pointer->mac, sizeof(dev_pointer->mac), dev.mac);
    Snprintf(dev_pointer->relay, sizeof(dev_pointer->relay), dev.relay);
    Snprintf(dev_pointer->SWVer, sizeof(dev_pointer->SWVer), dev.sw_ver);
    Snprintf(dev_pointer->HWVer, sizeof(dev_pointer->HWVer), dev.hw_ver);
    Snprintf(dev_pointer->config, sizeof(dev_pointer->config), dev.autop_config);
    Snprintf(dev_pointer->ip_addr, sizeof(dev_pointer->ip_addr), dev.ipaddr); //优先取wifi ip
    Snprintf(dev_pointer->wired_ip_addr, sizeof(dev_pointer->wired_ip_addr), dev.wired_ipaddr); //优先取有线ip
    Snprintf(dev_pointer->gateway, sizeof(dev_pointer->gateway), dev.gateway);
    Snprintf(dev_pointer->outer_ip, sizeof(dev_pointer->outer_ip), dev.outer_ip);
    Snprintf(dev_pointer->location, sizeof(dev_pointer->location), dev.location);
    Snprintf(dev_pointer->sip_account, sizeof(dev_pointer->sip_account), dev.sip);
    Snprintf(dev_pointer->device_node, sizeof(dev_pointer->device_node), dev.node);
    Snprintf(dev_pointer->community, sizeof(dev_pointer->community), dev.community);
    Snprintf(dev_pointer->rf_id_md5, sizeof(dev_pointer->rf_id_md5), dev.rf_id_md5);
    Snprintf(dev_pointer->sip_password, sizeof(dev_pointer->sip_password), dev.sippwd);
    Snprintf(dev_pointer->config_md5, sizeof(dev_pointer->config_md5), dev.config_md5);
    Snprintf(dev_pointer->rtsp_password, sizeof(dev_pointer->rtsp_password), dev.rtsppwd);
    Snprintf(dev_pointer->contact_md5, sizeof(dev_pointer->contact_md5), dev.contact_md5);
    Snprintf(dev_pointer->subnet_mask, sizeof(dev_pointer->subnet_mask), dev.subnet_mask);
    Snprintf(dev_pointer->wired_subnet_mask, sizeof(dev_pointer->wired_subnet_mask), dev.wired_subnet_mask);
    Snprintf(dev_pointer->primary_dns, sizeof(dev_pointer->primary_dns), dev.primary_dns);
    Snprintf(dev_pointer->secondary_dns, sizeof(dev_pointer->secondary_dns), dev.secondary_dns);
    Snprintf(dev_pointer->security_relay, sizeof(dev_pointer->security_relay), dev.security_relay);
    Snprintf(dev_pointer->private_key_md5, sizeof(dev_pointer->private_key_md5), dev.private_key_md5);
    Snprintf(dev_pointer->last_connection, sizeof(dev_pointer->last_connection), dev.last_connection);
    Snprintf(dev_pointer->uuid, sizeof(dev_pointer->uuid), dev.uuid);

    dev_pointer->allow_end_user_monitor = dev.allow_end_user_monitor;
    dev_pointer->camera_num = 1; //默认一个设备对应一个Camera
    if (SwitchHandle(dev_pointer->fun_bit, FUNC_DEV_SUPPORT_MULTI_MONITOR))
    {
        dev_pointer->camera_num = 2; //多摄像头目前只有两个摄像头的场景
    }
    Snprintf(dev_pointer->project_uuid, sizeof(dev_pointer->project_uuid), dev.project_uuid);
    Snprintf(dev_pointer->community_unit_uuid, sizeof(dev_pointer->community_unit_uuid), dev.unit_uuid);
    Snprintf(dev_pointer->node_uuid, sizeof(dev_pointer->node_uuid), dev.node_uuid);
    Snprintf(dev_pointer->create_time, sizeof(dev_pointer->create_time), dev.create_time);
}

void InsertIntoDevicesList(DEVICE_SETTING** dev_header, DEVICE_SETTING** dev_cur, DEVICE_SETTING* dev_pointer)
{
    if (*dev_header == NULL)
    {
        *dev_header = dev_pointer;
    }
    else
    {
        (*dev_cur)->next = dev_pointer;
    }
    *dev_cur = dev_pointer;
}

//销毁设备列表
void CDeviceSetting::DestoryDeviceSettingList(DEVICE_SETTING* device_header)
{
    DEVICE_SETTING* cur_device = nullptr;
    while (nullptr != device_header)
    {
        cur_device = device_header;
        device_header = device_header->next;
        delete cur_device;
    }
}

bool CDeviceSetting::DeviceSupportFaceMng(int type)
{
    if (type == DEVICE_TYPE_STAIR || type == DEVICE_TYPE_DOOR || type == DEVICE_TYPE_ACCESS)
    {
        return true;
    }
    return false;
}


int CDeviceSetting::GetMotionDetection(uint32_t dclient_version, int enable_motion)
{
    if (dclient_version < 6000)
    {
        return (enable_motion == (int)MotionDectionOption::MOTION_DECTION_VIDEO) ? (int)MotionDectionOption::MOTION_DECTION_IR : enable_motion;
    }
    else
    {
        return enable_motion;
    }
}

int CDeviceSetting::GetPhoneStatusByCallType(const int call_type)
{
    //V4.3修改去掉落地开关，当是calltype是如下两个选项时候开启落地
    if (call_type == NODE_CALL_TYPE_INDOOR_PHONE || call_type == NODE_CALL_TYPE_APP_INDOOR_BACK_PHONE
     || call_type == NODE_CALL_TYPE_INDOOR_BACK_PHONE || call_type == NODE_CALL_TYPE_INDOOR_BACK_APP_BACK_PHONE)
    {
        return 1;
    }
    else
    {
        return 0;
    }
}

int CDeviceSetting::GetManagementBuilding(int dev_id, std::vector<int>& unit_list)
{
    int manage_all_flag = 0;
    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    if (0 == dbinterface::ResidentDevices::GetDevByID(dev_id, dev))
    {
        if (dbinterface::SwitchHandle(dev.flags, DeviceSwitch::DEV_MNG_ALL))
        {
            manage_all_flag =  1;
        }
        else
        {
           dbinterface::PubDevMngList::GetManagementBuildingListById(dev_id, unit_list);
        }
    }
    return manage_all_flag;
}

// 获取单个mac的信息
int CDeviceSetting::GetDeviceSettingByMac(const std::string& mac, DEVICE_SETTING* device_setting)
{
    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    if (0 == dbinterface::ResidentDevices::GetMacDev(mac, dev)
     || 0 == dbinterface::ResidentPerDevices::GetMacDev(mac, dev))
    {
        TransferDevToPointer(dev, device_setting);
        return 0;
    }
    return -1;
}

// 获取单个mac的信息
DEVICE_SETTING* CDeviceSetting::GetDeviceSettingByMac(const std::string& mac)
{
    ResidentDev dev;
    memset(&dev, 0, sizeof(dev));
    if (0 == dbinterface::ResidentDevices::GetMacDev(mac, dev))
    {
        DEVICE_SETTING* dev_setting = new DEVICE_SETTING;
        TransferDevToPointer(dev, dev_setting);
        return dev_setting;
    }
    return NULL;
}

// 获取mac_set的信息
DEVICE_SETTING* CDeviceSetting::GetDeviceSettingByMacList(const std::set<std::string>& mac_set)
{
    DEVICE_SETTING* dev_cur = NULL;
    DEVICE_SETTING* dev_header = NULL;

    ResidentDeviceList dev_list;
    if (0 == dbinterface::ResidentDevices::GetMacListDevList(mac_set, dev_list))
    {
        for (const auto& dev : dev_list)
        {
            DEVICE_SETTING* dev_pointer  = new DEVICE_SETTING;
            TransferDevToPointer(dev, dev_pointer);
            InsertIntoDevicesList(&dev_header, &dev_cur, dev_pointer);
        }
    }
    return dev_header;
}

// 社区所有最外围设备 : pub
DEVICE_SETTING* CDeviceSetting::GetRootCommunityPublicDeviceSettingList(const int mng_id)
{
    DEVICE_SETTING* dev_cur = NULL;
    DEVICE_SETTING* dev_header = NULL;
    
    ResidentDeviceList dev_list;
    if (0 == dbinterface::ResidentDevices::GetPubDevList(mng_id, dev_list))
    {
        for (const auto& dev : dev_list)
        {
            DEVICE_SETTING* dev_pointer  = new DEVICE_SETTING;
            TransferDevToPointer(dev, dev_pointer);
            InsertIntoDevicesList(&dev_header, &dev_cur, dev_pointer);
        }
    }
    return dev_header;
}

// 社区所有楼栋设备 : unit
DEVICE_SETTING* CDeviceSetting::GetRootCommunityPublicUnitDeviceSettingList(const unsigned int unit_id)
{
    DEVICE_SETTING* dev_cur = NULL;
    DEVICE_SETTING* dev_header = NULL;
    
    ResidentDeviceList dev_list;
    if (0 == dbinterface::ResidentDevices::GetDepartmentDevList(unit_id, dev_list))
    {
        for (const auto& dev : dev_list)
        {
            DEVICE_SETTING* dev_pointer  = new DEVICE_SETTING;
            TransferDevToPointer(dev, dev_pointer);
            InsertIntoDevicesList(&dev_header, &dev_cur, dev_pointer);
        }
    }
    return dev_header;
}

DEVICE_SETTING* CDeviceSetting::GetDeviceSettingListByDevList(const ResidentDeviceList& dev_list)
{
    DEVICE_SETTING* dev_cur = NULL;
    DEVICE_SETTING* dev_header = NULL;
    
    for (const auto& dev : dev_list)
    {
        DEVICE_SETTING* dev_pointer  = new DEVICE_SETTING;
        TransferDevToPointer(dev, dev_pointer);
        InsertIntoDevicesList(&dev_header, &dev_cur, dev_pointer);
    }
    return dev_header;
}


// 社区所有公共设备 : pub + unit
DEVICE_SETTING* CDeviceSetting::GetRootCommunityAllPublicDeviceSettingList(const int mng_id)
{
    DEVICE_SETTING* dev_cur = NULL;
    DEVICE_SETTING* dev_header = NULL;

    ResidentDeviceList dev_list;
    if (0 == dbinterface::ResidentDevices::GetAllPubDevList(mng_id, dev_list))
    {
        for (const auto& dev : dev_list)
        {
            DEVICE_SETTING* dev_pointer  = new DEVICE_SETTING;
            TransferDevToPointer(dev, dev_pointer);
            InsertIntoDevicesList(&dev_header, &dev_cur, dev_pointer);
        }
    }
    return dev_header;
}

// 社区所有最外围 + 指定楼栋设备: pub + unit
DEVICE_SETTING* CDeviceSetting::GetRootPubAndUnitPubDeviceSettingList(uint32_t unit_id, uint32_t mng_id)
{
    DEVICE_SETTING* dev_cur = NULL;
    DEVICE_SETTING* dev_header = NULL;
    
    ResidentDeviceList dev_list;
    if (0 == dbinterface::ResidentDevices::GetRootPubAndUnitPubDeviceSettingList(unit_id, mng_id, dev_list))
    {
        for (const auto& dev : dev_list)
        {
            DEVICE_SETTING* dev_pointer  = new DEVICE_SETTING;
            TransferDevToPointer(dev, dev_pointer);
            InsertIntoDevicesList(&dev_header, &dev_cur, dev_pointer);
        }
    }
    return dev_header;
}

// 社区所有设备: pub + unit + apt
DEVICE_SETTING* CDeviceSetting::GetCommunityAllDev(int mng_id)
{
    DEVICE_SETTING* dev_cur = NULL;
    DEVICE_SETTING* dev_header = NULL;

    ResidentDeviceList dev_list;
    if (0 == dbinterface::ResidentDevices::GetAllDevList(mng_id, dev_list))
    {
        for (const auto& dev : dev_list)
        {
            DEVICE_SETTING* dev_pointer  = new DEVICE_SETTING;
            TransferDevToPointer(dev, dev_pointer);
            InsertIntoDevicesList(&dev_header, &dev_cur, dev_pointer);
        }
    }
    return dev_header;
}

void CDeviceSetting::GetCommunityAllDev(int mng_id, DeviceSettingList &alldevlist, 
  DeviceSettingIntMap &unit_dev_list, DeviceSettingStrMap &unit_uuid_dev_list, 
  DeviceSettingList &pub_dev_list, 
  DeviceSettingStrMap &node_dev_list, 
  DeviceSettingStrMap &mac_dev_list,
  DeviceSettingStrMap &uuid_dev_list)
{
    ResidentDeviceList dev_list;
    if (0 == dbinterface::ResidentDevices::GetAllDevList(mng_id, dev_list))
    {
        for (const auto& dev : dev_list)
        {
            DEVICE_SETTING* dev_pointer  = new DEVICE_SETTING;
            TransferDevToPointer(dev, dev_pointer);
            alldevlist.push_back(dev_pointer);
            mac_dev_list.insert(std::make_pair(dev.mac, dev_pointer));
            uuid_dev_list.insert(std::make_pair(dev.uuid, dev_pointer));
            if (dev.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC)
            {
                pub_dev_list.push_back(dev_pointer);
            }
            else if (dev.grade == csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT)
            {
                unit_dev_list.insert(std::make_pair(dev.unit_id, dev_pointer));
                unit_uuid_dev_list.insert(std::make_pair(dev.unit_uuid, dev_pointer));
            }
            else if (dev.grade == csmain::COMMUNITY_DEVICE_TYPE_PERSONAL)
            {
                node_dev_list.insert(std::make_pair(dev.node, dev_pointer));
            }
        }
    }
}

// 获取社区所有的管理机 : pub + unit + apt
DEVICE_SETTING* CDeviceSetting::GetCommunityMngDeviceSettingList(const int mng_id)
{
    DEVICE_SETTING* dev_cur = NULL;
    DEVICE_SETTING* dev_header = NULL;

    ResidentDeviceList dev_list;
    if (0 == dbinterface::ResidentDevices::GetAllMngDevList(mng_id, dev_list))
    {
        for (const auto& dev : dev_list)
        {
            DEVICE_SETTING* dev_pointer  = new DEVICE_SETTING;
            TransferDevToPointer(dev, dev_pointer);
            InsertIntoDevicesList(&dev_header, &dev_cur, dev_pointer);
        }
    }
    return dev_header;
}

// 获取社区apt内所有设备 : apt
DEVICE_SETTING* CDeviceSetting::GetCommunityAllNodeDev(int mng_id)
{
    DEVICE_SETTING* dev_cur = NULL;
    DEVICE_SETTING* dev_header = NULL;
    
    ResidentDeviceList dev_list;
    if (0 == dbinterface::ResidentDevices::GetCommunityAllNodeDevList(mng_id, dev_list))
    {
        for (const auto& dev : dev_list)
        {
            DEVICE_SETTING* dev_pointer  = new DEVICE_SETTING;
            TransferDevToPointer(dev, dev_pointer);
            InsertIntoDevicesList(&dev_header, &dev_cur, dev_pointer);
        }
    }
    return dev_header;
}

// 获取社区apt内所有设备联系人配置 : apt
DEVICE_SETTING* CDeviceSetting::GetNodeDeviceSettingList(const std::string& node)
{
    DEVICE_SETTING* dev_cur = NULL;
    DEVICE_SETTING* dev_header = NULL;

    ResidentDeviceList dev_list;
    dbinterface::ResidentDevices::GetNodeDevList(node, dev_list);
    if (dev_list.size() == 0)
    {
        return dev_header;
    }

    ResidentPerAccount node_info;
    memset(&node_info, 0, sizeof(node_info));
    if (0 == dbinterface::ResidentPersonalAccount::GetUserInfoByAccount(node, node_info))
    {
        PersonalAccountCnfInfo node_config;
        dbinterface::PersonalAccountCnf::GetPeronalAccountCnfByNode(node, node_config);
        
        node_info.phone_status = GetPhoneStatusByCallType(node_config.call_type);
        std::string sip_group = dbinterface::Sip::GetSipGroupByNode(node);
      
        std::list<DevNetInfo> push_ip_list;
        // 给node下的所有设备写主账号配置信息;记录室内机管理机的ip/sip,用于写pushbutton
        for (auto& dev : dev_list)
        {
            // 启用ip直播才能添加室内机IP到pushbutton 并且要把室内机移出群组 //管理中心机也可能放在室内 ********
            if ((dev.dev_type == DEVICE_TYPE_INDOOR || dev.dev_type == DEVICE_TYPE_MANAGEMENT) && node_info.ip_direct)
            {
                DevNetInfo infos = {0};
                infos.netgroup_num = dev.netgroup_num;
                snprintf(infos.ip, sizeof(infos.ip), "%s", dev.ipaddr);
                snprintf(infos.sip, sizeof(infos.sip), "%s", dev.sip);
                push_ip_list.push_front(infos);
        
            }
            // 写主账号配置信息
            dev.motion_time = node_config.motion_time;
            dev.enable_motion = node_config.enable_motion;
            dev.robin_call_time = node_config.robin_call_time;
            dev.enable_robin_call = node_config.enable_robin_call;
        }
        
        // 给node下的所有门口机写pushbutton/robin_call_val
        for (auto& dev : dev_list)
        {
            if (dev.dev_type == DEVICE_TYPE_DOOR || dev.dev_type == DEVICE_TYPE_STAIR || dev.dev_type == DEVICE_TYPE_WALL || dev.dev_type == DEVICE_TYPE_ACCESS)
            {
                std::string robincall;
                std::list<DevNetInfo>::iterator it = push_ip_list.begin();
        
                // 写ip/sip
                while (it != push_ip_list.end())
                {
                   if (it->netgroup_num == dev.netgroup_num && node_info.ip_direct)
                   {
                       snprintf(dev.pushbutton + strlen(dev.pushbutton), sizeof(dev.pushbutton) - strlen(dev.pushbutton), "%s;", it->ip);
                   }
                   else if (it->netgroup_num != dev.netgroup_num || !node_info.ip_direct)
                   {
                       snprintf(dev.pushbutton + strlen(dev.pushbutton), sizeof(dev.pushbutton) - strlen(dev.pushbutton), "%s;", it->sip);
                   }
                   it++;
                }
                
                // 写sip_group
                snprintf(dev.pushbutton + strlen(dev.pushbutton), sizeof(dev.pushbutton) - strlen(dev.pushbutton), "%s;", sip_group.c_str());
        
                // 写落地
                if (node_info.phone_status)
                {
                   if (strlen(node_info.phone) > 0)
                   {
                       snprintf(dev.pushbutton + strlen(dev.pushbutton), sizeof(dev.pushbutton) - strlen(dev.pushbutton), "%s%s%s;", PHONE_CALL_OUT_SUBFIX, node_info.phone_code, node_info.phone);
                   }
                   if (strlen(node_info.phone2) > 0)
                   {
                       snprintf(dev.pushbutton + strlen(dev.pushbutton), sizeof(dev.pushbutton) - strlen(dev.pushbutton), "%s%s%s;", PHONE_CALL_OUT_SUBFIX, node_info.phone_code, node_info.phone2);
                   }
                   if (strlen(node_info.phone3) > 0)
                   {
                       snprintf(dev.pushbutton + strlen(dev.pushbutton), sizeof(dev.pushbutton) - strlen(dev.pushbutton), "%s%s%s;", PHONE_CALL_OUT_SUBFIX, node_info.phone_code, node_info.phone3);
                   }
                }
               
                while (strstr(robincall.c_str(), ";;"))
                {
                   boost::replace_all(robincall, ";;", ";");
                }
                snprintf(dev.robin_call_val, sizeof(dev.robin_call_val), "%s", robincall.c_str());
        
                std::string push = dev.pushbutton;
                while (strstr(push.c_str(), ";;"))
                {
                   boost::replace_all(push, ";;", ";");
                }
                snprintf(dev.pushbutton, sizeof(dev.pushbutton), "%s", push.c_str());
            }
        }
        
        for (auto &dev : dev_list)
        {
            DEVICE_SETTING* dev_pointer  = new DEVICE_SETTING;
            TransferDevToPointer(dev, dev_pointer);
            dev_pointer->motion_time = dev.motion_time;
            dev_pointer->enable_motion = dev.enable_motion;
            dev_pointer->robin_call_time = dev.robin_call_time;
            dev_pointer->enable_robin_call = dev.enable_robin_call;
            Snprintf(dev_pointer->push_button, sizeof(dev_pointer->push_button), dev.pushbutton);
            Snprintf(dev_pointer->robin_call_val, sizeof(dev_pointer->robin_call_val), dev.robin_call_val);
            InsertIntoDevicesList(&dev_header, &dev_cur, dev_pointer);
        }

    }
    return dev_header;
}
