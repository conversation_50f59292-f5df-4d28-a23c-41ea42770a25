#ifndef __DEVICE_SETTING_H__
#define __DEVICE_SETTING_H__

#include <set>
#include "dbinterface/resident/ResidentDevices.h"
#include "DeviceControl.h"

namespace csconfig
{

enum CommunityDeviceType
{
    COMMUNITY_DEVICE_TYPE_NONE = 0,//代表个人
    COMMUNITY_DEVICE_TYPE_PUBLIC = 1,
    COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT = 2,
    COMMUNITY_DEVICE_TYPE_PERSONAL = 3,
};
    
}

void InsertIntoDevicesList(DEVICE_SETTING** dev_header, DEVICE_SETTING** dev_cur, DEVICE_SETTING* dev_pointer);

class CDeviceSetting
{
public:
    CDeviceSetting();
    ~CDeviceSetting();

    //销毁设备列表
    void DestoryDeviceSettingList(DEVICE_SETTING* device_header);

    //Bgine added by cheny<PERSON>,解决C<PERSON><PERSON>,空字符串构造时,断言失败的问题,获取根节点的设备列表
    DEVICE_SETTING* GetNodeDeviceSettingList(const std::string& user);

    static CDeviceSetting* GetInstance();
    
    DEVICE_SETTING* GetCommunityAllDev(int mng_id);
    DEVICE_SETTING* GetCommunityAllNodeDev(int mng_id);
    DEVICE_SETTING* GetDeviceSettingByMac(const std::string& mac);
    DEVICE_SETTING* GetCommunityMngDeviceSettingList(const int  mng_id);
    DEVICE_SETTING* GetRootCommunityPublicDeviceSettingList(const int  mng_id);
    DEVICE_SETTING* GetDeviceSettingByMacList(const std::set<std::string>& mac_set);
    DEVICE_SETTING* GetRootCommunityAllPublicDeviceSettingList(const int mng_id);
    DEVICE_SETTING* GetRootPubAndUnitPubDeviceSettingList(uint32_t unitid, uint32_t mngid);
    DEVICE_SETTING* GetRootCommunityPublicUnitDeviceSettingList(const unsigned int unit_id);
    DEVICE_SETTING* GetDeviceSettingListByDevList(const ResidentDeviceList& dev_list);

    int GetPhoneStatusByCallType(const int call_type);
    int GetMotionDetection(uint32_t dclient_version, int enable_motion);
    int GetManagementBuilding(int dev_id, std::vector<int>& unit_list);
    int GetCommunityAllPubDev(int mng_id, std::vector<std::string>& macs);
    int GetDeviceSettingByMac(const std::string& mac, DEVICE_SETTING* device_setting);

    bool DeviceSupportFaceMng(int type);
    void GetCommunityAllDev(int mng_id, DeviceSettingList &devlist, 
      DeviceSettingIntMap &unit_dev_list, DeviceSettingStrMap &unit_uuid_dev_list, 
      DeviceSettingList &pub_dev_list, DeviceSettingStrMap &node_dev_list, DeviceSettingStrMap &mac_dev_list,DeviceSettingStrMap &uuid_dev_list);


    
private:
    static CDeviceSetting* instance;
};

CDeviceSetting* GetDeviceSettingInstance();

#endif


