#include <cstring>
#include "ExtraDeviceRelayConfig.h"
#include "AkLogging.h"

using namespace dbinterface;

ExtraDeviceRelay::ExtraDeviceRelay() 
{
}

int ExtraDeviceRelay::CalculateFunctionValue(const ExtraDeviceRelayListInfo& relay, 
                                            int action_type, 
                                            int function_index) const
{
    // 非窗帘/升降门类型
    if (relay.function != RelayWebFunctionType::RELAY_FUNCTION_SHUTTER && 
        relay.function != RelayWebFunctionType::RELAY_FUNCTION_SHADE) 
    {
        switch (relay.function) {
            case RelayWebFunctionType::RELAY_FUNCTION_LIGHT:
                return FunctionDeviceValues::FUNCTION_DEVICES_VALUE_LIGHT;  // 灯光
            case RelayWebFunctionType::RELAY_FUNCTION_DOOR:
                return FunctionDeviceValues::FUNCTION_DEVICES_VALUE_DOOR;   // 门禁
            case RelayWebFunctionType::RELAY_FUNCTION_OTHER:
                return FunctionDeviceValues::FUNCTION_DEVICES_VALUE_OTHER;  // 其他
            case RelayWebFunctionType::RELAY_FUNCTION_SHUTTER_UP:
                return FUNCTION_DEVICES_VALUE_SHUTTER_UP;
            case RelayWebFunctionType::RELAY_FUNCTION_SHUTTER_DOWN:
                return FUNCTION_DEVICES_VALUE_SHUTTER_DOWN;
            case RelayWebFunctionType::RELAY_FUNCTION_SHUTTER_PAUSING:
                return FUNCTION_DEVICES_VALUE_SHUTTER_PAUSING;
            default:
                return relay.function;  // 保持原始值
        }
    }
    
    // 窗帘类型
    if (relay.function == RelayWebFunctionType::RELAY_FUNCTION_SHUTTER)
    {
        if (action_type == ActionType::ACTION_TYPE_UP) // Up操作
        {
            switch (function_index ) 
            {
                case 0:
                    return FunctionDeviceValues::FUNCTION_DEVICES_VALUE_SHUTTER1_UP;
                case 1:
                    return FunctionDeviceValues::FUNCTION_DEVICES_VALUE_SHUTTER2_UP;
                case 2:
                    return FunctionDeviceValues::FUNCTION_DEVICES_VALUE_SHUTTER3_UP;
                case 3:
                    return FunctionDeviceValues::FUNCTION_DEVICES_VALUE_SHUTTER4_UP;
                default:
                    return FunctionDeviceValues::FUNCTION_DEVICES_VALUE_SHUTTER1_UP;
            }
        }
        else if (action_type == ActionType::ACTION_TYPE_DOWN) // Down操作
        {
            switch (function_index ) 
            {
                case 0:
                    return FunctionDeviceValues::FUNCTION_DEVICES_VALUE_SHUTTER1_DOWN;
                case 1:
                    return FunctionDeviceValues::FUNCTION_DEVICES_VALUE_SHUTTER2_DOWN;
                case 2:
                    return FunctionDeviceValues::FUNCTION_DEVICES_VALUE_SHUTTER3_DOWN;
                case 3:
                    return FunctionDeviceValues::FUNCTION_DEVICES_VALUE_SHUTTER4_DOWN;
                default:
                    return FunctionDeviceValues::FUNCTION_DEVICES_VALUE_SHUTTER1_DOWN;
            }
        }
    }
    // 升降门类型  
    else if (relay.function == RelayWebFunctionType::RELAY_FUNCTION_SHADE)
    {
        if (action_type == ActionType::ACTION_TYPE_UP) // Open操作
        {
            switch (function_index ) 
            {
                case 0:
                    return FunctionDeviceValues::FUNCTION_DEVICES_VALUE_SHADE1_OPEN;
                case 1:
                    return FunctionDeviceValues::FUNCTION_DEVICES_VALUE_SHADE2_OPEN;
                case 2:
                    return FunctionDeviceValues::FUNCTION_DEVICES_VALUE_SHADE3_OPEN;
                case 3:
                    return FunctionDeviceValues::FUNCTION_DEVICES_VALUE_SHADE4_OPEN;
                default:
                    return FunctionDeviceValues::FUNCTION_DEVICES_VALUE_SHADE1_OPEN;
            }
        }
        else if (action_type == ActionType::ACTION_TYPE_DOWN) // Close操作
        {
            switch (function_index ) 
            {
                case 0:
                    return FunctionDeviceValues::FUNCTION_DEVICES_VALUE_SHADE1_CLOSE;
                case 1:
                    return FunctionDeviceValues::FUNCTION_DEVICES_VALUE_SHADE2_CLOSE;
                case 2:
                    return FunctionDeviceValues::FUNCTION_DEVICES_VALUE_SHADE3_CLOSE;
                case 3:
                    return FunctionDeviceValues::FUNCTION_DEVICES_VALUE_SHADE4_CLOSE;
                default:
                    return FunctionDeviceValues::FUNCTION_DEVICES_VALUE_SHADE1_CLOSE;
            }
        }
    }
    
    // 其他操作类型，返回默认值
    return FunctionDeviceValues::FUNCTION_DEVICES_VALUE_OTHER;
}




