#ifndef __EXTRA_DEVICE_RELAY_CONFIG_H__
#define __EXTRA_DEVICE_RELAY_CONFIG_H__

#include <vector>
#include <string>
#include <map>
#include "dbinterface/resident/ExtraDevice.h"
#include "dbinterface/resident/ExtraDeviceRelayList.h"
#include "dbinterface/resident/ExtraDeviceRelayAction.h"

// 外接设备relay信息相关
class ExtraDeviceRelay
{
public:
    ExtraDeviceRelay();

    // 计算功能值
    int CalculateFunctionValue(const ExtraDeviceRelayListInfo& relay, int action_type, int function_index) const;
    


public:
    ExtraDeviceInfo extra_device_;
    ExtraDeviceRelayListInfoList relay_list_;  // 包含完整relay信息的vector
    ExtraDeviceRelayActionInfoMap relay_uuid_to_actions_map_;  // relay_uuid到actions的multimap
};

// 使用indoor_monitor_config_uuid作为键的配置映射
using ExtraDeviceRelayConfigMap = std::multimap<std::string, ExtraDeviceRelay>;
using ExtraDeviceRelayList = std::vector<ExtraDeviceRelay>;
#endif // __EXTRA_DEVICE_RELAY_CONFIG_H__ 