﻿#include "FaceMng.h"
#include <sstream>
#include <unistd.h>
#include "RldbQuery.h"
#include "Rldb.h"
#include "AkLogging.h"
#include "util_cstring.h"
#include "AKCSMsg.h"
#include "AdaptUtility.h"
#include "tinyxml.h"
#include "ConfigDef.h"
#include "AES256.h"
#include "Singleton.h"
#include "AkcsMonitor.h"
#include "encrypt/Md5.h"
#include "ShadowMng.h"
#include "WriteFileControl.h"
#include "dbinterface/FaceMngDB.h"

extern CSCONFIG_CONF gstCSCONFIGConf;

CFaceMng& CFaceMng::GetInstance()
{
    static CFaceMng o_face_mng;
    return o_face_mng;
}

int CFaceMng::DaoGetFaceMngByPersonalAccountIds(std::vector<FaceMngInfo>& face_mng_infos, const std::vector<DEVICE_CONTACTLIST>& personal_account_ids)
{
    return dbinterface::FaceMng::GetFaceMngByPersonalAccountIds(face_mng_infos, personal_account_ids);
}

int CFaceMng::DaoGetFaceMngByPersonalAccountIds(std::map<std::string, FaceMngInfo>& list, const std::string &uid_ids)
{
    return dbinterface::FaceMng::GetFaceMngByPersonalAccountIds(list, uid_ids);
}

int CFaceMng::DelFaceMngByMngId(uint32_t mng_account_id)
{
    int ret = 0;

    char full_path[256];
    const char *prefix = "/var/www/download/face";
    snprintf(full_path, sizeof(full_path), "%s/%d/%u", prefix, mng_account_id % 100, mng_account_id);
    DeleteDir(std::string(full_path));

    return ret;
}

void CFaceMng::FilterByUnit(const std::vector<uint32_t> &unit_ids, std::vector<FaceMngInfo>& face_mng_infos)
{
    auto it = face_mng_infos.begin();
    while (it != face_mng_infos.end())
    {
        FaceMngInfo &face_mng_info = *it;
        if (face_mng_info.unit_id > 0 && find(unit_ids.begin(), unit_ids.end(), face_mng_info.unit_id) == unit_ids.end())
        {
            AK_LOG_INFO << "Face info,face_mng_id=" << face_mng_info.face_mng_id << ";unit_id=" << face_mng_info.unit_id << " is filtered by device manage building";
            it = face_mng_infos.erase(it);
        } else {
            ++it;
        }
    }
}

int CFaceMng::DaoGetFaceMng(std::vector<FaceMngInfo>& face_mng_infos, uint32_t mng_account_id, uint32_t unit_id, uint32_t personal_account_id)
{
    return dbinterface::FaceMng::GetFaceMng(face_mng_infos, mng_account_id, unit_id, personal_account_id);
}

CFaceXmlHelper& CFaceXmlHelper::GetInstance()
{
    static CFaceXmlHelper o_face_xml_helper;
    return o_face_xml_helper;
}

std::string CFaceXmlHelper::ToXml(const char* pic_file_path, const std::vector<FaceMngInfo>& face_mng_infos, const DEVICE_SETTING* device_setting)
{
    if (NULL == pic_file_path || strlen(pic_file_path) == 0)
    {
        return "";
    }

    TiXmlDocument doc;

    // 生成 <?xml version="1.0" encoding="UTF-8"?>
    TiXmlDeclaration* xml_decl = new TiXmlDeclaration("1.0", "UTF-8", "");
    if (xml_decl == NULL)
    {
        return "";
    }
    doc.LinkEndChild(xml_decl);


    // 生成XML根节点KeyData
    TiXmlElement* root_element = new TiXmlElement("KeyData");
    if (root_element != NULL)
    {
        root_element->SetAttribute("PicFilePath", pic_file_path); // 根节点属性
        doc.LinkEndChild(root_element); // 根节点放入XML文档
    }

    int relay_value = 0;
    GetValueByRelay(device_setting->relay, relay_value);
    std::string door_num = RelayToString(relay_value);

    int security_relay_value = 0;
    GetValueByRelay(device_setting->security_relay, security_relay_value);
    std::string security_relay = RelayToString(security_relay_value);

    for (auto it = face_mng_infos.begin(); it != face_mng_infos.end(); it++)
    {
        const FaceMngInfo& face_mng_info = *it;
        TiXmlElement* child_element = new TiXmlElement("Key");
        child_element->SetAttribute("ID", face_mng_info.face_mng_id);
        if(strlen(face_mng_info.name) > 0)  //Name为空 会导致人脸SDK无法注册
        {
            child_element->SetAttribute("Name", face_mng_info.name);
        }
        else
        {
            child_element->SetAttribute("Name", face_mng_info.account);
        }
        child_element->SetAttribute("NameMD5", "");
        child_element->SetAttribute("Account", face_mng_info.account);
        child_element->SetAttribute("FileType", "0");
        child_element->SetAttribute("File", face_mng_info.face_url);
        child_element->SetAttribute("DoorNum",  door_num.c_str());
        if (!security_relay.empty())
        {
            child_element->SetAttribute("SecurityRelay",  security_relay.c_str());
        }
        child_element->SetAttribute("Mon",  "1");
        child_element->SetAttribute("Tue",  "1");
        child_element->SetAttribute("Wed",  "1");
        child_element->SetAttribute("Thur",  "1");
        child_element->SetAttribute("Fri",  "1");
        child_element->SetAttribute("Sat",  "1");
        child_element->SetAttribute("Sun",  "1");
        child_element->SetAttribute("TimeStart",  "00:00");
        child_element->SetAttribute("TimeEnd",  "23:59");

        root_element->LinkEndChild(child_element);
    }

    TiXmlPrinter printer;
    doc.Accept(&printer);
    return printer.CStr();
}

CFaceXmlWriter& CFaceXmlWriter::GetInstance()
{
    static CFaceXmlWriter xml_writer;
    return xml_writer;
}

std::string CFaceXmlWriter::GetPicFilePath(const std::string &mac)
{
    char pic_file_path[128];

    // 灰度下发配置下载域名
    if (strlen(gstCSCONFIGConf.config_server_domain) > 0
        && static_cast<int>(std::hash<std::string>{}(mac) % 100) < gstCSCONFIGConf.config_server_domain_gray_percentage)
    {
        snprintf(pic_file_path, sizeof(pic_file_path), "https://%s:8091/download/face", gstCSCONFIGConf.config_server_domain);
    }
    else if (strlen(gstCSCONFIGConf.fdfs_config_addr) > 0)  
    {
        snprintf(pic_file_path, sizeof(pic_file_path), "https://%s/download/face", gstCSCONFIGConf.fdfs_config_addr);
    }
    else
    {
        snprintf(pic_file_path, sizeof(pic_file_path), "https://%s:443/download/face", gstCSCONFIGConf.web_ip);
    }
    return pic_file_path;
}

int CFaceXmlWriter::WriteXml(DEVICE_SETTING* device_setting, const std::vector<FaceMngInfo>& face_mng_infos, 
   const std::string& face_root_path, int project_type)
{
    if (device_setting == nullptr)
    {
        AK_LOG_WARN << "WriteXml device_setting is null";
        return -1;
    }

    std::string pic_file_path = GetPicFilePath(device_setting->mac);
    std::string face_xml = CFaceXmlHelper::GetInstance().ToXml(pic_file_path.c_str(), face_mng_infos, device_setting);
    
    std::string config_path = face_root_path + device_setting->mac + ".xml";
    DevFileInfoPtr ptr = std::make_shared<DevFileInfo>(device_setting->mac, config_path, face_xml, SHADOW_TYPE::SHADOW_FACECONF,
                                                        project_type, device_setting->id);
    GetWriteFileControlInstance()->AddFileInfo(device_setting->mac, ptr);
    return 0;
}

