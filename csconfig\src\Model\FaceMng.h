﻿#ifndef __FACE_MNG_H__
#define __FACE_MNG_H__

#include <boost/noncopyable.hpp>
#include "BasicDefine.h"
#include <vector>
#include <string>
#include "AKCSMsg.h"
#include "AkcsCommonSt.h"
#include "util.h"
#include "dbinterface/FaceMngDB.h"

class CFaceMng : private boost::noncopyable
{
public:
    static CFaceMng& GetInstance();
    int DaoGetFaceMng(std::vector<FaceMngInfo>& face_mng_infos, uint32_t mng_account_id, uint32_t unit_id, uint32_t personal_account_id);
    int DaoGetFaceMngByPersonalAccountIds(std::vector<FaceMngInfo>& face_mng_infos, const std::vector<DEVICE_CONTACTLIST>& personal_account_ids);
    int DaoGetFaceMngByPersonalAccountIds(std::map<std::string, FaceMngInfo>& list, const std::string &uid_ids);
    int DelFaceMngByMngId(uint32_t mng_account_id);
	/**
	 * 人脸信息根据设备所管的楼栋进行过滤
	*/
	void FilterByUnit(const std::vector<uint32_t> &unit_ids, std::vector<FaceMngInfo>& face_mng_infos);
};

class CFaceXmlHelper : private boost::noncopyable
{
public:
    static CFaceXmlHelper& GetInstance();
    std::string ToXml(const char* pic_file_path, const std::vector<FaceMngInfo>& face_mng_infos, const DEVICE_SETTING* device_setting);
};

class CFaceXmlWriter : private boost::noncopyable
{
public:
    static CFaceXmlWriter& GetInstance();
    int WriteXml(DEVICE_SETTING* device_setting, const std::vector<FaceMngInfo>& face_mng_infos, 
         const std::string& face_root_path, int project_type);
    std::string GetPicFilePath(const std::string &mac);
private:    
};

#endif

