#include <stdio.h>
#include <stdlib.h>
#include <fstream>
#include <cstdlib>
#include <evpp/libevent.h>
#include <evpp/timestamp.h>
#include <evpp/event_loop_thread.h>
#include <evpp/httpc/request.h>
#include <evpp/httpc/conn.h>
#include <evpp/httpc/response.h>
#include "evpp/http/service.h"
#include "evpp/http/context.h"
#include "evpp/http/http_server.h"
#include "HttpServer.h"
#include "util.h"
#include "DevUser.h"
#include "DeviceControl.h"
#include "http/HttpMsgControl.h"
#include "Md5.h"
#include "AES256.h"
#include "ConfigDef.h"
#include "AkcsPasswdConfuse.h"
#include "dbinterface/Shadow.h"
#include "SpecialTubeHandle.h"
#include "json/json.h"
#include "CsmainMsgHandle.h"
#include "AkLogging.h"
#include "SnowFlakeGid.h"
#include "AkcsWebMsgSt.h"
#include "AK.Adapt.pb.h"
#include "AkcsWebPduBase.h"
#include "AkcsCommonDef.h"
#include "UnixSocketControl.h"
#include "dbinterface/Account.h"
#include "dbinterface/DataConfusion.h"
#include "dbinterface/ProjectUserManage.h"
#include "kafka/AkcsKafkaConsumer.h" 
#include "redis/CachePool.h"
#include "RouteClientMng.h"
#include "ShadowMng.h"
#include "EtcdCliMng.h"
#include "MQProduce.h"
#include "KafkaConsumerNotifyConfigTopicHandle.h"
#include "MetricService.h"

//全局变量
extern int g_special_tube;
extern CAkEtcdCliManager* g_etcd_cli_mng ;
extern RouteMQProduce* g_nsq_producer;
extern CSCONFIG_CONF gstCSCONFIGConf;
static const char ak_download_key[] = "ak_download";

static std::string GetNginxAntiHotlink(const std::string& key, const std::string& uri)
{
    time_t timestamp = time(nullptr) + 3600;
    std::string form_token = key + ":" + uri + ":" + std::to_string(timestamp);
    char* token_pointer = akuvox_encrypt::MD5(form_token).GetBase64Md5();

    if (!token_pointer)
    {
        return "";
    }

    std::string token = token_pointer;
    free(token_pointer);
    
    StringReplace(token, "+", "-");
    StringReplace(token, "/", "_");  //nginx防盗链要求token中+/转换为-_

    std::string ret_url = "?token=" + token + "&e=" + std::to_string(timestamp);
    return ret_url;
}

//当请求无效时的处理函数
void DefaultHandler(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    AK_LOG_WARN << "http req route is not define";
    cb("http req route is not define");
}

void HttpReqGetMacUserCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    std::string mac = ctx->GetQuery("mac");
    std::string dst_file = ctx->GetQuery("dstfile");
    std::string ret;    
    
    DEVICE_SETTING* dev = GetDeviceControlInstance()->GetDeviceSettingByMac(mac);
    if (!dev)
    {
        cb(buildErrorHttpMsg(-1, "Devices is not exist, mac=" +  mac));
        return;
    }
    AK_LOG_INFO << "Maintance Mac:" << mac << " request user detail info.";
    
    UserUUIDList list;
    CommunityInfoPtr comm_info = std::make_shared<CommunityInfo>(dev->manager_account_id);
    std::string file_path;
    std::string file_md5;
    DevUser user(comm_info);
    user.GetDetailDataForRequest(dev, list, USER_MAINTANCE_ALL_TRACEID, file_path, file_md5);
    GetDeviceControlInstance()->DestoryDeviceSettingList(dev);

    if (dst_file.length() == 0)
    {
        dst_file = "/tmp/configcat2";
    }
    if (file_path.length() > 0)
    {
        file_path = "/var/www" + file_path;
        char key[] = AES_ENCRYPT_KEY_V1;
        FileAESDecrypt(file_path.c_str(), key, dst_file.c_str());        
    }

    HttpRespKV kv;
    kv.insert(std::map<std::string, std::string>::value_type("FilePath", dst_file));    
    cb(buildCommHttpMsg(HTTP_CODE_SUC, kv));
    return;
}

void HttpReqGetDevicesShadowCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    std::string mac = ctx->GetQuery("mac");
    std::string type = ctx->GetQuery("type");
    std::string token = ctx->GetQuery("token");

    AK_LOG_INFO << "maintance request device shadow, mac:" << mac << ",type:" << type << ",token:" << token;

    // 计算token 
    std::string token_string = mac + ":" + type + ":" + "Akuvox2023";
    std::string token_string_md5 = akuvox_encrypt::MD5(token_string).toStr();
    if (strcmp(token.c_str(), token_string_md5.c_str()) != 0)
    {
        AK_LOG_WARN << "token error, post_token = " << token << ", cal_token_string = " << token_string << ", cal_token_string_md5 = " << token_string_md5;
        cb(buildErrorHttpMsg(-1, "token is error, mac = " +  mac));
        return;
    }
    
    DEVICE_SETTING* dev = GetDeviceControlInstance()->GetDeviceSettingByMac(mac);
    if (!dev)
    {
        AK_LOG_WARN << "device is not exist, mac = " << mac;
        cb(buildErrorHttpMsg(-1, "device is not exist, mac = " +  mac));
        return;
    }
    
    std::stringstream content;
    std::string remote_filepath;
    
    int filetype = ATOI(type.c_str());
    if (filetype == SHADOW_USERALL)
    {
        UserUUIDList list;
        CommunityInfoPtr comm_info = std::make_shared<CommunityInfo>(dev->manager_account_id);
        std::string file_md5;
        DevUser user(comm_info);
        user.GetDetailDataForRequest(dev, list, USER_MAINTANCE_ALL_TRACEID, remote_filepath, file_md5);
    }
    else
    {
        // 获取文件链接
        DevShadow shadow = {0};
        if(dbinterface::Shadow::GetAllShadowByMac(mac, shadow) != 0)
        {
            AK_LOG_WARN << "device shadow is not exist, mac = " << mac;
            cb(buildErrorHttpMsg(-1, "get device shadow failed, mac = " +  mac));
            return;
        }

        switch(filetype)
        {
            case SHADOW_TYPE::SHADOW_CONFIG:
            {
                remote_filepath = shadow.config_storage_path;
                break;
            }
            case SHADOW_TYPE::SHADOW_PRIKEY:
            {
                remote_filepath = shadow.prikey_storage_path;
                break;
            }
            case SHADOW_TYPE::SHADOW_RFID:
            {
                remote_filepath = shadow.rfkey_storage_path;
                break;
            }
            case SHADOW_TYPE::SHADOW_CONTACT:
            {
                remote_filepath = shadow.contac_storage_path;
                break;
            }
            case SHADOW_TYPE::SHADOW_FACECONF:
            {
                remote_filepath = shadow.face_storage_path;
                break;
            }
            case SHADOW_TYPE::SHADOW_SCHE:
            {
                remote_filepath = shadow.schedule_storage_path;
                break;
            }
            case SHADOW_TYPE::SHADOW_USERMETA:
            {
                remote_filepath = shadow.usermeta_storage_path;
                break;
            }
        }
    }

    std::string antihot_link = GetNginxAntiHotlink(ak_download_key, remote_filepath);
    char download_url[256];
    snprintf(download_url, sizeof(download_url), "https://%s%s%s", gstCSCONFIGConf.fdfs_config_addr, remote_filepath.c_str(), antihot_link.c_str());

    std::string local_filepath =  "/tmp/devshadow";
    remove(local_filepath.c_str());

    char wget_cmd[256];
    snprintf(wget_cmd, sizeof(wget_cmd), "wget -c '%s' --no-check-certificate -O %s", download_url, local_filepath.c_str()); 
    system(wget_cmd);

    char enc_key_v1[64] = {0};
    Snprintf(enc_key_v1, sizeof(enc_key_v1), AES_ENCRYPT_KEY_V1);

    FileAESDecrypt(local_filepath.c_str(), enc_key_v1, local_filepath.c_str());  

    std::ifstream input_file(local_filepath.c_str()); 
    if (input_file.is_open()) 
    {
        std::string line;
        while (std::getline(input_file, line)) 
        {   
            DataMasking(line);            
            content << line << "\r\n";
        }
    }
    input_file.close(); // 关闭文件
    
    GetDeviceControlInstance()->DestoryDeviceSettingList(dev);
    
    cb(content.str());
    return;
}


void HttpReqGetDeviceRtspCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    std::string mac = ctx->GetQuery("mac");
    DEVICE_SETTING* dev = GetDeviceControlInstance()->GetDeviceSettingByMac(mac);

    if (!dev)
    {
        AK_LOG_WARN << "device is not exist, mac = " << mac;
        cb(buildErrorHttpMsg(-1, "device is not exist, mac = " +  mac));
        return;
    }

    std::string rtsp_password = dev->rtsp_password;
    PasswdDecode(rtsp_password.c_str(), rtsp_password.size(), dev->rtsp_password, sizeof(dev->rtsp_password));

    char rtsp_url[256];
    snprintf(rtsp_url, sizeof(rtsp_url), "rtsp://user:%s@%s:554/%s", rtsp_password.c_str(), gstCSCONFIGConf.vrtsp_server_domain, mac.c_str());
    
    GetDeviceControlInstance()->DestoryDeviceSettingList(dev);
    
    cb(rtsp_url);
    return;
}

void HttpReqGetMngFilterCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";

    HttpRespKV kv;
    kv.insert(std::map<std::string, std::string>::value_type("mng_id_filter", gstCSCONFIGConf.mng_id_filter));  
    int filter_id = 0;
    int size = 0;
    SpecialTubeHandle::GetInstance().GetSpecialTubeParam(filter_id, size);
    kv.insert(std::map<std::string, std::string>::value_type("special_tube_filter_id", std::to_string(filter_id)));
    kv.insert(std::map<std::string, std::string>::value_type("special_tube_trace_map_size", std::to_string(size)));
    cb(buildCommHttpMsg(HTTP_CODE_SUC, kv));
    return;
}

void HttpReqSetMngFilterCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";

    std::string filter = ctx->GetQuery("filter");
    snprintf(gstCSCONFIGConf.mng_id_filter, sizeof(gstCSCONFIGConf.mng_id_filter), "%s", filter.c_str());

    HttpRespKV kv;
    cb(buildCommHttpMsg(HTTP_CODE_SUC, kv));
    return;
}

//add by czw 20231030: 构建csconfig exporter指标
void HttpReqMetricsCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    LOG_INFO << "http req is [ " << ctx->original_uri() << " ]";
    MetricService* metric_service = MetricService::GetInstance();
    if (metric_service == nullptr)
    {
        AK_LOG_WARN << "metric service init failed.";
        cb("# metric service init failed.");
        return;
    }

    metric_service->UpdateMetrics();
    std::string response = metric_service->ToFormateString();
    cb(response);
    return;
}

void HttpReqGetDataConfusionCacheCountCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int decyrpt_cache_count = dbinterface::DataConfusion::GetDecryptCacheCount();
    int encyrpt_cache_count = dbinterface::DataConfusion::GetEncryptCacheCount();

    AK_LOG_INFO << "HttpReqGetDataConfusionCacheCountCallback decyrpt_cache_count = " << decyrpt_cache_count << ", encyrpt_cache_count = " << encyrpt_cache_count;
    
    std::stringstream resp;
    resp << "encyrpt_cache_count = " << encyrpt_cache_count << "\n"
         << "decyrpt_cache_count = " << decyrpt_cache_count << "\n";
    
    cb(resp.str());
    return;
}

void HttpRefreshProjectDataCallback(evpp::EventLoop* loop, const evpp::http::ContextPtr& ctx, const evpp::http::HTTPSendResponseCallback& cb)
{
    int delay_interval = 1;
    int mng_id = ATOI(ctx->GetQuery("mng_id").c_str());
    
    AK_LOG_INFO << "HttpImportCommunityCallback mng_id = "<< mng_id;

    dbinterface::AccountInfo account;
    if (0 != dbinterface::Account::GetAccountById(mng_id, account))
    {
        AK_LOG_INFO << "HttpImportCommunityCallback GetAccountById failed";
        cb("HttpImportCommunityCallback GetAccountById failed");
        return;
    }

    uint32_t msg_id;
    uint32_t change_type;
    project::PROJECT_TYPE project_type = project::NONE;

    if (account.grade == AccountGrade::COMMUNITY_MANEGER_GRADE)
    {
        change_type = WEB_COMM_IMPORT_COMMUNITY;
        msg_id = MSG_P2A_NOTIFY_COMMUNITY_MESSAGE;
        project_type = project::PROJECT_TYPE::RESIDENCE;
        
        dbinterface::ProjectUserManage::UpdateCommunityAllAccountDataVersion(mng_id);
    }
    else if (account.grade == AccountGrade::OFFICE_MANEGER_GRADE)
    {
        change_type = WEB_OFFICE_IMPORT_OFFICE;
        msg_id = MSG_P2A_NOTIFY_OFFICE_INFO_UPDATE;
        project_type = project::PROJECT_TYPE::OFFICE;
        
        dbinterface::ProjectUserManage::UpdateOfficeAllAccountDataVersion(mng_id);
    }
    else
    {
        cb("not community or office\n");
        return;
    }

    AK::Adapt::WebCommunityModifyNotify msg;
    msg.set_community_id(mng_id);
    msg.set_change_type(change_type);
    msg.set_trace_id(AKCS::Singleton<AKCS::SnowflakeIdWorker>::instance().getId());    

    CAkcsWebPdu web_pdu;
    web_pdu.SetMsgBody(&msg);
    web_pdu.SetProjectType(project_type);
    web_pdu.SetMsgID(msg_id);

    GetUnixSocketControlInstance()->AddCommonMsgToKafka(web_pdu.GetBuffer(), web_pdu.GetLength(), std::to_string(mng_id));

    std::stringstream resp;
    resp << "importCommunity mng_id = " << mng_id << " success\n";
    cb(resp.str());
    
    return;
}

void startHttpServer()
{
    const int port = 9996;
    const int thread_num = 0;
    std::string addr = GetEth0IPAddr(); //监听在内网

    evpp::http::Server server(addr, thread_num, false);//evpp::http::Server 不需要器线程池,网络线程跟业务处理线程同一个即可.
    server.RegisterDefaultHandler(&DefaultHandler);
    server.RegisterHandler("/getMacUser", HttpReqGetMacUserCallback);
    server.RegisterHandler("/getDevicesShadow", HttpReqGetDevicesShadowCallback);
    server.RegisterHandler("/getDeviceRtspUrl", HttpReqGetDeviceRtspCallback);
    server.RegisterHandler("/getMngFilter", HttpReqGetMngFilterCallback);
    server.RegisterHandler("/setMngFilter", HttpReqSetMngFilterCallback);    
    server.RegisterHandler("/getDataConfusionCacheCount", HttpReqGetDataConfusionCacheCountCallback);   
    server.RegisterHandler("/refreshProjectData", HttpRefreshProjectDataCallback);   
    server.RegisterHandler("/metrics", HttpReqMetricsCallback);
    
    server.Init(port);
    server.Start();
    
    return;
}
