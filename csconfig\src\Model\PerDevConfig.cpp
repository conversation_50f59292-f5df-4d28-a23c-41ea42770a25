#include <sstream>
#include "DevConfig.h"
#include "PerDevConfig.h"
#include <string.h>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "ConfigDef.h" 
#include "AdaptUtility.h"
#include "json/json.h"
#include "util.h"
#include "util_judge.h"
#include "AkLogging.h"
#include "DeviceSetting.h"
#include "AkcsWebMsgSt.h"
#include "CommunityMng.h"
#include "DeviceControl.h"
#include "AES256.h"
#include "AkcsMonitor.h"
#include "encrypt/Md5.h"
#include "AkcsCommonDef.h"
#include "ShadowMng.h"
#include "PersonalAccount.h"
#include "WriteFileControl.h"
#include "ConfigCommon.h"
#include "dbinterface/Account.h" 
#include "dbinterface/PmAccountMap.h" 
#include "dbinterface/VideoStorage.h"
#include "dbinterface/VideoStorageDevice.h"
#include "dbinterface/resident/ExtraDevice.h"
#include "dbinterface/resident/ExtraDeviceRelayList.h"
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/resident/IndoorMonitorConfig.h"
#include "dbinterface/resident/ResidentPersonalAccount.h" 
#include "util_judge.h"
#include "AkcsOemDefine.h"
#include "dbinterface/resident/ExtraDeviceRelayAction.h"
#include "util_relay.h"
extern CSCONFIG_CONF gstCSCONFIGConf;
extern OldDeviceTimezone g_old_dev_timezone_list;
extern std::map<string, AKCS_DST> g_time_zone_DST;  

int PerDevConfig::WriteDevListFiles(DEVICE_SETTING* dev_list)
{
    DEVICE_SETTING *dev = dev_list;
    int ret = 0;
    while (dev != NULL)
    {
        ret = WriteFiles(dev);
        if (ret !=0 )
        {
            return ret;
        }
        dev = dev->next;
    }
    return ret;
}

void PerDevConfig::WriteRelayConfig(std::stringstream &config, DEVICE_SETTING* dev)
{
    //relay 配置
    config << CONFIG_RELAY_DMTF_OPTION << 0 << "\n";
    std::vector<RELAY_INFO> relays;
    ParseRelay(dev->relay, relays);
    int i = 1;
    for (auto& Relay : relays)
    {
        if (!Relay.enable)
        {
            i++;
            continue;
        }
        switch (i)
        {
            case 1:
                config << CONFIG_RELAY_DMTF_CODE1 << Relay.dtmf << "\n";
                config << CONFIG_RELAY_DMTF_NAME1 << Relay.name << "\n";
                config << CONFIG_RELAY_RELAYUNLOCK1 << Relay.access_control << "\n";
                break;
    
            case 2:
                config << CONFIG_RELAY_DMTF_CODE2 << Relay.dtmf << "\n";
                config << CONFIG_RELAY_DMTF_NAME2 << Relay.name << "\n";
                config << CONFIG_RELAY_RELAYUNLOCK2 << Relay.access_control << "\n";
                break;
    
            case 3:
                config << CONFIG_RELAY_DMTF_CODE3 << Relay.dtmf << "\n";
                config << CONFIG_RELAY_DMTF_NAME3 << Relay.name << "\n";
                config << CONFIG_RELAY_RELAYUNLOCK3 << Relay.access_control << "\n";
                break;
                
            case 4:
                config << CONFIG_RELAY_DMTF_CODE4 << Relay.dtmf << "\n";
                config << CONFIG_RELAY_DMTF_NAME4 << Relay.name << "\n";
                config << CONFIG_RELAY_RELAYUNLOCK4 << Relay.access_control << "\n";
                break;
        }
        i++;
    }
    
    if (strlen(dev->security_relay) > 0)
    {
        int i = 1;
        std::vector<RELAY_INFO> security_relay_infos;
        ParseRelay(dev->security_relay, security_relay_infos);
        for (auto &relay : security_relay_infos)
        {
            if (!relay.enable)
            {
                i++;
                continue;
            }
            switch (i)
            {
                case 1:
                    config << CONFIG_SECURITY_RELAY_DMTF_CODE1 << relay.dtmf << "\n";
                    config << CONFIG_SECURITY_RELAY_DMTF_NAME1 << relay.name << "\n";
                    config << CONFIG_SECURITY_RELAY_ENABLED1 << relay.enable << "\n";
                    config << CONFIG_SECURITY_RELAY_RELAYUNLOCK1 << relay.access_control << "\n";
                    break;

                case 2:
                    config << CONFIG_SECURITY_RELAY_DMTF_CODE2 << relay.dtmf << "\n";
                    config << CONFIG_SECURITY_RELAY_DMTF_NAME2 << relay.name << "\n";
                    config << CONFIG_SECURITY_RELAY_ENABLED2 << relay.enable << "\n";
                    config << CONFIG_SECURITY_RELAY_RELAYUNLOCK2 << relay.access_control << "\n";
                    break;

                case 3:
                    config << CONFIG_SECURITY_RELAY_DMTF_CODE3 << relay.dtmf << "\n";
                    config << CONFIG_SECURITY_RELAY_DMTF_NAME3 << relay.name << "\n";
                    config << CONFIG_SECURITY_RELAY_ENABLED3 << relay.enable << "\n";
                    config << CONFIG_SECURITY_RELAY_RELAYUNLOCK3 << relay.access_control << "\n";
                    break;

                case 4:
                    config << CONFIG_SECURITY_RELAY_DMTF_CODE4 << relay.dtmf << "\n";
                    config << CONFIG_SECURITY_RELAY_DMTF_NAME4 << relay.name << "\n";
                    config << CONFIG_SECURITY_RELAY_ENABLED4 << relay.enable << "\n";
                    config << CONFIG_SECURITY_RELAY_RELAYUNLOCK4 << relay.access_control << "\n";
                    break;

                default:
                    //只支持4个
                    break;
            }
            i++;
        }
    }

}

void PerDevConfig::WriteTimeZoneConfig(std::stringstream &config, DEVICE_SETTING* dev)
{
    /*4.6 下发平台时区配置,5.4 下发时间格式,add by czw*/
    if (dev->dclient_version >= D_CLIENT_VERSION_4600)    //新版本设备时区列表才一致，旧版本不做同步
    {
        std::string ntp_time = "GMT";
        std::string city_name;
        std::string timezone;
        int time_format = 0;
        if (0 == dbinterface::ResidentPersonalAccount::GetDevTimeZoneConfig(dev->grade, dev->manager_account_id, dev->flags, dev->device_node, timezone, time_format))
        {
            std::size_t found = timezone.find(' ');
            if (found != std::string::npos)
            {
                ntp_time += timezone.substr(0, found);

                city_name = timezone.substr(found + 1);
            }
            //旧版本设备用了新时区的不配
            if (dev->dclient_version >= D_CLIENT_VERSION_5400 || \
                (city_name != CONFIG_NEW_TIMEZONE_NUUK && city_name != CONFIG_NEW_TIMEZONE_KOLKATA))  
            {
                config << CONFIG_CLOUDSERVER_TOMEZONE << ntp_time << "\n";
                config << CONFIG_CLOUDSERVER_CITYNAME << city_name << "\n";
            }
            
            if (dev->dclient_version >= D_CLIENT_VERSION_5400) 
            {
                int hour_format = CustomizeDateFormatToDeviceConfigValue(time_format, 1);
                config << CONFIG_CLOUDSERVER_TIMEFORMAT << hour_format << "\n";
                int date_format = CustomizeDateFormatToDeviceConfigValue(time_format, 2);
                config << CONFIG_CLOUDSERVER_DATEFORMAT << date_format << "\n";
            }
            if (dev->dclient_version < D_CLIENT_VERSION_5400)
            {
                std::string timezone = g_old_dev_timezone_list[city_name];
                config << CONFIG_CLOUDSERVER_TOMEZONE << timezone << "\n"; 
            }
        }
    }

}

void PerDevConfig::WriteDoorConfig(std::stringstream &config, DEVICE_SETTING* dev)
{
    config << CONFIG_DTMF_ENABLE << "1" << "\n"
             << CONFIG_DTMF_CODE1 << "11" << "\n"
             << CONFIG_RTSP_ENABLE << "1" << "\n"
             << CONFIG_RTSP_VIDEO << "1" << "\n"
             << CONFIG_RTSP_CODEC << "0" << "\n"
             //<< CONFIG_RTSP_H264_RESOLUTION << "3" << "\n"
             << CONFIG_RTSP_H264_FRAMERATE << "30" << "\n";
    //<< CONFIG_RTSP_H264_BITRATE << "256" << "\n";
    //v4.0 这个群组号是根据联动找设备时候带出来的 //社区公共设备应该是空值
    //过期的只有室内机IP，已经在查数据这步处理了
    //V5.0 单住户加入了落地，app和落地有呼叫顺序，要走callloop
    if (dev->dclient_version < D_CLIENT_VERSION_5000) //V5.0 单住户加入了落地，app和落地有呼叫顺序，要走callloop
    {
        config << CONFIG_SIP_GROUP_ACCOUNT << dev->push_button << "\n";
    } 
    else
    {
        config << CONFIG_SIP_GROUP_ACCOUNT << "" << "\n";
    }
    
    config << "Config.DoorSetting.RTSP.H264Resolution = 4\n";
    config << "Config.DoorSetting.RTSP.H264BitRate = 2048\n";

    if (!dev->flag & DEVICE_SETTING_FLAG_PER_PUBLIC)
    {
        config << CONFIG_FEATURES_CALLROBIN_ENABLE << dev->enable_robin_call << "\n"
                 << CONFIG_FEATURES_CALLROBIN_NUM << dev->robin_call_val << "\n"
                 << CONFIG_FEATURES_CALLROBIN_TIME << dev->robin_call_time << "\n";
                 
        int enable_motion = CDeviceSetting::GetInstance()->GetMotionDetection(dev->dclient_version, dev->enable_motion);
        config << CONFIG_DOORSETTING_MOTION_DETECT_ENABLE << enable_motion << "\n"
                 << CONFIG_DOORSETTING_MOTION_DETECT_TIME << dev->motion_time << "\n";
    }
    else //个人公共设备全部配置空
    {
        config << CONFIG_FEATURES_CALLROBIN_ENABLE << "0" << "\n"
                 << CONFIG_FEATURES_CALLROBIN_NUM << "" << "\n"
                 << CONFIG_FEATURES_CALLROBIN_TIME << "20" << "\n";
    }

    //relay 配置
    WriteRelayConfig(config, dev);    
}

void PerDevConfig::WriteCameraConfig(std::stringstream &config, const ThirdPartyCamreaInfo& camera)
{
    config <<  "Config.DoorSetting.RTSP.ThirdPartyCameraName=" << camera.location << "\n";
    config <<  "Config.DoorSetting.RTSP.ThirdPartyCameraUUID=" << camera.camera_uuid << "\n";
    config <<  "Config.DoorSetting.RTSP.ThirdPartyCameraUrl=" << camera.rtsp_url << "\n";
    config <<  "Config.DoorSetting.RTSP.ThirdPartyCameraRTSPUser=" << camera.username << "\n";
    config <<  "Config.DoorSetting.RTSP.ThirdPartyCameraRTSPPwd=" << camera.passwd << "\n";
}

void PerDevConfig::WriteThirdPartyCameraConfig(std::stringstream &config, DEVICE_SETTING* dev)
{
    //三方摄像头给门口机/管理机/门禁下发配置
    if (dev->dclient_version >= D_CLIENT_VERSION_6500 && (dev->type == DEVICE_TYPE_STAIR 
        ||dev->type == DEVICE_TYPE_DOOR || dev->type == DEVICE_TYPE_MANAGEMENT || dev->type == DEVICE_TYPE_INDOOR || dev->type == DEVICE_TYPE_ACCESS))
    {
        ThirdPartyCamreaInfo camera;
        if(0 == dbinterface::PersonalThirdPartyCamrea::GetPersonalThirdPartyCameraByMac(dev->mac, camera))
        {
            WriteCameraConfig(config, camera);
        }
    }

    //三方摄像头高级功能过期下发给室内机
    if (dev->dclient_version >= D_CLIENT_VERSION_6500 
        && (dev->type == DEVICE_TYPE_INDOOR || dev->type == DEVICE_TYPE_MANAGEMENT))
    {
        ResidentPerAccount account;
        memset(&account, 0, sizeof(account));
        int is_feature_expire = 0;
        if (0 == dbinterface::ResidentPersonalAccount::GetUidAccount(dev->device_node, account))
        {
            is_feature_expire = account.is_feature_expire;
        }

        if (!is_feature_expire)
        {
            config <<  "Config.Settings.THIRDPARTYCAMERA.Expire=0\n";
        }
        else
        {
            config <<  "Config.Settings.THIRDPARTYCAMERA.Expire=1\n";
        }
    }
}

void PerDevConfig::WriteExternRelayConfig(std::stringstream &config, DEVICE_SETTING* dev)
{
    if (!akjudge::DevIndoorType(dev->type))
    {
        return;
    }
    IndoorMonitorConfigInfo indoor_monitor_config;
    if (0 != dbinterface::IndoorMonitorConfig::GetIndoorMonitorConfigByDevUUID(dev->uuid, indoor_monitor_config))
    {
        AK_LOG_WARN << "device indoor config not found, dev uuid=" << dev->uuid;
        return;
    }

    //IndoorMonitorConfig.Meta为0，表示云上面没有进行配置，不刷配置
    if (indoor_monitor_config.meta == 0)
    {
        AK_LOG_INFO << "Skip external relay config refresh, IndoorMonitorConfig.Meta is 0 (ExternalDevice module not modified), uuid: " << indoor_monitor_config.uuid;
        return;
    }
    
    // 先重置配置, 再按实际的配置下发
    ResetExtraDeviceConfigs(config);
    
    //未开启外接relay 下发总开关关闭
    if (indoor_monitor_config.ex_relay_switch == 0)
    {
        config << "Config.Indoor.EXTRELAY.Type=" << ExtraRelayType::TYPE_NONE << "\n";
        return;
    }
    //设备有一种类型弃用了 web存的type与实际的偏移一位
    config << "Config.Indoor.EXTRELAY.Type=" << indoor_monitor_config_relay_type_map.at(indoor_monitor_config.ex_relay_type) << "\n";
    config << "Config.Indoor.EXTRELAY.Mode=" <<  indoor_monitor_config_relay_mode_map.at(indoor_monitor_config.ex_relay_mode) << "\n";
    if (indoor_monitor_config.ex_relay_mode == ExtraRelayMode::MODE_ETHERNET)
    {
        config << "Config.Indoor.EXTRELAY.RelayIpAddr=" << indoor_monitor_config.ex_relay_ip << "\n";
        config << "Config.Indoor.EXTRELAY.RelayPort=" << indoor_monitor_config.ex_relay_port << "\n";
    }

    WriteExRelayListConfig(config, indoor_monitor_config);
}

void PerDevConfig::WriteExRelayListConfig(std::stringstream &config, const IndoorMonitorConfigInfo& indoor_monitor_config)
{
    ExtraDeviceInfoList extra_devices;
    if (0 != dbinterface::ExtraDevice::GetExtraDevicesByIndoorConfigUUID(indoor_monitor_config.uuid, extra_devices))
    {
        AK_LOG_WARN << "Failed to get extra devices for uuid: " << indoor_monitor_config.uuid;
        return;
    }
    
    // 遍历所有设备并创建配置
    ExtraDeviceRelayList extra_device_infos;
    for (const auto& device : extra_devices)
    {
        InitExtraDeviceInfos(device, indoor_monitor_config, extra_device_infos);
    }
    
    // 根据继电器类型生成配置
    WriteConfigByRelayType(config, indoor_monitor_config, extra_device_infos);
    
    AK_LOG_INFO << "Loaded relay config (type=" << indoor_monitor_config.ex_relay_type 
               << ") for uuid: " << indoor_monitor_config.uuid 
               << ", devices=" << extra_devices.size();
}

void PerDevConfig::InitExtraDeviceInfos(const ExtraDeviceInfo& device, const IndoorMonitorConfigInfo& indoor_monitor_config, 
                                            ExtraDeviceRelayList& extra_device_infos)
{
    ExtraDeviceRelay extra_device_info;
    extra_device_info.extra_device_ = device;
    
    // 如果设备未启用，直接添加设备信息并返回
    if (device.enable_switch != 1)
    {
        extra_device_infos.push_back(extra_device_info);
        return;
    }
    
    // 获取设备的继电器列表
    std::vector<std::string> relay_uuids;
    if (0 != dbinterface::ExtraDeviceRelayList::GetRelayListByExtraDevice(device, relay_uuids, extra_device_info.relay_list_))
    {
        AK_LOG_WARN << "Failed to get relay list for device " << device.uuid;
        extra_device_infos.push_back(extra_device_info);
        return;
    }
    
    if (0 != dbinterface::ExtraDeviceRelayAction::GetRelayActionsByRelayList(relay_uuids, extra_device_info.relay_uuid_to_actions_map_))
    {
        AK_LOG_WARN << "Failed to get relay actions for device " << device.uuid;
        extra_device_infos.push_back(extra_device_info);
        return;
    }
    
    AK_LOG_INFO << "Loaded relay config for device index " << device.device_index
               << " in indoor config: " << indoor_monitor_config.uuid
               << ", relays=" << extra_device_info.relay_list_.size()
               << ", actions=" << extra_device_info.relay_uuid_to_actions_map_.size();
    
    // 添加设备配置
    extra_device_infos.push_back(extra_device_info);
}

void PerDevConfig::WriteConfigByRelayType(std::stringstream &config, const IndoorMonitorConfigInfo& indoor_monitor_config, 
                                         const ExtraDeviceRelayList& extra_device_infos)
{
    if (indoor_monitor_config.ex_relay_type == ExtraRelayType::TYPE_RSAC_C1_R8)
    {
        WriteR8RelayConfigByDeviceConfigs(config, extra_device_infos);
    }
    else
    {
        WriteLegacyRelayConfigByDeviceConfigs(config, extra_device_infos);
    }
}

void PerDevConfig::WriteR8RelayConfigByDeviceConfigs(std::stringstream &config, const ExtraDeviceRelayList& extra_device_infos)
{
    AK_LOG_INFO << "Writing RSAC_C1_R8 relay config with " << extra_device_infos.size() << " device configs";
    
    for (auto extra_device_info : extra_device_infos)
    {
        // 写入设备配置
        WriteExtraDeviceEnableConfig(config, extra_device_info.extra_device_.device_index, extra_device_info.extra_device_.enable_switch, extra_device_info.extra_device_.device_address);
        
        // 如果设备开关关闭，跳过后续继电器配置
        if (extra_device_info.extra_device_.enable_switch == 0)
        {
            continue;
        }
        
        WriteR8RelayConfigForDevice(config, extra_device_info);
    }
}

void PerDevConfig::WriteR8RelayConfigForDevice(std::stringstream &config, ExtraDeviceRelay& extra_device_info)
{
    // 循环所有的relay，为每个relay下的action生成配置
    int shutter_index = 0;
    int shade_index = 0;
    for (const auto& relay : extra_device_info.relay_list_)
    {
        // 确保继电器属于当前设备
        if (strcmp(relay.extra_device_uuid, extra_device_info.extra_device_.uuid) != 0)
        {
            AK_LOG_WARN << "Relay " << relay.uuid << " does not belong to extra device " << extra_device_info.extra_device_.uuid;
            continue;
        }
        
        // 根据relay类型确定索引，对于同一种function可以配置up down的，需要用不同index的autop
        int function_index = 0;
        if (relay.function == dbinterface::RELAY_FUNCTION_SHUTTER)
        {
            function_index = shutter_index++;
        }
        else if (relay.function == dbinterface::RELAY_FUNCTION_SHADE)
        {
            function_index = shade_index++;
        }
        
        // 获取该relay下的所有actions
        auto actions_range = extra_device_info.relay_uuid_to_actions_map_.equal_range(relay.uuid);
        for (auto action_it = actions_range.first; action_it != actions_range.second; ++action_it)
        {
            const auto& action = action_it->second;
            
            // 写入继电器配置，内部会计算function_value
            WriteRelayActionConfigWithFunction(config, action, relay, extra_device_info, function_index);
        }
    }
}

void PerDevConfig::WriteRelayActionConfigWithFunction(std::stringstream &config, 
                                           const ExtraDeviceRelayActionInfo& action, 
                                           const ExtraDeviceRelayListInfo& relay, 
                                           ExtraDeviceRelay& extra_device_info, 
                                           int function_index)
{
    // 从extra_device_info中获取device_index
    int device_index = extra_device_info.extra_device_.device_index;
    
    bool is_extern_relay;
    int output_index;
    if (GetRelayTypeAndIndex(action.output, is_extern_relay, output_index) !=0 )
    {
        AK_LOG_WARN << "Unknown output type: " << action.output << " for action uuid: " << action.uuid;
        return;
    }
    
    // 获取计算后的function值
    int function_value = extra_device_info.CalculateFunctionValue(relay, action.action_type, function_index);
    
    // 生成EXTRELAY或DIGITALOUTPUT配置
    if (is_extern_relay)
    {
        WriteExtRelayStatusConfig(config, device_index, output_index, relay.enable_switch, function_value, ConvertExtraRelayHoldDelayForDevice(ATOI(action.hold_delay)), relay.name);
    }
    else
    {
        WriteDigitalOutputConfig(config, device_index, output_index, relay.enable_switch, function_value, ConvertExtraRelayHoldDelayForDevice(ATOI(action.hold_delay)), relay.name);
    }
    
    // 生成DIGITALINPUT配置
    WriteDigitalInputConfig(config, action, relay, device_index);
}

void PerDevConfig::WriteDigitalInputConfig(std::stringstream &config, 
                                  const ExtraDeviceRelayActionInfo& action, 
                                  const ExtraDeviceRelayListInfo& relay, 
                                  int device_index)
{
    if (strlen(action.input) > 0)
    {
        // 从input字段解析索引（IT1->1, IT2->2, ... IT8->8）
        int input_index = GetRelayOutputValue(action.input);
        if (input_index == 0) 
        {
            AK_LOG_WARN << "Invalid input format: " << action.input;
            return;
        }
        
        // 根据ConnectType决定ShowPopup值：1-Button(ShowPopup=0), 2-Sensor(ShowPopup=1)
        int show_popup = (action.connect_type == 2) ? 1 : 0;
        // 从output字段获取LinkRelayorOutput的值
        int output_value = GetRelayOutputValue(action.output);
        
        WriteDigitalInputConfigs(config, device_index, input_index, output_value, show_popup, action.trigger_model, relay.name, relay.enable_switch);
    }
}



void PerDevConfig::WriteLegacyRelayConfigByDeviceConfigs(std::stringstream &config, const ExtraDeviceRelayList& extra_device_infos)
{
    // 为MK48和HF8000等类型设备生成配置，只会有一个设备
    AK_LOG_INFO << "Writing legacy relay config for " << extra_device_infos.size() << " device configs";
    
    if (extra_device_infos.empty()) {
        return;
    }
    
    // 非R8设备只有一个设备，直接取第一个
    const auto& extra_device_info = extra_device_infos[0];
    
    // 循环所有的relay，处理每个relay下的actions
    for (const auto& relay : extra_device_info.relay_list_)
    {
        auto actions_range = extra_device_info.relay_uuid_to_actions_map_.equal_range(relay.uuid);
        for (auto action_it = actions_range.first; action_it != actions_range.second; ++action_it)
        {
            const auto& action = action_it->second;
            
            int output_index = GetRelayOutputValue(action.output);
            if (output_index == 0 || output_index > 8) 
            {
                AK_LOG_WARN << "Invalid output format for legacy device: " << action.output;
                continue;
            }
            
            // 计算功能值（Legacy设备不会有SHUTTER/SHADE类型，直接返回原始值）
            int function_value = extra_device_info.CalculateFunctionValue(relay, action.action_type, 0);

            config << CONFIG_INDOOR_EXTRELAY_STATUS << output_index << "=" << relay.enable_switch << "\n";
            config << CONFIG_INDOOR_EXTRELAY_FUNCTION << output_index << "=" << function_value << "\n";
            config << CONFIG_INDOOR_EXTRELAY_DISPLAY_NAME << output_index << "=" << relay.name << "\n";
            // 只有灯才下发HOLD_DELAY和INTERVAL配置。因为其它类型，云端没有配置入口。
            if (function_value == dbinterface::FunctionDeviceValues::FUNCTION_DEVICES_VALUE_LIGHT)
            {
                config << CONFIG_INDOOR_EXTRELAY_HOLD_DELAY << output_index << "=" << ConvertExtraRelayHoldDelayForDevice(ATOI(action.hold_delay)) << "\n";
                config << CONFIG_INDOOR_EXTRELAY_INTERVAL << output_index << "=" << ConvertExtraRelayHoldDelayForDevice(ATOI(action.hold_delay)) << "\n";
            }
        }
    }
}

int PerDevConfig::WriteVideoRecordConfig(std::stringstream &config, DEVICE_SETTING* dev)
{
    // 判断设备是否支持视频存储功能
    if (!SwitchHandle(dev->fun_bit, FUNC_DEV_SUPPORT_VIDEO_RECORD))
    {
        return -1;
    }

    // 判断视频存储开关是否开启
    AK_LOG_INFO << "WriteVideoRecordConfig mac = " << dev->mac << ", node uuid = " << dev->node_uuid;
    VideoStorageInfo video_storage_info;
    if (0 != dbinterface::VideoStorage::GetVideoStorageByPersonalAccountUUID(dev->node_uuid, video_storage_info))
    {
        return -1;
    }
    
    // 判断门口机是否被选中
    if (akjudge::DevDoorType(dev->type))
    {
        VideoStorageDeviceInfo video_storage_device_info;
        if (DatabaseExistenceStatus::EXIST != dbinterface::VideoStorageDevice::GetVideoStorageDeviceInfo(dev->uuid, video_storage_device_info))
        {
            return -1;
        }
    }
    
    config << CONFIG_VIDEO_RECORD_CLOUD << "1" << "\n";
    config << CONFIG_VIDEO_RECORD_ENABLE << "1" << "\n";
    config << CONFIG_VIDEO_RECORD_LENGTH << "10" << "\n";
    config << CONFIG_VIDEO_RECORD_MOTION << "1" << "\n";
    config << CONFIG_VIDEO_RECORD_CALL_IN << "1" << "\n";
    config << CONFIG_VIDEO_RECORD_CALL_OUT << "1" << "\n";
    config << CONFIG_VIDEO_RECORD_ACCESS_DENIED << "1" << "\n";
    config << CONFIG_VIDEO_RECORD_ACCESS_GRANTED << "1" << "\n";
    config << CONFIG_VIDEO_RECORD_OPENDOOR_ALARM << "1" << "\n";
    config << CONFIG_VIDEO_RECORD_PACKAGE << "1" << "\n";
    
    if (video_storage_info.is_enable_call_audio)
    {
        config << CONFIG_VIDEO_RECORD_INCLUDE_AUDIO << "1" << "\n";
    }
    else
    {
        config << CONFIG_VIDEO_RECORD_INCLUDE_AUDIO << "0" << "\n";
    }
    return 0;
}

int PerDevConfig::WriteFiles(DEVICE_SETTING* dev)
{
    if (dev == NULL)
    {
        AK_LOG_WARN << "PerDevConfig::WriteFiles The param is null .";
        return -1;
    }

    //chenzhx 这个值不能一直变，相关设备/账户更新配置，会影响到这个配置重新下载
    int sip_port = HashtabHashString(dev->sip_password) % 55534 + 10000;

    std::stringstream config;
    config << CONFIG_ENABLE << "1" << "\n"
           << CONFIG_LABLE  << dev->sip_account << "\n"
           << CONFIG_DISPLAYNAME << dev->location << "\n"
           << CONFIG_USERNAME << dev->sip_account << "\n"
           << "Config.Account1.OUTPROXY.Enable=0" << "\n"
           << CONFIG_AUTHNAME << dev->sip_account << "\n"
           << CONFIG_PWD << dev->sip_password << "\n"
           << CONFIG_TIMEOUT << PERSONNAL_SIP_UA_TIMEOUT << "\n"
           << CONFIG_SIP_NAT_RPORT << "1" << "\n"
           << CONFIG_NAT_UDP_ENABLE <<  "1" << "\n"
           << CONFIG_NAT_UDP_INTERVAL <<  "30" << "\n"
           << CONFIG_CLOUDSERVER_FTP_USER << "akuvox" << "\n"
           << CONFIG_CLOUDSERVER_FTP_PWD << "pu6HYKvTkyGstq" << "\n"
           << CONFIG_CLOUDSERVER_DEV_EXPIRE << "0" << "\n"
           << "Config.Account1.Video00.ProfileLevel=2" << "\n"
           << "Config.Account1.Video00.MaxBR=512" << "\n"
           << "Config.Features.VIDEO_CODEC_PARAM.ProfileLevel=720P" << "\n"
           << "Config.Account1.SIP.ListenPortMin=" << sip_port << "\n"
           << "Config.Account1.SIP.ListenPortMax=" << sip_port + 10 << "\n";
            
    if (dev->dclient_version < D_CLIENT_VERSION_4400)
    {
        config << CONFIG_SERVER
                 << gstCSCONFIGConf.cspbx_outer_ip << "\n"
                 << CONFIG_PORT << gstCSCONFIGConf.cspbx_outer_port << "\n"
                 << CONFIG_CLOUDSERVER_FTP_URL << "ftp://" << gstCSCONFIGConf.ftp_ip << ":21" << "\n";
    }

    if (dev->dclient_version >= D_CLIENT_VERSION_5200)
    {
        config << "Config.Account1.Audio0.Enable=0" << "\n" //PCMU
                 << "Config.Account1.Audio1.Enable=1" << "\n" //PCMA
                 << "Config.Account1.Audio4.Enable=0" << "\n" //G729
                 << "Config.Account1.Audio5.Enable=0" << "\n"; //G722
    }
    
    if (dev->dclient_version >= D_CLIENT_VERSION_6100)
    {
        //******** 涂鸦强制用pcmu
        config << "Config.Account1.Audio0.Enable=1" << "\n"; //PCMU
    }

    if (dev->oem_id == OEMID_HAGER)
    {
        config << "Config.Account1.Audio1.Enable=1" << "\n" //PCMA
                   << "Config.Account1.Audio1.Priority=1" << "\n" //PCMA
                   << "Config.Account1.Audio0.Enable=1" << "\n" //PCMU
                   << "Config.Account1.Audio0.Priority=2" << "\n" //PCMU
                   << "Config.Account1.Audio5.Enable=1" << "\n" //G722
                   << "Config.Account1.Audio5.Priority=3" << "\n"; //G722
    }

    //门口机、梯口机增加sip群组账号
    if (akjudge::DevDoorType(dev->type))
    {
       WriteDoorConfig(config, dev); 
    }

    if (dev->flag & DEVICE_SETTING_FLAG_PER_PUBLIC)//个人公共
    {
        config << CONFIG_CLOUDSERVER_TYPE << CLOUD_SERVER_TYPE_PERSONAL_COMMUNITY << "\n";
        config <<  CONFIG_CLOUDSERVER_DOOR_OPEN_LIMIT << 1 << "\n";
    }
    else //个人
    {
        config << CONFIG_CLOUDSERVER_TYPE << CLOUD_SERVER_TYPE_PERSONAL << "\n";
        config <<  CONFIG_CLOUDSERVER_DOOR_OPEN_LIMIT << 1 << "\n";
    }

    WriteTimeZoneConfig(config, dev);

    //******** 加入DEVICE_TYPE_DOOR，设备已经默认是2了，就是防止设备移来移去配置没有更新
    if (akjudge::DevDoorType(dev->type))
    {
        if (dev->stair_show == 0)// 0是个人stair类型 用2显示app/devices
        {
            dev->stair_show = 2;
        }
        config <<  CONFIG_CONTACT_SHOW_TEYP << dev->stair_show << "\n";
    }
    
    if (dev->type == DEVICE_TYPE_INDOOR)
    {
        //all call  50000-65535
        int port = ATOI(dev->device_node) % 15534 + 50000;
        config << "Config.Multicast.SELECTEDGROUP.SelectedGroup=1\n"
                 << "Config.Multicast.GROUP1.IP=**********:" << port << "\n"
                 << "Config.Multicast.LISTEN1.IP=**********:" << port << "\n"
                 << "Config.Netcast.SELECTEDGROUP.SelectedGroup=1\n"
                 << "Config.Netcast.GROUP1.IP=**********:" << port << "\n"
                 << "Config.Netcast.LISTEN1.IP=**********:" << port << "\n";
        
        WriteVoiceAssistantConfig(config, dev);
    }
    //写siptype siphacking
    if (dev->dclient_version >= D_CLIENT_VERSION_4400)
    {
        config << "Config.Account1.CALL.PreventSIPHacking=1\n";

    }
    if (DevMngSipType_NONE == mng_sip_type_)
    {
        config << "Config.Account1.SIP.TransType=" << dev->sip_type << "\n";
    }
    else
    {
        config << "Config.Account1.SIP.TransType=" << mng_sip_type_ << "\n";
    }
    //rtp confuse 6.0
    if (dev->dclient_version >= D_CLIENT_VERSION_6000)
    {
        config << "Config.Account1.CALL.AudioVideoConfuse=" << rtp_confuse_ << "\n";
    }

    //v5.0加入设备名称配置项
    config << "Config.DoorSetting.DEVICENODE.Location=" << dev->location << "\n";

    if (dev->dclient_version >= D_CLIENT_VERSION_5200 && !SwitchHandle(dev->fun_bit, FUNC_DEV_GET_REMOTECONFIG_ADDR_BY_DCLIENT))
    {
        config << "Config.DoorSetting.DEVICENODE.SSHPassSrv=" << gstCSCONFIGConf.ssh_proxy_domain << "\n";
    }
     //******** add by chenzhx        
    if (gstCSCONFIGConf.server_type == ServerArea::ccloud && akjudge::DevDoorType(dev->type) && dev->dclient_version < D_CLIENT_VERSION_6400)
    {
        config <<  "Config.Account1.DTMF.Type=4" << "\n";
    }

    //三方摄像头下发配置
    WriteThirdPartyCameraConfig(config, dev);

    if (dev->dclient_version >= D_CLIENT_VERSION_6400 && akjudge::DevDoorType(dev->type))
    {
        //1:Inband;2:RFC2833;3:Info;4:Info+Inband;5:Info+RFC2833;6:Info+Inband+RFC2833
        //add by czw, dtmf国内外方式不同处理
        if(gstCSCONFIGConf.server_type == ServerArea::ccloud)
        {
            config <<  "Config.Account1.DTMF.Type=6" << "\n";
        }
        else
        {
            config <<  "Config.Account1.DTMF.Type=2" << "\n";
        }      
    }

   
    if (dev->type == DEVICE_TYPE_INDOOR)
    {
         // 室内机是否开启转发autop配置, 0:Off,  1:On
        config << "Config.Indoor.HidePage.TransferCall=" << dev->repost << "\n";

        // 室内机relay延时配置
        WriteIndoorRelayDelayConfig(config, dev->relay);
        
    }


    // rtsps 开关
    config << CONFIG_RTSPS_ENABLE << mng_rtsp_type_ << "\n";
    UpdateUcloudVideoBitRate(dev->SWVer, config);
    UpdateSipSrtpConfig(mng_sip_type_, dev->fun_bit, config);
    WriteExternRelayConfig(config, dev);
    UpdateAuxCameraConfig(dev->fun_bit, config);
    UpdateHighResolutionVideoResolution(dev->firmware, config);

    // 视频存储配置
    if (0 != WriteVideoRecordConfig(config, dev))
    {
        config << CONFIG_VIDEO_RECORD_ENABLE << "0" << "\n";
    }
    WriteDetectionConfig(config, dev);
    
    
    config << dev->config;

    std::string config_path = config_root_path_ + dev->mac + ".cfg";
    DevFileInfoPtr ptr = std::make_shared<DevFileInfo>(dev->mac, config_path, config.str(), SHADOW_TYPE::SHADOW_CONFIG, project::PERSONAL, dev->id);
    GetWriteFileControlInstance()->AddFileInfo(dev->mac, ptr);
    return 0;
}



void PerDevConfig::WriteDetectionConfig(std::stringstream &config, DEVICE_SETTING* dev)
{
    FirmwareList package_detection_list;
    dbinterface::VersionModel::GetPackageDetectionList(package_detection_list);
    if (package_detection_list.count(dev->firmware))
    {
        config <<  "Config.DoorSetting.PACKAGE_DETECT.Enable=" << dev->enable_package_detection << "\n";
    }
    // dbinterface::VersionModel::GetSoundDetectionList(sound_detection_list);
    // if (sound_detection_list.count(dev->firmware)) {
    //     config <<  "Config.DoorSetting.SOUND_DETECT.Enabled=" << dev->enable_sound_detection << "\n";
    //     config <<  "Config.DoorSetting.SOUND_DETECT.Gun=" << SwitchHandle(dev->sound_type, static_cast<int>(SoundType::Gun)) << "\n";
    //     config <<  "Config.DoorSetting.SOUND_DETECT.Dog=" << SwitchHandle(dev->sound_type, static_cast<int>(SoundType::Dog)) << "\n";
    //     config <<  "Config.DoorSetting.SOUND_DETECT.Baby=" << SwitchHandle(dev->sound_type, static_cast<int>(SoundType::Baby)) << "\n";
    //     config <<  "Config.DoorSetting.SOUND_DETECT.Glass=" << SwitchHandle(dev->sound_type, static_cast<int>(SoundType::Glass)) << "\n";
    //     config <<  "Config.DoorSetting.SOUND_DETECT.Siren=" << SwitchHandle(dev->sound_type, static_cast<int>(SoundType::Siren)) << "\n";
    // }
}

void PerDevConfig::WriteIndoorRelayDelayConfig(std::stringstream& config, const std::string& relay_json)
{
    // 从relay JSON中获取第一个和第二个relay的hold_delay值
    std::vector<RELAY_INFO> relays;
    ParseRelay(relay_json, relays);
    
    // 处理第一个relay的hold_delay
    if (relays.size() > 0)
    {
        int device_hold_delay;
        if (ConvertLocalRelayHoldDelayForDevice(relays[0].hold_delay, device_hold_delay))
        {
             //安卓室内机的Autop
            config << "Config.DoorSetting.RELAY.Delay1=" << device_hold_delay << "\n";
            // 嵌入式室内机
            config << "Config.Settings.RELAY.Interval=" << device_hold_delay << "\n";

        }
    }
    
    // 处理第二个relay的hold_delay
    if (relays.size() > 1)
    {
        int device_hold_delay;
        if (ConvertLocalRelayHoldDelayForDevice(relays[1].hold_delay, device_hold_delay))
        {
             //安卓室内机的Autop
            config << "Config.DoorSetting.RELAY.Delay2=" << device_hold_delay << "\n";
        }
    }
}
