#ifndef __PER_DEV_CONFIG_H__
#define __PER_DEV_CONFIG_H__
#include <string>
#include "dbinterface/CommunityInfo.h"
#include "AKCSMsg.h"
#include "dbinterface/PersonalThirdPartyCamera.h"
#include "dbinterface/ThirdPartyCamera.h"
#include "UpdateConfigContext.h"
#include "dbinterface/resident/ExtraDevice.h"

//单住户设备Config类
class PerDevConfig
{
public:
    PerDevConfig(  const std::string& config_root_path, int mng_sip_type, int rtp_confuse, int mng_rtsp_type)
    {
        config_root_path_ = config_root_path;
        mng_sip_type_ = mng_sip_type;
        rtp_confuse_ = rtp_confuse;
        mng_rtsp_type_ = mng_rtsp_type;
    }

    PerDevConfig() {};
    
    ~PerDevConfig() {}

    int WriteDevListFiles(DEVICE_SETTING* dev_list);
private:
    int WriteFiles(DEVICE_SETTING* dev);
    void WriteRelayConfig(std::stringstream &config, DEVICE_SETTING* dev);
    void WriteTimeZoneConfig(std::stringstream &config, DEVICE_SETTING* dev);
    void WriteDoorConfig(std::stringstream &config, DEVICE_SETTING* dev);
    void WriteThirdPartyCameraConfig(std::stringstream &config, DEVICE_SETTING* dev);
    void WriteCameraConfig(std::stringstream &config, const ThirdPartyCamreaInfo& camera);
    void WriteExternRelayConfig(std::stringstream &config, DEVICE_SETTING* dev);
    void WriteExRelayListConfig(std::stringstream &config, const IndoorMonitorConfigInfo& indoor_monitor_config);
    void InitExtraDeviceInfos(const ExtraDeviceInfo& device, const IndoorMonitorConfigInfo& indoor_monitor_config, 
                                  ExtraDeviceRelayList& extra_device_infos);
    void WriteConfigByRelayType(std::stringstream &config, const IndoorMonitorConfigInfo& indoor_monitor_config, 
                               const ExtraDeviceRelayList& extra_device_infos);


    void WriteR8RelayConfigForDevice(std::stringstream &config, ExtraDeviceRelay& extra_device_info);
    void WriteR8RelayConfigByDeviceConfigs(std::stringstream &config, const ExtraDeviceRelayList& extra_device_infos);
    void WriteLegacyRelayConfigByDeviceConfigs(std::stringstream &config, const ExtraDeviceRelayList& extra_device_infos);
    void WriteRelayActionConfigWithFunction(std::stringstream &config, const ExtraDeviceRelayActionInfo& action, 
                                           const ExtraDeviceRelayListInfo& relay, 
                                           ExtraDeviceRelay& extra_device_info, int function_index);
    void WriteDigitalInputConfig(std::stringstream &config, const ExtraDeviceRelayActionInfo& action, 
                                const ExtraDeviceRelayListInfo& relay, int device_index);
    int WriteVideoRecordConfig(std::stringstream &config, DEVICE_SETTING* dev);
    void WriteDetectionConfig(std::stringstream &config, DEVICE_SETTING* dev);
    void WriteIndoorRelayDelayConfig(std::stringstream& config, const std::string& relay_json);
    int mng_rtsp_type_; // 0:tcp,1:tls
    int mng_sip_type_;
    int rtp_confuse_;

    std::string config_root_path_;
};


#endif 

