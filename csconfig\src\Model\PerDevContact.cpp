#include <sstream>
#include "PerDevContact.h"
#include <string.h>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "ConfigDef.h" 
#include "AdaptUtility.h"
#include "json/json.h"
#include "util.h"
#include "AkLogging.h"
#include "DeviceSetting.h"
#include "AkcsWebMsgSt.h"
#include "CommunityMng.h"
#include "DeviceControl.h"
#include "AES256.h"
#include "AkcsMonitor.h"
#include "encrypt/Md5.h"
#include "PersonalAccount.h"
#include "DeviceSetting.h"
#include "PersonnalDeviceSetting.h"
#include "AkcsCommonDef.h"
#include "ShadowMng.h"
#include "ContactCommon.h"
#include "dbinterface/PersonalAccountCnf.h"
#include "dbinterface/resident/ResidentDevices.h" 
#include "dbinterface/ContactBlock.h"
#include "dbinterface/ContactFavorite.h"
#include "dbinterface/Account.h"
#include "dbinterface/PmAccountMap.h"
#include "CommConfigHandle.h"
#include "FileUpdateControl.h"
#include "AKCSView.h"
#include "WriteFileControl.h"
#include "dbinterface/PerNodeDevices.h"
#include "util_judge.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "PerDevPushButton.h"
#include "dbinterface/VideoStorage.h"
#include "dbinterface/VideoStorageDevice.h"

extern CSCONFIG_CONF gstCSCONFIGConf;


int PerDevContact::UpdatePerContactFile(DEVICE_SETTING* your_dev, DEVICE_SETTING* your_list, std::vector<DEVICE_CONTACTLIST>& app_list,
                                  const DEVICE_SETTING* pub_device_list)
{
    
    UpdateContactFile(your_dev, your_list, app_list, pub_device_list, nullptr);
}

//更新三方摄像头联系人文件
void PerDevContact::UpdateThirdCameraContactFile(const DEVICE_CONTACTLIST& app, DEVICE_SETTING* your_dev, std::stringstream& config_body,
                              const DEVICE_SETTING* pub_device_list/*最外层*/, 
                              const DEVICE_SETTING* unit_pub_device_list/*单元*/,
                              const DEVICE_SETTING* your_list)
{
    const DEVICE_SETTING* cur_device_setting = nullptr;
    //单住户室内机和管理机
    if (app.role == ACCOUNT_ROLE_PERSONNAL_MAIN && 
        ((your_dev->type == DEVICE_TYPE_INDOOR && your_dev->dclient_version >= D_CLIENT_VERSION_6533) 
        || (your_dev->type == DEVICE_TYPE_MANAGEMENT && your_dev->dclient_version >= D_CLIENT_VERSION_6542)))
    {
        ThirdPartyCamreaList node_camera_list;
        if (0 == dbinterface::PersonalThirdPartyCamrea::GetPersonalThirdPartyCameraList(app.uuid, node_camera_list))
        {
            WriteCameraContactFile(node_camera_list, your_list, config_body, app.enable_ip_direct);
        }
    }
}

int PerDevContact::UpdateContactFile(DEVICE_SETTING* your_dev, DEVICE_SETTING* your_list, std::vector<DEVICE_CONTACTLIST>& app_list,
                                      const DEVICE_SETTING* pub_device_list/*最外层*/, 
                                      const DEVICE_SETTING* unit_pub_device_list/*单元*/)
{
    if (!your_dev)
    {
        AK_LOG_WARN << "UpdateContactFile failed .";
        return -1;
    }
    
    //公共设备不需要更新联系人
    if (your_dev->flag & DEVICE_SETTING_FLAG_PER_PUBLIC)
    {
        //AK_LOG_WARN << "UpdateContactFile failed because dev is public dev.";
        return 0;
    }
   
    CreateGroupCallSeq(app_list[0].call_type, app_list, your_list);

    int num = 0;
    const DEVICE_SETTING* cur_device_setting = your_list;
    std::stringstream group_file;
    std::stringstream contact_file;
    std::stringstream config_body;
    
    config_body << "\n<?xml version=\"1.0\" encoding=\"UTF-8\" ?>\n";
    config_body << "<ContactData>\n";

    int have_master = 0;
    int is_master = 1;
    int enable_ip_direct = 0;
    int phone_status = 0; //是否有落地
    int node_register = 0;
    std::string phone_head;
    for (auto& app : app_list)
    {
        std::string phone, phone2, phone3, phone_all, phone_last/*phone2 phone3*/;
        GetPhoneInfo(app, phone_head, phone, phone2, phone3, phone_all, phone_last);

        //开启的calltype选项只有呼叫phone，没有呼叫app，如果从账号没有配置phone，要呼叫app
        if (!is_master)
        {
            ChangeSlaveCallInfo(app, phone);
        }

        std::string call_seq = app.seq;
        std::string call_seq2 = app.seq2;
        if (your_dev->type == DEVICE_TYPE_INDOOR || your_dev->type == DEVICE_TYPE_MANAGEMENT)
        {
            call_seq = "";
            call_seq2 = "";
        }

        if (is_master)
        {
            is_master = 0;
            have_master = 1;
            enable_ip_direct = app.enable_ip_direct;

            GetMasterGroupInfo(group_file, app);
            //三方摄像头
            UpdateThirdCameraContactFile(app, your_dev, contact_file, pub_device_list, unit_pub_device_list, your_list);

            //v6.5版本以上的设备,空房间主账号和未注册账号不写入联系人列表,AKUVOX设备需要Dclient版本6500以上支持,其他品牌不需要判断Dclient版本
            if (CheckDeviceDclientVer6500(your_dev) && (CheckIsEmptyRoom(app) || !AccountIsRegister(app)))
            {
                continue;
            } 
            node_register = 1;
            
            //是否启用落地
            std::string sip;
            phone_status = app.phone_status;
            if (phone_status && your_dev->type != DEVICE_TYPE_INDOOR && your_dev->type != DEVICE_TYPE_MANAGEMENT) //v5.0室内机不配置落地信息
            {
                ContactKvList kv;
                kv.push_back(std::make_pair(CONTACT_ATTR::NAME, app.name));
                kv.push_back(std::make_pair(CONTACT_ATTR::SIP, phone));
                kv.push_back(std::make_pair(CONTACT_ATTR::UID, app.sip_account));
                kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(DEVICE_TYPE_APP)));
                kv.push_back(std::make_pair(CONTACT_ATTR::GROUP_CALL, std::string("1")));
                kv.push_back(std::make_pair(CONTACT_ATTR::MASTER, std::string("1")));
                kv.push_back(std::make_pair(CONTACT_ATTR::MATCH_DTMF1, GetSubstrFromBehind(phone, PHONE_SUBSTR_DETECT_NUMBER)));
                kv.push_back(std::make_pair(CONTACT_ATTR::MATCH_DTMF2, app.sip_account));
                kv.push_back(std::make_pair(CONTACT_ATTR::MATCH_DTMF3, GetSubstrFromBehind(phone2, PHONE_SUBSTR_DETECT_NUMBER)));
                kv.push_back(std::make_pair(CONTACT_ATTR::MATCH_DTMF4, GetSubstrFromBehind(phone3, PHONE_SUBSTR_DETECT_NUMBER)));
                kv.push_back(std::make_pair(CONTACT_ATTR::SEQ, call_seq));                
                //区分于个人终端       目前个人 没有这个顺序。先呼叫app 在呼叫落地
                //兼容：MatchDtmf1 MatchDtmf2一定是app和第一个落地号码，旧版本不识别MatchDtmf3 MatchDtmf4
                if (app.call_type == NODE_CALL_TYPE_INDOOR_PHONE
                        || app.call_type == NODE_CALL_TYPE_INDOOR_BACK_PHONE
                        || app.call_type == NODE_CALL_TYPE_INDOOR_BACK_APP_BACK_PHONE)
                {
                    kv.push_back(std::make_pair(CONTACT_ATTR::SIP0, app.sip_account));
                    kv.push_back(std::make_pair(CONTACT_ATTR::LAND, phone_last));
                }
                else if (app.call_type == NODE_CALL_TYPE_APP_INDOOR_BACK_PHONE)
                {
                    kv.push_back(std::make_pair(CONTACT_ATTR::SIP0, app.sip_account));
                    kv.push_back(std::make_pair(CONTACT_ATTR::LAND, phone_all));
                }
                else
                {             
                    kv.push_back(std::make_pair(CONTACT_ATTR::APP, app.sip_account));
                    kv.push_back(std::make_pair(CONTACT_ATTR::LAND, phone_all));
                }
                GetContactStr(contact_file, kv);
            }
            else
            {
                //没有落地，就不要赋值land* 和app
                ContactKvList kv;
                kv.push_back(std::make_pair(CONTACT_ATTR::NAME, app.name));
                kv.push_back(std::make_pair(CONTACT_ATTR::UID, app.sip_account));                    
                kv.push_back(std::make_pair(CONTACT_ATTR::SIP, app.sip_account));
                kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(DEVICE_TYPE_APP)));
                kv.push_back(std::make_pair(CONTACT_ATTR::MATCH_DTMF1, GetSubstrFromBehind(phone, PHONE_SUBSTR_DETECT_NUMBER)));
                kv.push_back(std::make_pair(CONTACT_ATTR::SEQ, call_seq));
                GetContactStr(contact_file, kv);                             
            }
        }
        else
        {
            //家居适配
            if (!strcmp(app.name, "$$Delete$$ $$Delete$$"))
            {
                continue;
            }
            
            //主账号有注册,从账号不需要判断注册. 主账号未注册时,判断从账号有无注册
            //防止从账号未注册,但通过主账号邮箱收到的二维码登录app,产生呼叫相关的bug
            if (!node_register && !AccountIsRegister(app) && your_dev->dclient_version >= D_CLIENT_VERSION_6500)
            {
                continue;
            }
            
            //从账号
            ContactKvList kv; 
            kv.push_back(std::make_pair(CONTACT_ATTR::NAME, app.name));
            kv.push_back(std::make_pair(CONTACT_ATTR::UID, app.sip_account));                    
            kv.push_back(std::make_pair(CONTACT_ATTR::SIP, app.sip_account));
            kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(DEVICE_TYPE_APP)));                  
            kv.push_back(std::make_pair(CONTACT_ATTR::MATCH_DTMF1, GetSubstrFromBehind(phone, PHONE_SUBSTR_DETECT_NUMBER)));              
            kv.push_back(std::make_pair(CONTACT_ATTR::SEQ, call_seq));
            kv.push_back(std::make_pair(CONTACT_ATTR::SEQ2, call_seq2));            
            if (phone_status && your_dev->type != DEVICE_TYPE_INDOOR && your_dev->type != DEVICE_TYPE_MANAGEMENT) //v5.0室内机不配置落地信息
            {
                kv.push_back(std::make_pair(CONTACT_ATTR::LAND, phone));        
            }
            GetContactStr(contact_file, kv);
        }
    }
    
    if (!have_master)
    {
        group_file << "<Group  Name=\"\" Room=\"\" SIP=\"\" RoomN=\"\" IpDirect=\"\" />";
    }
 
    /*
    不启用IP直播，则联系都是SIP
    启用ip直播，则根据网络号，如果网络号一致放IP，反之放SIP
    */
    cur_device_setting = your_list;
    while (cur_device_setting != NULL)
    {
        if (cur_device_setting != your_dev &&
           ((your_dev->type != DEVICE_TYPE_INDOOR && CheckIsAptInDoorTypeDevice(cur_device_setting->type))  //门口机梯口机不写  只写室内机/管理中心
            || CheckIsAptInDoorTypeDevice(your_dev->type))  //室内机/管理中心机写全部联系人
            && !DeviceNotViewInContact(cur_device_setting->firmware, cur_device_setting->oem_id) //过滤不需要展示的设备
           )
        {
            ContactKvList kv;
            kv.push_back(std::make_pair(CONTACT_ATTR::NAME, cur_device_setting->location));
            kv.push_back(std::make_pair(CONTACT_ATTR::UID, cur_device_setting->sip_account));
            kv.push_back(std::make_pair(CONTACT_ATTR::MAC, cur_device_setting->mac));              
            kv.push_back(std::make_pair(CONTACT_ATTR::RTSPPWD, cur_device_setting->rtsp_password));
            kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(cur_device_setting->type)));
            kv.push_back(std::make_pair(CONTACT_ATTR::SEQ, cur_device_setting->seq));
            if (akjudge::DevDoorType(cur_device_setting->type))
            {
                int not_monitor = GetNoMonitorContactFile(cur_device_setting);
                
                not_monitor = not_monitor || !(cur_device_setting->allow_end_user_monitor);
                kv.push_back(std::make_pair(CONTACT_ATTR::NOT_MONITOR, std::to_string(not_monitor)));
                if (!not_monitor)
                {
                    kv.push_back(std::make_pair(CONTACT_ATTR::CAMERA_NUM, std::to_string(cur_device_setting->camera_num)));
                }
            }
            
            WriteRelayContact(cur_device_setting->type, cur_device_setting->relay, cur_device_setting->security_relay, kv);
            
            if (enable_ip_direct || your_dev->is_expire) //如果过期了默认回到ip直播
            {
                if (your_dev->netgroup_num == cur_device_setting->netgroup_num)
                {                  
                    kv.push_back(std::make_pair(CONTACT_ATTR::SIP, std::string("")));
                    kv.push_back(std::make_pair(CONTACT_ATTR::IP, GetCurDevIPAddress(your_dev, cur_device_setting)));     
                    kv.push_back(std::make_pair(CONTACT_ATTR::OPTIONIP, GetOptionIP(your_dev, cur_device_setting)));           
                }
                else
                {               
                    kv.push_back(std::make_pair(CONTACT_ATTR::SIP, cur_device_setting->sip_account));
                    kv.push_back(std::make_pair(CONTACT_ATTR::IP, std::string("")));    
                    kv.push_back(std::make_pair(CONTACT_ATTR::GROUP_CALL, std::string("1")));           
                }
            }
            else
            {                   
                kv.push_back(std::make_pair(CONTACT_ATTR::SIP, cur_device_setting->sip_account));
                kv.push_back(std::make_pair(CONTACT_ATTR::IP, std::string("")));    
            }
            // 室内机代理门口机转流
            GetRepostContact(cur_device_setting, your_dev, kv);

            // 视频存储
            GetVideoRecordContact(cur_device_setting, your_dev, kv);

            GetContactStr(contact_file, kv);
        }
        cur_device_setting = cur_device_setting->next;
    }

    //contact为空时,不写入group
    if(contact_file.str().length())
    {
        config_body << group_file.str() << contact_file.str() << "</Group>\n";
    } 

    config_body << "</ContactData>\n";
    //给设备写pushbutton 的配置
    bool is_support_extern_push_button = dbinterface::SwitchHandle(your_dev->fun_bit, FUNC_DEV_SUPPORT_EXTERN_PUSH_BUTTON);
    if (is_support_extern_push_button)
    {
        PerDevPushButton push_button;
        push_button.UpdatePerDevPushButtonFile(your_dev->mac, config_body);
    }
    

    //写入文件
    std::string config_path = config_root_path_ + your_dev->mac + ".xml";
    DevFileInfoPtr ptr = std::make_shared<DevFileInfo>(your_dev->mac, config_path, config_body.str(), SHADOW_TYPE::SHADOW_CONTACT,
                                                        project::PERSONAL, your_dev->id);
    GetWriteFileControlInstance()->AddFileInfo(your_dev->mac, ptr);

    return 0;
}

//写单住户公共设备联系人--历史遗留的一个单住户里面包含公共设备的功能
int PerDevContact::UpdatePerPublicContactFile(DEVICE_SETTING* dev)
{
    if (!dev)
    {
        return -1;
    }
    int public_dev_id = dev->id;
    int num = 0;
    std::stringstream config_body;
    std::stringstream file_content;
    config_body << "\n<?xml version=\"1.0\" encoding=\"UTF-8\" ?>\n";
    config_body << "<ContactData>\n";
    int is_master = 1;

    std::vector<PER_NODE_DEVICES> pernodes;
    dbinterface::PerNodeDevices::GetNodesByPublicDevID(public_dev_id, pernodes);

    for (auto& node : pernodes)
    {
        std::string user = node.node;
        std::vector<DEVICE_CONTACTLIST> app_list;
        GetPersonalAccountInstance()->DaoGetApplistByNode(user, app_list);
        if (app_list.size() == 0)
        {
            AK_LOG_WARN << "get app_list failed,user is: " << user;
            continue;
        }

        DEVICE_SETTING* device_setting_list = GetPersonnalDevSettingInstance()->GetNodeDeviceSettingList(user);

        int is_master = 1;
        int ip_direct = 0;
        int phone_status = 0;
        std::string phone_head;
        for (auto& app : app_list)
        {
            std::string phone, phone2, phone3, phone_all, phone_last;
            GetPhoneInfo(app, phone_head, phone, phone2, phone3, phone_all, phone_last);

            num++;
            if (is_master)
            {
                ip_direct = app.enable_ip_direct;
                GetMasterGroupInfo(config_body, app);

                //网页关闭高级功能时,落地也随之关闭,只需要通过phone_status就能判断落地是否开启
                phone_status = app.phone_status;

                ContactKvList kv;
                kv.push_back(std::make_pair(CONTACT_ATTR::NAME, app.name));
                kv.push_back(std::make_pair(CONTACT_ATTR::UID, app.sip_account)); 
                kv.push_back(std::make_pair(CONTACT_ATTR::MASTER, std::string("1")));                    
                kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(DEVICE_TYPE_APP)));
                kv.push_back(std::make_pair(CONTACT_ATTR::MATCH_DTMF1, GetSubstrFromBehind(phone, PHONE_SUBSTR_DETECT_NUMBER)));

                if (phone_status)
                {
                    //落地之后直接把号码赋值到IP,设备呼叫就会带上这个号码呼叫
                    kv.push_back(std::make_pair(CONTACT_ATTR::SIP, phone));                    
                    kv.push_back(std::make_pair(CONTACT_ATTR::APP, app.sip_account));
                    kv.push_back(std::make_pair(CONTACT_ATTR::LAND, phone_all));
                    kv.push_back(std::make_pair(CONTACT_ATTR::GROUP_CALL, std::string("1")));
                    kv.push_back(std::make_pair(CONTACT_ATTR::MATCH_DTMF2, app.sip_account));
                    kv.push_back(std::make_pair(CONTACT_ATTR::MATCH_DTMF3, GetSubstrFromBehind(phone2, PHONE_SUBSTR_DETECT_NUMBER)));
                    kv.push_back(std::make_pair(CONTACT_ATTR::MATCH_DTMF4, GetSubstrFromBehind(phone3, PHONE_SUBSTR_DETECT_NUMBER)));
                }
                else
                {                    
                    kv.push_back(std::make_pair(CONTACT_ATTR::SIP, app.sip_account));                                                   
                }
                GetContactStr(config_body, kv);
                is_master = 0;
            }
            else
            {
                //家居适配
                if (!strcmp(app.name, "$$Delete$$ $$Delete$$"))
                {
                    continue;
                }            
                //从账号
                ContactKvList kv;
                kv.push_back(std::make_pair(CONTACT_ATTR::NAME, app.name));
                kv.push_back(std::make_pair(CONTACT_ATTR::UID, app.sip_account));                    
                kv.push_back(std::make_pair(CONTACT_ATTR::SIP, app.sip_account));                    
                kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(DEVICE_TYPE_APP)));                                        
                kv.push_back(std::make_pair(CONTACT_ATTR::MATCH_DTMF1, GetSubstrFromBehind(phone, PHONE_SUBSTR_DETECT_NUMBER)));                 
                if (phone_status)
                {                   
                    kv.push_back(std::make_pair(CONTACT_ATTR::LAND, phone));                                       
                }
                GetContactStr(config_body, kv);
            }
        }
        DEVICE_SETTING* cur_device_setting = device_setting_list;
        while (cur_device_setting != NULL)
        {
            if (cur_device_setting->type == DEVICE_TYPE_INDOOR || dev->type == DEVICE_TYPE_MANAGEMENT)//管理中心机要有门口机
            {
                ContactKvList kv;
                kv.push_back(std::make_pair(CONTACT_ATTR::NAME, cur_device_setting->location));
                kv.push_back(std::make_pair(CONTACT_ATTR::UID, cur_device_setting->sip_account));                 
                kv.push_back(std::make_pair(CONTACT_ATTR::MAC, cur_device_setting->mac)); 
                kv.push_back(std::make_pair(CONTACT_ATTR::RTSPPWD, cur_device_setting->rtsp_password)); 
                kv.push_back(std::make_pair(CONTACT_ATTR::TYPE, std::to_string(cur_device_setting->type)));                 
                if (ip_direct && cur_device_setting->netgroup_num == dev->netgroup_num)
                {
                    kv.push_back(std::make_pair(CONTACT_ATTR::SIP, std::string("")));  
                    kv.push_back(std::make_pair(CONTACT_ATTR::IP, GetCurDevIPAddress(dev, cur_device_setting))); 
                    kv.push_back(std::make_pair(CONTACT_ATTR::OPTIONIP, GetOptionIP(dev, cur_device_setting)));
                }
                else if (ip_direct)
                {
                    //对于设备GroupCall只有在ip直播不同网络组才能配置                 
                    kv.push_back(std::make_pair(CONTACT_ATTR::SIP, cur_device_setting->sip_account));  
                    kv.push_back(std::make_pair(CONTACT_ATTR::IP, std::string(""))); 
                    kv.push_back(std::make_pair(CONTACT_ATTR::GROUP_CALL, std::string("1")));                   
                }
                else
                {                 
                    kv.push_back(std::make_pair(CONTACT_ATTR::SIP, cur_device_setting->sip_account));  
                    kv.push_back(std::make_pair(CONTACT_ATTR::IP, std::string(""))); 
                }
                GetContactStr(config_body, kv);
            }
            cur_device_setting = cur_device_setting->next;
        }
        config_body << "</Group>\n";
        if (device_setting_list != nullptr)
        {
            GetDeviceControlInstance()->DestoryDeviceSettingList(device_setting_list);
        }

    }
    
    config_body << "</ContactData>\n";

    //写入文件
    std::string config_path = config_root_path_ + dev->mac + ".xml";
    DevFileInfoPtr ptr = std::make_shared<DevFileInfo>(dev->mac, config_path, config_body.str(), SHADOW_TYPE::SHADOW_CONTACT,
                                                        project::PERSONAL, dev->id);
    GetWriteFileControlInstance()->AddFileInfo(dev->mac, ptr);

    return 0;
}

//写三方摄像头联系人文件
void PerDevContact::WriteCameraContactFile(ThirdPartyCamreaList camera_list, const DEVICE_SETTING* device_list, std::stringstream& config_body, int enable_ip_direct)
{
    const DEVICE_SETTING* cur_device_setting = nullptr;
    for (const auto& camera : camera_list)
    {
        int not_monitor = 0;
        //设备是否允许终端用户监控 0=不允许 1=允许
        if(!camera.allow_end_user_monitor)
        {
            not_monitor = 1;
        }
        //监控平台，1=SmartPlus + Indoor Monitor+apt下的management，2=Only SmartPlus，3=Only Indoor Monitor+apt下的management'
        if(camera.monitoring_platform == MONITOR_TYPE_ONLY_SMARTPLUS)
        {
            not_monitor = 1;
        }
        //有link ak设备,则要判断link的设备mac是否在device_list
        if (strlen(camera.mac) && device_list)
        {
            cur_device_setting = device_list;
            while (cur_device_setting != nullptr)
            {
                if (0 == strcmp(cur_device_setting->mac, camera.mac))
                {
                    if(enable_ip_direct)
                    {
                        InsertCameraList(camera, DEVICE_MONITOR_TYPE_THIRD_CAMERA, config_body, not_monitor);
                    }
                    else
                    {
                        InsertCameraList(camera, DEVICE_MONITOR_TYPE_CLOUD, config_body, not_monitor);
                    }
                }
                cur_device_setting = cur_device_setting->next;
            }
        }
        else
        {
            //mac为空代表未绑定门口机
            InsertCameraList(camera, DEVICE_MONITOR_TYPE_THIRD_CAMERA, config_body, not_monitor);
        }
    }
}

// 下发门口机是否支持视频存储给室内机
void PerDevContact::GetVideoRecordContact(const DEVICE_SETTING* cur_dev, const DEVICE_SETTING* your_dev, ContactKvList &kv)
{
	VideoStorageInfo video_storage_info;
    VideoStorageDeviceInfo video_storage_device_info;
    if (your_dev->type == DEVICE_TYPE_INDOOR                                   // 室内机
        && akjudge::DevDoorType(cur_dev->type)                                 // 写门口机联系人
        && SwitchHandle(your_dev->fun_bit, FUNC_DEV_SUPPORT_VIDEO_RECORD)      // 室内机支持视频存储 
        && SwitchHandle(cur_dev->fun_bit, FUNC_DEV_SUPPORT_VIDEO_RECORD)       // 门口机支持视频存储
        && 0 == dbinterface::VideoStorage::GetVideoStorageByPersonalAccountUUID(cur_dev->node_uuid, video_storage_info) // 家庭视频存储开关开启     
        && DatabaseExistenceStatus::EXIST == dbinterface::VideoStorageDevice::GetVideoStorageDeviceInfo(cur_dev->uuid, video_storage_device_info))   // 门口机被选为视频存储设备                                               
    {
        kv.push_back(std::make_pair(CONTACT_ATTR::RECORD_VIDEO, std::string("1")));
    }
    else
    {
        kv.push_back(std::make_pair(CONTACT_ATTR::RECORD_VIDEO, std::string("0")));
    }
    return;
}