#ifndef __PER_DEV_CONTACT_H__
#define __PER_DEV_CONTACT_H__
#include <string>
#include "AKCSMsg.h"
#include <set>
#include <DevContact.h>
#include "dbinterface/ThirdPartyCamera.h"


//单住户设备联系人类
class PerDevContact : public DevContact
{
public:
	PerDevContact(const std::string& config_root_path  )
    {
        config_root_path_ = config_root_path;
    }

    PerDevContact()
    {
    }

	virtual ~PerDevContact()
	{
	}
	
    int UpdatePerContactFile(DEVICE_SETTING* your_dev, DEVICE_SETTING* your_list, std::vector<DEVICE_CONTACTLIST>& app_list,
                                      const DEVICE_SETTING* pub_device_list);

    int UpdatePerPublicContactFile(DEVICE_SETTING* pub_device_setting); 

    void UpdateThirdCameraContactFile(const DEVICE_CONTACTLIST& app, DEVICE_SETTING* your_dev, std::stringstream& config_body,
                              const DEVICE_SETTING* pub_device_list/*最外层*/, 
                              const DEVICE_SETTING* unit_pub_device_list/*单元*/,
                              const DEVICE_SETTING* your_list) override;

    int UpdateContactFile(DEVICE_SETTING* your_dev, DEVICE_SETTING* your_list, std::vector<DEVICE_CONTACTLIST>& app_list,
                                  const DEVICE_SETTING* pub_device_list/*最外层*/, 
                                  const DEVICE_SETTING* unit_pub_device_list/*单元*/) override;
    void WriteCameraContactFile(ThirdPartyCamreaList camera_list, const DEVICE_SETTING* device_list, std::stringstream& config_body, int enable_ip_direct);

    void GetVideoRecordContact(const DEVICE_SETTING* cur_dev, const DEVICE_SETTING* your_dev, ContactKvList &kv);

private:
    std::string config_root_path_;

};


#endif 
