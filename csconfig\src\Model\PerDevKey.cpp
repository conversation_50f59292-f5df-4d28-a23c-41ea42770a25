#include <sstream>
#include <string.h>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "ConfigDef.h" 
#include "AdaptUtility.h"
#include "json/json.h"
#include "util.h"
#include "AkLogging.h"
#include "DeviceSetting.h"
#include "AkcsWebMsgSt.h"
#include "CommunityMng.h"
#include "DeviceControl.h"
#include "AES256.h"
#include "AkcsMonitor.h"
#include "encrypt/Md5.h"
#include "CharChans.h"
#include "PrivateKeyControl.h"
#include "DevUser.h"
#include "AkcsCommonDef.h"
#include "ShadowMng.h"
#include "PrivateKeyXml.h"
#include "WriteFileControl.h"
#include "PerDevKey.h"

extern CSCONFIG_CONF gstCSCONFIGConf;


int PerDevKey::UpdateRfKeyFiles(DEVICE_SETTING* device_setting_list, RF_KEY* privatekey_list)
{
    is_private_key_ = 0;
    UpdateKeyFiles(WEB_PER_UPDATE_RF, device_setting_list, (PRIVATE_KEY*)privatekey_list);
}

int PerDevKey::UpdatePrivateKeyFiles(DEVICE_SETTING* device_setting_list, PRIVATE_KEY* privatekey_list)
{
    is_private_key_ = 1;
    UpdateKeyFiles(WEB_PER_UPDATE_PIN, device_setting_list, privatekey_list);
} 

//更新个人终端用户的key配置表
int PerDevKey::UpdateKeyFiles(int type, DEVICE_SETTING* device_setting_list, PRIVATE_KEY* privatekey_list)
{
    if (device_setting_list == NULL)
    {
        AK_LOG_WARN << "UpdateKeyFiles device is NULL.";
        return -1;
    }

    DEVICE_SETTING* cur_device_setting = device_setting_list;
    //added by chenyc,先遍历每一个设备,然后在内循环中,比较该设备是否与key对应的所有管辖设备匹配
    while (cur_device_setting != NULL)
    {
        //只有DOOR/STAIR/WALL才需要生成PRIVATEKEY和RFKEY
        if ((cur_device_setting->type != DEVICE_TYPE_WALL) && (cur_device_setting->type != DEVICE_TYPE_DOOR) \
            && (cur_device_setting->type != DEVICE_TYPE_STAIR) && (cur_device_setting->type != DEVICE_TYPE_ACCESS))
        {
            cur_device_setting = cur_device_setting->next;
            continue;
        }

        PRIVATE_KEY* new_privatekey_list = NULL;
        PRIVATE_KEY* new_cur_privatekey = NULL;
        //遍历PRIVATEKEY，为该设备生成一个新的PRIVATEKEY列表
        PRIVATE_KEY* cur_privatekey = privatekey_list;
        while (cur_privatekey != NULL)
        {
            if (strlen(cur_privatekey->code) == 0)
            {
                cur_privatekey = cur_privatekey->next;
                continue;
            }

            // 单住户设置PIN支持设置Relay：如果选择全部door时不会插入PersonalPrivateKeyList表，做链接的记录access_mac为空
            if(strlen(cur_privatekey->access_mac) > 0 && strcmp(cur_privatekey->access_mac, cur_device_setting->mac) != 0)
            {
                cur_privatekey = cur_privatekey->next;
                continue;
            }

            PRIVATE_KEY *new_node = new PRIVATE_KEY;
            memcpy(new_node, cur_privatekey, sizeof(PRIVATE_KEY));
            new_node->next = NULL;
            new_node->access_device_list = NULL;
            
            if (new_privatekey_list == NULL)
            {
                new_privatekey_list = new_node;
            }
            else
            {
                new_cur_privatekey->next = new_node;
            }

            new_cur_privatekey = new_node;
            cur_privatekey = cur_privatekey->next;
        }

        PRIVATE_KEY* final_privatekey_list = NULL;
        final_privatekey_list = new_privatekey_list;
        new_privatekey_list = NULL;

        //获取文件名
        char file_path[MAX_FILE_PATH] = "";
        memset(file_path, 0, sizeof(file_path));
        CString path = _T("");
        if (is_private_key_)
        {
            path = config_root_path_ + cur_device_setting->mac + ".xml";
            AK_LOG_INFO << "The Privatekey file path is " << path;
        }
        else
        {
            path = config_root_path_ + cur_device_setting->mac + ".xml";
            AK_LOG_INFO << "The Rfid file path is " << path;
        }
        TransTcharToUtf8(path.GetBuffer(), file_path, sizeof(file_path));
        
        int default_relay = 0;  
        if(D_CLIENT_VERSION_5200 > cur_device_setting->dclient_version)
        {
            default_relay = 7;  //旧版本只支持doornum=123
        }
        else
        {
            GetValueByRelay(cur_device_setting->relay, default_relay);
        }
        int default_security_relay = 0;
        if (cur_device_setting->dclient_version >= D_CLIENT_VERSION_6400)
        {
            GetValueByRelay(cur_device_setting->security_relay, default_security_relay);
        }

        std::string file_content;
        if(type == WEB_PER_UPDATE_RF){
            personal_create_rf_key_xml(file_content, file_path, final_privatekey_list, default_relay, default_security_relay);
        }
        else{
            personal_create_private_key_xml(file_content, file_path, final_privatekey_list, default_relay, default_security_relay);
        }

        SHADOW_TYPE ftype = SHADOW_TYPE::SHADOW_NONE; 
        if (is_private_key_)
        {
            ftype = SHADOW_PRIKEY;
        }
        else
        {
            ftype = SHADOW_RFID;
        }

        DevFileInfoPtr ptr = std::make_shared<DevFileInfo>(cur_device_setting->mac, file_path, file_content, ftype,
                                                            project::PERSONAL, cur_device_setting->id);
        GetWriteFileControlInstance()->AddFileInfo(cur_device_setting->mac, ptr);

        //销毁新生成的KEY列表
        GetPrivateKeyControlInstance()->DestoryPrivateKeyList(new_privatekey_list);
        new_privatekey_list = NULL;

        
        GetPrivateKeyControlInstance()->DestoryPrivateKeyList(final_privatekey_list);
        final_privatekey_list = NULL;

        cur_device_setting = cur_device_setting->next;
    }

    return 0;
}
