#ifndef __PER_DEV_KEY_H__
#define __PER_DEV_KEY_H__
#include <string>
#include "dbinterface/CommunityInfo.h"
#include "AKCSMsg.h"


class PerDevKey
{
public:
	PerDevKey(  const std::string& config_root_path)
    {
        config_root_path_ = config_root_path;
    }

	~PerDevKey()
    {
        
    }
    int UpdateRfKeyFiles(DEVICE_SETTING* device_setting_list, RF_KEY* privatekey_list);
    int UpdatePrivateKeyFiles(DEVICE_SETTING* device_setting_list, PRIVATE_KEY* privatekey_list);
private:
    int UpdateKeyFiles(int type, DEVICE_SETTING* device_setting_list, PRIVATE_KEY* privatekey_list);
    int is_private_key_;
    std::string config_root_path_;
};

#endif 
