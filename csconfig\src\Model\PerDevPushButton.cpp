#include "PerDevPushButton.h"
#include <sstream>
#include "dbinterface/resident/ResidentDevices.h"
#include "dbinterface/ExternPushButton.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/resident/ResidentPersonalDevices.h"
#include "DeviceControl.h"
#include "PersonalAccount.h"
#include "Rldb.h"
#include "RldbQuery.h"
#include "util_judge.h"
#include "DeviceSetting.h"
#include "dbinterface/PubDevMngList.h"
#include "AKCSMsg.h"
#include "AkLogging.h"
#include "AkcsCommonSt.h"

typedef std::vector<DEVICE_CONTACTLIST> NodeAppList;


void PerDevPushButton::GenerateXMLForModule(const std::string& devicePushButtonUUID, int moduleID, std::stringstream& config_body, const std::string& mac) 
{
    std::vector<DevicePushButtonListInfo> module_sequence;
    dbinterface::DevicePushButtonList::GetButtonListByPushButtonUUIDandModuleID(devicePushButtonUUID, moduleID, module_sequence);

    if (module_sequence.empty()) 
    {
        return;
    }

    for (const auto& item : module_sequence) {
        ResidentPerAccount account;
        switch (item.type) {
            case WebCalleeType::WEB_NOT_SET: 
            {
                config_body << "<Key Index=\"" 
                            << item.sequence 
                            << "\" Type=\"" 
                            << DeviceCalleeType::DEV_NOT_SET 
                            << "\" Number=\"\"/>\n";
                break;
            }
            case WebCalleeType::WEB_SINGLE_CALL_PERSONAL: 
            {   
                if (dbinterface::ResidentPersonalAccount::GetUUIDAccount(item.callee_uuid, account) == 0) 
                {
                    config_body << "<Key Index=\"" 
                                << item.sequence 
                                << "\" Type=\"" 
                                << DeviceCalleeType::DEV_SINGLE_CALL 
                                << "\" Number=\"" 
                                << account.account 
                                << "\"/>\n";
                } 
                break;
            }
            case WebCalleeType::WEB_SINGLE_CALL_DEVICE: 
            {   
                ResidentDev dev;
                if (dbinterface::ResidentPerDevices::GetUUIDDev(item.callee_uuid, dev) == 0)
                {   
                    config_body << "<Key Index=\"" 
                                << item.sequence 
                                << "\" Type=\"" 
                                << DeviceCalleeType::DEV_SINGLE_CALL  
                                << "\" Number=\"" 
                                << dev.sip 
                                << "\"/>\n";
                }
                break;
            }            
            default: 
            {
                AK_LOG_WARN << "Unknown CalleeType: " << item.type;
                break;
            }
        }
    }
}


void PerDevPushButton::GenerateXMLForDevicePushButton(DevicePushButton& push_button, std::stringstream& config_body, const std::string& mac) 
{
    // 生成模块 0 的 XML
    if (push_button.module_0_buttonNum > 0) {
        config_body << "<Module ID=\"0\">\n";
        GenerateXMLForModule(push_button.uuid, 0, config_body, mac);
        config_body << "</Module>\n";
    }

    // 生成模块 1 的 XML
    if (push_button.module_1_buttonNum > 0) {
        config_body << "<Module ID=\"1\">\n";
        GenerateXMLForModule(push_button.uuid, 1, config_body, mac);
        config_body << "</Module>\n";
    }

    // 生成模块 2 的 XML
    if (push_button.module_2_buttonNum > 0) {
        config_body << "<Module ID=\"2\">\n";
        GenerateXMLForModule(push_button.uuid, 2, config_body, mac);
        config_body << "</Module>\n";
    }

    // 生成模块 3 的 XML
    if (push_button.module_3_buttonNum > 0) {
        config_body << "<Module ID=\"3\">\n";
        GenerateXMLForModule(push_button.uuid, 3, config_body, mac);
        config_body << "</Module>\n";
    }

    // 生成模块 4 的 XML
    if (push_button.module_4_buttonNum > 0) {
        config_body << "<Module ID=\"4\">\n";
        GenerateXMLForModule(push_button.uuid, 4, config_body, mac);
        config_body << "</Module>\n";
    }

    // 生成模块 5 的 XML
    if (push_button.module_5_buttonNum > 0) {
        config_body << "<Module ID=\"5\">\n";
        GenerateXMLForModule(push_button.uuid, 5, config_body, mac);
        config_body << "</Module>\n";
    }
}

void PerDevPushButton::UpdatePerDevPushButtonFile(const std::string& mac, std::stringstream& config_body)
{
    config_body << "<MDKeyData>\n";

    ResidentDev dev;
    dbinterface::ResidentPerDevices::GetMacDev(mac,dev);
    
    //获取该dev下的所有按键信息
    DevicePushButton dev_push_button;
    AK_LOG_INFO << "Update Per Dev Push Button Contact . dev.uuid: " << dev.uuid;
    if(0 !=dbinterface::ExternPushButton::GetDevicePushButtonByDeviceUUID(dev.uuid, dev_push_button))
    {
        config_body <<"</MDKeyData>";
        return;
    }
    GenerateXMLForDevicePushButton(dev_push_button, config_body, mac);
    config_body <<"</MDKeyData>";
   

}

