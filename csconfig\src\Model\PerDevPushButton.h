#ifndef __COMMUNITY_DEVICE_PUSH_BUTTON_H__
#define __COMMUNITY_DEVICE_PUSH_BUTTON_H__

#include <vector>
#include <string>
#include "AkcsCommonSt.h"
#include "dbinterface/ExternPushButton.h"
#include "dbinterface/DevicePushButtonList.h"


class PerDevPushButton
{

public:
    enum WebCalleeType
    {
        WEB_NOT_SET = 0,    // 未设置
        WEB_SINGLE_CALL_PERSONAL = 1,    // 单呼人
        WEB_SINGLE_CALL_DEVICE = 2,    // 单呼设备    

    };
    enum DeviceCalleeType
    {
        DEV_NOT_SET = 0,    // 未设置
        DEV_SINGLE_CALL = 1,    // 单呼            
    };
    
    PerDevPushButton()
    {
    }
    ~PerDevPushButton(){};

public:
 
    void UpdatePerDevPushButtonFile(const std::string& mac, std::stringstream& config_body);
    void GenerateXMLForDevicePushButton(DevicePushButton& push_button, std::stringstream& config_body, const std::string& mac);
    void GenerateXMLForModule(const std::string& devicePushButtonUUID, int moduleID, std::stringstream& config_body, const std::string& mac); 
private:

};


#endif

