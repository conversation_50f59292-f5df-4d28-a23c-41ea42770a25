#include <sstream>
#include "PersonalAccount.h"
#include "RldbQuery.h"
#include "Rldb.h"
#include "AkLogging.h"
#include <ctime>
#include <util.h>
#include "AkcsCommonDef.h"
#include "dbinterface/InterfaceComm.h"

#include "dbinterface/CommunityRoom.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"
#include "dbinterface/Sip.h"
#include "util_string.h"

CPersonalAccount* GetPersonalAccountInstance()
{
    return CPersonalAccount::GetInstance();
}

CPersonalAccount* CPersonalAccount::instance = NULL;

CPersonalAccount* CPersonalAccount::GetInstance()
{
    if (instance == NULL)
    {
        instance = new CPersonalAccount();
    }

    return instance;
}

void TransferUserInfoToDeviceContactlist(const ResidentPerAccount& user_info, DEVICE_CONTACTLIST& app_info)
{
    app_info.id = user_info.id;
    app_info.role = user_info.role;
    app_info.type = DEVICE_TYPE_APP;
    app_info.unit_id = user_info.unit_id;
    app_info.only_apt = user_info.only_apt;
    app_info.phone_status = user_info.phone_status;  //社区PhoneStatus不在使用，用PhoneExpireTime代替,个人终端用这个标识，社区的会在外层根据calltype改这个是否落地的标识
    app_info.enable_ip_direct = user_info.ip_direct;
    Snprintf(app_info.uuid, sizeof(app_info.uuid), user_info.uuid);
    Snprintf(app_info.name, sizeof(app_info.name), user_info.name);
    Snprintf(app_info.phone, sizeof(app_info.phone), user_info.phone);
    Snprintf(app_info.email, sizeof(app_info.email), user_info.getEmail());
    Snprintf(app_info.phone2, sizeof(app_info.phone2), user_info.phone2);
    Snprintf(app_info.phone3, sizeof(app_info.phone3), user_info.phone3);
    Snprintf(app_info.room_name, sizeof(app_info.room_name), user_info.name);
    Snprintf(app_info.room_num, sizeof(app_info.room_num), user_info.room_number);
    Snprintf(app_info.phone_code, sizeof(app_info.phone_code), user_info.phone_code);
    Snprintf(app_info.parent_uuid, sizeof(app_info.parent_uuid), user_info.parent_uuid);
    Snprintf(app_info.sip_account, sizeof(app_info.sip_account), user_info.sip_account);
    Snprintf(app_info.mobile_number, sizeof(app_info.mobile_number), user_info.getMobileNumber());
    Snprintf(app_info.firstname, sizeof(app_info.firstname), user_info.firstname);
    Snprintf(app_info.lastname, sizeof(app_info.lastname), user_info.lastname);
    app_info.app_login_status = user_info.app_login_status;
    if (app_info.role == ACCOUNT_ROLE_COMMUNITY_MAIN)
    {
        Snprintf(app_info.room_name, sizeof(app_info.room_name), user_info.room_number);
    }
    else
    {
        Snprintf(app_info.room_name, sizeof(app_info.room_name), user_info.name); //V4.3 只对社区生效。把主账号的Name当做RoomName
    }
}

// 单住户获取app_list
int CPersonalAccount::DaoGetApplistByNode(const std::string& node, std::vector<DEVICE_CONTACTLIST>& app_list)
{
    ResidentPerAccount node_info;
    memset(&node_info, 0, sizeof(node_info));
    if (0 == dbinterface::ResidentPersonalAccount::GetUserInfoByAccount(node, node_info))
    {
        DEVICE_CONTACTLIST node_app;
        memset(&node_app, 0, sizeof(node_app));

        TransferUserInfoToDeviceContactlist(node_info, node_app);
        
        std::string sip_group = dbinterface::Sip::GetSipGroupByNode(node);
        Snprintf(node_app.sip_group, sizeof(node_app.sip_group), sip_group.c_str());
        app_list.push_back(node_app);

        ResidentPerAccountList attendant_list;
        if (0 == dbinterface::ResidentPersonalAccount::GetAttendantUserInfoListByNode(node, attendant_list))
        {
            for (const auto& attendant : attendant_list)
            {
                DEVICE_CONTACTLIST attendant_app;
                memset(&attendant_app, 0, sizeof(attendant_app));
                TransferUserInfoToDeviceContactlist(attendant, attendant_app);
                app_list.push_back(attendant_app);
            }
        }
    }
    return 0;
}

// 社区获取app_list
int CPersonalAccount::DaoGetCommunityApplistByNode(const std::string& node, std::vector<DEVICE_CONTACTLIST>& app_list)
{
    ResidentPerAccount node_info;
    memset(&node_info, 0, sizeof(node_info));
    if (0 == dbinterface::ResidentPersonalAccount::GetUserInfoByAccount(node, node_info))
    {        
        DEVICE_CONTACTLIST node_app;
        memset(&node_app, 0, sizeof(node_app));

        TransferUserInfoToDeviceContactlist(node_info, node_app);
        
        std::string sip_group = dbinterface::Sip::GetSipGroupByNode(node);
        Snprintf(node_app.sip_group, sizeof(node_app.sip_group), sip_group.c_str());

        //社区PersonalAccount RoomNumber代表room name.CommunityRoom表RoomName代表room number
        CommunityRoomInfo room_info; 
        if (0 == dbinterface::CommunityRoom::GetCommunityRoomByID(node_info.room_id, room_info))
        {
            node_app.unit_id =  node_info.unit_id;
            Snprintf(node_app.floor, sizeof(node_app.floor), room_info.floor);
            Snprintf(node_app.room_num, sizeof(node_app.room_num), room_info.room_number);
            if (strlen(node_app.floor) == 0)
            {
                //若Floor字段为空,则unit_apt中apt值为RoomName
                snprintf(node_app.unit_apt, sizeof(node_app.unit_apt), "%u-%s", node_app.unit_id, ExtractFirstNumber(node_app.room_num).c_str());
            }            
            else
            {
                //若Floor字段不为空,则unit_apt中apt值为"Floor+00"
                snprintf(node_app.unit_apt, sizeof(node_app.unit_apt), "%u-%s%s", node_app.unit_id, node_app.floor, "00");
            }
            
            Snprintf(node_app.unit_uuid, sizeof(node_app.unit_uuid), room_info.unit_uuid);
        }        
        node_app.mng_account_id = node_info.parent_id;
        app_list.push_back(node_app);

        ResidentPerAccountList attendant_list;
        if (0 == dbinterface::ResidentPersonalAccount::GetAttendantUserInfoListByNode(node, attendant_list))
        {
            DEVICE_CONTACTLIST attendant_app;
            for (const auto& attendant : attendant_list)
            {
                memset(&attendant_app, 0, sizeof(attendant_app));
                attendant_app.mng_account_id = node_info.parent_id;
                TransferUserInfoToDeviceContactlist(attendant, attendant_app);
                app_list.push_back(attendant_app);
            }
        }
    }
    return 0;
}

void CPersonalAccount::DaoGetCommunityUnitAccounts(int unit_id, std::vector<COMMUNITY_ACCOUNTS_INFO>& accounts_info)
{
    ResidentPerAccountList account_list;
    if (0 == dbinterface::ResidentPersonalAccount::GetCommUnitMainList(unit_id, account_list))
    {
        COMMUNITY_ACCOUNTS_INFO account_info;
        for (const auto& account : account_list)
        {
            memset(&account_info, 0, sizeof(account_info));
            account_info.unit_id = unit_id;
            Snprintf(account_info.account, sizeof(account_info.account), account.sip_account);
            Snprintf(account_info.uuid, sizeof(account_info.uuid), account.uuid);
            accounts_info.push_back(account_info);
        }
    }
}

//获取社区的主账号 如果是社区公共的就是全部主账号，如果是单元，就是单元内的主账号
int CPersonalAccount::DaoGetCommunityAppMaster(const int grade, const int mng_id, const int unit_id, std::vector<COMMUNITY_ACCOUNTS_INFO>& node_list)
{
    ResidentPerAccountList account_list;
    if (csmain::COMMUNITY_DEVICE_TYPE_PUBLIC == grade)
    {
        dbinterface::ResidentPersonalAccount::GetCommPubMainList(mng_id, account_list);
    }
    else if (csmain::COMMUNITY_DEVICE_TYPE_PUBLIC_UNIT == grade)
    {
        dbinterface::ResidentPersonalAccount::GetCommUnitMainList(unit_id, account_list);
    }

    COMMUNITY_ACCOUNTS_INFO node;
    for (const auto& account : account_list)
    {
        memset(&node, 0, sizeof(node));
        Snprintf(node.account, sizeof(node.account), account.sip_account);
        node.unit_id = account.unit_id;
        Snprintf(node.uuid, sizeof(node.uuid), account.uuid);
        node_list.push_back(node);
    }
    return 0;
}

int CPersonalAccount::DaoGetPmApplistByMngID(const int mng_account_id, std::vector<DEVICE_CONTACTLIST>& pm_app_list)
{
    ResidentPerAccountList pm_info_list;
    if (0 == dbinterface::ResidentPersonalAccount::GetCommPmApplistByMngID(mng_account_id, pm_info_list))
    {
        DEVICE_CONTACTLIST pm_app;
        for (const auto& pm_info : pm_info_list)
        {
            memset(&pm_app, 0, sizeof(pm_app));
            TransferUserInfoToDeviceContactlist(pm_info, pm_app);
            pm_app.call_type = pm_info.cnf.call_type;
            pm_app_list.push_back(pm_app);
        }
    }
    return 0;
}
