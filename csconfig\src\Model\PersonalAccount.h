#ifndef __ADAPT_PERSONNAL_ACCOUNT_H__
#define __ADAPT_PERSONNAL_ACCOUNT_H__

#include <vector>
#include <boost/noncopyable.hpp>
#include "BasicDefine.h"
#include "util_cstring.h"
#include "ConfigDef.h"
#include "AKCSMsg.h"
#include "AdaptUtility.h"
#include "AkcsWebMsgSt.h"
#include "PersonalAccount.h"
#include "dbinterface/resident/ResidentPersonalAccount.h"


typedef struct RedirectAccountInfo_t
{
    int  account_id;    //mng_id or installer_id
    int  is_dev;
    char uid[32];   //dev--mac or user--node 
}RedirectAccountInfo;

class CPersonalAccount : public boost::noncopyable
{
public:
    CPersonalAccount()
    {
    }
    ~CPersonalAccount()
    {
    }

    int DaoGetApplistByNode(const std::string& node, std::vector<DEVICE_CONTACTLIST>& devices);
    void DaoGetCommunityUnitAccounts(int unit_id, std::vector<COMMUNITY_ACCOUNTS_INFO>& devices);
    int DaoGetCommunityApplistByNode(const std::string& nodes, std::vector<DEVICE_CONTACTLIST>& devices);
    int DaoGetPmApplistByMngID(const int mng_account_id, std::vector<DEVICE_CONTACTLIST>& pm_app_list);
    int DaoGetCommunityAppMaster(const int grade, const int mng_id, const int unit_id, std::vector<COMMUNITY_ACCOUNTS_INFO>& devices);
    static CPersonalAccount* GetInstance();
private:
    static CPersonalAccount* instance;
};

void TransferUserInfoToDeviceContactlist(const ResidentPerAccount& user_info, DEVICE_CONTACTLIST& app_info);
CPersonalAccount* GetPersonalAccountInstance();

#endif //__ADAPT_PERSONNAL_ACCOUNT_H__
